<template>
  <v-container>
    <!-- Modal เพิ่มตำแหน่ง -->
    <v-dialog v-model="modalAddPosition" width="800" persistent>
      <v-card>
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalAddPosition = !modalAddPosition" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container class="mb-0">
          <v-card-text class="mb-0 pb-0">
            <v-form ref="FindUser" :lazy-validation="lazy">
              <v-row dense justify="center">
                <v-col cols="12">
                  <span>ค้นหาผู้ใช้งาน <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" md="9" sm="9" xs="12">
                      <v-text-field placeholder="ระบุอีเมล" outlined dense v-model="email" :rules="Rules.emailRules"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" sm="3" xs="12">
                      <v-btn class="px-5 white--text" color="#27AB9C" @click="save()">ค้นหา</v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-container>
        <v-container v-show="notFoundUser">
          <v-card-text>
            <v-row dense no-gutters justify="center" class="mb-4">
              <v-col cols="12" md="12" sm="12" xs="12"  align="center">
                <span>ไม่พบข้อมูล</span>
              </v-col>
            </v-row>
            <v-row dense no-gutters justify="center">
              <v-col cols="12" md="4" sm="5" xs="12" class="pt-2" align="center">
                <v-btn>เพิ่มข้อมูลใน One ID</v-btn>
              </v-col>
              <v-col cols="12" md="1" sm="1" xs="12" class="pt-4" align="center">
                <span>หรือ</span>
              </v-col>
              <v-col cols="12" md="4" sm="5" xs="12" class="pt-2" align="center">
                <v-btn>แชทกับพาณิชย์</v-btn>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="modalAddPosition = !modalAddPosition">ยกเลิก</v-btn>
          </v-card-actions>
        </v-container>
        <v-container>
          <v-card-text>
            <v-form ref="FormAddPosition" :lazy-validation="lazy">
              <v-row dense no-gutters class="ml-4">
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>ชื่อ - สกุล</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span>นายซี ใจใจ</span>
                </v-col>
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>อีเมล</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span><EMAIL></span>
                </v-col>
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>เบอร์โทรศัพท์</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span>088-5862245</span>
                </v-col>
              </v-row>
              <v-row dense no-gutters>
                <v-col cols="12" class="mt-4">
                  <v-autocomplete
                    :items="items"
                    dense
                    chips
                    small-chips
                    label="กรุณาเลือกตำแหน่ง"
                    multiple
                    solo
                  ></v-autocomplete>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="modalAddPosition = !modalAddPosition">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="save()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal แสดงข้อมูล -->
    <v-dialog v-model="modalShowPosition" width="800" persistent>
      <v-card>
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>ประเมินความพึงพอใจสินค้า+++</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalShowPosition = !modalShowPosition" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text>
            <v-form ref="FormAddPosition" :lazy-validation="lazy">
              <v-row>
                <v-col cols="12" md="4">
                  <v-img
                   :src="`https://picsum.photos/500/300?image=${1 * 5 + 10}`"
                   :lazy-src="`https://picsum.photos/10/6?image=${1 * 5 + 10}`"
                    aspect-ratio="1"
                    class="grey lighten-2"
                    height="180px"
                    width="200px"
                  >
                  </v-img>
                </v-col>
                <v-col cols="12" md="4">
                  รองเท้าเซฟตี้ หัวเหล็ก เบอร์ 39 สีดำ<br>
                  Color : Black
                </v-col>
                <v-col cols="12" md="4">
                  จำนวน 1 ชิ้น
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" md="12">
                  <v-rating
      v-model="rating"
      background-color="orange lighten-3"
      color="orange"
      large
    ></v-rating>
                </v-col>
              </v-row>
              <v-row dense no-gutters>
              <v-col cols="12" md="12">
                        <span class="f-left" style="line-height: 26px; color: #333333;" :style="IpadSize ? 'font-size: 16px;' : 'font-size: 20px;'">เพิ่มรูปภาพสินค้า</span>
                      </v-col>
                      <v-col cols="12" md="12">
                        <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                          <v-card-text>
                            <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFile()">
                              <v-card-text>
                                <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                  <v-file-input
                                    v-model="DataImage"
                                    :items="DataImage"
                                    accept="image/jpeg, image/jpg, image/png"
                                    @change="UploadImage()"
                                    id="file_input"
                                    multiple
                                    :clearable="false"
                                    style="display:none">
                                  </v-file-input>
                                  <v-col cols="12" md="12">
                                    <v-row justify="center" align="center">
                                      <v-col cols="12" md="12" style="text-align: center;">
                                        <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br/>
                                        <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br/>
                                        <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 555 x 555 px ไฟล์นามสกุล .JPG, .JPEG, .PNG)</span><br/>
                                        <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'"><span style="color: red;">***</span> หมายเหตุ ไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-card-text>
                            </v-card>
                            <div v-if="Detail.product_image.length !== 0" class="mt-4">
                              <draggable v-model="Detail.product_image"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                                <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="12" md="3">
                                  <v-card outlined class="pa-1" width="146" height="146" v-if="statusPage !== 'Edit'">
                                    <v-img :src="item.url" :lazy-src="item.url" width="130" height="130" contain>
                                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                        <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                      </v-btn>
                                    </v-img>
                                  </v-card>
                                  <v-card outlined class="pa-1" width="146" height="146" v-else-if="statusPage === 'Edit'">
                                    <v-img :src="item.media_path" :lazy-src="item.media_path" width="130" height="130" contain>
                                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                        <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                      </v-btn>
                                    </v-img>
                                  </v-card>
                                </v-col>
                              </draggable>
                            </div>
                          </v-card-text>
                        </v-card>
                      </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" dark color="#27AB9C" @click="modalShowPosition = !modalShowPosition">ย้อนกลับ</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal แก้ไขตำแหน่ง -->
    <v-dialog v-model="modalEditPosition" width="800" persistent>
      <v-card>
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalEditPosition = !modalEditPosition" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-form ref="FormAddPosition" :lazy-validation="lazy">
              <v-row dense no-gutters class="ml-4">
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>ชื่อ - สกุล</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span>นายซี ใจใจ</span>
                </v-col>
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>อีเมล</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span><EMAIL></span>
                </v-col>
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>เบอร์โทรศัพท์</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span>088-5862245</span>
                </v-col>
              </v-row>
              <v-row dense no-gutters>
                <v-col cols="12" class="mt-4">
                  <v-autocomplete
                    v-model="values"
                    :items="items"
                    dense
                    chips
                    label="กรุณาเลือกตำแหน่ง"
                    multiple
                    solo
                  ></v-autocomplete>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="modalAddPosition = !modalAddPosition">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="save()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <v-row justify="center">
      <v-col cols="12">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense justify="center">
            <v-col cols="12" md="12" sm="12" xs="12">
             <!--  <v-card-title style="font-weight: bold; font-size: 24px;">รายชื่อผู้ใช้ในร้านค้า</v-card-title> -->
              <v-breadcrumbs :items="itemsBreadcrumb">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
    </v-breadcrumbs>
            </v-col>
          </v-row>
          <v-row dense no-gutters class="mb-2 mt-2">
            <v-col cols="12" md="6" sm="46" xs="12">
              <v-text-field
              v-model="search"
              rounded
              dense
              outlined
              placeholder="ค้นหารายชื่อผู้ใช้งาน"
              >
                <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6" sm="6" xs="12" align="end">
              <v-btn class="" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createPosition()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
            </v-col>
          </v-row>
          <v-row v-if="disableTable === true">
            <v-col cols="12" class="py-0">
              <a-tabs @change="SelectDetailPosition">
                <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                <a-tab-pane :key="0"><span slot="tab">ยังไม่ประเมิน <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countAll }}</a-tag></span></a-tab-pane>
                <a-tab-pane :key="1"><span slot="tab">ประเมินแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countActivePosition }}</a-tag></span></a-tab-pane>
                <!-- <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{ countInactivePosition }}</a-tag></span></a-tab-pane> -->
              </a-tabs>
            </v-col>
            <v-col cols="12" class="pl-4 pt-6">
              <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">แสดงรายการจำนวนสินค้า {{ itemsPerPage &lt; 0 ? DataTable.length : itemsPerPage }} รายการ</span>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12">
              <v-card outlined class="mb-4">
                <v-data-table
                :headers="keyCheckHead == 0 ? headersAll : keyCheckHead == 1 ? headersActivePosition : headersInactivePosition"
                :items="DataTable"
                :items-per-page="5"
                :page.sync="page"
                :search="search"
                @pagination="countCompany"
                no-results-text="ไม่พบรายชื่อผู้ใช้งานที่ค้นหา"
                no-data-text="ไม่มีรายชื่อผู้ใช้งานในตาราง"
                :update:items-per-page="getItemPerPage"
                >
                <template v-slot:[`item.index`]="{ index: number }">
                  <v-img
                   :src="`https://picsum.photos/500/300?image=${number * 5 + 10}`"
                   :lazy-src="`https://picsum.photos/10/6?image=${number * 5 + 10}`"
                    aspect-ratio="1"
                    class="grey lighten-2"
                    height="150px"
                    width="180px"
                  >
                  </v-img>
                </template>
                <template v-slot:[`item.productname`]="{ item: { productname } = {}}">
                  {{productname}}
                </template>
                <template v-slot:[`item.status`]="{ item }">
                  <span v-if="item.status === 'active'">
                    <v-chip small class="ma-2" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                  </span>
                  <span v-else-if="item.status === 'inactive'">
                    <v-chip small class="ma-2" color="#f7c5ad" text-color="#f50">ยกเลิก</v-chip>
                  </span>
                </template>
                <template v-slot:[`item.date`]="{ item: { date } = {}}">
                  {{date}}
                </template>
                <template v-slot:[`item.actions`]="{ item }">
                  <v-row dense justify="center">
                    <v-btn
                      @click="ShowDetail(item)"
                      class="pt-4 pb-4"
                      color="success"
                    >
                      ประเมินความพึงพอใจสินค้า
                    </v-btn>
                  </v-row>
                </template>
                </v-data-table>
              </v-card>
              <!-- <div class="text-center pt-2">
                <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
              </div> -->
            </v-col>
          </v-row>
          <v-row justify="center" align-content="center" v-else>
            <v-col cols="12" md="12" align="center">
              <div class="my-5">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีการเพิ่มตำแหน่ง</span><br/>
                <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span style="font-size: 28px;">“เพิ่มข้อมูล”</span> เพื่อเพิ่มตำแหน่งที่สมบูรณ์ของคุณ</span>
              </h2>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import draggable from 'vuedraggable'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    draggable
  },
  data () {
    return {
      itemsBreadcrumb: [
        {
          text: 'Dashboard',
          disabled: false,
          href: 'breadcrumbs_dashboard'
        },
        {
          text: 'Link 1',
          disabled: false,
          href: 'breadcrumbs_link_1'
        },
        {
          text: 'Link 2',
          disabled: true,
          href: 'breadcrumbs_link_2'
        }
      ],
      Detail: {
        product_name: '',
        seller_shop_id: '',
        sku: '',
        inventory_code: '',
        manufacturer: '',
        supplier: '',
        product_status: true,
        barcode: '',
        message_status: '',
        service_type: '',
        vat_type: '',
        actual_stock: '',
        effective_stock: '',
        min_per_order: '',
        max_per_order: '',
        manage_price: {
          fake_price: '',
          real_price: '',
          use_tier: '',
          tier_price: [
            { tier: '1', price: '' },
            { tier: '2', price: '' },
            { tier: '3', price: '' },
            { tier: '4', price: '' },
            { tier: '5', price: '' }
          ]
        },
        volumn: {
          width: '',
          length: '',
          height: '',
          weight: ''
        },
        product_category: [],
        product_industry: [],
        product_ratio: '',
        description: '',
        short_description: '',
        have_attribute: '',
        key_1_value: '',
        key_2_value: '',
        group_price: '',
        group_volumn: '',
        attribute_manage: [],
        product_property: [],
        product_certificate: [],
        product_manual: [],
        product_image: [],
        upload_video: ''
      },
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      search: '',
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: true,
      keyCheckHead: 0,
      countAll: 0,
      countActivePosition: 0,
      countInactivePosition: 0,
      DataTable: [],
      lazy: false,
      companyData: [],
      pathEditCompany: '',
      notFoundUser: false,
      FoundUser: false,
      modalAddPosition: false,
      modalEditPosition: false,
      modalShowPosition: false,
      email: '',
      positionType: [],
      DataImageColor: [],
      DataImage: [],
      DataImage1: [],
      DataImage2: [],
      DataImage3: [],
      file1: [],
      file2: [],
      file3: [],
      items: ['foo', 'bar', 'fizz', 'buzz'],
      itemMenuShop: [
        { key: 1, title: 'รายการสินค้า' },
        { key: 2, title: 'ดูรายการสั่งซื้อ' },
        { key: 3, title: 'เรียกพนักงานรับพัสดุ' },
        { key: 4, title: 'ติดตามสถานะสินค้า' },
        { key: 5, title: 'สต๊อกสินค้า' },
        { key: 6, title: 'จัดการร้านค้า' },
        { key: 7, title: 'การคืนสินค้า' },
        { key: 8, title: 'แดชบอร์ด' },
        { key: 9, title: 'เอกสารและคู่มือการใช้งาน' },
        { key: 10, title: 'รายได้ของฉัน' },
        { key: 11, title: 'จัดการตำแหน่งและสิทธิ์การใช้งาน' },
        { key: 12, title: 'จัดการผู้ใช้งาน' }
      ],
      Rules: {
        position_name: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่',
          v => (/^[-0-9/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => ((/^[0-9/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ]
      },
      headersAll: [
        { text: 'รูปสินค้า', value: 'index', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อสินค้า', value: 'productname', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'id', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ประเมินภายในวันที่', value: 'date', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersActivePosition: [
        { text: 'ลำดับที่', value: 'id', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ - สกุล', value: 'username', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ตำแหน่ง', value: 'position', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'การจัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersInactivePosition: [
        { text: 'ลำดับที่', value: 'id', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ - สกุล', value: 'username', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ตำแหน่ง', value: 'position', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'การจัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNav')
    this.getListCompany()
  },
  watch: {
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = [
          {
            id: 1,
            username: 'นางสาวเอ คนดี',
            position: 'เจ้าหน้าที่การเงิน',
            status: 'active'
          },
          {
            id: 2,
            username: 'นายบี คนคิด',
            position: 'ผู้ดูแลระบบระดับสูง',
            status: 'active'
          },
          {
            id: 3,
            username: 'นายซี คนเป็น',
            position: 'เจ้าหน้าที่การเงิน',
            status: 'inactive'
          }
        ]
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = [
          {
            id: 1,
            username: 'นางสาวเอ คนดี',
            position: 'เจ้าหน้าที่การเงิน',
            status: 'active'
          },
          {
            id: 2,
            username: 'นายซี คนเป็น',
            position: 'ผู้ดูแลระบบระดับสูง',
            status: 'active'
          }
        ]
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = [
          {
            id: 3,
            username: 'นายซี คนเป็น',
            position: 'เจ้าหน้าที่การเงิน',
            status: 'inactive'
          }
        ]
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    addImageColor (index) {
      // console.log(index)
      document.getElementById(`Dataimage${index}`).click()
    },
    UploadImageFixColor (val) {
      const element = this.DataImageColor
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
        const reader = new FileReader()
        reader.readAsDataURL(element)
        reader.onload = () => {
          var resultReader = reader.result
          var url = URL.createObjectURL(element)
          if (this.$route.query.Status === 'Edit' && val.attribute_image.id !== '-1' && val.attribute_image.id !== '') {
            // console.log('เข้าเงื่อนไข 1', val)
            val.attribute_image.image_data = resultReader.split(',')[1]
            val.attribute_image.imagePic = resultReader
            val.attribute_image.path = url
            val.attribute_image.name = this.DataImageColor.name
          } else if (this.$route.query.Status === 'Edit' && val.attribute_image.id === '-1' && val.attribute_image.id !== '') {
            // console.log('เข้าเงื่อนไข 2', val)
            val.attribute_image.id = '-1'
            val.attribute_image.image_data = resultReader.split(',')[1]
            val.attribute_image.imagePic = resultReader
            val.attribute_image.path = url
            val.attribute_image.name = this.DataImageColor.name
          } else if (this.$route.query.Status === 'Edit' && val.attribute_image.id === '') {
            // console.log('เข้าเงื่อนไข 3', val)
            val.attribute_image.id = '-1'
            val.attribute_image.image_data = resultReader.split(',')[1]
            val.attribute_image.imagePic = resultReader
            val.attribute_image.path = url
            val.attribute_image.name = this.DataImageColor.name
          } else {
            val.attribute_image.id = '-1'
            val.attribute_image.image_data = resultReader.split(',')[1]
            val.attribute_image.imagePic = resultReader
            val.attribute_image.path = url
            val.attribute_image.name = this.DataImageColor.name
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
      }
      // console.log('UploadImageFixColor', val)
    },
    RemoveImageColor (item, index) {
      if (this.$route.query.Status === 'Edit' && item.id !== '-1') {
        // console.log('เข้าเงื่อนไข 1', item)
        item.attribute_image.image_data = ''
        item.attribute_image.imagePic = ''
      } else if (this.$route.query.Status === 'Edit' && item.id === '-1') {
        // console.log('เข้าเงื่อนไข 2', item)
        item.attribute_image.id = '-1'
        item.attribute_image.image_data = ''
        item.attribute_image.imagePic = ''
      } else {
        item.attribute_image.id = '-1'
        item.attribute_image.image_data = ''
        item.attribute_image.imagePic = ''
        item.attribute_image.path = ''
        item.attribute_image.name = ''
      }
      // console.log('RemoveImageColor', item)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      for (let i = 0; i < this.DataImage.length; i++) {
        const element = this.DataImage[i]
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
          const imageSize = element.size / 1024 / 1024
          if (imageSize < 2) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = () => {
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (this.$route.query.Status !== 'Edit') {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  url: url,
                  name: this.DataImage[i].name
                })
              } else {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  media_path: url,
                  name: this.DataImage[i].name
                })
              }
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB', showConfirmButton: false, timer: 1500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
        }
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.product_image_delete.push(val.id)
        }
        this.Detail.product_image.splice(index, 1)
      } else {
        this.Detail.product_image.splice(index, 1)
      }
    },
    async getListCompany () {
      this.countAll = 0
      this.countActivePosition = 0
      this.countInactivePosition = 0
      await this.$store.dispatch('actionslistCompany')
      var response = await this.$store.state.ModuleAdminManage.stateListCompany
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.companyData = response.data
        this.countAll = 3
        this.countActivePosition = 2
        this.countInactivePosition = 1
        if (this.StateStatus === 0) {
          this.DataTable = [
            {
              id: '123456789011',
              productname: 'รองเท้าเซฟตี้ หัวเหล็ก เบอร์ 39 สีดำ',
              date: '24-05-2564 ',
              status: 'active'
            },
            {
              id: '123456789011',
              productname: 'รองเท้าเซฟตี้ หัวเหล็ก เบอร์ 39 สีดำ',
              date: '24-05-2564 ',
              status: 'active'
            },
            {
              id: '123456789011',
              productname: 'รองเท้าเซฟตี้ หัวเหล็ก เบอร์ 39 สีดำ',
              date: '24-05-2564 ',
              status: 'inactive'
            }
          ]
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.countAll = this.DataTable.length
            this.disableTable = true
          }
        } else if (this.StateStatus === 1) {
          this.DataTable = [
            {
              id: 1,
              username: 'นางสาวเอ คนดี',
              position: 'เจ้าหน้าที่การเงิน',
              status: 'active'
            },
            {
              id: 2,
              username: 'นายซี คนเป็น',
              position: 'ผู้ดูแลระบบระดับสูง',
              status: 'active'
            }
          ]
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 2) {
          this.DataTable = [
            {
              id: 3,
              username: 'นายซี คนเป็น',
              position: 'เจ้าหน้าที่การเงิน',
              status: 'inactive'
            }
          ]
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.countInactivePosition = this.DataTable.length
            this.disableTable = true
          }
        }
      }
    },
    ShowDetail (val) {
      this.modalShowPosition = !this.modalShowPosition
    },
    ShowEditPosition () {
      this.modalShowPosition = !this.modalShowPosition
      this.modalEditPosition = !this.modalEditPosition
    },
    createPosition () {
      this.modalAddPosition = !this.modalAddPosition
    },
    countCompany (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    SelectDetailPosition (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
      this.page = 1
    },
    async gotoCompanyDetail (val) {
      var data = {
        company_id: val.id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      // console.log(response)
      if (response.result === 'SUCCESS') {
        if (response.message === 'Show company detail success.') {
          localStorage.setItem('companyData', Encode.encode(response.data))
          this.$EventBus.$emit('getCompanyName')
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    }
  }
}
</script>
