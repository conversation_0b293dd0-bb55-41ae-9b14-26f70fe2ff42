<template>
  <v-container :style="MobileSize ? 'max-width: 100% !important;' : 'max-width: 1400px !important;'" :class="MobileSize ? 'px-2' : ''">
    <v-row v-if="dataRole === 'ext_buyer'" dense justify="center" class="py-4">
      <h1 class=" diaplayWeb HeaderWebProduct" style="font-size: 32px; font-weight: 800; color: #3EC6B6;"><span style="color: #464646;">{{ $t('TextHome.textRecommend') }}</span> {{ $t('TextHome.textProduct') }}</h1>
      <h1 class="displayIPAD HeaderIpadProduct" style="font-size: 32px; font-weight: 800; color: #3EC6B6;"><span style="color: #464646;">{{ $t('TextHome.textRecommend') }}</span> {{ $t('TextHome.textProduct') }}</h1>
      <h1 class="displayMobile HeaderMobileProduct" style="font-size: 24px; font-weight: 700; color: #3EC6B6;"><span style="color: #464646;">{{ $t('TextHome.textRecommend') }}</span> {{ $t('TextHome.textProduct') }}</h1>
      <!-- <v-spacer class="ml-4 spacerStyle"></v-spacer>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#27AB9C" :class="IpadProSize ? 'mt-4' : 'mt-4 mr-16'" v-if="!MobileSize && !IpadSize" plain style="font-weight: 600;">ดูทั้งหมด <v-btn height="24" width="24" fab x-small elevation="0" class="ml-0"><v-icon color="#27AB9C">mdi-arrow-right-circle-outline</v-icon></v-btn></v-btn>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#27AB9C" class="mt-4" v-if="!MobileSize && IpadSize" plain style="font-weight: 600;">ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#27AB9C" class="mt-5" v-if="MobileSize && !IpadSize" plain style="font-weight: 600;">ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn> -->
    </v-row>
    <v-row v-if="dataRole !== 'ext_buyer'">
      <h1 class="pt-1 mt-2  ml-16 pl-6 diaplayWeb HeaderWebProduct" style="font-size: 24px; font-weight: 700; color: #27AB9C;">{{ header }}</h1>
      <h1 class="pt-1 ml-4 mt-2 displayIPAD HeaderIpadProduct" style="font-size: 24px; font-weight: 700; color: #27AB9C;">{{ header }}</h1>
      <h1 class="pt-1 ml-3 mt-2 displayMobile HeaderMobileProduct" style="font-size: 18px; font-weight: 700; color: #27AB9C;">{{ header }}</h1>
      <v-spacer class="ml-4 spacerStyle"></v-spacer>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#27AB9C" :class="IpadProSize ? 'mt-4' : 'mt-4 mr-16'" v-if="!MobileSize && !IpadSize" plain style="font-weight: 600; text-transform: none;">{{ $t('Headers.HomeText.ViewAll') }} <v-btn height="24" width="24" fab x-small elevation="0" class="ml-0"><v-icon color="#27AB9C">mdi-arrow-right-circle-outline</v-icon></v-btn></v-btn>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#27AB9C" class="mt-4" v-if="!MobileSize && IpadSize" plain style="font-weight: 600; text-transform: none;">{{ $t('Headers.HomeText.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#27AB9C" class="mt-5" v-if="MobileSize && !IpadSize" plain style="font-weight: 600; text-transform: none;">{{ $t('Headers.HomeText.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
    </v-row>
    <vue-horizontal-list class="mx-16" :items='propsData' :options='options' v-if="!MobileSize && !IpadSize && dataRole !== 'ext_buyer'">
      <template v-slot:nav-prev>
        <div><v-icon color="#008E00" size="32">mdi-chevron-left</v-icon></div>
      </template>

      <template v-slot:nav-next>
        <div><v-icon color="#008E00" size="32">mdi-chevron-right</v-icon></div>
      </template>
      <template v-slot:default='{ item }' >
        <a-skeleton :loading="check === true ? !loading : loading">
          <CardProducts :itemProduct='item' />
        </a-skeleton>
      </template>
    </vue-horizontal-list>
    <v-row dense v-else-if="IpadSize && !MobileSize && dataRole !== 'ext_buyer'" class="px-0">
      <v-col cols="3" sm="3" xs="6" v-for="(item, index) in propsData.slice(0, 12)" :key="index" class="mb-4">
        <CardProductsResponsive :itemProduct='item'/>
      </v-col>
    </v-row>
    <v-row dense v-else-if="MobileSize && !IpadSize && dataRole !== 'ext_buyer'" class="px-0">
      <v-col cols="6" sm="3" xs="6" v-for="(item, index) in cleanData" :key="index" class="mb-4" >
        <CardProductsResponsive :itemProduct='item'/>
      </v-col>
    </v-row>
    <!-- <pre>{{propsData}}</pre> -->
    <!-- <vue-horizontal-list class="mx-16" :items='propsData' :options='options' v-if="!MobileSize && !IpadSize">
      <template v-slot:nav-prev>
        <div><v-icon color="#008E00" size="32">mdi-chevron-left</v-icon></div>
      </template>

      <template v-slot:nav-next>
        <div><v-icon color="#008E00" size="32">mdi-chevron-right</v-icon></div>
      </template>
      <template v-slot:default='{ item }' >
        <a-skeleton :loading="check === true ? !loading : loading">
          <CardProducts :itemProduct='item' />
        </a-skeleton>
      </template>
    </vue-horizontal-list> -->
    <v-row dense v-if="!MobileSize && !IpadSize && dataRole === 'ext_buyer'" class="px-0">
      <v-col cols="2" :md="IpadProSize ? '3' : '2'" sm="2" xs="2" v-for="(item, index) in propsData" :key="index" class="mb-4 d-flex justify-center">
        <CardProducts :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row dense v-else-if="IpadSize && !MobileSize && dataRole === 'ext_buyer'" class="px-0">
      <v-col cols="3" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in propsData.slice(0, 12)" :key="index" class="mb-4" style="display: flex; justify-content: center;">
        <CardProductsResponsive :itemProduct='item'/>
      </v-col>
    </v-row>
    <v-row dense v-else-if="MobileSize && !IpadSize && dataRole === 'ext_buyer'" class="px-0">
      <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in cleanData" :key="index" class="mb-4 px-0" style="display: flex; justify-content: center;">
        <CardProductsResponsive :itemProduct='item'/>
      </v-col>
    </v-row>
    <v-row v-if="dataRole === 'ext_buyer'" dense justify="center" class="pt-10 py-16">
      <v-col cols="12" align="center">
        <v-btn class="white--text" style="border-radius: 40px; font-size: 16px; font-weight: 500;" :width="!MobileSize ? '242px' : ''" height="40px" color="#3EC6B6" @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`"><span style="text-transform: none">{{ $t('TextHome.ViewAllProducts') }}</span> <v-icon color="white">mdi-arrow-right</v-icon></v-btn>
      </v-col>
    </v-row>
    <!-- <v-fab-transition>
      <v-btn
        elevation="1"
        v-scroll="onScroll"
        v-show="fab"
        max-height="50"
        max-width="50"
        fab
        dark
        fixed
        bottom
        right
        color="#27AB9C"
        @click="toTop"
      >
        <v-icon>mdi-arrow-up</v-icon>
      </v-btn>
    </v-fab-transition> -->
  </v-container>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'
// import { Skeleton } from 'ant-design-vue'
export default {
  props: ['propsData', 'typeProduct', 'header', 'check'],
  components: {
    // 'a-skeleton': Skeleton,
    VueHorizontalList,
    CardProducts: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      dataRole: '',
      fab: false,
      options: {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 5 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 16
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      loading: true,
      cleandata: [],
      page: 1,
      path: process.env.VUE_APP_DOMAIN,
      typeP: this.typeProduct
    }
  },
  created () {
    // this.cleanData()
    if (localStorage.getItem('roleUser') !== null) {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.dataRole = 'ext_buyer'
    }
    this.typeP = 'all_product_cat'
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    cleanData () {
      // console.log(this.propsData)
      var array1 = []
      array1 = this.propsData
      var i
      var cleandata = []
      for (i = 0; i < array1.length; i++) {
        if (i < 6) {
          cleandata.push(array1[i])
        }
      }
      return cleandata
    }
  },
  methods: {
    GetAllProductRightClick () {
      this.$router.push(`/ListProduct/${this.typeProduct}?page=1`).catch(() => {})
    },
    GetAllProduct () {
      this.$router.push(`/ListProduct/${this.typeProduct}?page=1`).catch(() => {})
    },
    onScroll (e) {
      if (typeof window === 'undefined') return
      const top = window.pageYOffset || e.target.scrollTop || 0
      this.fab = top > 20
    },
    toTop () {
      this.$vuetify.goTo(0)
    }

  }
}
</script>
<style lang="scss"  scoped>
.slick-slider {
  width: 100%;
  height: 100%;
  padding: 1%;
  ::v-deep .slick-arrow:before {
    color: #008E00;
    font-size: 30px;
  }
}
</style>

<style scoped>
.container {
  max-width: 100vw;
  width: 100%;
}
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
