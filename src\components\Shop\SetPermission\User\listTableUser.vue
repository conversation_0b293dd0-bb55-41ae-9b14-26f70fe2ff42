<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <!-- Modal เพิ่มตำแหน่ง ***-->
    <v-dialog v-model="modalAddPosition" width="579px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalAddPosition = !modalAddPosition" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container class="mb-0">
          <v-card-text class="mb-0 pb-0">
            <v-row v-if="!MobileSize">
              <v-col cols="12" md="2" sm="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="12" md="10" sm="10" class="mt-5 pt-2" style="font-size: 16px;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col cols="3" md="2" sm="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="9" md="10" sm="10" class="mt-2 pt-2" style="font-size: 14px; font-weight: 600;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="12">
                <v-text-field :rules="Rules.emailRules" @keyup.enter="searchData" v-model="email_user" solo dense
                  label="ระบุอีเมล" append-icon="mdi-magnify" ref="Emails">
                </v-text-field>
              </v-col>
            </v-row>
            <v-row v-if="notFoundUser" justify="center">
              <v-col cols="12" class="center-screen" align="center">
                <v-img lazy-src="@/assets/NotData.png" max-height="187.79px" max-width="172.15px"
                  src="@/assets/NotData.png"></v-img>
              </v-col>
            </v-row>
            <!-- <v-form ref="FindUser" :lazy-validation="lazy">
              <v-row dense justify="center">
                <v-col cols="12">
                  <span>ค้นหาผู้ใช้งาน <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" md="9" sm="9" xs="12">
                      <v-text-field placeholder="ระบุอีเมล" outlined dense v-model="email" :rules="Rules.emailRules"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" sm="3" xs="12">
                      <v-btn class="px-5 white--text" color="#27AB9C" @click="save()">ค้นหา</v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-form> -->
          </v-card-text>
        </v-container>
        <v-container v-show="notFoundUser">
          <v-card-text>
            <v-row dense no-gutters justify="center" class="mb-4">
              <v-col cols="12" md="12" sm="12" xs="12" align="center">
                <span>ไม่พบข้อมูล</span>
              </v-col>
            </v-row>
            <v-row dense no-gutters justify="center">
              <v-col cols="12" md="4" sm="5" xs="12" class="pt-2" align="center">
                <v-btn>เพิ่มข้อมูลใน One ID</v-btn>
              </v-col>
              <v-col cols="12" md="1" sm="1" xs="12" class="pt-4" align="center">
                <span>หรือ</span>
              </v-col>
              <v-col cols="12" md="4" sm="5" xs="12" class="pt-2" align="center">
                <v-btn>ติดต่อเจ้าหน้าที่</v-btn>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="modalAddPosition = !modalAddPosition">ยกเลิก</v-btn>
          </v-card-actions>
        </v-container>
        <v-container v-show="FoundUser">
          <div v-if="changeToSignCA === false">
            <v-card v-if="!MobileSize">
              <v-form ref="FormAddPosition" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="12" md="3" sm="3" justify-center>
                    <!-- <v-img lazy-src="@/assets/temp/image/gulf2.jpg" max-height="150" max-width="250" :src="img_detail">
                  </v-img> -->
                    <v-card-text class="rounded-lg mt-2">
                      <v-img lazy-src="img_detail" max-height="150" max-width="150" :src="img_detail" contain></v-img>
                    </v-card-text>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-row no-gutters class="mt-3 ml-6">
                      <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                          ชื่อ-สกุล :
                        </p>
                      </v-col>
                      <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 14px; color: #333333;">
                          {{ detail_name }}
                        </p>
                      </v-col>
                      <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          อีเมล :
                        </p>
                      </v-col>
                      <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                          {{ detail_email }}
                        </p>
                      </v-col>
                      <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          เบอร์โทรศัพท์ :
                        </p>
                      </v-col>
                      <v-col cols="8">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                          {{ detail_phone }}
                        </p>
                      </v-col>
                    </v-row>
                    <!-- <v-row dense no-gutters class="ml-4">
                      <v-col cols="12" md="2" sm="2" xs="12">
                        <span>ชื่อ - สกุล</span>
                      </v-col>
                      <v-col cols="12" md="10" sm="10" xs="12">
                        <span>{{ detail_name }}</span>
                      </v-col>
                      <v-col cols="12" md="2" sm="2" xs="12">
                        <span>อีเมล</span>
                      </v-col>
                      <v-col cols="12" md="10" sm="10" xs="12">
                        {{ detail_email }}
                      </v-col>
                      <v-col cols="12" md="2" sm="2" xs="12">
                        <span>เบอร์โทรศัพท์</span>
                      </v-col>
                      <v-col cols="12" md="10" sm="10" xs="12">
                        <span> {{ detail_phone }}</span>
                      </v-col>
                    </v-row> -->
                    <v-row dense no-gutters>
                      <v-col cols="12" class="mt-4 ml-4">
                        <v-autocomplete v-model="values" :rules="Rules.position_name" :items="itemPosition" dense chips small-chips
                          item-text="position_name" return-object label="กรุณาเลือกตำแหน่ง" multiple solo>
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
            </v-card>
            <v-card v-else>
              <v-card-text class="px-0">
                <v-form ref="FormAddPosition" :lazy-validation="lazy">
                  <v-row no-gutters>
                    <v-col cols="3" md="3" sm="3" justify-center>
                      <!-- <v-img lazy-src="@/assets/temp/image/gulf2.jpg" max-height="150" max-width="250" :src="img_detail">
                    </v-img> -->
                      <v-card-text class="rounded-lg mt-2">
                        <v-img lazy-src="img_detail" max-height="88" max-width="100" :src="img_detail" contain></v-img>
                      </v-card-text>
                    </v-col>
                    <v-col cols="9" md="8" sm="8">
                      <v-row no-gutters class="mt-3 ml-6">
                        <v-col cols="4">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            ชื่อ-สกุล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_name }}
                          </p>
                        </v-col>
                        <v-col cols="3">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            อีเมล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_email }}
                          </p>
                        </v-col>
                        <v-col cols="6">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            เบอร์โทรศัพท์ :
                          </p>
                        </v-col>
                        <v-col cols="6">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_phone }}
                          </p>
                        </v-col>
                      </v-row>
                      <!-- <v-row dense no-gutters class="ml-4">
                        <v-col cols="12" md="2" sm="2" xs="12">
                          <span>ชื่อ - สกุล</span>
                        </v-col>
                        <v-col cols="12" md="10" sm="10" xs="12">
                          <span>{{ detail_name }}</span>
                        </v-col>
                        <v-col cols="12" md="2" sm="2" xs="12">
                          <span>อีเมล</span>
                        </v-col>
                        <v-col cols="12" md="10" sm="10" xs="12">
                          {{ detail_email }}
                        </v-col>
                        <v-col cols="12" md="2" sm="2" xs="12">
                          <span>เบอร์โทรศัพท์</span>
                        </v-col>
                        <v-col cols="12" md="10" sm="10" xs="12">
                          <span> {{ detail_phone }}</span>
                        </v-col>
                      </v-row> -->
                      <v-row dense no-gutters>
                        <v-col cols="12" class="mt-4 ml-4">
                          <v-autocomplete v-model="values" :items="itemPosition" dense chips small-chips :rules="Rules.position_name"
                            item-text="position_name" return-object label="กรุณาเลือกตำแหน่ง" multiple solo style="font-size: 14px; width: 195px;">
                          </v-autocomplete>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-card>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="modalAddPosition = !modalAddPosition">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="AddUserSeller()" :disabled="disableButton">บันทึก</v-btn>
            </v-card-actions>
          </div>
          <div class="px-4" v-else>
            <v-form ref="formAddCA" :lazy-validation="lazyCA">
              <v-row dense>
                <v-col cols="12" md="12">
                  <span style="font-weight: 700; font-size: 16px;">วันที่มีผลบังคับใช้</span><span style="color: red; font-weight: 400; font-size: 16px;"> *</span>
                  <v-dialog
                    ref="dialog"
                    v-model="modal"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="date"
                        placeholder="เลือกวันที่มีผลบังคับใช้"
                        prepend-icon="mdi-calendar"
                        readonly
                        outlined
                        :rules="Rules.date"
                        dense
                        v-bind="attrs"
                        v-on="on"
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="date"
                      scrollable
                      locale="th-TH"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="$refs.dialog.save(date)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-form>
            <v-row dense>
              <v-col cols="12" md="12" align="end" class="d-flex">
                <span class="mr-auto pt-2" style="font-weight: 700; font-size: 16px;">ลายเซ็น <span style="color: red; font-weight: 400; font-size: 16px;">*</span></span>
                <v-btn class="ml-auto" rounded color="#27AB9C" outlined @click="clearSign()">ล้างค่า</v-btn>
              </v-col>
            </v-row>
            <v-card width="100%" height="100%" class="my-2">
              <v-card-text class="pa-0">
                <div id="app">
                  <vueSignature
                   ref="signature"
                   :sigOption="option"
                   :w="'100%'"
                   :h="'100%'"
                   :defaultUrl="dataUrl"
                  ></vueSignature>
                </div>
              </v-card-text>
            </v-card>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="backToSelectPosition()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="SendAttorney(statusSendCA)" :disabled="disableButton">บันทึก</v-btn>
            </v-card-actions>
          </div>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal แสดงข้อมูล -->
    <v-dialog v-model="modalShowPosition" width="590px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card class="rounded-lg">
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalShowPosition = !modalShowPosition" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text>
            <v-row v-if="!MobileSize">
              <v-col cols="12" md="2" sm="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="12" md="10" sm="10" class="mt-5 pt-2" style="font-size: 16px;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col cols="3" md="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="9" md="10" class="pt-4" style="font-size: 12px;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row style="border: 1px solid #E6E6E6;" v-if="!MobileSize">
              <v-col cols="12" md="3" sm="3">
                <!-- <v-img lazy-src="@/assets/temp/image/gulf2.jpg" max-height="150" max-width="250" :src="img_detail">
                </v-img> -->
                <v-img v-if="img_detail !== ''" lazy-src="img_detail" max-height="150" max-width="250" :src="img_detail"></v-img>
              </v-col>
              <v-col cols="12" md="8" sm="8">
                <v-row no-gutters class="mt-3 pl-4">
                  <v-col cols="3">
                    <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                      ชื่อ-สกุล :
                    </p>
                  </v-col>
                  <v-col cols="9">
                    <p style="font-weight: 700; font-size: 14px; line-height: 14px; color: #333333;">
                      {{ detail_name }}
                    </p>
                  </v-col>
                  <v-col cols="3">
                    <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                      อีเมล :
                    </p>
                  </v-col>
                  <v-col cols="9">
                    <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                      {{ detail_email }}
                    </p>
                  </v-col>
                  <v-col cols="4">
                    <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                      เบอร์โทรศัพท์ :
                    </p>
                  </v-col>
                  <v-col cols="8">
                    <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                      {{ detail_phone }}
                    </p>
                  </v-col>
                  <v-col cols="12" v-for="(item, index) in values" :key="index">
                    <v-row>
                      <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          ตำแหน่ง :
                        </p>
                      </v-col>
                      <v-col cols="9" class="pt-2">
                        <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                          {{ useAttorney === 'yes' ? item.position_name.replace(" (ใช้หนังสือมอบอำนาจ)", "") : item.position_name }}
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="1" sm="1">
                <v-icon @click="ShowEditPosition()">mdi-pencil-outline</v-icon>
              </v-col>
            </v-row>
            <v-row style="border: 1px solid #E6E6E6;" v-else>
              <v-col cols="3" md="3">
                <!-- <v-img lazy-src="@/assets/temp/image/gulf2.jpg" max-height="150" max-width="250" :src="img_detail">
                </v-img> -->
                <v-img v-if="img_detail !== ''" lazy-src="img_detail" max-height="88" max-width="100" :src="img_detail"></v-img>
              </v-col>
              <v-col cols="9" class="pl-0 pt-0">
                <v-row no-gutters class="mt-3">
                  <v-col cols="4">
                    <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                      ชื่อ-สกุล :
                    </p>
                  </v-col>
                  <v-col cols="8" class="pl-0">
                    <v-row dense no-gutters>
                      <v-col cols="10">
                        <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                          {{ detail_name }}
                        </p>
                      </v-col>
                      <v-col cols="2">
                        <v-icon @click="ShowEditPosition()" color="#27AB9C">mdi-pencil-outline</v-icon>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="3">
                    <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                      อีเมล :
                    </p>
                  </v-col>
                  <v-col cols="9">
                    <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                      {{ detail_email }}
                    </p>
                  </v-col>
                  <v-col cols="6" class="pr-0">
                    <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                      เบอร์โทรศัพท์ :
                    </p>
                  </v-col>
                  <v-col cols="6" class="pl-0">
                    <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                      {{ detail_phone }}
                    </p>
                  </v-col>
                  <v-col cols="12" v-for="(item, index) in values" :key="index">
                    <v-row dense>
                      <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                          ตำแหน่ง :
                        </p>
                      </v-col>
                      <v-col cols="8">
                        <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                          {{ useAttorney === 'yes' ? item.position_name.replace(" (ใช้หนังสือมอบอำนาจ)", "") : item.position_name }}
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <!-- <v-col cols="12" md="1">
                <v-icon @click="ShowEditPosition()">mdi-pencil-outline</v-icon>
              </v-col> -->
            </v-row>
            <!-- <v-row>
              <v-col cols="12" class="center-screen">
                <v-img
                  lazy-src="@/assets/NotData.png"
                  max-height="187.79px"
                  max-width="172.15px"
                  src="@/assets/NotData.png"
                ></v-img>
              </v-col>
            </v-row> -->
            <!--  <v-form ref="FormAddPosition" :lazy-validation="lazy">
              <v-row dense no-gutters>
                <v-col cols="12" md="12" sm="12" xs="12" class="mb-4" align="end">
                  <div style="cursor: pointer;"><v-icon>mdi-pencil-outline</v-icon>
                  <span color="#27AB9C" @click="ShowEditPosition(item)" class="pt-4 pb-4 pt-2">
                    แก้ไข
                  </span>
                  </div>
                </v-col>
              </v-row>
              <v-row dense no-gutters class="ml-4">
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>ชื่อ - สกุล</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span>นายซี ใจใจ</span>
                </v-col>
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>อีเมล</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span><EMAIL></span>
                </v-col>
                <v-col cols="12" md="2" sm="2" xs="12">
                  <span>เบอร์โทรศัพท์</span>
                </v-col>
                <v-col cols="12" md="10" sm="10" xs="12">
                  <span>088-5862245</span>
                </v-col>
                <v-row dense no-gutters class="mt-4 ml-3">
                  <v-col cols="12" md="6" sm="6" xs="12">
                    <p>ตำแหน่ง เจ้าหน้าที่การเงิน</p>
                  </v-col>
                  <v-col cols="12" md="6" sm="6" xs="12">
                    <p>สร้างเมื่อ : 31 พฤษภาคม 2565</p>
                    <p>แก้ไขเมื่อ : 31 พฤษภาคม 2565</p>
                  </v-col>
                </v-row>
              </v-row>
            </v-form> -->
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" dark color="#27AB9C" @click="modalShowPosition = !modalShowPosition">ย้อนกลับ</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal แก้ไขตำแหน่ง -->
    <v-dialog v-model="modalEditPosition" width="579px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card class="rounded-lg">
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalEditPosition = !modalEditPosition" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container>
          <div v-if="changeToSignCA === false">
            <v-card class="rounded-lg mt-1" v-if="!MobileSize" elevation="0">
              <!-- <v-card-text> -->
              <v-form class="pa-2" ref="FormAddPosition" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="12" md="3" sm="3" justify-center>
                    <!-- <v-img lazy-src="@/assets/temp/image/gulf2.jpg" max-height="150" max-width="250" :src="img_detail">
                  </v-img> -->
                    <v-card-text class="rounded-lg mt-2">
                      <v-img max-height="150" max-width="150" :src="img_detail" contain v-if="img_detail !== ''"></v-img>
                      <v-img max-height="150" max-width="150" src="@/assets/NoImage.png" contain v-else></v-img>
                      <!-- <v-img max-height="150" max-width="150" :src="img_detail" contain ></v-img>
                      <v-img max-height="150" max-width="150" src="@/assets/NoImage.png" contain></v-img> -->
                    </v-card-text>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-row no-gutters class="mt-3 ml-6">
                      <v-col cols="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                          ชื่อ-สกุล : <b>{{ detail_name }}</b>
                        </p>
                      </v-col>
                      <!-- <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 14px; color: #333333;">
                          {{ detail_name }}
                        </p>
                      </v-col> -->
                      <v-col cols="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          อีเมล : <b>{{ detail_email }}</b>
                        </p>
                      </v-col>
                      <!-- <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                          {{ detail_email }}
                        </p>
                      </v-col> -->
                      <v-col cols="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          เบอร์โทรศัพท์ : <b>{{ detail_phone }}</b>
                        </p>
                      </v-col>
                      <!-- <v-col cols="8">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                          {{ detail_phone }}
                        </p>
                      </v-col> -->
                    </v-row>
                    <!-- <v-row dense no-gutters class="ml-4">
                    <v-col cols="12" md="2" sm="2" xs="12">
                      <span>ชื่อ - สกุล</span>
                    </v-col>
                    <v-col cols="12" md="10" sm="10" xs="12">
                      <span>{{ detail_name }}</span>
                    </v-col>
                    <v-col cols="12" md="2" sm="2" xs="12">
                      <span>อีเมล</span>
                    </v-col>
                    <v-col cols="12" md="10" sm="10" xs="12">
                      <span>{{ detail_email }}</span>
                    </v-col>
                    <v-col cols="12" md="2" sm="2" xs="12">
                      <span>เบอร์โทรศัพท์</span>
                    </v-col>
                    <v-col cols="12" md="10" sm="10" xs="12">
                      <span>{{ detail_phone }}</span>
                    </v-col>
                  </v-row> -->
                    <v-row dense no-gutters>
                      <v-col cols="12" class="mt-4 ml-4">
                        <v-autocomplete v-model="values" :items="itemPosition" item-text="position_name" return-object
                          dense chips label="กรุณาเลือกตำแหน่ง" multiple solo>
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
              <!-- </v-card-text> -->
            </v-card>
            <v-card class="rounded-lg mt-1" v-else elevation="0">
              <v-card-text class="px-0">
                <v-form class="py-2 px-0" ref="FormAddPosition" :lazy-validation="lazy">
                  <v-row no-gutters dense>
                    <v-col cols="3" md="3" justify-center>
                      <!-- <v-img lazy-src="@/assets/temp/image/gulf2.jpg" max-height="150" max-width="250" :src="img_detail">
                    </v-img> -->
                      <v-card-text class="rounded-lg mt-2">
                        <v-img max-height="88" max-width="100" :src="img_detail" contain v-if="img_detail !== '' && img_detail !== null"></v-img>
                        <v-img max-height="88" max-width="100" src="@/assets/NoImage.png" contain v-else></v-img>
                      </v-card-text>
                    </v-col>
                    <v-col cols="9" md="8">
                      <v-row no-gutters class="mt-3 ml-6">
                        <v-col cols="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            ชื่อ-สกุล : <b>{{ detail_name }}</b>
                          </p>
                        </v-col>
                        <!-- <v-col cols="8">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_name }}
                          </p>
                        </v-col> -->
                        <v-col cols="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            อีเมล : <b>{{ detail_email }}</b>
                          </p>
                        </v-col>
                        <!-- <v-col cols="9">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_email }}
                          </p>
                        </v-col> -->
                        <v-col cols="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            เบอร์โทรศัพท์ : <b> {{ detail_phone }}</b>
                          </p>
                        </v-col>
                        <!-- <v-col cols="6">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_phone }}
                          </p>
                        </v-col> -->
                      </v-row>
                      <!-- <v-row dense no-gutters class="ml-4">
                      <v-col cols="12" md="2" sm="2" xs="12">
                        <span>ชื่อ - สกุล</span>
                      </v-col>
                      <v-col cols="12" md="10" sm="10" xs="12">
                        <span>{{ detail_name }}</span>
                      </v-col>
                      <v-col cols="12" md="2" sm="2" xs="12">
                        <span>อีเมล</span>
                      </v-col>
                      <v-col cols="12" md="10" sm="10" xs="12">
                        <span>{{ detail_email }}</span>
                      </v-col>
                      <v-col cols="12" md="2" sm="2" xs="12">
                        <span>เบอร์โทรศัพท์</span>
                      </v-col>
                      <v-col cols="12" md="10" sm="10" xs="12">
                        <span>{{ detail_phone }}</span>
                      </v-col>
                    </v-row> -->
                    <v-row dense no-gutters>
                      <v-col cols="12" class="mt-4 ml-4">
                        <v-autocomplete v-model="values" :items="itemPosition" item-text="position_name" return-object
                          dense chips label="กรุณาเลือกตำแหน่ง" multiple solo style="font-size: 14px; width: 195px;">
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-card>
            <v-card-actions class="mt-5">
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="modalEditPosition = !modalEditPosition">ยกเลิก
              </v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="EditUserSeller()" :disabled="disableButton">บันทึก</v-btn>
            </v-card-actions>
          </div>
          <div class="px-4" v-else>
            <v-form ref="formAddCA" :lazy-validation="lazyCA">
              <v-row dense>
                <v-col cols="12" md="12">
                  <span style="font-weight: 700; font-size: 16px;">วันที่มีผลบังคับใช้</span><span style="color: red; font-weight: 400; font-size: 16px;"> *</span>
                  <v-dialog
                    ref="dialog"
                    v-model="modal"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="date"
                        placeholder="เลือกวันที่มีผลบังคับใช้"
                        prepend-icon="mdi-calendar"
                        readonly
                        outlined
                        :rules="Rules.date"
                        dense
                        v-bind="attrs"
                        v-on="on"
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="date"
                      scrollable
                      locale="th-TH"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="$refs.dialog.save(date)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-form>
            <v-row dense>
              <v-col cols="12" md="12" align="end" class="d-flex">
                <span class="mr-auto pt-2" style="font-weight: 700; font-size: 16px;">ลายเซ็น <span style="color: red; font-weight: 400; font-size: 16px;">*</span></span>
                <v-btn class="ml-auto" rounded color="#27AB9C" outlined @click="clearSign()">ล้างค่า</v-btn>
              </v-col>
            </v-row>
            <v-card width="100%" height="100%" class="my-2">
              <v-card-text class="pa-0">
                <div id="app">
                  <vueSignature
                   ref="signature"
                   :sigOption="option"
                   :w="'100%'"
                   :h="'100%'"
                   :defaultUrl="dataUrl"
                  ></vueSignature>
                </div>
              </v-card-text>
            </v-card>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="backToSelectPosition()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="SendAttorney()" :disabled="disableButton">บันทึก</v-btn>
            </v-card-actions>
          </div>
        </v-container>
      </v-card>
    </v-dialog>
    <v-row dense justify="center">
      <v-col cols="12" class="px-0 py-0">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense justify="center">
            <v-col cols="12" md="12" sm="12" xs="12">
              <v-card-title class="pb-0" style="font-weight: bold; font-size:24px; line-height: 32px;" v-if="!MobileSize">จัดการผู้ใช้งาน</v-card-title>
              <v-card-title class="px-0" style="font-weight: bold; font-size: 18px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> จัดการผู้ใช้งาน</v-card-title>
            </v-col>
          </v-row>
          <v-card-text>
            <v-row dense no-gutters class="mb-2">
              <v-col cols="12" class="py-0 mb-0">
                <a-tabs @change="SelectDetailPosition">
                  <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                  <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{
                  countAll }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="1"><span slot="tab">กำลังใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{
                  countActivePosition }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{
                  countInactivePosition }}</a-tag></span></a-tab-pane>
                </a-tabs>
              </v-col>
            </v-row>
            <v-row v-if="disableTable === true">
              <v-col cols="12" md="6" sm="12" xs="12" class="py-0 my-0">
                <v-text-field v-model="search" dense outlined placeholder="ค้นหาจากรายชื่อผู้ใช้งาน">
                  <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="6" sm="12" xs="12" align="end" class="py-0" v-if="!IpadSize">
                <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
                  @click="createPosition()" dark>
                  <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล
                </v-btn>
              </v-col>
              <v-col cols="12" sm="8" :class="MobileSize ? 'pl-4 pt-4' : IpadSize ? 'pl-4 pt-1' : 'pl-4 pt-0'">
                <!-- <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">แสดงรายการจำนวนสินค้า {{ itemsPerPage &lt; 0 ? DataTable.length : itemsPerPage }} รายการ</span> -->
                <span
                  style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="StateStatus === 0">แสดงรายชื่อผู้ใช้ในร้านค้าทั้งหมด
                  {{ showCountOrder }} รายการ</span>
                <span
                  style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 1">แสดงรายชื่อผู้ใช้ในร้านค้าที่กำลังใช้งานทั้งหมด
                  {{ showCountOrder }} รายการ</span>
                <span
                  style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 2">แสดงรายชื่อผู้ใช้ในร้านค้าที่ยกเลิกทั้งหมด
                  {{ showCountOrder }} รายการ</span>
              </v-col>
              <v-col cols="12" md="6" sm="4" xs="12" align="end" class="py-0" v-if="IpadSize">
                <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
                  @click="createPosition()" dark>
                  <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล
                </v-btn>
              </v-col>
              <v-col cols="12" md="12" sm="12" xs="12">
                <v-card outlined class="mb-4">
                  <v-data-table
                    :headers="keyCheckHead == 0 ? headersAll : keyCheckHead == 1 ? headersActivePosition : headersInactivePosition"
                    :items="DataTable" :items-per-page="10" :page.sync="page" :search="search" @pagination="countCompany" :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                    no-results-text="ไม่พบรายชื่อผู้ใช้งานที่ค้นหา" class="" no-data-text="ไม่มีรายชื่อผู้ใช้งานในตาราง"
                    :update:items-per-page="getItemPerPage">
                    <template v-slot:[`item.id`]="{ item }">
                      {{ DataTable.map(x => {return x.id; }).indexOf(item.id) + 1 }}
                    </template>
                    <template v-slot:[`item.full_name_th`]="{ item }">
                      {{ item.first_name_th + ' ' + item.last_name_th }}
                    </template>
                    <template v-slot:[`item.status`]="{ item }">
                      <span v-if="item.status === 'active'">
                        <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                      </span>
                      <span v-else-if="item.status === 'inactive'">
                        <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f50">ยกเลิก</v-chip>
                      </span>
                    </template>
                    <template v-slot:[`item.actions`]="{ item }">
                      <v-row dense justify="center">
                        <span @click="ShowDetail(item)" class="pt-4 pb-4" style="cursor: pointer; color: #27AB9C;"
                          color="Primary">
                          รายละเอียด <v-icon style="color: #27AB9C;">mdi-chevron-right</v-icon>
                        </span>
                      </v-row>
                    </template>
                  </v-data-table>
                </v-card>
                <!-- <div class="text-center pt-2">
                  <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
                </div> -->
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" v-else>
              <!-- <v-col cols="12" class="py-0">
                <a-tabs @change="SelectDetailPosition">
                  <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{
                  countAll }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="1"><span slot="tab">กำลังใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{
                  countActivePosition }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{
                  countInactivePosition }}</a-tag></span></a-tab-pane>
                </a-tabs>
              </v-col> -->
              <v-col cols="12" md="12" align="center">
                <div class="my-5">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px"
                    max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 v-if="StateStatus === 0" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อผู้ใช้ในร้านค้าทั้งหมด</span><br />
                  <!-- <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span  style="font-size: 28px;">“เพิ่มข้อมูล”</span> เพื่อเพิ่มตำแหน่งที่สมบูรณ์ของคุณ</span> -->
                </h2>
                <h2 v-else-if="StateStatus === 1" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อผู้ใช้ในร้านค้าที่กำลังใช้งาน</span><br />
                  <!-- <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span style="font-size: 28px;">“เพิ่มข้อมูล”</span> เพื่อเพิ่มตำแหน่งที่สมบูรณ์ของคุณ</span> -->
                </h2>
                <h2 v-else-if="StateStatus === 2" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อผู้ใช้ในร้านค้าที่ยกเลิก</span><br />
                  <!-- <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span style="font-size: 28px;">“เพิ่มข้อมูล”</span> เพื่อเพิ่มตำแหน่งที่สมบูรณ์ของคุณ</span> -->
                </h2>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import vueSignature from 'vue-signature'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    vueSignature
  },
  data () {
    return {
      oneData: [],
      lazyCA: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      modal: false,
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      search: '',
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: true,
      keyCheckHead: 0,
      countAll: 0,
      countActivePosition: 0,
      countInactivePosition: 0,
      DataTable: [],
      lazy: false,
      companyData: [],
      pathEditCompany: '',
      notFoundUser: false,
      FoundUser: false,
      modalAddPosition: false,
      modalEditPosition: false,
      modalShowPosition: false,
      email: '',
      sellerShop: '',
      positionType: [],
      detail_name: '',
      detail_email: '',
      detail_phone: '',
      img_detail: '',
      itemPosition: [],
      disableButton: false,
      values: [],
      user_id: '',
      email_user: '',
      items: ['foo', 'bar', 'fizz', 'buzz'],
      itemMenuShop: [
        { key: 1, title: 'รายการสินค้า' },
        { key: 2, title: 'ดูรายการสั่งซื้อ' },
        { key: 3, title: 'เรียกพนักงานรับพัสดุ' },
        { key: 4, title: 'ติดตามสถานะสินค้า' },
        { key: 5, title: 'สต๊อกสินค้า' },
        { key: 6, title: 'จัดการร้านค้า' },
        { key: 7, title: 'การคืนสินค้า' },
        { key: 8, title: 'แดชบอร์ด' },
        { key: 9, title: 'เอกสารและคู่มือการใช้งาน' },
        { key: 10, title: 'รายได้ของฉัน' },
        { key: 11, title: 'จัดการตำแหน่งและสิทธิ์การใช้งาน' },
        { key: 12, title: 'จัดการผู้ใช้งาน' }
      ],
      Rules: {
        position_name: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่',
          v => (/^[-0-9/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => ((/^[0-9/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ],
        date: [
          v => !!v || 'กรุณาเลือกวันที่มีผลบังคับใช้'
        ]
      },
      headersAll: [
        { text: 'ลำดับที่', value: 'id', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ - สกุล', value: 'full_name_th', width: '180', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'อีเมล', filterable: false, value: 'email', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', filterable: false, value: 'actions', sortable: false, width: '180', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersActivePosition: [
        { text: 'ลำดับที่', filterable: false, value: 'id', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ - สกุล', value: 'full_name_th', width: '180', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'อีเมล', filterable: false, value: 'email', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', filterable: false, value: 'actions', sortable: false, width: '180', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersInactivePosition: [
        { text: 'ลำดับที่', filterable: false, value: 'id', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ - สกุล', value: 'full_name_th', width: '180', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'อีเมล', filterable: false, value: 'email', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', filterable: false, value: 'actions', sortable: false, width: '180', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      changeToSignCA: false,
      option: {
        penColor: 'rgba(12,38,154,255)',
        backgroundColor: 'rgb(255, 255, 255)'
      },
      dataUrl: '',
      listPositionUser: [],
      statusSendCA: '',
      useAttorney: ''
    }
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNav')
    this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.sellerShop = localStorage.getItem('shopSellerID')
    var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      if (dataDetail.can_use_function_in_shop.manage_user_with_position === '1') {
        this.getListCompany()
      } else {
        this.$router.push({ path: '/' })
      }
    } else {
      this.$router.push({ path: '/' })
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listShopUserMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listShopUser' }).catch(() => {})
      }
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.companyData.list_user
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        // this.DataTable = [
        //   {
        //     id: 1,
        //     username: 'นางสาวเอ คนดี',
        //     position: 'เจ้าหน้าที่การเงิน',
        //     status: 'active'
        //   },
        //   {
        //     id: 2,
        //     username: 'นายซี คนเป็น',
        //     position: 'ผู้ดูแลระบบระดับสูง',
        //     status: 'active'
        //   }
        // ]
        this.DataTable = this.companyData.list_user_active
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        // this.DataTable = [
        //   {
        //     id: 3,
        //     username: 'นายซี คนเป็น',
        //     position: 'เจ้าหน้าที่การเงิน',
        //     status: 'inactive'
        //   }
        // ]
        this.DataTable = this.companyData.list_user_inactive
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    clearSign () {
      this.$refs.signature.clear()
    },
    backToSelectPosition () {
      this.changeToSignCA = false
      this.$refs.signature.clear()
    },
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async getListCompany () {
      this.$store.commit('openLoader')
      this.countAll = 0
      this.countActivePosition = 0
      this.countInactivePosition = 0
      // await this.$store.dispatch('actionsListCompany')
      // var response = await this.$store.state.ModuleAdminManage.stateListCompany
      var position = {
        seller_shop_id: parseInt(this.sellerShop),
        role: 'seller'
      }
      await this.$store.dispatch('actionsListPositionSellerUser', position)
      var response = await this.$store.state.ModuleAdminManage.stateListPositionSellerUser
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.companyData = response.data
        this.useAttorney = response.data.use_Attorney
        this.countAll = this.companyData.list_user.length
        this.countActivePosition = this.companyData.list_user_active.length
        this.countInactivePosition = this.companyData.list_user_inactive.length
        if (this.StateStatus === 0) {
          this.DataTable = this.companyData.list_user
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.countAll = this.DataTable.length
            this.disableTable = true
          }
        } else if (this.StateStatus === 1) {
          this.DataTable = this.companyData.list_user_active
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 2) {
          this.DataTable = this.companyData.list_user_inactive
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.countInactivePosition = this.DataTable.length
            this.disableTable = true
          }
        }
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    async searchData () {
      const isValidate = this.$refs.Emails.validate()
      if (isValidate) {
        var dataSearch = {
          seller_shop_id: parseInt(this.sellerShop),
          user_email: this.email_user
        }
        await this.$store.dispatch('actionsSearchUserPositionSeller', dataSearch)
        var response = await this.$store.state.ModuleAdminManage.stateSearchPositionSellerUser
        if (response.code === 200) {
          if (response.message === 'This email can use to setting position and role in shop.') {
            this.modalEditPosition = false
            this.changeToSignCA = false
            this.FoundUser = true
            this.notFoundUser = false
            this.values = ''
            this.img_detail = response.data.img_path === null ? '' : response.data.img_path
            this.detail_name = response.data.first_name_th + '  ' + response.data.last_name_th
            this.detail_email = response.data.email
            this.detail_phone = response.data.phone
            this.user_id = response.data.user_id
            this.$refs.FormAddPosition.resetValidation()
            // this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
          } else {
            this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
          }
        } else {
          if (response.message === 'This user who invited is ready in your shop.') {
            this.notFoundUser = true
            this.FoundUser = false
            this.$swal.fire({ icon: 'error', showConfirmButton: false, timer: 2000, html: '<h3>กรุณาตรวจสอบในระบบอีกครั้ง เนื่องจาก User มีข้อมูลในระบบแล้ว</h3>' })
          } else {
            this.notFoundUser = true
            this.FoundUser = false
            this.$swal.fire({ icon: 'error', showConfirmButton: false, timer: 2000, html: '<h3>ไม่พบข้อมูลในระบบ กรุณาตรวจสอบอีกครั้ง</h3>' })
          }
        }
      } else {
        this.email_user = ''
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>โปรดตรวจสอบอีกครั้ง</h3>' })
      }
      // console.log('test', response)
    },
    async AddUserSeller () {
      if (this.$refs.FormAddPosition.validate(true)) {
        this.disableButton = true
        this.changeToSignCA = false
        this.$store.commit('openLoader')
        this.listPositionUser = []
        var listPositionUseCA = []
        var checkPositionCA = false
        for (let i = 0; i < this.values.length; i++) {
          this.listPositionUser.push(this.values[i].id)
          if (this.values[i].manage_income === '1' || this.values[i].sale_order === '1') {
            listPositionUseCA.push(this.values[i].id)
          }
        }
        checkPositionCA = this.values.some(position => position.manage_income === '1' || position.sale_order === '1')
        if (this.useAttorney === 'no') {
          var dataAdduser = {
            seller_shop_id: parseInt(this.sellerShop),
            position_id: this.listPositionUser,
            user_email: this.email_user
          }
          await this.$store.dispatch('actionsAddUserPositionSeller', dataAdduser)
          var response = await this.$store.state.ModuleAdminManage.stateAddPositionSellerUser
          if (response.code === 200) {
            // if (response.message === 'Update user with position and role success.') {
            await this.$store.dispatch('actionsAuthorityUser')
            var responseShop = await this.$store.state.ModuleUser.stateAuthorityUser
            var ListSeller = responseShop.data.list_shop_detail
            var CheckShop = ListSeller.filter(x => x.seller_shop_id === parseInt(this.sellerShop))
            if (CheckShop.length !== 0) {
              for (let i = 0; i < ListSeller.length; i++) {
                if (parseInt(this.sellerShop) === ListSeller[i].seller_shop_id) {
                  await localStorage.setItem('list_shop_detail', Encode.encode(ListSeller[i]))
                }
              }
            } else {
              localStorage.removeItem('list_shop_detail')
              this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
              this.$router.push({ path: '/' }).catch(() => {})
            }
            this.disableButton = false
            this.modalAddPosition = false
            this.modalEditPosition = false
            await this.$EventBus.$emit('AuthorityUser')
            await this.$EventBus.$emit('AuthorityUsers')
            await this.$EventBus.$emit('AuthorityUsersSellerMobile')
            this.getListCompany()
            this.$store.commit('closeLoader')
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
            // } else {
            //   this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
            // }
          } else {
            this.$store.commit('closeLoader')
            this.disableButton = false
            if (response.message === 'Please select at least one position.') {
              this.$swal.fire({ icon: 'error', text: 'กรุณาเลือกตำแหน่งอย่างน้อย 1 ตำแหน่ง', showConfirmButton: false, timer: 2000 })
            } else {
              this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
            }
          }
        } else {
          if (checkPositionCA === false) {
            var dataAdduser1 = {
              seller_shop_id: parseInt(this.sellerShop),
              position_id: this.listPositionUser,
              user_email: this.email_user
            }
            await this.$store.dispatch('actionsAddUserPositionSeller', dataAdduser1)
            var response1 = await this.$store.state.ModuleAdminManage.stateAddPositionSellerUser
            if (response1.code === 200) {
              // if (response.message === 'Update user with position and role success.') {
              await this.$store.dispatch('actionsAuthorityUser')
              var responseShop1 = await this.$store.state.ModuleUser.stateAuthorityUser
              var ListSeller1 = responseShop1.data.list_shop_detail
              var CheckShop1 = ListSeller1.filter(x => x.seller_shop_id === parseInt(this.sellerShop))
              if (CheckShop1.length !== 0) {
                for (let i = 0; i < ListSeller1.length; i++) {
                  if (parseInt(this.sellerShop) === ListSeller1[i].seller_shop_id) {
                    await localStorage.setItem('list_shop_detail', Encode.encode(ListSeller1[i]))
                  }
                }
              } else {
                localStorage.removeItem('list_shop_detail')
                this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
                this.$router.push({ path: '/' }).catch(() => {})
              }
              this.disableButton = false
              this.modalAddPosition = false
              this.modalEditPosition = false
              await this.$EventBus.$emit('AuthorityUser')
              await this.$EventBus.$emit('AuthorityUsers')
              await this.$EventBus.$emit('AuthorityUsersSellerMobile')
              this.getListCompany()
              this.$store.commit('closeLoader')
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
            } else {
              this.$store.commit('closeLoader')
              this.disableButton = false
              if (response.message === 'Please select at least one position.') {
                this.$swal.fire({ icon: 'error', text: 'กรุณาเลือกตำแหน่งอย่างน้อย 1 ตำแหน่ง', showConfirmButton: false, timer: 2000 })
              } else {
                this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
              }
            }
          } else {
            this.disableButton = false
            this.changeToSignCA = true
            this.statusSendCA = 'add'
            this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
            this.$store.commit('closeLoader')
          }
        }
      }
      // console.log('test', response)
    },
    async SendAttorney (status) {
      this.disableButton = true
      var response = ''
      this.$store.commit('openLoader')
      var sign = ''
      var dataAdduser = ''
      if (this.$refs.formAddCA.validate(true)) {
        // console.log(this.$refs.signature.isEmpty())
        // isEmpty() = true แปลว่าไม่มี
        if (!this.$refs.signature.isEmpty()) {
          sign = this.$refs.signature.save()
          var dataCA = {
            sender_user_id: this.oneData.user.user_id,
            receiver_user_id: this.user_id,
            seller_shop_id: parseInt(this.sellerShop),
            effective_date: this.date,
            sender_signature: sign.split(',')[1]
          }
          await this.$store.dispatch('actionAddAttorney', dataCA)
          var responseAddCA = await this.$store.state.ModuleShop.stateAddAttorney
          if (responseAddCA.message === 'Attorney added successfully') {
            if (status === 'add') {
              dataAdduser = {
                seller_shop_id: parseInt(this.sellerShop),
                position_id: this.listPositionUser,
                user_email: this.email_user
              }
              await this.$store.dispatch('actionsAddUserPositionSeller', dataAdduser)
              response = await this.$store.state.ModuleAdminManage.stateAddPositionSellerUser
            } else {
              dataAdduser = {
                seller_shop_id: parseInt(this.sellerShop),
                position_id: this.listPositionUser,
                user_in_shop_id: this.user_id
              }
              await this.$store.dispatch('actionsEditUserPositionSeller', dataAdduser)
              response = await this.$store.state.ModuleAdminManage.stateEditPositionSellerUser
            }
            if (response.code === 200) {
              await this.$store.dispatch('actionsAuthorityUser')
              var responseShop = await this.$store.state.ModuleUser.stateAuthorityUser
              var ListSeller = responseShop.data.list_shop_detail
              var CheckShop = ListSeller.filter(x => x.seller_shop_id === parseInt(this.sellerShop))
              if (CheckShop.length !== 0) {
                for (let i = 0; i < ListSeller.length; i++) {
                  if (parseInt(this.sellerShop) === ListSeller[i].seller_shop_id) {
                    await localStorage.setItem('list_shop_detail', Encode.encode(ListSeller[i]))
                  }
                }
              } else {
                localStorage.removeItem('list_shop_detail')
                this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
                this.$router.push({ path: '/' }).catch(() => {})
              }
              this.changeToSignCA = false
              this.$refs.signature.clear()
              this.disableButton = false
              this.modalAddPosition = false
              this.modalEditPosition = false
              await this.$EventBus.$emit('AuthorityUser')
              await this.$EventBus.$emit('AuthorityUsers')
              await this.$EventBus.$emit('AuthorityUsersSellerMobile')
              this.getListCompany()
              this.$store.commit('closeLoader')
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
            } else {
              this.$store.commit('closeLoader')
              this.disableButton = false
              this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.disableButton = false
            this.$swal.fire({ icon: 'error', text: responseAddCA.message, showConfirmButton: false, timer: 2000 })
          }
        } else {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.$swal.fire({ icon: 'error', text: 'กรุณาเซ็นลายเซ็น', showConfirmButton: false, timer: 2000 })
        }
      }
    },
    async EditUserSeller () {
      this.disableButton = true
      this.changeToSignCA = false
      this.$store.commit('openLoader')
      this.listPositionUser = []
      var listPositionUseCA = []
      var checkPositionCA = false
      for (let i = 0; i < this.values.length; i++) {
        this.listPositionUser.push(this.values[i].id)
        if (this.values[i].manage_income === '1' || this.values[i].sale_order === '1') {
          listPositionUseCA.push(this.values[i].id)
        }
      }
      checkPositionCA = this.values.some(position => position.manage_income === '1' || position.sale_order === '1')
      if (this.useAttorney === 'no') {
        var dataAdduser = {
          seller_shop_id: parseInt(this.sellerShop),
          position_id: this.listPositionUser,
          user_in_shop_id: this.user_id
        }
        await this.$store.dispatch('actionsEditUserPositionSeller', dataAdduser)
        var response = await this.$store.state.ModuleAdminManage.stateEditPositionSellerUser
        if (response.code === 200) {
          if (response.message === 'Update user with position and role success.') {
            await this.$store.dispatch('actionsAuthorityUser')
            var responseShop = await this.$store.state.ModuleUser.stateAuthorityUser
            var ListSeller = responseShop.data.list_shop_detail
            var CheckShop = ListSeller.filter(x => x.seller_shop_id === parseInt(this.sellerShop))
            if (CheckShop.length !== 0) {
              for (let i = 0; i < ListSeller.length; i++) {
                if (parseInt(this.sellerShop) === ListSeller[i].seller_shop_id) {
                  await localStorage.setItem('list_shop_detail', Encode.encode(ListSeller[i]))
                }
              }
            } else {
              localStorage.removeItem('list_shop_detail')
              this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
              this.$router.push({ path: '/' }).catch(() => {})
            }
            this.modalEditPosition = false
            this.disableButton = false
            await this.$EventBus.$emit('AuthorityUser')
            await this.$EventBus.$emit('AuthorityUsers')
            await this.$EventBus.$emit('AuthorityUsersSellerMobile')
            await this.getListCompany()
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
            this.$store.commit('closeLoader')
          } else {
            this.$store.commit('closeLoader')
            this.disableButton = false
            this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
          }
        } else {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        if (checkPositionCA === false) {
          var dataAdduser1 = {
            seller_shop_id: parseInt(this.sellerShop),
            position_id: this.listPositionUser,
            user_in_shop_id: this.user_id
          }
          await this.$store.dispatch('actionsEditUserPositionSeller', dataAdduser1)
          var response1 = await this.$store.state.ModuleAdminManage.stateEditPositionSellerUser
          if (response1.code === 200) {
            if (response1.message === 'Update user with position and role success.') {
              await this.$store.dispatch('actionsAuthorityUser')
              var responseShop1 = await this.$store.state.ModuleUser.stateAuthorityUser
              var ListSeller1 = responseShop1.data.list_shop_detail
              var CheckShop1 = ListSeller1.filter(x => x.seller_shop_id === parseInt(this.sellerShop))
              if (CheckShop1.length !== 0) {
                for (let i = 0; i < ListSeller1.length; i++) {
                  if (parseInt(this.sellerShop) === ListSeller1[i].seller_shop_id) {
                    await localStorage.setItem('list_shop_detail', Encode.encode(ListSeller1[i]))
                  }
                }
              } else {
                localStorage.removeItem('list_shop_detail')
                this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
                this.$router.push({ path: '/' }).catch(() => {})
              }
              this.modalEditPosition = false
              this.disableButton = false
              await this.$EventBus.$emit('AuthorityUser')
              await this.$EventBus.$emit('AuthorityUsers')
              await this.$EventBus.$emit('AuthorityUsersSellerMobile')
              await this.getListCompany()
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
              this.$store.commit('closeLoader')
            } else {
              this.$store.commit('closeLoader')
              this.disableButton = false
              this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.disableButton = false
            this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
          }
        } else {
          this.disableButton = false
          this.changeToSignCA = true
          this.statusSendCA = 'edit'
          this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
          this.$store.commit('closeLoader')
        }
      }
      // console.log('test', response)
    },
    async getDetailUser (val) {
      this.user_id = ''
      this.detail_name = ''
      this.detail_email = ''
      this.img_detail = ''
      this.detail_phone = ''
      this.values = []
      this.getPositionShop()
      var dataDetails = {
        seller_shop_id: parseInt(this.sellerShop),
        user_id_in_shop: val.id
      }
      await this.$store.dispatch('actionsDetailUserPositionSeller', dataDetails)
      var response = await this.$store.state.ModuleAdminManage.stateDetailPositionSellerUser
      if (response.message === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
      } else {
        this.detail_name = response.data[0].first_name_th + '  ' + response.data[0].last_name_th
        this.detail_email = response.data[0].email
        this.detail_phone = response.data[0].phone
        this.img_detail = response.data[0].img_path === null ? '' : response.data[0].img_path
        // this.itemPosition = response.data[0].position_and_role
        if (this.useAttorney === 'yes') {
          for (const data of response.data[0].position_and_role) {
            if (data.manage_income === '1' || data.sale_order === '1') {
              data.position_name = data.position_name + ' (ใช้หนังสือมอบอำนาจ)'
            }
          }
        }
        this.values = response.data[0].position_and_role
        this.user_id = val.id
      }
    },
    async getPositionShop () {
      // var setNewData = []
      var data = {
        seller_shop_id: parseInt(this.sellerShop)
      }
      await this.$store.dispatch('actionsListPositionShop', data)
      var response = await this.$store.state.ModuleAdminManage.stateListPositionShops
      if (response.code === 200) {
        this.itemPosition = []
        if (this.useAttorney === 'yes') {
          for (const data of response.data.list_position_active) {
            if (data.manage_income === '1' || data.sale_order === '1') {
              data.position_name = data.position_name + ' (ใช้หนังสือมอบอำนาจ)'
            }
          }
        }
        this.itemPosition = response.data.list_position_active
        // console.log('data ====>', this.itemPosition)
      } else {
        this.itemPosition = []
      }
      // console.log('dds', response)
    },
    async ShowDetail (val) {
      await this.getDetailUser(val)
      this.modalShowPosition = !this.modalShowPosition
    },
    ShowEditPosition () {
      this.changeToSignCA = false
      this.modalShowPosition = !this.modalShowPosition
      this.modalEditPosition = !this.modalEditPosition
    },
    async createPosition () {
      await this.getPositionShop()
      this.user_id = ''
      this.email_user = ''
      this.notFoundUser = false
      this.FoundUser = false
      this.modalAddPosition = !this.modalAddPosition
      this.$refs.Emails.resetValidation()
      // this.getDetailUser()
    },
    countCompany (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    SelectDetailPosition (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
      this.page = 1
    },
    async gotoCompanyDetail (val) {
      var data = {
        company_id: val.id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      // console.log(response)
      if (response.result === 'SUCCESS') {
        if (response.message === 'Show company detail success.') {
          localStorage.setItem('companyData', Encode.encode(response.data))
          this.$EventBus.$emit('getCompanyName')
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(5) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(5) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
