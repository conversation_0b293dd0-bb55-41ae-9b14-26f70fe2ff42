# Stage 0, based on Node.js, to build and compile Angular
FROM node:14.15.1-alpine as node
RUN apk add --no-cache git
WORKDIR /app
COPY package*.json /app/
COPY env.develop .env.production

RUN npm install
COPY ./ /app/
RUN npm run build
RUN cp /app/dist/index.html /app/dist/index.php
RUN rm /app/dist/index.html
CMD [ "http-server", "dist"]


# Stage 1, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM nginx:1.19.6-alpine
COPY --from=node /app/dist/ /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 5555
CMD ["nginx", "-g", "daemon off;"]
# RUN mkdir -p /etc/ssl/one.th/
# COPY ./one.th.crt /etc/ssl/one.th/one.th.crt
# COPY ./one.th.key /etc/ssl/one.th/one.th.key
