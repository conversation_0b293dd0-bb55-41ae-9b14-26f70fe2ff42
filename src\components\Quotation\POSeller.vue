<template>
  <v-container grid-list-xs>
    <!-- <v-card> -->
      <h2>รายการแสดงข้อมูลการสั่งซื้อ </h2>
      <!-- <v-card-subtitle>description description description description description description </v-card-subtitle> -->
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-row no-gutters justify="end">
      <!-- <v-col cols="12" class="py-0">
        <a-tabs @change="SelectDetailOrder">
          <a-tab-pane v-for="item in OrderName" :key="item.key" :tab="item.name"></a-tab-pane>
        </a-tabs>
      </v-col> -->
      <v-col cols="4" class="mr-4">
        <v-text-field
          v-model="search"
          dense
          hide-details
          outlined
          placeholder="ค้นหาใบสั่งซื้อ"
        ></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-card outlined class="my-4">
          <v-data-table
            :headers="headers"
            :items="orderList"
            style="width:100%"
            height="100%"
            :search="search"
            :items-per-page="7"
            >
              <!-- <template v-slot:[`item.order_created_date`]="{ item }">
                {{new Date(item.order_created_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' })}}
              </template> -->
              <template v-slot:[`item.transaction_status`]="{ item }">
                <span v-if="item.transaction_status ==='Success' "><v-chip class="ma-2" color="green" text-color="white">ชำระเงินสำเร็จ</v-chip></span>
                <span v-else-if="item.transaction_status ==='Not Paid' "><v-chip class="ma-2" color="red" text-color="white">ยังไม่ชำระเงิน</v-chip></span>
                <span v-else-if="item.transaction_status ==='Pending' "><v-chip class="ma-2" color="orange" text-color="white">รออนุมัติ</v-chip></span>
                <span v-else><v-chip class="ma-2" color="secondary" text-color="white">ยกเลิก</v-chip></span>
              </template>
              <template v-slot:[`item.user_name`]="{ item }">
                {{ item.user_real_name }}
              </template>
              <template v-slot:[`item.net_price`]="{ item }">
                {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              </template>
              <template v-slot:[`item.transaction_number`]="{ item }">
                <a @click="orderDetail(item)">{{item.transaction_number}}</a>
              </template>
              <template v-slot:[`item.seller_sent_status`]="{ item }">
                <v-row class="pt-5">
                  <v-select v-model="item.seller_sent_status" :items="item.transaction_status ==='Not Paid' || item.transaction_status === 'Pending' ? send_items_notPaid : send_items" item-text="text" item-value="value" @change="UpdateStatusSeller(item)" outlined dense :disabled="item.transaction_status ==='Cancel'"></v-select>
                </v-row>
              </template>
              <template v-slot:[`item.buyer_received_status`]="{ item }">
                <span v-if="item.buyer_received_status ==='Received' ">รับของแล้ว</span>
                <span v-else>ยังไม่ได้รับของ</span>
              </template>
              <!-- <template v-slot:[`item.order_number`]="{ item }">
                <a @click="orderDetail(item)">{{item.order_number}}</a>
              </template> -->
              <!-- <template v-slot:[`item.received`]>
                <v-row justify="center" >
                  <v-checkbox v-model="received"></v-checkbox>
                </v-row>
              </template> -->
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <!-- </v-card> -->
  </v-container>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      orderList: [],
      StateStatus: 0,
      // OrderName: [
      //   { key: 0, name: 'ข้อมูลไม่ครบ' },
      //   { key: 1, name: 'ยังไม่ได้ชำระเงิน' },
      //   { key: 2, name: 'ชำระเงินสำเร็จ' },
      //   { key: 3, name: 'ยกเลิก' },
      //   { key: 4, name: 'ชำระเงินไม่สำเร็จ' }
      // ],
      headers: [
        { text: 'วันที่', value: 'order_created_date', align: 'center' },
        // { text: 'รหัสการสั่งซื้อ', value: 'order_number', align: 'center' },
        { text: 'ชื่อผู้ซื้อ', value: 'user_name', align: 'center' },
        // { text: 'ที่อยู่จัดส่ง', value: 'user_address', align: 'center' },
        { text: 'ราคา', value: 'net_price', align: 'center' },
        { text: 'รหัสการสั่งซื้อ', value: 'transaction_number', align: 'center' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center' },
        { text: 'สถานะการจัดส่ง', value: 'seller_sent_status', align: 'center', width: '150px' },
        { text: 'วันที่ส่ง', value: 'sent_date', align: 'center' },
        { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        { text: 'วันที่รับ', value: 'received_date', align: 'center' }
        // { text: 'ส่งแล้ว?', value: 'received', align: 'center' }
      ],
      seller_sent_status: '',
      send_items: [
        { text: 'จัดส่งแล้ว', value: 'sent' },
        { text: 'ยังไม่จัดส่ง', value: 'not_sent' },
        { text: 'ยกเลิก', value: 'cancel' }
      ],
      send_items_notPaid: [
        { text: 'ยังไม่จัดส่ง', value: 'not_sent' },
        { text: 'ยกเลิก', value: 'cancel' }
      ],
      DataTable: [],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    }
  },
  watch: {
    DataTable (val) {
      // console.log('DataTable', val)
    }
    // StateStatus (val) {
    //   console.log('val', val)
    //   if (val === 0) {
    //     this.DataTable = this.orderList.data_incomplete
    //   } else if (val === 1) {
    //     this.DataTable = this.orderList.not_paid
    //   } else if (val === 2) {
    //     this.DataTable = this.orderList.success
    //   } else if (val === 3) {
    //     this.DataTable = this.orderList.cancel
    //   } else if (val === 4) {
    //     this.DataTable = this.orderList.fail
    //   }
    // }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    // await this.$store.dispatch('actionsGetShopData')
    // const shopData = await this.$store.state.ModuleShop.stateShopData.data[0]
    // console.log('shop id', shopData)
    await this.GetSellerShop()
    // await this.$store.dispatch('actionListOrderSeller', shopData)
    // this.orderList = await this.$store.state.ModuleOrder.stateOrderListSeller.data
    // console.log('orderlist Seller', this.orderList)
    // this.ProcurementData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('ProcurementData'))))
    // this.getDataTable()
    // if (this.StateStatus === 0) {
    //   this.DataTable = this.orderList.data_incomplete
    // } else if (this.StateStatus === 1) {
    //   this.DataTable = this.orderList.not_paid
    // } else if (this.StateStatus === 2) {
    //   this.DataTable = this.orderList.success
    // } else if (this.StateStatus === 3) {
    //   this.DataTable = this.orderList.cancel
    // } else if (this.StateStatus === 4) {
    //   this.DataTable = this.orderList.fail
    // }
  },
  methods: {
    async UpdateStatusSeller (val) {
      const update = {
        order_number: val.order_number,
        seller_sent_status: val.seller_sent_status
      }
      // console.log('update', update)
      await this.$store.dispatch('actionUpdateStatusSeller', update)
      this.$swal.fire({
        icon: 'success',
        title: 'บันทึกการส่งสินค้าสำเร็จ',
        showConfirmButton: false,
        timer: 1500
      })
      this.GetSellerShop()
    },
    async orderDetail (val) {
      var data = {
        transaction_number: val.transaction_number
      }
      // console.log('data naja', data)
      await this.$store.dispatch('actionOrderDetailSeller', data)
      // await this.$store.state.ModuleOrder.stateOrderDetailSeller.data
      this.$router.push({ path: '/quotationSeller' }).catch(() => {})
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('order detail na', response)
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      //   console.log('กี่ร้านนะะะะะะะะะ', response.data[0].order_number[0])
      // }
    },
    async GetSellerShop () {
      await this.$store.dispatch('actionsGetShopData')
      const shopData = await this.$store.state.ModuleShop.stateShopData.data[0]
      // console.log('shop id', shopData)
      await this.$store.dispatch('actionListOrderSeller', shopData)
      this.orderList = await this.$store.state.ModuleOrder.stateOrderListSeller.data
      // console.log('orderlist Seller', this.orderList)
    },
    SelectDetailOrder (item) {
      this.StateStatus = item
    },
    async getDataTable () {
      this.overlay = true
      const data = {
        procurement_org_id: this.ProcurementData.procurement_org_id
      }
      await this.$store.dispatch('actionListOrderProcurement', data)
      var response = await this.$store.state.ModuleCart.stateListOrderProcurement
      // console.log('response List Order Procurement', response)
      if (response.result === 'SUCCESS') {
        this.responseData = response.data
        this.overlay = false
      }
    },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateDetailOrder
      // console.log('response detail order', response)
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem('MyOrderDetail', Encode.encode(JSON.stringify(response.data)))
        this.$router.push('/myorderdetail')
      }
    }
  }
}
</script>
