<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">สร้าง Order JV ERP</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>สร้าง Order JV ERP</v-card-title>
      <v-card-text>
        <v-row dense style="justify-content: center;">
          <v-card class="my-6" elevation="0" style="background: #EBEBEB; border: 1px solid #EBEBEB; border-radius: 8px;" width="416" height="100%">
            <v-card-text>
              <v-row dense style="justify-content: center;">
                <v-col cols="12" align="center">
                  <v-form ref="formJVERP" :lazy-validation="lazy">
                    <v-col cols="12" align="start" class="mt-4">
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Order Number: <span style="color: red;">*</span></span>
                      <v-text-field v-model="ordernumber" class="input_text" placeholder="ระบุ Order Number" outlined dense :rules="Rules.ordernumber"></v-text-field>
                    </v-col>
                    <v-col cols="12" align="start">
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Token <span style="color: red;">*</span></span>
                      <v-text-field v-model="Token" class="input_text" placeholder="ระบุ Token" outlined dense :rules="Rules.Token"></v-text-field>
                    </v-col>
                  </v-form>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions class="mb-4">
              <v-row justify="end" dense>
                <v-btn color="#27AB9C" outlined class="px-7 mr-2" @click="cancel()">ยกเลิก</v-btn>
                <v-btn color="#27AB9C" class="white--text px-8 mr-2" @click="confirm()">ยืนยัน</v-btn>
              </v-row>
            </v-card-actions>
          </v-card>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      Token: '',
      ordernumber: '',
      Rules: {
        ordernumber: [
          v => !!v || 'กรุณากรอก Order Number'
        ],
        Token: [
          v => !!v || 'กรุณากรอก Token'
        ]
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/CreateOrderJVERPMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'CreateOrderJVERP')
        this.$router.push({ path: '/CreateOrderJVERP' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      // this.getOrderList()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    async confirm () {
      this.$store.commit('openLoader')
      if (this.$refs.formJVERP.validate(true)) {
        var token = this.Token
        var data = {
          order_number: this.ordernumber
        }
        // console.log(token)
        await this.$store.dispatch('actionsApproveQuotation', { access: data, token })
        var res = await this.$store.state.ModuleAdminManage.stateApproveQuotation
        if (res.code === 200) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: res.message
          })
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            text: res.message
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
      }
    },
    cancel () {
      this.resetData()
    },
    resetData () {
      this.Token = ''
      this.ordernumber = ''
      this.$refs.formJVERP.reset()
      this.$refs.formJVERP.resetValidation()
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    }
  }
}
</script>

<style>
  .input_text {
    height: 60px;
    opacity: 1 !important;
  }
  #txtPassword{
    -webkit-text-security:disc;
  }
</style>
<style scoped>
::v-deep .v-select.v-input--dense .v-select__selection--comma {
  padding: 2px;
}
::v-deep .v-list-item__title {
  padding: 6px;
}
</style>
