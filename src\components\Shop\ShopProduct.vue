<template>
  <v-container v-if="checkShop !== 'ไม่มีร้านค้า'">
    <a-card>
      <a-row>
        <a-col :span='24' class="mb-2">
          <a-row type="flex" justify="start">
            <v-icon x-large class="mb-2">mdi-cart-outline</v-icon><span class="pl-5 pt-1 headline">สินค้าที่ฉันขาย ( {{Shopname}} )</span>
          </a-row>
        </a-col>
        <a-col :span='24'>
          <!-- <a-tabs>
            <a-tab-pane v-for="item in OrderName" :key="item.key" :tab="item.name">
              <a-row type="flex">
                <a-col :span="24"> -->
                  <TableProduct :props='DataTable' :ShopID="seller_shop_id" />
                <!-- </a-col>
              </a-row>
            </a-tab-pane>
          </a-tabs> -->
        </a-col>
      </a-row>
    </a-card>
  </v-container>
</template>
<script>
export default {
  components: {
    TableProduct: () => import('@/components/Shop/ShopProduct/TableProduct')
  },
  data () {
    return {
      OrderName: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 1, name: 'พร้อมขาย' },
        { key: 2, name: 'หมด' }
      ],
      SelectDataTable: [],
      DataTable: [],
      Shopname: '',
      seller_shop_id: '',
      checkShop: 'ไม่มีร้านค้า'
    }
  },
  created () {
    this.CheckShop()
  },
  methods: {
    async CheckShop () {
      this.$EventBus.$emit('changeNav')
      await this.$store.dispatch('actionsGetShopData')
      var response = await this.$store.state.ModuleShop.stateShopData
      // console.log('response shop data====>', response.data)
      this.SetDataShop(response.data[0])
    },
    async SetDataShop (val) {
      // console.log('Val ===', Object.keys(val).length)
      if (Object.keys(val).length !== 0) {
        this.Shopname = val.name_th
        this.seller_shop_id = val.seller_shop_id
        var data = {
          seller_shop_id: this.seller_shop_id
        }
        await this.$store.dispatch('GetProductBySellerID', data)
        this.DataTable = this.$store.state.ModuleManageShop.ProductBySellerID
        // console.log('DataTable', this.DataTable)
        this.checkShop = 'มีร้านค้า'
      }
    }
  }
}
</script>
