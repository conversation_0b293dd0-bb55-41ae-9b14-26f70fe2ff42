<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รับสินค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> รับสินค้า
      </v-card-title>

      <v-col cols="12">
        <v-row>
          <v-col v-if="!MobileSize && !IpadSize" cols="12" class="d-flex flex-row" style="align-items: center;">
            <v-text-field v-model="search" @keyup="searchData(search)" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
            <span class="pl-3" style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              สถานะรายการ :
            </span>
            <v-col cols="3">
              <v-select
                  class="setCustomSelect"
                  v-model="stateOrderStatusDelivery"
                  :items="['ทั้งหมด','รอจัดส่งสินค้า', 'กำลังจัดส่งสินค้า', 'จัดส่งสินค้าสำเร็จ']"
                  placeholder="ทั้งหมด"
                  @change="selectOrderStatusDelivery()"
                  append-icon="mdi-chevron-down"
                  dense
                  outlined
                  hide-details
                  style="border-radius: 8px; font-size: 14px;"
                />
            </v-col>
          </v-col>
          <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
            <v-text-field v-model="searchDelivery" @keyup="searchData(searchDelivery)" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              สถานะรายการ :
            </span>
            <v-col class="pa-2">
              <v-select
                  class="setCustomSelect"
                  v-model="stateOrderStatusDelivery"
                  :items="['ทั้งหมด','รอจัดส่งสินค้า', 'กำลังจัดส่งสินค้า', 'จัดส่งสินค้าสำเร็จ']"
                  placeholder="ทั้งหมด"
                  @change="selectOrderStatusDelivery()"
                  append-icon="mdi-chevron-down"
                  dense
                  outlined
                  hide-details
                  style="border-radius: 8px; font-size: 14px;"
                />
            </v-col>
          </v-col>
        </v-row>
      </v-col>

      <v-col cols="12">
        <span style="font-size: 16px;">รายการสั่งซื้อสินค้าทั้งหมด {{ totalDelivery }} รายการ</span>
        <br>
        <br>
        <v-data-table
          :headers="headersDelivery"
          :items="delivery"
          :page.sync="page"
          :footer-props="{'items-per-page-text':'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
          :items-per-page="options.itemsPerPage"
          :server-items-length="Number(totalDelivery)"
          :options.sync="options"
          @update:options="updateOptionsDelivery"
          class="elevation-1"
          :class="!IpadSize && !MobileSize && !IpadProSize ? 'table-delivery' : ''"
        >

          <template v-slot:[`item.actions`]="{ item }">
            <v-row>
              <v-btn text rounded color="#27AB9C" small @click="gotoDeliveryDetail(item)">
                <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                <b>รายละเอียด</b><v-icon small>mdi-chevron-right</v-icon>
              </v-btn>
              <v-btn text rounded color="#27AB9C" small @click="openDialogWaitingConfirm(item)" v-if="item.status === 'inprogress'">
                <v-icon class="pr-1" color="27AB9C" small>mdi-package-variant-closed</v-icon>
                <b>ได้รับสินค้าแล้ว</b><v-icon small>mdi-chevron-right</v-icon>
              </v-btn>
            </v-row>
          </template>

          <template v-slot:[`item.status`]="{ item }">
            <div>
              <v-chip v-if="item.status === 'waiting'" text-color="#FAAD14" color="#FFF2E0">รอจัดส่งสินค้า</v-chip>
              <v-chip v-if="item.status === 'inprogress'" text-color="#3178BB" color="#EAF4FF">กำลังจัดส่งสินค้า</v-chip>
              <v-chip v-if="item.status === 'success'" text-color="#31BB31" color="#EAFFF4">จัดส่งสินค้าสำเร็จ</v-chip>
            </div>
          </template>

          <template v-slot:[`item.end_shipping`]="{ item }">
            <span v-if="item.end_shipping === 'Invalid Date'">-</span>
            <span v-else>{{ item.end_shipping }}</span>
          </template>

          <template v-slot:[`item.delivery_number`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                  style="cursor: pointer; text-decoration: underline; color: #27AB9C;"
                  @click="gotoDeliveryNote(item.pdf_path)"
                >
                  {{ item.delivery_number }}
                </span>
              </template>
              <span>รายละเอียดใบส่งสินค้า</span>
            </v-tooltip>
          </template>
        </v-data-table>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogConfirm" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #FDFAF9; padding: 50px;">
          <div>
            <v-img
              src="@/assets/shopDelivery/confirm.png"
              contain
              max-width="200"
            ></v-img>
          </div>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยอมรับสินค้า</b></span><br><br>
            <span style="font-size: 16px;">คุณได้รับสินค้าเรียบร้อยแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" color="#27AB9C" style="border-color: #27AB9C; color: #F2F2F2; width: 100px; font-size: 16px;" @click="dialogConfirm = false">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogWaitingConfirm" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogWaitingConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #FDFAF9; padding: 20px;">
          <div>
            <v-img
              src="@/assets/shopDelivery/info.png"
              contain
              max-width="200"
            ></v-img>
          </div>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยืนยันการรับสินค้า</b></span><br><br>
            <span style="font-size: 16px;">คุณได้รับสินค้าเรียบร้อยแล้วใช่หรือไม่</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogWaitingConfirm = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="SuccessDelivery()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dialogConfirm: false,
      dialogWaitingConfirm: false,
      activeTab: 'order',
      search: '',
      page: 1,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      stateOrderStatusDelivery: '',
      totalDelivery: '',
      totalPagesDelivery: '',
      delivery: [],
      headersDelivery: [
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'รหัสใบส่งสินค้า', value: 'delivery_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        // { text: 'ใบส่งสินค้า', value: 'pdf_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'จำนวนสินค้า', value: 'shipping_amount', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'วันที่จัดส่งสินค้า', value: 'start_date', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'วันที่รับสินค้า', value: 'end_shipping', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'สถานะรายการ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '300' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ReceiveItemsMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ReceiveItems' }).catch(() => { })
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    window.scrollTo(0, 0)
    if (
      localStorage.getItem('oneData') !== null &&
      localStorage.getItem('CompanyData') !== null
    ) {
      this.companyData = JSON.parse(
        Decode.decode(localStorage.getItem('CompanyData'))
      )
      await this.OrderDeliveryCompany(this.searchDelivery)
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    gotoDeliveryNote (item) {
      console.log(item)
      if (item === '-') {
        this.$swal.fire({
          icon: 'info',
          text: 'ไม่มีรายการใบส่งสินค้า',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      } else {
        window.open(item)
      }
    },
    selectOrderStatusDelivery () {
      if (this.stateOrderStatusDelivery === 'ทั้งหมด' || this.stateOrderStatusDelivery === '') {
        this.orderStatusDelivery = 'all'
      } else if (this.stateOrderStatusDelivery === 'รอจัดส่งสินค้า') {
        this.orderStatusDelivery = 'waiting'
      } else if (this.stateOrderStatusDelivery === 'กำลังจัดส่งสินค้า') {
        this.orderStatusDelivery = 'inprogress'
      } else if (this.stateOrderStatusDelivery === 'จัดส่งสินค้าสำเร็จ') {
        this.orderStatusDelivery = 'Success'
      }
      this.OrderDeliveryCompany(this.searchDelivery)
    },
    searchData (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.OrderDeliveryCompany(val)
      }, 500)
    },
    updateOptionsDelivery (options) {
      this.options = options
      this.page = options.page
      this.OrderDeliveryCompany(this.searchDelivery)
    },
    async OrderDeliveryCompany (textSearch) {
      this.orders = []
      this.totalOrders = ''
      this.totalPages = ''
      this.$store.commit('openLoader')
      var data = {
        company_id: this.companyData.id,
        delivery_status: this.orderStatusDelivery === 'all' ? null : this.orderStatusDelivery,
        limit: this.options.itemsPerPage,
        page: this.page,
        search: textSearch
      }
      await this.$store.dispatch('actionsOrderDeliveryCompany', data)
      var responseData = await this.$store.state.ModuleAdminManage.stateOrderDeliveryCompany
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.delivery = await responseData.data
        this.totalDelivery = await responseData.pagination.totalOrders
        this.totalPagesDelivery = await responseData.pagination.totalPages
        // console.log('delivery', this.delivery)
      }
    },
    openDialogWaitingConfirm (item) {
      this.dialogWaitingConfirm = true
      this.deliveryNumber = item.delivery_number
    },
    async SuccessDelivery () {
      this.$store.commit('openLoader')
      var data = {
        order_delivery_number: this.deliveryNumber
      }
      await this.$store.dispatch('actionsConfirmOrderDeliveryCompany', data)
      var responseData = await this.$store.state.ModuleAdminManage.stateConfirmOrderDeliveryCompany
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogWaitingConfirm = false
        this.dialogConfirm = true
        await this.OrderDeliveryCompany(this.searchDelivery)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    gotoDeliveryDetail (item) {
      this.orderDeliveryNumber = item.delivery_number
      if (this.MobileSize) {
        this.$router.push({ path: `/ReceiveItemsDetailsMobile?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ReceiveItemsDetails?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>

</style>

<style lang="scss" scoped>
  ::v-deep .table-delivery table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 300px;
          z-index: 10;
          background: white;
        }
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 300px;
        }
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
