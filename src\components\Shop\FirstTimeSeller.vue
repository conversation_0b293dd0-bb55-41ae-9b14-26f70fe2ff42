<template>
  <v-container v-if="checkShop !== 'ไม่มีร้านค้า'">
    <v-row dense class="mb-16 mt-2">
      <v-col cols="12" md="1" sm="1" xs="1">
        <v-avatar
         tile
         :width="IpadProSize ? '100px' : IpadSize ? '70px' : '100px'"
         :height="IpadProSize ? '100px' : IpadSize ? '70px' : '100px'"
        >
          <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain></v-img>
        </v-avatar>
      </v-col>
      <v-col cols="12" md="3" sm="10" xs="6" class="pl-7" :class="[IpadProSize ? '' : IpadSize ? 'ml-4' : '']">
        <v-card outlined :height="IpadProSize ? '100px' : IpadSize ? '70px' : '100px'" width="100%">
          <v-col cols="12" md="12" sm="12" :class="[IpadProSize ? '' : IpadSize ? 'pb-0 pt-0' : '']">
            <span style="line-height: 24px; font-weight: bold; color: #333333;" :style="{'font-size': IpadProSize ? '16x' : IpadSize ? '14px' : '16px'}">{{ Shopname }}</span>
          </v-col>
          <v-row>
            <v-col cols="12" md="5" sm="5" xs="4">
              <v-row dense no-gutters>
                <v-col cols="12" md="5" sm="3" xs="3">
                  <v-avatar
                    tile
                    :width="IpadProSize ? '32px' : IpadSize ? '20px' : '32px'"
                    :height="IpadProSize ? '32px' : IpadSize ? '20px' : '32px'"
                  >
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/shop.png" contain></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="12" md="7" sm="4" class="pl-4">
                  <span style="color: #000000; font-size: 12px; font-weight: 600; line-height: 16px;">{{DataTable.total_product_count}}</span><br/>
                  <span style="color: #000000; font-size: 10px; font-weight: 400; line-height: 14px;">สินค้า</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="1" sm="1" xs="1" class="pl-0 pr-0">
              <v-divider vertical style="border: 1px solid #F2F2F2;"></v-divider>
            </v-col>
            <v-col cols="12" md="5" sm="5" xs="4" class="pl-0">
              <v-row dense no-gutters>
                <v-col cols="12" md="5" sm="3" class="pl-0">
                  <v-avatar
                    tile
                    :width="IpadProSize ? '32px' : IpadSize ? '20px' : '32px'"
                    :height="IpadProSize ? '32px' : IpadSize ? '20px' : '32px'"
                  >
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/Group.png" contain></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="12" md="7" sm="4" class="pl-2">
                  <span style="color: #000000; font-size: 12px; font-weight: 600; line-height: 16px;">0</span><br/>
                  <span style="color: #000000; font-size: 10px; font-weight: 400; line-height: 14px;">ผู้ติดตาม</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <v-col cols="12" md="8" sm="12" xs="12">
        <v-row justify="center" class="mt-12" :class="[IpadProSize ? 'pr-10' : IpadSize ? '' : 'pr-10']">
          <v-img src="@/assets/ImageINET-Marketplace/ICONShop/FirstTime.png" :width="IpadProSize ? '740' : IpadSize ? '400' : '740'" :height="IpadProSize ? '100%' : IpadSize ? '400' : '100%'" contain></v-img>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      OrderName: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 1, name: 'พร้อมขาย' },
        { key: 2, name: 'หมด' }
      ],
      SelectDataTable: [],
      DataTable: [],
      Shopname: '',
      seller_shop_id: '',
      checkShop: 'ไม่มีร้านค้า'
    }
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        // console.log('123456789')
        this.$router.push({ path: '/' }).catch(() => {})
      }
    })
    this.CheckShop()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/sellersMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/sellers' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async CheckShop () {
      this.$EventBus.$emit('changeNav')
      this.$EventBus.$emit('ChangeActiveMenu', false)
      await this.$store.dispatch('actionsGetShopData')
      var response = await this.$store.state.ModuleShop.stateShopData
      // console.log('response shop data====>', response.data)
      await this.SetDataShop(response.data[0])
    },
    async SetDataShop (val) {
      // console.log('Val ===', Object.keys(val).length)
      if (Object.keys(val).length !== 0) {
        this.Shopname = val.name_th
        this.seller_shop_id = val.seller_shop_id
        var data = {
          seller_shop_id: this.seller_shop_id
        }
        await this.$store.dispatch('GetProductBySellerID', data)
        this.DataTable = this.$store.state.ModuleManageShop.ProductBySellerID.data
        // console.log('this.data', this.DataTable)
        if (this.DataTable.total_product_count !== 0) {
          this.checkShop = 'มีร้านค้า'
          this.$router.push({ path: '/seller' }).catch(() => {})
        } else {
          this.checkShop = 'มีร้านค้า'
          this.$router.push({ path: '/seller' }).catch(() => {})
        }
      }
    }
  }
}
</script>
