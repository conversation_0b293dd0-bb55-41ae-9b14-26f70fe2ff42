<template>
  <v-dialog v-model="$store.state.ModuleMyCouponsPoints.stateCouponModel" width="732px" persistent scrollable>
    <v-card max-height="690" id="style-15">
      <v-toolbar flat color="#BDE7D9" dense>
        <v-row>
          <v-col class="d-flex justify-space-around">
            <v-toolbar-title><span style="color: #27AB9C;"><b>ส่วนลด</b></span></v-toolbar-title>
          </v-col>
        </v-row>
        <v-btn fab small
          @click="$store.state.ModuleMyCouponsPoints.stateCouponModel = !$store.state.ModuleMyCouponsPoints.stateCouponModel"
          icon>
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-tabs v-model="tab">
          <v-tab>คูปองส่วนลด</v-tab>
          <v-tab>ใช้คะแนนส่วนลด</v-tab>
        </v-tabs>
        <v-tabs-items v-model="tab">
          <v-tab-item>
            <!-- <v-container grid-list-xs> -->
            <!-- <v-card-title> -->
            <v-row no-gutters>
              <v-col cols="12" md="12" class="mt-4">
                <v-text-field dense solo v-model="searchCoupon" @keyup="SearchCoupon"  label="" append-icon="mdi-magnify"></v-text-field>
              </v-col>
            </v-row>
            <table>
              <tbody>
                <div v-if="couponFilter.length !== 0">
                  <tr v-for="(item, i) in couponFilter" :key="i" class="ma-0 pa-0">
                    <td>
                      <Copons :items="item" :keep="false" colorCard="green" class="mb-8" />
                    </td>
                    <td class="" style="vertical-align: top; padding: 0em 8em;">
                      <v-btn class="ma-2" outlined color="primary" @click="selectCoupon(item)" width="120">
                        ใช้คูปอง
                      </v-btn><br /><br />
                      <!-- <span>ได้รับส่วนลด<span class="mx-2">{{item.real_discount}}</span>บาท</span> -->
                      <span class="ml-4" style="color: #636363; font-size: 10px; font-weight: 600;">ส่วนลดสูงสุด<span class="mx-1">{{
                      item.real_discount }}</span>บาท</span><br>
                      <span class="ml-4" style="color: #636363; font-size: 10px; font-weight: 600;">ได้รับส่วนลด<span class="mx-1">{{
                      item.discount }}</span> บาท</span>
                    </td>
                  </tr>
                </div>
                <div v-else-if="CouponsIteam.length !== 0">
                  <tr v-for="(item, i) in CouponsIteam" :key="i" class="ma-0 pa-0">
                    <td>
                      <Copons :items="item" :keep="false" colorCard="green" class="mb-8" />
                    </td>
                    <td class="" style="vertical-align: top; padding: 0em 8em;">
                      <v-btn class="ma-2" outlined color="primary" width="120" @click="selectCoupon(item)">
                        ใช้คูปอง
                      </v-btn><br /><br />
                      <!-- <span>ได้รับส่วนลด<span class="mx-2">{{item.real_discount}}</span>บาท</span> -->
                      <span class="ml-4" style="color: #636363; font-size: 10px; font-weight: 600;">ส่วนลดสูงสุด<span class="mx-1">{{
                      item.real_discount }}</span>บาท</span><br>
                      <span class="ml-4" style="color: #636363; font-size: 10px; font-weight: 600;">ได้รับส่วนลด<span class="mx-1">{{
                      item.discount }}</span> บาท</span>
                    </td>
                  </tr>
                </div>
                <div v-else>
                  <v-container>
                    <v-row justify="center" class="mx-2 ">
                      <v-col cols="12">
                        <v-row justify="center" class="my-5">
                          <v-img :src="require('@/assets/No-Favorite.png')" max-height="421" max-width="545"
                            height="100%" width="100%" contain></v-img>
                        </v-row>
                      </v-col>
                      <v-col cols="12">
                        <v-row justify="center" class="my-5">
                          <span
                            style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C; font-size: 24px;"><b>คุณยังไม่มีรายการคูปอง</b></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-container>
                </div>
              </tbody>
            </table>
            <!-- </v-card-title> -->
            <!-- </v-container> -->
          </v-tab-item>
          <v-tab-item>
            <v-container grid-list-xs>
              <v-card-title>
                <v-row>
                  <v-col cols="12" md="4" sm="2" class="pl-0">
                    <span style="font-weight: 700; font-size: 16px; line-height: 24px;">คะแนนของฉัน</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="9">
                    <span style="color: #FAAD14; font-size: 18px; font-weight: 600; line-height: 26px;">{{(typeof points.amount !==
                    'object' && points.amount !== null) ? points.amount : '0'}} คะแนน</span>
                  </v-col>
                </v-row>
              </v-card-title>
              <v-row>
                <v-col cols="10" md="8">
                  <v-text-field v-model="points.maxUse" label="" readonly outlined dense></v-text-field>
                </v-col>
                <v-col cols="2" md="4">
                  <v-btn v-if="(typeof points.amount !== 'object' && points.amount !== null)" color="#27AB9C" dark
                    @click="selectPoint(points)">
                    ใช้คะแนน
                  </v-btn>
                  <v-btn v-else color="#9A9A9A" dark>
                    ใช้คะแนน
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-tab-item>
        </v-tabs-items>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
// import { Decode } from '@/services'
export default {
  components: {
    Copons: () => import('@/components/CardCoupon/CardCoupon')
  },
  data () {
    return {
      itemsListUser: [],
      DialogDataUser: false,
      tab: null,
      searchCoupon: '',
      CouponsIteam: [],
      SearchCode: false
    }
  },
  watch: {
  },
  created () {
    this.$EventBus.$on('dialogDataUser', this.dialogDataUser)
  },
  destroyed () {
    this.$EventBus.$off('dialogDataUser')
  },
  computed: {
    couponFilter () {
      return this.$store.state.ModuleMyCouponsPoints.getCouponQu.filter(x => {
        return x.name.toLowerCase().includes(this.searchCoupon.toLowerCase())
      })
    },
    points () {
      return this.$store.state.ModuleMyCouponsPoints.getPointQu
    }
  },
  mounted () {
  },
  methods: {
    toFixData (data) {
      return data.toFixed(2)
    },
    async dialogDataUser (data) {
      // console.log('dialogDataUser', data)
      this.searchCoupon = ''
      this.DialogDataUser = await !this.DialogDataUser
      this.itemsListUser = await data
    },
    async selectCoupon (el) {
      var data = this.$store.state.ModuleMyCouponsPoints.stateData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (this.SearchCode === true) {
        var dataNGS = {
          coupons_code: el.couponCode,
          seller_shop_id: data.seller_shop_id,
          role_user: dataRole.role,
          company_id: data.company_id,
          net_price: data.net_price
        }
        await this.$store.dispatch('actionsUseCodeFromNGS', dataNGS)
      }
      this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon = await []
      this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point = await []
      await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon.push(el)
      this.$store.state.ModuleMyCouponsPoints.stateCouponModel = await false
    },
    async selectPoint (el) {
      this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon = await []
      this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point = await []
      await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point.push(el)
      this.$store.state.ModuleMyCouponsPoints.stateCouponModel = await false
    },
    async SearchCoupon () {
      this.CouponsIteam = []
      if (this.searchCoupon !== '') {
        // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var data = this.$store.state.ModuleMyCouponsPoints.stateData
        var dataSearch = {
          seller_shop_id: data.seller_shop_id,
          role_user: dataRole.role,
          company_id: data.company_id,
          key: this.searchCoupon,
          type: 'qu',
          net_price: data.net_price
        }
        await this.$store.dispatch('actionsSearchCoupon', dataSearch)
        var response = await this.$store.state.ModuleMyCouponsPoints.stateSearchCoupon
        this.CouponsIteam = []
        if (response.code === 200) {
          for (let i = 0; i < response.data.length; i++) {
            this.CouponsIteam.push({
              image: response.data[i].couponImagePath,
              name: response.data[i].couponName,
              description: response.data[i].couponDescription,
              couponDate: {
                useStartDate: response.data[i].useStartDate,
                useEndDate: response.data[i].useStartDate
              },
              shop_name: response.data[i].shop_name,
              couponCode: response.data[i].couponCode,
              couponId: response.data[i].couponId,
              status: response.data[i].couponType,
              discount: response.data[i].discount,
              real_discount: response.data[i].real_discount
            })
          }
        }
        if (this.CouponsIteam.length === 0) {
          this.SearchCode = false
        } else {
          this.SearchCode = true
        }
      } else {
        this.SearchCode = false
      }
    }
  }
}
</script>
<style scoped>
.inner-right {
    height: auto;
    overflow-y: scroll;
}

#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
