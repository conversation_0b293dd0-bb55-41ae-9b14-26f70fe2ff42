<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายชื่อร้่านค้าของระบบการซื้อออนไลน์</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายชื่อร้่านค้าของระบบการซื้อออนไลน์</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจาก....." outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col col="12" md="6" align="start">
                <span style="font-size: 18px; line-height: 24px; color: #333333; font-weight: 600">รายการชื่อร้่านค้าของระบบการซื้อออนไลน์ทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
              <v-col col="12" md="6" align="right" class="pt-0">
                <v-btn class="mt-2 white--text" color="#27AB9C"  @click="openModalAddSeller()"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="12">
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="dataList"
                :search="search"
                no-results-text="ไม่พบรายชื่อร้านค้า"
                no-data-text="ไม่พบรายชื่อร้านค้า"
                :items-per-page="10"

                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                style="width:100%;"
                height="100%"
                class="elevation-1 mt-4"
                >
                <template v-slot:[`item.index`]="{ item }">
                  <span>{{dataList.indexOf(item) + 1}}</span>
                </template>
                <template v-slot:[`item.owner_name`]="{ item }">
                  <span v-if="item.owner_name === ' '">-</span>
                  <span v-else>{{item.owner_name}}</span>
                </template>
                <template v-slot:[`item.actions`]="{ item }">
                  <v-row>
                    <v-col>
                      <v-btn @click="openDetailModal(item)" icon rounded color="#27AB9C" :class="!MobileSize? 'px-2' : 'pa-0 ma-0'" small>
                        <v-icon small>mdi-file-document-multiple-outline</v-icon>
                        <span>รายละเอียด</span>
                      </v-btn>
                    </v-col>
                    <v-col>
                      <v-btn @click="openConfirmDelete(item)" icon rounded color="#27AB9C" :class="!MobileSize? 'px-2' : 'pa-0 ma-0'" small>
                        <v-icon small>mdi-delete</v-icon>
                        <span>ลบ</span>
                      </v-btn>
                    </v-col>
                  </v-row>
                </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>

      <v-dialog v-model="ModalAddSeller" width="800px" persistent>
          <v-card width="100%" min-height="450px" height="100%" class="rounded-lg pb-4" style="overflow-x: hidden;">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px">
                <font color="#27AB9C" >รายชื่อร้านค้า</font>
              </span>
              <v-btn icon dark @click="ModalAddSeller = false">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text class="mt-4">

              <v-row justify="center" align-content="center" :class="!MobileSize ? 'px-6' : ''" v-if="clickSelectSeller === false">
                <v-col cols="12">
                  <v-text-field class="rounded-lg" v-model="searchSeller" placeholder="ค้นหาร้านค้า" outlined dense hide-details
                  @keydown.enter.prevent="submit">
                    <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                  </v-text-field>
                </v-col>
                <v-col cols="12" v-if="filteredList.length !== 0" style="max-height: 250px;" :style="{'overflow-y': filteredList.length === 0 ? 'hidden' : 'scroll'}">
                  <p>รายชื่อร้านค้าทั้งหมด {{filteredList.length}} รายการ</p>
                  <v-card outlined :class="!MobileSize ? 'pa-4' : 'pa-2'">
                    <v-row>
                      <v-col cols="6" v-for="(item, index) in filteredList" :key="index">
                        <v-card outlined class="mb-0" @click="selectSellerShop(item)">
                          <v-card-text class="pa-0">
                            <div class="d-flex flex-no-wrap">
                              <v-avatar :class="!MobileSize ? 'ma-2' : ''" :size="!MobileSize? '80' : '30'" tile>
                                <v-img :src="item.shop_profile" contain style="border-radius: 8px;" v-if="item.shop_profile !== ''"></v-img>
                                <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                              </v-avatar>
                              <div :class="!MobileSize ? 'ma-2' : 'pl-2'">
                                <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">ชื่อร้าน : <b>{{ item.shop_name !== null ? item.shop_name.length > 18 ? item.shop_name.substring(0, (18 - 4)) + '...' : item.shop_name : '-' }}</b></p>
                                <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">ชื่อเจ้าของร้าน : <b>{{ item.user_name !== ' ' ? item.user_name.length > 18 ? item.user_name.substring(0, (18 - 4)) + '...' : item.user_name : '-' }}</b></p>
                              </div>
                            </div>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
                <v-col cols="12" class="px-12" v-if="filteredList.length === 0 && searchSeller !== ''" align="center">
                  <div style="padding-top: 30px;">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" max-height="146px" max-width="135px" height="100%" width="100%" contain></v-img>
                  </div>
                  <h2 style="padding-top: 20px; padding-bottom: 50px; color: #333333;">
                    <span :class="MobileSize ? 'fontSizeCardTitleMobile' : 'fontSizeCardTitle'">ไม่พบรายชื่อร้านค้า</span><br/>
                  </h2>
                </v-col>
              </v-row>

              <v-row justify="center" align-content="center" :class="!MobileSize ? 'px-6' : ''" v-if="clickSelectSeller === true">
                <v-col cols="12" v-if="selectedSeller.length !== 0" style="max-height: 250px;">
                  <p>รายละเอียดร้านค้าที่เลือก</p>
                  <v-card width="100%" outlined :class="!MobileSize ? 'pa-4' : 'pa-2'">
                    <v-card outlined class="mb-0">
                      <v-card-text class="pa-0">
                        <div class="d-flex flex-no-wrap">
                          <v-avatar :class="!MobileSize ? 'ma-2' : ''" :size="!MobileSize? '120' : '60'" tile>
                            <v-img :src="selectedSeller.shop_profile" contain style="border-radius: 8px;" v-if="selectedSeller.shop_profile !== ''"></v-img>
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                          </v-avatar>
                          <div :class="!MobileSize ? 'ma-2 mt-4' : 'pl-2'">
                            <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">ชื่อร้าน : <b>{{ selectedSeller.shop_name}}</b></p>
                            <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">ชื่อเจ้าของร้าน : <b>{{ selectedSeller.user_name}}</b></p>
                            <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">คำอธิบาย : <b>{{ selectedSeller.shop_description }}</b></p>
                          </div>
                        </div>
                      </v-card-text>
                    </v-card>
                  </v-card>
                  <v-row dense no-gutters justify="end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-4" @click="showListAgain()">ย้อนกลับ</v-btn>
                    <v-btn dense color="#27AB9C" class="ml-4 mt-4 pl-8 pr-8 white--text" @click="dialogConfirm = true">ตกลง</v-btn>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
      </v-dialog>

      <v-dialog v-model="ModalDetailSeller" width="800px" persistent>
        <v-card width="100%" min-height="450px" height="100%" class="rounded-lg pb-4" style="overflow-x: hidden;">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C" >รายละเอียดร้านค้า</font>
            </span>
            <v-btn icon dark @click="ModalDetailSeller = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text class="mt-4">
            <v-row>
              <v-col cols="12">
                <v-card-text class="pa-0">
                  <div class="d-flex flex-no-wrap">
                    <v-avatar :class="!MobileSize ? 'ma-2' : ''" :size="!MobileSize? '80' : '30'" tile>
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;"></v-img>
                    </v-avatar>
                    <div :class="!MobileSize ? 'ma-2 mt-10' : 'pl-2'">
                      <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''" style="font-size:20px">ชื่อร้าน : <b>{{ selectedSeller.shop_name !== null ? selectedSeller.shop_name : '-' }}</b></p>
                    </div>
                  </div>
                </v-card-text>
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" :class="!MobileSize ? 'px-6' : ''" v-if="selectedSeller !== []">
              <v-col cols="12" style="max-height: 250px;">
                <p>รายชื่อพนักงานทั้งหมด {{emp_count}} รายการ</p>
                <v-card outlined :class="!MobileSize ? 'pa-4' : 'pa-2'">
                  <v-row>
                    <v-col cols="12" v-for="(item, index) in selectedSeller.employee" :key="index">
                      <v-card outlined class="mb-0">
                        <v-card-text class="pa-0">
                          <div class="d-flex flex-no-wrap">
                            <v-avatar :class="!MobileSize ? 'ma-2' : ''" :size="!MobileSize? '40' : '10'" tile>
                              <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;"></v-img>
                            </v-avatar>
                            <div :class="!MobileSize ? 'ma-2' : 'pl-2'">
                              <p :class="MobileSize ? 'fontSizeCardTitleMobile' : 'mb-0'">ชื่อพนักงาน : <b>{{ item.emp_name !== null ? item.emp_name : '-' }}</b></p>
                              <p :class="MobileSize ? 'fontSizeCardTitleMobile' : 'mb-0'">ตำแหน่ง :
                                <b v-if="item.position === 'sale'">ฝ่ายขาย</b>
                                <b v-if="item.position === 'admin'">แอดมิน</b>
                                <b v-else>ซูเปอร์แอดมิน</b>
                              </p>
                            </div>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-dialog>

      <!-- dialog comfirm Add -->
      <v-dialog max-width="464px" v-model="dialogConfirm" persistent>
        <v-card max-height="300px" align="center">
          <v-toolbar color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px" v-if="status !== 'delete'"><font color="#27AB9C">เพิ่มร้านค้า</font></span>
            <span class="flex text-center ml-5" style="font-size:20px" v-else><font color="#27AB9C">ลบร้านค้า</font></span>
            <v-btn icon dark @click="dialogConfirm = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <br/>
          <v-card-text class="mt-10 mb-4">
            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-if="status !== 'delete'">คุณทำการเพิ่มร้านค้า {{ selectedSeller.shop_name }}</span>
            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-else>คุณทำการลบร้านค้า {{ selectedSeller.shop_name }}</span>
            <br/>
            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">คุณต้องการทำรายการนี้ ใช่หรือไม่ ?</span>
          </v-card-text>
          <v-card-actions >
        <v-row justify="center" dense class="mb-4">
          <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogConfirm = false">ยกเลิก</v-btn>
          <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" v-if="status !== 'delete'" @click="addSeller()">เพิ่ม</v-btn>
          <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" v-else @click="deleteSeller()">ลบ</v-btn>
        </v-row>
        </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- dialog Add Success -->
      <v-dialog v-model="dialogSuccess" persistent width="373">
        <v-card style="background: #FFFFFF; border-radius: 4px;" min-height="246px">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C" v-if="status !== 'delete'">เพิ่มร้านค้า</font>
              <font color="#27AB9C" v-else>ลบร้านค้า</font>
            </span>
            <v-btn icon dark @click="dialogSuccess = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-container>
            <v-card-text>
              <v-row justify="center" no-gutters dense>
                <v-col cols="12" align="center">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
                </v-col>
                <v-col cols="12" align="center" class="mt-6">
                  <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">เสร็จสิ้น</p>
                </v-col>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
export default {
  data () {
    return {
      disableTable: false,
      search: '',
      showCountRequest: 0,
      dataList: [],
      headers: [
        { text: 'ลำดับ', value: 'index', width: '20', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้าน', value: 'shop_name', width: '180', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อเจ้าของร้าน', value: 'owner_name', width: '180', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', filterable: false, width: '180', sortable: false, class: 'backgroundTable fontTable--text' }
      ],

      status: '',
      ModalDetailSeller: false,
      ModalAddSeller: false,
      clickSelectSeller: false,
      searchSeller: '',
      listSeller: [],
      selectedSeller: [],
      emp_count: 0,

      dialogConfirm: false,
      dialogSuccess: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredList () {
      return this.listSeller.filter(element => {
        return element.shop_name.toLowerCase().includes(this.searchSeller.toLowerCase())
      })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageGracz' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageGracz' }).catch(() => {})
      }
    }
  },
  created () {
    this.$store.commit('openLoader')
    if (localStorage.getItem('oneData') !== null) {
      this.getData()
      this.getListSeller()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.$store.commit('closeLoader')
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async getData () {
      var data = {}
      await this.$store.dispatch('actionsListGSellerShop', data)
      var res = await this.$store.state.ModuleAdminPanit.stateListGSellerShop
      if (res.result === 'SUCCESS') {
        this.dataList = res.data
        this.showCountRequest = this.dataList.length
        this.disableTable = this.showCountRequest < 0
        // console.log(this.dataList)
        // console.log(this.showCountRequest)
        // console.log(this.disableTable)
      } else {
        this.$swal.fire({ icon: 'warning', text: 'error', showConfirmButton: false, timer: 2500 })
      }
    },
    async getListSeller () {
      var data = {}
      await this.$store.dispatch('actionsListSelectNewGSellerShop', data)
      var res = await this.$store.state.ModuleAdminPanit.stateListSelectNewGSellerShop
      if (res.result === 'SUCCESS') {
        this.listSeller = res.data
      } else {
        this.$swal.fire({ icon: 'warning', text: 'error', showConfirmButton: false, timer: 2500 })
      }
    },
    openDetailModal (item) {
      // reset data
      this.selectedSeller = []

      this.selectedSeller = item
      this.emp_count = this.selectedSeller.employee.length
      this.ModalDetailSeller = true
    },
    async openModalAddSeller () {
      // reset data
      this.status = ''
      this.searchSeller = ''
      this.clickSelectSeller = false
      this.selectedSeller = []

      this.ModalAddSeller = true
    },

    showListAgain () {
      this.searchSeller = ''
      this.clickSelectSeller = false
      this.selectedSeller = []
    },
    selectSellerShop (item) {
      this.selectedSeller = item
      this.clickSelectSeller = true
    },
    async addSeller () {
      this.dialogConfirm = false
      var data = {
        seller_shop_id: this.selectedSeller.seller_shop_id,
        select_user_id: this.selectedSeller.user_id
      }
      await this.$store.dispatch('actionsAddGAdminSellerShop', data)
      var res = await this.$store.state.ModuleAdminPanit.stateAddGAdminSellerShop
      if (res.result === 'SUCCESS') {
        this.dialogSuccess = true
        setTimeout(() => {
          this.getData()
          this.getListSeller()
          this.dialogSuccess = false
        }, 1000)
        window.scrollTo(0, 0)
      } else {
        this.$swal.fire({ icon: 'warning', text: 'error', showConfirmButton: false, timer: 2500 })
      }
      // reset value
      this.selectedSeller = []
      this.ModalAddSeller = false
      // this.$swal.fire({ timer: 2500 })
      // this.dialogSuccess = false
    },
    openConfirmDelete (item) {
      this.status = 'delete'
      this.selectedSeller = item
      this.dialogConfirm = true
    },
    async deleteSeller () {
      this.dialogConfirm = false
      var data = {
        seller_shop_id: this.selectedSeller.seller_shop_id
      }
      await this.$store.dispatch('actionsDeleteGSellerShop', data)
      var res = await this.$store.state.ModuleAdminPanit.stateDeleteGSellerShop
      if (res.result === 'SUCCESS') {
        this.dialogSuccess = true
        this.getData()
        this.getListSeller()
        setTimeout(() => {
          this.dialogSuccess = false
        }, 1000)
        window.scrollTo(0, 0)
      } else {
        this.$swal.fire({ icon: 'warning', text: 'error', showConfirmButton: false, timer: 2500 })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
