<template>
  <v-container grid-list-xs>
    <v-dialog
     v-model="BindAccountdialog"
     width="500"
     persistent
     dense
    >
      <v-form ref="FormcreateAccount" :lazy-validation="lazy">
        <v-card>
          <v-card-title>
            <span class="headline">ข้อมูลสำหรับผู้ใช้งาน OneID</span>
          </v-card-title>
          <v-card-text>
            <!-- <v-container> -->
              <v-row dense>
                <v-col cols="12" sm="6" md="12">
                  ชื่อผู้ใช้งานใน OneID <span style="color: red;">*</span>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  md="12"
                >
                  <v-text-field
                    v-model="usernameOne"
                    placeholder="ใส่ชื่อผู้ใช้งาน"
                    dense
                    :rules="Rules.username"
                    outlined
                    required
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" sm="6" md="12">
                  รหัสผ่านใน OneID <span style="color: red;">*</span>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  md="12"
                >
                  <v-text-field
                    v-model="passwordOne"
                    placeholder="ใส่รหัสผ่าน"
                    :rules="Rules.password"
                    dense
                    outlined
                    required
                  ></v-text-field>
                </v-col>
              </v-row>
            <!-- </v-container> -->
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="success" dense @click="confirm()">ยืนยัน</v-btn>
            <v-btn color="error" dense @click="BindAccountdialog = !BindAccountdialog">ยกเลิก</v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <v-form>
      <v-card width="100%" height="100%">
        <!-- <pre>{{userdetail}}</pre> -->
        <v-card-title>ข้อมูลของฉัน</v-card-title>
        <v-card-subtitle>
          จัดการข้อมูลส่วนตัวคุณเพื่อความปลอดภัยของบัญชีผู้ใช้นี้
        </v-card-subtitle>
        <v-col cols="12"><v-divider></v-divider></v-col>
          <v-row>
            <v-col cols="8">
              <v-row>
                <v-col cols="12">
                </v-col>
                <!-- /////////////////////// title ////////////////// -->
                <v-row class="pa-5">
                  <!-- ชื่อผู้ใช้ -->
                  <v-col cols="3">
                    <span class="f-right">ชื่อผู้ใช้</span>
                  </v-col>
                  <v-col cols="8" class="pl-5">
                    <span>{{ userdetail.email }}</span>
                  </v-col>
                  <!-- ชื่อ  -->
                  <v-col cols="3" class="mt-2">
                    <span class="f-right">ชื่อ</span>
                  </v-col>
                  <v-col cols="8" class="pl-5 mt-2">
                    <span>{{ userdetail.first_name_th }}</span>
                    <!-- <a-input
                      dense
                      placeholder="ชื่อ"
                      suffix=" "
                      v-model="userdetail.first_name_th"
                    /> -->
                  </v-col>
                  <!-- สกุล  -->
                  <v-col cols="3" class="mt-2">
                    <span class="f-right">นามสกุล</span>
                  </v-col>
                  <v-col cols="8" class="pl-5 mt-2">
                    <span>{{ userdetail.last_name_th }}</span>
                    <!-- <a-input
                      dense
                      placeholder="นามสกุล"
                      v-model="userdetail.last_name_th"
                    /> -->
                  </v-col>
                  <!-- อีเมล -->
                  <!-- <v-col cols="2">
                    <span class="f-right">อีเมล</span>
                  </v-col>
                  <v-col cols="10" class="pl-5">
                    <span><EMAIL></span>
                  </v-col> -->
                  <!-- หมายเลขโทรศัพท์ -->
                  <v-col cols="3">
                    <span class="f-right">หมายเลขโทรศัพท์</span>
                  </v-col>
                  <v-col cols="8" class="pl-5">
                    <span>{{ userdetail.phone }}</span>
                    <!-- <a-input
                      dense
                      placeholder="หมายเลขโทรศัพท์"
                      v-model="userdetail.phone"
                    /> -->
                  </v-col>
                  <v-col cols="3" />
                  <!-- <v-col cols="2">
                    <v-btn color="light-green" rounded block>บันทึก</v-btn>
                  </v-col> -->
                </v-row>
              </v-row>
            </v-col>
            <!-- col เส้น -->
            <v-col cols="1">
              <v-divider vertical></v-divider>
            </v-col>
            <v-col cols="3">
              <v-row>
                <v-col cols="9">
                  <img width="100%" src="@/assets/noprofile.png" />
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <div v-if="oneUserType === 'market_user'">
            <v-card-actions>
              <v-btn color="primary" @click="BindAccount()">ผูกบัญชีนี้กับ ONE ID</v-btn>
            </v-card-actions>
            <p class="ml-2">*หมายเหตุ ถ้าคุณผูกบัญชีกับ ONE ID คุณสามารถได้รับสิทธิพิเศษมากมายรวมถึงประสบการณ์ที่ดีขึ้น</p>
          </div>
        </v-card>
    </v-form>
  </v-container>
</template>
<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      userdetail: [],
      oneUserType: '',
      BindAccountdialog: false,
      usernameOne: '',
      passwordOne: '',
      lazy: false,
      Rules: {
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้',
          v => /^[A-Za-z0-9]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรและตัวเลขเท่านั้น'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ]
      }
    }
  },
  async created () {
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.oneUserType = onedata.user.type_user
    // console.log('type user', this.oneUserType)
    var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    var data = {
      role_user: dataRole.role
    }
    await this.$store.dispatch('actionsUserDetailPage', data)
    const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
    // console.log('userdetail data', userdetail)
    this.userdetail = userdetail.data[0]
  },
  methods: {
    BindAccount () {
      this.$swal.fire({
        text: `คุณต้องการผูกบัญชี ${this.userdetail.email} กับ One ID จริงหรือไม่?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยอมรับ',
        cancelButtonText: 'ไม่ยอมรับ'
      }).then(async (result) => {
        if (result.isConfirmed) {
          this.BindAccountdialog = true
          // const { value: password } = await this.$swal.fire({
          //   // text: 'กรุณาใส่รหัสผ่านเพื่อยืนยันผู้ใช้บัญชีนี้',
          //   showCancelButton: true,
          //   confirmButtonColor: '#3085d6',
          //   cancelButtonColor: '#d33',
          //   confirmButtonText: 'ตกลง',
          //   cancelButtonText: 'ยกเลิก',
          //   input: 'password',
          //   inputLabel: 'ใส่รหัสผ่านเพื่อยืนยันผู้ใช้บัญชีนี้',
          //   inputPlaceholder: 'กรุณาใส่รหัสผ่าน',
          //   inputValidator: (value) => {
          //     if (!value) {
          //       return 'กรุณาใส่รหัสผ่าน'
          //     }
          //   }
          // })
          // if (password) {
          //   var data = {
          //     username: this.userdetail.email,
          //     password: password
          //   }
          //   // console.log(data)
          //   await this.$store.dispatch('actionBindAccount', data)
          //   var response = await this.$store.state.ModuleUser.stateBindAccount
          //   console.log(response)
          //   if (response.result === 'SUCCESS') {
          //     this.$swal.fire({ title: 'ผูกบัญชีสำเร็จ!', text: 'กรุณา Login บัญชีใหม่เพื่อใช้งาน', icon: 'success', timer: 3000, showConfirmButton: false })
          //   } else {
          //     this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
          //   }
          // }
        }
      })
    },
    async confirm () {
      if (this.$refs.FormcreateAccount.validate(true)) {
        var data = {
          username: this.usernameOne,
          password: this.passwordOne
        }
        await this.$store.dispatch('actionBindAccount', data)
        var response = await this.$store.state.ModuleUser.stateBindAccount
        // console.log(response)
        if (response.result === 'SUCCESS') {
          this.$swal.fire({ title: 'ผูกบัญชีสำเร็จ!', text: 'กรุณา Login บัญชีใหม่เพื่อใช้งาน', icon: 'success', timer: 3000, showConfirmButton: false })
        } else {
          this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>
<style scoped>
.f-right {
  float: right;
}
</style>
