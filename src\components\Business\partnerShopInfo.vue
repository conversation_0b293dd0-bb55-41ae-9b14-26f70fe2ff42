<template>
  <v-container>
    <v-row v-if="!havePartner && isLoading">
      <v-col>
        <v-row>
          <v-col class="ma-3 mt-5 mb-0 pb-0">
            <span style="font-size: 24px; font-weight: bold;"><v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>ลงทะเบียน Partner</span>
          </v-col>
        </v-row>
        <v-row>
          <v-col :class="MobileSize ? '' : 'ma-6 mt-3 d-flex justify-center'">
            <v-card class="elevation-0">
            <v-card-text class="d-flex align-center flex-column" :style="MobileSize ? '' : 'padding: 5vw; padding-top: 3px;'">
                <v-img
                src="@/assets/Coorperation/createPartherShop.png"
                width="220"
                contain
                ></v-img>
                <v-col>
                  <v-card width="450" class="elevation-0">
                    <v-col cols="12" class="d-flex justify-center pb-2">
                      <span class="mt-2" style="font-size: 16px; color: #636363;">กรุณาเลือกร้านค้าของคุณที่ต้องการลงทะเบียน Partner</span>
                    </v-col>
                    <v-col cols="12" class="d-flex justify-center pt-0">
                      <span style="font-size: 16px; color: #636363;">โดยค้นหาจากเลขประจำตัวผู้เสียภาษีอากร (Tax ID)</span>
                    </v-col>
                  </v-card>
                </v-col>
                <v-row>
                  <v-col cols="12" class="pa-1 pt-4">
                    <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษีอากร (Tax ID)</span>
                  </v-col>
                  <v-col cols="12" class="pa-1">
                    <v-text-field
                      placeholder="ระบบเลขประจำตัวผู้เสียภาษีอากร (Tax ID)"
                      outlined
                      dense
                      disabled
                      v-model="taxId"
                      @input="validateTaxID($event)"
                      :rules="Rules.thaiID"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-row no-gutters>
                  <v-col>
                    <v-btn rounded color="#27ab9c" class="white--text" @click="gotoRegisterPartner">ลงทะเบียน</v-btn>
                  </v-col>
                </v-row>
                <!-- <div class="mt-2 d-flex align-center" style="font-size: large; gap: .5vw;">
                  <span>กดปุ่ม</span>
                  <v-btn @click="createService()" style="color: #fff; border-radius: 1vw;" color="#27AB9C">เพิ่มสินค้า</v-btn>
                  <span>ที่นี่</span>
                </div> -->
            </v-card-text>
        </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="havePartner && isLoading">
      <v-col cols="12" class="d-flex justify-center">
        <span style="font-size: 24px; font-weight: bold;"><v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>ข้อมูลร้านค้า Partner</span>
        <v-spacer></v-spacer>
        <v-btn rounded color="#38b2a4" @click="gotoEditPage"><v-icon color="white" class="mr-2">mdi-pencil</v-icon><span style="color: white;">แก้ไข</span></v-btn>
      </v-col>
    </v-row>
    <v-row v-if="havePartner && isLoading">
      <!-- First Card for Partner Images -->
      <v-col cols="12">
        <v-card class="custom-card elevation-0">
          <div class="card-header">
            รูปภาพร้านค้า Partner
          </div>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="3">
                <v-card class="text-center elevation-2">
                  <v-img
                    v-if="showpartnerdetail.media && showpartnerdetail.media.find(image => image.image_type === 'main')"
                    :src="showpartnerdetail.media.find(image => image.image_type === 'main').media_path"
                    class="custom-image"
                    height="250px"
                    width="100%"
                    contain
                  ></v-img>
                  <v-img
                    v-else
                    src="@/assets/NoImage.png"
                    class="custom-image"
                    height="250px"
                    width="100%"
                    contain
                  ></v-img>
                </v-card>
                <br>
                <v-row>
                  <v-col>
                    <span style="display: flex; justify-content: center; padding: 14px;"><b>โปรไฟล์ Partner</b></span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="9">
                <v-card class="elevation-2">
                  <v-img
                    v-if="showpartnerdetail.media && showpartnerdetail.media.find(image => image.image_type === 'banner')"
                    :src="showpartnerdetail.media.find(image => image.image_type === 'banner').media_path"
                    class="custom-image"
                    height="250px"
                    width="100%"
                    contain
                  >
                    <v-card-title>
                      หน้าปกร้านค้า Partner
                    </v-card-title>
                  </v-img>
                  <v-img
                    v-else
                    src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                    class="custom-image"
                    height="250px"
                    width="100%"
                    contain
                  ></v-img>
                </v-card>
                <br>
                <v-row>
                  <v-col
                    v-for="(image, index) in (Array.isArray(showpartnerdetail.media) ? showpartnerdetail.media.filter(image => image.image_type === 'banner') : [])"
                    :key="index"
                    cols="4"
                    sm="2"
                  >
                    <v-card class="elevation-1">
                      <v-img
                        :src="image.media_path || '@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png'"
                        height="50px"
                        width="100%"
                        contain
                      ></v-img>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12">
        <v-card class="custom-card elevation-0">
          <div class="card-header">
            ข้อมูล Partner
          </div>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <span style="font-size: 30px;"><b>{{ showpartnerdetail.partner_name !== undefined || '' || null ? showpartnerdetail.partner_name : '-' }}</b></span>
              </v-col>
              <v-col cols="12">
                <span>ประเภทบริการ : <span v-for="(item, index) in showpartnerdetail.service_type" :key="index">{{item}} </span></span><br>
                <span>เลขประจำตัวผู้เสียภาษีอากร : {{ showpartnerdetail.partner_id_card !== undefined || '' || null ? showpartnerdetail.partner_id_card : '-' }}</span><br>
                <span>URL ร้านค้า : {{ showpartnerdetail.url_name !== undefined || '' || null ? showpartnerdetail.url_name : '-' }}</span>
              </v-col>
              <br>
              <v-col cols="12" class="d-flex align-center">
                <img
                  src="@/assets/Marketplace_partner/Capa_1.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>เกี่ยวกับ Partner</b></span>
              </v-col>
              <v-col cols="12">
                  <span>{{ showpartnerdetail.detail !== undefined || '' || null ? showpartnerdetail.detail : '-' }}</span>
              </v-col>
              <v-col cols="12" class="d-flex align-center">
                <img
                  src="@/assets/Marketplace_partner/Layer_1_3.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>ข้อมูลติดต่อ</b></span>
              </v-col>
              <v-col cols="12">
                  <span>เบอร์โทรศัพท์ : {{ showpartnerdetail.partner_phone_no !== null ? showpartnerdetail.partner_phone_no : '-' }}</span><br>
                  <span>Line : {{ showpartnerdetail.line !== null ? showpartnerdetail.line : '-' }}</span><br>
                  <span>Facebook : {{ showpartnerdetail.facebook !== null ? showpartnerdetail.facebook : '-' }}</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12">
        <v-card class="custom-card elevation-0">
          <div class="card-header">
            ข้อมูลบัญชี
          </div>
          <v-card-text>
            <v-row>
              <v-col cols="12" class="d-flex align-center">
                <img
                  src="@/assets/Marketplace_partner/Layer_1.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>ข้อมูลบัญชี</b></span>
              </v-col>
              <v-col cols="12" md="4">
                <span><b>ประเภทการชำระเงิน :</b> {{ showpartnerdetail.book_bank_type !== undefined || '' || null ? (showpartnerdetail.book_bank_type === 'savings' ? 'ออมทรัพย์' : 'ฝากประจำ') : '-' }}</span>
              </v-col>
              <v-col cols="12" md="4">
                <span><b>ชื่อบัญชี :</b> {{ showpartnerdetail.bank_username !== undefined || '' || null ? showpartnerdetail.bank_username  : '-' }}</span>
              </v-col>
              <v-col cols="12" md="4">
                <span><b>ชื่อธนาคาร :</b> {{ showpartnerdetail.bank_name !== undefined || '' || null ? showpartnerdetail.bank_name : '-' }}</span>
              </v-col>
              <v-col cols="12" md="4">
                <span><b>ชื่อสาขาธนาคาร :</b> {{ showpartnerdetail.bank_branch !== undefined || '' || null ? showpartnerdetail.bank_branch : '-' }}</span>
              </v-col>
              <v-col cols="12" md="8">
                <span><b>หมายเลขบัญชีธนาคาร :</b> {{ showpartnerdetail.bank_no !== undefined || '' || null ? showpartnerdetail.bank_no : '-' }}</span>
              </v-col>
              <v-col cols="12">
                <span><b>รูปหน้าบัญชีธนาคาร :</b></span>
                <br>
                <img
                  :src="showpartnerdetail.bookbank_image_url"
                  class="responsive-image mt-2"
                />
              </v-col>
              <v-col cols="12" class="d-flex align-center">
                <img
                  src="@/assets/Marketplace_partner/Layer_1_2.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>ข้อมูลภาษี</b></span>
              </v-col>
              <v-col cols="12">
                <span><b>หมายเลขประจำตัวผู้เสียภาษี :</b> {{ showpartnerdetail.partner_id_card !== undefined || '' || null ? showpartnerdetail.partner_id_card : '-' }}</span>
              </v-col>
              <v-col cols="12" class="d-flex align-center">
                <img
                  src="@/assets/Marketplace_partner/image_3.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>ที่อยู่นิติบุคคล</b></span>
              </v-col>
              <v-col cols="12">
                <span><b>ประเภทที่อยู่ :</b> {{ showpartnerdetail.type_address === 'business' ? 'ใช้ที่อยู่ข้อมูลนิติบุคคล' : 'ใช้ที่อยู่ใหม่'}}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>ที่อยู่ :</b> {{ address.house_no !== undefined && address.house_no !== null && address.house_no !== '' ? address.house_no : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>ห้องเลขที่ :</b> {{ address.room_no !== undefined && address.room_no !== null && address.room_no !== '' ? address.room_no : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>ชั้นที่ :</b> {{ address.floor !== undefined && address.floor !== null && address.floor !== '' ? address.floor : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>อาคาร :</b> {{ address.building_name !== undefined && address.building_name !== null && address.building_name !== '' ? address.building_name : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>หมู่บ้าน :</b> {{ address.moo_ban !== undefined && address.moo_ban !== null && address.moo_ban !== '' ? address.moo_ban : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>หมู่ที่ :</b> {{ address.moo_no !== undefined && address.moo_no !== null && address.moo_no !== '' ? address.moo_no : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>ตรอก/ซอย :</b> {{ address.soi !== undefined && address.soi !== null && address.soi !== '' ? address.soi : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>แยก :</b> {{ address.yaek !== undefined && address.yaek !== null && address.yaek !== '' ? address.yaek : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>ถนน :</b> {{ address.street !== undefined && address.street !== null && address.street !== '' ? address.street : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>ตำบล/แขวง :</b> {{ address.tambon !== undefined && address.tambon !== null && address.tambon !== '' ? address.tambon : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>อำเภอ/เขต :</b> {{ address.amphoe !== undefined && address.amphoe !== null && address.amphoe !== '' ? address.amphoe : '-' }}</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 12" md="4">
                <span><b>จังหวัด :</b> {{ address.province !== undefined && address.province !== null && address.province !== '' ? address.province : '-' }}</span>
              </v-col>
              <v-col cols="12">
                <span><b>รหัสไปรษณีย์ :</b> {{ address.zipcode !== undefined && address.zipcode !== null && address.zipcode !== '' ? address.zipcode : '-' }}</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      havePartner: false,
      taxId: '',
      Rules: {
        required: [{ required: true, message: 'กรุณากรอกข้อมูล' }],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        thaiID: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => this.validNationalID(v) || 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
        ]
      },
      showpartnerdetail: [],
      showaccountdetail: [],
      showPartnerList: [],
      address: {},
      isLoading: true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
      }
    }
  },
  async created () {
    this.isLoading = false
    await this.getTaxId()
    await this.getPartnerCode()
    await this.getPartnerList()
    this.isLoading = true
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    gotoRegisterPartner () {
      if (this.MobileSize) {
        this.$router.push({ path: '/detailbusinesssidMobile?isRegisterPartner=yes' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailbusinesssid?isRegisterPartner=yes' }).catch(() => {})
      }
    },
    validateTaxID (val) {
      if (this.validNationalID(val)) {
        return true
      } else {
        return false
      }
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    gotoEditPage () {
      this.$router.push({ path: '/editPartner' }).catch(() => {})
      if (!this.MobileSize) {
        this.$router.push('/editPartner')
      } else {
        this.$router.push('/editPartnerMobile')
      }
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          // this.taxId = response.data.array_business[0].owner_tax_id
        }
        this.$store.commit('closeLoader')
      }
    },
    async getPartnerCode () {
      var data = {
        id_card_num: this.taxId
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.data.partner_code === null) {
        this.havePartner = false
      } else {
        this.havePartner = true
      }
      this.$store.commit('closeLoader')
    },
    async getPartnerList () {
      var data = {
        id_card_num: this.taxId // ใช้ไอดี mock up อยู่
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerList', data)
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerList
      this.showpartnerdetail = response.data[0]
      if (response.code === 200 && response.data.length !== 0) {
        if (this.showpartnerdetail.new_address.length === 0) {
          this.address = response.data[0].address[0]
        } else {
          this.address = response.data[0].new_address[0]
        }
      }
      this.$store.commit('closeLoader')
      // console.log(this.showpartnerdetail)
    },
    backtoMenu () {
      this.$router.push('/detailbusinesssidMobileMenu')
    }
  }
}
</script>

<style>

</style>

<style scoped>

.font-weight-bold {
  font-weight: bold;
}

.custom-card {
  border: 1px solid #ccc; /* กำหนดกรอบสีเทา */
  border-radius: 8px; /* เพิ่มมุมโค้ง */
}

.card-header {
  background-color: #f5f5f5; /* สีเทาสำหรับ Header */
  color: #333; /* สีข้อความใน Header */
  font-weight: bold;
  padding: 16px; /* เพิ่มพื้นที่ใน Header */
  border-bottom: 1px solid #ccc; /* เส้นแบ่งระหว่าง Header กับเนื้อหา */
}

.responsive-image {
  max-width: 350px; /* ขนาดสูงสุดสำหรับหน้าจอใหญ่ */
  width: 100%; /* ปรับให้เต็มความกว้างของหน้าจอสำหรับมือถือ */
  height: auto; /* รักษาอัตราส่วนภาพ */
}

.tooltip-box {
  position: absolute;
  top: 130px; /* ตำแหน่งด้านล่างข้อความ */
  background-color: #424242; /* สีดำ */
  color: white;
  border-radius: 8px;
  padding: 10px;
  width: 375px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  z-index: 10; /* เพื่อให้แสดงเหนือองค์ประกอบอื่น */
}

.tooltip-box-mobile {
  position: absolute;
  top: 190px; /* ตำแหน่งด้านล่างข้อความ */
  background-color: #424242; /* สีดำ */
  color: white;
  border-radius: 8px;
  padding: 10px;
  width: 300px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  z-index: 10; /* เพื่อให้แสดงเหนือองค์ประกอบอื่น */
}

.tooltip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-icon {
  margin-right: 5px;
  color: white;
}
</style>
