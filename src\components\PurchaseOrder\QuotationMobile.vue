<template>
<v-container>
  <v-row>
    <v-col>
      <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;">
        <v-icon color="#27AB9C" class="mr-2" @click="backtoList">mdi-chevron-left
          </v-icon>แก้ไขใบเสนอราคา
          <!-- {{(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)}}
          {{quotationDetail.date}} -->
        </span>
    </v-col>
  </v-row>
    <div class="center-screen mt-4 pt-6" style="background-color: #C4C4C4; width: 500px;overflow-y: auto; margin-top: 2em; margin-bottom: 3em;padding-top: 4em; padding-bottom: 4em;" >
    <v-card width="450px" height="1100px">
<div class="document active">
  <div class="spreadSheetGroup">
    <table class="shipToFrom">
      <thead style="font-weight:bold">
        <tr>
          <th></th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td style="width:30%; text-align: center;" class="">
             <div>
              <div v-if="(quotationDetail.logo_path === '' && quotationDetail.logo_path === null) || imgShow === true">
                <v-icon class="ml-12 pl-12" style="margin-top: 5px;cursor: pointer; position: absolute;z-index: 1;" @click="imgShow = !imgShow ">mdi-image-edit-outline</v-icon>
                <uploadImg
                ref="pictureInput"
                @change="onChange"
                :max="1"
                />
                </div>
                <div v-else>
                  <v-icon v-show="UserRoleSet.logo" class="ml-12 pl-12" style="position: absolute;z-index: 1;" @click="imgShow = !imgShow ">mdi-image-edit-outline</v-icon>
                  <img class="logo-qu" :src="quotationDetail.logo_path" alt="Logo" width="100" height="110" v-if="quotationDetail.logo_path !== '' && quotationDetail.logo_path !== null" >
                </div>
             </div>
          </td>
          <td  style="width:70%">
            <strong style="font-size: 16px;">{{addressCompany.name_on_document_th}}<!-- <span class="ml-3">{{addressCompany.first_name_th}}</span> --></strong><br/>
            <div class="mt-2">
            <span :class="addressCompany.address.room_no !== null ? 'mr-3' : ''">{{addressCompany.address}}</span>
            </div>
<v-row>
  <v-col cols="12" md="3" class="mr-0 pr-0 mt-2">โทร: {{addressCompany.tel_no}} </v-col>
  <v-col cols="12" md="3" class="mr-0 pr-0 mt-2">มือถือ: {{addressCompany.mobile_no}} </v-col>
  <v-col cols="12" md="6" class="ml-0 pl-0 mt-2">อีเมล: {{addressCompany.email}}</v-col>
</v-row>
<v-row class="mt-2 pt-0">
  <v-col cols="12" md="9" class="mt-0 pt-0">
เลขประจำตัวผู้เสียภาษี :&nbsp;&nbsp;{{addressCompany.id_card_num}}
</v-col>
<v-col  cols="12" md="3" class="mt-0 pt-0">({{addressCompany.branch_name}})</v-col>
</v-row>
          </td>
        </tr>
      </tbody>
    </table>
    <hr style="visibility:hidden"/>
    <v-row>
      <v-col cols="12" md="3"></v-col>
      <v-col cols="12" md="6" align="center" style="font-size: 27px; font-weight: bold"><div style="border: 1px solid #A1A1A1;border-radius: 4px;">ใบเสนอราคา / Quotation</div></v-col>
      <v-col cols="12" md="3"></v-col>
    </v-row>
    <table class="proposedWork" width="100%" style="margin-top:20px">
      <thead>
        <tr>
          <td colspan="5">
            <table width="100%">
      <tbody>
        <tr>
          <!-- <td style="50%" style="vertical-align:top"> -->
          <td style="width: 50%; border-top: hidden; border-left: hidden; border-bottom: hidden; border-right: hidden;" class="my-0 py-0">
            <table style="width:100%" class="my-0 py-0">
              <tbody class="my-0 py-0">
                <tr style="border: hidden" class="my-0 py-0">
                  <td style="text-align:left; border: hidden" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    ชื่อลูกค้า:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                     <v-text-field
                     :disabled="true"
                     v-model="addressCompany.name_on_document_th"
            label=""
            solo
            dense
            class="my-0 py-0"
          ></v-text-field>
                  </td>
                </tr>
                <tr>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    ที่อยู่:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-textarea
                    :disabled="true"
                    v-model="quotationDetail.address"
          solo
          name="input-7-4"
          label=""
          height="110px"
          class="my-0 py-0"
        ></v-textarea>
                  </td>
                </tr>
                <tr class="my-0 py-0">
                  <td style="text-align:left; border: hidden" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    เลขประจำตัวผู้เสียภาษี:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-text-field
                    :disabled="true"
                    v-model="quotationDetail.tax_id"
            label=""
            solo
            dense
            class="my-0 py-0"
          ></v-text-field>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
         <!--  <td style="width: 30%; border-top: hidden; border-bottom: hidden;">
          </td> -->
          <td style="padding-left:12px; width:50%; vertical-align:top; border-top: hidden; border-right: hidden; border-bottom: hidden;">
           <table style="width:100%" class="my-0 py-0">
              <tbody class="my-0 py-0">
                <tr style="border: hidden" class="my-0 py-0">
                  <td style="text-align:left; border: hidden ;" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    เลขที่ใบเสนอราคา:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-text-field
                    :disabled="!UserRoleSet.qu_number"
                    v-model="quotationDetail.qu_number"
            label=""
            solo
            dense
            class="my-0 py-0"
          ></v-text-field>
                  </td>
                </tr>
                <tr class="my-0 py-0">
                  <td style="text-align:left; border: hidden;" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    วันที่:
                  </td>
                  <td v-if="UserRoleSet.dates" style="text-align:left; border: hidden; " class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-menu
          ref="menu1"
          v-model="menu1"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          max-width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormatted"
              label=""
              hint=""
              persistent-hint
              append-icon="mdi-calendar"
              dense
              outlined
              class="mt-2"
              v-bind="attrs"
              v-on="on"
            ></v-text-field>
          </template>
          <v-date-picker
            v-model="quotationDetail.date"
            no-title
            :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            @input="menu1 = false"
          ></v-date-picker>
        </v-menu>
                    <!-- <v-text-field
                    v-model="dateTH"
                    :disabled="!UserRoleSet.dates"
            label=""
            solo
            dense
            class="my-0 py-0"
          ></v-text-field> -->
                  </td>
                  <td v-else style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-text-field
                    v-model="dateFormatted"
                    disabled
            label=""
            solo
            dense
            class="my-0 py-0"
          ></v-text-field>
                  </td>
                </tr>
                <tr class="my-0 py-0">
                  <td style="text-align:left; border: hidden" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    เงื่อนไขการชําระเงิน:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-select
                      :items="itemSelect"
                      item-text= "name"
                      item-value="name"
                      v-model="quotationDetail.payment_method"
                      :disabled="!UserRoleSet.payment_method"
                      dense
                      solo
                    ></v-select>
                  </td>
                </tr>
                <tr class="my-0 py-0">
                  <td style="text-align:left; border: hidden" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    เครดิต:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-text-field
                    v-model="quotationDetail.credit_term"
                    label="วัน"
                    disabled
            solo
            dense
            class="my-0 py-0"
          ></v-text-field>
                  </td>
                </tr>
                <tr class="my-0 py-0" v-if="quotationDetail.credit_term !== 0">
                  <td style="text-align:left; border: hidden" class="ma-0 pt-4 pl-0 pr-0 pb-0">
                    จำนวนงวด:
                  </td>
                  <td style="text-align:left; border: hidden" class="ma-0 pt-1 pl-0 pr-0 pb-0">
                    <v-select
                      v-model="quotationDetail.num_of_credit_term"
                      :disabled="!UserRoleSet.installment"
                      :items="itemSelectInstallments"
                      item-text= "name"
                      item-value="name"
                      dense
                      solo
                    ></v-select>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
          </td>
        </tr>
        <tr>
        <th>ลำดับ</th>
        <th>รายละเอียด</th>
        <th>จำนวน</th>
        <th>หน่วย</th>
        <th class="amountColumn">ราคา/หน่วย</th>
        <th class="docEdit trAdd" @click="addRow" v-if="UserRoleSet.btnAdd" ><v-icon color="white">mdi-plus</v-icon></th>
        </tr>
      </thead>
      <tbody>
        <tr v-for='(item, index) in quotationDetail.product_list' :key="index">
          <td style="text-align: center;vertical-align: middle;"><div style="font-size: 16px; font-weight: bold;">{{index + 1}}</div></td>
          <td class="description px-2 my-0 pt-2 pb-0" style="width: 65%; height: 2px;">
            <!-- <v-text-field
            label=""
            solo
            dense
            v-model="item.description"
            class="my-0 py-0"
          ></v-text-field> -->
          <div v-if="item.status !== '0'">
            <table>
              <tbody>
                <tr>
                 <!--  <td style="border: hidden" width="20%">
                    <v-img
              lazy-src="item.product_image"
              max-height="90"
              max-width="90"
              :src="item.product_image"
            ></v-img>
                  </td>
 -->                  <td style="border: hidden;text-align:left" width="50%">
                    <span style="font-size: 14px;">{{item.product_name !== '' ? item.product_name+' ' : item.product_name}}</span>
                    <span>
                     <div v-if="item.key_1_value !== null && item.key_1_value !== '' "> {{item.key_1_value  + ': ' + item.product_attribute_detail.attribute_priority_1 }}</div>

                     <div v-if="item.key_2_value !== null && item.key_2_value !== ''">{{item.key_2_value  + ': ' + item.product_attribute_detail.attribute_priority_2 }}</div>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else>
          <v-btn
      class="ma-2"
      color="success"
      @click="openModalPurchaseOrder(index)"
    >
      เพิ่มสินค้า
    </v-btn>
    </div>
          </td>
          <td class="" style="width: 10%;">
            <v-text-field
            label=""
            type="number"
            :max="item.stock !== '' ? item.stock : ''"
            min="1"
            solo
            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
            dense
            :disabled="!UserRoleSet.quantity"
            v-model="item.quantity"
            :rules="[v => !!v || 'กรุณากรอกจำนวน']"
          ></v-text-field>
          </td>
          <td class="unit"
          style="width: 5%;"
          >
            <v-text-field
            label=""
            placeholder="ชิ้น"
            solo
            dense
            disabled
            v-model="item.unit"
          ></v-text-field>
          </td>
          <td class=" amountColumn rowTotal"
          style="width: 15%;"
          >
            <v-text-field
            label=""
            solo
            dense
            disabled
            v-model="item.price"
          ></v-text-field>
          </td>
          <td class="docEdit tdDelete" @click="deleteRow(index)" v-if="UserRoleSet.btnRemove">
            <div style="padding-top: 80%;">
            <v-icon >mdi-delete-outline</v-icon>
            </div>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr >
          <td style=""></td>
          <td style=""></td>
          <td style="border-left: hidden"></td>
        <td style="text-align:right">รวมเงิน:</td>
        <td class=" subtotal">{{formatTofixed(parseFloat(excludingVat))}}</td>
        <td class="docEdit"></td>
        </tr>
        <tr>
          <td style=""></td>
          <td style="border-top: hidden;"></td>
          <td style="border-top: hidden; border-left: hidden"></td>
        <td style="text-align:right">ค่าจัดส่ง:</td>
        <td class=""><v-text-field
            label=""
            solo
            dense
            type="number"
            :rules="Rules.number"
            v-model="quotationDetail.total_shipping"
            :disabled="!UserRoleSet.total_shipping"
          ></v-text-field>
        <!-- {{formatTofixed(parseFloat(quotationDetail.total_shipping))}} -->
      </td>
        <td class="docEdit"></td>
        </tr>
        <!-- <tr>
          <td ></td>
          <td style="border-top: hidden;"></td>
          <td style="border-top: hidden; border-left: hidden"></td>
        <td style="text-align:right;white-space:nowrap">ภาษีมูลค่าเพิ่ม 7%:</td>
        <td class="">{{Vat}}</td>
        <td class="docEdit"></td>
        </tr> -->
        <tr>
          <td style=""></td>
          <td style="border-top: hidden; padding-top: 50px;">
            <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:right;padding-top: 15px;">
                    <strong style="font-size: 9px;">ตัวอักษร (</strong>
                  </td>
                  <td style="text-align:left; border: hidden">
                   <v-text-field
                   :disabled="!UserRoleSet.thText"
                   v-model="thaiIncludingVat"
                    label=""
                    solo
                    dense
                   ></v-text-field>
                  </td>
                  <td style="text-align:left; border: hidden; padding-top: 18px;">
                    <strong>)</strong>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
          <td style="border-top: hidden; border-left: hidden"></td>
          <td style="text-align:right;white-space:nowrap">ราคารวมทั้งสิ้น:</td>
        <td class="total2" >{{formatTofixed(excludingVat + parseFloat(quotationDetail.total_shipping !== '' ? quotationDetail.total_shipping: 0))}}</td>
        <td class="docEdit"></td>
        </tr>
      </tfoot>
    </table>
    <table width="100%">
      <tbody>
        <tr>
          <!-- <td style="50%" style="vertical-align:top"> -->
          <td style="50%">
            <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:left">
                  <!-- <p>1. Please send two copies of your invoice.</p>
                 </p> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
          <td style="padding-left:12px; width:100%; vertical-align:top">
            <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:left; width:5%;">
                    <strong>หมายเหตุ:</strong>
                  </td>
                  <td style="text-align:left; padding-top: 28px;">
                    <v-text-field
                    v-model="quotationDetail.note"
                    :disabled="!UserRoleSet.remark"
                    label=""
                    :rules="[v => !!v || 'กรุณากรอกหมายเหตุถ้าไม่มี ให้ระบุ -']"
                    solo
                    dense
                   ></v-text-field>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <!-- <v-row style="margin-top: -5em;">
      <v-col style="text-align:left;padding-left:12px;" cols="12">ขอบพระคุณที่ท่านให้ความสนใจในบริการของเรา</v-col>
    </v-row> -->
    <table class="mt-10">
      <tbody>
        <tr>
          <!-- <td style="50%" style="vertical-align:top"> -->
          <td style="padding-left:12px; width:100%; vertical-align:top">
            <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:center; width:30%;">
                    <strong>สั่งซื้อโดย</strong>
                  </td>
                  <td style="text-align:center; width:30%;">
                    <strong>ออกโดย</strong>
                  </td>
                  <td style="text-align:center; width:30%;">
                    <strong>ผู้มีอำนาจอนุมัติ</strong>
                  </td>
                </tr>
                <tr>
                  <td>
                    <v-text-field
                    width=""
                    label=""
                    disabled
                    dense
                   ></v-text-field>
                  </td>
                  <td>
                    <v-text-field
                    width=""
                    label=""
                    disabled
                    dense
                   ></v-text-field>
                  </td>
                  <td>
                    <v-text-field
                    width=""
                    label=""
                    disabled
                    dense
                   ></v-text-field>
                  </td>
                </tr>
                <tr>
                  <td style="text-align:center;">
                    <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:right;">
                  <div class="mb-5">(</div>
                  </td>
                  <td style="text-align:left; border: hidden;margin-top: 5px;">
                   <v-text-field
                   v-model="quotationDetail.buyer_name"
                   :disabled="!UserRoleSet.buyer_name_by"
                    label=""
                    solo
                    dense
                   ></v-text-field>
                  </td>
                  <td style="text-align:left; border: hidden; padding-top: 0px;">
                   <div class="mb-3">)</div>
                  </td>
                </tr>
              </tbody>
            </table>
                  </td>
                  <td style="text-align:center;">
                    <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:right;">
                  <div class="mb-5">(</div>
                  </td>
                  <td style="text-align:left; border: hidden;margin-top: 5px;">
                   <v-text-field
                   v-model="quotationDetail.prepared_by"
                   :disabled="!UserRoleSet.prepared_by"
                    label=""
                    solo
                    dense
                   ></v-text-field>
                  </td>
                  <td style="text-align:left; border: hidden; padding-top: 0px;">
                   <div class="mb-3">)</div>
                  </td>
                </tr>
              </tbody>
            </table>
                  </td>
                  <td style="text-align:center;">
                    <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:right;">
                  <div class="mb-5">(</div>
                  </td>
                  <td style="text-align:left; border: hidden;margin-top: 5px;">
                   <v-text-field
                   v-model="quotationDetail.approve_by"
                   :disabled="!UserRoleSet.approve_by"
                    label=""
                    solo
                    dense
                   ></v-text-field>
                  </td>
                  <td style="text-align:left; border: hidden; padding-top: 0px;">
                   <div class="mb-3">)</div>
                  </td>
                </tr>
              </tbody>
            </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <table v-if="UserRoleSet.couponAndpoint">
      <tbody>
        <tr>
          <td style="text-align:left;">
        <div v-if="$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon.length === 0 && $store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point.length === 0">
         <v-btn
        class="ma-1"
        outlined
        color="#0000F9"
        @click="getCoupon"
      ><v-icon color="#0000F9" class="mr-1">mdi-ticket-percent-outline</v-icon>
        <span style="text-decoration: underline; color: #0000F9; cursor: pointer;">เลือกใช้สิทธิพิเศษ</span>
      </v-btn>
    </div>
    <div v-else>
       <v-btn
        class="ma-1"
        outlined
        color="#0000F9"
      ><v-icon color="#0000F9" class="mr-1">mdi-ticket-percent-outline</v-icon>
        <span style="text-decoration: underline; color: #0000F9;">
          {{$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon.length !== 0 ? 'คูปองส่วน' + $store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].name + '&nbsp;&nbsp;ลด&nbsp;&nbsp;' + $store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].real_discount + '&nbsp;&nbsp;บาท': ''}}

          {{$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point.length !== 0 ? 'ใช้คะแนนเป็นส่วนลด&nbsp;&nbsp;' + $store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].maxUse : ''}}
        </span>
      </v-btn>
      <v-btn
      outlined
      small
      color="#FFA52C"
      fab @click="editCoupon">
        <v-icon>mdi-playlist-edit</v-icon>
      </v-btn>&nbsp;&nbsp;
      <v-btn
      outlined
      small
      fab
      color="#FF0018"
      @click="removeCoupon">
      <v-icon class="">mdi-delete-empty</v-icon>
    </v-btn>
    </div>
    </td>
   <!--  <td></td>
    <td></td> -->
    </tr>
      </tbody>
    </table>
  </div>
  <PurchaseOrderModal/>
  <selectCopons />
  <v-dialog v-model="quotationModel" width="800">
    <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>ข้อมูลใบเสนอราคา</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="quotationModel = false" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <div style="text-align:center;" >
        <div v-if="loading2" style="margin-left: 27em;" class="pb-10">
          <div class="spinner">
          <div></div>
          <div></div>
          <p class="p-text">กำลังโหลด...</p>
           </div>
        </div>
        <div v-else>
        <v-container grid-list-xs class="mt-5">
            <v-row>
              <v-col cols="4">รวมเงิน</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_price) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">ส่วนลด</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_discount) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">ภาษีมูลค่าเพิ่ม</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_vat) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">ค่าจัดส่ง</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_shipping): ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">รวมทั้งสิ้น</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.net_price) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
          <v-card-actions class="mt-5">
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="quotationModel = false">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" :loading="loading" :disabled="!quotationModel" @click="postFormData">ยืนยัน</v-btn>
          </v-card-actions>
        </v-container>
        </div>
        </div>
      </v-card>
  </v-dialog>
</div>
 </v-card>
 </div>
<!-- </v-card> -->
<v-row class="mb-5">
    <v-col cols="12" align="right">
       <v-btn outlined color="success" v-show="UserRoleSet.total_shipping" @click="shippingCostSeller" :loading="loading" :disabled="!btnShipping" class="mr-2">คำนวณค่าจัดส่ง</v-btn>
       <v-btn color="success" v-if="user_role === 'seller'" :disabled="quotationDetail.total_shipping === ''" class="mr-2" @click="postFormData">ยืนยัน</v-btn>
       <v-btn color="success" v-else :disabled="!CHKproductList" class="mr-2" @click="shippingCost">บันทึกข้อมูล</v-btn>
       <!-- <v-btn v-else class="mr-2" depressed disabled >ยืนยัน</v-btn> -->
       </v-col>
     </v-row>
</v-container>
</template>
<script>
import THBText from 'thai-baht-text'
import { Encode } from '@/services'
export default {
  components: {
    PurchaseOrderModal: () => import('@/components/Modal/PurchaseOrderModal'),
    uploadImg: () => import('@/components/library/updateImg'),
    selectCopons: () => import('./couPon')
  },
  data () {
    return {
      DetailProduct: [{ description: '', quantity: '', unit: '', cost: '' }],
      overlay: true,
      imgShow: false,
      chkBtn: [],
      chkForm: true,
      user_role: '',
      Rules: {
        number: [
          v => !!v || 'กรุณากรอกค่าจัดส่ง'
        ]
      },
      itemSelect: [
        { id: 1, name: 'ชำระทันที', name_eng: '' },
        { id: 2, name: 'เครดิตเทอม', name_eng: 'Credit Term' }
      ],
      itemSelectInstallments: [
        { id: 1, name: 1 },
        { id: 2, name: 2 },
        { id: 3, name: 3 },
        { id: 4, name: 4 },
        { id: 5, name: 5 },
        { id: 6, name: 6 },
        { id: 7, name: 7 },
        { id: 8, name: 8 },
        { id: 9, name: 9 },
        { id: 10, name: 10 },
        { id: 11, name: 11 },
        { id: 12, name: 12 }
      ],
      formData: {
        customerName: '',
        detailsProduct: [
          { description: '', quantity: '', unit: '', cost: '' }
        ],
        address: '',
        taxpayerIdentificationNo: '',
        quotationNumber: '',
        date: '',
        credit: '',
        signaturePersonality1: '',
        signaturePersonality2: '',
        signaturePersonality3: ''
      },
      CHKproductList: true,
      discountAll: '',
      couponId: '',
      unitCP: '',
      points: '',
      quotationModel: false,
      quotationId: '',
      loading: false,
      loading2: false,
      roleQuShop: '',
      dataForm: {},
      numOfCreditTerm: '',
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      menu: false,
      modal: false,
      menu1: false,
      dateStart: '',
      dateFormatted: '',
      btnShipping: true,
      itemShipping: '',
      something: '',
      UserRoleSet: {
        buyer_name: false,
        address: false,
        tax_id: false,
        dates: false,
        qu_number: false,
        payment_method: false,
        credit_term: false,
        total_shipping: false,
        buyer_name_by: false,
        prepared_by: false,
        approve_by: false,
        remark: false,
        logo: false,
        couponAndpoint: false,
        installment: false,
        btnAdd: false,
        btnRemove: false,
        quantity: false,
        thText: false
      }
    }
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.user_role = this.$route.query.role
    this.quotationId = this.$route.query.qu_id
    if (this.user_role === 'purchaser') {
      this.$EventBus.$emit('changeNavCompany')
      this.$EventBus.$emit('chackAuthority')
    } else {
      this.$EventBus.$emit('changeNav')
      this.$EventBus.$emit('AuthorityUsers')
    }
    this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon = []
    this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point = []
  },
  destroyed () {
  },
  mounted () {
    this.quotationDetail.date = this.dateTime
    console.log('quotationId', this.quotationDetail)
    if (this.quotationDetail.product_list.length === 0) {
      this.CHKproductList = false
    }
    if (this.user_role === 'purchaser') {
      if (this.quotationDetail.status === 'active' && this.quotationDetail.status_use_discount === 'yes') {
        this.UserRoleSet.couponAndpoint = true
      } else {
        this.UserRoleSet.buyer_name = true
        this.UserRoleSet.payment_method = true
        this.UserRoleSet.buyer_name_by = true
        this.UserRoleSet.dates = true
        // this.UserRoleSet.couponAndpoint = true
        this.UserRoleSet.installment = true
        this.UserRoleSet.btnAdd = true
        this.UserRoleSet.btnRemove = true
        this.UserRoleSet.quantity = true
      }
    } else {
      this.UserRoleSet.payment_method = true
      this.UserRoleSet.logo = true
      this.UserRoleSet.qu_number = true
      this.UserRoleSet.dates = true
      this.UserRoleSet.total_shipping = true
      this.UserRoleSet.remark = true
      this.UserRoleSet.prepared_by = true
      this.UserRoleSet.approve_by = true
      // this.UserRoleSet.couponAndpoint = true
      this.UserRoleSet.installment = true
      this.UserRoleSet.btnAdd = true
      this.UserRoleSet.btnRemove = true
      this.UserRoleSet.quantity = true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    excludingVat () {
      return this.quotationDetail.product_list.reduce((a, c) => { return a + Number((Math.abs(c.quantity) * c.price) || 0) }, 0)
    },
    includingVat () {
      return this.excludingVat + ((this.excludingVat * 7) / 100)
    },
    Vat () {
      return parseFloat(this.includingVat - this.excludingVat).toFixed(2)
    },
    thaiIncludingVat () {
      return THBText(this.excludingVat + parseFloat(this.quotationDetail.total_shipping !== '' ? this.quotationDetail.total_shipping : 0))
    },
    addressCompany () {
      return this.$store.state.ModuleAdminManage.QuotationformData.business_detail
    },
    productShop () {
      return this.$store.state.ModuleAdminManage.QuotationformData.product_of_shop
    },
    quotationDetail () {
      return this.$store.state.ModuleAdminManage.QuotationformData.qu_detail
    },
    checkStock () {
      return this.quotationDetail.product_list.map(x => { return { quantity: Math.abs(x.quantity), stock: x.stock } })
    },
    checkButton () {
      return this.quotationDetail.product_list.map(x => { return { quantity: Math.abs(x.quantity) } })
    },
    dateTH () {
      return new Date(this.quotationDetail.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
    },
    convertDate () {
      const [year, month, day] = this.quotationDetail.date.split('-')
      const [day2] = day.split(' ')
      // console.log(day2)
      return `${day2}-${month}-${year}`
    },
    dateTime () {
      // console.log('Date--++--->', this.convertDate)
      return this.convertDate
    },
    computedDateFormatted () {
      return this.formatDate(this.quotationDetail.date)
    }
    // selectedData () {
    //   return this.itemSelect.find(
    //     x => x.name === this.quotationDetail.payment_method || x.name_eng === this.quotationDetail.payment_method
    //   )
    // }
  },
  watch: {
    'quotationDetail.payment_method' (e) {
      // console.log(e)
      if (e === 'ชำระทันที') {
        this.quotationDetail.credit_term = 0
      } else {
        this.quotationDetail.credit_term = this.quotationDetail.credit_term_default
      }
    },
    'quotationDetail.product_list' (list) {
      if (list.length !== 0 && list.every(x => x.quantity !== '' && x.quantity > 0)) {
        // for (const i in list) {
        //   if (isNaN(list[i].quantity) && list[i].status === '0') {
        //     this.CHKproductList = false
        //   } else {
        //     this.CHKproductList = true
        //   }
        // }
        this.CHKproductList = true
      } else {
        this.CHKproductList = false
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/quotationMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/purchaseOrder' }).catch(() => {})
      }
    },
    dateTime (val) {
      this.dateFormatted = this.formatDate(this.quotationDetail.date)
    },
    checkStock (val) {
      for (const a in val) {
        this.quotationDetail.product_list[a].quantity = Math.abs(val[a].quantity)
      }
    },
    checkButton (val) {
      this.btnShipping = val.every(x => x.quantity !== '' && x.quantity > 0)
    }
    // dateTime (e) {
    //   return new Date(e).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
    // }
  },
  methods: {
    formatDate (date) {
      if (!date) return null
      const dates = new Date(date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
      return `${dates}`
    },
    afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [day, month, year] = date.split('-')
      return `${year}-${month}-${day}`
    },
    dateTimeIH (e) {
      return new Date(this.quotationDetail.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
    },
    formatTofixed (val) {
      return val.toFixed(2)
    },
    editCoupon () {
      this.$store.state.ModuleMyCouponsPoints.stateCouponModel = true
    },
    removeCoupon () {
      this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon = []
      this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point = []
    },
    async getCoupon () {
      await this.$store.commit('openLoader')
      var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
      const dataPoint = {
        qu_id: FormsAll.qu_detail.id,
        seller_shop_id: FormsAll.qu_detail.seller_shop_id,
        role_user: this.user_role,
        company_id: this.user_role === 'ext_buyer' ? '-1' : FormsAll.qu_detail.company_id,
        special_price_id: '-1',
        net_price: FormsAll.qu_detail.total_price_no_vat
      }
      await this.$store.dispatch('actionsGetPointsWithCart', dataPoint)
      var { data: { points = {} } } = await this.$store.state.ModuleMyCouponsPoints.stateGetPointsWithCart
      this.$store.state.ModuleMyCouponsPoints.getPointQu = await points
      // console.log('point', points)
      const dataCoupon = {
        qu_id: FormsAll.qu_detail.id,
        seller_shop_id: FormsAll.qu_detail.seller_shop_id,
        role_user: this.user_role,
        company_id: this.user_role === 'ext_buyer' ? '-1' : FormsAll.qu_detail.company_id,
        special_price_id: '-1',
        net_price: FormsAll.qu_detail.total_price_no_vat
      }
      await this.$store.dispatch('actionsGetCouponCart', dataCoupon)
      var { data: { coupon = [] } } = await this.$store.state.ModuleMyCouponsPoints.stateGetCouponCart
      this.$store.state.ModuleMyCouponsPoints.getCouponQu = await coupon.map(e => {
        return {
          image: e.couponImagePath,
          name: e.couponName,
          description: e.couponDescription,
          couponDate: {
            useStartDate: e.useStartDate,
            useEndDate: e.useEndDate
          },
          couponId: e.couponId,
          real_discount: e.real_discount,
          discount: e.discount,
          shop_name: e.shop_name
        }
      })
      await this.$store.commit('closeLoader')
      this.$store.state.ModuleMyCouponsPoints.stateData = await dataCoupon
      this.$store.state.ModuleMyCouponsPoints.stateCouponModel = await true
    },
    backtoList () {
      if (this.quotationId === '-1') {
        this.$router.push({ path: '/checkout' }).catch(() => { })
      } else {
        // console.log('stateURLQu', this.$store.state.ModuleAdminManage.stateURLQu)
        this.$router.push({ path: `${this.$store.state.ModuleAdminManage.stateURLQu}` }).catch(() => { })
      }
    },
    async shippingCost () {
      this.unitCP = ''
      if (this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon.length !== 0) {
        this.discountAll = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].real_discount
        this.couponId = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].couponId
        this.unitCP = await 'คูปอง'
      } else if (this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point.length !== 0) {
        this.discountAll = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].maxUse
        this.points = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].maxUse
        this.unitCP = await 'คะแนน'
      } else {
        this.discountAll = await ''
        this.couponId = await ''
        this.points = await ''
      }
      this.loading2 = true
      this.quotationModel = true
      var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
      // console.log('qu_detail.id', FormsAll.qu_detail.id)
      const dataForm = {
        role_user: this.user_role,
        qu_id: FormsAll.qu_detail.id,
        total_discount: this.discountAll,
        seller_shop_id: FormsAll.qu_detail.seller_shop_id,
        company_id: FormsAll.qu_detail.company_id,
        product_list: FormsAll.qu_detail.product_list.map(e => {
          return {
            product_id: e.product_id,
            attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
            quantity: e.quantity,
            price: e.price
          }
        })
      }
      await this.$store.dispatch('actionsEstimateQu', dataForm)
      var { result = '', data = {}, message = '' } = await this.$store.state.ModuleAdminManage.stateEstimateQu
      if (result === 'SUCCESS') {
        this.itemShipping = data
        this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.total_shipping = data.total_shipping
        this.loading2 = false
        this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon = []
        this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point = []
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1800, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' + message })
        this.quotationModel = false
        this.loading2 = false
        this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon = []
        this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point = []
      }
      // console.log(result, data)
    },
    async shippingCostSeller () {
      this.loading = true
      this.unitCP = ''
      if (this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon.length !== 0) {
        this.discountAll = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].real_discount
        this.couponId = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].couponId
        this.unitCP = await 'คูปอง'
      } else if (this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point.length !== 0) {
        this.discountAll = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].amount
        this.points = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].amount
        this.unitCP = await 'คะแนน'
      } else {
        this.discountAll = await ''
        this.couponId = await ''
        this.points = await ''
      }
      var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
      // console.log('qu_detail.id22', FormsAll.qu_detail.id)
      const dataForm = {
        role_user: this.user_role,
        qu_id: '-1',
        total_discount: this.discountAll,
        seller_shop_id: FormsAll.qu_detail.seller_shop_id,
        company_id: FormsAll.qu_detail.company_id,
        product_list: FormsAll.qu_detail.product_list.map(e => {
          return {
            product_id: e.product_id,
            attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
            have_attribute: e.product_attribute_detail.product_attribute_id === null ? 'no' : 'yes',
            quantity: e.quantity,
            price: e.price
          }
        })
      }
      await this.$store.dispatch('actionsEstimateQu', dataForm)
      var { result = '', data = {}, message = '' } = await this.$store.state.ModuleAdminManage.stateEstimateQu
      if (result === 'SUCCESS') {
        this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.total_shipping = data.total_shipping
        this.loading = false
      } else if (result === 'FAILED' && message === 'ข้อมูลไม่ครบ [หมายเหตุ] กรุณาตรวจสอบและลองใหม่อีกครั้ง') {
        this.$swal.fire({ showConfirmButton: false, timer: 1800, timerProgressBar: true, icon: 'error', html: '<h3>ข้อมูลไม่ครบ</h3>' + 'กรุณากรอกหมายเหตุให้ครบ' })
        this.loading = false
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1800, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' + message })
        this.loading = false
      }
      // console.log(result, data)
    },
    chkStock (el) {
      // console.log('ไม่ตรง', el)
    },
    onChange () {
      if (this.$refs.pictureInput.image) {
        // console.log('Picture loaded.', this.$refs.pictureInput.image)
      } else {
        // console.log('FileReader API not supported: use the <form>, Luke!')
      }
    },
    async openModalPurchaseOrder (index) {
      // console.log('CheckData', Number.isInteger('55bn'))
      this.$store.state.ModuleShop.openModalPurchaseOrder = await true
      await this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.splice(index, 1)
      // this.$store.state.ModuleShop.stateIndexAddProduct = await index
    },
    addRow () {
      // for ( var i = 0; i < a.length; i++ ) {
      //   for ( var e = 0; e < b.length; e++ ) {
      //       if ( a[i] === b[e] ) matches.push( a[i] );
      //   }
      // }
      this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.push({ status: '0' })
      this.$store.state.ModuleAdminManage.ListProductOfShop = this.productShop
    },
    deleteRow (index) {
      this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.splice(index, 1)
    },
    async postFormData () {
      if (this.quotationDetail.total_shipping !== '') {
        this.loading = true
        var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
        if (this.quotationId !== '-1') {
          this.dataForm = await {
            role_user: this.user_role,
            num_of_credit_term: this.quotationDetail.payment_method === 'เครดิตเทอม' ? this.quotationDetail.num_of_credit_term : 1,
            total_discount: this.discountAll,
            coupon_id: this.couponId,
            point: this.points,
            qu_id: this.quotationId === '0' ? '' : this.quotationId,
            logo_path: this.$store.state.ModuleAdminManage.stateImgQu !== '' ? this.$store.state.ModuleAdminManage.stateImgQu : FormsAll.qu_detail.logo_path,
            seller_shop_id: FormsAll.qu_detail.seller_shop_id,
            company_id: FormsAll.qu_detail.company_id,
            com_perm_id: FormsAll.qu_detail.com_perm_id,
            qu_number: FormsAll.qu_detail.qu_number,
            company_name: FormsAll.qu_detail.company_name,
            address: FormsAll.qu_detail.address,
            tax_id: FormsAll.qu_detail.tax_id,
            date: this.afterDate(FormsAll.qu_detail.date),
            payment_method: FormsAll.qu_detail.payment_method,
            credit_term: FormsAll.qu_detail.credit_term,
            buyer_name: FormsAll.qu_detail.buyer_name,
            prepared_by: FormsAll.qu_detail.prepared_by,
            approve_by: FormsAll.qu_detail.approve_by,
            total_shipping: FormsAll.qu_detail.total_shipping,
            note: FormsAll.qu_detail.note,
            product_list: FormsAll.qu_detail.product_list.length !== 0 ? FormsAll.qu_detail.product_list.map(e => {
              return {
                product_id: e.product_id,
                attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
                quantity: e.quantity,
                price: e.price
              }
            }) : []
          }
        } else {
          this.dataForm = await {
            role_user: this.user_role,
            num_of_credit_term: this.quotationDetail.payment_method === 'เครดิตเทอม' ? this.quotationDetail.num_of_credit_term : 1,
            total_discount: this.discountAll,
            coupon_id: this.couponId,
            point: this.points,
            qu_id: '',
            logo_path: this.$store.state.ModuleAdminManage.stateImgQu !== '' ? this.$store.state.ModuleAdminManage.stateImgQu : FormsAll.qu_detail.logo_path,
            seller_shop_id: FormsAll.qu_detail.seller_shop_id,
            company_id: FormsAll.qu_detail.company_id,
            com_perm_id: FormsAll.qu_detail.com_perm_id,
            qu_number: FormsAll.qu_detail.qu_number,
            company_name: FormsAll.qu_detail.company_name,
            address: FormsAll.qu_detail.address,
            tax_id: FormsAll.qu_detail.tax_id,
            date: this.afterDate(FormsAll.qu_detail.date),
            payment_method: FormsAll.qu_detail.payment_method,
            credit_term: FormsAll.qu_detail.credit_term,
            buyer_name: FormsAll.qu_detail.buyer_name,
            prepared_by: FormsAll.qu_detail.prepared_by,
            approve_by: FormsAll.qu_detail.approve_by,
            total_shipping: FormsAll.qu_detail.total_shipping,
            note: FormsAll.qu_detail.note,
            product_list: FormsAll.qu_detail.product_list.length !== 0 ? FormsAll.qu_detail.product_list.map(e => {
              return {
                product_id: e.product_id,
                attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
                quantity: e.quantity,
                price: e.price
              }
            }) : []
            // product_list: FormsAll.qu_detail.product_list
          }
        }
        const dataFormPurchaser = {
          address_id: '',
          budget_start_date: null,
          business_id: null,
          code: FormsAll.business_detail.business_code,
          company_address_id: 0,
          created_at: FormsAll.business_detail.created_at,
          created_by: FormsAll.business_detail.created_by,
          credit: 0,
          customer_code: null,
          customer_type_id: null,
          discount_percent: FormsAll.qu_detail.product_list.find(x => x.discount_percent),
          fax: FormsAll.business_detail.fax_no,
          fix_price_end_date: '',
          fix_price_start_date: '',
          id: FormsAll.qu_detail.company_id,
          img_path: '',
          name_en: FormsAll.business_detail.first_name_eng,
          name_th: FormsAll.business_detail.first_name_th,
          phone: FormsAll.business_detail.mobile_no,
          status: '',
          tax_id: FormsAll.qu_detail.tax_id,
          tel: null,
          type_budget: null,
          type_budget_roll_over: '',
          type_level: '',
          type_price: '',
          type_promotion: '',
          type_shipping: '',
          updated_at: '',
          updated_by: ''
        }
        // console.log('QuotationformData', this.dataForm, 'QW =>', dataFormPurchaser)
        const qp = await FormsAll.qu_detail.product_list.every(x => x.quantity !== '' && x.quantity > 0)
        const q = await [FormsAll.qu_detail.buyer_name !== '' && FormsAll.qu_detail.qu_number !== ''].includes(true)
        // console.log('QQQQ', q, qp)
        if (q && qp) {
          // console.log('QQ', qp)
          await this.$store.dispatch('actionsEditQuotation', this.dataForm)
          var { result = '', message = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQuotation
          // console.log('{DataFrom}', data, result)
          this.loading = false
          if (this.user_role === 'seller' && result === 'SUCCESS') {
            await this.$router.push({ path: `/QuotationDetail?QUNumber=${data.qu_id}&id=${FormsAll.qu_detail.seller_shop_id}&comID=${FormsAll.qu_detail.company_id}` }).catch(() => { })
          } else if (this.user_role === 'purchaser' && result === 'SUCCESS') {
            // console.log('qu_id', data.qu_id, 'ID', FormsAll.qu_detail.company_id, 'shopID', FormsAll.qu_detail.company_id)
            await localStorage.setItem('dataFormPurchaser', Encode.encode(dataFormPurchaser))
            await this.$router.push({ path: `/QUCompanyDetail?QU_ID=${data.qu_id}&id=${FormsAll.qu_detail.company_id}&shopID=${FormsAll.qu_detail.seller_shop_id}` }).catch(() => { })
          } else if (this.user_role === 'purchaser' && result === 'FAILED' && message === 'quantity should be than 0.') {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกจำนวนสินค้า</h3>' })
          } else if (this.user_role === 'purchaser' && result === 'FAILED' && message === 'Data missing. Please check your ["product_list"] and try again.') {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณาตรวจสอบรายการสินค้าของคุณ</h3>' })
          } else {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' + message })
          }
        } else {
          this.loading = false
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกข้อมูลให้ครบ</h3>' })
        }
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกข้อมูลค่าจัดส่ง</h3>' })
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import './quMobile.css';
@import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@100;200&display=swap');
.logo-qu {
  background-image: url("data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%201320%20300%22%3E%0A%09%3Ctext%20x%3D%2250%25%22%20y%3D%2250%25%22%20dy%3D%22.35em%22%20text-anchor%3D%22middle%22%3E%0A%09%09LOGO%0A%09%3C%2Ftext%3E%0A%3C%2Fsvg%3E%09");
  background-repeat: no-repeat;
}
::v-deep .v-messages__message {
  font-size: 8px;
}
::v-deep .theme--light.v-icon:focus::after {
  opacity: 0;
}
::v-deep #char1 .v-text-field {
  width: 25px;
}
::v-deep.v-text-field.v-text-field--solo .v-input__control {
  min-height: 0px;
  padding: 0;
}

::v-deep.v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
  min-height: 38px;
}
::v-deep.v-input__control {
    display: flex;
    /* flex-direction: column; */
    /* height: auto; */
    /* flex-grow: 1; */
    /* flex-wrap: wrap; */
    /* min-width: 0; */
    width: 100%;
}
.center-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
svg {
  font-family: 'Russo One', sans-serif;
  position: absolute;
  width: 100%; height: 100%;
  border-radius: 50%;
}
svg text {
  text-transform: uppercase;
  animation: stroke 5s infinite alternate;
  stroke-width: 2;
  stroke: #365fa0;
  font-size: 140px;
}
@keyframes stroke {
  0%   {
    fill: rgba(72,138,20,0); stroke: rgba(54,95,160,1);
    stroke-dashoffset: 25%; stroke-dasharray: 0 50%; stroke-width: 2;
  }
  70%  {fill: rgba(72,138,20,0); stroke: rgba(54,95,160,1); }
  80%  {fill: rgba(72,138,20,0); stroke: rgba(54,95,160,1); stroke-width: 3; }
  100% {
    fill: rgba(72,138,204,1); stroke: rgba(54,95,160,0);
    stroke-dashoffset: -25%; stroke-dasharray: 50% 0; stroke-width: 0;
  }
}
</style>
