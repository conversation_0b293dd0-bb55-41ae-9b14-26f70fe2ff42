<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
     <v-row :class="MobileSize ? 'pt-0' : ''">
        <v-col cols="12" md="6" :class="MobileSize ? 'pt-0' : IpadSize ? 'pl-0' : ''">
          <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รายงานรายจ่าย</v-card-title>
          <v-card-title class="mt-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333; margin-left: -25px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>รายงานรายจ่าย</v-card-title>
        </v-col>
        <v-col cols="12" md="6" v-if="!MobileSize">
        </v-col>
      </v-row>
      <div class="cs">
        <v-row>
          <v-col cols="12" md="6" sm="4" align="right">
            <div style="padding-top: 1.6em;font-size: 12px;">เริ่มต้น - สิ้นสุด</div>
          </v-col>
        <v-col
          cols="12"
          md="3"
          sm="4"
          lg="2"
        >
          <v-menu
            ref="menu1"
            v-model="menu1"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            max-width="290px"
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="dateFormatted"
                label=""
                hint=""
                persistent-hint
                append-icon="mdi-calendar"
                dense
                outlined
                class="mt-2"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="dateStart"
              no-title
              :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
              @input="menu1 = false"
              @change="changeDate"
            ></v-date-picker>
          </v-menu>
        </v-col>

        <v-col
          cols="12"
          md="3"
          sm="4"
          lg="2"
        >
          <v-menu
            v-model="menu2"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            max-width="290px"
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="dateFormatted2"
                label=""
                hint=""
                persistent-hint
                append-icon="mdi-calendar"
                dense
                outlined
                v-bind="attrs"
                v-on="on"
                class="mt-2"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="dateEnd"
              no-title
              :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
              :min="dateStart"
              @input="menu2 = false"
              @change="filterDate"
            ></v-date-picker>
          </v-menu>
        </v-col>
      </v-row>
      </div>
    </v-card>
  </v-container>
</template>
<script>
import dataTest from '../library/dataTest.json'
// import eventBus from '@/components/eventBus'
export default {
  data () {
    return {
      weekday: {},
      weekday2: {},
      weekdays: [
        {
          id: 2, name: '1 เดือน'
        },
        {
          id: 3, name: '6 เดือน'
        },
        {
          id: 4, name: '1 ปี'
        },
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      weekdays2: [
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      mode: '',
      type: [],
      types: [],
      modes: [],
      dataMain: dataTest,
      dateStart: '',
      dateEnd: '',
      dateFormatted: '',
      dateFormatted2: '',
      menu1: false,
      menu2: false,
      numChange: '1'
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$on('numChangeData', this.numChangeData)
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
  },
  destroyed () {
    this.$EventBus.$off('numChangeData')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ReportMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Report' }).catch(() => {})
      }
    },
    dateStart (val) {
      this.dateFormatted = this.formatDate(this.dateStart)
    },
    dateEnd (val) {
      this.dateFormatted2 = this.formatDate(this.dateEnd)
    }
  },
  methods: {
    backtoUserMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    changeDate () {
      this.dateEnd = ''
    },
    async numChangeData () {
      this.numChange = '0'
    },
    async filterDate () {
      var preStart = await this.afterDate(this.formatDate(this.dateStart))
      var preEnd = await this.afterDate(this.formatDate(this.dateEnd))
      const date = {
        start: preStart,
        end: preEnd
      }
      this.$EventBus.$emit('reloadDataTable', date)
      // console.log('filterDate**')
    },
    async filterOther () {
      const today = await new Date()
      if (this.weekday.id === 2) {
        const data = await {
          text: 'one_month',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 3) {
        var today2 = await new Date()
        var end = await ''
        if (((today2.getMonth() + 1) - 6) < 0) {
          await today2.setMonth((today2.getMonth() + 1) - 6)
          end = await today2
        } else {
          end = await today.setMonth(today.getMonth() - 6)
        }
        const data = await {
          text: 'six_months',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 4) {
        var today3 = await new Date()
        var end3 = await ''
        if (((today3.getMonth() + 1) - 12) < 0) {
          await today3.setMonth((today3.getMonth()) - 12)
          end3 = await today3
        } else {
          end3 = await today.setMonth(today.getMonth() - 12)
        }
        const data = await {
          text: 'one_year',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end3).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        // const data = await {
        //   text: 'one_year',
        //   start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
        //   end: new Date(today.setFullYear((today.getFullYear() + 1) - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        // }
        await this.EventBusCall(data)
      } else {
        const data = await {
          text: 'all'
        }
        await this.EventBusCall(data)
        await this.filterOther2()
      }
      // console.log('filterOther', new Date().toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }))
    },
    async filltWeek2 () {
      if (this.weekday2.id === 5) {
        const data = await {
          text: 'all'
        }
        this.numChange = await '1'
        await this.EventBusCall(data)
        await this.filterOther2()
      }
    },
    async EventBusCall (data) {
      await this.$EventBus.$emit('updateData', data)
    },
    async filterOther2 () {
      const num = await {
        number: '1'
      }
      await this.$EventBus.$emit('FuntionChange2', num)
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    parseDate (date) {
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${day}-${month}-${year}`
    },
    afterDate (date) {
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${year}-${month}-${day}`
    },
    timeStamp (Dates) {
      var myDate = Dates.split('-')
      var newDate = new Date(myDate[2], myDate[1] - 1, myDate[0])
      return newDate.getTime()
    },
    randomColor () {
      const randomColor = Math.floor(Math.random() * 16777215).toString(16)
      return `#${randomColor}`
    }
  }
}
</script>
