<template>
  <v-container class="pa-0">
    <v-row dense >
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >QT</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #587fe3;
                  font-size: 12px;
                "
              >
                {{formatNumber(docQT)}}
              </v-chip>
            </span>
            <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
            <p
            v-bind="attrs" v-on="on"
              style="
                color: #587fe3;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
              v-if="docRevenue > 9999"
            >
            THB {{formatNumber(docRevenue)}}
            </p>
            <p
            v-bind="attrs" v-on="on"
              style="
                color: #587fe3;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
              v-else
            >
            THB {{formatNumberTooltip(docRevenue)}}
            </p>
            </template>
            <span>{{ formatNumberTooltip(docRevenue) }} บาท</span>
            </v-tooltip>
          </div>
        </v-card>
      </v-col>
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >SO</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #f87956;
                  font-size: 12px;
                "
              >
              {{formatNumber(docSO)}}
              </v-chip>
            </span>
            <p
              style="
                color: #f87956;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
            >
              {{formatNumber(docSORevenue)}}
            </p>
          </div>
        </v-card>
      </v-col>
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >PR</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #67b7dc;
                  font-size: 12px;
                "
              >
                {{formatNumber(docPR)}}
              </v-chip>
            </span>
            <p
              style="
                color: #67b7dc;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
            >
              {{formatNumber(docPRRevenue)}}
            </p>
          </div>
        </v-card>
      </v-col>
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >PO</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #7d72b9;
                  font-size: 12px;
                "
              >
                {{formatNumber(docPO)}}
              </v-chip>
            </span>
            <p
              style="
                color: #7d72b9;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
            >
              {{formatNumber(docPORevenue)}}
            </p>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense>
      <v-col class="QT">
        <v-card class="pa-2 card" outlined>
          <div id="chart">
          <v-col>
            <v-row class="d-flex justify-space-between">
              <div style="height: 50px;">
                <p style="font-size:16px; padding-top:10px;">Summary Trend</p>
              </div>
              <div >
              <v-chip-group v-model="firstLine">
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('day')">วัน</v-chip>
                <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docLine('month')">เดือน</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('quarter')">ไตรมาส</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('halfyear')">ครึ่งปี</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
          </v-col>
          <!-- {{docOfLine}} -->
          <v-divider style="background:#67B7DC66"></v-divider>
            <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" v-if="isLoadLine === true">
              <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
            </div>
            <apexchart
              type="line"
              height="350"
              :options="optionLine.chartOptions"
              :series="seriesLine.series"
              v-else
            ></apexchart>
          <div style="color:red; margin-top:-22px" class="d-flex justify-end">
            * กราฟแสดงเฉพาะวันที่มี Transaction เท่านั้น
            </div>
          </div>
        </v-card>
      </v-col>
      <v-col class="QT">
        <v-card class="pa-2 card" outlined>
          <div id="chart">
          <v-col>
            <v-row class="d-flex justify-space-between">
              <div style="height: 50px;">
                <p style="font-size:16px; padding-top:10px;">จำนวนเอกสาร</p>
              </div>
              <div >
              <v-chip-group v-model="firstBar">
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('day')">วัน</v-chip>
                <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docBar('month')">เดือน</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('quarter')">ไตรมาส</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('halfyear')">ครึ่งปี</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
          </v-col>
          <!-- {{docOfBar}} -->
          <v-divider style="background:#67B7DC66"></v-divider>
            <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" v-if="isLoad === true">
              <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
            </div>
            <apexchart
              type="bar"
              height="350"
              :options="option.chartBarOptions"
              :series="series.seriesBar"
              v-else
            ></apexchart>
            <div style="color:red; margin-top:-22px" class="d-flex justify-end">
            * กราฟแสดงเฉพาะวันที่มี Transaction เท่านั้น
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense>
      <v-col class="QT">
        <v-card class="pa-2 card" outlined>
          <div id="chart">
            <v-col>
              <v-row class="d-flex justify-space-between">
                <div style="padding-left: 10px">
                  <p style="font-size:16px; padding-top:10px;">รายการสั่งซื้อสินค้า ( Top 5 )</p>
                </div>
                <div >
                <v-chip-group v-model="firstPie">
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('day')">วัน</v-chip>
                  <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docPie('month')">เดือน</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('quarter')">ไตรมาส</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('halfyear')">ครึ่งปี</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('year')">ปี</v-chip>
                  </v-chip-group>
                </div>
              </v-row>
            <v-divider style="background:#53535333; margin-top: 5px"></v-divider>
            </v-col>
            <v-col>
            <!-- <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" >
                <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
              </div> -->
              <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" v-if="isLoadPie === true">
                <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
              </div>
              <v-row style="display: flex; justify-content: center;" v-else>
                <v-col cols="12" md="6" sm="6" >
                  <apexchart type="donut" :options="optionPie.chartOptions" :series="seriesPie.series"></apexchart>
                </v-col>
                <v-col cols="12" md="6" sm="6" :style="MobileSize ? '' : 'margin-left:-40px'">
                  <v-data-table
                    :headers="headers"
                    :items="listTopFive"
                    style="background-color:transparent; height: 350px; width: 100%;"
                    no-data-text="ไม่มีรายการสั่งซื้อ"
                    :items-per-page="5"
                    :page="1"
                    hide-default-footer
                  >
                  <template v-slot:[`item.Revenue`]="{ item }">
                  {{formatNumber(item.Revenue)}}
                  </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </v-col>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
export default {
  name: 'ApexCharts',
  components: {
    apexchart: VueApexCharts
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    option () {
      if (this.docOfBar === 'day') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '30%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                this.currentDate
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'month') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '70%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              // categories: new Date(this.dataDateBar).toLocaleDateString('th-TH', { year: 'numeric', month: 'long' })
              categories: this.dataDateBar
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'quarter') {
        return {
          chartBarOptions: {
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '80%',
                endingShape: 'rounded'
              }
            },
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 380,
              toolbar: {
                show: false
              }
            },
            fill: {
              opacity: 1
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มีนาคม',
                'เมษายน - มิถุนายน',
                'กรกฎาคม - กันยายน',
                'ตุลาคม - ธันวาคม'
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'halfyear') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '50%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มิถุนายน',
                'กรกฎาคม - ธันวาคม'
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'year') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '80%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม',
                'กุมภาพันธ์',
                'มีนาคม',
                'เมษายน',
                'พฤษภาคม',
                'มิถุนายน',
                'กรกฎาคม',
                'สิงหาคม',
                'กันยายน',
                'ตุลาคม',
                'พฤศจิกายน',
                'ธันวาคม'
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else {
        return null
      }
    },
    optionLine () {
      if (this.docOfLine === 'day') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            // colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                this.currentDate
              ]
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'month') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: this.dataDate
              // {
              //   // data: this.dataDate
              //   data:
              //   '1',
              //   '2',
              //   '3'
              // },
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'quarter') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มีนาคม',
                'เมษายน - มิถุนายน',
                'กรกฎาคม - กันยายน',
                'ตุลาคม - ธันวาคม'
              ]
              // categories: this.dataDate
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'halfyear') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                // return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                return console.log(val)
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มิถุนายน',
                'กรกฎาคม - ธันวาคม'
              ]
              // {
              //   // data: this.dataDate
              //   data:
              //   '1',
              //   '2',
              //   '3'
              // },
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'year') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              // categories: this.dataDate
              categories: [
                'มกราคม',
                'กุมภาพันธ์',
                'มีนาคม',
                'เมษายน',
                'พฤษภาคม',
                'มิถุนายน',
                'กรกฎาคม',
                'สิงหาคม',
                'กันยายน',
                'ตุลาคม',
                'พฤศจิกายน',
                'ธันวาคม'
              ]
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else {
        return null
      }
    },
    optionPie () {
      if (this.docOfPie === 'day') {
        if (this.dataRevenuePie === 0) {
          return {
            chartOptions: {
              legend: {
                position: 'bottom',
                horizontalAlign: 'center'
              },
              chart: {
                height: 350,
                type: 'donut',
                toolbar: {
                  show: false
                }
              },
              labels: {
                show: false
              }
            }
          }
        } else {
          return {
            chartOptions: {
              legend: {
                position: 'bottom',
                horizontalAlign: 'center'
              },
              chart: {
                height: 350,
                type: 'donut',
                toolbar: {
                  show: false
                }
              },
              labels: this.dataNamePie
            }
          }
        }
      } else if (this.docOfPie === 'month') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else if (this.docOfPie === 'quarter') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else if (this.docOfPie === 'halfyear') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else if (this.docOfPie === 'year') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else {
        return null
      }
    },
    series () {
      if (this.docOfBar === 'day') {
      // กราฟแท่ง
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'month') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'quarter') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'halfyear') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'year') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else {
        return {
          seriesBar: [],
          seriesLine: [],
          seriesPie: []
        }
      }
    },
    seriesLine () {
      if (this.docOfLine === 'day') {
      // กราฟเส้น
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'month') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              // data: [12, 23, 59],
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'quarter') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'halfyear') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'year') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else {
        return {
          seriesLine: [],
          seriesBar: [],
          seriesPie: []
        }
      }
    },
    seriesPie () {
      if (this.docOfPie === 'day') {
        if (this.dataRevenuePie === 0) {
          return {
            series: [0]
          }
        } else {
          return {
            series: this.dataRevenuePie
          }
        }
      } else if (this.docOfPie === 'month') {
        return {
          series: this.dataRevenuePie
        }
      } else if (this.docOfPie === 'quarter') {
        return {
          series: this.dataRevenuePie
        }
      } else if (this.docOfPie === 'halfyear') {
        return {
          series: this.dataRevenuePie
        }
      } else if (this.docOfPie === 'year') {
        return {
          series: this.dataRevenuePie
        }
      } else {
        return {
          seriesLine: [],
          seriesBar: [],
          seriesPie: []
        }
      }
    }
  },
  data () {
    return {
      isLoad: false,
      isLoadLine: false,
      isLoadPie: false,
      dataDate: [],
      dataRevenue: [],
      dataDateBar: [],
      dataRevenueBar: [],
      dataDatePie: [],
      dataNamePie: [],
      dataRevenuePie: [],
      dataDateSOBar: [],
      dataRevenueSOBar: [],
      dataDatePRBar: [],
      dataRevenuePRBar: [],
      dataDatePOBar: [],
      dataRevenuePOBar: [],
      listTopFive: [],
      nameTopFive: '',
      qtTopFive: '',
      revenueTopFive: '',
      setOfData: '',
      currentDate: '',
      currentMonth: '',
      docOfBar: 'month',
      docOfLine: 'month',
      docOfPie: 'month',
      firstBar: 'month',
      firstLine: 'month',
      firstPie: 'month',
      sellerShopId: '',
      docQT: 0,
      docRevenue: 0,
      docSO: 0,
      docSORevenue: 0,
      docPR: 0,
      docPRRevenue: 0,
      docPO: 0,
      docPORevenue: 0,
      currentPage: 1,
      // docOfMonth: 'month',
      // docOfQuarter: 'quarter',
      // docOfHalfYear: 'halfYear',
      // docOfYear: 'year',
      headers: [
        {
          text: 'Customer Name',
          align: 'center',
          sortable: false,
          value: 'name'
        },
        { text: 'จำนวนรายการ', sortable: false, align: 'center', value: 'numDoc' },
        { text: 'Amount', sortable: false, align: 'center', value: 'Revenue' }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$store.commit('openLoader')
    // this.isLoad = true
    this.sellerShopId = localStorage.getItem('shopSellerID')
    this.getQT()
    this.getSumTrend()
    this.getSumDocQT()
    this.getSumDocSO()
    this.getSumDocPR()
    this.getSumDocPO()
    this.getSumTopFiveQT()
    this.$store.commit('closeLoader')
    // console.log(this.optionPie.chartOptions.chart)
    // this.setData()
    // this.setDate()
    // this.setMonth()
    // console.log('sellerShopId', this.sellerShopId)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    docOfBar: async function (val) {
      this.isLoad = true
      await this.getSumDocQT(val)
      await this.getSumDocSO(val)
      await this.getSumDocPR(val)
      await this.getSumDocPO(val)
      this.isLoad = false
    },
    docOfLine: async function (val) {
      this.isLoadLine = true
      await this.getSumTrend(val)
      this.isLoadLine = false
    },
    docOfPie: async function (val) {
      this.isLoadPie = true
      await this.getSumTopFiveQT(val)
      this.isLoadPie = false
    }
  },
  methods: {
    setDate () {
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'Asia/Bangkok'
      }
      var date = new Date().toLocaleDateString(undefined, options)
      this.currentDate = date
      // var date = new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
      // this.currentDate = date.getDate() + '/' + (date.getMonth() + 1) + '/' + date.getFullYear()
      // console.log(this.currentDate)
      // console.log(date)
    },
    setMonth () {
      var date = new Date()
      var month = date.toLocaleString('default', { month: 'long' })
      this.currentMonth = month
      // console.log(this.currentMonth)
    },
    docLine (actionKey) {
      if (actionKey === 'day') {
        this.docOfLine = 'day'
        this.setDate()
      } else if (actionKey === 'month') {
        this.docOfLine = 'month'
      } else if (actionKey === 'quarter') {
        this.docOfLine = 'quarter'
      } else if (actionKey === 'halfyear') {
        this.docOfLine = 'halfyear'
      } else if (actionKey === 'year') {
        this.docOfLine = 'year'
      }
    },
    docBar (actionKey) {
      if (actionKey === 'day') {
        this.docOfBar = 'day'
        this.setDate()
        // console.log(this.docOfBar)
      } else if (actionKey === 'month') {
        this.docOfBar = 'month'
      } else if (actionKey === 'quarter') {
        this.docOfBar = 'quarter'
      } else if (actionKey === 'halfyear') {
        this.docOfBar = 'halfyear'
      } else if (actionKey === 'year') {
        this.docOfBar = 'year'
      }
    },
    docPie (actionKey) {
      if (actionKey === 'day') {
        this.docOfPie = 'day'
      } else if (actionKey === 'month') {
        this.docOfPie = 'month'
      } else if (actionKey === 'quarter') {
        this.docOfPie = 'quarter'
      } else if (actionKey === 'halfyear') {
        this.docOfPie = 'halfyear'
      } else if (actionKey === 'year') {
        this.docOfPie = 'year'
      }
    },
    // async addLoadBar () {
    //   this.isLoad = true
    // },
    async getQT () {
      // console.log('QT')
      // actionsGetQTDashBoard
      var item = {
        seller_shop_id: this.sellerShopId
      }
      await this.$store.dispatch('actionsGetQTDashBoard', item)
      var response = await this.$store.state.ModuleShop.stateGetQTDashBoard
      if (response.ok === 'y') {
        // console.log('QT', response.query_result[0][0])
        // console.log('SO', response.query_result[0][1])
        // console.log('PO', response.query_result[0][2])
        // console.log('PR', response.query_result[0][3])
        for (var i = 0; i < response.query_result[0].length; i++) {
          if (response.query_result[0][i].type === 'QT') {
            this.docQT = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docRevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          } else if (response.query_result[0][i].type === 'SO') {
            this.docSO = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docSORevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          } else if (response.query_result[0][i].type === 'PO') {
            this.docPO = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docPORevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          } else if (response.query_result[0][i].type === 'PR') {
            this.docPR = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docPRRevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          }
        }
        // this.listQT = response.query_result[0]
        // for (const item of response.query_result[0]) {
        //   console.log('item------->', item)
        //   // this.docQT = item.sumDocumentQT
        //   // this.docRevenue = item.sumRevenueQT
        // }
        // this.docQT = response.query_result[0].sumDocumentQT
        // this.docRevenue = response.query_result[0].sumRevenueQT
      } else {
        this.docQT = 0
        this.docRevenue = 0
        this.docSO = 0
        this.docSORevenue = 0
        this.docPO = 0
        this.docPORevenue = 0
        this.docPR = 0
        this.docPRRevenue = 0
      }
    },
    async getSumTrend (val) {
      this.dataDate = []
      this.dataRevenue = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerShopId,
          type: val
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopId,
          type: val
        }
      }
      await this.$store.dispatch('actionsGetSumTrendDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumTrendDashBoard
      this.setOfData = await response.query_result.data
      if (this.setOfData.length !== 0) {
        for (const item of this.setOfData) {
          this.dataDate.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.Revenue === undefined || item.Revenue === null) {
            item.Revenue = 0
            this.dataRevenue.push(item.Revenue)
          } else {
            this.dataRevenue.push(item.Revenue)
            // console.log(this.dataRevenue)
          }
          // console.log('item------->', this.dataDate, this.dataRevenue)
        }
      } else {
        this.dataDate.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenue = []
      }
    },
    async getSumDocQT (val) {
      // this.isLoad = true
      var setOfData = []
      this.dataDateBar = []
      this.dataRevenueBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocQTDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocQTDashBoard
      // console.log('response QT------->', response.query_result.data)
      setOfData = await response.query_result.data
      if (setOfData.length !== 0) {
        for (const item of setOfData) {
          this.dataDateBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenueBar.push(item.numDoc)
          } else {
            this.dataRevenueBar.push(item.numDoc)
          }
          // console.log('item QT------->' + val, this.dataDateBar, this.dataRevenueBar)
        }
        // this.isLoad = false
      } else {
        this.dataDateBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenueBar = []
        // this.isLoad = false
      }
    },
    async getSumDocSO (val) {
      var setOfDataSO = []
      this.dataDateSOBar = []
      this.dataRevenueSOBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocSODashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocSODashBoard
      setOfDataSO = await response.query_result.data
      // console.log('setOfData SO------->', setOfDataSO)
      if (setOfDataSO.length !== 0) {
        for (const item of setOfDataSO) {
          this.dataDateSOBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenueSOBar.push(item.numDoc)
          } else {
            this.dataRevenueSOBar.push(item.numDoc)
          }
          // console.log('item SO------->' + val, this.dataDateSOBar, this.dataRevenueSOBar)
        }
      } else {
        this.dataDateSOBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenueSOBar = []
      }
    },
    async getSumDocPR (val) {
      var setOfDataPR = []
      this.dataDatePRBar = []
      this.dataRevenuePRBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocPRDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocPRDashBoard
      setOfDataPR = await response.query_result.data
      // console.log('setOfData PR------->', setOfDataPR)
      if (setOfDataPR.length !== 0) {
        for (const item of setOfDataPR) {
          this.dataDatePRBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenuePRBar.push(item.numDoc)
          } else {
            this.dataRevenuePRBar.push(item.numDoc)
          }
          // console.log('item PR------->' + val, this.dataDatePRBar, this.dataRevenuePRBar)
        }
      } else {
        this.dataDatePRBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenuePRBar = []
      }
    },
    async getSumDocPO (val) {
      var setOfDataPO = []
      this.dataDatePOBar = []
      this.dataRevenuePOBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopId,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocPODashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocPODashBoard
      // console.log('response PO------->', response.query_result.data)
      setOfDataPO = await response.query_result.data
      if (setOfDataPO.length !== 0) {
        for (const item of setOfDataPO) {
        // console.log('item------->', item)
          this.dataDatePOBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenuePOBar.push(item.numDoc)
          } else {
            this.dataRevenuePOBar.push(item.numDoc)
          }
          // console.log('item------->' + val, this.dataDatePOBar, this.dataRevenuePOBar)
        }
      } else {
        this.dataDatePOBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenuePOBar = []
      }
    },
    async getSumTopFiveQT (val) {
      var setOfData = []
      this.dataNamePie = []
      this.dataRevenuePie = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerShopId,
          type: val
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopId,
          type: val
        }
      }
      await this.$store.dispatch('actionsGetSumTopFiveQTDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumTopFiveQTDashBoard
      setOfData = await response.query_result.data
      this.listTopFive = setOfData
      // console.log('listTopFive', this.listTopFive)
      if (setOfData.length !== 0) {
        for (const item of setOfData) {
          // this.nameTopFive = item.name
          // this.qtTopFive = item.numDoc
          // this.revenueTopFive = item.Revenue
          // console.log('item------->', this.nameTopFive, this.qtTopFive, this.revenueTopFive)
          this.dataRevenuePie.push(item.Revenue)
          this.dataNamePie.push(item.name)
          // console.log('dataPie', this.dataRevenuePie, this.dataNamePie)
        }
      } else {
        this.dataRevenuePie = []
        // console.log('dataRevenuePie = 0', this.dataRevenuePie)
      }
    },
    formatNumberTooltip (value) {
      if (typeof value !== 'undefined') {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      return ''
    },
    formatNumber (number) {
      const formattedNumber = new Intl.NumberFormat('en-US', {
        notation: 'compact',
        compactDisplay: 'short'
      }).format(number)
      return formattedNumber
    },
    formatter (value) {
      // console.log('value', value)
      if (value > 0) {
        if (value === Infinity) {
          return '1'
        } else {
          return Math.round(value)
        }
      } else {
        return '0'
      }
    }
  }
}
</script>

<style>
.QT .card {
  border-radius: 5px !important;
  background-color: #f5f5f5;
}
.custom-chip.v-chip--active {
  background-color: #757575 !important; /* สีเทาเข้ม */
  color: #ffffff !important; /* สีขาว */
}
</style>
