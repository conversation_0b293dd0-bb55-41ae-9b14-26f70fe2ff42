import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async getSummaryOrder (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/dashboard/summary`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getSummarySuccess (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/dashboard/summary_success_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getSummaryPending (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/dashboard/summary_in_progress_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getOverViewOnProcess (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/dashboard/overview_on_process`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getOverViewOnProcessTable (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/dashboard/overview_on_process_table`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getIshiplistAllShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/listAllShop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getAllOutSourceCourier (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}iship/outsoruce_courier_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SentPickupAddress (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/iship/${data.seller_shop_id}/pickup_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
