<template>
  <v-container :class="MobileSize ? 'mt-3' : 'px-0 py-0'">
    <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px;" v-if="!MobileSize">แดชบอร์ดรายงานจำนวน Transaction ทั้งหมดบนระบบ</v-card-title>
      <v-card-title style="font-weight: 700; line-height: 22px; font-size: 18px;" v-else>
        <v-row dense>
          <v-icon color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon> แดชบอร์ดรายงานจำนวน Transaction ทั้งหมดบนระบบ
        </v-row>
      </v-card-title>
      <v-card-text>
        <v-row justify="center" dense class="mb-4">
          <v-col cols="12">
            <DashBoard />
          </v-col>
        </v-row>
        <v-row justify="center" dense class="mb-4" v-if="checkShowShopTopTen">
          <v-col cols="12">
            <ShopTopTen />
          </v-col>
        </v-row>
        <v-row justify="center" dense class="mb-4">
          <v-col cols="12">
            <UserTopTen />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  components: {
    DashBoard: () => import('@/components/AdminPanit/Transaction/Dashboard'),
    ShopTopTen: () => import('@/components/AdminPanit/Transaction/ShopTopTen'),
    UserTopTen: () => import('@/components/AdminPanit/Transaction/UserTopTen')
  },
  data () {
    return {
      checkShowShopTopTen: true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    MobileSize (val) {
      // console.log(val)
      if (val === true) {
        this.$router.push({ path: '/TransactionMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/AdminPanit' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$on('CheckShopTopTen', this.CheckShopTopTen)
    this.$EventBus.$emit('changeNavAdmin')
    // await this.getDataInDashboard()
  },
  methods: {
    backtoMenu () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    CheckShopTopTen (shopId) {
      if (shopId === '') {
        this.checkShowShopTopTen = true
      } else {
        this.checkShowShopTopTen = false
      }
    }
  }
}
</script>
