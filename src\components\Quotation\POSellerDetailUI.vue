<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row justify="center" class="my-4" v-if="!MobileSize">
        <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" >รายละเอียดการสั่งซื้อสินค้า</v-card-title>
      </v-row>
      <v-row justify="start" class="my-4" v-else>
        <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToPoseller()">mdi-chevron-left</v-icon> รายละเอียดการสั่งซื้อสินค้า</v-card-title>
      </v-row>
      <v-container>
        <v-row>
          <v-col :cols="IpadSize || MobileSize ? 12 : 7" :class="IpadSize || MobileSize ? 'pb-0' : 'pa-0 ma-0'">
            <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.order_number }}</span>
              <span  v-if="!MobileSize"> | </span>
              <v-chip class="ma-2" :color="getColor(items.transaction_status)" small :text-color="getTextColor(items.transaction_status)">
                {{ getStatus(items.transaction_status) }}
              </v-chip>
            </div>
            <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ใบกำกับภาษี : </span>
              <span v-if="items.required_invoice === 'ขอใบกำกับภาษี' ">
                <v-chip small class="ma-2" color="#E5EFFF" text-color="#1B5DD6">
                  ขอใบกำกับภาษี
                </v-chip>
              </span>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-else>
                {{'-'}}
              </span>
            </div>
            <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ : </span>
              <span>{{ items.buyer_name }}</span>
            </div>
            <!-- <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : </span>
              <span>{{ new Date(items.created_at).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) }}</span>
            </div> -->
            <!-- <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'">
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">จัดส่งโดย : </span>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.business_type === '' ? '-' : items.business_type }}</span>
            </div> -->
            <div :class="IpadSize || MobileSize ? '' : IpadProSize ? 'mb-3' : 'mb-3'" v-if="mobilystTrackingNo">
              <!-- <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ดาวน์โหลด QR Code</span> -->
              <v-btn text color="#27AB9C" @click="getQrCode()"><v-icon class="pr-2">mdi-download-circle-outline</v-icon> <span style="text-decoration: underline;">ดาวน์โหลด QR Code</span></v-btn>
            </div>
            <!-- <div class="mb-3 ml-2" v-if="mobilystTrackingNo">
              <span>ดาวน์โหลด QR Code รูปแบบเต็ม</span>
              <v-btn outlined class="ml-2" x-small color="#27AB9C" @click="printBiglabel()"><v-icon small>mdi-download</v-icon></v-btn>
            </div> -->
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? 12 : 5">
            <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : {{dateCreateOrderStep1}}</v-col>
            <v-row class="mb-1">
              <span class="ml-3 mt-3" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะผู้ขาย :</span>
              <v-col cols="7" v-if="itemStatus !== 'ดำเนินการแล้ว'">
                <!-- <v-select v-model="itemStatus" :items="status_items" item-text="text" item-value="value" color="#27AB9C" @change="OpenModelChangstatus(items)" :disabled="disablecancel" style="font-size: 14px; min-height: 50px;" outlined dense></v-select> -->
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" style="color: #1AB759">{{ items.status }}</span>
              </v-col>
              <v-col cols="7" v-else>
                <!-- <v-select v-model="itemStatus" :items="status_items_success" item-text="text" item-value="value" color="#27AB9C" @change="OpenModelChangstatus(items)" :disabled="disablecancel" style="font-size: 14px; min-height: 50px;" outlined dense></v-select> -->
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" style="color: #1AB759">{{ items.status }}</span>
              </v-col>
            </v-row>
            <!-- <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่รับ : {{ dateCreateOrderStep4 }}</v-col> -->
            <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะผู้ซื้อ : {{ trackingText }}</v-col>
          </v-col>
          <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 12">
            <v-row no-gutters>
              <v-col cols="12" md="7">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</p>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.address_data }}</span>
              </v-col>
              <v-col cols="12" md="5" class="pl-3">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งใบกำกับภาษี</p>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-if="items.invoice_address !== '' ">{{ items.invoice_address }}</span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-else>{{ '-' }}</span>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
      <v-col cols="12">
        <v-card outlined style="border-radius: 8px;">
          <v-container grid-list-lg>
            <v-row no-gutters>
              <!-- Mobilyst Flash -->
              <!-- <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 7" v-if="mobilystTrackingNo">
                <v-row justify="center" :class="IpadSize || IpadProSize || MobileSize ? '':'ml-8'">
                  <v-col cols="12" >
                    <div :class="IpadSize || IpadProSize || MobileSize ? '' : 'ml-6'">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Standard Delivery - {{items.service_type === 'normal' ? 'ส่งแบบปกติ' : items.service_type === 'chilled' ? 'ส่งแบบควบคุมอุณหภูมิ' : items.service_type === 'frozen' ? 'ส่งแบบแช่แข็ง' : 'ส่งของขนาดใหญ่' }} {{items.business_type}} Express</span><br/>
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Tracking Number :  <b>{{mobilystTrackingNo}}</b></span>
                    </div>
                    <v-btn class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" text color="#27AB9C" @click="GoToMobily(items.url_tracking)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain></v-img> ติดตามสถานะขนส่ง</v-btn>
                  </v-col>
                </v-row>
              </v-col> -->
              <v-col cols="12" class="pl-3 pt-4">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'"><b>รายการสั่งซื้อสินค้า</b></p>
              </v-col>
              <v-col cols="12" class="mr-4 mb-2" v-if=" items.data_list &&  items.data_list[0].product_list.length !== 0">
                <a-row type="flex" justify="start">
                  <span class="pl-3 pt-1 " style="font-size: 16px; font-weight: 700;">
                    {{ items.data_list[0].product_list ? Object.keys(items.data_list[0].product_list).length:0 }} รายการสินค้า
                  </span>
                </a-row>
              </v-col>
            </v-row>
          </v-container>
          <div v-if="cardProduct.length !== 0">
            <v-container v-if="!MobileSize && !IpadSize" grid-list-xs>
              <a-table :data-source="items.data_list[0].product_list" :rowKey="record => record.sku" :columns="headers" :pagination="{ pageSize: 100 }">
                <!-- <template slot="header" slot-scope="column">
                  <template v-if="column.key === 'sku'">
                    <span style="color: #1890ff;">รหัสสินค้า</span>
                  </template>
                </template> -->
                <template  slot="productdetails" slot-scope="text, record">
                  <v-row >
                    <v-col cols="12" md="3" class="pr-0 mt-2 py-1">
                      <v-img :src="record.product_image" contain max-height="48" max-width="76" v-if="record.product_image !== ''"/>
                      <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowMobile' : 'imageshow'" v-else/>
                    </v-col>
                    <v-col cols="12" md="9" >
                      <p class="mb-0 DetailsProductFront">{{record.product_name}}</p>
                      <div v-if="record.have_attribute === 'yes'" >
                        <span class="mb-0 mb-4 DetailsProductFront" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                        <span class="ml-3 mb-0 mb-4 DetailsProductFront"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                      </div>
                      <br/>
                      <!-- <span style="color: #27AB9C;" > &#8226; </span>
                      <span class="mb-0 mr-1 ExpressFront">การจัดส่งแบบ </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> Standard </span>
                      <span class="mb-0 mr-1 ExpressFront">ธรรมดา </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> 3-5 </span>
                      <span class="mb-0 ExpressFront">วันทำการ</span> -->
                    </v-col>
                  </v-row>
                </template>
                <template  slot="price" slot-scope="text, record">
                  <v-row >
                    <v-col cols="12">
                      <span >{{ Number(record.revenue_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </template>
                <template slot="quantity" slot-scope="text, record">
                  <v-col cols="12">
                    <span>{{ record.quantity }}</span>
                  </v-col>
                </template>
                <template slot="net_price" slot-scope="text, record">
                  <v-row>
                    <v-col cols="12">
                      <span > {{ Number(record.revenue_price * record.quantity).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </template>
                <template  slot="revenue_amount" slot-scope="text, record">
                  <v-row >
                    <v-col cols="12">
                      <span >{{ Number(record.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </template>
              </a-table>
            </v-container>
            <v-container v-else grid-list-xs>
              <a-table :data-source="items.data_list[0].product_list" :rowKey="record => record.sku" :columns="headersMobile" :pagination="{ pageSize: 100 }">
                <template slot="productdetails" slot-scope="text, record">
                  <v-row>
                    <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                      <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                      <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                    </v-col>
                    <v-col cols="9" md="8">
                      <span class="mb-0 DetailsProductFrontMobile">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</span><br>
                      <div class="mb-0" v-if="record.have_attribute === 'yes'">
                        <span class="mb-0 DetailsProductFrontMobile" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                        <span class="ml-3 mb-0 DetailsProductFrontMobile"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                      </div>
                      <span class="mb-0 DetailsProductFrontMobile">จำนวน: <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                      <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา: <span style="font-weight: 700;">{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span><br>
                      <span class="pt-1 mb-0 DetailsProductFrontMobile">ราคารวม: <span style="font-weight: 700;">{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span>
                    </v-col>
                    <!-- <v-col>
                      <span style="color: #27AB9C;" > &#8226; </span>
                      <span class="mb-0 mr-1 ExpressFront">การจัดส่งแบบ </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> Standard </span>
                      <span class="mb-0 mr-1 ExpressFront">ธรรมดา </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> 3-5 </span>
                      <span class="mb-0 ExpressFront">วันทำการ</span>
                    </v-col> -->
                  </v-row>
                </template>
              </a-table>
            </v-container>
            <v-container grid-list-xs>
              <!-- สรุปรายการสั่งซื้อ desktop,ipad, ipadpro -->
              <v-row v-if="!MobileSize">
                <v-col :cols="IpadSize ? 9 : 12" md="10">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ส่วนลด :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ราคารวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ค่าจัดส่ง :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'" style="font-size: 20px; font-weight: bold; ">ราคารวมทั้งหมด :</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col :cols="IpadSize ? 3 : 12" md="2">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span style="font-weight: 600;" :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_price_no_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span style="font-weight: 600;" :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_discount).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span style="font-weight: 600;" :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span style="font-weight: 600;" :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_price_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <!-- <span :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_shipping).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> -->
                      <span style="font-weight: 600;" :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">0.00 บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span  style="font-weight: bold; font-size: 20px;" :class="IpadSize || IpadProSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.net_price).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} บาท</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <!-- สรุปรายการสั่งซื้อ mobile -->
              <v-row v-else>
                <v-col cols="12">
                  <OrderSummary :items="items"></OrderSummary>
                </v-col>
              </v-row>
            </v-container>
          </div>
        </v-card>
      </v-col>
      <v-col cols="12" class="pt-0">
        <v-card outlined style="border-radius: 8px;">
          <v-container grid-list-xs class="mb-2">
            <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">รายละเอียดรายการสั่งซื้อ</p>
            <v-row dense>
              <v-col cols="4" class="mt-2">
                <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่เริ่มสัญญา : {{ formatDateToShow(items.start_date_contract) }}</span>
              </v-col>
              <v-col cols="4" class="mt-2">
                <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สิ้นสุดสัญญา : {{ formatDateToShow(items.end_date_contract) }}</span>
              </v-col>
              <v-col cols="4" class="mt-2">
                <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">Pay Type : {{items.pay_type}}</span>
              </v-col>
              <!-- <v-col cols="4" class="mt-2">
                <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ส่วนลด :</span>
              </v-col> -->
              <v-col cols="4" class="mt-2">
                <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ยอดเงิน : {{Number(items.net_price).toLocaleString(undefined, { minimumFractionDigits: 2 })}}</span>
              </v-col>
              <v-col cols="4" class="mt-2">
                <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">หมายเหตุ : {{items.remark}}</span>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" class="pt-0">
        <v-card outlined style="border-radius: 8px;">
          <v-container grid-list-xs class="mb-2">
            <v-col cols="12" class="pl-0 pb-0">
              <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน |
                <v-chip class="ma-2" :color="getColor(items.transaction_status)" small :text-color="getTextColor(items.transaction_status)">
                  {{ getStatus(items.transaction_status) }}
                </v-chip
                >
              </p>
            </v-col>
            <v-col cols="12" class="pl-0 pt-0">
              <v-btn
                class="white--text"
                color="#27AB9C"
                small
                @click="GoToPayment()"
                >ชำระเงิน</v-btn>
              <span v-if="items.transaction_status === 'Success'" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">คุณได้ชำระเงินเสร็จเรียบร้อยแล้ว ขอบคุณสำหรับการใช้บริการ</span>
              <span v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">คำสั่งซื้อสินค้าของคุณถูกยกเลิก</span>
              <span v-else-if="items.transaction_status === 'Fail'" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง</span>
            </v-col>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" class="pt-0">
        <v-card outlined style="border-radius: 8px;">
          <v-container grid-list-xs class="mb-2">
            <v-row no-gutters>
              <!-- INET Marketplace<img class="ml-3 mb-2" src="@/assets/ImageINET-Marketplace/ICONShop/Celebret.png"> -->
              <v-row v-if="items.transaction_status === 'Success'" no-gutters class="mt-4">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">หลักฐานการชำระเงิน</p>
                <!-- <v-col cols="1" :class="IpadSize || IpadProSize ? 'mt-8' : 'mt-9'">
                  <v-row style="width: 20px;" :class="MobileSize ? 'ml-0' : IpadSize ? 'ml-2' : 'ml-4'">
                    <v-img src="@/assets/ImageINET-Marketplace/PoSeller/paid_success.png" :height="MobileSize ? '225px' : IpadSize || IpadProSize ? '212px': '100%'"></v-img>
                  </v-row>
                </v-col> -->
                <v-col cols="12" class="mt-0">
                  <v-row no-gutters>
                    <v-col cols="12" md="7" sm="7" class="mb-4">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ items.order_number }}</span>
                    </v-col>
                    <v-col cols="12" md="5" sm="5" class="mb-4">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">จำนวนเงิน :  </span>
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ Number(items.receipt[0].TxnAmount).toLocaleString(undefined, { minimumFractionDigits: 2 }) || '-'}} บาท</span>
                    </v-col>
                    <v-col cols="12" md="7" sm="7" class="mb-4">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">วันและเวลาที่ทำรายการ : </span>
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{created_at}}</span>
                    </v-col>
                    <v-col cols="12" md="5" sm="5" class="mb-4">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">Ref : </span>
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ items.receipt[0].orderIDRef }}</span>
                    </v-col>
                    <v-col cols="12" md="7" sm="7">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">ธนาคาร : </span>
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ bankName }}</span>
                    </v-col>
                    <v-col cols="12" md="5" sm="5">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">รูปแบบการชำระเงิน : </span>
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ items.receipt[0].payType }}</span>
                    </v-col>
                    <!-- <v-col :cols="MobileSize || IpadSize ? 5 : 4">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ items.order_number }}</span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 5 : 4" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">ผลลัพธ์การทำรายการ : </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8">
                      <v-chip class="ma-2" :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" color="#E6F5F3" small text-color="#27AB9C">สำเร็จ</v-chip>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 5 : 4" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">จำนวนเงิน :  </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ Number(items.receipt[0].TxnAmount).toLocaleString(undefined, { minimumFractionDigits: 2 }) || '-'}} บาท</span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 5 : 4" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">วันและเวลาที่ทำรายการ : </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{created_at}}</span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 5 : 4" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">Ref : </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ items.receipt[0].orderIDRef }}</span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 5 : 4" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">ธนาคาร : </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ bankName }}</span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 5 : 4" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">รูปแบบการชำระเงิน : </span>
                    </v-col>
                    <v-col :cols="MobileSize || IpadSize ? 7 : 8" class="mt-2">
                      <span :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'" style="font-weight: bold; ">{{ items.receipt[0].payType }}</span>
                    </v-col> -->
                  </v-row>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
                <!-- <v-col cols="12" class="mt-5" v-if="item.transaction_status !== 'Cancel'">
                  <span style="color: #27AB9C;" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'"><b>&#8226; รายละเอียดใบสั่งซื้อที่ถูกยกเลิก</b></span>
                </v-col> -->
                <v-col cols="12" align="left" v-if="item.transaction_status !== 'Cancel'">
                  <v-timeline dense>
                    <v-timeline-item v-for="(item, index) in items.approver_list" :key="index" fill-dot class="white--text mb-12" color="#27AB9C" small>
                      <template v-slot:icon>
                        <span>{{ index + 1 }}</span>
                      </template>
                      <v-row no-gutters>
                        <v-col cols="12">
                          <span style="color: #27AB9C;"><b>ผู้อนุมัติ</b></span>
                        </v-col>
                        <v-col cols="12">
                          <span style="color: black;">สถานะ : </span>
                          <v-chip v-if="item.status === 'cancel'" class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip>
                          <v-chip v-else class="ma-2" color="#E6F5F3" small text-color="#27AB9C">อนุมัติ</v-chip>
                        </v-col>
                        <v-col cols="12">
                          <span style="color: black;" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้อนุมัติ : {{ item.approver_name }}({{ item.email }})</span>
                        </v-col>
                        <v-col v-if="item.time_approve === '-'" cols="12" class="mt-3">
                          <span style="color: black;" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่อนุมัติ : {{ item.time_approve }}</span>
                        </v-col>
                        <v-col v-else cols="12" class="mt-3">
                          <span style="color: black;" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่อนุมัติ : {{ new Date(item.time_approve).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) }}</span>
                        </v-col>
                      </v-row>
                    </v-timeline-item>
                  </v-timeline>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'ext_buyer'" no-gutters>
                <v-col cols="12" class="mt-4">
                  <!-- <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">
                    คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง
                    สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย
                  </span> -->
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Fail'" no-gutters>
                <v-col cols="12" class="mt-4">
                  <!-- <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">
                    คุณชำระเงินไม่สำเร็จ
                    กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง
                  </span> -->
                  <!-- <v-btn
                    class="white--text"
                    color="#27AB9C"
                    small
                    @click="GoToPayment()"
                    >ชำระเงิน</v-btn
                  > -->
                </v-col>
              </v-row>
              <v-row v-else no-gutters>
                <v-col cols="12" class="mt-4">
                  <span style="font-size: 18px; font-weight: 700;">หลักฐานการชำระเงิน</span><br/>
                  <span style="font-size: 16px; font-weight: 700;">-</span>
                </v-col>
              </v-row>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
      <!-- </v-row> -->
      <v-dialog v-model="dialogChangstatus" width="560" persistent dense>
        <v-card width="100%" justify="center" align="center" style="border-radius: 12px;">
          <v-toolbar color="#E6F5F3" dense elevation="0">
            <v-row justify="center" align-content="center">
              <span style="color: #27AB9C; font-size: 20px; font-weight: bold; line-height: 30px;">สถานะ</span>
            </v-row>
            <v-btn fab x-small @click="cancleStatus()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar>
            <v-card-text>
              <v-col cols="12" md="9" sm="8" class="my-8">
                <v-row justify="center" align="center" style="text-align: center">
                  <span style="font-weight: 800; color: #333333;" :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">คุณได้ทำการเปลี่ยนสถานะเป็น {{textstatus}}</span>
                  <br/>
                  <span style="font-weight: 800; color: #333333;" :class="IpadSize || IpadProSize ? 'fontSizeDetailMobile': MobileSize ? 'fontSizeXs' : 'fontSizeDetail'">คุณต้องการที่จะเปลี่ยนสถานะ ใช่ หรือ ไม่ ?</span>
                </v-row>
              </v-col>
              <v-col cols="12" md="12" sm="12">
                <v-row justify="center" align-content="center">
                  <v-btn outlined dense dark class="pl-7 pr-7 mr-4" color="#27AB9C" @click="cancleStatus()">ยกเลิก</v-btn>
                  <v-btn dark dense class="pl-7 pr-7" color="#27AB9C" @click="UpdateStatusSeller()">ตกลง</v-btn>
                </v-row>
              </v-col>
            </v-card-text>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import { Row, Table } from 'ant-design-vue'
export default {
  components: {
    'a-row': Row,
    'a-table': Table,
    OrderSummary: () => import('@/components/Card/OrderSummary.vue')
  },
  data () {
    return {
      itemColor: 'green',
      // overlay: false,
      items: [{ data_list: [] }],
      paymentNumber: {},
      cardProduct: [],
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingText: '',
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: '',
      created_at: '',
      status: '',
      mobilystTrackingNo: '',
      flashTracking: '',
      status_items: [
        // { text: 'ดำเนินการแล้ว', value: 'ดำเนินการแล้ว' },
        { text: 'ยังไม่ดำเนินการ', value: 'ยังไม่ดำเนินการ' }
        // { text: 'ยกเลิก', value: 'ยกเลิก' }
      ],
      status_items_success: [
        { text: 'ดำเนินการแล้ว', value: 'ดำเนินการแล้ว' }
      ],
      dialogChangstatus: false,
      disablecancel: false,
      textstatus: '',
      valChangStatus: '',
      itemStatus: '',
      estep: 0,
      ordernumber: '',
      transactionNumber: '',
      isUpdate: false,
      checkbox: false,
      menu: false,
      menu2: false,
      menu3: false,
      time: null,
      modal2: false,
      dateSent: null,
      dateReceived: null,
      lazy: false,
      qrcode: '',
      // flashMCHID: process.env.VUE_APP_FLASH,
      itemRules: {
        dateSent: [
          v => !!v || 'กรุณาระบุวันที่ส่ง'],
        time: [
          v => !!v || 'กรุณาระบุเวลา'],
        dateReceived: [
          v => !!v || 'กรุณาระบุวันที่รับ']
      },
      routes: [{
        routedAt: 1523356924,
        routeAction: 'DELIVERY_CONFIRM',
        message: 'พัสดุของคุณถูกเซ็นรับแล้ว เซ็นรับโดย TH01011C27',
        state: 5
      }, {
        routedAt: 1523356924,
        routeAction: 'DELIVERY_TICKET_CREATION_SCAN',
        message: 'มีพัสดุรอการนำส่ง กรุณารอการติดต่อจากเจ้าหน้าที่ Mobilyst Tech',
        state: 2
      }, {
        routedAt: 1523356560,
        routeAction: 'SHIPMENT_WAREHOUSE_SCAN',
        message: 'พัสดุของคุณอยู่ที่ กทม. จะถูกส่งไปยัง จตุโชติ-DC',
        state: 3
      }, {
        routedAt: 1523356029,
        routeAction: 'RECEIVED',
        message: 'zhao=DC พนักงานเข้ารับพัสดุแล้ว',
        state: 6
      }]
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    this.ordernumber = this.$route.query.orderNumber
    this.transactionNumber = this.$route.query.tranNumber
    if (localStorage.getItem('orderNumberSeller') !== null) {
      this.paymentNumber = JSON.parse(
        Decode.decode(localStorage.getItem('orderNumberSeller'))
      )
    }
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.getItemProduct()
    }
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รหัสสินค้า',
          dataIndex: 'sku',
          key: 'sku',
          align: 'start',
          scopedSlots: { customRender: 'sku' },
          width: '15%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          align: 'start',
          scopedSlots: { customRender: 'productdetails' },
          width: '25%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          key: 'price',
          align: 'start',
          scopedSlots: { customRender: 'price' },
          width: '15%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'start',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          align: 'start',
          width: '15%'
        },
        {
          title: 'Amount',
          dataIndex: 'revenue_amount',
          scopedSlots: { customRender: 'revenue_amount' },
          key: 'revenue_amount',
          align: 'start',
          width: '15%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headers
    },
    productList () {
      var list = []
      list = this.items.data_list[0]
      return list
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/posellerDetailMobile?orderNumber=${this.ordernumber}&tranNumber=${this.transactionNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/POSellerDetail?orderNumber=${this.ordernumber}&tranNumber=${this.transactionNumber}` }).catch(() => {})
      }
    }
  },
  methods: {
    backToPoseller () {
      this.$router.push({ path: '/posellerMobile' }).catch(() => {})
    },
    GoToMobily (tracking) {
      window.open(tracking)
    },
    async getItemProduct () {
      // this.overlay = true
      var data = {
        order_number: this.ordernumber,
        payment_transaction_number: this.transactionNumber
      }
      await this.$store.dispatch('actionOrderDetailSeller', data)
      var res = await this.$store.state.ModuleOrder.stateOrderDetailSeller
      // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.mobilystTrackingNo = res.data.order_mobilyst_no
      this.qrcode = res.data.url_barcode_picture
      // if (this.mobilystTrackingNo) {
      //   var data = {
      //     mchId: this.flashMCHID,
      //     token: onedata.user.access_token,
      //     pno: this.mobilystTrackingNo
      //   }
      //   await this.$store.dispatch('actionTrackingOrderFlash', data)
      //   var response = await this.$store.state.UPSModuleOrder.stateTrackingOrderFlash
      //   this.flashTracking = response.data
      //   if (this.flashTracking !== null) {
      //     if (this.flashTracking.state !== 0) {
      //       this.estep = await this.flashTracking.routes.length
      //     } else {
      //       this.estep = 0
      //     }
      //   }
      // } else {
      //   this.estep = 0
      // }
      // var dataListOder = { order_number: res.data.order_number }
      // await this.$store.dispatch('actionListOrder', dataListOder)
      // var responseListOrder = await this.$store.state.ModuleOrder.stateListOrder
      // const orderUPS = responseListOrder.data
      // if (responseListOrder.result === 'SUCCESS') {
      //   if (orderUPS.sent_date !== '') this.dateSent = orderUPS.sent_date
      //   if (orderUPS.received_date !== '') this.dateReceived = orderUPS.received_date
      //   if (orderUPS.time !== '') this.time = orderUPS.sent_time.substr(0, 5)
      // }

      if (res.result === 'SUCCESS') {
        this.items = res.data
        this.cardProduct = this.items.data_list[0]
        if (this.items.receipt.length !== 0) {
          if (this.items.receipt[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.items.receipt[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.items.receipt[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.items.receipt[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.items.receipt[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        this.trackingStatus = this.items.receipt
        if (this.isValidDate(this.items.receipt[0].created_at)) {
          this.created_at = new Date(
            this.items.receipt[0].created_at
          ).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        } else {
          this.created_at = '-'
        }
        if (this.isValidDate(this.items.tracking[0].time_step_1)) {
          this.dateCreateOrderStep1 = new Date(
            this.items.tracking[0].time_step_1
          ).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        }
        if (this.isValidDate(this.items.tracking[0].time_step_2)) {
          this.dateCreateOrderStep2 = new Date(
            this.items.tracking[0].time_step_2
          ).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        }
        if (this.isValidDate(this.items.tracking[0].time_step_3)) {
          this.dateCreateOrderStep3 = new Date(
            this.items.tracking[0].time_step_3
          ).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        }
        if (this.isValidDate(this.items.tracking[0].time_step_4)) {
          this.dateCreateOrderStep4 = new Date(
            this.items.tracking[0].time_step_4
          ).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
            // hour: 'numeric',
            // minute: 'numeric'
          })
        } else {
          this.dateCreateOrderStep4 = this.items.tracking[0].time_step_4
        }
        if (this.items.tracking.length !== 0) {
          if (this.items.tracking[0].status_tracking === 'Not Paid') {
            this.trackingText = 'ที่ต้องรอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Success') {
            this.trackingText = 'คำสั่งซื้อที่ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Sent') {
            this.trackingText = 'ที่ต้องจัดส่ง'
          } else if (this.items.tracking[0].status_tracking === 'Sent') {
            this.trackingText = 'จัดส่งแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Received') {
            this.trackingText = 'ได้รับสินค้าแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Received') {
            this.trackingText = 'ยังไม่ได้รับสินค้า'
          } else if (this.items.tracking[0].status_tracking === 'Cancel by approver') {
            this.trackingText = 'ยกเลิกโดยผู้อนุมัติ'
          } else if (this.items.tracking[0].status_tracking === 'Pick Up') {
            this.trackingText = 'รับที่หน้าร้าน'
          }
        } else {
          this.trackingText = ''
        }
        // this.overlay = false
        this.itemStatus = this.items.status
        if (this.itemStatus === 'ยกเลิก' || this.itemStatus === 'ดำเนินการแล้ว') {
          this.disablecancel = true
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else if (res.message === 'Not found!. The user is not seller in this shop.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: 'Not found!. The user is not seller in this shop.'
        })
        this.$router.push({ path: '/' })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: res.message
        })
      }
    },
    OpenModelChangstatus (item) {
      this.valChangStatus = ''
      this.dialogChangstatus = true
      this.valChangStatus = item
      this.textstatus = this.itemStatus
    },
    cancleStatus () {
      this.dialogChangstatus = false
      this.itemStatus = this.items.status
    },
    async UpdateStatusSeller (val) {
      this.dialogChangstatus = false
      if (this.itemStatus === 'ยกเลิก') {
        var dataCancel = {
          order_number: this.valChangStatus.order_number
        }
        await this.$store.dispatch('actionCancelStatusSeller', dataCancel)
        var responseCancelOrder = await this.$store.state.ModuleOrder.stateCancelStatusSeller
        if (responseCancelOrder.result === 'SUCCESS') {
          this.$swal.fire({ icon: 'success', title: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
          if (this.itemStatus === 'ยกเลิก') {
            this.disablecancel = true
          }
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ไม่สามารถดำเนินการได้' })
        }
      } else {
        if (this.itemStatus === 'ยังไม่ดำเนินการ') {
          this.status = 'pending'
        } else if (this.itemStatus === 'ดำเนินการแล้ว') {
          this.status = 'success'
          this.disablecancel = true
        }
        const shopId = localStorage.getItem('shopSellerID')
        const update = {
          seller_shop_id: shopId,
          order_number: this.valChangStatus.order_number,
          status: this.status
        }
        await this.$store.dispatch('actionUpdateStatusSeller', update)
        var responseUpdateOrder = await this.$store.state.ModuleOrder.stateUpdateStatusSeller
        if (responseUpdateOrder.result === 'SUCCESS') {
          this.$swal.fire({ icon: 'success', title: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ไม่สามารถดำเนินการได้' })
        }
      }

      // this.GetSellerShop()
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    getQrCode () {
      window.open(this.qrcode)
    },
    // async printSmalllabel () {
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var data = {
    //     mchId: this.flashMCHID,
    //     pno: this.mobilystTrackingNo,
    //     token: onedata.user.access_token
    //   }
    //   await this.$store.dispatch('actionPrintSmalllabelFlash', data)
    //   var response = await this.$store.state.ModuleOrder.statePrintSmalllabelFlash
    //   if (response.status === 'success') {
    //     window.open(response.path)
    //   }
    // },
    // async printBiglabel () {
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var data = {
    //     mchId: this.flashMCHID,
    //     pno: this.mobilystTrackingNo,
    //     token: onedata.user.access_token
    //   }
    //   await this.$store.dispatch('actionPrintBiglabelFlash', data)
    //   var response = await this.$store.state.ModuleOrder.statePrintBiglabelFlash
    //   if (response.status === 'success') {
    //     window.open(response.path)
    //   }
    // },
    // async Confirm () {
    //   if (this.$refs.FormOrderUPs.validate(true)) {
    //     if (this.dateSent === null) {
    //       var sentdate = ''
    //     } else {
    //       sentdate = this.dateSent.split('-')[0] + this.dateSent.split('-')[1] + this.dateSent.split('-')[2]
    //     }
    //     if (this.time === null) {
    //       var senttime = ''
    //     } else {
    //       senttime = this.time.split(':')[0] + this.time.split(':')[1] + '00'
    //     }
    //     if (this.dateReceived === null) {
    //       var receiveddate = ''
    //     } else {
    //       receiveddate = this.dateReceived.split('-')[0] + this.dateReceived.split('-')[1] + this.dateReceived.split('-')[2]
    //     }
    //     var dataSentDate = {
    //       order_number: this.items.order_number,
    //       sent_date: sentdate,
    //       sent_time: senttime,
    //       received_date: receiveddate
    //     }
    //     await this.$store.dispatch('actionCreateUPSOrder', dataSentDate)
    //     var responseCreateUPS = await this.$store.state.ModuleOrder.stateCreateUPSOrder
    //     if (responseCreateUPS.result === 'SUCCESS') {
    //       this.$swal.fire({ icon: 'success', title: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
    //     } else {
    //       this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ไม่สามารถดำเนินการได้' })
    //     }
    //   }
    // },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    async GoToPayment () {
      const PaymentID = {
        payment_transaction_number: this.items.payment_transaction
      }
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    isValidDate (dateObject) {
      return new Date(dateObject).toString() !== 'Invalid Date'
    },
    formmatdate (date) {
      if (date !== null) {
        const dates = JSON.parse(JSON.stringify(date))
        const getYear = Number(dates.split('-')[0]) + 543
        return dates.split('-')[2] + '/' + dates.split('-')[1] + '/' + getYear
      }
    },
    getColor (item) {
      if (item === 'Pending') return '#FCF0DA'
      else if (item === 'Not Paid') return '#FCF0DA'
      else if (item === 'Success') return '#F0F9EE'
      else if (item === 'Approve') return '#F0F9EE'
      else if (item === 'Fail') return '#F7D9D9'
      else if (item === 'Credit Term') return '#E5EFFF'
      else if (item === 'Cash') return '#F0F9EE'
      else return '#F7D9D9'
    },
    getTextColor (item) {
      if (item === 'Pending') return '#E9A016'
      else if (item === 'Not Paid') return '#E9A016'
      else if (item === 'Success') return '#1AB759'
      else if (item === 'Approve') return '#1AB759'
      else if (item === 'Fail') return '#D1392B'
      else if (item === 'Credit Term') return '#1B5DD6'
      else if (item === 'Cash') return '#1AB759'
      else return '#D1392B'
    },
    getStatus (item) {
      if (item === 'Pending') return 'รออนุมัติ'
      else if (item === 'Not Paid') return 'ยังไม่ชำระเงิน'
      else if (item === 'Success') return 'ชำระเงินสำเร็จ'
      else if (item === 'Approve') return 'วางบิล'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Credit Term') return 'ชำระเงินแบบเครดิตเทอม'
      else if (item === 'Cash') return 'ชำระเงินสด'
      else return 'ยกเลิกคำสั่งซื้อ'
    }
  }
}
</script>

<style>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
</style>

<style lang="css" scoped>
.imageshow {
  width: 80px;
  height: 80px;
  /* cursor: pointer; */
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.fontActive {
  color: #27AB9C;
}
.fontInactive {
  color: #a6a6a6;
}
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.fontSizeDetail {
  font-size: 16px;
}
.fontSizeDetailMobile {
  font-size: 14px;
}
.fontSizeXs {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
/* .frontFamily {
  font-family: 'Sukhumvit Set';
} */
.caption {
  font-size: 20px;
}
.HeadTableFront {
  /* font-family: 'Sukhumvit Set'; */
  font-size: 18px;
  font-weight: bold;
}
.DetailsProductFront {
  /* font-family: 'Sukhumvit Set'; */
  font-size: 14px;
}
.ExpressFront {
  /* font-family: 'Sukhumvit Set'; */
  font-size: 10px;
}
.classCycle{
  color: #27AB9C;
  font-size: 30px;
  margin-top: -21px;

}
.transparent {
  background-color: #F3F5F7 !important;
  opacity: 0.7;
  border-color: transparent !important;
}
.colorIcon .theme--light.v-icon {
  color: #27AB9C;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
::v-deep .ant-table-pagination {
  display: none;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>
