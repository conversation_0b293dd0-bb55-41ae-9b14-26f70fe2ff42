<template>
   <v-app class="backgroundPage" id="app">
  <div class="mb-4 mx-2">
    <Filters />
    <ChartGP />
    <DataTable />
  </div>
</v-app>
</template>
<script>
// import FrequencyUI from '@/components/Dashboard/frequencyTable'
// import dataTest from '@/components/library/dataTest.json'
export default {
  data () {
    return {
      freq: false,
      dataReq: [],
      dataSum: [],
      headers2: [],
      SETT: [],
      toDay: new Date().toISOString().slice(0, 10),
      Day: `${new Date().toISOString().slice(0, 7)}-01`,
      numChange: '1',
      selectFormData: ''
    }
  },
  components: {
    ChartGP: () => import(/* webpackPrefetch: true */ '@/components/AdminManage/Company/DashboardForGP/ChartGP'),
    DataTable: () => import(/* webpackPrefetch: true */ '@/components/AdminManage/Company/DashboardForGP/DataTable'),
    // summaryTransaction: () => import('@/components/AdminManage/Company/DashboardAdmin/summaryTransaction'),
    Filters: () => import(/* webpackPrefetch: true */ '@/components/AdminManage/Company/DashboardForGP/filterInfo')
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    this.$EventBus.$on('selectedChanged', this.selectedChanged)
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.init()
  },
  destroyed () {
    this.$EventBus.$off('selectedChanged')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardAdminForGPMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardAdminForGP' }).catch(() => {})
      }
    }
  },
  computed: {
    isMobile () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    Sums () {
      if (Object.values(this.dataSum).length !== 0) {
        return this.dataSum
      } else {
        return ''
      }
    }
  },
  methods: {
    async init () {
      await this.$store.commit('openLoader')
      const dataSent = await {
        shop_id: '',
        start_date: this.toDay,
        end_date: this.toDay,
        type: 'day'
      }
      await this.$store.dispatch('actionsAllGP', dataSent)
      const { data = {} } = await this.$store.state.ModuleAdminManage.stateAllGP
      console.log('stateAllGP', data)
      this.$store.state.ModuleAdminManage.stateGpChart.value = await [{
        name: 'รายได้',
        data: data.data_graph.map(e => {
          if (e !== '') {
            return e
          } else {
            return 0
          }
        })
      }]
      this.$store.state.ModuleAdminManage.stateGpChart.date = await data.date_graph.map(e => {
        return new Date(e).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
      })
      this.$store.state.ModuleAdminManage.stateGPTable = await data.transferred_data
      this.$store.state.ModuleAdminManage.stateshopList = await data.shop_list
      await this.$store.commit('closeLoader')
    },
    async selectedChanged (selectFormData) {
      console.log('selectFormData', selectFormData)
      await this.$store.dispatch('actionsAllGP', selectFormData)
      const { data = {} } = await this.$store.state.ModuleAdminManage.stateAllGP
      this.dataReq = await data
      this.$store.state.ModuleAdminManage.stateGpChart.value = await [{
        name: 'รายได้',
        data: data.data_graph.map(e => {
          if (e !== '') {
            return e
          } else {
            return 0
          }
        })
      }]
      this.$store.state.ModuleAdminManage.stateGpChart.date = await data.date_graph.map(e => {
        if (selectFormData.type === 'year' || selectFormData.type === 'all') {
          return new Date(e).toLocaleDateString('th-TH', { year: 'numeric', month: 'long' })
        } else {
          return new Date(e).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
        }
      })
      this.$store.state.ModuleAdminManage.stateGPTable = await data.transferred_data
      this.$store.state.ModuleAdminManage.stateshopList = await data.shop_list
      // console.log('shop_list AllGP', data.shop_list)
      await this.$EventBus.$emit('tableReload')
    }
  }
}
</script>
