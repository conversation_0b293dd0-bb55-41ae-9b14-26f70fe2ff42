<template>
    <v-card width="100%" :height="MobileSize ? '100%' : '0%'" elevation="0">
    <v-card-text class="px-1">
        <v-row class="mt-5 ml-5 mr-4 mb-6" dense>
        <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;">
           <v-card-text>
        <div  id='chart' class="mt-5">
        <!-- {{ comparedUser }} -->
        <apexchart type="bar" height="320" :options="$store.state.ModuleAdminManage.dashboardChart.dateUser" :series="comparedUser"></apexchart>
      </div>
          </v-card-text>
        </v-card>
      </v-row>
      <v-row class="mt-5 ml-5 mr-4 mb-6" dense>
        <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;">
           <v-card-text>
        <div  id='chart' class="mt-5">
        <apexchart type="bar" height="320" :options="$store.state.ModuleAdminManage.dashboardChart.dateSeller" :series="comparedSeller"></apexchart>
      </div>
        </v-card-text>
        </v-card>
      </v-row>
       </v-card-text>
  </v-card>
</template>
<script>
// import ApexCharts from 'apexcharts'
// import dataTest from '../library/dataTest.json'
// import eventBus from '@/components/eventBus'ฃ
import VueApexCharts from 'vue-apexcharts'
export default {
  name: 'ApexChart',
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      series: [{
        name: 'Net Profit',
        data: ['44', '55', '0']
      }, {
        name: 'Revenue',
        data: [76, 85, 101]
      }, {
        name: 'Free Cash Flow',
        data: [35, 41, 36]
      }],
      chartOptions: {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: ''
        },
        yaxis: {
          title: {
            text: ''
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return '$ ' + val + 'thousands'
            }
          }
        }
      },
      chartOptions2: {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: ''
        },
        yaxis: {
          title: {
            text: ''
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return '$ ' + val + 'thousands'
            }
          }
        }
      }
    }
  },
  created () {
  },
  mounted () {
  },
  updated () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    comparedSeller () {
      return this.$store.state.ModuleAdminManage.dashboardChart.comparedSeller.map(x => {
        return {
          name: x.name,
          data: [x.data[0] !== undefined ? x.data[0] : '0', x.data[1] !== undefined ? x.data[1] : '0', x.data[2] !== undefined ? x.data[2] : '0', x.data[3] !== undefined ? x.data[3] : '0', x.data[4] !== undefined ? x.data[4] : '0']
        }
      })
    },
    comparedUser () {
      // console.log(this.$store.state.ModuleAdminManage.dashboardChart.comparedUser)
      return this.$store.state.ModuleAdminManage.dashboardChart.comparedUser.map(x => {
        // console.log(x.data)
        return {
          name: x.name,
          data: [x.data[0] !== undefined ? x.data[0] : '0', x.data[1] !== undefined ? x.data[1] : '0', x.data[2] !== undefined ? x.data[2] : '0', x.data[3] !== undefined ? x.data[3] : '0', x.data[4] !== undefined ? x.data[4] : '0']
        }
      })
    },
    chartOptionSeller () {
      return this.$store.state.ModuleAdminManage.dashboardChart.dateSeller
    },
    chartOptionsUser () {
      return this.$store.state.ModuleAdminManage.dashboardChart.dateUser
    }
  },
  watch: {
    '$store.getters.chartOption' (e) {
      // console.log(e)
    }
  },
  methods: {
    // async init () {
    //   const data = await {
    //     start_date: this.Day,
    //     end_date: this.toDay
    //   }
    //   await this.$store.dispatch('actionsDashboard', data)
    //   var response = await this.$store.state.ModuleShop.stateDashboard
    //   var dataFilter = await []
    //   dataFilter = await response.data
    //   this.chartOptions.yaxis.max = await Math.max.apply(Math, dataFilter.map(o => o.value))
    // },
    async init () {
      // const data = await {
      //   start_date: this.Day,
      //   end_date: this.toDay
      // }
      // await this.$store.dispatch('actionsDashboard', data)
      // var response = await this.$store.state.ModuleShop.stateDashboard
      // var dataFilter = await []
      // dataFilter = await response.data
      // this.chartOptions.yaxis.max = await Math.max.apply(Math, dataFilter.map(o => o.value))
      // var grouped = await {}
      // for (const i in dataFilter) {
      //   grouped[dataFilter[i].date] = await grouped[dataFilter[i].buyer_name] || []
      //   await grouped[dataFilter[i].date].push(dataFilter[i].value)
      // }
      // const statusObj = await []
      // for (const [k, v] of Object.entries(grouped)) {
      //   await statusObj.push({
      //     name: k,
      //     data: v
      //   })
      // }
      // console.log('categories--', statusObj)
      // this.series = await []
      // this.series = await statusObj
      // this.chartOptions = await {
      //   xaxis: {
      //     categories: this.$store.state.ModuleShop.stateDateTime
      //   }
      // }
      // console.log('categories++', this.chartOptions.xaxis.categories)
    },
    async appendData () {
      // console.log('appendData***', this.series)
      this.series = await this.$store.state.ModuleShop.stateSeries
      // console.log('LineChar', this.series)
      // this.series = await this.$store.state.ModuleShop.sateSeries.map(el => {
      //   return {
      //     name: Object.values(el.name),
      //     data: el.data
      //   }
      // })
      // this.chartOptions = await {
      //   colors: colors
      // }
      this.chartOptions = await {
        xaxis: {
          categories: this.$store.state.ModuleShop.stateDateTime
        },
        yaxis: {
          max: this.$store.getters.chartOption
        },
        title: {
          text: `ข้อมูลการซื้อสินค้าของ : ${this.series[0].name}`,
          align: 'left',
          offsetX: 12,
          offsetY: 10
        }
      }
      // console.log('DataSeries', this.$store.state.ModuleShop.stateSeries, '==', this.$store.getters.chartColor)
    }
  }
}
</script>
