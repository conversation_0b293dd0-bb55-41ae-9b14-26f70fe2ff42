<template>
  <div>
    <v-btn
    color="green darken-1" dark absolute bottom right fixed fab style="margin-bottom: 65px" @click="printpo" v-if="printvisible" class="btnPrint">
    <v-icon>mdi-printer</v-icon>
  </v-btn>

  <div class="page elevation-0">
    <div>
      <QuotationTitle />
    </div>
    <div>
      <QuotationHeader />
    </div>
    <div>
      <v-row dense>
        <v-col cols="12" md="12">
          <v-card class="elevation-0">
            <v-row dense>
              <v-col cols="12" md="12" class="pa-0">
                <span>ร้านค้า: {{shopName}}</span>
              </v-col>
              <v-col cols="12" md="12" class="pa-0">
                <v-card outlined>
                  <v-data-table
                  hide-default-footer
                  disable-sort
                  :disable-pagination="disable_pagination"
                  :headers="locallang === 'en' ? headers_en : headers_th"
                  :items="table1"
                  item-key="name"
                  :calculate-widths="widths"
                  dense
                  class="fixed_header"
                  >
                  <template
                  v-slot:[`item.sku`]="{ item }"
                  :width="headers.width"
                  >
                  <span style="font-size: 10px; font-weight: 301">{{
                    item.sku
                  }}</span>
                </template>
                <template v-slot:[`item.name`]="{ item }">
                  <span style="font-size: 10px; font-weight: 301">{{
                    item.name
                  }}</span>
                </template>
                <template v-slot:[`item.quantity`]="{ item }">
                  <span style="font-size: 10px; font-weight: 301">{{
                    Number(item.quantity).toLocaleString()
                  }}</span>
                </template>
                <template v-slot:[`item.price`]="{ item }">
                  <span style="font-size: 10px; font-weight: 301">{{
                    Number(item.price).toLocaleString()
                  }}</span>
                </template>
                <template v-slot:[`item.discount_percent`]="{ item }">
                  <span style="font-size: 10px; font-weight: 301">{{
                    item.discount_percent
                  }}</span>
                </template>
                <template v-slot:[`item.net_price`]="{ item }">
                  <span style="font-size: 10px; font-weight: 301">{{
                    Number(item.net_price).toLocaleString()
                  }}</span>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </v-col>
    <v-col cols="12" md="12" v-if="table_body.length < 12" class="mt-2">
      <v-card outlined class="elevation-0">
        <QuotationFooter />
      </v-card>
    </v-col>
    <v-col cols="12" md="12" v-if="table_body.length > 12 && table_body.length == 20" class="page mt-2">
      <v-card outlined class="elevation-0">
        <QuotationFooter />
      </v-card>
    </v-col>
  </v-row>
</div>
</div>
<div v-if="table_body.length > 20 && table_body.length <= 42" class="page elevation-0">
  <v-row dense>
    <v-col cols="12" md="12" class="pa-0">
      <v-card outlined>
        <v-data-table
        hide-default-footer
        disable-sort
        :disable-pagination="disable_pagination"
        :headers="locallang === 'en' ? headers_en : headers_th"
        :items="table2"
        item-key="name"
        :calculate-widths="widths"
        dense
        class="fixed_header"
        >
        <template
        v-slot:[`item.sku`]="{ item }"
        :width="headers.width"
        >
        <span style="font-size: 10px; font-weight: 301">{{
          item.sku
        }}</span>
      </template>
      <template v-slot:[`item.name`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.name
        }}</span>
      </template>
      <template v-slot:[`item.quantity`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.quantity).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.price).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.discount_percent`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.discount_percent
        }}</span>
      </template>
      <template v-slot:[`item.net_price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.net_price).toLocaleString()
        }}</span>
      </template>
    </v-data-table>
  </v-card>
</v-col>
<v-col cols="12" md="12" class="mt-2">
  <QuotationFooter />
</v-col>
</v-row>
</div>
<div v-else-if="table_body.length > 42 && table_body.length <= 50" class="page mt-2 elevation-0">
  <v-row>
    <v-col cols="12" md="12" class="pa-0">
      <span>{{shopName}}</span>
    </v-col>
    <v-col cols="12" md="12" class="pa-0">
      <v-card outlined>
        <v-data-table
        hide-default-footer
        disable-sort
        :disable-pagination="disable_pagination"
        :headers="locallang === 'en' ? headers_en : headers_th"
        :items="table2"
        item-key="name"
        :calculate-widths="widths"
        dense
        class="fixed_header"
        >
        <template
        v-slot:[`item.sku`]="{ item }"
        :width="headers.width"
        >
        <span style="font-size: 10px; font-weight: 301">{{
          item.sku
        }}</span>
      </template>
      <template v-slot:[`item.name`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.name
        }}</span>
      </template>
      <template v-slot:[`item.quantity`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.quantity).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.price).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.discount_percent`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.discount_percent
        }}</span>
      </template>
      <template v-slot:[`item.net_price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.net_price).toLocaleString()
        }}</span>
      </template>
    </v-data-table>
  </v-card>
</v-col>
<v-col cols="12" md="12" class="mt-2">
  <QuotationFooter />
</v-col>
</v-row>
</div>
<!-- เป็นเงื่อนไขสุดท้าย ที่แสดง เพจ 2 เต็มๆ และเพจ 3 ครึ่งหน้า -->
<div v-if="table_body.length > 42 && table_body.length > 50" class="page mt-2 elevation-0">
  <!-- แสดงผล table 2 แบบเต็มๆ  -->
  <v-row>
    <v-col cols="12" md="12" class="pa-0">
      <span>{{shopName}}</span>
    </v-col>
    <v-col cols="12" md="12" class="pa-0">
      <v-card outlined>
        <v-data-table
        hide-default-footer
        disable-sort
        :disable-pagination="disable_pagination"
        :headers="locallang === 'en' ? headers_en : headers_th"
        :items="table2"
        item-key="name"
        :calculate-widths="widths"
        dense
        class="fixed_header"
        >
        <template
        v-slot:[`item.sku`]="{ item }"
        :width="headers.width"
        >
        <span style="font-size: 10px; font-weight: 301">{{
          item.sku
        }}</span>
      </template>
      <template v-slot:[`item.name`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.name
        }}</span>
      </template>
      <template v-slot:[`item.quantity`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.quantity).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.price).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.discount_percent`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.discount_percent
        }}</span>
      </template>
      <template v-slot:[`item.net_price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.net_price).toLocaleString()
        }}</span>
      </template>
    </v-data-table>
  </v-card>
</v-col>
</v-row>
<div v-if="table_body.length > 50" class="page mt-2 elevation-0">
  <!-- เป็นการแสดง เพจ 3 ครึ่งหน้า เมื่อ  page 2 เต็มหน้า -->
  <v-row dense>
    <v-col cols="12" md="12">
      <v-card class="elevation-0">
        <v-row>
          <v-col cols="12" md="12" class="pa-0">
            <span>{{shopName}}</span>
          </v-col>
          <v-col cols="12" md="12" class="pa-0">
            <v-card outlined>
              <v-data-table
              hide-default-footer
              disable-sort
              :disable-pagination="disable_pagination"
              :headers="locallang === 'en' ? headers_en : headers_th"
              :items="table3"
              item-key="name"
              :calculate-widths="widths"
              dense
              class="fixed_header"
              >
              <template
              v-slot:[`item.sku`]="{ item }"
              :width="headers.width"
              >
              <span style="font-size: 10px; font-weight: 301">{{
                item.sku
              }}</span>
            </template>
            <template v-slot:[`item.name`]="{ item }">
              <span style="font-size: 10px; font-weight: 301">{{
                item.name
              }}</span>
            </template>
            <template v-slot:[`item.quantity`]="{ item }">
              <span style="font-size: 10px; font-weight: 301">{{
                Number(item.quantity).toLocaleString()
              }}</span>
            </template>
            <template v-slot:[`item.price`]="{ item }">
              <span style="font-size: 10px; font-weight: 301">{{
                Number(item.price).toLocaleString()
              }}</span>
            </template>
            <template v-slot:[`item.discount_percent`]="{ item }">
              <span style="font-size: 10px; font-weight: 301">{{
                item.discount_percent
              }}</span>
            </template>
            <template v-slot:[`item.net_price`]="{ item }">
              <span style="font-size: 10px; font-weight: 301">{{
                Number(item.net_price).toLocaleString()
              }}</span>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
  </v-card>
</v-col>
<v-col cols="12" md="12" class="mt-2">
  <QuotationFooter />
</v-col>
</v-row>
</div>
</div>
<!-- จบ div นอก -->
</div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    QuotationTitle: () => import('@/components/QuotationCart/QuotationTitle'),
    QuotationHeader: () => import('@/components/QuotationCart/QuotationHeader'),
    QuotationFooter: () => import('@/components/QuotationCart/QuotationFooter')
  },
  data: () => ({
    locallang: '',
    printvisible: true,
    disable_pagination: true,
    widths: true,
    productName: '',
    table1: [],
    table2: [],
    table3: [],
    table_body: [],
    headers_th: [
      {
        text: 'รหัสสินค้า',
        value: 'sku',
        align: 'center',
        divider: true,
        width: 100
      },
      { text: 'ชื่อสินค้า', value: 'product_name', align: 'center', divider: true },
      {
        text: 'จำนวน',
        value: 'quantity',
        align: 'center',
        divider: true,
        width: 50
      },
      {
        text: 'ราคา',
        value: 'price',
        align: 'center',
        divider: true,
        width: 50
      },
      {
        text: 'ส่วนลด (%)',
        value: 'discount_percent',
        align: 'center',
        divider: true,
        width: 50
      },
      { text: 'ราคารวม', value: 'net_price', align: 'center', width: 50 }
    ],
    headers_en: [
      {
        text: 'SKU',
        value: 'sku',
        align: 'center',
        divider: true,
        width: 50
      },
      { text: 'Product Name', value: 'product_name', align: 'center', divider: true },
      {
        text: 'Quantity',
        value: 'quantity',
        align: 'center',
        divider: true,
        width: 50
      },
      {
        text: 'Price',
        value: 'price',
        align: 'center',
        divider: true,
        width: 50
      },
      {
        text: 'Discount (%)',
        value: 'discount_percent',
        align: 'center',
        divider: true,
        width: 50
      },
      { text: 'Net Price', value: 'net_price', align: 'center', width: 50 }
    ],
    shopName: ''
  }),
  computed: {
    orderType () {
      return localStorage.getItem('typeOrder')
    }
  },
  created () {
    this.locallang = localStorage.getItem('StorageLanguage')
    this.getOrderDetail()
  },
  methods: {
    printpo () {
      setTimeout(() => {
        window.print()
      }, 500)
      this.printvisible = false
      setTimeout(() => {
        this.printvisible = true
      }, 2000)
    },
    getOrderDetail () {
      var OrderDetail = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('PDF_Data'))))
      // console.log('OrderDetail', OrderDetail)
      this.shopName = OrderDetail.shop_name
      this.table_body = OrderDetail.product_list
      // console.log('table body', this.table_body)
      // looptable1
      // console.log('table body', this.table_body.length)
      if (this.table_body.length < 12 && this.table_body.length < 19) {
        for (let i = 0; i < this.table_body.length; i++) {
          const element = this.table_body[i]
          this.table1.push(element)
        }
        // console.log('loop<12', this.table1)
      } else if (this.table_body.length > 12) {
        // console.log('loop<19')
        for (let i = 0; i < this.table_body.length; i++) {
          const element = this.table_body[i]
          this.table1.push(element)
          // max20
        }
      } else {
        // console.log('else1')
      }
      // looptable2
      if (this.table_body.length >= 20 && this.table_body.length <= 42) {
        // console.log('loop >20')
        for (let i = 20; i < this.table_body.length; i++) {
          const element = this.table_body[i]
          this.table2.push(element)
          // max42
        }
      } else if (this.table_body.length > 42 && this.table_body.length <= 50) {
        // console.log('เงื่อนไขนี้นะ')
        for (let i = 20; i < this.table_body.length; i++) {
          const element = this.table_body[i]
          this.table2.push(element)
          // max49
        }
        // console.log('console.logเงื่อนไขนี้นะ', this.table2)
      } else if (this.table_body.length > 50) {
        // console.log('>40')
        for (let i = 20; i < this.table_body.length; i++) {
          // console.log('loop2>40')
          const element = this.table_body[i]
          this.table2.push(element)
          // max50
        }
        for (let i = 50; i < this.table_body.length; i++) {
          // console.log('loop2>40')
          const element = this.table_body[i]
          this.table3.push(element)
          // max50+
        }
      } else {
      }
      // console.log(this.table1.length)
      // console.log(this.table2.length)
    }
  }
}
</script>

<style scoped>
.page {
  width: 21cm;
  height: 29.7cm;
  padding: 1cm;
  margin: 0cm auto;
  border: 1px #d3d3d3 solid;
  border-radius: 5px;
  background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
@page {
  size: A4;
  margin: 0;
}
@page {
  size: A4;
  margin: 0;
}
@media print {
  .page {
    margin: 0;
    box-shadow: 0;
  }
  * {
    -webkit-print-color-adjust: exact;
  }
  .btnPrint {
    display: none;
  }
}
</style>
