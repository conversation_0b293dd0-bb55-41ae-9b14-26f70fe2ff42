<template>
  <v-container :class="MobileSize ? 'mt-3 px-0' : ''">
    <v-card elevation="0" width="100%" height="100%">
    <v-col :class="!MobileSize ? 'py-6 pl-6' : ''" cols="12">
        <span class="" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"
          v-if="!MobileSize">
          จัดการสินค้า Affiliate
        </span>
        <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
          <v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>
          จัดการสินค้า Affiliate
        </span>
        <!-- <v-row class="my-4">
          <v-col>
            <v-row style="align-items: baseline;" class="pl-3">
              <v-switch class="pl-1" false-value="no" true-value="yes" inset v-model="useAffiliateOrNot"></v-switch>
              <span class="pb-0" style="font-weight: 400; font-size:18px; line-height: 32px;">เปิด-ปิดการใช้งานโปรแกรม Affiliate</span>
            </v-row>
          </v-col>
        </v-row> -->
        <v-row dense class="mb-6 mt-2" v-if="!MobileSize">
          <v-col cols="12">
            <v-card width="100%" class="mt-4" height="100%" elevation="0" style="background: #F4F9FB; border-radius: 8px;">
              <v-card-text class="pa-0">
                <v-row>
                  <v-col cols="6" :style="IpadSize ? 'padding: 34px 0px 16px 16px;' : 'padding: 34px 0px 8px 16px;'">
                    <v-icon color="#27AB9C" class="px-2">mdi-storefront</v-icon>
                    <span style="font-size: 18px; font-weight: 400; line-height: 140%; color: #333333;" class="pt-2">ร้านค้า : </span><span style="font-size: 24px; font-weight: 700; line-height: 140%; color: #333333;" class="pt-2">{{ShopDetail.name}}</span><br/><br/>
                    <!-- <v-icon color="#27AB9C" class="px-2">mdi-cube-outline</v-icon>
                    <span style="font-size: 18px; font-weight: 400; line-height: 140%; color: #333333;" class="pt-2">จำนวนหมวดหมู่ที่เข้าร่วม : </span>
                    <span style="font-size: 20px; font-weight: 700; line-height: 140%; color: #27AB9C;" class="pt-2 classTextCountProduct">{{ Category.length }}</span><br/><br/> -->
                    <v-icon color="#27AB9C" class="px-2">mdi-cube-outline</v-icon>
                    <span style="font-size: 18px; font-weight: 400; line-height: 140%; color: #333333;" class="pt-2">จำนวนรายการสินค้าที่เข้าร่วม : </span>
                    <span style="font-size: 20px; font-weight: 700; line-height: 140%; color: #27AB9C;" class="pt-2 classTextCountProduct">{{ ProductList.length }}</span><br/><br/>
                  </v-col>
                  <v-col cols="6" class="pt-0 pb-2" style="display: flex; justify-content: flex-end;">
                    <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/ShopNew.png')" max-height="100%" max-width="379" width="100%" height="100%"></v-img>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <v-form ref="FormProductAffiliate">
          <v-col class="px-0" style="font-size: 16px;">
            <span><b>สินค้าที่เข้าร่วม</b></span>
          </v-col>
          <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
            <!-- {{ productList }} -->
            <!-- <treeselect @click="clearSelect(productList)" v-model="productList" :defaultExpandLevel="1" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :clearable="false" :options="dataListPrime" :normalizer="normalizer" :multiple="true" :openDirection="(MobileSize || IpadSize || IpadProSize) ? 'bottom' : 'top'" placeholder="เลือกสินค้าที่เข้าร่วม"/> -->
            <v-treeview v-model="productList" selection-type="leaf" selectable :open="open" :items="dataListPrime"></v-treeview>
            <!-- <treeselect @input="clearSelect(productList)" :value-consists-of="valueConsistsOf" v-model="productList" :defaultExpandLevel="1" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :clearable="false" :options="dataListPrime" :normalizer="normalizer" :multiple="true" :openDirection="(MobileSize || IpadSize || IpadProSize) ? 'bottom' : 'top'" placeholder="เลือกสินค้าที่เข้าร่วม"/> -->
          </div>
          <!-- <template v-if="!productList.length">
            No nodes selected. {{ productList }}
          </template>
          <template v-else>
            <div
              v-for="node in productList"
              :key="node.id"
            >
              {{ node.name }}
            </div>
          </template> -->
          <!-- {{ productList }} -->
          <!-- <v-col v-if="categoriesWithCommission.some(Cat => (Cat.commission === '' ? true : false))" cols="12" class="mt-4 pa-0">
            <span style="color: red; font-size: 16px; font-weight: 600; ">* กรุณากำหนดอัตราค่าคอมมิชชั่นของ <span style="color: #1AB759; font-weight: 700;">หมวดหมู่</span> ที่เลือกไว้ก่อน</span>
          </v-col> -->
          <v-col v-if="productWithCommission.some(Pro => (Pro.commission === '' || Pro.commission === '0' || Pro.commission === 0 || Pro.commission === null ? true : false))" cols="12" class="mt-4 pa-0">
            <v-card class="pa-4" style="border-radius: 8px; border: 1.5px solid #27AB9C" outlined>
              <span style="color: red; font-size: 16px; font-weight: 600; ">* กรุณากำหนดอัตราค่าคอมมิชชั่นของ <span style="color: #E9A016; font-weight: 700;">สินค้า</span> โดยกดปุ่ม "จัดการอัตราค่าคอมมิชชั่น" ด้านล่าง</span>
            </v-card>
          </v-col>
          <v-col cols="12" class="mt-4 pa-0">
            <a-tabs @change="SelectTabs">
              <!-- <a-tab-pane key="test"><span slot="tab">test <a-tag color="#E9A016" style="border-radius: 8px;">test</a-tag></span></a-tab-pane>
              <a-tab-pane key="หมวดหมู่"><span slot="tab">หมวดหมู่ <a-tag color="#1AB759" style="border-radius: 8px;">{{Category.length}}</a-tag></span></a-tab-pane> -->
              <a-tab-pane key="สินค้า"><span slot="tab">สินค้า <a-tag color="#E9A016" style="border-radius: 8px;">{{ProductList.length}}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col>
        <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3 mt-0" v-if="CategoryTab">
          <!-- {{categoriesWithCommission}} -->
          <v-col v-if="Category.length !== 0">
            <v-col class="px-0 pb-6"><span  style="font-size: 18px;"><b>หมวดหมู่</b></span></v-col>
            <v-row class="pb-6 pl-3"><span>หมวดหมู่สินค้าที่เข้าร่วมทั้งหมด {{ Category.length }} หมวดหมู่ </span></v-row>
              <template>
                <!-- <v-form ref="FormProductFlash2"> -->
                  <v-data-table
                    dense
                    :headers="headerscategory"
                    :items="categoriesWithCommission"
                    item-key="name"
                    class="elevation-1"
                    :items-per-page="10"
                  >
                  <template v-slot:[`item.number`]="{ item }">
                    <span>{{ categoriesWithCommission.indexOf(item) + 1 }}</span>
                  </template>
                  <!-- <template v-slot:[`item.imgflash`]="{ item }">
                    <v-card class="pa-1" width="100px" height="100px" elevation="0" contain>
                      <v-img width="100px" height="90px" v-if="item.images_URL !== '' && item.images_URL !== null && item.images_URL !== undefined" :src="item.images_URL"></v-img>
                      <v-img width="100px" v-else src="@/assets/NoImage.png" contain></v-img>
                    </v-card>
                  </template> -->
                  <template v-slot:[`item.detail`]="{ item }">
                    <!-- <span> {{ item.category_name }} </span> <br> -->
                    <b> {{ item.category_name }} </b>
                  </template>
                  <template v-slot:[`item.commission`]="{ item }">
                    <!-- <v-text-field
                      v-model="item.commission"
                        placeholder="ระบุค่าคอมมิชชั่นเป็น %"
                        outlined
                        dense
                        suffix="%"
                        class="mt-5"
                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                        @input="realPriceNoGoodCat(item, categoriesWithCommission.indexOf(item))"
                        :rules="rules"
                    ></v-text-field> -->
                    <v-chip v-if="item.commission !== '' && item.commission !== null && item.commission !== undefined && item.commission !== '0' && item.commission !== '0'" :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">สูงสุดถึง {{item.commission}} %</v-chip>
                    <v-chip v-else :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#F5222D">ยังไม่ได้กำหนดอัตราค่าคอมมิชชั่น</v-chip>
                  </template>
                  <template v-slot:[`item.actions`]="{ item }">
                    <v-btn :disabled="useAffiliateOrNot !== 'yes'"
                      x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="openSetCom(item)">
                      <v-icon color="#27AB9C" small>mdi-cog</v-icon>
                    </v-btn>
                    <v-btn color="#FFCDD2" :disabled="useAffiliateOrNot !== 'yes'"
                      x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="DeleteCategoryFromTable(item)">
                      <v-icon color="red" small>mdi-delete-outline</v-icon>
                    </v-btn>
                  </template>
                  </v-data-table>
                <!-- </v-form> -->
              </template>
          </v-col>
          <v-col v-if="Category.length === 0" cols="12" align="center" class="mt-10">
            <div class="mb-5">
              <v-img src="@/assets/New_No_Favorite.png" width="250" height="250" contain></v-img>
            </div>
            <div>
              <span style="font-size: 18px; font-weight: 700;">ไม่พบหมวดหมู่ที่เข้าร่วม</span>
            </div>
          </v-col>
        </v-row>
        <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3 mt-0" v-if="ProductTab">
          <v-col v-if="ProductList.length !== 0">
            <v-col class="px-0 pb-6"><span  style="font-size: 18px;"><b>รายการสินค้า</b></span></v-col>
            <v-row class="pb-6 pl-3 align-center">
              <v-col :cols="MobileSize ? 12 : 8" class="pa-0">
                <span>รายการสินค้าที่เข้าร่วมทั้งหมด {{ ProductList.length }} รายการ </span>
              </v-col>
              <v-col :cols="MobileSize ? 12 : 4" :class="MobileSize ? 'px-0' : 'pa-0 pr-3 d-flex justify-end'">
                <v-btn width="100%" rounded color="#27AB9C" class="white--text" @click="filterDataListPrime()">จัดการอัตราค่าคอมมิชชั่น</v-btn>
              </v-col>
            </v-row>
            <v-text-field class="mb-6" v-model="search" placeholder="ค้นหาจากชื่อสินค้า และ รหัส SKU" outlined dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
              <template>
                <!-- <v-form ref="FormProductFlash2"> -->
                  <v-data-table
                  dense
                  :headers="headers"
                  :items="filteredProducts"
                  class="elevation-1"
                  :items-per-page="10"
                  >
                  <template v-slot:[`item.number`]="{ item }">
                    <span>{{ filteredProducts.indexOf(item) + 1 }}</span>
                  </template>
                  <template v-slot:[`item.imgflash`]="{ item }">
                    <v-card class="pa-1" width="100px" height="100px" elevation="0" contain>
                      <v-img width="100px" height="90px" v-if="item.images_URL !== '' && item.images_URL !== null && item.images_URL !== undefined" :src="item.images_URL"></v-img>
                      <v-img width="100px" v-else src="@/assets/NoImage.png" contain></v-img>
                    </v-card>
                  </template>
                  <template v-slot:[`item.detail`]="{ item }">
                    <b>รหัส SKU:</b><span> {{ item.sku }} </span> <br>
                    <b>ชื่อสินค้า:</b><span> {{ item.name }} </span>
                  </template>
                  <template v-slot:[`item.commission`]="{ item }">
                    <!-- <v-text-field
                      v-model="item.commission"
                        placeholder="ระบุค่าคอมมิชชั่นเป็น %"
                        outlined
                        dense
                        suffix="%"
                        oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                        :rules="rules"
                        @input="realPriceNoGoodPro(item, productWithCommission.indexOf(item))"
                    ></v-text-field> -->
                    <v-chip v-if="item.commission !== '' && item.commission !== null && item.commission !== undefined && item.commission !== '0' && item.commission !== 0" :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">สูงสุดถึง {{item.commission}} %</v-chip>
                    <v-chip v-else :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#F5222D">ยังไม่ได้กำหนดอัตราค่าคอมมิชชั่น</v-chip>
                  </template>
                  <template v-slot:[`item.actions`]="{ item }">
                    <v-btn :disabled="useAffiliateOrNot !== 'yes'"
                      x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="openSetComPro(item)">
                      <v-icon color="#27AB9C" small>mdi-cog</v-icon>
                    </v-btn>
                    <!-- <v-btn color="#FFCDD2" :disabled="useAffiliateOrNot !== 'yes'"
                      x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="DeleteFromTable(item)">
                      <v-icon color="red" small>mdi-delete-outline</v-icon>
                    </v-btn> -->
                  </template>
                  </v-data-table>
                <!-- </v-form> -->
              </template>
          </v-col>
          <v-col v-if="ProductList.length === 0" cols="12" align="center" class="mt-10">
            <div class="mb-5">
              <v-img src="@/assets/New_No_Favorite.png" width="250" height="250" contain></v-img>
            </div>
            <div>
              <span style="font-size: 18px; font-weight: 700;">ไม่พบสินค้าที่เข้าร่วม</span>
            </div>
          </v-col>
        </v-row>
        <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3 mt-0" v-if="TestTab">
          <!-- <v-footer
            v-if="ShowFooter === true"
            v-bind="localAttrs"
            :padless="true"
            style="z-index: 2 !important; justify-self: center;"
            rounded
            width="90%"
          >
            <v-card
              flat
              tile
              width="100%"
              style="border-top-left-radius: 20px !important; border-top-right-radius: 20px !important; border: 1px solid #000 !important;"
            >
              <v-card-text>
                <v-row>
                  <v-col cols="4">
                    <span style="font-size: 16px; font-weight: 700;">รูปแบบ</span>
                    <v-radio-group
                      v-model="row"
                      row
                    >
                      <v-radio
                        label="เลือกทั้งหมด"
                        value="radio-1"
                      ></v-radio>
                      <v-radio
                        label="เลือกทั้งหมวดหมู่"
                        value="radio-2"
                      ></v-radio>
                      <v-radio
                        label="เลือกรายสินค้า"
                        value="radio-3"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col cols="2">
                    <span style="font-size: 16px; font-weight: 700;">ประเภท</span>
                    <v-radio-group
                      v-model="row"
                      row
                    >
                      <v-radio
                        label="เปอร์เซ็น"
                        value="radio-1"
                      ></v-radio>
                      <v-radio
                        label="บาท"
                        value="radio-2"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col cols="3">
                    <span style="font-size: 16px; font-weight: 700;">อัตราค่าคอมมิชชั่น</span>
                    <v-text-field outlined dense placeholder="ระบุอัตราค่าคอมมิชชั่น"></v-text-field>
                  </v-col>
                  <v-col cols="3" class="d-flex align-center">
                    <v-btn height="40" outlined rounded color="#27AB9C" class="mr-4" :style="{ flex: '1' }" @click="ShowFooter = !ShowFooter" >ยกเลิก</v-btn>
                    <v-btn rounded color="#27AB9C" class="white--text" :style="{ flex: '1' }" >บันทึก</v-btn>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-footer> -->
          <v-sheet
          height="100%"
          class="overflow-hidden"
          style="position: relative; width: -webkit-fill-available;"
        >
      <v-container class="fill-height">
        <v-row
          align="center"
          justify="start"
          class="mb-4"
        >
        <v-col cols="12">
          <span style="font-size: 16px;">ค้นหาชื่อสินค้า </span>
          <v-text-field outlined dense></v-text-field>
        </v-col>
        <!-- <v-col cols="6" class="justify-end d-flex">
          <v-btn v-if="ShowFooter === false" v-model="ShowFooter" @click="ShowFooter = !ShowFooter" color="#27AB9C" rounded dark>แก้ไขอัตราค่าคอมมิชชั่น</v-btn>
        </v-col> -->
        <v-row dense class="d-flex align-center">
          <v-col cols="6" class="py-0">
            <span style="font-size: 16px;">หมวดหมู่: <b>{{CategoryName === 'Default Category' ? 'สินค้าทั้งหมด' : CategoryName}}</b></span>
            <v-btn
            color="#27AB9C"
            rounded
            dark
            @click.stop="drawer = !drawer"
            v-click-outside="{
              handler: onClickOutsideStandard,
              include: include,
            }"
            class="ml-2"
            >
            เลือกหมวดหมู่
          </v-btn>
        </v-col>
        <v-col cols="6" class="py-0 d-flex justify-end">
          <!-- <v-row v-if="CategoryName !== 'Default Category'" style="align-items: baseline;" class="px-4 ">
            <v-switch class="pl-1" false-value="no" true-value="yes" inset v-model="useAffiliateOrNot"></v-switch>
            <span class="" style="font-weight: 400; font-size:16px; line-height: 32px;">เปิด-ปิดหมวดหมู่ Affiliate</span>
          </v-row> -->
          <v-checkbox v-if="CategoryName === 'Default Category'" label="เลือกสินค้าทั้งหมด"></v-checkbox>
          <v-checkbox v-else v-model="selectAll" label="เลือกสินค้าทั้งหมดในหมวดหมู่นี้" @change="selectAll ? toggleSelection(true) : toggleSelection(false)"></v-checkbox>
        </v-col>
        </v-row>
    </v-row>
        <v-col class="py-0">
          <v-col v-if="AllChangeProduct.length !== 0">
            <v-row>
              <v-col align="center" v-for="(item, index) in paginated" :key="index" class="px-2" :class="IpadSize ? 'cardProductIpad' :IpadProSize ? 'cardProductIpadPro' : 'cardProductDesk'">
                <CardProducts :itemProduct='item' :selectAll="item.selected" align="start"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-pagination
                  color="#27AB9C"
                  v-model="pageNumber"
                  :length="pageMax"
                  circle
                  @input="pageChange($event)"
                > </v-pagination>
              </v-col>
            </v-row>
          </v-col>
          <v-col v-else>
            <v-row justify="center" align-content="center" style="height: 200px;">
              <!-- <template> -->
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '20px', marginTop: '60px' }"
                >
                  <h1 slot="description"
                  style="
                    color: #636363;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                  ">ยังไม่มีสินค้า
                  </h1>
                  <br />
                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              <!-- </template> -->
            </v-row>
          </v-col>
        </v-col>
      </v-container>
      <v-navigation-drawer
        v-model="drawer"
        class="included" @click="() => null"
        absolute
      >
        <v-list-item class="included" @click="() => null">
          <!-- <v-list-item-content>
            <v-icon>mdi-view-grid</v-icon>
          </v-list-item-content> -->
          <v-list-item-content>
            <v-list-item-title style="font-size: 18px !important;font-weight: 700;">หมวดหมู่</v-list-item-title>
          </v-list-item-content>
        </v-list-item>

        <v-divider></v-divider>

        <v-list dense nav style="border-radius: 8px; cursor: pointer;" class="included" @click="() => null">
          <template v-for="(item, i) in DataCategory">
            <v-list-group v-if="item.children.length !== 0" :key="i" >
              <template v-slot:activator>
                <v-list-item-content style="border-radius: 8px; cursor: pointer;">
                    <v-list-item-title ><v-icon :color="categoryID === item.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon> <span @click="selectCategory(item.ms_category_id, item.name)" style="font-size: 16px; cursor: pointer;" :style="categoryID === item.ms_category_id ? 'color: rgb(39, 171, 156); cursor: pointer;' : ''">{{ item.category_name }}</span></v-list-item-title>
                </v-list-item-content>
              </template>
              <v-list-item v-for="(record, i) in item.children" :key="i" class="cat" @click="selectCategory(record.ms_category_id)" :style="categoryID === record.ms_category_id ? 'background-color: #E9FFF8; cursor: pointer;' : ''">
                <v-list-item-content class="ml-4">
                    <v-list-item-title> <v-icon :color="categoryID === record.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon><span :style="categoryID === record.ms_category_id ? 'font-size: 16px; color: #27AB9C; font-weight: 600; cursor: pointer;': 'font-size: 16px; font-weight: 400; cursor: pointer;'">{{ record.category_name }}</span></v-list-item-title>
                </v-list-item-content>
              </v-list-item>
          </v-list-group>
            <v-list-item-content v-else :key="`else-${i}`" class="cat px-2"  @click="selectCategory(item.ms_category_id)" :style="categoryID === item.ms_category_id ? 'background-color: #E9FFF8; font-size: 16px; color: #27AB9C; font-weight: 600; cursor: pointer;' : 'font-size: 16px; font-weight: 400; cursor: pointer;'" style="border-radius: 8px; cursor: pointer;">
              <v-list-item-title ><v-icon :color="categoryID === item.ms_category_id ? '#27AB9C':''">mdi-circle-small</v-icon> <span>{{ item.category_name }}</span></v-list-item-title>
            </v-list-item-content>
          </template>
        </v-list>
      </v-navigation-drawer>
          </v-sheet>
        </v-row>
        <!-- <v-row justify="space-between" class="px-5 mt-6">
          <v-btn :disabled="disSave" width="140" height="40" outlined rounded color="#27AB9C" @click="cancelSave()">ยกเลิก</v-btn>
          <v-btn :disabled="disSave" width="140" height="40" rounded color="#27AB9C" class="white--text" @click="AddProduct()">บันทึก</v-btn>
        </v-row> -->
        </v-form>
    </v-col>
  </v-card>
    <v-dialog v-model="DialogSetCom" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 80px;">
            <v-row style="height: 80px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>ตั้งค่าอัตราค่าคอมมิชชั่น</b></span>
              </v-col>
              <v-btn fab small @click="cancelSetCom()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card :class="MobileSize ? 'pt-6 pb-4 px-2' : 'pt-6 pb-4 px-12'" elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-form ref="FormProductAffiliate" :lazy-validation="lazy">
                <template>
                    <v-data-table
                      dense
                      :headers="headerscategory2"
                      :items="CatData"
                      item-key="name"
                      class="elevation-1"
                      :items-per-page="5"
                      hide-default-footer
                    >
                    <template v-slot:[`item.detail`]="{ item }">
                      <b> {{ item.category_name }} </b>
                    </template>
                    <template v-slot:[`item.commission`]="{ item }">
                      <v-text-field
                        v-model="item.commission"
                          placeholder="ระบุค่าคอมมิชชั่นเป็น %"
                          :style="MobileSize ? 'width: 100%;' : 'width: 50%;'"
                          outlined
                          dense
                          suffix="%"
                          class="mt-5"
                          oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                          @input="realPriceNoGoodCat(item, CatData.indexOf(item))"
                          :rules="rules"
                      ></v-text-field>
                    </template>
                    </v-data-table>
                </template>
              </v-form>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions class="py-0 justify-space-between d-flex" style="height: 88px; background-color: #F5FCFB;">
          <v-btn width="140" height="40" outlined rounded color="#27AB9C" @click="cancelSetCom()">ยกเลิก</v-btn>
          <v-btn width="140" height="40" rounded color="#27AB9C" class="white--text" @click="AddProduct()">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogSetComPro" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 80px;">
            <v-row style="height: 80px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>ตั้งค่าอัตราค่าคอมมิชชั่น</b></span>
              </v-col>
              <v-btn fab small @click="cancelSetComPro()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card :class="MobileSize ? 'pt-6 pb-4 px-2' : 'pt-6 pb-4 px-12'" elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-form ref="FormProductAffiliate" :lazy-validation="lazy">
                <template>
                    <v-data-table
                      dense
                      :headers="headers2"
                      :items="ProData"
                      item-key="name"
                      class="elevation-1"
                      :items-per-page="5"
                      hide-default-footer
                    >
                    <template v-slot:[`item.imgflash`]="{ item }">
                      <v-card class="pa-1" width="100px" height="100px" elevation="0" contain>
                        <v-img width="100px" height="90px" v-if="item.images_URL !== '' && item.images_URL !== null && item.images_URL !== undefined" :src="item.images_URL"></v-img>
                        <v-img width="100px" v-else src="@/assets/NoImage.png" contain></v-img>
                      </v-card>
                    </template>
                    <template v-slot:[`item.detail`]="{ item }">
                      <b>รหัส SKU:</b><span> {{ item.sku }} </span> <br>
                      <b>ชื่อสินค้า:</b><span> {{ item.name }} </span>
                    </template>
                    <template v-slot:[`item.commission`]="{ item }">
                      <v-text-field
                        v-model="item.commission"
                          placeholder="ระบุค่าคอมมิชชั่นเป็น %"
                          outlined
                          dense
                          suffix="%"
                          class="mt-5"
                          oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                          @input="realPriceNoGoodPro(item, ProData.indexOf(item)), checkRules(parseFloat(item.commission))"
                          :rules="rules"
                      ></v-text-field>
                    </template>
                    </v-data-table>
                </template>
              </v-form>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions class="py-0 justify-space-between d-flex" style="height: 88px; background-color: #F5FCFB;">
          <v-btn width="140" height="40" outlined rounded color="#27AB9C" @click="cancelSetComPro()">ยกเลิก</v-btn>
          <v-btn :disabled="checkTrueFalse" width="140" height="40" rounded color="#27AB9C" class="white--text" @click="AddProduct()">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogSelect" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 80px;">
            <v-row style="height: 80px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>ตั้งค่าอัตราค่าคอมมิชชั่น</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogSetComCat()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card :class="MobileSize ? 'pt-6 pb-4 px-2' : 'pt-6 pb-4 px-12'" elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <span>หมวดหมู่ที่เลือกไว้</span>
              <!-- {{ dataListCat }} -->
              <v-treeview v-model="productListCat" selection-type="leaf" selectable :open="open" :items="dataListCat"></v-treeview>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="py-0 justify-space-between d-flex" style="height: 88px; background-color: #F5FCFB;">
          <div>
            <v-btn width="140" height="40" outlined rounded color="#27AB9C" @click="closeDialogSetComCat()">ยกเลิก</v-btn>
          </div>
          <div>
            <!-- <v-btn :disabled="productListCat.length === 0" width="auto" height="40" rounded color="red" class="white--text mr-4" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ลบอัตราค่าคอมมิชชั่น</v-btn> -->
            <v-btn :disabled="productListCat.length === 0" width="auto" height="40" rounded color="#27AB9C" class="white--text" @click="OpenSetCom()">แก้ไขอัตราค่าคอมมิชชั่น</v-btn>
          </div>
        </v-card-actions>
        <v-card-actions v-else class="py-0 d-block" style="height: 100px; background-color: #F5FCFB;">
          <v-col cols="12">
            <!-- <v-btn :disabled="productListCat.length === 0" width="auto" height="40" rounded color="red" class="white--text mr-4" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ลบอัตราค่าคอมมิชชั่น</v-btn> -->
            <v-btn :disabled="productListCat.length === 0" width="100%" height="40" rounded color="#27AB9C" class="white--text" @click="OpenSetCom()">แก้ไขอัตราค่าคอมมิชชั่น</v-btn>
          </v-col>
          <v-col cols="12">
            <v-btn width="100%" height="40" outlined rounded color="#27AB9C" @click="closeDialogSetComCat()">ยกเลิก</v-btn>
          </v-col>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogSetCommission" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 80px;">
            <v-row style="height: 80px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>ตั้งค่าอัตราค่าคอมมิชชั่น</b></span>
              </v-col>
              <v-btn fab small @click="closeSetCom()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card :class="MobileSize ? 'pt-6 pb-4 px-2' : 'pt-6 pb-4 px-12'" elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-col>
                <span>ประเภทของอัตราค่าคอมมิชชั่น</span>
                <v-select disabled outlined dense v-model="ItemsSelect" :items="itemsSelect"></v-select>
              </v-col>
              <v-col>
                <span>อัตราค่าคอมมิชชั่น</span>
                <v-text-field
                  v-model="commissionRATE"
                  placeholder="ระบุค่าคอมมิชชั่นเป็น %"
                  style="width: 100%;"
                  outlined
                  dense
                  suffix="%"
                  oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                  @input="checkRules(parseFloat(commissionRATE))"
                  :rules="rules"></v-text-field>
              </v-col>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions class="py-0 justify-space-between d-flex" style="height: 88px; background-color: #F5FCFB;">
          <v-btn width="140" height="40" outlined rounded color="#27AB9C" @click="closeSetCom()">ยกเลิก</v-btn>
          <v-btn :disabled="checkTrueFalse" width="140" height="40" rounded color="#27AB9C" class="white--text" @click="SetCom(commissionRATE)">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบอัตราค่าคอมมิชชั่น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่
              หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4"
                @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                @click="DeleteSetCom()">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense>
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }"
                @click="DeleteSetCom()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
</v-container>
</template>
<script>
// import Treeselect from '@riophae/vue-treeselect'
// import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { Tabs, Tag, Empty } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    'a-empty': Empty
    // Treeselect,
    // CardProducts: () => import('@/components/Card/ProductCardAffiliateSellerUI')
  },
  data () {
    return {
      checkTrueFalse: true,
      dataListCat: [],
      search: '',
      dialogAwaitConfirm: false,
      deleteSetCom: [],
      productSetCom: [],
      commissionRATE: '',
      ItemsSelect: 'เปอร์เซ็น',
      itemsSelect: ['เปอร์เซ็น', 'บาท'],
      DialogSetCommission: false,
      DialogSelect: false,
      productListCat: [],
      open: [],
      selectAllPerPage: [],
      selected: false,
      selectAll: false,
      selectCount: 0,
      current: 1,
      ShowFooter: false,
      icons: [
        'mdi-home',
        'mdi-email',
        'mdi-calendar',
        'mdi-delete'
      ],
      items2: [
        'default',
        'absolute',
        'fixed'
      ],
      padless: false,
      variant: 'fixed',
      CategoryName: '',
      AllChangeProduct: [],
      categoryID: '',
      DataCategory: [],
      drawer: false,
      items: [
        { title: 'Home', icon: 'mdi-view-dashboard' },
        { title: 'About', icon: 'mdi-forum' }
      ],
      lazy: false,
      valueConsistsOf: 'BRANCH_PRIORITY',
      Text: 'เพิ่ม',
      useAffiliateOrNot: 'yes',
      CatData: [],
      ProData: [],
      DialogSetCom: false,
      DialogSetComPro: false,
      CategoryTab: false,
      ProductTab: true,
      TestTab: false,
      StateStatus: 'สินค้า',
      checkCom: true,
      disSave: true,
      CommissionData: [],
      ProductListData: [],
      RawListData: [],
      CategoryData: [],
      ShopDetail: '',
      errorMessage: '',
      rules: [
        v => !!v || 'กรุณากรอกข้อมูล',
        v => (v < 100 && v > 0) || 'กรุณากรอกจำนวนที่มากกว่า 0 และ ไม่เกิน 100'
      ],
      ProductList: [],
      itemFlashSalePhase2: [],
      Category: [],
      headers: [
        { text: 'ลำดับที่', value: 'number', sortable: false, width: '100px' },
        { text: 'รูปภาพสินค้า', value: 'imgflash', sortable: false, width: '100px' },
        { text: 'รายละเอียดสินค้า', value: 'detail', sortable: false, width: '150px' },
        { text: 'อัตราค่าคอมมิชชั่น', value: 'commission', sortable: false, width: '250px' },
        { text: 'จัดการ', value: 'actions', sortable: false, width: '200px' }
      ],
      headers2: [
        { text: 'รูปภาพสินค้า', value: 'imgflash', sortable: false },
        { text: 'รายละเอียดสินค้า', value: 'detail', sortable: false },
        { text: 'อัตราค่าคอมมิชชั่น', value: 'commission', sortable: false }
      ],
      headerscategory: [
        { text: 'ลำดับที่', value: 'number', sortable: false, width: '100px' },
        { text: 'หมวดหมู่', value: 'detail', sortable: false, width: '150px' },
        { text: 'อัตราค่าคอมมิชชั่น', value: 'commission', sortable: false, width: '250px' },
        { text: 'จัดการ', value: 'actions', sortable: false, width: '200px' }
      ],
      headerscategory2: [
        // { text: 'ลำดับที่', value: 'number', sortable: false },
        { text: 'หมวดหมู่', value: 'detail', sortable: false },
        // { text: 'ค่าคอมมิชชั่น', value: 'commission' },
        { text: 'อัตราค่าคอมมิชชั่น', value: 'commission', sortable: false }
      ],
      theRedP: true,
      normalizer (node) {
        let id
        let childrenKey
        let labelKey
        if (node.type === 'category') {
          id = 'hierachy'
          childrenKey = node.sub_category === null ? 'product_list' : 'sub_category'
          labelKey = 'category_name'
        } else if (node.type === 'product') {
          id = 'product_id'
          labelKey = 'product_name'
        } else if (node.type === 'special_1_category') {
          id = 'all_id'
          childrenKey = 'all'
          labelKey = 'all_name'
        }
        return {
          id: node[id],
          name: node[labelKey],
          children: node[childrenKey]
        }
      },
      // normalizer (node) {
      //   return {
      //     id: node.category_id,
      //     label: node.category_name,
      //     children: node.sub_category
      //   }
      // },
      productList: [],
      dataListPrime: [],
      shopSellerID: ''
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    this.ShopDetail = shopDetail
    this.shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
    this.pageNumber = parseInt(this.$route.query.page)
    this.categoryID = 1
    // this.selectCategory()
    await this.getdata()
    await this.SeletedProductList()
    // this.GetCategory()
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('updateSelectedAffiliate', this.updateSelected)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('updateSelectedAffiliate')
    })
  },
  watch: {
    dataListPrime: {
      handler (newValue) {
        this.setOpenNodes(newValue)
      },
      deep: true
    },
    async $route (to, from) {
      // console.log('to====>', to)
      // console.log('from====>', from)
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page
      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          this.pageNumber = parseInt(this.$route.query.page)
          this.pageChange(this.pageNumber)
        }
      }
    },
    StateStatus (val) {
      // console.log('StateStatus---->', val)
      this.StateStatus = val
      // if (val === 'หมวดหมู่') {
      //   this.TestTab = false
      //   this.CategoryTab = true
      //   this.ProductTab = false
      // } else if (val === 'สินค้า') {
      //   this.TestTab = false
      //   this.ProductTab = true
      //   this.CategoryTab = false
      // } else {
      //   this.TestTab = true
      //   this.ProductTab = false
      //   this.CategoryTab = false
      // }
      this.ProductTab = true
    },
    productList (N, O) {
      if (N.length < O.length) {
        var data = []
        this.disSave = false
        const differentItems = N.filter(item => !O.includes(item)).concat(O.filter(item => !N.includes(item)))
        if (differentItems.length !== 0) {
          if (typeof differentItems[0] !== 'number') {
            data.push({
              hierachy: differentItems[0]
            })
            this.DeleteCategoryFromTable(data[0])
            // if (this.productList.length === 0) {
            // this.Text = 'ลบ'
            // this.AddProduct()
            // }
            // this.AddProduct()
          } else {
            data.push({
              id: differentItems[0]
            })
            this.DeleteFromTable(data[0])
            // this.Text = 'ลบ'
            // this.AddProduct()
          }
        }
        this.AddProduct()
        this.updateFlash()
        // this.filterDataListPrime()
      }
      if (N.length > O.length) {
        this.updateFlash()
        // this.filterDataListPrime()
      }
      if (N.length === 0 && O.length === 0) {
        this.ProductList = []
        this.Category = []
      }
      if (!N.some(item => typeof item === 'number')) {
        this.ProductList = []
      }
      if (!N.some(item => typeof item === 'string')) {
        this.Category = []
      }
      // if (this.productList.length === 0 && O.length === 1 && N.length === 0) {
      //   this.Text = 'ลบ'
      //   this.AddProduct()
      // }
    },
    MobileSize (val) {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (val === true) {
        this.$router.push({ path: '/sellerShopMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      }
    }
  },
  computed: {
    filteredProducts () {
      const sortedProducts = [...this.productWithCommission].sort((a, b) => {
        const commissionA = a.commission !== null ? a.commission : 0
        const commissionB = b.commission !== null ? b.commission : 0
        return commissionA - commissionB
      })
      if (this.search === '') {
        return sortedProducts
      } else {
        return sortedProducts.filter(product =>
          product.name.includes(this.search) || product.sku.includes(this.search)
        )
      }
    },
    selectedItems () {
      var dataselect = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliateAll
      return dataselect.product_list ? dataselect.product_list.length : 0
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        this.selectAll = this.selectAllPerPage[this.pageNumber - 1]
        this.selectCount = 0
        window.scrollTo(0, 0)
        // window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    localAttrs () {
      const attrs = {}
      if (this.variant === 'fixed') {
        attrs.fixed = true
      }
      return attrs
    },
    paginated () {
      return this.AllChangeProduct
    },
    categoriesWithCommission () {
      // console.log('this.Categorycom', this.Category)
      return this.Category.map(category => {
        if (this.CommissionData.category.length !== 0) {
          var matchingRate = this.CommissionData.category.find(rate => rate.hierachy === category.hierachy)
        } else {
          matchingRate = this.CommissionData.raw_list.find(rate => rate.special === category.hierachy)
        }
        return {
          ...category,
          commission: matchingRate ? matchingRate.commission_rate : '0'
        }
      })
    },
    productWithCommission () {
      // console.log('this.ProductListcom', this.ProductList)
      return this.ProductList.map(product => {
        var matchingRate = this.CommissionData.product_list.find(rate => parseInt(rate.product_id) === parseInt(product.id))
        // console.log('matchingRate', matchingRate)
        return {
          ...product,
          commission: matchingRate ? matchingRate.commission_rate === 0 ? null : matchingRate.commission_rate : null
        }
      })
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    // confirmCancelReceiveAllLinks () {
    //   this.toggleSelection(false, true)
    //   this.selectAll = false
    //   this.showDialogCancel = false
    // },
    checkRules (val) {
      this.checkTrueFalse = true
      if (isNaN(val)) {
        this.checkTrueFalse = true
      } else if (val === null) {
        this.checkTrueFalse = true
      } else if (val >= 100 || val <= 0) {
        this.checkTrueFalse = true
      } else {
        this.checkTrueFalse = false
      }
      // console.log('val', typeof val)
      // console.log('checkTrueFalse', this.checkTrueFalse)
    },
    DeleteSetCom () {
      this.deleteSetCom = this.productList.filter(product =>
        this.productListCat.includes(product)
      )
      this.AddProduct()
      this.closeDialogSetComCat()
    },
    async SetCom (rate) {
      this.ItemsSelect = 'เปอร์เซ็น'
      // this.commissionRATE = ''
      this.productSetCom = this.productListCat.map(productId => ({
        product_id: productId,
        commission_rate: rate
      }))
      // console.log('this.productSetCom', this.productSetCom)
      await this.AddProduct()
      this.DialogSetCommission = false
      this.closeDialogSetComCat()
    },
    closeSetCom () {
      this.DialogSetCommission = false
      this.ItemsSelect = 'เปอร์เซ็น'
      this.commissionRATE = ''
    },
    OpenSetCom () {
      this.DialogSetCommission = true
    },
    closeDialogSetComCat () {
      this.productListCat = []
      this.DialogSelect = false
    },
    // Test () {
    //   console.log('productListCat', this.productListCat)
    // },
    async toggleSelection (selected, allPages = false) {
      // console.log(1)
      if (allPages) {
        // console.log(2)
        // ใช้การเลือกกับทุกหน้า
        this.AllChangeProduct.forEach((itemProduct) => {
          itemProduct.selected = selected

          const selectProduct = {
            seller_shop_id: itemProduct.seller_shop_id,
            product_id: itemProduct.id,
            product_name: itemProduct.name,
            sku: itemProduct.sku,
            price: itemProduct.real_price,
            commission_rate: itemProduct.commission_rate
          }

          const dataDetail = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate

          if (selected) {
            // console.log(3)
            if (dataDetail.length === 0) {
              dataDetail.push({ product_list: [selectProduct] })
            } else {
              const productExists = dataDetail[0].product_list.some(product => product.product_id === selectProduct.product_id)
              if (!productExists) {
                dataDetail[0].product_list.push(selectProduct)
              }
            }
          } else {
            if (dataDetail.length > 0) {
              dataDetail[0].product_list = dataDetail[0].product_list.filter(product => product.product_id !== selectProduct.product_id)
            }
          }

          const formattedData = {
            sub_type: this.subType,
            sub_id_1: '',
            sub_id_2: '',
            sub_id_3: '',
            sub_id_4: '',
            sub_id_5: '',
            product_list: dataDetail.length > 0 ? dataDetail[0].product_list : []
          }

          this.formattedData = formattedData
          this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)
        })

        // ตั้งค่า selectAllPerPage เป็น false สำหรับทุกเพจ
        this.selectAllPerPage = this.selectAllPerPage.map(() => false)
      } else {
        // console.log(4)
        // ใช้การเลือกกับหน้าปัจจุบันเท่านั้น
        this.selectAllPerPage[this.pageNumber - 1] = selected
        // console.log(5)
        this.AllChangeProduct.forEach((itemProduct, index) => {
          if (index <= (this.indexEnd - 1) && index >= (this.indexStart)) {
            itemProduct.selected = selected

            const selectProduct = {
              seller_shop_id: itemProduct.seller_shop_id,
              product_id: itemProduct.id,
              product_name: itemProduct.name,
              sku: itemProduct.sku,
              price: itemProduct.real_price,
              commission_rate: itemProduct.commission_rate
            }

            const dataDetail = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate
            // console.log('dataDetail', dataDetail)

            if (selected) {
              if (dataDetail.length === 0) {
                dataDetail.push({ product_list: [selectProduct] })
              } else {
                const productExists = dataDetail[0].product_list.some(product => product.product_id === selectProduct.product_id)
                if (!productExists) {
                  dataDetail[0].product_list.push(selectProduct)
                }
              }
            } else {
              if (dataDetail.length > 0) {
                dataDetail[0].product_list = dataDetail[0].product_list.filter(product => product.product_id !== selectProduct.product_id)
              }
            }

            const formattedData = {
              sub_type: this.subType,
              sub_id_1: '',
              sub_id_2: '',
              sub_id_3: '',
              sub_id_4: '',
              sub_id_5: '',
              product_list: dataDetail.length > 0 ? dataDetail[0].product_list : []
            }

            this.formattedData = formattedData
            // console.log('this.formattedData', this.formattedData)
            this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)
          }
        })
      }
    },
    updateSelected (selected, id) {
      this.AllChangeProduct.forEach((element, index) => {
        if (element.id === id) {
          element.selected = true
        }
      })

      if (selected) {
        this.selectCount = this.selectCount + 1
      } else {
        this.selectCount = this.selectCount - 1
      }

      if (this.selectCount === this.paginated.length) {
        this.selectAllPerPage[this.pageNumber - 1] = true
        this.selectAll = this.selectAllPerPage[this.pageNumber - 1]
      } else {
        this.selectAllPerPage[this.pageNumber - 1] = false
        this.selectAll = this.selectAllPerPage[this.pageNumber - 1]
      }
      // console.log('checkAll', this.selectCount, this.paginated.length)
      this.$forceUpdate()
    },
    include () {
      return [document.querySelector('.included')]
    },
    onClickOutsideStandard () {
      this.drawer = false
    },
    pageChange (val) {
      // console.log('val', val)
      this.$router.push(`${this.$route.path}?page=${val}`).catch(() => {})
      this.pageNumber = val
      this.selectCategory()
      // console.log('val')
      // this.$router.push(`/ListProduct/${this.typeProduct}?page=${val}`).catch(() => {})
    },
    openSetCom (item) {
      this.CatData = []
      this.CatData = [
        {
          category_name: item.category_name,
          category_id: item.category_id,
          hierachy: item.hierachy,
          commission: item.commission
        }
      ]
      this.DialogSetCom = true
    },
    openSetComPro (item) {
      // console.log(item)
      this.ProData = []
      this.ProData = [
        {
          id: item.id,
          seller_shop_id: item.seller_shop_id,
          name: item.name,
          have_attribute: item.have_attribute,
          short_description: item.short_description,
          sku: item.sku,
          inventory_code: item.inventory_code,
          message_status: item.message_status,
          inventory_ratio: item.inventory_ratio,
          stock_status: item.stock_status,
          stock_count: item.stock_count,
          inventory_stock: item.inventory_stock,
          images_URL: item.images_URL,
          revenue_price: item.revenue_price,
          real_price: item.real_price,
          fake_price: item.fake_price,
          discount_percent: item.discount_percent,
          stars: item.stars,
          fake_price_vat: item.fake_price_vat,
          real_price_vat: item.real_price_vat,
          vat_include: item.vat_include,
          vat_default: item.vat_default,
          sold: item.sold,
          commission: item.commission
        }
      ]
      this.DialogSetComPro = true
    },
    cancelSetCom () {
      this.DialogSetCom = false
    },
    cancelSetComPro () {
      this.DialogSetComPro = false
    },
    SelectTabs (item) {
      this.StateStatus = item
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    cancelSave () {
      this.disSave = true
      this.SeletedProductList()
    },
    findAndAddProduct (node, hierarchy, commissionRate, commissionType) {
      if (node.id === hierarchy) {
        this.extractIdsAndAddToNewProductList(node, commissionRate, commissionType)
      } else {
        if (node.children) {
          node.children.forEach(child => {
            this.findAndAddProduct(child, hierarchy, commissionRate, commissionType)
          })
        }

        if (node.sub_product) {
          node.sub_product.forEach(sub => {
            this.findAndAddProduct(sub, hierarchy, commissionRate, commissionType)
          })
        }
      }
    },
    extractIdsAndAddToNewProductList (node, commissionRate, commissionType) {
      if (!isNaN(node.id) && !this.newProductList.some(item => item.product_id === node.id)) {
        this.newProductList.push({
          product_id: node.id,
          commission_rate: commissionRate,
          commission_type: commissionType
        })
      }
      if (node.children) {
        node.children.forEach(child => {
          this.extractIdsAndAddToNewProductList(child, commissionRate, commissionType)
        })
      }
      if (node.sub_category) {
        node.sub_category.forEach(sub => {
          this.extractIdsAndAddToNewProductList(sub, commissionRate, commissionType)
        })
      }
    },
    async SeletedProductList () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopSellerID
      }
      await this.$store.dispatch('actionsSeletedProductList', data)
      const res1 = await this.$store.state.ModuleAffiliate.stateSeletedProductList
      if (res1.success === true) {
        this.$store.commit('closeLoader')
        this.disSave = true
        this.productList = []
        this.newProductList = []
        this.CommissionData = res1.data
        res1.data.product_list.forEach(element => {
          this.productList.push(parseInt(element.product_id))
        })
        // console.log('this.productList', this.productList)
        // if (res1.data.category.length !== 0) {
        //   res1.data.category.forEach(element => {
        //     this.findAndAddProduct(this.dataListPrime[0], element.hierachy, element.commission_rate, element.commission_type)
        //   })
        // }
        // if (res1.data.raw_list.length !== 0) {
        //   res1.data.raw_list.forEach(element => {
        //     this.findAndAddProduct(this.dataListPrime[0], element.special, element.commission_rate, element.commission_type)
        //   })
        // }
        // if (this.newProductList.length !== 0) {
        //   var NewProduct = res1.data.product_list.map(item => {
        //     return {
        //       product_id: item.product_id,
        //       commission_rate: item.commission_rate,
        //       commission_type: item.commission_type
        //     }
        //   })
        //   // console.log('object', NewProduct)
        //   NewProduct.forEach(newProduct => {
        //     const existingIndex = this.newProductList.findIndex(product => product.product_id === newProduct.product_id)
        //     if (existingIndex === -1) {
        //       this.newProductList.push(newProduct)
        //     } else {
        //       this.newProductList[existingIndex] = newProduct
        //     }
        //   })
        //   var dataNew = {
        //     seller_shop_id: this.shopSellerID,
        //     raw_list: [],
        //     category: [],
        //     product_list: this.newProductList
        //   }
        //   await this.$store.dispatch('actionsAddProductAffiliate', dataNew)
        //   const res = await this.$store.state.ModuleAffiliate.stateAddProductAffiliate
        //   if (res.success === true) {
        //     this.$store.commit('closeLoader')
        //     this.DialogSetCom = false
        //     this.DialogSetComPro = false
        //     this.dialogAwaitConfirm = false
        //     this.disSave = true
        //     this.checkTrueFalse = true
        //     this.Category = []
        //     this.productSetCom = []
        //     this.deleteSetCom = []
        //     this.ProductList = []
        //     this.RawListData = []
        //     this.CategoryData = []
        //     this.ProductListData = []
        //     this.commissionRATE = ''
        //     this.SeletedProductList()
        //     if (this.Text === 'เพิ่ม') {
        //       this.$swal.fire({ icon: 'success', text: 'ดำเนินการเสร็จสิ้น', showConfirmButton: false, timer: 2000 })
        //     } else {
        //       this.Text = 'เพิ่ม'
        //       this.$swal.fire({ icon: 'success', text: 'ดำเนินการเสร็จสิ้น', showConfirmButton: false, timer: 2000 })
        //     }
        //   } else {
        //     this.$store.commit('closeLoader')
        //     this.DialogSetCom = false
        //     this.DialogSetComPro = false
        //     this.dialogAwaitConfirm = false
        //     this.disSave = true
        //     this.checkTrueFalse = true
        //     this.Category = []
        //     this.productSetCom = []
        //     this.deleteSetCom = []
        //     this.ProductList = []
        //     this.RawListData = []
        //     this.CategoryData = []
        //     this.ProductListData = []
        //     this.commissionRATE = ''
        //     this.SeletedProductList()
        //     this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาลองใหม่ภายหลัง', showConfirmButton: false, timer: 2000 })
        //   }
        // }
        // console.log('newProductList', this.newProductList)
        // if (this.productList[0] === -1) {
        //   this.productList = []
        //   this.productList = ['special_1']
        //   this.CommissionData.raw_list.push({
        //     id: this.CommissionData.product_list[0].id,
        //     special: 'special_1',
        //     commission_rate: this.CommissionData.product_list[0].commission_rate
        //   })
        //   this.CommissionData.product_list = []
        // }
        if (this.productList.length !== 0) {
          this.updateFlash()
        }
      } else {
        this.$store.commit('closeLoader')
        if (res1.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาลองใหม่ภายหลัง', showConfirmButton: false, timer: 2000 })
        }
      }
    },
    async AddProduct () {
      this.$store.commit('openLoader')
      if (this.productList.length !== 0 || this.productList.length === 0) {
        this.checkCom = true
        this.Category = this.categoriesWithCommission
        this.CategoryData = this.Category.map(e => {
          return {
            category: e.category_name,
            hierachy: e.hierachy,
            commission_rate: e.commission,
            commission_type: 'percent'
          }
        })
        if (this.CatData.length !== 0) {
          this.CategoryData.forEach(element => {
            if (element.hierachy === this.CatData[0].hierachy) {
              element.commission_rate = this.CatData[0].commission
            }
          })
        }
        if (this.CategoryData.length !== 0) {
          if (this.CategoryData[0].category === 'ทั้งหมด') {
            this.RawListData = this.CategoryData.map(e => {
              return {
                special_1: e.hierachy,
                commission_rate: e.commission_rate,
                commission_type: 'percent'
              }
            })
            this.CategoryData = []
          } else {
            this.RawListData = []
          }
        }
        this.ProductList = this.productWithCommission
        this.ProductListData = this.ProductList.map(e => {
          return {
            product_id: parseInt(e.id),
            commission_rate: e.commission,
            commission_type: 'percent'
          }
        })
        if (this.ProData.length !== 0) {
          this.ProductListData.forEach(element => {
            if (element.product_id === this.ProData[0].id) {
              element.commission_rate = this.ProData[0].commission
            }
          })
        }
        if (this.productSetCom.length !== 0) {
          this.ProductListData.forEach(product => {
            const match = this.productSetCom.find(item => item.product_id === product.product_id)
            if (match) {
              product.commission_rate = match.commission_rate
            }
          })
        }
        if (this.deleteSetCom.length !== 0) {
          this.ProductListData = this.ProductListData.filter(product =>
            !this.deleteSetCom.includes(product.product_id)
          )
        }
        if (this.checkCom === true) {
          // const uniqueRawListData = [...new Set(this.RawListData)]
          // const uniqueCategoryData = [...new Set(this.CategoryData)]
          var uniqueProductListData = [...new Set(this.ProductListData)]
          if (this.productSetCom.length === 0 && this.deleteSetCom.length === 0) {
            var filteredUniqueProductListData = uniqueProductListData.filter(product => this.productList.includes(product.product_id))
          }
          // console.log('filteredUniqueProductListData', filteredUniqueProductListData)
          // console.log('uniqueProductListData', uniqueProductListData)
          var data = {
            seller_shop_id: this.shopSellerID,
            raw_list: [],
            category: [],
            product_list: filteredUniqueProductListData === undefined ? uniqueProductListData : filteredUniqueProductListData
          }
          await this.$store.dispatch('actionsAddProductAffiliate', data)
          const res = await this.$store.state.ModuleAffiliate.stateAddProductAffiliate
          if (res.success === true) {
            this.$store.commit('closeLoader')
            this.DialogSetCom = false
            this.DialogSetComPro = false
            this.dialogAwaitConfirm = false
            this.disSave = true
            this.checkTrueFalse = true
            this.Category = []
            this.productSetCom = []
            this.deleteSetCom = []
            this.ProductList = []
            this.RawListData = []
            this.CategoryData = []
            this.ProductListData = []
            this.commissionRATE = ''
            this.SeletedProductList()
            if (this.Text === 'เพิ่ม') {
              this.$swal.fire({ icon: 'success', text: 'ดำเนินการเสร็จสิ้น', showConfirmButton: false, timer: 2000 })
            } else {
              this.Text = 'เพิ่ม'
              this.$swal.fire({ icon: 'success', text: 'ดำเนินการเสร็จสิ้น', showConfirmButton: false, timer: 2000 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.DialogSetCom = false
            this.DialogSetComPro = false
            this.dialogAwaitConfirm = false
            this.disSave = true
            this.checkTrueFalse = true
            this.Category = []
            this.productSetCom = []
            this.deleteSetCom = []
            this.ProductList = []
            this.RawListData = []
            this.CategoryData = []
            this.ProductListData = []
            this.commissionRATE = ''
            this.SeletedProductList()
            this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาลองใหม่ภายหลัง', showConfirmButton: false, timer: 2000 })
          }
        }
      } else {
        // this.$swal.fire({ icon: 'warning', text: 'กรุณากรอกข้อมูล Affiliate ให้ครบถ้วน', showConfirmButton: false, timer: 2000 })
        // if (this.productList.length === 0) {
        //   this.theRedP = false
        // }
        // this.$store.commit('closeLoader')
      }
      // if (this.$refs.FormProductAffiliate.validate(true)) {
      // } else {
      //   this.$store.commit('closeLoader')
      //   if (this.productList.length !== 0) {
      //     this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
      //   }
      //   // this.theRedP = false
      // }
    },
    realPriceNoGoodCat (item, index) {
      this.disSave = false
      if (isNaN(item.commission) || item.commission === '') {
        this.Category[index].commission = ''
      }
      // else {
      //   this.Category[index].commission = this.Category[index].commission.replace(/,/g, '')
      //   this.Category[index].commission = Number(this.Category[index].commission).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      // }
    },
    realPriceNoGoodPro (item, index) {
      this.disSave = false
      if (isNaN(item.commission) || item.commission === '') {
        this.ProductList[index].commission = ''
      }
      // else {
      //   this.ProductList[index].commission = this.ProductList[index].commission.replace(/,/g, '')
      //   this.ProductList[index].commission = Number(this.ProductList[index].commission).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      // }
    },
    DeleteCategoryFromTable (item) {
      var indexX = item.hierachy
      this.productList = this.productList.filter(function (value) {
        return value !== indexX
      })
      this.Category = this.Category.filter(function (value) {
        return value.hierachy !== indexX
      })
      // this.updateFlash()
    },
    DeleteFromTable (item) {
      var indexX = item.id
      this.productList = this.productList.filter(function (value) {
        return value !== indexX
      })
      this.ProductList = this.ProductList.filter(function (value) {
        return value.id !== indexX
      })
      // this.AddProduct()
      // this.updateFlash()
    },
    async updateFlash () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopSellerID,
        raw_list: this.productList
      }
      await this.$store.dispatch('actionsSelectProductAffiliate', data)
      const res = await this.$store.state.ModuleAffiliate.stateSelectProductAffiliate
      if (res.success === true) {
        this.$store.commit('closeLoader')
        const filteredProductList = res.data.product_list.filter(item => item !== null)
        const filteredProductIDs = filteredProductList.map(item => item.id)
        this.productList = await this.productList.filter(id => filteredProductIDs.includes(id))
        this.Category = await res.data.category
        this.ProductList = await filteredProductList
        // console.log('object', this.ProductList)
        this.theRedP = true
        if (!Array.isArray(this.Category)) {
          this.Category = [{
            category_name: 'ทั้งหมด',
            category_id: '',
            hierachy: 'special_1'
          }]
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
      }
    },
    transformKeys (obj) {
      const transformed = {}
      for (const key in obj) {
        let newKey
        switch (key) {
          case 'hierachy':
            newKey = 'id'
            break
          case 'category_name':
            newKey = 'name'
            break
          case 'sub_category':
            newKey = 'children'
            break
          case 'product_id':
            newKey = 'id'
            break
          case 'product_name':
            newKey = 'name'
            break
          default:
            newKey = key
        }

        if (key === 'sub_category' && obj.sub_category === null && obj.product_list) {
          transformed.children = obj.product_list.map(item => this.transformKeys(item))
        } else if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
          transformed[newKey] = this.transformKeys(obj[key])
        } else if (Array.isArray(obj[key])) {
          transformed[newKey] = obj[key].map(item => this.transformKeys(item))
        } else {
          transformed[newKey] = obj[key]
        }
      }
      return transformed
    },
    async getdata () {
      var sittingCategory = {}
      var data = {
        seller_shop_id: this.shopSellerID
      }
      await this.$store.dispatch('actionsListProductInShop', data)
      const response = await this.$store.state.ModuleManageCoupon.stateListProductInShop
      // var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      var filteredData = []
      // กรณีเก่งตัดหมวดหมู่ service และบริการ ให้แล้ว
      // filteredData = response.data[0]
      // filteredData = response.data[0].sub_category.filter(item => item.category_name !== 'Service และบริการ')
      filteredData = {
        ...response.data[0], // คงข้อมูลเดิมของ response.data[0]
        sub_category: response.data[0].sub_category.filter(sub => sub.category_name !== 'Service และบริการ') // กรอง sub_category
      }
      // กรณีเก่งไม่ตัดหมวดหมู่ service และบริการ ให้แล้ว
      // if (dataDetail.is_JV === 'yes') {
      //   filteredData = response.data[0]
      // } else {
      //   filteredData = response.data[0]
      // }
      if (filteredData.product_list.length !== 0) {
        sittingCategory = [{
          id: 'special_1',
          name: 'หมวดหมู่ทั้งหมด',
          children: [filteredData]
        }]
      } else {
        sittingCategory = [{
          id: 'special_1',
          name: 'ทั้งหมด',
          children: filteredData.sub_category
        }]
      }
      sittingCategory[0].children = await this.transformDataToTree(sittingCategory[0].children)
      this.dataListPrime = await sittingCategory
      this.setOpenNodes(this.dataListPrime)
      // await this.$store.dispatch('actionsAffiListProductByCategory', data)
      // const res = await this.$store.state.ModuleAffiliate.stateAffiListProductByCategory
      // const filteredData = res.data.filter(item => item.hierachy !== '1_259')
      // const cleanedData = filteredData.map(category => {
      //   if (Array.isArray(category.sub_category)) {
      //     category.sub_category = category.sub_category.filter(sub => {
      //       return !(
      //         (Array.isArray(sub.product_list) && sub.product_list.length === 0) ||
      //         ((Array.isArray(sub.sub_category) && sub.sub_category.length === 0))
      //       )
      //     })
      //   }
      //   return category
      // }).filter(category => {
      //   return !(
      //     (Array.isArray(category.product_list) && category.product_list.length === 0) ||
      //     ((Array.isArray(category.sub_category) && category.sub_category.length === 0))
      //   )
      // })
      // const transformedData = cleanedData.map(item => this.transformKeys(item))
      // this.dataListPrime.push({
      //   id: 'special_1',
      //   name: 'ทั้งหมด',
      //   children: transformedData
      //   // type: 'special_1_category'
      // })
      // // this.dataListPrime = filteredData
      // this.setOpenNodes(this.dataListPrime)
      // console.log('this.dataListPrime', this.dataListPrime)
    },
    setOpenNodes (data) {
      data.forEach(node => {
        if (node.name === 'ทั้งหมด' || node.name === 'หมวดหมู่ทั้งหมด') {
          this.open.push(node.id)
        }
        if (node.children && node.children.length) {
          this.setOpenNodes(node.children)
        }
      })
    },
    transformDataToTree (data) {
      return data.map((item) => {
        const transformed = {
          id: item.hierachy,
          name: item.category_name,
          children: []
        }

        // เพิ่ม sub_category
        if (item.sub_category && item.sub_category.length > 0) {
          transformed.children = this.transformDataToTree(item.sub_category)
        }

        // เพิ่ม product_list
        if (item.product_list && item.product_list.length > 0) {
          const products = item.product_list

          const productItems = products.map((product) => {
            const productNode = {
              id: product.product_id,
              name: product.product_name
              // children: []
            }

            // เพิ่ม attribute ถ้ามี
            // if (product.attributes && product.attributes.length > 0) {
            //   productNode.children = product.attributes.map((attr) => ({
            //     id: attr.product_attribute_id,
            //     name: attr.product_attribute_name
            //   }))
            // }

            return productNode
          })

          if (productItems.length > 0) {
            transformed.children.push(...productItems)
          }
        }

        return transformed
      })
    },
    filterDataListPrime () {
      this.DialogSelect = true
      const productList = this.productList
      // console.log(productList)
      const recursiveFilter = (node) => {
        // console.log(node.children)
        // if (node.type === 'product') {
        //   return productList.includes(node.id) ? node : null
        // }
        if (node.children) {
          // return productList.includes(node.id) ? node : null
          if (node.children && node.children.length > 0) {
            const filteredChildren = node.children.map(recursiveFilter).filter(child => child !== null)
            return filteredChildren.length > 0 ? { ...node, children: filteredChildren } : null
          }
        } else {
          return productList.includes(node.id) ? node : null
        }
        return null
      }
      this.dataListCat = this.dataListPrime.map(recursiveFilter).filter(node => node !== null)
      // console.log('this.dataListCat', this.dataListCat)
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-input--selection-controls {
  margin-top: 0px;
  padding-top: 10px;
}
.cardProductDesk{
  width: 20%;
  max-width: 20%;
  flex-basis: 20%;
}
.classTextCountProduct {
  padding: 4px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 35px;
  background: #DAF1E9;
  width: 100%;
}
.addUnderline {
  text-decoration-line: none;
  color: #333333;
  font-weight: 400;
}
.addUnderline:hover {
  text-decoration-line: underline;
  color: #1B5DD6;
  font-weight: 600;
}
</style>
