<template>
  <div>
    <v-row dense>
      <v-col cols="6" align="start" class="mt-4 pt-2 pl-1">
        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">ร้านค้าขายดี Top 10</span>
      </v-col>
      <v-col cols="6" align="end" class="mt-4" v-if="dataShop.length !== 0">
        <v-btn @click="AllPageTopTenShop(dataShop)" text color="#27AB9C" plain style="font-weight: 600;"><span style="text-decoration-line: underline;">ดูทั้งหมด</span> <v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
      </v-col>
    </v-row>
    <v-row dense v-if="dataShop.length !== 0">
      <v-col cols="12" md="6" sm="6" v-for="(item, index) in dataShop" :key="index">
        <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" v-if="index < 6">
          <v-card-text :class="IpadSize ? 'px-2 py-2' : ''">
            <v-row dense>
              <v-col cols="2" md="1" sm="1" v-if="index < 5 && (!MobileSize && !IpadSize)" :class="MobileSize ? '' : IpadSize ? '' :'mr-3'">
                <v-avatar size="45">
                  <v-img contain v-if="item.ranking === 1" src="@/assets/icons/level_1.png"></v-img>
                  <v-img contain v-else-if="item.ranking === 2" src="@/assets/icons/level_2.png"></v-img>
                  <v-img contain v-else-if="item.ranking === 3" src="@/assets/icons/level_3.png"></v-img>
                  <v-img contain v-else-if="item.ranking === 4" src="@/assets/icons/level_4.png"></v-img>
                  <v-img contain v-else-if="item.ranking === 5" src="@/assets/icons/level_5.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="3" md="2" sm="4">
                <v-avatar tile :size="IpadSize ? '50' : '63'">
                  <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" :max-height="IpadSize ? '80px' : '120px'" :max-width="IpadSize ? '60px' : '120px'"></v-img>
                  <!-- <v-img :src="item.image_shop" max-height="120px" max-width="120px" style="border-radius: 8px;"></v-img> -->
                </v-avatar>
              </v-col>
              <v-col cols="7" :md="index < 5 ? 6 : 7" sm="8" class="pt-2">
                <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">{{ item.shop_name }}</span><br/>
                <!-- <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;">{{ item.shop_name }}</span> -->
              </v-col>
              <!-- <v-col cols="12" md="1" :class="index < 5 ? 'mt-2 mr-2' : 'mt-2 mr-4'">
                <div :style="{'width': index < 5 ? '105px' : '115px'}" style="height: 40px; background: #F8FAFC; border-radius: 8px;">
                  <v-row justify="center" class="pt-2">
                    <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #27AB9C;">Partner <v-icon color="#27AB9C">mdi-bookmark</v-icon></span>
                  </v-row>
                </div>
              </v-col> -->
            </v-row>
            <v-row dense class="mt-6">
              <v-col cols="12">
                <v-row dense>
                  <v-icon :class="IpadSize ? 'pr-1 ml-0' : 'ml-1 pr-2'" color="#A1A1A1">mdi-file-document-outline</v-icon>
                  <span style="font-weight: 500; line-height: 24px; color: #333333;" :style="IpadSize ? 'font-size: 12px;' : 'font-size: 16px;'">รายการชำระเงินสำเร็จ : <b :style="IpadSize ? 'font-size: 14px;' : 'font-size: 14px;'">{{ item.transaction }}</b> รายการ</span>
                </v-row>
              </v-col>
            </v-row>
            <v-row dense class="mt-6">
              <v-col cols="12">
                <v-row dense>
                  <v-icon :class="IpadSize ? 'pr-1 ml-0' : 'ml-1 pr-2'" color="#A1A1A1">mdi-currency-usd</v-icon>
                  <span style="font-weight: 500; line-height: 24px; color: #333333;" :style="IpadSize ? 'font-size: 12px;' : 'font-size: 16px;'">จำนวนเงิน : <b style="line-height: 32px; color: #52C41A;" :style="IpadSize ? 'font-size: 18px;' : 'font-size: 24px;'">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}</b> บาท</span>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-else>
      <v-card width="100%" height="100" elevation="0">
        <v-card-text>
          <v-row justify="center" align="center">
            <p style="font-size: 18px;" class="pt-5">ไม่มีข้อมูลร้านค้าขายดี Top 10</p>
          </v-row>
        </v-card-text>
      </v-card>
    </v-row>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      dateStart: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateEnd: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dataShop: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  created () {
    this.$EventBus.$on('getDataShopTopTen', this.getDataShopTopTen)
    this.getDataShopTopTen(this.dateStart, this.dateEnd, '', '')
  },
  methods: {
    AllPageTopTenShop (val) {
      localStorage.setItem('AllShopTopTen', Encode.encode(val))
      if (this.MobileSize) {
        this.$router.push({ path: '/AllShopTopTenMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/AllShopTopTen' }).catch(() => {})
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    async getDataShopTopTen (startDate, endDate, type, shopID) {
      this.dataShop = []
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      // console.log(data)
      await this.$store.dispatch('actionsTop10Sellers', data)
      var response = await this.$store.state.ModuleAdminPanit.stateTop10Sellers
      // console.log('getDataShopTopTen', response)
      if (response.result === 'OK') {
        this.dataShop = response.data
        // console.log(this.dataShop)
      }
    }
  }
}
</script>
