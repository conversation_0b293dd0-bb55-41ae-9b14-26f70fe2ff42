<template>
  <v-container>
    <v-row>
      <h2 class="pt-1 ml-3 mt-2 mb-4 diaplayWeb" style="font-weight: bold; font-size: 24px; line-height: 48px; color: #333333;">{{ header }}</h2>
      <h2 class="pt-1 ml-4 mt-2 mb-4 displayIPAD" style="font-weight: bold; font-size: 18px; line-height: 48px; color: #333333;">{{ header }}</h2>
      <h2 class="pt-1 ml-3 mt-2 mb-4 displayMobile" style="font-weight: bold; font-size: 18px; line-height: 48px; color: #333333;">{{ header }}</h2>
      <v-spacer></v-spacer>
      <v-btn text @mousedown.left="GetAllProduct(cleanData)" @mousedown.right="GetAllProductRightClick()"  color="#27AB9C " class="mt-4 diaplayWeb" plain style="font-weight: 600;">ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
      <v-btn text @click="GetAllProduct(cleanData)" color="#27AB9C" class="mt-4 displayIPAD" plain style="font-weight: 600;">ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
      <v-btn text @click="GetAllProduct(cleanData)" color="#27AB9C" class="mt-4 displayMobile" plain style="font-weight: 600;">ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
    </v-row>
      <!-- <pre>{{propsData}}</pre> -->
      <!-- <vue-horizontal-list :items='propsData' :options='options' v-if="!MobileSize">
        <template v-slot:nav-prev>
          <div><v-icon color="#27AB9C" size="32">mdi-chevron-left</v-icon></div>
        </template>

        <template v-slot:nav-next>
          <div><v-icon color="#27AB9C" size="32">mdi-chevron-right</v-icon></div>
        </template>
        <template v-slot:default='{ item }'>
          <a-skeleton :loading="check === true ? !loading : loading">
            <CardProducts :itemProduct='item' />
          </a-skeleton>
        </template>
      </vue-horizontal-list> -->
      <v-row dense v-if="!MobileSize && !IpadSize">
        <v-col cols="3" sm="3" xs="3" v-for="(item, index) in cleanData" :key="index">
          <CardProducts v-if="index < 4" :itemProduct='item' :gracz=gracz />
        </v-col>
      </v-row>
      <v-row dense v-else class="mb-0">
        <v-col cols="6" sm="6" xs="6" v-for="(item, index) in cleanData" :key="index">
          <CardProductsResponsive :itemProduct='item' />
        </v-col>
      </v-row>
  </v-container>
</template>

<script>
import 'vue-slick-carousel/dist/vue-slick-carousel.css'
import 'vue-slick-carousel/dist/vue-slick-carousel-theme.css'
import { Encode } from '@/services'
export default {
  props: ['propsData', 'header', 'check', 'gracz'],
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      options: {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 5 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 14
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      loading: true,
      cleandata: [],
      page: 1
    }
  },
  created () {
    // this.cleanData()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    cleanData () {
      // console.log(this.propsData)
      var array1 = this.propsData
      var i
      var cleandata = []
      for (i = 0; i < 6; i++) {
        cleandata.push(array1[i])
      }
      return cleandata
    }
  },
  methods: {
    GetAllProduct (allItem) {
      localStorage.setItem('itemShop', Encode.encode(allItem))
      console.log('this.gracz', this.gracz)
      if (this.gracz === true) {
        this.$router.push(`/Order/ListProductShop/${this.header}`).catch(() => {})
      } else {
        this.$router.push(`/ListProductShop/${this.header}`).catch(() => {})
      }
    }
  }
}
</script>
<style lang="scss"  scoped>
.slick-slider {
  width: 100%;
  height: 100%;
  padding: 1%;
  ::v-deep .slick-arrow:before {
    color: #008E00;
    font-size: 30px;
  }
}
</style>

<style scoped>
.container {
  max-width: 1250px;
}
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>

<style scoped>
.vhl-btn-left {
  width: 32px !important;
  height: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  background: #DCFFDC !important;
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  z-index: 2;
}
.vhl-btn-right {
  width: 32px !important;
  height: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  background: #DCFFDC !important;
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  z-index: 2;
}
.vhl-btn-right {
  margin-left: auto;
  margin-right: -16px !important;
}
.vhl-btn-left[data-v-8b923bbc] {
  margin-left: -16px !important;
  margin-right: auto;
}
</style>
