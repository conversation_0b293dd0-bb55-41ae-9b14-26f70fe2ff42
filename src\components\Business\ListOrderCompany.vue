<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row>
        <!-- หัวข้อเรื่อง -->
        <v-col cols="12">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการสั่งซื้อบริษัท</v-card-title>
          <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon>รายการสั่งซื้อบริษัท</v-card-title>
        </v-col>
        <v-col cols="12" :style="MobileSize ? 'margin-top: -10vw;' : 'margin-top: -2vw;'">
          <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
            <v-card-text>
              <!-- ค้นหาจากหมายเลขคำสั่งซื้อ -->
              <v-row>
                <v-col :cols="MobileSize ? 8 : 6" :style="MobileSize ? 'display: flex; flex-direction: column; padding: 0; margin-right: 2vw;' : ''">
                  <span v-if="MobileSize === false" class="pr-2">เลือกรหัสการสั่งซื้อ</span>
                  <v-text-field
                    placeholder="ค้นหาจากหมายเลขคำสั่งซื้อ"
                    outlined
                    dense
                    hide-details
                    v-model="search"
                  >
                    <v-icon slot="append" color="#757575">mdi-magnify</v-icon>
                  </v-text-field>
                </v-col>
                <v-col v-if="MobileSize === false" :col="MobileSize ? 8 : 6" :style="MobileSize ? 'display: flex; flex-direction: column; padding: 0; margin-right: 2vw;' : ''">
                  <span class="pr-2">เลือกบริษัท</span>
                  <v-select
                    :items="companiesList"
                    v-model="selectedCompany"
                    item-text="name_th"
                    item-value="id"
                    outlined
                    multiple
                    dense
                    hide-details
                    no-data-text="ไม่พบบริษัท"
                    @change="filterShop"
                  >
                    <template v-slot:selection="{ item, index }">
                      <v-chip v-if="index === 0">
                        <span>{{ item.name_th }}</span>
                      </v-chip>
                      <span
                        v-if="index === 1"
                        class="grey--text text-caption"
                      >
                        (+{{ selectedCompany.length - 1 }} อื่นๆ)
                      </span>
                    </template>
                  </v-select>
                </v-col>
                <v-col cols="3" v-if="MobileSize" style="padding: 0;">
                  <v-btn @click="OpenModalFilter()" outlined rounded color="#27AB9C" height="36"><v-icon size="24" left dark>mdi-filter-outline</v-icon>ตัวกรอง</v-btn>
                </v-col>
              </v-row>
              <!-- รายการสั่งซื้อบริษัท -->
              <v-row v-if="!MobileSize">
                <!-- สถานะรายการ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">สถานะรายการ :</span>
                  <v-select
                    outlined
                    dense
                    v-model="selectedOrderType"
                    :items="statusList"
                    hide-details
                    @change="filterOrdertype"
                  ></v-select>
                </v-col>
                <!-- Pay type -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">Pay type :</span>
                  <v-select
                    outlined
                    dense
                    :items="payTypeList"
                    v-model="selectedPaytype"
                    hide-details
                    @change="filterPayType"
                  ></v-select>
                </v-col>
                <!-- วันที่สั่งซื้อ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">วันที่สั่งซื้อ :</span>
                  <v-dialog
                    ref="dialogBuyDate"
                    v-model="modalBuyDate"
                    :return-value.sync="date"
                    persistent
                    width="480px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="buyDate"
                        v-bind="attrs"
                        v-on="on"
                        hide-details
                        outlined
                        style="border-radius: 8px;"
                        dense
                        :class="MobileSize ? '' : ''"
                        placeholder="วว/ดด/ปปปป"
                        ><v-icon slot="append" color="#CCCCCC"
                          >mdi-calendar-multiselect</v-icon
                        ></v-text-field
                      >
                    </template>
                    <v-date-picker
                      v-model="date"
                      color = "#27AB9C"
                      scrollable
                      reactive
                      full-width
                      locale="Th-th"
                      :max="
                        new Date(
                          Date.now() - new Date().getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .substr(0, 10)
                      "
                    >
                      <v-spacer></v-spacer>
                      <v-btn text color="primary" @click="closeModalBuyDate($refs)">
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="
                          saveBuyDate
                        "
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <!-- วันที่อนุมัติ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">วันที่อนุมัติ :</span>
                  <v-dialog
                  ref="dialogAcceptDate"
                  v-model="modalAcceptDate"
                  :return-value.sync="dateApprove"
                  persistent
                  width="480px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="acceptDate"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      hide-details
                      style="border-radius: 8px;"
                      dense
                      placeholder="วว/ดด/ปปปป"
                      ><v-icon slot="append" color="#CCCCCC"
                        >mdi-calendar-multiselect</v-icon
                      ></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="dateApprove"
                    scrollable
                    reactive
                    locale="Th-th"
                    full-width
                    @change="setValueAcceptDate(dateApprove)"
                    color = "#27AB9C"
                    :max="
                      new Date(
                        Date.now() - new Date().getTimezoneOffset() * 60000
                      )
                        .toISOString()
                        .substr(0, 10)
                    "
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="primary"
                    @click="
                      closeModalAcceptDate($refs)">
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="
                        saveAcceptDate
                      "
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                  </v-dialog>
                </v-col>
                <!-- วันที่รอบบริการ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">วันที่รอบบริการ :</span>
                  <v-dialog
                    ref="modalRangeDate"
                    v-model="modalRangeDate"
                    :return-value.sync="dateRange"
                    persistent
                    width="480px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="RangeDate1"
                        v-bind="attrs"
                        v-on="on"
                        style="border-radius: 8px;"
                        outlined
                        dense
                        hide-details
                        :class="MobileSize ? '' : ''"
                        placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                        ><v-icon slot="append" color="#CCCCCC"
                          >mdi-calendar-multiselect</v-icon
                        ></v-text-field>
                    </template>
                    <v-date-picker
                      color="#27AB9C"
                      v-model="dateRange"
                      scrollable
                      range
                      reactive
                      full-width
                      locale="Th-th"
                    >
                      <v-spacer></v-spacer>
                      <v-btn text color="primary" @click="CloseModalRangeDate()">
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="setValueRangeDate(dateRange)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <!-- สถานะการชำระเงิน -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">สถานะการชำระเงิน :</span>
                  <v-select
                    :items="paidStatusList"
                    v-model="selectedPaidStatus"
                    dense
                    outlined
                    hide-details
                    @change="filterTransactionStatus"
                  ></v-select>
                </v-col>
              </v-row>
            </v-card-text>
            <v-row :style="MobileSize ? 'display: flex; flex-direction: column;' : ''">
              <v-col v-if="!MobileSize" :style="!MobileSize ? 'padding: 2.5vw 2.5vw 0 2.5vw; display: flex; justify-content: space-between; align-items: center;' : 'margin-left: 2vw; padding: 5vw 4vw;'">
                <span :style="!MobileSize ? 'font-size: 18px; font-weight: bold;' : ''">รายการสั่งซื้อสินค้าทั้งหมด</span>
                <div style="display: flex; gap: .5vw">
                  <v-btn color="#38b2a4" rounded class="white--text" @click="clearFilter">ล้างค่า</v-btn>
                  <v-btn color="#38b2a4" rounded class="white--text" @click="exportExcel">EXPORT FILE</v-btn>
                </div>
              </v-col>
              <v-row v-else dense>
                <v-col style="padding: 5vw 0 0 6vw;">
                  <span
                    :class="MobileSize ? '' : ''"
                    style="line-height: 24px; align-items: center; color: #333333; font-weight: 400;"
                    :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'"
                    >รายการสั่งซื้อสินค้า {{dataTable.length}} รายการ</span
                  >
                </v-col>
                <v-col style="padding: 0vw 6vw 3vw 6vw;" cols="12" class="mt-2" v-if="MobileSize">
                  <v-btn block rounded color="#27AB9C" height="40" class="white--text mr-2" @click="exportExcel">
                    <v-icon>mdi-file-export</v-icon> Export File
                  </v-btn>
                </v-col>
              </v-row>
              <!-- ตารางรายการสั่งซื้อ -->
              <v-col style="padding: 2vw;">
                <v-data-table
                  :headers="headersOrdersTable"
                  :items="dataTable"
                  :search="search"
                  style="width: 100%; white-space: nowrap; border: 1px solid #d8d8d8; border-radius: 1vw"
                  height="100%"
                  @pagination="countOrdar"
                  :items-per-page="10"
                  class=""
                  no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
                  no-data-text="ไม่มีข้อมูลรายการสั่งซื้อในตาราง"
                  :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
                >
                  <template v-slot:[`item.approved_at`]="{ item }">
                    <span v-if="item.approved_at !== '-'">
                      {{
                      new Date(item.approved_at).toLocaleDateString('th-TH', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    }}
                    </span>
                    <span v-else>
                      -
                    </span>
                  </template>
                  <template v-slot:[`item.order_number`]="{ item }">
                    <a v-if="item.QT_order !== '-' && item.transaction_status === 'Success'" :href="item.QT_order" target="_blank" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_number }}</a>
                    <span v-else>{{ item.order_number }}</span>
                  </template>
                  <template v-slot:[`item.created_at`]="{ item }">
                    <v-row class="pl-0" style="height: 40px;">
                      <div v-if="item.condition_send_cs !== '-'" class="mr-2" :style="item.condition_send_cs === 'green' ? 'border: 2px solid green' : item.condition_send_cs === 'yellow' ? 'border: 2px solid yellow' : 'border: 2px solid red'"></div>
                      <div style="display: flex; justify-content: center; align-items: center;">
                        {{new Date(item.created_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
                      </div>
                    </v-row>
                  </template>
                  <template v-slot:[`item.pay_type`]="{ item }">
                    <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                    <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                    <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.order_type`]="{ item }">
                    <div v-if="item.shop_approve !== 'waiting_approve' && item.shop_approve !== 'reject'">
                      <span style="color: #636363;" v-if="item.detail_status !== 'ยกเลิกคำสั่งซื้อ' && (item.transaction_status === '-' || item.transaction_status === null)"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ item.order_type }}</span>
                      <span v-else-if="item.detail_status !== 'ยกเลิกคำสั่งซื้อ' && item.transaction_status !== '-'" :style="{ 'color' : getTextColorStatus(item.transaction_status)}"><v-icon :color="getTextColorStatus(item.transaction_status)">mdi-circle-medium</v-icon>{{ getTextStatus(item.transaction_status) }}</span>
                      <span style="color: #D1392B;" v-else><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ item.detail_status }}</span>
                    </div>
                    <div>
                      <span v-if="item.shop_approve === 'waiting_approve'" style="color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รออนุมัติคำสั่งซื้อ</span>
                      <span v-if="item.shop_approve === 'reject'" style="color: #D1392B;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>ปฏิเสธคำสั่งซื้อ</span>
                    </div>
                  </template>
                  <template v-slot:[`item.paid_datetime`]="{ item }">
                    {{
                      item.paid_datetime === '-'
                        ? item.paid_datetime
                        : new Date(item.paid_datetime).toLocaleDateString('th-TH', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric'
                          })
                    }}
                  </template>
                  <template v-slot:[`item.date_contract`]="{ item }">
                    <span>{{formatDate(item.date_contract)}}</span>
                  </template>
                  <template v-slot:[`item.po_document_id`]="{ item }">
                    <a v-if="item.po_document_id !== '-'&& item.PO_External !== '-'" :href="item.PO_External" target="_blank">{{item.po_document_id}}</a>
                    <span v-else-if="item.po_document_id !== '-' && item.PO_External === '-'">{{item.po_document_id}}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.pr_document_id`]="{ item }">
                    <a v-if="item.pr_document_id !== '-' && item.PR_External !== '-'" :href="item.PR_External" target="_blank">{{item.pr_document_id}}</a>
                    <span v-else-if="item.pr_document_id !== '-' && item.PR_External === '-'">{{item.pr_document_id}}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.so_document_id`]="{ item }">
                    <a v-if="(item.so_document_id !== '-' || item.ref_callback_so_id !== '-') && item.SO_External !== '-'" :href="item.SO_External" target="_blank">{{ item.so_document_id !== '-' ? item.so_document_id : item.ref_callback_so_id }}</a>
                    <span v-else-if="(item.so_document_id !== '-' || item.ref_callback_so_id !== '-') && item.SO_External === '-'">{{ item.so_document_id !== '-' ? item.so_document_id : item.ref_callback_so_id }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.approve_at`]="{ item }">
                    <span>{{new Date(item.approve_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                  </template>
                  <template v-slot:[`item.payment_transaction_status`]="{ item }">
                    <!-- <v-chip v-if="item.payment_transaction_status !== 'ยังไม่ชำระเงิน'" color="#F0F9EE" text-color="#1AB759">{{ item.payment_transaction_status }}</v-chip>
                    <v-chip v-else color="#FCF0DA" text-color="#E9A016">{{ item.payment_transaction_status }}</v-chip> -->
                    <v-chip v-if="item.transaction_status !== 'Success' && item.payment_transaction_status !== 'เกินกำหนดชำระเงิน'" color="#FCF0DA" text-color="#E9A016">ยังไม่ชำระเงิน</v-chip>
                    <v-chip v-else-if="item.payment_transaction_status === 'เกินกำหนดชำระเงิน'" color="#F7D9D9" text-color="#D1392B">ยกเลิกคำสั่งซื้อ</v-chip>
                    <v-chip v-else color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                  </template>
                  <template v-slot:[`item.transportation_status`]="{ item }">
                    <v-chip v-if="item.transportation_status !== '-'" :color="getColor(item.transportation_status)" :text-color="getTextColor(item.transportation_status)">{{ item.transportation_status }}</v-chip>
                    <span v-else>{{ item.transportation_status }}</span>
                  </template>
                  <template v-slot:[`item.order_mobilyst_no`]="{ item }">
                    <a v-if="item.order_mobilyst_no !== '-' && (item.url_tracking !== '-' && item.url_tracking !== '')" target="_blank" :href="item.url_tracking" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</a>
                    <div @click="copyClipboard()" v-else-if="(item.order_mobilyst_no !== '-' && item.url_tracking === '')" style="cursor: pointer;" class="pl-2">
                      <input type="text" :value="item.order_mobilyst_no" id="trackingNumberList" style="display: none;">
                      <span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</span>
                    </div>
                    <span v-else>{{ item.order_mobilyst_no }}</span>
                  </template>
                  <template v-slot:[`item.payment_transaction_number`]="{ item }">
                    <div v-if="item.QT_order !== '-' && item.transaction_status === 'Success'">
                      <a @click="orderDetail(item)">{{
                        item.payment_transaction_number
                      }}</a>
                    </div>
                    <div v-else>
                      {{ item.payment_transaction_number }}
                    </div>
                  </template>
                  <template v-slot:[`item.buyer_name`]="{ item }">
                    <span v-if="item.buyer_name === ''">-</span>
                    <span v-else>{{item.buyer_name}}</span>
                  </template>
                  <template v-slot:[`item.quatation_sheet`] = "{ item }">
                    <v-btn x-small class="elevation-0 py-5 px-0" @click="getQuatationSheet(item.quatation_sheet)">
                      <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                    </v-btn>
                  </template>
                  <template v-slot:[`item.tax_receipt`] = "{ item }">
                    <v-btn v-if="item.tax_receipt !== '-'" x-small class="elevation-0 py-5 px-0" @click="getQuatationSheet(item.tax_receipt)">
                      <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                    </v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.qt_order_invoice`] = "{ item }">
                    <v-btn v-if="item.qt_order_invoice !== '-'" x-small class="elevation-0 py-5 px-0" @click="getQuatationSheet(item.qt_order_invoice)">
                      <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                    </v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.cost_sheet`] = "{ item }">
                    <v-btn v-if="item.cost_sheet !== '-'" x-small class="elevation-0 py-5 px-0" @click="getQuatationSheet(item.cost_sheet)">
                      <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                    </v-btn>
                    <span v-else>-</span>
                  </template>
                  <template
                    v-slot:[`item.payment_transaction_number_icon`]="{ item }"
                  >
                    <div v-if="item.qt_number !== '-'">
                      <a @click="QuotationShow(item)">
                        <v-btn
                          x-small
                          style="
                            border: 1px solid #f2f2f2;
                            box-sizing: border-box;
                            box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                            border-radius: 4px;
                          "
                          :style="
                            IpadProSize
                              ? 'max-width: 24px; max-height: 24px;'
                              : IpadSize
                              ? 'max-width: 16px; max-height: 16px;'
                              : 'max-width: 32px; max-height: 32px;'
                          "
                          class="pt-4 pb-4"
                        >
                          <v-icon color="#27AB9C">mdi-file-document</v-icon>
                        </v-btn>
                      </a>
                    </div>
                    <div v-else-if="item.QT_order !== '-' && item.transaction_status === 'Success'">
                      <a @click="QuotationShow(item)">
                        <v-btn
                          x-small
                          style="
                            border: 1px solid #f2f2f2;
                            box-sizing: border-box;
                            box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                            border-radius: 4px;
                          "
                          :style="
                            IpadProSize
                              ? 'max-width: 24px; max-height: 24px;'
                              : IpadSize
                              ? 'max-width: 16px; max-height: 16px;'
                              : 'max-width: 32px; max-height: 32px;'
                          "
                          class="pt-4 pb-4"
                        >
                          <v-icon color="#27AB9C">mdi-file-document</v-icon>
                        </v-btn>
                      </a>
                    </div>
                    <div v-else>
                      <span>{{ '-' }}</span>
                    </div>
                  </template>
                  <template v-slot:[`item.pdf_cs_path`]="{ item }">
                    <div v-if="item.pdf_cs_path !== '-' && item.cs_number !== '-'" >
                      <a @click="costSheetShow(item)">
                        <v-btn
                          x-small
                          style="
                            border: 1px solid #f2f2f2;
                            box-sizing: border-box;
                            box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                            border-radius: 4px;
                          "
                          :style="
                            IpadProSize
                              ? 'max-width: 24px; max-height: 24px;'
                              : IpadSize
                              ? 'max-width: 16px; max-height: 16px;'
                              : 'max-width: 32px; max-height: 32px;'
                          "
                          class="pt-4 pb-4"
                        >
                          <v-icon color="#27AB9C">mdi-file-document</v-icon>
                        </v-btn>
                      </a>
                    </div>
                    <div v-else>
                      <span>{{ '-' }}</span>
                    </div>
                  </template>
                  <template v-slot:[`item.transaction_code_icon`]="{ item }">
                    <div
                      v-if="
                        item.transaction_code !== '-' &&
                        item.required_invoice !== '-'
                      "
                    >
                      <!-- <a @click="GetETaxPDF(item)"> -->
                      <v-btn
                        x-small
                        @click="GetETaxPDF(item)"
                        style="
                          border: 1px solid #f2f2f2;
                          box-sizing: border-box;
                          box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                          border-radius: 4px;
                        "
                        :style="
                          IpadProSize
                            ? 'max-width: 24px; max-height: 24px;'
                            : IpadSize
                            ? 'max-width: 16px; max-height: 16px;'
                            : 'max-width: 32px; max-height: 32px;'
                        "
                        class="pt-4 pb-4"
                      >
                        <v-icon color="#27AB9C">mdi-file-document</v-icon>
                      </v-btn>
                      <!-- </a> -->
                    </div>
                    <div
                      v-else-if="
                        item.transaction_code === '-' &&
                        item.required_invoice !== '-'
                      "
                    >
                      <span>{{ item.required_invoice }}</span>
                    </div>
                    <div v-else>
                      <span>{{ '-' }}</span>
                    </div>
                  </template>
                  <template v-slot:[`item.QT_order_invoice`]="{ item }">
                    <div v-if="item.QT_order_invoice !== '-' && item.QT_order_invoice !== ''">
                      <v-btn
                        x-small
                        @click="GetInvoice(item)"
                        style="
                          border: 1px solid #f2f2f2;
                          box-sizing: border-box;
                          box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                          border-radius: 4px;
                        "
                        :style="
                          IpadProSize
                            ? 'max-width: 24px; max-height: 24px;'
                            : IpadSize
                            ? 'max-width: 16px; max-height: 16px;'
                            : 'max-width: 32px; max-height: 32px;'
                        "
                        class="pt-4 pb-4"
                      >
                        <v-icon color="#27AB9C">mdi-file-document</v-icon>
                      </v-btn>
                    </div>
                    <div v-else>
                      <span>{{ '-' }}</span>
                    </div>
                  </template>
                  <template v-slot:[`item.total_amount`]="{ item }">
                    {{
                      Number(item.total_amount).toLocaleString(undefined, {
                        minimumFractionDigits: 2
                      })
                    }}
                  </template>
                  <template v-slot:[`item.payment`]="{ item }">
                    <v-row justify="center">
                      <v-btn
                        v-if="item.seller_sent_status === 'Success'"
                        text
                        disabled
                        rounded
                        color="#1AB759"
                        small
                        @click="GoToPayment(item)"
                      >
                        <b>จ่ายเงิน</b>
                        <v-icon small>mdi-chevron-right</v-icon>
                      </v-btn>
                      <v-btn
                        v-else-if="item.seller_sent_status === 'cancel'"
                        text
                        disabled
                        rounded
                        color="#1AB759"
                        small
                        @click="GoToPayment(item)"
                      >
                        <b>จ่ายเงิน</b>
                        <v-icon small>mdi-chevron-right</v-icon>
                      </v-btn>
                      <v-btn
                        v-else
                        text
                        rounded
                        color="#1AB759"
                        small
                        @click="GoToPayment(item)"
                      >
                        <b>จ่ายเงิน</b>
                        <v-icon small>mdi-chevron-right</v-icon>
                      </v-btn>
                    </v-row>
                  </template>
                  <template v-slot:[`item.transaction_status`]="{ item }">
                    <span
                      v-if="
                        item.transaction_status === 'Success' &&
                        item.seller_sent_status !== 'cancel'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >ชำระเงินสำเร็จ</v-chip
                      >
                    </span>
                    <span
                      v-else-if="
                        item.seller_sent_status === 'cancel' ||
                        item.transaction_status === 'Cancel'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#f7c5ad"
                        text-color="#f50"
                        >ยกเลิกสินค้า</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Pending'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >รออนุมัติ</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Approve'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >อนุมัติ</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Credit'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#E5EFFF"
                        text-color="#1B5DD6"
                        >ชำระเงินแบบเครดิตเทอม</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Fail'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F7D9D9"
                        text-color="#D1392B"
                        >ชำระเงินไม่สำเร็จ</v-chip
                      >
                    </span>
                    <span v-else>
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >ยังไม่ชำระเงิน</v-chip
                      >
                    </span>
                  </template>
                  <template v-slot:[`item.buyer_received_status`]="{ item }">
                    <v-row class="pt-5">
                      <v-select
                        v-model="item.buyer_received_status"
                        :items="receive_items"
                        item-text="text"
                        item-value="value"
                        @change="UpdateStatusBuyer(item)"
                        outlined
                        dense
                      ></v-select>
                    </v-row>
                  </template>
                  <template v-slot:[`item.detail`]="{ item }">
                    <v-menu offset-y>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-bind="attrs"
                          v-on="on"
                          class="pt-4 pb-4"
                          x-small
                          outlined
                          style="
                            max-width: 32px;
                            max-height: 32px;
                            border-radius: 4px;
                            border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                            background: var(--neutral-ffffff, #fff);
                            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                          "
                        >
                          <!-- <b>รายละเอียด</b> -->
                          <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-item
                          v-for="(items, index) in actionsItem"
                          :key="index"
                          link
                        >
                          <v-list-item-content
                            @click="gotoActions(item, items.value)"
                          >
                            <v-list-item-title>{{ items.text }}</v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="ModalFilter" width="100%" persistent>
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 20px;">
        <v-card-text>
          <v-toolbar flat color="rgba(0, 0, 0, 0)">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title><span style="color: #333333; font-size: 16px;"><b>ตัวกรอง</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="ModalFilter = !ModalFilter" icon><v-icon color="#CCCCCC">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-row dense>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
              >เลือกบริษัท :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                :items="companiesList"
                v-model="selectedCompany"
                item-text="name_th"
                item-value="id"
                outlined
                multiple
                dense
                hide-details
                @change="filterShop"
              >
                <template v-slot:selection="{ item, index }">
                  <v-chip v-if="index === 0">
                    <span>{{ item.name_th }}</span>
                  </v-chip>
                  <span
                    v-if="index === 1"
                    class="grey--text text-caption"
                  >
                    (+{{ selectedCompany.length - 1 }} อื่นๆ)
                  </span>
                </template>
              </v-select>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
                >สถานะรายการ :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="selectedOrderType"
                :items="statusList"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                class="setCustomSelect"
                dense
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >Pay Type : </span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                :items="payTypeList"
                v-model="selectedPaytype"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                style="border-radius: 8px;"
                class="setCustomSelect"
                :class="MobileSize ? '' : 'pr-4'"
                outlined
                hide-details
                dense
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่สั่งซื้อ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogBuyDate"
                v-model="modalBuyDate"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="buyDate"
                    v-bind="attrs"
                    v-on="on"
                    hide-details
                    outlined
                    style="border-radius: 8px;"
                    dense
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="date"
                  color = "#27AB9C"
                  scrollable
                  reactive
                  locale="Th-th"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="closeModalBuyDate($refs)">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="
                      saveBuyDate
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่อนุมัติ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogAcceptDate"
                v-model="modalAcceptDate"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="acceptDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    hide-details
                    style="border-radius: 8px;"
                    dense
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="dateApprove"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueAcceptDate(dateApprove)"
                  color = "#27AB9C"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary"
                  @click="
                    closeModalAcceptDate($refs)">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="
                      saveAcceptDate
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >วันที่รอบบริการ :</span
              >
            </v-col>
            <v-col cols="12" >
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangeDate1"
                    v-bind="attrs"
                    v-on="on"
                    style="border-radius: 8px;"
                    outlined
                    dense
                    hide-details
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  range
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="CloseModalRangeDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="setValueRangeDate(dateRange)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >สถานะการชำระเงิน :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                :items="paidStatusList"
                v-model="selectedPaidStatus"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                dense
                class="setCustomSelect"
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-row dense justify="center">
            <v-btn width="125" height="36" text color="#27AB9C" @click="clearFilter()">ล้างค่า</v-btn>
            <v-btn width="125" height="36" rounded color="#27AB9C" class="white--text" @click="saveFilterMobile()">ยืนยัน</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      search: '',
      showCountOrder: 0,
      ModalFilter: false,
      dataTable: [],
      statusList: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'New Service', value: 'newser' },
        { text: 'Change', value: 'change' },
        { text: 'Renew', value: 'renew' },
        { text: 'Change&Renew', value: 'change_and_renew' },
        { text: 'Terminate', value: 'terminate' }
      ],
      selectedOrderType: '',
      selectedPaytype: '',
      payTypeList: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'General', value: 'general' },
        { text: 'OneTime', value: 'onetime' },
        { text: 'Recurring', value: 'recurring' }
      ],
      selectedPaidStatus: '',
      paidStatusList: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ชำระเงินสำเร็จ', value: 'Success' },
        { text: 'ยังไม่ชำระเงิน', value: 'Not Paid' },
        { text: 'ยกเลิกคำสั่งซื้อ', value: 'Cancel' }
      ],
      headersOrdersTable: [
        { text: 'วันที่ทำรายงาน', value: 'created_at', align: 'center', class: 'backgroundTable' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', align: 'center', class: 'backgroundTable' },
        { text: 'Pay Type', value: 'pay_type', align: 'center', class: 'backgroundTable' },
        { text: 'สถานะสั่งซื้อ', value: 'transaction_status', align: 'center', class: 'backgroundTable' },
        { text: 'สถานะรายการ', value: 'order_type', align: 'center', class: 'backgroundTable' },
        { text: 'ผู้ใช้', value: 'buyer_name', align: 'center', class: 'backgroundTable' },
        { text: 'วันที่อนุมัติ', value: 'approve_at', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ใบเสร็จ', value: 'orderIDRef', align: 'center', class: 'backgroundTable' },
        { text: 'ใบเสนอราคา', value: 'quatation_sheet', align: 'center', class: 'backgroundTable' },
        { text: 'ใบแจ้งหนี้', value: 'qt_order_invoice', align: 'center', class: 'backgroundTable' },
        { text: 'วันที่รอบบริการ', value: 'date_contract', align: 'center', class: 'backgroundTable' },
        { text: 'ใบกำกับภาษี', value: 'tax_receipt', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ PR', value: 'pr_document_id', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ PO', value: 'po_document_id', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ SO', value: 'ref_callback_so_id', align: 'center', class: 'backgroundTable' },
        { text: 'cost sheet', value: 'cost_sheet', align: 'center', class: 'backgroundTable' },
        { text: 'สถานะการจัดส่ง', value: 'transportation_status', align: 'center', class: 'backgroundTable' },
        { text: 'Tricking Number', value: 'tracking_number', align: 'center', class: 'backgroundTable' },
        { text: 'จัดการ', value: 'detail', align: 'center', class: 'backgroundTable' }
      ],
      actionsItem: [
        { text: 'รายละเอียด', value: 'detail' }
      ],
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      ordersRequestbody: {
        tax_id: this.taxId,
        list: [15],
        type: 'buyer',
        search: '',
        order_type: '',
        pay_type: '',
        order_created_at: '',
        start_date_contract: '',
        end_date_contreact: '',
        transaction_status: '',
        approve_at: '',
        count: -1,
        pages: 1
      },
      companylistRequestBody: {
        // tax_id: localStorage.getItem('tax_id'),
        tax_id: this.taxId,
        type: 'buyer'
      },
      selectedOrderId: '',
      dateRange: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      modalRangeDate: false,
      dateApprove: '',
      date2: '',
      date3: '',
      modalAcceptDate: false,
      RangeDate1: [],
      modalBuyDate: false,
      buyDate: '',
      acceptDate: [],
      companiesList: [],
      selectedCompany: [],
      createdOrderAt: '',
      approveDate: '',
      taxId: '',
      orderId: ''
    }
  },
  watch: {
    dateRange (val) {
      // console.log('dateRange', val)
      this.startDateToSend = val[0] !== undefined ? val[0] : ''
      this.endDateToSend = val[1] !== undefined ? val[1] : ''
      this.contractStartDate = val[0] !== undefined ? this.formatDateToShow(val[0]) : ''
      this.contractEndDate = val[1] !== undefined ? this.formatDateToShow(val[1]) : ''
      if (this.contractStartDate !== '' && this.contractEndDate !== '') {
        this.RangeDate1 = this.contractStartDate + ' - ' + this.contractEndDate
        this.ordersRequestbody.start_date_contract = val[0]
        this.ordersRequestbody.end_date_contreact = val[1]
      } else {
        this.RangeDate1 = ''
        this.ordersRequestbody.start_date_contract = ''
        this.ordersRequestbody.end_date_contreact = ''
      }
    },
    overlay (val) {
      val &&
        setTimeout(() => {
          this.overlay = false
        }, 500)
    },
    date (val) {
      // console.log(val, 'date')
    },
    dateApprove (val) {
      // this.acceptDate = this.formatDateToShow(val)
      // console.log(this.dateApprove)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listOrderCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listOrderCompany' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavBusiness')
    await this.getTaxId()
    await this.fetchCompanylist()
    await this.fetchListBusinessOrders()
  },
  methods: {
    async gotoActions (item) {
      if (this.MobileSize === false) {
        this.$router.push({ path: `/detailOrderCompany?order_number=${item.order_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/detailOrderCompanyMobile?order_number=${item.order_number}` }).catch(() => {})
      }
    },
    async fetchCompanylist () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListCompany', this.companylistRequestBody)
      var response = await this.$store.state.ModuleBusiness.stateListCompany
      // console.log(response.result.companies)
      for (var i = 0; i < response.result.companies.length; i++) {
        this.companiesList.push(response.result.companies[i])
        this.selectedCompany.push(response.result.companies[i].id)
        // console.log(this.selectedCompany)
        // this.ordersRequestbody.list.push(response.result.companies[i].id)
      }
      this.ordersRequestbody.list = this.selectedCompany
      this.$store.commit('closeLoader')
    },
    async fetchListBusinessOrders () {
      if (this.ordersRequestbody.list.length !== 0) {
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsListBusinessOrders', this.ordersRequestbody)
        var response = await this.$store.state.ModuleBusiness.stateListBusinessOrders
        // console.log(response.result.orders)
        if (response.code !== 500) {
          this.dataTable = response.result.orders
        } else {
          this.$swal.fire({
            icon: 'error',
            text: 'กรุณาเลือกบริษัท',
            showConfirmButton: false,
            timer: 1500
          })
        }
        this.$store.commit('closeLoader')
      }
    },
    formatDate (val) {
      if (val !== '-' && val !== undefined) {
        return val.split(' - ').map(date => {
          new Date(date).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
        }).join(' - ')
      } else {
        return ' - '
      }
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    OpenModalFilter () {
      this.ModalFilter = true
    },
    reSetSearch () {
      this.searchContractStartDate = ''
      this.date = ''
      this.dateApprove = ''
      this.date2 = ''
      this.date3 = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.PayTypeSelect = ''
      this.InvoiceSelect = ''
      this.buyDate = ''
      this.acceptDate = ''
      this.statusImportantSelect = ''
      this.statusSelect = ''
      this.search = ''
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.selectPaymentStatus = ''
      this.dateRange = []
      this.RangeDate1 = []
      this.getOrder()
    },
    async getOrder () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.seller_shop_id.id,
        search_keyword: '',
        pay_type: this.PayTypeSelect,
        required_invoice: this.InvoiceSelect,
        create_date: this.buyDate,
        approved_at: this.acceptDate,
        start_date_contract: this.contractStartDate,
        end_date_contract: this.contractEndDate,
        order_status: this.statusSelect,
        condition_send_cs: this.statusImportantSelect,
        payment_transaction_status: this.selectPaymentStatus
      }
      await this.$store.dispatch('actionPOSellerb2b', data)
      const response = await this.$store.state.ModuleOrder.statePOSellerb2b
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.DataTable = response.data.list_all_data
        // console.log('this.DataTable', this.DataTable)
        this.UrlExponential = response.data.export_reports
        if (this.DataTable.length > 0) {
          this.disableTable = true
        }
        this.ModalFilter = false
      } else {
        this.$store.commit('closeLoader')
        this.ModalFilter = false
        this.$swal.fire({
          icon: 'error',
          text: `${response.message}`,
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    async closeModalBuyDate ($refs) {
      // console.log($refs, 'refs')
      this.modalBuyDate = false
      $refs.dialogBuyDate.save('')
      // this.date = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
      //   .toISOString()
      //   .substr(0, 10)
      this.searchBuyDate = ''
      this.buyDate = ''
      this.date = ''
      this.createdOrderAt = ''
      this.ordersRequestbody.order_created_at = ''
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async saveBuyDate () {
      // this.buyDate = this.date
      this.modalBuyDate = false
      // this.ordersRequestbody.order_created_at = this.date
      this.createdOrderAt = this.date
      this.ordersRequestbody.order_created_at = this.createdOrderAt
      this.buyDate = this.formatDateToShow(this.date)
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
      // console.log(this.buyDate)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    setValueAcceptDate (val) {
      this.searchAcceptDate = val
      // this.acceptDate = this.formatDateToShow(val)
    },
    async setValueRangeDate (date) {
      this.$refs.modalRangeDate.save(date)
      this.modalRangeDate = false
      var dates = ''
      if (date[0] > date[1]) {
        this.ordersRequestbody.start_date_contract = date[1]
        this.ordersRequestbody.end_date_contreact = date[0]
        dates = new Date(date[1]).toLocaleDateString('th-TH') + ' - ' + new Date(date[0]).toLocaleDateString('th-TH')
      } else if (date[0] < date[1]) {
        this.ordersRequestbody.start_date_contract = date[0]
        this.ordersRequestbody.end_date_contreact = date[1]
        dates = new Date(date[0]).toLocaleDateString('th-TH') + ' - ' + new Date(date[1]).toLocaleDateString('th-TH')
      } else if (date[0] === date[1]) {
        this.ordersRequestbody.start_date_contract = date[0]
        this.ordersRequestbody.end_date_contreact = date[0]
        dates = new Date(date[0]).toLocaleDateString('th-TH')
      } else if (date.length === 1) {
        this.ordersRequestbody.start_date_contract = date[0]
        this.ordersRequestbody.end_date_contreact = date[0]
        dates = new Date(date[0]).toLocaleDateString('th-TH')
      }
      this.RangeDate1 = dates
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async CloseModalRangeDate () {
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.startDateToSend = ''
      this.endDateToSend = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.dateRange = []
      this.RangeDate1 = []
      this.ordersRequestbody.start_date_contract = ''
      this.ordersRequestbody.end_date_contreact = ''
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async closeModalAcceptDate ($refs) {
      this.modalAcceptDate = false
      $refs.dialogAcceptDate.save('')
      // console.log($refs.dialogAcceptDate)
      this.dateApprove = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchAcceptDate = ''
      this.acceptDate = ''
      this.ordersRequestbody.approve_at = ''
      this.dateApprove = ''
      this.approveDate = ''
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async saveAcceptDate () {
      this.modalAcceptDate = false
      this.approveDate = this.dateApprove
      this.acceptDate = this.formatDateToShow(this.dateApprove)
      this.ordersRequestbody.approve_at = this.approveDate
      // console.log(this.acceptDate)
      // console.log(this.acceptDate)
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    getTextColorStatus (item) {
      if (item === 'Success') return '#52C41A'
      else if (item === 'Fail') return '#D1392B'
      else if (item === 'Cancel') return '#D1392B'
      else return '#FAAD14'
    },
    getTextStatus (item) {
      if (item === 'Success') return 'ชำระเงินแล้ว'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Cancel') return 'เกินกำหนดชำระ'
      else return 'รอชำระเงิน'
    },
    getColor (item) {
      if (item === 'การจัดส่งสำเร็จ') return '#F0F9EE'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#DBECFA'
      else if (item === 'ส่งคืนสินค้า') return '#F7D9D9'
      else return '#FCF0DA'
    },
    getTextColor (item) {
      if (item === 'การจัดส่งสำเร็จ') return '#1AB759'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#2A70C3'
      else if (item === 'ส่งคืนสินค้า') return '#D1392B'
      else return '#E9A016'
    },
    async getQuatationSheet (val) {
      window.open(val, '_blank')
    },
    async filterShop () {
      this.ordersRequestbody.list = this.selectedCompany
      // console.log(this.ordersRequestbody.list)
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async filterOrdertype () {
      // console.log(this.selectedOrderType)
      this.ordersRequestbody.order_type = this.selectedOrderType
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async filterPayType () {
      // console.log(this.selectedPaytype)
      this.ordersRequestbody.pay_type = this.selectedPaytype
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async filterTransactionStatus () {
      this.ordersRequestbody.transaction_status = this.selectedPaidStatus
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async clearFilter () {
      var response = await this.$store.state.ModuleBusiness.stateListCompany
      this.selectedCompany = []
      for (var i = 0; i < response.result.companies.length; i++) {
        this.selectedCompany.push(response.result.companies[i].id)
      }
      // this.selectedCompany = response.result.companies
      this.ordersRequestbody = {
        tax_id: this.taxId,
        type: 'buyer',
        list: this.selectedCompany,
        search: '',
        order_type: '',
        pay_type: '',
        order_created_at: '',
        start_date_contract: '',
        end_date_contreact: '',
        approve_at: '',
        transaction_status: '',
        count: -1,
        pages: 1
      }
      this.selectedOrderType = ''
      this.selectedPaytype = ''
      this.buyDate = ''
      this.selectedPaidStatus = ''
      this.acceptDate = ''
      this.RangeDate1 = ''
      this.date = ''
      this.dateApprove = ''
      this.dateRange = []
      this.createdOrderAt = ''
      // for (var i = 0; i < response.result.companies.length; i++) {
      //   this.ordersRequestbody.list.push(response.result.companies[i].id)
      // }
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}business/exportBussinessOrder`,
        data: this.ordersRequestbody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'report.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async saveFilterMobile () {
      // console.log(this.ordersRequestbody.order_created_at, '55555')
      this.ordersRequestbody = {
        tax_id: this.taxId,
        type: 'buyer',
        list: this.selectedCompany,
        search: '',
        order_type: this.selectedOrderType,
        pay_type: this.selectedPaytype,
        start_date_contract: '',
        end_date_contreact: '',
        order_created_at: this.createdOrderAt,
        approve_at: this.approveDate,
        transaction_status: this.selectedPaidStatus,
        count: -1,
        pages: 1
      }
      if (this.dateRange.length === 1) {
        this.ordersRequestbody.start_date_contract = this.dateRange[0]
        this.ordersRequestbody.end_date_contreact = this.dateRange[0]
      } else if (this.dateRange.length === 2) {
        if (this.dateRange[0] > this.dateRange[1]) {
          this.ordersRequestbody.start_date_contract = this.dateRange[1]
          this.ordersRequestbody.end_date_contreact = this.dateRange[0]
        } else if (this.dateRange[1] > this.dateRange[0]) {
          this.ordersRequestbody.start_date_contract = this.dateRange[0]
          this.ordersRequestbody.end_date_contreact = this.dateRange[1]
        }
      }
      this.ModalFilter = false
      await this.fetchListBusinessOrders()
    },
    async getTaxId () {
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        // this.taxId = response.data.array_business[0].owner_tax_id
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          this.ordersRequestbody.tax_id = this.taxId
          this.companylistRequestBody.tax_id = this.taxId
        }
        // console.log(this.ordersRequestbody.tax_id, 'tax_id')
      }
    },
    backToMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep table {
  tbody {
  tr {
      td:nth-child(19) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th:nth-child(1) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th:nth-child(19) {
      z-index: 11;
      background: white;
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      }
  }
  }
}
</style>
