<template>
  <v-container style="background: #FFFFFF;" :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mx-0 my-0">
      <div class="d-flex">
        <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการขนส่ง</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการขนส่ง</v-card-title>
        <v-spacer></v-spacer>
        <v-btn color="#27AB9C" style="color: #fff; border-radius: 1vw;">แก้ไข</v-btn>
      </div>
    </v-card>
    <v-row class="pa-4">
        <v-col class="d-flex align-end" style="gap: 2vw; margin-top: -1vw;">
            <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
            <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" :class="MobileSize || IpadSize ? 'pt-4' : ''"></v-switch>
        </v-col>
        <!-- เลือกรายการขนส่ง -->
        <v-col cols="12" md="12" sm="12" v-if="switchShipping">
          <v-row dense>
            <v-col cols="12">
              <span style="font-size: medium;">เลือกรายการขนส่ง <span style="color: red;">*</span></span>
            </v-col>
            <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
              <v-select v-model="SelectShipping" :menu-props="{ offsetY: true }" :items="itemShipping" item-text="name" item-value="code" :rules="Rules.ItemShipping" multiple chips outlined dense placeholder="เลือกขนส่ง" style="border-radius: 8px;">
                <template v-slot:selection="{ item }">
                  <v-chip
                    close
                    outlined
                    @click:close="removeShipping(item)" color="primary"
                  >
                    {{ item.name }}
                  </v-chip>
                </template>
                <template v-slot:item="{ item }">
                  <v-row dense class="py-2">
                    <v-img :src="item.media_path" max-height="50" max-width="50" style="border-radius: 999px;" contain></v-img><span class="pl-4" style="display: grid; align-items: center;">{{ item.name }}</span>
                  </v-row>
                </template>
              </v-select>
            </v-col>
          </v-row>
        </v-col>
        <!-- แสดงรายการขนส่งที่เลือก -->
        <v-col cols="12" style="margin-top: -2vw;">
          <span style="font-size: medium;">รายการขนส่งของร้านค้า</span>
        </v-col>
        <v-col class="d-flex pb-10" style="gap: 1vw; flex-wrap: wrap;">
          <!-- <span>รายการขนส่งของร้าน</span> -->
          <v-card class="pa-5" style="border-radius: 1vw; width: 10vw;" v-for="(items, index) in listSelectShipping"  :key="index">
            <div class="d-flex flex-column align-center" style="gap: 1vw;">
              <v-avatar :size="MobileSize ? 90 : 100" tile v-if="items.media_path !== null" width="80"><img :src="items.media_path" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;"></v-avatar>
              <span>{{items.name}}</span>
            </div>
          </v-card>
        </v-col>
        <!-- <v-col cols="12" class="d-flex justify-end">
          <v-btn color="#27AB9C" style="color: #fff; border-radius: 1vw;">บันทึก</v-btn>
        </v-col> -->
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      switchShipping: true,
      switchAtStore: false,
      shopID: '',
      itemShipping: [],
      SelectShipping: '',
      listSelectShipping: [],
      Rules: {
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ]
      }
    }
  },
  computed: {
    showTextShipping () {
      if (this.switchShipping === false) {
        return 'ไม่ใช้ขนส่งของระบบ'
      } else {
        return 'ใช้ขนส่งของระบบ'
      }
    },
    showTextswitchAtStore () {
      if (this.switchAtStore === false) {
        return 'ไม่มีรับหน้าร้าน'
      } else {
        return 'มีรับหน้าร้าน'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.shopID = JSON.parse(localStorage.getItem('shopSellerID'))
    this.getListShipping()
  },
  watch: {
    SelectShipping (val) {
      console.log(val, 55555)
      this.updateSelectedShippingImages()
      console.log('deee', this.listSelectShipping)
    },
    listSelectShipping (val) {
      console.log(val, 111111)
    }
  },
  methods: {
    async getListShipping () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const auth = {
        headers: { Authorization: `Bearer ${oneData.user.access_token}` }
      }
      const data = {
        seller_shop_id: this.shopID
      }
      const response = await this.axios.post(`${process.env.VUE_APP_BACK_END2}iship/iship_courier_list`, data, auth)
      if (response.data.ok === 'y') {
        this.itemShipping = [...response.data.query_result.filter(item => item.service_provider === 'ISHIP')]
        console.log(this.itemShipping, 9787)
      } else if (response.data.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      }
      await this.GetDetailShop()
    },
    async GetDetailShop () {
      console.log(11111)
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.code === 200) {
          if (response.data[0].shipping_method.length !== 0) {
            this.$store.commit('closeLoader')
            this.switchShipping = true
            // this.listSelectShipping = response.data[0].shipping_method[0]
            // console.log('see ya', this.listSelectShipping)
            this.SelectShipping = response.data[0].shipping_method[0].courier
          } else {
            this.$store.commit('closeLoader')
            this.switchShipping = false
            this.SelectShipping = []
          }
        }
        this.switchAtStore = response.data[0].store_front === 'yes'
        // console.log(this.Detail)
      }
    },
    updateSelectedShippingImages () {
      this.listSelectShipping = this.SelectShipping.map(code => {
        const found = this.itemShipping.find(item => item.code === code)
        return found ? { code: found.code, name: found.name, media_path: found.media_path } : { code, name: code, media_path: '' }
      })
      console.log(this.listSelectShipping)
    },
    removeShipping (item) {
      const index = this.SelectShipping.indexOf(item.code)
      if (index >= 0) this.SelectShipping.splice(index, 1)
    }
  }
}
</script>

<style>

</style>
