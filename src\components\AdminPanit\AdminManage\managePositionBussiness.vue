<template>
  <v-container :style="MobileSize ? 'background-color: white;' : ''">
    <v-row>
      <v-col>
        <span style="font-size: 24px; font-weight: bold;"><v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>จัดการตำแหน่ง Partner นิติบุคคล</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" sm="12" md="6">
        <v-text-field
          dense
          outlined
          v-model="search"
          placeholder="ค้นหาจากชื่อ Partner"
          hide-details
        ></v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        รายการนิติบุคคลที่มี Partner
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-card>
          <v-data-table
            :headers="header"
            :items="tableData"
            style="white-space:nowrap;"
            :search="search"
          >
            <template v-slot:[`item.actions`]="{ item }">
              <v-btn @click="joinBiz(item)" text color="#27AB9C">เข้าสู่ Partner</v-btn>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      search: '',
      tableData: [],
      header: [
        { text: 'ชื่อร้านค้า Partner', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'business_name_th' },
        { text: 'เลขประจำตัวผู้เสียภาษี', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'id_card_num' },
        { text: 'Partner Code', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'partner_code' },
        { text: 'ชื่อบริษัท', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'company_name_th' },
        { text: 'อีเมล', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'email' },
        { text: 'จัดการ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'actions' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    await this.getListServicePartner()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/managePositionBussinessMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/managePositionBussiness' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    async getListServicePartner () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetListServicePartner')
      var response = await this.$store.state.ModuleAdminManage.stateGetListServicePartner
      if (response.code === 200) {
        this.tableData = response.data
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
      this.$store.commit('closeLoader')
    },
    async joinBiz (val) {
      localStorage.removeItem('business_id')
      localStorage.setItem('business_id', val.business_id)
      var data = {
        tax_id: val.id_card_num
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetJoinServicePartnerWithAdmin', data)
      var response = await this.$store.state.ModuleAdminManage.stateGetJoinServicePartnerWithAdmin
      this.$store.commit('closeLoader')
      if (response.code === 200) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          text: `${response.message}`
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: `${response.message}`
        })
      }
      if (this.MobileSize) {
        this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
      }
    },
    backtoMenu () {
      this.$router.push('/AdminPanitMobile')
    }
  }
}
</script>

<style>

</style>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
