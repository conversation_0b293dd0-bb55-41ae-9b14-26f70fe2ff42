<template>
<div class="pt-3">
<a-popover v-model="visibleLogin" placement="bottom" trigger="click" class="borderPopOver">
  <template slot="content">
    <a-card :bordered="false" style="max-width: 350px; width: 350px; height:100%">
      <a-row type="flex" justify="center">
        <a-col :span="24">
          <a-row type="flex" justify="center">
            <h2>เข้าสู่ระบบ</h2>
          </a-row>
        </a-col>
        <a-col :span="24">
          <a-form
            id="components-form-demo-normal-login"
            :form="form"
            class="login-form"
            @submit="LoginMarket"
          >
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'emailMarket',
                  { rules: [{ required: true, message: 'กรุณากรอกอีเมล' }] },
                ]"
                placeholder="อีเมล*"
              >
                <a-icon slot="prefix" type="user" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-input-password
                size="large"
                v-decorator="[
                  'passwordMarket',
                  { rules: [{ required: true, message: 'กรุณากรอกรหัสผ่าน' }] },
                ]"
                placeholder="รหัสผ่าน*"
              >
                <a-icon slot="prefix" type="lock" style="color: rgba(0,0,0,.25)" />
              </a-input-password>
            </a-form-item>
            <a class="login-form-forgot" href="" style="padding-botton: 2px; color: #1E90FF;">
              ลืมรหัสผ่าน
            </a>
            <a-button size="large" html-type="submit" class="login-form-button" style="background-color: rgb(111,183,87); color: white; border-color: rgb(111,183,87);">
              เข้าสู่ระบบ
            </a-button>
          </a-form>
        </a-col>
      </a-row>
      <!-- <v-col cols="12">
        <v-text-field v-model="emailMarket" :rules="Rules.email" class="borderButtomText" outlined placeholder="อีเมล*"  prepend-inner-icon="mdi-account" dense></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-text-field v-model="passwordMarket" :rules="Rules.password" class="borderButtomText" outlined placeholder="รหัสผ่าน*"  prepend-inner-icon="mdi-lock" type="password" dense></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-btn dense block @click="LoginMarket()" style="background-color: rgb(111,183,87); color: white;">เข้าสู่ระบบ</v-btn>
      </v-col> -->
    </a-card>
    <v-row dense align="center">
      <v-divider></v-divider>หรือ<v-divider></v-divider>
    </v-row>
    <a-card :bordered="false" style="max-width: 350px; width: 350px; height:100%">
      <template slot="actions">
        <span style="cursor: default; color: rgb(60, 60, 60);">ยังไม่เคยสมัครสมาชิก ? <span @click="showRegister()" style="text-decoration: underline; color: #1E90FF;cursor: pointer;">สมัครสมาชิก</span> ที่นี่</span>
      </template>
      <v-row>
        <v-col cols="12">
          <v-btn @click="Login()" block outlined style="border-color: rgb(82, 86, 89); color: rgb(82, 86, 89); caret-color: rgb(82, 86, 89);"><v-img :src="require('@/assets/logo_one.png')" max-height="30" max-width="45" contain style="margin-right: 6px;"/>เข้าสู่ระบบด้วย one id</v-btn>
        </v-col>
      </v-row>
    </a-card>
  </template>
  <v-btn class="hidden-sm-and-down" text dark>เข้าสู่ระบบ</v-btn>
</a-popover>
<span style="color: white;">|</span>
<a-popover v-model="visibleRegis" placement="bottom" trigger="click" class="borderPopOver">
  <template slot="content">
    <a-card :bordered="false" style="max-width: 350px; width: 350px; height:100%">
      <a-row type="flex" justify="center">
        <a-col :span="24">
          <a-row type="flex" justify="center">
            <h2>สมัครสมาชิก</h2>
          </a-row>
        </a-col>
        <a-col :span="24">
          <a-form
            id="components-form-demo-normal-login"
            :form="FormRegister"
            class="login-form"
            @submit="Register"
          >
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'username',
                  { rules: [{ required: true, message: 'กรุณากรอกชื่อผู้ใช้งาน' }] },
                ]"
                placeholder="ชื่อผู้ใช้งาน*"
              >
                <a-icon slot="prefix" type="user" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-tooltip placement="topLeft" overlay-class-name="numeric-input">
                <template slot="title">
                  <ul>
                    <li>- ตัวพิมพ์เล็กอย่างน้อยหนึ่งตัว</li>
                    <li>- ตัวพิมพ์ใหญ่อย่างน้อยหนึ่งตัว</li>
                    <li>- ตัวเลขอย่างน้อยหนึ่งตัว</li>
                    <li>- ขั้นต่ำ 8 อักขระ</li>
                  </ul>
                </template>
                <a-input-password
                  size="large"
                  v-decorator="[
                    'password',
                    { rules: [{ required: true, message: 'กรุณากรอกรหัสผ่าน' }] },
                  ]"
                  placeholder="รหัสผ่าน*"
                >
                  <a-icon slot="prefix" type="lock" style="color: rgba(0,0,0,.25)" />
                </a-input-password>
              </a-tooltip>
            </a-form-item>
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'email',
                  { rules: [{ required: true, message: 'กรุณากรอกอีเมล' }] },
                ]"
                placeholder="อีเมล*"
              >
                <a-icon slot="prefix" type="mail" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'phone',
                  { rules: [{ required: true, message: 'กรุณากรอกเบอร์โทรศัพท์' }] },
                ]"
                placeholder="หมายเลขโทรศัพท์"
              >
                <a-icon slot="prefix" type="mobile" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-button size="large" html-type="submit" class="login-form-button" style="background-color: rgb(111,183,87); color: white;">
              สมัครสมาชิก
            </a-button>
          </a-form>
        </a-col>
      </a-row>
    </a-card>
    <!-- <v-form ref="FormRegister" :lazy-validation="lazy">
      <a-card :bordered="false" style="max-width: 350px; width: 350px; height:100%">
        <v-row >
          <v-col cols="12">
            <v-row align="center">
              <h2 style="position: absolute; left: 125px;">สมัครสมาชิก</h2>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-text-field v-model="username" :rules="Rules.name" class="borderButtomText" outlined placeholder="ชื่อผู้ใช้งาน*"  prepend-inner-icon="mdi-account" dense></v-text-field>
          </v-col>
          <v-col cols="12">
            <v-text-field v-model="password" :rules="Rules.password" class="borderButtomText" outlined placeholder="รหัสผ่าน*"  prepend-inner-icon="mdi-lock" type="password" dense></v-text-field>
          </v-col>
          <v-col cols="12">
            <v-text-field v-model="email" :rules="Rules.email" class="borderButtomText" outlined placeholder="อีเมล*" prepend-inner-icon="mdi-email" dense></v-text-field>
          </v-col>
          <v-col cols="12">
            <v-text-field v-model="phone" :rules="Rules.tel" class="borderButtomText" outlined placeholder="หมายเลขโทรศัพท์*" prepend-inner-icon="mdi-cellphone" dense></v-text-field>
          </v-col>
          <v-col cols="12">
            <v-btn @click="Register" block style="background-color: rgb(111,183,87); color: white;">สมัครสมาชิก</v-btn>
          </v-col>
        </v-row>
      </a-card>
    </v-form> -->
  </template>
  <v-btn class="hidden-sm-and-down" text dark>สมัครสมาชิก</v-btn>
</a-popover>
</div>
</template>
<script>
// import axios from 'axios'
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      tab: null,
      username: '',
      password: '',
      emailMarket: '',
      passwordMarket: '',
      email: '',
      phone: '',
      items: [
        { header: 'เข้าสู่ระบบ' },
        { header: 'สมัครสมาชิก' }
      ],
      visibleLogin: false,
      visibleRegis: false,
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        name: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้งาน'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ],
        email: [
          v => !!v || 'กรุณากรอกอีเมล'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ]
      },
      form: this.$form.createForm(this, { name: 'normal_login' }),
      FormRegister: this.$form.createForm(this, { name: 'register' })
    }
  },
  created () {
    this.$EventBus.$on('closeModalLogin', this.closeModalLogin)
    this.$EventBus.$on('openModalLogin', this.openModalLogin)
    this.$EventBus.$on('closeModalRegister', this.closeModalRegister)
    this.$EventBus.$on('openModalRegister', this.openModalRegister)
  },
  methods: {
    Register (e) {
      e.preventDefault()
      this.FormRegister.validateFields((err, values) => {
        // console.log(err, values)
        if (!err) {
          // console.log('ก่อนยิง api', Object.prototype.hasOwnProperty.call(localStorage, 'oneData'))
          var checkonedata = Object.prototype.hasOwnProperty.call(localStorage, 'oneData')
          if (checkonedata) {
            var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            onedata.CurrentPath = this.$router.currentRoute.path
            this.APIRegister(values)
          } else {
            onedata = {}
            onedata.CurrentPath = this.$router.currentRoute.path
            localStorage.setItem('oneData', Encode.encode(onedata))
            this.APIRegister(values)
          }
        }
      })
    },
    async APIRegister (values) {
      var data = {
        username: values.username,
        password: values.password,
        email: values.email,
        mobile_no: values.phone
      }
      // console.log(data)
      var RegisterData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/register_and_login`, data)
      if (RegisterData.data.result === 'SUCCESS') {
        this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
        this.username = ''
        this.password = ''
        this.email = ''
        this.phone = ''
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.user = RegisterData.data.data
        var dataRole = {
          role: 'ext_buyer'
        }
        localStorage.setItem('roleUser', JSON.stringify(dataRole))
        localStorage.setItem('oneData', Encode.encode(onedata))
        window.location.reload()
      } else {
        this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
      }
    },
    async Login () {
      // console.log('this.$router.currentRoute.params.data', this.$router.currentRoute.path)
      var onedata = {}
      onedata.CurrentPath = this.$router.currentRoute.path
      // console.log(onedata)
      localStorage.setItem('oneData', Encode.encode(onedata))
      // console.log('ข้อมูล local', JSON.parse(Decode.decode(localStorage.getItem('oneData'))))
      window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
    },
    LoginMarket (e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          var data = {
            email: values.emailMarket,
            password: values.passwordMarket
          }
          // console.log(data)
          var LoginData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/login_market`, data)
          // console.log(LoginData)
          if (LoginData.data.result === 'SUCCESS') {
            this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
            values.emailMarket = ''
            values.passwordMarket = ''
            this.form.resetFields()
            var dataRole = {
              role: 'ext_buyer'
            }
            localStorage.setItem('roleUser', JSON.stringify(dataRole))
            var onedata = {}
            // console.log('onedata ====>', onedata)
            onedata.user = LoginData.data.data
            localStorage.setItem('oneData', Encode.encode(onedata))
            window.location.reload()
          } else {
            this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
            this.passwordMarket = ''
            this.openModalLogin()
          }
        }
      })
    },
    closeModalLogin () {
      this.visibleLogin = false
    },
    openModalLogin () {
      this.visibleLogin = true
    },
    closeModalRegister () {
      this.visibleRegis = false
    },
    openModalRegister () {
      this.visibleRegis = true
    },
    showRegister () {
      this.visibleRegis = true
      this.visibleLogin = false
    }
  }
}
</script>

<style scoped>
.borderButtomText {
  margin-bottom: -30px
}
</style>

<style>
.numeric-input .ant-tooltip-inner {
  min-width: 50px;
  min-height: 35px;
}
.numeric-input .numeric-input-title {
  font-size: 14px;
}
.ant-popover-inner-content{
  padding: 0;
}
#components-form-demo-normal-login .login-form {
  max-width: 350px;
}
#components-form-demo-normal-login .login-form-forgot {
  float: right;
  margin-bottom: 10px;
  margin-top: -15px;
}
#components-form-demo-normal-login .login-form-button {
  width: 100%;
}
</style>
