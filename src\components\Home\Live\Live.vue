<template>
  <v-card class="height: 100vh pa-6 ma-6" min-height="500px">
    <v-row justify="center" v-if="this.listLiving.length > 0">
      <v-col cols="12" md="6" sm="6" align="center">
        <v-text-field
          append-icon="mdi-magnify"
          placeholder="ค้นหาห้องไลฟ์"
          outlined
          dense
          rounded
          hide-details>
        </v-text-field>
      </v-col>
      <v-col cols="12" align="center">
        <a-tabs v-model="show" v-if="MobileSize">
          <a-tab-pane :key="0"><span slot="tab">ทั้งหมด </span></a-tab-pane>
          <a-tab-pane :key="1"><span slot="tab">สำหรับคุณ </span></a-tab-pane>
        </a-tabs>
        <v-btn :color="show == 0 ? 'primary' : 'gray'" class="ml-2" @click="show = 0" v-if="!MobileSize">ทั้งหมด</v-btn>
        <v-btn :color="show == 1 ? 'primary' : 'gray'" class="ml-2" @click="show = 1" v-if="!MobileSize">สำหรับคุณ</v-btn>
      </v-col>
    </v-row>

    <v-row height="100%" v-if="this.listLiving.length > 0">
      <v-col cols="2" style="min-height:400px;" v-if="listLiving.length > 0 && !MobileSize">
        <v-card height="100%" outlined class="pa-4">
          <div class="">
            <p>LIVE ที่แนะนำ</p>
            <v-row dense v-for="(item, index) in listLiving" :key="index" class="pt-2">
              <div @click="gotoFullive(item)" style="cursor: pointer;">
                <TwoLayerCircle
                :size="40"
                :value="100"
                color="#27AB9C"
                :imgURL="item.logoShopPath"
                :smallSize="true"
                :live="true"
                />
                <span class="pt-2 pl-1"><b>{{ item.sellerShopName | truncate(10, '...') }}</b></span>
                <!-- <span class="pt-2 ml-auto">{{ item.numParticipants }}</span> -->
              </div>
            </v-row>
          </div>
        </v-card>
      </v-col>

      <v-col :cols="listLiving.length > 0 ? '12' : '12'" sm="10">
        <v-row v-if="show == 0">
          <v-col cols="12" sm="4" class="d-flex justify-center" :md="mdCol" v-for="(item, index) in listLiving" :key="index" align="center">
            <div style="height: 400px; width: 100%;" @click="gotoFullive(item)">
              <MiniLive
                :roomName="item.name"
                :title="item.title !== null ? item.title : ''"
                :shopName="item.sellerShopName"
                :imgURL="item.logoShopPath"
                :sid="item.sid"
                :viewers="item.numParticipants"
                @fullLive="newRoom(item)"
              />
            </div>
          </v-col>
          <!-- <v-col v-if="colLeft > 0 || listLiving.length == 0" align="center" align-self="center">
            <div :min-height="listLiving.length == 0 ? '400px' : '200px'" text-center class="d-flex align-center justify-center">
              <span v-if="listLiving.length > 0">สิ้นสุดผลลัพท์การค้นหา</span>
              <span v-else>ยังไม่มีไลฟ์</span>
            </div>
          </v-col> -->
        </v-row>

        <v-row v-else>
          <FullLive
            :listLiving="listLiving"
          />
          <!-- <v-col>
            <v-carousel  height="100%" hide-delimiters v-if="listLiving.length > 0">
              <template v-slot:prev="{ on, attrs }">
                <v-btn
                  icon
                  id="prev"
                  v-bind="attrs"
                  :v-on="getTrackSuccess? on : ''"
                  :disabled="currentIndex == 0"
                  @click="NextLive(on, attrs)"
                ><v-icon color="#27AB9C" large>mdi-chevron-left</v-icon></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  icon
                  id="next"
                  v-bind="attrs"
                  :v-on="getTrackSuccess? on : ''"
                  :disabled="(listLiving.length - 1) == currentIndex"
                  @click="NextLive(on, attrs)"
                >
                <v-icon color="#27AB9C" large>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
              <v-carousel-item v-for="(item, index) in listLiving" :key="index">
                <v-card :id="`card${index}`" @click="LeaveLivePrevious(item)">
                  <FullLive
                  :roomName="item.name"
                  :shopName="item.sellerShopName"
                  :imgURL="item.logoShopPath"
                  :sid="item.sid"
                  :viewers="item.numParticipants"
                />
                </v-card>
              </v-carousel-item>
            </v-carousel>
            <v-card v-else min-height="400px" text-center class="d-flex align-center justify-center">
              <v-col align="center">
                <p style="font-size: 20px;"><b>ยังไม่มีวิดีโอ LIVE สำหรับคุณ</b></p>
                <p>ย้อนกลับเพื่อสำรวจวิดีโอเพิ่มเติม</p>
                <v-btn>ย้อนกลับ</v-btn>
              </v-col>
            </v-card>
          </v-col> -->
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="this.listLiving.length === 0">
      <v-col class="d-flex justify-center">
        <div style="height: 400px;" class="d-flex align-center">
          <span style="font-size: 24px; font-weight: bold;">ไม่มีไลฟ์ในขณะนี้</span>
        </div>
      </v-col>
    </v-row>
  </v-card>
</template>

<script>
import { Decode } from '@/services'
import TwoLayerCircle from './component/TwoLayerCircle.vue'
import MiniLive from './component/MiniLive.vue'
import FullLive from './component/FullLive.vue'
import { Tabs } from 'ant-design-vue'
import { io } from 'socket.io-client'
import {
  Room,
  RoomEvent
} from 'livekit-client'
export default {
  components: {
    TwoLayerCircle,
    MiniLive,
    FullLive,
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      show: 0, // 0:showAll, 1:ForU

      // data all live
      listLiving: [],
      mdCol: 3,
      colLeft: null,
      userName: '',

      // data live for U
      listLiveSuggested: [],

      // roomkit
      room: null,
      remoteTracksMap: new Map(),
      getTrackSuccess: false,
      track: [],
      // LIVEKIT_URL: 'wss://helloworld-nt1b7zmh.livekit.cloud',
      LIVEKIT_URL: 'wss://meet-lab.one.th',

      // arrow
      currentIndex: 0,
      hostName: '',
      currentLive: []
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit)) + '...'
      }
      return value
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  async mounted () {
    this.socket = io(process.env.VUE_APP_BACK_END2.substring(0, 35), {
      path: '/api/backend_2/socket.io'
    })
    this.socket.on('update_live', async (data) => {
      // var oldData = this.listLiving
      // console.log(this.listLiving[this.currentIndex], 458)
      // this.currentLive.push(listLiving)
      // console.log(this.currentLive, this.listLiving, 'this.currentLive')
      // if (data.length === 0) {
      //   this.$swal.fire({
      //     showConfirmButton: false,
      //     timerProgressBar: true,
      //     icon: 'error',
      //     title: 'ไม่มีไลฟ์ในขณะนี้'
      //   })
      // }
      // var primeLive = {}
      if (data.length < this.listLiving.length) {
        // console.log('ไลฟ์หาย', this.listLiving)
        const removed = this.listLiving.filter(oldItem =>
          !data.some(newItem => newItem.name === oldItem.name)
        )
        this.currentIndex = await this.listLiving.findIndex(e => e.name === removed[0].name)
        // console.log(removed, 'removed', this.currentIndex)
        // console.log(this.hostName, 'hostName')
        // console.log(this.listLiving[this.currentIndex].name === removed[0].name, 'this.listLiving[this.currentIndex].name')
        // console.log(this.listLiving.filter(oldItem =>
        //   removed.some(newItem => newItem.name === oldItem.name)
        // ).length, '5658')
        if (this.listLiving[this.currentIndex].name === this.hostName) {
          // console.log('ในที่สุดก็ทำได้')
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timerProgressBar: true,
          //   icon: 'warning',
          //   title: 'ไลฟ์จบลงแล้ว'
          // })
          if (this.listLiving.length !== 0 && data.length !== 0) {
            this.currentIndex = 0
            // console.log(data, 'data')
            this.hostName = data[0].name
            // console.log('ทดสอบ1234', data[0])
            // this.currentIndex = await this.listLiving.findIndex(e => e.name === this.hostName)
            await this.$EventBus.$emit('ChangeHostNameLiveStream', this.hostName)
            // await this.newRoom(this.listLiving[this.currentIndex])
          }
        }
        // if (removed[0].name !== this.listLiving[this.currentIndex].name) {
        //   console.log('ไลฟ์ที่ดูอยู่หาย')
        // }
        // this.currentIndex = this.listLiving.findIndex(e => e.name === this.hostName)
        // console.log(removed, 'removed')
        // this.currentIndex = await this.listLiving.findIndex(e => e.name === removed[0].name)
        // await this.newRoom(this.listLiving[this.currentIndex])
        // console.log(data.length, 'data')
        // console.log(this.listLiving.length, 'length')
      }
      // primeLive = this.listLiving.filter(e => !data.some(f => f.name === e.name))
      // console.log(primeLive, 'primeLive')
      this.listLiving = data
      // console.log(this.listLiving.length, data.length, 'this.listLiving')
      if (this.listLiving[this.currentIndex] === undefined) {
        // console.log('ไลฟ์ปิดไปแล้ว')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timerProgressBar: true,
        //   icon: 'warning',
        //   title: 'ไลฟ์จบลงแล้ว'
        // })
        this.currentIndex = 0
        // console.log(this.listLiving[this.currentIndex], 'index undefined')
        await this.newRoom(this.listLiving[this.currentIndex])
      } else {
        // console.log('ยังเป็นไลฟ์เดิม')
      }
      // console.log(this.listLiving[this.currentIndex], 'index')
      // console.log(data, 'data')
    })
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.userName = onedata.user.username
    // this.newRoom()
  },
  async created () {
    await this.getAllLive()
    await this.checkPath()
    // await this.newRoom()
  },
  unmounted () {
    if (this.socket) {
      this.socket.off('update_live')
      this.socket.disconnect()
    }
  },
  async beforeDestroy () {
    // this.socket.off('update_live')
    if (this.socket) {
      this.socket.off('update_live')
      this.socket.disconnect()
    }
  },
  methods: {
    async getAllLive () {
      var data = null
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}getAllRoom`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      const res = await response.json()
      if (res.message === 'get list rooms success') {
        data = res.data
        // console.log(data, 'this.listLiving 5678')
        this.listLiving = data
        // await this.getRoom(this.listLiving[0])
        this.colLeft = this.listLiving.length % (12 / this.mdCol)
        // await (this.listLiving)
      }
    },
    async newRoom (item) {
      // ครั้งแรกจะไม่มี item เวลากด next จะมี item เดิมอยู่ต้องลบก่อน
      // console.log('newRoom', item)
      this.$store.commit('openLoader')
      if (item !== undefined) {
        await this.leaveRoom()
      }
      // console.log(item, 'ได้อะไรมาบ้าง')
      this.room = new Room()
      this.remoteTracksMap = new Map()
      await this.room.on(
        RoomEvent.TrackSubscribed,
        (_track, publication, participant) => {
          // console.log('TrackSubscribed')
          this.remoteTracksMap.set(publication.trackSid, {
            trackPublication: publication,
            participantIdentity: participant.identity
          })
          // this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
        }
      )
      // console.log('เข้ามั้ยเนี่ย', this.listLiving)

      this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        // this.viewers = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
        // console.log('Host ออกจากห้อง')
        // console.log('Live: first', participant)
        // console.log('ยังอยู่มั้ย')
        // console.log(this.listLiving, 'before')
        if (participant.identity.includes('Host')) {
          this.listLiving = this.listLiving.filter(obj => obj.name !== participant.identity)
          // console.log(this.listLiving, 'after')
          // console.log('เหลือกี่ตัว', this.listLiving)
          if (this.hostName === '') {
            this.currentIndex = 0
          } else {
            this.currentIndex = this.listLiving.findIndex(e => e.name === this.hostName)
          }
          if (this.listLiving.length > 1) {
            if (this.currentIndex === this.listLiving.length - 1) { // ถ้าอยู่หน้าสุดท้ายของสไลด์
              this.currentIndex = 0
              // for (var i = 0; i < this.listLiving.length; i++) {
              //   console.log(this.listLiving[i], 'help')
              // }
              document.getElementById('prev').click()
            } else {
              document.getElementById('next').click()
            }
          }
        }
      })
      // console.log('อะไรเปลี่ยน', this.listLiving)
      if (item !== undefined) {
        this.getRoom(item)
      }
      this.$store.commit('closeLoader')
    },
    async getRoom (item) {
      // console.log('getRoom ทำงาน')
      this.$store.commit('openLoader')
      var roomName = item.name
      if (roomName) {
        this.token = await this.getToken(roomName, this.userName)
        this.$store.commit('openLoader')
        await this.room.connect(this.LIVEKIT_URL, this.token)
        this.$store.commit('closeLoader')
        for (const remoteTrack of this.remoteTracksMap.values()) {
          if (remoteTrack.trackPublication.kind === 'video') {
            this.track = remoteTrack.trackPublication.videoTrack
          }
        }

        // console.log('listLiving bf', this.listLiving)
        // this.listLiving = this.listLiving.sort((a, b) => {
        //   return a.sellerShopId === item.sellerShopId ? -1 : b.sellerShopId === item.sellerShopId ? 1 : 0
        // })
        // console.log('listLiving af', this.listLiving)
        this.show = 1
        this.getTrackSuccess = true
        this.$store.commit('closeLoader')
      }
    },
    async getToken (roomName, participantName) {
      // console.log('ยิงกี่รอบ')
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roomName, participantName })
      })
      this.$store.commit('closeLoader')
      if (!response.ok) {
        const error = await response.json()
        throw new Error(`Failed to get token: ${error.errorMessage}`)
      }

      const data = await response.json()
      return data.token
    },
    async NextLive (item, attrs) {
      if (attrs['aria-label'] === 'Next visual') {
        this.currentIndex += 1
      } else {
        this.currentIndex -= 1
      }
      // this.getTrackSuccess = false
      // console.log('listLiving', this.listLiving[this.currentIndex])
      await this.newRoom(this.listLiving[this.currentIndex])
      // document.getElementById('card0').click()
      // console.log('this.currentIndex', this.currentIndex)
      // console.log('item', document.getElementById('card0'))
    },
    async LeaveLivePrevious (item) {
      // console.log('LeaveLifePrevious', item)
      // await this.leaveRoom()
      // this.getTrackSuccess = false
    },
    async leaveRoom () {
      this.getTrackSuccess = false
      // console.log('leaveRoom', this.room)
      if (this.room) {
        await this.room.disconnect()
        this.connected = false
      }
    },
    async checkPath () {
      var path = await this.$route.query.name
      if (path) {
        // console.log(path, 'as45')
        this.show = 1
        this.hostName = path
        setTimeout(() => {
          this.$EventBus.$emit('ChangeHostNameLiveStream', this.hostName)
        }, 500)
      }
      // console.log('hostName', this.hostName)
      // this.currentIndex = this.listLiving.findIndex(e => e.name === this.hostName)
      // this.$EventBus.$emit('ChangeHostNameLiveStream', this.hostName)
      // await this.newRoom(this.listLiving[this.currentIndex])
    },
    async gotoFullive (item) {
      // console.log(item, 'gotoFullLive')
      this.hostName = item.name
      this.show = 1
      this.currentIndex = await this.listLiving.findIndex(e => e.name === this.hostName)
      // console.log(this.hostName, 'hostname')
      await this.$EventBus.$emit('ChangeHostNameLiveStream', this.hostName)
      // await this.newRoom(this.listLiving[this.currentIndex])
    }
  }
}
</script>

<style scoped>
</style>
