<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-3">
      <v-row class="d-flex align-center">
        <v-col class="d-flex align-center">
          <v-card-title style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; white-space: nowrap;" v-if="!MobileSize">รายการโอนเงิน</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2 d-flex" @click="backtoPage()">mdi-chevron-left</v-icon>รายการโอนเงิน</v-card-title>
          <v-spacer></v-spacer>
          <v-btn rounded color="primary" class="mb-2 mr-2" @click="ExportInfo()">Export</v-btn>
        </v-col>
      </v-row>
      <v-row class="mx-2" :style="MobileSize ? 'font-size: large; font-weight: bold;' : 'font-size: large; font-weight: bold;'">
        <v-col :cols="MobileSize || IpadSize ? 12 : 4">
          <v-card class="d-flex flex-column align-center pa-2">
            <img class="mr-2" src="@/assets/icons/totalPrice.png" width="60" height="60">
            <span>ยอดรวมทุกร้านค้า</span>
            <span>{{ sumShop }}</span>
          </v-card>
        </v-col>
        <v-col :cols="MobileSize || IpadSize ? 6 : 4">
          <v-card class="d-flex flex-column align-center pa-2">
            <img class="mr-2" src="@/assets/box_2.png" width="60" height="60">
            <span>รวมออร์เดอร์</span>
            <span>{{ totalOrder }}</span>
          </v-card>
        </v-col>
        <v-col :cols="MobileSize || IpadSize ? 6 : 4">
          <v-card class="d-flex flex-column align-center pa-2">
            <img class="mr-2" src="@/assets/icons/checklist_2.png" width="60" height="60">
            <span>รวมร้านค้า</span>
            <span>{{ totalShop }}</span>
          </v-card>
        </v-col>
        <v-col>
          <v-data-table
            :headers="headers"
            :items="listOrder"
            :search="search"
            style="white-space: nowrap; width: 100%; font-weight: normal"
            height="100%"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-1"
            >
              <template v-slot:[`item.orderDetail`]="{ item }">
                <v-btn v-if="item.order_detail.length !== 0" outlined rounded color="#27AB9C" @click="openDialogDetailOrder(item.order_detail)">รายละเอียด</v-btn>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.shop_name`]="{ item }">
                <div>
                  <div v-if="item.shop_name" color="#27AB9C">
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on"> {{ substring(item.shop_name) }}</span>
                      </template>
                      <span>{{ item.shop_name }}</span>
                    </v-tooltip>
                  </div>
                  <span v-else>-</span>
                </div>
              </template>
              <template v-slot:[`item.total`]="{ item }">
                <span v-if="item.order_detail.length !== ''">{{ parseFloat(item.total).toLocaleString('en-US') }}</span>
                <span v-else>-</span>
              </template>
          </v-data-table>
        </v-col>
      </v-row>
      <v-row class="mx-5 mt-6">
        <!-- <v-data-table
          :headers="headers"
          :items="listOrder"
          :search="search"
          style="white-space: nowrap; width: 100%"
          height="100%"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          class="elevation-1"
          >
            <template v-slot:[`item.orderDetail`]="{ item }">
              <v-btn v-if="item.order_detail.length !== 0" outlined rounded color="#27AB9C" @click="openDialogDetailOrder(item.order_detail)">รายละเอียด</v-btn>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.shop_name`]="{ item }">
              <div>
                <div v-if="item.shop_name" color="#27AB9C">
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on"> {{ substring(item.shop_name) }}</span>
                    </template>
                    <span>{{ item.shop_name }}</span>
                  </v-tooltip>
                </div>
                <span v-else>-</span>
              </div>
            </template>
            <template v-slot:[`item.total`]="{ item }">
              <span v-if="item.order_detail.length !== ''">{{ parseFloat(item.total).toLocaleString('en-US') }}</span>
              <span v-else>-</span>
            </template>
        </v-data-table> -->
      </v-row>
    </v-card>
    <v-dialog v-model="dialogDetailOrder" width="500px" content-class="elevation-0">
      <v-card style="border-radius: 22px;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: large; font-weight: 700; color: #FFFFFF;">เลขออร์เดอร์
          </span>
          <v-btn icon dark @click="dialogDetailOrder = false">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="d-flex mt-2" :class="MobileSize ? 'flex-column' : ''">
          <span class="mr-1" style="white-space: nowrap;"><b>เลขออร์เดอร์ในรายการนี้: </b></span>
          <span> {{ allOrders }}</span>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import axios from 'axios'
import { Decode } from '@/services'
export default {
  data () {
    return {
      sumShop: '',
      totalOrder: '',
      totalShop: '',
      listOrder: [],
      allOrders: [],
      dialogDetailOrder: false,
      search: '',
      headers: [
        { text: 'ชื่อร้านค้า', align: 'center', sortable: false, value: 'shop_name', class: 'backgroundTable fontTable--text' },
        { text: 'เลขบัญชี', align: 'center', sortable: false, value: 'account_no', class: 'backgroundTable fontTable--text' },
        { text: 'ธนาคาร', align: 'center', sortable: false, value: 'bank', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดโอน (บาท)', align: 'center', sortable: false, value: 'total', class: 'backgroundTable fontTable--text' },
        { text: 'เลข order', align: 'center', sortable: false, value: 'orderDetail', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน order', align: 'center', sortable: false, value: 'total_orders', class: 'backgroundTable fontTable--text' }
      ],
      onedata: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.listTransfer()
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.current_role_user.super_admin === false && this.onedata.user.current_role_user.super_admin_platform === false && this.onedata.user.current_role_user.admin_platform === false) {
        this.$router.push({ path: '/' }).catch(() => {})
      }
      // console.log(this.onedata)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardTransferMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardTransfer' }).catch(() => {})
      }
    }
  },
  methods: {
    async ExportInfo () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END2}payment/getDashboardTransfer`,
        method: 'POST',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Dashboard_Transfer_Info_${dateCurrent.replaceAll(' ', '_')}.xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    openDialogDetailOrder (text) {
      this.allOrders = text.map(item => item.order_number)
      this.dialogDetailOrder = true
    },
    async listTransfer () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsTransfer')
      var res = await this.$store.state.ModuleAdminManage.stateTransfer
      if (res.code === 200) {
        this.sumShop = parseFloat(res.total_sum_shop).toLocaleString('en-US')
        this.totalOrder = res.total_order
        this.totalShop = res.total_shop
        this.listOrder = res.data
        this.$store.commit('closeLoader')
      } else if (res.code === 401) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    substring (data) {
      if (this.MobileSize) {
        return data.length > 40 ? data.substring(0, 40) + '...' : data
      } else if (this.IpadSize) {
        return data.length > 20 ? data.substring(0, 20) + '...' : data
      } else {
        return data.length > 50 ? data.substring(0, 50) + '...' : data
      }
    }
  }
}
</script>

<style>

</style>
