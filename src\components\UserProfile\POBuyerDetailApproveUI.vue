<template lang="html">
  <v-container grid-list-xl>
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-row justify="center" class="my-4">
      <h2 style="font-weight: 700; font-size: 24px;"> สถานะการอนุมัติการซื้อของสินค้า </h2>
    </v-row>
    <v-row no-gutters class="mx-4">
      <v-col cols="9">
        <h2 style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</h2>
      </v-col>
      <v-col cols="3" align="left">
        <span>รหัสการสั่งซื้อ : </span>
        <span>{{ items.payment_transaction }}</span>
      </v-col>
      <v-col cols="6" class="mb-2">
        <span>{{ items.address_data }}</span>
      </v-col>
      <v-col cols="3"></v-col>
      <v-col cols="3" align="left">
        <span>วันที่สั่งซื้อ :</span>
        {{new Date(items.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
      </v-col>
      <!-- <v-col cols="8">
        <span>วิธีในการจัดส่ง</span>
      </v-col> -->
      <v-col cols="9"></v-col>
      <v-col cols="3" align="left">
        <span>สถานะการจ่ายเงิน :</span>
        <v-chip v-if="items.transaction_status === 'Pending'" small class="ma-2" color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
        <v-chip v-else class="ma-2" color="#F0F9EE" small text-color="#1AB759">อนุมัติ</v-chip>
      </v-col>
      <v-col cols="12">
        <v-card outlined>
          <v-col cols="12" class="pl-5 pt-5">
            <h2><b>รายการสั่งซื้อสินค้า</b></h2>
          </v-col>
          <v-container grid-list-xs>
            <a-table bordered v-for="(item,index) in items.data_list" :key="index" :data-source="item.product_list" :rowKey="record => record.sku" :columns="headers">
              <template slot="title">
                <p class="text-left">ผู้ขาย:<b> {{item.shop_name}}</b></p>
              </template>
              <template slot="productdetails" slot-scope="text, record">
                <v-row>
                  <v-col cols="12" md="4" class="pr-0 py-1">
                    <v-img :src="record.product_image.url" class="imageshow"/>
                  </v-col>
                  <v-col cols="12" md="8">
                    <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                  </v-col>
                </v-row>
              </template>
              <template slot="price" slot-scope="text, record">
                <v-col cols="12">
                  <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </v-col>
              </template>
              <template slot="net_price" slot-scope="text, record">
                <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
            </a-table>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" md="12" class="mt-1">
        <v-card outlined>
          <v-container grid-list-xs>
            <v-row>
              <v-col cols="12" md="10">
                <v-row dense>
                  <v-col cols="12" class="text-right">
                    <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ส่วนลด :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ราคารวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ค่าจัดส่ง :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="subheader">ราคารวมทั้งหมด :</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="2">
                <v-row dense>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(items.total_price_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" class="mt-4">
        <h2 style="font-weight: 700;">สถานะผู้อนุมัติ</h2>
      </v-col>
      <v-col cols="12" class="mt-4">
        <h4 style="font-weight: 700;">ลำดับการอนุมัติ</h4>
      </v-col>
      <v-col cols="12" align="left">
        <v-timeline dense>
          <v-timeline-item v-for="(item,index) in items.approver_list" :key="index" fill-dot class="white--text mb-12" color="#27AB9C" small>
            <template v-slot:icon>
              <span>{{ index+1 }}</span>
            </template>
            <v-row no-gutters>
              <v-col cols="12">
                <span style="color: #27AB9C;"><b>ผู้อนุมัติ</b></span>
              </v-col>
              <v-col cols="12">
                <span style="color: black;">สถานะ :</span>
                <v-chip v-if="item.status === 'pending'" class="ma-2" color="#FCF0DA" small text-color="#E9A016">รออนุมัติ</v-chip>
                <v-chip v-else class="ma-2" color="#F0F9EE" small text-color="#1AB759">อนุมัติ</v-chip>
              </v-col>
              <v-col cols="12">
                <span style="color: black;">ผู้อนุมัติ : {{ item.approver_name }}({{ item.email }})</span>
              </v-col>
              <v-col v-if="item.time_approve === '-'" cols="12" class="mt-3">
                <span style="color: black;">วันที่อนุมัติ : {{ item.time_approve }}</span>
              </v-col>
              <v-col v-else cols="12" class="mt-3">
                <span style="color: black;">วันที่อนุมัติ : {{new Date(item.time_approve).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
              </v-col>
            </v-row>
          </v-timeline-item>
        </v-timeline>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data () {
    return {
      overlay: false,
      items: [],
      paymentNumber: {}
    }
  },
  async created () {
    this.paymentNumber = JSON.parse(Decode.decode(localStorage.getItem('orderNumber')))
    this.getItemProduct()
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          align: 'center',
          width: '20%'
        }
      ]
      return headers
    }
  },
  methods: {
    async getItemProduct () {
      this.overlay = true
      await this.$store.dispatch('actionOrderDetail', this.paymentNumber)
      var res = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('res in DetailOrder', res)
      if (res.message === 'Get detail order success') {
        this.items = res.data
        this.overlay = false
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    }
  },
  watch: {
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    }
  }
}
</script>

<style lang="css" scoped>
.imageshow {
  width: 80px;
  height: 80px;
  cursor: pointer;
}
</style>
