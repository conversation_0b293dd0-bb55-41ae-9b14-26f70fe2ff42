import AxiosUser from './axios_manage_point_api'

const ModuleManagePoint = {
  state: {
    // User Detail Page
    stateCreatePointSet: [],
    stateEditSellerShopPoint: [],
    stategetListUserPointByUserID: [],
    stategetListUserPointBySellerShopID: [],
    stategetSellerShopPointDetail: [],
    stategetDetailUserPointByUser: []
  },
  mutations: {
    // User Detail Page
    mutationsCreatePointSet (state, data) {
      state.stateCreatePointSet = data
    },
    mutationsEditSellerShopPoint (state, data) {
      state.stateEditSellerShopPoint = data
    },
    mutationsgetListUserPointByUserID (state, data) {
      state.stategetListUserPointByUserID = data
    },
    mutationsgetListUserPointBySellerShopID (state, data) {
      state.stategetListUserPointBySellerShopID = data
    },
    mutationsgetSellerShopPointDetail (state, data) {
      state.stategetSellerShopPointDetail = data
    },
    mutationsgetDetailUserPointByUser (state, data) {
      state.stategetDetailUserPointByUser = data
    }
  },
  actions: {
    // User Detail Page
    async actionsCreatePointSet (context, access) {
      const responseData = await AxiosUser.CreatePointSet(access)
      // console.log('response', responseData)
      await context.commit('mutationsCreatePointSet', responseData)
    },
    async actionsEditSellerShopPoint (context, access) {
      const responseData = await AxiosUser.EditSellerShopPoint(access)
      // console.log('response', responseData)
      await context.commit('mutationsEditSellerShopPoint', responseData)
    },
    async actionsgetListUserPointByUserID (context, access) {
      const responseData = await AxiosUser.getListUserPointByUserID(access)
      // console.log('response', responseData)
      await context.commit('mutationsgetListUserPointByUserID', responseData)
    },
    async actionsgetListUserPointBySellerShopID (context, access) {
      const responseData = await AxiosUser.getListUserPointBySellerShopID(access)
      // console.log('response', responseData)
      await context.commit('mutationsgetListUserPointBySellerShopID', responseData)
    },
    async actionsgetSellerShopPointDetail (context, access) {
      const responseData = await AxiosUser.getSellerShopPointDetail(access)
      // console.log('response', responseData)
      await context.commit('mutationsgetSellerShopPointDetail', responseData)
    },
    async actionsgetDetailUserPointByUser (context, access) {
      const responseData = await AxiosUser.getDetailUserPointByUser(access)
      // console.log('response', responseData)
      await context.commit('mutationsgetDetailUserPointByUser', responseData)
    }
  }
}
export default ModuleManagePoint
