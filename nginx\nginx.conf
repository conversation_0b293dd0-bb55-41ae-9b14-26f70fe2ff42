# Generated by nginxconfig.io
# https://nginxconfig.io/?php_server=127.0.0.1:9000

user nginx;
pid /run/nginx.pid;
worker_processes auto;
worker_rlimit_nofile 65535;

events {
        multi_accept on;
        worker_connections 65535;
}

http {
        charset utf-8;
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        server_tokens off;
        log_not_found off;
        types_hash_max_size 2048;
        client_max_body_size 256M;

        # MIME
        include mime.types;
        default_type application/octet-stream;

        # logging
        #access_log /var/log/nginx/myaccess.log;
        #error_log /var/log/nginx/myerror.log warn;
        access_log /frontlog/myaccess.log;
        error_log /frontlog/myerror.log warn;

        #error_log on;
        # error_log  /var/log/nginx/nginx_error.log  warn;

        # load configs
        include /etc/nginx/conf.d/*.conf;
        include /etc/nginx/sites-enabled/*;

        # Gzip compression
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;
        
        map $sent_http_content_type $cors {
          # Images
          ~*image/ "*";
      
          # Web fonts
          ~*font/                         "*";
          ~*application/vnd.ms-fontobject "*";
          ~*application/x-font-ttf        "*";
          ~*application/font-woff         "*";
          ~*application/x-font-woff       "*";
          ~*application/font-woff2        "*";
        }
}

