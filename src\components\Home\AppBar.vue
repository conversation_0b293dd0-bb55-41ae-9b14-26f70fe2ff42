<template>
<div>
  <!-- <a-row type="flex" justify="center" align="middle" class="bg-category">
    <marquee behavior="alternate">เปิดร้านกับ one middle space บริการค่าจัดส่งฟรี 3 เดือน</marquee>
    </a-row> -->
      <a-row type="flex" justify="center" class="bg-color">
        <a-col :span='2'>
          <v-img :src="require('@/assets/MarketLogo.png')"  class="mr-5" contain height="60" max-width="250" width="100%" style="cursor: pointer" @click="goHome()"/>
        </a-col>
        <a-col :span='15' class="pt-3">
          <v-text-field v-model="searchtext" placeholder="ค้นหาสินค้าและร้านค้า" dense solo hide-details @keyup.enter="searchdata()">
            <v-icon slot="append" @click="searchdata()">mdi-magnify </v-icon>
          </v-text-field>
        </a-col>
        <a-col :span='5'>
          <a-row type="flex" justify="space-around">
            <Login v-if="statusLogin === false"/>
            <LoginSuccess v-else/>
            <!-- <v-btn class="mt-3" text dark @click="gotoCreateEpro()">สร้างบริษัท</v-btn> -->
            <CartPopover v-if="path"/>
          </a-row>
        </a-col>
      </a-row>
    <!-- <a-row type="flex" justify="center" align="middle" class="bg-category">
      <div v-for="(item, index) in ListCategory" :key="index" class="mx-2">
        <Category :props="item" :IndexSelect='index'/>
      </div>
    </a-row> -->
</div>
</template>

<script>
import { Decode } from '@/services'
import { Col, Row } from 'ant-design-vue'
export default {
  components: {
    'a-row': Row,
    'a-col': Col,
    CartPopover: () => import('@/components/PopOver/PopoverCartAppBar'),
    Login: () => import('@/components/PopOver/Login'),
    LoginSuccess: () => import('@/components/PopOver/LoginSuccess')
    // Category: () => import('@/components/PopOver/Category')
  },
  data: () => ({
    searchtext: '',
    countCart: 1,
    statusLogin: false,
    onedata: [],
    ListCategory: [],
    path: false
  }),
  created () {
    this.GetCategory()
    this.getPath()
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.onedata = []
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(this.onedata)
      if (this.onedata.user !== undefined) {
        // this.username = onedata.user[0].first_name_th + ' ' + onedata.user[0].last_name_th
        this.statusLogin = true
      } else {
        this.statusLogin = false
      }
    } else {
      this.statusLogin = false
    }
  },
  mounted () {
    this.$EventBus.$on('searchdata', this.searchdata)
    this.$EventBus.$on('getPath', this.getPath)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('searchdata')
      this.$EventBus.$off('getPath')
    })
  },
  methods: {
    async getPath () {
      var currentRoutepath = this.$router.currentRoute.path
      if (currentRoutepath === '/shoppingcart' || currentRoutepath === '/checkout') {
        this.path = false
      } else {
        this.path = true
      }
    },
    async GetCategory () {
      await this.$store.dispatch('GetDefaultCagegory')
      this.ListCategory = await this.$store.state.ModuleManageShop.Category
      // console.log('ListCategory', this.ListCategory)
    },
    goHome () {
      this.searchtext = ''
      this.$router.push({ path: '/' }).catch(() => {})
    },
    gotoCreateEpro () {
      this.$router.push({ path: '/createbusinesssid' }).catch(() => {})
    },
    searchdata () {
      if (this.searchtext === '') {
        this.$swal.fire({
          icon: 'warning',
          title: 'โปรดใส่คำที่จะค้นหา',
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        // localStorage.setItem('searchtext', this.searchtext)
        if (this.$router.currentRoute.name === 'search') {
          if (this.$router.currentRoute.params.data === this.searchtext) {
            // console.log('condition 1')
            this.$EventBus.$emit('getResultSearch')
          } else {
            // console.log('condition 2')
            this.$router.push({ path: `${this.searchtext}` }).catch(() => {})
            this.$EventBus.$emit('getResultSearch')
          }
        } else {
          // console.log('condition 3')
          this.$router.push({ path: `/search/${this.searchtext}` }).catch(() => {})
          this.$EventBus.$emit('getResultSearch')
        }
      }
    }
  }
}
</script>

<style scoped>
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}
.bg-color {
  background-color: rgb(111,183,87) !important
}
.bg-category {
  background-color: aqua;
  height: 40px;
}

</style>
