<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"
        v-if="!MobileSize">ใบสั่งยา</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> ใบสั่งยา
      </v-card-title>
      <v-row dense>
        <v-container class="text-center"
          style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
          <v-col cols="12">
            <h1 class="ma-0">รหัสยา : <b>MSP007</b></h1>
          </v-col>
          <v-col class="text-center pt-0">
            <span
              style="color:#FAAD14; cursor: pointer; text-decoration: underline; font-size: 18px;">
              ใบเปรียบเทียบราคา <v-icon small
                style="color:#FAAD14;">mdi-arrow-right</v-icon>
            </span>
          </v-col>
          <v-row style="flex-wrap: nowrap; display: inline-flex;">
            <v-col v-for="(item, index) in ProductData.data" :key="index">
              <v-card class="pa-4" width="320px" style="border-radius: 10px;">
                <v-col class="text-center">
                  <span style="color:#2F8BDC; font-size: 18px;"><b>{{ item.shop_name }}</b></span>
                </v-col>
                <v-col class="text-left" style="height: 70px;">
                  <span style="color:#2F8BDC; font-size: 14px;">{{ item.product_list[0].name }}</span>
                </v-col>
                <v-col class="text-center">
                  <span style="font-size: 24px;">
                    <b>฿{{ item.net_price }}</b>
                  </span>
                  <!-- <span>
                    /<template v-if="item.payment_type === 'Monthly'">เดือน</template>
                  <template v-else-if="item.payment_type === 'Yearly'">ปี</template>
                  <template v-else-if="item.payment_type === 'Percent'">เปอร์เซ็นต์</template>
                  <template v-else>{{ item.payment_type }}</template>
                  </span> -->
                </v-col>
                <!-- <v-col class="text-left">
                  <span><b>รายละเอียด Package :</b></span><br>
                </v-col> -->
                <v-col class="text-center">
                <template>
                  <div ref="termsContent" @scroll="handleScroll" class="scrollable-content-first fixed-height">
                    <div>
                      <div>
                        <!-- <v-icon class="mr-2" style="color: #52c41a;">mdi-check-circle</v-icon> -->
                        <span>ราคาต่อชิ้น: ฿{{ Number(item.product_list[0].show_price).toLocaleString() }}</span>
                      </div>
                    </div>
                  </div>
                </template>
                </v-col>
                <v-col class="text-center">
                  <span
                    style="color:#FAAD14; cursor: pointer; text-decoration: underline;">
                    ใบเสนอราคา <v-icon small
                      style="color:#FAAD14;">mdi-arrow-right</v-icon>
                  </span>
                </v-col>
                <v-checkbox hide-details label="เลือกสินค้าร้านนี้"></v-checkbox>
                <!-- <v-col class="text-center">
                  <v-btn depressed class="w-100">
                    <b></b>
                  </v-btn>
                </v-col> -->
                <!-- <v-col>
                <a v-if="item.available_status === 'pending'" @click="GotoERPPartner()" style="text-decoration: none; color: inherit;">
                  <span><b>หากต้องการเชื่อมต่อ Partner <br>(โปรดคลิ๊กที่นี่)</b></span>
                </a>
                <a v-else-if="item.available_status === 'inactive' && item.package_status === 'approve'" @click="GotoERPPartner()" style="text-decoration: none; color: inherit;">
                  <span><b>หากต้องการเปิดการใช้งาน Partner <br>(โปรดคลิ๊กที่นี่)</b></span>
                </a>
                <a v-else-if="item.available_status === 'cancel'" @click="GotoPaymentPartner()" style="text-decoration: none; color: inherit;">
                  <span><b>โปรดทำการชำระเงินยอดค้างชำระ <br>(คลิ๊กที่นี่)</b></span>
                </a>
              </v-col> -->
              </v-card>
            </v-col>
          </v-row>
        </v-container>
        <v-container class="text-center"
          style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
          <v-col cols="12">
            <h1 class="ma-0">รหัสยา : <b>MSP007</b></h1>
          </v-col>
          <v-col class="text-center pt-0">
            <span
              style="color:#FAAD14; cursor: pointer; text-decoration: underline; font-size: 18px;">
              ใบเปรียบเทียบราคา <v-icon small
                style="color:#FAAD14;">mdi-arrow-right</v-icon>
            </span>
          </v-col>
  <v-data-table
    :headers="headers"
    :items="desserts"
    class="elevation-1"
    hide-default-footer
  >
  </v-data-table>
        </v-container>
      </v-row>
    </v-card>
  </v-container>
</template>
<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      ProductData: [],
      headers: [
        {
          text: '',
          align: 'start',
          sortable: false,
          value: 'name'
        },
        { text: 'BOCBOX2', value: 'BOCBOX2', width: 100 },
        { text: 'วันอีเมล', value: 'ONEEMAIL', width: 100 },
        { text: 'NGS', value: 'NGS', width: 100 }
      ],
      desserts: [
        {
          name: 'ชื่อสินค้า',
          BOCBOX2: 'น้ำซุปแจ่วฮ้อนเข้มข้น 15 เท่า น้ำซุปอีสาน...',
          ONEEMAIL: 'น้ำซุปแจ่วฮ้อนเข้มข้น 15 เท่า',
          NGS: 'น้ำซุปแจ่วฮ้อนเข้มข้น 15 เท่า น้ำซุปอีสาน...'
        },
        {
          name: 'รหัสยา',
          BOCBOX2: 'MSP007',
          ONEEMAIL: 'MSP007',
          NGS: 'MSP007'
        },
        {
          name: 'รายละเอียด',
          BOCBOX2: '-',
          ONEEMAIL: '-',
          NGS: '-'
        },
        {
          name: 'ราคาต่อชิ้น',
          BOCBOX2: '฿1,500',
          ONEEMAIL: '฿500',
          NGS: '฿45.79'
        },
        {
          name: 'ราคารวม',
          BOCBOX2: '฿4,500',
          ONEEMAIL: '฿1,500',
          NGS: '฿137.37'
        },
        {
          name: 'ใบเสนอราคา',
          BOCBOX2: 'ใบเสนอราคา',
          ONEEMAIL: 'ใบเสนอราคา',
          NGS: 'ใบเสนอราคา'
        }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/prescriptionMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/prescription' }).catch(() => { })
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    window.scrollTo(0, 0)
    if (
      localStorage.getItem('oneData') !== null &&
      localStorage.getItem('CompanyData') !== null
    ) {
      this.companyData = JSON.parse(
        Decode.decode(localStorage.getItem('CompanyData'))
      )
      await this.getData()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => { })
      }
    },
    async getData () {
      this.$store.commit('openLoader')
      var data = {
        company_id: 31,
        list_sku: ['MSP007'],
        quantity: 3
      }
      await this.$store.dispatch('actionsPrescription', data)
      var response = await this.$store.state.ModuleAdminManage.statePrescription
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.ProductData = response.data.list_product_data[0]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500,
          icon: 'error',
          text: `${response.message}`
        })
      }
    }
  }
}
</script>
<style scoped></style>
