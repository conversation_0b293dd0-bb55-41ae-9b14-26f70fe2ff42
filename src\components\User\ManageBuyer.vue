<template>
  <div>
    <v-row justify="center">
      <v-card
      class="my-4"
      outlined
      width="80%"
      >
        <v-card-title>
          ตั้งค่าผู้ซื้อ
          <v-spacer></v-spacer>
          <v-btn icon tile dark color="success" @click="addBuyer()"><v-icon dark>mdi-plus</v-icon></v-btn>
        </v-card-title>
      </v-card>
      <v-card
      class="my-2"
      outlined
      width="80%"
      v-for="(item, index) in dataDepartment"
      :key="index"
      >
        <v-card-title>
          {{ item.departmentname[0] }}
          <v-spacer></v-spacer>
          <v-btn color="info" @click="EditDepartment(item)"><v-icon>mdi-cog</v-icon> แก้ไข</v-btn>
          <v-btn icon color="dark" @click="item.openDialog = !item.openDialog">
            <v-icon large>{{ item.openDialog ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
        </v-card-title>
        <v-expand-transition>
          <div v-show="item.openDialog">
            <v-divider></v-divider>
            <v-card-text>
              <p>ประเภทผู้อนุมัติ : {{ item.approverType[0].name }}</p>
            </v-card-text>
            <v-container>
              <v-data-table
              :headers="headers"
              :items="item.approverName"
              hide-default-footer
              class="elevation-1"
              >
              </v-data-table>
            </v-container>
          </div>
        </v-expand-transition>
      </v-card>
    </v-row>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      dataDepartment: [
        {
          id: 1,
          departmentname: ['แผนกอนุมัติ'],
          approverType: [{ name: 'ผู้อนุมัติแบบหลายคน', value: 3 }],
          approverName: [
            { name: 'นายกีฬา สั่งซื้อ', value: '10,000' },
            { name: 'เก็ท เก็ท', value: '10,000' }
          ],
          approver: [
            { name: 'นายกีฬา สั่งซื้อ', value: 'นายกีฬา สั่งซื้อ' },
            { name: 'เก็ท เก็ท', value: 'เก็ท เก็ท' }
          ],
          openDialog: false
        },
        {
          id: 2,
          departmentname: ['แผนกกีฬา01'],
          approverType: [{ name: 'ผู้อนุมัติแบบหนึ่งคน', value: 2 }],
          approverName: [
            { name: 'เก็ท เก็ท', value: '10,000' }
          ],
          approver: [
            { name: 'เก็ท เก็ท', value: 'เก็ท เก็ท' }
          ],
          openDialog: false
        }
      ],
      headers: [
        { text: 'ผู้อนุมัติ', align: 'center', value: 'name', divider: true, sortable: false },
        { text: 'วงเงินงบประมาณ', align: 'center', value: 'value', divider: true, sortable: false }
      ]
    }
  },
  methods: {
    addBuyer () {
      this.$router.push('/sittingBuyer').catch(() => {})
    },
    EditDepartment (val) {
      // console.log('val', val)
      localStorage.setItem('DataBuyer', Encode.encode(val))
      this.$router.push('/editBuyer').catch(() => {})
    }
  }
}
</script>
