<template>
  <div>
    <v-container :class="MobileSize ? 'mt-2' : ''">
      <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
        <h2 v-if="!MobileSize" class="ml-4" style="font-size:24px"><B>เรียกพนักงานรับพัสดุ</B></h2>
        <v-row cols="12" md="12" class="pa-3" v-if="MobileSize">
          <v-icon v-if="MobileSize || IpadSize " @click="Cancle()" color="#27AB9C" class="mb-0 ml-2">mdi-chevron-left
          </v-icon>
          <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="ml-2">เรียกพนักงานรับพัสดุ</span>
        </v-row>
        <v-container v-if="data.length > 0">
          <v-text-field :style="IpadSize ? '' : 'width:400px'" v-model="search" append-icon="mdi-magnify" placeholder="ค้นหาจากรหัสการสั่งซื้อ"
            outlined dense rounded hide-details></v-text-field><br />
          <h4 v-if="!MobileSize" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" class="mb-6 mt-1">
            รายการสั่งซื้อที่ยังไม่ได้เรียกพนักงาน {{ showCountOrder }} รายการ
          </h4>
          <v-data-table v-if="MobileSize" v-model="selected" @toggle-select-all="selectAllToggle" :headers="MobileSize ? headersMobile : headers" :items="data"
            :search="search" item-key="reference_id" @pagination="countOrdar"  color="blue" show-select :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-1" no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา" no-data-text="ไม่มีรายการในตาราง">
            <template v-slot:[`item.data-table-select`]="{ item, isSelected, select}">
              <v-simple-checkbox :value="isSelected" :readonly="item.status === 1? false:true"
                :disabled="item.status === 1? false:true" @input="select($event)"></v-simple-checkbox>
            </template>
            <template v-slot:[`item.reference_id`]="{ item }">
              <span><a :href="item.url_barcode_picture" target="_blank">{{item.reference_id}}</a></span>
            </template>
            <template v-slot:[`item.order_no`]="{ item }">
              <span><a :href="item.url_tracking" target="_blank">{{item.order_no}}</a></span>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 1">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รอจัดสรร</v-chip>
              </span>
              <span v-else-if="item.status === 2">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รอรับพัสดุ</v-chip>
              </span>
              <span v-else-if="item.status === 5">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เรียกพนักงานรับพัสดุแล้ว</v-chip>
              </span>
              <span v-else-if="item.status === 0">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกแล้ว</v-chip>
              </span>
            </template>
            <template v-slot:[`item.updated_at`]="{ item }">
              {{ new Date(item.updated_at).toLocaleDateString('th-TH', {timeZone: "UTC", year: 'numeric', month:
              'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.callcuriers`]="{ item }">
              <v-btn
                :disabled="selected.length !== 0 && item.status === 1 ? true:false || item.status === 1? false:true "
                dense class="ma-2" outlined color="#27AB9C" @click="OpenDialogCallCurier(item)"><B>เรียกพนักงาน</B>
              </v-btn>
            </template>
          </v-data-table>
          <v-data-table v-else v-model="selected" @toggle-select-all="selectAllToggle" :headers="headers" :items="data"
            :search="search" item-key="reference_id" @pagination="countOrdar"  color="blue" show-select :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-1" no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา" no-data-text="ไม่มีรายการในตาราง">
            <template v-slot:[`item.data-table-select`]="{ item, isSelected, select}">
              <v-simple-checkbox :value="isSelected" :readonly="item.status === 1? false:true"
                :disabled="item.status === 1? false:true" @input="select($event)"></v-simple-checkbox>
            </template>
            <template v-slot:[`item.reference_id`]="{ item }">
              <span><a :href="item.url_barcode_picture" target="_blank">{{item.reference_id}}</a></span>
            </template>
            <template v-slot:[`item.order_no`]="{ item }">
              <span><a :href="item.url_tracking" target="_blank">{{item.order_no}}</a></span>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 1">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รอจัดสรร</v-chip>
              </span>
              <span v-else-if="item.status === 2">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รอรับพัสดุ</v-chip>
              </span>
              <span v-else-if="item.status === 5">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เรียกพนักงานรับพัสดุแล้ว</v-chip>
              </span>
              <span v-else-if="item.status === 0">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกแล้ว</v-chip>
              </span>
            </template>
            <template v-slot:[`item.updated_at`]="{ item }">
              {{ new Date(item.updated_at).toLocaleDateString('th-TH', {timeZone: "UTC", year: 'numeric', month:
              'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.callcuriers`]="{ item }">
              <v-btn :disabled="selected.length !== 0 && item.status === 1 ? true:false || item.status === 1? false:true " dense
                class="ma-2" outlined color="#27AB9C" @click="OpenDialogCallCurier(item)"><B>เรียกพนักงาน</B>
              </v-btn>
            </template>
          </v-data-table>
          <div class=""><span style="color:red">หมายเหตุ</span> : หากต้องการที่จะพิมพ์บาร์โค้ด(Barcode) สามารถกดที่
            "รหัสการสั่งซื้อ"</div>
          <v-container v-if="!MobileSize">
            <v-row class="d-flex justify-end">
              <v-col cols="3" md="1" :style="IpadProSize ? 'margin-right: 2%' : ''">
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true" outlined
                  @click="OpenDialogDelete()"><B>ยกเลิก</B></v-btn>
              </v-col>
              <v-col cols="7" :md="IpadProSize ? 4 : IpadSize ? 5 : 3">
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true"
                  class="ml-1 pl-8 pr-8 white--text" @click="OpenDialogCallCurierAll()">
                  <B>เรียกพนักงานทั้งหมด({{this.selected.length}})</B>
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
          <v-container v-if="MobileSize">
            <v-row class="d-flex justify-end">
              <v-col cols="3">
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true" outlined
                  @click="OpenDialogDelete()"><B>ยกเลิก</B></v-btn>
              </v-col>
              <v-col cols="9">
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true"
                  class="ml-1 pl-8 pr-8 white--text" @click="OpenDialogCallCurierAll()">
                  <B>เรียกพนักงานทั้งหมด({{this.selected.length}})</B>
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-container>
        <v-container v-else>
          <v-row justify="center" align-content="center" >
            <v-col cols="12" md="12" align="center" style="min-height: 636px;">
              <div style="padding-top: 90px;">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                  height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการเรียกพนักงานรับพัสดุ</span><br />
              </h2>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-container>

    <v-dialog v-model="dialog" persistent max-width="600" style="overflow: hidden;">
      <v-card width="100%" style="overflow: hidden;">
        <v-card-title>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณากดยืนยันเพื่อทำการเรียกพนักงานรับพัสดุ</span>
        </v-card-title><br />
        <v-card-text>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณาตรวจสอบก่อนที่จะเรียกพนักงานรับพัสดุ</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Colse()">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessCurier()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Delete" width="600" persistent>
      <v-card>
        <v-card-title>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณากดยืนยันเพื่อทำการยกเลิกการเลือก</span>
        </v-card-title><br />
        <v-card-text>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณาตรวจสอบก่อนที่จะยกเลิก</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Colse()">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="CancleSelect()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogAllselect" width="600" persistent>
      <v-card>
        <v-card-title>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณากดยืนยันเพื่อทำการเรียกพนักงานรับพัสดุทั้งหมด</span>
        </v-card-title><br />
        <v-card-text>
          <span style="white-space: normal; display: inline-block; word-break:break-word;">กรุณาตรวจสอบก่อนที่จะทำการเรียกพนักงานรับพัสดุทั้งหมด ที่ได้ทำการเลือกไว้</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Colse()">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessCurierAll()">ตกลง
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data: () => ({
    a: false,
    quantity: 0,
    res: [],
    respons: [],
    oneData: [],
    selected: [],
    data: [],
    Message: '',
    DataCurier: '',
    search: '',
    dialog: false,
    dialog_Delete: false,
    dialogAllselect: false,
    disabledCount: 0,
    showCountOrder: 0,
    token: '',
    desserts: [
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300300',
        staffInfoPhone: '092278555',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '0',
        updateAt: 'กำลังไปรับพัสดุ'
      },
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300181',
        staffInfoPhone: '092278555',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '1',
        updateAt: 'กำลังจัดส่ง'
      },
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300202',
        staffInfoPhone: '092278555',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '0',
        updateAt: 'กำลังไปรับพัสดุ'
      }
    ],
    headers: [
      { text: 'รหัสการสั่งซื้อ', value: 'reference_id', width: '140', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'ชื่อ - นามสกุล ผู้ซื้อ', value: 'buyer_name', width: '170', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'รหัสการติดตาม', value: 'order_no', width: '140', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'สถานะ', value: 'status', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'อัปเดตล่าสุด', value: 'updated_at', sortable: false, width: '150', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: '', value: 'callcuriers', sortable: false, width: '180', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
    ],
    headersMobile: [
      { text: 'รหัสการสั่งซื้อ', value: 'reference_id', width: '140', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'ชื่อ-นามสกุล', value: 'buyer_name', width: '200', align: 'start', sortable: false, filterable: false, class: 'backgroundTable fontTable--text white-space: normal;' },
      { text: 'รหัสการติดตาม', value: 'order_no', width: '140', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'สถานะ', value: 'status', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'อัปเดตล่าสุด', value: 'updated_at', sortable: false, width: '150', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: '', value: 'callcuriers', sortable: false, width: '180', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
    ],
    Rules: {
      curiernumber: [
        v => (/^[1-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้นและไม่ติดลบ'
      ]
    }
  }),
  created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      if (dataDetail.can_use_function_in_shop.manage_order === '1') {
        this.getListCurier()
        const self = this
        this.data.map(item => {
          if (item.status !== 1) self.disabledCount += 1
        })
      } else {
        this.$router.push({ path: '/' })
      }
    } else {
      this.$router.push({ path: '/' })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/CurierMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Curier' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    Cancle () {
      this.$router.push({ path: '/sellerMobile?ShopID=' + this.oneData.user.list_shop_detail[0].seller_shop_id + '&ShopName=' + this.oneData.user.list_shop_detail[0].shop_name_th })
    },
    CancleSelect () {
      this.selected = []
      this.dialog_Delete = false
    },
    OpenDialogDelete () {
      this.dialog_Delete = true
    },
    OpenDialogCallCurier (item) {
      this.dialog = true
      this.DataCurier = item
    },
    OpenDialogCallCurierAll () {
      this.dialogAllselect = true
    },
    async SuccessCurier () {
      this.dialog = false
      this.$store.commit('openLoader')
      const val = {
        reference_id: this.DataCurier.reference_id
      }
      await this.$store.dispatch('actionCallCurier', val)
      this.res = await this.$store.state.ModuleCurier.stateCurier
      if (this.res.code === 1010) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, showCloseButton: true, timerProgressBar: true, icon: 'info', html: '<h3>กรุณารอสักครู่ กำลังดำเนินการไปรับพัสดุ</h3><br/><h5>TicketPickupld ID : ' + this.res.data[0].ticketPickupId + '<br/>ชื่อพนักงาน : ' + this.res.data[0].staffInfoName + '</h5><h5>เบอร์โทรศัพท์: ' + this.res.data[0].staffInfoPhone + '   เวลา: ' + this.res.data[0].timeoutAtText + '</h5><h5>' + this.Message + '</h5>' })
      } else if (this.res.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>เรียกพนักงานไปรับพัสดุสำเร็จ</h3>' })
        this.$EventBus.$emit('getItemNoti')
        this.getListCurier()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
    },
    async SuccessCurierAll () {
      this.dialogAllselect = false
      this.$store.commit('openLoader')
      var a = []
      for (let i = 0; i < this.selected.length; i++) {
        a.push(this.selected[i].reference_id)
      }
      const val = {
        reference_id: a
      }
      await this.$store.dispatch('actionMutiCurier', val)
      this.res = await this.$store.state.ModuleCurier.stateMutiCourier
      if (this.res.code === 1010) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, showCloseButton: true, timerProgressBar: true, icon: 'info', html: '<h3>กรุณารอสักครู่ กำลังดำเนินการไปรับพัสดุ</h3><br/><h5>TicketPickupld ID : ' + this.res.data[0].ticketPickupId + '<br/>ชื่อพนักงาน : ' + this.res.data[0].staffInfoName + '</h5><h5>เบอร์โทรศัพท์: ' + this.res.data[0].staffInfoPhone + '   เวลา: ' + this.res.data[0].timeoutAtText + '</h5><h5>' + this.Message + '</h5>' })
      } else if (this.res.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>เรียกพนักงานไปรับพัสดุสำเร็จ</h3>' })
        this.$EventBus.$emit('get ItemNoti')
        this.getListCurier()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
      this.selected = []
    },
    Colse () {
      this.dialog_Delete = false
      this.dialog = false
      this.dialogAllselect = false
      this.DataCurier = ''
    },
    selectAllToggle (props) {
      if (this.search === '') {
        if (this.selected.length !== this.data.length - this.disabledCount) {
          if (this.a) {
            this.selected = []
            this.a = false
          } else {
            this.selected = []
            this.a = true
            const self = this
            this.data.forEach(item => {
              if (item.status === 1) {
                self.selected.push(item)
              }
            })
          }
        } else {
          this.selected = []
        }
      }
    },
    async getListCurier () {
      this.data = []
      var Data = {
        token: this.oneData.user.access_token,
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionGetCurier', Data)
      this.res = await this.$store.state.ModuleCurier.stateGetCurier
      if (this.res.message === 'Can not verify shop_id') {
        this.$router.push({ path: '/' })
      } else {
        this.data = this.res.data
        this.quantity = this.data.length - this.disabledCount
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-table-header-mobile__wrapper {
    display: flex;
    justify-content: end;
  }
  .v-data-table /deep/ .v-simple-checkbox {
    padding-left: 25px;
  }
  .v-data-table /deep/ .v-data-footer {
    font-size: 0.62rem;
  }
</style>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
