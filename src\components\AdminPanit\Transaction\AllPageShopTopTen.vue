<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px;" @click="backtoPage()" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> ร้านค้าขายดี Top 10</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 16px; line-height: 22px;" @click="backtoPage()" v-else><v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> ร้านค้าขายดี Top 10</v-card-title>
      <v-card-text class="my-4">
        <v-row dense>
          <v-col cols="12" md="6" v-for="(item, index) in dataShop" :key="index">
            <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0">
              <v-card-text>
                <v-row dense>
                  <v-col cols="2" md="1" sm="1" v-if="index < 5 && !MobileSize && !IpadSize" :class="!MobileSize ? 'mr-3' : ''">
                    <v-avatar size="45">
                      <v-img contain v-if="item.ranking === 1" src="@/assets/icons/level_1.png"></v-img>
                      <v-img contain v-else-if="item.ranking === 2" src="@/assets/icons/level_2.png"></v-img>
                      <v-img contain v-else-if="item.ranking === 3" src="@/assets/icons/level_3.png"></v-img>
                      <v-img contain v-else-if="item.ranking === 4" src="@/assets/icons/level_4.png"></v-img>
                      <v-img contain v-else-if="item.ranking === 5" src="@/assets/icons/level_5.png"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="3" md="2" sm="2">
                    <v-avatar tile :size="IpadSize ? 40 : 63">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" :max-height="IpadSize ? '40px' : '120px'" :max-width="IpadSize ? '40px' : '120px'"></v-img>
                      <!-- <v-img :src="item.image_shop" max-height="120px" max-width="120px" style="border-radius: 8px;"></v-img> -->
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" :md="index < 5 ? 6 : 7" :sm="index < 5 ? 6 : 7" class="pt-2">
                    <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">{{ item.shop_name }}</span><br/>
                    <!-- <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;">{{ item.shop_name }}</span> -->
                  </v-col>
                  <!-- <v-col cols="12" md="1" :class="index < 5 ? 'mt-2 mr-2' : 'mt-2 mr-4'">
                    <div :style="{'width': index < 5 ? '105px' : '115px'}" style="height: 40px; background: #F8FAFC; border-radius: 8px;">
                      <v-row justify="center" class="pt-2">
                        <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #27AB9C;">Partner <v-icon color="#27AB9C">mdi-bookmark</v-icon></span>
                      </v-row>
                    </div>
                  </v-col> -->
                </v-row>
                <v-row dense class="mt-6">
                  <v-col cols="12">
                    <v-row dense>
                      <v-icon class="ml-1 pr-2" color="#A1A1A1">mdi-file-document-outline</v-icon>
                      <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">รายการชำระเงินสำเร็จ : <b>{{ item.transaction }}</b> รายการ</span>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row dense class="mt-6">
                  <v-col cols="12">
                    <v-row dense>
                      <v-icon class="ml-1 pr-2" color="#A1A1A1">mdi-currency-usd</v-icon>
                      <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">จำนวนเงิน : <b style="font-size: 24px; line-height: 32px; color: #52C41A;">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}</b> บาท</span>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dataShop: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    window.scrollTo(0, 0)
    this.$store.commit('openLoader')
    this.getDataShop()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    backtoPage () {
      localStorage.removeItem('AllShopTopTen')
      if (this.MobileSize) {
        this.$router.push({ path: '/TransactionMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Transaction' }).catch(() => {})
      }
    },
    getDataShop () {
      this.dataShop = []
      this.dataShop = this.companyData = JSON.parse(Decode.decode(localStorage.getItem('AllShopTopTen')))
      this.$store.commit('closeLoader')
    }
  }
}
</script>
