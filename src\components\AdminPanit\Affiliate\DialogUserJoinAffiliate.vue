<template>
  <!-- <div class="text-center"> -->
  <div>
    <v-dialog v-model="ModalDetailUser" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายละเอียดเข้าร่วมโปรแกรม Affiliate</b></span>
              </v-col>
              <v-btn fab small @click="ModalDetailUser = !ModalDetailUser" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" class="mr-0 d-flex" v-if="!MobileSize">
                    <v-row dense class="mr-auto" style="justify-content: center;">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                    </v-row>
                  </v-col>
                  <v-col cols="12" v-else>
                    <v-row dense class="mr-auto">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="py-5">
                    <v-divider></v-divider>
                  </v-col>
                  <v-col cols="12" class="pb-4">
                    <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดการเข้าร่วมโปรแกรม</span>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col align="center" cols="12" md="6" sm="12">
                        <v-img v-if="sendData.img_path === '' || sendData.img_path === null || sendData.img_path === undefined" src="@/assets/Businessman.png" width="150px" height="150px"></v-img>
                        <v-img v-else :src="sendData.img_path" width="150px" height="150px"></v-img>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัส Affiliate : <b>{{sendData.an_id}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อ-นามสกุล : <b>{{sendData.first_name_th}} {{sendData.last_name_th}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{sendData.phone}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อีเมล : <b>{{sendData.email}}</b></p>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="12" md="6" sm="12">
                        <div class="pb-4 pt-6">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดการชำระเงิน</span>
                        </div>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ประเภทบัญชี : <b>{{sendData.account_type === 'savings' ? 'ออมทรัพย์' : 'ฝากประจำ'}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อบัญชี : <b>{{sendData.bank_username }}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อธนาคาร : <b>{{sendData.bank_name}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อสาขาธนาคาร : <b>{{sendData.bank_branch}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขบัญชีธนาคาร : <b>{{sendData.bank_no}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รูปหน้าบัญชีธนาคาร :
                          <v-card class="d-flex justify-center align-center mb-6" style="max-width: 200px; max-height: 200px; overflow: hidden;">
                          <v-img v-if="sendData.bookbank_image_url === '' || sendData.bookbank_image_url === null || sendData.bookbank_image_url === undefined" src="@/assets/NoImage.png" width="150px" height="150px"></v-img>
                          <v-img v-else :src="sendData.bookbank_image_url" width="100%" height="100%" object-fit="contain" @click="viewImageBookbank"></v-img>
                          </v-card>
                        </p>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <div class="pb-4 pt-6">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดข้อมูลภาษี</span>
                        </div>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขประจำตัวผู้เสียภาษี : <b>{{sendData.national_id}}</b></p>
                        <!-- <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รูปบัตรประจำตัวประชาชน :
                          <v-card class="d-flex justify-center align-center mb-6" style="max-width: 200px; max-height: 200px; overflow: hidden;">
                          <v-img v-if="sendData.id_card_image_url === '' || sendData.id_card_image_url === null || sendData.id_card_image_url === undefined" src="@/assets/NoImage.png" width="150px" height="150px"></v-img>
                          <v-img v-else :src="sendData.id_card_image_url" width="100%" height="100%" object-fit="contain" @click="viewImageTax"></v-img>
                          </v-card>
                        </p> -->
                        <div class="pb-4 pt-6">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบัญชีโซเชียลมีเดีย</span>
                        </div>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Facebook : <b>{{sendData.facebook_link !== '' ? sendData.facebook_link : '-'}}</b><v-icon class="ml-1" v-if="sendData.facebook_link !== ''" @click="copyLink('facebook')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">TikTok : <b>{{sendData.tiktok_link !== '' ? sendData.tiktok_link : '-'}}</b><v-icon class="ml-1" v-if="sendData.tiktok_link !== ''" @click="copyLink('tiktok')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Youtube : <b>{{sendData.youtube_link !== '' ? sendData.youtube_link : '-'}}</b><v-icon class="ml-1" v-if="sendData.youtube_link !== ''" @click="copyLink('youtube')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Instagram : <b>{{sendData.instagram_link !== '' ? sendData.instagram_link : '-'}}</b><v-icon class="ml-1" v-if="sendData.instagram_link !== ''" @click="copyLink('instagram')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Line : <b>{{sendData.line_link !== '' ? sendData.line_link : '-'}}</b><v-icon class="ml-1" v-if="sendData.line_link !== ''" @click="copyLink('line')" color="#27AB9C">mdi-link</v-icon></p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogBookbank" max-width="500px">
      <v-card>
        <v-card-text class="d-flex justify-center" style="padding: 20px;">
          <img :src="sendData.bookbank_image_url" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions style="padding: 10px;">
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogBookbank = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogTax" max-width="500px">
      <v-card>
        <v-card-text class="d-flex justify-center" style="padding: 20px;">
          <img :src="sendData.id_card_image_url" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions style="padding: 10px;">
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogTax = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dialogBookbank: false,
      dialogTax: false,
      ModalDetailUser: false,
      sendData: [],
      name: ''
    }
  },
  watch: {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    open (data) {
      this.sendData = ''
      this.ModalDetailUser = true
      this.sendData = data
    },
    viewImageBookbank () {
      this.dialogBookbank = true
    },
    viewImageTax () {
      this.dialogTax = true
    },
    copyLink (text) {
      if (text === 'facebook') {
        navigator.clipboard.writeText(this.sendData.facebook_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'tiktok') {
        navigator.clipboard.writeText(this.sendData.tiktok_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'youtube') {
        navigator.clipboard.writeText(this.sendData.youtube_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'instagram') {
        navigator.clipboard.writeText(this.sendData.instagram_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'line') {
        navigator.clipboard.writeText(this.sendData.line_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      }
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
