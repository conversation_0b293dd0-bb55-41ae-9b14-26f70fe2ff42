<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" :style="MobileSize ? 'background-color: white; padding: 26px;' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0" :class="!MobileSize ? 'pt-4 pl-6' : ''">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">วางบิล</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>วางบิล</v-card-title>
    </v-card>
    <v-row dense :class="!MobileSize ? 'pt-10 pl-5' : ''">
      <v-col cols="12" md="6">
        <v-text-field v-model="search" dense hide-details style="border-radius: 8px;" outlined placeholder="ค้นหาข้อมูลจากรหัสการสั่งซื้อ">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row dense :class="!MobileSize ? 'pt-5 pl-6' : 'pt-5'">
      <v-col cols="12" md="5" sm="12">
        <v-row dense no-gutters>
          <v-col cols="5" md="4" class="d-flex align-center">
            <span style="font-size: 16px;">สถานะการใช้งาน :</span>
          </v-col>
          <v-col cols="7" md="8">
            <v-select v-model="selectedStatus" @change="setValueStatus" :items="statusSelected" item-value="value" item-text="text" dense hide-details style="border-radius: 8px;" outlined class="setCustomSelect"></v-select>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="5" sm="12" :class="IpadSize || MobileSize ? 'pt-5' : 'ml-5'">
        <v-row dense no-gutters>
          <v-col cols="5" md="4" class="d-flex align-center">
            <span style="font-size: 16px;">วันที่รอบบริการ :</span>
          </v-col>
          <v-col cols="7" md="8">
            <v-dialog
              ref="modalRangeDate"
              v-model="modalRangeDate"
              :return-value.sync="dateRange"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  readonly
                  v-model="DateToShow"
                  v-bind="attrs"
                  v-on="on"
                  style="border-radius: 8px;"
                  outlined
                  dense
                  hide-details
                  :class="MobileSize || IpadSize ? '' : 'pr-4'"
                  placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                  ><v-icon slot="append" color="#27AB9C"
                    >mdi-calendar-multiselect</v-icon
                  ></v-text-field>
              </template>
              <v-date-picker
                color="#27AB9C"
                v-model="date"
                scrollable
                reactive
                locale="Th-th"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-spacer></v-spacer>
                <v-btn text color="primary" @click="CloseModalRangeDate()">
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="setValueRangeDate(dateRange)"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog>
            <!-- <v-dialog
              ref="modalRangeDate"
              v-model="modalRangeDate"
              :return-value.sync="dateRange"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  readonly
                  v-model="RangedateApprove"
                  v-bind="attrs"
                  v-on="on"
                  style="border-radius: 8px;"
                  outlined
                  dense
                  hide-details
                  :class="MobileSize || IpadSize ? '' : 'pr-4'"
                  placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                  ><v-icon slot="append" color="#27AB9C"
                    >mdi-calendar-multiselect</v-icon
                  ></v-text-field>
              </template>
              <v-date-picker
                color="#27AB9C"
                v-model="dateRange"
                scrollable
                range
                reactive
                locale="Th-th"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-spacer></v-spacer>
                <v-btn text color="primary" @click="CloseModalRangeDate()">
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="setValueRangeDate(dateRange)"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog> -->
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row dense class="pt-5"><v-row dense :class="!MobileSize ? 'pt-4 pl-6' : ''">
      <v-col cols="12" md="6" sm="6" class="d-flex align-end">
        <v-row dense no-gutters>
          <v-col cols="12" :class="IpadSize ? 'pr-10' : ''">
            <span style="font-size: 16px;">รายการชำระเงินทั้งหมด {{countBillingData}} รายการ</span>
          </v-col>
        </v-row>
      </v-col>
      <v-spacer></v-spacer>
      <v-col cols="12" md="6" sm="6" v-if="!MobileSize">
        <v-row dense no-gutters class="d-flex justify-end">
          <v-col cols="4" class="d-flex justify-end">
            <v-btn outlined rounded height="40" color="#27AB9C" class="mr-2" @click="getReportExcel()" :disabled="paymentList.length === 0">
              <v-img height="20px" width="20px" contain src="@/assets/Marketplace_partner/iconexport.png" col></v-img>
              <span>Export File</span>
            </v-btn>
            <v-btn elevation="0" @click="ApproveBillingMuliple()" :disabled="selected.length === 0" rounded color="#27AB9C" height="40" class="white--text">
              <span>อนุมัติวางบิล</span>
            </v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" v-if="MobileSize">
        <v-btn block outlined rounded height="40" color="#27AB9C" class="mr-2" @click="getReportExcel()" :disabled="paymentList.length === 0">
          <img height="20px" width="20px" contain src="@/assets/Marketplace_partner/iconexport.png" class="mr-3"/>
          <span>Export File</span>
        </v-btn>
      </v-col>
      <v-col cols="12" v-if="MobileSize">
        <v-btn elevation="0" block @click="ApproveBillingMuliple()" :disabled="selected.length === 0" rounded color="#27AB9C" height="40" class="white--text">
          <span>อนุมัติวางบิล</span>
        </v-btn>
      </v-col>
    </v-row>
    </v-row>
    <v-row dense class="mt-5">
      <v-col cols="12">
        <v-card v-if="(paymentList.length !== 0 && haveConnecShop) || (paymentList.length === 0 && haveConnecShop)">
          <v-data-table class="elevation-1" :search="search" @toggle-select-all="selectAllToggle" v-model="selected" :headers="headers" :items="paymentList" show-select item-key="bill_order_number">
            <template v-slot:[`header.data-table-select`]="{ on , props }">
              <v-simple-checkbox
                v-model="selectAll"
                :ripple="false"
                v-bind="props"
                v-on="on"
                color="#27AB9C"
                :indeterminate="checkboxIndeterminate"
              ></v-simple-checkbox>
            </template>
            <template v-slot:[`item.data-table-select`]="{isSelected, item, select}">
              <v-simple-checkbox @input="select($event)" :value="item.status !== 'ชำระเงินแล้ว' ? isSelected : null" :ripple="false" v-if="item.status === 'รอวางบิล'" color="#27AB9C"></v-simple-checkbox>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <v-chip style="font-weight: bold;" :text-color="textColorStatus(item.status)" :color="backgroundColorStatus(item.status)">{{ item.status }}</v-chip>
            </template>
            <template v-slot:[`item.bill_order_number`]="{ item }">
              <span style="text-decoration: underline; color: #1B5DD6 !important; cursor: pointer;" v-if="item.bill_order_number !== '-' && item.transaction_code_invoice !== '-'" @click="getInvoice(item)">{{ item.bill_order_number }}</span>
              <span v-else>-</span>
              <a download="filename.pdf" :href="invoicePath" id="downloadLink" style="text-decoration: underline; color: #1B5DD6 !important; display: none;"></a>
            </template>
            <template v-slot:[`item.receipt_number`]="{ item }">
              <span style="text-decoration: underline; color: #1B5DD6 !important; cursor: pointer;" v-if="item.receipt_number !== '-' && item.transaction_code_receipt !== '-'" @click="getReceipt(item)">{{ item.receipt_number }}</span>
              <span v-else>-</span>
              <a :href="receiptPath" download="filename.pdf" id="downloadLink2" v-if="item.receipt_number !== '-' && item.transaction_code_receipt !== '-'" style="text-decoration: underline; color: #1B5DD6 !important; display: none;"></a>
            </template>
            <template v-slot:[`item.invoice`]="{ item }">
              <!-- <v-btn text color="success"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.invoice }}</span></v-btn> -->
              <span v-if="item.invoice === '-'">{{ item.invoice }}</span>
              <a v-else style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.invoice }}</a>
              <!-- <a target="_blank" v-if="item.bill_order_number !== '-'" :href="item.invoice_path" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.bill_order_number }}</a> -->
              <!-- <a target="_blank" :href="item.url_tracking" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</a> -->
              <!-- <span v-else>-</span> -->
            </template>
            <template v-slot:[`item.start_date`]="{ item }">
              <span>{{ item.start_date }} - {{ item.end_date }}</span>
            </template>
            <template v-slot:[`item.amount`]="{ item }">
              <span>{{ AddTofix(item.amount) }}</span>
            </template>
            <template v-slot:[`item.receipt`]="{ item }">
              <!-- <v-btn text color="success"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.invoice }}</span></v-btn> -->
              <a style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.receipt }}</a>
              <!-- <a target="_blank" :href="item.url_tracking" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</a> -->
              <!-- <span v-else>-</span> -->
            </template>
            <template v-slot:[`item.action`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    width="30"
                    height="30"
                    v-bind="attrs"
                    v-on="on"
                    style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                    outlined icon @click="setDetailValue(item)">
                    <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                  </v-btn>
                </template>
                  <span>รายละเอียดการชำระเงิน</span>
              </v-tooltip>
              <v-btn small text rounded color="#27AB9C" @click="setDetailValue(item)">
                <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
              </v-btn>
          </template>
          </v-data-table>
        </v-card>
        <v-card v-if="paymentList.length === 0 && !haveConnecShop" elevation="0">
          <v-col cols="12"  class="d-flex justify-center">
            <v-img class="mr-2" src="@/assets/Coorperation/NoShopConnect.png" max-width="500" max-height="300" contain></v-img>
          </v-col>
        </v-card>
      </v-col>
    </v-row>
    <!-- modal รายละเอียด -->
    <v-dialog v-model="modalDetails" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : IpadProSize ? '100%' : '1100'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-16">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : IpadProSize ? 'width: 100%': 'width: 1100px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายละเอียดการชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="closeDetailDialog" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '1100px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="py-0">
                <v-row dense>
                  <v-col cols="12" class="d-flex">
                    <span style="font-size: 18px; color: black;">สถานะ :
                      <v-chip style="font-weight: bold; font-size: 16px;" :text-color="textColorStatus(detailData.status)" :color="backgroundColorStatus(detailData.status)">{{ detailData.status }}</v-chip>
                    </span>
                    <v-spacer></v-spacer>
                    <v-btn dense rounded color="primary" @click="ApproveBilling" v-if="detailData.status === 'รอวางบิล'">
                      <span>อนุมัติวางบิล</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row dense :class="!MobileSize ? 'mt-10 pl-5 pb-5' : ''">
                  <v-row dense>
                    <v-col cols="12" sm="9">
                      <span style="font-size: 16px; color: #636363;">เลขประจำตัวผู้เสียภาษีอาการ : <span style="font-size: 16px; color: black;">{{detailData.tax_id_number}}</span></span>
                    </v-col>
                    <v-col cols="12" sm="6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">วันที่รอบบริการ : <span style="font-size: 16px; color: black;">{{ detailData.service_date }}</span></span>
                    </v-col>
                    <v-col cols="12" sm="6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">จำนวน transaction : <span style="font-size: 16px; color: black;">{{ detailData.number_of_transactions }}</span></span>
                    </v-col>
                    <v-col cols="12" sm="6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">จำนวนเงิน : <span style="font-size: 16px; color: black;">{{ AddTofix(detailData.amount) }} บาท</span></span>
                    </v-col>
                    <v-col cols="12" sm="6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">วันที่วางบิล : <span style="font-size: 16px; color: black;">{{detailData.bill_date}}</span></span>
                    </v-col>
                  </v-row>
                </v-row>
                <v-row dense class="mt-5 pl-0">
                  <v-col cols="12">
                    <!-- <a-tabs @change="SelectDetailOrder"> -->
                      <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                      <!-- <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countOrderWaiting }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderApprove }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="3"><span slot="tab">ปฏิเสธอนุมัติ <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderReject }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
                    </a-tabs> -->
                    <a-tabs :activeKey="tabselect" @change="SelectDetailOrder" class="custom-tabs">
                        <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                      <a-tab-pane :key="0"><span :style="tabselect !== 0 ? 'font-weight: bold; color: #C4C4C4'  : 'font-weight: bold;'" slot="tab">ใบแจ้งหนี้</span></a-tab-pane>
                      <a-tab-pane :key="1"><span :style="tabselect !== 1 ? 'font-weight: bold; color: #C4C4C4'  : 'font-weight: bold;'" slot="tab">ใบเสร็จ</span></a-tab-pane>
                    </a-tabs>
                  </v-col>
                </v-row>
                <v-row dense v-if="tabselect === 0" class="mt-3">
                  <v-col cols="12" md="5" sm="5">
                      <span style="font-size: 16px; color: #636363;">เลขที่ใบแจ้งหนี้ : <span style="font-size: 16px; color: black;">{{ detailData.invoice_number }}</span></span>
                    </v-col>
                    <v-col cols="12" md="6" sm="6" :class="!MobileSize ? 'pl-5' : 'mt-3'">
                      <span style="font-size: 16px; color: #636363;">วันที่ทำรายการแจ้งหนี้ : <span style="font-size: 16px; color: black;">{{detailData.invoice_date}}</span></span>
                  </v-col>
                </v-row>
                <v-row dense v-if="tabselect === 1" class="mt-3">
                  <v-col cols="12" md="5" sm="5">
                      <span style="font-size: 16px; color: #636363;">เลขที่ใบเสร็จ : <span style="font-size: 16px; color: black;">{{ detailData.receipt_number }}</span></span>
                    </v-col>
                    <v-col cols="12" md="6" sm="6" :class="!MobileSize ? 'pl-5' : 'mt-3'">
                      <span style="font-size: 16px; color: #636363;">วันที่ทำรายการใบเสร็จ : <span style="font-size: 16px; color: black;">{{ detailData.receipt_date }}</span></span>
                  </v-col>
                </v-row>
                <v-card dense v-if="tabselect === 0" outlined width="100%" height="100%" @click="getInvoice(detailData)" :href="invoicePath" class="mt-2">
                  <v-card-text>
                    <v-row dense class="d-flex justify-start">
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                      </v-img>
                      <span style="text-align: center; align-content: center;" class="text-truncate pt-2 pr-2">ดาวน์โหลดใบแจ้งหนี้</span>
                    </v-row>
                  </v-card-text>
                </v-card>
                <v-card dense v-if="tabselect === 1 && detailData.transaction_code_receipt !== '-'" outlined width="100%" height="100%" @click="getReceipt(detailData)" class="mt-2">
                  <v-card-text >
                    <v-row dense class="d-flex justify-start" v-if="detailData.transaction_code_receipt !== '-'">
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                      </v-img>
                      <span style="text-align: center; align-content: center;" class="text-truncate pt-2 pr-2">ดาวน์โหลดใบเสร็จ</span>
                    </v-row>
                  </v-card-text>
                </v-card>
                <v-card dense v-if="tabselect === 1 && detailData.transaction_code_receipt === '-'" outlined width="100%" height="100%" class="mt-2">
                  <v-card-text>
                    <v-row dense class="d-flex justify-center">
                      <span style="font-weight: bold; fontsize: 18px">ไม่พบรายละเอียดใบเสร็จ</span>
                    </v-row>
                  </v-card-text>
                </v-card>
                <!-- <v-row dense class="mt-5" v-if="tabselect === 0">
                  <iframe v-if="detailData.transaction_code_invoice !== '-'" @load="onLoad" :src="invoicePathPreview" width="100%" :height="MobileSize ? '500' : IpadSize ? '700' : '1200'"></iframe>
                  <v-row v-else>
                    <v-col class="d-flex justify-center pt-10">
                      <span style="font-weight: bold; fontsize: 18px">ไม่พบรายละเอียดใบแจ้งหนี้</span>
                    </v-col>
                  </v-row>
                </v-row>
                <v-row dense class="mt-5" v-if="tabselect === 1">
                  <iframe v-if="detailData.transaction_code_receipt !== '-'" @load="onLoad" :src="receiptPathPreview" width="100%" :height="MobileSize ? '500' : IpadSize ? '700' : '1200'"></iframe>
                  <v-row v-else>
                    <v-col class="d-flex justify-center pt-10">
                      <span style="font-weight: bold; fontsize: 18px">ไม่พบรายละเอียดใบเสร็จ</span>
                    </v-col>
                  </v-row>
                </v-row> -->
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions class="pa-0">
          <!-- <v-row class="d-flex align-center justify-center mt-5 ma-0 pa-0" style="height: 88px; background: #F5FCFB;">
            <v-col cols="12" class="d-flex align-center justify-center">
              <v-btn dense rounded color="primary" @click="ApproveBilling" v-if="detailData.status === 'รอวางบิล'">
                <span>อนุมัติวางบิล</span></v-btn>
            </v-col>
          </v-row> -->
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Tabs } from 'ant-design-vue'
import { Decode } from '@/services'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      tabselect: 0,
      detaildata: [],
      modalDetails: false,
      startDateRange: '',
      endDateRange: '',
      dateRange: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      RangedateApprove: [],
      modalRangeDate: false,
      selectedStatus: '',
      statusSelected: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'รอวางบิล', value: 'pending' },
        { text: 'รอชำระเงิน', value: 'unpaid' },
        { text: 'ชำระเงินแล้ว', value: 'paid' },
        { text: 'เกินกำหนดชำระ', value: 'overdue_paid' },
        { text: 'ยกเลิกรายการ', value: 'canceled' }
      ],
      search: '',
      checkboxIndeterminate: false,
      singleSelect: false,
      selected: [],
      selectAll: false,
      headers: [
        {
          text: 'ชื่อร้านค้าที่เชื่อมบริการ',
          value: 'shop_name',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'purchase_order_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะดำเนินการ',
          value: 'status',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จำนวน Transaction',
          value: 'number_of_transactions',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบแจ้งหนี้',
          value: 'bill_order_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่รอบบริการ',
          value: 'service_date',
          sortable: false,
          align: 'start',
          width: '220px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขใบเสร็จ',
          value: 'receipt_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จำนวนเงิน',
          value: 'amount',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'action',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      items: [
        {
          shop_name: 'box1',
          ref_order: 'TR1234567890',
          status: 'รอชำระเงิน',
          total_amount: '10,000',
          invoice: 'INV1234567890',
          start_date: '01/01/2026',
          end_date: '31/01/2026',
          receipt: 'INV1234567890',
          payment: '10,000',
          action: 'ดูรายละเอียด'
        },
        {
          shop_name: 'box2',
          ref_order: 'TR1234567891',
          status: 'เกินกำหนดชำระเงิน',
          total_amount: '10,000',
          invoice: 'INV1234567890',
          start_date: '01/01/2026',
          end_date: '31/01/2026',
          receipt: 'INV1234567890',
          payment: '10,000',
          action: 'ดูรายละเอียด'
        },
        {
          shop_name: 'box3',
          ref_order: 'TR1234567892',
          status: 'ชำระเงินแล้ว',
          total_amount: '10,000',
          invoice: 'INV1234567890',
          start_date: '01/01/2026',
          end_date: '31/01/2026',
          receipt: 'INV1234567890',
          payment: '10,000',
          action: 'ดูรายละเอียด'
        }
      ],
      paymentList: [],
      billListPayloads: {
        search_keyword: '',
        bill_status: '',
        date_filter: '',
        page: 1
        // limit: 10
      },
      date: '',
      DateToShow: '',
      ExcelPath: '',
      detailData: {},
      tax_id: '',
      haveConnecShop: false,
      countBillingData: 0,
      taxId: '',
      invoicePath: '',
      invoicePathPreview: '',
      isloading: true,
      receiptPath: '',
      receiptPathPreview: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/PartnerBillingMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/PartnerBilling' }).catch(() => {})
      }
    }
  },
  async created () {
    await this.getBillList()
    if (this.paymentList.length !== 0) {
      this.haveConnecShop = true
    } else {
      this.haveConnecShop = false
    }
  },
  methods: {
    openDialog (val) {
      this.detaildata = []
      this.detaildata = val
      this.modalDetails = true
    },
    async changeStatusProduct () {
      var productIdToChange = []
      this.selected.forEach(item => {
        productIdToChange.push(item.ref_order)
      })
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year)
      return `${day}/${month}/${yearChange}`
    },
    async CloseModalRangeDate () {
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.DateToShow = ''
      this.billListPayloads.date_filter = this.DateToShow
      await this.getBillList()
    },
    async setValueRangeDate (val) {
      this.$refs.modalRangeDate.save(val)
      this.DateToShow = this.formatDateToShow(this.date)
      this.billListPayloads.date_filter = this.DateToShow
      await this.getBillList()
      // var Range = await val.sort((a, b) => {
      //   var dateA = new Date(a)
      //   var dateB = new Date(b)
      //   return dateA - dateB
      // })
      // if (!this.MobileSize) {
      //   await this.getOrderList()
      // }
    },
    selectAllToggle () {
      if (this.selected.length !== this.paymentList.length && this.selectAll) {
        this.selected = []
        const self = this
        if (this.search !== '') {
          this.paymentList.forEach(item => {
            if (item.status !== 'รอวางบิล') {
              self.selected.push(item)
            }
          })
        } else {
          this.paymentList.forEach(item => {
            if (item.status === 'รอวางบิล') {
              self.selected.push(item)
            }
          })
        }
        // this.items.forEach(item => {
        //   if (item.status !== 'ชำระเงินแล้ว') {
        //     self.selected.push(item)
        //   }
        // })
        if (this.selected.length > 0 && (this.paymentList.length === this.selected.length)) {
          this.selectAll = true
          this.checkboxIndeterminate = false
        } else if (this.selected.length > 0) {
          this.checkboxIndeterminate = true
        }
      } else {
        this.selected = []
        this.selectAll = false
        this.checkboxIndeterminate = false
      }
    },
    backgroundColorStatus (val) {
      if (val === 'รอชำระเงิน') {
        return '#EDF2F8'
      } else if (val === 'ชำระเงินแล้ว') {
        return '#F0F9EE'
      } else if (val === 'รอวางบิล') {
        return '#EDF2F8'
      } else if (val === 'ยกเลิกรายการ') {
        return '#e0e0e0'
      } else {
        return '#FEE7E8'
      }
    },
    textColorStatus (val) {
      if (val === 'รอชำระเงิน') {
        return '#1B5DD6'
      } else if (val === 'ชำระเงินแล้ว') {
        return '#52C41A'
      } else if (val === 'รอวางบิล') {
        return '#1AA8C4'
      } else if (val === 'ยกเลิกรายการ') {
        return '#000000'
      } else {
        return '#F5222D'
      }
    },
    SelectDetailOrder (val) {
      this.tabselect = val
      this.isloading = true
      // this.$store.commit('openLoader')
    },
    backtoMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    },
    async getBillList () {
      this.$store.commit('openLoader')
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerCode
      // console.log(response, 'toro')
      if (response.length === 0) {
        await this.getTaxId()
      }
      if (response.code === 200) {
        if (response.data.partner_code === null) {
          this.billListPayloads.partner_code = ''
        } else {
          this.billListPayloads.partner_code = response.data.partner_code
        }
      }
      await this.$store.dispatch('actionsGetBillList', this.billListPayloads)
      response = await this.$store.state.ModuleBusiness.stateGetBillList
      if (response.code === 200) {
        this.paymentList = response.data.bill_list
        this.ExcelPath = response.data.export_report
        this.countBillingData = this.paymentList.length
      } else {
        this.paymentList = []
        this.ExcelPath = ''
        this.countBillingData = 0
      }
      this.$store.commit('closeLoader')
    },
    async setValueStatus () {
      this.billListPayloads.bill_status = this.selectedStatus
      await this.getBillList()
    },
    async getReportExcel () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${this.ExcelPath}`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'billing_partner.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
      this.$store.commit('closeLoader')
    },
    async setDetailValue (item) {
      // this.$store.commit('openLoader')
      var data = {
        bill_order_number: item.bill_order_number
      }
      await this.$store.dispatch('actionsGetBillDetail', data)
      var response = await this.$store.state.ModuleBusiness.stateGetBillDetail
      if (response.code === 200) {
        this.detailData = response.data
      }
      // var data2 = {
      //   transactionCode: item.transaction_code_invoice
      // }
      // await this.$store.dispatch('actionsPartnerEtaxDocumentInvoice', data2)
      // var response2 = this.$store.state.ModuleBusiness.statePartnerEtaxDocumentInvoice
      // if (response2.code === 200) {
      //   this.invoicePathPreview = 'https://docs.google.com/gview?url=' + encodeURIComponent(response2.etaxResponse.pdfURL) + '&embedded=true'
      // }
      // if (item.transaction_code_receipt !== '-') {
      //   var data3 = {
      //     transactionCode: item.transaction_code_receipt
      //   }
      //   await this.$store.dispatch('actionsPartnerEtaxDocumentReceipt', data3)
      //   var response3 = await this.$store.state.ModuleBusiness.statePartnerEtaxDocumentReceipt
      //   if (response3.code === 200) {
      //     this.receiptPathPreview = 'https://docs.google.com/gview?url=' + encodeURIComponent(response3.etaxResponse.pdfURL) + '&embedded=true'
      //   }
      // }
      // console.log(this.detailData)
      this.modalDetails = true
      // this.$store.commit('closeLoader')
      // this.detailData = item
    },
    async ApproveBilling () {
      this.$store.commit('openLoader')
      var billId = []
      billId.push(this.detailData.id)
      var data = {
        partner_code: this.billListPayloads.partner_code,
        package_code: this.detailData.package_code,
        seller_shop_id: this.detailData.seller_shop_id,
        bill_id: billId
      }
      await this.$store.dispatch('actionsGetBillingApproval', data)
      var response = await this.$store.state.ModuleBusiness.stateGetBillingApproval
      if (response.code === 200) {
        this.$swal.fire({
          icon: 'success',
          text: 'อนุมัติวางบิลสำเร็จ',
          showConfirmButton: false,
          timer: 2500
        })
        this.modalDetails = false
        await this.getBillList()
      } else {
        this.$swal.fire({
          icon: 'error',
          text: `อนุมัติวางบิลไม่สำเร็จ ${response.message}`,
          showConfirmButton: false,
          timer: 2500
        })
      }
      this.$store.commit('closeLoader')
    },
    async ApproveBillingMuliple () {
      this.$store.commit('openLoader')
      var data = {
        bill_id: this.selected.map(e => {
          return e.id
        })
      }
      await this.$store.dispatch('actionsGetBillingApproval', data)
      var response = await this.$store.state.ModuleBusiness.stateGetBillingApproval
      if (response.code === 200) {
        this.$swal.fire({
          icon: 'success',
          text: 'อนุมัติวางบิลสำเร็จ',
          showConfirmButton: false,
          timer: 2500
        })
        this.modalDetails = false
        await this.getBillList()
      } else {
        this.$swal.fire({
          icon: 'error',
          text: `อนุมัติวางบิลไม่สำเร็จ ${response.message}`,
          showConfirmButton: false,
          timer: 2500
        })
      }
      this.$store.commit('closeLoader')
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        // this.taxId = response.data.array_business[0].owner_tax_id
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
        }
      }
      // console.log(this.taxId, 'this.taxId')
      await this.getPartnerCode()
      this.$store.commit('closeLoader')
    },
    async getPartnerCode (val, isNewTaxID) {
      var data = {
        id_card_num: isNewTaxID === 'newTaxId' ? val.tax_id : this.taxId
      }
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        this.billListPayloads.partner_code = response.data.partner_code
      }
    },
    async closeDetailDialog () {
      this.modalDetails = false
      this.tabselect = 0
      // this.SelectDetailOrder(this.tabselect)
    },
    async getInvoice (item) {
      // console.log('getInvoice')
      this.$store.commit('openLoader')
      var data = {
        transactionCode: item.transaction_code_invoice
      }
      await this.$store.dispatch('actionsPartnerEtaxDocumentInvoice', data)
      var response = await this.$store.state.ModuleBusiness.statePartnerEtaxDocumentInvoice
      if (response.code === 200) {
        this.invoicePath = await response.etaxResponse.pdfURL
      }
      // this.invoicePath = 'https://docs.google.com/gview?url=' + encodeURIComponent(response.etaxResponse.pdfURL) + '&embedded=true'
      setTimeout(() => {
        document.getElementById('downloadLink').click()
      }, 500)
      this.$store.commit('closeLoader')
      // window.open(this.invoicePath, '_blank')
      // await this.axios({
      //   url: `${response.etaxResponse.pdfURL}`,
      //   method: 'GET',
      //   responseType: 'blob'
      // }).then((response) => {
      //   // สร้าง Blob จากข้อมูลที่ได้รับ
      //   const blob = new Blob([response.data], { type: 'application/pdf' })
      //   const link = document.createElement('a')
      //   link.href = URL.createObjectURL(blob)
      //   link.download = 'document.pdf' // ตั้งชื่อไฟล์ที่ต้องการ
      //   document.body.appendChild(link)
      //   link.click()
      //   document.body.removeChild(link)
      // }).catch((error) => {
      //   console.error('Error:', error)
      // })
    },
    onLoad () {
      this.isloading = false
      this.$store.commit('closeLoader')
    },
    async getReceipt (val) {
      this.$store.commit('openLoader')
      var data = {
        transactionCode: val.transaction_code_receipt
      }
      await this.$store.dispatch('actionsPartnerEtaxDocumentReceipt', data)
      var response = await this.$store.state.ModuleBusiness.statePartnerEtaxDocumentReceipt
      if (response.code === 200) {
        this.receiptPath = response.etaxResponse.pdfURL
        // window.open(this.receiptPath, '_blank')
        setTimeout(() => {
          document.getElementById('downloadLink2').click()
        }, 500)
      }
      this.$store.commit('closeLoader')
    },
    AddTofix (val) {
      if (val !== undefined && val !== null) {
        return val.toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
    }
  }
}
</script>

<style>

</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(10) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(10) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  // ::v-deep(.v-data-table__mobile-row td:first-child),
  // ::v-deep(.v-data-table__wrapper table tbody tr td:first-child),
  // ::v-deep(.v-data-table__wrapper table thead tr th:first-child) {
  //   display: flex;
  //   justify-content: start;
  // }
</style>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
</style>
