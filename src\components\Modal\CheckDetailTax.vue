<template>
  <v-container v-if="pathname !== '/registerLineOA'" :class="!MobileSize ? 'pt-0 px-0 pb-0' : 'pt-0 px-0 pb-0'">
    <v-dialog width="700px" height="100%" v-model="ModalDetailTax" persistent>
      <v-card width="700px" height="100%" style="border-radius: 8px; overflow-x: hidden;" :elevation="MobileSize ? 1 : 0" :class="MobileSize ? 'my-4' : 'mb-0'">
        <div class="pt-9 px-9">
          <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลภาษี</span>
          <v-form ref="FormbuyerDetailTax" :lazy-validation="lazy">
            <v-row>
              <v-col cols="12" class="mt-4 reduce-spacing">
                <span>หมายเลขประจำตัวผู้เสียภาษี <span style="color: red;">*</span></span>
                <v-text-field class="input-text" placeholder="ระบุเลขประจำตัวผู้เสียภาษี" v-model="national_id" @input="validateTaxID()" :maxLength="13" counter="13" outlined dense :rules="Rules.national_id"></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="6" style="display: flex; align-items: center;">
                <span style="padding-right: 10px;">รูปบัตรประจำตัวประชาชน <span style="color: red;">*</span></span>
                <v-btn @click="triggerFileInputTax()" color="#27AB9C" class="white--text rounded-button">อัปโหลด</v-btn>
                <input type="file" ref="fileInputTax" @change="handleFileUploadTax($event)" style="display: none;">
              </v-col> -->
              <!-- <v-col cols="12" md="6">
                <div v-if="taxImageUrl" class="mt-2" style="display: flex; justify-content: space-evenly;">
                  <v-card class="d-flex justify-center align-center mb-6" style="max-width: 300px; max-height: 300px; overflow: hidden;">
                    <img :src="taxImageUrl" style="width: 100%; height: 100%; object-fit: contain;" @click="viewImageTax">
                  </v-card>
                </div>
              </v-col> -->
            </v-row>
            <v-divider class="my-4"></v-divider>
            <v-card-actions style="display: flex; justify-content: center; padding-bottom: 20px;">
              <!-- {{ national_id === '' || national_id === undefined || national_id === null ? true : false }} -->
              <!-- <v-btn :disabled="national_id !== '' && taxImageUrl !== null ? false : true" class="px-5 white--text" color="#27AB9C" @click="ConfirmModalEditDetailtax()">บันทึก</v-btn> -->
              <v-btn :disabled="national_id === '' || national_id === undefined || national_id === null ? true : false" class="px-5 white--text" color="#27AB9C" @click="ConfirmModalEditDetailtax()">บันทึก</v-btn>
            </v-card-actions>
          </v-form>
        </div>
      </v-card>
    </v-dialog>
    <!-- Modal Confirm Edit Account -->
    <v-dialog v-model="ModalConfirmEditDetailTax" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="ModalConfirmEditDetailTax = !ModalConfirmEditDetailTax"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลภาษี</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmEditDetailTax = !ModalConfirmEditDetailTax">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="saveUpdate()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Tier -->
    <v-dialog v-model="ModalSuccessEditDetailTax" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลภาษีเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" :block="MobileSize ? true : false" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogBookbank" max-width="500px">
      <v-card>
        <v-card-text class="d-flex justify-center" style="padding: 20px;">
          <img :src="bookbankImageUrl" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions style="padding: 10px;">
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogBookbank = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogTax" max-width="500px">
      <v-card>
        <v-card-text class="d-flex justify-center" style="padding: 20px;">
          <img :src="taxImageUrl" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions style="padding: 10px;">
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogTax = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { Decode } from '@/services'

export default {
  data () {
    return {
      pathname: '',
      ModalDetailTax: false,
      ModalConfirmEditDetailTax: false,
      ModalSuccessEditDetailTax: false,
      dialogBookbank: false,
      dialogTax: false,
      isEditable: false,
      lazy: false,
      accountTypes: [
        { text: 'ออมทรัพย์', value: 'savings' },
        { text: 'ฝากประจำ', value: 'current' }
      ],
      itemsBank: [],
      account_type: '',
      bank_username: '',
      selectedBank: null,
      bank_name: '',
      bank_code: '',
      bank_branch: '',
      bank_no: '',
      national_id: '',
      bookbankImage: '',
      bookbankImageUrl: null,
      taxImage: '',
      taxImageUrl: null,
      building_name: '',
      floor: '',
      house_no: '',
      moo_ban: '',
      moo_no: '',
      room_no: '',
      soi: '',
      street: '',
      yaek: '',
      sub_district: '',
      district: '',
      province: '',
      zip_code: '',
      Rules: {
        national_id: [
          v => !!v || 'กรุณาระบุเลขประจำตัวผู้เสียภาษี',
          v => (/^\d+$/.test(v) && v.length === 13) || 'เลขประจำตัวผู้เสียภาษีต้องมี 13 หลักและเป็นตัวเลขเท่านั้น',
          v => this.validNationalID(v) || 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
        ]
      }
    }
  },
  async created () {
    this.pathname = window.location.pathname.replace(/\/$/, '')
    if (localStorage.getItem('oneData') !== null) {
      // this.showDetailBuyer()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateTaxID () {
      if (this.validNationalID(this.national_id)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    async open () {
      // console.log('open Modal')
      await this.showDetailBuyer()
      this.ModalDetailTax = true
    },
    ConfirmModalEditDetailtax () {
      if (this.$refs.FormbuyerDetailTax.validate(true)) {
        this.ModalConfirmEditDetailTax = true
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณากรอกข้อมูลให้ถูกต้อง</h3>'
        })
      }
    },
    async saveUpdate () {
      this.update = false
      this.isEditable = false
      this.$store.commit('openLoader')

      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const formData = new FormData()
      formData.append('user_id', onedata.user.user_id)
      formData.append('bank_name', this.bank_name)
      formData.append('bank_branch', this.bank_branch)
      formData.append('bank_username', this.bank_username)
      formData.append('bank_code', this.bank_code)
      formData.append('bank_no', this.bank_no)
      formData.append('account_type', this.account_type)
      formData.append('bookbank_image', this.bookbankImage)
      formData.append('national_id', this.national_id)
      formData.append('id_card_image', this.taxImage)
      formData.append('building', this.building_name)
      formData.append('floor', this.floor)
      formData.append('house_no', this.house_no)
      formData.append('moo_ban', this.moo_ban)
      formData.append('moo_no', this.moo_no)
      formData.append('room_no', this.room_no)
      formData.append('soi', this.soi)
      formData.append('street', this.street)
      formData.append('yaek', this.yaek)
      formData.append('sub_district', this.sub_district)
      formData.append('district', this.district)
      formData.append('province', this.province)
      formData.append('zip_code', this.zip_code)

      await this.$store.dispatch('actionsAffiliateUpdatePayment', formData)
      const responseUpdatePayment = await this.$store.state.ModuleAffiliate.stateAffiliateUpdatePayment

      if (responseUpdatePayment.message === 'update success') {
        this.$store.commit('closeLoader')
        this.ModalSuccessEditDetailTax = true
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true,
        //   icon: 'success',
        //   title: 'อัปเดทสำเร็จ'
        // })
      } else if (responseUpdatePayment.message === 'Large file size') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ขนาดไฟล์เกิน 5  MB'
        })
      } else if (responseUpdatePayment.message === 'File must be JPEG, PNG, JPG only') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ต้องเป็นไฟล์ในรูปแบบของ JPEG, PNG, JPG เท่านั้น'
        })
      } else if (responseUpdatePayment.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true,
        //   icon: 'info',
        //   title: 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
        // })
      } else if (responseUpdatePayment.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseUpdatePayment.message}`
        })
      }
    },
    async showDetailBuyer () {
      this.$store.commit('openLoader')
      // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // var data = {
      //   user_id: onedata.user.user_id
      // }

      // await this.$store.dispatch('actionsAffiliateShowDetailBuyer', data)
      await this.$store.dispatch('actionsAffiliateShowDetailBuyer')
      const responseShowDetailBuyer = await this.$store.state.ModuleAffiliate.stateAffiliateShowDetailBuyer
      // console.log('responseShowDetailBuyer', responseShowDetailBuyer)
      if (responseShowDetailBuyer.data !== undefined) {
        this.account_type = responseShowDetailBuyer.data.account_type
        this.bank_username = responseShowDetailBuyer.data.bank_username
        this.bank_name = responseShowDetailBuyer.data.bank_name
        this.bank_code = responseShowDetailBuyer.data.bank_code
        this.bank_branch = responseShowDetailBuyer.data.bank_branch
        this.bank_no = responseShowDetailBuyer.data.bank_no
        this.bookbankImageUrl = responseShowDetailBuyer.data.bookbank_image_url
        this.national_id = responseShowDetailBuyer.data.national_id
        this.taxImageUrl = responseShowDetailBuyer.data.id_card_image_url
        this.building_name = responseShowDetailBuyer.data.building_name
        this.floor = responseShowDetailBuyer.data.floor
        this.house_no = responseShowDetailBuyer.data.house_no
        this.moo_ban = responseShowDetailBuyer.data.moo_ban
        this.moo_no = responseShowDetailBuyer.data.moo_no
        this.room_no = responseShowDetailBuyer.data.room_no
        this.soi = responseShowDetailBuyer.data.soi
        this.street = responseShowDetailBuyer.data.street
        this.yaek = responseShowDetailBuyer.data.yaek
        this.sub_district = responseShowDetailBuyer.data.sub_district
        this.district = responseShowDetailBuyer.data.district
        this.province = responseShowDetailBuyer.data.province
        this.zip_code = responseShowDetailBuyer.data.zip_code
      }
      this.$store.commit('closeLoader')
    },
    closeModalSuccess () {
      this.ModalConfirmEditDetailTax = false
      this.ModalSuccessEditDetailTax = false
      this.ModalDetailTax = false
    },
    triggerFileInputTax () {
      this.$refs.fileInputTax.click()
    },
    handleFileUploadTax (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.taxImageUrl = reader.result
            this.taxImage = file
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพที่สกุล jpeg/jpg/png เท่านั้น',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    viewImageBookbank () {
      this.dialogBookbank = true
    },
    viewImageTax () {
      this.dialogTax = true
    }
  }
}
</script>
