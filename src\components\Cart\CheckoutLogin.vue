<template>
  <v-container>
    <v-row dense>
      <v-overlay :value="overlay">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-col cols="12" md="12">
        <v-card outlined class="elevation-0">
          <v-container grid-list-xs,sm,md,lg,xl>
            <v-row dense >
              <span class="black--text subtitle-1 font-weight-bold">ที่อยู่ในการจัดส่ง</span>
              <!-- <v-spacer></v-spacer>
              <v-btn text color="primary">เพิ่มที่อยู่ใหม่</v-btn>
              <v-btn text color="primary">จัดการที่อยู่</v-btn> -->
              <v-container grid-list-xs>
                <v-row>
                  <v-col cols="12" md="12" class="text-left">
                    <b>{{ Fullname }}</b>
                    <span class="pl-2 black--text subtitle-3">{{ Address }}</span>
                    <!-- <span class="black--text subtitle-3">นายอภิชาติ คมสัน 0918258185</span>&nbsp;<a text class="pt-0" color="primary">เปลี่ยน</a> -->
                  </v-col>
                </v-row>
              </v-container>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" md="12">
        <v-card outlined class="elevation-0">
          <v-container grid-list-xs>
            <a-table bordered v-for="(item,index) in itemsCart.choose_list" :key="index" :data-source="item.product_list" :rowKey="record => record.sku" :columns="headers">
              <template slot="title">
                <v-row>
                  <v-col cols="6" class="text-left">
                     <p>{{item.shop_name}}</p>
                  </v-col>
                  <!-- <v-col cols="6" class="text-right">
                    <v-menu offset-y>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn outlined color="primary" dark v-bind="attrs" v-on="on">
                          <v-icon dark left>mdi-cloud-download-outline</v-icon>
                          เอกสาร
                          <v-icon dark right>mdi-menu-down</v-icon>
                        </v-btn>
                      </template>
                      <v-list subheader nav dense>
                        <v-list-item @click="quotation_page(item, 'purchase')">
                          <v-list-item-avatar>
                            <v-img :alt="`Purchaser Order.pdf avatar`" :src="require(`@/assets/icons/<EMAIL>`)"></v-img>
                          </v-list-item-avatar>
                          <v-list-item-content>
                            <v-list-item-title>ใบสั่งซื้อ</v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                        <v-list-item @click="quotation_page(item, 'quotation')">
                          <v-list-item-avatar>
                            <v-img :alt="`Quotation.pdf avatar`" :src="require(`@/assets/icons/<EMAIL>`)"></v-img>
                          </v-list-item-avatar>
                          <v-list-item-content>
                            <v-list-item-title>ใบเสนอราคา</v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </v-col> -->
                </v-row>
              </template>
              <template slot="productdetails" slot-scope="text, record">
                <v-row>
                  <v-col cols="12" md="4" class="pr-0 py-1">
                    <v-img :src="record.product_image.url" class="imageshow"/>
                  </v-col>
                  <v-col cols="12" md="8">
                    <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                  </v-col>
                </v-row>
              </template>
              <template slot="price" slot-scope="text, record">
                <v-col cols="12">
                  <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </v-col>
              </template>
              <template slot="net_price" slot-scope="text, record">
                <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
              <template slot="footer">
                <v-row>
                  <v-col cols="12" md="10" class="text-right">
                    ราคารวมร้านค้า
                  </v-col>
                  <v-col cols="12" md="2" class="text-left">
                    {{ Number(item.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </v-col>
                </v-row>
              </template>
            </a-table>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" md="12">
        <v-card outlined class="elevation-0">
          <v-container grid-list-xs>
            <v-row>
              <v-col cols="12" md="10">
                <v-row dense>
                  <v-col cols="12" class="text-right">
                    <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ส่วนลด :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ราคารวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ค่าจัดส่ง :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="subheader">ราคารวมทั้งหมด :</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="2">
                <v-row dense>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(itemsCart.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(itemsCart.total_price_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(itemsCart.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(itemsCart.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(itemsCart.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="6" class="text-left pt-5">
        <v-btn color="blue-grey" outlined @click="backstep()">
          ย้อนกลับ
        </v-btn>
      </v-col>
      <v-col cols="6" class="text-right pt-5">
        <v-btn color="primary" outlined @click="confirmCreateOrder()">
          ยืนยันการสั่งซื้อ
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data: () => ({
    overlay: false,
    cartData: '',
    itemsCart: [],
    Address: '',
    Fullname: ''
  }),
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '40%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          width: '20%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          width: '20%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          width: '20%'
        }
      ]
      return headers
    }
  },
  watch: {
  },
  async created () {
    this.$EventBus.$on('SentGetCart', this.getCart)
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('one data in check out', onedata)
    if (Object.prototype.hasOwnProperty.call(onedata, 'cartData')) {
      this.cartData = onedata.cartData
      this.getCart()
    } else {
      this.cartData = ''
      this.$router.push('/')
    }
  },
  methods: {
    async getCart () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // console.log('ยิง API หลังสร้าง', this.cartData)
      this.overlay = true
      await this.$store.dispatch('ActionGetCart', this.cartData)
      var res = await this.$store.state.ModuleCart.stateGetCart
      // console.log('res get cart in shopping cart', res)
      if (res.message === 'Get cart success') {
        this.itemsCart = res.data
        if (this.itemsCart.address_data.length !== 0) {
          // this.Fullname = this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name
          this.Fullname = this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name
          var address = ''
          if (dataRole.role === 'ext_buyer') {
            address = this.itemsCart.address_data[0].detail + ' ' + 'ตำบล' + ' ' + this.itemsCart.address_data[0].sub_district + ' ' + 'อำเภอ' + ' ' + this.itemsCart.address_data[0].district + ' ' + 'จังหวัด' + ' ' + this.itemsCart.address_data[0].province + ' ' + this.itemsCart.address_data[0].zip_code + ' ' + 'เบอร์' + ' ' + this.itemsCart.address_data[0].phone
            // address = this.itemsCart.address_data[0].house_no + ' ' + this.itemsCart.address_data[0].detail + ' ' + this.itemsCart.address_data[0].sub_district + ' ' + this.itemsCart.address_data[0].district + ' ' + this.itemsCart.address_data[0].province + ' ' + this.itemsCart.address_data[0].zip_code
            this.Address = address
          } else if (dataRole.role === 'purchaser') {
            address = ' ' + this.itemsCart.address_data[0].detail + ' ' + this.itemsCart.address_data[0].zip_code
            // address = this.itemsCart.address_data[0].house_no + ' ' + '' + ' ' + this.itemsCart.address_data[0].sub_district + ' ' + this.itemsCart.address_data[0].district + ' ' + this.itemsCart.address_data[0].province + ' ' + this.itemsCart.address_data[0].zip_code
            this.Address = address
          }
        } else {
          this.Fullname = ''
          this.Address = 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'
        }
        this.overlay = false
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'ใส่ข้อมูลไม่ครบ'
        })
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    backstep () {
      this.$router.push('/shoppingcart')
    },
    confirmCreateOrder () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // console.log('dataRole', dataRole)
      if (dataRole.role === 'purchaser') {
        // const ToastDelete = this.$swal.mixin({
        //   icon: 'warning',
        //   html: '* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>การซื้อของฉัน</u></b>',
        //   title: 'ยืนยันการสั่งซื้อหรือไม่',
        //   showCancelButton: true,
        //   confirmButtonText: 'ยืนยัน',
        //   cancelButtonText: 'ยกเลิก',
        //   cancelButtonColor: '#d33'
        // })
        this.$swal.fire({
          icon: 'warning',
          html: '* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>การซื้อของฉัน</u></b>',
          title: 'ยืนยันการสั่งซื้อหรือไม่',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          cancelButtonColor: '#d33'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      } else if (dataRole.role === 'ext_buyer') {
        // console.log('testttttttttt')
        // const ToastDelete = this.$swal.mixin({
        //   icon: 'warning',
        //   title: 'ยืนยันการสั่งซื้อหรือไม่',
        //   showCancelButton: true,
        //   confirmButtonText: 'ยืนยัน',
        //   cancelButtonText: 'ยกเลิก',
        //   cancelButtonColor: '#d33'
        // })
        this.$swal.fire({
          icon: 'warning',
          showCancelButton: true,
          title: 'ยืนยันการสั่งซื้อหรือไม่',
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          cancelButtonColor: '#d33'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      }
    },
    async CreateOrder () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.overlay = true
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('ActionCreateOrder', data)
      var res = await this.$store.state.ModuleCart.stateCreateOrder
      // console.log('create product', res.data)
      if (res.message === 'Create Order success') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'สั่งสินค้าเรียบร้อย'
        })
        var dataPayment = {
          payment_transaction_number: res.data
        }
        // console.log('dataPayment', dataPayment)
        await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
        var resRedirect = this.$store.state.ModuleCart.stateGetPaymentPage
        this.overlay = false
        // console.log('res get paymentpage', resRedirect)
        localStorage.setItem('PaymentData', Encode.encode(resRedirect))
        this.$router.push('/RedirectPaymentPage')
      } else if (res.message === 'Credit is not enough for buy') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 3000,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่สามารถสั่งซื้อได้เนื่องจากวงเงินเครดิตไม่เพียงพอ'
        })
        this.Cancel()
      } else if (res.message === 'Not enough product in stock') {
        // console.log('เข้ามาแล้วจ้า', res.data[0])
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: `สินค้า ${res.data[0].product} สามารถซื้อได้ ${res.data[0].quantity} ชิ้นเท่านั้น`
        })
        this.Cancel()
      } else if (res.message === 'Create Order success to approve') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'สั่งสินค้าเรียบร้อย'
        })
        this.goHomePage()
      } else if (res.message === 'Type_budget not found.') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ไม่พบประเภทการควบคุมวงเงิน'
        })
        this.Cancel()
      } else if (res.message === 'No approver in this company.') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ไม่มีผู้อนุมัติในบริษัทนี้'
        })
        this.Cancel()
      } else if (res.message === 'Update New Price') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          title: 'ระบบมีการอัปเดตสินค้าใหม่'
        })
        this.getCart()
      }
    },
    quotation_page (data, typeOrder) {
      // console.log('testTypeOrder', JSON.stringify(data.product_list))
      localStorage.setItem('typeOrder', typeOrder)
      localStorage.setItem('PDF_Data', Encode.encode(JSON.stringify(data)))
      window.open('/quotationpagecart', '_blank')
    },
    Cancel () {
      this.$router.push({ path: '/shoppingcart' })
    },
    goHomePage () {
      this.$router.push({ path: '/' })
    }
  }
}
</script>

<style scoped>
.imageshow {
    width: 50px;
    height: 50px;
}
</style>
