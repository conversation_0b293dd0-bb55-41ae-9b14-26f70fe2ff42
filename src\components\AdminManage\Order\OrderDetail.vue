<template>
  <div :class="MobileSize ? 'mt-3' : ''">
    <!-- Mobile ipad-->
    <v-container v-if="MobileSize || IpadSize" grid-list-xl>
      <v-card outlined>
        <v-card-title v-if="MobileSize" class="px-1"><v-icon color="#27AB9C" class="mr-2" @click="backtoPOBuyer()">mdi-chevron-left</v-icon> รายการสั่งซื้อ</v-card-title>
        <v-row justify="center" class="my-4">
          <h2 style="font-weight: 700; font-size: 20px;"> สถานะการสั่งซื้อของสินค้า</h2>
        </v-row>
        <v-row no-gutters class="mx-4">
          <v-col cols="12" align="left" class="mb-4">
            <span class="fontSizeDetailMobile'">รหัสการชำระเงิน : </span>
            <span class="fontSizeDetailMobile">{{ items.payment_transaction }}</span>
          </v-col>
          <v-col cols="12" align="left" class="mb-4" v-if="trackingText">
            <span>สถานะ : </span>
            <span><b>{{ trackingText }}</b></span>
          </v-col>
          <v-col v-if="dateCreateOrderStep4 !== 'Invalid Date'" cols="12" align="left" class="mb-4">
            <span>วันที่สินค้าทั้งหมดถึงผู้รับ : </span>
            <span>{{ dateCreateOrderStep4 }}</span>
          </v-col>
          <v-col cols="12">
            <!-- status ชำระเงินแบบเครดิตเทอม (Mobile, Ipad) -->
            <v-row no-gutters v-if="trackingStatus === 'Not Sent' && items.status === 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">ชำระเงินแบบเครดิตเทอม</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อรอการจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-if="trackingStatus === 'Not Received' && items.status === 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-if="trackingStatus === 'Received' && items.status === 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">ชำระเงินแบบเครดิตเทอม</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">สินค้าทั้งหมดถึงผู้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund' && items.status === 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">ชำระเงินแบบเครดิตเทอม</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">ผู้ซื้อตรวจสอบและขอคืนสินค้า</span>
              </v-col>
            </v-row>
            <!-- status ชำระเงินแบบปกติ (Mobile, Ipad) -->
            <v-row no-gutters v-if="trackingStatus === 'Not Paid' && items.status !== 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper2.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku"><b>ที่ต้องชำระเงิน</b></span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.status !== 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Sent'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Not Received' && items.status !== 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Received' && items.status !== 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">สินค้าทั้งหมดถึงผู้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund' && items.status !== 'Credit'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">ผู้ซื้อตรวจสอบและขอคืนสินค้า</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontActive captionSku">ที่ต้องจัดส่ง</span>
              </v-col>
              <v-col cols="3" align="center">
                <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Cancel by approver'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper7.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="6" align="left" :style="{'padding-left': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="6" align="right" :style="{'padding-right': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">ยกเลิกคำสั่งซื้อ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-else-if="trackingStatus === 'Cancel'" class="mb-5">
              <v-col cols="12" align="center">
                <v-img src="@/assets/stepper8.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
              </v-col>
              <v-col cols="6" align="left" :style="{'padding-left': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">คำสั่งซื้อใหม่</span>
              </v-col>
              <v-col cols="6" align="right" :style="{'padding-right': IpadSize ? '20px' : '10px'}">
                <span class="captionSku">ยกเลิกคำสั่งซื้อ</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-if="trackingStatus === ''" class="mb-5">
              <h1>ไม่พบข้อมูล</h1>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-card outlined>
              <!-- address and tracking mobile ipad-->
              <v-container grid-list-lg>
                <v-row no-gutters>
                  <v-col cols="12">
                    <v-row no-gutters>
                      <v-col cols="12">
                        <p class="fontSizeTitleMobile" style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</p>
                      </v-col>
                      <v-col cols="12" class="mb-4 ml-1">
                        <span class="fontSizeDetailMobile">{{ items.address_data }}</span>
                      </v-col>
                      <v-col :cols="12" >
                        <p class="fontSizeTitleMobile" style="font-weight: 700;">ที่อยู่ในการจัดส่งใบกำกับภาษี</p>
                      </v-col>
                      <v-col cols="12" class="mb-2 ml-1" v-if="items.invoice_address !== ''" >
                        <span class="fontSizeDetailMobile">{{ items.required_invoice !== '-' ? items.invoice_address : '-' }}</span>
                      </v-col>
                      <v-col cols="12" class="mb-2 ml-1" v-else>
                        <span class="fontSizeDetailMobile">{{ '-' }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-container>
              <!-- accept = success , review review mobile ipad-->
              <!-- product table mobile ipad-->
              <v-container grid-list-xs class="px-0">
                <v-col cols="12" class="pt-0">
                  <p class="fontSizeTitleMobile"><b>รายการสั่งซื้อสินค้า</b></p>
                </v-col>
                <v-col cols="12" v-for="(order, index) in items.data_list" :key="index">
                  <v-card outlined>
                    <v-row no-gutters class="pa-3 mb-2" type="flex" justify="start">
                      <v-col cols="12">
                        <span>รหัสการสั่งซื้อ
                          <b style="font-size: 12px;">{{order.order_number}}</b><br>
                        </span>
                      </v-col>
                      <!-- <v-col cols="6" align="start" :class="MobileSize ? 'mt-2' : ''">
                        <v-btn class="py-3 px-0 fontSizeDetailMobile" text color="#27AB9C" @click="GoToMobily(order.url_tracking)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain></v-img> ติดตามสถานะขนส่ง</v-btn>
                      </v-col> -->
                      <v-col cols="12" align="start" class="mt-2">
                        <span v-if="order.service_type === 'chilled'">
                          <v-chip small color="#E5EFFF" text-color="#1B5DD6">ส่งแบบควบคุมอุณหภูมิ {{ order.business_type }} Express</v-chip>
                        </span>
                        <span v-if="order.service_type === 'normal'">
                          <v-chip small color="#E6F5F3" text-color="#27AB9C">ส่งแบบปกติ {{ order.business_type }} Express</v-chip>
                        </span>
                        <span v-if="order.service_type === 'frozen'">
                          <v-chip small color="#E6F5F3" text-color="#26C6DA">ส่งแบบแช่แข็ง {{ order.business_type }} Express</v-chip>
                        </span>
                        <span v-if="order.service_type === 'bulk'">
                          <v-chip small color="#FCF0DA" text-color="#E9A016">ส่งของขนาดใหญ่ {{ order.business_type }} Express</v-chip>
                        </span>
                      </v-col>
                      <v-col cols="12" class="mt-2" v-if="order.order_mobilyst_no">
                        <span class="fontSizeDetailMobile">Tracking Number <b>{{order.order_mobilyst_no}}</b></span>
                      </v-col>
                      <v-col cols="12" class="mt-1">
                        <v-btn class="pr-1" :class="IpadSize ? 'pl-3' : 'pl-0'" v-if="order.order_mobilyst_no"  text color="#27AB9C" @click="GoToMobily(order.url_tracking)" style="color: #27AB9C; text-decoration: underline; font-size: 12px;">
                          <v-img src="@/assets/icons/Vector.png" contain></v-img>
                          ติดตามสถานะขนส่ง
                        </v-btn>
                      </v-col>
                      <v-col cols="12" class="mt-3">
                        <span>
                          {{ Object.keys(order.product_list).length }} รายการสินค้า
                        </span>
                      </v-col>
                    </v-row>
                    <a-table :data-source="order.product_list" :rowKey="record => record.sku" :columns="headersMobile" :pagination="{ pageSize: 100 }">
                      <template slot="productdetails" slot-scope="text, record">
                        <v-row>
                          <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                            <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                            <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                          </v-col>
                          <v-col cols="9" md="8">
                            <span class="mb-0 DetailsProductFrontMobile">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</span><br>
                            <!-- <div class="mb-0" v-if="record.have_attribute === 'yes'">
                              <span class="mb-0 DetailsProductFrontMobile" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                              <span class="ml-3 mb-0 DetailsProductFrontMobile"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                            </div> -->
                            <span class="mb-0 DetailsProductFrontMobile">จำนวน: <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                            <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา: <span style="font-weight: 700;">{{ Number(record.revenue_default).toLocaleString(undefined, { minimumFractionDigits: 2}) }}</span></span><br>
                            <span class="pt-1 mb-0 DetailsProductFrontMobile">ราคารวม: <span style="font-weight: 700;">{{ Number(record.total_revenue_default).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span></span>
                          </v-col>
                        </v-row>
                      </template>
                    </a-table>
                    <!-- btn comfirm accept, refund, contact seller mobile ipad-->
                    <!-- <v-row no-gutters v-if="checkAcceptProduct[index].status !== 'expired' && items.transaction_status !== 'Not Paid'" :class="checkAcceptProduct[index].status === 'waiting_review' || checkAcceptProduct[index].status === 'success' ? 'pa-0' : 'pa-3'">
                      <v-col cols="12" align="end">
                        <span v-if="checkAcceptProduct[index].status === 'reject'" style="color: #BDBDBD">คำขอคืนสินค้าไม่ได้รับการอนุมัติจากร้านค้า</span>
                        <span v-if="checkAcceptProduct[index].status === 'refund'" style="color: #BDBDBD">คำขอคืนสินค้าได้รับการอนุมัติแล้ว</span>
                        <span v-if="checkAcceptProduct[index].status === 'waiting_refund'" style="color: #BDBDBD">ส่งคำขอคืนสินค้าแล้ว รอร้านค้าอนุมัติ</span>
                      </v-col>
                      <v-col cols="12">
                        <v-btn small block v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].refund === 'no'" outlined class="mb-3 mr-2 px-5" color="#D1392B" @click="refundProductBuyer(order)"><b class="buttonFontSize">คืนสินค้า</b></v-btn>
                      </v-col>
                      <v-col cols="12">
                        <v-btn small block v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].status !== 'waiting_accept'" class="white--text px-5" color="#27AB9C" @click="acceptProduct(order)"><b class="buttonFontSize">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                      </v-col>
                    </v-row>
                    <v-row no-gutters v-if="checkAcceptProduct[index].status !== 'expired' && items.transaction_status === 'Not Paid' && items.status === 'Credit'" :class="checkAcceptProduct[index].status === 'waiting_review' || checkAcceptProduct[index].status === 'success' ? 'pa-0' : 'pa-3'">
                      <v-col cols="12" align="end">
                        <span v-if="checkAcceptProduct[index].status === 'reject'" style="color: #BDBDBD">คำขอคืนสินค้าไม่ได้รับการอนุมัติจากร้านค้า</span>
                        <span v-if="checkAcceptProduct[index].status === 'refund'" style="color: #BDBDBD">คำขอคืนสินค้าได้รับการอนุมัติแล้ว</span>
                        <span v-if="checkAcceptProduct[index].status === 'waiting_refund'" style="color: #BDBDBD">ส่งคำขอคืนสินค้าแล้ว รอร้านค้าอนุมัติ</span>
                      </v-col>
                      <v-col cols="12">
                        <v-btn small block v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].refund === 'no'" outlined class="mb-3 mr-2 px-5" color="#D1392B" @click="refundProductBuyer(order)"><b class="buttonFontSize">คืนสินค้า</b></v-btn>
                      </v-col>
                      <v-col cols="12">
                        <v-btn small block v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].status !== 'waiting_accept'" class="white--text px-5" color="#27AB9C" @click="acceptProduct(order)"><b class="buttonFontSize">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                      </v-col>
                    </v-row>
                    <v-container grid-list-xs v-if="checkAcceptProduct[index].status === 'waiting_review'">
                      <v-row>
                        <v-col cols="12" align="right" class="px-3">
                          <v-chip rounded class="white--text px-5" color="#52C41A" @click="openModalReviewProduct(order, index)"><b class="buttonFontSize">ประเมินความพึงพอใจสินค้า</b></v-chip>
                        </v-col>
                      </v-row>
                    </v-container>
                    <v-container grid-list-xs v-if="checkAcceptProduct[index].status === 'success'">
                      <v-row>
                        <v-col cols="12" align="right" class="px-3">
                          <v-chip rounded class="white--text px-5" color="#27AB9C" @click="openModalEditReviewProduct(order, index)"><b class="buttonFontSize">รายละเอียดการประเมินความพึงพอใจสินค้า</b></v-chip>
                        </v-col>
                      </v-row>
                    </v-container> -->
                  </v-card>
                </v-col>
              </v-container>
              <!-- price, total_price_vat, shipping mobile ipad-->
              <v-container grid-list-xs>
                <!-- สรุปรายการสั่งซื้อ ipad -->
                <v-row v-if="IpadSize">
                  <v-col cols="12" md="10">
                    <v-row dense>
                      <v-col cols="9" class="text-right">
                        <span class="fontSizeTotalPriceMobile">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                      </v-col>
                      <v-col cols="3" class="text-right">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9" class="text-right">
                        <span class="fontSizeTotalPriceMobile">ส่วนลด :</span>
                      </v-col>
                      <v-col cols="3" class="text-right">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9" class="text-right">
                        <span class="fontSizeTotalPriceMobile">ภาษีมูลค่าเพิ่ม :</span>
                      </v-col>
                      <v-col cols="3" class="text-right">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9" class="text-right">
                        <span class="fontSizeTotalPriceMobile">ราคารวมภาษีมูลค่าเพิ่ม :</span>
                      </v-col>
                      <v-col cols="3" class="text-right">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9" class="text-right">
                        <span class="fontSizeTotalPriceMobile">ค่าจัดส่ง :</span>
                      </v-col>
                      <v-col cols="3" class="text-right">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="9" class="text-right">
                        <span class="subheader fontSizeTotalPriceMobile">ราคารวมทั้งหมด :</span>
                      </v-col>
                      <v-col cols="3" class="text-right">
                        <span class="fontSizeTotalPriceMobile">{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <!-- สรุปรายการสั่งซื้อ mobile -->
                <v-row v-else>
                  <v-col cols="12">
                    <OrderSummary :items="items"></OrderSummary>
                  </v-col>
                </v-row>
              </v-container>
            </v-card>
          </v-col>
          <!-- status payment mobile ipad-->
          <v-col cols="12" class="mt-2">
            <v-card outlined>
              <v-container grid-list-xs>
                <v-row no-gutters>
                  <v-col v-if="items.status === 'Credit'" cols="12" class="mt-4">
                    <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E5EFFF" small text-color="#1B5DD6">ชำระเงินแบบเครดิตเทอม</v-chip></p>
                    <v-row dense v-if="items.transaction_status === 'Not Paid'" class="mb-4">
                      <v-btn class="white--text ml-1" color="#27AB9C" @click="gotoCrediterm(items.payment_transaction)">ไปหน้าชำระแบบเครดิตเทอม <v-icon small>mdi-chevron-right</v-icon></v-btn>
                    </v-row>
                  </v-col>
                  <v-col v-else-if="items.transaction_status === 'Success'" cols="12" class="mt-4">
                    <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสำเร็จ</v-chip></p>
                  </v-col>
                  <v-col v-else-if="items.transaction_status === 'Cancel'" cols="12" class="mt-4">
                    <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip></p>
                  </v-col>
                  <v-col v-else-if="items.transaction_status === 'Fail'" cols="12" class="mt-4">
                    <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#FFECB3" small text-color="#FFB300">ชำระเงินไม่สำเร็จ</v-chip></p>
                  </v-col>
                  <v-col v-else cols="12" class="mt-4">
                    <p class="fontSizeTitleMobile" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E5EFFF" small text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip></p>
                  </v-col>
                  <v-row v-if="items.transaction_status === 'Success'" no-gutters>
                    <v-col cols="12" class="mt-4">
                      <span class="mr-2">คุณได้ชำระเงินเสร็จเรียบร้อยแล้ว ขอบคุณสำหรับการใช้บริการ</span>
                    </v-col>
                    <v-col cols="12" class="mt-5">
                      <span style="color: #27AB9C;"><b>&#8226; หลักฐานการชำระเงิน</b></span>
                    </v-col>
                    <v-col cols="11" class="mt-5">
                      <v-row no-gutters>
                        <v-col cols="6" md="3">
                          <span>รหัสการชำระเงิน : </span>
                        </v-col>
                        <v-col cols="6" md="9">
                          <span>{{ items.receipt[0].orderId }}</span>
                        </v-col>
                        <v-col cols="6" md="3" class="mt-2">
                          <span>จำนวนเงิน : </span>
                        </v-col>
                        <v-col cols="6" md="9" class="mt-2">
                          <span>{{ Number(items.receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                        </v-col>
                        <v-col cols="6" md="3" class="mt-2">
                          <span>วันและเวลาที่ทำรายการ : </span>
                        </v-col>
                        <v-col cols="6" md="9" class="mt-2">
                          <span>{{new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                        </v-col>
                        <v-col cols="6" md="3" class="mt-2">
                          <span>Ref : </span>
                        </v-col>
                        <v-col cols="6" md="9" class="mt-2">
                          <span>{{ items.receipt[0].orderIDRef }}</span>
                        </v-col>
                        <v-col cols="6" md="3" class="mt-2">
                          <span>ธนาคาร : </span>
                        </v-col>
                        <v-col cols="6" md="9" class="mt-2">
                          <span>{{ bankName }}</span>
                        </v-col>
                        <v-col cols="6" md="3" class="mt-2">
                          <span>รูปแบบการชำระเงิน :</span>
                        </v-col>
                        <v-col cols="6" md="9" class="mt-2">
                          <span>{{ items.receipt[0].payType }}</span>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
                    <v-col cols="12">
                      <span>คำสั่งซื้อสินค้าของคุณถูกยกเลิก</span>
                    </v-col>
                    <v-col cols="12" class="mt-5">
                      <span style="color: #27AB9C;"><b>&#8226; รายละเอียดใบสั่งซื้อที่ถูกยกเลิก</b></span>
                    </v-col>
                    <v-col cols="12" align="left">
                      <v-timeline dense>
                        <v-timeline-item v-for="(item,index) in items.approver_list" :key="index" fill-dot class="white--text mb-12" color="#27AB9C" small>
                          <template v-slot:icon>
                            <span>{{ index+1 }}</span>
                          </template>
                          <v-row no-gutters>
                            <v-col cols="12">
                              <span style="color: #27AB9C;"><b>ผู้อนุมัติ</b></span>
                            </v-col>
                            <v-col cols="12">
                              <span style="color: black;">สถานะ :</span>
                              <v-chip v-if="item.status === 'cancel'" class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip>
                              <v-chip v-else class="ma-2" color="#E6F5F3" small text-color="#27AB9C">อนุมัติ</v-chip>
                            </v-col>
                            <v-col cols="12">
                              <span style="color: black;">ผู้อนุมัติ : {{ item.approver_name }}({{ item.email }})</span>
                            </v-col>
                            <v-col v-if="item.time_approve === '-'" cols="12" class="mt-3">
                              <span style="color: black;">วันที่อนุมัติ : {{ item.time_approve }}</span>
                            </v-col>
                            <v-col v-else cols="12" class="mt-3">
                              <span style="color: black;">วันที่อนุมัติ : {{new Date(item.time_approve).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                            </v-col>
                          </v-row>
                        </v-timeline-item>
                      </v-timeline>
                    </v-col>
                  </v-row>
                  <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'ext_buyer'" no-gutters>
                    <v-col cols="12" class="mt-4">
                      <span class="mr-2">คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย</span>
                    </v-col>
                  </v-row>
                  <v-row v-else-if="items.transaction_status === 'Fail'" no-gutters>
                    <v-col cols="12" class="mt-4">
                      <span class="mr-2">คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง</span>
                      <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn>
                    </v-col>
                  </v-row>
                  <v-row v-else no-gutters>
                    <v-col v-if="items.status !== 'Credit'" cols="12" class="mt-4">
                      <span class="mr-2">คุณยังไม่ได้ทำการชำระเงิน กรุณาชำระเงินผ่านบริการทุกช่องทางของ <b>Thaidotcom Payment</b> โดยสามารถกดชำระเงินได้ที่นี่</span>
                      <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn>
                    </v-col>
                  </v-row>
                </v-row>
              </v-container>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </v-container>
    <!-- Desktop ipadPro -->
    <v-container v-else grid-list-xl>
      <v-row justify="center" class="my-4">
        <h2 style="font-weight: 700; font-size: 24px;">สถานะการสั่งซื้อของสินค้า</h2>
      </v-row>
      <v-row no-gutters class="mx-4">
        <v-col :cols="IpadSize || MobileSize ? 12 : 7" :class="IpadSize || MobileSize ? 'pb-0 mb-4' : 'pa-0 ma-0'">
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'"></span>
            <span  v-if="!MobileSize"> {{ items.payment_transaction }} | </span>
            <!-- <v-chip class="ma-2" :color="getColor(items.transaction_status)" small :text-color="getTextColor(items.transaction_status)">
              {{ getStatus(items.transaction_status) }}
            </v-chip> -->
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะรายการ : {{ items.order_status }}</span>
            <!-- <span v-if="items.required_invoice === 'ขอใบกำกับภาษี' ">
              <v-chip small class="ma-2" color="#E5EFFF" text-color="#1B5DD6">
                ขอใบกำกับภาษี
              </v-chip>
            </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-else>
              {{'-'}}
            </span> -->
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ใบกำกับภาษี : {{ items.required_invoice }}</span>
            <!-- <span v-if="items.required_invoice === 'ขอใบกำกับภาษี' ">
              <v-chip small class="ma-2" color="#E5EFFF" text-color="#1B5DD6">
                ขอใบกำกับภาษี
              </v-chip>
            </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-else>
              {{'-'}}
            </span> -->
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ : </span>
            <span>{{ items.buyer_name }}</span>
          </div>
          <!-- <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : </span>
            <span>{{ new Date(items.created_at).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) }}</span>
          </div> -->
          <!-- <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">จัดส่งโดย : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.business_type === '' ? '-' : items.business_type }}</span>
          </div> -->
          <div :class="IpadSize || MobileSize ? '' : IpadProSize ? 'mb-3' : 'mb-3'" v-if="mobilystTrackingNo">
            <!-- <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ดาวน์โหลด QR Code</span> -->
            <v-btn text color="#27AB9C" @click="getQrCode()"><v-icon class="pr-2">mdi-download-circle-outline</v-icon> <span style="text-decoration: underline;">ดาวน์โหลด QR Code</span></v-btn>
          </div>
          <!-- <div class="mb-3 ml-2" v-if="mobilystTrackingNo">
            <span>ดาวน์โหลด QR Code รูปแบบเต็ม</span>
            <v-btn outlined class="ml-2" x-small color="#27AB9C" @click="printBiglabel()"><v-icon small>mdi-download</v-icon></v-btn>
          </div> -->
        </v-col>
        <v-col :cols="MobileSize || IpadSize ? 12 : 5">
          <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : {{dateCreateOrderStep1}}</v-col>
          <!-- <v-row class="mb-1">
            <span class="ml-3 mt-3" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะผู้ขาย :</span>
            <v-col cols="7" v-if="itemStatus !== 'ดำเนินการแล้ว'">
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" style="color: #1AB759ว">{{ items.status }}</span>
            </v-col>
            <v-col cols="7" v-else>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" style="color: #1AB759ว">{{ items.status }}</span>
            </v-col>
          </v-row> -->
          <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่รับ : {{ dateCreateOrderStep4 === 'Invalid Date' ? '-' : dateCreateOrderStep4  }}</v-col>
          <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะผู้ซื้อ : {{ trackingText }}</v-col>
        </v-col>
        <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 12" class="mb-4">
          <v-row no-gutters>
            <v-col cols="12" md="7">
              <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</p>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.address_data }}</span>
            </v-col>
            <v-col cols="12" md="5" class="pl-3">
              <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งใบกำกับภาษี</p>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-if="items.invoice_address !== '' ">{{ items.invoice_address }}</span>
              <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" v-else>{{ '-' }}</span>
            </v-col>
          </v-row>
        </v-col>
        <!-- <v-col cols="12" align="left" class="mb-4">
          <span>รหัสการชำระเงิน : </span>
          <span>{{ items.payment_transaction }}</span>
          <span> | </span>
          <span><b>{{ trackingText }}</b></span>
        </v-col>
        <v-col v-if="dateCreateOrderStep4 !== 'Invalid Date'" cols="12" align="left" class="mb-4">
          <span>วันที่สินค้าทั้งหมดถึงผู้รับ : </span>
          <span>{{ dateCreateOrderStep4 }}</span>
        </v-col> -->
        <!-- <v-col cols="12"> -->
          <!-- status ชำระเงินแบบเครดิตเทอม (Desktop, IpadPro) -->
          <!-- <v-row no-gutters v-if="trackingStatus === 'Not Sent' && items.status === 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>ชำระเงินแบบเครดิตเทอม</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อรอการจัดส่ง</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col v-if="dateCreateOrderStep2 !== 'Invalid Date'" cols="3" align="center">
              <p class="fontSizeStepOrder">(วันที่ชำระเงินงวดสุดท้าย {{ dateCreateOrderStep2 }})</p>
            </v-col>
            <v-col v-else cols="3" align="center"></v-col>
          </v-row>
          <v-row no-gutters v-if="trackingStatus === 'Not Received' && items.status === 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="captionSku">ชำระเงินแบบเครดิตเทอม</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col v-if="dateCreateOrderStep2 !== 'Invalid Date'" cols="3" align="center">
              <p class="fontSizeStepOrder">(วันที่ชำระเงินงวดสุดท้าย {{ dateCreateOrderStep2 }})</p>
            </v-col>
            <v-col v-else cols="3" align="center"></v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-if="trackingStatus === 'Received' && items.status === 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>ชำระเงินแบบเครดิตเทอม</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>สินค้าทั้งหมดถึงผู้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col v-if="dateCreateOrderStep2 !== 'Invalid Date'" cols="3" align="center">
              <p class="fontSizeStepOrder">(วันที่ชำระเงินงวดสุดท้าย {{ dateCreateOrderStep2 }})</p>
            </v-col>
            <v-col v-else cols="3" align="center"></v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สินค้าถึง {{ dateCreateOrderStep4 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund' && items.status === 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="captionSku">ชำระเงินแบบเครดิตเทอม</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>ผู้ซื้อตรวจสอบและขอคืนสินค้า</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col v-if="dateCreateOrderStep2 !== 'Invalid Date'" cols="3" align="center">
              <p class="fontSizeStepOrder">(วันที่ชำระเงินงวดสุดท้าย {{ dateCreateOrderStep2 }})</p>
            </v-col>
            <v-col v-else cols="3" align="center"></v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่คืนสินค้า {{ dateCreateOrderStep4 }})</span>
            </v-col>
          </v-row> -->
          <!-- status ชำระเงินแบบปกติ (Desktop, IpadPro) -->
          <!-- <v-row no-gutters v-if="trackingStatus === 'Not Paid' && items.status !== 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper2.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive"><b>ที่ต้องชำระเงิน</b></span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontInactive">ที่ต้องจัดส่ง</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontInactive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'Not Sent' && items.status !== 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive">ที่ต้องจัดส่ง</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontInactive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'Sent'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'Not Received' && items.status !== 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper4.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'Received' && items.status !== 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>สินค้าทั้งหมดถึงผู้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สินค้าถึง {{ dateCreateOrderStep4 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund' && items.status !== 'Credit'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>ผู้ซื้อตรวจสอบและขอคืนสินค้า</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่คืนสินค้า {{ dateCreateOrderStep4 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="3" align="center">
              <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontActive">ที่ต้องจัดส่ง</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontInactive">ที่ต้องได้รับ</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="3" align="center">
              <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-else-if="trackingStatus === 'Cancel by approver'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper7.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '50px' : '80px'}">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="6" :style="{'padding-right': IpadProSize ? '40px' : '75px'}">
              <span>ยกเลิกคำสั่งซื้อ</span>
            </v-col>
            <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '10px' : '50px'}">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '10px' : '50px'}">
              <span class="fontSizeStepOrder">(วันที่ยกเลิก {{ dateCreateOrderCancel }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-if="trackingStatus === 'Cancel'" class="mb-5">
            <v-col cols="12" align="center">
              <v-img src="@/assets/stepper8.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
            </v-col>
            <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '50px' : '80px'}">
              <span>คำสั่งซื้อใหม่</span>
            </v-col>
            <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '40px' : '75px'}">
              <span>ยกเลิกคำสั่งซื้อ</span>
            </v-col>
            <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '10px' : '50px'}">
              <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
            </v-col>
            <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '10px' : '50px'}">
              <span class="fontSizeStepOrder">(วันที่ยกเลิก {{ dateCreateOrderCancel }})</span>
            </v-col>
          </v-row>
          <v-row no-gutters v-if="trackingStatus === ''" class="mb-5">
            <h1>ไม่พบข้อมูล</h1>
          </v-row> -->
        <!-- </v-col> -->
        <v-col cols="12">
          <v-card outlined>
            <!-- address, tracking Desktop ipadPro-->
            <!-- accept status = success, review Desktop ipadPro-->
            <!-- product table Desktop ipadPro-->
            <v-container grid-list-xs>
              <!-- <v-col cols="6" class="pt-0">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'"><b>รายการสั่งซื้อสินค้า</b></p>
              </v-col> -->
              <v-col cols="12" class="mb-2" v-for="(order, index) in items.data_list" :key="index">
                <!-- <v-card outlined> -->
                <v-row no-gutters class=" mb-2" type="flex" justify="start">
                  <v-col cols="6" class="">
                    <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'"><b>รายการสั่งซื้อสินค้า</b></p>
                    <span class="pl-0">
                      {{ Object.keys(order.product_list).length }} รายการสินค้า
                    </span>
                  </v-col>
                  <!-- <v-col cols="6">
                    <span class="pl-3">รหัสการสั่งซื้อ
                      <b class="left">{{order.order_number}}</b>
                    </span>
                  </v-col> -->
                  <!-- <v-col :cols="6" align="end">
                    <span class="pl-3" v-if="order.service_type === 'chilled'">
                      <v-chip small color="#E5EFFF" text-color="#1B5DD6">ส่งแบบควบคุมอุณหภูมิ {{ order.business_type }} Express</v-chip>
                    </span>
                    <span class="pl-3" v-if="order.service_type === 'normal'">
                      <v-chip small color="#E6F5F3" text-color="#27AB9C">ส่งแบบปกติ {{ order.business_type }} Express</v-chip>
                    </span>
                    <span class="pl-3" v-if="order.service_type === 'frozen'">
                      <v-chip small color="#E6F5F3" text-color="#26C6DA">ส่งแบบแช่แข็ง {{ order.business_type }} Express</v-chip>
                    </span>
                    <span class="pl-3" v-if="order.service_type === 'bulk'">
                      <v-chip small color="#FCF0DA" text-color="#E9A016">ส่งของขนาดใหญ่ {{ order.business_type }} Express</v-chip>
                    </span>
                  </v-col> -->
                  <v-col cols="6" align="end" class="" v-if="order.order_mobilyst_no">
                    <p>Tracking Number <b>{{order.order_mobilyst_no}}</b></p>
                    <v-btn v-if="order.order_mobilyst_no" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail mr-0'" text color="#27AB9C" @click="GoToMobily(order.url_tracking)" style="color: #27AB9C; text-decoration: underline;">
                      <v-img src="@/assets/icons/Vector.png" contain></v-img>
                      ติดตามสถานะขนส่ง
                    </v-btn>
                  </v-col>
                  <!-- <v-col align="end" cols="6" class="mt-2">
                    <v-btn v-if="order.order_mobilyst_no" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail mr-0'" text color="#27AB9C" @click="GoToMobily(order.url_tracking)" style="color: #27AB9C; text-decoration: underline;">
                      <v-img src="@/assets/icons/Vector.png" contain></v-img>
                      ติดตามสถานะขนส่ง
                    </v-btn>
                  </v-col> -->
                </v-row>
                <a-table :data-source="order.product_list" :rowKey="record => record.sku" :columns="headers" :pagination="{ pageSize: 100 }">
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" class="pr-0 py-1">
                        <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
                        <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                      </v-col>
                      <v-col cols="12" md="8">
                        <p class="mb-0 DetailsProductFront">{{ record.product_name }}</p>
                        <!-- <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
                        <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span> -->
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="revenue_default" slot-scope="text, record">
                    <span style="font-weight: 400; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <v-col cols="12">
                      <span>{{ record.quantity }}</span>
                    </v-col>
                  </template>
                  <template slot="revenue_default" slot-scope="text, record">
                    <span style="font-weight: 400; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                  <template slot="total_revenue_default" slot-scope="text, record">
                    <span style="font-weight: 400; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                  <template slot="revenue_amount " slot-scope="text, record">
                    <span style="font-weight: 400; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.revenue_amount ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                </a-table>
                <!-- btn comfirm accept, refund, contact seller Desktop ipadPro-->
                <!-- <v-row no-gutters v-if="checkAcceptProduct[index].status !== 'expired' && items.transaction_status !== 'Not Paid'" :class="checkAcceptProduct[index].status === 'waiting_review' || checkAcceptProduct[index].status === 'success' ? 'pa-0' : 'pa-3'" >
                  <v-col cols="12" md="12" align="right">
                    <span v-if="checkAcceptProduct[index].status === 'reject'" style="color: #BDBDBD">คำขอคืนสินค้าไม่ได้รับการอนุมัติจากร้านค้า</span>
                    <span v-if="checkAcceptProduct[index].status === 'refund'" style="color: #BDBDBD">คำขอคืนสินค้าได้รับการอนุมัติแล้ว</span>
                    <span v-if="checkAcceptProduct[index].status === 'waiting_refund'" style="color: #BDBDBD">ส่งคำขอคืนสินค้าแล้ว รอร้านค้าอนุมัติ</span>
                    <v-btn v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].refund === 'no'" outlined class="px-5 mr-2" color="#D1392B" @click="refundProductBuyer(order)"><b class="buttonFontSize">คืนสินค้า</b></v-btn>
                    <v-btn v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].status !== 'waiting_accept'" class="white--text px-5" color="#27AB9C" @click="acceptProduct(order)"><b class="buttonFontSize">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                  </v-col>
                </v-row>
                <v-row no-gutters v-if="checkAcceptProduct[index].status !== 'expired' && items.transaction_status === 'Not Paid' && items.status === 'Credit'" :class="checkAcceptProduct[index].status === 'waiting_review' || checkAcceptProduct[index].status === 'success' ? 'pa-0' : 'pa-3'" >
                  <v-col cols="12" md="12" align="right">
                    <span v-if="checkAcceptProduct[index].status === 'reject'" style="color: #BDBDBD">คำขอคืนสินค้าไม่ได้รับการอนุมัติจากร้านค้า</span>
                    <span v-if="checkAcceptProduct[index].status === 'refund'" style="color: #BDBDBD">คำขอคืนสินค้าได้รับการอนุมัติแล้ว</span>
                    <span v-if="checkAcceptProduct[index].status === 'waiting_refund'" style="color: #BDBDBD">ส่งคำขอคืนสินค้าแล้ว รอร้านค้าอนุมัติ</span>
                    <v-btn v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].refund === 'no'" outlined class="px-5 mr-2" color="#D1392B" @click="refundProductBuyer(order)"><b class="buttonFontSize">คืนสินค้า</b></v-btn>
                    <v-btn v-if="checkAcceptProduct[index].status !== 'waiting_review' && checkAcceptProduct[index].status !== 'reject' && checkAcceptProduct[index].status !== 'waiting_refund' && checkAcceptProduct[index].status !== 'refund' && checkAcceptProduct[index].status !== 'success'" :disabled="checkAcceptProduct[index].status !== 'waiting_accept'" class="white--text px-5" color="#27AB9C" @click="acceptProduct(order)"><b class="buttonFontSize">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                  </v-col>
                </v-row> -->
                <!-- <v-container grid-list-xs v-if="checkAcceptProduct[index].status === 'waiting_review'">
                  <v-row class="px-3">
                    <v-col cols="12" md="8">
                      <span class="pr-5">ขอบคุณที่ซื้อสินค้ากับเราหากท่านชื่นชอบกรุณาประเมินความพึงพอใจสินค้า<br/>และให้คำแนะนำเพื่อที่เราจะได้นำความคิดเห็นของท่านไปปรับปรุงและพัฒนาการบริการให้ดียิ่งขึ้น</span>
                    </v-col>
                    <v-col cols="12" md="4" align="right" class="pt-4">
                      <v-chip rounded class="white--text px-5" color="#52C41A" @click="openModalReviewProduct(order, index)"><b class="buttonFontSize">ประเมินความพึงพอใจสินค้า</b></v-chip>
                    </v-col>
                  </v-row>
                </v-container>
                <v-container grid-list-xs v-if="checkAcceptProduct[index].status === 'success'">
                  <v-row class="px-3">
                    <v-col cols="12" align="right" class="pt-4">
                      <v-chip rounded class="white--text px-5" color="#27AB9C" @click="openModalEditReviewProduct(order, index)"><b class="buttonFontSize">รายละเอียดการประเมินความพึงพอใจสินค้า</b></v-chip>
                    </v-col>
                  </v-row>
                </v-container> -->
                <!-- </v-card> -->
              </v-col>
            </v-container>
             <!-- price, total_price_vat, shipping Desktop ipadPro-->
            <v-container grid-list-xs>
              <v-row no-gutters class=" mb-2" type="flex" justify="start">
                  <v-col cols="6" class="">
                    <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'"><b>รายการสั่งซื้อสินค้า</b></p>
                    <!-- <span class="pl-0">
                      {{ Object.keys(order.product_list).length }} รายการสินค้า
                    </span> -->
                  </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" sm="9" md="10">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice">ส่วนลด :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice">ภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice">ราคารวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice">ค่าจัดส่ง :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="subheader fontSizeTotalPrice" style="font-weight: 700; font-size: 20px;">ราคารวมทั้งหมด :</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" sm="3" md="2">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice" style="font-weight: 700;">{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice" style="font-weight: 700;">{{ Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice" style="font-weight: 700;">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice" style="font-weight: 700;">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice" style="font-weight: 700;">{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span class="fontSizeTotalPrice" style="font-weight: 700; font-size: 20px;">{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="12" class="mt-2">
          <v-card outlined>
            <v-container grid-list-xs>
              <v-row no-gutters>
                <!-- transaction_status credit -->
                <v-col cols="12" class="mt-4 ml-2">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">รายละเอียดรายการสั่งซื้อ</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2 pl-2">
                  <p>วันที่เริ่มสัญญา : {{ formatDateToShow(items.start_date_contract) }}</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2">
                  <p>วันที่สิ้นสุดสัญญา : {{ formatDateToShow(items.end_date_contract) }}</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2">
                  <p>Pay Type : {{ items.pay_type}}</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2 pl-2">
                  <p>ส่วนลด : {{ items.discount_amount }}</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2">
                  <p v-if="items.remark !== ''">หมายเหตุ : {{items.remark}}</p>
                  <p v-else>หมายเหตุ : -</p>
                </v-col>
                <v-col cols="12" class="mt-4 ml-2">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;font-size: 16px">ข้อมูลสำหรับการออก Purchase Requisition และ Purchase Order</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2 pl-2">
                  <p>หมวดงบประมาณ : {{ items.type_budget }}</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="mt-2">
                  <p>หมวดตัดงบ : {{ items.budget_cut }}</p>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
        <!-- status payment Desktop ipadPro-->
        <v-col cols="12" class="mt-2">
          <v-card outlined>
            <v-container grid-list-xs>
              <v-row no-gutters>
                <!-- transaction_status credit -->
                <v-col v-if="items.status === 'Credit'" cols="12" class="mt-4">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E5EFFF" small text-color="#1B5DD6">ชำระเงินแบบเครดิตเทอม</v-chip></p>
                  <v-row dense v-if="items.transaction_status === 'Not Paid'" class="mb-4">
                    <v-btn class="white--text ml-1" color="#27AB9C" @click="gotoCrediterm(items.payment_transaction)">ไปหน้าชำระแบบเครดิตเทอม <v-icon small>mdi-chevron-right</v-icon></v-btn>
                  </v-row>
                </v-col>
                <v-col v-else-if="items.transaction_status === 'Success'" cols="12" class="mt-4">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสำเร็จ</v-chip></p>
                </v-col>
                <v-col v-else-if="items.transaction_status === 'Cancel'" cols="12" class="mt-4">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#F7D9D9" small text-color="#D1392B">ยกเลิกสินค้า</v-chip></p>
                </v-col>
                <v-col v-else-if="items.transaction_status === 'Fail'" cols="12" class="mt-4">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#FFECB3" small text-color="#FFB300">ชำระเงินไม่สำเร็จ</v-chip></p>
                </v-col>
                <v-col v-else cols="12" class="mt-4">
                  <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">สถานะการชำระเงิน | <v-chip class="ma-2" color="#E5EFFF" small text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip></p>
                </v-col>
                <v-row v-if="items.transaction_status === 'Success'" no-gutters>
                  <v-col cols="12" class="mt-4">
                    <span class="mr-2">คุณได้ชำระเงินเสร็จเรียบร้อยแล้ว ขอบคุณสำหรับการใช้บริการ</span>
                  </v-col>
                  <v-col cols="12" class="mt-5">
                    <span style="color: #27AB9C;"><b>&#8226; หลักฐานการชำระเงิน</b></span>
                  </v-col>
                </v-row>
                <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
                  <v-col cols="12" class="mt-4">
                    <span class="mr-2">คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย</span>
                  </v-col>
                </v-row>
                <v-row v-else-if="items.transaction_status === 'Fail' && statusPayment" no-gutters>
                  <v-col cols="12" class="mt-4">
                    <span class="mr-2">คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง</span>
                    <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn>
                  </v-col>
                </v-row>
                <v-row v-else no-gutters>
                  <v-col v-if="items.status !== 'Credit' && statusPayment" cols="12" class="mt-4">
                    <span class="mr-2">คุณยังไม่ได้ทำการชำระเงิน กรุณาชำระเงินผ่านบริการทุกช่องทางของ <b>Thaidotcom Payment</b> โดยสามารถกดชำระเงินได้ที่นี่</span>
                    <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn>
                  </v-col>
                </v-row>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="12" class="mt-2">
          <v-card outlined>
            <v-container grid-list-xs>
              <v-row no-gutters>
                <!-- transaction_status credit -->
                <v-col cols="12" class="mt-4">
                  <span style="font-size: 20px;"><b>หลักฐานการชำระเงิน</b></span>
                </v-col>
                <v-row v-if="items.transaction_status === 'Success'" no-gutters>
                  <v-col cols="12" class="mt-5">
                    <v-row no-gutters>
                      <v-col cols="12" md="6" sm="6">
                        <span>รหัสการชำระเงิน : {{ items.receipt[0].orderId }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>จำนวนเงิน : {{ Number(items.receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันและเวลาที่ทำรายการ : {{new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>Ref : {{ items.receipt[0].orderIDRef }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>ธนาคาร : {{ bankName }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>รูปแบบการชำระเงิน : {{ items.receipt[0].payType }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row v-else>
                  <v-col cols="12" class="mt-5 ml-1">
                    <span>-</span>
                  </v-col>
                </v-row>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
    <ModalRefundProductBuyer ref="ModalRefundProductBuyer" />
    <ModalReviewProduct ref="ModalReviewProduct" />
  </div>
</template>

<script>
import { Decode } from '@/services'
import { Table } from 'ant-design-vue'
// import detailBuyerMockData from '@/components/UserProfile/detailBuyer.json'
export default {
  components: {
    'a-table': Table,
    ModalRefundProductBuyer: () => import(/* webpackPrefetch: true */ '@/components/Modal/RefundProductBuyerModal'),
    ModalReviewProduct: () => import(/* webpackPrefetch: true */ '@/components/UserProfile/ModalReview/ReviewProduct'),
    OrderSummary: () => import(/* webpackPrefetch: true */ '@/components/Card/OrderSummary.vue')
  },
  data () {
    return {
      items: [],
      paymentNumber: {},
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingText: '',
      flashTrackingNo: '',
      flashTrackingData: {},
      receivedDate: '',
      sentDate: '',
      sentTime: '',
      step: 0,
      flashMCHID: process.env.VUE_APP_FLASH,
      checkAcceptProduct: [],
      mockupTracking: {},
      flashRoutes: [],
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: '',
      dateCreateOrderCancel: '',
      companyId: null,
      statusPayment: false,
      mobilystTrackingNo: '',
      itemStatus: ''
    }
  },
  async created () {
    this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    this.$EventBus.$on('SentGetReview', this.getItemProduct)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.paymentNumber = {
        payment_transaction_number: this.$route.query.orderNumber,
        role_user: 'purchaser',
        company_id: companyId.id
      }
      this.getFranchise()
      this.getItemProduct()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('SentGetReview')
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รหัสสินค้า',
          dataIndex: 'sku',
          scopedSlots: { customRender: 'sku' },
          key: 'sku',
          align: 'start',
          width: '15%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          align: 'start',
          scopedSlots: { customRender: 'productdetails' },
          width: '25%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'revenue_default',
          scopedSlots: { customRender: 'revenue_default' },
          key: 'revenue_default',
          align: 'start',
          width: '15%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'start',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'total_revenue_default',
          scopedSlots: { customRender: 'total_revenue_default' },
          key: 'total_revenue_default',
          align: 'start',
          width: '15%'
        },
        {
          title: 'Amount',
          dataIndex: 'revenue_amount ',
          scopedSlots: { customRender: 'revenue_amount ' },
          key: 'revenue_amount ',
          align: 'start',
          width: '15%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        }
      ]
      return headersMobile
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/orderDetailCompany?orderNumber=${this.paymentNumber.payment_transaction_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${this.paymentNumber.payment_transaction_number}` }).catch(() => {})
      }
    }
  },
  methods: {
    gotoCrediterm (val) {
      if (this.MobileSize) {
        this.$router.push({ path: `/companyListCreditTermMobile?order_number=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/companyListCreditTerm?order_number=${val}` }).catch(() => {})
      }
    },
    backtoPOBuyer () {
      if (this.MobileSize) {
        this.$router.push({ path: '/orderCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      }
    },
    GoToMobily (Track) {
      window.open(Track)
    },
    async getFranchise () {
      var Franchise = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      if (Franchise.can_use_function_in_company.payment !== undefined) {
        this.statusPayment = true
      } else {
        this.statusPayment = false
      }
    },
    async getItemProduct () {
      // console.log('detailBuyerMockData', detailBuyerMockData)
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsDetailOrderPurchaser', this.paymentNumber)
      var res = await this.$store.state.ModuleAdminManage.stateDetailOrderPurchaser
      if (res.message === 'Get detail order purchaser success') {
        this.$store.commit('closeLoader')
        this.items = res.data
        // console.log('tong', this.items)
        // mock data ถ้าข้อมูลหลังบ้านใช้ได้แล้วสามารถลบบรรทัดนี้ได้
        // this.items = detailBuyerMockData.data
        if (this.items.receipt.length !== 0) {
          if (this.items.receipt[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.items.receipt[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.items.receipt[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.items.receipt[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.items.receipt[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        // ต่อ Flash
        // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (this.items.order_mobilyst_no !== '') {
          // this.flashTrackingNo = this.items.flash_pno
          // var data = {
          //   mchId: this.flashMCHID,
          //   token: onedata.user.access_token,
          //   pno: this.flashTrackingNo
          // }
          // await this.$store.dispatch('actionTrackingOrderFlash', data)
          // var response = await this.$store.state.ModuleOrder.stateTrackingOrderFlash
          var response = {
            code: 1,
            data: {
              customaryPno: null,
              eSignature: null,
              origPno: 'TH014276DR4A',
              pno: 'TH014276DR4A',
              returnedPno: null,
              routes: null,
              state: 0,
              stateChangeAt: null,
              stateText: '',
              ticketPickupId: null
            },
            message: 'success'
          }
          // console.log('res in Flash', response)
          this.flashTrackingData = response.data
          if (this.flashTrackingData !== null) {
            this.mockupTracking = this.flashTrackingData
            if (this.flashTrackingData.routes === null) {
              this.step = 0
            } else {
              this.step = this.flashTrackingData.routes.length
            }
          } else {
            this.step = 0
          }
        } else {
          this.flashTrackingNo = ''
        }
        // ต่อ UPS
        // var val = {
        //   order_number: this.paymentNumber
        // }
        // await this.$store.dispatch('actionListOrder', val)
        // var responseUPS = await this.$store.state.ModuleOrder.stateListOrder
        // this.receivedDate = responseUPS.data.received_date
        // this.sentDate = responseUPS.data.sent_date
        // this.sentTime = responseUPS.data.sent_time
        // console.log('res in UPS', responseUPS)
        this.trackingStatus = this.items.tracking[0].status_tracking
        this.dateCreateOrderStep1 = new Date(this.items.tracking[0].time_step_1).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep2 = new Date(this.items.tracking[0].time_step_2).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep3 = new Date(this.items.tracking[0].time_step_3).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderCancel = new Date(this.items.tracking[0].time_cancel).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        // this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
        if (this.items.tracking.length !== 0) {
          if (this.items.tracking[0].status_tracking === 'Not Paid') {
            this.trackingText = 'ที่ต้องรอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Success') {
            this.trackingText = 'คำสั่งซื้อที่ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Sent') {
            this.trackingText = 'ที่ต้องจัดส่ง'
          } else if (this.items.tracking[0].status_tracking === 'Sent') {
            this.trackingText = 'จัดส่งแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Received') {
            this.trackingText = 'ได้รับสินค้าแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Received') {
            this.trackingText = 'ยังไม่ได้รับสินค้า'
          } else if (this.items.tracking[0].status_tracking === 'Cancel by approver') {
            this.trackingText = 'ยกเลิกโดยผู้อนุมัติ'
          } else if (this.items.tracking[0].status_tracking === 'Cancel') {
            this.trackingText = 'ยกเลิกคำสั่งซื้อ'
          }
        } else {
          this.trackingText = ''
        }
        this.CheckAcceptProduct()
        this.$store.commit('closeLoader')
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else {
        this.$store.commit('closeLoader')
        if (res.message !== 'ผู้ใช้งานนี้ถูกใช้งานอยู่') {
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: res.message
          })
        } else {
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: res.message
          })
          localStorage.removeItem('oneData')
          this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    // approveRefundProductSeller () {
    //   this.$refs.ModalRefundProductSeller.open(this.items, this.items.payment_transaction)
    // },
    refundProductBuyer (order) {
      // console.log('refundProductBuyer', order)
      this.$refs.ModalRefundProductBuyer.open(order, order.order_number, 'purchaser')
    },
    contactSeller () {
      // console.log('contact seller')
    },
    async CheckAcceptProduct () {
      var data = {
        payment_transaction_number: this.items.payment_transaction
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      this.checkAcceptProduct = res.data
      // console.log('checkAcceptProduct', this.checkAcceptProduct)
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    async acceptProduct (order) {
      // console.log('acceptProduct', order)
      // อาจจะมีการเพิ่มค่าที่ส่งไป api ถ้าหากมีแยกหลาย order
      var data = {
        payment_transaction_number: this.items.payment_transaction,
        order_number: order.order_number,
        status: 'accepted'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      // console.log('acceptProduct', res)
      if (res.message === 'Update status success.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว'
        })
        this.getItemProduct()
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    openModalReviewProduct (order, index) {
      const actions = 'create'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'purchaser')
    },
    openModalEditReviewProduct (order, index) {
      const actions = 'edit'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'purchaser')
    },
    async GoToPayment () {
      const PaymentID = {
        // payment_transaction_number: this.items.payment_transaction
        role_user: 'purchaser'
      }
      // console.log('payment_transaction_number', PaymentID)
      await this.$store.dispatch('ActionGetPaymentPageB2B', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPageB2B
      // console.log('respose paymenttttttttt', response)
      // await localStorage.setItem('PaymentData', Encode.encode(response))
      // this.$router.push(response.data.link_url)
      window.location.replace(response.data.link_url)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    }
  }
}
</script>

<style>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
</style>

<style lang="css" scoped>
/* .v-application .mb-12 {
    margin-bottom: 12px !important;
} */
::v-deep .ant-table-pagination {
  display: none;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.bgShippingUPS {
  background-color: #F3F5F7;
}
.fontActive {
  color: #27AB9C;
}
.fontInactive {
  color: #A6A6A6;
}
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 18px;
}
.fontSizeTotalPriceMobile {
  font-size: 16px;
}
.fontSizeAddressDetail {
  font-size: 16px;
}
.buttonFontSize {
  font-size: 14px;
  font-weight: normal;
}
.captionSku {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
.fontSizeDetail {
  font-size: 14px;
}
.fontSizeDetailMobile {
  font-size: 12px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>
