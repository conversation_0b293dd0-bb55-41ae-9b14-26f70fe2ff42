<template>
  <div class="ma-5">
    <!-- <v-container> -->
      <v-row justify="start">
        <h1 class="mt-4">แก้ไขข้อมูลผู้ใช้งาน</h1>
      </v-row>
      <v-card
      class="mt-2"
      outlined
      width="100%"
      >
        <v-form
        ref="form"
        :lazy-validation="lazy"
        >
          <v-row justify="center" align="center" dense class="mt-2">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>ชื่อ (ภาษาไทย)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="dataUser.firstnameTH"
              :rules="Rules.firstnameTH"
              outlined
              dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>นามสกุล (ภาษาไทย)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="dataUser.lastnameTH"
              :rules="Rules.lastnameTH"
              outlined
              dense
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" align="center" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>ชื่อ (ภาษาอังกฤษ)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="dataUser.firstnameEN"
              :rules="Rules.firstnameEN"
              outlined
              dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>นามสกุล (ภาษาอังกฤษ)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="dataUser.lastnameEN"
              :rules="Rules.lastnameEN"
              outlined
              dense
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" align="center" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>อีเมลผู้ใช้งาน<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="dataUser.email"
              outlined
              :rules="Rules.emailRules"
              placeholder="<EMAIL>"
              dense
              disabled
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>หมายเลขโทรศัพท์<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="dataUser.mobile"
              v-mask="'###-###-####'"
              :maxlength="maxPhone"
              :rules="Rules.tel"
              outlined
              dense
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" align="start" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12" class="mt-4">
              <p>สิทธิ์การเข้าใช้งาน<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12">
              <v-checkbox
              v-model="dataUser.admin"
              v-if="dataUser.admin === true"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้ดูแลระบบ"
              disabled
              ></v-checkbox>
              <v-checkbox
              v-model="dataUser.assistant"
              v-if="dataUser.assistant === true"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้ช่วยผู้ดูแลระบบ"
              ></v-checkbox>
              <v-checkbox
              v-model="dataUser.assistant"
              v-else
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้ช่วยผู้ดูแลระบบ"
              ></v-checkbox>
              <v-checkbox
              v-model="dataUser.appover"
              v-if="dataUser.appover === true"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้อนุมัติ"
              ></v-checkbox>
              <v-checkbox
              v-model="dataUser.appover"
              v-else
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้อนุมัติ"
              ></v-checkbox>
              <v-checkbox
              v-model="dataUser.buyer"
              v-if="dataUser.buyer === true"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้สั่งซื้อ"
              ></v-checkbox>
              <v-checkbox
              v-model="dataUser.buyer"
              v-else
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้สั่งซื้อ"
              ></v-checkbox>
            </v-col>
            <v-col cols="5" md="5" sm="2" xs="12" v-if="dataUser.appover === true" class="mt-16">
              <v-row dense>
                <v-col cols="12" md="5" sm="2" xs="12" class="mt-4">
                  <p>จำนวนวันรอการอนุมัติ<span style="color: red;"> *</span></p>
                </v-col>
                <v-col cols="12" md="7" sm="3" xs="12" class="mt-2">
                  <v-text-field
                  v-model="dataUser.appoverDate"
                  outlined
                  dense
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="5" sm="2" xs="12" v-else>
            </v-col>
          </v-row>
        </v-form>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined color="success" dense @click="back()">ย้อนกลับ</v-btn>
          <v-btn color="success" @click="submit()" dense>บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    <!-- </v-container> -->
  </div>
</template>

<script>
import Vue from 'vue'
import VueMask from 'v-mask'
import { Decode } from '@/services'
Vue.use(VueMask)
export default {
  data () {
    return {
      dataUser: [],
      lazy: false,
      Rules: {
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        firstnameTH: [
          v => !!v || 'กรุณากรอก ชื่อ (ภาษาไทย)'
        ],
        lastnameTH: [
          v => !!v || 'กรุณากรอก นามสกุล (ภาษาไทย)'
        ],
        firstnameEN: [
          v => !!v || 'กรุณากรอก ชื่อ (ภาษาอังกฤษ)'
        ],
        lastnameEN: [
          v => !!v || 'กรุณากรอก นามสกุล (ภาษาอังกฤษ)'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ]
      },
      maxPhone: 12
    }
  },
  created () {
    this.dataUser = JSON.parse(Decode.decode(localStorage.getItem('DetailUser')))
  },
  methods: {
    back () {
      this.$router.push('/detailuser').catch(() => {})
    }
  }
}
</script>
