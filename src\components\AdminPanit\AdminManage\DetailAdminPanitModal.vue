<template>
  <div class="text-center">
    <v-dialog v-model="openModalDetailAdmin" width="732" persistent>
      <v-card min-height="558px">
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>กำหนดสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-row class="pt-2 px-5">
            <v-col cols="8" md="9" sm="9">
              <v-img class="float-left" src="@/assets/icons/Buyer.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 16px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดสิทธิ์การใช้งาน</v-card-title>
              <v-card-title style="font-weight: bold; font-size: 14px; line-height: 16px; color: #333333; word-break: break-all;" class="py-2 px-2 mr-0" v-else>รายละเอียดสิทธิ์การใช้งาน</v-card-title>
            </v-col>
            <v-col cols="4" md="3" sm="3" class="py-6" align="end">
              <v-icon @click="editAdmin()" left dense color="#27AB9C" :disabled="isSuperAdmin === false">mdi-pencil-outline</v-icon>
              <v-icon @click="confirmDelete()" dense color="#27AB9C" :disabled="isSuperAdmin === false">mdi-delete-outline</v-icon>
            </v-col>
          </v-row>
          <v-row justify="center" align-content="center" class="px-5" v-if="adminData !== ''">
            <v-col cols="12">
              <v-card outlined style="background-color: #FFFFFF;" v-if="!MobileSize">
                <div class="d-flex flex-no-wrap ">
                  <v-avatar class="ma-4" size="172" tile>
                    <v-img :src="adminData.img_path" contain style="border-radius: 8px;" v-if="adminData.img_path !== null "></v-img>
                    <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                  </v-avatar>
                  <div class="ma-4">
                    <p>ชื่อ-สกุล : <b>{{ name }}</b></p>
                    <p>อีเมล : <b>{{ adminData.email }}</b></p>
                    <p>เบอร์โทรศัพท์ : <b>{{ adminData.phone }}</b></p>
                    <p>Username : <b>{{ adminData.username_oneid }}</b></p>
                    <p class="mb-0">สิทธิ์การเข้าใช้งาน : <b>{{ adminData.admin_type === 'super_admin_platform' ? 'Super Admin Platform' : 'Admin Platform' }}</b></p>
                  </div>
                </div>
              </v-card>
              <v-card outlined style="background-color: #FFFFFF;" v-else>
                <v-card-text>
                  <div class="d-flex flex-no-wrap ">
                    <v-avatar class="ma-4" size="60" tile>
                      <v-img :src="adminData.img_path" contain style="border-radius: 8px;" v-if="adminData.img_path !== null "></v-img>
                      <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                    </v-avatar>
                    <div class="ma-4" style="font-size: 12px;">
                      <p>ชื่อ-สกุล : <br/><b>{{ name }}</b></p>
                      <p>อีเมล : <br/><b>{{ adminData.email }}</b></p>
                      <p>เบอร์โทรศัพท์ : <br/><b>{{ adminData.phone }}</b></p>
                      <p>Username : <br/><b>{{ adminData.username_oneid }}</b></p>
                      <p class="mb-0">สิทธิ์การเข้าใช้งาน : <br/><b>{{ adminData.admin_type === 'super_admin_platform' ? 'Super Admin Platform' : 'Admin Platform' }}</b></p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
    <EditAdminModal ref="EditAdminModal" />
  </div>
</template>

<script>
export default {
  components: {
    EditAdminModal: () => import(/* webpackPrefetch: true */ '@/components/AdminPanit/AdminManage/EditAdminPanitModal')
  },
  data () {
    return {
      openModalDetailAdmin: false,
      userIdAdmin: '',
      adminData: '',
      name: '',
      isSuperAdmin: null
    }
  },
  watch: {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    open (data, statusSuperAdmin) {
      this.adminData = ''
      this.openModalDetailAdmin = true
      this.userIdAdmin = data.user_id_admin
      this.isSuperAdmin = statusSuperAdmin
      this.getDetailAdmin()
    },
    cancel () {
      this.openModalDetailAdmin = false
    },
    async getDetailAdmin () {
      this.$store.commit('openLoader')
      var sendData = {
        user_id_admin: this.userIdAdmin
      }
      await this.$store.dispatch('actionsDetailAdminPlatform', sendData)
      var response = await this.$store.state.ModuleAdminManage.stateDetailAdminPlatform
      // console.log('getDetailAdmin', response)
      if (response.message === 'Get detail user admin success.') {
        this.$store.commit('closeLoader')
        this.adminData = response.data
        this.name = this.adminData.first_name_th + ' ' + this.adminData.last_name_th
      } else if (response.message === 'Not found this user.') {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ icon: 'warning', text: 'ไม่พบข้อมูลผู้ใช้ที่จะเพิ่มเป็นแอดมิน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is not super admin.') {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดูรายละเอียดได้เนื่องจากคุณไม่มีสิทธิ์ Super Admin Platform', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    editAdmin () {
      const data = this.adminData
      this.openModalDetailAdmin = false
      this.$refs.EditAdminModal.open(data)
    },
    confirmDelete () {
      const msgText = 'คุณได้ทำการลบสิทธิ์การใช้งาน ' + this.name + ' คุณต้องการทำรายการนี้ใช่ หรือไม่'
      this.$swal.fire({
        icon: 'warning',
        text: msgText,
        showCancelButton: true,
        confirmButtonText: 'ตกลง',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.deleteAdmin()
        } else if (result.dismiss === this.$swal.DismissReason.cancel) {
        }
      }).catch(() => {
      })
    },
    async deleteAdmin () {
      this.$store.commit('openLoader')
      var sendData = {
        user_id_admin: this.userIdAdmin
      }
      // console.log('getDetailAdmin', sendData)
      await this.$store.dispatch('actionsDeleteAdminPlatform', sendData)
      var response = await this.$store.state.ModuleAdminManage.stateDeleteAdminPlatform
      if (response.message === 'Delete user data success.') {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
        this.$EventBus.$emit('deleteAdminPanitSuccess')
      } else if (response.message === 'Not found this user.') {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ icon: 'warning', text: 'ไม่พบข้อมูลผู้ใช้ที่จะเพิ่มเป็นแอดมิน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is not super admin.') {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดูรายละเอียดได้เนื่องจากคุณไม่มีสิทธิ์ Super Admin Platform', showConfirmButton: false, timer: 2500 })
      } else if (response.message === 'Unable to delete your permission role.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 2500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.openModalDetailAdmin = false
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
