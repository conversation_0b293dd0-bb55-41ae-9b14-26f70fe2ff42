<template>
  <v-container width="100%" height="100%" style="background: #FFFFFF; border: 0px solid; border-radius: 8px;">
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-row no-gutters class="d-flex" align="center">
        <!-- Title -->
        <v-col cols="12" v-if="!MobileSize" class="pa-0 pt-2" align="end">
          <v-row dense justify="end">
            <v-btn v-if="!IpadSize" rounded color="primary" class="mb-2 mr-2" @click="ExportNewShop()">Export New Shop</v-btn>
            <v-btn v-else rounded color="primary" class="mb-2 mr-2" @click="ExportNewShop()"><v-icon>mdi-microsoft-excel</v-icon> New Shop</v-btn>
            <v-btn v-if="!IpadSize" rounded color="primary" class="mb-2 mr-2" @click="ExportOtop()">Export Otop</v-btn>
            <v-btn v-else rounded color="primary" class="mb-2 mr-2" @click="ExportOtop()"><v-icon>mdi-microsoft-excel</v-icon> Otop</v-btn>
            <v-btn v-if="!IpadSize" rounded color="primary" class="mb-2 mr-2" @click="ExportUsesCoupon()">Export Used Coupon</v-btn>
            <v-btn v-else rounded color="primary" class="mb-2 mr-2" @click="ExportUsesCoupon()"><v-icon>mdi-microsoft-excel</v-icon> Used Coupon</v-btn>
            <v-btn v-if="!IpadSize" rounded color="primary" class="mb-2 mr-2" @click="ExportSumTransaction()">Export Transaction</v-btn>
            <v-btn v-else rounded color="primary" class="mb-2 mr-2" @click="ExportSumTransaction()"><v-icon>mdi-microsoft-excel</v-icon> Transaction</v-btn>
            <v-btn v-if="!IpadSize" rounded color="primary" outlined @click="ExportSumTransactionJV()">Export Transaction JV</v-btn>
            <v-btn v-else rounded color="primary" outlined @click="ExportSumTransactionJV()"><v-icon>mdi-microsoft-excel</v-icon> Transaction JV</v-btn>
          </v-row>
        </v-col>
        <v-col v-if="IpadSize || IpadProSize" cols="2">
          <div class="text-container">
            <v-card-title class="pl-0 pt-0 mb-12" style="font-weight: 700; font-size: 24px; line-height: 32px;">แดชบอร์ดแอดมิน</v-card-title>
          </div>
        </v-col>
        <v-col v-else cols="2">
          <div class="text-container">
            <v-card-title class="pl-0" style="font-weight: 700; font-size: 24px; line-height: 32px;">แดชบอร์ดแอดมิน</v-card-title>
          </div>
        </v-col>
        <!-- Dropdowns IpadSize -->
        <v-col v-if="IpadSize || IpadProSize" cols="10" align="end">
          <v-row>
            <v-col cols="12" class="mt-1">
              <span style="font-size: 16px; font-weight: 500;">แสดงผล :</span>
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedDropdown || 'รายปี' }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
                  <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
                  <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
                </v-list>
              </v-menu>
            </v-col>

            <!-- Conditional year dropdown -->
            <v-col v-if="!showDatePicker" cols="12">
              <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
              <v-menu v-if="showYearDropdown" offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
                </v-list>
              </v-menu>

              <!-- Conditional month dropdown -->
              <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
              <v-menu v-if="showMonthDropdown" offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedMonth || 'เลือกเดือน' }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item v-for="(month, index) in months" :key="index" >
                    <span @click="onMonthSelected(month)" style="cursor: pointer;">
                    {{ month.text }}
                    </span>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>

            <!-- Conditional date picker -->
            <v-col cols="12">
              <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
              <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent :return-value.sync="date" width="480px">
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="dateRangeText"
                    placeholder="วว/ดด/ปปปป"
                    dense
                    rounded
                    readonly
                    style="border: 1px solid #EBEBEB; border-radius: 8px;"
                    v-bind="attrs"
                    v-on="on"
                    class="d-inline-block ml-2 custom-text-field"
                  >
                  <v-spacer></v-spacer>
                    <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  style="font-size:29px !important; height: 480px !important"
                  v-model="dates"
                  scrollable
                  reactive
                  locale="Th-th"
                  range
                  no-title
                  full-width
                  :min="minDate"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                <v-row>
                  <v-col align="end">
                    <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                    <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                  </v-col>
                </v-row>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
        </v-col>
        <!-- Dropdowns IpadPro and Desktop -->
        <v-col v-else cols="10" align="end">
          <span style="font-size: 16px; font-weight: 500;">แสดงผล :</span>
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedDropdown || 'รายปี' }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
              <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
              <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
            </v-list>
          </v-menu>

          <!-- Conditional year dropdown -->
          <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
          <v-menu v-if="showYearDropdown" offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
            </v-list>
          </v-menu>

          <!-- Conditional month dropdown -->
          <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
          <v-menu v-if="showMonthDropdown" offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedMonth || 'เลือกเดือน' }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item v-for="(month, index) in months" :key="index" >
                <span @click="onMonthSelected(month)" style="cursor: pointer;">
                {{ month.text }}
                </span>
              </v-list-item>
            </v-list>
          </v-menu>

          <!-- Conditional date picker -->
          <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
          <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent :return-value.sync="date" width="480px">
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="dateRangeText"
                placeholder="วว/ดด/ปปปป"
                dense
                rounded
                readonly
                style="border: 1px solid #EBEBEB; border-radius: 8px;"
                v-bind="attrs"
                v-on="on"
                class="d-inline-block ml-2 custom-text-field"
              >
              <v-spacer></v-spacer>
                <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              style="font-size:29px !important; height: 480px !important"
              v-model="dates"
              scrollable
              reactive
              locale="Th-th"
              range
              no-title
              full-width
              :min="minDate"
              :max="
                new Date(
                  Date.now() - new Date().getTimezoneOffset() * 60000
                )
                  .toISOString()
                  .substr(0, 10)
              "
            >
            <v-row>
              <v-col align="end">
                <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
              </v-col>
            </v-row>
            </v-date-picker>
          </v-dialog>
        </v-col>
      </v-row>
    </v-card>
    <!-- ข้อมูลรายได้ header & shop selection -->
    <v-card class="mb-4" style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="6" class="mt-4">
          <v-avatar rounded size="24">
            <v-img contain :src="passiveIncomeIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">ข้อมูลรายได้</span>
        </v-col>
        <v-col cols="6" class="mt-4 d-flex">
          <v-autocomplete
            v-model="selectedItemShop"
            :items="itemsShop"
            item-text="name_th"
            item-value="id"
            label="เลือกร้านค้า"
            height="22px"
            dense
            outlined
            :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
            @change="handleSelectChange"
          ></v-autocomplete>
        </v-col>
      </v-row>
    </v-card>
    <!-- กราฟ -->
    <v-card class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-layout align-center justify-center>
        <v-flex>
          <v-card>
            <v-card-title>
              <v-row>
                <v-col cols="6">
                  <v-avatar rounded size="20">
                    <v-img contain :src="statisticsIconPath"></v-img>
                  </v-avatar>
                  <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">กราฟแสดงรายได้</span>
                </v-col>
                <v-col cols="6" align="end">
                  <v-avatar rounded size="27" class="mt-2">
                    <v-img contain :src="graphLineIconPath"></v-img>
                  </v-avatar>
                  <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">รายได้</span>
                </v-col>
              </v-row>
            </v-card-title>
            <v-card-text>
              <apexchart  height="400" type="line" :options="chartOptions" :series="chartSeries"></apexchart>
            </v-card-text>
          </v-card>
        </v-flex>
      </v-layout>
    </v-card>
    <v-card class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-layout align-center justify-center>
        <v-flex>
          <v-card>
            <v-card-title>
              <v-row>
                <v-col cols="8">
                  <v-avatar rounded size="20">
                    <v-img contain :src="statisticsIconPath"></v-img>
                  </v-avatar>
                  <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">กราฟรายการ transaction</span>
                </v-col>
                <v-col cols="4" align="end">
                  <v-avatar rounded size="27" class="mt-2">
                    <v-img contain :src="graphLineIconPath"></v-img>
                  </v-avatar>
                  <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">รายการ transaction</span>
                </v-col>
              </v-row>
            </v-card-title>
            <v-card-text>
              <apexchart  height="400" type="line" :options="chartOptions2" :series="chartSeries2"></apexchart>
            </v-card-text>
          </v-card>
        </v-flex>
      </v-layout>
    </v-card>
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-row>
        <!-- ยอดขาย -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon2"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalSale).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ยอดขายทั้งหมด <br/> (บาท)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- จำนวนรายการสั่งซื้อทั้งหมด -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon3"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ totalOrder ? Number(totalOrder).toLocaleString(undefined) : '0' }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">จำนวนรายการสั่งซื้อทั้งหมด <br/> (รายการ)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- ค่าธรรมเนียม -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon4"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalFee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ค่าธรรมเนียมทั้งหมด <br/> (บาท)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- ยอดเงินสุทธิ -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon6"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalNetAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ยอดเงินสุทธิทั้งหมด <br/> (บาท)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <!-- รายการสั่งซื้อสินค้าทั้งหมด -->
    <v-card class="mb-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="dataModelIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">รายการสั่งซื้อสินค้าทั้งหมด ({{ totalOrder }} รายการ)</span>
        </v-col>
        <v-col v-if="saleOrder.length !== 0" cols="3" class="mt-5 d-flex justify-end">
          <v-btn @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'orderlist', 'รายการสั่งซื้อสินค้าทั้งหมด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- Data table -->
    <v-card elevation="0">
      <v-data-table
        v-if="saleOrder.length !== 0"
        :headers="headers"
        :items="saleOrder"
        :items-per-page="5"
        class="elevation-1"
        item-key="i"
        no-data-text="ไม่มีรายการสั่งซื้อสินค้า"
        no-results-text="ไม่พบรายการสั่งซื้อสินค้า"
      >
        <template v-slot:[`item.paid_datetime`]="{ item }">
          {{ new Date(item.paid_datetime).toLocaleDateString('th-TH', {
            timeZone: "UTC",
            year: 'numeric',
            month: 'long',
            day: 'numeric' })
          }}
        </template>
        <template v-slot:[`item.product_list`]="{ item }">
          <v-row dense justify="center">
            <v-btn
              x-small
              outlined
              @click="openDialog(item)"
              style="border: none; width:100%;"
              height="100%"
              class="pt-4 pb-4"
            >
              <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
            </v-btn>
          </v-row>
        </template>
        <template v-slot:[`item.TxnAmount`]="{ item }">
          <span>{{ Number(item.TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.vat_price_payment`]="{ item }">
          <span>{{ Number(item.vat_price_payment).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_affiliate`]="{ item }">
          <span>{{ Number(item.total_affiliate).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.discount_coupon`]="{ item }">
          <span>{{ Number(item.discount_coupon).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.discount_ngc`]="{ item }">
          <span>{{ Number(item.discount_ngc).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_gp`]="{ item }">
          <span>{{ Number(item.total_gp).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_shipping`]="{ item }">
          <span>{{ Number(item.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_shop`]="{ item }">
          <span>{{ Number(item.total_shop).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
      </v-data-table>
      <v-card v-else elevation="0">
        <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
      </v-card>
    </v-card>
    <br>
    <!-- TOP 10 สินค้าขายดี header -->
    <v-card style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6 mb-4">
          <v-avatar rounded size="24">
            <v-img contain :src="uniqueIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 สินค้าขายดี</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="bestSeller.length !== 0" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestsoldproduct', 'TOP_10_สินค้าขายดี')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- TOP 10 สินค้าขายดี body -->
    <v-card v-if="bestSeller.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in bestSeller.slice(0, 5)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #38C9B9;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <!-- 1 -->
                    <!-- Conditionally render specific avatar for index 0 -->
                    <v-avatar v-if="index === 0" rounded size="43">
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            1
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            2
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            3
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- For other indices, render a different avatar -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">
                        {{ Number( item.sold ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showBestSeller : true">
          <v-row no-gutters>
            <v-col v-for="(item, index) in bestSeller.slice(5, 10)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #38C9B9;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">
                        {{ Number( item.sold ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    <!-- show more -->
    <v-row v-if="MobileSize || IpadSize">
      <v-col align="center">
        <v-card-actions v-if="bestSeller.length > 5">
          <v-divider></v-divider>
          <v-btn
            color="blue"
            text
            @click="showBestSeller = !showBestSeller"
          >
            {{ showBestSeller ? 'ย่อ' : 'เพิ่มเติม' }}
          </v-btn>

          <v-btn
            icon
            @click="showBestSeller = !showBestSeller"
          >
            <v-icon>{{ showBestSeller ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
          <v-divider></v-divider>
        </v-card-actions>
      </v-col>
    </v-row>
    <!-- /show more -->
    <!-- TOP 10 มูลค่าการสั่งซื้อ header -->
    <v-card style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="commissionIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 มูลค่าการสั่งซื้อ</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="orderValue.length !== 0" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestrevenuproduct', 'TOP_10_มูลค่าการสั่งซื้อ')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- TOP 10 มูลค่าการสั่งซื้อ body -->
    <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row>
            <v-col v-for="(item, index) in orderValue.slice(0, 5)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar v-if="index === 0" rounded size="43">
                      <!-- Content for index 0 -->
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            1
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            2
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            3
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- For other indices, render a different avatar -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">
                        {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showOrderValue : true">
          <v-row>
            <v-col v-for="(item, index) in orderValue.slice(5, 10)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">
                        {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    <!-- show more -->
    <v-row v-if="MobileSize || IpadSize">
      <v-col align="center">
        <v-card-actions v-if="orderValue.length > 5">
          <v-divider></v-divider>
          <v-btn
            color="blue"
            text
            @click="showOrderValue = !showOrderValue"
          >
            {{ showOrderValue ? 'ย่อ' : 'เพิ่มเติม' }}
          </v-btn>

          <v-btn
            icon
            @click="showOrderValue = !showOrderValue"
          >
            <v-icon>{{ showOrderValue ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
          <v-divider></v-divider>
        </v-card-actions>
      </v-col>
    </v-row>
    <!-- /show more -->
    <!-- TOP 10 ยอดขายร้านค้า header -->
    <v-card style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="rankingIconPathconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 ยอดขายร้านค้า</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="orderValue.length !== 0" @click="getExportDashboardSeller(shopID)" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- TOP 10 ยอดขายร้านค้า body -->
    <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row>
            <v-col v-for="(item, index) in topSeller.slice(0, 5)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar v-if="index === 0" rounded size="43">
                      <!-- Content for index 0 -->
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            1
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            2
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            3
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- For other indices, render a different avatar -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.media_path === null || item.media_path === ''" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.name_th }}</span>
                    <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">
                        {{ item.revenue }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showOrderValue : true">
          <v-row>
            <v-col v-for="(item, index) in topSeller.slice(5, 10)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.media_path === null || item.media_path === ''" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.name_th }}</span>
                    <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">
                        {{ item.revenue }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    <!-- /show more -->
    <!-- TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด header -->
    <v-card style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="bestCustomerIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="orderValue.length !== 0" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'topbuyers', 'TOP_10_ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด body -->
    <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <!-- Top three -->
      <v-row justify="center">
        <v-col cols="3" md="3" sm="4" xs="4" class="d-flex justify-end mb-2 mt-3">
          <v-card v-if="topBuyers.length >= 2" height="164px" width="150px" elevation="0" style="border: 0px solid #1B5DD6;">
            <v-row>
              <v-col align="center">
                <v-avatar rounded size="43" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                  <v-img contain :src="silverMedalIconPath">
                    <span
                      class="display-1 font-weight-bold"
                      style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                    >
                      <font style="font-size: 20px;">
                        2
                      </font>
                    </span>
                  </v-img>
                </v-avatar>
                <v-avatar v-if="topBuyers[1].user_image === null || topBuyers[1].user_image ===  'not found image'" rounded size="78" color="#FFF">
                  <!-- <span>No image</span> -->
                  <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                </v-avatar>
                <v-avatar v-else size="78" color="#FFF">
                  <v-img contain :src="topBuyers[1].user_image"></v-img>
                </v-avatar>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-0 pb-0">
                <span class="one-lines">{{ topBuyers[1].buyer_name }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-1">
                <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                  <span class="vchipFontSize" style="color: #1B5DD6">
                    {{ Number(topBuyers[1].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                  </span>
                </v-chip>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3" md="3" sm="4" xs="4" class="d-flex justify-center mb-2">
          <v-card v-if="topBuyers.length >= 1" height="176px" width="150px" elevation="0" style="border: 0px solid #1B5DD6;">
            <v-row>
              <v-col align="center">
                <v-avatar rounded size="43" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                  <v-img contain :src="goldMedalIconPath">
                    <span
                      class="display-1 font-weight-bold"
                      style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                    >
                      <font style="font-size: 20px;">
                        1
                      </font>
                    </span>
                  </v-img>
                </v-avatar>
                <v-avatar v-if="topBuyers[0].user_image === null || topBuyers[0].user_image === 'not found image'" rounded size="90" color="#FFF">
                  <!-- <span>No image</span> -->
                  <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                </v-avatar>
                <v-avatar v-else size="90" color="#FFF">
                  <v-img contain :src="topBuyers[0].user_image"></v-img>
                </v-avatar>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-0 pb-0">
                <span class="one-lines">{{ topBuyers[0].buyer_name }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-1">
                <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                  <span class="vchipFontSize" style="color: #1B5DD6">
                    {{ Number(topBuyers[0].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                  </span>
                </v-chip>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3" md="3" sm="4" xs="4" class="d-flex mb-2 mt-3">
          <v-card v-if="topBuyers.length >= 3" height="164px" width="150px" elevation="0" style="border: 0px solid #1B5DD6;">
            <v-row>
              <v-col align="center">
                <v-avatar rounded size="43" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                  <v-img contain :src="bronzeMedalIconPath">
                    <span
                      class="display-1 font-weight-bold"
                      style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                    >
                      <font style="font-size: 20px;">
                        3
                      </font>
                    </span>
                  </v-img>
                </v-avatar>
                <v-avatar v-if="topBuyers[2].user_image === null || topBuyers[2].user_image === 'not found image'" rounded size="78" color="#FFF">
                  <!-- <span>No image</span> -->
                  <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                </v-avatar>
                <v-avatar v-else size="78" color="#FFF">
                  <v-img contain :src="topBuyers[2].user_image"></v-img>
                </v-avatar>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-0 pb-0">
                <span class="one-lines">{{ topBuyers[2].buyer_name }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-1">
                <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                  <span class="vchipFontSize" style="color: #1B5DD6">
                    {{ Number(topBuyers[2].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                  </span>
                </v-chip>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row>
            <v-col v-for="(item, index) in topBuyers.slice(3, 7)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #1B5DD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 4 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.user_image === null || item.user_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else size="44" color="#FFF">
                      <v-img contain :src="item.user_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="one-lines">{{ item.buyer_name }}</span>
                    <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #1B5DD6">
                        {{ Number(item.sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showTopBuyers : true">
          <v-row>
            <v-col v-for="(item, index) in topBuyers.slice(7, 10)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #1B5DD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 8 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.user_image === null || item.user_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else size="44" color="#FFF">
                      <v-img contain :src="item.user_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="one-lines">{{ item.buyer_name }}</span>
                    <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #1B5DD6">
                        {{ Number(item.sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    <!-- show more -->
    <v-row v-if="MobileSize || IpadSize">
      <v-col align="center">
        <v-card-actions v-if="topBuyers.length > 5">
          <v-divider></v-divider>
          <v-btn
            color="blue"
            text
            @click="showTopBuyers = !showTopBuyers"
          >
            {{ showTopBuyers ? 'ย่อ' : 'เพิ่มเติม' }}
          </v-btn>

          <v-btn
            icon
            @click="showTopBuyers = !showTopBuyers"
          >
            <v-icon>{{ showTopBuyers ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
          <v-divider></v-divider>
        </v-card-actions>
      </v-col>
    </v-row>

    <!-- dialog detail -->
    <v-dialog
      v-model="dialog_detail"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">รายการสินค้า</font>
          </span>
          <v-btn icon dark @click="CloseDialog('readonly')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pt-3" style="text-align: center;">
                <span style="color:#333333; font-size: 16px; font-weight: 600;">รายการสินค้าทั้งหมด {{ Number( order_Detail.length ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น</span>
              </v-col>
            </v-row>
        </v-card-text>
        <v-card-text v-bind:style="{'height' : '400px'}">
          <v-container>
            <v-row v-for="(item, index) in order_Detail" :key="index">
              <v-col cols="12">
                <v-card class="mt-4">
                  <v-row>
                    <v-col cols="4" align="center">
                      <v-avatar tile size="160">
                        <div v-if="item.product_image !== ''">
                          <img :src="item.product_image" alt="Product Image" class="avatar-image" />
                        </div>
                        <div v-else>
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </div>
                      </v-avatar>
                    </v-col>
                    <v-col cols="8">
                      <v-card-title class="no-word-break">{{ item.product_name }}</v-card-title>
                      <v-card-text>
                        <div><b>Product ID:</b> {{ item.product_id }}</div>
                        <div><b>Main SKU:</b> {{ item.main_sku }}</div>
                        <div><b>Price:</b> {{ Number( item.revenue_default ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</div>
                        <div><b>รูปแบบการชำระเงิน:</b> {{ typePayment }}</div>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import axios from 'axios'
import VueApexCharts from 'vue-apexcharts'
import { Decode } from '@/services'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      chartSeries2: [],
      chartSeries: [],
      onedata: [],
      selectedItemShop: -3,
      itemsShop: [],
      showBestSeller: false,
      showOrderValue: false,
      showTopBuyers: false,
      monthSelected: '',
      date: '',
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31', // Set your maximum date here
      dialog_detail: false,
      order_Detail: [],
      saleOrder: [],
      keyField: 'i',
      bestSeller: [],
      orderValue: [],
      topBuyers: [],
      topSeller: [],
      xAxis: '',
      // exportFile: [],
      UrlExponential: '',
      // exportDashboard: '',
      startDate: new Date().getFullYear(),
      endDate: new Date().getFullYear(),
      shopID: '-3',
      dateFilter: 'year',
      modalDateSelect: false,
      dates: [],
      picker: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      options: ['รายปี', 'รายเดือน', 'รายวัน'],
      selectedOption: 'รายปี',
      years: [2022, 2023, 2024, 2025],
      showYearDropdown: null,
      selectedDropdown: 'รายปี',
      showMonthDropdown: false,
      selectedYear: new Date().getFullYear(),
      startDateTopSeller: null,
      endDateTopSeller: null,
      showDatePicker: false,
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      selectedMonth: null,
      selectedMonthValue: null,
      menu: false,
      selectedDates: [],
      availableYears: [],
      totalSale: '',
      totalOrder: '0',
      totalFee: '',
      totalNetAmount: '',
      typePayment: '',
      moneyIconPath: require('@/assets/icons/SellerDashboard/money (1) 1.png'),
      boxIconPath: require('@/assets/icons/SellerDashboard/box 1.png'),
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      dataModelIconPath: require('@/assets/icons/SellerDashboard/data-model 1.png'),
      uniqueIconPath: require('@/assets/icons/SellerDashboard/unique 1.png'),
      commissionIconPath: require('@/assets/icons/SellerDashboard/commission 1.png'),
      rankingIconPathconPath: require('@/assets/icons/SellerDashboard/ranking-media.jpg'),
      bestCustomerIconPath: require('@/assets/icons/SellerDashboard/best-customer-experience 1.png'),
      rankingIconPath: require('@/assets/icons/SellerDashboard/ranking (1) 1.png'),
      graphLineIconPath: require('@/assets/icons/SellerDashboard/graph-line 1.png'),
      goldMedalIconPath: require('@/assets/icons/SellerDashboard/gold-medal.png'),
      silverMedalIconPath: require('@/assets/icons/SellerDashboard/silver-medal.png'),
      bronzeMedalIconPath: require('@/assets/icons/SellerDashboard/bronze-medal.png'),
      Icon1: require('@/assets/WDShop1.png'),
      Icon2: require('@/assets/WDShop2.png'),
      Icon3: require('@/assets/WDShop3.png'),
      Icon4: require('@/assets/WDShop4.png'),
      Icon5: require('@/assets/WDShop5.png'),
      Icon6: require('@/assets/icon6.png'),
      items: [
        { text: 'Item 1', amount: '9999' },
        { text: 'Item 2', amount: '10' },
        { text: 'Item 3', amount: '9' },
        { text: 'Item 4', amount: '8' },
        { text: 'Item 5', amount: '7' },
        { text: 'Item 6', amount: '6' },
        { text: 'Item 7', amount: '5' },
        { text: 'Item 8', amount: '4' },
        { text: 'Item 9', amount: '3' },
        { text: 'Item 10', amount: '2' },
        { text: 'Item 11', amount: '1' }
        // Add more items as needed
      ],
      headers: [
        { text: 'วันที่ทำรายการ', width: '160', align: 'center', sortable: true, value: 'paid_datetime', class: 'backgroundTable fontTable--text' },
        { text: 'รายชื่อลูกค้า', width: '200', align: 'center', sortable: false, value: 'buyer_name', class: 'backgroundTable fontTable--text' },
        { text: 'เลขที่ทำรายการสั่งซื้อ', width: '160', sortable: false, align: 'center', value: 'order_number', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา (บาท)', width: '100', sortable: false, align: 'center', value: 'TxnAmount', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าธรรมเนียม (บาท)', width: '150', sortable: false, align: 'center', value: 'vat_price_payment', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชั่น/Affiliate (บาท)', width: '200', sortable: false, align: 'center', value: 'total_affiliate', class: 'backgroundTable fontTable--text' },
        { text: 'คูปองส่วนลด/แต้มส่วนลด (บาท)', width: '220', align: 'center', sortable: false, value: 'discount_coupon', class: 'backgroundTable fontTable--text' },
        { text: 'ส่วนลดของระบบ (บาท)', width: '170', align: 'center', sortable: false, value: 'discount_ngc', class: 'backgroundTable fontTable--text' },
        { text: 'ค่า GP (บาท)', width: '120', sortable: false, align: 'center', value: 'total_gp', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าขนส่ง (บาท)', width: '120', sortable: false, align: 'center', value: 'total_shipping', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนเงินที่ได้รับ (บาท)', width: '170', sortable: false, align: 'center', value: 'total_shop', class: 'backgroundTable fontTable--text' },
        { text: 'รายการสินค้า', width: '120', sortable: false, align: 'center', value: 'product_list', class: 'backgroundTable fontTable--text' }
      ],
      chartOptions: {
        chart: {
          id: 'income-difference-chart',
          // stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          colors: ['#008FFB', '#FF4560']
        },
        markers: {
          size: 5, // Adjust the size of markers as per your preference
          colors: '#fff',
          strokeColors: '#AE8FF7',
          strokeWidth: 2,
          strokeOpacity: 0.9,
          strokeDashArray: 0,
          fillOpacity: 0,
          discrete: [],
          shape: 'circle',
          radius: 2,
          offsetX: 0,
          offsetY: 0,
          onClick: undefined,
          onDblClick: undefined,
          showNullDataPoints: true,
          hover: {
            size: undefined,
            sizeOffset: 6
          }
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      },
      chartOptions2: {
        chart: {
          id: 'income-difference-chart',
          // stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          colors: ['#008FFB', '#FF4560']
        },
        markers: {
          size: 5, // Adjust the size of markers as per your preference
          colors: '#fff',
          strokeColors: '#AE8FF7',
          strokeWidth: 2,
          strokeOpacity: 0.9,
          strokeDashArray: 0,
          fillOpacity: 0,
          discrete: [],
          shape: 'circle',
          radius: 2,
          offsetX: 0,
          offsetY: 0,
          onClick: undefined,
          onDblClick: undefined,
          showNullDataPoints: true,
          hover: {
            size: undefined,
            sizeOffset: 6
          }
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'dark',
            type: 'horizontal',
            shadeIntensity: 0.5,
            gradientToColors: ['#ABE5A1'],
            inverseColors: true,
            opacityFrom: 1,
            opacityTo: 1,
            stops: [0, 100]
          }
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' รายการ'
            }
          }
        }
      },
      startDateNewshop: `${new Date().getFullYear()}-01-01`,
      endDateNewshop: this.formatDateToYMD(new Date())
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardShopAdminMobile' }).catch(() => { })
      } else {
        await localStorage.setItem('pathAdmin', 'BackToMobile')
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => { })
      }
    }
  },
  created () {
    // this.chartSeries[0].data = [0, 0, 0, 0, 0, 0, 178500, 212210, 625136297.93, 626470, 224280, 8800]
    this.$EventBus.$emit('changeNavAdmin')
    // Set default values or perform initial actions on component creation
    if (this.selectedDropdown === 'รายปี') {
      this.showYearDropdown = true
    }
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.current_role_user.super_admin === false && this.onedata.user.current_role_user.super_admin_platform === false && this.onedata.user.current_role_user.admin_platform === false) {
        this.$router.push({ path: '/' }).catch(() => {})
      }
      // console.log(this.onedata)
    }
    // if (localStorage.getItem('shopSellerID') === '' || localStorage.getItem('shopSellerID') === null || localStorage.getItem('shopSellerID') === undefined) {
    //   this.$router.push({ path: '/' })
    // } else {
    //   this.shopID = localStorage.getItem('shopSellerID')
    //   console.log(this.shopID, 'sshhooppiidd')
    // }
    // this.shopID = this.$route.params.id
    this.getShopData()
    this.getPageData()
    this.getOrderList(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopProducts(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopBuyers(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopSeller(`${this.selectedYear}-01-01`, `${this.selectedYear}-12-31`, this.shopID)
    // this.getRevenuGraphSummary(this.startDate, this.endDate, this.shopID, this.dateFilter)
  },
  computed: {
    dateRangeText () {
      if (this.dates.length > 1) {
        var startDays = new Date(this.dates[0]).toLocaleDateString('th-TH')
        var endDays = new Date(this.dates[1]).toLocaleDateString('th-TH')
        var totalDays = startDays + ' - ' + endDays
        return totalDays
      } else if (this.dates.length === 1) {
        var oneDays = new Date(this.dates).toLocaleDateString('th-TH')
        return oneDays
      }
      return this.dates.join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    this.startDateTopSeller = `${this.selectedYear}-01-01`
    this.endDateTopSeller = `${this.selectedYear}-12-31`
  },
  methods: {
    formatDateToYMD (date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    async ExportNewShop () {
      this.$store.commit('openLoader')
      // var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      var startDate = this.startDateNewshop
      var endDate = this.endDateNewshop
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_seller_created_at?created_from=${startDate}&created_to=${endDate}`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `รายงานร้านค้าที่สร้างตั้งแต่วันที่ ${startDate} ถึงวันที่ ${endDate}.xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async ExportOtop () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/admin_platform/export_otop_shop_product`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Otop_Shop_Product_${dateCurrent.replaceAll(' ', '_')}.xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async ExportUsesCoupon () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/admin_platform/export_coupon_platform`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Couon_Platfrom_${dateCurrent.replaceAll(' ', '_')}.xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async ExportSumTransaction () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_transactionv2`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Transaction_${dateCurrent.replaceAll(' ', '_')}.csv`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async ExportSumTransactionJV () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_transaction_jv`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Transaction_JV_${dateCurrent.replaceAll(' ', '_')}.xls`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async saveDates (val) {
      if (this.dates.length === 1) {
        this.getRevenuGraphSummary(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getOrderList(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopProducts(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopBuyers(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopSeller(this.dates, this.dates, this.shopID)
        this.startDate = this.dates
        this.endDate = this.dates
        this.startDateTopSeller = this.dates // อัพเดต startDateTopSeller
        this.endDateTopSeller = this.dates // อัพเดต endDateTopSeller
        this.startDateNewshop = this.dates
        this.endDateNewshop = this.dates
        this.modalDateSelect = false
      } else {
        this.$refs.modalDateSelect.save(val)
        var Range = await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.dateRange = Range
        // กำหนด startDateTopSeller และ endDateTopSeller ตามช่วงวันที่ที่เลือก
        this.startDateTopSeller = Range[0]
        this.endDateTopSeller = Range[1]
        this.startDateNewshop = Range[0]
        this.endDateNewshop = Range[1]

        this.getRevenuGraphSummary(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getOrderList(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopProducts(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopBuyers(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopSeller(this.dates[0], this.dates[1], this.shopID)

        this.startDate = this.dates[0]
        this.endDate = this.dates[1]
        this.modalDateSelect = false
      }
    },
    getPageData () {
      // โหลดครั้งแรกตอนเปิดหน้านี้
      this.getRevenuGraphSummary(this.startDate, this.endDate, this.shopID, this.dateFilter)
    },
    openDialog (item) {
      this.typePayment = ''
      this.dialog_detail = true
      this.order_Detail = item.product_list
      this.typePayment = item.type_payment
    },
    CloseDialog (val) {
      this.type = val
      this.dialog_detail = false
    },
    ChangeChartOptions (val) {
      this.chartOptions = {
        chart: {
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          markers: {
            size: 4, // Adjust the size of markers as per your preference
            colors: undefined,
            strokeColors: '#fff',
            strokeWidth: 2,
            strokeOpacity: 0.9,
            strokeDashArray: 0,
            fillOpacity: 1,
            discrete: [],
            shape: 'circle',
            radius: 2,
            offsetX: 0,
            offsetY: 0,
            onClick: undefined,
            onDblClick: undefined,
            showNullDataPoints: true,
            hover: {
              // size: undefined,
              sizeOffset: 6
            }
          }
        },
        xaxis: {
          categories: this.showMonthDropdown ? val : this.showDatePicker ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      }
      this.chartOptions2 = {
        chart: {
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          markers: {
            size: 4, // Adjust the size of markers as per your preference
            colors: undefined,
            strokeColors: '#fff',
            strokeWidth: 2,
            strokeOpacity: 0.9,
            strokeDashArray: 0,
            fillOpacity: 1,
            discrete: [],
            shape: 'circle',
            radius: 2,
            offsetX: 0,
            offsetY: 0,
            onClick: undefined,
            onDblClick: undefined,
            showNullDataPoints: true,
            hover: {
              // size: undefined,
              sizeOffset: 6
            }
          }
        },
        xaxis: {
          categories: this.showMonthDropdown ? val : this.showDatePicker ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'dark',
            type: 'horizontal',
            shadeIntensity: 0.5,
            gradientToColors: ['#ABE5A1'],
            inverseColors: true,
            opacityFrom: 1,
            opacityTo: 1,
            stops: [0, 100]
          }
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' รายการ'
            }
          }
        }
      }
    },
    handleSelectChange () {
      // console.log('Selected item:', this.selectedItemShop)
      this.shopID = this.selectedItemShop
      this.getPageData()
      this.getOrderList(this.startDate, this.endDate, this.shopID, this.dateFilter)
      this.getTopProducts(this.startDate, this.endDate, this.shopID, this.dateFilter)
      this.getTopBuyers(this.startDate, this.endDate, this.shopID, this.dateFilter)
      this.getTopSeller(this.startDateTopSeller, this.endDateTopSeller, this.shopID)
    },
    async getShopData () {
      await this.$store.dispatch('actionListAllShopData')
      var response = await this.$store.state.ModuleDashBoardForAdmin.stateListAllShopData
      if (response.ok === 'y') {
        // console.log('actionListAllShopData', response)
        var statAllShop = [{ name_th: 'ทั้งหมด', id: -3 }]
        this.itemsShop = response.query_result
        this.itemsShop = this.itemsShop.filter(item => item.is_JV === 'no')
        this.itemsShop = statAllShop.concat(this.itemsShop)
      }
    },
    async getRevenuGraphSummary (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      // console.log(data, 'RGS')
      await this.$store.dispatch('actionRevenuGraphSummary', data)
      var response = await this.$store.state.ModuleDashBoard.stateRevenuGraphSummary
      if (response.ok === 'y') {
        // console.log('actionRevenuGraphSummary', response)
        var dataTable = []
        var dataTable2 = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dataTable.push(response.query_result.revenugrap[i].revenu)
          dataTable2.push(response.query_result.revenugrap[i].transaction)
        }
        var dayInMonth = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dayInMonth.push(new Date(response.query_result.revenugrap[i].date).toLocaleDateString('th-TH', { month: 'long', day: 'numeric' }))
        }
        var dateToDate = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dateToDate.push(new Date(response.query_result.revenugrap[i].date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        }
        // console.log(dayInMonth, 'dayInMonth')
        if (response.ok === 'y') {
          this.chartSeries = await [{ name: 'รายได้', data: dataTable }]
          this.chartSeries2 = await [{ name: 'รายการ transaction', data: dataTable2 }]
          if (this.showMonthDropdown) {
            this.ChangeChartOptions(dayInMonth)
          } else if (this.showDatePicker) {
            this.ChangeChartOptions(dateToDate)
          } else {
            this.ChangeChartOptions(false)
          }
          if (response.query_result.sumaryDocument === '' || response.query_result.sumaryDocument === null || response.query_result.sumaryDocument === undefined) {
            // ในกรณีที่ ค่าจำนวนรายการสั่งซื้อทั้งหมด = ค่าว่าง, null, undefined
            this.totalOrder = 0 // จำนวนรายการสั่งซื้อทั้งหมด
          } else {
            this.totalOrder = response.query_result.sumaryDocument // จำนวนรายการสั่งซื้อทั้งหมด
          }
          if (response.query_result.sumaryRevenu === '' || response.query_result.sumaryRevenu === null || response.query_result.sumaryRevenu === undefined) {
            // ในกรณีที่ ค่ายอดขายทั้งหมด = ค่าว่าง, null, undefined
            this.totalsale = 0 // ยอดขายทั้งหมด
          } else {
            this.totalSale = response.query_result.sumaryRevenu // ยอดขายทั้งหมด
          }
          if (response.query_result.fee === '' || response.query_result.fee === null || response.query_result.fee === undefined) {
            // ในกรณีที่ ค่าธรรมเนียม = ค่าว่าง, null, undefined
            this.totalFee = 0 // ค่าธรรมเนียม
          } else {
            this.totalFee = response.query_result.fee // ค่าธรรมเนียม
          }
          if (response.query_result.net_amount === '' || response.query_result.net_amount === null || response.query_result.net_amount === undefined) {
            // ในกรณีที่ ยอดเงินสุทธิ = ค่าว่าง, null, undefined
            this.totalNetAmount = 0 // ยอดเงินสุทธิ
          } else {
            this.totalNetAmount = response.query_result.net_amount // ยอดเงินสุทธิ
          }
        } else {
          // if response.message === 'n'
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: response.message
          })
        }
      }
    },
    async getOrderList (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionOrderList', data)
      var response = await this.$store.state.ModuleDashBoard.stateOrderList
      if (response.ok === 'y') {
        // console.log('actionOrderList', response.query_result.orderList)
        this.saleOrder = response.query_result.orderList
        var count = 1
        this.saleOrder.forEach((item) => {
          item.i = count
          count = count + 1
        })
        // this.saleOrder = datasale
        // console.logconsole.log(this.saleOrder, 'tong')
      }
    },
    async getTopProducts (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      // console.log('see data product', data)
      await this.$store.dispatch('actionTopProducts', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopProducts
      if (response.ok === 'y') {
        // console.log('actionTopProducts', response)
        this.bestSeller = response.query_result.countProduct
        this.orderValue = response.query_result.sumRevenuProduct
      }
    },
    async getTopBuyers (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      // console.log('see data buy', data)
      await this.$store.dispatch('actionTopBuyers', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopBuyers
      if (response.ok === 'y') {
        // console.log('actionTopBuyers', response)
        this.topBuyers = response.query_result
      }
    },
    async getTopSeller (startDate, endDate, shopID) {
      var data = {
        start_date: startDate,
        end_date: endDate,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      // console.log('see data', data)
      await this.$store.dispatch('actionTopSeller', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopSeller
      if (response.code === 200) {
        this.topSeller = response.data
        // console.log('see top seller', this.topSeller)
      }
    },
    async getExportDashboard (startDate, endDate, shopID, dateFilter, exportDashboard, fileName) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        exportdashboard: exportDashboard,
        start: startDate.toString(),
        end: endDate.toString(),
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/exportdashboard`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', fileName + '.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async getExportDashboardSeller (shopID) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // Set start_date and end_date to the selected year values for the first load
      if (!this.startDateTopSeller || !this.endDateTopSeller) {
        this.startDateTopSeller = `${this.selectedYear}-01-01`
        this.endDateTopSeller = `${this.selectedYear}-12-31`
      }
      var data = {
        start_date: this.startDateTopSeller,
        end_date: this.endDateTopSeller,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      // console.log('export', data)
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/export-topseller`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'TOP_10_ยอดขายร้านค้า' + '.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    closeDateSelect ($refs) {
      this.modalDateSelect = false
      this.dates = []
    },
    handleOptionChange () {
      // Reset selections when the option changes
      this.selectedYear = null
      this.selectedMonth = null
      this.selectedMonthValue = null
      this.selectedDates = []
    },
    onDropdownSelected (option) {
      this.dates = []
      this.selectedDropdown = option
      if (option === 'รายปี' || option === 'รายเดือน') {
        this.showYearDropdown = true
        if (option === 'รายปี') {
          this.dateFilter = 'year'
          this.startDate = this.selectedYear
          this.endDate = this.selectedYear
          this.showMonthDropdown = false
          this.showDatePicker = false
          this.selectedDates = null
          this.startDateTopSeller = `${this.selectedYear}-01-01`
          this.endDateTopSeller = `${this.selectedYear}-12-31`
          this.startDateNewshop = `${this.selectedYear}-01-01`
          this.endDateNewshop = `${this.selectedYear}-12-31`
          this.saleOrder = []
          this.bestSeller = []
          this.orderValue = []
          this.topBuyers = []
          this.topSeller = []
          this.getRevenuGraphSummary(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getOrderList(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getTopProducts(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getTopBuyers(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getTopSeller(this.startDateTopSeller, this.endDateTopSeller, this.shopID)
          // this.onYearSelected(new Date().getFullYear())
        }
      } else {
        this.showYearDropdown = false
      }
      this.selectedYear = new Date().getFullYear()
      if (option === 'รายเดือน') {
        this.showMonthDropdown = true
        // this.selectedYear = null
      } else {
        this.showMonthDropdown = false
      }
      this.selectedMonth = null
      this.selectedMonthValue = null
      if (option === 'รายวัน') {
        this.showDatePicker = true
        this.dateFilter = 'day'
      } else {
        this.showDatePicker = false
      }
      this.selectedDates = null
    },
    onMonthSelected (month) {
      this.dateFilter = 'month'
      this.selectedMonth = month.text
      this.selectedMonthValue = `${this.selectedYear}-${month.value}`
      this.startDate = this.selectedMonthValue
      this.endDate = this.selectedMonthValue
      this.selectedMonthSeller = `${this.selectedYear}-${month.value}-01`

      const selectedMonth = `${this.selectedYear}-${month.value}`
      const date = new Date(selectedMonth)
      const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      const lastDay = nextMonth.getDate()

      if (selectedMonth === `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`) {
        this.endDateTopSeller = new Date().toISOString().split('T')[0]
      } else {
        this.endDateTopSeller = `${this.selectedYear}-${month.value}-${lastDay.toString().padStart(2, '0')}`
      }

      this.startDateTopSeller = this.selectedMonthSeller
      this.startDateNewshop = this.selectedMonthSeller
      this.endDateNewshop = this.endDateTopSeller
      this.getRevenuGraphSummary(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getOrderList(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopProducts(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopBuyers(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopSeller(this.startDateTopSeller, this.endDateTopSeller, this.shopID)
    },
    onYearSelected (year) {
      if (this.dateFilter === 'year') {
        this.dateFilter = 'year'
        this.selectedYear = year
        this.startDateTopSeller = `${this.selectedYear}-01-01`
        this.endDateTopSeller = `${this.selectedYear}-12-31`
        this.startDateNewshop = `${this.selectedYear}-01-01`
        this.endDateNewshop = `${this.selectedYear}-12-31`
        // console.log(this.selectedYear, 'selecteddd')
        this.getRevenuGraphSummary(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getOrderList(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getTopProducts(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getTopBuyers(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getTopSeller(this.startDateTopSeller, this.endDateTopSeller, this.shopID)
      } else if (this.dateFilter === 'month') {
        this.selectedYear = year
        var changeYear = this.selectedMonthValue.split('-')
        this.selectedMonthValue = `${year}-${changeYear[1]}`
        // คำนวณช่วงวันที่สำหรับเดือนที่เลือก
        const selectedMonth = `${this.selectedYear}-${changeYear[1]}`
        const date = new Date(selectedMonth)
        const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)
        const lastDay = nextMonth.getDate()

        // อัปเดต startDate และ endDate ให้ตรงกับเดือนที่เลือก
        this.startDateTopSeller = `${this.selectedYear}-${changeYear[1]}-01`
        this.endDateTopSeller = `${this.selectedYear}-${changeYear[1]}-${lastDay.toString().padStart(2, '0')}`
        this.startDateNewshop = this.startDateTopSeller
        this.endDateNewshop = this.endDateTopSeller
        this.getRevenuGraphSummary(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getOrderList(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getTopProducts(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getTopBuyers(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getTopSeller(this.startDateTopSeller, this.endDateTopSeller, this.shopID)
      }
    }
  }
}
</script>

<style scoped>
.text-container {
  overflow: visible;  /* or 'auto' or 'scroll' depending on your requirements */
  white-space: nowrap;  /* prevent text from wrapping */
}
.two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
}
.one-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.custom-text-field {
  height: 37px;
  width: 280px;
}
.custom-chip {
  overflow: visible !important;
}
.no-word-break {
  word-break: normal;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.backgroundSellerDashboard {
  max-width: 100% !important;
  background: #F7FCFC;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C
}
.exportButtonText {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF
}
.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
</style>
<style scoped>
.v-btn__content {
    font-size: 16px;
}
</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(12) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(12) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
