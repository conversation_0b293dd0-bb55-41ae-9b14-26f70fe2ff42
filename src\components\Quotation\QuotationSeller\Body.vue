<template>
  <v-card outlined class="ma-5">
    <v-data-table
      hide-default-footer
      disable-sort
      :headers="headers"
      :items="product_each"
      item-key="id"
      dense
      class="fixed_header"
    >
      <template v-slot:[`item.net_price`]="{ item }">
        {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
      </template>
    </v-data-table>
  </v-card>
</template>

<script>
export default {
  props: ['OrderSellerDetailProp'],
  created () {
    this.foreach(this.OrderSellerDetailProp)
  },
  methods: {
    foreach (val) {
      val.product_list.forEach(element1 => {
        this.product_each.push(element1)
        // console.log('element1', element1)
        // element1.data.product_list.forEach(element => {
        //   console.log('log elemnt เฉยๆ', element)
        //   this.product_each.push(element)
        // })
      })
    }
  },
  watch: {
    OrderSellerDetailProp (val) {
      // console.log('val =======', val)
      this.foreach(val)
      // val.order_number.forEach(element1 => {
      //   console.log('ele1', element1)
      // element1.order_number.forEach(element => {
      //   console.log('element===', element)
      //   element.data.product_list.forEach(element2 => {
      //     console.log('element2 ===', element2)
      //     element2.order_number = element.order_number
      //     this.product_each.push(element2)
      //   })
      // })
      // })
      // console.log('val', this.product_each)
    }
  },
  data: () => ({
    product_each: [],
    headers: [
      {
        text: 'รหัสสินค้า',
        value: 'product_sku',
        align: 'center',
        divider: true,
        width: '100'
      },
      { text: 'ชื่อสินค้า', value: 'product_name', align: 'center', divider: true },
      { text: 'จำนวน', value: 'product_quantity', align: 'center', width: 50, divider: true },
      {
        text: 'ราคา',
        value: 'net_price',
        align: 'center',
        width: '100'
      }
    ]
  })
}
</script>

<style>

</style>
