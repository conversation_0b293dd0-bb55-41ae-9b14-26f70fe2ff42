import AxiosNotification from './axios_notification_api'

const ModuleNotification = {
  state: {
    stateListNotification: [],
    stateUpdateNotification: [],
    stateReadAllNotification: []
  },
  mutations: {
    mutationsListNotification (state, data) {
      state.stateListNotification = data
    },
    mutationsUpdateNotification (state, data) {
      state.stateUpdateNotification = data
    },
    mutationsReadAllNotification (state, data) {
      state.stateReadAllNotification = data
    }
  },
  actions: {
    async actionsListNotification (context, data) {
      var response = await AxiosNotification.ListNotification(data)
      await context.commit('mutationsListNotification', response)
    },
    async actionsUpdateNotification (context, data) {
      var response = await AxiosNotification.UpdateNotification(data)
      await context.commit('mutationsUpdateNotification', response)
    },
    async actionsReadAllNotification (context) {
      var response = await AxiosNotification.ReadAllNotification()
      await context.commit('mutationsReadAllNotification', response)
    }
  }
}

export default ModuleNotification
