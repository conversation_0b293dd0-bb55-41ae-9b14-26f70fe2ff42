<template>
  <v-row>
    <v-col cols="12">
      <v-card elevation="0">
        <v-container grid-list-xs>
          <v-row>
            <v-col cols="12">
              <p :style="MobileSize ? 'font-size: 18px;' : 'font-size: 21px;'"><b>สรุปรายการสั่งซื้อสินค้า</b></p>
            </v-col>
            <v-col cols="8">
              <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
            </v-col>
            <v-col cols="4" align="right">
              <span><b>{{ items.total_price_no_vat_web ? Number(items.total_price_no_vat_web).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <!-- <v-col cols="12">
              <v-divider></v-divider>
            </v-col> -->
            <v-col cols="8">
              <span>ภาษีมูลค่าเพิ่ม</span>
            </v-col>
            <v-col cols="4" align="right">
              <span><b>{{ items.total_vat ? Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
            </v-col>
            <v-col cols="4" align="right">
              <span><b>{{ items.total_price_vat ? Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <!-- ส่วนลดคูปอง -->
            <v-col cols="8">
              <span class="red--text ml-3">ส่วนลดคูปอง(ร้านค้า) :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span class="red--text"><b>- {{ items.total_coupon_discount ? Number(items.total_coupon_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span class="red--text ml-3">ส่วนลดคูปอง(ระบบ) :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span class="red--text"><b>- {{ items.total_coupon_platform_discount ? Number(items.total_coupon_platform_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span class="red--text ml-3">ส่วนลดแต้ม(ร้านค้า) :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span class="red--text"><b>- {{ items.total_point ? Number(items.total_point).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span>ราคาหลังหักส่วนลด :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span><b>{{ items.total_price_after_all_discount ? Number(items.total_price_after_all_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span>ค่าจัดส่ง :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span><b>{{ items.shipping_price ? Number(items.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span class="red--text ml-3">ส่วนลดค่าจัดส่ง(ร้านค้า) :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span class="red--text"><b>- {{ items.total_coupon_shipping_discount_v2 ? Number(items.total_coupon_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <v-col cols="8">
              <span class="red--text ml-3">ส่วนลดค่าจัดส่ง(ระบบ) :</span>
            </v-col>
            <v-col cols="4" align="right">
              <span class="red--text"><b>- {{ items.total_coupon_platform_shipping_discount_v2 ? Number(items.total_coupon_platform_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
            <!-- <v-col cols="8" class="py-0">
              <span style="font-size: 10px; color: #8C8C8C;" class="mb-0">ราคานี้เป็นมาตรฐาน -
                ราคาอาจแตกต่างกันไปขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่ จัดส่งจะติดต่อคุณ</span>
            </v-col> -->
            <v-col cols="8">
              <span class="totalPriceFont"><b>ราคารวมทั้งหมด</b></span>
            </v-col>
            <v-col cols="4" align="right">
              <span class="totalPriceFont"><b>{{ items.net_price ? Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-col>
  </v-row>
</template>
<script>
export default {
  props: ['items'],
  data () {
    return {
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  }
}
</script>
