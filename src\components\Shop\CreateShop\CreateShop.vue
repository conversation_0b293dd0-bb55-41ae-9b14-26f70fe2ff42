<template>
  <v-container>
    <v-row dense class="mt-3" v-if="MobileSize">
      <v-col cols="12" md="12" class="mt-0 pt-0">
        <v-img v-if="$i18n.locale === 'th'" src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.png" lazy-src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.jpg" max-height="394" max-width="100%" contain></v-img>
        <v-img v-else src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShopEN.png" lazy-src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShopEN.png" max-height="394" max-width="100%" contain></v-img>
      </v-col>
    </v-row>
    <v-row dense>
      <v-col cols="12" md="12">
        <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
          <v-card-text style="text-align: center;">
            <v-row dense justify="center">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/iconCreateShop.png" lazy-src="@/assets/ImageINET-Marketplace/ICONShop/iconCreateShop.jpg" contain max-height="212" max-width="202" class="mt-12 mb-8" v-if="!MobileSize && !IpadSize"></v-img>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/iconCreateShop.png" lazy-src="@/assets/ImageINET-Marketplace/ICONShop/iconCreateShop.jpg" contain max-height="212" max-width="202" class="mt-12 mb-8" v-else-if="!MobileSize && IpadSize"></v-img>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/iconCreateShop.png" lazy-src="@/assets/ImageINET-Marketplace/ICONShop/iconCreateShop.jpg" contain max-height="212" max-width="202" class="mt-12 mb-8" v-else-if="MobileSize && !IpadSize"></v-img>
            </v-row>
            <span class="HeadTextCreateShop"><b>{{ $t('CreateShop.TitleCreate') }}</b></span><br/>
            <span class="SubTextCreateShop pt-2">{{ $t('CreateShop.Description') }}</span><br/>
            <v-btn color="#27AB9C" width="145" height="40" rounded style="font-weight: 500; font-size: 16px; line-height: 140%;" dark @click="createShopStep()" class="mb-12 mt-4"><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus</v-icon>{{ $t('CreateShop.TitleCreate') }}</v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  created () {
    this.$EventBus.$emit('CheckShop')
    this.$EventBus.$emit('checkpath')
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('changeNav', this.SelectPath)
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    // this.$EventBus.$emit('changeNav', this.SelectPath)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/createShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createShop' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    createShopStep () {
      if (this.MobileSize !== true) {
        this.$router.push({ path: '/stepCreateShop?step=1' }).catch(() => {})
      } else {
        this.$router.push({ path: '/stepCreateShopMobile?step=1' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
