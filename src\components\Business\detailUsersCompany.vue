<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>รายละเอียดผู้ใช้งานภายใน{{ this.nameTH }}</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 17px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> รายละเอียดผู้ใช้งานภายใน{{ this.nameTH }}</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาชื่อ-สกุลผู้เข้าร่วม Affiliate" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="companyUsersList.length !== 0 && (!MobileSize && !IpadSize)">รายการผู้ใช้งานภายใน{{ this.nameTH }} ทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="companyUsersList.length !== 0 && (MobileSize || IpadSize)">รายการผู้ใช้งานภายใน{{ this.nameTH }} ทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="companyUsersList"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบผู้ใช้งาน"
                no-data-text="ไม่มีรายชื่อผู้ใช้งาน"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                <template v-slot:[`item.indexOfUser`]="{ index }">
                  <span>{{ index + 1 }}</span>
                </template>
                <template v-slot:[`item.name`]="{ item }">
                  <span>{{ item.name }}</span>
                </template>
                <template v-slot:[`item.email`]="{ item }">
                  <span>{{ item.email }}</span>
                </template>
                <template v-slot:[`item.status`]="{ item }">
                  <span v-if="item.status === 'active'">
                    <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ใช้งานได้</v-chip>
                  </span>
                  <span v-else-if="item.status === 'inactive'">
                    <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
                  </span>
                </template>
                <template v-slot:[`item.detailUser`]="{ item }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="#27AB9C"
                        dark
                        v-bind="attrs"
                        v-on="on"
                        @click="showDetailUser(item)"
                      >
                        รายละเอียดและสิทธิการใช้งาน
                      </v-btn>
                    </template>
                    <span>ดูรายละเอียดและสิทธิการใช้งาน</span>
                  </v-tooltip>
                </template>
              </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <DetailUserModal ref="DetailUserModal" />
    </v-card>
  </v-container>
</template>

<script>
export default {
  components: {
    DetailUserModal: () => import(/* webpackPrefetch: true */ '@/components/Business/dialogDetailUserCompany.vue')
  },
  data () {
    return {
      search: '',
      companyUsersList: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-สกุล', value: 'name', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ข้อมูลผู้ใช้งาน', value: 'detailUser', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/detailUserCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailUserCompany' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNavBusiness')
    this.companyID = Number(this.$route.query.companyID)
    this.taxID = Number(this.$route.query.taxID)
    this.nameTH = this.$route.query.nameTH

    this.getUserCompany()
  },
  methods: {
    backtoUserMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getUserCompany () {
      this.$store.commit('openLoader')
      const data = {
        tax_id: this.taxID,
        company_id: this.companyID
      }
      await this.$store.dispatch('actionsListUsersCompany', data)
      const detailUsersCompany = await this.$store.state.ModuleBusiness.stateListUsersCompany
      if (detailUsersCompany.result === 'SUCCESS') {
        this.$EventBus.$emit('changeNavBusiness')
        this.companyUsersList = detailUsersCompany.data.users
        if (this.companyUsersList.length !== 0) {
          this.companyUsersList.forEach(element => {
            element.name = element.first_name_th + ' ' + element.last_name_th
          })
          for (var i = 0; i < this.companyUsersList.length; i++) {
            this.companyUsersList[i].indexOfUser = i + 1
          }
        }
        this.countUsersCompany = detailUsersCompany.data.total_user
        this.$store.commit('closeLoader')
      } else if (detailUsersCompany && detailUsersCompany.message === 'This user didn\'t have permissions.') {
        this.$store.commit('closeLoader')
        // this.$swal.fire({ icon: 'warning', text: 'ผู้ใช้รายนี้ไม่มีสิทธิ์ใช้งานแอดมิน', showConfirmButton: false, timer: 1500 })
        this.$swal.fire({ icon: 'warning', text: 'ผู้ใช้รายนี้ไม่มีสิทธิ์ใช้งาน', showConfirmButton: false, timerProgressBar: true, timer: 1500 })
      } else if (detailUsersCompany.message === 'Error message.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timerProgressBar: true, timer: 2500 })
      } else if (detailUsersCompany.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timerProgressBar: true, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${detailUsersCompany.message}` })
      }
    },
    showDetailUser (item) {
      const data = item
      this.$refs.DetailUserModal.open(data)
    }
  }
}
</script>

<style scoped>

</style>
