<template>
  <v-container>
    <!-- <div class=" pb-3">
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.png" class="rounded-xl"></v-img>
    </div> -->
    <v-card class="container" elevation="0">
      <v-row justify="center" dense no-gutters>
        <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step1.png" contain max-height="108" max-width="576"
          class="mt-12 mb-10" v-if="stepper === 1"></v-img>
        <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step2.png" contain max-height="108" max-width="576"
          class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
      </v-row>
      <v-card elevation="0" width="100%" height="100%"
        style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
        <v-form ref="formRegis" :lazy-validation="lazy" v-if="stepper === 1">
          <v-card-text>
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ข้อมูลทั่วไป</span>
            <v-col cols="12" md="12" class="mt-6 px-0">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                <v-card-text>
                  <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                    @click="onPickFile()">
                    <v-card-text>
                      <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                        <v-file-input v-model="DataImage" :items="DataImage" accept="image/jpeg, image/jpg, image/png"
                          @change="UploadImage()" id="file_input" multiple :clearable="false" style="display:none">
                        </v-file-input>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="280.34"
                              height="154.87" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-6">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="5" style="text-align: center;">
                              <span
                                style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มรูปภาพของคุณที่นี่</span><br />
                              <span
                                style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG,
                                .PNG
                                เพิ่มได้สูงสุด 6 รูปภาพ)</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span
                                  style="color: red;">***</span> หมายเหตุ
                                ไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;">
                    <span style="color: red;">***</span> หมายเหตุ รูปแรกจะเป็นรูปโปรไฟล์(Profile)
                  </span>
                  <div v-if="Detail.product_image.length !== 0" class="mt-4">
                    <draggable v-model="Detail.product_image" :move="onMove" @start="drag=true" @end="drag=false"
                      class="pl-5 pr-5 row  fill-height align-center sortable-list">
                      <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="6" sm="4" md="2">
                        <v-card v-if="item.type === 'image'" outlined class="pa-1" width="146" height="146">
                          <v-img :src="item.path" :lazy-src="item.url" width="130" height="130" contain>
                            <v-btn icon x-small style="float: right; background-color: #ff5252;">
                              <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                            </v-btn>
                          </v-img>
                        </v-card>
                        <!-- <v-card v-else outlined  class="pa-1" width="146" height="146"  >
                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                        </v-btn>
                      <video    autoplay loop muted playsinline  >
                        <source :src="item.length ? item.path + '?=' + `${currentTime.getTime()}`:item.path" type="video/mp4" >
                      </video>
                    </v-card> -->
                      </v-col>
                    </draggable>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
            <v-row no-gutters>
              <v-col cols="12" md="12" class="">
                <v-col cols="12" md="12" class="pb-0">
                  <span class="f-left"
                    style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ประเภทธุรกิจ</span>
                </v-col>
              </v-col>
              <v-col cols="12" md="12" class="px-2 py-0">
                <v-radio-group v-model="bussinessType" mandatory row disabled>
                  <v-radio label="นิติบุคคลอื่นๆ" style="color: #333333;" value="citizen"></v-radio>
                  <v-radio label="นิติบุคคล" style="color: #333333;" value="business"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col cols="12" md="12" class="px-2 py-0">
                <v-row dense no-gutters>
                  <v-col cols="12" md="5" sm="12">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อร้านค้า
                          <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                        <v-text-field v-model="shopName" outlined dense :rules="Rules.shop_name"
                          :error="this.v && this.shopName === null ? true : false " :maxLength="255"
                          placeholder="ระบุชื่อร้านค้า" ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="4" sm="6">
                    <v-row dense>
                      <v-col cols="12">
                        <span
                          style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เลขประจำตัวผู้เสียภาษีอากร
                          <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="pr-4">
                        <v-text-field v-model="taxNumber" outlined dense :maxLength="13" :rules="Rules.Idcard"
                          :error="this.v && this.taxNumber === null ? true : false "
                          placeholder="ระบุเลขประจำตัวผู้เสียภาษี 13 หลัก" disabled></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="3" sm="6">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">URL
                          ร้านค้า</span>
                      </v-col>
                      <v-col cols="12">
                        <!-- <v-select
                      v-model="category"
                      :items="items"
                      dense
                      placeholder="ระบุหมวดหมู่สินค้า"
                      outlined
                      style="border-radius: 4px;"
                     ></v-select> -->
                        <v-text-field v-model="urlname" outlined dense placeholder="ระบุ url ร้านค้า" readonly></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="12" class="pa-0">
                <v-col cols="12" md="12" class="pb-0 pt-0">
                  <span class="f-left" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เกี่ยวกับร้านค้า</span>
                </v-col>
              </v-col>
              <v-col cols="12" md="12" class="px-2 pt-2">
                <v-textarea v-model="descriptionShop" placeholder="อธิบายเกี่ยวกับร้านค้าของคุณ"
                  background-color="#FFFFFF" outlined height="123"></v-textarea>
              </v-col>
               <!-- เบอร์โทรศัพท์, ข้อมูลฝ่ายขาย, ข้อมูลการ เปิด - ปิด ร้านค้า -->
              <v-col cols="12" md="12" class="px-2 py-0">
                <v-row dense no-gutters>
                <!-- เบอร์โทรศัพท์ -->
                <v-col cols="12" md="12" sm="4">
                  <v-row dense>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ระบุชื่อย่อร้านค้า <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field v-model="shortName" outlined dense :rules="Rules.short_name" placeholder="ระบุชื่อย่อร้านค้า"></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เบอร์โทรศัพท์ร้านค้า <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field
                            v-model="mobile"
                            outlined
                            dense
                            :rules="Rules.telShop"
                            placeholder="ระบุเบอร์โทรศัพท์ร้านค้า"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- ข้อมูลฝ่ายขาย -->
                <!-- <v-col cols="12" md="12" sm="12">
                  <v-row dense>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อฝ่ายขาย <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field
                            v-model="saleName"
                            outlined
                            dense
                            :rules="Rules.sale_name"
                            placeholder="ระบุชื่อฝ่ายขาย"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เบอร์โทรศัพท์ฝ่ายขาย</span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field
                            v-model="salePhone"
                            outlined
                            dense
                            :maxLength="20"
                            placeholder="ระบุเบอร์โทรศัพท์ฝ่ายขาย"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เบอร์มือถือฝ่ายขาย <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field
                            v-model="saleMobilePhone"
                            outlined
                            dense
                            :rules="Rules.tel"
                            :maxLength="10"
                            oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                            placeholder="ระบุเบอร์มือถือฝ่ายขาย 10 หลัก"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อีเมลฝ่ายขาย <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field
                            v-model="saleEmail"
                            outlined
                            dense
                            :rules="Rules.email"
                            placeholder="ระบุอีเมลฝ่ายขาย"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อทีมฝ่ายขาย</span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                          <v-text-field
                            v-model="saleTeamName"
                            outlined
                            dense
                            placeholder="ระบุชื่อทีมฝ่ายขาย"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col> -->
                  <!-- <v-col cols="12" md="4">
                    <v-row dense>
                      <v-col cols="12">
                        <span
                          style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Facebook</span>
                      </v-col>
                      <v-col cols="12" class="pr-4">
                        <v-text-field v-model="facebook" outlined dense placeholder="ระบุ Facebook"></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="4">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Line</span>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field v-model="line" outlined dense placeholder="ระบุ ID LINE"
                          oninput="this.value = this.value.replace(/[^a-z0-9_.-\s]/g, '')"></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col> -->
                  <v-row dense no-gutters>
                    <v-col cols="12" md="7" style="margin-left: -5px">
                      <v-col cols="12" md="12">
                        <v-row dense no-gutters>
                          <v-col cols="12" class="mb-4">
                            <v-col cols="12" class="px-0 py-0">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ข้อมูลการ เปิด - ปิด ร้านค้า</span>
                            </v-col>
                            <v-col cols="12" class="px-0 pb-0">
                              <v-switch v-model="openshop" label="เปิด-ปิดร้านค้า" color="#27AB9C" style="font-size: 14px !important;" inset hide-details></v-switch>
                            </v-col>
                          </v-col>
                          <!-- <v-col cols="12" md="4">
                        <v-switch
                          v-model="publicshop"
                          label="ขายสินค้า Public"
                          inset
                          style="font-size: 14px !important;"
                          color="#27AB9C"
                          hide-details
                        ></v-switch>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-switch
                          v-model="partner"
                          label="ขายสินค้า Partner"
                          inset
                          style="font-size: 14px !important;"
                          color="#27AB9C"
                          hide-details
                        ></v-switch>
                      </v-col> -->
                          <v-col cols="12" md="12" class="pt-3 pb-4">
                            <v-col cols="12" class="px-0 pb-0">
                              <span class="f-left" style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ข้อมูลการขอเป็นคู่ค้า</span>
                            </v-col>
                            <v-col cols="12" class="px-0 pb-0">
                              <v-switch v-model="publicshop" label="เปิด-ปิดการขอเป็นคู่ค้า" inset style="font-size: 14px !important;" color="#27AB9C" hide-details></v-switch>
                            </v-col>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12">
                        <span
                          style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ข้อมูลการเงิน
                        </span>
                        <!-- <v-row>
                          <v-col cols="12" md="6" class="mt-3">
                            <h4>รหัสการจ่ายเงิน</h4>
                            <v-text-field outlined dense width="100" v-model="merchant_key"
                              placeholder="ระบุรหัสการจ่ายเงิน ( Merchant Key)">
                            </v-text-field>
                          </v-col>
                        </v-row> -->
                      </v-col>
                    </v-col>
                    <!-- <v-col cols="12" md="2" :style="MobileSize || IpadSize ? '': 'margin-left:-30%'"> -->
                      <!-- <v-col cols="12" md="12" class="pb-2 pl-0">
                      <span class="f-left"
                        style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ข้อมูลขนส่ง</span>
                    </v-col> -->
                      <!-- <v-col cols="12" md="12">
                        <v-row dense no-gutters>
                          <span class="f-left"
                            style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ข้อมูลขนส่ง</span>
                          <v-col cols="12" class="pt-0 ml-4">
                            <v-row dense> -->
                              <!-- <v-col cols="12" md="6" class="pt-3">
                           <span :style="IpadProSize ? 'font-size: 11px;' : 'font-size: 13px;'" style="font-weight: 400;  line-height: 22px; color: #333333;">จัดส่งธรรมดา  :</span>
                          </v-col>
                          <v-col cols="12" ms="12" md="6"> -->
                              <!-- <v-select
                            v-model="selects"
                            :items="transport"
                            :item-value="name"
                            placeholder="เลือกขนส่ง"
                            dense
                            outlined
                           >
                            <template v-slot:selection="{ item }">
                              <span style="font-size:13px">{{ item.name }}</span>
                            </template>
                            <template v-slot:item="{ item }">
                              <v-img class="mr-3" width="31" height="11" :src="item.img"></v-img>
                              {{ item.name }}
                            </template>
                           </v-select>
                          </v-col> -->
                              <!-- <v-checkbox v-model="shipping" color="#27AB9C" value="Flash" :success="true"></v-checkbox>
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"
                                class="pt-5">Flash</span>
                            </v-row>
                          </v-col>
                          <v-col cols="12" class="pt-0 ml-4">
                            <v-row dense> -->
                              <!-- <v-col cols="12" ms="12" md="6" class="pt-3">
                           <span class="f-left " :style="IpadProSize ? 'font-size: 11px;' : 'font-size: 13px;'" style="font-weight: 400; line-height: 22px; color: #333333;">จัดส่งแบบเก็บอุณหภูมิ  :</span>
                          </v-col>
                          <v-col cols="12" md="6">
                           <v-select
                            v-model="selects"
                            :items="transport"
                            item-value="name"
                            placeholder="เลือกขนส่ง"
                            dense
                            outlined
                           >
                            <template v-slot:selection="{ item }">
                              <span style="font-size:13px">{{ item.name }}</span>
                            </template>
                            <template v-slot:item="{ item }">
                              <v-img class="mr-3" width="31" height="11" :src="item.img"></v-img>
                              {{ item.name }}
                            </template>
                           </v-select>
                          </v-col> -->
                              <!-- <v-checkbox success v-model="shipping" color="#27AB9C" value="SCG"></v-checkbox><span
                                style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"
                                class="pt-5">SCG</span>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-col> -->
                    <!-- <v-col cols="12" md="4" class="pt-6">
                    <v-switch v-model="shop_cost" label="ร้านค้ารับผิดชอบค่าจัดส่งเอง" inset
                      style="font-size: 14px !important;" color="#27AB9C" hide-details></v-switch>
                  </v-col> -->
                  </v-row>
                </v-row>
              </v-col>
            </v-row>
            <v-row dense justify="end">
              <v-btn color="#27AB9C" class="px-7 mr-2" outlined dark @click="backtoMenu()">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" class="px-8" dark @click="nextStep(2)">ถัดไป</v-btn>
            </v-row>
          </v-card-text>
        </v-form>
        <v-form ref="formRegis2" :lazy-validation="lazy" v-else-if="stepper === 2">
          <v-card-text>
            <v-row dense no-gutters class="mt-0 mb-8">
              <v-col cols="12" md="12" class="mb-5">
                <span class="f-left"
                  style="font-weight: 400; font-size: 18px; line-height: 22px; color: #333333;">แก้ไขที่อยู่</span>
              </v-col>
              <v-col cols="12" md="6" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อ <span
                        style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                    <v-text-field v-model="name_profile" outlined dense placeholder="ระบุชื่อ" :rules="Rules.firstname"></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">นามสกุล <span
                        style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                    <v-text-field v-model="last_name" outlined dense placeholder="ระบุนามสกุล" :rules="Rules.lastname"></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อีเมล </span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                    <v-text-field :rules="Rules.email" v-model="email_profie" outlined dense placeholder="ระบุอีเมล"
                      required></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span
                      style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                    <v-text-field v-model="phone_profile" outlined dense :maxLength="10" :rules="Rules.tel" maxlen="10"
                      placeholder="ระบุหมายเลขโทรศัพท์ 10 หลัก"></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span
                      style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รายละเอียดที่อยู่
                    </span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                    <v-text-field v-model="details_profile" outlined dense placeholder="ระบุรายละเอียดที่อยู่">
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เลขที่ <span
                        style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                    <v-text-field v-model="house_no" outlined dense
                      oninput="this.value = this.value.replace(/[^0-9-\s\/]/g, '').replace(/(\..*)\./g, '$1')"
                      placeholder="ระบุบ้านเลขที่" :rules="Rules.house_Num"></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">แขวง/ตำบล <span
                        style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                    <addressinput-subdistrict :rules="Rules.empty" label=""
                      :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'"
                      v-model="subdistrict" placeholder="ระบุแขวง/ตำบล" />
                    <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เขต/อำเภอ <span
                        style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                    <addressinput-district label="" v-model="district"
                      :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'"
                      placeholder="ระบุเขต/อำเภอ" />
                    <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">จังหวัด <span
                        style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                    <addressinput-province label=""
                      :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'"
                      v-model="province" placeholder="ระบุจังหวัด" />
                    <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสไปรษณีย์
                      <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                    <addressinput-zipcode numbered label=""
                      :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'"
                      v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                    <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <!-- <v-col cols="12" md="12" class="mt-5">
                <v-row dense>
                  <v-col cols="12" class="pr-4 ml-3">
                    <v-row>
                      <v-checkbox v-model="default_address" color="#27AB9C" value="main" :success="true"></v-checkbox>
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"
                        class="pt-5">ตั้งค่าเป็นที่อยู่เริ่มต้น</span>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col> -->
            </v-row>
            <v-row dense justify="end">
              <v-btn color="#27AB9C" class="px-4 mr-2" outlined dark @click="stepper = 1">ย้อนกลับ</v-btn>
              <v-btn color="#27AB9C" class="px-8" dark @click="seve_edit_shop()">บันทึก</v-btn>
            </v-row>
          </v-card-text>
        </v-form>
      </v-card>
    </v-card>
    <v-dialog v-model="dialog" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">แก้ไขข้อมูลร้านค้า</font>
          </span>
          <v-btn icon dark @click="dialog = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br /><br />
        <v-card-text>
          <span>
            คุณได้ทำการแก้ไขข้อมูลร้านค้า<br />
            คุณต้องการทำรายการนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions>
          <v-container>
            <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">ยกเลิก</v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="seve_edit_shop()">ตกลง</v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
// import { Decode } from '@/services'
import draggable from 'vuedraggable'
Vue.use(VueThailandAddress)
export default {
  components: { draggable },
  data () {
    return {
      lazy: false,
      subdistrict: '',
      district: '',
      province: '',
      details: '',
      house_no: '',
      zipcode: '',
      name_profile: '',
      last_name: '',
      email_profile: '',
      phone_profile: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      dialog: false,
      stepper: 1,
      selects: '',
      DataImage: [],
      Detail: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      bussinessType: '',
      category: '',
      taxNumber: '',
      shopName: '',
      items: ['Foo', 'Bar', 'Fizz', 'Buzz'],
      descriptionShop: '',
      mobile: '',
      facebook: '',
      line: '',
      SCG: '',
      flash: '',
      urlname: '',
      shop_cost: false,
      partner: false,
      publicshop: false,
      openshop: false,
      allDay: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
      MondaytimeStart: null,
      MondaytimeEnd: null,
      TuesdaytimeStart: null,
      TuesdaytimeEnd: null,
      WednesdaytimeStart: null,
      WednesdaytimeEnd: null,
      ThursdaytimeStart: null,
      ThursdaytimeEnd: null,
      FirdaytimeStart: null,
      FirdaytimeEnd: null,
      SaturdaytimeStart: null,
      SaturdaytimeEnd: null,
      SundaytimeStart: null,
      SundaytimeEnd: null,
      menuMondayStart: false,
      menuMondayEnd: false,
      menuTuesdayStart: false,
      menuTuesdayEnd: false,
      menuWednesdayStart: false,
      menuWednesdayEnd: false,
      menuThursdayStart: false,
      menuThursdayEnd: false,
      menuFirdayStart: false,
      menuFirdayEnd: false,
      menuSaturdayStart: false,
      menuSaturdayEnd: false,
      menuSundayStart: false,
      menuSundayEnd: false,
      merchant_key: '',
      shop_address: '',
      shipping: [],
      default_address: '',
      img: '',
      v: false,
      idTell: '',
      shortName: '',
      saleName: '',
      salePhone: '',
      saleMobilePhone: '',
      saleEmail: '',
      saleTeamName: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        sale_name: [v => !!v || 'กรุณากรอกชื่อฝ่ายขาย'],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล'
        ],
        shop_name: [
          v => !!v || 'กรุณากรอกชื่อร้านค้า'
        ],
        short_name: [
          v => !!v || 'กรุณากรอกชื่อย่อร้านค้า',
          v => v.length <= 5 || 'กรุณากรอกชื่อย่อร้านค้าไม่เกิน 5 ตัวอักษร'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครับ 13 ตัว'
        ],
        firstname: [
          v => !!v || 'กรุณากรอกชื่อ'
        ],
        lastname: [
          v => !!v || 'กรุณากรอกนามสกุล'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => (v.length > 8 && v.length <= 20) || v === '' || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        Merchant: [
          v => !!v || 'กรุณาระบุรหัสการจ่ายเงิน'
        ],
        bussinessType: [
          v => !!v || 'กรุณาเลือกประเภทธุรกิจ'
        ],
        Idcard: [
          v => /^[0-9]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข'
        ]
      },
      transport: [
        {
          name: 'Flash Express',
          img: require('@/assets/Create_Store/Flash.png')
        },
        {
          name: 'SCG Express',
          img: require('@/assets/Create_Store/SCG.png')
        }
      ]
    }
  },
  watch: {
    shopName (val) {
      if (val !== '') {
        const shopCleaned = val.replace(/\s/g, '-')
        var idshop = localStorage.getItem('shopSellerID')
        this.urlname = `${process.env.VUE_APP_DOMAIN}shoppage/${shopCleaned}-${idshop}`
      }
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNavAdmin')
    // this.$EventBus.$emit('changeNav', this.SelectPath)
    // var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail_admin')))
    if (localStorage.getItem('id_shop_detail_admin') !== null) {
      this.getDetailShop()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    backtoMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/adminShopManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminShopManage' }).catch(() => {})
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    async getDetailShop () {
      var idshop = JSON.parse(localStorage.getItem('id_shop_detail_admin'))
      var data = {
        seller_shop_id: idshop
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      this.descriptionShop = response.data[0].shop_description
      this.urlname = response.data[0].url_name
      this.img = response.data[0].shop_image
      this.shop_address = response.data[0].address_detail
      this.shopEmail = response.data[0].shop_email
      this.bussinessType = response.data[0].shop_type
      this.shopName = response.data[0].shop_name
      this.facebook = response.data[0].facebook_url
      this.line = response.data[0].line_id
      this.taxNumber = response.data[0].tax_id
      this.merchant_key = response.data[0].merchant_key
      this.img = response.data[0].shop_image
      this.shortName = response.data[0].short_name
      // this.saleName = response.data[0].sale_shop[0].sale_name
      // this.salePhone = response.data[0].sale_shop[0].sale_tel_no
      // this.saleMobilePhone = response.data[0].sale_shop[0].sale_mobile
      // this.saleEmail = response.data[0].sale_shop[0].sale_email
      // this.saleTeamName = response.data[0].sale_shop[0].sale_team
      var email = response.data[0].shop_email
      this.email_profie = email[0].seller_email
      this.shipping = []
      // for (let i = 0; i < response.data[0].shipping_type.length; i++) {
      //   this.shipping.push(response.data[0].shipping_type[i].shipping_type)
      // }
      if (response.data[0].shop_status === 'active') {
        this.openshop = true
      } else {
        this.openshop = false
      }
      if (response.data[0].have_partner === 'yes') {
        this.publicshop = true
      } else {
        this.publicshop = false
      }
      if (response.data[0].partner_show === 'yes') {
        this.partner = true
      } else {
        this.partner = false
      }
      for (let i = 0; i < this.img.length; i++) {
        var sent = {
          path: this.img[i].media_path,
          name: 'default',
          type: 'image',
          id: this.img[i].id
        }
        this.Detail.product_image.push(sent)
      }
      this.default_address = this.shop_address[0].default_address
      this.house_no = this.shop_address[0].house_no
      this.subdistrict = this.shop_address[0].sub_district
      this.district = this.shop_address[0].district
      this.province = this.shop_address[0].province
      this.details = this.shop_address[0].details
      this.zipcode = this.shop_address[0].zipcode
      this.details_profile = this.shop_address[0].detail
      this.name_profile = response.data[0].first_name
      this.last_name = response.data[0].last_name
      this.mobile = response.data[0].shop_phone[0].phone
      this.phone_profile = response.data[0].shop_phone[1] !== undefined ? response.data[0].shop_phone[1].phone : ''
      this.idTell = response.data[0].shop_phone[0].id
      // console.log('testTongs', response)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      // console.log(this.DataImage, 'this.DataImage')
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      // this.Detail.product_image = []
      // console.log(this.Detail.product_image.length, 'aaaaa')
      if (this.Detail.product_image.length < 6) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          if (imageSize < 2) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = () => {
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (this.Detail.product_image.length < 6) {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  path: url,
                  name: this.DataImage[i].name,
                  type: (this.DataImage[i].type.split('/', 1)).toString()
                })
                // console.log(this.Detail.product_image.length, 'aaaaa2')
              } else {
                this.$swal.fire({
                  icon: 'warning',
                  text: 'กรุณาใส่รูปไม่เกิน 6 รูป',
                  showConfirmButton: false,
                  timer: 1500
                })
              }
              // console.log(this.Detail.product_image, 'this.Detail.product_image')
              // mediaType = this.DataImage[i].type
              // var checkType = mediaType.split('/', 1)
              // if (checkType.toString() === 'video') {
              //   checkType = 'vdo'
              // } else {
              //   checkType = 'image'
              // }
              // this.shop_media.push({
              //   media: element,
              //   media_type: checkType
              // })
              // console.log(this.shop_media, 'this.shop_media')
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
              showConfirmButton: false,
              timer: 1500
            })
          }
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาใส่รูปไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.product_image.splice(index, 1)
      } else {
        this.Detail.product_image.splice(index, 1)
      }
      this.DataImage = []
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    nextStep (val) {
      if (this.$refs.formRegis.validate(true)) {
        this.stepper = val
        window.scrollTo(0, 0)
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    openModal () {
      this.dialog = true
    },
    closeModal () {
      this.dialog = false
      window.scrollTo(0, 0)
    },
    async seve_edit_shop () {
      this.dialog = false
      if (this.$refs.formRegis2.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkSendAddress()
            if (check.length !== 0) {
              var list = []
              for (let i = 0; i < this.Detail.product_image.length; i++) {
                if (this.Detail.product_image[i].name === 'default') {
                  list.push({
                    id: this.Detail.product_image[i].id
                  })
                } else {
                  list.push({
                    id: '-1',
                    image_data: this.Detail.product_image[i].image_data
                  })
                }
              }
              var dataEdit = {
                seller_shop_id: JSON.parse(localStorage.getItem('id_shop_detail_admin')),
                shop_name: this.shopName,
                shop_url: this.urlname,
                tax_id: this.taxNumber,
                shop_description: this.descriptionShop,
                shop_status: this.openshop ? 'active' : 'inactive',
                public_show: this.publicshop ? 'yes' : 'no',
                have_partner: this.publicshop ? 'yes' : 'no',
                partner_show: this.partner ? 'yes' : 'no',
                facebook_url: this.facebook,
                line_id: this.line,
                shop_type: this.bussinessType,
                // shop_address: this.shop_address,
                first_name: this.name_profile,
                last_name: this.last_name,
                shop_address: [
                  {
                    id: this.shop_address[0].id,
                    default_address: this.default_address === 'main' ? 'main' : '',
                    house_no: this.house_no,
                    detail: this.details_profile,
                    province: this.province,
                    district: this.district,
                    sub_district: this.subdistrict,
                    zipcode: this.zipcode
                  }
                ],
                shop_email: [{
                  id: this.shopEmail[0].id === undefined ? -1 : this.shopEmail[0].id,
                  seller_email: this.email_profie
                }],
                merchant_key: this.merchant_key,
                shop_phone: [
                  {
                    id: -1,
                    phone_no: this.mobile
                  },
                  {
                    id: -1,
                    phone_no: this.phone_profile
                  }
                ],
                shop_shipping_type: this.shipping,
                shop_image: list,
                short_name: this.shortName,
                sale_name: this.saleName,
                sale_tel_no: this.salePhone,
                sale_mobile: this.saleMobilePhone,
                sale_email: this.saleEmail,
                sale_team: this.saleTeamName
              }
              await this.$store.dispatch('actionEditShop', dataEdit)
              var response = await this.$store.state.ModuleShop.stateEditShop
              if (response.code === 200) {
                this.$swal.fire({ text: 'บันทึกข้อมูลสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
                window.scrollTo(0, 0)
                this.Detail.product_image = []
                this.getDetailShop()
                this.$EventBus.$emit('AuthorityUser')
                this.$EventBus.$emit('getUserDetail')
                if (this.MobileSize) {
                  this.$router.push({ path: '/adminShopManageMobile' }).catch(() => {})
                } else {
                  this.$router.push({ path: '/adminShopManage' }).catch(() => {})
                }
              } else {
                this.$swal.fire({ text: 'บันทึกข้อมูลไม่สำเร็จ', icon: 'error', timer: 2500, showConfirmButton: false })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistrict
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.district
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.province
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcode)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistrict)
      this.checkdistrictConfirm(this.district)
      this.checkprovinceConfirm(this.province)
      this.checkzipcodeConfirm(this.zipcode)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    }
  }
}
</script>
<style lans="scss">
  .v-application .success--text {
      color: #27AB9C !important;
      caret-color: #27AB9C !important;
  }
</style>
<style scoped>
.v-input--selection-controls {
  margin-top: 0px;
  padding-top: 0px;
}
</style>

<style>
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
