<template>
  <v-card elevation="0" width="100%" height="100%">
    <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="pt-6">รายการร้องของวดใหม่แบบเครดิตเทอม</v-card-title>
    <v-card-text>
      <v-row no-gutters>
        <v-col cols="12" class="py-0 pr-2">
          <a-tabs @change="getRequest">
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countall }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countWaiting }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countActive }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">ปฏิเสธ <a-tag color="#F5222D" style="border-radius: 8px;">{{ countInactive }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#F5222D" style="border-radius: 8px;">{{ countCancel }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="6" sm="6" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3 pt-3' : 'pl-2 pr-2 mb-3 pt-3'">
          <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากชื่อบริษัทหรือรหัสการสั่งซื้อ" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col cols="12" class="pt-3 pb-3" v-if="disableTable === true">
          <v-row dense>
            <v-col cols="12" md="6" sm="12" class="pt-2">
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 700" v-if="StateStatus === 0">คำร้องขออนุมัติเครดิตเทอมทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 700" v-else-if="StateStatus === 1">คำร้องขออนุมัติเครดิตเทอมที่รออนุมัติทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 700" v-else-if="StateStatus === 2">คำร้องขออนุมัติเครดิตเทอมที่อนุมัติแล้วทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 700" v-else-if="StateStatus === 3">คำร้องขออนุมัติเครดิตเทอมที่ปฏิเสธทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 700" v-else-if="StateStatus === 4">คำร้องขออนุมัติเครดิตเทอมที่ยกเลิกทั้งหมด {{ showCountRequest }} รายการ</span>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-data-table
           :headers="headers"
           :items="ListData"
           :search="search"
           style="width:100%;"
           height="100%"
           :page.sync="page"
           @pagination="countRequest"
           no-results-text="ไม่พบคำร้องขออนุมัติเครดิตเทอมษ"
           no-data-text="ไม่มีคำร้องขออนุมัติเครดิตเทอม"
           :update:items-per-page="itemsPerPage"
           :items-per-page="10"
           class="elevation-1 mt-4"
           v-if="disableTable === true"
          >
            <template v-slot:[`item.request_term`]="{ item }">
              <span class="ma-2" v-if="item.request_term !== null">{{item.request_term}}</span>
              <span class="ma-2" v-else> - </span>
            </template>
            <template v-slot:[`item.request_status`]="{ item }">
              <span v-if="item.request_status === 'Approved'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
              </span>
              <span v-else-if="item.request_status === 'Pending'">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
              </span>
              <span v-else-if="item.request_status === 'Not Approved'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ปฏิเสธ</v-chip>
              </span>
              <span v-else-if="item.request_status === 'Cancel'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ยกเลิก</v-chip>
              </span>
            </template>
            <template v-slot:[`item.manages`]="{ item }">
              <v-row>
                <span class="ma-2"
                  style="line-height: 22px; color:  #27AB9C; cursor: pointer"
                  @click="RequestDetail(item)">รายละเอียด <v-icon size="15" color="#27AB9C">
                    mdi-chevron-right</v-icon>
                </span>
              </v-row>
            </template>
          </v-data-table>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีคำร้องขออนุมัติเครดิตเทอม</b></h2>
        </v-col>
      </v-row>
    </v-card-text>

    <!-- Modal show detail of request -->
    <v-dialog v-model="ModalDetailRequest" width="730px" persistent>
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">คำร้องขออนุมัติเครดิตเทอม</font>
          </span>
          <v-btn icon dark @click="ModalDetailRequest = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row class="mt-2">
            <v-col cols="12" md="12" sm="12" class="pb-0">
              <v-row dense justify="start">
                <v-col cols="12" md="2" sm="6">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ราคาต่องวด :</p>
                </v-col>
                <v-col cols="12" md="4" sm="6" class="px-0">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                    <b v-if="DetailList.type === 'WithPrice'">กำหนดราคาเอง</b>
                    <b v-else>ราคาเท่ากันทุกงวด</b>
                  </p>
                </v-col>
                <v-col cols="12" md="6" sm="6" align="end">
                  <p style="margin-bottom:0; font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ :
                    <span v-if="DetailList.request_status === 'Pending'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                      <v-chip class="ma-2" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
                    </span>
                    <span v-if="DetailList.request_status === 'Approved'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                      <v-chip class="" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
                    </span>
                    <span v-if="DetailList.request_status === 'Not Approved'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                      <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ปฏิเสธ</v-chip>
                    </span>
                    <span v-if="DetailList.request_status === 'Cancel'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                      <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ยกเลิก</v-chip>
                    </span>
                  </p>
                </v-col>
              </v-row>
              <v-row dense justify="start">
                <v-col cols="12" md="2" sm="6">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">จำนวนงวด :</p>
                </v-col>
                <v-col cols="12" md="6" sm="6" class="px-0">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                    <b>  {{DetailList.request_term}}</b>
                  </p>
                </v-col>
              </v-row>
              <v-row dense justify="start">
                <v-col cols="12" md="2" sm="6">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เหตุผลร้องขอ :</p>
                </v-col>
                <v-col cols="12" md="6" sm="6" class="px-0">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                    <b v-if="DetailList.description !== ''">{{DetailList.reason}}</b>
                    <b v-else>-</b>
                  </p>
                </v-col>
              </v-row>
              <v-row dense justify="start">
                <v-col cols="12" md="2" sm="6">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">คำอธิบาย :</p>
                </v-col>
                <v-col cols="12" md="6" sm="6" class="px-0">
                  <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                    <b v-if="DetailList.reason !== ''">{{DetailList.description}}</b>
                    <b v-else>-</b>
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pt-0">
              <v-row dense>
                <v-col cols="12">
                  <v-data-table
                  :headers="headersTerm"
                  :items="DetailList.price_list"
                  color="blue-grey lighten-2"
                  style="width:100%;"
                  height="100%"
                  class="elevation-1 mt-4 row-height-180"
                  hide-default-footer
                  :items-per-page="12"
                  >
                    <template v-slot:[`item.term_price`]="{ item }">
                      <span>{{ parseFloat(item.term_price).toFixed(2) }}</span>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
              <v-row dense style="background-color:#FAFBFB; border-radius:8px;">
                <v-col align="right">
                  <span  style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">ราคารวมทั้งสิ้น </span>
                  <span  style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;"> <b>{{ parseFloat(DetailList.total_amount).toFixed(2) }}</b> </span>
                  <span  style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"> บาท</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col align="right" v-if="DetailList.request_status === 'Pending'">
                  <v-btn dense dark outlined color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--tex" @click="dialogCancel = true">
                    ปฏิเสธ
                  </v-btn>
                  <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="dialogApprove = true">
                    อนุมัติ
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- dialog comfirm Approve -->
    <v-dialog  v-model="dialogApprove" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">อนุมัติคำร้องขอเครดิตเทอม</font></span>
          <v-btn
            icon
            dark
            @click="dialogApprove = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/><br/>
        <v-card-text >
          <span>
            คุณต้องการอนุมัติคำขอนี้<br/>
            คุณต้องการทำรายการนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogApprove = false">ยกเลิก</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Approve('Approved')">อนุมัติ</v-btn>
       </v-container>
      </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- dialog comfirm cancle -->
    <v-dialog  v-model="dialogCancel" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">ปฏิเสธคำร้องขออนุมัติเครดิตเทอม</font></span>
          <v-btn
            icon
            dark
            @click="dialogCancel = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/><br/>
        <v-card-text >
          <span>
            คุณต้องการปฏิเสธคำขอนี้<br/>
            คุณต้องการทำรายการนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogCancel = false">ยกเลิก</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Approve('Not Approved')">ตกลง</v-btn>
       </v-container>
      </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      shopDetail: '',
      order_number: '',
      total_price: '',

      // modal,dialog,table
      disableTable: false,
      ModalDetailRequest: false,
      dialogApprove: false,
      dialogCancel: false,

      // tab bar------------------------------------------------------------------------------------------
      checkQuantity: false,
      showCountRequest: 0,
      StateStatus: 0,
      countall: 0,
      countWaiting: 0,
      countActive: 0,
      countInactive: 0,
      countCancel: 0,

      // table list special price---------------------------------------------
      search: '',
      page: 1,
      itemsPerPage: 10,
      requestList: [],
      ListData: [],
      headers: [
        { text: 'ชื่อบริษัทผู้ซื้อ', value: 'company_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ร้องขอ', value: 'created_at', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนงวด', value: 'request_term', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'request_status', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'การจัดการ', value: 'manages', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' }
      ],

      // table detail special price--------------------------------------------------
      DetailList: [],
      headersTerm: [
        { text: 'งวดที่', value: 'term', width: '40%', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนเงิน', value: 'term_price', width: '20%', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('checkpath')
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    this.getListRequest()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ListRequestNewTermMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ListRequestNewTerm' }).catch(() => {})
      }
    },
    StateStatus (val) {
      // console.log('state is', val)
      if (val === 0) {
        this.ListData = this.requestList.all !== undefined ? this.requestList.all.map(x => {
          return {
            company_id: x.company_id,
            company_name: x.company_name,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            created_by: x.created_by,
            description: x.description,
            id: x.id,
            old_term: x.old_term,
            order_number: x.order_number,
            price_list: x.price_list,
            reason: x.reason,
            request_status: x.request_status,
            request_term: x.request_term,
            seller_shop_id: x.seller_shop_id,
            total_amount: x.total_amount,
            type: x.type,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            updated_by: x.updated_by
          }
        }) : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.ListData = this.requestList.pending !== undefined ? this.requestList.pending.map(x => {
          return {
            company_id: x.company_id,
            company_name: x.company_name,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            created_by: x.created_by,
            description: x.description,
            id: x.id,
            old_term: x.old_term,
            order_number: x.order_number,
            price_list: x.price_list,
            reason: x.reason,
            request_status: x.request_status,
            request_term: x.request_term,
            seller_shop_id: x.seller_shop_id,
            total_amount: x.total_amount,
            type: x.type,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            updated_by: x.updated_by
          }
        }) : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.ListData = this.requestList.approve !== undefined ? this.requestList.approve.map(x => {
          return {
            company_id: x.company_id,
            company_name: x.company_name,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            created_by: x.created_by,
            description: x.description,
            id: x.id,
            old_term: x.old_term,
            order_number: x.order_number,
            price_list: x.price_list,
            reason: x.reason,
            request_status: x.request_status,
            request_term: x.request_term,
            seller_shop_id: x.seller_shop_id,
            total_amount: x.total_amount,
            type: x.type,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            updated_by: x.updated_by
          }
        }) : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.ListData = this.requestList.not_approve !== undefined ? this.requestList.not_approve.map(x => {
          return {
            company_id: x.company_id,
            company_name: x.company_name,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            created_by: x.created_by,
            description: x.description,
            id: x.id,
            old_term: x.old_term,
            order_number: x.order_number,
            price_list: x.price_list,
            reason: x.reason,
            request_status: x.request_status,
            request_term: x.request_term,
            seller_shop_id: x.seller_shop_id,
            total_amount: x.total_amount,
            type: x.type,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            updated_by: x.updated_by
          }
        }) : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 4) {
        this.ListData = this.requestList.cancel !== undefined ? this.requestList.cancel.map(x => {
          return {
            company_id: x.company_id,
            company_name: x.company_name,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            created_by: x.created_by,
            description: x.description,
            id: x.id,
            old_term: x.old_term,
            order_number: x.order_number,
            price_list: x.price_list,
            reason: x.reason,
            request_status: x.request_status,
            request_term: x.request_term,
            seller_shop_id: x.seller_shop_id,
            total_amount: x.total_amount,
            type: x.type,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            updated_by: x.updated_by
          }
        }) : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    getRequest (item) {
      this.StateStatus = item
      // i dnk
      this.page = 1
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getListRequest () {
      var data = {
        seller_shop_id: this.shopDetail.id
      }
      await this.$store.dispatch('actionsListRequestChangeTermBySellerShop', data)
      var response = await this.$store.state.ModuleShop.stateListRequestChangeTermBySellerShop
      if (response.result === 'SUCCESS') {
        this.requestList = response.data
        if (this.requestList !== '') {
          this.ListData = this.requestList
          this.countall = this.ListData.all.length
          this.countWaiting = this.ListData.pending.length
          this.countActive = this.ListData.approve.length
          this.countInactive = this.ListData.not_approve.length
          this.countCancel = this.ListData.cancel.length
          // console.log(this.requestList)
          if (this.StateStatus === 0) {
            this.ListData = this.requestList.all.map(x => {
              return {
                company_id: x.company_id,
                company_name: x.company_name,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                created_by: x.created_by,
                description: x.description,
                id: x.id,
                old_term: x.old_term,
                order_number: x.order_number,
                price_list: x.price_list,
                reason: x.reason,
                request_status: x.request_status,
                request_term: x.request_term,
                seller_shop_id: x.seller_shop_id,
                total_amount: x.total_amount,
                type: x.type,
                updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                updated_by: x.updated_by
              }
            })
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            this.ListData = this.requestList.pending.map(x => {
              return {
                company_id: x.company_id,
                company_name: x.company_name,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                created_by: x.created_by,
                description: x.description,
                id: x.id,
                old_term: x.old_term,
                order_number: x.order_number,
                price_list: x.price_list,
                reason: x.reason,
                request_status: x.request_status,
                request_term: x.request_term,
                seller_shop_id: x.seller_shop_id,
                total_amount: x.total_amount,
                type: x.type,
                updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                updated_by: x.updated_by
              }
            })
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            this.ListData = this.requestList.approve.map(x => {
              return {
                company_id: x.company_id,
                company_name: x.company_name,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                created_by: x.created_by,
                description: x.description,
                id: x.id,
                old_term: x.old_term,
                order_number: x.order_number,
                price_list: x.price_list,
                reason: x.reason,
                request_status: x.request_status,
                request_term: x.request_term,
                seller_shop_id: x.seller_shop_id,
                total_amount: x.total_amount,
                type: x.type,
                updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                updated_by: x.updated_by
              }
            })
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 3) {
            this.ListData = this.requestList.not_approve.map(x => {
              return {
                company_id: x.company_id,
                company_name: x.company_name,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                created_by: x.created_by,
                description: x.description,
                id: x.id,
                old_term: x.old_term,
                order_number: x.order_number,
                price_list: x.price_list,
                reason: x.reason,
                request_status: x.request_status,
                request_term: x.request_term,
                seller_shop_id: x.seller_shop_id,
                total_amount: x.total_amount,
                type: x.type,
                updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                updated_by: x.updated_by
              }
            })
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 4) {
            this.ListData = this.requestList.cancel.map(x => {
              return {
                company_id: x.company_id,
                company_name: x.company_name,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                created_by: x.created_by,
                description: x.description,
                id: x.id,
                old_term: x.old_term,
                order_number: x.order_number,
                price_list: x.price_list,
                reason: x.reason,
                request_status: x.request_status,
                request_term: x.request_term,
                seller_shop_id: x.seller_shop_id,
                total_amount: x.total_amount,
                type: x.type,
                updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                updated_by: x.updated_by
              }
            })
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          }
        } else {
          this.disableTable = false
        }
      }
      // console.log('send data 2 API list', data)
      // console.log('res data fm API list', response)
    },
    async RequestDetail (item, status) {
      this.DetailList = []
      this.order_number = ''
      this.order_number = item.order_number
      this.DetailList = item
      this.ModalDetailRequest = true
      // console.log('DetailList', this.DetailList)
    },
    async Approve (status) {
      this.dialogApprove = false
      this.dialogCancel = false
      this.ModalDetailRequest = false
      this.$store.commit('openLoader')
      var dataDetail = {
        order_number: this.order_number,
        seller_shop_id: this.shopDetail.id,
        status_approval: status
      }
      await this.$store.dispatch('actionsUpdateRequestCreditTerm', dataDetail)
      var response = await this.$store.state.ModuleShop.stateUpdateRequestCreditTerm
      this.$store.commit('closeLoader')
      if (response.result === 'SUCCESS') {
        if (status === 'Approved') {
          var msg = 'อนุมัติ'
        } else if (status === 'Not Approved') {
          msg = 'ปฏิเสธ'
        }
        this.$swal.fire({ text: `${msg}คำขอสำเร็จ`, icon: 'success', timer: 2500, showConfirmButton: false })
        this.getListRequest()
        this.order_number = ''
        this.DetailList = []
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: `${msg}ไม่สำเร็จ ${response.message}`
        })
      }
      // console.log('send data 2 Approve API list', dataDetail)
      // console.log('res data fm Approve API list', response)
    }
  }
}
</script>
