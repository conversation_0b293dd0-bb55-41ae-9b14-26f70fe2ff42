<template>
  <v-container v-if="checkisBuyer === true" class="pa-4">
    <v-card :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']" elevation="0">
      <div>
        <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
          ตั้งค่าการชำระเงิน
        </v-card-title>
        <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
          <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
          โปรแกรม Affiliate
        </v-card-title>

        <v-divider class="my-4"></v-divider>

        <div class="px-6">
          <span class="text-start" style="font-size: 20px; font-weight: 700;">ประเภทบัญชี</span>

          <v-form ref="FormbuyerDetailType" :lazy-validation="lazy" v-model="FormbuyerDetailType">
            <v-row dense>
              <v-col cols="12" class="mt-4 reduce-spacing">
                <span class="labelInputSize" style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ประเภทบัญชี <span style="color: red;">*</span></span>
                <v-select :items="accountTypes" item-text="text" item-value="value" placeholder="เลือกประเภทบัญชี" v-model="account_type" outlined dense :rules="Rules.account_type"></v-select>
              </v-col>
            </v-row>
          </v-form>

          <v-divider class="my-4"></v-divider>

          <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลการชำระเงิน</span>

          <v-form ref="FormbuyerDetailPay" :lazy-validation="lazy" v-model="FormbuyerDetailPay">
            <v-row dense class="mt-4 reduce-spacing">
              <v-col cols="12">
                <v-row dense>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อบัญชี <span style="color: red;">*</span></span>
                    <v-text-field class="input-text" placeholder="ระบุชื่อบัญชี" v-model="bank_username" outlined dense :rules="Rules.bank_username"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อธนาคาร <span style="color: red;">*</span></span>
                    <v-select :items="itemsBank" item-text="name" item-value="code" :return-object="true" placeholder="เลือกชื่อธนาคาร" v-model="selectedBank" outlined dense :rules="Rules.selectedBank"></v-select>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อสาขาธนาคาร <span style="color: red;">*</span></span>
                    <v-text-field class="input-text" placeholder="ระบุชื่อสาขาธนาคาร" v-model="bank_branch" outlined dense :rules="Rules.bank_branch"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมายเลขบัญชีธนาคาร <span style="color: red;">*</span></span>
                    <v-text-field class="input-text" placeholder="ระบุหมายเลขบัญชีธนาคาร" v-model="bank_no"
                      outlined dense :rules="Rules.bank_no">
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="6" style="display: flex; align-items: center;">
                    <span style="padding-right: 10px;">รูปหน้าบัญชีธนาคาร <span style="color: red;">*</span></span>
                    <v-btn @click="triggerFileInput()" color="#27AB9C" class="white--text rounded-button">อัปโหลด</v-btn>
                    <input type="file" ref="fileInput" @change="handleFileUpload($event)" style="display: none;">
                  </v-col>
                  <v-col cols="6">
                    <div v-if="bookbankImageUrl" class="mt-2" style="display: flex; justify-content: space-evenly;">
                      <v-card class="d-flex justify-center align-center mb-6" style="max-width: 300px; max-height: 300px; overflow: hidden;">
                        <img :src="bookbankImageUrl" style="width: 100%; height: 100%; object-fit: contain;" @click="viewImageBookbank">
                      </v-card>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-form>
          <v-divider class="my-8"></v-divider>
        </div>
        <div class="px-6">
          <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลภาษี</span>
          <v-form ref="FormbuyerDetailTax" :lazy-validation="lazy" v-model="FormbuyerDetailTax">
            <v-row dense>
              <v-col cols="12" class="mt-4 reduce-spacing">
                <span>หมายเลขประจำตัวผู้เสียภาษี <span style="color: red;">*</span></span>
                <v-text-field class="input-text" placeholder="ระบุเลขประจำตัวผู้เสียภาษี" v-model="national_id" :maxLength="13" counter="13" outlined dense @input="validateTaxID()" :rules="Rules.national_id"></v-text-field>
              </v-col>
              <!-- <v-col cols="6" style="display: flex; align-items: center;">
                <span style="padding-right: 10px;">รูปบัตรประจำตัวประชาชน <span style="color: red;">*</span></span>
                <v-btn @click="triggerFileInputTax()" color="#27AB9C" class="white--text rounded-button">อัปโหลด</v-btn>
                <input type="file" ref="fileInputTax" @change="handleFileUploadTax($event)" style="display: none;">
              </v-col>
              <v-col cols="6">
                <div v-if="taxImageUrl" class="mt-2" style="display: flex; justify-content: space-evenly;">
                  <v-card class="d-flex justify-center align-center mb-6" style="max-width: 300px; max-height: 300px; overflow: hidden;">
                    <img :src="taxImageUrl" style="width: 100%; height: 100%; object-fit: contain;" @click="viewImageTax">
                  </v-card>
                </div>
              </v-col> -->
            </v-row>
            <v-divider class="my-6"></v-divider>
          </v-form>
        </div>
      </div>

      <div class="px-6">
        <span class="text-start" style="font-size: 20px; font-weight: 700;">ที่อยู่ตามบัตรประชาชน</span>
          <v-form ref="FormbuyerDetailAddress" :lazy-validation="lazy" v-model="FormbuyerDetailAddress">
            <v-row dense class="mt-4 reduce-spacing">
              <v-col cols="12">
                <div class="borderd-content">
                  <!-- แถวที่ 1 -->
                  <v-row dense>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">เลขที่ <span style="color: red;"> *</span></span>
                      <v-text-field v-model="houseNo" placeholder="ระบุเลขที่อยู่" dense outlined :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ห้องเลขที่</span>
                      <v-text-field v-model="roomNo" placeholder="ระบุเลขห้อง" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชั้นที่</span>
                      <v-text-field v-model="floor" placeholder="ระบุชั้น" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 2 -->
                  <v-row dense>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">อาคาร</span>
                      <v-text-field v-model="buildingName" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมู่บ้าน</span>
                      <v-text-field v-model="mooBan" placeholder="ชื่อหมู่บ้าน" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมู่ที่</span>
                      <v-text-field v-model="mooNo" placeholder="ระบุหมู่" dense outlined :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 3 -->
                  <v-row dense>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ตรอก/ซอย</span>
                      <v-text-field v-model="soi" placeholder="ระบุตรอก,ซอย" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">แยก</span>
                      <v-text-field v-model="yaek" placeholder="ระบุแยก" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ถนน</span>
                      <v-text-field v-model="street" placeholder="ระบุชื่อถนน" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 4 -->
                  <v-row dense>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">แขวง/ตำบล<span style="color: red;"> *</span></span>
                      <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                      <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">เขต/อำเภอ<span style="color: red;"> *</span></span>
                      <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                      <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 5 -->
                  <v-row dense>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">จังหวัด<span style="color: red;"> *</span></span>
                      <addressinput-province label="" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                      <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">รหัสไปรษณีย์<span style="color: red;"> *</span></span>
                      <addressinput-zipcode label="" v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                      <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </v-form>
          <v-divider class="my-4"></v-divider>
        </div>

      <div>
        <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
        ตั้งค่าบัญชีโซเชียลมีเดีย
        </v-card-title>

        <v-divider class="my-4"></v-divider>

        <div class="px-6">
          <v-form ref="FormbuyerDetailSocail" :lazy-validation="lazy" v-model="FormbuyerDetailSocail">
            <v-row>
              <v-col cols="12" class="reduce-spacing">
                <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">Facebook</span>
                <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Facebook" v-model="facebook_link" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
              <v-col cols="12" class="reduce-spacing">
                <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">TikTok</span>
                <v-text-field class="input-text" placeholder="ระบุลิ้งค์ TikTok" v-model="tiktok_link" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
              <v-col cols="12" class="reduce-spacing">
                <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">Youtube</span>
                <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Youtube" v-model="youtube_link" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
              <v-col cols="12" class="reduce-spacing">
                <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">Instagram</span>
                <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Instagram" v-model="instagram_link" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
              <v-col cols="12" class="reduce-spacing">
                <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">Line</span>
                <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Line" v-model="line_link" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </div>
      </div>

      <v-divider class="my-4"></v-divider>
      <v-row class="align-center pl-10">
        <v-checkbox v-model="checkbox">
        <!-- <template v-slot:label>
          <div>
            รับทราบ
            <span style="text-decoration: underline; color:#27AB9C;" @click="showDialogConsent()">ข้อกำหนดดังกล่าว</span>
          </div>
        </template> -->
        </v-checkbox>
        รับทราบ<span class="pl-1" style="text-decoration: underline; color:#27AB9C; cursor: pointer;" @click="showDialogConsent()">ข้อกำหนดดังกล่าว</span>
      </v-row>
      <div style="display: flex; justify-content: center;">
        <v-btn :disabled="!checkbox || !formValid" dense color="#27AB9C" class="pl-8 pr-8 white--text" @click="confirmConsent()">ยืนยันการเข้าร่วม</v-btn>
      </div>
    </v-card>

    <v-dialog persistent v-model="showDialog" max-width="600px">
      <div class="d-flex justify-center">
        <v-card>
          <v-card style="overflow-x: hidden;">
            <v-row class="py-8" dense no-gutters>
              <v-img src="@/assets/inetlogo.png" contain height="58" width="121" position="center" class="mr-4"></v-img>
            </v-row>
            <v-card-text style="font-size: 18px; text-align:center; overflow: hidden;">{{title}}</v-card-text>
            <v-divider></v-divider>
            <v-card-text style="height: 300px;">
              <div v-html="this.detail" style="padding: 15px 0;"></div>
            </v-card-text>
            <v-divider></v-divider>
          </v-card>
          <v-card-actions style="justify-content: center; padding: 15px;">
            <v-btn class="px-5 white--text" color="#27AB9C" @click="showDialog = false">ปิด</v-btn>
          </v-card-actions>
        </v-card>
      </div>
    </v-dialog>

    <v-dialog v-model="dialogBookbank" max-width="500px">
      <v-card>
        <v-card-title class="headline" style="justify-content: center;">รูปบัญชีธนาคาร</v-card-title>
        <v-card-text class="d-flex justify-center">
          <img :src="bookbankImageUrl" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogBookbank = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogTax" max-width="500px">
      <v-card>
        <v-card-title class="headline" style="justify-content: center;">รูปบัตรประจำตัวประชาชน</v-card-title>
        <v-card-text class="d-flex justify-center">
          <img :src="taxImageUrl" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogTax = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import Vue from 'vue'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode } from '@/services'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      checkisBuyer: false,
      lazy: false,
      dialogBookbank: false,
      dialogTax: false,
      showDialog: false,
      checkbox: false,
      FormbuyerDetailType: false,
      FormbuyerDetailPay: false,
      FormbuyerDetailSocail: false,
      FormbuyerDetailTax: false,
      FormbuyerDetailAddress: false,
      accountTypes: [
        { text: 'ออมทรัพย์', value: 'savings' },
        { text: 'ฝากประจำ', value: 'current' }
      ],
      checkeKYCUser: false,
      itemsBank: [],
      account_type: '',
      bank_username: '',
      selectedBank: null,
      bank_name: '',
      bank_code: '',
      bank_branch: '',
      bank_no: '',
      bookbankImage: null,
      bookbankImageUrl: null,
      taxImage: null,
      taxImageUrl: null,
      national_id: '',
      facebook_link: '',
      tiktok_link: '',
      youtube_link: '',
      instagram_link: '',
      line_link: '',
      title: '',
      detail: '',
      // address
      houseNo: '',
      roomNo: '',
      floor: '',
      buildingName: '',
      mooBan: '',
      mooNo: '',
      soi: '',
      yaek: '',
      street: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        account_type: [
          v => !!v || 'กรุณาเลือกประเภทบัญชี'
        ],
        bank_username: [
          v => !!v || 'กรุณาระบุชื่อบัญชี'
        ],
        selectedBank: [
          v => !!v || 'กรุณาเลือกชื่อธนาคาร'
        ],
        bank_branch: [
          v => !!v || 'กรุณาระบุชื่อสาขาธนาคาร'
        ],
        bank_no: [
          v => !!v || 'กรุณาระบุหมายเลขบัญชีธนาคาร',
          v => /^[0-9]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข'
        ],
        national_id: [
          v => !!v || 'กรุณาระบุเลขประจำตัวผู้เสียภาษี',
          v => (/^\d+$/.test(v) && v.length === 13) || 'เลขประจำตัวผู้เสียภาษีต้องมี 13 หลักและเป็นตัวเลขเท่านั้น',
          v => this.validNationalID(v) || 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
        ],
        // address
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่',
          v => v.charAt(0) !== '-' || 'กรุณากรอกข้อมูลให้ถูกต้อง',
          v => (v.split('').filter(char => char === '-').length <= 1) || 'ระบุข้อมูลไม่ถูกต้อง',
          v => (/^[-0-9,-/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9,-/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        maxText: [
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    formValid () {
      return this.FormbuyerDetailType && this.FormbuyerDetailPay && this.FormbuyerDetailTax && this.bookbankImage && this.FormbuyerDetailAddress
    }
  },
  watch: {
    selectedBank (newVal) {
      if (newVal) {
        this.bank_name = newVal.name
        this.bank_code = newVal.code
      } else {
        this.bank_name = ''
        this.bank_code = ''
      }
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAccount')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      await this.getConsent()
      // this.checkeKYC()
      this.getText()
      this.AffiliateListBank()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateTaxID () {
      if (this.validNationalID(this.national_id)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    async getConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      this.isBuyer = res.isBuyer
      if (this.isBuyer === '1') {
        this.checkisBuyer = false
        if (this.MobileSize) {
          this.$router.push({ path: '/showShopSellerAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/showShopSellerAffiliate' }).catch(() => {})
        }
      } else {
        this.checkisBuyer = true
      }
      this.$store.commit('closeLoader')
    },
    async AffiliateListBank () {
      await this.$store.dispatch('actionsAffiliateListBank')
      const response = await this.$store.state.ModuleAffiliate.stateAffiliateListBank
      if (response.result === 'SUCCESS') {
        this.itemsBank = await [...response.data]
      }
    },
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    triggerFileInput () {
      this.$refs.fileInput.click()
    },
    triggerFileInputTax () {
      this.$refs.fileInputTax.click()
    },
    handleFileUpload (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.bookbankImageUrl = reader.result
            this.bookbankImage = file
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500
        })
      }
    },
    handleFileUploadTax (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.taxImageUrl = reader.result
            this.taxImage = file
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500
        })
      }
    },
    async getText () {
      await this.$store.dispatch('actionsGetTextConsent')
      var res = await this.$store.state.ModuleUser.stateGetTextConsent
      this.title = res.data.title
      this.detail = res.data.consent_text
    },
    // async checkeKYC () {
    //   this.$store.commit('openLoader')
    //   // alert('*********')
    //   // this.checkeKYCUser = false
    //   await this.$store.dispatch('actionsCheckeKYC')
    //   var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
    //   // console.log('eKYC', response)

    //   if (response.result === 'SUCCESS') {
    //     this.$store.commit('closeLoader')
    //     this.checkeKYCUser = response.data.eKYC_approve === 'yes'
    //     // console.log('this.checkeKYCUser', this.checkeKYCUser)
    //     if (response.data.eKYC_approve === 'no') {
    //       this.$store.commit('closeLoader')
    //       this.checkeKYCUser = false
    //       if (this.MobileSize) {
    //         this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //       } else {
    //         this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //       }
    //     } else {
    //       if (response.data.eKYC_expire === 'yes') {
    //         this.$store.commit('closeLoader')
    //         if (this.MobileSize) {
    //           this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //         } else {
    //           this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //         }
    //       }
    //     }
    //   }
    //   this.$store.commit('closeLoader')
    // },
    showDialogConsent () {
      this.showDialog = true
    },
    async confirmConsent () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsCheckeKYC')
      // var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      // // console.log('eKYC', response)

      // if (response.result === 'SUCCESS') {
      //   this.$store.commit('closeLoader')
      //   this.checkeKYCUser = response.data.eKYC_approve === 'yes'
      //   // console.log('this.checkeKYCUser', this.checkeKYCUser)
      //   if (response.data.eKYC_approve === 'no') {
      //     this.$store.commit('closeLoader')
      //     this.checkeKYCUser = false
      //     if (this.MobileSize) {
      //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p>' + '<span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //       return
      //     } else {
      //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p>' + '<span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //       return
      //     }
      //   } else if (response.data.eKYC_expire === 'yes') {
      //     this.$store.commit('closeLoader')
      //     if (this.MobileSize) {
      //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p>' + '<span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //       return
      //     } else {
      //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p>' + '<span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //       return
      //     }
      //   }
      // }

      if (this.facebook_link !== '' || this.tiktok_link !== '' || this.youtube_link !== '' || this.instagram_link !== '' || this.line_link !== '') {
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        var userId = {
          user_id: onedata.user.user_id
        }
        await this.$store.dispatch('actionsAffiliateConsent', userId)
        this.responseUserid = await this.$store.state.ModuleAffiliate.stateAffiliateConsent

        var data = {
          user_id: onedata.user.user_id,
          approve_consent: true
        }

        // ต้อง Join consent ก่อนที่จะเพิ่มข้อมูล buyer
        await this.$store.dispatch('actionsAffiliateConfirmJoin', data)
        const responseConfirmJoin = await this.$store.state.ModuleAffiliate.stateAffiliateConfirmJoin

        const formData = new FormData()
        formData.append('user_id', onedata.user.user_id)
        formData.append('bank_name', this.bank_name)
        formData.append('bank_branch', this.bank_branch)
        formData.append('bank_username', this.bank_username)
        formData.append('bank_code', this.bank_code)
        formData.append('bank_no', this.bank_no)
        formData.append('account_type', this.account_type)
        formData.append('bookbank_image', this.bookbankImage)
        formData.append('national_id', this.national_id)
        formData.append('id_card_image', this.taxImage)
        formData.append('facebook_link', this.facebook_link)
        formData.append('tiktok_link', this.tiktok_link)
        formData.append('youtube_link', this.youtube_link)
        formData.append('instagram_link', this.instagram_link)
        formData.append('line_link', this.line_link)
        // address
        formData.append('house_no', this.houseNo)
        formData.append('room_no', this.roomNo)
        formData.append('floor', this.floor)
        formData.append('building', this.buildingName)
        formData.append('moo_ban', this.mooBan)
        formData.append('moo_no', this.mooNo)
        formData.append('soi', this.soi)
        formData.append('yaek', this.yaek)
        formData.append('street', this.street)
        formData.append('sub_district', this.subdistrict)
        formData.append('district', this.district)
        formData.append('province', this.province)
        formData.append('zip_code', this.zipcode)

        await this.$store.dispatch('actionsAffiliateBuyerCreateDetail', formData)
        this.responseBuyerCreateDetail = await this.$store.state.ModuleAffiliate.stateAffiliateBuyerCreateDetail
        console.log('responseConfirmJoin', responseConfirmJoin)

        if (responseConfirmJoin.message === 'User joined') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'ผู้ใช้ได้เข้าร่วม'
          })
          await this.$EventBus.$emit('GetConsent')
          this.$router.push('/showShopSellerAffiliate')
        } else if (responseConfirmJoin.message === 'This user is already joined') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'info',
            title: 'ผู้ใช้เข้าร่วมแล้ว'
          }).then(() => {
            this.$router.push('/showShopSellerAffiliate')
          })
        } else if (responseConfirmJoin.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true,
          //   icon: 'info',
          //   title: 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
          // })
        } else if (responseConfirmJoin.message === 'Failed to join') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'info',
            title: 'ไม่สามารถเข้าร่วมได้'
          })
        } else if (responseConfirmJoin.message === 'An error has occurred. Please try again in an hour or two.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: `${responseConfirmJoin.message}`
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          html: '<h3>กรุณากรอกอย่างน้อย 1 บัญชีโซเชียล</h3>'
        })
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    viewImageBookbank () {
      this.dialogBookbank = true
    },
    viewImageTax () {
      this.dialogTax = true
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipcode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}
</style>
