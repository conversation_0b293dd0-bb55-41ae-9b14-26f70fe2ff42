<template>
  <v-dialog v-model="visible" width="425" persistent>
    <v-card style="border-radius: 24px; background-color: #27AB9C;">
      <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
        <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">เงื่อนไขโค้ดส่วนลด
        </span>
        <v-btn icon dark @click="$emit('close')">
          <v-icon color="#FFFFFF">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card class="pa-0" style="background-color: #FFFFFF;" v-if="conditionData !== ''">
        <v-col cols="12">
          <v-row no-gutters>
            <v-col cols="12" class="pb-2">
              <v-col class="couponIMGMobile">
                <v-col cols="12" md="12" class="pt-0" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                  <v-row no-gutters>
                    <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                      <v-row no-gutters v-if="conditionData.coupon_type === 'free_product'">
                        <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                          <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  overflow: hidden; white-space: nowrap; text-overflow: ellipsis; max-width: 120px;"> {{conditionData.coupon_name }}</span><br>
                          <span class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;"></span><br>
                          <span v-if="conditionData.useEndDate !== null" class="mb-4" style="font-size: 10px;"> ใช้ได้ถึง {{conditionData.useEndDate}}</span>
                          <span v-else style="font-size: 10px;">ไม่มีวันหมดอายุ</span>
                          <v-col cols="12" md="12" class="pa-0">
                            <!-- <v-row no-gutters>
                              <v-col cols="8" md="8" class="pa-0 pt-2">
                                <v-progress-linear color="#C04F36" class="powerOfEndDate" :value="item.powerOfEndDate"></v-progress-linear>
                              </v-col>
                              <v-col cols="4" md="4" class="pl-2 pb-2"> <span style="font-size: 8px; color: #27AB9C;">ใช้แล้ว {{item.powerOfEndDate}} %</span></v-col>
                            </v-row> -->
                            <v-row no-gutters>
                              <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="conditionData.powerOfEndDate">
                                <template #progress="{ value }">
                                <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                </template>
                              </v-progress-linear>
                              <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">ใช้แล้ว {{conditionData.powerOfEndDate}} %</span>
                            </v-row>
                          </v-col>
                        </v-col>
                        <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                          <span style="color: #FB9372; font-size: 12px;"> {{conditionData.coupon_type === 'free_product' ? 'คูปอง': 'ส่วนลด'}}</span><br>
                          <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> แถมฟรี</span><br>
                          <v-btn v-if="conditionData.is_collected === 'N'" @click="getCouponsCode(conditionData)" color="#F56E22" style="color:white;" small rounded>เก็บโค้ด</v-btn>
                          <v-btn v-else disabled color="#F56E22" style="color:white;" small rounded>เก็บโค้ดแล้ว</v-btn>
                        </v-col>
                      </v-row>
                      <v-row no-gutters v-else>
                        <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                          <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  overflow: hidden; white-space: nowrap; text-overflow: ellipsis; max-width: 120px;">{{conditionData.coupon_name }}</span><br>
                          <span> ขั้นต่ำ {{conditionData.spend_minimum}} บาท</span><br>
                          <span class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;"></span><br>
                          <span v-if="conditionData.useEndDate !== null" class="mb-4" style="font-size: 10px;"> ใช้ได้ถึง {{conditionData.useEndDate}}</span>
                          <span v-else style="font-size: 10px;">ไม่มีวันหมดอายุ</span>
                          <v-col cols="12" md="12" class="pa-0">
                            <!-- <v-row no-gutters>
                              <v-col cols="8" md="8" class="pa-0 pt-2">
                                <v-progress-linear color="#C04F36" class="powerOfEndDate" :value="item.powerOfEndDate"></v-progress-linear>
                              </v-col>
                              <v-col cols="4" md="4" class="pl-2 pb-2"> <span style="font-size: 8px; color: #27AB9C;">ใช้แล้ว {{item.powerOfEndDate}} %</span></v-col>
                            </v-row> -->
                            <v-row no-gutters>
                              <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="conditionData.powerOfEndDate">
                                <template #progress="{ value }">
                                <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                </template>
                              </v-progress-linear>
                              <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">ใช้แล้ว {{conditionData.powerOfEndDate}} %</span>
                            </v-row>
                          </v-col>
                        </v-col>
                        <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                          <span style="color: #FB9372; font-size: 12px;"> {{conditionData.coupon_type === 'free_shipping' ? 'โค้ดส่งฟรี' : 'ส่วนลด'}}</span><br>
                          <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{conditionData.discount_amount}} {{conditionData.discount_type === 'percent'? '%':'บาท'}}</span><br>
                          <v-btn v-if="conditionData.is_collected === 'N'" @click="getCouponsCode(conditionData)" color="#F56E22" style="color:white;" small rounded>เก็บโค้ด</v-btn>
                          <v-btn v-else disabled color="#F56E22" style="color:white;" small rounded>เก็บโค้ดแล้ว</v-btn>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-col>
            </v-col>
            <v-col>
              <v-col cols="12">
                <span style="font-weight: 600;">ประเภทโปรโมชัน</span><br>
                <span class="pl-2">{{conditionData.coupon_type === 'free_product'? 'แถมฟรี':conditionData.coupon_type === 'discount'? 'ส่วนลดราคาสินค้า':'ส่วนลดค่าขนส่ง'}}</span>
              </v-col>
              <v-col cols="12">
                <span style="font-weight: 600;">สิทธิ์การใช้คูปอง</span><br>
                <span v-if="conditionData.quota === null" class="pl-2">ไม่จำกัดสิทธิ์</span>
                <span v-else class="pl-2">{{conditionData.quota}} คูปอง</span>
              </v-col>
              <v-col cols="12" v-if="conditionData.coupon_description !== null && conditionData.coupon_description !== ''">
                <span style="font-weight: 600;">รายละเอียดโค้ดส่วนลด</span><br>
                <span class="pl-2" v-html="conditionData.coupon_description"></span>
              </v-col>
              <v-col cols="12" v-if="conditionData.coupon_product_free">
                <span style="font-weight: 600;">รายละเอียดของแถม</span><br>
                <v-row no-gutters class="pa-0" v-for="(buyItem, index) in conditionData.coupon_product_free.product_details_buy" :key="'pair-' + index">
                  <template v-if="conditionData.coupon_product_free.product_details_free[index]">
                    <!-- รายการสั่งซื้อ -->
                    <v-col cols="12" class="pa-0 pt-2">
                      <span style="font-weight: 600;" class="pl-2">รายการสั่งซื้อ</span>
                      <v-row no-gutters class="pa-0">
                        <v-col cols="4" class="pa-0" style="justify-items: center;">
                          <v-img height="100" width="100" :src="buyItem.image"></v-img>
                        </v-col>
                        <v-col cols="8" class="pa-0">
                          <span><b>ชื่อสินค้า: </b>{{ buyItem.name }}</span><br>
                          <span><b>ตัวเลือก: </b>{{ buyItem.attribute_priority_1 }}{{ buyItem.attribute_priority_2 === '-' ? '' : `, ${buyItem.attribute_priority_2}` }}</span><br>
                          <span><b>จำนวน: </b>{{ buyItem.quantity }}</span>
                        </v-col>
                      </v-row>
                    </v-col>
                    <!-- ของแถม -->
                    <v-col cols="12" class="pa-0 pt-2">
                      <!-- <span style="font-weight: 600;" class="pl-2">ของแถม</span> -->
                      <v-col class="text-center rounded-b-0" style="background-color: #E9F6F5; border-top-right-radius: 8px; border-top-left-radius: 8px;">
                        <span style="font-weight: 600; font-size: 14px; color: #27AB9C;" class="">ของแถม</span>
                      </v-col>
                      <v-row no-gutters class="pa-0 py-2" style="background-color: #F5F5F5; border: 2px solid #E9F6F5; border-top-width: 0px;">
                        <v-col cols="4" class="pa-0" style="justify-items: center;">
                          <v-img height="100" width="100" :src="conditionData.coupon_product_free.product_details_free[index].image"></v-img>
                        </v-col>
                        <v-col cols="8" class="pa-0">
                          <span><b>ชื่อสินค้า: </b>{{ conditionData.coupon_product_free.product_details_free[index].name }}</span><br>
                          <span><b>ตัวเลือก: </b>{{ conditionData.coupon_product_free.product_details_free[index].attribute_priority_1 }}{{ conditionData.coupon_product_free.product_details_free[index].attribute_priority_2 === '-' ? '' : `, ${conditionData.coupon_product_free.product_details_free[index].attribute_priority_2}` }}</span><br>
                          <span><b>จำนวน: </b>{{ conditionData.coupon_product_free.product_details_free[index].quantity }}</span>
                        </v-col>
                      </v-row>
                    </v-col>
                    <!-- Divider -->
                    <v-col cols="12" v-if="index < conditionData.coupon_product_free.product_details_buy.length - 1" class="py-2">
                      <v-divider></v-divider>
                    </v-col>
                  </template>
                </v-row>
              </v-col>
            </v-col>
          </v-row>
        </v-col>
      </v-card>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    conditionData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    initialRoute () {
      return this.$store.state.ModuleGlobal.initialRoute
    },
    previousRoute () {
      return this.$store.state.ModuleGlobal.previousRoute
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped>
.couponAll {
  cursor: pointer;
}
.couponAll:hover {
  transform: scale(1.02) !important;
}
.progress-gradient {
width: 100%;
height: 100%;
border-radius: 48px;
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.vProgressLinearDesk {
  max-width: 4.5vw;
}
.vProgressLinearIped {
  max-width: 8vw;
}
.vProgressLinearMobile {
  max-width: 23vw;
}

.backIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/background.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
}
.couponIMGDesk{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  padding: 1%;
  /* width: 100%; */
}
</style>
