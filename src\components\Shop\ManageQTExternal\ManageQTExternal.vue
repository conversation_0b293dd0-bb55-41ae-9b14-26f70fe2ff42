<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการใบเสนอราคา</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backTomenu()">mdi-chevron-left</v-icon>จัดการใบเสนอราคา</v-card-title>
      <v-card-text>
        <v-row dense class="align-center">
          <span class="textStepOne pr-4">รูปภาพ Logo ใบเสนอราคา</span><span class="subtextStepOne">ขนาดไฟล์: สูงสุด 1 MB</span>
          <v-checkbox :class="MobileSize ? 'mt-2' : 'ml-4'" v-model="checkboxlogo" @click="GetDetailShop()" hide-details>
            <template v-slot:label>
              <div>
                <span>ใช้ข้อมูลจากจัดการร้านค้า</span>
              </div>
            </template>
          </v-checkbox>
        </v-row>
        <v-row dense class="my-2">
          <v-col cols="12" md="3" sm="12">
            <v-card elevation="0" outlined width="100%" :height="IpadSize || MobileSize ? '100%' : '321'" class="cardImageStyle">
              <v-card-text style="text-align: center;" class="px-2">
                <v-row justify="center">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NoImageShop.png" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-if="LogoImage.length === 0 || showError === true"></v-img>
                  <v-img :src="LogoImage[0].path" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-else></v-img>
                </v-row>
                <span class="textStepOne">Logo ใบเสนอราคา</span><br/>
                <span class="subtextStepOne" v-if="LogoImage.length === 0">( ขนาดรูปภาพ 244 x 244 pxและไฟล์นามสกุล .JPEG, .PNG, .JPG )</span><br/>
                <v-btn width="125" height="40" text rounded color="#1B5DD6" class="mt-4" @click="uploadImageShop()" v-if="LogoImage.length === 0"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                <v-file-input
                  v-model="DataImageShop"
                  :items="DataImageShop"
                  accept="image/*"
                  @change="UploadImageShop()"
                  @click="event => event.target.value = null"
                  id="imageShop"
                  :clearable="false"
                  style="display:none"
                />
                <!-- wait upload -->
                <v-row v-if="LogoImage.length !== 0 && show === true" class="pt-4">
                  <span class="textUploadnameImage" v-if="LogoImage[0].name !== null">{{ LogoImage[0].name.substring(0, 10) }}<span v-if="LogoImage[0].name.length > 10">...</span></span>
                  <v-spacer></v-spacer>
                  <span class="textUploadsizeImage pt-1">{{ Math.round(LogoImage[0].size / 1000) }} KB</span>
                </v-row>
                <v-row v-if="LogoImage.length !== 0 && show === true" class="pt-2">
                  <v-progress-linear :value="percentUpload" height="7" rounded :active="show"></v-progress-linear>
                </v-row>
                <v-row v-if="LogoImage.length !== 0 && show === true" class="pt-1">
                  <span class="textUploadsizeImage">กำลังอัปโหลด...</span>
                  <v-spacer></v-spacer>
                  <span class="textUploadpercentImage">{{ percentUpload }} %</span>
                </v-row>
                <v-row justify="center" v-if="LogoImage.length !== 0 && show === true" class="pt-2">
                  <v-btn text color="#27AB9C" style="text-decoration-line: underline; font-size: 14px;" @click="cancelImageShop()">ยกเลิก</v-btn>
                </v-row>
                <!-- upload success -->
                <v-row dense justify="center" v-if="LogoImage.length !== 0 && show === false && showError === false" class="pt-0">
                  <v-col cols="12" class="pt-0">
                    <span class="textUploadnameImage" v-if="LogoImage[0].name !== null">{{ LogoImage[0].name.substring(0, 20) }}<span v-if="LogoImage[0].name.length > 20">...</span></span><br/>
                    <span class="textUploadsizeImage">{{ Math.round(LogoImage[0].size / 1000) }} KB</span>
                  </v-col>
                  <v-col cols="12" class="pt-0">
                    <v-btn :disabled="checkboxlogo" width="50" height="40" text color="#27AB9C" @click="uploadImageShop()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                  </v-col>
                </v-row>
                <!-- upload fail -->
                <v-row dense justify="center" v-if="LogoImage.length !== 0 && show === false && showError === true" class="pt-0">
                  <v-col cols="12" class="pt-0">
                    <span class="textUploadnameImageFail" v-if="LogoImage[0].name !== null">{{ LogoImage[0].name.substring(0, 10) }}<span v-if="LogoImage[0].name.length > 10">...</span></span><br/>
                    <span class="textFailUpload">{{ showErrorText }}</span>
                  </v-col>
                  <v-col cols="12" class="pt-0">
                    <v-btn width="50" height="40" text color="#636363" @click="cancelImageShop()"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
      <v-form ref="formTwo" :lazy-validation="lazyTwo">
        <v-card-text>
          <v-row dense class="align-center">
            <span class="textStepOne pr-4">ที่อยู่ใบเสนอราคา</span>
            <v-checkbox :class="MobileSize ? 'ml-2' : 'ml-4'" v-model="checkboxaddress" @click="GetDetailShop()"  hide-details>
              <template v-slot:label>
                <div>
                  <span>ใช้ข้อมูลจากจัดการร้านค้า</span>
                </div>
              </template>
            </v-checkbox>
          </v-row>
          <v-row dense no-gutters class="mt-2">
            <v-col cols="12" md="4" sm="12">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">อีเมล <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <v-text-field
                    :disabled="checkboxaddress"
                    v-model="email"
                    outlined
                    dense
                    style="border-radius: 8px;"
                    placeholder="ระบุอีเมล"
                    :rules="Rules.email"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">หมายเลขโทรศัพท์ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <v-text-field
                    :disabled="checkboxaddress"
                    v-model="telNoNumber"
                    outlined
                    dense
                    oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                    :maxLength="10"
                    style="border-radius: 8px;"
                    @keypress="CheckSpacebar($event)"
                    :rules="Rules.tel"
                    placeholder="ระบุหมายเลขโทรศัพท์ 9 หรือ 10 หลัก"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">หมายเลขมือถือ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    :disabled="checkboxaddress"
                    v-model="mobileNumber"
                    outlined
                    dense
                    oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                    :maxLength="10"
                    style="border-radius: 8px;"
                    @keypress="CheckSpacebarMobile($event)"
                    :rules="Rules.telShop"
                    placeholder="ระบุหมายเลขโทรศัพท์มือถือ"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รายละเอียดที่อยู่</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    :disabled="checkboxaddress"
                    v-model="addressDetail"
                    outlined
                    style="border-radius: 8px;"
                    dense
                    placeholder="ระบุรายละเอียดที่อยู่"
                    :rules="Rules.spaceRule"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense no-gutters class="mt-0">
            <v-col cols="12" md="4" sm="12">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">เลขที่ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    :disabled="checkboxaddress"
                    v-model="houseNo"
                    outlined
                    style="border-radius: 8px;"
                    dense
                    oninput="this.value = this.value.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1')"
                    placeholder="ระบุบ้านเลขที่"
                    :rules="Rules.house_Num"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">แขวง/ตำบล <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <addressinput-subdistrict :disabled="checkboxaddress" style="border-radius: 8px;" label="" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล" />
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">เขต/อำเภอ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <addressinput-district :disabled="checkboxaddress" label="" style="border-radius: 8px;" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense no-gutters class="mt-0">
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">จังหวัด <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <addressinput-province :disabled="checkboxaddress" label="" style="border-radius: 8px;" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="province" placeholder="ระบุจังหวัด" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รหัสไปรษณีย์ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <addressinput-zipcode :disabled="checkboxaddress" numbered label="" style="border-radius: 8px;" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense class="mt-4">
            <v-btn rounded color="#27AB9C" width="165" height="40" outlined @click="GetDetailQT()">คืนค่าที่เคยบันทึก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" width="125" height="40" rounded class="white--text" :class="MobileSize ? '' : ''" @click="saveData()">บันทึก</v-btn>
          </v-row>
        </v-card-text>
      </v-form>
    </v-card>
  </v-container>
</template>

<script>
import Vue from 'vue'
import { Decode } from '@/services'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
Vue.use(VueThailandAddress)
export default {
  components: {
  },
  data () {
    return {
      ShopAddress: [
        {
          house_no: '',
          detail: '',
          province: '',
          district: '',
          sub_district: '',
          zipcode: ''
        }
      ],
      lazyTwo: false,
      checkboxlogo: false,
      checkboxaddress: false,
      LogoImage: [],
      Detail: [],
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์มือถือ',
          v => (v.length > 8 && v.length <= 20) || v === '' || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล'
        ]
      },
      shopID: '',
      show: false,
      showError: false,
      percentUpload: 0,
      showErrorText: '',
      telNoNumber: '',
      DataImageShop: [],
      email: '',
      mobileNumber: '',
      addressDetail: [],
      houseNo: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageQTExternalMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageQTExternal' }).catch(() => {})
      }
    },
    subdistrict (val) {
      if (/\s/g.test(val)) {
        this.subdistrict = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcode = ''
            this.district = ''
            this.province = ''
          }
        } else {
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      }
    },
    district (val) {
      if (/\s/g.test(val)) {
        this.district = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcode = ''
            this.subdistrict = ''
            this.province = ''
          }
        } else {
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      }
    },
    province (val) {
      if (/\s/g.test(val)) {
        this.province = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcode = ''
            this.subdistrict = ''
            this.district = ''
          }
        } else {
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      }
    },
    zipcode (val) {
      if (/\s/g.test(val)) {
        this.zipcode = val.replace(/\s/g, '')
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            // this.checkAdressError('checkZipcodeError')
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistrict = ''
            this.district = ''
            this.province = ''
          }
        } else {
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      }
    }
  },
  mounted () {
  },
  destroy () {
    clearInterval(this.timer)
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    this.shopID = parseInt(localStorage.getItem('shopSellerID'))
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      await this.GetDetailQT()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    async GetDetailQT () {
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID
        }
        await this.$store.dispatch('actionsDetailSettingQTExternal', data)
        var response = await this.$store.state.ModuleShop.stateDetailSettingQTExternal
        if (response.result === 'Success') {
          if (response.data.length === 0) {
            this.checkboxaddress = true
            this.checkboxlogo = true
            await this.GetDetailShop()
          } else {
            if (response.data[0].use_address_from_default === 1 || response.data[0].use_logo_from_default === 1) {
              this.checkboxaddress = response.data[0].use_address_from_default === 1 || response.data.length === 0
              this.checkboxlogo = response.data[0].use_logo_from_default === 1 || response.data.length === 0
              await this.GetDetailShop()
              if (this.checkboxaddress === true && this.checkboxlogo === false) {
                this.LogoImage = []
                this.LogoImage.push({
                  image_data: response.data[0].path_logo,
                  image_data_lazy: response.data[0].path_logo_lazy,
                  path: response.data[0].path_logo,
                  name: 'Logo',
                  size: ''
                })
              }
              if (this.checkboxlogo === true && this.checkboxaddress === false) {
                this.Detail = response.data[0]
                this.email = this.Detail.email
                this.telNoNumber = this.Detail.tel_no
                this.mobileNumber = this.Detail.mobile_no
                this.addressDetail = this.Detail.detail
                this.houseNo = this.Detail.house_no
                this.province = this.Detail.province
                this.district = this.Detail.district
                this.subdistrict = this.Detail.sub_district
                this.zipcode = this.Detail.zipcode
              }
            } else {
              this.LogoImage = []
              this.Detail = response.data[0]
              this.email = this.Detail.email
              this.telNoNumber = this.Detail.tel_no
              this.mobileNumber = this.Detail.mobile_no
              this.addressDetail = this.Detail.detail
              this.houseNo = this.Detail.house_no
              this.province = this.Detail.province
              this.district = this.Detail.district
              this.subdistrict = this.Detail.sub_district
              this.zipcode = this.Detail.zipcode
              this.LogoImage.push({
                image_data: response.data[0].path_logo,
                image_data_lazy: response.data[0].path_logo_lazy,
                path: response.data[0].path_logo,
                name: 'Logo',
                size: ''
              })
            }
          }
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({ icon: 'error', title: '<h5>เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง</h5>', showConfirmButton: false, timer: 2000 })
          this.$store.commit('closeLoader')
        }
      }
    },
    async GetDetailShop () {
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.result === 'SUCCESS') {
          if (this.checkboxaddress === true) {
            this.Detail = response.data[0]
            this.email = this.Detail.shop_email[0].seller_email
            this.telNoNumber = this.Detail.shop_phone[0].phone
            this.mobileNumber = this.Detail.shop_phone[1].phone
            this.addressDetail = this.Detail.address_detail[0].detail
            this.houseNo = this.Detail.address_detail[0].house_no
            this.province = this.Detail.address_detail[0].province
            this.district = this.Detail.address_detail[0].district
            this.subdistrict = this.Detail.address_detail[0].sub_district
            this.zipcode = this.Detail.address_detail[0].zipcode
          }
          if (this.checkboxlogo === true) {
            this.LogoImage = []
            if (response.data[0].shop_image.length !== 0) {
              this.LogoImage.push({
                image_data: response.data[0].shop_image[0].media_path,
                image_data_lazy: response.data[0].shop_image[0].media_path_lazy,
                path: response.data[0].shop_image[0].media_path,
                name: response.data[0].shop_image[0].name,
                size: ''
              })
            }
          }
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({ icon: 'error', title: '<h5>เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง</h5>', showConfirmButton: false, timer: 2000 })
          this.$store.commit('closeLoader')
        }
      }
    },
    async saveData () {
      this.$store.commit('openLoader')
      if (this.$refs.formTwo.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkSendAddress()
            if (check.length !== 0) {
              this.ShopAddress[0].house_no = this.houseNo
              this.ShopAddress[0].detail = this.addressDetail
              this.ShopAddress[0].province = this.province
              this.ShopAddress[0].district = this.district
              this.ShopAddress[0].sub_district = this.subdistrict
              this.ShopAddress[0].zipcode = this.zipcode
              var AddressData = this.ShopAddress
              var data = {
                seller_shop_id: this.shopID,
                shop_address: this.checkboxaddress === true ? [] : AddressData,
                use_address_from_default: this.checkboxaddress,
                use_logo_from_default: this.checkboxlogo,
                email: this.email,
                tel_no: this.telNoNumber,
                mobile_no: this.mobileNumber,
                path_logo: this.checkboxlogo === true ? '' : this.LogoImage[0].image_data,
                path_logo_lazy: this.checkboxlogo === true ? '' : this.LogoImage[0].image_data_lazy,
                short_name: '',
                prepared_by_default: '',
                approve_by_default: ''
              }
              await this.$store.dispatch('actionsSettingQTExternal', data)
              var response = await this.$store.state.ModuleShop.stateSettingQTExternal
              if (response.result === 'SUCCESS') {
                this.$store.commit('closeLoader')
                this.$swal.fire({ icon: 'success', title: '<h5>ดำเนินการเสร็จสิ้น</h5>', showConfirmButton: false, timer: 1500 })
                this.GetDetailQT()
              } else {
                this.$store.commit('closeLoader')
                this.$swal.fire({ icon: 'error', title: '<h5>เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง</h5>', showConfirmButton: false, timer: 2000 })
              }
            } else {
              this.$store.commit('closeLoader')
              this.checkConfirmAddress()
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$store.commit('closeLoader')
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$store.commit('closeLoader')
        this.callCheckAdress()
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistrict
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.district
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.province
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcode)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistrict)
      this.checkdistrictConfirm(this.district)
      this.checkprovinceConfirm(this.province)
      this.checkzipcodeConfirm(this.zipcode)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    CheckSpacebarMobile (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    UploadImageShop () {
      this.show = true
      this.showError = false
      this.showErrorText = ''
      this.percentUpload = 0
      var data = {}
      const element = this.DataImageShop
      const imageSize = element.size / 1024 / 1024
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
        this.LogoImage = []
        if (imageSize < 1) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = async () => {
            var resultReader = reader.result
            var url = URL.createObjectURL(element)
            data = {
              image: [resultReader.split(',')[1]],
              type: 'shop',
              seller_shop_id: this.shopID
            }
            await this.$store.dispatch('actionsUploadToS3', data)
            var response = await this.$store.state.ModuleShop.stateUploadToS3
            if (response.message === 'List Success.') {
              this.LogoImage.push({
                image_data: response.data.list_path[0].path,
                image_data_lazy: response.data.list_path[0].path_lazy,
                path: url,
                name: this.DataImageShop.name,
                size: this.DataImageShop.size
              })
              setInterval(() => {
                if (this.percentUpload >= 100) {
                  this.percentUpload = 100
                  this.show = false
                } else {
                  const increment = Math.random() * 10 + 5 // เพิ่มทีละ 5-10 แบบสุ่ม
                  this.percentUpload = Math.min(100, (this.percentUpload + increment).toFixed(2)) // จำกัดที่ 2 ตำแหน่งและไม่เกิน 100
                }
              }, 200)
            }
          }
        } else {
          this.show = false
          this.showError = true
          this.showErrorText = 'ไฟล์มีขนาดใหญ่เกินไป'
          this.LogoImage.push({
            name: this.DataImageShop.name
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    cancelImageShop () {
      this.show = false
      this.percentUpload = 0
      this.DataImageShop = []
      this.LogoImage = []
    },
    uploadImageShop () {
      document.getElementById('imageShop').click()
    },
    backTomenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    }
  }
}
</script>

<style>
.v-radio .v-icon {
  color: #27AB9C;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 16px;
  border-radius: 8px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 8px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px;
  color: #212121;
  border-radius: 8px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.v-input .v-input__slot {
  border-radius: 8px !important;
}
.v-input--selection-controls {
  margin-top: 0px;
  padding-top: 0px;
}
li::marker {
  color: #27AB9C;
}
</style>
