<template>
<v-card width="100%" elevation="0">
<v-card-text :class="MobileSize ? 'mt-4 py-0' : ''">
    <v-card-title style="font-weight: 700; font-size: 18px; line-height: 22px; color: #333333;" @click="$router.go(-1)" class="px-0" v-if="MobileSize"><v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> แดชบอร์ดรายงานกำไรเบื้องต้น (ค่า GP) ทั้งหมดบนระบบ</v-card-title>
    <v-card-title class="pt-0 px-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-else> แดชบอร์ดรายงานกำไรเบื้องต้น (ค่า GP) <br v-if="IpadSize"/>ทั้งหมดบนระบบ</v-card-title>
    <v-row v-if="!MobileSize">
      <v-col class="mt-5 ml-2">
        <div style="font-weight: 700; font-size: 24px; line-height: 32px;"></div>
      </v-col>
    </v-row>
 <!--  <v-row>
    <v-col cols="5" align="end">
    <div class="mt-6">
    วันที่เริ่ม - สิ้นสุด
    </div>
    </v-col>
    <v-col
        cols="7"
        md="7"
        lg="2"
        v-show="numChange === '1'"
      >
        <v-menu
          ref="menu1"
          v-model="menu1"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          max-width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormatted"
              label=""
              hint=""
              persistent-hint
              append-icon="mdi-calendar"
              dense
              outlined
              class="mt-2"
              v-bind="attrs"
              v-on="on"
            ></v-text-field>
          </template>
          <v-date-picker
            v-model="dateStart"
            no-title
            :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            @input="menu1 = false"
            @change="changeDate"
          ></v-date-picker>
        </v-menu>
      </v-col>

      <v-col
        cols="12"
        md="2"
        lg="2"
        v-show="numChange === '1'"
      >
        <v-menu
          v-model="menu2"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          max-width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormatted2"
              label=""
              hint=""
              persistent-hint
              append-icon="mdi-calendar"
              dense
              outlined
              v-bind="attrs"
              v-on="on"
              class="mt-2"
            ></v-text-field>
          </template>
          <v-date-picker
            v-model="dateEnd"
            no-title
            :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            :min="dateStart"
            @input="menu2 = false"
            @change="filterDate"
          ></v-date-picker>
        </v-menu>
      </v-col>
      <v-col cols="12" md="3">
         <v-select
        v-show="numChange === '1'"
         style="position: 'absolute'; elevation: 50; zIndex:50 ;"
          v-model="weekday"
          :items="weekdays"
          item-value="id"
          item-text="name"
          return-object
          @change="filterOther"
          dense
          outlined
          hide-details
          label="เลือก"
          class="ma-2"
        ></v-select>
      </v-col>
</v-row> -->

      <v-row dense>
        <v-col cols="12" md="4" :class="MobileSize ? 'pb-0' : ''">
          <v-row dense>
            <v-col cols="12" md="3" class="pt-3">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกร้านค้า :</span>
            </v-col>
            <v-col cols="12" md="8">
              <v-autocomplete v-model="shopID" :items="shopList" dense outlined item-text="name_th" item-value="shop_id" placeholder="เลือกร้านค้า" @change="selectShop($event)" style="z-index: 11 !important;"></v-autocomplete>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="8" :class="MobileSize ? 'mb-4' : ''">
          <v-row dense :justify="dataSelect !== 3 && dataSelect !== 4 ? 'center' : 'end'">
            <v-col cols="12" md="2" class="pt-3" v-if="dataSelect !== 3 && dataSelect !== 4">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">วันที่เริ่ม - สิ้นสุด</span>
            </v-col>
            <v-col cols="12" md="2" class="pt-3" v-else-if="dataSelect === 3">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกเดือน</span>
            </v-col>
            <v-col cols="12" md="2" class="pt-3" v-else-if="dataSelect === 4">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกปี</span>
            </v-col>
            <v-col cols="12" :md="dataSelect !== 3 && dataSelect !== 4 ? 7 : 4">
              <v-row dense v-if="dataSelect !== 3 && dataSelect !== 4">
                <!-- เลือกวันเริ่มต้น -->
                <v-col cols="5" md="5" class="px-0">
                  <v-dialog
                    ref="dialog"
                    v-model="modal"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="dateStart"
                        v-bind="attrs"
                        v-on="on"
                        readonly
                        outlined
                        :disabled="dataSelect === 5 ? true : false"
                        dense
                        placeholder="DD/MM/YYYY"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="pickerDateStart"
                      v-model="dateFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      reactive
                      no-title
                      @input="modal = false"
                      @change="getSelectDate(dateFormatted)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                    >
                     <!--  <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="getSelectDate(dateFormatted)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <v-col cols="2" md="1" :class="MobileSize ? 'pt-3 pl-6' : 'pt-3 pl-3'">-</v-col>
                <!-- เลือกวันสิ้นสุด -->
                <v-col cols="5" md="5" class="px-0">
                  <v-dialog
                    ref="dialog1"
                    v-model="modal1"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="dateEnd"
                        v-bind="attrs"
                        v-on="on"
                        :readonly="dataSelect === 2 ? true : false"
                        dense
                        :disabled="dataSelect === 5 ? true : dataSelect === 2 ? true : false"
                        placeholder="DD/MM/YYYY"
                        outlined
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="dateFormatted2"
                      ref="pickerDateEnd"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      :readonly="dataSelect === 2 ? true : false"
                      @input="modal1 = false"
                      @change="getSelectDateEnd(dateFormatted2)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      :min="dateFormatted"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal1 = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        :disabled="dataSelect === 2 ? true : false"
                        color="primary"
                        @click="getSelectDateEnd(dateFormatted2)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
              <v-row dense v-else-if="dataSelect === 3" justify="center" class="pr-4">
                <!-- เลือกเดือน -->
                <v-col cols="12" class="px-0">
                  <v-dialog
                    ref="dialogMonth"
                    v-model="modalMonth"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="month"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        dense
                        placeholder="MMMM"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="pickerMonth"
                      v-model="MonthFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      reactive
                      min="1950-01-01"
                      @change="SaveMonth(MonthFormatted)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      @click:month="SaveMonth(MonthFormatted)"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modalMonth = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="SaveMonth(MonthFormatted)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
              <v-row dense v-else-if="dataSelect === 4" justify="center" class="pr-4">
                <!-- เลือกปี -->
                <v-col cols="12" class="px-0">
                  <v-dialog
                    ref="dialogYear"
                    v-model="modalYear"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="year"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        dense
                        placeholder="YYYY"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="picker"
                      v-model="YearFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      reactive
                      @change="SaveYear(YearFormatted)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      min="1950-01-01"
                      @click:year="SaveYear(YearFormatted)"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modalYear = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="SaveYear(YearFormatted)"
                      >
                        ตกลง -->
                      <!-- </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" class="px-0">
              <v-select
               v-model="selectFilter"
               :items="selectFilteritem"
               @change="getFilter($event)"
               item-value="id"
               item-text="name"
               dense
               outlined
               hide-details
               placeholder="เลือก"
               style="z-index: 11 !important;"
              >
              </v-select>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script>
// import $ from 'jquery'
// Based on example from:
// https://datatables.net/forums/discussion/49457
export default {
  data () {
    return {
      headerProps: {
        sortByText: 'เรียงตาม'
      },
      month: '',
      shopID: '',
      activePicker: null,
      modalMonth: false,
      modalYear: false,
      selectFilter: 0,
      dataSelect: 0,
      dataRes: [],
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      modal: false,
      modal1: false,
      series: [],
      dateGraph: [],
      dateStart: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateEnd: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      startDate: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      endDate: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      year: '',
      dateSelectFunctionOne: '',
      dateSelectFunctionTwo: '',
      FirstDateSelect: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      EndDateSelect: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      typeFilterForShop: '',
      MonthFormatted: null,
      typefilter: '',
      YearFormatted: null,
      dateFormatted: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted2: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      selectFilteritem: [
        { id: 1, name: 'ไม่ระบุ' },
        { id: 2, name: 'รายวัน' },
        { id: 3, name: 'รายเดือน' },
        { id: 4, name: 'รายปี' },
        { id: 5, name: 'ทั้งหมด' }
      ]
    }
  },
  created () {
    // var typefilter = 'day'
    // console.log('', this.startDate, this.endDate)
    // this.filterDate(this.startDate, this.endDate, typefilter, this.shopID)
    // console.log('Date', new Date())
    // console.log('getTimezoneOffset', new Date(Date.now() - (new Date()).getTimezoneOffset()).toISOString())
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
  },
  destroyed () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    },
    shopList () {
      return this.$store.state.ModuleAdminManage.stateshopList
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardAdminForGPMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardAdminForGP' }).catch(() => {})
      }
    },
    modalYear (val) {
      // console.log(val)
      val && this.$nextTick(() => (this.activePicker = 'YEAR'))
    },
    modalMonth (val) {
      val && this.$nextTick(() => (this.activePicker = 'MONTH'))
    },
    dialog (val) {
      val && this.$nextTick(() => (this.activePicker = 'DATE'))
    }
  },
  methods: {
    changeDate () {
      // console.log('End')
      this.dateEnd = ''
    },
    backtoUserMenu () {
      this.$router.push({ path: '/sellerMobile' }).catch(() => {})
    },
    async selectShop (val) {
      // console.log(val)
      await this.filterOther(this.FirstDateSelect, this.EndDateSelect, this.typefilter, val)
    },
    async filterDate (startSelect, endSelect, typefilter, shopID) {
      // var preStart = await this.afterDate(this.formatDate(this.dateStart))
      // var preEnd = await this.afterDate(this.formatDate(this.dateEnd))
      const data = await {
        start_date: startSelect,
        end_date: endSelect,
        type: typefilter,
        seller_shop_id: shopID
      }
      // this.$EventBus.$emit('getDataTop10buyers', startSelect, endDate, typefilter)
      await this.$EventBus.$emit('selectedChanged', data)
    },
    async filterOther (startSelect, endSelect, typefilter, shopID) {
      const data = await {
        start_date: startSelect,
        end_date: endSelect,
        type: typefilter,
        seller_shop_id: shopID
      }
      await this.$EventBus.$emit('selectedChanged', data)
    },
    async getFilter (valSelect) {
      // console.log(valSelect)
      this.dateStart = ''
      this.dateEnd = ''
      this.dateFormatted = ''
      this.dateFormatted2 = ''
      this.typefilter = ''
      // var date = new Date()
      if (valSelect === 1) {
        this.dataSelect = valSelect
        this.month = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.year = ''
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        // this.dateFormatted = ''
        // console.log(this.dateStart)
        // console.log(this.formatDate(new Date(date.setDate(date.getDate() + 3) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)))
        // if (this.dateStart !== '') {
        //   this.dateFormatted2 = new Date(date.setDate(date.getDate(this.dateStart) + 3) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        // }
        this.typefilter = 'day'
      } else if (valSelect === 2) {
        this.dataSelect = valSelect
        this.dateFormatted = ''
        this.dateFormatted2 = ''
        this.month = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.year = ''
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.typefilter = 'day'
      } else if (valSelect === 3) {
        this.dataSelect = valSelect
        this.month = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted2 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.typefilter = 'month'
      } else if (valSelect === 4) {
        this.dataSelect = valSelect
        this.year = ''
        this.typefilter = 'year'
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted2 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      } else if (valSelect === 5) {
        this.dataSelect = valSelect
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var startSelect = ''
        var endSelect = ''
        this.FirstDateSelect = startSelect
        this.EndDateSelect = endSelect
        this.typefilter = 'all'
        this.typeFilterForShop = ''
        await this.filterOther(startSelect, endSelect, this.typefilter, this.shopID)
      }
    },
    getSelectDateEnd (val) {
      this.$refs.dialog1.save(val)
      this.dateSelectFunctionTwo = ''
      this.dateSelectFunctionTwo = new Date(this.dateFormatted2)
      this.dateEnd = this.formatDate(new Date(this.dateSelectFunctionTwo.setDate(this.dateSelectFunctionTwo.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      var startSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var endDate = this.dateFormat(new Date(this.dateSelectFunctionTwo.setDate(this.dateSelectFunctionTwo.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      this.FirstDateSelect = startSelect
      this.EndDateSelect = endDate
      var typefilter = 'day'
      this.typeFilterForShop = ''
      this.filterOther(startSelect, endDate, typefilter, this.shopID)
      this.modal1 = false
    },
    async getSelectDate (val) {
      this.$refs.dialog.save(val)
      this.$refs.pickerDateStart.activePicker = 'DATE'
      this.dateStart = ''
      this.dateEnd = ''
      this.dateSelectFunctionOne = ''
      if (this.dataSelect === 1) {
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      } else if (this.dataSelect === 2) {
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        this.dateFormatted2 = new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.dateEnd = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        var startSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
        var endSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
        this.FirstDateSelect = startSelect
        this.EndDateSelect = endSelect
        var typefilter = 'day'
        this.typeFilterForShop = ''
        await this.filterOther(startSelect, endSelect, typefilter, this.shopID)
      } else if (this.dataSelect === 3) {
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      } else if (this.dataSelect === 4) {
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      } else if (this.dataSelect === 5) {
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      } else if (this.dataSelect === 0) {
        // console.log('เข้าเงื่อนไข')
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      }
      this.modal = false
    },
    dateFormat (inputDate, format) {
      // parse the input date
      const date = new Date(inputDate)
      // console.log(date)
      // extract the parts of the date
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      // replace the month
      format = format.replace('MM', month.toString().padStart(2, '0'))
      // replace the year
      if (format.indexOf('yyyy') > -1) {
        format = format.replace('yyyy', year.toString())
      } else if (format.indexOf('yy') > -1) {
        format = format.replace('yy', year.toString().substr(2, 2))
      }
      // replace the day
      format = format.replace('dd', day.toString().padStart(2, '0'))
      return format
    },
    SaveMonth (val) {
      // console.log(val)
      this.$refs.dialogMonth.save(val)
      this.$refs.pickerMonth.activePicker = 'MONTH'
      // var getMonth = new Date(this.MonthFormatted)
      // const month = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
      var monthCurrent = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 7)
      // console.log(monthCurrent)
      this.month = new Date(this.MonthFormatted).toISOString().substr(0, 7)
      // console.log(this.month)
      var SelectMonth = new Date(this.MonthFormatted)
      var firstDateMonth = this.dateFormat(new Date(new Date(SelectMonth.getFullYear(), SelectMonth.getMonth(), 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var lastDateMonth = ''
      if (monthCurrent !== this.month) {
        lastDateMonth = this.dateFormat(new Date(new Date(SelectMonth.getFullYear(), SelectMonth.getMonth() + 1, 0) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      } else {
        lastDateMonth = this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd')
      }
      // console.log(lastDateMonth)
      this.FirstDateSelect = firstDateMonth
      this.EndDateSelect = lastDateMonth
      var typefilter = 'month'
      this.typeFilterForShop = ''
      // console.log(firstDateMonth, lastDateMonth)
      this.filterOther(firstDateMonth, lastDateMonth, typefilter, this.shopID)
      this.modalMonth = false
    },
    SaveYear (val) {
      this.$refs.dialogYear.save(val)
      this.$refs.picker.activePicker = 'YEAR'
      this.year = new Date(this.YearFormatted).toISOString().substr(0, 4)
      var currentDate = new Date(this.YearFormatted)
      var firstDateYear = this.dateFormat(new Date(new Date(currentDate.getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var LastDateYear = this.dateFormat(new Date(new Date(currentDate.getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      this.FirstDateSelect = firstDateYear
      this.EndDateSelect = LastDateYear
      var typefilter = 'year'
      this.typeFilterForShop = 'year'
      // console.log(firstDateYear, LastDateYear)
      this.filterOther(firstDateYear, LastDateYear, typefilter, this.shopID)
      this.modalYear = false
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    parseDate (date) {
      // console.log('parseDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${day}-${month}-${year}`
    },
    afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${year}-${month}-${day}`
    },
    timeStamp (Dates) {
      var myDate = Dates.split('-')
      var newDate = new Date(myDate[2], myDate[1] - 1, myDate[0])
      return newDate.getTime()
    },
    randomColor () {
      const randomColor = Math.floor(Math.random() * 16777215).toString(16)
      return `#${randomColor}`
    }
  }
}
</script>
<style lang="css" scoped>
::v-deep #example_filter {
  display: none;
}
::v-deep #example_paginate {
  display: none;
}
::v-deep #example_info {
    display: none;
}
::v-deep #example_wrapper {
  margin-left: 70%;
}
::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
}
</style>
