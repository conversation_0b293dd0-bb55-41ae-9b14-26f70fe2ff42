<template>
  <v-container>
  <!-- <v-container grid-list-xs> -->
    <!-- Modal Edit User Detail -->
    <v-dialog v-model="EditDialogAccount" persistent width="729">
      <v-card >
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 24px; font-weight: 700;">แก้ไขข้อมูลส่วนตัว</span>
          </v-row>
          <v-btn fab small @click="CloseDialog()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text>
            <v-form ref="FormUserDetail" :lazy-validation="lazy">
              <v-row justify="center" align-content="center" no-gutters>
                <v-col cols="8"  @click="changePic()">
                  <v-row justify="center" align="center">
                    <v-hover v-slot="{ hover }">
                      <div :elevation="hover ? 16 : 2" :class="{ 'on-hover': hover }" class="mx-auto" color="grey lighten-4">
                        <div style="position: relative;" >
                          <v-avatar class='ml-6 mr-7' style="cursor: pointer;" width="106.98" height="160.47" tile>
                            <v-img :src="userdetail.img_path" v-if="userdetail.img_path !== null" contain></v-img>
                            <v-img v-else src="@/assets/noprofile.png" class="pointPic" contain></v-img>
                          </v-avatar>
                          <span style="position: absolute; margin-top: 110px; right: 0px;"><v-icon color="#27AB9C">mdi-pencil-circle</v-icon></span>
                        </div>
                      </div>
                    </v-hover>
                  </v-row>
                  <input
                    type="file"
                    style="display: none"
                    ref="image"
                    id="picTure"
                    accept="image/*"
                    @change="showPicture"
                  >
                </v-col>
                <v-col cols="8" class="mt-4">
                  <span>ชื่อ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="8"  class="mt-4">
                  <v-text-field
                    class="input_text"
                    placeholder="ระบุชื่อ"
                    outlined
                    dense
                    v-model="first_name"
                    :maxLength="20"
                    :rules="Rules.first_name"
                    counter="20"
                    oninput="this.value = this.value.replace(/[^a-zก-๙]/, '')">
                  </v-text-field>
                </v-col>
                <v-col cols="8">
                  <span>นามสกุล <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="8">
                  <v-text-field
                    class="input_text"
                    placeholder="ระบุนามสกุล"
                    outlined
                    dense
                    v-model="last_name"
                    :maxLength="20"
                    :rules="Rules.last_name"
                    counter="20"
                    oninput="this.value = this.value.replace(/[^a-zก-๙]/, '')">
                  </v-text-field>
                </v-col>
                <v-col cols="8">
                  <span>ชื่อผู้ใช้ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="8">
                  <v-text-field class="input_text" placeholder="ชื่อผู้ใช้" outlined dense v-model="user" readonly disabled :rules="Rules.user"></v-text-field>
                </v-col>
                <v-col cols="8">
                  <span>เบอร์โทรศัพท์ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="8">
                  <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" disabled :maxLength="10" readonly :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="8">
                  <span>E-mail <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="8">
                  <v-text-field class="input_text" placeholder="E-mail" outlined dense v-model="email" disabled :readonly="userdetail.email === '' ? false : true" :rules="Rules.emailRules"></v-text-field>
                </v-col>
              </v-row>
              <v-row justify="center" align-content="center" dense class="mx-2 mt-4 mb-4">
                <!-- action -->
                <v-col cols="12" md="8">
                  <v-row justify="end">
                    <v-btn outlined color="#27AB9C" class="mr-4 pl-8 pr-8" @click="CloseDialog()">ยกเลิก</v-btn>
                    <v-btn color="#27AB9C" dark class="pl-9 pr-9" @click="ConfirmModalEditAccount()">บันทึก</v-btn>
                  </v-row>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal Confirm Edit Account -->
    <v-dialog
     v-model="ModalConfirmEditAccount"
     width="464"
     persistent
     dense
    >
      <v-card width="100%" height="258" style="border-radius: 12px;" >
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 20px; font-weight: bold; line-height: 30px;">แก้ไขข้อมูลส่วนตัว</span>
          </v-row>
          <v-btn fab x-small @click="ModalConfirmEditAccount = !ModalConfirmEditAccount" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" md="9" sm="8" :class="!MobileSize ? 'mt-12 mb-8 ml-14' : 'mt-12 mb-8 '">
            <v-row justify="center" style="text-align: center">
              <span style="font-weight: 800; font-size: 18px; color: #333333; line-height: 26px;">คุณต้องการแก้ไขข้อมูลส่วนตัว</span>
            </v-row>
            <v-row justify="center" style="text-align: center">
              <span style="font-weight: 800; font-size: 18px; color: #333333; line-height: 26px;">ใช่ หรือ ไม่</span>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" sm="12">
            <v-row justify="center" align-content="center">
              <v-btn outlined dense rounded dark class="mr-4 pl-8 pr-8" color="#27AB9C" @click="CloseModalEditAccount()">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark dense rounded class="pl-8 pr-8" @click="EditAccount()">ตกลง</v-btn>
            </v-row>
          </v-col>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal Confirm Bind Account -->
    <v-dialog
     v-model="ModalConfirmBindAccount"
     width="464"
     persistent
     dense
    >
     <v-card width="100%">
        <v-toolbar dark dense elevation="0">
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 16px; font-weight: 700;">ผู้บัญชีกับ ONE ID</span>
          </v-row>
          <v-btn fab x-small @click="ModalConfirmBindAccount = !ModalConfirmBindAccount" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" md="8" sm="8" class="mt-8 mb-8 ml-16">
            <v-row justify="center" align-content="center" style="text-align: center">
              <span style="font-weight: 400; font-size: 14px; color: #333333;">คุณต้องการผูกบัญชี <span style="font-weight: bold;">{{ userdetail.email }}</span> กับ ONE ID ใช่หรือไม่</span>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" sm="12">
            <v-row justify="center" align-content="center">
              <v-btn outlined color="#27AB9C" @click="ModalConfirmBindAccount = !ModalConfirmBindAccount" class="mr-4">ไม่ยอมรับ</v-btn>
              <v-btn color="#27AB9C" dark class="pl-7 pr-7" @click="ShowBindAccountDialog()">ยอมรับ</v-btn>
            </v-row>
          </v-col>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal Bind Account -->
    <v-dialog
     v-model="BindAccountdialog"
     width="500"
     persistent
     dense
    >
      <v-form ref="FormcreateAccount" :lazy-validation="lazy">
        <v-card width="729" height="380">
          <v-row no-gutters>
            <v-container>
              <v-row no-gutters justify="center" align-content="center" class="mt-6 mb-6">
                <img src="@/assets/ImageINET-Marketplace/ICONProfile/One.png" contain width="60" height="60" /><span style="font-weight: 700; font-size: 18px;" class="mt-3 ml-4">ข้อมูลสำหรับผู้ใช้งาน OneID</span>
              </v-row>
              <v-row no-gutters class="pl-14">
                <v-col cols="12" sm="12" md="10">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">ชื่อผู้ใช้งาน OneID</span>
                </v-col>
                <v-col cols="12" sm="12" md="10">
                  <v-text-field
                    v-model="usernameOne"
                    placeholder="ใส่ชื่อผู้ใช้งาน"
                    dense
                    :rules="Rules.username"
                    outlined
                    required
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="12" md="10">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">รหัสผ่าน OneID</span>
                </v-col>
                <v-col cols="12" sm="8" md="10">
                  <v-text-field
                    v-model="passwordOne"
                    placeholder="ใส่รหัสผ่าน"
                    :rules="Rules.password"
                    :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'"
                    :type="show1 ? 'text' : 'password'"
                    @click:append="show1 = !show1"
                    dense
                    outlined
                    required
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="8" md="10" class="mt-4">
                  <v-row justify="end" style="margin-right: 2px;">
                    <v-btn color="#27AB9C" dense @click="CloseModalBindAccount()" outlined class="px-5">ยกเลิก</v-btn>
                    <v-btn color="#27AB9C" dense @click="confirm()" class="ml-4 px-5" dark>ยืนยัน</v-btn>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </v-row>
        </v-card>
      </v-form>
    </v-dialog>
    <v-form>
      <!-- ส่วนแสดงข้อมูล -->
      <v-card width="100%" height="100%" elevation="0" class="mb-4" :class="MobileSize ? 'mx-2 mt-2' : ''">
        <!-- <pre>{{userdetail}}</pre> -->
        <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ข้อมูลของฉัน</v-card-title>
        <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> ข้อมูลของฉัน</v-card-title>
        <v-card elevation="0" width="100%" height="100%" class="pb-8">
          <v-card-text>
            <!-- <v-card-title class="ml-8" style="font-weight: bold; font-size: 20px; line-height: 30px;">ข้อมูลส่วนตัว</v-card-title> -->
            <v-row dense :class="IpadSize ? 'mt-2 mb-6' : 'mt-2 mb-6 mr-4'">
              <v-col cols="12" md="12" :class="IpadSize ? '' : 'px-6'">
                <v-row justify="end" class="mr-2">
                  <v-btn color="#27AB9C" dark @click="openEditDialog()" outlined tile style="border-radius: 4px;"><v-icon left>mdi-pencil</v-icon>แก้ไขข้อมูล</v-btn>
                </v-row>
              </v-col>
            </v-row>
            <v-row :justify="MobileSize ? 'center' : 'start'" class="mt-2">
              <v-col cols="12" md="3" sm="3" class="mr-0">
                <v-row justify="center" align-content="center">
                  <v-avatar width="106.98" height="120" tile>
                    <v-img v-if="img_path === null"  src="@/assets/noprofile.png" contain/>
                    <v-img v-else :src="img_path" contain />
                  </v-avatar>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="8" class="ml-0">
                <v-row justify="center" align-content="center">
                  <!-- /////////////////////// title ////////////////// -->
                  <v-row justify="start" align-content="center" dense>
                    <!-- ชื่อผู้ใช้ -->
                    <v-col cols="5">
                      <span class="f-right">ชื่อผู้ใช้ :</span>
                    </v-col>
                    <v-col cols="7" class="pl-5">
                      <span>{{ username }}</span>
                    </v-col>
                    <!-- ชื่อผู้ใช้ OneID -->
                    <!-- <v-col cols="3">
                      <span class="f-right">ชื่อผู้ใช้ One ID :</span>
                    </v-col>
                    <v-col cols="8" class="pl-5">
                      <span>{{ userdetail.username_oneid }}</span>
                    </v-col> -->
                    <!-- ชื่อ  -->
                    <v-col cols="5" class="mt-2">
                      <span class="f-right">ชื่อ :</span>
                    </v-col>
                    <v-col cols="7" class="pl-5 mt-2">
                      <span>{{ fullname }}</span>
                      <!-- <a-input
                        dense
                        placeholder="ชื่อ"
                        suffix=" "
                        v-model="userdetail.first_name_th"
                      /> -->
                    </v-col>
                    <!-- สกุล  -->
                    <v-col cols="5" class="mt-2">
                      <span class="f-right">Email :</span>
                    </v-col>
                    <v-col cols="7" class="pl-5 mt-2">
                      <span>{{ userdetail.email }}</span>
                      <!-- <a-input
                        dense
                        placeholder="นามสกุล"
                        v-model="userdetail.last_name_th"
                      /> -->
                    </v-col>
                    <!-- อีเมล -->
                    <!-- <v-col cols="2">
                      <span class="f-right">อีเมล</span>
                    </v-col>
                    <v-col cols="10" class="pl-5">
                      <span><EMAIL></span>
                    </v-col> -->
                    <!-- หมายเลขโทรศัพท์ -->
                    <v-col cols="5">
                      <span class="f-right">หมายเลขโทรศัพท์ :</span>
                    </v-col>
                    <v-col cols="7" class="pl-5">
                      <span>{{ userdetail.phone }}</span>
                      <!-- <a-input
                        dense
                        placeholder="หมายเลขโทรศัพท์"
                        v-model="userdetail.phone"
                      /> -->
                    </v-col>
                    <!-- ชื่อร้านค้า -->
                    <!-- <v-col cols="3" v-if="userShop.name_th !== ''">
                      <span class="f-right">ชื่อร้านของฉัน</span>
                    </v-col>
                    <v-col cols="8" class="pl-5" v-if="userShop !== null">
                      <span>{{ userShop.name_th }}</span>
                    </v-col> -->
                    <!-- <v-col cols="2">
                      <v-btn color="light-green" rounded block>บันทึก</v-btn>
                    </v-col> -->
                  </v-row>
                </v-row>
              </v-col>
              <!-- col เส้น -->
            </v-row>
          </v-card-text>
        </v-card>
      </v-card>
      <!-- <v-col cols="12" md="12" class="pr-12 pl-12" v-if="oneUserType === 'market_user'">
        <v-divider></v-divider>
      </v-col>
      <v-card width="100%" height="100%" class="mb-4" v-if="oneUserType === 'market_user'"  elevation="0">
        <v-card width="100%" height="100%" elevation="0" class="px-6 pb-8">
          <v-card-title style="font-weight: 700;" class="ml-2">ผูกบัญชีกับ ONE ID</v-card-title>
          <v-card-subtitle>
            <p class="ml-2 mb-0 mt-2" style="color: #333333;"><span style="color: red;">***</span> หมายเหตุ ถ้าคุณผูกบัญชีกับ ONE ID คุณสามารถรับสิทธิพิเศษมากมายรวมถึงประสบการณ์การใช้งานที่ดียิ่งขึ้น</p>
          </v-card-subtitle>
          <v-card-text class="mt-4">
            <v-row>
              <v-img :src="require('@/assets/logo_one.png')" max-height="35" max-width="82" contain class="ml-6"/>
              <span style="font-size: 16px; font-weight: 400;" class="ml-4 pt-2">Connect wiht ONE ID</span>
              <v-spacer></v-spacer>
              <v-btn color="#27AB9C" @click="BindAccount()" dark class="px-4 mr-4">ผูกบัญชีกับ ONE ID</v-btn>
            </v-row>
          </v-card-text>
        </v-card>
      </v-card> -->
    </v-form>
  <!-- </v-container> -->
  </v-container>
</template>
<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      ModalConfirmEditAccount: false,
      show1: false,
      img_path: '',
      showImage: '',
      DataImage: [],
      imageName: '',
      first_name: '',
      last_name: '',
      user: '',
      phone: '',
      email: '',
      username: '',
      ModalConfirmBindAccount: false,
      EditDialogAccount: false,
      userdetail: [],
      fullname: '',
      userAddress: [],
      userShop: [],
      oneUserType: '',
      BindAccountdialog: false,
      usernameOne: '',
      passwordOne: '',
      lazy: false,
      Rules: {
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้',
          v => /^[A-Za-z0-9]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรและตัวเลขเท่านั้น'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ],
        first_name: [
          v => !!v || 'กรุณากรอกชื่อจริง',
          v => v.length <= 20 || 'ห้ามกรอกชื่อจริงเกิน 20 ตัวอักษร',
          v => /[^๑-๙฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรเท่านั้น'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => v.length <= 20 || 'ห้ามกรอกนามสกุลเกิน 20 ตัวอักษร',
          v => /[^๑-๙฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรเท่านั้น'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ],
        user: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้'
        ]
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/userprofileDetail' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userprofile' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAccount')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.getProfile()
    }
  },
  methods: {
    async getProfile () {
      this.$store.commit('openLoader')
      this.img_path = ''
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        this.oneUserType = onedata.user.type_user
        // console.log('type user', this.oneUserType)
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var data = {
          role_user: dataRole.role
        }
        await this.$store.dispatch('actionsUserDetailPage', data)
        const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
        // console.log('userdetail data', userdetail)
        if (userdetail.message !== 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.userdetail = userdetail.data[0]
          this.img_path = this.userdetail.img_path
          // Username
          if (this.userdetail.username !== null && this.userdetail.username_oneid === null) {
            this.username = this.userdetail.username
          } else if (this.userdetail.username === null && this.userdetail.username_oneid !== null) {
            this.username = this.userdetail.username_oneid
          } else if (this.userdetail.username !== null && this.userdetail.username_oneid !== null) {
            this.username = this.userdetail.username
          } else {
            this.username = '-'
          }
          // Full name
          if (this.userdetail.first_name_th === '' && this.userdetail.last_name_th === '') {
            this.fullname = '-'
          } else {
            this.fullname = this.userdetail.first_name_th + ' ' + this.userdetail.last_name_th
          }
          if (this.userdetail.address_data !== undefined) {
            this.userAddress = this.userdetail.address_data[0]
          } else {
            this.userAddress = []
          }
          this.userShop = this.userdetail.shop_data[0]
          // console.log('userdetail data', userdetail, this.userAddress, this.userShop)
        } else {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
          // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
          // window.location.assign('/')
        }
      } else {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/ ' }).catch(() => {})
      }
    },
    CloseModalEditAccount () {
      this.ModalConfirmEditAccount = false
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    BindAccount () {
      this.ModalConfirmBindAccount = true
    },
    ShowBindAccountDialog () {
      this.BindAccountdialog = true
      this.ModalConfirmBindAccount = false
    },
    async confirm () {
      if (this.$refs.FormcreateAccount.validate(true)) {
        var data = {
          username: this.usernameOne,
          password: this.passwordOne
        }
        await this.$store.dispatch('actionBindAccount', data)
        var response = await this.$store.state.ModuleUser.stateBindAccount
        // console.log(response)
        if (response.result === 'SUCCESS') {
          this.$swal.fire({ title: 'ผูกบัญชีสำเร็จ!', text: 'กรุณา Login บัญชีใหม่เพื่อใช้งาน', icon: 'success', timer: 3000, showConfirmButton: false })
        } else {
          this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 1500 })
      }
    },
    ConfirmModalEditAccount () {
      if (this.$refs.FormUserDetail.validate(true)) {
        this.ModalConfirmEditAccount = true
      }
    },
    openEditDialog () {
      this.userdetail.img_path = this.img_path
      this.first_name = this.userdetail.first_name_th
      this.last_name = this.userdetail.last_name_th
      this.user = this.userdetail.username
      this.phone = this.userdetail.phone
      this.email = this.userdetail.email
      this.EditDialogAccount = true
    },
    CloseDialog () {
      if (this.userdetail.img_path !== '') {
        this.userdetail.img_path = this.img_path
        this.$refs.image.value = null
      } else {
        this.userdetail.img_path = null
        this.$refs.image.value = null
      }
      this.imageBase = ''
      this.$refs.FormUserDetail.resetValidation()
      this.EditDialogAccount = false
    },
    CloseModalBindAccount () {
      this.$refs.FormcreateAccount.resetValidation()
      this.$refs.FormcreateAccount.reset()
      this.BindAccountdialog = false
    },
    changePic () {
      document.getElementById('picTure').click()
    },
    onPickFile () {
      this.$refs.image.click()
    },
    showPicture (e) {
      const files = e.target.files
      if (files[0] !== undefined) {
        this.imageName = files[0].name
        const element = files[0]
        const reader = new FileReader()
        reader.readAsDataURL(element)
        reader.onload = () => {
          this.imageBase = reader.result.split(',')[1]
          this.showImage = URL.createObjectURL(element)
          this.userdetail.img_path = this.showImage
        }
      }
    },
    async EditAccount () {
      this.EditDialogAccount = false
      this.ModalConfirmEditAccount = false
      var data = {
        first_name_th: this.first_name,
        last_name_th: this.last_name,
        username: this.user,
        phone: this.phone,
        image: this.imageBase
      }
      // console.log(data, 'data')
      await this.$store.dispatch('actionsEditUserProfile', data)
      var responseEditAccount = await this.$store.state.ModuleUser.stateEditUserProfile
      // console.log(responseEditAccount, 'responseEditAccount')
      if (responseEditAccount.result === 'SUCCESS') {
        this.ModalSuccessEditAccount = true
        this.getProfile()
        this.$EventBus.$emit('getUserDetail')
        // window.location.reload()
      } else {
        this.$swal.fire({ icon: 'warning', title: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>
<style scoped>
.f-right {
  float: right;
}
.pointPic {
  opacity: 0.5;
}
.pointPic:hover {
  opacity: 1.0;
}
</style>
