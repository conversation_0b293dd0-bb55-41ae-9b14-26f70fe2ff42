<template>
  <div class="pt-3">
    <v-col cols="12" class="pa-0">
      <v-container>
      <!-- Preview single -->
      <div style="width: 100%; max-width: 1400px;" class="mx-auto" dense v-if="selectTypeLayout === 'single' || selectTypeLayout === 'singleduo' || (selectTypeLayout === '2duoright' && MobileSize)">
          <div style="width: 100%;">
            <v-carousel
              :cycle="setCycle"
              :interval="interval"
              :height="MobileSize ? '170' : IpadSize ? '200' :  IpadProSize ? '275' : '350'"
              hide-delimiter-background
              :hide-delimiters="data_banner_1.length > 1 ? false : true"
              :show-arrows="data_banner_1.length > 1 ? true : false"
              class="pa-0"
              style="border-radius: 16px;"
            >
            <template v-slot:prev="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-left.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-right.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <v-carousel-item
                v-for="(item, i) in data_banner_1"
                :key="i"
              >
                <img
                  :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                  :height="MobileSize ? '170' : IpadSize ? '200' :  IpadProSize ? '275' : '350'"
                  width="100%"
                  style="border-radius: 24px;"
                  :style="item.href !== '' ? 'cursor: pointer;' : ''"
                  @click="item.href !== '' && gotoLink(item.href)"
                  loading="lazy"
                  decoding="async"
                />
              </v-carousel-item>
            </v-carousel>
          </div>
        </div>
        <!-- Preview singleduo -->
        <div style="display: flex; justify-content: space-between; width: 100%; max-width: 1400px;" class="mx-auto mt-2" dense v-if="selectTypeLayout === 'singleduo' || (selectTypeLayout === '2duoright' && MobileSize)">
            <div style="width: 49%;">
              <v-carousel
                :cycle="setCycle"
                :interval="interval"
                :height="MobileSize ? '85' : IpadSize ? '140' : IpadProSize ? '230' : '270'"
                hide-delimiter-background
                :hide-delimiters="data_banner_2.length > 1 ? false : true"
                :show-arrows="data_banner_2.length > 1 ? true : false"
                class="pa-0"
                style="border-radius: 16px;"
              >
              <template v-slot:prev="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-left.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-right.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
                <v-carousel-item
                  v-for="(item, i) in data_banner_2"
                  :key="i"
                >
                  <v-img
                    :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                    :height="MobileSize ? '85' : IpadSize ? '140' : IpadProSize ? '230' : '270'"
                    style="border-radius: 16px;"
                    :style="item.href !== '' ? 'cursor: pointer;' : ''"
                    @click="item.href !== '' && gotoLink(item.href)"
                    alt="data_banner_2"
                    loading="lazy"
                  />
                </v-carousel-item>
              </v-carousel>
            </div>
            <div style="width: 49%;">
              <v-carousel
                :cycle="setCycle"
                :interval="interval"
                :height="MobileSize ? '85' : IpadSize ? '140' : IpadProSize ? '230' : '270'"
                hide-delimiter-background
                :hide-delimiters="data_banner_3.length > 1 ? false : true"
                :show-arrows="data_banner_3.length > 1 ? true : false"
                class="pa-0"
                style="border-radius: 16px;"
              >
              <template v-slot:prev="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-left.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-right.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
                <v-carousel-item
                  v-for="(item, i) in data_banner_3"
                  :key="i"
                >
                  <v-img
                    :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                    :height="MobileSize ? '85' : IpadSize ? '140' : IpadProSize ? '230' : '270'"
                    style="border-radius: 16px;"
                    :style="item.href !== '' ? 'cursor: pointer;' : ''"
                    @click="item.href !== '' && gotoLink(item.href)"
                    alt="data_banner_3"
                    loading="lazy"
                  />
                </v-carousel-item>
              </v-carousel>
            </div>
        </div>
        <!-- Preview 2duoright -->
        <div style="width: 100%; max-width: 1400px;" class="mx-auto" dense v-if="selectTypeLayout === '2duoright' && !MobileSize">
          <div class="d-flex" style="width: 100%;" :style="MobileSize || IpadSize ? 'gap: 6px;' :  IpadProSize ? 'gap: 10px;' : 'gap: 12px;'">
            <div :style="selectTypeLayout === '2duoright' ? `width: ${width1}%;` : ''">
              <v-carousel
                :cycle="setCycle"
                :interval="interval"
                :height="MobileSize ? '170' : IpadSize ? '210' :  IpadProSize ? '275' : '350'"
                hide-delimiter-background
                :hide-delimiters="data_banner_1.length > 1 ? false : true"
                :show-arrows="data_banner_1.length > 1 ? true : false"
                class="pa-0"
                style="border-radius: 16px;"
              >
              <template v-slot:prev="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-left.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-right.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
                <v-carousel-item
                  v-for="(item, i) in data_banner_1"
                  :key="i"
                >
                  <img
                    :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                    :height="MobileSize ? '170' : IpadSize ? '210' :  IpadProSize ? '275' : '350'"
                    width="100%"
                    style="border-radius: 24px;"
                    :style="item.href !== '' ? 'cursor: pointer;' : ''"
                    @click="item.href !== '' && gotoLink(item.href)"
                    loading="lazy"
                    decoding="async"
                  />
                </v-carousel-item>
              </v-carousel>
            </div>
            <div :style="selectTypeLayout === '2duoright' ? `width: ${width2}%;` : ''" style="display: flex; flex-direction: column; justify-content: space-between;">
              <v-carousel
                :cycle="setCycle"
                :interval="interval"
                :height="MobileSize ? '83' : IpadSize ? '103' : IpadProSize ? '136' : '172'"
                hide-delimiter-background
                :hide-delimiters="data_banner_2.length > 1 ? false : true"
                :show-arrows="data_banner_2.length > 1 ? true : false"
                class="pa-0"
                style="border-radius: 16px;"
              >
              <template v-slot:prev="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-left.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-right.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
                <v-carousel-item
                  v-for="(item, i) in data_banner_2"
                  :key="i"
                >
                  <v-img
                    :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                    :height="MobileSize ? '83' : IpadSize ? '103' : IpadProSize ? '136' : '172'"
                    width="100%"
                    style="border-radius: 16px;"
                    :style="item.href !== '' ? 'cursor: pointer;' : ''"
                    @click="item.href !== '' && gotoLink(item.href)"
                    alt="data_banner_2"
                    loading="lazy"
                  />
                </v-carousel-item>
              </v-carousel>
              <v-carousel
                :cycle="setCycle"
                :interval="interval"
                :height="MobileSize ? '83' : IpadSize ? '103' : IpadProSize ? '136' : '172'"
                hide-delimiter-background
                :hide-delimiters="data_banner_3.length > 1 ? false : true"
                :show-arrows="data_banner_3.length > 1 ? true : false"
                class="pa-0"
                style="border-radius: 16px;"
              >
              <template v-slot:prev="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-left.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
              <template v-slot:next="{ on, attrs }">
                <v-btn
                  color="rgb(255, 255, 255 , 0.9)"
                  v-bind="attrs"
                  v-on="on"
                  :style="MobileSize || IpadSize ? 'width: 24px; height: 24px;' : 'width: 40px; height: 40px;'"
                  icon
                ><img src="@/assets/ImageINET-Marketplace/Banner/arrow-right.png" :style="MobileSize || IpadSize ? 'width: 14px; height: 14px;' : 'width: 24px; height: 24px;'" /></v-btn>
              </template>
                <v-carousel-item
                  v-for="(item, i) in data_banner_3"
                  :key="i"
                >
                  <v-img
                    :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                    :height="MobileSize ? '83' : IpadSize ? '103' : IpadProSize ? '136' : '172'"
                    width="100%"
                    style="border-radius: 16px;"
                    :style="item.href !== '' ? 'cursor: pointer;' : ''"
                    @click="item.href !== '' && gotoLink(item.href)"
                    alt="data_banner_3"
                    loading="lazy"
                  />
                </v-carousel-item>
              </v-carousel>
            </div>
          </div>
        </div>
      </v-container>
    </v-col>
  </div>
</template>

<script>
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      // slides: [
      //   // { img: require('@/assets/ImageINET-Marketplace/Banner/rsz_e-factoring-1.jpg'), link: 'https://factoring.one.th/', lazy: '@/assets/ImageINET-Marketplace/Banner/rsz_e-factoring-1.jpg' },
      //   // { img: require('@/assets/ImageINET-Marketplace/Banner/BannerB2BNew.png'), link: '', lazy: '@/assets/ImageINET-Marketplace/Banner/BannerB2BNew.png' }
      //   { src: this.imageBanner }
      //   // { img: require('@/assets/ImageINET-Marketplace/Banner/rsz_1banner2.jpg'), link: '', lazy: '@/assets/ImageINET-Marketplace/Banner/rsz_banner2.jpg' }
      // ],
      BigBanner: [],
      listShop: [],
      listBanner: '',
      imageBanner: '',
      imageLazyBanner: '',
      interval: 12000,
      intervalSetZero: 999999999,
      setCycle: true,
      windowWidth: window.innerWidth,
      widthDivide: window.innerWidth / 2,
      loadingCarousel: true,
      typeGroup: '',
      imageGroupBanner: [
        {
          name: 'main',
          image: 'https://nexgencommerce.one.th/static/landing_page/banner202409301645330.webp',
          path_lazy: 'https://nexgencommerce.one.th/static/landing_page/banner202409301645330.webp',
          link: 'https://docs.google.com/forms/d/e/1FAIpQLSeHedkvGxoJeoqQFrvKK8xC54bjGsBTM8cFfs0v3qeEaUGGMQ/viewform'
        },
        {
          name: 'banner_nexgen_dx3.png',
          image: 'https://nexgencommerce.one.th/static/landing_page/banner202410081400290.webp',
          path_lazy: 'https://nexgencommerce.one.th/static/landing_page/banner_lazy202410081400290.webp',
          link: 'https://factoring.one.th/nexgen-smedbank'
        }
      ],
      selectTypeLayout: 'single',
      width1: '',
      width2: '',
      data_banner_1: [],
      data_banner_2: [],
      data_banner_3: []
    }
  },
  computed: {
    main () {
      return this.$route.name === 'HomeProduct'
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    slides () {
      return [
        {
          img: this.imageBanner, lazy: this.imageLazyBanner
        }
      ]
    }
  },
  mounted () {
    window.addEventListener('resize', () => {
      this.windowWidth = window.innerWidth
      this.widthDivide = window.innerWidth / 2
    })
  },
  async created () {
    // this.getAuthorityUser()
    this.cycle = true
    this.loadingCarousel = true
    await this.getDataBannerWeb()
  },
  methods: {
    handleMouseOver () {
      this.setCycle = false
      this.interval = this.intervalSetZero // Set interval to a very high value on mouseover
    },
    handleMouseLeave () {
      this.setCycle = true
      this.interval = 12000 // Set interval to 12000 on mouseleave
    },
    async getDataBannerWeb () {
      const data = null
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsgetDataIcon', data)
      const response = this.$store.state.ModuleAdminManage.stategetDataIcon
      if (response.code === 200) {
        // console.log('response', response)
        if (response.data.banner.length !== 0) {
          this.selectTypeLayout = response.data.banner.big_banner_type
          this.width1 = response.data.banner.big_banner_1_ratio
          this.width2 = response.data.banner.big_banner_2_ratio
        }
        if (this.selectTypeLayout === 'single') {
          this.data_banner_1 = response.data.banner.data_banner_1
        } else {
          this.data_banner_1 = response.data.banner.data_banner_1
          this.data_banner_2 = response.data.banner.data_banner_2
          this.data_banner_3 = response.data.banner.data_banner_3
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
      this.$EventBus.$emit('changeReadyCarousel')
    },
    gotoLink (href) {
      window.open(href, '_blank')
    },
    navigateToLink (link) {
      window.location.href = link
    },
    // async getAuthorityUser () {
    //   await this.$store.dispatch('actionsAuthorityUser')
    //   var response = await this.$store.state.ModuleUser.stateAuthorityUser
    //   this.listShop = response.data.list_shop_detail
    // },
    async link () {
      if (localStorage.getItem('oneData') === null) {
        // if (this.onedata.user === undefined) {
        this.$router.push({ path: '/Login' }).catch(() => {})
        // }
      } else {
        if (this.listShop !== null) {
          this.listShop = JSON.parse(Decode.decode(localStorage.getItem('AuthorityUser')))
        }
        if (this.listShop.length !== 0) {
          localStorage.setItem('shopSellerID', this.listShop[0].seller_shop_id)
          localStorage.setItem('list_shop_detail', Encode.encode(this.listShop[0]))
          var shopname = this.listShop[0].shop_name_th === null ? this.listShop[0].shop_name : this.listShop[0].shop_name_th
          if (!this.MobileSize) {
            this.$router.push({ path: '/seller?ShopID=' + this.listShop[0].seller_shop_id + '&ShopName=' + shopname }).catch(() => { })
            this.$EventBus.$emit('CheckShop')
            this.$EventBus.$emit('checkpath')
            this.$EventBus.$emit('AuthorityUsers')
          } else {
            this.$router.push({ path: '/sellerMobile?ShopID=' + this.listShop[0].seller_shop_id + '&ShopName=' + shopname }).catch(() => { })
            this.$EventBus.$emit('CheckShop')
            this.$EventBus.$emit('checkpath')
            this.$EventBus.$emit('AuthorityUsers')
          }
        } else {
          if (!this.MobileSize) {
            this.$router.push({ path: '/createShop' }).catch(() => {})
            this.$EventBus.$emit('CheckShop')
            this.$EventBus.$emit('checkpath')
          } else {
            this.$router.push({ path: '/createShopMobile' }).catch(() => {})
            this.$EventBus.$emit('CheckShop')
            this.$EventBus.$emit('checkpath')
          }
        }
      }
    },
    // async GetBanner () {
    //   this.BigBanner = []
    //   await this.$store.dispatch('actionsGetBanner')
    //   var response = await this.$store.state.ModuleHompage.stateGetBanner
    //   // console.log('response_GetBanner', response.data.image_big_banner[0])
    //   // this.listBanner = response.data.image_big_banner[0]
    //   // this.imageBanner = this.listBanner.path
    //   // this.imageLazyBanner = this.listBanner.path_lazy
    //   // console.log('imageBanner', this.imageBanner)
    //   // console.log('imageLazyBanner', this.imageLazyBanner)
    //   // console.log('ListCategory', this.ListCategory)
    //   this.$EventBus.$emit('changeReadyCarousel')
    //   if (response.data.image_big_banner_web.length > 0) {
    //     for (let i = 0; i < response.data.image_big_banner_web.length; i++) {
    //       this.BigBanner.push({
    //         image_path: response.data.image_big_banner_web[i].path,
    //         image_path_lazy: response.data.image_big_banner_web[i].path_lazy,
    //         link_banner: response.data.image_big_banner_web[i].href
    //       })
    //     }
    //     this.loadingCarousel = false
    //   } else {
    //     this.BigBanner = []
    //     this.loadingCarousel = false
    //   }
    // },
    handleClick (link) {
      window.open(link, '_blank')
    }
  }
}
</script>

<style scoped>
::v-deep .v-btn--icon.v-size--default {
  background-color: rgb(255, 255, 255 , 0.9);
}
::v-deep .v-carousel__controls__item.v-btn.v-btn--icon {
  height: 10px;
  width: 10px;
  border-radius: 50%;
}
::v-deep .v-carousel__controls {
  height: 25px;
}
::v-deep .v-carousel__controls .v-btn--icon.theme--dark ::before {
  font-size: x-small;
}
/*
::v-deep .v-carousel__controls {
  height: 12px;
  bottom: 5px;
  font-size: x-small;
} */
::v-deep .v-window__next {
  top: calc(50% - 10px);
}
::v-deep .v-window__prev {
  top: calc(50% - 10px);
}
@media (max-width: 900px) {
  ::v-deep .v-btn--icon.v-size--default {
    background-color: rgba(255, 255, 255, 0.85);
  }
  ::v-deep .v-carousel__controls__item.v-btn.v-btn--icon {
    height: 10px;
    width: 10px;
    border-radius: 50%;
  }
  ::v-deep .v-carousel__controls .v-btn--icon.theme--dark ::before {
    font-size: x-small;
  }
}
</style>
