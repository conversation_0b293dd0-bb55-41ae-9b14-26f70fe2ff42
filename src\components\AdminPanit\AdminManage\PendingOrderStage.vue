<template>
  <v-container v-if="!MobileSize" class="pl-3 pr-3 pt-4 pb-4">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
    <div class="d-flex align-center">
      <v-card-title style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333;">Order stage คงค้าง</v-card-title>
      <v-spacer></v-spacer>
      <v-menu v-model="menu" offset-y :close-on-content-click="false">
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-if="!MobileSize" v-bind="attrs" v-on="on" :style="!IpadProSize && !IpadSize ? 'padding: 35px;' : 'padding: 35px; width: 26.5vw;'" color="#f4fcff">
            <div style="display: flex; gap: 1vw;">
              <v-icon v-if="!IpadSize" color="#2d9cdb" style="background-color: #d6eefa; padding: 5px; border-radius: .5vw;">mdi-calendar</v-icon>
              <div style="display: flex; flex-direction: column; align-items: flex-start;">
                <span>Filter Period</span>
                <span style="font-size: 12px">{{ displayDateFilterText }}</span>
              </div>
              <v-icon class="mdi-rotate-180 mb-2" color="#b9bbbd">mdi-apple-keyboard-control</v-icon>
            </div>
          </v-btn>
          <v-btn v-else v-bind="attrs" v-on="on" style="padding: .5vw;" color="#f4fcff">
            <div style="display: flex; gap: 1vw;">
              <v-icon color="#2d9cdb">mdi-filter</v-icon>
              <div style="display: flex; flex-direction: column; align-items: flex-start;">
                <span>ตัวกรอง</span>
              </div>
            </div>
          </v-btn>
        </template>
        <v-card>
          <v-card-text>
            <v-radio-group v-model="selectedFilterType" row>
              <v-radio label="รายปี" value="yearly"></v-radio>
              <v-radio label="รายเดือน" value="monthly"></v-radio>
              <v-radio label="รายวัน" value="daily"></v-radio>
            </v-radio-group>
            <template v-if="selectedFilterType === 'yearly'">
              <v-select v-model="selectedYear" :items="years" label="ปี" dense outlined>
                <template slot="selection">
                  {{selectedYear + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
            </template>
            <template v-if="selectedFilterType === 'monthly'">
              <v-select v-model="selectedYearMonth" :items="years" label="ปี" dense outlined>
                <template slot="selection">
                  {{selectedYearMonth + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
              <v-select v-model="selectedMonth" :items="months" label="เดือน" dense outlined></v-select>
            </template>
            <template v-if="selectedFilterType === 'daily'">
              <v-date-picker
                v-model="selectedDays" range locale="th" :show-current="true" @change="handleDateChange"
                :max="new Date( Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)">
              </v-date-picker>
            </template>
          </v-card-text>
          <v-card-actions style="display: flex; justify-content: center;">
            <v-btn color="primary" @click="applyDateFilter()">ยืนยัน</v-btn>
            <v-btn text @click="menu = false">ยกเลิก</v-btn>
          </v-card-actions>
        </v-card>
      </v-menu>
    </div>
      <v-row class="mt-2">
        <!-- waiting -->
        <v-col :cols="IpadSize ? 12 : 6">
          <v-card-title style="width: 100%; font-size: medium; display: flex; justify-content: center; color: #4d5e80;
            font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
            {{titleWaiting}}
          </v-card-title>
          <v-data-table
            :headers="header1"
            :items="itemWaiting"
            class="elevation-1 flex-table mb-2"
            fixed-header
            hide-default-footer
            disable-pagination
            id="waiting"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="max-height: 400px; overflow-y: auto;"
          >
            <template v-slot:[`item.name_th`]="{ item }">
                <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
              </template>
            <template slot="body.append">
              <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
                <th class="th1" style="white-space: nowrap; text-align: center;">รวมทั้งสิ้น {{ totalWaiting.summary_shop }} ร้านค้า</th>
                <th class="th1" style="text-align: center;">{{ totalWaiting.summary_normal }}</th>
                <th class="th1" style="text-align: center;">{{ totalWaiting.summary_monitor }}</th>
                <th class="th1" style="text-align: center;">{{ totalWaiting.summary_alarm }}</th>
                <th class="th1" style="text-align: center;">{{ totalWaiting.summary_total }}</th>
              </tr>
            </template>
          </v-data-table>
        </v-col>
        <!--waiting out_source -->
        <v-col :cols="IpadSize ? 12 : 6">
          <v-card-title style="width: 100%; font-size: medium; display: flex; justify-content: center; color: #4d5e80;
            font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
            {{titleWaitingOutSource}}
          </v-card-title>
          <v-data-table
            :headers="header1"
            :items="itemWaitingOutSource"
            class="elevation-1 flex-table mb-2"
            fixed-header
            hide-default-footer
            disable-pagination
            id="outsource"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="max-height: 400px; overflow-y: auto;"
          >
          <template v-slot:[`item.name_th`]="{ item }">
              <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
            </template>
          <template slot="body.append">
            <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
              <th class="th1" style="white-space: nowrap; text-align: center;">รวมทั้งสิ้น {{ totalWaitingOutSource.summary_shop }} ร้านค้า</th>
              <th class="th1" style="text-align: center;">{{ totalWaitingOutSource.summary_normal }}</th>
              <th class="th1" style="text-align: center;">{{ totalWaitingOutSource.summary_monitor }}</th>
              <th class="th1" style="text-align: center;">{{ totalWaitingOutSource.summary_alarm }}</th>
              <th class="th1" style="text-align: center;">{{ totalWaitingOutSource.summary_total }}</th>
            </tr>
          </template>
        </v-data-table>
        </v-col>
        <!-- picked_up -->
        <v-col :cols="IpadSize ? 12 : 6">
          <v-card-title style="width: 100%; font-size: medium; display: flex; justify-content: center; color: #4d5e80;
            font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
            {{titlePickedUp}}
          </v-card-title>
          <v-data-table
            :headers="header1"
            :items="itemPickedUp"
            class="elevation-1 flex-table mb-2"
            fixed-header
            hide-default-footer
            disable-pagination
            id="pickedup"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="max-height: 400px; overflow-y: auto;"
          >
          <template v-slot:[`item.name_th`]="{ item }">
            <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
          </template>
          <template slot="body.append">
            <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
              <th class="th1" style="white-space: nowrap; text-align: center;">รวมทั้งสิ้น {{ totalPickedUp.summary_shop }} ร้านค้า</th>
              <th class="th1" style="text-align: center;">{{ totalPickedUp.summary_normal }}</th>
              <th class="th1" style="text-align: center;">{{ totalPickedUp.summary_monitor }}</th>
              <th class="th1" style="text-align: center;">{{ totalPickedUp.summary_alarm }}</th>
              <th class="th1" style="text-align: center;">{{ totalPickedUp.summary_total }}</th>
            </tr>
          </template>
        </v-data-table>
        </v-col>
        <!-- shipping OutSource-->
        <v-col :cols="IpadSize ? 12 : 6">
          <v-card-title style="width: 100%; font-size: medium; display: flex; justify-content: center; color: #4d5e80;
            font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
            {{titleOutSource}}
          </v-card-title>
          <v-data-table
            :headers="header1"
            :items="itemOutSource"
            class="elevation-1 flex-table"
            fixed-header
            hide-default-footer
            disable-pagination
            id="shipping"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="max-height: 400px; overflow-y: auto;"
          >
          <template v-slot:[`item.name_th`]="{ item }">
              <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
            </template>
          <template slot="body.append">
            <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
              <th class="th1" style="white-space: nowrap; text-align: center;">รวมทั้งสิ้น {{ totalOutSource.summary_shop }} ร้านค้า</th>
              <th class="th1" style="text-align: center;">{{ totalOutSource.summary_normal }}</th>
              <th class="th1" style="text-align: center;">{{ totalOutSource.summary_monitor }}</th>
              <th class="th1" style="text-align: center;">{{ totalOutSource.summary_alarm }}</th>
              <th class="th1" style="text-align: center;">{{ totalOutSource.summary_total }}</th>
            </tr>
          </template>
        </v-data-table>
        </v-col>
        <!-- shipping NGC-->
        <v-col :cols="IpadSize ? 12 : 6">
          <v-card-title style="width: 100%; font-size: medium; display: flex; justify-content: center; color: #4d5e80;
            font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
            {{titleShipping}}
          </v-card-title>
          <v-data-table
            :headers="header1"
            :items="itemShipping"
            class="elevation-1 flex-table"
            fixed-header
            hide-default-footer
            disable-pagination
            id="shipping"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="max-height: 400px; overflow-y: auto;"
          >
          <template v-slot:[`item.name_th`]="{ item }">
              <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
            </template>
          <template slot="body.append">
            <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
              <th class="th1" style="white-space: nowrap; text-align: center;">รวมทั้งสิ้น {{ totalShipping.summary_shop }} ร้านค้า</th>
              <th class="th1" style="text-align: center;">{{ totalShipping.summary_normal }}</th>
              <th class="th1" style="text-align: center;">{{ totalShipping.summary_monitor }}</th>
              <th class="th1" style="text-align: center;">{{ totalShipping.summary_alarm }}</th>
              <th class="th1" style="text-align: center;">{{ totalShipping.summary_total }}</th>
            </tr>
          </template>
        </v-data-table>
        </v-col>
        <!-- return -->
        <v-col :cols="IpadSize ? 12 : 6">
          <v-card-title style="width: 100%; font-size: medium; display: flex; justify-content: center; color: #4d5e80;
            font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
            รายชื่อร้านค้า สถานะติดต่อผู้รับไม่ได้ (ตีกลับ)
          </v-card-title>
          <v-data-table
          :headers="header4"
            :items="itemReturn"
            class="elevation-1 flex-table tableReturn mb-2"
            fixed-header
            hide-default-footer
            disable-pagination
            id="return"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="max-height: 400px; overflow-y: auto;"
          >
          <template v-slot:[`item.name_th`]="{ item }">
            <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
          </template>
          <template slot="body.append">
            <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
              <th class="th1" style="white-space: nowrap; text-align: center;">รวมทั้งสิ้น {{ totalReturn.summary_shop }} ร้านค้า</th>
              <th class="th1" style="text-align: center;">{{ totalReturn.summary_normal }}</th>
              <th class="th1" style="text-align: center;">{{ totalReturn.summary_monitor }}</th>
              <th class="th1" style="text-align: center;">{{ totalReturn.summary_alarm }}</th>
              <th class="th1" style="text-align: center;">{{ totalReturn.summary_return_success }}</th>
              <th class="th1" style="text-align: center;">{{ totalReturn.summary_normal + totalReturn.summary_monitor + totalReturn.summary_alarm + totalReturn.summary_return_success }}</th>
              <!-- <th class="th1" style="text-align: center;">{{ totalReturn.summary_normal + totalReturn.summary_monitor + totalReturn.summary_alarm + totalReturn.summary_return_success }}</th> -->
            </tr>
          </template>
        </v-data-table>
        </v-col>
        <!-- cancel -->
        <v-col :cols="IpadSize ? 12 : 12">
            <v-card-title style="font-size: medium; display: flex; justify-content: center; color: #4d5e80;
              font-weight: 600; background-color: #f0f0f0;border: black 1px dashed; margin: 5px 0; padding: 5px 0;">
              รายชื่อร้านค้า สถานะยกเลิก (ออเดอร์)
            </v-card-title>
            <v-data-table
              :headers="header2"
              :items="itemCancel"
              class="elevation-1 flex-table mb-2"
              fixed-header
              hide-default-footer
              disable-pagination
              id="cancel"
            height="400px"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
              style="max-height: 400px; overflow-y: auto;"
            >
            <template v-slot:[`item.name_th`]="{ item }">
            <td style="white-space: nowrap; text-align: left;">{{ item.name_th }}</td>
          </template>
            <template slot="body.append">
              <tr style="position: sticky; z-index: 20; bottom: 0; background-color: white;">
                <th class="th1" style="text-align: center; width: 40% !important;">รวมทั้งสิ้น {{ totalCancel.summary_shop }} ร้านค้า</th>
                <th class="th1" style="text-align: center; width: 20% !important;">0</th>
                <th class="th1" style="text-align: center; width: 20% !important;">{{ totalCancel.summary_total }}</th>
                <th class="th1" style="text-align: center; width: 20% !important;">{{ totalCancel.summary_total }}</th>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
  <v-container v-else-if="MobileSize" class="pl-1 pr-1 mt-2 mb-2">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <div class="d-flex align-center">
        <v-card-title style="font-weight: 700; line-height: 0.5;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>Order stage คงค้าง</v-card-title>
      <v-spacer></v-spacer>
      </div>
      <div style="display: flex; justify-content: end;" class="mr-2">
      <v-menu v-model="menu" offset-y :close-on-content-click="false">
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-if="!MobileSize" v-bind="attrs" v-on="on" :style="!IpadProSize && !IpadSize ? 'padding: 35px;' : 'padding: 35px; width: 26.5vw;'" color="#f4fcff">
            <div style="display: flex; gap: 1vw;">
              <v-icon v-if="!IpadSize" color="#2d9cdb" style="background-color: #d6eefa; padding: 5px; border-radius: .5vw;">mdi-calendar</v-icon>
              <div style="display: flex; flex-direction: column; align-items: flex-start;">
                <span>Filter Period</span>
                <span style="font-size: 12px">{{ displayDateFilterText }}</span>
              </div>
              <v-icon class="mdi-rotate-180 mb-2" color="#b9bbbd">mdi-apple-keyboard-control</v-icon>
            </div>
          </v-btn>
          <v-btn v-else v-bind="attrs" v-on="on" style="padding: .5vw;" color="#f4fcff">
            <div style="display: flex; gap: 1vw;">
              <v-icon color="#2d9cdb">mdi-filter</v-icon>
              <div style="display: flex; flex-direction: column; align-items: flex-start;">
                <span>ตัวกรอง</span>
              </div>
            </div>
          </v-btn>
        </template>
        <v-card>
          <v-card-text>
            <v-radio-group v-model="selectedFilterType" row>
              <v-radio label="รายปี" value="yearly"></v-radio>
              <v-radio label="รายเดือน" value="monthly"></v-radio>
              <v-radio label="รายวัน" value="daily"></v-radio>
            </v-radio-group>
            <template v-if="selectedFilterType === 'yearly'">
              <v-select v-model="selectedYear" :items="years" label="ปี" dense outlined>
                <template slot="selection">
                  {{selectedYear + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
            </template>
            <template v-if="selectedFilterType === 'monthly'">
              <v-select v-model="selectedYearMonth" :items="years" label="ปี" dense outlined>
                <template slot="selection">
                  {{selectedYearMonth + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
              <v-select v-model="selectedMonth" :items="months" label="เดือน" dense outlined></v-select>
            </template>
            <template v-if="selectedFilterType === 'daily'">
              <v-date-picker
                v-model="selectedDays" range locale="th" :show-current="true" @change="handleDateChange"
                :max="new Date( Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)">
              </v-date-picker>
            </template>
          </v-card-text>
          <v-card-actions style="display: flex; justify-content: center;">
            <v-btn color="primary" @click="applyDateFilter()">ยืนยัน</v-btn>
            <v-btn text @click="menu = false">ยกเลิก</v-btn>
          </v-card-actions>
        </v-card>
      </v-menu>
    </div>
      <v-row class="mt-2">
        <v-col cols="12">
          <v-select
          v-model="mobileTitlesSelected"
          :items="mobileTitles"
          hide-details
          outlined
          label="เลือกเมนู"
          style="padding: 10px;"
          >
            <template slot="selection">
              <span style="font-weight: 600; font-size: 16px;">{{mobileTitlesSelected}}</span>
            </template>
          </v-select>
        </v-col>
      </v-row>
      <!-- สถานะเตรียมจัดส่ง (ขนส่งใน NGC) -->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะเตรียมจัดส่ง (ขนส่งใน NGC)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalWaiting.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 10px;">
          <v-col cols="5" style="background-color: #c0f5e3; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Normal</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaiting.summary_normal | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaiting.summary_normal }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #fff6a4; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Monitor</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaiting.summary_monitor | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaiting.summary_monitor }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Alarm</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaiting.summary_alarm | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaiting.summary_alarm }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaiting.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaiting.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
      </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header1"
            :items="itemWaiting"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
              no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
      </v-row>
      <!-- สถานะเตรียมจัดส่ง (ขนส่งนอก) -->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะเตรียมจัดส่ง (ขนส่งนอก)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalOutSource.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 10px;">
          <v-col cols="5" style="background-color: #c0f5e3; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Normal</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaitingOutSource.summary_normal | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaitingOutSource.summary_normal }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #fff6a4; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Monitor</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaitingOutSource.summary_monitor | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaitingOutSource.summary_monitor }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Alarm</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaitingOutSource.summary_alarm | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaitingOutSource.summary_alarm }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalWaitingOutSource.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalWaitingOutSource.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header1"
            :items="itemOutSource"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
              no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
      </v-row>
      <!-- สถานะเข้าระบบขนส่ง (ขนส่งใน NGC) -->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะเข้าระบบขนส่ง (ขนส่งใน NGC)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalPickedUp.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 10px;">
          <v-col cols="5" style="background-color: #c0f5e3; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Normal</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalPickedUp.summary_normal | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalPickedUp.summary_normal }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #fff6a4; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Monitor</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalPickedUp.summary_monitor | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalPickedUp.summary_monitor }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Alarm</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalPickedUp.summary_alarm | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalPickedUp.summary_alarm }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalPickedUp.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalPickedUp.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header1"
            :items="itemPickedUp"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
            no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
      </v-row>
      <!-- สถานะระหว่างจัดส่ง (ขนส่งนอก)-->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะระหว่างจัดส่ง (ขนส่งนอก)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalPickedUp.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 10px;">
          <v-col cols="5" style="background-color: #c0f5e3; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Normal</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalOutSource.summary_normal | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalOutSource.summary_normal }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #fff6a4; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Monitor</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalOutSource.summary_monitor | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalOutSource.summary_monitor }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Alarm</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalOutSource.summary_alarm | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalOutSource.summary_alarm }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalOutSource.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalOutSource.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header1"
            :items="itemPickedUp"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
              no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
      </v-row>
      <!-- สถานะระหว่างจัดส่ง (ขนส่งใน NGC) -->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะระหว่างจัดส่ง (ขนส่งใน NGC)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalShipping.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 10px;">
          <v-col cols="5" style="background-color: #c0f5e3; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Normal</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalShipping.summary_normal | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalShipping.summary_normal }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #fff6a4; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Monitor</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalShipping.summary_monitor | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalShipping.summary_monitor }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Alarm</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalShipping.summary_alarm | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalShipping.summary_alarm }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalShipping.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalShipping.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
      </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header1"
            :items="itemShipping"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
              no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
      </v-row>
      <!-- สถานะติดต่อผู้รับไม่ได้ (ตีกลับ) -->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะติดต่อผู้รับไม่ได้ (ตีกลับ)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalReturn.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 10px;">
          <v-col cols="5" style="background-color: #c0f5e3; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Normal</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalReturn.summary_normal | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalReturn.summary_normal }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #fff6a4; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Monitor</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalReturn.summary_monitor | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalReturn.summary_monitor }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Order Alarm</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalReturn.summary_alarm | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalReturn.summary_alarm }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #ff9966; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">ตีกลับสำเร็จ</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalReturn.summary_return_success | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalReturn.summary_return_success }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px; margin-top: 12px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalReturn.summary_normal + totalReturn.summary_monitor + totalReturn.summary_alarm + totalReturn.summary_return_success | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalReturn.summary_normal + totalReturn.summary_monitor + totalReturn.summary_alarm + totalReturn.summary_return_success }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
        </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header3"
            :items="itemReturn"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
              no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
      </v-row>
      <!-- สถานะยกเลิก (ออเดอร์) -->
      <v-row v-if="MobileSize && mobileTitlesSelected === 'สถานะยกเลิก (ออเดอร์)'">
        <v-col cols="12">
        <v-card-title style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;">รวมทั้งสิ้น {{ totalCancel.summary_shop }} ร้านค้า</v-card-title>
        </v-col>
        <v-col cols="12">
        <v-row style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px;">
          <v-col cols="5" style="background-color: #f38183; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">ลูกค้ายกเลิก</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  0
                </v-card-title>
              </template>
              <span>0</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #ff9966; color: black; border-radius: 15px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">ร้านค้ายกเลิก</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalCancel.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalCancel.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        <v-col cols="5" style="background-color: #d8e9ff; color: black; border-radius: 15px; margin-top: 12px;">
          <v-card-title style="font-size: small; font-weight: 600; display: flex; justify-content: center; line-height: 0.8;">Total</v-card-title>
          <v-card width="70%" height="50%" style="margin: auto; border-radius: 15px;">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-card-title
                  v-bind="attrs"
                  v-on="on"
                  style="font-size: medium; font-weight: 600; display: flex; justify-content: center; line-height: 1;"
                >
                  {{ totalCancel.summary_total | formatNumber }}
                </v-card-title>
              </template>
              <span>{{ totalCancel.summary_total }}</span>
            </v-tooltip>
          </v-card>
        </v-col>
        </v-row>
      </v-col>
        <v-col cols="12" style="font-size: x-small !important;">
          <v-data-table
            :headers="header2"
            :items="itemCancel"
            class="elevation-1 flex-table mobile-size mb-2"
            :items-per-page="5"
            no-results-text="ไม่พบข้อมูล"
              no-data-text="ไม่พบข้อมูล"
            style="width: 96%;margin: 0 2%;"
          >
          </v-data-table>
        </v-col>
        </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import Vue from 'vue'
export default {
  data () {
    return {
      itemWaiting: [],
      totalWaiting: [],
      itemOutSource: [],
      totalOutSource: [],
      itemPickedUp: [],
      totalPickedUp: [],
      itemReturn: [],
      totalReturn: [],
      itemShipping: [],
      totalShipping: [],
      itemCancel: [],
      totalCancel: [],
      headers: [],
      menu: false,
      selectedFilterType: 'yearly',
      selectedYear: new Date().getFullYear(),
      selectedYearMonth: new Date().getFullYear(),
      selectedMonth: String(new Date().getMonth() + 1).padStart(2, '0'),
      selectedDays: [],
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      dateFilter: {
        start_date: '',
        end_date: ''
      },
      displayDateFilterText: '',
      mobileTitles: [
        'สถานะเตรียมจัดส่ง (ขนส่งใน NGC)',
        'สถานะเตรียมจัดส่ง (ขนส่งนอก)',
        'สถานะเข้าระบบขนส่ง (ขนส่งใน NGC)',
        'สถานะระหว่างจัดส่ง (ขนส่งนอก)',
        'สถานะระหว่างจัดส่ง (ขนส่งใน NGC)',
        'สถานะติดต่อผู้รับไม่ได้ (ตีกลับ)',
        'สถานะยกเลิก (ออเดอร์)'
      ],
      mobileTitlesSelected: 'สถานะเตรียมจัดส่ง (ขนส่งใน NGC)',
      header1: [
        { text: 'Merchant Name', value: 'name_th', width: '180', sortable: true, align: 'center', class: 'th1' },
        { text: 'Normal', value: 'normal', width: '100', sortable: true, align: 'center', class: 'th2' },
        { text: 'Monitor', value: 'monitor', width: '100', sortable: true, align: 'center', class: 'th3' },
        { text: 'Alarm', value: 'alarm', width: '100', sortable: true, align: 'center', class: 'th4' },
        { text: 'Total', value: 'total_orders', width: '80', sortable: true, align: 'center', class: 'th1', fixed: true, right: true }
      ],
      header2: [
        { text: 'Merchant Name', value: 'name_th', width: '180', sortable: true, align: 'center', class: 'th1' },
        { text: 'ลูกค้ายกเลิก', value: 'customer_cancel', width: '100', sortable: true, align: 'center', class: 'th5' },
        { text: 'ร้านค้ายกเลิก', value: 'shop_cancel', width: '100', sortable: true, align: 'center', class: 'th5' },
        { text: 'Total', value: 'total_orders', width: '80', sortable: true, align: 'center', class: 'th1' }
      ],
      header3: [
        { text: 'Merchant Name', value: 'name_th', sortable: true, align: 'center', class: 'th1' },
        { text: 'Normal (ตีกลับ)', value: 'normal', sortable: true, align: 'center', class: 'th2' },
        { text: 'Monitor (ตีกลับ)', value: 'monitor', sortable: true, align: 'center', class: 'th3' },
        { text: 'Alarm (ตีกลับ)', value: 'alarm', sortable: true, align: 'center', class: 'th4' },
        { text: 'ตีกลับสำเร็จ', value: 'total_return_success', sortable: true, align: 'center', class: 'th4' },
        { text: 'Total', value: 'total_orders', sortable: true, align: 'center', class: 'th1', fixed: true, right: true }
      ],
      header4: [
        { text: 'Merchant Name', value: 'name_th', width: '180', sortable: true, align: 'center', class: 'th1' },
        { text: 'Normal', value: 'normal', width: '100', sortable: true, align: 'center', class: 'th2' },
        { text: 'Monitor', value: 'monitor', width: '100', sortable: true, align: 'center', class: 'th3' },
        { text: 'Alarm', value: 'alarm', width: '100', sortable: true, align: 'center', class: 'th4' },
        { text: 'ตีกลับสำเร็จ', value: 'total_return_success', width: '120', sortable: true, align: 'center', class: 'th1' },
        { text: 'Total', value: 'total_orders', width: '80', sortable: true, align: 'center', class: 'th1', fixed: true, right: true }
      ],
      titleCancel: '',
      titleShipping: '',
      titleReturn: '',
      titlePickedUp: '',
      titleOutSource: '',
      titleWaiting: '',
      titleWaitingOutSource: '',
      itemWaitingOutSource: [],
      totalWaitingOutSource: []
    }
  },
  created () {
    this.applyDateFilter()
    this.generateYears()
    this.getDataStage(this.dateFilter, 'active')
    this.formatNumber()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dateFilterText () {
      const isLeapYear = (year) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
      const monthsWith30Days = ['04', '06', '09', '11']
      const monthsWith31Days = ['01', '03', '05', '07', '08', '10', '12']
      // Get current date details
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = String(today.getMonth() + 1).padStart(2, '0') // month is zero-based
      const currentDay = String(today.getDate()).padStart(2, '0')
      if (this.selectedFilterType === 'yearly') {
        const isCurrentYear = parseInt(this.selectedYear) === currentYear
        const endDate = isCurrentYear
          ? `${currentYear}-${currentMonth}-${currentDay}`
          : `${this.selectedYear}-12-31`
        return `${this.selectedYear}-01-01 - ${endDate}`
      } else if (this.selectedFilterType === 'monthly') {
        if (this.selectedMonth !== null) {
          const isCurrentMonthAndYear =
            (parseInt(this.selectedYearMonth) === currentYear && this.selectedMonth === currentMonth)
          let endDate
          if (monthsWith31Days.includes(this.selectedMonth)) {
            endDate = isCurrentMonthAndYear ? currentDay : '31'
          } else if (this.selectedMonth === '02') {
            endDate = isLeapYear(parseInt(this.selectedYearMonth)) ? '29' : '28'
            if (isCurrentMonthAndYear) endDate = currentDay
          } else if (monthsWith30Days.includes(this.selectedMonth)) {
            endDate = isCurrentMonthAndYear ? currentDay : '30'
          }
          return `${this.selectedYearMonth}-${this.selectedMonth}-01 - ${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
        } else {
          return ''
        }
      } else if (this.selectedDays.length > 1) {
        if (this.selectedDays[0] > this.selectedDays[1]) {
          return `${this.selectedDays[1]} - ${this.selectedDays[0]}`
        } else {
          return `${this.selectedDays[0]} - ${this.selectedDays[1]}`
        }
      } else if (this.selectedDays.length === 1) {
        return `${this.selectedDays[0]} - ${this.selectedDays[0]}`
      } else {
        return this.selectedDays[0] || ''
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/pendingOrderStageMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'pendingOrderStage')
        this.$router.push({ path: '/pendingOrderStage' }).catch(() => {})
      }
    },
    selectedFilterType (val) {
      this.selectedYear = new Date().getFullYear()
      if (val === 'daily') {
        this.formatCurrentDate()
      } else if (val === 'monthly') {
        this.selectedMonth = String(new Date().getMonth() + 1).padStart(2, '0')
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.displayDateFilterText = this.dateFilter.start_date + ' - ' + this.dateFilter.end_date
    this.selectedFilterType = 'yearly'
    this.displayDateFilterText = this.dateFilterText
    this.intervalId = setInterval(() => {
      this.getDataStage(this.dateFilter)
    }, 10000)
  },
  beforeDestroy () {
    clearInterval(this.intervalId)
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    handleDateChange (dates) {
      this.selectedDays = dates
    },
    async applyDateFilter () {
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = String(today.getMonth() + 1).padStart(2, '0')
      const currentDay = String(today.getDate()).padStart(2, '0')
      if (this.selectedFilterType === 'yearly') {
        const isCurrentYear = parseInt(this.selectedYear) === currentYear
        this.dateFilter = {
          start_date: `${this.selectedYear}-01-01`,
          end_date: isCurrentYear ? `${currentYear}-${currentMonth}-${currentDay}` : `${this.selectedYear}-12-31`
        }
        this.menu = false
        this.getDataStage(this.dateFilter, 'active')
      } else if (this.selectedFilterType === 'monthly') {
        const isLeapYear = (year) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
        const monthsWith31Days = ['01', '03', '05', '07', '08', '10', '12']
        const monthsWith30Days = ['04', '06', '09', '11']
        if (this.selectedMonth !== null) {
          const isCurrentMonthAndYear = (parseInt(this.selectedYearMonth) === currentYear && this.selectedMonth === currentMonth)
          let endDate
          if (this.selectedMonth === '02') {
            endDate = isLeapYear(parseInt(this.selectedYearMonth)) ? '29' : '28'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.dateFilter = {
              start_date: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end_date: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          } else if (monthsWith30Days.includes(this.selectedMonth)) {
            endDate = '30'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.dateFilter = {
              start_date: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end_date: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          } else if (monthsWith31Days.includes(this.selectedMonth)) {
            endDate = '31'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.dateFilter = {
              start_date: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end_date: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          }
          this.menu = false
          this.getDataStage(this.dateFilter, 'active')
        } else {
          await this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกเดือน'
          })
          this.menu = true
        }
      } else if (this.selectedFilterType === 'daily') {
        if (this.selectedDays.length !== 0) {
          if (this.selectedDays.length === 1) {
            this.dateFilter = { start_date: this.selectedDays[0], end_date: this.selectedDays[0] }
          } else if (this.selectedDays.length > 1) {
            if (this.selectedDays[0] > this.selectedDays[1]) {
              this.dateFilter = {
                start_date: this.selectedDays[1],
                end_date: this.selectedDays[0]
              }
            } else {
              this.dateFilter = {
                start_date: this.selectedDays[0],
                end_date: this.selectedDays[1]
              }
            }
          }
          this.menu = false
          this.getDataStage(this.dateFilter, 'active')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกวัน'
          })
          this.menu = true
        }
      }
      // this.displayDateFilterText = this.dateFilter.start_date + ' - ' + this.dateFilter.end_date
      this.displayDateFilterText = this.dateFilterText
      // console.log('this.displayDateFilterText', this.displayDateFilterText)
      if (this.selectedFilterType === 'yearly') {
        this.selectedMonth = null
        this.selectedDays = []
        this.selectedYearMonth = new Date().getFullYear()
      } else if (this.selectedFilterType === 'monthly') {
        this.selectedYear = new Date().getFullYear()
        this.selectedDays = []
      } else {
        this.selectedYear = new Date().getFullYear()
        this.selectedMonth = null
        this.selectedYearMonth = new Date().getFullYear()
      }
    },
    async generateYears () {
      this.years = []
      const currentYear = new Date().getFullYear()
      const years = []
      for (let year = 2022; year <= currentYear; year++) {
        years.push(year)
      }
      this.years = years
    },
    async getDataStage (data, active) {
      if (active) {
        this.$store.commit('openLoader')
      }
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}iship/dashboard/stage`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST'
      }).then((response) => {
        // console.log('response.data.data', response.data.data.find(stage => stage.stage_name_en === 'waiting').shop_data)
        // console.log('response.data', response.data)
        this.itemWaiting = response.data.data.waiting_ngc.shop_data
        this.itemOutSource = response.data.data.shipping_outsource.shop_data
        this.itemPickedUp = response.data.data.picked_up.shop_data
        this.itemReturn = response.data.data.return.shop_data
        this.itemShipping = response.data.data.shipping_ngc.shop_data
        this.itemCancel = response.data.data.cancel.shop_data
        this.itemWaitingOutSource = response.data.data.waiting_outsource.shop_data
        // console.log('totalWaiting', response.data.data.find(stage => stage.stage_name_en === 'waiting'))
        this.totalWaiting = response.data.data.waiting_ngc
        this.totalOutSource = response.data.data.shipping_outsource
        this.totalPickedUp = response.data.data.picked_up
        this.totalReturn = response.data.data.return
        this.totalShipping = response.data.data.shipping_ngc
        this.totalCancel = response.data.data.cancel
        this.totalWaitingOutSource = response.data.data.waiting_outsource

        this.titleWaiting = response.data.data.waiting_ngc.stage_name_th
        this.titleOutSource = response.data.data.shipping_outsource.stage_name_th
        this.titlePickedUp = response.data.data.picked_up.stage_name_th
        this.titleReturn = response.data.data.return.stage_name_th
        this.titleShipping = response.data.data.shipping_ngc.stage_name_th
        this.titleCancel = response.data.data.cancel.stage_name_th
        this.titleWaitingOutSource = response.data.data.waiting_outsource.stage_name_th
      })
      if (active) {
        this.$store.commit('closeLoader')
      }
    },
    formatNumber () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000000) {
          return (Math.floor(value / 100000) / 10) + ' M'
        } else if (value >= 1000) {
          return (Math.floor(value / 100) / 10) + ' K'
        }
        return value.toString()
      })
    },
    async formatCurrentDate () {
      var datenow = new Date()
      var year = datenow.getFullYear()
      var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
      var day = datenow.getDate().toString().padStart(2, '0')
      var formattedDate = `${year}-${month}-${day}`
      this.selectedDays = [formattedDate]
    }
  }
}
</script>

<style scoped>
::v-deep th {
  position: sticky;
  top: 0;
  right: 0;
  z-index: 20;
  background-color: white;
}
.header2 ::v-deep tr {
  position: sticky;
  top: 0;
  right: 0;
  z-index: 20;
  background-color: white;
}
::v-deep tbody td:nth-child(5) {
  position: sticky;
  right: 0;
  z-index: 1;
  background-color: white;
}
.tableReturn ::v-deep tbody td:nth-child(6) {
  position: sticky;
  right: 0;
  z-index: 1;
  background-color: white;
}
::v-deep th.th1 {
  background-color: #d8e9ff !important;
  color: black !important;
}
::v-deep th.th5 {
  background-color: #d8e9ff !important;
  color: black !important;
  white-space: nowrap;
}
::v-deep th.th2 {
  background-color: #c0f5e3 !important;
  color: black !important;
}
::v-deep th.th3 {
  background-color: #fff6a4 !important;
  color: black !important;
}
::v-deep th.th4 {
  background-color: #f38183 !important;
  color: black !important;
}
</style>

<style scoped>
.flex-table {
  width: 100%;
}

.flex-table > div {
  width: 100%;
}

#waiting .v-data-footer, #return .v-data-footer, #outsource .v-data-footer, #pickedup .v-data-footer, #shipping .v-data-footer, #cancel .v-data-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: white;
}

#waiting .v-data-table__wrapper, #return .v-data-table__wrapper, #outsource .v-data-table__wrapper, #pickedup .v-data-table__wrapper, #shipping .v-data-table__wrapper, #cancel .v-data-table__wrapper {
  margin-bottom: 60px;
}

.v-data-table ::-webkit-scrollbar {
  width: 10px;
}

/* Track */
.v-data-table ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
.v-data-table ::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 5px;
}

.v-data-table ::-webkit-scrollbar-button {
  display: none;
}

/* Handle on hover */
.v-data-table ::-webkit-scrollbar-thumb:hover {
  background: #b3b3b3;
}

.v-data-table ::-webkit-scrollbar {
  height: 10px;
}

</style>
