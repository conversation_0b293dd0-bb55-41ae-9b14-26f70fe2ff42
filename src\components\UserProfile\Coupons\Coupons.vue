<template>
  <div>
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4' : 'mb-4' ]">
      <v-card-title class="pt-6 pl-6" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"
        v-if="!MobileSize">
        {{ $t('CouponProfile.header') }}
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        {{ $t('CouponProfile.header') }}
      </v-card-title>
      <!-- ส่วนคะแนนของฉัน start-->
      <v-container class="pa-2">
      <v-card elevation="0" width="100%" height="100%" :class="IpadSize ? 'px-0 py-0' : MobileSize ? 'ma-1' : '' ">
        <v-card-text>
          <v-row>
            <v-col align="end">
              <span>*{{ $t('CouponProfile.Condition') }}</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-data-table
              :items="dataPrime"
              :headers="headersMain"
              :footer-props="{'items-per-page-text': $t('CouponProfile.subHeaderTable.NumberOfRows')}"
              outline
              >
              <template v-slot:top>
                <v-container style="background-color: #e6f5f3; display: flex; justify-content: center; min-height: 70px; align-items: center;">
                  <span style="font-size: 18px; font-weight: 700;">{{ $t('CouponProfile.headerTable') }}</span>
                </v-container>
                <v-divider class="border-opacity-100"></v-divider>
              </template>
              <template v-slot:[`item.info`]="{ item }">
                <v-btn icon @click="openDialog1(item)">
                  <v-icon>mdi-eye</v-icon>
                </v-btn>
              </template>
              <template v-slot:[`item.total_receive_point`]="{ item }">
                <span>{{ Number(parseInt(item.total_receive_point) - item.total_use_point).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
              </template>
              <template v-slot:[`item.bathPerPoint`]="{ item }">
                {{ parseInt(item.point_total_order)/parseInt(item.point_received) }} {{ $t('CouponProfile.subHeaderTable.Baht') }} {{ $t('CouponProfile.subHeaderTable.Per') }} 1 {{ $t('CouponProfile.subHeaderTable.Point') }}
                <!-- {{ parseInt(item.x_baht) }} บาท ต่อ {{ parseInt(item.x_point) }} คะแนน -->
              </template>
            </v-data-table>
          </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      <v-dialog v-model="diaLog1" width="700px">
        <v-card >
          <v-card-title class="pt-3 pr-3">
            <v-row>
              <v-col style="display: flex; align-items: center;">
                <span style="font-size: 20px; font-weight: 600;">{{ dataDialog1.seller_shop_name }}</span>
              </v-col>
              <v-col align="end">
                <v-btn icon @click="diaLog1 = false"><v-icon>mdi-close</v-icon></v-btn>
              </v-col>
            </v-row>
          </v-card-title>
          <v-data-table
           :items="dataDialog1.order_list"
           :headers="headersDialog1"
           :hide-default-footer="true"
          >
            <template v-slot:[`item.order_no`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <span v-bind="attrs" v-on="on" @click="openDetailOrder(item.order_no)" style="text-decoration: underline; cursor: pointer; color: rgb(39, 171, 156);">
                    {{ item.order_no }}
                  </span>
                </template>
                <span>{{ $t('CouponProfile.subHeaderTable.detail.ViewDetails') }}</span>
              </v-tooltip>
            </template>
            <template v-slot:[`item.order_price`]="{ item }">
              <span>{{ Number(item.order_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`body.append`]>
              <tr class="sticky-table-footer">
                <td align="center"><span>{{ $t('CouponProfile.subHeaderTable.detail.total') }}</span></td>
                <td align="center">{{ Number(dataDialog1.total_order_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</td>
                <td align="center">{{ Number(dataDialog1.total_receive_point).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</td>
                <td align="center">{{ Number(dataDialog1.total_use_point).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</td>
              </tr>
            </template>
            <template v-slot:[`item.use_point`]="{ item }">
              <span>{{ Number(item.use_point).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.receive_point`]="{ item }">
              <span>{{ Number(item.receive_point).toLocaleString(undefined, {maximumFractionDigits: 2}) }}</span>
            </template>
          </v-data-table>
        </v-card>
      </v-dialog>
    </v-container>
      <!-- <v-card-text align="center" justify="center">
        <v-card outlined width="450" height="242" class="pa-auto ma-2 rounded-xl border-scroll">
          <v-card-text>
            <v-row justify="center" align-content="center">
              <v-col cols="12" md="12" class="pt-10">
                <v-row justify="center" align-content="center">
                  <v-img width="86px" height="86px" contain :src="require('@/assets/favourites1.png')" />
                </v-row>
              </v-col>
              <v-col cols="12" md="12" class="pt-12 pb-4">
                <v-row justify="center" align-content="center">
                  <p style="font-size: 28px; font-weight: bold; color: #333333;">{{ Points }}</p>
                </v-row>
              </v-col>
              <p style="color: #636363; font-size: 14px; font-weight: 700;">คะแนนของฉัน</p>
            </v-row>
          </v-card-text>
        </v-card>
      </v-card-text> -->
      <!-- ส่วนคะแนนของฉัน end-->

      <!-- ส่วนนับจำนวนคูปอง start -->
      <!-- <span class="pa-4 pl-8 mt-2" style="font-size:18px ; font-weight: 10;">
        จำนวนคูปองทั้งหมด : {{ CountCoupons }} คูปอง
      </span> -->
      <!-- ส่วนนับจำนวนคูปอง end -->

      <v-col cols="12" class="py-0">
        <a-tabs @change="SelectCouponTabs">
          <a-tab-pane key="ใช้งานได้"><span slot="tab">{{ $t('CouponProfile.Coupon.CanUse') }} <a-tag color="#1AB759" style="border-radius: 8px;">{{CouponActiveData.length}}</a-tag></span></a-tab-pane>
          <a-tab-pane key="ใช้งานไม่ได้/หมดอายุ"><span slot="tab">{{ $t('CouponProfile.Coupon.CannotUse') }} <a-tag color="#E9A016" style="border-radius: 8px;">{{CouponInActiveData.length}}</a-tag></span></a-tab-pane>
        </a-tabs>
      </v-col>
      <!-- {{StateStatus}} -->

      <!-- ในกรณี  มีคูปอง start -->
      <v-container v-if="CouponsIteam.length !== 0">
        <v-card-actions>
          <v-row class="pa-3">
            <v-col v-for="(items, index) in CouponsIteam" :key="index" cols="12" xs="12" sm="12" md="6">
              <CardCoupon v-if="index % 2 === 0" :items="items" :keep="false" colorCard="blue" />
              <CardCoupon v-else :items="items" :keep="false" colorCard="green" />
            </v-col>
          </v-row>
        </v-card-actions>
        <v-container>
          <v-pagination color="#27AB9C" v-model="pageNumber" :length="pageMax" :total-visible="7" @input="pageChange">
          </v-pagination>
        </v-container>
      </v-container>
      <!-- ในกรณี  มีคูปอง end -->

      <!-- ในกรณี ไม่มีคูปอง start -->
      <v-container v-if="CouponData.length !== 0">
      <!-- <pre>
            MobileSize   {{MobileSize}}
            IpadProSize  {{IpadProSize}}
            IpadSize     {{IpadSize}}
      </pre> -->
        <v-row dense v-if="!MobileSize">
          <v-col v-for="(item, index) in CouponData" :key="index" :cols="IpadProSize ? '9' : IpadSize ? '12' : '6'" class="ma-0 pb-4 d-flex justify-center">
            <v-img style="filter: drop-shadow(4px 4px 2px #333333); width: auto; height: auto;" src="@/assets/ConponNGC/shopConpon/NewCoupon.png">
              <v-col cols="12" class="pa-1">
                <v-row no-gutters justify="center" align="center" style="">
                  <v-col :cols="IpadProSize ? '1' : IpadSize ? '' : ''" class="ma-0 pl-4 py-0" align="center" >
                  </v-col>
                  <v-col :cols="IpadProSize ? '6' : IpadSize ? '6' : '6'" :class="IpadProSize ? 'pl-3 pt-3 pb-2'
                  : IpadSize ? 'pl-3 pt-2 pb-2' : 'pl-3 pt-2 pb-2'" :style="IpadProSize ? '' : IpadSize ? 'margin-bottom:0px' : ''" align="start" >
                    <p class="text-truncate ma-0" :style="IpadProSize ? 'color: #27AB9C; font-size: 24px; font-weight: 600;' : IpadSize ? 'color: #27AB9C; font-size: 18px; font-weight: 600;' : 'color: #27AB9C; font-size: 18px; font-weight: 600;'">{{item.coupon_name}}</p>
                    <v-col class="pr-0 pt-4 ml-n3 pb-5" style="min-width: 200px;">
                      <v-row>
                        <v-col v-if="item.coupon_image !== null" style="max-width: 100px;" class="pr-0 py-0">
                          <v-img :src="item.coupon_image" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="80" height="50"></v-img>
                        </v-col>
                        <v-col v-else style="max-width: 100px;" class="pr-0 py-0">
                          <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="80" height="50"></v-img>
                        </v-col>
                        <v-col class="px-0 py-0">
                          <span style="font-size: 10px; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br><span style="color: red;">{{ $t('CouponProfile.Coupon.CouponCard.Minimum') }} {{item.spend_minimum}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span><br>
                          <span v-if="item.discount_maximum !== null">{{ $t('CouponProfile.Coupon.CouponCard.Maximum') }} {{item.discount_maximum}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                        </span>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col v-if="item.use_enddate !== null" :cols="IpadProSize ? 'pl-0 px-0 pb-1 pt-2' : 'pl-0 px-0 pb-1 pt-0'" style="min-width: 200px;">
                      <span style="font-size: 12px;">{{ $t('CouponProfile.Coupon.CouponCard.ValidUntil') }} {{formatDateToShow(item.use_enddate)}}</span>
                    </v-col>
                    <v-col v-else :cols="IpadProSize ? 'px-0 pb-1 pt-2 pl-0' : 'px-0 pb-1 pt-0 pl-0'" style="min-width: 200px;">
                      <span style="font-size: 12px;">{{ $t('CouponProfile.Coupon.CouponCard.NoExpirationDate') }}</span>
                    </v-col>
                    <v-row class="ml-0 py-0 py-1 align-center" style="min-width: 300px;">
                      <v-progress-linear v-if="item.quota !== 0" color="transparent" background-color="#CCCCCC" class="" style="max-width: 120px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(item.use_count) / parseInt(item.quota)) * 100)">
                        <template #progress="{ value }">
                          <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                        </template>
                      </v-progress-linear>
                      <span v-if="item.quota !== 0" class="ml-2" style="font-size: 12px; color: #27AB9C;">{{ $t('CouponProfile.Coupon.CouponCard.Used') }} {{parseInt(parseFloat(parseInt(item.use_count) / parseInt(item.quota)) * 100)}}%</span>
                      <span v-if="item.budget_coupon !== 'N'" class="" style="color: #333333; font-size: 12px; font-weight: 400; max-width: 120px;">{{ $t('CouponProfile.Coupon.CouponCard.Remaining') }} <span style="color: #2d95ff; font-weight: 700;">{{ formatNumber(item.budget_limit - item.budget_used) }}</span> {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                      <v-btn text x-small class="px-0 ml-2" @click="condition(item)"><span class="text-decoration-underline" style="font-size: 12px; color: blue; text-transform: none;">{{ $t('CouponProfile.Coupon.CouponCard.ViewConditions') }}</span></v-btn>
                    </v-row>
                  </v-col>
                  <v-col class="pl-0 mt-1" style="min-width: 200px;">
                    <v-col v-if="item.coupon_type === 'free_product'" class="text-center py-0" style="min-width: 200px;">
                      <span style="font-size: 16px; color: #FFC107;" >{{ $t('CouponProfile.Coupon.CouponCard.Coupons') }}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'free_shipping'" class="text-center py-0" style="min-width: 200px;">
                      <span style="font-size: 16px; color: #FF9800;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'discount'" class="text-center py-0" style="min-width: 200px;">
                      <span style="font-size: 16px; color: red;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'free_product'" class="text-center py-0" style="min-width: 200px;">
                      <span style="font-size: 46px; font-weight: 700; color: #FFC107;">{{ $t('CouponProfile.Coupon.CouponCard.FreeGift') }}</span>
                    </v-col>
                    <v-col  v-if="item.coupon_type === 'free_shipping'" class="text-center py-0" style="min-width: 200px;">
                      <span style="font-size: 46px; font-weight: 700; color: #FF9800;">{{item.discount_type === 'baht' ? `฿${item.discount_amount}` : `${item.discount_amount}%`}}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'discount'" class="text-center py-0" style="min-width: 200px;">
                      <span style="font-size: 46px; font-weight: 700; color: red;">{{item.discount_type === 'baht' ? `฿${item.discount_amount}` : `${item.discount_amount}%`}}</span>
                    </v-col>
                    <v-col class="text-center pt-0" style="min-width: 200px;">
                    </v-col>
                    <v-col class="text-center py-0" style="min-width: 200px;" v-if="item.seller_shop_id !== -1">
                      <span class="d-inline-block text-truncate" style="font-size: 16px; color: #269AFD; max-width: 150px;" >{{item.shop_name}}</span>
                    </v-col>
                  </v-col>
                </v-row>
              </v-col>
            </v-img>
          </v-col>
        </v-row>
        <v-row dense v-if="MobileSize">
          <v-col v-for="(item, index) in CouponData" :key="index" cols="12" class="ma-0 pb-4 d-flex justify-center" >
            <v-img style="filter: drop-shadow(4px 4px 2px #333333); width: auto; height: auto;" src="@/assets/ConponNGC/shopConpon/NewCoupon.png">
              <v-col cols="12" class="pa-1">
                <v-row no-gutters>
                  <v-col cols="1" align="center" >
                  </v-col>
                  <v-col class="ml-2" cols="6" align="start" >
                    <p class="text-truncate ma-0" style="color: #27AB9C; font-size: 12px; font-weight: 700;">{{item.coupon_name}}</p>
                    <v-col class="py-2 pr-0 ml-n3" style="min-width: 200px;">
                      <v-row>
                        <v-col v-if="item.coupon_image !== null" style="max-width: 70px;" class="pr-0 pb-0 pt-1">
                          <v-img :src="item.coupon_image" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="50" height="40"></v-img>
                        </v-col>
                        <v-col v-else style="max-width: 70px;" class="pr-0 pb-0 pt-1">
                          <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="50" height="40"></v-img>
                        </v-col>
                        <v-col class="px-0 py-0">
                          <span style="font-size: 10px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br><span style="color: red;">{{ $t('CouponProfile.Coupon.CouponCard.Minimum') }} {{item.spend_minimum}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span><br>
                          <span v-if="item.discount_maximum !== null">{{ $t('CouponProfile.Coupon.CouponCard.Maximum') }} {{item.discount_maximum}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                        </span>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col v-if="item.use_enddate !== null" class="px-0 py-1" style="min-width: 200px;">
                      <span style="font-size: 10px;">{{ $t('CouponProfile.Coupon.CouponCard.ValidUntil') }} {{formatDateToShow(item.use_enddate)}}</span>
                    </v-col>
                    <v-col v-else class="px-0 py-1" style="min-width: 200px;">
                      <span style="font-size: 10px;">{{ $t('CouponProfile.Coupon.CouponCard.NoExpirationDate') }}</span>
                    </v-col>
                    <v-row class="ml-0 py-0 py-1 align-center" style="min-width: 300px;">
                      <v-progress-linear v-if="item.quota !== 0" color="transparent" background-color="#CCCCCC" class="" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(item.use_count) / parseInt(item.quota)) * 100)">
                        <template #progress="{ value }">
                          <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                        </template>
                      </v-progress-linear>
                      <span v-if="item.quota !== 0" class="ml-2" style="font-size: 12px; color: #27AB9C;">{{ $t('CouponProfile.Coupon.CouponCard.Used') }} {{parseInt(parseFloat(parseInt(item.use_count) / parseInt(item.quota)) * 100)}}%</span>
                      <span v-if="item.budget_coupon !== 'N'" class="" style="color: #333333; font-size: 12px; font-weight: 400;">{{ $t('CouponProfile.Coupon.CouponCard.Remaining') }} <span style="color: #2d95ff; font-weight: 700;">{{ formatNumber(item.budget_limit - item.budget_used) }}</span> {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                      <v-btn text x-small class="px-0 ml-2" @click="condition(item)"><span class="text-decoration-underline" style="font-size: 10px; color: blue; text-transform: none;">{{ $t('CouponProfile.Coupon.CouponCard.ViewConditions') }}</span></v-btn>
                    </v-row>
                  </v-col>
                  <v-col class="pl-0 mt-2">
                    <v-col v-if="item.coupon_type === 'free_product'" class="text-center py-0">
                      <span style="font-size: 10px; color: #FFC107;" >{{ $t('CouponProfile.Coupon.CouponCard.Coupons') }}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'free_shipping'" class="text-center py-0">
                      <span style="font-size: 10px; color: #FF9800;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'discount'" class="text-center py-0">
                      <span style="font-size: 10px; color: red;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'free_product'" class="text-center py-0">
                      <span style="font-size: 28px; font-weight: 700; color: #FFC107;">{{ $t('CouponProfile.Coupon.CouponCard.FreeGift') }}</span>
                    </v-col>
                    <v-col  v-if="item.coupon_type === 'free_shipping'" class="text-center py-0">
                      <span style="font-size: 28px; font-weight: 700; color: #FF9800;">{{item.discount_type === 'baht' ? `฿${item.discount_amount}` : `${item.discount_amount}%`}}</span>
                    </v-col>
                    <v-col v-if="item.coupon_type === 'discount'" class="text-center py-0">
                      <span style="font-size: 28px; font-weight: 700; color: red;">{{item.discount_type === 'baht' ? `฿${item.discount_amount}` : `${item.discount_amount}%`}}</span>
                    </v-col>
                    <v-col class="text-center pt-0">
                    </v-col>
                    <v-col class="text-center py-0" v-if="item.seller_shop_id !== -1">
                      <span class="d-inline-block text-truncate" style="font-size: 10px; color: #269AFD; max-width: 100px;" >{{item.shop_name}}</span>
                    </v-col>
                  </v-col>
                </v-row>
              </v-col>
            </v-img>
          </v-col>
        </v-row>
      </v-container>
      <v-container v-else>
        <v-row justify="center" class="mx-2">
          <v-col cols="12">
            <v-row justify="center" class="my-5">
              <v-img :src="require('@/assets/No-Favorite.png')" max-height="421" max-width="545" height="100%"
                width="100%" contain></v-img>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row justify="center" class="my-5">
              <span
                style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C; font-size: 24px;"><b>{{ $t('CouponProfile.ConditionModal.NoCouponsAvailable') }}{{$i18n.locale === 'th' ? StateStatus : ''}}</b></span>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
      <!-- ในกรณี ไม่มีคูปอง end -->
    </v-card>
    <v-dialog v-model="detailCondition" width="760" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 760px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
                <v-col style="text-align: center;" :class="MobileSize ? 'pt-6 ml-8' : 'pt-6 ml-12'">
                  <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{ $t('Coupon.Conditions') }}</b></span>
                </v-col>
                <v-btn  fab small @click="cancel()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '760px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'py-6 px-2' : 'py-10 px-10'">
                    <v-col class="pb-0">
                      <v-btn small text @click="cancel()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon><span style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('CouponProfile.ConditionModal.GoBack') }}</span></v-btn>
                    </v-col>
                    <v-card-text v-if="!MobileSize" class="pt-0">
                      <div>
                        <v-card-text class="justify-center">
                          <v-img src="@/assets/ConponNGC/shopConpon/Coupons.png" style="filter: drop-shadow(rgb(51, 51, 51) 2px 2px 2px);" width="100%" height="169">
                            <v-row>
                              <v-col class="pr-0">
                                <v-col class="ml-12 pb-0 pr-0" style="min-width: 200px; line-height: 32px;">
                                  <span class="d-inline-block text-truncate" style="color: #27AB9C; font-size: 18px; font-weight: 700; max-width: 280px;">{{CouponName}}</span>
                                </v-col>
                                <v-col class="ml-12 pr-0 pt-0" style="min-width: 200px;">
                                  <v-row>
                                    <v-col v-if="CouponImage !== null" style="max-width: 100px;" class="pr-0">
                                      <v-img :src="CouponImage" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="100" height="50"></v-img>
                                    </v-col>
                                    <v-col v-else style="max-width: 100px;" class="pr-0">
                                      <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="100" height="50"></v-img>
                                    </v-col>
                                    <v-col class="pb-0">
                                      <span v-if="CouponType === 'discount'" style="font-size: 14px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br><span style="color: red;">{{ $t('CouponProfile.Coupon.CouponCard.Minimum') }} {{Number(SpendMinimum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span><br>
                                      <span v-if="DiscountMaximum !== null">{{ $t('CouponProfile.Coupon.CouponCard.Maximum') }} {{Number(DiscountMaximum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                                    </span>
                                      <span v-else-if="CouponType === 'free_shipping'" style="font-size: 14px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br><span style="color: #FF9800;">{{ $t('CouponProfile.Coupon.CouponCard.Minimum') }} {{Number(SpendMinimum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span><br>
                                      <span v-if="DiscountMaximum !== null">{{ $t('CouponProfile.Coupon.CouponCard.Maximum') }} {{Number(DiscountMaximum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                                    </span>
                                      <span v-else style="font-size: 14px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br></span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <v-col v-if="UseEnddate !== null" class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">{{ $t('CouponProfile.Coupon.CouponCard.ValidUntil') }} {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">{{ $t('CouponProfile.Coupon.CouponCard.NoExpirationDate') }}</span>
                                </v-col>
                                <v-row class="ml-14 py-0 py-1 align-center" style="min-width: 300px;">
                                  <v-progress-linear v-if="Quota !== 0" color="transparent" background-color="#CCCCCC" class="" style="max-width: 130px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)">
                                    <template #progress="{ value }">
                                      <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                    </template>
                                  </v-progress-linear>
                                  <span v-if="Quota !== 0" class="ml-2" style="font-size: 12px; color: #27AB9C;">{{ $t('CouponProfile.Coupon.CouponCard.Used') }} {{parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)}}%</span>
                                  <span v-if="BudgetCoupon !== 'N'" class="" style="color: #333333; font-size: 12px; font-weight: 400; max-width: 130px;">{{ $t('CouponProfile.Coupon.CouponCard.Remaining') }} <span style="color: #2d95ff; font-weight: 700;">{{ formatNumber(BudgetLimit - BudgetUsed) }}</span> {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                                  <!-- <v-btn text x-small class="px-0 ml-8" @click="OpenDetail()"><span class="text-decoration-underline" style="font-size: 14px; color: blue;">ดูเงื่อนไข</span></v-btn> -->
                                </v-row>
                              </v-col>
                              <v-col class="pl-0 pt-6" style="min-width: 200px;">
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: red;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: #FF9800;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: #FFC107;" >{{ $t('CouponProfile.Coupon.CouponCard.Coupons') }}</span>
                                </v-col>
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: red;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: #FF9800;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: #FFC107;">{{ $t('CouponProfile.Coupon.CouponCard.FreeGift') }}</span>
                                </v-col>
                                <v-col class="text-center pt-0" style="min-width: 200px;">
                                </v-col>
                                <v-col class="text-center py-0" style="min-width: 200px;" v-if="checkTypeCoupon !== -1">
                                  <span class="d-inline-block text-truncate" style="font-size: 16px; color: #269AFD; max-width: 150px;" >{{ShopName}}</span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-img>
                        </v-card-text>
                      </div>
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.PromotionType') }}
                        </span>
                      </v-col>
                      <v-col v-if="CouponType === 'discount'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ $t('CouponProfile.ConditionModal.PromotionTypeList.ProductPriceDiscount') }}
                        </span>
                      </v-col>
                      <v-col v-else-if="CouponType === 'free_shipping'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ $t('CouponProfile.ConditionModal.PromotionTypeList.ShippingDiscount') }}
                        </span>
                      </v-col>
                      <v-col v-else class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ $t('CouponProfile.ConditionModal.PromotionTypeList.FreeGiftCoupon')}}
                        </span>
                      </v-col>
                      <v-divider v-if="Quota !== 0" class="mt-1"></v-divider>
                      <v-col v-if="Quota !== 0">
                        <span style="font-size: 18px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.CouponEligibilityList.CouponEligibility') }}
                        </span>
                      </v-col>
                      <v-col v-if="Quota !== 0" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ $t('CouponProfile.ConditionModal.CouponEligibilityList.CanBeUsed') }} {{Number(Quota).toLocaleString(undefined)}} {{ $t('CouponProfile.ConditionModal.CouponEligibilityList.Times') }}
                        </span>
                      </v-col>
                      <v-divider v-if="BudgetCoupon !== 'N'" class="mt-1"></v-divider>
                      <v-col v-if="BudgetCoupon !== 'N'">
                        <span style="font-size: 18px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.AvailableCreditLimit') }}
                        </span>
                      </v-col>
                      <v-col v-if="BudgetCoupon !== 'N'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ $t('CouponProfile.Coupon.CouponCard.Remaining') }} {{ formatNumber(BudgetLimit - BudgetUsed) }} {{ $t('CouponProfile.subHeaderTable.Baht') }}
                        </span>
                      </v-col>
                      <v-divider v-if="CouponType !== 'free_product'" class="mt-1"></v-divider>
                      <v-col v-if="CouponType !== 'free_product'">
                        <span style="font-size: 18px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.MaximumDiscount') }}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'percent' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{DiscountMaximum !== null ? `${$t('CouponProfile.Coupon.CouponCard.Maximum')} ${Number(DiscountMaximum).toLocaleString(undefined)} ${$t('CouponProfile.subHeaderTable.Baht')}` : $t('CouponProfile.ConditionModal.Unlimited')}}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'baht' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ `${$t('CouponProfile.Coupon.CouponCard.Maximum')} ${Number(DiscountAmount).toLocaleString(undefined)} ${$t('CouponProfile.subHeaderTable.Baht')}` }}
                        </span>
                      </v-col>
                      <v-divider v-if="checkTypeCoupon === -1" class="mt-1"></v-divider>
                      <v-col v-if="checkTypeCoupon === -1">
                        <span style="font-size: 18px; font-weight: 600;" v-if="shop_name_platform === ''">
                          {{ $t('CouponProfile.ConditionModal.ParticipatingStores') }}
                        </span>
                        <span style="font-size: 18px; font-weight: 600;" v-if="shop_name_platform !== ''">
                          {{ $t('CouponProfile.ConditionModal.ListOfAll') }} <b>{{ shopList.length }}</b> {{ $t('CouponProfile.ConditionModal.Stores') }}
                        </span>
                      </v-col>
                      <v-col v-if="checkTypeCoupon === -1" class="ml-4 pt-0">
                        <span v-if="shop_name_platform === ''">
                          {{ $t('CouponProfile.ConditionModal.ValidAtAllStores') }}
                        </span>
                        <span v-else-if="shop_name_platform !== ''">
                          <div v-for="(shop, index) in displayedShops" :key="index" style="font-size: 14px;">
                            • {{ shop }}
                          </div>
                          <v-btn text small color="primary" @click="toggleShowAll" v-if="shopList.length > limit">
                            {{ showAll ? this.$t('CouponProfile.ConditionModal.ShowLess') : this.$t('CouponProfile.ConditionModal.ShowMore') }}
                          </v-btn>
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="CouponDescription !== null"></v-divider>
                      <v-col v-if="CouponDescription !== null">
                        <span style="font-size: 18px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.DiscountCodeDetails') }}
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="CouponDescription !== null">
                        <span style="font-size: 14px;" v-html="CouponDescription">
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="Object.keys(CouponProductFree).length !== 0"></v-divider>
                      <v-col v-if="Object.keys(CouponProductFree).length !== 0">
                        <span style="font-size: 18px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.FreeGiftDetails') }}
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="Object.keys(CouponProductFree).length !== 0">
                        <v-row class="pa-0" v-for="(buyItem, index) in CouponProductFree.product_details_buy" :key="'pair-' + index">
                          <template v-if="CouponProductFree.product_details_free[index]">
                            <!-- รายการสั่งซื้อ -->
                            <v-col cols="12" class="px-0">
                              <span style="font-weight: 600; font-size: 16px;" class="pl-2">{{ $t('CouponProfile.ConditionModal.OrderList') }}</span>
                              <v-row no-gutters class="pa-0">
                                <v-col cols="4" class="pa-0pl-2" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="buyItem.image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 16px;"><b>{{ $t('CouponProfile.ConditionModal.ProductName') }}: </b>{{ buyItem.name }}</span><br>
                                  <span style="font-size: 16px;"><b>{{ $t('CouponProfile.ConditionModal.Attribute') }}: </b>{{ buyItem.attribute_priority_1 }}{{ buyItem.attribute_priority_2 === '-' ? '' : `, ${buyItem.attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 16px;"><b>{{ $t('CouponProfile.ConditionModal.Amount') }}: </b>{{ buyItem.quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- ของแถม -->
                            <v-col cols="12" class="px-0">
                              <v-col class="text-center rounded-b-0" style="background-color: #E9F6F5; border-top-right-radius: 8px; border-top-left-radius: 8px;">
                                <span style="font-weight: 600; font-size: 16px; color: #27AB9C;" class="">{{ $t('CouponProfile.ConditionModal.FreeGift') }}</span>
                              </v-col>
                              <v-row no-gutters class="pa-0 py-2" style="background-color: #F5F5F5; border: 2px solid #E9F6F5; border-top-width: 0px;">
                                <v-col cols="4" class="pa-0" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="CouponProductFree.product_details_free[index].image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 16px;"><b>{{ $t('CouponProfile.ConditionModal.ProductName') }}: </b>{{ CouponProductFree.product_details_free[index].name }}</span><br>
                                  <span style="font-size: 16px;"><b>{{ $t('CouponProfile.ConditionModal.Attribute') }}: </b>{{ CouponProductFree.product_details_free[index].attribute_priority_1 }}{{ CouponProductFree.product_details_free[index].attribute_priority_2 === '-' ? '' : `, ${CouponProductFree.product_details_free[index].attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 16px;"><b>{{ $t('CouponProfile.ConditionModal.Amount') }}: </b>{{ CouponProductFree.product_details_free[index].quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- Divider -->
                            <v-col cols="12" v-if="index < CouponProductFree.product_details_buy.length - 1" class="py-4">
                              <v-divider></v-divider>
                            </v-col>
                          </template>
                        </v-row>
                      </v-col>
                    </v-card-text>
                    <v-card-text v-if="MobileSize" class="pt-0 px-0">
                      <div>
                        <v-card-text class="justify-center">
                          <v-img src="@/assets/ConponNGC/shopConpon/Coupons.png" style="filter: drop-shadow(rgb(51, 51, 51) 2px 2px 2px);" width="100%" height="150">
                            <v-row>
                              <v-col class="pr-0" style="max-width: 220px;">
                                <v-col class="ml-1 pb-0 pr-0" style="min-width: 150px;">
                                  <span class="d-inline-block text-truncate" style="color: #27AB9C; font-size: 14px; font-weight: 700; max-width: 280px;">{{CouponName}}</span>
                                </v-col>
                                <v-col class="ml-0 py-0 pr-0" style="min-width: 60px;">
                                  <v-row>
                                    <v-col v-if="CouponImage !== null" style="max-width: 60px;" class="pr-0">
                                      <v-img :src="CouponImage" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" width="50" height="50"></v-img>
                                    </v-col>
                                    <v-col v-else style="max-width: 60px;" class="pr-0">
                                      <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" width="50" height="50"></v-img>
                                    </v-col>
                                    <v-col class="pb-0" style="max-width: 150px;">
                                      <span v-if="CouponType === 'discount'" style="font-size: 9px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br><span style="color: red;">{{ $t('CouponProfile.Coupon.CouponCard.Minimum') }} {{Number(SpendMinimum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span><br>
                                      <span v-if="DiscountMaximum !== null">{{ $t('CouponProfile.Coupon.CouponCard.Maximum') }} {{Number(DiscountMaximum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                                    </span>
                                      <span v-else-if="CouponType === 'free_shipping'" style="font-size: 9px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br><span style="color: #FF9800;">{{ $t('CouponProfile.Coupon.CouponCard.Minimum') }} {{Number(SpendMinimum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span><br>
                                      <span v-if="DiscountMaximum !== null">{{ $t('CouponProfile.Coupon.CouponCard.Maximum') }} {{Number(DiscountMaximum).toLocaleString(undefined)}} {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                                    </span>
                                      <span v-else style="font-size: 9px;">{{ $t('CouponProfile.Coupon.CouponCard.CannotUseWith') }}<br></span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <v-col v-if="UseEnddate !== null" class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">{{ $t('CouponProfile.Coupon.CouponCard.ValidUntil') }} {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <v-spacer style="margin-top: 24px;"></v-spacer>
                                </v-col>
                                <v-row class="ml-2 py-0 py-1 align-center" style="min-width: 15p0x;">
                                  <v-progress-linear v-if="Quota !== 0" color="transparent" background-color="#CCCCCC" class="" style="max-width: 65px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)">
                                    <template #progress="{ value }">
                                      <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                    </template>
                                  </v-progress-linear>
                                  <span v-if="Quota !== 0" class="ml-2" style="font-size: 12px; color: #27AB9C;">{{ $t('CouponProfile.Coupon.CouponCard.Used') }} {{parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)}}%</span>
                                  <span v-if="BudgetCoupon !== 'N'" class="" style="color: #333333; font-size: 12px; font-weight: 400; max-width: 65px;">{{ $t('CouponProfile.Coupon.CouponCard.Remaining') }} <span style="color: #2d95ff; font-weight: 700;">{{ formatNumber(BudgetLimit - BudgetUsed) }}</span> {{ $t('CouponProfile.subHeaderTable.Baht') }}</span>
                                  <!-- <v-btn text x-small class="px-0 ml-8" @click="OpenDetail()"><span class="text-decoration-underline" style="font-size: 14px; color: blue;">ดูเงื่อนไข</span></v-btn> -->
                                </v-row>
                              </v-col>
                              <v-col class="pl-0 pt-6 ml-n3" style="min-width: 100px;">
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: red;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: #FF9800;" >{{ $t('CouponProfile.Coupon.CouponCard.Discount') }}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: #FFC107;" >{{ $t('CouponProfile.Coupon.CouponCard.Coupons') }}</span>
                                </v-col>
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 30px; font-weight: 700; color: red;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 30px; font-weight: 700; color: #FF9800;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 100px;">
                                  <span style="font-size: 20px; font-weight: 700; color: #FFC107;">{{ $t('CouponProfile.Coupon.CouponCard.FreeGift') }}</span>
                                </v-col>
                                <v-col class="text-center py-0" v-if="checkTypeCoupon !== -1">
                                  <span class="d-inline-block text-truncate" style="font-size: 14px; color: #269AFD; max-width: 90px;" >{{ShopName}}</span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-img>
                        </v-card-text>
                      </div>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.PromotionType') }}
                        </span>
                      </v-col>
                      <v-col v-if="CouponType === 'discount'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ $t('CouponProfile.ConditionModal.PromotionTypeList.ProductPriceDiscount') }}
                        </span>
                      </v-col>
                      <v-col v-else-if="CouponType === 'free_shipping'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ $t('CouponProfile.ConditionModal.PromotionTypeList.ShippingDiscount') }}
                        </span>
                      </v-col>
                      <v-col v-else class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ $t('CouponProfile.ConditionModal.PromotionTypeList.FreeGiftCoupon')}}
                        </span>
                      </v-col>
                      <v-divider v-if="Quota !== 0" class="mt-1"></v-divider>
                      <v-col v-if="Quota !== 0">
                        <span style="font-size: 16px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.CouponEligibilityList.CouponEligibility') }}
                        </span>
                      </v-col>
                      <v-col v-if="Quota !== 0" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ $t('CouponProfile.ConditionModal.CouponEligibilityList.CanBeUsed') }} {{Number(Quota).toLocaleString(undefined)}} {{ $t('CouponProfile.ConditionModal.CouponEligibilityList.Times') }}
                        </span>
                      </v-col>
                      <v-divider v-if="BudgetCoupon !== 'N'" class="mt-1"></v-divider>
                      <v-col v-if="BudgetCoupon !== 'N'">
                        <span style="font-size: 16px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.AvailableCreditLimit') }}
                        </span>
                      </v-col>
                      <v-col v-if="BudgetCoupon !== 'N'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ $t('CouponProfile.Coupon.CouponCard.Remaining') }} {{ formatNumber(BudgetLimit - BudgetUsed) }} {{ $t('CouponProfile.subHeaderTable.Baht') }}
                        </span>
                      </v-col>
                      <v-divider v-if="CouponType !== 'free_product'" class="mt-1"></v-divider>
                      <v-col v-if="CouponType !== 'free_product'">
                        <span style="font-size: 16px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.MaximumDiscount') }}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'percent' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{DiscountMaximum !== null ? `${$t('CouponProfile.Coupon.CouponCard.Maximum')} ${Number(DiscountMaximum).toLocaleString(undefined)} ${$t('CouponProfile.subHeaderTable.Baht')}` : $t('CouponProfile.ConditionModal.Unlimited')}}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'baht' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ `${$t('CouponProfile.Coupon.CouponCard.Maximum')} ${Number(DiscountAmount).toLocaleString(undefined)} ${$t('CouponProfile.subHeaderTable.Baht')}` }}
                        </span>
                      </v-col>
                      <v-divider v-if="checkTypeCoupon === -1" class="mt-1"></v-divider>
                      <v-col v-if="checkTypeCoupon === -1">
                        <span style="font-size: 18px; font-weight: 600;" v-if="shop_name_platform === ''">
                          {{ $t('CouponProfile.ConditionModal.ParticipatingStores') }}
                        </span>
                        <span style="font-size: 18px; font-weight: 600;" v-if="shop_name_platform !== ''">
                          {{ $t('CouponProfile.ConditionModal.ListOfAll') }} <b>{{ shopList.length }}</b> {{ $t('CouponProfile.ConditionModal.Stores') }}
                        </span>
                      </v-col>
                      <v-col v-if="checkTypeCoupon === -1" class="ml-4 pt-0">
                        <span v-if="shop_name_platform === ''">
                          {{ $t('CouponProfile.ConditionModal.ValidAtAllStores') }}
                        </span>
                        <span v-else-if="shop_name_platform !== ''">
                          <div v-for="(shop, index) in displayedShops" :key="index" style="font-size: 14px;">
                            • {{ shop }}
                          </div>
                          <v-btn text small color="primary" @click="toggleShowAll" v-if="shopList.length > limit">
                            {{ showAll ? this.$t('CouponProfile.ConditionModal.ShowLess') : this.$t('CouponProfile.ConditionModal.ShowMore') }}
                          </v-btn>
                        </span>
                      </v-col>
                      <v-divider v-if="CouponDescription !== null" class="mt-1"></v-divider>
                      <v-col v-if="CouponDescription !== null">
                        <span style="font-size: 16px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.DiscountCodeDetails') }}
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="CouponDescription !== null">
                        <span style="font-size: 12px;" v-html="CouponDescription">
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="Object.keys(CouponProductFree).length !== 0"></v-divider>
                      <v-col v-if="Object.keys(CouponProductFree).length !== 0">
                        <span style="font-size: 16px; font-weight: 600;">
                          {{ $t('CouponProfile.ConditionModal.FreeGiftDetails') }}
                        </span>
                      </v-col>
                      <v-col class="pt-0" v-if="Object.keys(CouponProductFree).length !== 0">
                        <v-row class="pa-0" v-for="(buyItem, index) in CouponProductFree.product_details_buy" :key="'pair-' + index">
                          <template v-if="CouponProductFree.product_details_free[index]">
                            <!-- รายการสั่งซื้อ -->
                            <v-col cols="12" class="px-0">
                              <span style="font-weight: 600; font-size: 14px;" class="pl-2">{{ $t('CouponProfile.ConditionModal.OrderList') }}</span>
                              <v-row no-gutters class="pa-0">
                                <v-col cols="4" class="pa-0"  style="justify-items: center;">
                                  <v-img height="100" width="100" :src="buyItem.image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 14px;"><b>{{ $t('CouponProfile.ConditionModal.ProductName') }}: </b>{{ buyItem.name }}</span><br>
                                  <span style="font-size: 14px;"><b>{{ $t('CouponProfile.ConditionModal.Attribute') }}: </b>{{ buyItem.attribute_priority_1 }}{{ buyItem.attribute_priority_2 === '-' ? '' : `, ${buyItem.attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 14px;"><b>{{ $t('CouponProfile.ConditionModal.Amount') }}: </b>{{ buyItem.quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- ของแถม -->
                            <v-col cols="12" class="px-0">
                              <v-col class="text-center rounded-b-0" style="background-color: #E9F6F5; border-top-right-radius: 8px; border-top-left-radius: 8px;">
                                <span style="font-weight: 600; font-size: 14px; color: #27AB9C;" class="">{{ $t('CouponProfile.ConditionModal.FreeGift') }}</span>
                              </v-col>
                              <v-row no-gutters class="pa-0 py-2" style="background-color: #F5F5F5; border: 2px solid #E9F6F5; border-top-width: 0px;">
                                <v-col cols="4" class="pa-0" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="CouponProductFree.product_details_free[index].image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 14px;"><b>{{ $t('CouponProfile.ConditionModal.ProductName') }}: </b>{{ CouponProductFree.product_details_free[index].name }}</span><br>
                                  <span style="font-size: 14px;"><b>{{ $t('CouponProfile.ConditionModal.Attribute') }}: </b>{{ CouponProductFree.product_details_free[index].attribute_priority_1 }}{{ CouponProductFree.product_details_free[index].attribute_priority_2 === '-' ? '' : `, ${CouponProductFree.product_details_free[index].attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 14px;"><b>{{ $t('CouponProfile.ConditionModal.Amount') }}: </b>{{ CouponProductFree.product_details_free[index].quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- Divider -->
                            <v-col cols="12" v-if="index < CouponProductFree.product_details_buy.length - 1" class="py-4">
                              <v-divider></v-divider>
                            </v-col>
                          </template>
                        </v-row>
                      </v-col>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    CardCoupon: () => import('@/components/CardCoupon/CardCoupon')
  },
  data () {
    return {
      BudgetLimit: '',
      BudgetCoupon: '',
      BudgetUsed: '',
      CouponProductFree: {},
      ShopName: '',
      CouponID: '',
      CollectID: '',
      CouponImage: '',
      CouponName: '',
      CouponCode: '',
      CouponDescription: '',
      CollectStartdate: '',
      CollectEnddate: '',
      UseStartdate: '',
      UseEnddate: '',
      CouponType: '',
      checkTypeCoupon: '',
      shop_name_platform: '',
      Quota: '',
      UseCount: '',
      UserCap: '',
      SpendMinimum: '',
      DiscountAmount: '',
      ProductList: '',
      DiscountType: '',
      SellerShopID: '',
      Status: '',
      detailCondition: false,
      pageMax: 1,
      pageNumber: 1,
      CountCoupons: 0,
      Points: 0,
      response: [],
      valueDeterminate: 20,
      CouponsIteam: [],
      StateStatus: 'ใช้งานได้',
      CouponData: [],
      CouponActiveData: [],
      CouponInActiveData: [],
      typeOfCoupon: '',
      nameOfCoupon: '',
      descriptionOfCoupon: '',
      useCapOfCoupon: 0,
      endDateOfCoupon: '',
      useCountOfCoupon: 0,
      diaLog1: false,
      dataDialog1: [],
      dataTemp: [],
      dataPrime: [],
      dataMock: [
        {
          id: 1,
          shop_name: 'ร้าน A',
          all_point: 100,
          all_price: 5050,
          bathPerPoint: '50',
          all_product: [
            {
              product_name: 'STTS-909 Rising Freedom Gundam',
              product_price: '1050',
              point_get: '20'
            },
            {
              product_name: 'STTS-808 Immortal Justice Gundam',
              product_price: '1050',
              point_get: '20'
            },
            {
              product_name: 'ZGMF/A-42S2 Destiny Gundam Spec II',
              product_price: '950',
              point_get: '20'
            },
            {
              product_name: 'ZGMF-56E2/α Force Impulse Gundam Spec II',
              product_price: '950',
              point_get: '20'
            },
            {
              product_name: 'ORB-01 Akatsuki Gundam',
              product_price: '1050',
              point_get: '20'
            }
          ]
        },
        {
          id: 2,
          shop_name: 'ร้าน B',
          all_point: 50,
          all_price: 2500,
          bathPerPoint: '500',
          all_product: [
            {
              product_name: 'ข้าวสาร 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'ข้าวกล้อง 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'ข้าวเหนียว 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'ข้าวไรซ์เบอร์รี่ 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'สังข์หยดพัทลุง 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            }
          ]
        },
        {
          id: 3,
          shop_name: 'ร้าน C',
          all_point: 20,
          all_price: 400,
          bathPerPoint: '20',
          all_product: [
            {
              product_name: 'พวงกุญแจพี่ปูเป้[Naka is real]',
              product_price: '200',
              point_get: '10'
            },
            {
              product_name: 'พวงกุญแจแหม่มแหม่ม',
              product_price: '200',
              point_get: '10'
            }
          ]
        }
      ],
      headersMain: [
        { text: this.$t('CouponProfile.subHeaderTable.Shop'), value: 'seller_shop_name', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: this.$t('CouponProfile.subHeaderTable.getPoint'), value: 'bathPerPoint', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: this.$t('CouponProfile.subHeaderTable.usePoint'), filterable: false, value: 'total_receive_point', sortable: false, align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: this.$t('CouponProfile.subHeaderTable.detailTable'), filterable: false, sortable: false, value: 'info', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersDialog1: [
        { text: this.$t('CouponProfile.subHeaderTable.detail.OrderList'), value: 'order_no', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: this.$t('CouponProfile.subHeaderTable.detail.ProductPrice'), filterable: false, value: 'order_price', sortable: false, align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: this.$t('CouponProfile.subHeaderTable.detail.PointsEarned'), filterable: false, sortable: false, value: 'receive_point', align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: this.$t('CouponProfile.subHeaderTable.detail.PointsUsed'), filterable: false, sortable: false, value: 'use_point', align: 'center', width: '', class: 'backgroundTable fontTable--text' }
      ],
      showAll: false,
      limit: 10
      // coupon_description
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    shopList () {
      return this.shop_name_platform.split(',').map(name => name.trim())
    },
    displayedShops () {
      return this.showAll ? this.shopList : this.shopList.slice(0, this.limit)
    }
  },
  watch: {
    StateStatus (val) {
      // console.log('StateStatus---->', val)
      this.StateStatus = val
      if (val === 'ใช้งานได้') {
        // this.StateStatus = 'ใช้งานได้'
        this.CouponData = this.CouponActiveData
      } else {
        // this.StateStatus = 'ใช้งานไม่ได้/หมดอายุ'
        this.CouponData = this.CouponInActiveData
      }
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/MyCouponsAndPointsMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/MyCouponsAndPoints' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAccount')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      // this.getDataCoupons()
      this.getCoupon()
      this.getData()
      // this.getPoint()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    formatNumber (value) {
      const num = Number(value)
      return Number.isInteger(num)
        ? num.toLocaleString()
        : num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    },
    toggleShowAll () {
      this.showAll = !this.showAll
    },
    formatDateToShow (data) {
      if (!data) return null
      const date = data.split('T')
      // console.log('date', date)
      const [year, month, day] = date[0].split('-')
      if (this.$i18n.locale === 'th') {
        return `${day}/${month}/${parseInt(year) + 543}`
      } else {
        return `${day}/${month}/${year}`
      }
    },
    openDetailOrder (val) {
      if (this.MobileSize) {
        this.$router.push({ path: `/pobuyerdetailMobile?orderNumber=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/pobuyerdetail?orderNumber=${val}` }).catch(() => {})
      }
    },
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    async getDataCoupons () {
      this.$store.commit('openLoader')
      var DataCoupons = {
        page: this.pageNumber
      }
      await this.$store.dispatch('actionsMyCuoponsAndPoint', DataCoupons)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateGetMyCuoponsAndPoint
      if (response.code === 200) {
        if (response.message === 'Get coupon and point success.') {
          // this.CouponsIteam = response.data.coupon
          this.CouponsIteam = []
          for (let i = 0; i < response.data.coupon.length; i++) {
            this.CouponsIteam.push({
              image: response.data.coupon[i].image,
              name: response.data.coupon[i].name,
              description: response.data.coupon[i].description,
              couponDate: response.data.coupon[i].couponDate,
              couponId: response.data.coupon[i].couponId,
              shop_name: response.data.coupon[i].shop_name
            })
          }
          this.pageMax = parseInt(response.data.total_page)
          this.Points = parseInt(response.data.points.amount) < 0 ? 0 : response.data.points.amount
          this.CountCoupons = response.data.total_coupon
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    async pageChange (page) {
      this.$store.commit('openLoader')
      var DataCoupons = {
        page: page
      }
      await this.$store.dispatch('actionsMyCuoponsAndPoint', DataCoupons)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateGetMyCuoponsAndPoint
      if (response.code === 200) {
        if (response.message === 'Get coupon and point success.') {
          // this.CouponsIteam = response.data.coupon
          this.CouponsIteam = []
          for (let i = 0; i < response.data.coupon.length; i++) {
            this.CouponsIteam.push({
              image: response.data.coupon[i].image,
              name: response.data.coupon[i].name,
              description: response.data.coupon[i].description,
              couponDate: response.data.coupon[i].couponDate,
              couponId: response.data.coupon[i].couponId,
              shop_name: response.data.coupon[i].shop_name
            })
          }
          this.pageMax = parseInt(response.data.total_page)
          this.Points = parseInt(response.data.points.amount) < 0 ? 0 : response.data.points.amount
          this.CountCoupons = response.data.total_coupon
        } else {
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
      window.scrollTo(0, 0)
      this.$store.commit('closeLoader')
    },
    async getCoupon () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role,
        company_id: '-1',
        customer_id: '-1'
      }
      await this.$store.dispatch('actionsGetCoupon', data)
      var response = await this.$store.state.ModuleUser.stateGetCoupon
      if (response.result === 'Success') {
        this.CouponData = response.data
        this.CouponActiveData = this.CouponData.active
        this.CouponInActiveData = this.CouponData.inactive
        if (this.StateStatus === 'ใช้งานได้') {
          this.CouponData = this.CouponActiveData
          // this.CouponData.forEach(element => {
          //   element.use_count = (element.use_count / element.quota) * 100
          // })
        } else if (this.StateStatus === 'ใช้งานไม่ได้/หมดอายุ') {
          this.CouponData = this.CouponInActiveData
          // this.CouponData.forEach(element => {
          //   element.use_count = (element.use_count / element.quota) * 100
          // })
        }
      } else {
        if (response.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
        }
      }
      this.$store.commit('closeLoader')
      // var data = this.CouponActiveData[0].product_list
      // console.log('dataA---->', JSON.stringify(data))
      // console.log('CouponActiveData---->', this.CouponActiveData)
      // console.log('CouponInActiveData---->', this.CouponInActiveData)
    },
    SelectCouponTabs (item) {
      this.StateStatus = item
    },
    formattedDate (val) {
      const date = new Date(val)
      const day = ('0' + date.getDate()).slice(-2)
      const month = ('0' + (date.getMonth() + 1)).slice(-2)
      const year = date.getFullYear() + 543
      return `${day}-${month}-${year}`
    },
    condition (val) {
      this.detailCondition = true
      this.showAll = false
      this.CouponID = val.coupon_id
      this.CollectID = val.collect_id
      this.CouponImage = val.coupon_image
      this.CouponName = val.coupon_name
      this.CouponCode = val.coupon_code
      this.CouponDescription = val.coupon_description
      this.CollectStartdate = val.collect_startdate
      this.CollectEnddate = val.collect_enddate
      this.UseStartdate = val.use_startdate
      this.UseEnddate = val.use_enddate
      this.ShopName = val.shop_name
      this.CouponType = val.coupon_type
      this.checkTypeCoupon = val.seller_shop_id
      if (val.shop_name !== '') {
        this.shop_name_platform = val.shop_name
      } else {
        this.shop_name_platform = ''
      }
      this.Quota = val.quota
      this.BudgetUsed = val.budget_used
      this.BudgetCoupon = val.budget_coupon
      this.BudgetLimit = val.budget_limit
      this.UseCount = val.use_count
      this.UserCap = val.user_cap
      this.SpendMinimum = val.spend_minimum
      this.DiscountAmount = val.discount_amount
      this.DiscountMaximum = val.discount_maximum
      this.CouponProductFree = val.coupon_product_free
      this.ProductList = val.product_list
      this.DiscountType = val.discount_type
      this.SellerShopID = val.seller_shop_id
      this.Status = val.status
      // this.$swal.fire({
      //   title: 'เงื่อนไขการใช้คูปอง',
      //   text: 'เงื่อนไขการใช้คูปอง',
      //   icon: 'info',
      //   confirmButtonText: 'ปิด'
      // })
    },
    cancel () {
      this.detailCondition = false
    },
    async getData () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // await this.$store.dispatch('actionsUserDetailPage', data)
      // const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
      // this.dataTemp = userdetail.data[0]
      // var data1 = {
      //   user_id: this.dataTemp.id
      // }
      //       {
      //     "role_user": "sale_order_no_JV",
      //     "customer_id": 128,
      //     "seller_shop_id": 72,
      //     "company_id": -1,
      //     "com_perm_id": -1
      // }
      var data = {
        role_user: dataRole.role,
        customer_id: -1,
        seller_shop_id: -1,
        company_id: -1,
        com_perm_id: -1
      }
      await this.$store.dispatch('actionsgetListUserPointByUserID', data)
      var res = await this.$store.state.ModuleManagePoint.stategetListUserPointByUserID
      this.$store.commit('closeLoader')
      // console.log(this.dataTemp.id, res)
      this.dataPrime = res.data
    },
    openDialog1 (item) {
      this.dataDialog1 = []
      this.dataDialog1 = item
      this.diaLog1 = true
    }
  }
}
</script>

<style lang="scss" scoped>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.border-scroll {
    border: 1px solid #EBEBEB;
}
</style>
