import AxiosDashboardTransport from './axios_dashboard_transport'

const ModuleDashboardTransport = {
  state: {
    stateSummaryOrder: [],
    stateSummarySuccess: [],
    stateSummaryPending: [],
    stateOverViewOnProcess: [],
    stateOverViewOnProcessTable: [],
    stategetIshiplistAllShop: [],
    stategetAllOutSourceCourier: [],
    stateSentPickupAddress: [],
    stategetAllOutSourceCourierII: [],
    stateUpdateOrderV2: []
  },
  mutations: {
    mutationsSummaryOrder (state, data) {
      state.stateSummaryOrder = data
    },
    mutationsSummarySuccess (state, data) {
      state.stateSummarySuccess = data
    },
    mutationsSummaryPending (state, data) {
      state.stateSummaryPending = data
    },
    mutationsgetOverViewOnProcess (state, data) {
      state.stateOverViewOnProcess = data
    },
    mutationsgetOverViewOnProcessTable (state, data) {
      state.stateOverViewOnProcessTable = data
    },
    mutationsgetIshiplistAllShop (state, data) {
      state.stategetIshiplistAllShop = data
    },
    mutationsgetAllOutSourceCourier (state, data) {
      state.stategetAllOutSourceCourier = data
    },
    mutationsSentPickupAddress (state, data) {
      state.stateSentPickupAddress = data
    },
    mutationsgetAllOutSourceCourierII (state, data) {
      state.stategetAllOutSourceCourierII = data
    },
    mutationsUpdateOrderV2 (state, data) {
      state.stateUpdateOrderV2 = data
    }
  },
  actions: {
    // display summary order
    async actionsSummaryOrder (context, access) {
      const response = await AxiosDashboardTransport.getSummaryOrder(access)
      await context.commit('mutationsSummaryOrder', response)
    },
    async actionsSummarySuccess (context, access) {
      const response = await AxiosDashboardTransport.getSummarySuccess(access)
      await context.commit('mutationsSummarySuccess', response)
    },
    async actionsSummaryPending (context, access) {
      const response = await AxiosDashboardTransport.getSummaryPending(access)
      await context.commit('mutationsSummaryPending', response)
    },
    async actionsgetOverViewOnProcess (context, access) {
      const response = await AxiosDashboardTransport.getOverViewOnProcess(access)
      await context.commit('mutationsgetOverViewOnProcess', response)
    },
    async actionsgetOverViewOnProcessTable (context, access) {
      const response = await AxiosDashboardTransport.getOverViewOnProcessTable(access)
      await context.commit('mutationsgetOverViewOnProcessTable', response)
    },
    async actionsgetIshiplistAllShop (context, access) {
      const response = await AxiosDashboardTransport.getIshiplistAllShop(access)
      await context.commit('mutationsgetIshiplistAllShop', response)
    },
    async actionsgetAllOutSourceCourier (context, access) {
      const response = await AxiosDashboardTransport.getAllOutSourceCourier(access)
      await context.commit('mutationsgetAllOutSourceCourier', response)
    },
    async actionsSentPickupAddress (context, access) {
      const response = await AxiosDashboardTransport.SentPickupAddress(access)
      await context.commit('mutationsSentPickupAddress', response)
    },
    async actionsgetAllOutSourceCourierII (context, access) {
      const response = await AxiosDashboardTransport.getAllOutSourceCourierII(access)
      await context.commit('mutationsgetAllOutSourceCourierII', response)
    },
    async actionsUpdateOrderV2 (context, access) {
      const response = await AxiosDashboardTransport.UpdateOrderV2(access)
      await context.commit('mutationsUpdateOrderV2', response)
    }
  }
}
export default ModuleDashboardTransport
