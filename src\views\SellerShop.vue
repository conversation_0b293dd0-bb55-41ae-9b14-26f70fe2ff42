<template>
  <a-layout id="components-layout-demo-top-side-2">
    <a-layout-header :style="{ position: 'fixed', zIndex: 1, width: '100%', background: '#fff'}" >
      <a-row type="flex">
        <a-col :span='10'>
          <a-row type="flex">
            <v-avatar tile size="35" class="mt-3 ml-3">
              <img :src="require('@/assets/LogoIconWeb.png')"/>
            </v-avatar>
            <a-breadcrumb separator=">" class="ml-3 mt-4" v-if="$router.currentRoute.name === 'seller'">
              <a-breadcrumb-item class="headline"><span style="color:green">ร้านค้า</span>ของฉัน</a-breadcrumb-item>
            </a-breadcrumb>
            <a-breadcrumb separator=">" class="ml-3 mt-4" v-else>
              <a-breadcrumb-item class="headline" v-for="(item, index) in dataBreadcrumb" :key="index" style="cursor:pointer"><span @click="Gopage(item)">{{item.name}}</span></a-breadcrumb-item>
            </a-breadcrumb>
          </a-row>
        </a-col>
        <a-col :span='14'>
          <a-row type="flex" justify="end">
            <!-- <a-popover trigger="hover" placement="bottomRight">
            <template slot="content">
              <v-list dense>
                  <v-list-item v-for="(item, index) in BtnLink" :key="index" @click="Gopage(item)">
                  <a-icon large :type="item.icon" class="pd-link"/><span>{{item.name}}</span>
                  </v-list-item>
                </v-list>
            </template>
            <v-btn class="mt-3 mr-10" color="black"  text>
              <v-avatar size="25">
                <img :src="`${path}${shopSeller.path_logo}?=${currentTime.getTime()}`" />
              </v-avatar>
              <span>{{shopname}}</span>
            </v-btn>
          </a-popover> -->
          </a-row>
        </a-col>
      </a-row>
    </a-layout-header>
      <a-layout-sider width="230" :style="{ overflow: 'auto', height: '90vh', position: 'fixed', left: 0, background: '#fff', marginTop:'70px' }">
        <a-menu mode="inline" :style="{ height: '100%', borderRight: 0 }" :open-keys.sync="openKeys" :default-selected-keys="[defaultSelect]" :selectedKeys="[defaultSelect]">
          <a-sub-menu v-for="item in ListNavbar" :key="item.key">
            <span slot="title"><a-icon :type="item.icon" />{{item.name}}</span>
            <a-menu-item v-for="itemchild in item.child" :key="itemchild.key" @click="Gopage(itemchild)">{{itemchild.name}}</a-menu-item>
          </a-sub-menu>
        </a-menu>
      </a-layout-sider>
        <a-layout-content :style="{  marginTop:'40px',height: '85vh', marginLeft:'235px', marginRight: '5px', position: 'fixed', width:'83.5%'}">
          <v-container grid-list-xs style="background-color:#fff;margin-top:30px">
            <router-view></router-view>
          </v-container>
        </a-layout-content>
    </a-layout>
</template>
<script>
// import { Decode } from '@/services'
export default {
  // components: {
  //   Footer: () => import('@/components/Home/Footer')
  // },
  data () {
    return {
      shopname: '',
      ImgShop: '',
      dataBreadcrumb: [
        {
          name: 'หน้าหลัก',
          path: 'seller'
        },
        {
          name: 'รายการสินค้า',
          path: 'seller'
        }
      ],
      BtnLink: [
        { name: 'รายละเอียดร้านค้า', icon: 'idcard', path: 'shop' },
        { name: 'ออกจากระบบ', icon: 'export', path: '' }
      ],
      defaultSelect: 4,
      openKeys: ['sub2'],
      ListNavbar: [
        {
          key: 'sub2',
          icon: 'shopping',
          name: 'จัดการร้านค้า',
          child: [
            { key: 4, name: 'รายการสินค้า', path: 'seller' },
            { key: 5, name: 'ดูรายการสั่งซื้อ', path: 'poseller' },
            { key: 11, name: 'สต๊อกสินค้า', path: 'inventory' }
          ]
        }
        // {
        //   key: 'sub5',
        //   icon: 'bank',
        //   name: 'ร้านค้า',
        //   child: [
        //     { key: 9, name: 'รายละเอียดร้านค้า', path: 'shop' }
        //   ]
        // }
      ]
    }
  },
  created () {
    // console.log('เข้า create in shop')
    // var dataUser = JSON.parse(Decode.decode(localStorage.getItem('DetailUser')))
    // console.log('dataUser', dataUser)
    this.$EventBus.$emit('getPath')
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$on('changeNav', this.SelectPath)
  },
  methods: {
    async Gopage (val) {
      // console.log('val path====>', val)
      this.dataBreadcrumb.pop()
      this.dataBreadcrumb.push(val)
      this.$router.push(val.path).catch(() => {})
    },
    SelectPath () {
      // console.log('----->.', this.$router.currentRoute.name)
      if (this.$router.currentRoute.name === 'category_seller') {
        this.defaultSelect = 1
      } else if (this.$router.currentRoute.name === 'supplier_seller') {
        this.defaultSelect = 2
      } else if (this.$router.currentRoute.name === 'manufacturer_seller') {
        this.defaultSelect = 3
      } else if (this.$router.currentRoute.name === 'seller') {
        this.defaultSelect = 4
      } else if (this.$router.currentRoute.name === 'poseller') {
        this.defaultSelect = 5
      } else if (this.$router.currentRoute.name === 'promotion_seller') {
        this.defaultSelect = 6
      } else if (this.$router.currentRoute.name === 'partner_seller') {
        this.defaultSelect = 7
      } else if (this.$router.currentRoute.name === 'order_seller') {
        this.defaultSelect = 8
      } else if (this.$router.currentRoute.name === 'shop') {
        this.defaultSelect = 9
      } else if (this.$router.currentRoute.name === 'shopAddress') {
        this.defaultSelect = 10
      } else if (this.$router.currentRoute.name === 'inventory') {
        this.defaultSelect = 11
      }
    }
  }
}
</script>

<style scoped>
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
