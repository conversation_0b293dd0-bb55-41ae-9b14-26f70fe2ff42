<template>
  <div class="ma-5">
    <!-- <v-container> -->
      <v-row justify="center">
        <h1 class="mt-4 ml-4">ผู้ใช้งาน</h1>
        <v-spacer></v-spacer>
        <v-btn dense outlined rounded color="info" class="mt-4">นำเข้าไฟล์ excel</v-btn>
        <v-btn dense rounded class="mt-4 ml-2 mr-2 mb-2" outlined color="info">ดาว์นโหลดแบบฟอร์ม user</v-btn>
        <v-btn dense rounded class="mt-4" color="info" @click="goToCreateUser()"><v-icon>mdi-plus</v-icon> เพิ่มผู้ใช้งาน</v-btn>
      </v-row>
      <v-card
       class="mt-2"
       outlined
       width="100%"
      >
      <UserTable/>
      </v-card>
    <!-- </v-container> -->
  </div>
</template>

<script>
export default {
  components: {
    UserTable: () => import(/* webpackPrefetch: true */ '@/components/User/UserTable.vue')
  },
  methods: {
    goToCreateUser () {
      this.$router.push('/createuser').catch(() => {})
    }
  }
}
</script>
