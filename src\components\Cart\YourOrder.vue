<template>
  <div>
    <v-row dense :class="MobileSize ? 'pb-2' : ''">
      <v-col cols="12" class="overflow-auto">
        <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
          <template v-slot:divider>
            <v-icon>mdi-chevron-right</v-icon>
          </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
              <span :style="{
                  color: item.disabled === true ? '#27AB9C' : '#636363',
                  'font-size': '16px',
                }">{{ item.text
              }}</span>
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
      </v-col>
    </v-row>
    <v-container grid-list-xs :class="MobileSize ? 'pa-0' : ''">
      <!-- หน้าเว็บ, ipad -->
      <v-row class="mt-0" v-if="!MobileSize">
        <v-col v-if="itemsResult.length !== 0" cols="12" >
          <v-row dense>
            <v-col cols="12" >
              <v-card class="pa-3" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
                <v-col cols="12" class="my-2 pt-0">
                  <v-img :src="$t('YourOrderPage.banner')" style="border-radius: 8px;"></v-img>
                  <v-row dense class="px-1">
                    <v-col :cols="IpadSize ? '6' : ''" class="mt-6 pa-6" style="background-color: #F9FAFD; border-radius: 8px; border: 1px solid #F2F2F2;">
                      <div class="d-flex mb-3">
                        <v-img src="@/assets/icon_yourorder.png" max-height="24px" max-width="24px"></v-img>
                        <b style="font-size: 22px; font-weight: 700;" class="ml-2">{{ $t('YourOrderPage.PaymentInformation') }}</b>
                      </div>
                      <v-divider style="border: 1px solid #F2F2F2;"></v-divider>
                      <v-row>
                        <v-col class="mt-5" cols="6">
                          <span style="font-size: 18px;">{{ $t('YourOrderPage.OrderID') }}</span>
                        </v-col>
                        <v-col class="mt-5" cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{itemsResult.data_payment[0].orderId}}</span>
                        </v-col>
                        <v-col cols="6">
                          <span style="font-size: 18px;">{{ $t('YourOrderPage.TotalAmount') }} <v-btn @click="ShowMoreDetail = !ShowMoreDetail" max-height="24px" max-width="24px" elevation="0" fab color="#EBF2F1"><v-icon color="#3EC6B6" size="16">{{ ShowMoreDetail ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon></v-btn></span>
                        </v-col>
                        <v-col cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{ Number(itemsResult.data_payment[0].TxnAmount).toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          }) }} {{ $t('YourOrderPage.Baht') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" class="pl-4">
                          <span style="font-size: 16px;">{{ $t('YourOrderPage.NexgenDiscountCoupon') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" align="right">
                          <span style="font-size: 18px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_platform_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_platform_discount).toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" class="pl-4">
                          <span style="font-size: 16px;">{{ $t('YourOrderPage.SellerDiscount') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" align="right">
                          <span style="font-size: 18px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_discount).toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" class="pl-4">
                          <span style="font-size: 16px;">{{ $t('YourOrderPage.NexgenPoinsDiscount') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" align="right">
                          <span style="font-size: 18px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_point !== null ? Number(itemsResult.data_payment[0].total_point).toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" class="pl-4">
                          <span style="font-size: 16px;">{{ $t('YourOrderPage.SystemCoupon') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" align="right">
                          <span style="font-size: 18px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_platform_shipping_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_platform_shipping_discount).toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" class="pl-4">
                          <span style="font-size: 16px;">{{ $t('YourOrderPage.StoreCoupon') }}</span>
                        </v-col>
                        <v-col cols="6" v-if="ShowMoreDetail" align="right">
                          <span style="font-size: 18px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_shipping_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_shipping_discount).toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                        </v-col>
                        <v-col cols="6">
                          <span style="font-size: 18px;">{{ $t('YourOrderPage.DateTimeofPayment') }}</span>
                        </v-col>
                        <v-col cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{$i18n.locale === 'th' ? new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) : new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('en-GB', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })}}</span>
                        </v-col>
                        <v-col cols="6">
                          <span style="font-size: 18px;">Ref</span>
                        </v-col>
                        <v-col cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{itemsResult.data_payment[0].orderIDRef}}</span>
                        </v-col>
                        <v-col cols="6">
                          <span style="font-size: 18px;">{{ $t('YourOrderPage.PaymentMethod') }}</span>
                        </v-col>
                        <v-col v-if="itemsResult.data_payment[0].payType === 'Credit Card Installment'" cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{itemsResult.data_payment[0].payType + ' ' + '(' + itemsResult.data_payment[0].installment_month + 'x @0%)'}}</span>
                        </v-col>
                        <v-col v-else cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{itemsResult.data_payment[0].payType}}</span>
                        </v-col>
                        <!-- <v-col cols="6">
                          <span style="font-size: 18px;">ธนาคารที่ชำระเงิน</span>
                        </v-col>
                        <v-col cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;">{{bankName}}</span>
                        </v-col> -->
                        <v-col cols="6">
                          <span style="font-size: 18px;">{{ $t('YourOrderPage.PaymentResult') }}</span>
                        </v-col>
                        <v-col cols="6" align="right">
                          <span style="font-size: 18px; font-weight: 700;" v-if="itemsResult.data_payment[0].rmsg === 'Success'">{{ $t('YourOrderPage.PaymentConfirmed') }}</span>
                        </v-col>
                      </v-row>
                    </v-col>
                    <div v-if="!IpadSize" class="mx-1"></div>
                    <v-col class="mt-5 px-0" :cols="IpadSize ? '6' : '3'">
                      <!-- ที่อยู่ในการจัดส่ง + ที่อยู่ใบกำกับภาษี -->
                      <v-card elevation="0" outlined style="border: 1px solid #F2F2F2; border-radius: 8px;">
                        <v-card-title>
                          <v-img src="@/assets/map1.png" max-width="24" max-height="24" contain></v-img>
                          <b class="ml-1" :style="IpadSize ? 'font-size: 18px' : 'font-size: 22px'" style="font-weight: 700; color: #333333;">{{ $t('YourOrderPage.ShippingAddress') }}</b>
                        </v-card-title>
                        <v-card-text>
                          <v-spacer style="border-top: 1px solid #EBEBEB; margin-top: 0px;" class="mb-4"></v-spacer>
                          <v-img class="my-2" :src="$t('YourOrderPage.ShippingNew')" :max-width="$i18n.locale === 'th' ? '128px' : '163px'" max-height="32px"></v-img>
                          <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                            <div>
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                              <v-row>
                                <v-col class="mt-2">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ addressCustomer.address }}</span>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                          <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                            <div>
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                              <v-row>
                                <v-col class="mt-2">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ addressCustomer.address }}</span>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                          <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                            <div>
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                              <v-row>
                                <v-col class="mt-2">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ addressCustomer.address }}</span>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                          <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                            <div>
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                              <v-row>
                                <v-col class="mt-2">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ addressCustomer.address }}</span>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                          <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === true">
                            <div>
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                              <v-row>
                                <v-col class="mt-2">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ addressCustomer.address }}</span>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                          <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === false">
                            <div>
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                              <v-row>
                                <v-col class="mt-2">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ addressCustomer.address }}</span>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                        </v-card-text>
                      </v-card>
                      <!-- <b style="font-size: 24px; font-weight: 700;">สถานะการสั่งซื้อ</b>
                      <v-col class="mt-3 pa-0">
                        <v-img v-show="IpadSize" class="rounded-lg" src="@/assets/successorder.png" height="350" width="auto" contain style="object-fit: cover;"></v-img>
                        <v-img v-show="!IpadSize" class="rounded-lg" src="@/assets/successorder.png" height="350" width="auto" style="object-fit: cover;"></v-img>
                      </v-col> -->
                    </v-col>
                  </v-row>
                  <v-col cols="12" class="py-0 px-2 mt-6">
                    <v-card elevation="0">
                      <div class="d-flex">
                        <v-img src="@/assets/icon_yourorder_1.png" max-height="24px" max-width="24px" class="mr-2"></v-img>
                        <b style="font-size: 22px; font-weight: 700;">{{ $t('YourOrderPage.OrderDetails') }}</b>
                      </div>
                      <!-- สานสัมพันธ์ -->
                      <v-card v-if="inetRelation.length !== 0" class="mt-3 pa-4 " color="#F9FAFD" style="border-radius: 8px;" elevation="0">
                        <v-col>
                          <v-row class="pl-4 pr-3 pb-2 align-baseline">
                            <v-col cols="12" class="pl-0 pt-2 pb-1">
                              <span style="font-size: 24px; font-weight: 700;">{{  $t('YourOrderPage.DiscountCode') }}</span>
                            </v-col>
                            <v-col cols="12" class="pl-0 pt-0">
                              <span style="font-size: 18px; font-weight: 700;">{{ $t('YourOrderPage.ListOf') }} {{inetRelation.length}} {{ $t('YourOrderPage.employees') }}</span>
                            </v-col>
                          </v-row>
                          <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #FFFFFF; border-radius: 20px; max-height: 435px; overflow-y: auto; overflow-x: hidden;">
                            <v-row class="pb-1 pt-2"  v-for="(item, index) in inetRelation" :key="index">
                              <v-col cols="12" class="px-4 py-0">
                                <v-col cols="12" class="pa-0">
                                  <span style="font-size: 16px; font-weight: 400;">{{index + 1}}. {{ $t('YourOrderPage.Department') }}: <b>{{item.team}}</b></span>
                                </v-col>
                                <v-col cols="12" class="pa-0 pl-4">
                                  <span style="font-size: 16px; font-weight: 400;">{{ $t('YourOrderPage.Company') }}: <b>{{item.company}}</b></span>
                                </v-col>
                                <v-col cols="12" class="pa-0 pl-4">
                                  <span style="font-size: 16px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-card>
                        </v-col>
                      </v-card>
                      <!-- ส่วนแสดงใหม่ แบบสินค้าจัดส่ง -->
                      <v-card-text v-if="receivedNull.length !== 0" class="pa-0">
                        <v-row dense>
                          <v-col v-for="(item, index) in receivedNull" :key="index" cols="12" md="12" sm="12">
                            <a-table :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headers" class="setBorderTable">
                              <template slot="title">
                                <v-row class="text-left">
                                  <v-col cols="12" justify="center">
                                    <b class="pb-2" style="font-size: 18px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.Store') }}: {{ item.shop_name_th }}</b>
                                  </v-col>
                                  <v-col cols="12"  v-if="item.invoice_address_json.length !== 0" class="pt-0">
                                    <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                                  </v-col>
                                  <v-col cols="12" justify="center" v-if="item.invoice_address_json.length !== 0" class="pt-0">
                                    <div class="d-flex">
                                      <v-img src="@/assets/yourorder_tax.png" max-width="24" max-height="24" contain></v-img>
                                      <b class="ml-1" :style="IpadSize ? 'font-size: 18px' : 'font-size: 18px'" style="font-weight: 700; color: #333333;">{{ $t('YourOrderPage.InvoiceAddress') }}</b>
                                    </div>
                                    <div class="d-flex pt-2">
                                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ item.invoice_address_json[0].name }} | {{ item.invoice_address_json[0].tax_id }}</span>
                                    </div>
                                    <div class="d-flex pt-2">
                                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ item.invoice_address_json[0].address }} {{ $t('YourOrderPage.SubDistrict') }} {{ item.invoice_address_json[0].sub_district }} {{ $t('YourOrderPage.District') }} {{ item.invoice_address_json[0].district }} {{ $t('YourOrderPage.Province') }} {{ item.invoice_address_json[0].province }} {{ item.invoice_address_json[0].postal_code }}</span>
                                    </div>
                                  </v-col>
                                </v-row>
                                <v-row dense class="pb-1">
                                  <v-col cols="12">
                                    <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                                  </v-col>
                                </v-row>
                              </template>
                              <template slot="productdetails" slot-scope="text, record">
                                <v-row dense>
                                  <v-col cols="12" md="2" sm="3" class="px-0" style="text-align: -webkit-center; align-content: center;">
                                    <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                      :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                      />
                                    <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                      style="border-radius: 4px; padding-right: 8px;" v-else  />
                                  </v-col>
                                  <v-col cols="12" md="10" sm="9" class="">
                                    <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_name }}</p>
                                    <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                      style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                        style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                    </p>
                                    <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                      style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                        style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                    </p>
                                    <v-chip :color="record.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{record.product_type === 'general'? $t('YourOrderPage.GeneralProducts') : $t('YourOrderPage.ServiceProduct')}}</v-chip>
                                  </v-col>
                                </v-row>
                              </template>
                              <template slot="revenue_default" slot-scope="text, record">
                                <v-col cols="12">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                                </v-col>
                              </template>
                              <template slot="quantity" slot-scope="text, record">
                                <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.quantity).toLocaleString() }}</span>
                              </template>
                              <template slot="revenue_vat" slot-scope="text, record">
                                <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number((record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : record.revenue_default) * parseFloat(record.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
                                <!-- <span v-else style="font-size: 14px; font-weight: 600;">{{ Number(record.revenue_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} </span> -->
                              </template>
                            </a-table>
                            <a-table v-if="item.product_free !== null" :data-source="item.product_free"
                            :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                            :columns="headers" :pagination="false">
                            <template slot="title">
                              <v-row class="text-left">
                                <v-col justify="center">
                                  <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30" contain></v-img>
                                  <b class="ml-2"
                                    style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">{{ $t('YourOrderPage.Freegift') }}</b>
                                </v-col>
                              </v-row>
                            </template>
                            <!-- <template slot="sku" slot-scope="text, record">
                              <v-col cols="12" class="pl-0">
                                <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                              </v-col>
                            </template> -->
                            <template slot="productdetails" slot-scope="text, record">
                              <v-row>
                                <v-col cols="12" md="2" sm="3" class="px-0" style="text-align: -webkit-center; align-content: center;">
                                  <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                    :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                    />
                                  <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                    style="border-radius: 4px; padding-right: 8px;" v-else  />
                                </v-col>
                                <v-col cols="12" md="10" sm="9" class="">
                                  <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_name }}</p>
                                  <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                  </p>
                                  <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                  </p>
                                </v-col>
                              </v-row>
                            </template>
                            <template slot="revenue_default">
                              <v-col cols="12">
                                <span style="font-size: 16px; font-weight: 400; color: #333333;">0</span>
                              </v-col>
                            </template>
                            <template slot="quantity" slot-scope="text, record">
                              <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.quantity).toLocaleString() }}</span>
                            </template>
                            <template slot="revenue_vat" slot-scope="text, record">
                              <span v-if="record.vat_default === 'no'" style="font-size: 16px; font-weight: 400; color: #333333;">0
                                </span>
                              <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">
                                0</span>
                            </template>
                          </a-table>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      <!-- ส่วนแสดงใหม่ แบบรับสินค้าหน้าร้าน -->
                      <v-card-text v-if="receivedNotNull.length !== 0" class="pa-0">
                        <v-row dense>
                          <!-- <v-col v-if="receivedNotNull.length !== 0" cols="12">
                            <v-img class="float-left" src="@/assets/pickup.png" width="24" height="24"></v-img>
                            <b class="ml-3" style="line-height: 35px; font-size: 16px; font-weight: 600; color:  #F4BC5F;">ที่อยู่ในการรับสินค้า</b>
                            <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 0px;" class="mb-4"></v-spacer>
                          </v-col> -->
                          <v-row v-for="(item, index) in receivedNotNull" :key="index" dense>
                            <v-col cols="12">
                              <a-table :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headers">
                                <template slot="title">
                                  <v-row class="text-left px-2" dense>
                                    <v-col cols="12" justify="center">
                                      <b style="line-height: 35px; cursor: pointer; font-size: 18px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.Store') }}: {{ item.shop_name_th }}</b>
                                      <v-spacer style="border-top: 1px solid #EBEBEB; margin-top: 0px;"></v-spacer>
                                    </v-col>
                                    <v-col sm="12" md="12">
                                      <v-img class="my-2" :src="$t('YourOrderPage.PickUpNew')" max-width="163px" max-height="32px"></v-img>
                                      <div v-if="item.received_date !== null">
                                        <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ item.shop_name_th }} | {{ item.shop_phone }}</span>
                                        <v-row dense>
                                          <v-col>
                                            <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ item.shop_address }}</span>
                                          </v-col>
                                        </v-row>
                                        <v-row dense>
                                          <v-col >
                                            <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.PickupDate') }} :<span class="float-end" style="font-size: 16px; font-weight: 700; color: #333333;">{{ formatDateToShow(item.received_date) }}</span></span>
                                          </v-col>
                                        </v-row>
                                        <v-row dense>
                                          <v-col>
                                            <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.PickupTime') }} :<span class="float-end" style="font-size: 16px; font-weight: 700; color: #333333;">{{ new Date(item.received_date).toLocaleTimeString('th-TH', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) }} {{ $t('YourOrderPage.Time') }}</span></span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </v-col>
                                    <v-col cols="12"  v-if="item.invoice_address_json.length !== 0" class="pt-2 pb-4">
                                      <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                                    </v-col>
                                    <v-col cols="12" justify="center" v-if="item.invoice_address_json.length !== 0" class="pt-0">
                                      <div class="d-flex">
                                        <v-img src="@/assets/yourorder_tax.png" max-width="24" max-height="24" contain></v-img>
                                        <b class="ml-1" :style="IpadSize ? 'font-size: 18px' : 'font-size: 18px'" style="font-weight: 700; color: #333333;">{{ $t('YourOrderPage.InvoiceAddress') }}</b>
                                      </div>
                                      <div class="d-flex pt-2">
                                        <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ item.invoice_address_json[0].name }} | {{ item.invoice_address_json[0].tax_id }}</span>
                                      </div>
                                      <div class="d-flex pt-2">
                                        <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ item.invoice_address_json[0].address }} {{ $t('YourOrderPage.SubDistrict') }} {{ item.invoice_address_json[0].sub_district }} {{ $t('YourOrderPage.District') }} {{ item.invoice_address_json[0].district }} {{ $t('YourOrderPage.Province') }} {{ item.invoice_address_json[0].province }} {{ item.invoice_address_json[0].postal_code }}</span>
                                      </div>
                                    </v-col>
                                  </v-row>
                                </template>
                                <!-- <template slot="sku" slot-scope="text, record">
                                  <v-col cols="12" class="pl-0">
                                    <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                                  </v-col>
                                </template> -->
                                <template slot="productdetails" slot-scope="text, record">
                                  <v-row>
                                    <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center; align-content: center;">
                                      <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                        :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                        />
                                      <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                        style="border-radius: 4px; padding-right: 8px;" v-else  />
                                    </v-col>
                                    <v-col cols="12" md="8" sm="8" class="">
                                      <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_name }}</p>
                                      <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                        style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                          style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                      </p>
                                      <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                        style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                          style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                      </p>
                                      <v-chip :color="record.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{record.product_type === 'general'? $t('YourOrderPage.GeneralProducts') : $t('YourOrderPage.ServiceProduct')}}</v-chip>
                                    </v-col>
                                  </v-row>
                                </template>
                                <template slot="revenue_default" slot-scope="text, record">
                                  <v-col cols="12">
                                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                                  </v-col>
                                </template>
                                <template slot="quantity" slot-scope="text, record">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.quantity).toLocaleString() }}</span>
                                </template>
                                <template slot="revenue_vat" slot-scope="text, record">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number((record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : record.revenue_default) * parseFloat(record.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
                                </template>
                              </a-table>
                              <a-table v-if="item.product_free !== null" :data-source="item.product_free"
                              :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                              :columns="headers" :pagination="false">
                              <template slot="title">
                                <v-row class="text-left">
                                  <v-col justify="center">
                                    <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30" contain></v-img>
                                    <b class="ml-2"
                                      style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">{{ $t('YourOrderPage.Freegift') }}</b>
                                  </v-col>
                                </v-row>
                              </template>
                              <template slot="sku" slot-scope="text, record">
                                <v-col cols="12" class="pl-0">
                                  <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                                </v-col>
                              </template>
                              <template slot="productdetails" slot-scope="text, record">
                                <v-row>
                                  <v-col cols="12" md="2" sm="3" class="px-0" style="text-align: -webkit-center; align-content: center;">
                                    <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                      :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                      />
                                    <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                      style="border-radius: 4px; padding-right: 8px;" v-else />
                                  </v-col>
                                  <v-col cols="12" md="10" sm="9" class="">
                                    <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_name }}</p>
                                    <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                      style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                        style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                    </p>
                                    <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                      style="font-size: 16px; color: #636363;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                        style="font-size: 16px; font-weight: 400; color: #333333;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                    </p>
                                  </v-col>
                                </v-row>
                              </template>
                              <template slot="revenue_default">
                                <v-col cols="12">
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;">0</span>
                                </v-col>
                              </template>
                              <template slot="quantity" slot-scope="text, record">
                                <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ Number(record.quantity).toLocaleString() }}</span>
                              </template>
                              <template slot="revenue_vat" slot-scope="text, record">
                                <span v-if="record.vat_default === 'no'" style="font-size: 16px; font-weight: 400; color: #333333;">0
                                  </span>
                                <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">
                                  0</span>
                              </template>
                            </a-table>
                            </v-col>
                          </v-row>
                        </v-row>
                      </v-card-text>
                      <!-- <v-container grid-list-xs> -->
                        <!-- <v-row class="ma-0" v-if="receivedNull.length !== 0">
                          <v-card>
                            <v-card-text>
                              <v-row dense>
                                <v-col sm="12" md="12">
                                  <v-img class="float-left" src="@/assets/map1.png" width="24" height="24"></v-img>
                                  <b class="ml-3"
                                    style="line-height: 35px; font-size: 20px; font-weight: 600; color: #333333;">ที่อยู่ในการจัดส่งสินค้า</b>
                                    <v-spacer style="border-top: 1px solid #EBEBEB; margin-top: 0px;" class="mb-4"></v-spacer>
                                    <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                                      <div>
                                        <span style="font-size: 16px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                                      <div>
                                        <span style="font-size: 16px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                                      <div>
                                        <span style="font-size: 16px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                                      <div>
                                        <span style="font-size: 16px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === true">
                                      <div>
                                        <span style="font-size: 16px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === false">
                                      <div>
                                        <span style="font-size: 16px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                </v-col>
                                <v-col v-for="(item, index) in receivedNull" :key="index" cols="12" md="12" sm="12">
                                  <a-table :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headers" class="setBorderTable">
                                    <template slot="title">
                                      <v-row class="text-left">
                                        <v-col justify="center">
                                          <b
                                            style="cursor: pointer; font-size: 18px; font-weight: 600; color: #333333;">ร้านค้า: {{ item.shop_name_th }}</b>
                                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 0px;"></v-spacer>
                                        </v-col>
                                      </v-row>
                                    </template>
                                    <template slot="sku" slot-scope="text, record">
                                      <v-col cols="12" class="pl-0">
                                        <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                                      </v-col>
                                    </template>
                                    <template slot="productdetails" slot-scope="text, record">
                                      <v-row>
                                        <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                                          <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                            />
                                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                            style="border-radius: 4px; padding-right: 8px;" v-else  />
                                        </v-col>
                                        <v-col cols="12" md="8" sm="8" class="">
                                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                          </p>
                                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                          </p>
                                          <v-chip :color="record.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{record.product_type === 'general'? 'สินค้าทั่วไป':'สินค้าบริการ'}}</v-chip>
                                        </v-col>
                                      </v-row>
                                    </template>
                                    <template slot="revenue_default" slot-scope="text, record">
                                      <v-col cols="12">
                                        <span style="font-size: 14px; font-weight: 600;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                                      </v-col>
                                    </template>
                                    <template slot="quantity" slot-scope="text, record">
                                      <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                                    </template>
                                    <template slot="revenue_vat" slot-scope="text, record">
                                      <span style="font-size: 14px; font-weight: 600;">{{ Number((record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : record.revenue_default) * parseFloat(record.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
                                    </template>
                                  </a-table>
                                  <a-table v-if="item.product_free !== null" :data-source="item.product_free"
                                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                                  :columns="headers" :pagination="false">
                                  <template slot="title">
                                    <v-row class="text-left">
                                      <v-col justify="center">
                                        <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30" contain></v-img>
                                        <b class="ml-2"
                                          style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">แถมฟรี</b>
                                      </v-col>
                                    </v-row>
                                  </template>
                                  <template slot="sku" slot-scope="text, record">
                                    <v-col cols="12" class="pl-0">
                                      <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                                    </v-col>
                                  </template>
                                  <template slot="productdetails" slot-scope="text, record">
                                    <v-row>
                                      <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                                        <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                          />
                                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                          style="border-radius: 4px; padding-right: 8px;" v-else  />
                                      </v-col>
                                      <v-col cols="12" md="8" sm="8" class="">
                                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                        </p>
                                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                        </p>
                                      </v-col>
                                    </v-row>
                                  </template>
                                  <template slot="revenue_default">
                                    <v-col cols="12">
                                      <span style="font-size: 14px; font-weight: 600;">0</span>
                                    </v-col>
                                  </template>
                                  <template slot="quantity" slot-scope="text, record">
                                    <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                                  </template>
                                  <template slot="revenue_vat" slot-scope="text, record">
                                    <span v-if="record.vat_default === 'no'" style="font-size: 14px; font-weight: 600;">0
                                      </span>
                                    <span v-else style="font-size: 14px; font-weight: 600;">
                                      0</span>
                                  </template>
                                </a-table>
                              </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-row> -->
                      <!-- <div v-if="receivedNotNull.length !== 0" class="mt-6">
                        <v-card class="pa-4" style="border-radius: 16px;">
                          <v-card-text>
                            <v-col v-if="receivedNotNull.length !== 0" cols="12" class="pa-0">
                              <v-img class="float-left" src="@/assets/pickup.png" width="24" height="24"></v-img>
                              <b class="ml-3"
                                style="line-height: 35px; font-size: 20px; font-weight: 600; color:  #F4BC5F;">ที่อยู่ในการรับสินค้า</b>
                                <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 0px;" class="mb-4"></v-spacer>
                            </v-col>
                            <v-row dense v-for="(item, index) in receivedNotNull" :key="index">
                            <v-col cols="12" md="12" sm="12">
                              <a-table :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headers">
                                <template slot="title">
                                  <v-row class="text-left">
                                    <v-col justify="center">
                                      <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                                      <b class="ml-3"
                                        style="line-height: 35px; cursor: pointer; font-size: 16px; font-weight: 600; color: #F4BC5F;">ร้านค้า: {{ item.shop_name_th }}</b>
                                      <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 0px;"></v-spacer>
                                    </v-col>
                                    <v-col sm="12" md="12">
                                      <div v-if="item.received_date !== null">
                                        <span style="font-size: 16px; font-weight: 600;">{{ item.shop_name_th }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 16px;">{{ item.shop_address }}</span>
                                          </v-col>
                                        </v-row>
                                        <v-row>
                                          <v-col class="mt-2" >
                                            <span style="font-size: 18px;">วันรับสินค้า :<span class="float-end" style="font-size: 18px; font-weight: 700;">{{ formatDateToShow(item.received_date) }}</span></span>
                                          </v-col>
                                        </v-row>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 18px;">เวลารับสินค้า :<span class="float-end" style="font-size: 18px; font-weight: 700;">{{ new Date(item.received_date).toLocaleTimeString('th-TH', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) }} น.</span></span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                  </v-col>
                                  </v-row>
                                </template>
                                <template slot="sku" slot-scope="text, record">
                                  <v-col cols="12" class="pl-0">
                                    <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                                  </v-col>
                                </template>
                                <template slot="productdetails" slot-scope="text, record">
                                  <v-row>
                                    <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                                      <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                        :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                        />
                                      <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                        style="border-radius: 4px; padding-right: 8px;" v-else  />
                                    </v-col>
                                    <v-col cols="12" md="8" sm="8" class="">
                                      <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                      <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                        style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                          style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                      </p>
                                      <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                        style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                          style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                      </p>
                                    </v-col>
                                  </v-row>
                                </template>
                                <template slot="revenue_default" slot-scope="text, record">
                                  <v-col cols="12">
                                    <span style="font-size: 14px; font-weight: 600;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include):record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                                  </v-col>
                                </template>
                                <template slot="quantity" slot-scope="text, record">
                                  <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                                </template>
                                <template slot="revenue_vat" slot-scope="text, record">
                                  <span style="font-size: 14px; font-weight: 600;">{{ Number((record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : record.revenue_default) * parseFloat(record.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
                                </template>
                              </a-table>
                              <a-table v-if="item.product_free !== null" :data-source="item.product_free"
                              :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                              :columns="headers" :pagination="false">
                              <template slot="title">
                                <v-row class="text-left">
                                  <v-col justify="center">
                                    <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30" contain></v-img>
                                    <b class="ml-2"
                                      style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">แถมฟรี</b>
                                  </v-col>
                                </v-row>
                              </template>
                              <template slot="sku" slot-scope="text, record">
                                <v-col cols="12" class="pl-0">
                                  <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                                </v-col>
                              </template>
                              <template slot="productdetails" slot-scope="text, record">
                                <v-row>
                                  <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                                    <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                                      :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                      />
                                    <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                      style="border-radius: 4px; padding-right: 8px;" v-else />
                                  </v-col>
                                  <v-col cols="12" md="8" sm="8" class="">
                                    <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                    <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                      style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                        style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                    </p>
                                    <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                      style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                        style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                    </p>
                                  </v-col>
                                </v-row>
                              </template>
                              <template slot="revenue_default">
                                <v-col cols="12">
                                  <span style="font-size: 14px; font-weight: 600;">0</span>
                                </v-col>
                              </template>
                              <template slot="quantity" slot-scope="text, record">
                                <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                              </template>
                              <template slot="revenue_vat" slot-scope="text, record">
                                <span v-if="record.vat_default === 'no'" style="font-size: 14px; font-weight: 600;">0
                                  </span>
                                <span v-else style="font-size: 14px; font-weight: 600;">
                                  0</span>
                              </template>
                            </a-table>
                            </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </div>
                    </v-container> -->
                  </v-card>
                  <!-- <v-card v-if="inetRelation.length !== 0" class="mt-6 pa-4 " color="#F9FAFD" style="border-radius: 8px;" elevation="0">
                    <v-col>
                      <v-row class="pl-4 pr-3 pb-2 align-baseline">
                        <v-col cols="12" class="pl-0 pt-2 pb-1">
                          <span style="font-size: 24px; font-weight: 700;">โค้ดส่วนลด</span>
                        </v-col>
                        <v-col cols="12" class="pl-0 pt-0">
                          <span style="font-size: 18px; font-weight: 700;">รายชื่อพนักงาน {{inetRelation.length}} รายชื่อ</span>
                        </v-col>
                      </v-row>
                      <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #FFFFFF; border-radius: 20px; max-height: 435px; overflow-y: auto; overflow-x: hidden;">
                        <v-row class="pb-1 pt-2"  v-for="(item, index) in inetRelation" :key="index">
                          <v-col cols="12" class="px-4 py-0">
                            <v-col cols="12" class="pa-0">
                              <span style="font-size: 16px; font-weight: 400;">{{index + 1}}. แผนก: <b>{{item.team}}</b></span>
                            </v-col>
                            <v-col cols="12" class="pa-0 pl-4">
                              <span style="font-size: 16px; font-weight: 400;">บริษัท: <b>{{item.company}}</b></span>
                            </v-col>
                            <v-col cols="12" class="pa-0 pl-4">
                              <span style="font-size: 16px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                            </v-col>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-card> -->
                  </v-col>
                  <v-row justify="center" no-gutters class="mt-8" style="margin-bottom: -10px">
                    <v-btn v-if="golocalmail === false" width="250"  outlined rounded color="#27AB9C" class="mr-8" style="font-size: medium; font-weight: 600;" @click="goPoBuyerProfilePage()">{{ $t('YourOrderPage.GotoOrderList') }}</v-btn>
                    <v-btn width="250" rounded color="#27AB9C" class="white--text" style="font-size: medium; font-weight: 600;" @click="goHomePage()">{{ $t('YourOrderPage.GotoHomepage') }}</v-btn>
                  </v-row>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <!-- mobile -->
      <v-row v-if="MobileSize" :class="MobileSize ? 'pa-0' : ''">
        <v-col v-if="itemsResult.length !== 0" cols="12" :class="MobileSize ? 'pa-0' : ''">
          <!-- Ui ใหม่ -->
          <v-row dense>
            <v-col cols="12">
              <!-- ส่วนแสดง banner -->
              <v-card elevation="0" width="100%" height="134px" style="box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14; border-bottom: 1px solid #F2F2F2;">
                <v-card-text style="display: flex; justify-content: center;">
                  <v-img :src="$t('YourOrderPage.bannerMobile')" max-width="382px" max-height="102px"></v-img>
                </v-card-text>
              </v-card>
              <!-- ส่วนแสดงที่อยู่จัดส่ง -->
              <v-card v-if="receivedNull.length !== 0" elevation="0" width="100%" class="mt-3" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                <v-card-text style="display: flex; justify-content: center;" class="py-0">
                  <v-row dense>
                    <v-col cols="12" style="padding: 24px;">
                      <v-img class="float-left" src="@/assets/map1.png" width="22" height="22"></v-img>
                      <b class="ml-3" style="font-size: 16px; font-weight: 700; color: #333333;">{{ $t('YourOrderPage.ShippingAddress') }}</b>
                      <v-spacer style="border: 1px solid #F2F2F2;" class="my-4"></v-spacer>
                      <v-img class="my-2" :src="$t('YourOrderPage.ShippingNew')" :max-width="$i18n.locale === 'th' ? '128px' : '163px'" max-height="32px"></v-img>
                      <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                        <div>
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                          <v-row dense>
                            <v-col class="mt-2">
                              <span style="font-size: 16px; color: #333333;">{{ addressCustomer.address }}</span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                      <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                        <div>
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                          <v-row dense>
                            <v-col class="mt-2">
                              <span style="font-size: 16px; color: #333333;">{{ addressCustomer.address }}</span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                      <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                        <div>
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                          <v-row dense>
                            <v-col class="mt-2">
                              <span style="font-size: 16px; color: #333333;">{{ addressCustomer.address }}</span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                      <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                        <div>
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                          <v-row dense>
                            <v-col class="mt-2">
                              <span style="font-size: 16px; color: #333333;">{{ addressCustomer.address }}</span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                      <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === false">
                        <div>
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                          <v-row dense>
                            <v-col class="mt-2">
                              <span style="font-size: 16px; color: #333333;">{{ addressCustomer.address }}</span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                      <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === true">
                        <div>
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ addressCustomer.name }}</span>
                          <v-row dense>
                            <v-col class="mt-2">
                              <span style="font-size: 16px; color: #333333;">{{ addressCustomer.address }}</span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- การชำระเงิน -->
              <v-card elevation="0" width="100%" class="mt-3" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                <v-card-text style="display: flex; justify-content: center;" class="py-0">
                  <v-row dense style="padding: 24px;">
                    <v-col cols="12">
                      <v-img class="float-left" src="@/assets/icon_yourorder.png" width="22" height="22"></v-img>
                      <b class="ml-3" style="font-size: 16px; font-weight: 700; color: #333333;">{{ $t('YourOrderPage.PaymentInformation') }}</b>
                    </v-col>
                    <!-- รหัสการสั่งซื้อ -->
                    <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">{{ $t('YourOrderPage.OrderID') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{itemsResult.data_payment[0].orderId}}</span>
                      </div>
                    </v-col>
                    <!-- ยอดชำระเงิน -->
                    <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">{{ $t('YourOrderPage.TotalAmount') }} <v-btn @click="ShowMoreDetail = !ShowMoreDetail" max-height="24px" max-width="24px" elevation="0" fab color="#EBF2F1"><v-icon color="#3EC6B6" size="16">{{ ShowMoreDetail ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon></v-btn></span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(itemsResult.data_payment[0].TxnAmount).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) }} {{ $t('YourOrderPage.Baht') }}</span>
                      </div>
                    </v-col>
                    <!-- ส่วนลดคูปองระบบ -->
                    <v-col cols="12" class="mt-2 pl-2" v-if="ShowMoreDetail">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #464646;">{{ $t('YourOrderPage.NexgenDiscountCoupon') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_platform_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_platform_discount).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                      </div>
                    </v-col>
                    <!-- ส่วนลดร้านค้าทั้งหมด -->
                    <v-col cols="12" class="mt-2 pl-2" v-if="ShowMoreDetail">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #464646;">{{ $t('YourOrderPage.SellerDiscount') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_discount).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                      </div>
                    </v-col>
                    <!-- ส่วนลดจากการใช้แต้ม -->
                    <v-col cols="12" class="mt-2 pl-2" v-if="ShowMoreDetail">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #464646;">{{ $t('YourOrderPage.NexgenPoinsDiscount') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_point !== null ? Number(itemsResult.data_payment[0].total_point).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                      </div>
                    </v-col>
                    <!-- คูปองระบบ (ส่งฟรี) -->
                    <v-col cols="12" class="mt-2 pl-2" v-if="ShowMoreDetail">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #464646;">{{ $t('YourOrderPage.SystemCoupon') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_platform_shipping_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_platform_shipping_discount).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                      </div>
                    </v-col>
                    <!-- คูปองร้านค้า (ส่งฟรี) -->
                    <v-col cols="12" class="mt-2 pl-2" v-if="ShowMoreDetail">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #464646;">{{ $t('YourOrderPage.StoreCoupon') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 500; color: #D1392B;">-{{ itemsResult.data_payment[0].total_coupon_shipping_discount !== null ? Number(itemsResult.data_payment[0].total_coupon_shipping_discount).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) : '0.00' }} {{ $t('YourOrderPage.Baht') }}</span>
                      </div>
                    </v-col>
                    <!-- วันและเวลาที่ชำระเงิน -->
                    <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">{{ $t('YourOrderPage.DateTimeofPayment') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ $i18n.locale === 'th' ? new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) : new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('en-GB', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })}}</span>
                      </div>
                    </v-col>
                    <!-- Ref -->
                    <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">Ref</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{itemsResult.data_payment[0].orderIDRef}}</span>
                      </div>
                    </v-col>
                    <!-- รูปแบบการชำระเงิน -->
                    <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">{{ $t('YourOrderPage.PaymentMethod') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;" v-if="itemsResult.data_payment[0].payType === 'Credit Card Installment'">{{ itemsResult.data_payment[0].payType + ' ' + '(' + itemsResult.data_payment[0].installment_month + 'x @0%)' }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;" v-else>{{ itemsResult.data_payment[0].payType }}</span>
                      </div>
                    </v-col>
                    <!-- ธนาคารที่ชำระเงิน -->
                    <!-- <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">ธนาคารที่ชำระเงิน</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{bankName}}</span>
                      </div>
                    </v-col> -->
                    <!-- ผลการชำระเงิน -->
                    <v-col cols="12" class="mt-2">
                      <div class="d-flex">
                        <span class="mr-auto" style="font-size: 16px; color: #333333;">{{ $t('YourOrderPage.PaymentResult') }}</span>
                        <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;" v-if="itemsResult.data_payment[0].rmsg === 'Success'">{{ $t('YourOrderPage.PaymentConfirmed') }}</span>
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- สานสัมพันธ์ -->
              <v-card v-if="inetRelation.length !== 0" elevation="0" width="100%" class="mt-3" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                <v-card-text style="display: flex; justify-content: center;" class="py-0">
                  <v-row dense style="padding: 24px;">
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 700; color: #333333;">{{  $t('YourOrderPage.DiscountCode') }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 14px; font-weight: 700; color: #333333;">{{ $t('YourOrderPage.ListOf') }} {{inetRelation.length}} {{ $t('YourOrderPage.employees') }}</span>
                    </v-col>
                    <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #F9FAFD; border-radius: 20px; max-height: 435px; overflow-y: auto; overflow-x: hidden; width: 100%;">
                      <v-row class="pb-1 pt-2"  v-for="(item, index) in inetRelation" :key="index">
                        <v-col cols="12" class="px-4 py-0">
                          <v-col cols="12" class="pa-0">
                            <span style="font-size: 14px; font-weight: 400;">{{index + 1}}. {{ $t('YourOrderPage.Department') }}: <b>{{item.team}}</b></span>
                          </v-col>
                          <v-col cols="12" class="pa-0 pl-4">
                            <span style="font-size: 14px; font-weight: 400;">{{ $t('YourOrderPage.Company') }}: <b>{{item.company}}</b></span>
                          </v-col>
                          <v-col cols="12" class="pa-0 pl-4">
                            <span style="font-size: 14px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                          </v-col>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- ส่วนแสดงรายการสินค้า -->
              <v-card elevation="0" width="100%" class="mt-3" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                <v-card-text style="display: flex; justify-content: center;" class="py-0">
                  <v-row dense style="padding: 24px;">
                    <v-col cols="12">
                      <v-img class="float-left" src="@/assets/icon_yourorder_1.png" width="22" height="22"></v-img>
                      <b class="ml-3" style="font-size: 16px; font-weight: 700; color: #333333;">{{ $t('YourOrderPage.OrderDetails') }}</b>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- ส่วนแสดงสินค้า แบบจัดส่ง -->
              <div v-if="receivedNull.length !== 0">
                <v-col cols="12" v-for="(item, index) in receivedNull" :key="index" :class="index === 0 ? 'py-0' : 'mt-1'">
                  <v-card elevation="0" width="100%" style="border-width: 0px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                    <v-card-text class="pa-0">
                      <div style="height: 56px; padding: 16px; display: flex; align-items: center; ">
                        <b style="cursor: pointer; font-size: 16px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.Store') }}: {{ item.shop_name_th }}</b>
                      </div>
                      <v-col cols="12" justify="center" v-if="item.invoice_address_json.length !== 0" class="pt-0" style="padding: 16px;">
                        <div class="d-flex">
                          <v-img src="@/assets/yourorder_tax.png" max-width="24" max-height="24" contain></v-img>
                          <b class="ml-1" :style="IpadSize ? 'font-size: 18px' : 'font-size: 18px'" style="font-weight: 700; color: #333333;">{{ $t('YourOrderPage.InvoiceAddress') }}</b>
                        </div>
                        <div class="d-flex pt-2">
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ item.invoice_address_json[0].name }} | {{ item.invoice_address_json[0].tax_id }}</span>
                        </div>
                        <div class="d-flex pt-2">
                          <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ item.invoice_address_json[0].address }} {{ $t('YourOrderPage.SubDistrict') }} {{ item.invoice_address_json[0].sub_district }} {{ $t('YourOrderPage.District') }} {{ item.invoice_address_json[0].district }} {{ $t('YourOrderPage.Province') }} {{ item.invoice_address_json[0].province }} {{ item.invoice_address_json[0].postal_code }}</span>
                        </div>
                      </v-col>
                      <v-row dense class="pa-0">
                        <v-col cols="12" v-for="(items, index2) in item.product_list" :key="index2" class="py-0">
                          <v-card elevation="0" width="100%" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                            <v-card-text>
                              <v-row dense>
                                <v-col cols="2" style="display: flex; justify-content: center;">
                                  <v-img :src="`${items.product_image}`" contain style="border-radius: 4px; padding-right: 8px;" v-if="items.product_image !== ''"/>
                                  <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                    style="border-radius: 4px; padding-right: 8px;" v-else  />
                                </v-col>
                                <v-col cols="10">
                                  <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_name }}</p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_1 !== '' && items.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_1 }}</span>
                                  </p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_2 !== '' && items.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_2 }}</span>
                                  </p>
                                  <v-chip :color="items.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{ items.product_type === 'general'? $t('YourOrderPage.GeneralProducts') : $t('YourOrderPage.ServiceProduct') }}</v-chip>
                                  <div class="d-flex pt-3">
                                    <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #464646;">ราคารวม : {{ Number((items.vat_default === 'yes' ? parseFloat(items.revenue_default) + parseFloat(items.vat_include) : items.revenue_default) * parseFloat(items.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                                    <span class="ml-auto" style="font-size: 16px; font-weight: 400; color: #464646;">จำนวน : {{ Number(items.quantity).toLocaleString() }}</span>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                      <!-- สินค้าแถมฟรี -->
                      <v-row dense class="pa-0" v-if="item.product_free !== null">
                        <v-col cols="12">
                          <div style="height: 56px; padding: 16px; display: flex; align-items: center; ">
                            <v-img src="@/assets/icons/nullproduct.png" max-width="22" max-height="22" contain></v-img>
                            <b class="ml-2" style="line-height: 35px; font-size: 16px; font-weight: 600; color: #F4BC5F;">{{ $t('YourOrderPage.Freegift') }}</b>
                          </div>
                        </v-col>
                        <v-col cols="12" v-for="(items, index2) in item.product_free" :key="index2" class="py-0">
                          <v-card elevation="0" width="100%" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                            <v-card-text>
                              <v-row dense>
                                <v-col cols="2" style="display: flex; justify-content: center;">
                                  <v-img :src="`${items.product_image}`" contain style="border-radius: 4px; padding-right: 8px;" v-if="items.product_image !== ''"/>
                                  <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                    style="border-radius: 4px; padding-right: 8px;" v-else  />
                                </v-col>
                                <v-col cols="10">
                                  <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_name }}</p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_1 !== '' && items.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_1 }}</span>
                                  </p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_2 !== '' && items.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_2 }}</span>
                                  </p>
                                  <v-chip :color="items.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{ items.product_type === 'general'? $t('YourOrderPage.GeneralProducts') : $t('YourOrderPage.ServiceProduct') }}</v-chip>
                                  <div class="d-flex pt-3">
                                    <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #464646;">ราคารวม : 0.00</span>
                                    <span class="ml-auto" style="font-size: 16px; font-weight: 400; color: #464646;">จำนวน : {{ Number(items.quantity).toLocaleString() }}</span>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </div>
              <!-- ส่วนแสดงสินค้า แบบรับหน้าร้าน -->
              <div v-if="receivedNotNull.length !== 0">
                <v-col cols="12" v-for="(item, index) in receivedNotNull" :key="index" :class="index === 0 && receivedNull.length !== 0 ? 'mt-1' : index === 0 && receivedNull.length !== 0 ? 'py-0' : 'mt-1'">
                  <v-card elevation="0" width="100%" style="border-width: 0px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                    <v-card-text class="pa-0">
                      <div style="padding: 16px;">
                        <b style="cursor: pointer; font-size: 16px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.Store') }}: {{ item.shop_name_th }}</b>
                        <v-img class="my-2" :src="$t('YourOrderPage.PickUpNew')" max-width="163px" max-height="32px"></v-img>
                        <div v-if="item.received_date !== null">
                          <v-row dense>
                            <v-col cols="12">
                              <span style="font-size: 16px; font-weight: 700; color: #333333;">{{ item.shop_name_th }} | {{ item.shop_phone }}</span>
                            </v-col>
                          </v-row>
                          <v-row dense>
                            <v-col>
                              <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ item.shop_address }}</span>
                            </v-col>
                          </v-row>
                          <v-row dense>
                            <v-col cols="12" >
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.PickupDate') }} :<span class="float-end" style="font-size: 16px; font-weight: 700; color: #333333;">{{ formatDateToShow(item.received_date) }}</span></span>
                            </v-col>
                          </v-row>
                          <v-row dense>
                            <v-col cols="12">
                              <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('YourOrderPage.PickupTime') }} :<span class="float-end" style="font-size: 16px; font-weight: 700; color: #333333;">{{ new Date(item.received_date).toLocaleTimeString('th-TH', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) }} {{ $t('YourOrderPage.Time') }}</span></span>
                            </v-col>
                          </v-row>
                        </div>
                      </div>
                      <v-col cols="12" v-if="item.invoice_address_json.length !== 0" class="pt-0 pb-4">
                        <v-spacer style="border-top: 1px solid #F2F2F2;"></v-spacer>
                      </v-col>
                      <v-col cols="12" justify="center" v-if="item.invoice_address_json.length !== 0" class="pt-0" style="padding: 16px;">
                        <div class="d-flex">
                          <v-img src="@/assets/yourorder_tax.png" max-width="24" max-height="24" contain></v-img>
                          <b class="ml-1" :style="IpadSize ? 'font-size: 18px' : 'font-size: 18px'" style="font-weight: 700; color: #333333;">{{ $t('YourOrderPage.InvoiceAddress') }}</b>
                        </div>
                        <div class="d-flex pt-2">
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ item.invoice_address_json[0].name }} | {{ item.invoice_address_json[0].tax_id }}</span>
                        </div>
                        <div class="d-flex pt-2">
                          <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ item.invoice_address_json[0].address }} {{ $t('YourOrderPage.SubDistrict') }} {{ item.invoice_address_json[0].sub_district }} {{ $t('YourOrderPage.District') }} {{ item.invoice_address_json[0].district }} {{ $t('YourOrderPage.Province') }} {{ item.invoice_address_json[0].province }} {{ item.invoice_address_json[0].postal_code }}</span>
                        </div>
                      </v-col>
                      <v-row dense class="pa-0">
                        <v-col cols="12" v-for="(items, index2) in item.product_list" :key="index2" class="py-0">
                          <v-card elevation="0" width="100%" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                            <v-card-text>
                              <v-row dense>
                                <v-col cols="2" style="display: flex; justify-content: center;">
                                  <v-img :src="`${items.product_image}`" contain style="border-radius: 4px; padding-right: 8px;" v-if="items.product_image !== ''"/>
                                  <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                    style="border-radius: 4px; padding-right: 8px;" v-else  />
                                </v-col>
                                <v-col cols="10">
                                  <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_name }}</p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_1 !== '' && items.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_1 }}</span>
                                  </p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_2 !== '' && items.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_2 }}</span>
                                  </p>
                                  <v-chip :color="items.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{ items.product_type === 'general'? $t('YourOrderPage.GeneralProducts') : $t('YourOrderPage.ServiceProduct') }}</v-chip>
                                  <div class="d-flex pt-3">
                                    <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #464646;">ราคารวม : {{ Number((items.vat_default === 'yes' ? parseFloat(items.revenue_default) + parseFloat(items.vat_include) : items.revenue_default) * parseFloat(items.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                                    <span class="ml-auto" style="font-size: 16px; font-weight: 400; color: #464646;">จำนวน : {{ Number(items.quantity).toLocaleString() }}</span>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                      <!-- สินค้าแถมฟรี -->
                      <v-row dense class="pa-0" v-if="item.product_free !== null">
                        <v-col cols="12">
                          <div style="height: 56px; padding: 16px; display: flex; align-items: center; ">
                            <v-img src="@/assets/icons/nullproduct.png" max-width="22" max-height="22" contain></v-img>
                            <b class="ml-2" style="line-height: 35px; font-size: 16px; font-weight: 600; color: #F4BC5F;">{{ $t('YourOrderPage.Freegift') }}</b>
                          </div>
                        </v-col>
                        <v-col cols="12" v-for="(items, index2) in item.product_free" :key="index2" class="py-0">
                          <v-card elevation="0" width="100%" style="border-width: 1px, 0px, 1px, 0px; border-style: solid; border-color: #F2F2F2;">
                            <v-card-text>
                              <v-row dense>
                                <v-col cols="2" style="display: flex; justify-content: center;">
                                  <v-img :src="`${items.product_image}`" contain style="border-radius: 4px; padding-right: 8px;" v-if="items.product_image !== ''"/>
                                  <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                                    style="border-radius: 4px; padding-right: 8px;" v-else  />
                                </v-col>
                                <v-col cols="10">
                                  <p class="mb-0" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_name }}</p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_1 !== '' && items.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_1 }}</span>
                                  </p>
                                  <p v-if="items.product_attribute_detail.attribute_priority_2 !== '' && items.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                    style="font-size: 16px; color: #636363;">{{ items.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                      style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.product_attribute_detail.attribute_priority_2 }}</span>
                                  </p>
                                  <v-chip :color="items.product_type === 'general'? '#3EC6B6' : '#FAC352'" class="white--text" label small style="font-size: 14px; border-radius: 6px !important; height: 20px;">{{ items.product_type === 'general'? $t('YourOrderPage.GeneralProducts') : $t('YourOrderPage.ServiceProduct') }}</v-chip>
                                  <div class="d-flex pt-3">
                                    <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #464646;">ราคารวม : 0.00</span>
                                    <span class="ml-auto" style="font-size: 16px; font-weight: 400; color: #464646;">จำนวน : {{ Number(items.quantity).toLocaleString() }}</span>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </div>
              <v-row justify="center" class="my-6" >
                <v-btn v-if="golocalmail === false" class="mb-4" width="80%"  outlined rounded color="#27AB9C" style="font-size: medium; font-weight: 600;" @click="goPoBuyerProfilePage()">{{ $t('YourOrderPage.GotoOrderList') }}</v-btn>
                <v-btn width="80%" rounded color="#27AB9C" class="white--text mb-3" style="font-size: medium; font-weight: 600;" @click="goHomePage()">{{ $t('YourOrderPage.GotoHomepage') }}</v-btn>
              </v-row>
              <!-- <v-card class="pa-2" style="border-radius: 8px;">
                <v-img class="mb-0" style="border-radius: 8px;" src="@/assets/yourordermo.png"></v-img>
                <v-col cols="12" class="my-0 px-0 pt-0">
                  <v-col class="mt-0 pb-0" cols="12">
                    <b style="font-size: 18px; font-weight: 700;">สถานะการสั่งซื้อ</b>
                    <v-col class="mt-3 pa-0">
                      <v-img class="rounded-lg" src="@/assets/successmo.png" width="auto" style="object-fit: cover;"></v-img>
                    </v-col>
                  </v-col>
                  <div class="mx-2"></div>
                  <v-col class="mt-0 pt-0">
                    <b style="font-size: 18px; font-weight: 700;">การชำระเงิน</b>
                    <v-row>
                      <v-col class="mt-5" cols="6">
                        <span style="font-size: 14px;">รหัสการสั่งซื้อ</span>
                      </v-col>
                      <v-col class="mt-5" cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;">{{itemsResult.data_payment[0].orderId}}</span>
                      </v-col>
                      <v-col cols="6">
                        <span style="font-size: 14px;">จำนวนเงิน</span>
                      </v-col>
                      <v-col cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;">{{ Number(itemsResult.data_payment[0].TxnAmount).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }) }} บาท</span>
                      </v-col>
                      <v-col cols="6">
                        <span style="font-size: 14px;">วันและเวลาที่ชำระเงิน</span>
                      </v-col>
                      <v-col cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;">{{new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                      </v-col>
                      <v-col cols="6">
                        <span style="font-size: 14px;">Ref</span>
                      </v-col>
                      <v-col cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;">{{itemsResult.data_payment[0].orderIDRef}}</span>
                      </v-col>
                      <v-col cols="6">
                        <span style="font-size: 14px;">รูปแบบการชำระเงิน</span>
                      </v-col>
                      <v-col v-if="itemsResult.data_payment[0].payType === 'Credit Card Installment'" cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;">{{itemsResult.data_payment[0].payType + ' ' + '(' + itemsResult.data_payment[0].installment_month + 'x @0%)'}}</span>
                      </v-col>
                      <v-col v-else cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;">{{itemsResult.data_payment[0].payType}}</span>
                      </v-col>
                      <v-col cols="6">
                        <span style="font-size: 14px;">ผลการชำระเงิน</span>
                      </v-col>
                      <v-col cols="6" align="right">
                        <span style="font-size: 14px; font-weight: 700;" v-if="itemsResult.data_payment[0].rmsg === 'Success'">ชำระเงินสำเร็จ</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col class="pa-1">
                    <v-card class="mt-4 py-3" color="#F9FAFD" style="border-radius: 8px;" elevation="0">
                      <b class="px-3" style="font-size: 18px; font-weight: 600;">รายละเอียดการสั่งซื้อ</b>
                      <v-container grid-list-xs class="pt-0">
                        <v-row class="ma-0" v-if="receivedNull.length !== 0">
                          <v-card class="pa-4" style="border-radius: 16px;">
                            <v-card-text class="pt-0">
                              <v-row dense>
                                <v-col class="pt-0 pa-0" style="margin-top: 5px;" cols="12">
                                  <v-img class="float-left" src="@/assets/map1.png" width="24" height="24"></v-img>
                                  <b class="ml-3"
                                    style="line-height: 35px; font-size: 16px; font-weight: 600; color: #333333;">ที่อยู่ในการจัดส่งสินค้า</b>
                                    <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 0px;" class="mb-4"></v-spacer>
                                    <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                                      <div>
                                        <span style="font-size: 14px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 12px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="role.role !== 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                                      <div>
                                        <span style="font-size: 14px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 12px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === true">
                                      <div>
                                        <span style="font-size: 14px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 12px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="role.role === 'ext_buyer' && SaleOrder === '' && golocalmail === false">
                                      <div>
                                        <span style="font-size: 14px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 12px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === false">
                                      <div>
                                        <span style="font-size: 14px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 12px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                    <div v-if="(role.role === 'ext_buyer' || role.role !== 'ext_buyer') && (SaleOrder === 'saleorder' || SaleOrder === 'saleorderbysale' || SaleOrder === 'ordercustomer') && golocalmail === true">
                                      <div>
                                        <span style="font-size: 14px; font-weight: 600;">{{ addressCustomer.name }}</span>
                                        <v-row>
                                          <v-col class="mt-2">
                                            <span style="font-size: 12px;">{{ addressCustomer.address }}</span>
                                          </v-col>
                                        </v-row>
                                      </div>
                                    </div>
                                </v-col>
                                <v-col v-for="(item, index) in receivedNull" :key="index"  cols="12" class="pa-0">
                                  <a-table :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headersMobile">
                                    <template slot="title">
                                      <v-row class="text-left">
                                        <v-col justify="center">
                                          <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                                          <b class="ml-3"
                                            style="line-height: 35px; cursor: pointer; font-size: 16px; font-weight: 600; color: #F4BC5F;">ร้านค้า: {{ item.shop_name_th }}</b>
                                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                                        </v-col>
                                      </v-row>
                                    </template>
                                    <template slot="productdetails" slot-scope="text, record">
                                      <v-row >
                                        <v-col cols="4" md="4" class="pr-2">
                                          <v-img :src="`${record.product_image}`" contain
                                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                            />
                                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                                            />
                                        </v-col>
                                        <v-col cols="8" md="8">
                                          <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                          </p>
                                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                          </p>
                                          <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;"> {{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : parseFloat(record.revenue_default)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></p>
                                          <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                                          <p class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">{{Number((record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : record.revenue_default) * parseFloat(record.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b>
                                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                                          </p>
                                        </v-col>
                                      </v-row>
                                    </template>
                                  </a-table>
                                  <a-table v-if="item.product_free !== null" :data-source="item.product_free"
                                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                                  :columns="headersMobile" :pagination="false">
                                  <template slot="title">
                                    <v-row class="text-left">
                                      <v-col align="center">
                                        <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="24" height="24"></v-img>
                                        <b class="float-left ml-2 d-inline-block text-truncate"
                                          style="line-height: 35px; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px">แถมฟรี</b>
                                          </v-col>
                                        </v-row>
                                        <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                                  </template>
                                  <template slot="productdetails" slot-scope="text, record">
                                    <v-row >
                                      <v-col cols="4" md="4" class="pr-2">
                                        <v-img :src="`${record.product_image}`" contain
                                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                          />
                                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                                          />
                                      </v-col>
                                      <v-col cols="8" md="8">
                                        <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                        </p>
                                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                        </p>
                                        <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;">0</b></p>
                                        <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                                        <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                                        </p>
                                        <p v-else class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                                        </p>
                                      </v-col>
                                    </v-row>
                                  </template>
                                </a-table>
                              </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-row>
                        <div v-if="receivedNotNull.length !== 0" class="mt-6">
                          <v-card class="pa-4" style="border-radius: 16px;">
                            <v-card-text class="pt-0">
                              <v-col v-if="receivedNotNull.length !== 0" cols="12" class="pa-0">
                                <v-img class="float-left" src="@/assets/pickup.png" width="24" height="24"></v-img>
                                <b class="ml-3"
                                  style="line-height: 35px; font-size: 16px; font-weight: 600; color:  #F4BC5F;">ที่อยู่ในการรับสินค้า</b>
                                  <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 0px;" class="mb-4"></v-spacer>
                              </v-col>
                              <v-row v-for="(item, index) in receivedNotNull" :key="index">
                                <v-col cols="12" class="pa-0">
                                  <a-table :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headersMobile">
                                    <template slot="title">
                                      <v-row class="text-left">
                                        <v-col justify="center">
                                          <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                                          <b class="ml-3"
                                            style="line-height: 35px; cursor: pointer; font-size: 16px; font-weight: 600; color: #F4BC5F;">ร้านค้า: {{ item.shop_name_th }}</b>
                                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                                        </v-col>
                                        <v-col cols="12">
                                          <div v-if="item.received_date !== null">
                                            <span style="font-size: 14px; font-weight: 600;">{{ item.shop_name_th }}</span>
                                            <v-row>
                                              <v-col class="mt-2">
                                                <span style="font-size: 12px;">{{ item.shop_address }}</span>
                                              </v-col>
                                            </v-row>
                                            <v-row>
                                              <v-col class="mt-2" >
                                                <span style="font-size: 14px;">วันรับสินค้า :<span class="float-end" style="font-size: 16px; font-weight: 700;">{{ formatDateToShow(item.received_date) }}</span></span>
                                              </v-col>
                                            </v-row>
                                            <v-row>
                                              <v-col class="mt-2">
                                                <span style="font-size: 14px;">เวลารับสินค้า :<span class="float-end" style="font-size: 16px; font-weight: 700;">{{ new Date(item.received_date).toLocaleTimeString('th-TH', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) }} น.</span></span>
                                              </v-col>
                                            </v-row>
                                          </div>
                                        </v-col>
                                      </v-row>
                                    </template>
                                    <template slot="productdetails" slot-scope="text, record">
                                      <v-row >
                                        <v-col cols="4" md="4" class="pr-2">
                                          <v-img :src="`${record.product_image}`" contain
                                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                            />
                                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                                            />
                                        </v-col>
                                        <v-col cols="8" md="8">
                                          <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                          </p>
                                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                          </p>
                                          <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;"> {{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : parseFloat(record.revenue_default)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></p>
                                          <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                                          <p class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">{{Number((record.vat_default === 'yes' ? parseFloat(record.revenue_default) + parseFloat(record.vat_include) : record.revenue_default) * parseFloat(record.quantity)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b>
                                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                                          </p>
                                        </v-col>
                                      </v-row>
                                    </template>
                                  </a-table>
                                  <a-table v-if="item.product_free !== null" :data-source="item.product_free"
                                  :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                                  :columns="headersMobile" :pagination="false">
                                  <template slot="title">
                                    <v-row class="text-left">
                                      <v-col align="center">
                                        <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="24" height="24"></v-img>
                                        <b class="float-left ml-2 d-inline-block text-truncate"
                                          style="line-height: 35px; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px">แถมฟรี</b>
                                          </v-col>
                                        </v-row>
                                        <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                                  </template>
                                  <template slot="productdetails" slot-scope="text, record">
                                    <v-row >
                                      <v-col cols="4" md="4" class="pr-2">
                                        <v-img :src="`${record.product_image}`" contain
                                          :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                                          />
                                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                                          />
                                      </v-col>
                                      <v-col cols="8" md="8">
                                        <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                                        <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                                        <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                                          style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                                        </p>
                                        <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                                          style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                                            style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                                        </p>
                                        <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;">0</b></p>
                                        <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                                        <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                                        </p>
                                        <p v-else class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                                          <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                                        </p>
                                      </v-col>
                                    </v-row>
                                  </template>
                                </a-table>
                              </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </div>
                      </v-container>
                    </v-card>
                    <v-card v-if="inetRelation.length !== 0" class="mt-6 pa-4 " color="#F9FAFD" style="border-radius: 8px;" elevation="0">
                      <v-col>
                        <v-row class="pl-4 pr-3 pb-2 align-baseline">
                          <v-col cols="12" class="pl-0 pt-4 pb-1">
                            <span style="font-size: 16px; font-weight: 700;">โค้ดส่วนลด</span>
                          </v-col>
                          <v-col cols="12" class="pl-0 pt-0">
                            <span style="font-size: 14px; font-weight: 700;">รายชื่อพนักงาน {{inetRelation.length}} รายชื่อ</span>
                          </v-col>
                        </v-row>
                        <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #FFFFFF; border-radius: 20px; max-height: 435px; overflow-y: auto; overflow-x: hidden;">
                          <v-row class="pb-1 pt-2"  v-for="(item, index) in inetRelation" :key="index">
                            <v-col cols="12" class="px-4 py-0">
                              <v-col cols="12" class="pa-0">
                                <span style="font-size: 14px; font-weight: 400;">{{index + 1}}. แผนก: <b>{{item.team}}</b></span>
                              </v-col>
                              <v-col cols="12" class="pa-0 pl-4">
                                <span style="font-size: 14px; font-weight: 400;">บริษัท: <b>{{item.company}}</b></span>
                              </v-col>
                              <v-col cols="12" class="pa-0 pl-4">
                                <span style="font-size: 14px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                              </v-col>
                            </v-col>
                          </v-row>
                        </v-card>
                      </v-col>
                    </v-card>
                  </v-col>
                  <v-row v-if="!MobileSize" justify="center" no-gutters class="mt-8" style="margin-bottom: -10px">
                    <v-btn v-if="golocalmail === false" width="250"  outlined rounded color="#27AB9C" class="mr-8" style="font-size: medium; font-weight: 600;" @click="goPoBuyerProfilePage()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
                    <v-btn width="250" rounded color="#27AB9C" class="white--text" style="font-size: medium; font-weight: 600;" @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                  </v-row>
                  <v-row v-if="MobileSize" justify="center" class="mt-8" >
                    <v-btn v-if="golocalmail === false" class="mb-4" width="80%"  outlined rounded color="#27AB9C" style="font-size: medium; font-weight: 600;" @click="goPoBuyerProfilePage()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
                    <v-btn width="80%" rounded color="#27AB9C" class="white--text mb-3" style="font-size: medium; font-weight: 600;" @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                  </v-row>
                </v-col>
              </v-card> -->
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data () {
    return {
      ShowMoreDetail: true,
      receivedNotNull: [],
      receivedNull: [],
      inetRelation: [],
      SaleOrder: '',
      golocalmail: false,
      bankName: '',
      datePickUp: '',
      comAddress: [],
      timePickUp: '',
      dataSellerShop: [],
      typeShipping: '',
      itemsResult: [],
      radioPayment: '',
      deleteAdressData: '',
      typeButton: '',
      DialogAddress: false,
      DialogQR: false,
      dialogSuccess: false,
      dialogAwaitConfirm: false,
      modalContractStartDate: false,
      modalContractEndDate: false,
      CartPage: 'CartPage',
      role: '',
      dates: '',
      menu: false,
      default_address: '',
      radios: 'radio-2',
      lazy: false,
      overlay: false,
      cartData: '',
      itemsCart: [],
      Address: '',
      Fullname: '',
      EditAddressDetail: '',
      titleAddress: '',
      address_data: '',
      taxAddress: '',
      companyName: '',
      companyTaxID: '',
      checkAdminQU: true,
      pplURL: '',
      pplToken: '',
      page: '',
      taxinvoiceAddress: [],
      taxinvoiceAddressNew: [],
      googleItem: [],
      taxRoles: 'No',
      selectTypeAddress: 'Normal',
      oneDataSpecial: '',
      modalPayment: false,
      responseSentDataPPL: '',
      SelectCouponOrPoint: true,
      dialog_Cancel_Coupon: false,
      checkOwnShop: 'N',
      discountBaht: '',
      discountCode: '',
      disableButtonPay: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      minDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchContractStartDate: '',
      setMinDateContractEndDate: '',
      contractStartDate: '',
      contractDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      ActiveDiscount: false,
      selectDiscount: '',
      discount: '',
      contractSet: false,
      reason: '',
      selectBudget: '',
      choose_list: '',
      selectTypeDoc: '',
      tax_id: '',
      itemTypeDoc: [],
      selectedPr: '',
      itemCodePrList: [],
      itemBudget: [
        { text: 'งบดำเนินการ', value: 'operating_budget' },
        { text: 'งบลงทุน', value: 'investment_budget' },
        { text: 'งบรายจ่ายประจำ', value: 'regular_expenditure_budget' }
      ],
      selectCutBudget: '',
      itemCutBudget: [
        { text: 'ต้นทุนขาย (COGS)', value: 'COGS' },
        { text: 'ค่าใช้จ่ายและบริการ (SG&A)', value: 'SG&A' },
        { text: 'ต้นทุนวิจัยและพัฒนา (R&D)', value: 'R&D' }
      ],
      Name_Buyer: '',
      Phone_Buyer: '',
      Position_Buyer: '',
      Email_Buyer: '',
      Name_Audit1: '',
      Phone_Audit1: '',
      Position_Audit1: '',
      Email_Audit1: '',
      Name_Audit2: '',
      Phone_Audit2: '',
      Position_Audit2: '',
      Email_Audit2: '',
      json_personal: [],
      purchasing_cheif: [],
      inspectors_one: [],
      inspectors_two: [],
      name: '',
      position: '',
      phone: '',
      email: '',
      buyer_name: '',
      buyer_phone: '',
      buyer_email: '',
      userdetail: [],
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'รถเข็นสินค้า',
          disabled: false,
          href: '/shoppingcart'
        },
        {
          text: 'รายการสั่งซื้อสินค้า',
          disabled: false,
          href: '/checkout'
        },
        {
          text: 'คำสั่งซื้อของคุณ',
          disabled: true,
          href: '/yourorder'
        }
      ],
      addressCustomer: [],
      paymentNum: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headers () {
      if (this.role.role !== 'ext_buyer' && this.itemsCart.is_JV === 'yes') {
        const headers = [
          // {
          //   title: 'รหัส SKU',
          //   dataIndex: 'sku',
          //   key: 'sku',
          //   scopedSlots: { customRender: 'sku' },
          //   width: '15%'
          // },
          {
            title: this.$t('YourOrderPage.ProductDetails'),
            dataIndex: 'productdetails',
            key: 'productdetails',
            align: 'start',
            scopedSlots: { customRender: 'productdetails' },
            width: '25%'
          },
          {
            title: this.$t('YourOrderPage.UnitPrice'),
            dataIndex: 'revenue_default',
            key: 'revenue_default',
            scopedSlots: { customRender: 'revenue_default' },
            align: 'center',
            width: '20%'
          },
          {
            title: this.$t('YourOrderPage.Quantity'),
            dataIndex: 'quantity',
            key: 'quantity',
            scopedSlots: { customRender: 'quantity' },
            align: 'center',
            width: '10%'
          },
          {
            title: this.$t('YourOrderPage.TotalPrice'),
            dataIndex: 'revenue_vat',
            scopedSlots: { customRender: 'revenue_vat' },
            key: 'revenue_vat',
            align: 'center',
            width: '15%'
          },
          {
            title: 'Item Code PR',
            dataIndex: 'item_code_pr',
            scopedSlots: { customRender: 'item_code_pr' },
            key: 'item_code_pr',
            align: 'center',
            width: '20%'
          }
        ]
        return headers
      } else if (this.role.role !== 'ext_buyer' && this.itemsCart.is_JV !== 'yes') {
        const headers = [
          // {
          //   title: 'รหัส SKU',
          //   dataIndex: 'sku',
          //   key: 'sku',
          //   scopedSlots: { customRender: 'sku' },
          //   width: '15%'
          // },
          {
            title: this.$t('YourOrderPage.ProductDetails'),
            dataIndex: 'productdetails',
            key: 'productdetails',
            align: 'start',
            scopedSlots: { customRender: 'productdetails' },
            width: '25%'
          },
          {
            title: this.$t('YourOrderPage.UnitPrice'),
            dataIndex: 'revenue_default',
            key: 'revenue_default',
            scopedSlots: { customRender: 'revenue_default' },
            align: 'center',
            width: '20%'
          },
          {
            title: this.$t('YourOrderPage.Quantity'),
            dataIndex: 'quantity',
            key: 'quantity',
            scopedSlots: { customRender: 'quantity' },
            align: 'center',
            width: '10%'
          },
          {
            title: this.$t('YourOrderPage.TotalPrice'),
            dataIndex: 'revenue_vat',
            scopedSlots: { customRender: 'revenue_vat' },
            key: 'revenue_vat',
            align: 'center',
            width: '15%'
          }
        ]
        return headers
      } else {
        const headers = [
          // {
          //   title: 'รหัส SKU',
          //   dataIndex: 'sku',
          //   key: 'sku',
          //   scopedSlots: { customRender: 'sku' },
          //   width: '15%'
          // },
          {
            title: this.$t('YourOrderPage.ProductDetails'),
            dataIndex: 'productdetails',
            key: 'productdetails',
            align: 'start',
            scopedSlots: { customRender: 'productdetails' },
            width: '25%'
          },
          {
            title: this.$t('YourOrderPage.UnitPrice'),
            dataIndex: 'revenue_default',
            key: 'revenue_default',
            scopedSlots: { customRender: 'revenue_default' },
            align: 'center',
            width: '20%'
          },
          {
            title: this.$t('YourOrderPage.Quantity'),
            dataIndex: 'quantity',
            key: 'quantity',
            scopedSlots: { customRender: 'quantity' },
            align: 'center',
            width: '10%'
          },
          {
            title: this.$t('YourOrderPage.TotalPrice'),
            dataIndex: 'revenue_vat',
            scopedSlots: { customRender: 'revenue_vat' },
            key: 'revenue_vat',
            align: 'center',
            width: '15%'
          }
        ]
        return headers
      }
    },
    headersMobile () {
      const headersMobile = [
        {
          title: this.$t('YourOrderPage.ProductDetails'),
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headersMobile
    }
  },
  async created () {
    this.$EventBus.$on('SentGetCart', this.getCart)
    this.$EventBus.$on('EditAddressComplete', (data) => { this.getCart(data) })
    this.$EventBus.$on('selectcouponorpointCheckout', this.selectcouponorpointCheckout)
    this.$EventBus.$emit('GetTaxAddress')
    if (localStorage.getItem('roleUser') !== null) {
      this.role = JSON.parse(localStorage.getItem('roleUser'))
    } else {
      this.role = {
        role: 'ext_buyer'
      }
    }
    if (localStorage.getItem('oneData') !== null) {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (Object.prototype.hasOwnProperty.call(onedata, 'cartData')) {
        // if (onedata.cartData.coupon.length !== 0) {
        //   if (onedata.cartData.coupon === undefined || onedata.cartData.coupon[0].coupon_sort === 'cancel' || onedata.cartData.coupon.length === 0) {
        //     if (localStorage.getItem('ClickgoToCheckOut') !== null) {
        //       this.SelectCouponOrPoint = true
        //     }
        //   } else {
        //     localStorage.setItem('ClickgoToCheckOut', true)
        //     if (localStorage.getItem('ClickgoToCheckOut') !== null) {
        //       this.SelectCouponOrPoint = false
        //       if (localStorage.getItem('CouponOrPoint') === 'Point') {
        //         this.nameCouponOrPoint = 'ใช้คะแนน ' + onedata.cartData.coupon[0].point + ' คะแนน'
        //       } else {
        //         this.nameCouponOrPoint = onedata.cartData.coupon[0].coupon_name
        //       }
        //     }
        //   }
        // }
        this.cartData = onedata.cartData
        this.oneDataSpecial = onedata.cartDataSpecialPrice
        // this.getCart()
        // this.checkURLParams()
        // await this.getItemCodePr()
        // console.log(this.$router.currentRoute.query.id)
        // await this.getCompanyAddress()
        // await this.CheckUpdateOrderRepeat()
        if (localStorage.getItem('SetRowCompany') !== null) {
          // await this.GetPersonal()
        }
        // localStorage.removeItem('ClickgoToCheckOut')
      } else {
        this.cartData = ''
        if (localStorage.getItem('roleUser').role !== 'purchaser') {
          this.$router.push('/')
        }
      }
      if (localStorage.getItem('sale_order') !== null) {
        this.SaleOrder = localStorage.getItem('sale_order')
      } else {
        this.SaleOrder = ''
      }
    }
    const goLocalValue = this.$route.query.go_local
    if (goLocalValue === 'mail') {
      this.golocalmail = true
      this.typeShipping = 'online'
      await this.checkResult()
    } else {
      this.golocalmail = false
      if (localStorage.getItem('oneData') === null) {
        this.goHomePage()
      } else {
        await this.getAddress()
        // await this.checkTypeShipping()
      }
    }
    window.scrollTo(0, 0)
  },
  methods: {
    // async getCompanyAddress () {
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var companyId = ''
    //   if (dataRole.role !== 'ext_buyer') {
    //     if (localStorage.getItem('SetRowCompany') !== null) {
    //       var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //       companyId = companyDataID.company.company_id
    //     } else {
    //       companyId = -1
    //     }
    //   }
    //   var data = {
    //     company_id: companyId
    //   }
    //   await this.$store.dispatch('actionsGetCompanyAddress', data)
    //   var res = await this.$store.state.ModuleCart.stateGetCompanyAddress
    //   if (res.ok === 'y') {
    //     // console.log(res.query_result)
    //     this.comAddress = res.query_result
    //   }
    // },
    // async GetPersonal () {
    //   const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //   const data = {
    //     company_id: companyId.company.company_id
    //   }
    //   await this.$store.dispatch('actionsDetailCompany', data)
    //   var companyData = await this.$store.state.ModuleAdminManage.stateDetailCompany
    //   this.companydata = companyData.data
    //   this.companyAddressData = companyData.data.company_address
    // },
    goHomePage () {
      localStorage.removeItem('sale_order')
      this.$router.push({ path: '/' }).catch(() => { })
    },
    async goPoBuyerProfilePage () {
      var SaleOrder = ''
      if (localStorage.getItem('sale_order') !== null) {
        SaleOrder = localStorage.getItem('sale_order')
      } else {
        SaleOrder = ''
      }
      if (this.role.role === 'ext_buyer' && SaleOrder === '') {
        if (!this.MobileSize) {
          this.$router.push({ path: '/pobuyerProfile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => { })
        }
      } else if (this.role.role === 'purchaser' && SaleOrder === '') {
        const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        var companyid = companyId.company.company_id
        const data = {
          company_id: companyid
        }
        await this.$store.dispatch('actionsDetailCompany', data)
        await this.$store.dispatch('actionsAuthorityUser')
        var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
        var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
        var listcompany = responseposition.data.list_company
        for (let i = 0; i < listcompany.length; i++) {
          if (responsecompany.data.id === listcompany[i].company_id) {
            localStorage.removeItem('list_Company_detail')
            localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
          }
        }
        localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
        if (!this.MobileSize) {
          this.$router.push({ path: '/orderCompany' }).catch(() => { })
        } else {
          this.$router.push({ path: '/orderCompanyMobile' }).catch(() => { })
        }
      } else if (this.role.role === 'sale_order_no_JV' && SaleOrder === 'saleorderbysale') {
        if (!this.MobileSize) {
          localStorage.removeItem('sale_order')
          this.$router.push({ path: '/DetailOrderSalesNoJV?orderNumber=' + this.paymentNum + '&tranNumber=' + this.paymentNum }).catch(() => {})
        } else {
          localStorage.removeItem('sale_order')
          this.$router.push({ path: '/DetailOrderSalesNoJVMobile?orderNumber=' + this.paymentNum + '&tranNumber=' + this.paymentNum }).catch(() => {})
        }
      } else if (this.role.role === 'sale_order_no_JV' && SaleOrder === 'ordercustomer') {
        var shopDetailSale1 = JSON.parse(localStorage.getItem('shopDetail'))
        if (!this.MobileSize) {
          localStorage.removeItem('sale_order')
          this.$router.push({ path: '/seller?ShopID=' + shopDetailSale1.id + '&ShopName=' + shopDetailSale1.name }).catch(() => {})
          await this.$router.push({ path: '/DetailOrderCustomer?orderNumber=' + this.paymentNum + '&tranNumber=' + this.paymentNum }).catch(() => {})
        } else {
          localStorage.removeItem('sale_order')
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetailSale1.id + '&ShopName=' + shopDetailSale1.name }).catch(() => {})
          await this.$router.push({ path: '/DetailOrderCustomerMobile?orderNumber=' + this.paymentNum + '&tranNumber=' + this.paymentNum }).catch(() => {})
        }
      } else {
        var shopDetailSale = JSON.parse(localStorage.getItem('shopDetail'))
        if (!this.MobileSize) {
          localStorage.removeItem('sale_order')
          this.$router.push({ path: '/seller?ShopID=' + shopDetailSale.id + '&ShopName=' + shopDetailSale.name }).catch(() => {})
          await this.$router.push({ path: '/DetailOrderSales?orderNumber=' + this.paymentNum + '&tranNumber=' + this.paymentNum }).catch(() => {})
        } else {
          localStorage.removeItem('sale_order')
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetailSale.id + '&ShopName=' + shopDetailSale.name }).catch(() => {})
          await this.$router.push({ path: '/DetailOrderSalesMobile?orderNumber=' + this.paymentNum + '&tranNumber=' + this.paymentNum }).catch(() => {})
        }
      }
    },
    formatISODateTimeToThaiFormat (isoDateTime) {
      const date = new Date(isoDateTime)
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      const hours = date.getHours() - 7
      const minutes = date.getMinutes()
      const seconds = date.getSeconds()
      return `${day}/${month}/${year} ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
    },
    // async getItemCodePr () {
    //   this.tax_id = localStorage.getItem('tax_id')
    //   await this.$store.dispatch('actionsListItemCodePr', this.tax_id)
    //   var res = await this.$store.state.ModuleCart.stateListItemCodePr
    //   if (res.message === 'Show section success') {
    //     // console.log('res.data', res.data)
    //     this.itemCodePrList = res.data
    //   }
    // },
    async getAddress () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionListUserAddress')
      var userdetail = await this.$store.state.ModuleUser.stateListUserAddress
      // this.$store.commit('closeLoader')
      this.userdetail = [...userdetail.data]
      this.userdetail.forEach(element => {
        var x = element.phone.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/)
        this.telnumber = !x[2] ? x[1] : x[1] + '-' + x[2] + (x[3] ? '-' + x[3] : '')
        if (element.first_name === '' && element.last_name === '') {
          this.fullname = '-'
        } else {
          this.fullname = element.first_name + ' ' + element.last_name
        }
      })
      await this.checkResult()
    },
    async getCart () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var res
      this.checkOwnShop = 'N'
      this.cartData = onedata.cartData
      if (Object.prototype.hasOwnProperty.call(onedata, 'cartDataSpecialPrice')) {
        if (onedata.cartDataSpecialPrice === 'yes') {
          await this.$store.dispatch('ActionGetCartSpecialPrice', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCartSpecialPrice
          // console.log('6666666', res)
        } else {
          await this.$store.dispatch('ActionGetCart', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCart
          // console.log('555555555', res)
        }
      }
      if (res.message === 'Get cart success') {
        this.itemsCart = res.data
        // this.itemsCart.choose_list[0].product_list.forEach(element => {
        //   if (element.item_code_pr_buyer === null) {
        //     element.item_code_pr_buyer = ''
        //   }
        // })
        // console.log(this.itemsCart)
        if (res.data.check_own_shop === 'yes') {
          this.checkOwnShop = 'Y'
        } else {
          this.checkOwnShop = 'N'
        }
        if (this.itemsCart.total_price_no_vat >= 50000) {
          this.contractSet = true
        }
        // this.googleSentData()
        if (this.itemsCart.address_data.length !== 0) {
          if (dataRole.role === 'purchaser') {
            var addressPurchaser = ''
            this.choose_list = this.itemsCart.choose_list[0].pay_type
            this.address_data = this.itemsCart.address_data[0]
            this.Fullname = this.address_data.first_name + ' ' + this.address_data.last_name
            // addressPurchaser = this.address_data.detail + ' ' + 'แขวง/ตำบล' + ' ' + this.address_data.district + ' ' + 'จังหวัด' + ' ' + this.address_data.province + ' ' + this.address_data.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + this.address_data.phone
            this.Address = addressPurchaser
          } else {
            this.itemsCart.address_data.forEach(element => {
              // console.log(element)
              if (element.default_address === 'Y' || element.default_address !== undefined) {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address = ''
                address = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address
              } else {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address1 = ''
                address1 = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address1
              }
            })
          }
        } else {
          this.Fullname = ''
          this.Address = this.$t('NoAddress')
        }
        // get user data
        // var data = {
        //   role_user: dataRole.role
        // }
        // await this.$store.dispatch('actionsUserDetailPage', data)
        // var response = await this.$store.state.ModuleUser.stateUserDetailPage
        var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
        var user = response.data[0]
        this.buyer_name = user.first_name_th + ' ' + user.last_name_th
        this.buyer_phone = user.phone
        this.buyer_email = user.email
        // get admin data
        const sendId = { user_id: user.id }
        await this.$store.dispatch('ActionGetAdminData', sendId)
        var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
        var adminStatus = false
        if (responseAdmin.data.length !== 0) {
          adminStatus = true
        } else {
          adminStatus = false
        }
        if (adminStatus === true) {
          this.sentDataPPL()
        }
        this.overlay = false
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: this.$t('Incompleteinformation')
        })
        this.backstep()
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'SERVER ERROR'
        })
        this.backstep()
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: this.$t('ConfirmOrder')
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: this.$t('OverWeight')
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${this.$t('Sku') + ' ' + res.data[0].sku + ' ' + this.$t('WeightProblem')}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        this.backstep()
      } else if (res.message === 'ไม่พบที่อยู่สำหรับจัดส่งสินค้า กรุุณาทำการเพิ่มข้อมูลที่อยู่ใหม่อีกครั้ง') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/addressProfileMobile' })
        } else {
          this.$router.push({ path: '/addressProfile' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.SelectCouponOrPoint = true
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else if (res.message === 'Company User Permission Not Found.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: this.$t('UserPermission')
        })
        this.backstep()
      } else {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        await this.$EventBus.$emit('CancleBookCouponCheckout')
        this.backstep()
      }
      this.$store.commit('closeLoader')
    },
    // async goProductDetail (item) {
    //   console.log(item)
    //   const nameCleaned = item.product_name.replace(/\s/g, '-')
    //   this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } }).catch(() => {})
    //   // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } })
    //   // window.location.assign(routeData.href, '_blank')
    // },
    gotoShopDetail (name, id) {
      const shopCleaned = encodeURIComponent(name.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${id}` }).catch(() => {})
    },
    async checkResult () {
      this.$store.commit('openLoader')
      var data
      data = {
        payment_transaction_number: this.$router.currentRoute.query.id
      }
      await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
      // await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
      const res = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
      // const res = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
      if (res.result === 'SUCCESS') {
        this.addressCustomer = ''
        this.dataSellerShop = ''
        this.itemsResult = res.data
        this.paymentNum = res.data.payment_num
        this.dataSellerShop = await res.data.data_seller_shop_all
        this.receivedNull = this.dataSellerShop.filter(shop => shop.received_date === null)
        this.receivedNotNull = this.dataSellerShop.filter(shop => shop.received_date !== null)
        this.addressCustomer = await res.data.data_customer
        this.inetRelation = await res.data.data_inet_relation
        // this.typeShipping = res.data.type_shipping
        this.datePickUp = this.itemsResult.data_seller_shop_all.received_date
        this.timePickUp = new Date(this.itemsResult.data_seller_shop_all.received_date).toLocaleTimeString('th-TH', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' })
        // console.log(this.addressCustomer)
        if (this.itemsResult.length !== 0) {
          if (this.itemsResult.data_payment[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'CIMB') {
            this.bankName = 'ธนาคารซีไอเอ็มบี'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        this.CheckUpdateOrderRepeat()
        this.$store.commit('closeLoader')
        // console.log(this.dataSellerShop)
      } else if (res.result === 'ERROR') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: this.$t('YourOrderPage.Paymentincomplete')
        })
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    async checkTypeShipping () {
      this.$store.commit('openLoader')
      var data
      var res = ''
      var companyId = ''
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyId = companyDataID.company.company_id
      } else {
        companyId = -1
      }
      if (dataRole.role === 'ext_buyer' && this.SaleOrder === '') {
        data = {
          payment_transaction_number: this.$router.currentRoute.query.id,
          role_user: dataRole.role
        }
        await this.$store.dispatch('actionOrderDetail', data)
        res = await this.$store.state.ModuleOrder.stateOrderDetailData
        // console.log('1', sessionStorage.getItem('utm_source'))
        // var onedata = await JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        // var dataCheckOrder = {
        //   utm_source: sessionStorage.getItem('utm_source'),
        //   user_id: onedata.user.user_id,
        //   order_number: res.data.payment_transaction,
        //   status: res.data.transaction_status
        // }
        // // console.log('dataCheckOrder', dataCheckOrder)
        // await this.$store.dispatch('actionsOrderReach', dataCheckOrder)
        // // var response = await this.$store.state.ModuleAdminManage.stateOrderReach
        // // console.log('response', response)
        // sessionStorage.removeItem('utm_source')
        // console.log('2', sessionStorage.getItem('utm_source'))
      } else if (dataRole.role !== 'ext_buyer' && this.SaleOrder === '') {
        data = {
          payment_transaction_number: this.$router.currentRoute.query.id,
          role_user: dataRole.role,
          company_id: companyId
        }
        await this.$store.dispatch('actionsDetailOrderPurchaser', data)
        res = await this.$store.state.ModuleAdminManage.stateDetailOrderPurchaser
      } else {
        data = {
          order_number: this.$router.currentRoute.query.id
        }
        await this.$store.dispatch('actionsDetailOrderSale', data)
        res = await this.$store.state.ModuleSaleOrder.stateDetailOrderSale
      }
      if (res.message === 'Get detail order success' || res.message === 'Get detail order sale success.' || res.message === 'Get detail order purchaser success') {
        this.inetRelation = res.data.inet_relation_ship !== undefined ? res.data.inet_relation_ship : []
        this.typeShipping = res.data.type_shipping
        this.datePickUp = this.itemsResult.data_seller_shop.received_date
        this.timePickUp = new Date(this.itemsResult.data_seller_shop.received_date).toLocaleTimeString('th-TH', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' })
        this.$store.commit('closeLoader')
        // console.log(this.datePickUp)
        // console.log(this.timePickUp)
      } else {
        this.$store.commit('closeLoader')
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      if (this.$i18n.locale === 'th') {
        return new Date(date).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' })
      } else {
        return new Date(date).toLocaleDateString('en-GB', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' })
      }
    },
    async CheckUpdateOrderRepeat () {
      var data
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      data = {
        payment_transaction_number: this.$router.currentRoute.query.id,
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionCheckUpdateOrderRepeat', data)
      await this.$store.state.ModuleOrder.stateCheckUpdateOrderRepeat
    }
  }
}
</script>

<style lang="css" scoped>
.m-auto {
  margin: auto;
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 4px 0px 4px 0px !important;
}

.v-breadcrumbs li .v-icon {
  color: #27AB9C;
}
</style>

<style>
.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px 10px;
  overflow-wrap: break-word;
  box-shadow: 0px 0.5px 2px 0px #60617029;
  box-shadow: 0px 0px 0px 0px #28293D14;
}

.ant-table-thead>tr>th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #F2F9FF !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}

.ant-table-column-title {
  color: #333333 !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 14px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  top: 40px;
}

.ant-time-picker-panel {
  width: 418px;
}

.ant-time-picker-panel-select:first-child {
  width: 50%;
}

.ant-time-picker-panel-select:last-child {
  width: 50%;
}

.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}
</style>

<style lang="css" scoped>
.m-auto {
  margin: auto;
}

.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}
@media screen and (max-width: 640px) {
  .breadcrumbsPadding {
    display: flex !important;
    align-items: center !important;
    width: max-content !important;
    padding: 1rem 1rem !important;
    /* margin-bottom: 12px !important; */
    min-width: 100% !important;
    overflow: auto !important;
    padding-left: 2.5%;
    padding-top: 1.5%;
    padding-bottom: 1.5%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: inline-block;
    flex-wrap: wrap;
    flex: 0 1 auto;
    list-style-type: none;
    width: 100%;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}

.v-breadcrumbs li .v-icon {
  color: #27AB9C;
}

.imageshow {
  max-width: 52px !important;
  width: 52px;
  height: 52px;
}

.imageshowIpadPro {
  max-width: 52px !important;
  width: 52px;
  height: 52px;
}

.totalPriceFont {
  font-size: 20px;
}

::v-deep .ant-table-pagination {
  display: none;
}

::v-deep .ant-table-thead > tr > th, ::v-deep .ant-table-tbody > tr > td {
  padding: 9px 10px !important;
  overflow-wrap: break-word;
}

::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}

::v-deep .ant-table-thead>tr>th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-tbody>tr>td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-body {
  transition: opacity 0.3s;
  border: 1px solid #F2F2F2;
  border-radius: 8px;
  box-shadow: 0px 0.5px 2px 0px #60617029;
  box-shadow: 0px 0px 1px 0px #28293D14;
}

::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px transparent;
  margin-bottom: 6px;
  border-radius: 8px;
}
/* ::v-deep .setBorderTable .ant-table-thead {
  box-shadow: 0px 0.5px 2px 0px #60617029;
  box-shadow: 0px 0px 1px 0px #28293D14;
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}
::v-deep .setBorderTable .ant-table-tbody {
  box-shadow: 0px 0.5px 2px 0px #60617029;
  box-shadow: 0px 0px 1px 0px #28293D14;
  border: 1px solid #F2F2F2 !important;
  border-bottom-right-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
} */
::v-deep .setBorderTable .ant-table-tbody > tr > td {
  border-bottom: 1px solid #F3F5F7 !important;
  transition: background 0.3s;
}
</style>

<style>
.custom-background .v-input__slot {
  background-color: #E6E6E6 !important;
}

.v-input--selection-controls .v-input__slot,
.v-input--selection-controls .v-radio {
  margin-bottom: 0px;
  cursor: pointer;
}</style>

<style lang="css" scoped>.v-Card {
  border-radius: 8px;
}

.Textcard {
  font-size: 16px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
  padding-right: 0px;
}

.TextBaht {
  font-size: 16px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
}</style>
<style scoped>
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
</style>
