<template>
  <v-container grid-list-xs>
    <v-card>
      <v-card-title>รายการซื้อของฉัน</v-card-title>
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-row no-gutters justify="end">
      <v-col cols="12" class="py-0">
        <a-tabs @change="SelectDetailOrder">
          <a-tab-pane v-for="item in OrderName" :key="item.key" :tab="item.name"></a-tab-pane>
        </a-tabs>
      </v-col>
      <v-col cols="4" class="mr-4">
        <v-text-field
          v-model="search"
          dense
          hide-details
          outlined
          placeholder="ค้นหาใบสั่งซื้อ"
        ></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-card outlined class="small-card mx-4 my-5">
          <v-data-table
            :headers="keyCheckHead == 3 || keyCheckHead == 4 ? headersSuccess : keyCheckHead == 1 ? headersPending : keyCheckHead == 2 ? headersNoPaid : headers"
            :items="DataTable"
            :search="search"
            style="width:100 %"
            height="588px"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' })}}
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                <a @click="orderDetail(item)">{{item.payment_transaction_number}}</a>
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                {{ Number(item.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              </template>
              <template v-slot:[`item.payment`]="{ item }">
                <v-row justify="center" >
                  <v-btn v-if="item.seller_sent_status ==='Success'" disabled rounded color="primary" small @click="GoToPayment(item)">จ่ายเงิน!</v-btn>
                  <v-btn v-else-if="item.seller_sent_status ==='cancel'" disabled rounded color="primary" small @click="GoToPayment(item)">จ่ายเงิน!</v-btn>
                  <v-btn v-else rounded color="primary" small @click="GoToPayment(item)">จ่ายเงิน!</v-btn>
                </v-row>
              </template><template v-slot:[`item.transaction_status`]="{ item }">
                <span v-if="item.transaction_status === 'Success' && item.seller_sent_status !== 'cancel'">ชำระเงินสำเร็จ</span>
                <span v-else-if="item.transaction_status === 'Refund' && item.seller_sent_status === 'cancel'">รอการคืนเงิน</span>
                <span v-else-if="item.seller_sent_status === 'cancel' || item.transaction_status === 'Cancel'">ยกเลิกสินค้า</span>
                <span v-else-if="item.transaction_status === 'Pending'">รออนุมัติ</span>
                <span v-else-if="item.transaction_status === 'Fail'">ชำระเงินไม่สำเร็จ</span>
                <span v-else>ยังไม่ได้ชำระเงิน</span>
              </template>

              <template v-slot:[`item.buyer_received_status`]="{ item }">
                <v-row class="pt-5">
                  <v-select
                   v-model="item.buyer_received_status"
                   :items="receive_items"
                   item-text="text"
                   item-value="value"
                   @change="UpdateStatusBuyer(item)"
                   outlined dense>
                  </v-select>
                </v-row>
              </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      orderList: [],
      StateStatus: 0,
      OrderName: [
        { key: 0, name: 'ข้อมูลไม่ครบ' },
        { key: 1, name: 'รออนุมัติ' },
        { key: 2, name: 'ยังไม่ได้ชำระเงิน' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 4, name: 'ยกเลิก' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' }
      ],
      keyCheckHead: null,
      headers: [
        { text: 'วันที่', value: 'created_at', align: 'center' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center' },
        // { text: 'ร้านค้า', value: 'order_number' },
        // { text: 'สินค้า', value: 'order_number' },
        { text: 'ราคา', value: 'total_amount', align: 'center' },
        { text: 'ชำระเงินเมื่อ', value: 'paid_datetime', align: 'center' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center' },
        { text: 'จ่ายเงิน', value: 'payment', align: 'center' }
      ],
      headersSuccess: [
        { text: 'วันที่', value: 'created_at', align: 'center' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center' },
        { text: 'ราคา', value: 'total_amount', align: 'center' },
        { text: 'ชำระเงินเมื่อ', value: 'paid_datetime', align: 'center' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center' }
        // { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        // { text: 'วันที่ได้รับของ', value: 'received_date', align: 'center' }
      ],
      headersPending: [
        { text: 'วันที่', value: 'created_at', align: 'center' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center' },
        { text: 'ราคา', value: 'total_amount', align: 'center' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center' }
        // { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        // { text: 'วันที่ได้รับของ', value: 'received_date', align: 'center' }
      ],
      headersNoPaid: [
        { text: 'วันที่', value: 'created_at', align: 'center' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center' },
        { text: 'ราคา', value: 'total_amount', align: 'center' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center' },
        { text: 'จ่ายเงิน', value: 'payment', align: 'center' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    }
  },
  watch: {
    DataTable (val) {
      // console.log('DataTable', val)
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.orderList.data_incomplete
        this.keyCheckHead = 0
      } else if (val === 1) {
        this.DataTable = this.orderList.pending
        this.keyCheckHead = 1
      } else if (val === 2) {
        this.DataTable = this.orderList.not_paid
        this.keyCheckHead = 2
      } else if (val === 3) {
        this.DataTable = this.orderList.success
        this.keyCheckHead = 3
      } else if (val === 4) {
        this.DataTable = this.orderList.cancel
        this.keyCheckHead = 4
      } else if (val === 5) {
        this.DataTable = this.orderList.fail
        this.keyCheckHead = 5
      }
    }
  },
  async created () {
    this.ListDataTable()
  },
  methods: {
    async ListDataTable () {
      await this.$store.dispatch('actionListOrderBuyer')
      this.orderList = await this.$store.state.ModuleOrder.stateOrderListData.data
      // console.log('orderlist', this.orderList)
      // this.ProcurementData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('ProcurementData'))))
      // this.getDataTable()
      if (this.StateStatus === 0) {
        this.DataTable = this.orderList.data_incomplete
      } else if (this.StateStatus === 1) {
        this.DataTable = this.orderList.pending
      } else if (this.StateStatus === 2) {
        this.DataTable = this.orderList.not_paid
      } else if (this.StateStatus === 3) {
        this.DataTable = this.orderList.success
      } else if (this.StateStatus === 4) {
        this.DataTable = this.orderList.cancel
      } else if (this.StateStatus === 5) {
        this.DataTable = this.orderList.fail
      }
    },
    async UpdateStatusBuyer (item) {
      // console.log(item)
      if (item.seller_sent_status === 'not_sent') {
        this.$swal.fire({
          icon: 'warning',
          text: 'สินค้ากำลังจัดส่ง',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      } else {
        const update = {
          order_number: item.order_number,
          buyer_received_status: item.buyer_received_status
        }
        // console.log('update', update)
        await this.$store.dispatch('actionUpdateStatusBuyer', update)
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      }
    },
    async GoToPayment (item) {
      const PaymentID = {
        payment_transaction_number: item.payment_transaction_number
      }
      // console.log('payment_transaction_number', PaymentID)
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      // console.log('respose paymenttttttttt', response)
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    async orderDetail (val) {
      // console.log('path to not tell =====>', val.pdf_for_buyer)
      window.open(`${val.pdf_for_buyer}`)
      // var data = {
      //   payment_transaction_number: val.payment_transaction_number
      // }
      //   console.log('data naja', data)
      // await this.$store.dispatch('actionOrderDetail', data)
      // await this.$router.push({ path: '/quotation1shop' }).catch(() => {})
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('order detail na', response)
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      //   console.log('กี่ร้านนะะะะะะะะะ', response.data[0].order_number[0])
      // }
    },
    SelectDetailOrder (item) {
      this.StateStatus = item
    },
    // async getDataTable () {
    //   this.overlay = true
    //   const data = {
    //     procurement_org_id: this.ProcurementData.procurement_org_id
    //   }
    //   await this.$store.dispatch('actionListOrderProcurement', data)
    //   var response = await this.$store.state.ModuleCart.stateListOrderProcurement
    //   // console.log('response List Order Procurement', response)
    //   if (response.result === 'SUCCESS') {
    //     this.responseData = response.data
    //     this.overlay = false
    //   }
    // },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateDetailOrder
      // console.log('response detail order', response)
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem('MyOrderDetail', Encode.encode(JSON.stringify(response.data)))
        this.$router.push('/myorderdetail')
      }
    }
  }
}
</script>
