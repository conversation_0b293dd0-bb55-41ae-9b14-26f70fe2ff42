<template>
  <v-container grid-list-xs rounded :class="MobileSize ? 'background_color_Mobile' : 'background_color'">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row justify="start" class="pt-3 pb-3 px-3" v-if="MobileSize">
        <span class="pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
          <v-icon style="color: #27ab9c;" @click="backToHome()">mdi-chevron-left</v-icon> แก้ไขรายการแฟลชเซลล์
        </span>
      </v-row>
      <v-row justify="space-between" class="pt-3 px-3" v-else>
        <span class="pl-4 pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
          <v-icon style="color: #27ab9c;" @click="backToHome()">mdi-chevron-left</v-icon> แก้ไขรายการแฟลชเซลล์
        </span>
      </v-row>
      <v-form ref="FormProductFlash">
        <v-row class="ma-1" v-if="dataList.length > 0 && dataList[0].image_path !== ''">
          <v-col cols="12">
            <v-icon>mdi-menu-right</v-icon>
            <span>ภาพแบนเนอร์</span>
          </v-col>
          <v-col cols="12" style="position: relative; margin-top: -1vw;">
            <v-card
              class="mt-3 pa-3"
              elevation="0"
              :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px;'"
              height="400px%"
              @click="onPickFile()"
            >
              <v-img :src="dataList[0].image_path" style="width: 100%; height: auto;"></v-img>
              <v-btn
                text
                small
                fab
                style="position: absolute; top: 5px; right: 5px; z-index: 1; background-color: #f44336;"
                @click="removeImage"
                >
                <v-icon small color="#fff">mdi-delete</v-icon>
              </v-btn>
            </v-card>
          </v-col>
        </v-row>
        <v-row class="ma-1" v-else>
          <v-col cols="12" md="12">
            <v-icon>mdi-menu-right</v-icon>
            <span>เพิ่มรูปภาพแบนเนอร์สำหรับแฟลชเซลล์ </span>
            <v-card
              class="mt-3"
              elevation="0"
              :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px;'"
              height="400px%"
              @click="onPickFile()"
            >
              <v-card-text >
                <v-row dense align="center" justify="center" style="cursor: pointer;">
                  <v-file-input
                    v-model="DataImage"
                    accept="image/jpeg, image/jpg, image/png"
                    @change="onFileSelected"
                    id="file_input"
                    multiple :clearable="false"
                    style="display:none"
                  >
                  </v-file-input>
                  <v-col cols="12" md="12">
                    <v-row justify="center" align="center">
                      <v-col cols="12" md="12" align="center">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                          width="280.34"
                          height="154.87"
                          contain
                        ></v-img>
                      </v-col>
                      <v-col cols="12" md="12" style="text-align: center;">
                        <span style="line-height: 24px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                        <span style="line-height: 24px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                        <span style="line-height: 16px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <v-row class="ma-1">
          <v-col class="d-flex align-center">
            <v-icon>mdi-menu-right</v-icon>
            <span>รายการสินค้า</span>
          </v-col>
          <!-- <v-col cols="6" class="d-flex justify-end">
            <v-btn color="#2bad9e">
              <v-icon color="white">mdi-plus</v-icon>
              <span style="color: #fff;">เพิ่มรายการสินค้า</span>
            </v-btn>
          </v-col> -->
          <v-col cols="12" style="margin-top: -2vw">
            <!-- <span>สินค้าที่เข้าร่วม <span style="color:#F5222D">*</span></span> -->
            <!-- <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
              <v-select
                v-model="mockVSelectData"
                :items="sellerProductList"
                item-text="product_name"
                item-value="product_id"
                multiple
                return-object
                clearable
                chips
                dense
                outlined
              >
                <template v-slot:item="{ item }">
                  <div>
                    <span>{{ item.product_name }}</span> -->
                    <!-- แสดง v-chip -->
                    <!-- <span v-if="item.attributes && item.attributes.length">
                      <v-chip-group
                        multiple
                      >
                        <v-chip
                          v-for="attribute in item.attributes"
                          :key="attribute.product_attribute_id"
                          class="ma-2"
                          @click="selectAttribute(attribute, item)"
                          outlined
                        >
                          {{ attribute.attribute_name }}
                          <v-btn icon small>
                            <v-icon small>mdi-close</v-icon>
                          </v-btn>
                        </v-chip>
                      </v-chip-group>
                    </span>
                  </div>
                </template>

                <template v-slot:selection="{ item }">
                  <v-chip v-if="item">
                    <span>{{ item.name }}</span>
                    <span v-if="selectedAttribute" class="grey--text text-caption">
                      ({{ selectedAttribute.attribute_name }})
                    </span>
                  </v-chip>
                  <v-btn
                    v-if="selectProduct"
                    @click="clearSelect"
                    small
                    text
                  >
                  </v-btn>
                </template>
              </v-select>
            </div> -->
            <!-- <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
              <treeselect
                v-model="itemFlashSale"
                :multiple="true"
                :options="sellerProductList"
                placeholder="ทดสอบ ..."
                :normalizer="normalizer2"
                @input="updateFlash()"
                valueFormat="object"
                openDirection="top"
                />
            </div> -->
            <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
              <treeselect
                v-model="itemFlashSale"
                :multiple="true"
                :options="dataItem"
                placeholder="เลือกสินค้าที่เข้าร่วม..."
                :normalizer="normalizer"
                valueFormat="object"
                @input="updateFlash()"
                :disable-branch-nodes="true"
                openDirection="top"
                />
            </div>
          </v-col>
          <v-col cols="12">
            <v-data-table
              dense
              :headers="headers"
              :items="itemFlashSalePhase2"
              item-key="id"
              class="elevation-1"
              :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
              style="text-align: center; white-space: nowrap;"
            >
              <!-- คอลัมน์ลำดับ -->
              <template v-slot:[`item.number`]="{ item }">
                <span>{{ itemFlashSalePhase2.indexOf(item) + 1 }}</span>
              </template>

              <!-- คอลัมน์ชื่อสินค้า -->
              <template v-slot:[`item.product_name`]="{ item }">
                <span>{{ item.product_name|truncate(26) }} {{item.have_attribute === 'no' ? '' : '(' + item.attribute_priority_1 + ')'}}</span>
              </template>

              <!-- คอลัมน์รูปภาพสินค้า -->
              <template v-slot:[`item.images_URL`]="{ item }">
                <v-card class="pa-1" width="100px" height="100px" elevation="0" contain>
                  <v-img
                    width="100px"
                    height="90px"
                    v-if="item.images_URL"
                    :src="item.have_attribute === 'no' ? item.images_URL : item.color_image_path"
                    contain
                  ></v-img>
                  <v-img width="100px" v-else src="@/assets/NoImage.png" contain></v-img>
                </v-card>
              </template>

              <!-- คอลัมน์ราคาตั้งต้น -->
              <template v-slot:[`item.fake_price`]="{ item }">
                <span>{{ item.fake_price ? item.fake_price.toLocaleString(undefined, { minimumFractionDigits: 2 }) : 'N/A' }}</span>
              </template>

              <!-- คอลัมน์ราคาขาย -->
              <template v-slot:[`item.real_price`]="{ item }">
                <span>{{ item.real_price ? item.real_price.toLocaleString(undefined, { minimumFractionDigits: 2 }) : 'N/A' }}</span>
              </template>

              <!-- คอลัมน์ส่วนลด -->
              <template v-slot:[`item.discount_percent`]="{ item }">
                <span>{{ item.discount_percent }}</span>
              </template>

              <!-- คอลัมน์ราคาแฟลชเซลล์ใหม่ -->
              <template v-slot:[`item.flashsale_price`]="{ item }">
                <v-text-field
                  v-model="item.flashsale_price"
                  placeholder="ระบุราคาแฟลชเซลล์"
                  outlined
                  dense
                  oninput="this.value = this.value.replace(/^[.]/, '').replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                  @change="realPriceNoGood(item, itemFlashSalePhase2.indexOf(item))"
                  :rules="rule.amountVoucherWhenEdit(item.flashsale_price, item.real_price)"
                ></v-text-field>
              </template>

              <!-- คอลัมน์ลบสินค้า -->
              <template v-slot:[`item.action`]="{ item, index }">
                <v-btn
                  x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="DeleteFromTable(item, index)">
                  <v-icon color="#A1A1A1" small>mdi-delete-outline</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </v-form>
    </v-card>
    <v-row justify="end" class="px-5 my-4">
      <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="backToHome()" class="mr-2">ยกเลิก</v-btn>
      <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="itemFlashSalePhase2.length === 0" @click="UpdateFlashSales()">บันทึก</v-btn>
    </v-row>
  </v-container>
</template>

<script>
import { msgErr, statusErr } from '@/enum/GetError'
import Treeselect from '@riophae/vue-treeselect'
import { Decode } from '@/services'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  props: ['itemProduct', 'pageCheck'],
  components: {
    Treeselect
  },
  filters: {
    truncate: function (value, limit) {
      if (!value) return ''
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      dataList: [],
      orderList: [],
      productList: [],
      headers: [
        { text: 'ลำดับที่', value: 'number', sortable: false },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false },
        { text: 'รูปภาพสินค้า', value: 'images_URL', sortable: false },
        { text: 'ราคาตั้งต้น', value: 'fake_price', sortable: false },
        { text: 'ราคาขาย', value: 'real_price', sortable: false },
        { text: 'ส่วนลด (%)', value: 'discount_percent', sortable: false },
        { text: 'ราคาแฟลชเซลล์', value: 'flashsale_price' },
        { text: 'จัดการ', value: 'action', sortable: false }
      ],
      DataImage: [],
      theRedI: true,
      base64Image: '',
      itemFlashSale: [],
      dataItem: [],
      itemFlashSalePhase2: [],
      normalizer (node) {
        let id
        let childrenKey
        let labelKey

        if (node.type === 'category') {
          id = 'hierachy'
          childrenKey = node.sub_category === null ? 'product_list' : 'sub_category'
          labelKey = 'category_name'
        } else if (node.type === 'product') {
          id = 'product_id'
          childrenKey = node.sub_product.length === 0 ? '' : 'sub_product'
          labelKey = 'product_name'
        } else if (node.type === 'product_attribute') {
          id = 'product_attribute_id'
          labelKey = 'product_attribute_name'
        } else if (node.type === 'special_1_category') {
          id = 'all_id'
          childrenKey = 'all'
          labelKey = 'all_name'
        } else {
          id = 'product_id'
          childrenKey = 'product_list'
          labelKey = 'product_attribute_name'
        }
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey]
        }
      },
      normalizer2 (node) {
        var id
        var childrenKey
        var labelKey
        if (node.have_attribute !== undefined) {
          id = 'product_id'
          childrenKey = node.have_attribute === 'no' ? '' : 'attributes'
          labelKey = 'product_name'
        } else if (node.have_attribute === undefined) {
          id = 'product_attribute_id'
          labelKey = 'attribute_name'
        }
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey]
        }
      },
      theRedP: true,
      flashsaleId: '',
      productListID: [],
      productAttributeList: [],
      FlashsTest: [],
      rule: {
        amountVoucherWhenEdit (falshSalePrice, realPrice) {
          if (falshSalePrice === '' || isNaN(parseFloat(falshSalePrice))) {
            return ['กรุณากรอกราคาแฟลชเซลล์']
          } else if (parseInt(falshSalePrice) <= parseInt(0)) {
            return ['กรุณากรอกราคาให้มากกว่า 0']
          } else {
            const falshSalePriceCheck = falshSalePrice.replace(/,/g, '')
            if (parseFloat(falshSalePriceCheck) >= parseFloat(realPrice)) {
              return ['กรุณากรอกราคาให้ต่ำกว่าราคาขาย']
            }
          }
        }
      },
      mockuptreeSelectData: [
        {
          category_id: 2,
          category_name: 'เวชภัณฑ์',
          hierachy: '1_2_3',
          seller_shop_id: 139,
          type: 'product',
          product_id: 41171,
          sub_product: [
          ]
        },
        {
          product_attribute_name: 'OOO(tagakiriba)',
          product_attribute_id: 48438,
          product_id: 41174,
          type: 'product_attribute'
        }
      ],
      testTreeSelectData: [],
      mockVSelectData: [],
      mockVSelectItem: [],
      sellerProductList: [],
      selectProduct: [],
      imagesPath: '',
      oldData: []
    }
  },
  computed: {
    checkWidth () {
      return window.screen.width
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    itemFlashSale (item, ATBItem) {
      if (item.length < ATBItem.length) {
        this.updateFlash('del', item.length)
      }
      if (item.length > ATBItem.length) {
        this.updateFlash('add', item.length)
      }
      // this.getProduct()
    },
    mockVSelectData (val) {
      this.updateTableProduct()
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageFlashSaleEditMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageFlashSaleEdit' }).catch(() => {})
      }
    }
  },
  // mounted () {
  // },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      // this.getDataCoupons()
      this.seller_shop_id = localStorage.getItem('shopSellerID')
      await this.getDataForTree()
      this.getData()
      // this.getaListAllShopProductFlashSale()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    async getProduct () {
      // var shopID = localStorage.getItem('shopSellerID')
      this.oldData = this.itemFlashSalePhase2
      var data = {
        // seller_shop_id: parseInt(shopID),
        // raw_list: this.productList
        seller_shop_id: this.seller_shop_id,
        product_list: this.productListID,
        attribute_list: this.productAttributeList
      }
      if (this.productListID.length !== 0 && this.productAttributeList !== 0) {
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsGetListProductDetail', data)
        const res = await this.$store.state.ModuleManageFlashSale.stateGetListProductDetail
        // this.itemFlashSalePhase2 = res.data
        var newData = res.data
        const ids = new Set(this.oldData.map(item1 => item1.have_attribute === 'no' ? item1.product_id : item1.attribute_id))
        newData.forEach(item2 => {
          if (!ids.has(item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id)) {
            this.oldData.push(item2)
            ids.add(item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id) // อัปเดต Set
          }
        })
        this.itemFlashSalePhase2 = await [...this.oldData]
        this.$store.commit('closeLoader')
      }
    },
    async getData () {
      this.$store.commit('openLoader')
      var shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopSellerID,
        page: 1,
        limit: 1000
      }
      await this.$store.dispatch('actionsGetFlashSale', data)
      var responseData = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
      if (responseData.result === 'SUCCESS') {
        this.dataList = responseData.data.data
        this.countItemAll = responseData.data.count_flash_sale
        this.productList = this.dataList[0].product_list
        this.imagesPath = this.dataList[0].image_path
        await this.mockStartData()
        for (var i = 0; i < this.productList.length; i++) {
          this.productListID.push(this.productList[i].id)
          this.productAttributeList.push(Number(this.productList[i].product_attribute_id))
        }

        var dataforList = {
          product_list: this.productListID,
          attribute_list: this.productAttributeList
        }
        if (this.productListID.length !== 0 && this.productAttributeList !== 0) {
          await this.$store.dispatch('actionsGetListProductDetail', dataforList)
          const res = await this.$store.state.ModuleManageFlashSale.stateGetListProductDetail
          this.itemFlashSalePhase2 = res.data
        }
        const filterStepOne = await this.itemFlashSale.map(e => { return e.product_attribute_id === undefined ? e.product_id : e.product_attribute_id })
        const filterStepTwo = Array.isArray(this.itemFlashSalePhase2)
          ? this.itemFlashSalePhase2.filter(x => {
            const id = x.attribute_id === null ? x.product_id : x.attribute_id
            return filterStepOne.includes(id)
          })
          : []
        filterStepTwo.sort((a, b) => {
          const idA = a.attribute_id === null ? a.product_id : a.attribute_id
          const idB = b.attribute_id === null ? b.product_id : b.attribute_id
          return filterStepOne.indexOf(idA) - filterStepOne.indexOf(idB)
        })
        this.itemFlashSalePhase2 = filterStepTwo
        // this.itemFlashSalePhase2 = this.dataList[0].product_list
        // for (var i = 0; i < this.productList.length; i++) {
        //   this.itemFlashSalePhase2.push({
        //     attribute: this.productList[i].product_attribute_id,
        //     product_id: this.productList[i].id,
        //     product_name: this.productList[i].name,
        //     id: this.productList[i].id,
        //     attribute_id: this.productList[i].have_attribute === 'no' ? '-1' : this.productList[i].product_attribute_id,
        //     fake_price: this.productList[i].fake_price,
        //     real_price: this.productList[i].real_price,
        //     media_path: this.productList[i].images_URL.length === 0 ? null : this.productList[i].images_URL[0]
        //   })
        // }
        this.flashsaleId = this.dataList[0].id
        // this.updateFlash('add', this.productList.length)
        // this.itemFlashSale.push()
        // Check set switchStatus
        if (this.dataList.length > 0 && this.dataList[0].status === 'active') {
          this.switchStatus = true
        } else {
          this.switchStatus = false
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (responseData.message === 'Not access this function') {
          await this.$swal.fire({
            icon: 'warning',
            text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้',
            showConfirmButton: false,
            timer: 2000
          })
          this.$router.push({ path: '/userInfo' }).catch(() => {})
        } else if (responseData.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(responseData.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(responseData.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    async getaListAllShopProductFlashSale () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListAllShopProductFlashSale', data)
      const responseData = await this.$store.state.ModuleManageFlashSale.stateListAllShopProductFlashSale
      if (responseData.result === 'SUCCESS') {
        this.sellerProductList = responseData.data
        this.$store.commit('closeLoader')
      }
      this.$store.commit('closeLoader')
    },
    async DeleteFromTable (item, index) {
      var indexX = this.itemFlashSalePhase2.indexOf(item)
      // alert(this.itemFlashSale[index].flashprice)
      // this.itemFlashSale.splice(indexX, 1)
      // this.productList.splice(indexX, 1)
      this.itemFlashSale.splice(indexX, 1)
      this.productListID = this.itemFlashSale.map(e => e.product_id)
      this.productAttributeList = this.itemFlashSale.map(e => e.have_attribute === 'no' ? '' : e.product_attribute_id)
      await this.getProduct()
      this.updateFlash('del', this.itemFlashSalePhase2.length)
    },
    async updateFlash (text, index) {
      if (text === 'add') {
        // this.itemFlashSalePhase2.push({
        //   attribute: this.itemFlashSale[index === 0 ? index : index - 1].attribute,
        //   product_id: this.itemFlashSale[index === 0 ? index : index - 1].sku,
        //   product_name: this.itemFlashSale[index === 0 ? index : index - 1].name2,
        //   id: this.itemFlashSale[index === 0 ? index : index - 1].product_id,
        //   attribute_id: this.itemFlashSale[index === 0 ? index : index - 1].have_attribute === 'no' ? '' : this.itemFlashSale[index === 0 ? index : index - 1].toString(),
        //   fake_price: this.itemFlashSale[index === 0 ? index : index - 1].fake_price,
        //   real_price: this.itemFlashSale[index === 0 ? index : index - 1].real_price,
        //   media_path: this.itemFlashSale[index === 0 ? index : index - 1].media_path
        // })
        // this.productListID.push(this.itemFlashSale[index === 0 ? index : index - 1].product_id)
        // this.productAttributeList.push(this.itemFlashSale[index === 0 ? index : index - 1].have_attribute === 'no' ? '' : this.itemFlashSale[index === 0 ? index : index - 1].product_attribute_id)
        this.productListID = this.itemFlashSale.map(e => e.product_id)
        this.productAttributeList = this.itemFlashSale.map(e => e.have_attribute === 'no' ? '' : e.product_attribute_id)
        await this.getProduct()
        const filterStepOne = await this.itemFlashSale.map(e => { return e.product_attribute_id === undefined ? e.product_id : e.product_attribute_id })
        const filterStepTwo = Array.isArray(this.itemFlashSalePhase2)
          ? this.itemFlashSalePhase2.filter(x => {
            const id = x.attribute_id === null ? x.product_id : x.attribute_id
            return filterStepOne.includes(id)
          })
          : []
        filterStepTwo.sort((a, b) => {
          const idA = a.attribute_id === null ? a.product_id : a.attribute_id
          const idB = b.attribute_id === null ? b.product_id : b.attribute_id
          return filterStepOne.indexOf(idA) - filterStepOne.indexOf(idB)
        })
        this.oldData = this.itemFlashSalePhase2
        this.oldData.sort((a, b) => {
          const idA = a.attribute_id === null ? a.product_id : a.attribute_id
          const idB = b.attribute_id === null ? b.product_id : b.attribute_id
          return filterStepOne.indexOf(idA) - filterStepOne.indexOf(idB)
        })
        // filterStepTwo.forEach(item2 => {
        //   const exists = this.oldData.some(item1 => item1.have_attribute === 'no' ? item1.product_id : item1.attribute_id === item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id)
        //   if (!exists) {
        //     this.oldData.push(item2)
        //   }
        // })
        // const ids = new Set(this.oldData.map(item1 => item1.have_attribute === 'no' ? item1.product_id : item1.attribute_id))
        // filterStepTwo.forEach(item2 => {
        //   if (!ids.has(item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id)) {
        //     this.oldData.push(item2)
        //     ids.add(item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id) // อัปเดต Set
        //   }
        // })
        // this.itemFlashSalePhase2 = await [...this.oldData]
        this.theRedP = true
        // this.productListID.push(this.mockVSelectData[index === 0 ? index : index - 1].product_id)
      }
      if (text === 'del') {
        const filterStepOne = await this.itemFlashSale.map(e => { return e.product_attribute_id === undefined ? e.product_id : e.product_attribute_id })
        const filterStepTwo = Array.isArray(this.itemFlashSalePhase2)
          ? this.itemFlashSalePhase2.filter(x => {
            const id = x.attribute_id === null ? x.product_id : x.attribute_id
            return filterStepOne.includes(id)
          })
          : []
        filterStepTwo.sort((a, b) => {
          const idA = a.attribute_id === null ? a.product_id : a.attribute_id
          const idB = b.attribute_id === null ? b.product_id : b.attribute_id
          return filterStepOne.indexOf(idA) - filterStepOne.indexOf(idB)
        })
        this.itemFlashSalePhase2 = await filterStepTwo
        // this.itemFlashSale = filterStepTwo
        // this.productListID = this.itemFlashSale.map(e => e.product_id)
        // this.productAttributeList = this.itemFlashSale.map(e => e.have_attribute === 'no' ? '' : e.product_attribute_id)
        // this.getProduct()
      }
    },
    // },
    // async updateFlashSale () {
    //   this.$store.commit('openLoader')
    //   await this.$store.dispatch('actionsUpdateFlashSale')
    //   var response = await this.$store.state.ModuleAdminManage.stateUpdateFlashSale
    //   if (response.code === 200) {}
    // },
    backToHome () {
      // var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/manageFlashSaleMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageFlashSale' })
      }
    },
    async onFileSelected (files) {
      if (files && files.length > 0) {
        const file = files[0] // รับไฟล์แรก
        if (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png') {
          const url = URL.createObjectURL(file) // สร้าง URL ชั่วคราวสำหรับไฟล์
          const image = new Image()
          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => {
              const dimensions = { height: image.height, width: image.width }
              resolve(dimensions)
            }
            image.src = url
          })

          // ตรวจสอบขนาดภาพ
          if (imageDimensions.height <= 620 && imageDimensions.width <= 1480) {
            const reader = new FileReader()
            reader.readAsDataURL(file)
            reader.onload = async () => {
              var resultReader = reader.result
              this.base64Image = resultReader.split(',')[1]
              this.dataList[0].image_path = `data:image/jpeg;base64,${this.base64Image}`
              const DataImage = {
                image: [this.base64Image],
                type: '',
                seller_shop_id: this.seller_shop_id
              }
              this.$store.commit('openLoader')
              await this.$store.dispatch('actionsUploadToS3', DataImage)
              const response = await this.$store.state.ModuleShop.stateUploadToS3

              if (response.message === 'List Success.') {
                this.imagesPath = response.data.list_path[0].path
              }
              this.$store.commit('closeLoader')
              // this.$swal.fire({ icon: 'success', text: 'อัปโหลดภาพสำเร็จ', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'โปรดใช้รูปตามขนาดที่กำหนด', showConfirmButton: false, timer: 1500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)', showConfirmButton: false, timer: 2500 })
        }
      } else {
        console.warn('ไม่มีไฟล์ที่เลือก')
      }
    },
    async getDataForTree () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionscategoryShopList', data)
      var res = await this.$store.state.ModuleManageCoupon.stateCategoryShopList
      if (res.result === 'Success') {
        this.dataItem = res.data
      } else {
        if (res.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(res.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(res.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    removeImage () {
      if (this.dataList.length > 0) {
        this.dataList[0].image_path = ''
      }
      this.DataImage = null
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    async UpdateFlashSales () {
      if (this.$refs.FormProductFlash.validate(true)) {
        this.itemFlashSalefinalPhase = this.itemFlashSalePhase2.map(e => {
          if (e.flashsale_price !== '' && e.flashsale_price !== null && e.flashsale_price !== undefined) {
            return {
              product_attribute_id: e.attribute_id === undefined || e.attribute_id === null ? '-1' : e.attribute_id.toString(),
              product_id: e.product_id.toString(),
              flashsale_price: e.flashsale_price.replace(/,/g, '')
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มราคาสินค้าในอีกหน้าให้ครบถ้วน', showConfirmButton: false, timer: 2000 })
            this.itemFlashSalefinalPhase = ''
          }
        })
        if (this.imagesPath === '' || this.imagesPath === null || this.imagesPath === undefined) {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ', showConfirmButton: false, timer: 2000 })
          this.theRedI = false
        } else if (this.itemFlashSalePhase2.length === 0) {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มสินค้าแฟลชเซลล์', showConfirmButton: false, timer: 2000 })
          this.theRedP = false
        }
        // var data = {
        //   seller_shop_id: this.seller_shop_id,
        //   image_path: this.FlashsTest[0].image_data,
        //   // start_date: this.create_date,
        //   // end_date: this.create_date1,
        //   start_date: '2024-07-15',
        //   end_date: '2024-07-15',
        //   countdown_date: this.create_date2,
        //   status: 'active',
        //   product_list: this.itemFlashSalefinalPhase
        // }
        // await this.$store.dispatch('actionsCreateFlashSale', data)
        // const res = await this.$store.state.ModuleManageFlashSale.stateCreateFlashSale
        var data = {
          flashsale_id: this.flashsaleId,
          image_path: this.imagesPath,
          status: 'active',
          start_date: '2024-06-15',
          end_date: '2024-06-15',
          countdown_date: '',
          product_list: this.itemFlashSalefinalPhase
        }
        await this.$store.dispatch('actionsUpdateFlashSale', data)
        var res = await this.$store.state.ModuleManageFlashSale.stateUpdateFlashSale
        if (res.result === 'SUCCESS') {
          this.$swal.fire({ icon: 'success', text: `${res.message}`, showConfirmButton: false, timer: 2000 })
          if (!this.MobileSize) {
            this.$router.push({ path: '/manageFlashSale' })
          } else {
            this.$router.push({ path: '/manageFlashSaleMobile' })
          }
          // this.dialogSuccess = true
          // this.$EventBus.$emit('getNewDataFlashSaleList')
          // setTimeout(() => {
          //   this.dialogSuccess = false
          //   if (!this.MobileSize) {
          //     this.$router.push({ path: '/manageFlashSale' })
          //   } else {
          //     this.$router.push({ path: '/manageFlashSaleMobile' })
          //   }
          // }, 2000)
        } else if (res.result === 'FAILED') {
          this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 2000 })
        } else {
          if (res.message === 'Not access this function') {
            await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
            this.$router.push({ path: '/userInfo' }).catch(() => {})
          } else if (res.result === 'FAILED') {
            if (res.message === 'Have flashsale in this start_date') {
              this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในวันที่เริ่มต้นนี้แล้ว', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'Have flashsale in this end_date') {
              this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในวันที่สิ้นสุดนี้แล้ว', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'Have flashsale in this start_date and end_date') {
              this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในช่วงเวลานี้แล้ว', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'end_date not over start_date') {
              this.$swal.fire({ icon: 'error', text: 'วันที่สิ้นสุดต้องไม่น้อยกว่าวันที่เริ่มต้น', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'countdown_date not over start_date') {
              this.$swal.fire({ icon: 'error', text: 'ตัวนับเวลาถอยหลังต้องไม่น้อยกว่าวันที่เริ่มต้น', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'start_date not over countdown_date') {
              this.$swal.fire({ icon: 'error', text: 'วันที่เริ่มต้น มากกว่าตัวนับเวลาถอยหลังก่อนเริ่มแฟลชเซลล์ไม่ได้', showConfirmButton: false, timer: 2000 })
            }
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณากรอกข้อมูลแฟลชเซลล์ให้ครบถ้วน', showConfirmButton: false, timer: 2000 })
        if (this.itemFlashSalePhase2.length === 0) {
          this.theRedP = false
        }
        this.theRedI = false
      }
    },
    closeUpdateFlashSales () {

    },
    realPriceNoGood (item, index) {
      this.realPriceForSure = ''
      if (item.real_price) {
        this.realPriceForSure = item.real_price
      }
      if (item.flashsale_price === 'NaN' || item.flashsale_price === '') {
        this.itemFlashSalePhase2[index].flashsale_price = ''
      } else {
        this.itemFlashSalePhase2[index].flashsale_price = this.itemFlashSalePhase2[index].flashsale_price.replace(/,/g, '')
        this.itemFlashSalePhase2[index].flashsale_price = Number(this.itemFlashSalePhase2[index].flashsale_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }
      this.oldData = this.itemFlashSalePhase2
    },
    // editPreDataTreeSelect () {
    //   if ()
    //   this.testTreeSelectData
    // }
    updateTableProduct () {
      var listIDProduct = []
      var listAttributeProduct = []
      for (var i = 0; i < this.mockVSelectData.length; i++) {
        listIDProduct.push(this.mockVSelectData[i].product_id)
        listAttributeProduct.push(Number(this.mockVSelectData[i].product_attribute_id))
      }
      this.productListID = listIDProduct
      this.productAttributeList = listAttributeProduct
      this.getProduct()
    },
    selectAttribute (attribute, item) {
      this.selectedAttribute = attribute
      this.productListID.push(item.product_id)
      this.productAttributeList.push(attribute.product_attribute_id)
      this.getProduct()
      // this.selectProduct
      // this.selectProduct = {
      //   id: item.id,
      //   name: item.name,
      //   selectedAttribute: attribute
      // }
    },
    clearSelect () {
      // เคลียร์ selectProduct และ selectedAttribute
      this.selectProduct = null
      this.selectedAttribute = null
    },
    async convertImageUrlToBase64 (imageUrl) {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
        reader.readAsDataURL(blob)
      })
    },
    async mockStartData () {
      var mockData = this.productList.map(e => {
        if (e.have_attribute === 'yes') {
          return {
            product_attribute_name: e.attribute_priority_1,
            product_attribute_id: e.product_attribute_id,
            product_id: e.id,
            type: 'product_attribute'
          }
        } else if (e.have_attribute === 'no') {
          return {
            product_id: e.id,
            type: 'product',
            product_name: e.name,
            sub_product: []
          }
        }
      })
      this.itemFlashSale = mockData
    }
  }
}
</script>
