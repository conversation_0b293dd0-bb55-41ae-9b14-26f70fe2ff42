<template>
  <v-container>
    <!-- Start List Customer Sales  -->
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="border-radius: 8px;">
      <v-card-text class="px-0">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-0' : 'px-0 py-0'">
          <v-row :class="MobileSize ? 'mx-0' : ''">
            <v-card-title v-if="MobileSize" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'" style="font-weight: bold; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายชื่อลูกค้า</v-card-title>
            <v-card-title v-else style="font-size: 24px;font-weight: bold; line-height: 32px; color: #333333;">รายชื่อลูกค้า</v-card-title>
          </v-row>
        </v-col>
        <!-- Tab list Customer -->
        <v-col cols="12">
          <v-row dense>
            <v-tabs
              v-model="tab"
              background-color="transparent"
              @change="changeTab(tab)"
            >
              <v-tab
               v-for="item in itemTab"
               :key="item"
              >
                {{ item }}
              </v-tab>
            </v-tabs>
          </v-row>
        </v-col>
        <v-col cols="12" md="4" sm="6" class="mt-3 ">
          <v-text-field v-model="searchCustomer" outlined style="border-radius: 8px;" dense :placeholder="tab === 0 ? 'ค้นหาจากรายชื่อลูกค้าหรือรหัส sale' : tab === 1 ?'ค้นหาจากรายชื่อบริษัทหรือรหัส sale': 'ค้นหาจากรายชื่อ vendor หรือรหัส sale'" hide-details></v-text-field>
        </v-col>
        <v-col cols="12" class="mb-2">
          <v-row dense>
            <v-col cols="12" md="6" sm="6" align="start" class="pt-2">
              <span v-if="tab === 0" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อลูกค้าบุคคลธรรมดาทั้งหมด {{ showCountOrder }} คน</span>
              <span v-if="tab === 1" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อลูกค้าบริษัท นิติบุคคลทั้งหมด {{ showCountOrder }} คน</span>
              <span v-if="tab === 2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อ Vendor ทั้งหมด {{ showCountOrder }} คน</span>
            </v-col>
            <v-col cols="12" md="6" sm="6" align="end" v-if="SaleCanAddCus === 'Y'">
              <v-btn color="primary" @click="addCustomer()" rounded height="40">เพิ่มข้อมูลลูกค้า</v-btn>
            </v-col>
          </v-row>
        </v-col>
        <!-- Table List Customer -->
        <v-row dense justify="center">
          <v-card width="100%" elevation="0" outlined>
            <v-data-table
              :headers="tab === 0 ? headerListGeneralCustomer : tab === 1 ? headerListBusinessCustomer : headerListVendorCustomer"
              :items="itemsCustomer"
              :items-per-page="10"
              :search="searchCustomer"
              :footer-props="{'items-per-page-text': 'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
              no-results-text="ไม่พบรายชื่อลูกค้าในตาราง"
              no-data-text="ไม่มีรายชื่อลูกค้าในตาราง"
              @pagination="countCustomer"
              :style="IpadSize ? 'max-width: 100%; overflow: hidden !important;' : ''"
            >
              <template v-slot:[`item.tier_id`]="{ item }">
                <span>{{item.tier_name}}</span>
              </template>
              <template v-slot:[`item.details`]="{ item }">
                <!-- <v-row dense class="d-flex justify-center">
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="primary"
                        icon
                        outlined
                        height="40"
                        width="40"
                        class="mr-2"
                        v-bind="attrs"
                        v-on="on"
                        style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                        @click="openDialogDetailCustomer(item)"
                      >
                        <v-icon color="primary" size="24">mdi-file-document-outline</v-icon>
                      </v-btn>
                    </template>
                    <span>รายละเอียดลูกค้า</span>
                  </v-tooltip>
                  <v-tooltip top v-if="SaleCanAddCus === 'Y'">
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="primary"
                        icon
                        outlined
                        height="40"
                        width="40"
                        v-bind="attrs"
                        v-on="on"
                        style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                        @click="editDialogCustomer(item)"
                      >
                        <v-icon color="primary" size="24">mdi-pencil</v-icon>
                      </v-btn>
                    </template>
                    <span>แก้ไขข้อมูลลูกค้า</span>
                  </v-tooltip>
                  <v-tooltip top v-if="SaleCanAddCus === 'Y'">
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="primary"
                        icon
                        outlined
                        height="40"
                        width="40"
                        class="ml-2"
                        v-bind="attrs"
                        v-on="on"
                        style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                        @click="confirmDeleteCustomer(item)"
                      >
                        <v-icon color="primary" size="24">mdi-delete-outline</v-icon>
                      </v-btn>
                    </template>
                    <span>ลบข้อมูลลูกค้า</span>
                  </v-tooltip>
                </v-row> -->
                <v-menu offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      v-on="on"
                      class="pt-4 pb-4"
                      x-small
                      outlined
                      style="
                        max-width: 32px;
                        max-height: 32px;
                        border-radius: 4px;
                        border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                        background: var(--neutral-ffffff, #fff);
                        box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                      "
                    >
                      <!-- <b>รายละเอียด</b> -->
                      <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="(items, index) in actionsItem"
                      :key="index"
                      link
                    >
                      <v-list-item-content
                        @click="gotoActions(item, items.value)"
                      >
                        <v-list-item-title>{{ items.text }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month:
                'long', day: 'numeric' })}}
              </template>
            </v-data-table>
          </v-card>
        </v-row>
      </v-card-text>
    </v-card>
      <v-dialog v-model="dialogDetailCustomer" width="600" persistent>
        <v-card style="border-radius: 24px; background-color: #27AB9C;">
          <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
            <!-- <v-col cols="12"> -->
              <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">รายละเอียดลูกค้า
              </span>
              <v-btn fab small @click="dialogDetailCustomer = !dialogDetailCustomer" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            <!-- </v-col> -->
          </v-toolbar>
          <v-card class="pa-4 px-8">
            <v-row no-gutters>
              <v-col cols="4" align="center">
                <v-icon size="80" :color="customerData.status === 'active'? '#27AB9C' : '#F55A5A'">mdi-account-circle</v-icon>
              </v-col>
              <v-col cols="8">
                  <span>ชื่อ - นามสกุล : </span><span>{{customerData.cus_name ? customerData.cus_name: '-'}}</span><br>
                  <span>รหัสลูกค้า : </span><span>{{customerData.cus_code ? customerData.cus_code: '-'}}</span><br>
                  <span>กลุ่มลูกค้า : </span><span>{{customerData.tier_name ? customerData.tier_name: '-'}}</span><br>
                  <span>ระดับกลุ่มลูกค้า : </span><span>{{customerData.tier_level}}</span><br>
                  <span>หมายเหตุ : </span><span>{{customerData.remark ? customerData.remark: '-'}}</span><br>
                  <span>status : </span><span :style="customerData.status === 'active'? 'color: #27AB9C;':'color: red;'"> <v-icon  :color="customerData.status === 'active'? '#27AB9C':'#F55A5A'" class="ml-n2">mdi-circle-small</v-icon>{{customerData.status ? customerData.status: '-'}}</span><br>
              </v-col>
            </v-row>
            <v-col cols="12" md="12" align="center" v-if="!MobileSize">
              <v-btn class="mr-2" color="primary" @click="openListAddress('')" rounded height="40">รายละเอียดที่อยู่จัดส่งสินค้า</v-btn>
              <v-btn class="ml-2" color="primary" @click="openListInvoice('')" rounded height="40">รายละเอียดที่อยู่ใบกำกับภาษี</v-btn>
            </v-col>
            <v-col cols="12" align="center" v-else>
              <v-btn color="primary" @click="openListAddress('')" rounded height="40">รายละเอียดที่อยู่จัดส่งสินค้า</v-btn>
              <v-btn class="mt-2" color="primary" @click="openListInvoice('')" rounded height="40">รายละเอียดที่อยู่ใบกำกับภาษี</v-btn>
            </v-col>
          </v-card>
        </v-card>
      </v-dialog>
      <v-dialog v-model="dialogListEditCustomer" width="650" persistent>
          <v-card style="border-radius: 24px; background-color: #27AB9C;">
            <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
              <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">เลือกข้อมูลการแก้ไข
              </span>
              <v-btn icon dark @click="dialogListEditCustomer = false">
                <v-icon color="#FFFFFF">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card class="pa-4" :height="!MobileSize? '400':'250'">
              <v-col cols="12">
                <v-row no-gutters>
                  <v-col cols="4">
                    <v-card :class="!MobileSize? 'cardChooseType':'cardChooseTypeMobile'" @click="editDataCustomer('')">
                      <v-icon class="colorChange" :size="!MobileSize? '100': '80'">mdi-account-edit</v-icon>
                      <h2 style="color:#27AB9C; font-weight: 600;" :class="!MobileSize? 'pt-8':'pt-2'">แก้ไข</h2>
                      <span :style="!MobileSize? 'font-size: 16px;':'font-size: 10px;'">ข้อมูลลูกค้า</span>
                    </v-card>
                  </v-col>
                  <v-col cols="4" class="pl-2">
                    <v-card :class="!MobileSize? 'cardChooseType':'cardChooseTypeMobile'" @click="openListAddress('edit')">
                      <v-icon class="colorChange" :size="!MobileSize? '100': '80'">mdi-home-edit</v-icon>
                      <h2 style="color:#27AB9C; font-weight: 600;" :class="!MobileSize? 'pt-8':'pt-2'">แก้ไข</h2>
                      <span :style="!MobileSize? 'font-size: 16px;':'font-size: 10px;'" >ที่อยู่ในการจัดส่งลูกค้า</span>
                    </v-card>
                  </v-col>
                  <v-col cols="4" class="pl-2">
                    <v-card :class="!MobileSize? 'cardChooseType':'cardChooseTypeMobile'" @click="openListInvoice('edit')">
                      <v-icon class="colorChange" :size="!MobileSize? '100': '80'">mdi-file-document-edit</v-icon>
                      <h2 style="color:#27AB9C; font-weight: 600;" :class="!MobileSize? 'pt-8':'pt-2'">แก้ไข</h2>
                      <span :style="!MobileSize? 'font-size: 16px;':'font-size: 10px;'" >ที่อยู่ใบกำกับภาษีลูกค้า</span>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-card>
          </v-card>
      </v-dialog>
      <v-dialog v-model="dialogListAddressCustomer" width="650" persistent>
          <v-card style="border-radius: 24px; background-color: #27AB9C;">
            <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
              <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">เลือก{{titleList}}
              </span>
              <v-btn icon dark @click="close()">
                <v-icon color="#FFFFFF">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card class="pa-4">
              <v-col cols="12" class="pt-2">
                <v-row dense class="mt-0" vt-if="userdetail.length !== 0">
                  <v-col cols="12" v-if="userdetail.length !== 0">
                    <span style="font-weight: 700;" :style="!MobileSize? 'font-size:18px;': 'font-size:14px'">รายการที่อยู่ทั้งหมด {{totalAddress}} รายการ</span>
                  </v-col>
                  <v-col cols="12" v-else>
                    <span style="font-size:18px;">ยังไม่มีที่อยู่ใบกำกับภาษี</span>
                  </v-col>
                  <v-col cols="12" class="pt-2" v-for="(item, index) in userdetail" :key="index">
                    <v-card class="addAddress" @click="setDefaultAdress(item)" min-height="149" elevation="0" outlined style="border-radius: 8px;" :style="item.default_address === 'Y' ? 'border-color: #27AB9C' : 'border-color: #C4C4C4'">
                    <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
                      <v-row class="pa-4">
                        <v-col cols="12" md="12">
                          <v-row dense>
                            <!-- action -->
                            <v-col cols="8" md="8" class="d-flex">
                              <div class="mr-auto" >
                                <span style="font-weight: 700; font-size: 18px;">{{ item.name }}<span class="px-1" style="font-weight: 500;">| {{item.phone}}</span> </span>
                              </div>
                            </v-col>
                            <v-row no-gutters v-if="SaleCanAddCus === 'Y'" :justify="!MobileSize? 'end':''">
                              <v-col v-if="titleList === 'ที่อยู่จัดส่งสินค้า'" cols="4" md="4" align="end"><v-btn rounded outlined color="#27AB9C" @click.stop="editAddressCus('edit', item)">แก้ไข</v-btn></v-col>
                              <v-col v-else cols="4" md="4" align="end"><v-btn outlined rounded color="#27AB9C" @click.stop="editInvoiceAddressCus('edit', item)">แก้ไข</v-btn></v-col>
                            </v-row>
                            <v-col cols="12" md="12">
                              <span v-snip="3" style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.detail_address }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.zip_code }}</span>
                            </v-col>
                            <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                            <v-col cols="12" md="12">
                              <v-radio-group v-model="item.default_address">
                                <v-radio
                                  color="#27AB9C"
                                  label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                                  value="Y"
                                  style="color: #333333"
                                ></v-radio>
                              </v-radio-group>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col cols="12" align="end" v-if="SaleCanAddCus === 'Y'">
                    <v-btn color="#27AB9C" block v-if="titleList === 'ที่อยู่จัดส่งสินค้า'" class="addAddress" outlined @click="addAddressCustomer('addAddress','')"><v-icon>mdi-home-plus</v-icon> เพิ่มที่อยู่ใหม่</v-btn>
                    <v-btn color="#27AB9C" block v-else class="addAddress" outlined @click="addAddressInvoiceCustomer('addAddress','')"><v-icon>mdi-home-plus</v-icon> เพิ่มที่อยู่ใหม่</v-btn>
                  </v-col>
                  <v-col cols="12" align="end" v-if="userdetail.length !== 0">
                    <v-pagination
                      color="#27AB9C"
                      v-model="pageAddress"
                      :length="pageMaxAddress"
                      circle
                    > </v-pagination>
                    </v-col>
                </v-row>
              </v-col>
            </v-card>
          </v-card>
      </v-dialog>
      <v-dialog v-model="dialogAwaitDeleteCustomer" width="424" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/DeleteProduct.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="dialogAwaitDeleteCustomer = !dialogAwaitDeleteCustomer"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบรายชื่อลูกค้า</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบรายชื่อลูกค้า</span><br/>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitDeleteCustomer = !dialogAwaitDeleteCustomer">ยกเลิก</v-btn>
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="deleteCustomer(itemForDelete)">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <ModalManageCustomerSale ref="ModalManageCustomerSale" />
      <!-- <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" :customerSaleData="customerSaleData" /> -->
      <EditModalAddress ref="EditModalAddress"/>
      <!-- <EditModalEtaxAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" :customerSaleData="customerSaleData"  /> -->
      <EditModalEtaxAddress ref="EditModalEtaxAddress"/>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  components: {
    ModalManageCustomerSale: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/CustomerSaleModel'),
    EditModalAddress: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditAddressCustomerSale'),
    EditModalEtaxAddress: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditETAXAddressCustomerSale')
  },
  data () {
    return {
      SaleVendor: false,
      AdminSaleNoJV: '',
      dialogAwaitDeleteCustomer: false,
      itemForDelete: '',
      SaleCanAddCus: '',
      titleList: '',
      pageAddress: 1,
      cusCode: '',
      cusID: '',
      userdetail: [],
      totalAddress: 0,
      pageMaxAddress: null,
      dialogListEditCustomer: false,
      dialogListAddressCustomer: false,
      titleAddress: '',
      customerData: [],
      dialogDetailCustomer: false,
      dataOfSale: [],
      listGeneralCustomer: [],
      listVendorCustomer: [],
      listBusinessCustomer: [],
      tab: null,
      itemTab: [
        'บุคคลธรรมดา',
        'บริษัท นิติบุคคล'
        // 'vendor'
      ],
      showCountOrder: 0,
      headerListGeneralCustomer: [
        { text: 'ชื่อ - นามสกุล', value: 'cus_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสฝ่ายขาย', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่เพิ่ม', value: 'created_at', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'กลุ่มลูกค้า', value: 'tier_id', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerListVendorCustomer: [
        { text: 'ชื่อ - นามสกุล', value: 'cus_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสฝ่ายขาย', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่เพิ่ม', value: 'created_at', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'กลุ่มลูกค้า', value: 'tier_id', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerListBusinessCustomer: [
        { text: 'บริษัท', value: 'cus_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสฝ่ายขาย', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่เพิ่ม', value: 'created_at', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'กลุ่มลูกค้า', value: 'tier_id', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      actionsItem: [
        { text: 'รายละเอียดลูกค้า', value: 'detail' },
        { text: 'แก้ไขข้อมูลลูกค้า', value: 'edit' },
        { text: 'ลบข้อมูลลูกค้า', value: 'delete' },
        { text: 'คูปองลูกค้า', value: 'coupon' },
        { text: 'ประวัติรายการสั่งซื้อลูกค้า', value: 'order' }
      ],
      itemsCustomer: [],
      searchCustomer: ''
    }
  },
  mounted () {
    this.$EventBus.$on('EditAddressCustomerComplete', this.getCustomer)
    this.$EventBus.$on('EditComplete', this.openDialogDetail)
    this.$EventBus.$on('closeDialogListEditCustomer', this.awaitEdit)
    // this.$EventBus.$on('openDialogListEditCustomer', this.openListEditCustomer)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('ChackRowUser')
      this.$EventBus.$off('EditAddressCustomerComplete')
      this.$EventBus.$off('closeDialogListEditCustomer')
      // this.$EventBus.$off('openDialogListEditCustomer')
    })
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.$store.commit('openLoader')
    // window.addEventListener('storage', function (event) {
    //   if (event.key === 'oneData' && !event.newValue) {
    //     window.location.assign('/')
    //   }
    // })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    this.page = 'listCustomerSaleOrder'
    this.getCustomer()
    // if (localStorage.getItem('list_shop_detail') !== null) {
    //   var dataSale = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    //   console.log('dataSale', dataSale.can_use_function_in_shop.manage_sale_order)
    //   // ถ้าmanage_sale_order = 1  == adminSale ทำได้ทุกอย่าง
    //   if (dataSale.can_use_function_in_shop.manage_sale_order === '1') {
    //     this.SaleCanAddCus = 'Y'
    //     // await this.getDetailSale()
    //     await this.getListCustomerData()
    //     console.log('1')
    //   } else {
    //     console.log('2')
    //     this.SaleID = dataSale.sale_id
    //     await this.getDetailSale()
    //   }
    // }
    // if (localStorage.getItem('Detail_sales') !== null && (this.$router.currentRoute.query.sale_id !== undefined && this.$router.currentRoute.query.sale_id !== '')) {
    //   this.dataOfSale = JSON.parse(Decode.decode(localStorage.getItem('Detail_sales')))
    //   await this.getListCustomerOfSales()
    // } else {
    //   // this.getListCustomerOfSales()
    //   this.getListCustomerData()
    //   await this.getListSales()
    //   this.dataOfSale = this.itemSales
    //   // this.$router.push({ path: '/listSales' }).catch(() => {})
    // }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push('/listCustomerSaleOrderMobile')
      } else {
        this.$router.push('/listCustomerSaleOrder')
      }
    },
    pageAddress (val) {
      // console.log('valpageAddress', val)
      this.openDialogDetail(this.cusID)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    gotoActions (item, actions) {
      console.log('item', item)
      if (actions === 'detail') {
        this.openDialogDetailCustomer(item)
      } else if (actions === 'edit') {
        this.editDialogCustomer(item)
      } else if (actions === 'delete') {
        this.confirmDeleteCustomer(item)
      } else if (actions === 'coupon') {
        if (this.AdminSaleNoJV === 'Y') {
          if (this.MobileSize) {
            this.$router.push({ path: `/CouponSalesOrderMobile?Id=${item.cus_id}&CusType=${item.cus_type}&SaleId=${item.sale_id}&AdminSaleNoJV=Y&SaleCode=${item.sale_code}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/CouponSalesOrder?Id=${item.cus_id}&CusType=${item.cus_type}&SaleId=${item.sale_id}&AdminSaleNoJV=Y&SaleCode=${item.sale_code}` }).catch(() => {})
          }
        } else {
          if (this.MobileSize) {
            this.$router.push({ path: `/CouponSalesOrderMobile?Id=${item.id}&CusType=${item.cus_type}&SaleId=${item.sale_id}&AdminSaleNoJV=N&SaleCode=${item.sale_code}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/CouponSalesOrder?Id=${item.id}&CusType=${item.cus_type}&SaleId=${item.sale_id}&AdminSaleNoJV=N&SaleCode=${item.sale_code}` }).catch(() => {})
          }
        }
      } else if (actions === 'order') {
        if (this.AdminSaleNoJV === 'Y') {
          if (this.MobileSize) {
            this.$router.push({ path: `/ListOrderCustomerMobile?Id=${item.cus_id}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/ListOrderCustomer?Id=${item.cus_id}` }).catch(() => {})
          }
        } else {
          if (this.MobileSize) {
            this.$router.push({ path: `/ListOrderCustomerMobile?Id=${item.id}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/ListOrderCustomer?Id=${item.id}` }).catch(() => {})
          }
        }
      }
      // console.log('gotoActions', item, actions)
    },
    getCustomer () {
      // console.log('getCustomer')
      if (localStorage.getItem('list_shop_detail') !== null) {
        var dataSale = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
        // console.log('dataSale', dataSale.can_use_function_in_shop.manage_sale_order)
        // ถ้าmanage_sale_order = 1  == adminSale ทำได้ทุกอย่าง
        if (dataSale.can_use_function_in_shop.manage_sale_order === '1') {
          this.SaleCanAddCus = 'Y'
          this.AdminSaleNoJV = 'Y'
          // await this.getDetailSale()
          this.getListCustomerData()
          // console.log('1')
        } else {
          // console.log('2')
          this.AdminSaleNoJV = 'N'
          this.SaleID = dataSale.sale_id
          this.getDetailSale()
        }
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    editDialogCustomer (val) {
      console.log('editDialogCustomer', val)
      var typeCus = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      if (typeCus === 'vendor') {
        this.SaleVendor = true
      } else {
        this.SaleVendor = false
      }
      if (val.cus_id === undefined) {
        this.cusID = val.id
      } else {
        this.cusID = val.cus_id
      }
      // console.log('this.cusID', this.cusID)
      this.cusCode = val.cus_code
      this.customerData = val
      this.dataCustomer = val
      // console.log('this.dataCustomer', this.dataCustomer)
      this.dialogListEditCustomer = true
      // this.$refs.ModalManageCustomerSale.open('แก้ไขข้อมูลลูกค้า', 'listCustomerSaleOrder', val.sale_id, val)
    },
    editDataCustomer () {
      console.log('this.SaleVendor', this.SaleVendor)
      this.$refs.ModalManageCustomerSale.open('แก้ไขข้อมูลลูกค้า', 'listCustomerSaleOrder', this.dataCustomer.sale_id, this.dataCustomer, this.SaleVendor)
    },
    async openListAddress (actions) {
      // console.log('openListAddress', this.cusID)
      await this.getAddress(this.cusID, this.cusCode)
      this.titleList = 'ที่อยู่จัดส่งสินค้า'
      this.dialogListAddressCustomer = true
      this.dialogListEditCustomer = false
      if (actions !== '') {
        this.$EventBus.$emit('actionSale', actions)
      }
    },
    async openListInvoice (actions) {
      await this.getInvoice(this.cusID, this.cusCode)
      this.titleList = 'ที่อยู่ใบกำกับภาษี'
      this.dialogListAddressCustomer = true
      this.dialogListEditCustomer = false
      this.$EventBus.$emit('actionSale', actions)
    },
    async getListCustomerData () {
      // console.log('getListCustomerData')
      // this.dialogListAddressCustomer = false
      // this.dialogListEditCustomer = false
      // this.dialogDetailCustomer = false
      //   var dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      //   var cusType = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_type: 'all'
      }
      // console.log('data====>', data)
      await this.$store.dispatch('actionsGetListCustomer', data)
      const response = await this.$store.state.ModuleShop.stateGetListCustomer
      if (response.message === 'Get list customer success.') {
        // var dataGenSale = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerSale')))
        // var dataBusSale = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerBussinessSale')))
        // console.log('dataGenSale', dataGenSale)
        // console.log('response', response)
        this.$store.commit('closeLoader')
        this.listBusinessCustomer = response.data.business_customer
        this.listGeneralCustomer = response.data.general_customer
        this.listVendorCustomer = response.data.vendor_customer
        this.$EventBus.$emit('SaleCanAddCus', this.SaleCanAddCus, this.AdminSaleNoJV, this.SaleID)
        var roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
        this.itemsCustomer = []
        if (roleCustomer === 'general') {
          this.itemsCustomer = this.listGeneralCustomer
          this.tab = 0
          // this.SaleVendor = false
        } else if (roleCustomer === 'vendor') {
          this.itemsCustomer = this.listVendorCustomer
          this.tab = 2
          // this.SaleVendor = true
        } else {
          this.itemsCustomer = this.listBusinessCustomer
          this.tab = 1
          // this.SaleVendor = false
        }
        // console.log('this.itemsCustomer', this.itemsCustomer)
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
          this.$store.commit('closeLoader')
        }
      }
      // this.changeTab(0)
    },
    async getDetailSale () {
      // console.log('getDetailSale')
      this.$store.commit('openLoader')
      var role = JSON.parse(localStorage.getItem('roleUser')).role
      var data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: role === 'sale_order_no_JV' ? role : 'seller',
        sale_id: role === 'sale_order_no_JV' ? '' : this.SaleID,
        general_page: 1,
        business_page: 1,
        from: 'shop'
      }
      await this.$store.dispatch('actionsGetSaleOrder', data)
      var res = await this.$store.state.ModuleShop.stateGetSaleOrder
      // console.log('res', res)
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.SaleID = res.data[0].id
        localStorage.setItem('SaleID', this.SaleID)
        // console.log('manage_customer', res.data[0].manage_customer)
        // this.SaleCanAddCus = res.data[0].SaleCanAddCus
        this.SaleCanAddCus = res.data[0].manage_customer
        this.$EventBus.$emit('SaleCanAddCus', this.SaleCanAddCus, this.AdminSaleNoJV, this.SaleID)
        this.listBusinessCustomer = res.data[0].business_customer
        this.listGeneralCustomer = res.data[0].general_customer
        this.listVendorCustomer = res.data[0].vendor_customer
        var roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
        this.itemsCustomer = []
        if (roleCustomer === 'general') {
          this.itemsCustomer = this.listGeneralCustomer
          this.tab = 0
          // this.SaleVendor = false
        } else if (roleCustomer === 'vendor') {
          this.itemsCustomer = this.listVendorCustomer
          this.tab = 2
          // this.SaleVendor = true
        } else {
          this.itemsCustomer = this.listBusinessCustomer
          this.tab = 1
          // this.SaleVendor = false
        }
        // console.log('this.SaleCanAddCus', this.SaleCanAddCus) {
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2500 })
          // console.log('error')
        }
      }
    },
    openDialogDetailCustomer (item) {
      if (item.cus_id === undefined) {
        this.cusID = item.id
      } else {
        this.cusID = item.cus_id
      }
      this.cusCode = item.cus_code
      // console.log('customerData', item)
      this.customerData = item
      this.dialogDetailCustomer = true
    },
    addCustomer () {
      // console.log('addCustomer')
      this.$refs.ModalManageCustomerSale.open('เพิ่มข้อมูลลูกค้า', 'listCustomerSaleOrder', undefined, undefined, this.SaleVendor)
    },
    addAddressCustomer (actions, item) {
      // console.log('addAddressCustomer')
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        postcode: ''
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'listCustomerSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      // this.$EventBus.$emit('getCustomDetail', val.cus_id)
      this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
      // this.$EventBus.$emit('EditModalAddress', val, this.titleAddress, this.page, actions)
    },
    addAddressInvoiceCustomer (actions) {
      // console.log('addAddressInvoiceCustomer')
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        postcode: ''
      }
      // console.log('addAddressInvoiceCustomer========>>>>', val)
      if (this.cusType === 'general') {
        localStorage.setItem('AddAddressCustomerSale', Encode.encode(val))
      } else {
        localStorage.setItem('AddAddressCustomerBussinessSale', Encode.encode(val))
      }
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'listCustomerSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$EventBus.$emit('getCustomDetail', val.cus_id, val.seller_shop_id)
      // this.$EventBus.$emit('EditModalEtaxAddress', this.customerData)
      this.$refs.EditModalEtaxAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    openDialogDetail (id, data) {
      if (data === undefined) {
        if (this.titleList === 'ที่อยู่ใบกำกับภาษี') {
          this.getInvoice(id)
        } else {
          this.getAddress(id)
        }
      }
    },
    async getAddress (id, cusCode) {
      // ฟิก
      // var roleUserData = JSON.parse(localStorage.getItem('roleUser')).role
      // console.log('openAddress', id)
      this.$store.commit('openLoader')
      this.cusID = id
      if (cusCode !== undefined) {
        this.cusCode = cusCode
      }
      if (this.pageAddress !== 1) {
        // window.scrollTo(0, 0)
      }
      var data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.cusID,
        page: this.pageAddress === null ? 1 : this.pageAddress
      }
      localStorage.setItem('partner_id', this.cusID)
      // console.log('openAddress0002', this.cusID)
      await this.$store.dispatch('actionsGetListCustomerAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerAddress
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.userdetail = res.data.address
        this.totalAddress = res.data.total_address
        this.pageMaxAddress = parseInt(res.data.total_address / 5) === 0 ? 1 : Math.ceil(res.data.total_address / 5)
      } else {
        this.$swal.fire({ icon: 'error', text: this.getMessage(res.message), showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
        // console.log('error')
      }
      // console.log('this.userdetail', this.userdetail)
      this.dialogListPartner = false
    },
    async getInvoice (id, cusCode) {
      // ฟิก
      // var roleUserData = JSON.parse(localStorage.getItem('roleUser')).role
      // console.log('getInvoice')
      this.$store.commit('openLoader')
      this.cusID = id
      if (cusCode !== undefined) {
        this.cusCode = cusCode
      }
      if (this.pageAddress !== 1) {
        // window.scrollTo(0, 0)
      }
      var data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.cusID,
        page: this.pageAddress === null ? 1 : this.pageAddress
      }
      // console.log('getInvoice', this.cusID, data)
      await this.$store.dispatch('actionsGetListCustomerInvAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerInvAddress
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.userdetail = res.data.address
        this.totalAddress = res.data.total_address
        this.pageMaxAddress = parseInt(res.data.total_address / 5) === 0 ? 1 : Math.ceil(res.data.total_address / 5)
        // this.dialogListAddressCustomer = true
      } else {
        this.dialogListAddressCustomer = false
        this.$swal.fire({ icon: 'error', text: this.getMessage(res.message), showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
        // console.log('error')
      }
      // console.log('this.userdetail', this.userdetail)
      this.dialogListPartner = false
    },
    editAddressCus (actions, item) {
      this.EditAddressDetail = ''
      // console.log('item=====================================>', item)
      // var data = this.customerData.customer_address[0]
      // console.log('customerDatacus_id', this.customerData)
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
        name: item.name,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        address_id: item.id,
        default_address: item.default_address
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
      this.page = 'listCustomerSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$EventBus.$emit('itemEdit', this.customerData)
      // console.log('this.customerData', this.customerData)
      // this.$EventBus.$emit('getCustomDetail', val.cus_id)
      this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
      // this.$EventBus.$emit('EditModalAddress', val, this.titleAddress, this.page, actions)
    },
    editInvoiceAddressCus (actions, item) {
      this.EditAddressDetail = ''
      // var typeCus = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      // if (typeCus === 'vendor') {
      //   this.SaleVendor = true
      // } else {
      //   this.SaleVendor = false
      // }
      // console.log('itemeditInvoiceAddressCus ')
      // var data = this.customerData.customer_address[0]
      // console.log('editInvoiceAddressCus', this.SaleVendor)
      if (this.SaleVendor === true) {
        var val = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          role: 'seller',
          cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
          name: item.name,
          email: item.email,
          first_name: item.first_name,
          last_name: item.last_name,
          phone: item.phone,
          house_no: item.house_no,
          room_no: item.room_no,
          floor: item.floor,
          building: item.building,
          moo_ban: item.moo_ban,
          moo_no: item.moo_no,
          soi: item.soi,
          yaek: item.yaek,
          street: item.street,
          tax_id: item.tax_id,
          sub_district: item.sub_district,
          district: item.district,
          province: item.province,
          postcode: item.postcode,
          address_id: item.id,
          default_address: item.default_address,
          invoice_address_id: item.id,
          tax_type: item.tax_type
        }
      } else {
        val = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          role: 'seller',
          cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
          name: item.name,
          email: item.email,
          first_name: item.first_name,
          last_name: item.last_name,
          phone: item.phone,
          house_no: item.house_no,
          room_no: item.room_no,
          floor: item.floor,
          building: item.building,
          moo_ban: item.moo_ban,
          moo_no: item.moo_no,
          soi: item.soi,
          yaek: item.yaek,
          street: item.street,
          tax_id: item.tax_id,
          sub_district: item.sub_district,
          district: item.district,
          province: item.province,
          postcode: item.postcode,
          address_id: item.id,
          default_address: item.default_address,
          invoice_address_id: item.id
        }
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'listCustomerSaleOrder'
      this.$EventBus.$emit('actionSale', actions)
      this.$EventBus.$emit('itemEdit', this.customerData)
      // this.$EventBus.$emit('getCustomDetail', val.cus_id)
      this.$refs.EditModalEtaxAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    async setDefaultAdress (item) {
      // console.log('setDefaultAdresssetDefaultAdress')
      localStorage.setItem('partner_id', this.cusID)
      var res
      var data
      if (this.titleList === 'ที่อยู่ใบกำกับภาษี') {
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          role: 'seller',
          cus_id: this.cusID,
          address_id: item.id,
          email: item.email,
          name: item.name,
          first_name: item.first_name,
          last_name: item.last_name,
          phone: item.phone,
          tax_id: item.tax_id,
          house_no: item.house_no,
          room_no: item.room_no,
          floor: item.floor,
          building: item.building,
          moo_ban: item.moo_ban,
          moo_no: item.moo_no,
          soi: item.soi,
          yaek: item.yaek,
          street: item.street,
          sub_district: item.sub_district,
          district: item.district,
          province: item.province,
          postcode: item.postcode,
          invoice_address_id: item.id,
          // default_address: item.default_address
          default_address: 'Y'
        }
        // console.log('sata', data)
        await this.$store.dispatch('actionsUpdateCustomerInvAddress', data)
        res = await this.$store.state.ModuleUser.stateUpdateCustomerInvAddress
      } else {
        // console.log('else')
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          role: 'seller',
          cus_id: this.cusID,
          address_id: item.id,
          name: item.name,
          first_name: item.first_name,
          last_name: item.last_name,
          phone: item.phone,
          house_no: item.house_no,
          room_no: item.room_no,
          floor: item.floor,
          building: item.building,
          moo_ban: item.moo_ban,
          moo_no: item.moo_no,
          soi: item.soi,
          yaek: item.yaek,
          street: item.street,
          sub_district: item.sub_district,
          district: item.district,
          province: item.province,
          postcode: item.postcode,
          // default_address: item.default_address
          default_address: 'Y'
        }
        // console.log('data', data)
        await this.$store.dispatch('actionsUpdateCustomerAddress', data)
        res = await this.$store.state.ModuleSaleOrder.stateUpdateCustomerAddress
        // console.log('res', res)
      }
      if (this.cusType === 'general') {
        localStorage.setItem('AddressCustomerDetail', Encode.encode(data))
      } else {
        localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(data))
      }
      if (res.message === 'Update customer address successfully.' || res.message === 'Update customer invoice address success.') {
        this.$swal.fire({ icon: 'success', title: `<h5>ตั้งค่าเป็น${this.titleList}เริ่มต้นแล้ว</h5>`, showConfirmButton: false, timer: 1500 })
        this.userdetail = [...res.data]
        this.dialogListAddressCustomer = false
        this.pageAddress = 1
        // console.log('this.page', this.page)
        // this.$EventBus.$emit('getCustomDetail', data.cus_id)
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</h5>', showConfirmButton: false, timer: 1500 })
      }
    },
    confirmDeleteCustomer (item) {
      // console.log('เข้าไหม')
      this.itemForDelete = item
      this.dialogAwaitDeleteCustomer = true
    },
    async deleteCustomer (item) {
      var data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: item.cus_id === undefined ? item.id : item.cus_id
      }
      var text = 'ลูกค้า'
      await this.$store.dispatch('actionsDeleteCustomer', data)
      var res = await this.$store.state.ModuleSaleOrder.stateDeleteCustomer
      if (res.message === 'Delete customer successfully.') {
        this.$swal.fire({ icon: 'success', title: `<h5>ลบ${text}สำเร็จ</h5>`, showConfirmButton: false, timer: 1500 })
        this.dialogAwaitDeleteCustomer = false
        this.getCustomer()
      } else {
        this.dialogAwaitDeleteCustomer = false
        this.$swal.fire({ icon: 'error', title: `<h5>ดำเนินการลบ${text}ไม่สำเร็จ</h5>`, showConfirmButton: false, timer: 1500 })
      }
    },
    getMessage (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Data missing. Please check your parameter and try again.') {
        return 'ข้อมูลขาดหาย โปรดตรวจสอบและลองอีกครั้ง'
      } else if (msg === 'Shop not found.') {
        return 'ไม่พบร้านค้า'
      } else if (msg === 'Your data not found in this shop.') {
        return 'ไม่พบข้อมูลของคุณในร้านค้านี้'
      } else if (msg === 'Sales man not found.') {
        return 'ไม่พบข้อมูลฝ่ายขาย'
      } else {
        return 'An error has occurred. Please try again in an hour or two.'
      }
    },
    changeTab (tabValue) {
      // console.log('changeTab', tabValue)
      if (tabValue === null || undefined) {
        tabValue = 0
      }
      this.itemsCustomer = []
      if (tabValue === 0) {
        this.itemsCustomer = this.listGeneralCustomer
        localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'general' }))
        // this.SaleVendor = false
      } else if (tabValue === 2) {
        this.itemsCustomer = this.listVendorCustomer
        localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'vendor' }))
        // this.SaleVendor = true
      } else {
        this.itemsCustomer = this.listBusinessCustomer
        localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'business' }))
        // this.SaleVendor = false
      }
    },
    close () {
      this.dialogListAddressCustomer = false
      this.pageAddress = 1
    },
    countCustomer (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    awaitEdit () {
      this.dialogListEditCustomer = false
    }
    // openListEditCustomer () {
    //   console.log('openListEditCustomer')
    //   this.dialogListEditCustomer = true
    // }
  }
}
</script>

<style scoped>
.cardChooseType{
  height: 350px;
  padding-top: 35px;
  text-align: center;
  border-radius: 10px;
}
.cardChooseTypeMobile{
  height: 200px;
  padding-top: 10px;
  text-align: center;
  border-radius: 10px;
}
.colorChange:hover{
  color: #27AB9C;
}
.cardChooseType:hover {
  transform: scale(1.05);
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
  border-bottom: 1px solid #27AB9C !important;
}
</style>
