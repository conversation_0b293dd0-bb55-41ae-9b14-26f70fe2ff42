<template>
  <v-row class="d-flex justify-center" justify="center" dense>
    <v-col cols="12" align="center" :class="MobileSize ? 'px-4' : ''">
      <!-- <v-img @click="test()" style="cursor: pointer;" class="rounded-lg" width="1225" :src="this.imageBanner" :lazy-src="this.imageLazyBanner"></v-img> -->
      <!-- <v-img style="margin: 0px 165px 0px 165px" src="@/assets/NewBanner.png"></v-img> -->
      <v-img v-if="Banner2.length !== 0 && Banner2[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg my-4" :src="Banner2[0].image_path" :lazy-src="Banner2[0].image_path_lazy" @click="navigateToLink(Banner2[0].link_banner)" max-height="270" :width="IpadSize || MobileSize ? '100%' : IpadProSize ? '98vw' : '1225'" contain></v-img>
      <v-img v-if="Banner2.length !== 0 && Banner2[0].link_banner === '-'" class="rounded-lg my-4" :src="Banner2[0].image_path" :lazy-src="Banner2[0].image_path_lazy" max-height="270" :width="IpadSize || MobileSize ? '100%' : IpadProSize ? '98vw' : '1225'" contain></v-img>
    </v-col>
  </v-row>
</template>

<script>
export default {
  data () {
    return {
      Banner2: [],
      listBanner: [],
      imageBanner: '',
      imageLazyBanner: ''
    }
  },
  created () {
    this.GetBanner()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    navigateToLink (link) {
      window.location.href = link
    },
    async GetBanner () {
      await this.$store.dispatch('actionsGetBanner')
      var response = await this.$store.state.ModuleHompage.stateGetBanner
      // console.log('response_GetBannerB', response.data.image_banner_2)
      // console.log('response_GetBannerA', response.data.image_banner_1[0])
      //   this.listBanner = response.data.image_banner_2[0]
      //   this.imageBanner = this.listBanner.path
      //   this.imageLazyBanner = this.listBanner.path_lazy
      if (response.data.image_banner_2.length > 0) {
        for (let i = 0; i < response.data.image_banner_2.length; i++) {
          this.Banner2.push({
            image_path: response.data.image_banner_2[i].path,
            image_path_lazy: response.data.image_banner_2[i].path_lazy,
            link_banner: response.data.image_banner_2[i].href
          })
        }
        // console.log(response.data.image_banner_3)
      } else {
        this.Banner2 = []
      }
    }
  }
}
</script>
