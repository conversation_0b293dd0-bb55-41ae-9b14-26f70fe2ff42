<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-col class="pt-6">
        <span v-if="MobileSize" class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">
          <v-icon color="#27AB9C" class="mr-2" @click="backToPage()">mdi-chevron-left</v-icon>จัดการ Flash Sale ร้านค้า</span>
        <span v-else class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">จัดการ Flash Sale ร้านค้า</span>
      </v-col>

      <v-col cols="12">
        <v-row style="display: flex; flex-wrap: wrap; align-items: center;">
          <v-col cols="12" md="auto">
            <span style="font-size: 16px;"><b>ตั้งค่าการใช้ส่วนลด :</b></span>
          </v-col>

          <v-col cols="12" sm="12" md="auto" class="d-flex align-center">
            <v-switch
              v-model="isSystemFlashSale"
              :disabled="isSystemFlashSale"
              color="#3eb3a6"
              inset
              class="my-0 ml-2"
              hide-details
              @change="handleSystemFlashSale"
            ></v-switch>
            <span class="ml-2" style="font-size: 16px;">เปิด (ส่วนลดระบบ)</span>
          </v-col>

          <v-col cols="12" sm="12" md="auto" class="d-flex align-center">
            <v-switch
              v-model="isShopFlashSale"
              :disabled="isShopFlashSale"
              color="#3eb3a6"
              inset
              class="my-0 ml-2"
              hide-details
              @change="handleShopFlashSale"
            ></v-switch>
            <span class="ml-2" style="font-size: 16px;">เปิด (ส่วนลดร้านค้า)</span>
          </v-col>

          <v-col cols="12" md="auto" class="ml-md-auto text-md-right">
            <span style="font-size: 16px;"><b>สถานะ :</b> </span>
            <v-chip outlined class="ml-2" color="orange"><span style="font-size: 16px;">{{ setStatus() }}</span></v-chip>
          </v-col>
        </v-row>
      </v-col>

      <v-col cols="12" class="text-end">
        <v-btn rounded color="#27ab9c" dark @click="editImage()">
          <span style="font-size: 16px; text-transform: none;">
            แก้ไขภาพ Flash Sale
          </span>
        </v-btn>
        <v-btn rounded color="#27ab9c" :class="MobileSize || IpadSize ? 'mt-2' : 'ml-2'" style="color: #FFFFFF;" @click="dialogSelectProductFlashSale = true">
          <span style="font-size: 16px; text-transform: none;">
            จัดการสินค้า Flash Sale หน้า Home
          </span>
        </v-btn>
      </v-col>

      <v-col cols="12" class="text-end">
        <span style="font-size: 16px;"><b>เวลาในการแสดงสินค้า Flash Sale :</b></span>
        <v-chip outlined class="ml-2" color="orange"><span style="font-size: 16px;">{{ this.startTime }} - {{ this.endTime }} น.</span></v-chip>
      </v-col>

      <CarouselFlashSell />

    </v-card>

    <v-dialog v-model="dialogSelectProductFlashSale" persistent max-width="750">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogSelectProductFlashSale = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span :style="MobileSize || IpadSize ? 'font-size: 16px;' : 'font-size: 20px;'"><b>จัดการสินค้า Flash Sale หน้า Home</b></span>
        </v-card-title>
        <br>

        <v-card-text>
          <v-col cols="12">
            <v-row>
              <v-col cols="12">
                <span style="font-size: 16px;"><b>เลือกเวลาแสดงสินค้า Flash Sale</b></span>
              </v-col>
              <v-col cols="12" sm="6">
                <v-menu
                  ref="startMenu"
                  v-model="startMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="startTime"
                      label="เวลาเริ่มต้น"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      dense
                      hide-details="true"
                    ></v-text-field>
                  </template>
                  <v-time-picker
                    v-model="startTime"
                    @change="startMenu = false"
                    format="24hr"
                  ></v-time-picker>
                </v-menu>
              </v-col>
              <v-col cols="12" sm="6">
                <v-menu
                  ref="endMenu"
                  v-model="endMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="endTime"
                      label="เวลาสิ้นสุด"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      dense
                      hide-details="true"
                    ></v-text-field>
                  </template>
                  <v-time-picker
                    v-model="endTime"
                    @change="endMenu = false"
                    format="24hr"
                  ></v-time-picker>
                </v-menu>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 16px;"><b>เลือกสินค้าที่ต้องการให้แสดงจำนวน 18 สินค้า</b></span>
              </v-col>
              <v-col cols="12">
                <v-text-field v-model="searchAddTag" @keyup="searchDataAddTag(searchAddTag)" placeholder="ค้นหาชื่อสินค้าหรือรหัสสินค้า" outlined rounded dense hide-details style="border-radius: 8px;">
                  <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" v-if="this.flashSaleProduct.length === 0">
                <v-card
                  elevation="0"
                  style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
                >
                <v-container>
                    <v-col cols="12">
                      <div class="d-flex flex-column align-center justify-center">
                        <v-avatar size="200">
                          <v-img src="@/assets/NoProducts.png" width="300" height="300" contain></v-img>
                        </v-avatar>
                        <span style="font-size: 18px;">ไม่มีรายการสินค้า</span>
                      </div>
                    </v-col>
                  </v-container>
                </v-card>
              </v-col>
              <v-col cols="12" class="pa-0" v-else>
                <v-row no-gutters>
                  <v-col
                    v-for="product in flashSaleProduct"
                    :key="product.product_id"
                    cols="12" sm="6" md="4" lg="4"
                    class="mb-4 pa-2"
                  >
                    <v-card>
                      <div style="display: flex; justify-content: center; align-items: center; padding: 10px;">
                        <v-img
                          v-if="product.product_image && product.product_image !== '-' && product.product_image !== null"
                          :src="product.product_image"
                          height="100px"
                          width="100px"
                          class="mr-2"
                          contain
                        ></v-img>
                        <img
                          v-else
                          src="@/assets/NoImage.png"
                          style="max-width: 100px; max-height: 100px;"
                          class="mr-2"
                        />
                      </div>

                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-card-title
                            v-bind="attrs"
                            v-on="on"
                            style="
                              font-size: 16px;
                              color: #333333;
                              white-space: nowrap;
                              overflow: hidden;
                              text-overflow: ellipsis;
                              width: 100%;
                              display: block;
                            "
                            >
                            <b>{{ product.product_name }}</b>
                          </v-card-title>
                        </template>
                        <span>{{ product.product_name }}</span>
                      </v-tooltip>

                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-card-subtitle
                            v-bind="attrs"
                            v-on="on"
                            style="
                              font-size: 14px;
                              color: #333333;
                              white-space: nowrap;
                              overflow: hidden;
                              text-overflow: ellipsis;
                              width: 100%;
                              display: block;
                              padding-top: 0px;
                            "
                          >
                            รหัสสินค้า : {{ product.product_sku }}
                          </v-card-subtitle>
                        </template>
                        <span>รหัสสินค้า : {{ product.product_sku }}</span>
                      </v-tooltip>

                      <v-card-actions>
                        <v-checkbox
                          :value="product.product_id"
                          v-model="selectedProductIds"
                          hide-details
                          outline
                          label="เลือก"
                          class="pt-0 mr-0"
                        />
                      </v-card-actions>

                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
        </v-card-text>

        <v-col cols="12">
          <v-row no-gutters justify="center" v-if="pageMaxDialog > 0">
            <v-pagination
              color="#27AB9C"
              v-model="pageNumberDialog"
              :length="pageMaxDialog"
              :total-visible="9">
            </v-pagination>
          </v-row>
        </v-col>

        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 150px; font-size: 16px;" @click="dialogSelectProductFlashSale = false">ยกเลิก</v-btn>
          <!-- <v-btn rounded color="#27AB9C" class="ma-1" style="width: 150px; color: white; font-size: 16px;" :disabled="disabledSubmit" @click="submitTags()">บันทึก</v-btn> -->
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 150px; color: white; font-size: 16px;" @click="SaveManageFlashSale()">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
// import VueHorizontalList from 'vue-horizontal-list'
export default {
  components: {
    // VueHorizontalList,
    CarouselFlashSell: () => import('@/components/Carousel/FlashProductUI')
    // CardProductsFlashSale: () => import(/* webpackPrefetch: true */ '@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome'),
    // CardProductsFlashSaleMobile: () => import(/* webpackPrefetch: true */ '@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome')
  },
  data () {
    return {
      statusFlashSale: '',
      isSystemFlashSale: false,
      isShopFlashSale: false,
      dialogSelectProductFlashSale: false,
      searchAddTag: '',
      current: 1,
      itemsPerPage: 9,
      currentDialog: 1,
      pageMax: 0,
      // pageMaxDialog: 10,
      pageMaxDialog: 0,
      selectedProductIds: [],
      startTime: '00:00',
      endTime: '00:00',
      startMenu: false,
      endMenu: false,
      flashSaleProduct: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // isDataReady () {
    //   return this.flashSaleProduct && this.flashSaleProduct.length > 0
    // },
    // optionsItems () {
    //   return {
    //     responsive: [
    //       { end: 576, size: 2 },
    //       { start: 576, end: 768, size: 4 },
    //       { start: 768, end: 1024, size: 4 },
    //       { start: 1024, end: 1200, size: 6 },
    //       { start: 1200, size: 6 }
    //     ],
    //     list: {
    //       // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
    //       windowed: 1200,
    //       // Because: #app {padding: 80px 24px;}
    //       padding: 0
    //     },
    //     item: {
    //       // css class to inject into each individual item
    //       class: '',
    //       // padding between each item
    //       padding: this.IpadSize || this.IpadProSize ? '8' : this.MobileSize ? '4' : '24'
    //     },
    //     position: {
    //       // Start from '1' on mounted.
    //       start: 0
    //     }
    //   }
    // },
    // pageNumberDialog: {
    //   get () {
    //     return this.currentDialog || 1
    //   },
    //   set (newPage) {
    //     this.currentDialog = newPage
    //     window.scrollTo(0, 0)
    //     this.ListProductTagDialog(newPage, this.search)
    //   }
    // }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminFlashsaleManageMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'adminFlashsaleManage')
        this.$router.push({ path: '/adminFlashsaleManage' }).catch(() => {})
      }
    }
  },
  async mounted () {
    await this.StatusFlashSale()
    this.setStatus()
  },
  async created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNavAdmin')
    await this.StatusFlashSale()
    // if (this.statusFlashSale === 'no') {
    //   await this.getFlashSaleProduct()
    // } else if (this.statusFlashSale === 'yes') {
    //   await this.getFlashSaleProductSystem()
    // }
  },
  methods: {
    backToPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    editImage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/EditImagesFlashSaleMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/EditImagesFlashSale' }).catch(() => { })
      }
    },
    async handleSystemFlashSale () {
      if (this.isSystemFlashSale) {
        this.isShopFlashSale = false
        await this.updateFlashSaleStatus('yes')
      } else {
        await this.updateFlashSaleStatus('no')
      }
    },
    async handleShopFlashSale () {
      if (this.isShopFlashSale) {
        this.isSystemFlashSale = false
        await this.updateFlashSaleStatus('no')
      } else {
        await this.updateFlashSaleStatus('yes')
      }
    },
    setStatus () {
      if (this.isSystemFlashSale) {
        return 'กำลังใช้งานส่วนลดระบบ'
      } else if (this.isShopFlashSale) {
        return 'กำลังใช้งานส่วนลดร้านค้า'
      } else {
        return 'สถานะส่วนลดไม่ถูกต้อง'
      }
    },
    async StatusFlashSale () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsStatusFlashSale')
      const responseStatusFlashSale = await this.$store.state.ModuleAdminManage.stateStatusFlashSale

      if (responseStatusFlashSale.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.statusFlashSale = responseStatusFlashSale.data
        this.isSystemFlashSale = this.statusFlashSale === 'yes'
        this.isShopFlashSale = this.statusFlashSale === 'no'
      }
    },
    async updateFlashSaleStatus (status) {
      this.$store.commit('openLoader')
      const data = {
        status_flash_sale: status
      }

      await this.$store.dispatch('actionsSetFlashSale', data)
      const responseUpdateSetFlashSale = await this.$store.state.ModuleAdminManage.stateSetFlashSale

      if (responseUpdateSetFlashSale.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: status === 'yes'
            ? 'เปิดการใช้งาน Flash Sale สำเร็จ (ส่วนลดระบบ)'
            : 'เปิดการใช้งาน Flash Sale สำเร็จ (ส่วนลดร้านค้า)'
        })
        this.$EventBus.$emit('getAllProductFlashSale')
      } else {
        this.$swal.fire({
          icon: 'error',
          title: 'ไม่สามารถเปลี่ยนสถานะ Flash Sale ได้',
          text: responseUpdateSetFlashSale.message
        })
      }
    },
    // async getFlashSaleProduct () {
    //   var data = {
    //     page: 1,
    //     limit: 18
    //   }
    //   this.$store.commit('openLoader')
    //   await this.$store.dispatch('actionsFlashSaleProductatShop', data)
    //   var response = await this.$store.state.ModuleHompage.stateFlashSaleProduct
    //   if (response.message === 'Get Product Success.') {
    //     this.flashSaleProduct = response.data.products
    //     this.$store.commit('closeLoader')
    //   } else {
    //     this.flashSaleProduct = []
    //     this.$store.commit('closeLoader')
    //   }
    // },
    // async getFlashSaleProductSystem () {
    //   var dataRole = ''
    //   if (localStorage.getItem('roleUser') !== null) {
    //     dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   } else {
    //     dataRole = {
    //       role: 'ext_buyer'
    //     }
    //   }
    //   var data = {
    //     company_id: -1,
    //     status_product: 'discount',
    //     role_user: dataRole.role
    //   }
    //   this.$store.commit('openLoader')
    //   await this.$store.dispatch('actionsProductCardLandingPage', data)
    //   var response = await this.$store.state.ModuleHompage.stateGetProductCardLandingPage
    //   if (response.ok === 'y') {
    //     this.flashSaleProduct = response.query_result
    //     this.isLoading = true
    //     this.$store.commit('closeLoader')
    //   } else {
    //     this.flashSaleProduct = []
    //     this.$store.commit('closeLoader')
    //   }
    // },
    searchDataAddTag (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.ListProductTagDialog(1, val)
      }, 500)
    },
    async ListProductTagDialog (page = 1, textSearch = '') {
      this.pageNumberDialog = page
      this.searchAddTag = textSearch
      this.listProductTagDialog = this.flashSaleProduct
      this.pageMaxDialog = 2
    }
  }
}
</script>

<style scoped>
::v-deep .vhl-list>*[data-v-8b923bbc] {
  flex-shrink: 1 !important;
}
::v-deep .vhl-container {
  margin: auto !important;
  max-width: 1320px !important;
}
::v-deep .vhl-btn-left {
  max-width: 40px !important;
  max-height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  box-shadow: none !important;
  background: transparent !important;
  z-index: 2;
}
::v-deep .vhl-btn-right {
  max-width: 40px !important;
  max-height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  background: transparent !important;
  box-shadow: none !important;
  z-index: 2;
}
/* margin: auto;
max-width: 1290px; */
</style>
