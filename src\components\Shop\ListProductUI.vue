<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container style="max-width: 1320px !important;">
      <v-overlay :value="overlay2">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <div v-if="this.typeProduct !== 'flashSaleShop'">
        <v-row dense align-content="center" justify="center">
          <v-col cols="12" md="12" xs="12">
            <v-row dense justify="center">
              <h2 class="pt-3 fontHeaderListProduct gradient-underline" :style="MobileSize ? 'font-size: 20px !important;' : ''">{{header}}</h2>
            </v-row>
          </v-col>
        </v-row>
        <v-row justify="start" class="pt-12" v-if="!MobileSize">
          <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" :class="IpadProSize ? '' : IpadSize ? 'px-1' : ''" style="display: flex; justify-content: center;">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-else>
          <v-col cols="6" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" class="mb-4 px-0" style="display: flex; justify-content: center;">
            <CardProductsMobile :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="my-6">
        <v-pagination
          color="#3EC6B6"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="MobileSize ? 5 : 7"
          class="paginationStyle"
          @input="pageChange($event)"
        ></v-pagination>
      </v-row>
      </div>
      <div v-else>
        <v-col cols="12" class="pl-0 pr-0">
          <v-card outlined>
            <v-card-title style="display: flex; justify-content: center;">
              <span style="color: #ff4500;"><b>{{ titleFlashSale }}</b></span>
              <v-icon class="ml-2" color="#333333">mdi mdi-clock-time-eight-outline</v-icon>
              <v-chip class="ml-2" text-color="#ff4500" color="rgba(255, 113, 11, 0.10)" style="font-size: 20px; font-weight: 500;">
                {{ startFlashSale }} - {{ endFlashSale }}
              </v-chip>
            </v-card-title>
          </v-card>
        </v-col>
        <v-col cols="12" class="pl-0 pr-0 pb-0">
          <v-img
            :src="BannerFlashSale"
            style=""
            contain
          ></v-img>
        </v-col>
        <v-col cols="12" class="pl-0 pr-0 pt-0">
          <v-row>
            <v-col cols="12">
              <v-row no-gutters>
                <v-tabs
                  v-model="tab"
                  background-color="#FF4500"
                  grow
                  show-arrows
                  class="time-tabs"
                  slider-color="#FF4500"
                  icons-and-text
                  @change="onTabChange"
                >
                  <v-tab
                    v-for="(item, index) in itemsTabs"
                    :key="index"
                    :class="tab === index ? 'active-tab' : 'inactive-tab'"
                  >
                    <div class="text-center">
                      <div class="time-text">{{ item.time }}</div>
                      <div class="sub-text" style="text-transform: none;">{{ item.label }}</div>
                    </div>
                  </v-tab>
                </v-tabs>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-row justify="start" class="pt-5" v-if="!MobileSize">
          <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" :class="IpadProSize ? '' : IpadSize ? 'px-1' : ''" style="display: flex; justify-content: center;">
            <CardProductsFlashSale :itemProduct='item' />
          </v-col>
          <v-col cols="12" v-if="paginated.length === 0" class="d-flex justify-center align-center" style="padding-bottom: 20px;">
            <span style="font-size: 16px;"><b>{{ $t('FlashSale.flashSaleComingSoon') }}</b></span>
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-else>
          <v-col cols="6" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" class="mb-4 px-0" style="display: flex; justify-content: center;">
            <CardProductsFlashSale :itemProduct='item' />
          </v-col>
          <v-col cols="12" v-if="paginated.length === 0" class="d-flex justify-center align-center" style="padding-top: 15px; padding-bottom: 15px;">
            <span style="font-size: 16px;"><b>{{ $t('FlashSale.flashSaleComingSoon') }}</b></span>
          </v-col>
        </v-row>
        <v-row justify="center" class="my-6" v-if="paginated.length !== 0">
          <v-pagination
            color="#3EC6B6"
            v-model="pageNumber"
            :length="pageMax"
            :total-visible="MobileSize ? 5 : 7"
            class="paginationStyle"
            @input="pageChange($event)"
          ></v-pagination>
        </v-row>
      </div>
      <!-- <a-row type="flex" :gutter="[16, 8]">
        <a-col :span="24" style="text-align:center">
          <span class="display-1">{{header}}</span>
        </a-col>
        <a-col :span="24">
          <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} รายการสำหรับ "{{ header }}"</span>
        </a-col>
        <a-col :span="24" style="padding: 0; margin-top: -20px;">
          <a-divider></a-divider>
        </a-col>
        <div v-if="AllProduct.length !== 0">
          <a-col :span="24" :md="4" :sm="12" :xs="24" v-for="(item,index) in AllProduct" :key="index">
            <CardProducts :itemProduct="item" />
          </a-col>
        </div>
        <div v-else>
          <h2>ยังไม่มีรายการสินค้า{{ header }}</h2>
        </div>
      </a-row> -->
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
import moment from 'moment'
const FakeData = []
for (let i = 0; i < 48; i++) {
  FakeData.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsMobile: () => import('@/components/Card/ProductCardResponsive'),
    CardProductsFlashSale: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome')
  },
  data () {
    return {
      path: process.env.VUE_APP_DOMAIN,
      limit: 48,
      pathShopSale: '',
      header: '',
      FakeData,
      overlay2: false,
      productCount: null,
      AllProduct: [],
      pageMax: null,
      current: 1,
      pageSize: 48,
      companyId: '',
      typeProduct: '',
      RowUserData: '',
      articleId: '',
      sellerId: '',
      items: [
        {
          category_name: this.$t('ShopPage.Title'),
          disabled: false,
          color: '#636363',
          href: '/'
        }
      ],
      BannerFlashSale: null,
      itemsTabs: [],
      timeSlot: '',
      titleFlashSale: '',
      startFlashSale: '',
      endFlashSale: ''
    }
  },
  watch: {
    async $route (to, from) {
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page

      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          const newPage = parseInt(getIDToParams)

          // ตรวจสอบว่า pageNumber ถูกต้องก่อนเรียก pageChange()
          if (newPage !== this.pageNumber) {
            this.pageNumber = newPage
            this.pageChange(newPage)
          }
        }
      }
    },
    tab (newVal) {
      this.onTabChange(newVal)
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    this.typeProduct = this.$route.params.data
    // this.pageNumber = parseInt(this.$route.query)
    // console.log('pageNumber111', this.pageNumber)
    // this.header = this.$router.currentRoute.params.data
    // console.log('this.header------->', this.$route.query)
    // this.AllProduct = JSON.parse(Decode.decode(localStorage.getItem('itemShop')))
    // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
    this.oneData = []
    if (localStorage.getItem('oneData') !== null) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RowUserData = 'ext_buyer'
    }
    if (localStorage.getItem('SetRowCompany') !== null) {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      this.companyId = companyId.company.company_id
      // console.log('this.companyId', this.companyId)
    } else if (this.RowUserData === 'sale_order') {
      this.companyId = localStorage.getItem('PartnerID')
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
    } else if (this.RowUserData === 'sale_order_no_JV') {
      this.companyId = '-1'
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
    } else {
      this.companyId = ''
    }
    if (this.typeProduct === 'new_product') {
      this.header = this.$t('ShopPage.NewArrival')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllNewProductInShop', this.getAllNewProductInShop)
      this.getAllNewProductInShop()
    } else if (this.typeProduct === 'best_seller') {
      this.header = this.$t('ShopPage.BestSellers')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllBestSellerInShop', this.getAllBestSellerInShop)
      this.getAllBestSellerInShop()
    } else if (this.typeProduct === 'recommended_product') {
      this.header = this.$t('ShopPage.Recommended')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllRecomenProductShopInShop', this.getAllRecomenProductShopInShop)
      this.getAllRecomenProductShopInShop()
    } else if (this.typeProduct === 'all_product') {
      this.typeProduct = 'all_product'
      this.header = this.$t('ShopPage.AllProducts')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        // console.log('sale_order_no_JV---->', this.pathShopSale)
        // this.items.push({
        //   category_name: this.header,
        //   disabled: true,
        //   color: '#3EC6B6',
        //   href: this.pathShopSale
        // })
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllProductShopInShop', this.getAllProductShopInShop)
      this.getAllProductShopInShop()
    } else if (this.typeProduct === 'sale_product') {
      this.header = this.$t('ShopPage.DiscountedProducts')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllSaleProductInShop', this.getAllSaleProductInShop)
      this.getAllSaleProductInShop()
    } else if (this.typeProduct === 'normal_product') {
      this.header = this.$t('ShopPage.GeneralProducts')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllNormalProductInShop', this.getAllNormalProductInShop)
      this.getAllNormalProductInShop()
    } else if (this.typeProduct === 'out_product') {
      this.header = this.$t('ShopPage.OutOfStock')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllOutProductInShop', this.getAllOutProductInShop)
      this.getAllOutProductInShop()
    } else if (this.typeProduct === 'article') {
      this.header = 'สินค้าในบทความ'
      if (this.RowUserData === 'article') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      this.$EventBus.$on('getDataListDetailArticle', this.getDataListDetailArticle)
      this.getDataListDetailArticle('action')
    } else if (this.typeProduct === 'flashSaleShop') {
      this.header = 'Flash Sale'
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllBestSellerInShop', this.getAllBestSellerInShop)
      this.getProductFlashSale()
      this.updateTimeTabs()
      this.startTimeSlotWatcher()
      this.$EventBus.$on('getProductFlashSale', this.getProductFlashSale)
    }
    // console.log('===========>', this.header)
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      // var repeatOrder = []
      // var product = ''
      // if (this.AllProduct.length !== 0) {
      //   for (const item of this.AllProduct) {
      //     // console.log('item------>', item)
      //     product = {
      //       ...item,
      //       link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id)
      //     }
      //     // console.log('product', product)
      //     repeatOrder.push(product)
      //   }
      // }
      // return repeatOrder
      return this.AllProduct
    }
  },
  methods: {
    pageChange (val) {
      window.scrollTo(0, 0)
      val = this.pageNumber
      // this.pageNumber = val
      this.$router.push(`/ListShopProduct/${this.typeProduct}?page=${val}`).catch(() => {})
      if (this.typeProduct === 'article') {
        this.$router.push(`/ListShopProduct/${this.typeProduct}?ArticleID=${this.articleId}&SellerShopID=${this.sellerId}&page=${val}`).catch(() => {})
      }
      // console.log('pageChange====>', this.typeProduct)
      if (this.typeProduct === 'all_product') {
        // this.$EventBus.$on('getAllProductShopInShop', this.getAllProductShopInShop)
        this.getAllProductShopInShop()
      } else if (this.typeProduct === 'sale_product') {
        this.getAllSaleProductInShop()
      } else if (this.typeProduct === 'normal_product') {
        this.getAllNormalProductInShop()
      } else if (this.typeProduct === 'out_product') {
        this.getAllOutProductInShop()
      } else if (this.typeProduct === 'new_product') {
        this.getAllNewProductInShop()
      } else if (this.typeProduct === 'best_seller') {
        this.getAllBestSellerInShop()
      } else if (this.typeProduct === 'recommended_product') {
        this.getAllRecomenProductShopInShop()
      } else if (this.typeProduct === 'article') {
        this.getDataListDetailArticle('action')
      } else if (this.typeProduct === 'flashSaleShop') {
        // this.getProductFlashSale()
        console.log('voke')
        this.$router.push(`/ListShopProduct/${this.typeProduct}?&ID=${this.sellerId}&page=${val}`).catch(() => {})
        this.getProductFlashSale()
      }
    },
    gotoBannerPage (val) {
      var dataR = JSON.parse(localStorage.getItem('roleUser')).role
      if (dataR === 'sale_order' || dataR === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathShopSale }).catch(() => {})
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    async getAllNewProductInShop () {
      if (this.header === this.$t('ShopPage.NewArrival')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataNewProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataNewProduct = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'new',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataNewProduct = {
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'new',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        // console.log('dataNewProduct', dataNewProduct)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataNewProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },

    async getAllBestSellerInShop () {
      if (this.header === this.$t('ShopPage.BestSellers')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataBestProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataBestProduct = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'best-seller',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataBestProduct = {
            // token: '',
            // role_user: 'ext_buyer',
            // begin_search_type: 'component',
            // begin_search_details: {
            //   custom_user_ID: '1',
            //   what_component: 'best_seller',
            //   component_id: ''
            // },
            // user_detail: {
            //   company_id: this.id_company
            // },
            // seller_shop_id: shopId
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'best-seller',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataBestProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },

    async getAllRecomenProductShopInShop () {
      if (this.header === this.$t('ShopPage.Recommended')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataRecomProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataRecomProduct = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'recommend',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataRecomProduct = {
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'recommend',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataRecomProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },
    async getAllSaleProductInShop () {
      if (this.header === this.$t('ShopPage.DiscountedProducts')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAllSaleProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAllSaleProduct = {
            // token: this.oneData.user.access_token,
            // role_user: dataRole.role,
            // begin_search_type: 'component',
            // begin_search_details: {
            //   custom_user_ID: '1',
            //   what_component: 'all_product',
            //   component_id: ''
            // },
            // user_detail: {
            //   company_id: this.id_company
            // },
            // seller_shop_id: shopId
            category: '',
            orderBy: '',
            status_product: 'sale',
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            page: this.pageNumber,
            limit: this.limit
          }
        } else {
          dataAllSaleProduct = {
            // token: '',
            // role_user: 'ext_buyer',
            // begin_search_type: 'component',
            // begin_search_details: {
            //   custom_user_ID: '1',
            //   what_component: 'all_product',
            //   component_id: ''
            // },
            // user_detail: {
            //   company_id: this.id_company
            // },
            // seller_shop_id: shopId
            category: '',
            orderBy: '',
            status_product: 'sale',
            role_user: 'ext_buyer',
            company_id: this.id_company,
            page: this.pageNumber,
            seller_shop_id: shopId,
            limit: this.limit
          }
        }
        // console.log('dataAllSaleProduct', dataAllSaleProduct)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataAllSaleProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },
    async getAllNormalProductInShop () {
      if (this.header === this.$t('ShopPage.GeneralProducts')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAllNormalProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAllNormalProduct = {
            category: '',
            orderBy: '',
            status_product: 'general',
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            page: this.pageNumber,
            seller_shop_id: shopId,
            limit: this.limit
          }
        } else {
          dataAllNormalProduct = {
            category: '',
            orderBy: '',
            status_product: 'general',
            page: this.pageNumber,
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            limit: this.limit
          }
        }
        // console.log('dataAllNormalProduct', dataAllNormalProduct)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataAllNormalProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        // console.log('response', response)
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },
    async getAllOutProductInShop () {
      if (this.header === this.$t('ShopPage.OutOfStock')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAllOutProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAllOutProduct = {
            page: this.pageNumber,
            category: '',
            orderBy: '',
            status_product: '',
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            limit: this.limit
          }
        } else {
          dataAllOutProduct = {
            page: this.pageNumber,
            category: '',
            orderBy: '',
            status_product: '',
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            limit: this.limit
          }
        }
        // console.log('dataAllOutProduct', dataAllOutProduct)
        await this.$store.dispatch('actionsOutOfStockProducts', dataAllOutProduct)
        var response = await this.$store.state.ModuleShop.stateOutOfStockProducts
        // console.log('response', response)
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              normalOutStock: true
            }))
          } else {
            this.AllProduct = []
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
      // console.log('this.AllProduct', this.AllProduct)
    },
    async getAllProductShopInShop () {
      // console.log('testttt')
      this.$store.commit('openLoader')
      this.pageNumber = parseInt(this.$route.query.page)
      if (this.header === this.$t('ShopPage.AllProducts')) {
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAll
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAll = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: '',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataAll = {
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: '',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        // console.log('dataAll', dataAll)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataAll)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          this.$store.commit('closeLoader')
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.$store.commit('closeLoader')
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
      }
    },
    async getDataListDetailArticle (action) {
      if (action === 'action') {
        this.$store.commit('openLoader')
      }
      if (this.articleId === '' && this.sellerId === '') {
        this.articleId = this.$route.query.Article
        this.sellerId = this.$route.query.ID
      }
      const payload = {
        article_id: this.articleId,
        seller_shop_id: this.sellerId,
        Page: this.pageNumber,
        Limit: this.limit
      }
      await this.$store.dispatch('actionsListProductAritcleV2', payload)
      var response = await this.$store.state.ModuleArticle.stateListProductAritcleV2
      if (response.result === 'SUCCESS') {
        // console.log('response', response)
        if (response.data.data.length !== 0) {
          var refCode = this.$route.query.ref_code
          this.AllProduct = []
          this.AllProduct = response.data.data
          this.AllProduct = this.AllProduct.map((item, index) => ({
            ...item,
            link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
            ref_code: refCode
          }))
        } else {
          this.AllProduct = []
        }
        this.pageMax = parseInt(response.data.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
      if (action === 'action') {
        this.$store.commit('closeLoader')
      }
    },
    async getProductFlashSale () {
      this.$store.commit('openLoader')
      this.sellerId = this.$route.query.ID
      this.pageNumber = parseInt(this.$route.query.page)
      var data = {
        seller_shop_id: parseInt(this.sellerId),
        page: this.pageNumber,
        limit: this.limit,
        role: this.RowUserData,
        time_slot: this.timeSlot
      }
      await this.$store.dispatch('actionsGetFlashSale', data)
      var response = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
      if (response.result === 'SUCCESS') {
        // var id = JSON.parse(Decode.decode(localStorage.getItem('flashSaleId')))
        var productFlashSale = response.data.data
        if (productFlashSale.length !== 0) {
          this.AllProduct = []
          this.AllProduct = productFlashSale[0].product_list
          this.titleFlashSale = productFlashSale[0].title
          this.startFlashSale = productFlashSale[0].start_time
          this.endFlashSale = productFlashSale[0].end_time
          this.BannerFlashSale = productFlashSale[0].image_path
          // this.AllProduct = this.AllProduct.map((item, index) => ({
          //   ...item,
          //   link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
          // }))
        } else {
          this.AllProduct = []
        }
        this.pageMax = parseInt(response.data.max_page)
        // console.log(this.pageMax, 'max')
        // this.pageNumber = parseInt(this.$route.query.page)
        // console.log(this.pageNumber, 'number')
        this.$store.commit('closeLoader')
      }
    },
    onTabChange (newTabIndex) {
      this.timeSlot = this.itemsTabs[newTabIndex].time
      this.getProductFlashSale()
    },
    startTimeSlotWatcher () {
      this.updateTimeTabs()

      const now = moment()
      const startOfToday = moment().startOf('day')

      const timeSlots = [
        moment(startOfToday).hour(12),
        moment(startOfToday).hour(18),
        moment(startOfToday).hour(21),
        moment(startOfToday).add(1, 'day') // 00:00 ของวันถัดไป
      ]

      // หา slot ถัดไปจากตอนนี้
      const nextSlot = timeSlots.find(t => now.isBefore(t))

      if (!nextSlot) {
        // ถ้าเลยทุก slot → รีใหม่พรุ่งนี้ตอน 12:00
        const tomorrowNoon = moment(startOfToday).add(1, 'day').hour(12)
        const ms = tomorrowNoon.diff(now)
        setTimeout(() => this.startTimeSlotWatcher(), ms)
      } else {
        const msUntilNext = nextSlot.diff(now)
        setTimeout(() => this.startTimeSlotWatcher(), msUntilNext)
      }
    },
    updateTimeTabs () {
      const now = moment()
      const startOfToday = moment().startOf('day')

      const rawTimeItems = [
        { time: '12:00' },
        { time: '18:00' },
        { time: '21:00' },
        { time: '00:00' }
      ]

      const updatedItems = rawTimeItems.map((item, index) => {
        var start = moment(item.time, 'HH:mm').set({
          year: now.year(),
          month: now.month(),
          date: now.date()
        })

        if (item.time === '00:00') {
          const noonToday = moment(startOfToday).hour(12)
          if (now.isSameOrAfter(noonToday)) {
            start.add(1, 'day')
          }
        }

        var nextIndex = index + 1
        const nextItem = rawTimeItems[nextIndex % rawTimeItems.length]
        let end = null

        if (nextItem && nextItem.time) {
          end = moment(nextItem.time, 'HH:mm').set({
            year: start.year(),
            month: start.month(),
            date: start.date()
          })

          if (nextItem.time === '00:00') {
            end.add(1, 'day')
          }

          if (end.isSameOrBefore(start)) {
            end.add(1, 'day')
          }
        }

        let label = this.$t('FlashSale.tomorrow')
        if (now.isSameOrAfter(start) && (!end || now.isBefore(end))) {
          label = this.$t('FlashSale.ongoing')
        } else if (now.isBefore(start) && start.isSame(now, 'day')) {
          label = this.$t('FlashSale.comingSoon')
        } else if (start.diff(now, 'days') === 1) {
          label = this.$t('FlashSale.tomorrow')
        }

        return { ...item, label, start, end }
      })

      // หาจุดเริ่ม: slot ที่กำลังดำเนินอยู่หรือ slot ถัดไป
      let rotateIndex = updatedItems.findIndex(item => {
        return now.isSameOrAfter(item.start) && now.isBefore(item.end)
      })

      if (rotateIndex === -1) {
        // ถ้าไม่มีช่วงเวลาที่ครอบคลุม now ให้หา slot ถัดไป
        rotateIndex = updatedItems.findIndex(item => now.isBefore(item.start))
      }

      // ถ้ายังไม่เจอเลย (เลยเวลาหมดแล้ว) → เริ่มจาก index 0
      if (rotateIndex === -1) rotateIndex = 0

      // หมุน array
      const rotatedItems = [
        ...updatedItems.slice(rotateIndex),
        ...updatedItems.slice(0, rotateIndex)
      ]

      this.itemsTabs = rotatedItems
      this.tab = 0
      this.timeSlot = rotatedItems[0].time
      // this.getProductFlashSale()
    }
  }
}
</script>

<style scoped>
.active-tab {
  background-color: #ff4500;
  color: white !important;
}

.inactive-tab {
  background-color: #ffbda0;
  color: #ffffff !important;
}

.time-text {
  font-size: 20px;
  font-weight: bold;
}

.sub-text {
  font-size: 14px;
}

.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  white-space: nowrap;
  list-style-type: none;
  margin-bottom: 24px;
  /* padding: 8px 0px 8px 75px !important; */
}
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #3EC6B6 !important;
}
.v-breadcrumbs li .v-icon {
  color: #3EC6B6 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
</style>
