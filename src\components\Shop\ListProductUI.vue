<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container style="max-width: 1320px !important;">
      <v-overlay :value="overlay2">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-row dense align-content="center" justify="center" v-if="BannerFlashSale !== null" class="mb-5">
        <v-img
        :src="BannerFlashSale"
        style="border-radius: 10px;"
        :aspect-ratio="370/130">
        </v-img>
      </v-row>
      <v-row dense align-content="center" justify="center">
        <v-col cols="12" md="12" xs="12">
          <v-row dense justify="center">
            <h2 class="pt-3 fontHeaderListProduct gradient-underline" :style="MobileSize ? 'font-size: 20px !important;' : ''">{{header}}</h2>
          </v-row>
        </v-col>
      </v-row>
      <v-row justify="start" class="pt-12" v-if="!MobileSize">
        <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" :class="IpadProSize ? '' : IpadSize ? 'px-1' : ''" style="display: flex; justify-content: center;">
          <CardProducts :itemProduct='item' />
        </v-col>
      </v-row>
      <v-row justify="start" class="px-0" v-else>
        <v-col cols="6" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" class="mb-4 px-0" style="display: flex; justify-content: center;">
          <CardProductsMobile :itemProduct='item' />
        </v-col>
      </v-row>
      <v-row justify="center" class="my-6">
        <v-pagination
        color="#3EC6B6"
        v-model="pageNumber"
        :length="pageMax"
        :total-visible="MobileSize ? 5 : 7"
        class="paginationStyle"
        @input="pageChange($event)"
        ></v-pagination>
      </v-row>
      <!-- <a-row type="flex" :gutter="[16, 8]">
        <a-col :span="24" style="text-align:center">
          <span class="display-1">{{header}}</span>
        </a-col>
        <a-col :span="24">
          <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} รายการสำหรับ "{{ header }}"</span>
        </a-col>
        <a-col :span="24" style="padding: 0; margin-top: -20px;">
          <a-divider></a-divider>
        </a-col>
        <div v-if="AllProduct.length !== 0">
          <a-col :span="24" :md="4" :sm="12" :xs="24" v-for="(item,index) in AllProduct" :key="index">
            <CardProducts :itemProduct="item" />
          </a-col>
        </div>
        <div v-else>
          <h2>ยังไม่มีรายการสินค้า{{ header }}</h2>
        </div>
      </a-row> -->
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
const FakeData = []
for (let i = 0; i < 48; i++) {
  FakeData.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsMobile: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      path: process.env.VUE_APP_DOMAIN,
      limit: 48,
      pathShopSale: '',
      header: '',
      FakeData,
      overlay2: false,
      productCount: null,
      AllProduct: [],
      pageMax: null,
      current: 1,
      pageSize: 48,
      companyId: '',
      typeProduct: '',
      RowUserData: '',
      articleId: '',
      sellerId: '',
      items: [
        {
          category_name: this.$t('ShopPage.Title'),
          disabled: false,
          color: '#636363',
          href: '/'
        }
      ],
      BannerFlashSale: null
    }
  },
  watch: {
    async $route (to, from) {
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page

      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          const newPage = parseInt(getIDToParams)

          // ตรวจสอบว่า pageNumber ถูกต้องก่อนเรียก pageChange()
          if (newPage !== this.pageNumber) {
            this.pageNumber = newPage
            this.pageChange(newPage)
          }
        }
      }
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    this.typeProduct = this.$route.params.data
    // this.pageNumber = parseInt(this.$route.query)
    // console.log('pageNumber111', this.pageNumber)
    // this.header = this.$router.currentRoute.params.data
    // console.log('this.header------->', this.$route.query)
    // this.AllProduct = JSON.parse(Decode.decode(localStorage.getItem('itemShop')))
    // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
    this.oneData = []
    if (localStorage.getItem('oneData') !== null) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RowUserData = 'ext_buyer'
    }
    if (localStorage.getItem('SetRowCompany') !== null) {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      this.companyId = companyId.company.company_id
      // console.log('this.companyId', this.companyId)
    } else if (this.RowUserData === 'sale_order') {
      this.companyId = localStorage.getItem('PartnerID')
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
    } else if (this.RowUserData === 'sale_order_no_JV') {
      this.companyId = '-1'
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
    } else {
      this.companyId = ''
    }
    if (this.typeProduct === 'new_product') {
      this.header = this.$t('ShopPage.NewArrival')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllNewProductInShop', this.getAllNewProductInShop)
      this.getAllNewProductInShop()
    } else if (this.typeProduct === 'best_seller') {
      this.header = this.$t('ShopPage.BestSellers')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllBestSellerInShop', this.getAllBestSellerInShop)
      this.getAllBestSellerInShop()
    } else if (this.typeProduct === 'recommended_product') {
      this.header = this.$t('ShopPage.Recommended')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllRecomenProductShopInShop', this.getAllRecomenProductShopInShop)
      this.getAllRecomenProductShopInShop()
    } else if (this.typeProduct === 'all_product') {
      this.typeProduct = 'all_product'
      this.header = this.$t('ShopPage.AllProducts')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        // console.log('sale_order_no_JV---->', this.pathShopSale)
        // this.items.push({
        //   category_name: this.header,
        //   disabled: true,
        //   color: '#3EC6B6',
        //   href: this.pathShopSale
        // })
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllProductShopInShop', this.getAllProductShopInShop)
      this.getAllProductShopInShop()
    } else if (this.typeProduct === 'sale_product') {
      this.header = this.$t('ShopPage.DiscountedProducts')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllSaleProductInShop', this.getAllSaleProductInShop)
      this.getAllSaleProductInShop()
    } else if (this.typeProduct === 'normal_product') {
      this.header = this.$t('ShopPage.GeneralProducts')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllNormalProductInShop', this.getAllNormalProductInShop)
      this.getAllNormalProductInShop()
    } else if (this.typeProduct === 'out_product') {
      this.header = this.$t('ShopPage.OutOfStock')
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllOutProductInShop', this.getAllOutProductInShop)
      this.getAllOutProductInShop()
    } else if (this.typeProduct === 'article') {
      this.header = 'สินค้าในบทความ'
      if (this.RowUserData === 'article') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      this.$EventBus.$on('getDataListDetailArticle', this.getDataListDetailArticle)
      this.getDataListDetailArticle('action')
    } else if (this.typeProduct === 'flashSaleShop') {
      this.header = 'Flash Sale'
      if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.items = [
          {
            category_name: this.$t('ShopPage.Title'),
            disabled: false,
            color: '#636363',
            href: this.pathShopSale
          },
          {
            category_name: this.header,
            disabled: true,
            color: '#3EC6B6',
            href: this.pathShopSale
          }]
      } else {
        this.items.push({
          category_name: this.header,
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        })
      }
      // this.$EventBus.$on('getAllBestSellerInShop', this.getAllBestSellerInShop)
      this.getProductFlashSale()
      this.$EventBus.$on('getProductFlashSale', this.getProductFlashSale)
    }
    // console.log('===========>', this.header)
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      // var repeatOrder = []
      // var product = ''
      // if (this.AllProduct.length !== 0) {
      //   for (const item of this.AllProduct) {
      //     // console.log('item------>', item)
      //     product = {
      //       ...item,
      //       link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id)
      //     }
      //     // console.log('product', product)
      //     repeatOrder.push(product)
      //   }
      // }
      // return repeatOrder
      return this.AllProduct
    }
  },
  methods: {
    pageChange (val) {
      window.scrollTo(0, 0)
      val = this.pageNumber
      // this.pageNumber = val
      this.$router.push(`/ListShopProduct/${this.typeProduct}?page=${val}`).catch(() => {})
      if (this.typeProduct === 'article') {
        this.$router.push(`/ListShopProduct/${this.typeProduct}?ArticleID=${this.articleId}&SellerShopID=${this.sellerId}&page=${val}`).catch(() => {})
      }
      // console.log('pageChange====>', this.typeProduct)
      if (this.typeProduct === 'all_product') {
        // this.$EventBus.$on('getAllProductShopInShop', this.getAllProductShopInShop)
        this.getAllProductShopInShop()
      } else if (this.typeProduct === 'sale_product') {
        this.getAllSaleProductInShop()
      } else if (this.typeProduct === 'normal_product') {
        this.getAllNormalProductInShop()
      } else if (this.typeProduct === 'out_product') {
        this.getAllOutProductInShop()
      } else if (this.typeProduct === 'new_product') {
        this.getAllNewProductInShop()
      } else if (this.typeProduct === 'best_seller') {
        this.getAllBestSellerInShop()
      } else if (this.typeProduct === 'recommended_product') {
        this.getAllRecomenProductShopInShop()
      } else if (this.typeProduct === 'article') {
        this.getDataListDetailArticle('action')
      } else if (this.typeProduct === 'flashSaleShop') {
        // this.getProductFlashSale()
        console.log('voke')
        this.$router.push(`/ListShopProduct/${this.typeProduct}?&ID=${this.sellerId}&page=${val}`).catch(() => {})
        this.getProductFlashSale()
      }
    },
    gotoBannerPage (val) {
      var dataR = JSON.parse(localStorage.getItem('roleUser')).role
      if (dataR === 'sale_order' || dataR === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathShopSale }).catch(() => {})
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    async getAllNewProductInShop () {
      if (this.header === this.$t('ShopPage.NewArrival')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataNewProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataNewProduct = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'new',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataNewProduct = {
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'new',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        // console.log('dataNewProduct', dataNewProduct)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataNewProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },

    async getAllBestSellerInShop () {
      if (this.header === this.$t('ShopPage.BestSellers')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataBestProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataBestProduct = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'best-seller',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataBestProduct = {
            // token: '',
            // role_user: 'ext_buyer',
            // begin_search_type: 'component',
            // begin_search_details: {
            //   custom_user_ID: '1',
            //   what_component: 'best_seller',
            //   component_id: ''
            // },
            // user_detail: {
            //   company_id: this.id_company
            // },
            // seller_shop_id: shopId
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'best-seller',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataBestProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },

    async getAllRecomenProductShopInShop () {
      if (this.header === this.$t('ShopPage.Recommended')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataRecomProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataRecomProduct = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'recommend',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataRecomProduct = {
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: 'recommend',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataRecomProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },
    async getAllSaleProductInShop () {
      if (this.header === this.$t('ShopPage.DiscountedProducts')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAllSaleProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAllSaleProduct = {
            // token: this.oneData.user.access_token,
            // role_user: dataRole.role,
            // begin_search_type: 'component',
            // begin_search_details: {
            //   custom_user_ID: '1',
            //   what_component: 'all_product',
            //   component_id: ''
            // },
            // user_detail: {
            //   company_id: this.id_company
            // },
            // seller_shop_id: shopId
            category: '',
            orderBy: '',
            status_product: 'sale',
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            page: this.pageNumber,
            limit: this.limit
          }
        } else {
          dataAllSaleProduct = {
            // token: '',
            // role_user: 'ext_buyer',
            // begin_search_type: 'component',
            // begin_search_details: {
            //   custom_user_ID: '1',
            //   what_component: 'all_product',
            //   component_id: ''
            // },
            // user_detail: {
            //   company_id: this.id_company
            // },
            // seller_shop_id: shopId
            category: '',
            orderBy: '',
            status_product: 'sale',
            role_user: 'ext_buyer',
            company_id: this.id_company,
            page: this.pageNumber,
            seller_shop_id: shopId,
            limit: this.limit
          }
        }
        // console.log('dataAllSaleProduct', dataAllSaleProduct)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataAllSaleProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },
    async getAllNormalProductInShop () {
      if (this.header === this.$t('ShopPage.GeneralProducts')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAllNormalProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAllNormalProduct = {
            category: '',
            orderBy: '',
            status_product: 'general',
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            page: this.pageNumber,
            seller_shop_id: shopId,
            limit: this.limit
          }
        } else {
          dataAllNormalProduct = {
            category: '',
            orderBy: '',
            status_product: 'general',
            page: this.pageNumber,
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            limit: this.limit
          }
        }
        // console.log('dataAllNormalProduct', dataAllNormalProduct)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataAllNormalProduct)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        // console.log('response', response)
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
    },
    async getAllOutProductInShop () {
      if (this.header === this.$t('ShopPage.OutOfStock')) {
        this.pageNumber = parseInt(this.$route.query.page)
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAllOutProduct
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAllOutProduct = {
            page: this.pageNumber,
            category: '',
            orderBy: '',
            status_product: '',
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            limit: this.limit
          }
        } else {
          dataAllOutProduct = {
            page: this.pageNumber,
            category: '',
            orderBy: '',
            status_product: '',
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            limit: this.limit
          }
        }
        // console.log('dataAllOutProduct', dataAllOutProduct)
        await this.$store.dispatch('actionsOutOfStockProducts', dataAllOutProduct)
        var response = await this.$store.state.ModuleShop.stateOutOfStockProducts
        // console.log('response', response)
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              normalOutStock: true
            }))
          } else {
            this.AllProduct = []
          }
        } else {
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
      // console.log('this.AllProduct', this.AllProduct)
    },
    async getAllProductShopInShop () {
      // console.log('testttt')
      this.$store.commit('openLoader')
      this.pageNumber = parseInt(this.$route.query.page)
      if (this.header === this.$t('ShopPage.AllProducts')) {
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        var shopId = localStorage.getItem('shopID')
        var dataAll
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          dataAll = {
            role_user: dataRole !== '' ? dataRole.role : 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: '',
            limit: this.limit,
            page: this.pageNumber
          }
        } else {
          dataAll = {
            role_user: 'ext_buyer',
            company_id: this.id_company,
            seller_shop_id: shopId,
            orderBy: '',
            category: '',
            status_product: '',
            limit: this.limit,
            page: this.pageNumber
          }
        }
        // console.log('dataAll', dataAll)
        await this.$store.dispatch('actionsSelectCategoryShopList', dataAll)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          this.$store.commit('closeLoader')
          if (response.query_result.length !== 0) {
            var refCode = this.$route.query.ref_code
            this.AllProduct = []
            this.AllProduct = response.query_result
            this.AllProduct = this.AllProduct.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          }
        } else {
          this.$store.commit('closeLoader')
          this.AllProduct = []
        }
        // this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        this.pageMax = parseInt(response.pagination.max_page)
      }
    },
    async getDataListDetailArticle (action) {
      if (action === 'action') {
        this.$store.commit('openLoader')
      }
      if (this.articleId === '' && this.sellerId === '') {
        this.articleId = this.$route.query.Article
        this.sellerId = this.$route.query.ID
      }
      const payload = {
        article_id: this.articleId,
        seller_shop_id: this.sellerId,
        Page: this.pageNumber,
        Limit: this.limit
      }
      await this.$store.dispatch('actionsListProductAritcleV2', payload)
      var response = await this.$store.state.ModuleArticle.stateListProductAritcleV2
      if (response.result === 'SUCCESS') {
        // console.log('response', response)
        if (response.data.data.length !== 0) {
          var refCode = this.$route.query.ref_code
          this.AllProduct = []
          this.AllProduct = response.data.data
          this.AllProduct = this.AllProduct.map((item, index) => ({
            ...item,
            link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
            ref_code: refCode
          }))
        } else {
          this.AllProduct = []
        }
        this.pageMax = parseInt(response.data.pagination.max_page)
        this.pageNumber = parseInt(this.$route.query.page)
      }
      if (action === 'action') {
        this.$store.commit('closeLoader')
      }
    },
    async getProductFlashSale () {
      this.$store.commit('openLoader')
      this.sellerId = this.$route.query.ID
      this.pageNumber = parseInt(this.$route.query.page)
      var data = {
        seller_shop_id: parseInt(this.sellerId),
        page: this.pageNumber,
        limit: this.limit,
        role: this.RowUserData
      }
      await this.$store.dispatch('actionsGetFlashSale', data)
      var response = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
      if (response.result === 'SUCCESS') {
        // var id = JSON.parse(Decode.decode(localStorage.getItem('flashSaleId')))
        var productFlashSale = response.data.data
        if (productFlashSale.length !== 0) {
          this.AllProduct = []
          this.AllProduct = productFlashSale[0].product_list
          this.BannerFlashSale = productFlashSale[0].image_path
          // this.AllProduct = this.AllProduct.map((item, index) => ({
          //   ...item,
          //   link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
          // }))
        } else {
          this.AllProduct = []
        }
        this.pageMax = parseInt(response.data.max_page)
        // console.log(this.pageMax, 'max')
        // this.pageNumber = parseInt(this.$route.query.page)
        // console.log(this.pageNumber, 'number')
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style scoped>
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  white-space: nowrap;
  list-style-type: none;
  margin-bottom: 24px;
  /* padding: 8px 0px 8px 75px !important; */
}
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #3EC6B6 !important;
}
.v-breadcrumbs li .v-icon {
  color: #3EC6B6 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
</style>
