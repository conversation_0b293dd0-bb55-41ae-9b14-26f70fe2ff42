<template>
  <v-col cols="12" :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : 'pa-0'">
    <!-- <v-col> -->
    <!-- <v-container :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : ''"> -->
    <v-breadcrumbs :items="RoleUser == 'sale_order' || RoleUser === 'sale_order_no_JV' ? itemsSale : itemsPage" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
        <template v-slot:divider>
          <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
        </template>
        <template v-slot:item="{ item }">
          <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
            <span style="z-index:1;" :style="{ color: item.disabled === true ? '#3EC6B6' : '#636363', 'font-size': '16px' }">{{ item.text
            }}</span>
          </v-breadcrumbs-item>
        </template>
    </v-breadcrumbs>
    <!-- </v-container> -->
    <!-- </v-col> -->
    <v-col cols="12" md="12" :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : ''">
      <v-container :class="MobileSize || IpadSize || IpadProSize? 'pa-0': ''">
        <v-card
          max-height="100%"
          max-width="100%"
          elevation="0"
          :class="IpadSize? 'mt-6': 'mt-1'"
          v-if="bannerShop !== ''"
        >
          <v-row no-gutters>
            <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 325px;': MobileSize ? 'position: relative; height: 150px;' :IpadProSize?'position: relative; height: 350px;': 'position: relative; height: 455px;'">
              <v-carousel
                style="position: absolute; border-radius: 12px;"
                cycle
                hide-delimiter-background
                show-arrows-on-hover
                :show-arrows="bannerShop.length > 1 ? true : false"
                hide-delimiters
              >
                <div v-if="bannerShop.length !== 0">
                  <v-carousel-item
                    eager
                    v-for="(slide, i) in bannerShop"
                    :key="i"
                  >
                  <div v-if="slide.type === 'banner'">
                      <v-img
                        :src="slide.path"
                        width="100%"
                        contain
                      ></v-img>
                    </div>
                    <div v-else-if="slide.type === 'vdo'">
                      <Media
                        :kind="'video'"
                        :isMuted="false"
                        :src="[slide.path]"
                        :autoplay="false"
                        :controls="true"
                        :loop="true"
                        @pause="handle"
                        :ref="'video_player'"
                        width="630"
                        height="400"
                      ></Media>
                    </div>
                    <div v-else-if="slide.type === 'image'">
                      <v-img
                        :src="slide.path !== '' ? slide.path : require('@/assets/ImageINET-Marketplace/Shop/NoImgStore.png')"
                        :height="IpadSize ? '220' : IpadProSize? '290' : MobileSize && bannerShop.length !== 0 ? '120' : MobileSize && bannerShop.length === 0 ? '120': '380'"
                        width="100%"
                        style="object-fit: fill; background-size: cover; background-position: center"
                      ></v-img>
                    </div>
                  </v-carousel-item>
                </div>
                <div v-else>
                  <!-- <v-carousel
                    :height=" IpadSize ? '290': MobileSize ? '120':'500'"
                  > -->
                    <v-carousel-item eager>
                      <div>
                        <v-img
                          src="@/assets/ImageINET-Marketplace/Shop/NoImgStore.png"
                          :height="IpadSize? '100%': '100%'"
                          width="100%"
                          contain
                        ></v-img>
                      </div>
                    </v-carousel-item>
                  <!-- </v-carousel> -->
                </div>
              </v-carousel>
              <v-col :style="IpadSize? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                <v-row no-gutters>
                  <v-col dense no-gutters :cols="MobileSize? 2: 3" :md="IpadProSize ? 4 : 3" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0 pr-7'" style="text-align: center;">
                    <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '70' : '244'" v-if="shop_logo === ''">
                      <v-img
                        src="@/assets/ImageINET-Marketplace/Shop/Store2.png"
                        style="border: 10px solid #ffffff"
                      ></v-img>
                    </v-avatar>
                    <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '70' : '244'" v-else>
                      <v-img
                        :src="`${shop_logo}?=${time}`"
                        contain
                        style="border: 5px solid #ffffff"
                      ></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col :cols="MobileSize? 10: 9" :md="IpadProSize ? 8 : 9" :class="IpadSize? 'pb-0 mb-15':MobileSize ? '' : 'pt-3'">
                    <v-row dense no-gutters :justify="IpadSize? 'center': 'start'" v-if="!MobileSize" :class="IpadSize? 'pa-0': 'pa-2 pl-0 pr-1'">
                      <v-col cols="12" md="12" sm="12" xs="12"
                        v-if="
                          dataShop.shop_name_th !== '' &&
                          dataShop.shop_name_th !== null
                        "
                      >
                        <span v-if="dataShop.shop_name_en !== '' && dataShop.shop_name_en !== null">
                          <p :class="IpadSize? 'mb-0 ipadfont' :'mb-1 Deskfont'"
                            style=" color: white;"
                          >
                            {{ dataShop.shop_name_en }} <br>
                          </p>
                          <p :class="IpadSize? 'mb-0 ipadfonts' :'mb-2'"
                            style="color: white;"
                          >
                            {{ dataShop.shop_name_th }}
                          </p>
                        </span>
                        <span v-else>
                          <p :class="IpadSize? 'mb-0 ipadfont' : 'mt-2 mb-6 Deskfont'"
                           style="color: white;"
                          >
                            {{ dataShop.shop_name_th }} <br>
                          </p>
                        </span>
                        <v-row no-gutters >
                          <v-col cols="1" class="py-0" style="border-right: solid 1px #FFFFFF;">
                            <v-row no-gutters>
                              <div class="d-flex" :class="IpadProSize? 'mr-1':IpadSize ? '':'mr-3'">
                              <!-- <v-avatar :size="IpadSize? '15': IpadProSize? '20': '28'"> -->
                                <!-- <v-img :class="IpadSize || IpadProSize? 'classFBLineSizeIpad':'classFBLineSizeDesk'"
                                  src="@/assets/ImageINET-Marketplace/Shop/FB.png"
                                ></v-img> -->
                              <!-- </v-avatar> -->
                              </div>
                              <div class="d-flex">
                              <!-- <v-avatar :size="IpadSize? '15': IpadProSize? '20': '28'"> -->
                                <!-- <v-img :class="IpadSize || IpadProSize? 'classFBLineSizeIpad':'classFBLineSizeDesk'"
                                  src="@/assets/ImageINET-Marketplace/Shop/LINE.png"
                                ></v-img> -->
                              <!-- </v-avatar> -->
                              </div>
                            </v-row>
                          </v-col>
                          <!-- <v-col cols="10">
                            <v-row no-gutters>
                              <div class="d-flex ml-2">
                                <v-row no-gutters>
                                  <v-img style="width: 24px; height: 24px;" src="@/assets/ImageINET-Marketplace/Shop/iconShop/Star.png"></v-img>
                                  <span v-if="starShop !== 0" style="color: white;" class=" starFont ml-2">คะแนนร้านค้า : {{Number(parseFloat(starShop)).toLocaleString(undefined, {minimumFractionDigits: 1})}} / 5.0</span>
                                  <span v-else style="color: white;" class=" starFont ml-2">คะแนนร้านค้า : ยังไม่มีคะแนน </span>
                                </v-row>
                              </div>
                            </v-row>
                          </v-col> -->
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" xs="12" :class="IpadProSize ? 'mt-12' : 'mt-6'">
                        <v-row v-if="!MobileSize">
                          <!-- <v-row
                            v-if="RowUserData === 'purchaser' && statusShop"
                            dense
                          >
                        {{ chackpartner }} {{ contact }} {{ statusOpenPartner }} {{ ParnertPerminsion }}
                            <v-btn small text color="#3EC6B6" @click="gotoShopDetail()">
                              <v-icon small class="pr-1">mdi-chat</v-icon> แชท
                            </v-btn>
                            <v-divider v-if="chackpartner || statusOpenPartner && ParnertPerminsion" vertical class="ml-2 mr-2"></v-divider>
                            {{ !chackpartner }} {{ contact }} {{ statusOpenPartner }} {{ ParnertPerminsion }}
                            <v-btn
                              v-if="
                                !chackpartner &&
                                contact &&
                                statusOpenPartner &&
                                ParnertPerminsion
                              "
                              dense
                              dark
                              outlined
                              color="#3EC6B6"
                              style="
                                height: 28px;
                                margin-left: 8px;
                                margin-top: 5px;
                              "
                              @click="OpenModalPartner()"
                            >
                              ยื่นคำขอคู่ค้า
                            </v-btn>
                            <v-btn v-if="chackpartner && !status_btn && contact" class="ml-1" dense dark outlined
                              color="#3EC6B6" style="height: 28px" @click="ModalCreateQuotation()">
                              <v-icon small>mdi-pencil</v-icon> สร้างใบเสนอราคา
                            </v-btn>
                            <v-btn v-if="chackpartner && !status_btn && contact" class="ml-1" dense dark outlined
                              color="#3EC6B6" style="height: 28px" @click="OpenModalSpecialPrice()">
                              <v-icon small>mdi-file-document</v-icon> ร้องขอราคาพิเศษ
                            </v-btn>
                            <v-chip
                              v-if="chackpartner && status_btn && contact"
                              small
                              class="ma-1"
                              color="#FCF0DA"
                              text-color="#FAAD14"
                              >รออนุมัติ
                              รออนุมัติ {{chackpartner}}{{status_btn}}{{contact}}
                            </v-chip>
                            <v-chip
                              v-if="!chackpartner && !contact"
                              small
                              class="ma-1"
                              color="#FCF0DA"
                              text-color="#FAAD14"
                            >
                              {{!chackpartner}} {{!contact}} {{statusOpenPartner}} {{ParnertPerminsion}}
                              ติดต่อร้านค้า(ยื่นคำขอคู่ค้า)
                            </v-chip>
                            <span
                              v-else
                              class="ml-2 ma-1"
                              style="
                                color: #636363;
                                font-size: 14px;
                                font-weight: 400;
                              "
                            >
                              <a
                                v-if="response_coupon !== 0"
                                @click="openModalCoupons()"
                              >
                                <U>คูปองส่วนลดจากร้านค้า</U></a
                              >
                            </span>
                            <v-btn small text color="#3EC6B6" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-account-plus</v-icon> ติดตาม</v-btn>
                          </v-row> -->
                          <!-- <span
                            v-else
                            class="ml-3"
                            style="
                              color: #636363;
                              font-size: 14px;
                              font-weight: 400;
                            "
                          >
                            <a
                              v-if="response_coupon !== 0"
                              @click="openModalCoupons()"
                            >
                              <U>คูปองส่วนลดจากร้านค้า</U></a
                            >
                          </span> -->
                          <v-col v-if="!MobileSize && !IpadSize" :cols="IpadProSize ? 7 : 8" class="mt-12">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-card-title v-if="!MobileSize && !IpadSize && !IpadProSize" v-on="on" v-bind="attrs" style="font-weight: 700; font-size: x-large; margin-left: -20px;">
                                {{ groupName | truncate(48, '...') }}
                                <v-btn color="primary" fab x-small @click="copyShortLink()" class="ml-2" style="margin-top: -2px; box-shadow: none;"><v-icon>mdi-share</v-icon></v-btn>
                              </v-card-title>
                              <v-card-title v-else v-on="on" v-bind="attrs" style="font-weight: 700; font-size: 20px; margin-left: -40px;">
                                {{ groupName | truncate(40, '...') }}
                                <v-btn color="primary" fab width="26" height="26" @click="copyShortLink()" class="ml-2" style="margin-top: -2px; box-shadow: none;"><v-icon size="18">mdi-share</v-icon></v-btn>
                              </v-card-title>
                            </template>
                            <div style="max-width: 500px;"><span style="max-width: 480px; display: inline-block; word-wrap: break-word; white-space: normal;">{{ groupName }}</span>
                            </div>
                        </v-tooltip>
                            <!-- <v-card-title style="font-weight: 700; font-size: x-large; margin-left: -20px;">{{ groupName }}</v-card-title> -->
                          </v-col>
                          <v-col v-else-if="IpadSize" cols="12" class="mt-5">
                            <v-tooltip top>
                              <template v-slot:activator="{ on, attrs }">
                                <v-card-title class="pr-0 pb-1" v-on="on" v-bind="attrs" style="font-weight: 700; font-size: 16px; margin-left: -26px;">
                                  {{ groupName | truncate(48, '...') }}
                                  <v-btn color="primary" fab width="22" height="22" @click="copyShortLink()" class="ml-1" style="margin-top: -2px; box-shadow: none;"><v-icon size="18">mdi-share</v-icon></v-btn>
                                </v-card-title>
                              </template>
                              <div style="max-width: 300px;"><span style="max-width: 290px; display: inline-block; word-wrap: break-word; white-space: normal;">
                                {{ groupName }}</span></div>
                            </v-tooltip>
                            <!-- <v-card-title style="font-weight: 700; font-size: large;">{{ groupName }}</v-card-title> -->
                            <div class="d-flex justify-start pl-2" style="margin-left: -26px;">
                              <div style="display: flex; gap: 5px;" class="pl-2">
                                <v-img src="@/assets/ImageINET-Marketplace/Shop/imageStore.png" width="16px" height="16px"/>
                                <span style="font-weight: 500; font-size: 14px;">{{ $t('GroupShopHomepage.TitleAllShops') }}</span>
                              </div>
                              <div style="display: flex; gap: 5px; align-items: center;">
                                <span style="font-weight: 700; color: #3EC6B6; font-size: 14px; text-align: right;" class="ml-1">{{ objGropShop.length }}</span><span style="font-weight: 500; color: #989898; font-size: 14px;" class="mr-1">{{ $t('GroupShopHomepage.TitleShop') }}</span>
                              </div>
                            </div>
                          </v-col>
                          <v-col v-if="!MobileSize && !IpadSize" :cols="IpadProSize ? 5 : 4" class="mt-12 pr-4" style="display: flex; justify-content: flex-end; align-items: center;">
                            <div style="display: flex; gap: 10px;">
                              <v-img src="@/assets/ImageINET-Marketplace/Shop/imageStore.png" width="24px" height="24px"/>
                              <span style="font-weight: 500; font-size: large; width: 120px;">{{ $t('GroupShopHomepage.TitleAllShops') }}</span>
                            </div>
                            <div style="display: flex; gap: 10px; align-items: center;">
                              <span style="font-weight: 700; color: #3EC6B6; font-size: x-large; text-align: right; width: 70px;">{{ objGropShop.length }}</span><span style="font-weight: 500; color: #989898; font-size: large;">{{ $t('GroupShopHomepage.TitleShop') }}</span>
                            </div>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                    <v-row v-else>
                      <v-col v-if="MobileSize" cols="12" class="pl-4 pr-0 d-flex flex-column" style="margin-top: 32px;">
                        <div>
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-card-title v-on="on" v-bind="attrs" class="ml-1 pb-2" style="font-weight: 700; font-size: x-small; line-height: 1.5; padding-right: 0;">
                                {{ groupName | truncate(48, '...') }}
                                <v-btn color="primary" fab width="18" height="18" @click="copyShortLink()" class="ml-2" style="margin-top: -2px; box-shadow: none;"><v-icon size="12">mdi-share</v-icon></v-btn>
                              </v-card-title>
                            </template>
                            <div style="max-width: 250px;"><span style="max-width: 240px; display: inline-block; word-wrap: break-word; white-space: normal;">
                              {{ groupName }}</span></div>
                          </v-tooltip>
                        </div>
                        <div class="d-flex justify-start pl-2" style="">
                          <div style="display: flex; gap: 5px;" class="pl-3">
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/imageStore.png" width="12px" height="12px"/>
                            <span style="font-weight: 500; font-size: x-small;">{{ $t('GroupShopHomepage.TitleAllShops') }}</span>
                          </div>
                          <div style="display: flex; gap: 5px; align-items: center;">
                            <span style="font-weight: 700; color: #3EC6B6; font-size: x-small; text-align: right;" class="ml-1">{{ objGropShop.length }}</span><span style="font-weight: 500; color: #989898; font-size: x-small;" class="mr-1">{{ $t('GroupShopHomepage.TitleShop') }}</span>
                          </div>
                        </div>
                        <!-- <v-card-title style="font-weight: 700; font-size: x-small; line-height: 1.5; padding-right: 0;">{{ groupName }}</v-card-title> -->
                      </v-col>
                    </v-row>
                    <!-- mobile name shop -->
                    <v-row dense no-gutters justify="center" v-if="MobileSize">
                      <!-- <v-col
                        cols="12"
                        md="12"
                        sm="12"
                        xs="12"
                        class="pl-8"
                        v-if="
                          dataShop.shop_name_en !== '' &&
                          dataShop.shop_name_en !== null
                        "
                      >
                        <p
                          class="mb-1"
                          style="
                            color: white;
                            font-weight: bold;
                            font-size: 10px;
                            height: 10px;
                            text-transform: uppercase;
                          "
                        >
                          {{ dataShop.shop_name_en }}
                        </p>
                      </v-col> -->
                      <!-- <v-col
                        class="pl-8"
                        col="12"
                        sm="12"
                        v-if="
                          dataShop.shop_name_th !== '' &&
                          dataShop.shop_name_th !== null
                        "
                      >
                        <p
                          class="mb-1"
                          style="
                            color: white;
                            font-weight: bold;
                            font-size: 10px;
                            height: 10px;
                            text-transform: uppercase;
                          "
                        >
                          {{ dataShop.shop_name_th }}
                        </p>
                        <v-col cols="12" :class="dataShop.shop_name_en === '' || dataShop.shop_name_en === null ? 'pa-0 pt-5': 'pa-0 pt-2'">
                          <v-row no-gutters>
                            <v-col cols="2" class="pa-0 mb-3" style="border-right: solid 1px #C4C4C4;">
                              <v-avatar size="16">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/Shop/FBMobile.png"
                                ></v-img>
                              </v-avatar>
                              <v-avatar size="16">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/Shop/LineMobile.png"
                                ></v-img>
                              </v-avatar>
                            </v-col>
                            <v-col cols="10" class="pa-0">
                              <v-row no-gutters>
                                <div class="d-flex ml-1 mt-1">
                                    <v-img style="width: 16px; height: 16px;" src="@/assets/ImageINET-Marketplace/Shop/iconShop/Star.png"></v-img>
                                    <span style="color: #333333; font-size: 10px;margin-top: 2px !important;" class=" ml-1 mb-2"> คะแนนร้านค้า : {{starShop !== 0? Number(parseFloat(starShop)).toLocaleString(undefined, {minimumFractionDigits: 1}) + ' / 5.0':'ยังไม่มีคะแนน' }}</span>
                                </div>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-col> -->
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-col>
            <!-- แท็บ สินค้า ร้านค้า -->
            <v-col cols="12" style="margin-top: 10vh;">
              <v-row dense class="pl-2">
                <v-tabs
                  v-model="tab"
                  background-color="transparent"
                  @change="changeTab(tab)"
                >
                  <v-tab
                  v-for="item in itemTab"
                  :key="item"
                  >
                  <span style="font-size: medium; font-weight: 600;">{{ item }}</span>
                  </v-tab>
                </v-tabs>
              </v-row>
            </v-col>
            <v-row dense align-content="center" justify="center" class="mt-2" v-if="tab === 0">
              <v-col cols="12" md="12" sm="12" :class="MobileSize? 'pt-0':'mb-0'">
                <div v-if="showSkeletonLoader === true">
                  <v-row dense class="pt-12">
                    <v-col cols="6" md="2" sm="3" xs="4" v-for="item in 6" :key="item">
                      <v-skeleton-loader
                        type="image, list-item-two-line"
                      ></v-skeleton-loader>
                    </v-col>
                  </v-row>
                </div>
                <div v-else>
                  <v-row  class="pt-10 pb-6" no-gutters justify="center" v-if="AllProduct.length === 0"><span style="font-size: large; font-weight: 600;">{{ $t('GroupShopHomepage.NoProduct') }}</span></v-row>
                  <v-row dense justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize">
                    <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" v-for="(item, index) in paginated" :key="index" :class="IpadProSize ? 'px-0' : IpadSize ? 'px-1' : ''" style="display: flex; justify-content: center;">
                      <CardProducts :itemProduct='item' />
                    </v-col>
                  </v-row>
                  <v-row dense justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && !MobileSize && IpadSize" style="display: flex; justify-content: center;">
                    <v-col cols="12" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" class="px-1">
                      <CardProductsResponsive :itemProduct='item' />
                    </v-col>
                  </v-row>
                  <v-row dense justify="start" class="pt-4 px-1"  v-if="AllProduct.length !== 0 && MobileSize && !IpadSize" style="display: flex; justify-content: center;">
                    <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index" class="pt-4">
                      <CardProductsResponsive :itemProduct='item' />
                    </v-col>
                  </v-row>
                  <v-row justify="center" class="my-6 pb-2" v-if="AllProduct.length !== 0">
                    <v-pagination
                    color="#3EC6B6"
                    v-model="pageNumberProduct"
                    :length="pageMaxProduct"
                    :total-visible="MobileSize ? 5 : 7"
                    class="paginationStyle"
                    @input="pageChangeProduct($event)"
                    ></v-pagination>
                  </v-row>
                </div>
              </v-col>
            </v-row>
            <v-col cols="12" md="12" sm="12" :class="MobileSize? 'pt-0':'mb-0'" v-if="tab === 1">
              <v-row dense :class=" MobileSize ? 'mt-5' : IpadProSize || IpadSize ? 'mt-5' : 'mt-10'">
                <v-col cols="12" md="12" :class="IpadSize ? 'pl-8 pt-2':IpadProSize? 'pl-5 pt-16 mt-2':MobileSize? 'pt-4 mt-3':'pt-3 pl-14'" v-if="dataShop.shop_description !== null">
                      <!-- <v-img :width="IpadSize? '18': MobileSize ? '18' : '24'" :height="IpadSize? '18': MobileSize ? '18' : '24'" src="@/assets/ImageINET-Marketplace/Shop/shop1.png">
                      </v-img>
                      <span class="pl-2" :style="IpadSize ? 'font-weight: bold; font-size: 15px':MobileSize ? 'font-weight: bold; font-size: 14px' : 'font-weight: bold; font-size: 20px'">เกี่ยวกับร้านค้า</span><br> -->
                      <v-row>
                      <v-col :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6' : 'd-flex align-items-end'">
                        <v-text-field
                        v-model="search"
                        dense
                        outlined
                        hide-details=""
                        :placeholder="$t('GroupShopHomepage.SearchByShopName')"
                        @input="pageNumber = 1"
                        style="font-size: small; display: flex; align-items: end;"
                        >
                          <v-icon slot="append" color="#3EC6B6">mdi-magnify </v-icon>
                        </v-text-field>
                      </v-col>
                      <v-col :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6' : IpadSize ? 'pr-5' : IpadProSize ? 'pr-5' : 'pr-6'" style="display: flex; gap: 10px;" :style="MobileSize ? 'justify-content: start;' : 'justify-content: end;'">
                        <div>
                          <!-- <span style="font-size: small;">จังหวัด</span> -->
                          <v-autocomplete
                            dense
                            filled
                            outlined
                            color="#3EC6B6"
                            v-model="dataProvince"
                            @change="selectDataProvince"
                            :items="itemProvinceAll"
                            :label="$t('GroupShopHomepage.Province')"
                            item-text="name"
                            item-value="value"
                            hide-details
                            class="mt-1"
                            style="font-size: small; font-weight: 500; height: 40px; color: #3EC6B6;"
                            :style="MobileSize ? 'max-width: 240px;' : IpadSize ? 'max-width: 160px;' : ''"
                          >
                          </v-autocomplete>
                        </div>
                        <div>
                          <!-- <span style="font-size: small;">หมวดหมู่</span> -->
                          <v-autocomplete
                            dense
                            filled
                            outlined
                            color="#3EC6B6"
                            v-model="dataCategory"
                            @change="selectDataCategory"
                            :items="itemCategoryAll"
                            :label="$t('GroupShopHomepage.Category')"
                            item-text="name"
                            item-value="id"
                            hide-details
                            class="mt-1"
                            style="font-size: small; font-weight: 500; height: 40px; color: #3EC6B6;"
                            :style="MobileSize ? 'max-width: 240px;' : IpadSize ? 'max-width: 160px;' : ''"
                          >
                          </v-autocomplete>
                        </div>
                      </v-col>
                      <v-col cols="12" class="mt-4">
                        <span style=" color: #9A9A9A; font-size: 16px; line-height: 22.4px; font-weight: 400;" :class="MobileSize ? 'pl-4' : 'pa-0'" >{{ $t('GroupShopHomepage.AllFound') }} {{GroupStore.length !== 0 ? GroupStore.length : 0}} {{ $t('GroupShopHomepage.TitleShop') }}</span>
                      </v-col>
                      <!-- <v-col cols="12" md="8" sm="4" xs="12">
                        <div style="float: right; display: inline-flex; height: 44px;" > -->
                        <!-- <v-img class="mt-3" :width="IpadSize? '18': MobileSize ? '18' : '24'" :height="IpadSize? '18': MobileSize ? '18' : '24'" src="@/assets/ImageINET-Marketplace/Shop/iconShop/height.png">
                      </v-img> -->
                      <!-- <v-icon color="#3EC6B6" @click="search2 = true" v-if="search2 === null" >mdi-sort</v-icon>
                      <v-icon color="#3EC6B6"  @click="search2 = !search2" v-if="search2 && search2 !== null">mdi-sort-descending</v-icon>
                      <v-icon color="#3EC6B6" @click="search2 = !search2" v-if="!search2 && search2 !== null">mdi-sort-ascending</v-icon>
                      <span class="pl-2 mt-3" v-if="search2 && search2 !== null" :style="IpadSize ? 'font-weight: bold; font-size: 15px':MobileSize ? 'font-weight: bold; font-size: 14px' : 'font-weight: 400; font-size: 16px'">ล่าสุด</span>
                      <span class="pl-2 mt-3" v-if="!search2 && search2 !== null" :style="IpadSize ? 'font-weight: bold; font-size: 15px':MobileSize ? 'font-weight: bold; font-size: 14px' : 'font-weight: 400; font-size: 16px'">แรกสุด</span> -->
                      <!-- <v-divider  vertical class="d-sm-block mx-4"></v-divider> -->
                      <!-- <span class="pl-2 mt-3 mr-3" :style="IpadSize ? 'font-weight: bold; font-size: 15px':MobileSize ? 'font-weight: bold; font-size: 14px' : 'font-weight: 500; font-size: 16px'">คะแนน :</span> -->
                      <!-- <v-select
                            v-model="search3"
                            :items="filterName"
                            item-text="text"
                            item-value="id"
                            placeholder="ทั้งหมด"
                            dense
                            :style="MobileSize ? 'width: 90px !important;' : 'width: 120px !important;'"
                            outlined
                            class="mt-1"
                            :rules="Rules.selectType"
                          ></v-select> -->
                    <!-- </div>
                      </v-col> -->
                    </v-row>
                  <!-- <div :class="MobileSize? 'py-4': 'pt-2'">
                    <span :style="IpadSize ? 'font-size: 13px;':MobileSize ? 'font-size: 14px' : 'font-size: 18px;'">{{dataShop.shop_description}}</span>
                  </div> -->
                  <!-- <p  style=" font-size: 13px;margin-Top:-10px">{{ dataShop.shop_description }}</p> -->
                  <!-- <v-textarea
                    style="font-size: 18px;"
                    auto-grow
                    rows="2"
                    hide-details
                    readonly
                    :value="dataShop.shop_description"
                    rounded
                  ></v-textarea> -->
                  <!-- <span style=" font-size: 13px;margin-Top:-10px" v-html="dataShop.shop_description"></span> -->
                </v-col>
                <!-- <v-col>
                  <p  style=" font-size: 13px;">{{ dataShop.shop_description }}</p>
                </v-col> -->
                <!-- <v-col cols="12" md="12" class="pt-4">
                  <p style="font-weight: bold; font-size: 13px">
                    รายการสินค้าทั้งหมด
                  </p>
                </v-col> -->
              </v-row>
            </v-col>
          </v-row>
        <v-row v-if="GroupStore.length !== 0 && tab === 1" :justify="MobileSize ? 'center' : 'start'" :class="MobileSize ? 'px-3' : 'mr-auto ml-auto'">
          <v-col cols="6" sm="4" md="3" lg="2" xl="3" v-for="(item, index) in GroupStore.slice(this.indexStart, this.indexEnd)" :key="index" align="start" :class="MobileSize ? 'pt-3' : 'pt-8'">
        <v-hover v-slot="{ hover }">
          <v-card outlined class="rounded-lg px-2 custom-card" height="100%" :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;" @click="gotoShopDetail(item)" :href="item.shopPathLink">
            <v-row dense align="center" justify="center">
              <v-col cols="12" md="12" class="d-flex justify-center align-center" :class="MobileSize ? 'mt-2 mx-1' : IpadSize ? 'mt-2 mx-1' : IpadProSize ? 'mt-3 mx-1' : 'mt-4 mx-2'" >
                <v-avatar v-if="item.shop_profile !== null || item.shop_profile !== undefined" rounded :size="MobileSize ? '24vw' : IpadSize ? '18vw' : IpadProSize ? '16vw' : '10vw'">
                <v-img  v-lazyload :src="item.shop_profile" contain style="width: 100%;"/>
                </v-avatar>
                <v-avatar v-else rounded size="24vw">
                <v-img :src="require('@/assets/ImageINET-Marketplace/Shop/Store.png')" />
                </v-avatar>
              </v-col>
              <v-col cols="12" md="12" class="d-flex justify-center">
                <v-card-title v-if="MobileSize" style="font-weight: 700; font-size: small; line-height: 1;" class="text-truncate d-inline-block">
                  {{ item.shop_name }}
                </v-card-title>
                <v-card-title v-if="IpadSize || IpadProSize" style="font-weight: 700; font-size: medium; line-height: 1;" class="text-truncate d-inline-block">
                  {{ item.shop_name }}
                </v-card-title>
                <v-tooltip v-if="!MobileSize && !IpadSize && !IpadProSize" top>
                  <template v-slot:activator="{ on, attrs }">
                    <v-card-title v-on="on" v-bind="attrs" style="font-weight: 700; font-size: medium; line-height: 1;" class="text-truncate d-inline-block">
                      {{ item.shop_name }}
                    </v-card-title>
                  </template>
                  <div style="max-width: 250px;"><span style="max-width: 240px; display: inline-block; word-wrap: break-word; white-space: normal;">{{ item.shop_name }}</span></div>
                </v-tooltip>
                <!-- <v-card-title class="mb-0 text-truncate d-inline-block " style="font-size: small; font-weight: 700; line-height: 1;">{{ item.shop_name }}</v-card-title> -->
              </v-col>
                <!-- <v-col cols="12" md="12" class="px-0 pb-0 pb-0" >
            <v-row no-gutters justify="center">
            </v-row>
          </v-col> -->
      <!-- <v-row no-gutters justify="center" style="margin-top:  -10px;"> -->
              <!-- <v-col cols="2" md="2" class="mb-3 mt-1 pr-0 mr-0 pl-2"> -->
                <!-- <v-avatar rounded size="16"> -->
                <!-- <v-img  v-if="item.shop_profile !== null || item.shop_profile !== undefined" v-lazyload :src="item.shop_profile" /> -->
                <!-- <v-img  src="@/assets/favourites1.png" />
                </v-avatar> -->
              <!-- </v-col>
              <v-col cols="10" md="10"> -->
                <!-- <p class="mt-2 ml-0 pl-0 mb-0 text-truncate d-inline-block " style="max-width: 125px; font-size: 12px; font-weight: 400;">คะแนนร้านค้า : {{ item.shop_rate_score === null ? 'ไม่มีคะแนน' :  Number(parseFloat(item.shop_rate_score)).toLocaleString(undefined, {minimumFractionDigits: 1})}} / 5.0</p> -->
              <!-- </v-col> -->
            <!-- </v-row> -->
            </v-row>
          </v-card>
        </v-hover>
      </v-col>
      </v-row>
        <a-row
          v-else-if="GroupStore.length === 0 && tab === 1"
          :class="MobileSize? 'pt-16 mt-16':''"
          type="flex"
          justify="center"
          style="margin-top: 10%; margin-bottom: 10%"
        >
          <template>
            <a-empty
            v-if="search === '' && search3 === ''"
              :image="require('@/assets/NoGroupShop.png')"
              :image-style="{ height: '150px', marginBottom: '40px' }"
            >
              <h1 slot="description"
              style="
                color: #636363;
                font-size: 18px;
                line-height: 25.2px;
                font-weight: 600;
              ">{{ $t('GroupShopHomepage.NoShops') }}
              </h1>
              <br />
              <h3 slot="description" style="
                color: #9A9A9A;
                font-size: 16px;
                line-height: 22.4px;
                font-weight: 400;
              ">{{ $t('GroupShopHomepage.NoItemsInGroup') }}</h3>
              <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
            </a-empty>
            <a-empty
            v-if="search !== '' || search3 !== ''"
              :image="require('@/assets/searchGroupShop.png')"
              :image-style="{ height: '150px', marginBottom: '40px' }"
            >
              <h1 slot="description"
              style="
                color: #636363;
                font-size: 18px;
                line-height: 25.2px;
                font-weight: 600;
              ">{{ $t('GroupShopHomepage.ShopNotFound') }}
              </h1>
              <br />
              <h3 slot="description" style="
                color: #9A9A9A;
                font-size: 16px;
                line-height: 22.4px;
                font-weight: 400;
              ">{{ $t('GroupShopHomepage.ShopSearchNotFound') }}</h3>
              <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
            </a-empty>
          </template>
        </a-row>
        <v-row justify="center" class="my-6 pb-4" v-if="GroupStore.length !== 0 && tab === 1">
          <!-- <v-col cols="12" md="12" sm="12" class="my-6"> -->
          <v-pagination
            color="#3EC6B6"
            v-model="pageNumber"
            :length="pageMax"
            :total-visible="MobileSize ? 5 : 7"
            class="paginationStyle"
            @change="pageChange()"
          > </v-pagination>
          <!-- </v-col> -->
        </v-row>
        </v-card>
      </v-container>
    </v-col>
  </v-col>
</template>

<script>
import { Decode, Encode } from '@/services'
import Media from '@dongido/vue-viaudio'
import { Row, Empty } from 'ant-design-vue'
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
// const NewProduct = []
// const bestSeller = []
export default {
  metaInfo () {
    return {
      title: this.groupName + ' | Nex Gen Commerce',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        {
          vmid: 'description',
          name: 'description',
          content: this.groupName
        },
        {
          property: 'og:site_name',
          vmid: 'og:site_name',
          name: 'site_name',
          content: 'https://testinetmarket.one.th'
        },
        {
          property: 'og:title',
          vmid: 'og:title',
          name: 'title',
          content: this.groupName + ' | Nex Gen Commerce'
        },
        {
          property: 'og:description',
          vmid: 'og:description',
          content: this.groupName
        },
        { property: 'og:type', vmid: 'og:type', content: 'website' },
        { property: 'og:url', vmid: 'og:url', content: this.message }
        // { property: 'og:image', name: 'image', content: this.primaryImage },
        // { property: 'og:image:width', content: '640' },
        // { property: 'og:image:height', content: '480' }
      ]
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    Media,
    'a-row': Row,
    'a-empty': Empty,
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      filterName: [
        { id: 0, text: 'ค่าเริ่มต้น' },
        { id: 1, text: '1 คะแนน' },
        { id: 2, text: '2 คะแนน' },
        { id: 3, text: '3 คะแนน' },
        { id: 4, text: '4 คะแนน' },
        { id: 5, text: '5 คะแนน' }
      ],
      itemsSale: [],
      currentPage: 1,
      pageListcustomer: 1,
      pageMaxListcustomer: null,
      customerSaleData: [],
      cusCode: 0,
      dialogChooseTypeCustomer: false,
      SaleID: 0,
      cusType: '',
      // titleCompanyAddress: '',
      showCustomer: false,
      userdetail: [],
      dialogListAddressCustomer: false,
      page: '',
      EditAddressDetail: '',
      titleAddress: '',
      search: '',
      namePartner: '',
      dataCusList: [],
      dialogListPartner: false,
      dialogFunnelMobile: false,
      cancelPartner: false,
      starShop: 0,
      emailCheck: true,
      dialogSuccess: false,
      dialogConfirm: false,
      nameDoc: [],
      itemsDes: '',
      sellerShopID: '',
      categoryid: '',
      categoryID: '',
      priceList: '',
      typeList: '',
      current: 1,
      pageSize: 24,
      pageMax: 1,
      pageMaxAddress: null,
      totalAddress: 0,
      pageAddress: 1,
      DataCategory: [],
      headersCategory: [
        {
          sortable: false,
          value: 'category_name'
        }
        // { text: 'รหัสการสั่งซื้อ', value: 'payment_credit_term_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'จำนวนเงิน', value: 'total_amount', filterable: false, sortable: false, align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      changePage: '',
      changeText: '',
      priceText: '',
      // itemsDescription: [
      //   'หน้าแรก', 'สินค้าทั้งหมด/หมวดหมู่ทั้งหมด'
      // ],
      RoleUser: '',
      itemsPage: [
        {
          text: this.$t('GroupShopHomepage.Home'),
          disabled: false,
          href: '/'
        },
        {
          text: this.$t('GroupShopHomepage.ShopType'),
          disabled: true,
          href: 'ShoppageUI'
        }
      ],
      dialog_partner_mobile: false,
      dialog_partner: false,
      selectBudget: '',
      selectCutBudget: '',
      email: '',
      checkDoc: false,
      oneData: [],
      DataFile: [],
      ModalCreate: false,
      selectgroup: {
        document: []
      },
      can_request_partner: true,
      pdftofile: '',
      company_id: '',
      user_id: '',
      Detail: {
        product_file: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      ParnertPerminsion: false,
      chackpartner: false,
      shopname: 'ร้านขายของ 1',
      shopImage: '',
      statusOpenPartner: true,
      dialogPartner: false,
      contact: true,
      response_doc: [],
      shop_logo: '',
      tabs: null,
      Path: process.env.VUE_APP_DOMAIN,
      items: ['สินค้าทั้งหมด', 'สินค้าขายดี'],
      RowUserData: '',
      // roleCustomer: '',
      NewProduct: [],
      // bestSeller,
      NormalProduct: [],
      OutProduct: [],
      SaleProduct: [],
      AllProduct: [],
      BestSeller: [],
      Recommended: [],
      AllChangeProduct: [],
      CouponsIteam: [],
      dataShop: [],
      bannerShop: [],
      newsShop: [],
      PathImage: process.env.VUE_APP_IMAGE,
      status: false,
      datapartner: [],
      // editor: ClassicEditor,
      dialog_pdf: false,
      purchaser: '',
      status_btn: false,
      statusShop: true,
      statusPartner: true,
      response_coupon: 0,
      time: '',
      mail: '',
      i: 0,
      dataRole: '',
      companyId: null,
      lazy: false,
      Rules: {
        Email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ]
      },
      objGropShop: [],
      groupName: '',
      search2: null,
      search3: '',
      path: process.env.VUE_APP_DOMAIN,
      itemCategoryAll: [],
      itemProvinceAll: [],
      dataCategory: '',
      dataProvince: '',
      itemTab: [
        this.$t('GroupShopHomepage.Product'), this.$t('GroupShopHomepage.Shop')
      ],
      showSkeletonLoader: false,
      limit: 48,
      tokenstatus: '',
      pageMaxProduct: null,
      productCount: null,
      tab: 0,
      currentProduct: 1
    }
  },
  async created () {
    this.$store.commit('openLoader')
    this.$EventBus.$emit('getPath')
    this.pathRoute = this.$router.currentRoute.path.substring(1)
    this.message = this.Path + this.pathRoute
    // var countpath = window.location.href.split('-')
    var countpath = this.$router.currentRoute.params.data.split('-')
    // var idshop = this.pathRoute.split('-')
    // alert(countpath[countpath.length - 1])
    var idshop = countpath[countpath.length - 1]
    if (localStorage.getItem('roleUser') !== null) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
      // this.RoleUser = 'sale_order_no_JV'
    } else {
      this.RoleUser = 'ext_buyer'
    }
    if (this.RoleUser === 'sale_order' || this.RoleUser === 'sale_order_no_JV') {
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
      this.pathshop = this.pathShopSale.path.toString()
      this.itemsSale = [
        {
          text: this.$t('GroupShopHomepage.Home'),
          disabled: false,
          href: this.pathshop
        },
        {
          text: this.$t('GroupShopHomepage.Shop'),
          disabled: true,
          href: this.pathshop
        }
      ]
    }
    if (localStorage.getItem('ClickgoToCheckOut') !== null) {
      if (localStorage.getItem('list_Company_detail') !== null) {
        var auth = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
        if (
          auth.can_use_function_in_company.partner === undefined ||
          auth.can_use_function_in_company.partner === 0 ||
          auth.can_use_function_in_company.partner === null
        ) {
          this.ParnertPerminsion = false
        } else {
          this.ParnertPerminsion = true
        }
      } else {
        this.ParnertPerminsion = false
      }
    }
    localStorage.setItem('shopID', idshop)
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.user_id = this.oneData.user.user_id
      if (localStorage.getItem('roleUser') !== null) {
        this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
        // this.RowUserData = 'sale_order_no_JV'
        // this.RoleUser = 'sale_order_no_JV'
        // ฟิกกก
      }
    }
    // const companyId = []
    if (localStorage.getItem('SetRowCompany') !== null) {
      const companyId = JSON.parse(
        Decode.decode(localStorage.getItem('SetRowCompany'))
      )
      // console.log('companyId11111', companyId)
      this.purchaser = companyId.position.purchaser
      this.companyId = companyId.company.company_id
      this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
    } else if (this.RowUserData === 'sale_order') {
      this.companyId = localStorage.getItem('PartnerID')
      // console.log('created', this.companyId)
      this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      // this.dialogListPartner = true
      // console.log('this.sellerShopID01', this.sellerShopID)
    } else if (this.RowUserData === 'sale_order_no_JV') {
      this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      if (localStorage.getItem('sale_order_customer') !== null) {
        var type = JSON.parse(localStorage.getItem('sale_order_customer')).role
        if (type !== 'JV_customer') {
          this.cusCode = localStorage.getItem('cusCode')
        } else {
          this.cusCode = 0
        }
      }
      this.getDetailSale(this.sellerShopID)
      this.showCustomer = true
    } else {
      if (localStorage.getItem('shopID') !== null) {
        this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      } else {
        // console.log(this.$router.currentRoute.params.data)
        this.sellerShopID = this.$router.currentRoute.params.data
      }
      this.purchaser = ''
      this.companyId = ''
    }
    // console.log('this.RowUserData', this.RowUserData)
    this.categoryID = 1
    this.changePage = ''
    // console.log('this.companyId', this.companyId)
    await this.GetProductType(this.changePage)
    await this.getGroupStore()
    await this.getCategoryAll()
    await this.getProvinceAll()
    this.showSkeletonLoader = true
    const lastPart = decodeURIComponent(this.$route.path.split('/').pop())
    const parts = lastPart.split('-')
    var checkGroupshopID = parts.pop()
    var checkGroupshopName = parts.join(' ')
    // console.log(checkGroupshopID, ' ', checkGroupshopName)
    if (checkGroupshopName === null || checkGroupshopID === null) {
      this.$router.push('/allGroupShop?page=1').catch(() => {})
    } else {
      await this.getDataProductGroupShop()
      this.$EventBus.$on('getDataProductGroupShop', this.getDataProductGroupShop)
    }
  },
  watch: {
    pageListcustomer (val) {
      // console.log('valpageAddress', val)
      this.changePageCustomer()
    },
    GroupStore (newVal) {
      this.pageMax = Math.max(1, Math.ceil(newVal.length / 24))
    }
    // search2 (val) {
    //  if (val && val !== null) {
    //   const up = this.objGropShop.sort((a, b) => {
    //       var startDay = new Date(a)
    //       var endDay = new Date(b)
    //       return startDay - endDay
    //     })
    //  }
    // if (!val && val !== null) {

    //  }
    // }

  },
  computed: {
    GroupStore () {
      var x
      if (this.search !== '') {
        x = this.objGropShop.filter(e => {
          // console.log('sss', this.search, e.shop_name)
          return e.shop_name.toLowerCase().includes(this.search.toLowerCase())
        })
      } else if (this.search3 !== '' && this.search3 !== 0) {
        x = this.objGropShop.filter(e => {
          return parseInt(e.shop_rate_score) === parseInt(this.search3)
        })
      } else {
        return this.objGropShop
      }
      return x
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    DesktopSize () {
      const { lg } = this.$vuetify.breakpoint
      return !!lg
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    paginated () {
      return this.AllProduct
    },
    pageNumberProduct: {
      get () {
        return this.currentProduct || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.currentProduct = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    }
    // ,
    // pageMax () {
    //   return Math.max(1, Math.ceil(this.GroupStore.length / 24))
    // }
    // paginated () {
    //   return this.GroupStore.slice(this.indexStart, this.indexEnd)
    // }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$EventBus.$on('role', this.getRole)
    // this.$on('hook:beforeDestroy', () => {
    //   this.$EventBus.$off('role')
    //   this.$EventBus.$off('EditAddressComplete')
    // })
  },
  methods: {
    gotoShopDetail (val) {
      const shopCleaned = encodeURIComponent(val.shop_name.replace(/\s/g, '-'))
      this.$router
        .push({ path: `/shoppage/${shopCleaned}-${val.seller_shop_id}` })
        .catch(() => {})
    },
    async getGroupStore () {
      this.$store.commit('openLoader')
      var data = {
        group_id: this.sellerShopID,
        province: this.dataProvince ? this.dataProvince : '',
        category_id: this.dataCategory ? this.dataCategory : ''
      }
      await this.$store.dispatch('actionsDetailGroupSeller', data)
      var response = await this.$store.state.ModuleHompage.stateDetailGroupSeller
      // console.log('zzzz', response)
      if (response.result === 'SUCCESS') {
        this.current = 1
        this.pageNumber = 1
        var temp = await {
          type: 'image',
          path: response.data.group_shop_banner_path
        }
        await this.bannerShop.push(temp)
        // console.log('this.bannerShop', this.bannerShop)
        this.shop_logo = await response.data.group_shop_media_path
        this.groupName = await response.data.group_name
        this.objGropShop = await response.data.seller_shop_data.map(el => {
          return {
            group_shop_media_path: response.data.group_shop_media_path,
            group_shop_banner_path: response.data.group_shop_banner_path,
            seller_shop_id: el.seller_shop_id,
            shop_name: el.shop_name,
            shop_profile: el.shop_profile,
            shop_rate_score: el.shop_rate_score,
            shopPathLink: this.path + 'shoppage/' + encodeURIComponent(`${el.shop_name.replace(/\s/g, '-')}-${el.seller_shop_id}`)
          }
        })
        this.pageMax = parseInt(this.objGropShop.length / 24) === 0 ? 1 : Math.ceil(this.objGropShop.length / 24)
        // console.log('this.objGropShop', this.objGropShop)
      } else {
      }
      this.$store.commit('closeLoader')
    },
    async getCategoryAll () {
      await this.$store.dispatch('actionsGetCategory', 'all')
      const response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.code === 200) {
        // console.log('response', response)
        this.itemCategoryAll = [{ name: this.$t('GroupShopHomepage.All'), id: '' }]
        response.data.forEach(item => {
          this.itemCategoryAll.push({ name: item.category_name, id: item.id })
        })
        // console.log('itemCategoryAll', this.itemCategoryAll)
      }
    },
    async getProvinceAll () {
      await this.$store.dispatch('actionsGetProvinceAll')
      const response = await this.$store.state.ModuleHompage.stateGetProvinceAll
      if (response.code === 200) {
        // console.log('response', response)
        this.itemProvinceAll = [{ name: this.$t('GroupShopHomepage.All'), value: '' }]
        response.data.forEach(item => {
          this.itemProvinceAll.push({ name: item.name, value: item.name })
        })
      }
    },
    async getDataProductGroupShop () {
      this.$store.commit('openLoader')
      this.showSkeletonLoader = true
      var companyID = ''
      var dataBest = ''
      const lastPart = decodeURIComponent(this.$route.path.split('/').pop())
      var groupshopID = lastPart.split('-').pop()
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        this.tokenstatus = this.oneData.user.access_token
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      }
      if (this.dataRole.role !== 'sale_order') {
        dataBest = {
          orderBy: '',
          category: -1,
          status_product: -1,
          filter_group_seller_shop: groupshopID,
          limit: this.limit,
          page: this.pageNumberProduct,
          role_user: this.dataRole,
          company_id: companyID,
          seller_shop_id: -1
        }
      } else {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        dataBest = {
          orderBy: '',
          category: -1,
          status_product: -1,
          filter_group_seller_shop: groupshopID,
          limit: this.limit,
          page: this.pageNumberProduct,
          role_user: this.dataRole,
          company_id: companyID,
          seller_shop_id: -1
        }
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', dataBest)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      if (response.ok === 'y') {
        if (response.query_result !== 'No products ready to sell.') {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          this.pageMaxProduct = parseInt(response.pagination.max_page)
          this.pageNumberProduct = parseInt(response.pagination.current_page)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
      this.$store.commit('closeLoader')
    },
    async pageChangeProduct (val) {
      // console.log(val)
      this.pageNumberProduct = val
      await this.getDataProductGroupShop()
      this.currentProduct = val
    },
    selectDataCategory (value) {
      // console.log('selectDataCategory', value)
      this.dataCategory = value
      this.getGroupStore()
    },
    selectDataProvince (value) {
      // console.log('selectDataProvince', value)
      this.dataProvince = value
      this.getGroupStore()
    },
    getRole (role) {
      this.RoleUser = role
    },
    changePageCustomer () {
      // console.log('this.pageListcustomer', this.pageListcustomer)
      // this.pageListcustomer = this.currentPage
      this.getDetailSale()
    },
    reSet () {
      this.typeList = ''
      this.priceList = ''
    },
    confirm () {
      this.dialogPartner = false
      this.dialogConfirm = true
    },
    pageChange () {
      // console.log('pageChange')
      if (this.pageNumber !== 1) {
        // window.scrollTo(0, 0)
      }
    },
    GetProductPrice (text) {
      this.priceText = text
    },
    GetProductType (text) {
      this.typeList = text
      this.changePage = text
    },
    GetAllProducts (allItem, header) {
      if (allItem.length !== 0) {
        localStorage.setItem('itemShop', Encode.encode(allItem))
        this.$router.push(`/ListShopProduct/${header}?page=1`).catch(() => {})
      }
    },
    ModalCreateQuotation () {
      this.ModalCreate = true
    },
    closeModal () {
      this.$refs.formPartner.resetValidation()
      this.selectgroup = {
        document: []
      }
      this.dialogPartner = false
      this.email = ''
    },
    async changeTab (val) {
      this.tab = val
      if (this.tab === 0) {
        this.pageNumberProduct = 1
        this.search = ''
        await this.getDataProductGroupShop()
      } else if (this.tab === 1) {
        this.pageNumber = 1
        this.search = ''
        await this.getGroupStore()
      }
    },
    async copyShortLink () {
      this.$store.commit('openLoader')
      var link = window.location.href
      // console.log('window.location.href', window.location.href)
      link = link.split('?')[0]
      link = decodeURIComponent(link)
      // console.log('link', link)
      const payload = {
        link: link
      }
      // console.log('data item', item)
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}group_shop/generate_group_URL`,
        method: 'POST',
        data: payload
      }).then((response) => {
        this.shortLink = response.data.data.short_url
        // console.log('this.shortLink', this.shortLink)
      })
      navigator.clipboard.writeText(this.shortLink)
      this.$store.commit('closeLoader')
      this.$swal.fire({
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true,
        icon: 'success',
        html: '<h3>' + this.$t('GroupShopHomepage.CopyLinkSuccess') + '</h3>'
      })
    }
  }
}
</script>

<style scoped>
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 12px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 24px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 24px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 24px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.cardChooseType{
  padding-top: 35px;
  text-align: center;
  border-radius: 10px;
}
.cardChooseType:hover {
  transform: scale(1.05);
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
  border-bottom: 1px solid #3EC6B6 !important;
}
.chipcus {
  border-radius: 6px !important;
  /* background-color: #3EC6B6 !important;
  color: white !important; */
}
>>> .v-dialog {
    box-shadow: none;
}
.setbackground > .v-dialog .v-dialog--active {
  box-shadow: none !important;
}
.v-card-store {
  border-radius: 8px !important;
  border-color: #3EC6B6;
  border-width: 1px;
}
.v-card-storeIpad {
  width: 95px;
  height: 66px;
}
.v-card-storeIpadPro {
  width: 118px;
  height: 72px;
}
.v-card-storeMobile {
  width: 31%;
  height: 80%;
  margin-right: 4px;
  margin-bottom: 4px;
}
.textCardNumberMobile {
  font-size: 10px !important;
  font-weight: 700;
}
.textCardMobile {
  font-size: 10px !important;
  margin-top: 0%;
  margin-bottom: 0%;
  font-weight: 500;
  color: #636363;
  /* padding-top: 25%; */
  /* padding-right: 25%;
  padding-left: 25%; */
}
.v-card-storeDesk {
  max-width: 18%;
  width: 18%;
  height: 87px;
}
.v-chip-store {
  font-weight: 500;
  line-height: 22.4px;
  font-size: 16px;
}
.v-chip-storeMobile {
  font-weight: 400;
  line-height: 22.4px;
  font-size: 10px;
}
.classFBLineSizeIpad {
  height: 19px;
  width: 19px;
  max-width: 19px;
}
.classFBLineSizeDesk {
height: 28px;
width: 28px;
max-width: 28px;
}
>>>.cardProductIpad {
  width: 33.3%;
  max-width: 33.3%;
  flex-basis: 33.3%;
}
>>>.cardProductMobile {
  width: 50%;
  max-width: 50%;
  flex-basis: 20%;
}
>>>.cardProductDesk{
  width: 20%;
  max-width: 20%;
  flex-basis: 20%;
}
>>>.cardProductIpadPro {
  width: 25%;
  max-width: 25%;
  flex-basis: 20%;
}
>>>.cardProductIpadProFrist {
  width: 100%;
  max-width: 100%;
  /* flex-basis: 50%; */
}
>>> .cat:hover {
 cursor: pointer;
 background-color: #F0F0F0 !important;
}
>>> .v-input__slot .v-select__selection--comma {
  color: #000000;
}
.Deskfont {
  font-weight: bold;
  font-size: 24px;
  text-transform: uppercase;
}
.ipadfont {
  font-weight: bold;
  font-size: 16px;
  text-transform: uppercase;
}
.ipadfonts {
  font-weight: bold;
  font-size: 10px;
  text-transform: uppercase;
}
>>>.ChipWating  {
  /* left: 0.8%; */
  border-radius: 24px 0px 0px 24px !important;
}
>>>.ChipWatingMobile {
  left: 0.8%;
  border-radius: 24px 0px 0px 24px !important;
}
>>>.starFont {
  font-size: 18px;
  font-weight: 400;
  line-height: 25.2px;
}
>>> .colorTap .ant-tabs-ink-bar {
  border-bottom: 2px solid #3EC6B6 !important;
  background-color: #3EC6B6;
}
</style>
