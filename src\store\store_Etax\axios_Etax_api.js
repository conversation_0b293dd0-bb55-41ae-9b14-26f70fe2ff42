import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('ONEDATA NAAAa', oneData)
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async CheckTaxIDShop (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_tax_id`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckETaxInShop (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}etax/shop/doShopEtaxCredentialsExist_r2`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendAccessETax (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}etax/shop/setCredentials`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendAccessETaxR2 (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}etax/shop/setCredentials_r2`, val)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async LoginBySharedTokenEfac (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/login_by_shared_token_efac/${data}`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }

}
