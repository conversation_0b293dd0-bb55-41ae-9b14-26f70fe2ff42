<template>
  <v-container>
    <!-- Send Create Shop -->
    <v-dialog v-model="dialogSendCreateShop" width="495" persistent>
      <v-card style="background: #FFFFFF; border-radius: 12px;">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''"><b>อนุมัติการสร้างร้านค้า</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogSendCreateShop = !dialogSendCreateShop" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text style="text-align: center;">
            <v-row dense justify="center">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/sendCreateShop.png" contain max-height="168" max-width="212"></v-img>
            </v-row>
            <p style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" class="mt-2"><b>ข้อมูลร้านค้าสมบูรณ์แล้ว</b></p>
            <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #27AB9C;">รอรับสัญญาอิเล็กทรอนิก ทาง E-mail</p>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Create Shop -->
    <v-dialog v-model="dialogAwaitCreateShop" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogConfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลร้านค้า</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogConfirm()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmEditShop()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Create Shop -->
    <v-dialog v-model="dialogSuccessCreateShop" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeEditShop()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลร้านค้าเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeEditShop()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Fail Create Shop -->
    <v-dialog v-model="dialogFailCreateShop" width="495" persistent>
      <v-card style="background: #FFFFFF; border-radius: 12px;">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''"><b>อนุมัติการสร้างร้านค้า</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogFailCreateShop = !dialogFailCreateShop" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text style="text-align: center;">
            <v-row dense justify="center">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/failCreateShop.png" contain max-height="175" max-width="212"></v-img>
            </v-row>
            <p style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" class="mt-2"><b>ไม่สามารถแก้ไขร้านค้าของคุณได้</b></p>
            <p style="font-weight: 400; font-size: 14px; line-height: 0px; color: #D1392B;">เนื่องจาก <b>{{ messageError }}</b></p>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Dialog Upload Banner -->
    <v-dialog v-model="dialogOpenDialogUploadBanner" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัปโหลดหน้าปกร้านค้า</b></span>
              </v-col>
              <v-btn fab small @click="cancelUploadBanner()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-card-text style="text-align: center;" class="mt-6" v-if="Detail.shop_image_banner.length === 0">
                <span class="textUploadnameImage">(เพิ่มได้สูงสุด 6 รูปภาพ)</span>
                <v-row justify="center" class="pt-6" style="cursor: pointer;">
                  <v-col cols="12" align="center">
                    <v-card @click="onPickFile()" @drop.prevent="DropImage($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                      <v-card-text>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-file-input
                              v-model="DataImage"
                              :items="DataImage"
                              accept="image/jpeg, image/jpg, image/png"
                              @change="UploadImage()"
                              id="file_input"
                              multiple
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-img
                              src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                              width="143"
                              height="143"
                              contain
                            ></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="12" style="text-align: center;">
                              <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                              <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-text class="px-4" v-else>
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <v-card-text style="text-align: center;" v-if="Detail.shop_image_banner.length < 6">
                    <v-row justify="center" class="pt-6" style="cursor: pointer;">
                      <v-col cols="12" align="center">
                        <v-card @click="onPickFile()" @drop.prevent="DropImage($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" :width="MobileSize ? '100%' : '882'" :height="MobileSize ? '100%' : '83'" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                          <v-card-text>
                            <v-row>
                              <v-file-input
                                v-model="DataImage"
                                :items="DataImage"
                                accept="image/jpeg, image/jpg, image/png"
                                @change="UploadImage()"
                                id="file_input"
                                multiple
                                :clearable="false"
                                style="display:none"
                              >
                              </v-file-input>
                              <v-col cols="12" md="2" sm="2" align="center" class="mt-2">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                  width="32"
                                  height="32"
                                  contain
                                ></v-img>
                              </v-col>
                              <v-col cols="12" md="8" sm="8" :align="MobileSize ? 'center' : 'start'">
                                <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                              </v-col>
                              <v-col cols="12" md="2" sm="2" :align="MobileSize ? 'center' : 'start'" :class="MobileSize ? '' : 'mt-3'">
                                <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-text>
                    <v-row dense>
                      <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                      <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                      <v-spacer></v-spacer>
                      <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 6 รูปภาพ)</span>
                    </v-row>
                    <v-row dense>
                      <v-col cols="12" v-for="(item, index) in Detail.shop_image_banner" :key="index">
                        <v-row dense class="pt-4" style="height: 60px;">
                          <v-col cols="1" align="center">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                          </v-col>
                          <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === false">
                            <span class="textUpload" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                            <span style="line-height: 14px; font-weight: 400;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'">{{ Math.round(item.size / 1000) }} KB</span>
                          </v-col>
                          <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === true">
                            <span class="textUpload" style="color: #F5222D;" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                            <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                          </v-col>
                          <v-col cols="7" md="4" sm="4" :class="MobileSize ? 'pt-4' : ''">
                            <v-row justify="end" dense>
                              <v-btn :class="MobileSize ? 'px-0' : ''" :height="MobileSize ? '20' : '40'" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImage('Banner', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">แสดงตัวอย่าง</span></v-btn>
                              <v-btn :class="MobileSize ? 'px-0' : ''" width="37" :height="MobileSize ? '20' : '40'" text color="#636363"  @click="RemoveImage(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">ลบ</span></v-btn>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-title class="backgroundHead" style="position: relative; z-index: 0;">
          <v-row style="height: 120px;">
            <v-col class="d-flex justify-space-around" style="align-items: center;">
              <span :class="MobileSize ? 'title-mobile white--text' : 'white--text'"><b>อัปโหลดหน้าปกร้านค้า</b></span>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancelUploadBanner()" icon><v-icon color="white">mdi-close</v-icon></v-btn>
        </v-card-title>
        <v-card-text class="px-0" style="position: relative; z-index: 2; background: #FFFFFF; align-self: stretch;" v-if="Detail.shop_image.length === 0">
          <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
            <v-card-text style="text-align: center;">
              <span class="textUploadnameImage">(เพิ่มได้สูงสุด 6 รูปภาพ)</span>
              <v-row justify="center" class="pt-6" style="cursor: pointer;">
                <v-col cols="12" align="center">
                  <v-card @click="onPickFile()" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                    <v-card-text>
                      <v-col cols="12" md="12" class="mb-6">
                        <v-row justify="center" class="pt-10">
                          <v-file-input
                            v-model="DataImage"
                            :items="DataImage"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImage()"
                            id="file_input"
                            multiple
                            :clearable="false"
                            style="display:none"
                          >
                          </v-file-input>
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                            width="143"
                            height="143"
                            contain
                          ></v-img>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" class="mt-2">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="12" style="text-align: center;">
                            <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                            <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                            <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-card-text>
        <v-card-text class="px-4" style="position: relative; z-index: 2; background: #FFFFFF;" v-else>
          <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
            <v-card-text style="text-align: center;" v-if="Detail.shop_image.length < 6">
              <v-row justify="center" class="pt-6" style="cursor: pointer;">
                <v-col cols="12" align="center">
                  <v-card @click="onPickFile()" width="882" height="83" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                    <v-card-text>
                      <v-row>
                        <v-file-input
                          v-model="DataImage"
                          :items="DataImage"
                          accept="image/jpeg, image/jpg, image/png"
                          @change="UploadImage()"
                          id="file_input"
                          multiple
                          :clearable="false"
                          style="display:none"
                        >
                        </v-file-input>
                        <v-col cols="2" align="center" class="mt-2">
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                            width="32"
                            height="32"
                            contain
                          ></v-img>
                        </v-col>
                        <v-col cols="8" align="start">
                          <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                          <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                        </v-col>
                        <v-col cols="2" align="start" class="mt-3">
                          <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-text>
              <v-row dense>
                <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                <v-spacer></v-spacer>
                <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 6 รูปภาพ)</span>
              </v-row>
              <v-row dense>
                <v-col cols="12" v-for="(item, index) in Detail.shop_image" :key="index">
                  <v-row dense class="pt-4" style="height: 60px;">
                    <v-col cols="1" align="center">
                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                    </v-col>
                    <v-col cols="7" align="start" v-if="item.statusFail === false">
                      <span class="textUpload">{{ item.name.substring(0, 50) + '...' }}</span><br />
                      <span style="font-size: 12px; line-height: 14px; font-weight: 400;">{{ Math.round(item.size / 1000) }} KB</span>
                    </v-col>
                    <v-col cols="7" align="start" v-if="item.statusFail === true">
                      <span class="textUpload" style="color: #F5222D;">{{ item.name.substring(0, 50) + '...' }}</span><br />
                      <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                    </v-col>
                    <v-col cols="4">
                      <v-row justify="end" dense>
                        <v-btn height="40" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImage('Banner', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;">แสดงตัวอย่าง</span></v-btn>
                        <v-btn width="37" height="40" text color="#636363"  @click="RemoveImage(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-card-text> -->
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelUploadBanner()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" :disabled="disableUploadButton" height="40" class="white--text" @click="uploadToShow()">อัปโหลด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog Upload Advert -->
    <v-dialog v-model="dialogOpenDialogUploadAdvert" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัปโหลดรูปโฆษณา</b></span>
              </v-col>
              <v-btn fab small @click="cancelUploadAdvert()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-card-text style="text-align: center;" class="mt-6" v-if="Detail.shop_advert.length === 0">
                <span class="textUploadnameImage">(เพิ่มได้สูงสุด 5 รูปภาพ)</span>
                <v-row justify="center" class="pt-6" style="cursor: pointer;">
                  <v-col cols="12" align="center">
                    <v-card @click="onPickFileAdvert()" @drop.prevent="DropImageAdvert($event)" @dragover.prevent="dragoverAdvert = true" @dragenter.prevent="dragoverAdvert = true" @dragleave.prevent="dragoverAdvert = false" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                      <v-card-text>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-file-input
                              v-model="DataImageAdvert"
                              :items="DataImageAdvert"
                              accept="image/jpeg, image/jpg, image/png"
                              @change="UploadImageAdvert()"
                              @click="event => event.target.value = null"
                              id="file_input_Advert"
                              multiple
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-img
                              src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                              width="143"
                              height="143"
                              contain
                            ></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="12" style="text-align: center;">
                              <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                              <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-text class="px-4" v-else>
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <v-card-text style="text-align: center;" v-if="Detail.shop_advert.length < 5">
                    <v-row justify="center" class="pt-6" style="cursor: pointer;">
                      <v-col cols="12" align="center">
                        <v-card @click="onPickFileAdvert()" @drop.prevent="DropImageAdvert($event)" @dragover.prevent="dragoverAdvert = true" @dragenter.prevent="dragoverAdvert = true" @dragleave.prevent="dragoverAdvert = false" :width="MobileSize ? '100%' : '882'" :height="MobileSize ? '100%' : '83'" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                          <v-card-text>
                            <v-row>
                              <v-file-input
                                v-model="DataImageAdvert"
                                :items="DataImageAdvert"
                                accept="image/jpeg, image/jpg, image/png"
                                @change="UploadImageAdvert()"
                                @click="event => event.target.value = null"
                                id="file_input_Advert"
                                multiple
                                :clearable="false"
                                style="display:none"
                              >
                              </v-file-input>
                              <v-col cols="12" md="2" sm="2" align="center" class="mt-2">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                  width="32"
                                  height="32"
                                  contain
                                ></v-img>
                              </v-col>
                              <v-col cols="12" md="8" sm="8" :align="MobileSize ? 'center' : 'start'">
                                <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                              </v-col>
                              <v-col cols="12" md="2" sm="2" :align="MobileSize ? 'center' : 'start'" class="mt-3">
                                <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-text>
                    <v-row dense>
                      <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                      <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                      <v-spacer></v-spacer>
                      <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 5 รูปภาพ)</span>
                    </v-row>
                    <v-row dense>
                      <v-col cols="12" v-for="(item, index) in Detail.shop_advert" :key="index">
                        <v-row dense class="pt-4" style="height: 60px;">
                          <v-col cols="1" align="center">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                          </v-col>
                          <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === false">
                            <span class="textUpload" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                            <span style="line-height: 14px; font-weight: 400;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'">{{ Math.round(item.size / 1000) }} KB</span>
                          </v-col>
                          <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === true">
                            <span class="textUpload" style="color: #F5222D;" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                            <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                          </v-col>
                          <v-col cols="7" md="4" sm="4" :class="MobileSize ? 'pt-4' : ''">
                            <v-row justify="end" dense>
                              <v-btn :class="MobileSize ? 'px-0' : ''" :height="MobileSize ? '20' : '40'" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImage('Advert', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">แสดงตัวอย่าง</span></v-btn>
                              <v-btn :class="MobileSize ? 'px-0' : ''" width="37" :height="MobileSize ? '20' : '40'" text color="#636363"  @click="RemoveImageAdvert(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">ลบ</span></v-btn>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-title class="backgroundHead" style="position: relative; z-index: 0;">
          <v-row style="height: 120px;">
            <v-col class="d-flex justify-space-around" style="align-items: center;">
              <span :class="MobileSize ? 'title-mobile white--text' : 'white--text'"><b>อัปโหลดรูปโฆษณา</b></span>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancelUploadAdvert()" icon><v-icon color="white">mdi-close</v-icon></v-btn>
        </v-card-title>
        <v-card-text class="px-0" style="position: relative; z-index: 2; background: #FFFFFF;" v-if="Detail.shop_advert.length === 0">
          <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
            <v-card-text style="text-align: center;">
              <span class="textUploadnameImage">(เพิ่มได้สูงสุด 5 รูปภาพ)</span>
              <v-row justify="center" class="pt-6" style="cursor: pointer;">
                <v-col cols="12" align="center">
                  <v-card @click="onPickFileAdvert()" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                    <v-card-text>
                      <v-col cols="12" md="12" class="mb-6">
                        <v-row justify="center" class="pt-10">
                          <v-file-input
                            v-model="DataImageAdvert"
                            :items="DataImageAdvert"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImageAdvert()"
                            @click="event => event.target.value = null"
                            id="file_input_Advert"
                            multiple
                            :clearable="false"
                            style="display:none"
                          >
                          </v-file-input>
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                            width="143"
                            height="143"
                            contain
                          ></v-img>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" class="mt-2">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="12" style="text-align: center;">
                            <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                            <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                            <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-card-text>
        <v-card-text class="px-4" style="position: relative; z-index: 2; background: #FFFFFF;" v-else>
          <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
            <v-card-text style="text-align: center;" v-if="Detail.shop_advert.length < 5">
              <v-row justify="center" class="pt-6" style="cursor: pointer;">
                <v-col cols="12" align="center">
                  <v-card @click="onPickFileAdvert()" width="882" height="83" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                    <v-card-text>
                      <v-row>
                        <v-file-input
                          v-model="DataImageAdvert"
                          :items="DataImageAdvert"
                          accept="image/jpeg, image/jpg, image/png"
                          @change="UploadImageAdvert()"
                          @click="event => event.target.value = null"
                          id="file_input_Advert"
                          multiple
                          :clearable="false"
                          style="display:none"
                        >
                        </v-file-input>
                        <v-col cols="2" align="center" class="mt-2">
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                            width="32"
                            height="32"
                            contain
                          ></v-img>
                        </v-col>
                        <v-col cols="8" align="start">
                          <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                          <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                        </v-col>
                        <v-col cols="2" align="start" class="mt-3">
                          <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-text>
              <v-row dense>
                <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                <v-spacer></v-spacer>
                <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 5 รูปภาพ)</span>
              </v-row>
              <v-row dense>
                <v-col cols="12" v-for="(item, index) in Detail.shop_advert" :key="index">
                  <v-row dense class="pt-4" style="height: 60px;">
                    <v-col cols="1" align="center">
                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                    </v-col>
                    <v-col cols="7" align="start" v-if="item.statusFail === false">
                      <span class="textUpload">{{ item.name.substring(0, 50) + '...' }}</span><br />
                      <span style="font-size: 12px; line-height: 14px; font-weight: 400;">{{ Math.round(item.size / 1000) }} KB</span>
                    </v-col>
                    <v-col cols="7" align="start" v-if="item.statusFail === true">
                      <span class="textUpload" style="color: #F5222D;">{{ item.name.substring(0, 50) + '...' }}</span><br />
                      <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                    </v-col>
                    <v-col cols="4">
                      <v-row justify="end" dense>
                        <v-btn height="40" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImage('Advert', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;">แสดงตัวอย่าง</span></v-btn>
                        <v-btn width="37" height="40" text color="#636363"  @click="RemoveImageAdvert(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-card-text> -->
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelUploadAdvert()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" :disabled="disableUploadButtonAdvert" height="40" class="white--text" @click="uploadAdvert()">อัปโหลด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog to Show Image Big -->
    <v-dialog v-model="dialogShowImage" width="100%" style="height: 90vh;" persistent>
      <v-card width="100%" height="90vh" elevation="0" style="background: rgba(0, 0, 0, 0.50);">
        <v-card-text>
          <v-row dense>
            <v-col cols="12">
              <v-row dense class="pt-4">
                <v-icon color="#FFFFFF" class="mr-2" @click="bactToModalImage()">mdi-arrow-left</v-icon>
                <v-img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" max-width="30" max-height="30" contain></v-img>
                <span class="textBigImage pt-1">{{ imagetoBig.name }}</span>
              </v-row>
            </v-col>
            <v-col cols="12" align="center">
              <v-img id="imgBig" :src="imagetoBig.path" :width="setWidth" :height="setHeight"></v-img>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row justify="center" dense>
            <div style="width: 128px; height: 40px; border-radius: 25px; background: #333; text-align: center;">
              <v-btn icon width="40" height="40" class="mr-2" @click="ZoomOut()"><v-icon color="#FFFFFF" size="24">mdi-minus</v-icon></v-btn>
              <v-icon color="#A1A1A1">mdi-magnify-minus-outline</v-icon>
              <v-btn icon width="40" height="40" class="ml-2" @click="ZoomIn()"><v-icon color="#FFFFFF" size="24">mdi-plus</v-icon></v-btn>
            </div>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog เงื่อนไขระบบการชำระเงิน Payment  -->
    <v-dialog v-model="dialogPaymentCondition" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัตราค่าบริการระบบชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="dialogPaymentCondition = !dialogPaymentCondition" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-card-text class="mt-6">
                <v-expansion-panels v-model="panel" readonly multiple accordion class="elevation-0">
                  <v-expansion-panel
                    v-for="(item, index) in itemPaymentCondition"
                    :key="index"
                  >
                    <v-expansion-panel-header>{{ item.header }}</v-expansion-panel-header>
                    <v-expansion-panel-content>
                      <ul v-for="(items, indexs) in item.content" :key="indexs">
                        <li v-html="items.text"></li>
                      </ul>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;" class="justify-center">
          <v-btn color="#27AB9C" rounded width="125" height="40" @click="dialogPaymentCondition = !dialogPaymentCondition" class="white--text">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Start Page -->
    <v-row dense class="mt-3">
      <v-col cols="12" md="12" :class="IpadSize || MobileSize ? 'mt-6' : 'mt-0 pt-0'">
        <v-img src="@/assets/Create_Store/Banner-3.png" class="rounded-xl"></v-img>
      </v-col>
    </v-row>
    <v-row justify="center" dense no-gutters v-if="!MobileSize && !IpadSize">
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step1.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-if="stepper === 1"></v-img>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step2.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step3.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-else-if="stepper === 3"></v-img>
    </v-row>
    <v-row justify="center" dense no-gutters v-else-if="!MobileSize && IpadSize">
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step1.png" contain max-height="120" max-width="100%" class="mt-12 mb-10" v-if="stepper === 1"></v-img>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step2.png" contain max-height="120" max-width="100%" class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step3.png" contain max-height="120" max-width="100%" class="mt-12 mb-10" v-else-if="stepper === 3"></v-img>
    </v-row>
    <v-row justify="center" dense no-gutters v-else>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step1.png" contain max-height="101" max-width="100%" class="mt-12 mb-10" v-if="stepper === 1"></v-img>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step2.png" contain max-height="101" max-width="100%" class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step3.png" contain max-height="101" max-width="100%" class="mt-12 mb-10" v-else-if="stepper === 3"></v-img>
    </v-row>
    <v-row class="mb-4">
      <span class="HeadStepCreate" v-if="stepper === 1">ข้อมูลทั่วไป</span>
      <span class="HeadStepCreate" v-if="stepper === 2">ที่อยู่ร้านค้า</span>
      <span class="HeadStepCreate" v-if="stepper === 3">รูปโฆษณา</span><span v-if="stepper === 3" class="pt-4 pl-4 textStepThree">หมายเหตุ : สามารถอัปโหลดรูปโฆษณาภายหลังได้</span>
    </v-row>
    <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
      <!--step 1  -->
      <v-form ref="formOne" :lazy-validation="lazyOne" v-if="stepper === 1" class="pt-2">
        <v-card-text>
          <span class="textStepOne pr-4">รูปภาพร้านค้า</span><span class="subtextStepOne">ขนาดไฟล์: สูงสุด 1 MB</span>
          <!-- เพิ่มรูปภาพ -->
          <v-row dense class="my-2">
            <v-col cols="12" md="3" sm="12">
              <v-card elevation="0" outlined width="100%" :height="IpadSize || MobileSize ? '100%' : '321'" class="cardImageStyle">
                <v-card-text style="text-align: center;" class="px-2">
                  <v-row justify="center">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NoImageShop.png" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-if="Detail.shop_logo_image.length === 0 || showError === true"></v-img>
                    <v-img :src="Detail.shop_logo_image[0].path" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-else></v-img>
                  </v-row>
                  <span class="textStepOne">โปรไฟล์ร้านค้า</span><br/>
                  <span class="subtextStepOne" v-if="Detail.shop_logo_image.length === 0">( ขนาดรูปภาพ 244 x 244 pxและไฟล์นามสกุล .JPEG, .PNG, .JPG )</span><br/>
                  <v-btn width="125" height="40" text rounded color="#1B5DD6" class="mt-4" @click="uploadImageShop()" v-if="Detail.shop_logo_image.length === 0"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                  <v-file-input
                    v-model="DataImageShop"
                    :items="DataImageShop"
                    accept="image/*"
                    @change="UploadImageShop()"
                    @click="event => event.target.value = null"
                    id="imageShop"
                    :clearable="false"
                    style="display:none"
                  />
                  <!-- wait upload -->
                  <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-4">
                    <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span>
                    <v-spacer></v-spacer>
                    <span class="textUploadsizeImage pt-1">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span>
                  </v-row>
                  <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                    <v-progress-linear :value="percentUpload" height="7" rounded :active="show"></v-progress-linear>
                  </v-row>
                  <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-1">
                    <span class="textUploadsizeImage">กำลังอัปโหลด...</span>
                    <v-spacer></v-spacer>
                    <span class="textUploadpercentImage">{{ percentUpload }} %</span>
                  </v-row>
                  <v-row justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                    <v-btn text color="#27AB9C" style="text-decoration-line: underline; font-size: 14px;" @click="cancelImageShop()">ยกเลิก</v-btn>
                  </v-row>
                  <!-- upload success -->
                  <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === false" class="pt-0">
                    <v-col cols="12" class="pt-0">
                      <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 20) }}<span v-if="Detail.shop_logo_image[0].name.length > 20">...</span></span><br/>
                      <span class="textUploadsizeImage">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span>
                    </v-col>
                    <v-col cols="12" class="pt-0">
                      <v-btn width="50" height="40" text color="#27AB9C" @click="uploadImageShop()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                    </v-col>
                  </v-row>
                  <!-- upload fail -->
                  <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === true" class="pt-0">
                    <v-col cols="12" class="pt-0">
                      <span class="textUploadnameImageFail" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span><br/>
                      <span class="textFailUpload">{{ showErrorText }}</span>
                    </v-col>
                    <v-col cols="12" class="pt-0">
                      <v-btn width="50" height="40" text color="#636363" @click="cancelImageShop()"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" md="9" sm="12">
              <v-card elevation="0" outlined width="100%" :height="IpadSize || MobileSize ? '100%' : '321'" class="cardImageStyle">
                <v-card-text style="text-align: center;" class="pt-0" v-if="DataToShowImageBanner.length === 0">
                  <span class="textStepOne">หน้าปกร้านค้า</span><br/>
                  <v-row justify="center" class="py-12">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="167" height="100" max-width="167" max-height="100" contain></v-img>
                  </v-row>
                  <span class="subtextStepOne">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, PNG, .JPG เพิ่มได้สูงสุด 6 รูปภาพ)</span><br/>
                  <v-btn width="125" height="40" text rounded color="#1B5DD6" class="mt-4" @click="openModalUploadBanner()"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                </v-card-text>
                <v-card-text style="text-align: center;" class="pt-0" v-else>
                  <v-row dense>
                    <span class="textStepOne">หน้าปกร้านค้า</span>
                    <v-spacer></v-spacer>
                    <v-btn width="50" height="20" text color="#27AB9C" @click="openModalUploadBanner()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                  </v-row>
                  <v-row justify="start">
                    <v-col cols="12" md="6" sm="12" v-for="(item, index) in DataToShowImageBanner" :key="index">
                      <v-card elevation="0" outlined width="100%" :height="MobileSize ? '100%' : '50'" style="border-radius: 8px;">
                        <v-card-text class="py-1">
                          <v-row dense>
                            <v-col cols="2" align="center" class="pt-2">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                            </v-col>
                            <v-col cols="8" align="start">
                              <span class="textUpload">{{ item.name.substring(0, 15)}}</span><span v-if="item.name.length > 15">...</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{ Math.round(item.size / 1000) }} KB</span>
                            </v-col>
                            <v-col cols="2" class="pt-3">
                              <v-btn width="20" height="20" icon color="#9A9A9A"  @click="RemoveImageBanner(index, item)"><v-icon size="20">mdi-delete-outline</v-icon></v-btn>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                    <v-col cols="12" md="6" sm="12" v-if="DataToShowImageBanner.length < 6">
                      <v-card @click="openModalUploadBanner()" elevation="0" outlined width="100%" height="50" style="border-radius: 8px; border-color: #27AB9C;">
                        <v-card-text class="py-1">
                          <v-row dense>
                            <v-col cols="2" align="center" class="pt-3">
                              <v-icon size="24" color="#27AB9C">mdi-plus</v-icon>
                            </v-col>
                            <v-col cols="10" align="start" class="pt-3">
                              <span class="textUploadImage">อัปโหลดรูปเพิ่ม</span>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                  <v-row justify="end" dense class="pt-4">
                    <span style="subtextbanner">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG,PNG เพิ่มได้สูงสุด 6 รูปภาพ)</span>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <!-- ประเภทธุรกิจ -->
            <v-col cols="12" md="12" class="pt-4 pb-2">
              <span class="f-left textStepOne">ประเภทธุรกิจ</span>
            </v-col>
            <!-- <v-col cols="12" md="12" class="px-0 pt-2 pb-3">
              <v-radio-group
                v-model="bussinessType"
                :rules="Rules.bussinessType"
                row
              >
                <v-radio
                  v-if="haveCitizen"
                  label="นิติบุคคลอื่นๆ"
                  style="color: #333333;"
                  value="citizen"
                ></v-radio>
                <v-radio
                  v-if="haveBusiness"
                  label="นิติบุคคล"
                  style="color: #333333;"
                  value="business"
                ></v-radio>
              </v-radio-group>
            </v-col> -->
            <v-col cols="12" md="12" sm="12" class="px-0 py-0">
              <v-row dense no-gutters>
                <!-- ชื่อร้านค้า -->
                <v-col cols="12" md="12" sm="12">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">ชื่อร้านค้า <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="shopName"
                        outlined
                        dense
                        style="border-radius: 8px;"
                        :rules="Rules.shop_name"
                        :maxLength="255"
                        placeholder="ระบุชื่อร้านค้า"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- เลขประจำตัวผู้เสียภาษีอากร -->
                <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">เลขประจำตัวผู้เสียภาษีอากร <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                      <v-text-field
                        v-model="taxNumber"
                        outlined
                        dense
                        disabled
                        :maxLength="13"
                        style="border-radius: 8px;"
                        :rules="Rules.tax_id"
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        @keypress="CheckSpacebar($event)"
                        placeholder="ระบุเลขประจำตัวผู้เสียภาษี 13 หลัก"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- URL ร้านค้า -->
                <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">URL ร้านค้า</span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="shopURL"
                        outlined
                        dense
                        style="border-radius: 8px;"
                        readonly
                        placeholder="ระบุ url ร้านค้า"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <!-- เกี่ยวกับร้านค้า -->
            <v-col cols="12" md="12" sm="12" class="pa-0">
              <span class="textFieldStepOne">เกี่ยวกับร้านค้า</span>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pt-2 pb-2 mb-2">
              <ckeditor placeholder="อธิบายเกี่ยวกับร้านค้าของคุณ" style="border: 1px #A0A0A0 solid;" :rules="Rules.spaceRule" :editor="editor" :config="editorConfigDescription" v-model="descriptionShop"></ckeditor>
              <!-- <v-textarea
                v-model="descriptionShop"
                placeholder="อธิบายเกี่ยวกับร้านค้าของคุณ"
                background-color="#FFFFFF"
                outlined
                style="border-radius: 8px;"
                no-resize
                height="136"
                :rules="Rules.spaceRule"
              ></v-textarea> -->
            </v-col>
            <!-- เบอร์โทรศัพท์, ข้อมูลฝ่ายขาย, ข้อมูลการ เปิด - ปิด ร้านค้า -->
            <v-col cols="12" md="12" sm="12" class="px-0 py-0">
              <v-row dense no-gutters>
                <!-- เบอร์โทรศัพท์ -->
                <v-col cols="12" md="12" sm="12">
                  <v-row dense>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span class="textFieldStepOne">ระบุชื่อย่อร้านค้า <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'" v-if="isJVShop === true">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-text-field v-model="shortName" v-bind="attrs" v-on="on" outlined dense :rules="Rules.short_name" :maxLength="5" placeholder="ระบุชื่อย่อร้านค้า ระบุได้ 1-5 ตัวอักษร" style="border-radius: 8px;"></v-text-field>
                            </template>
                            <span>ตัวย่อที่จะเอาไปใส่ run number ของ Cost Sheet และ PO ของแต่ละ JV</span>
                          </v-tooltip>
                        </v-col>
                        <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'" v-else>
                          <v-text-field v-model="shortName" outlined dense :rules="Rules.short_name" :maxLength="5" placeholder="ระบุชื่อย่อร้านค้า ระบุได้ 1-5 ตัวอักษร" style="border-radius: 8px;"></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <v-row dense>
                        <v-col cols="12">
                          <span class="textFieldStepOne">เบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ <span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field
                            v-model="mobile"
                            outlined
                            dense
                            style="border-radius: 8px;"
                            :maxLength="20"
                            :rules="Rules.telShop"
                            @keypress="CheckSpacebarMobile($event)"
                            placeholder="ระบุเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- Facebook -->
                <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">Facebook</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                      <v-text-field
                        v-model="facebook"
                        outlined
                        dense
                        style="border-radius: 8px;"
                        @blur="checkFackbook"
                        placeholder="ระบุ Facebook"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- Line -->
                <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">Line</span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="line"
                        outlined
                        dense
                        style="border-radius: 8px;"
                        placeholder="ระบุ ID LINE"
                        oninput="this.value = this.value.replace(/[^@a-zA-Z0-9_.-\s]/g, '')"
                        @blur="checkLine"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="mt-2">
              <v-divider></v-divider>
            </v-col>
            <v-row dense>
              <!-- ข้อมูลการ เปิด - ปิด ร้านค้า -->
              <v-col cols="12" md="6">
                <v-row dense class="mt-6 mb-4">
                  <v-col cols="12" md="6" class="pb-2 pr-4">
                    <span class="textStepOne">ข้อมูลการ เปิด - ปิด ร้านค้า</span>
                  </v-col>
                  <v-col cols="12" md="6" class="pb-2 pl-0">
                    <v-row dense>
                      <v-col cols="2" md="3" sm="1">
                        <v-switch v-model="openshop" inset color="#52C41A" hide-details></v-switch>
                      </v-col>
                      <v-col cols="8" md="7" sm="8" :class="[IpadSize ? 'pl-5' : MobileSize ? '' : 'pl-2']">
                        <span class="textFieldStepOne">สถานะ : {{ openshop === true ? 'เปิด' : 'ปิด' }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <!-- ข้อมูลการขอเป็นคู่ค้า -->
              <v-col cols="12" md="6">
                <v-row dense class="mt-6 mb-4">
                  <v-col cols="12" md="4" class="pb-2 pl-0">
                    <span class="textStepOne">ข้อมูลการขอเป็นคู่ค้า</span>
                  </v-col>
                  <v-col cols="12" md="8" class="pb-2 pl-0">
                    <v-row dense>
                      <v-col cols="2" md="2" sm="1">
                        <v-switch v-model="partner" inset color="#52C41A" hide-details></v-switch>
                      </v-col>
                      <v-col cols="8" md="8" sm="8"  xs="8" :class="[IpadSize ? 'pl-5' : MobileSize ? '' : 'pl-2']">
                        <span class="textFieldStepOne">สถานะ : {{ partner === true ? 'เปิดการขอเป็นคู่ค้า' : 'ปิดการขอเป็นคู่ค้า' }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-col cols="12" class="mb-4">
              <v-divider></v-divider>
            </v-col>
            <!-- ข้อมูลการจัดส่ง -->
            <v-col cols="12" md="12" class="pa-0 pb-4">
              <v-row dense class="pl-1 pt-2">
                <span class="textStepOne pr-4">ข้อมูลการจัดส่ง</span>
                <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showText}`" disabled class="pr-4"></v-switch>
                <!-- <v-switch v-model="switchEstimate" inset color="#52C41A" hide-details :label="`${showTextEstimate}`" :disabled="switchShipping" :class="MobileSize || IpadSize ? 'pt-4' : 'pr-4'"></v-switch> -->
                <v-switch v-model="switchStoreFront" inset color="#52C41A" hide-details :label="`${showTextStoreFront}`" :class="MobileSize || IpadSize ? 'pt-4' : ''"></v-switch>
              </v-row>
            </v-col>
            <!-- เลือกรายการขนส่ง -->
            <v-col cols="12" md="12" sm="12" v-if="switchShipping">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">เลือกรายการขนส่ง <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-select v-model="SelectShipping" :menu-props="{ offsetY: true }" :items="itemShipping" item-text="name" item-value="code" :rules="Rules.ItemShipping" multiple chips outlined dense placeholder="เลือกขนส่ง" style="border-radius: 8px;">
                    <template v-slot:selection="{ item }">
                      <v-chip
                       close
                       outlined
                       @click:close="removeShipping(item)" color="primary"
                      >
                        {{ item.name }}
                      </v-chip>
                    </template>
                    <template v-slot:item="{ item }">
                      <v-row dense class="py-2">
                        <v-img :src="item.media_path" max-height="50" max-width="50" style="border-radius: 999px;" contain></v-img><span class="pl-4" style="display: grid; align-items: center;">{{ item.name }}</span>
                      </v-row>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
            </v-col>
            <!-- จัดส่งแบบเก็บอุณหภูมิ -->
            <!-- <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">จัดส่งแบบเก็บอุณหภูมิ</span>
                </v-col>
                <v-col cols="12">
                  <v-select outlined dense placeholder="เลือกขนส่ง" :disabled="switchShipping === false ? true : false"></v-select>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- <v-col cols="12" class="mt-2 mb-6">
              <v-divider></v-divider>
            </v-col> -->
            <!-- ข้อมูลการเงิน -->
            <!-- <v-col cols="12" md="12" class="pa-0 pb-4">
              <span class="textStepOne">ข้อมูลการเงิน</span>
            </v-col> -->
            <!-- รหัสการจ่ายเงิน -->
            <!-- <v-col cols="12" md="12" class="pt-2">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รหัสการจ่ายเงิน <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="MerchantKey"
                    outlined
                    dense
                    style="border-radius: 8px;"
                    :rules="Rules.Merchant"
                    placeholder="ระบุรหัสการจ่ายเงิน (Merchant Key)"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- ช่องทางการชำระเงิน -->
            <!-- <v-col cols="12" md="12" class="pt-2">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">ช่องทางการชำระเงิน <span style="color: red;">*</span></span>
                  <v-chip class="ml-1" small outlined color="red" @click="dialogPaymentCondition = !dialogPaymentCondition" style="font-weight: 700;">อัตราค่าบริการระบบชำระเงิน</v-chip>
                </v-col>
                <v-col cols="12">
                  <v-select v-model="SelectPaymentType" :menu-props="{ offsetY: true }" :items="itemPayment" item-text="text" item-value="value" :rules="Rules.ItemPayment" multiple chips outlined dense placeholder="เลือกช่องทางการชำระเงิน" style="border-radius: 8px;">
                    <template #selection="{ item }">
                      <v-chip
                      close
                      @click:close="removePayment(item)" :color="item.value === 'qrcode' ? 'rgba(27, 93, 214, 0.10)' : item.value === 'installment' ? '#F8FFF5' : 'rgba(255, 113, 11, 0.10)'" :style="item.value === 'qrcode' ? 'color: #1B5DD6;' : item.value === 'installment' ? 'color: #8BC34A;' : 'color: #FF710B;'"
                      >
                        {{ item.text }}
                      </v-chip>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- เลือกรูปแบบการผ่อนชำระ -->
            <!-- <v-col cols="12" md="12" class="pt-2" v-if="SelectPaymentType.indexOf('installment') !== -1">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รูปแบบการผ่อนชำระ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-select v-model="selectInstallmentType" :menu-props="{ offsetY: true }" :items="itemInstallment" item-text="text" item-value="value" :rules="Rules.installment" multiple chips outlined dense placeholder="เลือกรูปแบบการผ่อนชำระ" style="border-radius: 8px;">
                    <template #selection="{ item }">
                      <v-chip
                      close
                      outlined
                      @click:close="removeInstallment(item)" color="primary"
                      >
                        {{ item.text }}
                      </v-chip>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- รูปแบบชำระเงิน -->
            <!-- <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รูปแบบชำระเงิน <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" md="3" sm="4" class="pt-3">
                  <v-radio-group
                    v-model="SelectType"
                    row
                    :rules="Rules.ItemContact"
                  >
                    <v-radio
                      label="ไม่มีสัญญา"
                      value="no_contact"
                    ></v-radio>
                    <v-radio
                      label="มีสัญญา"
                      value="contact"
                    ></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12" md="5" sm="8" :class="IpadSize ? 'pt-6' : ''" v-if="SelectType === 'contact'">
                  <v-select v-model="SelectTypePay" :menu-props="{ offsetY: true }" :items="itemPayType" item-text="text" item-value="value" :rules="Rules.ItempayType" multiple chips outlined dense placeholder="เลือกรูปแบบชำระเงิน" style="border-radius: 8px;">
                    <template #selection="{ item }">
                      <v-chip
                       close
                       @click:close="remove(item)" :color="item.value === 'onetime' ? 'rgba(27, 93, 214, 0.10)' : 'rgba(255, 113, 11, 0.10)'" :style="item.value === 'onetime' ? 'color: #1B5DD6;' : 'color: #FF710B;'"
                      >
                        {{ item.text }}
                      </v-chip>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- ปุ่ม เปิด - ปิด e-WHT -->
            <!-- <v-col cols="12" md="12" class="pa-0 pb-4">
              <v-row dense class="pl-1 pt-2">
                <span class="textFieldStepOne">e-Withholding Tax</span>
                <v-switch v-model="switchEWHT" inset color="#52C41A" hide-details :label="`${showTextEWHT}`"  class="pl-4"></v-switch>
              </v-row>
            </v-col> -->
            <!-- บัญชีธนาคาร สำหรับ e-WHT -->
            <!-- จัดส่งธรรมดา -->
            <!-- <v-col cols="12" md="12" sm="12"> -->
              <!-- ธนาคาร -->
              <!-- <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">ธนาคาร <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : ''">
                  <v-select v-model="bankCode" :menu-props="{ offsetY: true }" :items="itemsBank" item-text="name" item-value="code" :rules="Rules.ItemBank" outlined dense placeholder="เลือกธนาคาร" style="border-radius: 8px;" class="setCustomSelect"></v-select>
                </v-col>
              </v-row> -->
              <!-- เลขบัญชีและชื่อบัญชี -->
              <!-- <v-row dense> -->
                <!-- เลขบัญชีธนาคาร -->
                <!-- <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">เลขบัญชีธนาคาร <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                      <v-text-field
                        v-model="accountNo"
                        outlined
                        dense
                        :rules="Rules.emptyText"
                        @keypress="CheckSpacebar($event)"
                        :disabled="bankCode === '' ? true : false"
                        style="border-radius: 8px;"
                        oninput="this.value = this.value.replace(/[^0-9/\s]/g, '').replace(/(\..*)\./g, '$1')"
                        placeholder="ระบุเลขบัญชีธนาคาร"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col> -->
                <!-- ชื่อบัญชีธนาคาร -->
                <!-- <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">ชื่อบัญชีธนาคาร <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="accountName"
                        outlined
                        dense
                        :rules="Rules.emptyText"
                        :disabled="bankCode === '' ? true : false"
                        style="border-radius: 8px;"
                        @keypress="CheckSpacebarOne($event)"
                        oninput="this.value = this.value.replace(/[^a-zA-Zก-๏0-9.()\s]/g, '')"
                        placeholder="ระบุชื่อบัญชีธนาคาร"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col> -->
          </v-row>
          <v-row dense class="mt-4">
            <v-btn rounded color="#27AB9C" width="125" height="40" outlined @click="backToDesignShop()">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn text color="#27AB9C" width="81" height="40" @click="clearStepOne()">ล้างค่า</v-btn>
            <v-btn rounded color="#27AB9C" width="125" height="40" class="white--text" :class="MobileSize ? '' : ''" @click="nextStep(2)">ถัดไป</v-btn>
          </v-row>
        </v-card-text>
      </v-form>
      <!-- step 2 -->
      <v-form ref="formTwo" :lazy-validation="lazyTwo" v-else-if="stepper === 2">
        <v-card-text>
          <span class="textStepOne pr-4">เพิ่มที่อยู่</span>
          <!-- แถวที่ 1 -->
          <v-row dense no-gutters class="mt-4">
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">ชื่อ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <v-text-field
                    v-model="name"
                    outlined
                    dense
                    style="border-radius: 8px;"
                    :rules="Rules.firstname"
                    oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"
                    placeholder="ระบุชื่อ"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">นามสกุล <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    v-model="surname"
                    outlined
                    dense
                    style="border-radius: 8px;"
                    :rules="Rules.lastname"
                    oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"
                    placeholder="ระบุนามสกุล"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- แถวที่ 2 -->
          <v-row dense no-gutters class="mt-0">
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne" v-if="isJVShop === false">อีเมล <span style="color: red;">*</span></span>
                  <span class="textFieldStepOne" v-else>อีเมลติดต่อภายนอก <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <v-text-field
                    v-model="email"
                    outlined
                    dense
                    style="border-radius: 8px;"
                    :rules="Rules.email"
                    placeholder="ระบุอีเมล"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">หมายเลขโทรศัพท์ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    v-model="mobileNumber"
                    outlined
                    dense
                    oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                    :maxLength="10"
                    style="border-radius: 8px;"
                    :rules="Rules.tel"
                    @keypress="CheckSpacebar($event)"
                    placeholder="ระบุหมายเลขโทรศัพท์ 9 หรือ 10 หลัก"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6" v-if="isJVShop">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">อีเมลทีม (ติดต่อภายใน) <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <v-text-field
                    v-model="teamEmail"
                    outlined
                    dense
                    style="border-radius: 8px;"
                    :rules="Rules.email"
                    placeholder="ระบุอีเมล"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รายละเอียดที่อยู่</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    v-model="addressDetail"
                    outlined
                    style="border-radius: 8px;"
                    dense
                    placeholder="ระบุรายละเอียดที่อยู่ (***ยกเว้น บ้านเลขที่)"
                    :rules="Rules.spaceRule"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- แถวที่ 3 -->
          <v-row dense no-gutters class="mt-0">
            <v-col cols="12" md="4" sm="12">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">เลขที่ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <v-text-field
                    v-model="houseNo"
                    outlined
                    style="border-radius: 8px;"
                    dense
                    oninput="this.value = this.value.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1')"
                    placeholder="ระบุบ้านเลขที่"
                    :rules="Rules.house_Num"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">แขวง/ตำบล <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <addressinput-subdistrict :rules="Rules.empty" style="border-radius: 8px;" label="" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล" />
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">เขต/อำเภอ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                  <addressinput-district label="" style="border-radius: 8px;" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- แถว 4 -->
          <v-row dense no-gutters class="mt-0">
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">จังหวัด <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <addressinput-province label="" style="border-radius: 8px;" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="province" placeholder="ระบุจังหวัด" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">รหัสไปรษณีย์ <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <addressinput-zipcode numbered label="" style="border-radius: 8px;" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- <v-row dense no-gutters class="mt-0 pt-0"> -->
          <v-col cols="12" class="pt-0 pl-0 mt-3">
            <v-row dense>
              <v-checkbox v-model="setDefault" color="#27AB9C" :success="true"></v-checkbox><span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ตั้งค่าเป็นที่อยู่เริ่มต้น</span>
            </v-row>
          </v-col>
          <!-- </v-row> -->
          <v-row dense class="mt-4">
            <v-btn rounded color="#27AB9C" width="125" height="40" outlined @click="backStep(1)">ย้อนกลับ</v-btn>
            <v-spacer></v-spacer>
            <v-btn text color="#27AB9C" width="81" height="40" @click="clearStepTwo()">ล้างค่า</v-btn>
            <v-btn color="#27AB9C" width="125" height="40" rounded class="white--text" :class="MobileSize ? '' : ''" @click="nextStepThree(3)">ถัดไป</v-btn>
          </v-row>
        </v-card-text>
      </v-form>
      <!-- step 3 -->
      <v-form ref="formThree" :lazy-validation="lazyThree" v-else-if="stepper === 3">
        <v-card-text>
          <!-- ส่วนเพิ่มรูปภาพ -->
          <v-row dense no-gutters class="mt-0 mb-2" v-if="DataToShowImageAdvert.length === 0">
            <v-col cols="12">
              <v-card elevation="0" outlined width="100%" height="321" class="cardImageStyle">
                <v-card-text style="text-align: center;" class="pt-0">
                  <span class="textStepOne">เพิ่มรูปโฆษณา</span><br/>
                  <v-row justify="center" class="py-12">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="167" height="100" max-width="167" max-height="100" contain></v-img>
                  </v-row>
                  <span class="subtextStepOne">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, PNG, .JPG เพิ่มได้สูงสุด 5 รูปภาพ)</span><br/>
                  <v-btn width="125" height="40" text rounded color="#1B5DD6" class="mt-4" @click="openModalUploadAdvert()"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row dense no-gutters class="mt-0 mb-2" v-if="DataToShowImageAdvert.length !== 0">
            <v-col cols="6" align="start">
              <span class="textStepOne">รายการอัปโหลด</span><br/>
              <span class="subtextbanner">(ลำดับการจัดเรียงจะเป็นลำดับในการแสดงรูปบนหน้าร้าน)</span>
            </v-col>
            <v-col cols="6" align="end">
              <v-btn class="pt-1" width="50" height="40" text color="#27AB9C" @click="openModalUploadAdvert()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
            </v-col>
            <v-row dense class="px-0 pt-4">
              <v-col cols="1" md="1" sm="2">
                <v-card width="100%" outlined elevation="0" class="cardImageStyle py-0">
                  <v-card-text style="height: 89px; padding-top: 30px" v-for="(item, index) in DataToShowImageAdvert" :key="index">
                    <span style="text-align: center;" class="textNumberImage">{{ index + 1}}</span>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="11" md="11" sm="10">
                <draggable v-model="DataToShowImageAdvert" :move="onMove" @start="drag=true" @end="drag=false">
                  <v-card v-for="(item, index) in DataToShowImageAdvert" :key="index" width="100%" height="87" outlined elevation="0" class="cardImageStyle mb-1 pt-2">
                    <v-card-text class="pt-0" :class="IpadSize || MobileSize ? 'px-0' : ''">
                      <v-row dense class="pt-0">
                        <v-col cols="1" md="1" sm="1" style="text-align: center;" class="pt-6">
                          <v-icon size="24" color="#27AB9C">mdi-arrow-all</v-icon>
                        </v-col>
                        <v-col cols="3" md="3" sm="4" align="start" :class="IpadSize || MobileSize ? 'pt-2' : 'pt-6'">
                          <span class="textUploadImageSuccess pr-4">{{ item.name.substring(0, 15) }}<span v-if="item.name.length > 15">...</span></span><br v-if="IpadSize || MobileSize"/>
                          <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{ Math.round(item.size / 1000) }} KB</span>
                        </v-col>
                        <v-col cols="5" md="5" sm="5" align="center" class="py-0">
                          <v-img :src="item.path" max-width="314" max-height="68" contain></v-img>
                        </v-col>
                        <v-col cols="3" md="3" sm="2" align="end" class="pt-5">
                          <v-btn icon color="#636363" @click="RemoveImageAdvertOuter(index, item)"><v-icon size="20">mdi-delete-outline</v-icon></v-btn>
                          <!-- <v-img :src="item.path" max-width="314" max-height="68" contain></v-img> -->
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </draggable>
              </v-col>
            </v-row>
          </v-row>
          <v-row dense class="mt-4">
            <v-btn rounded color="#27AB9C" width="125" height="40" outlined @click="backStep(2)">ย้อนกลับ</v-btn>
            <v-spacer></v-spacer>
            <v-btn text color="#27AB9C" width="81" height="40" @click="clearStepThree()">ล้างค่า</v-btn>
            <v-btn color="#27AB9C" width="125" height="40" :class="MobileSize ? '' : ''" rounded class="white--text" @click="checkEditShop()">บันทึก</v-btn>
          </v-row>
        </v-card-text>
      </v-form>
    </v-card>
  </v-container>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import draggable from 'vuedraggable'
import { Decode } from '@/services'
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
Vue.use(VueThailandAddress)
export default {
  components: { draggable },
  data () {
    return {
      dragover: false,
      dragoverAdvert: false,
      setWidth: 1247,
      setHeight: '70vh',
      lazyOne: false,
      lazyTwo: false,
      lazyThree: false,
      isJVShop: false,
      dialogSendCreateShop: false,
      dialogAwaitCreateShop: false,
      dialogSuccessCreateShop: false,
      dialogFailCreateShop: false,
      dialogOpenDialogUploadBanner: false,
      dialogOpenDialogUploadAdvert: false,
      dialogPaymentCondition: false,
      dialogShowImage: false,
      stepper: 1,
      DataImage: [],
      DataImageShop: [],
      DataImageAdvert: [],
      DataToShowImageBanner: [],
      DataToShowImageAdvert: [],
      teamEmail: '',
      Detail: {
        seller_shop_id: '',
        shop_logo_image: [],
        image_banner: [],
        image_news: [],
        shop_image: [],
        shop_image_banner: [],
        shop_advert: [],
        shop_name: '',
        shop_url: '',
        tax_id: '',
        line_id: '',
        shop_description: '',
        payment_method: [],
        shipping_method: [],
        installment_method: [],
        use_estimate: '',
        store_front: '',
        shop_status: '',
        public_show: '',
        partner_show: '',
        have_partner: '',
        facebook_url: '',
        receiver_account_name: '',
        receiver_account_no: '',
        receiver_bank_code: '',
        shop_type: '',
        merchant_key: '',
        first_name: '',
        last_name: '',
        team_email: '',
        shop_address: [
          {
            id: '',
            default_address: '',
            house_no: '',
            detail: '',
            province: '',
            district: '',
            sub_district: '',
            zipcode: ''
          }
        ],
        shop_email: [],
        payment_costs: [],
        shop_phone: [
          { phone: '' },
          { phone: '' }
        ],
        use_attorney: ''
        // shop_shipping_type: []
      },
      accountName: '',
      accountNo: '',
      bankCode: '',
      bussinessType: '',
      shopURL: '',
      taxNumber: '',
      // MerchantKey: '',
      shopName: '',
      SelectTypePay: '',
      SelectPaymentType: '',
      panel: [0, 1, 2],
      itemPaymentCondition: [
        {
          header: '1. ชำระแบบ QR Code',
          content: [
            { text: 'กรณี <span style="color: #27AB9C; font-weight: 700; font-size: 16px; text-decoration: underline;"> Transaction ยอดชำระน้อยกว่า 500 บาท</span> : 2.5 % (ไม่รวม Vat)' },
            { text: 'กรณี <span style="color: #27AB9C; font-weight: 700; font-size: 16px; text-decoration: underline;"> Transaction ยอดชำระมากกว่าหรือเท่ากับ 500 บาท</span> : 8.03 บาท (รวม Vat)' }
          ]
        },
        {
          header: '2. ชำระแบบ Credit Card/Debit Card',
          content: [
            { text: 'ระบบรองรับ บัตรเครดิต/เดบิต ทุกธนาคาร โดยมี <span style="color: #27AB9C; font-weight: 700; font-size: 16px; text-decoration: underline;">ค่าธรรมเนียม 2.5% / 1 Transaction</span> (ไม่รวม Vat)' }
          ]
        }
        // { header: 'การผ่อนชำระผ่านบัตรเครดิต', content: 'ระบบรองรับเฉพาะบัตรเครดิตกรุงไทย (KTC) เท่านั้น โดยมี <span style="color: #27AB9C; font-weight: 700; font-size: 16px;">" ค่าธรรมเนียม 3%, ดอกเบี้ย 0.89% และ VAT 7% ของค่าธรรมเนียมและดอกเบี้ย / 1 Transaction "</span>' }
      ],
      itemPayment: [
        { text: 'QR Code', value: 'qrcode' },
        { text: 'Credit Card/Debit Card', value: 'creditcard' }
        // { text: 'การผ่อนชำระผ่านบัตรเครดิต', value: 'installment' }
      ],
      selectInstallmentType: [],
      itemInstallment: [
        { text: '3 เดือน', value: '3' },
        { text: '6 เดือน', value: '6' },
        { text: '10 เดือน', value: '10' }
      ],
      SelectShipping: '',
      itemShipping: [],
      show: false,
      showError: false,
      showErrorText: '',
      SelectType: 'no_contact',
      disableUploadButton: true,
      switchShipping: true,
      switchEstimate: false,
      switchStoreFront: false,
      switchEWHT: false,
      imagetoBig: '',
      fromClick: '',
      disableUploadButtonAdvert: true,
      items: ['Foo', 'Bar', 'Fizz', 'Buzz'],
      descriptionShop: '',
      mobile: '',
      facebook: '',
      line: '',
      SCG: '',
      flash: '',
      partner: false,
      publicshop: false,
      openshop: false,
      shippingPriceShop: false,
      allDay: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
      time: null,
      MondaytimeStart: null,
      MondaytimeEnd: null,
      TuesdaytimeStart: null,
      TuesdaytimeEnd: null,
      WednesdaytimeStart: null,
      WednesdaytimeEnd: null,
      ThursdaytimeStart: null,
      ThursdaytimeEnd: null,
      FirdaytimeStart: null,
      FirdaytimeEnd: null,
      SaturdaytimeStart: null,
      SaturdaytimeEnd: null,
      SundaytimeStart: null,
      SundaytimeEnd: null,
      menuMondayStart: false,
      menuMondayEnd: false,
      menuTuesdayStart: false,
      menuTuesdayEnd: false,
      menuWednesdayStart: false,
      menuWednesdayEnd: false,
      menuThursdayStart: false,
      menuThursdayEnd: false,
      menuFirdayStart: false,
      menuFirdayEnd: false,
      menuSaturdayStart: false,
      menuSaturdayEnd: false,
      menuSundayStart: false,
      menuSundayEnd: false,
      haveCitizen: false,
      haveBusiness: false,
      name: '',
      surname: '',
      companyName: '',
      email: '',
      mobileNumber: '',
      addressDetail: '',
      houseNo: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      setDefault: true,
      shortName: '',
      saleName: '',
      salePhone: '',
      saleMobilePhone: '',
      shopID: '',
      saleEmail: '',
      saleTeamName: '',
      itemsBank: [],
      percentUpload: 0,
      messageError: '',
      useAttorney: '',
      itemPayType: [
        { text: 'One Time', value: 'onetime' },
        { text: 'Recurring', value: 'recurring' }
      ],
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        seller_name: [
          v => !!v || 'กรุณากรอกชื่อฝ่ายขาย',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        shop_name: [
          v => !!v || 'กรุณากรอกชื่อร้านค้า',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        short_name: [
          v => !!v || 'กรุณากรอกชื่อย่อร้านค้า',
          v => v.length <= 5 || 'กรุณากรอกชื่อย่อร้านค้าไม่เกิน 5 ตัวอักษร',
          v => /^[A-Za-z0-9\s]+$/.test(v) || 'กรุณากรอกแค่ตัวอักษรภาษาอังกฤษและตัวเลข'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 ตัว',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        firstname: [
          v => !!v || 'กรุณากรอกชื่อ',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        lastname: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ',
          v => (v.length > 8 && v.length <= 20) || v === '' || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล'
          // v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          // v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        Merchant: [
          v => !!v || 'กรุณาระบุรหัสการจ่ายเงิน',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        bussinessType: [
          v => !!v || 'กรุณาเลือกประเภทธุรกิจ'
        ],
        ItemContact: [
          v => !!v || 'กรุณาเลือกประเภทสัญญา'
        ],
        ItempayType: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemPayment: [
          v => v.length !== 0 || 'กรุณาเลือกช่องทางการชำระเงิน'
        ],
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ],
        ItemBank: [
          v => v.length !== 0 || 'กรุณาเลือกธนาคาร'
        ],
        installment: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการผ่อนชำระ'
        ]
      },
      editor: ClassicEditor,
      editorConfigDescription: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          // 'imageUpload',
          '|',
          'blockquote',
          // 'inserttable',
          'undo',
          'redo'
        ],
        item: {
          descriptionPackage: '',
          descriptionCondition: ''
        }
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('CheckShop')
    this.$EventBus.$emit('checkpath')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.stepper = parseInt(this.$route.query.step)
    this.shopID = parseInt(this.$route.query.shopID)
    // this.getListBank()
    this.getListShipping()
    // this.CheckBusiness()
  },
  mounted () {
    setTimeout(async () => {
      // check step 1
      const CheckStep1 = sessionStorage.getItem('CheckStep1') !== null ? 'yes' : 'no'
      if (CheckStep1 === 'yes') {
        await this.uploadBannerToS3(this.DataToShowImageBanner)
        this.Detail.shop_name = this.shopName
        this.Detail.shop_url = this.shopURL
        if (this.Detail.shop_logo_image.length !== 0) {
          this.Detail.shop_image.push({
            name: this.Detail.shop_logo_image[0].name,
            path: this.Detail.shop_logo_image[0].image_data,
            path_lazy: this.Detail.shop_logo_image[0].image_data_lazy,
            href: ''
          })
        } else {
          this.Detail.shop_image = []
        }
        this.Detail.tax_id = this.taxNumber
        this.Detail.payment_costs = this.SelectType === 'no_contact' ? [] : this.SelectTypePay
        this.Detail.shipping_method = [
          { service: '', courier: [] }
        ]
        if (this.switchShipping === true) {
          for (var i = 0; i < this.SelectShipping.length; i++) {
            this.Detail.shipping_method[0].service = 'ISHIP'
            this.Detail.shipping_method[0].courier.push(this.SelectShipping[i])
          }
          this.Detail.shipping_method = this.functionCheckDuplicateValue(this.Detail.shipping_method)
        } else {
          this.Detail.shipping_method = []
        }
        this.Detail.use_estimate = this.switchEstimate ? 'yes' : 'no'
        this.Detail.store_front = this.switchStoreFront ? 'yes' : 'no'
        this.Detail.shop_description = this.descriptionShop
        this.Detail.payment_method = this.SelectPaymentType
        this.Detail.installment_method = this.selectInstallmentType
        this.Detail.shop_status = this.openshop ? 'active' : 'inactive'
        this.Detail.public_show = this.openshop ? 'yes' : 'no'
        this.Detail.partner_show = this.partner ? 'yes' : 'no'
        this.Detail.have_partner = this.partner ? 'yes' : 'no'
        this.Detail.use_ewht = this.switchEWHT ? 'yes' : 'no'
        this.Detail.facebook_url = this.facebook
        this.Detail.line_id = this.line
        // this.Detail.merchant_key = this.MerchantKey
        this.Detail.shop_type = this.bussinessType
        this.Detail.shop_phone[0].phone = this.mobile
        this.Detail.receiver_account_name = this.accountName
        this.Detail.receiver_account_no = this.accountNo
        this.Detail.receiver_bank_code = this.bankCode
      }
      // check step 2
      const CheckStep2 = sessionStorage.getItem('CheckStep2') !== null ? 'yes' : 'no'
      if (CheckStep2 === 'yes') {
        this.Detail.first_name = this.name
        this.Detail.last_name = this.surname
        this.Detail.shop_email.push({
          seller_email: this.email
        })
        if (this.isJVShop) {
          this.Detail.team_email = this.teamEmail
        }
        this.Detail.shop_phone[1].phone = this.mobileNumber
        this.Detail.short_name = this.shortName
        this.Detail.sale_name = this.saleName
        this.Detail.sale_tel_no = this.salePhone
        this.Detail.sale_mobile = this.saleMobilePhone
        this.Detail.sale_email = this.saleEmail
        this.Detail.sale_team = this.saleTeamName
        this.Detail.shop_address[0].default_address = this.setDefault ? 'main' : ''
        this.Detail.shop_address[0].house_no = this.houseNo
        this.Detail.shop_address[0].detail = this.addressDetail
        this.Detail.shop_address[0].province = this.province
        this.Detail.shop_address[0].district = this.district
        this.Detail.shop_address[0].sub_district = this.subdistrict
        this.Detail.shop_address[0].zipcode = this.zipcode
      }
    }, 2000)
  },
  computed: {
    showTextEWHT () {
      if (this.switchEWHT === false) {
        return 'ไม่ใช้'
      } else {
        return 'ใช้'
      }
    },
    showText () {
      if (this.switchShipping === false) {
        return 'ไม่ใช้ขนส่งของระบบ'
      } else {
        return 'ใช้ขนส่งของระบบ'
      }
    },
    showTextEstimate () {
      if (this.switchEstimate === false) {
        return 'ไม่ใช้คำนวณค่าขนส่งของระบบ (กรณีไม่ใช้ขนส่งระบบ)'
      } else {
        return 'ใช้คำนวณค่าขนส่งของระบบ'
      }
    },
    showTextStoreFront () {
      if (this.switchStoreFront === false) {
        return 'ไม่มีรับหน้าร้าน'
      } else {
        return 'มีรับหน้าร้าน'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    SelectPaymentType (val) {
      if (val.includes('installment') === false) {
        this.selectInstallmentType = []
      }
    },
    // switchEWHT (val) {
    //   if (val === false) {
    //     this.bankCode = ''
    //     this.accountNo = ''
    //     this.accountName = ''
    //   }
    // },
    switchShipping (val) {
      if (val === true) {
        this.switchEstimate = false
      }
    },
    switchEstimate (val) {
      if (val === true) {
        this.switchShipping = false
      }
    },
    shopName (val) {
      if (val !== '') {
        const shopCleaned = val.replace(/\s/g, '-')
        this.shopURL = `${process.env.VUE_APP_DOMAIN}shoppage/${shopCleaned}-${this.shopID}`
      } else {
        this.shopURL = ''
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/EditShopMobile?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/EditShop?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
      }
    },
    subdistrict (val) {
      if (/\s/g.test(val)) {
        this.subdistrict = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcode = ''
            this.district = ''
            this.province = ''
          }
        } else {
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      }
    },
    district (val) {
      if (/\s/g.test(val)) {
        this.district = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcode = ''
            this.subdistrict = ''
            this.province = ''
          }
        } else {
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      }
    },
    province (val) {
      if (/\s/g.test(val)) {
        this.province = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcode = ''
            this.subdistrict = ''
            this.district = ''
          }
        } else {
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      }
    },
    zipcode (val) {
      if (/\s/g.test(val)) {
        this.zipcode = val.replace(/\s/g, '')
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            // this.checkAdressError('checkZipcodeError')
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistrict = ''
            this.district = ''
            this.province = ''
          }
        } else {
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      }
    }
  },
  methods: {
    async getListShipping () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const auth = {
        headers: { Authorization: `Bearer ${oneData.user.access_token}` }
      }
      const data = {
        seller_shop_id: this.shopID
      }
      const response = await this.axios.post(`${process.env.VUE_APP_BACK_END2}iship/iship_courier_list`, data, auth)
      if (response.data.ok === 'y') {
        this.itemShipping = [...response.data.query_result.filter(item => item.service_provider === 'ISHIP')]
      } else if (response.data.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      }
      await this.GetDetailShop()
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    async getListBank () {
      await this.$store.dispatch('actionsListBank')
      const response = await this.$store.state.ModuleShop.stateListBank
      if (response.result === 'SUCCESS') {
        this.itemsBank = await [...response.data]
      }
    },
    DropImage (e) {
      if (this.Detail.shop_image_banner.length < 6) {
        if (e.dataTransfer.files !== undefined || e.dataTransfer.files.length < 6) {
          for (let i = 0; i < e.dataTransfer.files.length; i++) {
            const element = e.dataTransfer.files[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.Detail.shop_image_banner.length < 6) {
                  if (imageSize < 1) {
                    this.Detail.shop_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.shop_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.shop_image_banner.every((key) => key.statusFail === false)) {
                    this.disableUploadButton = false
                  } else {
                    this.disableUploadButton = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 6 รูปภาพ',
                    showConfirmButton: false,
                    timer: 1500,
                    status: false
                  })
                }
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    DropImageAdvert (e) {
      // console.log('Drop image', e.dataTransfer.files)
      if (this.Detail.shop_advert.length < 5) {
        if (e.dataTransfer.files !== undefined || e.dataTransfer.files.length < 5) {
          for (let i = 0; i < e.dataTransfer.files.length; i++) {
            const element = e.dataTransfer.files[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.Detail.shop_advert.length < 5) {
                  if (imageSize < 1) {
                    this.Detail.shop_advert.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.shop_advert.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.shop_advert.every((key) => key.statusFail === false)) {
                    this.disableUploadButtonAdvert = false
                  } else {
                    this.disableUploadButtonAdvert = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 5 รูปภาพ',
                    showConfirmButton: false,
                    timer: 1500,
                    status: false
                  })
                }
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 5 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 5 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    checkFackbook () {
      const spaceRegex = /^\s/
      if (spaceRegex.test(this.facebook)) {
        this.facebook = this.facebook.replace(/\s/g, '')
      }
    },
    checkLine () {
      const spaceRegex = /^\s/
      if (spaceRegex.test(this.line)) {
        this.line = this.line.replace(/\s/g, '')
      }
    },
    backToDesignShop () {
      if (this.MobileSize) {
        this.$router.push({ path: '/designShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/designShop' }).catch(() => {})
      }
    },
    async GetDetailShop () {
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.result === 'SUCCESS') {
          this.isJVShop = response.data[0].is_JV === 'yes'
          this.Detail.seller_shop_id = this.shopID
          if (response.data[0].shop_image.length !== 0) {
            this.Detail.shop_logo_image.push({
              image_data: response.data[0].shop_image[0].media_path,
              image_data_lazy: response.data[0].shop_image[0].media_path_lazy,
              path: response.data[0].shop_image[0].media_path,
              name: response.data[0].shop_image[0].name,
              size: ''
            })
            this.show = false
            this.showError = false
          } else {
            this.show = false
            this.showError = false
            this.Detail.shop_logo_image = []
          }
          if (response.data[0].image_banner.length !== 0) {
            for (let i = 0; i < response.data[0].image_banner.length; i++) {
              this.Detail.shop_image_banner.push({
                image_data: response.data[0].image_banner[i].path,
                image_data_lazy: response.data[0].image_banner[i].path_lazy,
                path: response.data[0].image_banner[i].path,
                name: response.data[0].image_banner[i].name,
                size: '',
                statusFail: false
              })
              this.DataToShowImageBanner.push({
                image_data: response.data[0].image_banner[i].path,
                image_data_lazy: response.data[0].image_banner[i].path_lazy,
                path: response.data[0].image_banner[i].path,
                name: response.data[0].image_banner[i].name,
                size: '',
                statusFail: false
              })
            }
          } else {
            this.Detail.shop_image_banner = []
            this.DataToShowImageBanner = []
          }
          // console.log(this.Detail.shop_logo_image)
          this.shopName = response.data[0].shop_name
          this.useAttorney = response.data[0].use_attorney
          this.taxNumber = response.data[0].tax_id
          this.shopURL = response.data[0].url_name
          this.descriptionShop = response.data[0].shop_description
          this.shortName = response.data[0].short_name
          this.mobile = response.data[0].shop_phone.length > 0 ? response.data[0].shop_phone[0].phone : ''
          this.facebook = response.data[0].facebook_url
          if (response.data[0].shipping_method.length !== 0) {
            this.switchShipping = true
            this.SelectShipping = response.data[0].shipping_method[0].courier
          } else {
            this.switchShipping = false
            this.SelectShipping = []
          }
          this.switchEstimate = response.data[0].use_estimate === 'yes'
          if (this.switchEstimate) {
            this.switchShipping = false
          }
          if (response.data[0].payment_method !== null) {
            if (response.data[0].payment_method.length !== 0) {
              this.SelectPaymentType = response.data[0].payment_method
            } else {
              this.SelectPaymentType = []
            }
          } else {
            this.SelectPaymentType = []
          }
          this.selectInstallmentType = response.data[0].installment_method
          this.line = response.data[0].line_id
          if (response.data[0].shop_status === 'active') {
            this.openshop = true
          } else {
            this.openshop = false
          }
          if (response.data[0].shop_status === 'active') {
            this.publicshop = true
          } else {
            this.publicshop = false
          }
          if (response.data[0].partner_show === 'yes') {
            this.partner = true
          } else {
            this.partner = false
          }
          // this.MerchantKey = response.data[0].merchant_key
          if (response.data[0].payment_costs !== null) {
            if (response.data[0].payment_costs.length !== 0) {
              this.SelectType = 'contact'
              this.SelectTypePay = response.data[0].payment_costs
            } else {
              this.SelectType = 'no_contact'
              this.SelectTypePay = []
            }
          } else {
            this.SelectType = 'no_contact'
            this.SelectTypePay = []
          }
          this.accountName = response.data[0].account_bank
          this.accountNo = response.data[0].account_no
          this.bankCode = response.data[0].bank_code
          // if (this.bankCode === '' || this.bankCode === undefined || this.bankCode === null) {
          //   this.switchEWHT = false
          // } else {
          //   this.switchEWHT = true
          // }
          this.switchEWHT = response.data[0].use_ewht === 'yes'
          this.switchStoreFront = response.data[0].store_front === 'yes'
          this.name = response.data[0].first_name
          this.surname = response.data[0].last_name
          this.email = response.data[0].shop_email.length !== 0 ? response.data[0].shop_email[0].seller_email : ''
          this.mobileNumber = response.data[0].shop_phone.length > 1 ? response.data[0].shop_phone[1].phone : ''
          this.Detail.shop_address[0].id = response.data[0].address_detail[0].id
          this.addressDetail = response.data[0].address_detail[0].detail
          this.houseNo = response.data[0].address_detail[0].house_no
          if (this.isJVShop) {
            this.teamEmail = response.data[0].team_email
          } else {
            this.teamEmail = ''
          }
          this.subdistrict = response.data[0].address_detail[0].sub_district
          this.district = response.data[0].address_detail[0].district
          this.province = response.data[0].address_detail[0].province
          this.details = response.data[0].address_detail[0].details
          this.zipcode = response.data[0].address_detail[0].zipcode
          this.default_address = response.data[0].address_detail[0].default_address === 'main'
          if (response.data[0].image_news.length !== 0) {
            for (let j = 0; j < response.data[0].image_news.length; j++) {
              this.Detail.shop_advert.push({
                image_data: response.data[0].image_news[j].path,
                image_data_lazy: response.data[0].image_news[j].path_lazy,
                path: response.data[0].image_news[j].path,
                // name: response.data[0].shop_profile[0].name,
                name: response.data[0].image_news[j].name,
                size: '',
                statusFail: false
              })
              this.DataToShowImageAdvert.push({
                image_data: response.data[0].image_news[j].path,
                image_data_lazy: response.data[0].image_news[j].path_lazy,
                path: response.data[0].image_news[j].path,
                // name: response.data[0].shop_profile[0].name,
                name: response.data[0].image_news[j].name,
                size: '',
                statusFail: false
              })
            }
          } else {
            this.Detail.shop_advert = []
            this.DataToShowImageAdvert = []
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
        }
        // console.log(this.Detail)
      }
    },
    clearStepOne () {
      this.Detail.shop_logo_image = []
      this.Detail.shop_image = []
      this.DataImageShop = []
      this.show = false
      this.showError = false
      this.DataToShowImageBanner = []
      this.Detail.shop_image_banner = []
      this.Detail.image_banner = []
      this.shopName = ''
      this.shopURL = ''
      this.descriptionShop = ''
      this.SelectPaymentType = ''
      this.switchShipping = true
      this.SelectShipping = ''
      this.selectInstallmentType = []
      this.shortName = ''
      this.mobile = ''
      this.facebook = ''
      this.line = ''
      this.openshop = false
      this.partner = false
      // this.MerchantKey = ''
      this.SelectType = 'no_contact'
      this.SelectTypePay = []
      this.switchEWHT = false
    },
    clearStepTwo () {
      this.name = ''
      this.surname = ''
      this.email = ''
      if (this.isJVShop) {
        this.teamEmail = ''
      }
      this.mobileNumber = ''
      this.addressDetail = ''
      this.houseNo = ''
      this.subdistrict = ''
      this.checkSubDistrictError = false
      this.district = ''
      this.checkDistrictError = false
      this.province = ''
      this.checkProvinceError = false
      this.zipcode = ''
      this.checkZipcodeError = false
      this.setDefault = true
    },
    clearStepThree () {
      this.DataToShowImageAdvert = []
      this.Detail.image_news = []
      this.Detail.shop_advert = []
    },
    ZoomIn () {
      var myImg = document.getElementById('imgBig')
      var currWidth = myImg.clientWidth
      if (this.IpadSize) {
        if (currWidth > 750) {
          this.disableZoomInButton = true
          // return false
        } else {
          myImg.style.width = (currWidth + 100) + 'px'
          this.disableZoomInButton = false
        }
      } else {
        if (currWidth > 1200) {
          this.disableZoomInButton = true
          return false
        } else {
          myImg.style.width = (currWidth + 100) + 'px'
          this.disableZoomInButton = false
        }
      }
    },
    ZoomOut () {
      var myImg = document.getElementById('imgBig')
      var currWidth = myImg.clientWidth
      if (this.IpadSize) {
        if (currWidth < 500) {
          this.disableZoonOutButton = true
          return false
        } else {
          myImg.style.width = (currWidth - 100) + 'px'
          this.disableZoonOutButton = false
        }
      } else {
        if (currWidth < 800) {
          this.disableZoonOutButton = true
          return false
        } else {
          myImg.style.width = (currWidth - 100) + 'px'
          this.disableZoonOutButton = false
        }
      }
    },
    bactToModalImage () {
      if (this.fromClick === 'Advert') {
        this.dialogOpenDialogUploadAdvert = true
        this.dialogShowImage = false
      } else {
        this.dialogOpenDialogUploadBanner = true
        this.dialogShowImage = false
      }
    },
    ShowBigImage (from, image) {
      this.imagetoBig = image
      this.fromClick = ''
      if (from === 'Advert') {
        this.fromClick = from
        this.dialogOpenDialogUploadAdvert = false
        this.dialogShowImage = true
      } else {
        this.fromClick = from
        this.dialogOpenDialogUploadBanner = false
        this.dialogShowImage = true
      }
    },
    uploadToShow () {
      this.DataToShowImageBanner = []
      this.DataToShowImageBanner = [...this.Detail.shop_image_banner]
      this.dialogOpenDialogUploadBanner = false
    },
    uploadAdvert () {
      this.DataToShowImageAdvert = []
      this.DataToShowImageAdvert = [...this.Detail.shop_advert]
      this.dialogOpenDialogUploadAdvert = false
    },
    cancelUploadBanner () {
      this.Detail.shop_image_banner = []
      this.Detail.shop_image_banner = [...this.DataToShowImageBanner]
      this.dialogOpenDialogUploadBanner = false
    },
    cancelUploadAdvert () {
      this.Detail.shop_advert = []
      this.Detail.shop_advert = [...this.DataToShowImageAdvert]
      this.dialogOpenDialogUploadAdvert = false
    },
    remove (item) {
      const index = this.SelectTypePay.indexOf(item.value)
      if (index >= 0) this.SelectTypePay.splice(index, 1)
    },
    removePayment (item) {
      const index = this.SelectPaymentType.indexOf(item.value)
      if (index >= 0) this.SelectPaymentType.splice(index, 1)
    },
    removeShipping (item) {
      const index = this.SelectShipping.indexOf(item.code)
      if (index >= 0) this.SelectShipping.splice(index, 1)
    },
    removeInstallment (item) {
      const index = this.selectInstallmentType.indexOf(item.value)
      if (index >= 0) this.selectInstallmentType.splice(index, 1)
    },
    uploadImageShop () {
      document.getElementById('imageShop').click()
    },
    async UploadImageShop () {
      this.show = true
      this.showError = false
      this.showErrorText = ''
      this.percentUpload = 0
      var data = {}
      const element = this.DataImageShop
      const imageSize = element.size / 1024 / 1024
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
        var url = URL.createObjectURL(element)
        const image = new Image()
        const imageDimensions = await new Promise((resolve) => {
          image.onload = () => {
            const dimensions = {
              height: image.height,
              width: image.width
            }
            resolve(dimensions)
          }
          image.src = url
        })
        this.Detail.shop_logo_image = []
        if (imageSize < 1) {
          if (imageDimensions.height <= 244 && imageDimensions.width <= 244) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = async () => {
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              data = {
                image: [resultReader.split(',')[1]],
                type: 'shop',
                seller_shop_id: this.shopID
              }
              await this.$store.dispatch('actionsUploadToS3', data)
              var response = await this.$store.state.ModuleShop.stateUploadToS3
              if (response.message === 'List Success.') {
                this.Detail.shop_logo_image.push({
                  image_data: response.data.list_path[0].path,
                  image_data_lazy: response.data.list_path[0].path_lazy,
                  path: url,
                  name: this.DataImageShop.name,
                  size: this.DataImageShop.size
                })
                setInterval(() => {
                  if (this.percentUpload === 100) {
                    this.show = false
                  }
                  this.percentUpload += 25
                }, 1000)
              }
            }
          } else {
            this.show = false
            this.showError = true
            this.showErrorText = 'รูปต้องไม่เกินขนาด 244 x 244'
            this.Detail.shop_logo_image = []
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปไม่เกินขนาด 244 x 244',
              showConfirmButton: false,
              timer: 1500
            })
          }
        } else {
          this.show = false
          this.showError = true
          this.showErrorText = 'ไฟล์มีขนาดใหญ่เกินไป'
          this.Detail.shop_logo_image.push({
            name: this.DataImageShop.name
          })
          // this.$swal.fire({
          //   icon: 'warning',
          //   text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 1 MB',
          //   showConfirmButton: false,
          //   timer: 1500
          // })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    cancelImageShop () {
      this.show = false
      this.percentUpload = 0
      this.DataImageShop = []
      this.Detail.shop_logo_image = []
    },
    openModalUploadBanner () {
      this.Detail.shop_image_banner = [...this.DataToShowImageBanner]
      this.dialogOpenDialogUploadBanner = true
    },
    openModalUploadAdvert () {
      this.Detail.shop_advert = [...this.DataToShowImageAdvert]
      this.dialogOpenDialogUploadAdvert = true
    },
    CheckSpacebarMobile (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    async CheckBusiness () {
      await this.$store.dispatch('actionsCheckUserBusiness')
      var response = await this.$store.state.ModuleShop.stateCheckUserBusiness
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.haveCitizen = response.data.have_citizen === 'yes'
        this.haveBusiness = response.data.have_business === 'yes'
        // console.log(this.haveCitizen, this.haveBusiness)
      } else {
        this.haveCitizen = false
        this.haveBusiness = false
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    closeDialogConfirm () {
      this.Detail.image_news = []
      this.dialogAwaitCreateShop = !this.dialogAwaitCreateShop
    },
    UploadImage () {
      if (this.Detail.shop_image_banner.length < 6) {
        if (this.DataImage !== undefined || this.DataImage.length < 6) {
          for (let i = 0; i < this.DataImage.length; i++) {
            const element = this.DataImage[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.Detail.shop_image_banner.length < 6) {
                  if (imageSize < 1) {
                    this.Detail.shop_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: this.DataImage[i].name,
                      size: this.DataImage[i].size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.shop_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: this.DataImage[i].name,
                      size: this.DataImage[i].size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.shop_image_banner.every((key) => key.statusFail === false)) {
                    this.disableUploadButton = false
                  } else {
                    this.disableUploadButton = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 6 รูปภาพ',
                    showConfirmButton: false,
                    timer: 1500,
                    status: false
                  })
                }
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    onPickFileAdvert () {
      document.getElementById('file_input_Advert').click()
    },
    UploadImageAdvert () {
      if (this.Detail.shop_advert.length < 5) {
        if (this.DataImageAdvert !== undefined || this.DataImageAdvert.length < 5) {
          for (let i = 0; i < this.DataImageAdvert.length; i++) {
            const element = this.DataImageAdvert[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.Detail.shop_advert.length < 5) {
                  if (imageSize < 1) {
                    this.Detail.shop_advert.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: this.DataImageAdvert[i].name,
                      size: this.DataImageAdvert[i].size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.shop_advert.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: this.DataImageAdvert[i].name,
                      size: this.DataImageAdvert[i].size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.shop_advert.every((key) => key.statusFail === false)) {
                    this.disableUploadButtonAdvert = false
                  } else {
                    this.disableUploadButtonAdvert = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 5 รูปภาพ',
                    showConfirmButton: false,
                    timer: 1500,
                    status: false
                  })
                }
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 5 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 5 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.shop_image_banner.splice(index, 1)
      } else {
        this.DataImage.splice(index, 1)
        this.Detail.shop_image_banner.splice(index, 1)
      }
      if (this.Detail.shop_image_banner.every((key) => key.statusFail === false)) {
        this.disableUploadButton = false
      } else {
        this.disableUploadButton = true
      }
    },
    RemoveImageBanner (index, val) {
      this.Detail.shop_image_banner.splice(index, 1)
      this.DataToShowImageBanner.splice(index, 1)
    },
    RemoveImageAdvertOuter (index, val) {
      this.Detail.shop_advert.splice(index, 1)
      this.DataToShowImageAdvert.splice(index, 1)
    },
    RemoveImageAdvert (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.shop_advert.splice(index, 1)
      } else {
        this.DataImage.splice(index, 1)
        this.Detail.shop_advert.splice(index, 1)
      }
      if (this.Detail.shop_advert.every((key) => key.statusFail === false)) {
        this.disableUploadButton = false
      } else {
        this.disableUploadButton = true
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    async nextStep (val) {
      this.Detail.payment_costs = []
      this.$store.commit('openLoader')
      if (this.$refs.formOne.validate(true)) {
        await this.uploadBannerToS3(this.DataToShowImageBanner)
        this.Detail.shop_name = this.shopName
        this.Detail.shop_url = this.shopURL
        if (this.Detail.shop_logo_image.length !== 0) {
          this.Detail.shop_image.push({
            name: this.Detail.shop_logo_image[0].name,
            path: this.Detail.shop_logo_image[0].image_data,
            path_lazy: this.Detail.shop_logo_image[0].image_data_lazy,
            href: ''
          })
        } else {
          this.Detail.shop_image = []
        }
        this.Detail.tax_id = this.taxNumber
        this.Detail.payment_costs = this.SelectType === 'no_contact' ? [] : this.SelectTypePay
        this.Detail.shipping_method = [
          { service: '', courier: [] }
        ]
        if (this.switchShipping === true) {
          for (var i = 0; i < this.SelectShipping.length; i++) {
            this.Detail.shipping_method[0].service = 'ISHIP'
            this.Detail.shipping_method[0].courier.push(this.SelectShipping[i])
          }
          this.Detail.shipping_method = this.functionCheckDuplicateValue(this.Detail.shipping_method)
        } else {
          this.Detail.shipping_method = []
        }
        this.Detail.use_estimate = this.switchEstimate ? 'yes' : 'no'
        this.Detail.store_front = this.switchStoreFront ? 'yes' : 'no'
        this.Detail.shop_description = this.descriptionShop
        this.Detail.payment_method = this.SelectPaymentType
        this.Detail.installment_method = this.selectInstallmentType
        this.Detail.shop_status = this.openshop ? 'active' : 'inactive'
        this.Detail.public_show = this.openshop ? 'yes' : 'no'
        this.Detail.partner_show = this.partner ? 'yes' : 'no'
        this.Detail.have_partner = this.partner ? 'yes' : 'no'
        this.Detail.use_ewht = this.switchEWHT ? 'yes' : 'no'
        this.Detail.facebook_url = this.facebook
        this.Detail.line_id = this.line
        // this.Detail.merchant_key = this.MerchantKey
        this.Detail.shop_type = this.bussinessType
        this.Detail.shop_phone[0].phone = this.mobile
        this.Detail.receiver_account_name = this.accountName
        this.Detail.receiver_account_no = this.accountNo
        this.Detail.receiver_bank_code = this.bankCode
        this.stepper = val
        if (!this.MobileSize) {
          window.scrollTo(0, 0)
          this.$store.commit('closeLoader')
          this.$router.push({ path: `/EditShop?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
        } else {
          window.scrollTo(0, 0)
          this.$store.commit('closeLoader')
          this.$router.push({ path: `/EditShopMobile?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
        }
        sessionStorage.setItem('CheckStep1', 'step1')
      } else {
        this.$store.commit('closeLoader')
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    functionCheckDuplicateValue (arr) {
      const ids = arr.map(({ courier }) => courier)
      const filtered = arr.filter(({ courier }, index) => !ids.includes(courier, index + 1))
      return filtered
    },
    async uploadBannerToS3 (imageBanner) {
      var data = {
        image: [],
        type: '',
        seller_shop_id: ''
      }
      this.Detail.image_banner = []
      for (let i = 0; i < imageBanner.length; i++) {
        data.image.push(imageBanner[i].image_data)
      }
      data.type = 'shop_banner'
      data.seller_shop_id = this.shopID
      await this.$store.dispatch('actionsUploadToS3', data)
      var response = await this.$store.state.ModuleShop.stateUploadToS3
      if (response.message === 'List Success.') {
        for (let j = 0; j < response.data.list_path.length; j++) {
          this.Detail.shop_image_banner[j].image_data = response.data.list_path[j].path
          this.Detail.shop_image_banner[j].image_data_lazy = response.data.list_path[j].path_lazy
        }
        for (let k = 0; k < this.Detail.shop_image_banner.length; k++) {
          this.Detail.image_banner.push({
            name: this.Detail.shop_image_banner[k].name,
            path: this.Detail.shop_image_banner[k].image_data,
            path_lazy: this.Detail.shop_image_banner[k].image_data_lazy,
            href: ''
          })
        }
        this.$store.commit('closeLoader')
      }
    },
    nextStepThree (val) {
      this.Detail.shop_email = []
      this.$store.commit('openLoader')
      if (this.$refs.formTwo.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkSendAddress()
            if (check.length !== 0) {
              this.Detail.first_name = this.name
              this.Detail.last_name = this.surname
              this.Detail.shop_email.push({
                seller_email: this.email
              })
              if (this.isJVShop) {
                this.Detail.team_email = this.teamEmail
              }
              this.Detail.shop_phone[1].phone = this.mobileNumber
              this.Detail.short_name = this.shortName
              this.Detail.sale_name = this.saleName
              this.Detail.sale_tel_no = this.salePhone
              this.Detail.sale_mobile = this.saleMobilePhone
              this.Detail.sale_email = this.saleEmail
              this.Detail.sale_team = this.saleTeamName
              this.Detail.shop_address[0].default_address = this.setDefault ? 'main' : ''
              this.Detail.shop_address[0].house_no = this.houseNo
              this.Detail.shop_address[0].detail = this.addressDetail
              this.Detail.shop_address[0].province = this.province
              this.Detail.shop_address[0].district = this.district
              this.Detail.shop_address[0].sub_district = this.subdistrict
              this.Detail.shop_address[0].zipcode = this.zipcode
              this.stepper = val
              if (this.MobileSize === false) {
                window.scrollTo(0, 0)
                this.$store.commit('closeLoader')
                this.$router.push({ path: `/EditShop?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
              } else {
                window.scrollTo(0, 0)
                this.$store.commit('closeLoader')
                this.$router.push({ path: `/EditShopMobile?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
              }
              sessionStorage.setItem('CheckStep2', 'step2')
            } else {
              this.$store.commit('closeLoader')
              this.checkConfirmAddress()
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$store.commit('closeLoader')
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$store.commit('closeLoader')
        this.callCheckAdress()
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    backStep (val) {
      this.stepper = val
      if (this.MobileSize === false) {
        this.$router.push({ path: `/EditShop?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/EditShopMobile?step=${this.stepper}&shopID=${this.shopID}` }).catch(() => {})
      }
    },
    async checkEditShop () {
      this.$store.commit('openLoader')
      if (this.$refs.formThree.validate(true)) {
        if (this.DataToShowImageAdvert.length !== 0) {
          await this.uploadAdvertToS3(this.DataToShowImageAdvert)
        } else {
          this.Detail.image_news = []
        }
        this.Detail.use_attorney = this.useAttorney
        this.$store.commit('closeLoader')
        this.dialogAwaitCreateShop = true
      } else {
        this.$store.commit('closeLoader')
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async confirmEditShop () {
      this.dialogAwaitCreateShop = false
      this.$store.commit('openLoader')
      var headerChat = ''
      var boiID = ''
      var dataChat = {
        user_id: null
      }
      await this.$store.dispatch('actionslistBotChatWithUser', dataChat)
      var resChat = await this.$store.state.ModuleHompage.statelistBotChatWithUser
      if (resChat.message === 'list bot chat success') {
        var listChat = []
        listChat = resChat.data.listChat
        boiID = listChat.filter(item => item.shopNGSID === this.shopID)
        if (boiID.length !== 0 && boiID[0].botToken !== null) {
          headerChat = {
            headers: { Authorization: `Bearer ${boiID[0].botToken}` }
          }
        }
      }
      await this.$store.dispatch('actionEditShop', this.Detail)
      var res = await this.$store.state.ModuleShop.stateEditShop
      if (res.result === 'SUCCESS') {
        if (boiID.length !== 0 && boiID[0].botToken !== null) {
          var dataUpdateChat = {
            bot_name: this.Detail.shop_name,
            profile_picture: this.Detail.shop_logo_image[0].image_data
          }
          var dataUpdateOwnChat = {
            shopNGSID: boiID[0].shopNGSID,
            bot_name: this.Detail.shop_name,
            profile_picture: this.Detail.shop_logo_image[0].image_data
          }
          var responseChat = await this.axios.put(`${process.env.VUE_APP_CHAT_PLUGIN}/edit-bot-data`, dataUpdateChat, headerChat)
          var responseOwnChat = await this.$store.dispatch('actionsUpdateSellerBot', dataUpdateOwnChat)
          // console.log('responseOwnChat', responseOwnChat)
          if (responseChat.message === 'success' && responseOwnChat.message === 'update shop bot success') {
            this.dialogSuccessCreateShop = true
            this.$store.commit('closeLoader')
          } else {
            this.dialogSuccessCreateShop = true
            this.$store.commit('closeLoader')
          }
        } else {
          this.dialogSuccessCreateShop = true
          this.$store.commit('closeLoader')
        }
        sessionStorage.removeItem('CheckStep1')
        sessionStorage.removeItem('CheckStep2')
      } else {
        this.$store.commit('closeLoader')
        this.checkTextError(res.message)
        this.dialogFailCreateShop = true
      }
    },
    checkTextError (error) {
      this.messageError = ''
      if (error === 'Not found this shop data.') {
        this.messageError = 'ไม่พบข้อมูลร้านค้านี้ในระบบ โปรดติดต่อเข้าหน้าที่'
      } else if (error === 'This short name has already used.') {
        this.messageError = 'ชื่อย่อร้านค้านี้ถูกใช้งานแล้ว'
      }
    },
    closeEditShop () {
      this.dialogSuccessCreateShop = false
      if (this.MobileSize) {
        this.$router.push({ path: '/designShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/designShop' }).catch(() => {})
      }
    },
    async uploadAdvertToS3 (imageAdvert) {
      var data = {
        image: [],
        type: '',
        seller_shop_id: ''
      }
      for (let i = 0; i < imageAdvert.length; i++) {
        data.image.push(imageAdvert[i].image_data)
      }
      data.type = 'shop_banner'
      data.seller_shop_id = this.shopID
      await this.$store.dispatch('actionsUploadToS3', data)
      var response = await this.$store.state.ModuleShop.stateUploadToS3
      if (response.message === 'List Success.') {
        for (let j = 0; j < response.data.list_path.length; j++) {
          this.Detail.shop_advert[j].image_data = response.data.list_path[j].path
          this.Detail.shop_advert[j].image_data_lazy = response.data.list_path[j].path_lazy
        }
        for (let k = 0; k < this.Detail.shop_advert.length; k++) {
          this.Detail.image_news.push({
            name: this.Detail.shop_advert[k].name,
            path: this.Detail.shop_advert[k].image_data,
            path_lazy: this.Detail.shop_advert[k].image_data_lazy,
            href: ''
          })
        }
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistrict
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.district
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.province
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcode)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistrict)
      this.checkdistrictConfirm(this.district)
      this.checkprovinceConfirm(this.province)
      this.checkzipcodeConfirm(this.zipcode)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style>
.v-radio .v-icon {
  color: #27AB9C;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  color: #212121;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.v-input .v-input__slot {
  border-radius: 8px !important;
}
.v-input--selection-controls {
  margin-top: 0px;
  padding-top: 0px;
}
li::marker {
  color: #27AB9C;
}
</style>
