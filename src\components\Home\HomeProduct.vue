<template>
  <v-container>
    <v-card outlined>
      <v-card-title class="justify-content: center;">
        {{ header }}
        <v-spacer></v-spacer>
        <v-btn text @click="GetAllProduct">ดูทั้งหมด</v-btn>
      </v-card-title>
    </v-card>
    <v-card outlined>
      <v-container>
        <!-- <pre>{{propsData}}</pre> -->
        <div v-if="propsData !== 'ยังไม่มีรายการสินค้าขายดี'">
          <vue-horizontal-list :items='propsData' :options='options'>
            <template v-slot:default='{ item }'>
              <a-skeleton :loading="check === true ? !loading : loading">
                <CardProducts :itemProduct='item' />
              </a-skeleton>
            </template>
          </vue-horizontal-list>
        </div>
        <div v-else>
          <h2>{{propsData}}</h2>
        </div>
      </v-container>
    </v-card>
  </v-container>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'
import { Skeleton } from 'ant-design-vue'
export default {
  props: ['propsData', 'header', 'check'],
  components: {
    'a-skeleton': Skeleton,
    VueHorizontalList,
    CardProducts: () => import('@/components/Card/ProductCard')
  },
  data () {
    return {
      options: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        }
      },
      loading: true
    }
  },
  methods: {
    GetAllProduct () {
      this.$router.push(`/ListProduct/${this.header}?page=1`).catch(() => {})
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1250px;
}
</style>
