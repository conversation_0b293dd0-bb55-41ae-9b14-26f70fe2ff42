<template>
  <v-container class="text-center" style="width:1223px">
    <v-row
      align="center"
      justify="center"
      class="mt-2 "
    >
      <template v-for="(item, i) in dataCupon" >
        <v-col
          :key="i"
          cols="12"
          md="4"
        >
            <div class="d-flex justify-center" style=" position:relative; margin-top: 40px">
              <div  style="position:absolute; z-index:2; margin-top:-80px">
                <v-img :src="item" width="324" height="119" contain style="cursor: pointer;"></v-img>
              </div>
                <v-card width="400px"  height="150px" color="primary" ></v-card>
            </div>
        </v-col>
      </template>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      dataCupon: [
        require('@/assets/ImageINET-Marketplace/Cupon/Gift-1.png'),
        require('@/assets/ImageINET-Marketplace/Cupon/Group638.png'),
        require('@/assets/ImageINET-Marketplace/Cupon/Gif.png')
      //   require('@/assets/ImageINET-Marketplace/Cupon/Group172.png'),
      //   require('@/assets/ImageINET-Marketplace/Cupon/Group173.png'),
      //   require('@/assets/ImageINET-Marketplace/Cupon/Group174.png')
      ]
      // dataColor: [
      //   '#F2F2',
      //   '#2F2',
      //   '#F2f'
      // ]
    }
  }
}
</script>

<style>
.container {
  max-width: 1250px;
}
</style>
