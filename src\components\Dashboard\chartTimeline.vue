<template lang="html">
        <div id="chart">
        <div id="chart-timeline">
        <apexchart type="area" height="350" ref="chart" :options="chartOptions" :series="series"></apexchart>
      </div>
      </div>
</template>

<script>
// import ApexCharts from 'apexcharts'
// import dataTest from '../library/timeline.json'
// import eventBus from '@/components/eventBus'
import VueApexCharts from 'vue-apexcharts'
export default {
  name: 'ApexChart',
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      keyReload: 0,
      series2: [{
        data: [
          [Math.floor(new Date().getTime() / 1000), 0]
        ]
      }],
      series: [],
      chartOptions: {
        subtitle: {
          align: 'left'
        },
        chart: {
          // toolbar: {
          //   show: true,
          //   offsetX: -820,
          //   offsetY: -10
          // },
          id: 'area-datetime',
          type: 'area',
          height: 350,
          zoom: {
            autoScaleYaxis: true
          },
          selection: {
            enabled: true
          }
        },
        title: {
          text: 'กราฟข้อมูลการขายสินค้าทั้งหมดของร้าน',
          align: 'left',
          offsetX: 12,
          offsetY: 10
        },
        // annotations: {
        //   yaxis: [{
        //     y: 30,
        //     borderColor: '#999',
        //     label: {
        //       show: true,
        //       text: 'Support',
        //       style: {
        //         color: '#fff',
        //         background: '#00E396'
        //       }
        //     }
        //   }],
        //   xaxis: [{
        //     x: new Date('01 Nov 2022').getTime(),
        //     borderColor: '#999',
        //     yAxisIndex: 0,
        //     label: {
        //       show: true,
        //       text: 'Rally',
        //       style: {
        //         color: '#fff',
        //         background: '#775DD0'
        //       }
        //     }
        //   }]
        // },
        dataLabels: {
          enabled: true
        },
        markers: {
          size: 0,
          style: 'hollow'
        },
        xaxis: {
          type: 'category',
          labels: {
            formatter: function (val) {
              return new Date(val).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
            }
          }
          // min: new Date('29 Mar 2022').getTime(),
          // tickAmount: 6
        },
        tooltip: {
          x: {
            format: 'dd MMM yyyy'
          }
          // fixed: {
          //   enabled: true,
          //   position: 'topLeft'
          // }
        }
        // fill: {
        //   type: 'gradient',
        //   gradient: {
        //     shadeIntensity: 1,
        //     opacityFrom: 0.7,
        //     opacityTo: 0.9,
        //     stops: [0, 100]
        //   }
        // }
      },
      selection: 'one_month',
      dataFreq: [],
      dataSum: [],
      headers2: [],
      SETT: [],
      toDay: new Date().toISOString().slice(0, 10),
      Day: `${new Date().toISOString().slice(0, 7)}-01`
    }
  },
  created () {
    this.init()
    this.$EventBus.$on('appendData', this.appendData)
    this.$EventBus.$on('updateData', this.updateData)
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
  },
  destroyed () {
    this.$EventBus.$off('appendData')
    this.$EventBus.$off('updateData')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    dataSum (e) {
      // console.log('EdataSum', e)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async init () {
      const data = await {
        start_date: '',
        end_date: '',
        search_type: 'all'
      }
      await this.$store.dispatch('actionsDashboard', data)
      this.series = await [{ name: 'รายได้', data: this.$store.state.ModuleShop.stateTimeline }]
      // const selectedMonth = 12
      // const selectedYear = 2022
      // const weekTransitionDays = []
      // const firstDayOfMonth = (new Date(selectedYear, selectedMonth - 1, 1)).getDay()
      // const daysInMonth = (new Date(selectedYear, selectedMonth, 0)).getDate()
      // console.log('firstDayOfMonth:', firstDayOfMonth, 'daysInMonth:', daysInMonth)
    },
    // reload () {
    //   this.$forceUpdate()
    // },
    updateData (timeline) {
      // console.log('timeline ->', timeline.end)
      this.selection = timeline.text
      if (timeline.text === 'one_month') {
        this.$refs.chart.zoomX(
          new Date(timeline.end).getTime(),
          new Date(timeline.start).getTime()
        )
      } else if (timeline.text === 'six_months') {
        // console.log('six_months--***', timeline)
        this.$refs.chart.zoomX(
          new Date(timeline.end).getTime(),
          new Date(timeline.start).getTime()
        )
      } else if (timeline.text === 'one_year') {
        // console.log('one_year', timeline)
        this.$refs.chart.zoomX(
          new Date(timeline.end).getTime(),
          new Date(timeline.start).getTime()
        )
      } else if (timeline.text === 'ytd') {
        // console.log('one_yearcvcv', timeline)
        this.$refs.chart.zoomX(
          new Date(timeline.end).getTime(),
          new Date(timeline.start).getTime()
        )
      } else {
        this.selectDataAll()
      }
    },
    async appendData () {
      // console.log('stateTimeline', this.$store.state.ModuleShop.stateTimeline)
      this.series = await [{ data: this.$store.state.ModuleShop.stateTimeline }]
      // console.log('series2', this.series2)
      // console.log('series', this.series)
    },
    async selectDataAll () {
      this.series = await []
      const data = await {
        start_date: '',
        end_date: '',
        search_type: 'all'
      }
      await this.$store.dispatch('actionsDashboard', data)
      this.series = await [{ data: this.$store.getters.drawChart }]
      await this.$EventBus.$emit('ResetTable')
      await this.reloadPage()
    },
    async reloadPage () {
      await window.location.reload(true)
    },
    getValues (e) {
      const as = e.map(x => { return { ...x } })
      return as
    }
  }
}
</script>

<style lang="css" scoped>
.inner-right {
    height: 300px;
    max-height: 300px;
    overflow-y: scroll;
}
#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
