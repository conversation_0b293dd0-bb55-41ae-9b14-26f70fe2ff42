<template>
<v-container :class="MobileSize ? 'mt-3' : ''">
  <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
    <!-- ส่วน หัวเรื่อง -->
    <v-card-title v-if="!MobileSize" style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;">{{ $t('withdrawAffiliate.WithdrawHistory') }}</v-card-title>
    <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToUsr()">mdi-chevron-left</v-icon>{{ $t('withdrawAffiliate.WithdrawHistory') }}</v-card-title>
    <!-- ส่วน หัวเรื่อง -->

    <!-- ส่วน filter ช่วงเวลา -->
    <!-- <v-row class="ml-2 mt-2 mr-2">
      <v-col
      cols="12"
      sm="6"
      md="4"
    >
      <v-select
        v-model="selected"
        :items="selectFilteritem"
        item-text="name"
        item-value="id"
        label="เลือกช่วงเวลา"
        height="22px"
        dense
        outlined
        :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
        @change="handleSelectChange"
      ></v-select>
    </v-col>
    </v-row> -->
    <!-- ส่วน filter ช่วงเวลา -->

    <!-- ส่วน Export -->
    <v-row v-if="!MobileSize && !IpadSize" class="mx-3">
      <v-row class="mx-3 my-4" >
        <v-col cols="10"></v-col>
        <v-col cols="2">
          <v-btn width="100%" style="height: 40px; font-size: small;" color="#38b2a4" class="white--text" @click="exportExcel()" rounded>Export</v-btn>
        </v-col>
      </v-row>
    </v-row>
    <v-row v-if="IpadSize" class="mx-1 mb-2" style="margin-top: -20px;">
      <v-row class="mx-3 my-4" >
        <v-col cols="8"></v-col>
        <v-col cols="4">
          <v-btn width="100%" style="height: 40px; font-size: small;" color="#38b2a4" class="white--text" @click="exportExcel()" rounded>Export</v-btn>
        </v-col>
      </v-row>
    </v-row>
    <v-row v-if="MobileSize" class="mx-1 mb-2">
      <v-row class="mx-3 my-4" >
        <v-col cols="6"></v-col>
        <v-col cols="6">
          <v-btn width="100%" style="height: 40px; font-size: small;" color="#38b2a4" class="white--text" @click="exportExcel()" rounded>Export</v-btn>
        </v-col>
      </v-row>
    </v-row>
    <!-- ส่วน Export -->

    <!-- ส่วน สรุปยอดและการถอนเงิน -->
    <v-row v-if="!MobileSize && !IpadSize" class="mx-3 my-2">
      <v-col cols="7">
        <v-row>
          <v-col cols="6">
            <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
              <v-row>
                <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                    <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Views') }}</span>
                    <div style="text-align: right;">
                      <v-tooltip top v-if="IpadProSize">
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ viewClick | formatNumber }}
                          </span>
                        </template>
                        <span>{{ viewClick }}</span>
                      </v-tooltip>
                      <v-tooltip top v-else>
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: xx-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ viewClick | formatNumber }}
                          </span>
                        </template>
                        <span>{{ viewClick }}</span>
                      </v-tooltip>
                      <br>
                      <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Times') }}</span>
                    </div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="6">
            <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
              <v-row>
                <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                    <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Orders') }}</span>
                    <div style="text-align: right;">
                      <v-tooltip top v-if="IpadProSize">
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalOrderSuccess | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalOrderSuccess }}</span>
                      </v-tooltip>
                      <v-tooltip top v-else>
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: xx-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalOrderSuccess | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalOrderSuccess }}</span>
                      </v-tooltip>
                      <br>
                      <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Items') }}</span>
                    </div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="6">
            <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
              <v-row>
                <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                    <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.TotalSales') }}</span>
                    <div style="text-align: right;">
                      <v-tooltip top v-if="IpadProSize">
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalSale | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalSale }}</span>
                      </v-tooltip>
                      <v-tooltip top v-else>
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: xx-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalSale | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalSale }}</span>
                      </v-tooltip>
                      <br>
                      <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                    </div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="6">
            <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
              <v-row>
                <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                    <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Commission') }}</span>
                    <div style="text-align: right;">
                      <v-tooltip top v-if="IpadProSize">
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalEstCommision | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalEstCommision }}</span>
                      </v-tooltip>
                      <v-tooltip top v-else>
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: xx-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalEstCommision | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalEstCommision }}</span>
                      </v-tooltip>
                        <br>
                      <span style="font-size: smaller; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                    </div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
    </v-col>
    <v-col cols="5">
        <v-row>
          <v-col>
            <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 154px; border-radius: 15px;">
              <v-row>
                <v-col cols="12" style="display: flex; flex-direction: column;">
                    <span style="color: #27AB9C; font-size: x-large; font-weight: 600; margin-bottom: 20px;">Affiliate</span>
                    <div style="text-align: right;">
                      <v-tooltip top v-if="IpadProSize">
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: 36px; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalWithdraw | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalWithdraw }}</span>
                      </v-tooltip>
                      <v-tooltip top v-else>
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            style="font-weight: 700; font-size: 48px; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                          >
                            {{ totalWithdraw | formatNumber }}
                          </span>
                        </template>
                        <span>{{ totalWithdraw }}</span>
                      </v-tooltip>
                        <br>
                      <span style="font-size: small; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                    </div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-row>
              <v-col cols="6" style="height: 50px;" v-if="IpadProSize">
                <span style="font-size: 10px;">*{{ $t('withdrawAffiliate.FeeOnly') }}/{{ $t('withdrawAffiliate.Times') }}</span><br>
                <span style="font-size: 10px;">*{{ $t('withdrawAffiliate.MinWithdrawNote') }}</span>
              </v-col>
              <v-col cols="6" style="height: 50px;" v-else>
                <span style="font-size: smaller;">*{{ $t('withdrawAffiliate.FeeOnly') }}/{{ $t('withdrawAffiliate.Times') }}</span><br>
                <span style="font-size: smaller;">*{{ $t('withdrawAffiliate.MinWithdrawNote') }}</span>
              </v-col>
              <v-col cols="6" style="height: 50px;">
                <v-btn width="100%" style="height: 40px; font-size: small;" color="error" :disabled="disabledWithdraw" @click="openWithdraw = true" rounded>{{ $t('withdrawAffiliate.Withdraw') }}</v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
    </v-col>
    </v-row>
    <v-row v-if="IpadSize" class="mx-3" style="margin: -20px 0 20px 0;">
      <v-col cols="12">
      <v-row>
      <v-col cols="12">
         <v-row>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Views') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ viewClick | formatNumber }}
                            </span>
                          </template>
                          <span>{{ viewClick }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Times') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Orders') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalOrderSuccess | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalOrderSuccess }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Items') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.TotalSales') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalSale | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalSale }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Commission') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalEstCommision | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalEstCommision }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
      </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-row>
            <v-col>
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 154px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column;">
                      <span style="color: #27AB9C; font-size: x-large; font-weight: 600; margin-bottom: 20px;">Affiliate</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: 36px; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalWithdraw | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalWithdraw }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: small; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="6" style="height: 50px;">
                  <span style="font-size: smaller;">*{{ $t('withdrawAffiliate.FeeOnly') }}/{{ $t('withdrawAffiliate.Times') }}</span><br>
                  <span style="font-size: smaller;">*{{ $t('withdrawAffiliate.MinWithdrawNote') }}</span>
                </v-col>
                <v-col cols="6" style="height: 50px;">
                  <v-btn width="100%" style="height: 40px; font-size: small;" color="error" :disabled="disabledWithdraw" @click="openWithdraw = true" rounded>{{ $t('withdrawAffiliate.Withdraw') }}</v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      </v-col>
    </v-row>

    <v-row v-if="MobileSize" class="mx-3" style="margin: -20px 0 20px 0;">
      <v-col cols="12">
      <v-row>
      <v-col cols="12">
         <v-row>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Views') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ viewClick | formatNumber }}
                            </span>
                          </template>
                          <span>{{ viewClick }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Times') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Orders') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalOrderSuccess | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalOrderSuccess }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Items') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.TotalSales') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalSale | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalSale }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 100px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column; margin-bottom: -10px;">
                      <span style="color: #27AB9C; font-weight: 600; margin-top: -12px;">{{ $t('withdrawAffiliate.Commission') }}</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: large; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px;"
                            >
                              {{ totalEstCommision | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalEstCommision }}</span>
                        </v-tooltip>
                        <br>
                        <span style="font-size: smaller; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
      </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-row>
            <v-col>
              <v-card  class="px-3 py-2 d-flex justify-center align-center" style="border: 2px solid gray; height: 154px; border-radius: 15px;">
                <v-row>
                  <v-col cols="12" style="display: flex; flex-direction: column;">
                      <span style="color: #27AB9C; font-size: x-large; font-weight: 600; margin-bottom: 20px;">Affiliate</span>
                      <div style="text-align: right;">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span
                              v-bind="attrs"
                              v-on="on"
                              style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333; margin-top: 10px; margin-bottom: 5px; display: block;"
                            >
                              {{ totalWithdraw | formatNumber }}
                            </span>
                          </template>
                          <span>{{ totalWithdraw }}</span>
                        </v-tooltip>
                        <span style="font-size: small; margin-top: 12px; color: #acacac;">{{ $t('withdrawAffiliate.Baht') }}</span>
                      </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="6" style="height: 50px;">
                  <span style="font-size: smaller;">*{{ $t('withdrawAffiliate.FeeOnly') }}/{{ $t('withdrawAffiliate.Times') }}</span><br>
                  <span style="font-size: smaller;">*{{ $t('withdrawAffiliate.MinWithdrawNote') }}</span>
                </v-col>
                <v-col cols="6" style="height: 50px;">
                  <v-btn width="100%" style="height: 40px; font-size: small;" color="error" :disabled="disabledWithdraw" @click="openWithdraw = true" rounded>{{ $t('withdrawAffiliate.Withdraw') }}</v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      </v-col>
    </v-row>
    <v-row v-if="disabledWithdraw === true">
      <v-col cols="12">
        <v-card-title v-if="!MobileSize && !IpadSize" style="font-size: large; font-weight: 600; display: flex; justify-content: center; text-align: center; line-height: 1; color: #ff6060;">{{ $t('withdrawAffiliate.EKYCWarning') }}</v-card-title>
        <v-card-title v-if="MobileSize || IpadSize" style="font-size: small; font-weight: 600; display: flex; justify-content: center; text-align: center; line-height: 1.5; margin-top: -20px; color: #ff6060;">{{ $t('withdrawAffiliate.EKYCWarning1') }}<br>{{ $t('withdrawAffiliate.EKYCWarning2') }}</v-card-title>
      </v-col>
    </v-row>
    <!-- ส่วน สรุปยอดและการถอนเงิน -->

    <!-- ส่วน กราฟ -->
    <!-- <div id="chart" class="mx-3 my-4">
      <apexchart type="line" height="350" :options="chartOptions" :series="series"></apexchart>
    </div> -->
    <!-- ส่วน กราฟ -->

    <!-- ตาราง แสดงรายได้ affiliate -->
    <v-row dense>
      <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-2 pr-1 mt-4 pt-3' : 'pl-2 pr-2 pt-1'">
        <v-text-field v-model="searchData" :placeholder="$t('withdrawAffiliate.SearchPlaceholder')" outlined rounded dense hide-details>
        <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row class="pl-2 pr-2">
      <v-col cols="12">
        <v-data-table
          :headers="headers"
          :search="searchData"
          :items="itemTransactionUserAffiliate"
          class="elevation-1 mt-3"
          height="100%"
          :no-results-text="$t('withdrawAffiliate.NoData')"
          :no-data-text="$t('withdrawAffiliate.NoData')"
          style="width: 100%;"
          :items-per-page="itemsPerPage"
        >
        <!-- <template v-slot:[`item.order_number`]="{ item }">
          <td style="white-space: nowrap; text-align: left;">{{ item.order_number }}</td>
        </template> -->
        <template v-slot:[`item.order_affiliate`]="{ item }">
          <td v-if="item.order_affiliate || item.order_affiliate !== null" style="white-space: nowrap; text-align: center;">{{ item.order_affiliate }}</td>
          <td v-else style="text-align: center;">-</td>
        </template>
        <template v-slot:[`item.transfer_affiliate`]="{ item }">
          <v-chip :text-color="item.transfer_affiliate === 'Success' ? '#1AB759' : '#FAD02C' " :color="item.transfer_affiliate === 'Success' ? '#F0F9EE' : '#fff9de' ">{{ item.transfer_affiliate }}</v-chip>
        </template>
        </v-data-table>
      </v-col>
    </v-row>
    <!-- ตาราง แสดงรายได้ affiliate -->

    <!-- ส่วน Top -->
    <v-row v-if="!MobileSize && !IpadSize" class="mx-3 my-4">
    <v-col cols="6" v-if="topProduct.length !== 0" style="height: 50px;">
      <v-card-title style="font-size: large; font-weight: 700; line-height: 18px; color: #27AB9C;">{{ $t('withdrawAffiliate.TopProduct') }}</v-card-title>
    </v-col>
    <v-col cols="6" v-if="topEarning.length !== 0" style="height: 50px;">
      <v-card-title style="font-size: large; font-weight: 700; line-height: 18px; color: #27AB9C;">{{ $t('withdrawAffiliate.TopEarning') }}</v-card-title>
    </v-col>
    <!-- ส่วน Top Product -->
    <v-col cols="6" v-if="topProduct.length !== 0" style="height: 120px;" class="mb-2">
        <v-card class="px-3 py-2" style="border: 2px solid gray; height: 120px; border-radius: 15px; overflow: hidden;">
          <v-row>
            <v-col cols="12" style="height: 120px;">
              <v-row v-for="(item, index) in topProduct" :key="index" class="mx-1 my-2" style="display: flex; align-items: center;">
                <!-- รูปภาพ -->
                <v-col cols="3" class="d-flex justify-center align-center">
                  <v-avatar tile size="60"><img :src="item.media_path" alt="No Image"></v-avatar>
                </v-col>
                <!-- ชื่อสินค้า -->
                <v-col cols="5" class="text-truncate">
                  <v-tooltip top v-if="IpadProSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 14px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                </v-col>
                <!-- จำนวน -->
                <v-col cols="4" class="d-flex justify-end align-center">
                  <v-tooltip top v-if="IpadProSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.total_quantity | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.total_quantity }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 20px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.total_quantity | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.total_quantity }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- ส่วน Top Earning -->
      <v-col cols="6" v-if="topEarning.length !== 0" style="height: 120px;" class="mb-2">
        <v-card class="px-3 py-2" style="border: 2px solid gray; height: 120px; border-radius: 15px; overflow: hidden;">
          <v-row>
            <v-col cols="12" style="height: 120px;">
              <v-row v-for="(item, index) in topEarning" :key="index" class="mx-1 my-2" style="display: flex; align-items: center;">
                <!-- รูปภาพ -->
                <v-col cols="3" class="d-flex justify-center align-center">
                  <v-avatar tile size="60"><img :src="item.media_path" alt="No Image"></v-avatar>
                </v-col>
                <!-- ชื่อสินค้า -->
                <v-col cols="5" class="text-truncate">
                  <v-tooltip top v-if="IpadProSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 14px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                </v-col>
                <!-- ค่าคอมมิชชั่น -->
                <v-col cols="4" class="d-flex justify-end align-center">
                  <v-tooltip top v-if="IpadProSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.estimate_commission | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.estimate_commission }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 20px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.estimate_commission | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.estimate_commission }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>

    <v-row v-if="MobileSize || IpadSize" class="mx-3 my-4">
    <!-- ส่วน Top Product -->
    <v-col cols="12" v-if="topProduct.length !== 0" style="height: 50px;">
      <v-card-title style="font-size: large; font-weight: 700; line-height: 18px; color: #27AB9C;">{{ $t('withdrawAffiliate.TopProduct') }}</v-card-title>
    </v-col>
    <v-col cols="12" v-if="topProduct.length !== 0">
        <v-card class="px-3 py-2" style="border: 2px solid gray; border-radius: 15px; overflow: hidden;">
          <v-row>
            <v-col cols="12" style="height: 120px;">
              <v-row v-for="(item, index) in topProduct" :key="index" class="mx-1 my-2" style="display: flex; align-items: center;">
                <!-- รูปภาพ -->
                <v-col cols="3" class="d-flex justify-center align-center">
                  <v-avatar tile size="50"><img :src="item.media_path" alt="No Image"></v-avatar>
                </v-col>
                <!-- ชื่อสินค้า -->
                <v-col cols="5" class="text-truncate">
                  <v-tooltip top v-if="MobileSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 14px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                </v-col>
                <!-- จำนวน -->
                <v-col cols="4" class="d-flex justify-end align-center">
                  <v-tooltip top v-if="MobileSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.total_quantity | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.total_quantity }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 20px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.total_quantity | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.total_quantity }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- ส่วน Top Earning -->
      <v-col cols="12" v-if="topEarning.length !== 0" style="height: 50px;">
        <v-card-title style="font-size: large; font-weight: 700; line-height: 18px; color: #27AB9C;">{{ $t('withdrawAffiliate.TopEarning') }}</v-card-title>
      </v-col>
      <v-col cols="12" v-if="topEarning.length !== 0" class="mb-2">
        <v-card class="px-3 py-2" style="border: 2px solid gray; border-radius: 15px; overflow: hidden;">
          <v-row>
            <v-col cols="12" style="height: 120px;">
              <v-row v-for="(item, index) in topEarning" :key="index" class="mx-1 my-2" style="display: flex; align-items: center;">
                <!-- รูปภาพ -->
                <v-col cols="3" class="d-flex justify-center align-center">
                  <v-avatar tile size="50"><img :src="item.media_path" alt="No Image"></v-avatar>
                </v-col>
                <!-- ชื่อสินค้า -->
                <v-col cols="5" class="text-truncate">
                  <v-tooltip top v-if="MobileSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 14px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 100%;"
                      >
                        {{ item.name }}
                      </span>
                    </template>
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                </v-col>
                <!-- ค่าคอมมิชชั่น -->
                <v-col cols="4" class="d-flex justify-end align-center">
                  <v-tooltip top v-if="MobileSize">
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 16px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.estimate_commission | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.estimate_commission }}</span>
                  </v-tooltip>
                  <v-tooltip top v-else>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="font-size: 20px; font-weight: 700; text-align: right; max-width: 100%;"
                      >
                        {{ item.estimate_commission | formatNumber }}
                      </span>
                    </template>
                    <span>{{ item.estimate_commission }}</span>
                  </v-tooltip>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>

    <!-- ส่วน Dialog -->
    <v-dialog v-model='openWithdraw' :width="MobileSize ? '60%' : '40%'" persistent @keydown.esc="openWithdraw = false">
      <v-card min-height='100%'>
        <div style="display: flex; justify-content: end;">
          <v-btn plain fab small @click='openWithdraw = false' icon><v-icon color='#BABABA'>mdi-close</v-icon></v-btn>
        </div>
        <div style="display: flex; justify-content: center; align-items: center;">
          <v-img style="max-width: 60%; height: auto;" :src="require('@/assets/ImageINET-Marketplace/ICONShop/withdraw.png')"></v-img>
        </div>
        <v-container>
          <v-card-text v-if="!MobileSize" style="text-align: center; font-size: large;">
              {{ $t('withdrawAffiliate.ConfirmTitle') }}
          </v-card-text>
          <v-card-text v-else style="text-align: center;">
              {{ $t('withdrawAffiliate.ConfirmTitle') }}
          </v-card-text>
          <v-card-actions>
            <v-row dense>
              <v-col cols="12" style="display: flex; justify-content: center; gap: 10%;">
                <v-btn v-if="!MobileSize" class="white--text ml-2" rounded color="error" @click="openWithdraw = false">{{ $t('withdrawAffiliate.Cancel') }}</v-btn>
                <v-btn v-else style="font-size: small;" class="white--text ml-2" rounded color="error" @click="openWithdraw = false">{{ $t('withdrawAffiliate.Cancel') }}</v-btn>
                <v-btn v-if="!MobileSize" class="white--text ml-2" rounded color="#27AB9C" @click="getDataTransferAffiliateClick()">{{ $t('withdrawAffiliate.Confirm') }}</v-btn>
                <v-btn v-else style="font-size: small;" class="white--text ml-2" rounded color="#27AB9C" @click="getDataTransferAffiliateClick()">{{ $t('withdrawAffiliate.Confirm') }}</v-btn>
              </v-col>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model='openDialog' :width="MobileSize ? '60%' : '40%'" persistent @keydown.esc="openDialog = false">
      <v-card min-height='100%'>
        <v-img height="100%" :src="dialogImage">
        <v-app-bar flat color="rgba(0, 0, 0, 0)">
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn plain fab small @click='openDialog = false' icon><v-icon color='#BABABA'>mdi-close</v-icon></v-btn>
        </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text v-if="!MobileSize" style="text-align: center; font-size: large;">
            {{ dialogMessage }}
          </v-card-text>
          <v-card-text v-else style="text-align: center;">
            {{ dialogMessage }}
          </v-card-text>
          <v-card-actions>
            <v-row dense class='d-flex justify-center' style="padding: 0 25%;">
              <v-btn v-if="!MobileSize" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openDialog = false">{{ $t('withdrawAffiliate.Confirm') }}</v-btn>
              <v-btn v-else style="font-size: small;" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openDialog = false">{{ $t('withdrawAffiliate.Confirm') }}</v-btn>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- ส่วน Dialog -->

  </v-card>
</v-container>
</template>

<script>
import { Decode } from '@/services'
// import VueApexCharts from 'vue-apexcharts'
import Vue from 'vue'

export default {
  components: {
    // apexchart: VueApexCharts
  },
  data () {
    return {
      menu: '',
      date: '',
      viewClick: '',
      totalOrderSuccess: '',
      totalEstCommision: '',
      totalSale: '',
      totalWithdraw: '',
      topProduct: [],
      topEarning: [],
      itemTransactionUserAffiliate: [],
      searchData: '',
      access_token: '',
      dataUser: '',
      disabledWithdraw: true,
      itemsPerPage: 10,
      headers: [
        { text: this.$t('withdrawAffiliate.OrderNumber'), value: 'order_number', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: this.$t('withdrawAffiliate.Shop'), value: 'name_th', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: this.$t('withdrawAffiliate.OrderAffiliate'), value: 'order_affiliate', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: this.$t('withdrawAffiliate.TotalAffiliate'), value: 'total_affiliate', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: this.$t('withdrawAffiliate.TimeTransfer'), value: 'time_transfer', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: this.$t('withdrawAffiliate.TransferAffiliate'), value: 'transfer_affiliate', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' }
      ],
      openWithdraw: false,
      openDialog: false,
      dialogMessage: '',
      dialogImage: '',
      selected: 1,
      selectFilteritem: [
        { id: 1, name: 'ทั้งหมด' },
        { id: 2, name: 'รายวัน' },
        { id: 3, name: 'รายเดือน' },
        { id: 4, name: 'รายปี' }
      ],
      series: [{
        name: 'Desktops',
        data: [10, 41, 35, 51, 49, 62, 69, 91, 148]
      }],
      chartOptions: {
        theme: {
          mode: 'light',
          monochrome: {
            enabled: true,
            color: '#38b2a4'

          }
        },
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          },
          toolbar: {
            show: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'straight'
        },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'],
            opacity: 0.5
          }
        },
        xaxis: {
          categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep']
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/withdrawAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/withdrawAffiliate' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.checkConsent()
    this.$EventBus.$emit('changeNavAccount')
    this.checkeKYC()
    this.getDataTransactionUserAffiliate()
    this.getDataDashboardAffiliate()
    this.formatNumber()
    if (this.MobileSize) {
      this.itemsPerPage = 5
    }
  },
  methods: {
    backToUsr () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async getDataTransactionUserAffiliate () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const payload = {
        user_id: onedata.user.user_id
        // user_id: 119
      }
      await this.$store.dispatch('actionsTransactionUserAffiliate', payload)
      var response = await this.$store.state.ModuleWithdrawAffiliate.stateTransactionUserAffiliate
      if (response.code === 200) {
        // console.log('TransactionUserAffiliate', response.data)
        this.itemTransactionUserAffiliate = response.data
      } else if (response.code === 500) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('withdrawAffiliate.SystemError') + '</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async getDataDashboardAffiliate () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const payload = {
        user_id: onedata.user.user_id
        // user_id: 119
      }
      await this.$store.dispatch('actionsDashboardAffiliate', payload)
      var response = await this.$store.state.ModuleWithdrawAffiliate.stateDashboardAffiliate
      if (response.code === 200) {
        // console.log('DashboardAffiliate', response.data)
        this.viewClick = response.data.view_click
        this.totalOrderSuccess = response.data.total_order_success
        this.totalEstCommision = response.data.total_est_commision
        this.totalSale = response.data.total_sale
        this.totalWithdraw = response.data.total_withdraw
        this.topProduct = response.data.top_product
        // console.log('topProduct', this.topProduct)
        this.topEarning = response.data.top_earning
      } else if (response.code === 500) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('withdrawAffiliate.SystemError') + '</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async getDataTransferAffiliateClick () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const payload = {
        user_id: onedata.user.user_id
        // user_id: 119
      }
      await this.$store.dispatch('actionTransferAffiliateClick', payload)
      var response = await this.$store.state.ModuleWithdrawAffiliate.stateTransferAffiliateClick
      // console.log('getDataTransferAffiliateClick', response.data)
      if (response.code === 200) {
        this.dialogMessage = response.message
        this.dialogImage = require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')
        this.openWithdraw = false
        this.openDialog = true
        this.getDataTransactionUserAffiliate()
        this.getDataDashboardAffiliate()
      } else if (response.code === 400) {
        this.dialogMessage = response.message
        this.dialogImage = require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')
        this.openWithdraw = false
        this.openDialog = true
      } else if (response.code === 500) {
        this.openWithdraw = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('withdrawAffiliate.SystemError') + '</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async checkeKYC () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsCheckeKYC')
      var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      // console.log(response)
      if (response.result === 'SUCCESS') {
        // console.log('response.data.eKYC_approve', response.data.eKYC_approve)
        if (response.data.eKYC_approve === 'yes') {
          // console.log('response.data.eKYC_approve === yes', response.data.eKYC_approve === 'yes')
          this.disabledWithdraw = false
        } else if (response.data.eKYC_approve === 'no') {
          // console.log('response.data.eKYC_approve === no', response.data.eKYC_approve === 'no')
          this.disabledWithdraw = true
        }
      }
      this.$store.commit('closeLoader')
    },
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // this.access_token = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      const data = {
        user_id: oneData.user.user_id
        // user_id: 218
      }
      // this.dataUser = data
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END}api/affiliate/excel_affiliate_user`,
          // headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          headers: { Authorization: `Bearer ${this.access_token}` },
          method: 'POST',
          responseType: 'blob',
          data: data
          // data: this.dataUser
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'WithdrawAffiliate.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      } catch (error) {
        if (error.response && error.response.status === 400) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>' + this.$t('withdrawAffiliate.NoWithdrawData') + '</h3>'
          })
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>' + this.$t('withdrawAffiliate.SystemError') + '</h3>'
          })
        }
      }
    },
    handleSelectChange (value) {
    },
    formatNumber () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        // if (value >= 1000000) {
        //   return (Math.floor(value / 100000) / 10) + ' ล้าน'
        // } else if (value >= 100000) {
        //   return (Math.floor(value / 10000) / 10) + ' แสน'
        // } else if (value >= 10000) {
        //   return (Math.floor(value / 1000) / 10) + ' หมื่น'
        // } else if (value >= 1000) {
        //   return (Math.floor(value / 100) / 10) + ' พัน'
        // }
        if (value >= 1000000) {
          return (Math.floor(value / 100000) / 10) + 'M'
        } else if (value >= 1000) {
          return (Math.floor(value / 100) / 10) + 'K'
        }
        return value.toString()
      })
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep table {
tbody {
  td {
    white-space: nowrap;
  }
  tr {
    td:nth-child(6) {
    position: sticky !important;
    position: -webkit-sticky !important;
    right: 0;
    z-index: 21;
    background: white;
    }
  }
}
thead {
  tr {
    th {
      white-space: nowrap;
    }
    th:nth-child(1) {
    position: sticky !important;
    position: -webkit-sticky !important;
    right: 0;
    z-index: 10;
    background: white;
    }
  }
}
thead {
  tr {
    th:nth-child(6) {
    text-align: center;
    z-index: 16;
    background: white;
    position: sticky !important;
    position: -webkit-sticky !important;
    right: 0;
    }
  }
}
}
</style>

<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
