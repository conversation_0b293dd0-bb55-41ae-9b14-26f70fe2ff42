<template>
  <v-container grid-list-xs>
    <v-row class="mt-5">
      <v-col cols="12" md="12">
        <!-- รวมเงิน -->
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row>
        <!-- ส่วนลด -->
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ส่วนลด :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.total_price_discount).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row>
        <!-- ส่วนลดทั้งสิ้น -->
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ภาษีมูลค่าเพิ่ม 7% :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row>
        <!-- ลดแล้วเหลือ  -->
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ราคารวมมูลค่าเพิ่ม :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row>
        <!-- price no vat -->
        <!-- <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ราคารวมมูลค่าภาษี 7% :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row> -->
        <!-- price vat -->
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ค่าจัดส่ง :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row>
        <!-- ราคาสุทธิ -->
        <!-- price vat -->
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 16px; font-weight: bold">ราคาสุทธิ :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 16px; font-weight: 300">
              {{Number(OrderDetailProp.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}}
            </span>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
export default {
  props: ['OrderDetailProp'],
  created () {
    this.total_price_after_discount = this.OrderDetailProp.total_price_vat - this.OrderDetailProp.total_price_discount
    // console.log('total_price_after_discount', this.total_price_after_discount)
  },
  data: () => ({
    total_price_no_vat: '',
    total_vat: '',
    total_price_vat: '',
    // total_discount: '',
    total_shipping: '',
    net_price: ''
  }),
  watch: {
    // OrderDetailProp (val) {
    //   console.log('summary', this.OrderDetailProp)
    //   if (Object.keys(val).length !== 0) {
    //     this.total_price_vat = val.total_price_vat
    //     this.total_discount = val.total_discount
    // this.total_price_after_discount = val.total_price_vat - val.total_price_discount
    //     this.total_vat = val.total_vat
    //     this.total_shipping = val.total_shipping
    //     this.net_price = val.net_price
    //   }
    // }
  }
}
</script>
<style scoped>
/* @import url("https://fonts.googleapis.com/css?family=Sarabun&display=swap"); */
.footerimg {
  font-family: "Sarabun" !important;
  text-align: right;
  color: rgb(3, 39, 0);
  background-color: white;
  width: 70%;
}
.approvedtr {
  font-family: "Sarabun" !important;
  text-align: right;
  font-size: 13px !important;
  line-height: 12px;
}
</style>
