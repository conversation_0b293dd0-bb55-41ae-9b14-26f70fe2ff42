<template>
  <v-container>
    <v-row dense no-gutters justify="end">
      <v-col cols="12" md="4" sm="4" xs="12">
        <v-text-field
         v-model="search"
         rounded
         dense
         outlined
         placeholder="ค้นหาบริษัท"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row v-if="props.length !== 0">
      <v-col cols="12" class="pl-4 pt-6">
        <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">แสดงรายการจำนวนสินค้า {{ itemsPerPage &lt; 0 ? props.length : itemsPerPage }} รายการ</span>
      </v-col>
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card outlined class="mb-4">
          <v-data-table  v-if="type === 'userList'"
           :headers="headersListUser"
           :items="props"
           :items-per-page="5"
           :search="search"
           no-results-text="ไม่พบบริษัทที่ค้นหา"
           no-data-text="ไม่มีบริษัทในตาราง"
           :update:items-per-page="getItemPerPage"
          >
          <!-- hide-default-header -->
          <!-- <template slot="header" :headers="headers">
            <tr class="backgroundTableCustom">
              <th rowspan="2" width='80'>{{ headers[0].text }}</th>
              <th rowspan="2" width='200'>{{ headers[1].text }}</th>
              <th rowspan="2" width='120'>{{ headers[2].text }}</th>
              <th rowspan="2" width='140'>{{ headers[3].text }}</th>
              <th colspan="4" width='440' style="border-bottom: 1px solid;">สิทธิ์การเข้าใช้งาน</th>
              <th rowspan="2" width='140'>{{ headers[8].text }}</th>
              <th rowspan="2" width='120'>{{ headers[9].text }}</th>
              <th rowspan="2" width='120'>{{ headers[10].text }}</th>
            </tr>
            <tr class="backgroundTableCustom">
              <th width='110'>{{ headers[4].text }}</th>
              <th width='140'>{{ headers[5].text }}</th>
              <th width='100'>{{ headers[6].text }}</th>
              <th width='100'>{{ headers[7].text }}</th>
            </tr>
          </template> -->
            <template v-slot:[`item.detail`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  outlined
                  color="#27AB9C"
                  style="border: 1px solid #27AB9C; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  @click="gotoUserDetail(item)"
                  :to="pathEditUser"
                  class="pt-4 pb-4"
                >
                  รายละเอียด
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.userSetting`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-icon v-if="item.userSetting === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.permission.admin`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-icon v-if="item.permission.admin === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.permission.adminAssistant`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-icon v-if="item.permission.adminAssistant === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.permission.approver`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-icon v-if="item.permission.approver === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.permission.buyer`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-icon v-if="item.permission.buyer === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.verifyEmail`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-icon v-if="item.verifyEmail === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-switch :ripple="false" class="hide-background-hover" inset v-model="item.status" color="#27ab9c" @change="actions(item.userID, item.status)">
                  </v-switch>
                </v-col>
              </v-row>
            </template>
          </v-data-table>
          <v-data-table v-else
           :headers="headerUserRequest"
           :items="props"
           :items-per-page="5"
           :search="search"
           no-results-text="ไม่พบบริษัทที่ค้นหา"
           no-data-text="ไม่มีบริษัทในตาราง"
           :update:items-per-page="getItemPerPage"
          >
            <template v-slot:[`item.status`]="{ item }">
              <v-row dense justify="center">
                <v-col>
                  <v-switch :ripple="false" class="hide-background-hover" inset v-model="item.status" color="#27ab9c" @change="actions(item.userID, item.status)">
                  </v-switch>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.detail`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  outlined
                  color="#27AB9C"
                  style="border: 1px solid #27AB9C; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  @click="gotoRequestUserDetail(item)"
                  :to="pathEditUser"
                  class="pt-4 pb-4"
                >
                  รายละเอียด
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' }) }}
            </template>
          </v-data-table>
        </v-card>
        <!-- <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div> -->
      </v-col>
    </v-row>
    <v-row justify="center" align-content="center" v-else>
      <v-col cols="12" md="12" align="center">
        <div class="my-5">
          <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
        </div>
        <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีบริษัท</span><br/>
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span style="font-size: 28px;">“เพิ่มบริษัท”</span> เพื่อเพิ่มบริษัทที่สมบูรณ์ของคุณ</span>
        </h2>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: ['props', 'type'],
  data () {
    return {
      status: false,
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      search: '',
      pathEditUser: '',
      header: '',
      headerUserRequest: [
        { text: 'ชื่อ-นามสกุล', value: 'name_th', sortable: false, align: 'center', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'เลข oneid', value: 'oneid', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สร้างคำขอ', value: 'created_at', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'detail', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersListUser: [
        { text: 'ลำดับที่', value: 'id', sortable: false, align: 'center', width: '80', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ', value: 'name_th', sortable: false, align: 'center', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'การตั้งค่าผู้ซื้อ', value: 'userSetting', sortable: false, align: 'center', width: '110', class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์ผู้ดูแลระบบ', value: 'permission.admin', sortable: false, align: 'center', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์ผู้ช่วยผู้ดูแลระบบ', value: 'permission.adminAssistant', sortable: false, align: 'center', width: '150', class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์ผู้อนุมัติ', value: 'permission.approver', sortable: false, align: 'center', width: '100', class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์ผู้สั่งซื้อ', value: 'permission.buyer', sortable: false, align: 'center', width: '100', class: 'backgroundTable fontTable--text' },
        { text: 'การยืนยันอีเมล', value: 'verifyEmail', sortable: false, align: 'center', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'ข้อมูล', value: 'detail', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'ผู้ใช้งาน')
    this.$EventBus.$emit('changeNavAdminManage')
  },
  methods: {
    gotoUserDetail (val) {
      this.$router.push({ path: '/detailUserCompany' }).catch(() => {})
    },
    gotoRequestUserDetail (val) {
      this.$router.push({ path: '/detailUserCompany' }).catch(() => {})
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
    },
    actions (userId, item) {
      if (item === false) {
        this.$swal.fire({
          icon: 'warning',
          title: 'ยกเลิกสิทธิ์การเข้าใช้',
          html: '<h3>ผู้ใช้นี้จะไม่สามารถเข้าใช้เว็บไซต์ได้</h3>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.props.forEach(element => {
              if (element.userID === userId) {
                element.status = false
              }
            })
          } else if (result.dismiss === this.$swal.DismissReason.cancel) {
            this.props.forEach(element => {
              if (element.userID === userId) {
                element.status = true
              }
            })
          }
        }).catch(() => {
        })
      } else {
        this.$swal.fire({
          icon: 'warning',
          title: 'อนุญาตสิทธิ์การเข้าใช้',
          html: '<h3>ผู้ใช้นี้สามารถเข้าใช้เว็บไซต์นี้ได้</h3>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.props.forEach(element => {
              if (element.userID === userId) {
                element.status = true
              }
            })
          } else if (result.dismiss === this.$swal.DismissReason.cancel) {
            this.props.forEach(element => {
              if (element.userID === userId) {
                element.status = false
              }
            })
          }
        }).catch(() => {
        })
      }
    }
  }
}
</script>

<style>
.backgroundTableCustom {
  color: #27ab9c !important;
  font-size: 12px;
  text-align: center;
  background-color: #e6f5f3 !important;
  border-color: #e6f5f3 !important;
}
</style>
