<template>
  <div class="text-center">
    <v-dialog v-model="EditaddressDialog" width="800" persistent>
      <v-card>
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>แก้ไขที่อยู่</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="EditaddressDialog = !EditaddressDialog" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text>
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="3">
                  <span>ชื่อ</span>
                </v-col>
                <v-col cols="3">
                  <span>นามสกุล</span>
                </v-col>
                <v-col cols="6">
                  <span>เบอร์โทรศัพท์</span>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="first_name" :rules="Rules.first_name"></v-text-field>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุนามสกุล" outlined dense v-model="last_name" :rules="Rules.last_name"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.tel"></v-text-field>
                </v-col>
                <v-col cols="3">
                  <span>เลขที่</span>
                </v-col>
                <v-col cols="3">
                  <span>ห้องเลขที่</span>
                </v-col>
                <v-col cols="6">
                  <span>ชั้นที่</span>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุเลขที่อยู่" outlined dense v-model="house_no" :rules="Rules.house_no"></v-text-field>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุเลขห้อง" outlined dense v-model="room_no"></v-text-field>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุชั้น" outlined dense v-model="floor"></v-text-field>
                </v-col>
                <v-col cols="3"></v-col>
                <v-col cols="6">
                  <span>อาคาร</span>
                </v-col>
                <v-col cols="6">
                  <span>หมู่บ้าน</span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <v-text-field class="input_text" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" outlined dense v-model="building_name"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field class="input_text" placeholder="ชื่อหมู่บ้าน" outlined dense v-model="moo_ban"></v-text-field>
                </v-col>
                <v-col cols="3">
                  <span>หมู่ที่</span>
                </v-col>
                <v-col cols="3">
                  <span>ตรอก/ซอย</span>
                </v-col>
                <v-col cols="3">
                  <span>แยก</span>
                </v-col>
                <v-col cols="3">
                  <span>ถนน</span>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุหมู่" outlined dense v-model="moo_no"></v-text-field>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุตรอก,ซอย" outlined dense v-model="soi"></v-text-field>
                </v-col>
                <v-col cols="3" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุแยก" outlined dense v-model="yaek"></v-text-field>
                </v-col>
                <v-col cols="3">
                  <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <span>แขวง/ตำบล</span>
                </v-col>
                <v-col cols="6">
                  <span>เขต/อำเภอ</span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-subdistrict :rules="Rules.empty" label=""  v-model="subdistrict" placeholder="ระบุแขวง"/>
                </v-col>
                <v-col cols="6">
                  <addressinput-district label="" v-model="district"  placeholder="ระบุเขต" />
                </v-col>
                <v-col cols="6">
                  <span>จังหวัด</span>
                </v-col>
                <v-col cols="6">
                  <span>รหัสไปรษณีย์</span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-province label="" v-model="province" placeholder="ระบุจังหวัด" />
                </v-col>
                <v-col cols="6">
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="CreateAddress()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import { Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  props: ['EditAddressDetail'],
  data () {
    return {
      dataEditAddress: [],
      data: [],
      lazy: false,
      first_name: '',
      last_name: '',
      phone: '',
      detail: '',
      EditaddressDialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      house_no: '',
      room_no: '',
      floor: '',
      building_name: '',
      moo_ban: '',
      moo_no: '',
      soi: '',
      yaek: '',
      street: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        first_name: [
          v => !!v || 'กรุณากรอกชื่อจริงผู้รับ'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่'
        ],
        room_no: [
          v => !!v || 'กรุณาระบุห้องเลขที่'
        ],
        floor: [
          v => !!v || 'กรุณาระบุชั้นที่'
        ],
        building_name: [
          v => !!v || 'กรุณาระบุอาคาร'
        ],
        moo_ban: [
          v => !!v || 'กรุณาระบุหมู่บ้าน'
        ],
        moo_no: [
          v => !!v || 'กรุณาระบุหมู่ที่'
        ],
        soi: [
          v => !!v || 'กรุณาระบุตรอก/ซอย'
        ],
        yaek: [
          v => !!v || 'กรุณาระบุแยก'
        ],
        street: [
          v => !!v || 'กรุณาระบุถนน'
        ]
      }
    }
  },
  watch: {
    EditAddressDetail (val) {
      // console.log('val =====>', val)
      this.dataEditAddress = val
    }
  },
  created () {
    this.$EventBus.$on('EditModalAddress', this.EditModalAddress)
    this.getAddressData()
  },
  mounted () {
    this.getAddressData()
  },
  methods: {
    EditModalAddress () {
      this.EditaddressDialog = !this.EditaddressDialog
    },
    cancel () {
      this.EditaddressDialog = !this.EditaddressDialog
    },
    async getAddressData () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      if (localStorage.getItem('AddressUserDetail') !== null) {
        this.data = JSON.parse(Decode.decode(localStorage.getItem('AddressUserDetail')))
        var addressData = this.data.address_data[0]
        // console.log('AddressUserDetail', this.data)
        this.first_name = this.data.first_name_th
        this.last_name = this.data.last_name_th
        this.house_no = addressData.house_no === '' ? '-' : addressData.house_no
        this.moo_ban = addressData.moo_ban === '' ? '-' : addressData.moo_ban
        this.building_name = addressData.building_name === '' ? '-' : addressData.building_name
        this.street = addressData.street === '' ? '-' : addressData.street
        this.soi = addressData.soi === '' ? '-' : addressData.soi
        this.room_no = addressData.room_no === '' ? '-' : addressData.room_no
        this.floor = addressData.floor === '' ? '-' : addressData.floor
        this.moo_no = addressData.moo_no === '' ? '-' : addressData.moo_no
        this.yaek = addressData.yaek === '' ? '-' : addressData.yaek
        this.subdistrict = addressData.sub_district
        this.district = addressData.district
        this.province = addressData.province
        this.phone = this.data.phone
        this.zipcode = addressData.zip_code
      }
    }
    // async getAddressData () {
    //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   var data = {
    //     role_user: dataRole.role
    //   }
    //   await this.$store.dispatch('GetUserAddress', data)
    //   if (localStorage.getItem('AddressProfileData') !== null) {
    //     this.data = JSON.parse(Decode.decode(localStorage.getItem('AddressData')))
    //     console.log('AddressProfileData', this.data)
    //     this.first_name = this.data[0].first_name
    //     this.last_name = this.data[0].last_name
    //     this.house_no = this.data[0].house_no
    //     this.moo_ban = this.data[0].moo_ban
    //     this.building_name = this.data[0].building_name
    //     this.street = this.data[0].street
    //     this.soi = this.data[0].soi
    //     this.room_no = this.data[0].room_no
    //     this.floor = this.data[0].floor
    //     this.moo_no = this.data[0].moo_no
    //     this.yaek = this.data[0].yaek
    //     this.subdistrict = this.data[0].sub_district
    //     this.district = this.data[0].district
    //     this.province = this.data[0].province
    //     this.phone = this.data[0].phone
    //     this.zipcode = this.data[0].zipcode
    //   } else {
    //     this.getAddressData()
    //   }
    // }
    // async CreateAddress () {
    //   if (this.$refs.FormAddress.validate(true)) {
    //     var data = {
    //       first_name: this.first_name,
    //       last_name: this.last_name,
    //       house_no: this.house_no,
    //       moo_ban: this.moo_ban,
    //       building_name: this.building_name,
    //       street: this.street,
    //       soi: this.soi,
    //       room_no: this.room_no,
    //       floor: this.floor,
    //       moo_no: this.moo_no,
    //       yaek: this.yaek,
    //       sub_district: this.subdistrict,
    //       district: this.district,
    //       province: this.province,
    //       phone: this.phone,
    //       zipcode: this.zipcode
    //     }
    //     console.log('data ที่จะสร้างที่อยู่ร้าน', data)
    //     await this.$store.dispatch('CreateAddressUser', data)
    //     var res = this.$store.state.ModuleManageShop.CreateAddressUser
    //     var updateAddress = {
    //       address_id: res.data
    //     }
    //     console.log('ข้อมูล หลังจาก create user =', res)
    //     if (res.message === 'Create user address success') {
    //       this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
    //       this.dialog = false
    //       localStorage.removeItem('AddressData')
    //       await this.$store.dispatch('UpdateAddressCart', updateAddress)
    //       await this.$EventBus.$emit('SentGetCart')
    //     } else if (res.message === 'Parameter is missing') {
    //       this.$swal.fire({ icon: 'warning', title: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
    //     }
    //   }
    // }
  }
}
</script>

<style>
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
</style>
