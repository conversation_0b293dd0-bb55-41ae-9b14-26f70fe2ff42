<template>
  <v-container :style="MobileSize ? 'background-color: #ffffff' : ''" :class="MobileSize ? 'pa-3' : 'mb-3'">
    <v-row dense class="pt-2">
      <v-col cols="12" md="6" sm="6" style="align-content: center;">
        <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>
        <span style="font-weight: bold; align-content: center;" :style="MobileSize || IpadSize ? 'font-size: large;' : 'font-size: x-large;'">รายการลงทะเบียนคู่ค้า</span>
      </v-col>
      <v-col :cols="MobileSize ? '12' : '6'" style="display: flex; justify-content: end; align-items: center;">
        <v-btn rounded color="#38b2a4" @click="exportExcel()" :style="MobileSize ? 'width: 100%;' : ''" class="white--text"><v-icon small class="mr-1">mdi-file-document-multiple</v-icon><span style="font-size: small;">รายการลงทะเบียนคู่ค้า</span></v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="6" sm="6">
        <v-text-field
          v-model="search"
          placeholder="ค้นหาข้อมูลในตาราง"
          dense
          outlined
          hide-details
        ></v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <span style="font-size: 18px; font-weight: bold;">ทั้งหมด {{ items.length }} รายการ</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-card>
        <!-- :custom-filter="customSearch" -->
          <v-data-table
            :headers="header"
            :items="items"
            style="white-space: nowrap;"
            :items-per-page="10"
            :search="search"
            height="100%"
            item-key="id"
            no-data-text="ไม่มีรายการลงทะเบียนคู่ค้า"
            no-results-text="ไม่พบรายการลงทะเบียนคู่ค้า"
            :class="MobileSize ? 'mb-4' : ''"
          >
            <template v-slot:[`item.index`]="{ index }">
              <span>{{index + 1}}</span>
            </template>
            <!-- <template v-slot:[`item.created_at`]="{ item }">
              <span>{{new Date(item.created_at.substring(0, 10)).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}</span>
            </template> -->
            <template v-slot:[`item.type_biz`]="{ item }">
              <span v-if="item.type_biz === 'corporation'">นิติบุคคล</span>
              <span v-else-if="item.type_biz === 'BusinessRegis'">ทะเบียนพาณิชย์</span>
              <span v-else-if="item.type_biz === 'Community'">วิสาหกิจชุมชน</span>
              <span v-else-if="item.type_biz === 'Social'">วิสาหกิจเพื่อสังคม</span>
              <span v-else-if="item.type_biz === 'OTOP'">OTOP</span>
              <span v-else-if="item.type_biz === 'pracharath'">ประชารัฐรักสามัคคี</span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.detail`]="{ item }">
              <v-btn small rounded color="#27ab9c" @click="gotoDetailPage(item.id)">
                <span class="white--text">รายละเอียด</span>
              </v-btn>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
export default {
  data () {
    return {
      search: '',
      header: [
        { text: 'ลำดับ', value: 'index', width: '50', align: 'center', filterable: false, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_on_document_th', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'เลขบัตรประชาชน', value: 'id_card_num', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทธุรกิจ', value: 'type_biz', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่บันทึกข้อมูล', value: 'created_at', width: '50', align: 'center', filterable: true, sortable: true, class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'detail', width: '50', align: 'center', filterable: false, sortable: true, class: 'backgroundTable fontTable--text' }
      ],
      items: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageRegisterInfoPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageRegisterInfoPartner' }).catch(() => {})
      }
    }
  },
  created () {
    this.getListInfoPartner()
  },
  methods: {
    async exportExcel () {
      // const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}business/export/listBussinessPartner`,
        // headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        // console.log(response)
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'register-info-partner.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async getListInfoPartner () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListRegisterInfoPartner')
      var response = await this.$store.state.ModuleAdminManage.stateListRegisterInfoPartner
      // console.log(response, 'response')
      if (response.code === 200) {
        this.items = response.data
      } else {
        this.items = []
        this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
      }
      this.$store.commit('closeLoader')
    },
    gotoDetailPage (val) {
      if (!this.MobileSize) {
        this.$router.push({ path: `/ManageRegisterInfoPartnerDetail?businessId=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageRegisterInfoPartnerDetailMobile?businessId=${val}` }).catch(() => {})
      }
    },
    async backtoMenu () {
      if (!this.MobileSize) {
        window.scrollTo(0, 0)
        this.$router.push({ path: '/adminPanit' }).catch(() => {})
      } else {
        window.scrollTo(0, 0)
        this.$router.push({ path: '/adminPanitMobile' }).catch(() => {})
      }
    }
    // customSearch (value, search, item) {
    //   return (
    //     item.shop_name.toLowerCase().includes(search.toLowerCase()) ||
    //     item.business_type.toLowerCase().includes(search.toLowerCase()) ||
    //     item.phone_number.includes(search)
    //   )
    // }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>

</style>
