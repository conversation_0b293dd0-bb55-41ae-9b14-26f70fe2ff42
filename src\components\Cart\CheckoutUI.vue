<template lang="html">
  <div>
    <!-- <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay> -->
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          <span
            :style="{
              color: item.disabled === true ? '#27AB9C' : '#636363',
              'font-size': '16px',
            }"
            >{{ item.text }}</span
          >
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <!-- <v-divider class="mt-1"></v-divider> -->
    <v-container>
      <v-row class="mt-1">
        <v-col cols="12" md="8">
          <v-row no-gutters>
            <!-- <v-col cols="12" class="mb-5">
              <v-card class="mt-3">
                <v-row no-gutters>
                  <v-col cols="8" md="6" class="pl-5 pt-5 pb-0">
                    <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                  </v-col> -->
            <!-- start แก้ไขที่อยู่ -->
            <!-- <v-col cols="4" md="6" class="pt-5 text-right" v-if="Address !== 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                    <v-btn text rounded color="#333333" :class="MobileSize || IpadSize || IpadProSize ? 'mr-2' : 'mr-5'" small elevation="0" @click="editAddress(address_data)"><v-icon class="pr-1" small color="#A1A1A1">mdi-pencil</v-icon>แก้ไขที่อยู่</v-btn>
                  </v-col> -->
            <!-- end แก้ไขที่อยู่ -->
            <!-- </v-row>
                <v-col cols="12" align="left" class="pt-0 pl-5" v-if="Address !== 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                  <v-radio-group v-model="selectTypeAddress" row>
                    <v-radio color="#27AB9C" label="รับสินค้าหน้าร้าน" value="Shop"></v-radio>
                    <v-radio color="#27AB9C" label="จัดส่งสินค้าปกติ" value="Normal"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12" align="left" class="pt-0 pl-5" v-if="Address === 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                  <v-btn outlined icon small color="#A1A1A1">
                    <v-icon small color="#A1A1A1">mdi-plus</v-icon>
                  </v-btn>
                  <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งสินค้า</b></span>
                </v-col>
                <v-col cols="12" align="left" class="pt-0 pl-5" v-else>
                  {{ Fullname }} {{ Address }}
                </v-col>
              </v-card>
            </v-col> -->
            <!-- Start เพิ่มที่อยู่ในการจัดส่งโดย ผู้ใช้งานภายในร้าน -->
            <!-- <v-col cols="12" class="mb-5" v-if="checkOwnShop === 'Y'">
              <v-card class="mt-3">
                <v-row no-gutters>
                  <v-col cols="8" md="6" class="pl-5 pt-5 pb-0">
                    <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>ที่อยู่ในการจัดส่งสินค้าสำหรับลูกค้า</b></p>
                  </v-col>
                </v-row>
                <v-col cols="12" align="left" class="pt-0 pl-5">
                  <v-btn outlined icon small color="#A1A1A1" @click="openModalAddressCustomer()">
                    <v-icon small color="#A1A1A1">mdi-plus</v-icon>
                  </v-btn>
                  <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งสินค้า</b></span>
                </v-col>
              </v-card>
            </v-col> -->
            <!-- End เพิ่มที่อยู่ในการจัดส่งโดย ผู้ใช้งานภายในร้าน -->
            <!--start เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
            <!-- <v-col cols="12" class="mb-5">
              <v-card style="padding-bottom: 15px">
                <v-col cols="12" class="pl-5 pt-5 pb-0">
                  <p :style="MobileSize ? 'font-size: 24px;' : 'font-size: 24px;'">
                    <b>ที่อยู่ในการจัดส่งใบกำกับภาษี</b>
                  </p>
                </v-col>
                <v-row dense no-gutters class="ml-4 pt-0">
                  <v-col cols="12">
                    <v-radio-group v-model="taxRoles" row style="margin-top: -10px">
                      <v-radio color="#27AB9C" label="ผู้ใช้ทั่วไป" value="Personal" v-if="role.role !== 'purchaser'">
                      </v-radio>
                      <v-radio color="#27AB9C" label="นิติบุคคล" value="Business"></v-radio>
                      <v-radio color="#27AB9C" label="ไม่รับใบกำกับภาษี" value="No"></v-radio>
                    </v-radio-group>
                  </v-col>
                  {{ taxRoles }}
                </v-row>
                <v-col v-if="taxAddress === '' && taxRoles !== 'No'" cols="12" align="left" class="pt-2 pl-5">
                  <v-btn outlined icon small color="#A1A1A1" @click="openModalTaxAddress()">
                    <v-icon color="#A1A1A1" small>mdi-plus</v-icon>
                  </v-btn>
                  <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี</b></span>
                </v-col>
                <v-col v-else-if="taxAddress !== '' && taxRoles !== 'No'" cols="12" align="left" class="pt-0 pl-5">
                  {{ companyName }} {{ companyTaxID }} {{ taxAddress }}
                </v-col>
              </v-card>
            </v-col> -->
            <!--end เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
            <!-- Desktop, ipadPro -->
            <v-col cols="12" md="12" v-if="!MobileSize">
              <v-card class="pa-5" style="border-radius: 8px;">
                <v-col cols="12" class="my-2 pt-0">
                  <v-row dense>
                    <p style="font-size: 24px" class="mb-0"><b>รายการสั่งซื้อสินค้า</b></p>
                    <!-- <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 16px; margin-left: 10px;"></v-spacer> -->
                  </v-row>
                </v-col>
                <!-- start ใบเสนอราคา และแก้ไขใบเสนอราคา Desktop -->
                <!-- <v-col v-show="checkAdminQU" cols="12" md="12" sm="12">
                  <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain></v-img>
                  </v-avatar>
                  <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false" class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ขอใบเสนอราคา</v-btn>
                </v-col> -->
                <!-- <v-col v-show="checkAdminQU && oneDataSpecial !== 'yes'" cols="12" md="12" sm="12">
                  <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                    </v-img>
                  </v-avatar>
                  <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                    class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ตัวอย่างใบเสนอราคา</v-btn>
                </v-col>
                <v-col v-if="role.role === 'purchaser' && oneDataSpecial !== 'yes'" cols="12" md="12" sm="12">
                  <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                    </v-img>
                  </v-avatar>
                  <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                    class="pl-0 pr-0 ml-2 hide-background-hover"
                    @click="SelectCouponOrPoint ? openEditQU() : openModaleEditQUYesNo()">
                    ขอแก้ไขใบเสนอราคา
                  </v-btn>
                </v-col>
                <v-col v-if="role.role === 'purchaser'" cols="12" md="12" sm="12">
                  <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain>
                    </v-img>
                  </v-avatar>
                  <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false"
                    class="pl-0 pr-0 ml-2 hide-background-hover" @click="openModalPayment()">สร้างใบเสนอราคา</v-btn>
                </v-col> -->
                <!-- end ใบเสนอราคา และแก้ไขใบเสนอราคา Desktop -->
                <v-container grid-list-xs class="pt-0">
                  <a-table v-for="(item, index) in itemsCart.choose_list" :key="index" :data-source="item.product_list"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headers" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col justify="center">
                          <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                          <b class="ml-3"
                            style="line-height: 35px; cursor: pointer; font-size: 18px; font-weight: 600; color: #F4BC5F;"
                            @click="gotoShopDetail(item.product_list[0].seller_shop_name, item.product_list[0].seller_shop_id)">ร้านค้า: {{ item.product_list[0].seller_shop_name }}</b>
                          <v-btn class="float-end" color="#1B5DD6" text dark style="font-size:12px; font-weight: 600;"
                            @click="GetReviewQuotation()"><v-icon class="pr-2" size="20">mdi-file-document-outline</v-icon> <span
                              style="text-decoration-line: underline;">ตัวอย่างใบเสนอราคา</span></v-btn>
                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="sku" slot-scope="text, record">
                      <v-col cols="12" class="pl-0">
                        <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                      </v-col>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                          <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                            @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                            style="border-radius: 4px; padding-right: 8px;" v-else @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="12" md="8" sm="8" class="">
                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                          </p>
                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                          </p>
                          <!-- <p class="mb-0 captionSku" style="font-size: 10px;">ราคา: {{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                          <p class="mb-0 captionSku" style="font-size: 10px;">ราคา: {{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p> -->
                          <!-- start detail product attribute Desktop -->
                          <!-- <span v-if="record.product_attribute_detail.attribute_priority_1"
                            class="mb-0 captionSku">{{record.key_1_value}}:
                            {{record.product_attribute_detail.attribute_priority_1}}</span>
                          <span v-if="record.product_attribute_detail.attribute_priority_2"
                            class="pl-2 mb-0 captionSku">{{record.key_2_value}}:
                            {{record.product_attribute_detail.attribute_priority_2}}</span> -->
                          <!-- end detail product attribute Desktop -->
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="revenue_default" slot-scope="text, record">
                        <span style="font-size: 14px; font-weight: 600;">{{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default_with_vat).toFixed(2) : record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                    </template>
                    <template slot="revenue_vat" slot-scope="text, record">
                      <span v-if="record.vat_default === 'no'" style="font-size: 14px; font-weight: 600;">{{
                        Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                      <span v-else style="font-size: 14px; font-weight: 600;">{{
                        Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                    <template slot="item_code_pr" slot-scope="text, record">
                      <v-row v-if="role.role !== 'ext_buyer'">
                        <v-col>
                          <v-autocomplete  v-model="record.item_code_pr_buyer" :items="itemCodePrList" outlined dense
                            style="height: 50px;" item-text="material_name" item-value="material_code"
                            @change="updateSelectPr()" no-data-text="ไม่พบ Item Code PR"></v-autocomplete>
                        </v-col>
                      </v-row>
                      <v-row v-else>
                        <v-col>
                          <v-text-field v-model="record.item_code_pr_buyer" outlined dense style="height: 50px;" ></v-text-field>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                  <a-table v-if="itemsCart.product_free.length !== 0" :data-source="itemsCart.product_free"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headers" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col justify="center">
                          <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="30" height="30" contain></v-img>
                          <b class="ml-2"
                            style="line-height: 35px; font-size: 18px; font-weight: 600; color: #F4BC5F;">แถมฟรี</b>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="sku" slot-scope="text, record">
                      <v-col cols="12" class="pl-0">
                        <span style="font-size: 14px; font-weight: 600;">{{ record.sku }}</span>
                      </v-col>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="12" md="4" sm="4" class="px-0" style="text-align: -webkit-center;">
                          <v-img :src="`${record.product_image}`" contain style="border-radius: 4px; padding-right: 8px;"
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                            @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                            style="border-radius: 4px; padding-right: 8px;" v-else @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="12" md="8" sm="8" class="">
                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                          </p>
                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                          </p>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="revenue_default">
                        <span style="font-size: 14px; font-weight: 600;">0</span>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <span style="font-size: 14px; font-weight: 600;">{{ Number(record.quantity).toLocaleString() }}</span>
                    </template>
                    <template slot="revenue_vat" slot-scope="text, record">
                      <span v-if="record.vat_default === 'no'" style="font-size: 14px; font-weight: 600;">0
                        </span>
                      <span v-else style="font-size: 14px; font-weight: 600;">
                        0</span>
                    </template>
                  </a-table>
                  <v-card class="pl-2 my-3" v-if="role.role === 'ext_buyer'">
                    <v-row class="align-center">
                      <v-col :cols="MobileSize ? '6' : '3'">
                        <span>คูปองส่วนลดร้านค้า</span>
                      </v-col>
                      <v-col :cols="MobileSize ? '6' : '3'">
                        <v-chip v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0" color="#F56E22" @click="clickCoupon('', PointData, parseFloat(XBaht), itemsCart.choose_list[0])" outlined class="pr-5"><span class="px-2">คูปองส่วนลด</span>
                          <v-icon>mdi-ticket-percent-outline</v-icon>
                        </v-chip>
                        <v-chip v-else :color="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? '#FFCDD2' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                          :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                          <span v-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount'" class="px-2 d-inline-block text-truncate" :style="IpadSize ? 'max-width: 150px;' : ''">ส่วนลด {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                          <span v-else-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping'" class="px-2 d-inline-block text-truncate" :style="IpadSize ? 'max-width: 150px;' : ''">ส่วนลดค่าขนส่ง {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                          <span v-else class="px-2">แถมฟรี</span>
                          <v-icon small @click.stop="closeCoupon(itemsCart.choose_list[0])" :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                        </v-chip>
                      </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'">
                      <span>แต้มส่วนลดร้านค้า</span>
                    </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData === 0">
                      <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(getCouponId(), PointData, parseFloat(XBaht), itemsCart.choose_list[0].total_coupon_discount, itemsCart.choose_list[0])" outlined>
                      <span class="px-2">แต้มส่วนลด</span>
                      </v-chip>
                      <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, parseFloat(XBaht), itemsCart.choose_list[0].total_coupon_discount, itemsCart.choose_list[0])" outlined>
                      <span class="px-2">แต้มส่วนลด</span>
                      </v-chip>
                    </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData !== 0">
                      <v-chip color="#FFECB4" style="color: #FFC107;">
                      <span class="px-2">แต้มที่ใช้ {{PointData / (parseFloat(XBaht))}} แต้ม</span>
                        <v-icon small @click.stop="closePoint()" style="color: #FFC107;">mdi-close</v-icon>
                      </v-chip>
                    </v-col>
                    </v-row>
                  </v-card>
                    <v-card v-if="role.role !== 'ext_buyer' && itemsCart.choose_list[0].pay_type !== 'general'" class="mt-6 pa-4" color="#F9FAFD" elevation="0"
                      style="border-radius: 8px;">
                      <v-img class="float-left" src="@/assets/shopping1.png" width="30" height="30"></v-img>
                      <b class="ml-3"
                        style="line-height: 35px; font-size: 18px; font-weight: 600; color: #000000;">รายละเอียดรายการสั่งซื้อ</b>
                      <v-row class="pt-5 text-left d-block">
                        <v-col>
                          <span style="font-size: 16px; font-weight: 400;">PayType :
                            <v-chip v-if="itemsCart.choose_list[0].pay_type === 'onetime'" small
                              style="color: #1B5DD6; background: rgba(27, 93, 214, 0.10); font-size: 14px; padding: 2px 12px;">{{ itemsCart.choose_list[0].pay_type
                                === 'onetime' ? 'One Time' : itemsCart.choose_list[0].pay_type }}</v-chip>
                            <v-chip v-else small
                              style="color: #FF710B; background: rgba(255, 113, 11, 0.10); padding: 2px 12px; font-size: 14px;">{{ itemsCart.choose_list[0].pay_type
                                === 'recurring' ? 'Recurring' : itemsCart.choose_list[0].pay_type }}</v-chip>
                          </span>
                        </v-col>
                      </v-row>
                      <v-form class="mt-3" ref="orderListDetails" :lazy-validation="lazy">
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                        <v-row v-if="chooseitem.product_list.some(productitem => (productitem.product_type !== 'general' && itemsCart.is_JV === 'no') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' ) || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'no' && itemsCart.choose_list[0].pay_type !== 'onetime'))" dense style="justify-content: space-between;">
                          <v-col col="6" class="pb-0" >
                            <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่เริ่มสัญญา <span
                                style="color: red;">*</span></span>
                            <v-dialog ref="dialogContractStartDate" v-model="modalContractStartDate" persistent width="290px">
                              <template v-slot:activator="{ on, attrs }">
                                <v-text-field readonly style="border-radius: 8px;" v-model="contractStartDate" v-bind="attrs"
                                  v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                    color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                              </template>
                              <v-date-picker color="#27AB9C" v-model="date" scrollable reactive locale="Th-th">
                                <v-spacer></v-spacer>
                                <v-btn text color="primary" @click="closeModalContractStartDate()">
                                  ยกเลิก
                                </v-btn>
                                <v-btn text color="primary" @click="setValueContractStartDate(date, chooseindex)">
                                  ตกลง
                                </v-btn>
                              </v-date-picker>
                            </v-dialog>
                          </v-col>
                          <v-col cols="6" class="pb-0">
                            <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่สิ้นสุดสัญญา <span
                                style="color: red;">*</span></span>
                            <v-dialog ref="dialogContractEndDate" v-model="modalContractEndDate" persistent width="290px">
                              <template v-slot:activator="{ on, attrs }">
                                <v-text-field style="border-radius: 8px;"
                                  :disabled="searchContractStartDate !== '' ? false : true" readonly v-model="contractEndDate"
                                  v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                    color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                              </template>
                              <v-date-picker color="#27AB9C" v-model="date1" scrollable reactive locale="Th-th"
                                :min="setMinDateContractEndDate">
                                <v-spacer></v-spacer>
                                <v-btn text color="primary" @click="closeModalContractEndDate()">
                                  ยกเลิก
                                </v-btn>
                                <v-btn text color="primary" @click="setValueContractEndDate(date1, chooseindex)">
                                  ตกลง
                                </v-btn>
                              </v-date-picker>
                            </v-dialog>
                          </v-col>
                        </v-row>
                        </div>
                        <!-- ชื่อผู้ซื้อ -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 14px;font-weight: 400; color: red;">*ไม่ต้องใส่คำนำหน้าชื่อ</span>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">ชื่อผู้ซื้อ</span>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>ชื่อ-สกุล</span><span style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="buyer_name" placeholder="ระบุชื่อ-สกุล" outlined dense
                              @keydown="CheckSpacebarName($event)"
                              oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>เบอร์โทร</span><span style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="buyer_phone" placeholder="ระบุเบอร์โทร"
                                outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>อีเมล</span><span style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="buyer_email" placeholder="ระบุอีเมลล์" outlined
                              dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">หัวหน้าผู้ขอซื้อ</span>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                          </v-row>
                        </div>
                        <!-- หัวหน้าผู้ขอซื้อ -->
                        <!-- <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">หัวหน้าผู้ขอซื้อ</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="4">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="4">
                              <span>เบอร์โทร</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Buyer" placeholder="ระบุเบอร์โทร"
                                outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="4">
                              <span>ตำแหน่ง</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Position" v-model="Position_Buyer" placeholder="ระบุตำแหน่ง" outlined
                                dense @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="5" sm="4">
                              <span>อีเมล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="Email_Buyer" placeholder="ระบุอีเมล" outlined
                                dense></v-text-field>
                            </v-col>
                          </v-row>
                        </div> -->
                        <!-- คณะผู้ตรวจรับ 1 -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="6" md="6" sm="6" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 1</span>
                            </v-col>
                            <v-col cols="6" md="6" sm="6" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 2</span>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Audit1" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Audit2" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <!-- <v-col cols="12" md="3" sm="4">
                              <span>เบอร์โทร</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Audit1"
                                placeholder="ระบุเบอร์โทร" outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="4">
                              <span>ตำแหน่ง</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Position" v-model="Position_Audit1" placeholder="ระบุตำแหน่ง" outlined
                                dense @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="5" sm="4">
                              <span>อีเมล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="Email_Audit1" placeholder="ระบุอีเมล" outlined
                                dense></v-text-field>
                            </v-col> -->
                          </v-row>
                        </div>
                        <!-- คณะผู้ตรวจรับ 2 -->
                        <!-- <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 2</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="4">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Audit2" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="4">
                              <span>เบอร์โทร</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Audit2"
                                placeholder="ระบุเบอร์โทร" outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="4">
                              <span>ตำแหน่ง</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Position" v-model="Position_Audit2" placeholder="ระบุตำแหน่ง" outlined
                                dense @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="5" sm="4">
                              <span>อีเมล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="Email_Audit2" placeholder="ระบุอีเมล" outlined
                                dense></v-text-field>
                            </v-col>
                          </v-row>
                        </div> -->
                        <!-- คณะกรรมการจัดหา 1&2 -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="6" md="6" sm="6" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะกรรมการจัดหา 1</span>
                            </v-col>
                            <v-col cols="6" md="6" sm="6" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะกรรมการจัดหา 2</span>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_SupplyBoard1" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6" sm="6">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_SupplyBoard2" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                          </v-row>
                        </div>
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-col class="pa-0" v-if="(chooseitem.pay_type === 'recurring' || chooseitem.pay_type === 'onetime') && (itemsCart.is_JV === 'no' || itemsCart.is_JV_buyer === 'no')">
                            <v-col cols="12" md="6" sm="4" class="px-0">
                              <span style="font-size: 16px;">งวดชำระเงิน<span style="color: red;"> *</span></span>
                              <v-select :disabled="itemsCart.choose_list[0].pay_type === 'onetime'"  v-model="selectinstallment" hide-details :items="installmentOptions" item-text="formattedMonth" item-value="month" style="border-radius: 8px;" append-icon="mdi-chevron-down" placeholder="กรุณาเลือกงวดชำระเงิน" outlined dense></v-select>
                            </v-col>
                            <v-col cols="12" md="6" sm="4" class="px-0">
                              <span style="font-size: 16px;">Credit term<span style="color: red;"> *</span></span>
                              <v-autocomplete :disabled="itemsCart.is_Tae === 'yes'" v-model="selectedCreditTerm" :items="creditTermOptions" item-text="label" item-value="value" style="border-radius: 8px;" hide-details append-icon="mdi-chevron-down" placeholder="เลือก Credit term" outlined dense></v-autocomplete>
                            </v-col>
                          </v-col>
                        </div>
                        <v-col class="py-6">
                          <v-row style="align-items: center;">
                            <span class="pr-2" style="font-size: 16px;">ขอใช้ส่วนลด: </span>
                            <v-checkbox v-model="discountBahtB2B" @click="clickCheckboxDiscount('Baht')" class="pr-4" hide-details label="ส่วนลดรูปแบบระบุยอด">
                            </v-checkbox>
                            <v-checkbox v-model="discountPercentB2B" @click="clickCheckboxDiscount('Percent')" hide-details label="ส่วนลดรูปแบบเปอร์เซ็นต์">
                            </v-checkbox>
                          </v-row>
                        </v-col>
                        <v-col v-if="!discountBahtB2B && discountPercentB2B || discountBahtB2B && !discountPercentB2B" class="px-0">
                          <v-row>
                            <v-col v-if="discountBahtB2B" cols="12">
                              <span style="font-size: 16px;">ส่วนลด (บาท)<span style="color: red;"> *</span></span>
                              <v-text-field :value="formatAmount(B2BDiscountBaht)" @change="val => onInputB2BDiscount(val)" style="border-radius: 8px;" hide-details placeholder="กรุณาระบุ" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^\./, '').replace(/(\..*)\./g, '$1').replace(/^0+(\d)/, '$1').replace(/(\.\d{2})\d+/, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col v-if="discountPercentB2B" cols="6">
                              <span style="font-size: 16px;">ส่วนลด (%)<span style="color: red;"> *</span></span>
                              <v-text-field v-model="B2BDiscountPercent" @change="onInputB2BPercent()" style="border-radius: 8px;" hide-details placeholder="เลือกส่วนลด" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^\./, '').replace(/(\..*)\./g, '$1').replace(/^0+(\d)/, '$1').replace(/(\.\d{2})\d+/, '$1'); if (parseFloat(this.value) > 99.99) this.value = '99.99';" outlined dense></v-text-field>
                            </v-col>
                            <v-col v-if="discountPercentB2B" cols="6">
                              <span style="font-size: 16px;">ยอดส่วนลด (บาท)</span>
                              <v-text-field :value="formattedB2BDiscount" disabled style="border-radius: 8px;" hide-details placeholder="0.00" outlined dense></v-text-field>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col v-if="selectinstallment !== null" cols="12" class="px-0">
                          <v-card style="border-radius: 8px;" elevation="0">
                            <v-card-text>
                              <v-row>
                                <v-col class="text-start pb-0" cols="12">
                                  <v-row dense>
                                    <v-img class="mr-2" src="@/assets/Layer_1.png" style="max-height: 25px; max-width: 25px;"></v-img>
                                    <span style="font-size: 16px;">ยอดเงินที่ต้องการชำระแต่ละงวด<span style="color: red;"> *</span></span>
                                  </v-row>
                                </v-col>
                                <v-col class="text-start" style="align-content: end;" cols="6">
                                  <!-- <span>ยอดเงินเท่ากันทุกงวด</span> -->
                                  <v-switch
                                    :disabled="itemsCart.choose_list[0].pay_type === 'onetime'"
                                    v-model="sameAmountPerMonth"
                                    inset
                                    label="ยอดเงินเท่ากันทุกงวด"
                                    hide-details
                                  ></v-switch>
                                </v-col>
                                <v-col class="text-end" style="align-content: end;" cols="6">
                                  <span style="font-size: 16px;">ยอดเงินที่ต้องระบุ: <span :style="{ fontSize: '16px', fontWeight: '700', color: Number(remainingAmount.replace(/,/g, '')) < 0 ? 'red' : '#27AB9C' }">{{remainingAmount}} บาท</span></span>
                                </v-col>
                                <v-col
                                  v-for="(amount, index) in installmentAmounts"
                                  :key="index"
                                  class="text-start py-2 px-1"
                                  cols="3"
                                >
                                  <span style="font-size: 16px;">งวดที่ {{ index + 1 }}</span>
                                  <v-text-field
                                    :readonly="sameAmountPerMonth"
                                    v-model="rawInstallmentAmounts[index]"
                                    @blur="() => onBlurAmount(index)"
                                    :rules="[validateInstallment]"
                                    style="border-radius: 8px;"
                                    hide-details
                                    placeholder="กรุณาระบุ"
                                    outlined
                                    dense
                                    oninput="this.value = this.value .replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d*\.\d{2}).*$/, '$1')"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                        <v-col class="px-0">
                          <div style="display: inline-flex;">
                            <v-checkbox
                              v-model="contractSet"
                              :readonly="itemsCart.total_price_no_vat >= 50000"
                              hide-details
                              :label="''"
                              style="margin: 0; padding: 0;"
                            />
                            <span class="ml-2" style="user-select: none; font-size: 16px;">ต้องการระบุสัญญาบริการ</span>
                          </div>
                        </v-col>
                        <span style="line-height: 24px; font-size: 16px; color: #333333;">หมายเหตุ</span>
                        <v-textarea style="border-radius: 8px;" v-model="reason" outlined
                          placeholder="กรุณาระบุ"></v-textarea>
                          <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                            <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                              <v-col cols="12" md="12" sm="12">
                                <span
                                  style="line-height: 24px; font-size: 16px; color: #333333; font-weight: 600px;">ข้อมูลสำหรับการออก
                                  Purchase Requisition และ Purchase Order</span>
                              </v-col>
                              <v-col cols="12" md="4" sm="4">
                                <span style="line-height: 24px; font-size: 16px; color: #333333;">งบประมาณ<span
                                    style="color: red;"> *</span></span>
                                <v-select v-model="selectBudget" :items="itemBudget" item-text="text" item-value="value" outlined
                                  dense></v-select>
                              </v-col>
                              <v-col cols="12" md="4" sm="4">
                                <span style="line-height: 24px; font-size: 16px; color: #333333;">ตัดงบ<span style="color: red;">
                                    *</span></span>
                                <v-select v-model="selectCutBudget" :items="itemCutBudget" item-text="text" item-value="value"
                                  outlined dense></v-select>
                              </v-col>
                              <v-col cols="12" md="4" sm="4">
                                <span style="line-height: 24px; font-size: 16px; color: #333333;">ประเภทเอกสาร<span
                                    style="color: red;"> *</span></span>
                                <v-select v-if="role.role !== 'ext_buyer'" v-model="selectTypeDoc" :items="itemTypeDoc" item-text="name" item-value="name" outlined
                                  dense></v-select>
                                  <v-text-field v-else v-model="selectTypeDoc" outlined dense></v-text-field>
                              </v-col>
                            </v-row>
                          </div>
                      </v-form>
                    </v-card>
                </v-container>
              </v-card>
              <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                <v-card v-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general')" class="pa-4"
                  style="border-radius: 8px; margin-top: 24px">
                  <div>
                    <p v-if="role.role === 'ext_buyer'" style="font-size: 24px"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                    <p v-if="role.role !== 'ext_buyer'" style="font-size: 24px"><b>ที่อยู่ในการจัดส่งสินค้า/ที่อยู่ใบเสนอราคา</b></p>
                  </div>
                  <v-row dense>
                    <v-row>
                      <v-col>
                        <v-radio-group v-model="radios" row class="mt-0">
                          <v-col :cols="IpadSize ? 4 : 3">
                            <span style="font-size: 16px; font-weight: 600; ">รูปแบบการจัดส่ง</span>
                          </v-col>
                          <v-col cols="4" v-if="itemsCart.store_front === 'yes'">
                            <v-radio value="radio-1" @click="getCart()"><template v-slot:label>
                                <span style="font-size: 16px;">รับสินค้าหน้าร้าน</span>
                              </template>
                            </v-radio>
                          </v-col>
                          <v-col cols="4">
                            <v-radio value="radio-2" @click="ClearRadio()"><template v-slot:label>
                                <span tyle="font-size: 16px;">จัดส่งสินค้า</span>
                              </template>
                            </v-radio>
                          </v-col>
                        </v-radio-group>
                        <v-spacer style="border-top: 2px solid #EBEBEB;"></v-spacer>
                        <div v-if="radios === 'radio-2' && CartAddress.length !== 0 && role.role === 'ext_buyer'">
                          <div v-if="userdetail.length !== 0 && (CartAddress[0].detail !== '' && CartAddress[0].detail !== null)">
                            <div v-for="(item, index) in userdetail" :key="index">
                              <div style="margin-top: 16px;" v-if="radios === 'radio-2' && item.default_address === 'Y' && item.detail !== '' && role.role === 'ext_buyer'">
                                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                                <span style="font-size: 16px; font-weight: 600;">{{ item.first_name }} {{ item.last_name
                                }}</span>
                                <span class="px-1" style="color: #EBEBEB;">|</span>
                                <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                                <v-btn class="float-end" color="#27AB9C" text dark
                                  style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                    style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                                  <v-col class="mt-2 pl-0 pr-10 mb-2 ml-8">
                                    <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district
                                    }} {{ item.province }} {{ item.zip_code }}</span><br>
                                    <span  v-if="item.note_address" >หมายเหตุ : {{ item.note_address }}</span>
                                  </v-col>
                              </div>
                            </div>
                          </div>
                          <div v-else-if="userdetail.length !== 0 && (CartAddress[0].detail === '' || CartAddress[0].detail === null)">
                            <div style="margin-top: 16px;">
                              <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                              <span style="font-size: 16px; font-weight: 600;">{{ CartAddress[0].first_name }} {{ CartAddress[0].last_name
                              }}</span>
                              <span class="px-1" style="color: #EBEBEB;">|</span>
                              <span style="font-size: 16px; font-weight: 600;">{{ CartAddress[0].phone }}</span>
                              <v-btn class="float-end" color="#27AB9C" text dark
                                style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                  style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                                <v-col class="mt-2 pl-0 pr-10 mb-2 ml-8">
                                  <span style="font-size: 14px; font-weight: 600; color: red;">* ที่อยู่จัดส่งสินค้าไม่สมบูรณ์ กรุณาแก้ไขหรือเลือกที่อยู่ใหม่</span>
                                </v-col>
                            </div>
                          </div>
                        </div>
                        <div v-if="radios === 'radio-2' && CartAddress.length === 0 && role.role === 'ext_buyer'">
                          <div v-if="radios === 'radio-2' && userdetail.length === 1 && ((userdetail[0].detail === '' && userdetail[0].detail === null) || userdetail[0].default_address !== 'Y') && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="editAddress(userdetail[0])">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                          <div v-else-if="radios === 'radio-2' && userdetail.length === 0 && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddress()">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                        </div>
                        <div v-for="(item, index) in comAddress" :key="index">
                          <div style="margin-top: 16px;" v-if="(radios === 'radio-2' || radios === 'radio-1') && role.role !== 'ext_buyer' && item.default === 'Y'">
                            <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                            <span style="font-size: 16px; font-weight: 600;">{{ item.name_th }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                            <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                              <v-col cols="12" class="pl-0 pb-0 mt-3 ml-8">
                                <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษี: <b>{{ item.tax_id }}</b></span>
                              </v-col>
                              <v-col class="mt-0 pt-0 pl-0 pr-10 mb-4 ml-8">
                                <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district
                                }} {{ item.province }} {{ item.zip_code }}</span>
                              </v-col>
                          </div>
                        </div>
                        <div v-if="radios === 'radio-1'">
                          <span v-if="role.role !== 'ext_buyer' " style="font-size: 20px;"><b>ที่อยู่ร้านค้า</b></span>
                          <div style="margin-top: 16px;" v-for="(item, index) in itemsCart.choose_list" :key="index">
                            <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                            <span style="font-size: 16px; font-weight: 600;">{{ item.product_list[0].seller_shop_name
                            }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 16px; font-weight: 600;">{{ item.shipping_detail.data_seller_address[0].phone !== undefined ? item.shipping_detail.data_seller_address[0].phone : '-' }}</span>
                              <v-col class="mt-2 pl-0 pr-10 mb-4 ml-8">
                                <span style="font-size: 16px;"> บ้านเลขที่ {{ item.shipping_detail.data_seller_address[0].house_no}} {{ item.shipping_detail.data_seller_address[0].detail }} ตำบล/แขวง {{ item.shipping_detail.data_seller_address[0].sub_district }}
                                  อำเภอ/เขต {{ item.shipping_detail.data_seller_address[0].district }} จังหวัด {{ item.shipping_detail.data_seller_address[0].province }} {{ item.shipping_detail.data_seller_address[0].zipcode }}
                                  </span>
                              </v-col>
                            <v-row>
                              <v-col cols="6">
                                <span style="line-height: 24px; font-size: 16px; color: #333333;">วันรับสินค้า <span
                                    style="color: red;">*</span></span>
                                <v-menu v-model="menu" :close-on-content-click="false" :nudge-right="40"
                                  transition="scale-transition" offset-y min-width="auto">
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field v-model="contractDate" style="border-radius: 8px;" outlined dense
                                      placeholder="วว/ดด/ปปปป" readonly v-bind="attrs" v-on="on">
                                      <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                                    </v-text-field>
                                  </template>
                                  <v-date-picker v-model="dates" @input="menu = false, setValueDate(dates)" locale="Th-th"
                                    scrollable no-title :min="today" :max="futureDate"></v-date-picker>
                                </v-menu>
                              </v-col>
                              <v-col cols="6" :class="{ 'is-disabled': dates === '' }">
                                <span style="line-height: 24px; font-size: 16px; color: #333333;">เวลารับสินค้า <span style="color: red;">*</span></span>
                                <v-col class="pt-0 pl-0" v-if="dates === today">
                                  <template>
                                    <a-space direction="vertical" style="width: 100%;">
                                      <a-time-picker v-model="timeselecttoday" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น."  :disabledHours="disabledHours" :disabledMinutes="disabledMinutes" />
                                      <a-time-range-picker :bordered="false" style="width: 100%;" />
                                    </a-space>
                                  </template>
                                </v-col>
                                <v-col class="pt-0 pl-0" v-else>
                                  <template>
                                    <a-space direction="vertical" style="width: 100%;">
                                      <a-time-picker v-model="timeselect" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น." />
                                      <a-time-range-picker :bordered="false" style="width: 100%;" />
                                    </a-space>
                                  </template>
                                </v-col>
                              </v-col>
                            </v-row>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-row>
                </v-card>
              </div>
                <v-card v-if="itemsCart.isEtax === 'yes'" class="pa-4"
                  style="border-radius: 8px; margin-top: 24px">
                  <div>
                    <p style="font-size: 24px"><b>ที่อยู่ในการออกใบกำกับภาษี</b></p>
                  </div>
                  <v-row dense>
                    <v-row>
                      <v-col>
                        <v-radio-group v-model="radiostax" row class="mt-0">
                          <v-col cols="4">
                            <v-radio value="radiotax-1" @click="closeDialogTax()">
                              <template v-slot:label>
                                <span style="font-size: 16px;">รับใบกำกับภาษี</span>
                              </template>
                            </v-radio>
                          </v-col>
                          <v-col cols="4">
                            <v-radio value="radiotax-2"><template v-slot:label>
                                <span tyle="font-size: 16px;">ไม่รับใบกำกับภาษี</span>
                              </template>
                            </v-radio>
                          </v-col>
                        </v-radio-group>
                      </v-col>
                    </v-row>
                  </v-row>
                  <div v-if="radiostax !== 'radiotax-2'">
                    <v-spacer class="mt-2" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                    <div style="margin-top: 16px;" v-for="(item, index) in invoicedetail" :key="index">
                      <div v-if="item.default_invoice === 'Y'">
                        <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                        <span style="font-size: 16px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600; color: #27AB9C;">{{item.tax_type === 'Personal' ? 'บุคคลธรรมดา' : 'นิติบุคคล'}}</span>
                        <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="DialogTaxAddress = !DialogTaxAddress"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                        <v-col class="ml-6 py-0 pt-2" v-if="item.tax_type !== 'Personal'">
                          <span style="font-size: 16px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                        </v-col>
                        <v-col class="ml-6 py-0">
                          <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                        </v-col>
                        <v-col class="ml-6 pt-1 pr-10">
                          <span style="font-size: 16px;">ที่อยู่: {{item.address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postal_code}}</span>
                        </v-col>
                        </div>
                      </div>
                      <v-card v-if="invoicedetail.length === 0" class="mt-7" elevation="0"
                      style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="openModalTaxAddress()">
                      <v-card-text class="py-2">
                        <v-row class="my-2 px-3 ">
                          <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                          <span class="pl-2"
                            style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                    </div>
                </v-card>
            </v-col>
            <!-- Mobile-->
            <v-col cols="12" md="12" v-if="MobileSize">
              <v-card class="px-1 py-2" style="border-radius: 8px;">
                <v-col cols="12" class="my-2 pt-0">
                  <v-row dense>
                    <p style="font-size: 18px" class="mb-0"><b>รายการสั่งซื้อสินค้า</b></p>
                    <!-- <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 16px; margin-left: 10px;"></v-spacer> -->
                  </v-row>
                </v-col>
                <v-container grid-list-xs class="pt-0">
                  <a-table v-for="(item, index) in itemsCart.choose_list" :key="index" :data-source="item.product_list"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headersMobile" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col align="center">
                          <v-img class="float-left" src="@/assets/icon_image/store.png" width="24" height="24"></v-img>
                          <b class="float-left ml-2 d-inline-block text-truncate"
                            style="line-height: 35px; cursor: pointer; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px"
                            @click="gotoShopDetail(item.product_list[0].seller_shop_name, item.product_list[0].seller_shop_id)">ร้านค้า: {{ item.product_list[0].seller_shop_name }}</b>
                          <v-btn class="float-end" color="#1B5DD6" small text dark style="font-size:12px; font-weight: 600;"
                            @click="GetReviewQuotation()"><v-icon class="pr-1" size="20">mdi-file-document-outline</v-icon> <span
                              style="text-decoration-line: underline;">ตัวอย่างใบเสนอราคา</span></v-btn>
                            </v-col>
                          </v-row>
                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row >
                        <v-col cols="4" md="4" class="pr-2">
                          <v-img :src="`${record.product_image}`" contain
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                            @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                            @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="8" md="8">
                          <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                          <!-- start detail product attribute mobile -->
                          <!-- <span v-if="record.product_attribute_detail.attribute_priority_1"
                            class="mb-0 captionSku">{{record.key_1_value}}:
                            {{record.product_attribute_detail.attribute_priority_1}}</span>
                          <span v-if="record.product_attribute_detail.attribute_priority_2"
                            class="pl-2 mb-0 captionSku">{{record.key_2_value}}:
                            {{record.product_attribute_detail.attribute_priority_2}}</span> -->
                          <!-- end detail product attribute mobile -->
                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                          </p>
                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                          </p>
                          <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;"> {{ Number(record.vat_default === 'yes' ? parseFloat(record.revenue_default_with_vat).toFixed(2) : record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</b></p>
                          <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                          <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">{{Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                          </p>
                          <p v-else class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">{{Number(record.show_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                          </p>
                          <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                            <div v-if="chooseitem.product_list.some(productitem => role.role !== 'ext_buyer' && itemsCart.is_JV_buyer === 'yes')" >
                            <div v-if="role.role !== 'ext_buyer'">
                              <p class="mb-0 captionSku">Item Code PR<span style="color: red;">*</span>:</p>
                              <v-autocomplete class="mt-1" v-model="record.item_code_pr_buyer" :items="itemCodePrList" outlined
                                dense style="height: 50px; width: 60%;" item-text="material_name" item-value="material_code"
                                @change="updateSelectPr()" no-data-text="ไม่พบ Item Code PR"></v-autocomplete>
                            </div>
                            <div v-else>
                              <p class="mb-0 captionSku">Item Code PR:</p>
                              <v-text-field v-model="record.item_code_pr_buyer" outlined dense style="height: 50px;  width: 60%;" ></v-text-field>
                            </div>
                          </div>
                          </div>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                  <a-table v-if="itemsCart.product_free.length !== 0" :data-source="itemsCart.product_free"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headersMobile" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col align="center">
                          <v-img class="float-left" src="@/assets/icons/nullproduct.png" width="24" height="24"></v-img>
                          <b class="float-left ml-2 d-inline-block text-truncate"
                            style="line-height: 35px; font-size: 14px; font-weight: 600; color: #F4BC5F; max-width: 90px">แถมฟรี</b>
                            </v-col>
                          </v-row>
                          <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 6px;"></v-spacer>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row >
                        <v-col cols="4" md="4" class="pr-2">
                          <v-img :src="`${record.product_image}`" contain
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"
                            @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else
                            @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="8" md="8">
                          <p class="mb-0 captionSku">รหัสสินค้า: <b style="font-size: 14px;">{{ record.sku }}</b></p>
                          <p class="mb-0" style="font-size: 14px; font-weight: 600;">{{ record.product_name }}</p>
                          <p v-if="record.product_attribute_detail.attribute_priority_1 !== '' && record.product_attribute_detail.attribute_priority_1 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_1_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_1 }}</span>
                          </p>
                          <p v-if="record.product_attribute_detail.attribute_priority_2 !== '' && record.product_attribute_detail.attribute_priority_2 !== null" class="mb-0"
                            style="font-size: 14px;">{{ record.product_attribute_detail.key_2_value }}: <span class="mb-0"
                              style="font-size: 14px; font-weight: 600;">{{ record.product_attribute_detail.attribute_priority_2 }}</span>
                          </p>
                          <p class="mb-0 captionSku">ราคาต่อชิ้น: <b style="font-size: 14px;">0</b></p>
                          <p class="mb-0 captionSku">จำนวน: <b style="font-size: 14px;">{{ record.quantity }}</b></p>
                          <p v-if="record.vat_default === 'no'" class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                          </p>
                          <p v-else class="mb-0 captionSku">ราคารวม: <b style="font-size: 14px;">0</b>
                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                          </p>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                  <v-card class="pl-2 my-3" v-if="role.role === 'ext_buyer'">
                    <v-row class="align-center">
                      <v-col :cols="MobileSize ? '6' : '3'">
                        <span>คูปองส่วนลดร้านค้า</span>
                      </v-col>
                      <v-col :cols="MobileSize ? '6' : '3'">
                        <v-chip v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0"  color="#F56E22" @click="clickCoupon('', PointData, parseFloat(XBaht), itemsCart.choose_list[0])" outlined class="pr-5"><span class="px-2">คูปองส่วนลด</span>
                          <v-icon>mdi-ticket-percent-outline</v-icon>
                        </v-chip>
                        <v-chip v-else :color="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? '#FFCDD2' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                          :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                          <span v-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount'" class="px-2 d-inline-block text-truncate" style="max-width: 100px;">ส่วนลด {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                          <span v-else-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping'" class="px-2 d-inline-block text-truncate" style="max-width: 100px;">ส่วนลดค่าขนส่ง {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                          <span v-else class="px-2">แถมฟรี</span>
                          <v-icon small @click.stop="closeCoupon(itemsCart.choose_list[0])" :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                        </v-chip>
                      </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'">
                      <span>แต้มส่วนลดร้านค้า</span>
                    </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData === 0">
                      <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-if="CouponData.length !== 0" color="#FFC107" @click="clickPoint(getCouponId(), PointData, parseFloat(XBaht), itemsCart.choose_list[0].total_coupon_discount, itemsCart.choose_list[0])" outlined>
                      <span class="px-2">แต้มส่วนลด</span>
                      </v-chip>
                      <v-chip :disabled="itemsCart.seller_use_point === 'no'" :class="itemsCart.seller_use_point === 'no' ? 'disChipClickCoupon' : ''" class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, parseFloat(XBaht), itemsCart.choose_list[0].total_coupon_discount, itemsCart.choose_list[0])" outlined>
                      <span class="px-2">แต้มส่วนลด</span>
                      </v-chip>
                    </v-col>
                    <v-col :cols="MobileSize ? '6' : '3'" v-if="PointData !== 0">
                      <v-chip color="#FFECB4" style="color: #FFC107;">
                      <span class="px-2">แต้มที่ใช้ {{PointData / (parseFloat(XBaht))}} แต้ม</span>
                        <v-icon small @click.stop="closePoint()" style="color: #FFC107;">mdi-close</v-icon>
                      </v-chip>
                    </v-col>
                    </v-row>
                  </v-card>
                    <v-card v-if="role.role !== 'ext_buyer' && itemsCart.choose_list[0].pay_type !== 'general'" class="mt-6 pa-4" color="#F9FAFD" elevation="0"
                      style="border-radius: 8px;">
                      <v-img class="float-left" src="@/assets/shopping1.png" width="24" height="24"></v-img>
                      <b class="ml-3"
                        style="line-height: 35px; font-size: 16px; font-weight: 600; color: #000000;">รายละเอียดรายการสั่งซื้อ</b>
                      <v-row class="pt-5 text-left d-block">
                        <v-col>
                          <span style="font-size: 14px; font-weight: 400;">PayType :
                            <v-chip v-if="itemsCart.choose_list[0].pay_type === 'onetime'" small
                              style="color: #1B5DD6; background: rgba(27, 93, 214, 0.10); font-size: 14px; padding: 2px 12px;">{{ itemsCart.choose_list[0].pay_type
                                === 'onetime' ? 'One Time' : itemsCart.choose_list[0].pay_type }}</v-chip>
                            <v-chip v-else small
                              style="color: #FF710B; background: rgba(255, 113, 11, 0.10); padding: 2px 12px; font-size: 14px;">{{ itemsCart.choose_list[0].pay_type
                                === 'recurring' ? 'Recurring' : itemsCart.choose_list[0].pay_type }}</v-chip>
                          </span>
                        </v-col>
                      </v-row>
                      <v-form class="mt-3" ref="orderListDetails" :lazy-validation="lazy">
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row v-if="chooseitem.product_list.some(productitem => (productitem.product_type !== 'general' && itemsCart.is_JV === 'no') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' ) || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && itemsCart.choose_list[0].pay_type !== 'onetime') || (productitem.product_type !== 'general' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'no' && itemsCart.choose_list[0].pay_type !== 'onetime'))" dense style="justify-content: space-between;">
                            <v-col col="12" class="pb-0">
                              <span style="line-height: 24px; font-size: 14px; color: #333333;">วันที่เริ่มสัญญา <span
                                  style="color: red;">*</span></span>
                              <v-dialog ref="dialogContractStartDate" v-model="modalContractStartDate" persistent width="290px">
                                <template v-slot:activator="{ on, attrs }">
                                  <v-text-field readonly style="border-radius: 8px;" v-model="contractStartDate" v-bind="attrs"
                                    v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                      color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                                </template>
                                <v-date-picker color="#27AB9C" v-model="date" scrollable reactive locale="Th-th">
                                  <v-spacer></v-spacer>
                                  <v-btn text color="primary" @click="closeModalContractStartDate()">
                                    ยกเลิก
                                  </v-btn>
                                  <v-btn text color="primary" @click="setValueContractStartDate(date, chooseindex)">
                                    ตกลง
                                  </v-btn>
                                </v-date-picker>
                              </v-dialog>
                            </v-col>
                            <v-col cols="12" class="pb-0">
                              <span style="line-height: 24px; font-size: 14px; color: #333333;">วันที่สิ้นสุดสัญญา <span
                                  style="color: red;">*</span></span>
                              <v-dialog ref="dialogContractEndDate" v-model="modalContractEndDate" persistent width="290px">
                                <template v-slot:activator="{ on, attrs }">
                                  <v-text-field style="border-radius: 8px;"
                                    :disabled="searchContractStartDate !== '' ? false : true" readonly v-model="contractEndDate"
                                    v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append"
                                      color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                                </template>
                                <v-date-picker color="#27AB9C" v-model="date1" scrollable reactive locale="Th-th"
                                  :min="setMinDateContractEndDate">
                                  <v-spacer></v-spacer>
                                  <v-btn text color="primary" @click="closeModalContractEndDate()">
                                    ยกเลิก
                                  </v-btn>
                                  <v-btn text color="primary" @click="setValueContractEndDate(date1, chooseindex)">
                                    ตกลง
                                  </v-btn>
                                </v-date-picker>
                              </v-dialog>
                            </v-col>
                          </v-row>
                        </div>
                        <!-- ชื่อผู้ซื้อ -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 14px;font-weight: 400; color: red;">*ไม่ต้องใส่คำนำหน้าชื่อ</span>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">ชื่อผู้ซื้อ</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="buyer_name" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keydown="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2">
                              <span>เบอร์โทร</span><span style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="buyer_phone" placeholder="ระบุเบอร์โทร"
                                outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="2">
                              <span>อีเมล</span><span style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="buyer_email" placeholder="ระบุอีเมลล์" outlined
                                dense></v-text-field>
                            </v-col>
                          </v-row>
                        </div>
                        <!-- หัวหน้าผู้ขอซื้อ -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">หัวหน้าผู้ขอซื้อ</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <!-- <v-col cols="12" md="3" sm="2">
                              <span>เบอร์โทร</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Buyer" placeholder="ระบุเบอร์โทร"
                                outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="2">
                              <span>ตำแหน่ง</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Position" v-model="Position_Buyer" placeholder="ระบุตำแหน่ง" outlined
                                dense @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="5" sm="2">
                              <span>อีเมล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="Email_Buyer" placeholder="ระบุอีเมล" outlined
                                dense></v-text-field>
                            </v-col> -->
                          </v-row>
                        </div>
                        <!-- คณะผู้ตรวจรับ 1 -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 1</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Audit1" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <!-- <v-col cols="12" md="3" sm="2">
                              <span>เบอร์โทร</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Audit1"
                                placeholder="ระบุเบอร์โทร" outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="2">
                              <span>ตำแหน่ง</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Position" v-model="Position_Audit1" placeholder="ระบุตำแหน่ง" outlined
                                dense @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="5" sm="2">
                              <span>อีเมล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="Email_Audit1" placeholder="ระบุอีเมล" outlined
                                dense></v-text-field>
                            </v-col> -->
                          </v-row>
                        </div>
                        <!-- คณะผู้ตรวจรับ 2 -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 2</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_Audit2" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <!-- <v-col cols="12" md="3" sm="2">
                              <span>เบอร์โทร</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Audit2"
                                placeholder="ระบุเบอร์โทร" outlined dense
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                @keypress="CheckSpacebarName($event)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="2">
                              <span>ตำแหน่ง</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Position" v-model="Position_Audit2" placeholder="ระบุตำแหน่ง" outlined
                                dense @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="5" sm="2">
                              <span>อีเมล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Email" v-model="Email_Audit2" placeholder="ระบุอีเมล" outlined
                                dense></v-text-field>
                            </v-col> -->
                          </v-row>
                        </div>
                        <!-- คณะกรรมการจัดหา 1&2 -->
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 00;">คณะกรรมการจัดหา 1</span>
                            </v-col>
                            <v-col cols="12" md="12" sm="12">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_SupplyBoard1" placeholder="ระบุชื่อ-สกุล" outlined dense
                              @keypress="CheckSpacebarName($event)"
                              oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" class="">
                              <span style="font-size: 16px;font-weight: 600;">คณะกรรมการจัดหา 2</span>
                            </v-col>
                            <v-col cols="12" md="12" sm="12">
                              <span>ชื่อ-สกุล</span><span v-if="role.role !== 'ext_buyer'" style="color: red;"> *</span>
                              <v-text-field :rules="Rules.Name" v-model="Name_SupplyBoard2" placeholder="ระบุชื่อ-สกุล" outlined dense
                                @keypress="CheckSpacebarName($event)"
                                oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                            </v-col>
                          </v-row>
                        </div>
                        <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                          <v-col class="pa-0" v-if="(chooseitem.pay_type === 'recurring' || chooseitem.pay_type === 'onetime') && (itemsCart.is_JV === 'no' || itemsCart.is_JV_buyer === 'no')">
                            <v-col cols="12" class="px-0">
                              <span style="font-size: 14px;">งวดชำระเงิน<span style="color: red;"> *</span></span>
                              <v-select :disabled="itemsCart.choose_list[0].pay_type === 'onetime'"  v-model="selectinstallment" hide-details :items="installmentOptions" item-text="formattedMonth" item-value="month" style="border-radius: 8px;" append-icon="mdi-chevron-down" placeholder="กรุณาเลือกงวดชำระเงิน" outlined dense></v-select>
                            </v-col>
                            <v-col cols="12" class="px-0">
                              <span style="font-size: 14px;">Credit term<span style="color: red;"> *</span></span>
                              <v-autocomplete :disabled="itemsCart.is_Tae === 'yes'" v-model="selectedCreditTerm" :items="creditTermOptions" item-text="label" item-value="value" style="border-radius: 8px;" hide-details append-icon="mdi-chevron-down" placeholder="เลือก Credit term" outlined dense></v-autocomplete>
                            </v-col>
                          </v-col>
                        </div>
                        <v-col class="px-0">
                            <span class="pr-2" style="font-size: 14px;">ขอใช้ส่วนลด: </span>
                            <v-checkbox v-model="discountBahtB2B" @click="clickCheckboxDiscount('Baht')" hide-details label="ส่วนลดรูปแบบระบุยอด">
                            </v-checkbox>
                            <v-checkbox v-model="discountPercentB2B" @click="clickCheckboxDiscount('Percent')" hide-details label="ส่วนลดรูปแบบเปอร์เซ็นต์">
                            </v-checkbox>
                        </v-col>
                        <v-col v-if="!discountBahtB2B && discountPercentB2B || discountBahtB2B && !discountPercentB2B" class="px-0">
                          <v-row>
                            <v-col v-if="discountBahtB2B" cols="12">
                              <span style="font-size: 14px;">ส่วนลด (บาท)<span style="color: red;"> *</span></span>
                              <v-text-field :value="formatAmount(B2BDiscountBaht)" @change="val => onInputB2BDiscount(val)" style="border-radius: 8px;" hide-details placeholder="กรุณาระบุ" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^\./, '').replace(/(\..*)\./g, '$1').replace(/^0+(\d)/, '$1').replace(/(\.\d{2})\d+/, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col v-if="discountPercentB2B" cols="12">
                              <span style="font-size: 14px;">ส่วนลด (%)<span style="color: red;"> *</span></span>
                              <v-text-field v-model="B2BDiscountPercent" @change="onInputB2BPercent()" style="border-radius: 8px;" hide-details placeholder="เลือกส่วนลด" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^\./, '').replace(/(\..*)\./g, '$1').replace(/^0+(\d)/, '$1').replace(/(\.\d{2})\d+/, '$1'); if (parseFloat(this.value) > 99.99) this.value = '99.99';" outlined dense></v-text-field>
                            </v-col>
                            <v-col v-if="discountPercentB2B" cols="12">
                              <span style="font-size: 14px;">ยอดส่วนลด (บาท)</span>
                              <v-text-field :value="formattedB2BDiscount" disabled style="border-radius: 8px;" hide-details placeholder="0.00" outlined dense></v-text-field>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col v-if="selectinstallment !== null" cols="12" class="px-0">
                          <v-card style="border-radius: 8px;" elevation="0">
                            <v-card-text>
                              <v-row>
                                <v-col class="text-start pb-0" cols="12">
                                  <v-row dense>
                                    <v-img class="mr-2" src="@/assets/Layer_1.png" style="max-height: 25px; max-width: 25px;"></v-img>
                                    <span style="font-size: 14px;">ยอดเงินที่ต้องการชำระแต่ละงวด<span style="color: red;"> *</span></span>
                                  </v-row>
                                </v-col>
                                <v-col class="text-start" style="align-content: end;" cols="6">
                                  <!-- <span>ยอดเงินเท่ากันทุกงวด</span> -->
                                  <v-switch
                                    :disabled="itemsCart.choose_list[0].pay_type === 'onetime'"
                                    v-model="sameAmountPerMonth"
                                    inset
                                    label="ยอดเงินเท่ากันทุกงวด"
                                    hide-details
                                  ></v-switch>
                                </v-col>
                                <v-col class="text-end" style="align-content: end;" cols="6">
                                  <span style="font-size: 14px;">ยอดเงินที่ต้องระบุ: <span :style="{ fontSize: '16px', fontWeight: '700', color: Number(remainingAmount.replace(/,/g, '')) < 0 ? 'red' : '#27AB9C' }">{{remainingAmount}} บาท</span></span>
                                </v-col>
                                <v-col
                                  v-for="(amount, index) in installmentAmounts"
                                  :key="index"
                                  class="text-start py-2 px-1"
                                  cols="12"
                                >
                                  <span style="font-size: 16px;">งวดที่ {{ index + 1 }}</span>
                                  <v-text-field
                                    :readonly="sameAmountPerMonth"
                                    v-model="rawInstallmentAmounts[index]"
                                    @blur="() => onBlurAmount(index)"
                                    :rules="[validateInstallment]"
                                    style="border-radius: 8px;"
                                    hide-details
                                    placeholder="กรุณาระบุ"
                                    outlined
                                    dense
                                    oninput="this.value = this.value .replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d*\.\d{2}).*$/, '$1')"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                        <v-col class="pa-0 mb-4">
                          <div style="display: inline-flex;">
                            <v-checkbox
                              v-model="contractSet"
                              :readonly="itemsCart.total_price_no_vat >= 50000"
                              hide-details
                              :label="''"
                              style="margin: 0; padding: 0;"
                            />
                            <span class="ml-2" style="user-select: none; font-size: 16px;">ต้องการระบุสัญญาบริการ</span>
                          </div>
                        </v-col>
                        <span style="line-height: 24px; font-size: 14px; color: #333333;">หมายเหตุ</span>
                        <v-textarea style="border-radius: 8px;" v-model="reason" outlined
                          placeholder="กรุณาระบุ"></v-textarea>
                          <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                            <v-row dense v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes')">
                              <v-col cols="12" md="12" sm="12">
                                <span
                                  style="line-height: 24px; font-size: 14px; color: #333333; font-weight: 600px;">ข้อมูลสำหรับการออก
                                  Purchase Requisition และ Purchase Order</span>
                              </v-col>
                              <v-col cols="12" md="4" sm="6">
                                <span style="line-height: 24px; font-size: 14px; color: #333333;">งบประมาณ<span
                                    style="color: red;"> *</span></span>
                                <v-select v-model="selectBudget" :items="itemBudget" item-text="text" item-value="value" outlined
                                  dense></v-select>
                              </v-col>
                              <v-col cols="12" md="4" sm="6">
                                <span style="line-height: 24px; font-size: 14px; color: #333333;">ตัดงบ<span style="color: red;">
                                    *</span></span>
                                <v-select v-model="selectCutBudget" :items="itemCutBudget" item-text="text" item-value="value"
                                  outlined dense></v-select>
                              </v-col>
                              <v-col cols="12" md="4" sm="6">
                                <span style="line-height: 24px; font-size: 14px; color: #333333;">ประเภทเอกสาร<span
                                    style="color: red;"> *</span></span>
                                <v-select v-if="role.role !== 'ext_buyer'" v-model="selectTypeDoc" :items="itemTypeDoc" item-text="name" item-value="name" outlined
                                  dense></v-select>
                                  <v-text-field v-else v-model="selectTypeDoc" outlined dense></v-text-field>
                              </v-col>
                            </v-row>
                          </div>
                      </v-form>
                    </v-card>
                </v-container>
              </v-card>
              <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                <v-card v-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general')" class="pa-4"
                  style="border-radius: 8px; margin-top: 24px">
                  <div>
                    <p v-if="role.role === 'ext_buyer'" style="font-size: 18px"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                    <p v-if="role.role !== 'ext_buyer'" style="font-size: 24px"><b>ที่อยู่ในการจัดส่งสินค้า/ที่อยู่ใบเสนอราคา</b></p>
                  </div>
                  <v-row dense>
                    <v-row>
                      <v-col>
                        <v-radio-group v-model="radios" row class="mt-n5">
                          <v-col cols="12">
                            <span style="font-size: 14px; font-weight: 600; ">รูปแบบการจัดส่ง</span>
                          </v-col>
                          <v-col cols="6" class="px-0 pt-0" v-if="itemsCart.store_front === 'yes'">
                            <v-radio small value="radio-1" @click="getCart()"><template v-slot:label>
                                <span style="font-size: 14px;"><b>รับสินค้าหน้าร้าน</b></span>
                              </template>
                            </v-radio>
                          </v-col>
                          <v-col cols="6" class="px-0 pt-0">
                            <v-radio small value="radio-2" @click="ClearRadio()"><template v-slot:label>
                                <span style="font-size: 14px;"><b>จัดส่งสินค้า</b></span>
                              </template>
                            </v-radio>
                          </v-col>
                        </v-radio-group>
                        <v-spacer style="border-top: 2px solid #EBEBEB;"></v-spacer>
                        <div v-if="radios === 'radio-2' && CartAddress.length !== 0 && role.role === 'ext_buyer'">
                          <div v-if="userdetail.length !== 0 && (CartAddress[0].detail !== '' && CartAddress[0].detail !== null)">
                            <div v-for="(item, index) in userdetail" :key="index">
                              <div style="margin-top: 16px;" v-if="radios === 'radio-2' && item.default_address === 'Y' && item.detail !== '' && role.role === 'ext_buyer'">
                                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                                <span style="font-size: 14px; font-weight: 600;">{{ item.first_name }} {{ item.last_name
                                }}</span>
                                <span class="px-1" style="color: #EBEBEB;">|</span>
                                <span style="font-size: 14px; font-weight: 600;">{{ item.phone }}</span>
                                <v-btn class="float-end" color="#27AB9C" text dark
                                  style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                    style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                                  <v-col class="ml-6 pt-1 pr-5" style="max-width: 260px;">
                                    <span style="font-size: 12px; font-weight: 600;">{{ item.detail }} {{ item.sub_district }} {{ item.district
                                    }} {{ item.province }} {{ item.zip_code }}</span><br>
                                    <span style="font-size: 12px; font-weight: 600;" v-if="item.note_address" >หมายเหตุ : {{ item.note_address }}</span>
                                  </v-col>
                              </div>
                            </div>
                          </div>
                          <div v-else-if="userdetail.length !== 0 && (CartAddress[0].detail === '' || CartAddress[0].detail === null)">
                            <div style="margin-top: 16px;">
                              <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                              <span style="font-size: 14px; font-weight: 600;">{{ CartAddress[0].first_name }} {{ CartAddress[0].last_name
                              }}</span>
                              <span class="px-1" style="color: #EBEBEB;">|</span>
                              <span style="font-size: 14px; font-weight: 600;">{{ CartAddress[0].phone }}</span>
                              <v-btn class="float-end" color="#27AB9C" text dark
                                style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                  style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                                <v-col class="ml-6 pt-1 pr-5" style="max-width: 260px;">
                                  <span style="font-size: 14px; font-weight: 600; color: red;">* ที่อยู่จัดส่งสินค้าไม่สมบูรณ์ กรุณาแก้ไขหรือเลือกที่อยู่ใหม่</span>
                                </v-col>
                            </div>
                          </div>
                        </div>
                        <div v-if="radios === 'radio-2' && CartAddress.length === 0 && role.role === 'ext_buyer'">
                          <div v-if="radios === 'radio-2' && userdetail.length === 1 && ((userdetail[0].detail === '' && userdetail[0].detail === null) || userdetail[0].default_address !== 'Y') && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                            style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="editAddress(userdetail[0])">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                  style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                          <div v-else-if="radios === 'radio-2' && userdetail.length === 0 && role.role === 'ext_buyer'">
                            <v-card class="mt-7" elevation="0"
                              style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addAddress()">
                              <v-card-text class="py-2">
                                <v-row class="my-2 px-3 ">
                                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                                  <span class="pl-2"
                                    style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </div>
                        </div>
                        <div v-for="(item, index) in comAddress" :key="index">
                          <div style="margin-top: 16px;" v-if="(radios === 'radio-2' || radios === 'radio-1') && role.role !== 'ext_buyer' && item.default === 'Y'">
                            <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                            <span style="font-size: 14px; font-weight: 600;">{{ item.name_th }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 14px; font-weight: 600;">{{ item.phone }}</span>
                            <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="changeAddress()"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                              <v-col cols="12" class="pl-8 pb-0 mt-3">
                                <span style="font-size: 12px;">เลขประจำตัวผู้เสียภาษี: <b>{{ item.tax_id }}</b></span>
                              </v-col>
                              <v-col cols="12" class="pl-8 pt-0" style="max-width: 260px;">
                                <span style="font-size: 12px; font-weight: 600;" >{{ item.detail }} {{ item.sub_district }} {{ item.district
                                }} {{ item.province }} {{ item.zip_code }}</span>
                              </v-col>
                          </div>
                        </div>
                        <div v-if="radios === 'radio-1'">
                          <span v-if="role.role !== 'ext_buyer' " style="font-size: 16px;"><b>ที่อยู่ร้านค้า</b></span>
                          <div style="margin-top: 16px;" v-for="(item, index) in itemsCart.choose_list" :key="index">
                            <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                            <span style="font-size: 14px; font-weight: 600;">{{ item.product_list[0].seller_shop_name
                            }}</span>
                            <span class="px-1" style="color: #EBEBEB;">|</span>
                            <span style="font-size: 14px; font-weight: 600;">{{ item.shipping_detail.data_seller_address[0].phone !== undefined ? item.shipping_detail.data_seller_address[0].phone : '-' }}</span>
                            <v-row>
                              <v-col class="mt-2 pl-0 mb-4" style="max-width: 260px;">
                                <span style="font-size: 12px; font-weight: 600;"> บ้านเลขที่ {{ item.shipping_detail.data_seller_address[0].house_no}} {{ item.shipping_detail.data_seller_address[0].detail }} ตำบล/แขวง {{ item.shipping_detail.data_seller_address[0].sub_district }}
                                  อำเภอ/เขต {{ item.shipping_detail.data_seller_address[0].district }} จังหวัด {{ item.shipping_detail.data_seller_address[0].province }} {{ item.shipping_detail.data_seller_address[0].zipcode }}
                                  </span>
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="6" class="pb-0">
                                <span style="line-height: 24px; font-size: 14px; color: #333333;">วันรับสินค้า <span
                                    style="color: red;">*</span></span>
                                <v-menu v-model="menu" :close-on-content-click="false" :nudge-right="40"
                                  transition="scale-transition" offset-y min-width="auto">
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field v-model="contractDate" style="border-radius: 8px;" outlined dense
                                      placeholder="วว/ดด/ปปปป" readonly v-bind="attrs" v-on="on">
                                      <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                                    </v-text-field>
                                  </template>
                                  <v-date-picker v-model="dates" @input="menu = false, setValueDate(dates)" locale="Th-th"
                                    scrollable no-title :min="today" :max="futureDate"></v-date-picker>
                                </v-menu>
                              </v-col>
                              <v-col cols="6" class="pl-0" :class="{ 'is-disabled': dates === '' }">
                                <span style="line-height: 24px; font-size: 14px; color: #333333;">เวลารับสินค้า <span style="color: red;">*</span></span>
                                <v-col class="pt-0 pl-0" v-if="dates === today">
                                  <template>
                                    <a-space direction="vertical" style="width: 100%;">
                                      <a-time-picker v-model="timeselecttoday" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น."  :disabledHours="disabledHours" :disabledMinutes="disabledMinutes"/>
                                      <a-time-range-picker :bordered="false" style="width: 100%;" />
                                    </a-space>
                                  </template>
                                </v-col>
                                <v-col class="pt-0 pl-0" v-else>
                                  <template>
                                    <a-space direction="vertical" style="width: 100%;">
                                      <a-time-picker v-model="timeselect" :bordered="false" style="width: 100%;" format="HH:mm น." size="large" placeholder="00.00 น." />
                                      <a-time-range-picker :bordered="false" style="width: 100%;" />
                                    </a-space>
                                  </template>
                                </v-col>
                              </v-col>
                            </v-row>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-row>
                </v-card>
              </div>
                <v-card v-if="itemsCart.isEtax === 'yes'" class="pa-4"
                  style="border-radius: 8px; margin-top: 24px">
                  <div>
                    <p style="font-size: 18px"><b>ที่อยู่ในการออกใบกำกับภาษี</b></p>
                  </div>
                  <v-row dense>
                    <v-row>
                      <v-col>
                        <v-radio-group v-model="radiostax" row class="mt-0">
                          <v-col cols="12">
                            <v-radio value="radiotax-1" @click="closeDialogTax()">
                              <template v-slot:label>
                                <span style="font-size: 14px;">รับใบกำกับภาษี</span>
                              </template>
                            </v-radio>
                          </v-col>
                          <v-col cols="12">
                            <v-radio value="radiotax-2"><template v-slot:label>
                                <span style="font-size: 14px;">ไม่รับใบกำกับภาษี</span>
                              </template>
                            </v-radio>
                          </v-col>
                        </v-radio-group>
                      </v-col>
                    </v-row>
                  </v-row>
                  <div v-if="radiostax !== 'radiotax-2'">
                    <v-spacer class="mt-2" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                    <div style="margin-top: 16px;" v-for="(item, index) in invoicedetail" :key="index">
                      <div v-if="item.default_invoice === 'Y'">
                        <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                        <span style="font-size: 14px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 14px; font-weight: 600; color: #27AB9C;">{{item.tax_type === 'Personal' ? 'บุคคลธรรมดา' : 'นิติบุคคล'}}</span>
                        <v-btn class="float-end" color="#27AB9C" text dark
                              style="font-size: 12px; font-weight: 500; margin-top: -5px;" @click="DialogTaxAddress = !DialogTaxAddress"><span
                                style="text-decoration-line: underline;">เปลี่ยนที่อยู่</span></v-btn>
                        <v-col class="ml-6 py-0 pt-2" v-if="item.tax_type !== 'Personal'">
                          <span style="font-size: 12px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                        </v-col>
                        <v-col class="ml-6 py-0">
                          <span style="font-size: 12px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                        </v-col>
                        <v-col class="ml-6 pt-1 pr-5" style="max-width: 260px;">
                          <span style="font-size: 12px;">ที่อยู่: <b>{{item.address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postal_code}}</b> </span>
                        </v-col>
                        </div>
                      </div>
                      <v-card v-if="invoicedetail.length === 0" class="mt-7" elevation="0"
                      style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="openModalTaxAddress()">
                      <v-card-text class="py-2">
                        <v-row class="my-2 px-3 ">
                          <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                          <span class="pl-2"
                            style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                    </div>
                </v-card>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="4" class="pb-6">
          <v-card class="v-Card">
            <v-container grid-list-xs class="pa-5">
              <v-row>
                <v-col cols="12" class="my-2">
                  <v-row dense>
                    <!-- <v-icon color="#27AB9C" class="pr-2">mdi-file-document-outline</v-icon> -->
                    <span
                      :style="MobileSize ? 'font-size: 18px;' : IpadSize ? 'font-size: 20px;' : 'font-size: 24px; font-weight: 700;'"><b>สรุปราคาสั่งซื้อสินค้า</b></span>
                  </v-row>
                </v-col>
                <v-col cols="6" sm="7" class="Textcard">
                  <span>ราคาสินค้า</span>
                </v-col>
                <v-col cols="6" sm="5" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_no_vat ? formatPrice(itemsCart.total_price_no_vat) : '0.00' }} บาท</span>
                </v-col>
                <!-- <v-col cols="6" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <span>คูปองส่วนลดร้านค้า</span>
                </v-col>
                <v-col cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip color="#FF9800" v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0"  @click="clickCoupon('', PointData, XBaht)" outlined class="pr-5"><span class="px-2">คูปองส่วนลด</span>
                    <v-icon>mdi-ticket-percent-outline</v-icon>
                  </v-chip>
                  <v-btn v-else small color="#27AB9C" @click="clickCoupon(CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id, PointData, XBaht)" text><span class="text-decoration-underline">เปลี่ยนคูปองส่วนลด</span>
                  </v-btn>
                </v-col>
                <v-col class="py-0" v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length !== 0" cols="6">
                </v-col>
                <v-col class="py-0" v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length !== 0" cols="6" align="right">
                  <v-chip class="mt-3" :color="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? '#FFCDD2' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                  :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                  <span v-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount'" class="px-2">ส่วนลด {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                  <span v-else-if="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping'" class="px-2">ส่วนลดค่าขนส่ง {{CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_type === 'baht' ? `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount} บาท` : `${CouponData.find(coupon => coupon.seller_shop_id !== -1).discount_amount}%`}}</span>
                  <span v-else class="px-2">แถมฟรี</span>
                    <v-icon small @click="closeCoupon()" :style="CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'discount' ? 'color: red;' : CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                  </v-chip>
                </v-col>
                <v-col v-if="itemsCart.seller_use_point === 'yes'" cols="6" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <span>ใช้งานแต้มส่วนลด</span>
                </v-col>
                <v-col v-if="itemsCart.seller_use_point === 'yes'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'" style="display: flex; justify-content: end;">
                  <v-switch false-value="no" true-value="yes" inset v-model="usePointOrNot" @click="closePoint()"></v-switch>
                </v-col>
                <v-col cols="6" v-if="usePointOrNot === 'yes'" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <span>แต้มส่วนลด</span>
                </v-col>
                <v-col cols="6" v-if="usePointOrNot === 'yes' && PointData === 0" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip class="mr-3" v-if="CouponData.filter(coupon => coupon.seller_shop_id !== -1).length !== 0" color="#FFC107" @click="clickPoint(CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id, PointData, XBaht, itemsCart.total_coupon_discount)" outlined>
                  <span class="px-2">แต้มส่วนลด</span>
                  </v-chip>
                  <v-chip class="mr-3" v-else color="#FFC107" @click="clickPoint('', PointData, XBaht, itemsCart.total_coupon_discount)" outlined>
                  <span class="px-2">แต้มส่วนลด</span>
                  </v-chip>
                </v-col>
                <v-col cols="6" v-if="usePointOrNot === 'yes' && PointData !== 0" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip class="mr-3" color="#FFECB4" style="color: #FFC107;">
                  <span class="px-2">แต้มที่ใช้ {{PointData / XBaht}} แต้ม</span>
                    <v-icon small @click="closePoint()" style="color: #FFC107;">mdi-close</v-icon>
                  </v-chip>
                </v-col> -->
                <v-col v-if="itemsCart.employee_inet && role.role === 'ext_buyer'" cols="6" class="Textcard">
                  <span>โค้ดส่วนลดสานสัมพันธ์</span>
                </v-col>
                <v-col v-if="itemsCart.employee_inet && role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip v-if="InetRelation.length === 0" color="#27AB9C" @click="clickCode()" outlined class="pr-5"><span class="px-2">โค้ดส่วนลด</span>
                    <v-icon>mdi-ticket-percent-outline</v-icon>
                  </v-chip>
                  <v-btn v-else small color="#27AB9C" @click="clickCode()" text><span class="text-decoration-underline">เปลี่ยนโค้ดส่วนลด</span></v-btn>
                </v-col>
                <v-col v-if="InetRelation.length !== 0" cols="12" class="pb-0">
                  <v-row class="pl-4 pr-3 pb-2 align-baseline">
                    <v-col cols="8" class="py-0">
                      <span style="font-size: 16px; font-weight: 700;">รายชื่อพนักงาน {{InetRelation.length}} รายชื่อ</span>
                    </v-col>
                    <v-col cols="auto" class="py-0">
                    </v-col>
                    <v-col cols="3" class="py-0 text-end">
                      <v-btn text color="red" class="text-decoration-underline" @click="clearUser()" >ล้างค่า</v-btn>
                      <!-- <span style="font-size: 16px; font-weight: 700;">รายชื่อ</span> -->
                    </v-col>
                  </v-row>
                  <v-card class="pa-2 py-4 custom-scroll" elevation="0" style="background: #FFFFFF; border-radius: 20px; border: 3px solid #27AB9C; max-height: 435px; overflow-y: auto; overflow-x: hidden;">
                    <v-row class="pb-1 pt-2"  v-for="(item, index) in InetRelation" :key="index">
                      <v-col cols="12" class="px-4 py-0">
                        <v-col cols="12" class="pa-0">
                          <span style="font-size: 16px; font-weight: 400;">{{index + 1}}. แผนก: <b>{{item.team}}</b></span>
                        </v-col>
                        <v-col cols="12" class="pa-0 pl-4">
                          <span style="font-size: 16px; font-weight: 400;">บริษัท: <b>{{item.company}}</b></span>
                        </v-col>
                        <v-col cols="12" class="pa-0 pl-4">
                          <span style="font-size: 16px; font-weight: 400;"><b>{{item.employee_one_id + ' ' + item.first_name_th + ' ' + item.last_name_th}} ({{ item.code }})</b></span>
                        </v-col>
                      </v-col>
                    </v-row>
                    <!-- <v-col style="text-align: center;" class="pb-0" v-if="!showAll && InetRelation.length > 5">
                      <v-btn color="#27AB9C" icon @click="ShowUsers()"><v-icon>mdi-chevron-down-circle-outline</v-icon></v-btn>
                    </v-col>
                    <v-col style="text-align: center;" class="pb-0" v-if="showAll && InetRelation.length > 5">
                      <v-btn color="#27AB9C" icon @click="HideUsers()"><v-icon>mdi-chevron-up-circle-outline</v-icon></v-btn>
                    </v-col> -->
                  </v-card>
                </v-col>
                <v-col v-if="role.role === 'ext_buyer'" cols="6" class="Textcard">
                  <span>โค้ดส่วนลดระบบ</span>
                </v-col>
                <v-col v-if="role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                  <v-chip v-if="CodePlatform.length === 0" color="#27AB9C" @click="clickSystemCode()" outlined class="pr-5"><span class="px-2">โค้ดส่วนลด</span>
                    <v-icon>mdi-ticket-percent-outline</v-icon>
                  </v-chip>
                  <v-btn v-else small color="#27AB9C" @click="clickSystemCode()" text><span class="text-decoration-underline">เปลี่ยนโค้ดส่วนลด</span></v-btn>
                </v-col>
                <v-col class="py-0" v-if="CodePlatform.length !== 0" cols="12" align="right">
                  <v-chip class="mt-3" :color="CodePlatform[0].coupon_type === 'discount' ? '#FFCDD2' : CodePlatform[0].coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                  :style="CodePlatform[0].coupon_type === 'discount' ? 'color: red;' : CodePlatform[0].coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                  <span v-if="CodePlatform[0].coupon_type === 'discount'" class="px-2">ส่วนลด {{CodePlatform[0].discount_type === 'baht' ? `${parseFloat(CodePlatform[0].discount_amount).toFixed(2)} บาท` : `${parseFloat(CodePlatform[0].discount_amount).toFixed(2)}%`}}</span>
                  <span v-else-if="CodePlatform[0].coupon_type === 'free_shipping'" class="px-2">ส่วนลดค่าขนส่ง {{CodePlatform[0].discount_type === 'baht' ? `${parseFloat(CodePlatform[0].discount_amount).toFixed(2)} บาท` : `${parseInt(CodePlatform[0].discount_amount)}%`}}</span>
                  <span v-else class="px-2">แถมฟรี</span>
                    <v-icon small @click="clearCodePlatform()" :style="CodePlatform[0].coupon_type === 'discount' ? 'color: red;' : CodePlatform[0].coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                  </v-chip>
                </v-col>
                <!-- <v-col cols="6" class="Textcard">
                  <span>ส่วนลดคูปองร้านค้า</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_coupon_discount ? formatPrice(itemsCart.total_coupon_discount) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ส่วนลดแต้ม</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.choose_list[0].total_point ? formatPrice(itemsCart.choose_list[0].total_point) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ส่วนลดคูปองระบบ</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_coupon_platform_discount ? formatPrice(itemsCart.total_coupon_platform_discount) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ราคาหลังหักส่วนลดคูปอง</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_after_all_discount ? formatPrice(itemsCart.total_price_after_all_discount) : '0.00' }} บาท</span>
                </v-col> -->
                <v-col v-if="role.role !== 'ext_buyer'" cols="6" class="Textcard">
                  <span v-if="role.role === 'purchaser' || role.role === 'sale_order'">ส่วนลดคู่ค้า</span>
                  <span v-else-if="role.role === 'sale_order_no_JV'">ส่วนลดลูกค้า</span>
                </v-col>
                <v-col v-if="role.role !== 'ext_buyer'" cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_b2b_discount ? formatPrice(itemsCart.total_b2b_discount) : '0.00' }} บาท</span>
                </v-col>
                <v-col v-if="role.role !== 'ext_buyer'" cols="6" class="Textcard">
                  <span v-if="role.role === 'purchaser' || role.role === 'sale_order'">ราคาหลังหักส่วนลดคู่ค้า</span>
                  <span v-else-if="role.role === 'sale_order_no_JV'">ราคาสินค้ารวมส่วนลด</span>
                </v-col>
                <v-col v-if="role.role !== 'ext_buyer'" cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_after_all_discount ? formatPrice(itemsCart.total_price_after_all_discount) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ภาษีมูลค่าเพิ่ม</span>
                  <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;"> (สินค้าราคารวมภาษี) </span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_include_vat ? formatPrice(itemsCart.total_include_vat) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ภาษีมูลค่าเพิ่ม</span>
                  <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;"> (สินค้าราคาไม่รวมภาษี) </span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_exclude_vat ? formatPrice(itemsCart.total_exclude_vat) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>รวมภาษีมูลค่าเพิ่ม</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_vat ? formatPrice(itemsCart.total_vat) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
                </v-col>
                <v-col cols="6" align="right" class="TextBaht">
                  <span>{{ itemsCart.total_price_vat ? formatPrice(itemsCart.total_price_vat) : '0.00' }} บาท</span>
                </v-col>
                <v-col cols="6" class="Textcard">
                  <span>ค่าจัดส่ง</span><br>
                  <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป </span>
                  <span style="font-size: 10px; font-weight: 400; color: #A1A1A1; line-height: 14px;">ขึ้นอยู่กับสินค้า/ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ</span>
                </v-col>
                <v-col v-if="parseInt(itemsCart.total_shipping) !== parseInt(itemsCart.shipping_price)" cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                  <span style="font-weight: 400; color: #BDBDBD;" class="text-decoration-line-through">{{ Number(itemsCart.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท' }}</span><br>
                  <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท'}}</span>
                </v-col>
                <v-col v-else cols="6" align="right" :class="MobileSize ? 'TextBahtMobileSize' : 'TextBaht'">
                  <span>{{ Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' บาท' }}</span><br>
                </v-col>
                <v-row v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                  <v-col cols="12" v-if="chooseitem.product_list.some(productitem => (role.role === 'ext_buyer') || (role.role !== 'ext_buyer' && itemsCart.is_JV === 'no') || (role.role !== 'ext_buyer' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer !== 'yes') || (role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && productitem.product_type === 'general'))">
                  <!-- <div v-if="chooseitem.product_list.some(productitem => (role.role === 'ext_buyer' && productitem.product_type === 'general' && radios !== 'radio-1' && itemsCart.shipping_method.length !== 0) || (role.role !== 'ext_buyer' && itemsCart.is_JV === 'no' && productitem.product_type === 'general' && radios !== 'radio-1' && itemsCart.shipping_method.length !== 0) || (role.role !== 'ext_buyer' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer !== 'yes' && productitem.product_type === 'general' && radios !== 'radio-1' && itemsCart.shipping_method.length !== 0) || (role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && productitem.product_type === 'general' && radios !== 'radio-1' && itemsCart.shipping_method.length !== 0))" :style="radioTransport === '' ? 'background-color: #fff1f4;' : ''"> -->
                  <div v-if="chooseitem.product_list.some(productitem => (role.role === 'ext_buyer' && productitem.product_type === 'general' && radios !== 'radio-1' && itemsCart.shipping_method.length !== 0))" :style="radioTransport === '' ? 'background-color: #fff1f4;' : ''">
                    <v-col cols="12" class="pt-0">
                      <v-spacer class="mb-3" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                      <span class="" :style="MobileSize ? 'font-size: 14px; color: #FAAD14;' : 'font-size: 16px; color: #FAAD14;'" color="#FAAD14">ขนส่ง</span>
                      <v-btn class="float-end" color="#27AB9C" text :disabled="btnEstimateCost === false" :class="btnEstimateCost === false ? '' : 'theme--dark'"
                        style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="EstimateCost()"><span
                        style="text-decoration-line: underline;">เลือกการจัดส่ง</span>
                      </v-btn>
                    </v-col>
                    <div v-if="radioTransport !== ''">
                      <v-col cols="12">
                        <v-img class="mt-n2 mr-2 float-left" src="@/assets/Transport.png" width="30" height="30" contain></v-img>
                        <span class="mt-4" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{nameTransport}}</span>
                        <span class="float-end mr-4" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{Number(costTransport).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}} บาท</span>
                        <!-- <span class="mt-4 pr-3" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{ Number(costTransport).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}  บาท</span> -->
                      </v-col>
                    </div>
                    <div v-if="radioTransport === ''">
                      <v-col cols="12" class="d-flex justify-end pt-0">
                        <!-- <v-img class="mt-n2 mr-2 float-left" src="@/assets/Transport.png" width="30" height="30" contain></v-img> -->
                        <span class="mt-2" :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: red; padding-right: 12px;' : 'font-size: 14px; font-weight: 400; color: red; padding-right: 16px;'">* คุณยังไม่ได้เลือกขนส่ง กรุณาเลือกขนส่ง</span>
                        <!-- <span class="mt-4 pr-3" :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 18px; font-weight: 600;'">{{ Number(costTransport).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}  บาท</span> -->
                      </v-col>
                    </div>
                    <v-spacer class="mx-3" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                  </div>
                  <v-row class="px-3">
                    <v-col v-if="role.role === 'ext_buyer'" cols="6" class="Textcard">
                      <span>โค้ดส่วนลดขนส่งระบบ</span>
                    </v-col>
                    <v-col v-if="role.role === 'ext_buyer'" cols="6" align="right" :class="MobileSize ? 'TextcardMobileSize' : 'Textcard'">
                      <v-chip v-if="CodePlatformShipping.length === 0" color="#27AB9C" @click="clickSystemCodeShipping()" outlined class="pr-5"><span class="px-2">โค้ดส่วนลด</span>
                        <v-icon>mdi-ticket-percent-outline</v-icon>
                      </v-chip>
                      <v-btn v-else small color="#27AB9C" @click="clickSystemCodeShipping()" text><span class="text-decoration-underline">เปลี่ยนโค้ดส่วนลด</span></v-btn>
                    </v-col>
                    <v-col class="py-0" v-if="CodePlatformShipping.length !== 0" cols="12" align="right">
                      <v-chip class="mt-3" :color="CodePlatformShipping[0].coupon_type === 'discount' ? '#FFCDD2' : CodePlatformShipping[0].coupon_type === 'free_shipping' ? '#FFEACC' : '#FFECB4'"
                      :style="CodePlatformShipping[0].coupon_type === 'discount' ? 'color: red;' : CodePlatformShipping[0].coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">
                      <span v-if="CodePlatformShipping[0].coupon_type === 'discount'" class="px-2">ส่วนลด {{CodePlatformShipping[0].discount_type === 'baht' ? `${parseInt(CodePlatformShipping[0].discount_amount)} บาท` : `${parseInt(CodePlatformShipping[0].discount_amount)}%`}}</span>
                      <span v-else-if="CodePlatformShipping[0].coupon_type === 'free_shipping'" class="px-2">ส่วนลดค่าขนส่ง {{CodePlatformShipping[0].discount_type === 'baht' ? `${parseInt(CodePlatformShipping[0].discount_amount)} บาท` : `${parseInt(CodePlatformShipping[0].discount_amount)}%`}}</span>
                      <span v-else class="px-2">แถมฟรี</span>
                        <v-icon small @click="clearCodePlatformShipping()" :style="CodePlatformShipping[0].coupon_type === 'discount' ? 'color: red;' : CodePlatformShipping[0].coupon_type === 'free_shipping' ? 'color: #FF9800;' : 'color: #FFC107;'">mdi-close</v-icon>
                      </v-chip>
                    </v-col>
                  </v-row>
                  <div v-if="role.role === 'ext_buyer'">
                    <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex" >
                      <v-radio-group v-model="radioPayment" row class="ma-0 pa-0 ml-3" :disabled="chooseitem.product_list.some(productitem => (productitem.product_type === 'general' && radioTransport === '' && itemsCart.shipping_method.length !== 0 && radios === 'radio-2'))">
                        <v-col :class="MobileSize ? 'px-1' : 'px-0 pb-0'" :cols="MobileSize ? 12 : 12">
                          <span class="pl-0 pb-0" :style="MobileSize ? 'font-size: 14px; color: #FAAD14;' : 'font-size: 16px; color: #FAAD14;'" color="#FAAD14">วิธีการชำระเงิน</span>
                        </v-col>
                        <v-col cols="12" v-if="itemsCart.payment_method[0] === 'qrcode' || itemsCart.payment_method[1] === 'qrcode' || itemsCart.payment_method[2] === 'qrcode'" :class="MobileSize ? 'pt-0' : 'px-0'" >
                          <v-radio value="radio-qr" @click="setRadioCreditTermNo()"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">QR Code</span>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" v-if="itemsCart.payment_method[0] === 'creditcard' || itemsCart.payment_method[1] === 'creditcard' || itemsCart.payment_method[2] === 'creditcard'" :class="MobileSize ? 'pt-0' : 'px-0'">
                          <v-radio value="radio-credit" @click="setRadioCreditTermNo()"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card / Debit Card</span>
                            </template>
                          </v-radio>
                        </v-col>
                        <!-- <v-col cols="12" v-if="itemsCart.payment_method[0] === 'installment' || itemsCart.payment_method[1] === 'installment' || itemsCart.payment_method[2] === 'installment'" :class="MobileSize ? 'pt-0' : 'px-0'">
                          <v-radio value="radio-installment"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card แบบผ่อนชำระ</span>
                            </template>
                          </v-radio>
                        </v-col> -->
                        <v-col cols="12" v-if="(itemsCart.bank_code !== null || itemsCart.bank_code !== '' || itemsCart.bank_code !== undefined) && roleCheck !== 'ext_buyer'" :class="MobileSize ? 'pt-0' : 'px-0'">
                          <v-radio value="radio-EWTH"><template v-slot:label>
                              <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">ชำระเงินผ่าน e-WHT</span>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                    </div>
                    <div v-if="radioPayment === 'radio-installment'" class="mt-0 mb-4 mx-3">
                      <v-row align="center">
                        <v-col :cols="MobileSize ? 4 : 4" class="pt-0">
                          <!-- <span style="font-size: 16px;">ระยะเวลาผ่อนชำระ</span> -->
                          <v-img src="@/assets/ktc_logo.png" width="130" height="100" contain style="border-radius: 8px;"></v-img>
                        </v-col>
                        <v-col :cols="MobileSize ? 8 : 8" class="pb-0">
                          <span style="font-size: 16px; font-weight: 600;">กรุงไทย (KTC)</span>
                          <v-select class="mt-2" outlined dense label="เลือกระยะเวลาผ่อนชำระ" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" style="border-radius: 8px;">
                            <template v-slot:append>
                              <v-icon>mdi-chevron-down</v-icon>
                            </template>
                            <template v-slot:no-data>
                              <v-list-item>
                                <v-list-item-content class="text-center">
                                  <v-list-item-title>ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง <span style="color: #27AB9C;">'ขั้นต่ำ'</span> ที่กำหนดไว้</v-list-item-title>
                                </v-list-item-content>
                              </v-list-item>
                            </template>
                          </v-select>
                        </v-col>
                      </v-row>
                    </div>
                    <v-spacer class="mx-3" style="border-top: 2px solid #EBEBEB;"></v-spacer>
                  </div>
                </v-col>
              </v-row>
              <!-- <v-col cols="8" class="py-0">
                  <span>ค่าจัดส่ง</span>
                </v-col>
                <v-col cols="4" class="py-0" align="right">
                  <span><b>{{ itemsCart.total_shipping ? Number(itemsCart.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
                </v-col> -->
                <!-- <v-col cols="8" class="py-0"> -->
                <!-- <span style="font-size: 10px" v-if="Number(itemsCart.total_shipping) === 0 &&
                  (address_data.province !== 'กรุงเทพมหานคร' && address_data.province !== 'นครปฐม' && address_data.province !== 'นนทบุรี' && address_data.province !== 'ปทุมธานี' && address_data.province !== 'สมุทรปราการ' && address_data.province !== 'สมุทรสาคร')">ค่าจัดส่งขึ้นอยู่เงื่อนที่ทางบริษัทเป็นผู้กำหนด</span>
                  <span style="font-size: 10px" v-else-if="Number(itemsCart.total_shipping) === 0 &&
                  (address_data.province === 'กรุงเทพมหานคร' || address_data.province === 'นครปฐม' || address_data.province === 'นนทบุรี' || address_data.province === 'ปทุมธานี' || address_data.province === 'สมุทรปราการ' || address_data.province === 'สมุทรสาคร')">ฟรีค่าส่งในกรุงเทพฯ และปริมณฑล</span> -->
                <!-- <span style="font-size: 10px; color: #8C8C8C;" class="mb-0">ราคานี้เป็นมาตรฐาน -
                    ราคาอาจแตกต่างกันไปขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่ จัดส่งจะติดต่อคุณ</span>
                </v-col> -->
                <v-col cols="12" style="margin-top: 100px;" class="py-0" v-if="itemsCart.seller_use_point === 'yes'">
                  <v-row dense class="pl-1 d-flex">
                    <span style="color: #faad14;" :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'"
                      class="mr-auto align-self-center"><b>แต้มสะสมที่ได้ครั้งนี้</b></span>
                    <span :style="MobileSize ? 'font-size: 20px; font-weight: 700; color: #27AB9C;' : 'font-size: 24px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1">
                      <b>{{Number(parseInt(itemsCart.total_point_receive)).toLocaleString(undefined, {})}}
                      <span style="color: #faad14;"
                      :style="MobileSize ? 'font-size: 14px; font-weight: 700; color: #faad14' : 'font-size: 18px; font-weight: 700; color: #faad14'" class="ml-2">แต้ม</span></b></span>
                    <!-- <span class="" style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    <span class="ml-auto pt-2" style="font-weight: 400; font-size: 10px; line-height: 16px; color: #606060; letter-spacing: -0.2px;" v-if="itemProduct.total_sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.total_sold) }} ชิ้น</span> -->
                  </v-row>
                </v-col>
                <!-- <v-col cols="12" class="py-0" :style="role.role === 'purchaser' ? 'margin-top: 100px;' : ''"> -->
                <v-col cols="12" class="py-0"  :style="itemsCart.seller_use_point !== 'yes' ? 'margin-top: 100px;' : ''">
                  <v-row dense class="pl-1 d-flex">
                    <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'"
                      class="mr-auto align-self-center"><b>ราคารวมทั้งหมด</b></span>
                    <span :style="MobileSize ? 'font-size: 20px; font-weight: 700; color: #27AB9C;' : 'font-size: 28px; font-weight: 700; color: #27AB9C;'" class="ml-auto pt-0 pr-1"><b>{{
                      itemsCart.net_price  ? formatPrice(itemsCart.net_price) : '0.00' }}<span v-if="choose_list === 'recurring'"
                      :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333' : 'font-size: 20px; font-weight: 700; color: #333333'" class="ml-2">บาท/เดือน</span><span
                          v-else :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333' : 'font-size: 20px; font-weight: 700; color: #333333'"
                          class="ml-2">บาท</span></b></span>
                    <!-- <span class="" style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    <span class="ml-auto pt-2" style="font-weight: 400; font-size: 10px; line-height: 16px; color: #606060; letter-spacing: -0.2px;" v-if="itemProduct.total_sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.total_sold) }} ชิ้น</span> -->
                  </v-row>
                </v-col>
                <v-col cols="12" class="pb-0" v-if="noShipping === true">
                  <v-row dense class="pl-1 d-flex">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 700;' : 'font-size: 14px; font-weight: 700;'"
                      class="mr-auto align-self-center" style="color: red;">*ส่วนลดค่าจัดส่งจะไม่ถูกใช้งาน เนื่องจากไม่มีค่าจัดส่ง</span>
                  </v-row>
                </v-col>
                <!-- <v-col cols="7" md="8" :class="MobileSize ? 'pr-0' : ''">
                  <span class="totalPriceFont"><b>ราคารวมทั้งหมด</b></span>
                </v-col>
                <v-col cols="5" md="4" align="right" :class="MobileSize ? 'pl-0' : ''">
                  <span class="totalPriceFont"><b>{{ itemsCart.net_price ? Number(itemsCart.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }} <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span></b></span>
                </v-col> -->
                <v-col cols="12" align="right" class="mt-2 mb-2">
                  <!-- ชำระเงิน -->
                  <!-- <v-btn style="border-radius: 40px;" class="white--text" block color="#27AB9C" @click="confirmCreateOrderMobile('payment')"
                    :disabled="checkSelectPr || contractStartDate === '' || contractEndDate === '' || selectBudget === '' || selectCutBudget === '' || selectTypeDoc === ''">
                    <span style="font-size: 16px; font-style: normal; font-weight: bold;">ยืนยันการขอซื้อ</span>
                  </v-btn> -->
                  <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                    <!-- <v-row v-if="chooseitem.product_list.some(productitem => (productitem.product_type !== 'general' && itemsCart.is_JV === 'no') -->
                    <v-btn v-if="role.role === 'ext_buyer' && radios === 'radio-2'" style="border-radius: 40px; height: 40px;" class="white--text"
                    block color="#27AB9C" @click="Confirm()" :disabled="radioPayment === '' || (radioCreditTerm === 'No' && radioPayment === 'radio-installment')">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role === 'ext_buyer' && radios === 'radio-1'" style="border-radius: 40px; height: 40px;" class="white--text"
                    block color="#27AB9C" @click="Confirm()" :disabled="radioPayment === '' || dates === '' || timeselect === '' || timeselecttoday === '' || timeselect === null || timeselecttoday === null || (radioCreditTerm === 'No' && radioPayment === 'radio-installment')">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="(role.role !== 'ext_buyer' && (itemsCart.is_JV !== 'yes' || itemsCart.is_JV_buyer !== 'yes') && (itemsCart.choose_list[0].pay_type === 'recurring' || itemsCart.choose_list[0].pay_type === 'onetime'))" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="(!chooseitem.product_list.some(p => p.product_type === 'general') && (contractStartDate === '' || contractEndDate === '')) || selectinstallment === null || selectedCreditTerm === null || hasInstallmentError || remainingAmount !== '0.00' || (discountBahtB2B && B2BDiscountBaht === '') || (discountPercentB2B && B2BDiscountPercent === '')">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role !== 'ext_buyer' && radios === 'radio-1' && (itemsCart.is_JV !== 'yes' || itemsCart.is_JV_buyer !== 'yes') && (itemsCart.choose_list[0].pay_type === 'recurring' || itemsCart.choose_list[0].pay_type === 'onetime')" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="dates === '' || timeselect === '' || timeselecttoday === '' || timeselect === null || timeselecttoday === null || (!chooseitem.product_list.some(p => p.product_type === 'general') && (contractStartDate === '' || contractEndDate === '')) || selectinstallment === null || selectedCreditTerm === null || hasInstallmentError || remainingAmount !== '0.00' || (discountBahtB2B && B2BDiscountBaht === '') || (discountPercentB2B && B2BDiscountPercent === '')">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role !== 'ext_buyer' && radios === 'radio-1' && itemsCart.choose_list[0].pay_type !== 'recurring'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="dates === '' || timeselect === '' || timeselecttoday === '' || timeselect === null || timeselecttoday === null">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="(radios === 'radio-1' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes') || (radios === 'radio-1' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no')" style="border-radius: 40px; height: 40px;" class="white--text"
                    block color="#27AB9C" @click="Confirm()" :disabled="dates === '' || timeselect === '' || timeselecttoday === '' || timeselect === null || timeselecttoday === null">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role !== 'ext_buyer' && itemsCart.is_JV === 'no' && itemsCart.choose_list[0].pay_type === 'onetime'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="(role.role !== 'ext_buyer' && itemsCart.is_JV === 'no' && itemsCart.choose_list[0].pay_type !== 'general')" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="contractStartDate === '' || contractEndDate === ''">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role !== 'ext_buyer' && itemsCart.is_JV === 'no' && itemsCart.choose_list[0].pay_type === 'general'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && itemsCart.choose_list[0].pay_type === 'general')" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && itemsCart.choose_list[0].pay_type === 'onetime')" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && itemsCart.choose_list[0].pay_type === 'recurring')" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="checkSelectPr">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role !== 'ext_buyer' && itemsCart.is_JV === 'yes' && itemsCart.choose_list[0].pay_type === 'general'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else-if="role.role !== 'ext_buyer' && itemsCart.is_JV !== 'no' && itemsCart.choose_list[0].pay_type !== 'general' && itemsCart.is_JV_buyer !== 'yes'" style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="contractStartDate === '' || contractEndDate === ''">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  <v-btn v-else style="border-radius: 40px; height: 40px;" class="white--text" block color="#27AB9C"
                    @click="Confirm()"
                    :disabled="checkSelectPr || contractStartDate === '' || contractEndDate === '' || selectBudget === '' || selectCutBudget === '' || selectTypeDoc === '' || Name_SupplyBoard1 === '' || Name_SupplyBoard2 === '' || buyer_name === '' || Name_Buyer === '' || Name_Audit1 === '' || Name_Audit2 === ''">
                    <span :style="MobileSize ? 'font-size: 16px; font-style: normal; font-weight: 700;' : 'font-size: 18px; font-style: normal; font-weight: 500;'">ยืนยันการสั่งซื้อ</span>
                  </v-btn>
                  </div>
                  <!-- ชำระเงินสด -->
                  <v-btn v-if="selectTypeAddress === 'Shop' && checkOwnShop === 'Y'" class="mt-4" outlined block
                    color="#27AB9C" @click="confirmCreateOrderMobile('cashPayment')"
                    :disabled="(taxRoles === 'Personal' || taxRoles === 'Business') && taxAddress === '' ? true : disableButtonPay ? true : false">
                    <span style="font-size: 16px; font-style: normal; font-weight: bold;">ชำระเงินสด</span>
                  </v-btn>
                  <!-- ชำระเงินแบบเครดิตเทอม -->
                  <!-- <v-btn v-if="role.role === 'purchaser'" class="mt-4" outlined block color="#27AB9C"
                    @click="confirmCreateOrderMobile('creditTerm')"
                    :disabled="(taxRoles === 'Personal' || taxRoles === 'Business') && taxAddress === '' ? true : false">
                    <span style="font-size: 16px; font-style: normal; font-weight: bold;">ชำระเงินแบบเครดิตเทอม</span>
                  </v-btn> -->
                  <!-- <v-btn rounded block color="#00B500" dark @click="confirmCreateOrderMobile()"><b>ชำระเงิน</b></v-btn> -->
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="typeButton === 'confirm' ? CancelAwaitConfirm() : dialogAwaitConfirm = !dialogAwaitConfirm">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ typeButton
              === 'confirm' ? 'ยืนยันการสั่งซื้อ' : 'ลบที่อยู่จัดส่ง' }}</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">คุณต้องการทำรายการนี้ ใช่
                หรือ ไม่</span>
              <span v-if="radiostax === 'radiotax-2' && itemsCart.isEtax === 'yes'" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;"><br><br><span style="color: red;">* </span>คุณไม่ต้องการรับใบกำกับภาษี ใช่
                หรือ ไม่</span>
            </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4"
                @click="CancelAwaitConfirm()">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                @click="typeButton === 'confirm' && radioPayment === '' ? Test('payment')
                  : typeButton === 'confirm' && radioPayment === 'radio-qr' ? GetQRCode('cashPayment') : typeButton === 'confirm' && (radioPayment === 'radio-credit' || radioPayment === 'radio-installment') ? GetCC('cashPayment')
                  : typeButton === 'confirm' && radioPayment === 'radio-EWTH' ? GetEWTH('cashPayment') : typeButton === 'deleteCompany' ? deleteCompanyAddress() : deleteAddress()">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense>
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="CancelAwaitConfirm()">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }"
                @click="typeButton === 'confirm' && radioPayment === '' ? Test('payment')
                  : typeButton === 'confirm' && radioPayment === 'radio-qr' ? GetQRCode('cashPayment') : typeButton === 'confirm' && (radioPayment === 'radio-credit' || radioPayment === 'radio-installment') ? GetCC('cashPayment')
                  : typeButton === 'confirm' && radioPayment === 'radio-EWTH' ? GetEWTH('cashPayment') : typeButton === 'deleteCompany' ? deleteCompanyAddress() : deleteAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon @click="typeButton === 'confirm' ? goHomePage() : typeButton === 'confirm' && radioPayment === 'radio-credit' ? goHomePage() : closeModalSuccess()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ typeButton
              === 'confirm' ? 'ยืนยันการสั่งซื้อเสร็จสิ้น' : 'ลบที่อยู่จัดส่งเสร็จสิ้น' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ typeButton === 'confirm'
              ? 'คุณได้ทำการสั่งซื้อสินค้าเรียบร้อย' : 'คุณได้ทำการลบที่อยู่จัดส่งเรียบร้อย' }}</span><br />
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;"
              v-if="typeButton === 'confirm' && radioPayment !== 'radio-credit'">กรุณารอผู้ขายอนุมัติการสั่งซื้อ</span>
          </v-card-text>
          <v-card-text :class="MobileSize ? 'px-0 pb-2' : ''">
            <v-row dense justify="center">
              <div v-for="(chooseitem, chooseindex) in itemsCart.choose_list" :key="chooseindex">
                <v-btn :width="MobileSize ? '165' : ''" :style="{ flex: MobileSize ? '0 0 auto' : '1 1 auto' }" height="38" outlined rounded color="#27AB9C" :class="MobileSize ? 'mr-2': 'mr-4'" v-if="(typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes') || (typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no') || (typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'no')"
                @click="GoToOrderCompany()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="(typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'yes') || (typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'no') || (typeButton === 'confirm' && radioPayment === '' && itemsCart.is_JV === 'no' && itemsCart.is_JV_buyer === 'no')"
                @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                <v-btn :width="MobileSize ? '165' : ''" :style="{ flex: MobileSize ? '0 0 auto' : '1 1 auto' }" height="38" outlined rounded color="#27AB9C" :class="MobileSize ? 'mr-2': 'mr-4'" v-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="GoToOrderCompany()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="chooseitem.product_list.some(productitem => productitem.product_type === 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                <v-btn :width="MobileSize ? '165' : ''" :style="{ flex: MobileSize ? '0 0 auto' : '1 1 auto' }" height="38" outlined rounded color="#27AB9C" :class="MobileSize ? 'mr-2': 'mr-4'" v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="goToQUCompany()">ไปยังหน้าใบเสนอราคา</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="chooseitem.product_list.some(productitem => productitem.product_type !== 'general' && itemsCart.is_JV === 'yes' && itemsCart.is_JV_buyer === 'yes' && typeButton === 'confirm' && radioPayment === '')"
                @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" outlined rounded color="#27AB9C" class="mr-4" v-if="typeButton === 'confirm' && radioPayment !== 'radio-credit' && radioPayment === 'radio-qr'"
                @click="goPoBuyerProfilePage()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" class="white--text" rounded color="#27AB9C" v-if="typeButton === 'confirm' && radioPayment !== 'radio-credit' && radioPayment === 'radio-qr'"
                @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" outlined rounded color="#27AB9C" class="mr-4" v-if="typeButton === 'confirm' && radioPayment === 'radio-credit' && radioPayment !== 'radio-credit'"
                  @click="goPoBuyerProfilePage()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
                <v-btn :style="{ flex: '1 1 auto' }" height="38" rounded color="#27AB9C" class="white--text" v-if="typeButton === 'confirm' && radioPayment === 'radio-credit' && radioPayment !== 'radio-credit'"
                  @click="goHomePage()">ไปยังหน้าหลัก</v-btn>
                <v-btn :width="!MobileSize ? '156' : ''" :style="{ flex: !MobileSize ? '' : '1' }" height="38" class="white--text mx-2" rounded color="#27AB9C" v-if="typeButton !== 'confirm'"
                  @click="closeModalSuccess()">ตกลง</v-btn>
              </div>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Dialog เปลี่ยนที่อยู่ -->
    <v-dialog v-model="DialogAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เปลี่ยนที่อยู่ในการจัดส่งสินค้า</b></span>
              </v-col>
              <v-btn fab small @click="cancelChangeAddrss()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ที่อยู่ในการจัดส่งสินค้า</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in AddressNew"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultAdress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="7">
                        <span style="font-size: 16px; font-weight: 600;">{{ item.first_name }} {{ item.last_name }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn :disabled="AddressNew.length === 1 || item.default_address === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                      <v-col class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                          item.province }} {{ item.zip_code }}</span><br>
                          <span v-if="item.note_address" style="font-size: 16px; color: #333333;">หมายเหตุ : {{ item.note_address }}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in AddressNew"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col  class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_address" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_address === 'Y'" @click="setDefaultAdress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn :disabled="AddressNew.length === 1 || item.default_address === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                    <v-col class="pt-2" cols="12">
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'" >{{ item.first_name }} {{ item.last_name }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'">{{ item.phone }}</span>
                      </v-col>
                    <v-col class="pb-2 px-2 pt-0">
                      <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                        item.province }} {{ item.zip_code }}</span><br>
                        <span v-if="item.note_address" style="font-size: 12px; color: #333333;">หมายเหตุ : {{ item.note_address }}</span>
                    </v-col>
                  </v-row>
                </v-card>
                <div v-if="AddressNew.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ไม่มีที่อยู่ในการจัดส่งสินค้า</span>
                  </v-col>
                </div>
                <v-card v-if="AddressNew.length !== 10" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="userdetail.length === 1 && (userdetail[0].zip_code === '' && userdetail[0].zip_code === null) ? editAddress(userdetail[0]) : addAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <!-- Dialog เปลี่ยนที่อยู่ Company -->
    <v-dialog v-model="DialogCompanyAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เปลี่ยนที่อยู่ในการจัดส่งสินค้า</b></span>
              </v-col>
              <v-btn fab small @click="cancelChangeAddrss()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ที่อยู่ในการจัดส่งสินค้า/ที่อยู่ใบเสนอราคา</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in comAddress"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="item.default" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default === 'Y'" @click="setDefaultCompanyAddress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="7">
                        <span style="font-size: 16px; font-weight: 600;">{{ item.name_th }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600;">{{ item.phone }}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn v-if="index !== 0" :disabled="comAddress.length === 1 || item.default === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="pt-0 pl-0 pb-0 mt-1">
                        <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษี: <b>{{ item.tax_id }}</b></span>
                      </v-col>
                      <v-col class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                          item.province }} {{ item.zip_code }}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in comAddress"
                  :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col  class="align-self-center mt-2">
                      <v-radio-group v-model="item.default" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default === 'Y'" @click="setDefaultCompanyAddress(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="editCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn v-if="index !== 0" :disabled="comAddress.length === 1 || item.default === 'Y'" color="red" text
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;"
                            @click="DeleteCompanyAddress(item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                    <v-col class="pt-2" cols="12">
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'" >{{ item.name_th }}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span :style="MobileSize ? 'font-size: 14px; font-weight: 600;' : 'font-size: 16px; font-weight: 600;'">{{ item.phone }}</span>
                      </v-col>
                      <v-col cols="12" class="pt-0 pl-2 pb-0 mt-1">
                        <span style="font-size: 12px;">เลขประจำตัวผู้เสียภาษี: <b>{{ item.tax_id }}</b></span>
                      </v-col>
                    <v-col class="pb-2 px-2 pt-0">
                      <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{
                        item.province }} {{ item.zip_code }}</span>
                    </v-col>
                  </v-row>
                </v-card>
                <div v-if="comAddress.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ไม่มีที่อยู่ในการจัดส่งสินค้า</span>
                  </v-col>
                </div>
                <v-card v-if="comAddress.length !== 10" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="addCompanyAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <!-- Dialog เลือกขนส่ง -->
    <v-dialog v-model="DialogTransport" :width="MobileSize ? '100%' : '600'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 600px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เลือกขนส่ง</b></span>
              </v-col>
              <v-btn fab small @click="DialogTransport = !DialogTransport" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '459px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card :class="MobileSize ? '' : 'custom-scroll'" elevation="0" width="100%" height="100%" :style="MobileSize ? '' : 'max-height: 550px;'" style="background: #FFFFFF; border-radius: 20px; overflow-y: auto; overflow-x: hidden;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/Transport.png" width="30" height="30" contain></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ขนส่งในการจัดส่งสินค้า</span>
                <div v-if="!MobileSize">
                  <v-card class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in estimateCostData"
                    :key="index"
                    style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                    <v-row>
                      <v-col cols="1" class="align-self-center mt-2">
                        <v-radio-group v-model="radioTransport" class="mt-0 pt-0">
                          <v-radio color="#27AB9C" :value="item.tpl_name" @click="selectTransport(item)" style="color: #333333">
                          </v-radio>
                        </v-radio-group>
                      </v-col>
                      <!-- <v-img v-if="item.businessType === 'THAILAND_POST'" class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/THAILAND_POST.jpg" contain max-width="125" max-height="125"></v-img>
                      <v-img v-else class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/Flash-express.jpg" contain max-width="125" max-height="125"></v-img> -->
                      <!-- รูปภาพขนส่ง -->
                      <v-col class="pa-0" cols="3">
                      <v-img v-if="item.media_path !== ''" class="rounded-circle ma-4" style="border: 1px solid" :src="item.media_path" contain max-width="125" max-height="125"></v-img>
                      <v-img v-else src="@/assets/NoImage.png" class="rounded-circle ma-4" style="border: 1px solid" contain max-width="125" max-height="125"></v-img>
                      </v-col>
                      <v-col  cols="8" class="align-self-center text-end pl-0 py-0">
                        <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'">{{item.tpl_name}}</span><br>
                        <span :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #27AB9C;' : 'font-size: 18px; font-weight: 700; color: #27AB9C;'">ค่าส่งเริ่มต้น {{Math.ceil(item.netEstimatePrice)}} บาท</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </div>
                <div v-else>
                  <v-card class="mt-7 pa-2" elevation="0" width="100%" height="100%" v-for="(item, index) in estimateCostData"
                    :key="index"
                    style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                    <v-row>
                      <v-col cols="1" class="align-self-center mt-2">
                        <v-radio-group v-model="radioTransport" class="mt-0 pt-0">
                          <v-radio color="#27AB9C" :value="item.tpl_name" @click="selectTransport(item)" style="color: #333333">
                          </v-radio>
                        </v-radio-group>
                      </v-col>
                      <v-col cols="4" class="pr-0">
                        <!-- <v-img v-if="item.businessType !== 'SCG'" class="rounded-pill ma-0 ml-2" src="@/assets/Flash-express.jpg" max-width="60" max-height="60"></v-img> -->
                        <!-- <v-img v-if="item.businessType === 'THAILAND_POST'" class="rounded-pill ma-0 ml-2" style="border: 1px solid" src="@/assets/THAILAND_POST.jpg" max-width="60" max-height="60"></v-img>
                        <v-img v-else class="rounded-pill ma-0 ml-2" style="border: 1px solid" src="@/assets/Flash-express.jpg" max-width="60" max-height="60"></v-img> -->
                        <v-img v-if="item.media_path !== ''" class="rounded-circle ma-4" style="border: 1px solid" :src="item.media_path" contain max-width="60" max-height="60"></v-img>
                        <v-img v-else class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/NoImage.png" contain max-width="60" max-height="60"></v-img>
                      </v-col>
                      <v-specer class="mx-auto"></v-specer>
                      <v-col cols="auto" class="text-end align-self-center pl-0">
                        <span :style="MobileSize ? 'font-size: 12px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'">{{item.tpl_name}}</span><br>
                        <span :style="MobileSize ? 'font-size: 12px; font-weight: 700; color: #27AB9C;' : 'font-size: 18px; font-weight: 700; color: #27AB9C;'">ค่าส่งเริ่มต้น {{Math.ceil(item.netEstimatePrice)}} บาท</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Dialog เปลี่ยนที่อยู่ Tax -->
    <v-dialog v-model="DialogTaxAddress" :width="MobileSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;" >
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เปลี่ยนที่อยู่ในการออกใบกำกับภาษี</b></span>
              </v-col>
              <v-btn @click="DialogTaxAddress = !DialogTaxAddress" fab small icon  :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div  :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ที่อยู่ใบกำกับภาษี</span>
                <v-card v-show="!MobileSize" class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in invoicedetail" :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col cols="1" class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_invoice" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_invoice === 'Y'"  @click="setDefaultInvoice(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="col-11 row">
                      <v-col class="pl-0 pb-0" cols="6">
                        <span style="font-size: 16px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 16px; font-weight: 600; color: #27AB9C;">{{item.tax_type === 'Personal' ? 'บุคคลธรรมดา' : 'นิติบุคคล'}}</span>
                      </v-col>
                      <v-col class="pl-0 pb-0" cols="6">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openModalEditTaxAddress(item)"
                            ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn color="red" text :disabled="invoicedetail.length === 1 || item.default_invoice === 'Y'"
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openDeleteInvoice(item)"
                            ><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                      <v-col class="py-0 pl-0" v-if="item.tax_type !== 'Personal'">
                        <span style="font-size: 16px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                      </v-col>
                      <v-col cols=12 class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                      </v-col>
                      <v-col cols="12" class="pb-2 pl-0 pa-3 pt-0">
                        <span style="font-size: 16px;">ที่อยู่: {{item.address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postal_code}}</span>
                      </v-col>
                    </div>
                  </v-row>
                </v-card>
                <v-card v-show="MobileSize" class="mt-4 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in invoicedetail" :key="index"
                  style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                  <v-row>
                    <v-col class="align-self-center mt-2">
                      <v-radio-group v-model="item.default_invoice" class="mt-0 pt-0">
                        <v-radio color="#27AB9C" value="Y" :disabled="item.default_invoice === 'Y'" @click="setDefaultInvoice(item)" style="color: #333333">
                        </v-radio>
                      </v-radio-group>
                    </v-col>
                    <div class="row">
                      <v-col class="pl-0 pb-0 mt-5">
                        <v-row no-gutters justify="end">
                          <v-btn color="#27AB9C" text dark style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openModalEditTaxAddress(item)"
                            ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon><span
                              style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                          <v-btn color="red" text :disabled="invoicedetail.length === 1 || item.default_invoice === 'Y'"
                            style="font-size: 14px; font-weight: 500; margin-top: -5px;" @click="openDeleteInvoice(item)"
                            ><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon><span
                              style="text-decoration-line: underline;">ลบ</span></v-btn>
                        </v-row>
                      </v-col>
                    </div>
                      <v-col class="pt-2" cols="12">
                        <span style="font-size: 14px; font-weight: 600;">{{item.name}}</span>
                        <span class="px-1" style="color: #EBEBEB;">|</span>
                        <span style="font-size: 14px; font-weight: 600; color: #27AB9C;">{{item.tax_type === 'Personal' ? 'บุคคลธรรมดา' : 'นิติบุคคล'}}</span>
                      </v-col>
                      <v-col class="py-0 pl-2" v-if="item.tax_type !== 'Personal'">
                        <span style="font-size: 14px;">รหัสสาขา: <b>{{item.branch_id}}</b></span>
                      </v-col>
                      <v-col cols=12 class="pb-2 px-2 pt-0">
                        <span style="font-size: 14px;">เลขประจำตัวผู้เสียภาษี: <b>{{item.tax_id}}</b></span>
                      </v-col>
                      <v-col cols="12" class="pb-2 px-2 pt-0">
                        <span style="font-size: 12px;">ที่อยู่: {{item.address}} {{item.sub_district}} {{item.district}} {{item.province}} {{item.postal_code}}</span>
                      </v-col>
                  </v-row>
                </v-card>
                <div v-if="invoicedetail.length === 0" style="text-align: -webkit-center;">
                  <v-img src="@/assets/emptypo.png" width="500" height="100%"></v-img>
                  <v-col class="pt-10 pb-0">
                    <span  :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'">ไม่มีที่อยู่ในการออกใบกำกับภาษี</span>
                  </v-col>
                </div>
                <v-card v-if="invoicedetail.length !== 5" class="mt-7" elevation="0"
                  style=" border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);" @click="openModalTaxAddress()">
                  <v-card-text class="py-2">
                    <v-row class="my-2 px-3 ">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br />
                      <span class="pl-2"
                        style="font-weight: 500; font-size: 14px; color: #1B5DD6;">เพิ่มที่อยู่ใหม่</span>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelChangeAddrss()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text"
            @click="confirmAddress()">ยืนยัน</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <!-- Dialog QR -->
    <v-dialog v-model="DialogQR" :width="MobileSize ? '100%' : '640'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
        <v-card-text class="px-0">
          <div  :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
            style="position: absolute; height: 120px; ">
            <v-row style="height: 120px; ">
              <v-col style="text-align: center;" class="pt-6">
                <span style="margin-left: 0px"
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{ QRShop === true ? 'สแกน QR Code ชำระเงินร้านค้า' : 'สแกน QR Code ชำระเงิน' }}</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '640px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
              style="background: #FFFFFF; border-radius: 20px;">
              <div style="text-align: center;">
                <v-col class="py-0">
                  <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${imageBase64}`" v-if="ImageQR !== ''"/>
                </v-col>
                <v-col class="py-0">
                  <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                </v-col>
                <div>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'">ยอดชำระเงินจำนวน : {{ netPrice ?
                      Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}
                      บาท</span>
                  </v-col>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 13px; font-weight: 400;' : 'font-size: 14px; font-weight: 400;'">{{ QRShop === false ? 'รหัสอ้างอิง' : '' }} {{Ref1}}</span>
                  </v-col>
                  <v-col class="py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 600; color: #A1A1A1;' : 'font-size: 14px; font-weight: 600; color: #A1A1A1;'">สามารถชำระเงินได้ตามขั้นตอนนี้
                      (กรณีชำระเงินผ่านมือถือ)</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">2. เปิดแอปธนาคารของท่าน
                      และเลือกเมนูสแกน QR Code</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                      Code</span>
                  </v-col>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" />
    <EditModalCompanyAddress :EditAddressDetail="EditAddressDetail" :title="titleCompanyAddress" :page="page" />
    <ModalTaxInvoice ref="ModalTaxAddress" :title="titleTaxAddress" :frompage="CartPage" :ShippingType="selectTypeAddress " :page="page"/>
    <!-- <ModalQuotation /> -->
    <v-dialog v-model="modalPayment" width="464" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">วิธีการชำระเงิน</font>
          </span>
          <v-btn icon dark @click="modalPayment = !modalPayment">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="mt-9 my-3" style="font-size:14px; color: #333333; font-weight: 600;">เลือกวิธีการชำระเงิน
        </v-card-text>
        <v-row no-gutters justify="center" align="center">
          <v-col cols="6" align="center" :class="MobileSize ? '' : 'pl-12'">
            <v-card @click="openCreateQU('ชำระทันที')" align="center" class="mb-9" outlined width="124" height="148">
              <v-col style="transform: translateY(30px);">
                <v-img src="@/assets/icons/money.png" width="40" height="40"></v-img>
                <span>ชำระเงินทันที</span>
              </v-col>
            </v-card>
          </v-col>
          <v-col cols="6" align="center" :class="MobileSize ? '' : 'pr-4'">
            <v-card @click="openCreateQU('เครดิตเทอม')" align="center" class="mb-9" outlined width="124" height="148">
              <v-col style="transform: translateY(30px);">
                <v-img src="@/assets/icons/term-loan.png" width="40" height="40"></v-img>
                <span>ใช้เครดิตเทอม</span>
              </v-col>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
    <CouponCart ref="CouponCart" />
    <CodeCart ref="CodeCart" />
    <SystemCodeCart ref="SystemCodeCart" />
    <SystemCodeCartShipping ref="SystemCodeCartShipping" />
    <PointCart ref="PointCart" />
    <v-dialog v-model="dialog_Cancel_Coupon" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">ขอแก้ไขใบเสนอราคา</font>
          </span>
          <v-btn icon dark @click="dialog_Cancel_Coupon = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row align="center" class="pa-2 mt-4">
              <span style="font-weight: 200; font-size: 18px; line-height: 26px; color: #333333;">
                การแก้ไขใบเสนอราคาจะยังไม่สามารถใช้คูปองส่วนลดได้จนกว่าผู้ขายจะทำการอนุมัติ<br />
                <font color="#27AB9C">"ยืนยันการดำเนินการต่อหรือไม่"</font>
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-1" @click="dialog_Cancel_Coupon = false">
              ยกเลิก
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-1 pl-8 pr-8 white--text" @click="openEditQU()">
              ยืนยัน
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="deleteDialog" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="deleteDialog = !deleteDialog"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4" ><b>ลบที่อยู่ในการออกใบกำกับภาษี</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row v-if="!MobileSize" dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="deleteDialog = !deleteDialog">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="DeleteInvoice()">ตกลง</v-btn>
            </v-row>
            <v-row v-if="MobileSize" dense justify="center">
              <v-btn height="38" outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="deleteDialog = !deleteDialog">ยกเลิก</v-btn>
              <v-btn height="38" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="DeleteInvoice()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDeleteSuccessTax" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่เสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบที่อยู่ในการออกใบกำกับภาษีเรียบร้อย</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">ตกลง</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogDeleteSuccessTax = !dialogDeleteSuccessTax">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table, Space, TimePicker } from 'ant-design-vue'
import Vue from 'vue'
export default {
  props: ['taxType'],
  components: {
    'a-table': Table,
    'a-space': Space,
    'a-time-picker': TimePicker,
    'a-time-range-picker': TimePicker.RangePicker,
    EditModalAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/EditAddressProfile'),
    ModalTaxInvoice: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalAddress/TaxInvoiceAddress'),
    CouponCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListCoupon'),
    CodeCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListCode'),
    SystemCodeCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListSystemCode'),
    SystemCodeCartShipping: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListSystemCodeShipping'),
    PointCart: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalCoupon/ListPoint'),
    EditModalCompanyAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/AddressCompanyModal')
    // ModalQuotation: () => import('@/components/Cart/ModalQuotation/QuotationModal')
  },
  data () {
    return {
      user: [],
      oldValueDiscount: '',
      supplyboardOne: '',
      supplyboardTwo: '',
      discountPercentB2B: false,
      discountBahtB2B: false,
      selectedCreditTerm: null,
      B2BDiscountBaht: '',
      B2BDiscountPercent: '',
      installmentAmounts: [],
      rawInstallmentAmounts: [],
      sameAmountPerMonth: false,
      selectinstallment: null,
      noShipping: false,
      showAll: false,
      showUsers: 5,
      InetRelation: [],
      CodePlatform: [],
      CodePlatformShipping: [],
      TypeOS: '',
      usePointOrNot: '',
      XBaht: '',
      PointData: '',
      CouponData: [],
      btnEstimateCost: false,
      dialogDeleteSuccessTax: false,
      radioCreditTerm: 'No',
      reShippingData: false,
      DialogCompanyAddress: false,
      comAddress: [],
      CartAddress: [],
      ShippingData: [],
      costTransport: '',
      nameTransport: '',
      radioTransport: '',
      estimateCostData: [],
      SellerShopID: '',
      DialogTransport: false,
      extbuyerrole: false,
      companydata: '',
      openEtaxAddress: false,
      deleteDialog: false,
      paymenttransactionnumber: '',
      CloseDialog: false,
      RequiredInvoice: '',
      invoiceID: '',
      EtaxType: '',
      titleTaxAddress: '',
      OldId: '',
      invoicedetail: [],
      TaxAddress: false,
      select: 1,
      disabledHours () {
        const currentHour = new Date().getHours()
        // Disable current hour and hours before it
        return Array.from({ length: currentHour + 1 }, (v, k) => k)
      },
      disabledMinutes (hour) {
        if (hour === new Date().getHours()) {
          // Disable current minute and minutes before it
          const currentMinute = new Date().getMinutes()
          return Array.from({ length: currentMinute + 1 }, (v, k) => k)
        }
        // Disable all minutes for other hours
        return []
      },
      companyAddressData: [],
      DialogTaxAddress: false,
      countproductgen: false,
      todayselect: false,
      timeselecttoday: '',
      timeselect: '',
      today: new Date().toISOString().substr(0, 10),
      futureDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().substr(0, 10),
      ImageQR: '',
      netPrice: '',
      Ref1: '',
      Ref2: '',
      radioPayment: '',
      deleteAdressData: '',
      deleteCompanyAdressData: '',
      deleteTaxAdressData: '',
      typeButton: '',
      DialogAddress: false,
      DialogQR: false,
      dialogSuccess: false,
      dialogAwaitConfirm: false,
      modalContractStartDate: false,
      modalContractEndDate: false,
      CartPage: 'CartPage',
      role: '',
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'รถเข็นสินค้า',
          disabled: false,
          href: '/shoppingcart'
        },
        {
          text: 'รายการสั่งซื้อสินค้า',
          disabled: true,
          href: '/checkout'
        }
      ],
      dates: '',
      menu: false,
      default_address: '',
      radios: 'radio-2',
      radiosinvoice: '',
      radiostax: 'radiotax-2',
      lazy: false,
      overlay: false,
      cartData: '',
      itemsCart: [],
      Address: '',
      Fullname: '',
      EditAddressDetail: '',
      titleAddress: '',
      address_data: '',
      taxAddress: '',
      companyName: '',
      companyTaxID: '',
      checkAdminQU: true,
      pplURL: '',
      pplToken: '',
      page: '',
      taxinvoiceAddress: [],
      taxinvoiceAddressNew: [],
      googleItem: [],
      taxRoles: 'No',
      selectTypeAddress: 'Normal',
      oneDataSpecial: '',
      modalPayment: false,
      responseSentDataPPL: '',
      SelectCouponOrPoint: true,
      dialog_Cancel_Coupon: false,
      checkOwnShop: 'N',
      discountBaht: '',
      discountCode: '',
      disableButtonPay: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      minDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date2: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchContractStartDate: '',
      setMinDateContractEndDate: '',
      contractStartDate: '',
      contractDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      ActiveDiscount: false,
      selectDiscount: '',
      discount: '',
      contractSet: false,
      reason: '',
      selectBudget: '',
      choose_list: '',
      selectTypeDoc: '',
      tax_id: '',
      itemTypeDoc: [],
      selectedPr: '',
      itemCodePrList: [],
      itemBudget: [
        { text: 'งบดำเนินการ', value: 'operating_budget' },
        { text: 'งบลงทุน', value: 'investment_budget' },
        { text: 'งบรายจ่ายประจำ', value: 'regular_expenditure_budget' }
      ],
      selectCutBudget: '',
      itemCutBudget: [
        { text: 'ต้นทุนขาย (COGS)', value: 'COGS' },
        { text: 'ค่าใช้จ่ายและบริการ (SG&A)', value: 'SG&A' },
        { text: 'ต้นทุนวิจัยและพัฒนา (R&D)', value: 'R&D' }
      ],
      itemsCreditTerm: [
        { value: '3', label: '3 เดือน' },
        { value: '6', label: '6 เดือน' },
        { value: '10', label: '10 เดือน' }
      ],
      Name_Buyer: '',
      Phone_Buyer: '',
      Position_Buyer: '',
      Email_Buyer: '',
      Name_Audit1: '',
      Phone_Audit1: '',
      Position_Audit1: '',
      Email_Audit1: '',
      Name_Audit2: '',
      Phone_Audit2: '',
      Position_Audit2: '',
      Email_Audit2: '',
      json_personal: [],
      purchasing_cheif: [],
      inspectors_one: [],
      inspectors_two: [],
      name: '',
      position: '',
      phone: '',
      email: '',
      buyer_name: '',
      buyer_phone: '',
      buyer_position: '',
      buyer_email: '',
      Name_SupplyBoard1: '',
      Phone_SupplyBoard1: '',
      Position_SupplyBoard1: '',
      Email_SupplyBoard1: '',
      Name_SupplyBoard2: '',
      Phone_SupplyBoard2: '',
      Position_SupplyBoard2: '',
      Email_SupplyBoard2: '',
      userdetail: [],
      a: '',
      Rules: {
        Name: [
          v => !!v || 'กรุณากรอกชื่อ-สกุล'
        ],
        Phone: [
          v => !!v || 'กรุณากรอกเบอร์โทร',
          v => /^[0-9]{10}$/.test(v) || 'กรุณากรอกเบอร์โทรให้ถูกต้อง'
        ],
        Position: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        Email: [
          v => !!v || 'กรุณากรอก Email',
          v => /^\w+([.-]?\w+)@[a-zA-Z]+([.-]?[a-zA-Z]+)(.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอก Email ให้ถูกต้อง'
        ]
      },
      titleCompanyAddress: '',
      imageBase64: '',
      roleCheck: '',
      QRShop: false
    }
  },
  computed: {
    formattedB2BDiscount () {
      const num = parseFloat(this.itemsCart.total_b2b_discount)
      if (isNaN(num)) return '0.00'
      return num.toLocaleString('th-TH', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    },
    hasInstallmentError () {
      return this.installmentAmounts.some(amount =>
        !amount || this.validateInstallment(amount) !== true
      )
    },
    creditTermOptions () {
      const maxTerm = (this.itemsCart && this.itemsCart.choose_list && this.itemsCart.choose_list[0] &&
      this.itemsCart.choose_list[0].setting_partner_data && this.itemsCart.choose_list[0].setting_partner_data[0] &&
      this.itemsCart.choose_list[0].setting_partner_data[0].credit_term) || 1
      return Array.from({ length: maxTerm }, (_, i) => ({
        label: `${i + 1} วัน`,
        value: i + 1
      }))
    },
    remainingAmount () {
      // แปลงค่าใน installmentAmounts เป็นตัวเลข แล้วหาผลรวม
      const totalInstallments = this.installmentAmounts
        .map(amount => Number(amount) || 0)
        .reduce((sum, val) => sum + val, 0)
      // คำนวณยอดที่เหลือ
      const remaining = parseFloat(this.itemsCart.net_price) - totalInstallments
      // คืนค่าพร้อมจัด format , และทศนิยม 2 ตำแหน่ง
      return remaining.toLocaleString('th-TH', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    installmentOptions () {
      const options = this.itemsCart.installment_method.map(item => ({
        ...item,
        formattedMonth: `${item.month} งวด`
      }))
      if (this.itemsCart.choose_list[0].pay_type === 'onetime') {
        return options.filter(item => item.month === 1)
      }
      return options
    },
    filteredCreditTerms () {
      return this.itemsCreditTerm.map(term => {
        const correspondingInstallment = this.itemsCart.installment_method.find(installment => installment.month === term.value)
        // เพิ่ม property displayText เพื่อแสดงราคาในตัวเลือก
        return {
          ...term,
          displayText: correspondingInstallment ? `${term.label} - ${Number(correspondingInstallment.price).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} บาท/เดือน` : term.label
        }
      }).filter(term => {
        // กรองตามเงื่อนไขที่ต้องการ
        const correspondingInstallment = this.itemsCart.installment_method.find(installment => installment.month === term.value)
        return correspondingInstallment && correspondingInstallment.price >= 500
      })
    },
    formattedTime () {
      if (this.dates !== this.today) {
        const dateObject = new Date(this.timeselect)
        const hours = dateObject.getHours()
        const minutes = dateObject.getMinutes()

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
      } else {
        const dateObject = new Date(this.timeselecttoday)
        const hours = dateObject.getHours()
        const minutes = dateObject.getMinutes()

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headers () {
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        if (element.product_type.length >= 1 && element.product_type === 'general') {
          this.countproductgen = true
        }
      })
      if (this.role.role !== 'ext_buyer' && this.itemsCart.is_JV_buyer === 'yes') {
        const headers = [
          {
            title: 'รหัส SKU',
            dataIndex: 'sku',
            key: 'sku',
            scopedSlots: { customRender: 'sku' },
            width: '15%'
          },
          {
            title: 'รายละเอียดสินค้า',
            dataIndex: 'productdetails',
            key: 'productdetails',
            scopedSlots: { customRender: 'productdetails' },
            width: '25%'
          },
          {
            title: 'ราคาต่อชิ้น',
            dataIndex: 'revenue_default',
            key: 'revenue_default',
            scopedSlots: { customRender: 'revenue_default' },
            align: 'center',
            width: '15%'
          },
          {
            title: 'จำนวน',
            dataIndex: 'quantity',
            key: 'quantity',
            scopedSlots: { customRender: 'quantity' },
            align: 'center',
            width: '10%'
          },
          {
            title: 'ราคารวม',
            dataIndex: 'revenue_vat',
            scopedSlots: { customRender: 'revenue_vat' },
            key: 'revenue_vat',
            align: 'center',
            width: '15%'
          },
          {
            title: this.$createElement('span', [
              'Item Code PR ',
              this.$createElement('span', { style: { color: 'red' } }, '*')
            ]),
            dataIndex: 'item_code_pr',
            scopedSlots: { customRender: 'item_code_pr' },
            key: 'item_code_pr',
            align: 'center',
            width: '25%'
          }
        ]
        return headers
      } else {
        const headers = [
          {
            title: 'รหัส SKU',
            dataIndex: 'sku',
            key: 'sku',
            scopedSlots: { customRender: 'sku' },
            width: '15%'
          },
          {
            title: 'รายละเอียดสินค้า',
            dataIndex: 'productdetails',
            key: 'productdetails',
            scopedSlots: { customRender: 'productdetails' },
            width: '25%'
          },
          {
            title: 'ราคาต่อชิ้น',
            dataIndex: 'revenue_default',
            key: 'revenue_default',
            scopedSlots: { customRender: 'revenue_default' },
            align: 'center',
            width: '20%'
          },
          {
            title: 'จำนวน',
            dataIndex: 'quantity',
            key: 'quantity',
            scopedSlots: { customRender: 'quantity' },
            align: 'center',
            width: '10%'
          },
          {
            title: 'ราคารวม',
            dataIndex: 'revenue_vat',
            scopedSlots: { customRender: 'revenue_vat' },
            key: 'revenue_vat',
            align: 'center',
            width: '15%'
          }
        ]
        return headers
      }
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headersMobile
    },
    checkSelectPr () {
      const checkPr = this.itemsCart.length === 0 ? true : this.itemsCart.choose_list[0].product_list.some(v => v.item_code_pr_buyer === null)
      return checkPr
    }
  },
  async created () {
    // console.log('%c Hello Bug ', 'background: red; color: #000; padding: 4px; border-radius: 2px; margin-left: 1ch;')
    this.$EventBus.$on('SentGetCart', this.getCart)
    // this.$EventBus.$on('EditAddressComplete', (data) => { this.getCart(data) })
    // this.$EventBus.$on('GetTaxAddress', this.checkAddressTaxinvoiceData)
    // this.$EventBus.$on('GetInvoiceAddress', this.checkAddressTaxinvoiceDataNew)
    this.$EventBus.$on('selectcouponorpointCheckout', this.selectcouponorpointCheckout)
    this.$EventBus.$emit('GetTaxAddress')
    // this.checkAddressTaxinvoiceDataNew()
    // localStorage.removeItem('CouponPlatform')
    localStorage.removeItem('CouponPlatformShipping')
    localStorage.removeItem('InetRelationShip')
    // this.checkAddressTaxinvoiceData()
    this.TypeOS = this.detectOS()
    // this.$EventBus.$on('closeEditModalAddress', this.checkStatus)
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('onedata1', onedata)
    // if (localStorage.getItem('CompanyData') !== null) {
    //   var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
    //   console.log(companyData)
    // }
    this.role = JSON.parse(localStorage.getItem('roleUser'))
    this.roleCheck = this.role.role
    this.pplToken = onedata.user.access_token
    if (Object.prototype.hasOwnProperty.call(onedata, 'cartData')) {
      if (onedata.cartData.coupon.length !== 0) {
        if (onedata.cartData.coupon === undefined || onedata.cartData.coupon[0].coupon_sort === 'cancel' || onedata.cartData.coupon.length === 0) {
          if (localStorage.getItem('ClickgoToCheckOut') !== null) {
            this.SelectCouponOrPoint = true
          }
        } else {
          localStorage.setItem('ClickgoToCheckOut', true)
          if (localStorage.getItem('ClickgoToCheckOut') !== null) {
            this.SelectCouponOrPoint = false
            if (localStorage.getItem('CouponOrPoint') === 'Point') {
              this.nameCouponOrPoint = 'ใช้คะแนน ' + onedata.cartData.coupon[0].point + ' คะแนน'
            } else {
              this.nameCouponOrPoint = onedata.cartData.coupon[0].coupon_name
            }
          }
        }
      }
      this.cartData = onedata.cartData
      this.oneDataSpecial = onedata.cartDataSpecialPrice
      if (onedata.cartData.coupon.length !== 0) {
        this.CouponData = onedata.cartData.coupon.filter(coupon => coupon.seller_shop_id !== -1)
      } else {
        this.CouponData = []
      }
      this.PointData = onedata.cartData.point
      if (this.PointData.length === 0) {
        this.PointData = 0
      } else {
        this.PointData = onedata.cartData.point[0].total_point
      }
      this.usePointOrNot = this.cartData.usePointOrNot
      // console.log('cart', this.cartData)
      // if (this.role.role !== 'ext_buyer') {
      //   this.getCompanyAddress()
      // }
      await this.getAddress()
      var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
      var user = response.data[0]
      this.user = user
      this.openTypeDoc()
      this.checkAddress()
      this.getItemCodePr()
      this.getListUserPointByUser()
      // console.log(this.userdetail)
      // this.getCart()
      this.GetAllinvoice()
      setTimeout(() => { this.btnEstimateCost = true }, 2000)
      await this.GetPersonal()
      this.buyer_name = this.buyer_name === '' || null ? user.first_name_th + ' ' + user.last_name_th : this.buyer_name
      this.buyer_phone = this.buyer_phone === '' || null ? user.phone : this.buyer_phone
      this.buyer_email = this.buyer_email === '' || null ? user.email : this.buyer_email
      localStorage.removeItem('ClickgoToCheckOut')
    } else {
      this.cartData = ''
      this.$router.push('/')
    }
    // await this.GetPersonal()
    window.scrollTo(0, 0)
  },
  mounted () {
    this.rawInstallmentAmounts = this.installmentAmounts.map(a =>
      this.formatAmount(a)
    )
    window.addEventListener('storage', this.handleStorageChange)
    // this.$EventBus.$on('selectcouponorpointCheckout', this.selectcouponorpointCheckout)
    // this.$on('hook:beforeDestroy', () => {
    //   this.$EventBus.$off('selectcouponorpointCheckout')
    // })
    this.$EventBus.$on('EditAddressComplete', (data) => { this.getAddress(data) })
    this.$EventBus.$on('EditAddressCompanyComplete', data => {
      this.getCompanyAddress(data)
    })
    this.$EventBus.$on('SelectCouponCheckout', this.getCart)
    this.$EventBus.$on('ListCode', this.getCart)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('SelectCouponCheckout')
      this.$EventBus.$off('ListCode')
    })
  },
  beforeDestroy () {
    window.removeEventListener('storage', this.handleStorageChange)
    this.$EventBus.$off('SentGetCart')
    this.$EventBus.$off('GetTaxAddress')
    this.$EventBus.$off('EditAddressComplete')
    this.$EventBus.$off('EditAddressCompanyComplete')
  },
  watch: {
    sameAmountPerMonth (newVal) {
      if (newVal && this.selectinstallment) {
        this.updateInstallmentAmounts()
      } else {
        this.installmentAmounts = this.createEmptyInstallments()
        this.rawInstallmentAmounts = this.installmentAmounts.map(() => '') // เคลียร์ให้ว่าง
      }
    },
    selectinstallment (newVal) {
      if (this.itemsCart.choose_list[0].pay_type !== 'onetime') {
        // this.sameAmountPerMonth = false
        // this.installmentAmounts = this.createEmptyInstallments()
        // this.rawInstallmentAmounts = this.installmentAmounts.map(() => '') // เคลียร์ให้ว่าง
        if (this.sameAmountPerMonth) {
          this.updateInstallmentAmounts()
        } else {
          this.installmentAmounts = this.createInstallments(this.installmentAmounts)
          this.rawInstallmentAmounts = this.installmentAmounts.map(val => val !== undefined && val !== null ? val : '')
          this.rawInstallmentAmounts = this.rawInstallmentAmounts.map(amount => this.formatAmount(amount))
        }
      }
    },
    usePointOrNot (val) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.usePointOrNot = val
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    '$store.state.ModuleUser.stateAddInvoice': {
      handler: async function (newValue) {
        console.log('stateAddInvoice changed', newValue)
        await this.GetAllinvoice()
      },
      deep: true
    },
    '$store.state.ModuleUser.stateupsertInvoice': {
      handler: async function (newValue) {
        console.log('stateupsertInvoice changed', newValue)
        await this.GetAllinvoice()
      },
      deep: true
    },
    selectTypeAddress (val) {
      var onedata
      // this.discountCode = ''
      // this.discountBaht = ''
      // onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // onedata.cartData.shipping = 'yes'
      // localStorage.setItem('oneData', Encode.encode(onedata))
      // this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      if (val === 'Normal') {
        this.discountCode = ''
        this.discountBaht = ''
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping = 'yes'
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      } else {
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.shipping = 'no'
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.getCart()
      }
    },
    taxRoles (val) {
      if (val === 'Personal' || val === 'Business') {
        this.taxAddress = ''
      }
    },
    discountCode (val) {
      if (val === '') {
        this.discountBaht = ''
        this.ChangeDiscount(this.itemsCart.total_price_no_vat)
      }
    }
  },
  methods: {
    onBlurAmount (index) {
      const val = this.rawInstallmentAmounts[index]
      const numeric = parseFloat(val.toString().replace(/,/g, ''))
      if (isNaN(numeric)) {
        this.$set(this.installmentAmounts, index, 0)
        this.$set(this.rawInstallmentAmounts, index, '')
        return
      }

      // ตรวจสอบไม่ให้เกิน remainingAmount
      const remaining = parseFloat(this.remainingAmount.toString().replace(/,/g, '')) || 0
      const currentInstallment = parseFloat(this.installmentAmounts[index].toString().replace(/,/g, '')) || 0
      var result = remaining + currentInstallment
      if (result < 0) {
        result = 0
      }
      const max = parseFloat(result.toString().replace(/,/g, ''))
      const final = numeric > max ? max : numeric
      this.$set(this.installmentAmounts, index, final)
      this.$set(this.rawInstallmentAmounts, index, this.formatAmount(final))
    },
    onInputB2BDiscount (val) {
      const stringVal = (val || '').toString()
      const rawValue = stringVal.replace(/,/g, '')
      const cleaned = rawValue
        .replace(/[^0-9.]/g, '')
        .replace(/^\./, '')
        .replace(/(\..*)\./g, '$1')
        .replace(/^0+(\d)/, '$1')
        .replace(/(\.\d{2})\d+/, '$1')
      this.B2BDiscountBaht = cleaned
      if (!this.sameAmountPerMonth) {
        this.installmentAmounts = this.createEmptyInstallments()
        this.rawInstallmentAmounts = this.installmentAmounts.map(() => '')
      }
      this.getCart()
    },
    onInputB2BPercent () {
      if (!this.sameAmountPerMonth) {
        this.installmentAmounts = this.createEmptyInstallments()
        this.rawInstallmentAmounts = this.installmentAmounts.map(() => '')
      }
      this.getCart()
    },
    formatAmount (value) {
      if (value === null || value === undefined || value === '') return ''
      const number = parseFloat(value.toString().replace(/,/g, ''))
      return isNaN(number) ? '' : number.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    },
    formatPrice (value) {
      const num = Number(value)
      return num < 0
        ? '0.00'
        : num.toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
    },
    validateInstallment (value) {
      if (!value || value === '0' || value === '0.00' || value === '.00' || /^0+(\.00)?$/.test(value) || /^\./.test(value)) {
        return 'กรุณาระบุจำนวนที่ถูกต้อง'
      }
      return true
    },
    updateInstallmentAmounts () {
      const selectedOption = this.installmentOptions.find(opt => opt.month === this.selectinstallment)
      if (selectedOption) {
        this.installmentAmounts = Array(this.selectinstallment).fill(parseFloat(selectedOption.price).toFixed(2))

        const totalInstallments = this.installmentAmounts.reduce((sum, num) => sum + parseFloat(num), 0)
        const difference = this.itemsCart.net_price - totalInstallments
        // เพิ่มส่วนต่างเข้าไปในงวดสุดท้าย
        this.installmentAmounts[this.installmentAmounts.length - 1] =
          (parseFloat(this.installmentAmounts[this.installmentAmounts.length - 1]) + difference).toFixed(2)

        // เพิ่มค่าที่ฟอร์แมตแล้วเข้าไปใน rawInstallmentAmounts
        this.rawInstallmentAmounts = this.installmentAmounts.map(amount => this.formatAmount(amount))
      }
    },
    createEmptyInstallments () {
      return this.selectinstallment ? Array(this.selectinstallment).fill('') : []
    },
    createInstallments (oldValues = []) {
      const newLength = this.selectinstallment || 0
      const result = []
      for (let i = 0; i < newLength; i++) {
        result[i] = oldValues[i] !== undefined ? oldValues[i] : ''
      }
      return result
    },
    clickCheckboxDiscount (val) {
      if (this.oldValueDiscount !== val) {
        if (val === 'Baht') {
          this.discountBahtB2B = true
          this.discountPercentB2B = false
          this.B2BDiscountPercent = ''
          this.oldValueDiscount = val
        } else if (val === 'Percent') {
          this.discountBahtB2B = false
          this.discountPercentB2B = true
          this.B2BDiscountBaht = ''
          this.oldValueDiscount = val
        }
      } else {
        this.oldValueDiscount = ''
        this.discountBahtB2B = false
        this.discountPercentB2B = false
        this.B2BDiscountBaht = ''
        this.B2BDiscountPercent = ''
      }
      this.getCart()
    },
    getCouponId () {
      const foundCoupon = this.CouponData.find(coupon => coupon.seller_shop_id !== -1)
      return foundCoupon ? foundCoupon.coupon_id : ''
    },
    async handleStorageChange (event) {
      if (event.key === 'oneData') {
        var NewValue = JSON.parse(Decode.decode(event.newValue))
        var oldValue = JSON.parse(Decode.decode(event.oldValue))
        if (NewValue.cartData !== oldValue.cartData) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 4500,
            timerProgressBar: true,
            icon: 'warning',
            text: 'ตระกร้ามีการเปลี่ยนแปลง'
          })
          this.backstep()
        }
      }
    },
    HideUsers () {
      this.showAll = false
      this.showUsers = 5
    },
    ShowUsers () {
      this.showAll = true
      this.showUsers = this.InetRelation.length
    },
    clearUser () {
      this.InetRelation = []
      localStorage.removeItem('InetRelationShip')
      this.$EventBus.$emit('ListCode')
    },
    clearCodePlatform () {
      this.CodePlatform = []
      this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1 || coupon.coupon_type !== 'discount')
      // localStorage.removeItem('CouponPlatform')
      // this.$EventBus.$emit('ListCode')
      this.$EventBus.$emit('clearCouponPlatform')
    },
    clearCodePlatformShipping () {
      this.CodePlatformShipping = []
      this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1 || coupon.coupon_type !== 'free_shipping')
      // localStorage.removeItem('CouponPlatform')
      // this.$EventBus.$emit('ListCode')
      this.$EventBus.$emit('clearCouponPlatformShipping')
    },
    clickCode () {
      this.page = 'checkout'
      // this.$EventBus.$emit('submitStart')
      this.$refs.CodeCart.open()
    },
    clickSystemCode () {
      this.page = 'checkout'
      // this.$EventBus.$emit('submitStart')
      var priceCheckCoupon = 0
      if (parseInt(this.itemsCart.total_include_vat) === 0) {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_no_vat) - parseFloat(this.itemsCart.choose_list[0].total_point)
      } else {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_vat) + parseFloat(this.itemsCart.total_coupon_discount)
      }
      this.$refs.SystemCodeCart.open('', this.CodePlatform, '', '', priceCheckCoupon, this.radios)
      this.$EventBus.$emit('getListPlatformCoupon')
    },
    clickSystemCodeShipping () {
      this.page = 'checkout'
      // this.$EventBus.$emit('submitStart')
      var priceCheckCoupon = 0
      if (parseInt(this.itemsCart.total_include_vat) === 0) {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_no_vat) - parseFloat(this.itemsCart.choose_list[0].total_point)
      } else {
        priceCheckCoupon = parseFloat(this.itemsCart.total_price_vat) + parseFloat(this.itemsCart.total_coupon_discount)
      }
      this.$refs.SystemCodeCartShipping.open('', this.CodePlatformShipping, '', '', priceCheckCoupon, this.radios)
      this.$EventBus.$emit('getListPlatformCouponShipping')
    },
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    async getListUserPointByUser () {
      this.XBaht = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
      var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var data = {
        role_user: RoleUser.role,
        customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
        seller_shop_id: this.SellerShopID,
        company_id: onedata.cartData.company_id,
        com_perm_id: onedata.cartData.com_perm_id
      }
      await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
      var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
      if (res.result === 'SUCCESS') {
        this.XBaht = res.data[0].x_baht
        this.XBaht = parseFloat(res.data[0].x_baht) / parseFloat(res.data[0].x_point)
        // console.log('this.XBaht', this.XBaht)
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>แต้มขัดข้อง กรุณาลองใหม่ภายหลัง</h3>'
        })
      }
    },
    closePoint () {
      this.PointData = 0
      this.getCart(this.CouponData, this.PointData)
      this.$EventBus.$emit('clearPoint')
    },
    closeCoupon (item) {
      this.PointData = 0
      this.usePointOrNot = 'no'
      this.CouponData = []
      this.noShipping = false
      this.getCart(this.CouponData, this.PointData)
      this.$EventBus.$emit('clearCoupon', item)
    },
    clickCoupon (item, point, baht, data) {
      // console.log(item, point, baht)
      this.page = 'checkout'
      this.closePoint()
      this.usePointOrNot = 'no'
      this.$refs.CouponCart.open(this.page, item, point, baht, this.radios, data)
      // this.$EventBus.$emit('getListCoupon')
    },
    clickPoint (item, point, baht, discoupon, data) {
      // console.log('discoupon', discoupon)
      this.page = 'checkout'
      this.$refs.PointCart.open(this.page, item, point, baht, discoupon, data)
      this.$EventBus.$emit('getListPoint')
    },
    async GetReviewQuotation () {
      this.$store.commit('openLoader')
      if ((this.itemsCart.isEtax === 'yes' || this.itemsCart.isEtax === 'no') && this.userdetail.length === 0 && this.radios === 'radio-2') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณากรอกที่อยู่สำหรับจัดส่งสินค้า<br>เพื่อดูตัวอย่างใบเสนอราคา</h3>'
        })
        this.DialogAddress = true
      } else if ((this.itemsCart.isEtax === 'yes' || this.itemsCart.isEtax === 'no') && (this.userdetail.length === 1 && (this.userdetail[0].zip_code === '' && this.userdetail[0].zip_code === null)) && this.radios === 'radio-2') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณากรอกที่อยู่สำหรับจัดส่งสินค้า<br>เพื่อดูตัวอย่างใบเสนอราคา</h3>'
        })
        this.editAddress(this.userdetail[0])
      } else if ((this.itemsCart.isEtax === 'yes' && this.radiostax === 'radiotax-1') && this.invoicedetail.length === 0) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณากรอกที่อยู่สำหรับใบกำกับภาษี<br>เพื่อดูตัวอย่างใบเสนอราคา</h3>'
        })
        this.DialogTaxAddress = true
      } else {
        var companyId = ''
        var comPermId = ''
        if (this.role.role !== 'ext_buyer') {
          if (localStorage.getItem('SetRowCompany') !== null) {
            var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            companyId = companyDataID.company.company_id
            comPermId = companyDataID.position.com_perm_id
          } else {
            companyId = -1
            comPermId = -1
          }
        }
        if (this.itemsCart.isEtax === 'yes') {
          this.invoicedetail.forEach(element => {
            if (element.default_invoice === 'Y') {
              this.invoiceID = element.id
            }
          })
        } else {
          this.invoiceID = ''
        }
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        const data = {
          invoice_id: this.itemsCart.isEtax === 'yes' && this.radiostax === 'radiotax-1' ? this.invoiceID : '',
          role_user: this.role.role,
          company_id: this.role.role !== 'ext_buyer' ? companyId : companyId = '-1',
          com_perm_id: this.role.role !== 'ext_buyer' ? comPermId : comPermId = '-1',
          seller_shop_id: this.SellerShopID,
          token: onedata.user.access_token,
          customer_id: '-1',
          installment_month: this.selectinstallment === null ? 0 : this.selectinstallment,
          credit_day: this.selectedCreditTerm === null ? 0 : this.selectedCreditTerm,
          product_free: JSON.stringify(this.itemsCart.product_free)
        }
        await this.$store.dispatch('actionsGetReviewQuotation', data)
        var res = await this.$store.state.ModuleCart.stateGetReviewQuotation
        if (res.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          window.open(res.data.pdf_path, '_blank')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ระบบขัดข้องไม่สามารถดูตัวอย่างใบเสนอราคาได้</h3>'
          })
        }
      }
    },
    async GoToOrderCompany () {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var companyid = companyId.company.company_id
      const data = {
        company_id: companyid
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      await this.$store.dispatch('actionsAuthorityUser')
      var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
      var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
      var listcompany = responseposition.data.list_company
      for (let i = 0; i < listcompany.length; i++) {
        if (responsecompany.data.id === listcompany[i].company_id) {
          localStorage.removeItem('list_Company_detail')
          localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
        }
      }
      localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      if (this.MobileSize) {
        this.$router.push({ path: '/orderCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      }
    },
    ClearRadio () {
      this.radioCreditTerm = 'No'
      this.radioTransport = ''
      this.radioPayment = ''
      this.getCart()
    },
    setRadioCreditTermNo () {
      this.radioCreditTerm = 'No'
    },
    DeleteCompanyAddress (item) {
      this.dialogAwaitConfirm = true
      // console.log(item)
      this.deleteCompanyAdressData = item
      this.typeButton = 'deleteCompany'
    },
    async deleteCompanyAddress () {
      const data = {
        id: this.deleteCompanyAdressData.id,
        company_id: this.deleteCompanyAdressData.company_id
      }
      await this.$store.dispatch('actionsDeleteCompanyAddress', data)
      const res = await this.$store.state.ModuleCart.stateDeleteCompanyAddress
      if (res.ok === 'y') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getCompanyAddress()
        this.deleteDialog = false
      } else {
        this.$swal.fire({ icon: 'error', title: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      }
      this.dialogAwaitConfirm = false
      this.dialogSuccess = true
    },
    async setDefaultCompanyAddress (item) {
      const data = {
        id: item.id,
        company_id: item.company_id
      }
      await this.$store.dispatch('actionsSetDefaultCompanyAddress', data)
      var res = await this.$store.state.ModuleCart.stateSetDefaultCompanyAddress
      if (res.ok === 'y') {
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
        // this.comAddress = [...res.data]
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
      this.reShippingData = true
      this.getCompanyAddress()
    },
    addCompanyAddress () {
      const val = {
        building_name: '',
        default: '',
        detail: '',
        district: '',
        email: '',
        floor: '',
        house_no: '',
        id: '',
        name_th: this.comAddress[0].name_th,
        moo_ban: '',
        moo_no: '',
        order_number: '',
        phone: '',
        province: '',
        room_no: '',
        soi: '',
        status: '',
        street: '',
        sub_district: '',
        company_id: this.comAddress[0].company_id,
        yaek: '',
        zipcode: ''
      }
      this.reShippingData = true
      localStorage.setItem('AddressCompanyDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.titleCompanyAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalCompanyAddress')
    },
    editCompanyAddress (val) {
      this.$store.commit('openLoader')
      const editData = {
        building_name: val.building_name,
        default: val.default,
        detail: val.detail,
        district: val.district,
        email: val.email,
        floor: val.floor,
        house_no: val.house_no,
        id: val.id,
        name_th: val.name_th,
        moo_ban: val.moo_ban,
        moo_no: val.moo_no,
        order_number: val.order_number,
        phone: val.phone,
        province: val.province,
        room_no: val.room_no,
        soi: val.soi,
        status: val.status,
        street: val.street,
        sub_district: val.sub_district,
        company_id: val.company_id,
        yaek: val.yaek,
        zipcode: val.zip_code
      }
      // console.log(editData.default_address)
      if (editData.default === 'Y') {
        this.reShippingData = true
      }
      localStorage.setItem('AddressCompanyDetail', Encode.encode(editData))
      this.EditAddressDetail = editData
      this.titleCompanyAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalCompanyAddress')
      this.getCompanyAddress()
    },
    async getCompanyAddress (item) {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (item) {
        if (item.default_address === 'Y' || item.default === 'Y') {
          this.ShippingData = []
        }
      }
      if (dataRole.role !== 'ext_buyer') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyId = companyDataID.company.company_id
        } else {
          companyId = -1
        }
      }
      var data = {
        company_id: companyId
      }
      await this.$store.dispatch('actionsGetCompanyAddress', data)
      var res = await this.$store.state.ModuleCart.stateGetCompanyAddress
      if (res.ok === 'y') {
        // console.log(res.query_result)
        this.$store.commit('closeLoader')
        this.comAddress = res.query_result
      } else {
        this.$store.commit('closeLoader')
      }
    },
    selectTransport (item) {
      if (this.radioTransport === item.tpl_name) {
        this.nameTransport = item.tpl_name
        this.costTransport = item.netEstimatePrice
        // var shippingData = {
        //   tpl_name: item.tpl_name,
        //   service_provider: item.service_provider,
        //   estimatePrice: item.estimatePrice,
        //   pricePolicy: item.pricePolicy,
        //   upCountry: item.upCountry,
        //   upCountryAmount: item.upCountryAmount,
        //   insureDeclareValue: item.insureDeclareValue,
        //   valueInsuranceFee: item.valueInsuranceFee,
        //   freightInsureEnabled: item.freightInsureEnabled,
        //   freightInsurance: item.freightInsurance,
        //   expressCategory: item.expressCategory,
        //   insured: item.insured,
        //   netEstimatePrice: item.netEstimatePrice,
        //   businessType: item.businessType,
        //   distance: item.distance,
        //   travelTime: item.travelTime,
        //   vehicleType: item.vehicleType,
        //   formulaName: item.formulaName,
        //   formulaCode: item.formulaCode,
        //   riderActive: item.riderActive,
        //   vehicleType_Id: item.vehicleType_Id,
        //   rawEstimatePrice: item.rawEstimatePrice,
        //   discountAmount: item.discountAmount,
        //   discountAmountPercent: item.discountAmountPercent,
        //   topUpAmount: item.topUpAmount,
        //   topUpAmountPercent: item.topUpAmountPercent,
        //   serviceType: item.serviceType
        // }
        var shippingData = item
        this.ShippingData = shippingData
        this.DialogTransport = false
        this.getCart(this.CouponData, this.PointData)
      }
    },
    async EstimateCost () {
      this.$store.commit('openLoader')
      // console.log(123456)
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (dataRole.role === 'ext_buyer' && this.radios === 'radio-2' && (this.userdetail.length === 0 || (this.userdetail.length === 1 && ((this.userdetail[0].detail === '' && this.userdetail[0].detail === null) || this.userdetail[0].default_address !== 'Y')))) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณากรอกที่อยู่สำหรับจัดส่งสินค้า</h3>'
        })
        if (this.userdetail.length === 1 && (this.userdetail[0].detail === '' && this.userdetail[0].detail === null)) {
          this.editAddress(this.userdetail[0])
        } else if (this.userdetail.length === 1 && (this.userdetail[0].default_address !== 'Y')) {
          this.DialogAddress = true
        } else if (this.userdetail.length === 0) {
          this.DialogAddress = true
        }
      } else {
        var companyId = ''
        var comPermId = ''
        if (dataRole.role !== 'ext_buyer') {
          if (localStorage.getItem('SetRowCompany') !== null) {
            // console.log(1)
            var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            // console.log(2)
            companyId = companyDataID.company.company_id
            comPermId = companyDataID.position.com_perm_id
          } else {
            companyId = -1
          }
        }
        // console.log(companyId)
        var conditionMet = false
        if (dataRole.role !== 'ext_buyer') {
          // console.log(3)
          if (this.comAddress.length === 0) {
            this.DialogTransport = false
            this.$swal.fire({
              showConfirmButton: false,
              timer: 5000,
              timerProgressBar: true,
              icon: 'warning',
              html: '<h3>กรุณาเลือกที่อยู่จัดส่งสินค้าก่อน หรือ กรอก/เพิ่มที่อยู่จัดส่งสินค้าให้สมบูรณ์ก่อน</h3>'
            })
            this.DialogCompanyAddress = true
            conditionMet = true
          }
          // console.log(4)
          if (conditionMet) {
            this.$store.commit('closeLoader')
            return
          }
          // console.log(5)
          var data = {
            form: 'web',
            address: this.CartAddress,
            role_user: dataRole.role,
            company_id: companyId,
            com_perm_id: comPermId,
            seller_shop_id: this.SellerShopID
          }
          // console.log(data)
        } else {
          if (this.CartAddress[0].detail === '' && this.CartAddress[0].detail === null) {
            this.DialogTransport = false
            // console.log(6666)
            this.$swal.fire({
              showConfirmButton: false,
              timer: 5000,
              timerProgressBar: true,
              icon: 'warning',
              html: '<h3>กรุณาเลือกที่อยู่จัดส่งสินค้าก่อน หรือ กรอก/เพิ่มที่อยู่จัดส่งสินค้าให้สมบูรณ์ก่อน</h3>'
            })
            this.DialogAddress = true
            conditionMet = true
          }
          if (conditionMet) {
            this.$store.commit('closeLoader')
            return
          }
          data = {
            form: 'web',
            address: this.CartAddress,
            role_user: dataRole.role,
            company_id: '-1',
            com_perm_id: '-1',
            seller_shop_id: this.SellerShopID
          }
        }
        // console.log('data', data)
        if (dataRole.role === 'ext_buyer') {
          await this.$store.dispatch('actionsEstimateCost', data)
          var res = await this.$store.state.ModuleCart.stateEstimateCost
          if (res.result === 'Success') {
            this.DialogTransport = true
            this.estimateCostData = res.data
            this.$store.commit('closeLoader')
            if (this.estimateCostData.length === 0 && this.itemsCart.shipping_method !== 0) {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                html: '<h3>ไม่มีขนส่งรองรับ</h3>'
              })
              this.backstep()
            }
          } else if (res.code === 400) {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 5000,
              timerProgressBar: true,
              icon: 'error',
              html: `<h4>${res.message}</h4>`
            })
            this.backstep()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
            })
            this.DialogTransport = false
          }
        }
      }
    },
    saveQRCode () {
      // const proxyUrl = 'https://cors-anywhere.herokuapp.com/'
      // const targetUrl =
      //   'https://media.geeksforgeeks.org/wp-content/uploads/20240426035114/79dd11a9452a92a1accceec38a45e16a.jpg'
      // fetch(this.imageBase64)
      //   .then((response) => {
      //     // console.log('Response:', response)
      //     return response.blob()
      //   })
      //   .then((blob) => {
      //     // console.log('Blob:', blob)
      //     const url = URL.createObjectURL(blob)
      //     const link = document.createElement('a')
      //     link.href = url
      //     link.download = 'QRCODE.png' // The name for the downloaded file
      //     document.body.appendChild(link)
      //     link.click()
      //     document.body.removeChild(link)
      //     URL.revokeObjectURL(url)
      //   })
      //   .catch(console.error)
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    checkIsEtax () {
      // if (this.itemsCart.isEtax === 'yes') {
      //   this.radiostax = 'radiotax-1'
      //   // this.DialogTaxAddress = true
      // }
      if (this.itemsCart.isEtax === 'yes' && this.invoicedetail.length === 0) {
        // this.radiostax = 'radiotax-1'
        // this.DialogTaxAddress = true
      }
    },
    closeDialogTax () {
      this.radiostax = 'radiotax-1'
    },
    openModalEditTaxAddress (item) {
      const editData = {
        id: item.id,
        user_id: item.user_id,
        branch_id: item.branch_id,
        company_id: item.company_id,
        tax_type: item.tax_type,
        tax_id: item.tax_id,
        buyer_one_id: item.buyer_one_id,
        name: item.name,
        email: item.email,
        address: item.address,
        postal_code: item.postal_code,
        province: item.province,
        district: item.district,
        sub_district: item.sub_district,
        role: item.role,
        default_invoice: item.default_invoice
      }
      // console.log(editData)
      localStorage.setItem('InvoiceAddress', Encode.encode(editData))
      this.titleTaxAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'checkout'
      this.$refs.ModalTaxAddress.open()
    },
    openModalTaxAddress () {
      this.titleTaxAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'checkout'
      this.$refs.ModalTaxAddress.open()
      // มีการยิง api get ข้อมูลใบกำกับภาษี
      // localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
    },
    async GetAllinvoice () {
      this.$store.commit('openLoader')
      // this.invoicedetail = []
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyId = companyDataID.company.company_id
        } else {
          companyId = -1
        }
      }
      if (dataRole.role !== 'ext_buyer') {
        var data = {
          user_id: onedata.user.user_id,
          company_id: companyId
        }
      } else {
        data = {
          user_id: onedata.user.user_id,
          company_id: companyId
        }
      }
      await this.$store.dispatch('actionsGetAllInvoiceAddress', data)
      var res = await this.$store.state.ModuleUser.stateGetAllInvoiceAddress
      if (res.message === 'Success') {
        this.invoicedetail = res.data
        this.invoicedetail.forEach(element => {
          if (element.default_invoice === 'Y') {
            this.OldId = element.id
          }
        })
        this.$store.commit('closeLoader')
      }
    },
    async setDefaultInvoice (item) {
      if (this.OldId === '') {
        this.OldId = 0
      }
      if (this.Old === item.id) {
        this.OldId = 0
      }
      var data
      if (item.role === 'purchaser') {
        data = {
          old_id: this.OldId,
          new_id: item.id,
          company_id: item.company_id
        }
      } else {
        data = {
          old_id: this.OldId,
          new_id: item.id
        }
      }
      await this.$store.dispatch('actionsSetDefaultInvoice', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultInvoice
      if (res.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
      this.GetAllinvoice()
    },
    openDeleteInvoice (item) {
      this.deleteDialog = true
      this.deleteTaxAdressData = item
    },
    async DeleteInvoice () {
      var data
      if (this.deleteTaxAdressData.role === 'purchaser') {
        data = {
          invoice_id: this.deleteTaxAdressData.id,
          company_id: this.deleteTaxAdressData.company_id
        }
      } else {
        data = {
          invoice_id: this.deleteTaxAdressData.id
        }
      }
      await this.$store.dispatch('actionsDeleteInvoice', data)
      var res = await this.$store.state.ModuleUser.stateDeleteInvoice
      if (res.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
        this.dialogDeleteSuccessTax = true
        this.GetAllinvoice()
        this.$store.commit('openLoader')
        setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
      }
      this.deleteDialog = false
    },
    async setDefaultAdress (item) {
      const data = {
        id: item.id,
        default_address: item.default_address
      }
      await this.$store.dispatch('actionDefaultUserAddress', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultUserAddress
      if (res.message === 'Update default address success') {
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
        this.userdetail = [...res.data]
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
      this.reShippingData = true
      this.getAddress()
    },
    addAddress () {
      const val = {
        building_name: '',
        default_address: '',
        detail: '',
        district: '',
        email: '',
        first_name: '',
        floor: '',
        house_no: '',
        id: '',
        last_name: '',
        moo_ban: '',
        moo_no: '',
        order_number: '',
        phone: '',
        province: '',
        room_no: '',
        soi: '',
        status: '',
        street: '',
        sub_district: '',
        user_id: '',
        yaek: '',
        zipcode: ''
      }
      this.reShippingData = true
      localStorage.setItem('AddressUserDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalAddress')
    },
    editAddress (val) {
      this.$store.commit('openLoader')
      const editData = {
        building_name: val.building_name,
        default_address: val.default_address,
        detail: val.detail,
        district: val.district,
        email: val.email,
        first_name: val.first_name,
        floor: val.floor,
        house_no: val.house_no,
        id: val.id,
        last_name: val.last_name,
        moo_ban: val.moo_ban,
        moo_no: val.moo_no,
        order_number: val.order_number,
        phone: val.phone,
        province: val.province,
        room_no: val.room_no,
        soi: val.soi,
        status: val.status,
        street: val.street,
        sub_district: val.sub_district,
        user_id: val.user_id,
        yaek: val.yaek,
        zipcode: val.zip_code,
        note_address: val.note_address
      }
      // console.log(editData.default_address)
      if (editData.default_address === 'Y') {
        this.reShippingData = true
      }
      localStorage.setItem('AddressUserDetail', Encode.encode(editData))
      this.EditAddressDetail = editData
      this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
      this.page = 'checkout'
      this.$EventBus.$emit('EditModalAddress')
    },
    openDialogQR () {
      this.dialogAwaitConfirm = !this.dialogAwaitConfirm
      this.DialogQR = !this.DialogQR
      // console.log(this.dates)
      // console.log(this.formattedTime)
    },
    // async confirmAddress () {
    //   this.DialogAddress = false
    // },
    async cancelChangeAddrss () {
      if (this.role.role === 'ext_buyer') {
        this.DialogAddress = false
      } else {
        this.DialogCompanyAddress = false
      }
    },
    async changeAddress () {
      if (this.role.role === 'ext_buyer') {
        this.DialogAddress = true
      } else {
        this.DialogCompanyAddress = true
      }
    },
    async changeAddressTax () {
      this.DialogTaxAddress = true
    },
    closeModalSuccess () {
      this.dialogSuccess = false
    },
    async goToQUCompany () {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var companyid = companyId.company.company_id
      const data = {
        company_id: companyid
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      await this.$store.dispatch('actionsAuthorityUser')
      var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
      var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
      var listcompany = responseposition.data.list_company
      for (let i = 0; i < listcompany.length; i++) {
        if (responsecompany.data.id === listcompany[i].company_id) {
          localStorage.removeItem('list_Company_detail')
          localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
        }
      }
      localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      if (this.MobileSize) {
        this.$router.push({ path: '/QUCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/QUCompany' }).catch(() => {})
      }
    },
    async Test (paymentType) {
      // console.log(paymentType)
      this.CreateOrder(paymentType)
      // else {
      // console.log(2)
      // this.GetCC(paymentType)
      // this.CreateOrder(paymentType)
      // }
      // else if (dataRole.role === 'ext_buyer' && this.itemsCart.is_JV === 'yes') {
      //   // console.log(3)
      //   this.CreateOrder(paymentType)
      // }
      this.dialogAwaitConfirm = false
    },
    async Confirm () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataToCheck = ''
      dataToCheck = {
        role_user: dataRole.role,
        seller_shop_id: this.itemsCart.seller_shop_id,
        company_id: this.itemsCart.company_id,
        customer_id: this.itemsCart.customer_id
      }
      var allHaveItemCode = true
      var messageCheckError = ''
      var i
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        if (element.product_type.length >= 1 && element.product_type === 'general') {
          this.countproductgen = true
        }
        if (element.item_code_pr_buyer === null) {
          allHaveItemCode = false
        }
      })
      // console.log(this.countproductgen)
      if (this.itemsCart.is_JV_buyer === 'yes' && !allHaveItemCode) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาเลือก Item Code PR</h3>'
        })
      } else {
        if (this.itemsCart.net_price >= 1) {
          if (this.radioPayment === 'radio-installment' && this.radioCreditTerm === 'No') {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'warning',
              html: '<h3>กรุณาเลือกระยะเวลาผ่อนชำระ</h3>'
            })
          } else if (this.radios === 'radio-2' && this.countproductgen === true && this.radioTransport === '' && this.itemsCart.shipping_method.length !== 0 && dataRole.role === 'ext_buyer') {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'warning',
              html: '<h3>กรุณาเลือกขนส่งในการจัดส่งสินค้า</h3>'
            })
            await this.EstimateCost()
            this.DialogTransport = true
          } else if (this.radios === 'radio-2' && this.radiostax === 'radiotax-2' && this.countproductgen === true && ((this.userdetail[0].detail === '' && this.userdetail[0].detail === null) || this.userdetail[0].default_address !== 'Y') && dataRole.role !== 'purchaser' && this.userdetail.length === 1) {
            if (this.userdetail.length === 1 && ((this.userdetail[0].detail === '' && this.userdetail[0].detail === null) || this.userdetail[0].default_address !== 'Y')) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h5>กรุณากรอกที่อยู่ในการจัดส่งสินค้าให้สมบูรณ์ หรือ</br>กด <b>บันทึก</b> เพื่อตั้งค่าที่อยู่เริ่มต้น</h5>'
              })
              // this.userdetail[0].default_address = 'Y'
              // this.userdetail[0].status = 'Y'
              this.editAddress(this.userdetail[0])
            }
            if (this.userdetail.length === 1 && ((this.userdetail[0].detail === '' && this.userdetail[0].detail === null) || this.userdetail[0].default_address !== 'Y')) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h5>กรุณากรอกที่อยู่ในการจัดส่งสินค้าให้สมบูรณ์ หรือ</br>กด <b>บันทึก</b> เพื่อตั้งค่าที่อยู่เริ่มต้น</h5>'
              })
              // this.userdetail[0].default_address = 'Y'
              // this.userdetail[0].status = 'Y'
              this.editAddress(this.userdetail[0])
            }
          } else if (this.radios === 'radio-2' && this.radiostax === 'radiotax-1' && this.countproductgen === true && this.itemsCart.isEtax === 'yes') {
            // if (this.invoicedetail.length === 0 && this.userdetail.length === 0) {
            //   this.$swal.fire({
            //     showConfirmButton: false,
            //     timer: 2500,
            //     timerProgressBar: true,
            //     icon: 'warning',
            //     html: '<h3>ไม่มีที่อยู่ในการจัดส่งสินค้า และ ไม่มีที่อยู่ในการออกใบกำกับภาษี</h3>'
            //   })
            // }
            if (this.userdetail.length === 0) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h3>ไม่มีที่อยู่ในการจัดส่งสินค้า</h3>'
              })
              this.DialogAddress = true
            } else if (this.invoicedetail.length === 0) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h3>ไม่มีที่อยู่ในการออกใบกำกับภาษี</h3>'
              })
              this.DialogTaxAddress = true
            } else if (this.userdetail.length !== 0 && this.invoicedetail.length !== 0) {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'ไม่พบตะกร้า'
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              }
            }
          } else if (this.radios === 'radio-2' && this.radiostax === 'radiotax-2' && this.countproductgen === true && dataRole.role !== 'purchaser') {
            if (this.userdetail.length === 0) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h4>ไม่มีที่อยู่ในการจัดส่งสินค้า</h4>'
              })
              this.DialogAddress = true
            } else if (this.userdetail.length === 1 && ((this.userdetail[0].detail === '' && this.userdetail[0].detail === null) || this.userdetail[0].default_address !== 'Y')) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h5>กรุณากรอกที่อยู่ในการจัดส่งสินค้าให้สมบูรณ์ หรือ</br>กด <b>บันทึก</b> เพื่อตั้งค่าที่อยู่เริ่มต้น</h5>'
              })
              // this.userdetail[0].default_address = 'Y'
              // this.userdetail[0].status = 'Y'
              this.editAddress(this.userdetail[0])
            } else {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'ไม่พบตะกร้า'
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              }
            }
          } else if (this.radios === 'radio-1' && this.radiostax === 'radiotax-1' && this.countproductgen === true && this.itemsCart.isEtax === 'yes') {
            if (this.invoicedetail.length === 0) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h3>ไม่มีที่อยู่ในการออกใบกำกับภาษี</h3>'
              })
              this.DialogTaxAddress = true
            }
            if (this.invoicedetail.length !== 0) {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'ไม่พบตะกร้า'
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              }
            }
          } else if (this.radiostax === 'radiotax-1' && this.countproductgen !== true && this.itemsCart.isEtax === 'yes') {
            if (this.invoicedetail.length === 0) {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                html: '<h3>ไม่มีที่อยู่ในการออกใบกำกับภาษี</h3>'
              })
              this.DialogTaxAddress = true
            }
            if (this.invoicedetail.length !== 0) {
              await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
              const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
              if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
                this.dialogAwaitConfirm = true
                this.typeButton = 'confirm'
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
                for (i = 0; i < response.data.product_free.length; i++) {
                  messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else if (response.message === 'ไม่พบตะกร้า') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'ไม่พบตะกร้า'
                })
                this.backstep()
              } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              } else {
                for (i = 0; i < response.data.product_list.length; i++) {
                  messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
                }
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'warning',
                  text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
                })
                this.backstep()
              }
            }
          } else {
            await this.$store.dispatch('actionsCheckStockBeforeCreateOrder', dataToCheck)
            const response = await this.$store.state.ModuleCart.stateCheckStockBeforeCreateOrder
            if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
              this.dialogAwaitConfirm = true
              this.typeButton = 'confirm'
            } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
              for (i = 0; i < response.data.product_free.length; i++) {
                messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
              }
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
              })
              this.backstep()
            } else if (response.message === 'ไม่พบตะกร้า') {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: 'ไม่พบตะกร้า'
              })
              this.backstep()
            } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
              for (i = 0; i < response.data.product_list.length; i++) {
                messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
              }
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
              })
              this.backstep()
            } else {
              for (i = 0; i < response.data.product_list.length; i++) {
                messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
              }
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: 'warning',
                text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาลบสินค้าออกจากรถเข็น'
              })
              this.backstep()
            }
          }
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>ราคารวมทั้งหมดต้องมีค่ามากกว่า 0 บาท</h3>'
          })
        }
      }
      this.getCart()
    },
    async CancelAwaitConfirm () {
      this.dialogAwaitConfirm = false
      this.getCart()
    },
    async DeleteAddress (item) {
      this.dialogAwaitConfirm = true
      this.deleteAdressData = item
      this.typeButton = 'delete'
    },
    async deleteAddress () {
      const data = {
        id: this.deleteAdressData.id
      }
      await this.$store.dispatch('actionDeleteUserAddress', data)
      const res = await this.$store.state.ModuleUser.stateDeleteUserAddress
      if (res.message === 'Delete user address success' || res.message === 'Delete user address success and change default address') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getAddress()
        this.deleteDialog = false
      } else {
        this.$swal.fire({ icon: 'error', title: `${this.$t('CheckOut.FailedToDeleteTheShippingAddress')}`, showConfirmButton: false, timer: 1500 })
      }
      this.dialogAwaitConfirm = false
      this.dialogSuccess = true
    },
    async getAddress (item) {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      await this.$store.dispatch('actionListUserAddress')
      this.userdetail = await this.$store.state.ModuleUser.stateListUserAddress
      this.AddressNew = this.userdetail.data
      if (item) {
        if (item.default_address === 'Y' || item.default === 'Y') {
          this.ShippingData = []
          this.radioPayment = ''
        }
      }
      if (dataRole.role === 'ext_buyer') {
        this.userdetail.data.forEach(element => {
          if (element.default_address === 'Y') {
            // console.log('get address', element)
            this.CartAddress = [
              {
                id: element.id,
                email: element.email,
                first_name: element.first_name,
                last_name: element.last_name,
                detail: element.detail,
                house_no: element.house_no,
                room_no: element.room_no,
                floor: element.floor,
                building_name: element.building_name,
                moo_ban: element.moo_ban,
                moo_no: element.moo_no,
                soi: element.soi,
                yaek: element.yaek,
                street: element.street,
                sub_district: element.sub_district,
                district: element.district,
                province: element.province,
                zip_code: element.zip_code,
                note_address: element.note_address,
                phone: element.phone,
                phone_ext: '',
                status: element.status
              }
            ]
          }
        })
        await this.getCart()
      } else {
        await this.getCompanyAddress()
        this.comAddress.forEach(element => {
          if (element.default === 'Y') {
            this.CartAddress = [
              {
                id: element.id,
                email: element.email,
                company_id: element.company_id,
                first_name: '',
                last_name: '',
                name_th: element.name_th,
                detail: element.detail,
                house_no: element.house_no,
                room_no: element.room_no,
                floor: element.floor,
                building_name: element.building_name,
                moo_ban: element.moo_ban,
                moo_no: element.moo_no,
                soi: element.soi,
                yaek: element.yaek,
                street: element.street,
                sub_district: element.sub_district,
                district: element.district,
                province: element.province,
                zip_code: element.zip_code,
                note_address: element.note_address,
                phone: element.phone,
                phone_ext: '',
                status: element.status
              }
            ]
          }
        })
        await this.getCart()
      }
      // console.log(userdetail)
      await this.itemsCart.choose_list[0].product_list.forEach(element => {
        this.SellerShopID = element.seller_shop_id
        if (element.product_type.length >= 1 && element.product_type === 'general') {
          this.countproductgen = true
        }
      })
      if (this.itemsCart.isEtax === 'yes') {
        this.radiostax = 'radiotax-1'
        // this.DialogTaxAddress = true
      }
      if (this.role.role === 'ext_buyer') {
        // this.extbuyerrole = true
        if (this.userdetail.data.length === 0 && this.countproductgen === true) {
          // this.DialogAddress = true
        }
        if (this.userdetail.data.length === 1 && this.userdetail.data[0].detail === '' && this.countproductgen === true) {
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 3000,
          //   timerProgressBar: true,
          //   icon: 'warning',
          //   html: '<h3>กรุณากรอกข้อมูลที่อยู่ในการจัดการส่งสินค้าให้สมบูรณ์ </h3>'
          // })
          // this.userdetail.data[0].default_address = 'Y'
          // this.userdetail.data[0].status = 'Y'
          // this.editAddress(this.userdetail.data[0])
          // // this.getAddress()
        } else if (this.userdetail.data.length === 1 && this.userdetail.data[0].detail !== '') {
          setTimeout(() => { this.checkIsEtax() }, 3000)
        }
      }
      // this.getAddress()
      this.userdetail = [...this.userdetail.data]
      // this.userdetail.forEach(element => {
      //   var x = element.phone.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/)
      //   this.telnumber = !x[2] ? x[1] : x[1] + '-' + x[2] + (x[3] ? '-' + x[3] : '')
      //   if (element.first_name === '' && element.last_name === '') {
      //     this.fullname = '-'
      //   } else {
      //     this.fullname = element.first_name + ' ' + element.last_name
      //   }
      // })
      // await this.getCart()
      this.$store.commit('closeLoader')
    },
    CheckSpacebarName (e) {
      // console.log(e)
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '' || e.target.value === ' ')) {
        e.preventDefault()
      }
    },
    async openTypeDoc () {
      this.tax_id = localStorage.getItem('tax_id')
      var body = {
        tax_id: this.tax_id
      }
      // console.log('body', body)
      await this.$store.dispatch('actionsGetDocumentType', body)
      var res = await this.$store.state.ModuleCart.stateGetDocumentType
      if (res.message === 'Show document type success') {
        this.itemTypeDoc = res.data
      } else {
        this.itemTypeDoc = []
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      // const [year, month, day] = date.split('-')
      // const yearChange = parseInt(year) + 543
      // return `${day}/${month}/${yearChange}`
      return new Date(date).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' })
    },
    setValueContractStartDate (val, index) {
      this.$refs.dialogContractStartDate[index].save(val)
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      // clear end date กรณีที่ start date เปลี่ยนแปลง
      var setMin = new Date(val)
      this.date1 = new Date(setMin.setDate(setMin.getDate() + 1)).toISOString().substr(0, 10)
      this.setMinDateContractEndDate = this.date1
      // console.log('date1', this.date1)
      this.contractEndDate = ''
      this.searchContractEndDate = ''
    },
    setValueContractStartDate1 (val, index) {
      // console.log('123', val)
      this.$refs.dialogContractStartDate1[index].save(val)
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      // clear end date กรณีที่ start date เปลี่ยนแปลง
      var setMin = new Date(val)
      this.date1 = new Date(setMin.setDate(setMin.getDate() + 1)).toISOString().substr(0, 10)
      this.setMinDateContractEndDate = this.date1
      // console.log('date1', this.date1)
      this.contractEndDate = ''
      this.searchContractEndDate = ''
    },
    setValueContractEndDate (val, chooseindex) {
      this.$refs.dialogContractEndDate[chooseindex].save(val)
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
    },
    setValueDate (val) {
      // this.$refs.contractDate.save(val)
      this.contractDate = this.formatDateToShow(val)
      if (this.dates !== this.today) {
        this.timeselect = ''
        this.timeselecttoday = '1'
      } else {
        this.timeselecttoday = ''
        this.timeselect = '1'
      }
    },
    async closeDialogQR () {
      this.$store.commit('openLoader')
      this.DialogQR = false
      this.CloseDialog = true
      var companyId = ''
      if (this.role.role !== 'ext_buyer') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyId = companyDataID.company.company_id
        } else {
          companyId = -1
        }
        const data = {
          company_id: companyId
        }
        await this.$store.dispatch('actionsDetailCompany', data)
        await this.$store.dispatch('actionsAuthorityUser')
        var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
        var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
        var listcompany = responseposition.data.list_company
        for (let i = 0; i < listcompany.length; i++) {
          if (responsecompany.data.id === listcompany[i].company_id) {
            localStorage.removeItem('list_Company_detail')
            localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
          }
        }
        localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      }
      if (this.role.role === 'ext_buyer') {
        this.$store.commit('closeLoader')
        if (!this.MobileSize) {
          this.$router.push({ path: '/pobuyerProfile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => { })
        }
      } else {
        this.$store.commit('closeLoader')
        if (!this.MobileSize) {
          this.$router.push({ path: `/orderDetailCompany?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
        }
      }
    },
    closeModalContractStartDate () {
      this.modalContractStartDate = false
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.date1 = ''
    },
    closeModalContractEndDate () {
      this.modalContractEndDate = false
      // this.date1 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      // this.searchContractEndDate = ''
      this.date1 = this.setMinDateContractEndDate
      this.contractEndDate = ''
    },
    ChangeDiscount (price) {
      var onedata
      this.disableButtonPay = false
      if (this.discountBaht !== '') {
        if (parseFloat(this.discountBaht) < parseFloat(price)) {
          // console.log('value =====>', this.discountCode, this.discountBaht)
          onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
          onedata.cartData.note_of_code = this.discountCode
          onedata.cartData.code_discount = this.discountBaht
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.getCart()
        } else {
          this.disableButtonPay = true
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณากรอกส่วนลดไม่เกินราคา</h3>'
          })
        }
      } else {
        onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.cartData.note_of_code = this.discountCode
        onedata.cartData.code_discount = this.discountBaht
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.getCart()
      }
      // console.log(onedata)
    },
    openModalPayment () {
      this.modalPayment = true
    },
    async checkAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
      if (dataRole.role === 'ext_buyer') {
        if (this.propsAddress[0].address_data.length !== 0) {
          if (this.propsAddress[0].address_data.length === 1) {
            localStorage.setItem('AddressUserDetail', Encode.encode(this.propsAddress[0].address_data[0]))
            if (this.propsAddress[0].address_data[0].status === 'Y' && (this.propsAddress[0].address_data[0].default_address === 'N' || this.propsAddress[0].address_data[0].default_address === null)) {
              // this.$swal.fire({
              //   showConfirmButton: false,
              //   timer: 2500,
              //   timerProgressBar: true,
              //   icon: 'warning',
              //   html: '<h3>คุณยังไม่ได้ตั้งค่าที่อยู่เป็นค่าเริ่มต้น</h3>'
              // })
              // if (this.MobileSize) {
              //   this.$router.push({ path: '/addressProfileMobile' })
              // } else {
              //   this.$router.push({ path: '/addressProfile' })
              // }
            }
          } else {
            this.propsAddress[0].address_data.forEach(element => {
              if (element.default_address === 'Y') {
                if (element.status === 'N') {
                  localStorage.setItem('AddressUserDetail', Encode.encode(element))
                  this.EditAddressDetail = element
                  this.titleAddress = 'เพิ่มที่อยู่จัดส่งสินค้า'
                  this.page = 'checkout'
                  this.$EventBus.$emit('EditModalAddress')
                }
              }
            })
          }
        }
      }
    },
    async checkStatus () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      if (this.propsAddress[0].address_data.length !== 0) {
        this.propsAddress[0].address_data.forEach(element => {
          if (element.default_address === 'Y') {
            if (element.status === 'N') {
              this.backstep()
            }
          }
        })
      }
    },
    async getItemCodePr () {
      this.tax_id = localStorage.getItem('tax_id')
      await this.$store.dispatch('actionsListItemCodePr', this.tax_id)
      var res = await this.$store.state.ModuleCart.stateListItemCodePr
      if (res.message === 'Show section success') {
        // console.log('res.data', res.data)
        this.itemCodePrList = res.data
      }
    },
    async getCart (itemCoupon, itemPoint) {
      this.$store.commit('openLoader')
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      if (localStorage.getItem('CouponPlatform') !== null) {
        this.CodePlatform = JSON.parse(localStorage.getItem('CouponPlatform'))
      } else {
        this.CodePlatform = []
      }
      if (localStorage.getItem('CouponPlatformShipping') !== null) {
        this.CodePlatformShipping = JSON.parse(localStorage.getItem('CouponPlatformShipping'))
      } else {
        this.CodePlatformShipping = []
      }
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (itemPoint !== undefined) {
        if (onedata.cartData.point.length === 0 || itemPoint.length === 0) {
          this.PointData = 0
          itemPoint = 0
        }
      }
      var res
      // var CartAddress
      this.checkOwnShop = 'N'
      this.cartData = onedata.cartData
      this.cartData.address = this.CartAddress
      if (itemPoint !== null && itemPoint !== undefined && itemPoint !== 0 && Array.isArray(itemPoint)) {
        this.cartData.point = Number(itemPoint[0].total_point)
        this.PointData = Number(itemPoint[0].total_point)
      } else {
        var Point = Number(this.PointData)
        this.cartData.point = Number(this.PointData)
        this.PointData = Point
      }
      if (itemCoupon !== null && itemCoupon !== undefined) {
        // this.cartData.coupon = itemCoupon
        this.CouponData = itemCoupon.filter(coupon => coupon.seller_shop_id !== -1)
      } else {
        if (this.CouponData.length !== 0) {
          this.CouponData = this.CouponData.filter(coupon => coupon.seller_shop_id !== -1)
        } else {
          this.CouponData = []
        }
      }
      if (this.cartData.type_shipping === '') {
        this.cartData.type_shipping = 'online'
      }
      if (this.CodePlatform.length !== 0) {
        const updatedCoupons = this.CodePlatform.map(coupon => {
          const updatedCoupon = {
            ...coupon,
            coupon_id: coupon.id
          }
          delete updatedCoupon.id
          return updatedCoupon
        })
        this.CodePlatform = updatedCoupons
        // this.cartData.coupon = updatedCoupons
      }
      if (this.CodePlatformShipping.length !== 0) {
        const updatedCoupons = this.CodePlatformShipping.map(coupon => {
          const updatedCoupon = {
            ...coupon,
            coupon_id: coupon.id
          }
          delete updatedCoupon.id
          return updatedCoupon
        })
        this.CodePlatformShipping = updatedCoupons
        // this.cartData.coupon = updatedCoupons
      }
      // this.cartData.coupon = [...this.CouponData, ...this.CodePlatform]
      this.CouponData = [...this.CouponData, ...this.CodePlatform, ...this.CodePlatformShipping]
      this.cartData.coupon = this.CouponData
      // console.log('this.CodePlatform', this.CodePlatform)
      // console.log('this.CouponData', this.CouponData)
      // this.cartData.address = CartAddress
      if (dataRole.role === 'purchaser') {
        if (this.discountBahtB2B && !this.discountPercentB2B) {
          this.cartData.b2b_discount_type = 'baht'
          if (this.itemsCart.total_price_after_discount != null &&
          Number(this.B2BDiscountBaht) > Number(this.itemsCart.total_price_after_discount)) {
            this.cartData.b2b_discount = this.itemsCart.total_price_after_discount
          } else {
            this.cartData.b2b_discount = this.B2BDiscountBaht
          }
        } else if (!this.discountBahtB2B && this.discountPercentB2B) {
          this.cartData.b2b_discount_type = 'percent'
          this.cartData.b2b_discount = this.B2BDiscountPercent
        } else if (!this.discountBahtB2B && !this.discountPercentB2B) {
          this.cartData.b2b_discount_type = 'baht'
          this.cartData.b2b_discount = 0
        }
        if (this.sameAmountPerMonth) {
          this.cartData.same_price_installment = 'yes'
        } else {
          this.cartData.same_price_installment = 'no'
        }
      }
      if (this.reShippingData === false && this.radios === 'radio-2') {
        Vue.set(this.cartData, 'shipping_data', this.ShippingData)
        this.cartData.type_shipping = 'online'
      } else {
        if (this.CouponData !== undefined && this.CouponData.length !== 0) {
          var hasFreeShippingPlatform = false
          var hasFreeShipping = false
          this.CouponData = this.CouponData.filter(coupon => {
            if (coupon.coupon_type === 'free_shipping') {
              if (coupon.seller_shop_id === -1) {
                hasFreeShippingPlatform = true
              } else {
                hasFreeShipping = true
              }
              return false
            }
            return true
          })
          if (hasFreeShippingPlatform) {
            this.$EventBus.$emit('clearCouponPlatform')
          }
          if (hasFreeShipping) {
            this.$EventBus.$emit('clearCoupon')
          }
          this.cartData.coupon = this.CouponData
        }
        this.cartData.type_shipping = 'pickup'
        this.ShippingData = []
        Vue.set(this.cartData, 'shipping_data', this.ShippingData)
        this.radioTransport = ''
        this.radioCreditTerm = 'No'
        this.radioPayment = ''
        this.reShippingData = false
      }
      if (Object.prototype.hasOwnProperty.call(onedata, 'cartDataSpecialPrice')) {
        if (onedata.cartDataSpecialPrice === 'yes') {
          await this.$store.dispatch('ActionGetCartSpecialPrice', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCartSpecialPrice
        } else {
          await this.$store.dispatch('ActionGetCart', this.cartData)
          res = await this.$store.state.ModuleCart.stateGetCart
        }
      }
      if (res.message === 'Get cart success') {
        this.itemsCart = res.data
        if (this.itemsCart && this.itemsCart.is_Tae === 'yes') {
          this.selectedCreditTerm = this.itemsCart.choose_list[0].setting_partner_data[0].credit_term
        }
        if (
          this.B2BDiscountBaht != null &&
          this.itemsCart.total_price_after_discount != null &&
          Number(this.B2BDiscountBaht) > Number(this.itemsCart.total_price_after_discount)
        ) {
          this.B2BDiscountBaht = this.itemsCart.total_price_after_discount
          this.getCart()
        }
        if (this.itemsCart.choose_list[0].pay_type === 'onetime' && dataRole.role === 'purchaser') {
          this.selectinstallment = 1
          this.sameAmountPerMonth = true
          this.cartData.same_price_installment = 'yes'
        }
        if (this.itemsCart.shipping_method.length === 0 && parseInt(this.itemsCart.shipping_price) === 0) {
          const freeShippingCoupon = this.CouponData.some(coupon => coupon.coupon_type === 'free_shipping')
          this.noShipping = freeShippingCoupon
        } else {
          this.noShipping = false
        }
        this.rawInstallmentAmounts = this.installmentAmounts.map(amount => this.formatAmount(amount))
        // this.itemsCart.choose_list[0].product_list.forEach(element => {
        //   if (element.item_code_pr_buyer === null) {
        //     element.item_code_pr_buyer = ''
        //   }
        // })
        // console.log(this.itemsCart)
        if (res.data.check_own_shop === 'yes') {
          this.checkOwnShop = 'Y'
        } else {
          this.checkOwnShop = 'N'
        }
        if (this.itemsCart.total_price_no_vat >= 50000) {
          this.contractSet = true
        }
        if (this.sameAmountPerMonth) {
          this.updateInstallmentAmounts()
        }
        // this.googleSentData()
        if (this.itemsCart.address_data.length !== 0) {
          if (dataRole.role === 'purchaser') {
            var addressPurchaser = ''
            this.choose_list = this.itemsCart.choose_list[0].pay_type
            this.address_data = this.itemsCart.address_data[0]
            this.Fullname = this.address_data.first_name + ' ' + this.address_data.last_name
            // addressPurchaser = this.address_data.detail + ' ' + 'แขวง/ตำบล' + ' ' + this.address_data.district + ' ' + 'จังหวัด' + ' ' + this.address_data.province + ' ' + this.address_data.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + this.address_data.phone
            this.Address = addressPurchaser
          } else {
            this.itemsCart.address_data.forEach(element => {
              // console.log(element)
              if (element.default_address === 'Y' || element.default_address !== undefined) {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address = ''
                address = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address
              } else {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address1 = ''
                address1 = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address1
              }
            })
          }
        } else {
          this.Fullname = ''
          this.Address = 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'
        }
        // get user data
        // var data = {
        //   role_user: dataRole.role
        // }
        // await this.$store.dispatch('actionsUserDetailPage', data)
        // var response = await this.$store.state.ModuleUser.stateUserDetailPage
        // get admin data
        const sendId = { user_id: this.user.id }
        await this.$store.dispatch('ActionGetAdminData', sendId)
        var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
        var adminStatus = false
        if (responseAdmin.data.length !== 0) {
          adminStatus = true
        } else {
          adminStatus = false
        }
        if (adminStatus === true) {
          this.sentDataPPL()
        }
        this.overlay = false
        this.$store.commit('closeLoader')
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ใส่ข้อมูลไม่ครบ'
        })
        this.backstep()
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'SERVER ERROR'
        })
        this.backstep()
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง'
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ'
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${'รหัสสินค้า' + ' ' + res.data[0].sku + ' ' + 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่'}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        this.backstep()
      } else if (res.message === 'ไม่พบที่อยู่สำหรับจัดส่งสินค้า กรุุณาทำการเพิ่มข้อมูลที่อยู่ใหม่อีกครั้ง') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/addressProfileMobile' })
        } else {
          this.$router.push({ path: '/addressProfile' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.SelectCouponOrPoint = true
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else if (res.message === 'Company User Permission Not Found.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        })
        this.backstep()
      } else if (res.message === 'Not found cart') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 2500,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        // })
        this.backstep()
        this.goHomePage()
      } else if (res.message.startsWith('คูปองไม่สามารถใช้ได้')) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'คูปองไม่สามารถใช้งานได้ กรุณาเลือกคูปองใหม่'
        })
        if (res.data.seller_shop_id !== -1) {
          this.closeCoupon(res.data)
        } else {
          if (res.data.coupon_type === 'free_shipping') {
            this.clearCodePlatformShipping()
          } else if (res.data.coupon_type === 'discount') {
            this.clearCodePlatform()
          }
        }
      } else {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        await this.$EventBus.$emit('CancleBookCouponCheckout')
        this.backstep()
      }
      this.$store.commit('closeLoader')
    },
    async updateSelectPr () {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = []
      var glAccount = []
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        // console.log(element)
        glAccount = this.itemCodePrList.filter(item => item.material_code === element.item_code_pr_buyer)
        // console.log(element)
        data.push({
          product_id: element.product_id,
          quantity: element.quantity,
          price: element.revenue_vat,
          item_code_pr_buyer: element.item_code_pr_buyer !== undefined || element.item_code_pr_buyer !== null ? element.item_code_pr_buyer : null,
          type_budget_gl_account: glAccount.length !== 0 ? glAccount[0].gl_account : null,
          product_attribute_id: element.product_attribute_detail.product_attribute_id,
          revenue_default: element.revenue_default,
          revenue_default_with_discount: element.revenue_default_with_discount,
          revenue_vat: element.revenue_vat,
          vat_revenue: element.vat_revenue
        })
      })
      onedata.cartData.product_to_calculate = data
      localStorage.setItem('oneData', Encode.encode(onedata))
      await this.getCart()
    },
    googleSentData () {
      for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
        this.googleItem.push({
          item_id: this.itemsCart.choose_list[0].product_list[i].sku,
          item_name: this.itemsCart.choose_list[0].product_list[i].product_name,
          currency: 'THB',
          price: this.itemsCart.choose_list[0].product_list[i].net_price,
          quantity: this.itemsCart.choose_list[0].product_list[i].quantity
        })
      }
      // window.dataLayer = window.dataLayer || []
      // window.dataLayer.push({
      //   event: 'begin_checkout',
      //   // currency: 'THB',
      //   // value: this.itemsCart.net_price,
      //   ecommerce: {
      //     items: this.googleItem
      //   }
      // })
      // this.$analytics.fbq.event('AddToCart', {
      //   productList: this.itemsCart.choose_list[0].product_list,
      //   productTotalPrice: this.itemsCart.net_price
      // })
    },
    backstep () {
      // this.$router.go(-1)
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.cartDataSpecialPrice === 'yes') {
        this.$router.replace({ path: '/specialPriceBuyerRequest' }).catch(() => { this.$router.go(-1) })
      } else {
        this.$router.replace({ path: '/shoppingcart' }).catch(() => { this.$router.go(-1) })
      }
    },
    confirmCreateOrder () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (dataRole.role === 'purchaser') {
        this.$swal.fire({
          icon: 'warning',
          html: '* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>รายการซื้อของฉัน</u></b>',
          title: '<h5>ยืนยันการสั่งซื้อหรือไม่</h5>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
          // cancelButtonColor: '#d33'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder()
            // this.googleSentData()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      } else if (dataRole.role === 'ext_buyer') {
        this.$swal.fire({
          icon: 'warning',
          showCancelButton: true,
          title: '<h5>ยืนยันการสั่งซื้อหรือไม่</h5>',
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
          // cancelButtonColor: '#aaa'
        }).then((result) => {
          if (result.isConfirmed) {
            this.CreateOrder()
            // this.googleSentData()
          } else if (result.isDismissed) {
            this.getCart()
          }
        }).catch(() => {
        })
      }
    },
    confirmCreateOrderMobile (paymentType) {
      if (this.$refs.orderListDetails.validate(true)) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (dataRole.role === 'purchaser') {
          this.$swal.fire({
            icon: 'warning',
            html: '* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>ใบเสนอราคา</u></b>',
            title: '<h5>ยืนยันการสั่งซื้อหรือไม่</h5>',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
            // cancelButtonColor: '#d33'
          }).then((result) => {
            if (result.isConfirmed) {
              this.CreateOrder(paymentType)
              // this.googleSentData()
            } else if (result.isDismissed) {
              this.getCart()
            }
          }).catch(() => {
          })
        } else if (dataRole.role === 'ext_buyer') {
          this.$swal.fire({
            icon: 'warning',
            showCancelButton: true,
            title: '<h5>ยืนยันการสั่งซื้อหรือไม่</h5>',
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
            // cancelButtonColor: '#aaa'
          }).then((result) => {
            if (result.isConfirmed) {
              this.CreateOrder(paymentType)
              // this.googleSentData()
            } else if (result.isDismissed) {
              this.getCart()
            }
          }).catch(() => {
          })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    // async CreateOrderSyncTaxaddress () {
    //   var data = {
    //     invoice_id: this.taxinvoiceAddress[0].invoice_id,
    //     cart_id: this.itemsCart.id
    //   }
    //   await this.$store.dispatch('ActionCreateOrderSyncTaxaddress', data)
    // },
    async GetPersonal () {
      this.$store.commit('openLoader')
      var companyId = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var company = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyId = company.company.company_id
      } else {
        companyId = -1
      }
      // console.log('companyId--------->', companyId.company.company_id)
      const data = {
        company_id: companyId
      }
      // console.log(data)
      await this.$store.dispatch('actionsDetailCompany', data)
      var companyData = await this.$store.state.ModuleAdminManage.stateDetailCompany
      if (companyData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        if (this.itemsCart.is_JV === 'yes' && this.itemsCart.is_JV_buyer === 'yes') {
          this.Name_Buyer = companyData.data.json_personal[0].purchasing_chief[0].name
          this.Phone_Buyer = companyData.data.json_personal[0].purchasing_chief[0].phone
          this.Position_Buyer = companyData.data.json_personal[0].purchasing_chief[0].position
          this.Email_Buyer = companyData.data.json_personal[0].purchasing_chief[0].email
          this.Name_Audit1 = companyData.data.json_personal[0].inspectors_one[0].name
          this.Phone_Audit1 = companyData.data.json_personal[0].inspectors_one[0].phone
          this.Position_Audit1 = companyData.data.json_personal[0].inspectors_one[0].position
          this.Email_Audit1 = companyData.data.json_personal[0].inspectors_one[0].email
          this.Name_Audit2 = companyData.data.json_personal[0].inspectors_two[0].name
          this.Phone_Audit2 = companyData.data.json_personal[0].inspectors_two[0].phone
          this.Position_Audit2 = companyData.data.json_personal[0].inspectors_two[0].position
          this.Email_Audit2 = companyData.data.json_personal[0].inspectors_two[0].email
          this.buyer_name = companyData.data.json_personal[0].json_buyer[0].name ? companyData.data.json_personal[0].json_buyer[0].name : ''
          this.buyer_phone = companyData.data.json_personal[0].json_buyer[0].phone ? companyData.data.json_personal[0].json_buyer[0].phone : ''
          this.buyer_position = companyData.data.json_personal[0].json_buyer[0].position ? companyData.data.json_personal[0].json_buyer[0].position : ''
          this.buyer_email = companyData.data.json_personal[0].json_buyer[0].email ? companyData.data.json_personal[0].json_buyer[0].email : ''
          this.Name_SupplyBoard1 = companyData.data.json_personal[0].supply_board_one[0].name ? companyData.data.json_personal[0].supply_board_one[0].name : ''
          this.Phone_SupplyBoard1 = companyData.data.json_personal[0].supply_board_one[0].phone ? companyData.data.json_personal[0].supply_board_one[0].phone : ''
          this.Position_SupplyBoard1 = companyData.data.json_personal[0].supply_board_one[0].position ? companyData.data.json_personal[0].supply_board_one[0].position : ''
          this.Email_SupplyBoard1 = companyData.data.json_personal[0].supply_board_one[0].email ? companyData.data.json_personal[0].supply_board_one[0].email : ''
          this.Name_SupplyBoard2 = companyData.data.json_personal[0].supply_board_two[0].name ? companyData.data.json_personal[0].supply_board_two[0].name : ''
          this.Phone_SupplyBoard2 = companyData.data.json_personal[0].supply_board_two[0].phone ? companyData.data.json_personal[0].supply_board_two[0].phone : ''
          this.Position_SupplyBoard2 = companyData.data.json_personal[0].supply_board_two[0].position ? companyData.data.json_personal[0].supply_board_two[0].position : ''
          this.Email_SupplyBoard2 = companyData.data.json_personal[0].supply_board_two[0].email ? companyData.data.json_personal[0].supply_board_two[0].email : ''
          this.companydata = companyData.data
          this.companyAddressData = companyData.data.company_address
        } else {
          this.companydata = companyData.data
          this.companyAddressData = companyData.data.company_address
        }
      }
      // console.log(this.companyAddressData)
      this.$store.commit('closeLoader')
    },
    // async EditPersonal () {
    //   await this.$store.dispatch('actionsEditCompany', this.Detail)
    //   var response = await this.$store.state.ModuleAdminManage.stateEditCompany
    //   console.log('response-------->', response)
    // },
    async GetEWTH (paymentTypeData) {
      this.$store.commit('openLoader')
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var res
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      if (this.CouponData.length === 0 || this.CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0) {
        resCoupon = {
          message: 'สามารถใช้คูปองได้'
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: -1
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        this.closeCoupon(this.itemsCart.choose_list[0])
        this.$store.commit('closeLoader')
      } else {
        if (paymentType === 'cashPayment') {
          if (dataRole.role === 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              shipping_data: this.ShippingData,
              installment_month: this.radioCreditTerm === 'No' ? '' : this.radioCreditTerm
            }
          } else {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              com_perm_id: onedata.cartData.com_perm_id,
              company_id: companyId.company.company_id,
              start_date_contract: this.searchContractStartDate,
              end_date_contract: this.searchContractEndDate,
              contract_service: this.contractSet === true ? 'Y' : 'N',
              remark: this.reason,
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              shipping_data: this.ShippingData,
              installment_month: this.radioCreditTerm === 'No' ? '' : this.radioCreditTerm
            }
            // if (this.radios === 'radio-1') {
            //   localStorage.setItem('DatePickUp', Encode.encode(this.dates))
            //   localStorage.setItem('TimePickUp', Encode.encode(this.formattedTime))
            // }
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
          if (res.result === 'SUCCESS') {
            localStorage.removeItem('sale_order')
            // if (this.CouponData.length !== 0) {
            //   var couponData = {
            //     counpon_id: this.CouponData[0].coupon_id,
            //     order_number: res.data.payment_transaction_number
            //   }
            //   // console.log('couponData', couponData)
            //   await this.$store.dispatch('actionsUseCoupon', couponData)
            //   await this.$store.state.ModuleCart.stateUseCoupon
            // }
            var resEWTH
            // var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
            // if (this.radioPayment !== 'radio-installment') {
            //   this.radioCreditTerm = ''
            // }
            data = {
              payment_transaction_number: res.data.payment_transaction_number
            }
            await this.$store.dispatch('actionsEWTH', data)
            resEWTH = await this.$store.state.ModuleCart.stateEWTH
            if (resEWTH.result === 'SUCCESS') {
              localStorage.setItem('PaymentData', Encode.encode(resEWTH.data))
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'success',
                text: 'สั่งสินค้าเรียบร้อย'
              })
              this.$router.push('/').catch(() => {})
            } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: 'ไม่สามารถใช้คูปองได้'
              })
              this.$EventBus.$emit('clearPoint')
              this.$EventBus.$emit('clearCoupon')
              this.dialogAwaitConfirm = false
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
              })
              this.backstep()
            }
          } else if (res.code === 400) {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            this.backstep()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
            })
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async GetCC (paymentTypeData) {
      this.$store.commit('openLoader')
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var res
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      if (this.CouponData.length === 0 || this.CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0) {
        resCoupon = {
          message: 'สามารถใช้คูปองได้'
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: -1
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        this.closeCoupon(this.itemsCart.choose_list[0])
        this.$store.commit('closeLoader')
      } else {
        if (paymentType === 'cashPayment') {
          if (dataRole.role === 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              shipping_data: this.ShippingData,
              installment_month: this.radioCreditTerm === 'No' ? '' : this.radioCreditTerm
            }
          } else {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              com_perm_id: onedata.cartData.com_perm_id,
              company_id: companyId.company.company_id,
              start_date_contract: this.searchContractStartDate,
              end_date_contract: this.searchContractEndDate,
              contract_service: this.contractSet === true ? 'Y' : 'N',
              remark: this.reason,
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              shipping_data: this.ShippingData,
              installment_month: this.radioCreditTerm === 'No' ? '' : this.radioCreditTerm
            }
            // if (this.radios === 'radio-1') {
            //   localStorage.setItem('DatePickUp', Encode.encode(this.dates))
            //   localStorage.setItem('TimePickUp', Encode.encode(this.formattedTime))
            // }
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
          if (res.result === 'SUCCESS') {
            localStorage.removeItem('sale_order')
            // if (this.CouponData.length !== 0) {
            //   var couponData = {
            //     counpon_id: this.CouponData[0].coupon_id,
            //     order_number: res.data.payment_transaction_number
            //   }
            //   // console.log('couponData', couponData)
            //   await this.$store.dispatch('actionsUseCoupon', couponData)
            //   await this.$store.state.ModuleCart.stateUseCoupon
            // }
            var resCC
            var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
            if (this.radioPayment !== 'radio-installment') {
              this.radioCreditTerm = ''
            }
            data = {
              go_local: goLocalValue,
              payment_transaction_number: res.data.payment_transaction_number,
              term: this.radioCreditTerm
            }
            await this.$store.dispatch('actionsGetCC', data)
            resCC = await this.$store.state.ModuleCart.stateGetCC
            if (resCC.result === 'SUCCESS') {
              localStorage.setItem('PaymentData', Encode.encode(resCC.data))
              this.$router.push('/RedirectPaymentPage').catch(() => {})
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
              })
              this.backstep()
            }
          } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่สามารถใช้คูปองได้'
            })
            this.$EventBus.$emit('clearPoint')
            this.$EventBus.$emit('clearCoupon')
            this.dialogAwaitConfirm = false
          } else if (res.code === 400) {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            this.backstep()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
            })
          }
          if (res.message === 'Division by zero') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
            })
            this.backstep()
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async GetQRCode (paymentTypeData) {
      this.$store.commit('openLoader')
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var res
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      if (this.CouponData.length === 0 || this.CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0) {
        resCoupon = {
          message: 'สามารถใช้คูปองได้'
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: -1
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        this.closeCoupon(this.itemsCart.choose_list[0])
        this.$store.commit('closeLoader')
      } else {
        if (paymentType === 'cashPayment') {
          if (dataRole.role === 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              shipping_data: this.ShippingData
            }
            // if (this.radios === 'radio-1') {
            //   // console.log(this.dates)
            //   localStorage.setItem('DatePickUp', Encode.encode(this.dates))
            //   localStorage.setItem('TimePickUp', Encode.encode(this.formattedTime))
            // }
          } else {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              com_perm_id: onedata.cartData.com_perm_id,
              company_id: companyId.company.company_id,
              start_date_contract: this.searchContractStartDate,
              end_date_contract: this.searchContractEndDate,
              contract_service: this.contractSet === true ? 'Y' : 'N',
              remark: this.reason,
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              shipping_data: this.ShippingData
            }
            // if (this.radios === 'radio-1') {
            //   localStorage.setItem('DatePickUp', Encode.encode(this.dates))
            //   localStorage.setItem('TimePickUp', Encode.encode(this.formattedTime))
            // }
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
          // console.log(res)
          if (res.result === 'SUCCESS') {
            localStorage.removeItem('sale_order')
            // if (this.CouponData.length !== 0) {
            //   var couponData = {
            //     counpon_id: this.CouponData[0].coupon_id,
            //     order_number: res.data.payment_transaction_number
            //   }
            //   // console.log('couponData', couponData)
            //   await this.$store.dispatch('actionsUseCoupon', couponData)
            //   await this.$store.state.ModuleCart.stateUseCoupon
            // }
            var resQR = ''
            // console.log(res.data)
            data = {
              payment_transaction_number: res.data.payment_transaction_number
            }
            // console.log(res.data.payment_transaction_number)
            this.paymenttransactionnumber = res.data.payment_transaction_number
            await this.$store.dispatch('actionsGetQRCode', data)
            resQR = await this.$store.state.ModuleCart.stateGetQRCode
            if (resQR.message.toLowerCase() === 'success' || resQR.result.toLowerCase() === 'success') {
              if (resQR.message !== 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
                this.QRShop = false
              } else if (resQR.message === 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
                this.QRShop = true
              } else {
                this.QRShop = false
              }
              this.netPrice = await resQR.data.net_price
              this.Ref1 = await resQR.data.ref1
              this.Ref2 = await resQR.data.ref2
              var base64 = resQR.data.img_base
              this.imageBase64 = 'data:image/png;base64,' + base64
              var ImageQR = await resQR.data.img_base64
              this.ImageQR = ImageQR
              this.$store.commit('closeLoader')
              this.openDialogQR()
              if (resQR.message !== 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
                data = {
                  payment_transaction_number: res.data.payment_transaction_number
                }
                var value = data.payment_transaction_number
                const maxAttempts = 15
                let currentAttempt = 1
                while (currentAttempt <= maxAttempts) {
                  await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
                  const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
                  if (this.CloseDialog === true) {
                    break
                  }
                  if (resCheckQR.result === 'SUCCESS') {
                    this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
                    break
                  }
                  await new Promise(resolve => setTimeout(resolve, 10000))
                  currentAttempt++
                  if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
                    this.$swal.fire({
                      showConfirmButton: false,
                      timer: 5000,
                      timerProgressBar: true,
                      icon: 'error',
                      text: 'การชำระเงินไม่เสร็จสมบูรณ์'
                    })
                    this.$router.push({ path: '/' }).catch(() => {})
                  }
                }
              }
            } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: 'ไม่สามารถใช้คูปองได้'
              })
              this.$EventBus.$emit('clearPoint')
              this.$EventBus.$emit('clearCoupon')
              this.dialogAwaitConfirm = false
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                icon: 'error',
                text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
              })
              this.backstep()
            }
            // this.$store.commit('closeLoader')
          } else if (res.code === 400) {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            this.backstep()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
            })
          }
          if (res.message === 'Division by zero') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
            })
            this.backstep()
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async CreateOrder (paymentTypeData) {
      // console.log(123)
      this.$store.commit('openLoader')
      var msg = ''
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var utmMedium = ''
      if (sessionStorage.getItem('utm_medium') !== null) {
        utmMedium = sessionStorage.getItem('utm_medium')
      } else {
        utmMedium = null
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(12345)
      var res
      var data
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var resCoupon
      if (this.installmentAmounts.length !== 0) {
        var installmentData = []
        if (this.itemsCart.is_JV === 'yes' && this.itemsCart.is_JV_buyer === 'yes') {
          installmentData = []
        } else {
          installmentData = this.installmentAmounts.map((amount, index) => {
            return { month: index + 1, price: parseFloat(amount).toFixed(2) }
          })
        }
      } else {
        installmentData = []
      }
      if (this.CouponData.length === 0 || this.CouponData.filter(coupon => coupon.seller_shop_id !== -1).length === 0) {
        resCoupon = {
          message: 'สามารถใช้คูปองได้'
        }
      } else {
        var dataCheckCoupon = {
          coupon_id: this.CouponData.length !== 0 ? [this.CouponData.find(coupon => coupon.seller_shop_id !== -1).coupon_id] : '',
          shop_id: onedata.cartData.seller_shop_id,
          net_price: couponData.net_price,
          price_inc_vat: couponData.price_inc_vat,
          role_user: dataRole.role,
          company_id: onedata.cartData.company_id,
          customer_id: -1
        }
        await this.$store.dispatch('actionsRecheckCoupon', dataCheckCoupon)
        resCoupon = await this.$store.state.ModuleCart.stateRecheck
      }
      if (resCoupon.message === 'พบบางคูปองมีปัญหา') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${resCoupon.data[0].error}</h3>`
        })
        this.dialogAwaitConfirm = false
        this.closeCoupon(this.itemsCart.choose_list[0])
        this.$store.commit('closeLoader')
      } else {
        // if (this.SelectCouponOrPoint === false) {
        //   var haveCoupon = [{
        //     type_discount: localStorage.getItem('CouponOrPoint') === 'Point' ? 'point' : 'coupon',
        //     coupon_id: localStorage.getItem('CouponOrPoint') === 'Point' ? '' : onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_id,
        //     coupon_discount: localStorage.getItem('CouponOrPoint') === 'Point' ? '' : onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_discount,
        //     net_price: parseInt(this.itemsCart.net_price),
        //     amount: localStorage.getItem('CouponOrPoint') === 'Point' ? onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].point : ''
        //   }]
        // } else {
        //   haveCoupon = []
        // }
        // if (onedata.cartDataSpecialPrice === 'yes') {
        //   var dataSpecialPrice = {
        //     role_user: dataRole.role,
        //     product_special_price_id: onedata.cartData.product_special_price_id,
        //     etax: this.taxRoles,
        //     credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
        //     company_id: onedata.cartData.company_id,
        //     company_position: onedata.cartData.company_position,
        //     com_perm_id: onedata.cartData.com_perm_id,
        //     coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon
        //   }
        //   await this.$store.dispatch('ActionCreateOrderSpecialprice', dataSpecialPrice)
        //   res = await this.$store.state.ModuleCart.stateCreateOrderSpecialprice
        // } else {
        // console.log('before')
        if (paymentType === 'cashPayment') {
          if (dataRole.role === 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              // com_perm_id: onedata.cartData.com_perm_id,
              // company_id: companyId.company.company_id,
              // invoice_id: this.taxinvoiceAddress.data.length > 0 ? this.taxinvoiceAddress.data[0].id : '',ของเก่าที่ยังมี invoice_id
              form: 'web',
              credit_day: this.selectedCreditTerm,
              installment_method: installmentData,
              address: this.CartAddress,
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              role_user: dataRole.role,
              shipping_data: this.ShippingData,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime
              // start_date_contract: this.searchContractStartDate,
              // end_date_contract: this.searchContractEndDate,
              // use_discount: 'N',
              // discount_percent: '0',
              // discount_amount: this.itemsCart.total_discount,
              // contract_service: this.contractSet === true ? 'Y' : 'N',
              // remark: this.reason,
              // type_budget: this.selectBudget,
              // budget_cut: this.selectCutBudget,
              // document_type_name: this.selectTypeDoc,
              // json_personal: [{
              //   purchasing_chief: [
              //     {
              //       name: this.Name_Buyer,
              //       phone: this.Phone_Buyer,
              //       email: this.Position_Buyer,
              //       position: this.Email_Buyer
              //     }
              //   ],
              //   inspectors_one: [
              //     {
              //       name: this.Name_Audit1,
              //       phone: this.Phone_Audit1,
              //       email: this.Position_Audit1,
              //       position: this.Email_Audit1
              //     }
              //   ],
              //   inspectors_two: [
              //     {
              //       name: this.Name_Audit2,
              //       phone: this.Phone_Audit2,
              //       email: this.Position_Audit2,
              //       position: this.Email_Audit2
              //     }
              //   ]
              // }
              // ],
              // json_buyer: [
              //   {
              //     buyer_name: this.buyer_name,
              //     buyer_phone: this.buyer_phone,
              //     buyer_email: this.buyer_email
              //   }
              // ]
              // credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
              // company_id: onedata.cartData.company_id ? onedata.cartData.company_id : -1,
              // company_position: onedata.cartData.company_position ? onedata.cartData.company_position : -1,
              // com_perm_id: onedata.cartData.com_perm_id ? onedata.cartData.com_perm_id : -1,
              // coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon,
              // payment_method: 'Cash'
            }
          } else {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              credit_day: this.selectedCreditTerm,
              installment_method: installmentData,
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              shipping_data: this.ShippingData,
              com_perm_id: onedata.cartData.com_perm_id,
              company_id: companyId.company.company_id,
              role_user: dataRole.role,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              date_pickup: this.dates,
              time_pickup: this.formattedTime
            }
          }
          // console.log('data-------->', data)
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
        } else {
          if (dataRole.role !== 'ext_buyer') {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            // console.log(this.EtaxType)
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              credit_day: this.selectedCreditTerm,
              installment_method: installmentData,
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              com_perm_id: onedata.cartData.com_perm_id,
              company_id: companyId.company.company_id,
              role_user: dataRole.role,
              // invoice_id: this.taxinvoiceAddress.data.length > 0 ? this.taxinvoiceAddress.data[0].id : '',ของเก่าที่ยังมี invoice_id
              shipping_data: this.ShippingData,
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              start_date_contract: this.searchContractStartDate,
              end_date_contract: this.searchContractEndDate,
              use_discount: 'N',
              discount_percent: '0',
              discount_amount: this.itemsCart.total_discount,
              contract_service: this.contractSet === true ? 'Y' : 'N',
              remark: this.reason,
              type_budget: this.selectBudget,
              budget_cut: this.selectCutBudget,
              document_type_name: this.selectTypeDoc,
              json_personal: [{
                purchasing_chief: [
                  {
                    name: this.Name_Buyer,
                    phone: this.Phone_Buyer,
                    email: this.Position_Buyer,
                    position: this.Email_Buyer
                  }
                ],
                inspectors_one: [
                  {
                    name: this.Name_Audit1,
                    phone: this.Phone_Audit1,
                    email: this.Position_Audit1,
                    position: this.Email_Audit1
                  }
                ],
                inspectors_two: [
                  {
                    name: this.Name_Audit2,
                    phone: this.Phone_Audit2,
                    email: this.Position_Audit2,
                    position: this.Email_Audit2
                  }
                ],
                supply_board_one: [
                  {
                    name: this.Name_SupplyBoard1,
                    phone: this.Phone_SupplyBoard1,
                    email: this.Position_SupplyBoard1,
                    position: this.Email_SupplyBoard1
                  }
                ],
                supply_board_two: [
                  {
                    name: this.Name_SupplyBoard2,
                    phone: this.Phone_SupplyBoard2,
                    email: this.Position_SupplyBoard2,
                    position: this.Email_SupplyBoard2
                  }
                ]
              }
              ],
              json_buyer: [
                {
                  buyer_name: this.buyer_name,
                  buyer_phone: this.buyer_phone,
                  buyer_position: this.buyer_position,
                  buyer_email: this.buyer_email
                }
              ]
              // purchasing_cheif: {
              //   name: this.Name_Buyer,
              //   phone: this.Phone_Buyer,
              //   position: this.Position_Buyer,
              //   email: this.Email_Buyer
              // },
              // inspectors_one: {
              //   name: this.Name_Audit1,
              //   phone: this.Phone_Audit1,
              //   position: this.Position_Audit1,
              //   email: this.Email_Audit1
              // },
              // inspectors_two: {
              //   name: this.Name_Audit2,
              //   phone: this.Phone_Audit2,
              //   position: this.Position_Audit2,
              //   email: this.Email_Audit2
              // }
              // role_user: dataRole.role,
              // etax: this.taxRoles,
              // credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
              // company_id: onedata.cartData.company_id ? onedata.cartData.company_id : -1,
              // company_position: onedata.cartData.company_position ? onedata.cartData.company_position : -1,
              // com_perm_id: onedata.cartData.com_perm_id ? onedata.cartData.com_perm_id : -1,
              // // coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon,
              // payment_method: 'ThaiDot'
            }
          } else {
            this.invoicedetail.forEach(element => {
              if (element.default_invoice === 'Y') {
                this.invoiceID = element.id
                this.EtaxType = element.tax_type
              }
            })
            // console.log(this.EtaxType)
            if (this.radiostax === 'radiotax-1') {
              this.RequiredInvoice = 'yes'
            } else {
              this.RequiredInvoice = 'no'
              this.EtaxType = 'No'
            }
            data = {
              form: 'web',
              credit_day: this.selectedCreditTerm,
              installment_method: installmentData,
              inet_relation_ship: this.InetRelation,
              utm_source_keyword: utmMedium === 'reach' ? sessionStorage.getItem('utm_source') : null,
              address: this.CartAddress,
              total_point_receive: this.itemsCart.total_point_receive,
              coupon: this.CouponData.length !== 0 && !this.noShipping
                ? this.CouponData
                : this.CouponData.filter(coupon => coupon.coupon_type !== 'free_shipping'),
              point: this.PointData !== 0 ? this.PointData : 0,
              point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0,
              product_free: this.itemsCart.product_free.length !== 0 ? this.itemsCart.product_free : [],
              role_user: dataRole.role,
              shipping_data: this.ShippingData,
              date_pickup: this.dates,
              time_pickup: this.formattedTime,
              required_invoice: this.RequiredInvoice,
              etax: this.EtaxType,
              invoice_id: this.itemsCart.isEtax === 'yes' ? this.invoiceID : '',
              type_shipping: this.radios === 'radio-1' ? 'pickup' : 'online',
              start_date_contract: this.searchContractStartDate,
              end_date_contract: this.searchContractEndDate,
              use_discount: 'N',
              discount_percent: '0',
              discount_amount: this.itemsCart.total_discount,
              contract_service: this.contractSet === true ? 'Y' : 'N',
              remark: this.reason,
              type_budget: this.selectBudget,
              budget_cut: this.selectCutBudget,
              document_type_name: this.selectTypeDoc,
              json_personal: [{
                purchasing_chief: [
                  {
                    name: this.Name_Buyer,
                    phone: this.Phone_Buyer,
                    email: this.Position_Buyer,
                    position: this.Email_Buyer
                  }
                ],
                inspectors_one: [
                  {
                    name: this.Name_Audit1,
                    phone: this.Phone_Audit1,
                    email: this.Position_Audit1,
                    position: this.Email_Audit1
                  }
                ],
                inspectors_two: [
                  {
                    name: this.Name_Audit2,
                    phone: this.Phone_Audit2,
                    email: this.Position_Audit2,
                    position: this.Email_Audit2
                  }
                ],
                supply_board_one: [
                  {
                    name: this.Name_SupplyBoard1,
                    phone: this.Phone_SupplyBoard1,
                    email: this.Position_SupplyBoard1,
                    position: this.Email_SupplyBoard1
                  }
                ],
                supply_board_two: [
                  {
                    name: this.Name_SupplyBoard2,
                    phone: this.Phone_SupplyBoard2,
                    email: this.Position_SupplyBoard2,
                    position: this.Email_SupplyBoard2
                  }
                ]
              }
              ],
              json_buyer: [
                {
                  buyer_name: this.buyer_name,
                  buyer_phone: this.buyer_phone,
                  buyer_position: this.buyer_position,
                  buyer_email: this.buyer_email
                }
              ]
            }
          }
          await this.$store.dispatch('ActionCreateOrder', data)
          res = await this.$store.state.ModuleCart.stateCreateOrder
          // console.log(res)
          // }
        }
        if (res.result === 'SUCCESS') {
          // if (this.CouponData.length !== 0) {
          //   var couponData = {
          //     counpon_id: this.CouponData[0].coupon_id,
          //     order_number: res.data.payment_transaction_number
          //   }
          //   // console.log('couponData', couponData)
          //   await this.$store.dispatch('actionsUseCoupon', couponData)
          //   await this.$store.state.ModuleCart.stateUseCoupon
          // }
          if (res.message === 'Create Order success.' || res.message === 'This user is purchaser.create order success.') {
            if (res.data.can_pay === 'yes') {
              this.$store.commit('closeLoader')
              this.dialogSuccess = true
              // this.$swal.fire({
              //   showConfirmButton: false,
              //   timer: 2500,
              //   timerProgressBar: true,
              //   icon: 'success',
              //   text: 'สั่งสินค้าเรียบร้อย'
              // })
              // var dataPayment = {
              //   payment_transaction_number: res.data.order_number
              // }
              // if (this.selectTypeAddress === 'Shop') {
              //   if (this.checkOwnShop === 'Y') {
              //     if (paymentType === 'cashPayment') {
              //       await this.$store.dispatch('actionsPayCashInStore', dataPayment)
              //       var resPayCashInStore = await this.$store.state.ModuleCart.statePayCashInStore
              //       console.log(resPayCashInStore)
              //       if (resPayCashInStore.result === 'SUCCESS') {
              //         if (resPayCashInStore.message === 'Cash payment success without e-tax' || resPayCashInStore.message === 'Cash payment success with e-tax') {
              //           this.$EventBus.$emit('getCartPopOver')
              //           if (!this.MobileSize) {
              //             this.$router.push({ path: '/pobuyerProfile' }).catch(() => {})
              //           } else {
              //             this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => {})
              //           }
              //         }
              //       } else {
              //         this.$swal.fire({
              //           showConfirmButton: false,
              //           timer: 3000,
              //           timerProgressBar: true,
              //           icon: 'error',
              //           text: 'เกิดข้อผิดพลาดของระบบ กรุณาติดต่อเจ้าหน้าที่'
              //         })
              //       }
              //     } else {
              //       await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
              //       var resRedirect2 = await this.$store.state.ModuleCart.stateGetPaymentPage
              //       this.overlay = false
              //       this.$EventBus.$emit('getCartPopOver')
              //       localStorage.setItem('PaymentData', Encode.encode(resRedirect2))
              //       this.$router.push('/RedirectPaymentPage').catch(() => {})
              //     }
              //   } else {
              //     await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
              //     var resRedirect1 = await this.$store.state.ModuleCart.stateGetPaymentPage
              //     this.overlay = false
              //     this.$EventBus.$emit('getCartPopOver')
              //     localStorage.setItem('PaymentData', Encode.encode(resRedirect1))
              //     this.$router.push('/RedirectPaymentPage').catch(() => {})
              //   }
              // } else {
              //   await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
              //   var resRedirect = await this.$store.state.ModuleCart.stateGetPaymentPage
              //   this.overlay = false
              //   this.$EventBus.$emit('getCartPopOver')
              //   localStorage.setItem('PaymentData', Encode.encode(resRedirect))
              //   this.$router.push('/RedirectPaymentPage').catch(() => {})
              // }
            } else {
              if (dataRole.role === 'purchaser') {
                const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
                var id2 = companyId.company.company_id
                const data2 = {
                  company_id: id2
                }
                await this.$store.dispatch('actionsDetailCompany', data2)
                await this.$store.dispatch('actionsAuthorityUser')
                var responseCompany2 = await this.$store.state.ModuleAdminManage.stateDetailCompany
                var responsePosition2 = await this.$store.state.ModuleUser.stateAuthorityUser
                var listCompany2 = responsePosition2.data.list_company
                for (let i = 0; i < listCompany2.length; i++) {
                  if (responseCompany2.data.id === listCompany2[i].company_id) {
                    localStorage.removeItem('list_Company_detail')
                    localStorage.setItem('list_Company_detail', Encode.encode(listCompany2[i]))
                  }
                }
                localStorage.setItem('CompanyData', Encode.encode(responseCompany2.data))
                this.$EventBus.$emit('getItemNoti')
                if (this.MobileSize) {
                  this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${res.data.order_number}` }).catch(() => { })
                } else {
                  this.$router.push({ path: `/orderDetailCompany?orderNumber=${res.data.order_number}` }).catch(() => { })
                }
              }
            }
          } else if (res.message === 'This user is purchaser & credit term . create order success.') {
            this.$store.commit('closeLoader')
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 2500,
            //   timerProgressBar: true,
            //   icon: 'success',
            //   text: 'สั่งสินค้าเรียบร้อย'
            // })
            if (localStorage.getItem('SetRowCompany') !== null) {
              const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
              var id = companyId.company.company_id
              const data = {
                company_id: id
              }
              await this.$store.dispatch('actionsDetailCompany', data)
              await this.$store.dispatch('actionsAuthorityUser')
              var responseCompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
              var responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
              var listCompany = responsePosition.data.list_company
              for (let i = 0; i < listCompany.length; i++) {
                if (responseCompany.data.id === listCompany[i].company_id) {
                  localStorage.removeItem('list_Company_detail')
                  localStorage.setItem('list_Company_detail', Encode.encode(listCompany[i]))
                }
              }
              localStorage.setItem('CompanyData', Encode.encode(responseCompany.data))
              this.$EventBus.$emit('getItemNoti')
              if (this.MobileSize) {
                this.$router.push({ path: '/companyListCreditOrderMobile' }).catch(() => { })
              } else {
                this.$router.push({ path: '/companyListCreditOrder' }).catch(() => { })
              }
            }
          } else if (res.message === 'This user is have approver . create order success.') {
            this.$store.commit('closeLoader')
            this.dialogSuccess = true
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 2500,
            //   timerProgressBar: true,
            //   icon: 'success',
            //   text: 'สั่งสินค้าเรียบร้อย'
            // })
            if (localStorage.getItem('SetRowCompany') !== null) {
              const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
              var Companyid = companyId.company.company_id
              const data = {
                company_id: Companyid
              }
              await this.$store.dispatch('actionsDetailCompany', data)
              await this.$store.dispatch('actionsAuthorityUser')
              var responseCompanyApprove = await this.$store.state.ModuleAdminManage.stateDetailCompany
              var responsePositionApprove = await this.$store.state.ModuleUser.stateAuthorityUser
              var listCompanyAprrove = responsePositionApprove.data.list_company
              for (let i = 0; i < listCompanyAprrove.length; i++) {
                if (responseCompanyApprove.data.id === listCompanyAprrove[i].company_id) {
                  localStorage.removeItem('list_Company_detail')
                  localStorage.setItem('list_Company_detail', Encode.encode(listCompanyAprrove[i]))
                }
              }
              localStorage.setItem('CompanyData', Encode.encode(responseCompanyApprove.data))
              this.$EventBus.$emit('getItemNoti')
              if (this.MobileSize) {
                this.$router.push({ path: '/listApproveMobile' }).catch(() => { })
              } else {
                this.$router.push({ path: '/listApprove' }).catch(() => { })
              }
            }
          } else {
            this.$store.commit('closeLoader')
            this.dialogSuccess = true
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 2500,
            //   timerProgressBar: true,
            //   icon: 'success',
            //   text: 'สั่งสินค้าเรียบร้อย'
            // })
            // window.location.assign('/')
            // this.$router.push({ path: '/' }).catch(() => {})
          }
        } else if (res.result === 'FAILED') {
          if (res.message === 'Credit is not enough for buy.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'วงเงินของบริษัทไม่เพียงพอ'
            })
            this.Cancel()
          } else if (res.message === 'Not enough product in stock.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: `ไม่สามารถซื้อสินค้า ${res.data[0].product} ได้เนื่องจากจำนวนสินค้ามีไม่เพียงพอ`
            })
            this.Cancel()
          } else if (res.message === 'ไม่พบตะกร้า') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่พบตะกร้า'
            })
            this.backstep()
          } else if (res.message === 'เกิดข้อผิดพลาด กรุณากลับไปยังหน้าตะกร้า') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'เกิดข้อผิดพลาด กรุณากลับไปยังหน้าตะกร้า'
            })
            this.backstep()
          } else if (res.message === 'Type_budget not found.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่พบประเภทการควบคุมวงเงิน'
            })
            this.Cancel()
          } else if (res.message === 'No approver in this company.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่มีผู้อนุมัติในบริษัทนี้'
            })
            this.Cancel()
          } else if (res.message === 'Approver limit exceeded.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'วงเงินผู้อนุมัติไม่เพียงพอ'
            })
            this.Cancel()
          } else if (res.message === 'Credit not enough for buy.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'วงเงินฝ่าย/แผนกไม่เพียงพอ'
            })
            this.Cancel()
          } else if (res.message === 'This user can not create order becase this user is not in some department') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: 'ผู้ซื้อไม่มีแผนก กรุณาติดต่อเจ้าหน้าที่'
            })
            this.Cancel()
          } else if (res.message === 'This user is unauthorized.') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   timer: 3000,
            //   timerProgressBar: true,
            //   icon: 'error',
            //   text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง'
            // })
            // window.location.assign('/')
          } else if (res.message === 'ไม่สามารถใช้คูปองได้ กรุณานำออกจากรถเข็น') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่สามารถใช้คูปองได้'
            })
            this.$EventBus.$emit('clearPoint')
            this.$EventBus.$emit('clearCoupon')
            this.dialogAwaitConfirm = false
          } else {
            // error msg form OrderPurchaserCheck
            this.$store.commit('closeLoader')
            msg = this.getErrorMsg(res.message)
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              icon: 'error',
              text: msg
            })
            this.Cancel()
          }
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: res.message
            })
          }
          this.Cancel()
        }
      }
      localStorage.removeItem('CouponDetail')
      localStorage.removeItem('PointDetail')
    },
    // error msg form OrderPurchaserCheck
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (msg === 'Data missing. Please check your [" company_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Order not found.') {
        return 'ไม่พบข้อมูลออเดอร์'
      } else if (msg === 'Data missing. Please check your [" order_number "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ เลขออเดอร์ ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'User Has PDF not found.') {
        return 'ไม่พบข้อมูลใบเสนอราคา'
      } else if (msg === 'Token not found.') {
        return 'ไม่พบข้อมูลโทเค่น'
      } else if (msg === 'You not Have Permission Purchaser.') {
        return 'คุณไม่มีสิทธิ์ผู้ซื้อองค์กร'
      } else if (msg === 'Approve Position Purchaser Not Found.') {
        return 'ไม่พบรูปแบบการอนุมัติ'
      } else if (msg === 'Approve Position Budget Less Than Order Net Price.') {
        return 'วงเงินของรูปแบบการอนุมัติน้อยกว่าเงินสุทธิออเดอร์'
      } else if (msg === 'Your Company is not Partner With This Shop.') {
        return 'บริษัทของคุณไม่ได้เป็นคู่ค้ากับร้านค้า'
      } else if (msg === 'Company Credit With This Seller Shop Less Than Order Net Price.') {
        return 'เงินขององค์กรน้อยกว่าเงินสุทธิของออเดอร์'
      } else if (msg === 'Not Have Approver In Approve Position.') {
        return 'ไม่พบผู้อนุมัติในลำดับอนุมัติ โปรดเพิ่มผู้อนุมัติก่อน'
      } else if (msg === 'Sum Approve Position Budget Less Than Order Net Price.') {
        return 'ผลรวมของวงเงินแต่ละลำดับน้อยกว่าเงินสุทธิของออเดอร์'
      } else if (msg === 'Wrong Approve Position Type.') {
        return 'ประเภทการอนุมัติผิด'
      } else if (msg === 'Some User not Have Permission to Approve Order.') {
        return 'มีผู้ใช้ในลำดับที่ไม่มีสิทธิ์การอนุมัติ กรุณาตรวจสอบรูปแบบการอนุมัติ'
      } else if (msg === '') {
        return ''
      } else {
        // return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
        return msg
      }
    },
    Cancel () {
      this.$router.go(-1)
    },
    goHomePage () {
      this.$router.push({ path: '/' }).catch(() => { })
    },
    goPoBuyerProfilePage () {
      if (!this.MobileSize) {
        // console.log(1)
        this.$router.push({ path: '/pobuyerProfile' }).catch(() => { })
      } else {
        // console.log(2)
        this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => { })
      }
    },
    // editAddress (val) {
    //   localStorage.setItem('AddressUserDetail', Encode.encode(val))
    //   this.EditAddressDetail = val
    //   this.titleAddress = 'แก้ไขที่อยู่จัดส่งสินค้า'
    //   this.page = 'checkout'
    //   this.$EventBus.$emit('EditModalAddress')
    // },
    openModalAddressCustomer () {
      this.$refs.ModalAddressCustomer.open()
    },
    // async getTaxAddressPurchaser () {
    //   await this.$store.dispatch('actionUPSGetAddressTaxinvoicePurchase')
    //   var res = await this.$store.state.ModuleUser.stateUPSGetAddressTaxinvoicePurchase
    //   if (res.message === 'Get corporate address success.') {
    //     this.companyName = res.data.company_name_th
    //     this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี' + ' ' + res.data.company_tax_id
    //     this.taxAddress = res.data.address[0].detail + ' ' + 'แขวง/ตำบล' + ' ' + res.data.address[0].sub_district + ' ' + 'เขต/อำเภอ' + ' ' + res.data.address[0].district + ' ' + 'จังหวัด' + ' ' + res.data.address[0].province + ' ' + res.data.address[0].zip_code
    //     this.updateInvoiceAddressPurchaser()
    //   } else {
    //     this.companyName = ''
    //     this.companyTaxID = ''
    //     this.taxAddress = ''
    //   }
    // },
    async CreateOrderSyncTaxaddressPurchaser (items) {
      var data = {
        // invoice_id: items,
        invoice_id: '',
        cart_id: this.itemsCart.id
      }
      await this.$store.dispatch('ActionCreateOrderSyncTaxaddress', data)
    },
    async updateInvoiceAddressPurchaser () {
      // this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('ActionCreateInvoiceAddressPurchaser', data)
      var res = await this.$store.state.UPSModuleCart.stateCreateInvoiceAddressPurchaser
      if (res.message === 'Create invoice address success.') {
        this.$store.commit('closeLoader')
        // this.CreateOrderSyncTaxaddressPurchaser(res.data.invoice_id)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          text: 'เกิดข้อผิดพลาดเกี่ยวกับที่อยู่ใบกำกับภาษี'
        })
      }
    },
    async checkAddressTaxinvoiceData () {
      // console.log('jjjjjjjj')
      this.companyName = ''
      this.companyTaxID = ''
      this.taxAddress = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        user_id: onedata.user.user_id,
        role: dataRole.role,
        company_id: dataCompany,
        tax_type: this.taxRoles
      }
      await this.$store.dispatch('actionsGetInvoice', data)
      this.taxinvoiceAddress = await this.$store.state.ModuleUser.stategetInvoice
      // console.log(this.taxinvoiceAddress)
      if (this.taxinvoiceAddress.data.length !== 0) {
        this.companyName = this.taxinvoiceAddress.data[0].name
        this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี :' + ' ' + this.taxinvoiceAddress.data[0].tax_id
        this.taxAddress = this.taxinvoiceAddress.data[0].address + ' ' + 'แขวง/ตำบล' + ' ' + this.taxinvoiceAddress.data[0].sub_district + ' ' + 'เขต/อำเภอ' + ' ' + this.taxinvoiceAddress.data[0].district + ' ' + 'จังหวัด' + ' ' + this.taxinvoiceAddress.data[0].province + ' ' + this.taxinvoiceAddress.data[0].postal_code
      }
      // this.CreateOrderSyncTaxaddress()
    },
    async checkAddressTaxinvoiceDataNew () {
      this.companyName = ''
      this.companyTaxID = ''
      this.taxAddress = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        user_id: onedata.user.user_id,
        role: dataRole.role,
        company_id: dataCompany,
        tax_type: this.taxRoles
      }
      await this.$store.dispatch('actionsGetInvoiceAddress', data)
      this.taxinvoiceAddressNew = await this.$store.state.ModuleUser.stateGetInvoiceAddress
      // console.log(this.taxinvoiceAddressNew.data)
      if (this.taxinvoiceAddressNew.data.length !== 0) {
        this.companyName = this.taxinvoiceAddressNew.data[0].name
        this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี :' + ' ' + this.taxinvoiceAddressNew.data[0].tax_id
        this.taxAddress = this.taxinvoiceAddressNew.data[0].address + ' ' + 'แขวง/ตำบล' + ' ' + this.taxinvoiceAddressNew.data[0].sub_district + ' ' + 'เขต/อำเภอ' + ' ' + this.taxinvoiceAddressNew.data[0].district + ' ' + 'จังหวัด' + ' ' + this.taxinvoiceAddressNew.data[0].province + ' ' + this.taxinvoiceAddressNew.data[0].postal_code
      }
      // this.CreateOrderSyncTaxaddress()
    },
    async openQuotation () {
      var dataList = []
      var sellerShopID
      var data = ''
      this.itemsCart.choose_list.forEach(element => {
        sellerShopID = element.seller_shop_id
        element.product_list.forEach(product => {
          const productList = {
            product_id: product.product_id,
            attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
            attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
            quantity: product.quantity,
            price: product.revenue_default,
            status: product.product_status,
            attribute_id: product.product_attribute_detail.product_attribute_id
          }
          dataList.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // get user data
      // var response = await this.$store.state.ModuleUser.stateUserDetailPage
      // var user = response.data[0]
      // get admin data
      // const sendId = { user_id: user.id }
      // await this.$store.dispatch('ActionGetAdminData', sendId)
      // var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
      // var adminStatus = false
      // if (responseAdmin.data.length !== 0) {
      //   adminStatus = true
      // } else {
      //   adminStatus = false
      // }
      // get company Purchaser data
      if (dataRole.role === 'purchaser') {
        // const sendData = { user_id: user.id }
        // await this.$store.dispatch('ActionGetCompanyPurchaser', sendData)
        // var responseCompanyPurchaser = await this.$store.state.ModuleCart.stateCompanyPurchaser
        // if (responseCompanyPurchaser.message === 'Get company purchaser success.') {
        var companyID = ''
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          companyID = companyDataSet.company.company_id
        } else {
          companyID = '-1'
        }
        data = {
          role_user: dataRole.role,
          product_list: dataList,
          company_id: companyID,
          // companyName: responseCompanyPurchaser.data.company_name,
          // fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
          // phone: this.itemsCart.address_data[0].phone,
          // email: responseCompanyPurchaser.data.user_email,
          total_shipping: this.itemsCart.total_shipping,
          // discount: this.itemsCart.total_price_discount,
          // vat: this.itemsCart.total_vat,
          // fullnameAdmin: responseAdmin.data.contact_name,
          // phoneAdmin: responseAdmin.data.contact_tel,
          // emailAdmin: responseAdmin.data.contact_email,
          // isAdmin: adminStatus,
          total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
          seller_shop_id: sellerShopID
        }
        // this.$EventBus.$emit('openModalQuotation', data)
      } else {
        data = {
          role_user: dataRole.role,
          product_list: dataList,
          company_id: -1,
          // fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
          // phone: this.itemsCart.address_data[0].phone,
          // email: '',
          total_shipping: this.itemsCart.total_shipping,
          // discount: this.itemsCart.total_price_discount,
          // vat: this.itemsCart.total_vat,
          // fullnameAdmin: responseAdmin.data.contact_name,
          // phoneAdmin: responseAdmin.data.contact_tel,
          // emailAdmin: responseAdmin.data.contact_email,
          // isAdmin: adminStatus,
          total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
          seller_shop_id: sellerShopID
        }
      }
      await this.$store.dispatch('ActionsPreviewQU', data)
      var responseData = await this.$store.state.ModuleCart.statePreviewQU
      // console.log(responseData.data)
      if (responseData.result === 'SUCCESS') {
        window.open(responseData.data.link_preview, '_blank')
      }
    },
    async sentDataPPL () {
      var dataList = []
      this.itemsCart.choose_list.forEach(element => {
        element.product_list.forEach(product => {
          const productList = {
            product_id: product.product_id,
            product_name: product.product_name + ' ' + (product.key_1_value ? product.key_1_value : '') + ' ' + (product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '') + ' ' + (product.key_2_value ? product.key_2_value : '') + ' ' + (product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : ''),
            quantity: product.quantity,
            price: product.price,
            status: product.product_status
          }
          dataList.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // get user data
      // var data = {
      //   role_user: dataRole.role
      // }
      // await this.$store.dispatch('actionsUserDetailPage', data)
      // var response = await this.$store.state.ModuleUser.stateUserDetailPage
      var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
      var user = response.data[0]
      // get admin data
      const sendId = { user_id: user.id }
      await this.$store.dispatch('ActionGetAdminData', sendId)
      var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
      var adminStatus = false
      this.checkAdminQU = false
      if (responseAdmin.data.length !== 0) {
        adminStatus = true
        this.checkAdminQU = true
      } else {
        adminStatus = false
        this.checkAdminQU = false
      }
      // get company Purchaser data
      if (dataRole.role === 'purchaser') {
        const sendData = { user_id: user.id }
        await this.$store.dispatch('ActionGetCompanyPurchaser', sendData)
        const responseData = await this.$store.state.UPSModuleCart.stateCompanyPurchaser
        const dataResponse = responseData.data
        if (responseData.message === 'Get company purchaser success.') {
          const data = {
            registered_key: '',
            action: 'input',
            flow_id: '',
            template_id: '619204fea310de0012c4b222',
            json_data: {
              datatable1: {
                isRowImport: true,
                row_data: dataList
              },
              quatation_number: '',
              companyName: dataResponse.company_name,
              fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
              phone: this.itemsCart.address_data[0].phone,
              email: dataResponse.user_email,
              total_shipping: this.itemsCart.total_shipping,
              discount: this.itemsCart.total_discount,
              vat: this.itemsCart.total_vat,
              fullnameAdmin: responseAdmin.data.contact_name,
              phoneAdmin: responseAdmin.data.contact_tel,
              emailAdmin: responseAdmin.data.contact_email,
              delivery: '3-5 วัน/DATE หลังจากได้รับใบสั่งซื้อ / AFTER RECEIPT OF CONFIRMED PURCHASE ORDER',
              other: 'ลูกค้าใช้งานเปิดบิลขั้นต่ำ 3,000 บาท/ครั้ง ตัวแทนเปิดบิลขั้นต่ำ 5,000 บาท/ครั้ง กรุงเทพ-ปริมณฑลจัดส่งฟรี',
              isAdmin: adminStatus
            },
            is_preinput: true,
            pdf_base: '',
            external_data: {}
          }
          await this.$store.dispatch('ActionSentDataPPL', data)
          const res = await this.$store.state.UPSModuleCart.stateSentDataPPL
          this.responseSentDataPPL = res
        }
      } else {
        const data = {
          registered_key: '',
          action: 'input',
          flow_id: '',
          template_id: '619204fea310de0012c4b222',
          json_data: {
            datatable1: {
              isRowImport: true,
              row_data: dataList
            },
            quatation_number: '',
            companyName: '',
            fullname: this.itemsCart.address_data[0].first_name + ' ' + this.itemsCart.address_data[0].last_name,
            phone: this.itemsCart.address_data[0].phone,
            email: '',
            total_shipping: this.itemsCart.total_shipping,
            discount: this.itemsCart.total_discount,
            vat: this.itemsCart.total_vat,
            fullnameAdmin: responseAdmin.data.contact_name,
            phoneAdmin: responseAdmin.data.contact_tel,
            emailAdmin: responseAdmin.data.contact_email,
            delivery: '3-5 วัน/DATE หลังจากได้รับใบสั่งซื้อ / AFTER RECEIPT OF CONFIRMED PURCHASE ORDER',
            other: 'ลูกค้าใช้งานเปิดบิลขั้นต่ำ 3,000 บาท/ครั้ง ตัวแทนเปิดบิลขั้นต่ำ 5,000 บาท/ครั้ง กรุงเทพ-ปริมณฑลจัดส่งฟรี',
            isAdmin: adminStatus
          },
          is_preinput: true,
          pdf_base: '',
          external_data: {}
        }
        await this.$store.dispatch('ActionSentDataPPL', data)
        const res = await this.$store.state.UPSModuleCart.stateSentDataPPL
        this.pplURL = res.result.redirect_url + this.pplToken
        // window.open(url, '_blank')
      }
    },
    openPPL () {
      window.open(this.pplURL, '_blank')
    },
    async openCreateQU (typeopenModalPayment) {
      this.modalPayment = false
      var dataListEdit = []
      var ShopID = ''
      this.itemsCart.choose_list.forEach(element => {
        ShopID = element.seller_shop_id
        element.product_list.forEach(product => {
          // const productList = {
          //   product_id: product.product_id,
          //   // product_image: product.product_image,
          //   product_name: product.product_name,
          //   product_sku: product.sku,
          //   attribute_1_key: product.key_1_value,
          //   attribute_2_key: product.key_2_value,
          //   attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
          //   attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
          //   quantity: product.quantity,
          //   price: product.price,
          //   status: product.product_status,
          //   attribute_id: product.product_attribute_detail.product_attribute_id
          // }
          const productList = {
            product_id: product.product_id,
            // name: product.product_name,
            // product_sku: product.sku,
            attribute_id: product.product_attribute_detail.product_attribute_id,
            // attribute_1_key: product.key_1_value,
            attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
            // attribute_2_key: product.key_2_value,
            attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
            quantity: product.quantity,
            price: product.price,
            status: product.product_status
          }
          dataListEdit.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyID = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var dataSendCreate = {
        role_user: dataRole.role,
        seller_shop_id: ShopID,
        // id: companyID,
        company_id: companyID,
        product_list: dataListEdit,
        total_shipping: this.itemsCart.total_shipping,
        special_price_id: this.oneDataSpecial === 'yes' ? onedata.cartData.product_special_price_id : null,
        payment_type: typeopenModalPayment,
        total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
        com_perm_id: this.itemsCart.com_perm_id,
        coupon_id: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_id,
        point: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].point
      }
      await this.$store.dispatch('actionsCreateQuCart', dataSendCreate)
      const { result = '', data = {}, message = '' } = await this.$store.state.ModuleAdminManage.createQuCart
      if (result === 'SUCCESS') {
        // this.$store.state.ModuleAdminManage.QuotationformData = await data
        if (!this.MobileSize) {
          this.$router.push({ path: '/QUCompanyDetail?QU_ID=' + data.qu_id + '&id=' + data.company_id + '&shopID=' + data.seller_shop_id }).catch(() => { })
        } else {
          this.$router.push({ path: '/QUCompanyDetailMobile?QU_ID=' + data.qu_id + '&id=' + data.company_id + '&shopID=' + data.seller_shop_id }).catch(() => { })
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          text: message,
          showConfirmButton: false,
          timer: 1500
        })
        this.$router.push({ path: '/checkout' }).catch(() => { })
      }
    },
    async openModaleEditQUYesNo () {
      this.dialog_Cancel_Coupon = true
    },
    async openEditQU () {
      this.dialog_Cancel_Coupon = false
      this.SelectCouponOrPoint = true
      if (localStorage.getItem('CouponOrPoint') !== null) {
        if (localStorage.getItem('CouponOrPoint') === 'Point') {
          this.nameCouponOrPoint = ''
          await this.$EventBus.$emit('CancleBookPointCheckout')
        } else {
          this.nameCouponOrPoint = ''
          await this.$EventBus.$emit('CancleBookCouponCheckout')
        }
      }
      var dataListEdit = []
      var ShopID = ''
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.itemsCart.choose_list.forEach(element => {
        ShopID = element.seller_shop_id
        element.product_list.forEach(product => {
          // const productList = {
          //   product_id: product.product_id,
          //   // product_image: product.product_image,
          //   product_name: product.product_name,
          //   product_sku: product.sku,
          //   attribute_1_key: product.key_1_value,
          //   attribute_2_key: product.key_2_value,
          //   attribute_option_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
          //   attribute_option_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
          //   quantity: product.quantity,
          //   price: product.price,
          //   status: product.product_status,
          //   attribute_id: product.product_attribute_detail.product_attribute_id
          // }
          const productList = {
            product_id: product.product_id,
            name: product.product_name,
            product_sku: product.sku,
            attribute_id: product.product_attribute_detail.product_attribute_id,
            attribute_1_key: product.key_1_value,
            attribute_priority_1: product.product_attribute_detail.attribute_priority_1 ? product.product_attribute_detail.attribute_priority_1 : '',
            attribute_2_key: product.key_2_value,
            attribute_priority_2: product.product_attribute_detail.attribute_priority_2 ? product.product_attribute_detail.attribute_priority_2 : '',
            quantity: product.quantity,
            price: product.price
          }
          dataListEdit.push(productList)
        })
      })
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyID = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var dataSendEdit = {
        role_user: dataRole.role,
        seller_shop_id: ShopID,
        id: companyID,
        company_id: companyID,
        // id: 29,
        // company_id: 29,
        qu_id: '-1',
        product_list: dataListEdit,
        total_price_no_vat: this.itemsCart.total_price_no_vat,
        total_vat: this.itemsCart.total_vat,
        total_shipping: this.itemsCart.total_shipping,
        net_price: this.itemsCart.net_price,
        total_discount: this.itemsCart.total_discount ? this.itemsCart.total_discount : '',
        coupon_id: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].coupon_id,
        point: onedata.cartData.coupon.length === 0 ? '' : onedata.cartData.coupon[0].point,
        point_used: this.PointData !== 0 ? Number(this.PointData / this.XBaht) : 0
      }
      // var testdat = {
      //   company_id: '29',
      //   qu_id: '1',
      //   role_user: 'purchaser',
      //   seller_shop_id: '288',
      //   id: '29',
      //   total_discount: '0',
      //   coupon_id: '',
      //   point: ''
      // }
      // console.log(testdat)
      await this.$store.dispatch('actionsEditQU', dataSendEdit)
      const { result = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQU
      if (result === 'SUCCESS') {
        onedata.cartData.coupon = []
        localStorage.setItem('oneData', Encode.encode(onedata))
        // console.log('test one2', onedata)
        this.$store.state.ModuleAdminManage.QuotationformData = await data
        this.$router.push({ path: '/Quotation?role=purchaser&qu_id=-1' }).catch(() => { })
      } else {
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่มีข้อมูลแก้ไขใบเสนอราคานี้',
          showConfirmButton: false,
          timer: 1500
        })
        this.$router.push({ path: '/checkout' }).catch(() => { })
      }
    },
    async cancelCoupon () {
      // การยกเลิกการเลือกคูปองและคะแนน
      this.SelectCouponOrPoint = true
      //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
      this.nameCouponOrPoint = ''
      if (localStorage.getItem('CouponOrPoint') !== null) {
        if (localStorage.getItem('CouponOrPoint') === 'Point') {
          // จะไปเรียกการยกเลิก function CancleBookPointCheckout  ในไฟล์ CouponCart
          this.$EventBus.$emit('CancleBookPointCheckout')
        } else {
          // จะไปเรียกการยกเลิก function CancleBookCouponCheckout  ในไฟล์ CouponCart
          this.$EventBus.$emit('CancleBookCouponCheckout')
        }
      }
    },
    async selectcouponorpointCheckout () {
      // function การเลือกคูปองและคะแนน
      if (this.SelectCouponOrPoint === true) {
        // การเลือกคูปองและคะแนน
        this.SelectCouponOrPoint = false
        if (localStorage.getItem('CouponOrPoint') !== null) {
          if (localStorage.getItem('CouponOrPoint') === 'Point') {
            var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
            this.nameCouponOrPoint = 'ใช้คะแนน ' + onedata.cartData.coupon[0].point + ' คะแนน'
            // this.getCart()
          } else {
            onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
            this.nameCouponOrPoint = onedata.cartData.coupon[0].coupon_name
            // this.getCart()
          }
        }
      }
      // } else {
      //   // การยกเลิกการเลือกคูปองและคะแนน
      //   this.SelectCouponOrPoint = true
      //   //  nameCouponOrPoint คือ ชื่อที่แสดงบนหน้าเว็บตรงส่วนที่ให้เลือก
      //   this.nameCouponOrPoint = ''
      //   if (localStorage.getItem('CouponOrPoint') !== null) {
      //     if (localStorage.getItem('CouponOrPoint') === 'Point') {
      //       // จะไปเรียกการยกเลิก function CancleBookPointCheckout  ในไฟล์ CouponCart
      //       await this.$EventBus.$emit('CancleBookPointCheckout')
      //     } else {
      //       // จะไปเรียกการยกเลิก function CancleBookCouponCheckout  ในไฟล์ CouponCart
      //       await this.$EventBus.$emit('CancleBookCouponCheckout')
      //     }
      //   }
      // }
    },
    async Click_Coupon () {
      var data = ''
      var companyID
      var list = []
      this.$store.commit('openLoader')
      if (localStorage.getItem('CouponOrPoint') !== null) {
        if (localStorage.getItem('CouponOrPoint') === 'Point') {
          this.SelectCouponOrPoint = true
          this.nameCouponOrPoint = ''
          await this.$EventBus.$emit('CancleBookPointCheckout')
        } else {
          this.SelectCouponOrPoint = true
          this.nameCouponOrPoint = ''
          await this.$EventBus.$emit('CancleBookCouponCheckout')
        }
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCoupon = {
        seller_shop_id: this.cartData.seller_shop_id,
        role_user: dataRole.role,
        company_id: companyID,
        special_price_id: this.oneDataSpecial === 'yes' ? onedata.cartData.product_special_price_id : -1,
        net_price: parseInt(this.itemsCart.total_price_no_vat)
      }
      await this.$store.dispatch('actionsGetCouponCart', dataCoupon)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateGetCouponCart
      if (response.code === 200) {
        // จัดเรียงข้อมูล ให้สามารใส่ใน Card ได้
        for (let i = 0; i < response.data.coupon.length; i++) {
          list.push({
            image: response.data.coupon[i].couponImagePath,
            name: response.data.coupon[i].couponName,
            description: response.data.coupon[i].couponDescription,
            couponDate: {
              useStartDate: response.data.coupon[i].useStartDate,
              useEndDate: response.data.coupon[i].useEndDate
            },
            couponId: response.data.coupon[i].couponId,
            status: response.data.coupon[i].couponType,
            discount: response.data.coupon[i].discount,
            real_discount: response.data.coupon[i].real_discount,
            shop_name: response.data.coupon[i].shop_name
          })
        }
        data = list
        this.$refs.CouponCart.open(data, dataCoupon, 'checkout')
        this.$store.commit('closeLoader')
      } else {
        if (response.message === 'NGS ShopID not found.') {
          this.$swal.fire({ text: 'ยังไม่มี ShopID ใน NGS', icon: 'error', timerProgressBar: true, showConfirmButton: false, timer: 1500 })
        }
        this.$store.commit('closeLoader')
      }
    },
    async goProductDetail (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } }).catch(() => {})
      // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } })
      // window.location.assign(routeData.href, '_blank')
    },
    gotoShopDetail (name, id) {
      const shopCleaned = encodeURIComponent(name.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${id}` }).cache(() => {})
    }
  }
}
</script>

<style scoped>
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.disChipClickCoupon {
  color: #989898 !important;
  border-color: #989898 !important;
}
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  height: 50%;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
</style>

<style>
.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}

.ant-table-thead>tr>th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}

.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  top: 40px;
}

.ant-time-picker-panel {
  width: 418px;
}

@media (max-width: 767px) {
  .ant-time-picker-panel {
    width: 300px;
  }
}
.ant-time-picker-panel-select:first-child {
  width: 50%;
}

.ant-time-picker-panel-select:last-child {
  width: 50%;
}

.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
  padding: 0 0 0 0px;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}
</style>

<style lang="css" scoped>
.m-auto {
  margin: auto;
}

.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}

.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}

.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}

.totalPriceFont {
  font-size: 20px;
}

::v-deep .ant-table-pagination {
  display: none;
}

::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}

::v-deep .ant-table-thead>tr>th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-tbody>tr>td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-bordered .ant-table-thead>tr>th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}

::v-deep .ant-table-bordered .ant-table-body>table {
  border: 0px solid #e8e8e8;
}

::v-deep .ant-table-bordered .ant-table-tbody>tr>td {
  border: 0px solid #e8e8e8;
}

::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px transparent;
  margin-bottom: 6px;
  border-radius: 8px;
}</style>

<style>
.custom-background .v-input__slot {
  background-color: #E6E6E6 !important;
}

.v-input--selection-controls .v-input__slot,
.v-input--selection-controls .v-radio {
  margin-bottom: 0px;
  cursor: pointer;
}</style>
<style lang="css" scoped>.v-Card {
  border-radius: 8px;
}

.Textcard {
  align-content: center;
  font-size: 16px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
  padding-right: 0px;
}
.Textcard2 {
  align-content: center;
  font-size: 16px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-right: 0px;
}
.TextcardMobileSize {
  font-size: 14px;
  font-weight: 400;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
  padding-right: 0px;
}

.TextBaht {
  font-size: 16px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
}

.TextBahtMobileSize {
  font-size: 14px;
  font-weight: 700;
  Line-height: 22.4px;
  padding-top: 34.75px !important;
}
.is-disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>

<style scoped>
.custom-radio input:disabled + .v-input--selection-controls .v-input__control::before {
  color: #27AB9C !important; /* Set your desired color */
}
</style>
