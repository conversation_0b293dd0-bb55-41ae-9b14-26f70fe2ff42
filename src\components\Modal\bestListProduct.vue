<template>
  <div class="text-center">
    <v-dialog v-model="BestListProduct" width="519" persistent scrollable>
      <v-card class="inner-right" id="style-15">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>สินค้าขายดี</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="BestListProduct = !BestListProduct" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
            <v-card-title>
      <v-avatar size="40">
             <img
              src="../../assets/icons/nullproduct.png"
              alt="Product"
             >
      </v-avatar>
      <div class="mx-3"></div>
          สินค้าขายดี
      </v-card-title>
  <hr/>
<v-list height="1042">
      <v-list-item-group
      >
        <v-list-item
          v-for="(item, i) in itemsListProduct"
          :key="item.user_id"
        >
        <div>{{i+1}}</div>
          <v-list-item-avatar size="100">
           <img v-if="item.product_image"
              :src="item.product_image"
              alt="Product"
             >
             <img v-else
              src="../../assets/icons/nullproduct.png"
              alt="Product"
             >
          </v-list-item-avatar>
          <v-list-item-content class="mx-1 pr-12">
            <v-list-item-title v-text="item.product_name"></v-list-item-title>
          </v-list-item-content>
          <div style="font-size: 16px;color: #1AB759;">{{item.total_sold_prize}}&nbsp;&nbsp;ชิ้น</div>
          <!-- <v-list-item-content>
            <v-list-item-title v-text="item.price"></v-list-item-title>
          </v-list-item-content> -->
        </v-list-item>
      </v-list-item-group>
    </v-list>
          <!-- <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="CreateAddress()">บันทึก</v-btn>
          </v-card-actions> -->
          <!-- <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="CreateAddress()">บันทึก</v-btn>
          </v-card-actions> -->
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      itemsListProduct: [],
      BestListProduct: false
    }
  },
  watch: {
  },
  created () {
    this.$EventBus.$on('dialogDataProduct', this.dialogDataProduct)
  },
  destroyed () {
    this.$EventBus.$off('dialogDataUser')
  },
  mounted () {
  },
  methods: {
    async dialogDataProduct (data) {
      // console.log('dialogDataProduct', data)
      this.BestListProduct = await !this.BestListProduct
      this.itemsListProduct = await data
    }
  }
}
</script>
<style scoped>
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
.inner-right {
    height: auto;
    overflow-y: scroll;
}
#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
