<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
  <!-- <v-container grid-list-xs> -->
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ใบเสนอราคา</v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#1AB759" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> ใบเสนอราคา</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" md="7" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'px-3 mb-3'">
          <v-text-field v-model="search" @change="getListData()" dense hide-details outlined placeholder="ค้นหา" style="border-radius: 8px;">
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col :class="MobileSize ? 'px-3' : IpadSize ? 'px-3' : ''" cols="12" md="5" sm="12">
          <v-row dense>
            <v-col cols="3" md="4" sm="2" class="mt-2" :class="IpadSize ? 'pl-0' : 'pl-0'">
              <span style="font-size: 16px;" class="pt-5">
                สถานะใบเสนอราคา :
              </span>
            </v-col>
            <v-col cols="9" md="7" sm="10" :class="MobileSize ? '' : IpadSize ? 'mb-2' : 'ml-2 mr-4'">
              <v-select
                outlined
                dense
                v-model="stateType"
                style="border-radius: 8px;"
                :items="['ทั้งหมด','อนุมัติ','รออนุมัติ']"
                @change="getListData()"
                placeholder="ทั้งหมด"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-card outlined class="small-card mx-4 my-5" min-height="436">
            <v-data-table
              :items="pagedItems"
              :headers="headers"
              show-group-by
              hide-default-footer
              :items-per-page="itemsListOrderFlat.length"
              group-by="his_request_product_id"
              :sort-by="[]"
              :custom-sort="() => pagedItems"
            >
              <template #[`group.header`]="{ group, items, headers }">
                <td :colspan="headers.length-1" style="background-color: #F8FCFF;">
                  <span class="mr-4"><b>TMTID : </b>{{ items[0].tmt_code }}</span>
                  <span><b>ชื่อยา : </b>{{ items[0].name || 'ไม่ระบุ' }}</span>
                </td>
                <td :colspan="1" style="background-color: #F8FCFF; text-align: -webkit-right;">
                  <v-btn text rounded color="#269AFD" small @click="goDetailQU(item)">
                    <v-icon class="pr-1" color="#269AFD" small>mdi-file-document-outline</v-icon>
                    <b class="text-decoration-underline">ใบเทียบราคา</b>
                  </v-btn>
                </td>
              </template>
              <template #item="{ item }">
                <tr :style="{ backgroundColor: item.approve_status === 'reject' ? '#EAEAEA' : '' }">
                  <td class="text-start">{{ item.order_number }}</td>
                  <td class="text-start">{{ item.ppr_id }}</td>
                  <td class="text-start">{{ item.shop_name }}</td>
                  <td class="text-center">
                    <v-btn
                      elevation="0"
                      min-width="36"
                      height="36"
                      style="padding: 0 0px; border: 1px solid gainsboro;"
                      :style="item.approve_status === 'reject' ? 'background-color: #EAEAEA;' : 'background-color: #FFFFFF;'"
                      @click="gotodetail(item)"
                    >
                      <v-icon color="#3EC6B6">mdi-pill</v-icon>
                    </v-btn>
                  </td>
                  <td class="text-start">
                    <v-chip
                      v-if="item.approve_status === 'waiting_approve'"
                      class="justify-center"
                      style="width: 110px;"
                      text-color="#FAAD14"
                      color="#FFF8EF"
                    >รออนุมัติ</v-chip>
                    <v-chip
                      v-else-if="item.approve_status === 'approve'"
                      class="justify-center"
                      style="width: 110px;"
                      text-color="#52C41A"
                      color="#EFFFF1"
                    >อนุมัติ</v-chip>
                  </td>
                  <td class="text-start">
                    <span
                      @click="gotoQUPDF(item)"
                      style="text-decoration: underline; color: #27AB9C; cursor: pointer;"
                    >{{ item.order_number }}</span>
                  </td>
                  <td class="text-start">{{ formatDateToShow(item.created_at) }}</td>
                  <td class="text-end"   :style="{
                      backgroundColor: item.approve_status === 'reject' ? '#EAEAEA' : 'white',
                      position: 'sticky',
                      right: '0',
                      zIndex: 10
                    }">
                    <v-btn text rounded color="#27AB9C" small @click="goDetailQU(item)">
                      <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                      <b class="text-decoration-underline">รายละเอียด</b>
                    </v-btn>
                  </td>
                </tr>
              </template>
            </v-data-table>
            <div class="text-center my-4 d-flex align-center justify-end mr-4">
              <span> จำนวนรายการ </span>
              <v-select class="pt-0 mt-0 mx-4" :items="pageGroupOptions" v-model="computedItemsPerPageGroup" style="max-width: 60px" @change="handleItemsPerPageChange" hide-details></v-select>
              <span> {{ groupRangeText }} </span>
              <v-icon class="mx-4" @click="prevPage" :disabled="currentPage === 1">mdi-chevron-left</v-icon>
              <v-icon @click="nextPage" :disabled="currentPage === pageCount">mdi-chevron-right</v-icon>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      stateType: 'ทั้งหมด',
      search: '',
      currentPage: 1,
      pageGroupOptions: [5, 10, 15, 20, 'All'],
      computedItemsPerPageGroup: 5,
      itemsPerPageGroup: 5,
      groupedItems: [],
      itemsListOrder: [],
      DataTable: [],
      dataRole: '',
      role: '',
      pplToken: '',
      headers: [
        { text: 'เลขที่ใบเสนอราคา', value: 'qu_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150', groupable: false },
        { text: 'หมายเลขใบรีเควส', value: 'ppr_id', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150', groupable: false },
        { text: 'ชื่อร้านค้า', value: 'shop_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230', groupable: false },
        { text: 'ข้อมูลสินค้า', value: 'detail_product', sortable: false, filterable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100', groupable: false },
        { text: 'สถานะใบเสนอราคา', value: 'qu_status', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150', groupable: false },
        { text: 'ใบเสนอราคา', value: 'qu_path', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180', groupable: false },
        { text: 'วันที่สร้างรายการ', value: 'create_date', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180', groupable: false },
        { text: '', value: 'action', sortable: false, align: 'end', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150', groupable: false }
      ]
    }
  },
  computed: {
    groupRangeText () {
      const seen = new Set()
      const orderedKeys = []
      for (const item of this.itemsListOrderFlat) {
        const key = item.his_request_product_id
        if (!seen.has(key)) {
          seen.add(key)
          orderedKeys.push(key)
        }
      }
      const total = orderedKeys.length
      const start = (this.currentPage - 1) * this.itemsPerPageGroup + 1
      const end = Math.min(start + this.itemsPerPageGroup - 1, total)

      return `${start}-${end} of ${total}`
    },
    pagedItems () {
      const groupMap = new Map()
      console.log('this.itemsListOrderFlat', this.itemsListOrderFlat)
      for (const item of this.itemsListOrderFlat) {
        const key = item.his_request_product_id
        if (!groupMap.has(key)) {
          groupMap.set(key, [])
        }
        groupMap.get(key).push(item)
      }
      const groupEntries = Array.from(groupMap.entries())
      const start = (this.currentPage - 1) * this.itemsPerPageGroup
      const end = start + this.itemsPerPageGroup
      const paginatedGroups = groupEntries.slice(start, end)
      const result = []
      for (const [, items] of paginatedGroups) {
        result.push(...items)
      }
      return result
    },
    pageCount () {
      const seen = new Set()
      const orderedKeys = []
      for (const item of this.itemsListOrderFlat) {
        const key = item.his_request_product_id
        if (!seen.has(key)) {
          seen.add(key)
          orderedKeys.push(key)
        }
      }
      return Math.ceil(orderedKeys.length / this.itemsPerPageGroup)
    },
    itemsListOrderFlat () {
      return this.itemsListOrder.flatMap(item =>
        item.list_order.map(sub => ({
          ...sub,
          tmt_code: item.tmt_code,
          name: item.name,
          ppr_id: item.ppr_id,
          approve_status_group: item.approve_status,
          quantity: item.quantity,
          unit: item.unit,
          his_request_product_id: item.his_request_product_id
        }))
      )
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/MyQuotationMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/MyQuotation' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.role = JSON.parse(localStorage.getItem('roleUser'))
      this.pplToken = onedata.user.access_token
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.getListData()
  },
  methods: {
    goDetailQU () {
      if (this.MobileSize) {
        this.$router.push({ path: '/DetailMyQuotationMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/DetailMyQuotation' }).catch(() => {})
      }
    },
    handleItemsPerPageChange (value) {
      if (value === 'All') {
        const seen = new Set()
        const orderedKeys = []
        for (const item of this.itemsListOrderFlat) {
          const key = item.his_request_product_id
          if (!seen.has(key)) {
            seen.add(key)
            orderedKeys.push(key)
          }
        }
        if (this.computedItemsPerPageGroup === 'All') {
          this.itemsPerPageGroup = orderedKeys.length
        }
      } else {
        this.itemsPerPageGroup = value
      }
      this.currentPage = 1
    },
    nextPage () {
      window.scrollTo(0, 0)
      if (this.currentPage < this.pageCount) this.currentPage++
    },
    prevPage () {
      window.scrollTo(0, 0)
      if (this.currentPage > 1) this.currentPage--
    },
    customSort (items) {
      return items
    },
    gotoQUPDF (item) {
      window.open(item.pdf_path)
    },
    async getListData () {
      this.$store.commit('openLoader')
      var data = {
        company_id: '31',
        com_perm_id: '199',
        search: this.search,
        status: this.stateType === 'อนุมัติ' ? 'approve' : this.stateType === 'รออนุมัติ' ? 'waiting_approve' : '',
        offset: 10,
        page: 1
      }
      await this.$store.dispatch('actionsListMyQTCompany', data)
      const response = await this.$store.state.ModuleAdminManage.stateListMyQTCompany
      if (response.result === 'Success') {
        this.$store.commit('closeLoader')
        this.itemsListOrder = response.data.list_request
      } else {
        this.$store.commit('closeLoader')
        this.itemsListOrder = []
      }
    },
    getTotal (items) {
      let total = 0
      total = items.reduce((prev, current) => {
        return prev + current.value
      }, 0)
      return total
    },
    formatDateToShow (dateTime) {
      if (!dateTime) return null
      const date = new Date(dateTime)
      const day = date.getDate()
      const year = date.getFullYear() + 543
      const monthThai = date.toLocaleString('th-TH', { month: 'long' })
      return `${day} ${monthThai} ${year}`
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userprofile' }).catch(() => {})
      }
    }
  }
}
</script>

<style>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
</style>

<style lang="scss" scoped>
::v-deep table {
  tbody tr td:last-child,
  thead tr th:last-child {
    position: sticky !important;
    position: -webkit-sticky !important;
    right: 0;
    z-index: 10;
    background: white;
  }
  thead tr th:last-child {
    z-index: 11;
  }
}
::v-deep table {
  border-collapse: separate !important;
}
::v-deep .backgroundTable {
    background-color: #F2F9FF !important;
    /* border-color: #e6f5f3 !important; */
}
</style>

<style scoped>
.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}
.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}
.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}
.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}
.m-auto {
margin: auto;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
::v-deep .ant-table-pagination {
  display: none;
}
::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}
::v-deep .ant-table-thead > tr > th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-tbody > tr > td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-thead > tr > th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-body > table {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table-bordered .ant-table-tbody > tr > td {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #e8e8e8;
  margin-bottom: 6px;
  border-radius: 8px;
}
</style>
