import AxiosHomepage from '../store_homepage_api/axios_hompage_api'

const ModuleHompage = {
  state: {
    // Homepage
    statehomepagedata: [],
    // more_new_products_home
    stateMoreNewProductHome: [],
    // more_bs_products_home
    stateMoreBestSeller: [],
    // Search Text
    stateSearchText: [],
    // Search Text New
    stateSearchTextNew: [],
    stateSearchImage: [],
    stateGetBanner: [],
    stateGetCategory: [],
    stateGetHomeCoupon: [],
    stateGroupStoreList: [],
    stateDetailGroupSeller: [],
    stateDeleteGroupSeller: [],
    stateGroupStoreName: [],
    stateCreateGroupStoreName: [],
    stateEditGroupStoreName: [],
    stateCreateBotChat: [],
    statelistBotChatWithUser: [],
    stateChatShop: [],
    stateFlashSaleProduct: [],
    stateFlashSaleProductSystem: [],
    stateGetLinkMobile: [],
    stateGetRedirectLinkMobile: [],
    stateGetProductCardLandingPage: [],
    stateSendOTPResetPassword: [],
    stateResetPasswordByOTP: [],
    stateResetPasswordByEmail: [],
    stateGetProvinceAll: [],
    // stateProductOTOP: [],
    stateGetProduct: [],
    stateProductType: [],
    stateProductTypeV2: [],
    stateProductStatus: [],
    stateDetailServiceCoupon: [],
    stateSearchTextProduct: [],
    stateSendOTPRegister: [],
    stateRegisterWebByOTP: [],
    stateViewDuration: [],
    stateSearchProductTag: [],
    stateCreateLogUser: [],
    stateSearchBarListAI: []
  },
  mutations: {
    // Homepage
    mutationHompage (state, data) {
      state.statehomepagedata = data
    },
    // more_new_products_home
    mutationMoreNewProductHome (state, data) {
      state.stateMoreNewProductHome = data
    },
    // more_bs_products_home
    mutationMoreBestSeller (state, data) {
      state.stateMoreBestSeller = data
    },
    // Search Text
    mutationSearchText (state, data) {
      state.stateSearchText = data
    },
    // Search Test New
    mutationsSearchTextNew (state, data) {
      state.stateSearchTextNew = data
    },
    mutationsSearchImage (state, data) {
      state.stateSearchImage = data
    },
    mutationsGetBanner (state, data) {
      state.stateGetBanner = data
    },
    mutationsGetCategory (state, data) {
      state.stateGetCategory = data
    },
    mutationsGetHomeCoupon (state, data) {
      state.stateGetHomeCoupon = data
    },
    mutationsGroupStoreList (state, data) {
      state.stateGroupStoreList = data
    },
    mutationsDetailGroupSeller (state, data) {
      state.stateDetailGroupSeller = data
    },
    mutationsDeleteGroupSeller (state, data) {
      state.stateDeleteGroupSeller = data
    },
    mutationsGroupStoreName (state, data) {
      state.stateGroupStoreName = data
    },
    mutationsCreateGroupStoreName (state, data) {
      state.stateCreateGroupStoreName = data
    },
    mutationsEditGroupStoreName (state, data) {
      state.stateEditGroupStoreName = data
    },
    mutationsCreateBotChat (state, data) {
      state.stateCreateBotChat = data
    },
    mutationslistBotChatWithUser (state, data) {
      state.statelistBotChatWithUser = data
    },
    mutationsChatShop (state, data) {
      state.stateChatShop = data
    },
    mutationsFlashSaleProduct (state, data) {
      state.stateFlashSaleProduct = data
    },
    mutationsFlashSaleProductSystem (state, data) {
      state.stateFlashSaleProductSystem = data
    },
    mutationsGetLinkMobile (state, data) {
      state.stateGetLinkMobile = data
    },
    mutationsGetRedirectLinkMobile (state, data) {
      state.stateGetRedirectLinkMobile = data
    },
    mutationsGetProductCardLandingPage (state, data) {
      state.stateGetProductCardLandingPage = data
    },
    mutationsSendOTPResetPassword (state, data) {
      state.stateSendOTPResetPassword = data
    },
    mutationsResetPasswordByOTP (state, data) {
      state.stateResetPasswordByOTP = data
    },
    mutationsResetPasswordByEmail (state, data) {
      state.stateResetPasswordByEmail = data
    },
    mutationsGetProvinceAll (state, data) {
      state.stateGetProvinceAll = data
    },
    // mutationsProductOTOP (state, data) {
    //   state.stateProductOTOP = data
    // },
    mutationsGetProduct (state, data) {
      state.stateGetProduct = data
    },
    mutationsProductType (state, data) {
      state.stateProductType = data
    },
    mutationsProductTypeV2 (state, data) {
      state.stateProductTypeV2 = data
    },
    mutationsProductStatus (state, data) {
      state.stateProductStatus = data
    },
    mutationsDetailServiceCoupon (state, data) {
      state.stateDetailServiceCoupon = data
    },
    mutationsSearchTextProduct (state, data) {
      state.stateSearchTextProduct = data
    },
    mutationsSendOTPRegister (state, data) {
      state.stateSendOTPRegister = data
    },
    mutationsRegisterWebByOTP (state, data) {
      state.stateRegisterWebByOTP = data
    },
    mutationsViewDuration (state, data) {
      state.stateViewDuration = data
    },
    mutationsSearchProductTag (state, data) {
      state.stateSearchProductTag = data
    },
    mutationsCreateLogUser (state, data) {
      state.stateCreateLogUser = data
    },
    mutationsSearchBarListAI (state, data) {
      state.stateSearchBarListAI = data
    }
  },
  actions: {
    // Homepage
    async actionHompage (context, access) {
      const dataHomepage = await AxiosHomepage.GetHomepage(access)
      await context.commit('mutationHompage', dataHomepage)
    },
    // more_new_products_home
    async actionMoreNewProductHome (context, access) {
      const dataAllNewProduct = await AxiosHomepage.GetMoreNewProduct(access)
      await context.commit('mutationMoreNewProductHome', dataAllNewProduct)
    },
    // more_bs_products_home
    async actionMoreBestSeller (context, access) {
      const dataAllBestSeller = await AxiosHomepage.GetMoreBestSeller(access)
      await context.commit('mutationMoreBestSeller', dataAllBestSeller)
    },
    // Search Text
    async actionSearchText (context, access) {
      const dataSearch = await AxiosHomepage.GetSearchResult(access)
      await context.commit('mutationSearchText', dataSearch)
    },
    // Search Test New
    async actionsSearchTextNew (context, access) {
      const dataSearchNew = await AxiosHomepage.GetSearchResultNew(access)
      await context.commit('mutationsSearchTextNew', dataSearchNew)
    },
    async actionsSearchImage (context, access) {
      const dataSearchNew = await AxiosHomepage.GetSearchResultImage(access)
      await context.commit('mutationsSearchImage', dataSearchNew)
    },
    async actionsGetBanner (context, access) {
      const dataGetBanner = await AxiosHomepage.GetBanner(access)
      await context.commit('mutationsGetBanner', dataGetBanner)
    },
    async actionsGetHomeCoupon (context, access) {
      const response = await AxiosHomepage.GetHomeCoupon(access)
      await context.commit('mutationsGetHomeCoupon', response)
    },
    async actionsGetCategory (context, access) {
      const response = await AxiosHomepage.GetCategory(access)
      await context.commit('mutationsGetCategory', response)
    },
    async actionsGroupStoreList (context, access) {
      const response = await AxiosHomepage.GroupStoreList(access)
      await context.commit('mutationsGroupStoreList', response)
    },
    async actionsDetailGroupSeller (context, access) {
      const response = await AxiosHomepage.DetailGroupSellerShop(access)
      await context.commit('mutationsDetailGroupSeller', response)
    },
    async actionsDeleteGroupSeller (context, access) {
      const response = await AxiosHomepage.DeleteGroupSeller(access)
      await context.commit('mutationsDeleteGroupSeller', response)
    },
    async actionsGroupStoreName (context, access) {
      const response = await AxiosHomepage.GroupStoreName(access)
      await context.commit('mutationsGroupStoreName', response)
    },
    async actionsCreateGroupStoreName (context, access) {
      const response = await AxiosHomepage.CreateGroupStoreName(access)
      await context.commit('mutationsCreateGroupStoreName', response)
    },
    async actionsEditGroupStoreName (context, access) {
      const response = await AxiosHomepage.EditGroupStoreName(access)
      await context.commit('mutationsEditGroupStoreName', response)
    },
    async actionsCreateBotChat (context, access) {
      const response = await AxiosHomepage.CreateBotChat(access)
      await context.commit('mutationsCreateBotChat', response)
    },
    async actionslistBotChatWithUser (context, access) {
      const response = await AxiosHomepage.listBotChatWithUser(access)
      await context.commit('mutationslistBotChatWithUser', response)
    },
    async actionsChatShop (context, access) {
      const response = await AxiosHomepage.ChatShop(access)
      await context.commit('mutationsChatShop', response)
    },
    async actionsFlashSaleProductatShop (context, access) {
      const response = await AxiosHomepage.FlashSaleProduct(access)
      await context.commit('mutationsFlashSaleProduct', response)
    },
    async actionsFlashSaleProductSystem (context, access) {
      const response = await AxiosHomepage.FlashSaleProductSystem(access)
      await context.commit('mutationsFlashSaleProductSystem', response)
    },
    async actionsGetLinkMobile (context, access) {
      const response = await AxiosHomepage.GetLinkMobile(access)
      await context.commit('mutationsGetLinkMobile', response)
    },
    async actionsGetRedirectLinkMobile (context, access) {
      const response = await AxiosHomepage.GetRedirectLinkMobile(access)
      await context.commit('mutationsGetRedirectLinkMobile', response)
    },
    async actionsProductCardLandingPage (context, access) {
      const response = await AxiosHomepage.GetProductCardLandingPage(access)
      await context.commit('mutationsGetProductCardLandingPage', response)
    },
    async actionsSendOTPResetPassword (context, access) {
      const response = await AxiosHomepage.SendOTPResetPassword(access)
      await context.commit('mutationsSendOTPResetPassword', response)
    },
    async actionsResetPasswordByOTP (context, access) {
      const response = await AxiosHomepage.ResetPasswordByOTP(access)
      await context.commit('mutationsResetPasswordByOTP', response)
    },
    async actionsResetPasswordByEmail (context, access) {
      const response = await AxiosHomepage.ResetPasswordByEmail(access)
      await context.commit('mutationsResetPasswordByEmail', response)
    },
    async actionsGetProvinceAll (context, access) {
      const response = await AxiosHomepage.GetProvinceAll(access)
      await context.commit('mutationsGetProvinceAll', response)
    },
    // async actionsProductOTOP (context, access) {
    //   const response = await AxiosHomepage.ProductOTOP(access)
    //   await context.commit('mutationsProductOTOP', response)
    // },
    async actionsGetProduct (context, access) {
      const response = await AxiosHomepage.GetProduct(access)
      await context.commit('mutationsGetProduct', response)
    },
    async actionsProductType (context, access) {
      const response = await AxiosHomepage.ProductType(access)
      await context.commit('mutationsProductType', response)
    },
    async actionsProductTypeV2 (context, access) {
      const response = await AxiosHomepage.ProductTypeV2(access)
      await context.commit('mutationsProductTypeV2', response)
    },
    async actionsProductStatus (context, access) {
      const response = await AxiosHomepage.ProductStatus(access)
      await context.commit('mutationsProductStatus', response)
    },
    async actionsDetailServiceCoupon (context, access) {
      const response = await AxiosHomepage.DetailServiceCoupon(access)
      await context.commit('mutationsDetailServiceCoupon', response)
    },
    async actionsSearchTextProduct (context, access) {
      const dataSearchNew = await AxiosHomepage.SearchTextProduct(access)
      await context.commit('mutationsSearchTextProduct', dataSearchNew)
    },
    async actionsSendOTPRegister (context, access) {
      const dataSearchNew = await AxiosHomepage.SendOTPRegister(access)
      await context.commit('mutationsSendOTPRegister', dataSearchNew)
    },
    async actionsRegisterWebByOTP (context, access) {
      const dataSearchNew = await AxiosHomepage.RegisterWebByOTP(access)
      await context.commit('mutationsRegisterWebByOTP', dataSearchNew)
    },
    async actionsViewDuration (context, access) {
      const data = await AxiosHomepage.ViewDuration(access)
      await context.commit('mutationsViewDuration', data)
    },
    async actionsSearchProductTag (context, access) {
      const data = await AxiosHomepage.SearchProductTag(access)
      await context.commit('mutationsSearchProductTag', data)
    },
    async actionsCreateLogUser (context) {
      const data = await AxiosHomepage.CreateLogUser()
      await context.commit('mutationsCreateLogUser', data)
    },
    async actionsSearchBarListAI (context, access) {
      const { keyword, limit, page } = access
      try {
        const response = await fetch(`${process.env.VUE_APP_BACK_END2}search/product/searchBar/list`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ keyword, limit, page })
        })
        if (!response.ok) {
          throw new Error('API Error')
        }
        const data = await response.json()
        return data // Return the data
      } catch (error) {
        console.error('Error in actionsSearchBarListAI:', error)
        throw error
      }
      // const data = await AxiosHomepage.SearchBarListAI(access)
      // await context.commit('mutationsSearchBarListAI', data)
    }
  }
}

export default ModuleHompage
