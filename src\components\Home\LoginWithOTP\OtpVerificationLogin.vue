<template>
  <div>
    <!-- Website -->
    <div v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="6" md="12" align="center" class="my-16">
            <v-form ref="formOTP" :lazy-validation="lazy">
              <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
                <v-card-text>
                  <v-card-title class="pb-0"><v-btn fab small elevation="0" color="#EBF1F9" @click="backtoOTPLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn></v-card-title>
                  <v-container class="pt-0">
                    <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                      <v-img :src="require('@/assets/obj.png')" max-height="50%" max-width="60%" contain/>
                    </v-row>
                    <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                      <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.LoginWithOTP') }}</span>
                    </v-row>
                    <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                      <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                    </v-row>
                    <v-row no-gutters dense class="mx-12" justify="center">
                      <v-col cols="12" md="12" sm="12">
                        <v-otp-input
                          ref="otpInput"
                          color="#269AFD"
                          v-model="otp"
                          :error="showError"
                          :length="length"
                          oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        ></v-otp-input>
                      </v-col>
                      <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                        <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.OTPSend') }} <span style="color: #269AFD;">{{maskPhoneNumber(mobile)}}</span></span>
                      </v-row>
                      <v-col cols="12" md="12" sm="12" class="mb-4">
                        <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('OTPPage.ReferenceCode') }}: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                      </v-col>
                      <v-col cols="12" md="12" sm="12">
                        <span style="font-weight: normal; font-size: 16px; color: #000000;" v-if="disableRefreshOTP === true">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span></span>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="mt-10">
                        <v-btn color="#269AFD" rounded block style="padding-left: 8%; padding-right: 8%; color: white;"  @click="checkOTP()" :disabled="!isActive">{{ $t('OTPPage.ConfirmOTP') }}</v-btn>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div v-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <v-form ref="formOTP" :lazy-validation="lazy">
            <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
              <v-card-text>
                <v-card-title class="pb-0"><v-btn fab small elevation="0" color="#EBF1F9" @click="backtoOTPLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn></v-card-title>
                <v-container class="pt-0">
                  <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                    <v-img :src="require('@/assets/obj.png')" max-height="50%" max-width="60%" contain/>
                  </v-row>
                  <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                    <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.LoginWithOTP') }}</span>
                  </v-row>
                  <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                    <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                  </v-row>
                  <v-row no-gutters dense class="mx-12" justify="center">
                    <v-col cols="12" md="12" sm="12">
                      <v-otp-input
                        ref="otpInput"
                        v-model="otp"
                        :error="showError"
                        :length="length"
                        color="#269AFD"
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                      ></v-otp-input>
                    </v-col>
                    <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                      <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.OTPSend') }} <span style="color: #269AFD;">{{maskPhoneNumber(mobile)}}</span></span>
                    </v-row>
                    <v-col cols="12" md="12" sm="12" class="mb-4 d-flex justify-center">
                      <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('OTPPage.ReferenceCode') }}: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                    </v-col>
                    <v-col cols="12" md="12" sm="12" class="d-flex justify-center">
                      <span style="font-weight: normal; font-size: 16px; color: #000000;" v-if="disableRefreshOTP === true">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span></span>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" class="mt-10">
                      <v-btn color="#269AFD" rounded block style="padding-left: 8%; padding-right: 8%; color: white;"  @click="checkOTP()" :disabled="!isActive">{{ $t('OTPPage.ConfirmOTP') }}</v-btn>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card-text>
            </v-card>
          </v-form>
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div v-if="MobileSize">
      <v-container class="my-6">
        <v-row dense justify="center">
          <v-col cols="12" md="12" align="center">
            <v-form ref="formOTP" :lazy-validation="lazy">
              <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
                <v-card-text>
                  <v-card-title class="pb-0"><v-btn fab small elevation="0" color="#EBF1F9" @click="backtoOTPLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn></v-card-title>
                  <v-container class="pt-0">
                    <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                      <v-img :src="require('@/assets/obj.png')" max-height="50%" max-width="60%" contain/>
                    </v-row>
                    <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                      <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.LoginWithOTP') }}</span>
                    </v-row>
                    <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                      <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                    </v-row>
                    <v-row no-gutters dense class="mx-0" justify="center">
                      <v-col cols="12" md="12" sm="12">
                        <v-otp-input
                          ref="otpInput"
                          v-model="otp"
                          color="#269AFD"
                          :error="showError"
                          :length="length"
                          type="number"
                          oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        ></v-otp-input>
                      </v-col>
                      <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                        <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.OTPSend') }} <span style="color: #269AFD;">{{maskPhoneNumber(mobile)}}</span></span>
                      </v-row>
                      <v-col cols="12" md="12" sm="12" class="mb-4">
                        <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('OTPPage.ReferenceCode') }}: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                      </v-col>
                      <v-col cols="12" md="12" sm="12">
                        <span style="font-weight: normal; font-size: 16px; color: #000000;" v-if="disableRefreshOTP === true">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span></span>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="mt-10">
                        <v-btn color="#269AFD" rounded block style="padding-left: 8%; padding-right: 8%; color: white;"  @click="checkOTP()" :disabled="!isActive">{{ $t('OTPPage.ConfirmOTP') }}</v-btn>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </div>
</template>

<script>
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      showError: false,
      displayCounter: '00:00',
      lazy: false,
      dataOTP: [],
      refCode: '',
      otpCode: '',
      mobile: '',
      length: 6,
      otp: '',
      email: '',
      phone: '',
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ]
      },
      counter: 0,
      disableRefreshOTP: true
    }
  },
  created () {
    this.dataOTP = JSON.parse(Decode.decode(localStorage.getItem('OTPData')))
    this.refCode = this.dataOTP.ref_code
    this.otpCode = this.dataOTP.otp
    this.mobile = this.dataOTP.mobileNo
    this.counter = 0
    this.countdownCheck(300)
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  beforeDestroy () {
    this.counter = 0
  },
  computed: {
    isActive () {
      return this.otp.length === this.length
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoOTPLogin () {
      this.$router.push({ path: '/otpLogin' }).catch(() => {})
    },
    maskPhoneNumber (phoneNumber) {
      return phoneNumber.substring(0, 2) + 'xxxxxx' + phoneNumber.substring(8)
    },
    countdownCheck (second) {
      this.counter = second
      const interval = setInterval(() => {
        var minutes = Math.floor(this.counter / 60)
        var seconds = this.counter % 60
        seconds = seconds < 10 ? `0${seconds}` : seconds
        this.displayCounter = `${minutes}:${seconds}`
        this.counter--
        if (this.counter < 0) {
          this.disableRefreshOTP = false
          clearInterval(interval)
        }
      }, 1000)
    },
    async RefreshOTP () {
      this.$store.commit('openLoader')
      this.otp = ''
      var mobile = this.mobile
      var dataReOTP = {
        mobile_no: this.mobile
      }
      await this.$store.dispatch('actionsGetOTP', dataReOTP)
      var resReOTP = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
      if (resReOTP.result === 'SUCCESS') {
        this.refCode = resReOTP.data.ref_code
        this.otpCode = resReOTP.data.otp
        localStorage.removeItem('OTPData')
        var dataOTP = {
          otp: resReOTP.data.otp,
          ref_code: resReOTP.data.ref_code,
          mobileNo: mobile
        }
        localStorage.setItem('OTPData', Encode.encode(dataOTP))
        this.disableRefreshOTP = true
        this.countdownCheck(300)
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', title: resReOTP.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async checkOTP () {
      if (this.otp === this.otpCode) {
        this.$store.commit('openLoader')
        var data = {
          mobile_no: this.mobile,
          otp: this.otp
        }
        await this.$store.dispatch('actionsConfirmOTPOneID', data)
        var res = await this.$store.state.ModuleRegister.stateConfirmOTPOneID
        // console.log(res)
        if (res.result === 'SUCCESS') {
          localStorage.setItem('AccessToken', Encode.encode(res.data))
          var dataRole = { role: 'ext_buyer' }
          localStorage.setItem('roleUser', JSON.stringify(dataRole))
          var onedata = {}
          onedata.user = res.data
          localStorage.setItem('LoginTime', Date.now())
          localStorage.setItem('oneData', Encode.encode(onedata))
          var PathRedirect = ''
          if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
            PathRedirect = sessionStorage.getItem('pathRedirect')
            if (PathRedirect === '/Login' || PathRedirect === '/Register') {
              PathRedirect = '/'
            }
          } else {
            PathRedirect = '/'
          }
          await this.$store.dispatch('actionsConGetBizDetail')
          var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
          // console.log('tong res', responseBiz)
          if (responseBiz.message === 'updated data from one id successful.') {
            localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
          }
          this.$EventBus.$emit('checkPDPA', res.data.one_id)
          // console.log(PathRedirect)
          // window.location.assign('/', '_blank')
          this.$store.commit('closeLoader')
          if (PathRedirect !== null) {
            window.location.assign(`${PathRedirect}`, '_blank')
          } else {
            window.location.assign('/', '_blank')
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', title: res.message, showConfirmButton: false, timer: 1500 })
        }
      } else {
        this.showError = true
        // this.$swal.fire({
        //   icon: 'warning',
        //   html: 'คุณต้องการส่งรหัสใหม่หรือไม่',
        //   title: 'คุณกรอกรหัส OTP ผิด',
        //   showCancelButton: true,
        //   confirmButtonText: 'ยืนยัน',
        //   cancelButtonText: 'ยกเลิก',
        //   confirmButtonColor: '#27AB9C',
        //   reverseButtons: true
        //   // cancelButtonColor: '#d33'
        // }).then(async (result) => {
        //   this.otp = ''
        //   var mobile = this.mobile
        //   if (result.isConfirmed) {
        //     this.$store.commit('openLoader')
        //     var dataReOTP = {
        //       mobile_no: this.mobile
        //     }
        //     await this.$store.dispatch('actionsGetOTP', dataReOTP)
        //     var resReOTP = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
        //     if (resReOTP.result === 'SUCCESS') {
        //       this.$store.commit('closeLoader')
        //       this.refCode = resReOTP.data.ref_code
        //       this.otpCode = resReOTP.data.otp
        //       localStorage.removeItem('OTPData')
        //       var dataOTP = {
        //         otp: resReOTP.data.otp,
        //         ref_code: resReOTP.data.ref_code,
        //         mobileNo: mobile
        //       }
        //       localStorage.setItem('OTPData', Encode.encode(dataOTP))
        //     } else {
        //       this.$store.commit('closeLoader')
        //       this.$swal.fire({ icon: 'warning', title: res.message, showConfirmButton: false, timer: 1500 })
        //     }
        //     // this.googleSentData()
        //   } else if (result.isDismissed) {
        //     this.$router.push({ path: '/otpLogin' }).catch(() => {})
        //   }
        // }).catch(() => {
        // })
      }
    }
  }
}
</script>

<style scoped>
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
