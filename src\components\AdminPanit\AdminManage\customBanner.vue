<template>
  <v-card width="100%" height="100%" elevation="0" :class="MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4'">
    <v-col class="pt-6">
      <span v-if="MobileSize" class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">
        <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ปรับแต่ง Banner</span>
      <span v-else class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">ปรับแต่ง Banner</span>
    </v-col>
    <v-container :class="MobileSize ? 'pa-0' : 'pt-4 pb-2'">
      <v-row dense>
        <v-tabs
          v-model="tab"
          background-color="transparent"
          @change="changeTab(tab)"
          grow
        >
          <v-tab
          v-for="item in itemTab"
          :key="item"
          >
          <span style="font-size: small; font-weight: 600;">{{ item }}</span>
          </v-tab>
        </v-tabs>
      </v-row>
      <v-row v-if="tab === 0">
        <v-col cols="12">
          <v-row>
          <v-col cols="12" class="d-flex align-center my-2">
            <v-card-title class="mr-auto" style="font-size: large; font-weight: 600;">Custom Icon</v-card-title>
            <v-btn v-if="items.length < 8" @click="addItem" rounded class="mr-2" style="font-weight: 600;" color="primary">เพิ่มไอคอน</v-btn>
            <!-- <v-btn v-if="items.some(item => !item.saved)" color="primary" small @click="saveEditIcon" class="mr-2" ><v-icon small class="mr-1">mdi-content-save-all</v-icon>บันทึก</v-btn> -->
          </v-col>
          <div v-if="items.length === 0" style="width: 100%; display: flex; justify-content: center; opacity: 0.9;" class="my-5"><span style="">กรุณาเพิ่มไอคอน</span></div>
          <v-col cols="12" class="d-flex justify-center">
            <draggable v-model="items" style="display: grid; max-width: 100vw; margin: auto; justify-items: center;" :style="IpadProSize ? `grid-template-columns: repeat(${numIcon}, 70px); column-gap: 10px; row-gap: 20px;` : IpadSize ? `grid-template-columns: repeat(${numIcon}, 50px); column-gap: 30px; row-gap: 20px;` : MobileSize ? `grid-template-columns: repeat(${numIcon}, 40px); column-gap: 30px; row-gap: 20px;` : `grid-template-columns: repeat(${numIcon}, 100px); column-gap: 10px; row-gap: 30px;`" @end="reorderItems">
              <div v-for="(item) in items" :key="item.index_in_type">
                <!-- PC -->
                <div v-if="!MobileSize && !IpadSize && !IpadProSize" style="height: 120px; width: 100px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 80px; height: 80px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`">
                    <v-btn @click="openDialogEditIcon(item)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="item.path" :lazy-src="item.path_lazy" style="max-height: 48px; max-width: 48px; z-index: 5;" width="48" height="48"></v-img>
                    <div v-else style="min-height: 48px; max-height: 48px; width: 48px;"></div>
                    <v-btn @click="removeItem(item.index_in_type)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 13px; text-align: center;">{{ item.name }}</span>
                </div>
                <!-- ipad Pro -->
                <div v-if="IpadProSize" style="height: 100px; width: 70px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 60px; height: 60px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`">
                    <v-btn @click="openDialogEditIcon(item)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="item.path" :lazy-src="item.path_lazy" style="max-height: 40px; max-width: 40px; z-index: 5;" width="40" height="40"></v-img>
                    <div v-else style="min-height: 40px; max-height: 40px; width: 40px;"></div>
                    <v-btn @click="removeItem(item.index_in_type)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 10px; text-align: center;">{{ item.name }}</span>
                </div>
                <!-- ipad -->
                <div v-if="IpadSize || MobileSize" style="height: 80px; width: 54px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 42px; height: 42px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`">
                    <v-btn @click="openDialogEditIcon(item)" width="14" height="14" elevation="2" icon style="margin-right: -30px; z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="item.path" :lazy-src="item.path_lazy" style="max-height: 28px; max-width: 28px; z-index: 5;" width="28" height="28"></v-img>
                    <div v-else style="min-height: 28px; max-height: 28px; width: 28px;"></div>
                    <v-btn @click="removeItem(item.index_in_type)" width="14" height="14" elevation="2" icon style="margin-right: -30px; z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 8px; text-align: center;">{{ item.name }}</span>
                </div>
                <!-- mobile -->
                <!-- <div v-if="MobileSize" style="height: 70px; width: 40px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 36px; height: 36px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`" @click="item.href ? gotoLink(item.href) : ''">
                    <v-btn @click="openDialogEditIcon(item)" width="14" height="14" elevation="2" icon style="margin-right: -30px; z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path" :lazy-src="item.path_lazy" style="max-height: 20px; max-width: 20px; z-index: 5;" width="20" height="20"></v-img>
                    <div v-else style="min-height: 20px; max-height: 20px; width: 20px;"></div>
                    <v-btn @click="removeItem(item.index_in_type)" width="14" height="14" elevation="2" icon style="margin-right: -30px; z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 8px; text-align: center;">{{ item.name }}</span>
                </div> -->
              </div>
            </draggable>
          </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="d-flex align-center my-2">
          <v-card-title class="mr-auto" style="font-size: large; font-weight: 600; ">Custom Coin</v-card-title>
          <v-btn rounded class="mr-2" style="font-weight: 600; height: 35px;" color="primary" @click="resetToDefault">ล้างค่า</v-btn>
        </v-col>
        <v-col cols="12" class="d-flex justify-center">
          <v-card width="90%" height="250" style="border-radius: 24px;" :style="bgType === 'Color' ? `background-color: ${bgColor};` : bgType === 'Image' ? `background-image: url('${bgImage}');`
          : bgType === 'Gradient' ? `background: linear-gradient(90deg, ${bgColor} -4%, ${bgColor2} 103.33%); box-shadow: 0px 0px 1px 0px #C3CDD517; box-shadow: 0px 0px 1px 0px #C3CDD50D; box-shadow: 0px 0px 1px 0px #C3CDD503; box-shadow: 1px 0px 1px 0px #C3CDD500;` : 'background-color: #333;'">
            <div class="d-flex align-center">
              <span style="font-size: large; font-weight: 600; line-height: 3;" :style="`color: ${titleColor};`" class="pl-4 mr-2">{{ titleText }}</span>
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn @click="openDialogCustomCoin('title')" v-bind="attrs" v-on="on" x-small elevation="2" icon style="z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                </template>
                <span style="font-size: small;">เพิ่มสีอักษรและข้อความ</span>
              </v-tooltip>
              <v-spacer></v-spacer>
              <v-tooltip left>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn @click="openDialogCustomCoin('bg')" v-bind="attrs" v-on="on" x-small elevation="2" class="mr-4" icon style="z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                </template>
                <span style="font-size: small;">เพิ่มสีหรือรูปภาพพื้นหลัง</span>
              </v-tooltip>
            </div>
            <div class="d-flex align-center justify-center">
              <span style="font-size: large; font-weight: 600; line-height: 2.5;" :style="`color: ${titleCoinColor};`" class="pl-4 mr-2">0.00</span>
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn @click="openDialogCustomCoin('titleCoin')" v-bind="attrs" v-on="on" x-small elevation="2" icon style="z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                </template>
                <span style="font-size: small;">เพิ่มสีอักษร</span>
              </v-tooltip>
            </div>
            <div class="d-flex align-center justify-center">
              <v-btn rounded class="mr-2" elevation="0" style="font-weight: 600; height: 30px;" :style="`color: ${btnTextColor};`" :color="btnColor">{{ btnText }}</v-btn>
              <v-tooltip right>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn @click="openDialogCustomCoin('btn')" v-bind="attrs" v-on="on" x-small elevation="2" icon style="z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                </template>
                <span style="font-size: small;">เพิ่มสีอักษร สีปุ่ม และข้อความ</span>
              </v-tooltip>
            </div>
             <div class="d-flex align-center justify-center pt-6">
              <div class="d-flex align-center justify-center" v-for="(day, id) in days" :key="id">
                <div class="d-flex flex-column align-center" style="gap: 4px;">
                  <span style="font-size: small; font-weight: 600;" :style="`color: ${coinColor};`">{{ day.coin }}</span>
                  <v-img v-if="coinImage !== ''" width="20" height="20" :src="day.id === 7 && coinType === 'Custom' ? lastCoinImage : day.id === 1 ? checkCoinImage : coinImage"></v-img>
                  <div v-if="coinImage === ''" style="width: 20px; height: 20px; border-radius: 50%; background-color: #333333;"></div>
                  <span class="mt-1" style="font-size: small; font-weight: 600;" :style="`color: ${dayColor};`">{{ day.day }}</span>
                </div>
                <v-divider v-if="day.id !== 7" :style="MobileSize || IpadSize ? `width: 2vw; border-width: 2px 0 0 0; border-color: ${lineColor};` : `width: 3vw; border-width: 3px 0 0 0; border-color: ${lineColor};`"></v-divider>
              </div>
              <div>
                <v-tooltip right>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn @click="openDialogCustomCoin('coin')" v-bind="attrs" v-on="on" class="ml-4 mt-n16" x-small elevation="2" icon style="z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                  </template>
                  <span style="font-size: small;">เพิ่มสีอักษร สีเส้น และรูปภาพ</span>
                </v-tooltip>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="12">
          <v-card-title style="font-size: large; font-weight: 600;">Banner ( Website )</v-card-title>
          <v-row>
            <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
              <v-select v-model="selectTypeLayout" :style="IpadSize || MobileSize ? 'padding: 0 12px;' : 'padding: 0 0 0 12px;'" :items="itemSelectTypeLayout" item-text="name" item-value="value" label="รูปแบบ Banner" @change="handleSelectTypeLayout" hide-details dense outlined></v-select>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="selectTypeLayout !== null && selectTypeLayout === '2duoright'">
              <v-select v-model="selectWidthLayout" :style="IpadSize || MobileSize ? 'padding: 0 12px;' : 'padding: 0 0 0 12px;'" :items="itemSelectWidthLayout" item-text="name" item-value="value" label="ความกว้างของคอลัมน์" @change="handleSelectWidthLayout" hide-details dense outlined></v-select>
            </v-col>
          </v-row>
          <!-- <v-row v-if="selectTypeLayout !== null && selectTypeLayout === '2duo'">
            <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
              <v-select v-model="selectWidthLayout1" :style="IpadSize || MobileSize ? 'padding: 0 12px;' : 'padding: 0 0 0 12px;'" :items="itemSelectWidthLayout" item-text="name" item-value="value" label="ความกว้างของคอลัมน์ แถวด้านบน" @change="handleSelectWidth2Duo('1')" hide-details dense outlined></v-select>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
              <v-select v-model="selectWidthLayout2" :style="IpadSize || MobileSize ? 'padding: 0 12px;' : 'padding: 0 0 0 12px;'" :items="itemSelectWidthLayout" item-text="name" item-value="value" label="ความกว้างของคอลัมน์ แถวด้านล่าง" @change="handleSelectWidth2Duo('2')" hide-details dense outlined></v-select>
            </v-col>
          </v-row> -->
          <v-row>
            <v-col cols="12">
              <v-row class="mb-1 ml-1">
                <v-col :cols="12" style="margin: -20px -12px;" class="d-flex align-center">
                  <div class="mr-auto">
                    <v-card-title style="font-size: medium; font-weight: 600;">Banner ใหญ่</v-card-title>
                    <v-card-title v-if="!MobileSize && !IpadSize" style="font-size: small; font-weight: 500; color: red; margin-top: -40px;">(อัปโหลดได้สูงสุด 10 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
                  </div>
                  <v-btn v-if="data_banner_1.length === 10 ? false : true" color="primary" style="background-color: #38b2a4; position: relative; overflow: hidden; cursor: pointer;" rounded class="mt-2 mb-2">
                    <!-- <v-icon color="white" dark>mdi-plus</v-icon> -->
                    <span style="cursor: pointer; font-weight: 600;">เพิ่มรูปภาพ</span>
                    <input
                      ref="fileInput"
                      type="file"
                      :key="fileInputKey"
                      multiple
                      accept="image/jpeg, image/jpg, image/png"
                      @change="onFileChangeLayoutWeb1($event)"
                      style="opacity: 0; position: absolute; inset: 0; cursor: pointer; z-index: 1;"
                    />
                  </v-btn>
                </v-col>
              </v-row>
              <v-col v-if="MobileSize || IpadSize" :cols="12" class="d-flex justify-center">
                <v-card-title style="font-size: 12px; font-weight: 500; color: red; margin: -20px -16px -36px -16px;">(อัปโหลดได้สูงสุด 10 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
              </v-col>
              <v-card class="mt-3 pb-5 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                <v-card-text>
                  <div v-if="data_banner_1.length === 0" style="width: 100%; display: flex; justify-content: center;" class="my-8"><span style="">กรุณาเพิ่มรูปภาพ</span></div>
                  <v-row>
                    <draggable v-model="data_banner_1" style="display: grid; max-width: 100vw; margin: auto; justify-items: center;" :style="IpadProSize ? 'grid-template-columns: repeat(4, 140px); column-gap: 14px; row-gap: 20px;' : IpadSize ? 'grid-template-columns: repeat(2, 180px); column-gap: 20px; row-gap: 20px;' : MobileSize ? 'grid-template-columns: repeat(2, 140px); column-gap: 20px; row-gap: 20px;' : 'grid-template-columns: repeat(5, 160px); column-gap: 14px; row-gap: 20px;'" @end="reorderItemsDataBanner1">
                      <div v-for="(item, index) in data_banner_1" :key="item.id" class="d-flex flex-column align-center mt-5">
                        <div class="d-flex">
                          <v-img :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path" contain :width="IpadProSize || MobileSize ? '120' : '150'" :height="IpadProSize || MobileSize ? '80' : '100'" class="mb-2" style="border-radius: 8px;"></v-img>
                          <v-btn @click="removeImageLayoutWeb1(index)" icon x-small style="float: right; background-color: #ff5252; margin-top: -10px; margin-left: -12px; z-index: 2;">
                          <v-icon x-small color="white" dark>mdi-delete</v-icon>
                        </v-btn>
                        </div>
                        <v-text-field v-model="item.href" @input="onInputLink(item.id, $event, '1')" label="เพิ่มลิงก์ (ไม่จำเป็น)" class="mt-2" hide-details dense outlined></v-text-field>
                      </div>
                  </draggable>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" v-if="selectTypeLayout !== null && selectTypeLayout !== 'single'">
              <v-row class="mb-1 ml-1">
                <v-col :cols="12" style="margin: -20px -12px;" class="d-flex align-center">
                  <div class="mr-auto">
                    <v-card-title v-if="selectTypeLayout === '2duoright'" style="font-size: medium; font-weight: 600;">Banner ย่อย (ด้านบน)</v-card-title>
                    <v-card-title v-if="selectTypeLayout === 'singleduo'" style="font-size: medium; font-weight: 600;">Banner ย่อย (ด้านซ้าย)</v-card-title>
                    <v-card-title v-if="!MobileSize && !IpadSize" style="font-size: small; font-weight: 500; color: red; margin-top: -40px;">(อัปโหลดได้สูงสุด 4 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
                  </div>
                  <v-btn v-if="data_banner_2.length === 4 ? false : true" color="primary" style="background-color: #38b2a4; position: relative; overflow: hidden; cursor: pointer;" rounded class="mt-2 mb-2">
                    <!-- <v-icon color="white" dark>mdi-plus</v-icon> -->
                    <span style="font-weight: 600; cursor: pointer;">เพิ่มรูปภาพ</span>
                    <input
                      ref="fileInput"
                      type="file"
                      :key="fileInputKey"
                      multiple
                      accept="image/jpeg, image/jpg, image/png"
                      @change="onFileChangeLayoutWeb2($event)"
                      style="opacity: 0; position: absolute; inset: 0; cursor: pointer; z-index: 1;"
                    />
                  </v-btn>
                </v-col>
              </v-row>
              <v-col v-if="MobileSize || IpadSize" :cols="12" class="d-flex justify-center">
                <v-card-title style="font-size: 12px; font-weight: 500; color: red; margin: -20px -16px -36px -16px;">(อัปโหลดได้สูงสุด 4 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
              </v-col>
              <v-card class="mt-3 pb-5 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                <v-card-text>
                  <div v-if="data_banner_2.length === 0" style="width: 100%; display: flex; justify-content: center;" class="my-8"><span style="">กรุณาเพิ่มรูปภาพ</span></div>
                  <v-row>
                    <draggable v-model="data_banner_2" style="display: grid; max-width: 100vw; margin: auto; justify-items: center;" :style="IpadProSize ? 'grid-template-columns: repeat(4, 140px); column-gap: 14px; row-gap: 20px;' : IpadSize ? 'grid-template-columns: repeat(2, 180px); column-gap: 20px; row-gap: 20px;' : MobileSize ? 'grid-template-columns: repeat(2, 140px); column-gap: 20px; row-gap: 20px;' : 'grid-template-columns: repeat(4, 160px); column-gap: 14px; row-gap: 20px;'" @end="reorderItemsDataBanner2">
                      <div v-for="(item, index) in data_banner_2" :key="item.id" class="d-flex flex-column align-center mt-5">
                        <div class="d-flex">
                          <v-img :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path" contain :width="IpadProSize || MobileSize ? '120' : '150'" :height="IpadProSize || MobileSize ? '80' : '100'" class="mb-2" style="border-radius: 8px;"></v-img>
                          <v-btn @click="removeImageLayoutWeb2(index)" icon x-small style="float: right; background-color: #ff5252; margin-top: -10px; margin-left: -12px; z-index: 2;">
                          <v-icon x-small color="white" dark>mdi-delete</v-icon>
                        </v-btn>
                        </div>
                        <v-text-field v-model="item.href" @input="onInputLink(item.id, $event, '2')" label="เพิ่มลิงก์ (ไม่จำเป็น)" class="mt-2" hide-details dense outlined></v-text-field>
                      </div>
                  </draggable>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" v-if="selectTypeLayout !== null && selectTypeLayout !== 'single'">
              <v-row class="mb-1 ml-1">
                <v-col :cols="12" style="margin: -20px -12px;" class="d-flex align-center">
                  <div class="mr-auto">
                    <v-card-title v-if="selectTypeLayout === '2duoright'" style="font-size: medium; font-weight: 600;">Banner ย่อย (ด้านล่าง)</v-card-title>
                  <v-card-title v-if="selectTypeLayout === 'singleduo'" style="font-size: medium; font-weight: 600;">Banner ย่อย (ด้านขวา)</v-card-title>
                    <v-card-title v-if="!MobileSize && !IpadSize" style="font-size: small; font-weight: 500; color: red; margin-top: -40px;">(อัปโหลดได้สูงสุด 4 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
                  </div>
                  <v-btn v-if="data_banner_3.length === 4 ? false : true" color="primary" style="background-color: #38b2a4; position: relative; overflow: hidden; cursor: pointer;" rounded class="mt-2 mb-2">
                    <!-- <v-icon color="white" dark>mdi-plus</v-icon> -->
                    <span style="font-weight: 600; cursor: pointer;">เพิ่มรูปภาพ</span>
                    <input
                      ref="fileInput"
                      type="file"
                      :key="fileInputKey"
                      multiple
                      accept="image/jpeg, image/jpg, image/png"
                      @change="onFileChangeLayoutWeb3($event)"
                      style="opacity: 0; position: absolute; inset: 0; cursor: pointer; z-index: 1;"
                    />
                  </v-btn>
                </v-col>
              </v-row>
              <v-col v-if="MobileSize || IpadSize" :cols="12" class="d-flex justify-center">
                <v-card-title style="font-size: 12px; font-weight: 500; color: red; margin: -20px -16px -36px -16px;">(อัปโหลดได้สูงสุด 4 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
              </v-col>
              <v-card class="mt-3 pb-5 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                <v-card-text>
                  <div v-if="data_banner_3.length === 0" style="width: 100%; display: flex; justify-content: center;" class="my-8"><span style="">กรุณาเพิ่มรูปภาพ</span></div>
                  <v-row>
                    <draggable v-model="data_banner_3" style="display: grid; max-width: 100vw; margin: auto; justify-items: center;" :style="IpadProSize ? 'grid-template-columns: repeat(4, 140px); column-gap: 14px; row-gap: 20px;' : IpadSize ? 'grid-template-columns: repeat(2, 180px); column-gap: 20px; row-gap: 20px;' : MobileSize ? 'grid-template-columns: repeat(2, 140px); column-gap: 20px; row-gap: 20px;' : 'grid-template-columns: repeat(4, 160px); column-gap: 14px; row-gap: 20px;'" @end="reorderItemsDataBanner3">
                      <div v-for="(item, index) in data_banner_3" :key="item.id" class="d-flex flex-column align-center mt-5">
                        <div class="d-flex">
                          <v-img :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path" contain :width="IpadProSize || MobileSize ? '120' : '150'" :height="IpadProSize || MobileSize ? '80' : '100'" class="mb-2" style="border-radius: 8px;"></v-img>
                          <v-btn @click="removeImageLayoutWeb3(index)" icon x-small style="float: right; background-color: #ff5252; margin-top: -10px; margin-left: -12px; z-index: 2;">
                          <v-icon x-small color="white" dark>mdi-delete</v-icon>
                        </v-btn>
                        </div>
                        <v-text-field v-model="item.href" @input="onInputLink(item.id, $event, '3')" label="เพิ่มลิงก์ (ไม่จำเป็น)" class="mt-2" hide-details dense outlined></v-text-field>
                      </div>
                  </draggable>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col :cols="12" style="margin: -20px 0;" class="d-flex align-center">
              <v-card-title style="font-size: medium; font-weight: 600;">ตัวอย่างการปรับแต่ง</v-card-title>
            </v-col>
            <!-- Preview single -->
            <div :style="MobileSize || IpadSize ? 'width: 90%;' :  IpadProSize ? 'width: 94%;' : 'width: 98%; max-width: 1400px;'" class="mx-auto" dense v-if="selectTypeLayout === 'single' || selectTypeLayout === 'singleduo'">
              <div style="width: 100%;">
                <v-carousel
                  :cycle="setCycle"
                  :interval="interval"
                  :height="MobileSize || IpadSize ? '170' :  IpadProSize ? '275' : '350'"
                  hide-delimiter-background
                  hide-delimiters
                  :show-arrows="data_banner_1.length > 1 ? true : false"
                  class="pa-0"
                  style="border-radius: 16px;"
                >
                  <v-carousel-item
                    v-for="(item, i) in data_banner_1"
                    :key="i"
                  >
                    <img
                      :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                      :height="MobileSize || IpadSize ? '170' :  IpadProSize ? '275' : '350'"
                      width="100%"
                      style="border-radius: 24px; cursor: pointer;"
                      @click="item.href !== '' && gotoLink(item.href)"
                      loading="lazy"
                      decoding="async"
                    />
                  </v-carousel-item>
                </v-carousel>
              </div>
            </div>
            <!-- Preview singleduo -->
            <div :style="MobileSize || IpadSize ? 'width: 90%;' :  IpadProSize ? 'width: 94%;' : 'width: 98%; max-width: 1400px;'" style="display: flex; justify-content: space-between;" class="mx-auto mt-2" dense v-if="selectTypeLayout === 'singleduo'">
                <div style="width: 49%;">
                  <v-carousel
                    :cycle="setCycle"
                    :interval="interval"
                    :height="MobileSize || IpadSize ? '85' :  IpadProSize ? '140' : '170'"
                    hide-delimiter-background
                    hide-delimiters
                    :show-arrows="data_banner_2.length > 1 ? true : false"
                    class="pa-0"
                    style="border-radius: 16px;"
                  >
                    <v-carousel-item
                      v-for="(item, i) in data_banner_2"
                      :key="i"
                    >
                      <v-img
                        :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                        :height="MobileSize || IpadSize ? '85' :  IpadProSize ? '140' : '170'"
                        style="border-radius: 16px; cursor: pointer;"
                        @click="item.href !== '' && gotoLink(item.href)"
                        alt="data_banner_2"
                        loading="lazy"
                      />
                    </v-carousel-item>
                  </v-carousel>
                </div>
                <div style="width: 49%;">
                  <v-carousel
                    :cycle="setCycle"
                    :interval="interval"
                    :height="MobileSize || IpadSize ? '85' :  IpadProSize ? '140' : '170'"
                    hide-delimiter-background
                    hide-delimiters
                    :show-arrows="data_banner_3.length > 1 ? true : false"
                    class="pa-0"
                    style="border-radius: 16px;"
                  >
                    <v-carousel-item
                      v-for="(item, i) in data_banner_3"
                      :key="i"
                    >
                      <v-img
                        :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                        :height="MobileSize || IpadSize ? '85' :  IpadProSize ? '140' : '170'"
                        style="border-radius: 16px; cursor: pointer;"
                        @click="item.href !== '' && gotoLink(item.href)"
                        alt="data_banner_3"
                        loading="lazy"
                      />
                    </v-carousel-item>
                  </v-carousel>
                </div>
            </div>
            <!-- Preview 2duoright -->
            <div :style="MobileSize || IpadSize ? 'width: 90%;' :  IpadProSize ? 'width: 94%;' : 'width: 98%; max-width: 1400px;'" class="mx-auto" dense v-if="selectTypeLayout === '2duoright'">
              <div class="d-flex" style="width: 100%;" :style="MobileSize || IpadSize ? 'gap: 6px;' :  IpadProSize ? 'gap: 10px;' : 'gap: 12px;'">
                <div :style="selectTypeLayout === '2duoright' ? `width: ${width1}%;` : ''">
                  <v-carousel
                    :cycle="setCycle"
                    :interval="interval"
                    :height="MobileSize || IpadSize ? '170' :  IpadProSize ? '275' : '350'"
                    hide-delimiter-background
                    hide-delimiters
                    :show-arrows="data_banner_1.length > 1 ? true : false"
                    class="pa-0"
                    style="border-radius: 16px;"
                  >
                    <v-carousel-item
                      v-for="(item, i) in data_banner_1"
                      :key="i"
                    >
                      <img
                        :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                        :height="MobileSize || IpadSize ? '170' :  IpadProSize ? '275' : '350'"
                        width="100%"
                        style="border-radius: 24px; cursor: pointer;"
                        @click="item.href !== '' && gotoLink(item.href)"
                        loading="lazy"
                        decoding="async"
                      />
                    </v-carousel-item>
                  </v-carousel>
                </div>
                <div :style="selectTypeLayout === '2duoright' ? `width: ${width2}%;` : ''" style="display: flex; flex-direction: column; justify-content: space-between;">
                  <v-carousel
                    :cycle="setCycle"
                    :interval="interval"
                    :height="MobileSize || IpadSize ? '83' :  IpadProSize ? '133' : '168'"
                    hide-delimiter-background
                    hide-delimiters
                    :show-arrows="data_banner_2.length > 1 ? true : false"
                    class="pa-0"
                    style="border-radius: 16px;"
                  >
                    <v-carousel-item
                      v-for="(item, i) in data_banner_2"
                      :key="i"
                    >
                      <v-img
                        :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                        :height="MobileSize || IpadSize ? '83' :  IpadProSize ? '133' : '168'"
                        width="100%"
                        style="border-radius: 16px; cursor: pointer;"
                        @click="item.href !== '' && gotoLink(item.href)"
                        alt="data_banner_2"
                        loading="lazy"
                      />
                    </v-carousel-item>
                  </v-carousel>
                  <v-carousel
                    :cycle="setCycle"
                    :interval="interval"
                    :height="MobileSize || IpadSize ? '83' :  IpadProSize ? '133' : '168'"
                    hide-delimiter-background
                    hide-delimiters
                    :show-arrows="data_banner_3.length > 1 ? true : false"
                    class="pa-0"
                    style="border-radius: 16px;"
                  >
                    <v-carousel-item
                      v-for="(item, i) in data_banner_3"
                      :key="i"
                    >
                      <v-img
                        :src="MobileSize ? item.path_mobile : IpadSize ? item.path_ipad : item.path"
                        :height="MobileSize || IpadSize ? '83' :  IpadProSize ? '133' : '168'"
                        width="100%"
                        style="border-radius: 16px; cursor: pointer;"
                        @click="item.href !== '' && gotoLink(item.href)"
                        alt="data_banner_3"
                        loading="lazy"
                      />
                    </v-carousel-item>
                  </v-carousel>
                </div>
              </div>
            </div>
            <v-col cols="12" class="d-flex justify-end">
              <v-btn style="font-weight: 600;" rounded class="mt-1 mb-2 mr-2" color="primary" @click="dialogConfirmWeb = true">บันทึก</v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row v-if="tab === 1">
      <v-col class="" cols="12">
        <v-row>
          <v-col cols="12" class="d-flex align-center my-2">
            <v-card-title class="mr-auto" style="font-size: large; font-weight: 600;">Custom Icon</v-card-title>
            <v-btn v-if="itemsMobile.length < 8" @click="addItemMobile" rounded class="mr-2" style="font-weight: 600;" color="primary">เพิ่มไอคอน</v-btn>
          </v-col>
          <div v-if="itemsMobile.length === 0" style="width: 100%; display: flex; justify-content: center; opacity: 0.9;" class="my-5"><span style="">กรุณาเพิ่มไอคอน</span></div>
          <v-col cols="12" class="d-flex justify-center">
            <draggable v-model="itemsMobile" style="display: grid; max-width: 100vw; margin: auto; justify-items: center;" :style="IpadProSize ? `grid-template-columns: repeat(${numIcon}, 70px); column-gap: 10px; row-gap: 20px;` : IpadSize ? `grid-template-columns: repeat(${numIcon}, 50px); column-gap: 30px; row-gap: 20px;` : MobileSize ? `grid-template-columns: repeat(${numIcon}, 40px); column-gap: 30px; row-gap: 20px;` : `grid-template-columns: repeat(${numIcon}, 100px); column-gap: 10px; row-gap: 30px;`" @end="reorderItemsMobile">
              <div v-for="(item) in itemsMobile" :key="item.index_in_type">
                <!-- PC -->
                <div v-if="!MobileSize && !IpadSize && !IpadProSize" style="height: 120px; width: 100px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 80px; height: 80px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`">
                    <v-btn @click="openDialogEditIconMobile(item)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="item.path" :lazy-src="item.path_lazy" style="max-height: 48px; max-width: 48px; z-index: 5;" width="48" height="48"></v-img>
                    <div v-else style="min-height: 48px; max-height: 48px; width: 48px;"></div>
                    <v-btn @click="removeItemMobile(item.index_in_type)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 13px; text-align: center;">{{ item.name }}</span>
                </div>
                <!-- ipad Pro -->
                <div v-if="IpadProSize" style="height: 100px; width: 70px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 60px; height: 60px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`">
                    <v-btn @click="openDialogEditIconMobile(item)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="item.path" :lazy-src="item.path_lazy" style="max-height: 40px; max-width: 40px; z-index: 5;" width="40" height="40"></v-img>
                    <div v-else style="min-height: 40px; max-height: 40px; width: 40px;"></div>
                    <v-btn @click="removeItemMobile(item.index_in_type)" x-small elevation="2" icon style="margin-right: -50px; z-index: 10; background-color: #FFFFFF;"><v-icon small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 10px; text-align: center;">{{ item.name }}</span>
                </div>
                <!-- ipad -->
                <div v-if="IpadSize || MobileSize" style="height: 80px; width: 54px; display: flex; flex-direction: column; align-items: center; justify-content: space-between;">
                  <v-card elevation="0" class="d-flex flex-column" style="width: 42px; height: 42px; text-align: center; border-radius: 35%; align-items: center; justify-content: center;" :style="item.href ? `background-color: ${item.bg_color};` : `background-color: ${item.bg_color}; cursor: default;`">
                    <v-btn @click="openDialogEditIconMobile(item)" width="14" height="14" elevation="2" icon style="margin-right: -30px; z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="primary">mdi-pencil</v-icon></v-btn>
                    <v-img v-if="item.path !== null && item.path !== ''" :src="item.path" :lazy-src="item.path_lazy" style="max-height: 28px; max-width: 28px; z-index: 5;" width="28" height="28"></v-img>
                    <div v-else style="min-height: 28px; max-height: 28px; width: 28px;"></div>
                    <v-btn @click="removeItemMobile(item.index_in_type)" width="14" height="14" elevation="2" icon style="margin-right: -30px; z-index: 10; background-color: #FFFFFF;"><v-icon x-small color="error">mdi-delete</v-icon></v-btn>
                  </v-card>
                  <span style="font-size: 8px; text-align: center;">{{ item.name }}</span>
                </div>
              </div>
            </draggable>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" class="my-3">
            <v-row class="mb-1 ml-1">
              <v-col :cols="12" style="margin: -20px -12px;" class="d-flex align-center">
                <div class="mr-auto">
                  <v-card-title style="font-size: large; font-weight: 600;">Banner ใหญ่</v-card-title>
                  <!-- <v-card-title style="font-size: large; font-weight: 600;">Banner ( Website )</v-card-title> -->
                  <!-- <v-card-title style="font-size: medium; font-weight: 600;">Banner ใหญ่</v-card-title> -->
                  <v-card-title v-if="!MobileSize && !IpadSize" style="font-size: small; font-weight: 500; color: red; margin-top: -40px;">(อัปโหลดได้สูงสุด 10 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
                </div>
                <v-btn v-if="data_banner_Mobile.length === 10 ? false : true" color="primary" style="background-color: #38b2a4; position: relative; overflow: hidden; cursor: pointer;" rounded class="mt-2 mb-2">
                  <!-- <v-icon color="white" dark>mdi-plus</v-icon> -->
                  <span style="cursor: pointer; font-weight: 600;">เพิ่มรูปภาพ</span>
                  <input
                    ref="fileInput"
                    type="file"
                    :key="fileInputKey"
                    multiple
                    accept="image/jpeg, image/jpg, image/png"
                    @change="onFileChangeLayoutMobile($event)"
                    style="opacity: 0; position: absolute; inset: 0; cursor: pointer; z-index: 1;"
                  />
                </v-btn>
              </v-col>
            </v-row>
            <v-col v-if="MobileSize || IpadSize" :cols="12" class="d-flex justify-center">
              <v-card-title style="font-size: 12px; font-weight: 500; color: red; margin: -20px -16px -36px -16px;">(อัปโหลดได้สูงสุด 10 รูปภาพ และไฟล์นามสกุล .jpg, .jpeg, .png เท่านั้น)</v-card-title>
            </v-col>
            <v-card class="mt-3 pb-5 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
              <v-card-text>
                <div v-if="data_banner_Mobile.length === 0" style="width: 100%; display: flex; justify-content: center;" class="my-8"><span style="">กรุณาเพิ่มรูปภาพ</span></div>
                <v-row>
                  <draggable v-model="data_banner_Mobile" style="display: grid; max-width: 90vw; margin: auto; justify-items: center;" :style="IpadProSize ? 'grid-template-columns: repeat(3, 170px); column-gap: 14px; row-gap: 20px;' : IpadSize ? 'grid-template-columns: repeat(2, 160px); column-gap: 14px; row-gap: 20px;' : MobileSize ? 'grid-template-columns: repeat(2, 150px); column-gap: 14px; row-gap: 20px;' : 'grid-template-columns: repeat(4, 180px); column-gap: 14px; row-gap: 20px;'" @end="reorderItemsDataMobile">
                    <div v-for="(item, index) in data_banner_Mobile" :key="item.id" class="d-flex flex-column align-center mt-5" style="width: 100%;">
                      <div class="d-flex">
                        <v-img :src="item.path" contain :width="IpadProSize || MobileSize ? '120' : '150'" :height="IpadProSize || MobileSize ? '80' : '100'" class="mb-2" style="border-radius: 8px;"></v-img>
                        <v-btn @click="removeImageLayoutMobile(index)" icon x-small style="float: right; background-color: #ff5252; margin-top: -10px; margin-left: -12px; z-index: 2;">
                        <v-icon x-small color="white" dark>mdi-delete</v-icon>
                      </v-btn>
                      </div>
                      <v-select v-model="item.action_type" :items="itemSelectTypeMobile" item-text="name" item-value="value" label="ประเภทของ Banner" @change="onInputTypeMobile(item.id, $event)" class="my-2" hide-details dense outlined></v-select>
                      <v-autocomplete v-if="item.action_type === 'group'" v-model="item.action_id" :items="itemGroupIDMobile" item-text="name" item-value="id" :label="labelGroupIDMobile" @change="onInputIDMobile(item.id, $event)" return-object class="my-2" hide-details dense outlined></v-autocomplete>
                      <v-autocomplete v-if="item.action_type === 'shop'" v-model="item.action_id" :items="itemShopIDMobile" item-text="name" item-value="id" :label="labelShopIDMobile" @change="onInputIDMobile(item.id, $event)" return-object class="my-2" hide-details dense outlined></v-autocomplete>
                      <v-text-field v-if="item.action_type === 'link'" v-model="item.href" @change="onInputLinkMobile(item.id, $event)" label="เพิ่มลิงก์" class="mt-2" hide-details dense outlined></v-text-field>
                      <v-select v-if="item.action_type === 'productFilter'" v-model="item.action_filter" :items="itemSelectProductFilter" item-text="name" item-value="value" label="ประเภทของสินค้า" @change="onInputFilterMobile(item.id, $event)" class="my-2" hide-details dense outlined></v-select>
                    </div>
                </draggable>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" class="d-flex justify-end">
            <v-btn style="font-weight: 600;" rounded class="mt-1 mb-2 mr-2" color="primary" @click="dialogConfirmMobile = true">บันทึก</v-btn>
          </v-col>
        </v-row>
      </v-col>
      <!-- ตัวเก่า Banner ย่อย -->
      <!-- <v-col class="d-flex align-center" cols="12" style="margin: -10px 0;">
        <v-card-title style="font-size: large; font-weight: 600;" class="mr-2">Banner ( Mobile )</v-card-title>
        <v-switch style="margin: -10px 0;" v-model="switchActive" :label="switchActive ? 'เปิด' : 'ปิด'" @change="handleActive" hide-details></v-switch>
        <v-spacer></v-spacer>
        <v-btn style="font-weight: 600;" color="error" small @click="dialogConfirmDelete = true" class="mr-2"><v-icon small>mdi-delete</v-icon> ลบข้อมูล</v-btn>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? '12' : '6'" style="margin: -10px 0;">
        <v-select v-model="selectLayoutBanner" :style="IpadSize || MobileSize ? 'padding: 0 12px;' : 'padding: 0 0 0 12px;'" :items="itemSelectLayoutBanner" item-text="name" item-value="value" label="จำนวน Layout" @change="handleSelectLayoutBanner" hide-details dense outlined></v-select>
      </v-col>
      <v-col cols="12">
        <v-row class="mb-1 mx-1">
          <v-col :cols="12" style="margin: -20px -12px;">
            <v-card-title style="font-size: medium; font-weight: 600;">Layout 1</v-card-title>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
            <v-select v-model="selectTypeBanner1" :items="itemSelectTypeBanner" item-text="name" item-value="value" label="ประเภท Banner" @change="handleSelectTypeBanner('1')" hide-details dense outlined></v-select>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="selectTypeBanner1 !== 'image' && this.selectTypeBanner1 !== 'link' && this.selectTypeBanner1 !== ''">
            <v-autocomplete v-model="selectIDBanner1" :items="itemSelectIDBanner1" item-text="name" item-value="id" :label="labelSelectID1" @change="handleSelectIDBanner('1')" hide-details dense outlined style="width: 100%;"></v-autocomplete>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="this.selectTypeBanner1 === 'link' && this.selectTypeBanner1 !== ''">
            <v-text-field v-model="LinkBanner1" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
          </v-col>
        </v-row>
        <v-card class="mt-3 py-1 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image_mobile_1 !== ''" :src="image_mobile_1" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image_mobile_1 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayout1($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image_mobile_1 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayout1()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="selectLayoutBanner !== '1'">
        <v-row class="mb-1 mx-1">
          <v-col :cols="12" style="margin: -20px -12px;">
            <v-card-title style="font-size: medium; font-weight: 600;">Layout 2</v-card-title>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
            <v-select v-model="selectTypeBanner2" :items="itemSelectTypeBanner" item-text="name" item-value="value" label="ประเภท Banner" @change="handleSelectTypeBanner('2')" hide-details dense outlined></v-select>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="selectTypeBanner2 !== 'image' && this.selectTypeBanner2 !== 'link' && this.selectTypeBanner2 !== ''">
            <v-autocomplete v-model="selectIDBanner2" :items="itemSelectIDBanner2" item-text="name" item-value="id" :label="labelSelectID2" @change="handleSelectIDBanner('2')" hide-details dense outlined style="width: 100%;"></v-autocomplete>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="this.selectTypeBanner2 === 'link' && this.selectTypeBanner2 !== ''">
            <v-text-field v-model="LinkBanner2" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
          </v-col>
        </v-row>
        <v-card class="mt-3 py-1 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image_mobile_2 !== ''" :src="image_mobile_2" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image_mobile_2 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayout2($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image_mobile_2 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayout2()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="selectLayoutBanner !==  '1' && selectLayoutBanner !== '2'">
        <v-row class="mb-1 mx-1">
          <v-col :cols="12" style="margin: -20px -12px;">
            <v-card-title style="font-size: medium; font-weight: 600;">Layout 3</v-card-title>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
            <v-select v-model="selectTypeBanner3" :items="itemSelectTypeBanner" item-text="name" item-value="value" label="ประเภท Banner" @change="handleSelectTypeBanner('3')" hide-details dense outlined></v-select>
          </v-col>
            <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="selectTypeBanner3 !== 'image' && this.selectTypeBanner3 !== 'link' && this.selectTypeBanner3 !== ''">
            <v-autocomplete v-model="selectIDBanner3" :items="itemSelectIDBanner3" item-text="name" item-value="id" :label="labelSelectID3" @change="handleSelectIDBanner('3')" hide-details dense outlined style="width: 100%;"></v-autocomplete>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="this.selectTypeBanner3 === 'link' && this.selectTypeBanner3 !== ''">
            <v-text-field v-model="LinkBanner3" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
          </v-col>
        </v-row>
        <v-card class="mt-3 py-1 mx-2 mb-1" elevation="0" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image_mobile_3 !== ''" :src="image_mobile_3" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image_mobile_3 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayout3($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image_mobile_3 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayout3()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="selectLayoutBanner === '4'">
        <v-row class="mb-1 mx-1">
          <v-col :cols="12" style="margin: -20px -12px;">
            <v-card-title style="font-size: medium; font-weight: 600;">Layout 4</v-card-title>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
            <v-select v-model="selectTypeBanner4" :items="itemSelectTypeBanner" item-text="name" item-value="value" label="ประเภท Banner" @change="handleSelectTypeBanner('4')" hide-details dense outlined></v-select>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="selectTypeBanner4 !== 'image' && this.selectTypeBanner4 !== 'link' && this.selectTypeBanner4 !== ''">
            <v-autocomplete v-model="selectIDBanner4" :items="itemSelectIDBanner4" item-text="name" item-value="id" :label="labelSelectID4" @change="handleSelectIDBanner('4')" hide-details dense outlined style="width: 100%;"></v-autocomplete>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? '12' : '6'" v-if="this.selectTypeBanner4 === 'link' && this.selectTypeBanner4 !== ''">
            <v-text-field v-model="LinkBanner4" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
          </v-col>
        </v-row>
        <v-card class="mt-3 py-1 mx-2 mb-1" :style="MobileSize ?  'width: 96%;' : 'width: 98%;'" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image_mobile_4 !== ''" :src="image_mobile_4" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image_mobile_4 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayout4($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image_mobile_4 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayout4()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" class="d-flex justify-end">
        <v-btn style="font-weight: 600;" class="mt-1 mb-2 mr-2" color="primary" small @click="dialogConfirm = true">บันทึก</v-btn>
      </v-col> -->
      </v-row>

      <!-- dialog edit icon -->
      <v-dialog v-model="dialogEditIcon" persistent width="420">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              แก้ไขไอคอน
            </span>
              <v-btn icon dark @click="closeDialogEditIcon()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row justify="center" dense class="pt-6">

              <v-col cols="12" align="center">
                <v-color-picker
                  dot-size="25"
                  hide-mode-switch
                  mode="hexa"
                  v-model="bgColorEdit"
                  swatches-max-height="200">
                </v-color-picker>
              </v-col>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" class="my-2">
                    <v-img  class="mx-auto" v-if="pathEdit !== ''" :src="pathEdit" :lazy-src="pathLazyEdit" width="100" height="100"></v-img>
                  </v-col>
                  <v-col cols="12" align="center" v-if="pathEdit === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeIcon($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="pathEdit !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageIcon()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" align="center">
                <v-form ref="form">
                  <v-text-field v-model="hrefEdit" label="ลิงก์" minlength="1" outlined dense class="my-3 py-2 px-2" hide-details=""></v-text-field>
                  <v-text-field v-model="nameEdit" label="ชื่อไอคอน (ไทย)" minlength="1" maxlength="20" counter outlined dense class="my-2 pt-2 px-2"></v-text-field>
                  <v-text-field v-model="nameEngEdit" label="ชื่อไอคอน (อังกฤษ)" minlength="1" maxlength="20" counter outlined dense class="my-2 px-2" :rules="Rules.NameEN" @keyup="nameEngEdit = nameEngEdit.replace(/[\u0E00-\u0E7F]/g, '')"></v-text-field>
                </v-form>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2" @click="closeDialogEditIcon()">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="editIcon()">ยืนยัน</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- dialog edit icon mobile -->
      <v-dialog v-model="dialogEditIconMobile" persistent width="420">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              แก้ไขไอคอน
            </span>
              <v-btn icon dark @click="closeDialogEditIconMobile()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row justify="center" dense class="pt-6">

              <v-col cols="12" align="center">
                <v-color-picker
                  dot-size="25"
                  hide-mode-switch
                  mode="hexa"
                  v-model="bgColorMobileEdit"
                  swatches-max-height="200">
                </v-color-picker>
              </v-col>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" class="my-2">
                    <v-img  class="mx-auto" v-if="pathMobileEdit !== ''" :src="pathMobileEdit" :lazy-src="pathLazyMobileEdit" width="100" height="100"></v-img>
                  </v-col>
                  <v-col cols="12" align="center" v-if="pathMobileEdit === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeIconMobile($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="pathMobileEdit !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageIconMobile()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" align="center">
                <v-form ref="form">
                  <v-select v-model="typeMobileEdit" :items="itemSelectTypeMobile" item-text="name" item-value="value" label="ประเภทไอคอน" @change="handleSelectTypeMobile" class="my-2 py-2 px-2" hide-details dense outlined></v-select>
                  <v-autocomplete v-if="typeMobileEdit === 'shop' || typeMobileEdit === 'group'" v-model="idMobileEdit" :items="itemSelectIDMobile" item-text="name" item-value="id" :label="labelSelectIDMobile" @change="handleSelectIDMobile" return-object class="my-2 py-2 px-2" hide-details dense outlined style="width: 100%;"></v-autocomplete>
                  <v-text-field v-if="typeMobileEdit === 'link'" v-model="hrefMobileEdit" label="ลิงก์" minlength="1" outlined dense class="my-3 py-2 px-2" hide-details=""></v-text-field>
                  <v-select v-if="typeMobileEdit === 'productFilter'" v-model="filterMobileEdit" :items="itemSelectProductFilter" item-text="name" item-value="value" label="ประเภทของสินค้า" @change="handleSelectFilterMobile" class="my-2 py-2 px-2" hide-details dense outlined></v-select>
                  <v-text-field v-model="nameMobileEdit" label="ชื่อไอคอน (ไทย)" minlength="1" maxlength="20" counter outlined dense class="my-2 pt-2 px-2"></v-text-field>
                  <v-text-field v-model="nameEngMobileEdit" label="ชื่อไอคอน (อังกฤษ)" minlength="1" maxlength="20" counter outlined dense class="my-2 px-2" :rules="Rules.NameEN" @keyup="nameEngMobileEdit = nameEngMobileEdit.replace(/[\u0E00-\u0E7F]/g, '')"></v-text-field>
                </v-form>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2" @click="closeDialogEditIconMobile()">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="editIconMobile()" :disabled="(typeMobileEdit === '' || typeMobileEdit === null || typeMobileEdit === undefined) || (typeMobileEdit === 'group' && (idMobileEdit === '' || idMobileEdit === null || idMobileEdit === undefined)) || (typeMobileEdit === 'shop' && (idMobileEdit === '' || idMobileEdit === null || idMobileEdit === undefined)) || (typeMobileEdit === 'link' && (hrefMobileEdit === '' || hrefMobileEdit === null || hrefMobileEdit === undefined)) || ((typeMobileEdit === 'image' || typeMobileEdit === 'healthKitchen' || typeMobileEdit === 'healthDriver' || typeMobileEdit === 'couponAll') && (pathMobileEdit === '' || pathMobileEdit === null || pathMobileEdit === undefined)) || (typeMobileEdit === 'productFilter' && (filterMobileEdit === null || filterMobileEdit === undefined))">ยืนยัน</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Dialog Delete -->
      <v-dialog v-model="dialogConfirmDelete" persistent width="464">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              ยืนยันการทำรายการ
            </span>
            <v-btn icon dark @click="dialogConfirmDelete = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการลบข้อมูล</span><br>
                <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
              </v-col>

            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogConfirmDelete = false" class="mr-2">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click=" updateBannerMobile('delete')">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Dialog Confirm -->
      <v-dialog v-model="dialogConfirm" persistent width="464">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              ยืนยันการทำรายการ
            </span>
            <v-btn icon dark @click="closeDialogConfirm()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการบันทึกข้อมูล</span><br>
                <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
              </v-col>

            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirm()" class="mr-2">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="updateBannerMobile('edit')">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Dialog Confirm Web -->
      <v-dialog v-model="dialogConfirmWeb" persistent width="464">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              ยืนยันการทำรายการ
            </span>
            <v-btn icon dark @click="closeDialogConfirmWeb()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการบันทึกข้อมูล</span><br>
                <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirmWeb()" class="mr-2">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="updateBanner()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Dialog Confirm Mobile -->
      <v-dialog v-model="dialogConfirmMobile" persistent width="464">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              ยืนยันการทำรายการ
            </span>
            <v-btn icon dark @click="closeDialogConfirmMobile()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการบันทึกข้อมูล</span><br>
                <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirmMobile()" class="mr-2">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="updateBannerMobile()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Dialog Custom Coin -->
      <v-dialog v-model="dialogCustomCoin" persistent width="464">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              {{ messageCustomCoin }}
            </span>
            <v-btn icon dark @click="closeDialogCustomCoin()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row v-if="statusDialog === 'bg'" justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <v-select v-model="editData.bgType" :items="itemSelectTypeBG" item-text="name" item-value="value" label="ประเภทพื้นหลัง"  @change="handleSelectBGType" class="my-2 py-2 px-2" hide-details dense outlined></v-select>
                <v-color-picker  v-if="editData.bgType === 'Color' || editData.bgType === 'Gradient'" v-model="editData.bgColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
                <v-color-picker  v-if="editData.bgType === 'Gradient'" v-model="editData.bgColor2" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
              </v-col>
              <v-col v-if="editData.bgType === 'Image' && editData.bgImage !== ''" cols="12" class="my-2">
                <v-img  class="mx-auto" :src="editData.bgImage" width="60%" height="auto"></v-img>
              </v-col>
              <v-col v-if="editData.bgType === 'Image' && editData.bgImage === ''" cols="12" align="center" class="mt-2">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
              </v-col>
              <v-col v-if="editData.bgType === 'Image'" cols="12" style="text-align: center;" class="mb-2 mt-2">
                <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                  เพิ่มรูปภาพ
                  <input
                    ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeBGImage($event)"
                    style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                  />
                </v-btn>
                <v-btn v-if="editData.bgImage !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageBGImage()"><v-icon small>mdi-delete</v-icon></v-btn>
              </v-col>
            </v-row>
            <v-row v-if="statusDialog === 'title'" justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <v-color-picker v-model="editData.titleColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
                <v-text-field v-model="editData.titleText" label="ข้อความ (ไทย)" minlength="1" maxlength="30" counter outlined dense class="px-4 my-2"></v-text-field>
                <v-text-field v-model="editData.titleTextEng" label="ข้อความ (อังกฤษ)" minlength="1" maxlength="30" counter @keyup="editData.titleTextEng = editData.titleTextEng.replace(/[\u0E00-\u0E7F]/g, '')" outlined dense class="px-4 my-2"></v-text-field>
              </v-col>
            </v-row>
            <v-row v-if="statusDialog === 'titleCoin'" justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <v-color-picker v-model="editData.titleCoinColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
              </v-col>
            </v-row>
            <v-row  v-if="statusDialog === 'btn'" justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <v-card-title style="font-size: medium; color: #333333;" class="py-1 pl-4">สีตัวอักษร</v-card-title>
                <v-color-picker v-model="editData.btnTextColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
                <v-text-field v-model="editData.btnText" label="ข้อความ (ไทย)" minlength="1" maxlength="30" counter outlined dense class="px-4 my-2"></v-text-field>
                <v-text-field v-model="editData.btnTextEng" label="ข้อความ (อังกฤษ)" minlength="1" maxlength="30" counter @keyup="editData.btnTextEng = editData.btnTextEng.replace(/[\u0E00-\u0E7F]/g, '')" outlined dense class="px-4 my-2"></v-text-field>
                <v-card-title style="font-size: medium; color: #333333;" class="py-1 pl-4">สีพื้นหลังปุ่ม</v-card-title>
                <v-color-picker v-model="editData.btnColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
              </v-col>
            </v-row>
            <v-row v-if="statusDialog === 'coin'" justify="center" dense class="pt-6">
              <v-col cols="12" align="center" class="pb-0">
                <v-card-title style="font-size: medium; color: #333333;" class="py-1 pl-4">สีตัวอักษร (coin)</v-card-title>
                <v-color-picker v-model="editData.coinColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
                <v-card-title style="font-size: medium; color: #333333;" class="py-1 pl-4">สีตัวอักษร (วัน)</v-card-title>
                <v-color-picker v-model="editData.dayColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
                <v-card-title style="font-size: medium; color: #333333;" class="py-1 pl-4">สีเส้นเชื่อม</v-card-title>
                <v-color-picker v-model="editData.lineColor" dot-size="25" hide-mode-switch mode="hexa" swatches-max-height="100"></v-color-picker>
                <v-select v-model="editData.coinType" :items="itemSelectTypeCoin" item-text="name" item-value="value" label="ประเภท Coin" @change="handleSelectCoinType" class="mb-4 py-2 px-4" hide-details dense outlined></v-select>
                <v-card-title v-if="editData.coinType === 'Custom'" style="font-size: medium; color: #333333;" class="py-1 pl-4">จำนวน Coin</v-card-title>
                <v-row>
                  <v-col :cols="editData.coinType === 'Custom' ? '6' : '12'">
                    <v-text-field v-if="editData.coinType === 'Default' || editData.coinType === 'Custom'" v-model="editData.coinDay1" type="number" hide-details :label="editData.coinType === 'Custom' ? 'วันที่ 1' : 'จำนวน coin'" outlined dense @wheel.native.prevent="(e) => e.target.blur()" :class="editData.coinType === 'Custom' ? 'pl-4 mb-2' : 'px-4 mb-2'"
                      @keyup="editData.coinDay1 = editData.coinDay1
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6" v-if="editData.coinType === 'Custom'">
                    <v-text-field v-if="editData.coinType === 'Custom'" v-model="editData.coinDay2" type="number" hide-details label="วันที่ 2" @wheel.native.prevent="(e) => e.target.blur()" outlined dense class="pr-4 mb-2"
                      @keyup="editData.coinDay2 = editData.coinDay2
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6" v-if="editData.coinType === 'Custom'">
                    <v-text-field v-if="editData.coinType === 'Custom'" v-model="editData.coinDay3" type="number" hide-details label="วันที่ 3" @wheel.native.prevent="(e) => e.target.blur()" outlined dense class="pl-4 mb-2"
                      @keyup="editData.coinDay3 = editData.coinDay3
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6" v-if="editData.coinType === 'Custom'">
                    <v-text-field v-if="editData.coinType === 'Custom'" v-model="editData.coinDay4" type="number" hide-details label="วันที่ 4" @wheel.native.prevent="(e) => e.target.blur()" outlined dense class="pr-4 mb-2"
                      @keyup="editData.coinDay4 = editData.coinDay4
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6" v-if="editData.coinType === 'Custom'">
                    <v-text-field v-if="editData.coinType === 'Custom'" v-model="editData.coinDay5" type="number" hide-details label="วันที่ 5" @wheel.native.prevent="(e) => e.target.blur()" outlined dense class="pl-4 mb-2"
                      @keyup="editData.coinDay5 = editData.coinDay5
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6" v-if="editData.coinType === 'Custom'">
                    <v-text-field v-if="editData.coinType === 'Custom'" v-model="editData.coinDay6" type="number" hide-details label="วันที่ 6" @wheel.native.prevent="(e) => e.target.blur()" outlined dense class="pr-4 mb-2"
                      @keyup="editData.coinDay6 = editData.coinDay6
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6" v-if="editData.coinType === 'Custom'">
                    <v-text-field v-if="editData.coinType === 'Custom'" v-model="editData.coinDay7" type="number" hide-details label="วันที่ 7" @wheel.native.prevent="(e) => e.target.blur()" outlined dense class="pl-4 mb-2"
                      @keyup="editData.coinDay7 = editData.coinDay7
                      .replace(/[^0-9.]/g, '')
                      .replace(/\.+$/, '0')"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-card-title style="font-size: medium; color: #333333;" class="py-3 mt-2 pl-4">รูปภาพ Coin ที่กดเช็คอินแล้ว</v-card-title>
                <v-row>
                  <v-col v-if="editData.checkCoinImage !== ''" cols="12" class="my-2">
                    <v-img  class="mx-auto" :src="editData.checkCoinImage" width="100" height="100"></v-img>
                  </v-col>
                  <v-col v-if="editData.checkCoinImage === ''" cols="12" align="center" class="mt-2">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;" class="mb-2 mt-2">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeCheckCoin($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="editData.checkCoinImage !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageCheckCoin()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
                <v-card-title style="font-size: medium; color: #333333;" class="py-3 pl-4">รูปภาพ Coin</v-card-title>
                <v-row>
                  <v-col v-if="editData.coinImage !== ''" cols="12" class="my-2">
                    <v-img  class="mx-auto" :src="editData.coinImage" width="100" height="100"></v-img>
                  </v-col>
                  <v-col v-if="editData.coinImage === ''" cols="12" align="center" class="mt-2">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;" class="mb-2 mt-2">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeCoin($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="editData.coinImage !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageCoin()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
                <v-card-title v-if="editData.coinType === 'Custom'" style="font-size: medium; color: #333333;" class="py-3 pl-4">รูปภาพ Coin วันสุดท้าย</v-card-title>
                <v-row v-if="editData.coinType === 'Custom'">
                  <v-col v-if="editData.lastCoinImage !== ''" cols="12" class="my-2">
                    <v-img  class="mx-auto" :src="editData.lastCoinImage" width="100" height="100"></v-img>
                  </v-col>
                  <v-col v-if="editData.lastCoinImage === ''" cols="12" align="center" class="mt-2">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;" class="mb-2 mt-2">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" :key="fileInputKey" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLastCoin($event)"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="editData.lastCoinImage !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLastCoin()"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row dense justify="center" class="pb-6 mt-2">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogCustomCoin()" class="mr-2">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="updateCustomCoin()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-container>
  </v-card>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      itemTab: [
        'Banner ( Website )', 'Banner ( Mobile )'
      ],
      tab: 0,
      items: [],
      itemsMobile: [],
      indexDefault: 0,
      nameDefault: '',
      hrefDefault: null,
      typeDefault: null,
      idDefault: null,
      filterDefault: '',
      pathDefault: '',
      bgDefault: '#f7f7f7',
      indexEdit: null,
      bgColorEdit: null,
      nameEdit: null,
      nameEngEdit: null,
      hrefEdit: null,
      pathEdit: '',
      pathLazyEdit: '',
      dialogEditIcon: false,
      dialogConfirmDelete: false,
      indexMobileEdit: null,
      bgColorMobileEdit: null,
      nameMobileEdit: null,
      nameEngMobileEdit: null,
      typeMobileEdit: null,
      idMobileEdit: null,
      hrefMobileEdit: null,
      filterMobileEdit: '',
      pathMobileEdit: '',
      pathLazyMobileEdit: '',
      dialogEditIconMobile: false,
      dataActive: '',
      switchActive: '',
      selectLayoutBanner: '1',
      selectTypeBanner1: '',
      selectTypeBanner2: '',
      selectTypeBanner3: '',
      selectTypeBanner4: '',
      selectIDBanner1: '',
      selectIDBanner2: '',
      selectIDBanner3: '',
      selectIDBanner4: '',
      labelSelectID1: '',
      labelSelectID2: '',
      labelSelectID3: '',
      labelSelectID4: '',
      LinkBanner1: '',
      LinkBanner2: '',
      LinkBanner3: '',
      LinkBanner4: '',
      image_mobile_1: '',
      image_mobile_layout_1: '',
      image_mobile_2: '',
      image_mobile_layout_2: '',
      image_mobile_3: '',
      image_mobile_layout_3: '',
      image_mobile_4: '',
      image_mobile_layout_4: '',
      itemSelectLayoutBanner:
      [
        { name: '1', value: '1' },
        { name: '2', value: '2' },
        { name: '3', value: '3' },
        { name: '4', value: '4' }
      ],
      itemSelectTypeBanner:
      [
        { name: 'ประเภทร้านค้า', value: 'group' },
        { name: 'ร้านค้า', value: 'shop' },
        { name: 'รูปภาพ (แสดงแค่รูปภาพ)', value: 'image' },
        { name: 'ลิงก์ไปยังเว็บไซต์ภายนอก', value: 'link' }
      ],
      itemSelectTypeMobile:
      [
        { name: 'ประเภทร้านค้า', value: 'group' },
        { name: 'ร้านค้า', value: 'shop' },
        { name: 'รูปภาพ (แสดงแค่รูปภาพ)', value: 'image' },
        { name: 'เว็บไซต์ภายนอก', value: 'link' },
        { name: 'สินค้าประเภทต่างๆ', value: 'productFilter' },
        { name: 'สั่งซื้ออีกครั้ง', value: 'orderRepeat' },
        { name: 'คูปอง', value: 'couponAll' },
        { name: 'Health Kitchen', value: 'healthKitchen' },
        { name: 'Health Driver', value: 'healthDriver' }
      ],
      itemSelectProductFilter:
      [
        { name: 'สินค้ามาใหม่', value: 'new' },
        { name: 'สินค้าขายดี', value: 'best-seller' },
        { name: 'สินค้าแนะนำ', value: 'recommend' },
        { name: 'สินค้าทั่วไป', value: 'general' },
        { name: 'สินค้าที่มีส่วนลด', value: 'discount' }
      ],
      itemSelectIDMobile: [],
      labelSelectIDMobile: '',
      itemGroupIDMobile: [],
      itemShopIDMobile: [],
      labelGroupIDMobile: '',
      labelShopIDMobile: '',
      itemSelectIDBanner1: [],
      itemSelectIDBanner2: [],
      itemSelectIDBanner3: [],
      itemSelectIDBanner4: [],
      image_banner_mobile: [],
      dialogConfirm: false,
      messageConfirm: '',
      statusConfirm: '',
      selectTypeLayout: null,
      itemSelectTypeLayout:
      [
        { name: 'Banner ใหญ่', value: 'single' },
        // { name: 'Banner 2 คอลัมน์', value: 'duo' },
        // { name: 'Banner 2 คอลัมน์ 2 แถว', value: '2duo' },
        { name: 'Banner ย่อย (ด้านขวา)', value: '2duoright' },
        { name: 'Banner ย่อย (ด้านล่าง)', value: 'singleduo' }
      ],
      selectWidthLayout: '',
      selectWidthLayout1: '',
      selectWidthLayout2: '',
      itemSelectWidthLayout:
      [
        // { name: '50 - 50', value: '50-50' },
        { name: '60 - 40', value: '60-40' },
        { name: '70 - 30', value: '70-30' }
        // { name: '40 - 60', value: '40-60' },
        // { name: '30 - 70', value: '30-70' }
      ],
      data_banner_1: [],
      image_layout_1: {},
      data_banner_2: [],
      image_layout_2: {},
      data_banner_3: [],
      image_layout_3: {},
      dialogConfirmWeb: false,
      dialogConfirmMobile: false,
      LinkBanner: [],
      isValid: true,
      interval: 12000,
      intervalSetZero: 999999999,
      setCycle: true,
      width1: '',
      width2: '',
      fileInputKey: Date.now(),
      itemMobile: [],
      data_banner_Mobile: [],
      imageBigBannerMobile: {},
      Rules: {
        NameEN: [
          v => (v === '' || /^[a-zA-Z0-9_.\-!#$%&():\s]+$/.test(v)) || 'กรุณากรอกเฉพาะภาษาอังกฤษ ตัวเลข หรือ - % & ( )'
        ]
      },
      // days: [
      //   { id: 1, coin: '0.10', day: 'วันที่ 1' },
      //   { id: 2, coin: '0.10', day: 'วันที่ 2' },
      //   { id: 3, coin: '0.10', day: 'วันที่ 3' },
      //   { id: 4, coin: '0.10', day: 'วันที่ 4' },
      //   { id: 5, coin: '0.10', day: 'วันที่ 5' },
      //   { id: 6, coin: '0.10', day: 'วันที่ 6' },
      //   { id: 7, coin: '0.10', day: 'วันที่ 7' }
      // ],
      days: [],
      dialogCustomCoin: false,
      messageCustomCoin: '',
      statusDialog: '',
      titleText: 'หัวเรื่อง',
      titleTextEng: 'Title',
      titleColor: '#333333',
      titleCoinColor: '#333333',
      itemSelectTypeBG: [
        { name: 'สี', value: 'Color' },
        { name: 'ไล่สี', value: 'Gradient' },
        { name: 'รูปภาพ', value: 'Image' }
      ],
      bgType: 'Color',
      bgColor: '#ffffff',
      bgColor2: '#ffffff',
      bgImage: '',
      btnText: 'ข้อความเช็คอิน',
      btnTextEng: 'Check In',
      btnTextColor: '#ffffff',
      btnColor: '#333333',
      coinColor: '#333333',
      dayColor: '#333333',
      lineColor: '#e0e0e0',
      itemSelectTypeCoin: [
        { name: 'เริ่มต้น', value: 'Default' },
        { name: 'ปรับแต่ง', value: 'Custom' }
      ],
      coinType: 'Default',
      coinDay1: '',
      coinDay2: '',
      coinDay3: '',
      coinDay4: '',
      coinDay5: '',
      coinDay6: '',
      coinDay7: '',
      checkCoinImage: '',
      coinImage: '',
      lastCoinImage: '',
      itemsCoin: [],
      editData: {},
      backupData: {}
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    numIcon () {
      // return this.items.length <= 8 ? 8 : Math.ceil(this.items.length / 2)
      return (this.MobileSize || this.IpadSize) ? 4 : 8
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/customBannerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/customBanner' }).catch(() => {})
      }
    }
  },
  created () {
    this.getDataBannerWeb()
    // this.calculateNumIcon()
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    handleMouseOver () {
      this.setCycle = false
      this.interval = this.intervalSetZero // Set interval to a very high value on mouseover
    },
    handleMouseLeave () {
      this.setCycle = true
      this.interval = 12000 // Set interval to 12000 on mouseleave
    },
    async changeTab (val) {
      this.tab = val
      if (this.tab === 0) {
        await this.getDataBannerWeb()
      } else if (this.tab === 1) {
        await this.getDataBannerMobile()
        // await this.getActiveGroupShop()
        // await this.getDetailBannerGroupShopV2()
      }
    },
    // calculateNumIcon () {
    //   var numIcon = 8
    //   if (this.items.length <= 8) {
    //     numIcon = 8
    //   } else {
    //     numIcon = Math.ceil(this.items.length / 2)
    //   }
    //   return numIcon
    // },
    reorderItems (val) {
      const before = val.oldIndex
      const after = val.newIndex
      const filterItem = this.items.filter(item => item.index_in_type === before)
      if (this.items.length < after) {
        this.items.push(filterItem)
      }
      this.items = this.items.map((item, index) => {
        return {
          ...item,
          index_in_type: index
        }
      })
    },
    addItem () {
      if (this.items.length < 8) {
        this.items.push({
          index_in_type: this.indexDefault,
          name: this.nameDefault,
          href: this.hrefDefault,
          path: this.pathDefault,
          path_lazy: this.pathDefault,
          bg_color: this.bgDefault
        })
        this.indexDefault = this.items.length
      }
    },
    removeItem (index) {
      this.items = this.items.filter(item => item.index_in_type !== index)
      this.items = this.items.map((item, index) => {
        item.index_in_type = index
        return item
      })
      this.indexDefault = this.items.length
    },
    openDialogEditIcon (item) {
      this.dialogEditIcon = true
      this.indexEdit = item.index_in_type
      this.nameEdit = item.name
      this.nameEngEdit = item.name_eng
      this.hrefEdit = item.href
      this.pathEdit = item.path
      this.pathLazyEdit = item.path_lazy
      this.bgColorEdit = item.bg_color
    },
    closeDialogEditIcon () {
      this.dialogEditIcon = false
      this.indexEdit = null
      this.nameEdit = null
      this.nameEngEdit = null
      this.hrefEdit = null
      this.pathEdit = null
      this.pathLazyEdit = null
      this.bgColorEdit = '#f7f7f7'
    },
    editIcon () {
      this.items = this.items.filter(item => item.index_in_type !== this.indexEdit)
      this.items.push({ index_in_type: this.indexEdit, path: this.pathEdit, path_lazy: this.pathLazyEdit, name: this.nameEdit, name_eng: this.nameEngEdit, href: this.hrefEdit, bg_color: this.bgColorEdit })
      this.items.sort((a, b) => a.index_in_type - b.index_in_type)
      this.closeDialogEditIcon()
    },
    async onFileChangeIcon (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.pathEdit = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]

            const icon = { image: [base64], type: 'custom_icon' }

            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', icon)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.pathEdit = response.data.list_path[0].path
              this.pathLazyEdit = response.data.list_path[0].path_lazy
              this.items = this.items.map(item =>
                item.index_in_type === this.indexEdit ? { ...item, path: this.pathEdit, path_lazy: this.pathLazyEdit } : item
              )
            }
          }
          reader.readAsDataURL(file)
          this.fileInputKey = Date.now()
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageIcon () {
      this.pathEdit = ''
      this.pathLazyEdit = ''
      this.fileInputKey = Date.now()
    },
    reorderItemsMobile (val) {
      const before = val.oldIndex
      const after = val.newIndex
      const filterItem = this.itemsMobile.filter(item => item.index_in_type === before)
      if (this.itemsMobile.length < after) {
        this.itemsMobile.push(filterItem)
      }
      this.itemsMobile = this.itemsMobile.map((item, index) => {
        return {
          ...item,
          index_in_type: index
        }
      })
    },
    addItemMobile () {
      if (this.itemsMobile.length < 8) {
        this.itemsMobile.push({
          index_in_type: this.indexDefault,
          name: this.nameDefault,
          href: this.hrefDefault,
          action_type: this.typeDefault,
          action_id: this.idDefault,
          action_filter: this.filterDefault,
          path: this.pathDefault,
          path_lazy: this.pathDefault,
          bg_color: this.bgDefault
        })
        this.indexDefault = this.itemsMobile.length
      }
    },
    removeItemMobile (index) {
      this.itemsMobile = this.itemsMobile.filter(item => item.index_in_type !== index)
      this.itemsMobile = this.itemsMobile.map((item, index) => {
        item.index_in_type = index
        return item
      })
      this.indexDefault = this.itemsMobile.length
    },
    async openDialogEditIconMobile (item) {
      // console.log('item', item)
      this.indexMobileEdit = item.index_in_type
      this.nameMobileEdit = item.name
      this.nameEngMobileEdit = item.name_eng
      this.hrefMobileEdit = item.href
      this.pathMobileEdit = item.path
      this.typeMobileEdit = item.action_type
      // console.log('item.action_type', item.action_type)
      await this.handleSelectTypeMobile()
      this.idMobileEdit = item.action_id
      if ((this.typeMobileEdit === 'group' || this.typeMobileEdit === 'shop') && this.idMobileEdit !== null) {
        // console.log('this.itemSelectIDMobile', this.itemSelectIDMobile)
        var items = this.itemSelectIDMobile.filter(item => item.id === parseInt(this.idMobileEdit))
        // console.log('items', items)
        this.handleSelectIDMobile(items[0])
      }
      if (this.typeMobileEdit === 'productFilter' && (item.action_filter !== '' || item.action_filter !== null)) {
        // console.log('item.action_filter', item.action_filter)
        // console.log('this.itemSelectProductFilter', this.itemSelectProductFilter)
        var actionFilter = item.action_filter
        var itemsfilter = this.itemSelectProductFilter.filter(item => item.value === actionFilter)
        // console.log('itemsfilter', itemsfilter)
        this.handleSelectFilterMobile(itemsfilter[0].value)
      }
      // console.log('idMobileEdit', this.idMobileEdit)
      this.pathLazyMobileEdit = item.path_lazy
      this.bgColorMobileEdit = item.bg_color
      this.dialogEditIconMobile = true
    },
    closeDialogEditIconMobile () {
      this.dialogEditIconMobile = false
      this.indexMobileEdit = null
      this.nameMobileEdit = null
      this.nameEngMobileEdit = null
      this.hrefMobileEdit = null
      this.filterMobileEdit = ''
      this.typeMobileEdit = null
      this.idMobileEdit = null
      this.pathMobileEdit = null
      this.pathLazyMobileEdit = null
      this.bgColorMobileEdit = '#f7f7f7'
    },
    editIconMobile () {
      this.itemsMobile = this.itemsMobile.filter(item => item.index_in_type !== this.indexMobileEdit)
      if (this.typeMobileEdit === 'image' || this.typeMobileEdit === 'orderRepeat' || this.typeMobileEdit === 'healthKitchen' || this.typeMobileEdit === 'healthDriver' || this.typeMobileEdit === 'couponAll') {
        this.itemsMobile.push({ index_in_type: this.indexMobileEdit, path: this.pathMobileEdit, path_lazy: this.pathLazyMobileEdit, name: this.nameMobileEdit, name_eng: this.nameEngMobileEdit, href: '', action_filter: '', action_type: this.typeMobileEdit, action_id: null, bg_color: this.bgColorMobileEdit })
      } else if (this.typeMobileEdit === 'link') {
        this.itemsMobile.push({ index_in_type: this.indexMobileEdit, path: this.pathMobileEdit, path_lazy: this.pathLazyMobileEdit, name: this.nameMobileEdit, name_eng: this.nameEngMobileEdit, href: this.hrefMobileEdit, action_filter: '', action_type: this.typeMobileEdit, action_id: null, bg_color: this.bgColorMobileEdit })
      } else if (this.typeMobileEdit === 'shop' || this.typeMobileEdit === 'group') {
        this.itemsMobile.push({ index_in_type: this.indexMobileEdit, path: this.pathMobileEdit, path_lazy: this.pathLazyMobileEdit, name: this.nameMobileEdit, name_eng: this.nameEngMobileEdit, href: '', action_filter: '', action_type: this.typeMobileEdit, action_id: this.idMobileEdit, bg_color: this.bgColorMobileEdit })
      } else if (this.typeMobileEdit === 'productFilter') {
        this.itemsMobile.push({ index_in_type: this.indexMobileEdit, path: this.pathMobileEdit, path_lazy: this.pathLazyMobileEdit, name: this.nameMobileEdit, name_eng: this.nameEngMobileEdit, href: '', action_filter: this.filterMobileEdit, action_type: this.typeMobileEdit, action_id: null, bg_color: this.bgColorMobileEdit })
      }
      this.itemsMobile.sort((a, b) => a.index_in_type - b.index_in_type)
      // console.log('this.itemsMobile', this.itemsMobile)
      this.closeDialogEditIconMobile()
    },
    async onFileChangeIconMobile (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.pathMobileEdit = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]

            const icon = { image: [base64], type: 'custom_icon_app' }

            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', icon)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.pathMobileEdit = response.data.list_path[0].path
              this.pathLazyMobileEdit = response.data.list_path[0].path_lazy
              this.itemsMobile = this.itemsMobile.map(item =>
                item.index_in_type === this.indexMobileEdit ? { ...item, path: this.pathMobileEdit, path_lazy: this.pathLazyMobileEdit } : item
              )
            }
          }
          reader.readAsDataURL(file)
          this.fileInputKey = Date.now()
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageIconMobile () {
      this.pathMobileEdit = ''
      this.pathLazyMobileEdit = ''
      this.fileInputKey = Date.now()
    },
    // async saveEditIcon () {
    //   const data = {
    //     image_custom_icon: this.items
    //   }
    //   this.$store.commit('openLoader')
    //   await this.$store.dispatch('actionsEditIcon', data)
    //   const response = this.$store.state.ModuleAdminManage.stateEditIcon
    //   if (response.code === 200) {
    //     this.$store.commit('closeLoader')
    //     await this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
    //     await this.getDataIcon()
    //   } else {
    //     this.$store.commit('closeLoader')
    //     await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
    //   }
    // },
    // async getDataIcon () {
    //   const data = null
    //   this.$store.commit('openLoader')
    //   await this.$store.dispatch('actionsgetDataIcon', data)
    //   const response = this.$store.state.ModuleAdminManage.stategetDataIcon
    //   if (response.code === 200) {
    //     this.$store.commit('closeLoader')
    //     if (response.data.length !== 0) {
    //       this.items = response.data[0].data
    //     } else {
    //       this.items = []
    //     }
    //     this.indexDefault = this.items.length
    //   } else {
    //     this.$store.commit('closeLoader')
    //     await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
    //   }
    // },
    gotoLink (href) {
      window.open(href, '_blank')
    },
    async getActiveGroupShop () {
      var data = {
        status: this.dataActive || ''
      }
      await this.$store.dispatch('actionActiveCustomGroupShop', data)
      var response = await this.$store.state.ModuleShop.stateActiveCustomGroupShop
      if (response.code === 200) {
        this.dataActive = response.data.status
        if (this.dataActive === 'active') {
          this.switchActive = true
        } else if (this.dataActive === 'inactive') {
          this.switchActive = false
        }
      } else {
        this.dataActive = 'inactive'
        this.switchActive = false
      }
    },
    handleActive (value) {
      if (value === true) {
        this.dataActive = 'active'
        this.$swal.fire({ icon: 'success', text: 'เปิดการใช้งานสำเร็จ', showConfirmButton: false, timer: 2000 })
      } else if (value === false) {
        this.dataActive = 'inactive'
        this.$swal.fire({ icon: 'success', text: 'ปิดการใช้งานสำเร็จ', showConfirmButton: false, timer: 2000 })
      }
      this.getActiveGroupShop()
    },
    handleSelectLayoutBanner (val) {
      this.selectLayoutBanner = val
    },
    async onFileChangeLayout1 (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.image_mobile_1 = url
          const reader = new FileReader()
          reader.onload = () => {
            let base64Image = ''
            base64Image = reader.result.split(',')[1]
            this.image_mobile_layout_1 = { image: [base64Image], type: 'custom_banner_mobile', layout: '1', num_layout: this.selectLayoutBanner }
            this.$forceUpdate()
          }
          reader.readAsDataURL(file)
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayout1 () {
      this.image_mobile_1 = ''
      this.image_mobile_layout_1 = ''
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    async onFileChangeLayout2 (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.image_mobile_2 = url
          const reader = new FileReader()
          reader.onload = () => {
            let base64Image = ''
            base64Image = reader.result.split(',')[1]
            this.image_mobile_layout_2 = { image: [base64Image], type: 'custom_banner_mobile', layout: '2', num_layout: this.selectLayoutBanner }
            this.$forceUpdate()
          }
          reader.readAsDataURL(file)
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayout2 () {
      this.image_mobile_2 = ''
      this.image_mobile_layout_2 = ''
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    async onFileChangeLayout3 (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.image_mobile_3 = url
          const reader = new FileReader()
          reader.onload = () => {
            let base64Image = ''
            base64Image = reader.result.split(',')[1]
            this.image_mobile_layout_3 = { image: [base64Image], type: 'custom_banner_mobile', layout: '3', num_layout: this.selectLayoutBanner }
            this.$forceUpdate()
          }
          reader.readAsDataURL(file)
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayout3 () {
      this.image_mobile_3 = ''
      this.image_mobile_layout_3 = ''
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    async onFileChangeLayout4 (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.image_mobile_4 = url
          const reader = new FileReader()
          reader.onload = () => {
            let base64Image = ''
            base64Image = reader.result.split(',')[1]
            this.image_mobile_layout_4 = { image: [base64Image], type: 'custom_banner_mobile', layout: '4', num_layout: this.selectLayoutBanner }
            this.$forceUpdate()
          }
          reader.readAsDataURL(file)
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayout4 () {
      this.image_mobile_4 = ''
      this.image_mobile_layout_4 = ''
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    async getGroupShop (layout) {
      this.$store.commit('openLoader')
      var dataAllShop = {
        group_id: ''
      }
      await this.$store.dispatch('actionFilterGroupShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateFilterGroupShop
      if (response.result === 'SUCCESS') {
        if (layout === '1') {
          this.itemSelectIDBanner1 = []
          this.itemSelectIDBanner1 = response.data.group_seller_data.map(item => {
            return {
              name: item.group_name,
              id: item.id
            }
          })
          this.labelSelectID1 = 'กรุณาเลือกประเภทร้านค้า'
          // console.log('this.itemSelectIDBanner1', this.itemSelectIDBanner1)
        } else if (layout === '2') {
          this.itemSelectIDBanner2 = []
          this.itemSelectIDBanner2 = response.data.group_seller_data.map(item => {
            return {
              name: item.group_name,
              id: item.id
            }
          })
          this.labelSelectID2 = 'กรุณาเลือกประเภทร้านค้า'
        } else if (layout === '3') {
          this.itemSelectIDBanner3 = []
          this.itemSelectIDBanner3 = response.data.group_seller_data.map(item => {
            return {
              name: item.group_name,
              id: item.id
            }
          })
          this.labelSelectID3 = 'กรุณาเลือกประเภทร้านค้า'
        } else if (layout === '4') {
          this.itemSelectIDBanner4 = []
          this.itemSelectIDBanner4 = response.data.group_seller_data.map(item => {
            return {
              name: item.group_name,
              id: item.id
            }
          })
          this.labelSelectID4 = 'กรุณาเลือกประเภทร้านค้า'
        } else if (layout === 'Mobile') {
          this.itemSelectIDMobile = []
          this.itemSelectIDMobile = response.data.group_seller_data.map(item => {
            return {
              name: item.group_name,
              id: item.id
            }
          })
          this.itemGroupIDMobile = this.itemSelectIDMobile
          this.labelSelectIDMobile = 'กรุณาเลือกประเภทร้านค้า'
          this.labelGroupIDMobile = this.labelSelectIDMobile
        }
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
        }
      }
      this.$store.commit('closeLoader')
    },
    async getShop (layout) {
      this.$store.commit('openLoader')
      const data = {
        unset_shop_profile: 'YES'
      }
      await this.$store.dispatch('actionsGroupStoreName', data)
      var response = await this.$store.state.ModuleHompage.stateGroupStoreName
      if (response.result === 'SUCCESS') {
        if (layout === '1') {
          this.itemSelectIDBanner1 = []
          this.itemSelectIDBanner1 = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
            return {
              name: item.shop_name,
              id: item.seller_shop_id
            }
          })
          this.labelSelectID1 = 'กรุณาเลือกร้านค้า'
        } else if (layout === '2') {
          this.itemSelectIDBanner2 = []
          this.itemSelectIDBanner2 = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
            return {
              name: item.shop_name,
              id: item.seller_shop_id
            }
          })
          this.labelSelectID2 = 'กรุณาเลือกร้านค้า'
        } else if (layout === '3') {
          this.itemSelectIDBanner3 = []
          this.itemSelectIDBanner3 = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
            return {
              name: item.shop_name,
              id: item.seller_shop_id
            }
          })
          this.labelSelectID3 = 'กรุณาเลือกร้านค้า'
        } else if (layout === '4') {
          this.itemSelectIDBanner4 = []
          this.itemSelectIDBanner4 = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
            return {
              name: item.shop_name,
              id: item.seller_shop_id
            }
          })
          this.labelSelectID4 = 'กรุณาเลือกร้านค้า'
        } else if (layout === 'Mobile') {
          this.itemSelectIDMobile = []
          this.itemSelectIDMobile = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
            return {
              name: item.shop_name,
              id: item.seller_shop_id
            }
          })
          this.itemShopIDMobile = this.itemSelectIDMobile
          this.labelSelectIDMobile = 'กรุณาเลือกร้านค้า'
          this.labelShopIDMobile = this.labelSelectIDMobile
        }
      }
      this.$store.commit('closeLoader')
    },
    async uploadS3Mobile () {
      if (this.image_banner_mobile.length > 0) {
        this.$store.commit('openLoader')
        for (const banner of this.image_banner_mobile) {
          if (banner !== '') {
            const layout = banner.layout
            await this.$store.dispatch('actionsUploadToS3', banner)
            const response = await this.$store.state.ModuleShop.stateUploadToS3
            if (response.code === 200) {
              const uploadedPath = response.data.list_path[0].path
              if (layout === '1') {
                this.image_mobile_layout_1 = uploadedPath
              }
              if (layout === '2') {
                this.image_mobile_layout_2 = uploadedPath
              }
              if (layout === '3') {
                this.image_mobile_layout_3 = uploadedPath
              }
              if (layout === '4') {
                this.image_mobile_layout_4 = uploadedPath
              }
            }
          }
        }
        this.$forceUpdate()
        this.$store.commit('closeLoader')
      }
      this.image_banner_mobile = []
    },
    async handleSelectTypeBanner (layout) {
      if (layout === '1') {
        if (this.selectTypeBanner1 === 'group') {
          await this.getGroupShop(layout)
        } else if (this.selectTypeBanner1 === 'shop') {
          await this.getShop(layout)
        }
      } else if (layout === '2') {
        if (this.selectTypeBanner2 === 'group') {
          await this.getGroupShop(layout)
        } else if (this.selectTypeBanner2 === 'shop') {
          await this.getShop(layout)
        }
      } else if (layout === '3') {
        if (this.selectTypeBanner3 === 'group') {
          await this.getGroupShop(layout)
        } else if (this.selectTypeBanner3 === 'shop') {
          await this.getShop(layout)
        }
      } else if (layout === '4') {
        if (this.selectTypeBanner4 === 'group') {
          await this.getGroupShop(layout)
        } else if (this.selectTypeBanner4 === 'shop') {
          await this.getShop(layout)
        }
      }
    },
    async handleSelectIDBanner (layout) {
      if (layout === '1') {
        var selectedBanner1 = this.itemSelectIDBanner1.find(item => item.id === this.selectIDBanner1)
        this.selectIDBanner1 = { name: selectedBanner1.name, id: selectedBanner1.id }
      } else if (layout === '2') {
        var selectedBanner2 = this.itemSelectIDBanner2.find(item => item.id === this.selectIDBanner2)
        this.selectIDBanner2 = { name: selectedBanner2.name, id: selectedBanner2.id }
      } else if (layout === '3') {
        var selectedBanner3 = this.itemSelectIDBanner3.find(item => item.id === this.selectIDBanner3)
        this.selectIDBanner3 = { name: selectedBanner3.name, id: selectedBanner3.id }
      } else if (layout === '4') {
        var selectedBanner4 = this.itemSelectIDBanner4.find(item => item.id === this.selectIDBanner4)
        this.selectIDBanner4 = { name: selectedBanner4.name, id: selectedBanner4.id }
      }
    },
    async handleSelectTypeMobile () {
      if (this.typeMobileEdit === 'group') {
        await this.getGroupShop('Mobile')
      } else if (this.typeMobileEdit === 'shop') {
        await this.getShop('Mobile')
      }
      this.idMobileEdit = ''
      this.hrefMobileEdit = ''
      this.filterMobileEdit = ''
      // console.log('this.typeMobileEdit', this.typeMobileEdit)
    },
    async handleSelectIDMobile (val) {
      this.idMobileEdit = val.id
      // console.log('val', val)
      // console.log('this.idMobileEdit', this.idMobileEdit)
    },
    async handleSelectFilterMobile (val) {
      this.filterMobileEdit = val
      // console.log('val', val)
    },
    async getDetailBannerGroupShopV2 () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetBannerGroupShopV2')
      var response = await this.$store.state.ModuleShop.stateGetBannerGroupShopV2
      if (response.code === 200) {
        this.selectLayoutBanner = response.data.num_layout_banner || '1'
        const banners = response.data.banner === null ? [] : response.data.banner

        if (banners[0]) {
          this.image_mobile_1 = banners[0].image || ''
          this.image_mobile_layout_1 = this.image_mobile_1
          this.LinkBanner1 = banners[0].link || ''
          this.selectTypeBanner1 = banners[0].action_type
          await this.handleSelectTypeBanner(banners[0].layout)
          if (this.selectTypeBanner1 !== 'image' && this.selectTypeBanner1 !== 'link') {
            this.selectIDBanner1 = parseInt(banners[0].action_id)
            await this.handleSelectIDBanner(banners[0].layout)
          }
        }

        if (banners[1] && this.selectLayoutBanner >= '2') {
          this.image_mobile_2 = banners[1].image || ''
          this.image_mobile_layout_2 = this.image_mobile_2
          this.LinkBanner2 = banners[1].link || ''
          this.selectTypeBanner2 = banners[1].action_type
          await this.handleSelectTypeBanner(banners[1].layout)
          if (this.selectTypeBanner2 !== 'image' && this.selectTypeBanner2 !== 'link') {
            this.selectIDBanner2 = parseInt(banners[1].action_id)
            await this.handleSelectIDBanner(banners[1].layout)
          }
        }

        if (banners[2] && this.selectLayoutBanner >= '3') {
          this.image_mobile_3 = banners[2].image || ''
          this.image_mobile_layout_3 = this.image_mobile_3
          this.LinkBanner3 = banners[2].link || ''
          this.selectTypeBanner3 = banners[2].action_type
          await this.handleSelectTypeBanner(banners[2].layout)
          if (this.selectTypeBanner3 !== 'image' && this.selectTypeBanner3 !== 'link') {
            this.selectIDBanner3 = parseInt(banners[2].action_id)
            await this.handleSelectIDBanner(banners[2].layout)
          }
        }

        if (banners[3] && this.selectLayoutBanner === '4') {
          this.image_mobile_4 = banners[3].image || ''
          this.image_mobile_layout_4 = this.image_mobile_4
          this.LinkBanner4 = banners[3].link || ''
          this.selectTypeBanner4 = banners[3].action_type
          await this.handleSelectTypeBanner(banners[3].layout)
          if (this.selectTypeBanner4 !== 'image' && this.selectTypeBanner4 !== 'link') {
            this.selectIDBanner4 = parseInt(banners[3].action_id)
            await this.handleSelectIDBanner(banners[3].layout)
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async clearDataBannerMobile () {
      this.selectLayoutBanner = '1'
      this.selectTypeBanner1 = ''
      this.selectTypeBanner2 = ''
      this.selectTypeBanner3 = ''
      this.selectTypeBanner4 = ''
      this.selectIDBanner1 = ''
      this.selectIDBanner2 = ''
      this.selectIDBanner3 = ''
      this.selectIDBanner4 = ''
      this.labelSelectID1 = ''
      this.labelSelectID2 = ''
      this.labelSelectID3 = ''
      this.labelSelectID4 = ''
      this.LinkBanner1 = ''
      this.LinkBanner2 = ''
      this.LinkBanner3 = ''
      this.LinkBanner4 = ''
      this.image_mobile_1 = ''
      this.image_mobile_layout_1 = ''
      this.image_mobile_2 = ''
      this.image_mobile_layout_2 = ''
      this.image_mobile_3 = ''
      this.image_mobile_layout_3 = ''
      this.image_mobile_4 = ''
      this.image_mobile_layout_4 = ''
      this.itemSelectIDBanner1 = []
      this.itemSelectIDBanner2 = []
      this.itemSelectIDBanner3 = []
      this.itemSelectIDBanner4 = []
      this.image_banner_mobile = []
    },
    // async updateBannerMobile (status) {
    //   var isValid = true
    //   var payload = {}
    //   this.image_banner_mobile.push(this.image_mobile_layout_1, this.image_mobile_layout_2, this.image_mobile_layout_3, this.image_mobile_layout_4)
    //   // console.log('this.image_banner_mobile', this.image_banner_mobile)
    //   if (this.image_banner_mobile.length !== 0) {
    //     await this.uploadS3Mobile()
    //   }
    //   if (status === 'edit') {
    //     if (this.selectLayoutBanner === '1') {
    //       if (this.image_mobile_1 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 1', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       }
    //       if (this.selectTypeBanner1 === 'group' || this.selectTypeBanner1 === 'shop') {
    //         if (this.selectIDBanner1.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner1 === 'link') {
    //         if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //     } else if (this.selectLayoutBanner === '2') {
    //       if (this.image_mobile_1 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 1', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       } else if (this.image_mobile_2 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 2', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       }
    //       if (this.selectTypeBanner1 === 'group' || this.selectTypeBanner1 === 'shop') {
    //         if (this.selectIDBanner1.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner2 === 'group' || this.selectTypeBanner2 === 'shop') {
    //         if (this.selectIDBanner2.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 2', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner1 === 'link') {
    //         if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner2 === 'link') {
    //         if (this.LinkBanner2 === '' || this.LinkBanner2 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 2', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //     } else if (this.selectLayoutBanner === '3') {
    //       if (this.image_mobile_1 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 1', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       } else if (this.image_mobile_2 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 2', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       } else if (this.image_mobile_3 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 3', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       }
    //       if (this.selectTypeBanner1 === 'group' || this.selectTypeBanner1 === 'shop') {
    //         if (this.selectIDBanner1.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner2 === 'group' || this.selectTypeBanner2 === 'shop') {
    //         if (this.selectIDBanner2.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 2', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner3 === 'group' || this.selectTypeBanner3 === 'shop') {
    //         if (this.selectIDBanner3.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 3', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner1 === 'link') {
    //         if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner2 === 'link') {
    //         if (this.LinkBanner2 === '' || this.LinkBanner2 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 2', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner3 === 'link') {
    //         if (this.LinkBanner3 === '' || this.LinkBanner3 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 3', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //     } else if (this.selectLayoutBanner === '4') {
    //       if (this.image_mobile_1 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 1', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       } else if (this.image_mobile_2 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 2', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       } else if (this.image_mobile_3 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 3', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       } else if (this.image_mobile_4 === '') {
    //         this.closeDialogConfirm()
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ layout 4', showConfirmButton: false, timer: 1500 })
    //         isValid = false
    //       }
    //       if (this.selectTypeBanner1 === 'group' || this.selectTypeBanner1 === 'shop') {
    //         if (this.selectIDBanner1.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner2 === 'group' || this.selectTypeBanner2 === 'shop') {
    //         if (this.selectIDBanner2.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 2', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner3 === 'group' || this.selectTypeBanner3 === 'shop') {
    //         if (this.selectIDBanner3.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 3', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner4 === 'group' || this.selectTypeBanner4 === 'shop') {
    //         if (this.selectIDBanner4.id === undefined) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 4', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner1 === 'link') {
    //         if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner2 === 'link') {
    //         if (this.LinkBanner2 === '' || this.LinkBanner2 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 2', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner3 === 'link') {
    //         if (this.LinkBanner3 === '' || this.LinkBanner3 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 3', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //       if (this.selectTypeBanner4 === 'link') {
    //         if (this.LinkBanner4 === '' || this.LinkBanner4 === null) {
    //           this.closeDialogConfirm()
    //           this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 4', showConfirmButton: false, timer: 1500 })
    //           isValid = false
    //         }
    //       }
    //     }
    //   }
    //   if (isValid === true) {
    //     if (status === 'delete') {
    //       payload = {
    //         banner: [],
    //         num_layout_banner: null
    //       }
    //     } else if (status === 'edit') {
    //       payload = {
    //         banner: [
    //           {
    //             image: this.image_mobile_layout_1,
    //             name: this.selectIDBanner1.name === undefined || this.selectIDBanner1.name === null || this.selectTypeBanner1 === 'image' ? '' : this.selectIDBanner1.name,
    //             action_id: this.selectIDBanner1.id === undefined || this.selectIDBanner1.id === null || this.selectTypeBanner1 === 'image' ? '' : String(this.selectIDBanner1.id),
    //             action_type: this.selectTypeBanner1 === 'group' ? 'group' : this.selectTypeBanner1,
    //             link: this.selectTypeBanner1 === 'link' ? this.LinkBanner1 : '',
    //             layout: '1'
    //           },
    //           {
    //             image: this.selectLayoutBanner === '1' ? '' : this.image_mobile_layout_2,
    //             name: this.selectLayoutBanner === '1' || this.selectIDBanner2.name === undefined || this.selectIDBanner2.name === null || this.selectTypeBanner2 === 'image' ? '' : this.selectIDBanner2.name,
    //             action_id: this.selectLayoutBanner === '1' || this.selectIDBanner2.id === undefined || this.selectIDBanner2.id === null || this.selectTypeBanner2 === 'image' ? '' : String(this.selectIDBanner2.id),
    //             action_type: this.selectTypeBanner2 === 'group' ? 'group' : this.selectTypeBanner2,
    //             link: this.selectTypeBanner2 === 'link' ? this.LinkBanner2 : '',
    //             layout: '2'
    //           },
    //           {
    //             image: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' ? '' : this.image_mobile_layout_3,
    //             name: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' || this.selectIDBanner3.name === undefined || this.selectIDBanner3.name === null || this.selectTypeBanner3 === 'image' ? '' : this.selectIDBanner3.name,
    //             action_id: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' || this.selectIDBanner3.id === undefined || this.selectIDBanner3.id === null || this.selectTypeBanner3 === 'image' ? '' : String(this.selectIDBanner3.id),
    //             action_type: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' ? '' : this.selectTypeBanner3,
    //             link: this.selectTypeBanner3 === 'link' ? this.LinkBanner3 : '',
    //             layout: '3'
    //           },
    //           {
    //             image: this.selectLayoutBanner !== '4' ? '' : this.image_mobile_layout_4,
    //             name: this.selectLayoutBanner !== '4' || this.selectIDBanner4.name === undefined || this.selectIDBanner4.name === null || this.selectTypeBanner4 === 'image' ? '' : this.selectIDBanner4.name,
    //             action_id: this.selectLayoutBanner !== '4' || this.selectIDBanner4.id === undefined || this.selectIDBanner4.id === null || this.selectTypeBanner4 === 'image' ? '' : String(this.selectIDBanner4.id),
    //             action_type: this.selectLayoutBanner !== '4' ? '' : this.selectTypeBanner4,
    //             link: this.selectTypeBanner4 === 'link' ? this.LinkBanner4 : '',
    //             layout: '4'
    //           }
    //         ],
    //         num_layout_banner: this.selectLayoutBanner
    //       }
    //     }
    //     this.$store.commit('openLoader')
    //     try {
    //       await this.$store.dispatch('actionEditBannerGroupShopV2', payload)
    //       var response = await this.$store.state.ModuleShop.stateEditBannerGroupShopV2
    //       if (response.code === 200) {
    //         this.$store.commit('closeLoader')
    //         if (status === 'delete') {
    //           this.dialogConfirmDelete = false
    //           await this.clearDataBannerMobile()
    //           await this.$swal.fire({ icon: 'success', text: 'ลบข้อมูลสำเร็จ', showConfirmButton: false, timer: 1500 })
    //         } else if (status === 'edit') {
    //           this.dialogConfirm = false
    //           await this.$swal.fire({ icon: 'success', text: 'บันทึกข้อมูลสำเร็จ', showConfirmButton: false, timer: 1500 })
    //         }
    //         this.removeImageLayout1()
    //         this.removeImageLayout2()
    //         this.removeImageLayout3()
    //         this.removeImageLayout4()
    //         await this.getDetailBannerGroupShopV2()
    //         // if (this.dataType === 'custom' || this.dataType === 'default') {
    //         //   this.handleType('banner')
    //         // }
    //       }
    //     } catch (error) {
    //       this.$store.commit('closeLoader')
    //       this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
    //     }
    //   }
    // },
    closeDialogConfirm () {
      this.dialogConfirm = false
    },
    handleSelectTypeLayout () {
      this.data_banner_1 = []
      this.image_layout_1 = {}
      this.data_banner_2 = []
      this.image_layout_2 = {}
      this.data_banner_3 = []
      this.image_layout_3 = {}
      this.fileInputKey = Date.now()
      if (this.selectTypeLayout === '2duoright') {
        this.selectWidthLayout = '70-30'
        this.handleSelectWidthLayout()
      }
    },
    handleSelectWidthLayout () {
      this.width1 = this.selectWidthLayout.slice(0, 2)
      this.width2 = this.selectWidthLayout.slice(-2)
      this.data_banner_1 = []
      this.image_layout_1 = {}
      this.data_banner_2 = []
      this.image_layout_2 = {}
      this.data_banner_3 = []
      this.image_layout_3 = {}
      this.fileInputKey = Date.now()
      // console.log(this.width1)
      // console.log(this.width2)
    },
    // handleSelectWidth2Duo () {
    //   console.log(this.selectWidthLayout1)
    //   console.log(this.selectWidthLayout2)
    // },
    convertToBase64 (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          const base64 = reader.result.split(',')[1]
          resolve(base64)
        }
        reader.onerror = reject
        reader.readAsDataURL(file)
      })
    },
    async onFileChangeLayoutWeb1 (event) {
      const files = event.target.files
      if (!files || files.length === 0) return
      const validFiles = Array.from(files).filter(file =>
        ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
      )
      const total = this.data_banner_1.length + validFiles.length
      if (total > 10) {
        this.$swal.fire({
          icon: 'warning',
          text: 'สามารถอัปโหลดได้สูงสุด 10 รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
        return
      }
      for (const file of validFiles) {
        const url = URL.createObjectURL(file)
        const base64 = await this.convertToBase64(file)
        this.data_banner_1.push({ path: url, path_ipad: url, path_mobile: url, path_lazy: '', href: '' })
        const currentIndex = this.data_banner_1.length - 1
        this.data_banner_1 = this.data_banner_1.map((item, index) => ({
          ...item,
          id: index + 1
        }))
        this.image_layout_1 = {
          image: [],
          type: 'custom_banner_new',
          layout: '0',
          type_layout: this.selectTypeLayout,
          ratio: this.selectTypeLayout === '2duoright' ? this.width1 : ''
        }
        if (!this.image_layout_1.image) {
          this.image_layout_1.image = []
        }
        this.image_layout_1.image.push(base64)
        this.$store.commit('openLoader')
        // console.log('this.image_layout_1', this.image_layout_1)
        await this.$store.dispatch('actionsUploadToS3', this.image_layout_1)
        const response = await this.$store.state.ModuleShop.stateUploadToS3
        this.$store.commit('closeLoader')

        if (response.code === 200 && response.data.list_path.length > 0) {
          const s3Image = response.data.list_path[0]
          // console.log('s3Image', s3Image)
          this.data_banner_1[currentIndex] = {
            ...s3Image,
            id: currentIndex + 1,
            href: ''
          }
        }
        // console.log('this.data_banner_1', this.data_banner_1)
      }
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    removeImageLayoutWeb1 (index) {
      this.data_banner_1.splice(index, 1)
      if (
        this.image_layout_1 &&
        Array.isArray(this.image_layout_1.image)
      ) {
        this.image_layout_1.image.splice(index, 1)
      }
      this.data_banner_1 = this.data_banner_1.map((item, id) => ({
        ...item,
        id: id + 1
      }))
      this.$forceUpdate()
    },
    reorderItemsDataBanner1 () {
      this.data_banner_1 = this.data_banner_1.map((item, index) => ({
        ...item,
        id: index + 1
      }))
      this.$nextTick(() => this.$forceUpdate())
    },
    async onFileChangeLayoutWeb2 (event) {
      const files = event.target.files
      if (!files || files.length === 0) return
      const validFiles = Array.from(files).filter(file =>
        ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
      )
      const total = this.data_banner_2.length + validFiles.length
      if (total > 4) {
        this.$swal.fire({
          icon: 'warning',
          text: 'สามารถอัปโหลดได้สูงสุด 4 รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
        return
      }
      for (const file of validFiles) {
        const url = URL.createObjectURL(file)
        const base64 = await this.convertToBase64(file)
        this.data_banner_2.push({ path: url, path_ipad: url, path_mobile: url, path_lazy: '', href: '' })
        const currentIndex = this.data_banner_2.length - 1
        this.data_banner_2 = this.data_banner_2.map((item, index) => ({
          ...item,
          id: index + 1
        }))
        this.image_layout_2 = {
          image: [],
          type: 'custom_banner_new',
          layout: '1',
          type_layout: this.selectTypeLayout,
          ratio: this.selectTypeLayout === '2duoright' ? this.width2 : ''
        }
        if (!this.image_layout_2.image) {
          this.image_layout_2.image = []
        }
        this.image_layout_2.image.push(base64)
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsUploadToS3', this.image_layout_2)
        const response = await this.$store.state.ModuleShop.stateUploadToS3
        this.$store.commit('closeLoader')

        if (response.code === 200 && response.data.list_path.length > 0) {
          const s3Image = response.data.list_path[0]
          this.data_banner_2[currentIndex] = {
            ...s3Image,
            id: currentIndex + 1,
            href: ''
          }
        }
        // console.log('this.data_banner_2', this.data_banner_2)
      }
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    removeImageLayoutWeb2 (index) {
      this.data_banner_2.splice(index, 1)
      if (
        this.image_layout_2 &&
        Array.isArray(this.image_layout_2.image)
      ) {
        this.image_layout_2.image.splice(index, 1)
      }
      this.data_banner_2 = this.data_banner_2.map((item, id) => ({
        ...item,
        id: id + 1
      }))
      this.$forceUpdate()
    },
    reorderItemsDataBanner2 () {
      this.data_banner_2 = this.data_banner_2.map((item, index) => ({
        ...item,
        id: index + 1
      }))
      this.$nextTick(() => this.$forceUpdate())
    },
    async onFileChangeLayoutWeb3 (event) {
      const files = event.target.files
      if (!files || files.length === 0) return
      const validFiles = Array.from(files).filter(file =>
        ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
      )
      const total = this.data_banner_3.length + validFiles.length
      if (total > 4) {
        this.$swal.fire({
          icon: 'warning',
          text: 'สามารถอัปโหลดได้สูงสุด 4 รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
        return
      }
      for (const file of validFiles) {
        const url = URL.createObjectURL(file)
        const base64 = await this.convertToBase64(file)
        this.data_banner_3.push({ path: url, path_ipad: url, path_mobile: url, path_lazy: '', href: '' })
        const currentIndex = this.data_banner_3.length - 1
        this.data_banner_3 = this.data_banner_3.map((item, index) => ({
          ...item,
          id: index + 1
        }))
        this.image_layout_3 = {
          image: [],
          type: 'custom_banner_new',
          layout: '2',
          type_layout: this.selectTypeLayout,
          ratio: this.selectTypeLayout === '2duoright' ? this.width2 : ''
        }
        if (!this.image_layout_3.image) {
          this.image_layout_3.image = []
        }
        this.image_layout_3.image.push(base64)
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsUploadToS3', this.image_layout_3)
        const response = await this.$store.state.ModuleShop.stateUploadToS3
        this.$store.commit('closeLoader')

        if (response.code === 200 && response.data.list_path.length > 0) {
          const s3Image = response.data.list_path[0]
          this.data_banner_3[currentIndex] = {
            ...s3Image,
            id: currentIndex + 1,
            href: ''
          }
        }
        // console.log('this.data_banner_3', this.data_banner_3)
      }
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    // removeImageLayoutWeb3 (index) {
    //   this.data_banner_3.splice(index, 1)
    //   this.image_layout_3.image.splice(index, 1)
    //   this.data_banner_3 = this.data_banner_3.map((item, id) => ({
    //     ...item,
    //     id: id + 1
    //   }))
    //   this.$forceUpdate()
    // },
    removeImageLayoutWeb3 (index) {
      this.data_banner_3.splice(index, 1)
      if (
        this.image_layout_3 &&
        Array.isArray(this.image_layout_3.image)
      ) {
        this.image_layout_3.image.splice(index, 1)
      }
      this.data_banner_3 = this.data_banner_3.map((item, id) => ({
        ...item,
        id: id + 1
      }))
      this.$forceUpdate()
    },
    reorderItemsDataBanner3 () {
      this.data_banner_3 = this.data_banner_3.map((item, index) => ({
        ...item,
        id: index + 1
      }))
      this.$nextTick(() => this.$forceUpdate())
    },
    closeDialogConfirmWeb () {
      this.dialogConfirmWeb = false
    },
    closeDialogConfirmMobile () {
      this.dialogConfirmMobile = false
    },
    async onFileChangeLayoutMobile (event) {
      const files = event.target.files
      if (!files || files.length === 0) return
      const validFiles = Array.from(files).filter(file =>
        ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
      )
      const total = this.data_banner_Mobile.length + validFiles.length
      if (total > 10) {
        this.$swal.fire({
          icon: 'warning',
          text: 'สามารถอัปโหลดได้สูงสุด 10 รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
        return
      }
      for (const file of validFiles) {
        const url = URL.createObjectURL(file)
        const base64 = await this.convertToBase64(file)
        this.data_banner_Mobile.push({ path: url, path_lazy: '', action_type: null, action_id: null, action_filter: '', href: '' })
        const currentIndex = this.data_banner_Mobile.length - 1
        this.data_banner_Mobile = this.data_banner_Mobile.map((item, index) => ({
          ...item,
          id: index + 1
        }))
        this.imageBigBannerMobile = {
          image: [],
          type: 'banner_landing_page_app'
        }
        if (!this.imageBigBannerMobile.image) {
          this.imageBigBannerMobile.image = []
        }
        this.imageBigBannerMobile.image.push(base64)
        this.$store.commit('openLoader')
        // console.log('this.imageBigBannerMobile', this.imageBigBannerMobile)
        await this.$store.dispatch('actionsUploadToS3', this.imageBigBannerMobile)
        const response = await this.$store.state.ModuleShop.stateUploadToS3
        this.$store.commit('closeLoader')

        if (response.code === 200 && response.data.list_path.length > 0) {
          const s3Image = response.data.list_path[0]
          // console.log('s3Image', s3Image)
          this.data_banner_Mobile[currentIndex] = {
            ...s3Image,
            id: currentIndex + 1,
            action_type: null,
            action_id: null,
            action_filter: '',
            href: ''
          }
        }
        // console.log('this.data_banner_Mobile', this.data_banner_Mobile)
      }
      this.fileInputKey = Date.now()
      this.$forceUpdate()
    },
    removeImageLayoutMobile (index) {
      this.data_banner_Mobile.splice(index, 1)
      if (
        this.imageBigBannerMobile &&
        Array.isArray(this.imageBigBannerMobile.image)
      ) {
        this.imageBigBannerMobile.image.splice(index, 1)
      }
      this.data_banner_Mobile = this.data_banner_Mobile.map((item, id) => ({
        ...item,
        id: id + 1
      }))
      this.$forceUpdate()
    },
    // removeImageLayoutMobile (index) {
    //   this.data_banner_Mobile.splice(index, 1)
    //   this.imageBigBannerMobile.image.splice(index, 1)
    //   this.data_banner_Mobile = this.data_banner_Mobile.map((item, id) => ({
    //     ...item,
    //     id: id + 1
    //   }))
    //   this.$forceUpdate()
    // },
    reorderItemsDataMobile () {
      this.data_banner_Mobile = this.data_banner_Mobile.map((item, index) => ({
        ...item,
        id: index + 1
      }))
      this.$nextTick(() => this.$forceUpdate())
    },
    onInputLink (id, val, banner) {
      if (banner === '1') {
        this.data_banner_1.find(item => item.id === id).href = val
      } else if (banner === '2') {
        this.data_banner_2.find(item => item.id === id).href = val
      } else if (banner === '3') {
        this.data_banner_3.find(item => item.id === id).href = val
      }
    },
    async onInputTypeMobile (id, val) {
      this.data_banner_Mobile.find(item => item.id === id).action_type = val
      this.typeMobileEdit = val
      if (val === 'group') {
        await this.getGroupShop('Mobile')
      } else if (val === 'shop') {
        await this.getShop('Mobile')
      }
      // console.log('this.itemGroupIDMobile', this.itemGroupIDMobile)
      // console.log('this.itemShopIDMobile', this.itemShopIDMobile)
      // this.idMobileEdit = ''
      // this.hrefMobileEdit = ''
      // console.log('val', val)
      // console.log('this.data_banner_Mobile', this.data_banner_Mobile)
    },
    onInputIDMobile (id, val) {
      this.data_banner_Mobile.find(item => item.id === id).action_id = val.id
      // console.log('val', val)
      // console.log('this.data_banner_Mobile', this.data_banner_Mobile)
      // this.idMobileEdit = val
    },
    onInputLinkMobile (id, val) {
      this.data_banner_Mobile.find(item => item.id === id).href = val
      // console.log('val', val)
      // console.log('this.data_banner_Mobile', this.data_banner_Mobile)
    },
    onInputFilterMobile (id, val) {
      this.data_banner_Mobile.find(item => item.id === id).action_filter = val
      // console.log('val', val)
      // console.log('this.data_banner_Mobile', this.data_banner_Mobile)
    },
    async updateBanner () {
      if (this.selectTypeLayout === '' || this.selectTypeLayout === null || this.selectTypeLayout === undefined) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกรูปแบบ Banner', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else if (this.selectTypeLayout === 'single' && this.data_banner_1.length === 0) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของ Banner ใหญ่', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else if (this.selectTypeLayout !== 'single' && this.data_banner_1.length === 0) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของ Banner ใหญ่', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else if (this.selectTypeLayout !== 'single' && this.data_banner_2.length === 0) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของ Banner ย่อย', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else if (this.selectTypeLayout !== 'single' && this.data_banner_3.length === 0) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของ Banner ย่อย', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else if (this.selectTypeLayout === '2duoright' && (this.selectWidthLayout === null || this.selectWidthLayout === undefined || this.selectWidthLayout === '')) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกความกว้างของคอลัมน์', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else {
        this.isValid = true
      }
      if (this.isValid === true) {
        this.$store.commit('openLoader')
        var dataBanner = []
        if (this.selectTypeLayout === 'single') {
          dataBanner = [
            {
              index_in_type: 0,
              image: this.data_banner_1
            }
          ]
        } else if (this.selectTypeLayout !== 'single') {
          dataBanner = [
            {
              index_in_type: 0,
              image: this.data_banner_1
            },
            {
              index_in_type: 1,
              image: this.data_banner_2
            },
            {
              index_in_type: 2,
              image: this.data_banner_3
            }
          ]
        }
        const payload = {
          image_custom_icon: this.items,
          big_banner_2_image: {
            big_banner_type: this.selectTypeLayout,
            big_banner_1_ratio: this.selectTypeLayout === '2duoright' ? this.width1 : '',
            big_banner_2_ratio: this.selectTypeLayout === '2duoright' ? this.width2 : '',
            big_banner_image: dataBanner
          },
          Coin: {
            line_color: this.lineColor,
            number_color: this.titleCoinColor,
            gradient_color: this.bgColor2,
            bg_type: this.bgType,
            bg_color: this.bgColor,
            bg_image: this.bgImage,
            title: {
              text: this.titleText,
              text_eng: this.titleTextEng,
              color: this.titleColor
            },
            check_in: {
              text: this.btnText,
              text_eng: this.btnTextEng,
              color: this.btnTextColor,
              btn_color: this.btnColor
            },
            coin_type: this.coinType,
            days: [
              {
                id: 1,
                coin: this.coinDay1,
                day: 'วันที่ 1'
              },
              {
                id: 2,
                coin: this.coinType === 'Default' ? this.coinDay1 : this.coinDay2,
                day: 'วันที่ 2'
              },
              {
                id: 3,
                coin: this.coinType === 'Default' ? this.coinDay1 : this.coinDay3,
                day: 'วันที่ 3'
              },
              {
                id: 4,
                coin: this.coinType === 'Default' ? this.coinDay1 : this.coinDay4,
                day: 'วันที่ 4'
              },
              {
                id: 5,
                coin: this.coinType === 'Default' ? this.coinDay1 : this.coinDay5,
                day: 'วันที่ 5'
              },
              {
                id: 6,
                coin: this.coinType === 'Default' ? this.coinDay1 : this.coinDay6,
                day: 'วันที่ 6'
              },
              {
                id: 7,
                coin: this.coinType === 'Default' ? this.coinDay1 : this.coinDay7,
                day: 'วันที่ 7'
              }
            ],
            coin_color: this.coinColor,
            day_color: this.dayColor,
            image_coin: this.coinImage,
            image_last_coin: this.lastCoinImage,
            image_check_coin: this.checkCoinImage
          }
        }
        await this.$store.dispatch('actionsEditIcon', payload)
        const response = this.$store.state.ModuleAdminManage.stateEditIcon
        if (response.code === 200) {
          this.getDataBannerWeb()
          this.$store.commit('closeLoader')
          await this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
          // console.log('response', response)
        } else {
          this.$store.commit('closeLoader')
          await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
        }
      }
      this.dialogConfirmWeb = false
    },
    async updateBannerMobile () {
      if (this.data_banner_Mobile.length === 0) {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของ Banner ใหญ่', showConfirmButton: false, timer: 1500 })
        this.isValid = false
      } else {
        this.isValid = true
      }
      if (this.isValid === true) {
        this.$store.commit('openLoader')
        var dataBanner = [
          {
            index_in_type: 0,
            image: this.data_banner_Mobile
          }
        ]
        const payload = {
          image_custom_icon: this.itemsMobile,
          big_banner_2_image: {
            big_banner_image: dataBanner
          }
        }
        await this.$store.dispatch('actionsEditIconMobile', payload)
        const response = this.$store.state.ModuleAdminManage.stateEditIconMobile
        if (response.code === 200) {
          this.getDataBannerMobile()
          this.$store.commit('closeLoader')
          await this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
          // console.log('response', response)
        } else {
          this.$store.commit('closeLoader')
          await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
        }
      }
      this.dialogConfirmMobile = false
    },
    setDaysFromCoinDays (status) {
      if (status === 'default') {
        // console.log('111')
        if (this.itemsCoin.length === 0) {
          this.days = [
            { id: 1, coin: '0.10', day: 'วันที่ 1' },
            { id: 2, coin: '0.10', day: 'วันที่ 2' },
            { id: 3, coin: '0.10', day: 'วันที่ 3' },
            { id: 4, coin: '0.10', day: 'วันที่ 4' },
            { id: 5, coin: '0.10', day: 'วันที่ 5' },
            { id: 6, coin: '0.10', day: 'วันที่ 6' },
            { id: 7, coin: '0.10', day: 'วันที่ 7' }
          ]
        } else {
          // console.log('111')
          this.days = this.itemsCoin.days
        }
      } else if (status === 'reset') {
        this.days = [
          { id: 1, coin: '0.10', day: 'วันที่ 1' },
          { id: 2, coin: '0.10', day: 'วันที่ 2' },
          { id: 3, coin: '0.10', day: 'วันที่ 3' },
          { id: 4, coin: '0.10', day: 'วันที่ 4' },
          { id: 5, coin: '0.10', day: 'วันที่ 5' },
          { id: 6, coin: '0.10', day: 'วันที่ 6' },
          { id: 7, coin: '0.10', day: 'วันที่ 7' }
        ]
      } else if (status === 'close' || status === 'update') {
        this.days = [
          { id: 1, coin: this.coinType === 'Custom' ? this.coinDay1 : this.coinDay1, day: 'วันที่ 1' },
          { id: 2, coin: this.coinType === 'Custom' ? this.coinDay2 : this.coinDay1, day: 'วันที่ 2' },
          { id: 3, coin: this.coinType === 'Custom' ? this.coinDay3 : this.coinDay1, day: 'วันที่ 3' },
          { id: 4, coin: this.coinType === 'Custom' ? this.coinDay4 : this.coinDay1, day: 'วันที่ 4' },
          { id: 5, coin: this.coinType === 'Custom' ? this.coinDay5 : this.coinDay1, day: 'วันที่ 5' },
          { id: 6, coin: this.coinType === 'Custom' ? this.coinDay6 : this.coinDay1, day: 'วันที่ 6' },
          { id: 7, coin: this.coinType === 'Custom' ? this.coinDay7 : this.coinDay1, day: 'วันที่ 7' }
        ]
      }
    },
    async getDataBannerWeb () {
      const data = null
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsgetDataIcon', data)
      const response = this.$store.state.ModuleAdminManage.stategetDataIcon
      if (response.code === 200) {
        // console.log('response', response)
        if (response.data.custom_icon.length !== 0) {
          this.items = response.data.custom_icon
        } else {
          this.items = []
        }
        this.indexDefault = this.items.length
        if (response.data.banner.length !== 0) {
          this.selectTypeLayout = response.data.banner.big_banner_type
          if (this.selectTypeLayout === '2duoright') {
            this.width1 = response.data.banner.big_banner_1_ratio
            this.width2 = response.data.banner.big_banner_2_ratio
            if (this.width1 === '70') {
              this.selectWidthLayout = '70-30'
            } else if (this.width1 === '60') {
              this.selectWidthLayout = '60-40'
            }
          } else {
            this.selectWidthLayout = ''
            this.width1 = ''
            this.width2 = ''
          }
          if (this.selectTypeLayout === 'single') {
            this.data_banner_1 = response.data.banner.data_banner_1
          } else {
            this.data_banner_1 = response.data.banner.data_banner_1
            this.data_banner_2 = response.data.banner.data_banner_2
            this.data_banner_3 = response.data.banner.data_banner_3
          }
        } else {
          this.selectTypeLayout = null
          this.width1 = ''
          this.width2 = ''
          this.selectWidthLayout = ''
          this.data_banner_1 = []
          this.data_banner_2 = []
          this.data_banner_3 = []
        }
        if (response.data.Coin.length !== 0) {
          // console.log('11')
          this.itemsCoin = response.data.Coin
          this.titleText = this.itemsCoin.title.text
          this.titleTextEng = this.itemsCoin.title.text_eng
          this.titleColor = this.itemsCoin.title.color
          this.titleCoinColor = this.itemsCoin.number_color
          this.handleSelectBGType(this.itemsCoin.bg_type)
          this.bgColor = this.itemsCoin.bg_color
          this.bgColor2 = this.itemsCoin.gradient_color
          this.bgImage = this.itemsCoin.bg_image
          this.btnText = this.itemsCoin.check_in.text
          this.btnTextEng = this.itemsCoin.check_in.text_eng
          this.btnTextColor = this.itemsCoin.check_in.color
          this.btnColor = this.itemsCoin.check_in.btn_color
          this.coinColor = this.itemsCoin.coin_color
          this.dayColor = this.itemsCoin.day_color
          this.lineColor = this.itemsCoin.line_color
          this.handleSelectCoinType(this.itemsCoin.coin_type)
          this.setDaysFromCoinDays('default')
          this.days.forEach(day => {
            if (day.id === 1) this.coinDay1 = day.coin
            else if (day.id === 2) this.coinDay2 = day.coin
            else if (day.id === 3) this.coinDay3 = day.coin
            else if (day.id === 4) this.coinDay4 = day.coin
            else if (day.id === 5) this.coinDay5 = day.coin
            else if (day.id === 6) this.coinDay6 = day.coin
            else if (day.id === 7) this.coinDay7 = day.coin
          })
          this.checkCoinImage = this.itemsCoin.image_check_coin
          this.coinImage = this.itemsCoin.image_coin
          this.lastCoinImage = this.itemsCoin.image_last_coin
          this.setDialogData()
        } else {
          // console.log('22')
          this.itemsCoin = []
          this.setDaysFromCoinDays('reset')
        }
        this.$store.commit('closeLoader')
        // if (response.data.length !== 0) {
        //   this.items = response.data[0].data
        // } else {
        //   this.items = []
        // }
        // this.indexDefault = this.items.length
        // console.log('this.items', this.items)
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async getDataBannerMobile () {
      const data = null
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsgetDataIconMobile', data)
      const response = this.$store.state.ModuleAdminManage.stategetDataIconMobile
      if (response.code === 200) {
        if (response.data.custom_icon_app.length !== 0) {
          this.itemsMobile = response.data.custom_icon_app
        } else {
          this.itemsMobile = []
        }
        this.indexDefault = this.itemsMobile.length
        if (response.data.banner.length !== 0) {
          this.data_banner_Mobile = response.data.banner.data_banner_1
          this.data_banner_Mobile.forEach(async (item) => {
            // console.log(item)
            await this.onInputTypeMobile(item.id, item.action_type)
            var actionID = item.action_id
            if (item.action_type === 'group' && actionID !== null) {
              // console.log('itemGroupIDMobile', this.itemGroupIDMobile)
              var itemsGroup = this.itemGroupIDMobile.filter(item => item.id === parseInt(actionID))
              // console.log('itemsGroup', itemsGroup)
              this.onInputIDMobile(item.id, itemsGroup[0])
            } else if (item.action_type === 'shop' && actionID !== null) {
              // console.log('itemShopIDMobile', this.itemShopIDMobile)
              var itemsShop = this.itemShopIDMobile.filter(item => item.id === parseInt(actionID))
              // console.log('itemsShop', itemsShop)
              this.onInputIDMobile(item.id, itemsShop[0])
            }
            // if (item.action_type === 'productFilter' && (item.action_filter !== '' || item.action_filter !== null)) {
            //   console.log('item.action_filter', item.action_filter)
            //   console.log('this.itemSelectProductFilter', this.itemSelectProductFilter)
            //   var actionFilter = item.action_filter
            //   var itemsfilter = this.itemSelectProductFilter.filter(item => item.value === actionFilter)
            //   console.log('itemsfilter', itemsfilter)
            //   this.handleSelectFilterMobile(itemsfilter[0].value)
            // }
          })
        } else {
          this.data_banner_Mobile = []
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async handleSelectBGType (val) {
      // console.log('val', val)
      this.bgType = val
    },
    async handleSelectCoinType (val) {
      // console.log('val', val)
      this.coinType = val
    },
    async onFileChangeBGImage (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.editData.bgImage = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]

            const icon = { image: [base64], type: 'custom_banner_new', layout: '0', type_layout: 'single', ratio: '' }
            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', icon)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.editData.bgImage = response.data.list_path[0].path
            }
          }
          reader.readAsDataURL(file)
          this.fileInputKey = Date.now()
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageBGImage () {
      this.editData.bgImage = ''
      this.fileInputKey = Date.now()
    },
    setDialogData () {
      this.backupData = {
        titleText: this.titleText,
        titleTextEng: this.titleTextEng,
        titleColor: this.titleColor,
        titleCoinColor: this.titleCoinColor,
        bgType: this.bgType,
        bgColor: this.bgColor,
        bgColor2: this.bgColor2,
        bgImage: this.bgImage,
        btnText: this.btnText,
        btnTextEng: this.btnTextEng,
        btnTextColor: this.btnTextColor,
        btnColor: this.btnColor,
        coinColor: this.coinColor,
        dayColor: this.dayColor,
        lineColor: this.lineColor,
        coinType: this.coinType,
        coinDay1: this.coinDay1,
        coinDay2: this.coinDay2,
        coinDay3: this.coinDay3,
        coinDay4: this.coinDay4,
        coinDay5: this.coinDay5,
        coinDay6: this.coinDay6,
        coinDay7: this.coinDay7,
        checkCoinImage: this.checkCoinImage,
        coinImage: this.coinImage,
        lastCoinImage: this.lastCoinImage
      }
      this.editData = { ...this.backupData }
    },
    async openDialogCustomCoin (status) {
      // console.log('status', status)
      this.setDialogData()
      // console.log('this.days', this.days)
      this.statusDialog = status
      switch (status) {
        case 'title':
          this.messageCustomCoin = 'แก้ไขส่วนของหัวเรื่อง'
          break
        case 'bg':
          this.messageCustomCoin = 'แก้ไขส่วนของพื้นหลัง'
          break
        case 'titleCoin':
          this.messageCustomCoin = 'แก้ไขสีอักษร'
          break
        case 'btn':
          this.messageCustomCoin = 'แก้ไขส่วนของปุ่ม'
          break
        case 'coin':
          this.messageCustomCoin = 'แก้ไขส่วนของ Coin'
          break
        default:
          this.messageCustomCoin = 'ปรับแต่ง'
      }
      this.dialogCustomCoin = true
    },
    closeDialogCustomCoin () {
      Object.assign(this, this.backupData)
      this.dialogCustomCoin = false
    },
    updateCustomCoin () {
      Object.assign(this, this.editData)
      if (this.statusDialog === 'coin') {
        this.setDaysFromCoinDays('update')
      }
      this.dialogCustomCoin = false
    },
    async onFileChangeCheckCoin (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.editData.checkCoinImage = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]

            const icon = { image: [base64], type: 'custom_icon' }

            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', icon)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.editData.checkCoinImage = response.data.list_path[0].path
            }
          }
          reader.readAsDataURL(file)
          this.fileInputKey = Date.now()
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageCheckCoin () {
      this.editData.checkCoinImage = ''
      this.fileInputKey = Date.now()
    },
    async onFileChangeCoin (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.editData.coinImage = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]

            const icon = { image: [base64], type: 'custom_icon' }

            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', icon)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.editData.coinImage = response.data.list_path[0].path
            }
          }
          reader.readAsDataURL(file)
          this.fileInputKey = Date.now()
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageCoin () {
      this.editData.coinImage = ''
      this.fileInputKey = Date.now()
    },
    async onFileChangeLastCoin (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.editData.lastCoinImage = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]

            const icon = { image: [base64], type: 'custom_icon' }

            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', icon)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.editData.lastCoinImage = response.data.list_path[0].path
            }
          }
          reader.readAsDataURL(file)
          this.fileInputKey = Date.now()
          this.$forceUpdate()
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLastCoin () {
      this.editData.lastCoinImage = ''
      this.fileInputKey = Date.now()
    },
    resetToDefault () {
      Object.assign(this, {
        titleText: 'หัวเรื่อง',
        titleTextEng: 'Title',
        titleColor: '#333333',
        titleCoinColor: '#333333',

        bgType: 'Color',
        bgColor: '#ffffff',
        bgColor2: '#ffffff',
        bgImage: '',

        btnText: 'ข้อความเช็คอิน',
        btnTextEng: 'Check In',
        btnTextColor: '#ffffff',
        btnColor: '#333333',

        coinColor: '#333333',
        dayColor: '#333333',
        lineColor: '#e0e0e0',

        coinType: 'Default',
        coinDay1: '',
        coinDay2: '',
        coinDay3: '',
        coinDay4: '',
        coinDay5: '',
        coinDay6: '',
        coinDay7: '',

        checkCoinImage: '',
        coinImage: '',
        lastCoinImage: ''
      })
      this.setDaysFromCoinDays('reset')
    }
  }
}
</script>

<style scoped>
.draggable-container {
  grid-template-columns: repeat(8, 100px);
  column-gap: 10px;
  row-gap: 30px;
}

.draggable-container-ipadPro {
  grid-template-columns: repeat(8, 70px);
  column-gap: 10px;
  row-gap: 20px;
}

.draggable-container-ipad {
  grid-template-columns: repeat(8, 46px);
  column-gap: 10px;
  row-gap: 20px;
}

.draggable-container-mobile {
  grid-template-columns: repeat(8, 36px);
  column-gap: 10px;
  row-gap: 20px;
}

.image-container {
  width: 100%;
  height: 150px;
  background: #e0e0e0;
  border-radius: 8px;
}

::v-deep .v-select__selections {
  line-height: normal;
}

</style>

<style lang="scss" scoped>
body {
  background: #f4f6fc;

  &:after {
    content: "Made with ❤️ by Adiel Hercules";
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    background-color: #3f436b;
    color: #fff;
    opacity: 0.6;
  }
}

.iphone-x {
  position: relative;
  margin: 40px auto;
  width: 360px;
  height: 780px;
   background-color: "#ffffff00";
  border-radius: 40px;
  box-shadow: 0px 0px 0px 11px #1f1f1f, 0px 0px 0px 13px #191919,
    0px 0px 0px 20px #111;

  &:before,
  &:after {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  // home button indicator
  &:after {
    bottom: 7px;
    width: 140px;
    height: 4px;
    background-color: #f2f2f2;
    border-radius: 10px;
  }

  // frontal camera/speaker frame
  &:before {
    top: 0px;
    width: 56%;
    height: 30px;
    background-color: #1f1f1f;
    border-radius: 0px 0px 40px 40px;
  }

  i,
  b,
  s,
  span {
    position: absolute;
    display: block;
    color: transparent;
  }

  // speaker
  i {
    top: 0px;
    left: 50%;
    transform: translate(-50%, 6px);
    height: 8px;
    width: 15%;
    background-color: #101010;
    border-radius: 8px;
    box-shadow: inset 0px -3px 3px 0px rgba(256, 256, 256, 0.2);
  }

  // camera
  b {
    left: 10%;
    top: 0px;
    transform: translate(180px, 4px);
    width: 12px;
    height: 12px;
    background-color: #101010;
    border-radius: 12px;
    box-shadow: inset 0px -3px 2px 0px rgba(256, 256, 256, 0.2);

    &:after {
      content: "";
      position: absolute;
      background-color: #2d4d76;
      width: 6px;
      height: 6px;
      top: 2px;
      left: 2px;
      top: 3px;
      left: 3px;
      display: block;
      border-radius: 4px;
      box-shadow: inset 0px -2px 2px rgba(0, 0, 0, 0.5);
    }
  }

  // time
  s {
    top: 50px;
    color: #fff;
    text-align: center;
    text-decoration: none;
    width: 100%;
    font-size: 70px;
    font-weight: 100;
    padding-top: 60px;
  }

  // action buttons
  span {
    bottom: 50px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    left: 30px;

    & + span {
      left: auto;
      right: 30px;
    }
  }
}
</style>
