<template>
  <div :class="MobileSize ? 'background_productMobile mt-2 pa-4' : 'background_product mt-0 pa-8'" style="background-color: #FFFFFF">
      <div>
        <v-row dense>
          <v-icon v-if="MobileSize" color="#27AB9C" class="" @click="canCel()">mdi-chevron-left</v-icon><span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">{{title}}โปรโมชันโค้ดส่วนลด</span>
        </v-row>
        <v-row dense class="mt-4">
          <span class="subTitle1" :style="MobileSize ? 'font-size: 16px;' : ''">ข้อมูลทั่วไป</span>
          <v-col cols="12" md="12">
            <v-card @click="onPickFile()" :disabled="img.length > 0" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;">
              <v-file-input
                v-model="DataImage"
                :items="DataImage"
                accept="image/jpeg, image/jpg, image/png"
                @click="event => event.target.value = null"
                @change="uploadImage()"
                id="file_input"
                :clearable="false"
                style="display:none">
              </v-file-input>
                <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                    <v-col cols="12" md="12" align="center">
                      <v-img
                        src="@/assets/icons/Upload.png"
                        width="280.34"
                        height="154.87"
                        contain
                      ></v-img>
                    </v-col>
                    <v-col cols="12" md="12" style="text-align: center;">
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                      <span style="line-height: 16px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                    </v-col>
                  </v-row>
                </v-col>
            </v-card>
          </v-col>

          <v-col cols="4" md="3" v-if="img.length > 0">
            <v-card width="136px" elevation="1" :class="MobileSize ? 'pa-2 mb-5' : 'pa-4 mb-5'">
              <v-btn icon small style="float: right; background-color: #ff5252;" v-if="!MobileSize">
                <v-icon small color="white" dark @click="removeImage()">mdi-close</v-icon>
              </v-btn>
              <v-btn icon x-small style="float: right; background-color: #ff5252;" v-else>
                <v-icon small color="white" dark @click="removeImage()">mdi-close</v-icon>
              </v-btn>
              <v-img :src="img[0].path" :lazy-src="img[0].path" width="136px" max-height="129px" contain class="mt-2">
              </v-img>
            </v-card>
          </v-col>
        </v-row>
      <v-form ref="FormManageDiscount">
        <v-row dense class="mt-4">
          <v-col cols="12"><span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">ข้อมูลโปรโมชัน</span></v-col>
          <!-- <v-col cols="12" class="pt-4">
            <span class="detail1">ต้องการใช้โปรโมชันอัตโนมัติโดยไม่ต้องกรอกโค้ดหรือไม่ <span style="color:#F5222D">*</span></span>
            <v-row no-gutters>
              <v-radio-group v-model="useCode" row>
                <v-radio label="ใช่" value="yes" ></v-radio>
                <v-radio label="ไม่ใช่" value="no" ></v-radio>
              </v-radio-group>
            </v-row>
          </v-col> -->
          <v-col cols="12" md="6">
            <span class="detail1">ชื่อคูปอง <span style="color:#F5222D">*</span></span>
            <v-text-field v-model="couponName" :rules="Rules.empty" placeholder="ระบุชื่อโปรโมชัน" @keypress="CheckSpacebar($event)" outlined dense></v-text-field>
          </v-col>
          <v-col cols="12" md="6">
            <span class="detail1">Sku คูปอง <span style="color:#F5222D">*</span></span>
            <v-text-field :maxLength="15" :rules="Rules.fourand15" :counter="15" :disabled="status === 'edit'" v-model="couponCode" placeholder="ระบุ SKU คูปอง" @keypress="CheckSpacebar($event)" oninput="this.value = this.value.replace(/^[-]/, '').replace(/[^a-z0-9]/gi, '')" outlined dense></v-text-field>
          </v-col>
          <v-col cols="12" class="pt-0">
            <span class="detail1">รายละเอียดโปรโมชัน</span>
            <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="couponDescription" @ready="onReady"></ckeditor>
            <span class="rule">* รายละเอียดโปรโมชัน วิธีใช้งานและสิทธิประโยชน์สำหรับสมาชิกอย่างละเอียด</span>
          </v-col>
        </v-row>
        <div class="mt-4">
          <!-- <span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">กำหนดจำนวนโปรโมชัน และสิทธิ์โปรโมชัน</span> -->
          <v-row dense class="mt-3">
            <!-- <v-col cols="12" md="6">
              <span class="detail1">ประเภทของส่วนลด</span>
              <v-text-field placeholder="โปรโมชันส่วนลดค่าสินค้า บริการ และค่าขนส่ง" outlined dense readonly disabled></v-text-field>
            </v-col> -->
            <v-col cols="12" md="6">
              <span class="detail1">จำนวนคูปองสูงสุด <span style="color:#F5222D">*</span></span>
              <v-text-field v-model="amonutUse" :rules="Rules.empty" :maxLength="7" placeholder="จำนวนคูปองสูงสุด" @input="checkZero('qouta')" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <span class="detail1">จำนวนคูปองที่ใช้ได้ต่อคน <span style="color:#F5222D">*</span></span>
              <v-text-field v-model="amonutCap" :maxLength="7" placeholder="จำนวนคูปองที่ใช้ได้ต่อคน" @input="checkZero('usecap')" :rules="Rules.amonutCap" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
          </v-row>
          <!-- <v-row dense>
            <v-col cols="12" md="4">
              <span class="detail1">สิทธิ์การใช้โปรโมชัน <span style="color:#F5222D">*</span></span>
              <v-radio-group v-model="usePremiss" dense hide-details class="detail1 ma-0">
                <v-radio label="1 คน ต่อ 1 สิทธิ์การใช้งาน" value="1"></v-radio>
                <v-radio label="ไม่จำกัดสิทธิ์การใช้" value="0"></v-radio>
                <v-radio label="กำหนดเอง" value=""></v-radio>
              </v-radio-group>
              <v-text-field v-model="usePremiss2" :rules="[itemRules.CheckCountPromotion(usePremiss2, amonutUse)]" v-if="usePremiss === ''" :maxLength="7" placeholder="ระบุจำนวนสิทธิ์การใช้โปรโมชัน" class="ml-8" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
          </v-row> -->
        </div>

        <div class="mt-4">
          <v-row class="">
            <v-col cols="12">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">กำหนดระยะเวลาเวาเชอร์</span>
            </v-col>
          </v-row>
          <div class="mt-4">
            <span class="detail1">ระยะเวลาเก็บเวาเชอร์ <span style="color:#F5222D">*</span></span>
            <v-row dense style="align-items: center;">
              <v-col cols="2" v-if="!MobileSize"><span class="detail1">วันที่เริ่ม - สิ้นสุด</span></v-col>
              <v-col cols="4" v-if="MobileSize"><span class="detail1">วันที่เริ่ม</span></v-col>
              <!-- collect start -->
              <div>
              <v-dialog
                ref="dialogStartDate1"
                v-model="dialogStartDate1"
                :return-value.sync="date11"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentStartDate1"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :disabled="disableddate11"
                    :rules="noEndDateCollect ? '' : Rules.empty"
                    @click="date21 = '', time21 = '', date22 = '', time22 = '', sentStartDate2 = '', sentEndDate2 = '', date12 = '', time12 = '', sentEndDate1 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date11"
                  scrollable
                  reactive
                  locale="TH-th"
                  :min="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  :max=date21
                  @change="time11 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-menu ref="menu1" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time11" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time11"
                          use-seconds
                          scrollable
                          :key="component"
                          :min="date11 == new Date().toISOString().substr(0, 10) ? new Date().toLocaleTimeString() : ''"
                          :max="date11 == date21 ? time21 : ''"
                          @click:second="$refs.menu1.save(time11)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogStartDate1 = false, date11 === '' ? time11 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date11 == '' || time11 == ''" @click="setValueDate(date11, 'date11'), $refs.dialogStartDate1.save(date11), date12 = '', sentEndDate1 = '', time12 = ''"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
              <span class="detail1 mx-4" v-if="!noEndDateCollect && !MobileSize"> - </span>
              <v-col cols="4" v-if="!noEndDateCollect && MobileSize"><span class="detail1">วันที่สิ้นสุด</span></v-col>
              <!-- collect end -->
              <div v-if="!noEndDateCollect">
              <v-dialog
                ref="dialogEndDate1"
                v-model="dialogEndDate1"
                :return-value.sync="date12"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentEndDate1"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    :disabled="sentStartDate1 === '' || disableddate12"
                    v-on="on"
                    :rules="noEndDateCollect ? '' : Rules.datesMustNotBeSame1"
                    @click="date21 = '', time21 = '', date22 = '', time22 = '', sentStartDate2 = '', sentEndDate2 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date12"
                  scrollable
                  reactive
                  locale="TH-th"
                  :min="date11 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time12 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-menu ref="menu2" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time12" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time12"
                          use-seconds
                          :key="component"
                          scrollable
                          :allowed-seconds="date12 == date11 ? allowedSeconds1 : ''"
                          :min="date12 == date11 ? time11 : ''"
                          @click:second="$refs.menu2.save(time12)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogEndDate1 = false, date12 === '' ? time12 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date12 == '' || time12 == ''" @click="setValueDate(date12, 'date12'), $refs.dialogEndDate1.save(date12)"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
            </v-row>
            <v-row class="mt-1">
              <v-col :cols="MobileSize? '5': '4'" class="pt-0">
                <v-checkbox v-model="noEndDateCollect" @click="noEnd('collect')" class="ma-0 pa-0" color="#27AB9C" label="ไม่ระบุวันสิ้นสุด"></v-checkbox>
              </v-col>
            </v-row>
          </div>

          <div>
            <span class="detail1">ระยะเวลาใช้งานเวาเชอร์<span style="color:#F5222D">*</span></span>
            <v-row dense style="align-items: center;">
              <v-col cols="2" v-if="!MobileSize"><span class="detail1">วันที่เริ่ม - สิ้นสุด</span></v-col>
              <v-col cols="4" v-if="MobileSize"><span class="detail1">วันที่เริ่ม</span></v-col>
              <!-- use start -->
              <div>
              <v-dialog
                ref="dialogStartDate2"
                v-model="dialogStartDate2"
                :return-value.sync="date21"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentStartDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :disabled="disableddate21"
                    :rules="Rules.empty"
                    @click="date22 = '', time22 = '', sentEndDate2 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date21"
                  scrollable
                  reactive
                  locale="TH-th"
                  :min="date11 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time21 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-menu ref="menu3" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time21" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time21"
                          scrollable
                          use-seconds
                          :key="component"
                          :min="date21 == new Date().toISOString().substr(0, 10) ? new Date().toLocaleTimeString() : date21 == date11 ? time11 : ''"
                          @click:second="$refs.menu3.save(time21)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogStartDate2 = false, date21 === '' ? time21 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date21 == '' || time21 == ''" @click="setValueDate(date21, 'date21'), $refs.dialogStartDate2.save(date21), date22 = '', sentEndDate2 = '', time22 = ''"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
              <span class="detail1 mx-4" v-if="!noEndDateUse && !MobileSize"> - </span>
              <v-col cols="4" v-if="!noEndDateUse && MobileSize"><span class="detail1">วันที่สิ้นสุด</span></v-col>
              <!-- use end -->
              <div v-if="!noEndDateUse">
              <v-dialog
                ref="dialogEndDate2"
                v-model="dialogEndDate2"
                :return-value.sync="date22"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentEndDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :disabled="sentStartDate2 === '' || disableddate22"
                    :rules="noEndDateUse ? '' : Rules.datesMustNotBeSame2"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date22"
                  scrollable
                  reactive
                  locale="TH-th"
                  :min="date21 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time22 = ''"
                >
                <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-menu ref="menu4" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time22" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time22"
                          use-seconds
                          :key="component"
                          scrollable
                          :allowed-seconds="date22 == date21 ? allowedSeconds2 : ''"
                          :min="date22 == date21 ? time21 : ''"
                          @click:second="$refs.menu4.save(time22)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogEndDate2 = false, date22 === '' ? time22 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date22 == '' || time22 == ''" @click="setValueDate(date22, 'date22'), $refs.dialogEndDate2.save(date22)"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
            </v-row>
            <v-row class="mt-1">
              <v-col :cols="MobileSize? '5': '4'" class="pt-0">
                <v-checkbox v-model="noEndDateUse"  @click="noEnd('use')" class="ma-0 pa-0" color="#27AB9C" label="ไม่ระบุวันสิ้นสุด"></v-checkbox>
              </v-col>
            </v-row>
          </div>
        </div>
        <!-- <pre>
          {{ date11 + time11 }}
          {{ date12 + time12 }}
          {{ date21 + time21 }}
          {{ date22 + time22 }}
        </pre> -->
        <div class="mt-6">
          <span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">การใช้โปรโมชัน</span>
          <!-- <v-row dense class="mt-3">
            <v-col cols="12" md="12">
              <span class="detail1">ต้องการใช้ร่วมกับโปรโมชันฟรีค่าจัดส่งหรือไม่ <span style="color:#F5222D">*</span></span>
              <v-radio-group v-model="multipleUse" row>
                <v-radio label="ใช้ร่วมกับโปรโมชันฟรีค่าจัดส่งได้" value="yes" ></v-radio>
                <v-radio label="ใช้ร่วมกับโปรโมชันฟรีค่าจัดส่งไม่ได้" value="no" ></v-radio>
              </v-radio-group>
            </v-col>
          </v-row> -->

          <v-row dense>
            <v-col cols="12">
              <span class="detail1">ค่าใช้จ่ายขั้นต่ำ <span style="color:#F5222D">*</span></span>
              <v-text-field v-model="spendMinimum" :key="component" :rules="Rules.spendminimumRule" placeholder="ระบุค่าใช้จ่ายขั้นต่ำในการใช้โปรโมชัน" @input="checkZero('minimum')" :maxLength="7" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"></v-text-field>
            </v-col>
          </v-row>

          <v-row dense>
            <v-col cols="12">
            <span class="detail1">ประเภทส่วนลด <span style="color:#F5222D">* (เลือกได้ 1 รูปแบบ)</span></span>
              <v-radio-group v-model="discountType" dense  class="detail1 ma-0">
                <v-col>
                  <v-row>
                    <v-radio label="ส่วนลดเป็นจำนวนเงิน" value="baht"  @click="discountAmountPercent = '', discount_maximum = null, discountMaximumType = 'haveCapNot'"></v-radio>
                    <v-text-field v-if="discountType === 'baht'" :rules="discountType === 'baht' ? Rules.minimumRule : ''" v-model="discountAmount" @input="checkZero(discountType)" suffix="บาท" class="ml-2" style="max-width: 250px;" outlined dense placeholder="ระบุส่วนลดที่ต้องการ" :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-row>
                </v-col>
                <v-col class="pa-0">
                  <v-row no-gutters>
                    <v-radio label="ส่วนลดเป็นเปอร์เซ็นต์ (%)" value="percent" @click="discountAmount = ''"></v-radio>
                    <v-text-field v-if="discountType === 'percent'" :rules="discountType === 'percent' ? Rules.empty : ''" :key="component" v-model="discountAmountPercent" @input="checkZero(discountType)" suffix="%" class="ml-2" style="max-width: 200px;" placeholder="ระบุส่วนลดที่ต้องการ" outlined dense :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-row>
                </v-col>
                <v-row align="center" v-if="discountType === 'percent'" no-gutters class="pl-6">
                  <v-radio-group v-model="discountMaximumType" dense v-if="discountType === 'percent'" class="detail1 ma-0">
                    <v-col class="pa-0">
                      <v-radio label="จำกัดจำนวนเงิน" value="haveCap"></v-radio>
                      <v-text-field v-if="discountMaximumType === 'haveCap'" :rules="discountMaximumType === 'haveCap' ? Rules.empty : ''" v-model="discount_maximum" placeholder="ระบุจำนวนเงิน" suffix="บาท" class="ml-2" style="max-width: 200px;" outlined dense :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col class="pa-0">
                      <v-radio label="ไม่จำกัดจำนวนเงิน" @click="discount_maximum = null" value="haveCapNot"></v-radio>
                    </v-col>
                  </v-radio-group>
                </v-row>
              </v-radio-group>
            </v-col>

            <v-col cols="12">
              <span class="detail1">สินค้าที่เข้าร่วม</span>
              <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                <treeselect v-model="productList" :defaultExpandLevel="1" @input="theRedP = true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :rules="Rules.empty" :options="dataListPrime" :normalizer="normalizer" :multiple="true" openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
              </div>
                <!-- <pre> -->
                <!-- {{ dataListPrime }} -->
                <!-- {{ productList }} -->
                <!-- {{ DataImage }} -->
                <!-- {{ date11 + ' ' + time11 }}
                {{ date12 + ' ' + time12 }}
                {{ date21 + ' ' + time21 }}
                {{ date22 + ' ' + time22 }} -->
              <!-- </pre> -->
            </v-col>
          </v-row>
        </div>

        <v-row :justify="MobileSize ? 'center' : 'end'" :align="MobileSize ? 'center' : 'end'" dense class="mt-10 mb-4">
          <v-col cols="12" md="12">
            <v-row :justify="MobileSize ? 'center' : 'end'">
              <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="canCel()">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="createCoupon()">ยืนยัน</v-btn>
              <!-- <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="test()">ยืนยัน</v-btn> -->
            </v-row>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <v-dialog v-model="dialogSuccess" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
        </v-img>
        <v-container>
          <v-card-text>
            <v-col>
              <v-row class="pb-2" style="justify-content: center;">
                <span style="font-size: 20px; font-weight: 600;">{{ title }}โปรโมชันส่วนลด</span>
              </v-row>
              <v-row style="justify-content: center;">
                <span style="font-size: 20px; font-weight: 600">"สำเร็จ"</span>
              </v-row>
            </v-col>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogFail" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
        </v-img>
        <v-container>
          <v-card-text>
            <v-col>
              <v-row justify="center" style="display: ruby-text;">
                <span style="font-size: 20px; font-weight: 600;">{{ dialogFailContext }}</span>
              </v-row>
            </v-col>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import { msgErr, statusErr } from '@/enum/GetError'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
export default {
  components: {
    Treeselect
  },
  data () {
    return {
      firstLoad: true,
      theRedP: true,
      dialogSuccess: false,
      dialogFail: false,
      dialogFailContext: '',
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'blockquote',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      },
      Rules: {
        amonutCap: [v => !!v || 'กรุณากรอกข้อมูล',
          v => (parseInt(v) <= parseInt(this.amonutUse)) || 'จำนวนคูปองที่ใช้ได้ต่อคนไม่ควรมากกว่าจำนวนคูปองสูงสุด'
        ],
        fourand15: [v => v.length >= 4 || 'กรุณากรอกข้อมูล'],
        datesMustNotBeSame1: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        datesMustNotBeSame2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate2 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        StartDate1MustNotBeSameStartDate2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'ระยะเวลาใช้งานและระยะเวลาเก็บ เวาเชอร์ไม่ควรเป็นวัน/เวลาเดียวกัน'
        ],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        minimumRule: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) < parseInt(this.spendMinimum) || 'กรุณากรอกราคาให้ไม่เกินค่าใช้จ่ายขั้นต่ำ'],
        spendminimumRule: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) > 0 || 'กรุณากรอกค่าใช้จ่ายขั้นต่ำให้มากกว่า 0']
      },
      img: [],
      DataImage: [],
      imgID: '',
      useCode: 'no',
      couponCode: '',
      couponName: '',
      couponIMG: '',
      couponDescription: '',
      amonutUse: '',
      amonutCap: '',
      usePremiss: '1',
      usePremiss2: '',
      dialogStartDate1: false,
      dialogEndDate1: false,
      dialogStartDate2: false,
      dialogEndDate2: false,
      date11: '',
      date12: '',
      date21: '',
      date22: '',
      sentStartDate1: '',
      sentEndDate1: '',
      sentStartDate2: '',
      sentEndDate2: '',
      time11: '',
      time12: '',
      time21: '',
      time22: '',
      noEndDateCollect: true,
      noEndDateUse: true,
      component: 0,
      seller_shop_id: '',
      dataListPrime: [],
      dataListPrime2: [],
      status: '',
      title: 'สร้าง',
      productList: [],
      productList2: [],
      normalizer (node) {
        let id
        let childrenKey
        let labelKey
        if (node.type === 'category') {
          id = 'hierachy'
          childrenKey = node.sub_category === null ? 'product_list' : 'sub_category'
          labelKey = 'category_name'
        } else if (node.type === 'product') {
          id = 'product_id'
          childrenKey = node.sub_product.length === 0 ? '' : 'sub_product'
          labelKey = 'product_name'
          // console.log(node.sub_product, 'sub_productsub_product')
        } else if (node.type === 'product_attribute') {
          id = 'product_attribute_id'
          labelKey = 'product_attribute_name'
        } else if (node.type === 'special_1_category') {
          id = 'all_id'
          childrenKey = 'all'
          labelKey = 'all_name'
        } else {
          id = 'product_id'
          childrenKey = 'product_list'
          labelKey = 'product_attribute_name'
        }
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey]
        }
      },
      selectedProduct: [],
      multipleUse: 'yes',

      spendMinimum: '',
      discountType: 'baht',
      discountAmount: '',
      discountMaximumType: 'haveCapNot',
      discount_maximum: null,
      discountAmountPercent: '',
      couponID: '',
      disableddate11: false,
      disableddate12: false,
      disableddate21: false,
      disableddate22: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    amonutUse (newVal, oldVal) {
      if (!this.firstLoad && newVal !== oldVal) {
        this.clearAmonutCap()
      } else {
        this.firstLoad = false
      }
    },
    // amonutUse () {
    //   if ((this.status !== 'edit')) {
    //     this.amonutCap = ''
    //   }
    // },
    discountAmountPercent (val) {
      // console.log('ส่วนลดเป็น%', val)
      if ((this.status !== 'edit') || (this.status === 'edit' && val === '')) {
        this.discount_maximum = null
      }
    },
    spendMinimum (val) {
      // console.log('ค่าใช้จ่ายขั้นต่ำ', val, this.status)
      if ((this.status !== 'edit') || (this.status === 'edit' && val === '')) {
        this.discount_maximum = null
        this.discountAmount = ''
        this.discountAmountPercent = ''
      }
    }
  },
  async created () {
    // console.log(this.dataMock, 'mockingBird')
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    this.status = this.$route.query.status
    this.getdata()
    if (this.status === 'edit') {
      this.couponID = this.$route.query.id
      this.title = 'แก้ไข'
      await this.getDataEdit()
    }
  },
  methods: {
    clearAmonutCap () {
      this.amonutCap = ''
    },
    canCel () {
      if (this.MobileSize) {
        this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageCoupon' }).catch(() => {})
      }
    },
    forceRerender () {
      this.component += 1
      // console.log(this.component)
    },
    checkZero (type) {
      if (type === 'qouta') {
        if (parseInt(this.amonutUse) === 0) {
          this.amonutUse = '1'
        }
      } else if (type === 'usecap') {
        if (parseInt(this.amonutCap) === 0) {
          this.amonutCap = '1'
        }
      } else if (type === 'minimum') {
        if (parseInt(this.spendMinimum) === 0) {
          this.spendMinimum = '1'
          this.forceRerender()
        }
      } else if (type === 'baht') {
        if (parseInt(this.discountAmount) === 0) {
          this.discountAmount = '0'
        }
      } else if (type === 'percent') {
        if (parseInt(this.discountAmountPercent) === 0) {
          this.discountAmountPercent = '1'
        } else if (parseInt(this.discountAmountPercent) > 99) {
          this.discountAmountPercent = '99'
          this.forceRerender()
        }
      }
    },
    onReady (editor) {
      editor.execute('heading', { value: 'heading2' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    allowedSeconds1 (v) {
      const getSecoinds = this.time11.split(':')
      const getSecoinds2 = this.time12.split(':')
      if (parseInt(getSecoinds[0]) === parseInt(getSecoinds2[0]) && parseInt(getSecoinds[1]) === parseInt(getSecoinds2[1])) {
        return v > parseInt(getSecoinds[2])
      } else {
        return true
      }
    },
    allowedSeconds2 (v) {
      const getSecoinds = this.time21.split(':')
      const getSecoinds2 = this.time22.split(':')
      if (parseInt(getSecoinds[0]) === parseInt(getSecoinds2[0]) && parseInt(getSecoinds[1]) === parseInt(getSecoinds2[1])) {
        return v > parseInt(getSecoinds[2])
      } else {
        return true
      }
    },
    async getdata () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionscategoryShopList', data)
      const res = await this.$store.state.ModuleManageCoupon.stateCategoryShopList
      // const dataNoService = res.data.filter(item => item.hierachy !== '1_259')
      this.dataListPrime.push({
        all_id: 'special_1',
        all_name: 'ทั้งหมด',
        all: res.data,
        type: 'special_1_category'
      })
      // this.dataListPrime.unshift({
      //   category_id: 'special_1',
      //   category_name: 'ทั้งหมด',
      //   type: 'special_1_category'
      // })

      // console.log(this.dataListPrime, 'dadata')
    },
    async getDataEdit () {
      // console.log('getDataEdit')
      var data = {
        seller_shop_id: this.seller_shop_id,
        coupon_id: this.couponID
      }
      // var nowday = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString()
      await this.$store.dispatch('actionsgetDetailCoupon', data)
      const res = await this.$store.state.ModuleManageCoupon.stategetDetailCoupon
      this.dataListPrime2 = res.data[0]
      // this.couponIMG = this.dataListPrime2.coupon_image
      this.couponName = this.dataListPrime2.coupon_name
      this.couponCode = this.dataListPrime2.coupon_code !== null ? this.dataListPrime2.coupon_code : ''
      this.couponDescription = this.dataListPrime2.coupon_description
      this.amonutUse = this.dataListPrime2.quota
      this.amonutCap = this.dataListPrime2.user_cap
      this.date11 = this.dataListPrime2.collect_startdate.slice(0, 10)
      this.time11 = this.dataListPrime2.collect_startdate.slice(11, 19)
      this.noEndDateCollect = this.dataListPrime2.collect_enddate === null
      this.date12 = this.dataListPrime2.collect_enddate === null ? '' : this.dataListPrime2.collect_enddate.slice(0, 10)
      this.time12 = this.dataListPrime2.collect_enddate === null ? '' : this.dataListPrime2.collect_enddate.slice(11, 19)
      this.date21 = this.dataListPrime2.use_startdate.slice(0, 10)
      this.time21 = this.dataListPrime2.use_startdate.slice(11, 19)
      this.noEndDateUse = this.dataListPrime2.use_enddate === null
      this.date22 = this.dataListPrime2.use_enddate === null ? '' : this.dataListPrime2.use_enddate.slice(0, 10)
      this.time22 = this.dataListPrime2.use_enddate === null ? '' : this.dataListPrime2.use_enddate.slice(11, 19)
      // console.log('resgetDataEdit', this.couponIMG)
      // this.disableddate11 = nowday > this.dataListPrime2.collect_startdate
      // this.disableddate12 = nowday > this.dataListPrime2.collect_enddate
      // this.disableddate21 = nowday > this.dataListPrime2.use_startdate
      // this.disableddate22 = nowday > this.dataListPrime2.use_enddate
      this.setValueDate(this.date11, 'date11')
      this.setValueDate(this.date12, 'date12')
      this.setValueDate(this.date21, 'date21')
      this.setValueDate(this.date22, 'date22')
      this.spendMinimum = this.dataListPrime2.spend_minimum
      this.discountType = this.dataListPrime2.discount_type
      if (this.discountType === 'baht') {
        this.discountAmount = this.dataListPrime2.discount_amount
      } else {
        this.discountAmountPercent = this.dataListPrime2.discount_amount
        if (this.dataListPrime2.discount_maximum !== null) {
          this.discountMaximumType = 'haveCap'
          this.discount_maximum = this.dataListPrime2.discount_maximum
        }
      }
      // console.log('this.discountType', this.discountType)
      // console.log('this.discountAmount', this.discountAmount)
      this.productList = JSON.parse(this.dataListPrime2.raw_list)
      // var b = JSON.parse(this.dataListPrime2.product_list)
      // if (this.dataListPrime2.raw_list === 'special_1') {
      //   this.productList = [this.dataListPrime2.raw_list]
      // } else {

      // }
      if (this.dataListPrime2.coupon_image !== null) {
        this.img.push({
          name: this.dataListPrime2.coupon_name,
          path: this.dataListPrime2.coupon_image,
          new: true
        })
      }
      this.couponIMG = this.dataListPrime2.coupon_image === null ? '' : this.dataListPrime2.coupon_image
      // console.log(this.dataListPrime2.coupon_image, 'this.img')
      // this.noEndDateUse = this.dataListPrime2.collect_enddate === '' || null
      // console.log(JSON.parse(this.dataListPrime2.product_list), 'dadataEdit')
    },
    // async test () {
    // },
    async createCoupon () {
      // console.log('createCoupon')
      // console.log(this.productList.length, typeof this.productList.length, 'productList.length')
      var myCateory = []
      var mySubCateory = []
      if (this.$refs.FormManageDiscount.validate(true) && this.productList.length !== 0) {
        if (this.productList[0] !== 'special_1') {
          this.productList.forEach(e => {
            this.dataListPrime[0].all.forEach(z => {
              if (z.hierachy === e) {
                myCateory.push(z.category_id)
                // this.productList2.push({ category_id: z.category_id, sub_category_id: '-1', product_id: '-1', product_attribute_id: '-1', hierachy: z.hierachy })
              } else {
                z.sub_category.forEach(x => {
                  if (x.hierachy === e) {
                    mySubCateory.push(x.category_id)
                    // this.productList2.push({ category_id: z.category_id, sub_category_id: x.category_id, product_id: '-1', product_attribute_id: '-1', hierachy: x.hierachy })
                  } else {
                    x.product_list.forEach(c => {
                      if (c.product_id === e) {
                        if (c.sub_product.length === 0) {
                          this.productList2.push({ product_id: c.product_id, product_attribute_id: '-1' })
                        } else {
                          c.sub_product.forEach(v => {
                            this.productList2.push({ product_id: v.product_id, product_attribute_id: v.product_attribute_id })
                          })
                        }
                        // this.productList2.push({ category_id: z.category_id, sub_category_id: x.category_id, product_id: c.product_id, product_attribute_id: '-1', hierachy: x.hierachy })
                      } else {
                        if (c.sub_product.length !== 0) {
                          c.sub_product.forEach(v => {
                            if (v.product_attribute_id === e) {
                              this.productList2.push({ product_id: v.product_id, product_attribute_id: v.product_attribute_id })
                              // this.productList2.push({ category_id: z.category_id, sub_category_id: x.category_id, product_id: c.product_id, product_attribute_id: v.product_attribute_id, hierachy: x.hierachy })
                            }
                          })
                        }
                      }
                    })
                  }
                })
              }
            })
          })
        } else {
          this.productList2 = []
          myCateory = []
          mySubCateory = []
        }
        // console.log(this.DataImage, '8902220')
        var formData = new FormData()
        formData.append('coupon_image', this.DataImage)
        formData.append('coupon_image_edit', this.couponIMG)
        formData.append('coupon_name', this.couponName)
        formData.append('coupon_code', this.couponCode)
        formData.append('coupon_description', this.couponDescription === null || this.couponDescription === 'null' ? '' : this.couponDescription)
        formData.append('collect_startdate', this.date11 + ' ' + this.time11)
        formData.append('collect_enddate', this.noEndDateCollect === true ? '' : this.date12 + ' ' + this.time12)
        formData.append('use_startdate', this.date21 + ' ' + this.time21)
        formData.append('use_enddate', this.noEndDateUse === true ? '' : this.date22 + ' ' + this.time22)
        formData.append('coupon_type', 'discount')
        formData.append('quota', this.amonutUse)
        formData.append('user_cap', this.amonutCap === '' || this.amonutCap === null ? '' : this.amonutCap)
        formData.append('spend_minimum', this.spendMinimum)
        formData.append('discount_type', this.discountType)
        formData.append('discount_amount', this.discountType === 'baht' ? this.discountAmount : this.discountAmountPercent)
        formData.append('product_list', JSON.stringify(this.productList2))
        formData.append('seller_shop_id', this.seller_shop_id)
        if (this.discountMaximumType === 'haveCap') {
          formData.append('discount_maximum', this.discount_maximum)
        }
        if (this.status === 'edit') {
          formData.append('id', this.couponID)
        }
        formData.append('cateory', JSON.stringify(myCateory))
        formData.append('sub_category', JSON.stringify(mySubCateory))
        // if (this.productList[0] === 'special_1') {

        // }
        formData.append('raw_list', JSON.stringify(this.productList))
        // console.log(formData, 'formData')
        if (this.status === 'edit') {
          // var res
          // console.log('บันทึกedit')
          // console.log(...formData.entries())
          await this.$store.dispatch('actionsEditCoupon', formData)
          var res = await this.$store.state.ModuleManageCoupon.stateEditCoupon
          // console.log('res', res)
          if (res.result === 'Success') {
            this.dialogSuccess = true
            setTimeout(() => {
              this.dialogSuccess = false
              if (this.MobileSize) {
                this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
              } else {
                this.$router.push({ path: '/manageCoupon' }).catch(() => {})
              }
            }, 2000)
          } else if (res.result === 'SKU duplicate') {
            this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 5000 })
          } else if (res.message === 'collectStartDate not over useStartDate') {
            this.$swal.fire({ icon: 'warning', text: 'ควรกำหนดช่วงเวลาในการจัดเก็บเวาเชอร์ให้เริ่มต้นก่อนช่วงเวลาในการใช้งานเวาเชอร์', showConfirmButton: false, timer: 5000 })
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาลองอีกครั้งในภายหลัง', showConfirmButton: false, timer: 1500 })
          }
        } else {
          await this.$store.dispatch('actionscreateCoupon', formData)
          res = await this.$store.state.ModuleManageCoupon.statecreateCoupon
          if (res.result === 'Success') {
            this.dialogSuccess = true
            setTimeout(() => {
              this.dialogSuccess = false
              if (this.MobileSize) {
                this.$router.push({ path: '/adminManageCouponMobile' }).catch(() => {})
              } else {
                this.$router.push({ path: '/adminManageCoupon' }).catch(() => {})
              }
            }, 2000)
          } else if (res.result === 'SKU duplicate') {
            this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 5000 })
          } else if (res.message === 'collectStartDate not over useStartDate') {
            this.$swal.fire({ icon: 'warning', text: 'ควรกำหนดช่วงเวลาในการจัดเก็บเวาเชอร์ให้เริ่มต้นก่อนช่วงเวลาในการใช้งานเวาเชอร์', showConfirmButton: false, timer: 5000 })
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาลองอีกครั้งในภายหลัง', showConfirmButton: false, timer: 1500 })
          }
        }
        // console.log(...formData.entries())
      } else {
        // console.log(parseInt(this.spendMinimum), '0120.020')
        if (this.couponName === '' || this.couponName === null) {
          this.openDialogFail('couponName')
        } else if (this.amonutUse === '' || this.amonutUse === null) {
          this.openDialogFail('amonutUse')
        } else if (this.date11 === '' && this.time11 === '') {
          this.openDialogFail('sentStartDate1')
        } else if (this.noEndDateCollect === false && (this.date12 === '' && this.time12 === '')) {
          this.openDialogFail('sentEndDate1')
        } else if (this.date21 === '' && this.time21 === '') {
          this.openDialogFail('sentStartDate2')
        } else if (this.noEndDateUse === false && (this.date22 === '' && this.time22 === '')) {
          this.openDialogFail('sentEndDate2')
        } else if (this.spendMinimum === '' || this.spendMinimum === null || parseInt(this.spendMinimum) <= 0) {
          this.openDialogFail('spendMinimum')
        } else if (this.discountType === 'baht' && (this.discountAmount === '' || this.discountAmount === null)) {
          this.openDialogFail('discountAmount')
        } else if (this.discountType === 'percent' && (this.discountAmountPercent === '' || this.discountAmountPercent === null)) {
          this.openDialogFail('discountAmountPercent')
        } else if (this.discountMaximumType === 'haveCap' && (this.discount_maximum === '' || this.discount_maximum === null)) {
          this.openDialogFail('discount_maximum')
        } else if (this.productList.length === 0) {
          this.openDialogFail('productList')
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 1500 })
        }
        if (this.productList.length === 0) {
          this.theRedP = false
        }
      }
    },
    openDialogFail (type) {
      if (type === 'couponName') {
        this.dialogFailContext = 'กรุณากรอกชื่อโปรโมชันให้ครบถ้วนและถูกต้อง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'amonutUse') {
        this.dialogFailContext = 'กรุณากรอกจำนวนคูปองสูงสุด'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentStartDate1') {
        this.dialogFailContext = 'กรุณากรอกวันที่เริ่มเก็บคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentEndDate1') {
        this.dialogFailContext = 'กรุณากรอกวันที่สิ้นสุดเก็บคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentStartDate2') {
        this.dialogFailContext = 'กรุณากรอกวันที่เริ่มใช้งานคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentEndDate2') {
        this.dialogFailContext = 'กรุณากรอกวันที่สิ้นสุดใช้งานคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'spendMinimum') {
        this.dialogFailContext = 'กรุณากรอกค่าใช้จ่ายขั้นต่ำ'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'discountAmount') {
        this.dialogFailContext = 'กรุณาตรวจสอบการกรอกส่วนลดเป็นจำนวนเงิน'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'discountAmountPercent') {
        this.dialogFailContext = 'กรุณาตรวจสอบการกรอกส่วนลดเป็นจำนวนเปอร์เซ็นต์'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'discount_maximum') {
        this.dialogFailContext = 'กรุณากรอกจำนวนจำกัดจำนวนเงินส่วนลดเป็นเปอร์เซ็นต์ครบถ้วนและถูกต้อง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'productList') {
        this.dialogFailContext = 'กรุณากรอกสินค้าที่เข้าร่วม'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      }
    },
    setValueDate (dateDay, proof) {
      if (!dateDay) return null
      const [year, month, day] = dateDay.split('-')
      const yearChange = parseInt(year) + 543
      if (proof === 'date11') {
        this.sentStartDate1 = `${day}/${month}/${yearChange}` + ' ' + this.time11
      } else if (proof === 'date12') {
        this.sentEndDate1 = `${day}/${month}/${yearChange}` + ' ' + this.time12
      } else if (proof === 'date21') {
        this.sentStartDate2 = `${day}/${month}/${yearChange}` + ' ' + this.time21
      } else if (proof === 'date22') {
        this.sentEndDate2 = `${day}/${month}/${yearChange}` + ' ' + this.time22
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return (`${day}/${month}/${year}`)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    uploadImage (e) {
      // console.log('uploadImage')
      // const element = e.target.files[0]
      const element = this.DataImage
      // console.log(element, 'DataImage')
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
        // ขนาดต้องไม่เกิน 2
        const imageSize = element.size / 1024 / 1024
        // ขนาดไม่เกิน 1480 * 620
        var url = URL.createObjectURL(element)
        var img = new Image()
        img.src = url
        img.onload = () => {
          if (imageSize < 2 && img.width <= 1480 && img.height <= 620) {
            // console.log('second')
            this.img.push({
              name: element.name,
              path: url,
              new: true
            })
          } else {
            // console.log('third')
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB หรือ มีขนาดน้อยกว่า 1480 * 620 px', showConfirmButton: false, timer: 1500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
      }
    },
    removeImage () {
      this.img = []
      this.imgID = ''
      this.couponIMG = '-1'
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    }

  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.title1 {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.subTitle1 {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.detail1 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.rule {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #C4C4C4;
}
</style>
