import AxiosETax from './axios_Etax_api'

const ModuleETax = {
  state: {
    stateCheckTaxIDShop: [],
    stateCheckEtaxInShop: [],
    stateSendAccessETax: [],
    stateSendAccessETaxR2: [],
    stateLoginBySharedTokenEfac: []
  },
  mutations: {
    nutationsCheckTaxIDShop (state, data) {
      state.stateCheckTaxIDShop = data
    },
    mutationsCheckETaxInShop (state, data) {
      state.stateCheckEtaxInShop = data
    },
    mutationsSendAccessETax (state, data) {
      state.stateSendAccessETax = data
    },
    mutationsSendAccessETaxR2 (state, data) {
      state.stateSendAccessETaxR2 = data
    },
    mutationsLoginBySharedTokenEfac (state, data) {
      state.stateLoginBySharedTokenEfac = data
    }
  },
  actions: {
    async actionsCheckTaxIDShop (context, access) {
      const responseData = await AxiosETax.CheckTaxIDShop(access)
      await context.commit('nutationsCheckTaxIDShop', responseData)
    },
    async actionsCheckETaxInShop (context, access) {
      const responseData = await AxiosETax.CheckETaxInShop(access)
      await context.commit('mutationsCheckETaxInShop', responseData)
    },
    async actionsSendAccessETax (context, access) {
      const responseData = await AxiosETax.SendAccessETax(access)
      await context.commit('mutationsSendAccessETax', responseData)
    },
    async actionsSendAccessETaxR2 (context, access) {
      const responseData = await AxiosETax.SendAccessETaxR2(access)
      await context.commit('mutationsSendAccessETaxR2', responseData)
    },
    async actionsLoginBySharedTokenEfac (context, access) {
      var response = await AxiosETax.LoginBySharedTokenEfac(access)
      await context.commit('mutationsLoginBySharedTokenEfac', response)
    }
  }
}
export default ModuleETax
