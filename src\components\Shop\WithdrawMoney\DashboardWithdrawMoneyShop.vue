<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <div class="px-2">
        <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
          <v-row no-gutters class="d-flex align-center">
            <v-col cols="8">
              <v-card-title class="pl-1" style="font-weight: 700; font-size: 22px; line-height: 32px;">
                <v-icon color="#27AB9C" class="mr-2" v-if="MobileSize" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>
                แดชบอร์ดระบบถอนเงิน
              </v-card-title>
            </v-col>
            <v-col cols="4" style="display: flex; justify-content: flex-end;">
              <v-btn rounded color="primary" height="40" @click="Exportwithdrawsummary()">
                <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
                <span class="exportButtonText ml-1">Export</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card>
        <v-card class="mb-4" style="border-radius: 8px;" elevation="0">
          <v-row>
            <v-col cols="4" class="mt-4">
              <v-avatar rounded size="30">
                <v-img contain :src="Icon1"></v-img>
              </v-avatar>
              <span class="subTitleText ml-2">ข้อมูลรายได้</span>
            </v-col>
            <v-col cols="8" class="mt-4">
                <div v-if="date" style="display: flex; justify-content: flex-end;">
                  <span style="font-size: 16px; font-weight: 500;">{{ date }}</span>
                </div>
            </v-col>
          </v-row>
        </v-card>
        <v-card class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
          <v-layout align-center justify-center>
            <v-flex>
              <v-card>
                <v-card-title>
                  <v-row>
                    <v-col cols="6">
                      <v-avatar rounded size="20">
                        <v-img contain :src="statisticsIconPath"></v-img>
                      </v-avatar>
                      <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">กราฟแสดงรายได้</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <v-avatar rounded size="27" class="mt-2">
                        <v-img contain :src="graphLineIconPath"></v-img>
                      </v-avatar>
                      <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">รายได้</span>
                    </v-col>
                  </v-row>
                </v-card-title>
                <v-card-text>
                  <apexchart height="400" type="line" :options="chartOptions" :series="chartSeries"></apexchart>
                </v-card-text>
              </v-card>
            </v-flex>
          </v-layout>
        </v-card>
        <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
          <v-row>
            <!-- ยอดขาย -->
            <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
              <v-card style="border-radius: 8px; background: #f5f5f5;" elevation="0" class="pa-2">
                <v-row>
                  <v-col cols="12">
                    <v-avatar rounded size="80">
                      <v-img contain :src="Icon2"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(this.summary.revenue).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ยอดขายปัจจุบัน <br/> (บาท)</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>

            <!-- จำนวนรายการสั่งซื้อทั้งหมด -->
            <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
              <v-card style="border-radius: 8px; background: #f5f5f5;" elevation="0" class="pa-2">
                <v-row>
                  <v-col cols="12">
                    <v-avatar rounded size="80">
                      <v-img contain :src="Icon3"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ this.summary.transactions ? Number(this.summary.transactions).toLocaleString(undefined) : '0' }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">จำนวนรายการสั่งซื้อ <br/> (รายการ)</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>

            <!-- ค่าธรรมเนียม -->
            <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
              <v-card style="border-radius: 8px; background: #f5f5f5;" elevation="0" class="pa-2">
                <v-row>
                  <v-col cols="12">
                    <v-avatar rounded size="80">
                      <v-img contain :src="Icon4"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(this.summary.fee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ค่าธรรมเนียม <br/> (บาท)</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>

            <!-- ยอดเงินสุทธิ -->
            <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
              <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
                <v-row>
                  <v-col cols="12">
                    <v-avatar rounded size="80">
                      <v-img contain :src="Icon6"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(this.summary.net_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12">
                  <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ยอดเงินสุทธิที่จะได้รับ <br/> (บาท)</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <!-- <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
              <v-card style="border-radius: 8px; background: #f5f5f5;" elevation="0" class="pa-2">
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333;">ยอดเงินสุทธิ</span>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">฿ {{ Number(this.summary.net_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                </v-row>
              </v-card>

              <v-row>
                <v-col cols="12" align="center" style="padding-top: 30px;">
                  <v-btn  rounded color="primary" height="50" width="200" @click="widthdrawMoney()" :disabled="this.summary.net_amount <= 99">ถอนเงิน</v-btn>
                  <br>
                  <br>
                  <span style="color: red;"><b>* อัตราค่าธรรมการถอนเงิน 5 บาท/ครั้ง</b></span>
                  <br>
                  <span style="color: red;"><b>* ถอนเงินขั้นต่ำ 100 บาท</b></span>
                </v-col>
              </v-row>
            </v-col> -->
          </v-row>
        </v-card>
        <br>
        <!-- รายการสั่งซื้อสินค้าทั้งหมด -->
        <v-card class="mb-2" style="border-radius: 8px;" elevation="0">
          <v-row>
            <v-col cols="12" md="8" sm="8">
              <v-avatar rounded size="24">
                <v-img contain :src="Icon5"></v-img>
              </v-avatar>
              <span class="subTitleText ml-2">รายการสั่งซื้อสินค้าทั้งหมด ({{ totalOrder }} รายการ)</span>
            </v-col>
            <v-col v-if="detailHistory.length !== 0" cols="12" md="4" sm="4" class="d-flex justify-end">
              <v-btn @click="Exportorderhistory()" height="40px" style="border-radius: 40px; background: #27AB9C">
                <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
                <span class="exportButtonText ml-1">Export</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card>
        <!-- Data table -->
        <v-card elevation="0">
          <v-data-table
            :headers="headers"
            :items="detailHistory"
            :items-per-page="5"
            class="elevation-1"
            item-key="i"
            no-data-text="ไม่พบรายการสั่งซื้อสินค้า"
          >
            <template v-slot:[`item.paid_datetime`]="{ item }">
              {{ new Date(item.paid_datetime).toLocaleDateString('th-TH', {
                timeZone: "UTC",
                year: 'numeric',
                month: 'long',
                day: 'numeric' })
              }}
            </template>
            <template v-slot:[`item.product_list`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  x-small
                  outlined
                  @click="openDialog(item)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.TxnAmount`]="{ item }">
              <span>{{ Number(item.TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.vat_price_payment`]="{ item }">
              <span>{{ Number(item.vat_price_payment).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.total_affiliate`]="{ item }">
              <span>{{ Number(item.total_affiliate).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.discount_coupon`]="{ item }">
              <span>{{ Number(item.discount_coupon).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.discount_ngc`]="{ item }">
              <span>{{ Number(item.discount_ngc).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.total_gp`]="{ item }">
              <span>{{ Number(item.total_gp).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.total_shipping`]="{ item }">
              <span>{{ Number(item.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.total_shop`]="{ item }">
              <span>{{ Number(item.total_shop).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
          </v-data-table>
        </v-card>
        <br>
        <v-card class="mb-2" style="border-radius: 8px;" elevation="0">
          <v-row>
            <v-col cols="12" md="9">
              <v-avatar rounded size="24">
                <v-img contain :src="Icon5"></v-img>
              </v-avatar>
              <span class="subTitleText ml-2">ประวัติการทำรายการ ({{ filterLabel }} - {{ filteredHistory.length }} รายการ)</span>
            </v-col>
            <v-col cols="12" md="3" class="d-flex justify-end">
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangeDate1"
                    v-bind="attrs"
                    v-on="on"
                    style="border-radius: 8px;"
                    outlined
                    dense
                    hide-details
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                  >
                    <v-icon slot="append" color="#CCCCCC">
                      mdi-calendar-multiselect
                    </v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  range
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <!-- <v-btn text color="red" @click="clearDateRange()">
                    ล้างค่า
                  </v-btn> -->
                  <v-btn text color="red" @click="CloseModalRangeDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn text color="primary" @click="setValueRangeDate(dateRange)">
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
        </v-card>
        <br>
        <div style="padding-bottom: 25px;">
          <v-row justify="center" align="center">
            <v-col cols="12" sm="4" md="3">
              <v-btn class="white--text" color="#27AB9C" height="40px" width="100%" style="border-radius: 40px;" @click="filterAll()">ทั้งหมด</v-btn>
            </v-col>
            <v-col cols="12" sm="4" md="3">
              <v-btn class="white--text" color="#D1392B" height="40px" width="100%" style="border-radius: 40px;" @click="filterOutgoing()">รายการเงินออก</v-btn>
            </v-col>
            <v-col cols="12" sm="4" md="3">
              <v-btn class="white--text" color="#1AB759" height="40px" width="100%" style="border-radius: 40px;" @click="filterIncoming()">รายการเงินเข้า</v-btn>
            </v-col>
          </v-row>
        </div>
        <!-- Data table -->
        <v-card elevation="0">
          <v-data-table
            :headers="headersWithdraw"
            :items="filteredHistory"
            :items-per-page="5"
            class="custom-data-table elevation-1"
            item-key="i"
            no-data-text="ไม่พบประวัติการทำรายการ"
          >
          <template v-slot:[`item.payment_datetime`]="{ item }">
            {{ new Date(item.payment_datetime).toLocaleDateString('th-TH', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: 'numeric',
              minute: 'numeric',
              second: 'numeric' })
            }}
          </template>
            <template v-slot:[`item.transaction_status`]="{ item }">
              <span v-if="item.transaction_status === 'Success'">
                <v-chip class="ma-2" color="#E8F5E9" text-color="#388E3C">สำเร็จ</v-chip>
              </span>
              <span v-if="item.transaction_status === 'Waiting'">
                <v-chip class="ma-2" color="#E1F5FE" text-color="#0288D1">อยู่ระหว่างดำเนินการ</v-chip>
              </span>
              <span v-if="item.transaction_status === 'Fail'">
                <v-chip class="ma-2" color="#FFFDE7" text-color="#FBC02D">ผิดพลาด</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Not Paid'">
                <v-chip class="ma-2" color="#FFF8E1" text-color="#F57C00">ไม่มีการชำระเงิน</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Cancel'">
                <v-chip class="ma-2" color="#FFEBEE" text-color="#D32F2F">ยกเลิก</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Waiting_Cancel'">
                <v-chip class="ma-2" color="#FFEBEE" text-color="#D32F2F">ยกเลิก</v-chip>
              </span>
            </template>
            <template v-slot:[`item.debit`]="{ item }">
              <span>{{ Number(item.debit).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.credit`]="{ item }">
              <span>{{ Number(item.credit).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.payment_fee`]="{ item }">
              <span>{{ Number(item.payment_fee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
          </v-data-table>
          <v-row style="display: flex; justify-content: flex-end; padding: 20px;">
            <span v-if="MobileSize" style="color: red; font-size: 13px;"><b>* หมายเหตุ รายการถอนเงินจะถูกโอนเข้าบัญชีของท่านภายใน 24 ชั่วโมง</b></span>
            <span v-else style="color: red; font-size: 16px;"><b>* หมายเหตุ รายการถอนเงินจะถูกโอนเข้าบัญชีของท่านภายใน 24 ชั่วโมง</b></span>
          </v-row>
        </v-card>
      </div>
    </v-card>

    <v-dialog
      v-model="dialog_detail"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">รายการสินค้า</font>
          </span>
          <v-btn icon dark @click="CloseDialog('readonly')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pt-3" style="text-align: center;">
                <span style="color:#333333; font-size: 16px; font-weight: 600;">รายการสินค้าทั้งหมด {{ Number( order_Detail.length ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น</span>
              </v-col>
            </v-row>
        </v-card-text>
        <v-card-text v-bind:style="{'height' : '400px'}">
          <v-container>
            <v-row v-for="(item, index) in order_Detail" :key="index" class="d-flex flex-column flex-md-row">
              <v-col cols="12">
                <v-card class="mt-4">
                  <v-row>
                    <v-col cols="12" md="4" align="center">
                      <v-avatar tile size="150">
                        <div v-if="item.product_image !== ''">
                          <img :src="item.product_image" alt="Product Image" class="avatar-image" />
                        </div>
                        <div v-else>
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </div>
                      </v-avatar>
                    </v-col>
                    <v-col cols="12" md="8">
                      <v-card-title class="no-word-break">{{ item.product_name }}</v-card-title>
                      <v-card-text>
                        <div><b>Product ID:</b> {{ item.product_id }}</div>
                        <div><b>Main SKU:</b> {{ item.main_sku }}</div>
                        <div><b>Price:</b> {{ Number( item.revenue_default ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</div>
                        <div><b>รูปแบบการชำระเงิน:</b> {{ typePayment }}</div>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="dialogwidthdrawMoney"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <v-btn icon dark @click="CloseDialogWithdraw('readonly')">
            <v-icon color="#27AB9C">mdi-arrow-left</v-icon>
          </v-btn>
          <span
            class="flex text-center"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">รายละเอียดการถอนเงิน</font>
          </span>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pl-7 pt-4 text-center">
                <span style="color:#27AB9C; font-size: 30px; font-weight: 600;">{{ Number(this.summary.net_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col cols="8">
                <span>ค่าธรรมเนียม e-payment (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.total_vat_price_payment).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="8">
                <span>ค่าธรรมเนียม GP (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.total_gp).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="8">
                <span>คูปองส่วนลด/แต้มส่วนลด (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.total_discount_coupon).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="8">
                <span>ส่วนลดของระบบ (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.total_discount_ngc).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="8">
                <span>ค่าธรรมเนียม affiliate (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.total_affiliate).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="8">
                <span>ค่าธรรมเนียมขนส่ง (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col cols="8">
                <span style="color: red;">ค่าธรรมเนียมการถอนเงิน (บาท)</span>
              </v-col>
              <v-col cols="4" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.withdraw_fee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col cols="9">
                <span style="font-size: 16px"><b>ยอดเงินที่ถอนเข้าบัญชีธนาคาร (บาท)</b></span>
              </v-col>
              <v-col cols="3" style="text-align: end;">
                <span>{{ Number(this.withdrawDetail.final_revenue).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
            </v-row>
            <v-row>
            <v-col cols="12" align="center">
              <v-btn rounded color="primary" height="50" @click="confirmwidthdrawMoney()">ทำการถอนเงิน</v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogConfirmwithdraw" persistent max-width="550" style="overflow: hidden;">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">ยืนยันการถอนเงิน</font>
        </span>
        <v-btn icon dark @click="CloseDialogConfirm()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card width="100%" style="overflow: hidden;" align="center">
        <v-card-text class="pt-6">
          <span v-if="MobileSize" style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">ยอดเงินที่จะได้รับ <b>{{ Number(this.withdrawDetail.final_revenue).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b> บาท</span>
          <span v-else style="white-space: normal; display: inline-block; word-break:break-word; font-size: 18px">ยอดเงินที่จะได้รับ <b>{{ Number(this.withdrawDetail.final_revenue).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b> บาท</span>
        </v-card-text>
        <v-card-text>
          <span v-if="MobileSize" style="white-space: normal; display: inline-block; word-break:break-word; font-size: 14px">กรุณาตรวจสอบความถูกต้อง ก่อนยืนยันการถอนเงิน</span>
          <span v-else style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">กรุณาตรวจสอบความถูกต้อง ก่อนยืนยันการถอนเงิน</span>
          <span v-if="MobileSize" style="color: red; white-space: normal; display: inline-block; word-break:break-word; font-size: 14px">รายการถอนเงินจะถูกโอนเข้าบัญชีของท่านภายใน 24 ชั่วโมง</span>
          <span v-else style="color: red; white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">รายการถอนเงินจะถูกโอนเข้าบัญชีของท่านภายใน 24 ชั่วโมง</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="CloseDialogConfirm('readonly')">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="successWithdrawMoney()">ตกลง</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import VueApexCharts from 'vue-apexcharts'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      dialog_detail: false,
      dialogwidthdrawMoney: false,
      dialogConfirmwithdraw: false,
      xAxis: '',
      date: '',
      startDate: new Date().getFullYear(),
      endDate: new Date().getFullYear(),
      shopID: '',
      dateFilter: 'year',
      options: ['รายวัน'],
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      graphLineIconPath: require('@/assets/icons/SellerDashboard/graph-line 1.png'),
      Icon1: require('@/assets/WDShop1.png'),
      Icon2: require('@/assets/WDShop2.png'),
      Icon3: require('@/assets/WDShop3.png'),
      Icon4: require('@/assets/WDShop4.png'),
      Icon5: require('@/assets/WDShop5.png'),
      Icon6: require('@/assets/icon6.png'),
      chartSeries: [],
      chartOptions: {
        chart: {
          id: 'income-difference-chart',
          // stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          colors: ['#008FFB', '#FF4560']
        },
        markers: {
          size: 5, // Adjust the size of markers as per your preference
          colors: '#fff',
          strokeColors: '#AE8FF7',
          strokeWidth: 2,
          strokeOpacity: 0.9,
          strokeDashArray: 0,
          fillOpacity: 0,
          discrete: [],
          shape: 'circle',
          radius: 2,
          offsetX: 0,
          offsetY: 0,
          onClick: undefined,
          onDblClick: undefined,
          showNullDataPoints: true,
          hover: {
            size: undefined,
            sizeOffset: 6
          }
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      },
      totalOrder: '',
      totalHistory: '',
      order_Detail: [],
      detailHistory: [],
      TransactionHistory: [],
      filteredHistory: [],
      filterLabel: 'ทั้งหมด',
      typePayment: '',
      headers: [
        { text: 'วันที่ทำรายการ', width: '160', align: 'center', sortable: true, value: 'paid_datetime', class: 'backgroundTable fontTable--text' },
        { text: 'รายชื่อลูกค้า', width: '200', align: 'center', sortable: false, value: 'buyer_name', class: 'backgroundTable fontTable--text' },
        { text: 'เลขที่ทำรายการสั่งซื้อ', width: '160', sortable: false, align: 'center', value: 'order_number', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา (บาท)', width: '100', sortable: false, align: 'center', value: 'TxnAmount', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าธรรมเนียม (บาท)', width: '150', sortable: false, align: 'center', value: 'vat_price_payment', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชั่น/Affiliate (บาท)', width: '200', sortable: false, align: 'center', value: 'total_affiliate', class: 'backgroundTable fontTable--text' },
        { text: 'คูปองส่วนลด/แต้มส่วนลด (บาท)', width: '220', align: 'center', sortable: false, value: 'discount_coupon', class: 'backgroundTable fontTable--text' },
        { text: 'ส่วนลดของระบบ (บาท)', width: '170', align: 'center', sortable: false, value: 'discount_ngc', class: 'backgroundTable fontTable--text' },
        { text: 'ค่า GP (บาท)', width: '120', sortable: false, align: 'center', value: 'total_gp', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าขนส่ง (บาท)', width: '120', sortable: false, align: 'center', value: 'total_shipping', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนเงินที่ได้รับ (บาท)', width: '170', sortable: false, align: 'center', value: 'total_shop', class: 'backgroundTable fontTable--text' },
        { text: 'รายการสินค้า', width: '120', sortable: false, align: 'center', value: 'product_list', class: 'backgroundTable fontTable--text' }
      ],
      headersWithdraw: [
        { text: 'วันที่และเวลาการชำระเงิน', width: '170', align: 'center', sortable: true, value: 'payment_datetime', class: 'backgroundTable fontTable--text' },
        { text: 'เลขที่ทำรายการสั่งซื้อ', width: '160', align: 'center', sortable: false, value: 'order_number', class: 'backgroundTable fontTable--text' },
        { text: '+ รายการเงินเข้า (บาท)', width: '140', sortable: false, align: 'center', value: 'debit', class: 'backgroundTable fontTableDebit--text' },
        { text: '- รายการเงินออก (บาท)', width: '140', sortable: false, align: 'center', value: 'credit', class: 'backgroundTable fontTableCredit--text' },
        { text: 'ค่าธรรมเนียม (บาท)', width: '100', sortable: false, align: 'center', value: 'payment_fee', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', width: '100', sortable: false, align: 'center', value: 'transaction_status', class: 'backgroundTable fontTable--text' }
      ],
      summary: [],
      withdrawSummary: [],
      withdrawDetail: [],
      modalRangeDate: false,
      dateRange: [],
      start_date: '',
      end_date: '',
      RangeDate1: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    if (localStorage.getItem('shopSellerID') === '' || localStorage.getItem('shopSellerID') === null || localStorage.getItem('shopSellerID') === undefined) {
      this.$router.push({ path: '/' })
    } else {
      this.shopID = localStorage.getItem('shopSellerID')
    }
    this.orderHistory()
    this.withdrawsummary()
    this.withdrawdetail()
    this.transactionHistory()
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    openDialog (item) {
      this.typePayment = ''
      this.dialog_detail = true
      this.order_Detail = item.product_list
      this.typePayment = item.type_payment
    },
    CloseDialog (val) {
      this.type = val
      this.dialog_detail = false
      this.dialogwidthdrawMoney = false
      this.dialogConfirmwithdraw = false
    },
    CloseDialogConfirm (val) {
      this.type = val
      this.dialogConfirmwithdraw = false
    },
    widthdrawMoney () {
      this.dialogwidthdrawMoney = true
    },
    openDialogWithdraw (item) {
      this.typePayment = ''
    },
    CloseDialogWithdraw (val) {
      this.type = val
      this.dialogwidthdrawMoney = false
    },
    confirmwidthdrawMoney () {
      this.dialogConfirmwithdraw = true
    },
    async successWithdrawMoney () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.shopID
      }

      await this.$store.dispatch('actionstransferShopClick', data)
      const response = await this.$store.state.ModuleShop.statetransferShopClick
      // console.log('withdraw', response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'แจ้งถอนเงินสำเร็จ',
          text: 'ระบบดำเนินการสั่งโอนเรียบร้อย จะได้รับภายใน 24 ชม. หลังจากการกดถอนครั้งนี้'
        })
        await this.withdrawsummary()
        await this.transactionHistory()
        this.dialogwidthdrawMoney = false
        this.dialogConfirmwithdraw = false
      } else if (response.result === 'FAILED') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
        this.dialogwidthdrawMoney = false
        this.dialogConfirmwithdraw = false
      }
    },
    async CloseModalRangeDate () {
      this.dateRange = null
      this.RangeDate1 = ''
      this.start_date = ''
      this.end_date = ''
      await this.transactionHistory()
      this.modalRangeDate = false
    },
    // async clearDateRange () {
    //   this.dateRange = null
    //   this.RangeDate1 = ''
    //   this.start_date = ''
    //   this.end_date = ''
    //   await this.transactionHistory()
    //   this.modalRangeDate = false
    // },
    async setValueRangeDate (val) {
      if (val && val.length === 2) {
        const [start, end] = [...val].sort((a, b) => new Date(a) - new Date(b))
        this.start_date = new Date(start).toISOString().split('T')[0]
        this.end_date = new Date(end).toISOString().split('T')[0]
        this.RangeDate1 = `${this.formatDate(start)} - ${this.formatDate(end)}`
      } else if (val && val.length === 1) {
        const date = new Date(val[0]).toISOString().split('T')[0]
        this.start_date = date
        this.end_date = date
        this.RangeDate1 = `${this.formatDate(val[0])} - ${this.formatDate(val[0])}`
      }
      this.modalRangeDate = false
      await this.transactionHistory()
    },
    formatDate (date) {
      const options = { year: 'numeric', month: '2-digit', day: '2-digit' }
      return new Date(date).toLocaleDateString('th-TH', options)
    },
    async transactionHistory () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        start_date: this.start_date,
        end_date: this.end_date
      }
      await this.$store.dispatch('actionstransactionHistory', data)
      var response = await this.$store.state.ModuleShop.statetransactionHistory
      if (response.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.$store.commit('closeLoader')
        this.TransactionHistory = response.data
        this.debit = response.data.filter(item => item.debit > 0)
        this.credit = response.data.filter(item => item.credit > 0)
        this.totalHistory = response.data.length
        this.filteredHistory = this.TransactionHistory
      }
    },
    filterAll () {
      this.filteredHistory = this.TransactionHistory
      this.filterLabel = 'ทั้งหมด'
    },
    filterOutgoing () {
      this.filteredHistory = this.credit
      this.filterLabel = 'รายการเงินออก'
    },
    filterIncoming () {
      this.filteredHistory = this.debit
      this.filterLabel = 'รายการเงินเข้า'
    },
    async orderHistory () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }

      await this.$store.dispatch('actionsDashboardWDshopOrderHistory', data)
      var response = await this.$store.state.ModuleShop.stateDashboardWDshopOrderHistory
      if (response.message === 'เรียกดูออเดอร์สำเร็จ') {
        this.$store.commit('closeLoader')
        this.detailHistory = response.data
        this.totalOrder = response.data.length
        var count = 1
        this.detailHistory.forEach((item) => {
          item.i = count
          count = count + 1
        })
        // console.log(this.typePayment)
      }
    },
    async withdrawsummary () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('actionsWithdrawSummary', data)
      var response = await this.$store.state.ModuleShop.stateWithdrawSummary
      if (response.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.$store.commit('closeLoader')
        var dataTable = []
        var dates = []

        for (let i = 0; i < response.data.withdraw_summary.length; i++) {
          const date = new Date(response.data.withdraw_summary[i].order_date)
          const formattedDate = new Intl.DateTimeFormat('th-TH', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }).format(date)

          dataTable.push({
            x: `${formattedDate}`, // แสดงวันที่พร้อมวันในสัปดาห์
            y: response.data.withdraw_summary[i].revenue.toFixed(2)
          })
          dates.push(date)
        }

        // ตั้งค่า chartSeries โดยรวมวันที่และรายได้
        this.chartSeries = [{ name: 'รายได้', data: dataTable }]

        // เปลี่ยนแปลงตัวเลือกกราฟโดยใช้วันที่จาก dataTable
        this.ChangeChartOptions(dataTable.map(item => item.x))

        // จัดเก็บช่วงเวลาวันที่สำหรับการแสดง
        const minDate = new Intl.DateTimeFormat('th-TH', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }).format(Math.min(...dates))

        const maxDate = new Intl.DateTimeFormat('th-TH', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }).format(Math.max(...dates))

        this.date = `${minDate} - ${maxDate}`

        this.summary = response.data.summary
        this.withdrawSummary = response.data.withdraw_summary
      }
    },
    async withdrawdetail () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('actionsWithdrawDetails', data)
      var response = await this.$store.state.ModuleShop.stateWithdrawDetails
      if (response.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.$store.commit('closeLoader')
        this.withdrawDetail = response.data[0]
        // console.log('detail', this.withdrawDetail)
      }
    },
    ChangeChartOptions (val) {
      this.chartOptions = {
        chart: {
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          markers: {
            size: 4, // Adjust the size of markers as per your preference
            colors: undefined,
            strokeColors: '#fff',
            strokeWidth: 2,
            strokeOpacity: 0.9,
            strokeDashArray: 0,
            fillOpacity: 1,
            discrete: [],
            shape: 'circle',
            radius: 2,
            offsetX: 0,
            offsetY: 0,
            onClick: undefined,
            onDblClick: undefined,
            showNullDataPoints: true,
            hover: {
              // size: undefined,
              sizeOffset: 6
            }
          }
        },
        xaxis: {
          categories: ['เสาร์', 'อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      }
    },
    async Exportwithdrawsummary () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        seller_shop_id: this.shopID
      }

      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/export_withdraw_summary`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'ข้อมูลรายได้.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function (error) {
        console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async Exportorderhistory () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        seller_shop_id: this.shopID
      }

      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/export_order_history`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'ข้อมูลรายได้.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function (error) {
        console.log(error)
        this.$store.commit('closeLoader')
      })
    }
  }
}
</script>

<style scoped>
.two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
}
.one-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.custom-text-field {
  height: 37px;
  width: 280px;
}
.custom-chip {
  overflow: visible !important;
}
.no-word-break {
  word-break: normal;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.backgroundSellerDashboard {
  max-width: 100% !important;
  background: #F7FCFC;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C
}
.exportButtonText {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF
}
.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
</style>

<style scoped>
.v-btn__content {
    font-size: 16px;
}
@media (max-width: 600px) {
  ::v-deep .custom-data-table .v-data-table__mobile-table-row td:nth-child(3) .v-data-table__mobile-row__header {
    color: green;
  }
  ::v-deep .custom-data-table .v-data-table__mobile-table-row td:nth-child(4) .v-data-table__mobile-row__header {
    color: red;
  }
}
</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(12) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(12) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
  ::v-deep .custom-data-table table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
