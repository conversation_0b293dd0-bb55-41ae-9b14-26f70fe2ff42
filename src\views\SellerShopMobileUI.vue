<template>
  <v-container>
    <!-- Mobile -->
    <v-row class="mb-6" v-if="MobileSize">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card class="mt-6" max-height="100%" height="100%" style="border-radius: 8px; border: 1px solid #F2F2F2;">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-home</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; font-size: 16px !important; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <div v-if="items.length > 1">
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in items" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.key !== 1">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </div>
          </v-list>
          <div v-if="itemsMembers.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsMembers" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการข้อมูลสมาชิก'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsShopAccount.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsShopAccount" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการบัญชี'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsLiveStreaming.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsLiveStreaming" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการ Live Streaming'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsManageTag.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsManageTag" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการแท็ก'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsCompany.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsCompany" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'บริษัทคู่ค้า'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsSalesOrder.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsSalesOrder" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'Sales Order'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsPromotion.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsPromotion" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการโปรโมชัน'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsAffiliate.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsAffiliate" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'โปรแกรม Affiliate'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsPartner.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsPartner" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'เชื่อมต่อ Partner'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
          <div v-if="itemsMarketplace.length !== 0">
            <v-list nav>
              <v-list-item-group color="#27AB9C">
                <v-list-item v-for="(item, i) in itemsMarketplace" :key="i" :disabled="item.disable" @click="changePage(item.path)">
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'Software Marketplace'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <!-- <v-col cols="12" md="9">
        <v-main style="padding: 0px;">
          <v-container>
            <v-card max-height="100%" height="100%" width="100%" class="mt-3">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col> -->
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import axios from 'axios'
export default {
  // components: {
  //   Footer: () => import('@/components/Home/Footer')
  // },
  data () {
    return {
      imgLogoShop: '',
      isJV: '',
      shopname: '',
      ImgShop: '',
      activeMenu: true,
      dataBreadcrumb: [
        {
          name: 'หน้าหลัก',
          path: 'selleI'
        },
        {
          name: 'รายการสินค้า',
          path: 'seller'
        }
      ],
      BtnLink: [
        { name: 'รายละเอียดร้านค้า', icon: 'idcard', path: 'shop' },
        { name: 'ออกจากระบบ', icon: 'export', path: '' }
      ],
      defaultSelect: 0,
      itemsCompany: [
        {
          key: 2,
          action: 'mdi-domain',
          active: false,
          title: 'บริษัทคู่ค้า',
          items: [
          ]
        }
      ],
      items: [
        { key: 1, action: 'mdi-storefront', title: 'จัดการร้านค้า', disable: true },
        { key: 4, action: '', title: 'รายการสินค้า', path: 'sellerShopMobile', disable: false },
        { key: 5, action: '', title: 'ดูรายการสั่งซื้อ', path: 'posellerMobile', disable: false },
        { key: 6, action: '', title: 'เรียกพนักงานรับพัสดุ', path: 'CurierMobile', disable: false },
        { key: 11, action: '', title: 'สต๊อกสินค้า', path: 'inventoryMobile', disable: false },
        { key: 12, action: '', title: 'จัดการร้านค้า', path: 'designShopMobile', disable: false },
        { key: 13, action: '', title: 'จัดการขนส่ง', path: 'manageShippingSellerMobile', disable: false },
        { key: 14, action: '', title: 'จัดการหมวดหมู่สินค้า', path: 'ManageCategoryProductMobile', disable: false },
        { key: 15, action: '', title: 'รายการคืนสินค้า', path: 'returnMobile', disable: false },
        { key: 16, action: '', title: 'แดชบอร์ด', path: 'dashboardMobile', disable: false },
        { key: 17, action: '', title: 'เอกสารและคู่มือการใช้งาน', path: 'DownloadFilesMobile', disable: false },
        { key: 18, action: '', title: 'รายได้ของฉัน', path: 'revenueMobile', disable: false },
        { key: 19, action: '', title: 'รายการเอกสารขอเป็นคู่ค้า', path: 'SettingPartnerRequestMobile', disable: false },
        { key: 20, action: '', title: 'ตั้งค่ากลุ่มคู่ค้า (Tier)', path: 'SettingTierMobile' },
        { key: 21, action: '', title: 'ตั้งค่าใบเสนอราคา', path: 'QuotationSettingSellerMobile' },
        { key: 22, action: '', title: 'e-Tax Credentail', path: 'EtaxCredentailMobile' },
        { key: 23, action: '', title: 'จัดการคูปอง', path: 'MerchantShopMobile' },
        { key: 24, action: '', title: 'จัดการบทความ', path: 'manageArticleMobile' },
        { key: 25, action: '', title: 'จัดการสินค้า Flash Sale', path: 'manageFlashSaleMobile', disable: false },
        { key: 26, action: '', title: 'จัดการใบเสนอราคา', path: 'ManageQTExternalMobile', disable: false },
        { key: 27, action: '', title: 'แดชบอร์ดระบบถอนเงิน', path: 'DashboardWithdrawMoneyShopMobile', disable: false }
        // { key: 25, action: '', title: 'แดชบอร์ดร้านค้า', path: 'sellerdashboard', disable: false },
        // { key: 27, action: '', title: 'แดชบอร์ด affiliate', path: 'dashboardShopAffiliateMobile', disable: false }
      ],
      itemsSalesOrder: [
        {
          key: 3,
          action: 'mdi-briefcase-account',
          active: false,
          title: 'Sales Order',
          items: []
        }
      ],
      itemsPromotion: [
        {
          key: 4,
          action: 'mdi-ticket-percent',
          active: false,
          title: 'จัดการโปรโมชัน',
          items: []
        }
      ],
      itemsAffiliate: [
        {
          key: 5,
          action: 'mdi-chart-line',
          active: false,
          title: 'โปรแกรม Affiliate',
          items: []
        }
      ],
      itemsPartner: [
        {
          key: 6,
          action: 'mdi-account-group',
          active: false,
          title: 'เชื่อมต่อ Partner',
          items: []
        }
      ],
      itemsMarketplace: [
        {
          key: 7,
          action: 'mdi-handshake',
          active: false,
          title: 'Software Marketplace',
          items: []
        }
      ],
      itemsShopAccount: [
        {
          key: 8,
          action: 'mdi-account-cash',
          active: false,
          title: 'จัดการบัญชี',
          items: []
        }
      ],
      itemsLiveStreaming: [
        {
          key: 9,
          action: 'mdi mdi-camera-wireless',
          active: false,
          title: 'จัดการ Live Streaming',
          items: []
        }
      ],
      itemsManageTag: [
        {
          key: 10,
          action: 'mdi mdi-tag',
          active: false,
          title: 'จัดการแท็ก',
          items: []
        }
      ],
      itemsMembers: [
        {
          key: 11,
          action: 'mdi-account-box-multiple',
          active: true,
          title: 'จัดการข้อมูลสมาชิก',
          items: []
        }
      ],
      pathData: '',
      roleUser: ''
    }
  },
  async created () {
    // console.log('เข้า create in shop')
    // var dataUser = JSON.parse(Decode.decode(localStorage.getItem('DetailUser')))
    // console.log('dataUser', dataUser)
    this.$store.commit('openLoader')
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('getPath')
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('resetSearch')
    this.$EventBus.$on('changeNav', this.SelectPath)
    this.$EventBus.$on('ChangeActiveMenu', this.ChangeActiveMenu)
    this.$EventBus.$on('AuthorityUsersSellerMobile', this.AuthorityUsers)
    var path = ''
    if (localStorage.getItem('roleUser') !== null) {
      var data = JSON.parse(localStorage.getItem('roleUser'))
      this.roleUser = data
    }
    if (localStorage.getItem('pathShopSale') !== null) {
      path = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
      this.pathData = path
    } else {
      path = '/'
      this.pathData = path
    }
    var dataShop = {
      id: this.$route.query.ShopID,
      name: this.$route.query.ShopName
    }
    localStorage.setItem('shopDetail', JSON.stringify(dataShop))
    await this.checkJVShop()
  },
  watch: {
    MobileSize (val) {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (val === true) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.checkJVShop()
    // this.$EventBus.$on('AuthorityUsers', this.AuthorityUsers)
    this.$EventBus.$on('checkJVShop', this.checkJVShop)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('AuthorityUsers')
      this.$EventBus.$off('checkJVShop')
    })
  },
  methods: {
    async checkJVShop () {
      this.isJV = ''
      var shopDetail = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: shopDetail,
        role: 'seller'
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        // console.log('response.data', response.data[0])
        const img = response.data[0].shop_profile
        if (img !== undefined) {
          this.imgLogoShop = img[0].media_path
        } else {
          this.imgLogoShop = ''
        }
        if (response.data[0].is_JV === 'yes') {
          this.isJV = 'yes'
        } else {
          this.isJV = 'no'
        }
      }
      await this.AuthorityUsers()
    },
    async AuthorityUsers () {
      this.dataDetail = []
      if (this.$router.currentRoute.name === 'createShopMobile' || this.$router.currentRoute.name === 'stepCreateShopMobile') {
      } else {
        if (localStorage.getItem('list_shop_detail') !== null) {
          this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
        } else {
          this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => {})
        }
      }
      if (this.dataDetail.array_position.some(element => element.position_name === 'เจ้าของร้าน')) {
        this.PositionName = 'เจ้าของร้าน'
      } else if (this.dataDetail.array_position.some(element => element.position_name === 'เจ้าของนิติบุคคล')) {
        this.PositionName = 'เจ้าของนิติบุคคล'
      } else {
        this.PositionName = 'ไม่ใช่เจ้าของร้านค้า'
      }
      var item1 = [{ key: 1, action: 'mdi-storefront', title: 'จัดการร้านค้า', disable: true }]
      var item2 = []
      var item3 = []
      var item4 = []
      var item5 = []
      var item6 = []
      var item7 = []
      var item8 = []
      var item9 = []
      var item10 = []
      var item11 = []
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1' || this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 2, action: 'mdi-domain', title: 'บริษัทคู่ค้า', disable: true })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 3, action: 'mdi-briefcase-account', title: 'Sales Order', disable: true })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 4, action: 'mdi-ticket-percent', title: 'จัดการโปรโมชัน', disable: true })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1' || (this.PositionName === 'เจ้าของนิติบุคคล' || this.PositionName === 'เจ้าของร้าน')) {
        item5.push({ key: 5, action: 'mdi-chart-line', title: 'โปรแกรม Affiliate', disable: true })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
        item6.push({ key: 6, action: 'mdi-account-group', title: 'เชื่อมต่อ Partner', disable: true })
      } if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
        item7.push({ key: 7, action: 'mdi-handshake', title: 'Software Marketplace', disable: true })
      } if (this.dataDetail.can_use_function_in_shop.manage_account_bank === '1') {
        item8.push({ key: 8, action: 'mdi-account-cash', title: 'จัดการบัญชี', disable: true })
      } if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item9.push({ key: 9, action: 'mdi mdi-camera-wireless', title: 'จัดการ Live Streaming', disable: true })
      } if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item10.push({ key: 10, action: 'mdi mdi-tag', title: 'จัดการแท็ก', disable: true })
      } if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item11.push({ key: 11, action: 'mdi mdi-account-box-multiple', title: 'จัดการข้อมูลสมาชิก', disable: true })
      } if (this.$router.currentRoute.name === 'createShopMobile' || this.$router.currentRoute.name === 'stepCreateShopMobile') {
        item1 = this.itemsCreateShop
      } else {
        if (this.dataDetail.can_use_function_in_shop.manage_product === '1') {
          item1.push({ key: 4, title: 'รายการสินค้า', path: 'sellerShopMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item1.push({ key: 5, title: 'ดูรายการสั่งซื้อ', path: 'posellerMobile' })
          item1.push({ key: 14, title: 'รายงานการจัดส่ง', path: 'ShippingReportMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1' && this.isJV === 'yes') {
          item1.push({ key: 13, title: 'รายงานการสั่งซื้อ', path: 'ReportsellerMobile' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        //   item1.push({ key: 6, title: 'เรียกพนักงานรับพัสดุ', path: 'CurierMobile' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_tracking === '1') {
        //   item1.push({ key: 7, title: 'ติดตามสถานะสินค้า', path: 'TackingorderMobile' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_stock === '1') {
        //   item1.push({ key: 11, title: 'สต๊อกสินค้า', path: 'inventoryMobile' })
        // }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 12, title: 'จัดการร้านค้า', path: 'designShopMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 30, title: 'จัดการใบเสนอราคา', path: 'ManageQTExternalMobile' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_product_refund === '1') {
        //   item1.push({ key: 13, title: 'รายการคืนสินค้า', path: 'returnMobile' })
        // }
        if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1' && this.isJV === 'yes') {
          item1.push({ key: 14, title: 'แดชบอร์ด', path: 'dashboardMobile' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 15, title: 'เอกสารและคู่มือการใช้งาน', path: 'DownloadFilesMobile' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_income === '1') {
        //   item1.push({ key: 16, title: 'รายได้ของฉัน', path: 'revenueMobile' })
        // }
        if (this.dataDetail.can_use_function_in_shop.manage_user_with_position === '1') {
          item1.push({ key: 17, title: 'จัดการตำแหน่งและสิทธิ์การใช้งาน', path: 'listShopPositionMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_user_with_position === '1') {
          item1.push({ key: 18, title: 'จัดการผู้ใช้งาน', path: 'listShopUserMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_shipping === '1') {
          item1.push({ key: 29, title: 'จัดการขนส่ง', path: 'manageShippingSellerMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_shipping === '1') {
          item1.push({ key: 31, title: 'จัดการหมวดหมู่สินค้า', path: 'ManageCategoryProductMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item1.push({ key: 19, title: 'จัดการความคิดเห็น', path: 'manageCommentsMobile' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        //   item1.push({ key: 23, title: 'ใบเสนอราคา', path: 'QuotationAll' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 20, title: 'ตั้งค่าการขอเป็นคู่ค้า', path: 'SettingPartnerRequestMobile' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 21, title: 'ตั้งค่ากลุ่มคู่ค้า (Tier)', path: 'SettingTierMobile' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 22, title: 'ตั้งค่าใบเสนอราคา', path: 'QuotationSettingSellerMobile' })
        // }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 23, title: 'e-Tax Credential', path: 'EtaxCredentailMobile' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 24, title: 'จัดการคูปอง', path: 'MerchantShopMobile' })
        // }
        if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1') {
          item1.push({ key: 25, title: 'แดชบอร์ดร้านค้า', path: 'sellerdashboardMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1') {
          item1.push({ key: 30, title: 'แดชบอร์ดระบบถอนเงิน', path: 'DashboardWithdrawMoneyShopMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 26, title: 'ประกาศงาน/ค้นหาผู้สมัคร', path: 'WLCAdmin' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1' || (this.PositionName === 'เจ้าของนิติบุคคล' || this.PositionName === 'เจ้าของร้าน')) {
          item1.push({ key: 27, title: 'รายการเอกสารมอบอำนาจ', path: 'listAttorneyMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 28, title: 'จัดการบทความ', path: 'manageArticleMobile' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 29, title: 'Traceability Profile', path: 'ProfileTraceabilityMobile' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 29, title: 'จัดการสินค้า Flash Sale', path: 'manageFlashSaleMobile' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item1.push({ key: 28, title: 'แดชบอร์ด Affiliate', path: 'dashboardShopAffiliateMobile' })
        // }
      }
      this.items = []
      this.items = item1
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 0, title: 'รายการสั่งซื้อสินค้า', path: 'POSellerB2BListMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 1, title: 'รายงานการจัดส่ง', path: 'ManageShippingB2BMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item2.push({ key: 2, title: 'รายการเอกสารขอเป็นคู่ค้า', path: 'SettingPartnerRequestMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item2.push({ key: 3, title: 'ตั้งค่ากลุ่มคู่ค้า (Tier)', path: 'SettingTierMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item2.push({ key: 4, title: 'รายชื่อคู่ค้า', path: 'partnerSellerMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 5, title: 'ใบเสนอราคา', path: 'QuotationAllMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 6, title: 'รายการใบส่งสินค้า', path: 'ListDeliveryMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 7, title: 'จัดการรูปแบบการอนุมัติคู่ค้า', path: 'ManageSalesPartnerApprovalMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 8, title: 'จัดการลำดับการอนุมัติคู่ค้า', path: 'manageSalePartnerApproveMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 9, title: 'รายการอนุมัติคู่ค้า', path: 'ListApprovePartnerShopMobile' })
      }
      this.itemsCompany = []
      this.itemsCompany = item2
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 0, title: 'รายชื่อลูกค้า', path: 'listCustomerSaleOrderMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 1, title: 'กลุ่มลูกค้า', path: 'listCustomerGroupSalesMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 2, title: 'ใบเสนอราคา', path: 'listQuotationSalesMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 3, title: 'รายการสั่งซื้อฝ่ายขาย', path: 'orderSalesMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 4, title: 'รายชื่อฝ่ายขาย', path: 'listSalesMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 5, title: 'จัดการรูปแบบการอนุมัติฝ่ายขาย', path: 'ManageSalesApprovalMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 6, title: 'จัดการลำดับการอนุมัติฝ่ายขาย', path: 'manageSaleApproveMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 7, title: 'รายการอนุมัติฝ่ายขาย', path: 'listApproveSalesMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 8, title: 'แดชบอร์ดฝ่ายขาย', path: 'DashboardSaleOrderMobile' })
      }
      this.itemsSalesOrder = []
      this.itemsSalesOrder = item3
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 0, title: 'จัดการคูปอง', path: 'manageCouponMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 1, title: 'จัดการแต้ม', path: 'setPointMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 2, title: 'รายชื่อลูกค้าสะสมแต้ม', path: 'allpointfromcostomerMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 3, title: 'จัดการสินค้า Flash Sale', path: 'manageFlashSaleMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 4, title: 'Bundle Deal', path: 'bundleDealMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        item4.push({ key: 5, title: 'Add-on Deal', path: 'AddOnDealMobile' })
      }
      this.itemsPromotion = []
      this.itemsPromotion = item4
      if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1' || (this.PositionName === 'เจ้าของนิติบุคคล' || this.PositionName === 'เจ้าของร้าน')) {
        item5.push({ key: 0, title: 'จัดการ Affiliate', path: 'sellerAffiliateMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1') {
        item5.push({ key: 1, title: 'รายชื่อผู้เข้าร่วม Affiliate', path: 'ListUserJoinAffiliateMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1') {
        item5.push({ key: 2, title: 'แดชบอร์ด Affiliate', path: 'DashboardShopAffiliateMobile' })
      }
      this.itemsAffiliate = []
      this.itemsAffiliate = item5
      if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
        item6.push({ key: 0, title: 'เชื่อมต่อบริการ', path: 'ERPPartnerMobile' })
      }
      this.itemsPartner = []
      this.itemsPartner = item6
      if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
        item7.push({ key: 0, title: 'ข้อมูลร้านค้า Partner', path: 'ShopJoinPartnerDetailsMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
        item7.push({ key: 1, title: 'รายการคำสั่งซื้อ', path: 'orderListPartnerMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
        item7.push({ key: 2, title: 'รายการชำระเงิน', path: 'paymentPartnerMobile' })
      }
      this.itemsMarketplace = []
      this.itemsMarketplace = item7
      if (this.dataDetail.can_use_function_in_shop.manage_account_bank === '1') {
        item8.push({ key: 0, title: 'จัดการบัญชีร้านค้า', path: 'ManageShopAccountMobile' })
      }
      this.itemsShopAccount = []
      this.itemsShopAccount = item8
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item9.push({ key: 0, title: 'Live Streaming', path: 'hostMobile' })
      }
      this.itemsLiveStreaming = []
      this.itemsLiveStreaming = item9
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item10.push({ key: 57, title: 'จัดการแท็กร้านค้า', path: 'ManageTagShopMobile' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item10.push({ key: 58, title: 'จัดการแท็กสินค้า', path: 'ManageTagProductShopMobile' })
      }
      this.itemsManageTag = []
      this.itemsManageTag = item10
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item11.push({ key: 59, title: 'จัดการข้อมูลสมาชิก', path: 'ManageMembersMobile' })
      }
      this.itemsMembers = []
      this.itemsMembers = item11
      this.$store.commit('closeLoader')
    },
    async changePage (val) {
      // window.location.assign(`/${val}`)
      // this.$router.push({ path: `/${val}` }).catch(() => {})
      this.$EventBus.$emit('resetAdminShop')
      if (val !== 'sellerShopMobile' && val !== '/') {
        if (val === 'WLCAdmin') {
          // API get share_token
          var auth
          var response
          if (localStorage.getItem('oneData') !== null) {
            var oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            auth = {
              headers: { Authorization: `Bearer ${oneData.user.access_token}` }
            }
          }
          var WLC = ''
          var URLCurrent = ''
          if (location.protocol === 'https:') {
            URLCurrent = window.location.href.substring(8, 11)
            if (URLCurrent === 'dev') {
              if (oneData.typeLoginOne === 'OneID') {
                WLC = 'prd'
                response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
                if (response.data.message === 'Success.') {
                  window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
                }
              } else {
                WLC = 'uat'
                response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
                if (response.data.message === 'Success.') {
                  window.open(`${process.env.VUE_APP_WLC_ADMIN_UAT}=${response.data.data.share_token}`)
                }
              }
            } else if (URLCurrent === 'uat') {
              WLC = 'prd'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
              }
            } else {
              WLC = 'prd'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
              }
            }
          } else {
            if (oneData.typeLoginOne === 'OneID') {
              WLC = 'prd'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
              }
            } else {
              WLC = 'uat'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN_UAT}=${response.data.data.share_token}`)
              }
            }
          }
        } else {
          this.$router.push({ path: `/${val}` }).catch(() => {})
        }
      } else if (val === '/') {
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
        // console.log(shopDetail)
        if (this.MobileSize === true) {
          this.$router.push({ path: '/sellerShopMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
        } else {
          this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
        }
        // this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async Gopage (val) {
      // console.log('val path====>', val)
      // this.dataBreadcrumb.pop()
      // this.dataBreadcrumb.push(val)
      if (this.roleUser.role === 'sale_order' || this.roleUser.role === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathData.path }).catch(() => {})
      } else {
        if (val !== 'sellerShoMobile') {
          this.$router.push(val).catch(() => {})
        } else {
          var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
          // console.log(shopDetail)
          if (this.MobileSize === true) {
            this.$router.push({ path: '/sellerShopMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
          } else {
            this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
          }
          // this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
        }
      }
    },
    ChangeActiveMenu (val) {
      this.activeMenu = val
    },
    SelectPath () {
      // console.log('----->.', this.$router.currentRoute.name)
      if (this.$router.currentRoute.name === 'sellerUI') {
        this.defaultSelect = 0
      } else if (this.$router.currentRoute.name === 'posellerMobileUI' || this.$router.currentRoute.name === 'posellerDetailMobileUI') {
        this.defaultSelect = 1
      } else if (this.$router.currentRoute.name === 'CurierMobile') { // inventoryUI'
        this.defaultSelect = 2
      } else if (this.$router.currentRoute.name === 'inventoryUI') {
        this.defaultSelect = 3
      } else if (this.$router.currentRoute.name === 'designShopUI') {
        this.defaultSelect = 4
      } else if (this.$router.currentRoute.name === 'supplier_seller') {
        this.defaultSelect = 5
      } else if (this.$router.currentRoute.name === 'promotion_seller') {
        this.defaultSelect = 6
      } else if (this.$router.currentRoute.name === 'partner_seller') {
        this.defaultSelect = 7
      } else if (this.$router.currentRoute.name === 'order_seller') {
        this.defaultSelect = 8
      } else if (this.$router.currentRoute.name === 'shop') {
        this.defaultSelect = 9
      } else if (this.$router.currentRoute.name === 'shopAddress') {
        this.defaultSelect = 10
      } else if (this.$router.currentRoute.name === 'manufacturer_seller') {
        this.defaultSelect = 11
      } else if (this.$router.currentRoute.name === 'DownloadFilesMobile') {
        this.defaultSelect = 7
      } else if (this.$router.currentRoute.name === 'dashboardmobile') {
        this.defaultSelect = 12
      } else if (this.$router.currentRoute.name === 'revenueMobile') {
        this.defaultSelect = 13
      } else if (this.$router.currentRoute.name === 'returnMobile') {
        this.defaultSelect = 14
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequestMobile') {
        this.defaultSelect = 15
      } else if (this.$router.currentRoute.name === 'SettingTierMobile') {
        this.defaultSelect = 16
      } else if (this.$router.currentRoute.name === 'QuotationSettingSellerMobile') {
        this.defaultSelect = 17
      } else if (this.$router.currentRoute.name === 'EtaxCredentailMobile') {
        this.defaultSelect = 18
      } else if (this.$router.currentRoute.name === 'MerchantShopMobile' || this.$router.currentRoute.name === 'ManagaCuponMobile') {
        this.defaultSelect = 19
      } else if (this.$router.currentRoute.name === 'manageArticleMobile') {
        this.defaultSelect = 20
      } else if (this.$router.currentRoute.name === 'ERPPartnerMobile') {
        this.defaultSelect = 21
      } else if (this.$router.currentRoute.name === 'manageFlashSaleMobile') {
        this.defaultSelect = 22
      } else if (this.$router.currentRoute.name === 'bundleDealMobile' || this.$router.currentRoute.name === 'createBundleDealMobile') {
        this.defaultSelect = 37
      } else if (this.$router.currentRoute.name === 'DashboardWithdrawMoneyShopMobile') {
        this.defaultSelect = 23
      } else if (this.$router.currentRoute.name === 'ManageQTExternalMobile') {
        this.defaultSelect = 24
      } else if (this.$router.currentRoute.name === 'orderListPartnerMobile') {
        this.defaultSelect = 26
      } else if (this.$router.currentRoute.name === 'ShopJoinPartnerDetailsMobile' || this.$router.currentRoute.name === 'ShopPartnerDetailsMobile' || this.$router.currentRoute.name === 'PaymentPackageMobile') {
        this.defaultSelect = 25
      } else if (this.$router.currentRoute.name === 'paymentPartnerMobile' || this.$router.currentRoute.name === 'paymentPartnerMobile') {
        this.defaultSelect = 27
      } else if (this.$router.currentRoute.name === 'ManageShopAccountMobile') {
        this.defaultSelect = 28
      } else if (this.$router.currentRoute.name === 'hostMobile') {
        this.defaultSelect = 29
      } else if (this.$router.currentRoute.name === 'ListDeliveryMobile') {
        this.defaultSelect = 30
      } else if (this.$router.currentRoute.name === 'ManageSalesPartnerApprovalMobile') {
        this.defaultSelect = 31
      } else if (this.$router.currentRoute.name === 'manageSalePartnerApproveMobile') {
        this.defaultSelect = 32
      } else if (this.$router.currentRoute.name === 'ListApprovePartnerShopMobile') {
        this.defaultSelect = 33
      } else if (this.$router.currentRoute.name === 'ManageTagShopMobile') {
        this.defaultSelect = 34
      } else if (this.$router.currentRoute.name === 'ManageTagProductShopMobile') {
        this.defaultSelect = 35
      } else if (this.$router.currentRoute.name === 'ProfileTraceabilityMobile' || this.$router.currentRoute.name === 'CreateProfileTraceabilityMobile' || this.$router.currentRoute.name === 'DetailProfileTraceabilityMobile') {
        this.defaultSelect = 36
      } else if (this.$router.currentRoute.name === 'ManageMembers' || this.$router.currentRoute.name === 'EditMembers' || this.$router.currentRoute.name === 'AddMembers') {
        this.defaultSelect = 37
      } else if (this.$router.currentRoute.name === 'POSellerB2BListMobile') {
        this.defaultSelect = 38
      } else if (this.$router.currentRoute.name === 'ManageShippingB2BMobile') {
        this.defaultSelect = 39
      }
    }
  }
}
</script>

<style scoped>
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
