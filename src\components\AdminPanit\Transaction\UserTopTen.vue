<template>
  <div>
    <!-- Top Ten Ext Buyer -->
    <v-dialog v-model="dialogUserTopTen" persistent width="500">
      <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>ผู้ซื้อยอดเยี่ยม Top 10</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogUserTopTen = !dialogUserTopTen" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="3" md="2" sm="2">
              <v-avatar size="60" style="background: #F3F5F9; border-radius: 999px;">
                <v-img src="@/assets/extbuyer.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="7" md="6" sm="6" class="mt-3">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">ผู้ซื้อทั่วไป</span>
            </v-col>
          </v-row>
          <v-row dense class="my-4">
            <v-img src="@/assets/LineDash.png" contain width="100%"></v-img>
          </v-row>
          <v-row dense>
            <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
              <v-card-text class="py-0 px-0">
                <v-list style="background: #FAFAFA;" disabled>
                  <v-list-item-group v-for="(item, i) in itemUserExtbuyer" :key="i">
                    <v-list-item>
                      <div style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333;" class="pr-4">{{ item.ranking }}</div>
                      <v-list-item-avatar size="64">
                        <!-- <v-img v-if="item.image !== null" :src="item.image" contain></v-img> -->
                        <v-img src="@/assets/extbuyer.png" contain max-height="40px" max-width="40px"></v-img>
                      </v-list-item-avatar>
                      <v-list-item-content class="mx-1 ml-4">
                        <v-list-item-title>
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.buyer_name }}</span><br/>
                          <!-- <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span> -->
                        </v-list-item-title>
                      </v-list-item-content>
                      <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-card-text>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Top Ten Purchaser -->
    <v-dialog v-model="dialogPurchaserTopTen" persistent width="500">
      <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>ผู้ซื้อองค์กรยอดเยี่ยม Top 10</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogPurchaserTopTen = !dialogPurchaserTopTen" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="3" md="2" sm="2">
              <v-avatar size="60" style="background: #F3F5F9; border-radius: 999px;">
                <v-img src="@/assets/purchaser.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="7" md="6" sm="6" class="mt-3">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">ผู้ซื้อองค์กร</span>
            </v-col>
          </v-row>
          <v-row dense class="my-4">
            <v-img src="@/assets/LineDash.png" contain width="100%"></v-img>
          </v-row>
          <v-row dense>
            <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
              <v-card-text class="py-0 px-0">
                <v-list style="background: #FAFAFA;" disabled>
                  <v-list-item-group v-for="(item, i) in itemUserPurchaser" :key="i">
                    <v-list-item>
                      <div style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333;" class="pr-4">{{ item.ranking }}</div>
                      <v-list-item-avatar size="64">
                        <!-- <v-img v-if="item.image !== null" :src="item.image" contain></v-img> -->
                        <v-img src="@/assets/purchaser.png" contain max-height="40px" max-width="40px"></v-img>
                      </v-list-item-avatar>
                      <v-list-item-content class="mx-1 ml-4">
                        <v-list-item-title>
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.company_name }}</span><br/>
                          <!-- <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span> -->
                        </v-list-item-title>
                      </v-list-item-content>
                      <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-card-text>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-row dense>
      <v-col cols="12" align="start" class="pl-1 mb-2">
        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">ผู้ซื้อยอดเยี่ยม Top 10 และผู้ซื้อองค์กรยอดเยี่ยม Top 10</span>
      </v-col>
    </v-row>
    <v-row dense>
      <v-col cols="12" md="6">
        <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
          <v-card-title>
            <v-row dense>
              <v-col cols="3" md="2" sm="2">
                <v-avatar :size="!MobileSize ? '60' : '50'" style="background: #F3F5F9; border-radius: 999px;">
                  <v-img src="@/assets/extbuyer.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="7" md="6" sm="6" class="mt-3">
                <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" v-if="!MobileSize">ผู้ซื้อยอดเยี่ยม Top 10</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;" v-else>ผู้ซื้อยอดเยี่ยม Top 10</span>
              </v-col>
              <v-col cols="2" :class="!MobileSize ? 'ml-4 mt-3' : 'mt-3'" v-if="itemUserExtbuyer.length !== 0">
                <v-btn outlined color="#27AB9C" @click="openAllTopTen('ExtBuyer')" v-if="!MobileSize">ดูทั้งหมด <v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
                <v-btn outlined color="#27AB9C" icon @click="openAllTopTen('ExtBuyer')" v-else><v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text>
            <v-row dense class="my-4">
              <v-img src="@/assets/LineDash.png" width="100%"></v-img>
            </v-row>
            <v-row dense>
              <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text class="py-0 px-0" v-if="itemUserExtbuyer.length !== 0">
                  <v-list style="background: #FAFAFA;" disabled>
                    <v-list-item-group v-for="(item, i) in itemUserExtbuyer" :key="i">
                      <v-list-item v-if="i < 5">
                        <v-list-item-avatar :size="!MobileSize ? '50' : '25'">
                          <v-img contain v-if="item.ranking === 1" src="@/assets/icons/level_1.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 2" src="@/assets/icons/level_2.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 3" src="@/assets/icons/level_3.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 4" src="@/assets/icons/level_4.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 5" src="@/assets/icons/level_5.png"></v-img>
                        </v-list-item-avatar>
                        <v-list-item-avatar :size="!MobileSize ? '64' : '50'">
                          <!-- <v-img v-if="item.image !== null" :src="item.image" contain></v-img> -->
                          <v-img src="@/assets/extbuyer.png" contain max-height="40px" max-width="40px"></v-img>
                        </v-list-item-avatar>
                        <v-list-item-content class="mx-1 ml-4">
                          <v-list-item-title>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.buyer_name }}</span><br/>
                            <!-- <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span> -->
                          </v-list-item-title>
                        </v-list-item-content>
                        <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                      </v-list-item>
                    </v-list-item-group>
                  </v-list>
                </v-card-text>
                <v-card-text v-else style="height: 200px;">
                  <v-row justify="center" align="center">
                    <p style="font-size: 18px;" class="pt-10">ไม่มีข้อมูลผู้ซื้อยอดเยี่ยม Top 10</p>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
          <v-card-title>
            <v-row dense>
              <v-col cols="3" md="2" sm="2">
                <v-avatar :size="!MobileSize ? '60' : '50'" style="background: #F3F5F9; border-radius: 999px;">
                  <v-img src="@/assets/purchaser.png" contain max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="7" md="6" sm="6" class="mt-3">
                <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" v-if="!MobileSize">ผู้ซื้อองค์กรยอดเยี่ยม Top 10</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;" v-else>ผู้ซื้อองค์กรยอดเยี่ยม Top 10</span>
              </v-col>
              <v-col cols="2" :class="!MobileSize ? 'ml-4 mt-3' : 'mt-3'" v-if="itemUserPurchaser.length !== 0">
                <v-btn outlined color="#27AB9C" @click="openAllTopTen('Purchaser')" v-if="!MobileSize">ดูทั้งหมด <v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
                <v-btn outlined color="#27AB9C" icon @click="openAllTopTen('Purchaser')" v-else><v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text>
            <v-row dense class="my-4">
              <v-img src="@/assets/LineDash.png" width="100%"></v-img>
            </v-row>
            <v-row dense>
              <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text class="py-0 px-0" v-if="itemUserPurchaser.length !== 0">
                  <v-list style="background: #FAFAFA;" disabled>
                    <v-list-item-group v-for="(item, i) in itemUserPurchaser" :key="i">
                      <v-list-item v-if="i < 5">
                        <v-list-item-avatar :size="!MobileSize ? '50' : '25'">
                          <v-img contain v-if="item.ranking === 1" src="@/assets/icons/level_1.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 2" src="@/assets/icons/level_2.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 3" src="@/assets/icons/level_3.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 4" src="@/assets/icons/level_4.png"></v-img>
                          <v-img contain v-else-if="item.ranking === 5" src="@/assets/icons/level_5.png"></v-img>
                        </v-list-item-avatar>
                        <v-list-item-avatar :size="!MobileSize ? '64' : '50'">
                          <!-- <v-img v-if="item.image !== null" :src="item.image" contain></v-img> -->
                          <v-img src="@/assets/purchaser.png" contain max-height="40px" max-width="40px"></v-img>
                        </v-list-item-avatar>
                        <v-list-item-content class="mx-1 ml-4">
                          <v-list-item-title>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.company_name }}</span><br/>
                            <!-- <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span> -->
                          </v-list-item-title>
                        </v-list-item-content>
                        <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                      </v-list-item>
                    </v-list-item-group>
                  </v-list>
                </v-card-text>
                <v-card-text v-else style="height: 200px;">
                  <v-row justify="center">
                    <p style="font-size: 18px;" class="pt-10">ไม่มีข้อมูลผู้ซื้อองค์กรยอดเยี่ยม Top 10</p>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dateStart: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateEnd: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dialogUserTopTen: false,
      dialogPurchaserTopTen: false,
      itemUserExtbuyer: [],
      itemUserPurchaser: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  async created () {
    this.$EventBus.$on('getDataTop10buyers', this.getDataTop10buyers)
    this.$EventBus.$on('getDataTop10purchasers', this.getDataTop10purchasers)
    await this.getDataTop10buyers(this.dateStart, this.dateEnd, '', '')
    await this.getDataTop10purchasers(this.dateStart, this.dateEnd, '', '')
  },
  methods: {
    openAllTopTen (val) {
      if (val === 'ExtBuyer') {
        this.dialogUserTopTen = !this.dialogUserTopTen
      } else if (val === 'Purchaser') {
        this.dialogPurchaserTopTen = !this.dialogPurchaserTopTen
      }
    },
    async getDataTop10buyers (startDate, endDate, type, shopID) {
      this.itemUserExtbuyer = []
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      // console.log(data)
      await this.$store.dispatch('actionsTop10Buyer', data)
      var response = await this.$store.state.ModuleAdminPanit.stateTop10Buyer
      // console.log('getDataTop10buyers', response)
      if (response.result === 'OK') {
        this.itemUserExtbuyer = response.data
      }
    },
    async getDataTop10purchasers (startDate, endDate, type, shopID) {
      this.itemUserPurchaser = []
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      // console.log(data)
      await this.$store.dispatch('actionsTop10Purchaser', data)
      var response = await this.$store.state.ModuleAdminPanit.stateTop10Purchaser
      // console.log('getDataTop10purchasers', response)
      if (response.result === 'OK') {
        this.itemUserPurchaser = response.data
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    }
  }
}
</script>
