<template>
  <v-container class="pa-2">
    <v-card elevation="0" width="100%" height="100%" :class="IpadSize ? 'px-0 py-0' : MobileSize ? 'ma-1' : '' ">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">{{ $t('PobuyerRecord.title') }}</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#1AB759" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> {{ $t('PobuyerRecord.title') }}
      </v-card-title>
      <v-col cols="12" md="6" sm="12">
        <v-text-field v-model="searchProduct" outlined dense :append-icon="'mdi-magnify'" :placeholder="$t('PobuyerRecord.search')" hide-details style="border-radius: 8px;"></v-text-field>
      </v-col>
      <v-col cols="12" v-if="itemListShow.length !== 0">
        <div v-for="(item, index) in itemListShow" :key="index" >
          <v-card elevation="0" class="mb-2" style="border: 1px solid rgb(230, 230, 230); border-radius: 8px;">
            <v-card-text>
              <v-row dense>
                <!-- ส่วน Head -->
                <v-col cols="12">
                  <v-row dense>
                    <v-col cols="6" align="start">
                      <span style="font-size: 14px; font-weight: 700; color: #333333;">{{ item.seller_shop_name }}</span>
                      <v-btn style="font-size: 10px;" @click="gotoShopDetail(item)" class="ml-2" outlined x-small color="#27AB9C"><v-icon class="pr-1" small>mdi-storefront</v-icon> {{ $t('PobuyerRecord.viewShop') }}</v-btn>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-size: 14px; font-weight: 400; color: #333333;">{{ $t('PobuyerRecord.orderDate') }} : {{ item.order_created_at !== '' ? formatDateTime(item.order_created_at) : '-' }} {{ $i18n.locale === 'th' ? new Date(item.order_created_at).toLocaleTimeString('th-TH') + ' น.' : new Date(item.order_created_at).toLocaleTimeString('en-EN')}}</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12">
                  <v-divider style="1px solid rgba(0,0,0,.09)"></v-divider>
                </v-col>
                <!-- ส่วน ข้อมูลสินค้า -->
                <v-col cols="12">
                  <v-row dense @click="gotoDetailProduct(item)" style="cursor: pointer;">
                    <v-col cols="5" md="2" sm="3">
                      <v-card max-width="110px" max-height="110px" outlined class="pa-1">
                        <v-img  v-if="item.product_image !== ''" :src="item.product_image" loading="lazy" class="img_center"  max-width="100px"  max-height="100px" contain></v-img>
                        <v-img v-if="item.product_image === ''" src="@/assets/NoImage.png" loading="lazy" class="img_center" max-width="100px"  max-height="100px" contain></v-img>
                      </v-card>
                    </v-col>
                    <v-col cols="7" md="6" sm="6" class="pl-0" :class="MobileSize ? 'pb-0' : ''">
                      <span style="font-weight: 700; font-size: 16px; line-height: 22px; color: #333333;">{{ item.product_name }}</span><br>
                      <span v-if="item.product_attribute.attribute_1_key !== ''" style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;" class="mb-0">{{item.product_attribute.attribute_1_key}}: {{item.product_attribute_detail.attribute_option_1}}</span>
                      <span v-if="item.product_attribute.attribute_2_key !== ''" style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;" class="pl-2 mb-0">{{item.product_attribute.attribute_2_key}}: {{item.product_attribute_detail.attribute_option_2}}</span><br><br>
                      <span style="color: #333333;">{{ $t('PobuyerRecord.quantity') }} : {{ item.quantity }}</span>
                      <v-row dense class="pl-1 pt-2" v-if="MobileSize">
                        <span style="color:#C4C4C4; text-decoration:line-through; font-size: 16px;" v-if="item.discount_percent !== 0">฿ {{ parseFloat(item.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <span style="color:red; font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-if="item.vat_default === 'yes' && item.discount_percent !== 0">฿ {{ (parseFloat(item.real_price) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <span style="color:red; font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-else-if="item.vat_default === 'no' && item.discount_percent !== 0">฿ {{ parseFloat(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <span style="font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-else-if="item.vat_default === 'yes' && item.discount_percent === 0">฿ {{ (parseFloat(item.real_price) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <span style="font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-else-if="item.vat_default === 'no' && item.discount_percent === 0">฿ {{ parseFloat(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <!-- <span>{{item.discount_percent}}</span> -->
                      </v-row>
                    </v-col>
                    <v-col v-if="!MobileSize" cols="12" md="4" sm="3" class="d-flex align-content-center flex-wrap" :class="MobileSize ? 'pb-0' : IpadSize ? 'px-0' : ''">
                      <span style="color:#C4C4C4; text-decoration:line-through; font-size: 16px;" v-if="item.discount_percent !== 0">฿ {{ parseFloat(item.fake_price).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
                      <span style="color:red; font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-if="item.vat_default === 'yes' && item.discount_percent !== 0">฿ {{ (parseFloat(item.real_price) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      <span style="color:red; font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-else-if="item.vat_default === 'no' && item.discount_percent !== 0">฿ {{ parseFloat(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      <span style="font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-else-if="item.vat_default === 'yes' && item.discount_percent === 0">฿ {{ (parseFloat(item.real_price) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      <span style="font-size: 16px; font-weight: 700;" :class="!IpadSize && !MobileSize ? 'pl-2' : ''" v-else-if="item.vat_default === 'no' && item.discount_percent === 0">฿ {{ parseFloat(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      <span v-if="item.discount_percent !== 0" class="pl-1"><v-chip x-small style="color: #FF0000; font-size: 12px; background-color: #FBE5E4; " class="rounded-pill">{{ $t('PobuyerRecord.discount') }} -{{item.discount_percent}}%</v-chip></span>
                      <!-- <v-chip small style="color: #FF0000; font-size: 16px; background-color: #FBE5E4; " class="rounded-pill">ส่วนลด -{{ productDetail.discount_percent }}%</v-chip> -->
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions style="background-color: #F5FCFB; height: 140px;">
              <v-row dense>
                <v-col cols="12" align="end" class="pr-2">
                  <p style="font-size: 16px; font-weight: 400; color: #333333;">{{ $t('PobuyerRecord.orderSummary') }} : <b style="font-size: 24px; color: #27ab9c;">฿ {{ parseFloat(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></p>
                </v-col>
                <v-col cols="12">
                  <v-row dense justify="end" class="pr-2">
                    <v-btn @click="addToCart('AddProduct', item)" height="40" class="mr-2" color="primary" rounded outlined :disabled="(item.stock_status === 'out of stock' && item.effective_stock === 0) ? true : false"><v-icon>mdi-cart</v-icon>{{ $t('PobuyerRecord.addCart') }}</v-btn>
                    <v-btn @click="addToCart('QuickAddProduct', item)" height="40" color="primary" rounded :disabled="(item.stock_status === 'out of stock' && item.effective_stock === 0) ? true : false"><v-icon>mdi-shopping-outline</v-icon>{{ $t('PobuyerRecord.reorder') }}</v-btn>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-actions>
          </v-card>
        </div>
      </v-col>
      <v-col cols="12" v-else>
        <v-row dense justify="center">
          <v-col cols="12" align="center" style="text-align: center;">
            <v-img src="@/assets/emptypo.png" max-height="100%" max-width="100%" height="250" width="100%" contain></v-img>
          </v-col>
          <v-col cols="12" style="text-align: center;">
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>{{ $t('PobuyerRecord.noOrder') }}</b></h2>
          </v-col>
        </v-row>
      </v-col>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      item: '',
      listOfProduct: [],
      tokenstatus: '',
      dataRole: '',
      selection: '',
      selectionSize: '',
      searchProduct: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAccount')
    this.getBuyProductAgain()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    itemListShow () {
      if (this.searchProduct === '') {
        return this.listOfProduct
      } else {
        return this.listOfProduct.filter(element => {
          return element.product_name.toLowerCase().includes(this.searchProduct.toLowerCase()) || element.seller_shop_name.toLowerCase().includes(this.searchProduct.toLowerCase())
        })
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/pobuyerProfileRecordMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/pobuyerProfileRecord' }).catch(() => {})
      }
    }
  },
  methods: {
    formatDateTime (val) {
      if (!val) return null
      var date = val.split(' ')
      const [year, month, day] = date[0].split('-')
      if (this.$i18n.locale === 'th') {
        const monthName = new Date(`${year}-${month}-01`).toLocaleString(this.$i18n.locale, { month: 'long' })
        return `${day} ${monthName} ${parseInt(year) + 543}`
      } else {
        const monthName = new Date(`${year}-${month}-01`).toLocaleString(this.$i18n.locale === 'en', { month: 'long' })
        return `${day} ${monthName} ${year}`
      }
    },
    toThaiDateString (date) {
      var dateChange = new Date(date)
      const monthNames = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน',
        'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม',
        'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const year = dateChange.getFullYear() + 543
      const month = monthNames[dateChange.getMonth()]
      const numOfDay = dateChange.getDate()

      const hour = dateChange.getHours().toString().padStart(2, '0')
      const minutes = dateChange.getMinutes().toString().padStart(2, '0')
      // const second = dateChange.getSeconds().toString().padStart(2, '0')
      // :${second}
      return `${numOfDay} ${month} ${year} ` + `${hour}:${minutes} น.`
    },
    gotoShopDetail (item) {
      const shopCleaned = encodeURIComponent(item.seller_shop_name.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${item.seller_shop_id}` }).catch(() => {})
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async addToCart (ActionKey, val) {
      // console.log('ActionKey------->', ActionKey, val)
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyDetail
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyDetail = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        // console.log('companyDetail', companyDetail)
      } else if (dataRole.role === 'sale_order') {
        this.companyId = localStorage.getItem('PartnerID')
        // console.log('this.companyIdAddToCartWithLogin', thsis.companyId)
      }
      const data = {
        seller_shop_id: val.seller_shop_id,
        role_user: dataRole.role,
        product_id: val.product_id,
        pay_type: val.pay_type,
        order_type: val.order_type,
        attribute_option_1: val.product_attribute_detail.attribute_option_1 !== '' ? val.product_attribute_detail.attribute_option_1 : '',
        attribute_option_2: val.product_attribute_detail.attribute_option_2 !== '' ? val.product_attribute_detail.attribute_option_2 : '',
        quantity: val.quantity,
        company_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail.company.company_id,
        company_position: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.role_id : -1,
        com_perm_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.com_perm_id : -1
      }
      await this.$store.dispatch('ActionAddToCart', data)
      const res = await this.$store.state.ModuleCart.stateAddToCart
      if (res.message === 'Add to Cart Success') {
        this.$EventBus.$emit('getCartPopOver')
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: `<h3>${this.$t('PobuyerRecord.textAddCart')}</h3>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: `${this.$t('PobuyerRecord.textAddCart')}` })
        }
        if (ActionKey === 'QuickAddProduct') {
          this.$router.push({ path: '/shoppingcart' }).catch(() => {})
        }
      } else if (res.message === 'This product is already in cart') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: `<h4>${this.$t('PobuyerRecord.textHaveProduct')}</h4>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: `${this.$t('PobuyerRecord.textHaveProduct')}` })
        }
      } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: `<h3>${this.$t('PobuyerRecord.textIncomplete')}</h3>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: `${this.$t('PobuyerRecord.textIncomplete')}` })
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>SERVER ERROR</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'SERVER ERROR' })
        }
      } else if (res.message === 'Please insert quantity > 0') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: `<h4>${this.$t('PobuyerRecord.textInsert')}</h4>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: `${this.$t('PobuyerRecord.textInsert')}` })
        }
      } else if (res.message === 'Not found product with attribute detail.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: `<h3>${this.$t('PobuyerRecord.textInsert')}</h3>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', title: `${this.$t('PobuyerRecord.textInsert')}` })
        }
      } else if (res.message === 'Not found this product.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: `<h3>${this.$t('PobuyerRecord.textInsert')}</h3>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', title: `${this.$t('PobuyerRecord.textInsert')}` })
        }
      } else if (res.message === 'Please clear same product in your cart before add a new product cause product data had change.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', html: `<h3>${this.$t('PobuyerRecord.textUnable')}</h3><br><p>${this.$t('PobuyerRecord.textReAdd')}</p>` })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', title: `${this.$t('PobuyerRecord.textUnable')}`, text: `${this.$t('PobuyerRecord.textReAdd')}` })
        }
      } else if (res.message === 'Select Product Limit') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: `${this.$t('PobuyerRecord.textUnable')}`, text: `${this.$t('PobuyerRecord.textAddOnly')}` })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
      }
    },
    async getBuyProductAgain () {
      this.listOfProduct = []
      var data
      data = {
        role_user: 'ext_buyer',
        company_id: '-1',
        type: 'all',
        limit_item: ''
      }
      await this.$store.dispatch('actionOrderBuyAgain', data)
      var response = await this.$store.state.ModuleOrder.stateOrderBuyAgain
      // console.log('response', response.data.group_order)
      // var dataGroup = response.data.group_order
      // for (const item of dataGroup) {
      //   console.log('item--dataGroup>', item)
      // }
      if (response.result === 'success') {
        this.listOfProduct = response.data.orders
      }
    },
    gotoDetailProduct (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } }).catch(() => {})
    }
  }
}
</script>

<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
