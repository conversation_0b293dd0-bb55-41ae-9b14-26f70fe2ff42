<template>
  <div>
    <!-- Website -->
    <div v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <!-- <v-col cols="6" md="7" class="my-16">
            <v-img :src="require('@/assets/icons/Login_BG.png')" contain></v-img>
          </v-col> -->
          <v-col cols="6" md="12" align="center" class="my-9">
            <!-- height="514px" old -->
            <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
              <!-- <v-tabs
                color="#27AB9C"
                grow
              >
                <v-tab v-for="(item, index) in itemsLogin" :key="index">
                  <span style="padding-top: 0.5em; font-weight: bold; font-size: 18px; line-height: 30px;">{{ item }}</span>
                </v-tab>
                <v-tab-item class="mt-6"> -->
                  <v-card-text>
                    <v-container>
                      <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                        <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
                      </v-row>
                      <v-row dense justify="center" align-content="center" class="mt-0 mb-8">
                        <span class="textLogin">{{ $t('Login.Welcome') }}</span>
                      </v-row>
                      <v-row v-if="showError" dense justify="center" align-content="center" class="mb-8">
                        <v-col cols="12" md="12" sm="12">
                          <v-card elevation="0" class="pa-4 mx-8" style="border: 1px solid #ebccd1; background-color: #f2dede;">
                            <ul class="ma-0 text-start">
                              <li style="color: #a94442; font-size: 14px;">{{texterror}}</li>
                            </ul>
                          </v-card>
                        </v-col>
                      </v-row>
                      <v-row no-gutters dense class="mx-8">
                        <!-- <h3 style="text-align: left; font-weight: 600;">Login with Username</h3> -->
                        <v-col cols="12" md="12" sm="12" class="mb-2">
                          <h3 style="text-align: left; font-size: 15px;">{{ $t('register.Username') }}</h3>
                          <v-text-field color="#269AFD" class="pb-4" v-model="username" :placeholder="$t('register.EnterUsername')" outlined dense hide-details append-icon="mdi-account"></v-text-field>
                          <h3 style="text-align: left; font-size: 15px;">{{ $t('register.Password') }}</h3>
                          <v-text-field color="#269AFD" :append-icon="Show ? 'mdi-eye' : 'mdi-eye-off'" :type="Show ? 'text' : 'password'" @click:append="Show = !Show" class="pb-2" v-model="password"
                          :placeholder="$t('register.EnterPassword')" outlined dense hide-details @copy.prevent @keydown.space.prevent></v-text-field>
                          <span class="text-decoration-underline" style="float: right; display: inline-block; color: #269AFD; cursor: pointer;" @click="forgotPassword()">{{ $t('Login.ForgotPassword') }}</span>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="text-start">
                          <input type="checkbox" v-model="ConCheck" name="CheckBoxConsent" style="" />
                          <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-3">
                          <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Login('Username')" :disabled="ConCheck === false" >{{ $t('Login.Login') }}</v-btn>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-3">
                          <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="LoginTestOneID('Username')" :disabled="ConCheck === false" >{{ $t('Login.LoginOne') }}</v-btn>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-8">
                          <v-row dense align="center">
                            <v-divider class="mr-2"></v-divider>{{ $t('Login.Or') }}<v-divider class="ml-2"></v-divider>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-8">
                          <v-btn @click="LoginWitHealthID()" color="#2b7654" block rounded elevation="4" outlined><v-img :src="require('@/assets/provider.png')" max-height="30px" max-width="60px" contain class="pr-1"></v-img> เข้าสู่ระบบด้วย Provider ID</v-btn>
                          <!-- <v-btn fab class="mt-3 white--text" @click="Login('Facebook')" color="#1877f2" elevation="4" ><v-icon color="white" class="mr-2">mdi-facebook</v-icon></v-btn>
                          <v-btn fab class="mt-3 white--text" @click="Login('Line')" color="#06C755" elevation="4" ><v-img :src="require('@/assets/ICON/Line_app.png')" max-height="30" max-width="30" contain class="mr-2"/></v-btn>
                          <v-btn fab class="mt-3" @click="Login('Google')" elevation="4" ><v-img :src="require('@/assets/ICON/google.png')" max-height="24" max-width="24" contain class="mr-2"/></v-btn> -->
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-2 d-flex justify-center">
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="LoginWithOTP()" color="#FFFFFF" elevation="4" ><v-icon color="#269AFD">mdi-phone-in-talk</v-icon></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #269AFD;">OTP</span>
                            </v-col>
                          </v-col>
                          <!-- <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="Login('Facebook')" color="#1877f2" elevation="4" ><v-icon color="white">mdi-facebook</v-icon></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #1877f2;">Facebook</span>
                            </v-col>
                          </v-col> -->
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="Login('Line')" color="#06C755" elevation="4" ><v-img :src="require('@/assets/ICON/Line_app.png')" max-height="30" max-width="30" contain/></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #06C755;">Line</span>
                            </v-col>
                          </v-col>
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3" @click="Login('Google')" elevation="4" ><v-img :src="require('@/assets/ICON/google.png')" max-height="24" max-width="24" contain/></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: red;">Google</span>
                            </v-col>
                          </v-col>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-10">
                          <v-row dense justify="center" align-content="center">
                            <span class="subTextLogin">{{ $t('Login.HaveAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Register')">{{ $t('Login.Register') }}</span></span>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                <!-- </v-tab-item> -->
              <!-- </v-tabs> -->
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div v-else-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <!-- <v-col cols="6" class="my-16">
            <v-img src="require('@/assets/icons/login_logo_Ipad.png')" contain max-width="100%" max-height="100%"></v-img>
          </v-col> -->
          <!-- <v-col cols="12" class="my-16" style="z-index: 1;"> -->
            <!-- height="514px" old -->
            <v-card width="60%" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
              <!-- <v-tabs
                color="#27AB9C"
                grow
              >
                <v-tab v-for="(item, index) in itemsLogin" :key="index">
                  <span style="padding-top: 0.5em; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item }}</span>
                </v-tab>
                <v-tab-item class="mt-6"> -->
                  <v-card-text>
                    <v-container>
                      <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                        <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
                      </v-row>
                      <v-row dense justify="center" align-content="center" class="mt-0 mb-8">
                        <span class="textLogin">{{ $t('Login.Welcome') }}</span>
                      </v-row>
                      <v-row v-if="showError" dense justify="center" align-content="center" class="mb-8">
                        <v-col cols="12" md="12" sm="12">
                          <v-card elevation="0" class="pa-4 mx-8" style="border: 1px solid #ebccd1; background-color: #f2dede;">
                            <ul class="ma-0 text-start">
                              <li style="color: #a94442; font-size: 14px;">{{texterror}}</li>
                            </ul>
                          </v-card>
                        </v-col>
                      </v-row>
                      <v-row no-gutters dense class="mx-8">
                        <!-- <h3 style="text-align: left; font-weight: 600;">Login with Username</h3> -->
                        <v-col cols="12" md="12" sm="12" class="mb-2">
                          <h3 style="text-align: left; font-size: 15px;">{{ $t('register.Username') }}</h3>
                          <v-text-field color="#269AFD" class="pb-4" v-model="username" :placeholder="$t('register.EnterUsername')" outlined dense hide-details append-icon="mdi-account"></v-text-field>
                          <h3 style="text-align: left; font-size: 15px;">{{ $t('register.Password') }}</h3>
                          <v-text-field color="#269AFD" :append-icon="Show ? 'mdi-eye' : 'mdi-eye-off'" :type="Show ? 'text' : 'password'" @click:append="Show = !Show" class="pb-4" v-model="password"
                          :placeholder="$t('register.EnterPassword')"  outlined dense hide-details @copy.prevent @keydown.space.prevent></v-text-field>
                          <span class="text-decoration-underline" style="float: right; display: inline-block; color: #269AFD; cursor: pointer;" @click="forgotPassword()">{{ $t('Login.ForgotPassword') }}</span>
                          <!-- <v-btn @click="Login('OneID')" color="#27AB9C" block outlined rounded elevation="4" class="styleButton"><v-img :src="require('@/assets/logo_one.png')" max-height="30" max-width="45" contain style="margin-right: 10px;"/>เข้าสู่ระบบด้วย one id</v-btn> -->
                          <!-- <v-btn @click="Login('TestOneID')" color="#27AB9C" block outlined rounded elevation="4" class="styleButton mt-3"><v-img :src="require('@/assets/logo_one.png')" max-height="30" max-width="45" contain style="margin-right: 10px;"/>เข้าสู่ระบบด้วย testone id</v-btn> -->
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="text-start">
                          <input type="checkbox" v-model="ConCheck" name="CheckBoxConsent" style="" />
                          <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-3">
                          <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Login('Username')" :disabled="ConCheck === false" >{{ $t('Login.Login') }}</v-btn>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-3">
                          <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="LoginTestOneID('Username')" :disabled="ConCheck === false" >{{ $t('Login.LoginOne') }}</v-btn>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-8">
                          <v-row dense align="center">
                            <v-divider class="mr-2"></v-divider>{{ $t('Login.Or') }}<v-divider class="ml-2"></v-divider>
                          </v-row>
                        </v-col>
                        <!-- <v-col cols="12" md="12" sm="12" class="mt-8">
                          <v-btn @click="LoginWithOTP()" color="#27AB9C" block outlined rounded elevation="4" class="styleButton">{{ $t('Login.LoginWithPhone') }}</v-btn>
                          <v-btn class="mt-3 white--text" @click="Login('Facebook')" color="#1877f2" block rounded elevation="4" style="border-radius: 32px;"><v-icon color="white" class="mr-2">mdi-facebook</v-icon>เข้าสู่ระบบด้วย FACEBOOK</v-btn>
                          <v-btn class="mt-3 white--text" @click="Login('Line')" color="#06C755" block rounded elevation="4" style="border-radius: 32px;"><v-img :src="require('@/assets/ICON/Line_app.png')" max-height="30" max-width="30" contain class="mr-2"/>เข้าสู่ระบบด้วย LINE</v-btn>
                          <v-btn class="mt-3" @click="Login('Google')" block outlined rounded elevation="4" style="border-radius: 32px;"><v-img :src="require('@/assets/ICON/google.png')" max-height="24" max-width="24" contain class="mr-2"/>เข้าสู่ระบบด้วย GOOGLE</v-btn>
                        </v-col> -->
                        <v-col cols="12" md="12" sm="12" class="mt-2 d-flex justify-center">
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="LoginWithOTP()" color="#FFFFFF" elevation="4" ><v-icon color="#269AFD">mdi-phone-in-talk</v-icon></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #269AFD;">OTP</span>
                            </v-col>
                          </v-col>
                          <!-- <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="Login('Facebook')" color="#1877f2" elevation="4" ><v-icon color="white">mdi-facebook</v-icon></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #1877f2;">Facebook</span>
                            </v-col>
                          </v-col> -->
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="Login('Line')" color="#06C755" elevation="4" ><v-img :src="require('@/assets/ICON/Line_app.png')" max-height="30" max-width="30" contain/></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #06C755;">Line</span>
                            </v-col>
                          </v-col>
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3" @click="Login('Google')" elevation="4" ><v-img :src="require('@/assets/ICON/google.png')" max-height="24" max-width="24" contain/></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: red;">Google</span>
                            </v-col>
                          </v-col>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-10">
                          <v-row dense justify="center" align-content="center">
                            <span style="font-size: 14px; line-height: 22px; color: #000000;">{{ $t('Login.HaveAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Register')">{{ $t('Login.Register') }}</span></span>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                <!-- </v-tab-item> -->
              <!-- </v-tabs> -->
            </v-card>
          <!-- </v-col> -->
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div v-if="MobileSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="12" md="12" class="d-flex justify-center my-8">
            <!-- height="510px" old -->
            <v-card width="480px" height="100%"  style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
                  <v-card-text>
                    <v-container>
                      <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                        <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
                      </v-row>
                      <v-row dense justify="center" align-content="center" class="mt-0 mb-8">
                        <span class="textLogin">{{ $t('Login.Welcome') }}</span>
                      </v-row>
                      <v-row v-if="showError" dense justify="center" align-content="center" class="mb-8">
                        <v-col cols="12" md="12" sm="12">
                          <v-card elevation="0" class="pa-4" style="border: 1px solid #ebccd1; background-color: #f2dede;">
                            <ul class="ma-0 text-start">
                              <li style="color: #a94442; font-size: 14px;">{{texterror}}</li>
                            </ul>
                          </v-card>
                        </v-col>
                      </v-row>
                      <v-row no-gutters dense>
                        <!-- <h3 style="text-align: left; font-weight: 600;">Login with Username</h3> -->
                        <v-col cols="12" md="12" sm="12" class="mb-2">
                          <h3 style="text-align: left; font-size: 15px;">{{ $t('register.Username') }}</h3>
                          <v-text-field color="#269AFD" class="pb-4" v-model="username" :placeholder="$t('register.EnterUsername')" outlined dense hide-details append-icon="mdi-account"></v-text-field>
                          <h3 style="text-align: left; font-size: 15px;">{{ $t('register.Password') }}</h3>
                          <v-text-field color="#269AFD" :append-icon="Show ? 'mdi-eye' : 'mdi-eye-off'" :type="Show ? 'text' : 'password'" @click:append="Show = !Show" class="pb-4" v-model="password"
                          :placeholder="$t('register.EnterPassword')" outlined dense hide-details @copy.prevent @keydown.space.prevent></v-text-field>
                          <span class="text-decoration-underline" style="float: right; display: inline-block; color: #269AFD; cursor: pointer;" @click="forgotPassword()">{{ $t('Login.ForgotPassword') }}</span>
                          <!-- <v-btn @click="Login('OneID')" color="#27AB9C" block outlined rounded elevation="4" class="styleButton"><v-img :src="require('@/assets/logo_one.png')" max-height="30" max-width="45" contain style="margin-right: 5px;"/>เข้าสู่ระบบด้วย one id</v-btn> -->
                          <!-- <v-btn @click="Login('TestOneID')" color="#27AB9C" block outlined rounded elevation="4" class="styleButton mt-3"><v-img :src="require('@/assets/logo_one.png')" max-height="30" max-width="45" contain style="margin-right: 5px;"/>เข้าสู่ระบบด้วย testone id</v-btn> -->
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="text-start">
                          <input type="checkbox" v-model="ConCheck" name="CheckBoxConsent" style="" />
                          <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-3">
                          <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Login('Username')" :disabled="ConCheck === false" >{{ $t('Login.Login') }}</v-btn>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-3">
                          <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="LoginTestOneID('Username')" :disabled="ConCheck === false" >{{ $t('Login.LoginOne') }}</v-btn>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-8">
                          <v-row dense align="center">
                            <v-divider class="mr-2"></v-divider>{{ $t('Login.Or') }}<v-divider class="ml-2"></v-divider>
                          </v-row>
                        </v-col>
                        <!-- <v-col cols="12" md="12" sm="12" class="mt-8">
                          <v-btn @click="LoginWithOTP()" color="#27AB9C" block outlined rounded elevation="4" class="styleButton">{{ $t('Login.LoginWithPhone') }}</v-btn>
                          <v-btn class="mt-3 white--text" @click="Login('Facebook')" color="#1877f2" block rounded elevation="4" style="border-radius: 32px;"><v-icon color="white" class="mr-2">mdi-facebook</v-icon>เข้าสู่ระบบด้วย FACEBOOK</v-btn>
                          <v-btn class="mt-3 white--text" @click="Login('Line')" color="#06C755" block rounded elevation="4" style="border-radius: 32px;"><v-img :src="require('@/assets/ICON/Line_app.png')" max-height="30" max-width="30" contain class="mr-2"/>เข้าสู่ระบบด้วย LINE</v-btn>
                          <v-btn class="mt-3" @click="Login('Google')" block outlined rounded elevation="4" style="border-radius: 32px;"><v-img :src="require('@/assets/ICON/google.png')" max-height="24" max-width="24" contain class="mr-2"/>เข้าสู่ระบบด้วย GOOGLE</v-btn>
                        </v-col> -->
                        <v-col cols="12" md="12" sm="12" class="mt-2 d-flex justify-center">
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="LoginWithOTP()" color="#FFFFFF" elevation="4" ><v-icon color="#269AFD">mdi-phone-in-talk</v-icon></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #269AFD;">OTP</span>
                            </v-col>
                          </v-col>
                          <!-- <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="Login('Facebook')" color="#1877f2" elevation="4" ><v-icon color="white">mdi-facebook</v-icon></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #1877f2;">Facebook</span>
                            </v-col>
                          </v-col> -->
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3 white--text" @click="Login('Line')" color="#06C755" elevation="4" ><v-img :src="require('@/assets/ICON/Line_app.png')" max-height="30" max-width="30" contain/></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: #06C755;">Line</span>
                            </v-col>
                          </v-col>
                          <v-col cols="3" class="pa-0">
                            <v-col cols="12" class="pb-1 d-flex justify-center">
                              <v-btn fab small class="mt-3" @click="Login('Google')" elevation="4" ><v-img :src="require('@/assets/ICON/google.png')" max-height="24" max-width="24" contain/></v-btn>
                            </v-col>
                            <v-col cols="12" class="pa-0 d-flex justify-center">
                              <span style="font-size: 12px; color: red;">Google</span>
                            </v-col>
                          </v-col>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" class="mt-10">
                          <v-row dense justify="center">
                            <span class="subTextLogin" style="text-align: center;">{{ $t('Login.HaveAccount') }} <br/><span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Register')">{{ $t('Login.Register') }}</span></span>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                <!-- </v-tab-item> -->
              <!-- </v-tabs> -->
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <v-dialog
    v-model="consentA"
    scrollable
    max-width="800px"
    :style="MobileSize ? 'z-index: ********' : ''"
  >
    <v-card style="overflow-x: hidden;">
      <v-card-title style="font-size: 18px; text-align:center;" class="d-flex justify-center">{{ $t('register.ServiceOneId') }}</v-card-title>
      <v-divider></v-divider>
      <v-card-text style="height: 100%;">
        <div v-html="detailA" style="padding: 15px 0;"></div>
      </v-card-text>
      <v-divider></v-divider>
    </v-card>
  </v-dialog>
  <v-dialog
    v-model="consentB"
    scrollable
    max-width="800px"
    :style="MobileSize ? 'z-index: ********' : ''"
  >
    <v-card style="overflow-x: hidden;">
      <v-card-title style="font-size: 18px; text-align:center;" class="d-flex justify-center">{{ $t('register.Policy') }}</v-card-title>
      <v-divider></v-divider>
      <v-card-text style="height: 100%;">
        <div v-html="detailB" style="padding: 15px 0;"></div>
      </v-card-text>
      <v-divider></v-divider>
    </v-card>
  </v-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { Encode } from '@/services'
export default {
  data () {
    return {
      Show: false,
      showError: false,
      texterror: '',
      ConCheck: false,
      detailA: '',
      detailB: '',
      consentA: false,
      consentB: false,
      lazy: false,
      username: '',
      password: '',
      Oneusername: '',
      Onepassword: '',
      show1: false,
      checkbox: false,
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        name: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้หรืออีเมล'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ],
        email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        confirmPasswordRules: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ]
      },
      itemsLogin: [
        'สำหรับผู้ซื้อทั่วไป',
        'สำหรับผู้ซื้อนิติบุลคล'
      ],
      tab: null,
      emailMarket: '',
      passwordMarket: '',
      checkClick: false,
      email: '',
      phone: '',
      items: [
        { header: 'เข้าสู่ระบบ' },
        { header: 'สมัครสมาชิก' }
      ],
      visibleLogin: false,
      visibleRegis: false
      // form: this.$form.createForm(this, { name: 'normal_login' }),
      // FormRegister: this.$form.createForm(this, { name: 'register' })
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    localStorage.removeItem('_sellerShop')
    localStorage.removeItem('_selectProduct')
    localStorage.removeItem('_productToCal')
    localStorage.removeItem('_selectDataCartFirstTime')
    localStorage.removeItem('_productListCartLoginFirstTime')
    this.$EventBus.$emit('main', this.$route.name)
    this.$EventBus.$emit('checkpage', this.$route.name)
    var CurrentPath = this.$router.currentRoute.path
    localStorage.setItem('CurrentPath', CurrentPath)
    this.GetPrivacyConsent()
    this.GetTermConsent()
  },
  methods: {
    LoginWitHealthID () {
      // step 1 : login Health ID
      window.location.assign(`${process.env.VUE_APP_REDIRECT_HEALTHID}`)
      // window.open('https://uat-moph.id.th/oauth/redirect?response_type=code&client_id=0197829c-8ba4-75f7-9af4-1a66d0ceebe9&redirect_uri=https://devinet-eprocurement.one.th/redirect_health')
    },
    forgotPassword () {
      this.$router.push({ path: '/ForgotPassword' }).catch(() => {})
    },
    async GetPrivacyConsent () {
      // Privacy
      await axios({
        url: 'https://one.th/api/privacy-policy-for-service?client_id=784',
        method: 'GET'
      }).then(
        (response) => {
          // console.log('detailB---->', response.data)
          this.detailB = response.data
        }
      )
    },
    async GetTermConsent () {
      // Term
      await axios({
        url: 'https://one.th/api/term-of-use-for-service?client_id=784',
        method: 'GET'
      }).then((response) => {
        // console.log('detailA---->', response.data)
        this.detailA = response.data
        // console.log(this.detailA)
      })
    },
    consent (val) {
      if (val === 'A') {
        this.consentA = true
      } else if (val === 'B') {
        this.consentB = true
      }
    },
    link (val) {
      this.$router.replace({ path: `/${val}` }).catch(() => {})
    },
    async encodePassword (password) {
      const CryptoJS = require('crypto-js')
      const phrase = 'tenmerucorpEDeveloper'
      // const iv = CryptoJS.enc.Utf8.parse('CGNecremmocnegxen') // Initialization vector
      const iv = CryptoJS.enc.Utf8.parse('CGNecremmocnegxe') // Initialization vector

      // Create a SHA-256 hash of the phrase to use as the secret key
      const secretKey = CryptoJS.SHA256(phrase).toString(CryptoJS.enc.Hex)

      // Encrypt the data
      const encryptedData = CryptoJS.AES.encrypt(password, CryptoJS.enc.Hex.parse(secretKey), {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })

      // Convert the encrypted data to Base64
      const encryptedBase64 = encryptedData.toString()
      return encryptedBase64
    },
    async Login (dataType) {
      // var CryptoJS = require('crypto-js')
      var onedata = {}
      if (dataType === 'Username') {
        this.$store.commit('openLoader')
        // Base64-encoded secret key
        // const base64Key = 'JrzHpeEQ9fY1TdLhqkexhQoQ2mM74otaJMt+Q5fahfM='
        // // const base64Key = 'tenmerucorpEDeveloper'
        // // Decode the Base64 key
        // const secretKey = CryptoJS.enc.Base64.parse(base64Key)
        // // IV ควรเป็น 16 characters (128 bits)
        // const iv = CryptoJS.enc.Utf8.parse('1234567890123456')
        // // Encrypt the token using AES with the decoded Base64 key
        // const encryptedToken = CryptoJS.AES.encrypt(this.password, secretKey, {
        //   iv: iv, // ระบุ IV ในการเข้ารหัส
        //   mode: CryptoJS.mode.CBC, // ใช้โหมด CBC
        //   padding: CryptoJS.pad.Pkcs7 // ใช้ padding แบบ PKCS7 (ค่าเริ่มต้น)
        // }).toString()
        const passwordEncode = await this.encodePassword(this.password)
        var data = {
          username: this.username,
          // password: this.password
          // password: encryptedToken
          password: passwordEncode
        }
        // console.log(data)
        await this.$store.dispatch('actionsLoginUsername', data)
        var response = await this.$store.state.ModuleRegister.stateLoginUsername
        if (response.result === 'SUCCESS') {
          if (response.data.access_token !== '') {
            // onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            localStorage.setItem('LoginTime', Date.now())
            // console.log('3', sessionStorage.getItem('isRefreshed'))
            onedata.user = response.data
            localStorage.setItem('oneData', Encode.encode(onedata))
          }
          var PathRedirect = ''
          if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
            PathRedirect = sessionStorage.getItem('pathRedirect')
            if (PathRedirect === '/Login' || PathRedirect === '/Register') {
              PathRedirect = '/'
            }
          } else {
            PathRedirect = '/'
          }
          var dataRole = {
            role: 'ext_buyer'
          }
          localStorage.setItem('roleUser', JSON.stringify(dataRole))
          this.$EventBus.$emit('LoginUser')
          this.$EventBus.$emit('checkPDPA')
          this.$EventBus.$emit('getCartPopOver')
          this.$EventBus.$emit('getItemNoti')
          this.$EventBus.$emit('checkChatMe')
          if (PathRedirect !== null) {
            this.$router.push({ path: `${PathRedirect}` }).catch(() => {})
          } else {
            this.$router.push({ path: '/' }).catch(() => {})
          }
          await this.$store.dispatch('actionsConGetBizDetail')
          var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
          if (responseBiz.message === 'updated data from one id successful.') {
            localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
          }
          this.$store.commit('closeLoader')
        } else if (response.result === 'FAILED') {
          if (response.message === 'The user credentials were incorrect.') {
            this.showError = true
            this.texterror = this.$t('Login.LoginFail1')
          } else if (response.message === 'username or password cannot be empty') {
            this.showError = true
            this.texterror = this.$t('Login.LoginFail2')
          } else {
            this.showError = true
            this.texterror = response.message
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: `<h3>${this.$t('Login.LoginFail3')}</h3>`
          })
        }
      } else if (dataType === 'OneID') {
        onedata = {}
        onedata.typeLoginOne = 'OneID'
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        if (this.$route.query.lineUserId !== undefined) {
          localStorage.setItem('lineUserId', this.$route.query.lineUserId)
        }
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      } else if (dataType === 'Facebook') {
        window.location.assign(`${process.env.VUE_APP_REDIRECT_FACKBOOK}`)
      } else if (dataType === 'Line') {
        window.location.assign(`${process.env.VUE_APP_REDIRECT_LINE}`)
      } else if (dataType === 'Google') {
        window.location.assign(`${process.env.VUE_APP_REDIRECT_GOOGLE}`)
      } else if (dataType === 'TestOneID') {
        onedata = {}
        onedata.typeLoginOne = 'TestOneID'
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        window.location.assign(`${process.env.VUE_APP_REDIRECT_TEST_ONE}`)
      } else {
        onedata = {}
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      }
    },
    async LoginTestOneID (dataType) {
      // var CryptoJS = require('crypto-js')
      var onedata = {}
      if (dataType === 'Username') {
        this.$store.commit('openLoader')
        // Base64-encoded secret key
        // const base64Key = 'JrzHpeEQ9fY1TdLhqkexhQoQ2mM74otaJMt+Q5fahfM='
        // // const base64Key = 'tenmerucorpEDeveloper'
        // // Decode the Base64 key
        // const secretKey = CryptoJS.enc.Base64.parse(base64Key)
        // // IV ควรเป็น 16 characters (128 bits)
        // const iv = CryptoJS.enc.Utf8.parse('1234567890123456')
        // // Encrypt the token using AES with the decoded Base64 key
        // const encryptedToken = CryptoJS.AES.encrypt(this.password, secretKey, {
        //   iv: iv, // ระบุ IV ในการเข้ารหัส
        //   mode: CryptoJS.mode.CBC, // ใช้โหมด CBC
        //   padding: CryptoJS.pad.Pkcs7 // ใช้ padding แบบ PKCS7 (ค่าเริ่มต้น)
        // }).toString()
        const passwordEncode = await this.encodePassword(this.password)
        var data = {
          username: this.username,
          // password: this.password
          // password: encryptedToken
          password: passwordEncode
        }
        // console.log(data)
        await this.$store.dispatch('actionsLoginUsernameTestOneID', data)
        var response = await this.$store.state.ModuleRegister.stateLoginUsernameTestOneID
        if (response.result === 'SUCCESS') {
          if (response.data.access_token !== '') {
            // onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            localStorage.setItem('LoginTime', Date.now())
            // console.log('3', sessionStorage.getItem('isRefreshed'))
            onedata.user = response.data
            localStorage.setItem('oneData', Encode.encode(onedata))
          }
          var PathRedirect = ''
          if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
            PathRedirect = sessionStorage.getItem('pathRedirect')
            if (PathRedirect === '/Login' || PathRedirect === '/Register') {
              PathRedirect = '/'
            }
          } else {
            PathRedirect = '/'
          }
          var dataRole = {
            role: 'ext_buyer'
          }
          localStorage.setItem('roleUser', JSON.stringify(dataRole))
          this.$EventBus.$emit('LoginUser')
          this.$EventBus.$emit('checkPDPA')
          this.$EventBus.$emit('getCartPopOver')
          this.$EventBus.$emit('getItemNoti')
          this.$EventBus.$emit('checkChatMe')
          if (PathRedirect !== null) {
            this.$router.push({ path: `${PathRedirect}` }).catch(() => {})
          } else {
            this.$router.push({ path: '/' }).catch(() => {})
          }
          await this.$store.dispatch('actionsConGetBizDetail')
          var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
          if (responseBiz.message === 'updated data from one id successful.') {
            localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
          }
          this.$store.commit('closeLoader')
        } else if (response.result === 'FAILED') {
          if (response.message === 'The user credentials were incorrect.') {
            this.showError = true
            this.texterror = this.$t('Login.LoginFail1')
          } else if (response.message === 'username or password cannot be empty') {
            this.showError = true
            this.texterror = this.$t('Login.LoginFail2')
          } else {
            this.showError = true
            this.texterror = response.message
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: `<h3>${this.$t('Login.LoginFail3')}</h3>`
          })
        }
      } else if (dataType === 'OneID') {
        onedata = {}
        onedata.typeLoginOne = 'OneID'
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        if (this.$route.query.lineUserId !== undefined) {
          localStorage.setItem('lineUserId', this.$route.query.lineUserId)
        }
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      } else if (dataType === 'Facebook') {
        window.location.assign(`${process.env.VUE_APP_REDIRECT_FACKBOOK}`)
      } else if (dataType === 'Line') {
        window.location.assign(`${process.env.VUE_APP_REDIRECT_LINE}`)
      } else if (dataType === 'Google') {
        window.location.assign(`${process.env.VUE_APP_REDIRECT_GOOGLE}`)
      } else if (dataType === 'TestOneID') {
        onedata = {}
        onedata.typeLoginOne = 'TestOneID'
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        window.location.assign(`${process.env.VUE_APP_REDIRECT_TEST_ONE}`)
      } else {
        onedata = {}
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      }
    },
    forgetPassword (val) {
      this.$router.push({ path: `/${val}` }).catch(() => {})
    },
    // async LoginMarket () {
    //   if (this.$refs.Loginform.validate(true)) {
    //     var data = {
    //       email: this.username,
    //       password: this.password
    //     }
    //     // console.log(data)
    //     var LoginData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/login_market`, data)
    //     console.log(LoginData.data)
    //     if (LoginData.data.result === 'SUCCESS') {
    //       this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
    //       // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //       var PathRedirect = localStorage.getItem('CurrentPath')
    //       if (PathRedirect === '/UPS' || PathRedirect === '/ups') {
    //         PathRedirect = 'UPS'
    //       } else if (PathRedirect === '/UPS/Register' || PathRedirect === '/UPS/Login') {
    //         PathRedirect = 'UPS'
    //       }
    //       var dataRole = {
    //         role: 'ext_buyer'
    //       }
    //       localStorage.setItem('roleUser', JSON.stringify(dataRole))
    //       var onedata = {}
    //       // console.log('onedata ====>', onedata)
    //       onedata.user = LoginData.data.data
    //       localStorage.setItem('oneData', Encode.encode(onedata))
    //       console.log(`${process.env.VUE_APP_DOMAIN}${PathRedirect}`)
    //       window.location.assign(`${process.env.VUE_APP_DOMAIN}${PathRedirect}`)
    //     } else {
    //       this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
    //       this.password = ''
    //     }
    //   }
    // }
    async LoginMarket (e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          var data = {
            email: values.emailMarket,
            password: values.passwordMarket
          }
          // console.log(data)
          var LoginData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/login_market`, data)
          // console.log(LoginData)
          if (LoginData.data.result === 'SUCCESS') {
            this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
            values.emailMarket = ''
            values.passwordMarket = ''
            this.form.resetFields()
            var dataRole = {
              role: 'ext_buyer'
            }
            localStorage.setItem('roleUser', JSON.stringify(dataRole))
            var onedata = {}
            // console.log('onedata ====>', onedata)
            onedata.user = LoginData.data.data
            localStorage.setItem('oneData', Encode.encode(onedata))
            await this.$store.dispatch('actionsConGetDizDetail')
            var response = await this.$store.state.ModuleRegister.stateGetBizDetail
            // console.log('tong res', response)
            if (response.message === 'updated data from one id successful.') {
              localStorage.setItem('BizDeartment', Encode.encode(response))
            }
            window.location.reload()
          } else {
            this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
            this.passwordMarket = ''
            this.openModalLogin()
          }
        }
      })
    },
    LoginWithOTP () {
      this.$router.push({ path: '/otpLogin' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.backgroundHealthID {
  background: #2b7654;
  background: linear-gradient(90deg, #2b7654 30%, #1f3c36 80%);
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: auto;
  color: #262626;
}
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
