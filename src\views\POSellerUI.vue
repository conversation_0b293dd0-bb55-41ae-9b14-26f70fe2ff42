<template>
    <POSeller/>
</template>
<script>
export default {
  components: {
    POSeller: () => import('@/components/Quotation/POSellerUI')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/posellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/poseller' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>
