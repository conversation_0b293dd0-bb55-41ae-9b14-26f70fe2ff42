import AxiosCart from '../store_Cart/axios_cart_api'

const ModuleCart = {
  state: {
    stateAddToCart: null,
    stateUpdateCart: null,
    stateDeleteAllProductCart: null,
    stateDetailCart: [],
    stateGetCart: [],
    stateGetCartV2: [],
    stateGetOrderEdit: [],
    stateEstimateEdit: [],
    stateGetCartSpecialPrice: null,
    stateCreateOrder: null,
    stateCreateOrderSpecialprice: null,
    stateCompanyPurchaser: null,
    stateCreateQuManual: null,
    stateGetAdminData: null,
    stateCreateInvoiceAddressPurchaser: null,
    stateCreateOrderSyncTaxaddress: null,
    stateGetPaymentPage: null,
    stateGetPaymentPageB2B: [],
    stateLocalstorageDetailCart: null,
    stateLocalstorageGetCart: null,
    stateLocalstorageCreateOrder: null,
    stateSentDataPPL: null,
    stateLocalstorageCheckAddToCart: null,
    // Preview QU
    statePreviewQU: [],
    stateGetETaxPDF: [],
    // ชำระแบบเงินสด
    statePayCashInStore: [],
    stateGetDocumentType: [],
    stateListItemCodePr: [],
    stateGetQRCode: [],
    stateGetQRCodeV2: [],
    stateCheckResultQRCode: [],
    stateCheckResultQRCodeV2: [],
    stateResponseFromTDCP: [],
    stateGetCC: [],
    stateGetCCV2: [],
    stateEstimateCost: [],
    statelistEstimateCostEdit: [],
    stateeditOrderQT: [],
    stateGetCompanyAddress: [],
    stateSetDefaultCompanyAddress: [],
    stateDeleteCompanyAddress: [],
    stateGetReviewQuotation: [],
    // check stock เฉพาะหน้า detail order
    stateCheckStockBeforePayment: [],
    // check stock ก่อน Create Order
    stateCheckStockBeforeCreateOrder: [],
    stateListCoupon: [],
    stateUseCoupon: [],
    stateEWTH: [],
    stateRecheck: [],
    stateRepeatOrder: [],
    stateCalculateQT: [],
    stateSearchInetRelation: [],
    stateCheckInetRelation: [],
    stateSearchCouponPlatform: [],
    stateListPlatformCoupon: [],
    stateSendEWHT: [],
    stateGetQRCodeB2B: [],
    stateCheckResultB2B: [],
    stateGetCCB2B: [],
    stateRedirectWHT: []
  },
  mutations: {
    mutationAddToCart (state, data) {
      state.stateAddToCart = data
    },
    mutationUpdateCart (state, data) {
      state.stateUpdateCart = data
    },
    mutationDeleteAllProductCart (state, data) {
      state.stateDeleteAllProductCart = data
    },
    mutationDetailCart (state, data) {
      state.stateDetailCart = data
    },
    mutationGetCart (state, data) {
      state.stateGetCart = data
    },
    mutationGetCartV2 (state, data) {
      state.stateGetCartV2 = data
    },
    mutationGetOrderEdit (state, data) {
      state.stateGetOrderEdit = data
    },
    mutationEstimateEdit (state, data) {
      state.stateEstimateEdit = data
    },
    mutationGetCartSpecialPrice (state, data) {
      state.stateGetCartSpecialPrice = data
    },
    mutationCreateOrder (state, data) {
      state.stateCreateOrder = data
    },
    mutationCreateOrderSpecialprice (state, data) {
      state.stateCreateOrderSpecialprice = data
    },
    // CreateInvoiceAddressPurchaser
    mutationCreateInvoiceAddressPurchaser (state, data) {
      state.stateCreateInvoiceAddressPurchaser = data
    },
    // confirmOrderLogin add Taxaddress sync CreateOrder
    mutationCreateOrderSyncTaxaddress (state, data) {
      state.stateCreateOrderSyncTaxaddress = data
    },
    mutationUPSCompanyPurchaser (state, data) {
      state.stateCompanyPurchaser = data
    },
    mutationCreateQuManual (state, data) {
      state.stateCreateQuManual = data
    },
    mutationGetAdminData (state, data) {
      state.stateGetAdminData = data
    },
    mutationGetPaymentPage (state, data) {
      state.stateGetPaymentPage = data
    },
    mutationGetPaymentPageB2B (state, data) {
      state.stateGetPaymentPageB2B = data
    },
    // localStorage
    mutationLocalstorageDetailCart (state, data) {
      state.stateLocalstorageDetailCart = data
    },
    mutationLocalstorageGetCart (state, data) {
      state.stateLocalstorageGetCart = data
    },
    mutationLocalstorageCreateOrder (state, data) {
      state.stateLocalstorageCreateOrder = data
    },
    mutationSentDataPPL (state, data) {
      state.stateSentDataPPL = data
    },
    mutationLocalstorageCheckAddToCart (state, data) {
      state.stateLocalstorageCheckAddToCart = data
    },
    // Preview QU
    mutationsPreviewQU (state, data) {
      state.statePreviewQU = data
    },
    mutationsGetETaxPDF (state, data) {
      state.stateGetETaxPDF = data
    },
    // ชำระแบบเงินสด
    mutationsPayCashInStore (state, data) {
      state.statePayCashInStore = data
    },
    mutationsGetDocumentType (state, data) {
      state.stateGetDocumentType = data
    },
    mutationsListItemCodePr (state, data) {
      state.stateListItemCodePr = data
    },
    mutationsGetQRCode (state, data) {
      state.stateGetQRCode = data
    },
    mutationsGetQRCodeV2 (state, data) {
      state.stateGetQRCodeV2 = data
    },
    mutationsCheckResultQRCode (state, data) {
      state.stateCheckResultQRCode = data
    },
    mutationsCheckResultQRCodeV2 (state, data) {
      state.stateCheckResultQRCodeV2 = data
    },
    mutationsResponseFromTDCP (state, data) {
      state.stateResponseFromTDCP = data
    },
    mutationsGetCC (state, data) {
      state.stateGetCC = data
    },
    mutationsGetCCV2 (state, data) {
      state.stateGetCCV2 = data
    },
    mutationsEstimateCost (state, data) {
      state.stateEstimateCost = data
    },
    mutationslistEstimateCostEdit (state, data) {
      state.statelistEstimateCostEdit = data
    },
    mutationseditOrderQT (state, data) {
      state.stateeditOrderQT = data
    },

    mutationsGetCompanyAddress (state, data) {
      state.stateGetCompanyAddress = data
    },
    mutationsSetDefaultCompanyAddress (state, data) {
      state.stateSetDefaultCompanyAddress = data
    },
    mutationsDeleteCompanyAddress (state, data) {
      state.stateDeleteCompanyAddress = data
    },
    mutationsGetReviewQuotation (state, data) {
      state.stateGetReviewQuotation = data
    },
    // check stock เฉพาะหน้า detail order
    mutationsCheckStockBeforePayment (state, data) {
      state.stateCheckStockBeforePayment = data
    },
    mutationsCheckStockBeforeCreateOrder (state, data) {
      state.stateCheckStockBeforeCreateOrder = data
    },
    mutationsListCoupon (state, data) {
      state.stateListCoupon = data
    },
    mutationsUseCoupon (state, data) {
      state.stateUseCoupon = data
    },
    mutationsEWTH (state, data) {
      state.stateEWTH = data
    },
    mutationsRecheck (state, data) {
      state.stateRecheck = data
    },
    mutationsRepeatOrder (state, data) {
      state.stateRepeatOrder = data
    },
    mutationsCalculateQT (state, data) {
      state.stateCalculateQT = data
    },
    mutationsSearchInetRelation (state, data) {
      state.stateSearchInetRelation = data
    },
    mutationsCheckInetRelation (state, data) {
      state.stateCheckInetRelation = data
    },
    mutationsSearchCouponPlatform (state, data) {
      state.stateSearchCouponPlatform = data
    },
    mutationsListPlatformCoupon (state, data) {
      state.stateListPlatformCoupon = data
    },
    mutationstateSendEWHT (state, data) {
      state.stateSendEWHT = data
    },
    mutationstateGetQRCodeB2B (state, data) {
      state.stateGetQRCodeB2B = data
    },
    mutationstateCheckResultB2B (state, data) {
      state.stateCheckResultB2B = data
    },
    mutationsGetCCB2B (state, data) {
      state.stateGetCCB2B = data
    },
    mutationsRedirectWHT (state, data) {
      state.stateRedirectWHT = data
    }
  },
  actions: {
    async ActionAddToCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.AddToCart(data)
      await contaxt.commit('mutationAddToCart', dataFromAxios)
    },
    async ActionUpdateCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.UpdateCart(data)
      await contaxt.commit('mutationUpdateCart', dataFromAxios)
    },
    async ActionDeleteAllProductCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.DeleteAllProductCart(data)
      await contaxt.commit('mutationDeleteAllProductCart', dataFromAxios)
    },
    async ActionDetailCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.DetailCart(data)
      await contaxt.commit('mutationDetailCart', dataFromAxios)
    },
    async ActionGetCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.GetCart(data)
      await contaxt.commit('mutationGetCart', dataFromAxios)
    },
    async ActionGetCartV2 (contaxt, data) {
      const dataFromAxios = await AxiosCart.GetCartV2(data)
      await contaxt.commit('mutationGetCartV2', dataFromAxios)
    },
    async ActionGetOrderEdit (contaxt, data) {
      const dataFromAxios = await AxiosCart.GetOrderEdit(data)
      await contaxt.commit('mutationGetOrderEdit', dataFromAxios)
    },
    async ActionEstimateEdit (contaxt, data) {
      const dataFromAxios = await AxiosCart.EstimateEdit(data)
      await contaxt.commit('mutationEstimateEdit', dataFromAxios)
    },
    async ActionGetCartSpecialPrice (contaxt, data) {
      const dataFromAxios = await AxiosCart.GetCartSpecialPrice(data)
      await contaxt.commit('mutationGetCartSpecialPrice', dataFromAxios)
    },
    async ActionCreateOrder (contaxt, data) {
      const dataFromAxios = await AxiosCart.CreateOrder(data)
      await contaxt.commit('mutationCreateOrder', dataFromAxios)
    },
    async ActionCreateOrderSpecialprice (contaxt, data) {
      const dataFromAxios = await AxiosCart.CreateOrderSpecialprice(data)
      await contaxt.commit('mutationCreateOrderSpecialprice', dataFromAxios)
    },
    // CreateInvoiceAddressPurchaser
    async ActionCreateInvoiceAddressPurchaser (contaxt, data) {
      const dataFromAxios = await AxiosCart.CreateInvoiceAddressPurchaser(data)
      await contaxt.commit('mutationCreateInvoiceAddressPurchaser', dataFromAxios)
    },
    // confirmOrderLogin add Taxaddress sync CreateOrder
    async ActionCreateOrderSyncTaxaddress (contaxt, access) {
      const responseData = await AxiosCart.CreateOrderSyncTaxaddress(access)
      await contaxt.commit('mutationCreateOrderSyncTaxaddress', responseData)
    },
    async ActionGetCompanyPurchaser (contaxt, data) {
      const dataFromAxios = await AxiosCart.GetCompanyPurchaser(data)
      await contaxt.commit('mutationUPSCompanyPurchaser', dataFromAxios)
    },
    async ActionCreateQuManual (contaxt, data) {
      const dataFromAxios = await AxiosCart.CreateQuManual(data)
      await contaxt.commit('mutationCreateQuManual', dataFromAxios)
    },
    async ActionGetAdminData (contaxt, data) {
      const dataFromAxios = await AxiosCart.GetAdminData(data)
      await contaxt.commit('mutationGetAdminData', dataFromAxios)
    },
    async ActionGetPaymentPage (contaxt, access) {
      const dataFromAxios = await AxiosCart.GetPaymentPage(access)
      await contaxt.commit('mutationGetPaymentPage', dataFromAxios)
    },
    async ActionGetPaymentPageB2B (contaxt, access) {
      const dataFromAxios = await AxiosCart.GetPaymentPageB2B(access)
      await contaxt.commit('mutationGetPaymentPageB2B', dataFromAxios)
    },
    // localsstorage
    async ActionLocalstorageDetailCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.LocalstorageDetailCart(data)
      await contaxt.commit('mutationLocalstorageDetailCart', dataFromAxios)
    },
    async ActionLocalstorageGetCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.LocalstorageGetCart(data)
      await contaxt.commit('mutationLocalstorageGetCart', dataFromAxios)
    },
    async ActionLocalstorageCreateOrder (contaxt, data) {
      const dataFromAxios = await AxiosCart.LocalstorageCreateOrder(data)
      await contaxt.commit('mutationLocalstorageCreateOrder', dataFromAxios)
    },
    // SentDataPaperless
    async ActionSentDataPPL (contaxt, data) {
      const dataFromAxios = await AxiosCart.CreateSentDataPPL(data)
      await contaxt.commit('mutationSentDataPPL', dataFromAxios)
    },
    async ActionLocalstorageCheckAddToCart (contaxt, data) {
      const dataFromAxios = await AxiosCart.LocalstorageCheckAddToCart(data)
      await contaxt.commit('mutationLocalstorageCheckAddToCart', dataFromAxios)
    },
    // Preview QU
    async ActionsPreviewQU (context, data) {
      const response = await AxiosCart.PreviewQU(data)
      await context.commit('mutationsPreviewQU', response)
    },
    async ActionsGetETaxPDF (context, data) {
      const response = await AxiosCart.GetETaxPDF(data)
      await context.commit('mutationsGetETaxPDF', response)
    },
    // ชำระแบบเงินสด
    async actionsPayCashInStore (context, data) {
      const response = await AxiosCart.PayCashInStore(data)
      await context.commit('mutationsPayCashInStore', response)
    },
    async actionsGetDocumentType (context, data) {
      const response = await AxiosCart.GetDocumentType(data)
      await context.commit('mutationsGetDocumentType', response)
    },
    async actionsListItemCodePr (context, data) {
      const response = await AxiosCart.ListItemCodePr(data)
      await context.commit('mutationsListItemCodePr', response)
    },
    async actionsGetQRCode (context, data) {
      const response = await AxiosCart.GetQRCode(data)
      await context.commit('mutationsGetQRCode', response)
    },
    async actionsGetQRCodeV2 (context, data) {
      const response = await AxiosCart.GetQRCodeV2(data)
      await context.commit('mutationsGetQRCodeV2', response)
    },
    async actionsCheckResultQRCode (context, data) {
      const response = await AxiosCart.CheckResultQRCode(data)
      await context.commit('mutationsCheckResultQRCode', response)
    },
    async actionsCheckResultQRCodeV2 (context, data) {
      const response = await AxiosCart.CheckResultQRCodeV2(data)
      await context.commit('mutationsCheckResultQRCodeV2', response)
    },
    async actionsResponseFromTDCP (context, data) {
      const response = await AxiosCart.ResponseFromTDCP(data)
      await context.commit('mutationsResponseFromTDCP', response)
    },
    async actionsGetCC (context, data) {
      const response = await AxiosCart.GetCC(data)
      await context.commit('mutationsGetCC', response)
    },
    async actionsGetCCV2 (context, data) {
      const response = await AxiosCart.GetCCV2(data)
      await context.commit('mutationsGetCCV2', response)
    },
    async actionsEstimateCost (context, data) {
      const response = await AxiosCart.EstimateCost(data)
      await context.commit('mutationsEstimateCost', response)
    },
    async actionslistEstimateCostEdit (context, data) {
      const response = await AxiosCart.listEstimateCostEdit(data)
      await context.commit('mutationslistEstimateCostEdit', response)
    },
    async actionseditOrderQT (context, data) {
      const response = await AxiosCart.editOrderQT(data)
      await context.commit('mutationseditOrderQT', response)
    },
    async actionsGetCompanyAddress (context, data) {
      const response = await AxiosCart.GetCompanyAddress(data)
      await context.commit('mutationsGetCompanyAddress', response)
    },
    async actionsSetDefaultCompanyAddress (context, data) {
      const response = await AxiosCart.SetDefaultCompanyAddress(data)
      await context.commit('mutationsSetDefaultCompanyAddress', response)
    },
    async actionsDeleteCompanyAddress (context, data) {
      const response = await AxiosCart.DeleteCompanyAddress(data)
      await context.commit('mutationsDeleteCompanyAddress', response)
    },
    async actionsGetReviewQuotation (context, data) {
      const response = await AxiosCart.GetReviewQuotation(data)
      await context.commit('mutationsGetReviewQuotation', response)
    },
    // check stock เฉพาะหน้า detail order
    async actionsCheckStockBeforePayment (context, data) {
      const response = await AxiosCart.CheckStockBeforePayment(data)
      await context.commit('mutationsCheckStockBeforePayment', response)
    },
    async actionsCheckStockBeforeCreateOrder (context, data) {
      const response = await AxiosCart.CheckStockBeforeCreateOrder(data)
      await context.commit('mutationsCheckStockBeforeCreateOrder', response)
    },
    async actionsListCoupon (context, data) {
      const response = await AxiosCart.ListCoupon(data)
      await context.commit('mutationsListCoupon', response)
    },
    async actionsUseCoupon (context, data) {
      const response = await AxiosCart.UseCoupon(data)
      await context.commit('mutationsUseCoupon', response)
    },
    async actionsEWTH (context, data) {
      const response = await AxiosCart.EWTH(data)
      await context.commit('mutationsEWTH', response)
    },
    async actionsRecheckCoupon (context, data) {
      const response = await AxiosCart.ReCheck(data)
      await context.commit('mutationsRecheck', response)
    },
    async actionsRepeatOrder (context, data) {
      const response = await AxiosCart.RepeatOrder(data)
      await context.commit('mutationsRepeatOrder', response)
    },
    async actionsCalculateQT (context, data) {
      const response = await AxiosCart.CalculateQT(data)
      await context.commit('mutationsCalculateQT', response)
    },
    async actionsSearchInetRelation (context, data) {
      const response = await AxiosCart.SearchInetRelation(data)
      await context.commit('mutationsSearchInetRelation', response)
    },
    async actionsCheckInetRelation (context, data) {
      const response = await AxiosCart.CheckInetRelation(data)
      await context.commit('mutationsCheckInetRelation', response)
    },
    async actionsSearchCouponPlatform (context, data) {
      const response = await AxiosCart.SearchCouponPlatform(data)
      await context.commit('mutationsSearchCouponPlatform', response)
    },
    async actionsListPlatformCoupon (context, data) {
      const response = await AxiosCart.ListPlatformCoupon(data)
      await context.commit('mutationsListPlatformCoupon', response)
    },
    async actionSendEWHT (context, data) {
      const response = await AxiosCart.SendEWHT(data)
      await context.commit('mutationstateSendEWHT', response)
    },
    async actionsGetQRCodeB2B (context, data) {
      const response = await AxiosCart.GetQRCodeB2B(data)
      await context.commit('mutationstateGetQRCodeB2B', response)
    },
    async actionsCheckResultB2B (context, data) {
      const response = await AxiosCart.CheckResultB2B(data)
      await context.commit('mutationstateCheckResultB2B', response)
    },
    async actionsGetCCB2B (context, data) {
      const response = await AxiosCart.GetCCB2B(data)
      await context.commit('mutationsGetCCB2B', response)
    },
    async actionsRedirectWHT (context, data) {
      const response = await AxiosCart.RedirectWHT(data)
      await context.commit('mutationsRedirectWHT', response)
    }
  }
}

export default ModuleCart
