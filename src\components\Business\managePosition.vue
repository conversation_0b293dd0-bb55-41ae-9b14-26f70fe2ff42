<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-row dense justify="center">
      <v-col cols="12" class="px-0 py-0">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense justify="center">
            <v-col cols="12" md="12" sm="12" xs="12">
              <v-card-title class="pb-0" style="font-weight: bold; font-size:24px; line-height: 32px;" v-if="!MobileSize">จัดการตำแหน่งและสิทธิ์การใช้งาน</v-card-title>
              <v-card-title class="px-0" style="font-weight: bold; font-size: 18px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> จัดการตำแหน่งและสิทธิ์การใช้งาน</v-card-title>
            </v-col>
          </v-row>
          <v-card-text>
            <v-row dense no-gutters class="mb-2">
              <v-col cols="12" class="py-0 mb-0">
                <a-tabs @change="selectTab">
                  <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                  <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ listPosition.length }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="1"><span slot="tab">ใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{ listPositionActive.length }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#FF0000" style="border-radius: 8px;">{{ listPositionInActive.length }}</a-tag></span></a-tab-pane>
                </a-tabs>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="6" sm="8" xs="12" class="mb-0 mt-0 py-0">
                <v-text-field
                v-model="search"
                dense
                outlined
                placeholder="ค้นหาจากตำแหน่งในบริษัท"
                >
                  <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="6" sm="3" xs="12" align="end" class="py-0" v-if="!IpadSize">
                <v-btn v-if="array_business.length !== 0 && managePosition === 'yes'" @click="showDialogRole()" :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
                <v-btn disabled v-else :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
              <v-col cols="3" align="end" class="py-0" v-if="IpadSize">
                <v-btn v-if="array_business.length !== 0 && managePosition === 'yes'" @click="showDialogRole()" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
                <v-btn disabled v-else color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
              <v-col v-if="disableTable === true" cols="12" md="12" sm="12" :class="MobileSize ? 'pl-4 pt-5 pb-0' :'pl-4 pt-0'">
                <span v-if="tab === 0" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" >รายการตำแหน่งในนิติบุคคลทั้งหมด {{ totalCount }} รายการ</span>
                <span v-else-if="tab === 1" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" >รายการตำแหน่งที่ใช้งานในนิติบุคคลทั้งหมด {{ totalCount }} รายการ</span>
                <span v-else-if="tab === 2" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" >รายการตำแหน่งที่ยกเลิกในนิติบุคคลทั้งหมด {{ totalCount }} รายการ</span>
              </v-col>
              <v-col cols="12" md="12" sm="12" xs="12" v-if="disableTable === true">
                <v-card outlined class="mb-4">
                  <v-data-table
                    @pagination="countRequest"
                    :search="search"
                    :headers=headersAll
                    :items="tab === 0 ? listPosition : tab === 1 ? listPositionActive : listPositionInActive"
                    no-results-text="ไม่พบตำแหน่งที่ค้นหา"
                    no-data-text="ไม่มีตำแหน่งในตาราง">
                    <template v-slot:[`item.created_at`]="{item}">
                      {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}
                    </template>
                    <template v-slot:[`item.active_status`]="{item}">
                      <v-chip :color="item.active_status === 'active' ? '#f0f9ee' : 'rgb(247, 217, 217)'">
                        <span v-if="item.active_status === 'active'" style="color: #27AB9C;">ใช้งาน</span>
                        <span v-if="item.active_status === 'inactive'" style="color: red;">ยกเลิก</span>
                      </v-chip>
                    </template>
                    <template v-slot:[`item.Company`]="{ item }">
                      <!-- <v-row dense justify="center">
                        <span
                          outlined
                          color="#27AB9C"
                          @click="ShowDetail(item, item.active_status, item.id)"
                          cclass="pt-4 pb-4"
                          style="cursor: pointer; color: #27AB9C;"
                        >
                        <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                          รายละเอียด <v-icon style="color: #27AB9C;">mdi-chevron-right</v-icon>
                        </span>
                      </v-row> -->
                      <v-row dense justify="center">
                        <span
                          outlined
                          color="#27AB9C"
                          @click="MobileSize ? detailPageMobile(item.id, item.position_name) : detailPage(item.id, item.position_name)"
                          class="pt-4 pb-4"
                          style="cursor: pointer; color: #27AB9C;"
                        >
                        <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                          รายละเอียด <v-icon style="color: #27AB9C;">mdi-chevron-right</v-icon>
                        </span>
                      </v-row>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" v-if="disableTable === false">
              <v-col cols="12" md="12" align="center">
                <div class="my-5">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px"
                    max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="tab === 0">
                  <span v-if="array_business.length !== 0" style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีการเพิ่มตำแหน่ง</span>
                  <span v-else style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีการเพิ่มตำแหน่ง</span>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="tab === 1">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีตำแหน่งที่ใช้งาน</span><br />
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="tab === 2">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีตำแหน่งที่ยกเลิก</span><br />
                </h2>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <!-- add role -->
    <v-dialog v-model="showDialogAddRole" :style="MobileSize ? 'z-index: 16000004' : ''" width="850px" scrollable content-class="elevation-0" persistent>
      <v-card width="100%" height="100%" class="rounded-xl" :style="MobileSize ? '' : 'overflow-y: hidden'">
        <v-toolbar dense elevation="0" color="#27AB9C">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #FFF;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #FFF; font-size: 16px;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialogRole()" icon><v-icon color="#FFF">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-form :lazy-validation="lazy">
          <v-container grid-list-xs>
            <v-card-text>
              <v-row class="pl-9" v-if="!MobileSize">
                <v-col cols="12" md="2" sm="2">
                  <v-img
                    lazy-src="@/assets/Businessman.png"
                    max-height="80"
                    max-width="180"
                    src="@/assets/Businessman.png"
                    contain
                  ></v-img>
                </v-col>
                <v-col cols="12" md="9" sm="8" class="pt-2" style="font-size: 18px;">
                  <!-- <span>กำหนดตำแหน่งและสิทธิ์การใช้งาน</span> -->
                  <v-col cols="12" md="12" sm="5" xs="12" class="pt-0">
                    <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" xs="12" class="pt-0">
                    <v-text-field :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                  </v-col>
                </v-col>
              </v-row>
              <v-divider v-if="!MobileSize" class="mt-3 mb-6"></v-divider>
              <v-row class="pl-9" v-if="!MobileSize">
                <v-col cols="6" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">การจัดการสิทธิ์และตำแหน่ง</span>
                  <v-switch
                    v-model="switch1"
                    inset
                    class="mt-0 pl-7"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch1 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
                <v-col cols="6" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">สิทธิ์ในการสร้างร้านค้า</span>
                  <v-switch
                    v-model="switch2"
                    inset
                    class="mt-0 pl-7"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch2 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
              </v-row>
              <v-divider v-if="!MobileSize" class=" mb-3"></v-divider>
              <v-row v-if="MobileSize">
                <v-col cols="3" md="2">
                  <v-img
                    lazy-src="@/assets/Businessman.png"
                    max-height="100"
                    max-width="200"
                    src="@/assets/Businessman.png"
                  ></v-img>
                </v-col>
                <v-col cols="9" md="10" class="pt-0" style="font-size: 14px; font-weight: bold">
                  <v-col cols="12" md="12" sm="2" xs="12" class="pt-0">
                    <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" xs="12" class="pt-0">
                    <v-text-field :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                  </v-col>
                </v-col>
              </v-row>
              <!-- <v-row v-if="!MobileSize" :class="MobileSize ? '' : 'mt-2 px-9'">
                <v-col cols="12" md="12" sm="2" xs="12" class="pt-2">
                  <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                </v-col>
              </v-row>
              <v-row dense no-gutters :class="MobileSize ? 'pt-3' : 'px-9 pb-3'">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <v-text-field :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                </v-col>
              </v-row> -->
              <v-divider v-if="MobileSize" class="mt-3 mb-6"></v-divider>
              <v-row v-if="MobileSize">
                <v-col cols="12" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">การจัดการสิทธิ์และตำแหน่ง</span>
                  <v-switch
                    v-model="switch1"
                    inset
                    class="mt-0 pl-4"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch1 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
                <v-col cols="12" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">สิทธิ์ในการสร้างร้านค้า</span>
                  <v-switch
                    v-model="switch2"
                    inset
                    class="mt-0 pl-10"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch2 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
              </v-row>
              <v-divider v-if="MobileSize" class="mb-8"></v-divider>
              <v-card v-if="MobileSize" class="mt-1 elevation-0" style="height: 46px; width: 100%; background-color: #F3F5F7; border-radius: 12px;" >
                <v-row justify="center">
                  <v-col cols="4" class="pr-0 pt-1">
                    <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onSelected(1)">
                      <v-row class="d-flex d-inline-block justify-center align-center">
                        <span style="font-size: 16px; color: #27AB9C;">บริษัท</span>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col cols="4" class="pl-0 pr-0 pt-1">
                    <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onSelected(2)">
                      <v-row class="d-flex d-inline-block justify-center align-center">
                        <span style="font-size: 16px; color: #27AB9C;">ร้านค้า</span>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card>
              <v-row justify="center" class="mt-0">
                <v-col v-if="MobileSize && selectType === 1" cols="12">
                  <v-card outlined class="pt-2" style="border-color: #27AB9C; border-width: 3px; overflow-y: auto;" height="270">
                    <v-row no-gutters justify="center">
                      <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center pa-0">
                        <span style="font-weight: bold; font-size: 16px;">บริษัท</span>
                      </v-col>
                      <v-row justify="center" class="mt-2">
                        <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                          <v-select
                            v-model="selectedCompany"
                            item-text="name_th"
                            item-value="company_id"
                            :items = "listSelectedCompany"
                            chips
                            placeholder="เลือกบริษัท"
                            multiple
                            solo
                            dense
                            scrollable
                            hide-details
                            no-data-text="ไม่พบบริษัท"
                            :menu-props="{ maxHeight: '230px'}"
                          >
                          <template v-slot:selection="{ item, index }">
                            <v-chip v-if="index === 0">
                              <span style="font-size: 12px">{{ item.name_th }}</span>
                            </v-chip>
                            <span
                              v-if="index === 1"
                              class="grey--text text-caption"
                              style="font-size: 12px"
                            >
                              (+{{ selectedCompany.length - 1 }} อื่นๆ)
                            </span>
                          </template>
                        </v-select>
                        </v-col>
                      </v-row>
                      <v-row dense class="mt-3" v-if="selectedCompany.length !== 0" justify="center">
                        <v-col cols="12" v-for="(item, index) in testArray" :key="index" class="d-flex justify-center">
                          <v-card class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="275px" >
                            <div v-for="(item2, index) in listSelectedCompany" :key="index">
                              <v-chip class="mt-3 ml-2" v-if="item2.company_id === item.company_id">
                                <span style="font-size: 14px">
                                  {{ item2.name_th }}
                                </span>
                              </v-chip>
                            </div>
                            <v-row dense class="pl-3 pt-3">
                              <v-col :cols="MobileSize ? '9' : '9'" md="9">
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  ตรวจสอบบุคคลากรบริษัท
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  ตรวจสอบรายการสั่งซื้อของบริษัท
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  แก้ไขสิทธิ์ภายในบริษัท
                                </v-list-item-content>
                              </v-col>
                              <v-spacer></v-spacer>
                              <v-col cols="2" class="justify-content-between">
                                <v-checkbox :ripple="false" v-model="item.access_company_member" style="margin-Top: -7px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox :ripple="false" v-model="item.access_company_order" style="margin-Top: 5px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox :ripple="false" v-model="item.assign_company_permission" style="margin-Top: 5px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                              </v-col>
                            </v-row>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-row>
                  </v-card>
                </v-col>
                <v-col v-if="!MobileSize" cols="6">
                  <v-card outlined class="pt-2 scroll-card" style="border-color: #27AB9C; border-width: 3px;" height="290">
                    <v-row no-gutters justify="center">
                      <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center pa-0">
                        <span style="font-weight: bold; font-size: 16px;">บริษัท</span>
                      </v-col>
                      <v-row justify="center" class="mt-2">
                        <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                          <v-select
                            v-model="selectedCompany"
                            item-text="name_th"
                            item-value="company_id"
                            :items = "listSelectedCompany"
                            chips
                            placeholder="เลือกบริษัท"
                            multiple
                            solo
                            dense
                            scrollable
                            hide-details
                            no-data-text="ไม่พบบริษัท"
                            :menu-props="{ maxHeight: '230px'}"
                          >
                          <template v-slot:selection="{ item, index }">
                            <v-chip v-if="index === 0">
                              <span>{{ item.name_th }}</span>
                            </v-chip>
                            <span
                              v-if="index === 1"
                              class="grey--text text-caption"
                            >
                              (+{{ selectedCompany.length - 1 }} อื่นๆ)
                            </span>
                          </template>
                        </v-select>
                        </v-col>
                      </v-row>
                      <v-row dense class="mt-3" v-if="selectedCompany.length !== 0" justify="center">
                        <v-col cols="12" v-for="(item, index) in testArray" :key="index" class="d-flex justify-center">
                          <v-card class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="330px">
                            <div v-for="(item2, index) in listSelectedCompany" :key="index">
                              <v-chip class="mt-3 ml-2" v-if="item2.company_id === item.company_id">
                                <span>
                                  {{ item2.name_th }}
                                </span>
                              </v-chip>
                            </div>
                            <v-row dense class="pl-3 pt-3 pb-2">
                              <v-col cols="9" md="9">
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  ตรวจสอบบุคคลากรบริษัท
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  ตรวจสอบรายการสั่งซื้อของบริษัท
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  แก้ไขสิทธิ์ภายในบริษัท
                                </v-list-item-content>
                              </v-col>
                              <v-spacer></v-spacer>
                              <v-col cols="2">
                                <v-checkbox v-model="item.access_company_member" style="margin-Top: -7px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox v-model="item.access_company_order" style="margin-Top: -1px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox v-model="item.assign_company_permission" style="margin-Top: -1px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                              </v-col>
                            </v-row>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-row>
                  </v-card>
                </v-col>
                <v-col v-if="MobileSize && selectType === 2" cols="12">
                  <v-card outlined class="pt-2" style="border-color: #27AB9C; border-width: 3px; overflow-y: auto;" height="270">
                    <v-row no-gutters justify="center">
                      <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center">
                        <span style="font-weight: bold; font-size: 16px;">ร้านค้า</span>
                      </v-col>
                      <v-row justify="center" class="mt-2">
                        <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                          <v-select
                            v-model="selectedShop"
                            item-text="name_th"
                            item-value="shop_id"
                            :items = "listSelectedShop"
                            chips
                            placeholder="เลือกร้านค้า"
                            multiple
                            solo
                            dense
                            hide-details
                            no-data-text="ไม่พบร้านค้า"
                            :menu-props="{ maxHeight: '230px' }"
                          >
                          <template v-slot:selection="{ item, index }">
                            <v-chip v-if="index === 0">
                              <span>{{ item.name_th }}</span>
                            </v-chip>
                            <span
                              v-if="index === 1"
                              class="grey--text text-caption"
                            >
                              (+{{ selectedShop.length - 1 }} อื่นๆ)
                            </span>
                          </template>
                        </v-select>
                        </v-col>
                      </v-row>
                      <v-row dense class="mt-3" v-if="selectedShop.length !== 0" justify="center">
                        <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in testArrayShop" :key="index" class="d-flex justify-center">
                          <v-card class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="275px">
                            <div v-for="(item2, index) in listSelectedShop" :key="index">
                              <v-chip class="mt-3 ml-2" v-if="item2.shop_id === item.seller_shop_id">
                                <span>
                                  {{ item2.name_th }}
                                </span>
                              </v-chip>
                            </div>
                            <v-row dense class="pl-3 pt-3">
                              <v-col cols="10">
                                <!-- <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  ตรวจสอบรายการสินค้าของร้านค้า
                                </v-list-item-content> -->
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  ตรวจสอบสมาชิกภายในร้านค้า
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  ตรวจสอบรายการสินค้าของร้านค้า
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                  แก้ไขสิทธิ์ภายในร้านค้า
                                </v-list-item-content>
                              </v-col>
                              <v-spacer></v-spacer>
                              <v-col cols="2">
                                <!-- <v-checkbox :ripple="false" v-model="item.access_shop_product" style="margin-Top: -7px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox> -->
                                <v-checkbox :ripple="false" v-model="item.access_shop_user" style="margin-Top: 5px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox :ripple="false" v-model="item.access_shop_order" style="margin-Top: 5px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox :ripple="false" v-model="item.assign_shop_permission" style="margin-Top: 5px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                              </v-col>
                            </v-row>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-row>
                  </v-card>
                </v-col>
                <v-col v-if="!MobileSize" cols="6">
                  <v-card outlined class="pt-2 scroll-card" style="border-color: #27AB9C; border-width: 3px;" height="290">
                    <v-row no-gutters justify="center">
                      <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center">
                        <span style="font-weight: bold; font-size: 16px;">ร้านค้า</span>
                      </v-col>
                      <v-row justify="center" class="mt-2">
                        <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                          <v-select
                            v-model="selectedShop"
                            item-text="name_th"
                            item-value="shop_id"
                            :items = "listSelectedShop"
                            chips
                            placeholder="เลือกร้านค้า"
                            multiple
                            solo
                            dense
                            hide-details
                            no-data-text="ไม่พบร้านค้า"
                            :menu-props="{ maxHeight: '230px' }"
                          >
                          <template v-slot:selection="{ item, index }">
                            <v-chip v-if="index === 0">
                              <span>{{ item.name_th }}</span>
                            </v-chip>
                            <span
                              v-if="index === 1"
                              class="grey--text text-caption"
                            >
                              (+{{ selectedShop.length - 1 }} อื่นๆ)
                            </span>
                          </template>
                        </v-select>
                        </v-col>
                      </v-row>
                      <v-row dense class="mt-3" v-if="selectedShop.length !== 0" justify="center">
                        <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in testArrayShop" :key="index" class="d-flex justify-center">
                          <v-card class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="330px">
                            <div v-for="(item2, index) in listSelectedShop" :key="index">
                              <v-chip class="mt-3 ml-2" v-if="item2.shop_id === item.seller_shop_id">
                                <span>
                                  {{ item2.name_th }}
                                </span>
                              </v-chip>
                            </div>
                            <v-row dense class="pl-3 pt-3 pb-2">
                              <v-col cols="9" md="9" sm="9" xs="9">
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  ตรวจสอบสมาชิกภายในร้านค้า
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  ตรวจสอบรายการสั่งซื้อของร้านค้า
                                </v-list-item-content>
                                <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  แก้ไขสิทธิ์ภายในร้านค้า
                                </v-list-item-content>
                                <!-- <v-list-item-content
                                  style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                  แก้ไขสิทธิ์ภายในร้านค้า
                                </v-list-item-content> -->
                              </v-col>
                              <v-col cols="3" md="3" sm="3" xs="3" class="pl-10">
                                <!-- <v-checkbox v-model="item.access_shop_product" style="margin-Top: -7px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox> -->
                                <v-checkbox v-model="item.access_shop_user" style="margin-Top: -1px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox v-model="item.access_shop_order" style="margin-Top: -1px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                <v-checkbox v-model="item.assign_shop_permission" style="margin-Top: -1px" dense
                                  color="#27AB9C" hide-details class="shrink"></v-checkbox>
                              </v-col>
                            </v-row>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <v-row no-gutters class="mt-4" justify="end">
                <v-btn @click="closeDialogRole()" outlined color="#27AB9C" class="mr-5">ยกเลิก</v-btn>
                <v-btn @click="submitCreate()" color="#27AB9C" class="white--text">บันทึก</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-form>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      managePosition: '',
      switch1: false,
      switch2: false,
      array_business: [],
      totalCount: 0,
      selectType: 1,
      testArrayShop: [],
      testArray: [],
      disableTable: false,
      positionName: '',
      taxId: '',
      roleId: null,
      status: '',
      checkList: [],
      search: '',
      listPosition: [],
      listPositionActive: [],
      listPositionInActive: [],
      listCompany: [],
      listSelectedCompany: [],
      selectedCompany: [],
      itemsCompany: [],
      listShop: [],
      listSelectedShop: [],
      selectedShop: [],
      lazy: false,
      showDialogAddRole: false,
      showDetailCompany: false,
      showDetailShop: false,
      tab: 0,
      headersAll: [
        { text: 'ตำแหน่ง', value: 'position_name', sortable: false, width: '100', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'created_at', filterable: false, sortable: false, align: 'center', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'active_status', filterable: false, width: '100', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', value: 'Company', filterable: false, width: '150', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ]
    }
  },
  methods: {
    countRequest (pagination) {
      this.totalCount = pagination.itemsLength
    },
    onSelected (val) {
      this.selectType = val
    },
    selectTab (val) {
      this.search = ''
      this.tab = val
      if (this.tab === 0 && this.listPosition.length !== 0) {
        this.disableTable = true
      } else if (this.tab === 1 && this.listPositionActive.length !== 0) {
        this.disableTable = true
      } else if (this.tab === 2 && this.listPositionInActive.length !== 0) {
        this.disableTable = true
      } else {
        this.disableTable = false
      }
    },
    showDialogRole () {
      this.showDialogAddRole = true
    },
    closeDialogRole () {
      this.switch1 = false
      this.switch2 = false
      this.positionName = ''
      this.selectedCompany = []
      this.selectedShop = []
      this.showDialogAddRole = false
    },
    async listPositionTable (id) {
      this.$store.commit('openLoader')
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListPositions
      this.managePosition = response.data.manage_position
      if (response.code === 200) {
        if (response.data.owner_business === 'yes') {
          this.listPosition = response.data.list_positions
          this.listPositionActive = response.data.list_positions.filter(position => position.active_status === 'active')
          this.listPositionInActive = response.data.list_positions.filter(position => position.active_status === 'inactive')
          if (this.tab === 0 && this.listPosition.length === 0) {
            this.disableTable = false
          } else if (this.tab === 1 && this.listPositionActive.length === 0) {
            this.disableTable = false
          } else if (this.tab === 2 && this.listPositionInActive.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (response.data.owner_business === 'no') {
          this.listPosition = response.data.list_positions.filter(position => position.position_name !== 'เจ้าของนิติบุคคล')
          this.listPositionActive = response.data.list_positions.filter(position => position.active_status === 'active' && position.position_name !== 'เจ้าของนิติบุคคล')
          this.listPositionInActive = response.data.list_positions.filter(position => position.active_status === 'inactive' && position.position_name !== 'เจ้าของนิติบุคคล')
          if (this.tab === 0 && this.listPosition.length === 0) {
            this.disableTable = false
          } else if (this.tab === 1 && this.listPositionActive.length === 0) {
            this.disableTable = false
          } else if (this.tab === 2 && this.listPositionInActive.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
        // if (this.tableData.length > 0) {
        //   this.disableTable = false
        // }
      } else {
        this.disableTable = false
      }
      this.$store.commit('closeLoader')
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          this.array_business = response.data.array_business
          // this.taxId = response.data.array_business[0].owner_tax_id
          await this.listPositionTable(this.taxId)
          await this.getListCompany(this.taxId)
          await this.getListShop(this.taxId)
        }
        this.$store.commit('closeLoader')
      } else {
        this.array_business = response.data.array_business
        this.disableTable = false
        this.$swal.fire({
          icon: 'error',
          text: 'ยังไม่อนุมัติ',
          showConfirmButton: false
        })
        this.$store.commit('closeLoader')
      }
    },
    async getListCompany (id) {
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListCompanyPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListCompanyPositions
      if (response.code === 200) {
        this.listSelectedCompany = response.data.companies.map(company => ({
          company_id: company.company_id,
          name_th: company.name_th
        }))
      }
    },
    async getListShop (id) {
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListShopPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListShopPositions
      if (response.code === 200) {
        this.listSelectedShop = response.data.shops.map(shop => ({
          shop_id: shop.seller_shop_id,
          name_th: shop.name_th
        }))
      }
    },
    async submitCreate () {
      var data = {
        tax_id: this.taxId,
        assigned_position_name: this.positionName,
        manage_position: this.switch1,
        manage_business: this.switch2,
        shop_permission: this.testArrayShop,
        company_permission: this.testArray
      }
      this.showDialogAddRole = false
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsCreateRole', data)
      this.$store.commit('closeLoader')
      var response = await this.$store.state.ModuleBusiness.stateCreateRole
      if (response.result === 'SUCCESS') {
        this.listPositionTable(this.taxId)
        this.$swal.fire({
          icon: 'success',
          text: 'สร้างรายละเอียดตำแหน่งและสิทธิ์การใช้งานสำเร็จ',
          showConfirmButton: false,
          timer: 1300
        })
        // window.location.reload()
      } else if (response.result === 'FAILD' || response.result === 'FAILED') {
        this.listPositionTable(this.taxId)
        if (response.message === 'business not found for tax id.') {
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่พบ tax id',
            showConfirmButton: false,
            timer: 1300
          })
        } else if (response.message === 'Position name is duplicate.') {
          this.listPositionTable(this.taxId)
          this.$swal.fire({
            icon: 'error',
            text: 'ชื่อตำแหน่งที่ใส่มาซ้ำ',
            showConfirmButton: false,
            timer: 1300
          })
        } else if (response.message === "You don't have permission in shop or company.") {
          this.listPositionTable(this.taxId)
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่มีสิทธิ์ใน ร้านค้า หรือบริษัท',
            showConfirmButton: false,
            timer: 1300
          })
        } else if (response.message === 'You did not enter a position name.') {
          this.listPositionTable(this.taxId)
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่ได้กรอกชื่อตำแหน่ง',
            showConfirmButton: false,
            timer: 1300
          })
        } else if (response.message === "You can't create this position name.") {
          this.listPositionTable(this.taxId)
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สามารถสร้างตำแหน่งเจ้าของนิติบุคคลได้',
            showConfirmButton: false,
            timer: 1300
          })
        }
      } else if (response.result === 'SERVER ERROR') {
        this.listPositionTable(this.taxId)
        this.$swal.fire({
          icon: 'error',
          text: 'An error has occurred. Please try again in an hour or two',
          showConfirmButton: false,
          timer: 1300
        })
      }
      this.positionName = ''
      this.selectedCompany = []
      this.selectedShop = []
    },
    detailPage (id, position) {
      localStorage.setItem('paramID', id)
      localStorage.setItem('paramName', position)
      this.$router.push(`/detailPositionCompanyShop?id=${id}&position=${position}`)
    },
    detailPageMobile (id, position) {
      localStorage.setItem('paramID', id)
      localStorage.setItem('paramName', position)
      this.$router.push(`/detailPositionCompanyShopMobile?id=${id}&position=${position}`)
    },
    backtoUserMenu () {
      this.$router.push('/detailbusinesssidMobileMenu')
    }
  },
  watch: {
    selectedCompany () {
    // ตรวจสอบและอัปเดตค่าของบริษัทที่ถูกเลือกใน testArray
      this.selectedCompany.forEach(company => {
        const existingCompany = this.testArray.find(item => item.company_id === company)
        if (!existingCompany) {
          // ถ้าบริษัทนี้ยังไม่มีใน testArray ให้เพิ่มเข้าไป
          this.testArray.push({
            company_id: company,
            access_company_member: false,
            access_company_order: false,
            assign_company_permission: false
          })
        }
      })
      // ลบบริษัทที่ไม่ได้ถูกเลือกใน selectedCompany ออกจาก testArray
      this.testArray = this.testArray.filter(item => this.selectedCompany.includes(item.company_id))
    },
    selectedShop () {
      this.selectedShop.forEach(shop => {
        const existingShop = this.testArrayShop.find(item => item.seller_shop_id === shop)
        if (!existingShop) {
          // ถ้าบริษัทนี้ยังไม่มีใน y ให้เพิ่มเข้าไป
          this.testArrayShop.push({
            seller_shop_id: shop,
            access_shop_product: false,
            access_shop_order: false,
            access_shop_user: false,
            assign_shop_permission: false
          })
        }
      })
      // ลบบริษัทที่ไม่ได้ถูกเลือกใน selectedShop ออกจาก testArrayShop
      this.testArrayShop = this.testArrayShop.filter(item => this.selectedShop.includes(item.seller_shop_id))
    },
    MobileSize (val) {
      // console.log(this.MobileSize, 'desktop')
      if (val === true) {
        this.$router.push({ path: '/managePositionComapny&BussinessMobile' }).catch(() => {})
      } else {
        localStorage.setItem('pathBusiness', 'managePositionComapny&Bussiness')
        this.$router.push({ path: '/managePositionComapny&Bussiness' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    }
  },
  created () {
    // this.listPositionTable()
    this.$EventBus.$emit('changeNavBusiness')
    this.getTaxId()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>

<!-- <style scoped>
.scroll-card {
  height: 450px; /* Set a fixed height */
  overflow-y: auto; /* Enable vertical scroll */
}
</style> -->

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style lang="scss" scoped>
::v-deep .swal-title {
  margin: 0px;
  font-size: 16px;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.21);
  margin-bottom: 28px;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
}
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.max-v-list-height {
  max-height: 10px;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.scroll-card {
  height: 260px; /* Set a fixed height */
  overflow-y: auto;
  overflow-x: hidden;
}
.scroll-card::-webkit-scrollbar {
  width: 8px;
}
.scroll-card::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}
.scroll-card::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
.scroll-card::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
}
</style>
