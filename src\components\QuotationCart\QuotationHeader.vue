<template>
  <v-container grid-list-xs>
    <v-row>
      <v-col cols="12" md="12" class="pt-0">
        <v-row dense v-if="orderType === 'quotation'">
          <v-col cols="12" style="text-align: center">
            <b style="font-family: 'Sarabun' !important; font-size: 16px">{{ procurementData.procurement_org_name_en }}</b><br>
            <p class="header_2">
              {{ companyaddress_th }}&nbsp;
              {{ $t("Tel.name") }}&nbsp;&nbsp;{{ tel_fixed }}
              {{ $t("fax.name") }}&nbsp;&nbsp;{{ fax_fixed }}
              {{ $t("TaxID.name") }}&nbsp;&nbsp;{{ tax_id_fixed }}
            </p>
          </v-col>
        </v-row>
        <v-row dense v-else-if="orderType === 'purchase'">
          <v-col cols="12" style="text-align: center">
            <b style="font-family: 'Sarabun' !important; font-size: 16px">{{ procurementData.procurement_org_name_en }}</b><br>
            <span class="header_2">
              {{ address_cart }}&nbsp;<br>
              {{ $t("Tel.name") }}&nbsp;{{ procurementData.procurement_org_tel }}&nbsp;
              {{ $t("fax.name") }}&nbsp;{{ procurementData.procurement_org_fax }}&nbsp;
              {{ $t("TaxID.name") }}&nbsp;{{ procurementData.procurement_org_tax_id }}
            </span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="12" class="pt-0">
        <v-row dense>
          <v-col cols="4" class="pt-0 pb-0">
            <span class="header_a">{{ $t("OrderPurchaser.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
          </v-col>
          <v-col cols="4" class="pt-0 pb-0">
            <span class="header_a">{{ $t("Shipping.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
          </v-col>
          <v-col cols="4" class="pt-0 pb-0">
            <span class="header_a">{{ $t("OrderDetail.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="12" class="pt-1">
        <v-row dense>
          <v-col cols="4" class="pa-0">
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("PurchaserName.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ user_name_en }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("CustomerCode.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ procurementData.procurement_org_code }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("Tel.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ userData.phone }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("EmailBuyer.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ userData.email }}</span>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="4" class="pa-0">
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("CompanyCode.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ procurementData.procurement_org_code }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("CompanyName.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ procurementData.procurement_org_name_th }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("ContactName.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">-</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("Shipping.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">-</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("Tel.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-flex cols="6" class="pt-0">
                <span class="header_2">{{ procurementData.procurement_org_phone }}</span>
              </v-flex>
            </v-row>
          </v-col>
          <v-col cols="4" class="pa-0">
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("CompanyName.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">{{ cartData.procurement_org_name_en }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("Taxinvoiceaddress.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0">
                <span class="header_2">-</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6" class="pt-0" style="text-align: right">
                <span class="header_2">{{ $t("TaxID.name") }}&nbsp;&nbsp;:&nbsp;&nbsp;</span>
              </v-col>
              <v-col cols="6" class="pt-0" style="text-align: left">
                <span class="header_2">{{ procurementData.procurement_org_tax_id }}</span>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      companyname_th: 'บริษัท อินเทอร์เน็ตประเทศไทย จำกัด (มหาชน)',
      companyname_en: 'Internet Thailand Public Company Limited',
      companyaddress_th: '1768 อาคารไทยซัมมิท ทาวเวอร์ ชั้น 10-12 และชั้น IT ถ.เพชรบุรีตัดใหม่ แขวงบางกะปิ เขตห้วยขวาง กรุงเทพมหานคร 10310',
      companyaddress_en: '1768 Thai Summit Tower, 10th -12th Floor and IT Floor New Petchaburi Road, Khwaeng Bang Kapi, Khet Huay Khwang, Bangkok 10310',
      tel_fixed: '0 2257 7000',
      fax_fixed: '0 2257 7111',
      tax_id_fixed: '12345678907654',
      cartData: '',
      address_cart: '',
      userData: '',
      procurementData: ''
    }
  },
  created () {
    this.getOrderDetail()
  },
  methods: {
    getOrderDetail () {
      const data = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('PDF_Data'))))
      this.cartData = data
      // console.log('this.cartData', this.cartData)
      this.procurementData = this.cartData.procurement_data
      const addressCart = data.cartData.address_cart
      this.address_cart = addressCart.house_no + ' ' + addressCart.detail + ' ' + addressCart.sub_district + ' ' + addressCart.district + ' ' + addressCart.province + ' ' + addressCart.zip_code
      this.userData = this.cartData.user_data_cart
      this.user_name_en = this.userData.first_name_en + ' ' + this.userData.last_name_en
      this.user_name_en = this.userData.first_name_th + ' ' + this.userData.last_name_th
    }
  },
  computed: {
    orderType () {
      return localStorage.getItem('typeOrder')
    }
  }
}
</script>

<style scoped>
.header_2 {
  font-family: "Sarabun" !important;
  font-size: 10px !important;
}
.header_a {
  font-family: "Sarabun" !important;
  font-weight: bold;
  font-size: 12px !important;
}
</style>
