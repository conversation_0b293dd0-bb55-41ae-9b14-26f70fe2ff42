import AxiosShop from './axios_dashboard_sale_order_api'

const ModuleDashBoardSaleOrder = {
  state: {
    stateSaleorderRevenueGraph: [],
    stateSaleorderOrderListAndTopProduct: [],
    stateSaleorderTopSale: [],
    stateSaleorderTopCustomer: [],
    stateSaleorderTopCustomerGroup: [],
    stateSaleorderSaleCustomerGroup: [],
    stateSaleorderSaleorderExport: []
  },
  mutations: {
    mutationsSaleorderRevenueGraph (state, data) {
      state.stateSaleorderRevenueGraph = data
    },
    mutationsSaleorderOrderListAndTopProduct (state, data) {
      state.stateSaleorderOrderListAndTopProduct = data
    },
    mutationsSaleorderTopSale (state, data) {
      state.stateSaleorderTopSale = data
    },
    mutationsSaleorderTopCustomer (state, data) {
      state.stateSaleorderTopCustomer = data
    },
    mutationsSaleorderTopCustomerGroup (state, data) {
      state.stateSaleorderTopCustomerGroup = data
    },
    mutationsSaleorderSaleCustomerGroup (state, data) {
      state.stateSaleorderSaleCustomerGroup = data
    },
    mutationsSaleorderExport (state, data) {
      state.stateSSaleorderExport = data
    }
  },
  actions: {
    async actionSaleorderRevenueGraph (context, access) {
      const responseData = await AxiosShop.SaleorderRevenueGraph(access)
      await context.commit('mutationsSaleorderRevenueGraph', responseData)
    },
    async actionSaleorderOrderListAndTopProduct (context, access) {
      const responseData = await AxiosShop.SaleorderOrderListAndTopProduct(access)
      await context.commit('mutationsSaleorderOrderListAndTopProduct', responseData)
    },
    async actionSaleorderTopSale (context, access) {
      const responseData = await AxiosShop.SaleorderTopSale(access)
      await context.commit('mutationsSaleorderTopSale', responseData)
    },
    async actionSaleorderTopCustomer (context, access) {
      const responseData = await AxiosShop.SaleorderTopCustomer(access)
      await context.commit('mutationsSaleorderTopCustomer', responseData)
    },
    async actionSaleorderTopCustomerGroup (context, access) {
      const responseData = await AxiosShop.SaleorderTopCustomerGroup(access)
      await context.commit('mutationsSaleorderTopCustomerGroup', responseData)
    },
    async actionSaleorderSaleCustomerGroup (context, access) {
      const responseData = await AxiosShop.SaleorderSaleCustomerGroup(access)
      await context.commit('mutationsSaleorderSaleCustomerGroup', responseData)
    },
    async actionSaleorderExport (context, access) {
      const responseData = await AxiosShop.SaleorderExport(access)
      await context.commit('mutationsSaleorderExport', responseData)
    }
  }
}
export default ModuleDashBoardSaleOrder
