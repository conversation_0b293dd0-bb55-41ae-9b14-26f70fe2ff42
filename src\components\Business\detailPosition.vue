<template>
    <v-container :class="MobileSize ? 'mt-3' : ''">
      <v-row dense justify="center">
        <v-col cols="12" class="px-0 py-0">
          <v-card width="100%" height="100%" elevation="0">
            <v-row dense justify="center">
              <v-col cols="12" md="12" sm="12" xs="12">
                <v-card-title class="pb-0" :style="IpadSize ? 'font-weight: bold; font-size:20px; line-height: 32px;' : 'font-weight: bold; font-size:24px; line-height: 32px;'" v-if="!MobileSize">
                  <v-icon color="#27AB9C" size="30" @click="backtoPage">mdi-chevron-left</v-icon>จัดการสิทธิ์การใช้งานตำแหน่ง
                  <span style="color: #27AB9C; margin-left: 5px;">{{ positionName }}</span>
                  <v-chip v-if="!IpadSize" :color="dataInTable.active_status === 'active' ? '#f0f9ee' : 'rgb(247, 217, 217)'" class="ml-3">
                    <span v-if="dataInTable.active_status === 'active'" style="color: #27AB9C;">ใช้งาน</span>
                    <span v-if="dataInTable.active_status === 'inactive'" style="color: red;">ยกเลิก</span>
                  </v-chip>
                </v-card-title>
                <v-card-title class="px-0" style="font-weight: bold; font-size: 18px;" v-if="MobileSize">
                  <v-icon color="#27AB9C" class="mr-2" @click="backtoPageMobile">mdi-chevron-left</v-icon> จัดการสิทธิ์การใช้งานตำแหน่ง
                  <span style="color: #27AB9C; margin-left: 5px;">{{ positionName }}</span>
                </v-card-title>
              </v-col>
            </v-row>
            <v-card-text>
              <v-row dense no-gutters class="mb-2">
                <v-col cols="12" class="py-0 mb-0">
                  <a-tabs @change="selectTab">
                    <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                    <a-tab-pane :key="0"><span slot="tab">บริษัท <a-tag color="#27AB9C" style="border-radius: 8px;">{{ companyData.length }}</a-tag></span></a-tab-pane>
                    <a-tab-pane :key="1"><span slot="tab">ร้านค้า <a-tag color="#1AB759" style="border-radius: 8px;">{{ shopData.length }}</a-tag></span></a-tab-pane>
                  </a-tabs>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" md="6" sm="8" xs="12" class="mb-0 mt-0 py-0">
                  <v-text-field
                  v-model="search"
                  dense
                  outlined
                  placeholder="ค้นหาจากร้านค้าหรือบริษัท"
                  >
                    <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
                  </v-text-field>
                </v-col>
                <!-- <v-col v-if="MobileSize" cols="12" class="pt-0">
                  <v-btn v-if="dataInTable.active_status === 'active'" @click="updateStatusRole('inactive', positionId)" :block="MobileSize" color="red" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-close-circle</v-icon>ยกเลิกการใช้งาน</v-btn>
                  <v-btn v-if="dataInTable.active_status === 'inactive'" @click="updateStatusRole('active', positionId)" :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-check-circle</v-icon>เปิดใช้งาน</v-btn>
                </v-col> -->
                <v-col cols="12" md="6" sm="4" xs="12" align="end" class="py-0">
                  <v-btn v-if="managePosition === 'yes'" @click="showDialogRole()" :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-square-edit-outline</v-icon>แก้ไขข้อมูล</v-btn>
                </v-col>
                <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'pl-4 pt-4 pb-0' : 'pl-4 pt-0'" v-if="disableTable === true">
                  <span v-if="tab === 0" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" >รายการสิทธิ์การใช้งานของบริษัททั้งหมด {{ totalCount }} รายการ</span>
                  <span v-else-if="tab === 1" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" >รายการสิทธิ์การใช้งานของร้านค้าทั้งหมด {{ totalCount }} รายการ</span>
                </v-col>
                <v-col cols="12" md="12" sm="12" xs="12" v-if="disableTable === true">
                  <v-card outlined class="mb-4">
                    <v-data-table @pagination="countRequest" :search="search" :headers="tab === 0 ? headersCompany : headersShop" :items="tab === 0 ? companyData : shopData">
                      <template v-slot:[`item.path_logo`]="{item}">
                        <v-col>
                          <v-avatar v-if="item.path_logo === null || item.path_logo === ''" rounded size="44" color="#FFF">
                            <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                          </v-avatar>
                          <v-avatar v-else rounded size="44" color="#FFF">
                            <v-img contain :src="item.path_logo"></v-img>
                          </v-avatar>
                        </v-col>
                      </template>
                      <template v-slot:[`item.img_path`]="{item}">
                        <v-col>
                          <v-avatar v-if="item.img_path === null || item.img_path === ''" rounded size="44" color="#FFF">
                            <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                          </v-avatar>
                          <v-avatar v-else rounded size="44" color="#FFF">
                            <v-img contain :src="item.img_path"></v-img>
                          </v-avatar>
                        </v-col>
                      </template>
                      <template v-slot:[`item.access_company_member`]="{item}">
                        <v-row justify="center">
                          <div>
                            <!-- <v-checkbox :ripple="false" v-if="item.access_company_member === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.access_company_member === '1'" hide-details readonly v-model="item.access_company_member"></v-checkbox> -->
                            <v-img v-if="item.access_company_member === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.access_company_member" v-else-if="item.access_company_member === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                          </div>
                        </v-row>
                      </template>
                      <template v-slot:[`item.access_company_order`]="{item}">
                        <v-row justify="center">
                          <div>
                            <!-- <v-checkbox :ripple="false" v-if="item.access_company_order === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.access_company_order === '1'" hide-details readonly v-model="item.access_company_order"></v-checkbox> -->
                            <v-img v-if="item.access_company_order === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.access_company_order" v-else-if="item.access_company_order === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                          </div>
                        </v-row>
                      </template>
                      <template v-slot:[`item.assign_company_permission`]="{item}">
                        <v-row justify="center">
                          <div>
                            <!-- <v-checkbox :ripple="false" v-if="item.assign_company_permission === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.assign_company_permission === '1'" hide-details readonly v-model="item.assign_company_permission"></v-checkbox> -->
                            <v-img v-if="item.assign_company_permission === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.assign_company_permission" v-else-if="item.assign_company_permission === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                          </div>
                        </v-row>
                      </template>
                      <!-- shop -->
                      <template v-slot:[`item.access_shop_product`]="{item}">
                        <v-row justify="center">
                          <div>
                            <v-img v-if="item.access_shop_product === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.access_shop_product" v-else-if="item.access_shop_product === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                            <!-- <v-checkbox :ripple="false" v-if="item.access_shop_product === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.access_shop_product === '1'" hide-details readonly v-model="item.access_shop_product"></v-checkbox> -->
                          </div>
                        </v-row>
                      </template>
                      <template v-slot:[`item.access_shop_order`]="{item}">
                        <v-row justify="center">
                          <div>
                            <v-img v-if="item.access_shop_order === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.access_shop_order" v-else-if="item.access_shop_order === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                            <!-- <v-checkbox :ripple="false" v-if="item.access_shop_order === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.access_shop_order === '1'" hide-details readonly v-model="item.access_shop_order"></v-checkbox> -->
                          </div>
                        </v-row>
                      </template>
                      <template v-slot:[`item.access_shop_user`]="{item}">
                        <v-row justify="center">
                          <div>
                            <v-img v-if="item.access_shop_user === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.access_shop_user" v-else-if="item.access_shop_user === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                            <!-- <v-checkbox :ripple="false" v-if="item.access_shop_product === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.access_shop_product === '1'" hide-details readonly v-model="item.access_shop_user"></v-checkbox> -->
                          </div>
                        </v-row>
                      </template>
                      <template v-slot:[`item.assign_shop_permission`]="{item}">
                        <v-row justify="center">
                          <div>
                            <v-img v-if="item.assign_shop_permission === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
                            <v-img v-model="item.assign_shop_permission" v-else-if="item.assign_shop_permission === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
                            <!-- <v-checkbox :ripple="false" v-if="item.assign_shop_permission === '0'" hide-details readonly></v-checkbox>
                            <v-checkbox :ripple="false" v-if="item.assign_shop_permission === '1'" hide-details readonly v-model="item.assign_shop_permission"></v-checkbox> -->
                          </div>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-card>
                </v-col>
              </v-row>
              <v-row justify="center" align-content="center" v-if="disableTable === false">
              <v-col cols="12" md="12" align="center">
                <div class="my-5">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px"
                    max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="tab === 0">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการสิทธิ์การใช้งานของบริษัท</span><br />
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="tab === 1">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการสิทธิ์การใช้งานของร้านค้า</span><br />
                </h2>
              </v-col>
            </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <!-- edit role -->
      <v-dialog v-model="showDialogAddRole" :style="MobileSize ? 'z-index: 16000004' : ''" width="850px" scrollable content-class="elevation-0" persistent>
        <v-card v-if="showDialogAddRole" width="100%" height="100%" class="rounded-xl" :style="MobileSize ? '' : 'overflow-y: hidden'">
          <v-toolbar dark dense elevation="0" color="#27AB9C">
            <v-row>
              <v-col class="d-flex justify-space-around" v-if="!MobileSize">
                <v-toolbar-title><span style="color: #FFF;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
              </v-col>
              <v-col class="d-flex justify-space-around" v-else>
                <v-toolbar-title><span style="color: #FFF; font-size: 16px;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="closeDialogRole()" icon><v-icon color="#FFF">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-form :lazy-validation="lazy">
            <v-container grid-list-xs>
              <v-card-text>
                <v-row class="pl-9" v-if="!MobileSize">
                  <v-col cols="12" md="2" sm="2">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="80"
                      max-width="180"
                      src="@/assets/Businessman.png"
                      contain
                    ></v-img>
                  </v-col>
                  <v-col cols="12" md="9" sm="8" class="pt-2" style="font-size: 18px;">
                    <!-- <span>กำหนดตำแหน่งและสิทธิ์การใช้งาน</span> -->
                    <v-col cols="12" md="12" sm="5" xs="12" class="pt-0">
                      <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12" class="pt-0">
                      <v-text-field v-if="newPositionName === 'เจ้าของนิติบุคคล'" readonly :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                      <v-text-field v-else :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                    </v-col>
                  </v-col>
                </v-row>
                <v-divider v-if="!MobileSize" class="mt-3 mb-6"></v-divider>
              <v-row class="pl-9" v-if="!MobileSize">
                <v-col cols="6" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">การจัดการสิทธิ์และตำแหน่ง</span>
                  <v-switch
                    v-if="newPositionName === 'เจ้าของนิติบุคคล'"
                    v-model="switch1"
                    inset
                    class="mt-0 pl-7"
                    color="success"
                    hide-details
                    disabled
                  ></v-switch>
                  <v-switch
                    v-else
                    v-model="switch1"
                    inset
                    class="mt-0 pl-7"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch1 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
                <v-col cols="6" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">สิทธิ์ในการสร้างร้านค้า</span>
                  <v-switch
                    v-if="newPositionName === 'เจ้าของนิติบุคคล'"
                    v-model="switch2"
                    inset
                    class="mt-0 pl-7"
                    color="success"
                    hide-details
                    disabled
                  ></v-switch>
                  <v-switch
                    v-else
                    v-model="switch2"
                    inset
                    class="mt-0 pl-7"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch2 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
              </v-row>
              <v-divider v-if="!MobileSize" class=" mb-3"></v-divider>
                <v-row v-else>
                  <v-col cols="3" md="2">
                    <v-img
                      lazy-src="@/assets/Businessman.png"
                      max-height="100"
                      max-width="200"
                      src="@/assets/Businessman.png"
                    ></v-img>
                  </v-col>
                  <v-col cols="9" md="10" class="pt-0" style="font-size: 14px; font-weight: bold">
                    <v-col cols="12" md="12" sm="2" xs="12" class="pt-0">
                      <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12" class="pt-0">
                      <v-text-field v-if="newPositionName === 'เจ้าของนิติบุคคล'" readonly :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                      <v-text-field v-else :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                    </v-col>
                  </v-col>
                </v-row>
                <!-- <v-row v-if="!MobileSize" class="mt-2 px-9">
                  <v-col cols="12" md="12" sm="2" xs="12" class="pt-2">
                    <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                  </v-col>
                </v-row>
                <v-row dense no-gutters :class="MobileSize ? 'pt-3' : 'px-9 pb-3'">
                  <v-col cols="12" md="12" sm="12" xs="12">
                    <v-text-field :label="MobileSize ? 'ชื่อตำแหน่ง' : ''" v-model="positionName" placeholder="ระบุชื่อตำแหน่ง" outlined dense hide-details></v-text-field>
                  </v-col>
                </v-row> -->
                <v-divider v-if="MobileSize" class="mt-3 mb-6"></v-divider>
              <v-row v-if="MobileSize">
                <v-col cols="12" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">การจัดการสิทธิ์และตำแหน่ง</span>
                  <v-switch
                    v-if="newPositionName === 'เจ้าของนิติบุคคล'"
                    v-model="switch1"
                    inset
                    class="mt-0 pl-4"
                    color="success"
                    hide-details
                    disabled
                  ></v-switch>
                  <v-switch
                    v-else
                    v-model="switch1"
                    inset
                    class="mt-0 pl-4"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch1 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
                <v-col cols="12" class="pt-0 pb-6" style="display: flex; align-items: center;">
                  <span style="font-weight: bold; font-size: 15px">สิทธิ์ในการสร้างร้านค้า</span>
                  <v-switch
                    v-if="newPositionName === 'เจ้าของนิติบุคคล'"
                    v-model="switch2"
                    inset
                    class="mt-0 pl-10"
                    color="success"
                    hide-details
                    disabled
                  ></v-switch>
                  <v-switch
                    v-else
                    v-model="switch2"
                    inset
                    class="mt-0 pl-10"
                    color="success"
                    hide-details
                  ></v-switch>
                  <span style="font-size: 15px">สถานะ : {{ switch2 ? 'เปิด' : 'ปิด' }}</span>
                </v-col>
              </v-row>
              <v-divider v-if="MobileSize" class="mb-8"></v-divider>
                <v-card v-if="MobileSize" class="mt-6 elevation-0" style="height: 46px; width: 100%; background-color: #F3F5F7; border-radius: 12px;" >
                  <v-row justify="center">
                    <v-col cols="4" class="pr-0 pt-1">
                      <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onSelected(1)">
                        <v-row class="d-flex d-inline-block justify-center align-center">
                          <span style="font-size: 16px; color: #27AB9C;">บริษัท</span>
                        </v-row>
                      </v-card>
                    </v-col>
                    <v-col cols="4" class="pl-0 pr-0 pt-1">
                      <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onSelected(2)">
                        <v-row class="d-flex d-inline-block justify-center align-center">
                          <span style="font-size: 16px; color: #27AB9C;">ร้านค้า</span>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card>
                <v-row justify="center" class="mt-0">
                  <v-col v-if="MobileSize && selectType === 1" cols="12">
                    <v-card outlined class="pt-2" style="border-color: #27AB9C; border-width: 3px; overflow-y: auto;" height="270">
                      <v-row style="display: flex; flex-direction: column" no-gutters justify="center">
                        <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center pa-0">
                          <span style="font-weight: bold; font-size: 16px;">บริษัท</span>
                        </v-col>
                        <v-row justify="center" class="mt-2">
                          <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                            <v-select
                              v-model="selectedCompany"
                              item-text="name_th"
                              item-value="company_id"
                              :items = "listSelectedCompany"
                              chips
                              placeholder="เลือกบริษัท"
                              multiple
                              solo
                              dense
                              scrollable
                              hide-details
                              :menu-props="{ maxHeight: '230px'}"
                              :item-disabled="isItemDisabledCompnay"
                            >
                            <template v-slot:selection="{ item, index }">
                              <v-chip v-if="index === 0">
                                <span style="font-size: 12px">{{ item.name_th }}</span>
                              </v-chip>
                              <span
                                v-if="index === 1"
                                class="grey--text text-caption"
                                style="font-size: 12px"
                              >
                                (+{{ selectedCompany.length - 1 }} อื่นๆ)
                              </span>
                            </template>
                          </v-select>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-3" v-if="selectedCompany.length !== 0" justify="center">
                          <v-col cols="12" v-for="(item, index) in testArray" :key="index" class="d-flex justify-center">
                            <div v-for="(item2, index) in listSelectedCompany" :key="index">
                            <v-card v-if="item2.company_id === item.company_id" class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="275px" >
                              <div>
                                <v-chip class="mt-3 ml-2" :ripple="false">
                                  <!-- <span style="font-size: 14px">
                                    {{ item2.name_th }}
                                  </span> -->
                                  <v-tooltip bottom>
                                    <template v-slot:activator="{ on, attrs }">
                                      <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                                        {{ item2.name_th|truncate(24, '...') }}
                                      </div>
                                    </template>
                                    <span>{{ item2.name_th }}</span>
                                  </v-tooltip>
                                </v-chip>
                              </div>
                              <v-row dense class="pl-3 pt-3">
                                <v-col :cols="MobileSize ? '9' : '9'" md="9">
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    ตรวจสอบบุคคลากรบริษัท
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    ตรวจสอบรายการสั่งซื้อของบริษัท
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    แก้ไขสิทธิ์ภายในบริษัท
                                  </v-list-item-content>
                                </v-col>
                                <v-spacer></v-spacer>
                                <v-col cols="2" class="justify-content-between" v-if="newPositionName === 'เจ้าของนิติบุคคล'">
                                  <v-checkbox disabled :ripple="false" v-model="item.access_company_member" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled :ripple="false" v-model="item.access_company_order" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled :ripple="false" v-model="item.assign_company_permission" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                                <v-col cols="2" class="justify-content-between" v-if="newPositionName !== 'เจ้าของนิติบุคคล'">
                                  <v-checkbox :ripple="false" v-model="item.access_company_member" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox :ripple="false" v-model="item.access_company_order" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox :ripple="false" v-model="item.assign_company_permission" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-card>
                          </div>
                          </v-col>
                        </v-row>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col v-if="!MobileSize" cols="6">
                    <v-card outlined class="pt-2 scroll-card" style="border-color: #27AB9C; border-width: 3px;" height="290">
                      <v-row style="display: flex; flex-direction: column" no-gutters justify="center" >
                        <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center pa-0">
                          <span style="font-weight: bold; font-size: 16px;">บริษัท</span>
                        </v-col>
                        <v-row justify="center" class="mt-2">
                          <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                            <v-select
                              v-model="selectedCompany"
                              item-text="name_th"
                              item-value="company_id"
                              :items = "listSelectedCompany"
                              chips
                              placeholder="เลือกบริษัท"
                              multiple
                              solo
                              dense
                              scrollable
                              hide-details
                              :menu-props="{ maxHeight: '230px'}"
                              :item-disabled="isItemDisabledCompnay"
                            >
                            <template v-slot:selection="{ item, index }">
                              <v-chip v-if="index === 0">
                                <span>{{ item.name_th }}</span>
                              </v-chip>
                              <span
                                v-if="index === 1"
                                class="grey--text text-caption"
                              >
                                (+{{ selectedCompany.length - 1 }} อื่นๆ)
                              </span>
                            </template>
                          </v-select>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-3" v-if="selectedCompany.length !== 0" justify="center">
                          <v-col cols="12" v-for="(item, index) in testArray" :key="index" class="d-flex justify-center">
                            <div v-for="(item2, index) in listSelectedCompany" :key="index">
                            <v-card v-if="item2.company_id === item.company_id" class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="330px">
                              <div>
                                <v-chip class="mt-3 ml-2">
                                  <span>
                                    {{ item2.name_th }}
                                  </span>
                                </v-chip>
                              </div>
                              <v-row dense class="pl-3 pt-3 pb-2">
                                <v-col cols="9" md="9">
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    ตรวจสอบบุคคลากรบริษัท
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    ตรวจสอบรายการสั่งซื้อของบริษัท
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    แก้ไขสิทธิ์ภายในบริษัท
                                  </v-list-item-content>
                                </v-col>
                                <v-spacer></v-spacer>
                                <v-col cols="2" v-if="newPositionName === 'เจ้าของนิติบุคคล'">
                                  <v-checkbox disabled v-model="item.access_company_member" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled v-model="item.access_company_order" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled v-model="item.assign_company_permission" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                                <v-col cols="2" v-if="newPositionName !== 'เจ้าของนิติบุคคล'">
                                  <v-checkbox v-model="item.access_company_member" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox v-model="item.access_company_order" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox v-model="item.assign_company_permission" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-card>
                          </div>
                          </v-col>
                        </v-row>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col v-if="MobileSize && selectType === 2" cols="12">
                    <v-card outlined class="pt-2" style="border-color: #27AB9C; border-width: 3px; overflow-y: auto;" height="270">
                      <v-row style="display: flex; flex-direction: column" no-gutters justify="center">
                        <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center">
                          <span style="font-weight: bold; font-size: 16px;">ร้านค้า</span>
                        </v-col>
                        <v-row justify="center" class="mt-2">
                          <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                            <v-select
                              v-model="selectedShop"
                              item-text="name_th"
                              item-value="shop_id"
                              :items = "listSelectedShop"
                              chips
                              placeholder="เลือกร้านค้า"
                              multiple
                              solo
                              dense
                              hide-details
                              :menu-props="{ maxHeight: '230px' }"
                              :item-disabled="isItemDisabled"
                            >
                            <template v-slot:selection="{ item, index }">
                              <v-chip v-if="index === 0">
                                <span>{{ item.name_th }}</span>
                              </v-chip>
                              <span
                                v-if="index === 1"
                                class="grey--text text-caption"
                              >
                                (+{{ selectedShop.length - 1 }} อื่นๆ)
                              </span>
                            </template>
                          </v-select>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-3" v-if="selectedShop.length !== 0" justify="center">
                          <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in testArrayShop" :key="index" class="d-flex justify-center">
                            <div v-for="(item2, index) in listSelectedShop" :key="index">
                            <v-card v-if="item2.shop_id === item.seller_shop_id" class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="275px">
                              <div>
                                <v-chip class="mt-3 ml-2" :ripple="false">
                                  <!-- <span>
                                    {{ item2.name_th }}
                                  </span> -->
                                  <v-tooltip bottom>
                                    <template v-slot:activator="{ on, attrs }">
                                      <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                                        {{ item2.name_th|truncate(24, '...') }}
                                      </div>
                                    </template>
                                    <span>{{ item2.name_th }}</span>
                                  </v-tooltip>
                                </v-chip>
                              </div>
                              <v-row dense class="pl-3 pt-3">
                                <v-col cols="10">
                                  <!-- <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    ตรวจสอบรายการสินค้าของร้านค้า
                                  </v-list-item-content> -->
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    ตรวจสอบสมาชิกภายในร้านค้า
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    ตรวจสอบรายการสั่งซื้อของร้านค้า
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 20px; color: #333333; padding-top: 0px">
                                    แก้ไขสิทธิ์ภายในร้านค้า
                                  </v-list-item-content>
                                </v-col>
                                <v-spacer></v-spacer>
                                <v-col cols="2" v-if="newPositionName === 'เจ้าของนิติบุคคล'">
                                  <!-- <v-checkbox :ripple="false" v-model="item.access_shop_product" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox> -->
                                  <v-checkbox disabled :ripple="false" v-model="item.access_shop_user" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled :ripple="false" v-model="item.access_shop_order" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled :ripple="false" v-model="item.assign_shop_permission" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                                <v-col cols="2" v-if="newPositionName !== 'เจ้าของนิติบุคคล'">
                                  <!-- <v-checkbox :ripple="false" v-model="item.access_shop_product" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox> -->
                                  <v-checkbox :ripple="false" v-model="item.access_shop_user" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox :ripple="false" v-model="item.access_shop_order" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox :ripple="false" v-model="item.assign_shop_permission" style="margin-Top: 5px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-card>
                          </div>
                          </v-col>
                        </v-row>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col v-if="!MobileSize" cols="6">
                    <v-card outlined class="pt-2 scroll-card" style="border-color: #27AB9C; border-width: 3px;" height="290">
                      <v-row style="display: flex; flex-direction: column" no-gutters justify="center">
                        <v-col cols="12" md="12" sm="12" xs="12" class="d-flex justify-center">
                          <span style="font-weight: bold; font-size: 16px;">ร้านค้า</span>
                        </v-col>
                        <v-row justify="center" class="mt-2">
                          <v-col cols="11" md="11" sm="11" xs="11" class="d-flex justify-center">
                            <v-select
                              v-model="selectedShop"
                              item-text="name_th"
                              item-value="shop_id"
                              :items = "listSelectedShop"
                              chips
                              placeholder="เลือกร้านค้า"
                              multiple
                              solo
                              dense
                              hide-details
                              :menu-props="{ maxHeight: '230px' }"
                              :item-disabled="isItemDisabled"
                            >
                            <template v-slot:selection="{ item, index }">
                              <v-chip v-if="index === 0">
                                <span>{{ item.name_th }}</span>
                              </v-chip>
                              <span
                                v-if="index === 1"
                                class="grey--text text-caption"
                              >
                                (+{{ selectedShop.length - 1 }} อื่นๆ)
                              </span>
                            </template>
                          </v-select>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-3" v-if="selectedShop.length !== 0" justify="center">
                          <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in testArrayShop" :key="index" class="d-flex justify-center">
                            <div v-for="(item2, index) in listSelectedShop" :key="index">
                            <v-card v-if="item2.shop_id === item.seller_shop_id" class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;" width="330px">
                              <div>
                                <v-chip class="mt-3 ml-2">
                                  <span>
                                    {{ item2.name_th }}
                                  </span>
                                </v-chip>
                              </div>
                              <!-- <div v-if="!listSelectedShop.some(shop => shop.shop_id === item.seller_shop_id)">
                                <v-chip class="mt-3 ml-2" v-if="shopData.find(shop => shop.shop_id === item.seller_shop_id)">
                                  <span>
                                    {{ shopData.find(shop => shop.shop_id === item.seller_shop_id).shop_name }}
                                  </span>
                                </v-chip>
                              </div> -->
                              <v-row dense class="pl-3 pt-3 pb-2" >
                                <v-col cols="9" md="9" sm="9" xs="9">
                                  <!-- <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    ตรวจสอบรายการสินค้าของร้านค้า
                                  </v-list-item-content> -->
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    ตรวจสอบสมาชิกภายในร้านค้า
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    ตรวจสอบรายการสั่งซื้อของร้านค้า
                                  </v-list-item-content>
                                  <v-list-item-content
                                    style="font-weight: 400; font-size: 14px; line-height: 1px; color: #333333;">
                                    แก้ไขสิทธิ์ภายในร้านค้า
                                  </v-list-item-content>
                                </v-col>
                                <v-col cols="3" md="3" sm="3" xs="3" class="pl-10" v-if="newPositionName === 'เจ้าของนิติบุคคล'">
                                  <!-- <v-checkbox v-model="item.access_shop_product" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox> -->
                                  <v-checkbox disabled v-model="item.access_shop_user" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled v-model="item.access_shop_order" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox disabled v-model="item.assign_shop_permission" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                                <v-col cols="3" md="3" sm="3" xs="3" class="pl-10" v-if="newPositionName !== 'เจ้าของนิติบุคคล'">
                                  <!-- <v-checkbox v-model="item.access_shop_product" style="margin-Top: -7px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox> -->
                                  <v-checkbox v-model="item.access_shop_user" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox v-model="item.access_shop_order" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                  <v-checkbox v-model="item.assign_shop_permission" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-card>
                          </div>
                          </v-col>
                        </v-row>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row no-gutters class="mt-4" justify="end">
                  <v-btn v-if="dataInTable.active_status === 'active' && newPositionName !== 'เจ้าของนิติบุคคล'" class="mr-5" @click="updateStatusRole('inactive', positionId)" color="red" style="font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-close-circle</v-icon>ยกเลิกการใช้งาน</v-btn>
                  <v-btn v-if="dataInTable.active_status === 'inactive'" class="mr-5" @click="updateStatusRole('active', positionId)" color="#27AB9C" style="font-size: 16px; line-height: 30px;" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-check-circle</v-icon>เปิดใช้งาน</v-btn>
                  <v-btn v-if="newPositionName !== 'เจ้าของนิติบุคคล'" @click="submitEditRole()" color="#27AB9C" style="font-size: 16px" class="white--text">บันทึก</v-btn>
                </v-row>
              </v-card-text>
            </v-container>
          </v-form>
        </v-card>
      </v-dialog>
    </v-container>
  </template>
<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      amoutselectedShop: [],
      amoutselectedCompany: [],
      managePosition: '',
      switch1: false,
      switch2: false,
      totalCount: 0,
      selectType: 1,
      testArray: [],
      testArrayShop: [],
      disableTable: false,
      companyData: [],
      shopData: [],
      dataInTable: [],
      positionId: null,
      positionName: '',
      newPositionName: '',
      newPositionName2: '',
      taxId: '',
      roleId: null,
      status: '',
      checkList: [],
      search: '',
      listPosition: [],
      listPositionActive: [],
      listPositionInActive: [],
      listCompany: [],
      listSelectedCompany: [],
      selectedCompany: [],
      itemsCompany: [],
      listShop: [],
      listSelectedShop: [],
      selectedShop: [],
      accessCompanyMember: 0,
      accessCompanyOrder: 0,
      assignCompanyPermission: 0,
      accessShopProduct: 0,
      accessShopOrder: 0,
      accessShopUser: 0,
      assignShopPermission: 0,
      lazy: false,
      showDialogAddRole: false,
      showDetailCompany: false,
      showDetailShop: false,
      tab: 0,
      headersCompany: [
        { text: 'โลโก้', value: 'img_path', sortable: false, width: '50', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'บริษัท', value: 'name_th', sortable: false, width: '150', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบบุคคลากรบริษัท', value: 'access_company_member', filterable: false, sortable: false, align: 'center', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบรายการสั่งซื้อของบริษัท', value: 'access_company_order', filterable: false, width: '150', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'แก้ไขสิทธิ์ภายในบริษัท', value: 'assign_company_permission', filterable: false, width: '150', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersShop: [
        { text: 'โลโก้', value: 'path_logo', sortable: false, width: '30', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ร้านค้า', value: 'shop_name', sortable: false, width: '130', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ตรวจสอบรายการสินค้าของร้านค้า', value: 'access_shop_product', filterable: false, sortable: false, align: 'center', width: '160', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบสมาชิกภายในร้านค้า', value: 'access_shop_user', filterable: false, width: '170', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบรายการสั่งซื้อของร้านค้า', value: 'access_shop_order', filterable: false, width: '170', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'แก้ไขสิทธิ์ภายในร้านค้า', value: 'assign_shop_permission', filterable: false, width: '150', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ]
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  methods: {
    isItemDisabled (item) {
      return this.amoutselectedShop.includes(item.shop_id)
    },
    isItemDisabledCompnay (item) {
      return this.amoutselectedCompany.includes(item.company_id)
    },
    countRequest (pagination) {
      this.totalCount = pagination.itemsLength
    },
    onSelected (val) {
      this.selectType = val
    },
    selectTab (val) {
      this.search = ''
      this.tab = val
      if (this.tab === 0 && this.companyData.length !== 0) {
        this.disableTable = true
      } else if (this.tab === 1 && this.shopData.length !== 0) {
        this.disableTable = true
      } else {
        this.disableTable = false
      }
    },
    showDialogRole () {
      this.showDialogAddRole = true
      window.scrollTo(0, 0)
    },
    closeDialogRole () {
      if (this.dataInTable.manage_position === '1') {
        this.switch1 = true
      }
      if (this.dataInTable.manage_position === '0') {
        this.switch1 = false
      }
      if (this.dataInTable.manage_business === '1') {
        this.switch2 = true
      }
      if (this.dataInTable.manage_business === '0') {
        this.switch2 = false
      }
      if (this.newPositionName2 !== '') {
        this.positionName = this.newPositionName2
      } else {
        this.positionName = this.$route.query.position
      }
      this.selectedShop = []
      this.selectedCompany = []
      for (let i = 0; i < this.companyData.length; i++) {
        this.selectedCompany[i] = this.companyData[i].company_id
      }
      for (let i = 0; i < this.shopData.length; i++) {
        this.selectedShop[i] = this.shopData[i].seller_shop_id
      }
      this.testArray = this.companyData.map(company => ({
        company_id: company.company_id,
        access_company_member: company.access_company_member === '1',
        access_company_order: company.access_company_order === '1',
        assign_company_permission: company.assign_company_permission === '1'
      }))
      this.testArrayShop = this.shopData.map(shop => ({
        seller_shop_id: shop.seller_shop_id,
        access_shop_product: shop.access_shop_product === '1',
        access_shop_order: shop.access_shop_order === '1',
        access_shop_user: shop.access_shop_user === '1',
        assign_shop_permission: shop.assign_shop_permission === '1'
      }))
      this.showDialogAddRole = false
    },
    async listPositionTable (id) {
      this.$store.commit('openLoader')
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListPositions
      this.managePosition = response.data.manage_position
      if (response.code === 200) {
        this.listPosition = response.data.list_positions
        this.dataInTable = this.listPosition.find(position => position.id === this.positionId)
        if (this.dataInTable.manage_position === '1') {
          this.switch1 = true
        }
        if (this.dataInTable.manage_position === '0') {
          this.switch1 = false
        }
        if (this.dataInTable.manage_business === '1') {
          this.switch2 = true
        }
        if (this.dataInTable.manage_business === '0') {
          this.switch2 = false
        }
        // console.log(this.switch1)
        // console.log(this.switch2)
        this.companyData = this.dataInTable.Company
        this.shopData = this.dataInTable.Shop
        // console.log(this.companyData, 'company data')
        // this.selectedCompany = this.companyData.company_id
        for (let i = 0; i < this.shopData.length; i++) {
          this.selectedShop[i] = this.shopData[i].seller_shop_id
          this.amoutselectedShop[i] = this.shopData[i].seller_shop_id
        }
        for (let i = 0; i < this.companyData.length; i++) {
          this.selectedCompany[i] = this.companyData[i].company_id
          this.amoutselectedCompany[i] = this.companyData[i].company_id
        }
        this.testArray = this.companyData.map(company => ({
          company_id: company.company_id,
          access_company_member: company.access_company_member === '1',
          access_company_order: company.access_company_order === '1',
          assign_company_permission: company.assign_company_permission === '1'
        }))
        this.testArrayShop = this.shopData.map(shop => ({
          seller_shop_id: shop.seller_shop_id,
          access_shop_product: shop.access_shop_product === '1',
          access_shop_order: shop.access_shop_order === '1',
          access_shop_user: shop.access_shop_user === '1',
          assign_shop_permission: shop.assign_shop_permission === '1'
        }))
        if (this.tab === 0 && this.companyData.length === 0) {
          this.disableTable = false
        } else if (this.tab === 1 && this.shopData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
      this.$store.commit('closeLoader')
    },
    backtoPage () {
      this.$router.push('/managePositionComapny&Bussiness')
    },
    backtoPageMobile () {
      this.$router.push('/managePositionComapny&BussinessMobile')
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          this.getListCompany(this.taxId)
          this.getListShop(this.taxId)
          await this.listPositionTable(this.taxId)
        }
      }
      this.$store.commit('closeLoader')
    },
    async getListCompany (id) {
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListCompanyPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListCompanyPositions
      if (response.code === 200) {
        this.listSelectedCompany = response.data.companies.map(company => ({
          company_id: company.company_id,
          name_th: company.name_th
        }))
      }
    },
    async getListShop (id) {
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListShopPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListShopPositions
      if (response.code === 200) {
        this.listSelectedShop = response.data.shops.map(shop => ({
          shop_id: shop.seller_shop_id,
          name_th: shop.name_th
        }))
      }
    },
    async submitEditRole () {
      // var companyPermission = this.selectedCompany.map(company => ({
      //   company_id: company,
      //   access_company_member: this.accessCompanyMember,
      //   access_company_order: this.accessCompanyOrder,
      //   assign_company_permission: this.assignCompanyPermission
      // }))
      // var shopPermission = this.selectedShop.map(shop => ({
      //   seller_shop_id: shop,
      //   access_shop_product: this.accessShopProduct,
      //   access_shop_order: this.accessShopOrder,
      //   access_shop_user: this.accessShopUser,
      //   assign_shop_permission: this.assignShopPermission
      // }))
      var data = {
        position_id: this.positionId,
        new_position_name: this.positionName === 'เจ้าของนิติบุคคล' && this.newPositionName === 'เจ้าของนิติบุคคล' ? '' : this.positionName,
        manage_position: this.switch1,
        manage_business: this.switch2,
        shop_permission: this.testArrayShop,
        company_permission: this.testArray
      }
      this.showDialogAddRole = false
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsEditRole', data)
      this.$store.commit('closeLoader')
      var response = await this.$store.state.ModuleBusiness.stateEditRole
      if (response.result === 'SUCCESS') {
        this.listPositionTable(this.taxId)
        this.$swal.fire({
          icon: 'success',
          text: 'แก้ไขข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1300
        })
        // window.location.reload()
      } else if (response.result === 'FAILD' || response.result === 'FAILED') {
        this.listPositionTable(this.taxId)
        if (response.message === 'Not found business id.') {
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่่พบ position id',
            showConfirmButton: false,
            timer: 1300
          })
        } else if (response.message === 'You did not enter a position id.') {
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่ได้ใส่ position id',
            showConfirmButton: false,
            timer: 1300
          })
        } else if (response.message === "You can't change this name.") {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สามารถเปลี่ยนชื่อเป็น "เจ้าของนิติบุคคลได้"',
            showConfirmButton: false,
            timer: 1300
          })
        }
      } else if (response.result === 'SERVER ERROR') {
        this.listPositionTable(this.taxId)
        this.$swal.fire({
          icon: 'error',
          text: 'An error has occurred. Please try again in an hour or two',
          showConfirmButton: false,
          timer: 1300
        })
      }
      if (this.positionName !== '' && this.positionName !== 'เจ้าของนิติบุคคล') {
        this.newPositionName2 = this.positionName
      } else {
        if (this.newPositionName2 === '') {
          this.positionName = this.newPositionName
        } else {
          this.positionName = this.newPositionName2
        }
      }
    },
    async updateStatusRole (status, id) {
      if (this.newPositionName2 !== '') {
        this.positionName = this.newPositionName2
      } else {
        this.positionName = this.$route.query.position
      }
      var text = ''
      var warning = ''
      var btnColor = ''
      if (status === 'active') {
        text = 'ยืนยันเปิดการใช้งาน ใช่หรือไม่?'
        warning = '* บุคคลที่อยู่ในตำแหน่งนี้จะกลับเข้ามาในนิติบุคคลนี้ *'
        btnColor = '#1ab759'
      } else {
        text = 'ยืนยันยกเลิกการใช้งาน ใช่หรือไม่?'
        warning = '* บุคคลที่อยู่ในตำแหน่งนี้จะออกจากนิติบุคคลนี้ *'
        btnColor = '#f5222d'
      }
      var datas = await {
        id: id,
        status: status
      }
      await this.$swal.fire({
        title: `${text}`,
        html: `<span style="color: red;">${warning}</span>`,
        // type: 'warning',
        // buttons: {
        //   confirm: 'ยืนยัน',
        //   cancel: 'ย้อนกลับ'
        // }
        showCancelButton: true,
        confirmButtonColor: `${btnColor}`,
        cancelButtonColor: '#f5222d',
        cancelButtonText: 'ย้อนกลับ',
        confirmButtonText: 'ยืนยัน',
        // closeOnConfirm: false,
        // closeOnCancel: false,
        reverseButtons: true
      }).then(async (result) => {
        if (result.isConfirmed) {
          await this.$store.dispatch('actionsRoleUpdate', datas)
          var data = await this.$store.state.ModuleBusiness.stateRoleUpdate
          if (data.result === 'SUCCESS') {
            this.showDialogAddRole = false
            this.listPositionTable(this.taxId)
            if (status === 'active') {
              this.$swal.fire(
                {
                  icon: 'success',
                  text: 'ยืนยันการเปิดใช้งานสำเร็จ',
                  showConfirmButton: false,
                  timer: 1500
                })
            } else {
              this.$swal.fire(
                {
                  icon: 'success',
                  text: 'ยืนยันการยกเลิกการใช้งานสำเร็จ',
                  showConfirmButton: false,
                  timer: 1500
                })
            }
          } else {
            this.listPositionTable(this.taxId)
            this.$swal.fire(
              {
                icon: 'error',
                text: 'ยืนยันการยกเลิกการใช้งานไม่สำเร็จ',
                showConfirmButton: false,
                timer: 2000
              })
          }
        } else {
        }
      })
    }
  },
  watch: {
    selectedCompany () {
      // ตรวจสอบบริษัทที่ถูกเลือกใหม่
      this.selectedCompany.forEach(company => {
        // ค้นหาบริษัทใน testArray ที่มีอยู่แล้ว
        const existingCompany = this.testArray.find(item => item.company_id === company)
        if (!existingCompany) {
          // ถ้าไม่มีอยู่ใน testArray ให้เพิ่มบริษัทใหม่
          const matchingCompany = this.companyData.find(data => data.company_id === company)
          this.testArray.push({
            company_id: company,
            access_company_member: matchingCompany ? matchingCompany.access_company_member === '1' : false,
            access_company_order: matchingCompany ? matchingCompany.access_company_order === '1' : false,
            assign_company_permission: matchingCompany ? matchingCompany.assign_company_permission === '1' : false
          })
        }
      })
      // ลบบริษัทที่ไม่ได้ถูกเลือกออกจาก testArray
      this.testArray = this.testArray.filter(item => this.selectedCompany.includes(item.company_id))
    },
    selectedShop () {
      // ตรวจสอบร้านค้าที่ถูกเลือกใหม่
      this.selectedShop.forEach(shop => {
        // ค้นหาร้านค้าใน testArrayShop ที่มีอยู่แล้ว
        const existingShop = this.testArrayShop.find(item => item.seller_shop_id === shop)
        if (!existingShop) {
          // ถ้าไม่มีอยู่ใน testArrayShop ให้เพิ่มร้านค้าใหม่
          const matchingShop = this.shopData.find(data => data.seller_shop_id === shop)
          this.testArrayShop.push({
            seller_shop_id: shop,
            access_shop_product: matchingShop ? matchingShop.access_shop_product === '1' : false,
            access_shop_order: matchingShop ? matchingShop.access_shop_order === '1' : false,
            access_shop_user: matchingShop ? matchingShop.access_shop_user === '1' : false,
            assign_shop_permission: matchingShop ? matchingShop.assign_shop_permission === '1' : false
          })
        }
      })
      // ลบบริษัทที่ไม่ได้ถูกเลือกออกจาก testArray
      this.testArrayShop = this.testArrayShop.filter(item => this.selectedShop.includes(item.seller_shop_id))
    },
    MobileSize (val) {
      // console.log(this.MobileSize, 'desktop')
      if (val === true) {
        this.$router.push({ path: '/managePositionComapny&BussinessMobile' }).catch(() => {})
      } else {
        localStorage.setItem('pathBusiness', 'managePositionComapny&Bussiness')
        this.$router.push({ path: '/managePositionComapny&Bussiness' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavBusiness')
    this.getTaxId()
    // this.listPositionTable()
    this.positionId = Number(this.$route.query.id)
    this.positionName = this.$route.query.position
    this.newPositionName = this.positionName
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .swal-title {
  margin: 0px;
  font-size: 16px;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.21);
  margin-bottom: 28px;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
}
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.max-v-list-height {
  max-height: 10px;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.scroll-card {
  height: 260px; /* Set a fixed height */
  overflow-y: auto; /* Enable vertical scroll */
  overflow-x: hidden;
}
.scroll-card::-webkit-scrollbar {
  width: 8px;
}
.scroll-card::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}
.scroll-card::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
.scroll-card::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
}
</style>
