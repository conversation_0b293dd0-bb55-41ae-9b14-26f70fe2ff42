<template>
  <v-container>
    <v-row >
      <v-col cols="7" md="6">
       <h1 v-if="!MobileSize"><B>รายละเอียดฝ่ายและแผนก</B></h1>
       <h3 v-if="MobileSize"><B>รายละเอียดฝ่ายและแผนก</B></h3>
      </v-col>
      <v-col cols="5" md="6" align="end">
        <!-- <v-btn   color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="Call_Staff()">
          <v-icon left>mdi-settings-box</v-icon><B>ตั้งค่า</B>
        </v-btn> -->
         <v-menu offset-y>
          <template v-slot:activator="{ on, attrs }">
          <v-btn color="primary" dark  v-bind="attrs" v-on="on" >
            <v-icon left>mdi-wrench</v-icon><B>ตั้งค่า</B>
          </v-btn>
          </template>
          <v-list>
            <v-list-item  v-for="(item, index) in menu"   :key="index" >
              <v-list-item-title @click="GoToCreateAndEdit(item.name)"><v-icon left> {{item.icon}}</v-icon>{{ item.name }}</v-list-item-title>
            </v-list-item>
          </v-list>
         </v-menu>
      </v-col>
    </v-row>
    <v-card class="rounded-lg" >
      <v-container v-if="this.dataDepartment.length !== 0" :style="!MobileSize ? 'margin-left:100px;padding-right:220px' : ''">
        <v-row class="pt-6">
          <v-col cols="12" dense>
            <h2>รายละเอียดบริษัท</h2><v-divider></v-divider><br/>
            <v-row justify="center">
             <v-col cols="6" md="3">
              <span>ชื่อบริษัท (ไทย)</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].company_name_th}}</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>ชื่อบริษัท (อังกฤษ)</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].company_name_en}}</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>รหัสบริษัท</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].customer_code}}</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>ระยะเวลา</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].type_department}} </span>
             </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" dense>
           <h2>รายละเอียดบริษัทลูก </h2><v-divider></v-divider><br/>
            <v-row justify="center">
             <v-col cols="6" md="3">
              <span>ชื่อบริษัท (ไทย)</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].company_name_th}}</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>ชื่อบริษัท (อังกฤษ)</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].company_name_en}}</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>รหัสลูกค้า</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].customer_code}}</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>รหัสประจำตัวผู้เสียภาษี</span>
             </v-col>
             <v-col cols="6" md="3">
              <span>{{this.dataDepartment[0].tax_id}}</span>
             </v-col>
            </v-row>
          </v-col>
        </v-row><br/><br/>
        <v-tabs  v-model="tab"  fixed-tabs   background-color="grey lighten-3" >
          <v-tab> ฝ่ายและแผนก </v-tab>
          <v-tab> ที่อยู่ใบเสร็จ </v-tab>
          <v-tab> ที่อยู่ที่จัดส่ง </v-tab>
          <v-tab> ผู้ซื้อ </v-tab>
        </v-tabs>
        <v-tabs-items v-model="tab">
         <v-tab-item    >
          <v-card  class="rounded-lg" style="border: 1px solid #EEEEEE;margin-Top:20px" color="basil"  flat>
           <v-card-text>
             <span class="col-sm-8 col-sm">
               <v-row>
                 <v-col cols="1" ><v-img  src="http://**************:8088/img/icon/Company.svg" width="40"></v-img></v-col>
                 <v-col cols="3" style="margin-Top:15px"  justify="center"><h2>ฝ่ายและแผนก</h2></v-col>
               </v-row>
             </span>
          </v-card-text>
           <v-card-text>
             <v-container style="margin-left:15%">
             <v-row>
               <v-col cols="6" v-if="this.company.type_level === 3">รหัสฝ่าย</v-col>
               <v-col cols="6" v-if="this.company.type_level === 3">{{this.dataDepartment[0].division_code}}</v-col>
               <v-col cols="6" v-if="this.company.type_level === 3">ชื่อฝ่าย (ภาษาไทย)</v-col>
               <v-col cols="6" v-if="this.company.type_level === 3">{{this.dataDepartment[0].division_name_th}}</v-col>
               <v-col cols="6" v-if="this.company.type_level === 3">ชื่อฝ่าย (ภาษาอังกฤษ)</v-col>
               <v-col cols="6" v-if="this.company.type_level === 3">{{this.dataDepartment[0].division_name_en}}</v-col>
               <v-col cols="6">รหัสแผนก</v-col>
               <v-col cols="6">{{this.dataDepartment[0].department_code}}</v-col>
               <v-col cols="6">ชื่อแผนก (ภาษาไทย)</v-col>
               <v-col cols="6">{{this.dataDepartment[0].department_name_th}}</v-col>
               <v-col cols="6">ชื่อแผนก (ภาษาอังกฤษ)</v-col>
               <v-col cols="6">{{this.dataDepartment[0].department_name_en}}</v-col>
             </v-row>
             </v-container>
           </v-card-text>
          </v-card>
         </v-tab-item>
        <v-tab-item >
          <v-card  class="rounded-lg" style="border: 1px solid #EEEEEE;margin-Top:20px" color="basil"  flat>
           <v-card-text>
             <span class="col-sm-8 col-sm">
               <v-row>
                 <v-col cols="1" ><v-img  src="http://**************:8088/img/icon/Billing.svg" width="40"></v-img></v-col>
                 <v-col cols="3" style="margin-Top:15px"  justify="center"><h2>ที่อยู่ใบเสร็จ</h2></v-col>
               </v-row>
             </span>
           </v-card-text>
           <v-card-text>
             <v-container style="margin-left:15%">
             <v-row>
               <v-col cols="4">ที่อยู่</v-col>
               <v-col cols="6">{{this.dataDepartment[0].billing_address.address1}}<br/>{{this.dataDepartment[0].billing_address.address2}}<br/>{{this.dataDepartment[0].billing_address.address3}}</v-col>
               <v-col cols="4">รหัสไปรษณีย์</v-col>
               <v-col cols="6">{{this.dataDepartment[0].billing_address.zip_code}}</v-col>
               <v-col cols="4">หมายเลขโทรศัพท์</v-col>
               <v-col cols="6">{{this.dataDepartment[0].billing_tel}}</v-col>
               <v-col cols="4">หมายเลขมือถือ</v-col>
               <v-col cols="6">{{this.dataDepartment[0].billing_phone}}</v-col>
               <v-col cols="4">แฟกซ์</v-col>
               <v-col cols="6">{{this.dataDepartment[0].billing_fax}}</v-col>
             </v-row>
             </v-container>
           </v-card-text>
          </v-card>
         </v-tab-item>
        <v-tab-item >
          <v-card  class="rounded-lg" style="border: 1px solid #EEEEEE;margin-Top:20px" color="basil"  flat>
           <v-card-text>
             <span class="col-sm-8 col-sm">
               <v-row>
                 <v-col cols="1" ><v-img  src="http://**************:8088/img/icon/Shipping.svg" width="40"></v-img></v-col>
                 <v-col cols="3" style="margin-Top:15px" justify="center"><h2>ที่อยู่ที่จัดส่ง</h2></v-col>
               </v-row>
             </span>
           </v-card-text>
           <v-card-text>
             <v-container style="margin-left:15%">
             <v-row>
               <v-col cols="4">ชื่อใบเสร็จผู้รับ</v-col>
               <v-col cols="6">{{this.dataDepartment[0].shipping_receiver}}</v-col>
               <v-col cols="4">ที่อยู่</v-col>
               <v-col cols="6">{{this.dataDepartment[0].shipping_address.address1}} <br/>{{this.dataDepartment[0].shipping_address.address2}} <br/>{{this.dataDepartment[0].shipping_address.address3}}</v-col>
               <v-col cols="4">รหัสไปรษณีย์</v-col>
               <v-col cols="6">{{this.dataDepartment[0].shipping_address.zip_code}}</v-col>
               <v-col cols="4">หมายเลขโทรศัพท์</v-col>
               <v-col cols="6">{{this.dataDepartment[0].shipping_tel}}</v-col>
               <v-col cols="4">หมายเลขมือถือ</v-col>
               <v-col cols="6">{{this.dataDepartment[0].shipping_phone}}</v-col>
               <v-col cols="4">แฟกซ์</v-col>
               <v-col cols="6">{{this.dataDepartment[0].shipping_fax}}</v-col>
             </v-row>
             </v-container>
           </v-card-text>
          </v-card>
         </v-tab-item>
        <v-tab-item >
          <v-card  class="rounded-lg" style="border: 1px solid #EEEEEE;margin-Top:20px" color="basil"  flat>
           <v-card-text>
             <span class="col-sm-8 col-sm">
               <v-row>
                 <v-col cols="1" ><v-img  src="http://**************:8088/img/icon/Purchaser.svg" width="40"></v-img></v-col>
                 <v-col cols="9" style="margin-Top:15px"  justify="center"><h2>ผู้ซื้อ</h2></v-col>
                 <v-col cols="2" justify="end"><v-btn width="1px" @click="Add_Division()" ><v-icon small> mdi-plus</v-icon></v-btn></v-col>
               </v-row>
             </span>
           </v-card-text>
          </v-card>
         </v-tab-item>
        </v-tabs-items>
        <v-row justify="end" style="margin:5px">
         <v-btn align="end" class="ma-2" outlined  color="#27AB9C" @click="GoToDepartment()">
          ย้อนกลับ
         </v-btn>
        </v-row>
      </v-container>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      tab: null,
      dataDepartment: [],
      company_id: '',
      department_id: '',
      company: '',
      items: [
        'ฝ่ายและแผนก', 'วงเงิน', 'ที่อยู่ใบเสร็จ', 'ที่อยู่ที่จัดส่ง', 'ผู้ซื้อ'
      ],
      menu: [
        {
          name: 'เพิ่มฝ่ายและแผนก',
          icon: 'mdi-plus-circle-outline'
        },
        {
          name: 'แก้ไขฝ่ายและแผนก',
          icon: 'mdi-pencil-box-outline'
        }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'แผนก')
    this.$EventBus.$emit('changeNavAdminManage')
    this.company = JSON.parse(Decode.decode(localStorage.getItem('companyData')))
    this.company_id = JSON.parse(Decode.decode(localStorage.getItem('companyData'))).id
    this.department_id = this.$route.params.id
    // this.department_id = localStorage.getItem('department_id')
    this.GetDetailDepartment()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    GoToDepartment () {
      this.$router.push({ path: '/departmentsCompany' })
    },
    GoToCreateAndEdit (item) {
      if (item === 'เพิ่มฝ่ายและแผนก') {
        this.$router.push({ path: '/createDepartment' })
      } else if (item === 'แก้ไขฝ่ายและแผนก') {
        this.$router.push({ path: 'editDepartment', name: 'EditDepartment', params: { id: this.department_id.toString() } })
      }
    },
    async GetDetailDepartment () {
      var data = {
        company_id: this.company_id.toString(),
        department_id: this.department_id.toString()
      }
      await this.$store.dispatch('actionsDetailDepartment', data)
      var response = await this.$store.state.ModuleDepartment.stateDetailDepartment
      this.dataDepartment = response.data
      // console.log('DetailDepartment', response)
    }
  }
}
</script>
