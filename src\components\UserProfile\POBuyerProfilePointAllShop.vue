<template>
  <v-container class="pa-2">
    <v-card elevation="0" width="100%" height="100%" :class="IpadSize ? 'px-0 py-0' : MobileSize ? 'ma-1' : '' ">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">แต้มของฉัน</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#1AB759" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> แต้มของฉัน
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col align="end">
            <span>*เงื่อนไขคะแนนเป็นไปตามที่ร้านค้ากำหนด</span>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-data-table
            :items="dataPrime"
            :headers="headersMain"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            outline
            >
            <template v-slot:top>
              <v-container style="background-color: #e6f5f3; display: flex; justify-content: center; min-height: 70px; align-items: center;">
                <span style="font-size: 18px; font-weight: 700;">รายการแต้มการสั่งซื้อสินค้าจากร้านค้าที่เข้าร่วมแต้มส่วนลด</span>
              </v-container>
              <v-divider class="border-opacity-100"></v-divider>
            </template>
            <template v-slot:[`item.info`]="{ item }">
              <!-- {{ item.id }} -->
              <v-btn icon @click="openDialog1(item)">
                <v-icon>mdi-eye</v-icon>
              </v-btn>
            </template>
            <template v-slot:[`item.bathPerPoint`]="{ item }">
              {{ parseInt(item.point_order_total)/parseInt(item.point_received) }} บาท ต่อ 1 คะแนน
            </template>
          </v-data-table>
        </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-dialog v-model="diaLog1" width="700px">
      <v-card >
        <v-card-title class="pt-3 pr-3">
          <v-row>
            <v-col style="display: flex; align-items: center;">
              <span style="font-size: 20px; font-weight: 600;">{{ dataDialog1.seller_shop_name }}</span>
            </v-col>
            <v-col align="end">
              <v-btn icon @click="diaLog1 = false"><v-icon>mdi-close</v-icon></v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-data-table
        :items="dataDialog1.products"
        :headers="headersDialog1"
        :hide-default-footer="true"
        >
          <template v-slot:[`body.append`]>
            <tr class="sticky-table-footer">
              <td align="center"><span>รวม</span></td>
              <td align="center">{{ dataDialog1.all_order_cost }}</td>
              <td align="center">{{ dataDialog1.all_point }}</td>
            </tr>
          </template>
        </v-data-table>
      </v-card>
    </v-dialog>
    <!-- <pre>
      {{ dataDialog1 }}
    </pre> -->
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
export default {
  data () {
    return {
      diaLog1: false,
      dataDialog1: [],
      dataTemp: [],
      dataPrime: [],
      dataMock: [
        {
          id: 1,
          shop_name: 'ร้าน A',
          all_point: 100,
          all_price: 5050,
          bathPerPoint: '50',
          all_product: [
            {
              product_name: 'STTS-909 Rising Freedom Gundam',
              product_price: '1050',
              point_get: '20'
            },
            {
              product_name: 'STTS-808 Immortal Justice Gundam',
              product_price: '1050',
              point_get: '20'
            },
            {
              product_name: 'ZGMF/A-42S2 Destiny Gundam Spec II',
              product_price: '950',
              point_get: '20'
            },
            {
              product_name: 'ZGMF-56E2/α Force Impulse Gundam Spec II',
              product_price: '950',
              point_get: '20'
            },
            {
              product_name: 'ORB-01 Akatsuki Gundam',
              product_price: '1050',
              point_get: '20'
            }
          ]
        },
        {
          id: 2,
          shop_name: 'ร้าน B',
          all_point: 50,
          all_price: 2500,
          bathPerPoint: '500',
          all_product: [
            {
              product_name: 'ข้าวสาร 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'ข้าวกล้อง 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'ข้าวเหนียว 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'ข้าวไรซ์เบอร์รี่ 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            },
            {
              product_name: 'สังข์หยดพัทลุง 1 กระสอบ',
              product_price: '500',
              point_get: '10'
            }
          ]
        },
        {
          id: 3,
          shop_name: 'ร้าน C',
          all_point: 20,
          all_price: 400,
          bathPerPoint: '20',
          all_product: [
            {
              product_name: 'พวงกุญแจพี่ปูเป้[Naka is real]',
              product_price: '200',
              point_get: '10'
            },
            {
              product_name: 'พวงกุญแจแหม่มแหม่ม',
              product_price: '200',
              point_get: '10'
            }
          ]
        }
      ],
      headersMain: [
        { text: 'ชื่อร้านค้า', value: 'seller_shop_name', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'อัตราส่วนคะแนนที่ได้ต่อยอดคำสั่งซื้อ', value: 'bathPerPoint', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'คะแนนที่ได้', filterable: false, value: 'all_point', sortable: false, align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', filterable: false, sortable: false, value: 'info', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersDialog1: [
        { text: 'รายการสั่งซื้อ', value: 'product_name', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาสินค้า', filterable: false, value: 'product_price', sortable: false, align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'คะแนนที่ได้', filterable: false, sortable: false, value: 'point_get', align: 'center', width: '', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    this.getData()
    this.$EventBus.$emit('changeNav')
  },
  computed: {

  },
  watch: {

  },
  methods: {
    async getData () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionsUserDetailPage', data)
      const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
      this.dataTemp = userdetail.data[0]
      var data1 = {
        user_id: this.dataTemp.id
      }
      await this.$store.dispatch('actionsgetListUserPointByUserID', data1)
      var res = await this.$store.state.ModuleManagePoint.stategetListUserPointByUserID
      // console.log(this.dataTemp.id, res)
      this.dataPrime = res.data
    },
    openDialog1 (item) {
      this.dataDialog1 = []
      this.dataDialog1 = item
      this.diaLog1 = true
    }
  }
}
</script>
