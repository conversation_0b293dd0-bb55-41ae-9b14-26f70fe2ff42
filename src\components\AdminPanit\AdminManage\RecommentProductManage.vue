<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col :cols="MobileSize ? 8 : 6">
          <v-card-title style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการสินค้าแนะนำ</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2 d-flex" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการสินค้าแนะนำ</v-card-title>
        </v-col>
        <v-spacer></v-spacer>
        <!-- <v-col v-if="checkEdit === false">
          <v-autocomplete no-data-text="ไม่พบร้านค้าที่ค้นหา" v-model="selectShop" :items="itemsShop" item-text="name_th" item-value="id" outlined dense placeholder="เลือกร้านค้าภายในระบบ" :menu-props="{ offsetY: true, offsetOverflowAuto: true }" @change="handleSelectChange"></v-autocomplete>
        </v-col> -->
        <v-col :cols="MobileSize ? 4 : 6" v-if="checkEdit" align="end">
          <v-btn rounded color="red" style="color: #fff;" @click="hadleEditProduct()">แก้ไข</v-btn>
        </v-col>
      </v-row>
      <v-row  v-if="checkEdit && listProduct.length === 0">
        <v-col>
          <v-card :style="MobileSize ? 'margin-top: -8vw' : ''">
            <v-card-text class="d-flex align-center flex-column" style="padding: 7vw;">
              <v-img
                src="@/assets/NoProducts.png"
                width="300"
                height="204"
                contain
              ></v-img>
              <span :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'">ไม่มีรายการสินค้าแนะนำ</span>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col>
          <v-card class="pa-2" :style="MobileSize ? 'margin-top: -2vw;' : ''">
            <div class="d-flex">
              <v-col cols="6">
                <span><b>เลือกร้านค้า</b></span>
                <v-autocomplete :disabled="checkEdit" no-data-text="ไม่พบร้านค้าที่ค้นหา" v-model="selectShop" :items="itemsShop" item-text="name_th" item-value="id" outlined dense placeholder="เลือกร้านค้าภายในระบบ" :menu-props="{ offsetY: true, offsetOverflowAuto: true }" @change="handleSelectChange"></v-autocomplete>
              </v-col>
              <v-col cols="6">
                <span><b>เลือกรายการสินค้า</b></span>
                <v-autocomplete :disabled="checkEdit" no-data-text="ไม่พบรายการสินค้า" v-model="selectProduct" :items="filteredProduct" item-text="name" item-value="product_id_main" outlined dense placeholder="เลือกสินค้าภายในร้าน" :menu-props="{ offsetY: true, offsetOverflowAuto: true }" @change="handleSelectProduct()"></v-autocomplete>
              </v-col>
            </div>
            <v-col :style="MobileSize ? 'margin-top: -5vw;' : 'margin-top: -2vw;'" v-if="listProduct.length !== 0">
              <v-card class="pa-2">
                <span><b>รายการสินค้า</b></span>
                <draggable
                :list="listProduct"
                class="list-group d-flex flex-wrap"
                :disabled="checkEdit"
                ghost-class="ghost"
                tag="div"
                @end="reorderIndex"
                >
                <v-col :cols="MobileSize ? 6 : IpadSize ? 4 : IpadProSize ? 3 : 2" v-for="(item, index) in listProduct" :key="item.product_id">
                    <v-card class="pa-2">
                    <div class="d-flex pa-1 justify-end">
                      <!-- <v-chip>{{ item.index }}</v-chip>
                      <v-spacer></v-spacer> -->
                      <v-btn v-if="checkEdit === false" icon x-small style="background-color: #ff5252;" @click="RemoveProduct(index, item)">
                        <v-icon x-small color="white" dark>mdi-close</v-icon>
                      </v-btn>
                    </div>
                    <div class="d-flex flex-column align-center">
                        <span><b>{{ item.index }}</b></span>
                        <img
                        v-if="item.product_image !== '' && item.product_image.length !== 0 && item.product_image !== '-'"
                        :src="item.product_image"
                        width="60"
                        height="60"
                        />
                        <img
                        v-else
                        src="@/assets/NoImage.png"
                        width="60"
                        height="60"
                        />
                        <span class="mt-2" style="font-size: small"><b>sku:</b> {{ item.product_id }}</span>
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span class="text-truncate" v-bind="attrs" v-on="on" style="font-size: small"><b>สินค้า:</b> {{ substring(item.product_name) }}</span>
                          </template>
                          <span>{{ item.product_name }}</span>
                        </v-tooltip>
                    </div>
                    </v-card>
                </v-col>
                </draggable>
                <v-card-text class="d-flex" v-if="checkEdit !== true">
                  <v-btn rounded outlined color="#38b2a4" @click="cancelEdit">ยกเลิก</v-btn>
                  <v-spacer></v-spacer>
                  <v-btn rounded color="#38b2a4" style="color: #fff;" @click="dialogUpdate = true">บันทึก</v-btn>
                </v-card-text>
              </v-card>
            </v-col>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="dialogUpdate" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogUpdate = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-container>
          <div class="d-flex justify-center">
            <v-avatar :size="MobileSize ? 90 : 250" tile><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/Coorperation/modalConfirmEditDialog.png" alt=""></v-avatar>
          </div>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: large; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขรายการสินค้าแนะนำ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายแก้ไขรายการสินค้าแนะนำ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogUpdate = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="updateSortProduct()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import draggable from 'vuedraggable'
import { Decode } from '@/services'
export default {
  components: {
    draggable
  },
  data () {
    return {
      selectShop: 0,
      itemsShop: [],
      search: '',
      detailProduct: [],
      selectProduct: 0,
      listProduct: [],
      checkEdit: true,
      dialogUpdate: false
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredProduct () {
      const selectedIds = this.listProduct.map(item => item.product_id)
      return this.detailProduct.filter(product => !selectedIds.includes(product.product_id_main))
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/recommentProductManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/recommentProductManage' }).catch(() => {})
      }
    }
  },
  async created () {
    await this.getListSortProduct()
    await this.getShopData()
  },
  methods: {
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsgetIshiplistAllShop')
      var response = await this.$store.state.ModuleDashboardTransport.stategetIshiplistAllShop
      if (response.code === 200) {
        // var statAllShop = [{ name_th: 'ทั้งหมด', id: -3 }]
        this.itemsShop = response.data
        // this.itemsShop = statAllShop.concat(this.itemsShop)
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${response.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
      }
      this.$store.commit('closeLoader')
    },
    handleSelectChange () {
      if (this.selectShop !== null && this.selectShop !== 0) {
        this.getDetailProduct()
      }
    },
    handleSelectProduct () {
      if (this.selectProduct !== 0 && this.selectProduct !== null) {
        const selected = this.detailProduct.find(
          item => item.product_id_main === this.selectProduct
        )
        this.listProduct.push({
          index: this.listProduct.length + 1,
          product_id: this.selectProduct,
          product_image: selected.media_path,
          product_name: selected.name
        })
      }
    },
    async getDetailProduct () {
      // console.log(shopID, 7777)
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        shopId: this.selectShop,
        limit: -1,
        pages: 1,
        search: this.search
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}product/detail_product`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: data
      }).then(response => {
        this.detailProduct = response.data.data.filter(item => item.status === 'active' && item.product_type !== 'service' && item.sell_product_to_ext_buyer === '1') || []
        // this.shopCount = response.data.shopCount
      })
      this.$store.commit('closeLoader')
    },
    // async getDetailProduct (offset) {
    //   this.$store.commit('openLoader')
    //   var data = {
    //     seller_shop_id: this.selectShop,
    //     page: 1,
    //     offset: -1,
    //     search: this.search,
    //     product_type: '',
    //     message_status: '',
    //     stock_status: ''
    //   }
    //   await this.$store.dispatch('actionGetDetailProduct', data)
    //   var res = await this.$store.state.ModuleShop.stateGetDetailProduct
    //   if (res.code === 200) {
    //     this.detailProduct = res.data.list_product
    //     console.log(this.detailProduct, '----')
    //     // this.countProduct = res.data.total_products_count
    //     // this.maxPage = res.data.max_page
    //     // this.totalProduct = res.data.total_list_product
    //     // if (this.selectedItem.length === this.countProduct && this.selectedItem.length !== 0) {
    //     //   this.selectAll = true
    //     // } else {
    //     //   this.selectAll = false
    //     // }
    //     // console.log(this.countProduct, 'sss')
    //     this.$store.commit('closeLoader')
    //   } else {
    //     this.$swal.fire({
    //       showConfirmButton: false,
    //       timer: 5000,
    //       timerProgressBar: true,
    //       icon: 'error',
    //       text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
    //     })
    //     this.$store.commit('closeLoader')
    //   }
    // },
    hadleEditProduct () {
      this.$store.commit('openLoader')
      this.checkEdit = false
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    async getListSortProduct () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetListSortProduct')
      var res = await this.$store.state.ModuleAdminManage.stateGetListSortProduct
      if (res.status === 200) {
        var list = res.data
        this.listProduct = list.map(item => {
          return {
            index: item.pinned_priority,
            product_id: item.id,
            product_image: item.images_URL,
            product_name: item.name
          }
        })
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    async updateSortProduct () {
      this.$store.commit('openLoader')
      var data = {
        products_pin: this.listProduct.map(item => {
          return {
            product_id: item.product_id,
            priority: item.index
          }
        })
      }
      await this.$store.dispatch('actionsUpdateSortProduct', data)
      var res = await this.$store.state.ModuleAdminManage.stateUpdateSortProduct
      if (res.result === 'success') {
        this.selectProduct = ''
        this.selectShop = ''
        this.getListSortProduct()
        this.dialogUpdate = false
        this.checkEdit = true
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    reorderIndex () {
      this.listProduct.forEach((item, idx) => {
        item.index = idx + 1
      })
    },
    substring (data) {
      if (this.MobileSize || this.IpadSize) {
        return data.length > 5 ? data.substring(0, 5) + '...' : data
      } else if (this.IpadProSize) {
        return data.length > 8 ? data.substring(0, 8) + '...' : data
      } else {
        return data.length > 10 ? data.substring(0, 10) + '...' : data
      }
    },
    async cancelEdit () {
      this.$store.commit('openLoader')
      this.selectProduct = ''
      this.selectShop = ''
      await this.getListSortProduct()
      this.checkEdit = true
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    RemoveProduct (index, item) {
      this.listProduct.splice(index, 1)
      this.listProduct.forEach((item, idx) => {
        item.index = idx + 1
      })
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    }
  }
}
</script>

<style>

</style>
