<template lang="html">
  <v-container>
    <ManageInventory/>
  </v-container>
</template>

<script>
export default {
  components: {
    ManageInventory: () => import('@/components/Inventory/ManageInventoryUI')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/inventoryMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/inventory' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>

<style lang="css" scoped>
</style>
