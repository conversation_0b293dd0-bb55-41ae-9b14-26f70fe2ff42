<template>
  <div class="text-center">
    <v-dialog v-model="ModalCoupon" width="684" persistent :style="MobileSize ? 'z-index: 16000103;' : ''">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 8px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                  <div :class="MobileSize ? 'pt-6' : 'pt-7 px-2'">
                    <v-card-text v-if="!MobileSize" class="pa-0">
                      <v-form ref="formCheckThai" :lazy-validation="lazy">
                        <v-col class="py-0">
                          <v-row class="align-center pb-6">
                            <v-col class="py-0">
                              <v-row class="">
                                <v-col cols="12" class="py-0">
                                <v-row dense class="pt-3 pb-6 align-center">
                                  <v-img src="@/assets/fi_5189229.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 18px; font-weight: 600;">
                                    {{$t('ListCode.Title')}}
                                  </span>
                                </v-row>
                                  <v-text-field v-model="search" height="40" hide-details :placeholder="$t('ListCode.SearchPlaceholder')" :rules="[rules.validInput]" oninput="this.value = this.value.replace(/[^A-Za-z0-9\s]/,g, '').replace(/(\..*)\./g, '$1')" @keypress="isLetterEng($event)" @keydown.enter="submit" outlined dense>
                                    <template slot="append">
                                      <v-img src="@/assets/Magnifer.png" max-width="24" max-height="24">
                                      </v-img>
                                    </template>
                                  </v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-card class="rounded-lg mt-6" elevation="0"  style="background-color: #FCFCFC; max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                            <v-card-text v-if="InetRelation.length !== 0" class="pb-0 px-0">
                              <v-col cols="12" class="pt-2 pb-2 mb-4">
                                <v-row dense class="align-center">
                                  <v-img src="@/assets/user.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 16px; font-weight: 600; color: #333333;">{{$t('ListCode.EmployeeList')}} {{InetRelation.length}} {{$t('ListCode.ListName')}}</span>
                                </v-row>
                              </v-col>
                              <v-col class="py-0" v-for="(item, index) in InetRelation" :key="index" cols="12" align="center">
                                <v-card class="rounded-lg mb-6 py-2 px-2 justify-start d-flex" elevation="0" style=" background-color: #F4F7F9;">
                                  <v-card-text class="px-0 py-2">
                                    <v-row class="d-flex align-center">
                                      <v-col cols="3" class="pa-0 text-start">
                                        <v-col class="py-2 pr-0">
                                          <span style="font-size: 16px; color: #636363;">{{$t('ListCode.Department')}} :</span>
                                        </v-col>
                                        <!-- <v-col class="py-2 pr-0">
                                          <span style="font-size: 16px; color: #636363;">ส่วนงาน :</span>
                                        </v-col> -->
                                        <v-col class="py-2 pr-0">
                                          <span style="font-size: 16px; color: #636363;">{{$t('ListCode.Company')}} :</span>
                                        </v-col>
                                        <v-col class="py-2 pr-0">
                                          <span style="font-size: 16px; color: #636363;">{{$t('ListCode.EmployeeName')}} :</span>
                                        </v-col>
                                      </v-col>
                                      <v-col cols="9" class="pa-0 text-start">
                                        <v-col class="py-2">
                                          <v-row dense no-gutters class="justify-space-between">
                                            <span style="font-size: 16px; font-weight: 600; color: #333333;">{{item.team}}</span>
                                            <v-btn v-if="!item.dont_delete" color="red" text x-small @click="deteleUser(index)"><v-img src="@/assets/trash-2.png" width="20" height="20">
                                          </v-img><span class="text-decoration-underline" style="font-size: 16px; font-weight: 500;">{{$t('ListCode.Delete')}}</span></v-btn>
                                          </v-row>
                                        </v-col>
                                        <!-- <v-col class="py-2">
                                          <span style="font-size: 16px; font-weight: 600; color: #333333;">ส่วนงาน :</span>
                                        </v-col> -->
                                        <v-col class="py-2">
                                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{item.company}}</span>
                                        </v-col>
                                        <v-col class="py-2">
                                          <span style="font-size: 16px; font-weight: 600; color: #333333;">{{item.titile_name + item.first_name_th + ' ' + item.last_name_th}}</span>
                                        </v-col>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                              </v-col>
                            </v-card-text>
                            <v-card-text v-else>
                              <v-col class="py-0" cols="12" align="center">
                                <v-img width="70%" height="70%" src="@/assets/ImageINET-Marketplace/ICONProfile/OBJECTS.png"></v-img>
                              </v-col>
                              <v-col class="pb-0 pt-4" cols="12" align="center">
                                <span style="font-size: 20px; font-weight: 700;">{{$t('ListCode.NotAdded')}}</span>
                              </v-col>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-form>
                    </v-card-text>
                    <v-card-text v-else class="pa-0">
                      <v-form ref="formCheckThai" :lazy-validation="lazy">
                        <v-col class="py-0">
                          <v-row class="align-center pb-6">
                            <v-col class="py-0">
                              <v-row class="">
                                <v-col cols="12" class="py-0">
                                <v-row dense class="pt-3 pb-6 align-center">
                                  <v-img src="@/assets/fi_5189229.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 16px; font-weight: 600;">
                                    {{$t('ListCode.Title')}}
                                  </span>
                                </v-row>
                                  <v-text-field v-model="search" height="40" hide-details :placeholder="$t('ListCode.SearchPlaceholder')" :rules="[rules.validInput]" oninput="this.value = this.value.replace(/[^A-Za-z0-9\s]/,g, '').replace(/(\..*)\./g, '$1')" @keypress="isLetterEng($event)" @keydown.enter="submit" outlined dense>
                                    <template slot="append">
                                      <v-img src="@/assets/Magnifer.png" max-width="24" max-height="24">
                                      </v-img>
                                    </template>
                                  </v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-card class="rounded-lg mt-6" elevation="0"  style="background-color: #FCFCFC; max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                            <v-card-text v-if="InetRelation.length !== 0" class="pb-0 px-0">
                              <v-col cols="12" class="pt-2 pb-2 mb-4">
                                <v-row dense class="align-center">
                                  <v-img src="@/assets/user.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 14px; font-weight: 600; color: #333333;">{{$t('ListCode.EmployeeList')}} {{InetRelation.length}} {{$t('ListCode.ListName')}}</span>
                                </v-row>
                              </v-col>
                              <v-col class="pa-0" v-for="(item, index) in InetRelation" :key="index" cols="12" align="center">
                                <v-card class="rounded-lg mb-6 py-2 px-2 justify-start d-flex" elevation="0" style=" background-color: #F4F7F9;">
                                  <v-card-text class="px-0 py-2">
                                    <v-row class="d-flex align-center">
                                      <v-col cols="5" class="pa-0 text-start">
                                        <v-col class="py-2 pr-0">
                                          <span style="font-size: 12px; color: #636363;">{{$t('ListCode.Department')}} :</span>
                                        </v-col>
                                        <!-- <v-col class="py-2 pr-0">
                                          <span style="font-size: 12px; color: #636363;">ส่วนงาน :</span>
                                        </v-col> -->
                                        <v-col class="py-2 pr-0">
                                          <span style="font-size: 12px; color: #636363;">{{$t('ListCode.Company')}} :</span>
                                        </v-col>
                                        <v-col class="py-2 pr-0">
                                          <span style="font-size: 12px; color: #636363;">{{$t('ListCode.EmployeeName')}} :</span>
                                        </v-col>
                                      </v-col>
                                      <v-col cols="7" class="pa-0 text-start">
                                        <v-col class="py-2">
                                          <v-row dense no-gutters class="justify-space-between">
                                            <span class="text-truncate" style="font-size: 12px; font-weight: 600; color: #333333; max-width: 150px;">{{item.team}}</span>
                                            <v-btn v-if="!item.dont_delete" color="red" text x-small @click="deteleUser(index)"><v-img src="@/assets/trash-2.png" width="18" height="18">
                                          </v-img></v-btn>
                                          </v-row>
                                        </v-col>
                                        <!-- <v-col class="py-2">
                                          <span class="text-truncate" style="font-size: 12px; font-weight: 600; color: #333333; max-width: 150px;">ส่วนงาน :</span>
                                        </v-col> -->
                                        <v-col class="py-2">
                                          <span class="text-truncate" style="font-size: 12px; font-weight: 600; color: #333333; max-width: 150px;">{{item.company}}</span>
                                        </v-col>
                                        <v-col class="py-2">
                                          <span class="dtext-truncate" style="font-size: 12px; font-weight: 600; color: #333333; max-width: 150px;">{{item.titile_name + item.first_name_th + ' ' + item.last_name_th}}</span>
                                        </v-col>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                              </v-col>
                            </v-card-text>
                            <v-card-text v-else>
                              <v-col class="py-0" cols="12" align="center">
                                <v-img width="70%" height="70%" src="@/assets/ImageINET-Marketplace/ICONProfile/OBJECTS.png"></v-img>
                              </v-col>
                              <v-col class="pb-0 pt-4" cols="12" align="center">
                                <span style="font-size: 20px; font-weight: 700;">{{$t('ListCode.NotAdded')}}</span>
                              </v-col>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-form>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="px-12 justify-center" style="height: 100px; background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="Close()" >{{$t('ListCode.Cancel')}}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="InetRelation.length === 0 || (InetRelation.length === 1 && InetRelation[0].dont_delete)" class="px-10 white--text " style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="confirm()" >{{$t('ListCode.Confirm')}}</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="px-4 justify-center" style="background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 130px;" color="#27AB9C" @click="Close()" >{{$t('ListCode.Cancel')}}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="InetRelation.length === 0 || (InetRelation.length === 1 && InetRelation[0].dont_delete)" class="px-10 white--text" style="border-radius: 40px; width: 130px;" color="#27AB9C" @click="confirm()" >{{$t('ListCode.Confirm')}}</v-btn>
        </v-card-actions>
        <!-- <v-card-actions v-if="!MobileSize" class="justify-space-between" style="height: 88px; background-color: #F5FCFB;">
        <v-btn outlined class="px-10" style="border-radius: 40px;" color="#27AB9C" @click="Close()" >ยกเลิก</v-btn>
        <v-btn :disabled="InetRelation.length === 0 || (InetRelation.length === 1 && InetRelation[0].dont_delete)" class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="confirm()" >ตกลง</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="justify-space-between px-2" style="height: 88px; background-color: #F5FCFB;">
        <v-btn outlined class="px-10" style="border-radius: 40px;" color="#27AB9C" @click="Close()" >ยกเลิก</v-btn>
        <v-btn :disabled="InetRelation.length === 0 || (InetRelation.length === 1 && InetRelation[0].dont_delete)" class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="confirm()" >ตกลง</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirm" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CancelSubmit()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{$t('ListCode.TitleConfirm')}}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{$t('ListCode.Description')}}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CancelSubmit()">{{$t('ListCode.Cancel')}}</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirm()">{{$t('ListCode.Save')}}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      rules: {
        validInput: value => /^[A-Za-z0-9,\s]*$/.test(value) || this.$t('ListCode.InvalidCouponCondition')
      },
      ModalConfirm: false,
      lazy: false,
      InetRelation: [],
      Add: 1,
      pricePoint: 0,
      MaxPoint: 0,
      checkradio: false,
      PointData: [],
      selectPoint: '',
      inputValue: '',
      show: true,
      UserPoint: 0,
      ShopPointDetail: [],
      ShopID: '',
      dataCouponList: [],
      search: '',
      checkSelect: false,
      CouponID: '',
      CollectID: '',
      CouponImage: '',
      CouponName: '',
      CouponCode: '',
      CouponDescription: '',
      CollectStartdate: '',
      CollectEnddate: '',
      UseStartdate: '',
      UseEnddate: '',
      CouponType: '',
      Quota: '',
      UseCount: '',
      UserCap: '',
      SpendMinimum: '',
      DiscountAmount: '',
      ProductList: '',
      DiscountType: '',
      SellerShopID: '',
      Status: '',
      Cards: [],
      Shipping: [],
      Free: [],
      ModalDetailCoupon: false,
      selectCoupon: [],
      showAllCards: false,
      showAllShipping: false,
      showAllFree: false,
      ModalCoupon: false,
      showProductPriceDiscount: '3',
      showShippingDiscount: '3',
      showFreeGiftCoupon: '3',
      page: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredItemsCards () {
      return this.dataCouponList.filter(item => {
        return item.coupon_name.toLowerCase().includes(this.search.toLowerCase())
      })
    }
  },
  mounted () {
    this.$EventBus.$on('submitStart', this.submitStart)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('submitStart')
    })
  },
  created () {
    // this.getListCoupon()
    // console.log('%c Hello Bug ', 'background: red; color: #000; padding: 4px; border-radius: 2px; margin-left: 1ch;', this.Test)
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  watch: {
    // selectPoint (item) {
    //   console.log('object', item)
    // }
  },
  methods: {
    isLetterEng (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z0-9():&.,-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    CancelSubmit () {
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
        this.submitStart()
      }
      this.ModalConfirm = false
      this.search = ''
    },
    clearUser () {
      localStorage.removeItem('InetRelationShip')
      this.InetRelation = []
      this.submitStart()
      this.$EventBus.$emit('ListCode')
    },
    async confirm () {
      this.$store.commit('openLoader')
      var data = {
        list_user: this.InetRelation
      }
      await this.$store.dispatch('actionsCheckInetRelation', data)
      var res = await this.$store.state.ModuleCart.stateCheckInetRelation
      if (res.data.all_can_use) {
        this.$store.commit('closeLoader')
        localStorage.setItem('InetRelationShip', JSON.stringify(res.data.list_user))
        this.$EventBus.$emit('ListCode')
        this.ModalCoupon = false
        this.ModalConfirm = false
        this.search = ''
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 2500,
        //   timerProgressBar: true,
        //   icon: 'warning',
        //   html: '<h3>ไม่พบโค้ด</h3>'
        // })
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
        // this.ModalCoupon = false
        this.ModalConfirm = false
        // this.search = ''
      }
    },
    deteleUser (index) {
      this.InetRelation.splice(index, 1)
      // localStorage.setItem('InetRelationShip', JSON.stringify(this.InetRelation))
      // this.$EventBus.$emit('ListCode')
    },
    async submitStart () {
      if (this.InetRelation.length === 0) {
        this.$store.commit('openLoader')
        var SearchArray = []
        var data = {
          select_user_id: SearchArray
        }
        await this.$store.dispatch('actionsSearchInetRelation', data)
        var res = await this.$store.state.ModuleCart.stateSearchInetRelation
        if (res.message === 'Success.') {
          this.$store.commit('closeLoader')
          res.data.forEach(element => {
            this.InetRelation.push(element)
          })
          this.search = ''
        } else if (res.code === 400 && res.message === 'Code Not Found.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>' + this.$t('ListCode.NotFound') + '</h3>'
          })
          this.ModalCoupon = false
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: res.message
            })
          }
          this.ModalCoupon = false
        }
      }
    },
    extractFirst10Chars (input) {
      const alphanumeric = input.match(/[a-zA-Z0-9]/g)
      return alphanumeric ? alphanumeric.slice(0, 10).join('') : ''
    },
    async submit (event) {
      event.preventDefault()
      if (this.$refs.formCheckThai.validate(true)) {
        this.$store.commit('openLoader')
        var CheckIndex
        var SearchArray = []
        var SameCode = []
        SearchArray = this.search.split(',')
        this.results = SearchArray.map(this.extractFirst10Chars)
        var data = {
          select_user_id: this.results
        }
        if (this.InetRelation.length !== 0) {
          SameCode = this.results.filter(code =>
            this.InetRelation.some(element => element.code === code)
          )
          CheckIndex = this.InetRelation.findIndex(element =>
            this.results.includes(element.code)
          )
        } else {
          CheckIndex = -1
        }
        if (CheckIndex === -1) {
          await this.$store.dispatch('actionsSearchInetRelation', data)
          var res = await this.$store.state.ModuleCart.stateSearchInetRelation
          if (res.message === 'Success.') {
            this.$store.commit('closeLoader')
            res.data.forEach(element => {
              this.InetRelation.push(element)
            })
            this.search = ''
          } else if (res.code === 400 && res.message === 'Code Not Found.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 4500,
              timerProgressBar: true,
              icon: 'warning',
              html: '<h3>' + this.$t('ListCode.NotFound') + '</h3>'
            })
            // this.ModalCoupon = false
          } else {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            // this.ModalCoupon = false
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>มีโค้ด <b style="color: red;">${SameCode.join(', ')}</b> อยู่แล้ว กรุณาตรวจสอบอีกครั้ง</h3>`
          })
        }
      } else {
        this.ModalCoupon = true
        this.$swal.fire({ icon: 'warning', text: this.$t('ListCode.InvalidInput'), showConfirmButton: false, timer: 2000 })
      }
    },
    async submit2 () {
      if (this.$refs.formCheckThai.validate(true)) {
        this.$store.commit('openLoader')
        var CheckIndex
        var SearchArray = []
        var SameCode = []
        SearchArray = this.search.split(',')
        this.results = SearchArray.map(this.extractFirst10Chars)
        var data = {
          select_user_id: this.results
        }
        if (this.InetRelation.length !== 0) {
          SameCode = this.results.filter(code =>
            this.InetRelation.some(element => element.code === code)
          )
          CheckIndex = this.InetRelation.findIndex(element =>
            this.results.includes(element.code)
          )
        } else {
          CheckIndex = -1
        }
        if (CheckIndex === -1) {
          await this.$store.dispatch('actionsSearchInetRelation', data)
          var res = await this.$store.state.ModuleCart.stateSearchInetRelation
          if (res.message === 'Success.') {
            this.$store.commit('closeLoader')
            res.data.forEach(element => {
              this.InetRelation.push(element)
            })
            // this.InetRelation.push(res.data[0])
            // console.log(this.InetRelation)
            this.search = ''
          } else if (res.code === 400 && res.message === 'Code Not Found.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 4500,
              timerProgressBar: true,
              icon: 'warning',
              html: '<h3>' + this.$t('ListCode.NotFound') + '</h3>'
            })
            // this.ModalCoupon = false
          } else {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            // this.ModalCoupon = false
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>' + this.$t('ListCode.AlreadyUsed_Prefix') + ` <b style="color: red;">${SameCode.join(', ')}</b> ` + this.$t('ListCode.AlreadyUsed_Suffix') + '</h3>'
          })
        }
      } else {
        this.ModalCoupon = true
        this.$swal.fire({ icon: 'warning', text: this.$t('ListCode.InvalidInput'), showConfirmButton: false, timer: 2000 })
      }
    },
    checkInput () {
      if (parseInt(this.inputValue) > parseInt(this.MaxPoint)) {
        this.inputValue = parseInt(this.MaxPoint)
      } else if (parseInt(this.inputValue) < 0) {
        this.inputValue = 0
      }
    },
    async getListUserPointByUser () {
      this.PointData = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
      var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var data = {
        role_user: RoleUser.role,
        customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
        seller_shop_id: this.ShopID,
        company_id: onedata.cartData.company_id,
        com_perm_id: onedata.cartData.com_perm_id
      }
      await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
      var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
      if (res.result === 'SUCCESS') {
        this.PointData = res.data[0]
        // console.log('this.PointData', this.PointData)
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('ListCode.PointError') + '</h3>'
        })
      }
    },
    formatDateToShow (data) {
      if (!data) return null
      const date = data.split('T')
      // console.log('date', date)
      const [year, month, day] = date[0].split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    async getListCoupon () {
      this.Cards = []
      this.Shipping = []
      this.Free = []
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      this.ShopID = couponData.shop_id
      await this.getListUserPointByUser()
      // this.getListUserPointBySellerShopID()
      // this.getSellerShopPointDetail()
      await this.$store.dispatch('actionsListCoupon', couponData)
      var res = await this.$store.state.ModuleCart.stateListCoupon
      if (res.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.dataCouponList = res.data.coupon
        var CouponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
        var pricePoint = parseFloat(this.PointData.x_baht) / parseFloat(this.PointData.x_point)
        // console.log('CouponData', CouponData)
        this.pricePoint = pricePoint
        this.MaxPoint = parseInt(((0.25 * CouponData.net_price) / parseFloat(pricePoint)) - 1)
        res.data.coupon.forEach(element => {
          const existingCardIndex = this.Cards.findIndex(card => card.coupon_id === element.coupon_id)
          const existingShippingIndex = this.Shipping.findIndex(shipping => shipping.coupon_id === element.coupon_id)
          const existingFreeIndex = this.Free.findIndex(free => free.coupon_id === element.coupon_id)
          if (existingCardIndex === -1 && existingShippingIndex === -1 && existingFreeIndex === -1) {
            if (element.coupon_type === 'discount') {
              this.Cards.push({ ...element })
            } else if (element.coupon_type === 'free_shipping') {
              this.Shipping.push({ ...element })
            } else {
              this.Free.push({ ...element })
            }
          }
        })
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        if (res.message === 'This user is Unauthorized') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>' + this.$t('ListCode.SystemError') + '</h3>'
          })
        }
      }
    },
    backstep () {
      this.ModalDetailCoupon = false
    },
    OpenDetail (item) {
      this.ModalDetailCoupon = true
      this.CouponID = item.coupon_id
      this.CollectID = item.collect_id
      this.CouponImage = item.coupon_image
      this.CouponName = item.coupon_name
      this.CouponCode = item.coupon_code
      this.CouponDescription = item.coupon_description
      this.CollectStartdate = item.collect_startdate
      this.CollectEnddate = item.collect_enddate
      this.UseStartdate = item.use_startdate
      this.UseEnddate = item.use_enddate
      this.CouponType = item.coupon_type
      this.Quota = item.quota
      this.UseCount = item.use_count
      this.UserCap = item.user_cap
      this.SpendMinimum = item.spend_minimum
      this.DiscountAmount = item.discount_amount
      this.DiscountMaximum = item.discount_maximum
      this.ProductList = item.product_list
      this.DiscountType = item.discount_type
      this.SellerShopID = item.seller_shop_id
      this.Status = item.status
    },
    Close () {
      if (this.InetRelation.length !== 1) {
        var CheckInetRelation = []
        if (localStorage.getItem('InetRelationShip') !== null) {
          CheckInetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
        } else {
          CheckInetRelation = []
        }
        if (this.InetRelation.length === CheckInetRelation.length) {
          const isMatching = this.InetRelation.every((item, index) => {
            return item.code === CheckInetRelation[index].code
          })
          if (isMatching) {
            this.ModalCoupon = false
            this.search = ''
          } else {
            this.ModalConfirm = true
          }
        } else {
          this.ModalConfirm = true
        }
      } else {
        this.ModalCoupon = false
        this.search = ''
      }
    },
    clearCoupon () {
      this.selectCoupon = ''
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
      this.checkSelect = false
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.coupon = []
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    clearPoint () {
      this.selectPoint = false
      this.inputValue = 0
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
      this.checkSelect = false
    },
    ShowProductPriceDiscount () {
      this.showAllCards = true
      this.showProductPriceDiscount = this.Cards.length
    },
    ShowShippingDiscount () {
      this.showAllShipping = true
      this.showShippingDiscount = this.Shipping.length
    },
    ShowFreeGiftCoupon () {
      this.showAllFree = true
      this.showFreeGiftCoupon = this.Free.length
    },
    async open (page, item, point, baht) {
      if (localStorage.getItem('InetRelationShip') !== null) {
        this.InetRelation = JSON.parse(localStorage.getItem('InetRelationShip'))
      } else {
        this.InetRelation = []
      }
      this.submitStart()
      if (item !== '') {
        this.selectCoupon = item
      }
      if (point !== null && point !== '') {
        this.inputValue = await point / parseFloat(baht)
        // console.log('this.inputValue', this.inputValue)
        // console.log('point', point, parseInt(baht))
      } else {
        point = 0
        this.inputValue = point
      }
      if (this.inputValue === 0) {
        // console.log(1)
        this.selectPoint = false
        this.checkradio = false
      } else {
        // console.log(2)
        this.selectPoint = true
        this.checkradio = true
      }
      this.page = page
      this.ModalCoupon = true
    }
  }
}
</script>

<style>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-input--selection-controls {
  margin-top: 0px;
}
</style>
<style scoped>
.dynamic-font-card {
  font-size: calc(1vw + 1vh + .5vmin); /* Adjust this value as needed */
  color: red;
}
.dynamic-font {
  display: block; /* Ensures the span takes up the full width */
}
</style>
<style scoped>
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
