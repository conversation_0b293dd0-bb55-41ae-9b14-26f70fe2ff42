import Vue from 'vue'
// import '@mdi/font/css/materialdesignicons.css'
// import Vuetify from 'vuetify/lib/framework'
import Vuetify, {
  VCard,
  VRating,
  VToolbar,
  VCardText,
  VCardTitle,
  VCardActions,
  VPagination,
  VDataTable,
  VDialog,
  VForm,
  VCol,
  VRow,
  VContainer,
  VBtn,
  VSelect,
  VIcon,
  VAppBar,
  VSpacer,
  VImg,
  VToolbarTitle,
  VTextField,
  VExpansionPanels,
  VExpansionPanel,
  VExpansionPanelHeader,
  VExpansionPanelContent,
  VStepper,
  VStepperStep,
  VStepperContent,
  VDivider,
  VSwitch,
  VCheckbox,
  VListItem,
  VListItemAction,
  VListItemContent,
  VListItemTitle,
  VChip,
  VMain,
  VFooter,
  VAvatar,
  VBadge,
  VAlert,
  VNavigationDrawer,
  VSystemBar,
  VAppBarNavIcon,
  VAppBarTitle,
  VBreadcrumbs,
  VCarousel,
  VAutocomplete,
  VSimpleCheckbox,
  VFileInput,
  VOtpInput,
  VRadio,
  VRadioGroup,
  VTextarea,
  VBtnToggle,
  VChipGroup,
  VListItemGroup,
  VSlideGroup,
  VHover,
  VListItemAvatar,
  VListItemIcon,
  VListItemSubtitle,
  VMenu,
  VOverlay,
  VDatePicker,
  VTimePicker,
  VProgressCircular,
  VProgressLinear,
  VSheet,
  VSkeletonLoader,
  VSubheader,
  VTabs,
  VTab,
  VTabItem,
  VTabsItems,
  VTimeline,
  VTimelineItem,
  VTooltip,
  VTreeview
} from 'vuetify/lib'

Vue.use(Vuetify, {
  components: {
    VCard,
    VRating,
    VToolbar,
    VCardText,
    VCardTitle,
    VCardActions,
    VPagination,
    VDataTable,
    VDialog,
    VForm,
    VCol,
    VRow,
    VContainer,
    VBtn,
    VSelect,
    VIcon,
    VAppBar,
    VSpacer,
    VImg,
    VToolbarTitle,
    VTextField,
    VExpansionPanels,
    VExpansionPanel,
    VExpansionPanelHeader,
    VExpansionPanelContent,
    VStepper,
    VStepperStep,
    VStepperContent,
    VDivider,
    VSwitch,
    VCheckbox,
    VListItem,
    VListItemAction,
    VListItemContent,
    VListItemTitle,
    VChip,
    VMain,
    VFooter,
    VAvatar,
    VBadge,
    VAlert,
    VNavigationDrawer,
    VSystemBar,
    VAppBarNavIcon,
    VAppBarTitle,
    VBreadcrumbs,
    VCarousel,
    VAutocomplete,
    VSimpleCheckbox,
    VFileInput,
    VOtpInput,
    VRadio,
    VRadioGroup,
    VTextarea,
    VBtnToggle,
    VChipGroup,
    VListItemGroup,
    VSlideGroup,
    VHover,
    VListItemAvatar,
    VListItemIcon,
    VListItemSubtitle,
    VMenu,
    VOverlay,
    VDatePicker,
    VTimePicker,
    VProgressCircular,
    VProgressLinear,
    VSheet,
    VSkeletonLoader,
    VSubheader,
    VTabs,
    VTab,
    VTabItem,
    VTabsItems,
    VTimeline,
    VTimelineItem,
    VTooltip,
    VTreeview
  }
})

export default new Vuetify({
  icons: {
    iconfont: 'mdi'
  },
  theme: {
    themes: {
      light: {
        primary: '#27AB9C',
        // backgroundTable: '#F3F5F7',
        backgroundTable: '#E6F5F3',
        fontTable: '#333333',
        fontTableDebit: '#1ab759',
        fontTableCredit: '#d1392b'
      }
    }
  }
})
