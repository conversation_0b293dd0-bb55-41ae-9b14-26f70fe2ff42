<template>
  <div class="ma-5">
    <!-- <v-container> -->
      <v-row justify="start">
        <h1 class="mt-4 ml-4">แก้ไขผู้ซื้อ</h1>
      </v-row>
      <v-card
      class="mt-2"
      outlined
      width="100%"
      >
        <v-form
        ref="form"
        :lazy-validation="lazy"
        >
          <v-row justify="center" align="center" dense class="mt-2">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>แผนก<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-select
              :items="dataBuyer.departmentname"
              v-model="dataBuyer.departmentname[0]"
              placeholder="--- กรุณาเลือกแผนก ---"
              dense
              disabled
              outlined
              ></v-select>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>ประเภทผู้อนุมัติ<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-select
              :items="itemsAprrover"
              v-model="dataBuyer.approverType[0]"
              placeholder="--- กรุณาเลือกประเภทผู้อนุมัติ ---"
              item-text="name"
              item-value="value"
              dense
              outlined
              ></v-select>
            </v-col>
          </v-row>
          <v-row justify="center" align="center" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>ผู้อนุมัติ</p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-select
              :disabled="dataBuyer.approverType[0].value !== 1 ? false : true"
              v-model="approver"
              :items="itemAppover"
              item-text="name"
              item-value="value"
              placeholder="--- กรุณาเลือกผู้อนุมัติ ---"
              @change="addAppover()"
              dense
              outlined
              ></v-select>
            </v-col>
            <v-col cols="12" md="5" sm="2" xs="12">
            </v-col>
          </v-row>
          <v-row justify="center">
            <v-card
            outlined
            width="85%"
            height="100%"
            class="mb-4"
            >
              <v-card-title>
                ผู้อนุมัติ
              </v-card-title>
              <v-divider></v-divider>
              <v-card-text>
                <v-row justify="center" v-for="(items, index) in dataBuyer.approverName" :key="index">
                  <v-col cols="12" md="1" sm="2" xs="12" class="mt-2">
                    <p>ชื่อ</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
                    <span>{{ items.name }}</span>
                  </v-col>
                  <v-col cols="12" md="1" sm="2" xs="12" class="mt-2">
                    <p>วงเงิน</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12">
                    <v-text-field
                      v-model="items.value"
                      outlined
                      placeholder="00.00"
                      dense
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="1" sm="2" xs="12" class="mt-2">
                    <p>บาท</p>
                  </v-col>
                  <v-col cols="12" md="1" sm="2" xs="12" class="mt-1">
                    <v-btn icon @click="deleteApprover(index)"><v-icon>mdi-trash-can</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-row>
        </v-form>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined color="success" dense @click="back()">ย้อนกลับ</v-btn>
          <v-btn color="success" @click="submit()" dense>บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    <!-- </v-container> -->
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      dataBuyer: [],
      approver: '',
      itemsDepartment: [],
      itemsAprrover: [
        { name: 'ไม่มีผู้อนุมัติ', value: 1 },
        { name: 'ผู้อนุมัติแบบหนึ่งคน', value: 2 },
        { name: 'ผู้อนุมัติแบบหลายคน', value: 3 },
        { name: 'ผู้อนุมัติแบบทั้งหมด', value: 4 }
      ],
      itemAppover: [
        { name: 'นายกีฬา สั่งซื้อ', value: 'นายกีฬา สั่งซื้อ' },
        { name: 'เก็ท เก็ท', value: 'เก็ท เก็ท' }
      ]
    }
  },
  created () {
    this.dataBuyer = JSON.parse(Decode.decode(localStorage.getItem('DataBuyer')))
    // console.log('dataBuyer', this.dataBuyer)
  },
  methods: {
    back () {
      this.$router.push('/detailuser').catch(() => {})
    },
    addAppover () {
      const result = this.dataBuyer.approverName.find((member) => { return member.name === this.approver })
      if (result === undefined) {
        this.dataBuyer.approverName.push({ name: this.approver, value: '' })
      } else {
        this.$swal.fire({
          icon: 'warning',
          title: 'ผู้อนุมัติมีอยู่แล้ว',
          text: 'คุณเลือกผู้อนุมัติคนนี้แล้ว'
        })
      }
    },
    deleteApprover (val) {
      this.dataBuyer.approverName.splice(val, val + 1)
    }
  }
}
</script>
