<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ใบเสนอราคา</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
          <v-icon color="#1AB759" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> ใบเสนอราคา
      </v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="py-0 px-4">
          <a-tabs @change="selectOrder">
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)" style="border-radius: 40px;">{{ countPOAll }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">สร้างรายการสั่งซื้อแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8" style="border-radius: 40px;">{{ countPOSuccess }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">ยังไม่ชำระเงิน <v-chip small text-color="rgb(28, 61, 119)" color="rgb(215, 226, 246)" style="border-radius: 40px;">{{ countPONotPaid }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">อนุมัติแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8" style="border-radius: 40px;">{{ countPOActive }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">รออนุมัติ <v-chip small text-color="#E9A016" color="#FEF6E6" style="border-radius: 40px;">{{ countPOWaitingDWF }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="5"><span slot="tab">รออนุมัติเอกสาร/รอผู้ซื้ออนุมัติ <v-chip small text-color="#FF710B" color="#FBECE1" style="border-radius: 40px;">{{ countPOWaiting }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="6"><span slot="tab">รอผู้อนุมัติ <v-chip small text-color="#FF710B" color="#FBECE1" style="border-radius: 40px;">{{ countPOWaitingApprove }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="7"><span slot="tab">ปฏิเสธ <v-chip small text-color="#F5222D" color="rgba(245, 34, 45, 0.10)" style="border-radius: 40px;">{{ countPOReject }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="8"><span slot="tab">ยกเลิก <v-chip small text-color="#636363" color="#E6E6E6" style="border-radius: 40px;">{{ countPOCancel }}</v-chip></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col cols="12">
          <v-row dense>
            <v-col cols="12" md="7" sm="12" :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'px-4 pb-4'">
              <v-text-field v-model="search" @keyup="searchData(search)" style="border-radius: 8px;" dense hide-details outlined placeholder="ค้นหาจากหมายเลขใบเสนอราคา, ชื่อผู้ซื้อ, ชื่อบริษัท">
                <v-icon slot="append">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="5" sm="12" :class="MobileSize ? 'px-3 pb-4' : ''">
              <v-row dense>
                <v-col cols="4" md="3" sm="3" class="mt-2">
                  <span style="font-size: 16px;" :class="MobileSize ? 'pl-2' : IpadSize ? 'pl-4 pr-0' : 'pt-5 pr-0'">
                    Pay Type :
                  </span>
                </v-col>
                <v-col cols="8" md="8" sm="8" :class="MobileSize ? 'pr-2' : IpadSize ? 'pl-0 pr-0' : 'ml-2 mr-4'">
                <!-- selectType -->
                  <v-select
                    outlined
                    dense
                    v-model="statePayType"
                    :items="['ทั้งหมด','recurring','onetime','general']"
                    @change="selectType()"
                    style="border-radius: 8px;"
                    placeholder="ทั้งหมด"
                    hide-details
                  ></v-select>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-row dense v-if="disableTable === true">
            <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-4 pr-3 mb-2 mt-2' : 'pl-4 pr-2 mb-0 mt-3'">
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-if="StateStatus === 0">รายการใบเสนอราคาทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่สำเร็จทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 2">รายการใบเสนอราคาที่ยังไม่ชำระเงิน {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 3">รายการใบเสนอราคาที่อนุมัติแล้วทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 4">รายการใบเสนอราคาที่รออนุมัติทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 5">รายการใบเสนอราคาที่รออนุมัติเอกสาร/รอผู้ซื้ออนุมัติทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 6">รายการใบเสนอราคาที่ปฏิเสธทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;" v-else-if="StateStatus === 7">รายการใบเสนอราคาที่ยกเลิกทั้งหมด {{ countSearch }} รายการ</span>
            </v-col>
            <v-col cols="12" md="6" sm="12" align="end" :class="!MobileSize ? 'pr-6' : ''">
              <v-btn color="primary" height="40" rounded :block="MobileSize || IpadSize ? true : false" @click="exportListQT()">export รายการใบเสนอราคา</v-btn>
            </v-col>
          </v-row>
          <v-row dense v-if="disableTable === false">
            <v-col cols="12" md="6" sm="6" :class="!MobileSize ? 'pl-4 pr-3 mb-2 mt-2' : 'pl-4 pr-2 mb-0 mt-3'">
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="StateStatus === 0">รายการใบเสนอราคาทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่สำเร็จทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 2">รายการใบเสนอราคาที่ยังไม่ชำระเงิน {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 3">รายการใบเสนอราคาที่อนุมัติแล้วทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 4">รายการใบเสนอราคาที่รออนุมัติทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 5">รายการใบเสนอราคาที่รออนุมัติเอกสาร/รอผู้ซื้ออนุมัติทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 6">รายการใบเสนอราคาที่ปฏิเสธทั้งหมด {{ countSearch }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 7">รายการใบเสนอราคาที่ยกเลิกทั้งหมด {{ countSearch }} รายการ</span>
            </v-col>
            <v-col cols="12" md="6" sm="6" align="end" :class="!MobileSize ? 'pr-6' : ''">
              <v-btn color="primary" height="40" rounded :block="MobileSize ? true : false" @click="exportListQT()">export รายการใบเสนอราคา</v-btn>
            </v-col>
          </v-row>
        </v-col>
        <!-- <v-col v-if="disableTable === false" cols="12" md="6" sm="12" class=""
            :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'pl-2 pr-2 mb-3'">
            <v-text-field v-model="search" dense hide-details outlined
                placeholder="ค้นหาจากชื่อบริษัทผู้ซื้อหรือหมายเลขใบเสนอราคา">
                <v-icon slot="append">mdi-magnify</v-icon>
            </v-text-field>
        </v-col> -->
        <!-- <v-col v-if="disableTable === false"  cols="12" md="5" align="end">
          <v-row  class="d-flex justify-end" no-gutters>
            <v-col cols="3" class="mt-2">
              <span style="font-size: 16px;" class="pt-5">
                Pay Type :
              </span>
            </v-col>
            <v-col cols="5" class="ml-2 mr-4">
              <v-select
                outlined
                dense
                v-model="statePayType"
                :items="['ทั้งหมด','recurring','onetime']"
                @change="selectType()"
                placeholder="ทั้งหมด"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-col> -->
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-4 my-5" min-height="436">
            <v-data-table
              :headers="isJV === 'yes' ? headers : headersNoJV"
              :items="DataTable"
              :page.sync="page"
              style="width:100%;"
              height="100%"
              :footer-props="{'items-per-page-text':'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50]}"
              :items-per-page="options.itemsPerPage"
              :server-items-length="totalItems"
              :options.sync="options"
              @update:options="updateOptions"
              no-results-text="ไม่พบบริษัทผู้ซื้อหรือหมายเลขใบเสนอราคาที่ค้นหา"
              no-data-text="ไม่มีรายการสินค้าในตาราง"
            >
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'success'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0FEE8" text-color="#52C41A">สร้างรายการสั่งซื้อ</v-chip>
                </span>
                <span v-else-if="item.status === 'approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0FEE8" text-color="#52C41A">อนุมัติแล้ว</v-chip>
                </span>
                <span v-else-if="item.status === 'not_paid'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" text-color="rgb(28, 61, 119)" color="rgb(215, 226, 246)">ยังไม่ชำระเงิน</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รออนุมัติเอกสาร</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รอผู้อนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_dwf'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" class="ma-2" color="#FEF6E6" text-color="#E9A016">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'check_doc'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" class="ma-2" color="#FBECE1" text-color="#FF710B">รอผู้ซื้ออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'reject'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" class="ma-2" text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">ปฏิเสธ</v-chip>
                </span>
                <span v-else>
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" class="ma-2" text-color="#636363" color="#E6E6E6">ยกเลิก</v-chip>
                </span>
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                {{ item.payment_transaction_number === null ? '-' : item.payment_transaction_number}}
              </template>
              <template v-slot:[`item.buyer_name`]="{ item }">
                {{ item.buyer_name !== '' ? item.buyer_name : '-'}}
              </template>
              <template v-slot:[`item.pay_type`]="{ item }">
                <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.start_date_contract`]="{ item }">
                {{ item.start_date_contract === null ? '-' :  new Date(item.start_date_contract).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.end_date_contract`]="{ item }">
                {{ item.end_date_contract === null ? '-' : new Date(item.end_date_contract).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.pdf_path`]="{ item }">
                <!-- <v-btn :disabled="item.status === 'check_doc' && item.is_order_JV === 'yes'" style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"  outlined small @click="goPDFQU(item)">
                  <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                </v-btn> -->
                <span v-if="(item.status === 'check_doc' && item.is_order_JV === 'yes') || item.pdf_path_qu === ''">-</span>
                <span v-else @click="goPDFQU(item)" style="text-decoration: underline; color: #27AB9C; cursor: pointer;">QT{{item.order_number}}</span>
              </template>
              <template v-slot:[`item.pdf_cs_path`]="{ item }">
                <!-- <v-btn :disabled="item.status === 'check_doc' && item.is_order_JV === 'yes'" style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"  outlined small @click="goPDFCS(item)">
                  <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                </v-btn> -->
                <span v-if="(item.status === 'check_doc' && item.is_order_JV === 'yes') || item.pdf_path_cs === ''">-</span>
                <span v-else @click="goPDFCS(item)" style="text-decoration: underline; color: #27AB9C; cursor: pointer;">CS{{item.order_number}}</span>
              </template>
              <template v-slot:[`item.ref_qt_document_id`]="{ item }">
                {{ item.ref_qt_document_id ? item.ref_qt_document_id : '-' }}
              </template>
              <template v-slot:[`item.ref_cs_document_id`]="{ item }">
                {{ item.ref_cs_document_id ? item.ref_cs_document_id : '-' }}
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <!-- <v-btn
                  width="24"
                  height="24"
                  :disabled="item.status === 'check_doc' && item.is_order_JV === 'yes'"
                  style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  outlined icon small @click="goDetailQU(item)">
                  <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                </v-btn>
                <v-btn :disabled="item.status === 'check_doc' && item.is_order_JV === 'yes'" small text rounded color="#27AB9C" @click="goDetailQU(item)">
                  <b style="text-decoration: underline;">รายละเอียด</b>
                </v-btn> -->
                <v-btn :disabled="item.status === 'check_doc' && item.is_order_JV === 'yes'" text rounded color="#27AB9C" small @click="goDetailQU(item)">
                  <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                  <b>รายละเอียด</b><v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0">
            <b>คุณยังไม่มีรายการใบเสนอราคา</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 1">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่สร้างรายการสั่งซื้อแล้ว</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 2">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่ยังไม่ชำระเงิน</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 3">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่อนุมัติแล้ว</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 4">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่รออนุมัติ</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 5">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่รออนุมัติเอกสาร</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 6">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่ปฏิเสธ</b>
          </h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 7">
            <b>คุณยังไม่มีรายการใบเสนอราคาที่ยกเลิก</b>
          </h2>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Tabs } from 'ant-design-vue'
import axios from 'axios'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      isJV: '',
      ShopDetailSale: '',
      countPOAll: 0,
      countPOSuccess: 0,
      countPOActive: 0,
      countPONotPaid: 0,
      countPOWaiting: 0,
      countPOWaitingApprove: 0,
      countPOWaitingDWF: 0,
      countPOReject: 0,
      countPOCancel: 0,
      countSearch: 0,
      dialogDetail: false,
      name: 'รายละเอียดตำแหน่งและสิทธิ์การใช้งาน',
      orderList: [],
      StateStatus: 0,
      showCountOrder: 0,
      showCountRequest: 0,
      totalItems: 0,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      statusFilter: 'all',
      disableTable: false,
      companyData: [],
      seller_shop_id: null,
      dataRole: '',
      search: '',
      page: 1,
      keyCheckHead: 0,
      dialog_rank: false,
      pay_type: 'all',
      statePayType: '',
      headers: [
        { text: 'หมายเลขใบเสนอราคา', value: 'QT_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ชื่อผู้ซื้อ', value: 'buyer_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ชื่อบริษัทผู้ซื้อ', value: 'company_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '270' },
        { text: 'ใบเสนอราคา', value: 'pdf_path', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'Cost Sheet', value: 'pdf_cs_path', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'Pay Type', value: 'pay_type', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'วันที่สร้างรายการ', filterable: false, value: 'created_at', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'วันที่เริ่มสัญญา', filterable: false, value: 'start_date_contract', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'วันที่สิ้นสุดสัญญา', filterable: false, value: 'end_date_contract', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'Document ID QT', filterable: false, value: 'ref_qt_document_id', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'Document ID CS', filterable: false, value: 'ref_cs_document_id', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'จัดการ', filterable: false, value: 'action', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' }
      ],
      headersNoJV: [
        { text: 'หมายเลขใบเสนอราคา', value: 'QT_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ชื่อผู้ซื้อ', value: 'buyer_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ชื่อบริษัทผู้ซื้อ', value: 'company_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '270' },
        { text: 'ใบเสนอราคา', value: 'pdf_path', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'Pay Type', value: 'pay_type', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'วันที่สร้างรายการ', filterable: false, value: 'created_at', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'วันที่เริ่มสัญญา', filterable: false, value: 'start_date_contract', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'วันที่สิ้นสุดสัญญา', filterable: false, value: 'end_date_contract', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '155' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'Document ID QT', filterable: false, value: 'ref_qt_document_id', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'Document ID CS', filterable: false, value: 'ref_cs_document_id', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'จัดการ', filterable: false, value: 'action', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      timer: null,
      orderListMap: [],
      //   headers: [
      //     { text: 'รูปแบบ', value: 'qu_number', sortable: false, class: 'backgroundTable fontTable--text' },
      //     { text: 'จำนวนที่ถูกสร้าง', value: 'created_by', sortable: false, class: 'backgroundTable fontTable--text' },
      //     { text: 'วันที่สร้าง', value: 'created_at', sortable: false, class: 'backgroundTable fontTable--text' },
      //     { text: 'วันที่แก้ไขล่าสุด', value: 'updated_at', sortable: false, class: 'backgroundTable fontTable--text' },
      //     { text: 'สถานะ', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
      //     { text: 'จัดการ', value: 'manages', sortable: false, class: 'backgroundTable fontTable--text' }
      //   ],
      data: []
    }
  },
  computed: {
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/QuotationAllMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/QuotationAll' }).catch(() => { })
      }
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    }
  },
  async created () {
    // console.log('StateStatus', this.StateStatus)
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    // if (localStorage.getItem('CompanyData') !== null) {
    // this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
    if (this.dataRole.role !== 'sale_order') {
      this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
      // console.log('shopDetail', this.seller_shop_id)
    } else {
      this.ShopDetailSale = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
    }
    //   this.ListDataTable()
    var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      if (dataDetail.can_use_function_in_shop.manage_order === '1') {
        // this.getListData()
        await this.getListDataV2(this.search)
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    window.scrollTo(0, 0)
  },
  methods: {
    async exportListQT () {
      this.$store.commit('openLoader')
      var sellerShopID
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.dataRole.role !== 'sale_order') {
        sellerShopID = this.seller_shop_id.id
      } else {
        sellerShopID = this.ShopDetailSale.seller_shop_id
      }
      await axios({
        method: 'GET',
        url: `${process.env.VUE_APP_BACK_END}api/list_QT_seller/export?seller_shop_id=${sellerShopID}&choice=null&find_pay_type=${this.pay_type === 'all' ? null : this.pay_type}`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        fileLink.setAttribute('download', `list_qt_seller_${sellerShopID}.xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(() => {
        this.$store.commit('closeLoader')
      })
    },
    selectType () {
      if (this.statePayType === 'ทั้งหมด' || this.statePayType === '') {
        this.pay_type = 'all'
      } else if (this.statePayType === 'recurring') {
        this.pay_type = 'recurring'
      } else if (this.statePayType === 'onetime') {
        this.pay_type = 'onetime'
      } else if (this.statePayType === 'general') {
        this.pay_type = 'general'
      }
      this.getListDataV2(this.search)
    },
    searchData (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.getListDataV2(val)
      }, 500)
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    goDetailQU (item) {
      // console.log('goDetailQU')
      var QUNumber = item.order_number
      var id
      if (this.dataRole.role !== 'sale_order') {
        id = this.seller_shop_id.id
      } else {
        id = this.ShopDetailSale.seller_shop_id
      }
      // var comID = item.company_id
      localStorage.setItem('detailItemQU', Encode.encode(item))
      if (this.MobileSize === false) {
        this.$router.push({ path: `/QuotationDetail?QUNumber=${QUNumber}&id=${id}` }).catch(() => { })
      } else {
        this.$router.push({ path: `/QuotationDetailMobile?QUNumber=${QUNumber}&id=${id}` }).catch(() => { })
      }
    },
    async selectOrder (item) {
      this.statusFilter = ''
      if (item === 0) {
        this.statusFilter = 'all'
      } else if (item === 1) {
        this.statusFilter = 'success'
      } else if (item === 2) {
        this.statusFilter = 'not_paid'
      } else if (item === 3) {
        this.statusFilter = 'approve'
      } else if (item === 4) {
        this.statusFilter = 'waiting_dwf'
      } else if (item === 5) {
        this.statusFilter = 'waiting'
      } else if (item === 6) {
        this.statusFilter = 'waiting_approve'
      } else if (item === 7) {
        this.statusFilter = 'reject'
      } else if (item === 8) {
        this.statusFilter = 'cancel'
      }
      this.page = 1
      await this.getListDataV2(this.search)
    },
    goPDFQU (item) {
      window.open(`${item.pdf_path_qu}`)
    },
    goPDFCS (item) {
      window.open(`${item.pdf_path_cs}`)
    },
    updateOptions (options) {
      this.options = options
      this.page = options.page
      this.getListDataV2(this.search)
    },
    async getListDataV2 (textsearch) {
      var sellerShopID
      this.DataTable = []
      this.$store.commit('openLoader')
      if (this.dataRole.role !== 'sale_order') {
        sellerShopID = this.seller_shop_id.id
      } else {
        sellerShopID = this.ShopDetailSale.seller_shop_id
      }
      const data = {
        seller_shop_id: sellerShopID,
        pay_type: this.pay_type === 'all' ? null : this.pay_type,
        page: this.page,
        offset: this.options.itemsPerPage,
        status: this.statusFilter,
        search: textsearch
      }
      await this.$store.dispatch('actionsNewListQTSellerV2', data)
      const response = await this.$store.state.ModuleShop.stateNewListQTSellerV2
      if (response.result === 'Success') {
        if (response.message === 'List Order Document Success.') {
          this.countPOAll = response.data.count_all
          this.countPOReject = response.data.count_reject
          this.countPOSuccess = response.data.count_success
          this.countPONotPaid = response.data.count_not_paid
          this.countPOActive = response.data.count_approve
          this.countPOWaiting = response.data.count_waiting
          this.countPOWaitingApprove = response.data.count_waiting_approve
          this.countPOWaitingDWF = response.data.count_waiting_dwf
          this.countPOCancel = response.data.count_cancel
          this.isJV = response.data.is_JV
          if (response.data.list_order.length !== 0) {
            this.disableTable = true
            this.DataTable = response.data.list_order.map(x => {
              return {
                created_at: x.created_at,
                QT_number: x.order_number,
                order_number: x.order_number,
                company_name: x.company_name !== null ? x.company_name : '-',
                status: x.status,
                buyer_name: x.buyer_name,
                start_date_contract: x.start_date_contract,
                end_date_contract: x.end_date_contract,
                pdf_path_cs: x.CS_path,
                pay_type: x.pay_type,
                ref_cs_document_id: x.document_id_CS,
                ref_qt_document_id: x.document_id_QT,
                pdf_path_qu: x.QT_path,
                is_order_JV: x.is_order_JV
              }
            })
            if (this.statusFilter === 'all') {
              this.totalItems = response.data.count_all
            } else if (this.statusFilter === 'success') {
              this.totalItems = response.data.count_success
            } else if (this.statusFilter === 'not_paid') {
              this.totalItems = response.data.count_not_paid
            } else if (this.statusFilter === 'approve') {
              this.totalItems = response.data.count_approve
            } else if (this.statusFilter === 'waiting_dwf') {
              this.totalItems = response.data.count_waiting_dwf
            } else if (this.statusFilter === 'waiting') {
              this.totalItems = response.data.count_waiting
            } else if (this.statusFilter === 'waiting_approve') {
              this.totalItems = response.data.count_waiting_approve
            } else if (this.statusFilter === 'reject') {
              this.totalItems = response.data.count_reject
            } else if (this.statusFilter === 'cancel') {
              this.totalItems = response.data.count_cancel
            }
            if (textsearch !== '') {
              this.countSearch = response.data.count_search
              this.totalItems = response.data.count_search
            } else {
              this.countSearch = this.totalItems
            }
          } else {
            this.disableTable = false
            this.totalItems = 0
            this.countSearch = 0
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: 'ผู้ใช้งานนี้ไม่มีสิทธิ์การเข้าถึงใบเสนอราคาบริษัท',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/' }).catch(() => { })
        }
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ text: 'เกิดข้อผิดพลาด', icon: 'error', timer: 2500, showConfirmButton: false })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep table {
  tbody tr td:last-child,
  thead tr th:last-child {
    position: sticky !important;
    position: -webkit-sticky !important;
    right: 0;
    z-index: 10;
    background: white;
  }
  thead tr th:last-child {
    z-index: 11;
  }
}
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
  .v-text-field--box .v-input__slot, .v-text-field--outline .v-input__slot{
       min-height:36px;
     }
</style>
