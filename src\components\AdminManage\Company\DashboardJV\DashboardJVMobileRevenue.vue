<template>
  <v-container>
    <v-card elevation="0">
      <v-row no-gutters class="d-flex" align="center">
        <v-col cols="12" class="pr-3 d-flex mt-3 justify-end">
          <v-btn @click="openSelectFilter()"
            elevation="0"
            outlined
            height="32px"
            class="custom-btn"
            style="border-radius: 40px; background: #FFF"
          >
            <v-icon style="color:#27AB9C">mdi-filter-outline</v-icon>
            <span style="color:#27AB9C" class="exportButtonText">ตัวกรอง</span>
          </v-btn>
        </v-col>
      </v-row>
      <v-row no-gutters>
        <v-col cols="12" class="pr-3" align="end">
          <div v-if="selectedItem === 'รายการสั่งซื้อสินค้าทั้งหมด'">
            <v-btn v-if="saleOrder.length !== 0" class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'orderlist', 'รายการสั่งซื้อสินค้าทั้งหมด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 เซอร์วิสขายดี'">
            <v-btn v-if="bestSeller.length !== 0" class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestsoldproduct', 'TOP_10_เซอร์วิสขายดี')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 มูลค่าการสั่งซื้อ'">
            <v-btn v-if="orderValue.length !== 0" class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestrevenuproduct', 'TOP_10_มูลค่าการสั่งซื้อ')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด'">
            <v-btn v-if="orderValue.length !== 0" class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'topbuyers', 'TOP_10_ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" class="pl-7 pr-7">
          <v-select
            v-model="selectedItemShop"
            :items="itemsShop"
            item-text="name_th"
            item-value="id"
            label="เลือกบริษัท"
            height="22px"
            dense
            outlined
            :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
            @change="handleSelectChange"
          ></v-select>
        </v-col>
      </v-row>
      <v-row class="pl-4 pr-4">
        <v-col cols="12" class="pb-0">
          <v-select
            v-model="selectedItem"
            :items="itemsHeader"
            class="subTitleText"
            height="22px"
            dense
            :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
          ></v-select>
        </v-col>
      </v-row>
      <v-row class="pl-4 pr-4">
        <v-col>
          <div v-if="selectedItem === 'ข้อมูลรายได้'">
          <!-- start ข้อมูลรายได้ -->
            <!-- กราฟ -->
            <v-card class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
              <v-layout align-center justify-center>
                <v-flex>
                  <v-card>
                    <v-card-title>
                      <v-row>
                        <v-col cols="8">
                          <v-avatar rounded size="20">
                            <v-img contain :src="statisticsIconPath"></v-img>
                          </v-avatar>
                          <span class="ml-2" style="font-size: 16px; font-style: normal; font-weight: 400;">กราฟแสดงรายได้</span>
                        </v-col>
                        <v-col cols="4" align="end">
                          <v-avatar rounded size="27" class="mt-2">
                            <v-img contain :src="graphLineIconPath"></v-img>
                          </v-avatar>
                          <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">รายได้</span>
                        </v-col>
                      </v-row>
                    </v-card-title>
                    <v-card-text>
                      <div class="chart-container">
                        <apexchart width="400" height="400" type="line" :options="chartOptions" :series="chartSeries"></apexchart>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-card>
            <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
              <v-row class="pb-4">
              <!-- ยอดขาย -->
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0">
                    <v-row>
                      <v-col cols="12">
                        <v-avatar rounded size="60">
                          <v-img contain :src="Icon2"></v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="12">
                        <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalSale).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color:#333333">ยอดขายทั้งหมด <br/> (บาท)</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <!-- จำนวนรายการสั่งซื้อทั้งหมด -->
              <v-row class="pb-4">
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0">
                    <v-row>
                      <v-col cols="12">
                        <v-avatar rounded size="60">
                          <v-img contain :src="Icon3"></v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="12">
                        <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ totalOrder ? Number(totalOrder).toLocaleString(undefined) : '0' }}</span>
                      </v-col>
                      <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color:#333333">จำนวนรายการสั่งซื้อทั้งหมด <br/> (รายการ)</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row >
              <!-- ค่าธรรมเนียม -->
              <v-row class="pb-4">
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0">
                    <v-row>
                      <v-col cols="12">
                        <v-avatar rounded size="60">
                          <v-img contain :src="Icon4"></v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="12">
                        <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalFee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color:#333333">ค่าธรรมเนียมทั้งหมด <br/> (บาท)</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <!-- ยอดเงินสุทธิ -->
              <v-row>
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0">
                    <v-row>
                      <v-col cols="12">
                        <v-avatar rounded size="60">
                          <v-img contain :src="Icon6"></v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="12">
                        <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalNetAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                      <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color:#333333">ยอดเงินสุทธิทั้งหมด <br/> (บาท)</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
            </v-card>
          <!-- end ข้อมูลรายได้ -->
          </div>
          <div v-if="selectedItem === 'รายการสั่งซื้อสินค้าทั้งหมด'">
          <!-- start รายการสั่งซื้อสินค้าทั้งหมด -->
          <v-row>
            <v-col cols="12" class="pt-0" style="text-align: center;">
              <span style="font-size: 14px; font-style: normal; font-weight: 400; color: #38C9B9;"><b>({{ totalOrder }} รายการ)</b></span>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-data-table
                v-if="saleOrder.length !== 0"
                :headers="headers"
                :items="saleOrder"
                :items-per-page="5"
                class="elevation-0"
                height="600px"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                item-key="i"
                >
                <template v-slot:item="{ item }">
                  <v-card class="mb-4" style="border-radius: 12px; border: 1px solid #38C9B9;" elevation="0">
                    <v-card-text>
                        <v-row>
                          <v-col cols="12">
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">วันที่ทำรายการ</b>
                              </v-col>
                              <v-col cols="5">
                                {{ new Date(item.paid_datetime).toLocaleDateString('th-TH', { timeZone: "UTC", year: 'numeric', month: 'long', day: 'numeric' }) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">รายชื่อลูกค้า</b>
                              </v-col>
                              <v-col cols="5">
                                {{ item.buyer_name }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">เลขที่ทำรายการสั่งซื้อ</b>
                              </v-col>
                              <v-col cols="5">
                                {{ item.order_number }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">ราคา (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">ค่าธรรมเนียม (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.vat_price_payment).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">ค่าคอมมิชชั่น/Affiliate (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.total_affiliate).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">คูปองส่วนลด/แต้มส่วนลด (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.discount_coupon).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">ส่วนลดของระบบ (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.discount_ngc).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">ค่า GP (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.total_gp).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">ค่าขนส่ง (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                                <b style="font-size: 14px;">จำนวนเงินที่ได้รับ (บาท)</b>
                              </v-col>
                              <v-col cols="5">
                                {{ Number(item.total_shop).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col cols="7">
                              </v-col>
                              <v-col cols="5">
                                <v-btn class="pl-0" elevation="0" style="background: #FFF" @click="openDialog(item)">
                                  <span style="font-size: 14px; color: #27AB9C">รายการสินค้า</span>
                                </v-btn>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                    </v-card-text>
                  </v-card>
                </template>
              </v-data-table>
              <v-card v-else height="200px" elevation="0" class="d-flex align-center justify-center">
                <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
              </v-card>
            </v-col>
          </v-row>
          <!-- end รายการสั่งซื้อสินค้าทั้งหมด -->
          </div>
          <div v-if="selectedItem === 'TOP 10 เซอร์วิสขายดี'">
          <!-- start TOP 10 เซอร์วิสขายดี -->
            <v-card v-if="bestSeller.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <v-row>
                <v-col cols="12">
                  <v-row no-gutters>
                    <v-col v-for="(item, index) in bestSeller" :key="index" cols="12" class="mb-4">
                      <v-card height="100%" style="border: 1px solid #38C9B9;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <!-- 1 -->
                            <!-- Conditionally render specific avatar for index 0 -->
                            <v-avatar v-if="index === 0" rounded size="43">
                              <v-img contain :src="goldMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    1
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 2 -->
                            <v-avatar v-else-if="index === 1" rounded size="43">
                              <v-img contain :src="silverMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    2
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 3 -->
                            <v-avatar v-else-if="index === 2" rounded size="43">
                              <v-img contain :src="bronzeMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    3
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- For other indices, render a different avatar -->
                            <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 1 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                              <!-- <span>No image</span> -->
                              <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else rounded size="44" color="#FFF">
                              <v-img contain :src="item.product_image"></v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="two-lines">{{ item.product_name }}</span>
                            <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #27AB9C">
                                {{ Number( item.sold ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} เซอร์วิส
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 เซอร์วิสขายดี -->
          </div>
          <div v-if="selectedItem === 'TOP 10 มูลค่าการสั่งซื้อ'">
          <!-- start TOP 10 มูลค่าการสั่งซื้อ -->
            <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <v-row>
                <!-- First half -->
                <v-col cols="12">
                  <v-row>
                    <v-col v-for="(item, index) in orderValue" :key="index" cols="12">
                      <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <v-avatar v-if="index === 0" rounded size="43">
                              <!-- Content for index 0 -->
                              <v-img contain :src="goldMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    1
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 2 -->
                            <v-avatar v-else-if="index === 1" rounded size="43">
                              <v-img contain :src="silverMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    2
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 3 -->
                            <v-avatar v-else-if="index === 2" rounded size="43">
                              <v-img contain :src="bronzeMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    3
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- For other indices, render a different avatar -->
                            <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 1 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                              <!-- <span>No image</span> -->
                              <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else rounded size="44" color="#FFF">
                              <v-img contain :src="item.product_image"></v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="two-lines">{{ item.product_name }}</span>
                            <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #FE6F07">
                                {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 มูลค่าการสั่งซื้อ -->
          </div>
          <div v-if="selectedItem === 'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด'">
          <!-- start TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด -->
            <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <!-- Top three -->
              <v-row justify="center" class="mb-0 pb-0">
                <v-col cols="4" class="d-flex justify-end mb-0 mt-3 pb-0">
                  <v-card v-if="topBuyers.length >= 2" height="140px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
                    <v-row>
                      <v-col align="center">
                        <v-avatar rounded size="40" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                          <v-img contain :src="silverMedalIconPath">
                            <span
                              class="display-1 font-weight-bold"
                              style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                            >
                              <font style="font-size: 20px;">
                                2
                              </font>
                            </span>
                          </v-img>
                        </v-avatar>
                        <v-avatar v-if="topBuyers[1].user_image === null || topBuyers[1].user_image ===  'not found image'" rounded size="50" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </v-avatar>
                        <v-avatar v-else size="50" color="#FFF">
                          <v-img contain :src="topBuyers[1].user_image"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-0 pb-0">
                        <span class="one-lines">{{ topBuyers[1].buyer_name }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-1 pl-0 pr-0">
                        <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                          <span class="vchipFontSize" style="color: #1B5DD6">
                            <!-- {{ Number(topBuyers[1].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท -->
                            {{ formatPrice(topBuyers[1].sum_price) }} บาท
                          </span>
                        </v-chip>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>

                <v-col cols="4" class="d-flex justify-center mb-0 pb-0">
                  <v-card v-if="topBuyers.length >= 1" height="160px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
                    <v-row>
                      <v-col align="center">
                        <v-avatar rounded size="40" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                          <v-img contain :src="goldMedalIconPath">
                            <span
                              class="display-1 font-weight-bold"
                              style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                            >
                              <font style="font-size: 20px;">
                                1
                              </font>
                            </span>
                          </v-img>
                        </v-avatar>
                        <v-avatar v-if="topBuyers[0].user_image === null || topBuyers[0].user_image === 'not found image'" rounded size="60" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </v-avatar>
                        <v-avatar v-else size="60" color="#FFF">
                          <v-img contain :src="topBuyers[0].user_image"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-0 pb-0">
                        <span class="one-lines">{{ topBuyers[0].buyer_name }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-1 pl-0 pr-0">
                        <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                          <span class="vchipFontSize" style="color: #1B5DD6">
                            <!-- {{ Number(topBuyers[0].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท -->
                            {{ formatPrice(topBuyers[0].sum_price) }} บาท
                          </span>
                        </v-chip>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>

                <v-col cols="4" class="d-flex mb-0 mt-3 pb-0 mt-3">
                  <v-card v-if="topBuyers.length >= 3" height="140px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
                    <v-row>
                      <v-col align="center">
                        <v-avatar rounded size="40" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                          <v-img contain :src="bronzeMedalIconPath">
                            <span
                              class="display-1 font-weight-bold"
                              style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                            >
                              <font style="font-size: 20px;">
                                3
                              </font>
                            </span>
                          </v-img>
                        </v-avatar>
                        <v-avatar v-if="topBuyers[2].user_image === null || topBuyers[2].user_image === 'not found image'" rounded size="50" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </v-avatar>
                        <v-avatar v-else size="50" color="#FFF">
                          <v-img contain :src="topBuyers[2].user_image"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-0 pb-0">
                        <span class="one-lines">{{ topBuyers[2].buyer_name }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-1 pl-0 pr-0">
                        <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                          <span class="vchipFontSize" style="color: #1B5DD6">
                            <!-- {{ Number(topBuyers[2].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท -->
                            {{ formatPrice(topBuyers[2].sum_price) }} บาท
                          </span>
                        </v-chip>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <v-row class="mt-0 pt-0">
                <v-col cols="12">
                  <v-row>
                    <v-col v-for="(item, index) in topBuyers.slice(3, 10)" :key="index" cols="12">
                      <v-card height="100%" style="border: 1px solid #1B5DD6;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 4 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.user_image === null || item.user_image === 'not found image'" rounded size="44" color="#FFF">
                              <!-- <span>No image</span> -->
                              <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else size="44" color="#FFF">
                              <v-img contain :src="item.user_image"></v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="one-lines">{{ item.buyer_name }}</span>
                            <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #1B5DD6">
                                {{ Number(item.sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด -->
          </div>
        </v-col>
      </v-row>
    </v-card>

    <v-dialog v-model="selectDateFilter" width="300px" content-class="rounded" persistent>
      <v-card class="rounded-xl" height="300px" align="center">
        <v-toolbar align="center" color="#FFF" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#333">ตัวกรอง</font>
          </span>
          <v-btn icon dark @click="closeSelectFilter('readonly')">
            <v-icon color="#333">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card class="mt-6" style="background-color: #F3F5F7; border-radius: 12px;" elevation="0" width="90%" height="46px">
          <div v-if="selectedFilterDates === 'รายปี'">
            <v-row>
              <v-col cols="4" class="pr-0 pt-1" align="center">
                <v-card style="border-radius: 12px;" @click="selectedFilterDate('รายปี')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #27AB9C">รายปี</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายเดือน')" width="90%" height="38px" elevation="0">
                  <span class="pt-2" style="font-size: 16px; color: #CCCCCC">รายเดือน</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายวัน')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายวัน</span>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <div v-if="selectedFilterDates === 'รายเดือน'">
            <v-row>
              <v-col cols="4" class="pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายปี')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายปี</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pr-0 pt-1" align="center">
                <v-card style="border-radius: 12px;" @click="selectedFilterDate('รายเดือน')" width="90%" height="38px" elevation="0">
                  <span class="pt-2" style="font-size: 16px; color: #27AB9C">รายเดือน</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายวัน')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายวัน</span>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <div v-if="selectedFilterDates === 'รายวัน'">
            <v-row>
              <v-col cols="4" class="pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายปี')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายปี</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายเดือน')" width="90%" height="38px" elevation="0">
                  <span class="pt-2" style="font-size: 16px; color: #CCCCCC">รายเดือน</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pt-1" align="center">
                <v-card style="border-radius: 12px;" @click="selectedFilterDate('รายวัน')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #27AB9C">รายวัน</span>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <v-row>
            <v-col>
              <!-- รายปี -->
              <div v-if="selectedFilterDates === 'รายปี'">
                <v-row style="height: 120px;">
                  <v-col cols="2" class="d-flex align-start mt-2">
                    <span style="font-size: 16px;">ปี: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-select
                      v-model="selectedYear"
                      :items="years"
                      placeholder="เลือกปี"
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      class="d-inline-block custom-text-field"
                      height="22px"
                      @change="checkSelected()"
                      dense
                      outlined
                      :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
                    >
                    <template slot="selection">
                      {{ selectedYear + 543 }}
                    </template>
                    <template slot="item" slot-scope="data">
                      {{ data.item + 543 }}
                    </template>
                      <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-select>
                  </v-col>
                </v-row>
              </div>

              <!-- รายเดือน -->
              <div v-if="selectedFilterDates === 'รายเดือน'">
                <v-row style="height: 60px;">
                  <v-col cols="2" class="d-flex align-start mt-2">
                    <span style="font-size: 16px;">ปี: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-select
                      v-model="selectedYear"
                      :items="years"
                      placeholder="เลือกปี"
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      class="d-inline-block custom-text-field"
                      height="22px"
                      @change="checkSelected()"
                      dense
                      outlined
                      :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
                    >
                    <template slot="selection">
                      {{ selectedYear + 543 }}
                    </template>
                    <template slot="item" slot-scope="data">
                      {{ data.item + 543 }}
                    </template>
                      <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-select>
                  </v-col>
                </v-row>
                <v-row style="height: 60px;">
                  <v-col cols="2" class="d-flex align-start mt-2">
                    <span style="font-size: 16px;">เดือน: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-select
                      v-model="selectedMonth"
                      :items="months"
                      placeholder="เลือกเดือน"
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      class="d-inline-block custom-text-field"
                      height="22px"
                      @change="checkSelected()"
                      dense
                      outlined
                      :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
                    >
                      <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-select>
                  </v-col>
                </v-row>
              </div>

              <!-- รายวัน -->
              <div v-if="selectedFilterDates === 'รายวัน'">
                <v-row style="height: 120px;">
                  <v-col cols="2" class="pr-0 mt-2">
                    <span style="font-size: 16px;">วันที่: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-dialog ref="modalDateSelect" v-model="showDatePicker" class="d-inline-block" persistent :return-value.sync="date" width="480px">
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          v-model="dateRangeText"
                          placeholder="วว/ดด/ปปปป"
                          dense
                          rounded
                          readonly
                          style="border: 1px solid #EBEBEB; border-radius: 8px;"
                          v-bind="attrs"
                          v-on="on"
                          class="d-inline-block custom-text-field my-input"
                        >
                          <v-spacer></v-spacer>
                          <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        style="font-size:29px !important; height: 420px !important"
                        v-model="dates"
                        scrollable
                        reactive
                        locale="Th-th"
                        range
                        no-title
                        @change="checkSelected()"
                        full-width
                        :min="minDate"
                        :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
                      >
                        <v-row>
                          <v-col align="end">
                            <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                            <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                          </v-col>
                        </v-row>
                      </v-date-picker>
                    </v-dialog>
                  </v-col>
                </v-row>
              </div>

            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-btn text @click="handleOptionChange()" elevation="0"
              :disabled="disabledSelected === true ? true : false"
              >
               <span :style="disabledSelected ? 'font-size: 16px; color: #CCCCCC' : 'font-size: 16px; color: #27AB9C'" >ล้างค่า</span>
                 <!-- <span :style="{ 'font-size': '16px', 'color': disabledSelected ? '#27AB9C' : '#333' }" >ล้างค่า</span> -->
              </v-btn>
            </v-col>
            <v-col>
              <v-btn @click="confirmSelect()" style="border-radius: 40px; background: #27AB9C; width: 125px"
              :disabled="disabledSelected === true ? true : false"
              >
                <span style="font-size: 16px; color: #FFFFFF">ยืนยัน</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialog_detail"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
      >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">รายการสินค้า</font>
          </span>
          <v-btn icon dark @click="CloseDialog('readonly')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pt-3" style="text-align: center;">
                <span style="color:#333333; font-size: 16px; font-weight: 600;">รายการสินค้าทั้งหมด {{ Number( order_Detail.length ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น</span>
              </v-col>
            </v-row>
        </v-card-text>
        <v-card-text v-bind:style="{'height' : '400px'}">
          <v-container>
            <v-row v-for="(item, index) in order_Detail" :key="index">
              <v-col cols="12">
                <v-card class="mt-4">
                  <v-row>
                    <v-col cols="12" md="4" align="center">
                      <v-avatar tile size="140">
                        <div v-if="item.product_image !== ''">
                          <img :src="item.product_image" alt="Product Image" class="avatar-image" />
                        </div>
                        <div v-else>
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </div>
                      </v-avatar>
                    </v-col>
                    <v-col cols="12" md="8">
                      <v-card-title class="no-word-break pb-1">{{ item.product_name }}</v-card-title>
                      <v-card-text>
                        <div><b>Product ID:</b> {{ item.product_id }}</div>
                        <div><b>Main SKU:</b> {{ item.main_sku }}</div>
                        <div><b>Price:</b> {{ Number( item.revenue_default ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</div>
                        <div><b>รูปแบบการชำระเงิน:</b> {{ typePayment }}</div>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import { Decode } from '@/services'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      chartSeries: [],
      selectedItem: null,
      selectedItemShop: -2,
      selectedFilterDates: 'รายปี',
      disabledSelected: true,
      itemsShop: [],
      itemsHeader: [
        'ข้อมูลรายได้',
        'รายการสั่งซื้อสินค้าทั้งหมด',
        'TOP 10 เซอร์วิสขายดี',
        'TOP 10 มูลค่าการสั่งซื้อ',
        'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด'
        // 'TOP 10 ฝ่ายขายที่ยอดขายสะสมเยอะที่สุด'
      ],
      datesFilter: [
        'รายปี',
        'รายเดือน',
        'รายวัน'
      ],
      selectDateFilter: false,
      showBestSeller: false,
      showOrderValue: false,
      showTopBuyers: false,
      monthSelected: '',
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31', // Set your maximum date here
      dialog_detail: false,
      order_Detail: [],
      saleOrder: [],
      keyField: 'i',
      bestSeller: [],
      orderValue: [],
      topBuyers: [],
      xAxis: '',
      // exportFile: [],
      UrlExponential: '',
      // exportDashboard: '',
      startDate: new Date().getFullYear(),
      endDate: new Date().getFullYear(),
      shopID: '-2',
      dateFilter: 'year',
      modalDateSelect: false,
      dates: [],
      picker: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      options: ['รายปี', 'รายเดือน', 'รายวัน'],
      selectedOption: 'รายปี',
      years: [2022, 2023, 2024, 2025],
      showYearDropdown: null,
      selectedDropdown: 'รายปี',
      // showMonthDropdown: false,
      selectedYear: new Date().getFullYear(),
      showDatePicker: false,
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      selectedMonth: null,
      selectedMonthValue: null,
      menu: false,
      selectedDates: [],
      availableYears: [],
      totalSale: '',
      totalOrder: '0',
      totalFee: '',
      totalNetAmount: '',
      typePayment: '',
      moneyIconPath: require('@/assets/icons/SellerDashboard/money (1) 1.png'),
      boxIconPath: require('@/assets/icons/SellerDashboard/box 1.png'),
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      dataModelIconPath: require('@/assets/icons/SellerDashboard/data-model 1.png'),
      uniqueIconPath: require('@/assets/icons/SellerDashboard/unique 1.png'),
      commissionIconPath: require('@/assets/icons/SellerDashboard/commission 1.png'),
      bestCustomerIconPath: require('@/assets/icons/SellerDashboard/best-customer-experience 1.png'),
      rankingIconPath: require('@/assets/icons/SellerDashboard/ranking (1) 1.png'),
      graphLineIconPath: require('@/assets/icons/SellerDashboard/graph-line 1.png'),
      goldMedalIconPath: require('@/assets/icons/SellerDashboard/gold-medal.png'),
      silverMedalIconPath: require('@/assets/icons/SellerDashboard/silver-medal.png'),
      bronzeMedalIconPath: require('@/assets/icons/SellerDashboard/bronze-medal.png'),
      Icon1: require('@/assets/WDShop1.png'),
      Icon2: require('@/assets/WDShop2.png'),
      Icon3: require('@/assets/WDShop3.png'),
      Icon4: require('@/assets/WDShop4.png'),
      Icon5: require('@/assets/WDShop5.png'),
      Icon6: require('@/assets/icon6.png'),
      headers: [
        { text: 'รหัสผู้ซื้อ', align: 'start', sortable: false, value: 'id' },
        { text: 'รายชื่อลูกค้า', align: 'start', sortable: false, value: 'buyer_name' },
        { text: 'วันที่ทำรายการ', sortable: false, value: 'start_date_contract' },
        { text: 'เลขที่ทำรายการสั่งซื้อ', sortable: false, value: 'order_number' },
        { text: 'ราคา (บาท)', sortable: false, value: 'total_price_no_vat' },
        { text: 'รายการสินค้า', sortable: false, align: 'center', value: 'product_list' }
      ],
      headersModal: [
        { text: 'สินค้า', align: 'start', sortable: false, value: 'product_image' },
        { text: 'รหัสสินค้า', align: 'start', sortable: false, value: 'product_id' },
        { text: 'รายการสินค้า', align: 'start', sortable: false, value: 'product_name' },
        { text: 'SKU', align: 'start', sortable: false, value: 'main_sku' },
        { text: 'ราคา (บาท)', sortable: false, value: 'revenue_default' }
      ],
      chartOptions: {
        chart: {
          id: 'income-difference-chart',
          // stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          colors: ['#008FFB', '#FF4560']
        },
        markers: {
          size: 5, // Adjust the size of markers as per your preference
          colors: '#fff',
          strokeColors: '#AE8FF7',
          strokeWidth: 2,
          strokeOpacity: 0.9,
          strokeDashArray: 0,
          fillOpacity: 0,
          discrete: [],
          shape: 'circle',
          radius: 2,
          offsetX: 0,
          offsetY: 0,
          onClick: undefined,
          onDblClick: undefined,
          showNullDataPoints: true,
          hover: {
            size: undefined,
            sizeOffset: 6
          }
        },
        xaxis: {
          tickPlacement: 'between',
          categories: [0]
          // categories: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      }
    }
  },
  mounted () {
    this.selectedItem = this.itemsHeader[0]
    this.selectedFilterDates = this.datesFilter[0]
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardJVMobile' }).catch(() => { })
      } else {
        window.location.replace(`${'/dashboardJV'}`)
      }
    }
  },
  created () {
    // this.chartSeries[0].data = [0, 0, 0, 0, 0, 0, 178500, 212210, 625136297.93, 626470, 224280, 8800]
    this.$EventBus.$emit('changeNav')
    // Set default values or perform initial actions on component creation
    if (this.selectedDropdown === 'รายปี') {
      this.showYearDropdown = true
    }
    // if (localStorage.getItem('shopSellerID') === '' || localStorage.getItem('shopSellerID') === null || localStorage.getItem('shopSellerID') === undefined) {
    //   this.$router.push({ path: '/' })
    // } else {
    //   this.shopID = localStorage.getItem('shopSellerID')
    //   console.log(this.shopID, 'sshhooppiidd')
    // }
    // this.shopID = this.$route.params.id
    this.getShopData()
    this.getPageData()
    this.getOrderList(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopProducts(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopBuyers(this.startDate, this.endDate, this.shopID, this.dateFilter)
  },
  computed: {
    dateRangeText () {
      if (this.dates.length > 1) {
        var startDays = new Date(this.dates[0]).toLocaleDateString('th-TH')
        var endDays = new Date(this.dates[1]).toLocaleDateString('th-TH')
        var totalDays = startDays + ' - ' + endDays
        return totalDays
      } else if (this.dates.length === 1) {
        var oneDays = new Date(this.dates).toLocaleDateString('th-TH')
        return oneDays
      }
      return this.dates.join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    formatPrice (price) {
      // Format the price based on its magnitude
      if (price >= 1000000) {
        return (price / 1000000).toLocaleString(undefined, { minimumFractionDigits: 2 }) + 'M'
      } else if (price >= 1000) {
        return (price / 1000).toLocaleString(undefined, { minimumFractionDigits: 2 }) + 'K'
      } else {
        return price.toLocaleString(undefined, { minimumFractionDigits: 2 })
      }
    },
    selectedFilterDate (filter) {
      this.selectedFilterDates = filter
      if (filter === 'รายปี') {
        this.dateFilter = 'year'
        this.checkSelected()
        this.handleOptionChange()
      } else if (filter === 'รายเดือน') {
        this.dateFilter = 'month'
        this.checkSelected()
        this.handleOptionChange()
      } else if (filter === 'รายวัน') {
        this.dateFilter = 'day'
        this.checkSelected()
        this.handleOptionChange()
      }
      // console.log(this.dateFilter, 'this.dateFilter')
    },
    checkSelected () {
      if (this.selectedFilterDates === 'รายปี') {
        if (this.selectedYear === null) {
          this.disabledSelected = true
        } else {
          this.disabledSelected = false
        }
      } else if (this.selectedFilterDates === 'รายเดือน') {
        if (this.selectedYear && this.selectedMonth === null) {
          this.disabledSelected = true
        } else {
          this.disabledSelected = false
        }
      } else if (this.selectedFilterDates === 'รายวัน') {
        if (this.dates.length === 0) {
          this.disabledSelected = true
        } else {
          this.disabledSelected = false
        }
      }
    },
    confirmSelect () {
      // this.$store.commit('openLoader')
      if (this.selectedFilterDates === 'รายปี') {
        this.onYearSelected()
      } else if (this.selectedFilterDates === 'รายเดือน') {
        this.onMonthSelected()
      } else if (this.selectedFilterDates === 'รายวัน') {
        this.onDatesSelected()
      }
      this.handleOptionChange()
      // this.$store.commit('closeLoader')
    },
    async saveDates (val) {
      if (this.dates.length === 1) {
        this.startDate = this.dates
        this.endDate = this.dates
        this.showDatePicker = false
      } else {
        this.$refs.modalDateSelect.save(val)
        var Range = await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.dateRange = Range
        this.startDate = this.dates[0]
        this.endDate = this.dates[1]
        this.showDatePicker = false
      }
      this.checkSelected()
    },
    async onDatesSelected () {
      if (this.dates.length === 1) {
        this.startDate = this.dates
        this.endDate = this.dates
        this.getRevenuGraphSummary(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getOrderList(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopProducts(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopBuyers(this.dates, this.dates, this.shopID, this.dateFilter)
        this.selectDateFilter = false
      } else {
        this.dateRange = Range
        this.startDate = this.dates[0]
        this.endDate = this.dates[1]
        this.getRevenuGraphSummary(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getOrderList(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopProducts(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopBuyers(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.selectDateFilter = false
      }
    },
    async handleSelectChange () {
      // เรียกตอนเปลี่ยนร้านค้า
      // console.log('Selected item:', this.selectedItemShop)
      this.shopID = this.selectedItemShop
      this.$store.commit('openLoader')
      await this.getPageData()
      await this.getOrderList(this.startDate, this.endDate, this.shopID, this.dateFilter)
      await this.getTopProducts(this.startDate, this.endDate, this.shopID, this.dateFilter)
      await this.getTopBuyers(this.startDate, this.endDate, this.shopID, this.dateFilter)
      this.$store.commit('closeLoader')
    },
    async getShopData () {
      await this.$store.dispatch('actionListAllShopData')
      var response = await this.$store.state.ModuleDashBoardForAdmin.stateListAllShopData
      if (response.ok === 'y') {
        // console.log('actionListAllShopData', response)
        var statAllShop = [{ name_th: 'ทั้งหมด', id: -2 }]
        this.itemsShop = response.query_result
        this.itemsShop = this.itemsShop.filter(item => item.is_JV === 'yes')
        this.itemsShop = statAllShop.concat(this.itemsShop)
      }
    },
    getPageData () {
      // โหลดครั้งแรกตอนเปิดหน้านี้
      this.getRevenuGraphSummary(this.startDate, this.endDate, this.shopID, this.dateFilter)
    },
    openSelectFilter () {
      this.selectDateFilter = true
    },
    closeSelectFilter () {
      this.selectDateFilter = false
      this.handleOptionChange()
    },
    openDialog (item) {
      this.typePayment = ''
      this.dialog_detail = true
      this.order_Detail = item.product_list
      this.typePayment = item.type_payment
    },
    CloseDialog (val) {
      this.type = val
      this.dialog_detail = false
    },
    ChangeChartOptions (val) {
      this.chartOptions = {
        chart: {
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          markers: {
            size: 4, // Adjust the size of markers as per your preference
            colors: undefined,
            strokeColors: '#fff',
            strokeWidth: 2,
            strokeOpacity: 0.9,
            strokeDashArray: 0,
            fillOpacity: 1,
            discrete: [],
            shape: 'circle',
            radius: 2,
            offsetX: 0,
            offsetY: 0,
            onClick: undefined,
            onDblClick: undefined,
            showNullDataPoints: true,
            hover: {
              // size: undefined,
              sizeOffset: 6
            }
          }
        },
        xaxis: {
          categories: this.selectedFilterDates === 'รายเดือน' ? val : this.selectedFilterDates === 'รายวัน' ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      }
    },
    async getRevenuGraphSummary (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      // console.log(data, 'RGS')
      await this.$store.dispatch('actionRevenuGraphSummary', data)
      var response = await this.$store.state.ModuleDashBoard.stateRevenuGraphSummary
      if (response.ok === 'y') {
        // console.log('actionRevenuGraphSummary', response)
        var dataTable = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dataTable.push(response.query_result.revenugrap[i].revenu)
        }
        var dayInMonth = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dayInMonth.push(new Date(response.query_result.revenugrap[i].date).toLocaleDateString('th-TH', { month: 'long', day: 'numeric' }))
        }
        var dateToDate = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dateToDate.push(new Date(response.query_result.revenugrap[i].date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        }
        // console.log(dayInMonth, 'dayInMonth')
        if (response.ok === 'y') {
          this.chartSeries = await [{ name: 'รายได้', data: dataTable }]
          if (this.selectedFilterDates === 'รายเดือน') {
            this.ChangeChartOptions(dayInMonth)
          } else if (this.selectedFilterDates === 'รายวัน') {
            this.ChangeChartOptions(dateToDate)
          } else {
            this.ChangeChartOptions(false)
          }
          if (response.query_result.sumaryDocument === '' || response.query_result.sumaryDocument === null || response.query_result.sumaryDocument === undefined) {
            // ในกรณีที่ ค่าจำนวนรายการสั่งซื้อทั้งหมด = ค่าว่าง, null, undefined
            this.totalOrder = 0 // จำนวนรายการสั่งซื้อทั้งหมด
          } else {
            this.totalOrder = response.query_result.sumaryDocument // จำนวนรายการสั่งซื้อทั้งหมด
          }
          if (response.query_result.sumaryRevenu === '' || response.query_result.sumaryRevenu === null || response.query_result.sumaryRevenu === undefined) {
            // ในกรณีที่ ค่ายอดขายทั้งหมด = ค่าว่าง, null, undefined
            this.totalsale = 0 // ยอดขายทั้งหมด
          } else {
            this.totalSale = response.query_result.sumaryRevenu // ยอดขายทั้งหมด
          }
          if (response.query_result.fee === '' || response.query_result.fee === null || response.query_result.fee === undefined) {
            // ในกรณีที่ ค่าธรรมเนียม = ค่าว่าง, null, undefined
            this.totalFee = 0 // ค่าธรรมเนียม
          } else {
            this.totalFee = response.query_result.fee // ค่าธรรมเนียม
          }
          if (response.query_result.net_amount === '' || response.query_result.net_amount === null || response.query_result.net_amount === undefined) {
            // ในกรณีที่ ยอดเงินสุทธิ = ค่าว่าง, null, undefined
            this.totalNetAmount = 0 // ยอดเงินสุทธิ
          } else {
            this.totalNetAmount = response.query_result.net_amount // ยอดเงินสุทธิ
          }
        } else {
          // if response.message === 'n'
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: response.message
          })
        }
      }
    },
    async getOrderList (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionOrderList', data)
      var response = await this.$store.state.ModuleDashBoard.stateOrderList
      if (response.ok === 'y') {
        // console.log('actionOrderList', response.query_result.orderList)
        this.saleOrder = response.query_result.orderList
        var count = 1
        this.saleOrder.forEach((item) => {
          item.i = count
          count = count + 1
        })
        // this.saleOrder = datasale
        // console.log(this.saleOrder, 'tong')
      }
    },
    async getTopProducts (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionTopProducts', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopProducts
      if (response.ok === 'y') {
        // console.log('actionTopProducts', response)
        this.bestSeller = response.query_result.countProduct
        this.orderValue = response.query_result.sumRevenuProduct
      }
    },
    async getTopBuyers (startDate, endDate, shopID, dateFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionTopBuyers', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopBuyers
      if (response.ok === 'y') {
        // console.log('actionTopBuyers', response)
        this.topBuyers = response.query_result
      }
    },
    async getExportDashboard (startDate, endDate, shopID, dateFilter, exportDashboard, fileName) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        exportdashboard: exportDashboard,
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/exportdashboard`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', fileName + '.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    closeDateSelect ($refs) {
      this.showDatePicker = false
      this.modalDateSelect = false
      this.dates = []
      this.handleOptionChange()
    },
    handleOptionChange () {
      // Reset selections when the option changes
      this.selectedYear = null
      this.selectedMonth = null
      this.selectedMonthValue = null
      // this.selectedDates = []
      this.dates = []
      this.disabledSelected = true
    },
    onMonthSelected () {
      this.selectedMonthValue = `${this.selectedYear}-${this.selectedMonth}`
      this.startDate = this.selectedMonthValue
      this.endDate = this.selectedMonthValue
      // console.log(this.selectedMonthValue, 'selecteddd')
      this.getRevenuGraphSummary(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getOrderList(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopProducts(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopBuyers(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.selectDateFilter = false
    },
    onYearSelected () {
      this.startDate = this.selectedYear
      this.endDate = this.selectedYear
      // console.log(this.selectedYear, 'selecteddd')
      this.getRevenuGraphSummary(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
      this.getOrderList(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
      this.getTopProducts(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
      this.getTopBuyers(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
      this.selectDateFilter = false
    }
  }
}
</script>

<style scoped>
.active-btn-selected {
  background-color: white !important;
  color: #27ab9c;
  border-radius: 20px;
  border: none;
  box-shadow: none;
  margin: 5px 10px;
}
.active-btn-default {
  background-color: #f3f5f7 !important;
  color: #8dd4cc;
  border-radius: 20px;
  border: none;
  box-shadow: none;
}
.btn-box {
  background-color: #f3f5f7;
  border-radius: 30px;
  width: 100%;
  padding: 5px;
}
.v-data-table /deep/ .v-data-footer {
  display: flex;
  flex-wrap: inherit !important;
  justify-content: flex-end;
  align-items: center;
  font-size: 12px;
  padding: 0 8px;

}
.v-select /deep/ .v-select__selections {
    align-items: center;
    display: flex;
    flex: 1 1;
    flex-wrap: wrap;
    line-height:22px !important;
    max-width: 100%;
    min-width: 0;
}
::v-deep .v-dialog {
  box-shadow: none !important;
}
.chart-container {
  width: 100%;
  overflow-x: auto; /* Enable horizontal scrolling */
  overflow-y: hidden;
}
.v-card__actions {
    align-items: center;
    display: revert;
    padding: 8px;
}
.v-card--link {
    cursor: pointer;
    padding-top: 8px;
}
.apple-keyboard-control {
  size: 20px;
}
.two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
}
.one-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.custom-text-field {
  height: 37px;
  width: 280px;
}
.custom-chip {
  overflow: visible !important;
}
.no-word-break {
  font-size: 16px;
  word-break: normal;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.backgroundSellerDashboard {
  max-width: 100% !important;
  background: #F7FCFC;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C;
}
.exportButtonText {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF
}
.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
</style>

<style scoped>
.v-btn__content {
    font-size: 16px;
}
.v-text-field--rounded > .v-input__control > .v-input__slot {
    padding: 0 10px;
}
.my-input.v-input .v-input__slot {
  padding: 0 10px;
}
</style>
