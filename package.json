{"name": "NexGenCommerce", "version": "0.1.0", "private": true, "scripts": {"serve": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "build": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "lint": "vue-cli-service lint"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "@ckeditor/ckeditor5-build-classic": "^19.0.2", "@ckeditor/ckeditor5-build-decoupled-document": "^19.0.2", "@ckeditor/ckeditor5-indent": "^19.0.1", "@ckeditor/ckeditor5-ui": "^19.0.1", "@ckeditor/ckeditor5-vue": "^1.0.1", "@dongido/vue-viaudio": "^0.3.5", "@gtm-support/vue2-gtm": "^1.3.0", "@line/liff": "^2.25.0", "@livelybone/vue-select": "^2.7.0", "@mdi/font": "^7.4.47", "@riophae/vue-treeselect": "^0.4.0", "ant-design-vue": "^1.7.8", "apexcharts": "^3.54.1", "async": "^3.2.6", "axios": "^0.21.4", "bootstrap": "^4.6.2", "crypto-js": "^4.2.0", "datatables.net-buttons": "^2.4.3", "datatables.net-buttons-dt": "^2.4.3", "datatables.net-dt": "^1.13.11", "datatables.net-editor": "^2.3.1", "datatables.net-responsive-dt": "^2.5.1", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "get-youtube-id": "^1.0.1", "html-truncate": "^1.2.2", "jquery": "^3.7.1", "jszip": "^3.10.1", "livekit-client": "^2.9.4", "lodash": "^4.17.21", "moment-timezone": "^0.5.46", "pdfmake": "^0.2.15", "qrcode": "^1.5.4", "socket.io-client": "^4.8.1", "thai-baht-text": "^1.0.8", "thai-data": "^1.0.9", "v-mask": "^2.3.0", "vue": "^2.7.16", "vue-affix": "^0.5.2", "vue-apexcharts": "^1.6.2", "vue-axios": "^3.5.2", "vue-blu": "^0.1.9", "vue-class-component": "^7.2.6", "vue-clipboard2": "^0.3.3", "vue-cookies": "^1.8.4", "vue-cool-select": "^3.5.2", "vue-date-dropdown": "^1.0.5", "vue-dropdown-datepicker": "^1.3.1", "vue-horizontal-list": "^1.3.0", "vue-i18n": "^8.28.2", "vue-jsonld": "^1.0.1", "vue-meta": "^1.6.0", "vue-moment": "^4.1.0", "vue-pdf": "^4.3.0", "vue-pdf-embed": "^1.2.1", "vue-photo-zoom-pro": "^2.5.0", "vue-property-decorator": "^9.1.2", "vue-router": "^3.6.5", "vue-signature": "^2.6.0", "vue-slick-carousel": "^1.0.6", "vue-snip": "^1.3.0", "vue-social-sharing": "^3.0.9", "vue-socials": "^1.1.0", "vue-sweetalert2": "^4.3.1", "vue-thailand-address": "^3.4.2", "vue-tree-chart": "^1.2.9", "vue-xlsx": "^0.2.1", "vue-youtube": "^1.4.0", "vuedraggable": "^2.24.3", "vuejs-date-dropdown": "^1.0.4", "vuetify": "^2.7.2", "vuex": "^3.6.2", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "youtube-vue": "^2.0.13"}, "devDependencies": {"@fortawesome/fontawesome-free": "^5.15.4", "@nuxtjs/vuetify": "^1.12.3", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-pwa": "^4.5.19", "@vue/cli-plugin-router": "^4.5.19", "@vue/cli-plugin-unit-jest": "^4.5.19", "@vue/cli-plugin-vuex": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-standard": "^5.1.2", "@vue/preload-webpack-plugin": "^2.0.0", "@vue/test-utils": "^1.3.6", "compression-webpack-plugin": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "^4.5.2", "lint-staged": "^9.5.0", "node-sass": "^5.0.0", "sass": "^1.32.0", "sass-loader": "^10.0.0", "style-loader": "^2.0.0", "vue-cli-plugin-vuetify": "^2.1.1", "vue-template-compiler": "^2.7.16", "vuetify-loader": "^1.9.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "jest": {"preset": "@vue/cli-plugin-unit-jest/presets/no-babel"}}