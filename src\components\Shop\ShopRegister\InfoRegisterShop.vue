<template>
  <v-app class="backgroundPage" id="app">
    <AppBarRegis :step="checkNextStep === false ? 2 : 3" />
    <v-main :class="MobileSize ? 'ma-3' : IpadProSize ? 'ma-8' : 'mx-16 mt-5'">
      <v-card :class="MobileSize ? 'pa-2' : 'pa-5 mb-5'">
        <v-form ref="form" v-model="formRegis" lazy-validation>
          <span style="font-size: large;"><b>ข้อมูลบัญชีนิติบุคคล</b></span>
          <!-- <v-row :class="MobileSize ? 'px-3' : 'px-8'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">ประเภทการลงทะเบียน</span>
              <div class="d-flex flex-wrap">
                <v-radio-group
                  v-model="radiosTypeShop"
                  row
                  class="d-flex"
                  v-for="(item, index) in itemRadioTypeShop" :key="index"
                  :disabled="checkNextStep"
                >
                  <v-radio
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
            </v-col>
          </v-row> -->
          <v-row :class="MobileSize ? 'px-3' : 'px-8'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">ประเภทธุรกิจ</span>
              <div class="d-flex flex-wrap" v-if="!MobileSize">
                <v-radio-group
                  v-model="radiosTypeBiz"
                  row
                  class="d-flex"
                  :disabled="checkNextStep"
                >
                  <v-radio
                    v-for="(item, index) in filteredItemRadioTypeBusiness"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
              <div v-else>
                <v-select
                  :rules="Rules.empty"
                  v-model="radiosTypeBiz"
                  :items="itemRadioTypeBusiness"
                  item-text="label"
                  item-value="value"
                  placeholder="ระบุประเภทนิติบุคคล"
                  outlined
                  dense
                  style="border-radius: 8px;"
                  :disabled="checkNextStep"
                >
                </v-select>
              </div>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-3' : 'px-8'" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
            <v-col cols="12" class="d-flex flex-column">
              <span style="font-size: large;">ข้อมูลธุรกิจ</span>
            </v-col>
            <v-col :cols="(radiosTypeBiz === 'typeBiz3' || radiosTypeBiz === 'typeBiz5') ? (MobileSize ? 12 : 8) : 12" :style="MobileSize ? 'margin-top: -5vw;' : 'margin-top: -1vw;'">
              <span>เลขทะเบียนนิติบุคคล <span style="color: red;">*</span>:</span><br>
              <div class="d-flex">
                 <v-text-field
                  v-model="bizNumber"
                  outlined
                  dense
                  placeholder="ระบุเลขทะเบียนนิติบุคคล"
                  style="border-radius: 8px;"
                  :rules="Rules.bizNumber"
                  :maxLength="13"
                  oninput="this.value = this.value.replace(/[^/0-9]/g, '')"
                  class="mr-1"
                  :disabled="checkNextStep"
                  :class="checkNextStep === true ? 'classCheck' : ''"
                  @change="checkBusiness()"
                >
                </v-text-field>
                <v-btn tile @click="checkBusiness()" outlined style="border: 0; cursor: pointer; color: rgb(72 72 72);" :style="MobileSize ? 'font-size: small' : ''" class="pa-0 ma-0">
                   <v-row dense class="py-2" align="center">
                    <v-col cols="12" class="pa-0">
                      <v-icon left medium style="display: contents; justify-items: center;">
                        mdi-magnify
                      </v-icon>
                    </v-col>
                    <v-col cols="12" class="pa-0" align="center">ค้นหาเลขนิติบุคคล</v-col>
                  </v-row>
                  <!-- <v-icon>mdi-magnify</v-icon>
                  <span style="font-size: small; color: #878787;">ค้นหาเลขนิติบุคคล</span> -->
                </v-btn>
                <!-- <v-card @click="checkBusiness()" outlined style="border: 0; cursor: pointer;" class="d-flex flex-column">
                  <v-icon>mdi-magnify</v-icon>
                  <span style="font-size: small; color: #878787;">ค้นหาข้อมูล DBD</span>
                </v-card> -->
              </div>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" v-if="radiosTypeBiz === 'typeBiz3' || radiosTypeBiz === 'typeBiz5'" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>รหัสทะเบียน <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="accessCode"
                outlined
                dense
                placeholder="ระบุเลขรหัสทะเบียน"
                style="border-radius: 8px;"
                :rules="Rules.accessCode"
                :maxLength="10"
                oninput="this.value = this.value.replace(/[^/0-9]/g, '')"
                class="mr-1"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              >
              </v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6" v-if="radiosTypeBiz === 'typeBiz4' || radiosTypeBiz === 'typeBiz6'" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>หมายเลขบัตรประชาชนของผู้มีอำนาจ <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="ownerIdCard"
                outlined
                dense
                placeholder="ระบุหมายเลขบัตรประชาชนของผู้มีอำนาจ"
                style="border-radius: 8px;"
                :rules="Rules.taxId"
                :maxLength="13"
                oninput="this.value = this.value.replace(/[^/0-9]/g, '')"
                class="mr-1"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
                @input="validateTaxID(ownerIdCard)"
              >
              </v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6" v-if="radiosTypeBiz === 'typeBiz4' || radiosTypeBiz === 'typeBiz6'" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>รหัสทะเบียน <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="accessCode"
                outlined
                dense
                placeholder="ระบุรหัสทะเบียน"
                style="border-radius: 8px;"
                :rules="Rules.accessCode"
                :maxLength="10"
                oninput="this.value = this.value.replace(/[^/0-9]/g, '')"
                class="mr-1"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              >
              </v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 2" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ประเภทนิติบุคคล (ไทย) <span style="color: red;">*</span>:</span><br>
              <v-select
                :rules="Rules.empty"
                v-model="businessTypeTH"
                :items="businessTypeItem"
                item-text="nameTH"
                item-value="value"
                placeholder="ระบุประเภทนิติบุคคล"
                outlined
                dense
                @change="changeTypeTH()"
                style="border-radius: 8px;"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              >
              </v-select>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 5" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อนิติบุคคล (ไทย) <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="bizNameTH"
                outlined
                dense
                :rules="Rules.bizNameTH"
                placeholder="ระบุชื่อนิติบุคคล"
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^ก-๙0-9\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 5" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อสำหรับแสดงบนเอกสาร (ไทย) <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="bizNameDocTH"
                :rules="Rules.bizNameDocTH"
                outlined
                dense
                placeholder="ระบุชื่อสำหรับแสดงบนเอกสาร"
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^ก-๙0-9_.()\,-\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 2" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ประเภทนิติบุคคล (อังกฤษ) <span style="color: red;">*</span>:</span><br>
              <v-select
                v-model="businessTypeEN"
                :items="businessTypeItem"
                item-text="nameEN"
                item-value="value"
                placeholder="ระบุประเภทนิติบุคคล"
                outlined
                dense
                style="border-radius: 8px;"
                :rules="Rules.empty"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
                @change="changeTypeEN()"
              >
              </v-select>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 5" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อนิติบุคคล (อังกฤษ) <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="bizNameEN"
                outlined
                dense
                placeholder="ระบุชื่อนิติบุคคล"
                style="border-radius: 8px;"
                :rules="Rules.bizNameEN"
                oninput="this.value = this.value.replace(/[^a-zA-Z0-9_-\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 5" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อสำหรับแสดงบนเอกสาร (อังกฤษ) <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="bizNameDocEN"
                outlined
                dense
                placeholder="ระบุชื่อสำหรับแสดงบนเอกสาร"
                style="border-radius: 8px;"
                :rules="Rules.bizNameDocEN"
                oninput="this.value = this.value.replace(/[^a-zA-Z0-9_.,()\-\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>เบอร์โทรศัพท์ <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="tel"
                outlined
                dense
                placeholder="ระบุเบอร์โทรศัพท์"
                style="border-radius: 8px;"
                :rules="Rules.tel"
                :maxLength="9"
                oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>เบอร์มือถือ <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="phone"
                outlined
                dense
                placeholder="ระบุเบอร์มือถือ"
                style="border-radius: 8px;"
                :rules="Rules.phone"
                :maxLength="10"
                oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>อีเมล <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="email"
                outlined
                dense
                placeholder="ระบุอีเมล"
                style="border-radius: 8px;"
                :rules="Rules.email"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
                oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-3' : 'px-8'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
            <v-col cols="12" class="d-flex flex-column">
              <span style="font-size: large;">ที่อยู่</span>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -3vw;' : 'margin-top: -1vw;'">
              <span>เลขที่ <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="houseNumber"
                outlined
                dense
                placeholder="ระบุเลขที่"
                style="border-radius: 8px;"
                :rules="Rules.houseNumber"
                oninput="this.value = this.value.replace(/[^/0-9/-]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ห้องเลขที่ :</span><br>
              <v-text-field
                v-model="roomNumber"
                outlined
                dense
                placeholder="ระบุห้องเลขที่"
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ชั้นที่ :</span><br>
              <v-text-field
                v-model="floorNumber"
                outlined
                dense
                placeholder="ระบุชั้น"
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>อาคาร :</span><br>
              <v-text-field
                v-model="building"
                outlined
                dense
                placeholder="ระบุอาคาร"
                style="border-radius: 8px;"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>หมู่บ้าน :</span><br>
              <v-text-field
                v-model="village"
                outlined
                dense
                placeholder="ระบุหมู่บ้าน"
                style="border-radius: 8px;"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>หมู่ที่ :</span><br>
              <v-text-field
                v-model="moo"
                outlined
                dense
                placeholder="ระบุหมู่ที่"
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^ก-๙0-9\s]/g, '')"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ตรอก/ซอย :</span><br>
              <v-text-field
                v-model="soi"
                outlined
                dense
                placeholder="ระบุตรอก/ซอย"
                style="border-radius: 8px;"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>แยก :</span><br>
              <v-text-field
                v-model="yaek"
                outlined
                dense
                placeholder="ระบุแยก"
                style="border-radius: 8px;"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ถนน :</span><br>
              <v-text-field
                v-model="road"
                outlined
                dense
                placeholder="ระบุถนน"
                style="border-radius: 8px;"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? 'margin-top: -6vw;' : 'margin-top: -1vw;'">
              <span>จังหวัด<span style="color: red;"> *</span></span>
              <addressinput-province :disabled="checkNextStep" label="" :class="[checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address', checkNextStep === true ? 'classCheck' : '']" v-model="provinceText" placeholder="ระบุจังหวัด"/>
              <div v-if="checkProvinceError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? '' : 'margin-top: -1vw;'">
              <span>อำเภอ/เขต<span style="color: red;"> *</span></span>
              <addressinput-district :disabled="checkNextStep" label="" v-model="districtText" :class="[checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address', checkNextStep === true ? 'classCheck' : '']" placeholder="ระบุเขต/อำเภอ"/>
              <div v-if="checkDistrictError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? '' : 'margin-top: -1vw;'">
              <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
              <addressinput-subdistrict :disabled="checkNextStep" label="" :class="[checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address', checkNextStep === true ? 'classCheck' : '']" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล" />
              <div v-if="checkSubDistrictError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :class="MobileSize || IpadSize ? '' : 'mt-2'">
              <span>รหัสไปรษณีย์<span style="color: red;"> *</span></span>
              <addressinput-zipcode :disabled="checkNextStep" label="" :class="[checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address', checkNextStep === true ? 'classCheck' : '']" v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์"/>
              <div v-if="checkZipcodeError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-3' : 'px-8'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">รายละเอียดสาขา</span>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>รหัสสาขา <span style="color: red;">*</span>: <span style="font-size: 10px; color: red;">(ตัวอย่างรหัสสาขา 00000)</span></span><br>
              <v-text-field
                v-model="branchNo"
                outlined
                dense
                placeholder="ระบุรหัสสาขา"
                style="border-radius: 8px;"
                :rules="Rules.empty"
                disabled
                :class="checkNextStep === true ? 'classCheck' : ''"
                :maxLength="5"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อสาขา <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="branchName"
                outlined
                dense
                placeholder="ระบุสาขา"
                style="border-radius: 8px;"
                :class="checkNextStep === true ? 'classCheck' : ''"
                :rules="Rules.empty"
                disabled
              ></v-text-field>
            </v-col>
          </v-row>
          <!-- <v-row :class="MobileSize ? 'px-3' : 'px-8'" :style="MobileSize || IpadSize ? 'margin-top: -6vw;' : 'margin-top: -1.5vw;'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">รายละเอียดเจ้าของนิติบุคคล</span>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>อีเมล <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="emailOwner"
                outlined
                dense
                placeholder="ระบุอีเมล"
                style="border-radius: 8px;"
                :rules="Rules.email"
                :disabled="checkNextStep"
                :class="checkNextStep === true ? 'classCheck' : ''"
                oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>แผนก <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="department"
                outlined
                dense
                placeholder="ระบุแผนก"
                style="border-radius: 8px;"
                disabled
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ตำแหน่ง <span style="color: red;">*</span>:</span><br>
              <v-text-field
                v-model="position"
                outlined
                dense
                placeholder="ระบุตำแหน่ง"
                style="border-radius: 8px;"
                disabled
                :class="checkNextStep === true ? 'classCheck' : ''"
              ></v-text-field>
            </v-col>
          </v-row> -->
          <v-row :class="MobileSize ? 'px-3' : 'px-8'" :style="MobileSize || IpadSize ? 'margin-top: -6vw;' : 'margin-top: -1.5vw;'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">ใบเสร็จรับรองอิเล็กทรอนิกส์</span>
              <div class="d-flex flex-wrap">
                <v-radio-group
                  v-model="radiosEtax"
                  row
                  class="d-flex"
                  v-for="(item, index) in itemRadioEtax" :key="index"
                  :disabled="checkNextStep"
                >
                  <v-radio
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
            </v-col>
            <v-col cols="12" class="d-flex flex-column">
              <span style="font-size: large;">ผู้ประสานงาน</span>
              <div class="d-flex">
                <v-radio-group
                  v-model="radiosCoordinator"
                  row
                  class="d-flex"
                  v-for="(item, index) in itemRadioCoordinator" :key="index"
                  :disabled="checkNextStep"
                >
                  <v-radio
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
            </v-col>
            <v-col cols="12" :style="MobileSize ? '' : 'margin-top: -1vw;'">
              <div v-if="radiosCoordinator === 'typeCoor1'" class="d-flex flex-wrap">
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>ชื่อ (ไทย) <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="firstNameTH"
                    outlined
                    dense
                    placeholder="ระบุชื่อ"
                    style="border-radius: 8px;"
                    :rules="Rules.firstNameTH"
                    oninput="this.value = this.value.replace(/[^ก-๙]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>นามสกุล (ไทย) <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="lastNameTH"
                    outlined
                    dense
                    placeholder="ระบุนามสกุล"
                    style="border-radius: 8px;"
                    :rules="Rules.lastNameTH"
                    oninput="this.value = this.value.replace(/[^ก-๙]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>เลขประจำตัวประชาชน <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="taxId"
                    outlined
                    dense
                    placeholder="ระบุเลขประจำตัวประชาชน"
                    style="border-radius: 8px;"
                    :rules="Rules.taxId"
                    :maxLength="13"
                    oninput="this.value = this.value.replace(/[^/0-9]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                    @input="validateTaxID(taxId)"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>เบอร์โทรศัพท์มือถือ <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="telNo"
                    outlined
                    dense
                    placeholder="ระบุเบอร์โทรศัพท์มือถือ"
                    style="border-radius: 8px;"
                    :rules="Rules.telNo"
                    :maxLength="10"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>อีเมล (ใช้ในการกดรับใบรับรอง) <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="email_eTax"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    :rules="Rules.email_eTax"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                  ></v-text-field>
                </v-col>
              </div>
              <div v-if="radiosCoordinator === 'typeCoor2'" class="d-flex flex-wrap">
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>ชื่อผู้รับมอบอำนาจ (ไทย) <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="firstNameTH"
                    outlined
                    dense
                    placeholder="ระบุชื่อผู้รับมอบอำนาจ"
                    style="border-radius: 8px;"
                    :rules="Rules.firstNameTH"
                    oninput="this.value = this.value.replace(/[^ก-๙]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>นามสกุลผู้รับมอบอำนาจ (ไทย) <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="lastNameTH"
                    outlined
                    dense
                    placeholder="ระบุนามสกุลผู้รับมอบอำนาจ"
                    style="border-radius: 8px;"
                    :rules="Rules.lastNameTH"
                    oninput="this.value = this.value.replace(/[^ก-๙]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span style="white-space: nowrap;">เลขประจำตัวประชาชนผู้รับมอบอำนาจ <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="taxId"
                    outlined
                    dense
                    placeholder="ระบุเลขประจำตัวประชาชนผู้รับมอบอำนาจ"
                    style="border-radius: 8px;"
                    :maxLength="13"
                    :rules="Rules.taxId"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                    @input="validateTaxID(taxId)"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>เบอร์โทรศัพท์มือถือ <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="telNo"
                    outlined
                    dense
                    placeholder="ระบุเบอร์โทรศัพท์มือถือ"
                    style="border-radius: 8px;"
                    :rules="Rules.telNo"
                    :maxLength="10"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">อีเมลผู้ประสานงานดูแลใบรับรอง (ใช้ในการกดรับใบรับรอง) <span style="color: red;">*</span>:</span><br>
                  <v-text-field
                    v-model="email_eTax"
                    outlined
                    dense
                    placeholder="ระบุอีเมลผู้ประสาน"
                    style="border-radius: 8px;"
                    :rules="Rules.email_eTax"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                  ></v-text-field>
                </v-col>
              </div>
            </v-col>
            <v-col cols="12" class="d-flex flex-column" :style="MobileSize || IpadSize ? 'margin-top: -6vw;' : 'margin-top: -2vw;'">
              <span style="font-size: large;">แนบเอกสาร</span>
            </v-col>
            <!-- เลือกนิติบุคคล -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz1'" :style="MobileSize ? '' : IpadSize ? '' : 'margin-top: -1vw;'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption1'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 5" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาหนังสือรับรองบริษัท อายุไม่เกิน 90 วัน (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    outlined
                    @change="UploadFile(certificateCompany, 'certificateCompanyPath')"
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    id="file_input"
                    label="เอกสารสำเนาหนังสือรับรองบริษัท"
                    dense
                    v-model="certificateCompany"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 5" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>ภพ.20 ของบริษัท (PDF) <span v-if="radiosEtax === 'typeEtax1'" style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารภพ.20 ของบริษัท"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="vatCompany"
                    :rules="radiosEtax === 'typeEtax1' ? Rules.emptyFile : []"
                    :validate-on-blur="true"
                    @change="UploadFile(vatCompany, 'vatCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 5" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <!-- <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 5" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col> -->
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption2'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาหนังสือรับรองบริษัท อายุไม่เกิน 90 วัน (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาหนังสือรับรองบริษัท"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="certificateCompany"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(certificateCompany, 'certificateCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>ภพ.20 ของบริษัท (PDF) <span v-if="radiosEtax === 'typeEtax1'" style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารภพ.20 ของบริษัท"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="vatCompany"
                    :rules="radiosEtax === 'typeEtax1' ? Rules.emptyFile : []"
                    :validate-on-blur="true"
                    @change="UploadFile(vatCompany, 'vatCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>หนังสือมอบอำนาจ พร้อมปิดอากรแสตมป์ 30 บาท (PDF) <span v-if="this.radiosTypeShop === 'typeShop1'" style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารหนังสือมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="this.radiosTypeShop === 'typeShop1' ? Rules.emptyFile : []"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาบัตรประชาชนผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้รับมอบอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้รับมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="proxyIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(proxyIdCardCopy, 'proxyIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
              </div>
            </v-col>
            <!-- เลือกทะเบียนพาณิชย์ -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz2'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption3'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาใบทะเบียนพาณิชย์ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาใบทะเบียนพาณิชย์"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="bizRegCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(bizRegCopy, 'bizRegPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาบัตรประชาชน พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนา บอ.01 <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเลขที่ผู้เสียภาษีอากร (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเลขที่ผู้เสียภาษีอากร"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="taxIDCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(taxIDCopy, 'taxIDPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <!-- <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col> -->
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption4'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาใบทะเบียนพาณิชย์ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาใบทะเบียนพาณิชย์"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="bizRegCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(bizRegCopy, 'bizRegPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเลขที่ผู้เสียภาษีอากร (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนาเลขที่ผู้เสียภาษีอากร"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="taxIDCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(taxIDCopy, 'taxIDPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ.01 <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้รับมอบอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนของผู้รับมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="proxyIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(proxyIdCardCopy, 'proxyIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
              </div>
            </v-col>
            <!-- เลือกวิสาหกิจชุมชน -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz3'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption5'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาใบวิสาหกิจชุมชน (PDF) <span style="color: red;">*</span></span><br>
                  <br v-if="!MobileSize && !IpadProSize && !IpadSize && radiosEtax === 'typeEtax2'">
                  <v-file-input
                    label="เอกสารสำเนาใบวิสาหกิจชุมชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseCopy, 'enterprisePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนกรรมการผู้มีอำนาจ พร้อมสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <br v-if="!MobileSize && !IpadProSize && !IpadSize && radiosEtax === 'typeEtax2'">
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.1"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเลขที่ผู้เสียภาษีอากร (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเลขที่ผู้เสียภาษีอากร"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="taxIDCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(taxIDCopy, 'taxIDPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร สวช.01 (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสาร สวช.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseRegisCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseRegisCopy, 'enterpriseRegisPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร หนังสือให้ความยินยอม (PDF) </span><br>
                  <v-file-input
                    label="เอกสารหนังสือให้ความยินยอม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="consentCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(consentCopy, 'consentPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>ใบรับรองหน้าระบบสารสนเทศวิสาหกิจชุมชน (PDF) <span style="color: red;">*</span></span><br>
                  <a href="https://smce.doae.go.th/search" target="_blank" style="color: red; font-size: small;">
                    ค้นหาได้จาก : https://smce.doae.go.th/search
                  </a><br>
                  <!-- <span style="color: red; font-size: small;">ค้นหาได้จาก : https://smce.doae.go.th/search </span><br> -->
                  <v-file-input
                    label="เอกสารใบรับรองหน้าระบบสารสนเทศ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="SMCECopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(SMCECopy, 'SMCEPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <!-- <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <br v-if="!MobileSize && !IpadProSize && !IpadSize && radiosEtax === 'typeEtax1'">
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col> -->
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption6'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาใบวิสาหกิจชุมชน (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาใบวิสาหกิจชุมชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseCopy, 'enterprisePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร หนังสือให้ความยินยอม (PDF) </span><br>
                  <v-file-input
                    label="เอกสารหนังสือให้ความยินยอม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="consentCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(consentCopy, 'consentPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเลขที่ผู้เสียภาษีอากร (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเลขที่ผู้เสียภาษีอากร"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="taxIDCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(taxIDCopy, 'taxIDPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร สวช.01 (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสาร สวช.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseRegisCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseRegisCopy, 'enterpriseRegisPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize || IpadProSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF)</span><br>
                  <br v-if="!MobileSize && !IpadProSize && !IpadSize && radiosEtax === 'typeEtax1'">
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>ใบรับรองหน้าระบบสารสนเทศวิสาหกิจชุมชน (PDF) <span style="color: red;">*</span></span><br>
                  <a href="https://smce.doae.go.th/search" target="_blank" style="color: red; font-size: small;">
                    ค้นหาได้จาก : https://smce.doae.go.th/search
                  </a><br>
                  <!-- <span style="color: red; font-size: small;">ค้นหาได้จาก : https://smce.doae.go.th/search </span><br> -->
                  <v-file-input
                    label="เอกสารใบรับรองหน้าระบบสารสนเทศ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="SMCECopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(SMCECopy, 'SMCEPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize || IpadProSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้รับมอบอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <br v-if="!MobileSize && !IpadProSize && !IpadSize">
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้รับมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="proxyIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(proxyIdCardCopy, 'proxyIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
              </div>
            </v-col>
            <!-- เลือกวิสาหกิจเพื่อสังคม -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz4'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption7'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาใบวิสาหกิจเพื่อสังคม (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาใบวิสาหกิจเพื่อสังคม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="comunityCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(comunityCopy, 'comunityPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนกรรมการผู้มีอำนาจ พร้อมสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.1"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเลขที่ผู้เสียภาษีอากร (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเลขที่ผู้เสียภาษีอากร"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="taxIDCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(taxIDCopy, 'taxIDPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร สวช.01 (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสาร สวช.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseRegisCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseRegisCopy, 'enterpriseRegisPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร หนังสือให้ความยินยอม (PDF) </span><br>
                  <v-file-input
                    label="เอกสารหนังสือให้ความยินยอม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="consentCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(consentCopy, 'consentPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <!-- <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col> -->
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption8'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาใบวิสาหกิจเพื่อสังคม (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาใบวิสาหกิจเพื่อสังคม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="comunityCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(comunityCopy, 'comunityPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร หนังสือให้ความยินยอม (PDF) </span><br>
                  <v-file-input
                    label="เอกสารหนังสือให้ความยินยอม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="consentCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(consentCopy, 'consentPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเลขที่ผู้เสียภาษีอากร (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเลขที่ผู้เสียภาษีอากร"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="taxIDCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(taxIDCopy, 'taxIDPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร สวช.01 (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสาร สวช.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseRegisCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseRegisCopy, 'enterpriseRegisPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize || IpadProSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize || IpadProSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้รับมอบอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้รับมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="proxyIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(proxyIdCardCopy, 'proxyIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
              </div>
            </v-col>
            <!-- เลือก OTOP -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz5'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption9'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารรับรองการลงทะเบียน OTOP (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารรับรองการลงทะเบียน OTOP"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="regisOTOPCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(regisOTOPCopy, 'regisOTOPPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนกรรมการผู้มีอำนาจ พร้อมสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.1"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <!-- <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col> -->
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption10'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารรับรองการลงทะเบียน OTOP (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารรับรองการลงทะเบียน OTOP"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="regisOTOPCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(regisOTOPCopy, 'regisOTOPPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.1"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้รับมอบอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้รับมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="proxyIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(proxyIdCardCopy, 'proxyIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
              </div>
            </v-col>
            <!-- เลือกประชารัฐรักสามัคคี -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz6'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption11'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารประชารัฐรักสามัคคี (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารประชารัฐรักสามัคคี"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="pracharathCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(pracharathCopy, 'pracharathPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนกรรมการผู้มีอำนาจ พร้อมสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาหนังสือรับรองบริษัท อายุไม่เกิน 90 วัน (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    outlined
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    @change="UploadFile(certificateCompany, 'certificateCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                    id="file_input"
                    label="เอกสารสำเนาหนังสือรับรองบริษัท"
                    dense
                    v-model="certificateCompany"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>ภพ.20 ของบริษัท (PDF) <span v-if="radiosEtax === 'typeEtax1'" style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารภพ.20 ของบริษัท"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="vatCompany"
                    :rules="radiosEtax === 'typeEtax1' ? Rules.emptyFile : []"
                    :validate-on-blur="true"
                    @change="UploadFile(vatCompany, 'vatCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร สวช.01 (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสาร สวช.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseRegisCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseRegisCopy, 'enterpriseRegisPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.1"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร หนังสือให้ความยินยอม (PDF) </span><br>
                  <v-file-input
                    label="เอกสารหนังสือให้ความยินยอม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="consentCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(consentCopy, 'consentPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <!-- <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col> -->
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-wrap" v-if="currentFormOption === 'showFormOption12'">
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารประชารัฐรักสามัคคี (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารประชารัฐรักสามัคคี"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="pracharathCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(pracharathCopy, 'pracharathPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร หนังสือให้ความยินยอม (PDF) </span><br>
                  <v-file-input
                    label="เอกสารหนังสือให้ความยินยอม"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="consentCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(consentCopy, 'consentPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนา บอ. 01 (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนา บอ.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="eTaxInvoiceCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(eTaxInvoiceCopy, 'eTaxInvoicePath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>ภพ.20 ของบริษัท (PDF) <span v-if="radiosEtax === 'typeEtax1'" style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารภพ.20 ของบริษัท"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="vatCompany"
                    :rules="radiosEtax === 'typeEtax1' ? Rules.emptyFile : []"
                    :validate-on-blur="true"
                    @change="UploadFile(vatCompany, 'vatCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''" v-if="radiosEtax === 'typeEtax1'">
                  <span>สำเนาเอกสาร สวช.01 (PDF) </span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสาร สวช.01"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="enterpriseRegisCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(enterpriseRegisCopy, 'enterpriseRegisPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาเอกสารใบมอบอำนาจ (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาเอกสารใบมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="powerOfAttorneyFile"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(powerOfAttorneyFile, 'powerOfAttorneyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้มีอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF)</span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชน"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="ownerIdCardCopy"
                    :validate-on-blur="true"
                    @change="UploadFile(ownerIdCardCopy, 'ownerIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span>สำเนาหนังสือรับรองบริษัท อายุไม่เกิน 90 วัน (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    outlined
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    @change="UploadFile(certificateCompany, 'certificateCompanyPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                    id="file_input"
                    label="เอกสารสำเนาหนังสือรับรองบริษัท"
                    dense
                    v-model="certificateCompany"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">สำเนาบัตรประชาชนของผู้รับมอบอำนาจ พร้อมรับรองสำเนาถูกต้อง (PDF) <span style="color: red;">*</span></span><br>
                  <v-file-input
                    label="เอกสารสำเนาบัตรประชาชนผู้รับมอบอำนาจ"
                    outlined
                    dense
                    accept=".pdf, image/jpeg, image/jpg, image/png, image/webp, image/heic"
                    v-model="proxyIdCardCopy"
                    :rules="Rules.emptyFile"
                    :validate-on-blur="true"
                    @change="UploadFile(proxyIdCardCopy, 'proxyIdCardPath')"
                    :disabled="checkNextStep"
                    :class="checkNextStep === true ? 'classCheck' : ''"
                  >
                    <template v-slot:selection="{ text }">
                      <v-chip
                        small
                        label
                        color="#27AB9C"
                        style="color: #fff;"
                      >
                        {{ text }}
                      </v-chip>
                    </template>
                  </v-file-input>
                </v-col>
              </div>
            </v-col>
          </v-row>
          <v-row class="pa-3" dense justify="end">
            <v-btn v-if="checkNextStep === false" color="#D9D9D9" style="color: #000; border-radius: 15px;" class="px-8" dark @click="backToHome()">กลับสู่หน้าหลัก</v-btn>
            <v-btn v-if="checkNextStep === true" color="#D9D9D9" style="color: #000; border-radius: 15px;" class="px-8" @click="backToEdit()" dark>ย้อนกลับ</v-btn>
            <v-spacer></v-spacer>
            <v-btn v-if="checkNextStep === false" color="#5578f5" style="border-radius: 15px;" class="px-8" dark @click="confirm">ถัดไป</v-btn>
            <v-btn v-if="checkNextStep === true" color="#5578f5" style="border-radius: 15px;" class="px-8" dark @click="openDialogConfirmRegis()">ยืนยัน</v-btn>
          </v-row>
        </v-form>
      </v-card>
      <v-dialog v-model="dialogConfirmRegis" persistent :width="MobileSize ? '100%' : IpadSize ? '50%' : IpadProSize ? '40%' : '25%'">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%" class="pa-2">
          <v-card-text class="d-flex flex-column align-center mt-8" style="gap: .5vw">
            <v-img
              src="@/assets/ICON/iconConfirmRegis.png"
              max-height="80"
              max-width="80"
            ></v-img>
            <h2 class="mt-2"><b>บันทึกข้อมูล</b></h2>
            <span>คุณได้ทำการกรอกข้อมูลการลงทะเบียน</span>
            <span>คุณต้องการยืนยันการทำรายการนี้ ใช่ หรือ ไม่</span>
            <div class="d-flex mt-3" style="gap: 5vw">
              <v-btn outlined color="red" style="border: 2px solid; border-radius: 15px;"  @click="dialogConfirmRegis = false">ยกเลิก</v-btn>
              <v-btn color="#36D7A7" style="border: 0; color: #fff; border-radius: 15px;" @click="registerBusiness()">ยืนยัน</v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-dialog>
      <v-dialog v-model="dialogNextToCreateShop" persistent :width="MobileSize ? '100%' : IpadSize ? '50%' : IpadProSize ? '40%' : '25%'">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%" class="pa-2">
          <v-card-text class="d-flex flex-column align-center pt-4" style="gap: .5vw">
            <v-img
              src="@/assets/ICON/iconSuccess.png"
              max-height="80"
              max-width="80"
            ></v-img>
            <h2 class="mt-2"><b>บันทึกเสร็จสิ้น</b></h2>
            <span>คุณได้ทำการบันทึกข้อมูลลงทะเบียนเรียบร้อย</span>
            <div class="d-flex mt-3" style="gap: 5vw">
              <v-btn color="#828685" outlined style="border-radius: 15px;" @click="goToCreateShop()">ตกลง</v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-main>
  </v-app>
</template>

<script>
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      itemRadioTypeBusiness: [
        { label: 'นิติบุคคล', value: 'typeBiz1' },
        { label: 'ทะเบียนพาณิชย์', value: 'typeBiz2' },
        { label: 'OTOP', value: 'typeBiz5' },
        { label: 'วิสาหกิจชุมชน', value: 'typeBiz3' },
        { label: 'วิสาหกิจเพื่อสังคม', value: 'typeBiz4' },
        { label: 'ประชารัฐรักสามัคคี', value: 'typeBiz6' }
      ],
      businessTypeItem: [
        { nameTH: 'ห้างหุ้นส่วนสามัญ', nameEN: 'Ordinary Partnership', value: 1 },
        { nameTH: 'ห้างหุ้นส่วนจำกัด', nameEN: 'Limited Partnership', value: 2 },
        { nameTH: 'บริษัทจำกัด', nameEN: 'Company Limited', value: 3 },
        { nameTH: 'บริษัทมหาชนจำกัด', nameEN: 'Public Limited Company', value: 4 },
        { nameTH: 'นิติบุคคลอื่นๆ ภายใต้กฎหมายเฉพาะ', nameEN: 'Other', value: 5 }
      ],
      itemRadioEtax: [
        { label: 'ใช้บริการ e-Tax Invoice & e-Receipt', value: 'typeEtax1' },
        { label: 'ไม่ใช้บริการ e-Tax Invoice & e-Receipt', value: 'typeEtax2' }
      ],
      itemRadioCoordinator: [
        { label: 'กรรมการดำเนินการเอง', value: 'typeCoor1' },
        { label: 'มอบอำนาจ', value: 'typeCoor2' }
      ],
      itemRadioTypeShop: [
        { label: 'ลงทะเบียนร้านค้า', value: 'typeShop1' },
        { label: 'ลงทะเบียนคู่ค้า', value: 'typeShop2' }
      ],
      radiosTypeBiz: 'typeBiz1',
      radiosEtax: 'typeEtax2',
      radiosCoordinator: 'typeCoor1',
      radiosTypeShop: 'typeShop1',
      bizNumber: '',
      accessCode: '',
      ownerIdCard: '',
      businessTypeTH: '',
      businessTypeEN: '',
      bizNameTH: '',
      bizNameDocTH: '',
      bizNameEN: '',
      bizNameDocEN: '',
      phone: '',
      tel: '',
      email: '',
      houseNumber: '',
      roomNumber: '',
      floorNumber: '',
      building: '',
      village: '',
      moo: '',
      soi: '',
      yaek: '',
      road: '',
      checkProvinceError: '',
      checkDistrictError: '',
      checkSubDistrictError: '',
      checkZipcodeError: '',
      provinceText: '',
      districtText: '',
      subdistricttext: '',
      zipcodeText: '',
      formRegis: true,
      firstNameTH: '',
      lastNameTH: '',
      taxId: '',
      telNo: '',
      email_eTax: '',
      department: 'owner',
      position: 'owner',
      emailOwner: '',
      branchNo: '00000',
      branchName: 'สำนักงานใหญ่',
      certificateCompany: null,
      vatCompany: null,
      ownerIdCardCopy: null,
      proxyIdCardCopy: null,
      powerOfAttorneyFile: null,
      eTaxInvoiceCopy: null,
      bizRegCopy: null,
      taxIDCopy: null,
      enterpriseCopy: null,
      enterpriseRegisCopy: null,
      consentCopy: null,
      comunityCopy: null,
      regisOTOPCopy: null,
      pracharathCopy: null,
      SMCECopy: null,
      formData: {
        certificateCompanyPath: '',
        vatCompanyPath: '',
        ownerIdCardPath: '',
        powerOfAttorneyPath: '',
        proxyIdCardPath: '',
        bizRegPath: '',
        eTaxInvoicePath: '',
        taxIDPath: '',
        enterprisePath: '',
        enterpriseRegisPath: '',
        consentPath: '',
        SMCEPath: '',
        comunityPath: '',
        regisOTOPPath: '',
        pracharathPath: '',
        approve_document_doc: '',
        result_meeting_doc: ''
      },
      checkNextStep: false,
      dialogConfirmRegis: false,
      dialogNextToCreateShop: false,
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        bizNameTH: [
          v => !!v || 'กรุณากรอกชื่อนิติบุคคล',
          v => /^[ก-๏0-9๐-๙():.,-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        firstNameTH: [
          v => !!v || 'กรุณากรอกชื่อ',
          v => /^[ก-๏0-9๐-๙():.,-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        lastNameTH: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => /^[ก-๏0-9๐-๙():.,-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        bizNameEN: [
          v => !!v || 'กรุณากรอกชื่อนิติบุคคล',
          v => /^[a-zA-Z0-9_.-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        bizNameDocTH: [
          v => !!v || 'กรุณากรอกชื่อสำหรับแสดงบนเอกสาร',
          v => /^[ก-๙0-9.,()_-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        bizNameDocEN: [
          v => !!v || 'กรุณากรอกชื่อสำหรับแสดงบนเอกสาร',
          v => /^[a-zA-Z0-9_.()\-,\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หลัก'
        ],
        phone: [
          v => !!v || 'กรุณากรอกเบอร์มือถือ',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขมือถือ 9 หรือ 10 หลัก'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        houseNumber: [
          v => !!v || 'กรุณาระบุเลขที่',
          v => /^[/0-9/-\s]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข'
        ],
        taxId: [
          v => !!v || 'กรุณากรอกเลขประจำตัวประชาชน',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวประชาชนให้ครบ 13 หลัก',
          v => this.validNationalID(v) || 'เลขประจำตัวประชาชนไม่ถูกต้อง'
        ],
        bizNumber: [
          v => !!v || 'กรุณาระบุเลขทะเบียนนิติบุคคล',
          v => /^[/0-9\s]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข',
          v => v.length === 13 || 'กรุณากรอกหมายเลขทะเบียนนิติบุคคล 13 หลัก'
        ],
        branchNo: [
          v => !!v || 'กรุณาระบุรหัสสาขา',
          v => /^[/0-9\s]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข',
          v => v.startsWith('00') || 'สองตัวแรกต้องเป็น 00',
          v => v.length === 5 || 'กรุณากรอกรหัสสาขา 5 หลัก'
        ],
        telNo: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        email_eTax: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        accessCode: [
          v => !!v || 'กรุณาระบุรหัสทะเบียน',
          v => /^[/0-9\s]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข',
          v => v.length === 10 || 'กรุณากรอกรหัสทะเบียน 10 หลัก'
        ],
        // ownerIdCard: [
        //   v => !!v || 'กรุณาระบุหมายเลขบัตรประชาชนของผู้มีอำนาจ',
        //   v => /^[/0-9\s]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข',
        //   v => v.length === 13 || 'กรุณากรอกหมายเลขบัตรประชาชน 13 หลัก'
        // ],
        emptyFile: [
          // v => (v && v.length > 0) || 'กรุณากรอกข้อมูล',
          v => !!v || 'กรุณากรอกข้อมูล',
          v => !v || (v.type === 'application/pdf' || v.type === 'image/png' || v.type === 'image/jpeg' || v.type === 'image/jpg' || v.type === 'image/webp' || v.type === 'image/heic') || 'กรุณาเพิ่มไฟล์ PDF หรือไฟล์รูปภาพ ไม่เกิน 10 MB เท่านั้น',
          v => !v || v.size < 10000000 || 'กรุณาเพิ่มไฟล์ไม่เกิน 10 MB'
          // v => (!!v || (v && v.length > 0)) || 'กรุณากรอกข้อมูล'
        ]
      },
      pageSelect: ''
    }
  },
  components: {
    AppBarRegis: () => import(/* webpackPrefetch: true */ '@/components/Home/AppBarRegisUI')
  },
  created () {
    this.pageSelect = this.$route.query.page
  },
  watch: {
    provinceText (val) {
      if (/\s/g.test(val)) {
        this.provinceText = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.districtText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.districtText = ''
        }
      }
    },
    districtText (val) {
      if (/\s/g.test(val)) {
        this.districtText = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.provinceText = ''
        }
      }
    },
    subdistricttext (val) {
      if (/\s/g.test(val)) {
        this.subdistricttext = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcodeText = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    zipcodeText (val) {
      if (/\s/g.test(val)) {
        this.zipcodeText = val.replace(/\s/g, '').substring(0, 5)
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            this.zipcodeText = this.checkZipcode
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistricttext = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.subdistricttext = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/infoRegisterShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/infoRegisterShop' }).catch(() => {})
      }
    },
    certificateCompany (val) {
      // console.log(val)
    }
    // radiosTypeBiz (val) {
    //   console.log(val)
    // },
    // radiosEtax (val) {
    //   console.log(val)
    // },
    // radiosCoordinator (val) {
    //   console.log(val)
    // },
    // certificate (val) {
    //   console.log(val)
    // }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    isValidEtax () {
      return this.radiosEtax === 'typeEtax1' || this.radiosEtax === 'typeEtax2'
    },
    currentFormOption () {
      if (!this.isValidEtax) return null
      const typeBiz = this.radiosTypeBiz.replace('typeBiz', '')
      const coor = this.radiosCoordinator.replace('typeCoor', '')
      return typeBiz && coor ? `showFormOption${(parseInt(typeBiz) - 1) * 2 + parseInt(coor)}` : null
    },
    filteredItemRadioTypeBusiness () {
      if (this.pageSelect === 'partner') {
        return [
          { label: 'นิติบุคคล', value: 'typeBiz1' }
        ]
      }
      return this.itemRadioTypeBusiness
    }
  },
  methods: {
    validateTaxID (num) {
      if (this.validNationalID(num)) {
        return true
      } else {
        return false
      }
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    changeTypeTH () {
      if (this.businessTypeTH === 1) {
        this.businessTypeEN = 1
      } else if (this.businessTypeTH === 2) {
        this.businessTypeEN = 2
      } else if (this.businessTypeTH === 3) {
        this.businessTypeEN = 3
      } else if (this.businessTypeTH === 4) {
        this.businessTypeEN = 4
      } else {
        this.businessTypeEN = 5
      }
    },
    changeTypeEN () {
      if (this.businessTypeEN === 1) {
        this.businessTypeTH = 1
      } else if (this.businessTypeEN === 2) {
        this.businessTypeTH = 2
      } else if (this.businessTypeEN === 3) {
        this.businessTypeTH = 3
      } else if (this.businessTypeEN === 4) {
        this.businessTypeTH = 4
      } else {
        this.businessTypeTH = 5
      }
    },
    goToCreateShop () {
      if (this.MobileSize) {
        if (this.pageSelect === 'shop') {
          this.$router.push({ path: '/createShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/detailCompanyMobile' }).catch(() => {})
        }
      } else {
        if (this.pageSelect === 'shop') {
          this.$router.push({ path: '/createShop' }).catch(() => {})
        } else {
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        }
      }
    },
    backToHome () {
      this.$router.push({ path: '/' }).catch(() => {})
    },
    // async UploadFile () {
    //   const reader = new FileReader()
    //   reader.readAsDataURL(this.certificateCompany)
    //   reader.onload = () => {
    //     var resultReader = reader.result
    //     console.log(resultReader.split(',')[1], 555)
    //     const DataImage = {
    //       image: [resultReader],
    //       type: ''
    //     }
    //     this.$store.commit('openLoader')
    //     await this.$store.dispatch('actionsUploadToS3', DataImage)
    //     const response = await this.$store.state.ModuleShop.stateUploadToS3

    //     if (response.message === 'List Success.') {
    //       this.imagesPath = response.data.list_path[0].path
    //     }
    //   }
    // },
    openDialogConfirmRegis () {
      this.dialogConfirmRegis = true
    },
    async checkBusiness () {
      this.$store.commit('openLoader')
      if (this.bizNumber === '') {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'กรุณากรอกเลขนิติบุคคล', showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      } else if (this.bizNumber.length < 13) {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'กรุณากรอกเลขนิติบุคคล 13 หลัก', showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      } else {
        var data = {
          id_card_num: this.bizNumber
        }
        await this.$store.dispatch('actionsCheckBusiness', data)
        var res = await this.$store.state.ModuleBusiness.stateCheckBusiness
        if (res.result === 'SUCCESS') {
          var resFindId = res.data
          if (resFindId.NGC_biz.length !== 0 && resFindId.OneID_biz.length === 0) {
            var resOneID = resFindId.NGC_biz[0]
            // const addressObj = JSON.parse(resOneID.address)
            // this.accessCode = resOneID.accessCode
            // this.ownerIdCard = resOneID.ownerIdCard
            this.businessTypeTH = resOneID.account_title_th === 'ห้างหุ้นส่วนสามัญ' ? 1 : resOneID.account_title_th === 'ห้างหุ้นส่วนจำกัด' ? 2 : resOneID.account_title_th === 'บริษัทจำกัด' ? 3 : resOneID.account_title_th === 'บริษัทมหาชนจำกัด' ? 4 : 5
            this.bizNameTH = resOneID.first_name_th
            this.bizNameDocTH = resOneID.name_on_document_th
            this.businessTypeEN = resOneID.account_title_eng === 'Ordinary Partnership' ? 1 : resOneID.account_title_eng === 'Limited Partnership' ? 2 : resOneID.account_title_eng === 'Company Limited' ? 3 : resOneID.account_title_eng === 'Public Limited Company' ? 4 : 5
            this.bizNameEN = resOneID.first_name_eng
            this.bizNameDocEN = resOneID.name_on_document_eng
            this.tel = resOneID.tel_no
            this.phone = resOneID.mobile_no
            this.email = resOneID.email
            this.houseNumber = resOneID.address.house_no
            this.roomNumber = resOneID.address.room_no
            this.floorNumber = resOneID.address.floor
            this.building = resOneID.address.building_name
            this.village = resOneID.address.moo_ban
            this.moo = resOneID.address.moo_no
            this.soi = resOneID.address.soi
            this.yaek = resOneID.address.yaek
            this.road = resOneID.address.street
            this.provinceText = resOneID.address.province
            this.districtText = resOneID.address.amphoe
            this.subdistricttext = resOneID.address.tambon
            this.zipcodeText = resOneID.address.zipcode
          } else if (resFindId.OneID_biz.length !== 0 && resFindId.NGC_biz.length === 0) {
            if (this.MobileSize) {
              this.$router.push({ path: '/createbusinesssidMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/createbusinesssid' }).catch(() => {})
            }
          } else if (resFindId.OneID_biz.length !== 0 && resFindId.NGC_biz.length !== 0) {
            if (this.MobileSize) {
              if (this.pageSelect === 'shop') {
                this.$router.push({ path: '/createShopMobile' }).catch(() => {})
              } else {
                this.$router.push({ path: '/detailCompanyMobile' }).catch(() => {})
              }
            } else {
              if (this.pageSelect === 'shop') {
                this.$router.push({ path: '/createShop' }).catch(() => {})
              } else {
                this.$router.push({ path: '/detailCompany' }).catch(() => {})
              }
            }
          } else {
            this.businessTypeTH = ''
            this.bizNameTH = ''
            this.bizNameDocTH = ''
            this.businessTypeEN = ''
            this.bizNameEN = ''
            this.bizNameDocEN = ''
            this.tel = ''
            this.phone = ''
            this.email = ''
            this.houseNumber = ''
            this.roomNumber = ''
            this.floorNumber = ''
            this.building = ''
            this.village = ''
            this.moo = ''
            this.soi = ''
            this.yaek = ''
            this.road = ''
            this.provinceText = ''
            this.districtText = ''
            this.subdistricttext = ''
            this.zipcodeText = ''
          }
          this.$store.commit('closeLoader')
        } else if (res.code === 400) {
          this.$swal.fire({
            icon: 'error',
            html: `${res.message}`,
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
          this.$store.commit('closeLoader')
        }
      }
    },
    async UploadFile (file, fieldName) {
      if (file.type === 'application/pdf' || file.type === 'image/png' || file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/webp' || file.type === 'image/heic') {
        if (file !== undefined && file !== null) {
          const fileSize = file.size / 1024 / 1024
          if (fileSize < 10) {
            var formData = new FormData()
            formData.append('pdf', file)
            formData.append('type', 'biz_pdf')
            formData.append('approve_document', fieldName === 'certificateCompanyPath' || fieldName === 'bizRegPath' || fieldName === 'enterprisePath' || fieldName === 'comunityPath' || fieldName === 'regisOTOPPath' || fieldName === 'pracharathPath' ? '1' : '0')
            formData.append('result_meeting', fieldName === 'powerOfAttorneyPath' ? '1' : '0')
            this.$store.commit('openLoader')
            await this.$store.dispatch('actionUploadPDF', formData)

            const response = this.$store.state.ModuleShop.stateUploadPDF

            if (response.message === 'List Success.') {
              const imagesPath = response.data.list_path[0]
              if (fieldName === 'powerOfAttorneyPath') {
                this.formData[fieldName] = imagesPath.path
                this.formData.result_meeting_doc = imagesPath.oneID_doc_path
              } else if (fieldName === 'certificateCompanyPath' || fieldName === 'powerOfAttorneyPath' || fieldName === 'bizRegPath' || fieldName === 'enterprisePath' || fieldName === 'comunityPath' || fieldName === 'regisOTOPPath' || fieldName === 'pracharathPath') {
                this.formData.approve_document_doc = imagesPath.oneID_doc_path
                this.formData[fieldName] = imagesPath.path
              } else {
                this.formData[fieldName] = imagesPath.path
              }
              this.$store.commit('closeLoader')
            } else if (response.code === 400) {
              this.$swal.fire({
                icon: 'error',
                html: `${response.message}`,
                confirmButtonText: 'ปิด',
                showConfirmButton: true
              })
              this.$store.commit('closeLoader')
            } else {
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
              })
              this.$store.commit('closeLoader')
            }
          } else {
            if (fieldName === 'certificateCompanyPath') {
              this.certificateCompany = []
              // document.getElementById('file_input').value = {}
              // console.log(document.getElementById('file_input').value)
            } else if (fieldName === 'vatCompanyPath') {
              this.vatCompany = []
            } else if (fieldName === 'ownerIdCardPath') {
              this.ownerIdCardCopy = []
            } else if (fieldName === 'proxyIdCardPath') {
              this.proxyIdCardCopy = []
            } else if (fieldName === 'powerOfAttorneyPath') {
              this.powerOfAttorneyFile = []
            } else if (fieldName === 'eTaxInvoicePath') {
              this.eTaxInvoiceCopy = []
            } else if (fieldName === 'bizRegPath') {
              this.bizRegCopy = []
            } else if (fieldName === 'taxIDPath') {
              this.taxIDCopy = []
            } else if (fieldName === 'enterprisePath') {
              this.enterpriseCopy = []
            } else if (fieldName === 'enterpriseRegisPath') {
              this.enterpriseRegisCopy = []
            } else if (fieldName === 'consentPath') {
              this.consentCopy = []
            } else if (fieldName === 'comunityPath') {
              this.comunityCopy = []
            } else if (fieldName === 'regisOTOPPath') {
              this.regisOTOPCopy = []
            } else if (fieldName === 'pracharathPath') {
              this.pracharathCopy = []
            } else {
              this.SMCECopy = []
            }
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่ไฟล์ที่มีขนาดไม่เกิน 10 MB', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        if (fieldName === 'certificateCompanyPath') {
          this.certificateCompany = []
          // document.getElementById('file_input').value = {}
          // console.log(document.getElementById('file_input').value)
        } else if (fieldName === 'vatCompanyPath') {
          this.vatCompany = []
        } else if (fieldName === 'ownerIdCardPath') {
          this.ownerIdCardCopy = []
        } else if (fieldName === 'proxyIdCardPath') {
          this.proxyIdCardCopy = []
        } else if (fieldName === 'powerOfAttorneyPath') {
          this.powerOfAttorneyFile = []
        } else if (fieldName === 'eTaxInvoicePath') {
          this.eTaxInvoiceCopy = []
        } else if (fieldName === 'bizRegPath') {
          this.bizRegCopy = []
        } else if (fieldName === 'taxIDPath') {
          this.taxIDCopy = []
        } else if (fieldName === 'enterprisePath') {
          this.enterpriseCopy = []
        } else if (fieldName === 'enterpriseRegisPath') {
          this.enterpriseRegisCopy = []
        } else if (fieldName === 'consentPath') {
          this.consentCopy = []
        } else if (fieldName === 'comunityPath') {
          this.comunityCopy = []
        } else if (fieldName === 'regisOTOPPath') {
          this.regisOTOPCopy = []
        } else if (fieldName === 'pracharathPath') {
          this.pracharathCopy = []
        } else {
          this.SMCECopy = []
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          html: 'กรุณาเพิ่มไฟล์ PDF หรือไฟล์รูปภาพเท่านั้น'
        })
      }
    },
    confirm () {
      this.$store.commit('openLoader')
      if (this.$refs.form.validate(true)) {
        // console.log(this.certificateCompany)
        if (this.provinceText === '' || this.districtText === '' || this.subdistricttext === '' || this.zipcodeText === '') {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          this.$store.commit('closeLoader')
        } else {
          this.checkNextStep = true
          window.scrollTo(0, 0)
          setTimeout(() => {
            this.$store.commit('closeLoader')
          }, 1000)
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
        setTimeout(() => {
          this.$store.commit('closeLoader')
        }, 1000)
      }
    },
    backToEdit () {
      this.$store.commit('openLoader')
      this.checkNextStep = false
      window.scrollTo(0, 0)
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    async registerBusiness () {
      this.$store.commit('openLoader')
      var data = {
        type: 'Enterprise',
        level: '0',
        account_title_th: this.businessTypeTH === 1 ? 'ห้างหุ้นส่วนสามัญ' : this.businessTypeTH === 2 ? 'ห้างหุ้นส่วนจำกัด' : this.businessTypeTH === 3 ? 'บริษัทจำกัด' : this.businessTypeTH === 4 ? 'บริษัทมหาชนจำกัด' : 'นิติบุคคลอื่นๆ ภายใต้กฎหมายเฉพาะ',
        first_name_th: this.bizNameTH,
        account_title_eng: this.businessTypeEN === 1 ? 'Ordinary Partnership' : this.businessTypeEN === 2 ? 'Limited Partnership' : this.businessTypeEN === 3 ? 'Company Limited' : this.businessTypeEN === 4 ? 'Public Limited Company' : 'Other',
        first_name_eng: this.bizNameEN,
        name_on_document_th: this.bizNameDocTH,
        name_on_document_eng: this.bizNameDocEN,
        id_card_type: 'TAX_ID',
        id_card_num: this.bizNumber,
        accessCode: this.radiosTypeBiz === 'typeBiz1' || this.radiosTypeBiz === 'typeBiz2' ? '' : this.accessCode,
        ownerIdCard: this.radiosTypeBiz === 'typeBiz4' || this.radiosTypeBiz === 'typeBiz6' ? this.ownerIdCard : '',
        email: this.email,
        tel_no: this.tel,
        mobile_no: this.phone,
        approve_document: this.radiosTypeBiz === 'typeBiz1' ? this.formData.certificateCompanyPath : this.radiosTypeBiz === 'typeBiz2' ? this.formData.bizRegPath : this.radiosTypeBiz === 'typeBiz3' ? this.formData.enterprisePath : this.radiosTypeBiz === 'typeBiz4' ? this.formData.comunityPath : this.radiosTypeBiz === 'typeBiz5' ? this.formData.regisOTOPPath : this.formData.pracharathPath,
        result_meeting: this.radiosCoordinator === 'typeCoor1' ? '' : this.formData.powerOfAttorneyPath,
        approve_document_doc: this.formData.approve_document_doc,
        result_meeting_doc: this.formData.result_meeting_doc,
        // owner_detail: [
        //   {
        //     thai_email: this.emailOwner,
        //     department: this.department,
        //     position: this.position
        //   }
        // ],
        address: [
          {
            house_code: '',
            house_no: this.houseNumber,
            room_no: this.roomNumber,
            moo_ban: this.village,
            moo_no: this.moo,
            building_name: this.building,
            floor: this.floorNumber,
            yaek: this.yaek,
            street: this.road,
            fax_number: '',
            soi: this.soi,
            province: this.provinceText,
            tambon: this.subdistricttext,
            amphoe: this.districtText,
            zipcode: this.zipcodeText,
            country: 'Thailand'
          }
        ],
        branch_no: this.branchNo,
        branch_name: this.branchName,
        type_register: this.pageSelect,
        type_biz: this.radiosTypeBiz === 'typeBiz1' ? 'corporation' : this.radiosTypeBiz === 'typeBiz2' ? 'BusinessRegis' : this.radiosTypeBiz === 'typeBiz3' ? 'Community' : this.radiosTypeBiz === 'typeBiz4' ? 'Social' : this.radiosTypeBiz === 'typeBiz5' ? 'OTOP' : 'pracharath',
        type_eTaxUse: this.radiosEtax === 'typeEtax1' ? 'Y' : 'N',
        type_coordinator: this.radiosCoordinator === 'typeCoor1' ? 'Director' : 'Empower',
        coordinator_info: [
          {
            firstNameTH: this.firstNameTH,
            lastNameTH: this.lastNameTH,
            taxId: this.taxId,
            telNo: this.radiosEtax === 'typeEtax2' ? '' : this.telNo,
            email_eTax: this.radiosEtax === 'typeEtax2' ? '' : this.email_eTax
          }
        ],
        certificateCompany: this.formData.certificateCompanyPath,
        vatCompany: this.formData.vatCompanyPath,
        ownerIdCardCopy: this.formData.ownerIdCardPath,
        powerOfAttorneyFile: this.formData.powerOfAttorneyPath,
        proxyIdCardCopy: this.formData.proxyIdCardPath,
        bizRegCopy: this.formData.bizRegPath,
        eTaxInvoiceCopy: this.formData.eTaxInvoicePath,
        taxIDCopy: this.formData.taxIDPath,
        enterpriseCopy: this.formData.enterprisePath,
        enterpriseRegisCopy: this.formData.enterpriseRegisPath,
        consentCopy: this.formData.consentPath,
        SMCECopy: this.formData.SMCEPath,
        comunityCopy: this.formData.comunityPath,
        regisOTOPCopy: this.formData.regisOTOPPath,
        pracharathCopy: this.formData.pracharathPath
      }
      // console.log(data, '***')
      await this.$store.dispatch('actionsRegisterShop', data)
      var res = await this.$store.state.ModuleBusiness.stateRegisterShop
      if (res.code === 200) {
        this.$EventBus.$emit('getUserDetail')
        await this.$EventBus.$emit('AuthorityUser')
        await this.$EventBus.$emit('getBusinessMP')
        this.dialogConfirmRegis = false
        this.dialogNextToCreateShop = true
        this.$store.commit('closeLoader')
      } else if (res.code === 400) {
        if (res.message === 'title account are not match') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>คำนำหน้าชื่อบริษัทภาษาไทยและอังกฤษไม่ตรงกัน</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'the selected account title th is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>คำนำหน้าชื่อบริษัทภาษาไทย รูปแบบ ไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The first name th format is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ชื่อจริงภาษาไทย รูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'title account are not match') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>คำนำหน้าชื่อภาษาไทยและอังกฤษไม่ตรงกัน</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'the selected account title eng is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>คำนำหน้าชื่อภาษาอังกฤษรูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The first name eng format is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ชื่อจริงภาษาอังกฤษ รูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'the selected id card type is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ชนิดบัตร รูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'id duplicate') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>มีรหัสประชาชน หรือ เลขที่หนังสือเดินทางนี้อยู่แล้ว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'ID card is incorrect') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>รหัสประชาชน หรือ เลขที่หนังสือเดินทาง รูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'email duplicate') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>มีอีเมลนี้อยู่แล้ว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The mobile no format is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>หมายเลขโทรศัพท์ รูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The mobile no may not be greater than 10 characters') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>เบอร์โทรศัพท์มือถือต้องไม่เกิน 10 ตัว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The mobile no must be at least 10 characters') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>เบอร์โทรศัพท์มือถือต้องไม่ น้อยกว่า 10 ตัว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The tel no format is invalid') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>หมายเลขโทรศัพท์ รูปแบบไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The tel no may not be greater than 20 characters') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>เบอร์โทรศัพท์ต้องไม่เกิน 20 ตัว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'The tel no must be at least 4 characters') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>เบอร์โทรศัพท์มือถือต้องไม่น้อยกว่า 4 ตัว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'approve Document not found') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ไม่พบไฟล์เอกสารหนังสือรับรองนิติบุคคล</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'result Document not found') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ไม่พบไฟล์เอกสารหนังสือมอบอำนาจ</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === '<EMAIL>/<EMAIL> not found') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ไม่พบอีเมลนี้ในระบบ</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'Address detail not found') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>ที่อยู่ตำบล อำเภอ จังหวัด และรหัสไปรษณีย์ไม่สัมพันธ์กัน</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'tax_id is incorrect.') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>เลขนิติบุคคลไม่ถูกต้อง</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'This TAX_ID is already exist.') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>เลขนิติบุคคลมีในระบบแล้ว</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else if (res.message === 'branch_no dupicate.') {
          this.$swal.fire({
            icon: 'error',
            html: '<h4>รหัสสาขาซ้ำ</h4>',
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({
            icon: 'error',
            html: `${res.message}`,
            confirmButtonText: 'ปิด',
            showConfirmButton: true
          })
          this.dialogConfirmRegis = false
          this.$store.commit('closeLoader')
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.dialogConfirmRegis = false
        this.$store.commit('closeLoader')
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistricttext
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.districtText
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.provinceText
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcodeText)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    }
  }
}
</script>

<style>

</style>
<style scoped>
::v-deep .th-address-input {
  border-radius: 8px;
}
::v-deep input.th-address-input {
  color: black !important;
}
::v-deep .classCheck .v-input__slot,
::v-deep .classCheck .th-address-input {
  background-color: #ebebeb !important;
}
</style>
