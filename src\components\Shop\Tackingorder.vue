<template>
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <v-card width="100%" height="100%" elevation="0">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ติดตามสถานะสินค้า</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> ติดตามสถานะสินค้า</v-card-title>
      <!-- <a-tabs @change="getOrderReturn">
    <a-tab-pane :key="0"><span slot="tab">รายการสั่งซื้อ <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countorder }}</a-tag></span></a-tab-pane>
    <a-tab-pane :key="1"><span slot="tab">รายการสั่งซื้อที่ถูกตีกลับ <a-tag color="#D1392B" style="border-radius: 8px;">{{ countreturn }}</a-tag></span></a-tab-pane>
  </a-tabs> -->
      <a-tabs @change="getOrderReturn" class="px-2">
        <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countorder
        }}</a-tag></span></a-tab-pane>
        <a-tab-pane :key="1"><span slot="tab">จัดส่งสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{
        countsuccess }}</a-tag></span></a-tab-pane>
        <a-tab-pane :key="4"><span slot="tab">กำลังดำเนินการ <a-tag color="#FFA500" style="border-radius: 8px;">{{
        countWaitDoing }}</a-tag></span></a-tab-pane>
        <a-tab-pane :key="2"><span slot="tab">กำลังจัดส่ง <a-tag color="#FAD02C" style="border-radius: 8px;">{{
        countwait }}</a-tag></span></a-tab-pane>
        <a-tab-pane :key="3"><span slot="tab">ถูกตีกลับ/ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{
        countreturn }}</a-tag></span></a-tab-pane>
      </a-tabs>
      <v-container>
        <v-text-field :style="IpadSize ? '' : 'width:400px'" v-model="search" append-icon="mdi-magnify" placeholder="ค้นหาจากรหัสการสั่งซื้อ"
          v-if="show" outlined dense rounded hide-details></v-text-field>
        <v-col v-if="disableTable === false" cols="12" :class="!MobileSize ? 'pl-0 pr-3 mb-3 mt-3' : 'pl-2 pr-2 mb-3 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="selectMenu === 0">รายการติดตามสถานะสินค้าทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="selectMenu === 1">รายการติดตามสถานะสินค้าที่จัดส่งสำเร็จทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="selectMenu === 2">รายการติดตามสถานะสินค้าที่กำลังจัดส่งทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="selectMenu === 3">รายการติดตามสถานะสินค้าที่ถูกตีกลับ/ยกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="selectMenu === 4">รายการติดตามสถานะกำลังดำเนินการทั้งหมด {{ showCountOrder }} รายการ</span>
        </v-col>
        <!-- <h4 v-if="!MobileSize"  style="font-size:14px">รายการสั่งซื้อที่ยังไม่ได้เรียกพนักงาน {{this.quantity}} รายการ</h4> -->
        <v-data-table v-if="show" v-model="selected" :page.sync="page" :headers="headers" :items="data" :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          :search="search" item-key="referance_id" color="blue" class="elevation-1" @pagination="countOrdar" no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา" no-data-text="ไม่มีรายการในตาราง">
          <!-- <template v-slot:[`item.reference_id`]="{ item }">
        <span><a :href="item.url_barcode_picture" target="_blank">{{item.reference_id}}</a></span>
      </template> -->
          <!-- <template v-slot:[`item.order_no`]="{ item }">
        <span><a :href="item.url_tracking" target="_blank">{{item.order_no}}</a></span>
      </template> -->
          <template v-slot:[`item.status`]="{ item }">
            <span v-if="item.status === 1">
              <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">สร้างใหม่</v-chip>
              <!-- <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เรียกพนักรับพัสดุ</v-chip> -->
            </span>
            <span v-else-if="item.status === 5">
              <v-chip class="ma-2" color="#E9EAFE" text-color="#111FFA">เรียกพนักงานรับพัสดุแล้ว</v-chip>
            </span>
            <span v-else-if="item.status === 6">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พนักงานรับพัสดุแล้ว</v-chip>
            </span>
            <span v-else-if="item.status === 7">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">อยู่ระหว่างการขนส่ง</v-chip>
            </span>
            <span v-else-if="item.status === 8">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พัสดุถึงสาขาปลายทาง</v-chip>
            </span>
            <span v-else-if="item.status === 9">
              <v-chip v-if="item.delivery_type  === 'DELIVERY'" class="ma-2" color="#F0F9EE" text-color="#1AB759">
                พัสดุถึงผู้รับแล้ว</v-chip>
              <v-chip v-if="item.delivery_type  === 'DELIVERY_RETURN'" class="ma-2" color="#F7D9D9"
                text-color="#D1392B">คืนพัสดุกลับไปต้นทาง</v-chip>
            </span>
            <span v-else-if="item.status === 11">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พัสดุตีกลับสาขาปลายทาง</v-chip>
            </span>
            <span v-else-if="item.status === 12">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">นำส่งไม่สำเร็จ</v-chip>
            </span>
            <span v-else-if="item.status === 13">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เคลมและไม่มีการส่งคืนสินค้า</v-chip>
            </span>
            <span v-else-if="item.status === 14">
              <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">คืนพัสดุกลับไปต้นทาง</v-chip>
            </span>
            <span v-else-if="item.status === 15">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">น้ำหนักและขนาดเกินที่กำหนด</v-chip>
            </span>
            <span v-else-if="item.status === 16">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เคลมสินค้า</v-chip>
            </span>
            <span v-else-if="item.status === -1">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">คืนพัสดุขณะขนส่ง</v-chip>
            </span>
            <span v-else-if="item.status === 0">
              <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกแล้ว</v-chip>
            </span>
            <span v-else-if="item.status === -99">
              <v-chip class="ma-2" color="#FFA500" text-color="#FFFFFF">กำลังดำเนินการ</v-chip>
            </span>
          </template>
          <template v-slot:[`item.updated_at`]="{ item }">
            {{ new Date(item.updated_at).toLocaleDateString('th-TH', {timeZone: "UTC", year: 'numeric', month:
            'long', day: 'numeric' })}}
          </template>
          <template v-slot:[`item.manages`]="{ item }">
            <!-- <a :href="item.url_barcode_picture" target="_blank"><v-icon color="#27AB9C" >mdi-barcode-scan</v-icon> ปริ้นบาร์โค้ด</a> -->
            <!-- <v-btn class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" text color="#27AB9C"  @click="OpenTacking(item)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain></v-img> ติดตามสถานะขนส่ง</v-btn> -->
            <v-row>
              <v-btn x-small outlined
                style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                class="pt-4 pb-4" :href="item.url_barcode_picture" target="_blank">
                <v-icon color="#27AB9C" size="22">mdi-barcode-scan</v-icon>
              </v-btn>
              <v-btn x-small outlined
                style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                class="pt-4 pb-4 ml-2" @click="OpenTacking(item)">
                <v-img width="25" src="@/assets/icons/Vector.png" contain></v-img>
              </v-btn>
            </v-row>
          </template>
        </v-data-table>
        <v-row justify="center" align-content="center" v-if="disableTable === true">
          <v-col cols="12" md="12" align="center" style="min-height: 636px;">
            <div style="padding-top: 90px;">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="selectMenu === 0">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการติดตามสถานะสินค้า</span><br />
            </h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="selectMenu === 1">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการติดตามสถานะสินค้าที่จัดส่งสำเร็จ</span><br />
            </h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="selectMenu === 2">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการติดตามสถานะสินค้าที่กำลังจัดส่ง</span><br />
            </h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="selectMenu === 3">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการติดตามสถานะสินค้าที่ถูกตีกลับ/ยกเลิก</span><br />
            </h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="selectMenu === 4">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการติดตามสถานะกำลังดำเนินการ</span><br />
            </h2>
          </v-col>
        </v-row>
        <!-- <div><span style="color:red">หมายเหตุ</span> : จะสามารถเรียกคูเรียร์/พนักงานเข้ารับได้ทีละ 1 งานเท่านั้น โดย 1 งานมีกี่คำสั่งซื้อก็ได้ หากต้องการความช่วยเหลือ ให้ติดต่อทาง Flash โดยตรง</div> -->
        <!-- <div v-if="show" class="mt-1"><span style="color:red">หมายเหตุ</span> : หากต้องการที่จะพิมพ์บาร์โค้ด(Barcode) สามารถกดที่ "สัญญาลักษณ์บาร์โค้ด(Icon Barcode)"</div> -->
      </v-container>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data: () => ({
    quantity: 0,
    page: 1,
    res: [],
    respons: [],
    oneData: [],
    selected: [],
    data: [],
    SelectOrder: [],
    Message: '',
    DataCurier: '',
    selectMenu: 0,
    search: '',
    dialog: false,
    dialog_Delete: false,
    dialogAllselect: false,
    disabledCount: 0,
    countorder: 0,
    countwait: 0,
    countreturn: 0,
    countsuccess: 0,
    countWaitDoing: 0,
    showCountOrder: 0,
    disableTable: false,
    show: true,
    seller_shop_id: '',
    token: '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    desserts: [
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300300',
        staffInfoPhone: '*********',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '0',
        updateAt: 'กำลังไปรับพัสดุ'
      },
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300181',
        staffInfoPhone: '*********',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '1',
        updateAt: 'กำลังจัดส่ง'
      },
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300202',
        staffInfoPhone: '*********',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '0',
        updateAt: 'กำลังไปรับพัสดุ'
      }
    ],
    headers: [
      { text: 'รหัสการสั่งซื้อ', value: 'reference_id', width: '140', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
      // { text: 'งานรับล่าสุด', value: 'staffInfoId', width: '130', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'ชื่อ - นามสกุล ผู้ซื้อ', filterable: false, value: 'buyer_name', width: '185', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
      // { text: 'รหัสการติดตาม', value: 'order_no', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'อัปเดตล่าสุด', value: 'updated_at', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
      // { text: 'จัดการ', value: 'manage', sortable: false, width: '160', align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'จัดการ', value: 'manages', filterable: false, sortable: false, width: '100', class: 'backgroundTable fontTable--text' }
    ],
    Rules: {
      curiernumber: [
        v => (/^[1-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้นและไม่ติดลบ'
      ]
    }
  }),
  created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
    }
    var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      if (dataDetail.can_use_function_in_shop.manage_tracking === '1') {
        this.getList()
      } else {
        this.$router.push({ path: '/' })
      }
    } else {
      this.$router.push({ path: '/' })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/TackingorderMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Tackingorder' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    OpenTacking (val) {
      window.open(val.url_tracking)
      // window.open('https://mobilysttech-poc.inet.co.th/staging/tracking3pl/tracking3pl?mode=readonly&track=' + val.order_no + '&type=D')
    },
    async getOrderReturn (item) {
      if (item === 0) {
        this.selectMenu = item
        this.data = []
        var Data = {
          // token: this.oneData.user.access_token,
          seller_shop_id: this.seller_shop_id.id,
          status: 'all'
        }
        await this.$store.dispatch('actionGetTackingAll', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrder
        this.data = this.res.data
        this.countorder = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 1) {
        this.selectMenu = item
        this.data = []
        Data = {
          // token: this.oneData.user.access_token,
          seller_shop_id: this.seller_shop_id.id,
          status: 'delivery success'
        }
        await this.$store.dispatch('actionGetTackingAll', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrder
        this.data = this.res.data
        this.countsuccess = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 2) {
        this.selectMenu = item
        this.data = []
        Data = {
          // token: this.oneData.user.access_token,
          seller_shop_id: this.seller_shop_id.id,
          status: 'delivery in progress'
        }
        await this.$store.dispatch('actionGetTackingAll', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrder
        this.data = this.res.data
        this.countwait = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 3) {
        this.selectMenu = item
        this.data = []
        Data = {
          // token: this.oneData.user.access_token
          seller_shop_id: this.seller_shop_id.id,
          status: 'delivery return'
        }
        await this.$store.dispatch('actionGetTackingAll', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrder
        this.data = this.res.data
        this.countreturn = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 4) {
        this.selectMenu = item
        this.data = []
        Data = {
          // token: this.oneData.user.access_token
          seller_shop_id: this.seller_shop_id.id,
          status: 'order in progress'
        }
        await this.$store.dispatch('actionGetTackingAll', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrder
        this.data = this.res.data
        this.countWaitDoing = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      }
      this.page = 1
    },
    async getList () {
      this.data = []
      //  all
      var Data = {
        // token: this.oneData.user.access_token,
        seller_shop_id: this.seller_shop_id.id,
        status: 'all'
      }
      await this.$store.dispatch('actionGetTackingAll', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrder
      this.data = this.res.data
      this.countorder = this.data.length
      // console.log('GetTackingAll', this.res)

      // success
      Data = {
        // token: this.oneData.user.access_token,
        seller_shop_id: this.seller_shop_id.id,
        status: 'delivery success'
      }
      await this.$store.dispatch('actionGetTackingAll', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrder
      this.countsuccess = this.res.data.length

      // waiting
      Data = {
        // token: this.oneData.user.access_token,
        seller_shop_id: this.seller_shop_id.id,
        status: 'delivery in progress'
      }
      await this.$store.dispatch('actionGetTackingAll', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrder
      this.countwait = this.res.data.length

      // return
      Data = {
        // token: this.oneData.user.access_token,
        seller_shop_id: this.seller_shop_id.id,
        status: 'delivery return'
      }
      await this.$store.dispatch('actionGetTackingAll', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrder
      this.countreturn = this.res.data.length
      if (this.data.length === 0 || this.data === undefined) {
        this.disableTable = true
        this.show = false
      } else {
        this.disableTable = false
        this.show = true
      }

      // waiting doing progress
      Data = {
        // token: this.oneData.user.access_token,
        seller_shop_id: this.seller_shop_id.id,
        status: 'order in progress'
      }
      await this.$store.dispatch('actionGetTackingAll', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrder
      this.countWaitDoing = this.res.data.length
      if (this.data.length === 0 || this.data === undefined) {
        this.disableTable = true
        this.show = false
      } else {
        this.disableTable = false
        this.show = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(5) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(5) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style lang="scss">
  .elevation-1  th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
