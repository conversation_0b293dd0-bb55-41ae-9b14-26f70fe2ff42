<template>
  <div>
    <v-container grid-list-xs>
      <CreateShop />
    </v-container>
  </div>
</template>
<script>
export default {
  components: {
    CreateShop: () => import('@/components/Shop/CreateShop/CreateShop')
  },
  created () {
    this.$EventBus.$emit('changeNav', this.SelectPath)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/createShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createShop' }).catch(() => {})
      }
    }
  }
}
</script>
