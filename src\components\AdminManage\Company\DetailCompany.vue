<template>
  <v-container :class="MobileSize ? 'mt-4' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-row dense>
        <v-col cols="12" sm="9" class="py-4">
          <div v-if="!MobileSize" style="font-weight: bold; line-height: 32px; color: #333333;" :style="IpadSize ? 'font-size: 16px;' : 'font-size: 24px;'">รายละเอียดของ{{ detailCompany.name_th }}</div>
          <div v-else style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> รายละเอียดของ{{ detailCompany.name_th }}</div>
        </v-col>
        <v-col cols="12" sm="3" class="pt-3 pr-4" align="end" v-if="IpadSize">
          <v-btn color="#27AB9C" dark rounded @click="editCompany()" text><v-icon color="#27AB9C" class="pl-0">mdi-pencil</v-icon> แก้ไขข้อมูล</v-btn>
        </v-col>
      </v-row>
      <v-card-text class="px-0">
        <v-row dense>
          <v-col cols="12" md="6" sm="12">
            <v-row no-gutters dense>
              <v-col cols="3" md="2" sm="2" class="ml-3">
                <v-avatar
                  style="border: 1px solid #EBEBEB;"
                  rounded
                  size="60"
                  :width="IpadProSize ? '80px' : IpadSize ? '70px' :'80px'"
                  :height="IpadProSize ? '80px' : IpadSize ? '70px' : '80px'"
                >
                  <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain v-if="detailCompany.img_path === null"></v-img>
                  <v-img :src="detailCompany.img_path + '?nochace=' + time" max-width="50" max-height="50" contain v-else></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="8" md="9" sm="9" class="pt-1 ml-3">
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ detailCompany.name_th }}</span><br/>
                <span style="font-weight: 600; font-size: 12px; line-height: 16px; color: #989898;">{{ detailCompany.name_en }}</span><br/>
                <v-row no-gutters>
                  <!-- <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;" class="pr-4"><v-icon color="#27AB9C">mdi-email-outline</v-icon> WWW.Eggs.net</span> -->
                  <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;" class="pr-4"><v-icon color="#27AB9C">mdi-phone-outline</v-icon> {{ detailCompany.tel !== null ? detailCompany.tel : '-'  }}</span>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="6" align="end" class="pr-4" v-if="!IpadSize">
            <v-btn color="#27AB9C" dark rounded @click="editCompany()" outlined ><v-icon color="#27AB9C" class="pr-2">mdi-pencil</v-icon> แก้ไขข้อมูล</v-btn>
          </v-col>
        </v-row>
        <v-row dense class="mx-2">
          <v-col cols="12" md="7" sm="12" class="mb-4">
            <v-card elevation="0" width="100%" height="100%" outlined style="border: 1px solid #EBEBEB; box-sizing: border-box; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
              <v-card-text>
                <v-row dense>
                  <v-col cols="2" md="2" sm="2" class="ml-1">
                    <v-avatar
                      style="background: #F3F5F9;"
                      :width="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                      :height="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                    >
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="40" height="40"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="9" md="9" sm="12" :class="MobileSize ? 'pl-1 mb-2' : 'pt-3 ml-0'">
                    <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ detailCompany.name_th }}</span><br/>
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Company Detail)</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4 mb-4">
                  <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                </v-row>
                <v-row dense>
                  <v-card elevation="0" width="100%" height="100%" style="background: #FAFAFA; border-radius: 8px;">
                    <v-card-text>
                      <span class="textCompany">ชื่อบริษัท (ภาษาไทย) :  <b style="color: #333333;">{{ detailCompany.name_th }}</b></span><br/>
                      <span class="textCompany">ชื่อบริษัท (ภาษาอังกฤษ) : <b style="color: #333333;">{{ detailCompany.name_en }}</b></span><br/>
                      <span class="textCompany">รหัสบริษัท  : <b style="color: #333333;">{{ detailCompany.code }}</b></span><br/>
                      <span class="textCompany">รหัสประจำตัวผู้เสียภาษี  : <b style="color: #333333;">{{ detailCompany.tax_id }}</b></span><br/>
                      <span class="textCompany">หมายเลขโทรศัพท์บริษัท  : <b style="color: #333333;">{{ detailCompany.phone !== null ? detailCompany.phone : '-' }}</b></span><br/>
                      <span class="textCompany">หมายเลขโทรศัพท์มือถือ  : <b style="color: #333333;">{{ detailCompany.tel !== null ? detailCompany.tel : '-' }}</b></span><br/>
                      <span class="textCompany">หมายเลขแฟกซ์  : <b style="color: #333333;">{{ detailCompany.fax !== null ? detailCompany.fax : '-' }}</b></span>
                    </v-card-text>
                  </v-card>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" md="5" sm="12" class="mb-4">
            <v-card elevation="0" width="100%" height="100%" outlined style="border: 1px solid #EBEBEB; box-sizing: border-box; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
              <v-card-text>
                <v-row no-gutters dense>
                  <v-col cols="2" md="2" sm="2" class="ml-1">
                    <v-avatar
                      style="background: #F3F5F9;"
                      :width="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                      :height="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                    >
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/map.png" contain width="40" height="40"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="9" md="9" sm="12" :class="MobileSize ? 'pl-1 mb-3' : 'pt-3 pl-3 mb-3 ml-1'">
                    <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ detailCompany.name_th }}</span><br/>
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Address)</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-2 mb-2">
                  <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                </v-row>
                <v-card elevation="0" width="100%" height="264" style="background: #FAFAFA; border-radius: 8px;" class="mt-4">
                  <v-card-text>
                    <span class="textCompany">ที่อยู่ :  <b style="color: #333333;">{{ detailCompanyAddress.detail }} {{ detailCompanyAddress.sub_district }} {{ detailCompanyAddress.district }} {{ detailCompanyAddress.province }} {{ detailCompanyAddress.zip_code }}</b></span>
                  </v-card-text>
                </v-card>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" md="12" sm="12" class="mb-4">
            <v-card elevation="0" width="100%" height="100%" outlined style="border: 1px solid #EBEBEB; box-sizing: border-box; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
              <v-card-text>
                <v-row dense>
                  <v-col cols="2" md="1" sm="2" class="ml-1">
                    <v-avatar
                      style="background: #F3F5F9;"
                      :width="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                      :height="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                    >
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/industry_1.png" contain width="40" height="40"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="9" md="9" sm="12" :class="MobileSize ? 'pl-1 mb-2' : 'pt-3 ml-0 pl-4'">
                    <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">บุคคล</span><br/>
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Personal)</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4 mb-4">
                  <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                </v-row>
                <v-row dense class="mb-6">
                  <v-card elevation="0" width="100%" height="100%" style="background: #FAFAFA; border-radius: 8px;">
                    <v-card-text>
                      <span class="textCompany">ฝ่ายการเงิน :
                        <v-row no-gutters class="ml-6">
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ชื่อ-สกุล: {{ Name_Finance }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> เบอร์โทร: {{ Phone_Finance }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ตำแหน่ง: {{ Position_Finance }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> อีเมล: {{ Email_Finance }}</span>
                          </v-col>
                        </v-row>
                      </span><br/>
                      <span class="textCompany">ฝ่ายเทคนิค :
                        <v-row no-gutters class="ml-6">
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ชื่อ-สกุล: {{ Name_Technical }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> เบอร์โทร: {{ Phone_Technical }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ตำแหน่ง: {{ Position_Technical }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> อีเมล: {{ Email_Technical }}</span>
                          </v-col>
                        </v-row>
                      </span><br/>
                      <span class="textCompany">หัวหน้าผู้ขอซื้อ  :
                        <v-row no-gutters class="ml-6">
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ชื่อ-สกุล: {{ Name_Buyer }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> เบอร์โทร: {{ Phone_Buyer }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ตำแหน่ง: {{ Position_Buyer }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> อีเมล: {{ Email_Buyer }}</span>
                          </v-col>
                        </v-row>
                      </span><br/>
                      <span class="textCompany">คณะผู้ตรวจรับ 1  :
                        <v-row no-gutters class="ml-6">
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ชื่อ-สกุล: {{ Name_Audit1 }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> เบอร์โทร: {{ Phone_Audit1 }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ตำแหน่ง: {{ Position_Audit1 }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> อีเมล: {{ Email_Audit1 }}</span>
                          </v-col>
                        </v-row>
                      </span><br/>
                      <span class="textCompany">คณะผู้ตรวจรับ 2  :
                        <v-row no-gutters class="ml-6">
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ชื่อ-สกุล: {{ Name_Audit2 }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> เบอร์โทร: {{ Phone_Audit2 }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> ตำแหน่ง: {{ Position_Audit2 }}</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="color: #333333;font-size: 14px;"> อีเมล: {{ Email_Audit2 }}</span>
                          </v-col>
                        </v-row>
                      </span><br/>
                      <!-- <span class="textCompany">คณะกรรมการจัดหา 1  : <b style="color: #333333;">{{ detailCompany.tel !== null ? detailCompany.tel : '-' }}</b></span><br/>
                      <span class="textCompany">คณะกรรมการจัดหา 2  : <b style="color: #333333;">{{ detailCompany.fax !== null ? detailCompany.fax : '-' }}</b></span> -->
                    </v-card-text>
                  </v-card>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      detailCompany: [],
      detailCompanyAddress: '',
      PersonalData: [],
      time: '',
      authority: '',
      Name_Finance: '-',
      Phone_Finance: '-',
      Position_Finance: '-',
      Email_Finance: '-',
      Name_Technical: '-',
      Phone_Technical: '-',
      Position_Technical: '-',
      Email_Technical: '-',
      Name_Buyer: '-',
      Phone_Buyer: '-',
      Position_Buyer: '-',
      Email_Buyer: '-',
      Name_Audit1: '-',
      Phone_Audit1: '-',
      Position_Audit1: '-',
      Email_Audit1: '-',
      Name_Audit2: '-',
      Phone_Audit2: '-',
      Position_Audit2: '-',
      Email_Audit2: '-'
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/detailCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeTitle', 'ข้อมูลบริษัท')
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$on('chackAuthorityCompany', this.chackAuthorityCompany)
    this.$EventBus.$on('getDetailCompany', this.getDetailCompany)
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      if (this.MobileSize) {
        this.chackAuthorityCompanyMobile()
      } else {
        this.chackAuthorityCompany()
      }
      // await this.$store.dispatch('actionsPersonalCompany')
      // this.responsePersonal = await this.$store.state.ModuleAdminManage.statePersonalCompany.data.active[0].json_personal
      this.getTime()
      this.getDetailCompany()
      window.scrollTo(0, 0)
    } else {
      this.router.push({ path: '/' }).catch(() => {})
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    chackAuthorityCompany () {
      this.authority = ''
      this.authority = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      // console.log('testaa', this.authority)
      if (this.authority.can_use_function_in_company.set_company === '1') {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_permission === '1') {
        this.$router.push({ path: '/ManagePosition' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_permission === '1') {
        this.$router.push({ path: '/ManageCompanyPostionUser' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_permission === '1') {
        this.$router.push({ path: '/Partner' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.report === '1') {
        this.$router.push({ path: '/Report' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_company === '1') {
        this.$router.push({ path: '/CompanyCouposAndPoints' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/listApprovePosition' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/ManageBuyerApprove' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/listApproveOrder' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/listApprove' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/specialPriceBuyerRequest' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/QUCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/companyListCreditOrder' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.tracking === '1') {
        this.$router.push({ path: '/TackingCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.refund === '1') {
        this.$router.push({ path: '/refundCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.review === '1') {
        this.$router.push({ path: '/reviewCompany' }).catch(() => {})
      }
    },
    chackAuthorityCompanyMobile () {
      this.authority = ''
      this.authority = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      if (this.authority.can_use_function_in_company.set_company === '1') {
        this.$router.push({ path: '/detailCompanyMobile' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_permission === '1') {
        this.$router.push({ path: '/ManagePosition' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_permission === '1') {
        this.$router.push({ path: '/ManageCompanyPostionUser' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_permission === '1') {
        this.$router.push({ path: '/Partner' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.report === '1') {
        this.$router.push({ path: '/Report' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.set_company === '1') {
        this.$router.push({ path: '/CompanyCouposAndPoints' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/listApprovePosition' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/ManageBuyerApprove' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/listApproveOrder' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.approve_order === '1') {
        this.$router.push({ path: '/listApprove' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/specialPriceBuyerRequest' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/QUCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.order === '1') {
        this.$router.push({ path: '/companyListCreditOrder' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.tracking === '1') {
        this.$router.push({ path: '/TackingCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.refund === '1') {
        this.$router.push({ path: '/refundCompany' }).catch(() => {})
      } else if (this.authority.can_use_function_in_company.review === '1') {
        this.$router.push({ path: '/reviewCompany' }).catch(() => {})
      }
    },
    getTime () {
      this.time = (new Date()).getTime() + Math.round(Math.random())
      // return (new Date()).getTime() + Math.round(Math.random())
    },
    async getDetailCompany () {
      this.$store.commit('openLoader')
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      // console.log('companyData ==========>', companyData)
      var data = {
        company_id: companyData.id
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      // console.log('detail ===========>', response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.detailCompany = await response.data
        this.detailCompanyAddress = await response.data.company_address
        // await this.$store.dispatch('actionsPersonalCompany')
        this.responsePersonal = await response.data.json_personal
        if (this.responsePersonal !== null) {
          // Finance
          this.Name_Finance = this.responsePersonal[0].financial[0].name
          this.Phone_Finance = this.responsePersonal[0].financial[0].phone
          this.Position_Finance = this.responsePersonal[0].financial[0].position
          this.Email_Finance = this.responsePersonal[0].financial[0].email
          // Technical
          this.Name_Technical = this.responsePersonal[0].technique[0].name
          this.Phone_Technical = this.responsePersonal[0].technique[0].phone
          this.Position_Technical = this.responsePersonal[0].technique[0].position
          this.Email_Technical = this.responsePersonal[0].technique[0].email
          // purchasing_chief
          this.Name_Buyer = this.responsePersonal[0].purchasing_chief[0].name
          this.Phone_Buyer = this.responsePersonal[0].purchasing_chief[0].phone
          this.Position_Buyer = this.responsePersonal[0].purchasing_chief[0].position
          this.Email_Buyer = this.responsePersonal[0].purchasing_chief[0].email
          // inspectors_one
          this.Name_Audit1 = this.responsePersonal[0].inspectors_one[0].name
          this.Phone_Audit1 = this.responsePersonal[0].inspectors_one[0].phone
          this.Position_Audit1 = this.responsePersonal[0].inspectors_one[0].position
          this.Email_Audit1 = this.responsePersonal[0].inspectors_one[0].email
          // inspectors_two
          this.Name_Audit2 = this.responsePersonal[0].inspectors_two[0].name
          this.Phone_Audit2 = this.responsePersonal[0].inspectors_two[0].phone
          this.Position_Audit2 = this.responsePersonal[0].inspectors_two[0].position
          this.Email_Audit2 = this.responsePersonal[0].inspectors_two[0].email
        }
        // console.log('this.responsePersonal', this.responsePersonal)
      } else if (response.message === 'This user is unauthorized') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'warning',
          text: 'มีคนใช้ผู้ใช้งานนี้อยู่ขณะนี้ กรุณาล็อกอินใหม่อีกครั้ง',
          showConfirmButton: false,
          timer: 1500
        })
        localStorage.removeItem('oneData')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'warning',
            text: 'ไม่พบบริษัทนี้ กรุณาตรวจสอบใหม่อีกครั้ง',
            showConfirmButton: false,
            timer: 1500
          })
          this.router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    editCompany () {
      localStorage.setItem('DetailCompany', Encode.encode(this.detailCompany))
      if (this.MobileSize) {
        this.$router.push({ path: '/manageCompanyMobile?Status=Edit' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageCompany?Status=Edit' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.cardDetailCompany {
  border-radius: 4px;
  border: 2px solid lightgrey;
}
.textCompany {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #636363;
}
.headCompany {
  /* justify-content: end;
  display: flex; */
  padding-right: 50px;
}
</style>
