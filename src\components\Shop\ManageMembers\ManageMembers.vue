<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">จัดการ Member ร้านค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> จัดการ Member ร้านค้า
      </v-card-title>

      <v-col cols="12">
        <v-row style="display: flex; align-items: center;">
          <v-col cols="7">
            <v-icon>mdi-circle-medium</v-icon>
            <span style="font-size: 16px;">List Member ของร้านค้า</span>
          </v-col>
          <v-col cols="5" class="text-end" v-if="listMembers.length === 0">
            <v-btn rounded color="#27AB9C" style="color: white; font-size: 16px; text-transform: none;" @click="AddMembers()">
              เพิ่ม Member
            </v-btn>
          </v-col>
          <v-col cols="5" class="text-end" v-else>
            <v-btn rounded color="#27AB9C" style="color: white; font-size: 16px; text-transform: none;" @click="EditMembers()">
              แก้ไข Member
            </v-btn>
          </v-col>
        </v-row>
      </v-col>

      <v-col cols="12" v-if="listMembers.length === 0">
        <v-card elevation="0" class="pa-4" style="background-color: #f9f9f9;">
          <v-img
            src="@/assets/Members/Members2.png"
            contain
            max-height="200"
            class="mb-4"
          />
          <div class="text-center">
            <span style="font-size: 16px;">ยังไม่ได้เปิดการใช้งาน โปรดทำการเพิ่มเพื่อเปิดการใช้งาน</span>
          </div>
        </v-card>
      </v-col>

      <v-col cols="12" v-else>
        <div class="pb-3">
          <span style="font-size: 16px;">รายการ List Member ทั้งหมด {{ this.listMembers.length }} รายการ</span>
        </div>
        <v-card elevation="1">
          <v-data-table
            :headers="headersList"
            :items="listMembers"
            hide-default-footer
          >
            <template v-slot:[`item.level_rank`]="{ item }">
              <span>{{ item.level_rank }}</span>
            </template>
            <template v-slot:[`item.name`]="{ item }">
              <span>{{ item.name }}</span>
            </template>
            <template v-slot:[`item.min_total_purchase`]="{ item }">
              <span>{{ Number(item.min_total_purchase).toLocaleString() }}</span>
            </template>
            <template v-slot:[`item.total_users`]="{ item }">
              <span>{{ Number(item.total_users).toLocaleString() }}</span>
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-btn text rounded color="#27AB9C" small @click="DialogDetail(item)" v-if="item.total_users !== 0">
                <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                <b>ดูสมาชิก</b><v-icon small>mdi-chevron-right</v-icon>
              </v-btn>
            </template>
          </v-data-table>
        </v-card>
        <div class="pt-3 text-end">
          <span style="font-size: 16px;"><v-icon color="#FF0000">mdi mdi-information</v-icon><b style="color: #FF0000">หมายเหตุ :</b> ระยะเวลาการสะสมยอด {{ this.startDate }} ถึง {{ this.endDate }}</span>
        </div>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogDetail" max-width="500px" persistent>
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDetail = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>รายชื่อยอดสะสม ระดับ {{ dialogLevelName }}</b></span>
        </v-card-title>
        <br>
        <v-card-text class="pa-5 pt-0">
          <v-card elevation="1">
            <v-data-table
              :headers="headersDetail"
              :items="memberDetail"
            >
              <template v-slot:[`item.name`]="{ item }">
                <span>{{ item.name }}</span>
              </template>
              <template v-slot:[`item.total`]="{ item }">
                <span>{{ Number(item.total).toLocaleString() }}</span>
              </template>
            </v-data-table>
          </v-card>
        </v-card-text>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      sellerShopID: '',
      listMembers: [],
      // listMembers: [
      //   {
      //     level: 'Silver',
      //     minimumAmount: 1000,
      //     memberCount: 2,
      //     members: [
      //       { name: 'สมชาย', amount: 1200 },
      //       { name: 'วิไล', amount: 1500 }
      //     ]
      //   },
      //   {
      //     level: 'Gold',
      //     minimumAmount: 3000,
      //     memberCount: 1,
      //     members: [
      //       { name: 'ณรงค์', amount: 3500 }
      //     ]
      //   },
      //   {
      //     level: 'Platinum',
      //     minimumAmount: 5000,
      //     memberCount: 0,
      //     members: []
      //   }
      // ],
      headersList: [
        { text: 'ลำดับ', value: 'level_rank', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '50' },
        { text: 'ระดับ', value: 'name', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'ยอดขั้นต่ำ', value: 'min_total_purchase', sortable: true, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '50' },
        { text: 'จำนวนคน', value: 'total_users', sortable: true, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '50' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '50' }
      ],
      headersDetail: [
        { text: 'ชื่อ', value: 'name', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'ยอด', value: 'total', sortable: true, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '50' }
      ],
      dialogDetail: false,
      memberDetail: [],
      dialogLevelName: '',
      startDate: '',
      endDate: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageMembersMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageMembers' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.sellerShopID = sellerShopID
    this.ListMemberLevelRank()
  },
  methods: {
    backToMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    AddMembers () {
      if (this.MobileSize) {
        this.$router.push({ path: '/AddMembersMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/AddMembers' }).catch(() => {})
      }
    },
    EditMembers () {
      if (this.MobileSize) {
        this.$router.push({ path: '/EditMembersMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/EditMembers' }).catch(() => {})
      }
    },
    async ListMemberLevelRank () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.sellerShopID
      }
      await this.$store.dispatch('actionsListMemberLevelRank', data)
      var responseData = await this.$store.state.ModuleMember.stateListMemberLevelRank
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listMembers = responseData.data.list
        this.startDate = responseData.data.start_date
        this.endDate = responseData.data.end_date
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    async DialogDetail (item) {
      this.$store.commit('openLoader')
      this.dialogDetail = true
      this.dialogLevelName = item.name
      var data = {
        seller_shop_id: this.sellerShopID,
        level_rank: item.level_rank
      }
      await this.$store.dispatch('actionsListMemberRanked', data)
      var responseData = await this.$store.state.ModuleMember.stateListMemberRanked
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.memberDetail = responseData.data
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    }
  }
}
</script>

<style>

</style>
