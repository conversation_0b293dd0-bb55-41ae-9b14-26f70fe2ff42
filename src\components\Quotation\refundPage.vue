<template>
  <v-container  :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
        <v-row dense :class="MobileSize ? 'mx-2 my-2' : 'mx-1'">
          <v-col cols="12" class="d-flex">
            <span :style="MobileSize ? 'font-size: 18px; ' : IpadSize ? 'font-size: 18px;' : 'font-size: 24px; '" class="mr-auto" style="font-weight: 700; color: #333333;" @click="backtoPage()">
              <v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> รายการคืนสินค้า
            </span>
          </v-col>
          <v-row class="d-flex justify-center mt-5" no-gutters>
            <v-col cols="7">
              <v-card elevation="3" width="100%" height="285px" outlined style="border:2px solid; border-radius: 10px; border-color: #38b2a4">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="12" md="12" sm="12">
                        <v-row>
                          <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                            <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">รหัสการสั่งซื้อ : </span>
                            <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">AHH-5544544665455</span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                            <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">อีเมลในการติดต่อ : </span>
                            <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''"><EMAIL></span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                            <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เบอร์โทรศัพท์ในการติดต่อ : </span>
                            <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">0650370797</span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                            <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ชื่อธนาคาร : </span>
                            <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">กรุงไทย</span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                            <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ชื่อบัญชี : </span>
                            <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">สหรัฐ เชิดสุข</span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                            <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เลขที่บัญชี : </span>
                            <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">0465654654646</span>
                          </v-col>
                        </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="3"  class="d-flex flex-column ml-10" style="gap: 1vw; justify-content: end">
              <v-card elevation="3" width="250px" height="135px" outlined style="border:2px solid; border-radius: 10px; border-color: #38b2a4" class="d-flex align-center">
                <v-card-text class="text-center">
                  <v-row dense>
                    <v-col cols="12">
                      <span style="font-size: 24px; font-weight: 600; color: green;"><v-icon color="green">mdi-circle-medium</v-icon>จัดส่งสำเร็จ</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 18px; font-weight: 600; color: #a0a0a0;">สถานะสินค้า</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <v-card elevation="3" width="250px" height="135px" outlined style="border:2px solid; border-radius: 10px; border-color: #38b2a4" class="d-flex align-center">
                <v-card-text class="text-center">
                  <v-row dense>
                    <v-col cols="12">
                      <span style="font-size: 24px; font-weight: 600; color: red;"><v-icon color="red">mdi-circle-medium</v-icon>คืนเงินไม่สำเร็จ</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 18px; font-weight: 600; color: #a0a0a0;">สถานะคืนเงิน</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-col cols="12" class="mt-10">
            <v-card style="border-radius: 10px ;">
              <v-data-table :headers="headers" :items="dataTable">
                <template v-slot:[`item.detail`]="{ item }">
                  <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          width="30"
                          height="30"
                          v-bind="attrs"
                          v-on="on"
                          style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          outlined icon @click="showDialog(item)">
                          <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                        </v-btn>
                      </template>
                      <span>รายละเอียดสินค้าที่คืนคืน</span>
                  </v-tooltip>
                  <v-btn small text rounded color="#27AB9C" @click="showDialog(item)">
                    <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
                  </v-btn>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
          <v-col class="d-flex justify-end mt-5" style="gap: 10px;">
            <v-btn rounded color="#38b2a4" class="white--text" style="font-size: 16px">อนุมัติ</v-btn>
            <v-btn rounded color="red" class="white--text" style="font-size: 16px" @click="cancelRefund()">ไม่อนุมัติ</v-btn>
          </v-col>
          <!-- <v-card>
           <v-row>
             <v-data-table :headers="headers" :items="dataTable"></v-data-table>
            </v-row>
          </v-card> -->
        </v-row>
    </v-card>
    <!-- dialog ยกเลิก -->
    <v-dialog v-model="cancelRefundDialog" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ไม่อนุมัติคำขอคืนสินค้า/เงิน</b></span>
              </v-col>
              <v-btn fab small @click="cancelRefundDialog = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">AHH-84849849849</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">ซาแบงค์ นักเตะขาดำ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">12 ธันวาคม 2567 เวลา 18:19น.</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reason" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelRefundDialog = false">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" :disabled="reason !== '' ? false : true" height="40" class="white--text">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  // components: {
  //   refundDetailModal: () => import(/* webpackPrefetch: true */ '@/components/Quotation/refundDetailModal')
  // },
  data () {
    return {
      reason: '',
      cancelRefundDialog: false,
      showDialogDetails: false,
      headers: [
        { text: 'รหัส SKU', value: 'id', width: '170', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียดสินค้า', value: 'name', width: '170', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'pricePer', width: '120', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำวนวน', value: 'amount', width: '120', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'summary', sortable: false, width: '120', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาคืน', value: 'refund', sortable: false, width: '120', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนสินค้าที่คืน', value: 'amountReturn', sortable: false, width: '150', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'detail', sortable: false, width: '180', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
      ],
      dataTable: [
        { id: 1, name: 'Voke Saharat', pricePer: 100, amount: 3, summary: 300, refund: 300, amountReturn: 2, detail: 'details' },
        { id: 2, name: 'Sa Bank', pricePer: 100, amount: 3, summary: 300, refund: 300, amountReturn: 2, detail: 'details' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    showDialog (item) {
      this.showDialogDetails = true
      // console.log(item)
    },
    backtoPage () {
      var orderNumber = localStorage.getItem('orderNumber')
      var transactionNumber = localStorage.getItem('transactionNumber')
      var termNumber = localStorage.getItem('termNumber')
      if (this.MobileSize) {
        this.$router.push({ path: `/posellerDetailMobile?orderNumber=${orderNumber}&tranNumber=${transactionNumber}&termNumber=${termNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/POSellerDetail?orderNumber=${orderNumber}&tranNumber=${transactionNumber}&termNumber=${termNumber}` }).catch(() => {})
      }
    },
    cancelRefund () {
      this.cancelRefundDialog = true
    }
  }
}
</script>

<style>

</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(8) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(8) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
