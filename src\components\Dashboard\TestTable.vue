<template>
  <div>
    <v-row
    class="justify-start ml-2 mb-2"
    >
    <v-col>
    <v-tabs>
    <v-tab>ทั้งหมด</v-tab>
    <v-tab>รออนุมัติ</v-tab>
    <v-tab>อนุมัติ</v-tab>
    <v-tab>ไม่อนุมัติ</v-tab>
  </v-tabs>
      </v-col>
    </v-row>
      <v-row
        style=""
        class="justify-start ml-0 mt-0 mb-2"
      >
        <v-col cols="8" class="ml-6 pr-0">
          <v-text-field
            outlined
            rounded
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
        <v-col cols="4">
        </v-col>
      </v-row>
  <v-data-table
          :headers="dataMain['headers']"
          :items="dataMain['data']"
          color="#D4F1E4"
          hide-default-footer
          hide-default-header
    >
     <template v-slot:header="{ props: { headers } }">
        <thead>
          <tr>
            <th v-for="h in headers" :class="h.class" :key="h.code">
              <span>{{h.text}}</span>
            </th>
          </tr>
        </thead>
    </template>
          <template
            v-slot:[`item.date`]="{ item: { date } = {} }">{{ date }}
          </template>
           <template
            v-slot:[`item.code`]="{ item: { code } = {} }">{{ code }}
          </template>
            <template
            v-slot:[`item.price`]="{ item: { price } = {} }">{{ price }}
          </template>
            <template
            v-slot:[`item.paymentDate`]="{ item: { paymentDate } = {} }">{{ paymentDate }}
          </template>
           <template
            v-slot:[`item.status`]="{ item: { status } = {} }">
            <v-chip
            :style="status === '1' ? 'color: #E9A016': status === '2' ? 'color: #42B971': status === '3' ? 'color: #FF7C9C' : ''"
            class="ma-2"
            :color="status === '1' ? '#FCF0DA' : status === '2' ? '#F0F9EE': status === '3' ? '#F7D9D9' : ''"
            >
            {{status === '1' ? 'รออนุมัติ' : status === '2' ? 'อนุมัติ' : status === '3' ? 'ไม่อนุมัติ' : ''}}
          </v-chip>
          </template>
            <template
            v-slot:[`item.detail`]="{ item }">
            <div
            v-if="item"
            style="color: #27AB9C"
            >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
          </template>
    </v-data-table>
  </div>
</template>
<script>
import dataMap from '../library/TestTable.json'
export default {
  data () {
    return {
      dataMain: dataMap
    }
  }
}
</script>

<style>
.header-style {
  background: #D4F1E4 !important;
}
.status-1 {
  color: #E9A016;
}
</style>
