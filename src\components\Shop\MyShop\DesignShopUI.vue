<template lang="html">
  <v-container grid-list-xs>
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
    <v-row dense class="mb-6 mt-0 ">
      <v-col cols="12" md="12" class="pa-3">
        <v-img
          src="@/assets/ImageINET-Marketplace/ICONShop/Banner_Store.png"
          contain
        ></v-img>
      </v-col>
    </v-row>
    <v-row cols="12" md="12" class="pa-3">
      <v-icon v-if="MobileSize || IpadSize " @click="Cancle()" color="#27AB9C" class="mb-0 ml-2">mdi-chevron-left</v-icon>
      <span
        style="font-weight: bold; font-size: 28px; line-height: 40px;"
        class="ml-2"
        >ออกแบบร้านค้า</span
      >
      <v-icon color="#27AB9C" class="mb-0 ml-2"
        >mdi-alert-circle-outline</v-icon
      >
    </v-row>
    <v-row no-gutters align="center">
      <!-- รูปภาพสินค้า -->
      <v-col cols="12" md="12" class="pa-3">
        <v-card
          outlined
          id="step-1"
          style="background: #FAFAFA; border-radius: 8px;"
        >
          <v-col cols="12" class="mt-2 pa-3">
            <v-row>
              <h2
                class="pt-1 ml-2 mt-2 mb-4"
                style="font-weight: 700; font-size: 24px;"
              >
                รูปภาพร้านค้า
              </h2>
              <v-spacer
                style="border-top: 1px solid #E6E6E6; margin-top: 24px; margin-left: 14px;"
              ></v-spacer>
              <v-btn
                icon
                outlined
                style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;"
                class="mt-1"
                ><v-icon color="#27AB9C" @click="reloadpage()">mdi-file-image</v-icon></v-btn
              >
            </v-row>
          </v-col>
          <!-- Draggable -->
          <v-col cols="12" md="12" class="mt-6">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #FFFFFF; border-radius: 8px;"
            >
              <v-card-text>
                <v-card
                  elevation="0"
                  style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                   @click="onPickFile()"
                >
                  <v-card-text>
                    <v-row
                      no-gutters
                      align="center"
                      justify="center"
                      style="cursor: pointer;"
                    >
                      <v-file-input
                        v-model="DataImage"
                        :items="DataImage"
                        accept="image/jpeg, image/jpg, image/png"
                        @change="UploadImage()"
                        id="file_input"
                        multiple
                        :clearable="false"
                        style="display:none"
                      >
                      </v-file-input>
                      <v-col cols="12" md="12" class="mb-6">
                        <v-row justify="center" class="pt-10">
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                            width="280.34"
                            height="154.87"
                            contain
                          ></v-img>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" class="mt-6">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="4" style="text-align: center;">
                            <span
                              style="font-size: 16px; line-height: 24px; font-weight: 400;"
                              >เพิ่มรูปภาพของคุณที่นี่</span
                            ><br />
                            <span
                              style="font-size: 16px; line-height: 24px; font-weight: 400;"
                              >หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span
                            ><br />
                            <span
                              style="font-size: 12px; line-height: 16px; font-weight: 400;"
                              >(ไฟล์นามสกุล .JPEG, .PNG)</span
                            ><br />
                            <span
                              style="font-size: 12px; line-height: 16px; font-weight: 400;"
                              ><span style="color: red;">***</span> หมายเหตุ
                              ไฟล์รูปควรมีขนาดไม่เกิน 8 MB</span
                            >
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
                 <div v-if="Detail.product_image.length !== 0" class="mt-4">
                  <draggable v-model="Detail.product_image"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                    <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="12" md="2">
                      <v-card  v-if="item.type === 'image'" outlined class="pa-1" width="146" height="146" >
                        <v-img  :src="item.path" :lazy-src="item.url" width="130" height="130" contain>
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                            <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                          </v-btn>
                        <!-- <v-img  :src="item.path" :lazy-src="item.url" width="130" height="130" contain> -->
                        </v-img>
                      </v-card>
                      <!-- <v-card v-else outlined  class="pa-1" width="146" height="146"  >
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                            <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                          </v-btn>
                        <video    autoplay loop muted playsinline  >
                          <source :src="item.length ? item.path + '?=' + `${currentTime.getTime()}`:item.path" type="video/mp4" >
                        </video>
                      </v-card> -->
<!-- <source :src="`${item.path}?=${currentTime.getTime()} `" type="video/mp4" > -->
<!-- <v-img :src="`${PathImage}${imageBase} ?=${currentTime.getTime()} `" v-if="imageBase !== '' && chackPicChange === false"></v-img> -->
                    </v-col>
                  </draggable>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-row no-gutters>
            <v-col cols="12" md="12" class="pa-3">
              <v-col cols="12" md="12">
                <span class="f-left" style="font-weight: 700; font-size: 24px;"
                  >ชื่อร้านค้าภาษาไทย
                </span>
              </v-col>
              <v-col cols="12" md="12" class="pr-5">
                <v-text-field
                v-model="Detail.shop_name_th"
                @keypress="isLetterThai($event)"
                :rules="Rules.textnameth"
                  outlined
                  dense
                  placeholder="ระบุชื่อร้านเป็นภาษาไทยหรือภาษาอังกฤษ"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <span class="f-left" style="font-weight: 700; font-size: 24px;"
                  >ชื่อร้านค้าภาษาอังกฤษ
                </span>
              </v-col>
              <v-col cols="12" md="12" class="pr-5">
                <v-text-field
                  v-model="Detail.shop_name_en"
                  @keypress="isLetterEng($event)"
                  :rules="Rules.textnameeng"
                  outlined
                  dense
                  placeholder="ระบุชื่อร้านเป็นภาษาไทยหรือภาษาอังกฤษ"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <span class="f-left" style="font-weight: 700; font-size: 24px;"
                  >รายละเอียดร้านค้า
                </span>
              </v-col>
              <v-col cols="12" md="12" class="pr-5">
                <v-textarea
                v-model="Detail.shop_description"
                  outlined
                  name="input-7-4"
                  value=""
                  placeholder="ระบุรายละเอียดร้านค้า"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <span class="f-left" style="font-weight: 700; font-size: 24px;"
                  >ที่อยู่ร้านค้า
                </span>
              </v-col>
              <v-col cols="12" md="12" class="pr-5">
               <v-card color="#FAFAFA" style="border: 1px solid #BEBEBE" elevation="0">
                 <v-container style="margin:2px;margin-Top:5px;margin-Bottom:10px">
               <v-form ref="FormAddress" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="4">
                    <span>เลขที่<span style="color: red;font-size:12"> *</span></span>
                  </v-col>
                  <v-col cols="4">
                    <span>ห้องเลขที่</span>
                  </v-col>
                  <v-col cols="4">
                    <span>ชั้นที่</span>
                  </v-col>
                  <v-col cols="4" class="pr-5">
                    <v-text-field class="input_text" placeholder="ระบุเลขที่อยู่" outlined dense v-model="house_no" :rules="Rules.house_no"></v-text-field>
                  </v-col>
                  <v-col cols="4" class="pr-5">
                    <v-text-field class="input_text" placeholder="ระบุเลขห้อง" outlined dense v-model="room_no"></v-text-field>
                  </v-col>
                  <v-col cols="4">
                    <v-text-field class="input_text" placeholder="ระบุชั้น" outlined dense v-model="floor"></v-text-field>
                  </v-col>
                  <v-col cols="12"></v-col>
                  <v-col cols="6">
                    <span>อาคาร</span>
                  </v-col>
                  <v-col cols="6">
                    <span>หมู่บ้าน</span>
                  </v-col>
                  <v-col cols="6" class="pr-5">
                    <v-text-field class="input_text" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" outlined dense v-model="building_name"></v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field class="input_text" placeholder="ชื่อหมู่บ้าน" outlined dense v-model="moo_ban"></v-text-field>
                  </v-col>
                  <v-col cols="3">
                    <span>หมู่ที่</span>
                  </v-col>
                  <v-col cols="3">
                    <span>ตรอก/ซอย</span>
                  </v-col>
                  <v-col cols="3">
                    <span>แยก</span>
                  </v-col>
                  <v-col cols="3">
                    <span>ถนน</span>
                  </v-col>
                  <v-col cols="3" class="pr-5">
                    <v-text-field class="input_text" placeholder="ระบุหมู่" outlined dense v-model="moo_no"></v-text-field>
                  </v-col>
                  <v-col cols="3" class="pr-5">
                    <v-text-field class="input_text" placeholder="ระบุตรอก,ซอย" outlined dense v-model="soi"></v-text-field>
                  </v-col>
                  <v-col cols="3" class="pr-5">
                    <v-text-field class="input_text" placeholder="ระบุแยก" outlined dense v-model="yaek"></v-text-field>
                  </v-col>
                  <v-col cols="3">
                    <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street"></v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <span>แขวง/ตำบล</span>
                  </v-col>
                  <v-col cols="6">
                    <span>เขต/อำเภอ</span>
                  </v-col>
                  <v-col cols="6" class="pr-5">
                    <addressinput-subdistrict class="input_text-thai-address" :rules="Rules.empty" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                  </v-col>
                  <v-col cols="6">
                    <addressinput-district class="input_text-thai-address" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                  </v-col>
                  <v-col cols="6">
                    <span>จังหวัด</span>
                  </v-col>
                  <v-col cols="6">
                    <span>รหัสไปรษณีย์</span>
                  </v-col>
                  <v-col cols="6" class="pr-5">
                    <addressinput-province class="input_text-thai-address" label="" v-model="province" placeholder="ระบุจังหวัด" />
                  </v-col>
                  <v-col cols="6">
                    <addressinput-zipcode class="input_text-thai-address" label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                  </v-col>
                </v-row>
              </v-form>
              </v-container>
              </v-card>
              </v-col>
            </v-col>
          </v-row>
          <v-card-actions>
            <v-row no-gutters justify="end" align="center" class="mt-4 mb-5">
              <v-btn
                @click="Cancle()"
                outlined
                color="#27AB9C"
                class="pl-7 pr-7"
                >ยกเลิก</v-btn
              >
              <v-btn
                color="#27AB9C"
                dark
                class="ml-4 pl-8 pr-8"
                id="confirmcreateproduct"
                @click="Confirm()"
                >ตกลง</v-btn
              >
            </v-row>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
    </v-card>
  </v-container>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import draggable from 'vuedraggable'
import { Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  components: { draggable },
  data () {
    return {
      DataImage: [],
      currentTime: new Date(),
      shop_id: '-',
      Detail: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      shop_media: [],
      dataEditAddress: [],
      data: [],
      lazy: false,
      first_name: '',
      last_name: '',
      phone: '',
      detail: '',
      EditaddressDialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      house_no: '',
      room_no: '',
      floor: '',
      building_name: '',
      moo_ban: '',
      moo_no: '',
      soi: '',
      yaek: '',
      street: '',
      id: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        textnameth: [
          v => /^[ก-๏\s,0-9]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        textnameeng: [
          v => /^[A-Za-z_@.,/#&+-\s,0-9]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        first_name: [
          v => !!v || 'กรุณากรอกชื่อจริง'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุล'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่'
        ],
        room_no: [
          v => !!v || 'กรุณาระบุห้องเลขที่'
        ],
        floor: [
          v => !!v || 'กรุณาระบุชั้นที่'
        ],
        building_name: [
          v => !!v || 'กรุณาระบุอาคาร'
        ],
        moo_ban: [
          v => !!v || 'กรุณาระบุหมู่บ้าน'
        ],
        moo_no: [
          v => !!v || 'กรุณาระบุหมู่ที่'
        ],
        soi: [
          v => !!v || 'กรุณาระบุตรอก/ซอย'
        ],
        yaek: [
          v => !!v || 'กรุณาระบุแยก'
        ],
        street: [
          v => !!v || 'กรุณาระบุถนน'
        ]
      }
    }
  },
  watch: {
    checkAddress (val) {
      this.EditaddressDialog = val
    },
    subdistrict (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
        } else {
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
        } else {
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
        } else {
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
        } else {
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.getDetailShop()
  },
  methods: {
    reloadpage () {
      window.location.reload()
    },
    isLetterThai (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[ก-๏\s,0-9]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    isLetterEng (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z_@.,/#&+-\s,0-9]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    async getDetailShop () {
      // await this.$store.dispatch('actionsGetShopData')
      // var response = await this.$store.state.ModuleShop.stateShopData
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // alert(response.data[0].seller_shop_id)
      const shopname = localStorage.getItem('shopDetail')
      const id = JSON.parse(shopname)
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.shop_id = id.id
      // var data = {
      //   seller_shop_id: id.id
      // }
      // var data2 = {
      //   seller_shop_id: response.data[0].seller_shop_id
      // }
      // this.shop_id = response.data[0].seller_shop_id
      // await this.$store.dispatch('actionsShopDetailPage', data)
      // var responseShop = await this.$store.state.ModuleShop.stateShopDetailPage
      // console.log('response Shop Detail Page =======>', responseShop)
      // this.Detail.shop_name_en = responseShop.data.shop_name_en
      // this.Detail.shop_name_th = responseShop.data.shop_name_th
      // this.Detail.product_image = responseShop.data.shop_media
      // await this.$store.dispatch('actionsgetUpdateShopAddress', data)
      // var responseShopUP = await this.$store.state.ModuleShop.stategetUpdateshopaddress
      // this.Detail.shop_name_en = responseShopUP.data.shop_name_en
      // this.Detail.shop_name_th = responseShopUP.data.shop_name_th
      // this.Detail.shop_description = responseShopUP.data.shop_description
      // this.house_no = responseShopUP.data.house_no
      // this.district = responseShopUP.data.district
      // this.subdistrict = responseShopUP.data.sub_district
      // this.province = responseShopUP.data.province
      // this.zipcode = responseShopUP.data.zipcode

      // this.address_detail = responseShopUP.data.address_detail.split('/')
      // this.room_no = this.address_detail[0]
      // this.floor = this.address_detail[1]
      // this.building_name = this.address_detail[2]
      // this.moo_ban = this.address_detail[3]
      // this.moo_no = this.address_detail[4]
      // this.soi = this.address_detail[5]
      // this.yaek = this.address_detail[6]
      // this.street = this.address_detail[7]
      var a = {
        seller_shop_id: id.id,
        role: dataRole.role
      }
      await this.$store.dispatch('actionsShopDetailPage', a)
      var responseShopUP = await this.$store.state.ModuleShop.stateShopDetailPage
      this.Detail.shop_name_en = responseShopUP.data.shop_name_en
      this.Detail.shop_name_th = responseShopUP.data.shop_name_th
      this.Detail.shop_description = responseShopUP.data.shop_description
      this.house_no = responseShopUP.data.shop_address.house_no
      this.district = responseShopUP.data.shop_address.district
      this.subdistrict = responseShopUP.data.shop_address.sub_district
      this.province = responseShopUP.data.shop_address.province
      this.zipcode = responseShopUP.data.shop_address.zip_code

      this.address_detail = responseShopUP.data.shop_address.address_detail.split('/')
      this.room_no = this.address_detail[0]
      this.floor = this.address_detail[1]
      this.building_name = this.address_detail[2]
      this.moo_ban = this.address_detail[3]
      this.moo_no = this.address_detail[4]
      this.soi = this.address_detail[5]
      this.yaek = this.address_detail[6]
      this.street = this.address_detail[7]
      for (let i = 0; i < responseShopUP.data.shop_media.length; i++) {
        var sent = {
          path: responseShopUP.data.shop_media[i].path,
          name: 'default',
          type: 'image',
          id: responseShopUP.data.shop_media[i].id
        }
        this.Detail.product_image.push(sent)
      }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    Cancle () {
      if (!this.MobileSize) {
        // this.$router.push({ path: '/seller?ShopID=' + this.onedata.user.list_shop_detail[0].seller_shop_id + '&ShopName=' + this.onedata.user.list_shop_detail[0].shop_name_th })
        this.Detail.product_image = []
        this.getDetailShop()
        window.scrollTo(0, 0)
      } else {
        this.$router.push({ path: '/sellerMobile?ShopID=' + this.onedata.user.list_shop_detail[0].seller_shop_id + '&ShopName=' + this.onedata.user.list_shop_detail[0].shop_name_th })
      }
    },
    UploadImage () {
      // console.log(this.DataImage, 'this.DataImage')
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      // this.Detail.product_image = []
      if (this.Detail.product_image.length < 5) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          if (imageSize < 2) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = () => {
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              this.Detail.product_image.push({
                image_data: resultReader.split(',')[1],
                path: url,
                name: this.DataImage[i].name,
                type: (this.DataImage[i].type.split('/', 1)).toString()
              })
              // console.log(this.Detail.product_image, 'this.Detail.product_image')
              // mediaType = this.DataImage[i].type
              // var checkType = mediaType.split('/', 1)
              // if (checkType.toString() === 'video') {
              //   checkType = 'vdo'
              // } else {
              //   checkType = 'image'
              // }
              // this.shop_media.push({
              //   media: element,
              //   media_type: checkType
              // })
              // // console.log(this.shop_media, 'this.shop_media')
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
              showConfirmButton: false,
              timer: 1500
            })
          }
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 5 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
      // console.log('me', this.Detail.product_image)
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.product_image.splice(index, 1)
      } else {
        this.Detail.product_image.splice(index, 1)
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    async Confirm () {
      // console.log('media', this.Detail.product_image)
      // console.log('tong', this.Detail.product_image)
      // console.log(this.Detail, 'this.Detail')
      // console.log(this.Detail.product_image, 'this.Detail.product_image')
      // var data = {
      //   seller_shop_id: this.shop_id,
      //   shop_name_th: this.Detail.shop_name_en,
      //   shop_name_en: this.Detail.shop_name_th,
      //   shop_media: this.shop_media
      // }
      if (this.province === undefined) {
        this.province = '-'
      }
      const formData = new FormData()
      formData.append('seller_shop_id', this.shop_id)
      formData.append('shop_name_th', this.Detail.shop_name_th)
      formData.append('shop_name_en', this.Detail.shop_name_en)
      formData.append('shop_description', this.Detail.shop_description)
      formData.append('house_no', this.house_no)
      formData.append('address_detail', this.room_no + '/' + this.floor + '/' + this.building_name + '/' + this.moo_ban + '/' + this.moo_no + '/' + this.soi + '/' + this.yaek + '/' + this.street)
      formData.append('province_name', this.province)
      formData.append('district_name', this.district)
      formData.append('city_name', this.subdistrict)
      formData.append('postal_code', this.zipcode)
      // console.log(formData)
      // if (this.shop_media.length !== 0) {
      //   for (let i = 0; i < this.shop_media.length; i++) {
      //     formData.append(`shop_media[${i}][media]`, this.shop_media[i].media)
      //     formData.append(`shop_media[${i}][media_type]`, this.shop_media[i].media_type)
      //   }
      // } else {
      //   formData.append('shop_media[0][media]', JSON.stringify(this.shop_media))
      //   formData.append('shop_media[0][media_type]', JSON.stringify(this.shop_media))
      // }
      var list = []
      for (let i = 0; i < this.Detail.product_image.length; i++) {
        if (this.Detail.product_image[i].name === 'default') {
          list.push({
            id: this.Detail.product_image[i].id
          })
        } else {
          list.push({
            id: '-1',
            media_type: 'image',
            media_path: this.Detail.product_image[i].image_data
          })
        }
      }
      var formData2 = {
        seller_shop_id: this.shop_id,
        shop_media: list,
        shop_name_th: this.Detail.shop_name_th,
        shop_name_en: this.Detail.shop_name_en,
        shop_description: this.Detail.shop_description,
        house_no: this.house_no,
        address_detail: this.room_no + '/' + this.floor + '/' + this.building_name + '/' + this.moo_ban + '/' + this.moo_no + '/' + this.soi + '/' + this.yaek + '/' + this.street,
        province_name: this.province,
        district_name: this.district,
        city_name: this.subdistrict,
        postal_code: this.zipcode
      }
      await this.$store.dispatch('actionCreateSettingSellerShop', formData2)
      var responseSetting = await this.$store.state.ModuleShop.stateCreateSettingSellerShop
      // console.log('response =======>', responseSetting)
      const check = this.checkSendAddress()
      if (check.length !== 0 || check.length === 0) {
        // var dataupdate = {
        //   seller_shop_id: this.shop_id
        // house_no: this.house_no,
        // address_detail: this.room_no + '/' + this.floor + '/' + this.building_name + '/' + this.moo_ban + '/' + this.moo_no + '/' + this.soi + '/' + this.yaek + '/' + this.street,
        // province_name: this.province,
        // district_name: this.district,
        // city_name: this.subdistrict,
        // postal_code: this.zipcode
        // }
        // await this.$store.dispatch('actionsUpdateShopAddress', dataupdate)
        // var responseUpdate = await this.$store.state.ModuleShop.stateUpdateshopaddress
        // console.log('resup', responseUpdate)
        if (responseSetting.result === 'SUCCESS') {
          await this.$swal.fire({ text: `${responseSetting.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
          // this.$router.push('/designShopUI')
          // window.location.reload()
          this.Detail.product_image = []
          this.getDetailShop()
          window.scrollTo(0, 0)
        } else {
          this.$swal.fire({ text: `${responseSetting.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    }
  }
}
</script>

<style lang="css" scoped>
video {
  max-width: 100%;
  height: 100%;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address {
  height: 60px;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
</style>
