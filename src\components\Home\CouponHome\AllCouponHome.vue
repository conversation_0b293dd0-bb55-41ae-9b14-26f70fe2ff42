<template>
  <v-container>
    <v-img :src="require('@/assets/Banner_Coupon.png')"></v-img>
    <v-row class="mt-5">
      <h2 class="pt-1 ml-3 mt-2 mb-4 diaplayWeb"
        style="font-weight: bold; font-size: 32px; line-height: 48px; color: #333333;">คูปอง</h2>
      <h2 class="pt-1 ml-4 mt-2 mb-4 displayIPAD"
        style="font-weight: bold; font-size: 20px; line-height: 48px; color: #333333;">คูปอง</h2>
      <h2 class="pt-1 ml-3 mt-2 mb-4 displayMobile"
        style="font-weight: bold; font-size: 18px; line-height: 48px; color: #333333;">คูปอง</h2>
    </v-row>
    <!--  ในกรณี มีคูปอง start  -->
    <v-row v-if="MobileSize || IpadSize || IpadProSize" align="center">
      <v-col v-for="(items, index) in CouponsIteam" :key="index" :cols="MobileSize ? 12 : 6" xs="6" md="4">
        <CardCouponMobile v-if="index === 0 || index === 1 || index === 2 || index === 6 || index === 7 || index === 8"
          :items="items" :keep="true" colorCard="blue" />
        <CardCouponMobile v-else :items="items" :keep="true" colorCard="green" />
      </v-col>
    </v-row>
    <v-row v-else>
      <v-col v-for="(items, index) in CouponsIteam" :key="index" class="d-flex justify-center mb-6" cols="12" xs="6"
        md="4">
        <CardCoupon v-if="index === 0 || index === 1 || index === 2 || index === 6 || index === 7 || index === 8"
          :items="items" :keep="true" colorCard="blue" />
        <CardCoupon v-else :items="items" :keep="true" colorCard="green" />
      </v-col>
    </v-row>
    <!--  ในกรณี มีคูปอง end  -->
    <v-container v-if="CouponsIteam.length > 0" class="mt-10">
      <v-pagination color="#27AB9C" v-model="pageNumber" :length="pageMax" :total-visible="7" @input="pageChange">
      </v-pagination>
    </v-container>
    <!-- ในกรณี ไม่มีคูปอง start -->
    <v-container v-else>
      <v-row justify="center" class="mx-2">
        <v-col cols="12">
          <v-row justify="center" class="my-5">
            <v-img :src="require('@/assets/No-Favorite.png')" max-height="421" max-width="545" height="100%"
              width="100%" contain></v-img>
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-row justify="center" class="my-5">
            <span
              style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C; font-size: 24px;"><b>ไม่มีรายการคูปอง</b></span>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
    <!-- ในกรณี ไม่มีคูปอง end -->
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardCoupon: () => import('@/components/CardCoupon/CardCoupon'),
    CardCouponMobile: () => import('@/components/CardCoupon/CardCouponMobile')
  },
  data () {
    return {
      pageMax: 1,
      pageNumber: 1,
      companyId: '',
      id_company: '',
      CouponsIteam: []
    }
  },
  created () {
    if (localStorage.getItem('SetRowCompany') !== null) {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      this.companyId = companyId.company.company_id
    } else {
      this.companyId = ''
    }
    this.getCouponHome()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  mounted () {
    this.$EventBus.$on('KeepCouponPageAll', this.getCouponHome)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('KeepCouponPageAll')
    })
  },
  methods: {
    async getCouponHome () {
      if (localStorage.getItem('SetRowCompany') !== null) {
        const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        this.companyId = companyId.company.company_id
      } else {
        this.companyId = ''
      }
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      var data = {
        seller_shop_id: '',
        role_user: dataRole.role,
        company_id: dataRole.role === 'purchaser' ? this.id_company : -1,
        list_type: 'panit',
        page: 1
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAllCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateAllCouponInShop
      this.CouponsIteam = []
      if (response.code === 200) {
        this.pageMax = parseInt(response.data.total_page)
        this.pageNumber = 1
        for (let i = 0; i < response.data.list_coupon.length; i++) {
          this.CouponsIteam.push({
            image: response.data.list_coupon[i].couponImagePath,
            name: response.data.list_coupon[i].couponName,
            description: response.data.list_coupon[i].couponDescription,
            couponDate: {
              useStartDate: response.data.list_coupon[i].useStartDate,
              useEndDate: response.data.list_coupon[i].useEndDate
            },
            status: response.data.list_coupon[i].status,
            couponId: response.data.list_coupon[i].couponId,
            shop_name: response.data.list_coupon[i].shop_name
          })
        }
        window.scrollTo(0, 0)
        this.$store.commit('closeLoader')
      } else {
        window.scrollTo(0, 0)
        this.$store.commit('closeLoader')
      }
    },
    async pageChange (page) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: '',
        role_user: dataRole.role,
        company_id: dataRole.role === 'purchaser' ? this.id_company : -1,
        list_type: 'panit',
        page: page
      }
      await this.$store.dispatch('actionsAllCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateAllCouponInShop
      if (response.code === 200) {
        this.CouponsIteam = []
        this.pageMax = parseInt(response.data.total_page)
        for (let i = 0; i < response.data.list_coupon.length; i++) {
          this.CouponsIteam.push({
            image: response.data.list_coupon[i].couponImagePath,
            name: response.data.list_coupon[i].couponName,
            description: response.data.list_coupon[i].couponDescription,
            couponDate: {
              useStartDate: response.data.list_coupon[i].useStartDate,
              useEndDate: response.data.list_coupon[i].useEndDate
            },
            status: response.data.list_coupon[i].status,
            couponId: response.data.list_coupon[i].couponId,
            shop_name: response.data.list_coupon[i].shop_name
          })
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
      window.scrollTo(0, 0)
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>

@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
    }
    .diaplayWeb {
        display: none;
    }
}
@media screen and (min-width: 1280px) {
    .displayIPAD {
        display: none;
    }
    .displayMobile {
        display: none;
    }
    .diaplayWeb {
        display: inline;
    }
}
</style>
