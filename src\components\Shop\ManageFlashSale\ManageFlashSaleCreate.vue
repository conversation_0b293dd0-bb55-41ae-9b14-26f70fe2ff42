<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col cols="12">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">สร้าง Flash Sale</v-card-title>
          <v-card-title style="font-weight: 700; font-size: 16px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToHome()">mdi-chevron-left</v-icon>สร้าง Flash Sale</v-card-title>
        </v-col>
      </v-row>

      <div class="pa-3">
        <v-form ref="FormProductFlashSale">
          <v-row>
            <v-col cols="12" style="display: flex; align-items: center;">
              <div>
                <v-icon size="30">mdi-circle-medium</v-icon>
                <span class="subTitle pl-3">หัวข้อ Flash Sale</span>
              </div>
              <div>
                <v-text-field
                  v-model="flashSaleTitle"
                  placeholder="กรุณากรอกหัวข้อ Flash Sale"
                  hide-details
                  outlined
                  dense
                  style="max-width: 400px;"
                  class="mt-3"
                ></v-text-field>
              </div>
            </v-col>
            <v-col cols="12" md="12">
              <div style="display: flex; align-items: center;">
                <v-icon size="30">mdi-circle-medium</v-icon>
                <span class="subTitle pl-3">เพิ่มรูปภาพแบนเนอร์สำหรับ Flash Sale </span>
              </div>
                <div>
                  <v-card
                    v-if="CreateFlashSale.length === 0"
                    class="mt-3"
                    elevation="0"
                    :style="theRedI ? 'border: 2px dashed #27AB9C; border-radius: 8px;' : 'border: 2px dashed red; border-radius: 8px;'"
                    style="box-sizing: border-box;"
                    @click="onPickFile()"
                  >
                    <v-card-text>
                      <v-row align="center" justify="center">
                        <v-file-input
                          v-model="DataImage"
                          accept="image/jpeg, image/jpg, image/png"
                          @change="UploadImage()"
                          id="file_input"
                          multiple
                          :clearable="false"
                          style="display: none"
                        />
                        <v-col cols="12" class="text-center">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="6" style="display: flex; justify-content: center;">
                              <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                class="w-100"
                                style="max-width: 250px;"
                                contain
                              />
                            </v-col>
                          </v-row>
                          <div class="mt-2">
                            <div :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">
                              เพิ่มรูปภาพของคุณที่นี่<br />
                              หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ
                            </div>
                            <div :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">
                              (ขนาดรูปภาพ 1480x620 px ไฟล์นามสกุล .JPEG, PNG)
                            </div>
                          </div>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <v-card
                    v-else
                    class="mt-3"
                    elevation="0"
                    style="border: 2px dashed #27AB9C; border-radius: 8px; box-sizing: border-box;"
                  >
                    <v-card-text>
                      <v-row>
                        <v-col
                          v-for="(item, index) in CreateFlashSale"
                          :key="index"
                          cols="12"
                          class="pa-4"
                        >
                          <div class="position-relative" @click="onPickFile()">
                            <v-img
                              :src="item.url"
                              width="100%"
                              contain
                              style="max-height: 300px;"
                            >
                              <v-btn
                                icon
                                x-small
                                class="remove-btn"
                                @click.stop="RemoveImage(index, item)"
                              >
                                <v-icon small color="white">mdi-close</v-icon>
                              </v-btn>
                            </v-img>
                          </div>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </div>
            </v-col>
            <v-col cols="12">
              <div class="d-flex align-center mb-4">
                <v-icon color="#333">mdi-circle-medium</v-icon>
                <span class="subTitle pl-3">ข้อมูลเบื้องต้น</span>
              </div>

              <div class="mb-4" v-if="MobileSize || IpadSize">
                <v-col cols="12" class="pl-0">
                  <span class="mr-2">รอบเวลา :</span>
                  <v-btn rounded outlined color="#27AB9C" @click="dialogDateTime = true">
                    <v-icon class="ml-2" color="#27AB9C">mdi-clock-time-eight-outline</v-icon>
                    <span class="ml-2">เลือกรอบเวลา</span>
                  </v-btn>
                </v-col>
                <v-col cols="12" class="pl-0">
                  <span>วันที่ : {{ formattedDateRange ? formattedDateRange : '-' }}</span>
                  <span class="ml-5">เวลา : {{ this.selectedTimeSlot ? this.selectedTimeSlot : '-' }}</span>
                </v-col>
              </div>

              <div class="mb-4 d-flex align-center" v-else>
                <span class="mr-2">รอบเวลา :</span>
                <v-btn rounded outlined color="#27AB9C" @click="dialogDateTime = true">
                  <v-icon class="ml-2" color="#27AB9C">mdi-clock-time-eight-outline</v-icon>
                  <span class="ml-2">เลือกรอบเวลา</span>
                </v-btn>
                <span class="ml-5">วันที่ : {{ formattedDateRange ? formattedDateRange : '-' }}</span>
                <span class="ml-5">เวลา : {{ this.selectedTimeSlot ? this.selectedTimeSlot : '-' }}</span>
              </div>

              <div class="d-flex align-center">
                <span class="mr-2">เงื่อนไขสินค้า :</span>
                <v-btn rounded outlined color="#333333" @click="dialogCondition = true">
                  <v-icon class="ml-2" color="#333333">mdi-help-circle-outline</v-icon>
                  <span class="ml-2">ดูเงื่อนไขสินค้า</span>
                </v-btn>
              </div>
            </v-col>
            <v-col cols="12">
              <v-row>
                <v-col cols="6" style="display: flex; align-items: center;">
                  <div>
                    <v-icon color="#333">mdi-circle-medium</v-icon>
                    <span class="subTitle pl-3">รายการสินค้า Flash Sale</span>
                  </div>
                </v-col>
                <v-col cols="6" style="text-align: end;">
                  <v-btn rounded color="#27AB9C" style="color: white; font-size: 16px;" @click="productFlashSaleSetting()">เพิ่มสินค้า</v-btn>
                </v-col>
              </v-row>
              <div class="mt-3">
                <v-card outlined class="mb-3">
                  <v-col cols="12">
                    <v-row>
                      <v-col :cols="MobileSize || IpadSize ? 12 : 2">
                        <span><b>แก้ไขเป็นชุด</b></span><br>
                        <span>เลือกแล้ว {{ selectedProducts.length }} รายการ</span>
                      </v-col>
                      <v-col :cols="MobileSize || IpadSize ? 12 : 4">
                        <span><b>ส่วนลด</b></span><br>
                        <div style="display: flex; align-items: center; gap: 8px;">
                          <v-text-field
                            v-model="bulkDiscountValue"
                            dense
                            outlined
                            hide-details
                            style="max-width: 180px;"
                            oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                          ></v-text-field>

                          <v-radio-group
                            v-model="bulkDiscountType"
                            row
                            dense
                            hide-details
                            style="margin: 0; padding: 0;"
                          >
                            <v-radio label="บาท" value="baht" />
                            <v-radio label="%" value="percent" />
                          </v-radio-group>
                        </div>
                      </v-col>
                      <v-col :cols="MobileSize || IpadSize ? 12 : 3">
                        <span><b>คลังสินค้าแคมเปญ</b></span><br>
                        <v-text-field
                          v-model="bulkQuantity"
                          dense
                          outlined
                          hide-details
                          style="max-width: 180px;"
                          oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" style="display: flex; align-items: flex-end;" v-if="MobileSize || IpadSize">
                        <v-btn rounded color="#27AB9C" style="color: #FFFFFF;" @click="applyBulkEdit()" :disabled="selectedProducts.length === 0 || bulkDiscountValue === '' || bulkQuantity === ''" class="mr-5">
                          อัปเดตทั้งหมด
                        </v-btn>
                        <v-btn rounded color="red" style="color: #FFFFFF;" @click="applyBulkDelete()" :disabled="selectedProducts.length === 0">
                          ลบ
                        </v-btn>
                      </v-col>
                      <v-col cols="3" class="d-flex justify-center align-center" style="height: 100%; flex-direction: column;" v-else-if="IpadProSize">
                        <v-btn
                          rounded
                          color="#27AB9C"
                          style="color: #FFFFFF;"
                          @click="applyBulkEdit()"
                          :disabled="selectedProducts.length === 0 || bulkDiscountValue === '' || bulkQuantity === ''"
                          class="mb-2"
                        >
                          อัปเดตทั้งหมด
                        </v-btn>
                        <v-btn
                          rounded
                          color="red"
                          style="color: #FFFFFF;"
                          @click="applyBulkDelete()"
                          :disabled="selectedProducts.length === 0"
                        >
                          ลบ
                        </v-btn>
                      </v-col>
                      <v-col cols="3" style="display: flex; align-items: flex-end;" v-else>
                        <v-btn rounded color="#27AB9C" style="color: #FFFFFF;" @click="applyBulkEdit()" :disabled="selectedProducts.length === 0 || bulkDiscountValue === '' || bulkQuantity === ''" class="mr-5">
                          อัปเดตทั้งหมด
                        </v-btn>
                        <v-btn rounded color="red" style="color: #FFFFFF;" @click="applyBulkDelete()" :disabled="selectedProducts.length === 0">
                          ลบ
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-card>
                <v-card>
                  <v-data-table
                    :headers="headers"
                    :items="productList"
                    item-key="compositeKey"
                    show-select
                    :value="selectedProductObjects"
                    @input="onSelectionChange"
                    :items-per-page="10"
                    class="elevation-0"
                    :search="search"
                    :loading="false"
                    no-data-text="ไม่มีรายการสินค้า Flash Sale"
                    no-results-text="ไม่พบรายการสินค้า Flash Sale"
                  >

                  <template v-slot:[`item.product_name`]="{ item }">
                    <v-col cols="12">
                      <v-row v-if="!MobileSize">
                        <v-col cols="3" class="pl-0 pr-0">
                          <v-img :src="`${item.product_image}`" v-if="item.product_image !== null" width="50"/>
                          <v-img src="@/assets/NoImage.png" width="50" v-else/>
                        </v-col>
                        <v-col cols="9" class="pr-0">
                          <b>{{ item.product_name }}</b> <br/>
                          <span v-if="item.attribute_priority_1">{{item.key_1_value}}: <b>{{item.attribute_priority_1}}</b></span> <br/>
                          <span v-if="item.attribute_priority_2">{{item.key_2_value}}: <b>{{item.attribute_priority_2}}</b></span>
                        </v-col>
                      </v-row>
                      <v-row v-else>
                        <v-col cols="4" class="pr-0">
                          <v-img :src="`${item.product_image}`" v-if="item.product_image !== null" width="50"/>
                          <v-img src="@/assets/NoImage.png" width="50" v-else/>
                        </v-col>
                        <v-col cols="8" class="pr-0">
                          <b>{{ item.product_name }}</b> <br/>
                          <span v-if="item.attribute_priority_1">{{item.key_1_value}}: <b>{{item.attribute_priority_1}}</b></span> <br/>
                          <span v-if="item.attribute_priority_2">{{item.key_2_value}}: <b>{{item.attribute_priority_2}}</b></span>
                        </v-col>
                      </v-row>
                    </v-col>
                  </template>

                  <template v-slot:[`item.real_price`]="{ item }">
                    <span>{{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>

                  <template v-slot:[`item.FlashSale_discount`]="{ item }">
                    <div style="display: flex; align-items: center; gap: 8px;">
                      <v-text-field
                        v-model="item.FlashSale_discount"
                        dense
                        outlined
                        hide-details="auto"
                        style="max-width: 120px"
                        :error="!!item.discountError"
                        :error-messages="item.discountError"
                        @input="validateDiscount(item)"
                        @blur="correctDiscount(item)"
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                      ></v-text-field>

                      <v-radio-group
                        v-model="item.discount_type"
                        row
                        dense
                        hide-details
                        style="margin: 0; padding: 0;"
                        @change="validateDiscount(item)"
                      >
                        <v-radio label="บาท" value="baht" />
                        <v-radio label="%" value="percent" />
                      </v-radio-group>
                    </div>
                  </template>

                  <template v-slot:[`item.FlashSale_stock`]="{ item }">
                    <v-text-field
                      v-model="item.FlashSale_stock"
                      dense
                      outlined
                      hide-details="auto"
                      :error="!!item.quantityError"
                      :error-messages="item.quantityError"
                      @input="validateQuantity(item)"
                      @blur="correctQuantity(item)"
                      oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                    ></v-text-field>
                  </template>

                  <template v-slot:[`item.product_stock`]="{ item }">
                     <span>{{ Number(item.product_stock).toLocaleString() }}</span>
                  </template>

                  <template v-slot:[`item.action`]="{ item }">
                    <v-btn
                      icon
                      color="red"
                      @click="RemoveProduct(item)"
                    >
                      <v-icon>mdi-delete-circle-outline</v-icon>
                    </v-btn>
                  </template>

                  </v-data-table>
                </v-card>
              </div>
            </v-col>
            <v-col cols="12" style="display: flex; justify-content: flex-end;">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="backToHome()" class="mr-2">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="!isFormValid" @click="CCFlashSale()">ยืนยัน</v-btn>
            </v-col>
          </v-row>
        </v-form>
      </div>
    </v-card>

    <v-dialog v-model="dialogDateTime" max-width="700px" persistent>
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDateTime = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>เลือกรอบเวลาและวันที่</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-container>
            <v-row v-if="MobileSize">
              <v-col cols="12">
                <v-date-picker
                  v-model="selectedDate"
                  :allowed-dates="allowedDates"
                  color="#27AB9C"
                  elevation="15"
                  locale="th"
                  range
                ></v-date-picker>
              </v-col>

              <v-col cols="12">
                <div class="mb-2"><b>รอบเวลา</b></div>
                <v-radio-group v-model="selectedTimeSlot">
                  <v-radio
                    v-for="(slot, index) in timeSlots"
                    :key="index"
                    :label="`${slot.time}`"
                    :value="slot.time"
                  ></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col cols="6">
                <v-date-picker
                  v-model="selectedDate"
                  :allowed-dates="allowedDates"
                  color="#27AB9C"
                  elevation="15"
                  locale="th"
                  range
                ></v-date-picker>
              </v-col>

              <v-col cols="6">
                <div class="mb-2"><b>รอบเวลา</b></div>
                <v-radio-group v-model="selectedTimeSlot">
                  <v-radio
                    v-for="(slot, index) in timeSlots"
                    :key="index"
                    :label="`${slot.time}`"
                    :value="slot.time"
                  ></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="CancelSelect()">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" :disabled="!isDateTimeSelected" @click="dialogDateTime = false">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogProductShop" persistent max-width="750">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogProductShop = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>เพิ่มสินค้า Flash Sale</b></span>
        </v-card-title>
        <br>

        <v-card-text>
          <v-col cols="12">
            <span style="font-size: 16px;"><b>เลือกสินค้าที่ต้องการ</b></span>
          </v-col>
          <v-col cols="12">
            <v-text-field v-model="searchAddFlashSale" @keyup="searchDataAddFlashsale(searchAddFlashSale)" placeholder="ค้นหาชื่อสินค้าหรือรหัสสินค้า" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" v-if="this.listProductTagDialog.length === 0">
            <v-card
              elevation="0"
              style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
            >
            <v-container>
                <v-col cols="12">
                  <div class="d-flex flex-column align-center justify-center">
                    <v-avatar size="200">
                      <v-img src="@/assets/NoProducts.png" width="300" height="300" contain></v-img>
                    </v-avatar>
                    <span style="font-size: 18px;">ไม่มีรายการสินค้า</span>
                  </div>
                </v-col>
              </v-container>
            </v-card>
          </v-col>
          <v-col cols="12" class="pa-0" v-else>
            <v-row no-gutters>
              <v-col
                v-for="product in listProductTagDialog"
                :key="`${product.product_id}-${product.product_attribute_id}`"
                cols="12" sm="6" md="4" lg="4"
                class="mb-4 pa-2"
              >
                <v-card class="fill-height d-flex flex-column">
                  <div style="display: flex; justify-content: center; align-items: center; padding: 10px;">
                    <v-img
                      v-if="product.product_image && product.product_image !== '-' && product.product_image !== null"
                      :src="product.product_image"
                      height="100px"
                      width="100px"
                      class="mr-2"
                      contain
                    ></v-img>
                    <img
                      v-else
                      src="@/assets/NoImage.png"
                      style="max-width: 100px; max-height: 100px;"
                      class="mr-2"
                    />
                  </div>

                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-card-title
                        v-bind="attrs"
                        v-on="on"
                        style="
                          font-size: 16px;
                          color: #333333;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          width: 100%;
                          display: block;
                        "
                        >
                        <b>{{ product.product_name }}</b>
                      </v-card-title>
                    </template>
                    <span>{{ product.product_name }}</span>
                  </v-tooltip>

                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-card-subtitle
                        v-bind="attrs"
                        v-on="on"
                        style="
                          font-size: 14px;
                          color: #333333;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          width: 100%;
                          display: block;
                          padding-top: 0px;
                        "
                        class="pb-0"
                      >
                        รหัสสินค้า : {{ product.product_sku }}
                      </v-card-subtitle>
                    </template>
                    <span>รหัสสินค้า : {{ product.product_sku }}</span>
                  </v-tooltip>

                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-card-subtitle
                        v-bind="attrs"
                        v-on="on"
                        style="
                          font-size: 14px;
                          color: #333333;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          width: 100%;
                          display: block;
                          padding-top: 0px;
                        "
                        class="pb-0"
                      >
                        <span v-if="product.attribute_priority_1">{{product.key_1_value}}: <b>{{product.attribute_priority_1}}</b></span> <br/>
                        <span v-if="product.attribute_priority_2">{{product.key_2_value}}: <b>{{product.attribute_priority_2}}</b></span>
                      </v-card-subtitle>
                    </template>
                    <span v-if="product.attribute_priority_1">{{product.key_1_value}}: <b>{{product.attribute_priority_1}}</b></span> <br/>
                    <span v-if="product.attribute_priority_2">{{product.key_2_value}}: <b>{{product.attribute_priority_2}}</b></span>
                  </v-tooltip>

                  <v-spacer></v-spacer>

                  <v-card-actions class="px-4 pt-0">
                    <v-checkbox
                      :input-value="isProductSelected(product)"
                      @change="toggleProduct(product)"
                      hide-details
                      label="เลือก"
                      class="pt-0 mr-0"
                    />
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
        </v-card-text>

        <v-col cols="12">
          <v-row no-gutters justify="center" v-if="pageMaxDialog > 0">
            <v-pagination
              color="#27AB9C"
              v-model="pageNumberDialog"
              :length="pageMaxDialog"
              :total-visible="9">
            </v-pagination>
          </v-row>
        </v-col>

        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 150px; font-size: 16px;" @click="dialogProductShop = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 150px; color: white; font-size: 16px;" @click="submitProduct()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogCondition" max-width="400px" persistent>
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogCondition = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>เงื่อนไขสินค้า</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <p>1. คลังสินค้าคงเหลือ: 5 ~ 10000</p>
          <p>2. คะแนนสินค้า (0.0 - 5.0): ไม่จำกัดเงื่อนไข</p>
          <p>3. Pre-Order(s): ไม่จำกัดเงื่อนไข</p>
          <p>4. วันจัดส่งสูงสุด: ไม่จำกัดเงื่อนไข วัน</p>
          <p>5. ส่วนลดขั้นต่ำ: 5% ~ 99%</p>
          <p>6. ถูกใจ: ไม่จำกัดเงื่อนไข</p>
          <p>7. มีจำนวนคำสั่งซื้อใน 30 วันที่แล้ว: ไม่จำกัดเงื่อนไข</p>
          <p>8. จำกัดการแสดงสินค้าซ้ำ: ไม่จำกัดเงื่อนไข วัน</p>
        </v-card-text>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import moment from 'moment'
import 'moment/locale/th'
export default {
  data () {
    return {
      DataImage: [],
      theRedI: true,
      CreateFlashSale: [],
      dialogDateTime: false,
      selectedDate: null,
      selectedTimeSlot: null,
      timeSlots: [
        { time: '00:00:00 - 11:59:59' },
        { time: '12:00:00 - 17:59:59' },
        { time: '18:00:00 - 20:59:59' },
        { time: '21:00:00 - 23:59:59' }
      ],
      search: '',
      searchAddFlashSale: '',
      dialogProductShop: false,
      listProductTagDialog: [],
      selectedProductIds: [],
      currentDialog: 1,
      pageMaxDialog: 0,
      itemsPerPage: 9,
      productList: [],
      dialogCondition: false,
      headers: [
        { text: 'รายการสินค้า', value: 'product_name', width: '250', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคาเดิม', value: 'product_price', width: '80', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ราคาส่วนลด (฿)', value: 'FlashSale_price', width: '50', sortable: false },
        { text: 'ส่วนลด', value: 'FlashSale_discount', width: '250', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'คลังสินค้าแคมเปญ', value: 'FlashSale_stock', width: '150', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'คลังสินค้า', value: 'product_stock', width: '100', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'จำกัดการซื้อ', value: 'FlashSale_limit', width: '50', sortable: false },
        { text: 'จัดการ', value: 'action', width: '50', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      flashSaleTitle: '',
      selectedProducts: [],
      bulkDiscountValue: '',
      bulkDiscountType: 'baht',
      bulkQuantity: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumberDialog: {
      get () {
        return this.currentDialog || 1
      },
      set (newPage) {
        this.currentDialog = newPage
        window.scrollTo(0, 0)
        this.ListProductTagDialog(newPage, this.search)
      }
    },
    sortedDateRange () {
      if (!this.selectedDate || this.selectedDate.length === 0) return []
      if (this.selectedDate.length === 1) return [this.selectedDate[0], this.selectedDate[0]]
      return [...this.selectedDate].sort((a, b) => new Date(a) - new Date(b))
    },
    formattedDateRange () {
      const [start, end] = this.sortedDateRange
      if (!start || !end) return ''
      return `${moment(start).format('D/M/YYYY')} - ${moment(end).format('D/M/YYYY')}`
    },
    isDateTimeSelected () {
      return this.selectedDate && this.selectedTimeSlot
    },
    isFormValid () {
      return (
        this.flashSaleTitle &&
        this.CreateFlashSale.length > 0 &&
        this.selectedDate &&
        this.selectedTimeSlot &&
        this.productList.length > 0 &&
        this.productList.every(p =>
          p.FlashSale_discount !== null &&
          p.FlashSale_discount !== '' &&
          p.discount_type &&
          p.FlashSale_stock !== null &&
          p.FlashSale_stock !== '' &&
          !p.discountError
        )
      )
    },
    selectedProductObjects () {
      return this.productList.filter(p => this.selectedProducts.includes(p.compositeKey))
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageFlashSaleCreateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageFlashSaleCreate' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
  },
  methods: {
    backToHome () {
      this.$EventBus.$emit('openNavBar')
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSale' })
      } else {
        this.$router.push({ path: '/manageFlashSaleMobile' })
      }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    async UploadImage () {
      const shopId = localStorage.getItem('shopSellerID')
      for (let i = 0; i < this.DataImage.length; i++) {
        const element = this.DataImage[i]
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
          var url = URL.createObjectURL(element)
          const image = new Image()
          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => {
              const dimensions = {
                height: image.height,
                width: image.width
              }
              resolve(dimensions)
            }
            image.src = url
          })
          if (this.CreateFlashSale.length < 2) {
            if (i < 1) {
              if (imageDimensions.height <= 620 && imageDimensions.width <= 1480) {
                const reader = new FileReader()
                reader.readAsDataURL(element)
                reader.onload = async () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  this.CreateFlashSale.push({
                    image_data: resultReader.split(',')[1],
                    url: url,
                    link: ''
                  })

                  const data = {
                    image: [this.CreateFlashSale[0].image_data],
                    type: '',
                    seller_shop_id: shopId
                  }
                  this.$store.commit('openLoader')
                  await this.$store.dispatch('actionsUploadToS3', data)
                  const response = await this.$store.state.ModuleShop.stateUploadToS3

                  if (response.message === 'List Success.') {
                    this.CreateFlashSale[0].image_data = response.data.list_path[0].path
                    this.$store.commit('closeLoader')
                  }
                  this.theRedI = true
                }
              } else {
                this.$swal.fire({
                  icon: 'warning',
                  text: 'โปรดใช้รูปตามขนาดที่กำหนด',
                  showConfirmButton: false,
                  timer: 1500,
                  timerProgressBar: true
                })
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ',
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true
              })
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ',
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true
            })
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
        }
      }
    },
    RemoveImage (index, val) {
      this.CreateFlashSale.splice(index, 1)
    },
    onSelectionChange (selectedItems) {
      this.selectedProducts = selectedItems.map(item => item.compositeKey)
    },
    applyBulkEdit () {
      this.selectedProducts.forEach(key => {
        const target = this.productList.find(p => p.compositeKey === key)
        if (!target) return

        if (this.bulkDiscountValue !== '') {
          this.$set(target, 'FlashSale_discount', parseFloat(this.bulkDiscountValue))
          this.$set(target, 'discount_type', this.bulkDiscountType)
        }

        if (this.bulkQuantity !== '') {
          this.$set(target, 'FlashSale_stock', parseInt(this.bulkQuantity))
        }

        this.validateDiscount(target)
        this.validateQuantity(target)
      })
    },
    applyBulkDelete () {
      this.selectedProducts.forEach(key => {
        const index = this.productList.findIndex(p => p.compositeKey === key)
        if (index !== -1) {
          this.productList.splice(index, 1)
        }
      })

      this.selectedProducts = []
      this.bulkDiscountValue = ''
      this.bulkDiscountType = 'baht'
      this.bulkQuantity = ''
    },
    searchDataAddFlashsale (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.ListProductTagDialog(1, val)
      }, 500)
    },
    async ListProductTagDialog (page = 1, textSearch = '') {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        page: page,
        limit: this.itemsPerPage,
        filter_product: textSearch
      }
      await this.$store.dispatch('actionSelectListProductFlashSale', data)
      var responseData = await this.$store.state.ModuleManageFlashSale.stateSelectListProductFlashSale
      if (responseData.message === 'ดึงข้อมูลรายการสินค้าสำเร็จ.') {
        this.$store.commit('closeLoader')
        // this.listProductTagDialog = responseData.data.product_list
        this.listProductTagDialog = responseData.data.product_list.map(item => {
          item.compositeKey = `${item.product_id}-${item.product_attribute_id}`
          return item
        })
        this.pageMaxDialog = responseData.data.pagination.total_page
        this.titleFlashSale = responseData.data.title
        this.imagePath = responseData.data.image_path
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    async productFlashSaleSetting () {
      this.dialogProductShop = true
      this.pageNumberDialog = 1
      this.selectedProductIds = []
      this.listProductTagDialog = []
      this.searchAddFlashSale = ''
    },
    validateDiscount (item) {
      const value = parseFloat(item.FlashSale_discount)
      item.discountError = ''

      if (isNaN(value)) return

      if (item.discount_type === 'percent') {
        if (value < 5) {
          item.discountError = 'ส่วนลดขั้นต่ำ 5%'
        } else if (value > 99) {
          item.discountError = 'ส่วนลดสูงสุด 99%'
        }
      } else if (item.discount_type === 'baht') {
        if (value <= 0) {
          item.discountError = 'ต้องมากกว่า 0 บาท'
        } else if (value > item.product_price) {
          item.discountError = `ต้องไม่เกิน ${item.product_price} บาท`
        }
      }
    },
    correctDiscount (item) {
      let value = parseFloat(item.FlashSale_discount)

      if (isNaN(value)) {
        item.FlashSale_discount = ''
        item.discountError = ''
        return
      }

      if (item.discount_type === 'percent') {
        if (value < 5) value = 5
        if (value > 99) value = 99
      } else if (item.discount_type === 'baht') {
        if (value <= 0) value = 1
        if (value > item.product_price) value = item.product_price
      }

      item.FlashSale_discount = value
      item.discountError = ''
    },
    validateQuantity (item) {
      const value = parseInt(item.FlashSale_stock)
      item.quantityError = ''

      if (isNaN(value)) return

      if (value <= 0) {
        item.quantityError = 'จำนวนต้องมากกว่า 0'
      } else if (value > item.product_stock) {
        item.quantityError = `ต้องไม่เกินสต็อก (${item.product_stock})`
      }
    },
    correctQuantity (item) {
      let value = parseInt(item.FlashSale_stock)

      if (isNaN(value)) {
        item.FlashSale_stock = ''
        item.quantityError = ''
        return
      }

      if (value <= 0) value = 1
      if (value > item.product_stock) value = item.product_stock

      item.FlashSale_stock = value
      item.quantityError = ''
    },
    allowedDates (date) {
      const today = new Date()
      const targetDate = new Date(date)

      today.setHours(0, 0, 0, 0)
      targetDate.setHours(0, 0, 0, 0)

      return targetDate >= today
    },
    isProductSelected (product) {
      return this.productList.some(p =>
        p.product_id === product.product_id &&
        p.product_attribute_id === product.product_attribute_id
      )
    },
    toggleProduct (product) {
      const index = this.productList.findIndex(item =>
        item.product_id === product.product_id &&
        item.product_attribute_id === product.product_attribute_id
      )

      if (index !== -1) {
        this.productList.splice(index, 1)
      } else {
        this.productList.push({ ...product })
      }
    },
    CancelSelect () {
      this.selectedDate = []
      this.selectedTimeSlot = null
      this.dialogDateTime = false
    },
    submitProduct () {
      this.dialogProductShop = false
    },
    RemoveProduct (item) {
      const index = this.productList.findIndex(p =>
        p.product_id === item.product_id &&
        p.product_attribute_id === item.product_attribute_id
      )
      if (index !== -1) {
        this.productList.splice(index, 1)
      }
    },
    async CCFlashSale () {
      if (this.CreateFlashSale.length === 0) {
        this.theRedI = false
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มรูปภาพแบนเนอร์สำหรับ Flash Sale',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        return
      }

      if (this.productList.length === 0) {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มรายการสินค้า Flash Sale',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        return
      }

      this.theRedI = true

      const products = this.productList.map(p => ({
        product_id: p.product_id,
        product_attribute_id: p.product_attribute_id,
        discount_type: p.discount_type,
        discount_value: Number(p.FlashSale_discount),
        quantity: Number(p.FlashSale_stock)
      }))

      const [startTime, endTime] = this.selectedTimeSlot.split(' - ')
      const [startDate, endDate] = this.sortedDateRange

      const shopId = localStorage.getItem('shopSellerID')
      const payload = {
        seller_shop_id: shopId,
        title: this.flashSaleTitle,
        start_date: `${moment(startDate).format('YYYY-MM-DD')} ${startTime}`,
        end_date: `${moment(endDate).format('YYYY-MM-DD')} ${endTime}`,
        image_path: this.CreateFlashSale[0].image_data,
        product_list: products
      }

      await this.$store.dispatch('actionsCreateFlashSaleV2', payload)
      var responseData = await this.$store.state.ModuleManageFlashSale.stateCreateFlashSaleV2
      if (responseData.message === 'Flash sale created successfully.') {
        this.$swal.fire({
          icon: 'success',
          text: 'สร้าง Flash Sale สำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        }).then(() => {
          this.$EventBus.$emit('openNavBar')
          if (!this.MobileSize) {
            this.$router.push({ path: '/manageFlashSale' })
          } else {
            this.$router.push({ path: '/manageFlashSaleMobile' })
          }
        })
      } else if (responseData.message === 'Not access this function. Required role [manage_flash_sale] in this shop.') {
        this.$swal.fire({
          icon: 'info',
          text: 'บัญชีของคุณยังไม่มีสิทธิ์สร้าง Flash Sale ในขณะนี้',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      } else {
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    }
  }
}
</script>

<style scoped>
.remove-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ff5252;
  z-index: 10;
}

::v-deep(.v-data-table-header-mobile__wrapper) {
  display: flex;
  justify-content: end;
}

::v-deep(.v-data-table-header-mobile__select .v-simple-checkbox) {
  padding-left: 25px;
}
</style>

<style lang="scss" scoped>
::v-deep .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
  border-bottom: none !important;
}

::v-deep .v-data-table thead th:first-of-type {
  background-color: #E6F5F3 !important;
  border-bottom: none !important;
}
</style>
