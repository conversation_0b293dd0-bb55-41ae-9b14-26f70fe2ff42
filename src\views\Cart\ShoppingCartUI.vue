<template lang="html">
  <!-- <v-container grid-list-xs> -->
  <div>
    <ShoppingCartUI v-if="checkLogin === true"/>
    <ShoppingCartLocalUI v-else/>
    <!-- <ProductSameShop :propsData='GetAllProduct.recommend' header='สินค้าที่คุณอาจจะชอบ' :check='status'/> -->
  </div>
  <!-- </v-container> -->
</template>

<script>
const recommend = []
for (let i = 0; i < 50; i++) {
  recommend.push({
    product_id: i,
    name: `Data Title recommend ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    ShoppingCartUI: () => import(/* webpackPrefetch: true */ '@/components/Cart/ShoppingCartUI'),
    ShoppingCartLocalUI: () => import(/* webpackPrefetch: true */ '@/components/Cart/ShoppingCartLocalUI')
    // ProductSameShop: () => import('@/components/DetailProduct/ProductSameShop')
  },
  data () {
    return {
      GetAllProduct: {
        recommend
      },
      checkLogin: false,
      status: false
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('oneData') !== null) {
      this.checkLogin = true
    } else {
      this.checkLogin = false
    }
  }
}
</script>

<style lang="css" scoped>
</style>
