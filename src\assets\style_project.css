.spacerStyle {
  border-top: 3px solid #daf1e9;
  margin-top: 35px;
}

.textSearch {
  font-size: 16px;
  margin-left: 4px;
}

.textSearchIpad {
  font-size: 16px;
  margin-left: 4px;
}

.instagram {
  background: radial-gradient(
    circle at 30% 107%,
    #fdf497 0%,
    #fdf497 5%,
    #fd5949 45%,
    #d6249f 60%,
    #285aeb 90%
  );
}

.headFooterAddress {
  font-size: 12px;
  font-weight: bold;
  color: #333333;
}

.textFooterAddress {
  font-size: 12px;
  color: #333333;
}

.headsubFooter {
  font-size: 16px;
  font-weight: 700;
}

.textsubFooter {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  align-items: center;
  color: #4F688C;
}

.headsubFooterIpad {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0px;
  color: #4F688C;
  line-height: 26px;
}

.textsubFooterIpad {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.headsubFooterMobile {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
}

.textsubFooterMobile {
  text-align: center;
  font-size: 12px;
  align-items: center;
}

.textSKUCard {
  font-size: 16px;
  font-weight: bold;
  line-height: 26px;
  color: #333333;
  text-decoration: none;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
  white-space: nowrap;
}

.textProduct {
  font-size: 16px;
  font-weight: bold;
  line-height: 26px;
  color: #333333;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 50px;
}

.textSKUCardMobile {
  font-size: 12px;
  font-weight: bold;
  line-height: 16px;
  color: #333333;
  text-decoration: none;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
  white-space: nowrap;
}

.textProductMobile {
  font-size: 12px;
  font-weight: bold;
  line-height: 16px;
  color: #333333;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 30px;
}

.priceDecrese {
  font-size: 14px;
  text-decoration: line-through;
  color: #929292;
  margin-right: 0px;
}

.specialPrice {
  font-size: 18px;
  color: #1B5DD6;
  font-weight: 700;
}

.priceDecreseIPAD {
  font-size: 16px;
  text-decoration: line-through;
  color: #636363;
  font-weight: 500;
  line-height: 22px;
  margin-right: 0px;
}

.specialPriceIPAD {
  font-size: 22px;
  font-weight: bold;
  color: #d1392b;
  line-height: 40px;
}

.specialPriceIPADPro {
  font-size: 20px;
  font-weight: bold;
  color: #d1392b;
  line-height: 40px;
}

.textLogin {
  font-weight: normal;
  font-size: 14px;
  line-height: 32px;
  color: #000000;
}

.styleButton {
  border: 1px solid #27ab9c;
  border-radius: 32px;
}

.subTextLogin {
  font-size: 14px;
  line-height: 22px;
  color: #000000;
}

.styleCardRegister {
  background: #ffffff;
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.textRegister {
  font-weight: normal;
  font-size: 14px;
  line-height: 32px;
  color: #000000;
}

.textbtnRegister {
  border-radius: 32px;
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
}

.subtextReegister {
  font-size: 16px;
  line-height: 24px;
}

.cardNotification {
  max-width: 350px;
  width: 350px;
  height: 100%;
}

.listNotification {
  overflow-y: auto;
  max-height: 498px;
  box-shadow: 0px 4px 16px rgba(51, 91, 225, 0.16);
  border-radius: 8px;
  padding: 0px 0px !important;
}

.imagesNotification {
  background-color: #ffffff;
  border: 2px solid #f3f5f7;
  border-radius: 999px;
}

.HeaderWebProduct {
  font-weight: bold;
  font-size: 32px;
  line-height: 48px;
  color: #333333;
}

.HeaderIpadProduct {
  font-weight: bold;
  font-size: 20px;
  line-height: 48px;
  color: #333333;
}

.HeaderMobileProduct {
  font-weight: bold;
  font-size: 18px;
  line-height: 48px;
  color: #333333;
}

.spacerStyleProductInDetailPage {
  border-top: 1px solid #bbb;
  margin-top: 24px;
}

.fontHeaderListProduct {
  color: #333333 !important;
  font-weight: 800 !important;
  font-size: 32px !important;
}

.gradient-underline {
  position: relative;
  display: inline-block;
  text-decoration: none; /* ปิดเส้นใต้ปกติ */
}

.gradient-underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 4px; /* ความหนาของเส้น */
  background: linear-gradient(90deg, #89FFD4 -4%, #2D95FF 103.33%);
  border-radius: 4px; /* ทำให้ปลายเส้นมน */
}

.fontCreatedDetailProduct {
  font-weight: bold !important;
  font-size: 18px !important;
  line-height: 26px !important;
  color: #333333 !important;
}

.fontAttributeDetailProduct {
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #636363 !important;
}

.buttonAddCartDetail {
  min-width: 78 !important;
  height: 48px !important;
  font-weight: bold !important;
  border-radius: 32px !important;
}

.buttonDisableAddCartDetail {
  min-width: 78 !important;
  height: 48px !important;
  border-radius: 32px !important;
}

.textComment {
  font-size: 20px !important;
  line-height: 26px !important;
  color: #333333 !important;
}

.texthover:hover {
  color: #27ab9c;
  text-decoration: underline;
}

.HeadTextCreateShop {
  font-size: 24px;
  font-weight: 700;
  line-height: 140%;
  color: #333333;
}

.SubTextCreateShop {
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  color: #9a9a9a;
}

.HeadStepCreate {
  display: flex;
  padding: 12px 48px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 0px 20px 20px 0px;
  background: #f4bc5f;
  box-shadow: 0px 0.5px 2px 0px rgba(96, 97, 112, 0.16),
    0px 0px 1px 0px rgba(40, 41, 61, 0.08);
  font-size: 18px;
  font-weight: 600;
  line-height: 140%;
  color: #ffffff;
}

.textStepOne {
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  color: #333333;
}

.textUploadnameImage {
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #9a9a9a;
}

.textUploadnameImageFail {
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #f5222d;
}

.textUploadsizeImage {
  font-size: 10px;
  font-weight: 400;
  line-height: 140%;
  color: #cccccc;
}

.textFailUpload {
  font-size: 10px;
  font-weight: 400;
  line-height: 140%;
  color: #f5222d;
}

.textUploadpercentImage {
  font-size: 10px;
  font-weight: 400;
  line-height: 140%;
  color: #27ab9c;
}

.textFieldStepOne {
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  color: #333333;
}

.subtextStepOne {
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  color: #a1a1a1;
}

.subtextbanner {
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  color: #c4c4c4;
}

.textUploadImage {
  font-size: 14px;
  font-weight: 500;
  line-height: 140%;
  color: #27ab9c;
}

.textNumberImage {
  font-size: 16px;
  font-weight: 700;
  line-height: 140%;
  color: #27ab9c;
}

.textUpload {
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  color: #636363;
}

.textUploadSelect {
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  color: #1b5dd6;
  text-decoration: underline;
}

.textEditManageShop {
  font-size: 16px;
  font-weight: 500;
  line-height: 140%;
  color: #27ab9c;
  text-decoration: underline;
}

.cardImageStyle {
  padding: 24px 16px;
  border-radius: 8px;
  border: 1px solid var(--neutral-e-6-e-6-e-6, #e6e6e6);
  justify-content: center;
  align-items: center;
}

.backgroundHead {
  background: url("../assets/ImageINET-Marketplace/ICONShop/backgroundHead.png")
    no-repeat;
  background-blend-mode: luminosity, normal;
  padding: 24px 48px 0px 48px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  display: flex;
  border-radius: 24px 24px 0px 0px;
  z-index: 1;
}

.backgroundHeadPartner {
  background: url("../assets/ImageINET-Marketplace/ICONShop/backgroundHeadPartner.png")
    no-repeat;
  background-blend-mode: luminosity, normal;
  padding: 24px 48px 0px 48px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  display: flex;
  border-radius: 24px 24px 0px 0px;
  z-index: 1;
}

.backgroundContent {
  display: flex;
  border-radius: 20px 20px 0px 0px;
  background: #ffffff;
  flex-direction: column;
  align-items: flex-start;
  gap: 28px;
  align-self: stretch;
  z-index: 2;
}

.textUploadNotComplete {
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  color: #636363;
}

.textProvice {
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #636363;
}

.textUploadlimitComplete {
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #9a9a9a;
}

.textStepThree {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  color: #a1a1a1;
}

.textUploadImageSuccess {
  color: #1b5dd6;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  /* 19.6px */
  text-decoration: underline;
}

.textBigImage {
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #ffffff;
}

.textManageShop {
  font-size: 18px;
  font-weight: 600;
  line-height: 140%;
  color: #333;
}

.textManageShopHead {
  font-size: 16px !important;
  font-weight: 600;
  line-height: 140%;
  color: #333;
}

.my-span {
  color: #ffffff;
  margin-left: 16px;
  margin-top: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 140%;
  float: left;
}

.my-span-Mobile {
  color: #ffffff;
  margin-left: 16px;
  margin-top: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 140%;
  float: left;
}

.shopName {
  font-size: 24px;
  font-weight: 700;
  line-height: 140%;
  color: #333;
}

.buttonAddListProductShop {
  box-shadow: 0px 8px 16px 0px rgba(96, 97, 112, 0.16),
    0px 2px 4px 0px rgba(40, 41, 61, 0.04);
  font-size: 16px;
  font-weight: 500;
  line-height: 140%;
}

.setBorder {
  border-top: 1px solid #7ccfb4 !important;
  border-bottom: 1px solid #7ccfb4 !important;
  border-right: 1px solid #7ccfb4 !important;
  border-left: 1px solid #7ccfb4 !important;
  border-radius: 8px !important;
}

/* ใช้สำหรับเพิ่มที่อยู่ใหม่ saleordernoJV */
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
  font-weight: 700 !important;
}

.v-data-table.row-height-64 td {
  height: 80px !important;
}

.disable-events {
  pointer-events: none;
}

.addAddress:hover {
  transform: scale(1.02) !important;
}

.QT .card {
  border-radius: 5px !important;
  background-color: #f5f5f5;
}

.custom-chip.v-chip--active {
  background-color: #757575 !important; /* สีเทาเข้ม */
  color: #ffffff !important; /* สีขาว */
}

.custom-btn {
  border-color: #27ab9c; /* Change to the color you desire */
}

.custom-btn .v-icon {
  font-size: 20px; /* Change to the desired size */
}

.reduce-spacing {
  margin-bottom: -25px;
}

.rounded-button {
  border-radius: 12px;
  padding: 10px 20px;
}

.header-color {
  background-color: #27ab9c; /* Change to desired color */
  color: rgb(255, 255, 255) !important;
  text-align: center;
}

.error-404 {
  text-align: center;
  align-items: center;
  justify-content: center;
  background-color: white;
  padding-top: 10%;
  padding-bottom: 10%;
}

.header-style {
  background: #d4f1e4 !important;
}
.status-1 {
  color: #e9a016;
}

.numeric-input .ant-tooltip-inner {
  min-width: 50px;
  min-height: 35px;
}
.numeric-input .numeric-input-title {
  font-size: 14px;
}
.ant-popover-inner-content {
  padding: 0;
}
#components-form-demo-normal-login .login-form {
  max-width: 350px;
}
#components-form-demo-normal-login .login-form-forgot {
  float: right;
  margin-bottom: 10px;
  margin-top: -15px;
}
#components-form-demo-normal-login .login-form-button {
  width: 100%;
}

.countdown-wrapper {
  display: flex;
  align-items: center;
  background-color: transparent;
  padding: 10px 20px;
  border-radius: 8px;
  color: white;
  font-family: 'Prompt', sans-serif;
}
.countdown-wrapper-mobile {
  display: flex;
  align-items: center;
  background-color: transparent;
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-family: 'Prompt', sans-serif;
}

.label {
  margin-right: 10px;
  font-size: 16px;
  white-space: nowrap;
  font-weight: 500;
}

.label-mobile {
  margin-right: 10px;
  font-size: 12px;
  white-space: nowrap;
  font-weight: 500;
}

.time-box {
  background-color: #333333;
  padding: 8px 10px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 700;
  max-width: 42px;
  max-height: 40px;
  text-align: center;
  font-weight: 700;
}

.time-box-mobile {
  background-color: #333333;
  padding: 4px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  max-width: 25px;
  max-height: 25px;
  text-align: center;
  font-weight: 700;
}

.colon {
  margin: 0 5px;
  font-size: 20px;
  font-weight: bold;
}

.colon-mobile {
  margin: 0 5px;
  font-size: 16px;
  font-weight: bold;
}
