<template>
  <v-container :class="MobileSize ? 'background_color_Mobile' : 'background_color'" grid-list-xs rounded class="pa-0">
    <v-card class="pa-6" elevation="0">
      <v-col>
        <v-row class="pb-6">
          <span style="font-size: 24px; line-height: 32px; font-weight: 700;">แบ่งออเดอร์</span>
        </v-row>
        <v-row class="pb-6">
          <v-card style="display: flex; align-items: center; justify-content: center; background-color: #F5F5F5;" class="pa-1 pr-3 rounded-pill" elevation="0">
            <v-img src="@/assets/iShip/Frame 1000003421.png" style="height: 28px; width: 28px;"></v-img>
            <span class="pl-2" style="font-size: 18px; line-height: 26px; font-weight: 700;">รหัสการสั่งซื้อ : {{ this.orderNumber }}</span>
          </v-card>
        </v-row>
        <v-row class="pb-6">
          <span style="font-size: 18px; font-weight: 700; line-height: 24px; color: #333333;">รายการสินค้าทั้งหมด {{ getAllOrder }} ชิ้น <span style="font-size: 16px; font-weight: 600; line-height: 24px; color: #F5222D;">(กล่องสินค้าสูงสุด {{ getAllOrder }} กล่อง)</span></span>
        </v-row>
        <v-row class="pb-6">
          <v-col class="px-0">
            <v-data-table
            :items="dataAlpha"
            :headers="headers"
            :item-class="() => 'forborderess'"
            hide-default-footer
            style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.185)); border-radius: 9px;"
            >
            <template v-slot:[`item.detail`]="{ item }">
              <v-row>
                <v-col :cols="MobileSize || IpadProSize || IpadSize ? 3 : 2 " class="px-0" style="display: flex; justify-content: center;">
                  <v-card style="height: 118px; width: 118px; display: grid; align-content: center; justify-content: center; align-items: center;" elevation="0">
                    <v-img :src="item.product_image" style="width: 86px;"></v-img>
                  </v-card>
                </v-col>
                <v-col :cols="MobileSize || IpadProSize || IpadSize ? 9 : 10 " style="display: inline-grid; align-content: center; justify-content: start;">
                  <div style="display: inline-flex; align-items: center;">
                    <span style="font-size: 16px; font-weight: 600; line-height: 24px; padding-top: 2px;">รหัสสินค้า </span>
                    <span v-if="item.sku.slice(item.sku.length - 11) === '(สินค้าแถม)'" style="font-size: 18px; line-height: 26px; font-weight: 700; padding-inline-start: 4px;">: {{ item.sku.slice(0, item.sku.length - 11) }}</span>
                    <span v-else style="font-size: 18px; line-height: 26px; font-weight: 700; padding-inline-start: 4px;">: {{ item.sku }}</span>
                    <!-- ห้ามลบ -->
                    <v-card v-if="item.sku.slice(item.sku.length - 11) === '(สินค้าแถม)'" style="display: inline-flex; place-content: center; justify-content: center; background-color: #DAF1E966 ;" class="ml-2 py-1 px-2 rounded-pill" elevation="0">
                      <v-img src="@/assets/iShip/gift1.png" style="height: 20px; width: 20px;"></v-img>
                      <span class="pl-2 " style="font-size: 14px; line-height: 22px; font-weight: 500; color: #27AB9C;">สินค้าแถม</span>
                    </v-card>
                    <br>
                  </div>
                  <span style="font-size: 18px; line-height: 26px; font-weight: 700;">{{ item.product_name }} <br></span>
                  <div style="display: inline-flex; align-items: center; justify-content: start;">
                    <span style="font-size: 16px; line-height: 24px; font-weight: 400;">{{ item.key_1_value }} : </span><span style="font-size: 16px; line-height: 24px; font-weight: 600; padding-inline-start: 4px;"> {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                  </div>
                </v-col>
              </v-row>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
        <v-divider class="px-0 pb-6"></v-divider>
        <v-container class="px-0" v-for="(cart, indexx) in GamMaInfo" :key="indexx">
          <v-row class="pb-3 pt-5">
            <v-col cols="5" class="pl-0" style="display: inline-flex; align-items: baseline;">
              <v-icon color="#27AB9C" size="21px"> mdi mdi-package-variant-closed </v-icon>
              <span class="pl-2" style="font-weight: 700; font-size: 17px; line-height: 24px; color: #27AB9C; min-width: 80px;">กล่องที่ {{ indexx + 1 }}</span>
              <v-select
                class="pl-2"
                :items="beTaInfo"
                item-disabled="disable"
                v-model="productToCart"
                @change="selectProduct(indexx)"
                @mousedown="checkBeForSelect(indexx)"
                item-text="pro_name_attribute"
                item-value="pro_sku_ex"
                :menu-props="{ bottom: true,  offsetY: true }"
                placeholder="กรุณาเลือกสินค้า"
                outlined
                dense
                hide-details
                :disabled="checkLastest(indexx)"
              >
              </v-select>
            </v-col>
            <v-col cols="3" offset="4" class="pr-0" style="display: inline-flex; justify-content: flex-end; align-items: baseline;">
              <v-btn v-if="indexx !== 0" @click="madeInHeaven(indexx)" :disabled="checkLastest(indexx)" elevation="0" rounded style="background-color: #FFFFFF;"><v-icon color="#A1A1A1" size="24px" class="pr-2"> mdi mdi-delete-outline </v-icon><span style="font-size: 16px; line-height: 24px; font-weight: 400; color: #333333;">ลบกล่องสินค้า</span></v-btn>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="pa-0">
              <v-card v-if="GamMaInfo[indexx] === undefined || GamMaInfo[indexx] === null || GamMaInfo[indexx].length === 0" style="background-color: #FAFAFA; min-height: 132px; display: flex; align-items: center; justify-content: center; flex-direction: column;" rounded elevation="0">
                <v-img src="@/assets/iShip/Divideorder.png" style="max-height: 70px; max-width: 70px;" class="mb-2"></v-img>
                <span style="font-size: 14px; line-height: 22px; font-weight: 400; color: #9A9A9A;">ยังไม่มีสินค้า</span>
              </v-card>
              <!-- --------------------------------------------------------------------------------------------------------------------- -->
              <v-data-table
              v-else
              :items="GamMaInfo[indexx]"
              :headers="headers2"
              :item-class="() => 'forborderess'"
              hide-default-footer
              style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.185)); border-radius: 9px;"
              >
              <template v-slot:[`item.detail`]="{ item }">
                <v-row>
                  <v-col :cols="MobileSize || IpadProSize || IpadSize ? 4 : 3 " class="px-0" style="display: flex; justify-content: center;">
                    <v-card style="height: 118px; width: 118px; display: grid; align-content: center; justify-content: center; align-items: center;" elevation="0">
                      <v-img :src="item.cart_pro_img" style="width: 86px;"></v-img>
                    </v-card>
                  </v-col>
                  <v-col :cols="MobileSize || IpadProSize || IpadSize ? 8 : 9 " style="display: inline-grid; align-content: center; justify-content: start;">
                    <div style="display: inline-flex; align-items: center;">
                      <span style="font-size: 16px; font-weight: 600; line-height: 24px; padding-top: 2px;">รหัสสินค้า </span>
                      <span v-if="item.cart_sku.slice(item.cart_sku.length - 11) === '(สินค้าแถม)'" style="font-size: 18px; line-height: 26px; font-weight: 700; padding-inline-start: 4px;">: {{ item.cart_sku.slice(0, item.cart_sku.length - 11) }}</span>
                      <span v-else style="font-size: 18px; line-height: 26px; font-weight: 700; padding-inline-start: 4px;">: {{ item.cart_sku }}</span>
                      <!-- ห้ามลบ -->
                      <v-card v-if="item.cart_sku.slice(item.cart_sku.length - 11) === '(สินค้าแถม)'" style="display: inline-flex; place-content: center; justify-content: center; background-color: #DAF1E966 ;" class="ml-2 py-1 px-2 rounded-pill" elevation="0">
                        <v-img src="@/assets/iShip/gift1.png" style="height: 20px; width: 20px;"></v-img>
                        <span class="pl-2 " style="font-size: 14px; line-height: 22px; font-weight: 500; color: #27AB9C;">สินค้าแถม</span>
                      </v-card>
                      <br>
                    </div>
                    <span style="font-size: 18px; line-height: 26px; font-weight: 700;">{{ item.cart_pro_name }} <br></span>
                    <div style="display: inline-flex; align-items: center; justify-content: start;">
                      <span style="font-size: 16px; line-height: 24px; font-weight: 400;">{{ item.cart_key1 }} : </span><span style="font-size: 16px; line-height: 24px; font-weight: 600; padding-inline-start: 4px;"> {{ item.cart_attribute1 }}</span>
                    </div>
                  </v-col>
                </v-row>
                </template>
                <template v-slot:[`item.product_quantity`]="{ item }">
                  <v-col :key="componentKey">
                    <v-row style="justify-content: center;">
                      <v-btn icon @click="selectMinus(item, indexx)" :disabled="checkLastest(indexx)">
                        <v-icon>mdi mdi-minus-circle</v-icon>
                      </v-btn>
                      <v-text-field class="centered-input mx-1" type="number" dense outlined style="max-width: 55px; " :disabled="checkLastest(indexx)" @change="checkInput(item, indexx)" v-model.number="item.cart_num" hide-details oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" hide-spin-buttons></v-text-field>
                      <v-btn :disabled="plusProof(item) || checkLastest(indexx)" icon @click="selectPlus(item)">
                        <v-icon>mdi mdi-plus-circle</v-icon>
                      </v-btn>
                    </v-row>
                  </v-col>
                </template>
                <template v-slot:[`item.product_manage`]="{ item }">
                  <v-btn @click="redempTionProduct(item, indexx)" :disabled="checkLastest(indexx)" style="min-width: max-content; padding-inline: 6px; padding-top: 4px; padding-bottom: 4px; background-color: #ffffff; filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.185)); border-radius: 3px;" elevation="0"><v-icon color="#A1A1A1">mdi mdi-delete-outline</v-icon></v-btn>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </v-container>
        <v-col cols="3" offset="4" class="mt-5 pr-0" style="display: inline-flex; justify-content: flex-end; align-items: baseline;">
          <v-btn elevation="0" :disabled="checkLastestbox()" @click="plusBox" text color="#27AB9C"><v-icon color="#27AB9C" size="21px" class="pr-2"> mdi mdi-plus-circle-outline </v-icon><span style="font-size: 16px; line-height: 24px; font-weight: 700;">เพิ่มกล่องสินค้า</span></v-btn>
        </v-col>
        <v-row class="pt-6">
          <v-col style="display: flex; justify-content: flex-end;">
            <v-row dense justify="end" >
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="backToBefore()" class="mr-2" style="font-weight: 700; font-size: 16px;">ยกเลิก</v-btn>
              <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="dialogConfirmSplitOrder = true" :disabled="checkEmptyBox()" style="font-weight: 700; font-size: 16px;">บันทึก</v-btn>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-card>
    <v-dialog v-model="dialogConfirmSplitOrder" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แบ่งออร์เดอร์
          </span>
           <v-btn icon dark @click="dialogConfirmSplitOrder = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: 18px;">คุณได้ทำการแบ่งออเดอร์สินค้า</span><br>
              <span style="font-size: 18px;"><b>"รหัส {{ this.orderNumber }}"</b></span><br>
              <span style="font-size: 18px;">คุณต้องการที่จะแบ่งออเดอร์สินค้า ใช่ หรือ ไม่ ?</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogConfirmSplitOrder = false" class="mr-2" style="font-weight: 700; font-size: 16px;">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmData()" style="font-weight: 700; font-size: 16px;">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="472" persistent>
      <v-card style="background: #FFFFFF; border-radius: 4px;" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center ml-9" style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">
            แบ่งออเดอร์
          </span>
          <v-btn icon dark @click="closeDialog()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense justify="center">
            <v-col cols="12" align="center" class="py-4">
              <!-- <v-icon color="#27AB9C" size="70px">mdi-check-circle</v-icon> -->
              <v-img  :src="require('@/assets/iShip/FrameSusses.png')" loading="lazy"  width="164px"  height="139px" ></v-img>
              <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #012A73;" class="pt-4">เสร็จสิ้น</p>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- <pre>
      <v-row>
        <v-col cols="6">
          {{ GamMaInfo }}
        </v-col>
        <v-col cols="6">
          {{ beTaInfo }}
        </v-col>
      </v-row>
    </pre> -->
    <!-- {{ this.dataAll.order_number }} -->
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      dataAlpha: [],
      beTaInfo: [],
      GamMaInfo: [[]],
      headers: [
        { text: 'รายละเอียดสินค้า', value: 'detail', sortable: false, align: 'start', class: 'headerSt1' },
        { text: 'จำนวน', value: 'quantity', sortable: false, align: 'center', width: '200', class: 'headerSt2' }
      ],
      headers2: [
        { text: 'รายละเอียดสินค้า', value: 'detail', sortable: false, align: 'start', class: 'headerSt1' },
        { text: 'จำนวน', value: 'product_quantity', sortable: false, align: 'center', width: '180', class: 'headerSt2' },
        { text: 'จัดการ', value: 'product_manage', sortable: false, align: 'center', width: '150', class: 'headerSt2' }
      ],
      getAllOrder: 0,
      orderNumber: null,
      dialogConfirmSplitOrder: false,
      dialogSuccess: false,
      productToCart: null,
      componentKey: 0,
      dataAll: [],
      newsId: null,
      indexAddBox: 0,
      routeNameBefore: null
    }
  },
  created () {
    this.$EventBus.$emit('SelectPath')
    this.$EventBus.$emit('checkAuthUser')
    this.newsId = this.$route.params.Id
    console.log(this.newsId)
    // console.log(this.$route.name)
    this.routeNameBefore = localStorage.getItem('routeNameBefore')
    console.log(this.routeNameBefore, 'routeNameBefore')
    this.getData()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  mounted () {
  },
  update () {
    console.log('llllll')
  },
  methods: {
    backToBefore () {
      var path = '/' + this.routeNameBefore
      this.$router.push({ path: path }).catch(() => { })
    },
    forceRerender () {
      this.componentKey += 1
    },
    checkInput (item, index) {
      var a = this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].max_num
      var p = 0
      for (let i = 0; i < (this.GamMaInfo.length) - 1; i++) {
        this.GamMaInfo[i].forEach(r => {
          if (r.cart_sku_ex === item.cart_sku_ex) {
            p = p + r.cart_num
          }
        })
      }
      if (item.cart_num === '' || item.cart_num === null || item.cart_num === undefined) {
        this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num = parseInt(a - (p + 1))
        item.cart_num = parseInt(1 + item.cart_num)
        // console.log(item.cart_num, 'ci-1')
      } else {
        if (item.cart_num > a) {
          this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num = 0
          item.cart_num = parseInt(a - p)
          // console.log(item.cart_num, 'ci-2.1')
        } else if (item.cart_num < a) {
          // this.GamMaInfo[index].filter(e => e.cart_sku_ex === item.cart_sku_ex)[0].cart_num = parseInt(p) !== 0 ? parseInt(p) : 1
          this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num = parseInt(a) - (parseInt(p) + parseInt(item.cart_num))
          if (parseInt(item.cart_num) === 0 || parseInt(item.cart_num) < 0) {
            item.cart_num = '1'
          }

          // this.GamMaInfo[index].filter(e => e.cart_sku_ex === item.cart_sku_ex)[0].cart_num = (parseInt(p) + parseInt(item.cart_num)) !== 0 ? (parseInt(p) + parseInt(item.cart_num)) : 1
          this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num = a - (parseInt(p) + parseInt(item.cart_num))
          item.cart_num = parseInt(item.cart_num)
          // console.log(item.cart_num + p, 'ci-2.2')
        } else {
          this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num = 0
          item.cart_num = parseInt(a - p)
          // console.log(item.cart_num, 'ci-2.3')
        }
      }
      this.forceRerender()
    },
    checkEmptyBox () {
      // console.log(!this.GamMaInfo.every(e => e.length !== 0))
      if (this.GamMaInfo.every(e => e.length !== 0) && this.GamMaInfo.length > 1 && this.beTaInfo.every(v => v.num === 0)) {
        return false
      } else {
        return true
      }
    },
    checkLastest (index) {
      var a = this.GamMaInfo.length - 1
      if (index === a) {
        return false
      } else {
        return true
      }
    },
    checkLastestbox () {
      if (this.getAllOrder > this.GamMaInfo.length) {
        var a = this.GamMaInfo.length - 1
        var b = this.beTaInfo.every(e => e.num === 0)
        // console.log('box', b)
        if (b) {
          return true
        } else {
          if (this.GamMaInfo[a].length === 0) {
            return true
          } else {
            return false
          }
        }
      } else {
        return true
      }
    },
    madeInHeaven (index) {
      var a = this.GamMaInfo[index]
      a.forEach(e => {
        var c = e.cart_num
        console.log(c)
        this.beTaInfo.filter(d => d.pro_sku_ex === e.cart_sku_ex)[0].num += c
        this.beTaInfo.filter(d => d.pro_sku_ex === e.cart_sku_ex)[0].disable = false
      })
      this.GamMaInfo.splice(index, 1)
    },
    redempTionProduct (item, index) {
      var a = item.cart_num
      this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num = this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)[0].num + parseInt(a)
      var b = this.GamMaInfo[index].indexOf(item)
      this.GamMaInfo[index].splice(b, 1)
    },
    plusProof (item) {
      var allProductInBoxs = 0
      // console.log(item.cart_sku_ex, '124524')
      this.GamMaInfo.forEach(e => {
        e.forEach(v => {
          if (v.cart_sku_ex === item.cart_sku_ex) {
            allProductInBoxs = v.cart_num + allProductInBoxs
          }
        })
      })
      console.log(item.cart_sku_ex)
      var maxOfProduct = this.beTaInfo.filter(g => g.pro_sku_ex === item.cart_sku_ex)[0].max_num
      if (allProductInBoxs === maxOfProduct) {
        return true
      }
    },
    plusBox () {
      this.GamMaInfo.push([])
    },
    selectPlus (item) {
      var selectListWithSameSku = this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)
      var indexOfSameSku = this.beTaInfo.indexOf(selectListWithSameSku[0])
      item.cart_num = parseInt(item.cart_num + 1)
      this.beTaInfo[indexOfSameSku].num = parseInt(this.beTaInfo[indexOfSameSku].num - 1)
      if (this.beTaInfo[indexOfSameSku].num === 0) {
        this.beTaInfo[indexOfSameSku].disable = true
      }
    },
    selectMinus (item, index) {
      var selectListWithSameSku = this.beTaInfo.filter(e => e.pro_sku_ex === item.cart_sku_ex)
      var indexOfSameSku = this.beTaInfo.indexOf(selectListWithSameSku[0])
      item.cart_num = parseInt(item.cart_num - 1)
      if (item.cart_num === 0) {
        this.GamMaInfo[index].splice(this.GamMaInfo[index].indexOf(item), 1)
      }
      if (this.beTaInfo[indexOfSameSku].num === 0) {
        this.beTaInfo[indexOfSameSku].disable = false
      }
      this.beTaInfo[indexOfSameSku].num = parseInt(this.beTaInfo[indexOfSameSku].num + 1)
    },
    checkBeForSelect (indexx) {
      var targetBoxIndex = this.GamMaInfo[indexx]
      var selectList = this.beTaInfo
      if (targetBoxIndex.length !== 0) {
        selectList.forEach(e => {
          targetBoxIndex.forEach(x => {
            if (e.pro_sku_ex === x.cart_sku_ex) {
              e.disable = true
            } else if (e.pro_sku_ex !== x.cart_sku_ex && e.num > 0) {
              console.log(e.pro_sku_ex, x.cart_sku_ex, 'excec')
              e.disable = false
            }
          })
        })
      } else {
        selectList.forEach(y => {
          if (y.num > 0) {
            y.disable = false
          } else {
            y.disable = true
          }
        })
      }
    },
    async selectProduct (index) {
      var selectedItem = this.beTaInfo.filter(e => e.pro_sku_ex === this.productToCart)
      var indexOfSelectedItem = this.beTaInfo.indexOf(selectedItem[0])
      var selectedItemForPush = selectedItem.map(e => {
        return {
          cart_sku_ex: e.pro_sku_ex,
          cart_main_sku_ex: e.pro_main_sku_ex,
          cart_id: e.pro_id,
          cart_sku: e.pro_sku,
          cart_main_sku: e.pro_main_sku,
          cart_pro_img: e.pro_img,
          cart_pro_name: e.pro_name,
          cart_key1: e.key1,
          cart_key2: e.key2,
          cart_attribute1: e.attribute1,
          cart_attribute2: e.attribute2,
          cart_num: e.num
        }
      })
      await this.GamMaInfo[index].push(selectedItemForPush[0])
      this.beTaInfo[indexOfSelectedItem].num = 0
      this.beTaInfo[indexOfSelectedItem].disable = true
      this.productToCart = null
      this.forceRerender()
    },
    async getData () {
      const data = await {
        id: this.newsId,
        type: this.routeNameBefore === 'manageShipping' ? 'transports_order' : 'transports_prepare_order'
      }
      await this.$store.dispatch('actionsGetDaTa', data)
      const response = await this.$store.state.NSGModuleIship.stateDaTa
      // console.log(response.data.couriers)
      if (response.result === 'Success') {
        this.dataAlpha = response.data.products
        this.orderNumber = response.data.order_number
        this.dataAll = response.data
        this.beTaInfo = this.dataAlpha.map((e, i) => {
          // console.log(e, 'ff9899')
          return {
            pro_sku_ex: e.sku + i,
            pro_main_sku_ex: e.main_sku + i,
            pro_id: e.product_id,
            pro_sku: e.sku,
            pro_main_sku: e.main_sku,
            pro_img: e.product_image,
            pro_name: e.product_name,
            pro_name_attribute: e.product_attribute_detail.attribute_priority_2 === null ? e.product_name + ' (' + e.product_attribute_detail.attribute_priority_1 + ')' : e.product_name + ' (' + e.product_attribute_detail.attribute_priority_1 + ', ' + e.product_attribute_detail.attribute_priority_2 + ')',
            key1: e.key_1_value,
            key2: e.key_2_value,
            attribute1: e.product_attribute_detail.attribute_priority_1,
            attribute2: e.product_attribute_detail.attribute_priority_2,
            num: e.quantity,
            max_num: e.quantity,
            disable: false
          }
        })
        // for (let i = 0; i < this.dataAlpha.length; i++) {
        //   const e = this.dataAlpha[i]
        //   e.pro_sku_ex = e.sku + i
        //   e.pro_name_attribute = e.product_attribute_detail.attribute_priority_2 === null ? e.product_name + ' (' + e.product_attribute_detail.attribute_priority_1 + ')' : e.product_name + ' (' + e.product_attribute_detail.attribute_priority_1 + ', ' + e.product_attribute_detail.attribute_priority_2 + ')'
        // }
        console.log(this.beTaInfo)
        for (let i = 0; i < this.dataAlpha.length; i++) {
          this.getAllOrder += this.dataAlpha[i].quantity
        }
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          await this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      }
    },
    async confirmData () {
      // apiเพื่อแบ่งออร์เดอร์
      this.$store.commit('openLoader')
      this.dialogConfirmSplitOrder = false
      var split = this.GamMaInfo.map(e => {
        return e.map(x => {
          return {
            product_id: x.cart_id,
            product_name: x.cart_pro_name,
            main_sku: x.cart_main_sku,
            main_sku_index: x.cart_main_sku_ex,
            sku: x.cart_sku,
            product_image: x.cart_pro_img,
            quantity: x.cart_num
          }
        })
      })
      console.log(split, 'split')
      var data
      var response
      if (this.routeNameBefore === 'manageShipping') {
        data = {
          order_number: this.dataAll.order_number,
          transport_order_id: this.dataAll.transports_order_id,
          remark: this.dataAll.remark,
          seller_shop_id: 1,
          product_split: split,
          type: 'split_order'
        }
        console.log(data)
        await this.$store.dispatch('actionsSplitOrder', data)
        response = await this.$store.state.NSGModuleIship.stateSplitOrder
      } else {
        data = {
          order_number: this.dataAll.order_number,
          prepare_order_id: this.dataAll.prepare_order_id,
          seller_shop_id: 1,
          product_split: split
        }
        console.log(data)
        await this.$store.dispatch('actionsCutOrder', data)
        response = await this.$store.state.NSGModuleIship.stateCutOrder
      }
      if (response.result === 'Success') {
        this.$store.commit('closeLoader')
        this.dialogSuccess = true
        setTimeout(() => {
          this.dialogSuccess = false
          if (this.routeNameBefore === 'manageShipping') {
            this.$router.push({ path: '/manageShipping' })
          } else {
            this.$router.push({ path: '/createShipping' })
          }
        }, 2000)
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
        }
      }
    }
  }
}
</script>

<style scoped>
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DAF1E9;
  border-radius: 8px;
}
.centered-input >>> input {
  text-align: center
}
</style>

<style>
.forborderess {
  border-bottom-style: hidden !important;
  background: none !important;
}
.headerSt1 {
  background-color: #F3F4F8 !important;
  border-top-left-radius: 9px;
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #333333 !important;
}
.headerSt2 {
  background-color: #F3F4F8 !important;
  border-top-right-radius: 9px;
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #333333 !important;
}
</style>
