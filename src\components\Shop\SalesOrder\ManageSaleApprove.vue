<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">{{ approvalTitle }}</v-card-title>
      <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> {{ approvalTitle }}</v-card-title>
      <v-card-text>
        <v-row dense class="mt-2">
          <v-col cols="12" md="6" sm="7">
            <v-text-field v-model="search" dense hide-details outlined placeholder="ค้นหาจากรูปแบบการอนุมัติ ชื่อ-นามสกุล อีเมลหรือเบอร์โทรศัพท์" style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" sm="5" align="end" :class="MobileSize ? 'pt-4' : ''">
            <v-btn rounded height="40" class="white--text" :block="MobileSize" color="#27AB9C" @click="addSaleApprove()"><v-icon color="" class="pr-2">mdi-plus</v-icon> เพิ่มรายชื่อ</v-btn>
          </v-col>
        </v-row>
        <v-row dense class="mt-4" v-if="showTable === true">
          <v-col cols="12" class="mb-4">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">{{ list }} {{ showCountOrder }} รายการ</span>
          </v-col>
          <v-col cols="12" md="12" sm="12" xs="12">
            <v-card outlined class="mb-4">
              <v-data-table
              :headers="headersListSale"
              :items="DataTable"
              :items-per-page="10"
              :page.sync="page"
              :search="search"
              @pagination="countSale"
              no-results-text="ไม่พบข้อมูลจากคำที่ค้นหา"
              no-data-text="ไม่มีข้อมูลในตาราง"
              :update:items-per-page="getItemPerPage"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              >
                <template v-slot:[`item.actions`]="{ item }">
                  <v-row dense>
                    <v-col cols="6">
                      <v-btn
                      x-small
                      @click="editSale(item)"
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      outlined
                      class="pt-4 pb-4"
                      :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                      >
                        <v-icon color="#27AB9C" :size="18">mdi-pencil-outline</v-icon>
                      </v-btn>
                    </v-col>
                    <!-- <v-col cols="6">
                      <v-btn
                      x-small
                      @click="confirmSettingAdmin('deleteBuyerApprove', item)"
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      outlined
                      class="pt-4 pb-4"
                      :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                      >
                        <v-icon color="#27AB9C" :size="18">mdi-delete-outline</v-icon>
                      </v-btn>
                    </v-col> -->
                  </v-row>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
        </v-row>
        <v-row no-gutters v-if="showTable === false">
          <v-col cols="12" md="12" sm="12" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>{{ noData }}</b></h2>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- Modal เพิ่มลำดับ Sale -->
    <v-dialog v-model="modalAddSaleApprove" width="750px" persistent>
      <v-card width="100%" height="100%" elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;"  class="rounded-lg">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'white--text' : 'white--text'" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'"><b>{{ clickSelectSale === false ? addData : settingData }}</b></span>
              </v-col>
              <v-btn fab small @click="CloseModalAddBuyer('close')" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;"></v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" class="mr-0">
                    <v-row dense>
                      <v-img src="@/assets/ICON/Buyer.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">{{ selectDetail }}</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>{{ selectDetail }}</p>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" align="start">
                    <v-form ref="FormSetSaleApprove" :lazy-validation="lazy">
                      <v-card-text>
                        <v-row justify="center" align-content="center" :class="MobileSize ? '' : ''" v-if="clickSelectSale === false">
                          <v-col cols="12" :class="MobileSize ? '' : ''">
                            <p>{{ searchSaleApprove }}</p>
                            <v-text-field class="rounded-lg" v-model="searchSale" @keydown.enter.prevent="submit" :placeholder="searchSaleApprove" outlined dense hide-details>
                              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                            </v-text-field>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? '' : ''" v-if="notfoundSale === false" style="max-height: 250px;" :style="{'overflow-y': filteredList.length === 0 ? 'hidden' : 'scroll'}">
                            <div v-for="(item, index) in filteredList" :key="index">
                              <v-card width="100%" height="100%" elevation="4" style="border: 1px solid #F3F5F7; border-radius: 8px; cursor: pointer;" outlined class="mb-4" @click="selectSale(item)">
                                <div class="d-flex flex-no-wrap">
                                  <v-avatar class="ma-4" :size="MobileSize ? '60' : '132'" tile>
                                    <v-img :src="item.img_path" contain style="border-radius: 8px;" v-if="item.img_path !== null"></v-img>
                                    <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                                  </v-avatar>
                                  <div class="ma-4">
                                    <p>ชื่อ - สกุล : <b>{{ item.name }}</b></p>
                                    <p>อีเมล : <b>{{ item.email }}</b></p>
                                    <p>เบอร์โทรศัพท์ : <b>{{ item.phone }}</b></p>
                                  </div>
                                </div>
                              </v-card>
                            </div>
                          </v-col>
                          <v-col cols="12" class="px-12" v-if="filteredList.length === 0 && searchSale !== ''" align="center">
                            <div style="padding-top: 30px;">
                              <v-img src="@/assets/icons/notfoundAdminUser.png" max-height="146px" max-width="135px" height="100%" width="100%" contain></v-img>
                            </div>
                            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #333333;">
                              <span style="font-weight: bold; font-size: 24px; line-height: 30px;">ไม่พบผู้ใช้งานนี้ในรายชื่อผู้ซื้อองค์กร หรือ ผู้ใช้งานนี้ถูกใช้งานแล้ว</span><br/>
                            </h2>
                          </v-col>
                        </v-row>
                        <v-row justify="center" align-content="center" :class="MobileSize ? '' : 'px-12'" v-else>
                          <v-col cols="12" :class="MobileSize ? '' : 'px-12'">
                            <v-card outlined>
                              <v-card-text class="px-0">
                                <div class="d-flex flex-no-wrap">
                                  <v-avatar class="ma-4" :size="MobileSize ? '60' : '132'" tile>
                                    <v-img :src="selectSaleItem.img_path" contain style="border-radius: 8px;" v-if="selectSaleItem.img_path !== null "></v-img>
                                    <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                                  </v-avatar>
                                  <div class="ma-4">
                                    <p>ชื่อ-สกุล : <b>{{ selectSaleItem.name }}</b></p>
                                    <p>อีเมล : <b>{{ selectSaleItem.email }}</b></p>
                                    <p>เบอร์โทรศัพท์ : <b>{{ selectSaleItem.phone }}</b></p>
                                    <p class="mb-0">รูปแบบการอนุมัติ <span style="color: red;">*</span> :
                                      <v-select
                                        v-model="select"
                                        :items="listTypeApprove"
                                        item-text="name"
                                        item-value="id"
                                        placeholder="กรุณาเลือก"
                                        dense
                                        :style="MobileSize ? 'width: 190px !important;' : ''"
                                        outlined
                                        class="mt-2"
                                        :rules="Rules.selectType"
                                      ></v-select>
                                    </p>
                                    <p class="mb-0">รูปแบบการอนุมัติ (แก้ไขใบเสนอราคา) <span style="color: red;">*</span> :
                                      <v-select
                                        v-model="selectEdit"
                                        :items="listTypeApprove"
                                        item-text="name"
                                        item-value="id"
                                        placeholder="กรุณาเลือก"
                                        dense
                                        :style="MobileSize ? 'width: 190px !important;' : ''"
                                        outlined
                                        class="mt-2"
                                        :rules="Rules.selectType"
                                      ></v-select>
                                    </p>
                                  </div>
                                </div>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-form>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <v-card-actions>
            <v-row justify="center" dense style="height: 88px; background: #F5FCFB; " :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'" v-if="filteredList.length !== 0">
              <v-btn outlined rounded width="125" height="40" class="my-auto" color="#27AB9C" @click="CloseModalAddBuyer('close')" v-if="clickSelectSale === false">ยกเลิก</v-btn>
              <v-btn outlined rounded width="125" height="40" class="my-auto" color="#27AB9C" @click="CloseModalAddBuyer('back')" v-else>ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded width="125" height="40" dense class="white--text my-auto" color="#27AB9C" @click="confirmSettingAdmin('addSaleApprove', '')" :disabled="clickSelectSale === false ? true : false">บันทึก</v-btn>
            </v-row>
          </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal Edit ลำดับ Sale -->
    <v-dialog v-model="modalEditSaleApprove" width="750px" persistent>
      <v-card width="100%" height="100%" elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;"  class="rounded-lg">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'white--text' : 'white--text'" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'"><b>การตั้งค่ารายละเอียดผู้ซื้อ</b></span>
              </v-col>
              <v-btn fab small @click="CloseModalEditSale()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;"></v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" class="mr-0">
                    <v-row dense>
                      <v-img src="@/assets/ICON/Buyer.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">{{ selectDetail }}</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>{{ selectDetail }}</p>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" align="start">
                    <v-form ref="FormSetSaleApprove" :lazy-validation="lazy">
                      <v-row justify="center" align-content="center" :class="MobileSize ? '' : 'px-12'">
                        <v-col cols="12" :class="MobileSize ? '' : 'px-12'">
                          <v-card outlined >
                            <div class="d-flex flex-no-wrap">
                              <v-avatar class="ma-4" :size="MobileSize ? '60' : '132'" tile>
                                <v-img :src="editSaleItem.img_path" contain style="border-radius: 8px;" v-if="editSaleItem.img_path !== null "></v-img>
                                <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                              </v-avatar>
                              <div class="ma-4">
                                <p>ชื่อ-สกุล : <b>{{ editSaleItem.name }}</b></p>
                                <p>อีเมล : <b>{{ editSaleItem.email }}</b></p>
                                <p>เบอร์โทรศัพท์ : <b>{{ editSaleItem.phone }}</b></p>
                                <!-- <p class="mb-0">ตำแหน่ง : <b>{{ editSaleItem.username }}</b></p> -->
                                <p class="mb-0">รูปแบบการอนุมัติ <span style="color: red;">*</span> :
                                  <v-select
                                    v-model="select"
                                    :items="listTypeApprove"
                                    item-text="name"
                                    item-value="id"
                                    placeholder="กรุณาเลือก"
                                    :style="MobileSize ? 'width: 190px !important;' : ''"
                                    dense
                                    outlined
                                    class="mt-2"
                                    :rules="Rules.selectType"
                                  ></v-select>
                                </p>
                                <p class="mb-0">รูปแบบการอนุมัติ (แก้ไขใบเสนอราคา) <span style="color: red;">*</span> :
                                  <v-select
                                    v-model="selectEdit"
                                    :items="listTypeApprove"
                                    item-text="name"
                                    item-value="id"
                                    placeholder="กรุณาเลือก"
                                    dense
                                    :style="MobileSize ? 'width: 190px !important;' : ''"
                                    outlined
                                    class="mt-2"
                                    :rules="Rules.selectType"
                                  ></v-select>
                                </p>
                              </div>
                            </div>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-form>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <v-card-actions>
            <v-row justify="center" dense style="height: 88px; background: #F5FCFB; " :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-btn outlined rounded width="125" height="40" class="my-auto" color="#27AB9C" @click="CloseModalEditSale()">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded width="125" height="40" dense class="white--text my-auto" color="#27AB9C" @click="confirmSettingAdmin('editSaleApprove', '')">บันทึก</v-btn>
            </v-row>
          </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal ยืนยันการเพิ่ม/แก้ไข/ลบ ผู้ซื้อ -->
    <v-dialog v-model="modalConfirmSaleApprove" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CloseModalConfirmBuyer()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ statusSitting === 'addSaleApprove' || statusSitting === 'editSaleApprove' ? settingDetail : deleteDetail }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ statusSitting === 'addSaleApprove' ? selectSaleApprove : statusSitting === 'editSaleApprove' ? selectSaleEdit : selectSaleDelete }}</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CloseModalConfirmBuyer('add')" v-if="statusSitting === 'addSaleApprove'">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CloseModalConfirmBuyer('edit')" v-else-if="statusSitting === 'editSaleApprove'">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CloseModalConfirmBuyer('delete')" v-else>ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirm('add')" v-if="statusSitting === 'addSaleApprove'">ตกลง</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirm('edit')" v-else-if="statusSitting === 'editSaleApprove'">ตกลง</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirm('delete')" v-else>ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- DialogSuccess -->
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalComfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการสร้างร้านค้าเรียบร้อย</span> -->
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModalComfirm()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      search: '',
      showTable: false,
      showCountOrder: 0,
      listSales: [],
      modalAddSaleApprove: false,
      clickSelectSale: false,
      searchSale: '',
      notfoundSale: Boolean,
      lazy: false,
      selectSaleItem: [],
      select: '',
      selectEdit: '',
      listTypeApprove: [],
      Rules: {
        selectType: [
          v => !!v || 'กรุณาเลือกข้อมูล'
        ]
      },
      headersListSale: [
        { text: 'รูปแบบการอนุมัติ', value: 'approve_position_name', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'รูปแบบการอนุมัติ (แก้ไข)', value: 'approve_position_edit_name', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-นามสกุล', value: 'name', sortable: false, align: 'start', width: '160', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทรศัพท์', value: 'phone', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' }
      ],
      page: 1,
      DataTable: [],
      statusSitting: '',
      modalConfirmSaleApprove: false,
      modalEditSaleApprove: false,
      editSaleItem: [],
      deleteSaleItem: [],
      dialogSuccess: false
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    this.$EventBus.$on('getListSaleApprove', this.getListSaleApprove)
    this.getListSaleApprove()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    filteredList () {
      return this.listSales.filter(item => {
        const name = item.name ? item.name.toLowerCase() : ''
        const email = item.email ? item.email.toLowerCase() : ''
        const phone = item.phone ? item.phone.toLowerCase() : ''
        return name.includes(this.searchSale.toLowerCase()) ||
              email.includes(this.searchSale.toLowerCase()) ||
              phone.includes(this.searchSale.toLowerCase())
      })
      // return this.listSales.filter(listSales => {
      //   return listSales.name.toLowerCase().includes(this.searchSale.toLowerCase()) || listSales.email.toLowerCase().includes(this.searchSale.toLowerCase()) || listSales.phone.toLowerCase().includes(this.searchSale.toLowerCase())
      // })
    },
    approvalTitle () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'จัดการลำดับการอนุมัติคู่ค้า' : 'จัดการลำดับการอนุมัติฝ่ายขาย'
    },
    selectDetail () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'รายชื่อคู่ค้า' : 'รายชื่อฝ่ายขาย'
    },
    addData () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'เพิ่มข้อมูลคู่ค้า' : 'เพิ่มข้อมูลฝ่ายขาย'
    },
    settingData () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'ตั้งค่ารายละเอียดคู่ค้า' : 'ตั้งค่ารายละเอียดฝ่ายขาย'
    },
    list () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'รายการคู่ค้า' : 'รายการฝ่ายขาย'
    },
    noData () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'คุณยังไม่มีรายการคู่ค้า' : 'คุณยังไม่มีรายการฝ่ายขาย'
    },
    settingDetail () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'การตั้งค่ารายละเอียดคู่ค้า' : 'การตั้งค่ารายละเอียดฝ่ายขาย'
    },
    deleteDetail () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'ลบคู่ค้า' : 'ลบฝ่ายขาย'
    },
    // selectSale () {
    //   const isPartnerPage = this.$route.path.includes('Partner')
    //   return isPartnerPage ? 'เลือกคู่ค้า' : 'เลือกฝ่ายขาย'
    // },
    selectSaleEdit () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'คุณได้ทำการแก้ไข การตั้งค่ารายละเอียดคู่ค้า' : 'คุณได้ทำการแก้ไข การตั้งค่ารายละเอียดฝ่ายขาย'
    },
    selectSaleDelete () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'คุณได้ทำการลบคู่ค้า' : 'คุณได้ทำการลบฝ่ายขาย'
    },
    selectSaleApprove () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'คุณได้ทำการตั้งค่ารายละเอียดคู่ค้า' : 'คุณได้ทำการตั้งค่ารายละเอียดฝ่ายขาย'
    },
    searchSaleApprove () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'ค้นหาพนักงานอนุมัติคู่ค้า' : 'ค้นหาพนักงานฝ่ายขาย'
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async getListSaleApprove (type) {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        type: type !== undefined ? type : this.$router.currentRoute.path.includes('Partner') ? 'partner_order' : 'sale_order'
      }
      await this.$store.dispatch('actionsListPurchaserSale', data)
      const response = await this.$store.state.ModuleSaleOrder.stateListPurchaserSale
      if (response.message === 'List Approve Position Purchaser Success.') {
        this.DataTable = await response.data.list_purchaser
        this.showTable = true
        await this.getListApprove(type)
        this.$store.commit('closeLoader')
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.showTable = false
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
        }
      }
    },
    async getListApprove (type) {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        type: type !== undefined ? type : this.$router.currentRoute.path.includes('Partner') ? 'partner_order' : 'sale_order'
      }
      await this.$store.dispatch('actionsListPositionSale', data)
      const response = await this.$store.state.ModuleSaleOrder.stateListPositionSale
      if (response.message === 'Get List approve Position Success.') {
        this.listTypeApprove = response.data.position_list
        this.listTypeApprove.unshift({
          id: -1,
          name: 'ไม่มีผู้อนุมัติ'
        })
        this.$store.commit('closeLoader')
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
        }
      }
    },
    async addSaleApprove () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        type: this.$router.currentRoute.path.includes('Partner') ? 'partner_order' : 'sale_order'
      }
      await this.$store.dispatch('actionsListUserPurchaserForSelect', data)
      const response = await this.$store.state.ModuleSaleOrder.stateListUserPurchaserForSelect
      if (response.message === 'Get List Approver Success.') {
        this.listSales = response.data.list_aprrover
        this.notfoundSale = false
        this.clickSelectSale = false
        this.searchSale = ''
        this.select = ''
        this.selectEdit = ''
        this.modalAddSaleApprove = true
        this.$store.commit('closeLoader')
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
        }
      }
    },
    getMessage (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Data missing. Please check your parameter and try again.') {
        return 'ข้อมูลขาดหาย โปรดตรวจสอบและลองอีกครั้ง'
      } else if (msg === 'Shop not found.') {
        return 'ไม่พบร้านค้า'
      } else if (msg === 'Your data not found in this shop.') {
        return 'ไม่พบข้อมูลของคุณในร้านค้านี้'
      } else if (msg === 'Sales man not found.') {
        return 'ไม่พบข้อมูลฝ่ายขาย'
      } else if (msg === 'This Sales is already exist.') {
        return 'มีฝ่ายขายคนนี้ในร้านอยู่แล้ว'
      } else if (msg === 'Sale userID not found.') {
        return 'ไม่พบ UserID ของฝ่ายขายในระบบนี้'
      } else if (msg === 'Cannot create sales data.') {
        return 'ไม่สามารถสร้างข้อมูลฝ่ายขายได้'
      } else if (msg === 'Has customer in active status. Please select new sales to the customer first.') {
        return 'มีลูกค้าที่ active อยู่ไม่สามารถปิดใช้งานได้ กรุณาเลือกฝ่ายขายใหม่ให้กับลูกค้าก่อน'
      } else if (msg === 'Has customer in active status. Please inActive the customer status or select new sales to the customer first.') {
        return 'มีลูกค้าที่ active อยู่ไม่สามารถปิดใช้งานได้ ต้องทำการปิดสถานะการใช้งานของลูกค้าหรือเลือกฝ่ายขายใหม่ให้กับลูกค้าก่อน'
      } else if (msg === 'Status is Active or inActive only.') {
        return 'สถานะคือ Active หรือ inActive เท่านั้น'
      } else {
        return 'An error has occurred. Please try again in an hour or two.'
      }
    },
    CloseModalAddBuyer (val) {
      if (val === 'close') {
        this.modalAddSaleApprove = !this.modalAddSaleApprove
        this.searchSale = ''
        this.select = ''
        this.selectEdit = ''
        this.notfoundSale = null
        this.clickSelectSale = false
      } else if (val === 'back') {
        this.searchSale = ''
        this.clickSelectSale = false
        this.select = ''
        this.selectEdit = ''
      }
    },
    selectSale (item) {
      this.selectSaleItem = item
      this.clickSelectSale = true
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
    },
    countSale (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    confirmSettingAdmin (status, item) {
      this.statusSitting = status
      if (this.statusSitting === 'addSaleApprove' || this.statusSitting === 'editSaleApprove') {
        if (this.$refs.FormSetSaleApprove.validate(true)) {
          if (this.statusSitting === 'addSaleApprove') {
            this.modalAddSaleApprove = !this.modalAddSaleApprove
          } else {
            this.modalEditSaleApprove = !this.modalEditSaleApprove
          }
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
        } else {
          this.$nextTick(() => {
            const el = document.getElementsByClassName('error--text')
            if (el) {
              document
                .getElementsByClassName('error--text')[0]
                .scrollIntoView({ behavior: 'smooth', block: 'end' })
            }
          })
        }
      } else {
        this.deleteSaleItem = item
        this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
      }
    },
    async confirm (val) {
      this.$store.commit('openLoader')
      var data = ''
      if (val === 'add') {
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          user_id: this.selectSaleItem.user_id,
          approve_position_id: this.select,
          approve_position_edit_id: this.selectEdit,
          type: this.$router.currentRoute.path.includes('Partner') ? 'partner_order' : 'sale_order'
        }
        await this.$store.dispatch('actionsSetPurchaserSale', data)
        var responseAdd = await this.$store.state.ModuleSaleOrder.stateSetPurchaserSale
        if (responseAdd.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
          this.dialogSuccess = !this.dialogSuccess
        } else {
          this.$store.commit('closeLoader')
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
          this.$swal.fire({ icon: 'error', text: this.getMessage(responseAdd.message), showConfirmButton: false, timer: 1500 })
        }
      } else if (val === 'edit') {
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          approve_purchaser_id: this.editSaleItem.approve_purchaser_id,
          approve_position_id: this.select,
          approve_position_edit_id: this.selectEdit
        }
        await this.$store.dispatch('actionsEditPurchaserSale', data)
        var responseEdit = await this.$store.state.ModuleSaleOrder.stateEditPurchaserSale
        if (responseEdit.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
          this.dialogSuccess = !this.dialogSuccess
        } else {
          this.$store.commit('closeLoader')
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
          this.$swal.fire({ icon: 'error', text: this.getMessage(responseEdit.message), showConfirmButton: false, timer: 1500 })
        }
      } else {
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          approve_purchaser_id: this.deleteSaleItem.approve_purchaser_id
        }
        await this.$store.dispatch('actionsDeletePurchaserSale', data)
        var responseDelete = await this.$store.state.ModuleSaleOrder.stateDeletePurchaserSale
        if (responseDelete.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
          this.dialogSuccess = !this.dialogSuccess
        } else {
          this.$store.commit('closeLoader')
          this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
          this.$swal.fire({ icon: 'error', text: this.getMessage(responseDelete.message), showConfirmButton: false, timer: 1500 })
        }
      }
    },
    async closeModalComfirm () {
      this.dialogSuccess = !this.dialogSuccess
      await this.getListSaleApprove()
    },
    CloseModalEditSale () {
      this.$refs.FormSetSaleApprove.resetValidation()
      this.modalEditSaleApprove = !this.modalEditSaleApprove
    },
    editSale (item) {
      this.editSaleItem = item
      this.select = item.approve_position_id
      this.selectEdit = item.approve_position_edit_id
      this.modalEditSaleApprove = !this.modalEditSaleApprove
    },
    CloseModalConfirmBuyer (val) {
      if (val === 'add') {
        this.searchSale = ''
        this.select = ''
        this.selectEdit = ''
        this.notfoundSale = null
        this.clickSelectSale = false
        this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
      } else {
        this.modalConfirmSaleApprove = !this.modalConfirmSaleApprove
      }
    }
  }
}
</script>

<style>

</style>
