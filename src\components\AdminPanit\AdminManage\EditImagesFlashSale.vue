<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-row justify="start" class="pt-3 pb-3 px-3" v-if="MobileSize">
        <span class="pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
          <v-icon style="color: #27ab9c;" @click="backToHome()">mdi-chevron-left</v-icon> แก้ไขภาพ Flash Sale
        </span>
      </v-row>
      <v-row justify="space-between" class="pt-3 px-3" v-else>
        <span class="pl-4 pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
          <v-icon style="color: #27ab9c;" @click="backToHome()">mdi-chevron-left</v-icon> แก้ไขภาพ Flash Sale
        </span>
      </v-row>
      <v-row class="ma-1" v-if="this.Banner && (this.Banner|| this.Banner.path !== '')">
        <v-col cols="12">
          <v-icon>mdi-menu-right</v-icon>
          <span>ภาพ Bannner</span>
        </v-col>
        <v-col cols="12" style="position: relative; margin-top: -1vw;">
          <v-card
            class="mt-3 pa-3"
            elevation="0"
            :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
            @click="onPickFileBanner()"
          >
            <v-img :src="this.Banner.path || this.Banner" style="width: 100%; height: auto; border-radius: inherit;"></v-img>
            <v-btn
              text
              small
              fab
              style="position: absolute; top: 5px; right: 5px; z-index: 1; background-color: #f44336;"
              @click="removeImageBanner()"
            >
              <v-icon small color="#fff">mdi-delete</v-icon>
            </v-btn>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="ma-1" v-else>
        <v-col cols="12" md="12">
          <v-icon>mdi-menu-right</v-icon>
          <span>เพิ่มรูปภาพ Bannner สำหรับ Flash Sale</span>
          <v-card
            class="mt-3"
            elevation="0"
            :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
            @click="onPickFileBanner()"
          >
            <v-card-text>
              <v-row dense align="center" justify="center" style="cursor: pointer;">
                <v-file-input
                  v-model="DataImageBanner"
                  accept="image/jpeg, image/jpg, image/png"
                  @change="onFileSelectedBanner"
                  id="file_input_banner"
                  multiple
                  :clearable="false"
                  style="display:none"
                ></v-file-input>
                <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                    <v-col cols="12" md="12" align="center">
                      <v-img
                        src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                        width="280.34"
                        height="154.87"
                        contain
                      ></v-img>
                    </v-col>
                    <v-col cols="12" md="12" style="text-align: center;">
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                      <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 2460x840 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="ma-1" v-if="this.Logo && (this.Logo || this.Logo.path !== '')">
        <v-col cols="12">
          <v-icon>mdi-menu-right</v-icon>
          <span>ภาพ LoGo</span>
        </v-col>
        <v-col cols="12" style="position: relative; margin-top: -1vw;">
          <v-card
            class="mt-3 pa-3"
            elevation="0"
            :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
            @click="onPickFileLogo()"
          >
            <v-img :src="this.Logo.path || this.Logo" style="width: 100%; height: auto; border-radius: inherit;"></v-img>
            <v-btn
              text
              small
              fab
              style="position: absolute; top: 5px; right: 5px; z-index: 1; background-color: #f44336;"
              @click="removeImageLogo()"
              >
              <v-icon small color="#fff">mdi-delete</v-icon>
            </v-btn>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="ma-1" v-else>
        <v-col cols="12" md="12">
          <v-icon>mdi-menu-right</v-icon>
          <span>เพิ่มรูปภาพ LoGo สำหรับ Flash Sale </span>
          <v-card
            class="mt-3"
            elevation="0"
            :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
            @click="onPickFileLogo()"
          >
            <v-card-text >
              <v-row dense align="center" justify="center" style="cursor: pointer;">
                <v-file-input
                  v-model="DataImageLogo"
                  accept="image/jpeg, image/jpg, image/png"
                  @change="onFileSelectedLogo"
                  id="file_input_logo"
                  multiple :clearable="false"
                  style="display:none"
                >
                </v-file-input>
                <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                    <v-col cols="12" md="12" align="center">
                      <v-img
                        src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                        width="280.34"
                        height="154.87"
                        contain
                      ></v-img>
                    </v-col>
                    <v-col cols="12" md="12" style="text-align: center;">
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                      <span style="line-height: 16px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 600x300 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="ma-1" v-if="this.Background && (this.Background || this.Background.path !== '')">
        <v-col cols="12">
          <v-icon>mdi-menu-right</v-icon>
          <span>ภาพ Background</span>
        </v-col>
        <v-col cols="12" style="position: relative; margin-top: -1vw;">
          <v-card
            class="mt-3 pa-3"
            elevation="0"
            :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
            @click="onPickFileBackground()"
          >
            <v-img :src="this.Background.path || this.Background" style="width: 100%; height: auto; border-radius: inherit;"></v-img>
            <v-btn
              text
              small
              fab
              style="position: absolute; top: 5px; right: 5px; z-index: 1; background-color: #f44336;"
              @click="removeImageBackground()"
              >
              <v-icon small color="#fff">mdi-delete</v-icon>
            </v-btn>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="ma-1" v-else>
        <v-col cols="12" md="12">
          <v-icon>mdi-menu-right</v-icon>
          <span>เพิ่มรูปภาพ Background สำหรับ Flash Sale </span>
          <v-card
            class="mt-3"
            elevation="0"
            :style="theRedI ? 'border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
            @click="onPickFileBackground()"
          >
            <v-card-text >
              <v-row dense align="center" justify="center" style="cursor: pointer;">
                <v-file-input
                  v-model="DataImageBackground"
                  accept="image/jpeg, image/jpg, image/png"
                  @change="onFileSelectedBackground"
                  id="file_input_background"
                  multiple :clearable="false"
                  style="display:none"
                >
                </v-file-input>
                <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                    <v-col cols="12" md="12" align="center">
                      <v-img
                        src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                        width="280.34"
                        height="154.87"
                        contain
                      ></v-img>
                    </v-col>
                    <v-col cols="12" md="12" style="text-align: center;">
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                      <span style="line-height: 16px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1400x350 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row justify="end" style="padding: 20px">
        <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="backToHome()" class="mr-2">ยกเลิก</v-btn>
        <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="handleConfirm()">บันทึก</v-btn>
      </v-row>
    </v-card>

    <v-dialog v-model="dialogConfirmUpdate" width="464" persistent>
      <v-card style="background: #FFFFFF; border-radius: 12px;" height="100%">
        <v-toolbar align="center" color="#C8F3E5" dark dense elevation="0">
          <span class="flex text-center" style="font-weight: 700; font-size: 18px; line-height: 24px; color: #27AB9C;">
            แก้ไขรูปภาพ Flash Sale
          </span>
          <v-btn icon dark @click="dialogConfirmUpdate = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense justify="center">
            <v-col cols="12" align="center" class="mt-8 mb-8">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="">คุณได้ทำการแก้ไขข้อมูล</span><br/>
              <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="pt-4">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-row class="mb-2" justify="center" dense>
            <v-btn outlined height="48" width="110" dense rounded dark color="#27AB9C" class="mr-4" @click="dialogConfirmUpdate = false">ยกเลิก</v-btn>
            <v-btn height="48" width="110" color="#27AB9C" dark dense rounded class="" @click="UpdateImagesFlashSales()">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      dialogConfirmUpdate: false,
      theRedI: true,
      base64Image: '',
      DataImageBanner: [],
      DataImageBannerApp: [],
      DataImageLogo: [],
      DataImageBackground: [],
      Banner: '',
      BannerApp: '',
      Logo: '',
      Background: '',
      mediaDetails: []
    }
  },
  computed: {
    checkWidth () {
      return window.screen.width
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/EditImagesFlashSaleMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'EditImagesFlashSale')
        // await this.$router.push({ path: '/AdminPanit' }).catch(() => {})
        this.$router.push({ path: '/EditImagesFlashSale' }).catch(() => {})
      }
    }
  },
  async created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNavAdmin')
    this.DetailImagesFlashSales()
  },
  methods: {
    backToHome () {
      if (this.MobileSize) {
        this.$router.push({ path: '/adminFlashsaleManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminFlashsaleManage' })
      }
    },
    onPickFileBanner () {
      document.getElementById('file_input_banner').click()
    },
    onPickFileLogo () {
      document.getElementById('file_input_logo').click()
    },
    onPickFileBackground () {
      document.getElementById('file_input_background').click()
    },
    async onFileSelectedBanner (files) {
      if (files && files.length > 0) {
        const file = files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          const image = new Image()

          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => resolve({ width: image.width, height: image.height })
            image.src = url
          })

          if (imageDimensions.height <= 840 && imageDimensions.width <= 2460) {
            const reader = new FileReader()
            reader.onload = async () => {
              this.base64Image = reader.result.split(',')[1]
              const Banner = { image: [this.base64Image], type: '', seller_shop_id: 'all' }

              this.$store.commit('openLoader')

              await this.$store.dispatch('actionsUploadToS3', Banner)
              const response = this.$store.state.ModuleShop.stateUploadToS3

              if (response.message === 'List Success.') {
                this.$store.commit('closeLoader')
                this.Banner = response.data.list_path[0].path
              }
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'โปรดใช้รูปภาพตามขนาดและสัดส่วนที่กำหนด (2460x840)',
              showConfirmButton: false,
              timer: 1500
            })
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        console.warn('ไม่มีไฟล์ที่เลือก')
      }
    },
    async onFileSelectedLogo (files) {
      if (files && files.length > 0) {
        const file = files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          const image = new Image()

          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => resolve({ width: image.width, height: image.height })
            image.src = url
          })

          if (imageDimensions.height <= 300 && imageDimensions.width <= 600) {
            const reader = new FileReader()
            reader.onload = async () => {
              this.base64Image = reader.result.split(',')[1]
              const Logo = { image: [this.base64Image], type: '', seller_shop_id: 'all' }

              this.$store.commit('openLoader')

              await this.$store.dispatch('actionsUploadToS3', Logo)
              const response = this.$store.state.ModuleShop.stateUploadToS3

              if (response.message === 'List Success.') {
                this.$store.commit('closeLoader')
                this.Logo = response.data.list_path[0].path
              }
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'โปรดใช้รูปภาพตามขนาดและสัดส่วนที่กำหนด (600x300)',
              showConfirmButton: false,
              timer: 1500
            })
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        console.warn('ไม่มีไฟล์ที่เลือก')
      }
    },
    async onFileSelectedBackground (files) {
      if (files && files.length > 0) {
        const file = files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          const image = new Image()

          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => resolve({ width: image.width, height: image.height })
            image.src = url
          })

          if (imageDimensions.height <= 350 && imageDimensions.width <= 1400) {
            const reader = new FileReader()
            reader.onload = async () => {
              this.base64Image = reader.result.split(',')[1]
              const Background = { image: [this.base64Image], type: '', seller_shop_id: 'all' }

              this.$store.commit('openLoader')

              await this.$store.dispatch('actionsUploadToS3', Background)
              const response = this.$store.state.ModuleShop.stateUploadToS3

              if (response.message === 'List Success.') {
                this.$store.commit('closeLoader')
                this.Background = response.data.list_path[0].path
              }
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'warning',
              text: 'โปรดใช้รูปภาพตามขนาดและสัดส่วนที่กำหนด (1400x350)'
            })
          }
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)'
          })
        }
      } else {
        console.warn('ไม่มีไฟล์ที่เลือก')
      }
    },
    removeImageBanner () {
      this.Banner = ''
    },
    removeImageLogo () {
      this.Logo = ''
    },
    removeImageBackground () {
      this.Background = ''
    },
    async DetailImagesFlashSales () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsMediaDetail')
      const responseDetailImages = this.$store.state.ModuleAdminPanit.stateMediaDetail

      if (responseDetailImages.message === 'get media success.') {
        this.$store.commit('closeLoader')
        // console.log(responseDetailImages)

        this.mediaDetails = responseDetailImages.data.map((item) => ({
          platform: item.platform,
          type: item.type,
          path: item.path
        }))

        // console.log(this.mediaDetails)

        this.Banner = this.mediaDetails[0].path
        this.BannerApp = this.mediaDetails[1].path
        this.Logo = this.mediaDetails[2].path
        this.Background = this.mediaDetails[3].path
        // console.log('Banner:', this.Banner)
        // console.log('BannerApp:', this.BannerApp)
        // console.log('Logo:', this.Logo)
        // console.log('Background:', this.Background)
      } else {
        console.error('Unexpected response:', responseDetailImages)
      }
    },
    async handleConfirm () {
      this.dialogConfirmUpdate = true
    },
    async UpdateImagesFlashSales () {
      this.$store.commit('openLoader')
      const data = {
        banner: this.Banner,
        banner_app: this.Banner,
        logo: this.Logo,
        background: this.Background
      }

      await this.$store.dispatch('actionsMediaUpload', data)
      const responseMediaUpload = await this.$store.state.ModuleAdminPanit.stateMediaUpload
      // console.log(responseMediaUpload)

      if (responseMediaUpload.message === 'upload media success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'แก้ไขรูปภาพสำเร็จ'
        })
        this.dialogConfirmUpdate = false
        if (this.MobileSize) {
          this.$router.push({ path: '/adminFlashsaleManageMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/adminFlashsaleManage' }).catch(() => { })
        }
        // this.$EventBus.$emit('getAllProductFlashSale')
      } else {
        this.$swal.fire({
          icon: 'error',
          title: 'ไม่สามารถแก้ไขรูปภาพ Flash sale ได้',
          text: responseMediaUpload.message
        })
      }
    }
  }
}
</script>

<style>

</style>
