<template>
  <v-container>
        <!-- <v-card  width="100%" height="100%" elevation="0" class="mb-2 mt-2"> -->
    <div class=" pb-3">
      <v-img src="@/assets/Create_Store/Banner-3.png" class="rounded-xl"  ></v-img>
    </div>
      <!-- </v-card> -->
    <v-card class="container">
    <v-card elevation="0" width="100%" height="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
      <v-card-text v-if="stepper === 1">
        <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ข้อมูลทั่วไป</span>
        <v-col cols="12" md="12"  class="mt-6 px-0">
          <v-card
            elevation="0"
            width="100%"
            height="100%"
            style="background: #FFFFFF; border-radius: 8px;"
          >
            <v-card-text>
              <v-card
                elevation="0"
                style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                @click="onPickFile()"
              >
                <v-card-text>
                  <v-row
                    no-gutters
                    align="center"
                    justify="center"
                    style="cursor: pointer;"
                  >
                    <v-file-input
                      v-model="DataImage"
                      :items="DataImage"
                      accept="image/jpeg, image/jpg, image/png"
                      @change="UploadImage()"
                      id="file_input"
                      multiple
                      :clearable="false"
                      style="display:none"
                    >
                    </v-file-input>
                    <v-col cols="12" md="12" class="mb-6">
                      <v-row justify="center" class="pt-10">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                          width="280.34"
                          height="154.87"
                          contain
                        ></v-img>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12" class="mt-6">
                      <v-row justify="center" align="center">
                        <v-col cols="12" md="6" style="text-align: center;">
                          <span
                            style="font-size: 16px; line-height: 24px; font-weight: 400;"
                            >เพิ่มรูปภาพของคุณที่นี่</span
                          ><br />
                          <span
                            style="font-size: 16px; line-height: 24px; font-weight: 40;"
                            >หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span
                          ><br />
                          <span
                            style="font-size: 12px; line-height: 16px; font-weight: 400;"
                            >(ไฟล์นามสกุล .JPEG, .PNG เพิ่มได้สูงสุด 6 รูปภาพ)</span
                          ><br />
                          <span
                            style="font-size: 12px; line-height: 16px; font-weight: 400;"
                            >
                            <!-- <span style="color: red;">***</span> หมายเหตุ -->
                            ไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span
                          >
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
                <span style="font-size: 12px; line-height: 16px; font-weight: 400;">
                  <span style="color: red;">***</span> หมายเหตุ  รูปแรกจะเป็นรูปโปรไฟล์(Profile)
                </span>
                <div v-if="Detail.product_image.length !== 0" class="mt-4">
                <draggable v-model="Detail.product_image"  :move="onMove" @start="drag=true" @end="drag=false" :class="MobileSize ? 'row fill-height align-center sortable-list' : 'pl-5 pr-5 row  fill-height align-center sortable-list'">
                  <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="6" sm="4" md="2">
                    <v-card  v-if="item.type === 'image'" outlined class="pa-1" width="146" height="146" >
                      <v-card-text :class="MobileSize ? 'pl-2 py-0' : 'px-0 py-0'">
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                        </v-btn>
                        <v-img  :src="item.path" :lazy-src="item.url" :width="MobileSize ? '115' : '130'" :height="MobileSize ? '115' : '130'" contain></v-img>
                      </v-card-text>
                    </v-card>
                    <!-- <v-card v-else outlined  class="pa-1" width="146" height="146"  >
                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                        </v-btn>
                      <video    autoplay loop muted playsinline  >
                        <source :src="item.length ? item.path + '?=' + `${currentTime.getTime()}`:item.path" type="video/mp4" >
                      </video>
                    </v-card> -->
                  </v-col>
                </draggable>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        <v-row dense justify="end">
          <v-btn  dark outlined color="#27AB9C" class="pl-7 pr-7 mr-1" @click="cancle()">ยกเลิก</v-btn>
          <v-btn color="#27AB9C" class="px-8" dark @click="open()">ตกลง</v-btn>
        </v-row>
      </v-card-text>
    </v-card>
    </v-card>
    <!-- <v-dialog  v-model="dialog" width="600" persistent>
     <v-card align="center">
      <v-toolbar color="#BDE7D9"  dark dense>
        <span class="flex text-center" style="font-size:20px"><font color="#27AB9C">แก้ไขรูปภาพ</font></span>
        <v-btn
          icon
          dark
          @click="dialog = false"
          >
           <v-icon  color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <br/><br/>
      <v-card-text >
        <span style="font-size:18px">คุณได้ทำการแก้ไขรูปภาพร้านค้า<br/>คุณต้องการทำรายการนี้ ใช่ หรือไม่</span>
      </v-card-text><br/>
      <v-card-text>
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 " @click="close()">ยกเลิก</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessCurierAll()">ตกลง</v-btn>
      </v-card-text>
       <br/>
     </v-card>
    </v-dialog> -->
    <v-dialog  v-model="dialog" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">แก้ไขข้อมูลร้านค้า</font></span>
          <v-btn
            icon
            dark
            @click="dialog = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/><br/>
        <v-card-text >
          <span>
            คุณได้ทำการแก้ไขข้อมูลร้านค้า<br/>
            คุณต้องการทำรายการนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">ยกเลิก</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="postDetailShop()">ตกลง</v-btn>
       </v-container>
      </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      stepper: 1,
      DataImage: [],
      Detail: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      dialog: false,
      bussinessType: '',
      category: '',
      taxNumber: '',
      shopName: '',
      items: ['Foo', 'Bar', 'Fizz', 'Buzz'],
      descriptionShop: '',
      mobile: '',
      facebook: '',
      line: '',
      SCG: '',
      flash: '',
      partner: false,
      publicshop: false,
      openshop: false,
      allDay: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
      MondaytimeStart: null,
      MondaytimeEnd: null,
      TuesdaytimeStart: null,
      TuesdaytimeEnd: null,
      WednesdaytimeStart: null,
      WednesdaytimeEnd: null,
      ThursdaytimeStart: null,
      ThursdaytimeEnd: null,
      FirdaytimeStart: null,
      FirdaytimeEnd: null,
      SaturdaytimeStart: null,
      SaturdaytimeEnd: null,
      SundaytimeStart: null,
      SundaytimeEnd: null,
      menuMondayStart: false,
      menuMondayEnd: false,
      menuTuesdayStart: false,
      menuTuesdayEnd: false,
      menuWednesdayStart: false,
      menuWednesdayEnd: false,
      menuThursdayStart: false,
      menuThursdayEnd: false,
      menuFirdayStart: false,
      menuFirdayEnd: false,
      menuSaturdayStart: false,
      menuSaturdayEnd: false,
      menuSundayStart: false,
      menuSundayEnd: false,
      first_name: '',
      last_name: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('changeNav', this.SelectPath)
    this.getDetailShop()
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    cancle () {
      this.$router.push({ path: 'designShop' })
    },
    open () {
      this.dialog = true
    },
    close () {
      this.dialog = false
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      // console.log(this.DataImage, 'this.DataImage')
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      // this.Detail.product_image = []
      if (this.Detail.product_image.length < 6) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          if (imageSize < 2) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = () => {
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (this.Detail.product_image.length < 6) {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  path: url,
                  name: this.DataImage[i].name,
                  type: (this.DataImage[i].type.split('/', 1)).toString()
                })
              } else {
                this.$swal.fire({
                  icon: 'warning',
                  text: 'กรุณาใส่รูปไม่เกิน 6 รูป',
                  showConfirmButton: false,
                  timer: 1500
                })
              }
              // console.log(this.Detail.product_image, 'this.Detail.product_image')
              // mediaType = this.DataImage[i].type
              // var checkType = mediaType.split('/', 1)
              // if (checkType.toString() === 'video') {
              //   checkType = 'vdo'
              // } else {
              //   checkType = 'image'
              // }
              // this.shop_media.push({
              //   media: element,
              //   media_type: checkType
              // })
              // console.log(this.shop_media, 'this.shop_media')
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
              showConfirmButton: false,
              timer: 1500
            })
          }
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาใส่รูปไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.product_image.splice(index, 1)
      } else {
        this.Detail.product_image.splice(index, 1)
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    closeModal () {
      this.dialog = false
      window.scrollTo(0, 0)
    },
    async postDetailShop () {
      this.dialog = false
      var list = []
      for (let i = 0; i < this.Detail.product_image.length; i++) {
        if (this.Detail.product_image[i].name === 'default') {
          list.push({
            id: this.Detail.product_image[i].id
          })
        } else {
          list.push({
            id: '-1',
            image_data: this.Detail.product_image[i].image_data
          })
        }
      }
      var dataEdit = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        shop_name: this.shopName,
        shop_url: this.urlname,
        tax_id: this.taxNumber,
        shop_description: this.descriptionShop,
        shop_status: this.openshop ? 'active' : 'inactive',
        public_show: this.publicshop ? 'yes' : 'no',
        have_partner: this.publicshop ? 'yes' : 'no',
        partner_show: this.partner ? 'yes' : 'no',
        facebook_url: this.facebook,
        line_id: this.line,
        shop_type: this.bussinessType,
        shop_address: this.shop_address,
        shop_email: this.shopEmail,
        merchant_key: this.merchant_key,
        shop_phone: this.mobile,
        shop_shipping_type: this.shipping,
        shop_image: list,
        first_name: this.first_name,
        last_name: this.last_name
      }
      await this.$store.dispatch('actionEditShop', dataEdit)
      var response = await this.$store.state.ModuleShop.stateEditShop
      if (response.code === 200) {
        this.$swal.fire({ text: 'บันทึกข้อมูลสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        window.scrollTo(0, 0)
        this.Detail.product_image = []
        this.getDetailShop()
        if (this.MobileSize) {
          this.$router.push({ path: '/designShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/designShop' }).catch(() => {})
        }
      } else {
        this.$swal.fire({ text: 'บันทึกข้อมูลไม่สำเร็จ', icon: 'error', timer: 2500, showConfirmButton: false })
      }
    },
    async getDetailShop () {
      var idshop = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: idshop
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      // response api get Detail
      this.img = response.data[0].shop_image
      for (let i = 0; i < response.data[0].shop_image.length; i++) {
        var sent = {
          path: this.img[i].media_path,
          name: 'default',
          type: 'image',
          id: this.img[i].id
        }
        this.Detail.product_image.push(sent)
      }
      this.first_name = response.data[0].first_name
      this.last_name = response.data[0].last_name
      this.descriptionShop = response.data[0].shop_description
      this.urlname = response.data[0].url_name
      this.shop_address = response.data[0].shop_address
      this.shopEmail = response.data[0].shop_email
      this.bussinessType = response.data[0].shop_type
      this.shopName = response.data[0].shop_name
      this.mobile = response.data[0].shop_phone
      this.facebook = response.data[0].facebook_url
      this.line = response.data[0].line_id
      this.taxNumber = response.data[0].tax_id
      this.merchant_key = response.data[0].merchant_key
      // this.shipping = []
      // for (let i = 0; i < response.data[0].shipping_type.length; i++) {
      //   this.shipping.push(response.data[0].shipping_type[i].shipping_type)
      // }
      if (response.data[0].shop_status === 'active') {
        this.openshop = true
      } else {
        this.openshop = false
      }
      if (response.data[0].have_partner === 'yes') {
        this.publicshop = true
      } else {
        this.publicshop = false
      }
      if (response.data[0].partner_show === 'yes') {
        this.partner = true
      } else {
        this.partner = false
      }
    },
    nextStep (val) {
      this.stepper = val
    }
  }
}
</script>
