<template>
  <div></div>
</template>

<script>
import { Encode } from '@/services'
export default {
  async created () {
    if (this.$router.currentRoute.query.shared_token !== undefined) {
      const response = await this.axios.get(`${process.env.VUE_APP_BACK_END}api/callback_shared_token?shared_token=${this.$router.currentRoute.query.shared_token}`)
      if (response.data.result === 'SUCCESS') {
        const auth = {
          headers: { Authorization: `Bearer ${response.data.data.access_token}` }
        }
        const responseUserDetail = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail_mp_v2`, '', auth)
        if (responseUserDetail.data.code !== 500) {
          var onedata = {}
          onedata.user = responseUserDetail.data.data
          localStorage.setItem('oneData', Encode.encode(onedata))
          // set การ redirect หน้า
          var PathRedirect = ''
          if (this.$router.currentRoute.query.redirect_url !== undefined) {
            PathRedirect = this.$router.currentRoute.query.redirect_url + '?page=1&ref_code=' + this.$router.currentRoute.query.ref_code + '&callback_url=' + this.$router.currentRoute.query.callback_url
          } else {
            PathRedirect = '/'
          }
          var dataRole = {
            role: 'ext_buyer'
          }
          localStorage.setItem('roleUser', JSON.stringify(dataRole))
          this.$EventBus.$emit('checkPDPA', response.data.data.one_id)
          // window.location.href = `${PathRedirect}`
          this.$router.push({ path: `/${PathRedirect.replace(`${process.env.VUE_APP_DOMAIN}`, '')}` }).catch(() => {})
        } else {
          localStorage.removeItem('oneData')
          window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
        }
      } else {
        localStorage.removeItem('oneData')
        this.$router.push({ path: '/' }).catch(() => {})
      }
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  }
}
</script>
