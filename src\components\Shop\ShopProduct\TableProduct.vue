<template>
  <div>
    <a-row type="flex" :gutter="[0,20]">
      <a-col :span="24" >
        <a-input style="width: 35%" v-model="search" placeholder="ค้นหารายการสินค้า">
          <a-icon slot="suffix" type="search" />
        </a-input>
      </a-col>
      <a-col :span="8">
        <span class="headline">สินค้า : {{props.length}} รายการ</span>
      </a-col>
      <a-col :span="16">
        <a-row type="flex" justify="end">
          <a-button @click="CreateProduct"><a-icon type="plus" />เพิ่มรายการสินค้า</a-button>
        </a-row>
      </a-col>
    </a-row>
    <a-row type="flex">
      <a-col :span="24">
        <v-card outlined class="mt-5">
          <v-data-table
          :headers="headers"
          :items="props"
          @page-count="pageCount = $event"
          :page.sync="page"
          :items-per-page="itemsPerPage"
          class="elevation-0, rounded-lg"
          :search="search"
          hide-default-footer
          >
          <template v-slot:[`item.image`]="{ item }">
              <!-- <pre>{{item}}</pre> -->
              <v-container grid-list-xs v-if="item.image.length === 0">
              <v-row no-gutters>
                <v-col cols="2">
                  <v-img width="40" height="40" src="@/assets/NoImage.png"></v-img>
                </v-col>
                <v-col cols="10" class="pt-2">
                  <a-tag color="red" v-if="item.stock_count === 0">สินค้าหมด</a-tag>
                  <a-tag color="orange" v-else-if="item.product_status === '0'">ซ่อนสินค้า</a-tag>
                  <a-tag color="green" v-else>พร้อมขาย</a-tag><br>
                  <span class="caption">{{item.name}}</span>
                </v-col>
              </v-row>
            </v-container>
            <v-container grid-list-xs v-else>
              <v-row no-gutters>
                <v-col cols="2">
                  <v-img width="40" height="40" :src="`${item.image[0].url}`"></v-img>
                </v-col>
                <v-col cols="10" class="pt-2">
                  <a-tag color="red" v-if="item.stock_count === 0">สินค้าหมด</a-tag>
                  <a-tag color="orange" v-else-if="item.product_status === '0'">ซ่อนสินค้า</a-tag>
                  <a-tag color="green" v-else>พร้อมขาย</a-tag><br>
                  <span class="caption">{{item.name}}</span>
                </v-col>
              </v-row>
            </v-container>
          </template>
          <template v-slot:[`item.product_price`]="{ item }">
            <span>{{ Number(item.product_price[0].price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          </template>
          <!-- <template v-slot:[`item.stock_count`]="{ item }">
            <a-tag color="red" v-if="item.stock_count === 0">ไม่พร้อมขาย</a-tag>
          </template> -->
          <template v-slot:[`item.edit`]="{ item }">
                <a-button type="link" @click="Edit(item)">เเก้ไข</a-button>
              </template>
          <template v-slot:[`item.stock`]>
            <v-icon color="success" block>mdi-plus-box</v-icon>
            <v-icon  block>mdi-minus-box</v-icon>
          </template>
          </v-data-table>
        </v-card>
        <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
export default {
  props: ['props', 'ShopID'],
  data () {
    return {
      pageCount: 5,
      page: 1,
      itemsPerPage: 4,
      search: '',
      headers: [
        { text: 'ชื่อสินค้า', value: 'image', sortable: false, align: 'left', width: '480' },
        { text: 'รหัส SKU', value: 'sku', sortable: false, width: '200' },
        { text: 'ราคา', value: 'product_price', sortable: false, align: 'center', width: '100' },
        { text: 'คลัง', value: 'stock_count', sortable: false, align: 'center', width: '80' },
        { text: 'การดำเนินการ', value: 'edit', sortable: false, align: 'center', width: '120' }
        // { text: 'เเก้ไขสต็อก', value: 'stock', sortable: false, align: 'center', width: '80' }
      ]
    }
  },
  watch: {
    props (val) {
      // console.log('val ======', val)
    }
  },
  methods: {
    CreateProduct () {
      this.$router.push({ path: `/manageproduct?Status=Create&ShopID=${this.ShopID}` })
    },
    Edit (val) {
      // console.log('Data Edit', val)
      this.$store.commit('SetEditProduct', val)
      this.$router.push({ path: `/manageproduct?Status=Edit&ShopID=${this.ShopID}` })
    }
  }
}
</script>
