<template>
  <v-container style="background: #FFFFFF;" :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mx-0 my-0">
      <div class="d-flex align-center">
        <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการขนส่ง</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>จัดการขนส่ง</v-card-title>
        <v-spacer></v-spacer>
        <v-btn color="#27AB9C" v-if="showForm === true && switchShipping !== false" style="color: #fff; border-radius: 5vw;" @click="editShipping()">แก้ไข</v-btn>
      </div>
    </v-card>
    <div v-if="showForm">
      <v-row class="pa-4">
          <div v-if="MobileSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <div>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              </v-col>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" disabled :class="IpadSize ? 'pt-4' : ''"></v-switch>
              </v-col>
            </div>
          </div>
          <div v-else-if="IpadSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <v-col cols="12" class="d-flex align-end" style="margin-top: -4vw;">
              <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" disabled :class="IpadSize ? 'pt-4' : ''"></v-switch>
            </v-col>
          </div>
          <v-col cols="12" v-else class="d-flex align-end" style="gap: 2vw; margin-top: -1vw;">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
              <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" disabled :class="MobileSize || IpadSize ? 'pt-4' : ''"></v-switch>
          </v-col>
          <!-- เลือกรายการขนส่ง -->
          <v-col cols="12" md="12" sm="12" v-if="switchShipping">
            <v-row dense>
              <v-col cols="12">
                <span style="font-size: medium;">เลือกรายการขนส่ง <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                <v-select class="showData" disabled v-model="SelectShippingShow" :menu-props="{ offsetY: true }" :items="itemShipping" item-text="name" item-value="code" :rules="Rules.ItemShipping" multiple chips outlined dense placeholder="เลือกขนส่ง" style="border-radius: 8px;">
                  <template v-slot:selection="{ item }">
                    <v-chip
                      outlined
                    >
                      {{ item.name }}
                    </v-chip>
                  </template>
                  <template v-slot:item="{ item }">
                    <v-row dense class="py-2">
                      <v-img :src="item.media_path" max-height="50" max-width="50" style="border-radius: 999px;" contain></v-img><span class="pl-4" style="display: grid; align-items: center;">{{ item.name }}</span>
                    </v-row>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <!-- แสดงรายการขนส่งที่เลือก -->
          <v-col cols="12" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -2vw;'" v-if="switchShipping !== false">
            <span style="font-size: medium;">รายการขนส่งของร้านค้า</span>
          </v-col>
          <v-col cols="12" class="d-flex pb-10" style="gap: 1vw; flex-wrap: wrap;" v-if="switchShipping !== false">
            <!-- <span>รายการขนส่งของร้าน</span> -->
            <v-card class="pa-5" :style="MobileSize ? 'border-radius: 3vw; width: 27vw;' : IpadSize ? 'border-radius: 2vw; width: 17vw;' : 'border-radius: 1vw; width: 10vw;'" v-for="(items, index) in listSelectShipping"  :key="index">
              <div class="d-flex flex-column align-center" style="gap: 2vw;">
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 100" tile v-if="items.media_path !== null && items.media_path !== ''" width="80">
                  <img :src="items.media_path" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 100" tile v-else width="80">
                  <img src="@/assets/NoImage.png" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <span :style="MobileSize ? 'font-size: x-small' : IpadSize ? 'font-size: small' : ''">{{items.name}}</span>
              </div>
            </v-card>
          </v-col>
          <v-col v-else>
            <v-card :style="MobileSize ? 'margin-top: -1vw' : ''">
              <v-card-text class="d-flex align-center flex-column" style="padding: 3vw;">
                <v-img
                  src="@/assets/ImageINET-Marketplace/ICONShop/noShipping.png"
                  width="500"
                  height="300"
                  contain
                  style="opacity: 80%;"
                ></v-img>
                <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">ร้านค้านี้ ไม่ใช้ขนส่งของระบบ</span>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- <v-col cols="12">
            <v-btn color="#27AB9C">บันทึก</v-btn>
          </v-col> -->
          <!-- <v-col cols="12" class="d-flex justify-end">
            <v-btn outlined color="#27AB9C" style="border-radius: 1vw;">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" style="color: #fff; border-radius: 1vw;">บันทึก</v-btn>
          </v-col> -->
      </v-row>
    </div>
    <div v-if="showFormEdit">
      <v-row class="pa-4">
          <div v-if="MobileSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <div>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              </v-col>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" :class="IpadSize ? 'pt-4' : ''"></v-switch>
              </v-col>
            </div>
          </div>
          <div v-else-if="IpadSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <v-col cols="12" class="d-flex align-end" style="margin-top: -4vw;">
              <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" :class="IpadSize ? 'pt-4' : ''"></v-switch>
            </v-col>
          </div>
          <v-col v-else class="d-flex align-end" style="gap: 2vw; margin-top: -1vw;">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
              <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" :class="MobileSize || IpadSize ? 'pt-4' : ''"></v-switch>
          </v-col>
          <!-- เลือกรายการขนส่ง -->
          <v-col cols="12" md="12" sm="12" v-if="switchShipping">
            <v-row dense>
              <v-col cols="12">
                <span style="font-size: medium;">เลือกรายการขนส่ง <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                <v-select v-model="SelectShipping" :menu-props="{ offsetY: true }" :items="itemShipping" item-text="name" item-value="code" :rules="Rules.ItemShipping" multiple chips outlined dense placeholder="เลือกขนส่ง" style="border-radius: 8px;">
                  <template v-slot:selection="{ item }">
                    <v-chip
                      close
                      outlined
                      @click:close="removeShipping(item)" color="primary"
                    >
                      {{ item.name }}
                    </v-chip>
                  </template>
                  <template v-slot:item="{ item }">
                    <v-row dense class="py-2">
                      <v-img :src="item.media_path" max-height="50" max-width="50" style="border-radius: 999px;" contain></v-img><span class="pl-4" style="display: grid; align-items: center;">{{ item.name }}</span>
                    </v-row>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <!-- แสดงรายการขนส่งที่เลือก -->
          <v-col cols="12" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -2vw;'">
            <span style="font-size: medium;">รายการขนส่งของร้านค้า</span>
          </v-col>
          <v-col class="d-flex pb-10" style="gap: 1vw; flex-wrap: wrap;">
            <!-- <span>รายการขนส่งของร้าน</span> -->
            <v-card class="pa-5" :style="MobileSize ? 'border-radius: 3vw; width: 27vw;' : IpadSize ? 'border-radius: 2vw; width: 17vw;' : 'border-radius: 1vw; width: 10vw;'" v-for="(items, index) in listSelectShipping"  :key="index">
              <div class="d-flex flex-column align-center" style="gap: 2vw;">
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 100" tile v-if="items.media_path !== null && items.media_path !== ''" width="80">
                  <img :src="items.media_path" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 100" tile v-else width="80">
                  <img src="@/assets/NoImage.png" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <span :style="MobileSize ? 'font-size: x-small' : IpadSize ? 'font-size: small' : ''">{{items.name}}</span>
              </div>
            </v-card>
          </v-col>
          <!-- <v-col cols="12">
            <v-btn color="#27AB9C">บันทึก</v-btn>
          </v-col> -->
          <v-col cols="12" class="d-flex justify-end">
            <v-btn outlined color="#27AB9C" style="border-radius: 5vw;" @click="cancelEdit">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" style="color: #fff; border-radius: 5vw;" @click="dialogEditShipping = true">บันทึก</v-btn>
          </v-col>
      </v-row>
    </div>
    <!-- Await edit shipping -->
    <v-dialog v-model="dialogEditShipping" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="cancelDialogEdit()"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-container>
          <div class="d-flex justify-center">
            <v-avatar :size="MobileSize ? 90 : 250" tile><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/ImageINET-Marketplace/ICONShop/edit_shipping.png" alt=""></v-avatar>
          </div>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: large; line-height: 24px; color: #333333;" class="my-4"><b>เปลี่ยนรายการขนส่งของร้านค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการเปลี่ยนรายการขนส่ง ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelDialogEdit()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editShippingSeller()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      switchShipping: true,
      switchAtStore: false,
      shopID: '',
      itemShipping: [],
      SelectShipping: '',
      SelectShippingShow: '',
      listSelectShipping: [],
      showForm: true,
      showFormEdit: false,
      dialogEditShipping: false,
      Rules: {
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ]
      }
    }
  },
  computed: {
    showTextShipping () {
      if (this.switchShipping === false) {
        return 'ไม่ใช้ขนส่งของระบบ'
      } else {
        return 'ใช้ขนส่งของระบบ'
      }
    },
    showTextswitchAtStore () {
      if (this.switchAtStore === false) {
        return 'ไม่มีรับหน้าร้าน'
      } else {
        return 'มีรับหน้าร้าน'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.shopID = JSON.parse(localStorage.getItem('shopSellerID'))
    this.$store.commit('openLoader')
    this.getListShipping()
    this.$store.commit('closeLoader')
  },
  watch: {
    SelectShipping (val) {
      // console.log(val, 55555)
      this.updateSelectedShippingImages()
      // console.log('deee', this.listSelectShipping)
    },
    listSelectShipping (val) {
      // console.log(val, 111111)
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageShippingSellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageShippingSeller' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    cancelDialogEdit () {
      this.dialogEditShipping = false
    },
    editShipping () {
      this.showForm = false
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.showFormEdit = true
        this.$store.commit('closeLoader')
      }, 1000)
    },
    cancelEdit () {
      this.GetDetailShop()
      this.showForm = true
      this.showFormEdit = false
    },
    async getListShipping () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const auth = {
        headers: { Authorization: `Bearer ${oneData.user.access_token}` }
      }
      const data = {
        seller_shop_id: this.shopID
      }
      const response = await this.axios.post(`${process.env.VUE_APP_BACK_END2}iship/iship_courier_list`, data, auth)
      if (response.data.ok === 'y') {
        this.itemShipping = [...response.data.query_result.filter(item => item.service_provider === 'ISHIP')]
        // console.log(this.itemShipping, 9787)
      } else if (response.data.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      }
      await this.GetDetailShop()
    },
    async GetDetailShop () {
      // console.log(11111)
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.code === 200) {
          // console.log('seeeee', response.data[0].shipping_method)
          if (response.data[0].shipping_method && response.data[0].shipping_method.length !== 0) {
            this.$store.commit('closeLoader')
            this.switchShipping = true
            // this.listSelectShipping = response.data[0].shipping_method[0]
            // console.log('see ya', this.listSelectShipping)
            this.SelectShipping = response.data[0].shipping_method[0].courier
            this.SelectShippingShow = response.data[0].shipping_method[0].courier
          } else {
            this.$store.commit('closeLoader')
            this.switchShipping = false
            this.SelectShipping = []
          }
        }
        this.switchAtStore = response.data[0].store_front === 'yes'
        // console.log(this.Detail)
      }
    },
    updateSelectedShippingImages () {
      this.listSelectShipping = this.SelectShipping.map(code => {
        const found = this.itemShipping.find(item => item.code === code)
        return found ? { code: found.code, name: found.name, media_path: found.media_path } : { code, name: code, media_path: '' }
      })
      // console.log(this.listSelectShipping)
    },
    removeShipping (item) {
      const index = this.SelectShipping.indexOf(item.code)
      if (index >= 0) this.SelectShipping.splice(index, 1)
    },
    async editShippingSeller () {
      this.$store.commit('openLoader')
      var selectShip = {
        service: 'ISHIP',
        courier: this.SelectShipping
      }
      var data = {
        seller_shop_id: this.shopID,
        store_front: this.switchAtStore === false ? 'no' : 'yes',
        shipping_method: [selectShip]
      }
      // console.log('datadata **', data)
      await this.$store.dispatch('actionEditShippingSeller', data)
      var res = await this.$store.state.ModuleShop.stateEditShippingSeller
      if (res.code === 200) {
        this.dialogEditShipping = false
        await this.GetDetailShop()
        this.showFormEdit = false
        this.showForm = true
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.dialogEditShipping = false
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style>

</style>
<style scoped>
.showData ::v-deep(.v-chip) {
  background-color: grey !important;
  color: rgb(191, 190, 190) !important;
  border: 1px solid rgb(174, 174, 174) !important;
}
</style>
