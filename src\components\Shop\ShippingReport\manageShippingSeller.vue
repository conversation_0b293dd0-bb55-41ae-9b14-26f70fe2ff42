<template>
  <v-container style="background: #FFFFFF;" :class="MobileSize ? 'mt-3' : ''">
    <v-img src="@/assets/ShippingBannerRedesign.png" style="position: relative; width: 100%;  border-radius: 3px;" class="mb-5">
    </v-img>
    <v-card width="100%" height="100%" elevation="0" class="mx-0 my-0">
      <div class="d-flex align-center">
        <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการขนส่ง</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>จัดการขนส่ง</v-card-title>
        <v-spacer></v-spacer>
        <v-btn elevation="0" color="#3EC6B6" v-if="showForm === true && switchShipping !== false" style="color: #fff; border-radius: 5vw;" @click="editShipping()"><v-icon size="24" class="mr-2">mdi-pencil-outline</v-icon>แก้ไข</v-btn>
      </div>
    </v-card>
    <div v-if="showForm">
      <v-row class="pa-4">
          <!-- <div v-if="MobileSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <div>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              </v-col>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" disabled :class="IpadSize ? 'pt-4' : ''"></v-switch>
              </v-col>
            </div>
          </div>
          <div v-else-if="IpadSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <v-col cols="12" class="d-flex align-end" style="margin-top: -4vw;">
              <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" disabled :class="IpadSize ? 'pt-4' : ''"></v-switch>
            </v-col>
          </div> -->
          <v-col cols="12" class="d-flex align-end" style="gap: 2vw;">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span v-if="switchShipping !== false" style="font-size: medium;">{{ showTextShipping }}</span>
                <span v-if="switchShipping !== false && switchAtStore !== false" style="font-size: medium;">  ,  </span>
                <span v-if="switchAtStore !== false" style="font-size: medium;">{{ showTextswitchAtStore }}</span>
              </span>
              <!-- <v-switch v-model="switchShipping" inset color="#52C41A" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-switch>
              <v-switch v-model="switchAtStore" inset color="#52C41A" hide-details :label="`${showTextswitchAtStore}`" disabled :class="MobileSize || IpadSize ? 'pt-4' : ''"></v-switch> -->
          </v-col>
          <!-- เลือกรายการขนส่ง -->
          <!-- <v-col cols="12" md="12" sm="12" v-if="switchShipping">
            <v-row dense>
              <v-col cols="12">
                <span style="font-size: medium;">เลือกรายการขนส่ง <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                <v-select class="showData" disabled v-model="SelectShippingShow" :menu-props="{ offsetY: true }" :items="itemShipping" item-text="name" item-value="code" :rules="Rules.ItemShipping" multiple chips outlined dense placeholder="เลือกขนส่ง" style="border-radius: 8px;">
                  <template v-slot:selection="{ item }">
                    <v-chip
                      outlined
                    >
                      {{ item.name }}
                    </v-chip>
                  </template>
                  <template v-slot:item="{ item }">
                    <v-row dense class="py-2">
                      <v-img :src="item.media_path" max-height="50" max-width="50" style="border-radius: 999px;" contain></v-img><span class="pl-4" style="display: grid; align-items: center;">{{ item.name }}</span>
                    </v-row>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col> -->
          <!-- แสดงรายการขนส่งที่เลือก -->
          <v-col class="mt-1" cols="12" :style="MobileSize ? 'margin-top: -8vw;' : ''" v-if="switchShipping !== false">
            <v-avatar rounded size="30" class="pr-2">
              <v-img contain src="@/assets/truck.png">
              </v-img>
            </v-avatar>
            <span style="font-size: medium; font-weight: bold;">ขนส่งสำหรับร้านค้า</span>
          </v-col>
          <v-row class="ma-0 pa-0" v-if="switchShipping !== false">
          <v-col cols="12" class="d-flex pb-5" style="gap: 1vw; flex-wrap: wrap;">
            <!-- <span>รายการขนส่งของร้าน</span> -->
            <v-card elevation="0" color="#FBFBFB" :class="MobileSize ? 'pa-4' : 'pa-5 pb-1'" :style="MobileSize ? 'border-radius: 3vw; width: 27vw; border-style: solid; border-color: #F3F5F7' : IpadSize || IpadProSize ? 'border-radius: 2vw; width: 17vw; border-style: solid; border-color: #F3F5F7' : 'border-radius: 1vw; width: 10vw; border-style: solid; border-color: #F3F5F7'" v-for="(items, index) in listSelectShipping"  :key="index">
              <div class="d-flex flex-column align-center" style="gap: 1vw;">
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 60" tile v-if="items.media_path !== null && items.media_path !== ''" :width="MobileSize ? 80 : 50">
                  <img :src="items.media_path" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 60" tile v-else :width="MobileSize ? 80 : 50">
                  <img src="@/assets/NoImage.png" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <span :class="MobileSize ? 'text-center pt-2' : 'text-center pt-1'" :style="MobileSize ? 'font-size: x-small; font-weight: bold;' : IpadSize ? 'font-size: small; font-weight: bold;' : 'font-weight: bold;'">{{items.name}}</span>
              </div>
            </v-card>
          </v-col>
          <v-col class="mt-1" cols="12">
            <v-avatar rounded size="30" class="pr-2">
              <v-img contain src="@/assets/mapredesign.png">
              </v-img>
            </v-avatar>
            <span style="font-size: medium; font-weight: bold;">ที่อยู่สำหรับจัดส่งสินค้า</span>
          </v-col>
          <v-col class="mt-1" cols="12" v-if="!MobileSize && !IpadSize && !IpadProSize">
            <v-col cols="12" class="d-flex align-center mt-0 pt-0">
              <span style="font-size: medium;">หมายเลขโทรศัพท์
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ phoneShop }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class="d-flex align-center mt-1 pt-1">
              <span style="font-size: medium;">รายละเอียดที่อยู่
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ addressRequest }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class="d-flex align-center mt-1 pt-1">
              <v-col cols="2" class="ma-0 pa-0">
                <span style="font-size: medium;">เลขที่
                  <span style="font-size: medium;" class="mx-3">  :  </span>
                  <span style="font-size: medium;">  {{ HouseNo }}  </span>
                </span>
              </v-col>
              <v-col cols="3" class="ma-0 pa-0">
                <span style="font-size: medium;">แขวง/ตำบล
                  <span style="font-size: medium;" class="mx-3">  :  </span>
                  <span style="font-size: medium;">  {{ subdistricttext }}  </span>
                </span>
              </v-col>
              <v-col cols="4" class="ma-0 pa-0">
                <span style="font-size: medium;">เขต/อำเภอ
                  <span style="font-size: medium;" class="mx-3">  :  </span>
                  <span style="font-size: medium;">  {{ districtText }}  </span>
                </span>
              </v-col>
            </v-col>
            <v-col cols="12" class="d-flex align-center mt-1 pt-1">
              <v-col cols="3" class="ma-0 pa-0">
                <span style="font-size: medium;">จังหวัด
                  <span style="font-size: medium;" class="mx-3">  :  </span>
                  <span style="font-size: medium;">  {{ provinceText }}  </span>
                </span>
              </v-col>
              <v-col cols="3" class="ma-0 pa-0">
                <span style="font-size: medium;">รหัสไปรษณีย์
                  <span style="font-size: medium;" class="mx-3">  :  </span>
                  <span style="font-size: medium;">  {{ zipcodeText }}  </span>
                </span>
              </v-col>
            </v-col>
          </v-col>
          <v-col class="mt-1" cols="12" v-if="MobileSize || IpadSize || IpadProSize">
            <v-col cols="12" class="mt-0 pt-0">
              <span style="font-size: medium;">หมายเลขโทรศัพท์
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ phoneShop }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class="mt-1 pt-1">
              <span style="font-size: medium;">รายละเอียดที่อยู่
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ addressRequest }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class="mt-1 pt-1">
              <span style="font-size: medium;">เลขที่
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ HouseNo }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class="mt-1 pt-1">
              <span style="font-size: medium;">แขวง/ตำบล
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ subdistricttext }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class="mt-1 pt-1">
              <span style="font-size: medium;">เขต/อำเภอ
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ districtText }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class=" mt-1 pt-1">
              <span style="font-size: medium;">จังหวัด
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ provinceText }}  </span>
              </span>
            </v-col>
            <v-col cols="12" class=" mt-1 pt-1">
              <span style="font-size: medium;">รหัสไปรษณีย์
                <span style="font-size: medium;" class="mx-3">  :  </span>
                <span style="font-size: medium;">  {{ zipcodeText }}  </span>
              </span>
            </v-col>
          </v-col>
          </v-row>
          <v-col v-else>
            <v-card :style="MobileSize ? 'margin-top: -1vw' : ''">
              <v-card-text class="d-flex align-center flex-column" style="padding: 3vw;">
                <v-img
                  src="@/assets/ImageINET-Marketplace/ICONShop/noShipping.png"
                  width="500"
                  height="300"
                  contain
                  style="opacity: 80%;"
                ></v-img>
                <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">ร้านค้านี้ ไม่ใช้ขนส่งของระบบ</span>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- <v-col cols="12">
            <v-btn color="#27AB9C">บันทึก</v-btn>
          </v-col> -->
          <!-- <v-col cols="12" class="d-flex justify-end">
            <v-btn outlined color="#27AB9C" style="border-radius: 1vw;">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" style="color: #fff; border-radius: 1vw;">บันทึก</v-btn>
          </v-col> -->
      </v-row>
    </div>
    <div v-if="showFormEdit">
      <v-form ref="form" v-model="formValid" class="pa-0 ma-0">
      <v-row class="pa-4">
          <div v-if="MobileSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <div>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-checkbox v-model="switchShipping" inset color="#3EC6B6" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-checkbox>
              </v-col>
              <v-col cols="12" style="margin-top: -4vw;">
                <v-checkbox v-model="switchAtStore" inset color="#3EC6B6" hide-details :label="`${showTextswitchAtStore}`" :class="IpadSize ? 'pt-4' : ''"></v-checkbox>
              </v-col>
            </div>
          </div>
          <div v-else-if="IpadSize" style="margin-top: -2vw;">
            <v-col cols="12">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
            </v-col>
            <v-col cols="12" class="d-flex align-end" style="margin-top: -4vw;">
              <v-checkbox v-model="switchShipping" inset color="#3EC6B6" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-checkbox>
              <v-checkbox v-model="switchAtStore" inset color="#3EC6B6" hide-details :label="`${showTextswitchAtStore}`" :class="IpadSize ? 'pt-4' : ''"></v-checkbox>
            </v-col>
          </div>
          <v-col v-else class="d-flex align-end" style="gap: 2vw; margin-top: -1vw;">
              <span style="font-size: medium;">ข้อมูลการจัดส่ง</span>
              <span style="font-size: medium;">:</span>
              <v-checkbox v-model="switchShipping" inset color="#3EC6B6" hide-details :label="`${showTextShipping}`" disabled class="pr-4"></v-checkbox>
              <v-checkbox v-model="switchAtStore" inset color="#3EC6B6" hide-details :label="`${showTextswitchAtStore}`" :class="MobileSize || IpadSize ? 'pt-4' : ''"></v-checkbox>
          </v-col>
          <!-- เลือกรายการขนส่ง -->
           <v-col class="mt-1" cols="12" :style="MobileSize ? 'margin-top: -8vw;' : ''" v-if="switchShipping !== false">
            <v-avatar rounded size="30" class="pr-2">
              <v-img contain src="@/assets/truck.png">
              </v-img>
            </v-avatar>
            <span style="font-size: medium; font-weight: bold;">ขนส่งสำหรับร้านค้า</span>
          </v-col>
          <v-col cols="12" md="12" sm="12" v-if="switchShipping" class="pb-0">
            <v-row dense>
              <v-col cols="12">
                <span style="font-size: medium;">ขนส่ง</span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                <v-select v-model="SelectShipping" :menu-props="{ offsetY: true }" :items="itemShipping" item-text="name" item-value="code" :rules="Rules.ItemShipping" multiple chips outlined dense placeholder="เลือกขนส่ง" style="border-radius: 8px;">
                  <template v-slot:selection="{ item }">
                    <v-chip
                      close
                      outlined
                      @click:close="removeShipping(item)" color="primary"
                    >
                      {{ item.name }}
                    </v-chip>
                  </template>
                  <template v-slot:item="{ item }">
                    <v-row dense class="py-2">
                      <v-img :src="item.media_path" max-height="50" max-width="50" style="border-radius: 999px;" contain></v-img><span class="pl-4" style="display: grid; align-items: center;">{{ item.name }}</span>
                    </v-row>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <!-- แสดงรายการขนส่งที่เลือก -->
          <!-- <v-col cols="12" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -2vw;'">
            <span style="font-size: medium;">รายการขนส่งของร้านค้า</span>
          </v-col> -->
          <v-col class="d-flex pb-5 pt-0" style="gap: 1vw; flex-wrap: wrap;">
            <!-- <span>รายการขนส่งของร้าน</span> -->
            <v-card elevation="0" color="#FBFBFB" :class="MobileSize ? 'pa-4' : 'pa-5 pb-1'" :style="MobileSize ? 'border-radius: 3vw; width: 27vw; border-style: solid; border-color: #F3F5F7' : IpadSize || IpadProSize ? 'border-radius: 2vw; width: 17vw; border-style: solid; border-color: #F3F5F7' : 'border-radius: 1vw; width: 10vw; border-style: solid; border-color: #F3F5F7'" v-for="(items, index) in listSelectShipping"  :key="index">
               <v-btn
                small
                icon
                color="primary"
                style="position: absolute; top: -9px; right: -9px; z-index: 1;"
                @click="removeShipping(items)"
              >
                <v-icon>mdi-close-circle</v-icon>
              </v-btn>
              <div class="d-flex flex-column align-center" style="gap: 1vw;">
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 60" tile v-if="items.media_path !== null && items.media_path !== ''" :width="MobileSize ? 80 : 50">
                  <img :src="items.media_path" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <v-avatar :size="MobileSize ? 50 : IpadSize ? 40 : 60" tile v-else :width="MobileSize ? 80 : 50">
                  <img src="@/assets/NoImage.png" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
                <span :class="MobileSize ? 'text-center pt-2' : 'text-center pt-1'" :style="MobileSize ? 'font-size: x-small; font-weight: bold' : IpadSize ? 'font-size: small font-weight: bold' : 'font-weight: bold'">{{items.name}}</span>
              </div>
            </v-card>
          </v-col>
          <v-col class="mt-1 pb-0" cols="12">
            <v-avatar rounded size="30" class="pr-2">
              <v-img contain src="@/assets/mapredesign.png">
              </v-img>
            </v-avatar>
            <span style="font-size: medium; font-weight: bold;">ที่อยู่สำหรับจัดส่งสินค้า</span>
          </v-col>
          <v-col class="mt-1 pt-0" cols="12">
            <v-checkbox v-model="sameAddressShop" inset color="#3EC6B6" hide-details label="ตั้งค่าเป็นที่อยู่เดียวกับที่อยู่ร้าน" class="pr-4"></v-checkbox>
          </v-col>
          <v-col class="mt-1 mb-0 pb-0" cols="12">
            <span style="font-size: medium;">หมายเลขโทรศัพท์</span><span style="color: red;"> *</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <v-text-field :disabled="sameAddressShop" :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9]/g, '')" @keypress="CheckSpacebar($event)" :hide-details="!Rules.tel.some(r => typeof r === 'function' ? r(phoneShop) !== true : false)" dense outlined placeholder="กรอกหมายเลขโทรศัพท์" v-model="phoneShop"></v-text-field>
            </v-col>
          </v-col>
          <v-col cols="12" class="mt-3 mb-0 pb-0">
            <span style="font-size: medium;">รายละเอียดที่อยู่</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <v-text-field hide-details :disabled="sameAddressShop" @keypress="CheckSpacebar($event)" dense outlined placeholder="กรอกรายละเอียดที่อยู่" v-model="addressRequest"></v-text-field>
            </v-col>
          </v-col>
          <v-col cols="12" md="4" class="mt-3 mb-0 pb-0">
            <span style="font-size: medium;">เลขที่</span><span style="color: red;"> *</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <v-text-field :disabled="sameAddressShop" v-model="HouseNo" dense outlined placeholder="กรอกเลขที่" @keypress="CheckSpacebar($event)" oninput="this.value = this.value.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1')" :rules="Rules.house_Num" :hide-details="!Rules.house_Num.some(r => typeof r === 'function' ? r(HouseNo) !== true : false)" ></v-text-field>
            </v-col>
          </v-col>
          <v-col cols="12" md="4" class="mt-3 mb-0 pb-0">
            <span style="font-size: medium;">ตำบล/แขวง</span><span style="color: red;"> *</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <addressinput-subdistrict :disabled="sameAddressShop" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" dense outlined v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล"/>
              <div v-if="checkSubDistrictError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
          </v-col>
          <v-col cols="12" md="4" class="mt-3 mb-0 pb-0">
            <span style="font-size: medium;">อำเภอ/เขต</span><span style="color: red;"> *</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <addressinput-district :disabled="sameAddressShop" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" style="border-radius: 8px !important;" v-model="districtText" placeholder="ระบุเขต/อำเภอ"/>
              <div v-if="checkDistrictError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
          </v-col>
          <v-col cols="12" md="6" class="mt-3 mb-0 pb-0">
            <span style="font-size: medium;">จังหวัด</span><span style="color: red;"> *</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <addressinput-province :disabled="sameAddressShop" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" style="border-radius: 8px !important;" v-model="provinceText" placeholder="ระบุจังหวัด"/>
              <div v-if="checkProvinceError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง {{ checkProvinceError }}</div>
            </v-col>
          </v-col>
          <v-col cols="12" md="6" class="mt-3 mb-0 pb-0">
            <span style="font-size: medium;">รหัสไปรษณีย์</span><span style="color: red;"> *</span>
            <v-col cols="12" class="pl-0 mb-0 pb-0">
              <addressinput-zipcode :disabled="sameAddressShop" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" numbered style="border-radius: 8px !important;" label="" v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์"/>
              <div v-if="checkZipcodeError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
          </v-col>
          <!-- <v-col cols="12">
            <v-btn color="#27AB9C">บันทึก</v-btn>
          </v-col> -->
          <v-col cols="12" class="d-flex justify-end mt-10">
            <v-btn outlined color="#27AB9C" style="border-radius: 5vw;" @click="cancelEdit">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn :disabled="!formValid || (!checkSubDistrictError && subdistricttext === '' ) || (!checkDistrictError && districtText === '') || (!checkProvinceError  && provinceText === '') || (!checkZipcodeError && zipcodeText === '')" color="#27AB9C" style="color: #fff; border-radius: 5vw;" @click="dialogEditShipping = true">บันทึก</v-btn>
          </v-col>
      </v-row>
      </v-form>
    </div>
    <!-- Await edit shipping -->
    <v-dialog v-model="dialogEditShipping" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="cancelDialogEdit()"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-container>
          <div class="d-flex justify-center">
            <v-avatar :size="MobileSize ? 90 : 250" tile><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/ImageINET-Marketplace/ICONShop/edit_shipping.png" alt=""></v-avatar>
          </div>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: large; line-height: 24px; color: #333333;" class="my-4"><b>เปลี่ยนรายการขนส่งของร้านค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการเปลี่ยนรายการขนส่ง ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelDialogEdit()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editShippingSeller()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="closeDialogSuccess()"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 22px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกสำเร็จ</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 22px; color: #9A9A9A;">คุณได้ทำการบันทึกข้อมูลเรียบร้อย</span><br/>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn elevation="0" width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeDialogSuccess()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      dialogSuccess: false,
      formValid: false,
      CheckResponse: null,
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      sameAddressShop: false,
      userDetail: [],
      phoneShop: '',
      addressRequest: '',
      zipcodeText: '',
      provinceText: '',
      districtText: '',
      subdistricttext: '',
      HouseNo: '',
      switchShipping: true,
      switchAtStore: false,
      shopID: '',
      itemShipping: [],
      SelectShipping: '',
      SelectShippingShow: '',
      listSelectShipping: [],
      showForm: true,
      showFormEdit: false,
      dialogEditShipping: false,
      Rules: {
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ],
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        seller_name: [
          v => !!v || 'กรุณากรอกชื่อฝ่ายขาย',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 ตัว',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        contactTel: [
          v => !v || /^[0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => !v || (v.length >= 9 && v.length <= 10) || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ',
          v => (v.length > 8 && v.length <= 20) || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        postCode: [
          v => v.length === 5 || 'กรุณากรอกรหัสไปรษณีย์'
        ]
      }
    }
  },
  computed: {
    showTextShipping () {
      if (this.switchShipping === false) {
        return 'ไม่ใช้ขนส่งของระบบ'
      } else {
        return 'ใช้ขนส่งของระบบ'
      }
    },
    showTextswitchAtStore () {
      if (this.switchAtStore === false) {
        return 'ไม่มีรับหน้าร้าน'
      } else {
        return 'มีรับหน้าร้าน'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.shopID = JSON.parse(localStorage.getItem('shopSellerID'))
    this.$store.commit('openLoader')
    this.getListShipping()
    // this.SelectRequestCourier()
    this.$store.commit('closeLoader')
  },
  watch: {
    sameAddressShop (val) {
      if (val === true) {
        if (this.userDetail.address_detail.length > 1) {
          for (var i = 0; this.userDetail.address_detail.length > i; i++) {
            if (this.userDetail.address_detail[i].default_address === 'main') {
              var defaultAddress = this.userDetail.address_detail[i]
            }
          }
        } else {
        // console.log(defaultAddress, 'defaultAddress')
          defaultAddress = this.userDetail.address_detail[0]
        }
        var address = defaultAddress.detail
        this.phoneShop = this.userDetail.shop_phone[0].phone
        this.addressRequest = address === null ? '' : address
        this.HouseNo = defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1') === '' || defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1') === '-' ? '-' : defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1')
        this.subdistricttext = defaultAddress.sub_district
        this.districtText = defaultAddress.district
        this.provinceText = defaultAddress.province
        this.zipcodeText = defaultAddress.zipcode
      }
    },
    subdistricttext (val) {
      if (/\s/g.test(val)) {
        this.subdistricttext = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcodeText = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    districtText (val) {
      if (/\s/g.test(val)) {
        this.districtText = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.provinceText = ''
        }
      }
    },
    provinceText (val) {
      if (/\s/g.test(val)) {
        this.provinceText = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.districtText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.districtText = ''
        }
      }
    },
    zipcodeText (val) {
      if (/\s/g.test(val)) {
        this.zipcodeText = val.replace(/\s/g, '').substring(0, 5)
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            // this.checkAdressError('checkZipcodeError')
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistricttext = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.subdistricttext = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    SelectShipping (val) {
      // console.log(val, 55555)
      this.updateSelectedShippingImages()
      // console.log('deee', this.listSelectShipping)
    },
    listSelectShipping (val) {
      // console.log(val, 111111)
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageShippingSellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageShippingSeller' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistricttext && data.amphoe === this.districtText && data.province === this.provinceText && data.zipcode === Number(this.zipcodeText)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    // async SelectRequestCourier () {
    //   // this.getUserShopDetail()
    //   var data = {
    //     seller_shop_id: this.shopID,
    //     role: 'seller'
    //   }
    //   await this.$store.dispatch('actionDetailShop', data)
    //   const response = await this.$store.state.ModuleShop.stateDatailShop
    //   this.userDetail = response.data[0]
    //   if (this.userDetail.address_detail.length > 1) {
    //     for (var i = 0; this.userDetail.address_detail.length > i; i++) {
    //       if (this.userDetail.address_detail[i].default_address === 'pickup') {
    //         var defaultAddress = this.userDetail.address_detail[i]
    //       }
    //     }
    //   } else {
    //     // console.log(defaultAddress, 'defaultAddress')
    //     defaultAddress = this.userDetail.address_detail[0]
    //   }
    //   var address = defaultAddress.detail
    //   this.phoneShop = this.userDetail.shop_phone[0].phone
    //   this.addressRequest = address
    //   this.HouseNo = defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1') === '' || defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1') === '-' ? '-' : defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1')
    //   this.subdistricttext = defaultAddress.sub_district
    //   this.districtText = defaultAddress.district
    //   this.provinceText = defaultAddress.province
    //   this.zipcodeText = defaultAddress.zipcode
    // },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    cancelDialogEdit () {
      this.dialogEditShipping = false
    },
    editShipping () {
      this.showForm = false
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.showFormEdit = true
        this.$store.commit('closeLoader')
      }, 1000)
    },
    cancelEdit () {
      this.GetDetailShop()
      this.showForm = true
      this.showFormEdit = false
      window.scrollTo(0, 0)
    },
    async getListShipping () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const auth = {
        headers: { Authorization: `Bearer ${oneData.user.access_token}` }
      }
      const data = {
        seller_shop_id: this.shopID
      }
      const response = await this.axios.post(`${process.env.VUE_APP_BACK_END2}iship/iship_courier_list`, data, auth)
      if (response.data.ok === 'y') {
        this.itemShipping = [...response.data.query_result.filter(item => item.service_provider === 'ISHIP')]
        // console.log(this.itemShipping, 9787)
      } else if (response.data.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      }
      await this.GetDetailShop()
    },
    async GetDetailShop () {
      // console.log(11111)
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.code === 200) {
          // console.log('seeeee', response.data[0].shipping_method)
          if (response.data[0].shipping_method && response.data[0].shipping_method.length !== 0) {
            this.$store.commit('closeLoader')
            this.switchShipping = true
            this.sameAddressShop = false
            // this.listSelectShipping = response.data[0].shipping_method[0]
            // console.log('see ya', this.listSelectShipping)
            this.SelectShipping = response.data[0].shipping_method[0].courier
            this.SelectShippingShow = response.data[0].shipping_method[0].courier
            this.userDetail = response.data[0]
            if (this.userDetail.address_detail.length > 1) {
              for (var i = 0; this.userDetail.address_detail.length > i; i++) {
                if (this.userDetail.address_detail[i].default_address === 'pickup') {
                  var defaultAddress = this.userDetail.address_detail[i]
                }
              }
            } else {
              // console.log(defaultAddress, 'defaultAddress')
              defaultAddress = this.userDetail.address_detail[0]
            }
            var address = defaultAddress.detail
            this.phoneShop = this.userDetail.shop_phone[0].phone
            this.addressRequest = address
            this.HouseNo = defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1') === '' || defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1') === '-' ? '-' : defaultAddress.house_no.replace(/[^0-9-/\s]/g, '').replace(/(\..*)\./g, '$1')
            this.subdistricttext = defaultAddress.sub_district
            this.districtText = defaultAddress.district
            this.provinceText = defaultAddress.province
            this.zipcodeText = defaultAddress.zipcode
          } else {
            this.$store.commit('closeLoader')
            this.switchShipping = false
            this.SelectShipping = []
          }
        }
        this.switchAtStore = response.data[0].store_front === 'yes'
        // console.log(this.Detail)
      }
    },
    updateSelectedShippingImages () {
      this.listSelectShipping = this.SelectShipping.map(code => {
        const found = this.itemShipping.find(item => item.code === code)
        return found ? { code: found.code, name: found.name, media_path: found.media_path } : { code, name: code, media_path: '' }
      })
      // console.log(this.listSelectShipping)
    },
    removeShipping (item) {
      const index = this.SelectShipping.indexOf(item.code)
      if (index >= 0) this.SelectShipping.splice(index, 1)
    },
    async editShippingSeller () {
      this.$store.commit('openLoader')
      var selectShip = {
        service: 'ISHIP',
        courier: this.SelectShipping
      }
      var data = {
        seller_shop_id: this.shopID,
        store_front: this.switchAtStore === false ? 'no' : 'yes',
        shipping_method: [selectShip]
      }
      // console.log('datadata **', data)
      await this.$store.dispatch('actionEditShippingSeller', data)
      var res = await this.$store.state.ModuleShop.stateEditShippingSeller
      await this.SentDefaultAddress()
      if (res.code && this.CheckResponse === 200) {
        this.dialogEditShipping = false
        this.$store.commit('closeLoader')
        this.dialogSuccess = true
        // await this.GetDetailShop()
        // this.showFormEdit = false
        // this.showForm = true
        // window.scrollTo(0, 0)
        // this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.dialogEditShipping = false
        this.$store.commit('closeLoader')
      }
    },
    async SentDefaultAddress () {
      var data = {
        seller_shop_id: this.shopID,
        house_no: this.HouseNo,
        details: this.addressRequest,
        sub_district: this.subdistricttext,
        district: this.districtText,
        province: this.provinceText,
        postcode: this.zipcodeText,
        phone: this.phoneShop
      }
      await this.$store.dispatch('actionsSentPickupAddress', data)
      var response = await this.$store.state.ModuleDashboardTransport.stateSentPickupAddress
      this.CheckResponse = response.code
    },
    async closeDialogSuccess () {
      this.dialogSuccess = false
      await this.GetDetailShop()
      this.showFormEdit = false
      this.showForm = true
      window.scrollTo(0, 0)
    }
  }
}
</script>

<style>
.validate_error {
    opacity: 1.5;
  }
  .input_text-thai-address-error {
    height: 45px !important;
  }
  .input_text-thai-address {
    border-radius: 8px !important;
  }
  .input_text-thai-address-error input.th-address-input {
    border-radius: 4px;
    padding-left: 10px;
    border: 2px solid #ff5252  !important;
  }
  .input_text-thai-address input.th-address-input {
    color: #212121;
    border-radius: 4px;
    padding-left: 10px;
    /* border: 1px solid red !important; */
  }
  .text-error {
    color: #ff5252;
    font-size: 12px;
    line-height: 12px;
    padding: 3px 12px 2px 0px !important;
}
</style>
<style scoped>
::v-deep .custom-texfield {
  border-radius: 8px;
}
.showData ::v-deep(.v-chip) {
  border: 1px solid rgb(174, 174, 174) !important;
}
</style>
