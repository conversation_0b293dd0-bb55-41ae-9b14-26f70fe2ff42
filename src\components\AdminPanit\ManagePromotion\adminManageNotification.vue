<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการแจ้งเตือนโปรโมชั่นและแคมเปญ</v-card-title>
      <v-card-title style="font-weight: 600; font-size: 18px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> จัดการแจ้งเตือนโปรโมชั่นและแคมเปญ</v-card-title>
      <v-row class="mt-2">
        <v-col cols="12" align="end" class="mr-2">
            <v-btn color="primary" rounded @click="getDataListNoti" class="mr-2" icon ><v-icon dense>mdi-refresh</v-icon></v-btn>
            <v-btn color="primary" rounded @click="openDialogAddNoti('create')">สร้างการแจ้งเตือน</v-btn>
        </v-col>
        <v-col cols="12" md="6" sm="12">
          <v-text-field :class="MobileSize ? 'mx-2' : ''" v-model="searchNoti" placeholder="ค้นหารายการแจ้งเตือน" @input="onInputSearch" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
          </v-col>
        <v-col cols="12">
          <v-data-table
          :headers="header"
          :search="searchNoti"
          :items="itemData"
          style="width:100%; white-space: nowrap;"
          height="100%"
          no-results-text="ไม่พบรายการแจ้งเตือน"
          no-data-text="ไม่พบรายการแจ้งเตือน"
          :items-per-page="10"
          class="elevation-1 mt-4"
          :footer-props="{ 'items-per-page-options': [10, 25, 50], 'items-per-page-text': 'จำนวนแถว' }"
          >
          <template v-slot:[`item.title`]="{ item }">
            <div style="white-space: nowrap; display: flex; flex-direction: column; gap: 5px; padding: 5px 0;">
              <span v-if="!MobileSize" style="font-weight: 600; text-align: start;">{{ item.title !== '' || item.title !== null ? item.title : '-' }}</span>
              <span v-else style="font-weight: 600; font-size: 9px; text-align: end;">{{ item.title !== '' || item.title !== null ? item.title : '-' }}</span>
              <span v-if="!MobileSize" style="text-align: start;">{{ item.message !== '' || item.message !== null ? item.message : '-' }}</span>
              <span v-else style=" font-size: 9px; text-align: end;">{{ item.message !== '' || item.message !== null ? item.message : '-' }}</span>
            </div>
          </template>
          <template v-slot:[`item.image_url`]="{ item }">
            <v-img v-if="item.image_url !== '' || item.image_url === null" :src="item.image_url" width="48" height="48"></v-img>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.noti_for`]="{ item }">
            <v-chip v-if="item.noti_for === 'buyer'" text-color="#2AB4F4" color="rgba(32, 201, 151, 0.10)">ผู้ซื้อ</v-chip>
            <v-chip v-else-if="item.noti_for === 'seller'" text-color="#9C6ADE" color="rgba(179, 157, 219, 0.10)">ผู้ขาย</v-chip>
          </template>
          <template v-slot:[`item.recipient_type`]="{ item }">
            <v-chip v-if="item.recipient_type === 'specific'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">ระบุผู้รับ</v-chip>
            <v-chip v-else-if="item.recipient_type === 'all'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">ทั้งหมด</v-chip>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <v-chip v-if="item.status === 'รอการแจ้งเตือน'" text-color="#FFB800" color="rgba(255, 184, 0, 0.10)">รอการแจ้งเตือน</v-chip>
            <v-chip v-else-if="item.status === 'แจ้งเตือนแล้ว'" text-color="#28A745" color="rgba(40, 167, 69, 0.10)">แจ้งเตือนแล้ว</v-chip>
          </template>
          <template v-slot:[`item.date`]="{ item }">
              <span style="white-space: nowrap;">{{ new Date(item.scheduled_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'UTC' }) }}</span>
          </template>
          <template v-slot:[`item.time`]="{ item }">
              <span style="white-space: nowrap;">{{ new Date(item.scheduled_at).toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit', timeZone: 'UTC' }) + ' น.' }}</span>
          </template>
          <template v-slot:[`item.manage`]="{ item }">
            <v-btn v-if="item" color="primary" small icon class="mr-2" @click="notiID = item.notification_id; openDialogAddNoti('edit')" :disabled="item.is_schedule === false ? true : item.status === 'แจ้งเตือนแล้ว' ? true : false"><v-icon small>mdi-pencil</v-icon></v-btn>
            <v-btn v-if="item" color="error" small icon><v-icon small @click="notiID = item.notification_id; openDialogConfirm('delete')">mdi-delete</v-icon></v-btn>
          </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
    <!-- Dialog เพิ่มการแจ้งเตือน -->
    <v-dialog v-model="dialogAddNoti" persistent :width="MobileSize ? '350' : '500'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            การแจ้งเตือน
          </span>
            <v-btn icon dark small @click="closeDialogAddNoti()">
            <v-icon small color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-form ref="formNoti" v-model="validNoti">
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center">
              <v-text-field class="mb-3" dense v-model="titleNoti" counter maxlength="100" outlined label="หัวเรื่อง" :rules="[Rules.required, Rules.charRules]" :counter-value="v => (v || '').length"></v-text-field>
              <v-text-field class="mb-3" dense v-model="textNoti" counter maxlength="100" outlined label="ข้อความ" :rules="[Rules.required, Rules.charRules]" :counter-value="v => (v || '').length"></v-text-field>
            </v-col>
            <v-col cols="12" style="text-align: start;" class="ml-1">
              <span style="line-height: 16px; font-weight: 400;">รูปภาพ (ไม่จำเป็น)</span>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3">
              <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.5); box-sizing: border-box; border-radius: 8px;">
              <v-row>
                <v-col cols="12" align="center">
                  <v-img v-if="path !== ''" :src="path" style="width: 100px; height: 100%;"></v-img>
                </v-col>
                <v-col cols="12" align="center" v-if="path === '' || path === null">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="200" height="100" contain class="mb-3"></v-img>
                  <span style="line-height: 16px; font-weight: 400; font-size: smaller;">ไฟล์นามสกุล .jpg, .png เท่านั้น</span><br>
                  <span style="line-height: 16px; font-weight: 400; font-size: smaller;">ขนาด 1 : 1  รูปแบบสี่เหลี่ยมจัตุรัส</span>
                </v-col>
                <v-col cols="12" style="text-align: center;" class="mb-2">
                  <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small >
                    เพิ่มรูปภาพ
                    <input
                      ref="fileInput" type="file" :key="fileInputKey" accept="image/jpg, image/png" @change="onFileImage($event)"
                      style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                    />
                  </v-btn>
                  <v-btn v-if="path!== ''" color="error" dense small class="ml-2" @click="removeImage()"><v-icon small>mdi-delete</v-icon></v-btn>
                </v-col>
              </v-row>
              </v-card>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3 mt-1">
              <v-select v-model="selectApp" :items="itemSelectApp" item-text="name" item-value="value" label="ระบุแอปพลิเคชันสำหรับแจ้งเตือน" @change="handleSelectApp" :rules="[Rules.required]" hide-details dense outlined></v-select>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3" v-if="selectApp !== '' && selectApp !== null && selectApp !== undefined">
              <v-select v-model="selectRecipient" :items="itemSelectRecipient" item-text="name" item-value="value" label="ระบุผู้รับการแจ้งเตือน" @change="handleSelectRecipient" :rules="[Rules.required]" hide-details dense outlined></v-select>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3" v-if="selectApp === 'buyer' && selectRecipient === 'specific'">
              <v-autocomplete
                v-model="recipients"
                :items="itemRecipient"
                item-text="name"
                item-value="id"
                label="เพิ่มรายชื่อ (ผู้ซื้อ)"
                placeholder="พิมพ์เพื่อค้นหารายชื่อ"
                :search-input.sync="searchText"
                :filter="() => true"
                @update:search-input="handleItemRecipient"
                :rules="recipientRules"
                chips dense closable-chips multiple outlined hide-details return-object
                >
                <template v-slot:selection="data">
                  <div class="mt-10"></div>
                  <v-chip
                    :key="data.index"
                    :input-value="data.selected"
                    class="mt-1"
                  >
                  <span style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ data.item.name }}</span>
                  </v-chip>
                </template>
                <template v-slot:item="data">
                  <template v-if="typeof data.item !== 'object'">
                    <v-list-item-content>{{ data.item }}</v-list-item-content>
                  </template>
                  <template v-else>
                    <v-list-item-content style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      <v-list-item-title >{{ data.item.name }}</v-list-item-title>
                    </v-list-item-content>
                  </template>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3" v-if="selectApp === 'seller' && selectRecipient === 'specific'">
              <v-autocomplete
                v-model="shop"
                :items="itemShop"
                item-text="name"
                item-value="id"
                label="เพิ่มรายชื่อ (ร้านค้า)"
                :rules="recipientRules"
                chips dense closable-chips multiple outlined hide-details return-object
                >
                <template v-slot:selection="data">
                  <div class="mt-10"></div>
                  <v-chip
                    :key="data.index"
                    :input-value="data.selected"
                    class="mt-1"
                  >
                  <span style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ data.item.name }}</span>
                  </v-chip>
                </template>
                <template v-slot:item="data">
                  <template v-if="typeof data.item !== 'object'">
                    <v-list-item-content>{{ data.item }}</v-list-item-content>
                  </template>
                  <template v-else>
                    <v-list-item-content style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      <v-list-item-title >{{ data.item.name }}</v-list-item-title>
                    </v-list-item-content>
                  </template>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3">
              <v-select v-model="selectSchedule" :items="itemSelectSchedule" item-text="name" item-value="value" label="กำหนด วัน - เวลา แจ้งเตือน" :rules="[Rules.required]" @change="handleSelectSchedule('setup'. selectSchedule)" hide-details dense outlined></v-select>
            </v-col>
            <v-col v-if="selectSchedule === '1'" cols="6" style="text-align: center;" class="mb-3">
              <v-menu
                ref="menuDate"
                v-model="menuDate"
                :close-on-content-click="false"
                :return-value.sync="date"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="date"
                    label="ระบุวันที่"
                    prepend-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                    :rules="scheduleDateRules"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="date"
                  no-title
                  scrollable
                  :min="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="menuDate = false"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.menuDate.save(date); time = ''"
                  >
                    OK
                  </v-btn>
                </v-date-picker>
              </v-menu>
            </v-col>
            <v-col v-if="selectSchedule === '1'" cols="6" style="text-align: center;" class="mb-3">
              <v-menu
              ref="menuTime"
              v-model="menuTime"
              :close-on-content-click="false"
              :nudge-right="40"
              :return-value.sync="time"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="time"
                  label="ระบุเวลา"
                  prepend-icon="mdi-clock-time-four-outline"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                  :rules="scheduleTimeRules"
                ></v-text-field>
              </template>
              <v-time-picker
                v-if="menuTime"
                v-model="time"
                full-width
                format="24hr"
                scrollable
                :min="minTime"
                @click:minute="$refs.menuTime.save(time)"
              ></v-time-picker>
            </v-menu>
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3">
              ตัวอย่างการแจ้งเตือน
            </v-col>
            <v-col cols="12" style="text-align: center;" class="mb-3">
              <div style="width: 98%; height: 180px; background: #F3FFFF; border-top-left-radius: 30px; border-top-right-radius: 30px; box-shadow: inset 0 0 0 5px black; position: relative; margin: 10px auto; overflow: hidden;">
                <div class="mx-auto" style="width: 60%; border: 15px solid black; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;"></div>
                <div style="background: white; border: 2px solid #999; border-radius: 6px; padding: 16px; margin: 20px; display: flex; align-items: center; justify-content: space-between;">
                  <div :style="path !== '' ? 'width: 65%;' : 'width: 100%;'" style="text-align: start;">
                    <div style="display: flex; flex-direction: column;">
                      <span style="font-weight: bold; font-size: small; color: #333; max-width: 100%;" class="text-truncate d-inline-block">{{ titleNoti }}</span>
                      <span style="font-size: small; color: #666; margin-top: 4px; max-width: 100%;" class="text-truncate d-inline-block">{{ textNoti }}</span>
                    </div>
                  </div>
                  <div v-if="path !== ''" style="width: 35%; display: flex; justify-content: end;">
                    <img :src="path" style="width: 48px; height: 48px; border: 1px solid #ccc; border-radius: 4px; background: #f2f2f2;" />
                  </div>
                </div>
              </div>
            </v-col>
          </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogAddNoti()">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="openDialogConfirm(statusConfirm)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog Confirm -->
    <v-dialog v-model="dialogConfirm" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการทำรายการ
          </span>
           <v-btn icon dark @click="dialogConfirm = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: medium; font-weight: 600; line-height: 3;">{{ messageConfirm }}</span><br>
              <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
            </v-col>

          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogConfirm = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmStatus(statusConfirm)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import moment from 'moment'
export default {
  data () {
    return {
      titleNoti: '',
      textNoti: '',
      Rules: {
        charRules: v => (v && v.length <= 100) || 'ข้อความยาวเกิน 100 ตัวอักษร',
        required: v => !!v || 'กรุณากรอกข้อมูล'
      },
      dialogAddNoti: false,
      path: '',
      image: '',
      selectRecipient: null,
      itemSelectRecipient: [
        { name: 'ทั้งหมด', value: 'all' },
        { name: 'เลือกผู้รับ', value: 'specific' }
      ],
      selectApp: null,
      itemSelectApp: [
        { name: 'Nexgen (ผู้ซื้อ)', value: 'buyer' },
        { name: 'Nexgen Seller (ผู้ขาย)', value: 'seller' }
      ],
      recipients: [],
      itemRecipient: [],
      searchText: '',
      debounceTimer: null,
      itemShop: [],
      shop: [],
      searchNoti: '',
      itemData: [],
      header: [
        // { text: 'ID', value: 'notification_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ข้อความ', value: 'title', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รูปภาพ', value: 'image_url', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'แอปพลิเคชัน', value: 'noti_for', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ผู้รับ', value: 'recipient_type', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่', value: 'date', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เวลา', value: 'time', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manage', sortable: false, class: 'backgroundTable fontTable--text ' }
      ],
      selectSchedule: '0',
      itemSelectSchedule: [
        { name: 'แจ้งเตือนทันที', value: '0' },
        { name: 'กำหนด วัน - เวลา แจ้งเตือน', value: '1' }
      ],
      menuDate: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      menuTime: false,
      time: new Date().toTimeString().substr(0, 5),
      fileInputKey: Date.now(),
      validNoti: '',
      dialogConfirm: false,
      messageConfirm: '',
      statusConfirm: '',
      notiID: null,
      minTime: null
    }
  },
  async created () {
    this.getDataListNoti()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    recipientRules () {
      return this.selectRecipient === 'specific'
        ? [v => !!v || 'จำเป็นต้องระบุผู้รับ']
        : []
    },
    scheduleDateRules () {
      return this.selectSchedule === '1'
        ? [v => !!v || 'จำเป็นต้องระบุวันที่']
        : []
    },
    scheduleTimeRules () {
      return this.selectSchedule === '1'
        ? [v => !!v || 'จำเป็นต้องระบุเวลา']
        : []
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminManageNotificationMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminManageNotification' }).catch(() => {})
      }
    },
    date () {
      this.handleDate()
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    handleDate () {
      const selectedDate = new Date(this.date)
      // console.log('selectedDate', selectedDate)
      const now = new Date()
      const isSameDate =
        selectedDate.getFullYear() === now.getFullYear() &&
        selectedDate.getMonth() === now.getMonth() &&
        selectedDate.getDate() === now.getDate()
      if (isSameDate) {
        const hour = now.getHours().toString().padStart(2, '0')
        const minute = now.getMinutes().toString().padStart(2, '0')
        this.minTime = `${hour}:${minute}`
      } else {
        this.minTime = null
      }
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async onFileImage (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]

        if (['image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.image = url

          const reader = new FileReader()
          reader.onload = async () => {
            this.path = reader.result.split(',')[1]

            const data = { image: [this.path], type: 'noti' }

            this.$store.commit('openLoader')

            await this.$store.dispatch('actionsUploadToS3', data)
            const response = this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.$store.commit('closeLoader')
              this.path = response.data.list_path[0].path
            }
            this.fileInputKey = Date.now()
            this.$forceUpdate()
          }
          reader.readAsDataURL(file)
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImage () {
      this.path = ''
      this.image = ''
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
      this.$forceUpdate()
    },
    async getDataListNoti () {
      this.searchNoti = ''
      this.$store.commit('openLoader')
      var data = null
      await this.$store.dispatch('actionsGetDataListNoti', data)
      var response = await this.$store.state.ModuleAdminManage.stateGetDataListNoti
      if (response.code === 200) {
        this.itemData = response.data
        // console.log('this.itemData', this.itemData)
      }
      this.$store.commit('closeLoader')
    },
    // closeDialogAddNoti () {
    //   this.dialogAddNoti = false
    //   this.titleNoti = ''
    //   this.textNoti = ''
    //   this.path = ''
    //   this.image = ''
    //   this.selectRecipient = null
    //   this.selectApp = null
    //   this.recipients = []
    //   this.itemRecipient = []
    //   this.searchText = ''
    //   this.itemShop = []
    //   this.shop = []
    // },
    openDialogAddNoti (status) {
      // console.log('this.notiID', this.notiID)
      this.statusConfirm = status
      if (status === 'create') {
        this.resetFormNoti()
      } else if (status === 'edit') {
        const itemEdit = this.itemData.find(item => item.notification_id === this.notiID)
        // console.log('itemEdit', itemEdit)
        if (itemEdit) {
          this.titleNoti = itemEdit.title || ''
          this.textNoti = itemEdit.message || ''
          this.path = itemEdit.image_url || ''
          this.selectApp = itemEdit.noti_for || null
          this.selectRecipient = itemEdit.recipient_type || null
          this.selectSchedule = itemEdit.scheduled_at ? '1' : '0'
          if (itemEdit.scheduled_at) {
            const [datePart, timePart] = itemEdit.scheduled_at.split('T')
            this.date = datePart
            this.time = timePart.slice(0, 5)
          } else {
            this.date = ''
            this.time = ''
          }
          this.$nextTick(async () => {
            if (this.selectApp === 'buyer' && this.selectRecipient === 'specific') {
              // await this.searchInput('')
              this.recipients = itemEdit.recipients || []
            } else if (this.selectApp === 'seller' && this.selectRecipient === 'specific') {
              await this.getShop()
              this.shop = itemEdit.recipients || []
            }
          })
        }
      }
      this.dialogAddNoti = true
    },
    closeDialogAddNoti () {
      this.dialogAddNoti = false
      this.resetFormNoti()
    },
    resetFormNoti () {
      if (this.$refs.formNoti) {
        this.$refs.formNoti.reset()
        this.$refs.formNoti.resetValidation()
      }
      this.titleNoti = ''
      this.textNoti = ''
      this.path = ''
      this.image = ''
      this.selectRecipient = null
      this.selectApp = null
      this.recipients = []
      this.itemRecipient = []
      this.searchText = ''
      this.itemShop = []
      this.shop = []
      this.selectSchedule = '0'
      this.date = ''
      this.time = ''
      this.$nextTick(() => {
        this.handleSelectSchedule('reset', this.selectSchedule)
      })
    },
    handleSelectApp () {
      this.selectRecipient = null
      this.recipients = []
      this.shop = []
      this.itemRecipient = []
      this.itemShop = []
    },
    async handleSelectRecipient () {
      // console.log('this.recipients 1', this.recipients)
      this.recipients = []
      this.shop = []
      if (this.selectApp === 'buyer' && this.selectRecipient === 'specific') {
        await this.searchInput('')
      } else if (this.selectApp === 'seller' && this.selectRecipient === 'specific') {
        await this.getShop()
      }
      // console.log('this.recipients 2', this.recipients)
    },
    handleItemRecipient (val) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = setTimeout(() => {
        this.searchInput(val)
      }, 1500)
    },
    handleSelectSchedule (status) {
      if (status === 'reset') {
        this.selectSchedule = '0'
        this.date = ''
        this.time = ''
      }
    },
    async searchInput (val) {
      this.$store.commit('openLoader')

      const keyword = val && val.trim() !== '' ? val : ''
      const data = { keyword }

      await this.$store.dispatch('actionsSearchListUser', data)
      const response = this.$store.state.ModuleAdminManage.stateSearchListUser

      if (response.code === 200) {
        const newList = response.data

        // รวมค่าที่เลือกไว้ ที่ยังไม่อยู่ใน itemRecipient ใหม่
        const selectedNotInList = this.recipients.filter(selected => {
          return !newList.some(item => item.id === selected.id)
        })

        this.itemRecipient = [...selectedNotInList, ...newList]
      }

      this.$store.commit('closeLoader')
    },
    async onInputSearch () {
      clearTimeout(this.debounceTimer)
      this.$store.commit('openLoader')

      this.debounceTimer = setTimeout(() => {
        this.performSearch()
      }, 1500)
    },
    async performSearch () {
      const data = { keyword: this.searchNoti }
      await this.$store.dispatch('actionsSearchNoti', data)
      const response = this.$store.state.ModuleAdminManage.stateSearchNoti
      if (response.code === 200) {
        this.itemData = response.data
      }
      this.$store.commit('closeLoader')
    },
    // handleItemShop (val) {
    //   clearTimeout(this.debounceTimer)
    //   this.debounceTimer = setTimeout(() => {
    //     this.searchInput(val)
    //   }, 1500)
    // },
    async getShop () {
      this.$store.commit('openLoader')
      const data = {
        unset_shop_profile: 'YES'
      }
      await this.$store.dispatch('actionsGroupStoreName', data)
      var response = await this.$store.state.ModuleHompage.stateGroupStoreName
      if (response.result === 'SUCCESS') {
        this.itemShop = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
          return {
            name: item.shop_name,
            id: item.seller_shop_id
          }
        })
      } else {
      }
      this.$store.commit('closeLoader')
    },
    async createNoti () {
      if (this.$refs.formNoti.validate()) {
        this.$store.commit('openLoader')

        var data = {}
        var response = {}
        if (this.selectSchedule === '1') {
          data = {
            title: this.titleNoti,
            message: this.textNoti,
            image_url: this.path,
            data: {
              action_type: 'viewPromotionDetail'
            },
            recipient_type: this.selectRecipient,
            recipients: this.selectRecipient === 'specific' && this.selectApp === 'buyer' ? this.recipients : this.selectRecipient === 'specific' && this.selectApp === 'seller' ? this.shop : [],
            noti_for: this.selectApp,
            is_schedule: this.selectSchedule === '1',
            scheduled_at: this.selectSchedule === '1' ? moment.utc(`${this.date}T${this.time}`).toISOString() : ''
          }
          await this.$store.dispatch('actionsCreateNoti', data)
          response = await this.$store.state.ModuleAdminManage.stateCreateNoti
        } else if (this.selectSchedule === '0') {
          data = {
            title: this.titleNoti,
            message: this.textNoti,
            image_url: this.path,
            data: {
              action_type: 'viewBoardcast'
            },
            recipient_type: this.selectRecipient,
            recipients: this.selectRecipient === 'specific' && this.selectApp === 'buyer' ? this.recipients : this.selectRecipient === 'specific' && this.selectApp === 'seller' ? this.shop : [],
            noti_for: this.selectApp,
            is_schedule: false
          }
          await this.$store.dispatch('actionsCreateNotiNow', data)
          response = await this.$store.state.ModuleAdminManage.stateCreateNotiNow
        }
        if (response.code === 200) {
          this.dialogAddNoti = false
          this.closeDialogAddNoti()
          this.closeDialogConfirm()
          this.getDataListNoti()
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
        } else if (response.code === 400) {
          this.dialogConfirm = false
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 1500 })
        } else if (response.code === 500) {
          this.dialogConfirm = false
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', text: 'บันทึกไม่สำเร็จ กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
        }
      } else {
        this.dialogConfirm = false
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณากรอกข้อมูลให้ครบถ้วนและถูกต้อง', showConfirmButton: false, timer: 2000 })
      }
    },
    async deleteNoti () {
      this.$store.commit('openLoader')
      var data = {
        notification_id: this.notiID
      }
      await this.$store.dispatch('actionsDeleteNoti', data)
      var response = await this.$store.state.ModuleAdminManage.stateDeleteNoti
      if (response.code === 200) {
        this.getDataListNoti()
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'ลบการแจ้งเตือนสำเร็จ', showConfirmButton: false, timer: 1500 })
      } else {
        this.$swal.fire({ icon: 'warning', text: 'ลบการแจ้งเตือนไม่สำเร็จ กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2000 })
        this.$store.commit('closeLoader')
      }
      this.closeDialogConfirm()
    },
    async editNoti () {
      this.$store.commit('openLoader')
      var data = {}
      if (this.selectSchedule === '1') {
        data = {
          notification_id: this.notiID,
          title: this.titleNoti,
          message: this.textNoti,
          image_url: this.path,
          data: {
            action_type: 'viewPromotionDetail'
          },
          recipient_type: this.selectRecipient,
          recipients: this.selectRecipient === 'specific' && this.selectApp === 'buyer' ? this.recipients : this.selectRecipient === 'specific' && this.selectApp === 'seller' ? this.shop : [],
          noti_for: this.selectApp,
          is_schedule: this.selectSchedule === '1',
          scheduled_at: this.selectSchedule === '1' ? moment.utc(`${this.date}T${this.time}`).toISOString() : ''
        }
      } else if (this.selectSchedule === '0') {
        data = {
          notification_id: this.notiID,
          title: this.titleNoti,
          message: this.textNoti,
          image_url: this.path,
          data: {
            action_type: 'viewBoardcast'
          },
          recipient_type: this.selectRecipient,
          recipients: this.selectRecipient === 'specific' && this.selectApp === 'buyer' ? this.recipients : this.selectRecipient === 'specific' && this.selectApp === 'seller' ? this.shop : [],
          noti_for: this.selectApp,
          is_schedule: false
        }
      }
      await this.$store.dispatch('actionsEditNoti', data)
      var response = await this.$store.state.ModuleAdminManage.stateEditNoti
      if (response.code === 200) {
        this.getDataListNoti()
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'แก้ไขการแจ้งเตือนสำเร็จ', showConfirmButton: false, timer: 1500 })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'แก้ไขการแจ้งเตือนไม่สำเร็จ กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2000 })
      }
      this.closeDialogConfirm()
      this.closeDialogAddNoti()
    },
    openDialogConfirm (status) {
      this.dialogConfirm = true
      this.statusConfirm = status
      if (status === 'delete') {
        this.messageConfirm = 'คุณต้องการลบการแจ้งเตือนนี้'
      } else if (status === 'edit') {
        this.messageConfirm = 'คุณต้องการแก้ไขการแจ้งเตือนนี้'
      } else if (status === 'create') {
        this.messageConfirm = 'คุณต้องการสร้างการแจ้งเตือนนี้'
      }
    },
    closeDialogConfirm () {
      this.dialogConfirm = false
      this.messageConfirm = ''
      this.statusConfirm = ''
      this.notiID = null
    },
    async confirmStatus (status) {
      if (status === 'delete') {
        await this.deleteNoti()
      } else if (status === 'edit') {
        await this.editNoti()
      } else if (status === 'create') {
        await this.createNoti()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>

</style>
