fastcgi_cache_path /var/cache/nginx/cachedemo-prod levels=1:2 keys_zone=cachedemo-prod:10m max_size=100m inactive=1y use_temp_path=off;
server {
    listen 81;
    #    listen 443 default_server ssl;

    # Configure cache 
    fastcgi_cache cachedemo-prod;
    fastcgi_cache_key $request_method$scheme$host$request_uri;

    # Disable cached responses if admins are logged in (admin session cookie exists)
    fastcgi_cache_bypass $cookie_wordpress_logged_in_abcdefg1234567890
                       $cookie_wordpress_sec_abcdefg1234567890;

        #ssl on;
    #    ssl_certificate /etc/ssl/one.th/one.th.crt;
    #    ssl_certificate_key /etc/ssl/one.th/one.th.key;

    server_name testsitepanit.one.th;
    #access_log on;
    #error_log on;
    access_log /frontlog/myaccess.log;
    error_log /frontlog/myerror.log warn;

    large_client_header_buffers 4 32k;
    #client_max_body_size 256M;

    location / {
        root /usr/share/nginx/html;
        #root /var/www/web;
        #index index.html =404;
        #index index.php;
        try_files $uri $uri/ /index.html =404;
        #try_files $uri $uri/ /index.php =404;
        #try_files $uri /index.html =404; 
        #try_files $uri /index.php =404;

        # The following directives are based on best practices from H5BP Nginx Server Configs
        # https://github.com/h5bp/server-configs-nginx

        # Expire rules for static content
        location ~* \.(?:manifest|appcache|html?|xml|json)$ {
            add_header Cache-Control "max-age=300";
        }

        location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|mp4|ogg|ogv|webm|htc|ttf)$ {
            add_header Cache-Control "max-age=86400";
            access_log off;
            expires -1;
        }
        
        location ~* .(js|css|ttf|ttc|otf|eot|woff|woff2)$ {
            add_header access-control-allow-origin "*";
            expires max;
        }

        location ~* \.(?:css|js)$ {
            add_header Cache-Control "max-age=3600";
            access_log off;
            expires -1;
        }
           
        
       
        
       	#location ~ \.php$ {
          # Keep cached pages for 90 days or set 1 day
          #fastcgi_cache_valid 200 90d;
          #fastcgi_cache_valid 200 1d;
          #fastcgi_connect_timeout 600;
          #fastcgi_send_timeout 600;
          #fastcgi_read_timeout 600;
          
        	# 404
	        #try_files $fastcgi_script_name =404;
	        #fastcgi_split_path_info ^(.+\.php)(./.+)$;
          #fastcgi_split_path_info ^(.+\.php)(/.+)$;

        	# default fastcgi_params
	        include fastcgi_params;
		
	        # fastcgi settings
	        #fastcgi_pass                    **********:9000;
        	#fastcgi_index                   index.php;
	        #fastcgi_buffers                 8 16k;
        	#fastcgi_buffer_size             32k;
		
	        # fastcgi params
	        #fastcgi_param DOCUMENT_ROOT     $realpath_root;
	        #fastcgi_param SCRIPT_FILENAME   $realpath_root$fastcgi_script_name;
	        #fastcgi_param PATH_INFO         $fastcgi_path_info;
	      #}

        # index.php fallback
        #location / {
        #    try_files $uri $uri/ /index.php?$query_string;
        #} 

        proxy_cache_bypass                      $http_upgrade;
        proxy_no_cache                          1;
        proxy_cache_bypass                      1;
        proxy_redirect                          off;
        proxy_cache                             off;
        add_header                      'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        
        expires -1;
    }

}
