<template>
  <v-dialog v-model="dialog" width="732px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
    <v-overlay :value="overlay2">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-card max-height="690" class="rounded-lg">
      <v-toolbar align="center" color="#BDE7D9" dark dense>
        <v-row>
          <v-col class="d-flex justify-space-around">
            <v-toolbar-title>
              <span style="color: #27AB9C;"><b>ส่วนลด</b></span>
            </v-toolbar-title>
          </v-col>
        </v-row>
        <v-btn fab small @click="dialog = !dialog" icon>
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-tabs @change="ChangeOrderModal" v-model="active_tab" class="mt-5" color="#27AB9C">
          <v-tab :key="0">
            <span style="font-size: 16px; font-weight: 400;">
              คูปองส่วนลด
            </span>
          </v-tab>
          <v-tab :key="1">
            <span style="font-size: 16px; font-weight: 400;">
              ใช้คะแนนส่วนลด
            </span>
          </v-tab>
        </v-tabs>
        <span v-show="coupons">
          <v-row class="mt-1">
            <v-col cols="12" md="12" class="mr-0">
              <v-text-field v-model="search" @keyup.enter="SearchCoupon()" class="my-input" append-icon="mdi-magnify"
                placeholder="กรอกโค้ดส่วนลด" :maxLength="6"
                oninput="this.value = this.value.replace(/[^a-zA-Z0-9\s]/g, '').replace(/(\..*)\./g, '$1')" outlined
                dense hide-details></v-text-field>
            </v-col>
          </v-row>
          <div v-if="CouponsIteam.length > 0">
            <v-row class="mt-5">
              <v-col v-for="(items, index) in CouponsIteam" :key="index" cols="12" md="12">
                <v-card class="rounded-lg ">
                  <v-row no-gutters>
                    <v-col cols="12" md="8">
                      <v-container v-if="MobileSize" class="mb-2">
                        <CardCouponMobile :items="items" :keep="false" colorCard="blue" />
                      </v-container>
                      <v-container v-else class="mb-2">
                        <CardCoupon :items="items" :keep="false" colorCard="blue" />
                      </v-container>
                    </v-col>
                    <v-col cols="12" md="4" align="right" v-if="!MobileSize">
                      <v-container justify="center">
                        <v-btn v-if="keep" @click="BookCoupon(items)" outlined dense color="#27AB9C" width="120">
                          ใช้คูปอง
                        </v-btn>
                        <!-- <v-btn v-else @click="CancleBookCoupon(items)" dense outlined disabled color="#27AB9C">
                          ลบคูปอง
                        </v-btn> -->
                      </v-container>
                      <v-container fluid justify="center">
                        <span style="color: #636363; font-size: 12px; font-weight: 600;">
                          ส่วนลดสูงสุด {{ items.real_discount }} บาท
                        </span>
                        <br />
                        <span style="color: #636363; font-size: 12px; font-weight: 600;">
                          ได้รับส่วนลด {{ items.discount }} บาท
                        </span>
                      </v-container>
                    </v-col>
                    <v-col cols="12" v-if="MobileSize && !IpadSize" class="mb-2">
                      <v-row dense justify="center">
                        <v-col cols="7" class="pl-4">
                          <v-btn v-if="keep" @click="BookCoupon(items)" block outlined dense color="#27AB9C" width="120">
                            ใช้คูปอง
                          </v-btn>
                        </v-col>
                        <v-col cols="5">
                          <span style="font-size: 12px; font-weight: 600; color: #333333;">
                            ส่วนลดสูงสุด {{ items.real_discount }} บาท
                          </span>
                          <br />
                          <span style="color: #333333; font-size: 12px; font-weight: 600;">
                            ได้รับส่วนลด {{ items.discount }} บาท
                          </span>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <div v-else>
            <v-container>
              <v-row justify="center" class="mx-2 ">
                <v-col cols="12">
                  <v-row justify="center" class="my-5">
                    <v-img :src="require('@/assets/No-Favorite.png')" max-height="421" max-width="545" height="100%"
                      width="100%" contain></v-img>
                  </v-row>
                </v-col>
                <v-col cols="12">
                  <v-row justify="center" class="my-5">
                    <span
                      style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C; font-size: 24px;"><b>ไม่มีรายการคูปอง</b></span>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </div>
        </span>
        <span v-show="score">
          <v-row class="mt-4" style="margin-bottom: 440px">
            <v-col cols="5" md="4" sm="2">
              <span style="color: #636363; font-size: 16px; font-weight: 700;">คะแนนของฉัน</span>
            </v-col>
            <v-col cols="7" md="8" sm="9">
              <span style="color: #FAAD14; font-size: 18px; font-weight: 400;">{{ Point }} คะแนน</span>
            </v-col>
            <v-col cols="7" md="9" sm="10">
              <v-text-field disabled v-model="scoreToPoint" placeholder="คะแนนที่จะใช้เป็นส่วนลด" outlined dense
                hide-details>
              </v-text-field>
            </v-col>
            <v-col cols="3" md="2" sm="2">
              <v-btn :disabled="scoreToPoint === 0 || Point - scoreToPoint  < 0" @click="BookPoint()"
                class="white--text" dense color="#27AB9C">
                ใช้คะแนน
              </v-btn>
            </v-col>
          </v-row>
        </span>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { Encode, Decode } from '@/services'
export default {
  components: {
    CardCoupon: () => import('@/components/CardCoupon/CardCoupon'),
    CardCouponMobile: () => import('@/components/CardCoupon/CardCouponMobile')
  },
  data () {
    return {
      overlay2: false,
      dialog: false,
      coupons: true,
      score: false,
      keep: true,
      claim: 'employee',
      ChangeOrder: 0,
      scoreToPoint: 0,
      Point: 0,
      data: [],
      CouponsIteam: [],
      companyId: '',
      id_company: -1,
      shop_id: '',
      special_price: '',
      net_price: 0,
      ref_id: '',
      search: '',
      active_tab: 0,
      CopyData: [],
      coupon_id_cancle: '',
      type: '',
      ChackBtnUse: 'Nonsearch'
    }
  },
  mounted () {
    this.$EventBus.$on('CancleBookPoint', this.CancleBookPoint)
    this.$EventBus.$on('CancleBookCoupon', this.CancleBookCoupon)
    this.$EventBus.$on('CancleBookCouponCheckout', this.CancleBookCouponCheckout)
    this.$EventBus.$on('CancleBookPointCheckout', this.CancleBookPointCheckout)
    // this.$EventBus.$on('CancleBookPoint', this.CancleBookPoint)
    // this.$EventBus.$on('CancleBookCoupon', this.CancleBookCoupon)
    // this.$EventBus.$on('CancleBookCouponCheckout', this.CancleBookCouponCheckout)
    // this.$EventBus.$on('CancleBookPointCheckout', this.CancleBookPointCheckout)
    // this.$on('hook:beforeDestroy', () => {
    //   this.$EventBus.$off('CancleBookPoint')
    //   this.$EventBus.$off('CancleBookCoupon')
    //   this.$EventBus.$off('CancleBookCouponCheckout')
    //   this.$EventBus.$off('CancleBookPointCheckout')
    // })
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  created () {
    // this.$EventBus.$on('CancleBookPoint', this.CancleBookPoint)
    // this.$EventBus.$on('CancleBookCoupon', this.CancleBookCoupon)
    // this.$EventBus.$on('CancleBookCouponCheckout', this.CancleBookCouponCheckout)
    // this.$EventBus.$on('CancleBookPointCheckout', this.CancleBookPointCheckout)
    // this.$on('hook:beforeDestroy', () => {
    //   this.$EventBus.$off('CancleBookPoint')
    //   this.$EventBus.$off('CancleBookCoupon')
    //   this.$EventBus.$off('CancleBookCouponCheckout')
    //   this.$EventBus.$off('CancleBookPointCheckout')
    // })
  },
  beforeDestroy () {
    this.$EventBus.$off('CancleBookPoint')
    this.$EventBus.$off('CancleBookCoupon')
    this.$EventBus.$off('CancleBookCouponCheckout')
    this.$EventBus.$off('CancleBookPointCheckout')
  },
  methods: {
    async open (data, dataCoupon, type) {
      this.ChackBtnUse = 'Nonsearch'
      this.type = type
      this.CouponsIteam = data
      this.CopyData = this.CouponsIteam
      this.shop_id = dataCoupon.seller_shop_id
      this.special_price = dataCoupon.special_price_id
      this.net_price = dataCoupon.net_price
      this.id_company = dataCoupon.company_id
      this.dialog = true
      this.coupons = true
      this.score = false
      this.active_tab = 0
      this.search = ''
      localStorage.setItem('seller_shop_id_coupon_point', this.shop_id)
    },

    // ChangeOrderModal เป็นการสลับระหว่างการใช้คูปองและการใช้คะแนน
    async ChangeOrderModal (item) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (item === 0) {
        this.coupons = true
        this.score = false
      } else if (item === 1) {
        // ใช้ในการ Get Point
        var dataScore = {
          seller_shop_id: this.shop_id,
          role_user: dataRole.role,
          company_id: this.id_company,
          special_price: this.special_price,
          net_price: this.net_price
        }
        await this.$store.dispatch('actionsGetPointsWithCart', dataScore)
        var response = await this.$store.state.ModuleMyCouponsPoints.stateGetPointsWithCart
        this.Point = response.data.points.amount
        this.scoreToPoint = response.data.points.maxUse
        this.coupons = false
        this.score = true
      }
    },

    //  BookCoupon เป็นการใช้ในการจองคูปอง(ปุ่มใช้งานคูปอง)
    async BookCoupon (item) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (this.type === 'shoppingcart') {
        if (this.ChackBtnUse === 'Nonsearch') {
          this.coupon_id_cancle = item.couponId
          localStorage.setItem('CouponOrPoint', 'Coupon')
          var data = {
            coupon_sort: 'reserve',
            coupon_id: item.couponId,
            coupon_name: item.name,
            coupon_discount: item.discount,
            point_sort: '',
            point: '',
            note_of_code: '',
            code_discount: ''
          }
          localStorage.setItem('CouponDetail', Encode.encode(data))
          this.$EventBus.$emit('selectCouponPoint')
          this.dialog = false
        } else if (this.ChackBtnUse === 'search') {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
          var dataNGS = {
            coupons_code: item.couponCode,
            seller_shop_id: this.shop_id,
            role_user: dataRole.role,
            company_id: this.id_company,
            net_price: this.net_price
          }
          await this.$store.dispatch('actionsUseCodeFromNGS', dataNGS)
          var responseDataNGS = await this.$store.state.ModuleMyCouponsPoints.stateUseCodeFromNGS
          if (responseDataNGS.code === 200) {
            this.coupon_id_cancle = item.couponId
            localStorage.setItem('CouponOrPoint', 'Coupon')
            data = {
              coupon_sort: 'reserve',
              coupon_id: item.couponId,
              coupon_name: item.name,
              coupon_discount: item.discount,
              point_sort: '',
              point: '',
              note_of_code: '',
              code_discount: ''
            }
            localStorage.setItem('CouponDetail', Encode.encode(data))
            this.$EventBus.$emit('selectCouponPoint')
            this.dialog = false
          }
        }
      } else if (this.type === 'checkout') {
        // ใช้ Coupon ในหน้า CheckoutUI
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (this.ChackBtnUse === 'Nonsearch') {
          if (onedata.cartDataSpecialPrice === 'yes') {
            localStorage.setItem('CouponOrPoint', 'Coupon')
            data = {
              coupon: [{
                coupon_sort: 'reserve',
                coupon_id: item.couponId,
                coupon_name: item.name,
                coupon_discount: item.discount,
                point_sort: '',
                point: ''
              }],
              product_special_price_id: onedata.cartData.product_special_price_id,
              com_perm_id: onedata.cartData.com_perm_id,
              company_position: onedata.cartData.company_position,
              role_user: onedata.cartData.role_user,
              seller_shop_id: onedata.cartData.seller_shop_id,
              shipping: onedata.cartData.shipping,
              note_of_code: onedata.cartData.note_of_code,
              code_discount: onedata.cartData.code_discount
            }
            onedata.cartData = data
            localStorage.setItem('oneData', Encode.encode(onedata))
            this.$EventBus.$emit('selectcouponorpointCheckout')
            this.$EventBus.$emit('SentGetCart')
            this.dialog = false
          } else {
            this.coupon_id_cancle = item.couponId
            localStorage.setItem('CouponOrPoint', 'Coupon')
            data = {
              coupon: [{
                coupon_sort: 'reserve',
                coupon_id: item.couponId,
                coupon_name: item.name,
                coupon_discount: item.discount,
                point_sort: '',
                point: ''
              }],
              com_perm_id: onedata.cartData.com_perm_id,
              company_id: onedata.cartData.company_id,
              company_position: onedata.cartData.company_position,
              product_to_calculate: onedata.cartData.product_to_calculate,
              role_user: onedata.cartData.role_user,
              seller_shop_id: onedata.cartData.seller_shop_id,
              shipping: onedata.cartData.shipping,
              note_of_code: onedata.cartData.note_of_code,
              code_discount: onedata.cartData.code_discount
            }
            onedata.cartData = data
            localStorage.setItem('oneData', Encode.encode(onedata))
            this.$EventBus.$emit('selectcouponorpointCheckout')
            this.$EventBus.$emit('SentGetCart')
            this.dialog = false
          }
        } else if (this.ChackBtnUse === 'search') {
          if (onedata.cartDataSpecialPrice === 'yes') {
            localStorage.setItem('CouponOrPoint', 'Coupon')
            dataNGS = {
              coupons_code: item.couponCode,
              seller_shop_id: this.shop_id,
              role_user: dataRole.role,
              company_id: this.id_company,
              net_price: this.net_price
            }
            console.log('test dataNGS ', dataNGS)
            await this.$store.dispatch('actionsUseCodeFromNGS', dataNGS)
            responseDataNGS = await this.$store.state.ModuleMyCouponsPoints.stateUseCodeFromNGS
            if (responseDataNGS.code === 200) {
              data = {
                coupon: [{
                  coupon_sort: 'reserve',
                  coupon_id: item.couponId,
                  coupon_name: item.name,
                  coupon_discount: item.discount,
                  point_sort: '',
                  point: ''
                }],
                product_special_price_id: onedata.cartData.product_special_price_id,
                com_perm_id: onedata.cartData.com_perm_id,
                company_position: onedata.cartData.company_position,
                role_user: onedata.cartData.role_user,
                seller_shop_id: onedata.cartData.seller_shop_id,
                shipping: onedata.cartData.shipping
              }
              onedata.cartData = data
              localStorage.setItem('oneData', Encode.encode(onedata))
              this.$EventBus.$emit('selectcouponorpointCheckout')
              this.$EventBus.$emit('SentGetCart')
            }
            this.dialog = false
          } else {
            this.coupon_id_cancle = item.couponId
            localStorage.setItem('CouponOrPoint', 'Coupon')
            dataNGS = {
              coupons_code: item.couponCode,
              seller_shop_id: this.shop_id,
              role_user: dataRole.role,
              company_id: this.id_company,
              net_price: this.net_price
            }
            await this.$store.dispatch('actionsUseCodeFromNGS', dataNGS)
            responseDataNGS = await this.$store.state.ModuleMyCouponsPoints.stateUseCodeFromNGS
            if (responseDataNGS.code === 200) {
              data = {
                coupon: [{
                  coupon_sort: 'reserve',
                  coupon_id: item.couponId,
                  coupon_name: item.name,
                  coupon_discount: item.discount,
                  point_sort: '',
                  point: ''
                }],
                com_perm_id: onedata.cartData.com_perm_id,
                company_id: onedata.cartData.company_id,
                company_position: onedata.cartData.company_position,
                product_to_calculate: onedata.cartData.product_to_calculate,
                role_user: onedata.cartData.role_user,
                seller_shop_id: onedata.cartData.seller_shop_id,
                shipping: onedata.cartData.shipping,
                note_of_code: onedata.cartData.note_of_code,
                code_discount: onedata.cartData.code_discount
              }
              onedata.cartData = data
              localStorage.setItem('oneData', Encode.encode(onedata))
              this.$EventBus.$emit('selectcouponorpointCheckout')
              this.$EventBus.$emit('SentGetCart')
            }
            this.dialog = false
          }
        }
      }
    },

    async CancleBookCoupon () {
      var data = {
        coupon_sort: 'cancel',
        coupon_id: this.coupon_id_cancle,
        coupon_name: -1,
        coupon_discount: '',
        point_sort: '',
        point: ''
      }
      localStorage.setItem('CouponDetail', Encode.encode(data))
      this.$EventBus.$emit('getCartTable')
      localStorage.removeItem('CouponDetail')
      this.dialog = false
    },

    async CancleBookCouponCheckout () {
      // ยกเลิกคูปองในหน้า CheckOutUI
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.cartDataSpecialPrice === 'yes') {
        // ยกเลิกคูปองในหน้า CheckOutUI ในส่วนของราคาพิเศษ
        var couponname = onedata.cartData.coupon[0].coupon_name
        var coupondiscount = onedata.cartData.coupon[0].coupon_discount
        var couponid = onedata.cartData.coupon[0].coupon_id
        var data = {
          coupon: [{
            coupon_sort: 'cancel',
            coupon_id: couponid,
            coupon_name: couponname,
            coupon_discount: coupondiscount
          }],
          product_special_price_id: onedata.cartData.product_special_price_id,
          com_perm_id: onedata.cartData.com_perm_id,
          company_id: onedata.cartData.company_id,
          company_position: onedata.cartData.company_position,
          product_to_calculate: onedata.cartData.product_to_calculate,
          role_user: onedata.cartData.role_user,
          seller_shop_id: onedata.cartData.seller_shop_id,
          shipping: onedata.cartData.shipping,
          note_of_code: onedata.cartData.note_of_code,
          code_discount: onedata.cartData.code_discount
        }
        onedata.cartData = data
        localStorage.setItem('oneData', Encode.encode(onedata))
        await this.$EventBus.$emit('SentGetCart')
        this.dialog = false
      } else {
        // ยกเลิกคูปองในหน้า CheckOutUI ในส่วนของราคาธรรมดา
        couponname = onedata.cartData.coupon[0].coupon_name
        coupondiscount = onedata.cartData.coupon[0].coupon_discount
        couponid = onedata.cartData.coupon[0].coupon_id
        data = {
          coupon: [{
            coupon_sort: 'cancel',
            coupon_id: couponid,
            coupon_name: couponname,
            coupon_discount: coupondiscount
          }],
          com_perm_id: onedata.cartData.com_perm_id,
          company_id: onedata.cartData.company_id,
          company_position: onedata.cartData.company_position,
          product_to_calculate: onedata.cartData.product_to_calculate,
          role_user: onedata.cartData.role_user,
          seller_shop_id: onedata.cartData.seller_shop_id,
          shipping: onedata.cartData.shipping,
          note_of_code: onedata.cartData.note_of_code,
          code_discount: onedata.cartData.code_discount
        }
        onedata.cartData = data
        localStorage.setItem('oneData', Encode.encode(onedata))
        await this.$EventBus.$emit('SentGetCart')
        this.dialog = false
      }
    },

    // ฟังก์ชั่นการใช้คะแนน
    async BookPoint () {
      if (this.type === 'shoppingcart') {
        // ใช้คะแนนในหน้า Shoppingcart
        localStorage.setItem('CouponOrPoint', 'Point')
        var data = {
          coupon_sort: '',
          coupon_id: '',
          coupon_name: '',
          coupon_discount: '',
          point_sort: 'reserve',
          point: this.scoreToPoint
        }
        localStorage.setItem('PointDetail', Encode.encode(data))
        this.$EventBus.$emit('selectCouponPoint')
        this.dialog = false
      } else if (this.type === 'checkout') {
        // ใช้คะแนนในหน้า CheckOutUI
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (onedata.cartDataSpecialPrice === 'yes') {
          // ใช้คะแนนในหน้า CheckOutUI ในส่วนของราคาพิเศษ
          localStorage.setItem('CouponOrPoint', 'Point')
          data = {
            coupon: [{
              coupon_sort: '',
              coupon_id: '',
              coupon_name: '',
              coupon_discount: '',
              point_sort: 'reserve',
              point: this.scoreToPoint
            }],
            product_special_price_id: onedata.cartData.product_special_price_id,
            com_perm_id: onedata.cartData.com_perm_id,
            company_id: onedata.cartData.company_id,
            company_position: onedata.cartData.company_position,
            product_to_calculate: onedata.cartData.product_to_calculate,
            role_user: onedata.cartData.role_user,
            seller_shop_id: onedata.cartData.seller_shop_id,
            shipping: onedata.cartData.shipping,
            note_of_code: onedata.cartData.note_of_code,
            code_discount: onedata.cartData.code_discount
          }
          onedata.cartData = data
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.$EventBus.$emit('selectcouponorpointCheckout')
          this.$EventBus.$emit('SentGetCart')
          this.dialog = false
        } else {
          // ใช้คะแนนในหน้า CheckOutUI ในส่วนของราคาธรรมดา
          localStorage.setItem('CouponOrPoint', 'Point')
          data = {
            coupon: [{
              coupon_sort: '',
              coupon_id: '',
              coupon_name: '',
              coupon_discount: '',
              point_sort: 'reserve',
              point: this.scoreToPoint
            }],
            com_perm_id: onedata.cartData.com_perm_id,
            company_id: onedata.cartData.company_id,
            company_position: onedata.cartData.company_position,
            product_to_calculate: onedata.cartData.product_to_calculate,
            role_user: onedata.cartData.role_user,
            seller_shop_id: onedata.cartData.seller_shop_id,
            shipping: onedata.cartData.shipping,
            note_of_code: onedata.cartData.note_of_code,
            code_discount: onedata.cartData.code_discount
          }
          onedata.cartData = data
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.$EventBus.$emit('selectcouponorpointCheckout')
          this.$EventBus.$emit('SentGetCart')
          this.dialog = false
        }
      }
    },

    async CancleBookPoint () {
      // ยกเลิกการใช้ใช้คะแนน ในหน้า ShoppingCart
      localStorage.setItem('CouponOrPoint', 'Point')
      var data = {
        coupon_sort: '',
        coupon_id: '',
        coupon_name: '',
        coupon_discount: '',
        point_sort: 'cancel',
        point: this.scoreToPoint
      }
      localStorage.setItem('PointDetail', Encode.encode(data))
      this.$EventBus.$emit('getCartTable')
      localStorage.removeItem('PointDetail')
      this.dialog = false
    },

    async CancleBookPointCheckout () {
      // ยกเลิกการใช้คะแนน ในหน้า Checkout
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.cartDataSpecialPrice === 'yes') {
        // ยกเลิกการใช้คะแนน ในราคาพิเศษ
        localStorage.setItem('CouponOrPoint', 'Point')
        var point = onedata.cartData.coupon[0].point
        var data = {
          coupon: [{
            coupon_sort: '',
            coupon_id: '',
            coupon_name: '',
            coupon_discount: '',
            point_sort: 'cancel',
            point: point
          }],
          product_special_price_id: onedata.cartData.product_special_price_id,
          com_perm_id: onedata.cartData.com_perm_id,
          company_id: onedata.cartData.company_id,
          company_position: onedata.cartData.company_position,
          product_to_calculate: onedata.cartData.product_to_calculate,
          role_user: onedata.cartData.role_user,
          seller_shop_id: onedata.cartData.seller_shop_id,
          shipping: onedata.cartData.shipping,
          note_of_code: onedata.cartData.note_of_code,
          code_discount: onedata.cartData.code_discount
        }
        onedata.cartData = data
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.$EventBus.$emit('SentGetCart')
        localStorage.removeItem('PointDetail')
        this.dialog = false
      } else {
        // ยกเลิกการใช้คะแนน ธรรมดา
        localStorage.setItem('CouponOrPoint', 'Point')
        point = onedata.cartData.coupon[0].point
        data = {
          coupon: [{
            coupon_sort: '',
            coupon_id: '',
            coupon_name: '',
            coupon_discount: '',
            point_sort: 'cancel',
            point: point
          }],
          com_perm_id: onedata.cartData.com_perm_id,
          company_id: onedata.cartData.company_id,
          company_position: onedata.cartData.company_position,
          product_to_calculate: onedata.cartData.product_to_calculate,
          role_user: onedata.cartData.role_user,
          seller_shop_id: onedata.cartData.seller_shop_id,
          shipping: onedata.cartData.shipping,
          note_of_code: onedata.cartData.note_of_code,
          code_discount: onedata.cartData.code_discount
        }
        onedata.cartData = data
        localStorage.setItem('oneData', Encode.encode(onedata))
        this.$EventBus.$emit('SentGetCart')
        localStorage.removeItem('PointDetail')
        this.dialog = false
      }
      localStorage.removeItem('CouponOrPoint')
    },
    async SearchCoupon () {
      // ใช้ค้นหา code ขอคูปองในหน้าShoppingCartUI  และหน้า checkout
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.overlay2 = true
      if (this.search === '') {
        this.CouponsIteam = this.CopyData
        this.ChackBtnUse = 'Nonsearch'
      } else {
        this.ChackBtnUse = 'search'
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var dataSearch = {
          seller_shop_id: this.shop_id,
          role_user: dataRole.role,
          company_id: this.id_company,
          key: this.search,
          type: onedata.cartDataSpecialPrice === 'yes' ? 'special_price' : dataRole.role,
          net_price: this.net_price
        }
        await this.$store.dispatch('actionsSearchCoupon', dataSearch)
        var response = await this.$store.state.ModuleMyCouponsPoints.stateSearchCoupon
        if (response.code === 200) {
          this.CouponsIteam = []
          for (let i = 0; i < response.data.length; i++) {
            this.CouponsIteam.push({
              image: response.data[i].couponImagePath,
              name: response.data[i].couponName,
              description: response.data[i].couponDescription,
              couponDate: {
                useStartDate: response.data[i].useStartDate,
                useEndDate: response.data[i].useStartDate
              },
              shop_name: response.data[i].shop_name,
              couponCode: response.data[i].couponCode,
              couponId: response.data[i].couponId,
              status: response.data[i].couponType,
              discount: response.data[i].discount,
              real_discount: response.data[i].real_discount
            })
          }
        }
      }
      this.overlay2 = false
    }
  }

}
</script>

<style lang="scss" scoped>
.a {
    border: #E6E6E6 1px dashed;
}
</style>
<style >
.my-input input {
  text-transform: lowercase
}
</style>
