<template>
 <v-card width="100%" elevation="0">
    <v-card-text class="px-1">
      <v-row style="background: #D8EFE4; border-radius: 12px;" :class="isMobile ? 'mt-5 mx-1 mb-6' : IpadSize ? 'mx-2' : 'mt-5 ml-1 mb-6'">
        <v-col cols="12" md="7" sm="7" :class="isMobile ? 'pt-4' : 'py-2'">
          <div :class="isMobile ? 'd-flex align-center justify-center' : IpadSize ? 'd-flex align-center justify-center pt-4' : 'd-flex align-center justify-end pt-4'">
            <span class="" style="font-weight: 500; font-size: 24px; color: #636363;">Success Transaction</span>
          </div>
        </v-col>
        <v-col cols="12" md="5" sm="5" :class="isMobile ? 'pb-4' : 'py-2'">
          <div :class="isMobile ? 'd-flex align-center justify-center' : IpadSize ? 'd-flex align-center justify-start pt-4' : 'd-flex align-center justify-start pt-2'"  style="color: #42B971;">
            <span style="font-size: 40px; font-weight: 700;">{{$store.state.ModuleAdminManage.dashboardSummary.transaction}}</span>
          </div>
        '</v-col>
      </v-row>
      <v-row :class="isMobile ? 'mt-5 mb-6 mx-1' : IpadSize ? 'mx-2 mt-6' : 'mt-5 mb-6'" :style="IpadSize ? 'border: 1px solid #EBEBEB; border-radius: 8px;' : ''">
        <v-col cols="12" md="6" sm="6" class="pr-0" :class="IpadSize ? 'pl-0' : ''" :align="IpadSize ? 'center' : ''">
          <v-card :outlined="IpadSize ? false : true" elevation="0" width="100%" height="100%" style="background: #FFFFFF; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;" :style="IpadSize ? '' : 'border: 1px solid #EBEBEB;'">
            <v-card-title>
              <v-avatar size="40">
                <img
                src="@/assets/icon_image/userSum1.png"
                alt="Product"
                >
              </v-avatar>
              <div class="mx-3"></div>
              <div v-if="isMobile">
                <span style="font-size: 18px; color: #333333; font-weight: 700;">User Summary</span> &nbsp;&nbsp;
              </div>
              <div v-else >
                <span style="font-size: 16px; color: #333333; font-weight: 700;">User Summary</span> &nbsp;&nbsp;
              </div>
              <!-- <span style="font-size: 16px; color: #333333; font-weight: 700;">User Summary</span> -->
              <!-- <div class="mx-8"></div>
              <v-btn v-if="isMobile" color="teal" fab text class="ml-0" @click="dialogDataProduct">
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
              <v-btn v-else outlined color="teal" class="ml-0" @click="dialogDataProduct">
                ดูทั้งหมด
                <v-icon>mdi-chevron-right</v-icon>
              </v-btn> -->
            </v-card-title>
            <!--   <hr/> -->
            <v-container>
              <v-row dense>
                <!-- <v-col cols="12">
                  <v-card
                    color="#385F73"
                    dark
                  >
                    <v-card-title class="text-h5">
                      Total of user
                    </v-card-title>

                    <v-card-subtitle>Listen to your favorite artists and albums whenever and wherever, online and offline.</v-card-subtitle>

                    <v-card-actions>
                      <v-btn text>
                        Listen Now
                      </v-btn>
                    </v-card-actions>
                  </v-card>
                </v-col> -->
                <v-col>
                  <div class="d-flex justify-center">
                    <div style="background: #FAFAFA; width: 100%; height: 143; border-radius: 8px; padding: 5px;">
                      <v-row dense class="py-2">
                        <v-col :cols="isMobile ? '6' : '12'" md="7" sm="6" style="text-align: end;" class="px-0">
                          Total of User :
                        </v-col>
                        <v-col :cols="isMobile ? '6' : '12'" md="5" sm="6">
                          {{$store.state.ModuleAdminManage.dashboardSummary.totalUser}}
                        </v-col>
                      </v-row>
                      <v-row dense class="py-2">
                        <v-col :cols="isMobile ? '6' : '12'" md="7" sm="6" style="text-align: end;" class="px-0">
                          Active User :
                        </v-col>
                        <v-col :cols="isMobile ? '6' : '12'" md="5" sm="6">
                          {{$store.state.ModuleAdminManage.dashboardSummary.activeUser}}
                        </v-col>
                      </v-row>
                      <v-row dense class="py-2">
                        <v-col :cols="isMobile ? '6' : '12'" md="7" sm="6" style="text-align: end;" class="px-0">
                          New of User :
                        </v-col>
                        <v-col :cols="isMobile ? '6' : '12'" md="5" sm="6">
                          {{$store.state.ModuleAdminManage.dashboardSummary.newUser}}
                        </v-col>
                      </v-row>
                      <!-- <v-card-title
                        class="text-h6 font-weight-black justify-center"
                        align="center"
                      >
                        <span style="color: #636363;">{{item.title}}</span>
                      </v-card-title>

                      <v-card-subtitle class="text-h5" ><span style="color: #333333;" >{{item.artist}}</span></v-card-subtitle> -->
                    </div>
                  </div>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="12" md="6" sm="6" class="pr-0" :align="IpadSize ? 'center' : ''">
          <v-card :outlined="IpadSize ? false : true" elevation="0" width="100%" height="100%" style="background: #FFFFFF; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;" :style="IpadSize ? '' : 'border: 1px solid #EBEBEB;'">
            <v-card-title>
              <v-avatar size="40">
                <img
                  src="@/assets/icon_image/userSum2.png"
                  alt="Product"
                >
              </v-avatar>
              <div class="mx-3"></div>
              <div v-if="isMobile">
                <span style="font-size: 18px; color: #333333; font-weight: 700;">Market Summary</span> &nbsp;&nbsp;
              </div>
              <div v-else >
                <span style="font-size: 16px; color: #333333; font-weight: 700;">Market Summary</span> &nbsp;&nbsp;
              </div>
              <!-- <div class="mx-8"></div>
              <v-btn v-if="isMobile" @click="dialogDataUser" fab text class="ml-0">
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
              <v-btn v-else @click="dialogDataUser" outlined color="teal" class="ml-0">
                ดูทั้งหมด
                <v-icon>mdi-chevron-right</v-icon>
              </v-btn> -->
            </v-card-title>
            <!--  <hr/> -->
            <v-container>
              <v-row dense>
                <!--  <v-col cols="12">
                    <v-card
                      color="#385F73"
                      dark
                    >
                      <v-card-title class="text-h5">
                        Unlimited music now
                      </v-card-title>

                      <v-card-subtitle>Listen to your favorite artists and albums whenever and wherever, online and offline.</v-card-subtitle>

                      <v-card-actions>
                        <v-btn text>
                          Listen Now
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-col> -->
                <v-col cols="12">
                  <!--  <v-card
                    :color="item.color"
                    dark
                    height="144px"
                  > -->
                  <div class="d-flex justify-center">
                    <div style="background: #FAFAFA; width: 100%; height: 143; border-radius: 8px; padding: 5px;">
                      <v-row dense class="py-2">
                        <v-col :cols="isMobile ? '6' : '12'" md="8" sm="6" style="text-align: end;" class="px-0">
                          Total of Seller :
                        </v-col>
                        <v-col :cols="isMobile ? '6' : '12'" md="4" sm="6">
                          {{$store.state.ModuleAdminManage.dashboardSummary.total}}
                        </v-col>
                      </v-row>
                      <v-row dense class="py-2">
                        <v-col :cols="isMobile ? '6' : '12'" md="8" sm="6" style="text-align: end;" class="px-0">
                          Active Seller :
                        </v-col>
                        <v-col :cols="isMobile ? '6' : '12'" md="4" sm="6">
                          {{$store.state.ModuleAdminManage.dashboardSummary.active}}
                        </v-col>
                      </v-row>
                      <v-row dense class="py-2">
                        <v-col :cols="isMobile ? '6' : '12'" md="8" sm="6" style="text-align: end;" class="px-0">
                          New of Seller :
                        </v-col>
                        <v-col :cols="isMobile ? '6' : '12'" md="4" sm="6" class="px-0">
                          {{$store.state.ModuleAdminManage.dashboardSummary.new}}
                        </v-col>
                      </v-row>
                      <!-- <v-card-title
                        class="text-h6 font-weight-black justify-center"
                        align="center"
                      >
                        <span style="color: #636363;">{{item.title}}</span>
                      </v-card-title>

                      <v-card-subtitle class="text-h5" ><span style="color: #333333;" >{{item.artist}}</span></v-card-subtitle> -->
                    </div>

                    <!-- <v-avatar
                      class="ma-3"
                      size="125"
                      tile
                    >
                      <v-img :src="item.src"></v-img>
                    </v-avatar> -->
                  </div>
                  <!-- </v-card> -->
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script>
export default {
  data () {
    return {
      items: [
        {
          color: '#FAFAFA',
          src: 'https://cdn.vuetifyjs.com/images/cards/foster.jpg',
          title: 'Total of user',
          artist: '2,550,800'
        }
      ]
    }
  },
  created () {
  },
  methods: {
  },
  computed: {
    isMobile () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>
<style lang="css" scoped>

</style>
