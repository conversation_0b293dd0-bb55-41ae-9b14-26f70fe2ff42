<template>
  <v-container class="pt-0">
    <v-row dense>
      <v-col cols="12">
        <v-tabs
          v-model="tab"
          background-color="transparent"
          color="#27AB9C"
          style="border-bottom: 2px solid #DAF1E9;"
        >
          <v-tab
            v-for="(item, index) in itemsTab"
            :key="index"
          >
            <span :style="tab === index ? 'font-size: 20px; font-weight: 700; line-height: 140%;' : 'font-size: 18px; font-weight: 500; line-height: 140%; color: #333333;'">{{ item.text }}</span>
            <v-chip class="ml-1" :color="index === 0 ? 'rgba(39, 171, 156, 0.10)' : index === 1 ? '#F0FEE8' : 'rgba(245, 34, 45, 0.10)'" :style="index === 0 ? 'color: #27AB9C;' : index === 1 ? 'color: #52C41A;' : 'color: #F5222D;'" style="font-size: 14px; font-weight: 400;">{{ item.value }}</v-chip>
          </v-tab>
        </v-tabs>
      </v-col>
    </v-row>
    <v-row v-if="props.total_products_count === 0">
       <v-col cols="12" sm="12" class="pl-3">
        <v-row dense>
          <v-col cols="12" md="9" sm="12">
            <v-row dense>
              <span class="pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;">รายการสินค้าทั้งหมด 0 รายการ</span>
              <v-text-field class="ml-1" v-model="search" dense outlined hide-details style="max-width: 350px; border-radius: 8px;" placeholder="ค้นหาข้อมูลสินค้า">
                <v-icon slot="append" color="#CCCCCC">mdi-magnify </v-icon>
              </v-text-field>
            </v-row>
          </v-col>
          <v-col cols="12" md="3" sm="12">
            <v-row dense :class="MobileSize ? 'mt-2' : ''">
              <span class="pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;">ประเภท</span>
              <v-select class="ml-1 setCustomSelect" v-model="selectType" :items="itemsSelect" append-icon="mdi-chevron-down" :menu-props="{ offsetY: true }" item-text="text" item-value="value" dense outlined hide-details style="border-radius: 8px;" :style="MobileSize ? '' : 'max-width: 194px;'">
              </v-select>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="props.total_products_count !== 0">
      <v-col cols="12" class="pl-3">
        <v-row dense>
          <v-col cols="12" md="12" sm="12" :class="MobileSize || IpadSize ? 'mt-2' : 'mt-1'">
            <v-row dense :class="MobileSize ? 'mt-2' : ''">
              <v-text-field class="ml-1" v-model="search" dense outlined hide-details :style="!MobileSize && !IpadSize ? 'max-width: 100%;' : 'max-width: 100%;'" style=" border-radius: 8px;" placeholder="ค้นหาข้อมูลสินค้า">
                <v-icon slot="append" color="#CCCCCC">mdi-magnify </v-icon>
              </v-text-field>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="6" align="center" class="pl-0" :class="MobileSize || IpadSize ? 'mt-4' : 'mt-4'">
            <v-row dense :class="MobileSize ? 'mt-2' : 'pt-0'">
              <v-col cols="4" class="pl-1 pr-0 pt-3">
                <span class="pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;">ประเภท : </span>
              </v-col>
              <v-col cols="8">
                <v-select class="ml-1 setCustomSelect" v-model="selectType" :items="itemsSelect" append-icon="mdi-chevron-down" :menu-props="{ offsetY: true }" item-text="text" item-value="value" dense outlined hide-details style="border-radius: 8px;" :style="MobileSize ? '' : IpadSize ? '' : ''">
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="6" align="center" class="pl-0" :class="MobileSize || IpadSize ? 'mt-4' : 'mt-4'">
            <v-row dense :class="MobileSize ? 'mt-2' : ''">
              <v-col cols="4" md="4" sm="5" class="pl-1 pr-0 pt-3">
                <span class="pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;">สถานะสินค้า : </span>
              </v-col>
              <v-col cols="8" md="8" sm="7">
                <v-select class="ml-1 setCustomSelect" v-model="selectStatus" :items="itemsStatus" append-icon="mdi-chevron-down" :menu-props="{ offsetY: true }" item-text="text" item-value="value" dense outlined hide-details style="border-radius: 8px;" :style="MobileSize ? '' : IpadSize ? '' : ''">
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="6" align="center" class="pl-0 pr-0" :class="MobileSize || IpadSize ? 'mt-4' : 'mt-4'">
            <v-row dense :class="MobileSize ? 'mt-2' : ''">
              <v-col cols="4" md="4" sm="5" class="pl-1 pr-0 pt-3">
                <span class="pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;">สถานะการขาย : </span>
              </v-col>
              <v-col cols="8" md="8" sm="7">
                <v-select class="ml-1 setCustomSelect" v-model="selectStatusProduct" :items="itemsStatusProduct" append-icon="mdi-chevron-down" :menu-props="{ offsetY: true }" item-text="text" item-value="value" dense outlined hide-details style="border-radius: 8px;" :style="MobileSize ? '' : IpadSize ? '' : ''">
                </v-select>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row justify="center">
      <!-- <v-row>
        <v-col cols="3">
          <v-btn color="#27AB9C" height="40" v-if="openSyncERP" rounded :disabled="Selected.length === 0" @click="openSyncProducts()" class="mr-2 white--text"><v-icon left>mdi-cog-sync-outline</v-icon>ซิงค์สินค้า ERP</v-btn>
        </v-col>
        <v-col cols="3">
          <v-btn color="#27AB9C" height="40" v-if="openSyncERP" rounded :disabled="Selected.length === 0" @click="updateSyncProducts()" class="mr-2 white--text"><v-icon left>mdi-sync</v-icon>อัพเดทสต๊อก ERP</v-btn>
        </v-col>
        <v-col cols="3">
          <v-btn color="#27AB9C" height="40" rounded :disabled="Selected.length === 0" @click="changeStatus()" class="mr-2 white--text"><v-icon left>mdi-list-status</v-icon>เปลี่ยนสถานะสินค้า</v-btn>
        </v-col>
        <v-col cols="4">
          <v-btn color="#27AB9C" height="40" outlined rounded :disabled="Selected.length === 0" @click="exportExcelProducts()" class="mr-2">Export รายการสินค้า</v-btn>
        </v-col>
        <v-col cols="4">
          <v-btn color="#27AB9C" height="40" outlined rounded :disabled="Selected.length === 0" @click="deleteProducts()">ลบสินค้าที่เลือก</v-btn>
        </v-col>
      </v-row> -->
      <v-col cols="12" align="end" class="d-flex justify-end" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <v-btn color="#27AB9C" height="40" v-if="openSyncERP" rounded @click="openSyncProducts('sync')" class="mr-2 white--text"><v-icon left>mdi-cog-sync-outline</v-icon>ซิงค์สินค้า ERP</v-btn>
        <v-btn color="#27AB9C" height="40" v-if="openSyncERP" rounded @click="updateSyncProducts('update')" class="mr-2 white--text"><v-icon left>mdi-sync</v-icon>อัพเดทสต๊อก ERP</v-btn>
        <v-btn color="#27AB9C" height="40" rounded :disabled="Selected.length === 0" @click="changeStatus()" class="mr-2 white--text"><v-icon left>mdi-list-status</v-icon>เปลี่ยนสถานะสินค้า</v-btn>
        <v-btn color="#27AB9C" height="40" outlined rounded :disabled="Selected.length === 0" @click="exportExcelProducts()" class="mr-2">Export รายการสินค้า</v-btn>
        <v-btn color="#27AB9C" height="40" outlined rounded :disabled="Selected.length === 0" @click="deleteProducts()">ลบสินค้าที่เลือก</v-btn>
      </v-col>
      <v-col cols="12" v-if="(!MobileSize && IpadSize) || IpadProSize">
        <v-row dense>
          <v-col cols="6" v-if="openSyncERP">
            <v-btn color="#27AB9C" block height="40" rounded @click="openSyncProducts('sync')" class="mr-2 white--text"><v-icon left>mdi-cog-sync-outline</v-icon>ซิงค์สินค้า ERP</v-btn>
          </v-col>
          <v-col cols="6" v-if="openSyncERP">
            <v-btn color="#27AB9C" block height="40" rounded @click="updateSyncProducts('update')" class="mr-2 white--text"><v-icon left>mdi-sync</v-icon>อัพเดทสต๊อก ERP</v-btn>
          </v-col>
          <v-col :cols="IpadSize ? 6 : 4">
            <v-btn color="#27AB9C" block height="40" rounded :disabled="Selected.length === 0" @click="changeStatus()" class="mr-2 white--text"><v-icon left>mdi-list-status</v-icon>เปลี่ยนสถานะสินค้า</v-btn>
          </v-col>
          <v-col :cols="IpadSize ? 6 : 4">
            <v-btn color="#27AB9C" block height="40" outlined rounded :disabled="Selected.length === 0" @click="exportExcelProducts()">Export รายการสินค้า</v-btn>
          </v-col>
          <v-col :cols="IpadSize ? 6 : 4">
            <v-btn color="#27AB9C" block height="40" outlined rounded :disabled="Selected.length === 0" @click="deleteProducts()">ลบสินค้าที่เลือก</v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" v-if="MobileSize && !IpadSize">
        <v-row dense>
          <v-col cols="12">
            <v-btn color="#27AB9C" block height="40" v-if="openSyncERP" rounded @click="openSyncProducts('sync')" class="mr-2 white--text"><v-icon left>mdi-cog-sync-outline</v-icon>ซิงค์สินค้า ERP</v-btn>
          </v-col>
          <v-col cols="12">
            <v-btn color="#27AB9C" block height="40" v-if="openSyncERP" rounded @click="updateSyncProducts('update')" class="mr-2 white--text"><v-icon left>mdi-sync</v-icon>อัพเดทสต๊อก ERP</v-btn>
          </v-col>
          <v-col cols="12">
            <v-btn color="#27AB9C" block height="40" rounded :disabled="Selected.length === 0" @click="changeStatus()" class="mr-2 white--text"><v-icon left>mdi-list-status</v-icon>เปลี่ยนสถานะสินค้า</v-btn>
          </v-col>
          <v-col cols="12">
            <v-btn color="#27AB9C" block height="40" outlined rounded :disabled="Selected.length === 0" @click="exportExcelProducts()">Export รายการสินค้า</v-btn>
          </v-col>
          <v-col cols="12">
            <v-btn color="#27AB9C" block height="40" outlined rounded :disabled="Selected.length === 0" @click="deleteProducts()">ลบสินค้าที่เลือก</v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="12" sm="12" :style="IpadSize? 'max-width: 100%; overflow: hidden;' : 'max-width: 100%;'">
        <v-card :outlined="((filterItemTable !== undefined && filterItemTable.length !== 0) && !MobileSize) ? true : false" elevation="0" class="mb-4" :style="IpadSize? 'max-width: 100%; overflow: scroll;' : 'max-width: 100%;'">
          <v-data-table
           :headers="(JV === 'yes' && !MobileSize) ? headersJV : (JV !== 'yes' && !MobileSize) ? headers : headersMobile"
           v-model="Selected"
           :items="filterItemTable"
           :items-per-page="10"
           :search="search"
           :footer-props="{'items-per-page-text': 'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
           @pagination="countProduct"
           show-select
           item-key="product_id"
           @toggle-select-all="selectAllToggle"
           :hide-default-footer="(filterItemTable !== undefined && filterItemTable.length !== 0) ? false : true"
           :hide-default-header="MobileSize ? true : false"
           no-results-text="ไม่พบรายการสินค้าที่ค้นหา"
           no-data-text="ไม่มีรายการสินค้าในตาราง"
           style="white-space: nowrap;"
           :style="IpadSize ? 'max-width: 100%; overflow: hidden !important;' : MobileSize ? 'max-width: 100%; overflow: hidden !important;' : ''"
           :update:items-per-page="getItemPerPage"
           :class="MobileSize ? 'px-0' : 'elevation-1'"
          >
            <template v-slot:[`header.data-table-select`]="{ on , props }">
              <v-simple-checkbox
                v-model="allSelected"
                :indeterminate="checkboxIndeterminate"
                :ripple="false"
                v-bind="props"
                v-on="on"
                color="#27AB9C"
              ></v-simple-checkbox>
            </template>
            <template v-slot:[`item.data-table-select`]="{ isSelected, select, item }">
              <v-simple-checkbox :ripple="false" :value="isSelected" @input="select($event)" v-if="!MobileSize" color="#27AB9C"></v-simple-checkbox>
              <v-card elevation="0" class="d-flex pb-0" v-if="MobileSize" style="max-width: 100%; border-top: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 8px 8px 0px 0px;">
                <v-card-text class="d-flex pb-0">
                  <v-simple-checkbox :ripple="false" :value="isSelected" @input="select($event)" class="pt-1 mr-auto" color="#27AB9C"></v-simple-checkbox>
                  <v-spacer></v-spacer>
                  <div class="d-flex align-center">
                    <span class="mr-3">เปิด-ปิด</span>
                    <v-switch :loading="isLoading[item.product_id]" :disabled="isLoading[item.product_id]" v-model="item.product_status" @change="actionChange(item.product_id)" inset true-value="active" false-value="inactive"></v-switch>
                  </div>
                </v-card-text>
              </v-card>
            </template>
            <template v-slot:[`item.image`]="{ item }" v-if="!MobileSize">
              <v-row justify="center" class="my-4 mx-4">
                <v-img max-height="93" max-width="48" width="93" height="48" src="@/assets/ImageINET-Marketplace/ICONShop/NoImageProduct.png" v-if="item.product_image.length === 0"></v-img>
                <v-img max-height="89" max-width="48" width="100%" height="100%" :src="`${item.product_image}`" v-else></v-img>
              </v-row>
            </template>
            <template v-slot:[`item.change_status`]="{ item }" v-if="!MobileSize">
              <v-switch :loading="isLoading[item.product_id]" :disabled="isLoading[item.product_id]" v-model="item.product_status" @change="actionChange(item.product_id)" inset true-value="active" false-value="inactive"></v-switch>
            </template>
            <template v-slot:[`item.productPrice`]="{ item }" v-if="!MobileSize">
              <span>{{ item.product_price_range === item.product_price ? Number(item.product_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : item.product_price_range }}</span>
            </template>
            <template v-slot:[`item.product_name`]="{ item }">
              <div v-if="!MobileSize" style="width: 200px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; color: #333333;">
                <span>{{ item.product_name }}</span>
              </div>
              <v-card v-else class="pa-3 mb-4" width="100%" elevation="0" style="border-bottom: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 0px 0px 8px 8px;">
                <v-row dense class="px-0 py-2" style="margin-top: -6vw;">
                  <v-col cols="4" align="center">
                    <v-img max-height="60" max-width="60" width="100%" height="100%" src="@/assets/ImageINET-Marketplace/ICONShop/NoImageProduct.png" v-if="item.product_image.length === 0"></v-img>
                    <v-img max-height="60" max-width="60" width="100%" height="100%" :src="`${item.product_image}`" contain v-else></v-img>
                  </v-col>
                  <v-col cols="8" align="start">
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-if="!IpadSize" v-bind="attrs" v-on="on"> {{ item.product_name | truncate(30, '...') }}</span>
                        <span v-else v-bind="attrs" v-on="on"> {{ item.product_name }}</span>
                      </template>
                      <span>{{ item.product_name }}</span>
                    </v-tooltip>
                    <br>
                    <!-- <span style="font-size: 14px; font-weight: 400; line-height: 19px; display: inline-block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%;">{{ item.product_name | truncate(40, '...') }}</span><br/> -->
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>รหัส SKU :</b> {{ item.sku }}</span><br/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;" v-if="JV === 'yes'"><b>Item code :</b> {{ item.item_code }}</span><br v-if="JV === 'yes'"/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>จำนวนสินค้าพร้อมขาย:</b> {{ Number(item.actual_stock).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span><br/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>สินค้าพร้อมขายพรีออเดอร์:</b> {{ Number(item.effective_stock).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span><br/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>ราคา:</b> {{ item.product_price_range === item.product_price ? Number(item.product_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : item.product_price_range }}</span><br/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>ประเภทสินค้า :</b> {{ item.product_type === 'service' ? 'สินค้าบริการ' : 'สินค้าทั่วไป' }}</span><br/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>สถานะสินค้า :</b> {{ MessageStatus(item.message_status) }}</span><br/>
                    <span style="font-size: small; font-weight: 400; line-height: 14px;"><b>สถานะการขาย : </b>
                      <v-chip small color="#FBE5E4" :class="!MobileSize ? 'ma-2' : 'my-2'" text-color="#D1392B" v-if="item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service'">สินค้าหมด</v-chip>
                      <v-chip small color="#27AB9C" :class="!MobileSize ? 'ma-2' : 'my-2'" text-color="#D8EFE4" v-else-if="item.actual_stock === 0 && item.effective_stock !== 0 && item.product_status === 'active' && item.product_type !== 'service'">สินค้าพรีออเดอร์</v-chip>
                      <v-chip small color="#FCF0DA" :class="!MobileSize ? 'ma-2' : 'my-2'" text-color="#E9A016" v-else-if="item.product_status === 'inactive'">ไม่พร้อมขาย</v-chip>
                      <v-chip small color="#ECF8EA" :class="!MobileSize ? 'ma-2' : 'my-2'" text-color="#1AB759" v-else>พร้อมขาย</v-chip>
                    </span>
                  </v-col>
                  <!-- <v-col cols="4" class="mt-1">
                    <v-row dense justify="center" class="px-0">
                      <v-col cols="12" md="6" sm="6">
                        <v-row dense class="px-0">
                          <v-btn
                            outlined
                            icon
                            style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                            height="20"
                            width="20"
                            @mousedown.left="Edit(item)"
                            @mousedown.right="EditRigthClick(item)"
                            :to="pathEditProduct"
                            :class="[IpadProSize ? 'mt-0' : IpadSize ? '' : '']"
                          >
                            <v-icon color="#27AB9C" size="12">mdi-pencil</v-icon>
                          </v-btn>
                          <span @mousedown.left="Edit(item)" @mousedown.right="EditRigthClick(item)" class="pl-1" style="text-decoration: underline; font-weight: 400; font-size: 12px; color: #27AB9C; cursor: pointer;">แก้ไข</span>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="6" sm="6" class="px-0">
                        <v-row dense class="px-0">
                          <v-btn
                            icon
                            outlined
                            height="20"
                            width="20"
                            style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                            @click="ConfirmDeleteProduct(item)"
                            :class="[IpadProSize ? 'mt-0' : IpadSize ? 'ml-0' : 'ml-1 mt-2']"
                          >
                            <v-icon color="#A1A1A1" size="12">mdi-delete-outline</v-icon>
                          </v-btn>
                          <span class="pl-1 pt-2" @click="ConfirmDeleteProduct(item)" style="text-decoration: underline; font-weight: 400; font-size: 12px; color: #9A9A9A; cursor: pointer;">ลบ</span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col> -->
                </v-row>
                <v-row style="margin-top: -3vw;">
                  <v-col cols="6" style="display: grid;">
                    <v-btn
                      color="#27AB9C"
                      @mousedown.left="Edit(item)"
                      @mousedown.right="EditRigthClick(item)"
                      :to="pathEditProduct"
                      style="border-radius: 5vw;"
                    >
                      <v-icon color="#fff" size="12">mdi-pencil</v-icon>
                      <span style="color: #fff;"> แก้ไข</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="6" style="display: grid;">
                    <v-btn
                      color="#27AB9C"
                      @click="ConfirmDeleteProduct(item)"
                      outlined
                      style="border-radius: 5vw;"
                    >
                      <v-icon color="#27AB9C" size="12">mdi-delete-outline</v-icon>
                      <span style="color: #27AB9C;"> ลบ</span>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card>
            </template>
            <template v-slot:[`item.product_type`]="{ item }" v-if="!MobileSize">
              <span>{{ item.product_type === 'service' ? 'สินค้าบริการ' : 'สินค้าทั่วไป' }}</span>
            </template>
            <template v-slot:[`item.actual_stock`]="{ item }" v-if="!MobileSize">
              <span>{{ Number(item.actual_stock).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
            </template>
            <template v-slot:[`item.effective_stock`]="{ item }" v-if="!MobileSize">
              <span>{{ Number(item.effective_stock).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
            </template>
            <template v-slot:[`item.message_status`]="{ item }" v-if="!MobileSize">
              <span>{{ MessageStatus(item.message_status) }}</span>
            </template>
            <template v-slot:[`item.status`]="{ item }" v-if="!MobileSize">
              <v-chip color="#FBE5E4" :class="!MobileSize ? 'ma-2' : 'mb-2'" text-color="#D1392B" v-if="item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service'">สินค้าหมด</v-chip>
              <v-chip color="#27AB9C" :class="!MobileSize ? 'ma-2' : 'mb-2'" text-color="#D8EFE4" v-else-if="item.actual_stock === 0 && item.effective_stock !== 0 && item.product_status === 'active' && item.product_type !== 'service'">สินค้าพรีออเดอร์</v-chip>
              <v-chip color="#FCF0DA" text-color="#E9A016" v-else-if="item.product_status === 'inactive'">ไม่พร้อมขาย</v-chip>
              <v-chip color="#ECF8EA" :class="!MobileSize ? 'ma-2' : 'mb-2'" text-color="#1AB759" v-else>พร้อมขาย</v-chip>
            </template>
            <template v-slot:[`item.edit`]="{ item }" v-if="!MobileSize">
              <v-row dense justify="center" class="px-0">
                <v-col cols="6" md="6" sm="6">
                  <v-row dense class="px-0">
                    <v-btn
                      outlined
                      icon
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      height="24"
                      width="24"
                      @mousedown.left="Edit(item)"
                      @mousedown.right="EditRigthClick(item)"
                      :to="pathEditProduct"
                      :class="[IpadProSize ? 'mt-0' : IpadSize ? '' : '']"
                    >
                      <v-icon color="#27AB9C" size="18">mdi-pencil</v-icon>
                    </v-btn>
                    <span @mousedown.left="Edit(item)" v-if="!MobileSize" @mousedown.right="EditRigthClick(item)" class="pl-1" style="text-decoration: underline; font-weight: 400; font-size: 14px; color: #27AB9C; cursor: pointer;">แก้ไข</span>
                  </v-row>
                </v-col>
                <v-col cols="6" md="6" sm="6" class="px-0">
                  <v-row dense class="px-0">
                    <v-btn
                      icon
                      outlined
                      height="24"
                      width="24"
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      @click="ConfirmDeleteProduct(item)"
                      :class="[IpadProSize ? 'mt-0' : IpadSize ? 'ml-0' : 'ml-2']"
                    >
                      <v-icon color="#A1A1A1" size="18">mdi-delete-outline</v-icon>
                    </v-btn>
                    <span class="pl-1" v-if="!MobileSize" @click="ConfirmDeleteProduct(item)" style="text-decoration: underline; font-weight: 400; font-size: 14px; color: #9A9A9A; cursor: pointer;">ลบ</span>
                  </v-row>
                </v-col>
              </v-row>
            </template>
            <!-- <template v-slot:[`item.product_name`]="{ item }" v-if="MobileSize">
              <v-row class="px-0 py-2">
                <v-col cols="4" align="center">
                  <v-img max-height="60" max-width="60" width="100%" height="100%" src="@/assets/ImageINET-Marketplace/ICONShop/NoImageProduct.png" v-if="item.product_image.length === 0"></v-img>
                  <v-img max-height="60" max-width="60" width="100%" height="100%" :src="`${item.product_image}`" contain v-else></v-img>
                </v-col>
                <v-col cols="5" align="start">
                  <span style="font-size: 14px; font-weight: 400; line-height: 19px; display: inline-block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%;">{{ item.product_name }}</span><br/>
                  <span style="font-size: 12px; font-weight: 400; line-height: 14px;"><b>รหัส SKU :</b> {{ item.sku }}</span><br/>
                  <span style="font-size: 12px; font-weight: 400; line-height: 14px;" v-if="JV === 'yes'"><b>Item code :</b> {{ item.item_code }}</span><br v-if="JV === 'yes'"/>
                  <span style="font-size: 12px; font-weight: 400; line-height: 14px;"><b>ราคา:</b> {{ item.product_price_range === item.product_price ? Number(item.product_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : item.product_price_range }}</span><br/>
                  <span style="font-size: 12px; font-weight: 400; line-height: 14px;"><b>ประเภทสินค้า :</b> {{ item.product_type === 'service' ? 'สินค้าบริการ' : 'สินค้าทั่วไป' }}</span><br/>
                  <span style="font-size: 12px; font-weight: 400; line-height: 14px;"><b>สถานะสินค้า :</b> {{ MessageStatus(item.message_status) }}</span><br/>
                  <span style="font-size: 12px; font-weight: 400; line-height: 14px;"><b>สถานะการขาย :</b>
                    <v-chip small color="#FCF0DA" text-color="#E9A016" v-if="item.product_status === 'inactive'" class="mt-1">ไม่พร้อมขาย</v-chip>
                    <v-chip small color="#ECF8EA" :class="!MobileSize ? 'ma-2' : 'my-2'" text-color="#1AB759" v-else>พร้อมขาย</v-chip>
                  </span>
                </v-col>
                <v-col cols="4" class="mt-1">
                  <v-row dense justify="center" class="px-0">
                    <v-col cols="6" md="6" sm="6">
                      <v-row dense class="px-0">
                        <v-btn
                          outlined
                          icon
                          style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          height="20"
                          width="20"
                          @mousedown.left="Edit(item)"
                          @mousedown.right="EditRigthClick(item)"
                          :to="pathEditProduct"
                          :class="[IpadProSize ? 'mt-0' : IpadSize ? '' : '']"
                        >
                          <v-icon color="#27AB9C" size="12">mdi-pencil</v-icon>
                        </v-btn>
                        <span @mousedown.left="Edit(item)" @mousedown.right="EditRigthClick(item)" class="pl-1" style="text-decoration: underline; font-weight: 400; font-size: 12px; color: #27AB9C; cursor: pointer;">แก้ไข</span>
                      </v-row>
                    </v-col>
                    <v-col cols="6" md="6" sm="6" class="px-0">
                      <v-row dense class="px-0">
                        <v-btn
                          icon
                          outlined
                          height="20"
                          width="20"
                          style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          @click="ConfirmDeleteProduct(item)"
                          :class="[IpadProSize ? 'mt-0' : IpadSize ? 'ml-0' : 'ml-2']"
                        >
                          <v-icon color="#A1A1A1" size="12">mdi-delete-outline</v-icon>
                        </v-btn>
                        <span class="pl-1" @click="ConfirmDeleteProduct(item)" style="text-decoration: underline; font-weight: 400; font-size: 12px; color: #9A9A9A; cursor: pointer;">ลบ</span>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </template> -->
            <template v-slot:no-data>
              <v-row dense justify="center">
                <v-col cols="12" align="center" :class="MobileSize ? 'py-10 px-16' : 'py-10'">
                  <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/NodataProductShop.png')" max-height="177" max-width="200"></v-img>
                  <v-row dense justify="center" class="pt-6">
                    <v-col cols="12" align="center">
                      <span style="font-size: 18px; font-weight: 600; line-height: 140%; color: #636363;">ยังไม่มีข้อมูลสินค้าในร้านค้า</span><br/>
                      <span style="font-size: 16px; font-weight: 400; line-height: 140%; color: #9A9A9A;">กรุณา </span><span style="font-size: 18px; font-weight: 600; line-height: 140%; color: #1B5DD6; text-decoration: underline;">เพิ่มสินค้า</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <!-- Await Delete Product -->
    <v-dialog v-model="dialogAwaitDeleteProduct" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/DeleteProduct.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="cancelDelete()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบสินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการลบสินค้าจำนวน <b style="color: #333333;">{{ FormdeleteProducts === true ? Selected.length : '1' }}</b> ชิ้น</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelDelete()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="FormdeleteProducts === true ? confirmDeleteProducts () : DeleteProduct(itemToDelete)">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Change Status Product -->
    <v-dialog v-model="dialogAwaitChangeStatus" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="cancelChange()"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-container>
          <div class="d-flex justify-center">
            <v-avatar :size="MobileSize ? 90 : 250" tile><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets//ImageINET-Marketplace/ICONShop/changeStatus.png" alt=""></v-avatar>
          </div>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: large; line-height: 24px; color: #333333;" class="my-4"><b>เปลี่ยนสถานะสินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการเปลี่ยนสถานะสินค้า ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelChange()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="changeStatusSelect()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Select NGC or ZORT Sync Product -->
    <v-dialog v-model="dialogSelectSyncProduct" width="424" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%; position: relative; height: 60px; top: 0px;' : IpadSize ? 'width: 100%; position: relative; height: 60px;' : 'width: 424px; height: 120px;'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row :style="MobileSize ? 'height: 80px;' : IpadSize ? 'height: 80px' : 'height: 120px;'">
              <v-col style="text-align: center;" :class="MobileSize ? 'mt-2' : 'pt-4'">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>กรุณาเลือก</b></span>
              </v-col>
              <v-btn fab small @click="dialogSelectSyncProduct = !dialogSelectSyncProduct" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div v-if="!MobileSize && !IpadSize" style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '424px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;" :style="!MobileSize ? 'padding: 10px 0px 10px 30px;' : 'padding: 10px 15px 10px 15px'">
              <v-card-text :class="MobileSize ? 'pa-0' : 'pa-0 pr-8'">
                <v-radio-group
                  v-model="selectTypeSync"
                  column
                >
                  <v-radio
                    label="Sync จาก ZORT -> NGC"
                    value="zort"
                  ></v-radio>
                  <v-radio
                    :label="Selected.length === 0 ? 'Sync จาก NGC -> ZORT (*กรุณาเลือกสินค้า)' : 'Sync จาก NGC -> ZORT'"
                    :disabled="Selected.length === 0"
                    value="nexgen"
                  ></v-radio>
                </v-radio-group>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-text>
          <v-row dense justify="center">
            <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogSelectSyncProduct = !dialogSelectSyncProduct">ยกเลิก</v-btn>
            <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" :disabled="selectTypeSync === ''" @click="confirmSelect()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Await Sync Product -->
    <v-dialog v-model="dialogAwaitSyncProduct" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          max-height="200px"
          contain
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/erp_icon.webp')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="cancelSyncProduct()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ซิงค์สินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;" v-if="selectTypeSync === 'zort' && Selected.length === 0">คุณต้องการซิงค์สินค้าทั้งหมด</span>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;" v-else>คุณต้องการซิงค์สินค้าจำนวน <b style="color: #333333;">{{ FormSyncProducts === true ? Selected.length : '1' }}</b> ชิ้น</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;"><br/>คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelSyncProduct()">ยกเลิก</v-btn>
              <!-- <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="FormSyncProducts === true ? confirmSyncProducts() : SyncProduct(itemToDelete)">ตกลง</v-btn> -->
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmSyncProducts()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Update Product -->
    <v-dialog v-model="dialogAwaitUpdateProduct" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          max-height="200px"
          contain
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/erp_icon.webp')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="cancelUpdateProduct()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>อัพเดทสต๊อกสินค้า ERP</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;" v-if="selectTypeSync === 'zort' && Selected.length === 0">คุณต้องการอัพเดทสต๊อกสินค้าทั้งหมด</span>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;" v-else>คุณต้องการอัพเดทสต๊อกสินค้าจำนวน <b style="color: #333333;">{{ FormSyncProducts === true ? Selected.length : '1' }}</b> ชิ้น</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelUpdateProduct()">ยกเลิก</v-btn>
              <!-- <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="FormSyncProducts === true ? confirmUpdateSyncProducts() : SyncProduct(itemToDelete)">ตกลง</v-btn> -->
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmUpdateSyncProducts()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Delete Product -->
    <v-dialog v-model="dialogSuccessDeleteProduct" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModal()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบสินค้าเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบสินค้าเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModal()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Sync Product -->
    <v-dialog v-model="dialogSuccessSyncProduct" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModal()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบสินค้าเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบสินค้าเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModal()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Result ERP -->
    <v-dialog v-model="DialogSyncERPResult" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '900'" content-class="elevation-0">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%; position: absolute; height: 80px;' : IpadSize ? 'width: 100%; position: absolute; height: 120px;' : 'width: 900px; height: 120px;'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row :style="MobileSize ? 'height: 80px;' : 'height: 120px;'">
              <v-col style="text-align: center;" :class="MobileSize ? 'mt-2' : 'pt-4'">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ดำเนินการสำเร็จ</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogSyncERP()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : IpadSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;" :style="!MobileSize ? 'padding: 10px 0px 10px 30px;' : 'padding: 10px 15px 10px 15px'">
              <v-card-text :class="MobileSize ? 'pa-0' : 'pa-0 pr-8'">
                <v-row :class="MobileSize ? 'mt-5' :'mt-1'">
                  <v-col cols="12" md="6" sm="8">
                    <v-text-field v-model="searchProduct" dense hide-details outlined placeholder="ค้นหา" style="border-radius: 8px;">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense no-gutters class="mb-2">
                  <v-col cols="12" class="py-0 mb-0">
                    <a-tabs v-model="activeTab" :show-arrows="IpadSize || IpadProSize">
                      <a-tab-pane :key="0">
                        <span slot="tab">ดำเนินการสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{ ProductSyncSuccessCount }}</a-tag></span>
                      </a-tab-pane>
                      <a-tab-pane :key="1">
                        <span slot="tab">ดำเนินการไม่สำเร็จ <a-tag color="#D1392B" style="border-radius: 8px;">{{ ProductSyncFailCount }}</a-tag></span>
                      </a-tab-pane>
                    </a-tabs>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" v-if="activeTab === 0">
                    <v-row>
                      <v-col>
                        <v-card>
                          <v-data-table
                          :headers="headersSuccessSyncERP"
                          :items="ProductSyncSuccess"
                          :items-per-page="5"
                          :search="searchProduct"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        ></v-data-table>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" v-if="activeTab === 1">
                    <v-row>
                      <v-col>
                        <v-card>
                          <v-data-table
                          :headers="headersFailSyncERP"
                          :items="ProductSyncFail"
                          :items-per-page="5"
                          :search="searchProduct"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        >
                          <template v-slot:[`item.message`]="{ item }">
                            <span v-if="item.message === 'Duplicated Product Sku.'">สินค้าชิ้นนี้ถูกซิงค์เรียบร้อยแล้ว</span>
                            <span v-else>{{item.message}}</span>
                          </template>
                        </v-data-table>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
              <DetailUserModal ref="DetailUserModal" />
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import axios from 'axios'
import { Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    DetailUserModal: () => import('@/components/Business/dialogDetailUser.vue')
  },
  props: ['props', 'ShopID', 'ShopName', 'JV', 'openSyncERP'],
  data () {
    return {
      FormdeleteProducts: false,
      FormSyncProducts: false,
      Selected: [],
      allSelected: false,
      checkboxIndeterminate: false,
      selectKey: [],
      tab: 0,
      itemToDelete: [],
      dialogAwaitDeleteProduct: false,
      dialogAwaitChangeStatus: false,
      dialogSuccessDeleteProduct: false,
      itemsTab: [
        { text: 'ทั้งหมด', value: 0 }
        // { text: 'พร้อมขาย', value: 0 },
        // { text: 'ไม่พร้อมขาย', value: 0 }
      ],
      selectType: 'all',
      selectStatus: 'all',
      selectStatusProduct: 'all',
      itemsSelect: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'สินค้าทั่วไป', value: 'general' },
        { text: 'สินค้าบริการ', value: 'service' }
      ],
      itemsStatusProduct: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'พร้อมขาย', value: 'active' },
        { text: 'ไม่พร้อมขาย', value: 'inactive' },
        { text: 'สินค้าหมด', value: 'outofstock' }
      ],
      itemsStatus: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'สินค้ามาใหม่', value: 'new' },
        { text: 'สินค้าขายดี', value: 'best-seller' },
        { text: 'สินค้าแนะนำ', value: 'recommend' },
        { text: 'สินค้าทั่วไป', value: 'general' },
        { text: 'สินค้าลดราคา', value: 'sale' },
        { text: 'สินค้าพรีออเดอร์', value: 'pre-order' }
        // { text: 'สินค้าช้อป ดี มี คืน', value: 'shop_dee_me_kuen' }
      ],
      showCountOrder: 0,
      excelFile: '',
      pageCount: 5,
      onedata: [],
      page: 1,
      itemsPerPage: 5,
      search: '',
      pathEditProduct: '',
      headers: [
        // { text: '', value: 'checkbox', filterable: false, sortable: false, width: '30', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รูปภาพสินค้า', value: 'image', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, align: 'left', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'left', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'เปิด-ปิด', filterable: false, value: 'change_status', sortable: false, align: 'left', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนสินค้าพร้อมขาย', value: 'actual_stock', sortable: false, align: 'left', width: '170', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนสินค้าพร้อมขายและพรีออเดอร์', value: 'effective_stock', sortable: false, align: 'left', width: '240', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'productPrice', filterable: false, sortable: false, align: 'left', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ประเภทสินค้า', value: 'product_type', filterable: false, sortable: false, align: 'left', width: '120', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะสินค้า', filterable: false, value: 'message_status', sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการขาย', value: 'status', filterable: false, sortable: false, align: 'left', width: '120', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการสินค้า', filterable: false, value: 'edit', sortable: false, align: 'left', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersJV: [
        // { text: '', value: 'checkbox', filterable: false, sortable: false, width: '30', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รูปภาพสินค้า', value: 'image', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, align: 'left', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'เปิด-ปิด', filterable: false, value: 'change_status', sortable: false, align: 'left', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'item code', value: 'item_code', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนสินค้าพร้อมขาย', value: 'actual_stock', sortable: false, align: 'left', width: '170', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนสินค้าพร้อมขายและพรีออเดอร์', value: 'effective_stock', sortable: false, align: 'left', width: '240', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'productPrice', filterable: false, sortable: false, align: 'left', width: '150', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ประเภทสินค้า', value: 'product_type', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะสินค้า', filterable: false, value: 'message_status', sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการขาย', value: 'status', filterable: false, sortable: false, align: 'left', width: '120', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการสินค้า', filterable: false, value: 'edit', sortable: false, width: '200', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersMobile: [
        { text: 'ชื่อสินค้า', value: 'product_name', filterable: true, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' }
      ],
      dialogAwaitSyncProduct: false,
      dialogSuccessSyncProduct: false,
      DialogSyncERPResult: false,
      ProductSyncSuccess: [],
      ProductSyncSuccessCount: 0,
      ProductSyncFail: [],
      ProductSyncFailCount: 0,
      headersSuccessSyncERP: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersFailSyncERP: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'หมายเหตุ', value: 'message', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      activeTab: 0,
      searchProduct: '',
      dialogAwaitUpdateProduct: false,
      isLoading: {},
      dialogSelectSyncProduct: false,
      selectTypeSync: '',
      typeForSync: ''
    }
  },
  async created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    await this.getCountInTable()
  },
  mounted () {
    this.$EventBus.$on('getCountInTable', this.getCountInTable)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getCountInTable')
    })
  },
  computed: {
    filterItemTable () {
      var productall = Array.isArray(this.props.list_product) ? this.props.list_product : []
      if (this.selectType === 'all' && this.selectStatus === 'all' && this.selectStatusProduct === 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            return item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase())
          })
        } else {
          return productall
        }
      } else if (this.selectType !== 'all' && this.selectStatus === 'all' && this.selectStatusProduct === 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            return item.product_type === this.selectType && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
          })
        } else {
          return productall.filter(item => {
            return item.product_type === this.selectType
          })
        }
      } else if (this.selectType === 'all' && this.selectStatus !== 'all' && this.selectStatusProduct === 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            return item.message_status === this.selectStatus && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
          })
        } else {
          return productall.filter(item => {
            return item.message_status === this.selectStatus
          })
        }
      } else if (this.selectType === 'all' && this.selectStatus === 'all' && this.selectStatusProduct !== 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.product_status === this.selectStatusProduct && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            } else if (this.selectStatusProduct === 'outofstock') {
              return item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service' && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            }
          })
        } else {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.product_status === this.selectStatusProduct
            } else {
              return item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service'
            }
          })
        }
      } else if (this.selectType !== 'all' && this.selectStatus !== 'all' && this.selectStatusProduct === 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            return item.message_status === this.selectStatus && item.product_type === this.selectType && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
          })
        } else {
          return productall.filter(item => {
            return item.message_status === this.selectStatus && item.product_type === this.selectType
          })
        }
      } else if (this.selectType !== 'all' && this.selectStatus === 'all' && this.selectStatusProduct !== 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.product_type === this.selectType && item.product_status === this.selectStatusProduct && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            } else {
              return item.product_type === this.selectType && item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service' && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            }
          })
        } else {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.product_type === this.selectType && item.product_status === this.selectStatusProduct
            } else {
              return item.product_type === this.selectType && item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service'
            }
          })
        }
      } else if (this.selectType === 'all' && this.selectStatus !== 'all' && this.selectStatusProduct !== 'all') {
        if (this.search !== '') {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.product_status === this.selectStatusProduct && item.message_status === this.selectStatus && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            } else if (this.selectStatusProduct === 'outofstock') {
              return item.message_status === this.selectStatus && item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service' && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            }
          })
        } else {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.product_status === this.selectStatusProduct && item.message_status === this.selectStatus
            } else {
              return item.message_status === this.selectStatus && item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service'
            }
          })
        }
      } else {
        if (this.search !== '') {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.message_status === this.selectStatus && item.product_type === this.selectType && item.product_status === this.selectStatusProduct && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            } else {
              return item.message_status === this.selectStatus && item.product_type === this.selectType && item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service' && (item.product_name.toLowerCase().includes(this.search.toLowerCase()) || item.sku.toLowerCase().includes(this.search.toLowerCase()))
            }
          })
        } else {
          return productall.filter(item => {
            if (this.selectStatusProduct !== 'outofstock') {
              return item.message_status === this.selectStatus && item.product_type === this.selectType && item.product_status === this.selectStatusProduct
            } else {
              return item.message_status === this.selectStatus && item.product_type === this.selectType && item.actual_stock === 0 && item.effective_stock === 0 && item.product_status === 'active' && item.product_type !== 'service'
            }
          })
        }
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  methods: {
    async closeDialogSyncERP () {
      this.DialogSyncERPResult = !this.DialogSyncERPResult
      await this.$EventBus.$emit('CheckShop')
    },
    selectAllToggle () {
      if (this.Selected.length !== this.DataTable.list_product.length && this.allSelected) {
        this.Selected = []
        const self = this
        if (this.search !== '' || this.selectType !== 'all' || this.selectStatus !== 'all' || this.selectStatusProduct !== 'all') {
          this.filterItemTable.forEach(item => {
            self.Selected.push(item)
          })
        } else {
          this.DataTable.list_product.forEach(item => {
            self.Selected.push(item)
          })
        }
        if (this.Selected.length > 0 && (this.DataTable.list_product.length === this.Selected.length)) {
          this.allSelected = true
          this.checkboxIndeterminate = false
        } else if (this.Selected.length > 0) {
          this.checkboxIndeterminate = true
        }
      } else {
        this.Selected = []
        this.allSelected = false
        this.checkboxIndeterminate = false
      }
    },
    deleteProducts () {
      this.FormdeleteProducts = false
      this.FormdeleteProducts = true
      this.dialogAwaitDeleteProduct = true
    },
    openSyncProducts (type) {
      this.typeForSync = type
      this.selectTypeSync = ''
      this.dialogSelectSyncProduct = true
      // this.FormSyncProducts = true
      // this.dialogAwaitSyncProduct = true
    },
    confirmSelect () {
      this.dialogSelectSyncProduct = false
      if (this.typeForSync === 'sync') {
        this.FormSyncProducts = true
        this.dialogAwaitSyncProduct = true
      } else {
        this.FormSyncProducts = true
        this.dialogAwaitUpdateProduct = true
      }
    },
    updateSyncProducts (type) {
      this.typeForSync = type
      this.selectTypeSync = ''
      this.dialogSelectSyncProduct = true
      // this.FormSyncProducts = true
      // this.dialogAwaitUpdateProduct = true
    },
    changeStatus () {
      this.dialogAwaitChangeStatus = true
    },
    checkAll (value) {
      // console.log('value', value)
      this.allSelected = value
      if (value) {
        this.selectKey = this.filterItemTable.map(item => item.product_id)
        // console.log('this.selectKey', this.selectKey)
      } else {
        this.selectKey = []
      }
    },
    // FilterTypeProduct (type) {
    //   if (type !== 'all') {
    //     this.filterItemTable = this.props.list_product.filter(item => {
    //       return item.product_type === type
    //     })
    //   } else {
    //     this.filterItemTable = this.props.list_product
    //   }
    // },
    MessageStatus (val) {
      if (val === 'new') {
        return 'สินค้ามาใหม่'
      } else if (val === 'sale') {
        return 'สินค้าลดราคา'
      } else if (val === 'general') {
        return 'สินค้าทั่วไป'
      } else if (val === 'recommend') {
        return 'สินค้าแนะนำ'
      } else if (val === 'best-seller') {
        return 'สินค้าขายดี'
      } else if (val === 'pre-order') {
        return 'สินค้าพรีออเดอร์'
      } else {
        return 'ไม่มีสถานะ'
      }
      // else if (val === 'e-receipt') {
      //   return 'Easy E-Receipt'
      // }
    },
    async getCountInTable () {
      this.FormdeleteProducts = false
      this.allSelected = false
      this.selectKey = []
      this.Selected = []
      // this.filterItemTable = this.props.list_product
      this.itemsTab = [
        { text: 'ทั้งหมด', value: 0 }
        // { text: 'พร้อมขาย', value: 0 },
        // { text: 'ไม่พร้อมขาย', value: 0 }
      ]
      var data = {
        seller_shop_id: parseInt(this.ShopID)
      }
      const auth = {
        headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
      }
      // await this.$store.dispatch('GetProductBySellerID', data)
      var dataResponse = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/product/list_product`, data, auth)
      // console.log('dataResponse ====>', dataResponse)
      if (dataResponse.data.result === 'SUCCESS') {
        this.DataTable = await dataResponse.data.data
        for (let i = 0; i < this.itemsTab.length; i++) {
          if (i === 0) {
            this.itemsTab[i].value = this.DataTable.total_products_count
          }
        }
      } else {
        if (dataResponse.data.message === 'This user is not in your shop') {
          window.location.assign('/')
        } else {
          window.location.assign('/')
        }
      }
    },
    ImportExcel () {
      document.getElementById('importExcel').click()
    },
    async DownloadExcel () {
      await axios({
        // url: 'http://localhost:3000/paperlessToUPS/downloadFilexlsx',
        url: `${process.env.VUE_APP_BACK_END}/api/product/export_product_template`,
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'import_product_template.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
        // console.log('fileLink ')
      })
    },
    async UploadExcel (e) {
      this.$store.commit('openLoader')
      var files = e.target.files
      var f = files[0]
      // console.log(f)
      var data = new FormData()
      data.append('seller_shop_id', this.ShopID)
      data.append('file_products', f)
      await this.$store.dispatch('actionImportExcel', data)
      var response = await this.$store.state.ModuleShop.stateImportExcel
      if (response.code === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'นำเข้าสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.$EventBus.$emit('CheckShop')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${response.message}`, showConfirmButton: false, timer: 3500 })
      }
    },
    countProduct (pagination) {
      window.scrollTo(0, 0)
      this.showCountOrder = pagination.itemsLength
      for (let i = 0; i < this.itemsTab.length; i++) {
        if (i === 0) {
          this.itemsTab[i].value = pagination.itemsLength
        }
      }
    },
    getItemPerPage (val) {
      // console.log(val)
      this.itemsPerPage = val
    },
    CreateProduct () {
      if (this.MobileSize === true) {
        this.$router.push({ path: `/manageproductMobile?Status=Create&ShopID=${this.ShopID}` })
      } else {
        this.$router.push({ path: `/manageproduct?Status=Create&ShopID=${this.ShopID}` })
      }
    },
    Edit (val) {
      // this.$store.commit('SetEditProduct', val)
      if (this.MobileSize === true) {
        this.$router.push({ path: `/manageproductMobile?Status=Edit&ShopID=${this.ShopID}&ProductID=${val.product_id}` })
      } else {
        this.$router.push({ path: `/manageproduct?Status=Edit&ShopID=${this.ShopID}&ProductID=${val.product_id}` })
      }
      // this.$router.push({ path: `/manageproduct?Status=Edit&ShopID=${this.ShopID}` })
    },
    async EditRigthClick (val) {
      // console.log(val)
      // var data = {
      //   product_id: val.product_id
      // }
      // await this.$store.dispatch('actionUPSGetProductDetail', data)
      // var responseProductDetail = await this.$store.state.UPSModuleShop.stateUPSGetProductDetail.data
      // console.log(responseProductDetail)
      // await this.$store.commit('mutationsUPSEditProduct', responseProductDetail)
      localStorage.setItem('checkClick', 'right')
      // localStorage.setItem('EditProductDetail', Encode.encode(responseProductDetail))
      this.pathEditProduct = ''
      if (this.MobileSize === true) {
        this.pathEditProduct = '/manageproductMobile?Status=Edit&ShopID=' + this.ShopID + '&ProductID=' + val.product_id
      } else {
        this.pathEditProduct = '/manageproduct?Status=Edit&ShopID=' + this.ShopID + '&ProductID=' + val.product_id
      }
    },
    ConfirmDeleteProduct (val) {
      this.itemToDelete = []
      this.itemToDelete = val
      this.dialogAwaitDeleteProduct = true
      // this.$swal.fire({
      //   icon: 'warning',
      //   title: '<h5>คุณต้องการที่จะลบสินค้านี้หรือไม่?</h5>',
      //   showCancelButton: true,
      //   confirmButtonText: 'ยืนยัน',
      //   cancelButtonText: 'ยกเลิก',
      //   confirmButtonColor: '#27AB9C',
      //   cancelButtonColor: '#FF3F00',
      //   reverseButtons: true
      // }).then((result) => {
      //   if (result.isConfirmed) {
      //     this.DeleteProduct(val)
      //   } else if (result.isDismissed) {
      //   }
      // }).catch(() => {
      // })
    },
    async DeleteProduct (val) {
      // console.log('====>')
      this.dialogAwaitDeleteProduct = false
      this.$store.commit('openLoader')
      var data = {
        product_id: val.product_id
      }
      await this.$store.dispatch('actionsDeleteProduct', data)
      var responseDeleteProduct = await this.$store.state.ModuleShop.stateDeleteProduct
      // console.log(responseDeleteProduct)
      if (responseDeleteProduct.result === 'SUCCESS') {
        // this.$swal.fire({ icon: 'success', text: 'ลบสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        // this.search = ''
        this.$store.commit('closeLoader')
        this.dialogSuccessDeleteProduct = true
        // console.log('allSelected')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 1500 })
      }
    },
    async SyncProduct (val) {
      // console.log('====>')
      this.dialogAwaitSyncProduct = false
      this.$store.commit('openLoader')
      var data = {
        product_list: val.product_list
      }
      await this.$store.dispatch('actionsDeleteProduct', data)
      var responseDeleteProduct = await this.$store.state.ModuleShop.stateDeleteProduct
      // console.log(responseDeleteProduct)
      if (responseDeleteProduct.result === 'SUCCESS') {
        // this.$swal.fire({ icon: 'success', text: 'ลบสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        // this.search = ''
        this.$store.commit('closeLoader')
        // this.dialogSuccessDeleteProduct = true
        // console.log('allSelected')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 1500 })
      }
    },
    cancelDelete () {
      this.dialogAwaitDeleteProduct = false
      this.FormdeleteProducts = false
      // console.log('FormdeleteProducts', this.FormdeleteProducts)
    },
    cancelChange () {
      this.dialogAwaitChangeStatus = false
    },
    cancelSyncProduct () {
      this.dialogAwaitSyncProduct = false
      this.FormdeleteProducts = false
      // console.log('FormdeleteProducts', this.FormdeleteProducts)
    },
    cancelUpdateProduct () {
      this.dialogAwaitUpdateProduct = false
      this.FormdeleteProducts = false
    },
    changeStatusSelect () {
      this.$EventBus.$emit('CheckShop')
      this.changeStatusProduct()
    },
    async actionChange (id) {
      this.$set(this.isLoading, id, true)
      await this.changeStatusProduct(id)
      this.$set(this.isLoading, id, false)
    },
    async changeStatusProduct (id) {
      var productIdToChange = []
      if (this.Selected && this.Selected.length !== 0) {
        this.Selected.forEach(item => {
          productIdToChange.push(item.product_id)
        })
      } else {
        productIdToChange.push(id)
      }
      var data = {
        seller_shop_id: parseInt(this.ShopID),
        product_id: productIdToChange
      }
      await this.$store.dispatch('actionChangeStatusProduct', data)
      var respons = await this.$store.state.ModuleShop.stateChangeStatusProduct
      if (respons.code === 200) {
        this.dialogAwaitChangeStatus = false
      } else {
        // this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 1500 })
        this.dialogAwaitChangeStatus = false
      }
    },
    async confirmDeleteProducts () {
      this.dialogAwaitDeleteProduct = false
      this.$store.commit('openLoader')
      var productIdToDelete = []
      this.Selected.forEach(item => {
        productIdToDelete.push(item.product_id)
      })
      // console.log(productIdToDelete)
      var data = {
        seller_shop_id: parseInt(this.ShopID),
        product_id: productIdToDelete
      }
      // console.log('dataq11', data)
      await this.$store.dispatch('actionsDeleteBySeller', data)
      var responseDeleteProduct = await this.$store.state.ModuleShop.stateDeleteBySeller
      // console.log('responseDeleteProduct', responseDeleteProduct)
      // var responseDeleteProduct = {
      //   message: 'Products deleted successfully'
      // }
      if (responseDeleteProduct.message === 'Products deleted successfully') {
        // this.$swal.fire({ icon: 'success', text: 'ลบสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        // this.search = ''
        this.$store.commit('closeLoader')
        this.dialogSuccessDeleteProduct = true
        // console.log('allSelected')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 1500 })
      }
    },
    async confirmSyncProducts () {
      this.dialogAwaitSyncProduct = false
      this.$store.commit('openLoader')
      var productIdToSync = []
      this.Selected.forEach(item => {
        productIdToSync.push(item.product_id)
      })
      var data = {
        shop_id: parseInt(this.$route.query.ShopID),
        product_list: productIdToSync,
        from: this.selectTypeSync
      }
      await this.$store.dispatch('actionsAddProductERP', data)
      var response = await this.$store.state.ModuleShop.stateAddProductERP
      if (response.message === 'Success') {
        this.$store.commit('closeLoader')
        this.ProductSyncSuccessCount = response.data.success
        this.ProductSyncSuccess = response.data.success_list
        this.ProductSyncFailCount = response.data.fail
        this.ProductSyncFail = response.data.fail_list
        this.DialogSyncERPResult = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 1500 })
      }
    },
    async confirmUpdateSyncProducts () {
      this.dialogAwaitUpdateProduct = false
      this.$store.commit('openLoader')
      var productIdToSync = []
      this.Selected.forEach(item => {
        productIdToSync.push(item.product_id)
      })
      var data = {
        shop_id: parseInt(this.$route.query.ShopID),
        product_list: productIdToSync,
        from: this.selectTypeSync
      }
      await this.$store.dispatch('actionsUpdateStockErp', data)
      var response = await this.$store.state.ModuleShop.stateUpdateStockErp
      if (response.message === 'Success') {
        this.$store.commit('closeLoader')
        this.ProductSyncSuccessCount = response.data.success
        this.ProductSyncSuccess = response.data.success_list
        this.ProductSyncFailCount = response.data.fail
        this.ProductSyncFail = response.data.fail_list
        this.DialogSyncERPResult = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 1500 })
      }
    },
    async exportExcelProducts () {
      var productIdToExport = []
      this.Selected.forEach(item => {
        productIdToExport.push(item.product_id)
      })
      // console.log('productIdToExport', productIdToExport)
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        seller_shop_id: parseInt(this.ShopID),
        list_product_id: productIdToExport
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}exports/product/excel`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          responseType: 'blob',
          data: data
        }).then((response) => {
          // console.log('response.data', response.data)
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          const date = new Date().getDate().toString().padStart(2, '0') + '-' + (new Date().getMonth() + 1).toString().padStart(2, '0') + '-' + new Date().getFullYear()
          const time = new Date().getHours().toString().padStart(2, '0') + '' + new Date().getMinutes().toString().padStart(2, '0')
          fileLink.setAttribute('download', 'products' + '-' + this.$route.query.ShopName + '-' + date + '-' + time + '.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      } catch (error) {
        console.log('error', error)
        if (error.response && error.response.status === 400) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณาเลือกสินค้า</h3>'
          })
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
          })
        }
      }
    },
    closeModal () {
      // this.checkAll(false)
      this.dialogSuccessDeleteProduct = false
      this.$EventBus.$emit('CheckShop')
    },
    pageChange () {
      window.scrollTo(0, 0)
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  ::v-deep .elevation-1 tr th:first-of-type, td:first-of-type {
    background-color: #E6F5F3;
    border-style: none !important;
  }
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.fontSizeDetail {
  font-weight: 700 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.stickyTableHead {
  display: inline-block !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 9998 !important;
  width: 140px !important;
}
.stickyTableData {
  position: sticky !important;
  right: 0 !important;
  z-index: 9998 !important;;
}
@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.6rem;
    padding: 0 0 0 8px;
  }
}
@media only screen and (min-width: 768px) {
  /* For desktop: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    /* font-size: 14px; */
    padding: 0 0 0 8px;
  }
}
.v-data-table /deep/
.v-data-table__wrapper .v-data-table__mobile-row {
  border-bottom: 0px !important;
  padding-left: 4px;
  padding-right: 4px;
}
.v-data-table /deep/ .v-data-table__wrapper .v-data-table__mobile-row {
  height: initial;
  min-height: 48px;
  width: 100%;
  display: inline-block;
}
</style>
