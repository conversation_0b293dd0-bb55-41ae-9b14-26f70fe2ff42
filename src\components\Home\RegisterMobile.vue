<template>
  <!-- <div align="center">
   <v-container  style="margin:1px">
    <a-card :bordered="false" style="max-width: 350px; width: 350px; height:500px">
      <a-row type="flex" justify="center">
        <a-col :span="24">
          <a-row type="flex" justify="center">
            <h2>สมัครสมาชิก ONE ID</h2>
          </a-row>
        </a-col>
        <a-col :span="24">
          <a-form
            id="components-form-demo-normal-login"
            :form="FormRegister"
            class="login-form"
            @submit="Register"
          >
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'username',
                  { rules: [{ required: true, message: 'กรุณากรอกชื่อผู้ใช้งาน' }] },
                ]"
                placeholder="ชื่อผู้ใช้งาน*"
              >
                <a-icon slot="prefix" type="user" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-tooltip placement="topLeft" overlay-class-name="numeric-input">
                <template slot="title">
                  <ul>
                    <li>- ตัวพิมพ์เล็กอย่างน้อยหนึ่งตัว</li>
                    <li>- ตัวพิมพ์ใหญ่อย่างน้อยหนึ่งตัว</li>
                    <li>- ตัวเลขอย่างน้อยหนึ่งตัว</li>
                    <li>- ขั้นต่ำ 8 อักขระ</li>
                  </ul>
                </template>
                <a-input-password
                  size="large"
                  v-decorator="[
                    'password',
                    { rules: [{ required: true, message: 'กรุณากรอกรหัสผ่าน' }] },
                  ]"
                  placeholder="รหัสผ่าน*"
                >
                  <a-icon slot="prefix" type="lock" style="color: rgba(0,0,0,.25)" />
                </a-input-password>
              </a-tooltip>
            </a-form-item>
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'email',
                  { rules: [{ required: true, message: 'กรุณากรอกอีเมล' }] },
                ]"
                placeholder="อีเมล*"
              >
                <a-icon slot="prefix" type="mail" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-input
                size="large"
                v-decorator="[
                  'phone',
                  { rules: [{ required: true, message: 'กรุณากรอกเบอร์โทรศัพท์' }] },
                ]"
                placeholder="หมายเลขโทรศัพท์"
                :max-length="12"
                v-mask="'###-###-####'"
              >
                <a-icon slot="prefix" type="mobile" style="color: rgba(0,0,0,.25)" />
              </a-input>
            </a-form-item>
            <a-button size="large" html-type="submit" class="login-form-button" style="background-color: #27AB9C; color: white; border-color: #27AB9C;" :disabled="checkClick">
              สมัครสมาชิก
            </a-button>
          </a-form>
        </a-col>
      </a-row>
    </a-card>
   </v-container>
  </div> -->
  <v-container>
    <!-- website -->
    <v-container v-if="!MobileSize && !IpadSize">
      <v-row justify="center" class="my-7">
        <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text>
            <v-container>
              <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
              </v-row>
              <v-tabs
                v-model="tab"
                @change="handleTabChange"
                hide-slider centered grow class="tabsStyle" background-color="#F6F6F6" active-class="activeTabs" ref="tabs"
              >
                <v-tab
                  v-for="(item, index) in items"
                  :key="index"
                  class="mx-1"
                  ref="tab"
                >
                  {{ item.header }}
                </v-tab>
                <div class="glider" :style="gliderStyle"></div>
              </v-tabs>
              <v-tabs-items v-model="tab">
                <v-tab-item>
                  <!-- แบบกรอกข้อมูล -->
                  <v-form ref="Registerform" :lazy="lazy">
                    <v-row no-gutters dense class="mx-8 pt-4">
                      <!-- ชื่อผู้ใช้งาน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Username') }}</h3>
                        <v-text-field color="#269AFD" v-model="username" :placeholder="$t('register.EnterUsername')" outlined dense required @paste="onPaste" @keypress="checkCopyPaste($event)" :rules="Rules.name" oninput="this.value = this.value.replace(/[^A-Za-z0-9]/g, '').replace(/\s/g, '').toLowerCase()"></v-text-field>
                      </v-col>
                      <!-- รหัสผ่าน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Password') }}</h3>
                        <v-tooltip left>
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field color="#269AFD" v-model="password" :placeholder="$t('register.EnterPassword')" outlined dense required @keydown="noSpace" v-bind="attrs" v-on="on" :rules="Rules.password" :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'" :type="show1 ? 'text' : 'password'" @click:append="show1 = !show1"></v-text-field>
                          </template>
                          <ul>
                            <!-- <li>ตัวพิมพ์เล็ก(a-z)อย่างน้อยหนึ่งตัว</li>
                            <li>ตัวพิมพ์ใหญ่(A-Z)อย่างน้อยหนึ่งตัว</li>
                            <li>อักขระพิเศษอย่างน้อย 1 ตัว เช่น {{ dataText }}</li> -->
                            <li style="max-width: 200px;">{{ $t('register.TooltipPassword1') }}</li>
                            <li>{{ $t('register.TooltipPassword2') }}</li>
                          </ul>
                        </v-tooltip>
                      </v-col>
                      <!-- ยืนยันรหัสผ่าน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.ConfirmPassword') }}</h3>
                        <v-text-field color="#269AFD" v-model="confirmpassword" :placeholder="$t('register.EnterConfirmPassword')" outlined dense required @keydown="noSpace" :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'" :type="show2 ? 'text' : 'password'" @click:append="show2 = !show2" :rules="[v => confirmPasswordRules(v, password)]"></v-text-field>
                      </v-col>
                      <!-- อีเมล -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Email') }}</h3>
                        <v-text-field color="#269AFD" v-model="email" :placeholder="$t('register.EnterEmail')" outlined dense required @keydown="noSpace" :rules="Rules.email"></v-text-field>
                      </v-col>
                      <!-- เบอร์โทรศัพท์ -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Phone') }}</h3>
                        <v-text-field color="#269AFD" v-model="phone" :placeholder="$t('register.EnterPhone')" outlined dense required @keydown="noSpace" :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- Check Box ยอมรับ Consent -->
                      <v-col cols="12" md="12" sm="12" >
                        <input type="checkbox" v-model="ConCheck" style="" />
                        <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                      </v-col>
                      <!-- ปุ่มสมัครสมาชิก -->
                      <v-col cols="12" md="12" sm="12" class="mt-3">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Register()" :disabled="ConCheck === false" >{{ $t('register.Register') }}</v-btn>
                      </v-col>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-tab-item>
                <!-- แบบใช้เบอร์โทรศัพท์ -->
                <v-tab-item>
                  <v-form ref="RegisterformOTP" :lazy="lazy2" v-if="changeForm === false">
                    <v-row no-gutters dense class="mx-8 pt-4">
                      <!-- เบอร์โทรศัพท์ -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Phone') }}</h3>
                        <v-text-field color="#269AFD" v-model="phoneOTP" :placeholder="$t('register.EnterPhone')" outlined dense required @keydown="noSpace" :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- Check Box ยอมรับ Consent -->
                      <v-col cols="12" md="12" sm="12" >
                        <input type="checkbox" v-model="ConCheck" style="" />
                        <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                      </v-col>
                      <!-- ปุ่มรับ OTP -->
                      <v-col cols="12" md="12" sm="12" class="mt-3">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="RegisterOTP()" :disabled="ConCheck === false" >{{ $t('register.OTP') }}</v-btn>
                      </v-col>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                  <v-form ref="formOTPNumber" :lazy-validation="lazyOTPNumber" v-else>
                    <v-card elevation="0" width="100%" height="100%" class="mt-4">
                      <v-card-text :class="MobileSize ? 'px-2' : ''">
                        <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                          <img loading="lazy" src="@/assets/ForgotPasswordOTP.jpg" width="174px" height="110px" alt="ForgotPasswordOTP">
                        </v-row>
                        <v-row dense justify="center" align-content="center" class="mt-4 mb-4">
                          <span style="font-weight: 700; color: #333333; font-size: 16px;">{{ $t('register.EnterOTP') }}</span>
                        </v-row>
                        <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                          <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                        </v-row>
                        <v-row no-gutters dense :class="MobileSize ? 'mx-0' : 'mx-4'" justify="center">
                          <v-col cols="12" md="12" sm="12">
                            <v-otp-input
                              ref="otpInput"
                              color="#269AFD"
                              v-model="otp"
                              :error="showError"
                              :length="length"
                              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                            ></v-otp-input>
                          </v-col>
                          <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                            <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('register.SendOTPPhone') }} <span style="color: #269AFD;">{{maskPhoneNumber(phoneOTP)}}</span></span>
                          </v-row>
                          <!-- <v-col cols="12" md="12" sm="12" class="mb-4">
                            <span style="font-weight: normal; font-size: 16px; color: #000000;">รหัสอ้างอิง: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                          </v-col> -->
                          <v-col cols="12" md="12" sm="12">
                            <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn></span>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      <v-card-actions :class="MobileSize ? 'px-0 pt-0' : 'px-4 pt-0'">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="checkOTP()" :disabled="!isActive">{{ $t('register.SendOTPButton') }}</v-btn>
                      </v-card-actions>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-card>
                  </v-form>
                </v-tab-item>
              </v-tabs-items>
            </v-container>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
    <!-- Ipad -->
    <v-container v-if="!MobileSize && IpadSize">
      <v-row justify="center" class="my-7">
        <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text>
            <v-container>
              <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
              </v-row>
              <v-tabs
                v-model="tab"
                @change="handleTabChange"
                hide-slider centered grow class="tabsStyle" background-color="#F6F6F6" active-class="activeTabs" ref="tabs"
              >
                <v-tab
                  v-for="(item, index) in items"
                  :key="index"
                  class="mx-1"
                  ref="tab"
                >
                  {{ item.header }}
                </v-tab>
                <div class="glider" :style="gliderStyle"></div>
              </v-tabs>
              <v-tabs-items v-model="tab">
                <!-- แบบกรอกข้อมูล -->
                <v-tab-item>
                  <v-form ref="Registerform" :lazy="lazy">
                    <v-row no-gutters dense class="mx-8 pt-4">
                      <!-- ชื่อผู้ใช้งาน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Username') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="username" :placeholder="$t('register.EnterUsername')" outlined dense required @paste="onPaste" @keypress="checkCopyPaste($event)" :rules="Rules.name" oninput="this.value = this.value.replace(/[^A-Za-z0-9]/g, '').replace(/\s/g, '').toLowerCase()"></v-text-field>
                      </v-col>
                      <!-- รหัสผ่าน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Password') }}</h3>
                        <v-tooltip left>
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field color="#269AFD" @keydown="noSpace" v-model="password" :placeholder="$t('register.EnterPassword')" outlined dense required v-bind="attrs" v-on="on" :rules="Rules.password" :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'" :type="show1 ? 'text' : 'password'" @click:append="show1 = !show1"></v-text-field>
                          </template>
                          <ul>
                            <!-- <li>ตัวพิมพ์เล็ก(a-z)อย่างน้อยหนึ่งตัว</li>
                            <li>ตัวพิมพ์ใหญ่(A-Z)อย่างน้อยหนึ่งตัว</li>
                            <li>อักขระพิเศษอย่างน้อย 1 ตัว เช่น {{ dataText }}</li> -->
                            <li style="max-width: 200px;">{{ $t('register.TooltipPassword1') }}</li>
                            <li>{{ $t('register.TooltipPassword2') }}</li>
                          </ul>
                        </v-tooltip>
                      </v-col>
                      <!-- ยืนยันรหัสผ่าน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.ConfirmPassword') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="confirmpassword" :placeholder="$t('register.EnterConfirmPassword')" outlined dense required :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'" :type="show2 ? 'text' : 'password'" @click:append="show2 = !show2" :rules="[v => confirmPasswordRules(v, password)]"></v-text-field>
                      </v-col>
                      <!-- อีเมล -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Email') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="email" :placeholder="$t('register.EnterEmail')" outlined dense required  :rules="Rules.email"></v-text-field>
                      </v-col>
                      <!-- เบอร์โทรศัพท์ -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Phone') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="phone" :placeholder="$t('register.EnterPhone')" outlined dense required :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- Check Box ยอมรับ Consent -->
                      <v-col cols="12" md="12" sm="12" >
                        <input type="checkbox" v-model="ConCheck" style="" />
                        <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                      </v-col>
                      <!-- ปุ่มลงทะเบียน -->
                      <v-col cols="12" md="12" sm="12" class="mt-3">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Register()" :disabled="ConCheck === false" >{{ $t('register.Register') }}</v-btn>
                      </v-col>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-tab-item><!-- แบบใช้เบอร์โทรศัพท์ -->
                <v-tab-item>
                  <v-form ref="RegisterformOTP" :lazy="lazy2" v-if="changeForm === false">
                    <v-row no-gutters dense class="mx-8 pt-4">
                      <!-- เบอร์โทรศัพท์ -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Phone') }}</h3>
                        <v-text-field color="#269AFD" v-model="phoneOTP" :placeholder="$t('register.EnterPhone')" outlined dense required @keydown="noSpace" :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- Check Box ยอมรับ Consent -->
                      <v-col cols="12" md="12" sm="12" >
                        <input type="checkbox" v-model="ConCheck" style="" />
                        <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                      </v-col>
                      <!-- ปุ่มรับ OTP -->
                      <v-col cols="12" md="12" sm="12" class="mt-3">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="RegisterOTP()" :disabled="ConCheck === false" >{{ $t('register.OTP') }}</v-btn>
                      </v-col>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                  <v-form ref="formOTPNumber" :lazy-validation="lazyOTPNumber" v-else>
                    <v-card elevation="0" width="100%" height="100%" class="mt-4">
                      <v-card-text :class="MobileSize ? 'px-2' : ''">
                        <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                          <img loading="lazy" src="@/assets/ForgotPasswordOTP.jpg" width="174px" height="110px" alt="ForgotPasswordOTP">
                        </v-row>
                        <v-row dense justify="center" align-content="center" class="mt-4 mb-4">
                          <span style="font-weight: 700; color: #333333; font-size: 16px;">{{ $t('register.EnterOTP') }}</span>
                        </v-row>
                        <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                          <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                        </v-row>
                        <v-row no-gutters dense :class="MobileSize ? 'mx-0' : 'mx-4'" justify="center">
                          <v-col cols="12" md="12" sm="12">
                            <v-otp-input
                              ref="otpInput"
                              color="#269AFD"
                              v-model="otp"
                              :error="showError"
                              :length="length"
                              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                            ></v-otp-input>
                          </v-col>
                          <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                            <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('register.SendOTPPhone') }} <span style="color: #269AFD;">{{maskPhoneNumber(phoneOTP)}}</span></span>
                          </v-row>
                          <!-- <v-col cols="12" md="12" sm="12" class="mb-4">
                            <span style="font-weight: normal; font-size: 16px; color: #000000;">รหัสอ้างอิง: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                          </v-col> -->
                          <v-col cols="12" md="12" sm="12">
                            <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn></span>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      <v-card-actions :class="MobileSize ? 'px-0 pt-0' : 'px-4 pt-0'">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="checkOTP()" :disabled="!isActive">{{ $t('register.SendOTPButton') }}</v-btn>
                      </v-card-actions>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-card>
                  </v-form>
                </v-tab-item>
              </v-tabs-items>
            </v-container>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
    <!-- App -->
    <v-container v-if="MobileSize" class="px-0">
      <v-row justify="center" class="my-6">
        <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text>
            <v-container class="px-0">
              <v-row dense justify="center" align-content="center" class="mt-4 mb-8">
                <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
              </v-row>
              <v-tabs
                v-model="tab" @change="handleTabChange" hide-slider centered grow class="tabsStyle" background-color="#F6F6F6" active-class="activeTabs" ref="tabs">
                <v-tab
                  v-for="(item, index) in items"
                  :key="index"
                  ref="tab"
                >
                  {{ item.header }}
                </v-tab>
                <div class="glider" :style="gliderStyle"></div>
              </v-tabs>
              <v-tabs-items v-model="tab">
                <!-- แบบกรอกข้อมูล -->
                <v-tab-item>
                  <v-form ref="Registerform" :lazy="lazy">
                    <v-row no-gutters dense class="mx-8 pt-4">
                      <!-- ชื่อผู้ใช้งาน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Username') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="username" :placeholder="$t('register.EnterUsername')" outlined dense required @paste="onPaste" @keypress="checkCopyPaste($event)" :rules="Rules.name" oninput="this.value = this.value.replace(/[^A-Za-z0-9]/g, '').replace(/\s/g, '').toLowerCase()"></v-text-field>
                      </v-col>
                      <!-- รหัสผ่าน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Password') }}</h3>
                        <v-tooltip left>
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field color="#269AFD" @keydown="noSpace" v-model="password" :placeholder="$t('register.EnterPassword')" outlined dense required v-bind="attrs" v-on="on" :rules="Rules.password" :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'" :type="show1 ? 'text' : 'password'" @click:append="show1 = !show1"></v-text-field>
                          </template>
                          <ul>
                            <!-- <li>ตัวพิมพ์เล็ก(a-z)อย่างน้อยหนึ่งตัว</li>
                            <li>ตัวพิมพ์ใหญ่(A-Z)อย่างน้อยหนึ่งตัว</li>
                            <li>อักขระพิเศษอย่างน้อย 1 ตัว เช่น {{ dataText }}</li> -->
                            <li style="max-width: 200px;">{{ $t('register.TooltipPassword1') }}</li>
                            <li>{{ $t('register.TooltipPassword2') }}</li>
                          </ul>
                        </v-tooltip>
                      </v-col>
                      <!-- ยืนยันรหัสผ่าน -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.ConfirmPassword') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="confirmpassword" :placeholder="$t('register.EnterConfirmPassword')" outlined dense required :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'" :type="show2 ? 'text' : 'password'" @click:append="show2 = !show2" :rules="[v => confirmPasswordRules(v, password)]"></v-text-field>
                      </v-col>
                      <!-- อีเมล -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Email') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="email" :placeholder="$t('register.EnterEmail')" outlined dense required  :rules="Rules.email"></v-text-field>
                      </v-col>
                      <!-- เบอร์โทรศัพท์ -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Phone') }}</h3>
                        <v-text-field color="#269AFD" @keydown="noSpace" v-model="phone" :placeholder="$t('register.EnterPhone')" outlined dense required :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- Check Box ยอมรับ Consent -->
                      <v-col cols="12" md="12" sm="12" >
                        <input type="checkbox" v-model="ConCheck" style="" />
                        <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                      </v-col>
                      <!-- ปุ่มลงทะเบียน -->
                      <v-col cols="12" md="12" sm="12" class="mt-3">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Register()" :disabled="ConCheck === false" >{{ $t('register.Register') }}</v-btn>
                      </v-col>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-tab-item>
                <!-- แบบใช้เบอร์โทรศัพท์ -->
                <v-tab-item>
                  <v-form ref="RegisterformOTP" :lazy="lazy2" v-if="changeForm === false">
                    <v-row no-gutters dense class="mx-8 pt-4">
                      <!-- เบอร์โทรศัพท์ -->
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('register.Phone') }}</h3>
                        <v-text-field color="#269AFD" v-model="phoneOTP" :placeholder="$t('register.EnterPhone')" outlined dense required @keydown="noSpace" :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- Check Box ยอมรับ Consent -->
                      <v-col cols="12" md="12" sm="12" >
                        <input type="checkbox" v-model="ConCheck" style="" />
                        <span class="ml-2">{{ $t('register.Accept') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('A')">{{ $t('register.ServiceOneId') }}</a> {{ $t('register.And') }} <a class="text-decoration-underline" style="font-weight: 600; color: #269AFD;" @click="consent('B')">{{ $t('register.Policy') }}</a></span>
                      </v-col>
                      <!-- ปุ่มรับ OTP -->
                      <v-col cols="12" md="12" sm="12" class="mt-3">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="RegisterOTP()" :disabled="ConCheck === false" >{{ $t('register.OTP') }}</v-btn>
                      </v-col>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                  <v-form ref="formOTPNumber" :lazy-validation="lazyOTPNumber" v-else>
                    <v-card elevation="0" width="100%" height="100%" class="mt-4">
                      <v-card-text :class="MobileSize ? 'px-2' : ''">
                        <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                          <img loading="lazy" src="@/assets/ForgotPasswordOTP.jpg" width="174px" height="110px" alt="ForgotPasswordOTP">
                        </v-row>
                        <v-row dense justify="center" align-content="center" class="mt-4 mb-4">
                          <span style="font-weight: 700; color: #333333; font-size: 16px;">{{ $t('register.EnterOTP') }}</span>
                        </v-row>
                        <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                          <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                        </v-row>
                        <v-row no-gutters dense :class="MobileSize ? 'mx-0' : 'mx-4'" justify="center">
                          <v-col cols="12" md="12" sm="12">
                            <v-otp-input
                              ref="otpInput"
                              color="#269AFD"
                              v-model="otp"
                              :error="showError"
                              :length="length"
                              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                            ></v-otp-input>
                          </v-col>
                          <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                            <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('register.SendOTPPhone') }} <span style="color: #269AFD;">{{maskPhoneNumber(phoneOTP)}}</span></span>
                          </v-row>
                          <!-- <v-col cols="12" md="12" sm="12" class="mb-4">
                            <span style="font-weight: normal; font-size: 16px; color: #000000;">รหัสอ้างอิง: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                          </v-col> -->
                          <v-col cols="12" md="12" sm="12">
                            <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn></span>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      <v-card-actions :class="MobileSize ? 'px-0 pt-0' : 'px-4 pt-0'">
                        <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="checkOTP()" :disabled="!isActive">{{ $t('register.SendOTPButton') }}</v-btn>
                      </v-card-actions>
                      <!-- มีบัญชีอยู่แล้ว? ลงชื่อเข้าใช้ -->
                      <v-col cols="12" md="12" sm="12" class="mt-10 mb-6">
                        <v-row dense justify="center" align-content="center">
                          <span class="subtextReegister">{{ $t('register.AlreadyAccount') }} <span class="text-decoration-underline" style="color: #1E90FF; cursor: pointer;" @click="link('Login')">{{ $t('register.SignIn') }}</span></span>
                        </v-row>
                      </v-col>
                    </v-card>
                  </v-form>
                </v-tab-item>
              </v-tabs-items>
            </v-container>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
    <v-row justify="center">
      <v-dialog
        v-model="consentA"
        scrollable
        max-width="800px"
        :style="MobileSize ? 'z-index: ********' : ''"
      >
        <v-card style="overflow-x: hidden;">
          <v-card-title style="font-size: 18px; text-align:center;" class="d-flex justify-center">{{ $t('register.ServiceOneId') }}</v-card-title>
          <v-divider></v-divider>
          <v-card-text style="height: 100%;">
            <div v-html="detailA" style="padding: 15px 0;"></div>
          </v-card-text>
          <v-divider></v-divider>
        </v-card>
      </v-dialog>
      <v-dialog
        v-model="consentB"
        scrollable
        max-width="800px"
        :style="MobileSize ? 'z-index: ********' : ''"
      >
        <v-card style="overflow-x: hidden;">
          <v-card-title style="font-size: 18px; text-align:center;" class="d-flex justify-center">{{ $t('register.Policy') }}</v-card-title>
          <v-divider></v-divider>
          <v-card-text style="height: 100%;">
            <div v-html="detailB" style="padding: 15px 0;"></div>
          </v-card-text>
          <v-divider></v-divider>
        </v-card>
      </v-dialog>
    </v-row>
    <!-- dialog ยืนยันก่อนขอ OTP -->
    <v-dialog
      v-model="dialogBeforeRequestOTP"
      persistent
      max-width="424px"
      :style="MobileSize ? 'z-index: ********' : ''"
    >
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogBeforeRequestOTP = !dialogBeforeRequestOTP"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" class="my-4"><b>{{ $t('register.EasyOneID') }}</b></p>
            <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #9A9A9A;">{{ $t('register.OneIDSuccess') }} <br> {{ $t('register.OtherAccount') }}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogBeforeRequestOTP = !dialogBeforeRequestOTP">{{ $t('register.Cancel') }}</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ConfirmSentOTP()">{{ $t('register.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog Confirm Change Tab -->
    <v-dialog v-model="DialogConfirmChangeTab" persistent width="420px" style="border-radius: 24px;">
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 24px;">
        <v-card-text class="px-2 py-2">
          <v-row dense>
            <v-col cols="12" align="end">
              <v-btn icon @click="cancelChangeTab()">
                <v-icon color="primary">mdi-close</v-icon>
              </v-btn>
            </v-col>
            <v-col cols="12" align="center">
              <v-row dense>
                <v-col cols="12" align="center">
                  <v-icon size="108" color="#ffcd3f">mdi-information</v-icon>
                </v-col>
                <v-col cols="12" align="center">
                  <span style="font-weight: 700; color: #333333; font-size: 18px;">{{ $t('register.LeavePage') }}</span>
                </v-col>
                <v-col cols="12" align="center">
                  <span style="color: #333333; font-size: 16px;">{{ $t('register.ExitPage') }}</span>
                </v-col>
                <v-col cols="12" align="center" class="px-4 py-4">
                  <v-row dense class="d-flex">
                    <v-col class="mr-auto">
                      <v-btn block rounded height="40px" outlined color="primary" @click="cancelChangeTab()">{{ $t('register.Cancel') }}</v-btn>
                    </v-col>
                    <v-col class="ml-auto">
                      <v-btn block rounded height="40px" color="primary" @click="confirmChangeTab()">{{ $t('register.Confirm') }}</v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogMessage" persistent width="420px" style="border-radius: 24px;">
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 24px;">
        <v-card-text class="px-2 py-2">
          <v-row dense>
            <v-col cols="12" align="end">
              <v-btn icon @click="closeModal()">
                <v-icon color="primary">mdi-close</v-icon>
              </v-btn>
            </v-col>
            <v-col cols="12" align="center" v-if="responseCode === 'success'">
              <v-row dense>
                <v-col cols="12" align="center">
                  <v-icon size="108" color="primary">mdi-check-circle</v-icon>
                </v-col>
                <v-col cols="12" align="center">
                  <span style="font-weight: 700; color: #333333; font-size: 18px;">{{ $t('register.OTPCorrect') }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" align="center" v-else>
              <v-row dense>
                <v-col cols="12" align="center">
                  <v-icon size="108" color="red">mdi-close-circle</v-icon>
                </v-col>
                <v-col cols="12" align="center">
                  <span v-if="messageFail === 'รหัส OTP ไม่ถูกต้อง'" style="font-weight: 700; color: #333333; font-size: 18px;">{{ this.$i18n.locale === 'th' ? messageFail : 'Incorrect OTP' }}</span>
                  <span v-else-if="messageFail === 'ไม่พบชื่อผู้ใช้งานและเบอร์โทรของท่านที่ได้สมัครไว้กับระบบ'" style="font-weight: 700; color: #333333; font-size: 18px;">{{ this.$i18n.locale === 'th' ? messageFail : 'Username and phone number not found in the system.' }}</span>
                  <span v-else-if="messageFail === 'เบอร์โทรศัพท์นี้ถูกใช้งานไปแล้ว'" style="font-weight: 700; color: #333333; font-size: 18px;">{{ this.$i18n.locale === 'th' ? messageFail : 'This phone number is already in use.' }}</span>
                  <span v-else style="font-weight: 700; color: #333333; font-size: 18px;">{{ messageFail }}</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
// import axios from 'axios'
import axios from 'axios'
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      show1: false,
      show2: false,
      consentA: false,
      consentB: false,
      overlay: false,
      confirmpassword: '',
      ConCheck: false,
      MaxLength: 10,
      telephone: '',
      lazy: false,
      lazy2: false,
      lazyOTPNumber: false,
      tab: 0,
      pendingTab: null,
      username: '',
      password: '',
      emailMarket: '',
      passwordMarket: '',
      checkClick: false,
      dataText: '!@#$%^&*(),.?":{}|<>_',
      email: '',
      phone: '',
      phoneOTP: '',
      items: [
        { header: `${this.$t('register.SignUp')} One Platform` },
        { header: 'Easy OneID' }
      ],
      visibleLogin: false,
      visibleRegis: false,
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        name: [
          v => !!v || `${this.$t('register.ValidateUsername1')}`,
          v => /^[A-Za-z0-9\s]+$/.test(v) || `${this.$t('register.ValidateUsername2')}`,
          v => v.length >= 6 || `${this.$t('register.ValidateUsername3')}`
        ],
        password: [
          v => !!v || `${this.$t('register.ValidatePassword1')}`,
          v => /[A-Za-z0-9!@#$%^&*(),.?":{}|<>_]/.test(v) || `${this.$t('register.ValidatePassword2')}`,
          v => /(?=.*?[A-Za-z]).+/.test(v) || `${this.$t('register.ValidatePassword3')}`,
          v => /(?=.*\d).+/.test(v) || `${this.$t('register.ValidatePassword4')}`,
          v => v.length >= 8 || `${this.$t('register.ValidatePassword5')}`
        ],
        email: [
          v => !!v || `${this.$t('register.ValidateEmail1')}`,
          v => /.+@.+\..+/.test(v) || `${this.$t('register.ValidateEmail2')}`,
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || `${this.$t('register.ValidateEmail3')}`,
          v => /^\S*$/.test(v) || `${this.$t('register.ValidateEmail4')}`
        ],
        tel: [
          v => !!v || `${this.$t('register.ValidatePhone1')}`
        ]
      },
      detailA: '',
      detailB: '',
      dialogBeforeRequestOTP: false,
      DialogConfirmChangeTab: false,
      gliderPosition: {
        width: '0px',
        left: '0px'
      },
      changeForm: false,
      responseCode: '',
      messageFail: '',
      DialogMessage: false,
      otp: '',
      length: 6,
      dataOTP: [],
      refCode: '',
      otpCode: '',
      showError: false,
      displayCounter: '00:00',
      counter: 0,
      disableRefreshOTP: true,
      dataToKeepLogin: []
      // Rules: {
      //   empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
      //   name: [
      //     v => !!v || 'กรุณากรอกชื่อผู้ใช้งาน'
      //   ],
      //   password: [
      //     v => !!v || 'กรุณากรอกรหัสผ่าน'
      //   ],
      //   email: [
      //     v => !!v || 'กรุณากรอกอีเมล'
      //   ],
      //   tel: [
      //     v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
      //   ]
      // },
      // form: this.$form.createForm(this, { name: 'normal_login' }),
      // FormRegister: this.$form.createForm(this, { name: 'register' })
    }
  },
  computed: {
    isActive () {
      return this.otp.length === this.length
    },
    passwordConfirmationRule () {
      return () =>
        this.password === this.confirmpassword || 'กรุณาใส่รหัสผ่านให้ตรงกัน'
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    gliderStyle () {
      // กำหนดสไตล์ให้ glider ตามตำแหน่งและขนาดที่คำนวณได้
      return {
        width: this.gliderPosition.width,
        left: this.gliderPosition.left,
        transition: 'all 0.3s ease'
      }
    }
  },
  created () {
    this.$EventBus.$emit('main', this.$route.name)
    this.$EventBus.$emit('checkpage', this.$route.name)
    var CurrentPath = this.$router.currentRoute.path
    localStorage.setItem('CurrentPath', CurrentPath)
    this.$EventBus.$on('closeModalLogin', this.closeModalLogin)
    this.$EventBus.$on('openModalLogin', this.openModalLogin)
    this.$EventBus.$on('closeModalRegister', this.closeModalRegister)
    this.$EventBus.$on('openModalRegister', this.openModalRegister)
    this.GetPrivacyConsent()
    this.GetTermConsent()
  },
  methods: {
    confirmPasswordRules (confirmPass, Pass) {
      if (confirmPass === '') {
        return this.$t('register.ValidatePassword1')
      } else if (confirmPass !== Pass) {
        return this.$t('register.ValidateConfirmPassword1')
      }
    },
    maskPhoneNumber (phoneNumber) {
      return phoneNumber.substring(0, 2) + 'xxxxxx' + phoneNumber.substring(8)
    },
    handleTabChange (newTab) {
      if (newTab === 1) {
        if (this.username !== '' || this.password !== '' || this.confirmpassword !== '' || this.email !== '' || this.phone !== '') {
          this.pendingTab = newTab
          this.DialogConfirmChangeTab = true
        } else {
          this.pendingTab = newTab
          this.confirmChangeTab()
        }
      } else {
        if (this.phoneOTP !== '') {
          this.pendingTab = newTab
          this.DialogConfirmChangeTab = true
        } else {
          this.pendingTab = newTab
          this.confirmChangeTab()
        }
      }
    },
    cancelChangeTab () {
      this.tab = this.pendingTab === 1 ? 0 : 1
      this.DialogConfirmChangeTab = false
    },
    confirmChangeTab () {
      this.tab = this.pendingTab
      if (this.tab === 1) {
        if (this.changeForm === false) {
          this.phoneOTP = ''
          this.ConCheck = false
          this.$refs.RegisterformOTP.resetValidation()
        } else {
          this.phoneOTP = ''
          this.ConCheck = false
          this.changeForm = false
        }
      } else {
        this.username = ''
        this.password = ''
        this.confirmpassword = ''
        this.phone = ''
        this.ConCheck = false
        this.$refs.Registerform.resetValidation()
      }
      this.updateGlider()
      this.DialogConfirmChangeTab = false
    },
    updateGlider () {
      const tabs = this.$refs.tab
      if (tabs && tabs[this.tab]) {
        const currentTab = tabs[this.tab].$el || tabs[this.tab]
        this.gliderPosition = {
          width: `${currentTab.offsetWidth}px`,
          left: `${currentTab.offsetLeft}px`
        }
      }
    },
    countdownCheck (second) {
      this.counter = second
      const interval = setInterval(() => {
        var minutes = Math.floor(this.counter / 60)
        var seconds = this.counter % 60
        seconds = seconds < 10 ? `0${seconds}` : seconds
        this.displayCounter = `${minutes}:${seconds}`
        this.counter--
        if (this.counter < 0) {
          this.disableRefreshOTP = false
          clearInterval(interval)
        }
      }, 1000)
    },
    async RefreshOTP () {
      this.messageFail = ''
      this.responseCode = ''
      this.otp = ''
      this.$store.commit('openLoader')
      const dataSentOTP = {
        phone: this.phoneOTP
      }
      await this.$store.dispatch('actionsSendOTPRegister', dataSentOTP)
      const responseSentOTP = await this.$store.state.ModuleHompage.stateSendOTPRegister
      if (responseSentOTP.message === 'ส่ง OTP สำเร็จ') {
        this.$store.commit('closeLoader')
        this.counter = 0
        this.countdownCheck(300)
      } else {
        this.$store.commit('closeLoader')
        this.responseCode = 'fail'
        this.messageFail = responseSentOTP.message
        this.DialogMessage = true
      }
    },
    async checkOTP () {
      this.messageFail = ''
      this.responseCode = ''
      this.$store.commit('openLoader')
      const dataCheckOTP = {
        phone: this.phoneOTP,
        otp: this.otp
      }
      await this.$store.dispatch('actionsRegisterWebByOTP', dataCheckOTP)
      const responseCheckOTP = await this.$store.state.ModuleHompage.stateRegisterWebByOTP
      if (responseCheckOTP.message === 'ลงทะเบียนสำเร็จ') {
        this.$store.commit('closeLoader')
        this.dataToKeepLogin = responseCheckOTP.data
        this.responseCode = 'success'
        this.DialogMessage = true
      } else {
        this.$store.commit('closeLoader')
        this.responseCode = 'fail'
        this.messageFail = responseCheckOTP.message
        this.DialogMessage = true
      }
    },
    onPaste (evt) {
      if (evt.which === 32) {
        evt.preventDefault()
      }
    },
    checkCopyPaste (e) {
      if (e.which === 32) {
        e.preventDefault()
      }
    },
    async GetPrivacyConsent () {
      // Privacy
      await axios({
        url: 'https://one.th/api/privacy-policy-for-service?client_id=784',
        method: 'GET'
      }).then(
        (response) => {
          // console.log('detailB---->', response.data)
          this.detailB = response.data
        }
      )
    },
    async GetTermConsent () {
      // Term
      await axios({
        url: 'https://one.th/api/term-of-use-for-service?client_id=784',
        method: 'GET'
      }).then((response) => {
        // console.log('detailA---->', response.data)
        this.detailA = response.data
      })
    },
    consent (val) {
      if (val === 'A') {
        this.consentA = true
      } else if (val === 'B') {
        this.consentB = true
      }
    },
    noSpace (e) {
      if (e.which === 32) {
        e.preventDefault()
      }
    },
    link (val) {
      this.$router.replace({ path: `/${val}` }).catch(() => {})
    },
    // Register (e) {
    //   e.preventDefault()
    //   this.FormRegister.validateFields(async (err, values) => {
    //     // console.log(err, values)
    //     if (!err) {
    //       console.log('ก่อนยิง api', Object.prototype.hasOwnProperty.call(localStorage, 'oneData'))
    //       this.checkClick = false
    //       var checkonedata = Object.prototype.hasOwnProperty.call(localStorage, 'oneData')
    //       if (checkonedata) {
    //         var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //         onedata.CurrentPath = this.$router.currentRoute.path
    //         await this.APIRegister(values)
    //       } else {
    //         onedata = {}
    //         onedata.CurrentPath = this.$router.currentRoute.path
    //         localStorage.setItem('oneData', Encode.encode(onedata))
    //         await this.APIRegister(values)
    //       }
    //     }
    //   })
    // },
    closeModal () {
      this.DialogMessage = false
      if (this.responseCode === 'success') {
        // this.$router.push({ path: '/Login' }).catch(() => {})
        var onedata = {}
        onedata.user = this.dataToKeepLogin
        // console.log(onedata.user)
        var dataRole = {
          role: 'ext_buyer'
        }
        localStorage.setItem('roleUser', JSON.stringify(dataRole))
        localStorage.setItem('oneData', Encode.encode(onedata))
        // window.location.reload()
        this.$store.commit('openLoader')
        setTimeout(() => {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('LoginUser')
          this.$EventBus.$emit('checkPDPA')
          this.$EventBus.$emit('getCartPopOver')
          this.$EventBus.$emit('getItemNoti')
          this.$EventBus.$emit('checkChatMe')
          this.$router.push({ path: '/' }).catch(() => {})
        }, 100)
        // window.location.assign('/')
      }
    },
    RegisterOTP () {
      if (this.$refs.RegisterformOTP.validate(true)) {
        this.dialogBeforeRequestOTP = true
      } else {
      }
    },
    async ConfirmSentOTP () {
      this.dialogBeforeRequestOTP = false
      this.messageFail = ''
      this.responseCode = ''
      this.$store.commit('openLoader')
      var data = {
        phone: this.phoneOTP
      }
      await this.$store.dispatch('actionsSendOTPRegister', data)
      var response = await this.$store.state.ModuleHompage.stateSendOTPRegister
      // console.log(response)
      if (response.message === 'ส่ง OTP สำเร็จ') {
        this.$store.commit('closeLoader')
        this.changeForm = true
        window.scrollTo(0, 0)
        this.counter = 0
        this.countdownCheck(300)
      } else {
        this.$store.commit('closeLoader')
        this.responseCode = 'fail'
        this.messageFail = response.message
        this.DialogMessage = true
      }
      // this.changeForm = true
    },
    async Register () {
      if (this.$refs.Registerform.validate(true)) {
        this.$store.commit('openLoader')
        var checkonedata = Object.prototype.hasOwnProperty.call(localStorage, 'oneData')
        if (checkonedata) {
          var checkMail = {
            email: this.email
          }
          var ResponseCheckMail = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/check_email_one_id`, checkMail)
          // console.log('ResponseCheckMail =========>', ResponseCheckMail)
          if (ResponseCheckMail.data.code !== 400 && ResponseCheckMail.data.message !== 'email duplicate') {
            var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            onedata.CurrentPath = this.$router.currentRoute.path
            await this.APIRegister()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ text: this.$t('register.AlreadyUseEmail'), icon: 'error', timer: 2500, showConfirmButton: false })
          }
        } else {
          var checkMail1 = {
            email: this.email
          }
          var ResponseCheckMail1 = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/check_email_one_id`, checkMail1)
          // console.log('ResponseCheckMail1 =========>', ResponseCheckMail1)
          if (ResponseCheckMail1.data.code !== 400 && ResponseCheckMail1.data.message !== 'email duplicate') {
            onedata = {}
            onedata.CurrentPath = this.$router.currentRoute.path
            localStorage.setItem('oneData', Encode.encode(onedata))
            await this.APIRegister()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ text: this.$t('register.AlreadyUseEmail'), icon: 'error', timer: 2500, showConfirmButton: false })
          }
        }
      }
    },
    async encodePassword (password) {
      const CryptoJS = require('crypto-js')
      const phrase = 'tenmerucorpEDeveloper'
      // const iv = CryptoJS.enc.Utf8.parse('CGNecremmocnegxen') // Initialization vector
      const iv = CryptoJS.enc.Utf8.parse('CGNecremmocnegxe') // Initialization vector

      // Create a SHA-256 hash of the phrase to use as the secret key
      const secretKey = CryptoJS.SHA256(phrase).toString(CryptoJS.enc.Hex)

      // Encrypt the data
      const encryptedData = CryptoJS.AES.encrypt(password, CryptoJS.enc.Hex.parse(secretKey), {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })

      // Convert the encrypted data to Base64
      const encryptedBase64 = encryptedData.toString()
      return encryptedBase64
    },
    async APIRegister () {
      const encryptedToken = await this.encodePassword(this.password)
      const encryptedTokenRePassword = await this.encodePassword(this.confirmpassword)
      var data = {
        username: this.username,
        password: encryptedToken,
        re_password: encryptedTokenRePassword,
        email: this.email,
        mobile_no: this.phone
      }
      // console.log(data)
      var RegisterData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/register_and_login`, data)
      if (RegisterData.data.result === 'SUCCESS') {
        this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
        this.username = ''
        this.password = ''
        this.email = ''
        this.phone = ''
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.user = RegisterData.data.data
        var dataRole = {
          role: 'ext_buyer'
        }
        localStorage.setItem('roleUser', JSON.stringify(dataRole))
        localStorage.setItem('oneData', Encode.encode(onedata))
        // window.location.reload()
        window.location.assign('/')
      } else if (RegisterData.data.message === 'Your username duplicate') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ text: this.$t('register.RegisterFail1'), icon: 'error', timer: 2500, showConfirmButton: false })
      } else if (RegisterData.data.message === 'Parameter is missing.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ text: this.$t('register.RegisterFail2'), icon: 'error', timer: 2500, showConfirmButton: false })
      } else if (RegisterData.data.message === 'หมายเลขโทรศัพท์มือถือซ้ำ หรือถูกใช้งานโดยผู้ใช้อื่นแล้ว') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ text: this.$t('register.RegisterFail3'), icon: 'error', timer: 2500, showConfirmButton: false })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
      }
      // var RegisterData = await this.axios.post(`${process.env.VUE_APP_CONNECT_REGISTER_AND_LOGIN}`, data)
      // if (RegisterData.data.result === 'SUCCESS') {
      //   this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
      //   var onedata = {}
      //   onedata.user = RegisterData.data.data
      //   var PathRedirect = localStorage.getItem('CurrentPath')
      //   if (PathRedirect === '/' || PathRedirect === '/') {
      //     PathRedirect = ''
      //   } else if (PathRedirect === '/Register' || PathRedirect === '/Login') {
      //     PathRedirect = ''
      //   }
      //   var dataRole = {
      //     role: 'ext_buyer'
      //   }
      //   localStorage.setItem('roleUser', JSON.stringify(dataRole))
      //   localStorage.setItem('oneData', Encode.encode(onedata))
      //   this.$refs.Registerform.resetValidation()
      //   this.$refs.Registerform.reset()
      //   this.$store.commit('closeLoader')
      //   window.location.assign(`${process.env.VUE_APP_DOMAIN}${PathRedirect}`)
      //   // this.$router.push({ path: `${PathRedirect}` }).catch(() => {})
      // } else if (RegisterData.data.message === 'Your username duplicate.') {
      //   this.$store.commit('closeLoader')
      //   this.$swal.fire({ text: 'ชื่อผู้ใช้นี้ถูกใช้ไปแล้ว กรุณาเปลี่ยนชื่อผู้ใช้ใหม่ครับ', icon: 'error', timer: 2500, showConfirmButton: false })
      // } else {
      //   this.$store.commit('closeLoader')
      //   this.$swal.fire({ text: 'กรุณาทำรายการใหม่อีกครั้งครับ', icon: 'error', timer: 2500, showConfirmButton: false })
      // }
    },
    //  APIRegister =====>  ของเก่า
    // async APIRegister (values) {
    //   var data = {
    //     username: values.username,
    //     password: values.password,
    //     email: values.email,
    //     mobile_no: values.phone.replace(/-/g, '')
    //   }
    //   // console.log(data)
    //   var RegisterData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/register_and_login`, data)
    //   if (RegisterData.data.result === 'SUCCESS') {
    //     this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
    //     this.username = ''
    //     this.password = ''
    //     this.email = ''
    //     this.phone = ''
    //     var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //     onedata.user = RegisterData.data.data
    //     var dataRole = {
    //       role: 'ext_buyer'
    //     }
    //     localStorage.setItem('roleUser', JSON.stringify(dataRole))
    //     localStorage.setItem('oneData', Encode.encode(onedata))
    //     window.location.reload()
    //   } else {
    //     this.$swal.fire({ text: `${RegisterData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
    //   }
    // },
    // async Login () {
    //   console.log('this.$router.currentRoute.params.data', this.$router.currentRoute.path)
    //   var onedata = {}
    //   onedata.CurrentPath = this.$router.currentRoute.path
    //   console.log(onedata)
    //   localStorage.setItem('oneData', Encode.encode(onedata))
    //   console.log('ข้อมูล local', JSON.parse(Decode.decode(localStorage.getItem('oneData'))))
    //   window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
    // },
    async LoginMarket (e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          var data = {
            email: values.emailMarket,
            password: values.passwordMarket
          }
          // console.log(data)
          var LoginData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/login_market`, data)
          // console.log(LoginData)
          if (LoginData.data.result === 'SUCCESS') {
            this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
            values.emailMarket = ''
            values.passwordMarket = ''
            this.form.resetFields()
            var dataRole = {
              role: 'ext_buyer'
            }
            localStorage.setItem('roleUser', JSON.stringify(dataRole))
            var onedata = {}
            // console.log('onedata ====>', onedata)
            onedata.user = LoginData.data.data
            localStorage.setItem('oneData', Encode.encode(onedata))
            window.location.reload()
          } else {
            this.$swal.fire({ text: `${LoginData.data.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
            this.passwordMarket = ''
            this.openModalLogin()
          }
        }
      })
    },
    closeModalLogin () {
      this.visibleLogin = false
    },
    openModalLogin () {
      this.visibleLogin = true
    },
    closeModalRegister () {
      this.visibleRegis = false
    },
    openModalRegister () {
      this.visibleRegis = true
    },
    showRegister () {
      this.visibleRegis = true
      this.visibleLogin = false
    }
  }
}
</script>

<style scoped>
.borderButtomText {
  margin-bottom: -30px
}
.tabsStyle {
  border-radius: 999px;
  position: relative;
  overflow: hidden;
}
.activeTabs {
  background-color: #FFFFFF;
  border-radius: 999px;
  margin: 2px;
}
.v-tab {
  z-index: 2; /* ให้แท็บอยู่ด้านบน */
}
.glider {
  position: absolute;
  top: 2px;
  bottom: 2px;
  background-color: #ffffff; /* สีขาวที่ครอบแท็บ */
  border-radius: 999px; /* เพิ่มความโค้งให้กลมกลืน */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); /* เงาเพื่อความสวยงาม */
  z-index: 1; /* อยู่ด้านล่างของแท็บ */
  transition: all 0.3s ease;
}
@media (max-width: 600px) {
  ::v-deep(.v-slide-group__prev--disabled),
  ::v-deep(.v-slide-group__next--disabled) {
    display: none !important;
  }
}
</style>

<style>
.numeric-input .ant-tooltip-inner {
  min-width: 50px;
  min-height: 35px;
}
.numeric-input .numeric-input-title {
  font-size: 14px;
}
.ant-popover-inner-content{
  padding: 0;
}
#components-form-demo-normal-login .login-form {
  max-width: 350px;
}
#components-form-demo-normal-login .login-form-forgot {
  float: right;
  margin-bottom: 10px;
  margin-top: -15px;
}
#components-form-demo-normal-login .login-form-button {
  width: 100%;
}
</style>
