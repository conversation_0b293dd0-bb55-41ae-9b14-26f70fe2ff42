import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  // Create Business ID
  async axiosCreateBusiness (val) {
    const data = val
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_business`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Get Detail Business ID
  async axiosDetailBusiness (val) {
    // console.log(val)
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_business`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // find tax id detail อันนี้จะเป็นตัวที่ยิงไปหากรมอะไรซักอย่าง
  async axiosFindTaxIDByRevenue (val) {
    // console.log(val)
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/find_tax_id`, val, auth)
      // console.log('axiosFindTaxIDByRevenue', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // find tax id detail by one id
  async axiosFindTaxIDByOneID (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/find_tax_id_by_one_id`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Upload Approver
  async axiosUploadApprover (val) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`,
        'Content-Type': 'multipart/form-data'
      }
    }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/register_business_step_1`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Upload ResultDocument
  async axiosUploadResultDocument (val) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`,
        'Content-Type': 'multipart/form-data'
      }
    }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/register_business_step_2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosDetailBusinessOfUser () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/detail_business_of_user`, '', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosCreateBusinessOther (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/register_business_other`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosManageUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_users_with_tax_id`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosManageCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_company_with_tax_id_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosRoleUpdate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async orderDetailCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}orderJV/orderJV_details`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosManageShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_shop_with_tax_id_v3`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosListCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_company_with_tax_id`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listBusinessOrders (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}business/orderList`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosListPositions (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosListShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_shop_with_tax_id_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosAssignPositionsUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/assign_business_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosCreateRole (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_position_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosSearchUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/search_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosEditRole (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}business/listCompanyV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListUsersCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_users_with_company_id`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListUsersShops (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/list_users_with_seller_shop_id`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateSellerShopNewBranch (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_seller_shop_new_branch`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateOwnerPosition (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_owner_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/business/check_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPackage (data) {
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/partner/get_package_list`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreatePackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/create_package_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailPackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/get_package`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPartnerDetails (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/partner_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RegisterBusiness (data) {
    try {
      const auth = await GetToken()
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/register`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PartnerEdit (data) {
    try {
      const auth = await GetToken()
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/edit`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPartnerCode (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/code`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RegisterPartner (data) {
    try {
      const auth = await GetToken()
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}/api/partner/register`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditPackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/edit_package_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CancelPackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/cancel_package`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPartnerList (data) {
    try {
      const auth = await GetToken()
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}/api/partner/list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPartnerService (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/check_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPartnerOrderList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/orderlist_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBillList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/get_bill_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPartnerOrderListV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/orderlist_partnerV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBillDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/get_bill_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailPartnerOrderList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/check_order_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBillingApproval (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/billing_approval`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CancelFunction (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/cancel_function`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async changeStatusPackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/cancel_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDashboardPartnerDistributeIncome (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/dashboard/distribute_income`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDashboardPartnerOrderListIncome (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/dashboard/order_list_income`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetTransfer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/partner/transfer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RanksPackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/dashboard/ranks_package`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RanksShopBuying (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/dashboard/ranks_shop_buying`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListOrderPackage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/dashboard/list_order_packages`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PartnerEtaxDocumentInvoice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/etax/document_invoice`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PartnerEtaxDocumentReceipt (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/etax/decument_receipt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PartnerChartIncome (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/dashboard/graph_income`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckBusiness (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_business`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RegisterShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/register_business`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
