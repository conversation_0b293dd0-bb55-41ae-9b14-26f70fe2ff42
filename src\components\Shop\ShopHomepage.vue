<template>
  <v-container>
    <v-card
     class="mx-auto mb-5"
     max-width="100%"
     elevation="0"
    >
      <v-card
       outlined
       max-width="100%"
       height="125"
       class="backPic"
      >
        <v-card-text>
          <v-row no-gutters justify="start">
            <!-- <v-avatar size="60" v-if="dataShop.shop_image !== ''">
              <img
                alt="user"
                :src="`${dataShop.shop_image}`"
              >
            </v-avatar>
            <v-avatar size="60" v-else>
              <v-icon large>
                mdi-storefront
              </v-icon>
            </v-avatar> -->
            <p style="font-weight: bold; font-size: 15px;">{{ dataShop.shop_name_th }}</p>
          </v-row>
          <!-- <v-row dense no-gutters justify="start" class="pt-2">
            <v-col cols="12" md="12" sm="12" xs="12" v-if="statusPartner !== 'send'">
              <v-btn outlined small color="black" @click="followShop()" class="px-12"><v-icon small>mdi-plus</v-icon> ติดตามร้านค้า</v-btn>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12" v-else-if="statusPartner === 'send' ">
              <v-btn outlined small color="rgb(255, 99, 71)" @click="unfollowShop()" class="px-6">ยกเลิกการติดตามร้านค้า</v-btn>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12" v-else>
              <v-btn outlined small color="rgb(255, 99, 71)" disabled class="px-12"><v-icon small>mdi-plus</v-icon> ติดตามร้านค้า</v-btn>
            </v-col>
          </v-row> -->
        </v-card-text>
      </v-card>
    </v-card>
    <v-card
     outlined
     max-width="100%"
    >
      <v-tabs
       v-model="tabs"
       background-color="transparent"
       grow
       color="success"
      >
        <v-tab v-for="(item, index) in items" :key="index">
          {{ item }}
        </v-tab>
        <v-tab-item v-if="status === true">
          <v-row no-gutters v-if="AllProduct.length !== 0">
            <v-col cols="12" md="2" sm="6" xs="12" v-for="(item, index) in AllProduct" :key="index" class="my-2">
              <CardProducts :itemProduct="item" />
            </v-col>
          </v-row>
          <v-row justify="start" v-else>
            <v-col cols="12" md="6" sm="12" xs="12" class="pa-5">
              <h2>ไม่มีสินค้า</h2>
            </v-col>
          </v-row>
        </v-tab-item>
        <v-tab-item v-if="status === true">
          <v-row no-gutters v-if="bestSeller.length !== 0">
            <v-col cols="12" md="2" sm="6" xs="12" v-for="(itemProduct, index) in bestSeller" :key="index" class="my-2">
              <CardProducts :itemProduct="itemProduct"/>
            </v-col>
          </v-row>
          <v-row justify="start" v-else>
            <v-col cols="12" md="6" sm="12" xs="12" class="pa-5">
              <h2>ไม่มีสินค้า</h2>
            </v-col>
          </v-row>
        </v-tab-item>
      </v-tabs>
    </v-card>
  </v-container>
</template>

<script>
const AllProduct = []
for (let i = 100; i < 200; i++) {
  AllProduct.push({
    product_id: i,
    name: `Data Title Product ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: 'https://picsum.photos/id/1039/600/600'
  })
}
const bestSeller = []
for (let i = 0; i < 50; i++) {
  bestSeller.push({
    product_id: i,
    name: `Data Title bestSeller ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: 'https://picsum.photos/id/1039/600/600'
  })
}
export default {
  components: {
    CardProducts: () => import('@/components/Card/ProductCard')
  },
  data () {
    return {
      shopname: 'ร้านขายของ 1',
      shopImage: '',
      statusPartner: '',
      tabs: null,
      items: [
        'สินค้าทั้งหมด', 'สินค้าขายดี'
      ],
      AllProduct,
      bestSeller,
      dataShop: [],
      PathImage: process.env.VUE_APP_IMAGE,
      status: false
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    this.$EventBus.$on('getSellerShopPage', this.getSellerShopPage)
    this.getSellerShopPage()
  },
  methods: {
    followShop () {
      this.statusPartner = 'send'
    },
    unfollowShop () {
      this.statusPartner = ''
    },
    async getSellerShopPage () {
      var data
      var path = this.$router.currentRoute.params.data
      var cleanPath = path.split('-')
      if (localStorage.getItem('roleUser') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        data = {
          seller_shop_id: cleanPath[cleanPath.length - 1],
          role: dataRole.role
        }
      } else {
        data = {
          seller_shop_id: cleanPath[cleanPath.length - 1],
          role: 'ext_buyer'
        }
      }
      await this.$store.dispatch('actionsShopDetailPage', data)
      var response = await this.$store.state.ModuleShop.stateShopDetailPage
      // console.log('response Shop Detail Page =======>', response)
      if (response.result === 'SUCCESS') {
        this.dataShop = response.data
        this.AllProduct = response.data.list_new_products
        this.bestSeller = response.data.list_best_sold_products
        this.status = true
      }
    }
  }
}
</script>

<style scoped>
.backPic {
  background-image: linear-gradient(to left, #f4fadd, #dcf8e7);
  background-size: 100%;
}
</style>
