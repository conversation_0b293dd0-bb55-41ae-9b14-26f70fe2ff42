import AxiosShop from './axios_shop_api'
import eventBus from '@/components/eventBus'
// import Converter from '@/components/library/tableDataForm'
// import moment from 'moment'

const ModuleShop = {
  state: {
    // Shop Detail Page
    stateShopDetailPage: [],
    // Create Shop Page
    stateCreateSellerShop: [],
    // Get Shop Data
    stateShopData: [],
    // Create Setting Shop
    stateCreateSettingSellerShop: [],
    // Delete product
    stateDeleteProduct: [],
    stateDeleteBySeller: [],
    // product select option
    SelectOption: [],
    // update shop address
    stateUpdateshopaddress: [],
    stategetUpdateshopaddress: [],
    stateDashboard: [],
    stateDashboard2: [],
    stateBeforeDashboard: [],
    setKey: [],
    stateGetListPartner: [],
    stateCheckRequestPartner: [],
    staterequest: [],
    stateSeries: [],
    stateMax: 0,
    stateColor: [],
    tmpObj: {},
    tmpJson: [],
    stateListRefundSeller: [],
    stateApproveRefundSeller: [],
    stateDetailRefundSeller: [],
    transactionNumber: '',
    stateNotiRefundSeller: [],
    stateDateTime: [],
    stateTimeline: [],
    stateProductBestSeller: [],
    stateUserMostBuyProduct: [],
    stateListRefMerchant: [],
    stateRefMerchant: [],
    stateNewRefMerchant: [],
    stateFuntionChange: {},
    keyReload: '0',
    dateBus: {},
    stateCreateShop: [],
    stateDatailShop: [],
    stateCategory: [],
    stateDetailDocumentShop: [],
    stateEditShop: [],
    stateShowProductShop: [],
    stateDetailMerchantFromPayment: [],
    stateDetailSellerPartner: [],
    stateEditPartner: [],
    stateCreatePosition: [],
    stateEditPodition: [],
    stateListCompanyPorsition: [],
    stateSearchUserCompany: [],
    stateGetListCustomerAddress: [],
    stateGetListCustomerInvAddress: [],
    stateList_Appover: [],
    stateAdd_Appover: [],
    stateEdit_Appover: [],
    openModalPurchaseOrder: false,
    formData: {
      customerName: 'ประยุทธ์ อาทิตย์โอชา',
      detailsProduct: [
        { description: 'ATK 100 อัน 58 บาทThe v-btn component replaces the standard html button with a material design theme and a multitude of options. Any color helper class can be used to alter the background or text color. Try out an interactive screencast on how Vuetify buttons work.', quantity: 3, unit: 'อัน', cost: 53 }
      ],
      address: '320 หมู่ 23 ตำบล บ้านเป็ด อำเภอเมืองขอนแก่น จังหวัดขอนแก่น 40000',
      taxpayerIdentificationNo: '',
      quotationNumber: '',
      date: '',
      conditional: '',
      credit: '',
      signaturePersonality1: 'วรวุฒิ ธิดากร',
      signaturePersonality2: 'วินัย ธรรมนัส',
      signaturePersonality3: 'Alex choc',
      thaiIncluding: ''
    },
    // Get Review Prodoct
    stateGetReviewProduct: [],
    stateListQuotationSeller: [],
    stateListUserCompany: [],
    stateDetailUserCompanyPosition: [],
    stateEditPositionCompany: [],
    stateCreatePositionCompany: [],
    stateList_ManageBuyer: [],
    stateReport: [],
    stateDetail_ManageBuyer: [],
    stateAppoverQU: [],
    // (YaPalm)Seller List Order Credit Term
    stateListOrderCreditTerm: [],
    stateListCreditTerm: [],
    stateUploadInvoiceCreditTerm: [],
    stateListRequestChangeTermBySellerShop: [],
    stateUpdateRequestCreditTerm: [],
    // (Ya)List Special Price Selller
    stateListSpecialPriceSeller: [],
    stateDetailSpecialPrice: [],
    stateDetailSpecialPriceSeller: [],
    stateApproveSpecialPrice: [],
    stateCalculateSpecialPrice: [],
    stateEditSpecialPrice: [],
    stateCreateMerchant: [],
    stateCheckMerchant: [],
    // stateListPositionSeller: []
    stateCreateUpdatePositionInShop: [],
    stateGetPositionDetailInShop: [],
    stateListPositionInShop: [],
    stateManageUserWithRoleInShop: [],
    stateIndexAddProduct: '',
    // shop coupon
    stateListShopCoupon: [],
    stateDetailShopCoupon: [],
    stateDeleteShopCoupon: [],
    stateCreateShopCoupon: [],
    stateSearchCoupon: [],
    stateEditShopCoupon: [],
    stateAppoverQT: [],
    stateConfirmAppoverQT: [],
    stateBuyerUpdateStatusQT: [],
    // Credit Term Revenue
    stateDetailMerchantCrtermFromPayment: [],
    stateRefShareCreditTermToMerchant: [],
    stateNewGetDashboard: [],
    state10User: [],
    state10Product: [],
    stateUpdatePositionStatus: [],
    // Check User Business
    stateCheckUserBusiness: [],
    stateListAllShop: [],
    // B2B
    stateImportExcel: [],
    stateImportExcelNoJV: [],
    stateCancelAppoverQT: [],
    stateCreateJSON_QT: [],
    stateNewListQTSeller: [],
    stateNewListQTSellerV2: [],
    stateNewDetailQTSeller: [],
    stateListIncludeQT: [],
    stateGetQTDashBoard: [],
    stateGetSumTrendDashBoard: [],
    stateGetSumDocQTDashBoard: [],
    stateGetSumDocSODashBoard: [],
    stateGetSumDocPRDashBoard: [],
    stateGetSumDocPODashBoard: [],
    stateGetSumTopFiveQTDashBoard: [],
    stateAccecptProduct: [],
    // New Upload image to s3
    stateUploadToS3: [],
    stateGetCategoryShopList: [],
    stateGetSaleOrder: [],
    stateGetListTierCustomer: [],
    stateSelectCategoryShopList: [],
    stateOutOfStockProducts: [],
    stateUpdateTrackingNumber: [],
    stateGenerateShortenlink: [],
    // check sku
    stateCheckSKU: [],
    stateCalPriceVat: [],
    stateCountSummaryProduct: [],
    stateListCoupon: [],
    stateCollectCoupon: [],
    stateListBank: [],
    stateImportExcelNoJVSimple: [],
    stateGetProvince: [],
    stateUpdateSellerBot: [],
    // Attorney
    stateListAttorneyShop: [],
    stateAddAttorney: [],
    stateGetAttorneyUserDetail: [],
    stateAttorneyUpdateAndConfrime: [],
    stateListReciver: [],
    stateSetActiveAttorney: [],
    stateUploadFDAFile: [],
    stateListDocumentsSellerShop: [],
    stateApprovePRDocument: [],
    stateAddProductERP: [],
    stateSetServiceKeyERP: [],
    stateUpdateServiceKeyERP: [],
    stateUpdateStatusServiceKeyERP: [],
    stateDetailServiceKeyERP: [],
    stateListSyncShopErp: [],
    stateSetSyncShopErp: [],
    stateUpdateStockErp: [],
    // DashboardWDshop
    stateDashboardWDshopOrderHistory: [],
    stateWithdrawSummary: [],
    stateWithdrawDetails: [],
    statetransactionHistory: [],
    statetransferShopClick: [],
    stateSettingQTExternal: [],
    stateDetailSettingQTExternal: [],
    stateupdateExcelProducts: [],
    stateFilterAllGroupShop: [],
    stateFilterGroupShop: [],
    stateGetListDetailPartnerShop: [],
    stateUpdateStatusAfterBuyPartner: [],
    stateListInterestedPartner: [],
    stateApproveSlipPayment: [],
    stateUploadSlipLotus: [],
    stateAddwarehouse: [],
    stateSyncWarehouse: [],
    stateCustomGroupShop: [],
    stateGetCustomGroupShop: [],
    stateActiveCustomGroupShop: [],
    stateAccountDetailShop: [],
    stateAccountDetailShopUpdate: [],
    stateCreateAccountShop: [],
    // change status product
    stateChangeStatusProduct: [],
    statePayWithCreditCard: [],
    statePayWithQRCode: [],
    stateCheckResultQRCodeMarketplace: [],
    stateGetPackageList: [],
    stateAddUserManual: [],
    stateDeleteUserManual: [],
    stateDetailUserManual: [],
    stateEditShippingSeller: [],
    // change status shop
    stateChangeStatusShop: [],
    stateChangePartnerShow: [],
    stateTypeGroupShop: [],
    stateEditBannerGroupShop: [],
    stateGetBannerGroupShop: [],
    stateGetBannerGroupShopV2: [],
    stateEditBannerGroupShopV2: [],
    stateLoginSSO: [],
    stateListGategory: [],
    stateCreateCategory: [],
    stateEditCategory: [],
    stateOldPartner: [],
    stateDeleteCategory: [],
    stateGetListProduct: [],
    stateDetailPosition: [],
    stateDeleteRoom: [],
    stateDelivery: [],
    stateGetDetailProduct: [],
    stateEditProduct: [],
    stateListProductDelivery: [],
    stateCreateProductDelivery: [],
    stateChangeStatusCategory: [],
    stateOrderDeliveryShop: [],
    stateOrderDeliveryOrderDetail: [],
    stateUpdateDateDelivery: [],
    stateDeleteDelivery: [],
    stateListShopTag: [],
    stateCreateShopTag: [],
    stateUpdateShopTag: [],
    stateUpdateShopTagStatus: [],
    stateDeleteShopTag: [],
    stateDeleteShopTagItem: [],
    stateListProductTag: [],
    stateSelectListProductTag: [],
    stateCreateProductTag: [],
    stateUpdateProductTag: [],
    stateGetShopProduct: [],
    stateRegisterOnePlatformToRegisterShop: [],
    stateUploadPDF: [],
    statePreviewSettingQT: [],
    stateAccecptAllProduct: [],
    stateSelectListProductFlashSale: [],
    stateListBundle: [],
    stateCheckStatusDBDL: [],
    stateListBusiness: [],
    stateRequestDBD: [],
    stateCheckVerify: [],
    stateRegOTP: [],
    stateSendOTP: []
  },
  getters: {
    drawChart (state) {
      return state.stateSeries
    },
    dataChart (state) {
      return JSON.stringify(state.setKey)
    },
    chartOption (state) {
      return state.stateMax
    },
    chartColor (state) {
      return state.stateColor
    },
    jsonCopy (state) {
      var keys = JSON.stringify(state.tmpJson).toString()
      var key = '/[]/'
      const JsonS = keys.split(key)
      return JsonS.toString()
    },
    chartDate (state) {
      return state.stateDateTime
    },
    topProduct (state) {
      return state.stateNewGetDashboard.top_product
    },
    topBuyer (state) {
      return state.stateNewGetDashboard.top_user
    },
    // allReturn (state) {
    //   return state.stateListRefundSeller
    // },
    // approveReturn (state) {
    //   return state.stateListRefundSeller.data.waiting.filter(approve => {
    //     return approve.
    //   }),
    allReturn: (state) => (status) => {
      return state.stateListRefundSeller.data.waiting.filter(waiting => waiting.status_refund === status)
    },
    filterDate: (state) => (...as) => {
      // console.log('as[0]:', as[0], 'as[1]:', as[1])
      const data = state.stateDashboard.data.filter(date => {
        var myDate = date.date.split('-')
        var newDate = new Date(myDate[2], myDate[1] - 1, myDate[0])
        // console.log('newDate.getTime:', newDate.getTime())
        if (newDate.getTime() >= as[0] && newDate.getTime() <= as[1]) {
          return date
        }
      })
      return data
    },
    groupBy: (state) => (...month) => {
      // var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    }
  },
  mutations: {
    mutationsImportExcel (state, data) {
      state.stateImportExcel = data
    },
    mutationsImportExcelNoJV (state, data) {
      state.stateImportExcelNoJV = data
    },
    mutaionsCategory (state, data) {
      state.stateCategory = data
    },
    // Check User Business
    mutationsCheckUserBusiness (state, data) {
      state.stateCheckUserBusiness = data
    },
    // Shop Detail Page
    mutationsShopDetailPage (state, data) {
      state.stateShopDetailPage = data
    },
    // Create Shop Page
    mutationsCreateSellerShop (state, data) {
      state.stateCreateSellerShop = data
    },
    // Get Shop Data
    mutationsGetShopData (state, data) {
      state.stateShopData = data
    },
    mutationsCreateSettingeSellerShop (state, data) {
      state.stateCreateSettingSellerShop = data
    },
    // delete Product
    mutationsDeleteProduct (state, data) {
      state.stateDeleteProduct = data
    },
    mutationsDeleteBySeller (state, data) {
      state.stateDeleteBySeller = data
    },
    // product select option
    mutationsSelectOption (state, data) {
      state.SelectOption = data
    },
    // update shop address
    mutationsUpdateShop (state, data) {
      state.stateUpdateshopaddress = data
    },
    mutationsgetUpdateShop (state, data) {
      state.stategetUpdateshopaddress = data
    },
    async mutationsDashboard (state, data) {
      var date = await []
      state.stateDashboard = await []
      state.stateDashboard = await data
      var DataValues = await []
      DataValues = await data.data.map(x => {
        var myDate = x.date.split(' ')
        var timeParts = myDate[1].split(':')
        var dateParts = myDate[0].split('-')
        var newDate = new Date(dateParts[2], parseInt(dateParts[1], 10) - 1, dateParts[0], timeParts[0], timeParts[1])
        return [String(newDate.getTime()).substring(0, 5), x.value]
      })
      // var DataValues = data.data.map(x => {
      //   return [String(Date.parse(x.date)), x.value]
      // })
      // console.log('unstate', DataValues)
      date = await new Set(data.data.map(el => {
        return el.date
      }))
      var sum = {}
      for (const x of DataValues) {
        // console.log('C :', sum[c[0]])
        // if (sum[c[0]] === undefined) {
        //   sum[c[0]] = c
        //   console.log('unSum', sum[c[0]])
        // }
        if (undefined === sum[x[0]]) {
          sum[x[0]] = x
          // console.log('undefined = ', sum[x[0]])
        } else {
          sum[x[0]][1] += x[1]
          // console.log('unSum ', sum)
        }
      }
      state.stateTimeline = await Object.keys(sum).map((val) => { return [Number(sum[val][0] + '00000000'), sum[val][1]] })
      state.stateDateTime = await [...date]
      // console.log('unstateDateTime', state.stateTimeline)
    },
    async mutationsDashboard2 (state, data) {
      // console.log('SSmutationsDashboard2', data)
      state.stateDashboard2 = data
    },
    mutationsListRefundSeller (state, data) {
      state.stateListRefundSeller = data
    },
    mutationsApproveRefundSeller (state, data) {
      state.stateApproveRefundSeller = data
    },
    mutationsDetailRefundSeller (state, data) {
      state.stateDetailRefundSeller = data
    },
    mutationsNotiRefundSeller (state, data) {
      state.stateNotiRefundSeller = data
    },
    async mutationsSetChart (state, data = '') {
      // console.log('TEST+3:', data)
      // state.stateSeries = await []
      // state.tmpObj = await []
      // state.stateMax = await []
      // state.setKey = await []
      // var temp = await []
      var temp = await state.stateDashboard
      // console.log('TEST+4:', state.stateDashboard)
      // console.log('TEST+5:', temp)
      for (var o in data) {
        state.tmpObj[o] = data[o]
        state.tmpJson.push(data)
      }
      // console.log('TEST+6:', state.tmpObj)
      // console.log('temp', temp)
      // var Xc = await []
      var Xc = await temp.data.filter((el) => {
        for (const key in state.tmpObj) {
          if (el[key] === undefined || el[key] !== state.tmpObj[key]) {
            return false
          }
        }
        return { ...el }
      })
      // await console.log('TEST+7:', Xc)
      state.stateMax = await Math.max.apply(Math, Xc.map(o => o.value))
      // console.log('TEST+4', Xc)
      // console.log('stateMax', state.stateMax)
      // console.log('Object**', data)
      var val = await {
        name: data.buyer_name,
        data: Xc.map(e => { return Number(e.value) })
      }
      // console.log('TEST+5', val)
      // console.log('LineChar - Val', val)
      // const dataForm = await val
      // console.log('DataForm', val)
      await state.stateSeries.push(val)
      // console.log('LineChar - Seriers', state.stateSeries)
      // await state.setKey.push(data)
      // console.log('TTbefore')
      state.stateDashboard = await { data: Xc }
      // console.log('TTafter', state.stateDashboard)
      eventBus.$emit('selectDataFilter')
    },
    mutationsProductBestSeller (state, data) {
      state.stateProductBestSeller = data
    },
    mutationsUserMostBuyProduct (state, data) {
      state.stateUserMostBuyProduct = data
    },
    mutationsListRefMerchant (state, data) {
      state.stateListRefMerchant = data
    },
    mutationsRefMerchant (state, data) {
      state.stateRefMerchant = data
    },
    mutationsCreateShop (state, data) {
      state.stateCreateShop = data
    },
    mutationsDetailShop (state, data) {
      state.stateDatailShop = data
    },
    mutationsEditShop (state, data) {
      state.stateEditShop = data
    },
    mutationsShowProductShop (state, data) {
      state.stateShowProductShop = data
    },
    mutationsDetailMerchantFromPayment (state, data) {
      state.stateDetailMerchantFromPayment = data
    },
    // Get Review Prodoct
    mutationsGetReviewProduct (state, data) {
      state.stateGetReviewProduct = data
    },
    // Get list Partner
    mutationsGetListPratner (state, data) {
      state.stateGetListPartner = data
    },
    mutationsCheckRequestPartner (state, data) {
      state.stateCheckRequestPartner = data
    },
    // Post Partner new
    mutationsPostPartner (state, data) {
      state.staterequest = data
    },
    //
    mutationsDetailDocumentShop (state, data) {
      state.stateDetailDocumentShop = data
    },
    // seller_partner
    mutationsDetailSellerPartner (state, data) {
      state.stateDetailSellerPartner = data
    },
    // Edit partner
    mutationsEditPartner (state, data) {
      state.stateEditPartner = data
    },
    // Get List Quotation seller
    mutationsListQuotationSeller (state, data) {
      state.stateListQuotationSeller = data
    },
    // list_user_company
    mutationsListUserCompany (state, data) {
      state.stateListUserCompany = data
    },
    mutationsDetailUserCompanyPosition (state, data) {
      state.stateDetailUserCompanyPosition = data
    },
    mutationsCreatePosition (state, data) {
      state.stateCreatePosition = data
    },
    mutationsEditPodition (state, data) {
      state.stateEditPodition = data
    },
    mutationsListCompanyPosition (state, data) {
      state.stateListCompanyPorsition = data
    },
    mutationsSearchUserCompany (state, data) {
      state.stateSearchUserCompany = data
    },
    mutationsDetailUserCompany (state, data) {
      state.stateDetailUserCompany = data
    },
    // Edit Position User Company
    mutationsEditPositionCompany (state, data) {
      state.stateEditPositionCompany = data
    },
    // Create Position User Company
    mutationsCreatePositionCompany (state, data) {
      state.stateCreatePositionCompany = data
    },
    // List Manage buyer
    mutationsList_ManageBuyer (state, data) {
      state.stateList_ManageBuyer = data
    },
    mutationsReport (state, data) {
      state.stateReport = data
    },
    // Detail Manage Buyer
    mutationDetailManageBuyer (state, data) {
      state.stateDetail_ManageBuyer = data
    },
    mutationListAppover (state, data) {
      state.stateList_Appover = data
    },
    mutationAddAppover (state, data) {
      state.stateAdd_Appover = data
    },
    mutationEditAppover (state, data) {
      state.stateEdit_Appover = data
    },
    mutationAppoverQU (state, data) {
      state.stateAppoverQU = data
    },
    mutationAppoverQT (state, data) {
      state.stateAppoverQT = data
    },
    mutationConfirmAppoverQT (state, data) {
      state.stateConfirmAppoverQT = data
    },
    mutationBuyerUpdateStatusQT (state, data) {
      state.stateBuyerUpdateStatusQT = data
    },
    mutationCancelAppoverQT (state, data) {
      state.stateCancelAppoverQT = data
    },
    mutationCreateJSON_QT (state, data) {
      state.stateCreateJSON_QT = data
    },
    // (YaPalm)List Order Credit Term
    mutationsListOrderCreditTerm (state, data) {
      state.stateListOrderCreditTerm = data
    },
    // (YaPalm)List Credit Term
    mutationsListCreditTerm (state, data) {
      state.stateListCreditTerm = data
    },
    // (YaPalm)List Credit Term
    mutationsUploadInvoiceCreditTerm (state, data) {
      state.stateUploadInvoiceCreditTerm = data
    },
    // (YaPalm)List Request New Credit Term
    mutationsListRequestChangeTermBySellerShop (state, data) {
      state.stateListRequestChangeTermBySellerShop = data
    },
    // (YaPalm)List Request New Credit Term
    mutationsUpdateRequestCreditTerm (state, data) {
      state.stateUpdateRequestCreditTerm = data
    },
    // (Ya)List Special Price Seller
    mutationsListSpecialPriceSeller (state, data) {
      state.stateListSpecialPriceSeller = data
    },
    // (Ya)Detail Special Price Seller
    mutationsDetailSpecialPrice (state, data) {
      state.stateDetailSpecialPrice = data
    },
    // (Non) New Detail Special Price Seller
    mutationsDetailSpecialPriceSeller (state, data) {
      state.stateDetailSpecialPriceSeller = data
    },
    // (Ya)Approve Special Price Seller
    mutationsApproveSpecialPrice (state, data) {
      state.stateApproveSpecialPrice = data
    },
    mutationsCalculateSpecialPrice (state, data) {
      state.stateCalculateSpecialPrice = data
      // console.log('mutation pass')
    },
    mutationsEditSpecialPrice (state, data) {
      state.stateEditSpecialPrice = data
    },
    // (Ya)CreateMerchant
    mutationsCheckMerchant (state, data) {
      state.stateCheckMerchant = data
    },
    mutationsCreateMerchant (state, data) {
      state.stateCreateMerchant = data
    },
    mutatonCreateUpdatePositionInShop (state, data) {
      state.stateCreateUpdatePositionInShop = data
    },
    mutatonGetPositionDetailInShop (state, data) {
      state.stateGetPositionDetailInShop = data
    },
    mutatonListPositionInShop (state, data) {
      state.stateListPositionInShop = data
    },
    mutatonManageUserWithRoleInShop (state, data) {
      state.stateManageUserWithRoleInShop = data
    },
    // shop coupon
    mutatonListShopCoupon (state, data) {
      state.stateListShopCoupon = data
    },
    mutatonDetailShopCoupon (state, data) {
      state.stateDetailShopCoupon = data
    },
    mutatonDeleteShopCoupon (state, data) {
      state.stateDeleteShopCoupon = data
    },
    mutationCreateShopCoupon (state, data) {
      state.stateCreateShopCoupon = data
    },
    mutationSearchCoupon (state, data) {
      state.stateSearchCoupon = data
    },
    mutationEditShopCoupon (state, data) {
      state.stateEditShopCoupon = data
    },
    // Credit Term Revenue
    mutationDetailMerchantCrterm (state, data) {
      state.stateDetailMerchantCrtermFromPayment = data
    },
    mutationRefShareCreditTerm (state, data) {
      state.stateRefShareCreditTermToMerchant = data
    },
    mutationNewGetDashboard (state, data) {
      state.stateNewGetDashboard = data
    },
    mutationUpdatePositionStatus (state, data) {
      state.stateUpdatePositionStatus = data
    },
    mutationsNewRefMerchant (state, data) {
      state.stateNewRefMerchant = data
    },
    mutationsListAllShop (state, data) {
      state.stateListAllShop = data
    },
    mutationsNewListQTSeller (state, data) {
      state.stateNewListQTSeller = data
    },
    mutationsNewListQTSellerV2 (state, data) {
      state.stateNewListQTSellerV2 = data
    },
    mutationsNewDetailQTSeller (state, data) {
      state.stateNewDetailQTSeller = data
    },
    mutationsListIncludeQT (state, data) {
      state.stateListIncludeQT = data
    },
    mutationsGetQTDashBoard (state, data) {
      state.stateGetQTDashBoard = data
    },
    mutationsGetSumTrendDashBoard (state, data) {
      state.stateGetSumTrendDashBoard = data
    },
    mutationsGetSumDocQTDashBoard (state, data) {
      state.stateGetSumDocQTDashBoard = data
    },
    mutationsGetSumDocSODashBoard (state, data) {
      state.stateGetSumDocSODashBoard = data
    },
    mutationsGetSumDocPRDashBoard (state, data) {
      state.stateGetSumDocPRDashBoard = data
    },
    mutationsGetSumDocPODashBoard (state, data) {
      state.stateGetSumDocPODashBoard = data
    },
    mutationsGetSumTopFiveQTDashBoard (state, data) {
      state.stateGetSumTopFiveQTDashBoard = data
    },
    mutationUploadToS3 (state, data) {
      state.stateUploadToS3 = data
    },
    mutationGetCategoryShopList (state, data) {
      state.stateGetCategoryShopList = data
    },
    mutationGetSaleOrder (state, data) {
      state.stateGetSaleOrder = data
    },
    mutationGetListTierCustomer (state, data) {
      state.stateGetListTierCustomer = data
    },
    mutationGetListCustomerAddress (state, data) {
      state.stateGetListCustomerAddress = data
    },
    mutationGetListCustomerInvAddress (state, data) {
      state.stateGetListCustomerInvAddress = data
    },
    mutationGetListCustomer (state, data) {
      state.stateGetListCustomer = data
    },
    mutationSelectCategoryShopList (state, data) {
      state.stateSelectCategoryShopList = data
    },
    mutationOutOfStockProducts (state, data) {
      state.stateOutOfStockProducts = data
    },
    mutationsAccecptProduct (state, data) {
      state.stateAccecptProduct = data
    },
    mutationsUpdateTrackingNumber (state, data) {
      state.stateUpdateTrackingNumber = data
    },
    mutationsGenerateShortenlink (state, data) {
      state.stateGenerateShortenlink = data
    },
    mutationsCheckSKU (state, data) {
      state.stateCheckSKU = data
    },
    mutationsCalPriceVat (state, data) {
      state.stateCalPriceVat = data
    },
    mutationsCountSummaryProduct (state, data) {
      state.stateCountSummaryProduct = data
    },
    mutationsListCoupon (state, data) {
      state.stateListCoupon = data
    },
    mutationsCollectCoupon (state, data) {
      state.stateCollectCoupon = data
    },
    mutationsListBank (state, data) {
      state.stateListBank = data
    },
    mutationImportExcelNoJVSimple (state, data) {
      state.stateImportExcelNoJVSimple = data
    },
    mutationGetProvince (state, data) {
      state.stateGetProvince = data
    },
    mutationsUpdateSellerBot (state, data) {
      state.stateUpdateSellerBot = data
    },
    mutationsListAttorneyShop (state, data) {
      state.stateListAttorneyShop = data
    },
    mutationsAddAttorney (state, data) {
      state.stateAddAttorney = data
    },
    mutationsGetAttorneyUserDetail (state, data) {
      state.stateGetAttorneyUserDetail = data
    },
    mutationsAttorneyUpdateAndConfrime (state, data) {
      state.stateAttorneyUpdateAndConfrime = data
    },
    mutationsListReciver (state, data) {
      state.stateListReciver = data
    },
    mutationsSetActiveAttorney (state, data) {
      state.stateSetActiveAttorney = data
    },
    mutationsUploadFDAFile (state, data) {
      state.stateUploadFDAFile = data
    },
    mutationsListDocumentsSellerShop (state, data) {
      state.stateListDocumentsSellerShop = data
    },
    mutationsAddProductERP (state, data) {
      state.stateAddProductERP = data
    },
    mutationsSetServiceKeyERP (state, data) {
      state.stateSetServiceKeyERP = data
    },
    mutationsUpdateServiceKeyERP (state, data) {
      state.stateUpdateServiceKeyERP = data
    },
    mutationsUpdateStatusServiceKeyERP (state, data) {
      state.stateUpdateStatusServiceKeyERP = data
    },
    mutationsDetailServiceKeyERP (state, data) {
      state.stateDetailServiceKeyERP = data
    },
    mutationsListSyncShopErp (state, data) {
      state.stateListSyncShopErp = data
    },
    mutationsSetSyncShopErp (state, data) {
      state.stateSetSyncShopErp = data
    },
    mutationsUpdateStockErp (state, data) {
      state.stateUpdateStockErp = data
    },
    mutationsDashboardWDshopOrderHistory (state, data) {
      state.stateDashboardWDshopOrderHistory = data
    },
    mutationsWithdrawSummary (state, data) {
      state.stateWithdrawSummary = data
    },
    mutationsWithdrawDetails (state, data) {
      state.stateWithdrawDetails = data
    },
    mutationstransactionHistory (state, data) {
      state.statetransactionHistory = data
    },
    mutationstransferShopClick (state, data) {
      state.statetransferShopClick = data
    },
    mutationsSettingQTExternal (state, data) {
      state.stateSettingQTExternal = data
    },
    mutationsDetailSettingQTExternal (state, data) {
      state.stateDetailSettingQTExternal = data
    },
    mutationupdateExcelProducts (state, data) {
      state.stateupdateExcelProducts = data
    },
    mutationsFilterAllGroupShop (state, data) {
      state.stateFilterAllGroupShop = data
    },
    mutationsFilterGroupShop (state, data) {
      state.stateFilterGroupShop = data
    },
    mutationsGetListDetailPartnerShop (state, data) {
      state.stateGetListDetailPartnerShop = data
    },
    mutationsUpdateStatusAfterBuyPartner (state, data) {
      state.stateUpdateStatusAfterBuyPartner = data
    },
    mutationsListInterestedPartner (state, data) {
      state.stateListInterestedPartner = data
    },
    mutationsApproveSlipPayment (state, data) {
      state.stateApproveSlipPayment = data
    },
    mutationsUploadSlipLotus (state, data) {
      state.stateUploadSlipLotus = data
    },
    mutationsAddwarehouse (state, data) {
      state.stateAddwarehouse = data
    },
    mutationsSyncWarehouse (state, data) {
      state.stateSyncWarehouse = data
    },
    mutationsCustomGroupShop (state, data) {
      state.stateCustomGroupShop = data
    },
    mutationsGetCustomGroupShop (state, data) {
      state.stateGetCustomGroupShop = data
    },
    mutationsActiveCustomGroupShop (state, data) {
      state.stateActiveCustomGroupShop = data
    },
    mutationsAccountDetailShop (state, data) {
      state.stateAccountDetailShop = data
    },
    mutationsAccountDetailShopUpdate (state, data) {
      state.stateAccountDetailShopUpdate = data
    },
    mutationsCreateAccountShop (state, data) {
      state.stateCreateAccountShop = data
    },
    mutationsChangeStatusProduct (state, data) {
      state.stateChangeStatusProduct = data
    },
    mutationsPayWithCreditCard (state, data) {
      state.statePayWithCreditCard = data
    },
    mutationsPayWithQRCode (state, data) {
      state.statePayWithQRCode = data
    },
    mutationsCheckResultQRCodeMarketplace (state, data) {
      state.stateCheckResultQRCodeMarketplace = data
    },
    mutationsGetPackageList (state, data) {
      state.stateGetPackageList = data
    },
    mutationsAddUserManual (state, data) {
      state.stateAddUserManual = data
    },
    mutationsDeleteUserManual (state, data) {
      state.stateDeleteUserManual = data
    },
    mutationsDetailUserManual (state, data) {
      state.stateDetailUserManual = data
    },
    mutationsEditShippingSeller (state, data) {
      state.stateEditShippingSeller = data
    },
    mutationsChangeStatusShop (state, data) {
      state.stateChangeStatusShop = data
    },
    mutationsChangePartnerShow (state, data) {
      state.stateChangePartnerShow = data
    },
    mutationsTypeGroupShop (state, data) {
      state.stateTypeGroupShop = data
    },
    mutationsEditBannerGroupShop (state, data) {
      state.stateEditBannerGroupShop = data
    },
    mutationsGetBannerGroupShop (state, data) {
      state.stateGetBannerGroupShop = data
    },
    mutationsEditBannerGroupShopV2 (state, data) {
      state.stateEditBannerGroupShopV2 = data
    },
    mutationsGetBannerGroupShopV2 (state, data) {
      state.stateGetBannerGroupShopV2 = data
    },
    mutationsLoginSSO (state, data) {
      state.stateLoginSSO = data
    },
    mutationsListGategory (state, data) {
      state.stateListGategory = data
    },
    mutationsCreateCategory (state, data) {
      state.stateCreateCategory = data
    },
    mutationsEditCategory (state, data) {
      state.stateEditCategory = data
    },
    mutationsOldPartner (state, data) {
      state.stateOldPartner = data
    },
    mutationsDeleteCategory (state, data) {
      state.stateDeleteCategory = data
    },
    mutationsGetListProduct (state, data) {
      state.stateGetListProduct = data
    },
    mutationsDetailPosition (state, data) {
      state.stateDetailPosition = data
    },
    mutationsDeleteRoom (state, data) {
      state.stateDeleteRoom = data
    },
    mutationsDelivery (state, data) {
      state.stateDelivery = data
    },
    mutationsGetDetailProduct (state, data) {
      state.stateGetDetailProduct = data
    },
    mutationsEditProduct (state, data) {
      state.stateEditProduct = data
    },
    mutationsListProductDelivery (state, data) {
      state.stateListProductDelivery = data
    },
    mutationsCreateProductDelivery (state, data) {
      state.stateCreateProductDelivery = data
    },
    mutationsChangeStatusCategory (state, data) {
      state.stateChangeStatusCategory = data
    },
    mutationsOrderDeliveryShop (state, data) {
      state.stateOrderDeliveryShop = data
    },
    mutationsOrderDeliveryOrderDetail (state, data) {
      state.stateOrderDeliveryOrderDetail = data
    },
    mutationsUpdateDateDelivery (state, data) {
      state.stateUpdateDateDelivery = data
    },
    mutationsDeleteDelivery (state, data) {
      state.stateDeleteDelivery = data
    },
    mutationsApprovePRDocument (state, data) {
      state.stateApprovePRDocument = data
    },
    mutationsListShopTag (state, data) {
      state.stateListShopTag = data
    },
    mutationsCreateShopTag (state, data) {
      state.stateCreateShopTag = data
    },
    mutationsUpdateShopTag (state, data) {
      state.stateUpdateShopTag = data
    },
    mutationsUpdateShopTagStatus (state, data) {
      state.stateUpdateShopTagStatus = data
    },
    mutationsDeleteShopTag (state, data) {
      state.stateDeleteShopTag = data
    },
    mutationsDeleteShopTagItem (state, data) {
      state.stateDeleteShopTagItem = data
    },
    mutationsListProductTag (state, data) {
      state.stateListProductTag = data
    },
    mutationsSelectListProductTag (state, data) {
      state.stateSelectListProductTag = data
    },
    mutationsCreateProductTag (state, data) {
      state.stateCreateProductTag = data
    },
    mutationsUpdateProductTag (state, data) {
      state.stateUpdateProductTag = data
    },
    mutationsGetShopProduct (state, data) {
      state.stateGetShopProduct = data
    },
    mutationsRegisterOnePlatformToRegisterShop (state, data) {
      state.stateRegisterOnePlatformToRegisterShop = data
    },
    mutationsUploadPDF (state, data) {
      state.stateUploadPDF = data
    },
    mutationsPreviewSettingQT (state, data) {
      state.statePreviewSettingQT = data
    },
    mutationsAccecptAllProduct (state, data) {
      state.stateAccecptAllProduct = data
    },
    mutationsSelectListProductFlashSale (state, data) {
      state.stateSelectListProductFlashSale = data
    },
    mutationsListBundle (state, data) {
      state.stateListBundle = data
    },
    mutationsCheckStatusDBDL (state, data) {
      state.stateCheckStatusDBDL = data
    },
    mutationsListBusiness (state, data) {
      state.stateListBusiness = data
    },
    mutationsRequestDBD (state, data) {
      state.stateRequestDBD = data
    },
    mutationsCheckVerify (state, data) {
      state.stateCheckVerify = data
    },
    mutationsRegOTP (state, data) {
      state.stateRegOTP = data
    },
    mutationsSendOTP (state, data) {
      state.stateSendOTP = data
    }
  },
  actions: {
    async actionImportExcel (context, access) {
      const response = await AxiosShop.ImportExcel(access)
      await context.commit('mutationsImportExcel', response)
    },
    async actionImportExcelNoJV (context, access) {
      const response = await AxiosShop.ImportExcelNoJV(access)
      await context.commit('mutationsImportExcelNoJV', response)
    },
    async actionCategory (context) {
      const response = await AxiosShop.getCategory()
      await context.commit('mutaionsCategory', response)
    },
    // check User Business
    async actionsCheckUserBusiness (context) {
      const responseData = await AxiosShop.CheckUserBusiness()
      await context.commit('mutationsCheckUserBusiness', responseData)
    },
    // Shop Detail Page
    async actionsShopDetailPage (context, access) {
      const responseData = await AxiosShop.GetShopDetailPage(access)
      await context.commit('mutationsShopDetailPage', responseData)
    },
    // Create Shop Page
    async actionCreateSellerShop (context, access) {
      const responseData = await AxiosShop.CreateSellerShop(access)
      await context.commit('mutationsCreateSellerShop', responseData)
    },
    // Get Shop Data
    async actionsGetShopData (context, access) {
      const responseShopData = await AxiosShop.GetSellerShop(access)
      await context.commit('mutationsGetShopData', responseShopData)
    },
    async actionCreateSettingSellerShop (context, access) {
      const responseData = await AxiosShop.CreateSettingSellerShop(access)
      await context.commit('mutationsCreateSettingeSellerShop', responseData)
    },
    // delete Product
    async actionsDeleteProduct (context, access) {
      var responseData = await AxiosShop.deleteProduct(access)
      await context.commit('mutationsDeleteProduct', responseData)
    },
    async actionsDeleteBySeller (context, access) {
      var responseData = await AxiosShop.DeleteBySeller(access)
      await context.commit('mutationsDeleteBySeller', responseData)
    },
    // product select option
    async actionsSelectOption (context, access) {
      var responseData = await AxiosShop.GetSelectOption(access)
      await context.commit('mutationsSelectOption', responseData)
    },
    // Update Shop address
    async actionsUpdateShopAddress (context, access) {
      var responseData = await AxiosShop.UpShopaddresss(access)
      await context.commit('mutationsUpdateShop', responseData)
    },
    async actionsgetUpdateShopAddress (context, access) {
      var responseData = await AxiosShop.GetUpShopaddresss(access)
      await context.commit('mutationsgetUpdateShop', responseData)
    },
    async actionsDashboard (context, access) {
      // console.log('TEST++Access', access)
      var responseData = await AxiosShop.Dashboard(access)
      // console.log('TEST++responseData', responseData)
      await context.commit('mutationsDashboard', responseData)
    },
    async actionsDashboard2 (context, access) {
      // console.log('SSactionsDashboard2', access)
      var responseData = await AxiosShop.Dashboard2(access)
      await context.commit('mutationsDashboard2', responseData)
    },
    async actionsSetChart (context, access) {
      // console.log('TEST+2', access)
      await context.commit('mutationsSetChart', access)
    },
    async actionsListRefundSeller (context, access) {
      var responseData = await AxiosShop.PostListRefundSeller(access)
      await context.commit('mutationsListRefundSeller', responseData)
    },
    async actionsDetailRefundSeller (context, access) {
      var responseData = await AxiosShop.PostDetailRefundSeller(access)
      await context.commit('mutationsDetailRefundSeller', responseData)
    },
    async actionsNotiRefundSeller (context, access) {
      var responseData = await AxiosShop.PostNotiRefundSeller(access)
      await context.commit('mutationsNotiRefundSeller', responseData)
    },
    async actionsProductBestSeller (context, access) {
      var responseData = await AxiosShop.GetProductBestSeller(access)
      await context.commit('mutationsProductBestSeller', responseData)
    },
    async actionsUserMostBuyProduct (context, access) {
      var responseData = await AxiosShop.GetUserMostBuyProduct(access)
      await context.commit('mutationsUserMostBuyProduct', responseData)
    },
    async actionsListRefMerchant (context, access) {
      var responseData = await AxiosShop.GetListRefMerchant(access)
      await context.commit('mutationsListRefMerchant', responseData)
    },
    async actionsRefMerchant (context, access) {
      var responseData = await AxiosShop.GetRefMerchant(access)
      await context.commit('mutationsRefMerchant', responseData)
    },
    async actionsNewRefMerchant (context, access) {
      var responseData = await AxiosShop.GetNewRefMerchant(access)
      await context.commit('mutationsNewRefMerchant', responseData)
    },
    async actionsCreateShop (context, access) {
      var responseData = await AxiosShop.CreateShop(access)
      await context.commit('mutationsCreateShop', responseData)
    },
    async actionDetailShop (context, access) {
      var responseData = await AxiosShop.DetailShop(access)
      await context.commit('mutationsDetailShop', responseData)
    },
    async actionEditShop (context, access) {
      var responseData = await AxiosShop.EditShop(access)
      await context.commit('mutationsEditShop', responseData)
    },
    async actionShowProductShop (context, access) {
      var responseData = await AxiosShop.ShowProductShop(access)
      await context.commit('mutationsShowProductShop', responseData)
    },
    async actionDetailMerchantFromPayment (context, access) {
      var responseData = await AxiosShop.DetailMerchantFromPayment(access)
      await context.commit('mutationsDetailMerchantFromPayment', responseData)
    },
    // Get Review Prodoct
    async actionsGetReviewProduct (context, access) {
      var responseData = await AxiosShop.GetReviewProduct(access)
      await context.commit('mutationsGetReviewProduct', responseData)
    },
    // List Partner
    async actionsListPartnerBuyer (context, access) {
      var responseData = await AxiosShop.GetListPartnerBuyer(access)
      await context.commit('mutationsGetListPratner', responseData)
    },
    // Check partner
    async actionsCheckRequestPartner (context, access) {
      var responseData = await AxiosShop.CheckRequestPartner(access)
      await context.commit('mutationsCheckRequestPartner', responseData)
    },
    // Post new request partner
    async actionsPostPartnerBuyer (context, access) {
      var responseData = await AxiosShop.PostRequestPartnerBuyer(access)
      await context.commit('mutationsPostPartner', responseData)
    },
    // detail_document_shop
    async actionsDetailDocumentShop (context, access) {
      var responseData = await AxiosShop.DetailDocumentShop(access)
      await context.commit('mutationsDetailDocumentShop', responseData)
    },
    // detail_seller_partner
    async actionsDetailSellerpartner (context, access) {
      var responseData = await AxiosShop.DetailSellerPartner(access)
      await context.commit('mutationsDetailSellerPartner', responseData)
    },
    // edit partner
    async actionsEditPartner (context, access) {
      var responseData = await AxiosShop.EditPartner(access)
      await context.commit('mutationsEditPartner', responseData)
    },
    // List Quotation Seller
    async actionsListQuotationSeller (context, access) {
      var responseData = await AxiosShop.ListQuotationSeller(access)
      await context.commit('mutationsListQuotationSeller', responseData)
    },
    async actionsListUserCompanyPosition (context, access) {
      var responseData = await AxiosShop.ListUserCompany(access)
      await context.commit('mutationsListUserCompany', responseData)
    },
    async actionsDetailUserCompanyPosition (context, access) {
      var responseData = await AxiosShop.DetailUserCompanyPosition(access)
      await context.commit('mutationsDetailUserCompanyPosition', responseData)
    },
    // create business
    async actionsCreatePositon (context, access) {
      var responseData = await AxiosShop.CreatePositon(access)
      await context.commit('mutationsCreatePosition', responseData)
    },
    // Edit Position company
    async avtionsEditPosition (context, access) {
      var responseData = await AxiosShop.EditPosition(access)
      await context.commit('mutationsEditPodition', responseData)
    },
    // List company Position
    async actionsListCompanyPosition (context, access) {
      var responseData = await AxiosShop.ListCompanyPosition(access)
      await context.commit('mutationsListCompanyPosition', responseData)
    },
    // Search User company
    async actionsSearchUsercompany (context, access) {
      var responseData = await AxiosShop.SearchUserCompany(access)
      await context.commit('mutationsSearchUserCompany', responseData)
    },
    // Detail User company
    async actionsDetailUserCompany (context, access) {
      var responseData = await AxiosShop.DetailUserCompany(access)
      await context.commit('mutationsDetailUserCompany', responseData)
    },
    // Edit Position User Company
    async actionsEditPositionCompany (context, access) {
      var responseData = await AxiosShop.EditPositionCompany(access)
      await context.commit('mutationsEditPositionCompany', responseData)
    },
    // Create Position User Company
    async actionCreatePositonUser (context, access) {
      var responseData = await AxiosShop.CreatePositionCompany(access)
      await context.commit('mutationsCreatePositionCompany', responseData)
    },
    // List Manage buyer
    async actionsList_ManageBuyer (context, access) {
      var responseData = await AxiosShop.ListManageBuyer(access)
      await context.commit('mutationsList_ManageBuyer', responseData)
    },
    async actionsReport (context, access) {
      var responseData = await AxiosShop.PostReport(access)
      await context.commit('mutationsReport', responseData)
    },
    async actionsDetail_ManageBuyer (context, access) {
      var responseData = await AxiosShop.DetailManageBuyer(access)
      await context.commit('mutationDetailManageBuyer', responseData)
    },
    // list Apppover
    async actionsList_Appover (context, access) {
      var responseData = await AxiosShop.ListAppover(access)
      await context.commit('mutationListAppover', responseData)
    },
    // add Appover
    async actionAdd_Appover (context, access) {
      var responseData = await AxiosShop.AddAppover(access)
      await context.commit('mutationAddAppover', responseData)
    },
    // Edit Appover
    async actionEdit_Appover (context, access) {
      var responseData = await AxiosShop.EditAppover(access)
      await context.commit('mutationEditAppover', responseData)
    },
    // Approve Model  Qu
    async Approve_QU (context, access) {
      var responseData = await AxiosShop.AppoverQU(access)
      await context.commit('mutationAppoverQU', responseData)
    },
    async Approve_QT (context, access) {
      var responseData = await AxiosShop.AppoverQT(access)
      await context.commit('mutationAppoverQT', responseData)
    },
    async Confirm_Approve_QT (context, access) {
      var responseData = await AxiosShop.ConfirmAppoverQT(access)
      await context.commit('mutationConfirmAppoverQT', responseData)
    },
    async BuyerUpdateStatusQT (context, access) {
      var responseData = await AxiosShop.BuyerUpdateStatusQT(access)
      await context.commit('mutationBuyerUpdateStatusQT', responseData)
    },
    async Cancel_Approve_QT (context, access) {
      var responseData = await AxiosShop.CancelAppoverQT(access)
      await context.commit('mutationCancelAppoverQT', responseData)
    },
    async Create_JSON_QT (context, access) {
      var responseData = await AxiosShop.CreateJSON_QT(access)
      await context.commit('mutationCreateJSON_QT', responseData)
    },
    // (YaPalm)Seller List Order Credit Term
    async actionsSellerListOrderCreditTerm (context, access) {
      var response = await AxiosShop.listOrderCreditTerm(access)
      await context.commit('mutationsListOrderCreditTerm', response)
    },
    // (YaPalm)Seller List Creadit Term
    async actionsUploadInvoiceCreditTerm (context, access) {
      const response = await AxiosShop.uploadInvoiceCreditTerm(access)
      await context.commit('mutationsUploadInvoiceCreditTerm', response)
    },
    // (YaPalm)Seller List Creadit Term
    async actionsSellerListCreditTerm (context, access) {
      const response = await AxiosShop.listCreditTerm(access)
      await context.commit('mutationsListCreditTerm', response)
    },
    // (YaPalm)Seller Request New Creadit Term
    async actionsListRequestChangeTermBySellerShop (context, access) {
      const response = await AxiosShop.listRequestChangeTermBySellerShop(access)
      await context.commit('mutationsListRequestChangeTermBySellerShop', response)
    },
    // (YaPalm)Seller Request New Creadit Term
    async actionsUpdateRequestCreditTerm (context, access) {
      const response = await AxiosShop.updateRequestCreditTerm(access)
      await context.commit('mutationsUpdateRequestCreditTerm', response)
    },
    // (Ya)Seller List Special Price Seller
    async actionsListSpecialPriceSeller (context, access) {
      const response = await AxiosShop.listSpecialPriceSeller(access)
      await context.commit('mutationsListSpecialPriceSeller', response)
    },
    // (Ya)Seller Detail Special Price Seller
    async actionsDetailSpecialPrice (context, access) {
      const response = await AxiosShop.detailSpecialPrice(access)
      await context.commit('mutationsDetailSpecialPrice', response)
    },
    // (Ya) New Seller Detail Special Price Seller
    async actionsDetailSpecialPriceSeller (context, access) {
      const response = await AxiosShop.newDetailSpecialPrice(access)
      await context.commit('mutationsDetailSpecialPriceSeller', response)
    },
    // (Ya)Seller Approve Special Price Seller
    async actionsApproveSpecialPrice (context, access) {
      const response = await AxiosShop.approveSpecialPrice(access)
      await context.commit('mutationsApproveSpecialPrice', response)
    },
    // (Ya)Seller Calculate Special Price Seller
    async actionsCalculateSpecialPrice (context, access) {
      const response = await AxiosShop.calculateSpecialPrice(access)
      await context.commit('mutationsCalculateSpecialPrice', response)
      // console.log('action pass')
    },
    // (Ya)Seller Edit Special Price Seller
    async actionsEditSpecialPrice (context, access) {
      const response = await AxiosShop.editSpecialPrice(access)
      await context.commit('mutationsEditSpecialPrice', response)
      // console.log('action pass')
    },
    // (Ya)Merchant
    async actionsCheckMerchant (context, access) {
      const response = await AxiosShop.checkMerchant(access)
      await context.commit('mutationsCheckMerchant', response)
    },
    async actionsCreateMerchant (context, access) {
      const response = await AxiosShop.createMerchant(access)
      await context.commit('mutationsCreateMerchant', response)
    },
    async actionCreateUpdatePositionInShop (context, access) {
      var responseData = await AxiosShop.createUpdatePositionInShop(access)
      await context.commit('mutatonCreateUpdatePositionInShop', responseData)
    },
    async actionGetPositionDetailInShop (context, access) {
      var responseData = await AxiosShop.getPositionDetailInShop(access)
      await context.commit('mutatonGetPositionDetailInShop', responseData)
    },
    async actionListPositionInShop (context, access) {
      // console.log('actionListPositionInShop2')
      var responseData = await AxiosShop.listPositionInShop(access)
      await context.commit('mutatonListPositionInShop', responseData)
    },
    async actionManageUserWithRoleInShop (context, access) {
      var responseData = await AxiosShop.manageUserWithRoleInShop(access)
      await context.commit('mutatonManageUserWithRoleInShop', responseData)
    },
    // shop coupon
    async actionListShopCoupon (context, access) {
      var responseData = await AxiosShop.listShopCoupon(access)
      await context.commit('mutatonListShopCoupon', responseData)
    },
    async actionDetailShopCoupon (context, access) {
      var responseData = await AxiosShop.detailShopCoupon(access)
      await context.commit('mutatonDetailShopCoupon', responseData)
    },
    async actionDeleteShopCoupon (context, access) {
      var responseData = await AxiosShop.deleteShopCoupon(access)
      await context.commit('mutatonDeleteShopCoupon', responseData)
    },
    async actionCreateShopCoupon (context, access) {
      var responseData = await AxiosShop.createShopCoupon(access)
      await context.commit('mutationCreateShopCoupon', responseData)
    },
    async actionSearchCoupon (context, access) {
      var responseData = await AxiosShop.searchCoupon(access)
      await context.commit('mutationSearchCoupon', responseData)
    },
    async actionEditShopCoupon (context, access) {
      var responseData = await AxiosShop.editShopCoupon(access)
      await context.commit('mutationEditShopCoupon', responseData)
    },
    // Credit Term Revenue
    async actionDetailMerchantCrterm (context, access) {
      var responseData = await AxiosShop.DetailMerchantCrterm(access)
      await context.commit('mutationDetailMerchantCrterm', responseData)
    },
    async actionRefShareCreditTerm (context, access) {
      var responseData = await AxiosShop.RefShareCreditTerm(access)
      await context.commit('mutationRefShareCreditTerm', responseData)
    },
    async actionNewGetDashboard (context, access) {
      var responseData = await AxiosShop.NewGetDashboard(access)
      await context.commit('mutationNewGetDashboard', responseData)
    },
    async actionUpdatePositionStatus (context, access) {
      var responseData = await AxiosShop.UpdatePositionStatus(access)
      await context.commit('mutationUpdatePositionStatus', responseData)
    },
    async actionListAllShop (context, access) {
      var responseData = await AxiosShop.GetListAllShop(access)
      await context.commit('mutationsListAllShop', responseData)
    },
    async actionsNewListQTSeller (context, access) {
      var responseData = await AxiosShop.NewListQTSeller(access)
      await context.commit('mutationsNewListQTSeller', responseData)
    },
    async actionsNewListQTSellerV2 (context, access) {
      var responseData = await AxiosShop.NewListQTSellerV2(access)
      await context.commit('mutationsNewListQTSellerV2', responseData)
    },
    async actionsNewDetailQTSeller (context, access) {
      var responseData = await AxiosShop.NewDetailQTSeller(access)
      await context.commit('mutationsNewDetailQTSeller', responseData)
    },
    async actionsListIncludeQT (context, access) {
      var responseData = await AxiosShop.ListIncludeQT(access)
      await context.commit('mutationsListIncludeQT', responseData)
    },
    async actionsGetQTDashBoard (context, access) {
      var responseData = await AxiosShop.GetQTDashBoard(access)
      await context.commit('mutationsGetQTDashBoard', responseData)
    },
    async actionsGetSumTrendDashBoard (context, access) {
      var responseData = await AxiosShop.GetSumTrendDashBoard(access)
      await context.commit('mutationsGetSumTrendDashBoard', responseData)
    },
    async actionsGetSumDocQTDashBoard (context, access) {
      var responseData = await AxiosShop.GetSumDocQTDashBoard(access)
      await context.commit('mutationsGetSumDocQTDashBoard', responseData)
    },
    async actionsGetSumDocSODashBoard (context, access) {
      var responseData = await AxiosShop.GetSumDocSODashBoard(access)
      await context.commit('mutationsGetSumDocSODashBoard', responseData)
    },
    async actionsGetSumDocPRDashBoard (context, access) {
      var responseData = await AxiosShop.GetSumDocPRDashBoard(access)
      await context.commit('mutationsGetSumDocPRDashBoard', responseData)
    },
    async actionsGetSumDocPODashBoard (context, access) {
      var responseData = await AxiosShop.GetSumDocPODashBoard(access)
      await context.commit('mutationsGetSumDocPODashBoard', responseData)
    },
    async actionsGetSumTopFiveQTDashBoard (context, access) {
      var responseData = await AxiosShop.GetSumTopFiveQTDashBoard(access)
      await context.commit('mutationsGetSumTopFiveQTDashBoard', responseData)
    },
    async actionsUploadToS3 (context, access) {
      var responseData = await AxiosShop.UploadToS3(access)
      await context.commit('mutationUploadToS3', responseData)
    },
    async actionsGetCategoryShopList (context, access) {
      var responseData = await AxiosShop.GetCategoryShopList(access)
      await context.commit('mutationGetCategoryShopList', responseData)
    },
    async actionsGetSaleOrder (context, access) {
      var responseData = await AxiosShop.GetSaleOrder(access)
      await context.commit('mutationGetSaleOrder', responseData)
    },
    async actionsGetListTierCustomer (context, access) {
      var responseData = await AxiosShop.GetListTierCustomer(access)
      await context.commit('mutationGetListTierCustomer', responseData)
    },
    async actionsGetListCustomer (context, access) {
      var responseData = await AxiosShop.GetListCustomer(access)
      await context.commit('mutationGetListCustomer', responseData)
    },
    async actionsGetListCustomerAddress (context, access) {
      var responseData = await AxiosShop.GetListCustomerAddress(access)
      await context.commit('mutationGetListCustomerAddress', responseData)
    },
    async actionsGetListCustomerInvAddress (context, access) {
      var responseData = await AxiosShop.GetListCustomerInvAddress(access)
      await context.commit('mutationGetListCustomerInvAddress', responseData)
    },
    async actionsSelectCategoryShopList (context, access) {
      var responseData = await AxiosShop.SelectCategoryShopList(access)
      await context.commit('mutationSelectCategoryShopList', responseData)
    },
    async actionsGetProvince (context, access) {
      var responseData = await AxiosShop.GetProvince(access)
      await context.commit('mutationGetProvince', responseData)
    },
    async actionsOutOfStockProducts (context, access) {
      var responseData = await AxiosShop.OutOfStockProducts(access)
      await context.commit('mutationOutOfStockProducts', responseData)
    },
    async actionsAccecptProduct (context, access) {
      var responseData = await AxiosShop.AccecptProduct(access)
      await context.commit('mutationsAccecptProduct', responseData)
    },
    async actionsUpdateTrackingNumber (context, access) {
      var responseData = await AxiosShop.UpdateTrackingNumber(access)
      await context.commit('mutationsUpdateTrackingNumber', responseData)
    },
    async actionsGenerateShortenlink (context, access) {
      var responseData = await AxiosShop.GenerateShortenlink(access)
      await context.commit('mutationsGenerateShortenlink', responseData)
    },
    async actionsCheckSKU (context, access) {
      var responseData = await AxiosShop.CheckSKU(access)
      await context.commit('mutationsCheckSKU', responseData)
    },
    async actionsCalPriceVat (context, access) {
      var responseData = await AxiosShop.CalPriceVat(access)
      await context.commit('mutationsCalPriceVat', responseData)
    },
    async actionsCountSummaryProduct (context, access) {
      var responseData = await AxiosShop.CountSummaryProduct(access)
      await context.commit('mutationsCountSummaryProduct', responseData)
    },
    async actionListCoupon (context, access) {
      var responseData = await AxiosShop.ListCoupon(access)
      await context.commit('mutationsListCoupon', responseData)
    },
    async actionCollectCoupon (context, access) {
      var responseData = await AxiosShop.CollectCoupon(access)
      await context.commit('mutationsCollectCoupon', responseData)
    },
    async actionsListBank (context) {
      var responseData = await AxiosShop.ListBank()
      await context.commit('mutationsListBank', responseData)
    },
    async actionsImportExcelNoJVSimple (context, data) {
      var responseData = await AxiosShop.ImportExcelNoJVSimple(data)
      await context.commit('mutationImportExcelNoJVSimple', responseData)
    },
    async actionsUpdateSellerBot (context, data) {
      var responseData = await AxiosShop.UpdateSellerBot(data)
      await context.commit('mutationsUpdateSellerBot', responseData)
    },
    async actionListAttorneyShop (context, data) {
      var response = await AxiosShop.ListAttorneyShop(data)
      await context.commit('mutationsListAttorneyShop', response)
    },
    async actionAddAttorney (context, data) {
      var response = await AxiosShop.AddAttorney(data)
      await context.commit('mutationsAddAttorney', response)
    },
    async actionGetAttorneyUserDetail (context, data) {
      var response = await AxiosShop.GetAttorneyUserDetail(data)
      await context.commit('mutationsGetAttorneyUserDetail', response)
    },
    async actionAttorneyUpdateAndConfrime (context, data) {
      var response = await AxiosShop.AttorneyUpdateAndConfrime(data)
      await context.commit('mutationsAttorneyUpdateAndConfrime', response)
    },
    async actionListReciver (context) {
      var response = await AxiosShop.ListReciver()
      await context.commit('mutationsListReciver', response)
    },
    async actionsSetActiveAttorney (context, data) {
      var response = await AxiosShop.SetActiveAttorney(data)
      await context.commit('mutationsSetActiveAttorney', response)
    },
    async actionsUploadFDAFile (context, data) {
      var response = await AxiosShop.UploadFDAFile(data)
      await context.commit('mutationsUploadFDAFile', response)
    },
    async actionsListDocumentsSellerShop (context, data) {
      var response = await AxiosShop.ListDocumentsSellerShop(data)
      await context.commit('mutationsListDocumentsSellerShop', response)
    },
    async actionsApprovePRDocument (context, data) {
      var response = await AxiosShop.ApprovePRDocument(data)
      await context.commit('mutationsApprovePRDocument', response)
    },
    async actionsAddProductERP (context, data) {
      var response = await AxiosShop.AddProductERP(data)
      await context.commit('mutationsAddProductERP', response)
    },
    async actionsSetServiceKeyERP (context, data) {
      var response = await AxiosShop.SetServiceKeyERP(data)
      await context.commit('mutationsSetServiceKeyERP', response)
    },
    async actionsUpdateServiceKeyERP (context, data) {
      var response = await AxiosShop.UpdateServiceKeyERP(data)
      await context.commit('mutationsUpdateServiceKeyERP', response)
    },
    async actionsUpdateStatusServiceKeyERP (context, data) {
      var response = await AxiosShop.UpdateStatusServiceKeyERP(data)
      await context.commit('mutationsUpdateStatusServiceKeyERP', response)
    },
    async actionsDetailServiceKeyERP (context, data) {
      var response = await AxiosShop.DetailServiceKeyERP(data)
      await context.commit('mutationsDetailServiceKeyERP', response)
    },
    async actionsListSyncShopErp (context, data) {
      var response = await AxiosShop.ListSyncShopErp(data)
      await context.commit('mutationsListSyncShopErp', response)
    },
    async actionsSetSyncShopErp (context, data) {
      var response = await AxiosShop.SetSyncShopErp(data)
      await context.commit('mutationsSetSyncShopErp', response)
    },
    async actionsUpdateStockErp (context, data) {
      var response = await AxiosShop.UpdateStockErp(data)
      await context.commit('mutationsUpdateStockErp', response)
    },
    async actionsDashboardWDshopOrderHistory (context, data) {
      var response = await AxiosShop.DashboardWDshopOrderHistory(data)
      await context.commit('mutationsDashboardWDshopOrderHistory', response)
    },
    async actionsWithdrawSummary (context, data) {
      var response = await AxiosShop.WithdrawSummary(data)
      await context.commit('mutationsWithdrawSummary', response)
    },
    async actionsWithdrawDetails (context, data) {
      var response = await AxiosShop.WithdrawDetails(data)
      await context.commit('mutationsWithdrawDetails', response)
    },
    async actionstransactionHistory (context, data) {
      var response = await AxiosShop.transactionHistory(data)
      await context.commit('mutationstransactionHistory', response)
    },
    async actionstransferShopClick (context, data) {
      var response = await AxiosShop.transferShopClick(data)
      await context.commit('mutationstransferShopClick', response)
    },
    async actionsSettingQTExternal (context, data) {
      var response = await AxiosShop.SettingQTExternal(data)
      await context.commit('mutationsSettingQTExternal', response)
    },
    async actionsDetailSettingQTExternal (context, data) {
      var response = await AxiosShop.DetailSettingQTExternal(data)
      await context.commit('mutationsDetailSettingQTExternal', response)
    },
    async actionsupdateExcelProducts (context, data) {
      var response = await AxiosShop.updateExcelProducts(data)
      await context.commit('mutationupdateExcelProducts', response)
    },
    async actionFilterAllGroupShop (context, access) {
      var responseData = await AxiosShop.GetFilterAllGroupShop(access)
      await context.commit('mutationsFilterAllGroupShop', responseData)
    },
    async actionFilterGroupShop (context, access) {
      var responseData = await AxiosShop.GetFilterGroupShop(access)
      await context.commit('mutationsFilterGroupShop', responseData)
    },
    async actionGetListDetailPartnerShop (context, access) {
      var responseData = await AxiosShop.GetListDetailPartnerShop(access)
      await context.commit('mutationsGetListDetailPartnerShop', responseData)
    },
    async actionUpdateStatusAfterBuyPartner (context, access) {
      var responseData = await AxiosShop.UpdateStatusAfterBuyPartner(access)
      await context.commit('mutationsUpdateStatusAfterBuyPartner', responseData)
    },
    async actionListInterestedPartner (context, access) {
      var responseData = await AxiosShop.ListInterestedPartner(access)
      await context.commit('mutationsListInterestedPartner', responseData)
    },
    async actionApproveSlipPayment (context, access) {
      var responseData = await AxiosShop.ApproveSlipPayment(access)
      await context.commit('mutationsApproveSlipPayment', responseData)
    },
    async actionUploadSlipLotus (context, access) {
      var responseData = await AxiosShop.UploadSlipLotus(access)
      await context.commit('mutationsUploadSlipLotus', responseData)
    },
    async actionAddwarehouse (context, access) {
      var responseData = await AxiosShop.Addwarehouse(access)
      await context.commit('mutationsAddwarehouse', responseData)
    },
    async actionSyncWarehouse (context, access) {
      var responseData = await AxiosShop.SyncWarehouse(access)
      await context.commit('mutationsSyncWarehouse', responseData)
    },
    async actionCustomGroupShop (context, access) {
      var responseData = await AxiosShop.CustomGroupShop(access)
      await context.commit('mutationsCustomGroupShop', responseData)
    },
    async actionGetCustomGroupShop (context, access) {
      var responseData = await AxiosShop.GetCustomGroupShop(access)
      await context.commit('mutationsGetCustomGroupShop', responseData)
    },
    async actionActiveCustomGroupShop (context, access) {
      var responseData = await AxiosShop.ActiveCustomGroupShop(access)
      await context.commit('mutationsActiveCustomGroupShop', responseData)
    },
    async actionAccountDetailShop (context, access) {
      var responseData = await AxiosShop.AccountDetailShop(access)
      await context.commit('mutationsAccountDetailShop', responseData)
    },
    async actionAccountDetailShopUpdate (context, access) {
      var responseData = await AxiosShop.AccountDetailShopUpdate(access)
      await context.commit('mutationsAccountDetailShopUpdate', responseData)
    },
    async actionCreateAccountShop (context, access) {
      var responseData = await AxiosShop.CreateAccountShop(access)
      await context.commit('mutationsCreateAccountShop', responseData)
    },
    async actionChangeStatusProduct (context, access) {
      var responseData = await AxiosShop.ChangeStatusProduct(access)
      await context.commit('mutationsChangeStatusProduct', responseData)
    },
    async actionPayWithCreditCard (context, access) {
      var responseData = await AxiosShop.PayWithCreditCard(access)
      await context.commit('mutationsPayWithCreditCard', responseData)
    },
    async actionPayWithQRCode (context, access) {
      var responseData = await AxiosShop.PayWithQRCode(access)
      await context.commit('mutationsPayWithQRCode', responseData)
    },
    async actionCheckResultQRCodeMarketplace (context, access) {
      var responseData = await AxiosShop.CheckResultQRCodeMarketplace(access)
      await context.commit('mutationsCheckResultQRCodeMarketplace', responseData)
    },
    async actionGetPackageList (context, access) {
      var responseData = await AxiosShop.GetPackageList(access)
      await context.commit('mutationsGetPackageList', responseData)
    },
    async actionAddUserManual (context, access) {
      var responseData = await AxiosShop.AddUserManual(access)
      await context.commit('mutationsAddUserManual', responseData)
    },
    async actionDeleteUserManual (context, access) {
      var responseData = await AxiosShop.DeleteUserManual(access)
      await context.commit('mutationsDeleteUserManual', responseData)
    },
    async actionDetailUserManual (context, access) {
      var responseData = await AxiosShop.DetailUserManual(access)
      await context.commit('mutationsDetailUserManual', responseData)
    },
    async actionEditShippingSeller (context, access) {
      var responseData = await AxiosShop.EditShippingSeller(access)
      await context.commit('mutationsEditShippingSeller', responseData)
    },
    async actionChangeStatusShop (context, access) {
      var responseData = await AxiosShop.ChangeStatusShop(access)
      await context.commit('mutationsChangeStatusShop', responseData)
    },
    async actionChangePartnerShow (context, access) {
      var responseData = await AxiosShop.ChangePartnerShow(access)
      await context.commit('mutationsChangePartnerShow', responseData)
    },
    async actionTypeGroupShop (context, access) {
      var responseData = await AxiosShop.TypeGroupShop(access)
      await context.commit('mutationsTypeGroupShop', responseData)
    },
    async actionEditBannerGroupShop (context, access) {
      var responseData = await AxiosShop.EditBannerGroupShop(access)
      await context.commit('mutationsEditBannerGroupShop', responseData)
    },
    async actionGetBannerGroupShop (context, access) {
      var responseData = await AxiosShop.GetBannerGroupShop(access)
      await context.commit('mutationsGetBannerGroupShop', responseData)
    },
    async actionEditBannerGroupShopV2 (context, access) {
      var responseData = await AxiosShop.EditBannerGroupShopV2(access)
      await context.commit('mutationsEditBannerGroupShopV2', responseData)
    },
    async actionGetBannerGroupShopV2 (context, access) {
      var responseData = await AxiosShop.GetBannerGroupShopV2(access)
      await context.commit('mutationsGetBannerGroupShopV2', responseData)
    },
    async actionGetAllRoom (context, access) {
      var responseData = await AxiosShop.GetAllRoom(access)
      await context.commit('mutationsGetAllRoom', responseData)
    },
    async actionLoginSSO (context, access) {
      var responseData = await AxiosShop.LoginSSO(access)
      await context.commit('mutationsLoginSSO', responseData)
    },
    async actionListGategory (context, access) {
      var responseData = await AxiosShop.ListGategory(access)
      await context.commit('mutationsListGategory', responseData)
    },
    async actionCreateCategory (context, access) {
      var responseData = await AxiosShop.CreateCategory(access)
      await context.commit('mutationsCreateCategory', responseData)
    },
    async actionEditCategory (context, access) {
      var responseData = await AxiosShop.EditCategory(access)
      await context.commit('mutationsEditCategory', responseData)
    },
    async actionOldPartner (context, access) {
      var responseData = await AxiosShop.OldPartner(access)
      await context.commit('mutationsOldPartner', responseData)
    },
    async actionDeleteCategory (context, access) {
      var responseData = await AxiosShop.DeleteCategory(access)
      await context.commit('mutationsDeleteCategory', responseData)
    },
    async actionGetListProduct (context, access) {
      var responseData = await AxiosShop.GetListProduct(access)
      await context.commit('mutationsGetListProduct', responseData)
    },
    async actionDetailPosition (context, access) {
      var responseData = await AxiosShop.DetailPosition(access)
      await context.commit('mutationsDetailPosition', responseData)
    },
    async actionDeleteRoom (context, access) {
      var responseData = await AxiosShop.DeleteRoom(access)
      await context.commit('mutationsDeleteRoom', responseData)
    },
    async actionDelivery (context, access) {
      var responseData = await AxiosShop.Delivery(access)
      await context.commit('mutationsDelivery', responseData)
    },
    async actionGetDetailProduct (context, access) {
      var responseData = await AxiosShop.GetDetailProduct(access)
      await context.commit('mutationsGetDetailProduct', responseData)
    },
    async actionEditProduct (context, access) {
      var responseData = await AxiosShop.EditProduct(access)
      await context.commit('mutationsEditProduct', responseData)
    },
    async actionListProductDelivery (context, access) {
      var responseData = await AxiosShop.ListProductDelivery(access)
      await context.commit('mutationsListProductDelivery', responseData)
    },
    async actionCreateProductDelivery (context, access) {
      var responseData = await AxiosShop.CreateProductDelivery(access)
      await context.commit('mutationsCreateProductDelivery', responseData)
    },
    async actionChangeStatusCategory (context, access) {
      var responseData = await AxiosShop.ChangeStatusCategory(access)
      await context.commit('mutationsChangeStatusCategory', responseData)
    },
    async actionOrderDeliveryShop (context, access) {
      var responseData = await AxiosShop.OrderDeliveryShop(access)
      await context.commit('mutationsOrderDeliveryShop', responseData)
    },
    async actionOrderDeliveryOrderDetail (context, access) {
      var responseData = await AxiosShop.OrderDeliveryOrderDetail(access)
      await context.commit('mutationsOrderDeliveryOrderDetail', responseData)
    },
    async actionUpdateDateDelivery (context, access) {
      var responseData = await AxiosShop.UpdateDateDelivery(access)
      await context.commit('mutationsUpdateDateDelivery', responseData)
    },
    async actionDeleteDelivery (context, access) {
      var responseData = await AxiosShop.DeleteDelivery(access)
      await context.commit('mutationsDeleteDelivery', responseData)
    },
    async actionListShopTag (context, access) {
      var responseData = await AxiosShop.ListShopTag(access)
      await context.commit('mutationsListShopTag', responseData)
    },
    async actionCreateShopTag (context, access) {
      var responseData = await AxiosShop.CreateShopTag(access)
      await context.commit('mutationsCreateShopTag', responseData)
    },
    async actionUpdateShopTag (context, access) {
      var responseData = await AxiosShop.UpdateShopTag(access)
      await context.commit('mutationsUpdateShopTag', responseData)
    },
    async actionUpdateShopTagStatus (context, access) {
      var responseData = await AxiosShop.UpdateShopTagStatus(access)
      await context.commit('mutationsUpdateShopTagStatus', responseData)
    },
    async actionDeleteShopTag (context, access) {
      var responseData = await AxiosShop.DeleteShopTag(access)
      await context.commit('mutationsDeleteShopTag', responseData)
    },
    async actionDeleteShopTagItem (context, access) {
      var responseData = await AxiosShop.DeleteShopTagItem(access)
      await context.commit('mutationsDeleteShopTagItem', responseData)
    },
    async actionListProductTag (context, access) {
      var responseData = await AxiosShop.ListProductTag(access)
      await context.commit('mutationsListProductTag', responseData)
    },
    async actionSelectListProductTag (context, access) {
      var responseData = await AxiosShop.SelectListProductTag(access)
      await context.commit('mutationsSelectListProductTag', responseData)
    },
    async actionCreateProductTag (context, access) {
      var responseData = await AxiosShop.CreateProductTag(access)
      await context.commit('mutationsCreateProductTag', responseData)
    },
    async actionUpdateProductTag (context, access) {
      var responseData = await AxiosShop.UpdateProductTag(access)
      await context.commit('mutationsUpdateProductTag', responseData)
    },
    async actionGetShopProduct (context, access) {
      var responseData = await AxiosShop.GetShopProduct(access)
      await context.commit('mutationsGetShopProduct', responseData)
    },
    async actionRegisterOnePlatformToRegisterShop (context, access) {
      var responseData = await AxiosShop.RegisterOnePlatformToRegisterShop(access)
      await context.commit('mutationsRegisterOnePlatformToRegisterShop', responseData)
    },
    async actionUploadPDF (context, access) {
      var responseData = await AxiosShop.UploadPDF(access)
      await context.commit('mutationsUploadPDF', responseData)
    },
    async actionPreviewSettingQT (context, access) {
      var responseData = await AxiosShop.PreviewSettingQT(access)
      await context.commit('mutationsPreviewSettingQT', responseData)
    },
    async actionAccecptAllProduct (context, access) {
      var responseData = await AxiosShop.AccecptAllProduct(access)
      await context.commit('mutationsAccecptAllProduct', responseData)
    },
    async actionSelectListProductFlashSale (context, access) {
      var responseData = await AxiosShop.SelectListProductFlashSale(access)
      await context.commit('mutationsSelectListProductFlashSale', responseData)
    },
    async actionListBundle (context, access) {
      var responseData = await AxiosShop.ListBundle(access)
      await context.commit('mutationsListBundle', responseData)
    },
    async actionCheckStatusDBDL (context, access) {
      var responseData = await AxiosShop.CheckStatusDBDL(access)
      await context.commit('mutationsCheckStatusDBDL', responseData)
    },
    async actionListBusiness (context, access) {
      var responseData = await AxiosShop.ListBusiness(access)
      await context.commit('mutationsListBusiness', responseData)
    },
    async actionRequestDBD (context, access) {
      var responseData = await AxiosShop.RequestDBD(access)
      await context.commit('mutationsRequestDBD', responseData)
    },
    async actionCheckVerify (context, access) {
      var responseData = await AxiosShop.CheckVerify(access)
      await context.commit('mutationsCheckVerify', responseData)
    },
    async actionRegOTP (context, access) {
      var responseData = await AxiosShop.RegOTP(access)
      await context.commit('mutationsRegOTP', responseData)
    },
    async actionSendOTP (context, access) {
      var responseData = await AxiosShop.SendOTP(access)
      await context.commit('mutationsSendOTP', responseData)
    }
  }
}
export default ModuleShop
