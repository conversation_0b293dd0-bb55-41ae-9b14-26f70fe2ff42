<template>
  <div class="page elevation-0">
    <div>
      <Header :OrderDetailProp="OrderDetailProp"/>
    </div>
    <div>
      <v-row dense>
        <v-col cols="12" md="12">
          <v-card class="elevation-0">
            <v-row dense>
              <v-col cols="12" md="12" class="pa-0">
                <Body :OrderDetailProp="OrderDetailProp"/>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <Summary :OrderDetailProp="OrderDetailProp"/>
      </v-row>
    </div>
    <v-btn v-if="this.printvisible" @click="printpo()" color="green lighten-1" dark fab bottom right fixed><v-icon>mdi-printer</v-icon></v-btn>
  </div>
</template>
<script>
export default {
  components: {
    Header: () => import('@/components/Quotation/Header'),
    Body: () => import('@/components/Quotation/Body'),
    Summary: () => import('@/components/Quotation/Summary')
  },
  data: () => ({
    OrderDetailProp: [],
    printvisible: true
  }),
  async created () {
    this.$EventBus.$emit('getPath')
    // await this.$store.dispatch('actionListOrderBuyer')
    this.OrderDetailProp = await this.$store.state.ModuleOrder.stateOrderDetailData.data[0]
    // console.log('OrderDetailProp', this.OrderDetailProp)
    // this.orderDetail()
  },
  methods: {
    printpo () {
      setTimeout(() => {
        window.print()
      }, 500)
      this.printvisible = false
      setTimeout(() => {
        this.printvisible = true
      }, 2000)
    }
  }
}
</script>
<style scoped>
.page {
  width: 21cm;
  height: 29.7cm;
  padding: 1cm;
  margin: 0cm auto;
  border: 1px #d3d3d3 solid;
  border-radius: 5px;
  background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
@page {
  size: A4;
  margin: 0;
}
@page {
  size: A4;
  margin: 0;
}
@media print {
  .page {
    margin: 0;
    box-shadow: 0;
  }
  * {
    -webkit-print-color-adjust: exact;
  }
  .btnPrint {
    display: none;
  }
}
</style>
