<template>
 <div>
   <v-breadcrumbs :items="RoleUser == 'sale_order' ? itemsSale : items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
     <template v-slot:divider>
       <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
     </template>
     <template v-slot:item="{ item }">
       <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
         <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }">
           {{ item.text }}
         </span>
       </v-breadcrumbs-item>
     </template>
   </v-breadcrumbs>
   <!-- <v-overlay :value="overlay2">
     <v-progress-circular indeterminate size="64"></v-progress-circular>
   </v-overlay> -->
    <v-container>
      <v-row class="mt-4" v-if="itemTag.length === 0">
        <v-col cols="5" md="5" class="pa-0" v-if="!MobileSize && !IpadSize">
          <span style="font-weight: 700; font-size: 18px; color: #333333;" class="mt-2 ml-3">{{ $t('SearchPage.SearchKeyword') }} "<span
            style="color: #27AB9C;">{{ textSearch|truncate(30, '...') }}</span>" {{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
        </v-col>
        <v-col cols="5" md="5" class="pa-0 pt-2" v-else-if="!MobileSize && IpadSize">
          <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2 ml-3">{{ $t('SearchPage.SearchKeyword') }} "<span
            style="color: #27AB9C;">{{ textSearch|truncate(18, '...') }}</span>" {{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
        </v-col>
        <v-col cols="5" md="5" class="pa-0" v-else-if="MobileSize && !IpadSize">
          <span style="font-weight: 700; font-size: 14px; color: #333333;" class="mt-2 ml-3">{{ $t('SearchPage.SearchKeyword') }} "
            <span style="color: #27AB9C;">{{ textSearch|truncate(10, '...') }}</span>"<br />
            <span style="font-weight: 700; font-size: 14px; color: #333333;" class="mt-2 ml-3">{{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
          </span>
        </v-col>
        <v-spacer v-if="!MobileSize"></v-spacer>
        <v-col cols="4" md="3" class="pa-0">
          <v-select v-model="selectPrice" :items="itemPrice" @change="ADCDESC()" item-text="text" item-value="value"
            :placeholder="$t('SelectPrice.Price')" dense outlined></v-select>
        </v-col>
        <v-col cols="2" md="1" :class="MobileSize ? 'pa-0 mb-4' : IpadSize ? 'pa-0 pl-4' : 'pt-0 mb-4 mr-4'">
          <v-btn-toggle v-model="toggle_exclusive" mandatory text group dense>
            <v-btn>
              <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
              <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
            </v-btn>
            <v-btn>
              <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-agenda-outline</v-icon>
              <v-icon v-else color="#27AB9C">mdi-view-agenda</v-icon>
            </v-btn>
          </v-btn-toggle>
        </v-col>
      </v-row>
      <div v-else>
        <v-col cols="12" class="pa-0 ml-3" v-if="!MobileSize && !IpadSize && !IpadProSize">
          <v-row class="pt-4">
            <v-col cols="5" class="pa-0" v-if="selectedTagValues.length !== 0 && productSearch.length === 0">
              <span style="font-weight: 700; font-size: 18px; color: #333333;" class="mt-2">{{ $t('SearchPage.NotFoundTag') }}</span>
            </v-col>
            <v-col cols="5" class="pa-0" v-else>
              <span style="font-weight: 700; font-size: 18px; color: #333333;" class="mt-2">{{ $t('SearchPage.SearchKeyword') }} "<span
                style="color: #27AB9C;">{{ textSearch|truncate(30, '...') }}</span>" {{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
            </v-col>
            <v-spacer></v-spacer>
            <v-col cols="3" :class="MobileSize ? 'pa-0 pt-3 pl-3' : 'pa-0'">
              <v-menu
                v-model="menu"
                :close-on-content-click="false"
                offset-y
                :rounded="rounded"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-bind="attrs"
                    v-on="on"
                    :label="$t('SelectPrice.TagProduct')"
                    readonly
                    :value="selectedTagTexts.join(', ')"
                    outlined
                    dense
                    clearable
                    @click:clear="clearAll"
                    hide-details
                  />
                </template>

                <v-list style="max-height: 300px; overflow-y: auto;">
                  <template v-if="groupedItems.length > 0">
                    <v-list-group
                      v-for="group in groupedItems"
                      :key="group.header"
                      v-model="expanded[group.header]"
                      class="pl-0"
                    >
                      <template v-slot:activator>
                        <v-list-item-title style="cursor: pointer">
                          {{ group.header }}
                        </v-list-item-title>
                      </template>

                      <v-list-item
                        v-for="tag in group.tags"
                        :key="tag.value"
                        @click="toggleTag(tag.value)"
                        :active="selectedTagValues.includes(tag.value)"
                        dense
                        :input-value="selectedTagValues.includes(tag.value)"
                        class="pa-0 pl-10"
                      >
                        <v-list-item-content>
                          <span style="font-size: 14px;">{{ tag.text }}</span>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list-group>
                  </template>

                  <template v-else>
                    <v-list-item>
                      <v-list-item-content class="text-center">
                        <span style="font-size: 14px; color: #999;">{{ $t('SelectPrice.NotTag') }}</span>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-list>
              </v-menu>
            </v-col>
            <v-col cols="3" :class="MobileSize ? 'pa-0 pt-3 pl-3 pr-3' : 'pa-0 pl-2'">
              <v-select v-model="selectPrice" :items="itemPrice" @change="ADCDESC()" item-text="text" item-value="value"
                :label="$t('SelectPrice.Price')" dense outlined hide-details></v-select>
            </v-col>
            <v-col cols="1" :class="MobileSize ? 'pa-0 mb-4 pt-3 pr-3 text-right' : IpadSize ? 'pa-0 pl-4' : 'pt-0'">
              <v-btn-toggle v-model="toggle_exclusive" mandatory text group dense>
                <v-btn>
                  <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
                  <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
                </v-btn>
                <v-btn>
                  <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-agenda-outline</v-icon>
                  <v-icon v-else color="#27AB9C">mdi-view-agenda</v-icon>
                </v-btn>
              </v-btn-toggle>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pa-0" v-else-if="!MobileSize && IpadSize && !IpadProSize">
            <v-col cols="5" class="pa-0" v-if="selectedTagValues.length !== 0 && productSearch.length === 0">
              <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2">{{ $t('SearchPage.NotFoundTag') }}</span>
            </v-col>
          <v-col cols="12" class="pa-0 pt-2" v-else>
            <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2">{{ $t('SearchPage.SearchKeyword') }} "<span
              style="color: #27AB9C;">{{ textSearch|truncate(40, '...') }}</span>" {{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
            </v-col>
          <v-spacer></v-spacer>
          <v-col cols="12">
            <v-row class="pb-4 pt-2">
              <v-col cols="5" :class="MobileSize ? 'pa-0 pt-3 pl-3' : 'pa-0'">
                <v-menu
                  v-model="menu"
                  :close-on-content-click="false"
                  offset-y
                  :rounded="rounded"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-bind="attrs"
                      v-on="on"
                      :label="$t('SelectPrice.TagProduct')"
                      readonly
                      :value="selectedTagTexts.join(', ')"
                      outlined
                      dense
                      clearable
                      @click:clear="clearAll"
                      hide-details
                    />
                  </template>

                  <v-list style="max-height: 300px; overflow-y: auto;">
                    <template v-if="groupedItems.length > 0">
                      <v-list-group
                        v-for="group in groupedItems"
                        :key="group.header"
                        v-model="expanded[group.header]"
                        class="pl-0"
                      >
                        <template v-slot:activator>
                          <v-list-item-title style="cursor: pointer">
                            {{ group.header }}
                          </v-list-item-title>
                        </template>

                        <v-list-item
                          v-for="tag in group.tags"
                          :key="tag.value"
                          @click="toggleTag(tag.value)"
                          :active="selectedTagValues.includes(tag.value)"
                          dense
                          :input-value="selectedTagValues.includes(tag.value)"
                          class="pa-0 pl-10"
                        >
                          <v-list-item-content>
                            <span style="font-size: 14px;">{{ tag.text }}</span>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-group>
                    </template>

                    <template v-else>
                      <v-list-item>
                        <v-list-item-content class="text-center">
                          <span style="font-size: 14px; color: #999;">{{ $t('SelectPrice.NotTag') }}</span>
                        </v-list-item-content>
                      </v-list-item>
                    </template>
                  </v-list>
                </v-menu>
              </v-col>
              <v-col cols="5" md="2" :class="MobileSize ? 'pa-0 pt-3 pl-3 pr-3' : 'pa-0 pl-2'">
                <v-select v-model="selectPrice" :items="itemPrice" @change="ADCDESC()" item-text="text" item-value="value"
                  :label="$t('SelectPrice.Price')" dense outlined hide-details></v-select>
              </v-col>
              <v-col cols="2" :class="MobileSize ? 'pa-0 mb-4 pt-3 pr-3' : IpadSize ? 'pa-0 pl-4' : 'pt-0 mb-4 mr-4'">
                <v-btn-toggle v-model="toggle_exclusive" mandatory text group dense>
                  <v-btn>
                    <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
                    <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
                  </v-btn>
                  <v-btn>
                    <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-agenda-outline</v-icon>
                    <v-icon v-else color="#27AB9C">mdi-view-agenda</v-icon>
                  </v-btn>
                </v-btn-toggle>
              </v-col>
            </v-row>
          </v-col>
        </v-col>
        <v-col cols="12" class="pa-0" v-else-if="MobileSize && !IpadSize && !IpadProSize">
            <v-col cols="5" class="pa-0" v-if="selectedTagValues.length !== 0 && productSearch.length === 0">
              <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2">{{ $t('SearchPage.NotFoundTag') }}</span>
            </v-col>
          <v-col cols="12" class="pa-0 pt-2" v-else>
          <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2">{{ $t('SearchPage.SearchKeyword') }} "<span
            style="color: #27AB9C;">{{ textSearch|truncate(15, '...') }}</span>" {{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
          </v-col>
          <v-col cols="12">
            <v-row class="pb-4 pt-2">
              <v-col cols="6" :class="MobileSize ? 'pa-0 pt-3' : 'pa-0'">
                <v-menu
                  v-model="menu"
                  :close-on-content-click="false"
                  offset-y
                  :rounded="rounded"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-bind="attrs"
                      v-on="on"
                      :label="$t('SelectPrice.TagProduct')"
                      readonly
                      :value="selectedTagTexts.join(', ')"
                      outlined
                      dense
                      clearable
                      @click:clear="clearAll"
                      hide-details
                    />
                  </template>

                  <v-list style="max-height: 300px; overflow-y: auto;">
                    <template v-if="groupedItems.length > 0">
                      <v-list-group
                        v-for="group in groupedItems"
                        :key="group.header"
                        v-model="expanded[group.header]"
                        class="pl-0"
                      >
                        <template v-slot:activator>
                          <v-list-item-title style="cursor: pointer">
                            {{ group.header }}
                          </v-list-item-title>
                        </template>

                        <v-list-item
                          v-for="tag in group.tags"
                          :key="tag.value"
                          @click="toggleTag(tag.value)"
                          :active="selectedTagValues.includes(tag.value)"
                          dense
                          :input-value="selectedTagValues.includes(tag.value)"
                          class="pa-0 pl-10"
                        >
                          <v-list-item-content>
                            <span style="font-size: 14px;">{{ tag.text }}</span>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-group>
                    </template>

                    <template v-else>
                      <v-list-item>
                        <v-list-item-content class="text-center">
                          <span style="font-size: 14px; color: #999;">{{ $t('SelectPrice.NotTag') }}</span>
                        </v-list-item-content>
                      </v-list-item>
                    </template>
                  </v-list>
                </v-menu>
              </v-col>
              <v-col cols="6" :class="MobileSize ? 'pa-0 pt-3 pl-3' : 'pa-0 pl-2'">
                <v-select v-model="selectPrice" :items="itemPrice" @change="ADCDESC()" item-text="text" item-value="value"
                  :label="$t('SelectPrice.Price')" dense outlined hide-details></v-select>
              </v-col>
              <v-col cols="12" :class="MobileSize ? 'pa-0 mb-4 pt-3 text-right' : IpadSize ? 'pa-0 pl-4' : 'pt-0 mb-4 mr-4'">
                <v-btn-toggle v-model="toggle_exclusive" mandatory text group dense>
                  <v-btn>
                    <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
                    <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
                  </v-btn>
                  <v-btn>
                    <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-agenda-outline</v-icon>
                    <v-icon v-else color="#27AB9C">mdi-view-agenda</v-icon>
                  </v-btn>
                </v-btn-toggle>
              </v-col>
            </v-row>
          </v-col>
        </v-col>
        <v-col cols="12" class="pa-0" v-else-if="!MobileSize && !IpadSize && IpadProSize">
            <v-col cols="5" class="pa-0" v-if="selectedTagValues.length !== 0 && productSearch.length === 0">
              <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2">{{ $t('SearchPage.NotFoundTag') }}</span>
            </v-col>
          <v-col cols="12" class="pa-0 pt-2" v-else>
          <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2">{{ $t('SearchPage.SearchKeyword') }} "<span
            style="color: #27AB9C;">{{ textSearch|truncate(40, '...') }}</span>" {{ $t('SearchPage.All') }} {{ productSearch.length }} {{ $t('SearchPage.list') }}</span>
          </v-col>
          <v-spacer></v-spacer>
          <v-col cols="12">
            <v-row class="pb-4 pt-2">
              <v-col cols="5" :class="MobileSize ? 'pa-0 pt-3 pl-3' : 'pa-0'">
                <v-menu
                  v-model="menu"
                  :close-on-content-click="false"
                  offset-y
                  :rounded="rounded"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-bind="attrs"
                      v-on="on"
                      :label="$t('SelectPrice.TagProduct')"
                      readonly
                      :value="selectedTagTexts.join(', ')"
                      outlined
                      dense
                      clearable
                      @click:clear="clearAll"
                      hide-details
                    />
                  </template>

                  <v-list style="max-height: 300px; overflow-y: auto;">
                    <template v-if="groupedItems.length > 0">
                      <v-list-group
                        v-for="group in groupedItems"
                        :key="group.header"
                        v-model="expanded[group.header]"
                        class="pl-0"
                      >
                        <template v-slot:activator>
                          <v-list-item-title style="cursor: pointer">
                            {{ group.header }}
                          </v-list-item-title>
                        </template>

                        <v-list-item
                          v-for="tag in group.tags"
                          :key="tag.value"
                          @click="toggleTag(tag.value)"
                          :active="selectedTagValues.includes(tag.value)"
                          dense
                          :input-value="selectedTagValues.includes(tag.value)"
                          class="pa-0 pl-10"
                        >
                          <v-list-item-content>
                            <span style="font-size: 14px;">{{ tag.text }}</span>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-group>
                    </template>

                    <template v-else>
                      <v-list-item>
                        <v-list-item-content class="text-center">
                          <span style="font-size: 14px; color: #999;">{{ $t('SelectPrice.NotTag') }}</span>
                        </v-list-item-content>
                      </v-list-item>
                    </template>
                  </v-list>
                </v-menu>
              </v-col>
              <v-col cols="5" :class="MobileSize ? 'pa-0 pt-3 pl-3 pr-3' : 'pa-0 pl-2'">
                <v-select v-model="selectPrice" :items="itemPrice" @change="ADCDESC()" item-text="text" item-value="value"
                  :label="$t('SelectPrice.Price')" dense outlined hide-details></v-select>
              </v-col>
              <v-col cols="2" :class="MobileSize ? 'pa-0 mb-4 pt-3 pr-3' : IpadSize ? 'pa-0 pl-4' : 'pt-0'">
                <v-btn-toggle v-model="toggle_exclusive" mandatory text group dense>
                  <v-btn>
                    <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
                    <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
                  </v-btn>
                  <v-btn>
                    <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-agenda-outline</v-icon>
                    <v-icon v-else color="#27AB9C">mdi-view-agenda</v-icon>
                  </v-btn>
                </v-btn-toggle>
              </v-col>
            </v-row>
          </v-col>
        </v-col>
      </div>
      <div v-if="showSkeletonLoader">
        <v-skeleton-loader
            v-bind="attrs"
            type="image, list-item-two-line"
          ></v-skeleton-loader>
      </div>
      <div v-else>
        <v-row justify="start" class="px-0" v-if="toggle_exclusive === 0 && !MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="(item, index) in paginated" :key="index" class="px-2">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-if="toggle_exclusive === 0 && !MobileSize && !IpadSize && IpadProSize">
          <v-col cols="6" :md="IpadProSize ? '3' : '2'" v-for="(item, index) in paginated" :key="index" style="display: flex; justify-content: center;" class="px-0">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-else-if="toggle_exclusive === 0 && !MobileSize && IpadSize && !IpadProSize">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="(item, index) in paginated" :key="index" class="px-0">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-else-if="toggle_exclusive === 0 && MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="(item, index) in paginated" :key="index" class="px-0">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="px-0" v-if="toggle_exclusive === 1">
          <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in paginated" :key="index" class="px-2">
            <CardProductsList :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="my-6" v-if="shopSearchShow.length !== 0 && productSearch.length !== 0">
          <v-pagination color="#3EC6B6" v-model="pageNumber" :length="pageMax" :total-visible="MobileSize ? 5 : 7" class="paginationStyle" @change="pageChange()">
          </v-pagination>
        </v-row>
      </div>
    </v-container>
   <!-- <a-row type="flex" v-if="shopSearch.length !== 0">
     <a-col :span="24">
       <a-row type="flex">
         <a-col :span="12">
           <span style="font-weight: bold;">ร้านค้าที่เกี่ยวข้องกับ "{{ textSearch }}"</span>
         </a-col>
         <a-col :span="12">
           <a-row type="flex" justify="end">
             <v-btn text color="success" @click="gotoShowAllShop()">ร้านค้าอื่นๆ<v-icon small class="pt-1">mdi-chevron-right</v-icon></v-btn>
           </a-row>
         </a-col>
       </a-row>
     </a-col>
     <a-col :span="24" style="padding: 0; margin-top: -20px; margin-bottom: -20px;">
       <a-divider></a-divider>
     </a-col>
     <a-col :span="24" style="margin-top: 0px;">
       <v-card class="mx-auto mt-5 mb-5" max-width="100%" outlined hover @click="gotoShopDetail(shopSearchShow)">
         <v-card-text style="padding: 1.5625rem;">
           <v-row no-gutters justify="start">
               <v-col cols="1" md="1" sm="12" xs="12">
               <v-avatar size="60" @click="gotoShopDetail(shopSearchShow)" v-if="shopSearchShow.path_logo !== ''" style="cursor: pointer;">
                 <img
                   alt="user"
                   :src="`${shopSearchShow.path_logo}?=${new Date().getTime()}`"
                 >
               </v-avatar>
               <v-avatar size="60" v-else style="cursor: pointer;" @click="gotoShopDetail(shopSearchShow)">
                 <v-icon>
                   mdi-storefront
                 </v-icon>
               </v-avatar>
               </v-col>
               <v-col cols="2" md="2" sm="12" xs="12">
               <v-row dense no-gutters justify="start">
                   <v-col cols="12" md="12" sm="12" xs="12">
                       <p style="font-weight: bold; font-size: 15px;">{{ shopSearchShow.name_th }}</p>
                   </v-col>
                   <v-col cols="12" md="12" sm="12" xs="12">
                       <v-btn outlined small color="orange" @click="gotoShopDetail(shopSearchShow)"><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
                   </v-col>
               </v-row>
               </v-col>
           </v-row>
         </v-card-text>
       </v-card>
     </a-col>
   </a-row> -->
   <!-- ********************* ของเก่า ********************* -->
   <!-- <a-row type="flex" :gutter="[16, 8]" v-if="productSearch.length !== 0">
     <a-col :span="24">
       <a-row type="flex">
         <a-col :span="24">
           <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} ชิ้น ในการค้นหาคำว่า "{{ textSearch }}"</span>
         </a-col>
         <a-col :span="24" style="padding: 0; margin-top: -20px;">
           <a-divider></a-divider>
         </a-col>
         <a-col :xs="24" :sm="12" :md="4" v-for="(item, index) in productSearch" :key="index">
           <CardProducts :itemProduct="item" />
         </a-col>
       </a-row>
     </a-col>
   </a-row> -->
   <a-row v-show="NoDataInSearch" type="flex" justify="center" style="margin-top: 10%; margin-bottom: 10%;"
     v-if="shopSearchShow.length === 0 && productSearch.length === 0">
     <template>
       <a-empty :image="require('@/assets/Not_Result.png')" :image-style="{height: '200px', marginBottom: '40px'}">
         <h1 slot="description" style="font-weight: bold;">{{ $t('SearchPage.NoResults') }}</h1>
         <h3 slot="description" style="font-weight: bold;">{{ $t('SearchPage.CheckSpelling') }}
           {{ $t('SearchPage.TrySimilarWords') }}</h3>
         <h3 slot="description" style="font-weight: bold;">{{ $t('SearchPage.TryAgain') }}</h3>
       </a-empty>
     </template>
   </a-row>
 </div>
</template>

<script>
import { Encode, Decode } from '@/services'
// import VueHorizontalList from 'vue-horizontal-list'
import { Row, Empty } from 'ant-design-vue'
const productResult = []
for (let i = 0; i < 48; i++) {
  productResult.push({
    product_id: i,
    name: `Data Title product ${i}`,
    price: ` ${(i + 1) * 100}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  metaInfo () {
    return {
      title: this.textSearch + ' | Nex Gen Commerce',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        { vmid: 'description', name: 'description', content: this.textSearch + ' | Nex Gen Commerce' },
        { property: 'og:site_name', vmid: 'og:site_name', content: 'https://nexgencommerce.one.th/' },
        { property: 'og:title', vmid: 'og:title', content: this.textSearch + ' | Nex Gen Commerce' },
        { property: 'og:description', vmid: 'og:description', content: this.textSearch + '| Nex Gen Commerce' },
        { property: 'og:type', vmid: 'og:type', content: 'website' },
        { property: 'og:url', vmid: 'og:url', content: 'https://nexgencommerce.one.th/' }
        // { property: 'og:image', name: 'image', content: this.primaryImage },
        // { property: 'og:image:width', content: '640' },
        // { property: 'og:image:height', content: '480' }
      ]
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    'a-row': Row,
    'a-empty': Empty,
    // VueHorizontalList,
    // CardShop: () => import('@/components/Card/CardShop'),
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive'),
    CardProductsList: () => import('@/components/Card/ProductCardListUI')
  },
  data () {
    return {
      shopToShow: [],
      rounded: true,
      check: false,
      options: {
        responsive: [
          { end: 576, size: 6 },
          { start: 576, end: 768, size: 6 },
          { start: 768, end: 992, size: 6 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,
          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 16
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      optionsCard: {
        responsive: [
          { end: 576, size: 3 },
          { start: 576, end: 768, size: 4 },
          { start: 768, end: 992, size: 4 },
          { start: 992, end: 1200, size: 5 },
          { start: 1200, end: 1300, size: 7 },
          { size: 7 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1200,
          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      RoleUser: '',
      pathShop: '',
      productResult,
      textSearch: '',
      productSearch: [],
      shopSearch: [],
      overlay2: true,
      pageMax: null,
      current: 1,
      pageSize: 48,
      productCount: null,
      shopCount: null,
      PathImage: process.env.VUE_APP_IMAGE,
      path: process.env.VUE_APP_DOMAIN,
      shopSearchShow: [],
      selectPrice: '',
      search_ADC_DESC: '',
      shopID: '',
      ADC_DESC: '',
      NoDataInSearch: false,
      items: [],
      itemPrice: [
        { text: this.$t('SelectPrice.All'), value: '-' },
        { text: this.$t('SelectPrice.Low'), value: 'lowToHigh' },
        { text: this.$t('SelectPrice.High'), value: 'HighToLow' }
      ],
      toggle_exclusive: 0,
      // showSkeletonLoader: false,
      idCategory: '',
      shopName: '',
      shopId: '',
      shopNameBack: '',
      menu: false,
      expanded: {},
      selectedTagValues: [],
      itemTag: []
    }
  },
  created () {
    if (localStorage.getItem('roleUser') !== null) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RoleUser = 'ext_buyer'
    }
    if (this.RoleUser === 'sale_order') {
      this.pathshop = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      this.itemsSale = [
        {
          text: this.$t('SearchPage.HomePage'),
          disabled: false,
          href: this.pathshop
        },
        {
          text: this.$t('SearchPage.SearchResults'),
          disabled: true,
          href: '/shoppingcart'
        }
      ]
    }
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    // this.$EventBus.$emit('searchdata')
    this.$EventBus.$emit('getPath')
    this.getResultSearch()
    this.TagList()
    // this.shopId = JSON.parse(localStorage.getItem('shopSellerID'))
    // this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.productSearch.slice(this.indexStart, this.indexEnd)
    },
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    },
    groupedItems () {
      return this.itemTag.map((cat) => ({
        header: cat.tag_category_name,
        tags: cat.tag_list.map((tag) => ({
          text: tag.tag_name,
          value: tag.tag_id
        }))
      }))
    },
    selectedTagTexts () {
      return this.selectedTagValues.map((val) => this.getTagText(val))
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('getResultSearch', this.getResultSearch)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getResultSearch')
    })
  },
  methods: {
    toggleTag (value) {
      const i = this.selectedTagValues.indexOf(value)
      if (i === -1) {
        this.selectedTagValues.push(value)
        this.getResultSearch()
      } else {
        this.selectedTagValues.splice(i, 1)
        this.getResultSearch()
      }
    },
    getTagText (value) {
      for (const group of this.groupedItems) {
        const tag = group.tags.find((t) => t.value === value)
        if (tag) return tag.text
      }
      return value
    },
    clearAll () {
      this.selectedTagValues = []
      this.getResultSearch()
    },
    async getResultSearch () {
      this.productSearch = []
      this.shopName = localStorage.getItem('shopName')
      this.shopId = JSON.parse(localStorage.getItem('shopID'))
      this.shopNameBack = this.shopName.replace(/\s+/g, '-')
      var shopCleaned = encodeURIComponent(this.shopNameBack)
      this.items = [
        {
          text: this.$t('SearchPage.HomePage'),
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          text: this.$t('SearchPage.Store'),
          disabled: false,
          color: '#636363',
          href: `/shoppage/${shopCleaned}-${this.shopId}?page=1`
        },
        {
          text: this.$t('SearchPage.SearchResults'),
          disabled: true,
          color: '#27AB9C',
          href: ''
        }
      ]
      // this.shopName = JSON.parse(localStorage.getItem('shopName'))
      // this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      // this.shopName = this.shopDetail.name
      this.SearchImage = false
      this.stopLazyLoad = true
      this.isLoading = true
      // this.$store.commit('openLoader')
      var refCode = this.$route.query.ref_code
      // console.log('getResultSearch02')
      if (this.$route.query.idcat !== '') {
        this.idCategory = this.$route.query.idcat
      } else {
        this.idCategory = ''
      }
      this.textSearch = this.$router.currentRoute.params.data
      var data
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId = ''
        var sellerShop
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        } else if (this.RoleUser === 'sale_order') {
          companyId = JSON.parse(localStorage.getItem('PartnerID'))
          sellerShop = JSON.parse(localStorage.getItem('ShopID'))
        } else if (this.RoleUser === 'sale_order_no_JV') {
          // companyId = JSON.parse(localStorage.getItem('partner_id'))
          sellerShop = JSON.parse(localStorage.getItem('ShopID'))
        }
        data = {
          role_user: dataRole.role,
          company_id: dataRole.role === 'sale_order' ? companyId : companyId !== '' ? companyId.company.company_id : '-1',
          seller_shop_id: dataRole.role === 'sale_order' || dataRole.role === 'sale_order_no_JV' ? sellerShop : this.shopId,
          keyword: this.$router.currentRoute.params.data,
          order_by_price: this.search_ADC_DESC !== '' ? this.search_ADC_DESC : '',
          filter_tag_shop_id: this.selectedTagValues.length ? this.selectedTagValues : '',
          category: this.idCategory === '' || this.idCategory === undefined ? '' : this.idCategory,
          status_product: '',
          limit: 48,
          page: this.page
        }
      } else {
        data = {
          role_user: 'ext_buyer',
          company_id: '-1',
          seller_shop_id: this.shopId,
          keyword: this.$router.currentRoute.params.data,
          order_by_price: this.search_ADC_DESC !== '' ? this.search_ADC_DESC : '',
          filter_tag_shop_id: this.selectedTagValues.length ? this.selectedTagValues : '',
          category: this.idCategory === '' ? '' : this.idCategory,
          status_product: '',
          limit: 48,
          page: this.page
        }
      }
      // console.log('data to search=======>', data)
      await this.$store.dispatch('actionsSearchTextProduct', data)
      var response = await this.$store.state.ModuleHompage.stateSearchTextProduct
      // console.log('Result Search ======>', response)
      if (response.ok === 'y') {
        // this.overlay2 = false
        if (this.page === 1) {
          // ถ้าเป็นการค้นหาจากหน้าแรกให้เขียนทับข้อมูลใน productSearch
          this.$store.commit('openLoader')
          setTimeout(() => {
            this.productSearch = response.query_result.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
            if (this.search_ADC_DESC === 'ASC') {
              this.productSearch.sort((a, b) => a.net_price - b.net_price)
            } else if (this.search_ADC_DESC === 'DESC') {
              this.productSearch.sort((a, b) => b.net_price - a.net_price)
            }
            window.scrollTo(0, 0)
            this.$store.commit('closeLoader')
          }, 1000)
          this.shopSearchShow = response.query_result_shop
        } else {
          // ถ้าเป็นหน้าถัดไปให้เพิ่มข้อมูลเข้าไปใน productSearch โดยใช้ spread operator
          this.productSearch = [
            ...this.productSearch, // ข้อมูลเดิม
            ...response.query_result.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          ]
          if (this.search_ADC_DESC === 'ASC') {
            this.productSearch.sort((a, b) => a.net_price - b.net_price)
          } else if (this.search_ADC_DESC === 'DESC') {
            this.productSearch.sort((a, b) => b.net_price - a.net_price)
          }
          this.isLoading = false
        }
        // console.log('this.productSearch', this.productSearch)
        this.pageMax = parseInt(this.productSearch.length / 48) === 0 ? 1 : Math.ceil(this.productSearch.length / 48)
        // this.productCount = this.productSearch.length
        // this.shopSearch = response.data.seller_shop
        // this.shopSearchShow = response.query_result_shop
        this.shopToShow = this.shopSearchShow.slice(0, 10)
        this.shopToShow = this.shopToShow.map((item, index) => ({
          ...item,
          // link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
          pathShop: this.path + 'shoppage/' + encodeURIComponent(item.name_th.replace(/\s/g, '-') + '-' + item.id) + '?page=1'
        }))
        // console.log('shopToShow======>', this.shopToShow)
        // console.log('shopSearchShow======>', this.shopSearchShow)
        this.shopCount = this.shopSearchShow.length
        this.showSkeletonLoader = false
        // console.log(this.shopSearch)
        if (response.query_result.length === 0) {
          this.productSearch = []
          this.shopSearchShow = []
          this.NoDataInSearch = true
          this.showSkeletonLoader = false
          this.isLoading = false
          //   this.$swal.fire({
          //     icon: 'error',
          //     title: 'ไม่เจอสินค้าที่ค้นหา',
          //     showConfirmButton: false,
          //     timer: 1500
          //   })
        }
        this.page++
        this.stopLazyLoad = false
        if (response.query_result.length === 0) {
          this.page = 1
          this.stopLazyLoad = true
          this.isLoading = false
        }
        // console.log('this.productSearch', this.productSearch)
        // this.overlay2 = false
        // this.$store.commit('closeLoader')
      }
    },
    async ADCDESC () {
      this.shopId = JSON.parse(localStorage.getItem('shopID'))
      if (this.selectPrice === 'lowToHigh') {
        this.search_SKU = ''
        this.search_ADC_DESC = 'ASC'
      } else if (this.selectPrice === 'HighToLow') {
        this.search_ADC_DESC = 'DESC'
        this.search_SKU = ''
      } else if (this.selectPrice === 'SKU: จากน้อยไปมาก') {
        this.search_ADC_DESC = ''
        this.search_SKU = 'ADC'
      } else if (this.selectPrice === 'SKU: จากมากไปน้อย') {
        this.search_ADC_DESC = ''
        this.search_SKU = 'DESC'
      } else {
        this.search_ADC_DESC = ''
      }
      var data
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        } else if (dataRole.role === 'sale_order') {
          companyId = JSON.parse(localStorage.getItem('PartnerID'))
          var sellerShop = JSON.parse(localStorage.getItem('ShopID'))
          // console.log('companyId', companyId)
        }
        data = {
          role_user: dataRole.role,
          company_id: dataRole.role === 'sale_order' ? companyId : companyId !== undefined ? companyId.company.company_id : '-1',
          seller_shop_id: dataRole.role === 'sale_order' ? sellerShop : this.shopId,
          keyword: this.$router.currentRoute.params.data,
          order_by_price: this.search_ADC_DESC,
          filter_tag_shop_id: this.selectedTagValues.length ? this.selectedTagValues : '',
          hierachy: this.idCategory === '' ? '' : this.idCategory
        }
      } else {
        data = {
          role_user: 'ext_buyer',
          company_id: '-1',
          seller_shop_id: this.shopId,
          keyword: this.$router.currentRoute.params.data,
          order_by_price: this.search_ADC_DESC,
          filter_tag_shop_id: this.selectedTagValues.length ? this.selectedTagValues : '',
          hierachy: this.idCategory === '' ? '' : this.idCategory
        }
      }
      await this.$store.dispatch('actionsSearchTextProduct', data)
      var response = await this.$store.state.ModuleHompage.stateSearchTextProduct
      if (response.ok === 'y') {
        this.overlay2 = false
        this.productSearch = response.query_result
        this.pageMax = parseInt(this.productSearch.length / 48) === 0 ? 1 : Math.ceil(this.productSearch.length / 48)
        this.shopSearchShow = response.query_result_shop
        this.shopToShow = this.shopSearchShow.slice(0, 10)
        this.shopToShow = this.shopToShow.map((item, index) => ({
          ...item,
          // link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
          pathShop: this.path + 'shoppage/' + encodeURIComponent(item.name_th.replace(/\s/g, '-') + '-' + item.id)
        }))
        this.shopCount = this.shopSearchShow.length
        if (response.query_result.length === 0 && response.query_result_shop.length === 0) {
          this.productSearch = []
          this.shopSearchShow = []
        }
      }
      this.$EventBus.$emit('searchProduct')
    },
    async TagList () {
      this.shopId = JSON.parse(localStorage.getItem('shopID'))
      var data = {
        seller_shop_id: this.shopId,
        type: '',
        search_tag_name: ''
      }
      await this.$store.dispatch('actionsSearchProductTag', data)
      var response = await this.$store.state.ModuleHompage.stateSearchProductTag

      if (response.result === 'SUCCESS') {
        this.itemTag = response.data
      }
      this.$EventBus.$emit('searchProduct')
    },
    gotoShopDetail (val) {
      // console.log('val', val)
      // const shopCleaned = val[0].name_th.replace(/\s/g, '-')
      // const shopCleaned = []
      // val.forEach((e, i) => {
      //   shopCleaned.push(val[i].name_th.replace(/\s/g, '-'))
      //   this.$router.push(`/shoppage/${shopCleaned[i]}-${val[i].id}`).catch(() => {})
      // })
      var pathToLink = ''
      pathToLink = '/shoppage/' + encodeURIComponent(val.name_th.replace(/\s/g, '-') + '-' + val.id) + '?page=1'
      this.$router.push({ path: `${pathToLink}` }).catch(() => {})
      // console.log('shopCleaned', `/shoppage/${val.name_th.replace(/\s/g, '-')}-${val.id}?page=1`)
    },
    gotoShowAllShop () {
      localStorage.setItem('AllShopSearch', Encode.encode(this.shopSearchShow))
      this.$router.push(`/search-shop/${this.textSearch}`).catch(() => {})
    }
  }
}
</script>

<style scoped>
.paginationStyle /deep/ .v-pagination__item {
 background: transparent;
 border-radius: 6px !important;
 border: 1px solid #E6E6E6 !important;
 font-size: 1rem;
 height: 40px;
 margin: 0.3rem;
 min-width: 40px;
 padding: 0 5px;
 text-decoration: none;
 transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
 width: auto;
 box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
 box-shadow: none !important;
 border-radius: 6px !important;
 border: 1px solid #E6E6E6 !important;
 display: inline-flex;
 justify-content: center;
 align-items: center;
 text-decoration: none;
 height: 40px;
 width: 40px;
 margin: 0.3rem 10px;
}
.v-breadcrumbs__item  {
 color: #27AB9C !important;
}
.v-breadcrumbs li .v-icon {
 color: #27AB9C !important;
}
@media screen and (min-width: 768px) {
 .breadcrumbsPadding {
   padding-left: 2.5%;
   padding-top: 1%;
   padding-bottom: 1%;
 }
 .v-breadcrumbs {
   align-items: center;
   display: flex;
   flex-wrap: wrap;
   flex: 0 1 auto;
   white-space: nowrap;
   list-style-type: none;
   /* margin-bottom: 12px; */
   /* padding: 8px 0px 8px 75px !important; */
 }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
 .breadcrumbsPadding {
   padding-left: 4.2% !important;
   padding-top: 1.5% !important;
   padding-bottom: 1.5% !important;
 }
 .v-breadcrumbs {
   align-items: center;
   display: flex;
   flex-wrap: wrap;
   flex: 0 1 auto;
   white-space: nowrap;
   list-style-type: none;
   /* margin-bottom: 24px; */
   /* padding: 8px 0px 8px 75px !important; */
 }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
 .breadcrumbsPadding {
   padding-left: 5% !important;
   padding-top: 1% !important;
   padding-bottom: 1% !important;
 }
 .v-breadcrumbs {
   align-items: center;
   display: flex;
   flex-wrap: wrap;
   flex: 0 1 auto;
   white-space: nowrap;
   list-style-type: none;
   /* margin-bottom: 24px; */
   /* padding: 8px 0px 8px 75px !important; */
 }
}
@media screen and (min-width: 1600px) {
 .breadcrumbsPadding {
   padding-left: 8.75% !important;
   padding-top: 12px !important;
   padding-bottom: 12px !important;
 }
 .v-breadcrumbs {
   align-items: center;
   display: flex;
   flex-wrap: wrap;
   flex: 0 1 auto;
   white-space: nowrap;
   list-style-type: none;
   /* margin-bottom: 24px; */
   /* padding: 8px 0px 8px 75px !important; */
 }
}
.container {
 max-width: 1400px !important;
}
.cardShop {
 background-color:white;
}
.cardShop:hover {
 transform: scale(1.02) !important;
 background-color:white;
 border:1px solid #BDE7D9;
 box-shadow: 0 4px 8px rgba(167, 230, 213, 0.711) !important;
}
.v-btn-toggle--group > .v-btn.v-btn {
 background-color: transparent !important;
 border-color: transparent;
 margin: 0px !important;
 min-width: auto;
}
</style>
