<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card elevation="0" width="100%" height="100%" class="rounded-lg">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">การร้องขอราคาพิเศษ</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> การร้องขอราคาพิเศษ</v-card-title>
      <v-card-text>
        <v-row no-gutters>
          <v-col cols="12" class="px-2 py-0">
            <a-tabs @change="getRequest">
              <a-tab-pane key="ทั้งหมด"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countAll }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="รออนุมัติ"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countWaitingApprove }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="อนุมัติ"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countActive }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="สร้างใบเสนอราคาแล้ว"><span slot="tab">สร้างใบเสนอราคาแล้ว <a-tag color="#42A5F5" style="border-radius: 8px;">{{ countSuccessQu }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="สร้างรายการสั่งซื้อแล้ว"><span slot="tab">สร้างรายการสั่งซื้อแล้ว  <a-tag color="#1AB759" style="border-radius: 8px;">{{ countSuccess }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="การร้องขอที่มีการแก้ไขจากร้านค้า"><span slot="tab">การร้องขอที่มีการแก้ไขจากร้านค้า <a-tag color="#f50" style="border-radius: 8px;">{{ countEdited }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="การร้องขอที่ยกเลิก"><span slot="tab">การร้องขอที่ยกเลิก <a-tag color="#F5222D" style="border-radius: 8px;">{{ countCancel }}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col>
          <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-2 pt-2' : 'pl-2 pr-2 mb-3 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าหรือหมายเลขร้องขอ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col v-if="disableTable === true" cols="12" class="pt-2 pb-2">
            <v-row dense>
              <v-col cols="12" md="6" sm="12" class="pt-2">
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 'ทั้งหมด'">รายการร้องขอราคาพิเศษทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'รออนุมัติ'">รายการร้องขอราคาพิเศษที่รออนุมัติทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'อนุมัติ'">รายการร้องขอราคาพิเศษที่อนุมัติทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'สร้างใบเสนอราคาแล้ว'">รายการร้องขอราคาพิเศษที่สร้างใบเสนอราคาแล้วทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'สร้างรายการสั่งซื้อแล้ว'">รายการร้องขอราคาพิเศษที่สร้างรายการสั่งซื้อแล้วทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'การร้องขอที่มีการแก้ไขจากร้านค้า'">รายการร้องขอราคาพิเศษที่มีการแก้ไขจากร้านค้าทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'การร้องขอที่ยกเลิก'">รายการร้องขอราคาพิเศษที่ยกเลิกทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
          </v-col>
          <v-col v-if="disableTable === true" cols="12">
            <v-data-table
            :headers="headers"
            :items="DataTable"
            :search="search"
            style="width:100%;"
            height="100%"
            :page.sync="page"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการร้องขอพิเศษ"
            no-data-text="ไม่มีรายการร้องขอพิเศษ"
            :update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{ new Date(item.created_at).toLocaleString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric'}) }}
              </template>
              <template v-slot:[`item.total_price`]="{ item }">
                <span>{{ Number(item.total_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                <span>{{ item.status ===  'success' ? item.payment_transaction_number : item.status === 'success_qu' ? item.qu_number : '-' }}</span>
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'active'">
                  <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">อนุมัติแล้ว</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_approve'">
                  <v-chip class="ma-2" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'success_qu'">
                  <v-chip class="ma-2" color="#E3F2FD" text-color="#42A5F5">สร้างใบเสนอราคาแล้ว</v-chip>
                </span>
                <span v-if="item.status === 'success'">
                  <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">สร้างรายการสั่งซื้อแล้ว</v-chip>
                </span>
                <span v-else-if="item.status === 'edited'">
                  <v-chip class="ma-2" color="#f7c5ad" text-color="#f50">มีการแก้ไขจากร้านค้า</v-chip>
                </span>
                <span v-else-if="item.status === 'reject' || item.status === 'inactive'">
                  <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ยกเลิก</v-chip>
                </span>
              </template>
              <template v-slot:[`item.manages`]="{ item }">
                <v-row>
                  <span class="ma-2"
                    style="line-height: 22px; color:  #27AB9C; cursor: pointer"
                    @click="RequestDetail(item, item.status)">รายละเอียด <v-icon size="15" color="#27AB9C">
                      mdi-chevron-right</v-icon>
                  </span>
                </v-row>
              </template>
            </v-data-table>
          </v-col>
          <v-col cols="12" v-if="disableTable === false" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain
                aspect-ratio="2"></v-img>
            </div>
            <h2 v-if="StateStatus === 'ทั้งหมด'" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการร้องขอราคาพิเศษ</b></h2>
            <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการร้องขอราคาพิเศษที่{{ StateStatus }}</b></h2>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- dialog show detail of request -->
      <v-dialog v-model="ModalDetailRequest" width="850px" :style="MobileSize ? 'z-index: 16000004' : ''" persisten>
        <v-card width="100%" height="100%" class="rounded-lg">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ร้องขอราคาพิเศษ</font>
            </span>
            <v-btn icon dark @click="ModalDetailRequest = !ModalDetailRequest">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row class="mt-2">
              <v-col cols="12" md="12">
                <v-row dense>
                  <v-col cols="12" md="8">
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขร้องขอ : <b>{{DataTableProduct.special_price_code }}</b></p>
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">บริษัท : <b>{{DataTableProduct.name_th}}</b></p>
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ผู้ร้องขอ : <b>{{ DataTableProduct.c_firstname + " " + DataTableProduct.c_lastname }}</b></p>
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">วันที่ร้องขอ : <b>{{new Date(DataTableProduct.created_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</b></p>
                    <p v-if="DataTableProduct.status === 'waiting_approve'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ : <b>รออนุมัติ</b></p>
                    <p v-if="DataTableProduct.status === 'active'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ : <b>อนุมัติ</b></p>
                    <p v-if="DataTableProduct.status === 'success_qu'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ : <b>สร้างใบเสนอราคาแล้ว</b></p>
                    <p v-if="DataTableProduct.status === 'success'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ : <b>สร้างรายการสั่งซื้อแล้ว </b></p>
                    <p v-if="DataTableProduct.status === 'edited'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ : <b>มีการแก้ไขจากร้านค้า</b></p>
                    <p v-if="DataTableProduct.status === 'inactive' || DataTableProduct.status === 'reject'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ : <b>ยกเลิก</b></p>
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อัปเดตข้อมูลล่าสุด : <b> {{new Date(DataTableProduct.updated_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</b></p>
                  </v-col>
                  <v-col cols="12" md="4">
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="12" class="pt-0">
                <v-row dense>
                  <v-col cols="12">
                    <span v-if="DataTableProduct.product_list" style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รายการสินค้า {{ DataTableProduct.product_list.length }} รายการ</span>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12">
                    <v-data-table
                    :headers="headersProduct"
                    :items="DataTableProduct.product_list"
                    style="width:100%;"
                    height="100%"
                    no-results-text="ไม่พบรายการร้องขอพิเศษ"
                    no-data-text="ไม่มีรายการร้องขอพิเศษ"
                    class="elevation-1 mt-4 row-height-180"
                    hide-default-footer
                    :disable-pagination="true"
                    :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                    >
                    <template v-slot:[`item.product_detail`]="{ item }">
                      <v-row class="py-2" :style="MobileSize ? '' : 'width:250px'" justify-center>
                        <v-col :cols="MobileSize ? 12 : 3" md="3" class="px-0 mx-2 my-auto" justify-center>
                          <v-img width="60px" height="60px" contain :src="`${item.product_image}`" v-if="item.product_image !== ''"/>
                          <v-img width="60px" height="60px" contain src="@/assets/NoImage.png" v-else />
                        </v-col>
                        <v-col :cols="MobileSize ? 12 : 7" :align="!MobileSize ? '' : 'left'" :class="!MobileSize ? 'px-0 ml-0 my-auto' : 'px-1 ml-0 pt-0'" :style="{ 'width': MobileSize ? '68px' : '' }">
                          <span class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{item.product_name}}</span><br/>
                          <span v-if="item.have_attribute === 'yes' && item.key_1_value !== '' && item.key_1_value !== null" class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.key_1_value }}: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                          <span v-if="item.have_attribute === 'yes' && item.key_2_value !== '' && item.key_2_value !== null" class="mb-0 ml-1" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.key_2_value }}: {{ item.product_attribute_detail.attribute_priority_2 }}</span>
                          <p v-if="item.status_data_change === 'yes'" class="mb-0" style="font-size: 10px; color: red;">สินค้ามีการเปลี่ยนแปลง กรุณาลบสินค้าหรือติดต่อร้านค้า</p>
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.price`]="{ item }">
                      <span>{{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.net_price`]="{ item }">
                      <span>{{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </template>
                    </v-data-table>
                  </v-col>
                </v-row>
                <v-row dense v-if="DataTableProduct.shop_status === 'active' && DataTableProduct.partner_status === true">
                  <v-col align="right">
                    <span>ราคารวมทั้งสิ้น (ไม่รวม % ภาษี)</span>
                    <span> {{ Number(DataTableProduct.total_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span>
                  </v-col>
                </v-row>
                <v-row dense v-if="DataTableProduct.shop_status === 'active' && DataTableProduct.partner_status === true">
                  <v-col align="right" v-if="DataTableProduct.status !== 'reject' || DataTableProduct.status === 'inactive'">
                    <v-btn v-if="DataTableProduct.status === 'edited'" dense dark outlined color="#F5222D" class="pl-7 pr-7 mt-2" @click="confirmCancle()">
                      ยกเลิกการร้องขอ
                    </v-btn>
                    <v-btn v-if="DataTableProduct.status === 'edited' || DataTableProduct.status === 'waiting_approve'" dense dark outlined color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--tex" @click="edit(DataTableProduct.status)">
                      แก้ไข
                    </v-btn>
                    <v-btn v-if="DataTableProduct.status === 'edited' || DataTableProduct.status === 'active'" :disabled="checkProductChange === true" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="openModalSetPositionCompany()">
                      ชำระเงิน
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col align="right">
                    <span v-if="DataTableProduct.shop_status === 'active' && DataTableProduct.partner_status === false" style="font-size: 12px; color: red;">
                      <v-icon left small color="red">mdi-alert-circle-outline</v-icon>ไม่พบข้อมูลการเป็นคู่ค้า กรุณาติดต่อร้านค้า
                    </span>
                    <span v-if="DataTableProduct.shop_status === 'inactive'" style="font-size: 12px; color: red;">
                      <v-icon left small color="red">mdi-alert-circle-outline</v-icon>ร้านค้าอยู่ในสถานะปิดการใช้งาน กรุณาลองใหม่อีกครั้งภายหลัง
                    </span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- dialog edit detail -->
      <v-dialog v-model="ModalEditRequest"  width="850px" :style="MobileSize ? 'z-index: 16000004' : ''" persisten>
        <v-card width="100%" height="100%" class="rounded-lg">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">แก้ไขการร้องขอราคาพิเศษ</font>
            </span>
            <v-btn icon dark @click="ModalEditRequest = !ModalEditRequest">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row class="mt-2">
            <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12" md="6">
                  <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
                  <span class="ml-3" style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">{{ DataTableProduct.shop_name }}</span>
                </v-col>
                <v-col cols="12" md="6" :align="!MobileSize ? 'right' : ''">
                  <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขร้องขอ : {{DataTableProduct.special_price_code}}</span>
                </v-col>
                <v-col cols="12">
                  <v-autocomplete
                    return-object
                    v-model="selectedItem"
                    :items="searchProductList"
                    outlined
                    dense
                    chips
                    small-chips
                    multiple
                    item-text="name"
                    item-value="searchId"
                    @change="(event) => updateSelectData(event)"
                    no-data-text="ไม่พบสินค้าที่ค้นหา"
                  >
                  <template v-slot:selection="data">
                    <v-chip
                      class="ma-1"
                      v-bind="data.attrs"
                      :input-value="data.selected"
                      close
                      @click="data.select"
                      @click:close="remove(data.item)"
                    >
                      <v-avatar left>
                        <v-img :src="data.item.product_image" v-if="data.item.product_image !== null"></v-img>
                        <v-img src="@/assets/NoImage.png" v-else />
                      </v-avatar>
                      <span :class="MobileSize ? 'mobile-font-size' : ''">{{ data.item.name | truncate(15, '...') }}</span>
                      <span :class="MobileSize ? 'mobile-font-size' : ''" class="ml-1" v-if="data.item.have_attribute === 'yes' && data.item.attribute_1_key !== null" >{{ data.item.attribute_1_key | truncate(15, '...') }} : {{ data.item.attribute_priority_1 | truncate(15, '...') }}</span>
                      <span :class="MobileSize ? 'mobile-font-size' : ''" class="ml-1" v-if="data.item.have_attribute === 'yes' && data.item.attribute_2_key !== null"> {{ data.item.attribute_2_key | truncate(15, '...') }} : {{ data.item.attribute_priority_2 | truncate(15, '...')}}</span>
                    </v-chip>
                  </template>
                    <template v-slot:item="{ item, attrs }">
                      <v-container :class="MobileSize ? 'px-0' : 'pa-2'">
                        <v-row no-gutters v-if="!MobileSize">
                          <v-col cols="2">
                            <v-icon
                              v-if="selectedItem.length !== 0 && selectedItem.includes(item)"
                              color="primary"
                              class="mr-3">
                              mdi-checkbox-marked
                            </v-icon>
                            <v-icon v-else class="mr-3">
                              mdi-checkbox-blank-outline
                            </v-icon>
                            <img v-bind="attrs" :src="item.product_image" width="50" height="50" v-if="item.product_image !== null">
                            <img src="@/assets/NoImage.png" width="50" height="50" v-else />
                          </v-col>
                          <v-col cols="7" style="max-width: 380px;">
                            <span :class="MobileSize ? 'mobile-font-size' : ''">{{ item.name|truncate(20, '...') }}</span>
                            <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_1_key !== null" >{{ item.attribute_1_key }} : {{ item.attribute_priority_1| truncate(20, '...') }}</span>
                            <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_2_key !== null"> {{ item.attribute_2_key }} : {{ item.attribute_priority_2| truncate(20, '...') }}</span>
                          </v-col>
                          <v-col cols="3" align="end">
                            <span :class="MobileSize ? 'mobile-font-size' : ''">ราคา {{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span><br>
                          </v-col>
                        </v-row>
                        <v-row no-gutters v-else style="width: 250px;">
                          <v-col cols="5" class="pt-2">
                            <v-icon
                              v-if="selectedItem.length !== 0 && selectedItem.includes(item)"
                              color="primary"
                              class="mr-3">
                              mdi-checkbox-marked
                            </v-icon>
                            <v-icon v-else class="mr-3">
                              mdi-checkbox-blank-outline
                            </v-icon>
                            <img v-bind="attrs" :src="item.product_image" width="50" height="50" v-if="item.product_image !== null">
                            <img src="@/assets/NoImage.png" width="50" height="50" v-else />
                          </v-col>
                          <v-col cols="7" class="pl-2">
                            <span :class="MobileSize ? 'mobile-font-size' : ''">{{ item.name|truncate(20, '...') }}</span><br/>
                            <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_1_key !== null" >{{ item.attribute_1_key }} : {{ item.attribute_priority_1| truncate(20, '...') }}</span>
                            <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_2_key !== null"> {{ item.attribute_2_key }} : {{ item.attribute_priority_2| truncate(20, '...') }}</span>
                            <br v-if="item.have_attribute === 'yes'" /><span :class="MobileSize ? 'mobile-font-size' : ''">ราคา {{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span><br>
                          </v-col>
                        </v-row>
                      </v-container>
                    </template>
                  </v-autocomplete>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รายการสินค้า</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" v-if="checkTable">
                  <v-data-table
                  :headers="headersEditProduct"
                  :items="DataTableProduct.product_list"
                  style="width:100%;"
                  height="100%"
                  no-results-text="ไม่พบรายการร้องขอพิเศษ"
                  no-data-text="ไม่มีรายการร้องขอพิเศษ"
                  class="elevation-1 mt-4"
                  hide-default-footer
                  :class="MobileSize ? 'pt-2' : ''"
                  :disable-pagination="true"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  >
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <v-row class="py-2" :style="MobileSize ? '' : 'width:250px'" justify-center>
                      <v-col :cols="MobileSize ? 12 : 3" class="px-0 mx-2 my-auto" justify-center>
                        <v-img width="60px" height="60px" contain :src="`${item.product_image}`" v-if="item.product_image !== ''"/>
                        <v-img width="60px" height="60px" contain src="@/assets/NoImage.png" v-else />
                      </v-col>
                      <v-col :cols="MobileSize ? 12 : 7" :align="!MobileSize ? '' : 'left'" :class="!MobileSize ? 'px-0 ml-0 my-auto' : 'px-1 ml-0 pt-0'" :style="{ 'width': MobileSize ? '68px' : '' }">
                        <span class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{item.product_name}}</span><br/>
                        <span v-if="item.have_attribute === 'yes' && item.key_1_value !== '' && item.key_1_value !== null" class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.key_1_value }}: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                        <span v-if="item.have_attribute === 'yes' && item.key_2_value !== '' && item.key_2_value !== null" class="mb-0 ml-1" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.key_2_value }}: {{ item.product_attribute_detail.attribute_priority_2 }}</span>
                        <p v-if="item.status_data_change === 'yes'" class="mb-0" style="font-size: 10px; color: red;">สินค้ามีการเปลี่ยนแปลง กรุณาลบสินค้าหรือติดต่อร้านค้า</p>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    <v-card outlined max-width="200px;" :height="MobileSize ? 'auto' : '50px'" :width="!MobileSize ? '100%' : '100px'" :class="MobileSize? 'ma-2' : ''">
                      <v-card-text class="py-2 px-1">
                        <v-row justify="center" class="py-2 px-3">
                          <v-col cols="12" md="12" class="pa-0" align="center">
                            <v-text-field
                              :style="MobileSize ? 'font-weight: 400; font-size: 14px; line-height: 22px; color: #333333; height: auto;' : 'height: 50px; width:100px'"
                              v-model="item.price"
                              dense
                              hide-details
                              solo flat
                              class="pa-0 text-price"
                              @change="changeInputPriceAndQuantity(item, 'price')"
                              oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"
                              :readonly="item.status_data_change === 'yes'"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </template>
                  <template v-slot:[`item.quantity`]="{ item }">
                    <v-card outlined height="50px" :width="!MobileSize ? '100%' : '100px'" :class="MobileSize? 'ma-2' : ''">
                      <v-card-text class="py-2 px-1">
                        <v-row justify="center" class="py-2 px-3">
                          <v-col cols="4" md="4" class="pa-0" align="center">
                            <v-hover v-slot="{ hover }">
                              <v-btn :disabled="item.quantity <= 1 || item.status_data_change === 'yes'" @click="item.quantity > 1 && item.price.toString() !== '0.00' ? item.quantity-- : '', changeInputPriceAndQuantity(item, 'quantity')" elevation="0" class="mt-1" :color="hover ? '#A1A1A1' : '#E6E6E6'" style="min-width: 26px !important; height: 30px !important; padding: 0px 8px !important;">
                                <v-icon class="px-0" color="white" small>mdi-minus</v-icon>
                              </v-btn>
                            </v-hover>
                          </v-col>
                          <v-col cols="4" md="4" class="pa-0" align="center">
                            <v-text-field
                              v-model="item.quantity"
                              dense
                              solo flat
                              :style="MobileSize ? 'font-weight: 400; font-size: 14px; line-height: 22px; color: #333333' : 'width:60px'"
                              type="number"
                              class="pa-0 quantity-input"
                              @click="item.price.toString() == '0.00' ? errMsg() : ''"
                              @change="changeInputPriceAndQuantity(item, 'quantity')"
                              :readonly="item.status_data_change === 'yes' || item.price.toString() === '0.00'"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="4" md="4" sm="4" class="pa-0" align="center">
                            <v-hover v-slot="{ hover }">
                              <v-btn :disabled="item.quantity > item.stock || item.status_data_change === 'yes'" @click="item.price.toString() !== '0.00' ? item.quantity++ : '', changeInputPriceAndQuantity(item, 'quantity')" elevation="0" class="mt-1" :color="hover ? '#A1A1A1' : '#E6E6E6'" style="min-width: 28px !important; height: 30px !important; padding: 0px 8px !important;">
                                <v-icon class="px-0" color="white" small>mdi-plus</v-icon>
                              </v-btn>
                            </v-hover>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </template>
                  <template v-slot:[`item.net_price`]="{ item }">
                    <span>{{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                  </template>
                  <template v-slot:[`item.action`]="{ item, index }">
                    <v-row>
                      <v-col cols="12">
                        <v-card elevation="1" width="100%" height="100%">
                          <v-btn color="#27AB9C" icon @click="deleteCartItem(item, index)">
                            <v-icon class="button-edit-delete">mdi-delete-outline</v-icon>
                          </v-btn>
                        </v-card>
                      </v-col>
                    </v-row>
                  </template>
                  </v-data-table>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col align="right">
                  <span>ราคารวมทั้งสิ้น (ไม่รวม % ภาษี)</span>
                  <span> {{ Number(totalPriceNoVat).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col align="right">
                  <v-btn :loading="loading" :disabled="'product_list' in DataTableProduct && DataTableProduct.product_list.length === 0 || checkKeyClick === true || checkProductChange === true" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="sendSpecialPriceRequest()">
                    ร้องขอราคาพิเศษ
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- dialog เลือกตำแหน่งก่อนชำระเงิน -->
      <v-dialog v-model="modalSetPositionCompany" width="730" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card align="center" class="rounded-lg">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ข้อมูลตำแหน่งของบริษัท</font>
            </span>
            <v-btn icon dark @click="modalSetPositionCompany = !modalSetPositionCompany">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-title class="my-3" style="color: #333333; font-weight: 700;" :style="MobileSize ? 'font-size:14px;' : 'font-size:20px;'">รายชื่อตำแหน่งของบริษัทของฉัน ( {{ list_position.length }} รายชื่อ)</v-card-title>
          <v-card-text>
              <v-row dense no-gutters>
                <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in list_position" :key="index" class="mb-4">
                  <v-card width="100%" height="100%" outlined
                    style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
                    <v-card-text>
                      <v-row dense no-gutters>
                        <v-col cols="4" md="2" sm="3" class="pr-0">
                          <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                            rounded>
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                            </v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="8" md="7" sm="6" class="pt-4 pl-0">
                          <div
                            style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                            {{ item.role_name }}</div>
                        </v-col>
                        <v-col cols="12" md="2"  sm="3" class="pt-3 pl-6" :align="MobileSize ? 'right' : ''">
                          <v-btn text color="#27AB9C" @click="setPositionCompany(item)">เลือกตำแหน่ง<v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- dialog comfirm cancle -->
      <v-dialog  v-model="dialog" width="400" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card align="center" class="rounded-lg">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ยกเลิกการร้องขอ</font>
            </span>
            <v-btn icon dark @click="dialog = !dialog">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <br/><br/>
          <v-card-text >
            <span>
              คุณต้องการยกเลิกคำขอนี้ ใช่ หรือไม่
            </span>
          </v-card-text>
          <v-card-actions >
        <v-container >
          <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialog = !dialog">ยกเลิก</v-btn>
          <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Cancle()">ตกลง</v-btn>
        </v-container>
        </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      loading: false,
      shopDetail: '',
      showCountRequest: 0,
      countAll: 0,
      countSuccess: 0,
      countActive: 0,
      countWaitingApprove: 0,
      countEdited: 0,
      countCancel: 0,
      countSuccessQu: 0,
      StateStatus: 'ทั้งหมด',
      search: '',
      itemsPerPage: 10,
      headers: [
        { text: 'ชื่อร้านค้า', value: 'shop_name', sortable: false, class: 'backgroundTable fontTable--text', width: 270 },
        { text: 'หมายเลขร้องขอ', value: 'special_price_code', sortable: false, class: 'backgroundTable fontTable--text', width: 200 },
        { text: 'วันที่ร้องขอ', value: 'created_at', sortable: false, filterable: false, class: 'backgroundTable fontTable--text', width: 180 },
        { text: 'ราคารวม', value: 'total_price', sortable: false, filterable: false, class: 'backgroundTable fontTable--text', width: 120 },
        { text: 'สถานะ', value: 'status', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเลขอ้างอิง', value: 'payment_transaction_number', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', width: 230 },
        { text: 'การจัดการ', value: 'manages', sortable: false, filterable: false, class: 'backgroundTable fontTable--text', width: 150 }
      ],
      DataTable: [],
      disableTable: true,
      requestList: [],
      // requestDetail: [],
      page: 1,
      ModalDetailRequest: false,
      headersProduct: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', width: 260, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', width: 120, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', width: 180, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'net_price', width: 120, sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      DataTableProduct: [],
      ModalEditRequest: false,
      headersEditProduct: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', width: 260, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', width: 120, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', width: 180, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'net_price', width: 120, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: '', value: 'action', width: '50px', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      dialog: false,
      statusData: '',
      modalSetPositionCompany: false,
      spePriceId: null,
      spePriceCode: '',
      selectedItem: [],
      searchProductList: [],
      companyData: null,
      companyId: null,
      sellerShopID: '',
      userId: '',
      checkTable: true,
      items: '',
      totalPriceNoVat: 0,
      list_position: [],
      list_company: [],
      selectedCompany: null,
      selectCompanyName: '',
      checkKeyClick: false,
      checkProductChange: null
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.companyId = this.companyData.id
      this.getListRequest()
      this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    StateStatus (val) {
      if (val === 'ทั้งหมด') {
        this.DataTable = this.requestList.all !== undefined ? this.requestList.all : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'รออนุมัติ') {
        this.DataTable = this.requestList.waiting_approve !== undefined ? this.requestList.waiting_approve : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'อนุมัติ') {
        this.DataTable = this.requestList.active !== undefined ? this.requestList.active : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'สร้างใบเสนอราคาแล้ว') {
        this.DataTable = this.requestList.success_qu !== undefined ? this.requestList.success_qu : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'สร้างรายการสั่งซื้อแล้ว') {
        this.DataTable = this.requestList.success !== undefined ? this.requestList.success : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'การร้องขอที่มีการแก้ไขจากร้านค้า') {
        this.DataTable = this.requestList.edited !== undefined ? this.requestList.edited : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'การร้องขอที่ยกเลิก') {
        this.DataTable = this.requestList.cancel !== undefined ? this.requestList.cancel : []
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/specialPriceBuyerRequestMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/specialPriceBuyerRequest' }).catch(() => {})
      }
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    async AuthorityUser () {
      this.list_position = []
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      this.list_company = response.data.list_company
      this.list_company.forEach(element => {
        if (element.company_id === this.companyId) {
          this.selectedCompany = element
          element.array_position.forEach(position => {
            if (position.purchaser === true) {
              this.list_position.push(position)
            }
          })
        }
      })
    },
    async getListRequest () {
      this.$store.commit('openLoader')
      this.countAll = 0
      this.countSuccess = 0
      this.countEdited = 0
      this.countWaitingApprove = 0
      this.countCancel = 0
      this.countActive = 0
      this.countSuccessQu = 0
      var data = {
        company_id: this.companyId
      }
      await this.$store.dispatch('actionsListSpecialPriceBuyer', data)
      var response = await this.$store.state.ModuleAdminManage.stateListSpecialPriceBuyer
      if (response.message === 'Success' || response.message === 'ไม่พบข้อมูล') {
        this.$store.commit('closeLoader')
        this.requestList = response.data
        if (this.requestList !== '') {
          this.countAll = this.requestList.all.length
          this.countActive = this.requestList.active.length
          this.countSuccess = this.requestList.success.length
          this.countWaitingApprove = this.requestList.waiting_approve.length
          this.countEdited = this.requestList.edited.length
          this.countCancel = this.requestList.cancel.length
          this.countSuccessQu = this.requestList.success_qu.length
          if (this.StateStatus === 'ทั้งหมด') {
            this.DataTable = this.requestList.all
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 'รออนุมัติ') {
            this.DataTable = this.requestList.waiting_approve
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 'อนุมัติ') {
            this.DataTable = this.requestList.active
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 'สร้างใบเสนอราคาแล้ว') {
            this.DataTable = this.requestList.success_qu
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 'สร้างรายการสั่งซื้อแล้ว') {
            this.DataTable = this.requestList.success
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 'การร้องขอที่มีการแก้ไขจากร้านค้า') {
            this.DataTable = this.requestList.edited
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 'การร้องขอที่ยกเลิก') {
            this.DataTable = this.requestList.cancel
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          }
        } else {
          this.disableTable = false
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    getRequest (item) {
      this.StateStatus = item
      this.page = 1
    },
    async RequestDetail (item, status, keys) {
      this.selectedItem = []
      this.statusData = status
      this.spePriceId = item.id
      this.spePriceCode = item.special_price_code
      var dataDetail = {
        spe_price_id: this.spePriceCode
      }
      await this.$store.dispatch('actionsDetailSpecialPrice', dataDetail)
      var response = await this.$store.state.ModuleAdminManage.stateDetailSpecialPrice
      if (response.message === 'Success') {
        this.DataTableProduct = response.data[0]
        this.checkStatusProduct()
        this.sellerShopID = response.data[0].seller_shop_id
        this.ModalDetailRequest = !this.ModalDetailRequest
      } else {
        this.DataTableProduct = []
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    checkStatusProduct () {
      this.checkProductChange = this.DataTableProduct.product_list.some(e => e.status_data_change === 'yes')
    },
    async confirmCancle () {
      this.dialog = !this.dialog
    },
    async Cancle () {
      var data = {
        spe_price_id: this.spePriceCode,
        status: 'inactive'
      }
      this.ModalDetailRequest = !this.ModalDetailRequest
      await this.$store.dispatch('actionsUpdateStatusSpecialPriceBuyer', data)
      var response = await this.$store.state.ModuleAdminManage.stateUpdateStatusSpecialPriceBuyer
      if (response.message === 'Update success') {
        this.dialog = !this.dialog
        this.ModalDetailRequest = false
        this.$swal.fire({ text: 'ยกเลิกคำขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.getListRequest()
      } else {
        this.dialog = !this.dialog
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${response.message}`
        })
      }
    },
    errMsg () {
      const msg = 'ไม่สามารถเปลี่ยนแปลงจำนวนสินค้าได้เนื่องจากราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
      this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', text: msg })
    },
    async changeInputPriceAndQuantity (item, type) {
      if (type === 'price') {
        this.inputPrice(item)
      } else if (type === 'quantity') {
        if (parseFloat(item.price) === 0.00) {
          item.quantity = parseInt(item.quantity)
          const msg = 'ไม่สามารถเปลี่ยนแปลงจำนวนสินค้าได้เนื่องจากราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', text: msg })
        } else {
          this.inputQuantity(item)
        }
      }
    },
    async inputPrice (item) {
      if (parseFloat(item.price) === 0.00) {
        item.price = parseFloat(item.price).toFixed(2)
        this.checkKeyClick = true
        await this.updateTable()
      } else if (item.price === '' || item.quantity === null || item.price === 'NaN') {
        item.price = 0.00
        this.checkKeyClick = true
        await this.updateTable()
      } else {
        item.price = parseFloat(item.price).toFixed(2)
        this.checkKeyClick = false
        await this.updateTable()
      }
    },
    async inputQuantity (item) {
      if (parseInt(item.quantity) === 0 || item.quantity === '' || item.quantity === null || item.quantity === 'NaN') {
        const msg = 'จำนวนสินค้าควรเป็นจำนวนบวกและมีค่ามากกว่า 0 และ ไม่ใช่ค่าว่าง'
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', text: msg })
        item.quantity = 1
        await this.updateTable()
      } else if (parseInt(item.quantity) > parseInt(item.stock)) {
        const msg = `ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว (สินค้าคงเหลือ ${item.stock} ชิ้น)`
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'warning', text: msg })
        item.quantity = parseInt(item.stock)
        await this.updateTable()
      } else {
        await this.updateTable()
      }
    },
    deleteCartItem (item, index) {
      this.$swal.fire({
        icon: 'warning',
        text: 'คุณต้องการที่จะลบสินค้านี้หรือไม่?',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        cancelButtonColor: '#FF3F00',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.DataTableProduct.product_list.splice(index, 1)
          const indexSelect = this.selectedItem.findIndex(e => item.have_attribute === 'yes' ? item.product_attribute_detail.product_attribute_id === e.attribute_id : item.product_id === e.product_id)
          this.selectedItem.splice(indexSelect, 1)
          this.updateTable()
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    async edit (status) {
      this.$store.commit('openLoader')
      this.getSearchProductData()
      var dataDetail = {
        spe_price_id: this.spePriceCode
      }
      await this.$store.dispatch('actionsDetailSpecialPrice', dataDetail)
      var responsePending = await this.$store.state.ModuleAdminManage.stateDetailSpecialPrice
      if (responsePending.message === 'Success') {
        this.$store.commit('closeLoader')
        this.DataTableProduct = responsePending.data[0]
        this.ModalDetailRequest = !this.ModalDetailRequest
        this.ModalEditRequest = !this.ModalEditRequest
        this.updateTable()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${responsePending.message}`
        })
      }
    },
    async sendSpecialPriceRequest () {
      this.loading = true
      this.$store.commit('openLoader')
      this.checkKeyClick = true
      const productList = []
      this.DataTableProduct.product_list.forEach(element => {
        productList.push({
          product_id: element.product_id,
          product_name: element.product_name,
          attribute_id: element.product_attribute_detail.product_attribute_id === null ? '-1' : element.product_attribute_detail.product_attribute_id,
          quantity: parseInt(element.quantity),
          price: parseFloat(element.price),
          product_image: element.product_image
        })
      })
      var data = {
        spe_price_id: this.spePriceCode,
        product_list: productList
      }
      await this.$store.dispatch('actionsEditSpecialPriceBuyer', data)
      var responseEdit = await this.$store.state.ModuleAdminManage.stateEditSpecialPriceBuyer
      if (responseEdit.message === 'your request edit special price successful') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.ModalEditRequest = !this.ModalEditRequest
        this.$swal.fire({ text: 'ส่งการแก้ไขการร้องขอราคาพิเศษแล้ว', icon: 'success', timer: 2500, showConfirmButton: false })
        this.getListRequest()
      } else if (responseEdit.result === 'quantity หรือ price ควรเป็นจำนวนบวกและมีค่ามากกว่า 0') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'จำนวน หรือ ราคา ควรเป็นจำนวนที่มีค่ามากกว่า 0'
        })
      } else if (responseEdit.result === 'request special price can not more than or equal products price') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: responseEdit.message
        })
      } else if (responseEdit.result === 'can not request special price more than your credit') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'ไม่สามารถร้องขอราคาพิเศษได้ เนื่องจากราคาสินค้าเกินวงเงินของบริษัท'
        })
      } else if (responseEdit.result === 'Can not buy more than stock.') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: responseEdit.message
        })
      } else {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseEdit.message}`
        })
      }
    },
    async Save () {
      this.$swal.fire({ text: 'บันทึกข้อมูลสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
      this.ModalEditRequest = !this.ModalEditRequest
    },
    async Approve () {
      this.$swal.fire({ text: 'อนุมัติคำขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
    },
    openModalSetPositionCompany () {
      this.ModalDetailRequest = !this.ModalDetailRequest
      this.modalSetPositionCompany = !this.modalSetPositionCompany
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    // เลือกตำแหน่งก่อนชำระเงิน
    setPositionCompany (positionData) {
      var KepData = {
        company: {
          compant_name_en: this.selectedCompany.compant_name_en,
          compant_name_th: this.selectedCompany.compant_name_th,
          company_id: this.selectedCompany.company_id
        },
        position: positionData
      }
      localStorage.setItem('SetRowCompany', Encode.encode(KepData))
      this.selectCompanyName = 'ตำแหน่ง : ' + positionData.role_name + '   บริษัท : ' + this.selectedCompany.compant_name_th
      localStorage.setItem('selectCompanyName', this.selectCompanyName)
      this.$EventBus.$emit('setPositionCompanyFromSpecialPrice')
      this.goToPayment(positionData)
    },
    async goToPayment (position) {
      this.modalSetPositionCompany = !this.modalSetPositionCompany
      var calArr = []
      // const dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.DataTableProduct.product_list.forEach(element => {
        calArr.push(
          {
            attribute_priority_1: element.have_attribute === 'yes' ? element.product_attribute_detail.attribute_priority_1 : '',
            attribute_priority_2: element.have_attribute === 'yes' ? element.product_attribute_detail.attribute_priority_2 : '',
            product_attribute_id: element.have_attribute === 'yes' ? element.product_attribute_detail.product_attribute_id : '-1',
            product_id: element.product_id

          }
        )
      })
      var RowCompany = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      const data = {
        product_special_price_id: this.spePriceId,
        role_user: 'purchaser',
        seller_shop_id: this.sellerShopID,
        company_position: RowCompany.position.role_id,
        com_perm_id: RowCompany.position.com_perm_id,
        coupon: []
      }
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData = data
      onedata.cartDataSpecialPrice = 'yes'
      localStorage.setItem('oneData', Encode.encode(onedata))
      this.modalSetPositionCompany = !this.modalSetPositionCompany
      await this.$router.push({ path: '/checkout' }).catch(() => {})
    },
    goToCreditTerm () {
      this.modalSetPositionCompany = !this.modalSetPositionCompany
      this.$swal.fire({ text: 'ชำระเงินโดยใช้เครดิตเทอมสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
    },
    async getSearchProductData () {
      this.userId = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        company_id: this.companyId,
        seller_shop_id: parseInt(this.sellerShopID),
        user_id: this.userId ? this.userId.user.user_id : ''
      }
      await this.$store.dispatch('actionsListProductAttribute', data)
      var response = await this.$store.state.ModuleAdminManage.stateListProductAttribute
      if (response.message === 'Find product for QU success.') {
        this.searchProductList = response.data
        this.searchProductList.forEach((element, i) => {
          this.searchProductList[i].price = element.real_price
          this.searchProductList[i].quantity = null
          this.searchProductList[i].searchId = element.have_attribute === 'yes' ? element.attribute_id + ',id' + element.product_id : element.product_id
        })
        this.DataTableProduct.product_list.forEach(element => {
          this.searchProductList.forEach((search, i) => {
            if (element.have_attribute === 'yes') {
              if (element.product_attribute_detail.product_attribute_id === search.attribute_id) {
                this.selectedItem.push(search)
              }
            } else {
              if (element.product_id === search.product_id) {
                this.selectedItem.push(search)
              }
            }
          })
        })
      }
    },
    prepareUpdateSelectData (element) {
      console.log('eee', element)
      this.DataTableProduct.product_list.push({
        product_id: element.product_id,
        product_name: element.name,
        product_image: element.product_image,
        have_attribute: element.have_attribute,
        key_1_value: element.attribute_1_key,
        key_2_value: element.attribute_2_key,
        product_attribute_detail: {
          attribute_priority_1: element.attribute_priority_1,
          attribute_priority_2: element.attribute_priority_2,
          product_attribute_id: element.attribute_id
        },
        price: parseFloat(element.real_price).toFixed(2),
        quantity: 1,
        net_price: 0,
        stock: element.stock_count,
        service_type: element.service_type
      })
    },
    updateSelectData (value) {
      if (value.length < this.DataTableProduct.product_list.length) {
        this.DataTableProduct.product_list.forEach((element, index) => {
          const check = value.some(e => element.have_attribute === 'yes' ? element.product_attribute_detail.product_attribute_id === e.attribute_id : element.product_id === e.product_id)
          if (check === false) {
            this.DataTableProduct.product_list.splice(index, 1)
          }
        })
        this.updateTable()
      } else {
        value.forEach((element, i) => {
          const check2 = this.DataTableProduct.product_list.some(e => e.have_attribute === 'yes' ? e.product_attribute_detail.product_attribute_id === element.attribute_id : e.product_id === element.product_id)
          const checkItemBulkInTable = this.DataTableProduct.product_list.some(e => e.service_type === 'bulk')
          // เช็คว่าใน table มีสินค้าที่มีขนาดใหญ่แล้วรึยังถ้ามีอยู่แล้วจะการแจ้งเตือนว่าสามารถซื้อสินค้าขนาดใหญ่ได้แค่ชิ้นเดียวและไม่ add ชิ้นอื่นลงใน table
          if (check2 === false && checkItemBulkInTable === false) {
            this.prepareUpdateSelectData(element)
            this.updateTable()
          } else if (check2 === false && checkItemBulkInTable === true) {
            // เช็คว่าสินค้าชิ้นที่เลือกเป็นสินค้าขนาดใหญ่ไหมถ้าใช่จะไม่ add ลง table
            if (element.service_type !== 'bulk') {
              this.prepareUpdateSelectData(element)
              this.updateTable()
            } else {
              this.selectedItem.splice(i, 1)
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                text: 'ไม่สามารถเพิ่มสินค้าได้ เนื่องจากเป็นสินค้าขนาดใหญ่ สามารถซื้อสินค้าขนาดใหญ่ได้ 1 ชิ้นต่อคำสั่งซื้อ'
              })
            }
          }
        })
      }
    },
    prepareDataTable (response) {
      this.DataTableProduct.product_list = []
      this.totalPriceNoVat = response.total_price_no_vat
      response.product_list.forEach(element => {
        this.DataTableProduct.product_list.push({
          product_id: element.product_id,
          product_name: element.product_name,
          product_image: element.product_image,
          have_attribute: element.have_attribute,
          key_1_value: element.key_1_value,
          key_2_value: element.key_2_value,
          product_attribute_detail: {
            attribute_priority_1: element.product_attribute_detail.attribute_priority_1,
            attribute_priority_2: element.product_attribute_detail.attribute_priority_2,
            product_attribute_id: element.product_attribute_detail.product_attribute_id
          },
          price: parseFloat(element.price).toFixed(2),
          quantity: element.quantity,
          net_price: element.total_price,
          stock: element.stock,
          status_data_change: element.status_data_change,
          service_type: element.service_type
        })
      })
    },
    async updateTable () {
      this.$store.commit('openLoader')
      const data = {
        product_list: this.DataTableProduct.product_list
      }
      await this.$store.dispatch('actionsUpdateListDataTableSpecialPrice', data)
      var response = await this.$store.state.ModuleAdminManage.stateUpdateListDataTableSpecialPrice
      this.DataTableProduct.product_list = []
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.prepareDataTable(response)
        this.checkStatusProduct()
        // this.DataTableProduct.product_list = response.product_list
      } else if (response.message === 'price can not less than 0') {
        this.$store.commit('closeLoader')
        this.prepareDataTable(response)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'ราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
        })
      } else if (response.result === 'เป็นสินค้าขนาดใหญ่สามารถซื้อได้ 1 ชิ้นเท่านั้น') {
        this.$store.commit('closeLoader')
        this.prepareDataTable(response)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: response.message
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${response.message}`
        })
      }
    },
    remove (item) {
      const index = this.selectedItem.findIndex(e => e.searchId === item.searchId)
      this.selectedItem.splice(index, 1)
      const indexI = this.DataTableProduct.product_list.findIndex(e => e.have_attribute === 'yes' ? e.product_attribute_detail.product_attribute_id === item.attribute_id : e.product_id === item.product_id)
      this.DataTableProduct.product_list.splice(indexI, 1)
      this.checkTable = true
    }
  }
}
</script>

<style>
.quantity-input .v-input__slot {
  padding: 0 4px !important;
}
.mobile-font-size {
  font-size: 12px;
}
/* .v-data-table > .v-data-table__wrapper > table > tbody > tr > td,
.v-data-table > .v-data-table__wrapper > table > thead > tr > td,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > td {
  height: 100px !important;
} */
</style>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
