<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" class="pl-3" v-if="!MobileSize">ร้านค้าที่เชื่อมต่อ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPageMobile()">mdi-chevron-left</v-icon>ร้านค้าที่เชื่อมต่อ</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้า Partner" outlined rounded dense hide-details style="border-radius: 10px;">
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row dense>
              <v-col v-if="!MobileSize && !IpadSize" cols="10" class="pt-5 d-flex flex-row">
                <span style="font-size: 16px; color: #333333" class="pt-2 pr-2">Package :</span>
                <v-col class="pa-0" cols="3"><v-select :disabled="tableData.length === 0 && checkData === false" @change="getDataTable" v-model="selectedPackage" :items="packages" item-text="detail" item-value="package_code" dense outlined hide-details style="font-size: 14px;"></v-select></v-col>
                <span style="font-size: 16px; color: #333333" class="pt-2 pr-2 pl-6">สถานะการใช้งาน :</span>
                <v-col class="pa-0" cols="3"><v-select :disabled="tableData.length === 0 && checkData === false" @change="getDataTable" v-model="selectedStatus" :items="zortStatus === 'N' ? status : statusZort" item-text="statusName" item-value="value" dense outlined hide-details style="font-size: 14px;"></v-select></v-col>
              </v-col>
              <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 d-flex flex-row">
                <span style="font-size: 16px; color: #333333" class="pt-2 pr-15">Package :</span>
                <v-col class="pa-0" cols="7"><v-select :disabled="tableData.length === 0 && checkData === false" @change="getDataTable" v-model="selectedPackage" :items="packages" item-text="detail" item-value="package_code" dense outlined hide-details style="font-size: 14px;"></v-select></v-col>
              </v-col>
              <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-5 d-flex flex-row">
                <span style="font-size: 16px; color: #333333" class="pt-2 pr-3">สถานะการใช้งาน :</span>
                <v-col class="pa-0" cols="7"><v-select :disabled="tableData.length === 0 && checkData === false" @change="getDataTable" v-model="selectedStatus" :items="zortStatus === 'N' ? status : statusZort" item-text="statusName" item-value="value" dense outlined hide-details style="font-size: 14px;"></v-select></v-col>
              </v-col>
            </v-row>
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 12" :class="IpadSize || MobileSize ? 'pt-4 d-flex flex-column' : 'pt-6 d-flex flex-row'">
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200;" v-if="(!MobileSize && !IpadSize)">รายการร้านค้าเชื่อมต่อ ทั้งหมด {{ countShop }} ร้านค้า</span>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายการร้านค้าเชื่อมต่อ ทั้งหมด {{ countShop }} ร้านค้า</span>
                  <v-spacer v-if="(!MobileSize && !IpadSize)"></v-spacer>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200; color: #989898;" v-if="(!MobileSize && !IpadSize) && checkData === true">จำนวนการใช้งานทั้งหมด : <span style="color: black;">{{ amountTransaction }}  Transaction</span></span>
                  <span class="pb-0 pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200; color: #989898;" v-if="(MobileSize || IpadSize) && checkData === true">จำนวนการใช้งานทั้งหมด : <span style="color: black;">{{ amountTransaction }}  Transaction</span></span>
                  <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200; color: #989898;" v-if="(MobileSize || IpadSize)">จำนวนร้านค้าเชื่อมต่อทั้งหมด : <span style="color: black;">{{ amountShop }}  ร้านค้า</span></span> -->
                </v-col>
            </v-row>
            <v-row dense v-if="noInfo === false">
              <v-col cols="12">
                <v-data-table
                  :headers="header"
                  :items="filteredData"
                  :search="search"
                  class="elevation-1 mt-4"
                  :items-per-page="10"
                  style="width:100%;"
                  height="100%">
                  <template v-slot:[`item.status`]="{item}">
                    <v-row dense class="align-center">
                      <v-col :cols="MobileSize ? 1 : 2">
                        <v-switch v-if="item.status === 'active' || item.status === 'cancel'" false-value="cancel" true-value="active" dense v-model="item.status" inset @click="SwitchActiver(item)" readonly></v-switch>
                      </v-col>
                      <v-col :cols="MobileSize ? 11 : 8">
                        <v-chip small :color="backgroundcolorPartner(item.status)" :text-color="textColor(item.status)" style="font-weight: bold; font-size: 12px;">
                          {{ statusShop(item.status)}}
                        </v-chip>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.transaction`]="{item}">
                    <span>{{ item.transaction === null ? '0' : item.transaction }}</span>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" v-else>
                  <v-col cols="12" align="center">
                    <div class="my-5">
                      <v-img
                          :src="checkData === false ? require('@/assets/noinfo.png') : require('@/assets/noExist.png')"
                          max-height="500px"
                          max-width="500px"
                          height="100%"
                          width="100%"
                          contain
                          aspect-ratio="2">
                      </v-img>
                    </div>
                      <h2 v-if="checkData === false" style="padding-top: 20px; padding-bottom: 50px; color: #9A9A9A">
                        <span>ไม่มีข้อมูลรายการร้านค้าเชื่อมต่อ</span>
                      </h2>
                      <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #9A9A9A" class="d-flex flex-column">
                        <span>ไม่พบข้อมูลร้านค้าที่เชื่อมบริการ ที่คุณค้นหา</span>
                        <span class="mt-2">กรุณาค้นหาใหม่อีกครั้ง</span>
                      </h2>
                  </v-col>
              </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- await confirm -->
    <v-dialog v-model="modalAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="modalAwaitConfirm = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ TextSwitch }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span><br/>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คำสั่งซื้อรายการนี้</span> -->
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="modalAwaitConfirm = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmDialogActiver()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- success -->
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ TextSwitchSuccess }}</b></p>
            <!-- <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ TextDetailSuccess }}</b></p> -->
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ TextDetailSuccess }}</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeDialogSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  data () {
    return {
      dialogSuccess: false,
      TextSwitch: '',
      TextSwitchSuccess: '',
      TextDetailSuccess: '',
      modalAwaitConfirm: false,
      switchPartner: [],
      zortStatus: '',
      taxId: '',
      checkData: false,
      noInfo: true,
      sellerName: '',
      // idTest: null,
      id: null,
      val: [],
      search: '',
      header: [
        { text: 'ชื่อร้านค้าที่เชื่อมบริการ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail', width: '180', value: 'name_th' },
        { text: 'Package', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail', width: '120', value: 'package_name' },
        { text: 'สถานะ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail', width: '220', value: 'status' },
        { text: 'วันที่เข้าร่วมบริการ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail', width: '140', value: 'created_at' },
        { text: 'Transaction', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail', width: '120', value: 'transaction' },
        { text: 'งวดค้างชำระ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail', width: '120', value: 'unPaidShop' }
      ],
      // tableData2: [
      //   { id: 1, partnerName: 'ร้าน Double Plus', package: 'Large', status: 'yes', joinTime: '15/12/2024', transaction: 1000 },
      //   { id: 2, partnerName: 'ร้าน  DD Plus', package: 'Small', status: 'yes', joinTime: '11/12/2024', transaction: 1000 },
      //   { id: 3, partnerName: 'ร้าน  PPlus', package: 'Small', status: 'yes', joinTime: '10/12/2024', transaction: 1000 },
      //   { id: 4, partnerName: 'ร้าน  Double DD', package: 'Small', status: 'yes', joinTime: '01/11/2024', transaction: 1000 },
      //   { id: 5, partnerName: 'ร้าน Double Plus', package: 'Small', status: 'no', joinTime: '01/11/2024', transaction: 1000 },
      //   { id: 6, partnerName: 'ร้าน  Small D', package: 'Small', status: 'yes', joinTime: '20/10/2024', transaction: 1000 },
      //   { id: 7, partnerName: 'ร้าน  Sound T', package: 'Medium', status: 'no', joinTime: '20/10/2024', transaction: 1000 },
      //   { id: 8, partnerName: 'ร้าน  BB Plus', package: 'Medium', status: 'no', joinTime: '01/10/2024', transaction: 1000 },
      //   { id: 9, partnerName: 'ร้าน PURIN', package: 'Large', status: 'yes', joinTime: '01/10/2024', transaction: 1000 },
      //   { id: 10, partnerName: 'ร้าน Santa', package: 'Large', status: 'yes', joinTime: '01/10/2024', transaction: 1000 }
      // ],
      tableData: [],
      packages: [],
      status: [
        { statusName: 'ทั้งหมด', value: '' },
        { statusName: 'กำลังใช้งาน', value: 'active' },
        { statusName: 'รอการติดต่อกลับ', value: 'waiting' },
        { statusName: 'ปิดการใช้งาน', value: 'inactive' },
        { statusName: 'ระงับการใช้งาน', value: 'cancel' },
        { statusName: 'ไม่มีการเชื่อมต่อ', value: 'pending' },
        { statusName: 'ยกเลิกการใช้งาน', value: 'deleted' }
      ],
      statusZort: [
        { statusName: 'ทั้งหมด', value: '' },
        { statusName: 'กำลังใช้งาน', value: 'active' },
        { statusName: 'รอการติดต่อกลับ', value: 'zort_waiting' },
        { statusName: 'ปิดการใช้งาน', value: 'inactive' },
        { statusName: 'ระงับการใช้งาน', value: 'cancel' },
        { statusName: 'ไม่มีการเชื่อมต่อ', value: 'pending' },
        { statusName: 'ยกเลิกการใช้งาน', value: 'deleted' }
      ],
      selectedPackage: '',
      selectedStatus: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredData () {
      // กรองข้อมูลตาม search
      return this.tableData.filter(item =>
        item.name_th.toLowerCase().includes(this.search.trim().toLowerCase())
      )
    },
    countShop () {
    // แสดงจำนวนร้านค้าที่เหลืออยู่หลังการค้นหา
      return this.filteredData.length
    },
    amountTransaction () {
      return this.filteredData.reduce((sum, item) => sum + (item.transaction || 0), 0)
    },
    noInfoComputed () {
      return this.filteredData.length === 0
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/shopConnectedMobile' }).catch(() => {})
      } else {
        localStorage.setItem('pathBusiness', 'shopConnected')
        this.$router.push({ path: '/shopConnected' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    },
    filteredData (newValue) {
      this.noInfo = newValue.length === 0
    },
    search () {
      this.noInfo = this.noInfoComputed
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$EventBus.$on('shop-id', data => {
    //   this.idTest = data
    //   // this.getDataTable(this.idTest)
    //   console.log(this.idTest, 'idTest')
    //   this.getDataTable(this.idTest)
    // })
  },
  async created () {
    // var response = this.$store.state.ModuleAdminManage.stateSellerShopID
    // this.idTest = response
    // console.log(this.idTest, 'id')
    // this.id = this.$route.query.id
    // this.id = '001'
    await this.getTaxId()
    // await this.getListPackages()
    // await this.getDataTable()
    // if (this.tableData.length > 0) {
    //   this.checkData = true
    // }
  },
  methods: {
    textColor (val) {
      if (val === 'active') {
        return 'green'
      } else if (val === 'waiting' || val === 'zort_waiting') {
        return '#eead2a'
      } else if (val === 'cancel') {
        return '#9A9A9A'
      } else {
        return 'red'
      }
    },
    backgroundcolorPartner (val) {
      if (val === 'active') {
        return '#F0F9EE'
      } else if (val === 'waiting' || val === 'zort_waiting') {
        return '#fcf0da'
      } else if (val === 'cancel') {
        return '#EBEBEB'
      } else if (val === 'deleted') {
        return '#FEF0E8'
      } else {
        return '#FEE7E8'
      }
    },
    closeDialogSuccess () {
      this.dialogSuccess = false
      this.getDataTable()
    },
    SwitchActiver (item) {
      this.modalAwaitConfirm = true
      this.TextSwitch = item.status === 'active' ? 'ยืนยันระงับการใช้บริการ' : 'ยืนยันยกเลิกระงับการใช้บริการ'
      this.switchPartner = []
      this.switchPartner = item
    },
    async confirmDialogActiver () {
      this.modalAwaitConfirm = false
      this.$store.commit('openLoader')
      var data = {
        partner_code: this.switchPartner.partner_code,
        seller_shop_id: this.switchPartner.seller_shop_id,
        package_code: this.switchPartner.package_name,
        status: this.switchPartner.status === 'active' ? 'cancel' : 'active'
      }
      await this.$store.dispatch('actionschangeStatusPackage', data)
      var response = await this.$store.state.ModuleBusiness.statechangeStatusPackage
      if (response.result === 'SUCCESS') {
        this.TextSwitchSuccess = this.switchPartner.status === 'active' ? 'ระงับการใช้บริการเสร็จสิ้น' : 'ยกเลิกระงับการใช้บริการเสร็จสิ้น'
        this.TextDetailSuccess = this.switchPartner.status === 'active' ? 'คุณได้ทำการระงับการใช้บริการชั่วคราวเรียบร้อย' : 'คุณได้ทำการยกเลิกระงับการใช้บริการเรียบร้อย'
        this.dialogSuccess = true
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่สามารถทำรายการได้',
          showConfirmButton: false,
          timer: 2500
        })
        this.$store.commit('closeLoader')
      }
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          await this.getPartnerCode(this.taxId)
          await this.getListPackages()
          await this.getDataTable()
          if (this.tableData.length > 0) {
            this.checkData = true
          }
          // this.taxId = response.data.array_business[0].owner_tax_id
        }
        this.$store.commit('closeLoader')
      }
    },
    async getPartnerCode (taxID) {
      var data = {
        id_card_num: taxID
      }
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        this.id = response.data.partner_code
      } else {
        this.id = ''
        // this.$swal.fire({
        //   icon: 'error',
        //   text: 'คุณไม่ใช่เจ้าของนิติบุคคล',
        //   showConfirmButton: false,
        //   timer: 2500
        // })
      }
    },
    backtoPageMobile () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    },
    async getListPackages () {
      // this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListPackagePartners')
      var response = await this.$store.state.ModuleAdminManage.stateListPackagePartners
      if (response.code === 200) {
        var packages = response.data
        this.packages = [
          { detail: 'ทั้งหมด', package_code: '' },
          ...Object.keys(packages).map(key => ({
            detail: packages[key].detail, // เปลี่ยนตามโครงสร้าง object
            package_code: packages[key].package_code // เปลี่ยนตามโครงสร้าง object
          }))
        ]
      } else {
        this.packages = [
          { package: 'ทั้งหมด', value: '' },
          { package: 'Small', value: 'PACK-S' },
          { package: 'Medium', value: 'PACK-M' },
          { package: 'Large', value: 'PACK-L' }
        ]
      }
      // this.$store.commit('closeLoader')
    },
    async getDataTable () {
      this.$store.commit('openLoader')
      var data = {
        partner_code: this.id,
        status: this.selectedStatus,
        package: this.selectedPackage
      }
      await this.$store.dispatch('actionsGetPartnerDetails', data)
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerDetails
      if (response.code === 200) {
        this.tableData = response.data.formattedShopAdmin
        this.zortStatus = response.data.checkzort
        if (this.tableData.length > 0) {
          this.noInfo = false
        } else {
          this.noInfo = true
        }
      } else {
        this.zortStatus = 'N'
        this.tableData = []
        this.noInfo = true
      }
      this.$store.commit('closeLoader')
    },
    statusShop (val) {
      if (val === 'active') {
        return 'กำลังใช้งาน'
      } else if (val === 'waiting') {
        return 'รอการติดต่อกลับ'
      } else if (val === 'zort_waiting') {
        return 'รออนุมัติการใช้งาน'
      } else if (val === 'inactive') {
        return 'ปิดการใช้งาน'
      } else if (val === 'cancel') {
        return 'ระงับการใช้งาน'
      } else if (val === 'deleted') {
        return 'ยกเลิกการใช้งาน'
      } else if (val === 'pending') {
        return 'ไม่มีการเชื่อมต่อ'
      }
    }
  }
}
</script>
<!-- <style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 10;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style> -->
