<template>
  <v-container>
    <!-- comfirmEditCompany -->
    <v-dialog v-model="comfirmEditCompany" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 4px;">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''"><b>แก้ไขข้อมูลบริษัท</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="comfirmEditCompany = !comfirmEditCompany" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text style="text-align: center;">
            <p class="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">คุณได้ทำการแก้ไขขัอมูล{{ dataCompany.name_th }}</p>
            <p class="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">คุณต้องการทำรายการนี้ ใช่หรือไม่</p>
          </v-card-text>
          <v-card-actions class="justify-center">
            <v-btn outlined color="#27AB9C" style="border-radius: 8px; font-weight: 400; font-size: 16px; line-height: 24px;" @click="comfirmEditCompany = !comfirmEditCompany" class="px-5">ยกเลิก</v-btn>
            <v-btn color="#27AB9C" dark style="border-radius: 8px; font-weight: 400; font-size: 16px; line-height: 24px;" @click="editCompany()" class="px-6">ตกลง</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Setting Company -->
    <v-dialog v-model="modalSuccessSettingCompany" width="373">
      <v-card style="background: #FFFFFF; border-radius: 4px;" width="100%">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row>
            <v-col class="d-flex justify-space-around"></v-col>
          </v-row>
          <v-btn fab small @click="modalSuccessSettingCompany = !modalSuccessSettingCompany" icon><v-icon color="#A1A1A1">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text style="text-align: center;" class="mt-4">
          <v-row dense justify="center">
            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
          </v-row>
          <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;" class="mt-8"><b>เสร็จสิ้น</b></p>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-row dense justify="center">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-row justify="start" class="mb-2 ml-3 mt-2">
          <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" v-if="statusPage === 'Create'">เพิ่มข้อมูลบริษัท</span>
          <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" v-else>แก้ไขข้อมูลบริษัท</span>
        </v-row>
      </v-col>
      <v-col cols="12" md="12" sm="12" xs="12" class="px-0 mx-0">
        <v-card elevation="0" width="100%" height="100%">
          <v-form ref="form" v-model="valid" lazy-validation>
            <v-card-text>
              <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="3" md="1" sm="2">
                      <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="40" height="40"></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="8" md="10" sm="9" class="mt-2">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">ข้อมูลบริษัท</span><br/>
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Company Detail)</span>
                    </v-col>
                  </v-row>
                  <v-row dense class="mt-4 mb-4">
                    <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                  </v-row>
                  <v-row dense>
                    <v-col cols="12" class="px-0">
                      <v-card elevation="0" outlined width="100%" height="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                        <v-card-text>
                          <v-row dense>
                            <v-col cols="12">
                              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">เครื่องหมายบริษัท</span>
                            </v-col>
                          </v-row>
                          <!-- เพิ่มรูปภาพ -->
                          <v-row dense>
                            <v-col cols="12">
                              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                                <v-card-text>
                                  <v-card
                                    elevation="0"
                                    style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                                    @click="onPickFile()"
                                  >
                                    <v-card-text>
                                      <v-row
                                        no-gutters
                                        align="center"
                                        justify="center"
                                        style="cursor: pointer;"
                                      >
                                        <v-file-input
                                          v-model="DataImage"
                                          :items="DataImage"
                                          accept="image/jpeg, image/jpg, image/png"
                                          @change="UploadImage()"
                                          id="file_input"
                                          multiple
                                          :clearable="false"
                                          style="display:none"
                                        >
                                        </v-file-input>
                                        <v-col cols="12" md="12" class="mb-6">
                                          <v-row justify="center" class="pt-10">
                                            <v-img
                                              src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                              width="280.34"
                                              height="154.87"
                                              contain
                                            ></v-img>
                                          </v-row>
                                        </v-col>
                                        <v-col cols="12" md="12" class="mt-6">
                                          <v-row justify="center" align="center">
                                            <v-col cols="12" md="12" style="text-align: center;">
                                              <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br/>
                                              <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br/>
                                              <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ไฟล์นามสกุล .JPEG,PNG เพิ่มได้สูงสุด 1 รูปภาพ)</span><br/>
                                              <!-- <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'"><span style="color: red;">***</span> หมายเหตุ ไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span> -->
                                            </v-col>
                                          </v-row>
                                        </v-col>
                                      </v-row>
                                    </v-card-text>
                                  </v-card>
                                  <div v-if="Detail.product_image.length !== 0" class="mt-4">
                                    <draggable v-model="Detail.product_image"  :move="onMove" @start="drag=true" @end="drag=false" :class="MobileSize ? 'pl-0 pr-0' : 'pl-5 pr-5 row  fill-height align-center sortable-list'">
                                      <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="6" md="3" sm="4" :class="MobileSize ? 'pl-0 pr-2' : ''">
                                        <v-card outlined class="pa-1" width="146" height="146" v-if="statusPage !== 'Edit'">
                                          <v-img :src="item.path + '?nochace=' + time" :lazy-src="item.path" width="130" height="130" contain>
                                            <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                              <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                            </v-btn>
                                          </v-img>
                                        </v-card>
                                        <v-card outlined class="pa-1" width="146" height="146" v-else-if="statusPage === 'Edit' && Detail.product_image[0].media_path !== null">
                                          <v-img :src="item.media_path" :lazy-src="item.media_path" width="130" height="130" contain>
                                            <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                              <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                            </v-btn>
                                          </v-img>
                                        </v-card>
                                      </v-col>
                                    </draggable>
                                  </div>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </v-row>
                          <!-- ส่วนกรอกข้อมูลแถว 1 -->
                          <v-row dense class="mt-6">
                            <v-col cols="12" md="4" sm="12">
                              <span :style="IpadSize ? 'font-size: 14px;' : ''">รหัสบริษัท</span><span style="color: red;"> *</span>
                              <v-text-field v-model="companyCode" :disabled="statusPage === 'Edit' ? true : false" dense placeholder="ระบุรหัสบริษัท (สูงสุด 15 ตัวอักษร)"  outlined maxLength="15" :rules="Rules.company_code"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="6">
                              <span :style="IpadSize ? 'font-size: 14px;' : ''">ชื่อบริษัท (ภาษาไทย) <span style="color: red;">*</span></span>
                              <v-text-field v-model="companyNameTH" :disabled="statusPage === 'Edit' ? true : false" dense placeholder="ระบุชื่อบริษัทไม่เกิน 30 ตัวอักษร" outlined maxLength="30" :rules="Rules.CompanyNameTHRules"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="6">
                              <span :style="IpadSize ? 'font-size: 14px;' : ''">ชื่อบริษัท (ภาษาอังกฤษ) <span style="color: red;">*</span></span>
                              <v-text-field v-model="companyNameEN"  :disabled="statusPage === 'Edit' ? true : false" dense placeholder="ระบุชื่อบริษัทไม่เกิน 30 ตัวอักษร" outlined></v-text-field>
                            </v-col>
                          </v-row>
                          <!-- ส่วนกรอกข้อมูลแถว 2 -->
                          <v-row dense>
                            <v-col cols="12" md="3" sm="6">
                              <span>เลขประจำตัวผู้เสียภาษีอากร </span><span style="color: red;"> *</span>
                              <v-text-field v-model="taxID" :disabled="statusPage === 'Edit' ? true : false" dense placeholder="ระบุเลขประจำตัวผู้เสียภาษี 13 หลัก" :rules="Rules.TexID" outlined maxLength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="6">
                              <span>หมายเลขโทรศัพท์บริษัท <span style="color: red;">*</span></span>
                              <v-text-field v-model="companyTel" dense outlined placeholder="ระบุหมายเลขโทรศัพท์บริษัท" :rules="Rules.tel"
                              oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="6">
                              <span>หมายเลขโทรศัพท์มือถือ</span>
                              <v-text-field v-model="tel" dense outlined maxLength="10" placeholder="ระบุหมายเลขโทรศัพท์ 10 หลัก" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="6">
                              <span>หมายเลขแฟกซ์</span>
                              <v-text-field v-model="fax" dense outlined maxLength="9" placeholder="ระบุหมายเลขแฟกซ์" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- ส่วนเพิ่มที่อยู่ -->
              <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px; margin-top: 40px;">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="3" md="1" sm="2">
                      <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/map.png" contain width="40" height="40"></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="8" md="10" sm="9" class="mt-2">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">ที่อยู่จัดส่ง</span><br/>
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Address)</span>
                    </v-col>
                  </v-row>
                  <v-row dense class="mt-4 mb-4">
                    <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                  </v-row>
                  <v-row dense>
                    <v-col cols="12" class="px-0">
                      <v-card elevation="0" outlined width="100%" height="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                        <v-card-text>
                          <!-- ส่วนกรอกข้อมูลแถว 1 -->
                          <v-row dense class="mt-4">
                            <v-col cols="12" md="5" sm="8">
                              <span>รายละเอียดที่อยู่</span><span style="color: red;"> *</span>
                              <v-text-field  v-model="address" placeholder="ระบุรายละเอียดที่อยู่" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.address"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="2" sm="2">
                              <span>ห้องเลขที่</span>
                              <v-text-field  v-model="roomNo" placeholder="ระบุห้องเลขที่" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="2" sm="2">
                              <span>ชั้น</span>
                              <v-text-field  v-model="floor" placeholder="ระบุชั้น" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="6">
                              <span>อาคาร</span>
                              <v-text-field  v-model="buildingName" placeholder="ระบุอาคาร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="3" v-if="IpadSize">
                              <span>หมู่บ้าน</span>
                              <v-text-field  v-model="houseName" placeholder="ระบุหมู่บ้าน" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="3" v-if="IpadSize">
                              <span>หมู่ที่</span>
                              <v-text-field  v-model="houseGroup" placeholder="ระบุหมู่ที่" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                          </v-row>
                          <!-- ส่วนกรอกข้อมูลแถว 2 -->
                          <v-row dense>
                            <v-col cols="12" md="4" v-if="!IpadSize">
                              <span>หมู่บ้าน</span>
                              <v-text-field  v-model="houseName" placeholder="ระบุหมู่บ้าน" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="2" v-if="!IpadSize">
                              <span>หมู่ที่</span>
                              <v-text-field  v-model="houseGroup" placeholder="ระบุหมู่ที่" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="4">
                              <span>ตรอก/ซอย</span>
                              <v-text-field  v-model="alley" placeholder="ระบุตรอก/ซอย" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="4">
                              <span>แยก</span>
                              <v-text-field v-model="cross" placeholder="ระบุแยก" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="4" v-if="IpadSize">
                              <span>ถนน</span>
                              <v-text-field v-model="road" placeholder="ระบุถนน" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                          </v-row>
                          <!-- ส่วนกรอกข้อมูลแถว 3 -->
                          <v-row dense>
                            <v-col cols="12" md="3" v-if="!IpadSize">
                              <span>ถนน</span>
                              <v-text-field v-model="road" placeholder="ระบุถนน" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="4" sm="6">
                              <span>แขวง/ตำบล <span style="color: red;">*</span></span>
                              <addressinput-subdistrict :rules="Rules.empty" label="" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล" />
                              <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                            </v-col>
                            <v-col cols="12" md="5" sm="6">
                              <span>เขต/อำเภอ <span style="color: red;">*</span></span>
                              <addressinput-district label="" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ" />
                              <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                            </v-col>
                          </v-row>
                          <!-- ส่วนกรอกข้อมูลแถว 4 -->
                          <v-row dense>
                            <v-col cols="12" md="6" sm="6">
                              <span>จังหวัด <span style="color: red;">*</span></span>
                              <addressinput-province label="" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="province" placeholder="ระบุจังหวัด" />
                              <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                            </v-col>
                            <v-col cols="12" md="6" sm="6">
                              <span>รหัสไปรษณีย์ <span style="color: red;">*</span></span>
                              <addressinput-zipcode numbered label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                              <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- เพิ่มบุคคล Start -->
              <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px; margin-top: 40px;">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="3" md="1" sm="2">
                      <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/industry_1.png" contain width="40" height="40"></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="8" md="10" sm="9" class="mt-2">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">บุคคล</span><br/>
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Personal)</span>
                    </v-col>
                  </v-row>
                  <v-row dense class="mt-4 mb-4">
                    <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                  </v-row>
                  <v-row dense>
                    <v-col cols="12" class="px-0">
                      <v-card elevation="0" outlined width="100%" height="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                        <v-card-text>
                          <v-row dense no-gutters class="mt-4">
                            <!-- ชื่อผู้ขอซื้อ -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">ชื่อผู้ขอซื้อ</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense :rules="Rules.Name"></v-text-field> -->
                              <v-text-field  v-model="buyer_name" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span><span style="color: red;"></span>
                              <!-- <v-text-field maxlength="10" v-model="Phone_Buyer" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Phone"></v-text-field> -->
                              <v-text-field maxlength="10" v-model="buyer_phone" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Position_Buyer" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Position"></v-text-field> -->
                              <v-text-field  v-model="buyer_position" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Email_Buyer" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense  :rules="Rules.Email"></v-text-field> -->
                              <v-text-field  v-model="buyer_email" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <!-- หัวหน้าผู้ขอซื้อ -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">หัวหน้าผู้ขอซื้อ</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense :rules="Rules.Name"></v-text-field> -->
                              <v-text-field  v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span><span style="color: red;"></span>
                              <!-- <v-text-field maxlength="10" v-model="Phone_Buyer" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Phone"></v-text-field> -->
                              <v-text-field maxlength="10" v-model="Phone_Buyer" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Position_Buyer" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Position"></v-text-field> -->
                              <v-text-field  v-model="Position_Buyer" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Email_Buyer" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense  :rules="Rules.Email"></v-text-field> -->
                              <v-text-field  v-model="Email_Buyer" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <!-- คณะผู้ตรวจรับ 1 -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 1</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Name_Audit1" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense :rules="Rules.Name"></v-text-field> -->
                              <v-text-field  v-model="Name_Audit1" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span><span style="color: red;"></span>
                              <!-- <v-text-field maxlength="10" v-model="Phone_Audit1" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Phone"></v-text-field> -->
                              <v-text-field maxlength="10" v-model="Phone_Audit1" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Position_Audit1" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Position"></v-text-field> -->
                              <v-text-field  v-model="Position_Audit1" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Email_Audit1" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense  :rules="Rules.Email"></v-text-field> -->
                              <v-text-field  v-model="Email_Audit1" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense ></v-text-field>
                            </v-col>
                            <!-- คณะผู้ตรวจรับ 2 -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 2</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Name_Audit2" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense :rules="Rules.Name"></v-text-field> -->
                              <v-text-field  v-model="Name_Audit2" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span><span style="color: red;"></span>
                              <!-- <v-text-field maxlength="10" v-model="Phone_Audit2" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Phone"></v-text-field> -->
                              <v-text-field maxlength="10" v-model="Phone_Audit2" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense ></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Position_Audit2" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Position"></v-text-field> -->
                              <v-text-field  v-model="Position_Audit2" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Email_Audit2" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense  :rules="Rules.Email"></v-text-field> -->
                              <v-text-field  v-model="Email_Audit2" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense ></v-text-field>
                            </v-col>
                            <!-- คณะกรรมการจัดหา 1 -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะกรรมการจัดหา 1</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                            <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <v-text-field  v-model="Name_SupplyBoard1" placeholder="ระบุชื่อ-สกุล" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span>
                              <v-text-field  v-model="Phone_SupplyBoard1" placeholder="ระบุเบอร์โทร" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span>
                              <v-text-field  v-model="Position_SupplyBoard1" placeholder="ระบุตำแหน่ง" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span>
                              <v-text-field  v-model="Email_SupplyBoard1" placeholder="ระบุอีเมล" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '')"></v-text-field>
                            </v-col>
                            <!-- คณะกรรมการจัดหา 2 -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">คณะกรรมการจัดหา 2</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <v-text-field  v-model="Name_SupplyBoard2" placeholder="ระบุชื่อ-สกุล" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span>
                              <v-text-field  v-model="Phone_SupplyBoard2" placeholder="ระบุเบอร์โทร" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span>
                              <v-text-field  v-model="Position_SupplyBoard2" placeholder="ระบุตำแหน่ง" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span>
                              <v-text-field  v-model="Email_SupplyBoard2" placeholder="ระบุอีเมล" outlined dense oninput="this.value = this.value.replace(/^[\s]/, '')"></v-text-field>
                            </v-col>
                            <!-- ฝ่ายการเงิน -->
                            <v-col cols="12" md="12" sm="12">
                              <span style="font-size: 16px;font-weight: 600;">ฝ่ายการเงิน</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Name_Finance" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense :rules="Rules.Name"></v-text-field> -->
                              <v-text-field  v-model="Name_Finance" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span><span style="color: red;"></span>
                              <!-- <v-text-field maxlength="10" v-model="Phone_Finance" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Phone"></v-text-field> -->
                              <v-text-field maxlength="10" v-model="Phone_Finance" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span><span style="color: red;"></span>
                              <!-- <v-text-field v-model="Position_Finance" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Position"></v-text-field> -->
                              <v-text-field v-model="Position_Finance" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Email_Finance" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense  :rules="Rules.Email"></v-text-field> -->
                              <v-text-field  v-model="Email_Finance" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <!-- ฝ่ายเทคนิค -->
                            <v-col cols="12" md="12" sm="12" class="mt-2">
                              <span style="font-size: 16px;font-weight: 600;">ฝ่ายเทคนิค</span>
                            </v-col>
                            <v-col cols="12" md="5" sm="8">
                              <span>ชื่อ-สกุล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Name_Technical" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense :rules="Rules.Name"></v-text-field> -->
                              <v-text-field  v-model="Name_Technical" placeholder="ระบุชื่อ-สกุล" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^A-zก-๙\s]/, '')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>เบอร์โทร</span><span style="color: red;"></span>
                              <!-- <v-text-field maxlength="10" v-model="Phone_Technical" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Phone"></v-text-field> -->
                              <v-text-field maxlength="10" v-model="Phone_Technical" placeholder="ระบุเบอร์โทร" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="3" sm="2" class="ml-2">
                              <span>ตำแหน่ง</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Position_Technical" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense :rules="Rules.Position"></v-text-field> -->
                              <v-text-field  v-model="Position_Technical" placeholder="ระบุตำแหน่ง" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')" outlined dense></v-text-field>
                            </v-col>
                            <v-col cols="12" md="7" sm="2">
                              <span>อีเมล</span><span style="color: red;"></span>
                              <!-- <v-text-field  v-model="Email_Technical" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense :rules="Rules.Email"></v-text-field> -->
                              <v-text-field  v-model="Email_Technical" placeholder="ระบุอีเมล" oninput="this.value = this.value.replace(/^[\s]/, '')" outlined dense ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              <!-- เพิ่มบุคคล End -->
            </v-card-text>
            <v-card-actions>
              <v-row justify="end" dense class="mr-2">
                <v-btn text outlined class="pr-8 pl-8 mr-4" color="primary" style="border: 1px solid #27AB9C;" v-if="statusPage !== 'Edit'" @click="BackToCompany('Create')">ยกเลิก</v-btn>
                <v-btn text outlined class="pr-8 pl-8 mr-4" color="primary" style="border: 1px solid #27AB9C;" v-else @click="BackToCompany('Edit')">ยกเลิก</v-btn>
                <v-btn color="primary" class="pr-8 pl-8" @click="confirm()" v-if="statusPage !== 'Edit'">บันทึก</v-btn>
                <v-btn color="primary" class="pr-8 pl-8" @click="confirmEdit()" v-else>บันทึก</v-btn>
              </v-row>
            </v-card-actions>
          </v-form>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import draggable from 'vuedraggable'
import { Encode, Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  components: { draggable },
  data () {
    return {
      Name_SupplyBoard1: '',
      Phone_SupplyBoard1: '',
      Position_SupplyBoard1: '',
      Email_SupplyBoard1: '',
      Name_SupplyBoard2: '',
      Phone_SupplyBoard2: '',
      Position_SupplyBoard2: '',
      Email_SupplyBoard2: '',
      buyer_name: '',
      buyer_phone: '',
      buyer_position: '',
      buyer_email: '',
      comfirmEditCompany: false,
      modalSuccessSettingCompany: false,
      statusPage: '',
      dataCompany: '',
      stepper: 1,
      valid: true,
      valid1: true,
      Name_Finance: '',
      Phone_Finance: '',
      Position_Finance: '',
      Email_Finance: '',
      Name_Technical: '',
      Phone_Technical: '',
      Position_Technical: '',
      Email_Technical: '',
      Name_Buyer: '',
      Phone_Buyer: '',
      Position_Buyer: '',
      Email_Buyer: '',
      Name_Audit1: '',
      Phone_Audit1: '',
      Position_Audit1: '',
      Email_Audit1: '',
      Name_Audit2: '',
      Phone_Audit2: '',
      Position_Audit2: '',
      Email_Audit2: '',
      DepartmentTypeItem: [
        { nameTH: '2 ระดับ, แผนก', value: 2 },
        { nameTH: '3 ระดับ, ฝ่ายและแผนก', value: 3 }
      ],
      showImage: '',
      imageName: '',
      LogoCompany: '',
      companyCode: '',
      customerCode: '',
      companyNameTH: '',
      companyNameEN: '',
      roomNo: '',
      floor: '',
      buildingName: '',
      houseName: '',
      houseGroup: '',
      alley: '',
      cross: '',
      road: '',
      taxID: '',
      companyTel: '',
      tel: '',
      fax: '',
      departmentType: null,
      address1: '',
      address: '',
      address2: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      imageBase: '',
      DataImage: [],
      Detail: {
        product_image: [],
        company_logo: '',
        company_code: '',
        company_name_th: '',
        company_name_en: '',
        company_tax_id: '',
        company_phone: '',
        company_tel: '',
        company_fax: '',
        company_address: {
          house_no: '',
          room_no: '',
          floor: '',
          building_name: '',
          moo_ban: '',
          moo_no: '',
          soi: '',
          yaek: '',
          street: '',
          sub_district: '',
          district: '',
          province: '',
          zip_code: '',
          time: ''
        },
        json_personal: [
          {
            financial: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            technique: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            purchasing_chief: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            inspectors_one: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            inspectors_two: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            supply_board_one: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            supply_board_two: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ],
            json_buyer: [
              {
                name: '',
                phone: '',
                email: '',
                position: ''
              }
            ]
          }
        ]
      },
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        company_code: [
          v => !!v || 'กรุณากรอกรหัสบริษัท'
        ],
        customer_code: [
          v => !!v || 'กรุณากรอกรหัสลูกค้า'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        TexID: [
          v => !!v || 'กรุณากรอกรหัสประจำตัวผู้เสียภาษี'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        address2: [
          v => !!v || 'กรุณาระบุแขวง/เขต หรือ ตำบล/อำเภอ'
        ],
        Name: [
          v => !!v || 'กรุณากรอกชื่อ-สกุล',
          v => /[^๑-๙฿@#$%&*()_+{}:;<^>,.?~]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรเท่านั้น'
        ],
        Phone: [
          v => !!v || 'กรุณากรอกเบอร์โทร'
        ],
        Position: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        Email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        CompanyNameTHRules: [
          v => !!v || 'กรุณากรอกชื่อบริษัท(ไทย)',
          v => /^[ก-๏0-9-()\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        CompanyNameENRules: [
          v => !!v || 'กรุณากรอกชื่อบริษัท(อังกฤษ)',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        province: [
          v => !!v || 'กรุณาระบุจังหวัด'
        ],
        zipcode: [
          v => !!v || 'กรุณาระบุรหัสไปรษณีย์'
        ]
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.statusPage = this.$route.query.Status
    if (this.$route.query.Status === 'Edit') {
      this.getTime()
      this.setDataEditCompany()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  watch: {
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  methods: {
    getTime () {
      this.time = (new Date()).getTime() + Math.round(Math.random())
      // return (new Date()).getTime() + Math.round(Math.random())
    },
    backtoCompany () {
      this.$router.push({ path: '/Company' }).catch(() => {})
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      // console.log(this.Detail.product_image, 'this.DataImage')
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      if (this.Detail.product_image.length < 1) {
        // console.log('url ====>', this.DataImage)
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          // console.log('url ====>', imageSize)
          if (imageSize < 2) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = () => {
              var resultReader = reader.result
              // console.log(resultReader)
              var url = URL.createObjectURL(element)
              if (this.$route.query.Status !== 'Edit') {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  path: url,
                  name: this.DataImage[i].name
                })
                this.Detail.company_logo = resultReader.split(',')[1]
              } else {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  media_path: url,
                  name: this.DataImage[i].name
                })
                this.Detail.company_logo = resultReader.split(',')[1]
              }
              // console.log(this.Detail.product_image, 'this.Detail.product_image')
              // mediaType = this.DataImage[i].type
              // var checkType = mediaType.split('/', 1)
              // if (checkType.toString() === 'video') {
              //   checkType = 'vdo'
              // } else {
              //   checkType = 'image'
              // }
              // this.shop_media.push({
              //   media: element,
              //   media_type: checkType
              // })
              // console.log(this.shop_media, 'this.shop_media')
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
              showConfirmButton: false,
              timer: 1500
            })
          }
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
      // console.log('me', this.Detail.product_image)
    },
    RemoveImage (index, val) {
      this.DataImage = []
      if (this.$route.query.Status === 'Edit') {
        // if (val.id !== undefined) {
        //   this.Detail.remove_img.push({
        //     id: val.id
        //   })
        // }
        this.Detail.product_image.splice(index, 1)
        this.Detail.company_logo = ''
      } else {
        this.Detail.product_image.splice(index, 1)
        this.Detail.company_logo = ''
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    async confirm () {
      if (this.$refs.form.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            if (this.statusPage === 'Create') {
              await this.addCompany()
            } else {
              await this.editCompany()
            }
          } else {
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async confirmEdit () {
      if (this.$refs.form.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            this.comfirmEditCompany = !this.comfirmEditCompany
          } else {
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    BackToCompany (val) {
      if (val === 'Create') {
        if (this.MobileSize) {
          this.$router.push({ path: '/' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Company' }).catch(() => {})
        }
      } else {
        if (this.MobileSize) {
          this.$router.push({ path: '/detailCompanyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        }
      }
    },
    // create company
    async addCompany () {
      this.Detail.company_code = this.companyCode
      this.Detail.company_name_th = this.companyNameTH
      this.Detail.company_name_en = this.companyNameEN
      this.Detail.company_tax_id = this.taxID
      this.Detail.company_phone = this.companyTel
      this.Detail.company_tel = this.tel
      this.Detail.company_fax = this.fax
      this.Detail.company_address.house_no = this.address !== '' ? this.address : '-'
      this.Detail.company_address.room_no = this.roomNo !== '' ? this.roomNo : '-'
      this.Detail.company_address.floor = this.floor !== '' ? this.floor : '-'
      this.Detail.company_address.building_name = this.buildingName !== '' ? this.buildingName : '-'
      this.Detail.company_address.moo_ban = this.houseName !== '' ? this.houseName : '-'
      this.Detail.company_address.moo_no = this.houseGroup !== '' ? this.houseGroup : '-'
      this.Detail.company_address.soi = this.alley !== '' ? this.alley : '-'
      this.Detail.company_address.yaek = this.cross !== '' ? this.cross : '-'
      this.Detail.company_address.street = this.road !== '' ? this.road : '-'
      this.Detail.company_address.sub_district = this.subdistrict
      this.Detail.company_address.district = this.district
      this.Detail.company_address.province = this.province
      this.Detail.company_address.zip_code = this.zipcode
      this.Detail.json_personal[0].financial[0].name = this.Name_Finance
      this.Detail.json_personal[0].financial[0].phone = this.Phone_Finance
      this.Detail.json_personal[0].financial[0].position = this.Position_Finance
      this.Detail.json_personal[0].financial[0].email = this.Email_Finance
      this.Detail.json_personal[0].technique[0].name = this.Name_Technical
      this.Detail.json_personal[0].technique[0].phone = this.Phone_Technical
      this.Detail.json_personal[0].technique[0].position = this.Position_Technical
      this.Detail.json_personal[0].technique[0].email = this.Email_Technical
      this.Detail.json_personal[0].purchasing_chief[0].name = this.Name_Buyer
      this.Detail.json_personal[0].purchasing_chief[0].phone = this.Phone_Buyer
      this.Detail.json_personal[0].purchasing_chief[0].position = this.Position_Buyer
      this.Detail.json_personal[0].purchasing_chief[0].email = this.Email_Buyer
      this.Detail.json_personal[0].inspectors_one[0].name = this.Name_Audit1
      this.Detail.json_personal[0].inspectors_one[0].phone = this.Phone_Audit1
      this.Detail.json_personal[0].inspectors_one[0].position = this.Position_Audit1
      this.Detail.json_personal[0].inspectors_one[0].email = this.Email_Audit1
      this.Detail.json_personal[0].inspectors_two[0].name = this.Name_Audit2
      this.Detail.json_personal[0].inspectors_two[0].phone = this.Phone_Audit2
      this.Detail.json_personal[0].inspectors_two[0].position = this.Position_Audit2
      this.Detail.json_personal[0].inspectors_two[0].email = this.Email_Audit2
      this.Detail.json_personal[0].supply_board_one[0].name = this.Name_SupplyBoard1
      this.Detail.json_personal[0].supply_board_one[0].phone = this.Phone_SupplyBoard1
      this.Detail.json_personal[0].supply_board_one[0].position = this.Position_SupplyBoard1
      this.Detail.json_personal[0].supply_board_one[0].email = this.Email_SupplyBoard1
      this.Detail.json_personal[0].supply_board_two[0].name = this.Name_SupplyBoard2
      this.Detail.json_personal[0].supply_board_two[0].phone = this.Phone_SupplyBoard2
      this.Detail.json_personal[0].supply_board_two[0].position = this.Position_SupplyBoard2
      this.Detail.json_personal[0].supply_board_two[0].email = this.Email_SupplyBoard2
      this.Detail.json_personal[0].json_buyer[0].name = this.buyer_name
      this.Detail.json_personal[0].json_buyer[0].phone = this.buyer_phone
      this.Detail.json_personal[0].json_buyer[0].position = this.buyer_position
      this.Detail.json_personal[0].json_buyer[0].email = this.buyer_email
      await this.$store.dispatch('actionsCreateCompany', this.Detail)
      var response = await this.$store.state.ModuleAdminManage.stateCreateCompany
      // console.log('response data after use api ============>', response)
      if (response.result === 'SUCCESS') {
        if (response.message === 'Create company success.') {
          this.modalSuccessSettingCompany = !this.modalSuccessSettingCompany
          // this.$swal.fire({ icon: 'success', title: 'สร้างบริษัทของคุณสำเร็จ', showConfirmButton: false, timer: 2000 })
          this.$EventBus.$emit('getCompany')
          this.$router.push({ path: '/' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    changePic () {
      document.getElementById('picTure').click()
    },
    // showPicture (e) {
    //   console.log(this.showImage, 'showImage')
    //   const files = e.target.files
    //   console.log(files, 'files')
    //   if (files[0] !== undefined) {
    //     this.imageName = files[0].name
    //     const element = files[0]
    //     const reader = new FileReader()
    //     reader.readAsDataURL(element)
    //     reader.onload = () => {
    //       this.imageBase = reader.result.split(',')[1]
    //       this.showImage = URL.createObjectURL(element)
    //       this.LogoCompany = this.showImage
    //     }
    //     console.log(this.showImage, 'showImage')
    //   }
    // },
    // set data edit company
    setDataEditCompany () {
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('DetailCompany')))
      // console.log(companyData, 'tong')
      this.dataCompany = companyData
      if (companyData.img_path !== null) {
        this.Detail.product_image = [{
          image_data: companyData.img_path,
          media_path: companyData.img_path
        }]
      } else {
        this.Detail.product_image = []
      }
      this.companyCode = companyData.code
      this.companyNameTH = companyData.name_th
      this.companyNameEN = companyData.name_en
      this.taxID = companyData.tax_id
      this.companyTel = companyData.phone
      this.tel = companyData.tel
      this.fax = companyData.fax
      this.address = companyData.company_address.house_no
      this.roomNo = companyData.company_address.room_no
      this.floor = companyData.company_address.floor
      this.buildingName = companyData.company_address.building_name
      this.houseName = companyData.company_address.moo_ban
      this.houseGroup = companyData.company_address.moo_no
      this.alley = companyData.company_address.soi
      this.cross = companyData.company_address.yaek
      this.road = companyData.company_address.street
      this.subdistrict = companyData.company_address.sub_district
      this.district = companyData.company_address.district
      this.province = companyData.company_address.province
      this.zipcode = companyData.company_address.zip_code
      this.Name_Finance = companyData.json_personal[0].financial[0].name
      this.Phone_Finance = companyData.json_personal[0].financial[0].phone
      this.Position_Finance = companyData.json_personal[0].financial[0].position
      this.Email_Finance = companyData.json_personal[0].financial[0].email
      this.Name_Technical = companyData.json_personal[0].technique[0].name
      this.Phone_Technical = companyData.json_personal[0].technique[0].phone
      this.Position_Technical = companyData.json_personal[0].technique[0].position
      this.Email_Technical = companyData.json_personal[0].technique[0].email
      this.Name_Buyer = companyData.json_personal[0].purchasing_chief[0].name
      this.Phone_Buyer = companyData.json_personal[0].purchasing_chief[0].phone
      this.Position_Buyer = companyData.json_personal[0].purchasing_chief[0].position
      this.Email_Buyer = companyData.json_personal[0].purchasing_chief[0].email
      this.Name_Audit1 = companyData.json_personal[0].inspectors_one[0].name
      this.Phone_Audit1 = companyData.json_personal[0].inspectors_one[0].phone
      this.Position_Audit1 = companyData.json_personal[0].inspectors_one[0].position
      this.Email_Audit1 = companyData.json_personal[0].inspectors_one[0].email
      this.Name_Audit2 = companyData.json_personal[0].inspectors_two[0].name
      this.Phone_Audit2 = companyData.json_personal[0].inspectors_two[0].phone
      this.Position_Audit2 = companyData.json_personal[0].inspectors_two[0].position
      this.Email_Audit2 = companyData.json_personal[0].inspectors_two[0].email
      this.Name_SupplyBoard1 = companyData.json_personal[0].supply_board_one[0].name
      this.Phone_SupplyBoard1 = companyData.json_personal[0].supply_board_one[0].phone
      this.Position_SupplyBoard1 = companyData.json_personal[0].supply_board_one[0].position
      this.Email_SupplyBoard1 = companyData.json_personal[0].supply_board_one[0].email
      this.Name_SupplyBoard2 = companyData.json_personal[0].supply_board_two[0].name
      this.Phone_SupplyBoard2 = companyData.json_personal[0].supply_board_two[0].phone
      this.Position_SupplyBoard2 = companyData.json_personal[0].supply_board_two[0].position
      this.Email_SupplyBoard2 = companyData.json_personal[0].supply_board_two[0].email
      this.buyer_name = companyData.json_personal[0].json_buyer[0].name
      this.buyer_phone = companyData.json_personal[0].json_buyer[0].phone
      this.buyer_position = companyData.json_personal[0].json_buyer[0].position
      this.buyer_email = companyData.json_personal[0].json_buyer[0].email
    },
    // Edit Company
    async editCompany () {
      this.Detail.company_id = this.dataCompany.id
      this.Detail.company_code = this.companyCode
      this.Detail.company_name_th = this.companyNameTH
      this.Detail.company_name_en = this.companyNameEN
      this.Detail.company_tax_id = this.taxID
      this.Detail.company_phone = this.companyTel
      this.Detail.company_tel = this.tel
      this.Detail.company_fax = this.fax
      this.Detail.company_address.house_no = this.address !== '' ? this.address : '-'
      this.Detail.company_address.room_no = this.roomNo !== '' ? this.roomNo : '-'
      this.Detail.company_address.floor = this.floor !== '' ? this.floor : '-'
      this.Detail.company_address.building_name = this.buildingName !== '' ? this.buildingName : '-'
      this.Detail.company_address.moo_ban = this.houseName !== '' ? this.houseName : '-'
      this.Detail.company_address.moo_no = this.houseGroup !== '' ? this.houseGroup : '-'
      this.Detail.company_address.soi = this.alley !== '' ? this.alley : '-'
      this.Detail.company_address.yaek = this.cross !== '' ? this.cross : '-'
      this.Detail.company_address.street = this.road !== '' ? this.road : '-'
      this.Detail.company_address.sub_district = this.subdistrict
      this.Detail.company_address.district = this.district
      this.Detail.company_address.province = this.province
      this.Detail.company_address.zip_code = this.zipcode
      this.Detail.json_personal[0].financial[0].name = this.Name_Finance
      this.Detail.json_personal[0].financial[0].phone = this.Phone_Finance
      this.Detail.json_personal[0].financial[0].position = this.Position_Finance
      this.Detail.json_personal[0].financial[0].email = this.Email_Finance
      this.Detail.json_personal[0].technique[0].name = this.Name_Technical
      this.Detail.json_personal[0].technique[0].phone = this.Phone_Technical
      this.Detail.json_personal[0].technique[0].position = this.Position_Technical
      this.Detail.json_personal[0].technique[0].email = this.Email_Technical
      this.Detail.json_personal[0].purchasing_chief[0].name = this.Name_Buyer
      this.Detail.json_personal[0].purchasing_chief[0].phone = this.Phone_Buyer
      this.Detail.json_personal[0].purchasing_chief[0].position = this.Position_Buyer
      this.Detail.json_personal[0].purchasing_chief[0].email = this.Email_Buyer
      this.Detail.json_personal[0].inspectors_one[0].name = this.Name_Audit1
      this.Detail.json_personal[0].inspectors_one[0].phone = this.Phone_Audit1
      this.Detail.json_personal[0].inspectors_one[0].position = this.Position_Audit1
      this.Detail.json_personal[0].inspectors_one[0].email = this.Email_Audit1
      this.Detail.json_personal[0].inspectors_two[0].name = this.Name_Audit2
      this.Detail.json_personal[0].inspectors_two[0].phone = this.Phone_Audit2
      this.Detail.json_personal[0].inspectors_two[0].position = this.Position_Audit2
      this.Detail.json_personal[0].inspectors_two[0].email = this.Email_Audit2
      this.Detail.json_personal[0].supply_board_one[0].name = this.Name_SupplyBoard1
      this.Detail.json_personal[0].supply_board_one[0].phone = this.Phone_SupplyBoard1
      this.Detail.json_personal[0].supply_board_one[0].position = this.Position_SupplyBoard1
      this.Detail.json_personal[0].supply_board_one[0].email = this.Email_SupplyBoard1
      this.Detail.json_personal[0].supply_board_two[0].name = this.Name_SupplyBoard2
      this.Detail.json_personal[0].supply_board_two[0].phone = this.Phone_SupplyBoard2
      this.Detail.json_personal[0].supply_board_two[0].position = this.Position_SupplyBoard2
      this.Detail.json_personal[0].supply_board_two[0].email = this.Email_SupplyBoard2
      this.Detail.json_personal[0].json_buyer[0].name = this.buyer_name
      this.Detail.json_personal[0].json_buyer[0].phone = this.buyer_phone
      this.Detail.json_personal[0].json_buyer[0].position = this.buyer_position
      this.Detail.json_personal[0].json_buyer[0].email = this.buyer_email
      console.log(this.Detail)
      await this.$store.dispatch('actionsEditCompany', this.Detail)
      var response = await this.$store.state.ModuleAdminManage.stateEditCompany
      // console.log('response data after use api ============>', response)
      if (response.result === 'SUCCESS') {
        localStorage.removeItem('CompanyData')
        this.comfirmEditCompany = false
        // this.$swal.fire({ icon: 'success', title: 'แก้ไขบริษัทของคุณสำเร็จ', showConfirmButton: false, timer: 2000 })
        this.modalSuccessSettingCompany = !this.modalSuccessSettingCompany
        await this.$store.dispatch('actionslistCompany')
        var response1 = await this.$store.state.ModuleAdminManage.stateListCompany
        var filterCompany = response1.data.active.find(e => e.id === this.dataCompany.id)
        // console.log('filterCompany =======>', filterCompany)
        localStorage.setItem('CompanyData', Encode.encode(filterCompany))
        this.$EventBus.$emit('getDetailCompany')
        this.$EventBus.$emit('checkPathCompany')
        this.$EventBus.$emit('getCompany')
        if (this.MobileSize) {
          this.$router.push({ path: '/detailCompanyMobile' }).catch(() => { })
        } else {
          this.$router.push({ path: '/detailCompany' }).catch(() => { })
        }
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistrict
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.district
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.province
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcode)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistrict)
      this.checkdistrictConfirm(this.district)
      this.checkprovinceConfirm(this.province)
      this.checkzipcodeConfirm(this.zipcode)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style scoped>
.v-stepper__header {
  box-shadow: none;
}
</style>

<style>
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
