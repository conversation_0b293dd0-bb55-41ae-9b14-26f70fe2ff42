<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" :style="MobileSize ? 'background-color: white' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0" :class="!MobileSize ? 'pt-4 pb-5' : ''">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoMenu('desktop')">mdi-chevron-left</v-icon>รายการชำระเงิน</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoMenu('mobile')">mdi-chevron-left</v-icon>รายการชำระเงิน</v-card-title>
    </v-card>
    <v-data-table
      :headers="headers"
      :items="paymentList"
      item-value="purchaseOrderNumber"
      :items-per-page="itemsPerPage"
      :page.sync="page"
    >
      <template v-slot:[`item.index`]="{ index }">
        {{ (page - 1) * itemsPerPage + index + 1 }}
      </template>
      <template v-slot:[`item.amountMoney`]="{ item }">
        <span>{{item.amountMoney !== '-' ? Number(item.amountMoney).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}</span>
      </template>
      <!-- <template v-slot:[`item.invoiceNumber`]="{ item }"> -->
        <!-- <v-btn text color="success"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.invoice }}</span></v-btn> -->
        <!-- <a target="_blank" v-if="item.invoiceNumber !== '-'" :href="item.invoiceDocPath" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.invoiceNumber }}</a>
        <span v-else>-</span> -->
        <!-- <a target="_blank" :href="item.url_tracking" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</a> -->
        <!-- <span v-else>-</span> -->
      <!-- </template> -->
    </v-data-table>
    <!-- <v-row class="pt-5 pb-3">
      <v-col class="my-3 d-flex justify-between align-center">
        <span style="font-weight: bold; font-size: 16px;">เลขที่ Withholding Tax</span>
        <v-spacer></v-spacer>
        <v-btn @click="openDialogWHTfile()" :disabled="paymentMethod !== 'qrcode'" rounded dense color="#27ab9c" outlined>
          <v-icon >mdi-cloud-upload-outline</v-icon>
          <span>อัปโหลดไฟล์</span>
        </v-btn>
      </v-col>
    </v-row> -->
    <v-divider style="color: #F2F2F2;" class="mt-5"></v-divider>
    <v-row class="pt-5">
      <v-col class="d-flex align-center" :cols="IpadProSize ? 7 : IpadSize || MobileSize ? 12 : 6">
        <span style="font-weight: bold; font-size: 16px;" class="mr-3">วิธีการชำระเงิน</span>
        <v-radio-group
          v-model="paymentMethod"
          row
        >
          <!-- <v-radio
            label="QRCode"
            style="color: #333333;"
            value="qrcode"
          ></v-radio>
          <v-radio
            label="Credit Card"
            style="color: #333333;"
            value="creditcard"
          ></v-radio> -->
          <v-radio
            v-for="(method, index) in typePayment"
            :key="index"
            :label="getOriginalPaymentMethod(method)"
            :value="method"
          ></v-radio>
        </v-radio-group>
      </v-col>
      <v-spacer></v-spacer>
      <v-col :cols="IpadProSize ? 5 : IpadSize || MobileSize ? 12 : 4" style="font-weight: bold; font-size: 16px;" class="d-flex align-center">
        <span>ราคารวมทั้งหมด</span>
        <v-spacer></v-spacer>
        <span color="primary" style="color: #27ab9c; font-weight: bold; font-size: 28px;" class="mr-4">{{ totalAmount !== '-' ? Number(totalAmount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}</span>
        <span>บาท</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col class="d-flex justify-end">
        <v-btn :disabled="paymentMethod === ''" @click="modalAwaitOrder = true" width="200px" rounded color="primary" class="white--text">ชำระเงิน</v-btn>
      </v-col>
    </v-row>
    <!-- Qr code -->
    <v-dialog v-model="DialogQR" :width="MobileSize ? '100%' : '640'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
        <v-card-text class="px-0">
          <div  :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
            style="position: absolute; height: 120px; ">
            <v-row style="height: 120px; ">
              <v-col style="text-align: center;" class="pt-6">
                <span style="margin-left: 0px"
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>สแกน QR Code ชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '640px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
              style="background: #FFFFFF; border-radius: 20px;">
              <div style="text-align: center;">
                <v-col class="py-0">
                  <!-- ต้องทำ function save qr code ด้วยดูที่ id="qrcode" -->
                  <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${imageBase64}`" v-if="ImageQR !== ''"/>
                </v-col>
                <v-col class="py-0">
                  <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                </v-col>
                <div>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'">ยอดชำระเงินจำนวน : {{ netPrice ?
                      Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}
                      บาท</span>
                  </v-col>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 13px; font-weight: 400;' : 'font-size: 14px; font-weight: 400;'">รหัสอ้างอิง {{Ref1}}</span>
                  </v-col>
                  <v-col class="py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 600; color: #A1A1A1;' : 'font-size: 14px; font-weight: 600; color: #A1A1A1;'">สามารถชำระเงินได้ตามขั้นตอนนี้
                      (กรณีชำระเงินผ่านมือถือ)</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">2. เปิดแอปธนาคารของท่าน
                      และเลือกเมนูสแกน QR Code</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                      Code</span>
                  </v-col>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- await confirm purchase -->
    <v-dialog v-model="modalAwaitOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="modalAwaitOrder = false"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยืนยันการสั่งซื้อ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span><br/>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คำสั่งซื้อรายการนี้</span> -->
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="modalAwaitOrder = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="openDialogQr(paymentMethod)">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- uploadWHFile -->
    <v-dialog v-model="openDialogWHT" :width="MobileSize ? '100%' : '640'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
        <v-card-text class="px-0">
          <div  :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
            style="position: absolute; height: 120px; ">
            <v-row style="height: 120px; ">
              <v-col style="text-align: center;" class="pt-6">
                <span style="margin-left: 0px"
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>อัปโหลด Withholding Tax</b></span>
              </v-col>
              <v-btn fab small @click="openDialogWHT = !openDialogWHT" icon class="mt-3"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '640px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%">
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <v-row v-if="fileWHT.length === 0" dense>
                  <v-col cols="12">
                    <v-card width="100%" height="100%" outlined elevation="0" class="cardImageStyle" @click="onPickFile()">
                      <v-card-text style="text-align: center;" class="px-2">
                        <input type="file" ref="fileWHT" @change="handleFileUpload($event)" style="display: none;" accept=".pdf">
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-0">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="100" height="100" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center">
                            <v-col cols="12" md="4" style="text-align: center;">
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row dense v-if="fileWHT.length !== 0">
                  <v-col v-for="(item, index) in fileWHT" :key="index" cols="12" md="12" sm="12">
                    <v-card outlined width="100%" height="100%" @click="openLink(item)" class="mt-2">
                      <v-row dense justify="end">
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click.stop="Removefile(item, index)">mdi-close</v-icon>
                        </v-btn>
                      </v-row>
                      <v-card-text>
                        <v-row dense>
                          <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                          </v-img>
                          <span style="text-align: center; align-content: center;" class="text-truncate pt-2">{{ item.name }}</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      SelectPartnerCode: '',
      typePayment: [],
      fileWHT64: '',
      fileWHT: [],
      openDialogWHT: false,
      itemsPerPage: 5,
      page: 1,
      totalAmount: null,
      modalAwaitOrder: false,
      ImageQR: '',
      imageBase64: '',
      Ref1: '',
      netPrice: '',
      TypeOS: '',
      DialogQR: false,
      paymentList: [],
      paymentMethod: '',
      headers: [
        {
          text: 'ลำดับ',
          value: 'index',
          sortable: false,
          align: 'start',
          width: '50px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสคำสั่งซื้อ',
          value: 'purchaseOrderNumber',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Partner',
          value: 'partnerName',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขใบแจ้งหนี้',
          value: 'invoiceNumber',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ยอดใบแจ้งหนี้',
          value: 'amountMoney',
          sortable: false,
          align: 'start',
          width: '150px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ]
    }
  },
  async created () {
    this.TypeOS = this.detectOS()
    var items = []
    items = JSON.parse(localStorage.getItem('orderPartner')) || []
    var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
    await this.getDetails(shopID, items)
    await this.AccountDetailShop(shopID)
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/paymentListMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentList' }).catch(() => {})
      }
    }
  },
  methods: {
    getOriginalPaymentMethod (method) {
      if (method === 'qrcode') {
        return 'QR Code'
      } else if (method === 'creditcard') {
        return 'Credit Card'
      } else {
        return method
      }
    },
    Removefile (item, index) {
      this.fileWHT.splice(index, 1)
      this.fileWHT64 = ''
    },
    onPickFile () {
      this.$refs.fileWHT.click()
    },
    openDialogWHTfile () {
      this.openDialogWHT = true
    },
    handleFileUpload (event) {
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.fileWHT.push(file)
          // แปลงเป็น base 64
          this.fileWHT64 = reader.result
        }
      }
      // this.fileWHT.push(file)
    },
    openLink (link) {
      const fileURL = URL.createObjectURL(link)
      window.open(fileURL, '_blank')
    },
    async getDetails (id, order) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: id,
        billOrderNumber: order
      }
      await this.$store.dispatch('actionDetailOrderPurchasePartner', data)
      var response = await this.$store.state.ModuleOrder.stateDetailOrderPurchasePartner
      if (response.code === 200) {
        this.totalAmount = response.totalAmount
        this.paymentList = response.data
        this.SelectPartnerCode = response.data[0].partnerCode
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async openDialogQr () {
      this.$store.commit('openLoader')
      var productIdToChange = []
      this.paymentList.forEach(item => {
        productIdToChange.push(item.invoiceNumber)
      })
      if (this.paymentMethod === 'qrcode') {
        this.modalAwaitOrder = false
        // this.DialogQR = true
        var dataOrderNumber = {
          partner_code: this.SelectPartnerCode,
          payment_transaction_number: productIdToChange
        }
        await this.$store.dispatch('actionPayWithQRCode', dataOrderNumber)
        var resQR = await this.$store.state.ModuleShop.statePayWithQRCode
        if (resQR.result === 'SUCCESS') {
          this.DialogQR = true
          this.netPrice = await resQR.data.net_price
          this.Ref1 = await resQR.data.ref1
          var base64 = resQR.data.img_base
          this.imageBase64 = 'data:image/png;base64,' + base64
          var ImageQR = await resQR.data.img_base64
          this.ImageQR = ImageQR
          this.$store.commit('closeLoader')
          this.DialogQR = true
          if (resQR.message !== 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
            var data = {
              payment_transaction_number: resQR.data.order_number
            }
            const maxAttempts = 15
            let currentAttempt = 1
            while (currentAttempt <= maxAttempts) {
              await this.$store.dispatch('actionCheckResultQRCodeMarketplace', data)
              const resCheckQR = await this.$store.state.ModuleShop.stateCheckResultQRCodeMarketplace
              if (this.DialogQR === false) {
                break
              }
              if (resCheckQR.result === 'SUCCESS') {
                this.$store.commit('openLoader')
                setTimeout(() => {
                  this.$router.push({ path: `/successPaymentPartner?id=${resQR.data.order_number}` }).catch(() => {})
                }, 500)
                break
              }
              await new Promise(resolve => setTimeout(resolve, 10000))
              currentAttempt++
              if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 5000,
                  timerProgressBar: true,
                  icon: 'error',
                  text: 'การชำระเงินไม่เสร็จสมบูรณ์'
                })
                this.$router.push({ path: '/paymentPartner' }).catch(() => {})
              }
            }
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'error',
              text: `${resQR.message}`
            })
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: `${resQR.message}`
          })
        }
      } else if (this.paymentMethod === 'creditcard') {
        this.modalAwaitOrder = false
        var resCC
        var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
        data = {
          go_local: goLocalValue,
          partner_code: this.SelectPartnerCode,
          payment_transaction_number: productIdToChange
        }
        await this.$store.dispatch('actionPayWithCreditCard', data)
        resCC = await this.$store.state.ModuleShop.statePayWithCreditCard
        if (resCC.result === 'SUCCESS') {
          localStorage.setItem('PaymentData', Encode.encode(resCC.data))
          this.$router.push('/RedirectPaymentPage').catch(() => {})
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
        }
        // console.log(this.paymentMethod)
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาเลือกช่องการชำระเงิน</h3>'
        })
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    closeDialogQR () {
      this.DialogQR = false
      localStorage.removeItem('orderPartner')
      if (this.MobileSize) {
        this.$router.push({ path: '/paymentPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentPartner' }).catch(() => {})
      }
    },
    backtoMenu (val) {
      localStorage.removeItem('orderPartner')
      if (val === 'mobile') {
        this.$router.push({ path: '/paymentPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentPartner' }).catch(() => {})
      }
    },
    async AccountDetailShop (shopID) {
      const data = {
        seller_shop_id: shopID
      }
      await this.$store.dispatch('actionGetListDetailPartnerShop', data)
      var response = await this.$store.state.ModuleShop.stateGetListDetailPartnerShop
      if (response.message === 'list partner success.') {
        var dataServicePartner = Object.values(response.data).filter(partnerCode => partnerCode.partner_code === this.SelectPartnerCode)
        this.typePayment = dataServicePartner[0].payment_method
      } else {
        this.typePayment = []
      }
    }
  }
}
</script>

<style>

</style>
