<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการสั่งซื้อสินค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>รายการสั่งซื้อสินค้า</v-card-title>
    <!-- แถว 1 -->
    <v-row dense>
      <v-col
        :cols="MobileSize ? 8 : 7"
        md="8"
        sm="12"
        :class="!MobileSize ? 'pl-2 pt-0 pt-6' : 'mb-3'"
      >
        <v-text-field
          v-model="search"
          dense
          hide-details
          style="border-radius: 8px;"
          :class="MobileSize ? '' : IpadSize ? 'pr-0' : 'pr-4'"
          outlined
          @change="getOrderList(); options.page = 1"
          @keyup="checkSearch"
          placeholder="ค้นหาข้อมูลจากชื่อ Service Partner ที่เชื่อมบริการ"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="3" v-if="MobileSize">
        <v-btn @click="dialogFilter = true" outlined rounded color="#27AB9C" height="36"><v-icon size="24" left dark>mdi-filter-outline</v-icon>ตัวกรอง</v-btn>
      </v-col>
      <v-col
        v-if="!MobileSize"
        cols="12"
        md="4"
        sm="12"
        :class="!MobileSize && !IpadSize ? 'pt-0' : IpadSize ? '' : 'pl-2 pr-2 mb-3'"
      >
        <v-row dense :class="!MobileSize ? 'pt-6' : ''">
          <v-col cols="4" md="4" sm="3" :class="!MobileSize ? 'pt-3' : 'pt-3 pr-0'">
            <span
              style="
                font-size: 16px;
                line-height: 24px;
                color: #333333;
                font-weight: 400;
              "
              >สถานะรายการ :</span
            >
          </v-col>
          <v-col cols="8" md="8" sm="9">
            <v-select
              v-model="statusSelect"
              :items="statusItem"
              item-text="text"
              item-value="value"
              append-icon="mdi-chevron-down"
              outlined
              class="setCustomSelect"
              dense
              @change="getOrderList(); options.page = 1"
              style="border-radius: 8px;"
              hide-details
              placeholder="เลือกสถานะรายการ"
            ></v-select>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <!-- แถว 2 -->
    <v-row dense class="px-1" v-if="!MobileSize">
      <v-col cols="12" md="4" sm="6">
        <v-row dense :class="!MobileSize ? 'pt-5' : 'pt-0'">
          <v-col cols="4" :class="!MobileSize ? 'pt-3 pr-0' : 'pt-3 pr-0'">
            <span style="font-size: 16px; line-height: 24px; color: #333333"
              >Pay Type : </span
            >
          </v-col>
          <v-col cols="8">
            <v-select
              v-model="PayTypeSelect"
              :items="payTypeItem"
              item-text="text"
              item-value="value"
              @change="getOrderList(); options.page = 1"
              append-icon="mdi-chevron-down"
              style="border-radius: 8px;"
              class="setCustomSelect"
              :class="MobileSize ? '' : IpadSize ? 'pr-0' : 'pr-4'"
              outlined
              dense
              placeholder="เลือกประเภทการชำระเงิน"
            ></v-select>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <v-row dense :class="!MobileSize ? 'pt-5' : 'pt-0'">
          <v-col cols="4" :class="!MobileSize ? 'pt-3 pr-0 pl-0' : 'pt-3 pr-0'">
            <span style="font-size: 16px; line-height: 24px; color: #333333"
              >วันที่สั่งซื้อ : </span
            >
          </v-col>
          <v-col cols="8">
            <v-dialog
              ref="dialogBuyDate"
              v-model="modalBuyDate"
              :return-value.sync="date"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  readonly
                  v-model="buyDate"
                  v-bind="attrs"
                  v-on="on"
                  outlined
                  style="border-radius: 8px;"
                  dense
                  @change="getOrderList(); options.page = 1"
                  :class="MobileSize ? '' : IpadSize ? 'pr-0' : 'pr-4'"
                  placeholder="วว/ดด/ปปปป"
                  ><v-icon slot="append" color="#27AB9C"
                    >mdi-calendar-multiselect</v-icon
                  ></v-text-field
                >
              </template>
              <v-date-picker
                v-model="date"
                color = "#27AB9C"
                scrollable
                reactive
                locale="Th-th"
                @change="setValueBuyDate(date)"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-spacer></v-spacer>
                <v-btn text color="primary" @click="closeModalBuyDate($refs)">
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="
                    $refs.dialogBuyDate.save(date);
                    getOrderList(); options.page = 1
                  "
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="4" sm="12">
        <v-row dense :class="!MobileSize && !IpadSize ? 'pt-5' : 'pt-0'">
          <v-col cols="4" sm="3" :class="!MobileSize ? 'pt-3 pr-0' : 'pt-3 pr-0'">
            <span style="font-size: 16px; line-height: 24px; color: #333333"
              >วันที่อนุมัติ : </span
            >
          </v-col>
          <v-col cols="8" sm="9" class="pl-0 pr-0">
            <v-dialog
              ref="dialogAcceptDate"
              v-model="modalAcceptDate"
              :return-value.sync="dateApprove"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  readonly
                  v-model="acceptDate"
                  v-bind="attrs"
                  v-on="on"
                  outlined
                  @change="getOrderList(); options.page = 1"
                  style="border-radius: 8px;"
                  dense
                  placeholder="วว/ดด/ปปปป"
                  ><v-icon slot="append" color="#27AB9C"
                    >mdi-calendar-multiselect</v-icon
                  ></v-text-field
                >
              </template>
              <v-date-picker
                v-model="dateApprove"
                scrollable
                reactive
                locale="Th-th"
                @change="setValueAcceptDate(dateApprove)"
                color = "#27AB9C"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-spacer></v-spacer>
                <v-btn text color="primary"
                @click="
                  closeModalAcceptDate($refs)">
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="
                    $refs.dialogAcceptDate.save(dateApprove)
                    getOrderList(); options.page = 1
                  "
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <!-- แถวที่ 3 -->
    <v-row dense class="px-1" v-if="!MobileSize">
      <v-col cols="6" md="8" sm="12">
        <v-row dense>
          <v-col cols="4" md="3" sm="3" :class="!MobileSize ? 'pt-3 pl-0 pr-0' : 'pt-3 px-0'">
            <span
              style="
                font-size: 16px;
                line-height: 24px;
                color: #333333;
                font-weight: 400;
              "
              >วันที่รอบบริการ :</span
            >
          </v-col>
          <v-col cols="8" md="9" sm="9" >
            <v-dialog
              ref="modalRangeDate"
              v-model="modalRangeDate"
              :return-value.sync="dateRange"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  readonly
                  v-model="RangedateApprove"
                  v-bind="attrs"
                  v-on="on"
                  style="border-radius: 8px;"
                  outlined
                  dense
                  :class="MobileSize || IpadSize ? '' : 'pr-4'"
                  placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                  ><v-icon slot="append" color="#27AB9C"
                    >mdi-calendar-multiselect</v-icon
                  ></v-text-field>
              </template>
              <v-date-picker
                color="#27AB9C"
                v-model="dateRange"
                scrollable
                range
                reactive
                locale="Th-th"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-spacer></v-spacer>
                <v-btn text color="primary" @click="CloseModalRangeDate()">
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="setValueRangeDate(dateRange)"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        v-if="!MobileSize"
        cols="12"
        md="4"
        sm="12"
        :class="!MobileSize && !IpadSize ? 'pt-0' : IpadSize ? '' : 'pl-2 pr-2 mb-3'"
      >
        <v-row dense>
          <!-- <v-col cols="4" md="4" sm="3" :class="!MobileSize ? 'pt-3' : 'pt-3 pr-0'">
            <span
              style="
                font-size: 16px;
                line-height: 24px;
                color: #333333;
                font-weight: 400;
              "
              >Service Name :</span
            >
          </v-col> -->
          <!-- <v-col cols="8" md="8" sm="9">
            <v-select
              v-model="selectedServiceName"
              :items="listService"
              item-text="service_name"
              item-value="id"
              append-icon="mdi-chevron-down"
              outlined
              dense
              @change="getOrderList()"
              style="border-radius: 8px;"
              hide-details
              placeholder="เลือก Service Name"
            ></v-select>
          </v-col> -->
        </v-row>
      </v-col>
    </v-row>
    <!-- list order and action button -->
    <v-row dense class="px-0">
      <v-col cols="12" md="8" sm="12" align="end" class="pt-3" v-if="MobileSize || IpadSize">
        <v-row>
          <v-col :cols="IpadSize ? 4 : 6">
            <v-btn @click="exportExcel()" rounded color="#27AB9C" height="40" class="white--text mr-2" style="width: inherit;">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span>Export File</span>
            </v-btn>
          </v-col>
          <!-- <v-col :cols="IpadSize ? 4 : 6">
            <v-btn :disabled="handleBtnPay" rounded color="#27AB9C" height="40" class="white--text mr-2" style="width: inherit;">
              <v-icon>mdi-cash-multiple</v-icon>
              <span>ชำระเงิน</span>
            </v-btn>
          </v-col> -->
          <v-col cols="4" v-if="!MobileSize">
            <v-btn @click="reSetSearch()" class="white--text mr-2" rounded color="#27AB9C" width="100" height="40" style="width: inherit;">
              <v-icon small class="mr-1">mdi-restart</v-icon>ล้างค่า
            </v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="4" sm="12" align="start" :class="MobileSize ? 'pl-2 mt-4' : 'pt-6'">
        <span
          :class="MobileSize ? '' : ''"
          style="line-height: 24px; align-items: center; color: #333333; font-weight: 400;"
          :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'"
          >รายการสั่งซื้อสินค้า {{ showCountOrder }} รายการ</span
        >
      </v-col>
      <v-col cols="12" md="8" sm="12" align="end" class="pt-3" v-if="!MobileSize && !IpadSize">
        <v-btn @click="exportExcel()" rounded color="#27AB9C" height="40" class="white--text mr-2">
          <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
          <span>Export File</span>
        </v-btn>
        <!-- <v-btn :disabled="handleBtnPay" rounded color="#27AB9C" height="40" class="white--text mr-2">
          <v-icon>mdi-cash-multiple</v-icon>
          <span>ชำระเงิน</span>
        </v-btn> -->
        <v-btn @click="reSetSearch()" class="white--text mr-2" rounded color="#27AB9C" width="100" height="40">
          <v-icon small class="mr-1">mdi-restart</v-icon>ล้างค่า
        </v-btn>
      </v-col>
    </v-row>
    <!-- data table -->
    <v-row>
      <v-col cols="12">
        <v-data-table
          v-model="selected"
          :headers="headersAll"
          :items="orderList"
          :search="search"
          :footer-props="{ 'items-per-page-options': [5, 10, 15, 25], 'items-per-page-text': 'จำนวนแถว' }"
          class="elevation-1"
          no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
          no-data-text="ไม่มีรายการในตาราง"
          style="white-space: nowap; text-align: center;"
          :options.sync="options"
          :items-per-page="options.itemsPerPage"
          :server-items-length="maxPage"
          @update:options="updateOptionsTable"
          @toggle-select-all="selectAllToggle"
        >
          <!-- color="blue" show-select -->
          <!-- <template v-slot:[`header.data-table-select`]="{ on , props }">
            <v-simple-checkbox
              v-model="checkboxAll"
              :indeterminate="checkboxIndeterminate"
              :ripple="false"
              v-bind="props"
              v-on="on"
              :disabled="isCheckboxDisabled"
            ></v-simple-checkbox>
          </template>
          <template v-slot:[`item.data-table-select`]="{ isSelected, select, item }">
            <v-simple-checkbox :ripple="false" :value="item.transaction_text !== 'รอชำระเงิน' ? null : isSelected" @input="select($event)" :disabled="item.transaction_text !== 'รอชำระเงิน' ? true : false" v-if="!MobileSize"></v-simple-checkbox>
            <v-card elevation="0" class="d-flex pb-0" v-if="MobileSize">
              <v-card-text class="d-flex pb-0">
                <v-simple-checkbox :ripple="false" :value="item.transaction_text !== 'รอชำระเงิน' ? null : isSelected" @input="select($event)" :disabled="item.transaction_text !== 'รอชำระเงิน' ? true : false" class="pt-1 mr-auto"></v-simple-checkbox>
              </v-card-text>
            </v-card>
          </template> -->
          <template v-slot:[`item.created_at`]="{ item }">
            <span v-if="item.created_at" color="#27AB9C" > {{ item.created_at }}</span>
            <!-- <span v-if="item.created_date" color="#27AB9C" > {{ new Date(item.created_date).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span> -->
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.approve_date`]="{ item }">
            <!-- <span v-if="item.approve_date !== '-'" color="#27AB9C" > {{ new Date(item.approve_date).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span> -->
            <span v-if="item.approve_date !== '-'" color="#27AB9C" > {{ item.approve_date }}</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.purchase_order_number`]="{ item }">
            <span v-if="item.purchase_order_number !== '-'" color="#27AB9C" style="white-space: nowrap;"> {{ item.purchase_order_number }}</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.name_th`]="{ item }">
            <span v-if="item.name_th !== '-'" color="#27AB9C" > {{ item.name_th }}</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.transaction_text`]="{ item }">
            <span v-if="item.transaction_text === 'ชำระเงินสำเร็จ'" style="color: #52c41a;"><v-icon size="x-small" color="#52c41a">mdi-circle</v-icon> ชำระเงินแล้ว</span>
            <span v-else-if="item.transaction_text === 'สั่งซื้อสำเร็จ'" style="color: #47b8ff;"><v-icon size="x-small" color="#47b8ff">mdi-circle</v-icon> สั่งซื้อสำเร็จ</span>
            <!-- <span v-else-if="item.transaction_text === 'รอชำระเงิน'" style="color: #faad14;"><v-icon size="x-small" color="#faad14">mdi-circle</v-icon> รอชำระเงิน</span>
            <span v-else-if="item.transaction_text === 'ยกเลิก'"><v-icon size="x-small">mdi-circle</v-icon> ยกเลิกคำสั่งซื้อ</span>
            <span v-else-if="item.transaction_text === 'กำลังจัดส่ง'" style="color: #FAD02C;"><v-icon size="x-small" color="#FAD02C">mdi-circle</v-icon> กำลังจัดส่ง</span> -->
            <span v-else-if="item.transaction_text === 'รอดำเนินการ'" style="color: #434bf7;"><v-icon size="x-small" color="#434bf7">mdi-circle</v-icon> รอดำเนินการ</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.pay_type`]="{ item }">
            <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
            <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
            <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.QT_order_path`]="{ item }">
            <v-btn text color="success" v-if="item.QT_order_path !== '-'" @click="getQTOrder(item.QT_order_path)"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.order_number }}</span></v-btn>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.receipt_transaction_code`]="{ item }">
            <v-btn text color="success" v-if="item.receipt_transaction_code !== '-'" @click="getQTOrderInvoice(item.receipt_transaction_code)"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.bill_order_number }}</span></v-btn>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.service_date`]="{ item }">
            <span style="white-space: nowrap;" v-if="item.start_date_contract !== '-' && item.end_date_contract !== '-'" color="#27AB9C" > {{ item.start_date_contract.replace(/-/g, '/') + ' - ' + item.end_date_contract.replace(/-/g, '/') }}</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.etax_number`]="{ item }">
            <v-btn text color="success" v-if="item.etax_number !== '-'" @click="GetETaxPDF(item.etax_number)"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.etax_number }}</span></v-btn>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.detail`]="{ item }">
            <v-btn :disabled="(item.transaction_text !== 'ชำระเงินสำเร็จ' && item.transaction_text !== 'สั่งซื้อสำเร็จ') || (item.purchase_order_number === '-' || item.bill_order_number === '-')" color="#27AB9C" text @click="goDetailOrder(item.purchase_order_number, item.bill_order_number)">
              <v-icon>mdi-file-document-outline</v-icon>
              <span>รายละเอียด</span>
            </v-btn>
            <!-- <v-menu offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  v-on="on"
                  class="pt-4 pb-4"
                  x-small
                  outlined
                  style="
                    max-width: 32px;
                    max-height: 32px;
                    border-radius: 4px;
                    border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                    background: var(--neutral-ffffff, #fff);
                    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                  "
                >
                  <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="(items, index) in menuDetail"
                  :key="index"
                  link
                >
                  <v-list-item-content
                    @click="gotoActions(item, items.value)"
                  >
                    <v-list-item-title>{{ items.text }}</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu> -->
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <!-- dialog filter -->
    <v-dialog v-model="dialogFilter" width="100%" persistent>
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 20px;">
        <v-card-text>
          <v-toolbar flat color="rgba(0, 0, 0, 0)">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title><span style="color: #333333; font-size: 16px;"><b>ตัวกรอง</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="dialogFilter = !dialogFilter" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-row dense>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
                >สถานะรายการ :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="statusSelect"
                :items="statusItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                class="setCustomSelect"
                dense
                style="border-radius: 8px;"
                hide-details
                placeholder="เลือกสถานะรายการ"
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >Pay Type : </span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="PayTypeSelect"
                :items="payTypeItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                style="border-radius: 8px;"
                class="setCustomSelect"
                :class="MobileSize ? '' : 'pr-4'"
                outlined
                hide-details
                dense
                placeholder="เลือกประเภทการชำระเงิน"
              ></v-select>
            </v-col>
            <!-- <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
                >Service Name :</span
              >
            </v-col> -->
            <!-- <v-col cols="12">
              <v-select
                v-model="selectedServiceName"
                :items="listService"
                item-text="service_name"
                item-value="id"
                append-icon="mdi-chevron-down"
                outlined
                dense
                style="border-radius: 8px;"
                hide-details
                placeholder="เลือก Service Name"
              ></v-select>
            </v-col> -->
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่สั่งซื้อ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogBuyDate"
                v-model="modalBuyDate"
                :return-value.sync="date"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="buyDate"
                    v-bind="attrs"
                    v-on="on"
                    hide-details
                    outlined
                    style="border-radius: 8px;"
                    dense
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#27AB9C"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="date"
                  color = "#27AB9C"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueBuyDate(date)"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="closeModalBuyDate($refs)">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="
                      $refs.dialogBuyDate.save(date);
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่อนุมัติ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogAcceptDate"
                v-model="modalAcceptDate"
                :return-value.sync="dateApprove"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="acceptDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    hide-details
                    style="border-radius: 8px;"
                    dense
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#27AB9C"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="dateApprove"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueAcceptDate(dateApprove)"
                  color = "#27AB9C"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary"
                  @click="
                    closeModalAcceptDate($refs)">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="
                      $refs.dialogAcceptDate.save(dateApprove)
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >วันที่รอบบริการ :</span
              >
            </v-col>
            <v-col cols="12" >
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangedateApprove"
                    v-bind="attrs"
                    v-on="on"
                    style="border-radius: 8px;"
                    outlined
                    dense
                    hide-details
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#27AB9C"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  range
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="CloseModalRangeDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="setValueRangeDate(dateRange)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-row dense justify="center">
            <v-btn width="125" height="36" text color="#27AB9C" @click="reSetSearch()">ล้างค่า</v-btn>
            <v-btn width="125" height="36" rounded color="#27AB9C" class="white--text" @click="getOrderList(); options.page = 1">ยืนยัน</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      // statusItem: ['ทั้งหมด', 'ยกเลิก', 'กำลังจัดส่ง', 'ชำระเงินสำเร็จ', 'รอดำเนินการ', 'รอชำระเงิน'],
      statusItem: ['ทั้งหมด', 'ชำระเงินสำเร็จ', 'สั่งซื้อสำเร็จ', 'รอดำเนินการ'],
      statusSelect: 'ทั้งหมด',
      payTypeItem: ['ทั้งหมด', 'recurring', 'onetime', 'general'],
      PayTypeSelect: 'ทั้งหมด',
      search: '',
      showCountOrder: 0,
      orderList: [],
      selected: [],
      modalBuyDate: false,
      modalRangeDate: false,
      modalAcceptDate: false,
      acceptDate: '',
      buyDate: '',
      date: '',
      dateRange: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      searchBuyDate: '',
      dateApprove: '',
      RangedateApprove: [],
      startDateToSend: '',
      endDateToSend: '',
      // RangedateApprove: [],
      contractStartDate: '',
      contractEndtDate: '',
      startDateRange: '',
      endDateRange: '',
      dialogFilter: false,
      detailServiceID: [],
      serviceID: '',
      options: {
        page: 1,
        itemsPerPage: 10
      },
      // chekbox
      checkboxIndeterminate: false,
      checkboxAll: false,
      handleBtnPay: true,
      disabledCount: 0,
      listService: [],
      serviceName: [],
      // selectedServiceName: '',
      menuDetail: [
        { text: 'ชำระเงิน', value: 'pay_ment' },
        { text: 'รายละเอียด', value: 'detail' }
      ],
      headersAll: [
        {
          text: 'วันที่ทำรายการ',
          value: 'created_at',
          align: 'start',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'purchase_order_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ชื่อ Service Partner',
          value: 'name_th',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะสั่งซื้อ',
          value: 'transaction_text',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Pay Type',
          value: 'pay_type',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่อนุมัติ',
          value: 'approve_date',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบเสนอราคา',
          value: 'QT_order_path',
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบแจ้งหนี้',
          value: 'receipt_transaction_code',
          align: 'start',
          filterable: false,
          width: '180px',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่รอบบริการ',
          value: 'service_date',
          sortable: false,
          aligpay_typen: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบกำกับภาษี',
          value: 'etax_number',
          filterable: false,
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'detail',
          align: 'start',
          filterable: false,
          sortable: false,
          class: 'backgroundTable fontTable--text'
        }
      ],
      headersMobile: [
        { text: 'รายละเอียด', value: 'Detail', sortable: false, width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      maxPage: 0
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    selectableItems () {
      return this.orderList.filter(item => item.transaction_text === 'รอชำระเงิน')
    },
    isCheckboxDisabled () {
      const startIndex = (this.options.page - 1) * this.options.itemsPerPage
      const endIndex = startIndex + this.options.itemsPerPage
      const visibleItems = this.orderList.slice(startIndex, endIndex)
      return !visibleItems.some(item => item.transaction_text === 'รอชำระเงิน')
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    await this.getServiceID()
    await this.getOrderList()
    await this.listServiceAll()
    // console.log('see selected', this.selected)
  },
  watch: {
    dateRange (val) {
      // console.log('dateRange', val)
      this.startDateToSend = val[0] !== undefined ? val[0] : ''
      this.endDateToSend = val[1] !== undefined ? val[1] : ''
      this.contractStartDate = val[0] !== undefined ? this.formatDateToShow(val[0]) : ''
      this.contractEndDate = val[1] !== undefined ? this.formatDateToShow(val[1]) : ''
      if (this.contractStartDate !== '' && this.contractEndDate !== '') {
        this.RangedateApprove = this.contractStartDate + ' - ' + this.contractEndDate
      } else {
        this.RangedateApprove = ''
      }
    },
    selected (val) {
      // console.log('see selected', val)
      if (val.length !== 0) {
        this.handleBtnPay = false
      } else {
        this.handleBtnPay = true
      }
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/orderListPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderListPartner' }).catch(() => {})
      }
    }
  },
  methods: {
    selectAllToggle (props) {
      if (this.searchh === '') {
        if (this.selected.length !== this.orderList.length - this.disabledCount && this.checkboxAll) {
          this.selected = []
          const self = this
          this.orderList.forEach(item => {
            if (item.transaction_text === 'รอชำระเงิน') {
              self.selected.push(item)
            }
          })
          if (this.selected.length > 0 && this.orderList.length === this.selected.length) {
            this.checkboxAll = true
            this.checkboxIndeterminate = false
          } else if (this.selected.length > 0) {
            this.checkboxIndeterminate = true
          }
        } else {
          this.selected = []
          this.checkboxAll = false
          this.checkboxIndeterminate = false
        }
      }
    },
    async listServiceAll () {
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopID
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionListService', data)
      var respons = await this.$store.state.ModuleOrder.stateListService
      if (respons.code === 200) {
        this.listService = respons.data
        this.serviceName = this.listService.map(item => item.service_name)
        // console.log('see service name', this.serviceName)
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async getServiceID () {
      this.$store.commit('openLoader')
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopID
      }
      // console.log(localStorage.getItem('shopSellerID'))
      await this.$store.dispatch('actionGetServiceID', data)
      var respons = await this.$store.state.ModuleOrder.stateGetServiceID
      if (respons.code === 200) {
        if (respons.data.length !== 0) {
          this.$store.commit('openLoader')
          this.detailServiceID = respons.data[0]
          // console.log('service', this.detailServiceID)
          this.serviceID = this.detailServiceID.service_id
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async getOrderList () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        transaction_text: this.statusSelect === 'ทั้งหมด' ? '' : this.statusSelect,
        pay_type: this.PayTypeSelect === 'ทั้งหมด' ? '' : this.PayTypeSelect,
        created_date: this.date,
        approve_date: this.dateApprove,
        start_date_contract: this.startDateRange,
        end_date_contract: this.endDateRange,
        search: this.search,
        limit: this.options.itemsPerPage,
        pages: this.options.page
      }
      await this.$store.dispatch('actionOrderListERP', data)
      var respons = await this.$store.state.ModuleOrder.stateOrderListERP
      if (respons.code === 200) {
        this.selected = []
        this.checkboxAll = false
        this.checkboxIndeterminate = false
        this.showCountOrder = respons.orderCount
        this.maxPage = respons.orderCount
        this.orderList = respons.data
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    setValueBuyDate (val) {
      this.searchBuyDate = val
      this.buyDate = this.formatDateToShow(val)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    closeModalBuyDate ($refs) {
      this.modalBuyDate = false
      $refs.dialogBuyDate.save('')
      this.date = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.buyDate = ''
      this.date = ''
    },
    setValueAcceptDate (val) {
      this.acceptDate = this.formatDateToShow(val)
    },
    closeModalAcceptDate ($refs) {
      this.modalAcceptDate = false
      $refs.dialogAcceptDate.save('')
      this.dateApprove = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.acceptDate = ''
      this.dateApprove = ''
    },
    async CloseModalRangeDate () {
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.startDateToSend = ''
      this.endDateToSend = ''
      this.dateRange = []
      this.RangedateApprove = []
      if (!this.MobileSize) {
        this.options.page = 1
        await this.getOrderList()
      }
    },
    async setValueRangeDate (val) {
      this.$refs.modalRangeDate.save(val)
      var Range = await val.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
      this.dateRange = Range
      this.startDateRange = this.dateRange[0]
      this.endDateRange = this.dateRange[1]
      if (!this.MobileSize) {
        this.options.page = 1
        await this.getOrderList()
      }
    },
    reSetSearch () {
      this.statusSelect = ''
      this.PayTypeSelect = ''
      this.PayTypeSelect = ''
      this.date = ''
      this.dateApprove = ''
      this.startDateRange = ''
      this.endDateRange = ''
      this.buyDate = ''
      this.acceptDate = ''
      this.search = ''
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.dateRange = []
      this.RangedateApprove = []
      this.options.page = 1
      // this.selectedServiceName = ''
      this.getOrderList()
    },
    getQTOrder (item) {
      window.open(item, '_blank')
    },
    async getQTOrderInvoice (item) {
      const data = {
        transactionCode: item
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}partner/etax/document_invoice`,
          method: 'POST',
          data: data
        }).then((response) => {
          window.open(response.data.etaxResponse.pdfURL, '_blank')
        })
      } catch (error) {
        // console.log('error', error)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}exports/orders/erpV2`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          responseType: 'blob',
          data: data
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          const date = new Date().getDate().toString().padStart(2, '0') + '_' + (new Date().getMonth() + 1).toString().padStart(2, '0') + '_' + new Date().getFullYear()
          const time = new Date().getHours().toString().padStart(2, '0') + '_' + new Date().getMinutes().toString().padStart(2, '0')
          fileLink.setAttribute('download', 'orderlist_shop_' + date + '_' + time + '.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      } catch (error) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    checkSearch () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        this.options.page = 1
        await this.getOrderList()
      }, 1000)
    },
    async updateOptionsTable (options) {
      this.options = options
      await this.getOrderList()
    },
    async GetETaxPDF (val) {

    },
    goDetailOrder (order, bill) {
      // console.log('val', val)
      if ((order !== undefined) && (bill !== undefined)) {
        if (this.MobileSize) {
          this.$router.push(`/DetailOrderProductShopMobile?orderNumber=${order}&billNumber=${bill}`).catch(() => {})
          // this.$router.push({ path: '/DetailOrderProductShopMobile' }).catch(() => { })
        } else {
          this.$router.push(`/DetailOrderProductShop?orderNumber=${order}&billNumber=${bill}`).catch(() => {})
          // this.$router.push({ path: '/DetailOrderProductShop' }).catch(() => { })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(11) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
          text-align: center !important;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(11) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          text-align: center !important;
        }
      }
    }
  }
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
