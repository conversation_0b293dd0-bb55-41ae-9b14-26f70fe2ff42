<template>
  <div>
    <!-- Website -->
    <div v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="6" md="12" align="center" class="my-16">
            <v-form ref="formRegis" :lazy-validation="lazy">
              <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
                <v-card-text>
                  <v-card-title class="pb-0"><v-btn fab small elevation="0" color="#EBF1F9" @click="backtoLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn></v-card-title>
                  <v-container class="pt-0">
                    <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                      <v-img :src="require('@/assets/obj.png')" max-height="50%" max-width="60%" contain/>
                    </v-row>
                    <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                      <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.LoginWithOTP') }}</span>
                    </v-row>
                    <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                      <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: #A1A1A1;">{{ $t('OTPPage.DescribeOTP') }}</span>
                    </v-row>
                    <v-row no-gutters dense class="mx-12">
                      <v-col cols="12" md="12" sm="12">
                        <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('OTPPage.PhoneOTP') }}</h3>
                        <v-text-field color="#269AFD" v-model="telephone" outlined :placeholder="$t('OTPPage.EnterPhone')" dense maxLength="10" @paste.prevent  @copy.prevent @keydown.space.prevent @keydown.enter.prevent :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <!-- <v-col cols="12" md="12" sm="12" class="mt-8">
                        <v-row dense class="ml-1">
                          <v-checkbox
                            v-model="checkConsent"
                            hide-details
                            class="consent-login-otp mt-0 pt-0"
                            label="ยอมรับ ข้อกำหนดการใช้บริการ และ นโยบายคุ้มครองส่วนบุคคล"
                          ></v-checkbox>
                        </v-row>
                      </v-col> -->
                      <v-col cols="12" md="12" sm="12" class="mt-0">
                        <v-btn color="#269AFD" rounded block :disabled="telephone.length !== 10" style="color: white;" @click="confirmOTP()">{{ $t('register.SendOTPButton') }}</v-btn>
                      </v-col>
                      <!-- <v-col cols="12" md="12" sm="12" class="mt-9">
                        <v-row dense justify="center" align-content="center">
                          <span style="font-size: 14px; line-height: 22px; color: #000000;">คุณยังไม่มีบัญชีผู้ใช้ ใช่หรือไม่? <span style="color: #1E90FF; cursor: pointer;" @click="link('Register')">ลงทะเบียน</span></span>
                        </v-row>
                      </v-col> -->
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div v-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
            <v-card-text>
              <v-card-title class="pb-0"><v-btn fab small elevation="0" color="#EBF1F9" @click="backtoLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn></v-card-title>
              <v-container class="pt-0">
                <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                  <v-img :src="require('@/assets/obj.png')" max-height="50%" max-width="60%" contain/>
                </v-row>
                <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                  <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.LoginWithOTP') }}</span>
                </v-row>
                <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                  <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: #A1A1A1;">{{ $t('OTPPage.DescribeOTP') }}</span>
                </v-row>
                <v-row no-gutters dense class="mx-12">
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">{{ $t('OTPPage.PhoneOTP') }}</h3>
                    <v-text-field color="#269AFD" v-model="telephone" outlined :placeholder="$t('OTPPage.EnterPhone')" dense maxLength="10" @paste.prevent  @copy.prevent @keydown.space.prevent @keydown.enter.prevent :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- <v-col cols="12" md="12" sm="12" class="mt-4">
                    <v-row dense justify="center" align-content="center">
                      <v-checkbox
                        v-model="checkConsent"
                        hide-details
                        class="consent-login-otp mt-0 pt-0"
                        label="ยอมรับ ข้อกำหนดการใช้บริการ และ นโยบายคุ้มครองส่วนบุคคล"
                      ></v-checkbox>
                    </v-row>
                  </v-col> -->
                  <v-col cols="12" md="12" sm="12" class="mt-0">
                    <v-btn color="#269AFD" rounded block :disabled="telephone.length !== 10" style="color: white;" @click="confirmOTP()">{{ $t('register.SendOTPButton') }}</v-btn>
                  </v-col>
                  <!-- <v-col cols="12" md="12" sm="12" class="mt-9">
                    <v-row dense justify="center" align-content="center">
                      <span style="font-size: 14px; line-height: 22px; color: #000000;">คุณยังไม่มีบัญชีผู้ใช้ ใช่หรือไม่? <span style="color: #1E90FF; cursor: pointer;" @click="link('Register')">ลงทะเบียน</span></span>
                    </v-row>
                  </v-col> -->
                </v-row>
              </v-container>
            </v-card-text>
          </v-card>
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div v-if="MobileSize">
      <v-container class="my-6">
        <v-row dense justify="center">
          <v-col cols="12" md="12" align="center">
            <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
              <v-card-text>
                <v-card-title class="pb-0"><v-btn fab small elevation="0" color="#EBF1F9" @click="backtoLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn></v-card-title>
                <v-container class="pt-0">
                  <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                    <v-img :src="require('@/assets/obj.png')" max-height="50%" max-width="60%" contain/>
                  </v-row>
                  <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                    <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('OTPPage.LoginWithOTP') }}</span>
                  </v-row>
                  <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                    <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: #A1A1A1;">{{ $t('OTPPage.DescribeOTP') }}</span>
                  </v-row>
                  <v-row no-gutters dense class="mx-1">
                    <v-col cols="12" md="12" sm="12">
                      <h3 class="mb-0" style="text-align: left; font-size: 15px;">เบอร์โทรศัพท์</h3>
                      <v-text-field color="#269AFD" v-model="telephone" outlined :placeholder="$t('OTPPage.EnterPhone')" dense maxLength="10" @paste.prevent  @copy.prevent @keydown.space.prevent @keydown.enter.prevent :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <!-- <v-col cols="12" md="12" sm="12" class="mt-4">
                      <v-row dense justify="center" align-content="center">
                        <v-checkbox
                          v-model="checkConsent"
                          hide-details
                          class="consent-login-otp mt-0 pt-0"
                          label="ยอมรับ ข้อกำหนดการใช้บริการ และ นโยบายคุ้มครองส่วนบุคคล"
                        ></v-checkbox>
                      </v-row>
                    </v-col> -->
                    <v-col cols="12" md="12" sm="12" class="mt-0">
                      <v-btn color="#269AFD" rounded block :disabled="telephone.length !== 10" style="color: white;" @click="confirmOTP()">{{ $t('register.Confirm') }}</v-btn>
                    </v-col>
                    <!-- <v-col cols="12" md="12" sm="12" class="mt-9">
                      <v-row dense justify="center" align-content="center">
                        <span style="font-size: 14px; line-height: 22px; color: #000000;">คุณยังไม่มีบัญชีผู้ใช้ ใช่หรือไม่? <span style="color: #1E90FF; cursor: pointer;" @click="link('Register')">ลงทะเบียน</span></span>
                      </v-row>
                    </v-col> -->
                  </v-row>
                </v-container>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      telephone: '',
      checkConsent: false,
      lazy: false,
      Rules: {
        tel: [
          v => !!v || this.$t('OTPPage.ValidateOTP1'),
          v => v.length === 10 || v === '' || this.$t('OTPPage.ValidateOTP2')
        ]
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
  },
  methods: {
    backtoLogin () {
      this.$router.push({ path: '/Login' }).catch(() => {})
    },
    link (val) {
      this.$router.replace({ path: `/${val}` }).catch(() => {})
    },
    async confirmOTP () {
      this.$store.commit('openLoader')
      var data = {
        mobile_no: this.telephone
      }
      await this.$store.dispatch('actionsGetOTP', data)
      var res = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
      if (res.result === 'SUCCESS') {
        var dataOTP = {
          otp: res.data.otp,
          ref_code: res.data.ref_code,
          mobileNo: this.telephone
        }
        localStorage.setItem('OTPData', Encode.encode(dataOTP))
        this.$store.commit('closeLoader')
        this.$router.replace({ path: '/otpVerificationLogin' }).catch(() => {})
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'Mobile_no not found') {
          this.$swal.fire({
            icon: 'warning',
            html: `<p>${this.$t('OTPPage.OTPNotRegister1')}</p><span>${this.$t('OTPPage.OTPNotRegister2')}</span>`,
            showConfirmButton: true,
            confirmButtonText: `${this.$t('OTPPage.yes')}`,
            confirmButtonColor: '#27AB9C',
            showCancelButton: true,
            cancelButtonText: `${this.$t('OTPPage.no')}`
          }).then(async (result) => {
            if (result.isConfirmed) {
              this.$router.push({ path: '/Register' }).catch(() => {})
            }
          })
        } else {
          this.$swal.fire({ icon: 'warning', title: this.$t('OTPPage.OTPFail'), showConfirmButton: false, timer: 1500 })
        }
      }
    }
  }
}
</script>

<style scoped>
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>

<style>
.consent-login-otp .v-input--selection-controls__input + .v-label {
  font-size: 14px !important;
}
</style>
