<template>
  <div class="text-center">
    <v-dialog v-model="openModalSettingQuotation" width="700" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>{{ title }}</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <!-- detail Mobile -->
        <v-container v-if="MobileSize" grid-list-xs>
          <v-card-text class="pb-0">
            <v-form ref="FormSettingQu" lazy-validation>
              <v-row no-gutters>
                <v-col cols="12">
                  <span class="float-left pt-2">ชื่อเริ่มต้นผู้ทำการออกใบเสนอราคา</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุชื่อเริ่มต้นผู้ทำการออกใบเสนอราคา" outlined dense v-model="data.prepared_by_default" :rules="Rules.approveText" maxlength="40" :counter="40"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="float-left pt-2">ชื่อเริ่มต้นผู้ทำการอนุมัติใบเสนอราคา</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุชื่อเริ่มต้นผู้ทำการอนุมัติใบเสนอราคา" outlined dense v-model="data.approve_by_default" :rules="Rules.approveText" maxlength="40" :counter="40"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="float-left pt-2">ต้องการแสดงโลโก้ของร้านค้าหรือไม่</span>
                </v-col>
                <v-col cols="12">
                  <v-checkbox class="float-left" dense v-model="data.use_logo" label="ใช่" value="Y" :rules="Rules.emptyCheckbox"></v-checkbox>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_logo" label="ไม่" value="N" :rules="Rules.emptyCheckbox"></v-checkbox>
                </v-col>
                <v-col cols="12" v-if="data.use_logo === 'Y'">
                  <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                    <v-card-text class="px-0">
                      <v-card elevation="0" @click="onPickFile()" :style="errUploadImg ? 'border: 2px dashed #FF5252;' : 'border: 2px dashed #27AB9C;'" style="box-sizing: border-box; border-radius: 8px;">
                        <v-card-text>
                          <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                            <v-file-input
                              v-model="DataImage"
                              :items="DataImage"
                              accept="image/jpeg, image/jpg, image/png, video/quicktime, video/mp4"
                              @change="UploadImage()"
                              id="file_input"
                              multiple
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-col cols="12" md="12" class="mb-6">
                              <v-row justify="center" class="pt-10">
                                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/settingQuotation.png" width="280.34" height="154.87" contain></v-img>
                              </v-row>
                            </v-col>
                            <v-col cols="12" md="12" class="mt-6">
                              <v-row justify="center" align="center">
                                <v-col cols="12" style="text-align: center;">
                                  <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มรูปภาพโลโก้ของคุณที่นี่</span><br />
                                  <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG,JPG,PNG)</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                      <div v-if="data.logo_img.length !== 0" class="mt-4">
                        <!-- <draggable v-model="data.logo_img" :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list"> -->
                          <v-col v-for="(item, index) in data.logo_img" :key="index" cols="12" md="3" sm="4" class="px-0">
                            <v-card outlined class="pa-1" width="146" height="146">
                              <v-img :src="item.url" :lazy-src="item.url" height="130" contain>
                                <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                  <v-icon x-small color="white" dark @click="RemoveImage(index)">mdi-close</v-icon>
                                </v-btn>
                              </v-img>
                            </v-card>
                          </v-col>
                        <!-- </draggable> -->
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col cols="12">
                  <span class="float-left pt-2">ต้องการกำหนดรูปแบบหมายเลขใบเสนอราคา</span>
                </v-col>
                <v-col cols="12">
                  <v-checkbox class="float-left" dense v-model="data.use_pattern" label="ใช่" value="Y" :rules="Rules.emptyCheckbox"></v-checkbox>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_pattern" label="ไม่" value="N" :rules="Rules.emptyCheckbox"></v-checkbox>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">รูปแบบหมายเลขใบเสนอราคา</span>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <v-text-field class="input_text" placeholder="ระบุรูปแบบหมายเลขใบเสนอราคาไม่เกิน 5 ตัวอักษร" outlined dense v-model="data.code_pattern" @input="setExempleCode()" maxlength="5" :counter="5" :rules="Rules.quPattern" ></v-text-field>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">ต้องการใช้วันที่ในรูปแบบหรือไม่</span>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <v-checkbox class="float-left" dense v-model="data.use_date_in_pattern" label="ใช่" value="Y" :rules="Rules.emptyCheckbox" @change="setExempleCode()"></v-checkbox>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_date_in_pattern" label="ไม่" value="N" :rules="Rules.emptyCheckbox" @change="setExempleCode()"></v-checkbox>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">จำนวนตัวเลขที่ต้องการใช้ในรูปแบบ</span>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <v-text-field class="float-left input_text" placeholder="ระบุจำนวน" outlined dense v-model="data.number_pattern" @input="setExempleCode()" :rules="Rules.numberPattern" oninput="this.value = this.value.replace(/[^\d]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  <span class="float-left pl-3 pt-2">หลัก</span>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">ตัวอย่างหมายเลขใบเสนอราคา</span>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <v-text-field class="input_text" readonly disabled outlined dense v-model="data.exemple_number_code_pattern"></v-text-field>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-btn class="px-3" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn v-if="action === 'edit' && data.status === 'active'" class="px-5 white--text" color="#D1392B" @click="updateStatusSettingQu('inactive')">ยกเลิกใช้งาน</v-btn>
            <v-btn v-if="action === 'edit' && data.status === 'inactive'" class="px-5 white--text" color="#1AB759" @click="updateStatusSettingQu('active')">เปิดใช้งาน</v-btn>
            <v-btn class="px-3 white--text" color="#27AB9C" @click="sendSettingQuotation()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
       <!-- detail desktop, ipadpro, ipad -->
        <v-container v-else grid-list-xs>
          <v-card-text>
            <v-form ref="FormSettingQu" lazy-validation>
              <v-row no-gutters>
                <v-col cols="12">
                  <span class="float-left pt-2">ชื่อเริ่มต้นผู้ทำการออกใบเสนอราคา : </span>
                  <v-text-field class="pl-3 input_text" placeholder="ระบุชื่อเริ่มต้นผู้ทำการออกใบเสนอราคา" outlined dense v-model="data.prepared_by_default" :rules="Rules.approveText" maxlength="40" :counter="40"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="float-left pt-2">ชื่อเริ่มต้นผู้ทำการอนุมัติใบเสนอราคา : </span>
                  <v-text-field class="pl-3 input_text" placeholder="ระบุชื่อเริ่มต้นผู้ทำการอนุมัติใบเสนอราคา" outlined dense v-model="data.approve_by_default" :rules="Rules.approveText" maxlength="40" :counter="40"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="float-left pt-2">ต้องการแสดงโลโก้ของร้านค้าหรือไม่ : </span>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_logo" label="ใช่" value="Y" :rules="Rules.emptyCheckbox"></v-checkbox>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_logo" label="ไม่" value="N" :rules="Rules.emptyCheckbox"></v-checkbox>
                </v-col>
                <v-col cols="12" v-if="data.use_logo === 'Y'">
                  <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                    <v-card-text>
                      <v-card elevation="0"  @click="onPickFile()" :style="errUploadImg ? 'border: 2px dashed #FF5252;' : 'border: 2px dashed #27AB9C;'" style="box-sizing: border-box; border-radius: 8px;">
                        <v-card-text>
                          <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                            <v-file-input
                              v-model="DataImage"
                              :items="DataImage"
                              accept="image/jpeg, image/jpg, image/png, video/quicktime, video/mp4"
                              @change="UploadImage()"
                              id="file_input"
                              multiple
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-col cols="12" md="12" class="mb-6">
                              <v-row justify="center" class="pt-10">
                                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/settingQuotation.png" width="280.34" height="154.87" contain></v-img>
                              </v-row>
                            </v-col>
                            <v-col cols="12" md="12" class="mt-6">
                              <v-row justify="center" align="center">
                                <v-col cols="12" style="text-align: center;">
                                  <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มรูปภาพโลโก้ของคุณที่นี่</span><br />
                                  <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG,JPG,PNG)</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                      <div v-if="data.logo_img.length !== 0" class="mt-4">
                        <!-- <draggable v-model="data.logo_img" :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list"> -->
                          <v-col v-for="(item, index) in data.logo_img" :key="index" cols="12" md="3" sm="4">
                            <v-card outlined class="pa-1" width="146" height="146">
                              <v-img :src="item.url" :lazy-src="item.url" height="130" contain>
                                <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                  <v-icon x-small color="white" dark @click="RemoveImage(index)">mdi-close</v-icon>
                                </v-btn>
                              </v-img>
                            </v-card>
                          </v-col>
                        <!-- </draggable> -->
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col cols="12">
                  <span class="float-left pt-2">ต้องการกำหนดรูปแบบหมายเลขใบเสนอราคา : </span>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_pattern" label="ใช่" value="Y" :rules="Rules.emptyCheckbox"></v-checkbox>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_pattern" label="ไม่" value="N" :rules="Rules.emptyCheckbox"></v-checkbox>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">รูปแบบหมายเลขใบเสนอราคา : </span>
                  <v-text-field class="pl-3 input_text" placeholder="ระบุรูปแบบหมายเลขใบเสนอราคาไม่เกิน 5 ตัวอักษร" outlined dense v-model="data.code_pattern" @input="setExempleCode()" maxlength="5" :counter="5" :rules="Rules.quPattern" ></v-text-field>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">ต้องการใช้วันที่ในรูปแบบหรือไม่ : </span>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_date_in_pattern" label="ใช่" value="Y" :rules="Rules.emptyCheckbox" @change="setExempleCode()"></v-checkbox>
                  <v-checkbox class="float-left pl-3" dense v-model="data.use_date_in_pattern" label="ไม่" value="N" :rules="Rules.emptyCheckbox" @change="setExempleCode()"></v-checkbox>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">จำนวนตัวเลขที่ต้องการใช้ในรูปแบบ : </span>
                  <v-text-field class="float-left pl-3 input_text" placeholder="ระบุจำนวน" outlined dense v-model="data.number_pattern" @input="setExempleCode()" :rules="Rules.numberPattern" oninput="this.value = this.value.replace(/[^\d]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  <span class="float-left pl-3 pt-2">หลัก</span>
                </v-col>
                <v-col cols="12" v-if="data.use_pattern === 'Y'">
                  <span class="float-left pt-2">ตัวอย่างหมายเลขใบเสนอราคา : </span>
                  <v-text-field class="pl-3 input_text" readonly disabled outlined dense v-model="data.exemple_number_code_pattern"></v-text-field>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn v-if="action === 'edit' && data.status === 'active'" class="px-5 white--text" color="#D1392B" @click="updateStatusSettingQu('inactive')">ยกเลิกใช้งาน</v-btn>
            <v-btn v-if="action === 'edit' && data.status === 'inactive'" class="px-5 white--text" color="#1AB759" @click="updateStatusSettingQu('active')">เปิดใช้งาน</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="sendSettingQuotation()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import { Decode } from '@/services'
// import eventBus from '@/components/eventBus'
export default {
  // props: ['ModalTierDetail'],
  data () {
    return {
      title: '',
      lazy: false,
      errUploadImg: false,
      openModalSettingQuotation: false,
      action: '',
      editData: '',
      DataImage: [],
      data: {
        qu_setting_id: '',
        seller_shop_id: '',
        prepared_by_default: '',
        approve_by_default: '',
        use_logo: '',
        logo_img: [],
        use_pattern: '',
        code_pattern: '',
        number_pattern: '',
        use_date_in_pattern: '',
        exemple_number_code_pattern: '',
        status: '',
        formPage: '',
        editData: ''
      },
      Rules: {
        approveText: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => v.length <= 40 || 'ห้ามกรอกชื่อเกิน 40 ตัวอักษร'
        ],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        quPattern: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[A-Za-z0-9_-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ ตัวเลข หรือเครื่องหมาย "-" และ "_"'
        ],
        emptyCheckbox: [v => !!v || 'กรุณาเลือกข้อมูล'],
        numberPattern: [
          v => !!v || 'กรุณาเลือกข้อมูล',
          v => (v >= 5 && v <= 10) || 'กรอกจำนวนที่มีค่าตั้งแต่ 5 และไม่เกิน 10'
        ]
      }
    }
  },
  watch: {
  },
  mounted () {
  },
  created () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('IpadProSize')
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('IpadSize')
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    open (item, status, page) {
      this.openModalSettingQuotation = true
      this.action = status
      this.formPage = page
      this.editData = item
      // console.log('open', item)
      if (this.action === 'edit') {
        this.data.qu_setting_id = item.id
        this.data.seller_shop_id = item.seller_shop_id
        this.data.status = item.status
        this.data.prepared_by_default = item.prepared_by_default
        this.data.approve_by_default = item.approve_by_default
        this.data.use_logo = item.use_logo
        this.data.use_pattern = item.use_pattern
        this.data.code_pattern = item.code_pattern
        this.data.number_pattern = item.number_pattern
        this.data.use_date_in_pattern = item.use_date_in_pattern
        this.setExempleCode() // Set this.data.exemple_number_code_pattern
        if (item.logo_path !== null) {
          this.data.logo_img[0] = { image_data: '', url: item.logo_path, type: '', name: '' }
        }
        this.title = 'แก้ไขรูปแบบหมายเลขใบเสนอราคา'
      } else {
        this.title = 'กำหนดรูปแบบหมายเลขใบเสนอราคา'
      }
    },
    cancel () {
      this.clearData()
      this.openModalSettingQuotation = false
    },
    clearData () {
      this.DataImage = []
      this.data.logo_img = []
      this.$refs.FormSettingQu.resetValidation()
      this.$refs.FormSettingQu.reset()
    },
    setExempleCode () {
      var random = ''
      if (this.data.number_pattern !== '') {
        if (this.data.number_pattern >= 5 && this.data.number_pattern <= 10) {
          var numPattern = this.action === 'edit' ? this.editData.number_pattern : this.data.number_pattern
          random = this.getRandom(numPattern)
        }
      }
      var date = new Date()
      var day = this.padTo2Digits(date.getDate())
      var month = this.padTo2Digits(date.getMonth() + 1)
      var year = this.padTo2Digits(date.getFullYear()).slice(2)
      var dateInPattern = year + month + day
      if (this.data.use_date_in_pattern === 'Y') {
        this.data.exemple_number_code_pattern = this.data.code_pattern + dateInPattern + random
      } else {
        this.data.exemple_number_code_pattern = this.data.code_pattern + random
      }
    },
    padTo2Digits (num) {
      return num.toString().padStart(2, '0')
    },
    getRandom (length) {
      return Math.floor(Math.pow(10, length - 1) + Math.random() * 9 * Math.pow(10, length - 1))
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      if (this.data.logo_img.length < 1) {
        if (this.DataImage !== undefined && this.DataImage.length <= 1) {
          for (let i = 0; i < this.DataImage.length; i++) {
            this.errUploadImg = false
            const element = this.DataImage[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
              if (imageSize > 2 && (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png')) {
                this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพที่มีขนาดไม่เกิน 2 MB', showConfirmButton: false, timer: 2500 })
              } else {
                // const imageSize = element.size / 1024 / 1024
                const reader = new FileReader()
                // console.log('reader', reader)
                reader.readAsDataURL(element)
                reader.onload = () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  this.data.logo_img.push({
                    image_data: resultReader,
                    url: url,
                    type: element.type,
                    name: this.DataImage[i].name
                  })
                }
              }
              // console.log('this.data.logo_img upload', this.data.logo_img)
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์ jpeg, jpg, png เท่านั้น', showConfirmButton: false, timer: 2500 })
            }
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    RemoveImage (index) {
      // console.log('index', index)
      // console.log('this.data.logo_img remove', this.data.logo_img)
      this.data.logo_img.splice(index, 1)
      // console.log('this.data.logo_img remove after', this.data.logo_img)
    },
    checkActionCallApi () {
      if (this.action === 'create') {
        this.create()
      } else {
        this.edit()
      }
    },
    sendSettingQuotation () {
      if (this.$refs.FormSettingQu.validate(true)) {
        if (this.data.use_logo === 'Y' && this.data.logo_img.length !== 0) {
          this.checkActionCallApi()
        } else {
          if (this.data.use_logo === 'Y') {
            this.errUploadImg = true
            this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'กรุณาเพิ่มรูปภาพโลโก้ของร้านค้า' })
          } else {
            this.checkActionCallApi()
          }
        }
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'กรุณากรอกข้อมูลให้ครบ' })
      }
    },
    async create () {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      const data = {
        seller_shop_id: shopId,
        prepared_by_default: this.data.prepared_by_default,
        approve_by_default: this.data.approve_by_default,
        use_logo: this.data.use_logo,
        logo_img: this.data.use_logo === 'Y' ? this.data.logo_img.length !== 0 ? this.splitBase64(this.data.logo_img[0].image_data) : '' : '',
        use_pattern: this.data.use_pattern,
        code_pattern: this.data.use_pattern === 'Y' ? this.data.code_pattern : '',
        number_pattern: this.data.use_pattern === 'Y' ? this.data.number_pattern : '',
        use_date_in_pattern: this.data.use_pattern === 'Y' ? this.data.use_date_in_pattern : ''
      }
      await this.$store.dispatch('actionsCreateQuotation', data)
      const res = await this.$store.state.ModuleSettingQuotation.stateCreateQuotation
      if (res.message === 'Create QU Setting success.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('settingQUSuccess')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ดำเนินการสำเร็จ'
        })
        this.clearData()
        this.openModalSettingQuotation = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    },
    splitBase64 (data) {
      const base64 = data.split(',')
      return base64[1]
    },
    checkImage (data) {
      var sendData = ''
      if (data.use_logo === 'Y') {
        if (data.logo_img.length !== 0) {
          if (data.logo_img[0].image_data !== '') {
            sendData = this.splitBase64(data.logo_img[0].image_data)
          } else {
            if (data.logo_img[0].url !== '') {
              sendData = data.logo_img[0].url
            }
          }
        }
      }
      return sendData
    },
    async edit () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.data.seller_shop_id,
        qu_setting_id: this.data.qu_setting_id,
        prepared_by_default: this.data.prepared_by_default,
        approve_by_default: this.data.approve_by_default,
        use_logo: this.data.use_logo,
        logo_img: this.checkImage(this.data),
        use_pattern: this.data.use_pattern,
        code_pattern: this.data.use_pattern === 'Y' ? this.data.code_pattern : '',
        number_pattern: this.data.use_pattern === 'Y' ? this.data.number_pattern : '',
        use_date_in_pattern: this.data.use_pattern === 'Y' ? this.data.use_date_in_pattern : ''
      }
      await this.$store.dispatch('actionsEditQuotationSetting', data)
      const res = await this.$store.state.ModuleSettingQuotation.stateEditQuotation
      if (res.message === 'Edit QU Setting success.') {
        this.$EventBus.$emit('settingQUSuccess')
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ดำเนินการสำเร็จ'
        })
        this.clearData()
        if (this.formPage === 'detail') {
          this.$EventBus.$emit('editSettingQUSuccess')
        }
        this.openModalSettingQuotation = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    },
    async updateStatusSettingQu (status) {
      const data = {
        seller_shop_id: this.data.seller_shop_id,
        qu_setting_id: this.data.qu_setting_id,
        status: status
      }
      await this.$store.dispatch('actionsUpdateStatusQuotation', data)
      const res = await this.$store.state.ModuleSettingQuotation.stateUpdateStatusQuotation
      if (res.message === 'Update status QU Setting success.') {
        this.$EventBus.$emit('settingQUSuccess')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ดำเนินการสำเร็จ'
        })
        this.openModalSettingQuotation = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
.title-product {
  font-size: 14px;
}
.detail-product {
  font-size: 14px;
}
.shop-name {
  font-weight: 700;
  font-size: 20px;
}
</style>
