<template>
  <div :class="MobileSize ? 'background_productMobile mt-2 pa-4' : 'background_product mt-0 pa-8'" style="background-color: #FFFFFF">
    <v-form ref="FormManageXY">
      <div>
        <v-row dense>
          <v-icon v-if="MobileSize" color="#27AB9C" class="" @click="canCel()">mdi-chevron-left</v-icon><span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">{{title}}โปรโมชัน Xแถม Y</span>
        </v-row>
        <v-row dense class="mt-4">
          <span class="subTitle1" :style="MobileSize ? 'font-size: 16px;' : ''">ข้อมูลทั่วไป</span>
          <v-col cols="12" md="12">
            <v-card @click="onPickFile()" :disabled="img.length > 0" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;">
              <v-file-input
                v-model="DataImage"
                :items="DataImage"
                accept="image/jpeg, image/jpg, image/png"
                @click="event => event.target.value = null"
                @change="uploadImage()"
                id="file_input"
                :clearable="false"
                style="display:none">
              </v-file-input>
                <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                    <v-col cols="12" md="12" align="center">
                      <v-img
                        src="@/assets/icons/Upload.png"
                        width="280.34"
                        height="154.87"
                        contain
                      ></v-img>
                    </v-col>
                    <v-col cols="12" md="12" style="text-align: center;">
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                      <span style="line-height: 16px; font-weight: 400;"
                        :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                    </v-col>
                  </v-row>
                </v-col>
            </v-card>
          </v-col>

          <v-col cols="4" md="3" v-if="img.length > 0">
            <v-card width="136px" elevation="1" :class="MobileSize ? 'pa-2 mb-5' : 'pa-4 mb-5'">
              <v-btn icon small style="float: right; background-color: #ff5252;" v-if="!MobileSize">
                <v-icon small color="white" dark @click="removeImage()">mdi-close</v-icon>
              </v-btn>
              <v-btn icon x-small style="float: right; background-color: #ff5252;" v-else>
                <v-icon small color="white" dark @click="removeImage()">mdi-close</v-icon>
              </v-btn>
              <v-img :src="img[0].path" :lazy-src="img[0].path" width="136px" max-height="129px" contain class="mt-2">
              </v-img>
            </v-card>
          </v-col>
        </v-row>

        <v-row dense class="mt-4">
          <v-col cols="12"><span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">ข้อมูลโปรโมชัน</span></v-col>
          <!-- <v-col cols="12" class="pt-4">
            <span class="detail1">ต้องการใช้โปรโมชันอัตโนมัติโดยไม่ต้องกรอกโค้ดหรือไม่ <span style="color:#F5222D">*</span></span>
            <v-row no-gutters>
              <v-radio-group v-model="useCode" row>
                <v-radio label="ใช่" value="yes" ></v-radio>
                <v-radio label="ไม่ใช่" value="no" ></v-radio>
              </v-radio-group>
            </v-row>
          </v-col> -->
          <v-col cols="12" md="12">
            <span class="detail1">ชื่อคูปอง <span style="color:#F5222D">*</span></span>
            <v-text-field v-model="couponName" :rules="Rules.empty" placeholder="ระบุชื่อโปรโมชัน" @keypress="CheckSpacebar($event)" outlined dense></v-text-field>
          </v-col>
          <!-- <v-col cols="12" md="6">
            <span class="detail1">Sku คูปอง <span style="color:#F5222D">*</span></span>
            <v-text-field :maxLength="15" :counter="15" :rules="Rules.fourand15" v-model="couponCode" placeholder="ระบุ SKU คูปอง" @keypress="CheckSpacebar($event)" oninput="this.value = this.value.replace(/^[-]/, '').replace(/[^a-z0-9]/gi, '')" outlined dense></v-text-field>
          </v-col> -->
          <v-col cols="12" class="pt-0">
            <span class="detail1">รายละเอียดโปรโมชัน</span>
            <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="couponDescription" @ready="onReady"></ckeditor>
            <span class="rule">* รายละเอียดโปรโมชัน วิธีใช้งานและสิทธิประโยชน์สำหรับสมาชิกอย่างละเอียด</span>
          </v-col>
        </v-row>
        <div class="mt-4">
          <!-- <span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">กำหนดจำนวนโปรโมชัน และสิทธิ์โปรโมชัน</span> -->
          <v-row dense class="mt-3">
            <!-- <v-col cols="12" md="6">
              <span class="detail1">ประเภทของส่วนลด</span>
              <v-text-field placeholder="โปรโมชันส่วนลดค่าสินค้า บริการ และค่าขนส่ง" outlined dense readonly disabled></v-text-field>
            </v-col> -->
            <v-col cols="12" md="6">
              <span class="detail1">จำนวนคูปองสูงสุด <span style="color:#F5222D">*</span></span>
              <v-text-field v-model="amonutUse" :rules="Rules.empty" :maxLength="7" placeholder="จำนวนคูปองสูงสุด" @input="checkZero('qouta')" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <span class="detail1">จำนวนคูปองที่ใช้ได้ต่อคน <span style="color:#F5222D">*</span></span>
              <v-text-field v-model="amonutCap" :maxLength="7" placeholder="จำนวนคูปองที่ใช้ได้ต่อคน" @input="checkZero('usecap')" :rules="Rules.amonutCap" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
          </v-row>
          <!-- <v-row dense>
            <v-col cols="12" md="4">
              <span class="detail1">สิทธิ์การใช้โปรโมชัน <span style="color:#F5222D">*</span></span>
              <v-radio-group v-model="usePremiss" dense hide-details class="detail1 ma-0">
                <v-radio label="1 คน ต่อ 1 สิทธิ์การใช้งาน" value="1"></v-radio>
                <v-radio label="ไม่จำกัดสิทธิ์การใช้" value="0"></v-radio>
                <v-radio label="กำหนดเอง" value=""></v-radio>
              </v-radio-group>
              <v-text-field v-model="usePremiss2" :rules="[itemRules.CheckCountPromotion(usePremiss2, amonutUse)]" v-if="usePremiss === ''" type="number" placeholder="ระบุจำนวนสิทธิ์การใช้โปรโมชัน" class="ml-8" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
          </v-row> -->
        </div>

        <div class="mt-4">
          <v-row class="">
            <v-col cols="12">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">กำหนดระยะเวลาเวาเชอร์</span>
            </v-col>
          </v-row>
          <div class="mt-4">
            <span class="detail1">ระยะเวลาเก็บเวาเชอร์ <span style="color:#F5222D">*</span></span>
            <v-row dense style="align-items: center;">
              <v-col cols="2" v-if="!MobileSize"><span class="detail1">วันที่เริ่ม - สิ้นสุด</span></v-col>
              <v-col cols="4" v-if="MobileSize"><span class="detail1">วันที่เริ่ม</span></v-col>
              <!-- collect start -->
              <div>
              <v-dialog
                ref="dialogStartDate1"
                v-model="dialogStartDate1"
                :return-value.sync="date11"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentStartDate1"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :rules="Rules.empty"
                    @click="forceRerender(), date21 = '', time21 = '', date22 = '', time22 = '', sentStartDate2 = '', sentEndDate2 = '', date12 = '', time12 = '', sentEndDate1 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date11"
                  reactive
                  locale="TH-th"
                  :min="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  :max="date21"
                  @change="time11 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                          v-model="time11"
                          :bordered="false"
                          style="width: 100%;"
                          format="HH:mm:ss น."
                          valueFormat="HH:mm:ss"
                          size="large"
                          placeholder="00.00.00 น."
                          :disabled="date11 === ''"
                          :placement="'topLeft'"
                          :defaultOpenValue="defaultDate"
                          :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                        />
                      </v-col>
                      <!-- <v-menu ref="menu1" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time11" :disabled="date11 === ''" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time11"
                          use-seconds
                          scrollable
                          :key="component"
                          :min="date11 == new Date().toISOString().substr(0, 10) ? new Date().toLocaleTimeString() : ''"
                          :max="date11 == date21 ? time21 : ''"
                          @click:second="$refs.menu1.save(time11)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu> -->
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogStartDate1 = false, date11 === '' ? time11 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date11 == '' || time11 == ''" @click="setValueDate(date11, 'date11'), $refs.dialogStartDate1.save(date11), date12 = '', sentEndDate1 = '', time12 = ''"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
              <span class="detail1 mx-4" v-if="!noEndDateCollect && !MobileSize"> - </span>
              <v-col cols="4" v-if="!noEndDateCollect && MobileSize"><span class="detail1">วันที่สิ้นสุด</span></v-col>
              <!-- collect end -->
              <div v-if="!noEndDateCollect">
              <v-dialog
                ref="dialogEndDate1"
                v-model="dialogEndDate1"
                :return-value.sync="date12"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentEndDate1"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    :disabled="sentStartDate1 === ''"
                    v-on="on"
                    :rules="Rules.datesMustNotBeSame1"
                    @click="forceRerender(), date21 = '', time21 = '', date22 = '', time22 = '', sentStartDate2 = '', sentEndDate2 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date12"
                  reactive
                  locale="TH-th"
                  :min="date11 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time12 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                          v-model="time12"
                          :bordered="false"
                          style="width: 100%;"
                          format="HH:mm:ss น."
                          valueFormat="HH:mm:ss"
                          size="large"
                          placeholder="00.00.00 น."
                          :disabled="date12 === ''"
                          :placement="'topLeft'"
                          :disabledHours="disabledHours"
                          :disabledMinutes="disabledMinutes"
                          :disabledSeconds="disabledSeconds"
                          :defaultOpenValue="defaultDate"
                          :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                          />
                        <!-- <a-time-range-picker
                          use-seconds
                          :disabled="date12 === ''"
                          :bordered="false"
                          style="width: 100%;"
                          :placement="'topLeft'"
                        /> -->
                      </v-col>
                      <!-- <v-menu ref="menu2" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time12" :disabled="date12 === ''" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time12"
                          use-seconds
                          :key="component"
                          scrollable
                          :allowed-seconds="date12 == date11 ? allowedSeconds1 : ''"
                          :min="date12 == date11 ? time11 : ''"
                          @click:second="$refs.menu2.save(time12)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu> -->
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogEndDate1 = false, date12 === '' ? time12 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date12 == '' || time12 == ''" @click="setValueDate(date12, 'date12'), $refs.dialogEndDate1.save(date12)"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
            </v-row>
            <v-row class="mt-1">
              <v-col :cols="MobileSize? '5': '4'" class="pt-0">
                <v-checkbox v-model="noEndDateCollect" @click="noEnd('collect')" class="ma-0 pa-0" color="#27AB9C" label="ไม่ระบุวันสิ้นสุด"></v-checkbox>
              </v-col>
            </v-row>
          </div>

          <div>
            <span class="detail1">ระยะเวลาใช้งานเวาเชอร์<span style="color:#F5222D">*</span></span>
            <v-row dense style="align-items: center;">
              <v-col cols="2" v-if="!MobileSize"><span class="detail1">วันที่เริ่ม - สิ้นสุด</span></v-col>
              <v-col cols="4" v-if="MobileSize"><span class="detail1">วันที่เริ่ม</span></v-col>
              <!-- use start -->
              <div>
              <v-dialog
                ref="dialogStartDate2"
                v-model="dialogStartDate2"
                :return-value.sync="date21"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentStartDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :rules="Rules.empty"
                    @click="forceRerender(), date22 = '', time22 = '', sentEndDate2 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date21"
                  reactive
                  locale="TH-th"
                  :min="date11 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time21 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <!-- <v-menu ref="menu3" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time21" :disabled="date21 === ''" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time21"
                          use-seconds
                          scrollable
                          :key="component"
                          :min="date21 == new Date().toISOString().substr(0, 10) ? new Date().toLocaleTimeString() : date21 == date11 ? time11 : ''"
                          @click:second="$refs.menu3.save(time21)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu> -->
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                          v-model="time21"
                          :bordered="false"
                          style="width: 100%;"
                          format="HH:mm:ss น."
                          valueFormat="HH:mm:ss"
                          size="large"
                          placeholder="00.00.00 น."
                          :disabled="date21 === ''"
                          :placement="'topLeft'"
                          :disabledHours="disabledHoursUse"
                          :disabledMinutes="disabledMinutesUse"
                          :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                        />
                        <!-- <a-time-range-picker
                          use-seconds
                          :disabled="date21 === ''"
                          :bordered="false"
                          style="width: 100%;"
                          :placement="'topLeft'"
                        /> -->
                      </v-col>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogStartDate2 = false, date21 === '' ? time21 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date21 == '' || time21 == ''" @click="setValueDate(date21, 'date21'), $refs.dialogStartDate2.save(date21), date22 = '', sentEndDate2 = '', time22 = ''"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
              <span class="detail1 mx-4" v-if="!noEndDateUse && !MobileSize"> - </span>
              <v-col cols="4" v-if="!noEndDateUse && MobileSize"><span class="detail1">วันที่สิ้นสุด</span></v-col>
              <!-- use end -->
              <div v-if="!noEndDateUse">
              <v-dialog
                ref="dialogEndDate2"
                v-model="dialogEndDate2"
                :return-value.sync="date22"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentEndDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :rules="Rules.datesMustNotBeSame2"
                    @click="forceRerender()"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date22"
                  reactive
                  locale="TH-th"
                  :min="date21 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time22 = ''"
                >
                <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <!-- <v-menu ref="menu4" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-x max-width="290px" min-width="290px" >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="time22" :disabled="date22 === ''" readonly outlined dense v-bind="attrs" v-on="on" class="mt-2">
                            <v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon>
                          </v-text-field>
                        </template>
                        <v-time-picker
                          v-model="time22"
                          use-seconds
                          scrollable
                          :key="component"
                          :allowed-seconds="date22 == date21 ? allowedSeconds2 : ''"
                          :min="date22 == date21 ? time21 : ''"
                          @click:second="$refs.menu4.save(time22)"
                          full-width format="24hr"
                        ></v-time-picker>
                      </v-menu> -->
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                          v-model="time22"
                          :bordered="false"
                          style="width: 100%;"
                          format="HH:mm:ss น."
                          valueFormat="HH:mm:ss"
                          size="large"
                          placeholder="00.00.00 น."
                          :disabled="date22 === ''"
                          :placement="'topLeft'"
                          :disabledHours="disabledHoursRangeUse"
                          :disabledMinutes="disabledMinutesRangeUse"
                          :disabledSeconds="disabledSecondsRangeUse"
                          :defaultOpenValue="defaultDate"
                          :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                        />
                        <!-- <a-time-range-picker
                          use-seconds
                          :disabled="date22 === ''"
                          :bordered="false"
                          style="width: 100%;"
                          :placement="'topLeft'"
                        /> -->
                      </v-col>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogEndDate2 = false, date22 === '' ? time22 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date22 == '' || time22 == ''" @click="setValueDate(date22, 'date22'), $refs.dialogEndDate2.save(date22)"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
            </v-row>
            <v-row class="mt-1">
              <v-col :cols="MobileSize? '5': '4'" class="pt-0">
                <v-checkbox v-model="noEndDateUse" @click="noEnd('use')" class="ma-0 pa-0" color="#27AB9C" label="ไม่ระบุวันสิ้นสุด"></v-checkbox>
              </v-col>
            </v-row>
          </div>
        </div>
        <div>
          <v-row dense>
            <v-col cols="12">
              <v-row dense>
                <v-col cols="6" style="display: flex; align-content: center;">
                  <span style="font-weight: 700; font-size: 18px; display: flex; align-items: center;">กำหนดสิทธิ์เงื่อนไข</span>
                </v-col>
                <v-col cols="6" align="end">
                  <v-btn rounded outlined @click="plusBox()" style="color: #27AB9C;">
                    <span>เพิ่มสินค้า</span>
                  </v-btn>
                </v-col>
              </v-row>
              <!-- ของเก่า -->
              <!-- <v-row v-for="(item, index) in productList" :key="index">
                <v-col cols="11">
                  <v-row>
                    <v-col cols="4">
                      <span>สินค้าที่ {{ index + 1 }} *</span>
                      <div :style="item.productIdFail ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                        <treeselect class="setHeightTreeSelect" :key="componentAlpha" :backspaceRemoves="false" v-model="item.productId" noChildrenText="ไม่มีสินค้าในหมวดหมู่นี้" @input="deleteFromAlpha(item, 'alpha')" :disable-branch-nodes="true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :options="dataAlpha"  :normalizer="normalizerAlpha" openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
                      </div>
                    </v-col>
                    <v-col cols="2">
                      <span>จำนวนสินค้า *</span>
                      <v-text-field v-model="item.productNum" :rules="Rules.empty" dense type="number" outlined></v-text-field>
                    </v-col>
                    <v-col cols="4">
                      <span>สินค้าแถมที่ {{ index + 1 }} *</span>
                      <div :style="item.productFreeIdFail ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                        <treeselect class="setHeightTreeSelect" :key="componentBeta" :backspaceRemoves="false" v-model="item.productFreeId" noChildrenText="ไม่มีสินค้าในหมวดหมู่นี้" @input="deleteFromBeta(item, 'beta')" :disable-branch-nodes="true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :options="dataBeta"  :normalizer="normalizerBeta" openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
                      </div>
                    </v-col>
                    <v-col cols="2">
                      <span>จำนวนสินค้าแถม *</span>
                      <v-text-field v-model="item.productFreeNum" :rules="Rules.empty" dense type="number" outlined></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="1" align="center" class="mt-6">
                  <v-btn v-if="productList.indexOf(item) !== 0" class="mb-2" icon @click="removeBox(item)"><v-icon>mdi-delete</v-icon></v-btn>
                </v-col>
              </v-row> -->
              <!-- ของใหม่ -->
              <v-row v-for="(item, index) in productList" :key="index">
                <v-col cols="11">
                  <v-row dense>
                    <v-col cols="4">
                      <span>สินค้าที่ {{ index + 1 }}</span><span style="color: red;"> *</span>
                      <div :style="item.productIdFail ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                        <treeselect class="setHeightTreeSelect" :key="componentAlpha" :backspaceRemoves="false" v-model="item.productId" noChildrenText="ไม่มีสินค้าในหมวดหมู่นี้" @input="deleteFromAlpha(item, 'alpha')" :disable-branch-nodes="true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :options="dataProductListInShopByCatagory"  openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
                      </div>
                    </v-col>
                    <v-col cols="2">
                      <span>จำนวนสินค้า</span><span style="color: red;"> *</span>
                      <v-text-field v-model="item.productNum" :rules="Rules.empty" dense type="number" outlined></v-text-field>
                    </v-col>
                    <v-col cols="4">
                      <span>สินค้าแถมที่ {{ index + 1 }}</span><span style="color: red;"> *</span>
                      <div :style="item.productFreeIdFail ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                        <treeselect class="setHeightTreeSelect" :key="componentBeta" :backspaceRemoves="false" v-model="item.productFreeId" noChildrenText="ไม่มีสินค้าในหมวดหมู่นี้" @input="deleteFromBeta(item, 'beta')" :disable-branch-nodes="true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :options="dataProductListInShopByCatagoryFree" openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
                      </div>
                    </v-col>
                    <v-col cols="2">
                      <span>จำนวนสินค้าแถม</span><span style="color: red;"> *</span>
                      <v-text-field v-model="item.productFreeNum" :rules="Rules.empty" dense type="number" outlined></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="1" align="center" class="mt-6">
                  <v-btn v-if="productList.indexOf(item) !== 0" class="mb-2" icon @click="removeBox(item)"><v-icon>mdi-delete</v-icon></v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
        <!-- <pre>
          {{ date11 + time11 }}
          {{ date12 + time12 }}
          {{ date21 + time21 }}
          {{ date22 + time22 }}
        </pre> -->
        <!-- <pre>
          <v-row>
            <v-col cols="6">
              {{ dataAlpha }}
            </v-col>
            <v-col cols="6">
              {{ dataBeta }}
            </v-col>
          </v-row>
        </pre> -->
        <!-- <pre>
          {{ productList }}
          {{setFail}}
        </pre> -->

        <v-row :justify="MobileSize ? 'center' : 'end'" :align="MobileSize ? 'center' : 'end'" dense class="mt-10 mb-4">
          <v-col cols="12" md="12">
            <v-row :justify="MobileSize ? 'center' : 'end'">
              <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="canCel()">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="createCoupon()">ยืนยัน</v-btn>
              <!-- <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="test()">ยืนยัน</v-btn> -->
            </v-row>
          </v-col>
        </v-row>
      </div>
    </v-form>
    <v-dialog v-model="dialogSuccess" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
        </v-img>
        <v-container>
          <v-card-text>
            <v-col>
              <v-row class="pb-2" style="justify-content: center;">
                <span style="font-size: 20px; font-weight: 600;">{{ title }}โปรโมชัน Xแถม Y</span>
              </v-row>
              <v-row style="justify-content: center;">
                <span style="font-size: 20px; font-weight: 600">"สำเร็จ"</span>
              </v-row>
            </v-col>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogFail" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
        </v-img>
        <v-container>
          <v-card-text>
            <v-col>
              <v-row class="d-flex justify-center">
                <span style="font-size: 20px; font-weight: 600;">{{ dialogFailContext }}</span>
              </v-row>
            </v-col>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import { msgErr, statusErr } from '@/enum/GetError'
import { Decode } from '@/services'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
import { TimePicker } from 'ant-design-vue'
import dayjs from 'dayjs'
export default {
  components: {
    Treeselect,
    'a-time-picker': TimePicker
  },
  data () {
    return {
      defaultDate: dayjs('00:00:00', 'HH:mm:ss'),
      firstLoad: true,
      component: 0,
      dialogSuccess: false,
      dialogFail: false,
      dialogFailContext: '',
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'blockquote',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      },
      Rules: {
        amonutCap: [v => !!v || 'กรุณากรอกข้อมูล',
          v => (parseInt(v) <= parseInt(this.amonutUse)) || 'จำนวนคูปองที่ใช้ได้ต่อคนไม่ควรมากกว่าจำนวนคูปองสูงสุด'
        ],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        fourand15: [v => v.length >= 4 || 'กรุณากรอกข้อมูล'],
        datesMustNotBeSame1: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        datesMustNotBeSame2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate2 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        StartDate1MustNotBeSameStartDate2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'ระยะเวลาใช้งานและระยะเวลาเก็บ เวาเชอร์ไม่ควรเป็นวัน/เวลาเดียวกัน'
        ]
      },
      img: [],
      DataImage: [],
      imgID: '',
      useCode: 'no',
      couponCode: '',
      couponName: '',
      couponIMG: '',
      couponDescription: '',
      amonutUse: '',
      amonutCap: '',
      usePremiss: '1',
      usePremiss2: '',
      dialogStartDate1: false,
      dialogEndDate1: false,
      dialogStartDate2: false,
      dialogEndDate2: false,
      date11: '',
      date12: '',
      date21: '',
      date22: '',
      sentStartDate1: '',
      sentEndDate1: '',
      sentStartDate2: '',
      sentEndDate2: '',
      time11: '',
      time12: '',
      time21: '',
      time22: '',
      noEndDateCollect: true,
      noEndDateUse: true,
      seller_shop_id: '',
      dataListPrime: [],
      dataListPrime2: [],
      dataAlpha: [],
      dataBeta: [],
      status: '',
      setFail: true,
      title: 'สร้าง',
      productList: [{ productId: null, productIdFail: true, productNum: 1, productFreeId: null, productFreeIdFail: true, productFreeNum: 1 }],
      productList2: [{
        product_id: 0,
        product_attribute_id: 0,
        minimumBuy: 0,
        free_product_id: 0,
        free_product_attribute_id: 0,
        totalFree: 0
      }],
      componentAlpha: 0,
      componentBeta: 0,
      normalizerAlpha (node) {
        let id
        let childrenKey
        let labelKey

        if (node.type_Alpha === 'category1') {
          id = 'category_id_Alpha'
          childrenKey = 'sub_category_Alpha'
          labelKey = 'category_name_Alpha'
        } else if (node.type_Alpha2 === 'category2') {
          id = 'category_id_Alpha2'
          childrenKey = 'product_list_Alpha2'
          labelKey = 'category_name_Alpha2'
        } else if (node.type_Alpha3 === 'product3') {
          if (node.have_attribute_Alpha3 === 'yes') {
            id = 'product_id_Alpha3'
            childrenKey = 'sub_product_Alpha3'
            labelKey = 'product_name_Alpha3'
          } else {
            id = 'product_id_Alpha3'
            labelKey = 'product_name_Alpha3'
          }
        } else if (node.type_Alpha4 === 'product_attribute4') {
          id = 'product_attribute_id_Alpha4'
          labelKey = 'product_attribute_name_Alpha4'
        }
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey]
        }
      },
      normalizerBeta (node) {
        let id
        let childrenKey
        let labelKey

        if (node.type_Beta === 'category1') {
          id = 'category_id_Beta'
          childrenKey = 'sub_category_Beta'
          labelKey = 'category_name_Beta'
        } else if (node.type_Beta2 === 'category2') {
          id = 'category_id_Beta2'
          childrenKey = 'product_list_Beta2'
          labelKey = 'category_name_Beta2'
        } else if (node.type_Beta3 === 'product3') {
          if (node.have_attribute_Beta3 === 'yes') {
            id = 'product_id_Beta3'
            childrenKey = 'sub_product_Beta3'
            labelKey = 'product_name_Beta3'
          } else {
            id = 'product_id_Beta3'
            labelKey = 'product_name_Beta3'
          }
        } else if (node.type_Beta4 === 'product_attribute4') {
          id = 'product_attribute_id_Beta4'
          labelKey = 'product_attribute_name_Beta4'
        }
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey]
        }
      },
      couponID: '',
      dataProductListInShopByCatagory: [],
      dataProductListInShopByCatagoryFree: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    amonutUse (newVal, oldVal) {
      if (!this.firstLoad && newVal !== oldVal) {
        this.clearAmonutCap()
      } else {
        this.firstLoad = false
      }
    },
    // amonutUse () {
    //   if ((this.status !== 'edit')) {
    //     this.amonutCap = ''
    //   }
    // },
    discountAmountPercent (val) {
      // console.log('ส่วนลดเป็น%', val)
      if ((this.status !== 'edit') || (this.status === 'edit' && val === '')) {
        this.discount_maximum = null
      }
    },
    spendMinimum (val) {
      // console.log('ค่าใช้จ่ายขั้นต่ำ', val, this.status)
      if ((this.status !== 'edit') || (this.status === 'edit' && val === '')) {
        this.discount_maximum = null
        this.discountAmount = ''
        this.discountAmountPercent = ''
      }
    }
  },
  async created () {
    // console.log(this.dataMock, 'mockingBird')
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    this.status = this.$route.query.status
    // ของเก่า
    // await this.getdata()
    // ของใหม่
    await this.getdataNew()
    if (this.status === 'edit') {
      this.couponID = this.$route.query.id
      this.title = 'แก้ไข'
      await this.getDataEdit()
    }
  },
  methods: {
    disabledHoursUse () {
      const now = new Date()
      const selectedDate = new Date(this.date21)
      // ถ้า date21 ไม่ใช่วันเดียวกับวันนี้ ให้ไม่ disable อะไรเลย
      if (
        selectedDate.toDateString() !== now.toDateString()
      ) {
        return []
      }
      // ถ้าเป็นวันปัจจุบัน ให้ disable ชั่วโมงก่อนเวลาปัจจุบัน
      const currentHour = now.getHours()
      return Array.from({ length: currentHour }, (v, k) => k)
    },
    disabledMinutesUse (hour) {
      const now = new Date()
      const selectedDate = new Date(this.date21)
      if (
        selectedDate.toDateString() !== now.toDateString()
      ) {
        return []
      }
      // ถ้าเลือกชั่วโมงตรงกับชั่วโมงปัจจุบัน ให้ disable นาทีที่ผ่านมาแล้ว
      if (hour === now.getHours()) {
        const currentMinute = now.getMinutes()
        return Array.from({ length: currentMinute }, (v, k) => k)
      }
      return []
    },
    disabledHoursRangeUse () {
      if (this.date22 === this.date21 && this.time21) {
        const selectedHour = Number(this.time21.split(':')[0])
        return Array.from({ length: selectedHour }, (_, i) => i)
      }
      return []
    },
    disabledMinutesRangeUse (hour) {
      // ➕ ตรวจสอบก่อนว่าเลือกชั่วโมงหรือยัง
      if (!this.time22) return Array.from({ length: 60 }, (_, i) => i) // ปิดทั้งหมดถ้ายังไม่ได้เลือกอะไร
      const selectedHour = Number(this.time22.split(':')[0])
      if (isNaN(selectedHour) || hour !== selectedHour) return Array.from({ length: 60 }, (_, i) => i) // ปิดถ้าไม่ตรง

      if (this.date22 === this.date21 && this.time21) {
        const [h, m] = this.time21.split(':').map(Number)
        if (hour === h) {
          return Array.from({ length: m }, (_, i) => i)
        }
      }
      return []
    },
    disabledSecondsRangeUse (hour, minute) {
      // ➕ ตรวจสอบก่อนว่าเลือกชั่วโมงและนาทีหรือยัง
      if (!this.time22) return Array.from({ length: 60 }, (_, i) => i)
      const [selectedHour, selectedMinute] = this.time22.split(':').map(Number)
      if (
        isNaN(selectedHour) || isNaN(selectedMinute) ||
        hour !== selectedHour || minute !== selectedMinute
      ) {
        return Array.from({ length: 60 }, (_, i) => i)
      }

      if (this.date22 === this.date21 && this.time21) {
        const [h, m, s] = this.time21.split(':').map(Number)
        if (hour === h && minute === m) {
          return Array.from({ length: s + 1 }, (_, i) => i)
        }
      }
      return []
    },
    disabledHours () {
      if (this.date12 === this.date11 && this.time11) {
        const selectedHour = Number(this.time11.split(':')[0])
        return Array.from({ length: selectedHour }, (_, i) => i)
      }
      return []
    },
    disabledMinutes (hour) {
      if (!this.time12) return Array.from({ length: 60 }, (_, i) => i) // ปิดทั้งหมดถ้ายังไม่ได้เลือกอะไร
      const selectedHour = Number(this.time12.split(':')[0])
      if (isNaN(selectedHour) || hour !== selectedHour) return Array.from({ length: 60 }, (_, i) => i) // ปิดถ้าไม่ตรง

      if (this.date12 === this.date11 && this.time11) {
        const [h, m] = this.time21.split(':').map(Number)
        if (hour === h) {
          return Array.from({ length: m }, (_, i) => i)
        }
      }
      return []
    },
    disabledSeconds (hour, minute) {
      if (!this.time12) return Array.from({ length: 60 }, (_, i) => i)
      const [selectedHour, selectedMinute] = this.time12.split(':').map(Number)
      if (
        isNaN(selectedHour) || isNaN(selectedMinute) ||
        hour !== selectedHour || minute !== selectedMinute
      ) {
        return Array.from({ length: 60 }, (_, i) => i)
      }

      if (this.date12 === this.date11 && this.time11) {
        const [h, m, s] = this.time11.split(':').map(Number)
        if (hour === h && minute === m) {
          return Array.from({ length: s + 1 }, (_, i) => i)
        }
      }
      return []
    },
    clearAmonutCap () {
      this.amonutCap = ''
    },
    forceRerender () {
      this.component += 1
      // console.log(this.component)
    },
    canCel () {
      if (this.MobileSize) {
        this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageCoupon' }).catch(() => {})
      }
    },
    forceRerenderAlpha () {
      this.componentAlpha += 1
    },
    forceRerenderBeta () {
      this.componentBeta += 1
    },
    deleteFromAlpha (item, typeSelect) {
      // console.log(item, typeSelect, 'aaaaaalpha')
      item.productIdFail = true
      if (typeSelect === 'alpha') {
        this.forceRerenderAlpha()
        this.dataAlpha.forEach(q => {
          q.sub_category_Alpha.forEach(w => {
            w.product_list_Alpha2.forEach(e => {
              if (e.have_attribute_Alpha3 === 'no') {
                if (this.productList.some(m => m.productId === e.product_id_Alpha3)) {
                  e.isDisabled = false
                } else {
                  e.isDisabled = false
                }
              } else {
                e.sub_product_Alpha3.forEach(r => {
                  if (this.productList.some(n => n.productId === r.product_attribute_id_Alpha4)) {
                    r.isDisabled = false
                  } else {
                    r.isDisabled = false
                  }
                })
              }
            })
          })
        })
      }
    },
    deleteFromBeta (item, typeSelect) {
      // console.log(item, typeSelect, 'bbbbbbbeTa')
      item.productFreeIdFail = true
      if (typeSelect === 'beta') {
        this.forceRerenderBeta()
        this.dataBeta.forEach(q => {
          // console.log(q.category_id_Beta)
          q.sub_category_Beta.forEach(w => {
            w.product_list_Beta2.forEach(e => {
              if (e.have_attribute_Beta3 === 'no') {
                if (this.productList.some(m => m.productFreeId === e.product_id_Beta3)) {
                  e.isDisabled = false
                } else {
                  e.isDisabled = false
                }
              } else {
                e.sub_product_Beta3.forEach(r => {
                  if (this.productList.some(n => n.productFreeId === r.product_attribute_id_Beta4)) {
                    r.isDisabled = false
                  } else {
                    r.isDisabled = false
                  }
                })
              }
            })
          })
        })
      }
    },
    plusBox () {
      this.productList.push({ productId: null, productIdFail: true, productNum: 1, productFreeId: null, productFreeIdFail: true, productFreeNum: 1 })
      this.productList2.push({
        product_id: 0,
        product_attribute_id: 0,
        minimumBuy: 0,
        free_product_id: 0,
        free_product_attribute_id: 0,
        totalFree: 0
      })
    },
    removeBox (item) {
      // console.log(this.productList.indexOf(item))
      this.productList.splice(this.productList.indexOf(item), 1)
    },
    onReady (editor) {
      editor.execute('heading', { value: 'heading2' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    allowedSeconds1 (v) {
      const getSecoinds = this.time11.split(':')
      const getSecoinds2 = this.time12.split(':')
      if (parseInt(getSecoinds[0]) === parseInt(getSecoinds2[0]) && parseInt(getSecoinds[1]) === parseInt(getSecoinds2[1])) {
        return v > parseInt(getSecoinds[2])
      } else {
        return true
      }
    },
    allowedSeconds2 (v) {
      const getSecoinds = this.time21.split(':')
      const getSecoinds2 = this.time22.split(':')
      if (parseInt(getSecoinds[0]) === parseInt(getSecoinds2[0]) && parseInt(getSecoinds[1]) === parseInt(getSecoinds2[1])) {
        return v > parseInt(getSecoinds[2])
      } else {
        return true
      }
    },
    async getdata () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionscategoryShopList', data)
      const res = await this.$store.state.ModuleManageCoupon.stateCategoryShopList
      var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      var filteredData = []
      if (dataDetail.is_JV === 'yes') {
        filteredData = res.data.filter(item => item.hierachy !== '1_259')
      } else {
        filteredData = res.data
      }
      this.dataListPrime = filteredData
      // category_id
      // :
      // 2
      // category_name
      // :
      // "เวชภัณฑ์"
      // hierachy
      // :
      // "1_2"
      // seller_shop_id
      // :
      // 19
      // sub_category
      // :
      // [{category_id: 3, category_name: "ยาสามัญประจำบ้าน", hierachy: "1_2_3", seller_shop_id: 19,…},…]
      // type
      // :
      // "category"
      // this.dataAlpha = this.dataListPrime.map(e => {
      //   return {
      //     category_id_Alpha: e.category_id,
      //     category_name_Alpha: e.category_name,
      //     hierachy_Alpha: e.hierachy,
      //     seller_shop_id_Alpha: e.seller_shop_id,
      //     type_Alpha: e.type + '1',
      //     sub_category_Alpha: e.sub_category.map(r => {
      //       return {
      //         category_id_Alpha2: r.category_id,
      //         category_name_Alpha2: r.category_name,
      //         hierachy_Alpha2: r.hierachy,
      //         seller_shop_id_Alpha2: r.seller_shop_id,
      //         type_Alpha2: r.type + '2',
      //         product_list_Alpha2: r.product_list.map(t => {
      //           return {
      //             product_id_Alpha3: t.product_id,
      //             product_sku_Alpha3: t.product_sku,
      //             product_name_Alpha3: t.product_name,
      //             have_attribute_Alpha3: t.have_attribute,
      //             category_id_Alpha3: t.category_id,
      //             type_Alpha3: t.type + '3',
      //             sub_product_Alpha3: t.have_attribute === 'no' ? '' : t.sub_product.map(y => {
      //               return {
      //                 product_id_Alpha4: y.product_id,
      //                 product_sku_Alpha4: y.product_sku,
      //                 product_attribute_id_Alpha4: y.product_attribute_id,
      //                 product_attribute_sku_Alpha4: y.product_attribute_sku,
      //                 attribute_priority_1_Alpha4: y.attribute_priority_1,
      //                 attribute_priority_2_Alpha4: y.attribute_priority_2,
      //                 type_Alpha4: y.type + '4',
      //                 product_attribute_name_Alpha4: y.product_attribute_name,
      //                 category_id_Alpha4: y.category_id
      //               }
      //             })
      //           }
      //         })
      //       }
      //     })
      //   }
      // })
      // this.dataBeta = this.dataListPrime.map(e => {
      //   return {
      //     category_id_Beta: e.category_id,
      //     category_name_Beta: e.category_name,
      //     hierachy_Beta: e.hierachy,
      //     seller_shop_id_Beta: e.seller_shop_id,
      //     type_Beta: e.type + '1',
      //     sub_category_Beta: e.sub_category.map(r => {
      //       return {
      //         category_id_Beta2: r.category_id,
      //         category_name_Beta2: r.category_name,
      //         hierachy_Beta2: r.hierachy,
      //         seller_shop_id_Beta2: r.seller_shop_id,
      //         type_Beta2: r.type + '2',
      //         product_list_Beta2: r.product_list.map(t => {
      //           return {
      //             product_id_Beta3: t.product_id,
      //             product_sku_Beta3: t.product_sku,
      //             product_name_Beta3: t.product_name,
      //             have_attribute_Beta3: t.have_attribute,
      //             category_id_Beta3: t.category_id,
      //             type_Beta3: t.type + '3',
      //             sub_product_Beta3: t.have_attribute === 'no' ? '' : t.sub_product.map(y => {
      //               return {
      //                 product_id_Beta4: y.product_id,
      //                 product_sku_Beta4: y.product_sku,
      //                 product_attribute_id_Beta4: y.product_attribute_id,
      //                 product_attribute_sku_Beta4: y.product_attribute_sku,
      //                 attribute_priority_1_Beta4: y.attribute_priority_1,
      //                 attribute_priority_2_Beta4: y.attribute_priority_2,
      //                 type_Beta4: y.type + '4',
      //                 product_attribute_name_Beta4: y.product_attribute_name,
      //                 category_id_Beta4: y.category_id
      //               }
      //             })
      //           }
      //         })
      //       }
      //     })
      //   }
      // })
      // this.dataBeta = this.dataListPrime.map(e => {
      //   return {
      //     category_id_Beta: e.category_id,
      //     category_name: e.category_name,
      //     hierachy: e.hierachy,
      //     seller_shop_id: e.seller_shop_id,
      //     sub_category: e.sub_category,
      //     type: e.type + 'Beta'
      //   }
      // })
    },
    async getdataNew () {
      var sittingCategory = {}
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsListProductInShop', data)
      const response = await this.$store.state.ModuleManageCoupon.stateListProductInShop
      // var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      var filteredData = []
      // กรณีเก่งตัดหมวดหมู่ service และบริการ ให้แล้ว
      filteredData = response.data[0]
      // กรณีเก่งไม่ตัดหมวดหมู่ service และบริการ ให้แล้ว
      // if (dataDetail.is_JV === 'yes') {
      //   filteredData = response.data[0]
      // } else {
      //   filteredData = response.data[0]
      // }
      this.dataListPrime = [filteredData]
      if (filteredData.product_list.length !== 0) {
        sittingCategory = {
          id: 0,
          label: 'หมวดหมู่ทั้งหมด',
          children: [filteredData]
        }
      } else {
        sittingCategory = {
          id: 0,
          label: 'หมวดหมู่ทั้งหมด',
          children: filteredData.sub_category
        }
      }
      this.dataProductListInShopByCatagory = await this.transformDataToTree(sittingCategory.children)
      this.dataProductListInShopByCatagoryFree = await this.transformDataToTree(sittingCategory.children)
    },
    transformDataToTree (data) {
      return data.map((item) => {
        const transformed = {
          id: item.category_id,
          label: item.category_name,
          children: []
        }

        // เพิ่ม sub_category
        if (item.sub_category && item.sub_category.length > 0) {
          transformed.children = this.transformDataToTree(item.sub_category)
        }

        // เพิ่ม product_list
        if (item.product_list && item.product_list.length > 0) {
          const products = item.product_list

          const productItems = products.map((product) => {
            const productNode = {
              id: product.product_id,
              label: product.product_name
              // children: []
            }

            // เพิ่ม attribute ถ้ามี
            if (product.attributes && product.attributes.length > 0) {
              productNode.children = product.attributes.map((attr) => ({
                id: attr.product_attribute_id,
                label: attr.product_attribute_name
              }))
            }

            return productNode
          })

          if (productItems.length > 0) {
            transformed.children.push(...productItems)
          }
        }

        return transformed
      })
    },
    async getDataEdit () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.seller_shop_id,
        coupon_id: this.couponID
      }
      // var nowday = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString()
      await this.$store.dispatch('actionsgetDetailCoupon', data)
      const res = await this.$store.state.ModuleManageCoupon.stategetDetailCoupon
      // console.log(res)
      if (res.result === 'Success') {
        this.dataListPrime2 = res.data[0]
        this.couponName = this.dataListPrime2.coupon_name
        // this.couponCode = this.dataListPrime2.coupon_code !== null ? this.dataListPrime2.coupon_code : ''
        this.couponDescription = this.dataListPrime2.coupon_description === null ? '' : this.dataListPrime2.coupon_description
        this.amonutUse = this.dataListPrime2.quota
        this.amonutCap = this.dataListPrime2.user_cap
        this.date11 = this.dataListPrime2.collect_startdate.slice(0, 10)
        this.time11 = this.dataListPrime2.collect_startdate.slice(11, 19)
        this.noEndDateCollect = this.dataListPrime2.collect_enddate === null
        this.date12 = this.dataListPrime2.collect_enddate === null ? '' : this.dataListPrime2.collect_enddate.slice(0, 10)
        this.time12 = this.dataListPrime2.collect_enddate === null ? '' : this.dataListPrime2.collect_enddate.slice(11, 19)
        this.date21 = this.dataListPrime2.use_startdate.slice(0, 10)
        this.time21 = this.dataListPrime2.use_startdate.slice(11, 19)
        this.noEndDateUse = this.dataListPrime2.use_enddate === null
        this.date22 = this.dataListPrime2.use_enddate === null ? '' : this.dataListPrime2.use_enddate.slice(0, 10)
        this.time22 = this.dataListPrime2.use_enddate === null ? '' : this.dataListPrime2.use_enddate.slice(11, 19)
        // this.disableddate11 = nowday > this.dataListPrime2.collect_startdate
        // this.disableddate12 = nowday > this.dataListPrime2.collect_enddate
        // this.disableddate21 = nowday > this.dataListPrime2.use_startdate
        // this.disableddate22 = nowday > this.dataListPrime2.use_enddate
        this.setValueDate(this.date11, 'date11')
        this.setValueDate(this.date12, 'date12')
        this.setValueDate(this.date21, 'date21')
        this.setValueDate(this.date22, 'date22')
        if (this.dataListPrime2.coupon_image !== null) {
          this.img.push({
            name: this.dataListPrime2.coupon_name,
            path: this.dataListPrime2.coupon_image,
            new: true
          })
        }
        this.couponIMG = this.dataListPrime2.coupon_image === null ? '' : this.dataListPrime2.coupon_image
        // ของเก่า
        // var b = JSON.parse(this.dataListPrime2.product_list)
        // var tempLocal = 0
        // for (let i = 0; i < b.length; i++) {
        //   var proof1 = false
        //   var proof2 = false
        //   // console.log(b[i])
        //   if (b[i].product_attribute_id === null || b[i].product_attribute_id === -1 || b[i].product_attribute_id === '-1') {
        //     this.dataAlpha.forEach(q => {
        //       q.sub_category_Alpha.forEach(w => {
        //         w.product_list_Alpha2.forEach(e => {
        //           if (e.product_id_Alpha3 === b[i].product_id) {
        //             // console.log(b[i].product_id + ' / ' + i, 'test011')
        //             if (tempLocal !== 0) {
        //               this.plusBox()
        //             }
        //             this.productList[tempLocal].productId = b[i].product_id
        //             this.productList[tempLocal].productNum = b[i].minimumBuy
        //             proof1 = true
        //           }
        //         })
        //       })
        //     })
        //   } else {
        //     this.dataAlpha.forEach(q => {
        //       q.sub_category_Alpha.forEach(w => {
        //         w.product_list_Alpha2.forEach(e => {
        //           if (e.have_attribute_Alpha3 === 'yes') {
        //             e.sub_product_Alpha3.forEach(r => {
        //               if (r.product_attribute_id_Alpha4 === b[i].product_attribute_id) {
        //                 // console.log(b[i].product_attribute_id + ' / ' + i, 'test012')
        //                 if (tempLocal !== 0) {
        //                   this.plusBox()
        //                 }
        //                 this.productList[tempLocal].productId = b[i].product_attribute_id
        //                 this.productList[tempLocal].productNum = b[i].minimumBuy
        //                 proof1 = true
        //               }
        //             })
        //           }
        //         })
        //       })
        //     })
        //   }
        //   if (b[i].free_product_attribute_id === null || b[i].free_product_attribute_id === -1 || b[i].free_product_attribute_id === '-1') {
        //     this.dataBeta.forEach(q => {
        //       q.sub_category_Beta.forEach(w => {
        //         w.product_list_Beta2.forEach(e => {
        //           if (e.product_id_Beta3 === b[i].free_product_id) {
        //             // console.log(e.product_id_Beta3, 'test013')
        //             this.productList[tempLocal].productFreeId = b[i].free_product_id
        //             this.productList[tempLocal].productFreeNum = b[i].totalFree
        //             proof2 = true
        //           }
        //         })
        //       })
        //     })
        //   } else {
        //     this.dataBeta.forEach(q => {
        //       q.sub_category_Beta.forEach(w => {
        //         w.product_list_Beta2.forEach(e => {
        //           if (e.have_attribute_Beta3 === 'yes') {
        //             e.sub_product_Beta3.forEach(r => {
        //               if (r.product_attribute_id_Beta4 === b[i].free_product_attribute_id) {
        //                 // console.log(r.product_attribute_id_Beta4, 'test014')
        //                 this.productList[tempLocal].productFreeId = b[i].free_product_attribute_id
        //                 this.productList[tempLocal].productFreeNum = b[i].totalFree
        //                 proof2 = true
        //               }
        //             })
        //           }
        //         })
        //       })
        //     })
        //   }
        //   if (proof1 || proof2) {
        //     tempLocal++
        //   }
        // }
        // ของใหม่
        var productData = JSON.parse(this.dataListPrime2.product_list)
        var tempLocal = 0
        for (let index = 0; index < productData.length; index++) {
          var product = false
          var productFree = false
          // สินค้าตั้งต้น
          if (productData[index].product_attribute_id === null || productData[index].product_attribute_id === -1 || productData[index].product_attribute_id === '-1') {
            if (tempLocal !== 0) {
              this.plusBox()
            }
            this.productList[tempLocal].productId = productData[index].product_id
            this.productList[tempLocal].productNum = productData[index].minimumBuy
            product = true
          } else {
            if (tempLocal !== 0) {
              this.plusBox()
            }
            this.productList[tempLocal].productId = productData[index].product_attribute_id
            this.productList[tempLocal].productNum = productData[index].minimumBuy
            product = true
          }
          // สินค้าแถม
          if (productData[index].free_product_attribute_id === null || productData[index].free_product_attribute_id === -1 || productData[index].free_product_attribute_id === '-1') {
            this.productList[tempLocal].productFreeId = productData[index].free_product_id
            this.productList[tempLocal].productFreeNum = productData[index].totalFree
            productFree = true
          } else {
            this.productList[tempLocal].productFreeId = productData[index].free_product_attribute_id
            this.productList[tempLocal].productFreeNum = productData[index].totalFree
            productFree = true
          }
          if (product || productFree) {
            tempLocal++
          }
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 5000 })
        setTimeout(() => {
          if (this.MobileSize) {
            this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/manageCoupon' }).catch(() => {})
          }
        }, 3000)
      }
    },
    // async test () {
    //   var data = {
    //     shop_id: this.seller_shop_id,
    //     coupon_status: null
    //   }
    //   await this.$store.dispatch('actionsgetCoupon', data)
    //   const res2 = await this.$store.state.ModuleManageCoupon.stategetCoupon
    //   console.log(res2)
    // },
    seekingNull () {
      this.productList.forEach(e => {
        if (e.productId === null || e.productId === undefined) {
          e.productIdFail = false
          this.setFail = false
        }
        if (e.productFreeId === null || e.productFreeId === undefined) {
          e.productFreeIdFail = false
          this.setFail = false
        }
      })
      if (this.productList.every(e => ((e.productFreeId !== null && e.productFreeId !== undefined) && (e.productId !== null && e.productId !== undefined)))) {
        this.setFail = true
      }
    },
    // ฟังก์ชันค้นหาผลิตภัณฑ์ในหมวดหมู่
    searchProductInCategories (productId, categoryList) {
      for (var category of categoryList) {
        // ค้นหาผลิตภัณฑ์ใน product_list
        var productResult = this.searchProductInList(productId, category)
        if (productResult) return productResult

        // ค้นหาผลิตภัณฑ์ใน sub_category ถ้ามี
        if (category.sub_category && category.sub_category.length > 0) {
          var subCategoryResult = this.searchProductInCategories(productId, category.sub_category)
          if (subCategoryResult) return subCategoryResult
        }
      }
      return null // ถ้าไม่เจอ
    },

    // ค้นหาผลิตภัณฑ์ใน product_list ของ category
    searchProductInList (productId, category) {
      for (var product of category.product_list) {
        if (product.product_id === productId) {
          // ถ้าเจอใน product_list
          return {
            product_id: product.product_id,
            product_attribute_id: '-1'
          }
        }
        // ถ้าไม่เจอใน product_list แต่มี attributes ให้ค้นหาใน attributes
        if (product.attributes && product.attributes.length > 0) {
          var attributeResult = this.searchInAttributes(productId, product)
          if (attributeResult) return attributeResult
        }
      }
      return null // ถ้าไม่เจอ
    },
    // ค้นหาผลิตภัณฑ์ใน attributes ของผลิตภัณฑ์
    searchInAttributes (productId, product) {
      for (var attribute of product.attributes) {
        if (attribute.product_attribute_id === productId) {
          return {
            product_id: product.product_id,
            product_attribute_id: productId
          }
        }
      }
      return null // ถ้าไม่เจอใน attributes
    },
    // ฟังก์ชันค้นหาผลิตภัณฑ์ในหมวดหมู่ Free Product
    searchProductInCategoriesFreeProduct (productId, categoryList) {
      for (var category of categoryList) {
        // ค้นหาผลิตภัณฑ์ใน product_list
        var productResult = this.searchProductInListFreeProduct(productId, category)
        if (productResult) return productResult

        // ค้นหาผลิตภัณฑ์ใน sub_category ถ้ามี
        if (category.sub_category && category.sub_category.length > 0) {
          var subCategoryResult = this.searchProductInCategoriesFreeProduct(productId, category.sub_category)
          if (subCategoryResult) return subCategoryResult
        }
      }
      return null // ถ้าไม่เจอ
    },

    // ค้นหาผลิตภัณฑ์ใน product_list ของ category Free Product
    searchProductInListFreeProduct (productId, category) {
      for (var product of category.product_list) {
        if (product.product_id === productId) {
          // ถ้าเจอใน product_list
          return {
            free_product_id: product.product_id,
            free_product_attribute_id: '-1'
          }
        }
        // ถ้าไม่เจอใน product_list แต่มี attributes ให้ค้นหาใน attributes
        if (product.attributes && product.attributes.length > 0) {
          var attributeResult = this.searchInAttributesFreeProduct(productId, product)
          if (attributeResult) return attributeResult
        }
      }
      return null // ถ้าไม่เจอ
    },
    // ค้นหาผลิตภัณฑ์ใน attributes ของผลิตภัณฑ์ Free Product
    searchInAttributesFreeProduct (productId, product) {
      for (var attribute of product.attributes) {
        if (attribute.product_attribute_id === productId) {
          return {
            free_product_id: product.product_id,
            free_product_attribute_id: productId
          }
        }
      }
      return null // ถ้าไม่เจอใน attributes
    },
    async createCoupon () {
      // this.productList2
      this.$store.commit('openLoader')
      this.seekingNull()
      if (this.$refs.FormManageXY.validate(true) && this.setFail) {
        // ของเก่า
        // this.productList.forEach((e, index) => {
        //   // e.productId
        //   this.dataListPrime.forEach(r => {
        //     r.sub_category.forEach(t => {
        //       t.product_list.forEach(y => {
        //         if (y.have_attribute === 'no') {
        //           if (e.productId === y.product_id) {
        //             // console.log(y.product_id)
        //             this.productList2[index].product_id = y.product_id
        //             this.productList2[index].product_attribute_id = '-1'
        //           }
        //           if (e.productFreeId === y.product_id) {
        //             // console.log(y.product_id)
        //             this.productList2[index].free_product_id = y.product_id
        //             this.productList2[index].free_product_attribute_id = '-1'
        //           }
        //           this.productList2[index].minimumBuy = e.productNum
        //           this.productList2[index].totalFree = e.productFreeNum
        //         } else {
        //           y.sub_product.forEach(u => {
        //             if (e.productId === u.product_attribute_id) {
        //               // console.log(u.product_attribute_id)
        //               this.productList2[index].product_id = u.product_id
        //               this.productList2[index].product_attribute_id = u.product_attribute_id
        //             }
        //             if (e.productFreeId === u.product_attribute_id) {
        //               // console.log(u.product_attribute_id)
        //               this.productList2[index].free_product_id = u.product_id
        //               this.productList2[index].free_product_attribute_id = u.product_attribute_id
        //             }
        //             this.productList2[index].minimumBuy = e.productNum
        //             this.productList2[index].totalFree = e.productFreeNum
        //           })
        //         }
        //       })
        //     })
        //   })
        // })
        // console.log(this.date12 + ' ' + this.time12, '8902220')
        // ของใหม่
        // console.log(this.productList)
        this.productList2 = await this.productList.map(product => {
          const productResult = this.searchProductInCategories(product.productId, this.dataListPrime)
          const freeProductResult = this.searchProductInCategoriesFreeProduct(product.productFreeId, this.dataListPrime)

          return {
            product_id: productResult.product_id,
            product_attribute_id: productResult.product_attribute_id,
            free_product_id: freeProductResult.free_product_id,
            free_product_attribute_id: freeProductResult.free_product_attribute_id,
            minimumBuy: product.productNum,
            totalFree: product.productFreeNum
          }
        })
        // console.log('this.productList2 ====>', this.productList2)
        var myCateory = []
        var mySubCateory = []
        var formData = new FormData()
        formData.append('coupon_image', this.DataImage)
        formData.append('coupon_image_edit', this.couponIMG)
        formData.append('coupon_name', this.couponName)
        // formData.append('coupon_code', this.couponCode)
        formData.append('coupon_description', this.couponDescription === null || this.couponDescription === 'null' ? '' : this.couponDescription)
        formData.append('collect_startdate', this.date11 + ' ' + this.time11)
        formData.append('collect_enddate', this.noEndDateCollect === true ? '' : this.date12 + ' ' + this.time12)
        formData.append('use_startdate', this.date21 + ' ' + this.time21)
        formData.append('use_enddate', this.noEndDateUse === true ? '' : this.date22 + ' ' + this.time22)
        formData.append('coupon_type', 'free_product')
        formData.append('quota', this.amonutUse)
        formData.append('user_cap', this.amonutCap)
        formData.append('product_list', JSON.stringify(this.productList2))
        formData.append('seller_shop_id', this.seller_shop_id)
        if (this.status === 'edit') {
          formData.append('id', this.couponID)
        }
        formData.append('cateory', JSON.stringify(myCateory))
        formData.append('sub_category', JSON.stringify(mySubCateory))
        // console.log(formData, 'formData')
        if (this.status === 'edit') {
          await this.$store.dispatch('actionsEditCoupon', formData)
          var res = await this.$store.state.ModuleManageCoupon.stateEditCoupon
          if (res.result === 'Success') {
            this.$store.commit('closeLoader')
            this.dialogSuccess = true
            setTimeout(() => {
              this.dialogSuccess = false
              if (this.MobileSize) {
                this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
              } else {
                this.$router.push({ path: '/manageCoupon' }).catch(() => {})
              }
            }, 2000)
          } else if (res.result === 'SKU duplicate') {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 5000 })
          } else if (res.message === 'collectStartDate not over useStartDate') {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: 'ควรกำหนดช่วงเวลาในการจัดเก็บเวาเชอร์ให้เริ่มต้นก่อนช่วงเวลาในการใช้งานเวาเชอร์', showConfirmButton: false, timer: 5000 })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: 'กรุณาลองอีกครั้งในภายหลัง', showConfirmButton: false, timer: 3500 })
          }
        } else {
          await this.$store.dispatch('actionscreateCoupon', formData)
          res = await this.$store.state.ModuleManageCoupon.statecreateCoupon
          if (res.result === 'Success') {
            this.$store.commit('closeLoader')
            this.dialogSuccess = true
            setTimeout(() => {
              this.dialogSuccess = false
              if (this.MobileSize) {
                this.$router.push({ path: '/manageCouponMobile' }).catch(() => {})
              } else {
                this.$router.push({ path: '/manageCoupon' }).catch(() => {})
              }
            }, 2000)
          } else if (res.result === 'SKU duplicate') {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 5000 })
          } else if (res.message === 'collectStartDate not over useStartDate') {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: 'ควรกำหนดช่วงเวลาในการจัดเก็บเวาเชอร์ให้เริ่มต้นก่อนช่วงเวลาในการใช้งานเวาเชอร์', showConfirmButton: false, timer: 5000 })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: 'กรุณาลองอีกครั้งในภายหลัง', showConfirmButton: false, timer: 3500 })
          }
        }
      } else {
        this.$store.commit('closeLoader')
        if (this.couponName === '' || this.couponName === null) {
          this.openDialogFail('couponName')
        } else if (this.amonutUse === '' || this.amonutUse === null) {
          this.openDialogFail('amonutUse')
        } else if (this.date11 === '' && this.time11 === '') {
          this.openDialogFail('sentStartDate1')
        } else if (this.noEndDateCollect === false && (this.date12 === '' && this.time12 === '')) {
          this.openDialogFail('sentEndDate1')
        } else if (this.date21 === '' && this.time21 === '') {
          this.openDialogFail('sentStartDate2')
        } else if (this.noEndDateUse === false && (this.date22 === '' && this.time22 === '')) {
          this.openDialogFail('sentEndDate2')
        } else if (!this.setFail) {
          this.openDialogFail('productList')
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 1500 })
        }
      }
      // console.log(res)
    },
    openDialogFail (type) {
      if (type === 'couponName') {
        this.dialogFailContext = 'กรุณากรอกชื่อโปรโมชันให้ครบถ้วนและถูกต้อง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'amonutUse') {
        this.dialogFailContext = 'กรุณากรอกจำนวนคูปองสูงสุด'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentStartDate1') {
        this.dialogFailContext = 'กรุณากรอกวันที่เริ่มเก็บคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentEndDate1') {
        this.dialogFailContext = 'กรุณากรอกวันที่สิ้นสุดเก็บคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentStartDate2') {
        this.dialogFailContext = 'กรุณากรอกวันที่เริ่มใช้งานคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentEndDate2') {
        this.dialogFailContext = 'กรุณากรอกวันที่สิ้นสุดใช้งานคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'spendMinimum') {
        this.dialogFailContext = 'กรุณากรอกค่าใช้จ่ายขั้นต่ำ'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'productList') {
        this.dialogFailContext = 'กรุณากรอกสินค้าที่เข้าร่วม'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      }
    },
    setValueDate (dateDay, proof) {
      if (!dateDay) return null
      const [year, month, day] = dateDay.split('-')
      const yearChange = parseInt(year) + 543
      if (proof === 'date11') {
        this.sentStartDate1 = `${day}/${month}/${yearChange}` + ' ' + this.time11
      } else if (proof === 'date12') {
        this.sentEndDate1 = `${day}/${month}/${yearChange}` + ' ' + this.time12
      } else if (proof === 'date21') {
        this.sentStartDate2 = `${day}/${month}/${yearChange}` + ' ' + this.time21
      } else if (proof === 'date22') {
        this.sentEndDate2 = `${day}/${month}/${yearChange}` + ' ' + this.time22
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return (`${day}/${month}/${year}`)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    uploadImage (e) {
      // const element = e.target.files[0]
      const element = this.DataImage
      // console.log(element, 'DataImage')
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
        // ขนาดต้องไม่เกิน 2
        const imageSize = element.size / 1024 / 1024
        // ขนาดไม่เกิน 1480 * 620
        var url = URL.createObjectURL(element)
        var img = new Image()
        img.src = url
        img.onload = () => {
          if (imageSize < 2 && img.width <= 1480 && img.height <= 620) {
            // console.log('second')
            this.img.push({
              name: element.name,
              path: url,
              new: true
            })
          } else {
            // console.log('third')
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB หรือ มีขนาดน้อยกว่า 1480 * 620 px', showConfirmButton: false, timer: 1500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
      }
    },
    removeImage () {
      this.img = []
      this.imgID = ''
      this.couponIMG = '-1'
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    }

  }
}
</script>

<style scoped>
.setHeightTreeSelect >>> .vue-treeselect__control {
  height: 40px;
  /* border: 1px solid #636363; */
  /* transition: border 0.3s cubic-bezier(0.25, 0.8, 0.5, 1); */
}
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.title1 {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.subTitle1 {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.detail1 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.rule {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #C4C4C4;
}
</style>
<style>
.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}

.ant-table-thead>tr>th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}

.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  bottom: 40px;
}

.ant-time-picker-panel {
  width: 243px;
}

@media (max-width: 767px) {
  .ant-time-picker-panel {
    width: 243px;
  }
}
.ant-time-picker-panel-select:nth-child(1),
.ant-time-picker-panel-select:nth-child(2),
.ant-time-picker-panel-select:nth-child(3) {
  width: 33.33% !important;
}
.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
  padding: 0 0 0 0px;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}
</style>
