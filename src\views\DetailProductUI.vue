<template>
  <div>
    <DetailProductUI />
    <!-- <v-row dense class="mb-3">
      <v-col cols="12" class="overflow-auto">
        <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
          <template v-slot:divider>
            <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
          </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled" @click.prevent="gotoBannerPage(item)">
              <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }">
                {{ item.category_name }}
              </span>
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
      </v-col>
    </v-row> -->
    <!-- <v-container :class="MobileSize ? 'px-1' : ''"> -->
      <!-- <DetailProductUI /> -->
      <!-- <ProductSameShop :propsData='GetAllProduct.newArrivals' header='สินค้าจากร้านเดียวกัน' :check='status' class="mt-2"/>
      <ProductSameShop :propsData='GetAllProduct.bestSeller' header='สินค้าที่คล้ายกัน' :check='status'/>
      <ProductSameShop :propsData='GetAllProduct.recommend' header='สินค้าแนะนำสำหรับคุณ' :check='status'/> -->
    <!-- </v-container> -->
  </div>
</template>
<script>
import { Decode } from '@/services'
const newArrivals = []
for (let i = 0; i < 50; i++) {
  newArrivals.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
const bestSeller = []
for (let i = 0; i < 50; i++) {
  bestSeller.push({
    product_id: i,
    name: `Data Title bestSeller ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
const recommend = []
for (let i = 0; i < 50; i++) {
  recommend.push({
    product_id: i,
    name: `Data Title recommend ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    DetailProductUI: () => import('@/components/DetailProduct/DetailProductUI')
    // ProductSameShop: () => import('@/components/DetailProduct/ProductSameShop')
  },
  data () {
    return {
      itemsJSONld: [],
      pathShop: '',
      GetAllProduct: {
        newArrivals,
        bestSeller,
        recommend
      },
      productDetail: [],
      items: [
        {
          category_name: 'หน้าแรก',
          id: 0,
          disabled: false,
          color: '#636363',
          href: '/'
        }
      ],
      status: false,
      path: process.env.VUE_APP_DOMAIN
    }
  },
  async created () {
    if (this.MobileSize) {
      this.$EventBus.$emit('GetLink')
    }
    this.$EventBus.$emit('getPath')
    this.$EventBus.$emit('changeAppBarPanit')
    // this.$EventBus.$on('getBreadcrumbs', this.getProductDetail())
    if (localStorage.getItem('roleUser') !== null) {
      if (JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order' || JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order_no_JV') {
        this.getPath()
      }
    }
    this.itemsJSONld = [
      { '@type': 'ListItem', position: 1, item: { '@type': 'Thing', name: 'หน้าแรก' } }
    ]
    await this.getProductDetail()
    this.jsonld()
  },
  watch: {
    async $route (to, from) {
      // console.log('path', to, from)
      var getIDToParams = to.params.data.split('-')
      var getIDFromParan = from.params.data.split('-')
      // console.log(getIDToParams, getIDFromParan)
      var IDOfTo = parseInt(getIDToParams[getIDToParams.length - 1])
      var IDOfFrom = parseInt(getIDFromParan[getIDFromParan.length - 1])
      // console.log(IDOfTo, IDOfFrom)
      if (IDOfTo !== IDOfFrom) {
        await this.getProductDetail()
        // console.log('เข้าเงื่อนไขที่ 1')
        // await this.$EventBus.$emit('getBreadcrumb')
        // await this.setProductDetail()
        // await this.getReviewProduct(this.pageNumber)
        // await this.getProductRecomment()
      }
    }
  },
  methods: {
    jsonld () {
      return {
        '@type': 'BreadcrumbList',
        '@context': 'http://schema.org/',
        itemListElement: this.itemsJSONld
      }
    },
    getPath () {
      this.pathShop = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
      this.items[0].href = this.pathShop
    },
    // gotoBannerPage (val) {
    //   if (val.setLink === '/Home') {
    //     this.$router.push({ path: '/' }).catch(() => {})
    //   } else if (val.setLink === '/ListProduct') {
    //     const encodedEvent = encodeURIComponent(val.category_name)
    //     this.$router.push({ path: `/ListProduct/${encodedEvent}?id=${val.id}&page=1` }).catch(() => {})
    //   } else {
    //     this.$router.push(this.pathShop).catch(() => {})
    //   }
    // },
    async getProductDetail () {
      // var data
      // this.productDetail = []
      // var path = this.$router.currentRoute.params.data
      // var cleanPath = path.split('-')
      // this.keyword = cleanPath[0]
      // if (localStorage.getItem('roleUser') !== null) {
      //   var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      //   var companyID = ''
      //   if (localStorage.getItem('SetRowCompany') !== null) {
      //     var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      //     companyID = companyDataSet.company.company_id
      //   } else {
      //     companyID = ''
      //   }
      //   data = {
      //     product_id: parseInt(cleanPath[cleanPath.length - 1]),
      //     role_user: dataRole.role,
      //     company_id: companyID
      //   }
      // } else {
      //   data = {
      //     product_id: parseInt(cleanPath[cleanPath.length - 1]),
      //     role_user: 'ext_buyer',
      //     company_id: ''
      //   }
      // }
      // await this.$store.dispatch('actionsProductDetail', data)
      // var res = await this.$store.state.ModuleProduct.stateProductDetailData
      // if (res.message !== 'This user is unauthorized.') {
      //   if (res.result === 'SUCCESS') {
      //     this.items = []
      //     this.productDetail = res.data
      //     if (JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order' || JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order_no_JV') {
      //       this.items = [{
      //         category_name: 'หน้าแรก',
      //         id: 0,
      //         disabled: false,
      //         color: '#636363',
      //         setLink: '/SalesOrder',
      //         href: this.pathShop
      //       }]
      //     } else {
      //       this.items = [{
      //         category_name: 'หน้าแรก',
      //         id: 0,
      //         disabled: false,
      //         color: '#636363',
      //         setLink: '/Home',
      //         href: '/'
      //       }]
      //     }
      //     for (var i = 1; i < this.productDetail.list_category.length; i++) {
      //       if ((i + 1) === this.productDetail.list_category.length) {
      //         if (JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order' || JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order_no_JV') {
      //           this.items.push({
      //             category_name: this.productDetail.list_category[i].category_name,
      //             id: this.productDetail.list_category[i].id,
      //             disabled: true,
      //             color: '#3EC6B6',
      //             setLink: '/SalesOrder',
      //             href: this.pathShop
      //           })
      //         } else {
      //           this.items.push({
      //             category_name: this.productDetail.list_category[i].category_name,
      //             id: this.productDetail.list_category[i].id,
      //             disabled: true,
      //             color: '#3EC6B6',
      //             setLink: '/Last',
      //             href: '/'
      //           })
      //         }
      //       } else {
      //         if (JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order' || JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order_no_JV') {
      //           this.items.push({
      //             category_name: this.productDetail.list_category[i].category_name,
      //             id: this.productDetail.list_category[i].id,
      //             disabled: true,
      //             color: '#636363',
      //             setLink: '/SalesOrder',
      //             href: this.pathShop
      //           })
      //         } else {
      //           this.items.push({
      //             category_name: this.productDetail.list_category[i].category_name,
      //             id: this.productDetail.list_category[i].hierachy,
      //             disabled: false,
      //             color: '#636363',
      //             setLink: '/ListProduct',
      //             href: this.path + 'ListProduct/' + encodeURIComponent(`${this.productDetail.list_category[i].category_name}`) + '?id=' + `${this.productDetail.list_category[i].hierachy}` + '&page=1'
      //           })
      //         }
      //       }
      //     }
      //   }
      // } else {
      //   this.$EventBus.$emit('refreshToken')
      // }
      this.itemsJSONld = [
        { '@type': 'ListItem', position: 1, item: { '@type': 'Thing', name: 'หน้าแรก' } },
        { '@type': 'ListItem', position: 2, item: { '@type': 'Thing', name: this.items[1].category_name } },
        { '@type': 'ListItem', position: 3, item: { '@type': 'Thing', name: this.items[2].category_name } }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    if (this.MobileSize) {
      this.$EventBus.$emit('GetLink')
    }
  }
}
</script>

<style scoped>
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #3EC6B6 !important;
}
.v-breadcrumbs li .v-icon {
  color: #3EC6B6 !important;
}
@media screen and (max-width: 640px) {
  .breadcrumbsPadding {
    display: flex !important;
    align-items: center !important;
    width: max-content !important;
    padding: 1rem 1rem !important;
    /* margin-bottom: 12px !important; */
    min-width: 100% !important;
    overflow: auto !important;
    padding-left: 2.5%;
    padding-top: 1.5%;
    padding-bottom: 1.5%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: inline-block;
    flex-wrap: wrap;
    flex: 0 1 auto;
    list-style-type: none;
    width: 100%;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
</style>
