<template>
  <!-- <v-container> -->
  <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" :class="MobileSize ? 'mt-5' : ''">
    <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">แดชบอร์ด</v-card-title>
    <v-card-title style="font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>แดชบอร์ด</v-card-title>
    <v-card-text>
      <v-row justify="center" dense class="mb-4">
        <v-col cols="12">
          <DashBoard />
        </v-col>
      </v-row>
     <!--  <v-row justify="center" dense class="mb-4">
        <v-col cols="12">
          <ProductTopTen />
        </v-col>
      </v-row> -->
      <v-row justify="center" dense class="mb-4">
        <v-col cols="12">
          <UserTopTen />
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
  <!-- </v-container> -->
</template>

<script>
export default {
  components: {
    DashBoard: () => import(/* webpackPrefetch: true */ '@/components/NewDashboard/NewDashboard'),
    // ProductTopTen: () => import('@/components/NewDashboard/ProductTopTen'),
    UserTopTen: () => import(/* webpackPrefetch: true */ '@/components/NewDashboard/UserTopTen')
  },
  data () {
    return {
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAdmin')
    // await this.getDataInDashboard()
  },
  methods: {
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    }
  }
}
</script>
