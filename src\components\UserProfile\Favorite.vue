<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4' : 'mb-4' ]">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="pb-0" v-if="!MobileSize">{{ $t('FavoritePage.header') }}</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;" class="pb-0" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> {{ $t('FavoritePage.header') }}</v-card-title>
      <v-card-text>
        <!-- ส่วน Filter ข้อมูล -->
        <v-row dense class="my-6">
          <v-col cols="12" md="4" sm="12">
            <v-text-field v-model="searchNameProduct" outlined dense style="border-radius: 8px;" hide-details :placeholder="$t('FavoritePage.PlaceHolderName')" append-icon="mdi-magnify"></v-text-field>
          </v-col>
          <v-col cols="12" md="4" sm="6" :class="IpadSize ? 'pr-4' : MobileSize ? '' : 'px-4 pt-1'">
            <v-row dense :class="MobileSize ? 'pt-3' : 'pt-1'">
              <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pr-2 pt-2">{{ $t('FavoritePage.Type') }} :</span>
              <v-select v-model="selectType" :items="itemsSelect" class="setCustomSelect" @change="filterFavorite()" :menu-props="{ offsetY: true }" item-text="text" item-value="value" dense outlined hide-details append-icon="mdi-chevron-down" style="border-radius: 8px;" :style="(!IpadSize && !MobileSize) && checkWidth > 1345 ? 'max-width: 220px;' : (!IpadSize && !MobileSize) && checkWidth < 1345  ? 'max-width: 65%;' : 'max-width: 100%;'"></v-select>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-row dense :class="MobileSize ? 'pt-3' : 'pt-1'">
              <span style="font-size: 16px; font-weight: 500; color: #333333;"  class="pr-2 pt-2">{{ $t('FavoritePage.Category') }} :</span>
              <v-select v-model="searchCategory" :items="itemCategory" class="setCustomSelect" @change="filterFavorite()" :placeholder="$t('FavoritePage.PleceHolderCategory')" item-text="category_name" item-value="category_name" :menu-props="{ offsetY: true }" dense outlined hide-details append-icon="mdi-chevron-down" style="border-radius: 8px;" :style="(!IpadSize && !MobileSize) && checkWidth > 1345 ? 'max-width: 220px;' : (!IpadSize && !MobileSize) && checkWidth < 1345  ? 'max-width: 65%;' : 'max-width: 100%;'"></v-select>
            </v-row>
          </v-col>
        </v-row>
        <!-- ส่วนแสดงจำนวน -->
        <v-row dense :class="MobileSize ? 'pb-3' : ''">
          <v-col cols="12">
            <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ $t('FavoritePage.Wishlist') }} {{ searchNameProduct === '' ? AllProduct.length : paginated.length }} {{ $t('FavoritePage.list') }}</span>
          </v-col>
        </v-row>
        <v-row justify="start" class="mx-2" v-if="paginated.length !== 0 && !MobileSize && !IpadSize">
          <v-col cols="12" md="3" v-for="(item, index) in paginated" :key="index">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="mx-0" v-else-if="paginated.length !== 0 && !MobileSize && IpadSize">
          <v-col cols="12" md="4" sm="6" v-for="(item, index) in paginated" :key="index" class="px-1">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" dense v-else-if="paginated.length !== 0 && MobileSize && !IpadSize">
          <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index" class="px-1">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="mx-2" v-else>
          <v-col cols="12" align="center">
            <v-img :src="require('@/assets/New_No_Favorite.png')" max-height="222" max-width="223" height="100%" width="100%" contain class="mt-6 mb-6"></v-img>
            <p style="font-size: 18px; font-weight: 600; color: #636363;">{{ $t('FavoritePage.NoProduct1') }}</p>
            <span style="font-size: 16px; font-weight: 400; color: #9A9A9A;">{{ $t('FavoritePage.NoProduct2') }}</span><span style="font-size: 18px; font-weight: 600; color: #1B5DD6; text-decoration: underline;">{{ $t('FavoritePage.NoProduct3') }}</span>
          </v-col>
          <!-- <v-col cols="12">
            <v-row justify="center" class="my-5">
              <span style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C; font-size: 24px;"><b>ไม่มีรายการสินค้าที่ถูกใจ</b></span>
            </v-row>
          </v-col> -->
        </v-row>
        <v-row justify="center" class="my-6" v-if="AllProduct.length > 16">
          <v-pagination
          color="#27AB9C"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="7"
          @change="pageChange()"
          circle
          ></v-pagination>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardProducts: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      header: 'สินค้ามาใหม่',
      overlay2: true,
      productCount: null,
      checkWidth: '',
      widthScreen: window.innerWidth,
      AllProduct: [],
      pageMax: null,
      current: 1,
      pageSize: 16,
      countPage: 1,
      searchNameProduct: '',
      itemsSelect: [
        { text: this.$t('FavoritePage.TypeList.All'), value: '' },
        { text: this.$t('FavoritePage.TypeList.GeneralProducts'), value: 'general' },
        { text: this.$t('FavoritePage.TypeList.ServiceProduct'), value: 'service' }
      ],
      selectType: '',
      searchCategory: '',
      itemCategory: []
    }
  },
  created () {
    // console.log('เข้า ChangePassword')
    this.$EventBus.$emit('checkpage', this.$route.name)
    this.$EventBus.$emit('main', this.$route.name)
    this.$EventBus.$emit('changeNavAccount')
    if (localStorage.getItem('oneData') !== null) {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(this.onedata)
      this.getAllFavoriteProduct()
      this.getAllCategory()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('getAllFavoriteProduct', this.getAllFavoriteProduct)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getAllFavoriteProduct')
    })
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.searchNameProduct !== '') {
        return this.AllProduct.filter(item => {
          return item.name.toLowerCase().includes(this.searchNameProduct.toLocaleLowerCase())
        })
      } else {
        return this.AllProduct.slice(this.indexStart, this.indexEnd)
      }
    }
  },
  watch: {
    widthScreen (val) {
      this.checkWidth = val
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/favoriteMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/favorite' }).catch(() => {})
      }
    },
    paginated (val) {
      if (this.searchNameProduct !== '') {
        this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
      } else {
        this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
      }
    }
  },
  methods: {
    async getAllCategory () {
      var data = 'all'
      this.itemCategory = []
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.message === 'Get all category success.') {
        this.itemCategory = response.data
        this.itemCategory.unshift({
          category_logo_path: '',
          category_name: 'หมวดหมู่ทั้งหมด',
          hierachy: ''
        })
      }
      // console.log('response cat ====>', response)
    },
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async filterFavorite () {
      await this.getAllFavoriteProduct()
    },
    async getAllFavoriteProduct () {
      this.$store.commit('openLoader')
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // console.log(dataRole.role)
      // var companyId
      // if (localStorage.getItem('SetRowCompany') !== null) {
      //   companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      // }
      var data
      // if (dataRole.role === 'purchaser') {
      //   data = {
      //     role_user: dataRole.role,
      //     company_id: companyId.company.company_id,
      //     company_position_id: companyId.position.role_id,
      //     com_perm_id: companyId.position.com_perm_id
      //   }
      // } else
      // if (dataRole.role === 'ext_byer') {
      data = {
        // role_user: dataRole.role,
        role_user: 'ext_buyer',
        company_id: -1,
        company_position_id: -1,
        com_perm_id: -1,
        product_type: this.selectType,
        product_category: this.searchCategory === 'หมวดหมู่ทั้งหมด' ? '' : this.searchCategory
      }
      // }
      await this.$store.dispatch('actionsUPSGetFavoriteProductList', data)
      var response = await this.$store.state.ModuleFavoriteProduct.stateGetFavoriteProductList
      // console.log('response favorite list product =======>', response.data)
      if (response.result === 'SUCCESS') {
        if (response.message !== 'You do not have favorite product.') {
          if (response.data.length !== 0) {
            this.overlay2 = false
            this.AllProduct = response.data.favorite_product_detail
            // console.log('favorite List ======>', this.AllProduct)
            this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
            if (this.pageMax === 1) {
              this.pageNumber = 1
            }
            this.productCount = response.data.favorite_product_detail.length
            this.countPage = this.countPage + 1
            this.$store.commit('closeLoader')
          } else {
            this.overlay2 = false
            this.AllProduct = []
            this.productCount = 0
            this.$store.commit('closeLoader')
          }
        } else {
          // console.log('เข้ามาแล้วจ้า')
          this.AllProduct = []
          this.productCount = 0
          this.$store.commit('closeLoader')
        }
      } else {
        this.$store.commit('closeLoader')
        if (response.message !== 'This user is unauthorized.') {
          const Toast = this.$swal.mixin({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
          Toast.fire({
            icon: 'error',
            title: `${response.message}`
          })
        } else {
          this.$EventBus.$emit('refreshToken')
        }
      }
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
</style>
