<template>
  <v-container class="pt-0">
    <div :style="MobileSize ? 'padding: 24px; padding-top: 0px;' : ''">
      <v-row v-if="MobileSize">
      <v-col cols="12">
        <span style="font-size: 18px; font-weight: bold;">ประเภทการแสดงข้อมูล</span>
      </v-col>
        <v-col cols="12">
          <v-select
            v-model="selectedTypeData"
            :items="TypeDataItems"
            outlined
          ></v-select>
        </v-col>
      </v-row>
      <v-card class="elevation-0" style="background-color: #FBFBFB;" v-if="!MobileSize || MobileSize && selectedTypeData === 'กราฟแสดงรายได้'">
        <v-row>
          <v-col class="d-flex align-center">
            <img height="40" width="40" class="mx-2" src="@/assets/ImageINET-Marketplace/ICONDashboard/graphIcon.png"/>
            <span style="font-size: 16px; font-weight: bold;">กราฟแสดงรายได้</span>
          </v-col>
        </v-row>
        <v-row v-if="!MobileSize">
          <v-col class="d-flex justify-end align-center">
            <div style="background-color: #efbe72; width: 16px; height: 16px; border-radius: 5px;" class="mr-2"></div><span class="mr-5">ยอดขายทั้งหมด</span>
            <div style="background-color: #e5644e; width: 16px; height: 16px; border-radius: 5px;" class="mr-2"></div><span class="mr-5">ค่าธรรมเนียม</span>
            <div style="background-color: #006da0; width: 16px; height: 16px; border-radius: 5px;" class="mr-2"></div><span class="mr-5">ยอดสุทธิทั้งหมด (หลังหักค่าธรรมเนียม)</span>
          </v-col>
        </v-row>
        <v-row v-else>
          <v-col cols="12" class="d-flex">
            <div style="background-color: #efbe72; width: 16px; height: 16px; border-radius: 5px;" class="mr-2"></div><span class="mr-5">ยอดขายทั้งหมด</span>
          </v-col>
          <v-col cols="12" class="d-flex">
            <div style="background-color: #e5644e; width: 16px; height: 16px; border-radius: 5px;" class="mr-2"></div><span class="mr-5">ค่าธรรมเนียม</span>
          </v-col>
          <v-col cols="12" class="d-flex">
            <div style="background-color: #006da0; width: 16px; height: 16px; border-radius: 5px;" class="mr-2"></div><span class="mr-5">ยอดสุทธิทั้งหมด (หลังหักค่าธรรมเนียม)</span>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <apexchart type="bar" height="400" :options="chartOptions" :series="series"></apexchart>
          </v-col>
        </v-row>
      </v-card>
      <v-row v-if="!MobileSize || MobileSize && selectedTypeData === 'กราฟแสดงรายได้'">
        <v-col cols="6" md="4" sm="6" class="mt-5">
          <v-card  class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/totalSales.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center">
                <span>ยอดขายทั้งหมด</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{totalIncome}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="6" md="4" sm="6" class="mt-5">
          <v-card  class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/Fee.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center">
                <span>ค่าธรรมเนียม</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{totalfee}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="6" md="4" sm="6" :class="MobileSize || IpadSize ? '' : 'mt-5'">
          <v-card class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center pb-2">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/TotalSalesNoFee.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" class="d-flex justify-center pa-1">
                <span>รายได้ทั้งหมด</span>
              </v-col>
              <v-col cols="12" class="d-flex justify-center pa-0">
                <span style="font-size: 12px;">(หลังหักค่าธรรมเนียม)</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{totalIncomeNoFee}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="6" md="3" sm="6">
          <v-card  class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/Billing.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center">
                <span>จำนวนวางบิลทั้งหมด</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{totalBilling}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="6" md="3" sm="6">
          <v-card  class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/BillingPaid.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center">
                <span>บิลที่ชำระเงินแล้ว</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{BillingSuccess}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="6" md="3" sm="6">
          <v-card  class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/BillingNotPaid.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center">
                <span>บิลที่รอชำระเงิน</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{BillingNotPaid}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" md="3" sm="12">
          <v-card  class="elevation-0 pa-3 formatBox">
            <v-row>
              <v-col class="d-flex justify-center">
                <img height="50" width="50" src="@/assets/ImageINET-Marketplace/ICONDashboard/BillingOutOfDate.png"/>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center">
                <span>บิลที่เกินกำหนดชำระ</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 30px; color: #27ab9c; font-weight: bold">{{BillingOutOfDate}}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-center pt-0">
                <span style="font-size: 16px; color: #989898;">บาท</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-if="!MobileSize || MobileSize && selectedTypeData === 'ประวัติรายการสั่งซื้อ'">
        <v-col class="d-flex align-center">
          <img height="40" width="40" src="@/assets/ImageINET-Marketplace/ICONDashboard/TotalTransaction.png"/>
          <span style="font-size: 16px; color: #27ab9c; font-weight: bold">ประวัติรายการสั่งซื้อ ({{dataTable.length}} รายการ)</span>
        </v-col>
      </v-row>
      <v-row v-if="!MobileSize || MobileSize && selectedTypeData === 'ประวัติรายการสั่งซื้อ'">
        <v-col>
          <v-card>
            <v-data-table
              :headers="headers"
              :items="dataTable"
            >
              <template v-slot:[`item.status_payment`]="{ item }">
                <v-chip style="font-weight: bold;" :text-color="textColorStatus(item.status_payment)" :color="backgroundColorStatus(item.status_payment)">{{ textTranslate(item.status_payment) }}</v-chip>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </v-container>

</template>

<script>
import VueApexCharts from 'vue-apexcharts'
export default {
  props: {
    payload: {
      type: Object
    },
    selectedDropdown: {
      type: String
    },
    selectedYear: {
      type: Number
    },
    selectedMonth: {
      type: String
    },
    dates: {
      type: Array
    }
  },
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      selectedTypeData: 'กราฟแสดงรายได้',
      TypeDataItems: [
        'กราฟแสดงรายได้',
        'ประวัติรายการสั่งซื้อ'
      ],
      dataTable: [],
      headers: [
        {
          text: 'ชื่อร้านค้า',
          value: 'name_th',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'purchase_order_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วัน / เวลา ชำระเงิน',
          value: 'paid_datetime',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ยอดขาย',
          value: 'total_income',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ค่าธรรมเนียม GP',
          value: 'fee_gp',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ค่าธรรมเนียม Payment',
          value: 'fee_payment',
          sortable: false,
          align: 'start',
          width: '220px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ยอดเงินที่จะได้รับ',
          value: 'total_price_vat',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะโอนเงิน',
          value: 'status_payment',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      series: [
        {
          name: 'ยอดขายทั้งหมด',
          data: []
        },
        {
          name: 'ค่าธรรมเนียม',
          data: []
        },
        {
          name: 'ยอดสุทธิทั้งหมด (หลังหักค่าธรรมเนียม)',
          data: []
        }
      ],
      monthDay: [],
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      totalIncome: '',
      totalfee: '',
      totalIncomeNoFee: '',
      totalBilling: '',
      BillingSuccess: '',
      BillingNotPaid: '',
      BillingOutOfDate: '',
      // chartOptions: {
      //   chart: {
      //     type: 'bar',
      //     stacked: false
      //   },
      //   plotOptions: {
      //     bar: {
      //       horizontal: false,
      //       columnWidth: '55%'
      //     }
      //   },
      //   legend: {
      //     show: false
      //   },
      //   dataLabels: {
      //     enabled: false
      //   },
      //   tooltip: {
      //     custom: function ({ series, seriesIndex, dataPointIndex, w }) {
      //       var img = require('@/assets/ImageINET-Marketplace/ICONDashboard/TotalTransaction.png')
      //       return `<div style="
      //           background: white;
      //           padding: 10px;
      //           border-radius: 8px;
      //           box-shadow: 0px 2px 10px rgba(0,0,0,0.1);
      //           text-align: left;
      //         ">
      //           <div style="display: flex; align-items: center;">
      //             <img height="40" width="40" src="${img}"/>
      //             <strong>${w.globals.labels[dataPointIndex]}</strong>
      //           </div>
      //           <div style="display: flex; align-items: center; margin-top: 5px;">
      //             <span style="color: #ffb74d; font-size: 16px;">🟡</span>
      //             <span style="margin-left: 5px;">${series[seriesIndex][dataPointIndex]} บาท</span>
      //           </div>
      //         </div>`
      //     }
      //   },
      //   xaxis: {
      //     categories: this.selectedDropdown === 'รายปี' ? [
      //       'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
      //       'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      //     ] : this.selectedDropdown === 'รายเดือน' ? ['1', '2', '3'] : ['']
      //   },
      //   colors: ['#efbe72', '#e5644e', '#006da0']
      // }
      rawDateList: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    chartOptions () {
      return {
        chart: {
          type: 'bar',
          stacked: false
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%'
          }
        },
        legend: {
          show: false
        },
        dataLabels: {
          enabled: false
        },
        tooltip: {
          custom: function ({ series, seriesIndex, dataPointIndex, w }) {
            var img = require('@/assets/ImageINET-Marketplace/ICONDashboard/calendarIcon.png')
            const value = series[seriesIndex][dataPointIndex]
            return `<div style="
                background: white;
                padding: 10px;
                border-radius: 8px;
                box-shadow: 0px 2px 10px rgba(0,0,0,0.1);
                text-align: left;
              ">
                <div style="display: flex; align-items: center;">
                  <img height="20" width="20" class="mr-2" src="${img}"/>
                  <strong>${w.globals.labels[dataPointIndex]}</strong>
                </div>
                <div style="display: flex; align-items: center; margin-top: 5px;">
                  <span style="color: #ffb74d; font-size: 16px;">${seriesIndex === 0 ? '🟡' : seriesIndex === 1 ? '🟠' : '🔵'}</span>
                  <span style="margin-left: 5px;">${value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')} บาท</span>
                </div>
              </div>`
          }
        },
        xaxis: {
          categories: this.selectedDropdown === 'รายปี' ? [
            'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
            'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
          ] : this.selectedDropdown === 'รายเดือน' ? this.monthDay : this.monthDay
        },
        colors: ['#efbe72', '#e5644e', '#006da0']
      }
    }
  },
  watch: {
    payload: {
      async handler (newValue, oldValue) {
        await this.getChartData()
      },
      deep: true
    },
    selectedDropdown: {
      async handler (val) {
        if (val === 'รายเดือน' && this.selectedMonth !== null) {
          await this.getChartData()
          // await this.changeChartsOptions()
        }
      },
      deep: true
    },
    selectedYear: {
      async handler (val) {
        if (this.selectedMonth !== null && this.selectedMonth !== '') {
          await this.changeChartsOptions()
        }
      },
      deep: true
    },
    selectedMonth: {
      async handler (val) {
        await this.changeChartsOptions()
      },
      deep: true
    },
    dates: {
      async handler (val) {
        // await this.changeChartsOptions()
      },
      deep: true
    }
  },
  async created () {
    await this.getChartData()
    await this.$EventBus.$on('confirmDatePickerDashboardParnter', this.getXaxisCategory)
    // await this.$EventBus.$on('confirmDatePickerDashboardParnter', this.getXaxisCategory)
  },
  methods: {
    async getChartData () {
      // console.log(this.payload)
      var data1 = {
        ...this.payload,
        date_type: this.selectedDropdown === 'รายปี' ? 'year' : this.selectedDropdown === 'รายเดือน' ? 'month' : 'day'
      }
      await this.$store.dispatch('actionsGetDashboardPartnerDistributeIncome', data1)
      var response = await this.$store.state.ModuleBusiness.stateGetDashboardPartnerDistributeIncome
      if (response.code === 200) {
        // console.log(response.data, 'data')
        this.totalIncome = response.data.summaryPayment[0].income
        this.totalBilling = response.data.bills_partner.pending
        this.BillingOutOfDate = response.data.bills_partner.overdue_paid
        this.BillingSuccess = response.data.bills_partner.paid
        this.BillingNotPaid = response.data.bills_partner.unpaid
        this.totalfee = response.data.summaryPayment[0].payment_charge
        this.totalIncomeNoFee = response.data.summaryPayment[0].net_price_payment
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
      var data2 = {
        ...this.payload,
        date_type: this.selectedDropdown === 'รายปี' ? 'year' : this.selectedDropdown === 'รายเดือน' ? 'month' : 'day'
      }
      await this.$store.dispatch('actionsGetDashboardPartnerOrderListIncome', data2)
      var responseList = await this.$store.state.ModuleBusiness.stateGetDashboardPartnerOrderListIncome
      if (responseList.code === 200) {
        this.dataTable = responseList.data
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseList.message}`
        })
      }
      // var chartPayload = {
      //   ...this.payload,
      //   date_type: this.selectedDropdown === 'รายปี' ? 'year' : this.selectedDropdown === 'รายเดือน' ? 'month' : 'day'
      // }
      var chartPayloadMock = {
        ...this.payload,
        date_type: this.selectedDropdown === 'รายปี' ? 'year' : this.selectedDropdown === 'รายเดือน' ? 'month' : 'day'
      }
      chartPayloadMock.partner_code = '083'
      await this.$store.dispatch('actionsPartnerChartIncome', chartPayloadMock)
      var responseChart = await this.$store.state.ModuleBusiness.statePartnerChartIncome
      if (responseChart.code === 200) {
        this.monthDay = []
        if (this.selectedDropdown === 'รายเดือน' || this.selectedDropdown === 'รายวัน') {
          for (var i = 0; i < responseChart.data.length; i++) {
            await this.convertToThaiDate(responseChart.data[i].date)
          }
          // await this.convertToThaiDate(responseChart.data.map(item => item.date))
        }
        this.series = [
          {
            name: 'ยอดขายทั้งหมด',
            data: responseChart.data.map(item => item.revenue.total)
          },
          {
            name: 'ค่าธรรมเนียม',
            data: responseChart.data.map(item => item.revenue.fee)
          },
          {
            name: 'ยอดสุทธิทั้งหมด (หลังหักค่าธรรมเนียม)',
            data: responseChart.data.map(item => item.revenue.net_total)
          }
        ]
        // this.series
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseChart.message}`
        })
      }
    },
    backgroundColorStatus (val) {
      if (val === 'Pending') {
        return '#ffe7d7'
      } else if (val === 'Success') {
        return '#e8fbe3'
      } else if (val === 'Not Paid') {
        return '#ffe7d7'
      } else if (val === 'Fail') {
        return '#FEE7E8'
      } else if (val === 'Cancel') {
        return '#FEE7E8'
      } else {
        return '#FEE7E8'
      }
    },
    textColorStatus (val) {
      if (val === 'Pending') {
        return '#ff9d0d'
      } else if (val === 'Success') {
        return '#52C41A'
      } else if (val === 'Not Paid') {
        return '#ff9d0d'
      } else if (val === 'Fail') {
        return '#F5222D'
      } else if (val === 'Cancel') {
        return '#F5222D'
      } else {
        return '#F5222D'
      }
    },
    textTranslate (val) {
      if (val === 'Pending') {
        return 'รอชำระเงิน'
      } else if (val === 'Success') {
        return 'ชำระเงินแล้ว'
      } else if (val === 'Not Paid') {
        return 'รอชำระเงิน'
      } else if (val === 'Fail') {
        return 'ชำระเงินไม่สำเร็จ'
      } else if (val === 'Cancel') {
        return 'ยกเลิกการชำระเงิน'
      } else {
        return val
      }
    },
    async getXaxisCategory () {
      // this.monthDay = []
      // for (var i = 0; i < this.series.lenght; i++) {
      //   this.monthDay.push('กุมภาพันธ์ ' + '2568')
      // }
      // console.log(this.monthDay, 'sda')
      // this.changeChartsOptions()
    },
    changeChartsOptions () {
      this.monthDay = []
      if (this.selectedDropdown === 'รายเดือน') {
        // for (var i = 0; i < this.series[0].data.length; i++) {
        //   this.months.find(e => e.value === this.selectedMonth)
        //   this.monthDay.push((i + 1) + ' ' + this.months.find(e => e.value === this.selectedMonth).text + ' ' + this.selectedYear)
        // }
      } else if (this.selectedDropdown === 'รายวัน') {
        console.log(this.dates, 'dates')
      }
    },
    async convertToThaiDate (dateString) {
      const [year, month, day] = dateString.split('-').map(Number)
      const thaiYear = year + 543
      const thaiMonths = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const thaiDate = `${day} ${thaiMonths[month - 1]} ${thaiYear}`
      this.monthDay.push(thaiDate)
    }
  }
}
</script>

<style>

</style>
<style scoped>
  .formatBox {
    border: 1px solid #cbeae0;
    border-radius: 10px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px !important;
  }
</style>
