<template>
  <v-container :class="!MobileSize ? 'pt-0 px-0 pb-8' : ''" style="border: 1px solid rgb(242, 242, 242);">
    <v-card width="100%" height="100%"  style="border-radius: 8px;" :elevation="MobileSize ? 1 : 0" :class="MobileSize ? 'my-4' : 'py-4 px-4'">
        <v-row>
          <v-col cols="12" style="display: flex; align-items: center;">
            <v-btn v-if="MobileSize" icon style="background-color: #edfcf7;" width="24px" height="24px" class="mr-2 ml-2 pa-0" @click="backtoUser()"><v-icon small color="#27AB9C">mdi-chevron-left</v-icon></v-btn><span style="font-weight: 700; font-size: 24px;">ชวนเพื่อน</span>
            <v-spacer></v-spacer>
          </v-col>
        </v-row>
        <v-row>
          <v-col v-if="dataInviteCode.status_invite_code_friend === false" :cols="IpadSize || MobileSize ? '12' : '6'" class="d-flex justify-center align-center">
            <v-card elevation="1" class="d-flex pl-4 align-center" :style="MobileSize || IpadSize ? 'width: 50vw;' : 'width: 30vw;'" style="background-color: white; border-radius: 16px; height: 100px;">
              <div style="width: 100%;" >
                <v-card-title style="font-size: small; font-weight: 600;" class="ml-2 pa-0">รหัสคำเชิญ</v-card-title>
                <v-card-text class="d-flex justify-content-between align-end">
                  <v-text-field v-model="inputCode" placeholder="ใส่รหัสคำเชิญจากเพื่อน" hide-details class="pt-0" />
                  <v-spacer></v-spacer>
                  <v-btn color="primary" small rounded>ตกลง</v-btn>
                </v-card-text>
              </div>
            </v-card>
          </v-col>
          <v-col :cols="IpadSize || MobileSize ? '12' : dataInviteCode.status_invite_code_friend === true ? '12' : '6'" class="d-flex justify-center align-center">
            <v-card elevation="1" class="d-flex pl-4 align-center" :style="MobileSize || IpadSize ? 'width: 50vw;' : 'width: 30vw;'" style="background-color: white; border-radius: 16px; height: 100px;">
              <div style="width: 100%;">
                <v-card-title style="font-size: small; font-weight: 600;" class="ml-2 pa-0">รหัสของฉัน</v-card-title>
                <v-card-text class="d-flex justify-content-between align-bottom pb-2">
                  <v-card-title :style="IpadProSize || IpadSize || MobileSize ? 'font-size: medium;' : 'font-size: large;'" style="font-weight: 600; align-items: end;" class="ml-1 pa-0">{{ myCode }}</v-card-title>
                  <v-spacer></v-spacer>
                  <v-btn color="primary" small rounded @click="copyCode">คัดลอก</v-btn>
                </v-card-text>
              </div>
            </v-card>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" sm="6"  v-if="dataInviteCode.status_invite_code_friend === false">
          </v-col>
        </v-row>
        <!-- Select Node -->
        <v-row>
          <v-col cols="12">
            <v-select
              v-model="selectedId"
              :items="allNodes"
              item-text="name"
              item-value="id"
              label="เลือก Node"
              @change="handleSelect"
              outlined
              dense
            />
          </v-col>
        </v-row>
        <!-- Tree Chart -->
        <tree-chart :data="treeData">
          <template #node="{ node }">
            <div class="custom-node">
              <img :src="node.image" class="node-img" />
              <div class="node-name">{{ node.name }}</div>
            </div>
          </template>
        </tree-chart>

        <!-- Tree Chart -->
        <v-select
          v-model="selectedId"
          :items="itemAllUsers"
          item-text="name"
          item-value="id"
          label="เลือก Node"
          @change="handleSelect"
          outlined
          dense
        />
        <tree-chart
          :json="Users"
          :horizontal="false"
          class="mx-auto"
        >
          <template #node="{ node }">
            <div class="" :id="'node-' + node.id">
              <img :src="node.image_url" class="node-img" />
              <div class="">{{ node.name }}</div>
            </div>
          </template>
        </tree-chart>
    </v-card>
  </v-container>
</template>

<script>
import TreeChart from 'vue-tree-chart'
export default {
  components: { TreeChart },
  data () {
    return {
      myCode: '',
      inputCode: '',
      referrerCode: null,
      selectedId: null,
      allUsers: [
        {
          id: 'A001',
          code: 'X99999',
          name: 'พี่ใหญ่',
          image: 'https://static.refined-x.com/avat1.jpg',
          children: [
            {
              id: 'A002',
              code: 'ABC123',
              name: 'เรานี่แหละ',
              image: 'https://static.refined-x.com/avat2.jpg'
            }
          ]
        },
        {
          id: 'A003',
          code: 'Z55555',
          name: 'อีกคน',
          image: 'https://static.refined-x.com/avat3.jpg'
        }
      ],
      Users: {
        id: 1,
        name: 'Aon',
        image_url: 'data:image/jpeg;base64,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',
        class: ['rootNode'],
        children: [
          {
            id: 2,
            name: 'Tar',
            image_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR0VF3qeQMP14ZyvqhtzvIX41ONak6EWqtndA&s',
            children: [
              {
                id: 3,
                name: 'Bank',
                image_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRXJgHj1McH9fp_MsNbvHEG7m65DktcJfHhJw&s'
              }
            ]
          },
          {
            id: 4,
            name: 'Palm',
            image_url: 'data:image/png;base64,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',
            // mate: {
            //   name: 'Bank',
            //   image_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRXJgHj1McH9fp_MsNbvHEG7m65DktcJfHhJw&s'
            // },
            children: [
              {
                id: 5,
                name: 'Aof',
                image_url: 'data:image/jpeg;base64,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'
              },
              {
                id: 6,
                name: 'Deen',
                image_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRimVGV7GeNVUa2HRfU88SqwlfmNugKmUFLgA&s'
              },
              {
                id: 7,
                name: 'Gee',
                image_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSMhgoEtcB3F5niB2FP-K4Mfh5N8QPngaRDFA&s'
              }
            ]
          },
          {
            id: 8,
            name: 'Tell',
            image_url: 'data:image/jpeg;base64,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'
          }
        ]
      },
      treeData: {},
      allNodes: [],
      itemAllUsers: [],
      dataInviteCode: []
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.getDataInviteCode()
      const myNode = this.findNodeByCode(this.myCode)
      this.treeData = myNode || {}
      this.buildNodeList()
      this.selectItemAllUsers()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/FriendGetFriendsMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/FriendGetFriends' }).catch(() => {})
      }
    }
  },
  methods: {
    async getDataInviteCode () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetDataInviteCode')
      var response = await this.$store.state.ModuleUser.stateGetDataInviteCode
      console.log('response', response)
      this.$store.commit('closeLoader')
      if (response.code === 200) {
        // console.log('111')
        this.dataInviteCode = response.data
        this.myCode = this.dataInviteCode.invite_code
      } else {
        this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    copyCode () {
      navigator.clipboard.writeText(this.myCode).then(() => {
        this.$swal.fire({ icon: 'success', text: 'คัดลอกสำเร็จ', showConfirmButton: false, timer: 1500 })
      })
    },
    submitCode () {
      const refNode = this.findNodeByCode(this.inputCode)
      if (refNode) {
        this.referrerCode = this.inputCode
        this.treeData = refNode
      } else {
        this.$swal.fire({ icon: 'error', text: 'ไม่พบ', showConfirmButton: false, timer: 1500 })
      }
    },
    handleSelect (id) {
      const node = this.findNodeById(id)
      if (node) this.treeData = node
    },
    findNodeByCode (code) {
      let result = null
      const traverse = (node) => {
        if (node.code === code) {
          result = node
          return
        }
        if (node.children) {
          node.children.forEach(traverse)
        }
      }
      this.allUsers.forEach(traverse)
      return result
    },
    findNodeById (id) {
      let result = null
      const traverse = (node) => {
        if (node.id === id) {
          result = node
          return
        }
        if (node.children) {
          node.children.forEach(traverse)
        }
      }
      this.allUsers.forEach(traverse)
      return result
    },
    buildNodeList () {
      const nodes = []
      const traverse = (node) => {
        nodes.push({ id: node.id, name: node.name })
        if (node.children) node.children.forEach(traverse)
      }
      this.allUsers.forEach(traverse)
      this.allNodes = nodes
    },
    selectItemAllUsers () {
      const result = []
      const traverse = (node) => {
        if (!node) return
        result.push({ id: node.id, name: node.name })
        if (Array.isArray(node.children)) {
          node.children.forEach(traverse)
        }
      }
      if (Array.isArray(this.Users)) {
        this.Users.forEach(traverse)
      }
      this.itemAllUsers = result
    }
  }
}
</script>

<style scoped>
/* ::v-deep .childLevel:before, .extend:after {
  border-left: 2px solid black;
} */

.v-text-field--filled > .v-input__control > .v-input__slot, .v-text-field--full-width > .v-input__control > .v-input__slot, .v-text-field--outlined > .v-input__control > .v-input__slot {
    align-items: stretch;
    min-height: 46px;
}

::v-deep div.extend_handle {
  display: none;
}

::v-deep .node img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 6px;
}

::v-deep .node .person .avat {
  border: none;
}

::v-deep .node .name {
  font-size: small;
  font-weight: 600;
  margin-top: 4px;
}

.highlight {
  border-color: #4caf50 !important;
  box-shadow: 0 0 8px #4caf50 !important;
}
</style>
