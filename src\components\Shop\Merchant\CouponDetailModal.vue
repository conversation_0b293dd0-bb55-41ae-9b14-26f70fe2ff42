<template>
  <div class="text-center">
    <v-dialog v-model="openCouponDetailModal" width="732" persistent>
      <v-card min-height="558px">
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>ข้อมูลคูปอง</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs v-if="couponData">
          <v-row no-gutters justify="center" align-content="center" class="px-5">
            <v-col cols="12">
              <v-img :src="couponData.couponImagePath" :height="MobileSize ? 206 : 286" contain style="border-radius: 8px;" v-if="couponData.couponImagePath !== null"></v-img>
              <v-img src="@/assets/NoImage.png" :height="MobileSize ? 236 : 206" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
            </v-col>
            <v-col cols="12">
              <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                <v-row :class="MobileSize ? 'ma-0' : 'ma-4'">
                  <v-col cols="12">
                    <span style="font-size: 16px;"><b>ข้อมูลคูปอง</b></span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 3">
                    <p><b>ชื่อคูปอง</b></p>
                    <p><b>โค้ดส่วนลด</b></p>
                    <p class="mb-0"><b>รายละเอียดคูปอง</b></p>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 9">
                    <p>{{ couponData.couponDetail.couponName ? couponData.couponDetail.couponName : '-' }}</p>
                    <p>{{ couponData.couponDetail.couponCode ? couponData.couponDetail.couponCode : '-' }}</p>
                    <p class="mb-0">{{ couponData.couponDetail.couponDescription ? couponData.couponDetail.couponDescription : '-' }}</p>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="12">
              <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                <v-row :class="MobileSize ? 'ma-0' : 'ma-4'">
                  <v-col cols="12">
                    <span style="font-size: 16px;"><b>กำหนดระยะเวลาคูปอง</b></span>
                    </v-col>
                  <v-col :cols="MobileSize ? 6 : 3" v-if="!MobileSize">
                    <p><b>ระยะเวลาเก็บคูปอง</b></p>
                    <p class="mb-0"><b>ระยะเวลาใช้โปรโมชัน</b></p>
                  </v-col>
                  <v-col cols="12" v-if="MobileSize">
                    <v-row dense>
                      <v-col cols="6">
                        <p><b>ระยะเวลาเก็บคูปอง</b></p>
                      </v-col>
                      <v-col cols="6">
                        <p>{{ new Date(couponData.couponDate.collectStartDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }} {{ couponData.couponDate.collectEndDate === null ? '- ไม่ระบุวันสิ้นสุด' : '- ' + new Date(couponData.couponDate.collectEndDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }}</p>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 9"  v-if="!MobileSize">
                    <p>{{ new Date(couponData.couponDate.collectStartDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }} {{ couponData.couponDate.collectEndDate === null ? '- ไม่ระบุวันสิ้นสุด' : '- ' + new Date(couponData.couponDate.collectEndDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }}</p>
                    <p class="mb-0">{{ new Date(couponData.couponDate.useStartDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }} {{ couponData.couponDate.useEndDate === null ? '- ไม่ระบุวันสิ้นสุด' : '- ' + new Date(couponData.couponDate.useEndDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }}</p>
                  </v-col>
                  <v-col cols="12" class="pt-0" v-if="MobileSize">
                    <v-row dense>
                      <v-col cols="6">
                        <p class="mb-0"><b>ระยะเวลาใช้โปรโมชัน</b></p>
                      </v-col>
                      <v-col cols="6">
                        <p class="mb-0">{{ new Date(couponData.couponDate.useStartDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }} {{ couponData.couponDate.useEndDate === null ? '- ไม่ระบุวันสิ้นสุด' : '- ' + new Date(couponData.couponDate.useEndDate).toLocaleDateString('en-GB', { timeZone: 'UTC', hour: 'numeric', minute: 'numeric' }) + ' น.' }}</p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="12">
              <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                <v-row :class="MobileSize ? 'ma-0' : 'ma-4'">
                  <v-col cols="12">
                    <span style="font-size: 16px;"><b>กำหนดจำนวนคูปอง และสิทธิ์คูปอง</b></span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 :4" class="pb-0">
                    <p><b>จำนวนคูปอง</b></p>
                    <p><b>จำนวนคูปองที่ใช้ได้จริง</b></p>
                    <p><b>สิทธิ์การใช้คูปอง</b></p>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 8" class="pb-0">
                    <p>{{ couponData.collectQuota }}</p>
                    <p>{{ couponData.quota }}</p>
                    <p>{{ couponData.couponRule.userCap.toString() === '0' ? 'ไม่จำกัดสิทธิ์การใช้' : '1 คนต่อ ' + couponData.couponRule.userCap + ' สิทธิ์การใช้งาน' }}</p>
                  </v-col>
                  <v-col cols="12" class="py-0">
                    <span><b>ประเภทส่วนลด</b></span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 :4" class="py-0">
                    <span style="color: #989898;">ส่วนลดเป็น ({{ couponData.couponRule.discountType === 'money' ? 'จำนวนเงิน บาท' : 'เปอร์เซ็นต์'}})</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 8" class="pt-0">
                    <span>{{ couponData.couponRule.discountAmount }} {{ couponData.couponRule.discountType === 'money' ? ' บาท' : ' %'}}</span>
                  </v-col>
                  <v-col cols="12" class="py-0">
                    <span><b>ค่าใช้จ่ายขั้นต่ำ</b></span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 4"  class="py-0">
                    <span style="color: #989898;">ค่าใช้จ่ายขั้นต่ำ</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 8"  class="pt-0 pb-3">
                    <span>{{ couponData.couponRule.spendMinimum }} บาท</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="12" v-if="couponTypeUser">
              <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
                <v-row :class="MobileSize ? 'ma-0' : 'ma-4'">
                  <v-col cols="12">
                    <span style="font-size: 16px;"><b>รูปแบบที่สามารถใช้คูปองได้</b></span>
                    </v-col>
                  <v-col cols="12">
                    <p v-if="couponTypeUser.ext_buyer === 'y'">ผู้ซื้อทั่วไป</p>
                    <p v-if="couponTypeUser.purchaser === 'y'">ผู้ซื้อองค์กร</p>
                    <p v-if="couponTypeUser.special_price === 'y'">ร้องขอราคาพิเศษ</p>
                    <p v-if="couponTypeUser.qu === 'y'" class="mb-0">ใบเสนอราคา</p>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      openCouponDetailModal: false,
      couponData: '',
      couponTypeUser: '',
      shopDetail: ''
    }
  },
  watch: {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  created () {
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
  },
  methods: {
    open (data) {
      this.openCouponDetailModal = true
      this.getDetailCoupon(data)
    },
    async getDetailCoupon (data) {
      const sendData = {
        seller_shop_id: this.shopDetail.id,
        couponId: data.couponId
      }
      await this.$store.dispatch('actionDetailShopCoupon', sendData)
      var response = await this.$store.state.ModuleShop.stateDetailShopCoupon
      if (response.message === 'fetch data success') {
        this.couponData = response.data.coupon
        this.couponTypeUser = response.data.typeUse
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    cancel () {
      this.openCouponDetailModal = false
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
