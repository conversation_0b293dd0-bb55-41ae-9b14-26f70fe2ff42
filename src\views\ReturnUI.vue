<template lang="html">
  <div>
    <Return />
  </div>
</template>

<script>
export default {
  components: {
    Return: () => import('@/components/return/return')
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.$EventBus.$emit('changeNav')
    this.init()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/returnMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/return' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async init () {
      // const data = await {
      //   start_date: '2021-01-01',
      //   end_date: '2022-03-23'
      // }
      // await this.$store.dispatch('actionsDashboard', data)
      // var response = await this.$store.state.ModuleShop.stateDashboard
      // // this.chartOptions.yaxis.max = await Math.max.apply(Math, response.data.map(o => o.value))
      // console.log('XCSS', this.chartOptions.yaxis.max)
    }
  }
}
</script>

<style lang="css" scoped>
</style>
