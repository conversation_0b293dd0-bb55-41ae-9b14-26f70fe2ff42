import AxiosOrder from './axios_order_list'

const ModuleOrderList = {
  state: {
    stateOrderList: [],
    stateFindOrderList: [],
    stateEditOrderList: []
  },
  mutations: {
    mutationsOrderList (state, data) {
      state.stateOrderList = data
    },
    mutationsFindOrderList (state, data) {
      state.stateFindOrderList = data
    },
    mutationsEditOrderList (state, data) {
      state.stateEditOrderList = data
    }
  },
  actions: {
    async actionsOrderList (context, access) {
      const dataOrder = await AxiosOrder.GetOrderList(access)
      await context.commit('mutationsOrderList', dataOrder)
    },
    async actionsFindOrderList (context, OrderNumber) {
      const data = { order_number: OrderNumber }
      const OrderData = await AxiosOrder.FindOrderList(data)
      context.commit('mutationsFindOrderList', OrderData)
    },
    async actionsEditOrderList (context, Payload) {
      const data = Payload
      const OrderDataEdit = await AxiosOrder.EditOrderList(data)
      context.commit('mutationsEditOrderList', OrderDataEdit)
    }
  }
}
export default ModuleOrderList
