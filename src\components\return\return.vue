<template>
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รายการคืนสินค้า</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายการคืนสินค้า</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="py-0">
          <a-tabs @change="SelectDetailOrderRefund" class="px-2">
            <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countRefundAll }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countRefundPending }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">อนุมัติ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countRefundApproved }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">ไม่อนุมัติ <a-tag color="#D1392B" style="border-radius: 8px;">{{ countRefundNotAllowed }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-2 pr-3 mb-3' : 'pl-2 pr-2 mb-3'">
          <v-text-field v-model="search" dense hide-details outlined rounded placeholder="ค้นหาจากชื่อผู้ซื้อหรือรหัสการสั่งซื้อ">
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mt-3' : 'pl-2 pr-2 mt-3'">
          <span v-if="StateStatus == 0" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">รายการคืนสินค้าทั้งหมด {{ showCountOrder }} รายการ</span>
          <span v-if="StateStatus == 1" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">รายการคืนสินค้ารออนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
          <span v-if="StateStatus == 2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">รายการคืนสินค้าอนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
          <span v-if="StateStatus == 3" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">รายการคืนสินค้าไม่อนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
        </v-col>
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-2 my-5" min-height="512">
            <v-data-table
              :headers="keyCheckHead == 0 ? headersAll : keyCheckHead == 1 ? headersPending : keyCheckHead == 2 ? headersApproved : keyCheckHead == 3 ? headersNotAllowed : headersAll"
              :items="DataTable"
              :search="search"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              style="width:100%;"
              height="100%"
              @pagination="countOrdar"
              no-results-text="ไม่พบรายการคืนสินค้าที่ค้นหา"
              no-data-text="ไม่มีรายการคืนสินค้าในตาราง"
              :update:items-per-page="getItemPerPage"
              :items-per-page="10"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
              </template>
              <template v-slot:[`item.invoice`]="{ item }">
                <div v-if="item">
                  {{item.required_invoice}}
                </div>
              </template>

              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status_refund === 'approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status_refund === 'waiting'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status_refund === 'reject'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#D1392B">ไม่อนุมัติ</v-chip>
                </span>
              </template>
              <template v-slot:[`item.actions`]="{ item }">
                <v-btn text rounded color="#27AB9C" small @click="goDetailRefund(item.payment_transaction_number,item.status_refund)">
                  <b>รายละเอียด</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="keyCheckHead === 0"><b>คุณยังไม่มีรายการคืนสินค้า</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="keyCheckHead === 1"><b>คุณยังไม่มีรายการคืนสินค้าที่รออนุมัติ</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="keyCheckHead === 2"><b>คุณยังไม่มีรายการคืนสินค้าที่อนุมัติ</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="keyCheckHead === 3"><b>คุณยังไม่มีรายการคืนสินค้าที่ไม่อนุมัติ</b></h2>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      disableTable: false,
      search: '',
      // orderRefund: {
      //   all: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000442',
      //       price: '20000',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'Approved'
      //     },
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000440',
      //       price: '1800',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'pending'
      //     },
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000434',
      //       price: '400',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'notApproved'
      //     }
      //   ],
      //   pending: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000440',
      //       price: '1800',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'pending'
      //     }
      //   ],
      //   approved: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000442',
      //       price: '20000',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'Approved'
      //     }
      //   ],
      //   notApproved: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000434',
      //       price: '400',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'notApproved'
      //     }
      //   ]
      // },
      StateStatus: 0,
      keyCheckHead: 0,
      headersAll: [
        { text: 'วันที่', value: 'created_at', filterable: false, sortable: false, width: '160', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ - นามสกุล ผู้ซื้อ', value: 'user_name', sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ขอใบกำกับภาษี', value: 'invoice', filterable: false, sortable: false, width: '200', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', value: 'status', filterable: false, sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'actions', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersPending: [
        { text: 'วันที่', filterable: false, value: 'created_at', sortable: false, width: '160', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ - นามสกุล ผู้ซื้อ', value: 'user_name', sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ขอใบกำกับภาษี', filterable: false, value: 'invoice', sortable: false, width: '200', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', filterable: false, value: 'status', sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'actions', filterable: false, align: 'start', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersApproved: [
        { text: 'วันที่', filterable: false, value: 'created_at', sortable: false, width: '160', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ - นามสกุล ผู้ซื้อ', value: 'user_name', sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ขอใบกำกับภาษี', filterable: false, value: 'invoice', sortable: false, width: '200', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', filterable: false, value: 'status', sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', filterable: false, value: 'actions', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersNotAllowed: [
        { text: 'วันที่', filterable: false, value: 'created_at', sortable: false, width: '160', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ - นามสกุล ผู้ซื้อ', value: 'user_name', sortable: false, width: '159', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ขอใบกำกับภาษี', filterable: false, value: 'invoice', sortable: false, width: '200', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', filterable: false, value: 'status', sortable: false, width: '150', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', filterable: false, value: 'actions', align: 'start', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      countRefundAll: 0,
      countRefundPending: 0,
      countRefundApproved: 0,
      countRefundNotAllowed: 0,
      showCountOrder: 0,
      pageCount: 5,
      page: 1,
      itemsPerPage: 10,
      DataTable: [],
      dataRole: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    this.ListRefundDataTable()
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/returnMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/return' }).catch(() => {})
      }
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.orderRefund.all !== undefined ? this.orderRefund.all : []
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.orderRefund.waiting !== undefined ? this.orderRefund.waiting : []
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.orderRefund.approve !== undefined ? this.orderRefund.approve : []
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.DataTable = this.orderRefund.reject !== undefined ? this.orderRefund.reject : []
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    },
    SelectDetailOrderRefund (item) {
      this.StateStatus = item
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async ListRefundDataTable () {
      const shopId = localStorage.getItem('shopSellerID')
      var dataSent = {
        seller_shop_id: shopId
      }
      await this.$store.dispatch('actionsListRefundSeller', dataSent)
      var { data = {} } = await this.$store.state.ModuleShop.stateListRefundSeller
      this.orderRefund = data
      this.countRefundAll = data.count_all
      this.countRefundPending = data.count_waiting
      this.countRefundApproved = data.count_approve
      this.countRefundNotAllowed = data.count_reject
      if (this.StateStatus === 0) {
        this.DataTable = this.orderRefund.all
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 1) {
        this.DataTable = this.orderRefund.waiting
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 2) {
        this.DataTable = this.orderRefund.approve
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 3) {
        this.DataTable = this.orderRefund.reject
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    goDetailRefund (number, status) {
      localStorage.setItem('orderNumberReturn', number)
      localStorage.setItem('statusTransaction', status)
      // this.$router.push({ path: '/returnDetail' }).catch(() => {})
      if (this.MobileSize) {
        this.$router.push({ path: '/returnDetailMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/returnDetail' }).catch(() => {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.55rem;
}
</style>
