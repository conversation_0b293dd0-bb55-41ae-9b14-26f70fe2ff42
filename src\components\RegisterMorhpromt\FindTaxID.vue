<template>
  <v-container>
    <!-- Website -->
    <div style="background: #FFFFFF; border: 1px solid #F3F5F7; border-radius: 12px;" v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="6" md="12" align="center" class="my-16">
            <v-form ref="formTax" :lazy-validation="lazy">
              <v-card width="604" height="266" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;" elevation="2">
                <v-card-text class="pb-0">
                  <v-container>
                    <v-row dense justify="center" align-content="center" class="mb-8">
                      <v-btn icon color="#F3F5F7" disabled style="background-color: #F3F5F7;" class="px-7 py-7">
                        <v-img src="@/assets/taxIcon.png" max-height="40" max-width="40"  contain/>
                      </v-btn>
                      <h2 class="pt-4 ml-2 mttaxIcon-2 mb-4" style="font-weight: 700; cursor: pointer;" :style="IpadSize ? 'font-size: 16px;' : 'font-size: 20px;'">ชื่อองค์กรผู้เสียภาษี</h2>
                      <v-spacer style="border-top: 1px solid #E6E6E6; margin-top: 26px; margin-left: 10px; margin-right: 10px;"></v-spacer>
                      <v-btn icon outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;" class="mt-2"><v-icon color="#27AB9C">mdi-file-document-outline</v-icon></v-btn>
                    </v-row>
                    <v-row no-gutters dense class="ml-14 mr-8" justify="center">
                      <v-col cols="12" md="12" sm="12">
                        <!-- <v-text-field v-model="taxID" outlined placeholder="กรอกหมายเลขผู้เสียภาษี" dense maxLength="13" :rules="Rules.tax" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                        <v-select v-model="taxID" :items="dataSelect" item-value="tax_id" dense outlined placeholder="เลือกชื่อองค์กรผู้เสียภาษี">
                          <template v-slot:selection="{ item }">
                            {{ item.name_th }}
                          </template>
                          <template v-slot:item="{ item }">
                            {{ item.name_th }}
                          </template>
                        </v-select>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
                <v-card-actions class="mr-12 pb-2">
                  <v-spacer></v-spacer>
                  <v-btn color="#27AB9C" outlined class="px-5" @click="cancelTaxID()">ยกเลิก</v-btn>
                  <v-btn color="#27AB9C" style="color: white;" @click="checkTaxID()" class="px-6">ยืนยัน</v-btn>
                </v-card-actions>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div style="background: #FFFFFF; border: 1px solid #F3F5F7; border-radius: 12px;" v-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <v-form ref="formTax" :lazy-validation="lazy">
            <v-card width="604" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;" elevation="2">
              <v-card-text class="pb-0">
                <v-container>
                  <v-row dense justify="center" align-content="center" class="mb-8">
                    <v-btn icon color="#F3F5F7" disabled style="background-color: #F3F5F7;" class="px-7 py-7">
                      <v-img src="@/assets/taxIcon.png" max-height="40" max-width="40"  contain/>
                    </v-btn>
                    <h2 class="pt-4 ml-2 mttaxIcon-2 mb-4" style="font-weight: 700; cursor: pointer;" :style="IpadSize ? 'font-size: 16px;' : 'font-size: 20px;'">ชื่อองค์กรผู้เสียภาษี</h2>
                    <v-spacer style="border-top: 1px solid #E6E6E6; margin-top: 26px; margin-left: 10px; margin-right: 10px;"></v-spacer>
                    <v-btn icon outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;" class="mt-2"><v-icon color="#27AB9C">mdi-file-document-outline</v-icon></v-btn>
                  </v-row>
                  <v-row no-gutters dense class="ml-14 mr-8" justify="center">
                    <v-col cols="12" md="12" sm="12">
                      <!-- <v-text-field v-model="taxID" outlined placeholder="กรอกหมายเลขผู้เสียภาษี" dense maxLength="13" :rules="Rules.tax" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                      <v-select v-model="taxID" :items="dataSelect" item-value="tax_id" dense outlined placeholder="เลือกชื่อองค์กรผู้เสียภาษี">
                        <template v-slot:selection="{ item }">
                          {{ item.name_th }}
                        </template>
                        <template v-slot:item="{ item }">
                          {{ item.name_th }}
                        </template>
                      </v-select>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card-text>
              <v-card-actions class="mr-12 pb-4">
                <v-spacer></v-spacer>
                <v-btn color="#27AB9C" outlined class="px-5" @click="cancelTaxID()">ยกเลิก</v-btn>
                <v-btn color="#27AB9C" style="color: white;" @click="checkTaxID()" class="px-6">ยืนยัน</v-btn>
              </v-card-actions>
            </v-card>
          </v-form>
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div  style="background: #FFFFFF; border: 1px solid #F3F5F7; border-radius: 12px;" v-if="MobileSize">
      <v-container class="my-6">
        <v-row dense justify="center" align-content="center">
          <v-col cols="12" md="12">
            <v-form ref="formTax" :lazy-validation="lazy">
              <v-card width="100%" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;" elevation="2">
                <v-card-text class="pb-0">
                  <v-container>
                    <v-row dense justify="center" align-content="center" class="mb-8">
                      <v-btn icon color="#F3F5F7" disabled style="background-color: #F3F5F7;" class="px-7 py-7">
                        <v-img src="@/assets/taxIcon.png" max-height="40" max-width="40"  contain/>
                      </v-btn>
                      <h2 class="pt-4 ml-2 mttaxIcon-2 mb-4" style="font-weight: 700; cursor: pointer;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 20px;'">ชื่อองค์กรผู้เสียภาษี</h2>
                      <v-spacer style="border-top: 1px solid #E6E6E6; margin-top: 26px; margin-left: 10px; margin-right: 10px;"></v-spacer>
                      <v-btn icon outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;" class="mt-2"><v-icon color="#27AB9C">mdi-file-document-outline</v-icon></v-btn>
                    </v-row>
                    <v-row no-gutters dense class="mx-0" justify="center">
                      <v-col cols="12" md="12" sm="12">
                        <!-- <v-text-field v-model="taxID" outlined placeholder="กรอกหมายเลขผู้เสียภาษี" dense maxLength="13" :rules="Rules.tax" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                        <v-select v-model="taxID" :items="dataSelect" item-value="tax_id" dense outlined placeholder="เลือกชื่อองค์กรผู้เสียภาษี">
                          <template v-slot:selection="{ item }">
                            {{ item.name_th }}
                          </template>
                          <template v-slot:item="{ item }">
                            {{ item.name_th }}
                          </template>
                        </v-select>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
                <v-card-actions class="mr-4 pb-4">
                  <v-spacer></v-spacer>
                  <v-btn color="#27AB9C" outlined class="px-5" @click="cancelTaxID()">ยกเลิก</v-btn>
                  <v-btn color="#27AB9C" style="color: white;" @click="checkTaxID()" class="px-6">ยืนยัน</v-btn>
                </v-card-actions>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      dataSelect: [],
      accessToken: '',
      dataFindTax: [],
      data: [],
      taxID: '',
      email: '',
      phone: '',
      Rules: {
        tax: [
          v => !!v || 'กรุณากรอกหมายเลขผู้เสียภาษี',
          v => v.length === 13 || 'กรุณากรอกหมายเลขผู้เสียภาษี 13 หลัก'
        ]
      }
    }
  },
  created () {
    this.dataFindTax = JSON.parse(Decode.decode(localStorage.getItem('AccessToken')))
    // console.log(this.dataFindTax)
    this.accessToken = this.dataFindTax.access_token
    this.getTaxID()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    isActive () {
      return this.otp.length === this.length
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    cancelTaxID () {
      this.$router.push({ path: '/regisMorhpromt' }).catch(() => {})
    },
    async getTaxID () {
      const auth = {
        headers: { Authorization: `Bearer ${this.accessToken}` }
      }
      var ress = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/find_all_tax_user_one`, '', auth)
      this.dataSelect = ress.data.data
      // for (let i = 0; i < this.dataSelect.length; i++) {
      //   this.data.push(this.dataSelect[i].name_th)
      // }
      // console.log('test', this.dataSelect)
    },
    async checkTaxID () {
      // for (let i = 0; i < this.dataSelect.length; i++) {
      //   if (this.dataSelect[i].name_th === this.taxID) {
      //     this.taxID = this.dataSelect[i].tax_id
      //   }
      // }
      if (this.$refs.formTax.validate(true)) {
        this.$store.commit('openLoader')
        const auth = {
          headers: { Authorization: `Bearer ${this.accessToken}` }
        }
        var data = {
          tax_id: this.taxID
        }
        var res = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/business/find_tax_id_by_one_id`, data, auth)
        // console.log(res.data)
        if (res.data.result === 'SUCCESS') {
          // console.log(res.data.data)
          this.$store.commit('closeLoader')
          localStorage.setItem('BussinessData', Encode.encode(res.data.data))
          // this.$router.push({ path: '/Registerbuyer' }).catch(() => {})
          this.$router.push({ path: '/ListRegister' }).catch(() => {})
        } else {
          if (res.data.message === 'Not Found Business Account') {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', title: 'ไม่พบบัญชีผู้ใช้งานนี้ในบัญชีนิติบุคคล', showConfirmButton: false, timer: 1500 })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', title: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
          }
        }
      }
    }
  }
}
</script>

<style scoped>
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
