import AxiosUser from './axios_manage_flashsale_api'

const ModuleManageFlashSale = {
  state: {
    // flashSale
    stateCreateFlashSale: [],
    stateGetFlashSale: [],
    stateGetListProductDetail: [],
    stateDeleteFlashSale: [],
    stateUpdateFlashSale: [],
    stateListAllShopProductFlashSale: []
  },
  mutations: {
    // flashSale
    mutationsCreateFlashSale (state, data) {
      state.stateCreateFlashSale = data
    },
    mutationsGetFlashSale (state, data) {
      state.stateGetFlashSale = data
    },
    mutationsGetListProductDetail (state, data) {
      state.stateGetListProductDetail = data
    },
    mutationsDeleteFlashSale (state, data) {
      state.stateDeleteFlashSale = data
    },
    mutationsUpdateFlashSale (state, data) {
      state.stateUpdateFlashSale = data
    },
    mutationsListAllShopProductFlashSale (state, data) {
      state.stateListAllShopProductFlashSale = data
    }
  },
  actions: {
    // flashSale
    async actionsCreateFlashSale (context, access) {
      const responseData = await AxiosUser.CreateFlashSale(access)
      // console.log('response', responseData)
      await context.commit('mutationsCreateFlashSale', responseData)
    },
    async actionsGetFlashSale (context, access) {
      const responseData = await AxiosUser.GetFlashSale(access)
      // console.log('response', responseData)
      await context.commit('mutationsGetFlashSale', responseData)
    },
    async actionsGetListProductDetail (context, access) {
      const responseData = await AxiosUser.GetListProductDetail(access)
      // console.log('response', responseData)
      await context.commit('mutationsGetListProductDetail', responseData)
      // await context.commit('mutationsGetFlashSale', responseData)
    },
    async actionsDeleteFlashSale (context, access) {
      const responseData = await AxiosUser.DeleteFlashSale(access)
      await context.commit('mutationsDeleteFlashSale', responseData)
    },
    async actionsUpdateFlashSale (context, access) {
      const responseData = await AxiosUser.UpdateFlashSale(access)
      await context.commit('mutationsUpdateFlashSale', responseData)
    },
    async actionsListAllShopProductFlashSale (context, access) {
      const responseData = await AxiosUser.ListAllShopProductFlashSale(access)
      await context.commit('mutationsListAllShopProductFlashSale', responseData)
    }
  }
}
export default ModuleManageFlashSale
