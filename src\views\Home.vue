<template>
  <v-app class="backgroundPage">
    <AppBar style="position: fixed; z-index: 1; width: 100%"/>
    <v-main style="margin-top: 5%; overflow: auto; z-index: 0">
      <router-view v-on:scroll="closeModal"/>
      <!-- <v-speed-dial
        v-model="fab"
        bottom
        right
        fixed
        direction="top"
        transition="slide-y-reverse-transition"
      >
        <template v-slot:activator>
          <v-btn
            v-model="fab"
            color="blue darken-2"
            dark
            fab
          >
            <v-icon v-if="fab">
              mdi-close
            </v-icon>
            <v-icon v-else>
              mdi-message-processing
            </v-icon>
          </v-btn>
        </template>
        <v-row justify="start" style="margin-right: 230px;"> -->
          <!-- <div id="botChat" style="border-top-left-radius: 10px; border-top-right-radius: 10px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; float: right; z-index: 16000004; left: 10px; bottom: 0px; display: block; width: 280px; height: 381px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;">
            <div id="botTitleBar" style="z-index: 16000005; height: 27px; width: 280px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;">
              <div class="chatBigger" style="position: absolute; right: 45px; top:10px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; color:white; "><i class="far fa-window-maximize chatBigger"></i></div>
              <div class='chatright' style='position: absolute; right: 75px; top:10px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; color:white; '><i class='fa fa-arrow-circle-left chatright'></i></div>
              <div class='minBotChatTitle2' style='position: absolute; right: 15px; top:10px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; color:white; '><i class='fas fa-window-minimize minBotChatTitle2'></i></div>
            </div>
            <iframe frameborder="0" style="background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;" allow="geolocation" src="https://chat.one.th:8096/layout/gAAAAABgc80LkVWxv0pI4dxFhTaBDWHeYPiul6e1UEc9vCuakaxCUo7oZUP3vjPhRE-eDMnOEhBpvbmm9qzmP9mlqlXfnbS2Ka9atGuDYdc5XGxIkRSX_UM=/A8b3751e4de785937b0560bf89c1ee40bcbc10f64ced94ceb8c4e19d10db5f00e80df931d70244892a826fb86a5ebef8d"></iframe>
          </div>
          <div id="minBotChat" style="border-radius: 10px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; position: fixed; z-index: 16000002; width: 180px; height: 30px; right: 15px; bottom: 15px; display: block;">
            <div id="minBotChatTitle" style="z-index: 16000003;height: 30px; width: 180px; position:fixed; cursor: pointer;"></div>
            <iframe frameborder="0" style="background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;" src="https://chat.one.th:8096/minchat/Oneemail/A8b3751e4de785937b0560bf89c1ee40bcbc10f64ced94ceb8c4e19d10db5f00e80df931d70244892a826fb86a5ebef8d" allow="geolocation"></iframe>
          </div> -->
        <!-- </v-row>
      </v-speed-dial> -->
    </v-main>
    <Footer v-if="path === true"/>
    <div v-html="getChat()" v-if="onechatToken !== ''"></div>
  </v-app>
</template>

<script>
import { Decode } from '@/services'
export default {
  name: 'Home',
  data: () => ({
    path: false,
    fab: false,
    dialogInside: false,
    chat: '',
    showChat: false,
    chatHTML: '',
    onechatToken: '',
    onedata: []
  }),
  components: {
    AppBar: () => import('@/components/Home/AppBar'),
    Footer: () => import('@/components/Home/Footer')
  },
  created () {
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user.one_chat_token !== undefined) {
        this.onechatToken = oneData.user.one_chat_token
        this.getChat()
      } else {
        this.onechatToken = ''
      }
      // console.log('oneData===>', oneData.user)
      if (oneData.user === undefined) {
        localStorage.removeItem('oneData')
      }
    }
    window.addEventListener('scroll', this.closeModal)
    this.CheckPathFooter()
  },
  mounted () {
    this.$EventBus.$on('CheckFooter', this.CheckPathFooter)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('CheckFooter')
    })
  },
  destroyed () {
    // console.log('destroyed')
    window.removeEventListener('scroll', this.closeModal)
  },
  methods: {
    closeModal () {
      this.$EventBus.$emit('closeModalLogin')
      this.$EventBus.$emit('closeModalRegister')
      this.$EventBus.$emit('closeModalSuccess')
      this.$EventBus.$emit('closeModalCartNoLogin')
      this.$EventBus.$emit('closeModalCart')
    },
    async CheckPathFooter () {
      var currentRoutepath = this.$router.currentRoute.path
      if (currentRoutepath === '/shop' || currentRoutepath === '/poseller' || currentRoutepath === '/manageproduct' || currentRoutepath === '/orderdetailseller' || currentRoutepath === '/seller') {
        this.path = false
      } else {
        this.path = true
      }
    },
    closeChat () {
      this.showChat = !this.showChat
    },
    getChat () {
      // this.chatHTML = "<div id='botChat' style='border-top-left-radius: 10px; border-top-right-radius: 10px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 10px; bottom: 0px;display: block; width: 280px; height: 380px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;' :style='showChat === true ? 'display: block' : 'display: none''><div id='botTitleBar' style='z-index: 16000005;height: 27px; width: 360px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'></div><iframe frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;' allow='geolocation' src='https://chat.one.th:8096/login_service/E-Procurement/Ad631277f26ad58df9b1380cbd99d53f854a6f4b780d04b519c5d630f7782736ba0ab7687e1294a68851b7ec98eb915eb?onechat_token=" + this.onechatToken + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 180px; height: 30px; right: 10px; bottom: 15px; display: block;' @click='showChat = !showChat'><div id='minBotChatTitle' style='z-index: 16000003;height: 30px; width: 180px; position:fixed; cursor: pointer;'></div><iframe frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;' src='https://chat.one.th:8096/minchat/E-Procurement/Ad631277f26ad58df9b1380cbd99d53f854a6f4b780d04b519c5d630f7782736ba0ab7687e1294a68851b7ec98eb915eb' allow='geolocation'></iframe></div>"
      // console.log(this.chatHTML)
      var divChatbot = document.createElement('div')
      document.getElementsByTagName('body')[0].appendChild(divChatbot)
      divChatbot.outerHTML = "<div id='botChat' style='border-top-left-radius: 10px; border-top-right-radius: 10px; position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 10px; bottom: 0px;display: none; width: 280px; height: 380px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 27px; width: 280px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'><div class='chatBigger' style='position: absolute; right: 45px; top:10px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; color:white; '><i class='far fa-window-maximize chatBigger'></i></div><div class='chatright' style='position: absolute; right: 75px; top:10px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; color:white; '><i class='fa fa-arrow-circle-left chatright'></i></div><div class='minBotChatTitle2' style='position: absolute; right: 15px; top:10px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; color:white; '><i id='minWindow' class='fas fa-window-minimize minBotChatTitle2'></i></div></div><iframe frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;' allow='geolocation' src='https://chat.one.th:8096/login_service/E-Procurement/Ad631277f26ad58df9b1380cbd99d53f854a6f4b780d04b519c5d630f7782736ba0ab7687e1294a68851b7ec98eb915eb?onechat_token=" + this.onechatToken + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 180px; height: 30px; right: 10px; bottom: 15px;'><div id='minBotChatTitle' style='z-index: 16000003;height: 30px; width: 180px; position:fixed; cursor: pointer;'></div><iframe frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;' src='https://chat.one.th:8096/minchat/E-Procurement/Ad631277f26ad58df9b1380cbd99d53f854a6f4b780d04b519c5d630f7782736ba0ab7687e1294a68851b7ec98eb915eb' allow='geolocation'></iframe></div>"
      document.querySelector('body').addEventListener('click', function (e) {
        e.target.matches = e.target.matches || e.target.msMatchesSelector
        if (e.target.matches('#minWindow')) {
          document.getElementById('botChat').style.display = 'none'
          document.getElementById('minBotChat').style.display = 'block'
        } else if (e.target.matches('#minBotChatTitle')) {
          document.getElementById('botChat').style.display = 'block'
          document.getElementById('minBotChat').style.display = 'none'
        }
      })
    }
  }
}
</script>

<style scoped>
.backgroundPage{
  background-color: rgb(245, 245, 245)
}
</style>
