<template>
  <v-container :class="MobileSize ? 'pa-0' : ''" v-if="typeProduct !== 'article'">
    <v-row dense>
      <h2 class="pt-1 ml-2 mt-2 mb-4" :style="MobileSize? 'font-size: 17px; font-weight: 900; color: #27AB9C !important;': 'font-size: 24px; font-weight: 900; color: #27AB9C !important;'">{{ header }}</h2>
      <v-spacer :style="MobileSize ? 'border-top: 1px solid #DAF1E9; margin-top: 24px; margin-left: 15px;': 'border-top: 1px solid #DAF1E9; margin-top: 31px; margin-left: 15px;'"></v-spacer>
        <v-btn plain  text @click="GetAllProduct(propsData)" color="#27AB9C" :class="MobileSize? 'mt-1': 'mt-3'" :style="MobileSize? 'font-size: 17px; font-weight: 400; text-transform: none;': 'font-size: 16px; font-weight: 400; text-transform: none;'">{{ $t('ShopPage.ViewAll') }} <v-icon>mdi-arrow-right-circle-outline</v-icon></v-btn>
    </v-row>
    <div v-if="isCheck === true">
      <v-row dense v-if="MobileSize || IpadSize" :class="MobileSize ? 'px-0' : 'px-0'">
        <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" xs="6" v-for="(item, index) in cleanData" :key="index" :class="MobileSize? 'px-0 mb-2':'mb-4'" style="display: flex; justify-content: center;">
          <CardProductsMobile :itemProduct='item' />
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col cols="2" :md="IpadProSize ? '3' : '2'" sm="2" xs="2" v-for="(item, index) in cleanData" :key="index" class="d-flex justify-center">
          <CardProducts :itemProduct='item' />
        </v-col>
      </v-row>
    </div>
    <div v-else>
      <v-row dense v-if="MobileSize || IpadSize" :class="MobileSize ? 'px-0' : 'px-0'">
        <v-col cols="6" :md="IpadProSize ? '3' : '2'" sm="3" v-for="(item, index) in 6" :key="index" :class="MobileSize? 'px-0 mb-2':'mb-4'" style="display: flex; justify-content: center;">
          <v-skeleton-loader
            class="mx-auto"
            max-width="175"
            type="image, article"
          >
          </v-skeleton-loader>
        </v-col>
      </v-row>
    </div>
  </v-container>
  <v-container :class="MobileSize ? 'pa-0' : ''" v-else-if="typeProduct === 'article' && isDataReady">
    <v-row>
      <h2 class="pt-1 ml-2 mt-2 mb-4" :style="MobileSize? 'font-size: 17px; font-weight: 900; color: #27AB9C !important;': 'font-size: 24px; font-weight: 900; color: #27AB9C !important;'">{{ header }}</h2>
      <v-spacer :style="MobileSize ? 'border-top: 1px solid #DAF1E9; margin-top: 24px; margin-left: 15px;': 'border-top: 1px solid #DAF1E9; margin-top: 31px; margin-left: 15px;'"></v-spacer>
        <v-btn plain  text @click="GetAllProduct(propsData)" color="#27AB9C" :class="MobileSize? 'mt-1': 'mt-3'" :style="MobileSize? 'font-size: 17px; font-weight: 400; text-transform: none;': 'font-size: 16px; font-weight: 400; text-transform: none;'">{{ $t('ShopPage.ViewAll') }} <v-icon>mdi-arrow-right-circle-outline</v-icon></v-btn>
    </v-row>
    <vue-horizontal-list :class="MobileSize || IpadSize ? 'mx-4' : 'mx-10'"  :items='propsData' :options='optionsArticle'>
      <template v-slot:nav-prev>
        <div><v-icon color="#008E00" size="32">mdi-chevron-left</v-icon></div>
      </template>
      <template v-slot:nav-next>
        <div><v-icon color="#008E00" size="32">mdi-chevron-right</v-icon></div>
      </template>
      <template v-slot:default="{ item }">
        <v-col style="display: flex; height: 100%;">
        <component v-if="isDataReady" :is="MobileSize || IpadSize ? 'CardProductsMobile' : 'CardProducts'" :itemProduct="item"/>
      </v-col>
      </template>
    </vue-horizontal-list>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import VueHorizontalList from 'vue-horizontal-list'
export default {
  props: ['propsData', 'header', 'typeProduct', 'check', 'isCheck'],
  components: {
    VueHorizontalList,
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsMobile: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      options: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        }
      },
      optionsArticle: {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 3 },
          { start: 768, end: 992, size: 4 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        }
      },
      loading: true,
      cleandata: []
    }
  },
  created () {
    // this.cleanData()
  },
  computed: {
    cleanData () {
      var array1 = this.propsData
      var lengthData = this.propsData.length
      var i
      var cleandata = []
      if (lengthData < 12) {
        for (i = 0; i < lengthData; i++) {
          cleandata.push(array1[i])
        }
      } else {
        for (i = 0; i < 12; i++) {
          cleandata.push(array1[i])
        }
      }
      return cleandata
    },
    isDataReady () {
      return this.propsData && this.propsData.length > 0
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    GetAllProduct (allItem) {
      localStorage.setItem('itemShop', Encode.encode(allItem))
      if (this.typeProduct === 'article') {
        this.$router.push(`/ListShopProduct/${this.typeProduct}?Article=${this.$route.query.Article}&Name${this.$route.query.Name}&ID=${this.$route.query.ID}&page=1`).catch(() => {})
      } else {
        this.$router.push(`/ListShopProduct/${this.typeProduct}?page=1`).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>

</style>
