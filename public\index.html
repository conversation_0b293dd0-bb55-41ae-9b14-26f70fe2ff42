<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta http-equiv="Content-Security-Policy" content="script-src 'self' https://static.cloudflareinsights.com;"> -->
    <!-- <meta name="viewport" content="width=device-width,initial-scale=1.0"> -->
	<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name=keywords content="NexGenCommerce, inet, nexgencommerce.one.th, nex, Nex, gen, Gen, Commerce, INET, ตลาดออนไลน์, NexGenCommerce, nexgencommerce, ngc, NGC, NexGen Commerce, NexGen, ngc.one.th, สินค้าไทย, สินค้าคุณภาพ, ร้านค้าน่าเชื่อถือ, ค่าส่งถูก, สินค้าและบริการ, B2B2C, ออก E-tax invoice ได้, GP ต่ำ, แหล่งเงินทุนสำหรับผู้ประกอบการ, Digital Transformation, Affiliate">
    <meta name=author content="NexGenCommerce">
    <meta name="robots" content="index,follow">
    <meta name=description content="Nex Gen Commerce ซื้อขายออนไลน์ สินค้าและบริการ B2B2C เพิ่มโอกาสทางการขาย ต่อยอดธุรกิจ ส่งเสริมธุรกิจไทย ออก e-Tax invoice 'GP ต่ำ' ค่าส่งถูก Affiliate">
    <meta name="apple-itunes-app" content="app-id=com.inet.nexgenshop">
    <link rel="shortcut icon" href=/ngc_Logo.ico>
	<link rel='shortcut icon' href="<%= VUE_APP_DOMAIN %>ngc_Logo.ico">
	<link rel='icon' type="image/x-icon" href="<%= VUE_APP_DOMAIN %>ngc_Logo.ico">
	<link rel='apple-touch-icon' href="<%= VUE_APP_DOMAIN %>ngc_Logo.ico">
	<!-- <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet"> -->

	<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<meta name="google-site-verification" content="Xt6YUq6cU4aMdCq-H8WRGOn-3Yb0R04x2Zwp74TMOto" />
    <!-- <title>INET-Marketplace</title> -->
		<title>
			<%= htmlWebpackPlugin.options.title %>
	  </title>
    <!-- <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet"> -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/@mdi/font@5.x/css/materialdesignicons.min.css" rel="stylesheet"> -->
    <!-- <script type="text/javascript" src="https://unpkg.com/xlsx@0.15.1/dist/xlsx.full.min.js"></script> -->
    <!-- <?php

    	$URL = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    	if ( strpos( $URL, "DetailProduct" ) >= 0 ){

    		$G_DB_HOST = getenv("MYSQL_HOSTNAME");
    		$G_DB_NAME = getenv("MYSQL_DBNAME");
    		$G_DB_USER = getenv("MYSQL_DBUSERNAME");
    		$G_DB_PASS = getenv("MYSQL_DBPASSWORD");
    		$G_httphost_path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]" . "/backend";
    		$G_DATABASE_CONN = new mysqli($G_DB_HOST, $G_DB_USER, $G_DB_PASS, $G_DB_NAME);
    		if ($G_DATABASE_CONN->connect_errno){
    			if (!file_exists('php__log__for__vuejs')) {     mkdir('php__log__for__vuejs', 0755, true); }
    			file_put_contents("php__log__for__vuejs/dblog", $G_DATABASE_CONN->connect_errno . " " . $G_DATABASE_CONN->connect_error . PHP_EOL, FILE_APPEND | LOCK_EX);
    		}else{

    			$G_DATABASE_CONN->set_charset('utf8mb4_unicode_ci');

    			if ( strpos( $URL, "DetailProduct" ) !== false ){
    				$exploded_URL = explode("-", $URL);

    				$iidd = $exploded_URL[ count($exploded_URL) - 1 ];
    				if (is_numeric($iidd)){

    					$stmt = $G_DATABASE_CONN->prepare("SELECT msp.name, msp.short_description, iv.media_path FROM ms_product msp LEFT JOIN ms_product_image_vdo iv ON msp.id = iv.product_id WHERE msp.id = ? AND iv.media_type = 'image'");

    					if (!$stmt){
    						return;
    					}

    					$stmt->bind_param("i", $iidd);
    					$stmt->execute();
    					$stmt->bind_result($name, $short_desc, $media_path);

    					if ( $stmt->fetch() ){
    						echo '<meta property="og:url" content="' . $URL . '">';
    						echo '<meta property="og:type" content="website">';
    						echo '<meta property="og:title" content="' . $name . '">';

    						if ( isset($media_path)){
    							//$exploded_media_path = explode(",",$media_path);
    							//if (isset($exploded_media_path[0]))
    							//echo '<meta property="og:image" content="' . $G_httphost_path . $exploded_media_path[0] . '">';
                   //$G_httphost_path = "https://panit.sdi.inet.co.th/backend";
                   echo '<meta property="og:image" content="' . $G_httphost_path . $media_path . '">';
                   echo '<meta property="og:image:url" content="' . $G_httphost_path . $media_path . '">';
                   echo '<meta property="og:image:secure_url" content="' . $G_httphost_path . $media_path . '">';
    						}
    						echo '<meta property="og:description" content="' . $short_desc . '">';
    					}
    				}
    			}
    		}
    	}
    ?> -->
    <!-- <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.3.1/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.3.1/js/buttons.html5.min.js"></script> -->
  </head>
  <body>
		<!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/5.9.55/css/materialdesignicons.min.css" rel="stylesheet"> -->
		<!-- Google tag (gtag.js) -->
		<script src="https://cdn.jsdelivr.net/npm/centrifuge@2/dist/centrifuge.min.js"></script>
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-7NQ65YQ2S2"></script>
		<script>
		  window.dataLayer = window.dataLayer || [];
		  function gtag(){dataLayer.push(arguments);}
		  gtag('js', new Date());

		  gtag('config', 'G-7NQ65YQ2S2');
		</script>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@5.x/css/materialdesignicons.min.css" rel="stylesheet" rel="preload" as="style">
  </body>
</html>
<script>
</script>

