<template>
  <v-container style="min-height:650px" :class="MobileSize ? 'mt-2' : ''">
    <v-card width="100%" height="100%" elevation="0">
      <v-row dense class="mb-4">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-5' : 'px-0'">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">ตั้งค่าใบเสนอราคา</v-card-title>
          </v-row>
          <v-row v-else>
            <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> ตั้งค่าใบเสนอราคา</v-card-title>
          </v-row>
        </v-col>
        <v-col cols="12" class="pb-10" :class="MobileSize ? 'px-5' : 'px-3'">
          <v-row dense class="pb-0">
            <v-col :class="MobileSize|| IpadProSize || IpadSize ? 'pb-0' : ''" class="px-2 py-0">
              <a-tabs @change="getQuotationTemplateListReturn" class="">
                <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countNumberCodePatternAll }}</a-tag></span></a-tab-pane>
                <a-tab-pane :key="1"><span slot="tab">กำลังใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{ countNumberCodePatternActive }}</a-tag></span></a-tab-pane>
                <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countNumberCodePatternInactive }}</a-tag></span></a-tab-pane>
              </a-tabs>
            </v-col>
          </v-row>
          <v-container :class="MobileSize|| IpadProSize || IpadSize ? 'px-0' : 'mt-0 px-0'">
            <v-row no-gutters class="px-0">
              <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 6" align="start" class="pb-0">
                <v-text-field :style="IpadSize ? '' : 'width:400px'" v-model="search" append-icon="mdi-magnify" placeholder="ค้นหาจากชื่อรูปแบบ" v-if="disableTable === false" outlined dense hide-details></v-text-field><br/>
              </v-col>
              <v-col :cols="MobileSize? 12 : IpadSize ? 12 : 6" align="end" class="pt-0" v-if="StateStatus == 0">
                <v-btn :block="MobileSize" color="#27AB9C" dark @click="addQuotationTemplate()"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
            </v-row>
            <v-row class="mb-2" v-if="disableTable === false">
              <v-col cols="12" class="mt-0 pt-0">
                <span v-if="StateStatus == 0" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการใบเสนอราคาทั้งหมด {{ showCountOrder }} รายการ</span>
                <span v-if="StateStatus == 1" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการใบเสนอราคากำลังใช้งานทั้งหมด {{ showCountOrder }} รายการ</span>
                <span v-if="StateStatus == 2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการใบเสนอราคายกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
              </v-col>
            </v-row>
            <v-data-table v-if="disableTable === false" v-model="selected" :page.sync="page" :headers="headers" :items="DataTable" @pagination="countCompany" :search="search" :footer-props="{'items-per-page-text':'จำนวนแถว'}" item-key="referance_id" color="blue" class="elevation-1 px-0" no-results-text="ไม่พบรูปแบบที่ค้นหา">
              <template v-slot:[`item.code_pattern`]="{ item }">
                {{ item.code_pattern !== null ? item.code_pattern : '-' }}
              </template>
              <template v-slot:[`item.number_pattern`]="{ item }">
                {{ item.number_pattern !== null ? item.number_pattern : '-' }}
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.updated_at`]="{ item }">
                {{new Date(item.updated_at).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'active'">
                  <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                </span>
                <span v-else-if="item.status === 'inactive'">
                  <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
                </span>
              </template>
              <template v-slot:[`item.manage`]="{ item }">
                <v-btn text rounded color="#27AB9C" small @click="viewDetail(item)">
                  <b>รายละเอียด</b><v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
            <v-row justify="center" align-content="center" v-if="this.disableTable === true">
              <v-col cols="12" md="12" align="center" style="min-height: 636px;">
                <div style="padding-top: 90px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการใบเสนอราคา</span><br/>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการใบเสนอราคาที่กำลังใช้งาน</span><br/>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการใบเสนอราคาที่ยกเลิก</span><br/>
                </h2>
              </v-col>
            </v-row>
          </v-container>
        </v-col>
      </v-row>
      <ModalSettingQuotation ref="ModalSettingQuotation" />
      <ModalSettingDetailQuotation ref="ModalSettingDetailQuotation" />
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    ModalSettingQuotation: () => import('@/components/Shop/QuotationSettingSeller/QuotationSettingModal'),
    ModalSettingDetailQuotation: () => import('@/components/Shop/QuotationSettingSeller/QuotationSettingDetailModal')
  },
  data () {
    return {
      DataTable: [],
      search: '',
      selected: [],
      page: 1,
      StateStatus: 0,
      countNumberCodePatternAll: 0,
      countNumberCodePatternActive: 0,
      countNumberCodePatternInactive: 0,
      showCountOrder: 0,
      openDialog: false,
      disableTable: false,
      openTier: false,
      headers: [
        { text: 'รูปแบบ', value: 'code_pattern', width: '185', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนที่ถูกสร้าง', filterable: false, value: 'number_pattern', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สร้าง', filterable: false, value: 'created_at', sortable: false, width: '180', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่แก้ไขล่าสุด', filterable: false, value: 'updated_at', sortable: false, width: '180', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', filterable: false, value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', filterable: false, value: 'manage', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      QUcodePatternList: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.$EventBus.$on('settingQUSuccess', this.getData)
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/QuotationSettingSellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/QuotationSettingSeller' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.getQuotationTemplateListReturn(0)
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('settingQUSuccess')
  },
  methods: {
    countCompany (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    getData () {
      this.getQuotationTemplateListReturn(this.StateStatus)
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async getQuotationTemplateListReturn (item) {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      // console.log('shopId', typeof shopId)
      const data = { seller_shop_id: shopId }
      await this.$store.dispatch('actionsSettingQuotationList', data)
      const res = await this.$store.state.ModuleSettingQuotation.stateSettingQuotationList
      // console.log('res', res)
      if (res.message === 'List QU Setting success.') {
        this.$store.commit('closeLoader')
        this.page = 1
        this.StateStatus = item
        this.QUcodePatternList = res.data
        this.countNumberCodePatternAll = this.QUcodePatternList.all.length
        this.countNumberCodePatternActive = this.QUcodePatternList.active.length
        this.countNumberCodePatternInactive = this.QUcodePatternList.inactive.length
        if (this.StateStatus === 0) {
          this.DataTable = this.QUcodePatternList.all
        } else if (this.StateStatus === 1) {
          this.DataTable = this.QUcodePatternList.active
        } else if (this.StateStatus === 2) {
          this.DataTable = this.QUcodePatternList.inactive
        }
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    },
    inactive () {
      this.openDialog = !this.openDialog
    },
    save () {
      this.openDialog = !this.openDialog
      // console.log('save data send api')
    },
    addQuotationTemplate () {
      var item = ''
      this.$refs.ModalSettingQuotation.open(item, 'create', 'main')
    },
    viewDetail (item) {
      // console.log('viewDetail', item)
      this.$refs.ModalSettingDetailQuotation.open(item)
    }
  }
}
</script>
<style lang="scss" scoped>
  // ::v-deep table {
  //   tbody {
  //     tr {
  //       td:nth-child(6) {
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //         z-index: 10;
  //         background: white;
  //       }
  //     }
  //   }
  //   thead {
  //     tr {
  //       th:nth-child(1) {
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //         z-index: 10;
  //         background: white;
  //       }
  //     }
  //   }
  //   thead {
  //     tr {
  //       th:nth-child(6) {
  //         z-index: 11;
  //         background: white;
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //       }
  //     }
  //   }
  // }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
