<template>
  <v-container class="pa-4">
    <v-card :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']" elevation="0">
      <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
        ตั้งค่าบัญชีโซเชียลมีเดีย
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        ตั้งค่าบัญชีโซเชียลมีเดีย
      </v-card-title>

      <v-divider class="my-4"></v-divider>

      <v-form ref="FormbuyerDetailSocail" :lazy-validation="lazy" v-model="formValid">
          <v-row>
            <v-col cols="12" class="mt-4 reduce-spacing">
              <span>Facebook</span>
              <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Facebook" v-model="facebook_link" outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>TikTok</span>
              <v-text-field class="input-text" placeholder="ระบุลิ้งค์ TikTok" v-model="tiktok_link" outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>Youtube</span>
              <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Youtube" v-model="youtube_link" outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>Instagram</span>
              <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Instagram" v-model="instagram_link" outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>Line</span>
              <v-text-field class="input-text" placeholder="ระบุลิ้งค์ Line" v-model="line_link" outlined dense></v-text-field>
            </v-col>
          </v-row>
        </v-form>

        <v-card-actions style="padding-top: 20px;">
          <v-btn class="px-5" outlined color="#27AB9C" @click="beforeDetailPay()">ย้อนกลับ</v-btn>
          <v-spacer></v-spacer>
          <v-btn class="px-5 white--text" color="#27AB9C" @click="nextConfirmDetail()">ถัดไป</v-btn>
        </v-card-actions>

  </v-card>
</v-container>
</template>

<script>
export default {
  data () {
    return {
      detail: this.$route.params.detail,
      lazy: false,
      formValid: false,
      facebook_link: '',
      tiktok_link: '',
      youtube_link: '',
      instagram_link: '',
      line_link: ''
    }
  },
  created () {
    const previousDataPay = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[0]
    if (previousDataPay) {
      this.account_type = previousDataPay.account_type || ''
      this.bank_username = previousDataPay.bank_username || ''
      this.bank_name = previousDataPay.bank_name || ''
      this.bank_branch = previousDataPay.bank_branch || ''
      this.bank_no = previousDataPay.bank_no || ''
      this.bookbankImage = previousDataPay.bookbank_image || ''
      this.bookbankImageUrl = previousDataPay.bookbankImageUrl || ''
    }

    // เก็บข้อมูลล่าสุดไว้ใน array
    const latestDataArrayPay = []
    latestDataArrayPay.push({
      account_type: this.account_type,
      bank_username: this.bank_username,
      bank_name: this.bank_name,
      bank_branch: this.bank_branch,
      bank_no: this.bank_no,
      bookbankImage: this.bookbankImage,
      bookbankImageUrl: this.bookbankImageUrl
    })

    const previousDataSocail = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[1]
    if (previousDataSocail) {
      this.facebook_link = previousDataSocail.facebook_link || ''
      this.tiktok_link = previousDataSocail.tiktok_link || ''
      this.youtube_link = previousDataSocail.youtube_link || ''
      this.instagram_link = previousDataSocail.instagram_link || ''
      this.line_link = previousDataSocail.line_link || ''
    }

    // เก็บข้อมูลล่าสุดไว้ใน array
    const latestDataArraySocail = []
    latestDataArraySocail.push({
      facebook_link: this.facebook_link,
      tiktok_link: this.tiktok_link,
      youtube_link: this.youtube_link,
      instagram_link: this.instagram_link,
      line_link: this.line_link
    })
  },
  mounted () {
    if (this.detail) {
      this.facebook_link = this.detail.facebook_link || ''
      this.tiktok_link = this.detail.tiktok_link || ''
      this.youtube_link = this.detail.youtube_link || ''
      this.instagram_link = this.detail.instagram_link || ''
      this.line_link = this.detail.line_link || ''
    }
  },
  computed: {
    MobileSize () {
      return this.$vuetify.breakpoint.xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    beforeDetailPay () {
      this.$router.push({ name: 'buyerDetail' })
      var dataDetail = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay
      var object = {
        ...dataDetail[0],
        ...dataDetail[1]
      }
      this.object = object
    },
    nextConfirmDetail () {
      if (!this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[1]) {
        // ถ้าว่าง เพิ่มข้อมูลใหม่ลงในดัชนี 1
        this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[1] = {
          facebook_link: this.facebook_link,
          tiktok_link: this.tiktok_link,
          youtube_link: this.youtube_link,
          instagram_link: this.instagram_link,
          line_link: this.line_link
        }
      } else {
        // ถ้าไม่ว่าง ควบรวมข้อมูลใหม่กับข้อมูลที่มีอยู่ที่ดัชนี 1
        this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[1] = {
          ...this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[1],
          facebook_link: this.facebook_link,
          tiktok_link: this.tiktok_link,
          youtube_link: this.youtube_link,
          instagram_link: this.instagram_link,
          line_link: this.line_link
        }
      }

      var dataDetail = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay
      var object = {
        ...dataDetail[0],
        ...dataDetail[1]
      }

      this.object = object
      this.$router.push({ name: 'confirmDetail' })
    }
  }
}
</script>
