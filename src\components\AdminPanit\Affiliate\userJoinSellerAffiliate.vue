<template>
    <v-container :class="MobileSize ? 'mt-3' : ''">
        <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
        <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ผู้สมัครเข้าร่วมร้านค้า affiliate</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ผู้สมัครเข้าร่วมร้านค้า affiliate</v-card-title>
        <v-card-text>
            <v-row dense>
            <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
                <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าที่เข้าร่วม affiliate" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="12">
                <v-row>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                    <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อร้านค้าที่เข้าร่วมทั้งหมด {{ tableData.length }} รายการ</span>
                    <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายชื่อร้านค้าที่เข้าร่วมทั้งหมด {{ tableData.length }} รายการ</span>
                </v-col>
                </v-row>
                <v-row dense>
                <v-col cols="12">
                    <v-data-table
                      :headers="header"
                      :items="tableData"
                      :search="search"
                      class="elevation-1 mt-4"
                      :items-per-page="10"
                      style="width:100%;"
                      height="100%">
                      <template v-slot:[`item.indexTable`]="{index}">
                          {{ index + 1 }}
                      </template>
                      <template v-slot:[`item.path_logo`]="{item}">
                        <v-col>
                          <v-avatar v-if="item.path_logo === null" rounded size="44" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                          </v-avatar>
                          <v-avatar v-else rounded size="44" color="#FFF">
                            <v-img contain :src="item.path_logo"></v-img>
                          </v-avatar>
                        </v-col>
                      </template>
                        <template v-slot:[`item.status_shop`]="{item}">
                          <v-chip :color="item.status_shop === 'yes' ? '#f0f9ee' : 'rgb(247, 217, 217)'" :text-color="item.status_shop === 'yes' ? 'green' : 'red'">
                            {{ statusShop(item.status_shop)}}
                          </v-chip>
                        </template>
                        <template v-slot:[`item.seller_shop_id`]="{item}">
                            <v-btn :disabled="item.user_count === 0" outlined color="#27AB9C" @click="MobileSize ? openDialogDetailsMobile(item) : openDialogDetails(item)">ดูรายชื่อ</v-btn>
                        </template>
                    </v-data-table>
                </v-col>
                </v-row>
            </v-col>
            </v-row>
        </v-card-text>
        </v-card>
        <!-- <v-dialog
            v-model = "openDialog"
            width="850"
            scrollable>
        <v-card height="100%">
          <v-toolbar flat color="#E6F5F3">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title>
                  <span style="color: #27AB9C;">
                    <b>
                     รายชื่อ (0 รายการ)
                    </b>
                  </span>
                </v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small icon @click="closeDialogDetails()"><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-container>
            <v-card outlined>
                <v-data-table :headers="listNameHeader" :items="tableDataListName">
                    <template v-slot:[`item.indexTable`]="{index}">
                        {{ index + 1 }}
                    </template>
                </v-data-table>
            </v-card>
          </v-container>
        </v-card>
      </v-dialog> -->
    </v-container>
</template>
<script>
export default {
  data () {
    return {
      shopID: null,
      userDetails: [],
      search: '',
      header: [
        { text: 'ลำดับ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '50', value: 'indexTable' },
        { text: 'โลโก้', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'path_logo' },
        { text: 'ชื่อร้าน', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'name_th' },
        { text: 'สถานะ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'status_shop' },
        { text: 'จำนวนคนที่เข้าร่วม', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'user_count' },
        { text: 'รายชื่อ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'seller_shop_id' }
      ],
      tableData: []
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    this.getTableData()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      console.log(val)
      if (val === true) {
        this.$router.push({ path: '/userJoinSellerAffiliateMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'userJoinSellerAffiliate')
        this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    openDialogDetails (val) {
      // this.shopID = val.seller_shop_id
      // this.$store.commit('mutationsSellerShopId', this.shopID)
      // this.$EventBus.$emit('shop-id', this.shopID)
      this.$router.push(`/listUserJoinSellerAffiliate?id=${val.seller_shop_id}`)
    },
    openDialogDetailsMobile (val) {
      // this.shopID = val.seller_shop_id
      // this.$store.commit('mutationsSellerShopId', this.shopID)
      this.$router.push(`/listUserJoinSellerAffiliateMobile?id=${val.seller_shop_id}`)
    },
    async getTableData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetUserJoinSellerJAffiliate')
      var response = await this.$store.state.ModuleAdminManage.stateGetUserJoinSellerJAffiliate
      if (response.code === 200) {
        this.tableData = response.data
      }
      this.$store.commit('closeLoader')
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    statusShop (val) {
      if (val === 'yes') {
        return 'เปิดใช้งาน'
      } else if (val === 'no') {
        return 'ไม่เปิดใช้งาน'
      }
    }
  }
}
</script>
