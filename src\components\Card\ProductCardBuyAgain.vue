<template>
  <v-col no-gutters cols="12" class="d-flex justify-center" >
    <!-- Web -->
    <v-card v-if="!MobileSize && !IpadSize && !IpadProSize && HorizontalIpad > 1264" :href="itemProduct.link" class="d-flex align-center rounded-lg" height="290" width="600" style="background-color:#fff;" @click.prevent="DetailProduct(itemProduct)">
      <v-card-text class="pa-0">
        <v-row no-gutters justify="end">
          <p class="mr-2" style="font-size:12px; ">สั่งซื้อเมื่อวันที่ {{toThaiDateString(itemProduct.createdAT)}}</p>
        </v-row>
        <v-divider style="padding-bottom:10px; margin-top:-5px"></v-divider>
        <v-row no-gutters align="center">
          <v-col cols="4">
            <v-container v-if="itemProduct.img === null || itemProduct.img === undefined || itemProduct.img === ''" class="rounded-lg" style="width:100%; height:215px; max-width: 180px;">
              <v-img class="rounded-lg" src="@/assets/NoImage.png" width="100%" height="100%" contain></v-img>
            </v-container>
            <v-container v-else class="rounded-lg" style="width: 100%; height: 215px; max-width: 180px;">
              <v-img class="rounded-lg" :src="itemProduct.img" width="100%" height="100%" contain></v-img>
            </v-container>
          </v-col>
          <v-col align-self="start" cols="8">
            <h2 class="text-truncate pt-1" style="width: 360px"><b>{{itemProduct.title}}</b></h2>
            <p v-if="itemProduct.subtitle === '' || itemProduct.subtitle === '-'" style="margin-bottom:-12px; margin-top:-10px; color:transparent">-</p>
            <p v-else class="text-truncate" style="margin-bottom:-12px; margin-top:-10px;">{{itemProduct.subtitle}}</p>
            <v-row no-gutters style="margin-top:8px" >
              <v-rating
               v-model="itemProduct.stars"
               class="mr-3"
               color="#FB9300"
               background-color="#C4C4C4"
               empty-icon="$ratingFull"
               half-increments
               hover
               small
               dense
               readonly
              ></v-rating>
              <p style="margin-top:1px">ขายแล้ว {{itemProduct.amount | formatNumber}} ชิ้น</p>
            </v-row>
            <div style="margin-top:-12px" v-if="itemProduct.attribute === 'yes'">
              <div v-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 !== null">
                <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                <p style="margin-top:-15px; font-size: 12px !important;"><b>ตัวเลือก 2 :</b> {{itemProduct.attributeDetail.attribute_option_2}}</p>
              </div>
              <div v-else-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 === null">
                <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                <p style="margin-top:-15px; color: transparent; font-size: 12px !important;"><b>-</b></p>
              </div>
            </div>
            <div style="margin-top:-12px" v-if="itemProduct.attribute === 'no'">
              <p style="color:transparent;"><b>-</b></p>
              <p style="margin-top:-15px; color:transparent;"><b>-</b></p>
            </div>
            <v-row no-gutters class="d-flex justify-space-between align-center">
                <h1 v-if="itemProduct.vatDefault === 'yes' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></h1>
                <h1 v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></h1>
                <h1 v-else-if="itemProduct.vatDefault === 'yes' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></h1>
                <h1 v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></h1>
              <div class="mr-4" v-if="itemProduct.discount !== 0">
                <v-row no-gutters>
                  <p><b style="color:#C4C4C4; text-decoration:line-through; ">฿ {{formatNumberTooltip(parseFloat(itemProduct.price))}}</b></p>
                  <v-chip color="#FEE7E8" text-color="#FF7C9C" class="ml-4" style="margin-top:1px" x-small>ส่วนลด - {{itemProduct.discount}}%</v-chip>
                </v-row>
              </div>
            </v-row>
            <v-row no-gutters class="d-flex justify-end" :class="itemProduct.discount === 0 ? '' : ''" style="margin-top:0px">
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" onclick="return false;" @click.stop="addToCart()" class="rounded-pill" color="primary" style="margin-right:16px; padding-top:22px; padding-bottom:22px" outlined ><v-icon>mdi-cart</v-icon>เพิ่มไปยังรถเข็น</v-chip>
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" onclick="return false;" @click.stop="addToCart('QuickAddProduct')" class="rounded-pill" color="primary" style="margin-right:8px; padding-top:22px; padding-bottom:22px"><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- ipad แนวนอน -->
    <v-card v-if="!MobileSize && !IpadSize && !IpadProSize && HorizontalIpad < 1264" :href="itemProduct.link" class="d-flex align-center rounded-lg" height="290" width="600" style="background-color:#fff;" @click.prevent="DetailProduct(itemProduct)">
      <v-card-text class="pa-0">
        <v-row no-gutters justify="end">
          <p class="mr-2" style="font-size:12px; ">สั่งซื้อเมื่อวันที่ {{toThaiDateString(itemProduct.createdAT)}}</p>
        </v-row>
        <v-divider style="padding-bottom:10px; margin-top:-5px"></v-divider>
        <v-row no-gutters align="center">
          <v-col cols="4" style="" >
            <v-container v-if="itemProduct.img === null || itemProduct.img === undefined || itemProduct.img === ''" class="rounded-lg"  style=" width:100%; height:225px;">
              <v-img style="" class="rounded-lg" src="@/assets/NoImage.png" width="100%" height="100%"></v-img>
            </v-container>
            <v-container v-else class="rounded-lg"  style=" width:100%; height:225px;">
              <v-img class="rounded-lg" :src="itemProduct.img" width="100%" height="100%"></v-img>
            </v-container>
          </v-col>
          <v-col align-self="start" cols="8" style="">
            <h2 class="text-truncate pt-2" style="width: 100vw; margin-top:0px"><b>{{itemProduct.title}}</b></h2>
            <!-- <v-col class="ma-0 pa-0">
            </v-col> -->
            <p v-if="itemProduct.subtitle === '' || itemProduct.subtitle === '-'" style="margin-bottom:-12px; margin-top:-10px; color:transparent">-</p>
            <p v-else class="text-truncate" style="margin-bottom:-12px; margin-top:-10px">{{itemProduct.subtitle}}</p>
            <v-row no-gutters style="margin-top:8px" >
              <v-rating
              v-model="itemProduct.stars"
              class="mr-3 "
              color="#FB9300"
              background-color="#C4C4C4"
              empty-icon="$ratingFull"
              half-increments
              hover
              small
              dense
              readonly
              ></v-rating>
              <p style="margin-top:1px">ขายแล้ว {{itemProduct.amount | formatNumber}} ชิ้น</p>
            </v-row>
            <div style="margin-top:-12px" v-if="itemProduct.attribute === 'yes'">
              <div v-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 !== null">
                <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                <p style="margin-top:-15px; font-size: 12px !important;"><b>ตัวเลือก 2 :</b> {{itemProduct.attributeDetail.attribute_option_2}}</p>
              </div>
              <div v-else-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 === null">
                <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                <p style="margin-top:-15px; color:transparent; font-size: 12px !important;"><b>-</b> </p>
              </div>
            </div>
            <div style="margin-top:-12px" v-if="itemProduct.attribute === 'no'">
              <p style="color:transparent;"><b>-</b></p>
              <p style="margin-top:-15px; color:transparent;"><b>-</b></p>
            </div>
            <v-row dense class="d-flex justify-space-between align-center">
                <h1 v-if="itemProduct.vatDefault === 'yes' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></h1>
                <h1 v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></h1>
                <h1 v-else-if="itemProduct.vatDefault === 'yes' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></h1>
                <h1 v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></h1>
              <div class="mr-4" v-if="itemProduct.discount !== 0">
                <v-row dense>
                  <p><b style="color:#C4C4C4; text-decoration:line-through; ">฿ {{formatNumberTooltip(parseFloat(itemProduct.price))}}</b></p>
                  <v-chip color="#FEE7E8" text-color="#FF7C9C" class="ml-4" style="margin-top:1px" x-small>ส่วนลด - {{itemProduct.discount}}%</v-chip>
                </v-row>
              </div>
            </v-row>
            <v-row no-gutters class="d-flex justify-end" :class="itemProduct.discount === 0 ? 'pt-9' : ''" style="margin-top:8px">
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" @click.prevent="addToCart()" class="rounded-pill" color="primary" style="margin-right:16px; padding-top:22px; padding-bottom:22px" outlined ><v-icon>mdi-cart</v-icon>เพิ่มไปยังรถเข็น</v-chip>
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" @click.prevent="addToCart('QuickAddProduct')" class="rounded-pill" color="primary" style="margin-right:8px; padding-top:22px; padding-bottom:22px"><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- Mobile -->
    <v-card v-if="MobileSize" :href="itemProduct.link" class="px-0" height="100%" max-height="100%" width="100%" max-width="188px" style="background-color:#ffffff;" @click.prevent="DetailProduct(itemProduct)">
      <!-- New Card Product Buy Again -->
      <v-card-text class="px-2">
        <v-row dense>
          <v-col cols="12" class="px-0">
            <v-img
             v-if="itemProduct.img === null || itemProduct.img === undefined || itemProduct.img === ''"
             v-lazyload
             src="@/assets/NoImage.png"
             height="115"
             width="100%"
             loading='lazy'
             contain
            >
            </v-img>
            <v-img
             v-else
             v-lazyload
             :src="itemProduct.img"
             height="115"
             width="100%"
             loading='lazy'
             contain
            >
            </v-img>
          </v-col>
        </v-row>
        <v-row no-gutters>
          <v-col cols="12" md="12">
            <h1 class="mb-0" style="max-width: 90vw; font-size: 12px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;"
            :class="MobileSize? 'fontMobile' : IpadSize? 'fontIpad' : 'font'">{{ itemProduct.title }}</h1>
          </v-col>
        </v-row>
        <v-row no-gutters :class="itemProduct.subtitle === null || itemProduct.subtitle === '' ? 'mb-0': 'mb-0 pt-0'">
          <span v-if="itemProduct.subtitle === null || itemProduct.subtitle === ''" class="text-truncate mb-0" :style="IpadProSize? 'font-size: 10px; font-weight: 400; max-width: 132px; color:transparent':'font-size: 12px; font-weight: 400; max-width: 132px; color:transparent'"></span><br v-if="itemProduct.subtitle === null || itemProduct.subtitle === ''"/>
          <span v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; color: #9A9A9A; max-width: 100vw;">{{ itemProduct.subtitle }}</span>
        </v-row>
        <v-row dense>
          <v-rating
            v-model="itemProduct.stars"
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
          ></v-rating>
        </v-row>
        <v-row dense class="px-1 pt-1">
          <span v-if="itemProduct.vatDefault === 'yes' && itemProduct.discount !== 0"><b style="color: red; font-size: 20px;" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></span>
          <span v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount !== 0"><b style="color: red; font-size: 20px;" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></span>
          <span v-else-if="itemProduct.vatDefault === 'yes' && itemProduct.discount === 0"><b style="font-size: 20px;" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></span>
          <span v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount === 0"><b style="font-size: 20px;" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></span>
        </v-row>
        <v-row dense v-if="itemProduct.discount !== 0" class="px-1">
          <p><b style="color:#C4C4C4; text-decoration:line-through; font-size: 12px;">฿ {{formatNumberTooltip(parseFloat(itemProduct.price))}}</b></p>
          <v-chip  class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small>ส่วนลด -{{itemProduct.discount}}%</v-chip>
        </v-row>
        <v-row dense v-if="itemProduct.discount === 0" class="px-1" style="height: 22px;"></v-row>
        <v-row no-gutters class="d-flex justify-space-between" :class="itemProduct.discount === 0 ? 'pt-4' : ''">
          <v-btn rounded :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small onclick="return false;" @click.stop="addToCart()" color="primary" outlined><v-icon small>mdi-cart</v-icon></v-btn>
          <v-btn rounded :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small onclick="return false;" @click.stop="addToCart('QuickAddProduct')" color="primary"><v-icon small>mdi-shopping-outline</v-icon></v-btn>
        </v-row>
      </v-card-text>
      <!-- <v-container>
        <v-row no-gutters>
          <v-col cols="12">
            <v-img class="rounded-lg align-start" v-lazyload :src="itemProduct.img" width="100%" height="115" loading='lazy' ></v-img>
            <p class="mb-0 mt-1 px-0 text-truncate" style="font-size:18px"><b>{{ itemProduct.title }}</b></p>
            <p v-if="itemProduct.subtitle !== ''" class="text-truncate" style="color:#9A9A9A">{{itemProduct.subtitle}}</p>
            <p v-else style="color:#9A9A9A; height: 22px;"></p>
            <p class="mr-2" style="font-size:12px; margin-top:-12px; color:#9A9A9A">สั่งซื้อเมื่อวันที่ {{toThaiDateString(itemProduct.createdAT)}}</p>
              <v-row dense style="margin-top:-12px">
                <v-rating
                  v-model="itemProduct.stars"
                  color="#FB9300"
                  background-color="#C4C4C4"
                  empty-icon="$ratingFull"
                  half-increments
                  hover
                  small
                  dense
                  readonly
                ></v-rating>
              </v-row>
              <div style="margin-top:10px" v-if="itemProduct.attribute === 'yes'">
                <div v-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 !== null">
                  <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                  <p style="margin-top:-15px; font-size: 12px !important;"><b>ตัวเลือก 2 :</b> {{itemProduct.attributeDetail.attribute_option_2}}</p>
                </div>
                <div v-else-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 === null">
                  <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                  <p style="margin-top:-15px; color:transparent; font-size: 12px !important;"><b>-</b> </p>
                </div>
              </div>
              <div style="margin-top:10px" v-if="itemProduct.attribute === 'no'">
                <p style="color:transparent"><b>-</b></p>
                <p style="margin-top:-15px; color:transparent"><b>-</b></p>
              </div>
                <p style="margin-top:-12px" v-if="itemProduct.vatDefault === 'yes' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></p>
                <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></p>
                <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'yes' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></p>
                <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></p>
              <v-row v-if="itemProduct.discount !== 0" no-gutters style="margin-top:-20px">
                <p><b style="color:#C4C4C4; text-decoration:line-through; font-size:15px;">฿ {{formatNumberTooltip(parseFloat(itemProduct.price))}}</b></p>
                <v-chip color="#FEE7E8" text-color="#FF7C9C" class="ml-4" style="margin-top:1px" x-small>ส่วนลด - {{itemProduct.discount}}%</v-chip>
              </v-row>
              <v-row no-gutters class="d-flex justify-space-between" :class="itemProduct.discount === 0 ? 'pt-4' : ''" style="margin-top:-5px">
                <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small onclick="return false;" @click.stop="addToCart()" class="rounded-pill "  color="primary" style="padding:20px 10px 15px 10px" outlined ><v-icon>mdi-cart</v-icon>เพิ่มไปยังรถเข็น</v-chip>
                <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small onclick="return false;" @click.stop="addToCart('QuickAddProduct')" class="rounded-pill " color="primary" style="padding:20px 12px 18px 12px"><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
              </v-row>
          </v-col>
        </v-row>
      </v-container> -->
    </v-card>
    <!-- Ipad -->
    <v-card v-if="IpadSize" :href="itemProduct.link" class="d-flex align-center rounded-lg" height="260" width="70vw" style="background-color:#fff;" @click.prevent="DetailProduct(itemProduct)">
      <v-container >
        <v-row no-gutters justify="end">
          <p class="mr-2 mt-1" style="font-size:12px; margin-bottom:0px">สั่งซื้อเมื่อวันที่ {{toThaiDateString(itemProduct.createdAT)}}</p>
        </v-row>
        <v-divider style="padding-bottom:0px; margin-top:5px"></v-divider>
          <v-col cols="12" class="px-0">
            <v-row no-gutters>
              <v-col cols="12" class="px-0">
                <v-row no-gutters>
                  <v-col cols="5" class="pr-2">
                    <div v-if="itemProduct.img === null || itemProduct.img === undefined || itemProduct.img === ''" style="border:solid 1px rgb(39, 171, 156)" class="pa-2 rounded-lg">
                      <v-img
                      v-lazyload
                      src="@/assets/NoImage.png"
                      height="100"
                      width="100%"
                      loading='lazy'
                      contain
                      >
                      </v-img>
                    </div>
                    <div v-else style="border:solid 1px rgb(39, 171, 156)" class="pa-2 rounded-lg">
                      <v-img
                      v-lazyload
                      :src="itemProduct.img"
                      height="100"
                      width="100%"
                      loading='lazy'
                      contain
                      >
                      </v-img>
                    </div>
                  </v-col>
                  <v-col cols="7" class="pl-2">
                    <p class="mb-0 mt-1 px-0 text-truncate"><b>{{ itemProduct.title }}</b></p>
                    <v-row dense >
                      <v-rating
                        v-model="itemProduct.stars"
                        color="#FB9300"
                        background-color="#C4C4C4"
                        empty-icon="$ratingFull"
                        half-increments
                        hover
                        small
                        dense
                        readonly
                      ></v-rating>
                    </v-row>
                    <div style="margin-top:10px" v-if="itemProduct.attribute === 'yes'">
                      <div v-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 !== null">
                        <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                        <p style="margin-top:-15px; font-size: 12px !important;"><b>ตัวเลือก 2 :</b> {{itemProduct.attributeDetail.attribute_option_2}}</p>
                      </div>
                      <div v-else-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 === null">
                        <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                        <p style="margin-top:-15px; color:transparent; font-size: 12px !important;"><b>-</b> </p>
                      </div>
                    </div>
                    <div style="margin-top:10px" v-if="itemProduct.attribute === 'no'">
                      <p style="color:transparent;"><b>-</b></p>
                      <p style="margin-top:-15px; color:transparent;"><b>-</b></p>
                    </div>
                    <p style="margin-top:-12px" v-if="itemProduct.vatDefault === 'yes' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></p>
                    <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></p>
                    <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'yes' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></p>
                    <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></p>
                    <v-row v-if="itemProduct.discount !== 0" no-gutters justify="space-between" style="">
                      <p><b style="color:#C4C4C4; text-decoration:line-through; font-size:15px;">฿ {{formatNumberTooltip(parseFloat(itemProduct.price))}}</b></p>
                      <v-chip color="#FEE7E8" text-color="#FF7C9C" class="" style="margin-top:1px;" x-small>ส่วนลด - {{itemProduct.discount}}%</v-chip>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row no-gutters class="d-flex justify-space-between" :class="itemProduct.discount === 0 ? 'pt-9' : ''" style="margin-top:-5px">
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small @click.prevent="addToCart()" class="rounded-pill "  color="primary" style="padding:20px 10px 15px 10px" outlined ><v-icon>mdi-cart</v-icon>เพิ่มไปยังรถเข็น</v-chip>
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small @click.prevent="addToCart('QuickAddProduct')" class="rounded-pill " color="primary" style="padding:20px 12px 18px 12px"><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
            </v-row>
          </v-col>
      </v-container>
    </v-card>
    <v-card v-if="IpadProSize" :href="itemProduct.link" class="d-flex align-center rounded-lg" height="260" width="70vw" style="background-color:#fff;">
      <v-container >
        <v-row no-gutters justify="end">
          <p class="mr-2 mt-1" style="font-size:12px; margin-bottom:0px">สั่งซื้อเมื่อวันที่ {{toThaiDateString(itemProduct.createdAT)}}</p>
        </v-row>
        <v-divider style="padding-bottom:0px; margin-top:5px"></v-divider>
          <v-col cols="12" class="px-0">
            <v-row no-gutters>
              <v-col cols="12" class="px-0">
                <v-row no-gutters>
                  <v-col cols="5" class="pr-2">
                    <div v-if="itemProduct.img === null || itemProduct.img === undefined || itemProduct.img === ''" style="border:solid 1px rgb(39, 171, 156)" class="pa-2 rounded-lg">
                      <v-img
                      v-lazyload
                      src="@/assets/NoImage.png"
                      height="100"
                      width="100%"
                      loading='lazy'
                      contain
                      >
                      </v-img>
                    </div>
                    <div v-else style="border:solid 1px rgb(39, 171, 156)" class="pa-2 rounded-lg">
                      <v-img
                      v-lazyload
                      :src="itemProduct.img"
                      height="100"
                      width="100%"
                      loading='lazy'
                      contain
                      >
                      </v-img>
                    </div>
                  </v-col>
                  <v-col cols="7" class="pl-2">
                    <p class="mb-0 mt-1 px-0 text-truncate"><b>{{ itemProduct.title }}</b></p>
                    <v-row dense >
                      <v-rating
                        v-model="itemProduct.stars"
                        color="#FB9300"
                        background-color="#C4C4C4"
                        empty-icon="$ratingFull"
                        half-increments
                        hover
                        small
                        dense
                        readonly
                      ></v-rating>
                    </v-row>
                    <div style="margin-top:10px" v-if="itemProduct.attribute === 'yes'">
                      <div v-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 !== null">
                        <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                        <p style="margin-top:-15px; font-size: 12px !important;"><b>ตัวเลือก 2 :</b> {{itemProduct.attributeDetail.attribute_option_2}}</p>
                      </div>
                      <div v-else-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 === null">
                        <p style="font-size: 12px !important;"><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                        <p style="margin-top:-15px; color:transparent; font-size: 12px !important;"><b>-</b> </p>
                      </div>
                    </div>
                    <div style="margin-top:10px" v-if="itemProduct.attribute === 'no'">
                      <p style="color:transparent;"><b>-</b></p>
                      <p style="margin-top:-15px; color:transparent;"><b>-</b></p>
                    </div>
                    <p style="margin-top:-12px" v-if="itemProduct.vatDefault === 'yes' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></p>
                    <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount !== 0"><b style="color:red" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></p>
                    <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'yes' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice) + parseFloat(itemProduct.vatInclude)))}}</b></p>
                    <p style="margin-top:-12px" v-else-if="itemProduct.vatDefault === 'no' && itemProduct.discount === 0"><b style="" >฿ {{formatNumberTooltip(Number(parseFloat(itemProduct.realPrice)))}}</b></p>
                    <v-row v-if="itemProduct.discount !== 0" no-gutters justify="space-between" style="">
                      <p><b  style="color:#C4C4C4; text-decoration:line-through; font-size:15px;">฿ {{formatNumberTooltip(parseFloat(itemProduct.price))}}</b></p>
                      <v-chip  color="#FEE7E8" text-color="#FF7C9C" class="" style="margin-top:1px;" x-small>ส่วนลด - {{itemProduct.discount}}%</v-chip>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row no-gutters class="d-flex justify-space-between" :class="itemProduct.discount === 0 ? 'pt-9' : ''" style="margin-top:-5px">
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small onclick="return false;" @click.stop="addToCart()" class="rounded-pill "  color="primary" style="padding:20px 10px 15px 10px" outlined ><v-icon>mdi-cart</v-icon>เพิ่มไปยังรถเข็น</v-chip>
              <v-chip :disabled="itemProduct.actual_stock === 0 && itemProduct.effective_stock === 0 && itemProduct.product_type !== 'service'" small onclick="return false;" @click.stop="addToCart('QuickAddProduct')" class="rounded-pill " color="primary" style="padding:20px 12px 18px 12px"><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
            </v-row>
          </v-col>
      </v-container>
      <!-- <v-card-text class="pa-0">
        <v-row no-gutters align="center">
          <v-col cols="4" style="" >
            <v-container v-if="itemProduct.img === null || itemProduct.img === undefined || itemProduct.img === ''" class="rounded-lg"  style=" width:100%; height:215px;">
              <v-img style="" class="rounded-lg" src="@/assets/NoImage.png" width="100%" height="100%"></v-img>
            </v-container>
            <v-container v-else class="rounded-lg"  style=" width:100%; height:215px;">
              <v-img class="rounded-lg" :src="itemProduct.img" width="100%" height="100%"></v-img>
            </v-container>
          </v-col>
          <v-col align-self="start" cols="8" style="">
            <div>
              <h2><b>{{itemProduct.title}} </b></h2>
              <p v-if="itemProduct.subtitle === '' || itemProduct.subtitle === '-'" style="margin-bottom:-12px; margin-top:-10px; color:transparent">-</p>
              <p v-else style="margin-bottom:-12px; margin-top:-10px">{{itemProduct.subtitle}}</p>
              <v-row no-gutters style="margin-top:8px" >
                <v-rating
                class="mr-3 "
                color="#FB9300"
                background-color="#C4C4C4"
                empty-icon="$ratingFull"
                half-increments
                hover
                small
                dense
                readonly
                ></v-rating>
                <p style="margin-top:1px">ขายแล้ว {{itemProduct.amount}} ชิ้น</p>
              </v-row>
              <div style="margin-top:-12px" v-if="itemProduct.attribute === 'yes'">
                <div v-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 !== null">
                  <p><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                  <p style="margin-top:-15px"><b>ตัวเลือก 2 :</b> {{itemProduct.attributeDetail.attribute_option_2}}</p>
                </div>
                <div v-else-if="itemProduct.attributeDetail.attribute_option_1 !== null && itemProduct.attributeDetail.attribute_option_2 === null">
                  <p><b>ตัวเลือก 1 :</b> {{itemProduct.attributeDetail.attribute_option_1}}</p>
                  <p style="margin-top:-15px; color:transparent"><b>-</b> </p>
                </div>
              </div>
              <div style="margin-top:-12px" v-if="itemProduct.attribute === 'no'">
                <p style="color:transparent"><b>-</b></p>
                <p style="margin-top:-15px; color:transparent"><b>-</b></p>
              </div>
              <v-row no-gutters class="d-flex justify-space-between align-center">
                <div>
                  <h1><b style="color:red">฿ {{formatNumberTooltip(itemProduct.realPrice)}}</b></h1>
                </div>
                <div class="mr-4">
                  <v-row no-gutters>
                    <p><b style="color:#C4C4C4; text-decoration:line-through; ">฿ {{formatNumberTooltip(itemProduct.price)}}</b></p>
                    <v-chip color="#FEE7E8" text-color="#FF7C9C" class="ml-4" style="margin-top:1px" x-small>ส่วนลด - {{itemProduct.discount}}%</v-chip>
                  </v-row>
                </div>
              </v-row>
              <v-row no-gutters class="d-flex justify-space-between pr-2 pt-3" >
                <v-chip @click="addToCart()" @click.stop="DetailProduct('no')" class="rounded-pill" color="primary" style="" outlined ><v-icon>mdi-cart</v-icon>เพิ่มไปยังรถเข็น</v-chip>
                <v-chip @click="addToCart('QuickAddProduct')" @click.stop="DetailProduct('no')" class="rounded-pill" color="primary" style=""><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
              </v-row>
            </div>
          </v-col>
        </v-row>
      </v-card-text> -->
    </v-card>
  </v-col>
</template>

<script>
import { Decode } from '@/services'
import Vue from 'vue'
export default {
  props: ['itemProduct'],
  data () {
    return {
      item: this.itemProduct,
      selection: '',
      selectionSize: ''
    }
  },
  created () {
    this.formatSold()
    // console.log('itemProduct', this.item)
  },
  watch: {
  },
  computed: {
    HorizontalIpad () {
      const val = window.innerWidth
      return val
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    formatSold () {
      // console.log('item', this.itemProduct)
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'พัน'
        }
        return value.toString()
      })
    },
    toThaiDateString (date) {
      var dateChange = new Date(date)
      const monthNames = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน',
        'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม',
        'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const year = dateChange.getFullYear() + 543
      const month = monthNames[dateChange.getMonth()]
      const numOfDay = dateChange.getDate()

      const hour = dateChange.getHours().toString().padStart(2, '0')
      const minutes = dateChange.getMinutes().toString().padStart(2, '0')
      // const second = dateChange.getSeconds().toString().padStart(2, '0')
      // :${second}
      return `${numOfDay} ${month} ${year} ` + `${hour}:${minutes} น.`
    },
    test (val) {
      // console.log(val)
    },
    formatNumberTooltip (value) {
      if (typeof value !== 'undefined') {
        return value.toLocaleString(undefined, { minimumFractionDigits: 2 })
      }
      return ''
    },
    DetailProduct (val) {
      // console.log(val)
      if (val !== 'no') {
        const nameCleaned = val.title.replace(/\s/g, '-')
        // console.log('nameCleaned', nameCleaned + '-' + val.productID)
        this.$router.push({ path: '/DetailProduct/' + encodeURIComponent(nameCleaned) + '-' + (val.productID) }).catch(() => {})
      }
      // if (val !== 'no') {
      //   // console.log(val)
      //   const nameCleaned = val.title.replace(/\s/g, '-')
      //   if (val.id !== undefined && val.id !== '') {
      //     this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.productID}` } }).catch(() => {})
      //   } else {
      //     this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.productID}` } }).catch(() => {})
      //   }
      // }
    },
    async addToCart (ActionKey) {
      // console.log('ActionKey------->', ActionKey)
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyDetail
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyDetail = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        // console.log('companyDetail', companyDetail)
      } else if (dataRole.role === 'sale_order') {
        this.companyId = localStorage.getItem('PartnerID')
        // console.log('this.companyIdAddToCartWithLogin', thsis.companyId)
      }
      const data = {
        seller_shop_id: this.itemProduct.shopID,
        role_user: dataRole.role,
        product_id: this.itemProduct.productID,
        pay_type: this.itemProduct.payType,
        order_type: this.itemProduct.orderType,
        attribute_option_1: this.itemProduct.attribute === 'yes' ? this.itemProduct.attributeDetail.attribute_option_1 : '',
        attribute_option_2: this.itemProduct.attribute === 'yes' ? this.itemProduct.attributeDetail.attribute_option_2 : '',
        quantity: this.itemProduct.quantity,
        company_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail.company.company_id,
        company_position: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.role_id : -1,
        com_perm_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.com_perm_id : -1
      }
      // console.log('data', data)
      await this.$store.dispatch('ActionAddToCart', data)
      const res = await this.$store.state.ModuleCart.stateAddToCart
      if (res.message === 'Add to Cart Success') {
        this.$EventBus.$emit('getCartPopOver')
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>เพิ่มสินค้าลงในรถเข็นเรียบร้อย</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย' })
        }
        if (ActionKey === 'QuickAddProduct') {
          this.$router.push({ path: '/shoppingcart' }).catch(() => {})
        }
      } else if (res.message === 'This product is already in cart') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h4>มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว</h4>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว' })
        }
      } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ใส่ข้อมูลไม่ครบ</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ใส่ข้อมูลไม่ครบ' })
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>SERVER ERROR</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'SERVER ERROR' })
        }
      } else if (res.message === 'Please insert quantity > 0') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h4>กรุณาใส่จำนวนสินค้าอย่างน้อย 1 ชิ้น</h4>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาใส่จำนวนสินค้าอย่างน้อย 1 ชิ้น' })
        }
      } else if (res.message === 'Not found product with attribute detail.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: '<h3>กรุณาเลือกตัวเลือกของสินค้าก่อน</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', title: 'กรุณาเลือกตัวเลือกของสินค้าก่อน' })
        }
      } else if (res.message === 'Not found this product.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: '<h3>กรุณาเลือกตัวเลือกของสินค้าก่อน</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', title: 'กรุณาเลือกตัวเลือกของสินค้าก่อน' })
        }
      } else if (res.message === 'Please clear same product in your cart before add a new product cause product data had change.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', html: '<h3>ไม่สามารถดำเนินการได้</h3><br><p>มีสินค้ารายการนี้อยู่ในรถเข็นของคุณแล้ว และสินค้านี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้านี้ใหม่</p>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', title: 'ไม่สามารถดำเนินการได้', text: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้วของคุณแล้ว และสินค้านี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้านี้ใหม่' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else if (res.message === 'Select Product Limit') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ไม่สามารถดำเนินการได้', text: 'ตระกร้าสินค้าสามารถเพิ่มสูงสุดได้ 30 รายการเท่านั้น' })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
      }
    }
  }
}
</script>

<style scoped>
.fontMobile{
  line-height: 18px;
  height: 35px;
  max-height: 35px;
}
.fontIpad{
  line-height: 18px;
  height: 38.5px;
  max-height: 40px;
}
.font{
 line-height: 20px;
 height: 40px;
 max-height: 40px;
}
</style>
