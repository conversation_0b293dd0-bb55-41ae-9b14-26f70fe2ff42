<template>
 <v-container :class="MobileSize ? 'mt-4' : ''">
  <v-card style="height:650px">
    <v-row justify="right" dense class="mb-4 ml-0">
      <v-col cols="12" md="12">
        <v-row>
         <h2  class="ml-5 pt-10" style="font-size:14px;margin-bottom:2%">
          <v-icon v-if="MobileSize || IpadSize " @click="Cancle()" color="#27AB9C" >mdi-chevron-left</v-icon>
         <B>ดาวน์โหลดเอกสารและคู่มือการใช้งาน</B></h2>
         <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 50px; margin-left: 2px;" ></v-spacer>
         <v-btn  icon  outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;"   class="mt-8 mr-5" >
           <v-icon color="#27AB9C" @click="reloadpage()">mdi-file-document</v-icon>
         </v-btn>
        </v-row>
        <v-row class="mt-10">
          <v-card outlined width="300px" height="100px" style="border-radius: 8px; border: 1px solid #F2F2F2;" class="mr-4 ml-9" @click="D(manual)" dense>
              <v-row dense>
                <v-col cols="3" md="3" class="mt-2 ml-2">
                  <v-avatar
                    width="84px"
                    height="84px"
                    color="#EBEEF6"
                    style="border-radius: 8px;"
                  >
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="50px" height="50px" contain></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="8" md="8" class="mt-6 pl-6">
                  <span style="font-weight: bold; font-size: 16px; line-height: 24px; color: #333333; white-space: normal;">คู่มือการใช้ Panit (e-Pro).pdf</span><br/>
                  <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 1.68 MB</span>
                </v-col>
              </v-row>
          </v-card>
        </v-row>
      </v-col>
    </v-row>
  </v-card>
 </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      datas: []
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DownloadFilesMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/DownloadFiles' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.$EventBus.$emit('changeNav')
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    reloadpage () {
      window.location.reload()
    },
    DownloadForm (val) {
      if (val === 'POA') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88.docx')
      } else if (val === 'POAS') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%87.docx')
      } else if (val === 'manual') {
        window.open('https://s3gw.inet.co.th:8082/bucket-test-3-9-2022/File/%E0%B8%84%E0%B8%B9%E0%B9%88%E0%B8%A1%E0%B8%B7%E0%B8%AD%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B9%83%E0%B8%8A%E0%B9%89%20Panit%20%28e-Pro%29.pdf')
      }
    },
    Cancle () {
      this.$router.push({ path: '/sellerMobile?ShopID=' + this.onedata.user.list_shop_detail[0].seller_shop_id + '&ShopName=' + this.onedata.user.list_shop_detail[0].shop_name_th })
    }
  }
}
</script>

<style>

</style>
