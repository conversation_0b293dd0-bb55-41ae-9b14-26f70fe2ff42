<template>
  <v-container :class="MobileSize || IpadSize ? 'mt-9' : 'pt-0'">
    <!-- Banner Shop Setting -->
    <div class="ml-0 mr-0">
      <v-img src="@/assets/Create_Store/Banner-3.png" class="rounded-xl"></v-img>
    </div>
    <!-- Setting Shop -->
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4 pa-2" style="border-radius: 8px;">
      <v-card-text>
        <v-row dense>
          <!-- <v-col cols="12" v-if="MobileSize">
            <p class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> จัดการร้านค้า</p>
          </v-col> -->
          <!-- ส่วน Head ของ Shop -->
          <v-col class="d-flex flex-column" v-if="MobileSize || IpadSize || IpadProSize">
            <div class="d-flex">
              <v-icon color="#27AB9C" class="mr-2" v-if="MobileSize" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/shop_1.png" max-height="24" max-width="24" contain class="mr-2"></v-img>
              <span class="textManageShop mr-3">จัดการร้านค้า</span>
              <v-spacer></v-spacer>
              <v-btn text small color="#27AB9C" @click="editshop()"><v-icon size="24">mdi-pencil-outline</v-icon><span class="textEditManageShop">แก้ไข</span></v-btn>
            </div>
            <!-- <v-spacer></v-spacer> -->
            <div :class="IpadProSize ? 'mt-3 d-flex align-end mr-3' : 'mt-3'" :style="IpadProSize ? 'flex-wrap: wrap !important;' : ''">
              <div class="d-flex mr-3">
                <span class="textManageShop mr-3">การขอ DBD: </span>
                <div v-if="statusDBD === 'ร้านนี้ไม่มีคำขอ DBD'">
                  <span @click="dialogRegisDBD = true" style="cursor: pointer; color: #27AB9C; font-size: medium; text-decoration: underline">ลงทะเบียน DBD</span>
                </div>
                <div v-else>
                  <v-chip
                    v-if="reqStatus !== 'A'"
                    style="display: inline;"
                    :style="{ backgroundColor: colorBg(reqStatus), color: colorText(reqStatus) }"
                    @click="openDialogIfEditable"
                  >
                    <span>{{ mesStatus[1] }}</span>
                  </v-chip>
                  <v-btn v-if="reqStatus === 'R'" outlined color="#a0a0a0" fab x-small style="border: 0;" @click="dialogRegisDBD = true"><v-icon>mdi-reload</v-icon></v-btn>
                  <a :href="fileDBD" v-if="reqStatus === 'A'">
                    <span style="text-decoration: underline; font-size: medium">เครื่องหมายรับรอง DBD</span>
                  </a>
                </div>
              </div>
              <div class="d-flex" :class="!IpadProSize ? 'mt-3' : ''">
                <span class="textManageShop mr-3">เปิด - ปิด ร้านค้า</span>
                <v-switch v-model="openshop" inset color="#52C41A" hide-details @change="dialogConfirm = true; caseStatus = 'shop'"></v-switch>
                <span class="textFieldStepOne">{{ openshop === true ? 'เปิด' : 'ปิด' }}</span>
              </div>
              <div class="d-flex mt-3">
                <span :class="IpadProSize ? 'textManageShop mr-3' : 'textManageShop mr-3'">ข้อมูลการขอเป็นคู่ค้า</span>
                <v-switch v-model="partner" inset color="#52C41A" hide-details @change="dialogConfirm = true; caseStatus = 'partner'"></v-switch>
                <span class="textFieldStepOne">{{ partner === true ? 'เปิด' : 'ปิด' }}</span>
              </div>
            </div>
            <!-- <v-btn text small color="#27AB9C" @click="editshop()"><v-icon size="24">mdi-pencil-outline</v-icon><span class="textEditManageShop">แก้ไข</span></v-btn> -->
          </v-col>
          <v-col class="d-flex" v-else>
            <v-icon color="#27AB9C" class="mr-2" v-if="MobileSize" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>
            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/shop_1.png" max-height="24" max-width="24" contain class="mr-2"></v-img>
            <span class="textManageShop mr-3">จัดการร้านค้า</span>
            <v-spacer></v-spacer>
            <div class="d-flex mr-3">
              <span class="textManageShop mr-3">การขอ DBD: </span>
              <div v-if="statusDBD === 'ร้านนี้ไม่มีคำขอ DBD'">
                <span @click="dialogRegisDBD = true" style="cursor: pointer; color: #27AB9C; font-size: medium; text-decoration: underline">ลงทะเบียน DBD</span>
              </div>
              <div v-else>
                <v-chip
                  v-if="reqStatus !== 'A'"
                  style="display: inline;"
                  :style="{ backgroundColor: colorBg(reqStatus), color: colorText(reqStatus) }"
                  @click="openDialogIfEditable"
                >
                    <span>{{ mesStatus[1] }}</span>
                </v-chip>
                <v-btn v-if="reqStatus === 'R'" outlined color="#a0a0a0" fab x-small style="border: 0;" @click="dialogRegisDBD = true"><v-icon>mdi-reload</v-icon></v-btn>
                <a :href="fileDBD" v-if="reqStatus === 'A'">
                  <span style="text-decoration: underline; font-size: medium">เครื่องหมายรับรอง DBD</span>
                </a>
              </div>
            </div>
            <div class="d-flex mr-8">
              <span class="textManageShop mr-3">เปิด - ปิด ร้านค้า</span>
              <v-switch v-model="openshop" inset color="#52C41A" hide-details @change="dialogConfirm = true; caseStatus = 'shop'"></v-switch>
              <span class="textFieldStepOne">{{ openshop === true ? 'เปิด' : 'ปิด' }}</span>
            </div>
            <div class="d-flex mr-3">
              <span class="textManageShop mr-3">ข้อมูลการขอเป็นคู่ค้า</span>
              <v-switch v-model="partner" inset color="#52C41A" hide-details @change="dialogConfirm = true; caseStatus = 'partner'"></v-switch>
              <span class="textFieldStepOne">{{ partner === true ? 'เปิด' : 'ปิด' }}</span>
            </div>
            <v-btn text small color="#27AB9C" @click="editshop()"><v-icon size="24">mdi-pencil-outline</v-icon><span class="textEditManageShop">แก้ไข</span></v-btn>
          </v-col>
          <!-- ส่วนรูปภาพร้านค้า -->
          <v-col cols="12" class="mt-4 px-0">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="background: #F3F5F7; height: 57px;" :style="!MobileSize ? 'padding: 16px 20px;' : 'padding: 16px 16px;'">
                <span class="textManageShopHead">รูปภาพร้านค้า</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 8px 16px 8px;' : 'padding: 20px 20px 16px 20px;'">
                <v-row dense>
                  <v-col align="center" cols="12" md="4" sm="12">
                    <v-avatar class="rounded-circle"  tile :width="IpadProSize ? '100px' : IpadSize ? '60px' :'143'" :height="IpadProSize ? '100px' : IpadSize ? '60px' : '142'">
                      <v-img v-if="shopImg === null" src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain></v-img>
                      <v-img class="rounded-circle " v-else :src="shopImg" :lazy-src="shopImgLazy"></v-img>
                    </v-avatar>
                    <p class="textManageShopHead pt-4" style="text-align: center;">โปรไฟล์ร้านค้า</p>
                  </v-col>
                  <v-col cols="12" md="8" sm="12">
                    <v-row dense v-if="itemsBanner.length !== 0">
                      <v-col cols="12">
                        <v-img :src="itemsBanner[0].image_path" :lazy-src="itemsBanner[0].image_path_lazy" max-height="100%" max-width="100%" width="611" height="154" style="border-radius: 8px;">
                          <span :class="MobileSize ? 'my-span-Mobile' : 'my-span'">
                            หน้าปกร้านค้า
                          </span>
                        </v-img>
                      </v-col>
                      <v-col cols="12">
                        <div class="d-flex justify-start flex-wrap" v-if="!IpadSize && !MobileSize">
                          <!-- .splice(0, items.length - 1) -->
                          <v-card elevation="0" v-for="(item, index) in itemsSubBanner" :key="index" :class="index !== itemsSubBanner.length - 1 ? 'pr-1': ''">
                            <v-card-text class="pa-0">
                              <v-img :src="item.image_path" :lazy-src="item.image_path_lazy" style="border-radius: 4px;" max-height="100%" max-width="100%" width="119" height="40"></v-img>
                            </v-card-text>
                          </v-card>
                        </div>
                        <v-row dense v-else>
                          <v-col md="12" sm="6" v-for="(item, index) in itemsSubBanner" :key="index" :class="index !== itemsSubBanner.length - 1 ? 'pr-1': ''">
                            <v-card elevation="0" :class="MobileSize ? 'd-flex justify-space-between' : ''">
                              <v-img :src="item.image_path" :lazy-src="item.image_path_lazy" style="border-radius: 4px;" max-height="100%" max-width="100%" :width="MobileSize ? '35' : '100%'" :height="MobileSize ? 40 : 60"></v-img>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                    <v-row dense v-else>
                      <v-col cols="12" align="center">
                        <span>ไม่มีรูปภาพหน้าปก</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- ข้อมูลร้านค้า -->
          <v-col cols="12" class="mt-4 px-0">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead">ข้อมูลร้านค้า</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <v-row dense>
                  <v-col cols="12">
                    <span class="shopName">{{ this.nameShop }}</span>
                  </v-col>
                  <v-col cols="12" class="pl-0">
                    <span class="textProvice"><v-icon color="#27AB9C" size="24">mdi-map-marker</v-icon>{{this.provice}}</span>
                    <span class="textNumberImage pl-4 pr-1" >{{ itemsListProduct[0].value }}</span><span class="textProvice">รายการสินค้า</span>
                    <span class="textNumberImage pl-4 pr-1">-</span><span class="textProvice">ให้คะแนน</span>
                  </v-col>
                  <v-col cols="12" class="pl-0 pt-4">
                    <span class="textManageShopHead">ข้อมูลติดต่อ</span><br/><br/>
                    <span style="font-weight: 400; color: #333333; font-size: 16px; line-height: 26px;" :style="IpadProSize ? 'font-size: 16px;':'font-size: 16px;'">
                      บ้านเลขที่: {{this.detail.house_no === null ? '-' : this.detail.house_no}} {{this.detail.detail }}  ตำบล/แขวง: {{this.detail.sub_district === null ? '-' : this.detail.sub_district}}
                      อำเภอ/เขต: {{this.detail.district === null ? '-' : this.detail.district}} จังหวัด: {{this.detail.province === null ? '-' : this.detail.province}}
                      {{this.detail.zipcode === null ? '' : this.detail.zipcode}}
                    </span><br/>
                    <span style="font-weight: 400; color: #333333; font-size: 16px; line-height: 32px;" :style="IpadProSize ? 'font-size: 16px;':'font-size: 16px;'">โทร : {{ phone }}</span>
                    <br/><br/>
                    <span class="textManageShopHead">เกี่ยวกับร้านค้า</span><br/><br/>
                    <span style="font-weight: 400; color: #333333; font-size: 16px;" v-html="shopDetail"></span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- ส่วนรูปภาพโฆษณา -->
          <v-col cols="12" class="mt-4 px-0">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead">รูปโฆษณา</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 16px 8px 16px 8px;' : 'padding: 24px 20px 16px 20px;'" v-if="itemsAdvert.length !== 0">
                <v-row dense>
                  <v-col cols="12" md="10" sm="12">
                    <v-img style="border-radius: 8px;" :src="itemsAdvert[0].image_path" :lazy-src="itemsAdvert[0].image_path_lazy" max-height="100%" max-width="100%" height="165" width="100%"></v-img>
                  </v-col>
                  <v-col cols="12" md="2" sm="12">
                    <v-row dense>
                      <v-col cols="3" md="12" sm="6" v-for="(item, index) in itemsSubAdvert" :key="index" :class="MobileSize ? 'px-1' : ''">
                        <v-img :style="MobileSize ? 'border-radius: 4px;' : 'border-radius: 8px;'" :src="item.image_path" :lazy-src="item.image_path_lazy" max-height="100%" max-width="100%" :height="IpadSize || MobileSize ? 48 : 36" width="100%"></v-img>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-text style="padding: 24px 20px 16px 20px;" v-else>
                <v-row dense>
                  <v-col cols="12" md="12" sm="12" align="center">
                    <span>ไม่มีรูปภาพโฆษณา</span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- ส่วนรายการสินค้า -->
          <v-col cols="12" class="mt-4 px-0">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 72px;">
                <span class="textManageShopHead">รายการสินค้า</span>
                <v-spacer></v-spacer>
                <!-- <v-btn width="181" height="40" rounded color="#27AB9C" class="white--text buttonAddListProductShop"><v-icon size="24" color="white" class="pr-2">mdi-plus</v-icon>เพิ่มรายการสินค้า</v-btn> -->
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 16px 8px 16px 8px;' : 'padding: 24px 20px 16px 20px;'">
                <v-row dense v-if="!IpadSize && !MobileSize">
                  <v-col class="d-flex justify-space-between flex-wrap" v-for="(item, index) in itemsListProduct" :key="index">
                    <v-card style="border-radius: 8px; border: 1px solid #27AB9C;" outlined width="100%" height="88">
                      <v-card-text class="px-3">
                        <v-row dense>
                          <v-col cols="4" class="pt-2">
                            <v-img :src="item.icon" max-height="40" max-width="40"></v-img>
                          </v-col>
                          <v-col cols="8" align="end">
                            <span style="font-size: 24px; font-weight: 700; line-height: 140%; color: #27AB9C;">{{ item.value }}</span><br/>
                            <span style="font-size: 14px; font-weight: 500; line-height: 140%; color: #333;">{{ item.text }}</span>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row dense v-else>
                  <v-col cols="4" v-for="(item, index) in itemsListProduct" :key="index" class="px-1">
                    <v-card style="text-align: center; border-radius: 8px; border: 1px solid #27AB9C;" outlined width="100%" height="100%">
                      <v-card-text class="pa-2">
                        <v-row dense class="pa-0">
                          <v-col cols="3" class="pt-2 px-0">
                            <v-img :src="item.icon" max-height="32" max-width="32"></v-img>
                          </v-col>
                          <v-col cols="9" class="pl-0" align="end">
                            <span style="font-size: 14px; font-weight: 700; line-height: 19px; color: #27AB9C;">{{ item.value }}</span><br/>
                            <span style="font-size: 10px; font-weight: 500; line-height: 14px; color: #333;">{{ item.text }}</span>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- อัปโหลดเอกสารอย. -->
          <v-col cols="12" class="mt-4 px-0">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead">อัปโหลดเอกสาร อย.</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <v-row dense v-if="fileFDA.length === 0">
                  <v-col cols="12">
                    <v-card width="100%" height="100%" outlined elevation="0" class="cardImageStyle" @click="onPickFile()">
                      <v-card-text style="text-align: center;" class="px-2">
                        <input type="file" ref="fileFDA" @change="handleFileUpload($event)" style="display: none;" accept=".pdf">
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-0">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="100" height="100" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center">
                            <v-col cols="12" md="4" style="text-align: center;">
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row dense v-if="fileFDA.length !== 0">
                  <v-col v-for="(item, index) in fileFDA" :key="index" cols="12" md="12" sm="12">
                    <span v-if="item.doc_status === 'Pending'" style="color: #333333; font-weight: 700;">สถานะ : เอกสารกำลังรออนุมัติกับทางแอดมินระบบ</span>
                    <span v-if="item.doc_status === 'Reject'" style="color: #333333; font-weight: 700;">สถานะ : เอกสารโดนปฏิเสธ กรุณาตรวจสอบเอกสารอีกครั้ง</span>
                    <span v-if="item.doc_status === 'Approve'" style="color: #333333; font-weight: 700;">สถานะ : เอกสารอนุมัติกับทางแอดมินระบบเรียบร้อยแล้ว</span>
                    <v-card outlined width="100%" height="80%" @click="openLink(item.path)" class="mt-2">
                      <v-row dense justify="end" v-if="statusFDADoc === 'Reject'">
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click.stop="Removefile(item, index)">mdi-close</v-icon>
                        </v-btn>
                      </v-row>
                      <v-card-text>
                        <v-row dense>
                          <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                          </v-img>
                          <span style="text-align: center; align-content: center;" class="text-truncate pt-2">{{ item.file_name }}</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- <v-col cols="12" sm="7" md="6" :class="MobileSize ? 'mb-3' : ''" class="pt-1 pb-2">
            <v-card :outlined="IpadSize ? true : false" :class="IpadSize ? 'pr-2' : ''" :height="IpadSize ? '100%' : '100%'">
              <v-row dense :class="IpadSize ? 'pt-2 pr-2' : ''">
                <v-col cols="2">
                  <v-avatar class="ml-4 rounded-circle "  tile :width="IpadProSize ? '100px' : IpadSize ? '60px' :'100px'" :height="IpadProSize ? '100px' : IpadSize ? '60px' : '100px'">
                    <v-img v-if="Detail.product_image === null  || Detail.product_image.length === 0" src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain></v-img>
                    <v-img class="rounded-circle " v-else :src="Detail.product_image[0].path" ></v-img>
                  </v-avatar>
                  <v-card :width="IpadSize ? '20px' : '32px'" :height="IpadSize ? '20px' : '32px'" style="margin-Top:-15px" :style="IpadProSize ? 'margin-left:47px' : IpadSize ? 'margin-left: 35px': 'margin-left:49px' " @click="dialogopen()">
                    <v-icon :size="IpadSize ? 15 : 25" style="margin:3px" color="#27AB9C">mdi-qrcode-scan</v-icon>
                  </v-card>
              </v-col>
              <v-col cols="9">
                  <v-row>
                    <v-col :cols="IpadProSize ? 10 : MobileSize ? 9 : IpadSize ? 10 : 11">
                      <h2 :class="IpadProSize ? 'ml-15' : MobileSize ? 'ml-16' : 'ml-12'" :style="IpadSize ? 'font-weight: 700; font-size: 16px; line-height: 24px;' : ''">{{this.nameShop}}</h2>
                    </v-col>
                    <v-col :cols="MobileSize ? 2 : 1" align="end">
                      <v-btn icon dense elevation="0" :small="IpadSize ? true : false">
                        <v-avatar :size="IpadSize ? 18 : 27">
                          <v-img contain :src="require('@/assets/icons/edit-2.png')" @click="editshop()" ></v-img>
                        </v-avatar>
                      </v-btn>
                    </v-col>
                  </v-row>
                <span :class="IpadProSize ? 'ml-14 mb-20' : MobileSize ? 'ml-15' : 'ml-10 mb-20'"><v-icon color="#BEBEBE">mdi-map-marker</v-icon><font color="#BEBEBE">{{this.provice}}</font></span><br/>
                <span :class="IpadProSize ? 'ml-14 mt-2' : MobileSize ? 'ml-15' : 'ml-12 mt-2'"><font color="#0061A8">{{ this.dataShop.total_product }}</font> รายการสินค้า</span>
              </v-col>
              <v-col cols="11" sm="12" :class="IpadSize ? 'ml-0' : ''">
                <v-container style="height: 295px;">
                  <v-row>
                    <v-col cols="12">
                      <h3><B>ข้อมูลติดต่อ</B></h3>
                      <span  style="font-weight: 400; color: #333333;" :style="IpadProSize ? 'font-size: 12px;':'font-size: 14px;'">
                      บ้านเลขที่: {{this.detail.house_no === null ? '-' : this.detail.house_no}}   ตำบล/แขวง: {{this.detail.sub_district === null ? '-' : this.detail.sub_district}}
                      อำเภอ/เขต: {{this.detail.district === null ? '-' : this.detail.district}} จังหวัด: {{this.detail.province === null ? '-' : this.detail.province}}
                      {{this.detail.zipcode === null ? '' : this.detail.zipcode}}
                      </span><br/><br/>
                      <h3><B>เกี่ยวกับร้านค้า</B></h3>
                      <v-textarea class="HoverText" auto-grow :style="MobileSize ? '' : IpadSize ? 'width: 100% !important;' : 'width: 420px;'" full-width :height="!IpadSize ? '150' : '160'" hide-details readonly :value="shopDetail" rounded></v-textarea>
                    </v-col>
                  </v-row>
                </v-container>
              </v-col>
              </v-row>
            </v-card>
          </v-col> -->
          <!-- <v-col cols="12" sm="5" md="6" :class="IpadSize ? 'mt-0 pl-2' : ''">
            <v-row>
            <v-col cols="12">
              <v-card :outlined="IpadSize ? true : false">
                <v-row>
                <v-col cols="12" >
                  <v-row >
                  <v-col cols="9" md="10" sm="9" :class="IpadSize ? 'pt-4 mt-3' : ''">
                    <h2 class="ml-4" :style="IpadSize ? 'font-size: 16px; font-weight: 700; line-height: 24px;' : 'font-size: 20px;'">จัดการรูปภาพ</h2>
                  </v-col>
                  <v-col cols="2" md="1" sm="3" aling="end" :class="IpadSize ? 'pl-0 pt-2' : ''">
                    <v-btn icon dense elevation="0" :small="IpadSize ? true : false" :class="IpadSize ? 'mt-4' : '' ">
                    <v-avatar :size="IpadSize ? 18 : 27" >
                      <v-img contain :src="require('@/assets/icons/edit-2.png')" @click="editpictuer()" ></v-img>
                    </v-avatar>
                    </v-btn>
                  </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" align="center" style="margin-top:-25px">
                  <v-container>
                    <v-img v-if="Detail.product_images === null  || Detail.product_images.length === 0" class="rounded-lg" src="@/assets/NoImage.png" width="480" height="222" contain></v-img>
                    <v-carousel
                      v-else
                      cycle
                      height="222"
                      hide-delimiter-background
                      show-arrows-on-hover
                      hide-delimiters
                      >
                        <v-carousel-item v-for="(slide, i) in Detail.product_images" :key="i" >
                          <v-img  class="rounded-lg" :src="slide.path" width="480" height="222" contain></v-img>
                        </v-carousel-item>
                    </v-carousel>
                  </v-container>
                </v-col>
                </v-row>
              </v-card>
            </v-col>
            <v-col cols="12">
              <v-card width="100%" :outlined="IpadSize ? true : false">
                <v-card-title class="ml-4 mt-0 px-0 pt-3" :style="IpadSize ? 'font-size: 16px; font-weight: 700; line-height: 24px;' : 'font-size: 20px;'">รายการสินค้า</v-card-title>
                <v-card-text class="px-0 pb-2">
                  <v-row class="ml-5"  dense v-if="!MobileSize && !IpadSize">
                    <v-col class="tap_left"  cols="2">
                    <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_product }}</B></h3>
                    </v-col>
                    <v-col align="center" cols="3">
                    <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_best_sold_product}}</B></h3>
                    </v-col>
                    <v-col align="center" cols="3">
                    <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_new_product }}</B></h3>
                    </v-col>
                    <v-col align="center" cols="3">
                    <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_recommend_product }}</B></h3>
                    </v-col>
                    <v-col cols="3">
                    <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' :'font-size: 16px;'">สินค้าทั้งหมด</h3>
                    </v-col>
                    <v-col cols="3">
                    <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' :'font-size: 16px;'">สินค้าขายดี</h3>
                    </v-col>
                    <v-col cols="3">
                    <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' :'font-size: 16px;'">สินค้ามาใหม่</h3>
                    </v-col>
                    <v-col cols="3">
                    <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' :'font-size: 16px;'">สินค้าแนะนำ</h3>
                    </v-col>
                  </v-row>
                  <v-row dense v-else>
                    <v-col align="center" cols="3" sm="6">
                      <h3 style="color:#27AB9C" :style="IpadSize ? 'font-size: 16px;': ''"><B>{{ this.dataShop.total_product }}</B></h3>
                      <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' : IpadSize ? 'font-size: 12px;' : 'font-size: 16px;'">สินค้าทั้งหมด</h3>
                    </v-col>
                    <v-col align="center" cols="3" sm="6">
                      <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_best_sold_product}}</B></h3>
                      <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' : IpadSize ? 'font-size: 12px;' : 'font-size: 16px;'">สินค้าขายดี</h3>
                    </v-col>
                    <v-col align="center" cols="3" sm="6">
                      <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_new_product }}</B></h3>
                      <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' : IpadSize ? 'font-size: 12px;' : 'font-size: 16px;'">สินค้ามาใหม่</h3>
                    </v-col>
                    <v-col align="center" cols="3" sm="6">
                      <h3 style="color:#27AB9C"><B>{{ this.dataShop.total_recommend_product }}</B></h3>
                      <h3 :style="IpadProSize ? 'font-size: 14px;': MobileSize ? 'font-size: 12px;' : IpadSize ? 'font-size: 12px;' : 'font-size: 16px;'">สินค้าแนะนำ</h3>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            </v-row>
          </v-col> -->
        </v-row>
      </v-card-text>
      <br v-if="!MobileSize" />
      <!-- <v-container v-for="index in countloop" :key="index">
        <HomeProducts v-if="StatusHomeNewProduct && index-1 === 0 && GetAllProduct.allproduct.length !== 0"  :propsData='GetAllProduct.allproduct'  :header='namehearder[index-1]' :check='status'/>
        <HomeProducts v-if="StatusHomeNewProduct && index-1 === 1 && GetAllProduct.bestSeller.length !== 0" :propsData='GetAllProduct.bestSeller' :header='namehearder[index-1]' :check='status'/>
        <HomeProducts v-if="StatusHomeNewProduct && index-1 === 2 && GetAllProduct.newArrivals.length !== 0" :propsData='GetAllProduct.newArrivals' :header='namehearder[index-1]' :check='status'/>
        <HomeProducts v-if="StatusHomeNewProduct && index-1 === 3 && GetAllProduct.recommend.length !== 0"  :propsData='GetAllProduct.recommend' :header='namehearder[index-1]' :check='status'/>
      </v-container> -->
    <!-- <div v-if="!MobileSize">
      <v-container v-for="(item,index) in list" :key="index">
        <HomeProducts v-if="index < countloop" :propsData='list[index]' :header='headerlist[index]' :check='status' />
      </v-container>
    </div>
    <div v-else >
      <div v-for="(item,index) in list" :key="index">
        <HomeProducts v-if="index < countloop" :propsData='list[index]' :header='headerlist[index]' :check='status' style="margin-top: -20px" />
      </div>
    </div> -->
      <!-- <v-container >
        <HomeProducts v-if="this.GetAllProduct.allproduct !== 'ยังไม่มีรายการสินค้า'" :propsData='GetAllProduct.allproduct' :header='namehearder[0]' :check='status'/>
        <HomeProducts v-if="this.GetAllProduct.bestSeller !== 'ยังไม่มีรายการสินค้า'"  :propsData='GetAllProduct.bestSeller' :header='namehearder[1]' :check='status'/>
        <HomeProducts v-if="this.GetAllProduct.newArrivals !== 'ยังไม่มีรายการสินค้า'" :propsData='GetAllProduct.newArrivals' :header='namehearder[2]' :check='status'/>
        <HomeProducts v-if="this.GetAllProduct.recommend !== 'ยังไม่มีรายการสินค้า'"  :propsData='GetAllProduct.recommend' :header='namehearder[3]' :check='status'/>
      </v-container> -->
      <!-- <v-container v-if="this.listproduct > 1">
      <v-row justify="center">
        <a><h3  style="color:#27AB9C" @click="addshow()">ดูรายละเอียดเพิ่มเติม<v-icon  color="#27AB9C">{{this.icon}}</v-icon></h3></a>
      </v-row>
      </v-container> -->
    </v-card>
    <v-dialog  v-model="dialog" width="600" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">ลิงก์ของฉัน</font></span>
          <v-btn
            icon
            dark
            @click="dialog = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/>
        <v-card-text >
          <vue-qrcode :value="urlname" :size="size" />
          <v-text-field readonly v-model="urlname" class="ml-15 mr-15" placeholder="URL Link ของร้าน" max-width="383px"  outlined dense><v-icon  slot="append" color="#27AB9C">mdi-share-variant</v-icon></v-text-field>
          <v-btn dense rounded v-clipboard:copy="urlname" v-clipboard:error="onError" v-clipboard:success="onCopy" width="165" color="#27AB9C" class=" white--text" @click="SuccessCurierAll()">คัดลอกลิงก์</v-btn>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirm" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการทำรายการ
          </span>
           <v-btn icon dark @click="handleClose(caseStatus)">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการปรับสถานะ</span><br>
              <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
            </v-col>

          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="handleClose(caseStatus)" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="handleClick(caseStatus)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirmRegis" persistent :width="MobileSize ? '100%' : IpadSize ? '50%' : IpadProSize ? '40%' : '25%'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%" class="pa-2">
        <v-card-text class="d-flex flex-column align-center mt-8" style="gap: .5vw">
          <v-img
            src="@/assets/ICON/iconConfirmRegis.png"
            max-height="80"
            max-width="80"
          ></v-img>
          <h2 class="mt-2"><b>บันทึกข้อมูล</b></h2>
          <span>คุณได้ทำการกรอกข้อมูลการลงทะเบียน</span>
          <span>คุณต้องการยืนยันการทำรายการนี้ ใช่ หรือ ไม่</span>
          <div class="d-flex mt-3" style="gap: 5vw">
            <v-btn outlined color="red" style="border: 2px solid; border-radius: 15px;"  @click="dialogConfirmRegis = false">ยกเลิก</v-btn>
            <v-btn color="#36D7A7" style="border: 0; color: #fff; border-radius: 15px;" @click="registerDBD()">ยืนยัน</v-btn>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogRegisDBD" persistent width="550">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ลงทะเบียน DBD
          </span>
           <v-btn icon dark @click="closeDialogRegis">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="mt-2">
          <v-row>
            <v-col>
              <span><b>ข้อมูลการขอลงทะเบียน DBD</b></span>
            </v-col>
            <v-col cols="12" style="margin-top: -8px">
              <span>Tax Id: </span>
              <v-text-field
                v-model="taxIdShop"
                outlined
                dense
                disabled
                placeholder="ระบุรหัสสาขา"
              ></v-text-field>
            </v-col>
            <v-col style="margin-top: -35px">
              <span>ประเภทธุรกิจ: <span style="color: red">*</span></span>
              <v-autocomplete
                v-model="typeBusiness"
                :items="itemTypeBusiness"
                dense
                outlined
                label="เลือกประเภทธุรกิจ"
                class="mr-1 mt-2"
                no-data-text="ไม่พบประเภทธุรกิจ"
                item-text="details_th"
                item-value="type_code"
                multiple
                chips
                small-chips
              >
                  <template v-slot:selection="{ item, index  }">
                  <div>
                    <v-chip v-if="index < 3"
                      class="my-2"
                      close
                      outlined
                      @click:close="removeShop(item)" color="primary"
                    >
                      <span v-if="MobileSize">{{ item.details_th | truncate(35, '...') }}</span>
                      <span v-else>{{ item.details_th }}</span>
                    </v-chip>
                    <span
                      v-if="index === 3"
                      class="grey--text text-caption"
                    >
                      (+{{ typeBusiness.length - 3 }} ประเภท)
                    </span>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" style="margin-top: -28px">
              <span>เอกสารรับรอง: </span>
              <v-file-input
                outlined
                accept=".pdf"
                id="file_input"
                label="อัปโหลดเอกสารรับรอง"
                class="mr-1 mt-2"
                dense
                v-model="fileConsent"
                :validate-on-blur="true"
              >
                <template v-slot:selection="{ text }">
                  <v-chip
                    small
                    outlined
                    color="#27AB9C"
                  >
                    {{ text }}
                  </v-chip>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="d-flex pb-4" style="margin-top: -20px">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogRegis()" class="mr-2">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="dialogConfirmRegis = true">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDetailEdit" persistent :width="MobileSize ? '100%' : IpadSize ? '65%' : IpadProSize ? '50%' : '40%'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%" class="pa-2">
        <v-card-title primary-title class="d-flex justify-end">
          <v-btn icon dark @click="dialogDetailEdit = false">
            <v-icon color="#a0a0a0">mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="d-flex flex-column align-center mt-8" style="gap: .5vw">
          <v-img
            src="@/assets/NotData.png"
            max-height="150"
            max-width="150"
          ></v-img>
          <h4 class="mt-2" v-if="MobileSize"><b>สถานะการลงทะเบียนขอตรา DBD : แก้ไขข้อมูล</b></h4>
          <h2 class="mt-2" v-else><b>สถานะการลงทะเบียนขอตรา DBD : แก้ไขข้อมูล</b></h2>
          <div class="d-flex flex-column align-center">
            <span>รายละเอียดการแก้ไขถูกจัดส่งไปยังอีเมลของท่าน</span>
            <span>และช่องทางการแก้ไขผ่าน เว็บไซต์ด้านล่างนี้</span>
            <span><a href="https://dbdregistered.dbd.go.th/">https://dbdregistered.dbd.go.th/</a></span>
          </div>
          <!-- <span v-if="MobileSize" class="d-flex flex-column align-center">รายละเอียดการแก้ไขจะถูกจัดส่งไปยังอีเมลของท่าน และช่องทางการแก้ไขผ่าน เว็บไซต์ <a href="https://dbdregistered.dbd.go.th/">https://dbdregistered.dbd.go.th/</a></span> -->
          <!-- <div v-else>
            <span>รายละเอียดการแก้ไขจะถูกจัดส่งไปยังอีเมลของท่าน</span>
            <span>และช่องทางการแก้ไขผ่าน เว็บไซต์ <a href="https://dbdregistered.dbd.go.th/">https://dbdregistered.dbd.go.th/</a></span>
          </div> -->
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import Vue from 'vue'
import { Decode } from '@/services'
import VueQrcode from '@chenfengyuan/vue-qrcode'
import VueClipboard from 'vue-clipboard2'
// import { Encode } from '@/services'
VueClipboard.config.autoSetContainer = true
const newArrivals = []
const bestSeller = []
const recommend = []
const saleProduct = []
for (let i = 0; i < 50; i++) {
  recommend.push({
    product_id: i,
    name: `Data Title recommend ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
const ProductList = []
for (let i = 0; i < 20; i++) {
  ProductList.push(
    {
      product_id: i,
      name: `Data Title recommend ${i}`,
      price: ` ${i * 10}`,
      stock: `${i}`,
      image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
    }
  )
}
const Category = []
const allproduct = []
Category.push(
  {
    category_list: [{
      category_name: 'อุปกรณ์โรงงาน',
      category_img: 'https://images.unsplash.com/photo-1429514513361-8fa32282fd5f?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=3264&q=80'
    }],
    product_list: ProductList
  },
  {
    category_list: [{
      category_name: 'อุปกรณ์สำนักงาน',
      category_img: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=2100&q=80'
    }],
    product_list: ProductList
  },
  {
    category_list: [{
      category_name: 'เฟอร์นิเจอร์',
      category_img: 'https://images.unsplash.com/photo-1542320868-f4d80389e1c4?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=3750&q=80'
    }],
    product_list: ProductList
  }
)
const CategoryList = []
CategoryList.push({ data: Category })
export default {
  components: {
    VueQrcode
    // HomeProducts: () => import('@/components/Home/HomeProductUI')
    // VueQrcode,
    // HomeProducts: () => import('@/views/CardManageshop')
  },
  data () {
    return {
      itemsListProduct: [
        { icon: require('@/assets/ImageINET-Marketplace/ICONShop/AllProduct.png'), text: 'สินค้าทั้งหมด', value: 0 },
        { icon: require('@/assets/ImageINET-Marketplace/ICONShop/Promotion.png'), text: 'โปรโมชั่นแนะนำ', value: 0 },
        { icon: require('@/assets/ImageINET-Marketplace/ICONShop/NewProduct.png'), text: 'สินค้ามาใหม่', value: 0 },
        { icon: require('@/assets/ImageINET-Marketplace/ICONShop/Discount.png'), text: 'สินค้าลดราคา', value: 0 },
        { icon: require('@/assets/ImageINET-Marketplace/ICONShop/BestSeller.png'), text: 'สินค้าขายดี', value: 0 }
      ],
      itemsBanner: [],
      itemsSubBanner: [],
      itemsAdvert: [],
      itemsSubAdvert: [],
      phone: '',
      icon: 'mdi-chevron-down',
      GetAllProduct: {
        newArrivals,
        bestSeller,
        recommend,
        allproduct,
        saleProduct,
        CategoryList
      },
      dialog: false,
      openshop: false,
      partner: false,
      status: true,
      StatusHomeNewProduct: false,
      StatusHomeBestProduct: false,
      StatusHomeRecoment: false,
      tokenstatus: '',
      listproduct: 0,
      countloop: 1,
      namehearder: [
        'สินค้าทั้งหมด',
        'สินค้าขายดี',
        'สินค้ามาใหม่',
        'สินค้าแนะนำ'
      ],
      shopDetail: '',
      shopImg: '',
      shopImgLazy: '',
      detail: '',
      district: '',
      provice: '',
      sub_district: '',
      dataShop: '',
      urlname: '',
      value: '',
      nameShop: '',
      size: 450,
      img: '',
      Detail: {
        product_image: [],
        product_images: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      list: [],
      headerlist: [],
      fileFDA: [],
      statusFDADoc: '',
      dialogConfirm: false,
      caseStatus: '',
      statusDBD: '',
      reqNo: '',
      taxIdShop: '',
      dialogRegisDBD: false,
      resCheck: '',
      mesStatus: '',
      reqStatus: '',
      fileConsent: [],
      typeBusiness: [],
      itemTypeBusiness: [],
      dialogConfirmRegis: false,
      fileDBD: '',
      dialogDetailEdit: false
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  mounted () {
    Vue.use(VueClipboard)
  },
  watch: {
    MobileSize (val) {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (val === true) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/designShop' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.oneData === undefined) {
        this.$router.push({ path: '/' })
      }
    }
    var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      if (dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        window.scrollTo(0, 0)
        this.$store.commit('openLoader')
        this.$EventBus.$emit('CheckFooter')
        this.$EventBus.$emit('getPath')
        this.$EventBus.$emit('changeNav')
        this.$EventBus.$on('getHomepageItems', this.getHomepageItems)
        this.$store.commit('openLoader')
        await this.getHomepageItems()
        await this.getDataitem()
        await this.getDetailShop()
        await this.ListDocumentsSellerShop()
        this.$store.commit('closeLoader')
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.checkDBD()
    // window.scrollTo(0, 0)
    // this.$EventBus.$emit('CheckFooter')
    // this.$EventBus.$emit('getPath')
    // this.$EventBus.$emit('changeNav')
    // this.$EventBus.$on('getHomepageItems', this.getHomepageItems)
    // await this.getHomepageItems()
    // await this.getDataitem()
    // await this.getDetailShop()
  },
  methods: {
    openDialogIfEditable () {
      if (this.reqStatus === 'U') {
        this.dialogDetailEdit = true
      }
    },
    colorText (val) {
      if (val === 'U') {
        return '#FAAD14'
      } else if (val === 'A') {
        return '#52C41A'
      } else if (val === 'R') {
        return '#F5222D'
      } else {
        return '#d4ac0d'
      }
    },
    colorBg (val) {
      if (val === 'U') {
        return '#fdf8ed'
      } else if (val === 'A') {
        return '#def9d1'
      } else if (val === 'R') {
        return '#fdcbcd'
      } else {
        return '#fdf8ed'
      }
    },
    async changeStatusShop () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        status: this.openshop === false ? 'inactive' : 'active'
      }
      // console.log(data, 555555)
      await this.$store.dispatch('actionChangeStatusShop', data)
      var res = await this.$store.state.ModuleShop.stateChangeStatusShop
      if (res.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'ปรับสถานะสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.dialogConfirm = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    async changeStatusPartner () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        status: this.partner === false ? 'inactive' : 'active'
      }
      // console.log(data, 555555)
      await this.$store.dispatch('actionChangePartnerShow', data)
      var res = await this.$store.state.ModuleShop.stateChangePartnerShow
      if (res.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'ปรับสถานะสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.dialogConfirm = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    Removefile (item, index) {
      this.fileFDA.splice(index, 1)
    },
    async ListDocumentsSellerShop () {
      this.fileFDA = []
      const data = {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionsListDocumentsSellerShop', data)
      const response = await this.$store.state.ModuleShop.stateListDocumentsSellerShop
      if (response.message === 'Get Doucment Success.') {
        if (response.data.length !== 0) {
          this.fileFDA = response.data
          this.statusFDADoc = response.data[0].doc_status
        } else {
          this.fileFDA = []
        }
      } else {
        this.fileFDA = []
      }
    },
    openLink (link) {
      window.open(link, '_blank')
    },
    onPickFile () {
      this.$refs.fileFDA.click()
    },
    handleFileUpload (event) {
      this.$store.commit('openLoader')
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.uploadFDA(file)
          // console.log(reader.result, file)
        }
      }
    },
    async uploadFDA (file) {
      const formData = new FormData()
      formData.append('name', file.name)
      formData.append('document_fda', file)
      formData.append('seller_shop_id', localStorage.getItem('shopSellerID'))
      await this.$store.dispatch('actionsUploadFDAFile', formData)
      const response = await this.$store.state.ModuleShop.stateUploadFDAFile
      if (response.message === 'Upload Success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'อัปโหลดเอกสารสำเร็จ',
          showConfirmButton: false,
          timer: 2000
        })
        await this.ListDocumentsSellerShop()
      } else {
        this.$store.commit('closeLoader')
        if (response.message === "You don't have permission in manage setting shop.") {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'คุณไม่มีสิทธิ์ในร้านค้านี้'
          })
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: `${response.message}`
          })
        }
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    onCopy: function (e) {
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        position: 'top',
        icon: 'success',
        title: 'คัดลอกลิงก์สำเร็จ!'
      })
    },
    onError: function (e) {
      alert('Failed to copy texts')
    },
    dialogopen () {
      this.dialog = true
    },
    addshow () {
      if (this.countloop < this.list.length) {
        this.countloop = this.countloop + 1
        if (this.countloop === this.list.length) {
          this.icon = 'mdi-chevron-up'
        }
      } else {
        this.countloop = 1
        this.icon = 'mdi-chevron-down'
        window.scrollTo(0, 0)
      }
    },
    editshop () {
      var idshop = localStorage.getItem('shopSellerID')
      if (!this.MobileSize) {
        this.$router.push({ path: `/EditShop?step=1&shopID=${idshop}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/EditShopMobile?step=1&shopID=${idshop}` }).catch(() => {})
      }
    },
    // editpictuer () {
    //   if (!this.MobileSize) {
    //     this.$router.push({ path: '/EditPicturesShop' }).catch(() => {})
    //   } else {
    //     this.$router.push({ path: '/EditPicturesShopMobile' }).catch(() => {})
    //   }
    // },
    editImage () {
      window.addEventListener('load', function () {
        document.querySelector('input[type="file"]').addEventListener('change', function () {
          if (this.files && this.files[0]) {
            var img = document.querySelector('img')
            img.onload = () => {
              URL.revokeObjectURL(img.src) // no longer needed, free memory
            }
            img.src = URL.createObjectURL(this.files[0]) // set src to blob url
          }
        })
      })
    },
    async getDataitem () {
      var companyID
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var data
      if (localStorage.getItem('roleUser') !== null) {
        // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        data = {
          seller_shop_id: localStorage.getItem('shopSellerID'),
          company_id: companyID,
          role: 'seller'
        }
      } else {
        data = {
          seller_shop_id: localStorage.getItem('shopSellerID'),
          company_id: companyID,
          role: 'seller'
        }
      }
      await this.$store.dispatch('actionsShopDetailPage', data)
      var response = await this.$store.state.ModuleShop.stateShopDetailPage
      if (response.result === 'SUCCESS') {
        this.dataShop = response.data
      } else {
        if (response.message === 'Not found this shop.') {
          this.dataShop = []
        } else if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    async getDetailShop () {
      var idshop = localStorage.getItem('shopSellerID')
      if (idshop === null) {
        this.$router.push({ path: '/' })
      }
      var data = {
        seller_shop_id: idshop,
        role: 'seller'
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        this.shopDetail = response.data[0].shop_description
        this.detail = response.data[0].address_detail[0]
        this.district = response.data[0].address_detail[0].distric
        this.provice = response.data[0].address_detail[0].province
        this.sub_district = response.data[0].address_detail[0].sub_district
        this.urlname = response.data[0].url_name
        this.nameShop = response.data[0].shop_name
        this.img = response.data[0].shop_image
        this.phone = response.data[0].shop_phone.length === 0 ? '-' : response.data[0].shop_phone[0].phone !== '' ? response.data[0].shop_phone[0].phone : response.data[0].shop_phone[1].phone !== '' ? response.data[0].shop_phone[1].phone : '-'
        this.shopImg = response.data[0].shop_image.length !== 0 ? response.data[0].shop_image[0].media_path : null
        this.shopImgLazy = response.data[0].shop_image.length !== 0 ? response.data[0].shop_image[0].media_path_lazy : null
        this.taxIdShop = response.data[0].tax_id
        if (response.data[0].image_banner.length !== 0) {
          this.itemsBanner.push({
            image_path: response.data[0].image_banner[0].path,
            image_path_lazy: response.data[0].image_banner[0].path_lazy
          })
          if (response.data[0].image_banner.length > 1) {
            for (let i = 1; i < response.data[0].image_banner.length; i++) {
              this.itemsSubBanner.push({
                image_path: response.data[0].image_banner[i].path,
                image_path_lazy: response.data[0].image_banner[i].path_lazy
              })
            }
          }
        } else {
          this.itemsBanner = []
          this.itemsSubBanner = []
        }
        if (response.data[0].image_news.length !== 0) {
          this.itemsAdvert.push({
            image_path: response.data[0].image_news[0].path,
            image_path_lazy: response.data[0].image_news[0].path_lazy
          })
          if (response.data[0].image_news.length > 1) {
            for (let i = 1; i < response.data[0].image_news.length; i++) {
              this.itemsSubAdvert.push({
                image_path: response.data[0].image_news[i].path,
                image_path_lazy: response.data[0].image_news[i].path_lazy
              })
            }
          }
        } else {
          this.itemsAdvert = []
          this.itemsSubAdvert = []
        }
        if (response.data[0].shop_status === 'active') {
          this.openshop = true
        } else {
          this.openshop = false
        }
        if (response.data[0].partner_show === 'yes') {
          this.partner = true
        } else {
          this.partner = false
        }
        this.$store.commit('closeLoader')
      }
    },
    async getHomepageItems () {
      const idshop = localStorage.getItem('shopSellerID')
      const data = {
        role_user: '-1',
        company_id: -1,
        seller_shop_id: idshop
      }
      await this.$store.dispatch('actionsCountSummaryProduct', data)
      var response = await this.$store.state.ModuleShop.stateCountSummaryProduct
      if (response.ok === 'y') {
        this.itemsListProduct[0].value = await response.query_result.all
        this.itemsListProduct[1].value = await response.query_result.recommend
        this.itemsListProduct[2].value = await response.query_result.new
        this.itemsListProduct[3].value = await response.query_result.sale
        this.itemsListProduct[4].value = await response.query_result.best_seller
      } else {
        this.itemsListProduct = []
      }
      this.$store.commit('closeLoader')
      // ---------------- ของเก่า ------------------------
      // var data = ''
      // var data1 = ''
      // var data2 = ''
      // var data3 = ''
      // var data4 = ''
      // data = {
      //   role_user: 'ext_buyer',
      //   product_type: 'all',
      //   seller_shop_id: idshop
      // }
      // data1 = {
      //   role_user: 'ext_buyer',
      //   product_type: 'best-seller',
      //   seller_shop_id: idshop
      // }
      // data2 = {
      //   role_user: 'ext_buyer',
      //   product_type: 'new',
      //   seller_shop_id: idshop
      // }
      // data3 = {
      //   role_user: 'ext_buyer',
      //   product_type: 'recommend',
      //   seller_shop_id: idshop
      // }
      // data4 = {
      //   role_user: 'ext_buyer',
      //   company_id: '-1',
      //   category: '',
      //   seller_shop_id: idshop,
      //   orderBy: '',
      //   status_product: 'sale'
      // }
      // await this.$store.dispatch('actionsAllProduct', data)
      // var response = await this.$store.state.ModuleProductNode.stateAllProduct
      // await this.$store.dispatch('actionsProductBestSellerHome', data1)
      // var response1 = await this.$store.state.ModuleProductNode.stateProductBestSeller
      // await this.$store.dispatch('actionsProductNew', data2)
      // var response2 = await this.$store.state.ModuleProductNode.stateProductNew
      // await this.$store.dispatch('actionsProductRecomment', data3)
      // var response3 = await this.$store.state.ModuleProductNode.stateProductRecomment
      // await this.$store.dispatch('actionsSelectCategoryShopList', data4)
      // var response4 = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // this.GetAllProduct.allproduct = []
      // this.GetAllProduct.newArrivals = []
      // this.GetAllProduct.bestSeller = []
      // this.GetAllProduct.recommend = []
      // this.GetAllProduct.saleProduct = []
      // this.listproduct = 0
      // this.list = []
      // this.heaederlist = []
      // if (response.ok === 'y') {
      //   if (response.query_result.length !== 0) {
      //     if (response.query_result.length !== 0) {
      //       this.GetAllProduct.allproduct = response.query_result
      //       this.status = true
      //       this.StatusHomeNewProduct = true
      //       this.listproduct++
      //       this.headerlist.push('สินค้าทั้งหมด')
      //       this.itemsListProduct[0].value = this.GetAllProduct.allproduct.length
      //       this.list.push(this.GetAllProduct.allproduct)
      //     } else {
      //       this.GetAllProduct.allproduct = []
      //       this.itemsListProduct[0].value = '0'
      //       this.status = true
      //       this.StatusHomeNewProduct = false
      //     }
      //     if (response1.query_result.length !== 0) {
      //       this.GetAllProduct.bestSeller = response1.query_result
      //       this.status = true
      //       this.StatusHomeBestProduct = true
      //       this.listproduct++
      //       this.headerlist.push('สินค้าขายดี')
      //       this.itemsListProduct[4].value = this.GetAllProduct.bestSeller.length
      //       this.list.push(this.GetAllProduct.bestSeller)
      //     } else {
      //       this.GetAllProduct.bestSeller = []
      //       this.itemsListProduct[4].value = 0
      //       this.status = true
      //       this.StatusHomeBestProduct = false
      //     }
      //     if (response2.query_result.length !== 0) {
      //       this.GetAllProduct.newArrivals = response2.query_result
      //       this.status = true
      //       this.StatusHomeBestProduct = true
      //       this.listproduct++
      //       this.headerlist.push('สินค้ามาใหม่')
      //       this.itemsListProduct[2].value = this.GetAllProduct.newArrivals.length
      //       this.list.push(this.GetAllProduct.newArrivals)
      //     } else {
      //       this.GetAllProduct.newArrivals = []
      //       this.itemsListProduct[2].value = 0
      //       this.status = true
      //       this.StatusHomeBestProduct = false
      //     }
      //     if (response3.query_result.length !== 0) {
      //       this.GetAllProduct.recommend = response3.query_result
      //       this.status = true
      //       this.StatusHomeRecoment = true
      //       this.listproduct++
      //       this.headerlist.push('สินค้าแนะนำ')
      //       this.itemsListProduct[1].value = this.GetAllProduct.recommend.length
      //       this.list.push(this.GetAllProduct.recommend)
      //     } else {
      //       this.GetAllProduct.recommend = []
      //       this.itemsListProduct[1].value = 0
      //       this.status = true
      //       this.StatusHomeRecoment = false
      //     }
      //     if (response4.query_result.length !== 0) {
      //       this.GetAllProduct.saleProduct = response4.query_result
      //       this.status = true
      //       this.StatusHomeRecoment = true
      //       this.listproduct++
      //       this.headerlist.push('สินค้าลดราคา')
      //       this.itemsListProduct[3].value = this.GetAllProduct.saleProduct.length
      //       this.list.push(this.GetAllProduct.saleProduct)
      //     } else {
      //       this.GetAllProduct.saleProduct = []
      //       this.itemsListProduct[3].value = 0
      //       this.status = true
      //       this.StatusHomeRecoment = false
      //     }
      //   } else {
      //     this.GetAllProduct.allproduct = []
      //     this.GetAllProduct.newArrivals = []
      //     this.GetAllProduct.bestSeller = []
      //     this.GetAllProduct.recommend = []
      //     this.GetAllProduct.saleProduct = []
      //     this.status = true
      //     this.StatusHomeNewProduct = false
      //     this.StatusHomeBestProduct = false
      //     this.StatusHomeRecoment = false
      //   }
      // } else {
      //   const Toast = this.$swal.mixin({
      //     toast: true,
      //     showConfirmButton: false,
      //     timer: 1500,
      //     timerProgressBar: true
      //   })
      //   Toast.fire({
      //     icon: 'error',
      //     title: 'ไม่มีสินค้าในหน้าเพจ',
      //     text: 'โปรดติดต่อแอดมิน'
      //   })
      // }
    },
    handleClose (caseStatus) {
      if (caseStatus === 'shop') {
        this.dialogConfirm = false
        this.openshop = !this.openshop
      } else if (caseStatus === 'partner') {
        this.dialogConfirm = false
        this.partner = !this.partner
      }
    },
    handleClick (caseStatus) {
      if (caseStatus === 'shop') {
        this.changeStatusShop()
      } else if (caseStatus === 'partner') {
        this.changeStatusPartner()
      }
    },
    async checkDBD () {
      this.$store.commit('openLoader')
      var data = {
        shopId: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionCheckStatusDBDL', data)
      var res = this.$store.state.ModuleShop.stateCheckStatusDBDL
      if (res.code === 200) {
        this.statusDBD = res.message
        if (this.statusDBD === 'มีคำขอ DBD') {
          this.reqNo = res.data.request_no
          this.getStatus()
        } else {
          this.listBusiness()
        }
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: `${res.message}`
        })
        this.$store.commit('closeLoader')
      }
    },
    async getStatus () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (!this.reqNo || !this.taxIdShop) {
        this.$swal.fire({ icon: 'error', text: 'ไม่พบข้อมูล requestNo หรือ taxIdShop', showConfirmButton: false, timer: 2000 })
        this.$store.commit('closeLoader')
        return
      }
      try {
        const response = await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}shop/dbd_status?requestNo=${this.reqNo}&memberId=${this.taxIdShop}`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST'
        })
        if (response.data.code === 200) {
          var textStatus = response.data.message
          this.mesStatus = textStatus.split(' ')
          this.reqStatus = response.data.request_status
          this.fileDBD = response.data.download_banner_url
          if (this.reqStatus === 'U') {
            this.dialogDetailEdit = true
          }
          // handle success
        } else {
          this.$swal.fire({ icon: 'error', text: response.data.message, showConfirmButton: false, timer: 2000 })
        }
      } catch (error) {
        const message = error.response.data.message
        this.$swal.fire({ icon: 'error', text: `ตรวจสอบสถานะ DBD: ${message}`, confirmButtonText: 'ปิด', showConfirmButton: true })
        console.error('getStatus error:', error)
      }
      this.$store.commit('closeLoader')
    },
    async listBusiness () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionListBusiness')
      var res = await this.$store.state.ModuleShop.stateListBusiness
      if (res.code === 200) {
        this.itemTypeBusiness = res.data.filter(item => item.request_type === 'business')
      }
    },
    async registerDBD () {
      if (this.typeBusiness.length === 0) {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเลือกประเภทธุรกิจ',
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.dialogConfirmRegis = false
      } else {
        this.$store.commit('openLoader')
        const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        const auth = {
          headers: { Authorization: `Bearer ${oneData.user.access_token}` }
        }
        const response = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail_mp_v2`, '', auth)
        if (response.data.code !== 500) {
          var userID = response.data.data.user_id
          this.dialogRegisDBD = false
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'warning',
            html: response.data.message,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
        }
        var fileSize = this.fileConsent.size / 1024 / 1024
        if (fileSize > 1) {
          this.$swal.fire({
            icon: 'warning',
            html: 'กรุณาอัปโหลดไฟล์เอกสารรับรองไม่เกิน 1 MB',
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
          this.dialogConfirmRegis = false
          this.dialogRegisDBD = true
        } else {
          this.$store.commit('openLoader')
          var formData = new FormData()
          formData.append('tax_id', this.taxIdShop)
          formData.append('seller_shop_id', localStorage.getItem('shopSellerID'))
          formData.append('filePDF', this.fileConsent)
          formData.append('requestPaymentTypes', '4')
          formData.append('requestDeliveryTypes', '1')
          formData.append('requestReserveTypes', '1')
          formData.append('requestBusinessTypes', this.typeBusiness.join(','))
          formData.append('commercialTypeId', '6')
          formData.append('user_id', String(userID))
          await this.$store.dispatch('actionRequestDBD', formData)
          var res = await this.$store.state.ModuleShop.stateRequestDBD
          if (res.code === 200) {
            this.dialogConfirmRegis = false
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'success',
              html: 'ส่งคำขอลงทะเบียน DBD สำเร็จ',
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true
            })
            this.checkDBD()
          } else if (res.code === 500) {
            this.dialogConfirmRegis = false
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'error',
              html: res.dbdResponse.message,
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true
            })
            this.typeBusiness = []
            this.fileConsent = []
          } else {
            this.dialogConfirmRegis = false
            this.typeBusiness = []
            this.fileConsent = []
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'error',
              html: res.message,
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true
            })
          }
        }
      }
    },
    closeDialogRegis () {
      this.dialogRegisDBD = false
      this.typeBusiness = []
      this.fileConsent = []
    },
    removeShop (item) {
      const index = this.typeBusiness.indexOf(item.type_code)
      if (index >= 0) this.typeBusiness.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.HoverText {
  font-size: 13px;
  margin-top:-7px;
  margin-left:-25px;
  overflow: hidden !important;
}
.HoverText:hover {
  overflow-y: scroll !important;
}
.v-input--selection-controls {
  margin-top: 0 !important;
  padding-top: 0 !important;
}
</style>

<style lang="scss" scoped>
.tap_left {
  margin-left: 25px;
}
// textarea {
//   overflow: hidden !important;
// }
</style>
