<template>
  <v-container>
    <!-- Start List Customer Sales  -->
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="border-radius: 8px;">
      <v-card-text class="px-0">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-0' : 'px-0 py-0'">
          <v-row :class="MobileSize ? 'mx-0 d-flex' : 'd-flex'">
            <v-card-title :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'" style="font-weight: bold; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSales()">mdi-chevron-left</v-icon> รายชื่อลูกค้าที่ดูแล</v-card-title>
            <v-btn class="ml-auto" height="40" text color="#1B5DD6" @click="DownloadExcelCustomer()" v-if="!MobileSize && ((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าลูกค้า</span></v-btn>
          </v-row>
        </v-col>
        <v-col cols="12" align="end" v-if="MobileSize && ((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)">
          <v-btn text color="#1B5DD6" @click="DownloadExcelCustomer()" ><span style="font-size: 14px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าลูกค้า</span></v-btn>
        </v-col>
        <!-- Tab list Customer -->
        <v-col cols="12">
          <v-row dense>
            <v-tabs
              v-model="tab"
              background-color="transparent"
              @change="changeTab(tab)"
            >
              <v-tab
               v-for="item in itemTab"
               :key="item"
              >
                {{ item }}
              </v-tab>
            </v-tabs>
          </v-row>
        </v-col>
        <v-col cols="12" md="4" sm="6" class="mt-3 ">
          <v-text-field v-model="searchCustomer" outlined style="border-radius: 8px;" dense :placeholder="tab === 0 ? 'ค้นหาจากรายชื่อลูกค้าหรือรหัส sale' : 'ค้นหาจากรายชื่อบริษัทหรือรหัส sale'" hide-details></v-text-field>
        </v-col>
        <v-col cols="12" class="mb-2">
          <v-row dense no-gutters>
            <v-col cols="12" md="3" align="start" class="pt-3">
              <span v-if="tab === 0" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อลูกค้าบุคคลธรรมดาทั้งหมด {{ showCountOrder }} คน</span>
              <span v-if="tab === 1" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อลูกค้าบริษัท นิติบุคคลทั้งหมด {{ showCountOrder }} คน</span>
              <span v-if="tab === 2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อ Vendor ทั้งหมด {{ showCountOrder }} คน</span>
            </v-col>
            <v-col cols="12" md="9" :align="IpadSize? 'start' : 'end'" v-if="((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)">
              <v-btn color="primary" :block="MobileSize? true : false" rounded outlined height="40" :width="IpadSize? '155' : ''" @click="ImportCustomer()" :class="MobileSize? 'mt-2': 'mr-1'">Import ข้อมูลลูกค้า</v-btn>
              <input @click="event => event.target.value = null" @change="UploadExcelCustomer($event)" id="importExcelCustomer" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
              <v-btn color="primary" :block="MobileSize? true : false" rounded outlined height="40" :width="IpadSize? '155' : ''" @click="ExportCustomer()" :class="MobileSize? 'mt-2': 'mr-1'">Export ข้อมูลลูกค้า</v-btn>
              <v-btn color="primary" :block="MobileSize? true : false" rounded height="40" @click="addCustomer()" :class="MobileSize ? 'mt-2' : ''">เพิ่มข้อมูลลูกค้า</v-btn>
            </v-col>
          </v-row>
        </v-col>
        <!-- Table List Customer -->
        <v-row dense justify="center">
          <v-card width="100%" elevation="0" outlined>
            <v-data-table
              :headers="tab === 0 ? headerListGeneralCustomer : tab === 1 ? headerListBusinessCustomer : headerListVendorCustomer"
              :items="itemsCustomer"
              :items-per-page="10"
              :search="searchCustomer"
              :footer-props="{'items-per-page-text': 'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
              no-results-text="ไม่พบรายชื่อลูกค้าในตาราง"
              no-data-text="ไม่มีรายชื่อลูกค้าในตาราง"
              @pagination="countCustomer"
              :style="IpadSize ? 'max-width: 100%; overflow: hidden !important;' : ''"
            >
              <template v-slot:[`item.details`]="{ item }">
                <v-row dense class="d-flex justify-center">
                  <!-- Detail Button -->
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="#757D8A"
                        icon
                        outlined
                        height="40"
                        width="40"
                        class="mr-2"
                        v-bind="attrs"
                        v-on="on"
                        style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                        @click="openDialogDetailCustomer(item)"
                      >
                        <v-icon color="#757D8A" size="24">mdi-file-document-outline</v-icon>
                      </v-btn>
                    </template>
                    <span>รายละเอียดลูกค้า</span>
                  </v-tooltip>
                  <!-- Edit Button -->
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="primary"
                        icon
                        outlined
                        height="40"
                        width="40"
                        v-bind="attrs"
                        v-on="on"
                        v-if="((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)"
                        style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                        @click="editDialogCustomer(item)"
                      >
                        <v-icon color="primary" size="24">mdi-pencil</v-icon>
                      </v-btn>
                    </template>
                    <span>แก้ไขข้อมูลลูกค้า</span>
                  </v-tooltip>
                </v-row>
              </template>
            </v-data-table>
          </v-card>
        </v-row>
      </v-card-text>
    </v-card>
    <v-dialog v-model="dialogDetailCustomer" width="600" persistent>
      <v-card style="border-radius: 24px; background-color: #27AB9C;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">รายละเอียดลูกค้า</span>
          <v-btn fab small @click="dialogDetailCustomer = !dialogDetailCustomer" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card class="pa-4 px-8">
          <v-row no-gutters>
            <v-col cols="4" align="center">
              <v-icon size="80" :color="customerData.status === 'active'? '#27AB9C' : '#F55A5A'">mdi-account-circle</v-icon>
            </v-col>
            <v-col cols="8">
                <span>ชื่อ - นามสกุล : </span><span>{{customerData.cus_name ? customerData.cus_name: '-'}}</span><br>
                <span>รหัสลูกค้า : </span><span>{{customerData.cus_code ? customerData.cus_code: '-'}}</span><br>
                <span>tier_id : </span><span>{{customerData.tier_id ? customerData.tier_id: '-'}}</span><br>
                <span>tier_level : </span><span>{{customerData.tier_level ? customerData.tier_level: '-'}}</span><br>
                <span>หมายเหตุ : </span><span>{{customerData.remark ? customerData.remark: '-'}}</span><br>
                <span>status : </span><span :style="customerData.status === 'active'? 'color: #27AB9C;':'color: red;'"> <v-icon  :color="customerData.status === 'active'? '#27AB9C':'#F55A5A'" class="ml-n2">mdi-circle-small</v-icon>{{customerData.status ? customerData.status: '-'}}</span><br>
            </v-col>
          </v-row>
          <v-col cols="12" align="center" v-if="!MobileSize">
            <v-btn class="mr-2" color="primary" @click="openListAddress('')" rounded height="40">รายละเอียดที่อยู่จัดส่งสินค้า</v-btn>
            <v-btn class="ml-2" color="primary" @click="openListInvoice('')" rounded height="40">รายละเอียดที่อยู่ใบกำกับภาษี</v-btn>
          </v-col>
          <v-row v-if="MobileSize">
            <v-col cols="12" align="center">
              <v-btn color="primary" @click="openListAddress('')" rounded height="40">รายละเอียดที่อยู่จัดส่งสินค้า</v-btn>
            </v-col>
            <v-col cols="12" align="center">
              <v-btn color="primary" @click="openListInvoice('')" rounded height="40">รายละเอียดที่อยู่ใบกำกับภาษี</v-btn>
            </v-col>
          </v-row>
        </v-card>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogResultImportProduct" width="600" persistent>
      <v-card style="border-radius: 24px; background-color: #27AB9C;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">ผลลัพธ์ข้อมูลการนำเข้า</span>
          <v-btn fab small @click="DialogResultImportProduct = !DialogResultImportProduct" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card :class="MobileSize? 'pa-2 px-8' : 'pa-4 px-8'">
          <v-row no-gutters>
            <v-col cols="12" align="start">
              <span :style="MobileSize? 'font-size: 14px;': 'font-size: 18px;'" style=" font-weight: 500; color: #333333;">รายการผลลัพธ์ข้อมูลนำเข้าไม่สำเร็จ {{ dataError.length }} รายการ</span>
              <!-- <v-col cols="12">
                  <v-col cols="12" class="pa-0" v-if="dataErrorCus.length !== 0">
                    <v-row no-gutters v-for="(itemErrorCus, i1) in dataErrorCus" :key="i1">
                      <span>รหัสลูกค้า {{itemErrorCus.cusCode}} ซ้ำ</span>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="pa-0" v-if="dataErrorEmty.length !== 0">
                    <v-row no-gutters v-for="(itemErrorCus, i1) in dataErrorEmty" :key="i1">
                      <span>{{itemErrorCus.cusCode}} ไม่สามารถเป็นค่าว่างได้</span>
                    </v-row>
                  </v-col>
              </v-col> -->
              <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                <v-data-table
                  :headers="headTableImportNoSuccess"
                  :items="dataError"
                  :items-per-page="10"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  style="overflow-x: hidden !important;"
                >
                  <template v-slot:[`item.index`]="{ item }">
                    {{ dataError.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                  </template>
                </v-data-table>
              </v-col>
            </v-col>
            <!-- <v-col cols="4" align="center">
              <v-icon size="80" :color="customerData.status === 'active'? '#27AB9C' : '#F55A5A'">mdi-account-circle</v-icon>
            </v-col> -->
          </v-row>
          <v-row v-if="MobileSize">
            <v-col cols="12" style="border-radius: 8px;" class="pa-0" v-if="MobileSize">
                <v-data-table
                  class="pa-0"
                  :headers="headTableImportNoSuccessMobile"
                  :items="dataError"
                  :items-per-page="10"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  style="overflow-x: hidden !important;"
                >
                  <template v-slot:[`item.index`]="{ item }">
                    {{ dataError.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                  </template>
                </v-data-table>
              </v-col>
          </v-row>
        </v-card>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogListAddressCustomer" width="650" persistent>
      <v-card style="border-radius: 24px; background-color: #27AB9C;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'">เลือก{{titleList}}
          </span>
          <v-btn icon dark @click="close()">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card class="pa-4">
          <v-col cols="12" class="pt-2">
            <v-row dense class="mt-0" vt-if="userdetail.length !== 0">
              <v-col cols="6">
                <span :style="!MobileSize? 'font-size:18px;': 'font-size:14px'">รายการที่อยู่ทั้งหมด {{totalAddress}} รายการ</span>
              </v-col>
              <v-col cols="6" align="end" v-if="((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)">
                <v-btn v-if="titleList === 'ที่อยู่จัดส่งสินค้า'" color="#27AB9C" style="color: white;" rounded @click="addAddressCustomer('addAddress','')">เพิ่มที่อยู่ใหม่</v-btn>
                <v-btn v-else color="#27AB9C" style="color: white;" rounded @click="addAddressInvoiceCustomer('addAddress','')">เพิ่มที่อยู่ใหม่</v-btn>
              </v-col>
              <v-col cols="12" class="pt-2" v-for="(item, index) in userdetail" :key="index">
                <v-card min-height="149" elevation="0" outlined style="border-radius: 8px;" :style="item.default_address === 'Y' ? 'border-color: #27AB9C' : 'border-color: #C4C4C4'">
                <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
                  <v-row class="pa-4">
                    <v-col cols="12" md="12">
                      <v-row dense>
                        <!-- action -->
                        <v-col cols="6" md="6" class="d-flex">
                          <div class="mr-auto" >
                            <span style="font-weight: 700; font-size: 18px;">{{ item.name }}</span>
                          </div>
                        </v-col>
                        <v-col v-if="titleList === 'ที่อยู่จัดส่งสินค้า'" cols="6" md="6" class="" align="end"><v-btn v-if="((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)" rounded outlined color="#27AB9C" @click="editAddressCus('edit', item)">แก้ไข</v-btn></v-col>
                        <v-col v-else cols="6" md="6" class="" align="end"><v-btn v-if="((saleIDInpage === ownSaleID && CheckEditCustomer === 'Y') || checkAdminSale)" outlined rounded color="#27AB9C" @click="editInvoiceAddressCus('edit', item)">แก้ไข</v-btn></v-col>
                        <v-col cols="12" md="12">
                          <span v-snip="3" style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.detail_address }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.zip_code }}</span>
                        </v-col>
                        <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                        <v-col cols="12" md="12">
                          <v-radio-group v-model="item.default_address">
                            <v-radio
                              color="#27AB9C"
                              label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                              value="Y"
                              @click="setDefaultAdress(item)"
                              style="color: #333333"
                            ></v-radio>
                          </v-radio-group>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
              <v-col cols="12" align="end">
                <v-pagination
                  color="#27AB9C"
                  v-model="pageAddress"
                  :length="pageMaxAddress"
                  circle
                > </v-pagination>
                </v-col>
            </v-row>
          </v-col>
        </v-card>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogListEditCustomer" width="650" persistent>
      <v-card style="border-radius: 24px; background-color: #27AB9C;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-weight: 700; color: #FFFFFF;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">เลือกข้อมูลการแก้ไข
          </span>
          <v-btn icon dark @click="dialogListEditCustomer = false">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card class="pa-4" :height="!MobileSize? '400':'250'">
          <v-col cols="12">
            <v-row no-gutters>
              <v-col cols="4">
                <v-card :class="!MobileSize? 'cardChooseType':'cardChooseTypeMobile'" @click="editDataCustomer('')">
                  <v-icon class="colorChange" :size="!MobileSize? '100': '80'">mdi-account-edit</v-icon>
                  <h2 style="color:#27AB9C; font-weight: 600;" :class="!MobileSize? 'pt-8':'pt-2'">แก้ไข</h2>
                  <span :style="!MobileSize? 'font-size: 16px;':'font-size: 10px;'">ข้อมูลลูกค้า</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-2">
                <v-card :class="!MobileSize? 'cardChooseType':'cardChooseTypeMobile'" @click="openListAddress('edit')">
                  <v-icon class="colorChange" :size="!MobileSize? '100': '80'">mdi-home-edit</v-icon>
                  <h2 style="color:#27AB9C; font-weight: 600;" :class="!MobileSize? 'pt-8':'pt-2'">แก้ไข</h2>
                  <span :style="!MobileSize? 'font-size: 16px;':'font-size: 10px;'" >ที่อยู่ในการจัดส่งลูกค้า</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-2">
                <v-card :class="!MobileSize? 'cardChooseType':'cardChooseTypeMobile'" @click="openListInvoice('edit')">
                  <v-icon class="colorChange" :size="!MobileSize? '100': '80'">mdi-file-document-edit</v-icon>
                  <h2 style="color:#27AB9C; font-weight: 600;" :class="!MobileSize? 'pt-8':'pt-2'">แก้ไข</h2>
                  <span :style="!MobileSize? 'font-size: 16px;':'font-size: 10px;'" >ที่อยู่ใบกำกับภาษีลูกค้า</span>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
        </v-card>
      </v-card>
    </v-dialog>
    <ModalManageCustomerSale ref="ModalManageCustomerSale" />
    <EditModalAddress ref="EditModalAddress"/>
    <EditModalEtaxAddress ref="EditModalEtaxAddress"/>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import axios from 'axios'
export default {
  components: {
    ModalManageCustomerSale: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/CustomerSaleModel'),
    EditModalAddress: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditAddressCustomerSale'),
    EditModalEtaxAddress: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditETAXAddressCustomerSale')
  },
  data () {
    return {
      dataErrorCus: [],
      dataErrorEmty: [],
      dataError: [],
      DialogResultImportProduct: false,
      SaleVendor: false,
      dataOfSale: [],
      listGeneralCustomer: [],
      listVendorCustomer: [],
      listBusinessCustomer: [],
      tab: 0,
      itemTab: [
        'บุคคลธรรมดา',
        'บริษัท นิติบุคคล'
        // 'vendor'
      ],
      showCountOrder: 0,
      headerListGeneralCustomer: [
        { text: 'ชื่อ - นามสกุล', value: 'cus_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส Sale', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'กลุ่ม', value: 'tier_name', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerListVendorCustomer: [
        { text: 'ชื่อ - นามสกุล', value: 'cus_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส Sale', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'กลุ่ม', value: 'tier_name', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerListBusinessCustomer: [
        { text: 'บริษัท', value: 'cus_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส Sale', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'กลุ่ม', value: 'tier_name', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headTableImportNoSuccess: [
        { text: 'โรล', value: 'caseIndex', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส cusutomer code', value: 'CustomerCode', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การตรวจสอบข้อมูล', value: 'message', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headTableImportNoSuccessMobile: [
        { text: 'โรล', value: 'caseIndex', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส cusutomer code', value: 'CustomerCode', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การตรวจสอบข้อมูล', value: 'message', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      itemsCustomer: [],
      searchCustomer: '',
      customerData: [],
      dialogDetailCustomer: false,
      titleList: '',
      dialogListAddressCustomer: false,
      dialogListEditCustomer: false,
      cusID: '',
      cusCode: '',
      pageAddress: 1,
      userdetail: [],
      totalAddress: 0,
      pageMaxAddress: null,
      titleAddress: '',
      cusType: '',
      page: '',
      checkAdminSale: false,
      dataDetail: [],
      ownSaleID: '',
      CheckEditCustomer: 'N',
      saleIDInpage: ''
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$on('getListCustomerOfSales', this.getListCustomerOfSales)
    this.$store.commit('openLoader')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    if (localStorage.getItem('list_shop_detail') !== null) {
      this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      this.ownSaleID = this.dataDetail.sale_id
      if (this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        this.checkAdminSale = true
      } else {
        this.checkAdminSale = false
      }
    }
    if (localStorage.getItem('Detail_sales') !== null && (this.$router.currentRoute.query.sale_code !== undefined && this.$router.currentRoute.query.sale_code !== '')) {
      this.dataOfSale = await JSON.parse(Decode.decode(localStorage.getItem('Detail_sales')))
      localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'general' }))
      await this.getListCustomerOfSales()
    } else {
      if (this.MobileSize()) {
        this.$router.push({ path: '/listSalesMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listSales' }).catch(() => {})
      }
    }
  },
  watch: {
    pageAddress (val) {
      // console.log('valpageAddress', val)
      this.openDialogDetail(this.cusID)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/listCustomerSalesMobile?sale_code=${this.dataOfSale.sale_code}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/listCustomerSales?sale_code=${this.dataOfSale.sale_code}` }).catch(() => {})
      }
    }
  },
  mounted () {
    this.$EventBus.$on('EditComplete', this.openDialogDetail)
    this.$EventBus.$on('closeEdit', this.closeDialog)
    this.$on('hook:beforeDestory', () => {
      this.$EventBus.$off('getListCustomerOfSales')
      this.$EventBus.$off('closeEdit')
    })
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoSales () {
      if (this.MobileSize) {
        this.$router.push({ path: '/listSalesMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listSales' }).catch(() => {})
      }
    },
    async getListCustomerOfSales () {
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        sale_id: this.dataOfSale.id,
        from: 'shop'
      }
      await this.$store.dispatch('actionsListCustomerOfSales', data)
      const response = await this.$store.state.ModuleSaleOrder.stateListCustomerOfSales
      if (response.message === 'Get detail sales data successfully.') {
        this.$store.commit('closeLoader')
        this.saleIDInpage = await response.data[0].id
        this.CheckEditCustomer = await response.data[0].manage_customer
        this.listBusinessCustomer = await response.data[0].business_customer
        this.listGeneralCustomer = await response.data[0].general_customer
        this.listVendorCustomer = await response.data[0].vendor_customer
        var getSelectType = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
        this.itemsCustomer = []
        if (getSelectType === 'general') {
          this.itemsCustomer = this.listGeneralCustomer
          this.tab = 0
          // this.SaleVendor = false
        } else if (getSelectType === 'vendor') {
          this.itemsCustomer = this.listVendorCustomer
          // this.SaleVendor = true
          this.tab = 2
        } else {
          this.itemsCustomer = this.listBusinessCustomer
          this.tab = 1
          // this.SaleVendor = false
        }
      } else {
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      }
    },
    getMessage (msg) {
      if (msg === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
        // return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Data missing. Please check your parameter and try again.') {
        return 'ข้อมูลขาดหาย โปรดตรวจสอบและลองอีกครั้ง'
      } else if (msg === 'Shop not found.') {
        return 'ไม่พบร้านค้า'
      } else if (msg === 'Your data not found in this shop.') {
        return 'ไม่พบข้อมูลของคุณในร้านค้านี้'
      } else if (msg === 'Sales man not found.') {
        return 'ไม่พบข้อมูลฝ่ายขาย'
      } else {
        return 'An error has occurred. Please try again in an hour or two.'
      }
    },
    changeTab (tabValue) {
      this.itemsCustomer = []
      if (tabValue === 0) {
        this.itemsCustomer = this.listGeneralCustomer
        localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'general' }))
        // this.SaleVendor = false
      } else if (tabValue === 2) {
        this.itemsCustomer = this.listVendorCustomer
        localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'vendor' }))
        // this.SaleVendor = true
      } else {
        this.itemsCustomer = this.listBusinessCustomer
        localStorage.setItem('sale_order_customer', JSON.stringify({ role_customer: 'business' }))
        // this.SaleVendor = false
      }
    },
    countCustomer (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    openDialogDetailCustomer (itemCus) {
      this.cusID = itemCus.id
      this.cusCode = itemCus.cus_code
      this.customerData = itemCus
      this.dialogDetailCustomer = true
    },
    async openListAddress (actions) {
      await this.getAddress(this.cusID, this.cusCode)
      this.titleList = 'ที่อยู่จัดส่งสินค้า'
      this.dialogListAddressCustomer = true
      this.dialogListEditCustomer = false
      if (actions !== '') {
        this.$EventBus.$emit('actionSale', actions)
      }
    },
    async openListInvoice (actions) {
      await this.getInvoice(this.cusID, this.cusCode)
      this.titleList = 'ที่อยู่ใบกำกับภาษี'
      this.dialogListAddressCustomer = true
      this.dialogListEditCustomer = false
      this.$EventBus.$emit('actionSale', actions)
    },
    openDialogDetail (id, data) {
      // console.log('idid', id, data)
      if (data === undefined) {
        if (this.titleList === 'ที่อยู่ใบกำกับภาษี') {
          this.getInvoice(id)
        } else {
          this.getAddress(id)
        }
      }
    },
    async getAddress (id, cusCode) {
      this.cusID = id
      if (cusCode !== undefined) {
        this.cusCode = cusCode
      }
      if (this.pageAddress !== 1) {
        // window.scrollTo(0, 0)
      }
      var data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.cusID,
        page: this.pageAddress === null ? 1 : this.pageAddress
      }
      localStorage.setItem('partner_id', this.cusID)
      await this.$store.dispatch('actionsGetListCustomerAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerAddress
      if (res.result === 'SUCCESS') {
        this.userdetail = res.data.address
        this.totalAddress = res.data.total_address
        this.pageMaxAddress = parseInt(res.data.total_address / 5) === 0 ? 1 : Math.ceil(res.data.total_address / 5)
      } else {
        this.$swal.fire({ icon: 'error', text: this.getMessage(res.message), showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      }
      // this.dialogListPartner = false
    },
    async getInvoice (id, cusCode) {
      this.cusID = id
      if (cusCode !== undefined) {
        this.cusCode = cusCode
      }
      if (this.pageAddress !== 1) {
        // window.scrollTo(0, 0)
      }
      var data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.cusID,
        page: this.pageAddress === null ? 1 : this.pageAddress
      }
      await this.$store.dispatch('actionsGetListCustomerInvAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerInvAddress
      if (res.result === 'SUCCESS') {
        this.userdetail = res.data.address
        this.totalAddress = res.data.total_address
        this.pageMaxAddress = parseInt(res.data.total_address / 5) === 0 ? 1 : Math.ceil(res.data.total_address / 5)
      } else {
        this.dialogListAddressCustomer = false
        this.$swal.fire({ icon: 'error', text: this.getMessage(res.message), showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      }
      // this.dialogListPartner = false
    },
    addCustomer () {
      // console.log('SaleVendor', this.SaleVendor)
      this.$refs.ModalManageCustomerSale.open('เพิ่มข้อมูลลูกค้า', 'listCustomerSale', this.dataOfSale, undefined, this.SaleVendor)
    },
    async setDefaultAdress (item) {
      localStorage.setItem('partner_id', this.cusID)
      var res
      var data
      if (this.titleList === 'ที่อยู่ใบกำกับภาษี') {
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          role: 'seller',
          cus_id: this.cusID,
          address_id: item.id,
          email: item.email,
          name: item.name,
          first_name: item.first_name,
          last_name: item.last_name,
          phone: item.phone,
          tax_id: item.tax_id,
          house_no: item.house_no,
          room_no: item.room_no,
          floor: item.floor,
          building: item.building,
          moo_ban: item.moo_ban,
          moo_no: item.moo_no,
          soi: item.soi,
          yaek: item.yaek,
          street: item.street,
          sub_district: item.sub_district,
          district: item.district,
          province: item.province,
          postcode: item.postcode,
          invoice_address_id: item.id,
          default_address: item.default_address
        }
        await this.$store.dispatch('actionsUpdateCustomerInvAddress', data)
        res = await this.$store.state.ModuleUser.stateUpdateCustomerInvAddress
      } else {
        data = {
          seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
          role: 'seller',
          cus_id: this.cusID,
          address_id: item.id,
          name: item.name,
          first_name: item.first_name,
          last_name: item.last_name,
          phone: item.phone,
          house_no: item.house_no,
          room_no: item.room_no,
          floor: item.floor,
          building: item.building,
          moo_ban: item.moo_ban,
          moo_no: item.moo_no,
          soi: item.soi,
          yaek: item.yaek,
          street: item.street,
          sub_district: item.sub_district,
          district: item.district,
          province: item.province,
          postcode: item.postcode,
          default_address: item.default_address
        }
        await this.$store.dispatch('actionsUpdateCustomerAddress', data)
        res = await this.$store.state.ModuleSaleOrder.stateUpdateCustomerAddress
      }
      if (this.cusType === 'general') {
        localStorage.setItem('AddressCustomerDetail', Encode.encode(data))
      } else {
        localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(data))
      }
      if (res.message === 'Update customer address successfully.' || res.message === 'Update customer invoice address success.') {
        this.$swal.fire({ icon: 'success', title: `<h5>ตั้งค่าเป็น${this.titleList}เริ่มต้นแล้ว</h5>`, showConfirmButton: false, timer: 1500 })
        this.userdetail = [...res.data]
        this.dialogListAddressCustomer = false
        this.pageAddress = 1
      } else {
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</h5>', showConfirmButton: false, timer: 1500 })
      }
    },
    editDialogCustomer (val) {
      this.cusID = val.id
      this.cusCode = val.cus_code
      this.customerData = val
      this.dataCustomer = val
      this.dialogListEditCustomer = true
    },
    editDataCustomer () {
      this.$refs.ModalManageCustomerSale.open('แก้ไขข้อมูลลูกค้า', 'listCustomerSale', this.dataCustomer.sale_id, this.dataCustomer, this.SaleVendor)
    },
    addAddressCustomer (actions, item) {
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.id,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        postcode: ''
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'listCustomerSale'
      this.$EventBus.$emit('actionSale', actions)
      this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    addAddressInvoiceCustomer (actions) {
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.id,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        postcode: ''
      }
      if (this.cusType === 'general') {
        localStorage.setItem('AddAddressCustomerSale', Encode.encode(val))
      } else {
        localStorage.setItem('AddAddressCustomerBussinessSale', Encode.encode(val))
      }
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'listCustomerSale'
      this.$EventBus.$emit('actionSale', actions)
      this.$EventBus.$emit('getCustomDetail', val.cus_id)
      this.$refs.EditModalEtaxAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    editAddressCus (actions, item) {
      this.EditAddressDetail = ''
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.id,
        name: item.name,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        address_id: item.id,
        default_address: item.default_address
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
      this.page = 'listCustomerSale'
      this.$EventBus.$emit('actionSale', actions)
      this.$EventBus.$emit('itemEdit', this.customerSaleData)
      // console.log('customerSaleData', this.customerSaleData)
      this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
      // this.$EventBus.$emit('EditModalAddress', val, this.titleAddress, this.page, actions)
    },
    editInvoiceAddressCus (actions, item) {
      this.EditAddressDetail = ''
      var val = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        role: 'seller',
        cus_id: this.customerData.id,
        name: item.name,
        email: item.email,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        tax_id: item.tax_id,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        address_id: item.id,
        default_address: item.default_address,
        invoice_address_id: item.id
      }
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      this.customerSaleData = this.customerData
      this.EditAddressDetail = val
      this.titleAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
      this.page = 'listCustomerSale'
      this.$EventBus.$emit('actionSale', actions)
      this.$EventBus.$emit('itemEdit', this.customerSaleData)
      // console.log('customerSaleData2', this.customerSaleData)
      this.$refs.EditModalEtaxAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
    },
    close () {
      this.dialogListAddressCustomer = false
      this.pageAddress = 1
    },
    ImportCustomer () {
      document.getElementById('importExcelCustomer').click()
    },
    async UploadExcelCustomer (e) {
      this.$store.commit('openLoader')
      const file = e.target.files
      const f = file[0]
      var data = new FormData()
      data.append('seller_shop_id', parseInt(localStorage.getItem('shopSellerID')))
      data.append('customers', f)
      data.append('sale_code', this.dataOfSale.sale_code)
      // console.log('this.dataOfSale', this.dataOfSale)
      await this.$store.dispatch('actionsImportCustomer', data)
      var response = await this.$store.state.ModuleSaleOrder.stateImportCustomer
      // console.log('response', response.data.success.length)
      if (response.data.success.length !== 0 && response.data.errors.length === 0) {
        this.$store.commit('closeLoader')
        await this.getListCustomerOfSales()
        this.$swal.fire({ icon: 'success', text: 'นำเข้าสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
      } else if (response.data.success.length !== 0 && response.data.errors.length !== 0) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'นำเข้าสินค้าสำเร็จบางชิ้นและสินค้าบางชิ้นนำเข้าไม่สำเร็จ โปรดตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 1500 })
        this.dataError = []
        if (response.data.errors[0].message === 'ชื่อคอลัมน์ไม่ถูกต้อง') {
          this.dataError = response.data.errors.slice(1)
        } else {
          this.dataError = response.data.errors
        }
        await this.getListCustomerOfSales()
        this.DialogResultImportProduct = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'นำเข้าสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
        this.dataError = []
        if (response.data.errors[0].message === 'ชื่อคอลัมน์ไม่ถูกต้อง') {
          this.dataError = response.data.errors.slice(1)
        } else {
          this.dataError = response.data.errors
        }
        this.DialogResultImportProduct = true
      }
    },
    async ExportCustomer () {
      await axios({
        url: `${process.env.VUE_APP_BACK_END2}exports/users/excel/${this.dataOfSale.sale_code}`,
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        // console.log(response)
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        fileLink.setAttribute('download', 'รายชื่อลูกค้าทั้งหมด.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async DownloadExcelCustomer () {
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_user_general_template`,
        method: 'GET'
      }).then((response) => {
        var fileURL = response.data.data
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        // fileLink.setAttribute('href', fileURL)
        // fileLink.setAttribute('sandbox', 'allow-downloads, allow-scripts')
        fileLink.setAttribute('download', 'import_customer_template.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    closeDialog () {
      this.dialogListEditCustomer = false
    }
  }
}
</script>

<style scoped>
.cardChooseTypeMobile{
  height: 200px;
  padding-top: 10px;
  text-align: center;
  border-radius: 10px;
}
.cardChooseType{
  height: 350px;
  padding-top: 35px;
  text-align: center;
  border-radius: 10px;
}
.colorChange:hover{
  color: #27AB9C;
}
.cardChooseType:hover {
  transform: scale(1.05);
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
  border-bottom: 1px solid #27AB9C !important;
}
</style>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
