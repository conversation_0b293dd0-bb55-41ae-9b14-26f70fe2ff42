server {
    listen 80;
    #listen 443 ssl;
    #ssl on;
    #ssl_certificate /etc/ssl/one.th/one.th.crt;
    #ssl_certificate_key /etc/ssl/one.th/one.th.key;
    server_name inet-b2b.one.th;
    access_log off;
    error_log off;
    large_client_header_buffers 4 32k;
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html =404;
        # expires -1;

        # kill cache
        add_header Last-Modified $date_gmt;
        add_header Cache-Control 'no-store, no-cache';
        if_modified_since off;
        expires off;
    }
}
