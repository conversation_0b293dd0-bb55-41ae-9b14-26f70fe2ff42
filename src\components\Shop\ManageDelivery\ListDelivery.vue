<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รายการใบส่งสินค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายการใบส่งสินค้า
      </v-card-title>

      <v-col cols="12">
        <v-btn
          depressed
          class="ma-2"
          :class="{ 'v-btn--active': activeTab === 'order' }"
          @click="changeTab('order')"
          x-large
          style="border-radius: 12px;"
        >
        <v-avatar rounded size="45" class="pr-2">
          <v-img contain src="@/assets/shopDelivery/delivery.png"></v-img>
        </v-avatar>
          รายการสั่งซื้อสินค้า
        </v-btn>
        <v-btn
          depressed
          class="ma-2"
          :class="{ 'v-btn--active': activeTab === 'delivery' }"
          @click="changeTab('delivery')"
          x-large
          style="border-radius: 12px;"
        >
          <v-avatar rounded size="45" class="pr-2">
            <v-img contain src="@/assets/shopDelivery/delivery2.png"></v-img>
          </v-avatar>
          ใบส่งสินค้า
        </v-btn>
      </v-col>

      <div v-if="activeTab === 'order'">
        <v-col cols="12">
          <v-row>
            <v-col v-if="!MobileSize && !IpadSize" cols="12" class="d-flex flex-row" style="align-items: center;">
              <v-text-field v-model="search" @keyup="searchData(search)" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details style="border-radius: 8px;">
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
              <span class="pl-3" style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                สถานะสั่งซื้อ :
              </span>
              <v-col cols="3">
                <v-select
                    class="setCustomSelect"
                    v-model="statePayType"
                    :items="['ทั้งหมด','รอชำระเงิน','ชำระเงินแล้ว']"
                    placeholder="ทั้งหมด"
                    @change="selectType()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
              <span class="pl-3" style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                สถานะส่งสินค้า :
              </span>
              <v-col cols="3">
                <v-select
                    class="setCustomSelect"
                    v-model="stateOrderStatus"
                    :items="['ทั้งหมด','รอสร้างใบจัดส่ง', 'กำลังจัดส่งสินค้า', 'จัดส่งสินค้าสำเร็จ']"
                    placeholder="ทั้งหมด"
                    @change="selectOrderStatus()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <v-text-field v-model="search" @keyup="searchData(search)" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details style="border-radius: 8px;">
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                สถานะสั่งซื้อ :
              </span>
              <v-col class="pa-2">
                <v-select
                    class="setCustomSelect"
                    v-model="statePayType"
                    :items="['ทั้งหมด','รอชำระเงิน','ชำระเงินแล้ว']"
                    placeholder="ทั้งหมด"
                    @change="selectType()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                สถานะส่งสินค้า :
              </span>
              <v-col class="pa-2">
                <v-select
                    class="setCustomSelect"
                    v-model="stateOrderStatus"
                    :items="['ทั้งหมด','รอสร้างใบจัดส่ง', 'กำลังจัดส่งสินค้า', 'จัดส่งสินค้าสำเร็จ']"
                    placeholder="ทั้งหมด"
                    @change="selectOrderStatus()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
          </v-row>
        </v-col>
      </div>

      <div v-if="activeTab === 'delivery'">
        <v-col cols="12">
          <v-row>
            <v-col v-if="!MobileSize && !IpadSize" cols="12" class="d-flex flex-row" style="align-items: center;">
              <v-text-field v-model="searchDelivery" @keyup="searchData(searchDelivery)" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details style="border-radius: 8px;">
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
              <span class="pl-3" style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                สถานะรายการ :
              </span>
              <v-col cols="3">
                <v-select
                    class="setCustomSelect"
                    v-model="stateOrderStatusDelivery"
                    :items="['ทั้งหมด','รอจัดส่งสินค้า', 'กำลังจัดส่งสินค้า', 'จัดส่งสินค้าสำเร็จ']"
                    placeholder="ทั้งหมด"
                    @change="selectOrderStatusDelivery()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <v-text-field v-model="searchDelivery" @keyup="searchData(searchDelivery)" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details style="border-radius: 8px;">
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                สถานะรายการ :
              </span>
              <v-col class="pa-2">
                <v-select
                    class="setCustomSelect"
                    v-model="stateOrderStatusDelivery"
                    :items="['ทั้งหมด','รอจัดส่งสินค้า', 'กำลังจัดส่งสินค้า', 'จัดส่งสินค้าสำเร็จ']"
                    placeholder="ทั้งหมด"
                    @change="selectOrderStatusDelivery()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
          </v-row>
        </v-col>
      </div>

      <div>
        <v-col cols="12">
          <div v-if="activeTab === 'order'">
            <span style="font-size: 16px;">รายการสั่งซื้อสินค้าทั้งหมด {{ totalOrders }} รายการ</span>
            <br>
            <br>
            <v-data-table
              :headers="headersOrders"
              :items="orders"
              :page.sync="page"
              :footer-props="{'items-per-page-text':'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
              :items-per-page="options.itemsPerPage"
              :server-items-length="Number(totalOrders)"
              :options.sync="options"
              @update:options="updateOptions"
              class="elevation-1"
              :class="!IpadSize && !MobileSize && !IpadProSize ? 'table-orders' : ''"
            >
              <template v-slot:[`item.payType`]="{ item }">
                <v-chip v-if="item.payType === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                <v-chip v-else-if="item.payType === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                <v-chip v-else-if="item.payType === 'general'" text-color="#9A9A9A" color="#EBEBEB">General</v-chip>
                <span v-else>-</span>
              </template>

              <template v-slot:[`item.orderStatus`]="{ item }">
                <div>
                  <span v-if="item.orderStatus === 'Not Paid'" style="color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รอชำระเงิน</span>
                  <span v-else-if="item.orderStatus === 'Success'" style="color: #52C41A;"><v-icon color="#52C41A">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
                  <span v-else>-</span>
                </div>
              </template>

              <template v-slot:[`item.quoteNumber`]="{ item }">
                <div v-if="item.pdf_path !== '-'">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        style="cursor: pointer; text-decoration: underline; color: #27AB9C;"
                        @click="gotoQuotationNote(item.pdf_path)"
                      >
                        {{ item.quoteNumber }}
                      </span>
                    </template>
                    <span>รายละเอียดใบเสนอราคา</span>
                  </v-tooltip>
                </div>
                <div v-else>
                  <span>{{ item.quoteNumber }}</span>
                </div>
              </template>

              <template v-slot:[`item.productCount`]="{ item }">
                <span>{{ Number(item.productCount).toLocaleString() }}</span>
              </template>

              <template v-slot:[`item.limit_total`]="{ item }">
                <span>{{ Number(item.limit_total).toLocaleString() }}</span>
              </template>

              <template v-slot:[`item.shippedProducts`]="{ item }">
                <span>{{ formatShippedProducts(item.shippedProducts) }}</span>
              </template>

              <template v-slot:[`item.actions`]="{ item }">
                <v-btn text rounded color="#27AB9C" small @click="gotoActions(item)">
                  <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                  <b>สร้างใบส่งสินค้า</b><v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>

              <template v-slot:[`item.shippingStatus`]="{ item }">
                <div>
                  <v-chip v-if="item.shippingStatus === 'pending'" text-color="#FAAD14" color="#FFF2E0">รอสร้างใบจัดส่ง</v-chip>
                  <v-chip v-if="item.shippingStatus === 'inprogress'" text-color="#3178BB" color="#EAF4FF">กำลังจัดส่งสินค้า</v-chip>
                  <v-chip v-if="item.shippingStatus === 'success'" text-color="#31BB31" color="#EAFFF4">จัดส่งสินค้าสำเร็จ</v-chip>
                </div>
              </template>

            </v-data-table>
          </div>

          <div v-if="activeTab === 'delivery'">
            <span style="font-size: 16px;">รายการสั่งซื้อสินค้าทั้งหมด {{ totalDelivery }} รายการ</span>
            <br>
            <br>
            <v-data-table
              :headers="headersDelivery"
              :items="delivery"
              :page.sync="page"
              :footer-props="{'items-per-page-text':'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
              :items-per-page="options.itemsPerPage"
              :server-items-length="Number(totalDelivery)"
              :options.sync="options"
              @update:options="updateOptionsDelivery"
              class="elevation-1"
              :class="!IpadSize && !MobileSize && !IpadProSize ? 'table-delivery' : ''"
            >

              <template v-slot:[`item.actions`]="{ item }">
                <v-row>
                  <v-btn text rounded color="#27AB9C" small @click="gotoDeliveryDetail(item)">
                    <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                    <b>รายละเอียด</b><v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                  <v-btn text rounded color="#27AB9C" small @click="gotoDeliveryDetail(item)" v-if="item.status === 'waiting' || item.status === 'inprogress'">
                    <v-icon class="pr-1" color="27AB9C" small>mdi-basket-remove-outline</v-icon>
                    <b>ยกเลิกจัดส่ง</b><v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                </v-row>
              </template>

              <template v-slot:[`item.status`]="{ item }">
                <div>
                  <v-chip v-if="item.status === 'waiting'" text-color="#FAAD14" color="#FFF2E0">รอจัดส่งสินค้า</v-chip>
                  <v-chip v-if="item.status === 'inprogress'" text-color="#3178BB" color="#EAF4FF">กำลังจัดส่งสินค้า</v-chip>
                  <v-chip v-if="item.status === 'success'" text-color="#31BB31" color="#EAFFF4">จัดส่งสินค้าสำเร็จ</v-chip>
                </div>
              </template>

              <template v-slot:[`item.end_shipping`]="{ item }">
                <span v-if="item.end_shipping === 'Invalid Date'">-</span>
                <span v-else>{{ item.end_shipping }}</span>
              </template>

              <template v-slot:[`item.total_shipping_amount`]="{ item }">
                <span>{{ Number(item.total_shipping_amount).toLocaleString() }}</span>
              </template>

              <template v-slot:[`item.delivery_number`]="{ item }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                      style="cursor: pointer; text-decoration: underline; color: #27AB9C;"
                      @click="gotoDeliveryNote(item.pdf_path)"
                    >
                      {{ item.delivery_number }}
                    </span>
                  </template>
                  <span>รายละเอียดใบส่งสินค้า</span>
                </v-tooltip>
              </template>

            </v-data-table>
          </div>
        </v-col>
      </div>

    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      activeTab: 'order',
      search: '',
      page: 1,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      payType: 'all',
      orderStatus: 'all',
      statePayType: '',
      stateOrderStatus: '',
      actionsItem: [
        { text: 'สร้างรายการจัดส่ง', value: 'manageDelivery' }
      ],
      orders: [],
      totalOrders: '',
      totalPages: '',
      headersOrders: [
        { text: 'วันที่ทำรายการ', value: 'date', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'รหัสการสั่งซื้อ', value: 'orderId', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'Pay Type', value: 'payType', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'สถานะสั่งซื้อ', value: 'orderStatus', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'ผู้ซื้อ', value: 'buyer', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'วันที่อนุมัติ', value: 'quoteDate', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'ใบเสนอราคา', value: 'quoteNumber', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'วันที่รอบบริการ', value: 'serviceDate', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'รายการสินค้า', value: 'productCount', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'จำนวนสินค้าทั้งหมด', value: 'limit_total', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'จำนวนสินค้าที่จัดส่ง', value: 'shippedProducts', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'สถานะส่งสินค้า', value: 'shippingStatus', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' }
      ],
      searchDelivery: '',
      stateOrderStatusDelivery: '',
      totalDelivery: '',
      totalPagesDelivery: '',
      delivery: [],
      headersDelivery: [
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'รหัสใบส่งสินค้า', value: 'delivery_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        // { text: 'ใบส่งสินค้า', value: 'pdf_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'จำนวนสินค้าที่จัดส่ง', value: 'total_shipping_amount', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'วันที่จัดส่งสินค้า', value: 'start_date', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' },
        { text: 'วันที่รับสินค้า', value: 'end_shipping', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'สถานะรายการ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '150' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '280' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ListDeliveryMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ListDelivery' }).catch(() => { })
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    if (this.$route.query.tab) {
      this.activeTab = this.$route.query.tab
    } else {
      this.$router.replace({ query: { tab: this.activeTab } })
    }
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
    this.detailDelivery(this.search)
    this.orderDeliveryShop(this.searchDelivery)
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    changeTab (tab) {
      if (this.activeTab !== tab) {
        this.activeTab = tab
        this.resetData()
        this.$router.replace({ query: { tab } }).catch(() => {})

        if (tab === 'order') {
          this.detailDelivery()
        } else if (tab === 'delivery') {
          this.orderDeliveryShop()
        }
      }
    },
    resetData () {
      this.search = ''
      this.searchDelivery = ''
      this.page = 1
      this.options.page = 1
      this.statePayType = ''
      this.stateOrderStatus = ''
      this.stateOrderStatusDelivery = ''
      this.payType = 'all'
      this.orderStatus = 'all'
      this.orderStatusDelivery = 'all'
      this.orders = []
      this.delivery = []
      this.totalOrders = ''
      this.totalDelivery = ''
      this.totalPages = ''
      this.totalPagesDelivery = ''
    },
    selectType () {
      if (this.statePayType === 'ทั้งหมด' || this.statePayType === '') {
        this.payType = 'all'
      } else if (this.statePayType === 'รอชำระเงิน') {
        this.payType = 'Not Paid'
      } else if (this.statePayType === 'ชำระเงินแล้ว') {
        this.payType = 'Success'
      }
      this.detailDelivery(this.search)
    },
    selectOrderStatus () {
      if (this.stateOrderStatus === 'ทั้งหมด' || this.stateOrderStatus === '') {
        this.orderStatus = 'all'
      } else if (this.stateOrderStatus === 'รอสร้างใบจัดส่ง') {
        this.orderStatus = 'pending'
      } else if (this.stateOrderStatus === 'กำลังจัดส่งสินค้า') {
        this.orderStatus = 'inprogress'
      } else if (this.stateOrderStatus === 'จัดส่งสินค้าสำเร็จ') {
        this.orderStatus = 'Success'
      }
      this.detailDelivery(this.search)
    },
    selectOrderStatusDelivery () {
      if (this.stateOrderStatusDelivery === 'ทั้งหมด' || this.stateOrderStatusDelivery === '') {
        this.orderStatusDelivery = 'all'
      } else if (this.stateOrderStatusDelivery === 'รอจัดส่งสินค้า') {
        this.orderStatusDelivery = 'waiting'
      } else if (this.stateOrderStatusDelivery === 'กำลังจัดส่งสินค้า') {
        this.orderStatusDelivery = 'inprogress'
      } else if (this.stateOrderStatusDelivery === 'จัดส่งสินค้าสำเร็จ') {
        this.orderStatusDelivery = 'Success'
      }
      this.orderDeliveryShop(this.searchDelivery)
    },
    searchData (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.page = 1
        this.detailDelivery(val)
        this.orderDeliveryShop(val)
      }, 500)
    },
    updateOptions (options) {
      this.options = options
      this.page = options.page
      this.detailDelivery(this.search)
    },
    updateOptionsDelivery (options) {
      this.options = options
      this.page = options.page
      this.orderDeliveryShop(this.searchDelivery)
    },
    async detailDelivery (textSearch) {
      this.orders = []
      this.totalOrders = ''
      this.totalPages = ''
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        limit: this.options.itemsPerPage,
        shippingStatus: this.orderStatus === 'all' ? null : this.orderStatus,
        orderStatus: this.payType === 'all' ? null : this.payType,
        search: textSearch,
        page: this.page
      }
      await this.$store.dispatch('actionDelivery', data)
      var responseData = await this.$store.state.ModuleShop.stateDelivery
      // console.log(responseData)
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.orders = await responseData.data.order_details
        this.totalOrders = await responseData.data.totalOrders
        // this.totalOrders = await responseData.data.totalOrdersCount
        this.totalPages = await responseData.data.totalPages
      }
    },
    gotoActions (item) {
      this.orderNumber = item.orderId
      if (this.MobileSize) {
        this.$router.push({ path: `/ManageDeliveryMobile?orderNumber=${this.orderNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageDelivery?orderNumber=${this.orderNumber}` }).catch(() => {})
      }
    },
    gotoDeliveryNote (item) {
      if (item === '-') {
        this.$swal.fire({
          icon: 'info',
          text: 'ไม่มีรายการใบส่งสินค้า',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      } else {
        window.open(item)
      }
    },
    gotoQuotationNote (item) {
      if (item === '-') {
        this.$swal.fire({
          icon: 'info',
          text: 'ไม่มีรายการใบเสนอสินค้า',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      } else {
        window.open(item)
      }
    },
    async orderDeliveryShop (textSearch) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        delivery_status: this.orderStatusDelivery === 'all' ? null : this.orderStatusDelivery,
        limit: this.options.itemsPerPage,
        page: this.page,
        search: textSearch
      }
      await this.$store.dispatch('actionOrderDeliveryShop', data)
      var responseData = await this.$store.state.ModuleShop.stateOrderDeliveryShop
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.delivery = responseData.data
        this.totalDelivery = responseData.pagination.totalOrders
        this.totalPagesDelivery = responseData.pagination.totalPages
        // console.log('delivery', this.delivery)
      }
    },
    formatShippedProducts (value) {
      const [shipped, total] = value.split('/')
      return `${shipped}/${Number(total).toLocaleString()}`
    },
    gotoDeliveryDetail (item) {
      this.orderDeliveryNumber = item.delivery_number
      if (this.MobileSize) {
        this.$router.push({ path: `/DetailDeliveryMobile?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/DetailDelivery?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>

.v-btn--active {
  background-color: #F3FDF9 !important;
  color: #27AB9C !important;
  border: solid #BDE7D9 !important;
}

</style>

<style lang="scss" scoped>
  ::v-deep .table-orders table {
    tbody {
      tr {
        td:nth-child(12) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 199px;
          z-index: 10;
          background: white;
        }
        td:nth-child(13) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(12) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 199px;
        }
        th:nth-child(13) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }

  ::v-deep .table-delivery table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 280px;
          z-index: 10;
          background: white;
        }
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 280px;
        }
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
