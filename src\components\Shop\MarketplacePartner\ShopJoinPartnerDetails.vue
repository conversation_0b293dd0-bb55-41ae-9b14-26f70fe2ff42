<template>
  <v-container class="pa-4">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backTomenu()">mdi-chevron-left</v-icon>
      <div class="pt-3 px-3" style="position: relative; text-align: center; width: 100%;">
        <img
          src="@/assets/Marketplace_partner/ShopFrame.png"
          alt="Software Marketplace"
          width="100%">
        <p style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 3vw;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
          ">
          Software Marketplace
        </p>
      </div>
      <br>
      <div class="px-2">
          <v-col cols="12">
            <span style="font-weight: 700; font-size: 24px;"><b>Software Type</b></span>
          </v-col>
          <v-col cols="12" class="d-flex flex-wrap gap-2 justify-start">
            <v-btn
              v-for="(button, index) in itemsSelect"
              :key="index"
              class="v-btn-custom"
              :class="{ 'btn-selected': selectedButton === index, 'btn-outline': selectedButton !== index }"
              @click="selectType(index)"
              outlined
            >
              {{ button.label }}
            </v-btn>
          </v-col>
          <v-col cols="12">
            <span style="font-size: 16px"><b>ร้านค้า Partner ทั้งหมด {{ this.selectWithType.length }} ร้านค้า</b></span>
          </v-col>
          <v-row v-if="noInfo === false">
            <v-col
              v-for="(item, index) in newsListShow"
              :key="index"
              cols="12"
              sm="6"
              md="4"
              lg="3"
            >
              <v-card class="ma-2 setting-card" style="border-radius: 10px;">
                <v-card-text>
                  <v-img
                    v-if="item.partner_media && item.partner_media.find(image => image.image_type === 'main')"
                    :src="item.partner_media.find(image => image.image_type === 'main').media_path"
                    alt="Main image"
                    height="150px"
                    width="100%"
                    contain
                  ></v-img>
                  <v-img
                    v-else
                    src="@/assets/NoImage.png"
                    class="custom-image"
                    height="150px"
                    width="100%"
                    contain
                  ></v-img>
                </v-card-text>
                <v-card-text class="showcard">
                  <v-row dense justify="center" align="center">
                    <v-card
                      class="pa-3"
                      elevation="0"
                      style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;"
                    >
                      <v-row style="flex-wrap: nowrap; display: inline-flex; gap: 0;">
                        <v-col v-if="item.type_partner.includes('ERP')" cols="auto" class="pa-0 ma-1">
                          <v-chip label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b></v-chip>
                        </v-col>
                        <v-col v-if="item.type_partner.includes('Web Development')" cols="auto" class="pa-0 ma-1">
                          <v-chip label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b></v-chip>
                        </v-col>
                        <v-col v-if="item.type_partner.includes('POS')" cols="auto" class="pa-0 ma-1">
                          <v-chip label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>POS</b></v-chip>
                        </v-col>
                        <v-col v-if="item.type_partner.includes('OMS')" cols="auto" class="pa-0 ma-1">
                          <v-chip label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>OMS</b></v-chip>
                        </v-col>
                        <v-col v-if="item.type_partner.includes('Marketing')" cols="auto" class="pa-0 ma-1">
                          <v-chip label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b></v-chip>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-row>
                </v-card-text>
                <v-card-text class="text-center text-truncate" style="font-size: 16px;"><b>{{ item.partner_name }}</b></v-card-text>
                <v-card-text class="text-center pa-2">
                  <v-btn rounded @click="showDetailProductShopPartner(item)" class="mb-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C;">
                    <b>สนใจเข้าร่วม</b><v-icon small>mdi-arrow-right</v-icon>
                  </v-btn>
                  <!-- <v-btn rounded @click="OpendialogOldData(item)" class="mb-2" dark color="#27AB9C" v-if="partnerName.includes(item.partner_name.toLowerCase())"
                  >
                    <b>เชื่อมต่อข้อมูลเก่า</b><v-icon small>mdi-link</v-icon>
                  </v-btn> -->
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col cols="12" align="center">
              <div class="my-5">
                <v-img
                  src="@/assets/noinfo.png"
                  max-height="500px"
                  max-width="500px"
                  height="100%"
                  width="100%"
                  contain
                  aspect-ratio="2"
                ></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #9A9A9A">
                <span>ไม่มีร้านค้า Partner</span>
              </h2>
            </v-col>
          </v-row>
      </div>
      <v-row no-gutters justify="center" class="py-6" v-if="pageMax > 0">
        <v-pagination
          color="#27AB9C"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="8">
        </v-pagination>
      </v-row>
    </v-card>

    <v-dialog v-model="dialogSuspended" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogSuspended = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5; height: 200px;">
          <v-img
            src="@/assets/Marketplace_partner/Suspend.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>คุณมีบริการที่ถูกระงับการใช้งาน</b></span><br><br>
            <div class="scrollable-content-suspended" ref="termsContent" @scroll="handleScroll">
              '{{ this.suspendedPartner }}' <br>ถูกระงับการใช้งาน กรุณาชำระเงินเพื่อใช้งานต่อ
            </div>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center pb-5">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogSuspended = false">ปิด</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="GotoPaymentPartner()">ชำระเงิน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogOldData" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogOldData = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>คุณต้องการที่จะเชื่อมต่อข้อมูลเก่า</b></span><br><br>
            <span>
              ยืนยันการทำรายการนี้ ใช่ หรือไม่
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogOldData = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="confirmLinkOldPartnerData()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogAfterConfirm" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogAfterConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group1.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>ยืนยันการเชื่อมต่อข้อมูลเก่าเสร็จสิ้น</b></span><br><br>
            <span>
              คุณได้ทำการเชื่อมต่อข้อมูลเก่าเรียบร้อย<br>กรุณารอการติดต่อกลับ
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="dialogAfterConfirm = false">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      noInfo: true,
      // pageMax: null,
      current: 1,
      itemPerPage: 8,
      hidePagegination: true,
      itemsSelect: [
        { label: 'ทั้งหมด', value: 'all' },
        { label: 'ERP', value: 'ERP' },
        { label: 'OMS', value: 'OMS' },
        { label: 'Web Development', value: 'Web Development' },
        { label: 'POS', value: 'POS' },
        { label: 'Marketing', value: 'Marketing' }
      ],
      selectedButton: 0,
      selectedValue: 'all',
      detailPartner: [],
      selectWithType: [],
      suspendedPartner: [],
      dialogSuspended: false,
      dialogOldData: false,
      dialogAfterConfirm: false,
      oldData: [],
      partnerName: [
        'ZORT',
        'zort',
        'ซอร์ท',
        'ซอร์ท จำกัด',
        'บริษัท ZORT',
        'บริษัท ZORT จำกัด',
        'บริษัท Zort',
        'บริษัท Zort จำกัด',
        'บริษัท zort',
        'บริษัท zort จำกัด',
        'บริษัท ซอร์ท',
        'บริษัท ซอร์ท จำกัด',
        'บริษัทซอร์ท',
        'ซอร์ทเอาท์',
        'บริษัท ซอร์ทเอาท์',
        'บริษัท ซอร์ทเอาท์ จำกัด',
        'zortout',
        'zortout จำกัด',
        'บริษัท zortout',
        'บริษัท zortout จำกัด',
        'ZORTOUT'
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.itemPerPage
    },
    indexEnd () {
      return this.indexStart + this.itemPerPage
    },
    newsListShow () {
      return this.selectWithType.slice(this.indexStart, this.indexEnd)
    },
    pageMax () {
      return Math.ceil(this.selectWithType.length / this.itemPerPage)
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ShopJoinPartnerDetailsMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ShopJoinPartnerDetails' }).catch(() => {})
      }
    }
  },
  async created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNav')
    await this.getDataPartner()
    if (this.suspendedPartner === '') {
      this.dialogSuspended = false
    } else {
      this.dialogSuspended = true
    }
  },
  mounted () {

  },
  methods: {
    backTomenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async showDetailProductShopPartner (item) {
      if (this.MobileSize) {
        this.$router.replace(`/ShopPartnerDetailsMobile?SelectPartnerCode=${item.partner_code}`)
      } else {
        this.$router.replace(`/ShopPartnerDetails?SelectPartnerCode=${item.partner_code}`)
      }
    },
    async getDataPartner () {
      this.$store.commit('openLoader')
      const ShopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: ShopID
      }

      await this.$store.dispatch('actionGetListDetailPartnerShop', data)
      var response = await this.$store.state.ModuleShop.stateGetListDetailPartnerShop

      if (response.message === 'list partner success.') {
        this.$store.commit('closeLoader')
        this.detailPartner = response.data
        if (response.suspended_partner.length !== 0) {
          this.suspendedPartner = response.suspended_partner.join(', ')
        } else {
          this.suspendedPartner = ''
        }
        if (this.detailPartner.length > 0) {
          this.noInfo = false
        } else {
          this.detailPartner = []
          this.noInfo = true
        }
        this.selectType(this.selectedButton)
      }
    },
    async selectType (index) {
      this.selectedButton = index
      this.selectedValue = this.itemsSelect[index].value
      // console.log('Selected Value:', this.selectedValue)

      if (!this.detailPartner || this.detailPartner.length === 0) {
        console.error('No data in detailPartner')
        this.noInfo = true
        return
      }

      if (this.selectedValue === 'all') {
        this.selectWithType = [...this.detailPartner]
      } else {
        this.selectWithType = this.detailPartner.filter(item => {
          // console.log('item.type_partner:', item.type_partner)
          return item.type_partner && item.type_partner.includes(this.selectedValue)
        })
      }

      // console.log('Filtered selectWithType:', this.selectWithType)
      this.noInfo = this.selectWithType.length === 0
    },
    GotoPaymentPartner () {
      if (this.MobileSize) {
        this.$router.push({ path: '/paymentPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentPartner' }).catch(() => {})
      }
    },
    checkContentHeight () {
      this.$nextTick(() => {
        const content = this.$refs.termsContent
        if (content) {
          if (content.scrollHeight <= content.clientHeight) {
            this.isScrollComplete = true
          } else {
            this.isScrollComplete = false
          }
        }
      })
    },
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    },
    OpendialogOldData (item) {
      this.oldData = item
      this.dialogOldData = true
      this.partnerCode = item.partner_code
      this.serviceID = item.service_id
    },
    async confirmLinkOldPartnerData () {
      this.$store.commit('openLoader')
      const ShopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        partner_code: this.partnerCode,
        seller_shop_id: ShopID,
        service_id: this.serviceID,
        status: 'zort_waiting',
        type: 'old_customer'
      }

      await this.$store.dispatch('actionOldPartner', data)
      var response = await this.$store.state.ModuleShop.stateOldPartner

      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogOldData = false
        this.dialogAfterConfirm = true
      } else {
        this.$store.commit('closeLoader')
        this.dialogOldData = false
        this.$swal.fire({
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500,
          icon: 'error',
          text: `${response.message}`
        })
      }
    }
  }
}
</script>

<style scoped>
.v-btn-custom {
  min-width: 100px;
  height: 40px;
  text-transform: none;
  font-size: 14px;
  font-weight: bold;
  border-radius: 20px;
  background-color: transparent !important;
  border: 1px solid transparent;
  border-radius: 10px;
}

/* ปุ่มที่ถูกเลือก */
.btn-selected {
  background-color: #58A79E !important;
  color: white;
  border: none;
}

.btn-outline {
  background-color: transparent !important;
  border: 1px solid #58A79E;
  color: #58A79E;
}

.gap-2 {
  gap: 10px;
}

.showcard {
  height: 70px;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.scrollable-content-suspended {
  max-height: 100px;
  overflow-y: auto;
}
</style>
