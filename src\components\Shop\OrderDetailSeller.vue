<template>
  <v-container grid-list-xs>
    <v-card>
      <v-card-title>รายการแสดงข้อมูลการสั่งซื้อ </v-card-title>
      <v-card-subtitle>description description description description description description </v-card-subtitle>
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-row no-gutters justify="end">
      <!-- <v-col cols="12" class="py-0">
        <a-tabs @change="SelectDetailOrder">
          <a-tab-pane v-for="item in OrderName" :key="item.key" :tab="item.name"></a-tab-pane>
        </a-tabs>
      </v-col> -->
      <v-col cols="4" class="mr-4">
        <v-text-field
          v-model="search"
          dense
          hide-details
          outlined
          placeholder="ค้นหาใบสั่งซื้อ"
        ></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-card outlined class="small-card mx-4 my-5">
          <v-data-table
            :headers="headers"
            :items="product_list"
            style="width:100%"
            height="588px"
            :search="search"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' }) }}
              </template>
              <template v-slot:[`item.order_number`]="{ item }">
                <a @click="orderDetail(item)">{{item.order_number}}</a>
              </template>
              <!-- <template v-slot:[`item.received`]>
                <v-row justify="center" >
                  <v-checkbox v-model="checkbox"></v-checkbox>
                </v-row>
              </template> -->
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      shopData: [],
      product_list: [],
      StateStatus: 0,
      headers: [
        { text: 'วันที่', value: 'created_at', align: 'center' },
        { text: 'รหัสสินค้า', value: 'product_sku', align: 'center' },
        { text: 'ชื่อสินค้า', value: 'product_name', align: 'center' },
        { text: 'ราคาสินค้า', value: 'product_price', align: 'center' },
        { text: 'จำนวน', value: 'product_quantity', align: 'center' },
        { text: 'ราคาสุทธิ', value: 'net_price', align: 'center' },
        { text: 'ค่าจัดส่ง', value: 'shipping_rate', align: 'center' }
      ],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    }
  },
  watch: {
    DataTable (val) {
      // console.log('DataTable', val)
    }
  },
  async created () {
    // this.$EventBus.$emit('changeNav')
    this.shopData = await this.$store.state.ModuleOrder.stateOrderDetailSeller.data
    // console.log('this.shopData', this.shopData)
    this.product_list = this.shopData.product_list
    this.product_list.filter(element => {
      element.created_at = this.shopData.order_created_date
      return element
    })
    // this.product_list.created_at = this.shopData.order_created_date
    // console.log('order tetail dataaaaaaaaaa', this.product_list)
    // await this.$store.dispatch('actionListOrderSeller', shopData)
    // this.orderList = await this.$store.state.ModuleOrder.stateOrderListSeller.data
    // console.log('orderlist Seller', this.orderList)
  },
  methods: {
  }
}
</script>
