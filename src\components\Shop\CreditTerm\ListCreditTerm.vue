<template>
  <v-card elevation="0" width="100%" height="100%">
    <v-card-text>
      <v-row no-gutters>
        <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3 pt-3' : 'pl-0 pr-2 mb-3 pt-3'">
          <v-row dense class="mb-2">
            <v-btn icon @click="goBack()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon></v-btn>
             <h2 :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'">รายการสั่งซื้อสินค้าแบบเครดิตเทอม</h2>
          </v-row>
          <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col cols="12" class="pt-3 pb-3" v-if="disableTable === true">
          <v-row dense>
            <v-col cols="12" class="pt-2">
              <span :class="MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">รายการการชำระเงินแบบเครดิตเทอม {{ DataCount }} รายการ</span>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" v-if="disableTable === true">
          <v-data-table
            :headers="headersDetail"
            :items="DataDetail"
            :search="search"
            style="width:100%;"
            height="100%"
            :page.sync="page"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการสั่งซื้อ"
            no-data-text="ไม่มีรายการสั่งซื้อ"
            :update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 mt-4">
            <template v-slot:[`item.credit_term`]="{item}">
              <span>งวดที่ {{item.credit_term}}</span>
            </template>
            <template v-slot:[`item.total`]="{item}">
              <span>{{ Number(item.total).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
            </template>
            <template v-slot:[`item.paid_datetime`]="{ item }">
              <span>{{ item.paid_datetime === null ? '-' : new Date(item.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
            </template>
            <template v-slot:[`item.due_date`]="{ item }">
              <span>{{ item.due_date === null ? '-' : new Date(item.due_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
            </template>
            <template v-slot:[`item.transaction_status`]="{ item }">
              <span v-if="item.transaction_status === 'Success'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Not Paid'">
                <v-chip class="ma-2" color="#E5EFFF" text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Fail'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Cancel'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
              </span>
              <span v-else-if="item.transaction_status === 'Overdue'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">เกินกำหนดชำระ</v-chip>
              </span>
            </template>
            <template v-slot:[`item.pdf_path`]="{ item }">
              <v-btn max-width="32px" max-height="32px" color="#ffff" v-if="item.pdf_path !== null" @click="goPDF(item)">
                <v-icon color="#27AB9C"> mdi-file-document </v-icon>
              </v-btn>
              <v-btn text rounded color="#27AB9C" class="px-12" small @click="inputPDF(item)" v-if="item.pdf_path === null && item.transaction_status !== 'Overdue'">
                <b>แนบใบแจ้งหนี้</b>
                <v-icon small>mdi-chevron-right</v-icon>
              </v-btn>
              <div style="color: #27AB9C;" class="px-12" v-else-if="item.transaction_status === 'Overdue'" >
                -
              </div>
            </template>
          </v-data-table>
        </v-col>
        <v-col cols="12" v-else align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการเครดิตเทอม</b></h2>
        </v-col>
      </v-row>
      <!-- dialog show detail of request -->
      <v-dialog v-model="ModalPDF" width="800px" persistent>
        <v-card width="100%" :max-height="!MobileSize ? '100%' : '450'" class="rounded-lg">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">แนบเอกสารใบแจ้งหนี้</font>
            </span>
            <v-btn icon dark @click="ModalPDF = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row :class="!MobileSize ? 'mt-1' : 'mt-0'" dense>
              <v-col cols="12">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                  <v-card-text>
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสการสั่งซื้อที่ : {{ DataPDF.order_number }}</p>
                    <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">งวดที่ : {{ DataPDF.credit_term }}</p>
                    <v-card
                      elevation="0"
                      style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                      @click="onPickFile()"
                    >
                      <v-card-text>
                        <v-row
                          no-gutters
                          align="center"
                          justify="center"
                          style="cursor: pointer;"
                        >
                          <v-file-input
                            v-model="DataImage"
                            :items="DataImage"
                            accept=".pdf"
                            @change="UploadImage()"
                            id="file_input"
                            :clearable="false"
                            style="display:none"
                          >
                          </v-file-input>
                          <v-col cols="12" md="12" class="mb-6">
                            <v-row justify="center" :class="!MobileSize ? 'pt-10' : 'pa-0'">
                              <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                :width="!MobileSize ? '280.34' : '40px'"
                                :height="!MobileSize ? '154.87' : '40px'"
                                contain
                              ></v-img>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12" :class="!MobileSize ? 'mt-6' : 'ma-0'">
                            <v-row justify="center" align="center">
                              <v-col cols="12" md="12" style="text-align: center;">
                                <span style="line-height: 24px; font-weight: 400;" :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">เพิ่มเอกสารของคุณที่นี่หรือ</span><br/>
                                <span style="line-height: 24px; font-weight: 400;" :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">เลือกเอกสารจากคอมพิวเตอร์ของคุณ</span><br/>
                                <span style="line-height: 16px; font-weight: 400;" :style="MobileSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ไฟล์นามสกุล .pdf เพิ่มได้สูงสุด 1 ไฟล์)</span><br/>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                    <div v-if="DataImage.length !== 0" class="mt-2">
                      <draggable v-model="DataImage"  :move="onMove1" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                        <!-- <v-col v-for="(item, index) in DataImage" :key="index" cols="12" md="2"> -->
                        <v-col cols="12" md="2">
                          <v-card outlined class="pa-0" :min-width="!MobileSize ? '146' : '100%'" :max-height="!MobileSize ? '100%' : '95px'">
                            <v-card-text>
                              <v-btn icon x-small style="float: right; background-color: #ff5252;" v-if="MobileSize">
                                <v-icon x-small color="white" dark @click="RemoveImage()">mdi-close</v-icon>
                              </v-btn>
                              <v-row dense class="ma-0">
                                <v-col cols="12" align="center">
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" :width="!MobileSize ? '130' : '40px'" :height="!MobileSize ? '130' : '40px'" contain>
                                    <v-btn icon x-small style="float: right; background-color: #ff5252;" v-if="!MobileSize">
                                      <v-icon x-small color="white" dark @click="RemoveImage()">mdi-close</v-icon>
                                    </v-btn>
                                  </v-img>
                                </v-col>
                                <v-col cols="12">
                                  <p class="mb-2" style="text-align: center;" v-snip="1">{{ DataImage.name }}</p>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </draggable>
                    </div>
                  </v-card-text>
                  <v-row justify="end" dense class="mr-2">
                    <v-btn text outlined class="pr-8 pl-8 mr-4" @click="ModalPDF = false" color="primary" style="border: 1px solid #27AB9C;">ยกเลิก</v-btn>
                    <v-btn :disabled="DataImage === '' ? true : false" color="primary" class="pr-8 pl-8" @click="confirm()">บันทึก</v-btn>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-card-text>
  </v-card>
</template>

<script>
import { Encode } from '@/services'
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      shopDetail: '',
      order_number: '',
      term: '',
      // modal , dialog, table
      disableTable: true,
      ModalPDF: false,

      // table detail credit term--------------------------------------------------------
      search: '',
      page: 1,
      pageCount: 0,
      itemsPerPage: 10,
      DataCount: 0,
      DataDetail: [],
      headersDetail: [
        { text: 'งวด', value: 'credit_term', width: '100', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_credit_term_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนเงิน', value: 'total_amount', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', width: '180', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'วัน/เวลากำหนดชำระ', value: 'due_date', filterable: false, align: 'center', width: '150', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการชำระเงิน', value: 'transaction_status', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'pdf_path', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'action', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' }
      ],
      DataImage: [],
      DataPDF: {
        order_number: '',
        seller_shop_id: '',
        credit_term: '',
        pdf: ''
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('checkpath')
    this.order_number = this.$route.query.order_number
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    this.$store.commit('openLoader')
    await this.CreditTermDetail()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      var number = JSON.parse(localStorage.getItem('creditTermOrdernumber'))
      if (val === true) {
        this.$router.push({ path: '/sellerlistCreditTermMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: `/sellerlistCreditTerm?order_number=${number}` }).catch(() => {})
      }
    }
  },
  methods: {
    async CreditTermDetail () {
      var shopData = {
        seller_shop_id: this.shopDetail.id,
        order_number: this.order_number
      }
      await this.$store.dispatch('actionsSellerListCreditTerm', shopData)
      var response = await this.$store.state.ModuleShop.stateListCreditTerm
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.disableTable = true
        this.DataDetail = response.data
        this.DataCount = this.DataDetail.length
      } else {
        this.disableTable = false
        this.$store.commit('closeLoader')
        this.DataDetail = []
      }
      // console.log('data need to send', shopData)
      // console.log('response', response)
    },
    getRequest (item) {
      this.StateStatus = item
      // i dnk
      this.page = 1
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async inputPDF (item) {
      // reset value
      this.DataImage = ''
      this.DataPDF.order_number = ''
      this.DataPDF.seller_shop_id = ''
      this.DataPDF.credit_term = ''
      // asiign value
      this.ModalPDF = !this.ModalPDF
      this.DataPDF.order_number = item.order_number
      this.DataPDF.seller_shop_id = this.shopDetail.id
      this.DataPDF.credit_term = item.credit_term
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      if (this.DataImage !== '') {
        const element = this.DataImage
        const imageSize = element.size / 1024 / 1024
        if (imageSize < 2) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            this.DataPDF.pdf = resultReader.split(',')[1]
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
            showConfirmButton: false,
            timer: 1500
          })
          this.DataImage = ''
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    RemoveImage () {
      this.DataImage = ''
      this.DataPDF.pdf = ''
    },
    onMove1 ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    async confirm () {
      await this.$store.dispatch('actionsUploadInvoiceCreditTerm', this.DataPDF)
      var response = await this.$store.state.ModuleShop.stateUploadInvoiceCreditTerm
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: 'บันทึกเอกสารสำเร็จ', showConfirmButton: false, timer: 2000 })
        this.ModalPDF = !this.ModalPDF
        this.CreditTermDetail()
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    async goPDF (item) {
      localStorage.setItem('creditTerm', Encode.encode(item))
      if (!this.MobileSize) {
        await this.$router.push({ path: `/sellerInvoicePDF?order_number=${item.order_number}` }).catch(() => {})
      } else {
        await this.$router.push({ path: `/sellerInvoicePDFMobile?order_number=${item.order_number}` }).catch(() => {})
      }
    },
    goBack () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/sellerListCreditOrder' }).catch(() => {})
      } else {
        this.$router.push({ path: '/sellerListCreditOrderMobile' }).catch(() => {})
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(8) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(8) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.55rem;
}
</style>
<style scoped>
.fontSizeTitle {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333; padding-top: 5px;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;
}
.fontSizeTitle2 {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile2 {
  font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #636363 !important;
}
.v-btn {
  max-height: 32px !important;
  max-width: 32px !important;
}
</style>
