<template>
  <div>
    <v-row dense class="mt-5 pl-0">
      <v-col cols="12">
        <a-tabs :activeKey="tabselect" @change="SelectDetailOrder" class="custom-tabs">
          <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
          <a-tab-pane :key="0"><span
              :style="tabselect !== 0 ? 'font-weight: bold; color: #C4C4C4; font-size: 18px;' : 'font-weight: bold; font-size: 18px;'"
              slot="tab">Website</span></a-tab-pane>
          <a-tab-pane :key="1"><span
              :style="tabselect !== 1 ? 'font-weight: bold; color: #C4C4C4; font-size: 18px;' : 'font-weight: bold; font-size: 18px;'"
              slot="tab">Mobile</span></a-tab-pane>
        </a-tabs>
      </v-col>
    </v-row>
    <div v-if="tabselect === 0" class="pa-0">
      <v-col>
        <br>
        <span class="f-left ml-3" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">แก้ไข
          Banner หลัก<span class="pl-2" style="color: #C4C4C4; font-size: 18px;">(ไฟล์รูปควรมีขนาดไม่เกิน 2 MB
            และใส่ได้สูงสุด 10 รูป)</span></span>
        <br>
        <div v-if="BigBanner.length > 0">
          <v-col v-for="(item, index) in BigBanner" :key="index">
            <v-card outlined class="pa-1 mr-4 d-block" width="100%" height="300" v-if="BigBanner.length !== 0"
              style="justify-content: center; display: flex;">
              <v-img :src="item.image_path" :lazy-src="item.image_path_lazy" width="100%" height="100%" contain>
                <v-btn icon small style="float: right; background-color: #ff5252;">
                  <v-icon small color="white" dark @click="RemoveImage(index, item)"
                    @TouchStart="RemoveImage(index, item)">mdi-close</v-icon>
                </v-btn>
              </v-img>
            </v-card>
            <v-col>
              <v-col class="pa-0 pt-3">
                <span class="f-left" style="line-height: 26px; color: #333333; font-size: 16px">URL สำหรับ Banner</span>
              </v-col>
              <v-col class="pa-0">
                <v-text-field outlined dense :id="'link_banner_' + index" v-model="item.link_banner"
                  placeholder="ระบุ URL สำหรับ Banner">
                </v-text-field>
                <!-- <pre>{{item.link_banner}}</pre> -->
              </v-col>
            </v-col>
          </v-col>
        </div>
        <div v-if="BigBanner.length === 0 || BigBanner.length < 10">
          <v-col v-for="(item, index) in Detail.admin_image_banner" :key="index">
            <v-card outlined class="pa-1 mr-4 d-block" width="100%" height="300"
              v-if="Detail.admin_image_banner.length !== 0" style="justify-content: center; display: flex;">
              <v-img :src="item.url" :lazy-src="item.url" width="100%" height="100%" contain>
                <v-btn icon small style="float: right; background-color: #ff5252;">
                  <v-icon small color="white" dark @click="RemoveImageAdmin(index, item)"
                    @TouchStart="RemoveImageAdmin(index, item)">mdi-close</v-icon>
                </v-btn>
              </v-img>
            </v-card>
            <v-col>
              <v-col class="pa-0 pt-3">
                <span class="f-left" style="line-height: 26px; color: #333333; font-size: 16px">URL สำหรับ Banner</span>
              </v-col>
              <v-col class="pa-0">
                <v-text-field outlined dense :id="'link_banner_' + index" v-model="item.link_banner"
                  placeholder="ระบุ URL สำหรับ Banner">
                </v-text-field>
              </v-col>
            </v-col>
          </v-col>
        </div>
        <v-col v-if="BigBanner.length + Detail.admin_image_banner.length !== 10" class="mt-2">
          <v-row dense v-if="showSkeletonLoader === true">
            <v-col>
              <v-skeleton-loader max-width="100%" v-bind="attrs" type="image"></v-skeleton-loader>
            </v-col>
          </v-row>
          <v-card v-if="showSkeletonLoader === false" outlined width="100%" height="300"
            style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; display: flex; justify-content: center; align-items: center;"
            @click="onPickFile()">
            <v-card-text>
              <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                <v-file-input v-model="DataImage" :items="DataImage" accept="image/*" @change="UploadImage()"
                  id="file_input" multiple :clearable="false" style="display:none">
                </v-file-input>
                <v-col cols="12">
                  <v-row justify="center" align="center" class="mt-2">
                    <v-col cols="12" style="text-align: center;">
                      <v-icon color="#27AB9C" x-large>mdi-plus-circle-outline</v-icon><br />
                      <span
                        style="font-weight: 500; font-size: 16px; line-height: 24px; color: #27AB9C; text-decoration: underline;">เพิ่มรูปภาพ</span>
                    </v-col>
                    <span style="font-weight: 500; font-size: 14px; line-height: 24px; color: #27AB9C;">รูป Banner
                      ต้องมีขนาด ความยาว 1230 พิกเซล ความกว้าง 420 พิกเซล</span>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-row no-gutters justify="end" align="center" class="mt-4 mb-5">
          <v-btn class="mr-3" @click="Cancle()" elevation="0" outlined color="#27AB9C" width="125" height="40"
            style="border: 1px solid #27AB9C; box-sizing: border-box; border-radius: 32px; font-size: 16px; font-weight: 700; line-height: 24px;">ยกเลิก</v-btn>
          <v-btn color="#27AB9C" width="125" height="40" elevation="0" dark id="confirmcreateproduct" @click="Confirm()"
            style="border-radius: 32px; color: white; font-size: 16px; font-weight: 700; line-height: 24px;">บันทึก</v-btn>
        </v-row>
      </v-col>
      <v-dialog v-model="dialogAwaitConfirmBanner" width="424" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
            <v-app-bar flat color="rgba(0, 0, 0, 0)">
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn color="#CCCCCC" icon @click="dialogAwaitConfirmBanner = !dialogAwaitConfirmBanner">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4">
                <b>แก้ไขข้อมูล</b></p>
              <span
                style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูล</span><br />
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้
                ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4"
                  @click="dialogAwaitConfirmBanner = !dialogAwaitConfirmBanner">ยกเลิก</v-btn>
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                  @click="EditBanner()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <v-dialog v-model="dialogSuccessBanner" width="424" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
            <v-app-bar flat color="rgba(0, 0, 0, 0)">
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn color="#CCCCCC" icon @click="closeModalSuccess()">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4">
                <b>แก้ไขเสร็จสิ้น</b></p>
              <span
                style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C"
                  @click="closeModalSuccess()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
    </div>
    <div v-else>
      <editbannermobile />
    </div>
  </div>
</template>

<script>
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    editbannermobile: () => import(/* webpackPrefetch: true */ '@/components/AdminPanit/AdminManage/BannerManage/BigBannerEditMobile')
  },
  data () {
    return {
      tabselect: 0,
      dialogSuccessBanner: false,
      dialogAwaitConfirmBanner: false,
      showSkeletonLoader: false,
      link_banner: '',
      name: '',
      DataImage: [],
      dataEditBanner: [],
      BigBanner: [],
      Detail: {
        image_banner: [],
        admin_image_banner: []
      }
    }
  },
  watch: {
    tabselect (val) {
      if (val === 0) {
        // DataImage = []
        // dataEditBanner = []
        // BigBanner = []
        // this.Detail = {
        //   image_banner: [],
        //   admin_image_banner: []
        // }
        // console.log(this.DataImage)
        // console.log(this.BigBanner)
        // console.log(this.Detail)
        this.GetDataBanner()
        this.showSkeletonLoader = true
      }
    }
  },
  created () {
    this.GetDataBanner()
    this.showSkeletonLoader = true
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    SelectDetailOrder (val) {
      this.tabselect = val
    },
    Cancle () {
      this.$router.push({ path: '/adminBannerManage' }).catch(() => { })
    },
    UploadImage () {
      if (this.Detail.admin_image_banner.length + this.BigBanner.length < 10) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
            const imageSize = element.size / 1024 / 1024
            if (imageSize < 2) {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.$route.query.Status !== 'Edit') {
                  if (this.Detail.admin_image_banner.length + this.BigBanner.length < 10) {
                    this.Detail.admin_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      url: url,
                      name: this.DataImage[i].name
                    })
                  } else {
                    this.$swal.fire({
                      icon: 'warning',
                      text: 'กรุณาใส่รูปไม่เกิน 10 รูป',
                      showConfirmButton: false,
                      timer: 1500
                    })
                  }
                } else {
                  if (this.Detail.admin_image_banner.length + this.BigBanner.length < 10) {
                    this.Detail.admin_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImage[i].name
                    })
                  } else {
                    this.$swal.fire({
                      icon: 'warning',
                      text: 'กรุณาใส่รูปไม่เกิน 10 รูป',
                      showConfirmButton: false,
                      timer: 1500
                    })
                  }
                }
              }
              // console.log(this.Detail.admin_image_banner)
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปได้ไม่เกิน 10 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    RemoveImage (index, val) {
      if (val.id !== undefined) {
        this.banner_image_delete.push(val.id)
      }
      this.BigBanner.splice(index, 1)
      this.DataImage = []
    },
    RemoveImageAdmin (index, val) {
      if (val.id !== undefined) {
        this.banner_image_delete.push(val.id)
      }
      this.Detail.admin_image_banner.splice(index, 1)
      this.DataImage = []
    },
    async uploadBannerToS3 (imageBanner) {
      var data = {
        image: [],
        type: '',
        seller_shop_id: ''
      }
      this.Detail.image_banner = []
      for (let i = 0; i < imageBanner.length; i++) {
        data.image.push(imageBanner[i].image_data)
      }
      data.type = 'banner_landing_page'
      data.seller_shop_id = 'all'
      await this.$store.dispatch('actionsUploadToS3', data)
      var response = await this.$store.state.ModuleShop.stateUploadToS3
      if (response.message === 'List Success.') {
        for (let j = 0; j < response.data.list_path.length; j++) {
          this.Detail.admin_image_banner[j].image_data = response.data.list_path[j].path
          this.Detail.admin_image_banner[j].image_data_lazy = response.data.list_path[j].path_lazy
          this.Detail.admin_image_banner[j].image_data_app = response.data.list_path[j].path_app
          this.Detail.admin_image_banner[j].image_data_lazy_app = response.data.list_path[j].path_lazy_app
        }
        for (let k = 0; k < this.Detail.admin_image_banner.length; k++) {
          this.Detail.image_banner.push({
            name: this.Detail.admin_image_banner[k].name,
            path: this.Detail.admin_image_banner[k].image_data,
            path_lazy: this.Detail.admin_image_banner[k].image_data_lazy,
            path_app: this.Detail.admin_image_banner[k].image_data_app,
            path_lazy_app: this.Detail.admin_image_banner[k].image_data_lazy_app,
            href: this.Detail.admin_image_banner[k].link_banner
          })
        }
        await this.$store.dispatch('actionsDetailLandingPage')
        var response1 = await this.$store.state.ModuleAdminManage.stateDetailLandingPage
        if (response1.result === 'SUCCESS') {
          var data1 = {
            image_big_banner_web: [],
            image_big_banner: [],
            image_banner_1: [],
            image_banner_2: [],
            image_banner_3: []
          }
          for (let i = 0; i < response1.data.image_big_banner.length; i++) {
            data1.image_big_banner.push({
              name: response1.data.image_big_banner[i].name,
              path: response1.data.image_big_banner[i].path,
              path_lazy: response1.data.image_big_banner[i].path_lazy,
              path_app: response1.data.image_big_banner[i].path_app,
              path_lazy_app: response1.data.image_big_banner[i].path_lazy_app,
              href: response1.data.image_big_banner[i].href
            })
          }
          for (let i = 0; i < this.BigBanner.length; i++) {
            if (this.BigBanner[i].link_banner === undefined || this.BigBanner[i].link_banner === null || this.BigBanner[i].link_banner === '') {
              this.BigBanner[i].link_banner = '-'
            }
            data1.image_big_banner_web.push({
              name: this.BigBanner[i].image_name,
              path: this.BigBanner[i].image_path,
              path_lazy: this.BigBanner[i].image_path_lazy,
              path_app: this.BigBanner[i].image_path_app,
              path_lazy_app: this.BigBanner[i].image_path_lazy_app,
              href: this.BigBanner[i].link_banner
            })
          }
          for (let i = 0; i < this.Detail.image_banner.length; i++) {
            if (this.Detail.image_banner[i].href === undefined || this.Detail.image_banner[i].href === null || this.Detail.image_banner[i].href === '') {
              this.Detail.image_banner[i].href = '-'
            }
            data1.image_big_banner_web.push({
              name: this.Detail.image_banner[i].name,
              path: this.Detail.image_banner[i].path,
              path_lazy: this.Detail.image_banner[i].path_lazy,
              path_app: this.Detail.image_banner[i].path_app,
              path_lazy_app: this.Detail.image_banner[i].path_lazy_app,
              href: this.Detail.image_banner[i].href
            })
          }
          for (let i = 0; i < response1.data.image_banner_1.length; i++) {
            data1.image_banner_1.push({
              name: response1.data.image_banner_1[i].name,
              path: response1.data.image_banner_1[i].path,
              path_lazy: response1.data.image_banner_1[i].path_lazy,
              path_app: response1.data.image_banner_1[i].path_app,
              path_lazy_app: response1.data.image_banner_1[i].path_lazy_app,
              href: response1.data.image_banner_1[i].href
            })
          }
          for (let i = 0; i < response1.data.image_banner_2.length; i++) {
            data1.image_banner_2.push({
              name: response1.data.image_banner_2[i].name,
              path: response1.data.image_banner_2[i].path,
              path_lazy: response1.data.image_banner_2[i].path_lazy,
              path_app: response1.data.image_banner_2[i].path_app,
              path_lazy_app: response1.data.image_banner_2[i].path_lazy_app,
              href: response1.data.image_banner_2[i].href
            })
          }
          for (let i = 0; i < response1.data.image_banner_3.length; i++) {
            data1.image_banner_3.push({
              name: response1.data.image_banner_3[i].name,
              path: response1.data.image_banner_3[i].path,
              path_lazy: response1.data.image_banner_3[i].path_lazy,
              path_app: response1.data.image_banner_3[i].path_app,
              path_lazy_app: response1.data.image_banner_3[i].path_lazy_app,
              href: response1.data.image_banner_3[i].href
            })
          }
        }
        await this.$store.dispatch('actionsMangeLandingPage', data1)
        var response2 = await this.$store.state.ModuleAdminManage.stateMangeLandingPage
        if (response2.message === 'change Success.') {
          // console.log('change Success.')
          // this.$swal.fire({ icon: 'success', text: 'แก้ไขข้อมูลสำเร็จ', showConfirmButton: false, timer: 1500 })
          // this.$router.push({ path: '/adminBannerManage' }).catch(() => { })
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async GetDataBanner () {
      this.BigBanner = []
      await this.$store.dispatch('actionsDetailLandingPage')
      var response = await this.$store.state.ModuleAdminManage.stateDetailLandingPage
      if (response.result === 'SUCCESS') {
        if (response.data.image_big_banner_web.length > 0) {
          for (let i = 0; i < response.data.image_big_banner_web.length; i++) {
            this.BigBanner.push({
              image_name: response.data.image_big_banner_web[i].name,
              image_path: response.data.image_big_banner_web[i].path,
              image_path_lazy: response.data.image_big_banner_web[i].path_lazy,
              image_path_app: response.data.image_big_banner_web[i].path_app,
              image_path_lazy_app: response.data.image_big_banner_web[i].path_lazy_app,
              link_banner: response.data.image_big_banner_web[i].href
            })
            this.showSkeletonLoader = false
          }
        } else {
          this.BigBanner = []
          this.showSkeletonLoader = false
        }
      }
    },
    async Confirm () {
      this.dialogAwaitConfirmBanner = true
      // this.$store.commit('closeLoader')
    },
    async EditBanner () {
      this.dialogAwaitConfirmBanner = false
      this.$store.commit('openLoader')
      if (this.Detail.admin_image_banner.length !== 0) {
        await this.uploadBannerToS3(this.Detail.admin_image_banner)
      } else if (this.Detail.admin_image_banner.length === 0) {
        this.image_big_banner = []
        await this.uploadBannerToS3(this.Detail.admin_image_banner)
      }
      this.dialogSuccessBanner = true
    },
    closeModalSuccess () {
      this.dialogSuccessBanner = false
      this.$router.push({ path: '/adminBannerManage' }).catch(() => { })
    }
  }
}
</script>

<style scoped>
::v-deep .custom-tabs .ant-tabs-ink-bar {
  background-color: #27AB9C !important;
}

.formatBox {
  border: 1px solid #cbeae0;
  border-radius: 20px;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px !important;
}
</style>
