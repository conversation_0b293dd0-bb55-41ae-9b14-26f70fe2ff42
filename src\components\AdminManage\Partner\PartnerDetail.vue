<template>
  <v-container>
    <v-row dense class="mb-2">
      <h1 style="font-weight: bold;" class="ml-1">รายละเอียดคู่ค้าองค์กรกับ {{ PartnerData.name_th }}</h1>
    </v-row>
    <v-card width="100%" height="100%" elevation="0">
      <v-card-text>
        <v-row dense justify="center">
          <!-- ข้อมูลคู่ค้าองค์กร -->
          <v-col cols="12" md="12" sm="12" xs="12" class="my-4">
            <v-card outlined width="100%" height="100%" elevation="0" class="cardDetailPartner">
              <v-card-text>
                <v-row justify="center" class="mt-4">
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'end', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ชื่อร้านค้า (ภาษาไทย)</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-size: 14px;">{{ PartnerData.name_th }}</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'end', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ชื่อร้านค้า (ภาษาอังกฤษ)</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-size: 14px;">{{ PartnerData.name_en }}</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'end', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ที่อยู่</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-size: 14px;"></span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'end', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ลิงค์ร้านค้า</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="font-size: 14px;"></span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- ข้อมูลคู่ค้าองค์กรส่วนล่าง -->
          <v-col cols="12" md="12" sm="12" xs="12" class="my-4">
            <v-card outlined width="100%" height="100%" elevation="0" class="cardDetailPartner">
              <v-card-text>
                <v-row justify="center" class="mt-4">
                  <v-col cols="12" md="6" sm="6">
                    <v-row justify="center">
                      <!-- ส่วนหนึ่ง -->
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ระยะเวลาควบคุมงบประมาณ</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">ไม่</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ประเภทลูกค้า</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">Tier 5</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ต้องการที่จะรับโปรโมชันหรือไม่ ?</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">ไม่</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">ต้องการทบวงเงินหรือไม่ ?</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">ใช่</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- ส่วนสอง -->
                  <v-col cols="12" md="6" sm="6">
                    <v-row>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">วงเงินเครดิตของบริษัท</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">5,000,000.00</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">เปอร์เซ็นต์ส่วนลดสำหรับบริษัท</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">0.00%</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-weight: bold; font-size: 14px;" :style="{ 'justify-content': MobileSize === true ? '' : 'start', 'display': MobileSize === true ? '' : 'flex' }" class="pr-8">บริษัทต้องจะต้องถูกเก็บเงินค่าจัดส่งหรือไม่ ?</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span style="font-size: 14px;">ฟรีค่าจัดส่ง</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      PartnerData: []
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'ข้อมูลบริษัท')
    this.$EventBus.$emit('changeNavAdminManage')
    this.getDetailPartner()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  methods: {
    getDetailPartner () {
      var partnerData = JSON.parse(Decode.decode(localStorage.getItem('detailPartner')))
      // console.log(partnerData)
      this.PartnerData = partnerData
    }
  }
}
</script>

<style scoped>
.cardDetailPartner {
  border-radius: 4px;
  border: 2px solid lightgrey;
}
/* .textCompany {
  justify-content: center;
  display: flex;
}
.headCompany {
  padding-right: 50px;
} */
</style>
