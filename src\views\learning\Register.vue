<template>
  <div>
    <pre>{{ test }}</pre>
  </div>
</template>

<script>
export default {
  data () {
    return {
      test: []
    }
  },
  created () {
    this.GetShop()
  },
  methods: {
    async GetShop () {
      // console.log('test created')
      await this.$store.dispatch('actionsGetSellerShop')
      this.test = await this.$store.state.ModuleRegister.stateGetSellerShop
    }
  }
}
</script>

<style lang="css" scoped>
</style>
