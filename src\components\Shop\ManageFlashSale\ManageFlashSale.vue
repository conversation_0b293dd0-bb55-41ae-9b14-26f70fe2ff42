<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col cols="6" class="pr-0">
          <v-card-title class="pr-0" style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการ Flash Sale</v-card-title>
          <v-card-title style="font-weight: 700; font-size: 16px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon>จัดการ Flash Sale</v-card-title>
        </v-col>
        <v-col cols="6" v-if="dataList.length === 0" class="d-flex justify-end">
          <v-btn rounded color="#27ab9c" @click="addFlashSale()">
            <v-icon color="#fff" class="mr-1">mdi-plus-circle</v-icon>
            <span style="color: #fff; text-transform: none;">เพิ่ม Flash Sale</span>
          </v-btn>
        </v-col>
        <v-col cols="6" v-else class="d-flex justify-end" style="gap:.5vw">
          <v-btn rounded @click="editFlashSale()" class="ml-1" outlined color="#26ab9c">แก้ไข</v-btn>
        </v-col>
      </v-row>

      <v-col cols="12">
        <div v-if="dataList.length === 0" class="ma-3">
          <v-card class="mt-5">
            <v-card-text class="d-flex align-center flex-column">
              <v-img
                src="@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png"
                width="400"
                contain
              ></v-img>
              <span style="font-size: 18px">ไม่มีรายการแฟลชเซลล์ในร้านค้านี้</span>
            </v-card-text>
          </v-card>
        </div>
        <div v-else>
          <v-card outlined style="border-color: #FF4500;">
            <v-card-title style="display: flex; justify-content: center;">
              <span style="color: #ff4500;"><b>{{ dataList.title }}</b></span>
              <v-icon class="ml-2" color="#333333">mdi mdi-clock-time-eight-outline</v-icon>
              <v-chip class="ml-2" text-color="#ff4500" color="rgba(255, 113, 11, 0.10)" style="font-size: 20px; font-weight: 500;">
                {{ dataList.start_time }} - {{ dataList.end_time }}
              </v-chip>
            </v-card-title>
          </v-card>
          <v-col cols="12">
            <v-icon>mdi-circle-medium</v-icon>
            <span>ภาพแบนเนอร์</span>
          </v-col>
          <v-col cols="12" align="center" >
            <v-card width="100%" :height="MobileSize ? '100px' : '300px'" elevation="0" style="background-color: #F0F0F0; border-radius: 10px;">
              <v-img :src="dataList.image_path" v-if="dataList.image_path !== null" width="100%" height="100%" contain></v-img>
              <v-img src="@/assets/NoImage.png" v-else width="100%" height="266px" contain></v-img><br />
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-icon>mdi-circle-medium</v-icon>
            <span>เวลา Flash Sale</span>
          </v-col>
          <v-col cols="12" class="pa-2">
            <v-row>
              <v-col cols="auto" class="ml-auto">
                <v-menu
                  v-model="menu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  max-width="290px"
                  min-width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      :value="formattedDateThai"
                      label="เลือกวันที่"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                      dense
                      outlined
                      hide-details
                      style="max-width: 200px;"
                    />
                  </template>

                  <v-date-picker
                    v-model="selectedDate"
                    @input="onDateSelected"
                    locale="th"
                    scrollable
                    :min="today"
                  />
                </v-menu>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row>
              <v-col cols="12">
                <v-row no-gutters>
                  <v-tabs
                    v-model="tab"
                    background-color="#FF4500"
                    grow
                    show-arrows
                    class="time-tabs"
                    slider-color="#FF4500"
                    icons-and-text
                  >
                    <v-tab
                      v-for="(item, index) in itemsTabs"
                      :key="index"
                      :class="tab === index ? 'active-tab' : 'inactive-tab'"
                    >
                      <div class="text-center">
                        <div class="time-text">{{ item.time }}</div>
                        <div class="sub-text">{{ item.label }}</div>
                      </div>
                    </v-tab>
                  </v-tabs>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" class="d-flex align-center">
            <v-icon>mdi-circle-medium</v-icon>
            <span>รายการสินค้าทั้งหมด {{ dataList.total }} รายการ</span>
          </v-col>
          <v-col cols="12">
            <v-row>
              <v-col cols="12" v-if="!dataList.product_list || dataList.product_list.length === 0">
                <v-card-text class="text-center" style="font-size: 14px; color: #999;">
                  ไม่มีสินค้าในแฟลชเซลล์นี้
                </v-card-text>
              </v-col>
              <v-col v-for="(item, index) in dataList.product_list" :key="index" cols="12" md="6" lg="3">
                <v-card style="height: 100%; display: flex; flex-direction: column; justify-content: space-between; border-radius: 10px;">
                  <v-img
                    :src="item.product_image || require('@/assets/NoImage.png')"
                    width="100%"
                    height="150px"
                    contain
                    class="mt-5"
                  ></v-img>
                  <div class="mt-2 pa-2" style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between;">
                    <span class="productName">{{ item.product_name }}</span>
                    <div class="mt-2">
                      <div class="d-flex justify-center">
                        <span style="font-size: 12px; font-weight: 600; line-height: 19px; color: #636363; text-decoration-line: line-through;" class="pt-1">฿ {{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <v-chip color="#FBE5E4" text-color="#ff4500" style="border-radius: 4px;" :class="MobileSize ? 'px-2 ml-1' : 'ml-2'" small><span class="discountText">ส่วนลด {{ formatDiscountValue(item.discount_value) }}{{ item.discount_type === 'percent' ? '%' : item.discount_type === 'baht' ? '฿' : '' }}</span></v-chip>
                      </div>
                      <div style="display: flex; justify-content: center; align-items: center; margin-top: 10px;">
                        <span style="font-weight: 600; font-size: medium; line-height: 29px; color: #ff4500;">
                          ฿ {{ Number(item.sale_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                        </span>
                      </div>
                      <!-- <div style="display: flex; justify-content: center; align-items: center; margin-top: 10px;">
                        <span>การสั่งซื้อต่อผู้ใช้ {{ item.limit_per_user === null ? 'ไม่จำกัด' : item.limit_per_user }} ชิ้น</span>
                      </div> -->
                      <div v-if="item.sold_count !== '-'" style="position: relative; height: 24px; border-radius: 12px; background-color: #FDD1C7; overflow: hidden; width: 100%; margin-top: 5px;">
                        <div
                          :style="{
                            width: soldPercent(item) + '%',
                            background: 'linear-gradient(to right, #FF4D00, #FFA500)',
                            height: '100%',
                            borderRadius: '12px 0 0 12px'
                          }"
                        ></div>
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center;">
                          <span style="color: white; font-weight: 700;">ขายแล้ว {{ kFormatterSold(item.sold_count) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
          <v-row class="pa-3" style="margin-top: -1vw" v-if="dataList.product_list.length > 0">
            <v-col class="text-center">
              <v-pagination
                color="#27AB9C"
                v-model="pageNumber"
                :length="pageMax"
                :total-visible="7"
                @input="getListFlashSale"
              >
            </v-pagination>
            </v-col>
          </v-row>
        </div>
      </v-col>

    </v-card>
  </v-container>
</template>

<script>
import moment from 'moment'
export default {
  data () {
    return {
      overlay: false,
      dataList: [],
      current: parseInt(this.$route.query.page) || 1,
      pageSize: 12,
      pageMax: null,
      tab: 0,
      itemsTabs: [],
      timeSlot: '',
      menu: false,
      selectedDate: moment().format('YYYY-MM-DD'),
      today: moment().format('YYYY-MM-DD'),
      hasInitializedFlashSaleList: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        if (this.$route.query.page !== String(newPage)) {
          this.$router.push({ query: { ...this.$route.query, page: newPage } }).catch(() => {})
        }
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.dataList.product_list || []
    },
    formattedDateThai () {
      if (!this.selectedDate) return ''
      const m = moment(this.selectedDate, 'YYYY-MM-DD')
      const buddhistYear = m.year() + 543
      return m.format(`DD/MM/${buddhistYear}`)
    }
  },
  watch: {
    '$route.query.page' (newPage) {
      const pageNum = parseInt(newPage) || 1
      if (pageNum !== this.current) {
        this.current = pageNum
        this.getListFlashSale()
        window.scrollTo(0, 0)
      }
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageFlashSaleMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageFlashSale' }).catch(() => {})
      }
    },
    tab (newIndex) {
      this.timeSlot = this.itemsTabs[newIndex].time
      this.pageNumber = 1
      this.getListFlashSale()
    }
  },
  mounted () {

  },
  async created () {
    this.$EventBus.$emit('changeNav')
    const pageFromQuery = parseInt(this.$route.query.page)
    if (!isNaN(pageFromQuery) && pageFromQuery > 0) {
      this.pageNumber = pageFromQuery
    } else {
      this.pageNumber = 1
    }
    // await this.getListFlashSale()
    this.updateTimeTabs()
    this.startTimeSlotWatcher()
  },
  methods: {
    backToMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    addFlashSale () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSaleCreate' }).catch(() => { })
      } else {
        this.$router.push({ path: '/manageFlashSaleCreateMobile' }).catch(() => { })
      }
    },
    editFlashSale () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSaleEdit' }).catch(() => { })
      } else {
        this.$router.push({ path: '/manageFlashSaleEditMobile' }).catch(() => { })
      }
    },
    async getListFlashSale () {
      this.$store.commit('openLoader')
      var shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopSellerID,
        status: '',
        type: '',
        date_slot: this.selectedDate,
        time_slot: this.timeSlot,
        start_date: '',
        end_date: '',
        order: 'asc',
        page: this.pageNumber,
        limit: 12
      }
      await this.$store.dispatch('actionsListFlashSaleV2', data)
      var responseData = await this.$store.state.ModuleManageFlashSale.stateListFlashSaleV2
      if (responseData.message === 'Flash sales retrieved successfully.') {
        this.$store.commit('closeLoader')
        this.dataList = responseData.data
        this.pageMax = responseData.data.last_page
      } else if (responseData.message === 'This shop has not created a flash sale yet. Please create a flash sale.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'info',
          text: 'ยังไม่มีแฟลชเซลล์ในร้านค้านี้ กรุณาสร้างแฟลชเซลล์ใหม่'
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: responseData.message
        })
      }
    },
    formatTime (datetime) {
      if (!datetime) return ''
      return moment(datetime).format('HH:mm')
    },
    soldPercent (item) {
      const sold = parseInt(item.sold_count) || 0
      const total = parseInt(item.quantity) || 1 // ป้องกันหารศูนย์
      return Math.min((sold / total) * 100, 100)
    },
    kFormatterSold (num) {
      const n = parseInt(num)
      if (isNaN(n)) return 0
      if (n >= 1000000) return (n / 1000000).toFixed(n % 1000000 === 0 ? 0 : 1) + 'M'
      if (n >= 1000) return (n / 1000).toFixed(n % 1000 === 0 ? 0 : 1) + 'K'
      return n
    },
    formatDiscountValue (value) {
      const num = parseFloat(value)
      return Number.isInteger(num) ? parseInt(num) : num.toFixed(2).replace(/\.?0+$/, '')
    },
    onDateSelected (date) {
      this.selectedDate = date
      this.menu = false
      this.hasInitializedFlashSaleList = false
      this.updateTimeTabs()
    },
    startTimeSlotWatcher () {
      this.updateTimeTabs()

      const now = moment()
      const startOfToday = moment().startOf('day')

      const timeSlots = [
        moment(startOfToday).hour(12),
        moment(startOfToday).hour(18),
        moment(startOfToday).hour(21),
        moment(startOfToday).add(1, 'day') // 00:00 ของวันถัดไป
      ]

      // หา slot ถัดไปจากตอนนี้
      const nextSlot = timeSlots.find(t => now.isBefore(t))

      if (!nextSlot) {
        // ถ้าเลยทุก slot → รีใหม่พรุ่งนี้ตอน 12:00
        const tomorrowNoon = moment(startOfToday).add(1, 'day').hour(12)
        const ms = tomorrowNoon.diff(now)
        setTimeout(() => this.startTimeSlotWatcher(), ms)
      } else {
        const msUntilNext = nextSlot.diff(now)
        setTimeout(() => this.startTimeSlotWatcher(), msUntilNext)
      }
    },
    updateTimeTabs () {
      const now = moment()
      // var startOfToday = moment().startOf('day')

      const rawTimeItems = [
        { time: '12:00' },
        { time: '18:00' },
        { time: '21:00' },
        { time: '00:00' }
      ]

      const selectedDateMoment = moment(this.selectedDate)
      const isToday = selectedDateMoment.isSame(now, 'day')

      const updatedItems = rawTimeItems.map((item, index) => {
        var start = moment(item.time, 'HH:mm').set({
          year: selectedDateMoment.year(),
          month: selectedDateMoment.month(),
          date: selectedDateMoment.date()
        })

        if (item.time === '00:00') {
          const noon = moment(start).set({ hour: 12, minute: 0 })
          if (selectedDateMoment.isSame(now, 'day') && now.isSameOrAfter(noon)) {
            start.add(1, 'day')
          }
        }

        let end = null
        const nextIndex = (index + 1) % rawTimeItems.length
        const nextItem = rawTimeItems[nextIndex]

        if (nextItem && nextItem.time) {
          end = moment(nextItem.time, 'HH:mm').set({
            year: start.year(),
            month: start.month(),
            date: start.date()
          })

          if (nextItem.time === '00:00') {
            end.add(1, 'day')
          }

          if (end.isSameOrBefore(start)) {
            end.add(1, 'day')
          }
        }

        let label = 'เร็ว ๆ นี้'

        if (now.isSameOrAfter(start) && now.isBefore(end)) {
          label = 'กำลังดำเนินอยู่'
        }

        return { ...item, label, start, end }
      })

      // 🔁 กำหนด rotateIndex
      let rotateIndex = 0
      if (isToday) {
        rotateIndex = updatedItems.findIndex(item =>
          now.isSameOrAfter(item.start) && now.isBefore(item.end)
        )

        if (rotateIndex === -1) {
          // ถ้าไม่มีช่วงเวลาที่ครอบคลุม now ให้หา slot ถัดไป
          rotateIndex = updatedItems.findIndex(item => now.isBefore(item.start))
        }

        // ถ้ายังไม่เจอเลย (เลยเวลาหมดแล้ว) → เริ่มจาก index 0
        if (rotateIndex === -1) rotateIndex = 0
      } else {
        // ถ้าไม่ใช่วันนี้ → เริ่มจาก 00:00 เสมอ (index = 3)
        rotateIndex = updatedItems.findIndex(item => item.time === '00:00')
      }

      // หมุน array
      const rotatedItems = [
        ...updatedItems.slice(rotateIndex),
        ...updatedItems.slice(0, rotateIndex)
      ]

      this.itemsTabs = rotatedItems
      this.tab = 0
      this.timeSlot = rotatedItems[0].time
      if (!this.hasInitializedFlashSaleList) {
        this.getListFlashSale()
        this.hasInitializedFlashSaleList = true
      }
    },
    substring (data) {
      return data.length > 20 ? data.substring(0, 20) + '...' : data
    },
    kFormatter (num) {
      return Math.abs(num) > 999 ? Math.sign(num) * ((Math.abs(num) / 1000).toFixed(1)) + 'k' : Math.sign(num) * Math.abs(num)
    }
  }
}
</script>

<style scope>
.formatBox {
  display: flex;
  flex-direction: column;
  box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
  padding: 1.5vw;
  border-radius: .5vw;
  background-color: #fff;
}

.productName {
  font-weight: 600;
  font-size: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 47px;
  line-height: 1.4;
}
.time-tabs .v-tab {
  /* height: 70px;
  padding: 10px;
  border-radius: 0; */
}

.active-tab {
  background-color: #ff4500;
  color: white !important;
}

.inactive-tab {
  background-color: #ffbda0;
  color: #ffffff !important;
}

.time-text {
  font-size: 20px;
  font-weight: bold;
}

.sub-text {
  font-size: 14px;
}
</style>
