<template>
  <div>
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4' : 'mb-4' ]">
      <v-card-title class="pt-6 pl-6" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"
        v-if="!MobileSize">
        คูปองและคะแนนของฉัน
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        คูปองและคะแนนของฉัน
      </v-card-title>
      <!-- ส่วนคะแนนของฉัน start-->
      <v-card-text align="center" justify="center">
        <v-card outlined width="450" height="242" class="pa-auto ma-2 rounded-xl border-scroll">
          <v-card-text>
            <v-row justify="center" align-content="center">
              <v-col cols="12" md="12" class="pt-10">
                <v-row justify="center" align-content="center">
                  <v-img width="86px" height="86px" contain :src="require('@/assets/favourites1.png')" />
                </v-row>
              </v-col>
              <v-col cols="12" md="12" class="pt-12 pb-4">
                <v-row justify="center" align-content="center">
                  <p style="font-size: 28px; font-weight: bold; color: #333333;">{{ Points }}</p>
                </v-row>
              </v-col>
              <p style="color: #636363; font-size: 14px; font-weight: 700;">คะแนนของฉัน</p>
            </v-row>
          </v-card-text>
        </v-card>
      </v-card-text>
      <!-- ส่วนคะแนนของฉัน end-->

      <!-- ส่วนนับจำนวนคูปอง start -->
      <span class="pa-4 pl-8 mt-2" style="font-size:18px ; font-weight: 10;">
        จำนวนคูปองทั้งหมด : {{ CountCoupons }} คูปอง
      </span>
      <!-- ส่วนนับจำนวนคูปอง end -->

      <!-- ในกรณี  มีคูปอง start -->
      <v-container v-if="CouponsIteam.length !== 0">
        <v-card-actions>
          <v-row class="pa-0">
            <v-col v-for="(items, index) in CouponsIteam" :key="index" cols="12" xs="12" sm="12" md="6">
              <!-- ตัวการ์ดของคูปอง  start-->
              <CardCoupon v-if="index % 2 === 0" :items="items" :keep="false" colorCard="blue" />
              <CardCoupon v-else :items="items" :keep="false" colorCard="green" />
              <!-- ตัวการ์ดของคูปอง  end-->
            </v-col>
          </v-row>
        </v-card-actions>
        <v-container>
          <v-pagination color="#27AB9C" v-model="pageNumber" :length="pageMax" :total-visible="7" @input="pageChange">
          </v-pagination>
        </v-container>
      </v-container>
      <!-- ในกรณี  มีคูปอง end -->

      <!-- ในกรณี ไม่มีคูปอง start -->
      <v-container v-else>
        <v-row justify="center" class="mx-2">
          <v-col cols="12">
            <v-row justify="center" class="my-5">
              <v-img :src="require('@/assets/No-Favorite.png')" max-height="421" max-width="545" height="100%"
                width="100%" contain></v-img>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row justify="center" class="my-5">
              <span
                style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C; font-size: 24px;"><b>ไม่มีรายการคูปอง</b></span>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
      <!-- ในกรณี ไม่มีคูปอง end -->
    </v-card>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardCoupon: () => import('@/components/CardCoupon/CardCouponMobile')
  },
  data () {
    return {
      pageMax: 1,
      pageNumber: 1,
      CountCoupons: 0,
      Points: 0,
      response: [],
      CouponsIteam: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      this.getDataCoupons()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    async getDataCoupons () {
      this.$store.commit('openLoader')
      var DataCoupons = {
        page: this.pageNumber
      }
      await this.$store.dispatch('actionsMyCuoponsAndPoint', DataCoupons)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateGetMyCuoponsAndPoint
      if (response.code === 200) {
        if (response.message === 'Get coupon and point success.') {
          // this.CouponsIteam = response.data.coupon
          this.CouponsIteam = []
          for (let i = 0; i < response.data.coupon.length; i++) {
            this.CouponsIteam.push({
              image: response.data.coupon[i].image,
              name: response.data.coupon[i].name,
              description: response.data.coupon[i].description,
              couponDate: response.data.coupon[i].couponDate,
              couponId: response.data.coupon[i].couponId,
              shop_name: response.data.coupon[i].shop_name
            })
          }
          this.pageMax = parseInt(response.data.total_page)
          this.Points = parseInt(response.data.points.amount) < 0 ? 0 : response.data.points.amount
          this.CountCoupons = response.data.total_coupon
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    async pageChange (page) {
      this.$store.commit('openLoader')
      var DataCoupons = {
        page: page
      }
      await this.$store.dispatch('actionsMyCuoponsAndPoint', DataCoupons)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateGetMyCuoponsAndPoint
      if (response.code === 200) {
        if (response.message === 'Get coupon and point success.') {
          // this.CouponsIteam = response.data.coupon
          this.CouponsIteam = []
          for (let i = 0; i < response.data.coupon.length; i++) {
            this.CouponsIteam.push({
              image: response.data.coupon[i].image,
              name: response.data.coupon[i].name,
              description: response.data.coupon[i].description,
              couponDate: response.data.coupon[i].couponDate,
              couponId: response.data.coupon[i].couponId,
              shop_name: response.data.coupon[i].shop_name
            })
          }
          this.pageMax = parseInt(response.data.total_page)
          this.Points = parseInt(response.data.points.amount) < 0 ? 0 : response.data.points.amount
          this.CountCoupons = response.data.total_coupon
        } else {
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
      window.scrollTo(0, 0)
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style lang="scss" scoped>
  .border-scroll {
    border: 1px solid #EBEBEB;
  }
</style>
