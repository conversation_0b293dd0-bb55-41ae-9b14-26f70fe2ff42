<template>
  <v-container ref="reloadPage">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row justify="center" class="my-4" v-if="!MobileSize && !IpadSize">
        <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" >รายละเอียดการสั่งซื้อสินค้า</v-card-title>
      </v-row>
      <v-row justify="start" class="" v-else>
        <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToPoseller()">mdi-chevron-left</v-icon> รายละเอียดการสั่งซื้อสินค้า</v-card-title>
      </v-row>
      <v-container>
        <v-row>
          <v-card outlined class="mb-4 pa-4" width="100%" v-if="MobileSize">
            <v-col :cols="IpadSize || MobileSize ? 12 : 8" :class="IpadSize || MobileSize ? 'pb-0' : ''">
              <!-- <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.order_number }}</span>
                <span  v-if="!MobileSize"> | </span>
                <v-chip class="ma-2" :color="getColor(items.status_refund)" small :text-color="getTextColor(items.status_refund)">
                  {{ getStatus(items.status_refund) }}
                </v-chip>
              </div> -->
              <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.order_number }}</span>
              </div><div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะการสั่งซื้อ : </span>
                <span  v-if="!MobileSize"> | </span>
                <v-chip class="ma-2" :color="getColor(items.status_refund)" small :text-color="getTextColor(items.status_refund)">
                  {{ getStatus(items.status_refund) }}
                </v-chip>
              </div>
              <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ : {{items.user_name}}</span>
                <span v-if="items.required_invoice === 'ขอใบกำกับภาษี' ">
                  <v-chip small class="ma-2" color="#E5EFFF" text-color="#1B5DD6">
                    ขอใบกำกับภาษี
                  </v-chip>
                </span>
              </div>
              <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-4' : 'mb-4 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : </span>
                <span>{{ new Date(items.buy_datetime).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) }}</span>
              </div>
              <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ชำระเงิน : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.paid_datetime === 'ชำระเงินแบบเครดิตเทอม' ? items.paid_datetime : new Date(items.paid_datetime).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) }}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ยกเลิก : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{items.waiting_time }}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ยกเลิกโดย : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.user_name}}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">เหตุผลในการคืน : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{items.reason}}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">จำนวนเงินที่คืน : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.total_amount}}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">การคืนสินค้า : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ getStatusResult(items.reject_detail[0].status_refund) }}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">เหตุผลในการไม่อนุมัติ : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.reject_detail[0].seller_comment }}</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ดำเนินการโดย : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ขาย</span>
              </div>
              <div :class="(IpadSize || MobileSize) && mobilystTrackingNo ? 'mb-3' : (IpadSize || MobileSize) && mobilystTrackingNo === '' ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ไม่อนุมัติ : </span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.reject_detail[0].reject_time).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
              </div>
              <!-- <div :class="IpadSize || MobileSize ? '' : IpadProSize ? 'mb-3' : 'mb-3 ml-2'" v-if="mobilystTrackingNo">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ดาวน์โหลด QR Code</span>
                <v-btn outlined class="ml-2" x-small color="#27AB9C" @click="getQrCode()"><v-icon small>mdi-download</v-icon></v-btn>
              </div> -->
              <!-- <div class="mb-3 ml-2" v-if="mobilystTrackingNo">
                <span>ดาวน์โหลด QR Code รูปแบบเต็ม</span>
                <v-btn outlined class="ml-2" x-small color="#27AB9C" @click="printBiglabel()"><v-icon small>mdi-download</v-icon></v-btn>
              </div> -->
            </v-col>
          </v-card>
          <v-card outlined class="mb-4 pa-4" width="100%" v-if="MobileSize">
            <v-col :cols="MobileSize || IpadSize ? 12:4">
              <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ส่ง : {{items.sent_date !== null ? items.sent_date : '-'}}</v-col>
              <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" :style="{ 'color': items.buyer_received_status === 'refund' ? '#E9A016' : items.buyer_received_status === 'received' ? '#1AB759' : items.buyer_received_status === 'not_received' ? '#D1392B' : '#333333' }"><span style="color: #333333;">สถานะ :</span> {{ items.buyer_received_status === 'refund' ? 'รออนุมัติ' : items.buyer_received_status === 'received' ? 'ผู้ซื้อได้รับสินค้าเรียบร้อยแล้ว' : items.buyer_received_status === 'not_received' ? 'ผู้ซื้อยังไม่ได้รับสินค้า' : '-' }}</v-col>
              <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่รับ : {{items.received_date !== null ? items.received_date : '-'}}</v-col>
              <!-- <v-row>
                <span class="ml-3 mt-5" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะ :</span>
                <v-col cols="7">
                  <v-select v-model="itemStatus" :items="status_items" item-text="text" item-value="value" color="#27AB9C" @change="OpenModelChangstatus(items)" :disabled="disablecancel" style="font-size: 14px; min-height: 50px;" outlined dense></v-select>
                </v-col>
              </v-row> -->
            </v-col>
          </v-card>
        </v-row>
      </v-container>
      <v-card outlined>
      <v-col cols="12">
          <v-container grid-list-lg>
            <v-row no-gutters>
              <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6">
                <v-row no-gutters>
                  <v-col cols="12">
                    <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</p>
                  </v-col>
                  <v-col cols="12" class="mb-4 ml-1">
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.user_address }}</span>
                  </v-col>
                  <v-col :cols="12" >
                    <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งใบกำกับภาษี</p>
                  </v-col>
                  <v-col cols="12" class="mb-2 ml-1" v-if="items.invoice_address !== '' " >
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.invoice_address }}</span>
                  </v-col>
                  <v-col cols="12" class="mb-2 ml-1" v-else >
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ '-' }}</span>
                  </v-col>
                </v-row>
              </v-col>
              <!-- Mobilyst Flash -->
              <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6">
                <v-row justify="center" :class="IpadSize || IpadProSize || MobileSize ? '':'ml-8'">
                  <v-col cols="12" >
                    <div :class="IpadSize || IpadProSize || MobileSize ? '' : 'ml-6'">{{items.product_list.service_type | JSON}}
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Standard Delivery - {{items.service_type === 'normal' ? 'ส่งแบบปกติ' : items.service_type === 'chilled' ? 'ส่งแบบควบคุมอุณหภูมิ' : items.service_type === 'frozen' ? 'ส่งแบบแช่แข็ง' : 'ส่งของขนาดใหญ่' }} {{items.business_type}} Express</span><br/>
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Tracking Number :  <b>{{items.order_no}}</b></span>
                    </div>
                    <v-btn class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" text color="#27AB9C" @click="GoToMobily(items.url_tracking)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain></v-img> ติดตามสถานะขนส่ง</v-btn>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12">
                <v-row justify="center" :class="IpadSize || IpadProSize || MobileSize ? '':'ml-0'">
                  <v-col cols="12" md="4" class="ml-auto">
                    <!-- <v-btn rounded  dark class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" color="#27AB9C" @click="openModal()" style="color: #27AB9C; text-decoration: underline;">รายละเอียดการอนุมัติ</v-btn> -->
                    <v-btn rounded outlined color="primary" dark :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" @click="openModal()">รายละเอียดการอนุมัติ</v-btn>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" class="pl-3 pt-4">
                <v-divider></v-divider>
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'"><b>รายการสั่งซื้อสินค้า</b></p>
              </v-col>
              <v-col cols="12" class="mr-4 mb-2" v-if=" items &&  items.product_list.length !== 0">
                <a-row type="flex" justify="start">
                    <v-avatar :size="MobileSize || IpadSize ? 26 : 28" class="ml-3">
                      <img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" alt="Shop">
                    </v-avatar>
                    <span span :style="{ 'font-size': MobileSize || IpadSize ? '12px' : '16px'}" style="font-weight: 600;" class="pl-1">
                      {{items.name_th}}
                    </span>
                  <!-- <span class="pl-6 pt-3 " style="font-size: 16px; font-weight: 700;">
                    <v-icon>mdi-chat</v-icon>
                    <span  v-if="!MobileSize"> | </span>
                    <v-icon>mdi-account-plus</v-icon>
                  </span> -->
                </a-row>
              </v-col>
              <v-col cols="12" class="mr-4 mb-2" v-if=" items.product_list &&  items.product_list.length !== 0">
                <a-row type="flex" justify="start">
                  <span class="pl-3 pt-1 " style="font-size: 16px; font-weight: 700;">
                    {{ items.product_list ? Object.keys(items.product_list).length:0 }} รายการสินค้า
                  </span>
                </a-row>
              </v-col>
            </v-row>
          </v-container>
          <div v-if=" items.product_list && items.product_list !== 0">
            <v-container v-if="!MobileSize && !IpadSize" grid-list-xs>
              <!-- {{items.product_list}} **** -->
              <a-table :data-source="items.product_list" :rowKey="record => record.sku" :columns="headers">
                <template  slot="productdetails" slot-scope="text, record">
                  <v-row >
                    <v-col cols="12" md="3" class="pr-0 mt-2 py-1">
                      <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''"/>
                      <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowMobile' : 'imageshow'" v-else/>
                    </v-col>
                    <v-col cols="12" md="9" >
                      <p class="mb-0 DetailsProductFront">รหัสสินค้า : {{record.sku}}<br/>{{record.product_name}}</p>
                      <div v-if="record.have_attribute === 'yes'" >
                        <span class="mb-0 mb-4 DetailsProductFront" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                        <span class="ml-3 mb-0 mb-4 DetailsProductFront"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                      </div>
                      <br/>
                      <!-- <span style="color: #27AB9C;" > &#8226; </span>
                      <span class="mb-0 mr-1 ExpressFront">{{record.key_1_value}} </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> {{record.key_2_value}}</span> -->
                      <!-- <span class="mb-0 mr-1 ExpressFront">ธรรมดา </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> 3-5 </span>
                      <span class="mb-0 ExpressFront">วันทำการ</span> -->
                    </v-col>
                  </v-row>
                </template>
                 <template  slot="quantity" slot-scope="text, record">
                  <v-row class="pa-0">
                    <v-col cols="12" class="pa-0">
                      จำนวน<span class="mx-1">{{ record.quantity }}</span>ชิ้น
                    </v-col>
                  </v-row>
                </template>
                <template  slot="price" slot-scope="text, record">
                  <v-row class="pa-0">
                    <v-col cols="12" class="pa-0">
                      <span >{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </template>
              </a-table>
            </v-container>
            <v-container v-else grid-list-xs>
              <a-table :key="index" :data-source="items.product_list" :rowKey="record => record.sku" :columns="headersMobile">
                <template slot="productdetails" slot-scope="text, record">
                  <v-row>
                    <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                      <v-img :src="record.product_image" class="imageshowMobile" v-if="record.product_image !== ''"/>
                      <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                    </v-col>
                    <v-col cols="9" md="8">
                      <span class="mb-0 DetailsProductFrontMobile">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</span><br>
                      <div class="mb-0" v-if="record.have_attribute === 'yes'">
                        <span class="mb-0 DetailsProductFrontMobile" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                        <span class="ml-3 mb-0 DetailsProductFrontMobile"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                      </div>
                      <span class="mb-0 DetailsProductFrontMobile">จำนวน: <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                      <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา: <span style="font-weight: 700;">{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span>
                    </v-col>
                    <v-col>
                      <!-- <span style="color: #27AB9C;" > &#8226; </span>
                      <span class="mb-0 mr-1 ExpressFront">{{record.key_1_value}}</span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> {{record.key_2_value}}</span> -->
                     <!--  <span class="mb-0 mr-1 ExpressFront">ธรรมดา </span>
                      <span class="mb-0 mr-1 ExpressFront" style="font-weight: bold;"> 3-5 </span>
                      <span class="mb-0 ExpressFront">วันทำการ</span> -->
                    </v-col>
                  </v-row>
                </template>
              </a-table>
            </v-container>
          </div>
      </v-col>
      <v-col cols="12" md="12" class="mt-1">
        <v-container grid-list-xs>
          <!-- สรุปรายการสั่งซื้อ desktop, ipadpro, ipad -->
          <v-row v-if="!MobileSize">
            <v-col :cols="MobileSize ? 8 : IpadSize ? 9 : 12" md="10">
              <v-row dense>
                <v-col cols="12" class="text-right">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                </v-col>
                <v-col cols="12" class="text-right">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ส่วนลด :</span>
                </v-col>
                <v-col cols="12" class="text-right">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ภาษีมูลค่าเพิ่ม :</span>
                </v-col>
                <v-col cols="12" class="text-right">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ราคารวมภาษีมูลค่าเพิ่ม :</span>
                </v-col>
                <v-col cols="12" class="text-right">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ค่าจัดส่ง :</span>
                </v-col>
                <v-col cols="12" class="text-right">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'" style="font-weight: bold; ">ราคารวมทั้งหมด :</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col :cols="MobileSize ? 4 : IpadSize ? 3:12" md="2">
              <v-row dense>
                <v-col cols="12" class="text-left">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_price_no_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                </v-col>
                <v-col cols="12" class="text-left">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_discount).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                </v-col>
                <v-col cols="12" class="text-left">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                </v-col>
                <v-col cols="12" class="text-left">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_price_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                </v-col>
                <v-col cols="12" class="text-left">
                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_shipping).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                </v-col>
                <v-col cols="12" class="text-left">
                  <span  style="font-weight: bold;" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.net_price).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- สรุปรายการสั่งซื้อ mobile -->
          <v-row v-else>
            <v-col cols="12">
              <OrderSummary :items="items"></OrderSummary>
            </v-col>
          </v-row>
        </v-container>
      </v-col>
      </v-card>
    </v-card>
    <ModalReturnProductSeller :ModalReturnProductSeller.sync="ModalReturnProductSeller"/>
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
import eventBus from '@/components/eventBus'
import { Table, Row } from 'ant-design-vue'
export default {
  components: {
    'a-row': Row,
    'a-table': Table,
    ModalReturnProductSeller: () => import('@/components/Modal/RefundProductSellerModal')
  },
  data () {
    return {
      ModalReturnProductSeller: { status: false, orderNumber: '' },
      itemColor: 'green',
      // overlay: false,
      items: [{ data_list: [] }],
      formData: [],
      paymentNumber: {},
      cardProduct: [],
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingText: '',
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: '',
      created_at: '',
      status: '',
      mobilystTrackingNo: '',
      flashTracking: '',
      status_items: [
        { text: 'ดำเนินการแล้ว', value: 'ดำเนินการแล้ว' },
        { text: 'ยังไม่ดำเนินการ', value: 'ยังไม่ดำเนินการ' },
        { text: 'ยกเลิก', value: 'ยกเลิก' }
      ],
      dialogChangstatus: false,
      disablecancel: false,
      textstatus: '',
      valChangStatus: '',
      itemStatus: '',
      estep: 0,
      isUpdate: false,
      checkbox: false,
      menu: false,
      menu2: false,
      menu3: false,
      time: null,
      modal2: false,
      dateSent: null,
      dateReceived: null,
      lazy: false,
      qrcode: '',
      // flashMCHID: process.env.VUE_APP_FLASH,
      itemRules: {
        dateSent: [
          v => !!v || 'กรุณาระบุวันที่ส่ง'],
        time: [
          v => !!v || 'กรุณาระบุเวลา'],
        dateReceived: [
          v => !!v || 'กรุณาระบุวันที่รับ']
      },
      routes: [{
        routedAt: 1523356924,
        routeAction: 'DELIVERY_CONFIRM',
        message: 'พัสดุของคุณถูกเซ็นรับแล้ว เซ็นรับโดย TH01011C27',
        state: 5
      }, {
        routedAt: 1523356924,
        routeAction: 'DELIVERY_TICKET_CREATION_SCAN',
        message: 'มีพัสดุรอการนำส่ง กรุณารอการติดต่อจากเจ้าหน้าที่ Mobilyst Tech',
        state: 2
      }, {
        routedAt: 1523356560,
        routeAction: 'SHIPMENT_WAREHOUSE_SCAN',
        message: 'พัสดุของคุณอยู่ที่ กทม. จะถูกส่งไปยัง จตุโชติ-DC',
        state: 3
      }, {
        routedAt: 1523356029,
        routeAction: 'RECEIVED',
        message: 'zhao=DC พนักงานเข้ารับพัสดุแล้ว',
        state: 6
      }]
    }
  },
  async created () {
    // console.log(this.time.padStart(2, '0'), 'time')
    await this.$EventBus.$emit('changeNav')
    // await this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    // this.dataRole = await JSON.parse(localStorage.getItem('roleUser'))
    // this.paymentNumber = await JSON.parse(
    //   Decode.decode(localStorage.getItem('orderNumberSeller'))
    // )
    // window.addEventListener('storage', await function (event) {
    //   if (event.key === 'oneData' && !event.newValue) {
    //     window.location.assign('/')
    //   }
    // })
    // if (localStorage.getItem('oneData') === null) {
    //   await this.$router.push({ path: '/' }).catch(() => {})
    // }
    await this.init()
    await this.getItemProduct()
  },
  mounted () {
    window.scrollTo(0, 0)
    eventBus.$on('setStatus', this.setStatus)
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '45%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          align: 'center',
          width: '20%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headers
    },
    // productList () {
    //   // console.log(this.items.data_list[0].product_list, 'this.items.data_list[0].product_list')
    //   var list = []
    //   list = this.items.data_list[0]
    //   return list
    // },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      // console.log('vvvv', val)
      if (val === true) {
        this.$router.push({ path: '/posellerDetailMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/POSellerDetail' })
      }
    }
  },
  methods: {
    // async setStatus () {
    //   this.ModalReturnProductSeller.status = await false
    //   await this.$refs.reloadPage.$forceUpdate()
    // },
    openModal () {
      this.ModalReturnProductSeller.status = true
      this.ModalReturnProductSeller.orderNumber = localStorage.getItem('orderNumberReturn')
      this.$EventBus.$emit('open')
    },
    async init () {
      const status = await localStorage.getItem('statusTransaction')
      if (status === 'waiting') {
        this.ModalReturnProductSeller.status = true
        this.ModalReturnProductSeller.orderNumber = localStorage.getItem('orderNumberReturn')
      } else {
        this.ModalReturnProductSeller.status = false
        this.ModalReturnProductSeller.orderNumber = localStorage.getItem('orderNumberReturn')
      }
    },
    backToPoseller () {
      if (this.MobileSize) {
        this.$router.push({ path: '/returnMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/return' }).catch(() => {})
      }
    },
    GoToMobily (Track) {
      window.open(Track)
    },
    async getItemProduct () {
      // this.overlay = true
      const shopId = localStorage.getItem('shopSellerID')
      const query = {
        seller_shop_id: shopId,
        reference_id: localStorage.getItem('orderNumberReturn')
      }
      // console.log('query', query)
      await this.$store.dispatch('actionsDetailRefundSeller', query)
      var { data = {}, result = {} } = await this.$store.state.ModuleShop.stateDetailRefundSeller
      this.items = data[0]
      this.mobilystTrackingNo = this.items.order_no
      console.log('testReturnDetail', data, result)
    },
    OpenModelChangstatus (item) {
      this.valChangStatus = ''
      this.dialogChangstatus = true
      this.valChangStatus = item
      this.textstatus = this.itemStatus
    },
    cancleStatus () {
      this.dialogChangstatus = false
      this.itemStatus = this.items.status
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    isValidDate (dateObject) {
      return new Date(dateObject).toString() !== 'Invalid Date'
    },
    formmatdate (date) {
      if (date !== null) {
        const dates = JSON.parse(JSON.stringify(date))
        const getYear = Number(dates.split('-')[0]) + 543
        return dates.split('-')[2] + '/' + dates.split('-')[1] + '/' + getYear
      }
    },
    getColor (item) {
      if (item === 'Pending' || item === 'waiting') return '#FCF0DA'
      else if (item === 'Not Paid') return '#E5EFFF'
      else if (item === 'approve' || item === 'Approve') return '#F0F9EE'
      else if (item === 'reject') return '#F7D9D9'
      else return '#F7D9D9'
    },
    getTextColor (item) {
      if (item === 'Pending' || item === 'waiting') return '#E9A016'
      else if (item === 'Not Paid') return '#1B5DD6'
      else if (item === 'approve' || item === 'Approve') return '#1AB759'
      else if (item === 'reject') return '#D1392B'
      else return '#D1392B'
    },
    getStatus (item) {
      if (item === 'Pending' || item === 'waiting') return 'รออนุมัติ'
      else if (item === 'Not Paid') return 'ยังไม่ชำระเงิน'
      else if (item === 'approve' || item === 'Approve') return 'อนุมัติ'
      else if (item === 'reject') return 'ไม่อนุมัติ'
      else return 'ยกเลิกคำสั่งซื้อ'
    },
    getStatusResult (item) {
      if (item === 'waiting' || item === 'waiting') return 'รออนุมัติ'
      else if (item === 'approve' || item === 'Approve') return 'อนุมัติ'
      else if (item === 'reject') return 'ไม่อนุมัติ'
      else return 'ไม่อนุมัติ'
    }
  }
}
</script>

<style lang="css" scoped>
::v-deep .ant-table-thead {
  display: none;
}
::v-deep .ant-table-pagination {
  display: none;
}
.imageshow {
  width: 80px;
  height: 80px;
  /* cursor: pointer; */
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.fontActive {
  color: #27AB9C;
}
.fontInactive {
  color: #a6a6a6;
}
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.fontSizeDetail {
  font-size: 16px;
}
.fontSizeDetailMobile {
  font-size: 14px;
}
.fontSizeXs {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
/* .frontFamily {
  font-family: 'Sukhumvit Set';
} */
.caption {
  font-size: 20px;
}
.HeadTableFront {
  /* font-family: 'Sukhumvit Set'; */
  font-size: 18px;
  font-weight: bold;
}
.DetailsProductFront {
  /* font-family: 'Sukhumvit Set'; */
  font-size: 14px;
}
.ExpressFront {
  /* font-family: 'Sukhumvit Set'; */
  font-size: 10px;
}
.classCycle{
  color: #27AB9C;
  font-size: 30px;
  margin-top: -21px;

}
.transparent {
  background-color: #F3F5F7 !important;
  opacity: 0.7;
  border-color: transparent !important;
}
.colorIcon .theme--light.v-icon {
  color: #27AB9C;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
</style>
