<template>
  <div>
    <v-overlay :value="Loader" style="z-index: 99999;">
      <v-row
        class="fill-height"
        align-content="center"
        justify="center"
      >
        <v-col cols="12" align="center">
          <v-progress-circular
            color="#00B500"
            indeterminate
            size="64">
          </v-progress-circular>
        </v-col>
        <v-col cols="12" align="center">
          <div v-if="$i18n.locale === 'th'" class="loading loading01">
            <span class="px-1">ก</span>
            <span class="px-1">รุ</span>
            <span class="px-1">ณ</span>
            <span class="px-1">า</span>
            <span class="px-1">ร</span>
            <span class="px-1">อ</span>
            <span class="px-1">สั</span>
            <span class="px-1">ก</span>
            <span class="px-1">ค</span>
            <span class="px-1">รู่</span>
            <span class="px-1">.</span>
            <span class="px-1">.</span>
            <span class="px-1">.</span>
          </div>
          <div v-else class="loading loading01Eng">
            <span class="px-1">L</span>
            <span class="px-1">o</span>
            <span class="px-1">a</span>
            <span class="px-1">d</span>
            <span class="px-1">i</span>
            <span class="px-1">n</span>
            <span class="px-1">g</span>
            <span class="px-1">.</span>
            <span class="px-1">.</span>
            <span class="px-1">.</span>
          </div>
          <!-- <span style="text-size: 18px; font-weight: 600;">
            กรุณารอสักครู่
          </span> -->
        </v-col>
      </v-row>
      <!-- <v-progress-circular indeterminate size="64" color="#00B500"></v-progress-circular> -->
    </v-overlay>
  </div>
</template>

<script>
export default {
  computed: {
    Loader () {
      return this.$store.state.ModuleGlobal.Loader
    }
  }
}
</script>

<style lang="scss" scoped>
.loading {
  font-size: 20px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-align: center;
  span {
    display: inline-block;
    margin: 0 -.05em;
  }
}
.loading01 {
  span {
    animation: loading01 1.4s infinite alternate;
    @for $i from 0 through 13 {
      &:nth-child(#{$i+1}) {
        animation-delay: #{$i*.1}s;
      }
    }
  }
}
.loading01Eng {
  span {
    animation: loading01 1.4s infinite alternate;
    @for $i from 0 through 10 {
      &:nth-child(#{$i+1}) {
        animation-delay: #{$i*.1}s;
      }
    }
  }
}
@keyframes loading01 {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes loading01Eng {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
