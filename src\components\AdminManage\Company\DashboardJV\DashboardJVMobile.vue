<template>
  <v-container>
    <v-card elevation="0">
      <v-row no-gutters class="d-flex" align="center">
        <v-col cols="7">
          <v-card-title style="font-weight: 700;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>แดชบอร์ด JV</v-card-title>
        </v-col>
      </v-row>
    </v-card>
    <v-row>
      <v-col cols="12">
        <v-card class="btn-box" elevation="0" v-if="chartSelected === 'revenue'">
          <v-btn :class="{'active-btn-selected': chartSelected === 'revenue'}" @click="onChartSelected('revenue')">
            ข้อมูลรายได้
          </v-btn>
          <v-btn :class="{'active-btn-default': chartSelected === 'revenue'}" @click="onChartSelected('document')">
            ข้อมูลเอกสาร
          </v-btn>
        </v-card>
        <v-card class="btn-box" elevation="0" v-if="chartSelected === 'document'">
          <v-btn :class="{'active-btn-default': chartSelected === 'document'}" @click="onChartSelected('revenue')">
            ข้อมูลรายได้
          </v-btn>
          <v-btn :class="{'active-btn-selected': chartSelected === 'document'}" @click="onChartSelected('document')">
            ข้อมูลเอกสาร
          </v-btn>
        </v-card>
      </v-col>
    </v-row>
    <v-card v-if="RevenueEnabled" elevation="0">
      <DashboardJVMobileRevenue />
    </v-card>
    <v-card v-if="DocumentEnabled" elevation="0">
      <DashboardJVMobileDocument />
    </v-card>
  </v-container>
</template>

<script>
import DashboardJVMobileRevenue from '@/components/AdminManage/Company/DashboardJV/DashboardJVMobileRevenue.vue'
import DashboardJVMobileDocument from '@/components/AdminManage/Company/DashboardJV/DashboardJVMobileDocument.vue'
export default {
  components: {
    DashboardJVMobileRevenue,
    DashboardJVMobileDocument
  },
  data () {
    return {
      chartSelected: 'revenue',
      RevenueEnabled: true,
      DocumentEnabled: false
    }
  },
  mounted () {
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardJVMobile' }).catch(() => { })
      } else {
        // window.location.replace(`${'/dashboardJV'}`)
        this.$router.push({ path: '/dashboardJV' }).catch(() => {})
      }
    }
  },
  created () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    onChartSelected (choice) {
      if (choice === 'revenue') {
        this.chartSelected = 'revenue'
        this.RevenueEnabled = true
        this.DocumentEnabled = false
        // console.log('Revenue', this.RevenueEnabled)
        // console.log('Document', this.DocumentEnabled)
        // console.log('chartSelected', this.chartSelected)
      } else if (choice === 'document') {
        this.chartSelected = 'document'
        this.DocumentEnabled = true
        this.RevenueEnabled = false
        // console.log('Revenue', this.RevenueEnabled)
        // console.log('Document', this.DocumentEnabled)
        // console.log('chartSelected', this.chartSelected)
      }
    }
  }
}
</script>

<style scoped>
.active-btn-selected {
  background-color: white !important;
  color: #27ab9c;
  border-radius: 20px;
  border: none;
  box-shadow: none;
  margin: 5px 10px;
}
.active-btn-default {
  background-color: #f3f5f7 !important;
  color: #8dd4cc;
  border-radius: 20px;
  border: none;
  box-shadow: none;
}
.btn-box {
  background-color: #f3f5f7;
  border-radius: 30px;
  width: 100%;
  padding: 5px;
}
</style>
