<template>
  <div>
    <v-dialog v-model="modalShowPosition" width="590px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card class="rounded-lg">
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalShowPosition = !modalShowPosition" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text>
            <v-row v-if="!MobileSize">
              <v-col cols="12" md="2" sm="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="12" md="10" sm="10" class="mt-5 pt-2" style="font-size: 16px;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col cols="3" md="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="9" md="10" class="pt-4" style="font-size: 12px;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row style="border: 1px solid #E6E6E6;" v-if="!MobileSize">
              <v-col cols="12" md="3" sm="3">
                <v-img v-if="sendData.img_path !== ''" :lazy-src="sendData.img_path" max-height="150" max-width="250" :src="sendData.img_path"></v-img>
              </v-col>
              <v-col cols="12" md="8" sm="8">
                <v-row no-gutters class="mt-3 pl-4">
                  <v-col cols="3">
                    <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                      ชื่อ-สกุล :
                    </p>
                  </v-col>
                  <v-col cols="9">
                    <p style="font-weight: 700; font-size: 14px; line-height: 14px; color: #333333;">
                      {{ sendData.name }}
                    </p>
                  </v-col>
                  <v-col cols="3">
                    <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                      อีเมล :
                    </p>
                  </v-col>
                  <v-col cols="9">
                    <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                      {{ sendData.email }}
                    </p>
                  </v-col>
                  <v-col cols="4">
                    <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                      เบอร์โทรศัพท์ :
                    </p>
                  </v-col>
                  <v-col cols="8">
                    <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                      {{ sendData.phone }}
                    </p>
                  </v-col>
                  <v-col cols="12" v-for="(position, index) in sendData.positions" :key="index">
                    <v-row>
                      <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          ตำแหน่ง :
                        </p>
                      </v-col>
                      <v-col cols="8" class="pt-2">
                        <p
                          @click="handleMore(position, item)"
                          style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333; cursor: pointer; margin: 0;"
                        >
                          {{ position.position_name }}
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="1" sm="1">
                <v-icon v-if="managePosition === 'yes'" @click="ShowEditPosition()">mdi-pencil-outline</v-icon>
              </v-col>
            </v-row>
            <v-row style="border: 1px solid #E6E6E6;" v-else>
              <v-col cols="3" md="3">
                <v-img v-if="sendData.img_path !== ''" :lazy-src="sendData.img_path" max-height="88" max-width="100" :src="sendData.img_path"></v-img>
              </v-col>
              <v-col cols="9" class="pl-0 pt-0">
                <v-row no-gutters class="mt-3">
                  <v-col cols="4">
                    <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                      ชื่อ-สกุล :
                    </p>
                  </v-col>
                  <v-col cols="8" class="pl-0">
                    <v-row dense no-gutters>
                      <v-col cols="10">
                        <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                          {{ sendData.name }}
                        </p>
                      </v-col>
                      <v-col cols="2">
                        <v-icon v-if="managePosition === 'yes'" @click="ShowEditPosition()" color="#27AB9C">mdi-pencil-outline</v-icon>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="3">
                    <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                      อีเมล :
                    </p>
                  </v-col>
                  <v-col cols="9">
                    <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                      {{ sendData.email }}
                    </p>
                  </v-col>
                  <v-col cols="6" class="pr-0">
                    <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                      เบอร์โทรศัพท์ :
                    </p>
                  </v-col>
                  <v-col cols="6" class="pl-0">
                    <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                      {{ sendData.phone }}
                    </p>
                  </v-col>
                  <v-col cols="12" v-for="(position, index) in sendData.positions" :key="index">
                    <v-row>
                      <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          ตำแหน่ง :
                        </p>
                      </v-col>
                      <v-col cols="8" class="pt-2">
                        <p
                          @click="handleMore(position, item)"
                          style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333; cursor: pointer; margin: 0;"
                        >
                          {{ position.position_name }}
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" dark color="#27AB9C" @click="modalShowPosition = !modalShowPosition">ย้อนกลับ</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model="modalEditPosition" width="579px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card class="rounded-lg">
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>แก้ไขตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="modalEditPosition = !modalEditPosition" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container>
          <!-- <div v-if="changeToSignCA === false"> -->
          <div>
            <v-card class="rounded-lg mt-1" v-if="!MobileSize" elevation="0">
              <v-form class="pa-2" ref="FormAddPosition" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="12" md="3" sm="3" justify-center>
                    <v-card-text class="rounded-lg mt-2">
                      <v-img max-height="150" max-width="150" :src="sendData.img_path" contain v-if="sendData.img_path !== '' && sendData.img_path !== null"></v-img>
                      <v-img max-height="150" max-width="150" src="@/assets/NoImage.png" contain v-else></v-img>
                    </v-card-text>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-row no-gutters class="mt-3 ml-6">
                      <v-col cols="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                          ชื่อ-สกุล : <b>{{ sendData.name }}</b>
                        </p>
                      </v-col>
                      <v-col cols="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          อีเมล : <b>{{ sendData.email }}</b>
                        </p>
                      </v-col>
                      <v-col cols="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          เบอร์โทรศัพท์ : <b>{{ sendData.phone }}</b>
                        </p>
                      </v-col>
                    </v-row>
                    <v-row dense no-gutters>
                      <v-col cols="12" class="mt-4 ml-4">
                        <v-autocomplete v-model="values" :items="itemPosition" item-text="position_name" return-object
                          dense chips label="กรุณาเลือกตำแหน่ง" multiple solo @change="onChange" :item-disabled="isItemDisabled">
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
            </v-card>
            <v-card class="rounded-lg mt-1" v-else elevation="0">
              <v-card-text class="px-0">
                <v-form class="py-2 px-0" ref="FormAddPosition" :lazy-validation="lazy">
                  <v-row no-gutters dense>
                    <v-col cols="3" md="3" justify-center>
                      <v-card-text class="rounded-lg mt-2">
                        <v-img max-height="88" max-width="100" :src="sendData.img_path" contain v-if="sendData.img_path !== '' && sendData.img_path !== null"></v-img>
                        <v-img max-height="88" max-width="100" src="@/assets/NoImage.png" contain v-else></v-img>
                      </v-card-text>
                    </v-col>
                    <v-col cols="9" md="8">
                      <v-row no-gutters class="mt-3 ml-6">
                        <v-col cols="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            ชื่อ-สกุล : <b>{{ sendData.name }}</b>
                          </p>
                        </v-col>
                        <v-col cols="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            อีเมล : <b>{{ sendData.email }}</b>
                          </p>
                        </v-col>
                        <v-col cols="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            เบอร์โทรศัพท์ : <b> {{ sendData.phone }}</b>
                          </p>
                        </v-col>
                      </v-row>
                    <v-row dense no-gutters>
                      <v-col cols="12" class="mt-4 ml-4">
                        <v-autocomplete v-model="values" :items="itemPosition" item-text="position_name" return-object
                          dense chips label="กรุณาเลือกตำแหน่ง" multiple solo style="font-size: 14px; width: 195px;" @change="onChange" :item-disabled="isItemDisabled">
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-card>
            <v-card-actions class="mt-5">
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="modalEditPosition = !modalEditPosition">ยกเลิก
              </v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="EditUserPositions()" :disabled="disableButton">บันทึก</v-btn>
              <!-- <v-btn class="px-5 white--text" color="#27AB9C" @click="EditUserPositions()">บันทึก</v-btn> -->
            </v-card-actions>
          </div>
        </v-container>
      </v-card>
    </v-dialog>

    <DetailPositions ref="DetailPositions" />
  </div>
</template>

<script>
export default {
  components: {
    DetailPositions: () => import(/* webpackPrefetch: true */ '@/components/Business/dialogPositions.vue')
  },
  data () {
    return {
      managePosition: '',
      activeTab1: 0,
      status1: '',
      lazy: false,
      disableButton: false,
      modalShowPosition: false,
      dialogBookbank: false,
      dialogTax: false,
      // ModalDetailUser: false,
      modalEditPosition: false,
      changeToSignCA: false,
      sendData: [],
      name: '',
      itemPosition: [],
      itemPositionInactive: [],
      values: [],
      listPositionNew: [],
      listPositionOld: [],
      positionsOld: [],
      Rules: {
        position_name: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ]
      }
    }
  },
  watch: {
    sendData (newValue) {
      this.values = newValue.positions || []
      // console.log(newValue, 'here')
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    isItemDisabled (item) {
      // ตรวจสอบว่ามีการเลือกตำแหน่งหรือไม่ และถ้ามีก็จะทำให้ตัวเลือกอื่น ๆ ถูกปิดใช้งาน
      // console.log(item.id, 'id')
      // console.log(this.values, 'id val')
      if (item.position_name === 'เจ้าของนิติบุคคล') {
        return true
      }
      return this.values.length > 0 && !this.values.find(value => value.position_id === item.id || value.id === item.id)
    },
    onChange (newValues) {
      // หากมีการเลือกตัวเลือกใหม่ ให้เก็บเฉพาะค่าที่เลือก
      this.values = newValues
    },
    open (data, taxID, status, activeTab) {
      // this.ModalDetailUser = true
      this.activeTab1 = activeTab
      this.status1 = status
      this.sendData = data
      this.positionsOld = data.positions
      this.userID = data.user_id
      this.taxID = taxID
      this.modalShowPosition = !this.modalShowPosition
      this.getListPositions()
    },
    handleMore (position, item) {
      const data = item
      this.$refs.DetailPositions.open(data, position)
    },
    // ShowEditPosition () {
    //   this.values = this.sendData.positions || []
    //   this.modalShowPosition = !this.modalShowPosition
    //   this.modalEditPosition = !this.modalEditPosition
    // },
    ShowEditPosition () {
      this.values = this.sendData.positions || []
      // console.log('1', this.values)

      // กรองค่าที่ inactive ออก (ถ้ามี)
      this.values = this.values.filter(value => !this.itemPositionInactive.some(inactive => inactive.id === value.position_id))
      // console.log('2', this.values)

      this.modalShowPosition = !this.modalShowPosition
      this.modalEditPosition = !this.modalEditPosition
    },
    async getListPositions () {
      const data = {
        tax_id: this.taxID
      }
      // console.log(data)
      await this.$store.dispatch('actionsListPositions', data)
      const positions = this.$store.state.ModuleBusiness.stateListPositions
      // this.itemPosition = positions.data.list_positions
      this.itemPosition = positions.data.list_positions.filter(position => position.active_status === 'active')
      this.itemPositionInactive = positions.data.list_positions.filter(position => position.active_status === 'inactive')
      this.managePosition = positions.data.manage_position
      // console.log(this.managePosition)
    },
    async EditUserPositions () {
      this.$store.commit('openLoader')
      this.disableButton = true
      // console.log(this.itemPosition)

      // เช็ค values มีค่าและไม่เป็น array ว่าง
      if (!this.values || this.values.length === 0) {
        this.disableButton = false
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          title: 'ไม่มีข้อมูลตำแหน่งที่เลือก',
          text: 'กรุณาเลือกตำแหน่งก่อนที่จะบันทึก'
        })
        return
      }

      // ทำการรวมค่าเก่าและใหม่เข้าด้วยกัน
      this.listPositionOld = this.values.map(position => position.position_id)
      this.listPositionNew = this.values.map(position => position.id)
      this.listAll = [...this.listPositionOld, ...this.listPositionNew]

      // filter ค่าที่เป็น null หรือ undefined ออก
      const filteredList = this.listAll.filter(item => item !== null && item !== undefined)

      var dataAssignPositions = {
        tax_id: this.taxID,
        assigned_user_id: this.userID,
        assigned_position_id: filteredList
      }

      // console.log(dataAssignPositions)

      try {
        await this.$store.dispatch('actionsAssignPositionsUser', dataAssignPositions)
        var response = await this.$store.state.ModuleBusiness.stateAssignPositionsUser

        if (response.result === 'SUCCESS') {
          this.modalEditPosition = false
          this.disableButton = false
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'ดำเนินการสำเร็จ'
          })
          await this.$EventBus.$emit('SelectDetailUser', this.activeTab1)
        } else if (response.code === 403) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'คุณไม่มีสิทธิ์แก้ไขตำแหน่ง'
          })
        } else {
          this.disableButton = false
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถดำเนินการได้'
          })
        }
      } catch (error) {
        this.disableButton = false
        this.$store.commit('closeLoader')
        console.error('An error occurred:', error)
        this.$swal.fire({
          icon: 'error',
          title: 'เกิดข้อผิดพลาด',
          text: 'ไม่สามารถดำเนินการได้'
        })
      }
    }
  }
}
</script>

<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
