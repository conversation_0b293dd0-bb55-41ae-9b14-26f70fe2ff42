<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการยกเลิก order</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายการยกเลิก order</v-card-title>
      <v-card class="elevation-0">
        <v-row dense no-gutters class="mt-2">
          <v-col cols="12" class="py-0 mb-0">
            <a-tabs @change="selectTab">
              <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
              <!-- <a-tab-pane :key="0"><span slot="tab">อนุมัติแล้ว <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countApprove }}</a-tag></span></a-tab-pane> -->
              <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#1AB759" style="border-radius: 8px;">{{ totalOrder }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab">อนุมัติ <a-tag color="#FF0000" style="border-radius: 8px;">{{ totalOrderApprove }}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col>
        </v-row>
      </v-card>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" :class="IpadSize || MobileSize ? '' : 'd-flex flex-row'">
            <v-col :cols="IpadSize || MobileSize ? 12 : 5">
              <v-text-field v-model="search" @keyup="searchText(search)" placeholder="ค้นหาจากเลข order หรือชื่อร้านค้า" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col :cols="IpadSize || MobileSize ? 12 : IpadProSize ? 7 : 6">
              <v-row no-gutters>
                <v-col :cols="IpadProSize || MobileSize ? 4 : 3" class="pt-2">
                  <span style="font-size: 16px; font-weight: 500; color: #333333;">วันที่ทำรายการ :</span>
                </v-col>
                <v-col :cols="IpadSize ? 9 : IpadProSize || MobileSize ? 8 : 6">
                  <v-dialog
                    ref="dialogContractStartDate"
                    v-model="modalContractStartDate"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field rounded readonly v-model="contractStartDate" hide-details v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#CCCCCC">mdi-calendar-multiselect</v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      color="#27AB9C"
                      v-model="startDate"
                      scrollable
                      reactive
                      locale="Th-th"
                      :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="closeModalContractStartDate()"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="setValueContractStartDate(startDate)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
          </v-col>
          <v-col cols="12" md="12" class="mt-2">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" class="d-flex align-center">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายการ{{ tab === 0 ? 'รออนุมัติ' : 'อนุมัติ' }}ยกเลิกทั้งหมด {{ tab === 0 ? totalOrder : totalOrderApprove }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายการ{{ tab === 0 ? 'รออนุมัติ' : 'อนุมัติ' }}ยกเลิกทั้งหมด {{ tab === 0 ? totalOrder : totalOrderApprove }} รายการ</span>
                <!-- <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize) && tab === 1">รายการอนุมัติยกเลิกทั้งหมด {{  }} รายการ</span> -->
                <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize) && tab === 1">รายการอนุมัติยกเลิกทั้งหมด {{ totalOrder }} รายการ</span> -->
              </v-col>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 4 : 6" :style="MobileSize ? '' : 'text-align: end;'">
                <v-btn :block="MobileSize" :disabled="DataTable.length === 0" outlined color="primary" rounded>
                  <v-img style="flex-shrink: 0; max-width: 20px;" width="20px" height="20px" src="@/assets/Export_File.png"></v-img>
                  <span class="ml-1" style="font-size: 16px; color: primary; font-weight: 500;">Export File</span>
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="DataTable"
                :search="search"
                style="width:100%;"
                height="100%"
                no-results-text="ไม่พบรหัสการสั่งซื้อหรือร้านค้า"
                no-data-text="ไม่พบรหัสการสั่งซื้อหรือร้านค้า"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                :options.sync="options"
                :items-per-page="options.itemsPerPage"
                :server-items-length="maxPage"
                @update:options="updateOptions"
                >
                <template v-slot:[`item.transaction_status`]="{ item }">
                  <v-chip small v-if="item.transaction_status === 'Cancel'" color="#F7D9D9" text-color="#F5222D">
                    ยกเลิกคำสั่งซื้อ
                  </v-chip>
                  <v-chip small v-else-if="item.transaction_status === 'Waiting_Cancel'" color="#FAAD14" text-color="#FFFFFF">
                    รออนุมัติยกเลิก
                  </v-chip>
                </template>
                <template v-slot:[`item.created_at`]="{ item }">
                  {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month:
                  'long', day: 'numeric' })}}
                </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
    // CreateAdminModal: () => import('@/components/AdminPanit/AdminManage/CreateAdminPanitModal'),
    // DetailAdminModal: () => import('@/components/AdminPanit/AdminManage/DetailAdminPanitModal')
    // EditAdminModal: () => import('@/components/AdminPanit/AdminManage/EditAdminPanitModal')
  },
  data () {
    return {
      isFirstLoad: true,
      maxPage: 0,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      totalOrder: 0,
      totalOrderApprove: 0,
      dataType: 'all',
      timer: null,
      modalContractStartDate: false,
      startDate: '',
      contractStartDate: '',
      search: '',
      tab: 0,
      DataTable: [],
      headers: [
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', width: '170', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ร้านค้า', value: 'shop_name', width: '120', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการอนุมัติ', value: 'transaction_status', width: '120', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ผู้ซื้อ', value: 'buyer_name', width: '170', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ทำรายการ', value: 'created_at', width: '120', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    this.getListData()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminListOrderCancelMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'AdminListOrderCancel')
        this.$router.push({ path: '/AdminListOrderCancel' }).catch(() => {})
      }
    },
    async tab (val) {
      this.options.page = 1
      if (val === 0) {
        this.dataType = 'all'
      } else if (val === 1) {
        this.dataType = 'approve'
      }
      await this.getListData()
    }
  },
  methods: {
    async updateOptions (options) {
      this.options = options
      // if (this.isFirstLoad) {
      //   this.isFirstLoad = false
      //   return
      // }
      await this.getListData()
    },
    async getListData () {
      this.$store.commit('openLoader')
      this.DataTable = []
      const data = {
        page: this.options.page,
        limit: this.options.itemsPerPage,
        type: this.dataType,
        order_number: this.search,
        shop_name: '',
        created_date: ''
      }
      await this.$store.dispatch('actionsgetListCancelOrderAdmin', data)
      var response = await this.$store.state.ModuleAdminManage.stategetListCancelOrderAdmin
      if (response.code === 200) {
        this.DataTable = response.data.data
        this.totalOrder = response.data.total_all
        this.totalOrderApprove = response.data.total_approve
        if (this.tab === 0) {
          this.maxPage = response.data.total_all
        } else if (this.tab === 1) {
          this.maxPage = response.data.total_approve
        }
        this.$store.commit('closeLoader')
        console.log(this.DataTable)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    selectTab (val) {
      this.search = ''
      this.tab = val
      if (this.tab === 0) {
        this.dataType = 'all'
        this.options.page = 1
      } else if (this.tab === 1) {
        this.dataType = 'approve'
        this.options.page = 1
      }
      // console.log(this.tab, '5555')
    },
    closeModalContractStartDate () {
      if (this.startDate !== '') {
        this.modalContractStartDate = false
        this.contractStartDate = ''
        this.startDate = ''
      } else {
        this.modalContractStartDate = false
        this.contractStartDate = ''
      }
    },
    searchText (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        this.options.page = 1
        this.search = val
        await this.getListData()
        console.log(this.DataTable, 'data')
      }, 500)
    }
  }
}
</script>

<style>

</style>
