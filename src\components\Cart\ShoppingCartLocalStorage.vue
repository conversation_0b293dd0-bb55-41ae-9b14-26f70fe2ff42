<template>
  <v-row dense>
    <v-col cols="12" md="12" v-if="itemsCart.length === 0">
      <h1>ไม่มีสินค้าในรถเข็น</h1>
      <v-btn color="primary" outlined @click="goHomepage('/')">กลับไปหน้าแรก</v-btn>
    </v-col>
    <v-col cols="12" md="12" v-else>
      <v-row dense>
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
        <v-col cols="12" md="12" v-if="falseAlert">
          <v-row dense justify="center">
            <v-col cols="4">
              <v-alert dense text type="info">
                กรุณาเลือกรายการสินค้าที่ต้องการชำระเงิน
              </v-alert>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="12">
          <v-card outlined class="elevation-0">
            <v-container>
              <div v-for="(item,index) in itemsCart" :key="index">
                <a-table bordered :data-source="item.product_list" :rowKey="record => record.sku" :columns="headers" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" v-if="item.checkStatus === false">
                  <template slot="title">
                    <p class="text-left">{{item.shop_name}}</p>
                  </template>
                  <template slot="actions" slot-scope="text, record">
                    <v-icon small @click="changeQuantitySwal(record, 'DELETE')">
                      mdi-delete
                    </v-icon>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" class="pr-0 py-1">
                        <v-img :src="`${record.product_image.url}`" class="imageshow" v-if="record.product_image" @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="12" md="8">
                        <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <v-col cols="12" class="py-0 px-0">
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity--, changeQuantitySwal(record, 'UPDATE')" :disabled="checkQuantity">
                        <v-icon x-small>mdi-minus</v-icon>
                      </v-btn>
                      <input v-model="record.quantity" @change="changeQuantitySwal(record, 'UPDATE')" class="AddNumberProduct" size="4" :maxlength="record.stock" :max="record.stock" type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"/>
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity++, changeQuantitySwal(record, 'UPDATE')" :disabled="disabledinput_plus">
                        <v-icon x-small> mdi-plus</v-icon>
                      </v-btn>
                    </v-col>
                  </template>
                  <template slot="price" slot-scope="text, record">
                    <v-col cols="12">
                      <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </template>
                  <template slot="net_price" slot-scope="text, record">
                    <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                </a-table>
                <a-table bordered :data-source="item.product_list" :rowKey="record => record.sku" :columns="headers" v-else>
                  <template slot="title">
                    <p class="text-left">{{item.shop_name}}</p>
                  </template>
                  <template slot="actions" slot-scope="text, record">
                    <v-icon small @click="changeQuantitySwal(record, 'DELETE')">
                      mdi-delete
                    </v-icon>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" class="pr-0 py-1">
                        <v-img :src="`${record.product_image.url}`" class="imageshow" v-if="record.product_image" @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="12" md="8">
                        <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <v-col cols="12" class="py-0 px-0">
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity--, changeQuantitySwal(record, 'UPDATE')" :disabled="checkQuantity">
                        <v-icon x-small>mdi-minus</v-icon>
                      </v-btn>
                      <input v-model="record.quantity" @change="changeQuantitySwal(record, 'UPDATE')" class="AddNumberProduct" size="4" :maxlength="record.stock" :max="record.stock" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"/>
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity++, changeQuantitySwal(record, 'UPDATE')" :disabled="disabledinput_plus">
                        <v-icon x-small> mdi-plus</v-icon>
                      </v-btn>
                    </v-col>
                  </template>
                  <template slot="price" slot-scope="text, record">
                    <v-col cols="12">
                      <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
                    </v-col>
                  </template>
                  <template slot="net_price" slot-scope="text, record">
                    <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                </a-table>
              </div>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="12" md="12">
          <v-card outlined class="elevation-0">
            <v-container grid-list-xs>
              <v-row>
                <v-col cols="12" md="10">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span class="subheader">ราคารวมทั้งหมด</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" md="2">
                  <v-row dense>
                    <v-col cols="12" class="text-left">
                      <span>{{ Number(totalPriceNoVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" class="text-right pt-5">
          <v-btn color="primary" outlined @click="goCheckout" :disabled="selectProduct === false">
            ยืนยันรายการสั่งซื้อ
          </v-btn>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data () {
    return {
      overlay: false,
      itemsCart: [],
      selectedRowKeys: [],
      shopNameList: {
        data: []
      },
      selectProduct: false,
      disabledinput_plus: false,
      totalPriceNoVat: 0,
      falseAlert: true,
      checkQuantity: false
    }
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '35%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          width: '15%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          width: '20%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          width: '20%'
        },
        {
          title: 'แอคชั่น',
          key: 'actions',
          scopedSlots: { customRender: 'actions' },
          width: '10%'
        }
      ]
      return headers
    }
  },
  async created () {
    // console.log('create ja')
    await this.getCart()
  },
  methods: {
    async onSelectChange (selectedRowKeys) {
      // console.log('selectedRowKeys', selectedRowKeys)
      // set sku select
      this.selectedRowKeys = selectedRowKeys
      if (selectedRowKeys.length === 0) {
        this.selectProduct = false
      } else {
        this.selectProduct = true
      }
      //
      // set shop select
      this.itemsCart.forEach(element => {
        element.product_list.forEach(check1 => {
          selectedRowKeys.forEach(final => {
            if (check1.sku === final) {
              element.selectData.push(check1)
            }
          })
        })
      })
      const setShopNameList = []
      for (let index = 0; index < this.itemsCart.length; index++) {
        const element = this.itemsCart[index]
        if (element.selectData.length !== 0) {
          for (let index = 0; index < element.selectData.length; index++) {
            const element2 = element.selectData[index]
            setShopNameList.push(element2.shop_name)
          }
        }
      }
      const dataShopNameList = [...new Set(setShopNameList)]
      this.shopNameList.data = [...dataShopNameList]
      this.getCart()
    },
    async getCart () {
      this.overlay = true
      this.productList = []
      this.checkQuantity = true
      if (localStorage.getItem('_cartData') !== null) {
        var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
        // console.log('localstorage', cartData)
        var data = {
          shop_to_cal: this.shopNameList.data,
          product_to_cal: this.selectedRowKeys,
          shop_list: cartData.shop_list
        }
        await this.$store.dispatch('ActionLocalstorageDetailCart', data)
        const res = await this.$store.state.ModuleCart.stateLocalstorageDetailCart
        // console.log('response ===', res.data)
        this.itemsCart = []
        if (res.message === 'Get localstorage detail cart success') {
          this.checkQuantity = false
          if (res.data.shop_list.length !== 0) {
            for (let indexShop = 0; indexShop < res.data.shop_list.length; indexShop++) {
              const element = res.data.shop_list[indexShop]
              if (element.product_list.length === 0) {
                res.data.shop_list.splice(indexShop, 1)
              }
            }
            var setData = {
              product_to_cal: res.data.product_to_cal,
              shop_to_cal: res.data.shop_to_cal,
              address_data: {},
              shop_list: res.data.shop_list
            }
            await localStorage.setItem('_cartData', Encode.encode(setData))
            res.data.shop_list.forEach(list => {
              if (this.shopNameList.data.length !== 0) {
                this.falseAlert = false
                if (list.shop_name !== this.shopNameList.data[0]) {
                  list.checkStatus = true
                  this.itemsCart.push(list)
                } else {
                  list.checkStatus = false
                  this.itemsCart.push(list)
                }
              } else {
                this.falseAlert = true
                list.checkStatus = false
                this.itemsCart.push(list)
              }
            })
            // console.log('log list cart Ja', typeof res.data.total_price_no_vat)
            this.totalPriceNoVat = res.data.total_price_no_vat
            this.overlay = false
          } else {
            this.itemsCart = []
            this.overlay = false
          }
        }
      }
    },
    goHomepage () {
      this.$router.push('/')
    },
    goProductDetail (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      // console.log('nameCleaned', `/DetailProduct/${nameCleaned}-${item.product_id}`)
      this.$router.push(`/DetailProduct/${nameCleaned}-${item.product_id}`)
    },
    changeQuantitySwal (item, strkey) {
      // const ToastDelete = this.$swal.mixin({
      //   toast: true,
      //   showCancelButton: true,
      //   confirmButtonText: 'ยืนยัน',
      //   cancelButtonText: 'ยกเลิก',
      //   cancelButtonColor: '#d33'
      // })
      if (strkey === 'DELETE') {
        this.checkQuantity = true
        item.quantity = 0
        this.$swal.fire({
          toast: true,
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          cancelButtonColor: '#d33',
          icon: 'warning',
          title: 'คุณต้องการลบสินค้าหรือไม่'
        }).then((result) => {
          if (result.isConfirmed) {
            this.updateCartItem(item)
          } else if (result.isDismissed) {
            this.checkQuantity = true
            this.getCart()
          }
        }).catch(() => {
        })
      } else if (strkey === 'UPDATE') {
        this.checkQuantity = true
        // console.log('item.quantity =====>', item.quantity)
        if (parseInt(item.quantity) === 0) {
          this.$swal.fire({
            toast: true,
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            cancelButtonColor: '#d33',
            icon: 'warning',
            title: 'คุณต้องการลบสินค้าหรือไม่'
          }).then((result) => {
            if (result.isConfirmed) {
              this.updateCartItem(item)
            } else if (result.isDismissed) {
              this.checkQuantity = false
              this.getCart()
            }
          }).catch(() => {
          })
        } else if (parseInt(item.quantity) < 0 || item.quantity === '') {
          item.quantity = 1
          this.checkQuantity = false
        } else {
          this.checkQuantity = false
          this.updateCartItem(item)
        }
      }
    },
    async updateCartItem (product) {
      var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
      var finish = false
      for (let shopIndex = 0; shopIndex < cartData.shop_list.length; shopIndex++) {
        var shop = cartData.shop_list[shopIndex]
        for (let productIndex = 0; productIndex < shop.product_list.length; productIndex++) {
          const thisproduct = shop.product_list[productIndex]
          if (parseInt(product.product_id) === parseInt(thisproduct.product_id)) {
            this.checkQuantity = false
            if (parseInt(product.quantity) === 0) {
              // const Toast = this.$swal.mixin({
              //   toast: true,
              //   showConfirmButton: false,
              //   timer: 1500,
              //   timerProgressBar: true
              // })
              this.$swal.fire({
                toast: true,
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true,
                icon: 'success',
                title: 'ลบสินค้าในรถเข็นเรียบร้อย'
              })
              cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
              await localStorage.setItem('_cartData', Encode.encode(cartData))
              // console.log('cart data', cartData)
              this.$EventBus.$emit('getCartPopOver')
              this.getCart()
            } else if (parseInt(product.quantity) > parseInt(product.stock)) {
              this.disabledinput_plus = true
              this.$swal.fire({
                toast: true,
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                icon: 'warning',
                text: 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว'
              })
            } else {
              this.disabledinput_plus = false
              // const Toast = this.$swal.mixin({
              //   toast: true,
              //   showConfirmButton: false,
              //   timer: 1500,
              //   timerProgressBar: true
              // })
              this.$swal.fire({
                toast: true,
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true,
                icon: 'success',
                title: 'อัปเดตจำนวนสินค้าเรียบร้อย'
              })
              cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
              cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
              // console.log('cartData', cartData.shop_list[shopIndex].product_list[productIndex])
              await localStorage.setItem('_cartData', Encode.encode(cartData))
              this.$EventBus.$emit('getCartPopOver')
              this.getCart()
            }
            finish = true
            break
          }
        }
        if (finish) {
          break
        }
      }
    },
    goCheckout () {
      this.$router.push('/checkout')
    }
  }
}
</script>

<style scoped>
input {
  width: 100%;
  margin: 8px 0;
  box-sizing: border-box;
  border: 1px solid rgb(139, 136, 136);
  background-color: lightblue;
}
.AddNumberProduct {
  height: 25px;
  width: 40px;
  box-shadow: inset 0 1px 3px 0 rgba(232, 232, 232, 0.5);
  background-color: #ffffff;
  text-align: center;
}
.imageshow {
  width: 50px;
  height: 50px;
  cursor: pointer;
}
</style>
