<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">จัดการตำแหน่งภายในบริษัท</v-card-title>
      <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> จัดการตำแหน่งภายในบริษัท</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="px-2 py-0">
          <a-tabs @change="selectOrder">
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{
                  countPOAll }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">ใช้งานได้ <a-tag color="#1AB759" style="border-radius: 8px;">
                  {{ countPOSuccess }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#F5222D" style="border-radius: 8px;">
                  {{ countPOReject }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-2 pt-0' : 'pl-2 pr-2 mb-3'">
          <v-text-field v-if="disableTable === true" v-model="search" dense hide-details outlined placeholder="ค้นหาจากตำแหน่งในบริษัท">
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col cols="12" md="6" sm="2"  align="right" :class="MobileSize ? 'px-2' : 'pr-4'" v-if="StateStatus !== 2 && !IpadSize">
          <v-btn v-if="MobileSize || IpadSize" @click="openDialog()" :block="MobileSize" color="#27AB9C" dense :class="MobileSize ?  'white--text' : 'px-2 white--text'">
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
          <v-btn v-else @click="openDialog()" color="#27AB9C" dense class="pl-4 pr-4 pr white--text">
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
        </v-col>
        <v-col cols="12" sm="9" v-if="disableTable === true" :class="MobileSize ? 'pl-2 pr-2 mb-3 mt-3' : IpadSize ? 'pl-2 pr-2 pt-2 mb-3 mt-3' : 'pl-3 pr-3 mb-3 mt-3'">
          <span class="mt-4" v-if="MobileSize || IpadSize ">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
              v-if="StateStatus === 0">รายการตำแหน่งในบริษัททั้งหมด {{ showCountOrder }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
              v-else-if="StateStatus === 1">รายการตำแหน่งในบริษัทที่ใช้งานได้ {{ showCountOrder }}
              รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
              v-else-if="StateStatus === 2">รายการตำแหน่งในบริษัทที่ยกเลิก {{ showCountOrder }}
              รายการ</span>
          </span>
          <span v-else>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
              v-if="StateStatus === 0">รายการตำแหน่งในบริษัททั้งหมด {{ showCountOrder }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
              v-else-if="StateStatus === 1">รายการตำแหน่งในบริษัทที่ใช้งานได้ {{ showCountOrder }}
              รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
              v-else-if="StateStatus === 2">รายการตำแหน่งในบริษัทที่ยกเลิก {{ showCountOrder }}
              รายการ</span>
          </span>
        </v-col>
        <v-col cols="12" md="6" sm="2"  :class="MobileSize ? 'px-2' : 'pr-4 pt-4 pb-4'" v-if="StateStatus !== 2 && IpadSize">
          <v-btn v-if="MobileSize || IpadSize" @click="openDialog()" :block="MobileSize" color="#27AB9C" dense :class="MobileSize ?  'white--text' : 'px-2 white--text'">
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
          <v-btn v-else @click="openDialog()" color="#27AB9C" dense class="pl-4 pr-4 pr white--text">
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
        </v-col>
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-2 my-2" min-height="436">
            <v-data-table
              :headers="keyCheckHead == 0 ? headers : keyCheckHead == 1 ? headersSuccess : keyCheckHead == 2 ? headersActive : keyCheckHead == 3 ? headersWaitingApprove : keyCheckHead == 4 ? headersEdited : headersFail"
              :items="DataTable" :search="search" :page.sync="page" style="width:100%;" height="100%"
              @pagination="countOrdar" :items-per-page="10" no-results-text="ไม่พบตำแหน่งในบริษัทที่ค้นหา" class="" :footer-props="{'items-per-page-text':'จำนวนแถว'}">
              <template v-slot:[`item.status`]="{ item }" class="fontDataForTable">
                <span v-if="item.status === 'success'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">สำเร็จ</v-chip>
                </span>
                <span v-else-if="item.status === 'active'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">ใช้งานได้</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_approve'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'edited'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#FAAD14">ถูกแก้ไข</v-chip>
                </span>
                <span v-else-if="item.status === 'reject'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#F5222D">ปฏิเสธ</v-chip>
                </span>
                <span v-else>
                  <v-chip small class="ma-2" color="#F7D9D9" text-color="#F5222D">ยกเลิก</v-chip>
                </span>
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month:
                'long', day: 'numeric' })}}
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <!-- <v-btn
                  style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  outlined small @click="DetailOrder(item)">
                  <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                </v-btn> -->
                <v-btn text rounded color="#27AB9C" @click="DetailOrder(item)" small>
                  <b>รายละเอียด</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain
              aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
            <b v-if="StateStatus === 0">คุณยังไม่มีรายการตำแหน่งในบริษัททั้งหมด</b>
            <b v-else-if="StateStatus === 1">คุณยังไม่มีรายการตำแหน่งในบริษัทที่ใช้งานได้</b>
            <b v-else-if="StateStatus === 2">คุณยังไม่มีรายการตำแหน่งในบริษัทที่ยกเลิก</b>
          </h2>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="dialog_user" width="674px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
      <v-card class=".rounded-lg" elevation="0">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px; font-weight: bold;">
            <font color="#27AB9C">ตำแหน่งภายในบริษัท</font>
          </span>
          <v-btn icon dark @click="dialog_user = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="mb-15">
          <v-row class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar color="#F2F2F2" rounded icon size="70">
                <!-- <v-img src="@/assets/Create_Store/industry2.png"></v-img> -->
                <v-img contain :src="require('@/assets/icons/use.jpg')"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p class="mt-5" style="font-weight: 400; font-size: 20px; text-transform: #333333;" v-if="!MobileSize">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p class="mt-5 pl-2" style="font-weight: 400; font-size: 16px; text-transform: #333333;" v-else>
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-card class="rounded-lg" elevation="0" style="border: 1px solid #E6E6E6;">
                <v-row>
                  <v-col cols="12" md="12" sm="12" xs="12">
                    <v-row>
                      <v-col cols="12">
                        <v-container>
                          <v-row no-gutters>
                            <v-col cols="12" md="2" sm="2" align="right" v-if="MobileSize" class="mb-4">
                              <div>
                                <v-btn v-if="openStatus === 'active'" @click="EditPosition()" icon dense>
                                  <v-avatar rounded size="18">
                                    <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                                  </v-avatar>
                                </v-btn>
                                <v-btn v-if="openStatus === 'inactive'" dense dark color="success" class="" @click="cancelPosition(companyData.id, position_id, 'active')">
                                เปิดใช้งาน
                                </v-btn>
                              </div>
                            </v-col>
                            <v-col cols="10" md="10" sm="10">
                              <p class="mt-0"
                                style="font-weight: 400; font-size: 16px; line-height: 20px; color: #333333;">
                                ตำแหน่ง : {{ position }}
                              </p>
                            </v-col>
                            <v-col cols="2" md="2" sm="2" align="right" v-if="!MobileSize">
                              <div class="mr-3" style="margin-Top: -8px">
                                <v-btn v-if="openStatus === 'active'" @click="EditPosition()" icon dense>
                                  <v-avatar rounded size="18">
                                    <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                                  </v-avatar>
                                </v-btn>
                                <v-btn v-if="openStatus === 'inactive'" dense dark color="success" class="" @click="cancelPosition(companyData.id, position_id, 'active')">
                                เปิดใช้งาน
                                </v-btn>
                              </div>
                            </v-col>
                            <v-col cols="12" md="12">
                              <p class="mt-0"
                                style="font-weight: 400; font-size: 16px; line-height: 8px; color: #27AB9C;">
                                สิทธิ์การใช้งาน
                              </p>
                            </v-col>
                            <v-col v-for="(item, index) in detailPosition" :key="index" cols="12" md="12">
                              <v-row dense>
                                <v-col cols="10" md="11" sm="11">
                                  <v-list-item-content
                                    :style="MobileSize ? 'font-weight: 400; font-size: 16px; line-height: 16px; color:: #333333; padding-top: 6px;' : 'font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;'">
                                    {{item.name}}
                                  </v-list-item-content>
                                </v-col>
                                <v-col cols="2" md="1" sm="1">
                                  <v-checkbox readonly v-model="item.status" value="1" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-container>
                      </v-col>
                      <!-- <v-col cols="2">
                        <div class="mt-4">
                          <v-btn @click="EditPosition()" icon dense>
                            <v-avatar rounded size="18">
                              <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                            </v-avatar>
                          </v-btn>
                        </div>
                      </v-col> -->
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" xs="12" align="right" class="pr-8">
                    <v-row no-gutters>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <div disable class=" mb-6 ml-2" style="border-bottom: #E6E6E6 4px dashed;"></div>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <span style="font-weight: 500; font-size: 12px; line-height: 1px; color: #333333;">สร้างเมื่อ
                          : {{ createDate }}</span>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <span style="font-weight: 500; font-size: 12px; line-height: 1px; color: #333333;">แก้ไขเมื่อ
                          : {{ update }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- dialog  Edit Position -->
    <v-dialog v-model="dialogEdit" width="600px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
      <v-card class=".rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px; font-weight: bold;">
            <font color="#27AB9C">ตำแหน่งภายในบริษัท</font>
          </span>
          <v-btn icon dark @click="dialogEdit = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <!-- Mobile -->
        <v-card-text v-if="MobileSize">
          <v-row no-gutters class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar rounded size="72">
                <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p class="mt-5" style="font-weight: 400; font-size: 20px; text-transform: #333333;" v-if="!MobileSize">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p class="mt-5 pl-2" style="font-weight: 400; font-size: 16px; text-transform: #333333;" v-else>
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col class="mt-8" cols="12" md="12">
              <p style="font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;margin-left: 1px">
                ชื่อตำแหน่ง
              </p>
              <v-text-field placeholder="ระบุตำแหน่ง" v-model="position" dense outlined :rules="itemRules.position_name"></v-text-field>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12" dense style="margin-left:6px">
              <v-row dense>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    เลือกสิทธิ์การใช้งานทั้งหมด</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="allPosition" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการข้อมูลบริษัทได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_company" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content
                    :style="MobileSize ? 'font-weight: 400; font-size: 15px; line-height: 20px; color:: #333333; padding-top: 2px;' : 'font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;'">
                    สามารถจัดการตำแหน่งภายในบริษัทและเพิ่มผู้ใช้เข้าอยู่ภายใต้ตำแหน่งนั้นได้
                  </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_permission" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการข้อมูลร้านค้าคู่ค้าได้ </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="partner" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="order" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถอนุมัติรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="approve_order" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการการชำระจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="payment" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการรายงานรายจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="report" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถติดตามสถานะการส่งสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="tracking" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถติดตามการคืนสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="refund" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถประเมินความพึงพอใจสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="review" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <!-- web -->
        <v-card-text v-else>
          <v-row no-gutters class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar rounded size="72">
                <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p class="mt-5" style="font-weight: 400; font-size: 20px; text-transform: #333333;" v-if="!MobileSize">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p class="mt-5 pl-2" style="font-weight: 400; font-size: 16px; text-transform: #333333;" v-else>
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col class="mt-8" cols="12" md="12">
              <p style="font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;margin-left: 1px">
                ชื่อตำแหน่ง
              </p>
              <v-text-field placeholder="ระบุตำแหน่ง" v-model="position" dense outlined :rules="itemRules.position_name"></v-text-field>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12" dense style="margin-left:6px">
              <v-row dense>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    เลือกสิทธิ์การใช้งานทั้งหมด</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="allPosition" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการข้อมูลบริษัทได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_company" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 15px; line-height: 20px; color:: #333333; padding-top: 2px;' : 'font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;'">
                    สามารถจัดการตำแหน่งภายในบริษัทและเพิ่มผู้ใช้เข้าอยู่ภายใต้ตำแหน่งนั้นได้
                  </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_permission" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการข้อมูลร้านค้าคู่ค้าได้ </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="partner" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="order" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถอนุมัติรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="approve_order" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการการชำระจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="payment" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการรายงานรายจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="report" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถติดตามสถานะการส่งสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="tracking" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถติดตามการคืนสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="refund" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถประเมินความพึงพอใจสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="review" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-container style="display: flex; justify-content: flex-end">
            <v-btn dense dark color="error" class="pl-7 pr-7 mt-2" @click="cancelPosition(companyData.id, position_id, 'inactive')">
              ยกเลิกการใช้งาน
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="EditSave()" :disabled="this.allPosition === false">
              บันทึก
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- dialog  Create  Position -->
    <v-dialog v-model="dialogCreate" class=".rounded-lg" width="600px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
      <v-card class=".rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px; font-weight: bold;">
            <font color="#27AB9C">ตำแหน่งภายในบริษัท</font>
          </span>
          <v-btn icon dark @click="dialogCreate = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row no-gutters class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar rounded size="72">
                <!-- <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png"></v-img> -->
                <v-img contain :src="require('@/assets/icons/use.jpg')"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p class="mt-5" style="font-weight: 400; font-size: 20px; text-transform: #333333;" v-if="!MobileSize">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p class="mt-5 pl-2" style="font-weight: 400; font-size: 16px; text-transform: #333333;" v-else>
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col class="mt-8" cols="12" md="12">
              <p style="font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;">
                ชื่อตำแหน่ง
              </p>
              <v-text-field placeholder="ระบุตำแหน่ง" v-model="position" dense outlined :rules="itemRules.position_name"></v-text-field>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12" dense>
              <!-- Mobile -->
              <v-row v-if="MobileSize" dense style="margin-left: 4px">
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    เลือกสิทธิ์การใช้งานทั้งหมด</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="allPosition" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการข้อมูลบริษัทได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_company" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 15px; line-height: 20px; color:: #333333; padding-top: 2px;' : 'font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;'">
                    สามารถจัดการตำแหน่งภายในบริษัทและเพิ่มผู้ใช้เข้าอยู่ภายใต้ตำแหน่งนั้นได้
                  </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_permission" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการข้อมูลร้านค้าคู่ค้าได้ </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="partner" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="order" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถอนุมัติรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="approve_order" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการการชำระจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="payment" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถจัดการรายงานรายจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="report" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถติดตามสถานะการส่งสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="tracking" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถติดตามการคืนสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="refund" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 16px; color:: #333333;">
                    สามารถประเมินความพึงพอใจสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="review" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
              </v-row>
              <!-- web -->
              <v-row v-else dense style="margin-left: 4px">
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    เลือกสิทธิ์การใช้งานทั้งหมด</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="allPosition" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการข้อมูลบริษัทได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_company" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 15px; line-height: 20px; color:: #333333; padding-top: 2px;' : 'font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;'">
                    สามารถจัดการตำแหน่งภายในบริษัทและเพิ่มผู้ใช้เข้าอยู่ภายใต้ตำแหน่งนั้นได้
                  </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="set_permission" @click="checkAllPosition()" value="1" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการข้อมูลร้านค้าคู่ค้าได้ </v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="partner" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="order" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถอนุมัติรายการสั่งซื้อได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="approve_order" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C"
                    hide-details class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการการชำระจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="payment" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถจัดการรายงานรายจ่ายเงินได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="report" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถติดตามสถานะการส่งสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="tracking" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถติดตามการคืนสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="refund" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
                <v-col cols="10" md="11" sm="11">
                  <v-list-item-content style="font-weight: 400; font-size: 15px; line-height: 1px; color:: #333333;">
                    สามารถประเมินความพึงพอใจสินค้าได้</v-list-item-content>
                </v-col>
                <v-col cols="2" md="1" sm="1" :class="MobileSize ? 'pt-0' : ''">
                  <v-checkbox v-model="review" value="1" @click="checkAllPosition()" style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"></v-checkbox>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-container style="display: flex; justify-content: flex-end">
            <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">
              ยกเลิก
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="createPosition()" :disabled="this.allPosition === false">
              บันทึก
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      itemRules: {
        position_name: [
          v => !!v || 'กรุณาระบุชื่อตำแหน่ง'
        ]
      },
      quantity: 0,
      countPOAll: 0,
      countPOSuccess: 0,
      countPOActive: 0,
      countPOWaitingApprove: 0,
      countPOEdited: 0,
      countPOReject: 0,
      dialogCreate: false,
      dialogEdit: false,
      position: '',
      search: '',
      name: 'รายละเอียดตำแหน่งและสิทธิ์การใช้งาน',
      dialog_user: false,
      orderList: '',
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: false,
      companyData: [],
      seller_shop_id: null,
      allPosition: false,
      dataRole: '',
      page: 1,
      keyCheckHead: 0,
      dialog_rank: false,
      createDate: '',
      update: '',
      set_company: '0',
      set_permission: '0',
      partner: '0',
      order: '0',
      approve_order: '0',
      payment: '0',
      report: '0',
      tracking: '0',
      refund: '0',
      review: '0',
      detailPosition: [],
      position_id: '',
      list_company: '',
      position_user: [
        {
          name: 'สามารถจัดการข้อมูลบริษัทได้',
          status: '0'
        },
        {
          name: 'สามารถจัดการตำแหน่งภายในบริษัทและเพิ่มผู้ใช้เข้าอยู่ภายใต้ตำแหน่งนั้นได้',
          status: '0'
        },
        {
          name: ' สามารถจัดการข้อมูลร้านค้าคู่ค้าได้',
          status: '0'
        },
        {
          name: 'สามารถจัดการรายการสั่งซื้อได้',
          status: '0'
        },
        {
          name: ' สามารถอนุมัติรายการสั่งซื้อได้',
          status: '0'
        },
        {
          name: 'สามารถจัดการการชำระจ่ายเงินได้',
          status: '0'
        },
        {
          name: 'สามารถจัดการรายงานรายจ่ายเงินได้',
          status: '0'
        },
        {
          name: 'สามารถติดตามสถานะการส่งสินค้าได้',
          status: '0'
        },
        {
          name: 'สามารถติดตามการคืนสินค้าได้',
          status: '0'
        },
        {
          name: ' สามารถประเมินความพึงพอใจสินค้าได้',
          status: '0'
        }
      ],
      headers: [
        { text: 'ตำแหน่ง', value: 'name', sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', filterable: false, value: 'created_at', sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', filterable: false, sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersSuccess: [
        { text: 'ตำแหน่ง', value: 'name', sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'created_at', filterable: false, sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', filterable: false, sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersActive: [
        { text: 'ตำแหน่ง', value: 'name', sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'created_at', filterable: false, sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', filterable: false, sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      eadersFail: [
        { text: 'ตำแหน่ง', value: 'name', sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'company_name', filterable: false, sortable: false, align: 'start', width: '175', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', filterable: false, sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      openStatus: ''
    }
  },
  watch: {
    allPosition (val) {
      // console.log(val)
      if (val === true) {
        this.set_company = '1'
        this.set_permission = '1'
        this.partner = '1'
        this.order = '1'
        this.approve_order = '1'
        this.payment = '1'
        this.report = '1'
        this.tracking = '1'
        this.refund = '1'
        this.review = '1'
      } else {
        if (val === false) {
          this.set_company = null
          this.set_permission = null
          this.partner = null
          this.order = null
          this.approve_order = null
          this.payment = null
          this.report = null
          this.tracking = null
          this.refund = null
          this.review = null
        }
      }
    },
    partner (val) {
      // console.log('partner', val)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManagePositionMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ManagePosition' }).catch(() => { })
      }
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    StateStatus (val) {
      if (val === 0) {
        this.DataTable = this.orderList.data.all
        this.countPOAll = this.orderList.data.all.length
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.data.active
        this.countPOSuccess = this.orderList.data.active.length
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.data.inactive
        this.countPOReject = this.orderList.data.inactive.length
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      }
    }
  },
  created () {
    // console.log('StateStatus', this.StateStatus)
    // this.$EventBus.$emit('changeNavCompany')
    // this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      //   this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
      this.getListData()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    checkAllPosition () {
      if (this.set_company === '1' && this.set_permission === '1' && this.partner === '1' && this.order === '1' && this.approve_order === '1' && this.payment === '1' && this.report === '1' && this.tracking === '1' && this.refund === '1' && this.review === '1') {
        this.allPosition = true
      } else {
        if (this.set_company === '1' || this.set_permission === '1' || this.partner === '1' || this.order === '1' || this.approve_order === '1' || this.payment === '1' || this.report === '1' || this.tracking === '1' || this.refund === '1' || this.review === '1') {
          this.allPosition = null
        } else {
          this.allPosition = false
        }
      }
      // console.log('checkAllPosition', this.allPosition)
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    countOrdar (pagination) {
      if (this.disableTable === false) {
        this.showCountOrder = 0
      } else {
        this.showCountOrder = pagination.itemsLength
      }
      window.scrollTo(0, 0)
    },
    closeModal () {
      this.dialogCreate = false
      this.dialogEdit = false
      this.dialog_user = false
    },
    openDialog () {
      this.position = ''
      this.allPosition = false
      this.set_company = '0'
      this.set_permission = '0'
      this.partner = '0'
      this.order = '0'
      this.approve_order = '0'
      this.payment = '0'
      this.report = '0'
      this.tracking = '0'
      this.refund = '0'
      this.review = '0'
      this.dialogCreate = true
      this.checkAllPosition()
    },
    selectOrder (item) {
      this.StateStatus = item
      this.page = 1
      this.getListData()
    },
    async cancelPosition (typeId, positionId, status) {
      var text = ''
      var btnColor = ''
      if (status === 'active') {
        text = 'ยืนยันการเปิดการใช้งาน ใช่หรือไม่?'
        btnColor = '#1ab759'
      } else {
        text = 'ยืนยันการยกเลิกการใช้งาน ใช่หรือไม่?'
        btnColor = '#f5222d'
      }

      var datas = await {
        position_id: positionId,
        position_status: status,
        type: 'company',
        type_id: typeId
      }
      await this.$swal.fire({
        text: `${text}`,
        type: 'warning',
        // buttons: {
        //   confirm: 'ยืนยัน',
        //   cancel: 'ย้อนกลับ'
        // }
        showCancelButton: true,
        confirmButtonColor: `${btnColor}`,
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        closeOnConfirm: false,
        closeOnCancel: false,
        reverseButtons: true
      }).then(async (result) => {
        if (result.isConfirmed) {
          await this.$store.dispatch('actionUpdatePositionStatus', datas)
          var data = await this.$store.state.ModuleShop.stateUpdatePositionStatus
          if (data.result === 'SUCCESS') {
            this.getListData()
            if (status === 'active') {
              this.$swal.fire(
                {
                  icon: 'success',
                  html: '<h3>ยืนยันการเปิดใช้งานสำเร็จ</h3>',
                  showConfirmButton: false,
                  timer: 1500
                })
              this.dialog_user = false
            } else {
              this.$swal.fire(
                {
                  icon: 'success',
                  html: '<h3>ยืนยันการยกเลิกการใช้งานสำเร็จ</h3>',
                  showConfirmButton: false,
                  timer: 1500
                })
              this.dialogEdit = false
            }
          } else {
            if (data.message === 'This user is Unauthorized' || data.message === 'This user is unauthorized.' || data.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else {
              this.$store.commit('closeLoader')
              this.getListData()
              this.$swal.fire(
                {
                  icon: 'error',
                  html: '<h3>ยืนยันการยกเลิกการใช้งานไม่สำเร็จ</h3>',
                  showConfirmButton: false,
                  timer: 1500
                })
              this.dialogEdit = false
            }
          }
        } else {
          this.dialogEdit = false
          this.dialog_user = false
        }
      })
    },
    async getListData () {
      var datas = {
        company_id: this.companyData.id
      }
      await this.$store.dispatch('actionsListUserCompanyPosition', datas)
      this.orderList = await this.$store.state.ModuleShop.stateListUserCompany
      // console.log(this.orderList)
      if (this.orderList.result === 'SUCCESS') {
        if (this.orderList.message === 'Get list position of company success.') {
          this.countPOAll = this.orderList.data.all.length
          this.countPOReject = this.orderList.data.inactive.length
          this.countPOSuccess = this.orderList.data.active.length
          if (this.StateStatus === 0) {
            this.DataTable = this.orderList.data.all
            // this.showCountOrder = this.orderList.data.all.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
              this.countPOAll = 0
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            this.DataTable = this.orderList.data.active
            // this.showCountOrder = this.orderList.data.active.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
              this.countPOSuccess = 0
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            this.DataTable = this.orderList.data.inactive
            // this.showCountOrder = this.orderList.data.inactive.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
              this.countPOReject = 0
            } else {
              this.disableTable = true
            }
          }
        } else {
          this.$swal.fire({
            icon: 'error',
            html: '<h3>ผู้ใช้งานนี้ไม่มีสิทธิ์การเข้าถึงใบเสนอราคาบริษัท</h3>',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/detailCompany' }).catch(() => { })
        }
      } else {
        if (this.orderList.message === 'This user is Unauthorized' || this.orderList.message === 'This user is unauthorized.' || this.orderList.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: this.orderList.message
          })
        }
      }
    },
    async createPosition () {
      this.$store.commit('openLoader')
      if (this.position === '' || this.position === null) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกชื่อตำแหน่งที่ต้องการสร้าง</h3>' })
      } else if (this.position !== '' || this.position !== null) {
        var data = {
          business_id: '5',
          company_id: this.companyData.id,
          name: this.position,
          position_data: {
            set_company: this.set_company !== null ? '1' : '0',
            set_permission: this.set_permission !== null ? '1' : '0',
            partner: this.partner !== null ? '1' : '0',
            order: this.order !== null ? '1' : '0',
            approve_order: this.approve_order !== null ? '1' : '0',
            payment: this.payment !== null ? '1' : '0',
            report: this.report !== null ? '1' : '0',
            tracking: this.tracking !== null ? '1' : '0',
            refund: this.refund !== null ? '1' : '0',
            review: this.review !== null ? '1' : '0'
          }
        }
        await this.$store.dispatch('actionsCreatePositon', data)
        var res = await this.$store.state.ModuleShop.stateCreatePosition
        if (res.code === 200) {
          this.dialogCreate = false
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>สร้างตำแหน่งในบริษัทสำเร็จ</h3>' })
          this.getListData()
          this.$store.commit('closeLoader')
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            if (res.message === 'Please set permissions of this position.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>โปรดเลือกสิทธิ์การใช้งาน</h3>' })
            } else if (res.message === 'The position name is already exist in this company.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ตำแหน่งนี้มีอยู่แล้วในบริษัท</h3>' })
            } else if (res.message === 'Company not found.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ไม่พบบริษัทนี้</h3>' })
            } else if (res.message === 'The user was not found in the company.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ไม่พบผู้ใช้งานนี้ภายในบริษัทนี้</h3>' })
            } else if (res.message === 'Data missing. Please check your parameter and try again.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ข้อมูลไม่ครบ กรุณาตรวจสอบดูอีกครั้ง</h3>' })
            } else {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: res.message })
            }
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
    },
    async EditPosition () {
      this.$store.commit('openLoader')
      this.dialog_user = false
      var data = {
        company_id: this.companyData.id,
        company_position_id: this.position_id
      }
      await this.$store.dispatch('actionsDetailUserCompanyPosition', data)
      var responselist = await this.$store.state.ModuleShop.stateDetailUserCompanyPosition
      if (responselist.result === 'SUCCESS') {
        this.dialogEdit = true
        this.getListData()
        this.position = responselist.data[0].name
        this.set_company = responselist.data[0].set_company
        this.set_permission = responselist.data[0].set_permission
        this.partner = responselist.data[0].partner
        this.order = responselist.data[0].order
        this.approve_order = responselist.data[0].approve_order
        this.payment = responselist.data[0].payment
        this.report = responselist.data[0].report
        this.tracking = responselist.data[0].tracking
        this.refund = responselist.data[0].refund
        this.review = responselist.data[0].review
        if (this.set_company === '1' && this.set_permission === '1' && this.partner === '1' && this.order === '1' && this.approve_order === '1' && this.payment === '1' && this.report === '1' && this.tracking === '1' && this.refund === '1' && this.review === '1') {
          this.allPosition = true
        }
        this.$store.commit('closeLoader')
      } else {
        if (responselist.message === 'This user is Unauthorized' || responselist.message === 'This user is unauthorized.' || responselist.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          if (responselist.message === 'Please set permissions of this position.') {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>โปรดเลือกสิทธิ์การใช้งาน</h3>' })
          } else {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: responselist.message })
          }
        }
      }
    },
    async EditSave () {
      this.$store.commit('openLoader')
      if (this.position === '' || this.position === null) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกชื่อตำแหน่งที่ต้องการสร้าง</h3>' })
      } else if (this.position !== '' || this.position !== null) {
        var data = {
          company_position_id: this.position_id,
          company_id: this.companyData.id,
          name: this.position,
          position_data: {
            set_company: this.set_company === '1' ? '1' : '0',
            set_permission: this.set_permission === '1' ? '1' : '0',
            partner: this.partner === '1' ? '1' : '0',
            order: this.order === '1' ? '1' : '0',
            approve_order: this.approve_order === '1' ? '1' : '0',
            payment: this.payment === '1' ? '1' : '0',
            report: this.report === '1' ? '1' : '0',
            tracking: this.tracking === '1' ? '1' : '0',
            refund: this.refund === '1' ? '1' : '0',
            review: this.review === '1' ? '1' : '0'
          }
        }
        await this.$store.dispatch('avtionsEditPosition', data)
        var res = await this.$store.state.ModuleShop.stateEditPodition
        this.dialogCreate = false
        if (res.code === 200) {
          await this.$store.dispatch('actionsAuthorityUser')
          var response = await this.$store.state.ModuleUser.stateAuthorityUser
          this.list_company = response.data.list_company
          for (let i = 0; i < this.list_company.length; i++) {
            if (this.companyData.id === this.list_company[i].company_id) {
              localStorage.removeItem('list_Company_detail')
              localStorage.setItem('list_Company_detail', Encode.encode(this.list_company[i]))
            }
          }
          await this.$EventBus.$emit('chackAuthorityCompanyMenu')
          await this.$EventBus.$emit('checkPathCompany')
          await this.$EventBus.$emit('changeNavCompany')
          await this.$EventBus.$emit('AuthorityUser')
          this.getListData()
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>แก้ไขข้อมูลตำแหน่งสำเร็จ</h3>' })
          this.$store.commit('closeLoader')
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            // this.closeModal()
            if (res.message === 'Please set permissions of this position.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>โปรดเลือกสิทธิ์การใช้งาน</h3>' })
            } else if (res.message === 'The position name is already exist in this company.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ชื่อตำแหน่งนี้ได้ถูกใช้งานแล้ว</h3>' })
            } else if (res.message === 'The user was not found in the company.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ไม่พบผู้ใช้งานนี้ภายในบริษัท</h3>' })
            } else if (res.message === 'Data missing. Please check your parameter and try again.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ข้อมูลไม่ครบ กรุณาตรวจสอบใหม่อีกครั้ง</h3>' })
            } else {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: res.message })
            }
          }
        }
      } else {
        this.closeModal()
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
        this.$store.commit('closeLoader')
      }
    },
    async DetailOrder (item) {
      this.allPosition = false
      var data = {
        company_id: this.companyData.id,
        company_position_id: item.id
      }
      this.position_id = item.id
      await this.$store.dispatch('actionsDetailUserCompanyPosition', data)
      var detailorder = await this.$store.state.ModuleShop.stateDetailUserCompanyPosition
      this.position = item.name
      this.openStatus = detailorder.data[0].status
      this.createDate = new Date(detailorder.data[0].created_at).toLocaleDateString('th-TH',
        { year: 'numeric', month: 'long', day: 'numeric' })
      this.update = new Date(detailorder.data[0].updated_at).toLocaleDateString('th-TH', {
        year: 'numeric', month: 'long', day: 'numeric'
      })
      var a = [
        detailorder.data[0].set_company,
        detailorder.data[0].set_permission,
        detailorder.data[0].partner,
        detailorder.data[0].order,
        detailorder.data[0].approve_order,
        detailorder.data[0].payment,
        detailorder.data[0].report,
        detailorder.data[0].tracking,
        detailorder.data[0].refund,
        detailorder.data[0].review
      ]
      this.detailPosition = []
      for (let i = 0; i < this.position_user.length; i++) {
        if (a[i] === '1') {
          this.position_user[i].status = a[i]
          this.detailPosition.push(this.position_user[i])
        }
      }
      // console.log('test', detailorder)
      this.dialog_user = true
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
  margin-right: 0px !important;
}
.v-application--is-ltr /deep/ .v-data-footer__select {
  margin-left: auto;
  margin-right: 0px !important;
}
</style>
