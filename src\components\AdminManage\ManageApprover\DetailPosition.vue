<template>
  <v-container :class="MobileSize ? 'mt-3' : IpadSize ? 'px-0' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-text>
        <!-- 1st row header search -->
        <v-row no-gutters>
          <v-col cols="12" md="12" sm="12" :class="MobileSize || IpadSize || IpadProSize ? 'pl-0 pr-3 pt-3' : 'pl-2 pr-2 pt-3'">
            <v-row>
              <v-btn icon @click="goBack()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon></v-btn>
              <h2 :class="MobileSize || IpadSize || IpadProSize ? 'fontSizeTitleMobile': 'fontSizeTitle'">รายชื่อผู้อนุมัติ</h2>
            </v-row>
          </v-col>
        </v-row>

        <!-- 2nd row short data-->
        <v-row no-gutters v-if="showTable === true" class="ml-2 mb-2 mt-2">
          <!-- short data -->
          <v-col cols="12" md="12" sm="12" class="pt-2">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">ชื่อรูปแบบ : {{positionDetail.approve_position_name}}</span>
          </v-col>
          <v-col cols="12" md="12" sm="12" class="pt-2">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">รูปแบบ : {{positionTypeTH}}</span>
          </v-col>
        </v-row>

        <!-- 3rd card list -->
        <v-row no-gutters v-if="showTable === true">
          <v-col cols="12" md="12" sm="12" class="pt-2" v-for="(item, no) in numberOfTable" :key="no">
            <v-card elevation="0" :outlined="!MobileSize ? true : false" :class="!MobileSize && !IpadSize ? 'pa-4' : ''">
              <v-card-title :class="MobileSize ? 'pa-0 ma-0 mb-4 mt-4' : ''">
                <v-row>
                  <v-col cols="5" md="6" sm="4" :class="MobileSize ? 'py-0' : ''">
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeCardTitleMobile': 'fontSizeCardTitle'">ผู้อนุมัติลำดับที่ : {{no+1}}</span>
                  </v-col>
                  <v-spacer></v-spacer>
                  <v-col cols="7" md="6" sm="8" :class="MobileSize ? 'pa-0' : ''">
                    <!-- show default btn -->
                    <v-row justify="end" no-gutters v-if="createOrEditStatus !== 'edit' || no+1 !== SelectApproverNo">
                      <!-- btn add -->
                      <v-btn icon @click="getListSelectApprover(no+1, 'add')" :disabled="checkButtonAdd(approverList, no) ? true : false">
                        <v-avatar color="#F3F5F7" :size="!MobileSize ? '30' : '20'" class="pa-2">
                            <v-icon :small="MobileSize ? true : false" color="#27AB9C" class="">mdi-plus</v-icon>
                        </v-avatar>
                      </v-btn>
                      <v-btn text :class="MobileSize ? 'pa-0' : ''" @click="getListSelectApprover(no+1, 'add')" :disabled="checkButtonAdd(approverList, no) ? true : false">
                        <span class="mr-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">เพิ่ม</span>
                      </v-btn>
                      <!-- btn edit -->
                      <v-btn icon @click="SetApproveNo(no, 'edit')" :disabled="approverList[no].approver.length === 0 ? true : false">
                        <v-avatar color="#F3F5F7" :size="!MobileSize ? '30' : '20'" class="pa-2">
                            <v-icon :small="MobileSize ? true : false" color="#27AB9C" class="">mdi-pencil-outline</v-icon>
                        </v-avatar>
                      </v-btn>
                      <v-btn text :class="MobileSize ? 'pa-0' : ''" @click="SetApproveNo(no, 'edit')" class="mr-0" :disabled="approverList[no].approver.length === 0 ? true : false">
                        <span class="" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">แก้ไข</span>
                      </v-btn>
                    </v-row>
                    <v-row v-if="createOrEditStatus === 'edit' && no+1 === SelectApproverNo" :justify="IpadSize || IpadProSize || MobileSize ? 'center' : 'end'" no-gutters>
                      <!-- btn cancle -->
                      <v-btn icon @click="createOrEditStatus = ''">
                        <v-avatar color="#F3F5F7" :size="!MobileSize ? '30' : '20'" class="pa-2">
                            <v-icon :small="MobileSize ? true : false" color="#27AB9C" class="">mdi-close-circle-outline</v-icon>
                        </v-avatar>
                      </v-btn>
                      <v-btn text @click="createOrEditStatus = ''" class="mr-2">
                        <span class="mr-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">ยกเลิก</span>
                      </v-btn>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-title>
              <v-card-text :class="MobileSize ? 'pa-0 ma-0 mb-4' : ''">
                <v-card elevation="0" :outlined="MobileSize ? true : false" :class="MobileSize ? 'pa-4' : ''" v-if="approverList[no].approver.length !== 0">
                  <v-row dense>
                    <!-- {{ approverList[no].budget }} -->
                    <v-col cols="12" md="12" sm="12" v-if="approverList[no].budget !== 0">
                      <span style="font-weight: 500; font-size: 14px; line-height: 24px; color: #333333;">วงเงินอนุมัติ: <b>{{ approverList[no].budget }}</b> บาท</span>
                    </v-col>
                    <v-col cols="12" md="4" sm="6" v-for="(item, index) in approverList[no].approver" :key="index">
                      <!-- card approver detail by individual -->
                      <v-card height="100%" width="100%" min-height="150px" class="justify-center" color="#F8FAFB">
                        <v-card-text class="pl-1 pr-0">
                          <!-- default detail -->
                          <v-row no-gutters>
                            <v-col cols="3" md="3" sm="4" style="align-items: center; justify-content: center; display: flex;">
                              <v-avatar color="#F3F5F7" size="60" v-if="item.img_path !== null"><v-img :src="item.img_path"></v-img></v-avatar>
                              <v-avatar color="#F3F5F7" size="58" v-else><v-img src="@/assets/icons/businessman.jpg"></v-img></v-avatar>
                            </v-col>
                            <v-col cols="9" md="9" sm="8">
                              <p style="font-weight: 500; color: #333333;" :style="IpadSize ? 'font-size: 12px; line-height: 22px;' : 'font-size: 14px; line-height: 24px;'">ชื่อ: <b>{{item.name}}</b></p>
                              <p style="font-weight: 500; color: #333333;" :style="IpadSize ? 'font-size: 12px; line-height: 22px;' : 'font-size: 14px; line-height: 24px;'">อีเมล: <b style="white-space: normal; line-height: 24px;">{{item.email}}</b></p>
                            </v-col>
                          </v-row>
                        </v-card-text>
                        <!-- btn when click edit -->
                        <v-card-actions>
                          <v-row v-if="createOrEditStatus !== 'edit'" no-gutters justify="center" class="pb-2">
                            <v-col cols="12" md="5" sm="12">
                              <v-row justify="center">
                                <v-btn text @click="showApproverDetail(no+1, item, 'detail')" class="pa-0">
                                  <span style="color:  #27AB9C; text-decoration: underline;" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">รายละเอียด</span>
                                </v-btn>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-row v-if="createOrEditStatus === 'edit' && no+1 === SelectApproverNo" no-gutters class="pb-2">
                            <v-col cols="6" md="5" sm="6" v-if="positionType === 'many'" class="">
                              <v-row justify="center" class="pa-0">
                                <v-btn icon @click="showApproverDetail(no+1, item, 'edit')">
                                  <v-avatar color="#F3F5F7" size="30" class="pa-2">
                                      <v-icon color="#27AB9C" class="">mdi-pencil-outline</v-icon>
                                  </v-avatar>
                                </v-btn>
                                <v-btn text @click="showApproverDetail(no+1, item, 'edit')" class="pa-0">
                                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">แก้ไข</span>
                                </v-btn>
                              </v-row>
                            </v-col>
                            <v-col cols="6" md="5" sm="6">
                              <v-row justify="center">
                                <v-btn icon @click="openConfirmDialog (item, 'delete')" :disabled="checkButtonDelete(approverList, no) ? true : false">
                                <v-avatar color="#F3F5F7" size="30" class="pa-2">
                                    <v-icon color="#27AB9C" class="">mdi-delete</v-icon>
                                </v-avatar>
                                </v-btn>
                                <v-btn text @click="openConfirmDialog (item, 'delete')" :disabled="checkButtonDelete(approverList, no) ? true : false">
                                  <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">ลบ</span>
                                </v-btn>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-actions>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <!-- dialog List Select Approver -->
        <v-dialog v-model="modalListSelectApprover" width="800px" persistent>
          <v-card width="100%" height="100%" class="rounded-lg" style="overflow-x: hidden;">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px">
                <font color="#27AB9C" >รายละเอียด</font>
              </span>
              <v-btn icon dark @click="modalListSelectApprover = false">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text :class="!MobileSize ? 'pa-4' : 'pa-0 mt-4'">
              <!-- 1st row -->
              <v-row>
                <v-col cols="4" md="2" sm="12" align="center">
                  <v-img :src="require('@/assets/icons/Approver.png')" width="60px" height="60px"></v-img>
                </v-col>
                <v-col cols="8" md="4" sm="12" class="mt-4">
                  <span class="fontSizeCardTitle">รายชื่อผู้อนุมัติ ลำดับที่ {{SelectApproverNo}}</span>
                </v-col>
              </v-row>
              <v-form ref="form" :lazy-validation="lazy">
                <v-card-text>
                  <!-- card list approver -->
                  <v-row justify="center" align-content="center" :class="!MobileSize ? 'px-6' : ''" v-if="clickSelectApprover === false">
                    <v-col cols="12" :class="!MobileSize ? 'px-12' : ''">
                      <p>ค้นหาผู้ใช้งาน</p>
                      <v-text-field class="rounded-lg" v-model="searchApprover" placeholder="ค้นหาผู้ใช้งาน" outlined dense hide-details
                      @keydown.enter.prevent="submit">
                        <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" :class="!MobileSize ? 'px-12' : ''" v-if="filteredList.length !== 0" style="max-height: 250px;" :style="{'overflow-y': filteredList.length === 0 ? 'hidden' : 'scroll'}">
                      <v-card outlined :class="!MobileSize ? 'pa-4' : 'pa-2'">
                        <div v-for="(item, index) in filteredList" :key="index">
                          <v-card outlined class="mb-4" @click="selectApprover2Add(item)">
                            <v-card-text :class="MobileSize ? 'px-0' : ''">
                              <div class="d-flex flex-no-wrap">
                                <v-avatar :class="!MobileSize ? 'ma-4' : ''" :size="!MobileSize? '132' : '60'" tile>
                                  <v-img :src="item.img_path" contain style="border-radius: 8px;" v-if="item.img_path !== null"></v-img>
                                  <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                                </v-avatar>
                                <div :class="!MobileSize ? 'ma-4' : 'pl-2'">
                                  <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">ชื่อ-สกุล : <b>{{ item.name }}</b></p>
                                  <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">อีเมล : <b>{{ item.email }}</b></p>
                                  <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">เบอร์โทรศัพท์ : <b>{{ item.phone }}</b></p>
                                </div>
                              </div>
                            </v-card-text>
                          </v-card>
                        </div>
                      </v-card>
                    </v-col>
                    <v-col cols="12" class="px-12" v-if="filteredList.length === 0 && searchApprover !== ''" align="center">
                      <div style="padding-top: 30px;">
                        <v-img src="@/assets/icons/notfoundAdminUser.png" max-height="146px" max-width="135px" height="100%" width="100%" contain></v-img>
                      </div>
                      <h2 style="padding-top: 20px; padding-bottom: 50px; color: #333333;">
                        <span :class="MobileSize ? 'fontSizeCardTitleMobile' : 'fontSizeCardTitle'">ไม่พบผู้ใช้งานนี้ในรายชื่อผู้อนุมัติ หรือ ผู้ใช้งานนี้ถูกใช้งานแล้ว</span><br/>
                      </h2>
                    </v-col>
                  </v-row>
                  <!-- card selected approver -->
                  <v-row justify="center" align-content="center" :class="!MobileSize ? 'px-6' : ''" v-else>
                    <v-col cols="12" :class="!MobileSize ? 'px-12' : ''">
                      <v-card outlined elevation="1">
                        <v-card-text :class="MobileSize ? 'px-0' : ''">
                          <div class="d-flex flex-no-wrap">
                            <v-avatar class="ma-4" :size="!MobileSize? '132' : '60'" tile>
                              <v-img :src="selectedApproverDetail.img_path" contain style="border-radius: 8px;" v-if="selectedApproverDetail.img_path !== null "></v-img>
                              <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                            </v-avatar>
                            <div :class="positionType !== 'many' ? 'ma-2 mt-8' : 'ma-2 mt-2'">
                              <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">ชื่อ-สกุล : <b>{{ selectedApproverDetail.name }}</b></p>
                              <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">อีเมล : <b>{{ selectedApproverDetail.email }}</b></p>
                              <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''">เบอร์โทรศัพท์ : <b>{{ selectedApproverDetail.phone }}</b></p>
                              <p :class="MobileSize ? 'fontSizeCardTitleMobile' : ''" v-if="createOrEditStatus === 'detail' && positionType === 'many'">วงเงินอนุมัติ : <b>{{ selectedApproverDetail.budget }}</b></p>
                              <v-row v-if="(createOrEditStatus === 'add' || createOrEditStatus === 'edit') && positionType === 'many'">
                                <v-col cols="12" md="5" :class="MobileSize ? 'fontSizeCardTitleMobile mb-0' : 'mt-2'"> วงเงินอนุมัติ <span style="color: red;">*</span> : </v-col>
                                <v-col cols="12" md="7">
                                  <v-text-field
                                    height="30px"
                                    v-model="approverbudget"
                                    outlined
                                    hide-details
                                    dense
                                    oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0/, '')"
                                    :rules="[v => !!v || 'ระบุวงเงิน']"
                                    @keydown.enter.prevent="submit"
                                    ></v-text-field>
                                </v-col>
                              </v-row>
                            </div>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                  <v-card-actions>
                    <v-row cols="12" class="px-12 mt-10 pb-2" v-if="filteredList.length !== 0 || createOrEditStatus === 'edit'">
                      <v-col cols="12" :class="!MobileSize ? 'px-12 pt-0' : ''" align="end">
                        <v-btn outlined class="mr-2" color="#27AB9C" @click="modalListSelectApprover = false">ยกเลิก</v-btn>
                        <v-btn class="white--text" color="#27AB9C" :disabled="clickSelectApprover === false ? true : false" @click="checkFormAdd('add')" v-if="createOrEditStatus === 'add'">บันทึก</v-btn>
                        <v-btn class="white--text" color="#27AB9C" :disabled="clickSelectApprover === false ? true : false" @click="checkFormAdd('edit')" v-if="createOrEditStatus === 'edit'">แก้ไข</v-btn>
                      </v-col>
                    </v-row>
                    <v-row cols="12" class="px-12 mt-10 pb-2" :justify="!MobileSize ? 'end' : 'center'" v-if="createOrEditStatus === 'detail'">
                      <v-btn class="white--text" color="#27AB9C" @click="modalListSelectApprover = false">ตกลง</v-btn>
                    </v-row>
                  </v-card-actions>
                </v-card-text>
              </v-form>
            </v-card-text>
          </v-card>
        </v-dialog>

        <!-- dialog confirm add edit delete data -->
        <v-dialog v-model="dialogConfirmSave" width="500px" persistent>
          <v-card width="100%" height="100%" class="rounded-lg">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px">
                <font color="#27AB9C" v-if="createOrEditStatus === 'add'">เพิ่มตำแหน่งการอนุมัติ</font>
                <font color="#27AB9C" v-if="createOrEditStatus === 'edit'">แก้ไขรูปแบบการอนุมัติ</font>
                <font color="#27AB9C" v-if="createOrEditStatus === 'delete'">ลบข้อมูลผู้อนุมัติลำดับที่ {{SelectApproverNo}}</font>
              </span>
              <v-btn icon dark @click="dialogConfirmSave = false">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text class="pl-10 pr-10 mt-10" align="center">
              <p :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'" v-if="createOrEditStatus === 'add'">คุณได้ทำการเพิ่มรายชื่อผู้อนุมัติลำดับที่ <b>{{SelectApproverNo}}</b></p>
              <p :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'" v-if="createOrEditStatus === 'edit'">คุณได้ทำการแก้ไขรายชื่อผู้อนุมัติลำดับที่ <b>{{SelectApproverNo}}</b></p>
              <p :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'" v-if="createOrEditStatus === 'delete'">คุณได้ทำการลบข้อมูลผู้อนุมัติลำดับที่ <b>{{SelectApproverNo}}</b></p>
              <p :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'">คุณต้องการทำรายการนี้ ใช่ หรือไม่</p>
            </v-card-text>
            <v-card-actions>
              <v-container>
                <v-row dense no-gutters justify="center">
                  <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogConfirmSave = false">
                    ยกเลิก
                  </v-btn>
                  <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" v-if="createOrEditStatus === 'add' || createOrEditStatus === 'edit'" @click="save()">บันทึก</v-btn>
                  <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" v-if="createOrEditStatus === 'delete'" @click="save()">ตกลง</v-btn>
                </v-row>
              </v-container>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <!-- dialog save success data -->
        <v-dialog v-model="dialogSuccess" persistent width="373">
          <v-card style="background: #FFFFFF; border-radius: 4px;" min-height="246px">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px">
                <font color="#27AB9C" v-if="createOrEditStatus === 'add'">เพิ่มรายชื่อผู้อนุมัติ</font>
                <font color="#27AB9C" v-if="createOrEditStatus === 'edit'">แก้ไขรายชื่อผู้อนุมัติ</font>
                <font color="#27AB9C" v-if="createOrEditStatus === 'delete'">ลบรายชื่อผู้อนุมัติ</font>
              </span>
              <v-btn icon dark @click="refresh()">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-container>
              <v-card-text>
                <v-row justify="center" no-gutters dense>
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
                  </v-col>
                  <v-col cols="12" align="center" class="mt-6">
                    <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">เสร็จสิ้น</p>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-container>
          </v-card>
        </v-dialog>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      // default data
      company_id: '',
      com_perm_id: '',
      positionID: '',
      positionDetail: '',
      positionType: '',
      positionTypeTH: '',
      // card data
      numberOfTable: '',
      approverList: [],

      // selected data
      createOrEditStatus: '',
      SelectApproverNo: '',

      // card modal dialog
      showTable: false,
      modalListSelectApprover: false,
      dialogConfirmSave: false,
      dialogSuccess: false,

      // modalListSelectApprover
      lazy: false,
      clickSelectApprover: false,
      searchApprover: '',
      ListSelectApprover: [],
      selectedApproverDetail: [],
      approverbudget: '',
      // test data
      detailSent2API: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredList () {
      return this.ListSelectApprover.filter(listApprover => {
        return listApprover.name.toLowerCase().includes(this.searchApprover.toLowerCase()) || listApprover.email.toLowerCase().includes(this.searchApprover.toLowerCase()) || listApprover.phone.toLowerCase().includes(this.searchApprover.toLowerCase())
      })
    }
  },
  watch: {
    MobileSize (val) {
      var positionID = localStorage.getItem('positionID')
      if (val === true) {
        this.$router.push({ path: '/detailPositionMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: `/detailPosition?positionID=${positionID}` }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    this.company_id = companyId.company.company_id
    this.com_perm_id = companyId.position.com_perm_id
    this.positionID = this.$route.query.positionID
    this.getApproverList()
  },
  methods: {
    checkButtonAdd (item, index) {
      if (item[index].approver.length < 5) {
        if (index === 0) {
          return false
        } else {
          if (item[index - 1].approver.length === 0) {
            return true
          } else {
            return false
          }
        }
      } else {
        return true
      }
    },
    checkButtonDelete (item, index) {
      var dataLenght = item.length
      if (index > 0) {
        if (item[index].approver.length === 1) {
          if (index < (dataLenght - 1)) {
            if (item[index + 1].approver.length === 0) {
              return false
            } else {
              return true
            }
          } else {
            return false
          }
        }
      } else {
        if (item[index].approver.length === 1) {
          return true
        } else {
          return false
        }
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (msg === 'Data missing. Please check your [" company_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company Permission not found.') {
        return 'ไม่พบสิทธิ์ผู้ใช้องค์กรนี้'
      } else if (msg === 'This is not you Company Permission.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ใช่ของคุณ'
      } else if (msg === 'This Permission not in your Company.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ได้อยู่ในองค์กรของคุณ'
      } else if (msg === 'Data missing. Please check your [" com_perm_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสสิทธิ์ผู้ใช้องค์กร ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Purchaser not found.') {
        return 'ไม่พบข้อมูลผู้ซื้อองค์กร'
      } else if (msg === 'Sum Budget More Than Position Budget.') {
        return 'วงเงินอนุมัติเกินวงเงินที่กำหนด'
      } else if (msg === 'This user already in approver_list_1') {
        return 'ผู้ใช้คนนี้มีรายชื่ออยู่ในรายการอนุมัติลำดับที่ 1 แล้ว'
      } else if (msg === 'This user already in approver_list_2') {
        return 'ผู้ใช้คนนี้มีรายชื่ออยู่ในรายการอนุมัติลำดับที่ 2 แล้ว'
      } else if (msg === 'This user already in approver_list_3') {
        return 'ผู้ใช้คนนี้มีรายชื่ออยู่ในรายการอนุมัติลำดับที่ 3 แล้ว'
      } else if (msg === 'Over Approver Limit.') {
        return 'จำนวนผู้อนุมัติห้ามเกิน 5 คน'
      } else {
        return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
      }
    },
    async getApproverList () {
      // reset selected data
      this.createOrEditStatus = ''
      var data = {
        company_id: this.company_id,
        com_perm_id: this.com_perm_id,
        approve_position_id: this.positionID
      }
      await this.$store.dispatch('actionsListApproverInApprovePosition', data)
      var response = await this.$store.state.ModuleApprove.stateApproverInApprovePosition
      if (response.result === 'SUCCESS') {
        this.positionDetail = response.data.approve_position_data
        this.positionType = this.positionDetail.approve_position_type
        if (this.positionType === 'one') {
          this.positionTypeTH = 'อนุมัติ 1 คน'
          this.numberOfTable = 1
        } else if (this.positionType === 'all') {
          this.positionTypeTH = 'อนุมัติทั้งหมด'
          this.numberOfTable = 3
        } else if (this.positionType === 'many') {
          this.positionTypeTH = 'ระบุวงเงิน'
          this.numberOfTable = 3
        }
        // change obj to Array
        var approverlist = response.data.approver_list
        this.approverList = []
        for (const element in approverlist) {
          this.approverList.push(approverlist[element])
        }
        this.showTable = true
      } else {
        var msg = this.getErrorMsg(response.message)
        this.$swal.fire({
          icon: 'error',
          text: msg,
          showConfirmButton: false,
          timer: 1500
        })
        this.showTable = false
      }
    },
    // btn add
    async getListSelectApprover (no, status) {
      // reset value
      this.SelectApproverNo = ''
      this.clickSelectApprover = false
      this.searchApprover = ''
      this.approverbudget = ''
      this.ListSelectApprover = []
      // assign value
      this.createOrEditStatus = status
      this.SelectApproverNo = no
      var data = {
        company_id: this.company_id,
        com_perm_id: this.com_perm_id,
        approve_position_id: this.positionID
      }
      await this.$store.dispatch('actionsListSelectApprover', data)
      var response = await this.$store.state.ModuleApprove.stateListSelectApprover
      if (response.result === 'SUCCESS') {
        this.ListSelectApprover = response.data.list_aprrover
        this.modalListSelectApprover = true
      } else {
        var msg = this.getErrorMsg(response.message)
        this.$swal.fire({
          icon: 'error',
          text: msg,
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    selectApprover2Add (item) {
      // assign value
      this.selectedApproverDetail = item
      this.clickSelectApprover = true
      this.Approverbudget = ''
    },
    checkFormAdd (status) {
      var total = 0
      if (this.positionType === 'many') {
        if (this.approverbudget === '' || this.approverbudget === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'กรุณาระบุวงเงินอนุมัติ',
            showConfirmButton: false,
            timer: 1500
          })
        } else {
          this.approverList.forEach((element, index) => {
            if (index !== (this.SelectApproverNo - 1)) {
              if (element.approver.length !== 0) {
                total = total + parseFloat(element.approver[0].budget)
              }
            }
          })
          total = total + parseFloat(this.approverbudget)
          // if (parseFloat(total) <= parseFloat(this.positionDetail.approve_position_budget)) {
          if (this.$refs.form.validate(true)) {
            this.modalListSelectApprover = false
            this.openConfirmDialog(this.selectedApproverDetail, status)
          } else {
            this.$nextTick(() => {
              const el = document.getElementsByClassName('error--text')
              if (el) {
                document
                  .getElementsByClassName('error--text')[0]
                  .scrollIntoView({ behavior: 'smooth', block: 'end' })
              }
            })
          }
          // } else {
          //   this.$swal.fire({
          //     icon: 'error',
          //     text: 'วงเงินอนุมัติเกินวงเงินที่กำหนด',
          //     showConfirmButton: false,
          //     timer: 1500
          //   })
          // }
        }
      } else {
        if (this.$refs.form.validate(true)) {
          this.modalListSelectApprover = false
          this.openConfirmDialog(this.selectedApproverDetail, status)
        } else {
          this.$nextTick(() => {
            const el = document.getElementsByClassName('error--text')
            if (el) {
              document
                .getElementsByClassName('error--text')[0]
                .scrollIntoView({ behavior: 'smooth', block: 'end' })
            }
          })
        }
      }
    },
    // btn รายละเอียด
    showApproverDetail (no, item, status) {
      // reset value
      this.ListSelectApprover = []
      this.SelectApproverNo = ''
      this.selectedApproverDetail = []
      this.createOrEditStatus = ''
      this.approverbudget = ''
      this.clickSelectApprover = false
      this.modalListSelectApprover = false
      // assign value
      this.SelectApproverNo = no
      this.selectedApproverDetail = item
      this.createOrEditStatus = status
      this.clickSelectApprover = true
      this.modalListSelectApprover = true
      if (status === 'edit') {
        this.approverbudget = item.budget
      }
    },
    // btn edit
    SetApproveNo (no, status) {
      // reset value
      this.SelectApproverNo = ''
      this.createOrEditStatus = ''
      // assing value
      this.SelectApproverNo = no + 1
      this.createOrEditStatus = status
    },
    openConfirmDialog (item, status) {
      // reset value
      this.detailSent2API = []
      this.createOrEditStatus = ''
      // assign value
      this.detailSent2API = item
      this.createOrEditStatus = status
      this.dialogConfirmSave = true
    },
    async save () {
      var data = {}
      var response = ''
      if (this.createOrEditStatus === 'add') {
        data = {
          company_id: this.company_id,
          com_perm_id: this.com_perm_id,
          approve_position_id: this.positionID,
          no_of_approve: this.SelectApproverNo,
          approver: [{
            user_id: this.detailSent2API.user_id,
            budget: this.approverbudget
          }]
        }
        await this.$store.dispatch('actionsAddApproverInApprovePosition', data)
        response = await this.$store.state.ModuleApprove.stateAddApproverInApprovePosition
      } else if (this.createOrEditStatus === 'edit') {
        data = {
          company_id: this.company_id,
          com_perm_id: this.com_perm_id,
          approve_position_id: this.positionID,
          select_user_id: this.detailSent2API.user_id,
          name: this.detailSent2API.name,
          phone: this.detailSent2API.phone,
          budget: this.approverbudget
        }
        await this.$store.dispatch('actionsEditUserApproverInApprovePosition', data)
        response = await this.$store.state.ModuleApprove.stateEditUserApproverInApprovePosition
      } else if (this.createOrEditStatus === 'delete') {
        data = {
          company_id: this.company_id,
          com_perm_id: this.com_perm_id,
          approve_position_id: this.positionID,
          approver: [
            { user_id: this.detailSent2API.user_id }
          ]
        }
        await this.$store.dispatch('actionsDeleteApproverInApprovePosition', data)
        response = await this.$store.state.ModuleApprove.stateDeleteApproverInApprovePosition
      }
      this.dialogConfirmSave = false
      if (response.result === 'SUCCESS') {
        this.dialogSuccess = true
      } else {
        var msg = this.getErrorMsg(response.message)
        this.$swal.fire({
          icon: 'error',
          text: msg,
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    refresh () {
      this.dialogSuccess = false
      this.getApproverList()
    },
    goBack () {
      if (this.MobileSize) {
        this.$router.push({ path: '/listApprovePositionMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listApprovePosition' }).catch(() => {})
      }
    }
  }
}
</script>

<style>
.fontSizeTitle {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;
}
.fontSizeCardTitle {
  font-weight: 600 !important; font-size: 16px !important; line-height: 24px !important; color: #333333 !important;
}
.fontSizeCardTitleMobile {
  font-weight: 600 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.fontSizeTitle2 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile2 {
  font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
</style>
