<template>
  <div class="text-center">
    <v-dialog v-model="ModalCoupon" width="684" persistent :style="MobileSize ? 'z-index: 16000103;' : ''">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 8px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                  <div :class="MobileSize ? 'py-6 px-3' : 'pt-7 px-2'">
                    <v-card-text v-if="!MobileSize" class="pa-0">
                      <v-form ref="formCheckThai" :lazy-validation="lazy">
                        <v-col class="py-0">
                          <v-row dense class="align-center">
                            <v-col class="py-0">
                              <v-row class="">
                                <v-col cols="12" class="py-0">
                                <v-row dense class="pt-2 pb-6 align-center">
                                  <v-img src="@/assets/couponShop.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 18px; font-weight: 600;">
                                    {{ $t('ListCoupon.HeaderSystemCodeShipping') }}
                                  </span>
                                </v-row>
                                  <v-text-field v-model="search" height="40" hide-details :placeholder="$t('ListCoupon.PlaceHolder')" :rules="[rules.validInput]" oninput="this.value = this.value.replace(/[^A-Za-z0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="isLetterEng($event)" @keydown.enter="submit" outlined dense>
                                    <template slot="append">
                                      <v-img src="@/assets/Magnifer.png" max-width="24" max-height="24">
                                      </v-img>
                                    </template>
                                  </v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-row no-gutters v-if="CodePlatformShipping.length !== 0" class="rounded-lg mt-6" style="max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="CodePlatformShipping.length !== 0 " class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <div v-for="(item, index) in CodePlatformShipping" :key="index">
                              <v-card-text class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%" height="120">
                                  <v-row dense class="px-4 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-col class="pl-4" style="min-width: 330px; max-width: 330px;">
                                      <span class="d-inline-block text-truncate" style="color: #269AFD; font-size: 18px; font-weight: 700; min-width: 300px; max-width: 320px;">{{item.coupon_name}}</span>
                                      <!-- <span class="d-inline-block text-truncate" style="color: #333333; font-size: 16px; font-weight: 400; min-width: 300px; max-width: 320px;">สำหรับร้านค้า {{item.shop_name}} เท่านั้น</span> -->
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.id"></v-radio>
                                        </v-radio-group>
                                        <span class="" style="color: #333333; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.SelectCoupon') }}</span>
                                    </v-col>
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                          </v-card>
                        </v-row>
                        <v-row no-gutters v-else-if="(Cards.length || (Shipping.length !== 0 && PickUp === false) || Free.length !== 0)"  class="rounded-lg mt-6" style="max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="Shipping.length !== 0 " class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 18px; font-weight: 600;">{{ $t('ListCoupon.ShippingDiscount') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Shipping" :key="index">
                              <v-card-text v-if="index < showShippingDiscount" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%" height="120">
                                  <v-row dense class="px-4 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-col class="pl-4" style="min-width: 330px; max-width: 330px;">
                                      <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 18px; font-weight: 700; min-width: 300px; max-width: 320px;">{{item.coupon_name}}</span>
                                      <!-- <span class="d-inline-block text-truncate" style="color: #333333; font-size: 16px; font-weight: 400; min-width: 300px; max-width: 320px;">สำหรับร้านค้า {{item.shop_name}} เท่านั้น</span> -->
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.id"></v-radio>
                                        </v-radio-group>
                                        <span class="" style="color: #333333; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.SelectCoupon') }}</span>
                                    </v-col>
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllShipping && Shipping.length > 3">
                              <v-btn text @click="ShowShippingDiscount()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                        </v-row>
                          <v-card v-else class="rounded-lg mt-6 py-6 px-4 "  elevation="0" style=" background-color: #FFFFFF;" >
                          <v-card-text>
                            <v-col cols="12" align="center">
                              <div class="mb-5">
                                <v-img src="@/assets/nocoupon.png" width="120" height="120" contain></v-img>
                              </div>
                              <div>
                                <span style="font-size: 16px; font-weight: 400; color: #9A9A9A;">{{ $t('ListCoupon.NoCouponSystem') }}</span>
                              </div>
                            </v-col>
                          </v-card-text>
                        </v-card>
                        </v-col>
                      </v-form>
                    </v-card-text>
                    <v-card-text v-else class="pa-0">
                      <v-form ref="formCheckThai" :lazy-validation="lazy">
                        <v-col class="py-0 px-0">
                          <v-row dense class="align-center">
                            <v-col class="py-0">
                              <v-row class="">
                                <v-col cols="12" class="py-0">
                                <v-row dense class="pt-2 pb-6 align-center">
                                  <v-img src="@/assets/couponShop.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 18px; font-weight: 600;">
                                    {{ $t('ListCoupon.HeaderSystemCodeShipping') }}
                                  </span>
                                </v-row>
                                  <v-text-field v-model="search" height="40" hide-details :placeholder="$t('ListCoupon.PlaceHolder')" :rules="[rules.validInput]" oninput="this.value = this.value.replace(/[^A-Za-z0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="isLetterEng($event)" @keydown.enter="submit" outlined dense>
                                    <template slot="append">
                                      <v-img src="@/assets/Magnifer.png" max-width="24" max-height="24">
                                      </v-img>
                                    </template>
                                  </v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-row no-gutters v-if="CodePlatformShipping.length !== 0" class="rounded-lg mt-6" style="max-height: 285px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="CodePlatformShipping.length !== 0 " class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <div v-for="(item, index) in CodePlatformShipping" :key="index">
                              <v-card-text class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%">
                                  <v-row dense class="px-2 py-2 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else-if="item.coupon_image === null && item.coupon_type === 'discount'" style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-col class="pl-1" style="line-height: 1;">
                                      <v-row dense class="pa-0 align-baseline">
                                        <v-col class="pa-0">
                                          <span class="d-inline-block text-truncate" style="color: #269AFD; font-size: 14px; font-weight: 700; min-width: 140px; max-width: 160px;">{{item.coupon_name}}</span>
                                        </v-col>
                                        <v-col align="end" class="pa-0" style="">
                                          <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                            <v-radio class="custom-radio-checkout" style="display: flex !important; justify-content: flex-end !important; padding-right: 0px !important;" color="#3EC6B6" :value="item.id"></v-radio>
                                          </v-radio-group>
                                        </v-col>
                                      </v-row>
                                      <!-- <span class="d-inline-block text-truncate" style="color: #333333; font-size: 12px; font-weight: 400; min-width: 140px; max-width: 160px;">สำหรับร้านค้า {{item.shop_name}} เท่านั้น</span> -->
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <!-- <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.id"></v-radio>
                                        </v-radio-group>
                                    </v-col> -->
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                          </v-card>
                        </v-row>
                        <v-row no-gutters v-else-if="(Cards.length || (Shipping.length !== 0 && PickUp === false) || Free.length !== 0)"  class="rounded-lg mt-6" style="max-height: 285px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="Shipping.length !== 0" class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 14px; font-weight: 600;">ส่วนลดทั่วไป</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Shipping" :key="index">
                              <v-card-text v-if="index < showShippingDiscount" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%">
                                  <v-row dense class="px-2 py-2 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-col class="pl-1" style="line-height: 1;">
                                      <v-row dense class="pa-0 align-baseline">
                                        <v-col class="pa-0">
                                          <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 14px; font-weight: 700; min-width: 140px; max-width: 160px;">{{item.coupon_name}}</span>
                                        </v-col>
                                        <v-col align="end" class="pa-0" style="">
                                          <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                            <v-radio class="custom-radio-checkout" style="display: flex !important; justify-content: flex-end !important; padding-right: 0px !important;" color="#3EC6B6" :value="item.id"></v-radio>
                                          </v-radio-group>
                                        </v-col>
                                      </v-row>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 12px; font-weight: 400; min-width: 140px; max-width: 160px;">สำหรับร้านค้า {{item.shop_name}} เท่านั้น</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <!-- <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.id"></v-radio>
                                        </v-radio-group>
                                    </v-col> -->
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllShipping && Shipping.length > 3">
                              <v-btn text @click="ShowShippingDiscount()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                        </v-row>
                        <v-card v-else class="rounded-lg mt-6 py-6 px-4 "  elevation="0" style=" background-color: #FFFFFF;" >
                          <v-card-text>
                            <v-col cols="12" align="center">
                              <div class="mb-5">
                                <v-img src="@/assets/nocoupon.png" width="120" height="120" contain></v-img>
                              </div>
                              <div>
                                <span style="font-size: 16px; font-weight: 400; color: #9A9A9A;">{{ $t('ListCoupon.NoCouponSystem') }}</span>
                              </div>
                            </v-col>
                          </v-card-text>
                        </v-card>
                        </v-col>
                      </v-form>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="px-12 justify-center" style="height: 100px; background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="Close()" >{{ $t('register.Cancel') }}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="!selectCoupon" class="px-10 white--text " style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="confirm()" >{{ $t('register.Confirm') }}</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="px-4 justify-center" style="background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 130px;" color="#27AB9C" @click="Close()" >{{ $t('register.Cancel') }}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="!selectCoupon" class="px-10 white--text" style="border-radius: 40px; width: 130px;" color="#27AB9C" @click="confirm()" >{{ $t('register.Confirm') }}</v-btn>
        </v-card-actions>
        <!-- <v-card-actions v-if="!MobileSize" class="justify-space-between" style="height: 88px; background-color: #F5FCFB;">
        <v-btn :disabled="selectCoupon ===''" outlined class="px-10" style="border-radius: 40px;" color="#27AB9C" @click="clearUser()" >ล้างค่า</v-btn>
        <v-btn :disabled="selectCoupon ===''" class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="confirm()" >ตกลง</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="justify-space-between px-2" style="height: 88px; background-color: #F5FCFB;">
        <v-btn :disabled="selectCoupon ===''" outlined class="px-10" style="border-radius: 40px;" color="#27AB9C" @click="clearUser()" >ล้างค่า</v-btn>
        <v-btn :disabled="selectCoupon ===''" class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="confirm()" >ตกลง</v-btn>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirm" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CancelSubmit()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ $t('ListCoupon.errorMessage3') }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('ListCoupon.errorMessage4') }}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CancelSubmit()">{{ $t('register.Cancel') }}</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirm()">{{ $t('register.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalDetailCoupon" width="760" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 760px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
                <v-col style="text-align: center;" :class="MobileSize ? 'pt-6 ml-8' : 'pt-6 ml-12'">
                  <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เงื่อนไขโค้ดส่วนลด</b></span>
                </v-col>
                <v-btn  fab small @click="backstep()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '760px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'py-6 px-2' : 'py-10 px-10'">
                    <v-col class="pb-0">
                      <v-btn small text @click="backstep()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon><span style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">ย้อนกลับ</span></v-btn>
                    </v-col>
                    <v-card-text v-if="!MobileSize" class="pt-0">
                      <div>
                        <v-card-text class="justify-center">
                          <v-img src="@/assets/ConponNGC/shopConpon/Coupons.png" style="filter: drop-shadow(rgb(51, 51, 51) 2px 2px 2px);" width="100%" height="169">
                            <v-row>
                              <v-col class="pr-0">
                                <v-col class="ml-12 pb-0 pr-0" style="min-width: 200px; line-height: 32px;">
                                  <span class="d-inline-block text-truncate" style="color: #27AB9C; font-size: 18px; font-weight: 700; max-width: 280px;">{{CouponName}}</span>
                                </v-col>
                                <v-col class="ml-12 pr-0 pt-0" style="min-width: 200px;">
                                  <v-row>
                                    <v-col v-if="CouponImage !== null" style="max-width: 100px;" class="pr-0">
                                      <v-img :src="CouponImage" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="100" height="50"></v-img>
                                    </v-col>
                                    <v-col v-else style="max-width: 100px;" class="pr-0">
                                      <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="100" height="50"></v-img>
                                    </v-col>
                                    <v-col class="pb-0">
                                      <span v-if="CouponType === 'discount'" style="font-size: 14px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: red;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                      <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                    </span>
                                    <span v-else-if="CouponType === 'free_shipping'" style="font-size: 14px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: #FF9800;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                      <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                    </span>
                                      <span v-else style="font-size: 14px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br></span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <!-- <v-col v-if="UseEnddate !== null" class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <v-spacer style="margin-top: 24px;"></v-spacer>
                                </v-col> -->
                                <v-col v-if="UseEnddate !== null" class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">ไม่มีวันหมดอายุ</span>
                                </v-col>
                                <v-row class="ml-14 py-0 py-1" style="min-width: 300px;">
                                  <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 130px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)">
                                    <template #progress="{ value }">
                                      <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                    </template>
                                  </v-progress-linear>
                                  <span class="ml-2" style="font-size: 12px; color: #27AB9C;">ใช้แล้ว {{parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)}}%</span>
                                  <!-- <v-btn text x-small class="px-0 ml-8" @click="OpenDetail()"><span class="text-decoration-underline" style="font-size: 14px; color: blue;">ดูเงื่อนไข</span></v-btn> -->
                                </v-row>
                              </v-col>
                              <v-col class="pl-0 pt-6" style="min-width: 200px;">
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: red;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: #FF9800;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: #FFC107;" >คูปอง</span>
                                </v-col>
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: red;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: #FF9800;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: #FFC107;">แถมฟรี</span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-img>
                        </v-card-text>
                      </div>
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          ชื่อคูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ CouponName }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          ประเภทโปรโมชัน
                        </span>
                      </v-col>
                      <v-col v-if="CouponType === 'discount'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          ส่วนลดราคาสินค้า
                        </span>
                      </v-col>
                      <v-col v-else-if="CouponType === 'free_shipping'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          ส่วนลดค่าจัดส่งสินค้า
                        </span>
                      </v-col>
                      <v-col v-else class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          คูปองแถมฟรี
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          สิทธิ์การใช้คูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          สามารถใช้ได้ {{Number(Quota).toLocaleString(undefined)}} สิทธิ์
                        </span>
                      </v-col>
                      <v-divider v-if="CouponType !== 'free_product'" class="mt-1"></v-divider>
                      <v-col v-if="CouponType !== 'free_product'">
                        <span style="font-size: 18px; font-weight: 600;">
                          ส่วนลดสูงสุด
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'percent' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{DiscountMaximum !== null ? `ลดสูงสุด ${Number(DiscountMaximum).toLocaleString(undefined)} บาท` : 'ไม่จำกัด'}}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'baht' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ `ลดสูงสุด ${Number(DiscountAmount).toLocaleString(undefined)} บาท` }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="CouponDescription !== null"></v-divider>
                      <v-col v-if="CouponDescription !== null">
                        <span style="font-size: 18px; font-weight: 600;">
                          รายละเอียดโค้ดส่วนลด
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="CouponDescription !== null">
                        <span style="font-size: 14px;" v-html="CouponDescription">
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="AllowAllShops === 0"></v-divider>
                      <v-col v-if="AllowAllShops === 0">
                        <span style="font-size: 18px; font-weight: 600;">
                          รายชื่อร้านค้าที่เข้าร่วม
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="AllowAllShops === 0">
                        <span style="font-size: 16px;">
                          รายการร้านค้าทั้งหมด <b>{{shopList.length}}</b> ร้านค้า
                        </span>
                        <div v-for="(shop, index) in displayedShops" :key="index" style="font-size: 16px;">
                          • {{ shop }}
                        </div>
                        <v-btn text small color="primary" @click="toggleShowAll" v-if="shopList.length > limit">
                          {{ showAll ? 'แสดงน้อยลง' : 'ดูเพิ่มเติม' }}
                        </v-btn>
                      </v-col>
                    </v-card-text>
                    <v-card-text v-if="MobileSize" class="pt-0 px-0">
                      <div>
                        <v-card-text class="justify-center">
                          <v-img src="@/assets/ConponNGC/shopConpon/Coupons.png" style="filter: drop-shadow(rgb(51, 51, 51) 2px 2px 2px);" width="100%" height="150">
                            <v-row>
                              <v-col class="pr-0" style="max-width: 200px;">
                                <v-col class="ml-1 pb-0 pr-0" style="min-width: 150px;">
                                  <span class="d-inline-block text-truncate" style="color: #27AB9C; font-size: 14px; font-weight: 700; max-width: 280px;">{{CouponName}}</span>
                                </v-col>
                                <v-col class="ml-0 py-0 pr-0" style="min-width: 60px;">
                                  <v-row>
                                    <v-col v-if="CouponImage !== null" style="max-width: 60px;" class="pr-0">
                                      <v-img :src="CouponImage" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" width="50" height="50"></v-img>
                                    </v-col>
                                    <v-col v-else style="max-width: 60px;" class="pr-0">
                                      <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" width="50" height="50"></v-img>
                                    </v-col>
                                    <v-col class="pb-0" style="max-width: 150px;">
                                      <span v-if="CouponType === 'discount'" style="font-size: 9px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: red;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                      <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                    </span>
                                    <span v-else-if="CouponType === 'free_shipping'" style="font-size: 9px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: #FF9800;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                      <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                    </span>
                                      <span v-else style="font-size: 9px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br></span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <!-- <v-col v-if="UseEnddate !== null" class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <v-spacer style="margin-top: 24px;"></v-spacer>
                                </v-col> -->
                                <v-col v-if="UseEnddate !== null" class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">ไม่มีวันหมดอายุ</span>
                                </v-col>
                                <v-row class="ml-2 py-0 py-1" style="min-width: 15p0x;">
                                  <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 65px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)">
                                    <template #progress="{ value }">
                                      <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                    </template>
                                  </v-progress-linear>
                                  <span class="ml-2" style="font-size: 12px; color: #27AB9C;">ใช้แล้ว {{parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)}}%</span>
                                  <!-- <v-btn text x-small class="px-0 ml-8" @click="OpenDetail()"><span class="text-decoration-underline" style="font-size: 14px; color: blue;">ดูเงื่อนไข</span></v-btn> -->
                                </v-row>
                              </v-col>
                              <v-col class="pl-0 pt-6 ml-n3" style="min-width: 100px;">
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: red;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: #FF9800;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: #FFC107;" >คูปอง</span>
                                </v-col>
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 20px; font-weight: 700; color: red;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 20px; font-weight: 700; color: #FF9800;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 100px;">
                                  <span style="font-size: 20px; font-weight: 700; color: #FFC107;">แถมฟรี</span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-img>
                        </v-card-text>
                      </div>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          ชื่อคูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ CouponName }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          ประเภทโปรโมชัน
                        </span>
                      </v-col>
                      <v-col v-if="CouponType === 'discount'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          ส่วนลดราคาสินค้า
                        </span>
                      </v-col>
                      <v-col v-else-if="CouponType === 'free_shipping'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          ส่วนลดค่าจัดส่งสินค้า
                        </span>
                      </v-col>
                      <v-col v-else class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          คูปองแถมฟรี
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          สิทธิ์การใช้คูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          สามารถใช้ได้ {{Number(Quota).toLocaleString(undefined)}} สิทธิ์
                        </span>
                      </v-col>
                      <v-divider v-if="CouponType !== 'free_product'" class="mt-1"></v-divider>
                      <v-col v-if="CouponType !== 'free_product'">
                        <span style="font-size: 16px; font-weight: 600;">
                          ส่วนลดสูงสุด
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'percent' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{DiscountMaximum !== null ? `ลดสูงสุด ${Number(DiscountMaximum).toLocaleString(undefined)} บาท` : 'ไม่จำกัด'}}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'baht' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ `ลดสูงสุด ${Number(DiscountAmount).toLocaleString(undefined)} บาท` }}
                        </span>
                      </v-col>
                      <v-divider v-if="CouponDescription !== null" class="mt-1"></v-divider>
                      <v-col v-if="CouponDescription !== null">
                        <span style="font-size: 16px; font-weight: 600;">
                          รายละเอียดโค้ดส่วนลด
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="CouponDescription !== null">
                        <span style="font-size: 12px;" v-html="CouponDescription">
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="AllowAllShops === 0"></v-divider>
                      <v-col v-if="AllowAllShops === 0">
                        <span style="font-size: 16px; font-weight: 600;">
                          รายชื่อร้านค้าที่เข้าร่วม
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="AllowAllShops === 0">
                        <span style="font-size: 14px;">
                          รายการร้านค้าทั้งหมด <b>{{shopList.length}}</b> ร้านค้า
                        </span>
                        <div v-for="(shop, index) in displayedShops" :key="index" style="font-size: 14px;">
                          • {{ shop }}
                        </div>
                        <v-btn text small color="primary" @click="toggleShowAll" v-if="shopList.length > limit">
                          {{ showAll ? 'แสดงน้อยลง' : 'ดูเพิ่มเติม' }}
                        </v-btn>
                      </v-col>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      showAll: false,
      limit: 10,
      ShopName: '',
      AllowAllShops: '',
      PickUp: false,
      TotalPriceVat: 0,
      checkCodePlatform: false,
      lazy: false,
      rules: {
        validInput: value => /^[A-Za-z0-9,\s]*$/.test(value) || this.$t('ListCoupon.errorMessage5')
      },
      ModalConfirm: false,
      CodePlatformShipping: [],
      Add: 1,
      pricePoint: 0,
      MaxPoint: 0,
      checkradio: false,
      PointData: [],
      selectPoint: '',
      inputValue: '',
      show: true,
      UserPoint: 0,
      ShopPointDetail: [],
      ShopID: '',
      dataCouponList: [],
      search: '',
      checkSelect: false,
      CouponID: '',
      CollectID: '',
      CouponImage: '',
      CouponName: '',
      CouponCode: '',
      CouponDescription: '',
      CollectStartdate: '',
      CollectEnddate: '',
      UseStartdate: '',
      UseEnddate: '',
      CouponType: '',
      Quota: '',
      UseCount: '',
      UserCap: '',
      SpendMinimum: '',
      DiscountAmount: '',
      ProductList: '',
      DiscountType: '',
      SellerShopID: '',
      Status: '',
      Cards: [],
      Shipping: [],
      Free: [],
      ModalDetailCoupon: false,
      selectCoupon: [],
      showAllCards: false,
      showAllShipping: false,
      showAllFree: false,
      ModalCoupon: false,
      showProductPriceDiscount: '3',
      showShippingDiscount: '3',
      showFreeGiftCoupon: '3',
      page: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    shopList () {
      return this.ShopName.split(',').map(name => name.trim())
    },
    displayedShops () {
      return this.showAll ? this.shopList : this.shopList.slice(0, this.limit)
    }
  },
  mounted () {
    this.$EventBus.$on('getListPlatformCouponShipping', this.getListCoupon)
    this.$EventBus.$on('clearCouponPlatformShipping', this.clearUser)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getListPlatformCouponShipping')
      this.$EventBus.$off('clearCouponPlatformShipping')
    })
  },
  created () {
    // this.getListCoupon()
    // console.log('%c Hello Bug ', 'background: red; color: #000; padding: 4px; border-radius: 2px; margin-left: 1ch;', this.Test)
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  watch: {
    // selectPoint (item) {
    //   console.log('object', item)
    // }
  },
  methods: {
    toggleShowAll () {
      this.showAll = !this.showAll
    },
    backstep () {
      this.ModalDetailCoupon = false
    },
    OpenDetail (item) {
      this.ModalDetailCoupon = true
      this.ShopName = item.shop_name
      this.AllowAllShops = item.allow_all_shops
      this.CouponID = item.coupon_id
      this.CollectID = item.collect_id
      this.CouponImage = item.coupon_image
      this.CouponName = item.coupon_name
      this.CouponCode = item.coupon_code
      this.CouponDescription = item.coupon_description
      this.CollectStartdate = item.collect_startdate
      this.CollectEnddate = item.collect_enddate
      this.UseStartdate = item.use_startdate
      this.UseEnddate = item.use_enddate
      this.CouponType = item.coupon_type
      this.Quota = item.quota
      this.UseCount = item.use_count
      this.UserCap = item.user_cap
      this.SpendMinimum = item.spend_minimum
      this.DiscountAmount = item.discount_amount
      this.DiscountMaximum = item.discount_maximum
      this.ProductList = item.product_list
      this.DiscountType = item.discount_type
      this.SellerShopID = item.seller_shop_id
      this.Status = item.status
    },
    ShowProductPriceDiscount () {
      this.showAllCards = true
      this.showProductPriceDiscount = this.Cards.length
    },
    ShowShippingDiscount () {
      this.showAllShipping = true
      this.showShippingDiscount = this.Shipping.length
    },
    async getListCoupon () {
      this.Cards = []
      this.Shipping = []
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      if ('shop_id' in couponData) {
        delete couponData.shop_id
      }
      await this.$store.dispatch('actionsListPlatformCoupon', couponData)
      var res = await this.$store.state.ModuleCart.stateListPlatformCoupon
      if (res.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.dataCouponList = res.data.coupon
        res.data.coupon.forEach(element => {
          const existingCardIndex = this.Cards.findIndex(card => card.id === element.id)
          const existingShippingIndex = this.Shipping.findIndex(shipping => shipping.id === element.id)
          const existsInCodePlatform = this.CodePlatformShipping.some(platform => platform.id === element.id)
          if (existingCardIndex === -1 && existingShippingIndex === -1 && !existsInCodePlatform) {
            if (element.coupon_type === 'discount') {
              this.Cards = []
            } else if (element.coupon_type === 'free_shipping') {
              this.Shipping.push({ ...element })
            }
          }
        })
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('Login.LoginFail3')}</h3>`
          })
        }
      }
    },
    formatDateToShow (data) {
      if (!data) return null
      const date = data.includes('T') ? data.split('T')[0] : data.split(' ')[0]
      const [year, month, day] = date.split('-')
      if (this.$i18n.locale === 'th') {
        return `${day}/${month}/${parseInt(year) + 543}`
      } else {
        return `${day}/${month}/${year}`
      }
    },
    isLetterEng (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z0-9():&.,-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    CancelSubmit () {
      if (localStorage.getItem('CouponPlatformShipping') !== null) {
        this.CodePlatformShipping = JSON.parse(localStorage.getItem('CouponPlatformShipping'))
      } else {
        this.CodePlatformShipping = []
        // this.submitStart()
      }
      this.ModalConfirm = false
      this.search = ''
    },
    clearUser () {
      localStorage.removeItem('CouponPlatformShipping')
      this.CodePlatformShipping = []
      // this.submitStart()
      this.selectCoupon = ''
      this.checkSelect = false
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showAllCards = false
      this.showAllShipping = false
      this.$EventBus.$emit('ListCode')
    },
    async confirm () {
      if (this.selectCoupon.length === 0) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('ListCoupon.PleaseSelect')}</h3>`
        })
      } else {
        var Coupon = []
        if (this.CodePlatformShipping.length !== 0) {
          Coupon = [].concat(this.Cards, this.Shipping, this.CodePlatformShipping).filter(element => {
            return element.id === this.selectCoupon
          })
          if (this.TotalPriceVat < Coupon[0].spend_minimum) {
            this.$swal.fire({
              icon: 'warning',
              text: `${this.$t('ListCoupon.errorMessage6')}`,
              showConfirmButton: false,
              timer: 1500
            })
          } else if (this.PickUp && Coupon[0].coupon_type === 'free_shipping') {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'warning',
              html: `<h3>${this.$t('ListCoupon.errorMessage7')}</h3>`
            })
            this.selectCoupon = ''
            this.CodePlatformShipping = []
            if (localStorage.getItem('CouponPlatformShipping') !== null) {
              this.CodePlatformShipping = JSON.parse(localStorage.getItem('CouponPlatformShipping'))
            } else {
              this.CodePlatformShipping = []
            }
            if (this.CodePlatformShipping.length !== 0) {
              if (this.CodePlatformShipping[0].id !== '') {
                this.selectCoupon = this.CodePlatformShipping[0].id
              }
            } else {
              this.selectCoupon = ''
            }
            this.getListCoupon()
          } else {
            this.CodePlatformShipping = []
            localStorage.removeItem('CouponPlatformShipping')
            localStorage.setItem('CouponPlatformShipping', JSON.stringify(Coupon))
            this.$EventBus.$emit('ListCode')
            this.ModalCoupon = false
            this.ModalConfirm = false
            this.search = ''
          }
        } else {
          Coupon = [].concat(this.Cards, this.Shipping, this.CodePlatformShipping).filter(element => {
            return element.id === this.selectCoupon
          })
          this.CodePlatformShipping = []
          localStorage.removeItem('CouponPlatformShipping')
          localStorage.setItem('CouponPlatformShipping', JSON.stringify(Coupon))
          this.$EventBus.$emit('ListCode')
          this.ModalCoupon = false
          this.ModalConfirm = false
          this.search = ''
        }
      }
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showAllCards = false
      this.showAllShipping = false
    },
    deteleUser (index) {
      this.CodePlatformShipping.splice(index, 1)
      // localStorage.setItem('CouponPlatformShipping', JSON.stringify(this.CodePlatformShipping))
      // this.$EventBus.$emit('ListCode')
    },
    async getCouponsCode (item) {
      var data = {
        role_user: 'ext_buyer',
        company_id: '-1',
        shop_id: -1,
        customer_id: '-1',
        coupon_id: item
      }
      // console.log('data', data)
      await this.$store.dispatch('actionCollectCoupon', data)
      var resGetCoupons = this.$store.state.ModuleShop.stateCollectCoupon
      // console.log('resGetCoupons', resGetCoupons)
      if (resGetCoupons.result === 'Success' || (resGetCoupons.result === 'Error' && resGetCoupons.message === 'คูปองนี้ เคยถูกเก็บไปแล้ว')) {
        this.$swal.fire({
          icon: 'success',
          text: `${this.$t('ListCoupon.errorMessage8')}`,
          // text: 'เก็บคูปองสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        this.checkCodePlatform = true
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: `${this.$t('ListCoupon.errorMessage9')}`,
          // text: 'ไม่สามารถเก็บคูปองได้',
          showConfirmButton: false,
          timer: 1500
        })
        this.checkCodePlatform = false
      }
      // console.log('resgetCode', res)
    },
    async submit (event) {
      event.preventDefault()
      if (this.$refs.formCheckThai.validate(true)) {
        this.$store.commit('openLoader')
        var CheckIndex
        var SearchArray = []
        var SameCode = []
        SearchArray = this.search
        this.results = SearchArray
        var data = {
          code: this.results,
          type: 'free_shipping',
          role_user: 'ext_buyer'
        }
        if (this.CodePlatformShipping.length !== 0) {
          if (this.CodePlatformShipping[0].coupon_code === this.results) {
            SameCode = this.results
            CheckIndex = 0
          } else {
            CheckIndex = -1
          }
        } else {
          CheckIndex = -1
        }
        if (CheckIndex === -1) {
          await this.$store.dispatch('actionsSearchCouponPlatform', data)
          var res = await this.$store.state.ModuleCart.stateSearchCouponPlatform
          if (res.message === 'Success.') {
            this.$store.commit('closeLoader')
            await this.getCouponsCode(res.data[0].id)
            if (this.checkCodePlatform === true) {
              this.CodePlatformShipping = []
              res.data.forEach(element => {
                this.CodePlatformShipping.push(element)
              })
              this.getListCoupon()
              if (this.CodePlatformShipping.length !== 0) {
                if (this.CodePlatformShipping[0].id !== '') {
                  this.selectCoupon = this.CodePlatformShipping[0].id
                }
              } else {
                this.selectCoupon = ''
              }
            } else {
              this.CodePlatformShipping = []
              this.getListCoupon()
            }
            this.search = ''
          } else if (res.code === 400 && res.message === 'Code Not Found.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 4500,
              timerProgressBar: true,
              icon: 'warning',
              html: `<h3>${this.$t('ListCoupon.errorMessage10')}</h3>`
            })
            // this.ModalCoupon = false
          } else {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else if (res.message === 'ไม่พบคูปอง') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('ListCoupon.errorMessage1')
              })
            } else if (res.message === 'คูปองใช้เกินกำหนด') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('ListCoupon.errorMessage2')
              })
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            // this.ModalCoupon = false
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('ListCoupon.errorMessage11')} <b style="color: red;">${SameCode}</b> ${this.$t('ListCoupon.errorMessage12')}</h3>`
          })
        }
      } else {
        this.ModalCoupon = true
        this.$swal.fire({ icon: 'warning', text: this.$t('ListCoupon.errorMessage13'), showConfirmButton: false, timer: 2000 })
      }
    },
    async submit2 () {
      if (this.$refs.formCheckThai.validate(true)) {
        this.$store.commit('openLoader')
        var CheckIndex
        var SearchArray = []
        var SameCode = []
        SearchArray = this.search
        this.results = SearchArray
        var data = {
          code: this.results,
          type: 'free_shipping',
          role_user: 'ext_buyer'
        }
        if (this.CodePlatformShipping.length !== 0) {
          if (this.CodePlatformShipping[0].coupon_code === this.results) {
            SameCode = this.results
            CheckIndex = 0
          } else {
            CheckIndex = -1
          }
        } else {
          CheckIndex = -1
        }
        if (CheckIndex === -1) {
          await this.$store.dispatch('actionsSearchCouponPlatform', data)
          var res = await this.$store.state.ModuleCart.stateSearchCouponPlatform
          if (res.message === 'Success.') {
            this.$store.commit('closeLoader')
            await this.getCouponsCode(res.data[0].id)
            if (this.checkCodePlatform === true) {
              this.CodePlatformShipping = []
              res.data.forEach(element => {
                this.CodePlatformShipping.push(element)
              })
              this.getListCoupon()
              if (this.CodePlatformShipping.length !== 0) {
                if (this.CodePlatformShipping[0].id !== '') {
                  this.selectCoupon = this.CodePlatformShipping[0].id
                }
              } else {
                this.selectCoupon = ''
              }
            } else {
              this.CodePlatformShipping = []
              this.getListCoupon()
            }
            // this.CodePlatformShipping.push(res.data[0])
            // console.log(this.CodePlatformShipping)
            this.search = ''
          } else if (res.code === 400 && res.message === 'Code Not Found.') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 4500,
              timerProgressBar: true,
              icon: 'warning',
              html: `<h3>${this.$t('ListCoupon.errorMessage10')}</h3>`
            })
            // this.ModalCoupon = false
          } else {
            if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
              this.$store.commit('closeLoader')
              this.$EventBus.$emit('refreshToken')
            } else if (res.message === 'ไม่พบคูปอง') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('ListCoupon.errorMessage1')
              })
            } else if (res.message === 'คูปองใช้เกินกำหนด') {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: this.$t('ListCoupon.errorMessage2')
              })
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'error',
                text: res.message
              })
            }
            // this.ModalCoupon = false
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('ListCoupon.errorMessage11')} <b style="color: red;">${SameCode}</b> ${this.$t('ListCoupon.errorMessage12')}</h3>`
          })
        }
      } else {
        this.ModalCoupon = true
        this.$swal.fire({ icon: 'warning', text: this.$t('ListCoupon.errorMessage13'), showConfirmButton: false, timer: 2000 })
      }
    },
    Close () {
      if (this.selectCoupon !== '' && this.checkSelect === false) {
        this.selectCoupon = ''
      }
      if (this.CodePlatformShipping.length !== 1) {
        var CheckInetRelation = []
        if (localStorage.getItem('CouponPlatformShipping') !== null) {
          CheckInetRelation = JSON.parse(localStorage.getItem('CouponPlatformShipping'))
        } else {
          CheckInetRelation = []
        }
        if (this.CodePlatformShipping.length === CheckInetRelation.length) {
          const isMatching = this.CodePlatformShipping.every((item, index) => {
            return item.code === CheckInetRelation[index].code
          })
          if (isMatching) {
            this.ModalCoupon = false
            this.search = ''
          } else {
            this.ModalConfirm = true
          }
        } else {
          this.ModalConfirm = true
        }
      } else {
        this.ModalCoupon = false
        this.search = ''
      }
      this.checkSelect = true
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showAllCards = false
      this.showAllShipping = false
    },
    async open (page, item, point, baht, price, pickup) {
      if (localStorage.getItem('CouponPlatformShipping') !== null) {
        this.CodePlatformShipping = JSON.parse(localStorage.getItem('CouponPlatformShipping'))
      } else {
        this.CodePlatformShipping = []
      }
      this.TotalPriceVat = price
      // this.submitStart()
      if (this.CodePlatformShipping.length !== 0) {
        if (this.CodePlatformShipping[0].id !== '') {
          this.selectCoupon = this.CodePlatformShipping[0].id
        }
      } else {
        this.selectCoupon = ''
      }
      if (pickup === 'radio-1') {
        this.PickUp = true
      } else {
        this.PickUp = false
      }
      if (point !== null && point !== '') {
        this.inputValue = await point / parseFloat(baht)
        // console.log('this.inputValue', this.inputValue)
        // console.log('point', point, parseInt(baht))
      } else {
        point = 0
        this.inputValue = point
      }
      if (this.inputValue === 0) {
        // console.log(1)
        this.selectPoint = false
        this.checkradio = false
      } else {
        // console.log(2)
        this.selectPoint = true
        this.checkradio = true
      }
      this.page = page
      this.ModalCoupon = true
    }
  }
}
</script>

<style>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-input--selection-controls {
  margin-top: 0px;
}
</style>
<style scoped>
.dynamic-font-card {
  font-size: calc(1vw + 1vh + .5vmin); /* Adjust this value as needed */
  color: red;
}
.dynamic-font {
  display: block; /* Ensures the span takes up the full width */
}
::v-deep .v-radio {
  display: flex !important;
  justify-content: flex-end !important;
  padding-right: 12px !important;
}
::v-deep .custom-radio-checkout .v-input--selection-controls__input .v-icon {
  color: #3EC6B6 !important;
}
</style>
<style scoped>
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
</style>
