<template>
<v-container>
    <v-dialog v-model="modalManageCustomerSale" width="596" persistent>
      <v-card>
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>{{ ChangeLang(title) }}</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialog()" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-title class="pb-0">
            <v-row dense v-if="title === 'เพิ่มข้อมูลลูกค้า'">
              <v-col cols="12">
                <v-row no-gutters>
                  <v-col cols="4" align="center">
                    <v-icon color="#27AB9C" size="50">mdi-account-edit</v-icon>
                  </v-col>
                  <v-col cols="4" align="center">
                    <v-icon size="40">mdi-home-edit</v-icon>
                  </v-col>
                  <v-col cols="4" align="center">
                    <v-icon size="40">mdi-file-document-edit</v-icon>
                  </v-col>
                </v-row>
                <v-row no-gutters justify="center">
                  <v-col cols="4" align="center"><span style="color: #27AB9C; font-weight: 800;" :style="MobileSize && $i18n.locale === 'th'? 'font-size: 14px;': MobileSize && $i18n.locale === 'en'? 'font-size: 12px;' : 'font-size: 18px;'">1.{{ $t('ModalAddCustomerSaleOrder.CustomerData') }}</span></v-col>
                  <v-col cols="4" align="center"><span :style="MobileSize && $i18n.locale === 'th'? 'font-size: 14px; font-weight: 700;' : MobileSize && $i18n.locale === 'en'? 'font-size: 12px; font-weight: 700;' : 'font-size: 18px;'">2.{{ $t('ModalAddCustomerSaleOrder.CustomerAddress') }}</span></v-col>
                  <v-col cols="4" align="center"><span :style="MobileSize && $i18n.locale === 'th'? 'font-size: 14px; font-weight: 700;' : MobileSize && $i18n.locale === 'en'? 'font-size: 12px; font-weight: 700;': 'font-size: 18px;'">3.{{ MobileSize ? $t('ModalAddCustomerSaleOrder.CustomerInvoiceAddressMobile') : $t('ModalAddCustomerSaleOrder.CustomerInvoiceAddress') }}</span></v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row dense v-else>
              <v-col cols="12" md="12">
                <v-row no-gutters>
                  <v-col cols="12" align="center">
                    <v-icon color="#27AB9C" size="50">mdi-account-edit</v-icon>
                  </v-col>
                </v-row>
                <v-row no-gutters justify="center">
                  <v-col cols="4" md="4" align="center"><span style="color: #27AB9C; font-weight: 800;" :style="MobileSize? 'font-size: 14px;': 'font-size: 18px;'">{{ $t('ModalAddCustomerSaleOrder.HeaderEditCustomer') }}</span></v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-title>
            <v-card-text class="mt-3 px-0 pb-0">
              <v-form ref="form">
                <v-row justify="center" no-gutters v-if="useVendor === true">
                  <v-col cols="8">
                    <v-radio-group :disabled="this.pageForm === 'shoppage'" v-model="isVendor" row>
                        <v-radio
                          color="#27AB9C"
                          :label="$t('ModalAddCustomerSaleOrder.GeneralCustomer')"
                          value="no"
                        ></v-radio>
                        <v-radio
                          color="#27AB9C"
                          :label="$t('ModalAddCustomerSaleOrder.JVCustomer')"
                          value="yes"
                        ></v-radio>
                      </v-radio-group>
                  </v-col>
                </v-row>
                <v-row justify="center" no-gutters>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.CustomerName') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <!-- <v-text-field class="input_text" placeholder="ระบุชื่อลูกค้า" outlined dense v-model="inputData.cus_name" :rules="Rules.cus_name" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^A-Za-zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')" :counter="150" :maxlength="150"></v-text-field> -->
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderCustomerName')" outlined dense v-model="inputData.cus_name" :rules="Rules.cus_name" :counter="150" :maxlength="150"></v-text-field>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <span>FederalTaxID <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderTaxId')" outlined dense v-model="inputData.FederalTaxID" @input="validateTaxID()" :rules="Rules.FederalTaxID" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" :counter="13" :maxlength="13"></v-text-field>
                  </v-col>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.CustomerID') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderCustomerID')" outlined dense v-model="inputData.cus_code" :rules="Rules.cus_code" oninput="this.value = this.value.replace(/[^A-Za-z0-9-]/g, '').replace(/(\..*)\./g, '$1')" :counter="10" :maxlength="10"></v-text-field>
                  </v-col>
                  <!-- <v-col cols="8" v-if="SaleVendor === true">
                    <span>Customer Code <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <v-text-field class="input_text" placeholder="ระบุ Vendor Code" outlined dense v-model="inputData.cus_Vendor_Code" :rules="Rules.cus_code" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^A-Za-z0-9-\s]/g, '').replace(/(\..*)\./g, '$1')" :counter="10" :maxlength="10"></v-text-field>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <span>Customer Name <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <v-text-field class="input_text" placeholder="ระบุ Vendor Name" outlined dense v-model="inputData.cus_Vendor_Name" :rules="Rules.cus_name" oninput="this.value = this.value.replace(/[^A-Za-z-ก-๏]/g, '').replace(/(\..*)\./g, '$1')" :counter="150" :maxlength="150"></v-text-field>
                  </v-col> -->
                  <!-- <v-col cols="8">
                    <span>เลขประจำตัวผู้เสียภาษี <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field class="input_text" placeholder="ระบุรหัสลูกค้า" outlined dense v-model="taxID" :rules="Rules.taxID" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" :counter="13" :maxlength="13"></v-text-field>
                  </v-col> -->
                  <!-- <v-col cols="8">
                    <span>เบอร์ติดต่อ</span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field class="input_text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" placeholder="ระบุเบอร์ติดต่อลูกค้า" outlined dense v-model="inputData.cus_tel" :rules="Rules.tel" :counter="10" :maxlength="10"></v-text-field>
                  </v-col> -->
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.CustomerGroup.Headers') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-autocomplete
                      v-model="inputData.tier_name"
                      :items="ListGropCustomerSale"
                      outlined dense
                      @change="findTierlId(inputData.tier_name, '')"
                      item-text="tier_name"
                      :placeholder="$t('ModalAddCustomerSaleOrder.CustomerGroup.SelectGroup')"
                      :rules="Rules.empty"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.CustomerGroup.NotFoundGroup')"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <span>{{ $t('ModalAddCustomerSaleOrder.CustomerGroup.TierGroup') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8" v-if="SaleVendor === true">
                    <v-autocomplete
                      v-model="inputData.tier_vendor_Group"
                      :items="ListGropVendor"
                      outlined dense
                      @change="findListGroupId(inputData.tier_vendor_Group)"
                      @click="findListGroupId(inputData.tier_vendor_Group)"
                      item-text="name"
                      :placeholder="$t('ModalAddCustomerSaleOrder.CustomerGroup.SelectGroup')"
                      :rules="Rules.empty"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.CustomerGroup.NotFoundGroup')"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="8" v-if="inputData.tier_name !== ''">
                    <span>{{ $t('ModalAddCustomerSaleOrder.CustomerGroup.TierGroup') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8" v-if="inputData.tier_name !== ''">
                    <v-autocomplete
                      v-model="inputData.tier_level"
                      :items="listTierGrop"
                      outlined dense
                      @change="findTierlId(inputData.tier_name, inputData.tier_level)"
                      @click="findTierlId(inputData.tier_name, inputData.tier_level)"
                      item-text="tier_level"
                      :placeholder="$t('ModalAddCustomerSaleOrder.CustomerGroup.SelectGroup')"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.CustomerGroup.NotFoundGroup')"
                    >
                        <template #item="{ item }">
                            {{ item.tier_level }} {{ $t('ModalAddCustomerSaleOrder.CustomerGroup.Discount') }} {{item.discount_type === 'percent' ? item.discount_percent + ' %': item.discount_bath + ` ${$t('ModalAddCustomerSaleOrder.CustomerGroup.Baht')}`}}
                        </template>
                        <template v-slot:append>
                        <v-icon @click="findTierlId(inputData.tier_name, inputData.tier_level)">mdi-menu-down</v-icon>
                        </template>
                    </v-autocomplete>
                  </v-col>
                  <v-col cols="8">
                    <span>E-mail<span style="color: red;">*</span></span>
                    <!-- <v-text-field class="input_text" placeholder="ระบุ email" outlined dense v-model="email" :rules="Rules.email" oninput="this.value = this.value.replace(/[^0-9a-zA-Z@-.-.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  </v-col>
                  <v-col cols="8">
                    <!-- <span>email<span style="color: red;">*</span></span> -->
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderEmail')" outlined dense v-model="email" :rules="Rules.emailRules" oninput="this.value = this.value.replace(/[^0-9a-zA-Z@-.-.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.CreditTerm') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderCreditTerm')" outlined dense v-model="creditTerm" :maxlength="3" :rules="Rules.creditTerm" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.InstallmentPayment') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderInstallmentPayment')" outlined dense v-model="numCreditTerm" :rules="Rules.numCreditTerm" :maxlength="2" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.Remark') }}</span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field class="input_text" :placeholder="$t('ModalAddCustomerSaleOrder.PlaceHolderRemark')" outlined dense v-model="inputData.remark"></v-text-field>
                  </v-col>
                </v-row>
                <v-row justify="center" no-gutters v-if="pageForm === 'listCustomerSaleOrder'">
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.ListSaleOrder.Header') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8" v-if="AdminSaleNoJV === 'Y' && SaleCanAddCus === 'Y'">
                    <v-autocomplete
                      v-model="sale.saleName"
                      :items="itemSales"
                      outlined dense
                      @change="findSalesId(sale.saleName)"
                      @click="findSalesId(sale.saleName)"
                      item-text="sale_name"
                      :placeholder="$t('ModalAddCustomerSaleOrder.ListSaleOrder.SelectSaleOrder')"
                      :rules="Rules.empty"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.ListSaleOrder.NotFoundSaleOrder')"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="8" v-else-if="AdminSaleNoJV === 'N' && SaleCanAddCus === 'Y'">
                    <v-autocomplete
                      disabled
                      v-model="sale.saleName"
                      :items="itemSales"
                      outlined dense
                      item-text="sale_name"
                      :placeholder="$t('ModalAddCustomerSaleOrder.ListSaleOrder.SelectSaleOrder')"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.TypeOfCustomer.Headers') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-autocomplete
                      :disabled="SaleVendor === true"
                      v-model="typeCus"
                      :items="typeCustomer"
                      outlined dense
                      @change="selectTypecus(typeCus)"
                      :placeholder="$t('ModalAddCustomerSaleOrder.TypeOfCustomer.SelectTypeCustomer')"
                      :rules="Rules.empty"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.TypeOfCustomer.NotFoundTypeCustomer')"
                    ></v-autocomplete>
                  </v-col>
                </v-row>
                <v-row justify="center" no-gutters v-else-if="pageForm === 'shoppage'">
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.TypeOfCustomer.Headers') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-autocomplete
                      :disabled="this.isVendor === 'yes' && this.pageForm === 'shoppage'"
                      v-model="typeCus"
                      :items="typeCustomer"
                      outlined dense
                      @change="selectTypecus(typeCus)"
                      :placeholder="$t('ModalAddCustomerSaleOrder.TypeOfCustomer.SelectTypeCustomer')"
                      :rules="Rules.empty"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.TypeOfCustomer.NotFoundTypeCustomer')"
                    ></v-autocomplete>
                  </v-col>
                </v-row>
                <v-row justify="center" no-gutters v-if="pageForm === 'listCustomerSale'">
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.NameSaleOder') }}</span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field disabled outlined dense v-model="nameSale"></v-text-field>
                  </v-col>
                  <v-col cols="8">
                    <span>{{ $t('ModalAddCustomerSaleOrder.TypeOfCustomer.Headers') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="8">
                    <v-autocomplete
                      :disabled="SaleVendor === true"
                      v-model="typeCus"
                      :items="typeCustomer"
                      outlined dense
                      @change="selectTypecus(typeCus)"
                      :placeholder="$t('ModalAddCustomerSaleOrder.TypeOfCustomer.SelectTypeCustomer')"
                      :rules="Rules.empty"
                      :no-data-text="$t('ModalAddCustomerSaleOrder.TypeOfCustomer.NotFoundTypeCustomer')"
                    ></v-autocomplete>
                  </v-col>
                </v-row>
                <v-row justify="center" align-content="center" dense class="mx-2 mt-4 mb-4">
                  <v-col cols="12" md="8">
                    <v-row justify="end">
                      <v-btn outlined color="#27AB9C" class="mr-4 pl-8 pr-8" @click="closeDialog()">{{ $t('ModalAddCustomerSaleOrder.Button.Cancel') }}</v-btn>
                      <v-btn v-if="title === 'เพิ่มข้อมูลลูกค้า'" color="#27AB9C" :disabled="inputData.tier_level === ''" style="color: white;" class="pl-9 pr-9" @click="ManageCompanyAddress('add', '')">{{ $t('ModalAddCustomerSaleOrder.Button.Next') }}</v-btn>
                      <!-- <v-btn v-else color="#27AB9C" dark class="pl-9 pr-9" @click="ManageCompanyAddress('edit', items)">ถัดไป</v-btn> -->
                      <v-btn v-else color="#27AB9C" dark class="pl-9 pr-9" @click="ManageCompanyAddress('edit', customerSaleData)">{{ $t('ModalAddCustomerSaleOrder.Button.Save') }}</v-btn>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
            </v-card-text>
        </v-container>
      </v-card>
      <!-- dialog comfirm manages customer -->
      <v-dialog  v-model="dialogConfirmmanageCustomerSale" width="400" persistent>
        <v-card align="center" class="rounded-lg">
          <v-toolbar color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">{{ title }}</font></span>
            <v-btn icon dark @click="dialogConfirmmanageCustomerSale = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <br/><br/>
          <v-card-text >
            <span>{{ $t('ModalAddCustomerSaleOrder.dialogConfirmmanageCustomerSale.Text1') }}{{ title }}{{ $t('ModalAddCustomerSaleOrder.dialogConfirmmanageCustomerSale.Text2') }}</span>
          </v-card-text>
          <v-card-actions >
            <v-container >
              <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeConfirmManageCustomer()">{{ $t('ModalAddCustomerSaleOrder.Button.Cancel') }}</v-btn>
              <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="manageCustomser()">{{ $t('ModalAddCustomerSaleOrder.Button.Confirm') }}</v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogSuccess = !dialogSuccess"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขข้อมูลลูกค้าเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลลูกค้าเรียบร้อย</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogSuccess = !dialogSuccess">{{ $t('ModalAddCustomerSaleOrder.Button.Confirm') }}</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogSuccess = !dialogSuccess">{{ $t('ModalAddCustomerSaleOrder.Button.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" /> -->
    <EditModalAddress ref="EditModalAddress" />
    <!-- <EditModalCompanyAddress :EditAddressDetail="EditAddressDetail" :title="titleCompanyAddress" :page="page" /> -->
  </v-container>
</template>
<script>
import { Decode, Encode } from '@/services'
// import eventBus from '@/components/eventBus'
export default {
  components: {
    EditModalAddress: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditAddressCustomerSale')
    // EditModalCompanyAddress: () => import('@/components/Modal/AddressCompanyModal')
  },
  data () {
    return {
      useVendor: false,
      isVendor: 'no',
      SaleVendor: false,
      SaleCanAddCus: '',
      AdminSaleNoJV: '',
      SaleID: '',
      email: '',
      creditTerm: '',
      numCreditTerm: '',
      nameSale: '',
      saleData: [],
      SellerShopID: '',
      cusType: '',
      dialogSuccess: false,
      customerSaleData: [],
      sale: {
        saleID: '',
        saleName: ''
      },
      typeCus: '',
      typeCustomer: [
        this.$t('ModalAddCustomerSaleOrder.VSelectCustomer.general'),
        this.$t('ModalAddCustomerSaleOrder.VSelectCustomer.business')
        // { name: 'บริษัท' },
        // { name: 'บุคคล' }
      ],
      itemSales: [],
      action: '',
      EditAddressDetail: '',
      titleCompanyAddress: '',
      titleAddress: '',
      page: '',
      // tierNameID: '',
      // levelID: '',
      taxID: '',
      listTierGrop: '',
      ListGropCustomerSale: [],
      ListGropVendor: [],
      pageForm: '',
      modalManageCustomerSale: false,
      dialogConfirmmanageCustomerSale: false,
      addressList: [],
      pageMax: null,
      current: 1,
      pageSize: 5,
      customerData: '',
      countAddress: '',
      inputData: {
        FederalTaxID: '',
        discount_percent: '',
        tier_name: '',
        tier_vendor_Group: '',
        tier_level: '',
        cus_name: '',
        cus_code: '',
        cus_tel: '',
        channel_name: '',
        note: '',
        remark: '',
        emp_code: '',
        cus_Vendor_Code: '',
        cus_Vendor_Name: '',
        emp_name: ''
      },
      salesGroupCustomeristMenu: [],
      salesIdListMenu: [],
      salesNameListMenu: [],
      userDetailSelling: '',
      Rules: {
        FederalTaxID: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate1'),
          v => v.length >= 13 || this.$t('ModalAddCustomerSaleOrder.Validate.Validate2'),
          v => this.validNationalID(v) || this.$t('ModalAddCustomerSaleOrder.Validate.Validate3')
        ],
        cus_name: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate4'),
          v => v.length <= 150 || this.$t('ModalAddCustomerSaleOrder.Validate.Validate5')
        ],
        cus_code: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate6'),
          v => /^[A-Za-z0-9-]+$/.test(v) || this.$t('ModalAddCustomerSaleOrder.Validate.Validate7')
        ],
        taxID: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate8'),
          v => v.length > 12 || this.$t('ModalAddCustomerSaleOrder.Validate.Validate2'),
          v => /^[A-Za-z0-9]+$/.test(v) || this.$t('ModalAddCustomerSaleOrder.Validate.Validate9')
        ],
        empty: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate10')
        ],
        creditTerm: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate20'),
          v => v > 0 || this.$t('ModalAddCustomerSaleOrder.Validate.Validate11')
        ],
        numCreditTerm: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate20'),
          v => v > 0 || this.$t('ModalAddCustomerSaleOrder.Validate.Validate11'),
          v => v < 13 || this.$t('ModalAddCustomerSaleOrder.Validate.Validate11')
        ],
        tel: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate12'),
          v => v.charAt(0) === '0' || this.$t('ModalAddCustomerSaleOrder.Validate.Validate13'),
          v => v.length === 10 || v === '' || this.$t('ModalAddCustomerSaleOrder.Validate.Validate14')
        ],
        emailRules: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate15'),
          v => /.+@.+\..+/.test(v) || this.$t('ModalAddCustomerSaleOrder.Validate.Validate16'),
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || this.$t('ModalAddCustomerSaleOrder.Validate.Validate17'),
          v => /^\S*$/.test(v) || this.$t('ModalAddCustomerSaleOrder.Validate.Validate18')
        ],
        user: [
          v => !!v || this.$t('ModalAddCustomerSaleOrder.Validate.Validate19')
        ]
      },
      title: '',
      salesSelectData: ''
    }
  },
  watch: {
    isVendor (val) {
      // console.log('=========>')
      if (val === 'yes') {
        this.SaleVendor = true
        this.getListGroup()
      } else {
        this.SaleVendor = false
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$EventBus.$on('ChackRowUser', this.ChackRowUser)
    this.$EventBus.$on('AddAddressCusSale', this.ManageCompanyAddress)
    this.$EventBus.$on('EditAddressCustomerComplete', this.closeDialog)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('ChackRowUser')
      this.$EventBus.$off('AddAddressCusSale')
      this.$EventBus.$off('EditAddressCustomerComplete')
      this.$EventBus.$off('SaleCanAddCus')
    })
  },
  created () {
    if (localStorage.getItem('UserDetailSelling') !== null) {
      this.userDetailSelling = JSON.parse(Decode.decode(localStorage.getItem('UserDetailSelling')))
    }
    if (localStorage.getItem('sellerShopMP') !== null) {
      var sellerShopMPData = JSON.parse(Decode.decode(localStorage.getItem('sellerShopMP')))
      sellerShopMPData.use_vandor = true
      this.useVendor = sellerShopMPData.use_vandor
      // console.log('sellerShopMPData', sellerShopMPData)
    }
    if (localStorage.getItem('roleUser') !== null) {
      if (JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order_no_JV') {
        this.SellerShopID = parseInt(localStorage.getItem('ShopID'))
      } else {
        this.SellerShopID = parseInt(localStorage.getItem('shopSellerID'))
      }
    }
    this.$EventBus.$on('SaleCanAddCus', this.getRoleSale)
  },
  beforeDestroy () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.addressList.slice(this.indexStart, this.indexEnd)
    }
  },
  methods: {
    ChangeLang (val) {
      if (val === 'เพิ่มข้อมูลลูกค้า') {
        if (this.$i18n.locale === 'th') {
          return val
        } else {
          return this.$t('ModalAddCustomerSaleOrder.HeaderAddCustomer')
        }
      } else if (val === 'แก้ไขข้อมูลลูกค้า') {
        if (this.$i18n.locale === 'th') {
          return val
        } else {
          return this.$t('ModalAddCustomerSaleOrder.HeaderEditCustomer')
        }
      }
    },
    async open (action, page, saleid, item) {
      this.$store.commit('openLoader')
      // console.log('หน้าเพิ่มลูกค้าopen', action, page, saleid, item)
      await this.getListGropCustomerSale()
      await this.getListSaleData()
      // if (saleVendor !== undefined) {
      //   this.SaleVendor = saleVendor
      // } else {
      //   this.SaleVendor = false
      // }
      if (item) {
        if (item.is_vendor === 'yes') {
          this.SaleVendor = true
        } else {
          this.SaleVendor = false
        }
      }
      // console.log('this.SaleVendor=====>', this.SaleVendor)
      this.$EventBus.$emit('closeDialogListEditCustomer')
      this.title = action
      this.pageForm = page
      // console.log('page=====>', page)
      this.creditTerm = item === undefined ? '' : item.credit_term
      this.numCreditTerm = item === undefined ? '' : item.num_of_credit_term
      // this.saleID = this.pageForm === 'listCustomerSale' ? saleid.sale_id : saleid
      this.customerSaleData = item
      if (this.pageForm === 'shoppage') {
        this.SellerShopID = parseInt(localStorage.getItem('ShopID'))
        this.saleID = saleid
        if (localStorage.getItem('sale_order_customer') !== null) {
          var typeSale = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
          if (typeSale === 'vendor') {
            this.isVendor = 'yes'
            this.useVendor = true
          } else {
            this.isVendor = 'no'
            this.useVendor = true
          }
        }
      } else if (this.pageForm === 'listCustomerSale') {
        if (item === undefined) {
          this.saleData = saleid
          this.saleID = saleid.id
          this.nameSale = saleid.sale_name
        } else {
          this.saleData = item
          this.saleID = saleid
        }
        this.SellerShopID = parseInt(localStorage.getItem('shopSellerID'))
      } else {
        this.saleID = saleid === undefined ? this.SaleID : saleid
        this.SellerShopID = parseInt(localStorage.getItem('shopSellerID'))
      }
      this.modalManageCustomerSale = true
      if (this.title === 'แก้ไขข้อมูลลูกค้า') {
        this.getEditCustomerData(item)
      } else if (this.title === 'เพิ่มข้อมูลลูกค้า') {
        this.clearData()
        // console.log('this.pageForm', this.pageForm)
        // if (this.pageForm === 'listCustomerSaleOrder') {
        //   this.getListSaleData()
        // }
      }
      this.$store.commit('closeLoader')
      // this.$refs.form.resetValidation()
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateTaxID () {
      if (this.validNationalID(this.inputData.FederalTaxID)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    async getListGroup () {
      // console.log('getListGroup')
      var data = {
        seller_shop_id: this.SellerShopID
      }
      // console.log('datagetListGropCustomerSale', data)
      await this.$store.dispatch('actionsListCustomerTypeOption', data)
      var res = await this.$store.state.ModuleSaleOrder.stateListCustomerTypeOption
      if (res.result === 'SUCCESS') {
        this.ListGropVendor = res.data
      } else {
        // console.log('getListGropCustomerSale0001')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 2000 })
      }
    },
    getRoleSale (roleSale, admin, saleID) {
      this.SaleCanAddCus = roleSale
      this.AdminSaleNoJV = admin
      this.SaleID = saleID
      this.saleID = saleID
      // console.log('this.SaleCanAddCus', this.SaleCanAddCus, this.AdminSaleNoJV, this.SaleID, this.saleID)
    },
    async getListSaleData () {
      // console.log('getListSaleData')
      var data = {
        seller_shop_id: this.SellerShopID
      }
      // console.log('datagetListSaleData', data)
      await this.$store.dispatch('actionsListSales', data)
      const response = await this.$store.state.ModuleSaleOrder.stateListSales
      if (response.message === 'Get list sale data successfully.') {
        this.itemSales = response.data.active
        if (this.AdminSaleNoJV === 'N' && this.SaleCanAddCus === 'Y') {
          var itemSalesNoAdmin = response.data.active
          itemSalesNoAdmin.map(e => {
            if (e.id === this.SaleID) {
              this.sale.saleName = e.name
            }
          })
        }
        // console.log('itemsaleres', this.itemSales)
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      }
    },
    async getListGropCustomerSale () {
      // const sellerShopId = parseInt(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: this.SellerShopID
      }
      // console.log('datagetListGropCustomerSale', data)
      await this.$store.dispatch('actionsGetListTierCustomer', data)
      var res = await this.$store.state.ModuleShop.stateGetListTierCustomer
      if (res.result === 'SUCCESS') {
        this.ListGropCustomerSale = res.data.data_active
      } else {
        // console.log('getListGropCustomerSale0001')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 2000 })
      }
      // console.log('ListGropCustomerSale0111111111', this.ListGropCustomerSale)
    },
    selectTypecus (val) {
      // console.log('selectTypecus', val)
      var data
      // if (this.SaleVendor === true) {
      //   if (val === 'bussiness') {
      //     this.typethai = 'บริษัท'
      //   } else {
      //     this.typethai = 'บุคคล'
      //   }
      //   // this.ListGropVendor.forEach(e => {
      //   //   if (e.type === 'bussiness') {
      //   //     typethai = 'บริบัษ'
      //   //     list.push({ typethai: typethai })
      //   //   } else {
      //   //     typethai = 'บุคคล'
      //   //     list.push({ typethai: typethai })
      //   //   }
      //   // })
      // } else {
      if (val === 'บุคคล' || val === 'general') {
        data = { role_customer: 'general' }
      } else {
        data = { role_customer: 'business' }
      }
      // }
      localStorage.setItem('sale_order_customer', JSON.stringify(data))
    },
    async ManageCompanyAddress (actions, item) {
      // this.$store.commit('openLoader')
      // console.log('ManageCompanyAddress หน้า customerSale')
      // console.log('actions', actions, this.SaleVendor)
      // console.log('title', this.title)
      // console.log('item', item)
      // console.log('saleid', this.saleID)
      this.action = actions
      this.cusType = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      this.$EventBus.$emit('actionSale', this.action, this.cusType)
      // ฟิก
      var RoleSale = JSON.parse(localStorage.getItem('roleUser')).role
      if (RoleSale !== 'sale_order_no_JV') {
        RoleSale = 'seller'
      }
      // var RoleSale = 'sale_order_no_JV'
      // console.log('RoleUser', RoleUser)
      if (this.action === 'edit') {
        this.EditCustomer(RoleSale, item)
      } else {
        if (this.$refs.form.validate()) {
          if (this.cusType === 'general' && this.SaleVendor === false) {
            // console.log('ลูกค้าทั่วไป')
            if (this.action === 'add') {
              // console.log('เพิ่มลูกค้าทั่วไป')
              var val
              val = {
                role: RoleSale,
                seller_shop_id: this.SellerShopID,
                cus_name: this.inputData.cus_name,
                cus_code: this.inputData.cus_code,
                cus_type: this.cusType,
                is_vendor: this.isVendor,
                // tax_id: parseInt(this.taxID),
                email: this.email,
                tier_id: parseInt(this.tierID),
                tier_level: this.tierLevel,
                sale_id: parseInt(this.saleID),
                credit_term: parseInt(this.creditTerm),
                num_of_credit_term: parseInt(this.numCreditTerm),
                remark: this.inputData.remark,
                cus_address: {
                },
                invoice_address: {
                }
              }
              // console.log('val00000000000', this.SaleVendor, val)
              localStorage.setItem('AddressCustomerDetail', Encode.encode(val))
              this.EditAddressDetail = await val
              this.titleAddress = await 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
              this.page = await this.pageForm
              // this.$EventBus.$emit('EditModalAddress', this.EditAddressDetail, this.titleAddress, this.page, actions)
              this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
            } else if (this.action === 'addAddress') {
              const val = {
                role: RoleSale,
                seller_shop_id: this.SellerShopID,
                cus_name: this.inputData.cus_name,
                cus_code: this.inputData.cus_code,
                cus_type: this.cusType,
                is_vendor: this.isVendor,
                // tax_id: parseInt(this.taxID),
                email: this.email,
                tier_id: parseInt(this.tierID),
                tier_level: this.tierLevel,
                sale_id: parseInt(this.saleID),
                credit_term: parseInt(this.creditTerm),
                num_of_credit_term: parseInt(this.numCreditTerm),
                remark: this.inputData.remark,
                cus_address: {
                }
              }
              // var val = JSON.parse(Decode.decode(localStorage.getItem('AddAddressCustomer')))
              localStorage.setItem('AddressCustomerDetail', Encode.encode(val))
              this.EditAddressDetail = val
              this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
              this.page = this.pageForm
              // this.$EventBus.$emit('EditModalAddress', this.EditAddressDetail, this.titleAddress, this.page, actions)
              this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
              // console.log('AddAddressCustomer', val)
            }
            // } else {
            //   console.log('item=====>', item)
            //   // console.log('customer_address', item.customer_address[0])
            //   const editData = {
            //     seller_shop_id: SellerShopID,
            //     role: RoleSale,
            //     cus_id: item.cus_id,
            //     email: data.email ? data.email : null,
            //     name: data.name,
            //     phone: data.phone,
            //     first_name: data.first_name,
            //     last_name: data.last_name,
            //     // tax_id: data.taxID,
            //     house_no: data.house_no,
            //     room_no: data.room_no,
            //     floor: data.floor,
            //     building: data.building,
            //     moo_ban: data.moo_ban,
            //     moo_no: data.moo_no,
            //     soi: data.soi,
            //     yaek: data.yaek,
            //     street: data.street,
            //     sub_district: data.sub_district,
            //     district: data.district,
            //     province: data.province,
            //     postcode: data.postcode
            //   }
            //   console.log('editที่อยู่ทั่วไป', editData)
            //   // console.log('val', val)
            //   localStorage.setItem('AddressCustomerDetail', Encode.encode(editData))
            //   this.EditAddressDetail = editData
            //   this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
            //   this.page = 'shoppage'
            //   this.$EventBus.$emit('EditModalAddress')
            // }
          } else if (this.SaleVendor === true) {
            if (this.action === 'add') {
              // console.log('เพิ่มลูกค้าvendor')
              // var val
              val = {
                role: RoleSale,
                seller_shop_id: this.SellerShopID,
                vendor_name: this.inputData.cus_Vendor_Name,
                vendor_code: this.inputData.cus_Vendor_Code,
                sale_order_type: 'vendor',
                cus_name: this.inputData.cus_name,
                cus_code: this.inputData.cus_code,
                cus_type: this.cusType,
                customer_group_name: this.inputData.tier_vendor_Group,
                federal_tax_id: this.inputData.FederalTaxID,
                is_vendor: this.isVendor,
                // tax_id: parseInt(this.taxID),
                email: this.email,
                tier_id: parseInt(this.tierID),
                tier_level: this.tierLevel,
                sale_id: parseInt(this.saleID),
                credit_term: parseInt(this.creditTerm),
                num_of_credit_term: parseInt(this.numCreditTerm),
                remark: this.inputData.remark,
                cus_address: {
                },
                invoice_address: {
                }
              }
              // console.log('val00000000000', this.SaleVendor, val)
              localStorage.setItem('AddressCustomerDetail', Encode.encode(val))
              this.EditAddressDetail = await val
              this.titleAddress = await 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
              this.page = await this.pageForm
              // this.$EventBus.$emit('EditModalAddress', this.EditAddressDetail, this.titleAddress, this.page, actions)
              this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
            } else if (this.action === 'addAddress') {
              const val = {
                role: RoleSale,
                seller_shop_id: this.SellerShopID,
                cus_name: this.inputData.cus_name,
                cus_code: this.inputData.cus_code,
                cus_type: this.cusType,
                customer_group_name: this.inputData.tier_vendor_Group,
                federal_tax_id: this.inputData.FederalTaxID,
                is_vendor: this.isVendor,
                // tax_id: parseInt(this.taxID),
                email: this.email,
                tier_id: parseInt(this.tierID),
                tier_level: this.tierLevel,
                sale_id: parseInt(this.saleID),
                credit_term: parseInt(this.creditTerm),
                num_of_credit_term: parseInt(this.numCreditTerm),
                remark: this.inputData.remark,
                cus_address: {
                }
              }
              // var val = JSON.parse(Decode.decode(localStorage.getItem('AddAddressCustomer')))
              localStorage.setItem('AddressCustomerDetail', Encode.encode(val))
              this.EditAddressDetail = val
              this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
              this.page = this.pageForm
              // this.$EventBus.$emit('EditModalAddress', this.EditAddressDetail, this.titleAddress, this.page, actions)
              this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
              // console.log('AddAddressCustomer', val)
            }
          } else {
            // console.log('บอ')
            if (this.action === 'add') {
              // console.log('เพิ่มลูกค้าบอ')
              const val = {
                role: RoleSale,
                seller_shop_id: this.SellerShopID,
                cus_name: this.inputData.cus_name,
                is_vendor: this.isVendor,
                cus_code: this.inputData.cus_code,
                cus_type: this.cusType,
                // tax_id: parseInt(this.taxID),
                email: this.email,
                tier_id: parseInt(this.tierID),
                tier_level: this.tierLevel,
                sale_id: parseInt(this.saleID),
                credit_term: parseInt(this.creditTerm),
                num_of_credit_term: parseInt(this.numCreditTerm),
                remark: this.inputData.remark,
                cus_address: {
                },
                invoice_address: {
                }
              }
              // console.log('เพิ่มลูกค้าบอ', val)
              localStorage.setItem('AddressCustomerDetail', Encode.encode(val))
              this.EditAddressDetail = val
              this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
              this.page = this.pageForm
              // this.$EventBus.$emit('EditModalAddress')
              this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
            } else if (this.action === 'addAddress') {
              const val = {
                role: RoleSale,
                seller_shop_id: this.SellerShopID,
                cus_name: this.inputData.cus_name,
                is_vendor: this.isVendor,
                cus_code: this.inputData.cus_code,
                cus_type: this.cusType,
                // tax_id: parseInt(this.taxID),
                email: this.email,
                tier_id: parseInt(this.tierID),
                tier_level: this.tierLevel,
                sale_id: parseInt(this.saleID),
                credit_term: parseInt(this.creditTerm),
                num_of_credit_term: parseInt(this.numCreditTerm),
                remark: this.inputData.remark,
                cus_address: {
                }
              }
              localStorage.setItem('AddressCustomerDetail', Encode.encode(val))
              this.EditAddressDetail = val
              this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
              this.page = this.pageForm
              // this.$EventBus.$emit('EditModalAddress')
              this.$refs.EditModalAddress.open(this.EditAddressDetail, this.titleAddress, this.page, actions, this.SaleVendor)
              // console.log('AddAddressCustomer', val)
            }
            // } else {
            //   const editData = {
            //     seller_shop_id: SellerShopID,
            //     role: RoleSale,
            //     cus_id: item.cus_id,
            //     email: data.email ? data.email : null,
            //     name: data.name,
            //     phone: data.phone,
            //     first_name: data.first_name,
            //     last_name: data.last_name,
            //     tax_id: data.taxID,
            //     house_no: data.house_no,
            //     room_no: data.room_no,
            //     floor: data.floor,
            //     building: data.building,
            //     moo_ban: data.moo_ban,
            //     moo_no: data.moo_no,
            //     soi: data.soi,
            //     yaek: data.yaek,
            //     street: data.street,
            //     sub_district: data.sub_district,
            //     district: data.district,
            //     province: data.province,
            //     postcode: data.postcode
            //   }
            //   console.log('editที่อยู่บอ]', editData)
            //   // console.log('', editData)
            //   localStorage.setItem('AddressCompanyDetail', Encode.encode(editData))
            //   this.EditAddressDetail = editData
            //   this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
            //   this.page = 'shoppage'
            //   this.$EventBus.$emit('EditModalAddress')
            // }
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
        }
      }
    },
    async EditCustomer (RoleSale, item) {
      // console.log('inputData.tier_level', this.inputData.tier_level)
      // console.log('this.tierLevel', this.tierLevel)
      // console.log('sale.saleName', this.sale.saleName)
      // console.log('EditCustomer====>>>>>>>>>>>', item)
      // console.log('this.pageForm====>>>>>>>>>>>', this.pageForm)
      if (this.$refs.form.validate()) {
        var val
        // console.log('SaleVendor', this.SaleVendor)
        if (this.SaleVendor === true) {
          val = {
            role: RoleSale,
            seller_shop_id: this.SellerShopID,
            cus_name: this.inputData.cus_name,
            cus_code: this.inputData.cus_code,
            cus_type: this.cusType,
            is_vendor: this.isVendor,
            federal_tax_id: this.inputData.FederalTaxID,
            cus_id: item.id === undefined ? item.cus_id : item.id,
            status: item.status,
            tax_id: item.tax_id,
            customer_group_name: this.inputData.tier_vendor_Group,
            email: this.email,
            tier_id: this.tierID !== null && this.tierID !== undefined ? parseInt(this.tierID) : item.tier_id,
            tier_level: this.tierLevel !== null && this.tierLevel !== undefined ? parseInt(this.tierLevel) : item.tier_level,
            sale_id: parseInt(this.saleID),
            credit_term: parseInt(this.creditTerm),
            num_of_credit_term: parseInt(this.numCreditTerm),
            vendor_code: this.inputData.cus_Vendor_Code,
            vendor_name: this.inputData.cus_Vendor_Name,
            remark: this.inputData.remark
          }
        } else {
          val = {
            role: RoleSale,
            seller_shop_id: this.SellerShopID,
            cus_name: this.inputData.cus_name,
            is_vendor: this.isVendor,
            cus_code: this.inputData.cus_code,
            cus_type: this.cusType,
            cus_id: item.id === undefined ? item.cus_id : item.id,
            status: item.status,
            tax_id: item.tax_id,
            email: this.email,
            tier_id: this.tierID !== null && this.tierID !== undefined ? parseInt(this.tierID) : item.tier_id,
            tier_level: this.tierLevel !== null && this.tierLevel !== undefined ? parseInt(this.tierLevel) : item.tier_level,
            sale_id: parseInt(this.saleID),
            credit_term: parseInt(this.creditTerm),
            num_of_credit_term: parseInt(this.numCreditTerm),
            remark: this.inputData.remark
          }
        }
        // console.log('val=========>', val)
        await this.$store.dispatch('actionsEditCustomer', val)
        var response = await this.$store.state.ModuleSaleOrder.stateEditCustomer
        if (response.message === 'Update customer data successfully.') {
          // console.log('แก้ไขสำเร็จ')
          this.$EventBus.$emit('getListCustomerOfSales')
          this.$EventBus.$emit('closeEdit')
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('EditAddressCustomerComplete')
          this.dialogSuccess = true
          this.getListGropCustomerSale()
          this.closeDialog()
          // window.location.reload()
        } else {
          if (response.message === 'This user is unauthorized.') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
            // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
            // window.location.assign('/')
          } else {
            this.$store.commit('closeLoader')
            var [msg, iconMsg] = this.getErrorMsg(response.message)
            this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
    },
    getEditCustomerData (item) {
      // console.log('edit Customer', item)
      // console.log('SaleVendor', this.SaleVendor)
      if (item) {
        if (this.SaleVendor === true) {
          this.inputData.cus_Vendor_Code = item.vendor_code
          this.inputData.FederalTaxID = item.federal_tax_id
          this.inputData.tier_vendor_Group = item.customer_group_name
          // console.log('this.inputData.FederalTaxID', this.inputData.FederalTaxID, item.federal_tax_id)
          this.inputData.cus_Vendor_Name = item.vendor_name
        }
        this.isVendor = item.is_vendor
        this.inputData.cus_name = item.cus_name
        this.inputData.cus_code = item.cus_code
        this.inputData.cus_tel = item.cus_tel
        this.inputData.tier_name = item.tier_name
        this.inputData.tier_level = item.tier_level
        this.listTierGrop = item.tier_level
        this.inputData.remark = item.remark
        this.email = item.email
        // this.itemSales = item.sale_name
        this.taxID = item.tax_id
        this.sale.saleName = item.sale_name
        this.nameSale = item.sale_name
        this.typeCus = item.cus_type === 'general' ? 'บุคคล' : 'บริษัท'
      } else {
        this.$swal.fire({ icon: 'error', text: 'ไม่สามารถแก้ไขได้ กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2500 })
        this.$store.commit('closeLoader')
      }
      // this.$store.commit('openLoader')
      // const data = {
      //   seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
      //   cus_id: item.cus_id
      // }
      // await this.$store.dispatch('actionsEditCustomer', data)
      // var response = await this.$store.state.ModuleSaleOrder.stateEditCustomer
      // if (response.message === 'Get detail customer success.') {
      //   this.$store.commit('closeLoader')
      //   this.inputData.cus_name = response.data[0].cus_name
      //   this.inputData.cus_code = response.data[0].cus_code
      //   this.inputData.cus_tel = response.data[0].cus_tel.replace('-', '')
      //   this.inputData.channel_name = response.data[0].channel_name
      //   this.inputData.note = response.data[0].note
      //   this.inputData.emp_code = response.data[0].sales_code
      //   this.inputData.emp_name = response.data[0].sales_name
      // } else {
      //   if (response.message === 'This user is unauthorized.') {
      //     this.$store.commit('closeLoader')
      //     this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
      //     window.location.assign('/')
      //   } else {
      //     this.$store.commit('closeLoader')
      //     var [msg, iconMsg] = this.getErrorMsg(response.message)
      //     this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
      //   }
      // }
    },
    comfirmManageCustomer () {
      this.dialogConfirmmanageCustomerSale = true
    },
    closeConfirmManageCustomer () {
      this.dialogConfirmmanageCustomerSale = false
    },
    async manageCustomser () {
      this.dialogConfirmmanageCustomerSale = false
      if (this.title === 'เพิ่มข้อมูลลูกค้า') {
        this.addCustomer()
      } else {
        this.editCustomer()
      }
    },
    async addCustomer () {
      this.$store.commit('openLoader')
      if (this.$refs.form.validate(true)) {
        var shopData = this.userDetailSelling
        const data = {
          seller_shop_id: shopData.seller_shop_id,
          cus_name: this.inputData.cus_name,
          cus_code: this.inputData.cus_code,
          cus_tel: this.inputData.cus_tel,
          channel_id: this.findChannelId(this.inputData.channel_name),
          note: this.inputData.note,
          emp_code: this.inputData.emp_code,
          emp_name: this.inputData.emp_name
        }
        await this.$store.dispatch('actionsGraczCreateCustomer', data)
        var response = await this.$store.state.ModuleGraczCustomer.stateGraczCreateCustomer
        if (response.message === 'Create customer successfully.') {
          this.$store.commit('closeLoader')
          // this.$EventBus.$emit('getCustomerList')
          // this.closeDialog()
          this.$swal.fire({ icon: 'success', text: 'เพิ่มข้อมูลลูกค้าสำเร็จ', showConfirmButton: false, timer: 2000 })
          var cusid = response.data.cus_id
          if (this.MobileSize) {
            this.$router.push(`/Order/CustomerDetailMobile?cusid=${cusid}`).catch(() => {})
          } else {
            this.$router.push(`/Order/CustomerDetail?cusid=${cusid}`).catch(() => {})
          }
        } else {
          this.$store.commit('closeLoader')
          if (response.message === 'This user is unauthorized.') {
            this.$EventBus.$emit('refreshToken')
            // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
            // window.location.assign('/')
          } else if (response.message === 'Not access this function.') {
            this.$swal.fire({ icon: 'warning', text: 'มีการเปลี่ยนแปลงตำแหน่ง หรือเปลี่ยนแปลงสิทธิ์การใช้งาน กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
            window.location.assign('/')
          } else {
            var [msg, iconMsg] = this.getErrorMsg(response.message)
            this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 2000 })
      }
    },
    async editCustomer () {
      // console.log('editCustomer')
      this.$store.commit('openLoader')
      if (this.$refs.form.validate(true)) {
        var shopData = this.userDetailSelling
        const data = {
          seller_shop_id: shopData.seller_shop_id,
          cus_id: this.$route.query.cusid,
          cus_name: this.inputData.cus_name,
          cus_code: this.inputData.cus_code,
          cus_tel: this.inputData.cus_tel,
          channel_id: this.findChannelId(this.inputData.channel_name),
          note: this.inputData.note,
          emp_code: this.inputData.emp_code,
          emp_name: this.inputData.emp_name
        }
        await this.$store.dispatch('actionsGraczEditCustomer', data)
        var response = await this.$store.state.ModuleGraczCustomer.stateGraczEditCustomer
        if (response.message === 'Update customer data successfully.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('getCustomerDetail')
          this.closeDialog()
          this.$swal.fire({ icon: 'success', text: 'แก้ไขข้อมูลลูกค้าสำเร็จ', showConfirmButton: false, timer: 2000 })
        } else {
          this.$store.commit('closeLoader')
          if (response.message === 'This user is unauthorized.') {
            this.$EventBus.$emit('refreshToken')
            // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
            // window.location.assign('/')
          } else if (response.message === 'Not access this function.') {
            this.$swal.fire({ icon: 'warning', text: 'มีการเปลี่ยนแปลงตำแหน่ง หรือเปลี่ยนแปลงสิทธิ์การใช้งาน กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2000 })
            window.location.assign('/')
          } else {
            var [msg, iconMsg] = this.getErrorMsg(response.message)
            this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 2000 })
      }
    },
    findListGroupId (Group, id) {
      // console.log('Group', Group)
      // console.log('id', id)
      if (this.ListGropVendor.length !== 0) {
        this.ListGropVendor.forEach(e => {
          if (e.name === Group) {
            // this.typeCus = e.type
            this.typeCus = e.type === 'general' ? 'บุคคล' : 'บริษัท'
            this.selectTypecus(this.typeCus)
          }
        })
      }
    },
    findTierlId (tierName, level) {
      // console.log('findTierlId')
      if (tierName !== '') {
        if (this.ListGropCustomerSale.length !== 0) {
          this.ListGropCustomerSale.forEach(element => {
            if (element.tier_name === tierName) {
              this.listTierGrop = element.level_discount_percent
              if (level !== '') {
                this.listTierGrop.forEach(e => {
                  if (e.tier_level === level) {
                    this.tierLevel = e.tier_level
                  }
                })
              }
              this.tierID = element.id
              // console.log('element.id', element.id)
              // console.log('tier_id', this.tierID)
            }
          })
        }
      }
    },
    findSalesId (SaleName) {
      // console.log('findSalesId')
      if (SaleName !== '') {
        if (this.itemSales.length !== 0) {
          this.itemSales.forEach(element => {
            if (element.sale_name === SaleName) {
              this.saleID = element.id
              // console.log('this.saleID', this.saleID)
              // console.log('SaleName', SaleName)
            }
          })
        }
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return ('กรุณาเข้าสู่ระบบใหม่อีกครั้ง', 'warning')
      } else if (msg === 'Data missing. Please check your parameter and try again.') {
        return ['ข้อมูลขาดหาย', 'warning']
      } else if (msg === 'Not access this function.') {
        return ['มีการเปลี่ยนแปลงตำแหน่ง หรือเปลี่ยนแปลงสิทธิ์การใช้งาน กรุณาลองใหม่อีกครั้ง', 'warning']
      } else if (msg === 'Employee not found. Please check and try again.') {
        return ['ไม่พบพนักงาน โปรดเช็คแล้วลองใหม่อีกครั้ง', 'warning']
      } else if (msg === 'Seller shop not found.' || msg === 'Not Found Seller Shop') {
        return ['ไม่พบร้านค้า', 'warning']
      } else if (msg === 'Parameter Missing. Please check and try again.') {
        return ['ข้อมูลขาดหาย โปรดลองใหม่อีกครั้ง', 'warning']
      } else if (msg === 'Customer in this shop not found.') {
        return ['ไม่พบลูกค้าในร้านนี้', 'warning']
      } else if (msg === 'Employee is not found.') {
        return ['ไม่พบพนักงาน', 'warning']
      } else if (msg === 'Employee usage status is inactive.') {
        return ['สถานะการใช้งานของพนักงานปิดอยู่', 'warning']
      } else if (msg === 'Customer code is already exists.') {
        return ['รหัสลูกค้านี้ถูกใช้อยู่', 'warning']
      } else if (msg === "There's an item in the cart that can't be edited.") {
        return ['มีของในรถเข็นไม่สามารถแก้ไขได้', 'warning']
      } else if (msg === 'Please enter the same employee code.') {
        return ['กรุณากรอกรหัสพนักงานเดียวกัน', 'warning']
      } else if (msg === 'Please enter the same employee name.') {
        return ['กรุณากรอกชื่อพนักงานเดียวกัน', 'warning']
      } else if (msg === 'Customer name a maximum of 50 characters.') {
        return ['กรอกชื่อลูกค้าได้ไม่เกิน 50 ตัวอักษร', 'warning']
      } else {
        return [msg, 'error']
      }
    },
    closeDialog () {
      // console.log('เข้าไหม')
      this.$refs.form.resetValidation()
      this.clearData()
      localStorage.removeItem('AddressBackward')
      localStorage.removeItem('invoiceBackward')
      localStorage.removeItem('AddAddressCustomer')
      // localStorage.removeItem('AddressCustomerBussinessSale')
      // localStorage.removeItem('AddressCustomerSale')
      this.modalManageCustomerSale = false
      // this.modalManageCustomerSale = !this.modalManageCustomerSale
    },
    clearData () {
      this.$refs.form.resetValidation()
      this.$EventBus.$emit('clearData')
      this.inputData.remark = ''
      this.inputData.tier_level = ''
      this.inputData.tier_vendor_Group = ''
      this.inputData.FederalTaxID = ''
      this.isVendor = 'no'
      this.inputData.cus_name = ''
      this.inputData.cus_code = ''
      this.inputData.cus_tel = ''
      this.inputData.channel_name = ''
      this.inputData.note = ''
      this.inputData.emp_code = ''
      this.inputData.emp_name = ''
      this.inputData.tier_name = ''
      this.sale.saleName = ''
      this.typeCus = ''
      this.creditTerm = ''
      this.inputData.cus_Vendor_Code = ''
      this.inputData.cus_Vendor_Name = ''
      this.numCreditTerm = ''
    }
  }
}
</script>
<style lang="css" scoped>
.discount {
background: #F3F5F7;
border-radius: 4px;
padding: 5px;
}
::-webkit-scrollbar {
width: 6px;
}/* Track */
::-webkit-scrollbar-track {
background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
background: #E6E6E6;
border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
background: #888;
border-radius: 8px;
}
</style>
<style scoped>
  .v-text-field input {
    font-size: 0.9em;
  }
  .v-text-field__details {
    margin-bottom: 4px !important;
  }
  .input_text {
    height: 60px;
    opacity: 1 !important;
  }
  .doc-detail {
    font-size: 14px;
    text-align: center;
    font-weight: 600;
  }
  .blod-detail {
    font-size: 16px;
    font-weight: 600;
  }
  .title-detail {
    font-size: 14px;
    font-weight: 400;
  }
  .title-mobil {
    font-size: 16px;
  }
  .v-messages {
    min-height: 0px;
  }
  .on-hover {
    background: rgba(216, 239, 228, 0.5) !important;
    border: 1px solid #27AB9C !important;
  }
</style>
