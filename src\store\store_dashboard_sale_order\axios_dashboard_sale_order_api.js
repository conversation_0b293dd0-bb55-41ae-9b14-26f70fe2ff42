import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  // ดึงข้อมูลของกราฟรายได้ และ ผลรวมรายได้กับจำนวนรายการ
  async SaleorderRevenueGraph (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}saleorderDashboard/saleorderRevenueGraph`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SaleorderOrderListAndTopProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}saleorderDashboard/saleorderOrderListAndTopProduct`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SaleorderTopSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}saleorderDashboard/topSale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SaleorderTopCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}saleorderDashboard/topCustomer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SaleorderTopCustomerGroup (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}saleorderDashboard/topCustomerGroup`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SaleorderSaleCustomerGroup (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}sale/saleCustomerGroup`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // export ไฟล์ excel
  async SaleorderExport (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}saleorderDashboard/exportSaleorderDashboard`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
