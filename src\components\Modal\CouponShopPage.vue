<template>
  <v-dialog v-model="openCouponShopModal" :width="!MobileSize ? '784' : '100%'" :style="MobileSize ? 'z-index: 16000004' : ''" scrollable persisten>
    <v-card max-height="690" class="rounded-lg">
      <v-toolbar align="center" color="#BDE7D9" dark dense>
        <v-row>
          <v-col class="d-flex justify-space-around">
            <v-toolbar-title>
              <span style="color: #27AB9C; font-size: 20px;"><b>เก็บคูปองส่วนลดจากร้านค้า</b></span>
            </v-toolbar-title>
          </v-col>
        </v-row>
        <v-btn fab small @click="openCouponShopModal = !openCouponShopModal" icon>
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row class="mt-5">
          <v-col v-for="(items, index) in CouponsIteam" :key="index" cols="12" md="12" class="px-0">
            <v-card class="rounded-lg" width="100%">
              <v-card-text class="px-0">
                <v-row no-gutters>
                  <v-col class="pa-1 ml-1" :cols="MobileSize ? 8 : 8" md="8">
                    <!-- <v-hover v-slot="{ hover }" open-delay="200"> -->
                    <span v-if="MobileSize" class="pa-1">
                      <CardCouponMobile v-if="index % 2 === 0" :items="items" :keep="false" colorCard="blue" />
                      <CardCouponMobile v-else :items="items" :keep="false" colorCard="green" />
                    </span>
                    <span v-else class="pa-1">
                      <CardCoupon v-if="index % 2 === 0" :items="items" :keep="false" colorCard="blue" />
                      <CardCoupon v-else :items="items" :keep="false" colorCard="green" />
                    </span>
                    <!-- </v-hover> -->
                  </v-col>
                  <v-col v-if="!MobileSize" cols="1" md="1" align="center">
                    <v-divider vertical class="a"></v-divider>
                  </v-col>
                  <v-col cols="1" md="2">
                    <v-container bg fill-height grid-list-md text-xs-center>
                      <v-layout row wrap align-center>
                        <v-btn v-if="items.status === 'not_collected'" @click="keepCoupons(items)" class="white--text"
                          dense color="#27AB9C" :class="MobileSize ? 'px-2' : ''">
                          เก็บคูปอง
                        </v-btn>
                        <v-btn v-else dense outlined disabled color="#27AB9C" :class="MobileSize ? 'px-3' : ''">
                          เก็บแล้ว
                        </v-btn>
                      </v-layout>
                    </v-container>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <span @click="GoToAllCoupon()"
          style="color: #27AB9C; font-size: 16px; font-weight: 600; cursor: pointer;"><U>คูปองทั้งหมด</U></span>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardCoupon: () => import('@/components/CardCoupon/CardCoupon'),
    CardCouponMobile: () => import('@/components/CardCoupon/CardCouponShopUI')
  },
  data () {
    return {
      openCouponShopModal: false,
      keep: true,
      CouponsIteam: [],
      companyId: '',
      shop: '',
      role_user: '',
      company_id: '',
      path: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  created () {
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
  },
  methods: {
    async open (data, shop, companyid, path) {
      this.shop = shop
      this.company_id = companyid
      this.openCouponShopModal = true
      this.CouponsIteam = data
      this.path = path
    },
    async getCoupon () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        seller_shop_id: this.shop,
        role_user: dataRole.role,
        company_id: this.company_id,
        list_type: 'shop'
      }
      await this.$store.dispatch('actionsAllCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateAllCouponInShop
      this.CouponsIteam = []
      for (let i = 0; i < response.data.length; i++) {
        this.CouponsIteam.push({
          image: response.data[i].couponImagePath,
          name: response.data[i].couponName,
          description: response.data[i].couponDescription,
          couponDate: {
            useStartDate: response.data[i].useStartDate,
            useEndDate: response.data[i].useEndDate
          },
          status: response.data[i].status,
          couponId: response.data[i].couponId,
          shop_name: response.data[i].shop_name
        })
      }
    },
    GoToAllCoupon () {
      this.$router.push({ path: '/AllCouponInShop', name: 'AllCouponInShop', params: { data: this.path } })
    },
    async keepCoupons (item) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('SetRowCompany') !== null) {
        const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        this.companyId = companyId.company.company_id
      } else {
        this.companyId = '-1'
      }
      var data = {
        role_user: dataRole.role,
        company_id: this.companyId,
        coupons_id: item.couponId
      }
      await this.$store.dispatch('actionskeepCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateKeepCoupon
      if (response.code === 200) {
        this.getCoupon()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.a {
    border: #E6E6E6 1px dashed;
}
</style>
