<template>
<v-card width="100%" elevation="0">
  <v-card-text>
    <v-col cols="12" class="pa-0">
      <v-row no-gutters>
        <v-col :cols="MobileSize || IpadSize ? '12' : '6'" class="pa-0">
          <span style="font-weight: 700; font-size: 16px; line-height: 22px; color: #333333;" @click="backtoPage()" v-if="MobileSize"><v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> ADMIN Summary Dashboard</span>
          <span style="font-weight: 700; line-height: 32px; color: #333333;" :style="IpadSize ? 'font-size: 12px;' : 'font-size: 24px;'" class="py-0 px-0" v-else> ADMIN Summary Dashboard</span>
        </v-col>
        <v-col cols="12" v-if="MobileSize" class="pa-0 pt-2" align="end">
          <v-btn rounded color="primary" small block class="mb-2" @click="ExportSumTransaction()">Export Transaction</v-btn>
          <v-btn rounded color="primary" small block outlined @click="ExportSumTransactionJV()">Export Transaction JV</v-btn>
        </v-col>
        <v-col :cols="IpadSize? '12' : '6'" v-else align="end">
          <v-row no-gutters>
            <v-col cols="6">
            <v-btn v-if="IpadProSize" rounded color="primary" small class="mr-2" @click="ExportSumTransaction()">Export Transaction</v-btn>
            <v-btn v-else-if="IpadSize" block rounded color="primary" x-small class="mr-2" @click="ExportSumTransaction()">Export Transaction</v-btn>
            <v-btn v-else rounded color="primary" class="mr-1" @click="ExportSumTransaction()">Export Transaction</v-btn>
            </v-col>
            <v-col cols="6">
            <v-btn v-if="IpadProSize" rounded color="primary" small outlined @click="ExportSumTransactionJV()">Export Transaction JV</v-btn>
            <v-btn v-else-if="IpadSize" block rounded color="primary" x-small outlined @click="ExportSumTransactionJV()">Export Transaction JV</v-btn>
            <v-btn v-else rounded color="primary" outlined @click="ExportSumTransactionJV()">Export Transaction JV</v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-col>
  </v-card-text>
  <v-col cols="12">
    <v-row dense>
      <!-- QT -->
      <v-col class="QT" cols="6">
        <v-card class="card" width="100%" outlined>
          <v-col cols="12">
            <v-row no-gutters>
              <p style="color: #535353; font-size: 16px; font-weight: 600">QT</p>
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #587fe3;
                  font-size: 12px;
                "
              >
                {{formatNumber(sumDocQT)}}
              </v-chip>
            </v-row>
            <v-row no-gutters style="margin-top: -15px; margin-bottom: -15px;">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <p style="
                    color: #587fe3;
                    font-size: 28px;
                    font-weight: 600;"
                    v-bind="attrs" v-on="on"
                    v-if="sumDocRevenueQT > 99999999"
                  >THB {{formatNumber(sumDocRevenueQT)}}</p>
                  <p style="
                    color: #587fe3;
                    font-size: 28px;
                    font-weight: 600;"
                    v-bind="attrs" v-on="on"
                    v-else
                  >THB {{sumDocRevenueQT}}</p>
                </template>
                  <span>{{ formatNumberTooltip(sumDocRevenueQT) }} บาท</span>
              </v-tooltip>
            </v-row>
          </v-col>
        </v-card>
      </v-col>
      <!-- SO -->
      <v-col class="QT" cols="6">
        <v-card class="card" width="100%" outlined>
          <v-col cols="12">
            <v-row no-gutters>
              <p style="color: #535353; font-size: 16px; font-weight: 600">SO</p>
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #f87956;
                  font-size: 12px;
                "
              >
                {{formatNumber(sumDocSO)}}
              </v-chip>
            </v-row>
            <v-row no-gutters style="margin-top: -15px; margin-bottom: -15px;">
              <!-- <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <p style="
                    color: #f87956;
                    font-size: 28px;
                    font-weight: 600;"
                    v-bind="attrs" v-on="on"
                    v-if="sumDocRevenueSO > 99999999"
                  >{{formatNumber(sumDocRevenueSO)}}</p>
                  <p style="
                    color: #f87956;
                    font-size: 28px;
                    font-weight: 600;"
                    v-bind="attrs" v-on="on"
                    v-else
                  >{{sumDocRevenueSO}}</p>
                </template>
                  <span>{{ formatNumberTooltip(sumDocRevenueSO) }} บาท</span>
              </v-tooltip> -->
              <p style="
                color: #f87956;
                font-size: 28px;
                font-weight: 600;
              ">{{formatNumber(sumDocRevenueSO)}}</p>
            </v-row>
          </v-col>
        </v-card>
      </v-col>
      <!-- PR -->
      <v-col class="QT" cols="6">
        <v-card class="card" width="100%" outlined>
          <v-col cols="12">
            <v-row no-gutters>
              <p style="color: #535353; font-size: 16px; font-weight: 600">PR</p>
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #67b7dc;
                  font-size: 12px;
                "
              >
                {{formatNumber(sumDocPR)}}
              </v-chip>
            </v-row>
            <v-row no-gutters style="margin-top: -15px; margin-bottom: -15px;">
              <p style="
                color: #67b7dc;
                font-size: 28px;
                font-weight: 600;
              ">{{formatNumber(sumDocRevenuePR)}}</p>
            </v-row>
          </v-col>
        </v-card>
      </v-col>
      <!-- PO -->
      <v-col class="QT" cols="6">
        <v-card class="card" width="100%" outlined>
          <v-col cols="12">
            <v-row no-gutters>
              <p style="color: #535353; font-size: 16px; font-weight: 600">PO</p>
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #7d72b9;
                  font-size: 12px;
                "
              >
                {{formatNumber(sumDocPO)}}
              </v-chip>
            </v-row>
            <v-row no-gutters style="margin-top: -15px; margin-bottom: -15px;">
              <p style="
                color: #7d72b9;
                font-size: 28px;
                font-weight: 600;
              ">{{formatNumber(sumDocRevenuePO)}}</p>
            </v-row>
          </v-col>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense>
      <!-- กราฟเส้น -->
      <!-- <v-col class="QT" cols="6">
        <v-card class="pa-2 card" outlined>
          <v-col cols="12">
            <v-row class="d-flex justify-space-between">
              <div>
                <p style="font-size:16px; padding-top:10px;">Summary Trend</p>
              </div>
              <div>
                <v-chip-group v-model="firstLine">
                  <v-chip label plain small color="transparent" class="custom-chip" @click="getAllSumTrendAdmin('day')">วัน</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" value="month" @click="getAllSumTrendAdmin('month')">เดือน</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="getAllSumTrendAdmin('quarter')">ไตรมาส</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="getAllSumTrendAdmin('halfyear')">ครึ่งปี</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="getAllSumTrendAdmin('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
            <div v-if="dataRevenueOfSumTrend.length !== 0">
              <apexchart
                type="line"
                height="350"
                :options="optionLine.chartOptions"
                :series="seriesLine.series"
              ></apexchart>
              <v-row justify="end" style="margin-bottom:-20px" no-gutters>
                <p style="color:red">* กราฟแสดงเฉพาะวันที่มี Transaction เท่านั้น</p>
              </v-row>
            </div>
            <div v-else>
              <v-col>
                <v-row no-gutters justify="center">
                  <v-container>
                    <v-img contain height="250" src="@/assets/Not_Result.png"></v-img>
                  </v-container>
                  <p style="font-size:20px; font-weight:600; color:#636363; margin-top:38px;">ไม่มี Transaction </p>
                </v-row>
              </v-col>
            </div>
          </v-col>
        </v-card>
      </v-col> -->
      <!-- กราฟแท่ง -->
      <!-- <v-col class="QT" cols="6">
        <v-card class="pa-2 card" outlined>
          <v-col cols="12">
            <v-row class="d-flex justify-space-between">
              <div>
                <p style="font-size:16px; padding-top:10px;">จำนวนเอกสาร</p>
              </div>
              <div >
              <v-chip-group v-model="firstBar">
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('day')">วัน</v-chip>
                <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docBar('month')">เดือน</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('quarter')">ไตรมาส</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('halfyear')">ครึ่งปี</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
            <apexchart
              type="bar"
              height="350"
              :options="optionBar.chartOptions"
              :series="seriesBar.series"
            ></apexchart>
            <v-row justify="end" style="margin-bottom:-20px" no-gutters>
              <p style="color:red">* กราฟแสดงเฉพาะวันที่มี Transaction เท่านั้น</p>
            </v-row>
          </v-col>
        </v-card>
      </v-col> -->
    </v-row>
    <!-- <v-row dense>
      <v-col class="QT" cols="12">
        <v-card class="pa-2 card" outlined>
          <v-col cols="12">
            <v-row class="d-flex justify-space-between">
              <div style="padding-left: 10px">
                <p style="font-size:16px; padding-top:10px;">รายการสั่งซื้อสินค้า ( Top 5 )</p>
              </div>
              <div>
                <v-chip-group v-model="firstPie">
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('day')">วัน</v-chip>
                  <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docPie('month')">เดือน</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('quarter')">ไตรมาส</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('halfyear')">ครึ่งปี</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
            <v-divider style="background:#53535333; margin-top: 5px"></v-divider>
          </v-col>
        </v-card>
      </v-col>
    </v-row> -->
  </v-col>
  <!-- <v-card-text class="px-1 py-0">
    <v-card-title style="font-weight: 700; font-size: 16px; line-height: 22px; color: #333333;" @click="$router.go(-1)" v-if="MobileSize"><v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> PANIT Summary Dashboard</v-card-title>
    <v-card-title  style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="py-0" v-else> PANIT Summary Dashboard</v-card-title>
    <v-row dense :justify="MobileSize ? 'center' : IpadSize ? 'center' : 'end'" class="pb-0">
      <v-col :cols="MobileSize ? '12' : '6'" md="7" sm="12" align="end">
        <v-row dense :class="IpadSize ? 'mx-2' : ''">
          <v-col cols="12" md="3" sm="12" :align="MobileSize ? 'start' : IpadSize ? 'start' : 'center'">
            <div :class="MobileSize ? 'mt-4 ml-8':'mt-4'">
              วันที่เริ่ม - สิ้นสุด
            </div>
          </v-col>
          <v-col v-if="MobileSize" cols="1" class=""  align="center"></v-col>
          <v-col
            :cols="MobileSize ? '5' : '5'"
            md="4"
            sm="5"
            class="pr-6"
            v-show="numChange === '1'"
          >
            <v-dialog
              ref="menu1"
              v-model="menu1"
              persistent
              max-width="290px"
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="dateFormatted"
                  label=""
                  hint=""
                  persistent-hint
                  append-icon="mdi-calendar"
                  dense
                  outlined
                  class="mt-2"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="dateStart"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                @input="menu1 = false, dateEnd = ''"
              ></v-date-picker>
            </v-dialog>
          </v-col>
          <v-col v-if="!MobileSize" cols="1" md="1" sm="2" class="pt-5 pr-6"  align="center">-</v-col>
          <v-col v-if="MobileSize" cols="1" class="pt-5 pr-6"  align="center">-</v-col>
          <v-col
            :cols="MobileSize ? '5' : '5'"
            md="4"
            sm="5"
            class="pr-6"
            v-show="numChange === '1'"
          >
            <v-dialog
              v-model="menu2"
              persistent
              max-width="290px"
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="dateFormatted2"
                  label=""
                  hint=""
                  persistent-hint
                  append-icon="mdi-calendar"
                  dense
                  outlined
                  v-bind="attrs"
                  v-on="on"
                  class="mt-2"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="dateEnd"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                :min="dateStart"
                @input="menu2 = false"
                @change="filterDate"
              ></v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
</v-row>
<v-row :class="MobileSize ? 'ml-5 mr-0 mt-0 mb-0 pl-5 pr-0 pt-0 pb-0' : IpadSize ? 'ml-5 mr-0 mt-0 mb-0 pl-5 pr-0 pt-0 pb-0' : ''">
  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ml-3 mr-0 mt-0 mb-0 pl-5 pr-0 pt-0 pb-0' : IpadSize ? 'ml-5 mr-0 mt-0 mb-0 pl-0 pr-5 pt-0 pb-0' : ''" >
    <div class="d-flex align-center justify-end">
      <v-btn class="ma-2 mr-8" outlined color="success" v-on:click="exportTo()"><v-icon>mdi-file-excel</v-icon>EXCEL</v-btn>
    </div>
  </v-col>
</v-row>
    </v-card-text> -->
  </v-card>
</template>
<script>
// import $ from 'jquery'
import { Decode } from '@/services'
import axios from 'axios'
// import VueApexCharts from 'vue-apexcharts'
import { exportExcelForm } from '@/components/library/exportExcel/excelForm'
// Based on example from:
// https://datatables.net/forums/discussion/49457
export default {
  name: 'ApexCharts',
  // components: {
  //   apexchart: VueApexCharts
  // },
  data () {
    return {
      weekday: {},
      weekday2: {},
      weekdays: [
        {
          id: 2, name: '1 เดือน'
        },
        {
          id: 3, name: '6 เดือน'
        },
        {
          id: 4, name: '1 ปี'
        },
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      weekdays2: [
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      exportStatus: false,
      mode: '',
      type: [],
      types: [],
      modes: [],
      dateStart: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateEnd: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateFormatted2: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      menu1: false,
      menu2: false,
      numChange: '1',
      hideExport: false,
      firstLine: 'month',
      firstBar: 'month',
      firstPie: 'month',
      docOfBar: 'month',
      docOfLine: 'month',
      docOfPie: 'month',
      sumDocQT: '',
      sumDocRevenueQT: '',
      sumDocSO: '',
      sumDocRevenueSO: '',
      sumDocPR: '',
      sumDocRevenuePR: '',
      sumDocPO: '',
      sumDocRevenuePO: '',
      dataDateSumTrend: [],
      dataRevenueOfSumTrend: [],
      dataOfSumTrend: []

    }
  },
  created () {
    // this.getAllSumDocAdmin()
    // this.getAllSumTrendAdmin()
    // this.getAllSumBarQTAdmin()
    // this.getAllSumBarSOAdmin()
    // this.getAllSumBarPRAdmin()
    // this.getAllSumBarPOAdmin()
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
  },
  mounted () {
  },
  updated () {
  },
  destroyed () {
  },
  computed: {
    optionLine () {
      return {
        chartOptions: {
          legend: {
            position: 'bottom',
            horizontalAlign: 'center'
          },
          chart: {
            type: 'line',
            toolbar: {
              show: false
            }
          },
          colors: ['#77B6EA', '#545454'],
          dataLabels: {
            enabled: true,
            formatter: function (val) {
              return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          markers: {
            size: 1
          },
          stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
          },
          xaxis: {
            categories:
            this.docOfLine === 'day' ? this.dataDateSumTrend
              : this.docOfLine === 'month' ? this.dataDateSumTrend
                : this.docOfLine === 'quarter' ? ['มกราคม - มีนาคม', 'เมษายน - มิถุนายน', 'กรกฎาคม - กันยายน', 'ตุลาคม - ธันวาคม']
                  : this.docOfLine === 'halfyear' ? ['มกราคม - มิถุนายน', 'กรกฎาคม - ธันวาคม']
                    : this.docOfLine === 'year' ? ['มกรคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
                      : this.dataDateSumTrend
          },
          fill: {
            opacity: 1
          },
          tooltip: {
            y: {
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
              }
            }
          }
        }
      }
    },
    seriesLine () {
      return {
        series: [
          {
            name: 'QT',
            data: this.dataRevenueOfSumTrend,
            // data: [10, 20, 30, 40, 50, 60, 70],
            color: '#587fe3'
          }
        ]
      }
    },
    optionBar () {
      return {
        chartOptions: {
          legend: {
            position: 'bottom',
            horizontalAlign: 'center'
          },
          chart: {
            type: 'bar',
            toolbar: {
              show: false
            }
          },
          colors: ['#77B6EA', '#545454'],
          dataLabels: {
            enabled: true,
            formatter: function (val) {
              return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          markers: {
            size: 1
          },
          stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
          },
          xaxis: {
            // categories: this.dataDate
            categories:
            this.docOfLine === 'day' ? this.dataDateSumTrend
              : this.docOfLine === 'month' ? this.dataDateSumTrend
                : this.docOfLine === 'quarter' ? ['มกราคม - มีนาคม', 'เมษายน - มิถุนายน', 'กรกฎาคม - กันยายน', 'ตุลาคม - ธันวาคม']
                  : this.docOfLine === 'halfyear' ? ['มกราคม - มิถุนายน', 'กรกฎาคม - ธันวาคม']
                    : this.docOfLine === 'year' ? ['มกรคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
                      : this.dataDateSumTrend
            // title: {
            //   text: 'Month'
            // }
          },
          fill: {
            opacity: 1
          },
          tooltip: {
            y: {
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
              }
            }
          }
        }
      }
    },
    seriesBar () {
      return {
        series: [
          {
            name: 'QT',
            // data: this.dataRevenueBar,
            data: [10, 20, 30, 40, 50, 60, 70],
            color: '#587fe3'
          },
          {
            name: 'SO',
            // data: this.dataRevenueSOBar,
            data: [10, 20, 30, 40, 50, 60, 70],
            color: '#f87956'
          },
          {
            name: 'PR',
            // data: this.dataRevenuePRBar,
            data: [10, 20, 30, 40, 50, 60, 70],
            color: '#67b7dc'
          },
          {
            name: 'PO',
            // data: this.dataRevenuePOBar,
            data: [],
            color: '#7d72b9'
          }
        ]
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    },
    transition () {
      return this.$store.state.ModuleAdminManage.dashboardSummary.transaction
    },
    comparedSeller () {
      return this.$store.state.ModuleAdminManage.dashboardChart.comparedSeller.map((e, i) => {
        return {
          type: e.name === 'total' ? 'ผู้ใช้ทั้งหมด' : e.name === 'active' ? 'ผู้ใช้ที่มีการใช้งาน' : e.name === 'new' ? 'ผู้ใช้ใหม่' : '',
          month1: e.data[0] !== undefined ? e.data[0] : '',
          month2: e.data[1] !== undefined ? e.data[1] : '',
          month3: e.data[2] !== undefined ? e.data[2] : '',
          month4: e.data[3] !== undefined ? e.data[3] : '',
          month5: e.data[4] !== undefined ? e.data[4] : ''
        }
      })
    },
    comparedUser () {
      return this.$store.state.ModuleAdminManage.dashboardChart.comparedUser.map((e, i) => {
        return {
          type: e.name === 'total' ? 'ผู้ใช้ทั้งหมด' : e.name === 'active' ? 'ผู้ใช้ที่มีการใช้งาน' : e.name === 'new' ? 'ผู้ใช้ใหม่' : '',
          month1: e.data[0] !== undefined ? e.data[0] : '',
          month2: e.data[1] !== undefined ? e.data[1] : '',
          month3: e.data[2] !== undefined ? e.data[2] : '',
          month4: e.data[3] !== undefined ? e.data[3] : '',
          month5: e.data[4] !== undefined ? e.data[4] : ''
        }
      })
    },
    topOrder () {
      return this.$store.state.ModuleAdminManage.stateDashboardAdminAll.data.top_order.map((e, i) => {
        return {
          number: i + 1,
          seller_name: e.seller_name,
          total_order: e.total_order,
          percent: e.percent
        }
      })
    },
    topValues () {
      return this.$store.state.ModuleAdminManage.stateDashboardAdminAll.data.top_values.map((e, i) => {
        return {
          number: i + 1,
          seller_name: e.seller_name,
          total_price: e.total_price,
          percent: e.percent
        }
      })
    },
    sellerAndUser () {
      return this.$store.state.ModuleAdminManage.stateDashboardAdminAll.data
    },
    headerUser () {
      return [...['ประเภท'], ...this.$store.state.ModuleAdminManage.headerDBadmin.user]
    },
    headerSeller () {
      return [...['ประเภท'], ...this.$store.state.ModuleAdminManage.headerDBadmin.seller]
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardAdminMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'BackToMobile')
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      }
    },
    dateStart (val) {
      this.dateFormatted = this.formatDate(this.dateStart)
    },
    dateEnd (val) {
      this.dateFormatted2 = this.formatDate(this.dateEnd)
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async ExportSumTransaction () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_transactionv2`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Transaction_${dateCurrent.replaceAll(' ', '_')}.csv`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    async ExportSumTransactionJV () {
      this.$store.commit('openLoader')
      var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_transaction_jv`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', `Transaction_JV_${dateCurrent.replaceAll(' ', '_')}.xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function () {
        // handle error
        // console.log(error)
        this.$store.commit('closeLoader')
      })
    },
    // async getAllSumDocAdmin () {
    //   var data = {}
    //   await this.$store.dispatch('actionsGetAllSumDocAdmin', data)
    //   var response = await this.$store.state.ModuleAdminPanit.stateGetAllSumDocAdmin
    //   if (response.ok === 'y') {
    //     this.sumDocQT = response.query_result[0][0].sumDocumentQT
    //     this.sumDocRevenueQT = response.query_result[0][0].sumRevenueQT
    //     this.sumDocSO = response.query_result[0][1].sumDocumentQT
    //     this.sumDocRevenueSO = response.query_result[0][1].sumRevenueQT
    //     this.sumDocPO = response.query_result[0][2].sumDocumentQT
    //     this.sumDocRevenuePO = response.query_result[0][2].sumRevenueQT
    //     this.sumDocPR = response.query_result[0][3].sumDocumentQT
    //     this.sumDocRevenuePR = response.query_result[0][3].sumRevenueQT
    //   }
    // },
    // async getAllSumTrendAdmin (val) {
    //   this.dataDateSumTrend = []
    //   this.dataRevenueOfSumTrend = []
    //   this.docOfLine = val
    //   var data = {
    //     type: val === 'day' ? 'day' : val === 'month' ? 'month' : val === 'quarter' ? 'quarter' : val === 'halfyear' ? 'halfyear' : val === 'year' ? 'year' : 'month'
    //   }
    //   await this.$store.dispatch('actionsGetAllSumTrendAdmin', data)
    //   var response = await this.$store.state.ModuleAdminPanit.stateGetAllSumTrendAdmin
    //   if (response.ok === 'y') {
    //     // console.log('res_SumTrendAdmin----->', response.query_result) // QT
    //     this.dataOfSumTrend = response.query_result.data
    //     // console.log('res_SumTrendAdmin----->', val, this.dataOfSumTrend)
    //     if (this.dataOfSumTrend.length !== 0) {
    //       // console.log('res_SumTrendAdmin----->', val, this.dataOfSumTrend)
    //       for (const item of this.dataOfSumTrend) {
    //         // console.log('dataOfSumTrend----->', item)
    //         this.dataDateSumTrend.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
    //         if (item.Revenue === null || item.Revenue === undefined) {
    //           this.dataRevenueOfSumTrend.push(0)
    //         } else {
    //           this.dataRevenueOfSumTrend.push(item.Revenue)
    //         }
    //       }
    //     } else {
    //       this.dataDateSumTrend.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
    //       this.dataRevenueOfSumTrend.push(0)
    //       // console.log('res_SumTrendAdmin----->', this.dataRevenueOfSumTrend)
    //     }
    //     // console.log('res_SumTrendAdmin----->', this.dataRevenueOfSumTrend)
    //   }
    // },
    // async getAllSumBarQTAdmin (val) {
    //   this.docOfBar = val
    //   var data = {
    //     type: val === 'day' ? 'day' : val === 'month' ? 'month' : val === 'quarter' ? 'quarter' : val === 'halfyear' ? 'halfyear' : val === 'year' ? 'year' : 'month',
    //     customer: 'all'
    //   }
    //   await this.$store.dispatch('actionsGetAllSumBarQTAdmin', data)
    //   var response = await this.$store.state.ModuleAdminPanit.stateGetAllSumBarQTAdmin
    //   if (response.ok === 'y') {
    //     // console.log('res_SumBarQTAdmin----->', response.query_result) // QT
    //   }
    // },
    // async getAllSumBarSOAdmin (val) {
    //   this.docOfBar = val
    //   var data = {
    //     type: val === 'day' ? 'day' : val === 'month' ? 'month' : val === 'quarter' ? 'quarter' : val === 'halfyear' ? 'halfyear' : val === 'year' ? 'year' : 'month'
    //   }
    //   await this.$store.dispatch('actionsGetAllSumBarSOAdmin', data)
    //   var response = await this.$store.state.ModuleAdminPanit.stateGetAllSumBarSOAdmin
    //   if (response.ok === 'y') {
    //     // console.log('res_SumBarSOAdmin----->', response.query_result) // SO
    //   }
    // },
    // async getAllSumBarPRAdmin (val) {
    //   this.docOfBar = val
    //   var data = {
    //     type: val === 'day' ? 'day' : val === 'month' ? 'month' : val === 'quarter' ? 'quarter' : val === 'halfyear' ? 'halfyear' : val === 'year' ? 'year' : 'month'
    //   }
    //   await this.$store.dispatch('actionsGetAllSumBarPRAdmin', data)
    //   var response = await this.$store.state.ModuleAdminPanit.stateGetAllSumBarPRAdmin
    //   if (response.ok === 'y') {
    //     // console.log('res_SumBarPRAdmin----->', response.query_result) // PR
    //   }
    // },
    // async getAllSumBarPOAdmin (val) {
    //   this.docOfBar = val
    //   var data = {
    //     type: val === 'day' ? 'day' : val === 'month' ? 'month' : val === 'quarter' ? 'quarter' : val === 'halfyear' ? 'halfyear' : val === 'year' ? 'year' : 'month'
    //   }
    //   await this.$store.dispatch('actionsGetAllSumBarPOAdmin', data)
    //   var response = await this.$store.state.ModuleAdminPanit.stateGetAllSumBarPOAdmin
    //   if (response.ok === 'y') {
    //     // console.log('res_SumBarPOAdmin----->', response.query_result) // PO
    //   }
    // },
    formatNumberTooltip (value) {
      if (typeof value !== 'undefined') {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      return ''
    },
    formatNumber (number) {
      const formattedNumber = new Intl.NumberFormat('en-US', {
        notation: 'compact',
        compactDisplay: 'short'
      }).format(number)
      return formattedNumber
    },
    exportTo () {
      const ref = {
        startDate: this.dateFormatted,
        endDate: this.dateFormatted2
      }
      const data = {
        transition: this.transition,
        comparedSeller: this.comparedSeller,
        comparedUser: this.comparedUser,
        topOrder: this.topOrder,
        topValues: this.topValues,
        sellerAndUser: this.sellerAndUser,
        headerUser: this.headerUser,
        headerSeller: this.headerSeller
      }
      // console.log('Form', data)
      exportExcelForm(data, ref)
    },
    async numChangeData () {
      this.numChange = '0'
    },
    async filterDate () {
      // console.log('***********')
      // this.removeTable()
      this.exportStatus = await true
      var preStart = await this.afterDate(this.formatDate(this.dateStart))
      var preEnd = await this.afterDate(this.formatDate(this.dateEnd))
      // const start = await this.timeStamp(preStart)
      // const end = await this.timeStamp(preEnd)
      // const start = '31-2-2022'
      // const end = '31-2-2022'
      // const dataFilter = this.$store.getters.filterDate(start, end)
      // this.$store.state.ModuleShop.stateDashboard = []
      // this.$store.state.ModuleShop.stateDashboard = {
      //   data: dataFilter
      // }
      // const GetKey = dataFilter.map(name => {
      //   return {
      //     buyer_name: name.buyer_name
      //   }
      // })
      const data = await {
        start_date: preStart,
        end_date: preEnd,
        year: new Date().toISOString().slice(0, 4)
      }
      await this.$EventBus.$emit('age-changed', data)
      await this.reloadDataTable()
    },
    async filterOther () {
      const today = await new Date()
      // console.log('SDE', this.weekday.id)
      if (this.weekday.id === 2) {
        const data = await {
          text: 'one_month',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 3) {
        var today2 = await new Date()
        var end = await ''
        if (((today2.getMonth() + 1) - 6) < 0) {
          await today2.setMonth((today2.getMonth() + 1) - 6)
          end = await today2
        } else {
          end = await today.setMonth(today.getMonth() - 6)
        }
        const data = await {
          text: 'six_months',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 4) {
        var today3 = await new Date()
        var end3 = await ''
        if (((today3.getMonth() + 1) - 12) < 0) {
          await today3.setMonth((today3.getMonth()) - 12)
          end3 = await today3
        } else {
          end3 = await today.setMonth(today.getMonth() - 12)
        }
        const data = await {
          text: 'one_year',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end3).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        // const data = await {
        //   text: 'one_year',
        //   start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
        //   end: new Date(today.setFullYear((today.getFullYear() + 1) - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        // }
        await this.EventBusCall(data)
      } else {
        const data = await {
          text: 'all'
        }
        await this.EventBusCall(data)
        await this.filterOther2()
      }
      // console.log('filterOther', new Date().toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }))
    },
    async filltWeek2 () {
      // console.log('filltWeek2', this.weekday2.id)
      if (this.weekday2.id === 5) {
        const data = await {
          text: 'all'
        }
        this.numChange = await '1'
        await this.EventBusCall(data)
        await this.filterOther2()
      }
    },
    async EventBusCall (data) {
      await this.$EventBus.$emit('updateData', data)
    },
    async filterOther2 () {
      const num = await {
        number: '1'
      }
      await this.$EventBus.$emit('FuntionChange2', num)
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    parseDate (date) {
      // console.log('parseDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${day}-${month}-${year}`
    },
    afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${year}-${month}-${day}`
    },
    timeStamp (Dates) {
      var myDate = Dates.split('-')
      var newDate = new Date(myDate[2], myDate[1] - 1, myDate[0])
      return newDate.getTime()
    },
    randomColor () {
      const randomColor = Math.floor(Math.random() * 16777215).toString(16)
      return `#${randomColor}`
    }
  }
}
</script>
<style lang="css" scoped>
.QT .card {
  border-radius: 5px !important;
  background-color: #f5f5f5;
}
.custom-chip.v-chip--active {
  background-color: #757575 !important; /* สีเทาเข้ม */
  color: #ffffff !important; /* สีขาว */
}

::v-deep #example_filter {
  display: none;
}
::v-deep #example_paginate {
  display: none;
}
::v-deep #example_info {
    display: none;
}
::v-deep #example_wrapper {
  margin-left: 90%;
}
::v-deep #example_wrapper > hr {
  display: nones
}
::v-deep #example2_filter {
  display: none;
}
::v-deep #example2_paginate {
  display: none;
}
::v-deep #example2_info {
    display: none;
}
::v-deep #example2_wrapper {
  display: none;
}
::v-deep #example3_filter {
  display: none;
}
::v-deep #example3_paginate {
  display: none;
}
::v-deep #example3_info {
    display: none;
}
::v-deep #example3_wrapper {
  display: none;
}
::v-deep #example4_filter {
  display: none;
}
::v-deep #example4_paginate {
  display: none;
}
::v-deep #example4_info {
    display: none;
}
::v-deep #example4_wrapper {
  display: none;
}
::v-deep #example5_filter {
  display: none;
}
::v-deep #example5_paginate {
  display: none;
}
::v-deep #example5_info {
    display: none;
}
::v-deep #example5_wrapper {
  display: none;
}
::v-deep #example6_filter {
  display: none;
}
::v-deep #example6_paginate {
  display: none;
}
::v-deep #example6_info {
    display: none;
}
::v-deep #example6_wrapper {
  display: none;
}
::v-deep #example7_filter {
  display: none;
}
::v-deep #example7_paginate {
  display: none;
}
::v-deep #example7_info {
    display: none;
}
::v-deep #example7_wrapper {
  display: none;
}

::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
}
</style>
