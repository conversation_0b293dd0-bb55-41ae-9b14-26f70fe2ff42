<template>
  <div>
    <v-container v-if="dataCoupons.length > 0 && dataRole == 'ext_buyer'" class="d-flex justify-center" :class="MobileSize ? 'mb-4 mt-0 pa-0' : 'mt-12'" :style="IpadSize || MobileSize || IpadProSize ? 'width: 100%' : 'width: 1250px !important'">
      <v-col cols="12" class="p-0">
        <v-row>
          <v-col cols="12" md="12" style="position: relative" class="p-0 backIMG">
            <div class="d-flex pa-2">
              <span>
                <span style="font-size: large;"><b>คูปองลดสูงสุด </b></span>
                <span style="font-size: x-large; color: #f84340;"><b> 5,000 .-</b></span>
              </span>
              <v-spacer></v-spacer>
              <v-btn @click="goToPageCoupon()" style="color: #fff; background-image: linear-gradient(to left, #b67ee8, #eb7693); border: 3px solid #fff" rounded outlined color="success">
                <span style="color: #fff;" class="mr-1">เก็บคูปอง</span>
                <v-icon style="color: #fff;">mdi-chevron-right</v-icon>
              </v-btn>
            </div>
            <!-- <v-row justify="end" no-gutters > <span @click="goToPageCoupon()" style="font-size: 16px; color: #fff;" class="couponAll" >ดูทั้งหมด</span></v-row> -->
            <div v-if="!MobileSize" :class=" MobileSize? 'pa-1 pt-1':'pa-4 pt-6'">
              <section>
                <vue-horizontal-list :items="dataCoupons"
                  active-class="success"
                  show-arrows
                  center-active
                  :options="optionsCard">
                  <template v-slot:nav-prev>
                    <v-icon color="#269AFD" large>mdi-chevron-left</v-icon>
                  </template>

                  <template v-slot:nav-next>
                    <v-icon color="#269AFD" large>mdi-chevron-right</v-icon>
                  </template>

                  <template v-slot:default="{ item }">
                    <v-col class="mx-4">
                      <v-col :class="item.coupon_type === 'free_shipping' ? 'couponIMGDeskFree' : 'couponIMGDeskDis'" style="margin-bottom: 1.5vw;" :style="IpadSize ? 'width: 38vw;' : IpadProSize ? 'width: 29vw;' : 'width: 20vw;'">
                        <v-col cols="12" md="12" class="pt-0" :class="IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'" style="max-width: 320px !important;">
                          <v-row no-gutters>
                            <v-col class=" pl-5 pt-1">
                              <v-row>
                                <v-col cols="12" align="start">
                                  <!-- <span class="mb-5" style="color: #269AFD; font-size: large; white-space: nowrap;"><b>{{item.coupon_name}}</b></span><br> -->
                                  <v-tooltip top>
                                    <template v-slot:activator="{ on, attrs }">
                                      <span style="color: #269AFD; font-size: large; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substring(item.coupon_name) }}</b></span>
                                    </template>
                                    <span>{{ item.coupon_name }}</span>
                                  </v-tooltip><br>
                                  <span>ขั้นต่ำ {{item.spend_minimum}} .- </span>
                                  <span v-if="item.discount_maximum !== null"> ไม่เกิน {{item.discount_maximum}} .-</span><br>
                                  <span style="color: #636363;" v-if="item.use_enddate !== ''">ใช้ได้ถึง {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                                  <span style="color: #636363;" v-else>ไม่มีวันหมดอายุใช้งาน</span><br>
                                  <div class="mt-2" style="display: grid;">
                                    <v-btn v-if="item.is_collected === 'N'" color="#FEC81C" rounded style="color: #fff;" @click="CollectCoupon(item)"><b>เก็บ</b></v-btn>
                                    <v-btn v-else disabled color="#F56E22" style="color:white;" rounded>เก็บแล้ว</v-btn>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-col>
                    </v-col>
                  </template>
                </vue-horizontal-list>
              </section>
            </div>
            <div v-else :class=" MobileSize? 'pa-1 pt-1':'pa-4 pt-6'">
              <section>
                <vue-horizontal-list :items="dataCoupons"
                  active-class="success"
                  show-arrows
                  center-active
                  :options="optionsCard">
                  <template v-slot:nav-prev>
                    <v-icon color="#269AFD" large>mdi-chevron-left</v-icon>
                  </template>

                  <template v-slot:nav-next>
                    <v-icon color="#269AFD" large>mdi-chevron-right</v-icon>
                  </template>

                  <template v-slot:default="{ item }">
                    <v-col class="mx-4">
                      <v-col :class="item.coupon_type === 'free_shipping' ? 'couponIMGDeskFree' : 'couponIMGDeskDis'" style="margin-bottom: 1.5vw; width: 76vw;">
                        <v-col cols="12" md="12" class="pt-0" :class="IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'" style="max-width: 320px !important;">
                          <v-row no-gutters>
                            <v-col class=" pl-5 pt-1">
                              <v-row>
                                <v-col cols="12" align="start">
                                  <!-- <span class="mb-5" style="color: #269AFD; font-size: large; white-space: nowrap;"><b>{{item.coupon_name}}</b></span><br> -->
                                  <v-tooltip top>
                                    <template v-slot:activator="{ on, attrs }">
                                      <span style="color: #269AFD; font-size: large; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substring(item.coupon_name) }}</b></span>
                                    </template>
                                    <span>{{ item.coupon_name }}</span>
                                  </v-tooltip><br>
                                  <span>ขั้นต่ำ {{item.spend_minimum}} .- </span>
                                  <span v-if="item.discount_maximum !== null"> ไม่เกิน {{item.discount_maximum}} .-</span><br>
                                  <span style="color: #636363;" v-if="item.use_enddate !== -1">ใช้ได้ถึง {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                                  <span style="color: #636363;" v-else>ไม่มีวันหมดอายุใช้งาน</span><br>
                                  <div class="mt-2" style="display: grid;">
                                    <v-btn v-if="item.is_collected === 'N'" color="#FEC81C" rounded style="color: #fff;" @click="CollectCoupon(item)"><b>เก็บ</b></v-btn>
                                    <v-btn v-else disabled color="#F56E22" style="color:white;" rounded>เก็บแล้ว</v-btn>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-col>
                    </v-col>
                  </template>
                </vue-horizontal-list>
              </section>
            </div>
          </v-col>
        </v-row>
      </v-col>

      <dialogAllCoupon
      :visible="dialogAllCoupon"
      :dataCoupons="dataCoupons"
      @close="dialogAllCoupon = false"
      @btn1="ShowCouponcondition"
      @btn2="CollectCoupon"
      />

      <dialogConditionCoupon
      :visible="dialogConditionCoupon"
      :conditionData="conditionData"
      @close="closeDialogConditionCoupon"
      />
    </v-container >
  </div>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'

import { Decode } from '@/services'
export default {
  components: {
    VueHorizontalList,
    dialogAllCoupon: () => import('./ModalAllCoupon.vue'),
    dialogConditionCoupon: () => import('./ModalCouponCondition.vue')
  },
  data () {
    return {
      dataRole: '',
      cusID: '',
      companyID: '',
      sellerShopID: '',

      dataCoupons: [],
      RoleUser: '',
      SaleVendor: false,
      optionsCard: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 2 },
          { start: 821, end: 1024, size: 3 },
          { start: 1200, end: 1300, size: 4 },
          { size: 3 }
        ],
        list: {
          windowed: 1200,

          padding: 0
        },
        item: {
          class: '',
          padding: 0
        },
        position: {
          start: 0
        }
      },

      dialogAllCoupon: false,

      dialogConditionCoupon: false,
      conditionData: {}
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  async created () {
    // การแสดงคูปองในหน้า Home จะมี แค่ ext_buyer ที่สามารถเห็นได้
    // ถ้าใครมาทำต่อ ดัก role อื่นๆเองนะคะ

    // check login
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user !== undefined) {
        this.isLogin = true
        if (oneData.user.shared_token !== undefined && oneData.user.shared_token !== null && oneData.user.shared_token !== '') {
          this.onechatToken = oneData.user.shared_token
        } else {
          this.onechatToken = ''
        }
      }

      // set ค่าต่างๆ
      this.dataRole = localStorage.getItem('roleUser') !== null ? JSON.parse(localStorage.getItem('roleUser')).role : 'ext_buyer'
      this.cusID = localStorage.getItem('partner_id') !== null ? parseInt(localStorage.getItem('partner_id')) : '-1'
      this.companyID = localStorage.getItem('SetRowCompany') !== null ? JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany'))).company.company_id : '-1'
      this.sellerShopID = localStorage.getItem('shopID') !== null ? JSON.parse(localStorage.getItem('shopID')) : '-1'
      this.getCoupon()
    }
  },
  methods: {
    // async getCoupon () {
    //   var data = {
    //     role_user: this.dataRole,
    //     company_id: this.companyID,
    //     customer_id: this.cusID
    //   }
    //   await this.$store.dispatch('actionsGetHomeCoupon', data)
    //   var response = await this.$store.state.ModuleHompage.stateGetHomeCoupon
    //   this.dataCoupons = response.data.coupon
    // },
    async getCoupon (typeCoupon) {
      this.$store.commit('openLoader')
      var data = {
        role_user: this.dataRole,
        company_id: this.companyID,
        customer_id: this.cusID,
        type: typeCoupon,
        shop_id: -1,
        orderBy: 'uncollected_first',
        is_collected: 'all',
        limit: '',
        page: ''
      }
      await this.$store.dispatch('actionsDetailServiceCoupon', data)
      var res = this.$store.state.ModuleHompage.stateDetailServiceCoupon
      if (res.code === 200) {
        this.dataCoupons = res.data.coupon
        // this.statusCollect = res.data.is_collect_all
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    async CollectCoupon (item) {
      // console.log('CollectCoupon')
      var data = {
        role_user: this.dataRole,
        customer_id: this.cusID,
        company_id: this.companyID,
        shop_id: this.dataRole !== 'ext_buyer' ? this.sellerShopID : '-1',
        coupon_id: item.id
      }
      await this.$store.dispatch('actionCollectCoupon', data)
      var resGetCoupons = this.$store.state.ModuleShop.stateCollectCoupon
      if (resGetCoupons.result === 'Success') {
        this.$swal.fire({
          icon: 'success',
          text: 'เก็บคูปองสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.getCoupon()
      } else {
        if (resGetCoupons.message === 'This user is Unauthorized') {
          if (localStorage.getItem('oneData') === null) {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาเข้าสู่ระบบ',
              showConfirmButton: false,
              timer: 1500
            })
            this.$router.push({ path: '/Login' }).catch(() => {})
          } else {
            this.$EventBus.$emit('refreshToken')
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: resGetCoupons.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    // getAllCoupon () {
    //   this.dialogAllCoupon = true
    // },
    goToPageCoupon () {
      if (this.MobileSize) {
        this.$router.push({ path: '/DetailServiceCouponMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/DetailServiceCoupon' }).catch(() => {})
      }
    },
    ShowCouponcondition (item) {
      this.conditionData = item
      this.dialogConditionCoupon = true
    },
    closeDialogConditionCoupon () {
      this.dialogConditionCoupon = false
    },
    substring (data) {
      if (this.MobileSize || this.IpadSize || this.IpadProSize) {
        return data.length > 15 ? data.substring(0, 15) + '...' : data
      } else {
        return data.length > 20 ? data.substring(0, 20) + '...' : data
      }
    }
  }
}
</script>

<style scoped>
.couponAll {
  cursor: pointer;
}
.couponAll:hover {
  transform: scale(1.02) !important;
}
.progress-gradient {
width: 100%;
height: 100%;
border-radius: 48px;
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.vProgressLinearDesk {
  max-width: 4.5vw;
}
.vProgressLinearIped {
  max-width: 8vw;
}
.vProgressLinearMobile {
  max-width: 23vw;
}

.backIMG{
  /* background-image: url('../../../assets/ConponNGC/shopConpon/background.png'); */
  background-image: url('../../../assets/ConponNGC/shopConpon/bgHome2.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  border-radius: .5vw;
  /* width: 100%; */
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
}
.couponIMGDesk{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  padding: 1%;
  /* width: 100%; */
}
.couponIMGDeskDis{
  background-image: url('../../../assets/ConponNGC/serviceCoupon/bgHomeDis.png');
}
.couponIMGDeskFree{
  background-image: url('../../../assets/ConponNGC/serviceCoupon/bgHomeFree.png');
}
.couponIMGDeskDis,
.couponIMGDeskFree {
  background-size: cover; /* ทำให้ภาพเต็มพื้นที่ */
  background-position: center; /* จัดภาพให้อยู่ตรงกลาง */
  background-repeat: no-repeat; /* ไม่ให้ภาพซ้ำ */
  width: 100%; /* เต็มความกว้าง col */
  height: 100%; /* เต็มความสูง col */
}
/* ::v-deep .vhl-item {
  width: auto !important;
} */
</style>
