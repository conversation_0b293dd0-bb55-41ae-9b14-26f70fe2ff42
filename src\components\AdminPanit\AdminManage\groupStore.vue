<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายชื่อประเภทร้านค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายชื่อประเภทร้านค้า</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากรายชื่อประเภทร้านค้า" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row dense class="d-flex">
              <v-col cols="12" md="auto" sm="auto" class="mr-auto" style="align-items: center; display: flex;">
                <span style="font-size: 18px; color: #333333; font-weight: 600;" v-if="listBusinessAdmin.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อประเภทร้านค้าทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; color: #333333; font-weight: 600;" v-else-if="listBusinessAdmin.length !== 0 && (MobileSize || IpadSize)">รายชื่อประเภทร้านค้าทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
              <v-col cols="12" md="auto" sm="auto" class="ml-auto" style="justify-content: end; display: flex;">
                <v-btn
                rounded
                color="#27ab9c"
                dark
                :block="MobileSize ? true : false"
                @click="routePage('create')"
              >
                เพิ่มประเภทร้านค้า
              </v-btn>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="currentGroupItems"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อผู้ใช้งานของระบบ"
                no-data-text="ไม่มีชื่อผู้ใช้งานของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.index`]="{ item, index }">
                    {{ index + 1 }}
                  </template>
                  <template v-slot:[`item.group_name`]="{ item }">
                    {{ item.group_name }}
                  </template>
                  <template v-slot:[`item.group_shop_media_path`]="{ item }">
                    <div  style="width: 60px;">
                    <v-img :src="item.group_shop_media_path"  width="56px"/>
                  </div>
                  </template>
                  <!-- <template v-slot:[`item.created_at`]="{ item }">
                    {{ item.created_at }}
                  </template> -->
                  <template v-slot:[`item.openStatus`]="{ item }">
                    <div style="margin: -24px 0px 0px 14px;">
                    <v-switch
                      v-model="item.statusOpen"
                      color="success"
                      :value="true"
                      hide-details
                      @change="switchOpen(item)"
                    ></v-switch>
                  </div>
                  </template>
                  <template v-slot:[`item.product_status`]="{ item }">
                    <v-chip v-if="item.product_status === 'all'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">All</v-chip>
                    <v-chip v-else-if="item.product_status === 'custom'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Custom</v-chip>
                  </template>
                  <template v-slot:[`item.manage`]="{ item }">
                  <v-btn icon @click="viewDetail(item)">
                     <v-icon>mdi-eye-outline</v-icon>
                  </v-btn>
                  <v-btn icon @click="routePage('edit', item)">
                     <v-icon>mdi-pencil-outline</v-icon>
                  </v-btn>
                  <v-btn icon @click="confirmDelete(item)">
                     <v-icon>mdi-trash-can-outline</v-icon>
                  </v-btn>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- <CreateAdminModal ref="CreateAdminModal" />
      <DetailAdminModal ref="DetailAdminModal" /> -->
      <!-- <CreateCreateGroupShopModal ref="modalCreateGroupShop"/> -->
    </v-card>
  </v-container>
</template>
<script>
import { Decode } from '@/services'
export default {
  components: {
    // CreateCreateGroupShopModal: () => import('@/components/AdminPanit/AdminManage/CreateGroupStoreModal')
    // DetailAdminModal: () => import('@/components/AdminPanit/AdminManage/DetailAdminPanitModal')
    // EditAdminModal: () => import('@/components/AdminPanit/AdminManage/EditAdminPanitModal')
  },
  data () {
    return {
      userId: '',
      activeStatus: false,
      search: '',
      listBusinessAdmin: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      headers: [
        { text: 'ลำดับ', value: 'index', width: '10%', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'โลโก้', value: 'group_shop_media_path', width: '10%', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อประเภทร้านค้า', value: 'group_name', width: '40%', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ปิด/เปิด', value: 'openStatus', width: '10%', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการสินค้า', value: 'product_status', width: '10%', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manage', width: '20%', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      currentGroupItems: []
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/groupStoreMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'groupStore')
        this.$router.push({ path: '/groupStore' }).catch(() => {})
      }
    }
  },
  created () {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.userId = oneData.user.user_id
    this.$EventBus.$emit('changeNavAdmin')
    this.$EventBus.$on('callModalCreateGroupStore', this.getGroupStore)
    // this.$EventBus.$on('createAdminPanitSuccess', this.getShopData)
    // this.$EventBus.$on('listBusinessAdmindeleteAdminPanitSuccess', this.getShopData)
    // this.$EventBus.$on('editAdminPanitSuccess', this.getShopData)
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
      this.getGroupStore()
    //   this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    window.scrollTo(0, 0)
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  methods: {
    async switchOpen (item) {
      // console.log('List', item)
      if (item.custom_group_status === 'inactive') {
        var delVal = {
          group_id: item.id,
          group_status: item.group_status === 'active' ? 'inactive' : 'active',
          user_id: this.userId
        }
        // console.log('delVal', delVal)
        await this.$store.dispatch('actionsDeleteGroupSeller', delVal)
        var response = await this.$store.state.ModuleHompage.stateDeleteGroupSeller
        // console.log('fffhh', response)
        if (response.result === 'SUCCESS') {
          await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
          await this.getGroupStore()
        } else {
        }
      } else if (item.custom_group_status === 'active') {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ประเภทร้านค้านี้ใช้งานอยู่' })
        this.$nextTick(() => {
          item.statusOpen = !item.statusOpen
        })
      }
    },
    async openEditModal (item) {
      // var delVal = await {
      //   group_id: item,
      //   key_word: '',
      //   seller_shop_id: ''
      // }
      // await this.$store.dispatch('actionsDetailGroupSeller', delVal)
      // var response = await this.$store.state.ModuleHompage.stateDetailGroupSeller
      // if (response.result === 'SUCCESS') {
      //   this.$refs.modalCreateGroupShop.manageGroupStoreModal('openEditModal', response.data)
      // }
      this.$refs.modalCreateGroupShop.manageGroupStoreModal('openEditModal', 'NO')
      // console.log('xcvxcv', response)
    },
    routePage (status, item = null) {
      localStorage.setItem('status_managegroup', status)
      // console.log(status)
      if (status === 'create') {
        if (this.MobileSize) {
          this.$router.push({ path: `/manageGroupStoreMobile/${status}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/manageGroupStore/${status}` }).catch(() => {})
        }
      } else {
        if (this.MobileSize) {
          this.$router.push({ path: `/manageGroupStoreMobile/${status}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/manageGroupStore/${status}` }).catch(() => {})
        }
        const items = {
          data: item
        }
        localStorage.setItem('itemGroupStore', JSON.stringify(items))
      }
    },
    viewDetail (item) {
      const GroupShop = item.group_name.replace(/\s/g, '-')
      this.$router
        .push({ path: `/GroupShoppage/${GroupShop}-${item.id}` })
        .catch(() => {})
    },
    confirmDelete (item) {
      if (item.custom_group_status === 'inactive') {
        const msgText = 'คุณได้ทำการลบประเภทร้านค้า ' + item.group_name + ' คุณต้องการทำรายการนี้ใช่ หรือไม่'
        this.$swal.fire({
          icon: 'warning',
          text: msgText,
          showCancelButton: true,
          confirmButtonText: 'ตกลง',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.deleteGropShop(item)
          } else if (result.dismiss === this.$swal.DismissReason.cancel) {
          }
        }).catch(() => {
        })
      } else if (item.custom_group_status === 'active') {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ประเภทร้านค้านี้ใช้งานอยู่' })
      }
    },
    async deleteGropShop (item) {
      var delVal = {
        group_id: item.id,
        group_status: null,
        user_id: this.userId
      }
      await this.$store.dispatch('actionsDeleteGroupSeller', delVal)
      var response = await this.$store.state.ModuleHompage.stateDeleteGroupSeller
      // console.log('fffhh', response)
      if (response.result === 'SUCCESS') {
        await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
        await this.getGroupStore()
      } else {
      }
    },
    async getGroupStore () {
      var delVal = {
        user_id: this.userId
      }
      await this.$store.dispatch('actionsGroupStoreList', delVal)
      var response = await this.$store.state.ModuleHompage.stateGroupStoreList
      if (response.result === 'SUCCESS') {
        this.currentGroupItems = response.data.group_seller_data.map(e => {
          return {
            ...e,
            statusOpen: e.group_status === 'active'
          }
        })
      } else {
      }
      // console.log('fffff', this.currentGroupItems)
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    // async AuthorityUser () {
    //   await this.$store.dispatch('actionsAuthorityUser')
    //   var response = await this.$store.state.ModuleUser.stateAuthorityUser
    //   if (response.message === 'Get user detail success') {
    //     if (response.data.current_role_user.super_admin_platform === true) {
    //       this.isSuperAdmin = true
    //     } else {
    //       this.isSuperAdmin = false
    //     }
    //   }
    // },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsUserInSystem')
      var response = await this.$store.state.ModuleAdminManage.stateUserInSystem
      // console.log('list business ====>', response)
      if (response.message === 'Data List User') {
        this.$store.commit('closeLoader')
        this.listBusinessAdmin = [...response.data]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    }
  }
}
</script>

<style scoped>
.v-data-table /deep/ .v-data-footer {
font-size: 0.62rem;
}
</style>
