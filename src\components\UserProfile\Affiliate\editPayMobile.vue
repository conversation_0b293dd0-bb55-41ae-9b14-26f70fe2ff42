<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
        ตั้งค่าการชำระเงิน
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        ตั้งค่าการชำระเงิน
      </v-card-title>

      <v-divider class="my-4"></v-divider>

      <div class="px-6">
        <span class="text-start" style="font-size: 20px; font-weight: 700;">ประเภทบัญชี</span>

        <v-form ref="FormbuyerDetailType" :lazy-validation="lazy">
          <v-row>
            <v-col cols="12" class="mt-4 reduce-spacing">
              <span class="labelInputSize" style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ประเภทบัญชี <span style="color: red;">*</span></span>
              <v-select :items="accountTypes" item-text="text" item-value="value" placeholder="เลือกประเภทบัญชี" v-model="account_type" :disabled="!isEditable" outlined dense :rules="Rules.account_type"></v-select>
            </v-col>
          </v-row>
        </v-form>

        <v-divider class="my-4"></v-divider>

        <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลการชำระเงิน</span>

        <v-form ref="FormbuyerDetailPay" :lazy-validation="lazy">
          <v-row dense class="mt-4 reduce-spacing">
            <v-col cols="12">
              <v-row dense>
                <v-col cols="12" md="6" sm="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อบัญชี <span style="color: red;">*</span></span>
                  <v-text-field class="input-text" placeholder="ระบุชื่อบัญชี" v-model="bank_username" :disabled="!isEditable" outlined dense :rules="Rules.bank_username"></v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อธนาคาร <span style="color: red;">*</span></span>
                  <v-select :items="itemsBank" item-text="name" item-value="code" :return-object="true" placeholder="เลือกชื่อธนาคาร" v-model="selectedBank" :disabled="!isEditable" outlined dense :rules="Rules.selectedBank"></v-select>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="6" sm="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อสาขาธนาคาร <span style="color: red;">*</span></span>
                  <v-text-field class="input-text" placeholder="ระบุชื่อสาขาธนาคาร" v-model="bank_branch" :disabled="!isEditable" outlined dense :rules="Rules.bank_branch"></v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมายเลขบัญชีธนาคาร <span style="color: red;">*</span></span>
                  <v-text-field class="input-text" placeholder="ระบุหมายเลขบัญชีธนาคาร" v-model="bank_no" :disabled="!isEditable" outlined dense :rules="Rules.bank_no"></v-text-field>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="6" sm="6" style="display: flex; align-items: center;">
                  <span style="padding-right: 10px; font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">รูปหน้าบัญชีธนาคาร <span style="color: red;">*</span></span>
                  <v-btn @click="triggerFileInput()" :disabled="!isEditable" color="#27AB9C" class="white--text rounded-button">อัปโหลด</v-btn>
                  <input type="file" ref="fileInput" @change="handleFileUpload($event)" style="display: none;">
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <div v-if="bookbankImageUrl" class="mt-2" style="display: flex; justify-content: space-evenly;">
                    <v-card class="d-flex justify-center align-center mb-6" style="max-width: 300px; max-height: 300px; overflow: hidden;">
                      <img :src="bookbankImageUrl" style="width: 100%; height: 100%; object-fit: contain;" @click="viewImageBookbank">
                    </v-card>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-form>
        <v-divider class="my-4"></v-divider>
        <div>
          <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลภาษี</span>
          <v-form ref="FormbuyerDetailTax" :lazy-validation="lazy">
            <v-row>
              <v-col cols="12" class="mt-4 reduce-spacing">
                <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมายเลขประจำตัวผู้เสียภาษี <span style="color: red;">*</span></span>
                <v-text-field class="input-text" placeholder="ระบุเลขประจำตัวผู้เสียภาษี" v-model="national_id" :maxLength="13" counter="13"  @input="validateTaxID()" :disabled="!isEditable" outlined dense :rules="Rules.national_id"></v-text-field>
              </v-col>
              <!-- <v-col cols="12" style="display: flex; align-items: center;">
                <span style="padding-right: 10px;">รูปบัตรประจำตัวประชาชน <span style="color: red;">*</span></span>
                <v-btn @click="triggerFileInputTax()" :disabled="!isEditable" color="#27AB9C" class="white--text rounded-button">อัปโหลด</v-btn>
                <input type="file" ref="fileInputTax" @change="handleFileUploadTax($event)" style="display: none;">
              </v-col>
              <v-col cols="12">
                <div v-if="taxImageUrl" class="mt-2" style="display: flex; justify-content: space-evenly;">
                  <v-card class="d-flex justify-center align-center mb-6" style="max-width: 300px; max-height: 300px; overflow: hidden;">
                    <img :src="taxImageUrl" style="width: 100%; height: 100%; object-fit: contain;" @click="viewImageTax">
                  </v-card>
                </div>
              </v-col> -->
            </v-row>
          </v-form>
        </div>
        <v-divider class="my-6"></v-divider>
        <div>
          <span class="text-start" style="font-size: 20px; font-weight: 700;">ที่อยู่ตามบัตรประชาชน</span>
          <v-form ref="FormbuyerDetailAddress" :lazy-validation="lazy">
            <v-row dense class="mt-4 reduce-spacing">
              <v-col cols="12">
                <div class="borderd-content">
                  <!-- แถวที่ 1 -->
                  <v-row dense>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">เลขที่ <span style="color: red;"> *</span></span>
                      <v-text-field v-model="houseNo" placeholder="ระบุเลขที่อยู่" dense outlined :disabled="!isEditable" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ห้องเลขที่</span>
                      <v-text-field v-model="roomNo" placeholder="ระบุเลขห้อง" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชั้นที่</span>
                      <v-text-field v-model="floor" placeholder="ระบุชั้น" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 2 -->
                  <v-row dense>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">อาคาร</span>
                      <v-text-field v-model="buildingName" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมู่บ้าน</span>
                      <v-text-field v-model="mooBan" placeholder="ชื่อหมู่บ้าน" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมู่ที่</span>
                      <v-text-field v-model="mooNo" placeholder="ระบุหมู่" dense outlined :disabled="!isEditable" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 3 -->
                  <v-row dense>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ตรอก/ซอย</span>
                      <v-text-field v-model="soi" placeholder="ระบุตรอก,ซอย" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">แยก</span>
                      <v-text-field v-model="yaek" placeholder="ระบุแยก" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ถนน</span>
                      <v-text-field v-model="street" placeholder="ระบุชื่อถนน" dense outlined :disabled="!isEditable" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 4 -->
                  <v-row dense>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">แขวง/ตำบล<span style="color: red;"> *</span></span>
                      <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" label=""  :disabled="!isEditable" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                      <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">เขต/อำเภอ<span style="color: red;"> *</span></span>
                      <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" :disabled="!isEditable" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                      <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                  </v-row>
                  <!-- แถวที่ 5 -->
                  <v-row dense>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">จังหวัด<span style="color: red;"> *</span></span>
                      <addressinput-province label="" :disabled="!isEditable" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                      <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">รหัสไปรษณีย์<span style="color: red;"> *</span></span>
                      <addressinput-zipcode label="" :disabled="!isEditable" v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                      <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </v-form>
          <v-divider class="my-4"></v-divider>
        </div>
        <v-card-actions>
          <v-btn v-if="update" class="px-5" color="#27AB9C" outlined @click="cancelUpdate()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn v-if="!update" class="px-5 white--text" color="#27AB9C" @click="updatePay()">แก้ไข</v-btn>
          <v-btn v-if="update" class="px-5 white--text" color="#27AB9C" @click="saveUpdate()">บันทึก</v-btn>
        </v-card-actions>

      </div>
    </v-card>

    <v-dialog v-model="dialogBookbank" max-width="500px">
      <v-card>
        <v-card-title class="headline" style="justify-content: center;">รูปบัญชีธนาคาร</v-card-title>
        <v-card-text class="d-flex justify-center">
          <img :src="bookbankImageUrl" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogBookbank = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogTax" max-width="500px">
      <v-card>
        <v-card-title class="headline" style="justify-content: center;">รูปบัตรประจำตัวประชาชน</v-card-title>
        <v-card-text class="d-flex justify-center">
          <img :src="taxImageUrl" alt="Full Bank Book Image" style="width: 100%;">
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" class="white--text rounded-button" @click="dialogTax = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import Vue from 'vue'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode } from '@/services'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      lazy: false,
      update: false,
      isEditable: false,
      dialogBookbank: false,
      dialogTax: false,
      accountTypes: [
        { text: 'ออมทรัพย์', value: 'savings' },
        { text: 'ฝากประจำ', value: 'current' }
      ],
      itemsBank: [],
      account_type: '',
      bank_username: '',
      selectedBank: null,
      bank_name: '',
      bank_code: '',
      bank_branch: '',
      bank_no: '',
      national_id: '',
      bookbankImage: '',
      bookbankImageUrl: null,
      taxImage: '',
      taxImageUrl: null,
      // address
      houseNo: '',
      roomNo: '',
      floor: '',
      buildingName: '',
      mooBan: '',
      mooNo: '',
      soi: '',
      yaek: '',
      street: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        account_type: [
          v => !!v || 'กรุณาเลือกประเภทบัญชี'
        ],
        bank_username: [
          v => !!v || 'กรุณาระบุชื่อบัญชี'
        ],
        selectedBank: [
          v => !!v || 'กรุณาเลือกชื่อธนาคาร'
        ],
        bank_branch: [
          v => !!v || 'กรุณาระบุชื่อสาขาธนาคาร'
        ],
        bank_no: [
          v => !!v || 'กรุณาระบุหมายเลขบัญชีธนาคาร',
          v => /^[0-9]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข'
        ],
        national_id: [
          v => !!v || 'กรุณาระบุเลขประจำตัวผู้เสียภาษี',
          v => (/^\d+$/.test(v) && v.length === 13) || 'เลขประจำตัวผู้เสียภาษีต้องมี 13 หลักและเป็นตัวเลขเท่านั้น',
          v => this.validNationalID(v) || 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
        ],
        // address
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่',
          v => v.charAt(0) !== '-' || 'กรุณากรอกข้อมูลให้ถูกต้อง',
          v => (v.split('').filter(char => char === '-').length <= 1) || 'ระบุข้อมูลไม่ถูกต้อง',
          v => (/^[-0-9,-/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9,-/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        maxText: [
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ]
      }
    }
  },
  created () {
    this.checkConsent()
    this.$EventBus.$emit('changeNavAccount')
    this.showDetailBuyer()
    this.AffiliateListBank()
  },
  watch: {
    selectedBank (newVal) {
      if (newVal) {
        this.bank_name = newVal.name
        this.bank_code = newVal.code
      } else {
        this.bank_name = ''
        this.bank_code = ''
      }
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      return this.$vuetify.breakpoint.xs
    }
  },
  methods: {
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateTaxID () {
      if (this.validNationalID(this.national_id)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    async AffiliateListBank () {
      await this.$store.dispatch('actionsAffiliateListBank')
      const response = await this.$store.state.ModuleAffiliate.stateAffiliateListBank
      if (response.result === 'SUCCESS') {
        this.itemsBank = await [...response.data]
      }
    },
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    updatePay () {
      this.update = true
      this.isEditable = true
    },
    async cancelUpdate () {
      this.update = false
      this.isEditable = false
      await this.showDetailBuyer()
    },
    async saveUpdate () {
      const detailTypeValid = this.$refs.FormbuyerDetailType.validate()
      const detailPayValid = this.$refs.FormbuyerDetailPay.validate()
      const detailTaxValid = this.$refs.FormbuyerDetailTax.validate()

      if (!detailTypeValid || !detailPayValid || !detailTaxValid) {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณากรอกข้อมูลให้ครบถ้วน',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        return
      }

      this.update = false
      this.isEditable = false
      this.$store.commit('openLoader')

      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const formData = new FormData()
      formData.append('user_id', onedata.user.user_id)
      formData.append('bank_name', this.bank_name)
      formData.append('bank_branch', this.bank_branch)
      formData.append('bank_username', this.bank_username)
      formData.append('bank_code', this.bank_code)
      formData.append('bank_no', this.bank_no)
      formData.append('account_type', this.account_type)
      formData.append('bookbank_image', this.bookbankImage)
      formData.append('national_id', this.national_id)
      formData.append('id_card_image', this.taxImage)
      // address
      formData.append('house_no', this.houseNo)
      formData.append('room_no', this.roomNo)
      formData.append('floor', this.floor)
      formData.append('building', this.buildingName)
      formData.append('moo_ban', this.mooBan)
      formData.append('moo_no', this.mooNo)
      formData.append('soi', this.soi)
      formData.append('yaek', this.yaek)
      formData.append('street', this.street)
      formData.append('sub_district', this.subdistrict)
      formData.append('district', this.district)
      formData.append('province', this.province)
      formData.append('zip_code', this.zipcode)

      // console.log(this.taxImage)

      await this.$store.dispatch('actionsAffiliateUpdatePayment', formData)
      const responseUpdatePayment = await this.$store.state.ModuleAffiliate.stateAffiliateUpdatePayment

      if (responseUpdatePayment.message === 'update success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'อัปเดทสำเร็จ'
        })
      } else if (responseUpdatePayment.message === 'Large file size') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ขนาดไฟล์เกิน 5  MB'
        })
      } else if (responseUpdatePayment.message === 'File must be JPEG, PNG, JPG only') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ต้องเป็นไฟล์ในรูปแบบของ JPEG, PNG, JPG เท่านั้น'
        })
      } else if (responseUpdatePayment.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true,
        //   icon: 'info',
        //   title: 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
        // })
      } else if (responseUpdatePayment.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseUpdatePayment.message}`
        })
      }
    },
    triggerFileInput () {
      this.$refs.fileInput.click()
    },
    triggerFileInputTax () {
      this.$refs.fileInputTax.click()
    },
    handleFileUpload (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.bookbankImageUrl = reader.result
            this.bookbankImage = file
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพที่สกุล jpeg/jpg/png เท่านั้น',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    handleFileUploadTax (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.taxImageUrl = reader.result
            this.taxImage = file
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพที่สกุล jpeg/jpg/png เท่านั้น',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    async showDetailBuyer () {
      this.$store.commit('openLoader')
      // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // var data = {
      //   user_id: onedata.user.user_id
      // }

      // await this.$store.dispatch('actionsAffiliateShowDetailBuyer', data)
      await this.$store.dispatch('actionsAffiliateShowDetailBuyer')
      const responseShowDetailBuyer = await this.$store.state.ModuleAffiliate.stateAffiliateShowDetailBuyer
      if (responseShowDetailBuyer.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        this.account_type = responseShowDetailBuyer.data.account_type
        this.bank_username = responseShowDetailBuyer.data.bank_username
        this.selectedBank = {
          name: responseShowDetailBuyer.data.bank_name,
          code: responseShowDetailBuyer.data.bank_code
        }
        this.bank_branch = responseShowDetailBuyer.data.bank_branch
        this.bank_no = responseShowDetailBuyer.data.bank_no
        this.bookbankImageUrl = responseShowDetailBuyer.data.bookbank_image_url
        this.national_id = responseShowDetailBuyer.data.national_id
        this.taxImageUrl = responseShowDetailBuyer.data.id_card_image_url
        // address
        this.houseNo = responseShowDetailBuyer.data.house_no
        this.roomNo = responseShowDetailBuyer.data.room_no
        this.floor = responseShowDetailBuyer.data.floor
        this.buildingName = responseShowDetailBuyer.data.building_name
        this.mooBan = responseShowDetailBuyer.data.moo_ban
        this.mooNo = responseShowDetailBuyer.data.moo_no
        this.soi = responseShowDetailBuyer.data.soi
        this.yaek = responseShowDetailBuyer.data.yaek
        this.street = responseShowDetailBuyer.data.street
        this.subdistrict = responseShowDetailBuyer.data.sub_district
        this.district = responseShowDetailBuyer.data.district
        this.province = responseShowDetailBuyer.data.province
        this.zipcode = responseShowDetailBuyer.data.zip_code
      }

      this.$store.commit('closeLoader')
    },
    viewImageBookbank () {
      this.dialogBookbank = true
    },
    viewImageTax () {
      this.dialogTax = true
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipcode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.reduce-spacing {
  margin-bottom: -25px;
}

input:disabled {
  background-color: transparent;
}

/* .borderd-content {
  border: 1px solid #CCCCCC;
  border-radius: 4px;
  height: 100%;
  position: relative;
}

.borderd-content .title {
  margin: -21px 0 0 10px;
  background: #ffffff;
  padding: 4px;
  display: inline-block;
  font-family: 'Sukhumvit Set' !important;
  font-weight: 600;
  font-size: 16px !important;
  position: absolute;
} */
</style>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.input_text-thai-address input.th-address-input:disabled {
  color: rgba(0, 0, 0, 0.38) !important;
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}
</style>
