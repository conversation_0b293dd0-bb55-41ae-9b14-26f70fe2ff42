import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  // Create Company
  async createCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/create_v2`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Edit Company
  async editCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/edit_v2`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Detail Company
  async detailCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_v2`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async detailPersonalCompany () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/2022/company/list_v2`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // List Company
  async listCompany () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/2022/company/list_v2`, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // List User Commpany
  async listUserCompany () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/2022/user/list`, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // List PO For Buyer
  async listPOBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/list_for_buyer`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Detail QU for buyer
  async DetailQUBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/detail`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // update_status_buyer
  async UpdateStatusBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/update_status_buyer`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // list_order_purchaser
  async listOrderPurchaser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_purchaser`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Terminate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/order_terminate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // detail_order_purchaser
  async detailOrderPurchaser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_purchaser`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // edit QU Buyer
  async EditQU (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/detail_edit_qu`, data, auth)
      // console.log('', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async estimateQu (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/estimate_qu`, data, auth)
      // console.log('', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // List Refund Product Company
  async ListRefundPurchaser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_refund_purchaser`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Detail Refund Product Company
  async DetailRefundPurchaser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_refund_purchaser`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // (YaPalm) List Order Credit Term
  async listOrderCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/list_by_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm) List Credit Term
  async listCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/list_credit_term_by_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm) Request New Credit Term
  async calculateRequestCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/calculate_request_credit_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm) Check Calculate
  async checkCalculate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/check_calculate_request_credit_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm) Request New Credit Term
  async requestChangeTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/request_change_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm) Cancel Request New Credit Term
  async cancelRequestChangeTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/update_request_credit_term_by_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  async EditQuotation (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/edit_qu`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listPositionOfCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/list_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // List User  podition Seller
  async ListPositionUserSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_user_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async detailPositionOfCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailPositionUserSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_user_detail_with_role`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async createPositionOfCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/create_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchPositionUserSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/search_email_check_user_in_biz`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async editPositionOfCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/edit_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Edit User Position
  async EditPositionUserSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_user_in_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Add User Position
  async AddPositionUserSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/manage_user_with_role_in_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // list Position shop
  async ListPositionShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_position_in_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ListSpecialPriceBuyer
  async ListSpecialPriceBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}listSpecialPriceBuyer`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailSpecialPrice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}detailSpecialPrice`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // search product in modal special request buyer
  async ListProductAttribute (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_product_attribute`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateListDataTableSpecialPrice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}calculateSpecialPrice`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateStatusSpecialPriceBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}updateStatusSpecialPriceBuyer`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RequestSpecialPrice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}requestSpecialPrice`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditSpecialPriceBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}editSpecialPriceBuyer`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/create_order_qu`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateQUCart (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/create_qu_cart`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateCompanyByBiz () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_company_by_biz`, '', auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DashBorardAdmin (data) {
    // console.log('เข้า 2', data)
    const auth = await GetToken()
    // const data = {
    //   start_date: '2022-08-01',
    //   end_date: '2022-08-09',
    //   year: '2022'
    // }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}panit/dashboard/summary`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListAdminPlatform () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_admin_platform`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async allGpOfsystem (data) {
    // console.log('เข้า 2', data)
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}panit/dashboard/all_gp_of_system`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateAdminPlatform (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/create_admin_platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditAdminPlatform (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/edit_admin_platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailAdminPlatform (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/detail_admin_platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteAdminPlatform (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/delete_admin_platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchAdminPlatform (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/search_by_username`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDataQt (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}/get_quotation`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateQt (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_QT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateQtV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/create_qu_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditQtV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_QT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditQTDescription (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/config_qt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetShopDataAdmin () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_seller_shop`, '', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddAdminShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/add_admin_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListQTBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_QT_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddAdminCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/add_admin_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailQTBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_QT_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListBusinessAdmin () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_all_business_company_seller_shop`, '', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailCartInCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_approve`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CompanyApproveOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/company_approve_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailLandingPage () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_landing_page`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async MangeLandingPage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/mange_landing_page`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UserInSystem () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}users/all_user`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAddressCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}getCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddAddressCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}insertCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateAddressCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}updateCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteAddressCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}deleteCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetDefaultAddressCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}setDefaultCompanyAddress`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async eTaxAdmin () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}etax/report`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async MerchantEdit (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}admin/seller/merchant_edit`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async StockProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/getstock`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ImportStock (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}import/updateStock/excel`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async StatusPvNo (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/inquiry_status_pv_ap`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddDataEwth (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/add_data_ewth`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditDataEwth (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_data_ewth`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDataEwth (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_data_ewth`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetVender (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/get_vendor`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateVender (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/update_vendor`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserJoinAffiliate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/affiliate/get_affiliate_users`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSellerJoinAffiliate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/checkProductAffiliate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserJoinSellerJAffiliate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/checkSellerAffiliate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetTokenEmail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}users/search_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetShopComissionAffiliateTable (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/affiliate/report_shop_commissions`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserComissionAffiliateTable (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/affiliate/report_user_commission_incomes`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserBySeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/checkSellerUserAffiliateById`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserActive (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}users/active_user`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetOrderCommissionDetail (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/affiliate/report_user_commission_incomes/detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetProductSellerAffiliate (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/checkProductAffiliateById`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListAllCourier (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_all_courier`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditCourier (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/edit_courier`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditSellerShopShipping (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_seller_shop_shipping`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApproveQuotation (data, token) {
    const auth = {
      headers: { Authorization: `Bearer ${token}` }
    }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}approve/quotation`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchUserShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/search_user_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ManageListPosition (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/admin_list_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddUserShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/add_position_user_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCompanyDataAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserComapany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_users_with_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchUserCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/search_user_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditUserShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/edit_position_user_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPositionCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_position_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddUserCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}/api/admin_platform/add_user_position_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditUserCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/edit_user_position_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteUserCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/remove_user_position_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteUserShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/remove_user_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetFlashSale (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/set_flash_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async StatusFlashSale (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/admin_platform/get_status_flash_sale`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ShopPartnerAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/shop_partner_admin`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailShopPartnerAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/admin/partner_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPackagePartners () {
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/partner/get_package_list`)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateCouponPlatform (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/create_coupon_platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailCoupon (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}platform/coupon/get_detail_coupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditCouponPlatform (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}platform/coupon/edit_couponV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListCoupon (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/platform`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeStatusCoupon (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}platform/coupon/change_coupon_status`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteCoupon (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}platform/coupon/delete_couponV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditGP (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/update_is_gp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListServicePartner (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_service_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetJoinServicePartnerWithAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}/api/admin_platform/join_service_partner_with_admin`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListCostCenter (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}list_cost_center`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDataReach (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}reach/list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateDataReach (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}reach/createReach`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderReach (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}reach/increase`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminApproveShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/approve_shop_interested`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminListPosition (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/admin_list_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreatePositionShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/create_update_position_in_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendMail (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/email/send_mail_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditIcon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/manage_landing_page_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getDataIcon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_landing_page_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailPurchaserV2 (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}/api/detail_order_purchaser_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListCouponUser (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}admin/coupon/transfer/user/inventory`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchCoupon (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}admin/coupon/transfer/search/code`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddCouponUserAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}admin/coupon/transfer/collectById`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderDeliveryCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order_delivery/company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ConfirmOrderDeliveryCompany (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order_delivery/confirm_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchListUser (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/list_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDataListNoti (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/list_promotion`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateNoti (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/create_promotion`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async StoreRegisList (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/store_regis_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async StoreRegisDetail (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/request_seller_shop_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateNotiNow (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/instant_notification`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteNoti (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/delete_promotion`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditNoti (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/update_promotion`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchNoti (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobile/notification/search_promotion`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListType (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_otop_type`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchHistory (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/searchHistory`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditIconMobile (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/manage_landing_page_v2_mobile`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getDataIconMobile (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_landing_page_v2_mobile`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getListCancelOrderAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_order_cancel`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListSortProduct (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}product/listSortProduct`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateSortProduct (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}product/updateSortProduct`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPaymentTransfer (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/listPaymentTransfer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListRegisterInfoPartner (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}business/listBussinessPartner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailRegisterInfoPartner (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}business/detailBussinessPartner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListsOrderCancelAdmin (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/order_cancels`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdatePaymentTransfer (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/updatePaymentTransfer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateCancelOrderList (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_cancel_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListRegisterInfoShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/regis_shop_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailRegisterInfoShop (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/regis_shop_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
