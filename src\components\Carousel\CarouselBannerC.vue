<template>
  <div>
    <v-col cols="12" class="" v-if="!MobileSize && !IpadSize">
      <v-row no-gutters class="d-flex justify-center" v-if="HorizontalIpad > 1264">
        <!-- <div v-if="Banner3.length === 2" >
          <v-row no-gutters class="mb-6" >
              <v-img @click="test()" class="" style="cursor: pointer; " max-width="612" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img>
              <div class="mx-2"></div>
              <v-img @click="test()" class="" style="cursor: pointer; " max-width="612" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy"></v-img>
          </v-row>
        </div> -->
        <div v-if="Banner3.length === 2" >
          <v-row no-gutters class="my-5" >
          <!-- <v-col cols="6" class="my-5" > -->
            <v-img v-if="Banner3[0].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="612"  :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)"></v-img>
            <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg" max-height="270" max-width="612" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img>
            <div class="mx-2"></div>
            <v-img v-if="Banner3[1].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="612" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" @click="navigateToLink(Banner3[1].link_banner)"></v-img>
            <v-img v-if="Banner3[1].link_banner === '-'" class="rounded-lg" max-height="270" max-width="612" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy"></v-img>
          <!-- </v-col> -->
          </v-row>
        </div>
        <v-container v-if="Banner3.length === 1" class="d-flex justify-center" width="1240" >
          <v-col cols="6">
            <!-- <v-img class="rounded-lg" style="cursor: pointer;" width="1240" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img> -->
            <v-img v-if="Banner3[0].link_banner !== '-'" lazy="loading" style="cursor: pointer;" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" max-height="270" width="1240"></v-img>
            <v-img v-if="Banner3[0].link_banner === '-'" lazy="loading" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" max-height="270" width="1240"></v-img>
          </v-col>
        </v-container>
      </v-row>
      <v-row no-gutters class="d-flex justify-center mb-8 pa-0"  v-else>
        <div v-if="Banner3.length === 2" >
          <v-row dense >
            <v-col cols="6" class="pr-1">
              <v-img v-if="Banner3[0].link_banner !== '-'" lazy="loading" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="50vw"  :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)"></v-img>
              <v-img v-if="Banner3[0].link_banner === '-'" lazy="loading" class="rounded-lg" max-height="270" max-width="50vw" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img>
            </v-col>
            <v-col cols="6" class="pl-1">
              <v-img v-if="Banner3[1].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="50vw" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" @click="navigateToLink(Banner3[1].link_banner)"></v-img>
              <v-img v-if="Banner3[1].link_banner === '-'" class="rounded-lg" max-height="270" max-width="50vw" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy"></v-img>
            </v-col>
          </v-row>
        </div>
        <v-container v-if="Banner3.length === 1" class="d-flex justify-center" width="1240" >
          <div>
            <v-img v-if="Banner3[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" max-height="270" width="1240"></v-img>
            <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" max-height="270" width="1240"></v-img>
          </div>
        </v-container>
      </v-row>
    </v-col>
    <v-col cols="12" class="ma-0 pa-3" v-if="MobileSize">
      <div v-if="Banner3.length === 2">
        <v-img v-if="Banner3[0].link_banner !== '-'" class="rounded-lg" lazy="loading" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" width="100%" height="100%"></v-img>
        <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg" lazy="loading" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" width="100%" height="100%"></v-img>
        <v-img v-if="Banner3[1].link_banner !== '-'" class="rounded-lg mt-2" lazy="loading" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" @click="navigateToLink(Banner3[1].link_banner)" width="100%" height="100%"></v-img>
        <v-img v-if="Banner3[1].link_banner === '-'" class="rounded-lg mt-2" lazy="loading" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" width="100%" height="100%"></v-img>
        <!-- <v-img :src="Banner3[0].image_path" @click="navigateToLink('A', Banner3[0].link_banner)"></v-img>
        <v-img class="mt-2" :src="Banner3[1].image_path"></v-img> -->
      </div>
      <div v-if="Banner3.length === 1">
        <v-img v-if="Banner3[0].link_banner !== '-'" class="rounded-lg my-3" lazy="loading" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" ></v-img>
        <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg my-3" lazy="loading" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" ></v-img>
        <!-- <v-img :src="Banner3[0].image_path"></v-img> -->
      </div>
      <!-- <v-row no-gutters class="d-flex justify-center">
          <div v-if="Banner3.length === 2" >
            <v-row no-gutters class="my-5" >
                <v-img v-if="Banner3[0].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="612"  :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)"></v-img>
                <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg" max-height="270" max-width="612" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img>
                <div class="mx-2"></div>
                <v-img v-if="Banner3[1].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="612" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" @click="navigateToLink(Banner3[1].link_banner)"></v-img>
                <v-img v-if="Banner3[1].link_banner === '-'" class="rounded-lg" max-height="270" max-width="612" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy"></v-img>
            </v-row>
          </div>
          <v-container v-if="Banner3.length === 1" class="d-flex justify-center" width="1240" >
            <div>
              <v-img v-if="Banner3[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" max-height="270" width="1240"></v-img>
              <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" max-height="270" width="1240"></v-img>
            </div>
          </v-container>
      </v-row> -->
    </v-col>
    <v-col cols="12" v-if="IpadSize">
      <v-row no-gutters class="d-flex justify-center">
        <div v-if="Banner3.length === 2">
          <v-row dense class="my-5 ">
            <v-col cols="6" class="">
              <v-img v-if="Banner3[0].link_banner !== '-'" lazy="loading" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="100vw" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)"></v-img>
              <v-img v-if="Banner3[0].link_banner === '-'" lazy="loading" class="rounded-lg" max-height="270" max-width="100vw" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img>
            </v-col>
            <v-col cols="6">
              <v-img v-if="Banner3[1].link_banner !== '-'" lazy="loading" class="rounded-lg" style="cursor: pointer; " max-height="270" max-width="100vw" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" @click="navigateToLink(Banner3[1].link_banner)"></v-img>
              <v-img v-if="Banner3[1].link_banner === '-'" lazy="loading" class="rounded-lg" max-height="270" max-width="100vw" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy"></v-img>
            </v-col>
          </v-row>
        </div>
        <v-container v-if="Banner3.length === 1" class="d-flex justify-center" width="1240">
          <div>
            <!-- <v-img class="rounded-lg" style="cursor: pointer;" width="1240" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img> -->
            <v-img v-if="Banner3[0].link_banner !== '-'" lazy="loading" style="cursor: pointer;" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" max-height="270" width="1240"></v-img>
            <v-img v-if="Banner3[0].link_banner === '-'" lazy="loading" class="rounded-lg my-3" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" max-height="270" width="100vw"></v-img>
          </div>
        </v-container>
      </v-row>
    </v-col>
    <v-dialog v-model="ModelShop" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายชื่อร้านค้า</b></span>
              </v-col>
              <v-btn fab small @click="closeGetShop()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- New Select Company -->
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">ร้านค้าของฉัน</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{listShopData.length}} รายการ)</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-text-field v-model="searchShop" dense outlined style="border-radius: 6px;"
                      placeholder="ค้นหาจากชื่อร้านค้า">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                      :headers="headerTableShop"
                      :items="listShopData"
                      :items-per-page="10"
                      :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                      no-data-text="ไม่มีรายชื่อร้านค้าของฉันในตาราง"
                      no-results-text="ไม่พบรายชื่อร้านค้าในตาราง"
                    >
                      <template v-slot:[`item.index`]="{ item }">
                        {{ listShopData.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                      </template>
                      <template v-slot:[`item.ShopInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="4" sm="2" md="2" class="pr-0">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="8" sm="7" md="7" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn text color="#27AB9C" @click="selectPartner(item)">
                              <span style="text-decoration: underline;">เลือกร้านค้า</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in listShopData" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.shop_name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="selectPartner(item)">
                          <span style="text-decoration: underline; font-size: 12px;">เลือกร้านค้า</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      banner: 0,
      listBannerA: [],
      imageBannerA: '',
      imageLazyBannerA: '',
      listBannerB: [],
      imageBannerB: '',
      imageLazyBannerB: '',
      Banner3: [],
      link_WLC: '',
      link_EFC: '',
      linkData: '',
      getToken: '',
      value: '',
      err: '',
      ShopData: [],
      listShopData: [],
      listShop: [],
      ModelShop: false,
      headerTableShop: [
        { text: 'ลำดับ', value: 'index', width: '10%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อคู่ค้า', value: 'ShopInfo', width: '80%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      searchShop: '',
      indexShop: 0,
      dataTaxID: ''
    }
  },
  computed: {
    HorizontalIpad () {
      const val = window.innerWidth
      return val
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    await this.GetBanner()
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.getToken = oneData.user.access_token
    }
    // this.WLC()
  },
  methods: {
    navigateToLink (link) {
      this.linkData = link
      // console.log('link', link)
      fetch(link, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.getToken
        }
      })
        .then(response => response.json())
        .then(
          data => {
            if (data.data.require_taxid) {
              // window.open(data.data.url, '_blank')
              this.getShopData(data.data.url)
            } else {
              window.open(data.data.url, '_blank')
            }
          }
        )
        .catch(
          error => {
            this.err = error
            window.open(link, '_blank')
            // console.error('Error:', error)
          }
        )
    },
    async GetBanner () {
      await this.$store.dispatch('actionsGetBanner')
      var response = await this.$store.state.ModuleHompage.stateGetBanner
      // console.log('response_GetBannerC', response.data.image_banner_3.length)
      // this.banner = response.data.image_banner_3.length
      // console.log('response_GetBannerA', response.data.image_banner_1[0])
      // this.listBannerA = response.data.image_banner_3[0]
      // this.imageBannerA = this.listBannerA.path
      // this.imageLazyBannerA = this.listBannerA.path_lazy
      // this.listBannerB = response.data.image_banner_3[1]
      // this.imageBannerB = this.listBannerB.path
      // this.imageLazyBannerB = this.listBannerB.path_lazy
      if (response.data.image_banner_3.length > 0) {
        for (let i = 0; i < response.data.image_banner_3.length; i++) {
          this.Banner3.push({
            image_path: response.data.image_banner_3[i].path,
            image_path_lazy: response.data.image_banner_3[i].path_lazy,
            link_banner: response.data.image_banner_3[i].href
          })
        }
      } else {
        this.Banner3 = []
      }
    },
    async WLC () {
      var data = ''
      await this.$store.dispatch('actionsGetTokenWLC', data)
      var res = await this.$store.state.ModuleUser.stateGetTokenWLC
      this.link_WLC = res.data.url
      // console.log('WLC ===>', res)
    },
    getShopData (item) {
      this.ModelShop = true
      this.link_EFC = item
      // this.listShop = []
      // this.listShopData = []
      this.ShopData = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
      this.listShopData = this.ShopData.data[0].shop_data
      // console.log('listShopData', this.ShopData)
    },
    selectPartner (item) {
      // console.log('item', item.id, item.tax_id, this.linkData)
      fetch(this.linkData + '/' + item.tax_id, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.getToken
        }
      })
        .then(response => response.json())
        .then(
          data => {
            if (data.data.require_taxid === false) {
              window.open(data.data.url, '_blank')
            } else {
              window.open(data.data.url, '_blank')
            }
          }
        )
        .catch(
          error => {
            this.err = error
            // console.error('Error:', error)
          }
        )
    },
    closeGetShop () {
      this.ModelShop = false
    }
  }
}
</script>
