<template>
  <v-card elevation="0" width="100%" height="100%">
    <v-card-text>
      <v-row no-gutters>
        <v-col cols="12" :class="!MobileSize ? 'pl-0 pr-3 mb-3 pt-3' : 'pl-2 pr-2 mb-3 pt-3'">
          <v-row dense>
            <v-btn icon @click="goBack()" :class="MobileSize? 'mt-1' : ''"><v-icon color="#27AB9C">mdi-chevron-left</v-icon></v-btn>
            <h2 :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'">ใบแจ้งหนี้</h2>
          </v-row>
        </v-col>
        <v-col cols="12" class="mt-2">
          <v-row dense class="mb-2">
            <v-col cols="12" md="4">
              <span>วันที่สร้าง :</span>
              <span class="ml-4">
                <!-- <b>{{new Date(order_detail.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</b> -->
                <b>{{order_detail.created_at}}</b>
              </span>
            </v-col>
            <v-col cols="12" md="4" class="">
              <span>รหัสการสั่งซื้อ :</span>
              <span class="ml-4"><b>{{order_detail.payment_credit_term_number}}</b></span>
            </v-col>
            <v-col cols="12" md="4">
              <span>สถานะ : </span>
              <span v-if="order_detail.transaction_status === 'Success'">
                <v-chip color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Not Paid' || item.transaction_status === 'Overdue'">
                <v-chip color="#E5EFFF" text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Fail'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Cancel'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
              </span>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12">
        <v-row dense class="mb-2">
          <v-col cols="12" md="4">
            <span>วันที่อัปเดตล่าสุด :</span>
            <span class="ml-4">
              <!-- <b>{{new Date(order_detail.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</b> -->
              <b>{{order_detail.updated_at}}</b>
            </span>
          </v-col>
          <v-col cols="12" md="4">
            <span>ส่งคำขอโดย :</span>
            <span class="ml-4"><b>{{order_detail.buyer_name}}</b></span>
          </v-col>
        </v-row>
        </v-col>
        <v-card color="#C4C4C4" width="1057px" height="100%" elevation="1">
          <v-card-text align="center">
            <iframe
            :src="order_detail.pdf_path"
            width="100%" :height="!MobileSize ? '950px' : '450px'"></iframe>
          </v-card-text>
        </v-card>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      order_number: '',
      order_detail: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      var number = JSON.parse(Decode.decode(localStorage.getItem('creditTerm')))
      if (val === true) {
        this.$router.push({ path: `/invoicePDFMobile?order_number=${number.order_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/invoicePDF?order_number=${number.order_number}` }).catch(() => {})
      }
    }
  },
  created () {
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.order_number = this.$route.query.order_number
      this.order_detail = JSON.parse(Decode.decode(localStorage.getItem('creditTerm')))
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    // console.log(this.order_number)
    // console.log(this.order_detail)
  },
  methods: {
    goBack () {
      localStorage.removeItem('creditTerm')
      if (this.MobileSize === true) {
        this.$router.push({ path: `/companyListCreditTermMobile?order_number=${this.order_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/companyListCreditTerm?order_number=${this.order_number}` }).catch(() => {})
      }
    }
  }
}
</script>

<style>
.fontSizeTitle {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333; padding-top: 5px;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 32px; color: #333333;
}

</style>
