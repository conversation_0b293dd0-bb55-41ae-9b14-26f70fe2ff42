<template>
  <div class="div_overflow">
      <v-row no-gutters>
        <v-col cols="12" >
          <v-form ref="FormAddProduct" :lazy-validation="lazy">
              <v-row class="ml-2 mr-2" no-gutters>
                <v-col cols="12">
                  <v-card outlined id="step-1">
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10">
                        <h2 >ข้อมูลทั่วไป</h2>
                      </v-col>
                      <v-col cols="12"><v-divider></v-divider></v-col>
                      <v-col cols="5" md="2" class="pl-3"><span class="f-right">เปิดใช้งานสินค้า</span></v-col>
                      <v-col cols="7" md="9" class="pl-3">
                        <v-switch v-model="Detail[0].product_status" :label="`${message}`"></v-switch>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* ชื่อสินค้า</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          id="product_name"
                          v-model="product_name"
                          label="ชื่อสินค้า"
                          placeholder="ชื่อสินค้า"
                          :rules="itemRules.product_name"></v-text-field>
                      </v-col>
                      <v-col cols="12" md="1" class="pl-3 pb-5"><span class="f-right">* รหัส SKU</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          id="product_sku"
                          v-model="product_sku"
                          label="รหัส SKU"
                          placeholder="รหัส SKU"
                          :rules="itemRules.sku"></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"> <span class="f-right">หมวดหมู่สินค้า</span></v-col>
                      <v-col cols="12" md="9" class="pl-3 pb-5">
                        <treeselect
                          class="Bgtree"
                          :multiple="true"
                          :options="itemsListCategory"
                          v-model="treeCategory"
                          :normalizer="normalizer"
                          :openDirection="'bottom'"
                          :flat="true"
                          placeholder="Select your Parent Category(s)..."
                        />
                        </v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* ตัวเเทนจำหน่าย</span></v-col>
                       <v-col cols="12" md="4" class="pl-3 mb-5">
                        <cool-select v-model="SelectBrand" :items="ListBrand" placeholder="เเบรน" :menuDefaultPosition="menuDefaultPosition" :menuDynamicPosition="false">
                          <div slot="after-items-fixed">
                            <a-row type="flex" justify="center" class="py-3">
                              <!-- <span class="pt-1">ต้องการเพิ่มเเบรน</span> -->
                              <a-col :span="15" class="pl-2 pr-2"><a-input v-model="AddBrand" placeholder="เเบรน" suffix=" " /></a-col>
                              <a-button @click="AddNewBrand()"><a-icon type="plus" /> เพิ่ม</a-button>
                            </a-row>
                          </div>
                        </cool-select>
                      </v-col>
                      <v-col cols="12" md="1"  class="pl-3 pb-5"><span class="f-right">* เเบรน</span></v-col>
                      <v-col cols="12" md="4" class="pl-3 mb-5">
                        <cool-select v-model="SelectBrand" :items="ListBrand" placeholder="เเบรน" :menuDefaultPosition="menuDefaultPosition" :menuDynamicPosition="false">
                          <div slot="after-items-fixed">
                            <a-row type="flex" justify="center" class="py-3">
                              <!-- <span class="pt-1">ต้องการเพิ่มเเบรน</span> -->
                              <a-col :span="15" class="pl-2 pr-2"><a-input v-model="AddBrand" placeholder="เเบรน" suffix=" " /></a-col>
                              <a-button @click="AddNewBrand()"><a-icon type="plus" /> เพิ่ม</a-button>
                            </a-row>
                          </div>
                        </cool-select>
                      </v-col>
                      <v-col cols="2" class="pl-3"> <span class="f-right" style="padding-bottom:150px">คำอธิบาย</span></v-col>
                      <v-col cols="9"  class="pl-3">
                        <!-- <v-textarea outlined dense
                          id="description"
                          v-model="product_description"
                          label="คำอธิบาย"
                          placeholder="คำอธิบาย"></v-textarea> -->
                        <ckeditor :editor="editor" :config="editorConfig" v-model="product_description"></ckeditor>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
                <v-col cols="12" class="mt-5">
                  <v-card outlined>
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10" id="step-2">
                        <h2>ข้อมูลการขาย</h2>
                      </v-col>
                      <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* รหัสสต็อก</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense v-if="checkbox === true" disabled
                            id="product_stock"
                            v-model="product_sku"
                            label="รหัสสต็อก"
                            placeholder="รหัสสต็อก"></v-text-field>
                        <v-autocomplete outlined dense :items="itemsProductCode" v-else
                          id="inventory_code"
                          v-model="Detail[0].product_code"
                          item-text="inventory_code"
                          item-value="inventory_code"
                          label="รหัสสต็อก"
                          placeholder="รหัสสต็อก"
                          :rules="itemRules.empty"></v-autocomplete>
                      </v-col>
                      <v-col cols="12" md="5"  class="pl-3 pb-6">
                        <v-checkbox v-model="checkbox" :label="checkbox === true ? 'ใช้รหัสสต็อกร่วมกับ sku' : 'ใช้รหัสสต็อกร่วมกับ sku' "></v-checkbox>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">จำนวนคลังสินค้า</span></v-col>
                        <v-col cols="12" md="4" class="pl-3">
                          <v-text-field outlined dense
                            id="product_stock"
                            v-model="product_stock"
                            label="จำนวนสินค้า"
                            placeholder="จำนวนสินค้า"
                            suffix="ชิ้น"
                            :rules="itemRules.stock"></v-text-field>
                        </v-col>
                      <v-col cols="12" md="1" class="pl-3 pb-5"><span class="f-right">* น้ำหนัก</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          id="product_weight"
                          v-model="product_weight"
                          label="น้ำหนักสินค้า"
                          placeholder="น้ำหนักสินค้า"
                          suffix="กิโลกรัม"
                          :rules="itemRules.weight"></v-text-field>
                      </v-col>
                      <v-col cols="2" md="2" class="pl-3 pb-5"><span class="f-right">อัตราส่วนต่อหน่วย</span></v-col>
                        <v-col cols="4" md="4" class="pl-3">
                          <v-select outlined dense
                            id="ratio"
                            :items="Ratio"
                            label="อัตราส่วนต่อหน่วย"
                            placeholder="อัตราส่วนต่อหน่วย"
                            v-model="SelectRatio"></v-select>
                        </v-col>
                        <v-col cols="6"></v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* ราคา float</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          id="pricetier_1"
                          v-model="pricetier_1"
                          label="ราคา float"
                          suffix="บาท"
                          placeholder="ราคา float"
                          :rules="itemRules.float"></v-text-field>
                      </v-col>
                      <v-col cols="12" md="6"  class="pl-3 pb-6">
                        <a-button @click="Specialprice()"><a-icon type="plus" />เพิ่มราคาพิเศษ</a-button>
                      </v-col>

                      <v-row no-gutters v-if="disablePrice === true">
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* ราคา float</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          id="pricetier_1"
                          v-model="pricetier_1"
                          label="ราคา float"
                          suffix="บาท"
                          placeholder="ราคา float"
                          :rules="itemRules.float"></v-text-field>
                      </v-col>
                          <v-col cols="12" md="1" class="pl-3 pb-5"><span class="f-right">* ราคา tier 1</span></v-col>
                          <v-col cols="12" md="4" class="pl-3">
                            <v-text-field outlined dense
                              id="pricetier_2"
                              v-model="pricetier_2"
                              label="ราคา tier 1"
                              placeholder="ราคา tier 1"
                              suffix="บาท"
                              :rules="itemRules.tier1"></v-text-field>
                          </v-col>
                          <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* ราคา tier 2</span></v-col>
                          <v-col cols="12" md="4" class="pl-3">
                            <v-text-field outlined dense
                              id="pricetier_3"
                              v-model="pricetier_3"
                              label="ราคา tier 2"
                              placeholder="ราคา tier 2"
                              suffix="บาท"
                              :rules="itemRules.tier2"></v-text-field>
                          </v-col>
                          <v-col cols="12" md="1" class="pl-3 pb-5"><span class="f-right">* ราคา tier 3</span></v-col>
                          <v-col cols="12" md="4" class="pl-3">
                            <v-text-field outlined dense
                              id="pricetier_4"
                              v-model="pricetier_4"
                              label="ราคา tier 3"
                              placeholder="ราคา tier 3"
                              suffix="บาท"
                              :rules="itemRules.tier3"></v-text-field>
                          </v-col>
                          <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">* ราคา tier 4</span></v-col>
                          <v-col cols="12" md="4" class="pl-3">
                            <v-text-field outlined dense
                              id="pricetier_5"
                              v-model="pricetier_5"
                              label="ราคา tier 4"
                              placeholder="ราคา tier 4"
                              suffix="บาท"
                              :rules="itemRules.tier4"></v-text-field>
                          </v-col>
                          <v-col cols="12" md="1" class="pl-3 pb-5"><span class="f-right">* ราคา tier 5</span></v-col>
                          <v-col cols="12" md="4" class="pl-3">
                            <v-text-field outlined dense
                              id="pricetier_6"
                              v-model="pricetier_6"
                              label="ราคา tier 5"
                              placeholder="ราคา tier 5"
                              suffix="บาท"
                              :rules="itemRules.tier5"></v-text-field>
                          </v-col>
                          </v-row>
                          <!-- <v-col cols="2" md="2" class="pl-3 pb-5"><span class="f-right">อัตราส่วนต่อหน่วย</span></v-col>
                          <v-col cols="4" md="4" class="pl-3">
                            <v-select outlined dense
                              id="ratio"
                              :items="Ratio"
                              label="อัตราส่วนต่อหน่วย"
                              placeholder="อัตราส่วนต่อหน่วย"
                              v-model="SelectRatio"></v-select>
                          </v-col> -->
                          <!-- </v-row>
                        </v-col> -->
                        <!-- <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">{{$t('seller.product.detail.stock_status')}}</span></v-col>
                        <v-col cols="12" md="4" class="pl-3">
                          <v-select outlined dense
                            :items="StockStatus"
                            :label="$t('seller.product.detail.stock_status')"
                            :placeholder="$t('seller.product.detail.stock_status')"
                            v-model="SelectStockStatus"></v-select>
                        </v-col> -->
                    </v-row>
                  </v-card>
                </v-col>
                <!-- <v-col cols="12" class="mt-5">
                  <v-card outlined>
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10" id="step-3">
                        <h2>การจัดส่ง</h2>
                      </v-col>
                      <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">{{$t('seller.product.detail.weight')}}</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          v-model="product_weight"
                          :label="$t('seller.product.detail.weight')"
                          :placeholder="$t('seller.product.detail.weight')"
                          :rules="itemRules.weight"></v-text-field>
                      </v-col>
                      <v-col cols="12" md="1" class="pl-3 pb-5"><span class="f-right">{{$t('seller.product.detail.product_size')}}</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-select outlined dense
                          :items="ProductSize"
                          :label="$t('seller.product.detail.product_size')"
                          :placeholder="$t('seller.product.detail.product_size')"
                          v-model="Detail[0].product_size"></v-select>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">{{$t('seller.product.detail.shipping_rate')}}</span></v-col>
                      <v-col cols="12" md="4" class="pl-3">
                        <v-text-field outlined dense
                          v-model="Detail[0].product_shipping_rate"
                          :label="$t('seller.product.detail.shipping_rate')"
                          :placeholder="$t('seller.product.detail.shipping_rate')"
                          :rules="itemRules.shipping_rate"></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col> -->
                <v-col cols="12" class="mt-5">
                  <v-card outlined>
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10" id="step-4">
                        <h2>สื่อ</h2>
                      </v-col>
                      <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                      <v-col cols="12" md="12" class="pl-3 mb-5" @click="onPickFile()">
                        <v-row no-gutters align="center" justify="center">
                          <v-file-input
                            v-model="DataImage"
                            :items="DataImage"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImage()"
                            id="file_input"
                            multiple
                            :clearable="false"
                            style="display:none"></v-file-input>
                            <v-img max-width="50" src="@/assets/upload.png" class="mr-3"></v-img>
                            <span>คลิกเพื่อเลือกรูป</span>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" v-if="Detail[0].product_image.length !== 0">
                        <draggable v-model="Detail[0].product_image"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                          <v-col v-for="(item, index) in Detail[0].product_image" :key="index" cols="3" md="3">
                            <v-card dense light class="pa-1">
                              <v-card-actions>
                                <v-spacer></v-spacer>
                                <v-icon small light @click="RemoveImage(index)">mdi-close</v-icon>
                              </v-card-actions>
                              <v-img :src="item.url" :lazy-src="item.url" aspect-ratio="1.8" contain></v-img>
                              <v-card-text class="text-md-center">
                                <span class="subheading">{{item.name}}</span>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </draggable>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
            <v-row no-gutters justify="center" align="center" class="mt-5 mb-5">
              <v-btn @click="Cancle()">ยกเลิก</v-btn>
              <v-btn color="success" class="ml-4" id="confirmcreateproduct" @click="Confirm()">ตกลง</v-btn>
            </v-row>
          </v-form>
        </v-col>
      </v-row>
      <ModalAddProductCode />
  </div>
</template>

<script>
import { Decode } from '@/services'
import { CoolSelect } from 'vue-cool-select'
import draggable from 'vuedraggable'
import Treeselect from '@riophae/vue-treeselect'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// import ModalAddProductCode from '@/components/Seller/Inventory/ModalAddInventory'
import dataMessage from './datatour.json'
export default {
  components: {
    draggable,
    Treeselect,
    CoolSelect
  },
  data () {
    return {
      menuDefaultPosition: 'bottom',
      opendirection: 'bottom',
      checkbox: true,
      SelectBrand: null,
      ListBrand: [],
      disablePrice: false,
      targetOffset: undefined,
      lazy: false,
      EnableProduct: true,
      SelectRatio: '1',
      SelectStockStatus: 'In Stock',
      Ratio: ['1', '2', '6', '12', '24'],
      StockStatus: ['In Stock', 'Out Of Stock'],
      ProductSize: ['normal', 'large', 'special larg'],
      itemsProductCode: [],
      itemsManufacturer: [],
      itemsSupplier: [],
      DataImage: [],
      itemsListCategory: [],
      treeCategory: null,
      normalizer (node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children_data
        }
      },
      itemRules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        product_name: [
          v => !!v || 'กรุณาระบุชื่อสินค้า'
        ],
        sku: [
          v => !!v || 'กรุณาระบุรหัสสินค้า'
        ],
        float: [
          v => !!v || 'กรุณาระบุราคาสินค้า',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        tier1: [
          v => !!v || 'กรุณาระบุราคาสินค้า tier 1',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        tier2: [
          v => !!v || 'กรุณาระบุราคาสินค้า tier 2',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        tier3: [
          v => !!v || 'กรุณาระบุราคาสินค้า tier 3',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        tier4: [
          v => !!v || 'กรุณาระบุราคาสินค้า tier 4',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        tier5: [
          v => !!v || 'กรุณาระบุราคาสินค้า tier 5',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        shipping_rate: [
          v => !!v || 'กรุณาระบุราคาค่าขนส่ง',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        ratio: [
          v => !!v || 'กรุณาระบุอัตราส่วนต่อหน่วย',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        stock: [
          v => !!v || 'กรุณาระบุจำนวนสินค้าในคลัง',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ],
        weight: [
          v => !!v || 'กรุณาระบุนำ้หนักสินค้า',
          v => /^([0-9\W^ก-ฮ?*/s+])+$/.test(v) || 'รูปแบบไม่ถูกต้อง กรุณาระบุตัวเลขเท่านั้น'
        ]
      },
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'bulletedlist',
          'numberedlist',
          '|',
          'blockquote',
          'inserttable',
          'undo',
          'redo'
        ]
      },
      Detail: [
        {
          seller_shop_id: '1',
          product_sku: '',
          product_code: '',
          product_name: '',
          product_status: true,
          product_ratio: '',
          product_description: '',
          product_shipping_rate: '0',
          product_size: 'normal',
          product_weight: '',
          product_categories: [],
          manufacturer_name: '',
          product_short_description: '',
          supplier_name: '',
          product_stock: '',
          product_price: [
            { tier: '1', price: '' },
            { tier: '2', price: '' },
            { tier: '3', price: '' },
            { tier: '4', price: '' },
            { tier: '5', price: '' },
            { tier: '6', price: '' }
          ],
          product_image: []
        }
      ],
      product_sku: '',
      product_name: '',
      product_description: '',
      product_weight: '',
      product_stock: '0',
      pricetier_1: '',
      pricetier_2: '',
      pricetier_3: '',
      pricetier_4: '',
      pricetier_5: '',
      pricetier_6: '',
      statustour: false,
      title: '',
      content: '',
      note: ''
    }
  },
  mounted () {
    this.targetOffset = window.innerHeight
  },
  watch: {
    product_name (val) {
      this.title = dataMessage.data[0].title
      this.content = dataMessage.data[0].content
      this.statustour = true
    },
    product_sku (val) {
      this.title = dataMessage.data[1].title
      this.content = dataMessage.data[1].content
      this.statustour = true
    },
    product_description (val) {
      this.title = dataMessage.data[2].title
      this.content = dataMessage.data[2].content
      this.statustour = true
    },
    product_weight (val) {
      this.title = dataMessage.data[5].title
      this.content = dataMessage.data[5].content
      this.statustour = true
    },
    product_stock (val) {
      this.title = dataMessage.data[4].title
      this.content = dataMessage.data[4].content
      this.statustour = true
    },
    pricetier_1 (val) {
      this.title = dataMessage.data[3].title
      this.content = dataMessage.data[3].content
      this.statustour = true
    },
    pricetier_2 (val) {
      this.title = dataMessage.data[3].title
      this.content = dataMessage.data[3].content
      this.statustour = true
    },
    pricetier_3 (val) {
      this.title = dataMessage.data[3].title
      this.content = dataMessage.data[3].content
      this.statustour = true
    },
    pricetier_4 (val) {
      this.title = dataMessage.data[3].title
      this.content = dataMessage.data[3].content
      this.statustour = true
    },
    pricetier_5 (val) {
      this.title = dataMessage.data[3].title
      this.content = dataMessage.data[3].content
      this.statustour = true
    },
    pricetier_6 (val) {
      this.title = dataMessage.data[3].title
      this.content = dataMessage.data[3].content
      this.statustour = true
    }
  },
  computed: {
    message () {
      return this.Detail.product_status ? 'เปิด' : 'ปิด'
    },
    ModalAddProduct () {
      return this.$store.state.Seller.ModalAddProduct
    }
  },
  created () {
    this.$EventBus.$on('SetDataCreateProduct', this.GetDataProduct)
    this.GetDataProduct()
  },
  methods: {
    Specialprice () {
      this.disablePrice = !this.disablePrice
    },
    getCurrentAnchor () {
      return '#step-1'
    },
    CreateProductCode () {
      this.$store.commit('MutationsDailogAddInventory')
    },
    async GetDataProduct () {
      // const shopData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('shopData'))))
      // const shopID = {
      //   seller_shop_id: shopData.id
      // }
      // await this.$store.dispatch('GetListProductCode', shopID)
      // this.itemsProductCode = await this.$store.state.Seller.ListProductCode
      // await this.$store.dispatch('GetListManufacturer')
      // this.itemsManufacturer = await this.$store.state.Seller.ListManufacturer
      // await this.$store.dispatch('GetListSupplier')
      // this.itemsSupplier = await this.$store.state.Seller.ListSupplier
      await this.$store.dispatch('GetCagegory')
      var ResponseListCategory = await this.$store.state.ModuleManageShop.Category.data.data
      // console.log('ResponseListCategory', ResponseListCategory)
      this.treeCategory = [1]
      this.itemsListCategory = [ResponseListCategory[0]]
      // console.log('itemsListCategory', this.itemsListCategory)
      if (this.itemsListCategory[0].id === 1) {
        this.itemsListCategory[0].isDisabled = true
      }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      for (let i = 0; i < this.DataImage.length; i++) {
        const element = this.DataImage[i]
        const reader = new FileReader()
        reader.readAsDataURL(element)
        reader.onload = () => {
          var resultReader = reader.result
          var url = URL.createObjectURL(element)
          this.Detail[0].product_image.push({
            image_data: resultReader,
            url: url,
            name: this.DataImage[i].name
          })
        }
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage (index) {
      this.Detail[0].product_image.splice(index, 1)
    },
    async Confirm () {
      const shopData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('shopData'))))
      this.Detail[0].seller_shop_id = shopData.id
      if (this.$refs.FormAddProduct.validate(true)) {
        this.Detail[0].product_status ? this.Detail[0].product_status = '1' : this.Detail[0].product_status = '0'
        this.Detail[0].product_size === 'nomal' ? this.Detail[0].product_size = '1' : this.Detail[0].product_size === 'large' ? this.Detail[0].product_size = '2' : this.Detail[0].product_size = '3'
        for (let index = 0; index < this.treeCategory.length; index++) {
          this.Detail[0].product_categories.push(this.treeCategory[index])
        }
        this.Detail[0].product_ratio = this.SelectRatio
        this.Detail[0].product_ratio = this.SelectRatio
        this.Detail[0].product_sku = this.product_sku
        this.Detail[0].product_name = this.product_name
        this.Detail[0].product_weight = this.product_weight
        this.Detail[0].product_stock = this.product_stock
        this.Detail[0].product_price[0].price = this.pricetier_1
        this.Detail[0].product_price[1].price = this.pricetier_2
        this.Detail[0].product_price[2].price = this.pricetier_3
        this.Detail[0].product_price[3].price = this.pricetier_4
        this.Detail[0].product_price[4].price = this.pricetier_5
        this.Detail[0].product_price[5].price = this.pricetier_6
        await this.$store.dispatch('AddProductList', this.Detail)
        var res = await this.$store.state.Seller.DataSweetAlert
        if (res.result === 'SUCCESS') {
          this.$swal({ text: `${res.message}`, type: 'success', timer: 1500, showConfirmButton: false }).catch(() => {})
          this.$router.push('seller')
        } else if (res.result === 'FAILED') {
          this.$swal({ text: `${res.message}`, type: 'error', timer: 1500, showConfirmButton: false }).catch(() => {})
        }
      }
    },
    Cancle () {
      // this.$store.commit('MutationsModalAddProduct')
      this.$router.push('seller')
    }
  }
}
</script>
<style scoped>
.border_image {
  border: 1px solid whitesmoke
}
.f-right {
  float: right;
}
.div_overflow {
  overflow: auto;
  width:100%;
  height:89vh
}
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}
</style>
<style>
.IZ-select .IZ-select__input input {
  height: 32px !important;
}
.IZ-select__input input {
  font-size: 13px !important;
}
</style>
