<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">จัดการรูปแบบการอนุมัติ</v-card-title>
        <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> จัดการรูปแบบการอนุมัติ</v-card-title>
      <v-card-text>
        <!-- 1st row header search -->
        <v-row no-gutters>
          <!-- search -->
          <v-col cols="12" md="6" sm="6" :class="MobileSize ? 'pl-2 pr-2 mb-3' : IpadSize ? 'mb-3 mt-1' : 'pt-3'">
            <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากรูปแบบการอนุมัติ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- btn -->
          <v-col cols="12" md="6" sm="6" class="pt-0" :class="!MobileSize ? 'fontSizeDetail':''" align="end">
            <v-btn dense :block="MobileSize" color="#27AB9C" :class="MobileSize ? 'mt-2 white--text' : IpadSize ? 'mt-2 pl-4 pr-4 white--text' : 'ml-4 mt-2 pl-8 pr-8 white--text'" @click="createOrEditPositition('','create')">
            <v-icon>mdi-plus</v-icon>
            เพิ่มรายการ</v-btn>
          </v-col>
        </v-row>
        <!-- count list -->
        <v-row no-gutters>
          <v-col cols="8" md="12" sm="12" :class="!MobileSize ? 'pr-2 mb-0 mt-5' : 'pl-2 pr-2 mb-3 mt-3'">
            <span :class="MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">รายการผู้อนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
          </v-col>
        </v-row>
        <!-- 2nd row count list, table list -->
        <v-row no-gutters v-if="showTable === true">
          <!-- table -->
          <v-col cols="12" md="12" sm="12" class="pt-2">
            <v-data-table
            :headers="headers"
            :items="listPositionData"
            :search="search"
            style="width:100%;"
            height="100%"
            :page.sync="page"
            no-results-text="ไม่พบรายการผู้อนุมัติ"
            no-data-text="ไม่มีรายการผู้อนุมัติ"
            :update:items-per-page="itemsPerPage"
            @pagination="ApprovePosition"
            :items-per-page="10"
            :class="!IpadSize || !IpadProSize || !MobileSize ? '': ''"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.name`]="{ item }">
                <p class="mt-3">{{item.name}}
                  <span>{{checkType(item.type)}}</span>
                </p>
              </template>
              <template v-slot:[`item.manages`]="{ item }">
                <v-row>
                  <v-col cols="6" md="6" align="center">
                    <v-btn :style="IpadSize ? 'border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;' : ''" :class="MobileSize ? 'mb-2' : IpadSize ? 'px-0 mr-2' : 'ma-2'"  :icon="IpadSize ? true : false" :tile="IpadSize ? true : false" :outlined="IpadSize ? true : false" @click="createOrEditPositition(item.id, 'edit')"><v-icon color="#27AB9C">mdi-pencil-outline</v-icon></v-btn>
                    <span
                      v-if="!MobileSize"
                      :style="IpadSize ? 'mt-4' : ''"
                      style="line-height: 22px; color:  #27AB9C; cursor: pointer; font-weight: 700px;"
                      @click="createOrEditPositition(item.id, 'edit')"
                      >แก้ไข
                    </span>
                  </v-col>
                  <v-col cols="6" md="6" align="center" v-if="item.type !== 'no'">
                    <v-btn :style="IpadSize ? 'border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;' : ''" :class="MobileSize ? 'mb-2' : IpadSize ? 'px-0 mr-2' : 'ma-2'" :icon="IpadSize ? true : false" :outlined="IpadSize ? true : false" @click="goDetail(item.id)"><v-icon color="#27AB9C">mdi-file-document-outline</v-icon></v-btn>
                    <span
                      v-if="!MobileSize"
                      style="line-height: 22px; color: #27AB9C; cursor: pointer; font-weight: 700px;"
                      @click="goDetail(item.id)"
                      >รายละเอียด
                    </span>
                  </v-col>
                  <v-col cols="6" md="6" align="center" v-if="item.type === 'no'">
                    <v-btn :style="IpadSize ? 'border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;' : ''" :class="MobileSize ? 'mb-2' : IpadSize ? 'px-0 mr-2' : 'ma-2'" :icon="IpadSize ? true : false" :outlined="IpadSize ? true : false" disabled><v-icon color="#27AB9C">mdi-file-document-outline</v-icon></v-btn>
                    <span
                      v-if="!MobileSize"
                      style="line-height: 22px; color: #9A9A9A; font-weight: 700px;"
                      >รายละเอียด
                    </span>
                  </v-col>
                  <!-- <v-col cols="12" md="6" align="left">
                    <span class="ma-2"
                      style="line-height: 22px; color:  #27AB9C; cursor: pointer"
                      @click="goDetail(item.id)"
                      >รายละเอียด <v-icon size="15" color="#27AB9C">
                        mdi-chevron-right</v-icon>
                    </span>
                  </v-col> -->
                </v-row>
              </template>
              <!-- <template v-slot:[`item.delete`]>
                <v-row justify="end">
                  <v-btn><v-icon color="#27AB9C">mdi-delete-outline</v-icon></v-btn>
                </v-row>
              </template> -->
            </v-data-table>
          </v-col>
        </v-row>
        <v-row no-gutters v-if="showTable === false">
          <v-col cols="12" md="12" sm="12" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการผู้อนุมัติ</b></h2>
          </v-col>
        </v-row>
        <!-- dialog Create or Edit Register Position-->
        <v-dialog v-model="modalCreateOrEdit" :style="MobileSize ? 'z-index: 16000004' : ''" width="800px" persistent>
          <v-form ref="form" lazy-validation>
            <v-card width="100%" height="100%" class="rounded-lg">
              <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                  <font color="#27AB9C" v-if="createOrEditStatus === 'edit'">แก้ไขรูปแบบการอนุมัติ</font>
                  <font color="#27AB9C" v-else>เพิ่มตำแหน่งการอนุมัติ</font>
                </span>
                <v-btn icon dark @click="closeDialog()">
                  <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card-text>
                <v-row :class="!MobileSize ? 'pl-10 pr-10 mt-10' : ''" no-gutters>
                  <v-col cols="6" md="4" sm="4" class="mt-3"><span :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'"><b>รูปแบบการอนุมัติ :</b></span></v-col>
                  <v-col cols="6" md="8" sm="8">
                    <v-select
                      v-model = selectedPositionType
                      :rules="[v => !!v || 'เลือกรูปแบบการอนุมัติ']"
                      :items = positionType
                      item-text="text"
                      item-value="value"
                      label = "เลือกรูปแบบการอนุมัติ"
                      outlined
                    ></v-select>
                  </v-col>
                </v-row>
                <v-row :class="!MobileSize ? 'pl-10 pr-10 mt-10' : ''" no-gutters>
                  <v-col cols="6" md="4" sm="4" class="mt-3"><span :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'"><b>ชื่อรูปแบบการอนุมัติ :</b></span></v-col>
                  <v-col cols="6" md="8" sm="8">
                    <v-text-field
                      v-model=selectedPositionName
                      :rules="[v => !!v || 'ระบุชื่อตำแหน่งการอนุมัติ']"
                      label="ระบุชื่อตำแหน่งการอนุมัติ"
                      placeholder="ระบุชื่อตำแหน่งการอนุมัติ"
                      outlined>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row :class="!MobileSize ? 'pl-10 pr-10 mt-10' : ''" no-gutters v-if="selectedPositionType === 'many'">
                  <v-col cols="6" md="4" sm="4" class="mt-3"><span :class="!MobileSize ? 'fontSizeTitle2':'fontSizeTitleMobile2'"><b>วงเงิน :</b></span></v-col>
                  <v-col cols="6" md="8" sm="8">
                    <v-text-field
                      v-model=selectedPositionBudget
                      :rules="[v => !!v || 'ระบุวงเงิน', v => v>0 || 'วงเงินต้องมากกว่า 0']"
                      label="ระบุวงเงิน"
                      placeholder="ระบุวงเงิน"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0/, '')"
                      outlined>
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions>
                <v-container style="display: flex; justify-content: flex-end">
                  <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeDialog()">
                    ยกเลิก
                  </v-btn>
                  <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="openConfirmDialog()">
                    บันทึก
                  </v-btn>
                </v-container>
              </v-card-actions>
            </v-card>
          </v-form>
        </v-dialog>
        <!-- dialog confirm save data -->
        <v-dialog v-model="dialogConfirmSave" :style="MobileSize ? 'z-index: 16000004' : ''" width="500px" persistent>
          <v-card width="100%" height="100%" min-height="246px" class="rounded-lg">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px" v-if="createOrEditStatus === 'create'">
                <font color="#27AB9C">เพิ่มตำแหน่งการอนุมัติ</font>
              </span>
              <span class="flex text-center ml-5" style="font-size:20px" v-else>
                <font color="#27AB9C">แก้ไขตำแหน่งการอนุมัติ</font>
              </span>
              <v-btn icon dark @click="dialogConfirmSave = false, closeDialog()">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-container>
              <v-card-text :class="MobileSize ? 'pr-0 pl-0' : ''">
                <v-row justify="center" no-gutters dense v-if="createOrEditStatus === 'create'">
                  <v-col cols="12" align="center">
                    <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;">คุณต้องการเพิ่มรูปแบบการอนุมัติ <b>{{selectedPositionTypeTH}}</b></p>
                  </v-col>
                  <v-col cols="12" align="center">
                    <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">คุณต้องการทำรายการนี้ ใช่ หรือไม่</p>
                  </v-col>
                </v-row>
                <v-row justify="center" no-gutters dense v-else>
                  <v-col cols="12" align="center">
                    <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;">คุณต้องการแก้ไขรูปแบบการอนุมัติ <b>{{selectedPositionTypeTH}}</b></p>
                  </v-col>
                  <v-col cols="12" align="center">
                    <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">คุณต้องการทำรายการนี้ ใช่ หรือไม่</p>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions>
                <v-row justify="center" class="mb-4">
                  <v-btn class="mr-4" color="#27AB9C" dark outlined style="border: 1px solid #27AB9C; border-radius: 8px;" @click="dialogConfirmSave = false, closeDialog()">ยกเลิก</v-btn>
                  <v-btn color="#27AB9C" dark style="border: 1px solid #27AB9C; border-radius: 8px;" @click="save()">ตกลง</v-btn>
                </v-row>
              </v-card-actions>
            </v-container>
          </v-card>
        </v-dialog>
        <!-- dialog save success data -->
        <v-dialog v-model="dialogSuccess" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="373">
          <v-card style="background: #FFFFFF; border-radius: 4px;" min-height="246px">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px">
                <font color="#27AB9C" v-if="createOrEditStatus === 'edit'">แก้ไขรูปแบบการอนุมัติ</font>
                <font color="#27AB9C" v-else>เพิ่มตำแหน่งการอนุมัติ</font>
              </span>
              <v-btn icon dark @click="dialogSuccess = false, getPosition()">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-container>
              <v-card-text>
                <v-row justify="center" no-gutters dense>
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
                  </v-col>
                  <v-col cols="12" align="center" class="mt-6">
                    <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">เสร็จสิ้น</p>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-container>
          </v-card>
        </v-dialog>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      // default data
      company_id: '',
      com_perm_id: '',

      // modal
      modalCreateOrEdit: false,
      dialogConfirmSave: false,
      dialogSuccess: false,
      showCountOrder: 0,
      // default table
      showTable: true,
      showCountTableList: 0,
      search: '',
      page: 1,
      itemsPerPage: 10,

      // table position list
      listPositionData: [],
      headers: [
        { text: 'รูปแบบการอนุมัติ', value: 'name', sortable: false, width: '35%', class: 'backgroundTable fontTable--text' },
        // { text: 'id', value: 'id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manages', align: 'center', width: '65%', sortable: false, class: 'backgroundTable fontTable--text' }
      ],

      // selected data
      positionType: [{ value: 'all', text: 'อนุมัติทั้งหมด' }, { value: 'one', text: 'อนุมัติ 1 คน' }, { value: 'many', text: 'ระบุวงเงิน' }, { value: 'no', text: 'ไม่มีผู้อนุมัติ' }],
      createOrEditStatus: '',
      selectedPositionID: '',
      selectedPositionName: '',
      selectedPositionType: '',
      selectedPositionTypeTH: '',
      selectedPositionBudget: 0,
      selectedPositionDetail: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listApprovePositionMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listApprovePosition' }).catch(() => {})
      }
    }
  },
  created () {
    localStorage.removeItem('positionID')
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    this.company_id = companyId.company.company_id
    this.com_perm_id = companyId.position.com_perm_id
    this.getPosition()
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    ApprovePosition (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (msg === 'Data missing. Please check your [" company_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company Permission not found.') {
        return 'ไม่พบสิทธิ์ผู้ใช้องค์กรนี้'
      } else if (msg === 'This is not you Company Permission.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ใช่ของคุณ'
      } else if (msg === 'This Permission not in your Company.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ได้อยู่ในองค์กรของคุณ'
      } else if (msg === 'Data missing. Please check your [" com_perm_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสสิทธิ์ผู้ใช้องค์กร ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Purchaser not found.') {
        return 'ไม่พบข้อมูลผู้ซื้อองค์กร'
      } else if (msg === 'Budjet Can not be 0.') {
        return 'วงเงินไม่สามารถเป็น 0 ได้'
      } else {
        return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
      }
    },
    checkType (val) {
      let type = ''
      if (val === 'all') {
        type = '(อนุมัติทั้งหมด)'
      } else if (val === 'one') {
        type = '(อนุมัติ 1 คน)'
      } else if (val === 'many') {
        type = '(ระบุวงเงิน)'
      } else {
        type = ''
      }
      return type
    },
    async getPosition () {
      this.$store.commit('openLoader')
      var data = {
        company_id: this.company_id,
        com_perm_id: this.com_perm_id
      }
      await this.$store.dispatch('actionsListApprovePosition', data)
      var response = await this.$store.state.ModuleApprove.stateListApprovePosition
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listPositionData = response.data.company_approve_position_data
        this.showCountTableList = Object.keys(this.listPositionData).length
        if (this.showCountTableList !== 0) {
          this.showTable = true
        } else {
          this.showTable = false
        }
      } else {
        this.$store.commit('closeLoader')
        var msg = this.getErrorMsg(response.message)
        this.$swal.fire({
          icon: 'error',
          text: msg,
          showConfirmButton: false,
          timer: 1500
        })
      }
      // console.log('response from API', response)
      // console.log('date send to API', data)
    },
    async createOrEditPositition (val, status) {
      // reset data
      this.selectedPositionName = ''
      this.selectedPositionType = ''
      this.selectedPositionBudget = 0

      this.createOrEditStatus = status
      if (status === 'edit') {
        this.selectedPositionDetail = []
        this.selectedPositionID = val
        this.listPositionData.forEach((element) => {
          if (element.id === val) {
            this.selectedPositionDetail = element
          }
        })
        this.selectedPositionName = this.selectedPositionDetail.name
        this.selectedPositionType = this.selectedPositionDetail.type
        this.selectedPositionBudget = this.selectedPositionDetail.budget
      }
      this.modalCreateOrEdit = true
      // console.log('create/edit', this.createOrEditStatus)
      // console.log('data', val, this.selectedPositionDetail)
    },
    async openConfirmDialog () {
      if (this.$refs.form.validate(true)) {
        if (this.selectedPositionType === 'all') {
          this.selectedPositionTypeTH = 'อนุมัติทั้งหมด'
        } else if (this.selectedPositionType === 'one') {
          this.selectedPositionTypeTH = 'อนุมัติ 1 คน'
        } else if (this.selectedPositionType === 'many') {
          this.selectedPositionTypeTH = 'ระบุวงเงิน'
        }
        this.dialogConfirmSave = true
        this.modalCreateOrEdit = false
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async save () {
      var data = {
        company_id: this.company_id,
        com_perm_id: this.com_perm_id,
        approve_position_name: this.selectedPositionName,
        approve_position_type: this.selectedPositionType,
        approve_position_budget: this.selectedPositionBudget
      }
      var response = ''
      if (this.createOrEditStatus === 'create') {
        await this.$store.dispatch('actionsCreateApprovePosition', data)
        response = await this.$store.state.ModuleApprove.stateCreateApprovePosition
      } else if (this.createOrEditStatus === 'edit') {
        data = {
          company_id: this.company_id,
          com_perm_id: this.com_perm_id,
          approve_position_id: this.selectedPositionID,
          approve_position_name: this.selectedPositionName,
          approve_position_type: this.selectedPositionType,
          approve_position_budget: this.selectedPositionBudget
        }
        await this.$store.dispatch('actionsEditApprovePosition', data)
        response = await this.$store.state.ModuleApprove.stateEditApprovePosition
      }

      this.closeDialog()
      if (response.result === 'SUCCESS') {
        this.dialogSuccess = true
      } else {
        var msg = this.getErrorMsg(response.message)
        this.$swal.fire({
          icon: 'error',
          text: msg,
          showConfirmButton: false,
          timer: 1500
        })
      }
      this.dialogConfirmSave = false
      // console.log('data sent to API', data)
      // console.log('res from API', response)
    },
    closeDialog () {
      this.$refs.form.resetValidation()
      this.selectedPositionID = ''
      this.selectedPositionName = ''
      this.selectedPositionType = ''
      this.selectedPositionBudget = ''
      this.modalCreateOrEdit = false
    },
    goDetail (val) {
      localStorage.setItem('positionID', val)
      if (this.MobileSize === true) {
        this.$router.push({ path: `/detailPositionMobile?positionID=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/detailPosition?positionID=${val}` }).catch(() => {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(2) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(2) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style>
.fontSizeTitle {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 32px; color: #333333;
}
.fontSizeTitle2 {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile2 {
  font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}

</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
