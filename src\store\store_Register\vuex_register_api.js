import AxiosRegister from './axios_register_api'

const ModuleRegister = {
  state: {
    stateGetSellerShop: [],
    stateRegisterbuyer: [],
    stateConfirmOTPOneID: [],
    stateGetBizDetail: [],
    stateLoginUsername: [],
    stateRegisterLineOA: [],
    stateLoginUsernameTestOneID: []
  },
  mutations: {
    mutationsGetSellerShop (state, data) {
      state.stateGetSellerShop = data
    },
    mutationsRegisterbuyer (state, data) {
      state.stateRegisterbuyer = data
    },
    mutationsConfirmOTPOneID (state, data) {
      state.stateConfirmOTPOneID = data
    },
    mutationsGetBizDetail (state, data) {
      state.stateGetBizDetail = data
    },
    mutationsLoginUsername (state, data) {
      state.stateLoginUsername = data
    },
    mutationsRegisterLineOA (state, data) {
      state.stateRegisterLineOA = data
    },
    mutationsLoginUsernameTestOneID (state, data) {
      state.stateLoginUsernameTestOneID = data
    }
  },
  actions: {
    async actionsGetSellerShop (context, access) {
      const responseGetSellerShop = await AxiosRegister.GetSellerShop(access)
      // console.log('test', responseGetSellerShop)
      await context.commit('mutationsGetSellerShop', responseGetSellerShop)
    },
    async actionsRegisterbuyer (context, access) {
      const responseGetSellerShop = await AxiosRegister.PostRegisterbuyer(access)
      await context.commit('mutationsRegisterbuyer', responseGetSellerShop)
    },
    async actionsConfirmOTPOneID (context, access) {
      const confirmOTPOneID = await AxiosRegister.ConfirmOTPOneID(access)
      await context.commit('mutationsConfirmOTPOneID', confirmOTPOneID)
    },
    async actionsConGetBizDetail (context, access) {
      const confirmOTPOneID = await AxiosRegister.GetBizDetail(access)
      await context.commit('mutationsGetBizDetail', confirmOTPOneID)
    },
    async actionsLoginUsername (context, access) {
      const confirmOTPOneID = await AxiosRegister.LoginUsername(access)
      await context.commit('mutationsLoginUsername', confirmOTPOneID)
    },
    async actionsRegisterLineOA (context, { access, uuid }) {
      const confirmOTPOneID = await AxiosRegister.RegisterLineOA(access, uuid)
      await context.commit('mutationsRegisterLineOA', confirmOTPOneID)
    },
    async actionsLoginUsernameTestOneID (context, access) {
      const confirmOTPOneID = await AxiosRegister.LoginUsernameTestOneID(access)
      await context.commit('mutationsLoginUsernameTestOneID', confirmOTPOneID)
    }
  }
}
export default ModuleRegister
