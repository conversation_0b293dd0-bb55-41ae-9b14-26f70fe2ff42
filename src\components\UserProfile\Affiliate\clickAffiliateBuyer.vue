<template>
  <v-container width="100%" height="100%" style="background: #FFFFFF; border: 0px solid; border-radius: 8px;">
    <v-card class="mb-3 MobileSize" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" id="filterData">
        <v-row no-gutters class="d-flex" align="center">
            <v-col cols="7">
                <v-card-title class="pl-0" style="font-weight: 700; font-size: 22px; line-height: 32px;" v-if="!MobileSize">{{ $t('clickAffiliateBuyer.Title') }}</v-card-title>
                <v-card-title class="px-0" style="font-weight: 700; font-size: 18px; line-height: 32px;" v-else>
                  <v-icon color="#1AB759" class="mr-2" @click="backToUsr()">mdi-chevron-left</v-icon>{{ $t('clickAffiliateBuyer.Title') }}
                </v-card-title>
            </v-col>
        </v-row>
    </v-card>
    <!-- filter ข้อมูล -->
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-if="!MobileSize">
        <v-row justify="center" class="mr-3">
            <v-col cols="5">
                <v-row dense>
                  <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-2 pr-2 pt-2"> {{ $t('clickAffiliateBuyer.ClickTime') }} : </span>
                  <v-dialog v-model="modalDateSelect" class="d-inline-block" width="480px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field v-model="rangeDate" readonly dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.ClickTimePlaceholder')" v-bind="attrs" v-on="on">
                        <v-spacer></v-spacer>
                        <v-icon slot="append">mdi-calendar-month</v-icon>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="dates"
                      full-width
                      range
                      reactive
                      no-title
                      :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                      :min="minDate"
                      :max="
                        new Date(
                          Date.now() - new Date().getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .substr(0, 10)
                      ">
                      <v-row>
                        <v-col align="end">
                          <v-btn text color="primary" @click="closeDialog(dates)">{{ $t('clickAffiliateBuyer.Cancel') }}</v-btn>
                          <v-btn text color="primary" @click="saveDialog(dates)">{{ $t('clickAffiliateBuyer.Confirm') }}</v-btn>
                        </v-col>
                      </v-row>
                    </v-date-picker>
                  </v-dialog>
                </v-row>
            </v-col>
            <v-col cols="5">
                <v-row dense>
                  <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pr-2 pt-2"> {{ $t('clickAffiliateBuyer.ClickCode') }} :</span>
                  <v-text-field v-model="searchClickId" dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.ClickCodePlaceholder')"></v-text-field>
                </v-row>
            </v-col>
        </v-row>
        <v-row justify="center" class="mt-1 mr-3">
            <v-col cols="5">
                <v-row dense>
                    <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pr-2 pt-2"> {{ $t('clickAffiliateBuyer.Region') }} :</span>
                    <v-text-field v-model="searchRegion" dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.RegionPlaceholder')"></v-text-field>
                </v-row>
            </v-col>
            <v-col cols="5">
                <v-row dense>
                    <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-2 pt-2 pr-2"> {{ $t('clickAffiliateBuyer.SubId') }} :</span>
                    <v-text-field v-model = "searchSubId" dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.SubIdPlaceholder')"></v-text-field>
                </v-row>
                </v-col>
        </v-row>
        <v-row dense justify="center" class="mt-1" >
          <v-btn dark style="border-radius: 40px; background: red;" height="32px" @click="clearSearch">
            <v-icon small class="mr-1">mdi-restart</v-icon>{{ $t('clickAffiliateBuyer.Clear') }}</v-btn>
        </v-row>
    </v-card>

    <!-- MobileSize -->
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-else>
        <v-row no-gutters>
            <v-row no-gutters>
              <v-col cols="4" class="pt-2">
                <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-8"> {{ $t('clickAffiliateBuyer.ClickTime') }} : </span>
              </v-col>
              <v-col cols="7">
                <v-dialog v-model="modalDateSelect" class="d-inline-block" width="480px">
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field hide-details v-model="rangeDate" readonly dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.ClickTimePlaceholder')" v-bind="attrs" v-on="on">
                      <v-spacer></v-spacer>
                      <v-icon slot="append">mdi-calendar-month</v-icon>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="dates"
                    full-width
                    range
                    reactive
                    no-title
                    :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                    :min="minDate"
                    :max="
                      new Date(
                        Date.now() - new Date().getTimezoneOffset() * 60000
                      )
                        .toISOString()
                        .substr(0, 10)
                    ">
                    <v-row>
                      <v-col align="end">
                        <v-btn text color="primary" @click="closeDialog(dates)">{{ $t('clickAffiliateBuyer.Cancel') }}</v-btn>
                        <v-btn text color="primary" @click="saveDialog(dates)">{{ $t('clickAffiliateBuyer.Confirm') }}</v-btn>
                      </v-col>
                    </v-row>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
            <v-row dense no-gutters>
              <v-col cols="4" class="pt-2">
                <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-8"> {{ $t('clickAffiliateBuyer.ClickCode') }} :</span>
              </v-col>
              <v-col cols="7">
                <v-text-field v-model="searchClickId" dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.ClickCodePlaceholder')"></v-text-field>
              </v-col>
            </v-row>
            <v-row dense no-gutters>
              <v-col cols="4" class="pt-2">
                <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-5"> {{ $t('clickAffiliateBuyer.Region') }} :</span>
              </v-col>
              <v-col cols="7">
                <v-text-field v-model="searchRegion" dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.RegionPlaceholder')"></v-text-field>
              </v-col>
            </v-row>
            <v-row dense no-gutters>
              <v-col cols="4" class="pt-2">
                <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-9"> {{ $t('clickAffiliateBuyer.SubId') }} :</span>
              </v-col>
              <v-col cols="7">
                <v-text-field v-model = "searchSubId" dense outlined style="border-radius: 8px;" :placeholder="$t('clickAffiliateBuyer.SubIdPlaceholder')"></v-text-field>
              </v-col>
            </v-row>
        </v-row>
        <v-row dense justify="center" class="mt-1" >
                <v-btn dark style="border-radius: 40px; background: red;" height="32px" @click="clearSearch">
                  <v-icon small class="mr-1">mdi-restart</v-icon>{{ $t('clickAffiliateBuyer.Clear') }}</v-btn>
        </v-row>
    </v-card>

    <!-- ตารางข้อมูล -->
    <v-card class="mb-2 mt-4" style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col class="mt-6" cols="9">
          <span class="ml-2" style=" font-size: 16px; font-style: normal; font-weight: 600; color: #27AB9C">{{ $t('clickAffiliateBuyer.TotalResult') }} ({{ filterItems.length}} {{ $t('clickAffiliateBuyer.Items') }})</span>
        </v-col>
        <v-col class="mt-4 d-flex justify-end" cols="3">
          <v-btn style="border-radius: 40px; background: #27AB9C;" height="32px" @click="getReportExcel()">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1" style="font-size: 14px; font-style: normal; font-weight: 500; color: #FFFFFF">{{ $t('clickAffiliateBuyer.Export') }}</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <v-card outlined class="mb-4 mt-2" v-if="disableTable === true">
      <v-data-table :headers="header" :items="filterItems" elevation="1" :no-data-text="$t('clickAffiliateBuyer.NoClickData')" :height="MobileSize ? '390px':''">
          <template v-slot:[`item.click_time`]="{ item }">
              {{new Date(item.click_time).toLocaleDateString($i18n.locale === 'th' ? 'th-TH' : 'en-US', { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
          </template>
          <!-- <template v-slot:[`item.header`]="{ item }">
          </template> -->
      </v-data-table>
    </v-card>
   <v-row justify="center" align-content="center" v-if="disableTable === false">
    <v-col cols="12" align="center">
      <div class="my-5">
        <v-img
          src="@/assets/emptypo.png"
          max-height="500px"
          max-width="500px"
          height="100%"
          width="100%"
          contain
          aspect-ratio="2">
        </v-img>
      </div>
      <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c">
        <b>{{ $t('clickAffiliateBuyer.EmptyMessage') }}</b>
      </h2>
    </v-col>
   </v-row>
  </v-container>
</template>
<script>

import { Decode } from '@/services'
export default {
  data () {
    return {
      time: [],
      header: [
        {
          text: this.$t('clickAffiliateBuyer.ClickCode'),
          align: 'center',
          sortable: false,
          width: '200',
          value: 'click_id',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('clickAffiliateBuyer.ClickTime'),
          align: 'center',
          sortable: false,
          width: '150',
          value: 'click_time',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('clickAffiliateBuyer.Region'),
          align: 'center',
          sortable: false,
          width: '150',
          value: 'region',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('clickAffiliateBuyer.SubId'),
          align: 'center',
          sortable: false,
          width: '150',
          value: 'sub_id',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('clickAffiliateBuyer.Reference'),
          align: 'center',
          sortable: false,
          width: '150',
          value: 'browser',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      disableTable: false,
      searchClickId: '',
      searchRegion: '',
      searchSubId: '',
      searchRangeDate: [],
      dates: [],
      tableData: [],
      totalLists: 0,
      modalDateSelect: false,
      DataTables: [],
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31'
    }
  },
  mounted () {
    var element = document.getElementById('filterData')
    if (element) {
      window.scrollTo({
        top: element.offsetTop,
        behavior: 'auto'
      })
    }
  },
  created () {
    this.checkConsent()
    this.$EventBus.$emit('changeNavAccount')
    this.getClickReportData()
    // this.$EventBus.$emit('changeNav')
  },
  computed: {
    rangeDate () {
      return this.dates.map(date => new Date(date).toLocaleDateString(this.$i18n.locale === 'th' ? 'th-TH' : 'en-US')).join(' - ')
    },
    filterItems () {
      return this.tableData.filter(item => {
        const itemDate = new Date(item.click_time)
        const startDate = new Date(this.searchRangeDate[0])
        const endDate = new Date(this.searchRangeDate[1])
        // console.log('itemDate', itemDate)
        // console.log('startDate', startDate)
        // console.log('endDate', endDate)
        if (this.searchRangeDate.length === 0) {
          return item.click_id.toLowerCase().trim().includes(this.searchClickId.toLowerCase().trim()) &&
            item.region.toLowerCase().trim().includes(this.searchRegion.toLowerCase().trim()) &&
            item.sub_id.toLowerCase().trim().includes(this.searchSubId.toLowerCase().trim())
        }
        if (this.searchRangeDate.length === 1) {
          return item.click_id.toLowerCase().trim().includes(this.searchClickId.toLowerCase().trim()) &&
            item.region.toLowerCase().trim().includes(this.searchRegion.toLowerCase().trim()) &&
            item.sub_id.toLowerCase().trim().includes(this.searchSubId.toLowerCase().trim()) &&
            item.click_time.includes(this.searchRangeDate)
        } else {
          return item.click_id.toLowerCase().trim().includes(this.searchClickId.toLowerCase().trim()) &&
            item.region.toLowerCase().trim().includes(this.searchRegion.toLowerCase().trim()) &&
            item.sub_id.toLowerCase().trim().includes(this.searchSubId.toLowerCase().trim()) &&
            (itemDate >= startDate && itemDate <= endDate)
        }
      })
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ClickBuyerAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ClickBuyerAffiliate' }).catch(() => {})
      }
    }
  },
  methods: {
    backToUsr () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async getClickReportData () {
      this.$store.commit('openLoader')
      var userId = JSON.parse(Decode.decode(localStorage.getItem('oneData'))) // ห้ามลบ!!!!!!
      var data = {
        user_id: userId.user.user_id // ห้ามลบ!!!!!!
        // user_id: 119
      }
      await this.$store.dispatch('actionClickReportList', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateClickReportList
      if (response.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        if (response.success === true) {
          this.tableData = response.data
          for (var i = 0; i < this.tableData.length; i++) {
            if (this.tableData[i].click_time.length > 10) {
              this.tableData[i].click_time = this.tableData[i].click_time.slice(0, 10)
            }
          }
          if (response.data.length > 0) {
            this.disableTable = true
          }
        }
      }
      this.$store.commit('closeLoader')
      // if (response.length === '' || response.length === 'null' || response.length === 'undefined') {
      //   this.totalLists = 0
      // } else {
      //   this.totalLists = this.filterItems.length
      // }
    },
    async getReportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var userId = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = userId.user.user_id
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}affiliate/buyer/clickReportExcel/${data}`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'click-report.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    saveDialog (date) {
      date.sort((a, b) => a.localeCompare(b))
      this.dates = date
      this.searchRangeDate = this.dates
      this.modalDateSelect = false
    },
    closeDialog (date) {
      // date = []
      this.searchRangeDate = date
      this.dates = date
      this.modalDateSelect = false
    },
    clearSearch () {
      this.searchClickId = ''
      this.searchRegion = ''
      this.searchSubId = ''
      this.searchRangeDate = []
      this.dates = []
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
