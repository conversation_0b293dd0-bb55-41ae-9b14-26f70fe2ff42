<template>
  <v-container class="pa-2">
    <div :class="MobileSize ? 'background_color_Mobile' : 'background_color px-0 py-0'">
      <v-card elevation="0" :class="MobileSize ? 'pa-4' : 'pa-6'" width="100%" min-height="1500px">

        <v-row>
          <v-icon v-if="MobileSize" color="#27AB9C" class="" @click="backToMain()">mdi-chevron-left</v-icon>
          <v-col cols="6" align="start" :class="MobileSize ? 'titleee_Mobile' : 'titleee'"><span>เพิ่มบทความ</span></v-col>
        </v-row>

        <v-form ref="FormCreateNews" :lazy-validation="lazy">
          <v-row dense class="mt-4">
            <v-radio-group v-model="newsType" row>
              <v-radio value="imageType" label="เพิ่มรูปภาพหลักของบทความ" class="mr-2"></v-radio>
              <v-radio value="videoType" label="เพิ่มวิดีโอหลักของบทความ"></v-radio>
            </v-radio-group>
          </v-row>

          <div v-if="newsType === 'imageType'" id="img" class="mt-4">
            <v-row class="mb-4" v-for="(item, index) in img" :key="index">
              <v-col v-if="index === 0" cols="12" :class="MobileSize ? 'subTitle_Mobile' : 'subTitle'"><span>เพิ่มรูปภาพหลักของบทความ</span></v-col>
              <v-col v-else-if="index === 1" cols="12" :class="MobileSize ? 'subTitle_Mobile' : 'subTitle'"><span>เพิ่มรูปภาพของบทความเพิ่มเติม</span></v-col>
              <v-col cols="12" md="12" class="pt-0">
                <v-card v-if="item.name === ''" elevation="0" :style="noFirstImage && img[0].data === '' && index === 0 ? 'border-color: red' : 'border-color: #27AB9C;'" style="border: 2px dashed; box-sizing: border-box; border-radius: 8px;">
                  <input
                  type="file"
                  class="file"
                  accept="image/*"
                  @change="uploadImage($event, index)"
                  style="opacity: 0; width: 100%; height: 280px; position: absolute; cursor: pointer;z-index: 1;"
                  >
                  <v-col cols="12" md="12">
                    <v-row justify="center" align="center">
                      <v-col cols="12" md="12" align="center">
                        <v-img
                          src="@/assets/upload.png"
                          width="280.34"
                          height="154.87"
                          contain
                        ></v-img>
                      </v-col>
                      <v-col cols="12" md="12" style="text-align: center;">
                        <span style="line-height: 24px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                        <span style="line-height: 24px; font-weight: 400; color: #333333;"
                          :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                        <span style="line-height: 16px; font-weight: 400; color: #989898;"
                          :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'" class="mt-4">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                      </v-col>
                    </v-row>
                  </v-col>
                </v-card>
                <v-card v-else elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;">
                  <v-card-text>
                    <div class="mt-4">
                      <v-col cols="12" md="12" align="center">
                        <v-card outlined class="pa-1" width="247" height="247">
                            <v-img :src="item.path" :lazy-src="item.path" width="247" height="247" contain>
                              <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                <v-icon x-small color="white" dark
                                  @click="removeImage(item, index)">mdi-close</v-icon>
                              </v-btn>
                            </v-img>
                          </v-card>
                      </v-col>
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </div>

          <div v-else style="padding-top: 20px">
            <span>URL ของวิดีโอ <span style="color:#FF0000">*</span></span>
            <!-- <v-text-field v-model="videoURL" :rules="rule.linkYoutubeCantEmpty(videoURL)" placeholder="URL ของวิดีโอ" dense outlined></v-text-field> -->
            <v-text-field v-model="videoURL" :rules="rules.linkYoutubeCantEmpty" placeholder="URL ของวิดีโอ" dense outlined></v-text-field>
          </div>

          <div id="data">
            <v-row>
              <v-col cols="12"><span :class="MobileSize ? 'titleee_Mobile' : 'titleee'">ข้อมูลทั่วไป</span></v-col>
              <v-col cols="12" class="detail">
                <span>หัวข้อบทความ <span style="color: #FF0000;">*</span></span>
                <v-text-field  v-model="newsHeader" :rules="rules.newsHeader" placeholder="เพิ่มหัวข้อบทความ" @keypress="CheckSpacebar($event)" outlined dense>
                </v-text-field>
              </v-col>
              <v-col cols="12" class="detail">
                <span>คำอธิบายบทความโดยย่อ <span style="color: #FF0000;">*</span></span>
                <v-textarea v-model="shortDescription" :rules="rules.shortDescription" @keypress="CheckSpacebar($event)" placeholder="เพิ่มคำอธิบายบทความโดยย่อ" background-color="#FFFFFF" outlined></v-textarea>
              </v-col>
              <v-col cols="12" class="detail">
                <span>คำอธิบายบทความ <span style="color: #FF0000;">*</span></span>
                <div :class="checkDescription && description === '' ? 'borderError' : ''">
                  <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="description" @ready="onReady"></ckeditor>
                </div>
                <div v-if="checkDescription && description === ''" class="textError mt-2">กรุณากรอกคำอธิบายบทความ</div>
              </v-col>
            </v-row>
          </div>

          <div id="product">
            <v-row dense>
              <v-col cols="12" class="mt-4">
                <span :class="MobileSize ? 'subTitle_Mobile' : 'subTitle'">เพิ่มสินค้าที่เกี่ยวข้อง</span>
                <v-card elevation="0" class="px-0">
                  <!-- tab and product card -->
                  <v-tabs v-model="activeTab" class="mb-2" background-color="transparent" grow show-arrows>
                    <div v-if="!MobileSize" class="row">
                      <v-tab ref="summaryTab1" @click="clickTab1()" href="#tab1"> รายการสินค้าที่แสดง</v-tab>
                      <v-tab ref="summaryTab2" @click="clickTab2()" href="#tab2"> สินค้าทั้งหมด </v-tab>
                    </div>
                    <div v-else class="row">
                      <v-tab ref="summaryTab1" @click="clickTab1()" value="tab1" href="#tab1"> รายการสินค้าที่แสดง</v-tab>
                      <v-tab style="font-size:14px" ref="summaryTab2" @click="clickTab1()" value="tab2" href="#tab2"> สินค้าทั้งหมด </v-tab>
                    </div>
                    <v-tabs-items touchless v-model="activeTab" style="background-color: transparent;">
                      <v-container fluid :class="MobileSize ? '' : ''">
                        <v-row>
                          <v-col cols="12" class="px-0">
                            <!-- tab1(inheader) -->
                            <v-tab-item key="tab1" value="tab1">

                              <v-row>
                                <v-col cols="6" md="3" sm="4" class="mt-3" align="end">
                                  <div id="All">
                                    <v-select
                                    v-model="filterCatelog"
                                    :items="selectProducts"
                                    item-text="text"
                                    item-value="value"
                                    @change="filterCategory"
                                    label=""
                                    height="40"
                                    append-icon="mdi-chevron-down"
                                    class="setCustomSelect"
                                    dense
                                    outlined
                                    hide-details
                                    ></v-select>
                                  </div>
                                </v-col>
                                <v-col cols="6" md="4" sm="6" class="mt-3" align="start">
                                  <div id="All-sub" style="margin-left: -24px;">
                                    <v-text-field
                                    v-model="filterWord"
                                    placeholder="พิมพ์ชื่อสินค้า"
                                    class="ml-0"
                                    dense
                                    outlined
                                    hide-details
                                    ></v-text-field>
                                  </div>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && !MobileSize && !IpadSize && !IpadProSize">
                                <v-col cols="12" md="3" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && !MobileSize && !IpadSize && IpadProSize">
                                <v-col cols="12" md="4" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && !MobileSize && IpadSize && !IpadProSize">
                                <v-col cols="12" md="4" sm="6" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && MobileSize && !IpadSize && !IpadProSize">
                                <v-col cols="6" md="6" sm="6" xs="6" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row justify="start" class="mx-2" v-if="!showSkeletonLoader && showProduct.length !== 0 && !MobileSize && !IpadSize && !IpadProSize">
                                <v-container>
                                  <v-row>
                                    <draggable v-model="showProductSelect" :disabled="filterCatelog !== '' || filterWord !== ''? true : false" @start="drag=true" @end="drag=false" class="row fill-height align-center sortable-list ma-0" :scroll-sensitivity="150" :force-fallback="true" :animetion="200">
                                      <v-col cols="12" md="3" v-for="(item, index) in paginated" :key="index">
                                        <Cards :itemProduct="item" align="start" />
                                      </v-col>
                                    </draggable>
                                  </v-row>
                                </v-container>
                              </v-row>
                              <v-row justify="start" class="mx-2" v-else-if="!showSkeletonLoader && showProduct.length !== 0 && !MobileSize && !IpadSize && IpadProSize">
                                <v-container>
                                  <v-row>
                                    <draggable v-model="showProductSelect" :disabled="filterCatelog !== '' || filterWord !== ''? true : false" @start="drag=true" @end="drag=false" class="row fill-height align-center sortable-list ma-0" :scroll-sensitivity="150" :force-fallback="true" :animetion="200">
                                      <v-col cols="12" md="4" v-for="(item, index) in paginated" :key="index">
                                        <Cards :itemProduct="item" align="start" />
                                      </v-col>
                                    </draggable>
                                  </v-row>
                                </v-container>
                              </v-row>
                              <v-row justify="start" class="mx-0" v-else-if="!showSkeletonLoader && showProduct.length !== 0 && !MobileSize && IpadSize && !IpadProSize">
                                <v-container>
                                  <v-row>
                                    <draggable v-model="showProductSelect" :disabled="filterCatelog !== '' || filterWord !== ''? true : false" @start="drag=true" @end="drag=false" class="row fill-height align-center sortable-list ma-0" :scroll-sensitivity="150" :force-fallback="true" :animetion="200">
                                      <v-col cols="12" md="4" sm="6" v-for="(item, index) in paginated" :key="index">
                                        <CardsResponsive :itemProduct="item" align="start" />
                                      </v-col>
                                    </draggable>
                                  </v-row>
                                </v-container>
                              </v-row>
                              <v-row justify="start" v-else-if="!showSkeletonLoader && showProduct.length !== 0 && MobileSize && !IpadSize && !IpadProSize">
                                <v-container>
                                  <v-row>
                                    <draggable v-model="showProductSelect" :disabled="filterCatelog !== '' || filterWord !== ''? true : false" @start="drag=true" @end="drag=false" class="row fill-height align-center sortable-list ma-0" :scroll-sensitivity="150" :force-fallback="true" :animetion="200">
                                      <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index">
                                        <CardsResponsive :itemProduct="item" align="start" />
                                      </v-col>
                                    </draggable>
                                  </v-row>
                                </v-container>
                              </v-row>
                              <v-row justify="center" v-if="!showSkeletonLoader && showProduct.length === 0">
                                <v-col cols="12" md="12" align="center">
                                  <v-img src="@/assets/box.png" width="400" height="300" contain></v-img><br />
                                  <p :style="MobileSize ? 'font-size: 16px;' : 'font-size: 20px;'" style="font-weight: 400;  line-height: 26px; color: #C4C4C4;">คุณยังไม่ได้ทำการเพิ่มสินค้าที่ต้องการแสดง</p>
                                </v-col>
                              </v-row>

                              <v-row v-if="showProduct.length !== 0 && paginated.length === 0" justify="center">
                                <p>ไม่มีรายการที่ค้นหา</p>
                              </v-row>

                            </v-tab-item>

                            <!-- tab2 (all) -->
                            <v-tab-item  key="tab2" value="tab2">
                              <v-row>
                                <v-col cols="6" md="3" sm="4" class="mt-3" align="end">
                                  <div id="All">
                                    <v-select
                                      v-model="filterCatelog2"
                                      :items="selectProducts"
                                      item-text="text"
                                      item-value="value"
                                      @change="filterCategoryT2"
                                      label=""
                                      height="40"
                                      append-icon="mdi-chevron-down"
                                      class="setCustomSelect"
                                      dense
                                      outlined
                                      hide-details
                                    ></v-select>
                                  </div>
                                </v-col>
                                <v-col cols="6" md="4" sm="6" class="mt-3" align="start">
                                  <div id="All-sub" style="margin-left: -24px;">
                                    <v-text-field
                                    v-model="filterWord2"
                                    placeholder="พิมพ์ชื่อสินค้า"
                                    dense
                                    outlined
                                    hide-details
                                    class="ml-0"
                                    ></v-text-field>
                                  </div>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && !MobileSize && !IpadSize && !IpadProSize">
                                <v-col cols="12" md="3" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && !MobileSize && !IpadSize && IpadProSize">
                                <v-col cols="12" md="4" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && !MobileSize && IpadSize && !IpadProSize">
                                <v-col cols="12" md="4" sm="6" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row v-if="showSkeletonLoader && MobileSize && !IpadSize && !IpadProSize">
                                <v-col cols="6" md="6" sm="6" xs="6" v-for="n in 4" :key="n">
                                  <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
                                </v-col>
                              </v-row>
                              <v-row justify="start" class="mx-2" v-if="!showSkeletonLoader && paginatedT2.length !== 0 && !MobileSize && !IpadSize && !IpadProSize">
                                <v-col cols="12" md="3" v-for="(item, index) in paginatedT2" :key="index">
                                  <Cards :itemProduct="item" align="start" />
                                </v-col>
                              </v-row>
                              <v-row justify="start" class="mx-2" v-else-if="!showSkeletonLoader && paginatedT2.length !== 0 && !MobileSize && !IpadSize && IpadProSize">
                                <v-col cols="12" md="4" v-for="(item, index) in paginatedT2" :key="index">
                                  <Cards :itemProduct="item" align="start" />
                                </v-col>
                              </v-row>
                              <v-row justify="start" class="mx-0" v-else-if="!showSkeletonLoader && paginatedT2.length !== 0 && !MobileSize && IpadSize && !IpadProSize">
                                <v-col cols="12" md="4" sm="6" v-for="(item, index) in paginatedT2" :key="index">
                                  <CardsResponsive :itemProduct="item" align="start" />
                                </v-col>
                              </v-row>
                             <v-row justify="start" v-else-if="!showSkeletonLoader && paginatedT2.length !== 0 && MobileSize && !IpadSize && !IpadProSize">
                                <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginatedT2" :key="index">
                                  <CardsResponsive :itemProduct="item" align="start" />
                                </v-col>
                              </v-row>
                              <v-row v-if="!showSkeletonLoader && paginatedT2.length === 0" justify="center">
                                <p>ไม่มีรายการที่ค้นหา</p>
                              </v-row>
                            </v-tab-item>

                          </v-col>
                        </v-row>
                      </v-container>
                    </v-tabs-items>
                  </v-tabs>

                  <!-- pagination ใช้ด้วยกันเลย -->
                  <v-row dense justify="center" class="my-6 py-4">
                    <v-col cols="12" align="center">
                      <v-pagination
                        color="#27AB9C"
                        v-model="pageNumber"
                        :length="checkLengthPagination(activeTab)"
                        :total-visible="7"
                      ></v-pagination>
                    </v-col>
                  </v-row>

                </v-card>
              </v-col>
            </v-row>
          </div>

          <v-card-actions>
            <v-row class="" :justify="MobileSize ? 'center' : 'end'" dense>
              <v-btn outlined height="48" :width="MobileSize ? '120' : '140'" dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="backToMain()">ยกเลิก</v-btn>
              <v-btn height="48" :width="MobileSize ? '120' : '140'" color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="showDialogCF()">ยืนยัน</v-btn>
            </v-row>
          </v-card-actions>
        </v-form>

      </v-card>

      <v-dialog v-model="dialogConfirmCreate" width="464" persistent>
        <v-card style="background: #FFFFFF; border-radius: 12px;" height="100%">
          <v-toolbar align="center" color="#C8F3E5" dark dense elevation="0">
            <span class="flex text-center" style="font-weight: 700; font-size: 18px; line-height: 24px; color: #27AB9C;">
              เพิ่มบทความ
            </span>
            <v-btn icon dark @click="dialogConfirmCreate = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row dense justify="center">
              <v-col cols="12" align="center" class="mt-8 mb-8">
                <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="">คุณได้ตรวจสอบข้อมูลในบทความของคุณเรียบร้อยแล้ว</span><br/>
                <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="pt-4">และต้องการดำเนินการเพิ่มบทความใช่ หรือไม่?</span>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-row class="mb-2" justify="center" dense>
              <v-btn outlined height="48" width="110" dense rounded dark color="#27AB9C" class="mr-4" @click="dialogConfirmCreate = false">ยกเลิก</v-btn>
              <v-btn height="48" width="110" color="#27AB9C" dark dense rounded class="" @click="handleConfirm()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

    </div>
  </v-container>
</template>

<script>
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
import draggable from 'vuedraggable'
export default {
  components: {
    Cards: () => import('@/components/Card/ProductCardArticle.vue'),
    CardsResponsive: () => import('@/components/Card/ProductCardResponsiveArticle.vue'),
    draggable
  },
  data () {
    return {
      lazy: false,
      overlay2: true,
      showSkeletonLoader: true,
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'imageUpload',
          '|',
          'blockquote',
          'inserttable',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      },
      rules: {
        newsHeader: [
          v => !!v || 'กรุณากรอกหัวข้อบทความ'
        ],
        shortDescription: [
          v => !!v || 'กรุณากรอกคำอธิบายบทความโดยย่อ'
        ],
        linkYoutubeCantEmpty: [
          v => !!v || 'กรุณากรอก URL ของวิดีโอ'
        ]
      },
      newsType: 'imageType',
      videoURL: '',
      checkDescription: false,
      noFirstImage: false,
      img: [{ name: '', path: '', data: '' }, { name: '', path: '', data: '' }],
      newsHeader: '',
      shortDescription: '',
      description: '',

      // ข้อมูลสำหรับการเพิ่มสินค้า
      activeTab: 'tab1',
      selectProducts: [], // หมวดหมู่สินค้า

      showProduct: [], // array ที่แสดงใน tab\
      IdList: [], // array ที่จะส่งให่หลังบ้าน
      AllProduct: [],

      filterCatelog: '',
      filterCatelog2: '',
      filterWord: '',
      filterWord2: '',

      productCount: null,
      pageMax: null,
      current: 1,
      pageSize: 16,
      countPage: 1,
      dialogConfirmCreate: false,

      // img
      header_image: '',
      banner_image: '',

      // สำหรับการเลือก category
      category_id: '',
      category_idT2: '',
      getAllProduct: [],
      showProductSelect: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.filterWord !== '') {
        return this.showProductSelect.filter(item => {
          return item.name.toLowerCase().includes(this.filterWord.toLocaleLowerCase())
        })
      } else {
        return this.showProductSelect.slice(this.indexStart, this.indexEnd)
      }
    },
    paginatedT2 () {
      if (this.filterWord2 !== '') {
        return this.AllProduct.filter(item => {
          return item.name.toLowerCase().includes(this.filterWord2.toLocaleLowerCase())
        })
      } else {
        return this.AllProduct.slice(this.indexStart, this.indexEnd)
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.getAll()
    this.getDataCategory()
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('addRelated', this.addRelated)
    this.$EventBus.$on('RemoveRelated', this.RemoveRelated)
    this.$EventBus.$on('addItemLanding', this.addItem)
    this.$EventBus.$on('RemoveItemLanding', this.RemoveItem)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('addItemLanding')
      this.$EventBus.$off('RemoveRelated')
      this.$EventBus.$off('addItem')
      this.$EventBus.$off('RemoveItem')
    })
  },
  watch: {
    paginated (val) {
      if (this.filterWord !== '') {
        this.pageMax = parseInt(this.showProduct.length / 16) === 0 ? 1 : Math.ceil(this.showProduct.length / 16)
      } else {
        this.pageMax = parseInt(this.showProduct.length / 16) === 0 ? 1 : Math.ceil(this.showProduct.length / 16)
      }
    },
    paginatedT2 (val) {
      if (this.filterWord2 !== '') {
        this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
      } else {
        this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
      }
    }
  },
  methods: {
    backToMain () {
      if (this.MobileSize) {
        this.$router.push({ path: '/manageArticleMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageArticle' }).catch(() => {})
      }
    },
    onReady (editor) {
      editor.execute('heading', { value: 'heading2' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    async uploadImage (e, index) {
      const shopId = localStorage.getItem('shopSellerID')
      const file = document.querySelector('.file')
      const element = e.target.files[0]

      // ตรวจสอบประเภทไฟล์
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
        var url = URL.createObjectURL(element)
        const image = new Image()

        // รอโหลดรูปเพื่อดึงขนาด
        const imageDimensions = await new Promise((resolve) => {
          image.onload = () => {
            const dimensions = {
              height: image.height,
              width: image.width
            }
            resolve(dimensions)
          }
          image.src = url
        })

        // ตรวจสอบขนาดรูปภาพ
        if (imageDimensions.height < 620 && imageDimensions.width < 1480) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = async () => {
            var resultReader = reader.result
            this.img[index].name = element.name
            this.img[index].path = URL.createObjectURL(element)
            this.img[index].data = resultReader.split(',')[1]

            // กำหนดประเภทที่ส่งขึ้นอยู่กับ index
            const type = index === 0 ? 'header_image_article' : 'banner_image_article'

            // สร้างข้อมูลสำหรับการอัปโหลด
            const data = {
              image: [this.img[index].data],
              type: type,
              seller_shop_id: shopId
            }

            await this.$store.dispatch('actionsUploadToS3', data)
            const response = await this.$store.state.ModuleShop.stateUploadToS3

            if (response.message === 'List Success.') {
              this.img[index].uploadedPath = response.data.list_path[0].path

              // เก็บ uploadedPath เพื่อส่งไป backend
              if (index === 0) {
                this.header_image_video = this.img[index].uploadedPath
              } else if (index === 1) {
                this.banner_image = this.img[index].uploadedPath
              }
            }
          }
        } else {
          file.value = ''
          this.$swal.fire({ icon: 'warning', text: 'โปรดใช้รูปตามขนาดที่กำหนด', showConfirmButton: false, timer: 1500 })
        }
      } else {
        file.value = ''
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
      }
    },
    removeImage (item, index) {
      this.img[index].name = ''
      this.img[index].path = ''
      this.img[index].data = ''
    },
    checkLengthPagination (activeTab) { // คำนวณจำนวน pagination
      let max = 1 // กำหนดค่าเริ่มต้นให้กับ max

      // คำนวณจำนวนหน้าสำหรับแต่ละแท็บ
      if (activeTab === 'tab1') {
        max = Math.ceil(this.showProductSelect.length / this.pageSize) // ใช้ showProduct สำหรับ tab1
      } else if (activeTab === 'tab2') {
        max = Math.ceil(this.AllProduct.length / this.pageSize) // ใช้ AllProduct สำหรับ tab2
      }

      // อัปเดต pageNumber ให้ไม่เกิน max
      this.pageNumber = Math.min(this.pageNumber, max)
      return max // คืนค่าจำนวนสูงสุดของหน้า
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    showDialogCF () {
      this.dialogConfirmCreate = true
    },
    confirm () {
      this.checkDescription = this.description === ''
      this.noFirstImage = this.newsType === 'imageType' ? this.img[0].data === '' : false
      if (this.$refs.FormCreateNews.validate(true) && this.description && !this.noFirstImage) {
        this.dialogConfirmCreate = true
      } else {
        if (this.noFirstImage) {
          window.scrollTo(0, 0)
        } else if (!this.description) {
          document.getElementById('data').scrollIntoView()
        } else {
          this.$nextTick(() => {
            const el = document.getElementsByClassName('error--text')
            if (el) {
              document
                .getElementsByClassName('error--text')[0]
                .scrollIntoView({ behavior: 'smooth', block: 'end' })
            }
          })
        }
      }
    },
    async handleConfirm () {
      // ตรวจสอบประเภทที่เลือกและเรียกฟังก์ชันที่เหมาะสม
      if (this.newsType === 'imageType') {
        await this.confirmCreate()
      } else if (this.newsType === 'videoType') {
        await this.confirmCreateVDO()
      }

      this.dialogConfirmCreate = false
    },
    async confirmCreate () {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      this.checkDescription = this.description === ''
      this.noFirstImage = this.newsType === 'imageType' ? this.img[0].data === '' : false
      if (this.$refs.FormCreateNews.validate(true) && this.description && !this.noFirstImage) {
        this.$store.commit('openLoader')
        // this.IdList = []
        // this.showProduct.forEach(element => {
        //   this.IdList.push(element.id)
        // })
        this.IdList = this.showProductSelect.map(element => element.id)
        var data = {
          seller_shop_id: shopId,
          title: this.newsHeader,
          shot_description: this.shortDescription,
          description: this.description,
          header_image_video: this.header_image_video,
          banner_image: this.banner_image,
          product_id_activity: this.IdList
        }
        await this.$store.dispatch('actionsAddArticleV2Image', data)
        var res = await this.$store.state.ModuleArticle.stateAddArticleV2Image
        if (res.message === 'Add Article Success.') {
          this.$store.commit('closeLoader')
          this.dialogConfirmCreate = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'เพิ่มบทความสำเร็จ'
          }).then(() => {
            if (this.MobileSize) {
              this.$router.push('/manageArticleMobile')
            } else {
              this.$router.push('/manageArticle')
            }
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    },
    async confirmCreateVDO () {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      this.checkDescription = this.description === ''
      this.noFirstImage = this.newsType === 'imageType' ? this.img[0].data === '' : false
      if (this.$refs.FormCreateNews.validate(true) && this.description && !this.noFirstImage) {
        this.$store.commit('openLoader')
        // this.IdList = []
        // this.showProduct.forEach(element => {
        //   this.IdList.push(element.id)
        // })
        this.IdList = this.showProductSelect.map(element => element.id)
        var data = {
          seller_shop_id: shopId,
          title: this.newsHeader,
          shot_description: this.shortDescription,
          description: this.description,
          header_image_video: this.videoURL,
          product_id_activity: this.IdList
        }
        await this.$store.dispatch('actionsAddArticleV2Video', data)
        var res = await this.$store.state.ModuleArticle.stateAddArticleV2Video
        if (res.message === 'Add Article Success.') {
          this.$store.commit('closeLoader')
          this.dialogConfirmCreate = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'เพิ่มบทความสำเร็จ'
          }).then(() => {
            if (this.MobileSize) {
              this.$router.push('/manageArticleMobile')
            } else {
              this.$router.push('/manageArticle')
            }
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    },
    clickTab1 () {
      this.activeTab = 'tab1'
      this.getAll()
    },
    clickTab2 () {
      this.activeTab = 'tab2'
      this.getAll()
    },
    async filterCategory () {
      // console.log('first')
      this.category_id = this.filterCatelog === 'ทั้งหมด' ? '' : this.filterCatelog
      await this.getAll()
    },
    async filterCategoryT2 () {
      // console.log('second')
      this.category_idT2 = this.filterCatelog2 === 'ทั้งหมด' ? '' : this.filterCatelog2
      // console.log('2', this.filterCatelog2)
      await this.getAll()
    },
    async getAll () { // สินค้าทั้งหมด
      // console.log('5', this.category_id)
      // console.log('6', this.category_idT2)
      // console.log('7', this.activeTab)
      this.showSkeletonLoader = true
      const shopId = localStorage.getItem('shopSellerID')
      const data = {
        seller_shop_id: shopId,
        // category_id: this.category_id
        category_id: this.activeTab === 'tab1' ? this.category_id : this.category_idT2
      }

      await this.$store.dispatch('actionsListProductManageArticle', data)
      var response = await this.$store.state.ModuleArticle.stateListProductManageArticle

      if (response.message === 'Get Product Success.') {
        // console.log(response)
        this.showSkeletonLoader = false
        this.overlay2 = false
        this.getAllProduct = response.data

        if (response.data.length !== 0) {
          this.AllProduct = response.data.filter(e => {
            if (e.id !== parseInt(this.productId) && !this.IdList.includes(e.id)) {
              return true
            }
            return false
          }).map(e => {
            return {
              short_description: e.short_description,
              discount_percent: e.discount_percent,
              name: e.name,
              have_attribute: e.have_attribute,
              real_price: e.real_price,
              fake_price: e.fake_price,
              vat_include: e.vat_include,
              vat_default: e.vat_default,
              id: e.id,
              sku: e.sku,
              images_URL: e.images_URL,
              message_status: e.message_status,
              stars: e.stars,
              stock_status: e.stock_status,
              stock_count: e.stock_count,
              inventory_stock: e.inventory_stock,
              sold: e.sold,
              txt: false
            }
          })
          // console.log('111')
          // console.log('8', this.showProduct)
          // if (this.getAllProduct.length !== 0) {
          //   if (this.showProduct.length !== 0) {
          //     this.showProductSelect = this.getAllProduct.filter(product =>
          //       this.showProduct.some(showProd => showProd.id === product.id)
          //     )
          //   }
          // }
          if (this.getAllProduct.length !== 0) {
            if (this.showProduct.length !== 0) {
              // เพื่อให้สินค้าแสดงตาม response ที่ได้มา
              this.showProductSelect = this.showProduct.map(showProd =>
                this.getAllProduct.find(product => product.id === showProd.id)
              ).filter(product => product !== undefined)
            }
          }
          // console.log('9', this.AllProduct)
          this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
          if (this.pageMax === 1) {
            this.pageNumber = 1
          }
          this.productCount = response.data.length
          this.countPage = this.countPage + 1
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showProductSelect = []
          this.getAllProduct = []
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async getDataCategory () {
      var data = 'all'
      this.selectProducts = []
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.message === 'Get all category success.') {
        // console.log(response)
        this.selectProducts = response.data
        const categoryList = response.data.map(category => ({
          text: category.category_name,
          value: category.id
        }))

        this.selectProducts = [{ text: 'ทั้งหมด', value: '' }, ...categoryList]
      }
    },
    // ไม่ได้ใช้แต่เปิดไว้เพราะ Card มีการเรียกใช้เดี่ยวมันแตก
    async addRelated (index) {
    },
    async RemoveRelated (index) {
    },
    async addItem (ids) { // เลือกสินค้าที่จะเพิ่ม
      // เปิดใช้เป็น evenbus เรียกจาก Card ที่ import มา
      // console.log(this.IdList.length)
      if (this.IdList.findIndex((x) => x === ids.id) === -1) {
        ids.txt = true // เปลี่ยนปุ่มเป็น -
        // this.showProduct.push(ids) // เพิิ่มเข้าไปใน array ที่โชว์ใน tab1
        this.showProductSelect.push(ids) // เพิิ่มเข้าไปใน array ที่โชว์ใน tab1
        this.IdList.push(ids.id) // เพิ่มเข้าไปใน array ที่จะส่งให้หลังบ้าน

        // ลบ สิ้นค้าที่เลือกออกจากลิสต์ สินค้าทั้งหมด
        this.AllProduct.forEach((element, index) => {
          if (element.id === ids.id) {
            this.AllProduct.splice(index, 1)
          }
        })
      } else {
        this.$swal.fire({ icon: 'error', text: 'มีรายการสินค้าอยู่แล้ว', timer: 2000, showConfirmButton: false })
      }
    },
    async RemoveItem (ids) { // ลบสินค้าที่เลือกออก
      // เปิดใช้เป็น evenbus เรียกจาก Card ที่ import มา
      // console.log('สินค้าทั้งหมด', this.AllProduct.length)

      ids.txt = false // เปลี่ยนปุ่มเป็น +

      // ลบสินค้าใน array ที่โชว์ใน tab1
      this.showProductSelect.forEach((element, index) => {
        if (element.id === ids.id) {
          this.showProductSelect.splice(index, 1)
          this.IdList.splice(index, 1)
        }
      })
      this.showProduct = this.showProductSelect
      // ลบสินค้าใน array ลิสต์ สินค้าทั้งหมด และเรียงลำดับตาม id
      this.AllProduct.push(ids)
      this.AllProduct.sort((a, b) => a.id - b.id)
      // console.log('สินค้าทั้งหมด', this.AllProduct.length)
    }
  }
}
</script>

<style scoped>
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #C8F3E5;
  border-radius: 8px;
}
.titleee {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.titleee_Mobile {
  font-weight: 700; font-size: 18px; line-height: 24px; color: #333333;
}
.subTitle {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.subTitle_Mobile {
  font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;
}
.detail {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.textError {
  color: red; font-size: 12px; line-height: 18px; padding: 0 12px;
}
.borderError {
  border-radius: 6px;
  border: 2px solid red;
}
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
}
::v-deep #All-sub > .v-text-field--outlined > .v-input__control > .v-input__slot {
  height: 40px;
  min-height: 40px;
  border-top-right-radius: 500px 500px;
  border-bottom-right-radius: 500px 500px;
}
::v-deep #All > .v-text-field--outlined > .v-input__control > .v-input__slot {
  height: 40px;
  min-height: 40px;
  border-top-left-radius: 500px 500px;
  border-bottom-left-radius: 500px 500px;
  padding-top: 0px;
  padding-bottom: 0px;
}
::v-deep #ert > .v-text-field--outlined > .v-input__control > .v-input__slot {
  align-items: stretch;
  height: 40px;
  min-height: 40px;
  border-radius: 999px;
}
</style>

<style>
.v-text-field.v-text-field--enclosed .v-text-field__details {
  padding-top: 4px !important;
  /* margin-bottom: 8px; */
}
.ck-editor__editable {
  min-height: 210px;
  /* font-size: 14px !important; */
}
.v-textarea.v-text-field--box.v-text-field--single-line:not(.v-input--dense) .v-text-field__prefix, .v-textarea.v-text-field--box.v-text-field--single-line:not(.v-input--dense) .v-text-field__suffix, .v-textarea.v-text-field--box.v-text-field--single-line:not(.v-input--dense) textarea, .v-textarea.v-text-field--box.v-text-field--outlined:not(.v-input--dense) .v-text-field__prefix, .v-textarea.v-text-field--box.v-text-field--outlined:not(.v-input--dense) .v-text-field__suffix, .v-textarea.v-text-field--box.v-text-field--outlined:not(.v-input--dense) textarea, .v-textarea.v-text-field--enclosed.v-text-field--single-line:not(.v-input--dense) .v-text-field__prefix, .v-textarea.v-text-field--enclosed.v-text-field--single-line:not(.v-input--dense) .v-text-field__suffix, .v-textarea.v-text-field--enclosed.v-text-field--single-line:not(.v-input--dense) textarea, .v-textarea.v-text-field--enclosed.v-text-field--outlined:not(.v-input--dense) .v-text-field__prefix, .v-textarea.v-text-field--enclosed.v-text-field--outlined:not(.v-input--dense) .v-text-field__suffix, .v-textarea.v-text-field--enclosed.v-text-field--outlined:not(.v-input--dense) textarea {
  height: 98px;
}
.ck.ck-sticky-panel .ck.ck-sticky-panel__content_sticky * {
  display: none !important;
}
</style>
