import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async CreatePointSet (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_seller_shop_point`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditSellerShopPoint (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_seller_shop_point`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getListUserPointByUserID (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_list_user_point_by_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getListUserPointBySellerShopID (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_list_user_point_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getSellerShopPointDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_seller_shop_point_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getDetailUserPointByUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_detail_user_point_by_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
