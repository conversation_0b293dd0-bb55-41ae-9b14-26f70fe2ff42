<template>
    <div></div>
</template>

<script>
export default {
  data () {
    return {
      link: '',
      isAndroid: false,
      isiOS: false,
      URL: '',
      URLToStore: ''
    }
  },
  async created () {
    this.link = window.location.href
    let isAppOpened = false
    let timeout
    const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
    this.isAndroid = /Android/i.test(userAgent)
    this.isiOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream
    this.URL = ''
    this.URLToStore = ''
    var dataGetlinkMobile = {
      type: 'home'
    }
    // แก้ api
    await this.$store.dispatch('actionsGetLinkMobile', dataGetlinkMobile)
    const response = await this.$store.state.ModuleHompage.stateGetLinkMobile
    if (this.isAndroid === true) {
      this.URL = response.data.intentLink
    } else if (this.isiOS === true) {
      this.URL = response.data.short_url
      this.URL = this.URL.replace(/https/gi, 'nexgencommerce')
      this.URLToStore = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
    } else {
      this.URL = this.link.replace(/linkToApp/gi, '')
    }
    const onVisibilityChange = () => {
      // console.log(document.hidden)
      if (document.hidden) {
        isAppOpened = true
        clearTimeout(timeout)
      }
    }

    // console.log(this.URL, this.URLToStore)
    document.addEventListener('visibilitychange', onVisibilityChange)

    const now = Date.now()
    window.location.href = this.URL

    if (this.isiOS === true) {
      timeout = setTimeout(() => {
        const elapsed = Date.now() - now
        if (!isAppOpened && elapsed < 5000) {
          window.location.href = this.URLToStore
          // isRedirected = true
        }
        document.removeEventListener('visibilitychange', onVisibilityChange)
      }, 4000)
    }
    // window.location.replace(URL)
  }
}
</script>
