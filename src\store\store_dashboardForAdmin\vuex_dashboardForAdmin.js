import axios from './axios_dashboardForAdmin'

const ModuleDashBoardForAdmin = {
  state: {
    stateListAllShopData: []
  },
  mutations: {
    mutationsListAllShopData (state, data) {
      state.stateListAllShopData = data
    }
  },
  actions: {
    async actionListAllShopData (context, access) {
      const responseData = await axios.ListAllShopData(access)
      await context.commit('mutationsListAllShopData', responseData)
    }
  }
}
export default ModuleDashBoardForAdmin
