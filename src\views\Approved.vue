<template>
  <v-container>
    <v-row>
      <v-col cols="12" md="3">
        <v-card class="mt-6" max-height="100%" height="700px">
          <v-list nav>
            <v-list-group
             v-for="item in items"
             :key="item.key"
             v-model="item.active"
             :prepend-icon="item.action"
             no-action
             color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title v-text="item.title"></v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="selectedItem"
                mandatory
              >
                <v-list-item
                 v-for="child in item.items"
                 :key="child.key"
                 color="#27AB9C"
                 dense
                 class="pl-16"
                >
                  <v-list-item-content @click="changePath(child.path)">
                    <v-list-item-action v-text="child.title"></v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9">
        <v-main style="padding: 0px;">
          <v-container>
            <v-card max-height="100%" height="100%" class="mt-3">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      selectedItem: 0,
      items: [
        {
          action: 'mdi-account',
          active: true,
          key: 1,
          items: [
            { key: 0, title: 'รายการสั่งซื้อสินค้า', path: 'approved' }
          ],
          title: 'บัญชีของฉัน'
        }
        // {
        //   action: 'mdi-file-document-outline',
        //   key: 2,
        //   items: [{ key: 5, title: 'รายการสั่งซื้อของฉัน', path: 'pobuyeruiProfile' }],
        //   title: 'รายการสั่งซื้อ'
        // }
      ]
    }
  },
  methods: {
    changePath (val) {
      this.$router.push({ path: `${val}` }).catch(() => {})
    }
  }
}
</script>
