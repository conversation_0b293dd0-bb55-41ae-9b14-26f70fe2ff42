<template>
  <v-container>
    <v-row dense justify="center" class="mt-4">
      <v-col cols="12" md="12" sm="12" xs="12" v-if="!MobileSize">
        <v-img :src="require('@/assets/ImageINET-Marketplace/Payment/Thaidotcom-2.webp')" style="cursor: pointer" @click="gotoPage()"></v-img>
      </v-col>
      <v-col cols="12" md="12" sm="12" v-else>
        <v-img :src="require('@/assets/ImageINET-Marketplace/Payment/paymentMobile.jpg')" style="cursor: pointer" @click="gotoPage()" max-height="100%" max-width="100vw"></v-img>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  methods: {
    gotoPage () {
      window.open('https://www.thaidotcompayment.co.th/')
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    } //
  }
}
</script>

<style scoped>
.container {
  max-width: 1250px;
}
</style>
