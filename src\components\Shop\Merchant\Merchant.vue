<template>
  <v-container style="min-height:650px" :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card width="100%" height="100%" elevation="0">
      <v-row dense class="mb-4" :class="MobileSize ? 'px-0' : 'px-0'">
        <v-col cols="12" class="mt-3">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">จัดการคูปอง</v-card-title>
          </v-row>
          <v-row v-else>
            <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> จัดการคูปอง</v-card-title>
          </v-row>
        </v-col>
      </v-row>
      <v-row dense v-if="merchantID === false" class="px-2">
        <v-col cols="12" align="end">
          <v-btn :block="MobileSize" dense color="#27AB9C" class="mt-0 py-5 white--text" @click="CreateMerchant()"><v-icon>mdi-plus</v-icon>สร้าง Merchant Id </v-btn>
        </v-col>
        <v-col cols="12" align="center" class="mt-6">
            <div class="my-5">
              <v-img src="@/assets/icon_image/store.png" max-height="320px" max-width="320px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>ไม่พบข้อมูล Merchant</b></h2>
          </v-col>
      </v-row>
      <v-row dense v-if="merchantID === true" class="mb-0 pb-0 px-3">
        <v-col :cols="MobileSize ? 8 : IpadSize ? 5 : 4" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-0 pt-3' : 'pl-2 pr-2 mb-0 pt-3'">
          <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากรายการคูปอง" outlined dense rounded hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col :cols="MobileSize ? 4 : IpadSize ? 3 : 2" class="" :class="MobileSize ? 'pl-2 pr-2 mb-0 pt-3' : IpadSize ? 'pl-2 pr-1 mb-0 pt-3' : 'pl-0 pr-3 mb-0 pt-3'">
          <v-btn dense :block="IpadSize" color="#27AB9C" class="pl-7 pr-7 mt-0 py-5 white--text" @click="searchCoupon()">ค้นหา </v-btn>
        </v-col>
        <v-col :cols="MobileSize ? 12 : IpadSize ? 4 : 6" align="end" :class="!MobileSize ? 'pl-0 mb-0 pt-3' : 'pl-2 mb-0 pt-3'">
          <v-btn dense :block="MobileSize" dark outlined color="#27AB9C" class="pl-4 pr-4 mt-0 py-5" @click="ManageCupon('Create', '')"><v-icon>mdi-plus</v-icon>สร้างคูปอง </v-btn>
        </v-col>
      </v-row>
      <v-row dense v-if="merchantID === true" class="px-3">
        <v-col cols="6" class="pt-2">
          <span :class="IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">คูปองทั้งหมด ({{DataCount}})</span>
        </v-col>
      </v-row>
      <v-card justify-center class="mt-5" max-width="100%" max-height="100%" elevation="0">
        <!-- <v-row dense v-if="merchantID === true" class="mb-0 pb-0 px-3">
          <v-col :cols="MobileSize ? 8 : IpadSize ? 9 : 4" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-0 pt-3' : 'pl-2 pr-2 mb-0 pt-3'">
            <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหารายการคูปอง" outlined dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col :cols="MobileSize ? 4 : IpadSize ?  3 : 2" class="" :class="MobileSize ? 'pl-2 pr-2 mb-0 pt-3' : IpadSize ? 'pl-2 pr-1 mb-0 pt-3' : 'pl-0 pr-3 mb-0 pt-3'">
            <v-btn dense :block="IpadSize" color="#27AB9C" class="pl-7 pr-7 mt-0 py-5 white--text" @click="searchCoupon()">ค้นหา </v-btn>
          </v-col>
          <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 6" align="end" :class="!MobileSize ? 'pl-0 mb-0 pt-3' : 'pl-2 mb-0 pt-3'">
            <v-btn dense :block="MobileSize" dark outlined color="#27AB9C" class="pl-4 pr-4 mt-0 py-5" @click="ManageCupon('Create', '')"><v-icon>mdi-plus</v-icon>สร้างคูปอง </v-btn>
          </v-col>
        </v-row> -->
        <!-- <v-row dense v-if="merchantID === true" class="px-3">
          <v-col cols="6" class="pt-2">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">คูปองทั้งหมด ({{DataCount}})</span>
          </v-col>
        </v-row> -->
        <v-card justify-center class="mt-5" max-width="100%" max-height="100%" elevation="0">
          <v-row no-gutters justify="center" v-if="merchantID === true && couponList.length === 0 && notFoundSearchCoupon !== true" style="height: 350px;">
            <v-col cols="12" align="center" class="mt-9">
              <v-img max-width="100" max-height="100" src="@/assets/coupon_image/empty_coupon.png" contain></v-img>
              <p class="mt-4 mb-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">คุณยังไม่มีคูปอง</p>
              <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">กรุณากดปุ่ม
                <span style="color:#27AB9C" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">“สร้างคูปอง”</span>
              </p>
            </v-col>
          </v-row>
          <v-row no-gutters justify="center" v-if="merchantID === true && couponList.length === 0 && notFoundSearchCoupon === true" style="height: 350px;">
            <v-col cols="12" align="center" class="mt-9">
              <v-img max-width="100" max-height="100" src="@/assets/coupon_image/empty_coupon.png" contain></v-img>
              <p class="mt-4 mb-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">ไม่พบคูปองที่คุณค้นหา</p>
            </v-col>
          </v-row>
          <v-row no-gutters v-if="merchantID === true && couponList.length !== 0 && notFoundSearchCoupon !== true">
            <v-col :cols="MobileSize ? 12 : IpadProSize ? 6 : IpadSize ? 6 : 4" class="pa-3" v-for="(item, index) in paginated" :key="index">
              <v-hover v-slot="{ hover }">
                <div class="card">
                  <div class="card_a" v-if="hover">
                    <v-btn class="mr-2 mt-2" style="display: absolute" color="#FFFFFF" fab x-small elevation="0" @click="viewDetailCoupon(item)"><v-icon color="#27AB9C">mdi-eye</v-icon></v-btn>
                    <v-btn class="mr-2 mt-2" style="display: absolute" color="#FFFFFF" fab x-small elevation="0" @click="ManageCupon('Edit', item)" :disabled="item.statusFormat === 'expire'"><v-icon color="#27AB9C">mdi-pencil-outline</v-icon></v-btn>
                    <v-btn class="mr-2 mt-2" style="display: absolute" color="#FFFFFF" fab x-small elevation="0" @click="confirmDeleteCoupon(item)"><v-icon color="#27AB9C">mdi-delete-outline</v-icon></v-btn>
                  </div>
                  <v-card class="rounded-lg" min-height="339px" height="100%" elevation="0" style="background: #FFFFFF; border: 1px solid #E0E0E0; border-radius: 8px;">
                    <v-img :src="item.couponImagePath" height="147" contain v-if="item.couponImagePath !== null" style="border-radius: 8px 8px 0px 0px;">
                      <v-chip v-if="item.statusFormat === 'expire'" class="ma-2" text-color="grey" color="#EEEEEE" small>
                        คูปองหมดอายุ
                      </v-chip>
                      <v-chip v-if="item.isOut === true" class="ma-2" text-color="grey" color="#EEEEEE" small>
                        คูปองหมด
                      </v-chip>
                    </v-img>
                    <v-img src="@/assets/NoImage.png" height="147" contain v-else style="border-radius: 8px 8px 0px 0px;">
                      <v-chip v-if="item.statusFormat === 'expire'" class="ma-2" text-color="grey" color="#EEEEEE" small>
                        คูปองหมดอายุ
                      </v-chip>
                      <v-chip v-if="item.isOut === true" class="ma-2" text-color="grey" color="#EEEEEE" small>
                        คูปองหมด
                      </v-chip>
                    </v-img>
                    <p class="px-3 pt-1 pb-0" :class="IpadSize ? 'coupon-title-Ipad' : 'coupon-title'" v-snip="1">{{ item.couponDetail.couponName }}</p>
                    <p class="px-3 pt-1" :class="IpadSize ? 'coupon-description-Ipad' : 'coupon-description'" v-snip="2" style="min-height: 40px;">{{ item.couponDetail.couponDescription }}</p>
                    <v-card-text class="px-2 pt-0 pb-0" :class="IpadSize ? 'coupon-date-Ipad' : 'coupon-date'">
                      <v-icon dense color="#27AB9C">mdi-clock-time-four-outline</v-icon> ระยะเวลาเก็บคูปอง :
                      <span style="color: #333333; font-weight: 600;">{{new Date(item.couponDate.collectStartDate).toLocaleDateString('en-GB', { timeZone: 'UTC'})}} {{ item.couponDate.collectEndDate === null ? 'ไม่ระบุวันสิ้นสุด' : '- ' + new Date(item.couponDate.collectEndDate).toLocaleDateString('en-GB', { timeZone: 'UTC'})}}</span>
                    </v-card-text>
                    <v-card-text class="px-2 pt-1" :class="IpadSize ? 'coupon-date-Ipad' : 'coupon-date'">
                      <v-icon dense color="#27AB9C">mdi-clock-time-four-outline</v-icon> ระยะเวลาใช้คูปอง :
                      <span style="color: #333333; font-weight: 600;">{{new Date(item.couponDate.useStartDate).toLocaleDateString('en-GB', { timeZone: 'UTC'})}} {{ item.couponDate.useEndDate === null ? 'ไม่ระบุวันสิ้นสุด' : '- ' + new Date(item.couponDate.useEndDate).toLocaleDateString('en-GB', { timeZone: 'UTC'})}}</span>
                    </v-card-text>
                      <v-row no-gutters class="pb-0 mt-3" style="background: #FAFAFA; border-radius: 0px 0px 8px 8px;">
                        <v-col align="center" class="pr-0 pt-3 pb-1" cols="4" style="color: #636363;" :style="IpadSize ? 'font-weight: 400; font-size: 14px; line-height: 22px;' : 'font-weight: 500; font-size: 16px; line-height: 24px;'">เก็บแล้ว
                          <v-divider class="float-right" vertical ></v-divider>
                        </v-col>
                        <v-col class="pl-0 pr-0 pt-3 pb-1" align="center" cols="4" style="color: #636363;" :style="IpadSize ? 'font-weight: 400; font-size: 14px; line-height: 22px;' : 'font-weight: 500; font-size: 16px; line-height: 24px;'">ใช้แล้ว
                          <v-divider class="float-right" vertical ></v-divider>
                        </v-col>
                        <v-col class="pl-0 pt-3 pb-1" align="center" cols="4" style="color: #636363;" :style="IpadSize ? 'font-weight: 400; font-size: 14px; line-height: 22px;' : 'font-weight: 500; font-size: 16px; line-height: 24px;'">เหลืออยู่</v-col>
                      </v-row>
                      <v-row no-gutters class="mt-0" style="background: #FAFAFA; border-radius: 0px 0px 8px 8px;">
                        <v-col class="pb-2" align="center" cols="4" style="color: #27AB9C; font-weight: 500; font-size: 16px; line-height: 24px;" :style="IpadSize ? '' : ''">{{ item.collectQuota - item.collectRemaining }}</v-col>
                        <v-col class="pb-2" align="center" cols="4" style="color: #F5222D; font-weight: 500; font-size: 16px; line-height: 24px;">{{ item.quota - item.remaining }}</v-col>
                        <v-col class="pb-2" align="center" cols="4" style="color: #52C41A; font-weight: 500; font-size: 16px; line-height: 24px;">{{ item.collectRemaining }}</v-col>
                      </v-row>
                  </v-card>
                </div>
              </v-hover>
            </v-col>
          </v-row>
            <v-row no-gutters :justify="MobileSize ? 'center' : 'end'" class="my-6">
              <v-pagination v-if="DataCount !== 0" color="#27AB9C" v-model="pageNumber" :length="pageMax" :total-visible="7" @change="pageChange()">
            </v-pagination>
          </v-row>
        </v-card>
        <CouponDetailModal ref="CouponDetailModal" />
      </v-card>
    </v-card>
  </v-container>
</template>

<script>
export default {
  components: {
    CouponDetailModal: () => import('@/components/Shop/Merchant/CouponDetailModal')
  },
  data () {
    return {
      shopDetail: '',
      merchantID: false,
      DataCount: 0,
      couponList: [],
      pageMax: null,
      current: 1,
      pageSize: 24,
      search: '',
      notFoundSearchCoupon: false
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.$EventBus.$on('GetCouponList', this.GetCouponList)
      this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.CheckMerchant()
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.couponList.slice(this.indexStart, this.indexEnd)
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/MerchantShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/MerchantShop' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async CheckMerchant () {
      var shopData = {
        seller_shop_id: this.shopDetail.id
      }
      await this.$store.dispatch('actionsCheckMerchant', shopData)
      var response = await this.$store.state.ModuleShop.stateCheckMerchant
      if (response.result === 'SUCCESS') {
        this.merchantID = true
        this.GetCouponList()
      }
      // else if (response.result === 'FAILED') {
      //   var msg = response.message
      //   if (response.message === 'This user is unauthorized.') {
      //     msg = 'ผู้ใช้งานไม่ได้รับอนุญาตในการใช้ระบบ'
      //     this.$EventBus.$emit('Logout')
      //   } else if (response.message === 'Data missing. Please check your parameter and try again.') {
      //     msg = 'ข้อมูลรหัสร้านค้าขาดหาย โปรดติดต่อเจ้าหน้าที่'
      //   } else if (response.message === 'Not Found Seller Shop') {
      //     msg = 'ไม่พบร้านค้านี้'
      //   } else if (response.message === 'Not found!. This user access denied.') {
      //     msg = 'ผู้ใช้ไม่มีสิทธิ์ในการสร้าง Merchant ID'
      //   } else if (response.message === 'Not found!. The user is not seller in this shop.') {
      //     msg = 'ผู้ใช้ไม่ได้อยู่ในร้านนี้'
      //   } else if (response.message === 'Shop owner position not found!.') {
      //     msg = 'เจ้าของร้านเท่านั้นที่มีสิทธิ์สร้าง Merchant ID'
      //   }
      //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', text: msg })
      // }
    },
    async CreateMerchant () {
      var shopData = {
        seller_shop_id: this.shopDetail.id
      }
      this.$swal.fire({
        icon: 'warning',
        title: 'ต้องการสร้าง Merchant id ใช่หรือไม่',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true,
        timer: 5000
      }).then(async (result) => {
        if (result.isConfirmed) {
          await this.$store.dispatch('actionsCreateMerchant', shopData)
          var response = await this.$store.state.ModuleShop.stateCreateMerchant
          if (response.result === 'SUCCESS') {
            this.$swal.fire({ icon: 'success', title: 'สร้าง Merchant ID สำเร็จ', showConfirmButton: false, timer: 2000 })
            this.merchantID = true
            this.GetCouponList()
          }
          if (response.result === 'FAILED') {
            var msg = response.message
            if (response.message === 'This user is unauthorized.') {
              msg = 'ผู้ใช้งานไม่ได้รับอนุญาตในการใช้ระบบ'
            } else if (response.message === 'Data missing. Please check your parameter and try again.') {
              msg = 'ข้อมูลรหัสร้านค้าขาดหาย โปรดติดต่อเจ้าหน้าที่'
            } else if (response.message === 'Not Found Seller Shop') {
              msg = 'ไม่พบร้านค้านี้'
            } else if (response.message === 'Not found!. This user access denied.') {
              msg = 'ผู้ใช้ไม่มีสิทธิ์ในการสร้าง Merchant ID'
            } else if (response.message === 'Not found!. The user is not seller in this shop.') {
              msg = 'ผู้ใช้ไม่ได้อยู่ในร้านนี้'
            } else if (response.message === 'Shop owner position not found!.') {
              msg = 'เจ้าของร้านเท่านั้นที่มีสิทธิ์สร้าง Merchant ID'
            } else if (response.message === 'OneID of shop owner is not found!.') {
              msg = 'เจ้่าของร้านไม่มีข่้อมูลใน OneID'
            } else if (response.message === 'Please check and try again.') {
              msg = 'โปรดตรวจสอบแล้วลองใหม่อีกครั้ง'
            } else if (response.message === 'This shop is already exist.') {
              msg = 'ร้านค้านี้มีอยู่แล้ว'
            }
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', text: msg })
          }
        }
      }).catch(() => {})
    },
    async GetCouponList () {
      // var response = 'wating'
      // this.couponList = response.data
      this.$store.commit('openLoader')
      const data = { seller_shop_id: this.shopDetail.id }
      await this.$store.dispatch('actionListShopCoupon', data)
      var response = await this.$store.state.ModuleShop.stateListShopCoupon
      if (response.message === 'fetch data success') {
        this.$store.commit('closeLoader')
        this.couponList = response.data
        this.DataCount = this.couponList.length
        this.pageMax = parseInt(this.couponList.length / 24) === 0 ? 1 : Math.ceil(this.couponList.length / 24)
      } else if (response.message === 'not found data') {
        this.$store.commit('closeLoader')
        this.couponList = response.data
        this.DataCount = this.couponList.length
        this.pageMax = parseInt(this.couponList.length / 24) === 0 ? 1 : Math.ceil(this.couponList.length / 24)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    ManageCupon (status, item) {
      if (this.MobileSize) {
        if (status === 'Create') {
          this.$router.push({ path: `/ManagaCuponMobile?Status=${status}` }).catch(() => {})
        } else if (status === 'Edit') {
          this.$router.push({ path: `/ManagaCuponMobile?Status=${status}&id=${item.couponId}` }).catch(() => {})
        }
      } else {
        if (status === 'Create') {
          this.$router.push({ path: `/ManagaCupon?Status=${status}` }).catch(() => {})
        } else if (status === 'Edit') {
          this.$router.push({ path: `/ManagaCupon?Status=${status}&id=${item.couponId}` }).catch(() => {})
        }
      }
    },
    confirmDeleteCoupon (item) {
      this.$swal.fire({
        icon: 'warning',
        text: 'คุณต้องการลบคูปองนี้ใช่ หรือไม่',
        showCancelButton: true,
        confirmButtonText: 'ตกลง',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.deleteCoupon(item)
        } else if (result.dismiss === this.$swal.DismissReason.cancel) {
        }
      }).catch(() => {
      })
    },
    async deleteCoupon (item) {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.shopDetail.id,
        couponId: item.couponId
      }
      await this.$store.dispatch('actionDeleteShopCoupon', data)
      var response = await this.$store.state.ModuleShop.stateDeleteShopCoupon
      if (response.message === 'delete coupon success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
        this.GetCouponList()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async searchCoupon () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.shopDetail.id,
        key: this.search
      }
      await this.$store.dispatch('actionSearchCoupon', data)
      var response = await this.$store.state.ModuleShop.stateSearchCoupon
      if (response.message === 'Search coupon success.') {
        this.$store.commit('closeLoader')
        if (response.data.length !== 0) {
          this.notFoundSearchCoupon = false
        } else {
          this.notFoundSearchCoupon = true
        }
        this.couponList = response.data
        this.DataCount = this.couponList.length
        this.pageMax = parseInt(this.couponList.length / 24) === 0 ? 1 : Math.ceil(this.couponList.length / 24)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    viewDetailCoupon (item) {
      this.$refs.CouponDetailModal.open(item)
    }
  }
}
</script>

<style>
.fontSizeTitle {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 48px; color: #333333;
}
.fontSizeTitle2 {
  font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile2 {
  font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #636363 !important;
}
.coupon-title {
  font-weight: 600; font-size: 16px; line-height: 24px; color: #333333 !important;
}
.coupon-title-Ipad {
  font-weight: 600; font-size: 14px; line-height: 22px; color: #333333 !important;
}
.coupon-description {
  font-weight: 400; font-size: 14px; line-height: 18px; color: #989898 !important;
}
.coupon-description-Ipad {
  font-weight: 400; font-size: 10px; line-height: 14px; color: #989898 !important;
}
.coupon-date {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #989898 !important;
}
.coupon-date-Ipad {
  font-weight: 400; font-size: 10px; line-height: 14px; color: #989898 !important;
}
</style>
<style scoped>
.card {
  flex: 1;
  height: 100%;
  position: relative;
  background-color: #ffffff;
  border-radius: 8px;
}
.card >>> .card_a {
  height: 100%;
  z-index: 10;
  color: #ffffff;
  background: rgba(51, 51, 51, 0.3);
  border-radius: 8px;
  transform-origin: 0% 0%;
  text-align: end;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
</style>
