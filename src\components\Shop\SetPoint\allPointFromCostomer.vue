<template>
  <v-container :class="MobileSize ? 'background_productMobile my-4' : 'background_product pa-6'" style="background-color: #FFFFFF">
      <v-row>
        <v-col>
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">หน้ารายชื่อลูกค้าที่มีแต้มสะสมทั้งหมด</span>
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-data-table :headers="headersMain" :items="dataPrime">
            <!-- <template v-slot:[`item.info`]="{ item }">
            <v-btn icon @click="openDialog1(item)">
              <v-icon>mdi-eye</v-icon>
            </v-btn>
          </template> -->
          </v-data-table>
        </v-col>
      </v-row>
      <v-dialog v-model="diaLog1" width="700px">
      <v-card >
        <v-card-title class="pt-3 pr-3">
          <v-row>
            <v-col align="end">
              <v-btn icon @click="diaLog1 = false"><v-icon>mdi-close</v-icon></v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-data-table
        :items="dataDialog1.all_product"
        :headers="headersDialog1"
        :hide-default-footer="true"
        >
          <template v-slot:[`body.append`]>
            <tr class="sticky-table-footer">
              <td align="center"><span>รวม</span></td>
              <td align="center">{{ dataDialog1.cus_all_cost }}</td>
              <td align="center">{{ dataDialog1.cus_point }}</td>
            </tr>
          </template>
        </v-data-table>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      diaLog1: false,
      dataDialog1: [],
      dataPrime: [],
      headersMain: [
        { text: 'ชื่อ', value: 'user_name', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'คะแนนทั้งหมด', filterable: false, value: 'all_point', sortable: false, align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดรวมการซื้อ', filterable: false, sortable: false, value: 'all_order_cost', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersDialog1: [
        { text: 'รายการสั่งซื้อ', value: 'product_name', width: '', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาสินค้า', filterable: false, value: 'product_price', sortable: false, align: 'center', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'คะแนนที่ได้', filterable: false, sortable: false, value: 'product_point', align: 'center', width: '', class: 'backgroundTable fontTable--text' }
      ],
      seller_shop_id: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    this.getData()
  },
  mounted () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  methods: {
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    async getData () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsgetListUserPointBySellerShopID', data)
      var res = await this.$store.state.ModuleManagePoint.stategetListUserPointBySellerShopID
      // console.log(res, 'test30111')
      if (res.result === 'SUCCESS') {
        this.dataPrime = res.data
      } else {
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    openDialog1 (data) {
      this.diaLog1 = true
      this.dataDialog1 = data
      // console.log(this.dataDialog1)
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
</style>
