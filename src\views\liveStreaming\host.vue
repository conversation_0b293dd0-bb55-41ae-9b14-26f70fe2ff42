<template>
  <div class="pa-6" style="min-height: 500px;">
    <div v-if="!connected">
      <div style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">สร้างห้องไลฟ์</div>
      <v-row justify="center" class="">
        <v-col cols="4" align="center" align-self="center">
          <v-col cols="10" align="start">
            <span>ห้องไลฟ์</span>
            <v-text-field
              v-model="shopName"
              dense
              hide-details
              outlined
              placeholder="กรอกชื่อห้องไลฟ์"
            ></v-text-field>
          </v-col>
          <v-btn color="primary" @click="createRoome" :disabled="roomName == '' ||  participantName == ''">Create Room</v-btn>
        </v-col>
      </v-row>
    </div>

    <div v-else>
      <VideoComponent
        v-if="localTrack"
        :track="localTrack"
        :participantIdentity="`Host ${shopName}`"
        :local="true"
        :message="message"
        :token="token"
        :room="room"
        @leaveRoom="leaveRoom"
      />
    </div>

    <Dialog
      :modal = dialog
      :status="status"
      :title="title"
      :message="body"
      :message2="body2"
      @close="dialog = false"
    />
  </div>
</template>

<script>
import VideoComponent from './component/VideoComponent.vue'
import Dialog from './component/DialogStatus.vue'
import {
  Room
} from 'livekit-client'
export default {
  components: {
    VideoComponent,
    Dialog
  },
  data () {
    return {
      shopName: '',
      shopID: '',

      roomName: '',
      participantName: '',

      room: null,
      token: null,
      localTrack: null,

      connected: false,
      message: 'Hello everyone' + Math.floor(Math.random() * 100),

      dialog: false,
      status: '',
      title: '',
      body: '',
      body2: '',

      LIVEKIT_URL: 'wss://helloworld-nt1b7zmh.livekit.cloud'
    }
  },
  created () {
    if (localStorage.getItem('shopDetail') !== '' || localStorage.getItem('shopDetail') !== null || localStorage.getItem('shopDetail') !== undefined) {
      this.getAllRoom()
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.shopID = shopDetail.id
      this.shopName = shopDetail.name

      this.roomName = 'Host' + this.shopID
      this.participantName = this.roomName
    } else {
      this.dialog = true
      this.status = 'failed'
      this.title = 'ไม่สามารถระบุร้านค้าได้'
      this.body = 'กรุณาเข้าสู่ระบบใหม่'
      this.body2 = 'หากยังไม่สามารถให้บริการ กรุณาติดต่อเจ้าหน้าที่'
    }
  },
  methods: {
    async createRoome () {
      try {
        var tokenResponse = await this.getToken(this.roomName, this.participantName, this.liveTitle)
        console.log(tokenResponse)
        if (tokenResponse.code === 200) {
          this.room = new Room()
          this.token = tokenResponse.token
          await this.room.connect(this.LIVEKIT_URL, this.token)
          console.log('connect room suceess', this.roomName)
          await this.room.localParticipant.enableCameraAndMicrophone()
          const videoPublication = this.room.localParticipant.videoTrackPublications.values().next().value
          this.localTrack = videoPublication ? videoPublication.videoTrack : undefined
          this.connected = true
        } else {
          this.dialog = true
          this.status = 'failed'
          this.title = 'สร้างห้องไม่สำเร็จ'
          this.body =
          tokenResponse.message.includes('format') ? 'ข้อมูลร้านค้าไม่ถูกต้อง'
            : tokenResponse.message.includes('ไม่พบ') ? 'ไม่พบข้อมูลร้านค้า'
              : tokenResponse.message
          this.body2 = 'กรุณาตรวจสอบข้อมูลใหม่'
          console.log('tokenResponse', tokenResponse.message)
        }
      } catch (error) {
        console.error('There was an error connecting to the room:', error.message)
        // await this.leaveRoom()
      }
    },
    async getToken (roomName, participantName, title) {
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roomName, participantName, title })
      })

      var res = await response.json()
      if (res.message.includes('กำลังไลฟ์อยู่')) {
        await this.leaveRoom()
        await this.createRoome()
      } else {
        return await res
      }
    },

    async leaveRoom () {
      this.connected = false
      if (this.room) {
        await this.room.disconnect()
        await this.deleteRoom(this.roomName, true)
      } else {
        await this.deleteRoom(this.roomName, false)
      }

      this.room = undefined
      this.localTrack = undefined
    },

    async deleteRoom (roomName, roomConnect) {
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}deleteRoom`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          roomName: roomName,
          roomConnect: roomConnect
        })
      })
      console.log('response', response)
    },

    async roomOnEverything () {
      this.room = new Room()
      // this.room.on(
      //   RoomEvent.TrackSubscribed,
      //   (_track, publication, participant) => {
      //     this.remoteTracksMap.set(publication.trackSid, {
      //       trackPublication: publication,
      //       participantIdentity: participant.identity
      //     })
      //     this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      //   }
      // )

      // this.room.on(RoomEvent.ParticipantConnected, (participant) => {
      //   console.log('ParticipantDisconnected', participant)
      // })

      // this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      //   console.log('live ParticipantDisconnected')
      //   console.log('first', participant)
      // })

      // this.room.on(RoomEvent.TrackUnsubscribed, (_track, publication) => {
      //   // this.remoteTracksMap.delete(publication.trackSid)
      //   this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      // })
    },
    async getAllRoom () {
      await this.$store.dispatch('actionGetAllRoom', data)
      var response = await this.$store.state.ModuleShop.stateGetAllRoom
      console.log(response, 'response')
    }
  }
}
</script>

<style>

</style>
