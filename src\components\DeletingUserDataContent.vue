<template>
  <v-card width="100%" height="100%" elevation="0" style="border-radius: 16px; font-family: 'Kanit', sans-serif;" class="px-4 fontStyle">
    <div v-html="DeletingUserData[0].data_consent"></div>
  </v-card>
</template>

<script>
export default {
  data () {
    return {
      DeletingUserData: ''
    }
  },
  created () {
    this.GetDeletingUserData()
  },
  methods: {
    async GetDeletingUserData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsManageConsent')
      var response = await this.$store.state.ModuleAdminManage.stateManageConsent
      if (response.result === 'SUCCESS') {
        this.DeletingUserData = response.data.filter(e => e.name === 'Deleting User Data')
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${response.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style scoped>
.fontStyle {
  font-family: 'Kanit', sans-serif !important;
}
</style>
