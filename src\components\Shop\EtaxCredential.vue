<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0">
      <!-- Header -->
      <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-0' : 'px-0 py-0'">
        <v-row class="mx-0" v-if="!MobileSize">
          <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">e-Tax Credential</v-card-title>
        </v-row>
        <v-row v-else>
          <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> e-Tax Credentail</v-card-title>
        </v-row>
      </v-col>
      <!-- Title -->
      <v-card-title v-if="MobileSize" style="font-weight: 600; font-size: 18px; line-height: 26px;white-space: normal; display: inline-block; word-break:break-word;">ระบบ NGC สามารถสร้างใบกำกับภาษีได้อัตโนมัติ ผ่านระบบ e-Tax โดยเจ้าของร้านสามารถทำตามขั้นตอน ดังต่อไปนี้</v-card-title>
      <v-card-title v-else style="font-weight: 600; font-size: 18px; line-height: 26px;white-space: normal; display: inline-block; word-break:break-word;">ระบบ NGC สามารถสร้างใบกำกับภาษีได้อัตโนมัติ ผ่านระบบ e-Tax โดยเจ้าของร้านสามารถทำตามขั้นตอน ดังต่อไปนี้</v-card-title>
      <!-- Text E-tax -->
      <v-card-text id="v-step-0">
        <v-timeline dense id="v-step-0">
          <v-timeline-item fill-dot color="#E6E6E6" large>
            <template v-slot:icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step_1.png" max-height="24" max-width="24" contain></v-img>
            </template>
            <v-col cols="12" md="11">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">1. สมัครบัญชีผู้ใช้กับ e-Tax คลิก <a href="https://service-etax.one.th/regselfservice/?vendor=9b6add96-13c0-4f28-808c-7bfa7185307e" style="color: #1B5DD6; font-weight: bold; text-decoration: underline;">ลงทะเบียน</a></span>
            </v-col>
          </v-timeline-item>
          <v-timeline-item fill-dot color="#E6E6E6" large>
            <template v-slot:icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step_2.png" max-height="24" max-width="24" contain></v-img>
            </template>
            <v-col cols="12" md="11">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">2. นำ User code และ Access Key มากรอกที่ด้านล่าง (กรณี e-Tax type R1)</span>
            </v-col>
          </v-timeline-item>
          <v-timeline-item fill-dot color="#E6E6E6" large>
            <template v-slot:icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step_3.png" max-height="24" max-width="24" contain></v-img>
            </template>
            <v-col cols="12" md="11">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">3. หลังจากนั้นเมื่อไหร่ก็ตามที่มีลูกค้าคำสั่งซื้อและจ่ายเงินสำเร็จ ใบกำกับภาษีจะถูกสร้างขึ้นในระบบ e-Tax</span>
            </v-col>
          </v-timeline-item>
        </v-timeline>
        <!-- ส่วนแสดง Text Error/Success -->
        <v-row dense justify="center" class="mt-6">
          <v-col cols="12" align="center">
            <v-row dense justify="center">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/shop_1.png" contain max-height="57" max-width="57" :class="MobileSize ? 'mt-1 mb-4' : 'mr-4 mt-1'"></v-img>
              <div style="border-radius: 8px; background: #FBE5E4; min-height: 62px;" :style="IpadSize ? 'width: 360px;' : 'width: 680px;'" v-if="CheckETaxCredential === false && CheckErrorCredential === false">
                <span class="px-2" style="color: #F5222D; font-weight: 700; display: block; align-items: center; padding-top: 15px;" :style="IpadSize ? 'font-size: 14px; line-height: 22px;' : 'font-size: 20px; line-height: 30px;'">ร้านค้านี้ยังไม่ได้ยืนยัน Usercode และ Accesskey สำหรับต่อระบบ e-Tax</span>
              </div>
              <div style="border-radius: 8px; background: rgba(82, 196, 26, 0.2); min-height: 62px;" :style="IpadSize ? 'width: 360px;' : 'width: 680px;'" v-else-if="CheckETaxCredential === true && CheckErrorCredential === false">
                <span class="px-2" style="color: #52C41A; font-weight: 700; font-size: 20px; line-height: 30px; display: block; align-items: center; padding-top: 16px;" :style="IpadSize ? 'font-size: 14px; line-height: 22px;' : 'font-size: 20px; line-height: 30px;'">ร้านค้านี้ยืนยัน Usercode และ Accesskey สำหรับกับระบบ e-Tax แล้ว <v-icon color="#27AB9C">mdi-checkbox-marked-circle</v-icon></span>
              </div>
              <div style="border-radius: 8px; background: #FBE5E4; min-height: 62px;" :style="IpadSize ? 'width: 360px;' : 'width: 680px;'" v-else-if="(CheckETaxCredential === true || CheckETaxCredential === false) && CheckErrorCredential === true">
                <span class="px-2" style="color: #F5222D; font-weight: 700; font-size: 20px; line-height: 30px; display: block; align-items: center; padding-top: 15px;" :style="IpadSize ? 'font-size: 14px; line-height: 22px;' : 'font-size: 20px; line-height: 30px;'">เจอข้อผิดพลาดในระบบ NGC ในส่วน Credentials ของ e-Tax โปรดติดต่อเจ้าหน้าที่ NGC</span>
              </div>
            </v-row>
          </v-col>
        </v-row>
        <!-- ส่วน Card กรอก e-Tax -->
        <v-row dense justify="center" class="mt-8">
          <v-col cols="12" align="center">
            <v-form ref="formETax" :lazy-validation="lazy">
              <v-card elevation="0" style="background: #EBEBEB; border: 1px solid #EBEBEB; border-radius: 8px;" width="416" height="100%">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="12">
                      <div style="border: 2px solid #5BC3A2; border-radius: 8px; background: #FFFFFF; height: 62px;">
                        <span style="font-weight: 700; font-size: 20px; line-height: 30px; color: #333333; display: block; align-items: center; padding-top: 14px;">Tax ID : {{ taxID }}</span>
                      </div>
                    </v-col>
                    <v-col cols="12" class="mt-4" id="v-step-1">
                      <v-select v-model="selectTypeeTax" :items="eTaxItem" item-text="text" item-value="value" outlined style="border-radius: 8px;" dense></v-select>
                    </v-col>
                    <v-col cols="12" align="start" id="v-step-3">
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Access Token <span style="color: red;">*</span></span>
                      <v-text-field v-model="accessToken" dense solo placeholder="ระบุ Access Token" style="border-radius: 8px;" :rules="Rules.accessToken"></v-text-field>
                    </v-col>
                    <v-row dense class="py-0 px-1" id="v-step-2">
                      <v-col cols="12" align="start" v-if="selectTypeeTax === 'R1'">
                        <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">User Code <span style="color: red;">*</span></span>
                        <v-text-field v-model="userCode" dense solo placeholder="ระบุ User Code" style="border-radius: 8px;" :rules="Rules.usercode"></v-text-field>
                      </v-col>
                      <v-col cols="12" align="start" v-if="selectTypeeTax === 'R1'">
                        <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Access Key <span style="color: red;">*</span></span>
                        <v-text-field v-model="accessKey" dense solo placeholder="ระบุ Access Key" style="border-radius: 8px;" type="password" :rules="Rules.AccessKey"></v-text-field>
                      </v-col>
                    </v-row>
                  </v-row>
                </v-card-text>
                <v-card-actions class="mb-4">
                  <v-row justify="end" dense>
                    <v-btn color="#27AB9C" rounded outlined class="px-7 mr-2" @click="cancel()">ยกเลิก</v-btn>
                    <v-btn color="#27AB9C" rounded class="white--text px-8 mr-2" @click="confirm()">ยืนยัน</v-btn>
                  </v-row>
                </v-card-actions>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
        <v-row justify="center" class="mt-10 mb-4">
          <v-col cols="12" :align="MobileSize ? 'start' : 'center'">
            <span style="font-size: 16px; line-height: 26px; color: #333333;"><b>หมายเหตุ</b> : ถ้าต้องการจะเปลี่ยน Usercode หรือ Accesskey คุณสามารถกลับมายืนยัน Usercode และ Accesskey ใหม่ในหน้านี้ได้</span>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      taxID: '',
      lazy: false,
      CheckETaxCredential: false,
      CheckErrorCredential: false,
      accessKey: '',
      userCode: '',
      shopSellerID: '',
      accessToken: '',
      dataShop: [],
      onedata: [],
      sellerTaxID: '',
      Rules: {
        AccessKey: [
          v => !!v || 'กรุณากรอก access key'
        ],
        usercode: [
          v => !!v || 'กรุณากรอก user code'
        ],
        accessToken: [
          v => !!v || 'กรุณากรอก access token'
        ]
      },
      selectTypeeTax: 'R2',
      eTaxItem: [
        { text: 'e-Tax Type R1', value: 'R1' },
        { text: 'e-Tax Type R2', value: 'R2' }
      ]
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log(this.onedata)
    this.shopSellerID = localStorage.getItem('shopSellerID').toString()
    await this.getDatailShop()
    await this.checkShopTaxID()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    selectTypeeTax (val) {
      if (val === 'R2') {
        this.userCode = ''
        this.accessKey = ''
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/EtaxCredentailMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/EtaxCredentail' }).catch(() => {})
      }
    }
  },
  methods: {
    async getDatailShop () {
      var data = {
        seller_shop_id: this.shopSellerID,
        role: 'seller'
      }
      // console.log(data)
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        this.dataShop = response.data[0]
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
      // console.log('this.dataShop', this.dataShop)
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async checkShopTaxID () {
      // const shopSellerID = localStorage.getItem('shopSellerID').toString()
      var data = {
        seller_id: this.shopSellerID
      }
      await this.$store.dispatch('actionsCheckTaxIDShop', data)
      // console.log(data)
      var responseData = await this.$store.state.ModuleETax.stateCheckTaxIDShop
      if (responseData.result === 'SUCCESS') {
        // console.log(responseData)
        if (responseData.data !== '-') {
          this.taxID = responseData.data
          this.sellerTaxID = responseData.data
          this.checkShopEtax()
        } else {
          this.taxID = '-'
          this.sellerTaxID = '-'
        }
      }
    },
    async checkShopEtax () {
      const shopSellerID = localStorage.getItem('shopSellerID').toString()
      var data = {
        seller_shop_taxID: this.sellerTaxID,
        seller_shop_id: shopSellerID
      }
      // console.log('data to check shop', data)
      await this.$store.dispatch('actionsCheckETaxInShop', data)
      var response = await this.$store.state.ModuleETax.stateCheckEtaxInShop
      // console.log('response =============>', response)
      if (response.result === 'yes') {
        this.CheckETaxCredential = true
        this.CheckErrorCredential = false
      } else {
        this.CheckETaxCredential = false
        this.CheckErrorCredential = false
      }
    },
    cancel () {
      this.$refs.formETax.reset()
      this.selectTypeeTax = 'R1'
    },
    async confirm () {
      this.$store.commit('openLoader')
      if (this.$refs.formETax.validate(true)) {
        const shopSellerID = localStorage.getItem('shopSellerID').toString()
        if (this.selectTypeeTax === 'R1') {
          var dataR1 = {
            token: this.onedata.user.access_token,
            seller_shop_taxID: this.sellerTaxID,
            seller_shop_id: shopSellerID,
            UserCode: this.userCode,
            AccessKey: this.accessKey,
            access_token: this.accessToken,
            business_id: this.dataShop.business_id,
            etax_type: this.selectTypeeTax
          }
          await this.$store.dispatch('actionsSendAccessETaxR2', dataR1)
          var response = await this.$store.state.ModuleETax.stateSendAccessETaxR2
          if (response.result === 'OK') {
            this.$store.commit('closeLoader')
            this.CheckETaxCredential = true
            this.CheckErrorCredential = false
            this.$swal.fire({ icon: 'success', title: 'เชื่อมต่อกับระบบ e-Tax สำเร็จ', showConfirmButton: false, timer: 1500 })
          } else {
            this.$store.commit('closeLoader')
            this.CheckETaxCredential = false
            this.CheckErrorCredential = true
          }
        } else {
          var dataR2 = {
            token: this.onedata.user.access_token,
            seller_shop_taxID: this.sellerTaxID,
            seller_shop_id: shopSellerID,
            UserCode: '',
            AccessKey: '',
            access_token: this.accessToken,
            etax_type: this.selectTypeeTax
          }
          await this.$store.dispatch('actionsSendAccessETaxR2', dataR2)
          var responseR2 = await this.$store.state.ModuleETax.stateSendAccessETaxR2
          if (responseR2.result === 'OK') {
            this.$store.commit('closeLoader')
            this.CheckETaxCredential = true
            this.CheckErrorCredential = false
            this.$swal.fire({ icon: 'success', title: 'เชื่อมต่อกับระบบ e-Tax สำเร็จ', showConfirmButton: false, timer: 1500 })
          } else {
            this.$store.commit('closeLoader')
            this.CheckETaxCredential = false
            this.CheckErrorCredential = true
          }
        }
      }
    }
  }
}
</script>
