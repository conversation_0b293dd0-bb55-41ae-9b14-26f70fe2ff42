<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
        {{ $t('SocialMediaSettings.Title') }}
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        {{ $t('SocialMediaSettings.Title') }}
      </v-card-title>

      <v-divider class="my-4"></v-divider>

      <div class="px-6">
        <v-form ref="FormbuyerDetailSocail" :lazy-validation="lazy">
          <v-row>
            <v-col cols="12" class="reduce-spacing">
              <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">{{ $t('SocialMediaSettings.Facebook') }}</span>
              <v-text-field class="input-text" :placeholder="`${$t('SocialMediaSettings.EnterLink')} ${$t('SocialMediaSettings.Facebook')}`" v-model="facebook_link" :disabled="!isEditable" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">{{ $t('SocialMediaSettings.TikTok') }}</span>
              <v-text-field class="input-text" :placeholder="`${$t('SocialMediaSettings.EnterLink')} ${$t('SocialMediaSettings.TikTok')}`" v-model="tiktok_link" :disabled="!isEditable" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">{{ $t('SocialMediaSettings.Youtube') }}</span>
              <v-text-field class="input-text" :placeholder="`${$t('SocialMediaSettings.EnterLink')} ${$t('SocialMediaSettings.Youtube')}`" v-model="youtube_link" :disabled="!isEditable" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">{{ $t('SocialMediaSettings.Instagram') }}</span>
              <v-text-field class="input-text" :placeholder="`${$t('SocialMediaSettings.EnterLink')} ${$t('SocialMediaSettings.Instagram')}`" v-model="instagram_link" :disabled="!isEditable" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">{{ $t('SocialMediaSettings.Line') }}</span>
              <v-text-field class="input-text" :placeholder="`${$t('SocialMediaSettings.EnterLink')} ${$t('SocialMediaSettings.Line')}`" v-model="line_link" :disabled="!isEditable" outlined dense @keypress="CheckSpacebar($event)"></v-text-field>
            </v-col>
          </v-row>
        </v-form>
        <v-divider class="my-4"></v-divider>

        <v-card-actions>
          <v-btn v-if="update" class="px-5" color="#27AB9C" outlined @click="cancelUpdate()" style="text-transform: none;">{{ $t('SocialMediaSettings.Cancel') }}</v-btn>
          <v-spacer></v-spacer>
          <v-btn v-if="!update" class="px-5 white--text" color="#27AB9C" @click="updateSocail()" style="text-transform: none;">{{ $t('SocialMediaSettings.Edit') }}</v-btn>
          <v-btn v-if="update" class="px-5 white--text" color="#27AB9C" @click="saveUpdate()" style="text-transform: none;">{{ $t('SocialMediaSettings.Save') }}</v-btn>
        </v-card-actions>
      </div>

  </v-card>
</v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      update: false,
      isEditable: false,
      facebook_link: '',
      tiktok_link: '',
      youtube_link: '',
      instagram_link: '',
      line_link: ''
    }
  },
  created () {
    this.checkConsent()
    this.$EventBus.$emit('changeNavAccount')
    this.showDetailBuyer()
  },
  computed: {
    MobileSize () {
      return this.$vuetify.breakpoint.xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    updateSocail () {
      this.update = true
      this.isEditable = true
    },
    async cancelUpdate () {
      this.update = false
      this.isEditable = false
      await this.showDetailBuyer()
    },
    async saveUpdate () {
      this.$store.commit('openLoader')
      this.update = false
      this.isEditable = false

      if (this.facebook_link !== '' || this.tiktok_link !== '' || this.youtube_link !== '' || this.instagram_link !== '' || this.line_link !== '') {
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        var data = {
          user_id: onedata.user.user_id,
          facebook_link: this.facebook_link,
          tiktok_link: this.tiktok_link,
          youtube_link: this.youtube_link,
          instagram_link: this.instagram_link,
          line_link: this.line_link
        }

        await this.$store.dispatch('actionsAffiliateUpdateSocail', data)
        const responseUpdateSocail = await this.$store.state.ModuleAffiliate.stateAffiliateUpdateSocail
        // console.log('UpdateSocail:', responseUpdateSocail)

        if (responseUpdateSocail.message === 'updated') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: this.$t('SocialMediaSettings.UpdateSuccess')
          })
        } else if (responseUpdateSocail.message === 'fail to update') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'info',
            title: this.$t('SocialMediaSettings.UpdateFailed')
          })
        } else if (responseUpdateSocail.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true,
          //   icon: 'info',
          //   title: 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
          // })
        } else if (responseUpdateSocail.message === 'An error has occurred. Please try again in an hour or two.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: `${responseUpdateSocail.message}`
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          html: this.$t('SocialMediaSettings.RequireAtLeastOne')
        })
        await this.showDetailBuyer()
      }
    },
    async showDetailBuyer () {
      this.$store.commit('openLoader')
      // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // var data = {
      //   user_id: onedata.user.user_id
      // }

      // await this.$store.dispatch('actionsAffiliateShowDetailBuyer', data)
      await this.$store.dispatch('actionsAffiliateShowDetailBuyer')
      const responseShowDetailBuyer = await this.$store.state.ModuleAffiliate.stateAffiliateShowDetailBuyer
      // console.log('buyerDetail:', responseShowDetailBuyer)
      if (responseShowDetailBuyer.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        this.facebook_link = responseShowDetailBuyer.data.facebook_link
        this.tiktok_link = responseShowDetailBuyer.data.tiktok_link
        this.youtube_link = responseShowDetailBuyer.data.youtube_link
        this.instagram_link = responseShowDetailBuyer.data.instagram_link
        this.line_link = responseShowDetailBuyer.data.line_link
      }
      this.$store.commit('closeLoader')
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>
