<template>
  <v-container class="pa-2">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">จัดการบัญชีร้านค้า</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon> จัดการบัญชีร้านค้า</v-card-title>

      <v-form ref="shopAccount" :lazy-validation="lazy">
        <v-card-text class="px-0 pa-0">
          <v-col cols="12" md="12">
            <v-row dense>
              <v-col cols="12">
                <span class="textFieldStepOne">ช่องทางการชำระเงิน <span style="color: red;">*</span></span>
                <v-chip class="ml-1" small outlined color="red" @click="dialogPaymentCondition = !dialogPaymentCondition" style="font-weight: 700;">อัตราค่าบริการระบบชำระเงิน</v-chip>
              </v-col>
              <v-col cols="12">
                <v-select :disabled="!isEditable" v-model="SelectPaymentType" :menu-props="{ offsetY: true }" :items="itemPayment" item-text="text" item-value="value" :rules="Rules.ItemPayment" multiple chips outlined dense placeholder="เลือกช่องทางการชำระเงิน" style="border-radius: 8px;">
                  <template #selection="{ item }">
                    <v-chip
                      close
                      @click:close="removePayment(item)" :color="item.value === 'qrcode' ? 'rgba(27, 93, 214, 0.10)' : item.value === 'installment' ? '#F8FFF5' : 'rgba(255, 113, 11, 0.10)'" :style="item.value === 'qrcode' ? 'color: #1B5DD6;' : item.value === 'installment' ? 'color: #8BC34A;' : 'color: #FF710B;'"
                    >
                      {{ item.text }}
                    </v-chip>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" v-if="SelectPaymentType.indexOf('installment') !== -1">
            <v-row dense>
              <v-col cols="12">
                <span class="textFieldStepOne">รูปแบบการผ่อนชำระ <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12">
                <v-select :disabled="!isEditable" v-model="selectInstallmentType" :menu-props="{ offsetY: true }" :items="itemInstallment" item-text="text" item-value="value" :rules="Rules.installment" multiple chips outlined dense placeholder="เลือกรูปแบบการผ่อนชำระ" style="border-radius: 8px;">
                  <template #selection="{ item }">
                    <v-chip
                    close
                    outlined
                    @click:close="removeInstallment(item)" color="primary"
                    >
                      {{ item.text }}
                    </v-chip>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="12">
            <v-row dense>
              <v-col cols="12">
                <span class="textFieldStepOne">รูปแบบชำระเงิน <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" md="3">
                <v-radio-group
                  :disabled="!isEditable"
                  style="margin-top: 0px;"
                  v-model="SelectType"
                  :rules="Rules.ItemContact"
                  row
                >
                  <v-radio
                    label="ไม่มีสัญญา"
                    value="no_contact"
                  ></v-radio>
                  <v-radio
                    label="มีสัญญา"
                    value="contact"
                  ></v-radio>
                </v-radio-group>
              </v-col>
              <v-col cols="12" md="9" :class="IpadSize ? 'pt-6' : ''" v-if="SelectType === 'contact'">
                <v-select :disabled="!isEditable" v-model="SelectTypePay" :menu-props="{ offsetY: true }" :items="itemPayType" item-text="text" item-value="value" :rules="Rules.ItempayType" multiple chips outlined dense placeholder="เลือกรูปแบบชำระเงิน" style="border-radius: 8px;">
                  <template #selection="{ item }">
                    <v-chip
                      close
                      @click:close="remove(item)" :color="item.value === 'onetime' ? 'rgba(27, 93, 214, 0.10)' : 'rgba(255, 113, 11, 0.10)'" :style="item.value === 'onetime' ? 'color: #1B5DD6;' : 'color: #FF710B;'"
                    >
                      {{ item.text }}
                    </v-chip>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="12">
            <v-row dense align="center" justify="space-between">
              <v-col cols="12" md="12" class="d-flex align-center">
                <span class="textFieldStepOne" style="margin-right: 15px;">e-Withholding Tax</span>
                <v-switch
                  :disabled="!isEditable"
                  v-model="switchEWHT"
                  :true-value="'yes'"
                  :false-value="'no'"
                  inset
                  hide-details
                  style="padding-top: 0px; margin-top: 0px;">
                </v-switch>
              </v-col>
            </v-row>
          </v-col>
          <v-divider class="my-2"></v-divider>
          <v-col cols="12" md="12">
            <span  class="textFieldStepOne">บัญชีธนาคาร ({{ this.listAccounts.length }})</span>
          </v-col>
          <v-divider class="my-2"></v-divider>
          <div
            v-for="(item, index) in dataAccounts"
            :key="index"
          >
            <div v-if="item.statusAccount !== 'delete'">
              <v-col cols="12" md="12">
                <v-row dense>
                  <v-col cols="12" md="6" sm="6">
                    <v-row dense align="center" justify="space-between">
                      <v-col cols="12" md="12" class="d-flex align-center">
                        <span class="textFieldStepOne" style="margin-right: 15px;">
                          ตั้งเป็นบัญชีธนาคารตั้งต้น <span style="color: red;">*</span>
                        </span>
                        <v-switch
                          :disabled="!isEditable"
                          v-model="item.isDefault"
                          :true-value="'yes'"
                          :false-value="'no'"
                          inset
                          hide-details
                          @change="setDefaultAccount(index)"
                          style="padding-top: 0px; margin-top: 0px;"
                        ></v-switch>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <v-row dense>
                      <v-col cols="12" md="12" style="text-align: end;">
                          <v-btn v-model="item.statusAccount" color="#27AB9C" small :disabled="!isEditable" @click="removeAccount(index)">
                            <v-icon color="white">mdi-trash-can-outline</v-icon>
                          </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="12">
                <v-row dense>
                  <v-col cols="12" md="6" sm="6">
                    <v-row dense>
                      <v-col cols="12">
                        <span class="textFieldStepOne">ประเภทบัญชี <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12">
                        <v-select :disabled="!isEditable" :items="accountTypes" item-text="text" item-value="value" placeholder="เลือกประเภทบัญชี" v-model="item.accountType" style="border-radius: 8px;" outlined dense :rules="Rules.ItemTypeBank"></v-select>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <v-row dense>
                      <v-col cols="12">
                        <span class="textFieldStepOne">ธนาคาร <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12">
                        <v-select @change="item.accountNo = ''" :disabled="!isEditable" v-model="item.bankCode" :menu-props="{ offsetY: true }" :items="itemsBank" item-text="name" item-value="code" :rules="Rules.ItemBank" outlined dense placeholder="เลือกธนาคาร" style="border-radius: 8px;" class="setCustomSelect"></v-select>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <v-row dense>
                      <v-col cols="12">
                        <span class="textFieldStepOne">เลขบัญชีธนาคาร <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                        <v-text-field
                          :disabled="!isEditable"
                          v-model="item.accountNo"
                          outlined
                          dense
                          :rules="Rules.ItemNumberBank"
                          @keypress="CheckSpacebar($event)"
                          style="border-radius: 8px;"
                          oninput="this.value = this.value.replace(/[^0-9/\s]/g, '').replace(/(\..*)\./g, '$1')"
                          placeholder="ระบุเลขบัญชีธนาคาร"
                          :maxLength="checkLengthValid(item.bankCode)"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <v-row dense>
                      <v-col cols="12">
                        <span class="textFieldStepOne">ชื่อบัญชีธนาคาร <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          :disabled="!isEditable"
                          v-model="item.accountName"
                          outlined
                          dense
                          :rules="Rules.ItemNameBank"
                          style="border-radius: 8px;"
                          @keypress="CheckSpacebarOne($event)"
                          oninput="this.value = this.value.replace(/[^a-zA-Zก-๏0-9.()\s]/g, '')"
                          placeholder="ระบุชื่อบัญชีธนาคาร"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="12" md="6" sm="6" style="display: flex; align-items: center;">
                        <span style="padding-right: 10px; font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">รูปหน้าบัญชีธนาคาร</span>
                        <v-btn :disabled="!isEditable" @click="triggerFileInput(index)" color="#27AB9C" class="white--text rounded-button">อัปโหลด</v-btn>
                        <input
                          type="file"
                          :ref="'fileInput' + index"
                          @change="handleFileUpload($event, index)"
                          accept="image/jpeg, image/png"
                          style="display: none;"
                        />
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <div class="mt-2" style="display: flex; justify-content: space-evenly;">
                          <v-card v-if="item.bookbankImagePreview || item.bookbankImage" class="d-flex justify-center align-center mb-6" style="max-width: 300px; max-height: 300px; overflow: hidden;">
                            <img :src="item.bookbankImagePreview || item.bookbankImage" style="width: 100%; height: 100%; object-fit: contain;">
                          </v-card>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <v-divider class="my-2"></v-divider>
            </div>
          </div>
          <v-col cols="12" class="d-flex justify-center" v-if="dataAccounts.length < 5 || activeAccounts.length < 5">
            <v-btn :disabled="!isEditable" color="#27AB9C" text @click="addItem">
              <v-icon>mdi-plus-circle-outline</v-icon>
              <span style="text-decoration: underline;">เพิ่มบัญชีธนาคาร</span>
            </v-btn>
          </v-col>
        </v-card-text>
      </v-form>
      <v-divider class="my-2" v-if="dataAccounts.length < 5 || activeAccounts.length < 5"></v-divider>
      <v-col cols="12" md="12">
        <v-card-actions>
          <v-btn v-if="update" class="px-5" color="#27AB9C" outlined @click="cancelSelectSaveAccount()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn
            v-if="!update && (
                (Array.isArray(this.checkCreateAccount.list_account) && this.checkCreateAccount.list_account.length !== 0) ||
                (Array.isArray(this.checkCreateAccount.list_account) && this.checkCreateAccount.list_account.length === 0 &&
                this.checkCreateAccount.payment_method !== null || this.checkCreateAccount.payment_method !== 0 &&
                Array.isArray(this.checkCreateAccount.payment_costs) && this.checkCreateAccount.payment_costs.length !== 0)
            )"
            class="px-5 white--text"
            color="#27AB9C"
            @click="openData()">แก้ไข</v-btn>

          <v-btn
            v-else-if="!update && (
                (Array.isArray(this.checkCreateAccount.list_account) && this.checkCreateAccount.list_account.length === 0) &&
                this.checkCreateAccount.payment_method === null &&
                (Array.isArray(this.checkCreateAccount.payment_costs) && this.checkCreateAccount.payment_costs.length === 0)
            )"
            class="px-5 white--text"
            color="#27AB9C"
            @click="openData()">เพิ่ม</v-btn>
          <v-btn v-if="update" class="px-5 white--text" color="#27AB9C" @click="SelectSaveAccount()">บันทึก</v-btn>
        </v-card-actions>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogPaymentCondition" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัตราค่าบริการระบบชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="dialogPaymentCondition = !dialogPaymentCondition" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-card-text class="mt-6">
                <v-expansion-panels v-model="panel" readonly multiple accordion class="elevation-0">
                  <v-expansion-panel
                    v-for="(item, index) in itemPaymentCondition"
                    :key="index"
                  >
                    <v-expansion-panel-header>{{ item.header }}</v-expansion-panel-header>
                    <v-expansion-panel-content>
                      <ul v-for="(items, indexs) in item.content" :key="indexs">
                        <li v-html="items.text"></li>
                      </ul>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;" class="justify-center">
          <v-btn color="#27AB9C" rounded width="125" height="40" @click="dialogPaymentCondition = !dialogPaymentCondition" class="white--text">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      lazy: false,
      isEditable: false,
      update: false,
      Rules: {
        ItemContact: [
          v => !!v || 'กรุณาเลือกประเภทสัญญา'
        ],
        ItempayType: [
          v => (v && v.length !== 0) || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemPayment: [
          v => (v && v.length !== 0) || 'กรุณาเลือกช่องทางการชำระเงิน'
        ],
        ItemBank: [
          v => (v && v.length !== 0) || 'กรุณาเลือกธนาคาร'
        ],
        ItemTypeBank: [
          v => (v && v.length !== 0) || 'กรุณาเลือกประเภทบัญชี'
        ],
        ItemNameBank: [
          v => (v && v.length !== 0) || 'กรุณาระบุชื่อบัญชีธนาคาร'
        ],
        ItemNumberBank: [
          v => (v && v.length !== 0) || 'กรุณาระบุเลขบัญชีธนาคาร'
        ],
        installment: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการผ่อนชำระ'
        ]
      },
      itemPayment: [
        { text: 'QR Code', value: 'qrcode' },
        { text: 'Credit Card/Debit Card', value: 'creditcard' },
        { text: 'Credit Card แบบผ่อนชำระ', value: 'installment' }
      ],
      listAccounts: [],
      dataAccounts: [],
      deletedAccounts: [],
      checkCreateAccount: [],
      accountID: '',
      statusAccount: '',
      SelectPaymentType: '',
      SelectType: '',
      SelectTypePay: '',
      itemsBank: [],
      bankCode: '',
      accountNo: '',
      accountName: '',
      shortName: '',
      switchEWHT: 'no',
      itemShipping: [],
      selectInstallmentType: [],
      bussinessType: '',
      itemPayType: [
        { text: 'One Time', value: 'onetime' },
        { text: 'Recurring', value: 'recurring' }
      ],
      accountTypes: [
        { text: 'ออมทรัพย์', value: 'saving' },
        { text: 'ฝากประจำ', value: 'current' }
      ],
      accountType: '',
      isDefault: 'no',
      bookbankImage: '',
      dialogBookbank: false,
      dialogPaymentCondition: false,
      panel: [0, 1, 2],
      itemPaymentCondition: [
        {
          header: '1. ชำระแบบ QR Code',
          content: [
            { text: 'กรณี <span style="color: #27AB9C; font-weight: 700; font-size: 16px; text-decoration: underline;"> Transaction ยอดชำระน้อยกว่า 500 บาท</span> : 2.5 % (ไม่รวม Vat)' },
            { text: 'กรณี <span style="color: #27AB9C; font-weight: 700; font-size: 16px; text-decoration: underline;"> Transaction ยอดชำระตั้งแต่ 500 บาทขึ้นไป</span> : 8 บาท (รวม Vat)' }
          ]
        },
        {
          header: '2. ชำระแบบ Credit Card/Debit Card',
          content: [
            { text: 'ระบบรองรับ บัตรเครดิต/เดบิต ทุกธนาคาร โดยมี <span style="color: #27AB9C; font-weight: 700; font-size: 16px; text-decoration: underline;">ค่าธรรมเนียม 2.5% / 1 Transaction</span> (ไม่รวม Vat)' }
          ]
        }
        // { header: 'การผ่อนชำระผ่านบัตรเครดิต', content: 'ระบบรองรับเฉพาะบัตรเครดิตกรุงไทย (KTC) เท่านั้น โดยมี <span style="color: #27AB9C; font-weight: 700; font-size: 16px;">" ค่าธรรมเนียม 3%, ดอกเบี้ย 0.89% และ VAT 7% ของค่าธรรมเนียมและดอกเบี้ย / 1 Transaction "</span>' }
      ],
      itemInstallment: [
        { text: '3 เดือน', value: '3' },
        { text: '6 เดือน', value: '6' },
        { text: '10 เดือน', value: '10' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    activeAccounts () {
      return this.dataAccounts.filter(account => account.statusAccount !== 'delete')
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: 'ManageShopAccountMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: 'ManageShopAccount' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
    await this.AccountDetailShop()
    await this.getListBank()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    removeInstallment (item) {
      const index = this.selectInstallmentType.indexOf(item.value)
      if (index >= 0) this.selectInstallmentType.splice(index, 1)
    },
    backtoMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    triggerFileInput (index) {
      this.$refs[`fileInput${index}`][0].click()
    },
    async handleFileUpload (event, index) {
      this.$store.commit('openLoader')
      const file = event.target.files[0]

      if (!file || !['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพที่สกุล jpeg/jpg/png เท่านั้น',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500
        })
        return
      }

      const imageSize = file.size / 1024 / 1024
      if (imageSize > 5) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        return
      }

      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = async () => {
        this.$set(this.dataAccounts[index], 'bookbankImagePreview', reader.result)
        const base64Image = reader.result.split(',')[1]
        const data = {
          image: [base64Image],
          type: 'bookbankImage',
          seller_shop_id: localStorage.getItem('shopSellerID')
        }

        await this.$store.dispatch('actionsUploadToS3', data)
        const response = this.$store.state.ModuleShop.stateUploadToS3

        if (response.message === 'List Success.') {
          this.$store.commit('closeLoader')
          const s3Path = response.data.list_path[0].path
          this.$set(this.dataAccounts[index], 'bookbankImage', s3Path)
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: 'การอัปโหลดล้มเหลว',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2000
          })
        }
      }
    },
    openData () {
      this.update = true
      this.isEditable = true
    },
    async cancelSelectSaveAccount () {
      this.update = false
      this.isEditable = false
      await this.AccountDetailShop()
    },
    remove (item) {
      const index = this.SelectTypePay.indexOf(item.value)
      if (index >= 0) this.SelectTypePay.splice(index, 1)
    },
    removePayment (item) {
      const index = this.SelectPaymentType.indexOf(item.value)
      if (item.value === 'installment') {
        this.selectInstallmentType = []
      }
      if (index >= 0) this.SelectPaymentType.splice(index, 1)
    },
    setDefaultAccount (changedIndex) {
      if (this.dataAccounts[changedIndex].isDefault === 'yes') {
        this.dataAccounts.forEach((account, index) => {
          if (index !== changedIndex) {
            account.isDefault = 'no'
          }
        })
      }
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    CheckSpacebar (event) {
      if (event.code === 'Space') {
        event.preventDefault()
      }
    },
    async AccountDetailShop () {
      const data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('actionAccountDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateAccountDetailShop
      if (response.message === 'Success') {
        // console.log(response)
        this.checkCreateAccount = response.data
        // console.log('2', this.checkCreateAccount)
        this.switchEWHT = response.data.use_ewht
        this.SelectPaymentType = response.data.payment_method
        this.selectInstallmentType = response.data.installment_method
        if (response.data.payment_costs && response.data.payment_costs.length > 0) {
          this.SelectType = 'contact'
          this.SelectTypePay = response.data.payment_costs
        } else {
          this.SelectType = 'no_contact'
          this.SelectTypePay = []
        }
        this.listAccounts = response.data.list_account
        this.dataAccounts = response.data.list_account.map(
          accountItem => ({
            ...accountItem,
            accountID: accountItem.acc_id,
            isDefault: accountItem.is_main,
            accountType: accountItem.account_type,
            bankCode: accountItem.bank_code,
            accountNo: accountItem.account_no,
            accountName: accountItem.account_bank,
            bookbankImage: accountItem.bookbank_path,
            statusAccount: accountItem.status
          })
        )
      }
    },
    addItem () {
      // console.log(this.dataAccounts)
      this.dataAccounts.push({
        accountID: null,
        accountType: '',
        bankCode: '',
        accountNo: '',
        accountName: '',
        bookbankImage: '',
        isDefault: 'no'
      })
    },
    removeAccount (index) {
      const account = this.dataAccounts[index]

      // ตรวจสอบว่าข้อมูล account ว่างไหม
      if (!account || !account.accountID) {
        this.dataAccounts.splice(index, 1)
        return
      }

      const accountInList = this.dataAccounts.find(acc => acc.accountID === account.accountID)

      if (accountInList) {
        account.statusAccount = 'delete'
      } else {
        this.dataAccounts.splice(index, 1)
      }
    },
    SelectSaveAccount () {
      if (
        Array.isArray(this.checkCreateAccount.list_account) &&
        this.checkCreateAccount.list_account.length === 0 &&
        this.checkCreateAccount.payment_method === null &&
        Array.isArray(this.checkCreateAccount.payment_costs) &&
        this.checkCreateAccount.payment_costs.length === 0
      ) {
        this.createAccount()
      } else {
        this.updateAccount()
      }
    },
    async createAccount () {
      if (this.$refs.shopAccount.validate(true)) {
        const validAccounts = this.dataAccounts.filter(e => e.statusAccount !== 'delete')
        const mainAccounts = validAccounts.filter(e => e.isDefault === 'yes')
        if (validAccounts.length === 1 && mainAccounts.length === 0) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'warning',
            title: 'กรุณาเลือกบัญชีธนาคารตั้งต้น'
          })
          return
        }
        if (validAccounts.length > 1 && mainAccounts.length !== 1) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'warning',
            title: '<span style="font-size: 20px;">กรุณาเลือกบัญชีธนาคารตั้งต้นเพียง 1 บัญชีเท่านั้น</span>'
          })
          return
        }

        this.update = false
        this.isEditable = false
        this.$store.commit('openLoader')
        var finalListAccount = this.dataAccounts.map((e) => {
          return {
            acc_id: e.accountID,
            account_bank: e.accountName,
            account_no: e.accountNo,
            account_type: e.accountType,
            bank_code: e.bankCode,
            bookbank_path: e.bookbankImage,
            is_main: e.isDefault,
            status: e.statusAccount
          }
        })
        // console.log(finalListAccount)

        var data = {
          list_account: finalListAccount,
          payment_costs: this.SelectType === 'no_contact' ? [] : this.SelectTypePay,
          payment_method: this.SelectPaymentType,
          installment_method: this.selectInstallmentType,
          seller_shop_id: this.shopID,
          use_ewht: this.switchEWHT
        }
        await this.$store.dispatch('actionCreateAccountShop', data)
        var response = await this.$store.state.ModuleShop.stateCreateAccountShop
        // console.log(response)

        if (response.message === 'Success') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'เพิ่มข้อมูลบัญชีสำเร็จ'
          })
          await this.AccountDetailShop()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: `${response.message || response[0].message}`
          })
          await this.AccountDetailShop()
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน'
        })
      }
    },
    async updateAccount () {
      if (this.$refs.shopAccount.validate(true)) {
        const validAccounts = this.dataAccounts.filter(e => e.statusAccount !== 'delete')
        const mainAccounts = validAccounts.filter(e => e.isDefault === 'yes')
        if (validAccounts.length === 1 && mainAccounts.length === 0) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'warning',
            title: 'กรุณาเลือกบัญชีธนาคารตั้งต้น'
          })
          return
        }
        if (validAccounts.length > 1 && mainAccounts.length !== 1) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'warning',
            title: '<span style="font-size: 20px;">กรุณาเลือกบัญชีธนาคารตั้งต้นเพียง 1 บัญชีเท่านั้น</span>'
          })
          return
        }

        this.update = false
        this.isEditable = false
        this.$store.commit('openLoader')
        var finalListAccount = this.dataAccounts.map((e) => {
          return {
            acc_id: e.accountID,
            account_bank: e.accountName,
            account_no: e.accountNo,
            account_type: e.accountType,
            bank_code: e.bankCode,
            bookbank_path: e.bookbankImage,
            is_main: e.isDefault,
            status: e.statusAccount
          }
        })
        // console.log(finalListAccount)

        var data = {
          list_account: finalListAccount,
          payment_costs: this.SelectType === 'no_contact' ? [] : this.SelectTypePay,
          payment_method: this.SelectPaymentType,
          installment_method: this.selectInstallmentType,
          seller_shop_id: this.shopID,
          use_ewht: this.switchEWHT
        }
        await this.$store.dispatch('actionAccountDetailShopUpdate', data)
        var response = await this.$store.state.ModuleShop.stateAccountDetailShopUpdate
        // console.log(response)

        if (response.message === 'update success') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'อัปเดตข้อมูลบัญชีสำเร็จ'
          })
          await this.AccountDetailShop()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: `${response.message || response[0].message}`
          })
          await this.AccountDetailShop()
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน'
        })
      }
    },
    async getListBank () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListBank')
      const response = await this.$store.state.ModuleShop.stateListBank
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.itemsBank = await [...response.data]
      }
    },
    checkLengthValid (bankNo) {
      if (bankNo === '030') {
        return 15
      } else if (bankNo === '033' || bankNo === '034') {
        return 12
      } else {
        return 10
      }
    }
  }
}
</script>

<style scoped>
.title {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.title_Mobile {
  font-weight: 700; font-size: 18px; line-height: 24px; color: #333333;
}
</style>
