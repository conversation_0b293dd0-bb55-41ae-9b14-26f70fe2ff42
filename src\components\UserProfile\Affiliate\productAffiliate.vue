<template>
  <v-container :class="MobileSize ? 'pa-0' : 'pa-2'">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="pb-0" v-if="!MobileSize">{{ $t('Affiliate.ProductListTitle') }}</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;" class="pb-0" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>{{ $t('Affiliate.ProductListTitle') }}</v-card-title>
      <v-divider class="my-4"></v-divider>
      <v-card-text v-if="showSkeletonLoader" class="px-2">
        <!-- Skeleton Loader -->
        <v-row dense v-if="!MobileSize && !IpadSize && !IpadProSize">
          <v-col class="px-1" cols="12" md="3" v-for="n in 4" :key="n">
            <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-row dense v-if="!MobileSize && !IpadSize && IpadProSize">
          <v-col cols="12" md="4" v-for="n in 4" :key="n">
            <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-row dense v-else-if="!MobileSize && IpadSize && !IpadProSize">
          <v-col cols="12" md="4" sm="6" v-for="n in 4" :key="n">
            <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-row dense v-else-if="MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="6" md="6" sm="6" xs="6" v-for="n in 4" :key="n">
            <v-skeleton-loader type="card" class="my-3"></v-skeleton-loader>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-text v-else-if="paginated.length !== 0 || AllProduct.length !== 0" class="px-2">
        <!-- ส่วน Filter ข้อมูล -->
        <v-row dense style="padding-bottom: 10px;">
          <v-col cols="12" md="4" sm="12">
            <v-text-field v-model="searchNameProduct" outlined dense style="border-radius: 8px;" hide-details :placeholder="this.$t('Affiliate.SearchProductName')" append-icon="mdi-magnify"></v-text-field>
          </v-col>
          <!-- <v-col cols="12" md="4" sm="6" :class="IpadSize ? 'pr-4' : MobileSize ? '' : 'px-4 pt-1'">
            <v-row dense :class="MobileSize ? 'pt-3' : 'pt-1'">
              <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pr-2 pt-2">ประเภท :</span>
              <v-select v-model="selectType" :items="itemsSelect" class="setCustomSelect" @change="filterProductAffiliate()" :menu-props="{ offsetY: true }" item-text="text" item-value="value" dense outlined hide-details append-icon="mdi-chevron-down" style="border-radius: 8px;" :style="(!IpadSize && !MobileSize) && checkWidth > 1345 ? 'max-width: 220px;' : (!IpadSize && !MobileSize) && checkWidth < 1345  ? 'max-width: 65%;' : 'max-width: 100%;'"></v-select>
            </v-row>
          </v-col> -->
          <!-- <v-col cols="12" md="4" sm="6">
            <v-row dense :class="MobileSize ? 'pt-3' : 'pt-1'">
              <span style="font-size: 16px; font-weight: 500; color: #333333;"  class="pr-2 pt-2">หมวดหมู่ :</span>
              <v-select v-model="searchCategory" :items="itemCategory" class="setCustomSelect" @change="filterProductAffiliate()" placeholder="เลือก" item-text="category_name" item-value="category_name" :menu-props="{ offsetY: true }" dense outlined hide-details append-icon="mdi-chevron-down" style="border-radius: 8px;" :style="(!IpadSize && !MobileSize) && checkWidth > 1345 ? 'max-width: 220px;' : (!IpadSize && !MobileSize) && checkWidth < 1345  ? 'max-width: 65%;' : 'max-width: 100%;'"></v-select>
            </v-row>
          </v-col> -->
        </v-row>
        <!-- ส่วนแสดงจำนวน -->
        <v-row dense :class="MobileSize ? 'pb-3' : ''">
          <v-col cols="12">
            <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ $t('Affiliate.AllAffiliateProducts') }} {{ searchNameProduct === '' ? productCount : productSearch.length }} {{ $t('Affiliate.List') }}</span>
          </v-col>
        </v-row>
        <v-row justify="start" class="mx-2" v-if="paginated.length !== 0 && !MobileSize && !IpadSize && !IpadProSize">
          <v-col class="px-1" cols="12" md="3" v-for="(item, index) in paginated" :key="index">
            <CardProducts :itemProduct='item'/>
          </v-col>
        </v-row>
        <v-row justify="start" class="mx-2" v-if="paginated.length !== 0 && !MobileSize && !IpadSize && IpadProSize">
          <v-col cols="12" md="4" v-for="(item, index) in paginated" :key="index">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="mx-0" v-else-if="paginated.length !== 0 && !MobileSize && IpadSize && !IpadProSize">
          <v-col cols="12" md="4" sm="6" v-for="(item, index) in paginated" :key="index" class="px-1">
            <CardProductsResponsive :itemProduct='item'/>
          </v-col>
        </v-row>
        <v-row justify="start" dense v-else-if="paginated.length !== 0 && MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index" class="px-1">
            <CardProductsResponsive :itemProduct='item'/>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- <v-card-text v-if="paginated.length === 0 || AllProduct.length === 0" > -->
      <v-card-text v-else>
        <v-col cols="12" align="center">
          <div class="mb-5">
            <v-img src="@/assets/New_No_Favorite.png" width="200" height="200" contain></v-img>
          </div>
          <div>
            <span style="font-size: 18px; font-weight: 700;">{{ $t('Affiliate.NoAffiliateProductFound') }}</span>
          </div>
        </v-col>
      </v-card-text>
      <v-row justify="center" class="my-6" v-if="(AllProduct.length !== 0 || productSearch.length !== 0)">
        <v-pagination
          color="#27AB9C"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="7"
          :circle="true"
          v-if="searchNameProduct === ''"
          @input="getProductAffiliate"
        ></v-pagination>

        <v-pagination
          color="#27AB9C"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="7"
          :circle="true"
          v-else
        ></v-pagination>
      </v-row>
      <v-container v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize && !IpadProSize">
        <v-row>
          <v-col style="padding-left: 10px;">
            <v-checkbox v-if="this.searchNameProduct === ''" v-model="selectAll" :label="this.$t('Affiliate.SelectAllOnPage')" @change="selectAll ? toggleSelection(true) : toggleSelection(false)"></v-checkbox>
          </v-col>
          <v-col style="text-align: end; display: flex; align-items: center; justify-content: space-around;">
            <span style="font-size: 16px; padding-right: 10px;">{{ $t('Affiliate.Select') }} {{ selectedItems }} / {{ this.searchNameProduct === '' ? productCount : productSearch.length }}</span>
            <v-btn color="#27AB9C" outlined @click="cancelReceiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" :disabled="!this.selectedItems" @click="receiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.GetAllLinks') }}</v-btn>
          </v-col>
        </v-row>
      </v-container>
      <v-container v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize && IpadProSize">
        <v-row>
          <v-col cols="4" style="padding-left: 10px;">
            <v-checkbox v-if="this.searchNameProduct === ''" v-model="selectAll" :label="this.$t('Affiliate.SelectAllOnPage')" @change="selectAll ? toggleSelection(true) : toggleSelection(false)"></v-checkbox>
          </v-col>
          <v-col style="text-align: end; display: flex; align-items: center; justify-content: space-around;">
            <span style="font-size: 16px; padding-right: 10px;">{{ $t('Affiliate.Select') }} {{ selectedItems }} / {{ this.searchNameProduct === '' ? productCount : productSearch.length }}</span>
            <v-btn color="#27AB9C" outlined @click="cancelReceiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" :disabled="!this.selectedItems" @click="receiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.GetAllLinks') }}</v-btn>
          </v-col>
        </v-row>
      </v-container>
      <v-container v-if="AllProduct.length !== 0 && !MobileSize && IpadSize && !IpadProSize">
        <v-row>
          <v-col style="padding-left: 10px;">
            <v-checkbox v-if="this.searchNameProduct === ''" v-model="selectAll" :label="this.$t('Affiliate.SelectAllOnPage')" @change="selectAll ? toggleSelection(true) : toggleSelection(false)"></v-checkbox>
          </v-col>
          <v-col cols="12" style="text-align: end; display: flex; align-items: center; justify-content: space-around;">
            <span style="font-size: 16px; padding-right: 10px;">{{ $t('Affiliate.Select') }} {{ selectedItems }} / {{ this.searchNameProduct === '' ? productCount : productSearch.length }}</span>
            <v-btn color="#27AB9C" outlined @click="cancelReceiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" :disabled="!this.selectedItems" @click="receiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.GetAllLinks') }}</v-btn>
          </v-col>
        </v-row>
      </v-container>
      <v-container v-if="AllProduct.length !== 0 && MobileSize && !IpadSize && !IpadProSize">
        <v-row>
          <v-col class="py-0" v-if="this.searchNameProduct === ''" style="padding-left: 10px;">
            <v-checkbox  v-model="selectAll" :label="this.$t('Affiliate.SelectAllOnPage')" @change="selectAll ? toggleSelection(true) : toggleSelection(false)"></v-checkbox>
          </v-col>
          <v-col cols="12" class="py-0" style="text-align: end;">
            <span style="font-size: 16px; padding-right: 10px;">{{ $t('Affiliate.Select') }} {{ selectedItems }} / {{ this.searchNameProduct === '' ? productCount : productSearch.length }}</span>
          </v-col>
          <v-col style="text-align: end; display: flex; align-items: center; justify-content: space-around;">
            <v-btn small color="#27AB9C" outlined @click="cancelReceiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
            <v-btn small class="px-5 white--text" color="#27AB9C" :disabled="!this.selectedItems" @click="receiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.GetAllLinks') }}</v-btn>
          </v-col>
        </v-row>
      </v-container>
    </v-card>

    <v-dialog v-model="showDialog" persistent max-width="550" style="overflow: hidden;">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">{{ $t('Affiliate.GetAllLinks') }}</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card width="100%" style="overflow: hidden;" align="center">
        <v-radio-group hidden style="padding-left: 20px; padding-right: 20px;" v-model="subType" row>
          <v-radio label="มาตรฐาน" value="standard"></v-radio>
          <v-radio label="ขั้นกว่า" value="special"></v-radio>
        </v-radio-group>
        <div v-if="subType === 'special'" style="padding-left: 20px; padding-right: 20px;">
          <v-text-field name="Sub_id 1" label="Sub_id 1" placeholder="ตัวอย่าง: SportShoes" v-model="sub_id_1"></v-text-field>
          <v-text-field name="Sub_id 2" label="Sub_id 2" placeholder="ตัวอย่าง: InstagramFeed" v-model="sub_id_2"></v-text-field>
          <v-text-field name="Sub_id 3" label="Sub_id 3" placeholder="ตัวอย่าง: 1212BirthdaySale" v-model="sub_id_3"></v-text-field>
          <v-text-field name="Sub_id 4" label="Sub_id 4" v-model="sub_id_4"></v-text-field>
          <v-text-field name="Sub_id 5" label="Sub_id 5" v-model="sub_id_5"></v-text-field>
        </div>
        <v-card-text class="pt-6">
          <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">{{ $t('Affiliate.ValidateSelectionBeforeGetLink') }}</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessSelect()" style="text-transform: none;">{{ $t('Affiliate.Confirm') }}</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="showDialogCancel" persistent max-width="550" style="overflow: hidden;">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">{{ $t('Affiliate.ConfirmCancelAllSelection') }}</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card width="100%" style="overflow: hidden;" align="center">
        <v-card-text class="pt-6">
          <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">{{ $t('Affiliate.ConfirmCancelAllMessage') }} {{ selectedItems }} {{ $t('Affiliate.List') }}</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="closeCancelReceiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="confirmCancelReceiveAllLinks()" style="text-transform: none;">{{ $t('Affiliate.Confirm') }}</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardProducts: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardAffiliateUI'),
    CardProductsResponsive: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardResponsiveAffiliate')
  },
  data () {
    return {
      path: process.env.VUE_APP_DOMAIN,
      showSkeletonLoader: true,
      showDialog: false,
      showDialogCancel: false,
      selected: false,
      selectAll: false,
      overlay2: true,
      productCount: null,
      maxSelection: 50,
      checkWidth: '',
      widthScreen: window.innerWidth,
      AllProduct: [],
      pageMax: null,
      current: parseInt(this.$route.query.page) || 1,
      pageSize: 16,
      countPage: 1,
      searchNameProduct: '',
      itemsSelect: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'สินค้าทั่วไป', value: 'general' },
        { text: 'สินค้าบริการ', value: 'service' }
      ],
      selectType: '',
      searchCategory: '',
      itemCategory: [],
      selectAllPerPage: [],
      selectCount: 0,
      subType: 'standard',
      sub_id_1: '',
      sub_id_2: '',
      sub_id_3: '',
      sub_id_4: '',
      sub_id_5: '',
      productSearch: [],
      searchTimeout: null
    }
  },
  async created () {
    this.checkConsent()
    await this.autoJoinAffiliate()
    this.$EventBus.$emit('changeNavAccount')
    await this.$EventBus.$emit('clearSelectedProducts')
    await this.$EventBus.$emit('clearSelectedProductsResponsive')
    this.$EventBus.$emit('checkpage', this.$route.name)
    this.$EventBus.$emit('main', this.$route.name)

    if (localStorage.getItem('oneData') !== null) {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.getConsent()
      this.getAllCategory()

      // ดึง page จาก query string (เช่น /buyerAffiliate?page=2)
      const pageFromQuery = parseInt(this.$route.query.page)
      if (!isNaN(pageFromQuery) && pageFromQuery > 0) {
        this.pageNumber = pageFromQuery
      } else {
        this.pageNumber = 1
      }

      // เรียกข้อมูลตาม page
      this.getProductAffiliate()

      this.AllProduct.forEach(itemProduct => {
        itemProduct.selected = false
      })
      this.formattedData = []
      this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)
      this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate = []
      this.selectAll = false
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('getProductAffiliate', this.getProductAffiliate)
    this.$EventBus.$on('updateSelected', this.updateSelected)
    this.$EventBus.$on('clearAllProductAffiliate', this.clearAllProductAffiliate)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getProductAffiliate')
      this.$EventBus.$off('updateSelected')
    })
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        if (this.$route.query.page !== String(newPage)) {
          this.$router.push({ query: { ...this.$route.query, page: newPage } }).catch(() => {})
        }
        this.selectAll = this.selectAllPerPage[this.pageNumber - 1]
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.searchNameProduct !== '') {
        return this.productSearch.slice(this.indexStart, this.indexEnd)
      } else {
        return this.AllProduct
      }
    },
    selectedItems () {
      var dataselect = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliateAll
      return dataselect.product_list ? dataselect.product_list.length : 0
    }
  },
  watch: {
    '$route.query.page' (newPage) {
      const pageNum = parseInt(newPage) || 1
      if (pageNum !== this.current) {
        this.current = pageNum
        if (this.searchNameProduct === '') {
          this.getProductAffiliate()
        }
        window.scrollTo(0, 0)
      }
    },
    searchNameProduct (val) {
      if (val !== '') {
        this.productSearch = []
        this.pageNumber = 1
        this.selectAllPerPage = []
        // เคลียร์ Timeout ก่อนหน้าเพื่อไม่ให้เรียก API ซ้ำ
        if (this.searchTimeout) {
          clearTimeout(this.searchTimeout)
        }
        // ตั้งเวลา 300ms ก่อนเรียก API
        this.searchTimeout = setTimeout(async () => {
          this.$store.commit('openLoader')
          const data = {
            user_id: this.onedata.user.user_id,
            keyword: val
          }
          await this.$store.dispatch('actionsSearchProductAffiliate', data)
          const response = await this.$store.state.ModuleAffiliate.stateSearchProductAffiliate
          if (response.ok === 'y') {
            this.productSearch = response.query_result.map(item => {
              const matchingItem = this.AllProduct.find(data => data.id === item.id)
              return {
                ...item,
                selected: matchingItem ? matchingItem.selected : false,
                link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id)
              }
            })
            this.pageMax = parseInt(this.productSearch.length / 16) === 0 ? 1 : Math.ceil(this.productSearch.length / 16)
            for (let i = 0; i < this.pageMax; i++) {
              this.selectAllPerPage.push(false)
            }
            if (this.pageMax === 1) {
              this.pageNumber = 1
            }
            this.productCount = response.query_result.length
            this.countPage = this.countPage + 1
            this.$store.commit('closeLoader')
          } else {
            this.productSearch = []
            this.$store.commit('closeLoader')
          }
        }, 300)
      } else {
        // เมื่อช่องค้นหาว่าง: โหลดข้อมูลทั้งหมดใหม่
        this.pageNumber = 1
        this.selectAllPerPage = []
        this.productSearch = []
        this.getProductAffiliate()
      }
    },
    widthScreen (val) {
      this.checkWidth = val
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/buyerAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/buyerAffiliate' }).catch(() => {})
      }
    },
    paginated (val) {
      // console.log(val)
      var checkAllSelect
      if (this.searchNameProduct !== '') {
        this.pageMax = parseInt(this.productSearch.length / 16) === 0 ? 1 : Math.ceil(this.productSearch.length / 16)
        checkAllSelect = val.every(element => element.selected === true)
        if (checkAllSelect) {
          this.selectAll = true
        } else {
          this.selectAll = false
        }
      } else {
        // this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
        checkAllSelect = val.every(element => element.selected === true)
        if (checkAllSelect) {
          this.selectAll = true
        } else {
          this.selectAll = false
        }
      }
    }
  },
  methods: {
    async autoJoinAffiliate () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAutoJoin')
      var response = await this.$store.state.ModuleAffiliate.stateAutoJoin
      if (response.message === 'Buyer auto approve process completed') {
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: response.message
        })
      }
    },
    async getConsent () {
      // this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      this.isBuyer = res.isBuyer
      // this.$store.commit('closeLoader')
    },
    async getAllCategory () {
      var data = 'all'
      this.itemCategory = []
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.message === 'Get all category success.') {
        this.itemCategory = response.data
        this.itemCategory.unshift({
          category_logo_path: '',
          category_name: 'หมวดหมู่ทั้งหมด',
          hierachy: ''
        })
      }
      // console.log('response cat ====>', response)
    },
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async filterProductAffiliate () {
      await this.getProductAffiliate()
    },
    async getProductAffiliate () {
      this.showSkeletonLoader = true
      // this.$store.commit('openLoader')

      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id,
        page: this.pageNumber,
        limit: 16
      }
      await this.$store.dispatch('actionsAffiliateShowProduct', data)
      var responseProduct = await this.$store.state.ModuleAffiliate.stateAffiliateShowProduct
      // console.log('response product get =======>', responseProduct)
      if (responseProduct.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        if (responseProduct.success === true) {
          if (responseProduct.message === 'get product') {
            this.overlay2 = false

            // ดึงข้อมูลที่เคยเลือกไว้จาก Vuex
            const selectedProducts = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliateAll.product_list || []
            console.log(selectedProducts)

            // สร้าง map สำหรับ lookup เร็ว
            const selectedMap = {}
            selectedProducts.forEach(p => {
              selectedMap[p.product_id] = true
            })

            // เวลาสร้าง AllProduct ให้เช็คว่าเคยถูกเลือกไหม
            this.AllProduct = responseProduct.data.map(item => ({
              ...item,
              selected: !!selectedMap[item.id], // ตรงนี
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id)
            }))

            // this.AllProduct = responseProduct.data.map((item, index) => ({
            //   ...item,
            //   selected: false,
            //   link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id)
            // }))
            // this.pageMax = parseInt(this.AllProduct.length / 16) === 0 ? 1 : Math.ceil(this.AllProduct.length / 16)
            // for (let i = 0; i < this.pageMax; i++) {
            //   this.selectAllPerPage.push(false)
            // }
            // if (this.pageMax === 1) {
            //   this.pageNumber = 1
            // }
            // this.productCount = responseProduct.data.length
            this.pageMax = responseProduct.pagination.max_page
            this.productCount = responseProduct.pagination.total_products
            this.countPage = this.countPage + 1
            // console.log(this.countPage)
            // this.$store.commit('closeLoader')
            this.showSkeletonLoader = false
          } else {
            // console.log('เข้ามาแล้วจ้า')
            this.AllProduct = []
            this.productCount = 0
            // this.$store.commit('closeLoader')
            this.showSkeletonLoader = false
          }
        } else if (responseProduct.message === 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่') {
          // this.$store.commit('closeLoader')
          this.showSkeletonLoader = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
        } else {
          const Toast = this.$swal.mixin({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
          Toast.fire({
            icon: 'error',
            title: `${responseProduct.message}`
          })
          this.showSkeletonLoader = false
          // this.$store.commit('closeLoader')
        }
      }
      // this.showSkeletonLoader = false
    },
    async toggleSelection (selected, allPages = false) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))

      if (allPages === false) {
        // อัปเดตสถานะเลือกหน้าปัจจุบัน
        this.selectAllPerPage[this.pageNumber - 1] = selected

        // ดึงรายการสินค้าที่ถูกเลือกทั้งหมดใน store
        var dataDetail = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate
        if (dataDetail.length === 0) {
          dataDetail.push({ product_list: [] })
        }

        this.AllProduct.forEach(itemProduct => {
          itemProduct.selected = selected
          const selectProduct = {
            seller_shop_id: itemProduct.seller_shop_id,
            product_id: itemProduct.id,
            product_name: itemProduct.name,
            sku: itemProduct.sku,
            price: itemProduct.real_price,
            commission_rate: itemProduct.commission_rate,
            url: itemProduct.productURL
          }

          if (selected) {
            // เพิ่มสินค้าใน product_list ถ้ายังไม่มี
            const exists = dataDetail[0].product_list.some(p => p.product_id === selectProduct.product_id)
            if (!exists) {
              dataDetail[0].product_list.push(selectProduct)
            }
          } else {
            // ลบสินค้านั้นออกถ้ายกเลิกเลือก
            dataDetail[0].product_list = dataDetail[0].product_list.filter(p => p.product_id !== selectProduct.product_id)
          }
        })

        const formattedData = {
          user_id: onedata.user.user_id,
          sub_type: this.subType,
          sub_id_1: '',
          sub_id_2: '',
          sub_id_3: '',
          sub_id_4: '',
          sub_id_5: '',
          product_list: dataDetail[0].product_list
        }

        this.formattedData = formattedData
        this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)
      }
    },
    // async toggleSelection (selected, allPages = false) {
    //   var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))

    //   if (allPages === false) {
    //     // ใช้การเลือกกับหน้าปัจจุบันเท่านั้น
    //     this.selectAllPerPage[this.pageNumber - 1] = selected
    //     this.AllProduct.forEach((itemProduct, index) => {
    //       // if (index <= (this.indexEnd - 1) && index >= (this.indexStart)) {
    //       itemProduct.selected = selected

    //       const selectProduct = {
    //         seller_shop_id: itemProduct.seller_shop_id,
    //         product_id: itemProduct.id,
    //         product_name: itemProduct.name,
    //         sku: itemProduct.sku,
    //         price: itemProduct.real_price,
    //         commission_rate: itemProduct.commission_rate,
    //         url: itemProduct.productURL
    //       }

    //       let dataDetail = []
    //       dataDetail = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate
    //       // console.log('dataDetail2', dataDetail)

    //       if (selected) {
    //         if (dataDetail.length === 0) {
    //           dataDetail.push({ product_list: [selectProduct] })
    //         } else {
    //           const productExists = dataDetail[0].product_list.some(product => product.product_id === selectProduct.product_id)
    //           if (!productExists) {
    //             dataDetail[0].product_list.push(selectProduct)
    //           }
    //         }
    //       } else {
    //         if (dataDetail.length > 0) {
    //           if (dataDetail[0].product_list) {
    //             dataDetail[0].product_list = dataDetail[0].product_list.filter(product => product.product_id !== selectProduct.product_id)
    //           }
    //         } else {
    //           dataDetail.push({ product_list: [] })
    //         }
    //       }

    //       const formattedData = {
    //         user_id: onedata.user.user_id,
    //         sub_type: this.subType,
    //         sub_id_1: '',
    //         sub_id_2: '',
    //         sub_id_3: '',
    //         sub_id_4: '',
    //         sub_id_5: '',
    //         product_list: dataDetail.length > 0 ? dataDetail[0].product_list : []
    //       }

    //       this.formattedData = formattedData
    //       this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)
    //       // }
    //     })
    //   }
    // },
    clearAllProductAffiliate () {
      this.toggleSelection(false, true)
    },
    updateSelected (selected, id) {
      this.paginated.forEach(element => {
        if (element.id === id) {
          element.selected = selected
        }
      })
      this.AllProduct = this.AllProduct.map(item => {
        var selectedProduct = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliateAll
        const matchingItem = selectedProduct.product_list.find(data => data.product_id === item.id)
        return {
          ...item,
          selected: matchingItem ? Boolean(true) : Boolean(false)
        }
      })

      if (selected) {
        this.selectCount = this.selectCount + 1
      } else {
        this.selectCount = this.selectCount - 1
      }
      if (this.selectCount === this.paginated.length) {
        this.selectAllPerPage[this.pageNumber - 1] = true
        this.selectAll = this.selectAllPerPage[this.pageNumber - 1]
      } else {
        this.selectAllPerPage[this.pageNumber - 1] = false
        this.selectAll = this.selectAllPerPage[this.pageNumber - 1]
      }
      this.$forceUpdate()
    },
    receiveAllLinks () {
      this.showDialog = true
    },
    Close () {
      this.showDialog = false
      this.showDialogCancel = false
    },
    cancelReceiveAllLinks () {
      this.showDialogCancel = true
    },
    closeCancelReceiveAllLinks () {
      this.showDialogCancel = false
    },
    confirmCancelReceiveAllLinks () {
      this.toggleSelection(false, true)
      this.AllProduct.forEach((itemProduct) => {
        itemProduct.selected = false
      })
      this.productSearch.forEach((itemProduct) => {
        itemProduct.selected = false
      })
      this.formattedData = []
      this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)
      this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate = []
      this.selectAll = false
      this.showDialogCancel = false
    },
    async SuccessSelect () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataselect = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliateAll

      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}affiliate/buyer/getMultipleLink`,
        data: dataselect,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'ลิงก์สินค้าหลายลิ้ง.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function (error) {
        console.log(error)
        this.$store.commit('closeLoader')
      })
      this.showDialog = false
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
</style>
