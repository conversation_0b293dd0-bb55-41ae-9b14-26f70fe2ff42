<template>
    <v-container :class="MobileSize ? 'mt-3' : ''">
      <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
        <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" class="pl-0" v-if="!MobileSize"><v-icon color="#27AB9C" size="30" @click="backtoPage()">mdi-chevron-left</v-icon>รายชื่อผู้เข้าร่วมร้านค้า <span style="color: #27AB9C; margin-left: 5px;">{{ sellerName }}</span></v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPageMobile()">mdi-chevron-left</v-icon>รายชื่อผู้เข้าร่วมร้านค้า <span style="color: #27AB9C; margin-left: 5px;">{{ sellerName }}</span></v-card-title>
        <v-card-text>
          <v-row dense>
            <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
              <v-text-field v-model="search" placeholder="ค้นหาจากรายชื่อผู้เข้าร่วมร้านค้า affiliate" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="12">
              <v-row>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                  <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อทั้งหมด {{ tableData.length }} รายการ</span>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายชื่อทั้งหมด {{ tableData.length }} รายการ</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12">
                  <v-data-table
                    :headers="header"
                    :items="tableData"
                    :search="search"
                    class="elevation-1 mt-4"
                    :items-per-page="10"
                    style="width:100%;"
                    height="100%">
                    <template v-slot:[`item.indexTable`]="{index}">
                      {{ index + 1 }}
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </template>
<script>
export default {
  data () {
    return {
      sellerName: '',
      // idTest: null,
      id: null,
      val: [],
      search: '',
      header: [
        { text: 'ลำดับ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '50', value: 'indexTable' },
        { text: 'ชื่อ-สกุล', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'name' },
        { text: 'อีเมล', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'email' },
        { text: 'เบอร์โทรศัพท์', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'phone' }
      ],
      tableData: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      console.log(val)
      if (val === true) {
        this.$router.push({ path: '/userJoinSellerAffiliateMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'userJoinSellerAffiliate')
        this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$EventBus.$on('shop-id', data => {
    //   this.idTest = data
    //   // this.getDataTable(this.idTest)
    //   console.log(this.idTest, 'idTest')
    //   this.getDataTable(this.idTest)
    // })
  },
  created () {
    // var response = this.$store.state.ModuleAdminManage.stateSellerShopID
    // this.idTest = response
    // console.log(this.idTest, 'id')
    this.id = this.$route.query.id
    this.getDataTable(this.id)
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/userJoinSellerAffiliate' }).catch(() => {})
    },
    backtoPageMobile () {
      this.$router.push({ path: '/userJoinSellerAffiliateMobile' }).catch(() => {})
    },
    async getDataTable (id) {
      console.log(id, 'id')
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: id
      }
      await this.$store.dispatch('actionsGetUserBySeller', data)
      var response = await this.$store.state.ModuleAdminManage.stateGetUserBySeller
      if (response.code === 200) {
        this.tableData = response.data.users
        this.sellerName = response.data.name_th
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>
