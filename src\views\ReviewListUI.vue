<template>
    <ReviewTable/>
</template>
<script>
export default {
  components: {
    ReviewTable: () => import('@/components/UserProfile/ReviewListBuyer')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ReviewListBuyerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ReviewListBuyer' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>
