import AxiosDigitalSignature from '../store_digital_signature/axios_digital_signature'

const ModuleDigitalSignature = {
  state: {
    stateListSignature: [],
    stateSetSignature: [],
    stateEditSignature: [],
    stateDeleteSignature: [],
    stateSetDefaultSignature: [],
    stateSignDocuments: []
  },
  mutations: {
    mutationsListSignature (state, data) {
      state.stateListSignature = data
    },
    mutationsSetSignature (state, data) {
      state.stateSetSignature = data
    },
    mutationsEditSignature (state, data) {
      state.stateEditSignature = data
    },
    mutationsDeleteSignature (state, data) {
      state.stateDeleteSignature = data
    },
    mutationsSetDefaultSignature (state, data) {
      state.stateSetDefaultSignature = data
    },
    mutationsSignDocuments (state, data) {
      state.stateSignDocuments = data
    }
  },
  actions: {
    async actionsListSignature (context, access) {
      const response = await AxiosDigitalSignature.ListSignature(access)
      await context.commit('mutationsListSignature', response)
    },
    async actionsSetSignature (context, access) {
      const response = await AxiosDigitalSignature.SetSignature(access)
      await context.commit('mutationsSetSignature', response)
    },
    async actionsEditSignature (context, access) {
      const response = await AxiosDigitalSignature.EditSignature(access)
      await context.commit('mutationsEditSignature', response)
    },
    async actionsDeleteSignature (context, access) {
      const response = await AxiosDigitalSignature.DeleteSignature(access)
      await context.commit('mutationsDeleteSignature', response)
    },
    async actionsSetDefaultSignature (context, access) {
      const response = await AxiosDigitalSignature.SetDefaultSignature(access)
      await context.commit('mutationsSetDefaultSignature', response)
    },
    async actionsSignDocuments (context, access) {
      const response = await AxiosDigitalSignature.SignDocuments(access)
      await context.commit('mutationsSignDocuments', response)
    }
  }
}

export default ModuleDigitalSignature
