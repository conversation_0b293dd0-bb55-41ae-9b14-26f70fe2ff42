<template>
  <v-container :class="MobileSize ? 'mt-4' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-row dense>
        <v-col cols="12" sm="9" class="py-4">
            <span v-if="!MobileSize" class="f-left ml-3" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">การเข้าชม</span>
            <span v-else class="f-left ml-3" style="line-height: 24px; color: #333333; font-weight: 700; font-size: 20px;"><v-icon color="#27AB9C" class="mr-2" @click="backtopage()">mdi-chevron-left</v-icon> การเข้าชม</span>
        </v-col>
      </v-row>
      <v-row dense>
        <v-col cols="12" class="py-2 px-2 d-flex justify-end">
            <v-btn color="primary" rounded @click="GetDataReach" class="mr-2" icon ><v-icon dense>mdi-refresh</v-icon></v-btn>
            <v-btn color="primary" rounded @click="dialogCreateLink = true">สร้างลิงก์</v-btn>
        </v-col>
      </v-row>
      <v-row dense>
        <v-col v-if="disabledItems" cols="12">
            <!-- v-for="(item, index) in paginatedItems" :key="index" -->
            <v-row dense v-for="(item, index) in paginatedItems" :key="index" class="my-4">
                <v-col cols="12" style="width: 100%;">
                  <v-card class="mx-auto px-2" elevation="0" style="border: 2px solid #27ab9c; border-radius: 10px;" :style="MobileSize ? ' width: 70%;' : IpadSize ? ' width: 86%;' :  'width: 80%;'">
                    <v-card-text :style="MobileSize || IpadSize ? 'font-size: medium;' : 'font-size: large;'" style="font-weight: 600; margin: -4px; color: black;">{{ item.group_name }}</v-card-text>
                    <v-card-text :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: small;'" style="font-weight: 600; margin: -26px -4px; color: black;">{{ item.link !== '' ? '(' + item.link + ')' : '' }}</v-card-text>
                    <v-card-text :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'" style="font-weight: 600; margin: -12px -4px;">{{ item.path_return }}<v-icon color="primary" small class="ml-2" @click="copyToClipboard(item.path_return)">mdi-content-copy</v-icon></v-card-text>
                    <v-row dense :style="MobileSize || IpadSize ? 'width: 100%;' : 'gap: 5vw; width: 90%;'" class="mx-auto">
                      <v-col class="ma-2 pa-2 d-flex align-center" style="background-color: #ffffff; border-radius: 10px; width: 100%;">
                        <!-- <v-card-title class="mx-2 px-auto" style="font-size: small; font-weight: 500; margin-top: -12px;">ชื่อกลุ่ม</v-card-title> -->
                        <v-icon color="primary" :size="MobileSize || IpadSize ? '36' : '48'" style="margin-bottom: -10px;">mdi-eye</v-icon>
                        <v-spacer></v-spacer>
                        <div class="d-flex flex-column">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                            <v-card-text v-bind="attrs" v-on="on" class="mx-2 px-auto" style="font-weight: 600; text-align: end; margin-bottom: -12px; color: #585856;" :style="MobileSize || IpadSize ? 'font-size: large;' : 'font-size: x-large;'">{{ item.view_count | formatNumber }}</v-card-text>
                            </template>
                            <span>{{ item.view_count ? item.view_count.toLocaleString() : '0' }}</span>
                          </v-tooltip>
                            <span class="mx-2 px-auto" style="font-size: small; font-weight: 500; text-align: end; color: #c6c8cc;">views</span>
                        </div>
                      </v-col>
                      <v-col class="ma-2 pa-2 d-flex align-center" style="background-color: #ffffff; border-radius: 10px; width: 100%;">
                        <!-- <v-card-title class="mx-2 px-auto" style="font-size: small; font-weight: 500; margin-top: -12px;">ชื่อกลุ่ม</v-card-title> -->
                        <v-icon color="primary" :size="MobileSize || IpadSize ? '36' : '48'" style="margin-bottom: -10px;">mdi-cart</v-icon>
                        <v-spacer></v-spacer>
                        <div class="d-flex flex-column">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                            <v-card-text v-bind="attrs" v-on="on" class="mx-2 px-auto" style=" font-weight: 600; text-align: end; margin-bottom: -12px; color: #585856;" :style="MobileSize || IpadSize ? 'font-size: large;' : 'font-size: x-large;'">{{ item.order_count | formatNumber }}</v-card-text>
                            </template>
                            <span>{{ item.order_count ? item.order_count.toLocaleString() : '0' }}</span>
                          </v-tooltip>
                            <span class="mx-2 px-auto" style="font-size: small; font-weight: 500; text-align: end; color: #c6c8cc;">orders</span>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
            </v-row>
            <!-- Pagination -->
            <v-pagination
            v-model="currentPage"
            :length="totalPages"
            :total-visible="5"
            class="mt-4"
            ></v-pagination>
        </v-col>
        <v-col v-else cols="12">
          <v-card-title class="my-8" style="justify-content: center; font-size: large; font-weight: 600;">ไม่มีรายการ การเข้าชม</v-card-title>
        </v-col>
      </v-row>
    </v-card>
  <!-- Dialog สร้างลิงก์  -->
  <v-dialog v-model="dialogCreateLink" persistent width="420">
    <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
          สร้างลิงก์
        </span>
          <v-btn icon dark @click="closeDialogCreateLink()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row justify="center" dense class="pt-6">
          <v-col cols="12" align="center">
            <v-form ref="form">
            <v-text-field v-model="nameGroup" label="ชื่อกลุ่ม" minlength="1" maxlength="40" counter outlined dense :rules="[rules.required]" class="my-2 py-2 px-2"></v-text-field>
            <v-text-field v-model="nameENGroup" label="รหัสกลุ่ม" minlength="1" maxlength="30" counter outlined dense :rules="[rules.required, rules.onlyEnglish]" class="my-2 py-2 px-2"></v-text-field>
            </v-form>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogCreateLink()">ยกเลิก</v-btn>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="!isValidForm" @click="CreateLink()">สร้างลิงก์</v-btn>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <!-- Dialog คัดลอกลิงก์  -->
  <v-dialog v-model="dialogCopyLink" persistent width="420">
    <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
          คัดลอกลิงก์
        </span>
          <v-btn icon dark @click="closeDialogCopyLink()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row justify="center" dense class="pt-6">
          <v-col cols="12" align="center">
            <v-text-field v-model="generateLink" label="ลิงก์" hide-details readonly dense outlined class="my-2 py-2 px-2"></v-text-field>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="CopyGenerateLink()">คัดลอกลิงก์</v-btn>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
  </v-container>
</template>

<script>
import Vue from 'vue'
export default {
  data () {
    return {
      currentPage: 1,
      itemsPerPage: 5,
      header: '',
      url: '',
      viewReach: '',
      orderReach: '',
      dialogCreateLink: false,
      nameGroup: '',
      nameENGroup: '',
      dialogCopyLink: false,
      generateLink: '',
      items: [],
      rules: {
        required: v => !!v || 'กรุณากรอกข้อมูล',
        onlyEnglish: v => /^[A-Za-z0-9]+$/.test(v) || 'เฉพาะภาษาอังกฤษและตัวเลข ห้ามเว้นวรรค'
      },
      disabledItems: true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    totalPages () {
      return Math.ceil(this.items.length / this.itemsPerPage)
    },
    paginatedItems () {
      const start = (this.currentPage - 1) * this.itemsPerPage
      window.scrollTo(0, 0)
      return this.items.slice(start, start + this.itemsPerPage)
      // return ''
    },
    isValidForm () {
      return (
        this.nameGroup && this.nameENGroup &&
        this.rules.onlyEnglish(this.nameENGroup) === true
      )
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminReachMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/AdminReach' }).catch(() => {})
      }
    }
  },
  created () {
    window.scrollTo(0, 0)
    this.formatNumber()
    this.GetDataReach()
  },
  methods: {
    backtopage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async copyToClipboard (text) {
      navigator.clipboard.writeText(text)
      await this.$swal.fire({
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true,
        icon: 'success',
        html: '<h3>คัดลอกสำเร็จ</h3>'
      })
      // this.dialogCopyText = 'คัดลอกสำเร็จ'
      // this.dialogCopy = true
    },
    closeDialogCreateLink () {
      this.dialogCreateLink = false
      this.nameGroup = ''
      this.nameENGroup = ''
      this.generateLink = ''
      this.$nextTick(() => {
        this.$refs.form.resetValidation()
      })
    },
    async CreateLink () {
      var data = {
        target_name: this.nameGroup,
        link_name: this.nameENGroup
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsCreateDataReach', data)
      var response = await this.$store.state.ModuleAdminManage.stateCreateDataReach
      this.$store.commit('closeLoader')
      if (response.code === 200) {
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          html: '<h3>สร้างลิงก์สำเร็จ</h3>'
        })
        this.closeDialogCreateLink()
        this.generateLink = response.data.path_return
        this.dialogCopyLink = true
      } else if (response.code === 400) {
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'error',
          html: response.message
        })
      } else {
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'error',
          html: response.message
        })
        this.closeDialogCreateLink()
      }
    },
    closeDialogCopyLink () {
      this.dialogCopyLink = false
      this.nameGroup = ''
      this.nameENGroup = ''
      this.generateLink = ''
      this.$nextTick(() => {
        this.$refs.form.resetValidation()
      })
      this.GetDataReach()
    },
    async CopyGenerateLink () {
      navigator.clipboard.writeText(this.generateLink)
      await this.$swal.fire({
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        icon: 'success',
        html: '<h3>คัดลอกสำเร็จ</h3>'
      })
    },
    formatNumber () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000000) {
          return (Math.floor(value / 100000) / 10) + ' M'
        } else if (value >= 1000) {
          return (Math.floor(value / 100) / 10) + ' K'
        }
        return value.toString()
      })
    },
    async GetDataReach () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetDataReach')
      var response = await this.$store.state.ModuleAdminManage.stateGetDataReach
      if (response.code === 200) {
        // console.log('GetDataReach', response.data)
        this.items = response.data
        // this.items = []
        if (this.items.length === 0) {
          this.disabledItems = false
        } else {
          this.disabledItems = true
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>

</style>
