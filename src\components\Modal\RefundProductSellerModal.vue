<template>
  <div class="text-center">
    <v-dialog v-model="openModalReturnProductSeller" width="800" :style="MobileSize ? 'z-index: 16000004 !important;' : ''" persistent>
      <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>อนุมัติ</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <!-- ชื่อร้านค้า -->
          <v-form ref="FormReturnProduct" :lazy-validation="lazy">
            <v-card-text>
              <v-row>
                <v-col cols="12" md="7">
                  <v-avatar class="float-left" tile :width="IpadProSize ? '70px' : IpadSize ? '70px' :'70px'" :height="IpadProSize ? '70px' : IpadSize ? '70px' : '70px'">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/store-return-product.png" contain></v-img>
                  </v-avatar>
                  <span :class="!MobileSize ? 'float-left ml-4 mt-6 shop-name' : 'mt-6 ml-4 shop-name'">{{productData.name_th}}</span>
                </v-col>
                <v-col cols="4" md="5" v-if="!MobileSize">
                  <v-chip class="float-right" :color="getColor(productData.status_refund)" small :text-color="getTextColor(productData.status_refund)">
                    <span style="font-size: 37px; margin-right: 2px;" :text-color="getTextColor(productData.status_refund)"> &#8226; </span>{{ getStatus(productData.status_refund) }}
                  </v-chip>
                  <span class="mr-2 float-right">สถานะ : </span>
                </v-col>
              </v-row>
              <!-- ข้อมมูลสินค้า -->
              <v-row>
                <v-col cols="12">
                  <span>รายละเอียดสินค้า</span>
                </v-col>
                <v-row class="ml-3 my-2" v-for="(item, index) in productData.product_list" :key="index">
                  <v-col :cols="MobileSize ? 5 : IpadSize ? 3 : 6" md="2" class="pr-0">
                    <v-row>
                      <v-col cols="9" class="py-0"><span class="title-product">ชื่อสินค้า</span></v-col>
                      <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                      <v-col cols="9" class="py-0"><span class="title-product">รหัสสินค้า</span></v-col>
                      <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                      <v-col cols="12" v-if="item.have_attribute === 'yes'">
                        <div v-if="item.have_attribute === 'yes'">
                          <div v-if="item.key_2_value !== null && item.key_2_value !== ''">
                            <v-row>
                              <v-col cols="9" class="py-0"><span class="title-product">{{item.key_1_value}}</span></v-col>
                              <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                              <v-col cols="9" class="py-0"><span class="title-product">{{item.key_2_value}}</span></v-col>
                              <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                            </v-row>
                          </div>
                          <div v-else>
                            <v-row>
                              <v-col cols="9" class="py-0"><span class="title-product">{{item.key_1_value}}</span></v-col>
                              <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                            </v-row>
                          </div>
                        </div>
                      </v-col>
                      <v-col cols="9" class="py-0"><span class="title-product">จำนวนสินค้า</span></v-col>
                      <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="MobileSize ? 7 : IpadSize ? 9 : 6" md="10" class="pl-0">
                    <v-row>
                      <v-col cols="12" class="py-0"><span class="detail-product" align="start" v-snip="1"><b>{{item.product_name}}</b></span></v-col>
                      <v-col cols="12" class="py-0"><span class="detail-product" align="start"><b>{{item.sku}}</b></span></v-col>
                      <v-col cols="12"  v-if="item.have_attribute === 'yes'">
                        <div v-if="item.have_attribute === 'yes'">
                          <div v-if="item.key_2_value !== null || item.key_2_value !== ''">
                            <v-row>
                              <v-col cols="12" class="py-0"><span class="detail-product">{{item.product_attribute_detail.attribute_priority_1}}</span></v-col>
                              <v-col cols="12" class="py-0"><span class="detail-product">{{item.product_attribute_detail.attribute_priority_2}}</span></v-col>
                            </v-row>
                          </div>
                          <div v-else>
                            <v-row>
                              <v-col cols="12" class="py-0"><span class="detail-product">{{item.product_attribute_detail.attribute_priority_1}}</span></v-col>
                            </v-row>
                          </div>
                        </div>
                      </v-col>
                      <v-col cols="12" class="py-0"><span class="detail-product" align="start"><b>{{item.quantity}}</b></span></v-col>
                    </v-row>
                  </v-col>
                  <v-divider class="mt-3" v-if="productData.product_list.length !== 1 && index !== (productData.product_list.length - 1)" style="border: 1px solid #F2F2F2;"></v-divider>
                </v-row>
              </v-row>
              <!-- สาเหตุในการคืนสินค้า -->
              <v-row class="mt-6">
                <v-col cols="12" class="pb-0">
                  <span>สาเหตุในการคืนสินค้า</span><br>
                  <v-col cols="12" md="7" class="pl-0">
                    <v-text-field v-model="productData.reason" outlined dense readonly></v-text-field>
                  </v-col>
                </v-col>
              </v-row>
              <!-- เพิ่มรูปภาพ -->
              <v-row>
                <v-col cols="12" md="12">
                  <span>รูปภาพหรือวิดีโอสินค้า</span><br>
                  <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                    <v-card-text>
                      <div v-if="productData.image.length !== 0" class="mt-4">
                        <div class="row  fill-height align-center">
                          <v-col justify="center" v-for="(item, index) in productData.image" :key="index" cols="6" md="3" sm="4">
                            <v-card v-if="item.type === 'vdo'" outlined class="pa-2" width="146" height="100%">
                              <!-- <v-img :src="item.link" :lazy-src="item.link" width="130" height="130" contain></v-img> -->
                              <video autoplay loop muted playsinline id="upload-video" width="100%" controls>
                                <source :src="item.link"  type="video/mp4">
                              </video>
                            </v-card>
                            <v-card v-else outlined class="pa-2" width="146" height="146">
                              <v-img :src="item.link" :lazy-src="item.link" width="130" height="130" contain></v-img>
                            </v-card>
                          </v-col>
                        </div>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <!-- รายละเอียดการคืนสินค้า -->
              <v-row class="mt-3">
                <v-col cols="12" class="pb-0">
                  <span>รายละเอียด</span><br>
                  <v-col class="px-0">
                    <v-textarea v-model="productData.description" solo name="input-7-4" readonly></v-textarea>
                  </v-col>
                </v-col>
              </v-row>
              <v-row class="mt-2 pl-2" v-if="productData.status_refund !== 'approve'">
                <v-radio-group v-model="refundStatus" row>
                  <v-radio color="#27AB9C" label="อนุมัติ" value="approve"></v-radio>
                  <v-radio color="#27AB9C" label="ไม่อนุมัติ" value="reject"></v-radio>
                </v-radio-group>
              </v-row>
              <v-row class="mt-2" v-if="refundStatus === 'reject'">
                <v-col cols="12" class="pb-0">
                  <span>รายละเอียดการไม่อนุมัติ<span style="color: red;"> *</span></span><br>
                  <v-col class="px-0">
                    <v-textarea v-model="sellerComment" placeholder="ระบุรายละเอียด" solo name="input-7-4" :maxlength="1024" :counter="1024"></v-textarea>
                  </v-col>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
          <v-card-actions v-if="productData.status_refund !== 'approve'">
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="approvedRequestRefundProduct()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import { Decode } from '@/services'
import eventBus from '@/components/eventBus'
export default {
  props: ['ModalReturnProductSeller'],
  data () {
    return {
      data: [],
      lazy: false,
      disabledForm: true,
      openModalReturnProductSeller: false,
      productData: {},
      referenceId: '',
      sellerComment: '',
      image: [
        {
          url: 'https://image.uniqlo.com/UQ/ST3/AsianCommon/imagesgoods/434989/item/goods_01_434989.jpg?width=1600&impolicy=quality_75'
        },
        {
          url: 'https://image.uniqlo.com/UQ/ST3/AsianCommon/imagesgoods/434989/item/goods_01_434989.jpg?width=1600&impolicy=quality_75'
        },
        {
          url: 'https://image.uniqlo.com/UQ/ST3/AsianCommon/imagesgoods/434989/item/goods_01_434989.jpg?width=1600&impolicy=quality_75'
        },
        {
          url: 'https://image.uniqlo.com/UQ/ST3/AsianCommon/imagesgoods/434989/item/goods_01_434989.jpg?width=1600&impolicy=quality_75'
        },
        {
          url: 'https://image.uniqlo.com/UQ/ST3/AsianCommon/imagesgoods/434989/item/goods_01_434989.jpg?width=1600&impolicy=quality_75'
        },
        {
          url: 'https://image.uniqlo.com/UQ/ST3/AsianCommon/imagesgoods/434989/item/goods_01_434989.jpg?width=1600&impolicy=quality_75'
        }
      ],
      refundStatus: ''
    }
  },
  watch: {
  },
  mounted () {
    this.$EventBus.$on('open', this.open)
  },
  async created () {
    await this.open()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    async succesStatus () {
      await eventBus.$emit('setStatus')
      localStorage.setItem('statusTransaction', 'approve')
    },
    open () {
      this.ModalReturnProductSeller.status === true ? this.openModalReturnProductSeller = true : this.openModalReturnProductSeller = false
      this.referenceId = this.ModalReturnProductSeller.orderNumber
      this.getDetailRefundApproveSeller()
    },
    async getDetailRefundApproveSeller () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      const data = {
        role_user: dataRole.role,
        reference_id: this.referenceId
      }
      await this.$store.dispatch('actionsApproveDetailRefundSeller', data)
      var res = await this.$store.state.ModuleOrder.stateApproveDetailRefundSeller
      this.productData = res.data[0]
    },
    cancel () {
      this.openModalReturnProductSeller = false
    },
    checkStatusComment (status) {
      if (status === 'reject') {
        if (this.sellerComment === '') {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'กรุณากรอกเหตุผลที่ไม่อนุมัติ', timer: 2500 })
        } else {
          return this.sellerComment
        }
      }
    },
    approvedRequestRefundProduct () {
      if (this.refundStatus !== '') {
        if (this.refundStatus === 'reject') {
          if (this.sellerComment === '') {
            this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'กรุณากรอกเหตุผลที่ไม่อนุมัติ', timer: 2500 })
          } else {
            this.sendApprovedRequestRefundProduct()
          }
        } else {
          this.sendApprovedRequestRefundProduct()
        }
      } else {
        this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'กรุณาเลือกการอนุมัติ', timer: 2500 })
      }
    },
    async sendApprovedRequestRefundProduct () {
      const data = {
        reference_id: this.referenceId,
        status_approve: this.refundStatus,
        seller_comment: this.sellerComment
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsApproveRefundSeller', data)
      var res = await this.$store.state.ModuleOrder.stateApproveRefundSeller
      if (res.message === 'Approve refund success') {
        this.$store.commit('closeLoader')
        if (this.refundStatus === 'approve') {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'success', text: 'อนุมัติเรียบร้อย', timer: 2500 })
        } else {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'success', text: 'ไม่อนุมัติเรียบร้อย', timer: 2500 })
        }
        this.succesStatus()
        this.openModalReturnProductSeller = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'error', text: 'ดำเนินการไม่สำเร็จ', timer: 2500 })
        this.openModalReturnProductSeller = false
      }
    },
    getColor (item) {
      if (item === 'waiting') return '#FCF0DA'
      else if (item === 'approve') return '#F0F9EE'
      else if (item === 'reject') return '#F7D9D9'
    },
    getTextColor (item) {
      if (item === 'waiting') return '#E9A016'
      else if (item === 'approve') return '#1AB759'
      else if (item === 'reject') return '#D1392B'
    },
    getStatus (item) {
      if (item === 'waiting') return 'รออนุมัติ '
      else if (item === 'approve') return 'อนุมัติ'
      else if (item === 'reject') return 'ไม่อนุมัติ'
    }
  }
}
</script>
<style lang="css" scoped>
.v-dialog__content {
  z-index: 16000006 !important;
}
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
.title-product {
  font-size: 14px;
}
.detail-product {
  font-size: 14px;
}
.shop-name {
  font-weight: 700;
  font-size: 20px;
}
</style>
