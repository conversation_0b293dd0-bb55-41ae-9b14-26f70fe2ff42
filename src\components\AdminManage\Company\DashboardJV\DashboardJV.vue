<template>
  <v-container width="100%" height="100%" style="background: #FFFFFF; border: 0px solid; border-radius: 8px;">
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-row no-gutters class="d-flex" align="center">
        <!-- Title -->
        <v-col v-if="IpadSize" cols="2">
          <div class="text-container">
            <v-card-title class="pl-0 pr-0 pt-0 mb-12" style="font-weight: 700; font-size: 24px; line-height: 32px;">แดชบอร์ด JV</v-card-title>
          </div>
        </v-col>
        <v-col v-else cols="2">
          <div class="text-container">
            <v-card-title class="pl-0 pr-0" style="font-weight: 700; font-size: 24px; line-height: 32px;">แดชบอร์ด JV</v-card-title>
          </div>
        </v-col>
      </v-row>
    </v-card>
    <v-row>
       <v-col>
        <v-card class="btn-box" elevation="0" v-if="chartSelected === 'revenue'">
          <v-btn :class="{'active-btn-selected': chartSelected === 'revenue'}" @click="onChartSelected('revenue')">
            ข้อมูลรายได้
          </v-btn>
          <v-btn :class="{'active-btn-default': chartSelected === 'revenue'}" @click="onChartSelected('document')">
            ข้อมูลเอกสาร
          </v-btn>
        </v-card>
        <v-card class="btn-box" elevation="0" v-if="chartSelected === 'document'">
          <v-btn :class="{'active-btn-default': chartSelected === 'document'}" @click="onChartSelected('revenue')">
            ข้อมูลรายได้
          </v-btn>
          <v-btn :class="{'active-btn-selected': chartSelected === 'document'}" @click="onChartSelected('document')">
            ข้อมูลเอกสาร
          </v-btn>
        </v-card>
      </v-col>
    </v-row>
    <!-- ข้อมูลรายได้ header & shop selection -->
    <v-card v-if="RevenueEnabled" class="mb-4" style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="6" class="mt-4">
          <v-avatar rounded size="24">
            <v-img contain :src="passiveIncomeIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">ข้อมูลรายได้</span>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="4" class="d-flex">
          <v-select
            v-model="selectedItemShop"
            :items="itemsShop"
            item-text="name_th"
            item-value="id"
            label="เลือกบริษัท"
            height="22px"
            dense
            outlined
            :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
            @change="handleSelectChange"
          ></v-select>
        </v-col>
        <!-- Dropdowns IpadSize -->
        <v-col v-if="IpadSize" cols="8" align="end">
          <v-row>
            <v-col cols="12" class="mt-1">
              <span style="font-size: 16px; font-weight: 500;">แสดงผล :</span>
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedDropdown || 'รายปี' }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
                  <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
                  <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
                </v-list>
              </v-menu>
            </v-col>

            <!-- Conditional year dropdown -->
            <v-col v-if="!showDatePicker" cols="12">
              <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
              <v-menu v-if="showYearDropdown" offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
                </v-list>
              </v-menu>

              <!-- Conditional month dropdown -->
              <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
              <v-menu v-if="showMonthDropdown" offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedMonth || 'เลือกเดือน' }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item v-for="(month, index) in months" :key="index" >
                    <span @click="onMonthSelected(month)" style="cursor: pointer;">
                    {{ month.text }}
                    </span>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>

            <!-- Conditional date picker -->
            <v-col cols="12">
              <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
              <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent :return-value.sync="date" width="480px">
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="dateRangeText"
                    placeholder="วว/ดด/ปปปป"
                    dense
                    rounded
                    readonly
                    style="border: 1px solid #EBEBEB; border-radius: 8px;"
                    v-bind="attrs"
                    v-on="on"
                    class="d-inline-block ml-2 custom-text-field"
                  >
                  <v-spacer></v-spacer>
                    <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  style="font-size:29px !important; height: 480px !important"
                  v-model="dates"
                  scrollable
                  reactive
                  locale="Th-th"
                  range
                  no-title
                  full-width
                  :min="minDate"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                <v-row>
                  <v-col align="end">
                    <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                    <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                  </v-col>
                </v-row>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
        </v-col>
        <!-- Dropdowns IpadPro and Desktop -->
        <v-col v-else cols="8" align="end">
          <span style="font-size: 16px; font-weight: 500;">แสดงผล :</span>
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedDropdown || 'รายปี' }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
              <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
              <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
            </v-list>
          </v-menu>

          <!-- Conditional year dropdown -->
          <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
          <v-menu v-if="showYearDropdown" offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
            </v-list>
          </v-menu>

          <!-- Conditional month dropdown -->
          <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
          <v-menu v-if="showMonthDropdown" offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedMonth || 'เลือกเดือน' }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item v-for="(month, index) in months" :key="index" >
                <span @click="onMonthSelected(month)" style="cursor: pointer;">
                {{ month.text }}
                </span>
              </v-list-item>
            </v-list>
          </v-menu>

          <!-- Conditional date picker -->
          <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
          <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent :return-value.sync="date" width="480px">
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="dateRangeText"
                placeholder="วว/ดด/ปปปป"
                dense
                rounded
                readonly
                style="border: 1px solid #EBEBEB; border-radius: 8px;"
                v-bind="attrs"
                v-on="on"
                class="d-inline-block ml-2 custom-text-field"
              >
              <v-spacer></v-spacer>
                <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              style="font-size:29px !important; height: 480px !important"
              v-model="dates"
              scrollable
              reactive
              locale="Th-th"
              range
              no-title
              full-width
              :min="minDate"
              :max="
                new Date(
                  Date.now() - new Date().getTimezoneOffset() * 60000
                )
                  .toISOString()
                  .substr(0, 10)
              "
            >
            <v-row>
              <v-col align="end">
                <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
              </v-col>
            </v-row>
            </v-date-picker>
          </v-dialog>
        </v-col>
      </v-row>
    </v-card>
    <!-- กราฟ -->
     <!-- <v-card-title v-if="RevenueEnabled">123456</v-card-title>
     <v-card-title v-if="DocumentEnabled">654321</v-card-title> -->
    <v-card v-if="RevenueEnabled" class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-layout align-center justify-center>
        <v-flex>
          <v-card>
            <v-card-title>
              <v-row>
                <v-col cols="6">
                  <v-avatar rounded size="20">
                    <v-img contain :src="statisticsIconPath"></v-img>
                  </v-avatar>
                  <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">กราฟแสดงรายได้</span>
                </v-col>
                <v-col cols="6" align="end">
                  <v-avatar rounded size="27" class="mt-2">
                    <v-img contain :src="graphLineIconPath"></v-img>
                  </v-avatar>
                  <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">รายได้</span>
                </v-col>
              </v-row>
            </v-card-title>
            <v-card-text>
              <apexchart  height="400" type="line" :options="chartOptions" :series="chartSeries"></apexchart>
            </v-card-text>
          </v-card>
        </v-flex>
      </v-layout>
    </v-card>
    <v-card v-if="RevenueEnabled" class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-row>
        <!-- ยอดขาย -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon2"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalSale).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ยอดขายทั้งหมด <br/> (บาท)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- จำนวนรายการสั่งซื้อทั้งหมด -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon3"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ totalOrder ? Number(totalOrder).toLocaleString(undefined) : '0' }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">จำนวนรายการสั่งซื้อทั้งหมด <br/> (รายการ)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- ค่าธรรมเนียม -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon4"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalFee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ค่าธรรมเนียมทั้งหมด <br/> (บาท)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- ยอดเงินสุทธิ -->
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px;background: #f5f5f5;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="80">
                  <v-img contain :src="Icon6"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ Number(totalNetAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ยอดเงินสุทธิทั้งหมด <br/> (บาท)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <!-- รายการสั่งซื้อสินค้าทั้งหมด -->
    <v-card v-if="RevenueEnabled" class="mb-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="dataModelIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">รายการสั่งซื้อสินค้าทั้งหมด ({{ totalOrder }} รายการ)</span>
        </v-col>
        <v-col v-if="saleOrder.length !== 0" cols="3" class="mt-5 d-flex justify-end">
          <v-btn @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'orderlist', 'รายการสั่งซื้อสินค้าทั้งหมด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- Data table -->
    <div v-if="RevenueEnabled">
    <v-card elevation="0">
      <v-data-table
        v-if="saleOrder.length !== 0"
        :headers="headers"
        :items="saleOrder"
        :items-per-page="5"
        style="width:100%;"
        height="100%"
        class="elevation-1"
        item-key="i"
        no-data-text="ไม่มีรายการสั่งซื้อสินค้า"
      >
        <template v-slot:[`item.paid_datetime`]="{ item }">
          {{ new Date(item.paid_datetime).toLocaleDateString('th-TH', {
            timeZone: "UTC",
            year: 'numeric',
            month: 'long',
            day: 'numeric' })
          }}
        </template>
        <template v-slot:[`item.product_list`]="{ item }">
          <v-row dense justify="center">
            <v-btn
              x-small
              outlined
              @click="openDialog(item)"
              style="border: none; width:100%;"
              height="100%"
              class="pt-4 pb-4"
            >
              <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
            </v-btn>
          </v-row>
        </template>
        <template v-slot:[`item.TxnAmount`]="{ item }">
          <span>{{ Number(item.TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.vat_price_payment`]="{ item }">
          <span>{{ Number(item.vat_price_payment).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_affiliate`]="{ item }">
          <span>{{ Number(item.total_affiliate).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.discount_coupon`]="{ item }">
          <span>{{ Number(item.discount_coupon).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.discount_ngc`]="{ item }">
          <span>{{ Number(item.discount_ngc).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_gp`]="{ item }">
          <span>{{ Number(item.total_gp).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_shipping`]="{ item }">
          <span>{{ Number(item.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
        <template v-slot:[`item.total_shop`]="{ item }">
          <span>{{ Number(item.total_shop).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </template>
      </v-data-table>
      <v-card v-else elevation="0">
        <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
      </v-card>
    </v-card>
    <br>
    <!-- TOP 10 เซอร์วิสขายดี header -->
    <v-card style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6 mb-4">
          <v-avatar rounded size="24">
            <v-img contain :src="uniqueIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 เซอร์วิสขายดี</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="bestSeller.length !== 0" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestsoldproduct', 'TOP_10_เซอร์วิสขายดี')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    </div>
    <!-- TOP 10 เซอร์วิสขายดี body -->
    <div v-if="RevenueEnabled">
    <v-card v-if="bestSeller.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in bestSeller.slice(0, 5)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #38C9B9;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <!-- 1 -->
                    <!-- Conditionally render specific avatar for index 0 -->
                    <v-avatar v-if="index === 0" rounded size="43">
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            1
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            2
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            3
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- For other indices, render a different avatar -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">
                        {{ Number( item.sold ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} เซอร์วิส
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showBestSeller : true">
          <v-row no-gutters>
            <v-col v-for="(item, index) in bestSeller.slice(5, 10)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #38C9B9;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">
                        {{ Number( item.sold ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} เซอร์วิส
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    <!-- show more -->
    <div v-if="RevenueEnabled">
    <v-row v-if="MobileSize || IpadSize">
      <v-col align="center">
        <v-card-actions v-if="bestSeller.length > 5">
          <v-divider></v-divider>
          <v-btn
            color="blue"
            text
            @click="showBestSeller = !showBestSeller"
          >
            {{ showBestSeller ? 'ย่อ' : 'เพิ่มเติม' }}
          </v-btn>

          <v-btn
            icon
            @click="showBestSeller = !showBestSeller"
          >
            <v-icon>{{ showBestSeller ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
          <v-divider></v-divider>
        </v-card-actions>
      </v-col>
    </v-row>
    </div>
    <!-- /show more -->
    <!-- TOP 10 มูลค่าการสั่งซื้อ header -->
    <v-card v-if="RevenueEnabled" style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="commissionIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 มูลค่าการสั่งซื้อ</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="orderValue.length !== 0" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestrevenuproduct', 'TOP_10_มูลค่าการสั่งซื้อ')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    </div>
    <!-- TOP 10 มูลค่าการสั่งซื้อ body -->
    <div v-if="RevenueEnabled">
    <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row>
            <v-col v-for="(item, index) in orderValue.slice(0, 5)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar v-if="index === 0" rounded size="43">
                      <!-- Content for index 0 -->
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            1
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            2
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                        >
                          <font style="font-size: 20px;">
                            3
                          </font>
                        </span>
                      </v-img>
                    </v-avatar>
                    <!-- For other indices, render a different avatar -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">
                        {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showOrderValue : true">
          <v-row>
            <v-col v-for="(item, index) in orderValue.slice(5, 10)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #FE6F07;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.product_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="two-lines">{{ item.product_name }}</span>
                    <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">
                        {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    </div>
    <!-- show more -->
    <div v-if="RevenueEnabled">
    <v-row v-if="MobileSize || IpadSize">
      <v-col align="center">
        <v-card-actions v-if="orderValue.length > 5">
          <v-divider></v-divider>
          <v-btn
            color="blue"
            text
            @click="showOrderValue = !showOrderValue"
          >
            {{ showOrderValue ? 'ย่อ' : 'เพิ่มเติม' }}
          </v-btn>

          <v-btn
            icon
            @click="showOrderValue = !showOrderValue"
          >
            <v-icon>{{ showOrderValue ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
          <v-divider></v-divider>
        </v-card-actions>
      </v-col>
    </v-row>
    </div>
    <!-- /show more -->
    <!-- TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด header -->
    <div v-if="RevenueEnabled">
    <v-card style="border-radius: 8px;" elevation="0">
      <v-row>
        <v-col cols="9" class="mt-6">
          <v-avatar rounded size="24">
            <v-img contain :src="bestCustomerIconPath"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด</span>
        </v-col>
        <v-col cols="3" class="mt-5 d-flex justify-end">
          <v-btn v-if="orderValue.length !== 0" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'topbuyers', 'TOP_10_ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <!-- TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด body -->
    <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
      <!-- Top three -->
      <v-row justify="center">
        <v-col cols="3" md="3" sm="4" xs="4" class="d-flex justify-end mb-2 mt-3">
          <v-card v-if="topBuyers.length >= 2" height="164px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
            <v-row>
              <v-col align="center">
                <v-avatar rounded size="43" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                  <v-img contain :src="silverMedalIconPath">
                    <span
                      class="display-1 font-weight-bold"
                      style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                    >
                      <font style="font-size: 20px;">
                        2
                      </font>
                    </span>
                  </v-img>
                </v-avatar>
                <v-avatar v-if="topBuyers[1].user_image === null || topBuyers[1].user_image ===  'not found image'" rounded size="78" color="#FFF">
                  <!-- <span>No image</span> -->
                  <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                </v-avatar>
                <v-avatar v-else size="78" color="#FFF">
                  <v-img contain :src="topBuyers[1].user_image"></v-img>
                </v-avatar>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-0 pb-0">
                <span class="one-lines">{{ topBuyers[1].buyer_name }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-1">
                <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                  <span class="vchipFontSize" style="color: #1B5DD6">
                    {{ Number(topBuyers[1].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                  </span>
                </v-chip>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3" md="3" sm="4" xs="4" class="d-flex justify-center mb-2">
          <v-card v-if="topBuyers.length >= 1" height="176px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
            <v-row>
              <v-col align="center">
                <v-avatar rounded size="43" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                  <v-img contain :src="goldMedalIconPath">
                    <span
                      class="display-1 font-weight-bold"
                      style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                    >
                      <font style="font-size: 20px;">
                        1
                      </font>
                    </span>
                  </v-img>
                </v-avatar>
                <v-avatar v-if="topBuyers[0].user_image === null || topBuyers[0].user_image === 'not found image'" rounded size="90" color="#FFF">
                  <!-- <span>No image</span> -->
                  <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                </v-avatar>
                <v-avatar v-else size="90" color="#FFF">
                  <v-img contain :src="topBuyers[0].user_image"></v-img>
                </v-avatar>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-0 pb-0">
                <span class="one-lines">{{ topBuyers[0].buyer_name }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-1">
                <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                  <span class="vchipFontSize" style="color: #1B5DD6">
                    {{ Number(topBuyers[0].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                  </span>
                </v-chip>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3" md="3" sm="4" xs="4" class="d-flex mb-2 mt-3">
          <v-card v-if="topBuyers.length >= 3" height="164px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
            <v-row>
              <v-col align="center">
                <v-avatar rounded size="43" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                  <v-img contain :src="bronzeMedalIconPath">
                    <span
                      class="display-1 font-weight-bold"
                      style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                    >
                      <font style="font-size: 20px;">
                        3
                      </font>
                    </span>
                  </v-img>
                </v-avatar>
                <v-avatar v-if="topBuyers[2].user_image === null || topBuyers[2].user_image === 'not found image'" rounded size="78" color="#FFF">
                  <!-- <span>No image</span> -->
                  <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                </v-avatar>
                <v-avatar v-else size="78" color="#FFF">
                  <v-img contain :src="topBuyers[2].user_image"></v-img>
                </v-avatar>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-0 pb-0">
                <span class="one-lines">{{ topBuyers[2].buyer_name }}</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col align="center" class="pt-1">
                <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                  <span class="vchipFontSize" style="color: #1B5DD6">
                    {{ Number(topBuyers[2].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                  </span>
                </v-chip>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <!-- First half -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row>
            <v-col v-for="(item, index) in topBuyers.slice(3, 7)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #1B5DD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 4 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.user_image === null || item.user_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else size="44" color="#FFF">
                      <v-img contain :src="item.user_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="one-lines">{{ item.buyer_name }}</span>
                    <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #1B5DD6">
                        {{ Number(item.sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- Second half -->
        <v-col cols="6" md="6" sm="12" xs="12" v-show="MobileSize || IpadSize ? showTopBuyers : true">
          <v-row>
            <v-col v-for="(item, index) in topBuyers.slice(7, 10)" :key="index" cols="12">
              <v-card height="100%" style="border: 1px solid #1B5DD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index + 8 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <v-avatar v-if="item.user_image === null || item.user_image === 'not found image'" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else size="44" color="#FFF">
                      <v-img contain :src="item.user_image"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <span class="one-lines">{{ item.buyer_name }}</span>
                    <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #1B5DD6">
                        {{ Number(item.sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                      </span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card class="mt-1" v-else elevation="0">
      <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
    </v-card>
    <!-- show more -->
    <v-row v-if="MobileSize || IpadSize">
      <v-col align="center">
        <v-card-actions v-if="topBuyers.length > 5">
          <v-divider></v-divider>
          <v-btn
            color="blue"
            text
            @click="showTopBuyers = !showTopBuyers"
          >
            {{ showTopBuyers ? 'ย่อ' : 'เพิ่มเติม' }}
          </v-btn>

          <v-btn
            icon
            @click="showTopBuyers = !showTopBuyers"
          >
            <v-icon>{{ showTopBuyers ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
          <v-divider></v-divider>
        </v-card-actions>
      </v-col>
    </v-row>
    <!-- /show more -->
    </div>

    <!-- Dashboard Document -->
    <v-row v-if="DocumentEnabled">
      <v-col cols="4" class="d-flex mt-4">
        <v-select
          v-model="selectedItemSellerShop"
          :items="itemsSellerShop"
          item-text="name_th"
          item-value="id"
          label="เลือกร้านค้า"
          height="22px"
          dense
          outlined
          :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
          @change="handleSelectChangeSeller"
        ></v-select>
      </v-col>
    </v-row>
    <v-row dense v-if="DocumentEnabled">
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >QT</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #587fe3;
                  font-size: 12px;
                "
              >
                {{formatNumber(docQT)}}
              </v-chip>
            </span>
            <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
            <p
            v-bind="attrs" v-on="on"
              style="
                color: #587fe3;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
              v-if="docRevenue > 9999"
            >
            THB {{formatNumber(docRevenue)}}
            </p>
            <p
            v-bind="attrs" v-on="on"
              style="
                color: #587fe3;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
              v-else
            >
            THB {{formatNumberTooltip(docRevenue)}}
            </p>
            </template>
            <span>{{ formatNumberTooltip(docRevenue) }} บาท</span>
            </v-tooltip>
          </div>
        </v-card>
      </v-col>
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >SO</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #f87956;
                  font-size: 12px;
                "
              >
              {{formatNumber(docSO)}}
              </v-chip>
            </span>
            <p
              style="
                color: #f87956;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
            >
              {{formatNumber(docSORevenue)}}
            </p>
          </div>
        </v-card>
      </v-col>
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >PR</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #67b7dc;
                  font-size: 12px;
                "
              >
                {{formatNumber(docPR)}}
              </v-chip>
            </span>
            <p
              style="
                color: #67b7dc;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
            >
              {{formatNumber(docPRRevenue)}}
            </p>
          </div>
        </v-card>
      </v-col>
      <v-col cols="6" md="3" sm="6" class="QT">
        <v-card class="pa-1 card" outlined>
          <div style="margin: 10px 0px -10px 10px">
            <span>
              <span
                style="color: #535353; font-size: 16px; font-weight: 600"
                >PO</span
              >
              <v-chip
                style="
                  color: #f5f5f5;
                  margin-left: 5px;
                  height: 16px;
                  display: inline-flex;
                  justify-content: center;
                  background: #7d72b9;
                  font-size: 12px;
                "
              >
                {{formatNumber(docPO)}}
              </v-chip>
            </span>
            <p
              style="
                color: #7d72b9;
                font-size: 28px;
                font-weight: 600;
                line-height: normal;
              "
            >
              {{formatNumber(docPORevenue)}}
            </p>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense v-if="DocumentEnabled">
      <v-col class="QT">
        <v-card class="pa-2 card" outlined>
          <div id="chart">
          <v-col>
            <v-row class="d-flex justify-space-between">
              <div style="height: 50px;">
                <p style="font-size:16px; padding-top:10px;">Summary Trend</p>
              </div>
              <div >
              <v-chip-group v-model="firstLine">
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('day')">วัน</v-chip>
                <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docLine('month')">เดือน</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('quarter')">ไตรมาส</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('halfyear')">ครึ่งปี</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docLine('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
          </v-col>
          <!-- {{docOfLine}} -->
          <v-divider style="background:#67B7DC66"></v-divider>
            <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" v-if="isLoadLine === true">
              <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
            </div>
            <apexchart
              type="line"
              height="350"
              :options="optionLine.chartOptions"
              :series="seriesLine.series"
              v-else
            ></apexchart>
          <div style="color:red; margin-top:-22px" class="d-flex justify-end">
            * กราฟแสดงเฉพาะวันที่มี Transaction เท่านั้น
            </div>
          </div>
        </v-card>
      </v-col>
      <v-col class="QT">
        <v-card class="pa-2 card" outlined>
          <div id="chart">
          <v-col>
            <v-row class="d-flex justify-space-between">
              <div style="height: 50px;">
                <p style="font-size:16px; padding-top:10px;">จำนวนเอกสาร</p>
              </div>
              <div >
              <v-chip-group v-model="firstBar">
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('day')">วัน</v-chip>
                <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docBar('month')">เดือน</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('quarter')">ไตรมาส</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('halfyear')">ครึ่งปี</v-chip>
                <v-chip label plain small color="transparent" class="custom-chip" @click="docBar('year')">ปี</v-chip>
                </v-chip-group>
              </div>
            </v-row>
          </v-col>
          <!-- {{docOfBar}} -->
          <v-divider style="background:#67B7DC66"></v-divider>
            <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" v-if="isLoad === true">
              <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
            </div>
            <apexchart
              type="bar"
              height="350"
              :options="option.chartBarOptions"
              :series="series.seriesBar"
              v-else
            ></apexchart>
            <div style="color:red; margin-top:-22px" class="d-flex justify-end">
            * กราฟแสดงเฉพาะวันที่มี Transaction เท่านั้น
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense v-if="DocumentEnabled">
      <v-col class="QT">
        <v-card class="pa-2 card" outlined>
          <div id="chart">
            <v-col>
              <v-row class="d-flex justify-space-between">
                <div style="padding-left: 10px">
                  <p style="font-size:16px; padding-top:10px;">รายการสั่งซื้อสินค้า ( Top 5 )</p>
                </div>
                <div >
                <v-chip-group v-model="firstPie">
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('day')">วัน</v-chip>
                  <v-chip value="month" label plain small color="transparent" class="custom-chip" @click="docPie('month')">เดือน</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('quarter')">ไตรมาส</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('halfyear')">ครึ่งปี</v-chip>
                  <v-chip label plain small color="transparent" class="custom-chip" @click="docPie('year')">ปี</v-chip>
                  </v-chip-group>
                </div>
              </v-row>
            <v-divider style="background:#53535333; margin-top: 5px"></v-divider>
            </v-col>
            <v-col>
              <div class="text-center" style="padding-top: 100px; padding-bottom: 100px;" v-if="isLoadPie === true">
                <v-progress-circular indeterminate color="primary" :size="165"></v-progress-circular>
              </div>
              <v-row style="display: flex; justify-content: center;" v-else>
                <v-col cols="12" md="6" sm="6" >
                  <apexchart type="donut" :options="optionPie.chartOptions" :series="seriesPie.series"></apexchart>
                </v-col>
                <v-col cols="12" md="6" sm="6" :style="MobileSize ? '' : 'margin-left:-40px'">
                  <v-data-table
                    :headers="headersDoc"
                    :items="listTopFive"
                    style="background-color:transparent; height: 350px; width: 100%;"
                    no-data-text="ไม่มีรายการสั่งซื้อ"
                    :items-per-page="5"
                    :page="1"
                    hide-default-footer
                  >
                  <template v-slot:[`item.Revenue`]="{ item }">
                  {{formatNumber(item.Revenue)}}
                  </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </v-col>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <!-- dialog detail -->
    <v-dialog
      v-model="dialog_detail"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">รายการสินค้า</font>
          </span>
          <v-btn icon dark @click="CloseDialog('readonly')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pt-3" style="text-align: center;">
                <span style="color:#333333; font-size: 16px; font-weight: 600;">รายการสินค้าทั้งหมด {{ Number( order_Detail.length ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น</span>
              </v-col>
            </v-row>
        </v-card-text>
        <v-card-text v-bind:style="{'height' : '400px'}">
          <v-container>
            <v-row v-for="(item, index) in order_Detail" :key="index">
              <v-col cols="12">
                <v-card class="mt-4">
                  <v-row>
                    <v-col cols="4" align="center">
                      <v-avatar tile size="160">
                        <div v-if="item.product_image !== ''">
                          <img :src="item.product_image" alt="Product Image" class="avatar-image" />
                        </div>
                        <div v-else>
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </div>
                      </v-avatar>
                    </v-col>
                    <v-col cols="8">
                      <v-card-title class="no-word-break">{{ item.product_name }}</v-card-title>
                      <v-card-text>
                        <div><b>Product ID:</b> {{ item.product_id }}</div>
                        <div><b>Main SKU:</b> {{ item.main_sku }}</div>
                        <div><b>Price:</b> {{ Number( item.revenue_default ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</div>
                        <div><b>รูปแบบการชำระเงิน:</b> {{ typePayment }}</div>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import { Decode } from '@/services'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      chartSeries: [],
      selectedItemShop: -2,
      itemsShop: [],
      showBestSeller: false,
      showOrderValue: false,
      showTopBuyers: false,
      monthSelected: '',
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31', // Set your maximum date here
      dialog_detail: false,
      order_Detail: [],
      saleOrder: [],
      keyField: 'i',
      bestSeller: [],
      orderValue: [], // มูลค่าการสั่งซื้อ
      topBuyers: [],
      xAxis: '',
      UrlExponential: '',
      startDate: new Date().getFullYear(), // วันที่เริ่มต้น default เป็นปีปัจจุบัน
      endDate: new Date().getFullYear(), // วันที่สิ้นสุด default เป็นปีปัจจุบัน
      shopID: '-2',
      selectedItemSellerShop: -3,
      sellerID: -3,
      dateFilter: 'year', // dateFilter default เป็น year เป็นค่าที่หลังบ้านต้องการสำหรับยิง api
      modalDateSelect: false,
      dates: [],
      picker: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      options: ['รายปี', 'รายเดือน', 'รายวัน'], // ตัวเลือกในการแสดงผล
      selectedOption: 'รายปี',
      years: [2022, 2023, 2024, 2025], // ตัวเลือกปี
      showYearDropdown: null,
      selectedDropdown: 'รายปี',
      showMonthDropdown: false,
      selectedYear: new Date().getFullYear(),
      showDatePicker: false,
      chartSelected: 'revenue',
      RevenueEnabled: true,
      DocumentEnabled: false,
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      selectedMonth: null,
      selectedMonthValue: null,
      menu: false,
      selectedDates: [],
      availableYears: [],
      totalSale: '', // ยอดขายทั้งหมด
      totalOrder: '0', // จำนวนรายการสั่งซื้อทั้งหมด
      totalFee: '',
      totalNetAmount: '',
      typePayment: '',
      moneyIconPath: require('@/assets/icons/SellerDashboard/money (1) 1.png'),
      boxIconPath: require('@/assets/icons/SellerDashboard/box 1.png'),
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      dataModelIconPath: require('@/assets/icons/SellerDashboard/data-model 1.png'),
      uniqueIconPath: require('@/assets/icons/SellerDashboard/unique 1.png'),
      commissionIconPath: require('@/assets/icons/SellerDashboard/commission 1.png'),
      bestCustomerIconPath: require('@/assets/icons/SellerDashboard/best-customer-experience 1.png'),
      rankingIconPath: require('@/assets/icons/SellerDashboard/ranking (1) 1.png'),
      graphLineIconPath: require('@/assets/icons/SellerDashboard/graph-line 1.png'),
      goldMedalIconPath: require('@/assets/icons/SellerDashboard/gold-medal.png'),
      silverMedalIconPath: require('@/assets/icons/SellerDashboard/silver-medal.png'),
      bronzeMedalIconPath: require('@/assets/icons/SellerDashboard/bronze-medal.png'),
      Icon1: require('@/assets/WDShop1.png'),
      Icon2: require('@/assets/WDShop2.png'),
      Icon3: require('@/assets/WDShop3.png'),
      Icon4: require('@/assets/WDShop4.png'),
      Icon5: require('@/assets/WDShop5.png'),
      Icon6: require('@/assets/icon6.png'),
      headers: [
        { text: 'วันที่ทำรายการ', width: '160', align: 'center', sortable: true, value: 'paid_datetime', class: 'backgroundTable fontTable--text' },
        { text: 'รายชื่อลูกค้า', width: '200', align: 'center', sortable: false, value: 'buyer_name', class: 'backgroundTable fontTable--text' },
        { text: 'เลขที่ทำรายการสั่งซื้อ', width: '160', sortable: false, align: 'center', value: 'order_number', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา (บาท)', width: '100', sortable: false, align: 'center', value: 'TxnAmount', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าธรรมเนียม (บาท)', width: '150', sortable: false, align: 'center', value: 'vat_price_payment', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชั่น/Affiliate (บาท)', width: '200', sortable: false, align: 'center', value: 'total_affiliate', class: 'backgroundTable fontTable--text' },
        { text: 'คูปองส่วนลด/แต้มส่วนลด (บาท)', width: '220', align: 'center', sortable: false, value: 'discount_coupon', class: 'backgroundTable fontTable--text' },
        { text: 'ส่วนลดของระบบ (บาท)', width: '170', align: 'center', sortable: false, value: 'discount_ngc', class: 'backgroundTable fontTable--text' },
        { text: 'ค่า GP (บาท)', width: '120', sortable: false, align: 'center', value: 'total_gp', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าขนส่ง (บาท)', width: '120', sortable: false, align: 'center', value: 'total_shipping', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนเงินที่ได้รับ (บาท)', width: '170', sortable: false, align: 'center', value: 'total_shop', class: 'backgroundTable fontTable--text' },
        { text: 'รายการสินค้า', width: '120', sortable: false, align: 'center', value: 'product_list', class: 'backgroundTable fontTable--text' }
      ],
      chartOptions: {
        chart: {
          id: 'income-difference-chart',
          // stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          colors: ['#008FFB', '#FF4560']
        },
        markers: {
          size: 5, // Adjust the size of markers as per your preference
          colors: '#fff',
          strokeColors: '#AE8FF7',
          strokeWidth: 2,
          strokeOpacity: 0.9,
          strokeDashArray: 0,
          fillOpacity: 0,
          discrete: [],
          shape: 'circle',
          radius: 2,
          offsetX: 0,
          offsetY: 0,
          onClick: undefined,
          onDblClick: undefined,
          showNullDataPoints: true,
          hover: {
            size: undefined,
            sizeOffset: 6
          }
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      },
      isLoad: false,
      isLoadLine: false,
      isLoadPie: false,
      dataDate: [],
      dataRevenue: [],
      dataDateBar: [],
      dataRevenueBar: [],
      dataDatePie: [],
      dataNamePie: [],
      dataRevenuePie: [],
      dataDateSOBar: [],
      dataRevenueSOBar: [],
      dataDatePRBar: [],
      dataRevenuePRBar: [],
      dataDatePOBar: [],
      dataRevenuePOBar: [],
      listTopFive: [],
      nameTopFive: '',
      qtTopFive: '',
      revenueTopFive: '',
      setOfData: '',
      currentDate: '',
      currentMonth: '',
      docOfBar: 'month',
      docOfLine: 'month',
      docOfPie: 'month',
      firstBar: 'month',
      firstLine: 'month',
      firstPie: 'month',
      docQT: 0,
      docRevenue: 0,
      docSO: 0,
      docSORevenue: 0,
      docPR: 0,
      docPRRevenue: 0,
      docPO: 0,
      docPORevenue: 0,
      currentPage: 1,
      headersDoc: [
        {
          text: 'Customer Name',
          align: 'center',
          sortable: false,
          value: 'name'
        },
        { text: 'จำนวนรายการ', sortable: false, align: 'center', value: 'numDoc' },
        { text: 'Amount', sortable: false, align: 'center', value: 'Revenue' }
      ]
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardJVMobile' }).catch(() => { })
      } else {
        window.location.replace(`${'/dashboardJV'}`)
      }
    },
    docOfBar: async function (val) {
      this.isLoad = true
      await this.getSumDocQT(val)
      await this.getSumDocSO(val)
      await this.getSumDocPR(val)
      await this.getSumDocPO(val)
      this.isLoad = false
    },
    docOfLine: async function (val) {
      this.isLoadLine = true
      await this.getSumTrend(val)
      this.isLoadLine = false
    },
    docOfPie: async function (val) {
      this.isLoadPie = true
      await this.getSumTopFiveQT(val)
      this.isLoadPie = false
    }
  },
  async created () {
    await this.$EventBus.$emit('changeNavAdmin')
    if (this.selectedDropdown === 'รายปี') {
      this.showYearDropdown = true
    }
    this.getShopData()
    this.getSellerShopData()
    this.getPageData()
    this.getOrderList(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopProducts(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getTopBuyers(this.startDate, this.endDate, this.shopID, this.dateFilter)
    this.getQT()
    this.getSumTrend(this.firstLine)
    this.getSumDocQT(this.firstBar)
    this.getSumDocSO(this.firstBar)
    this.getSumDocPR(this.firstBar)
    this.getSumDocPO(this.firstBar)
    this.getSumTopFiveQT(this.firstPie)
  },
  computed: {
    dateRangeText () {
      if (this.dates.length > 1) {
        var startDays = new Date(this.dates[0]).toLocaleDateString('th-TH')
        var endDays = new Date(this.dates[1]).toLocaleDateString('th-TH')
        var totalDays = startDays + ' - ' + endDays
        return totalDays
      } else if (this.dates.length === 1) {
        var oneDays = new Date(this.dates).toLocaleDateString('th-TH')
        return oneDays
      }
      return this.dates.join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    option () {
      if (this.docOfBar === 'day') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '30%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                this.currentDate
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'month') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '70%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              // categories: new Date(this.dataDateBar).toLocaleDateString('th-TH', { year: 'numeric', month: 'long' })
              categories: this.dataDateBar
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'quarter') {
        return {
          chartBarOptions: {
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '80%',
                endingShape: 'rounded'
              }
            },
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 380,
              toolbar: {
                show: false
              }
            },
            fill: {
              opacity: 1
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มีนาคม',
                'เมษายน - มิถุนายน',
                'กรกฎาคม - กันยายน',
                'ตุลาคม - ธันวาคม'
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'halfyear') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '50%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มิถุนายน',
                'กรกฎาคม - ธันวาคม'
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else if (this.docOfBar === 'year') {
        return {
          chartBarOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              // type: 'bar',
              height: 350,
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '80%',
                endingShape: 'rounded'
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม',
                'กุมภาพันธ์',
                'มีนาคม',
                'เมษายน',
                'พฤษภาคม',
                'มิถุนายน',
                'กรกฎาคม',
                'สิงหาคม',
                'กันยายน',
                'ตุลาคม',
                'พฤศจิกายน',
                'ธันวาคม'
              ]
            },
            yaxis: {
              decimalsInFloat: 0,
              labels: {
                formatter: this.formatter
              }
            },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' ใบ'
                }
              }
            }
          }
        }
      } else {
        return null
      }
    },
    optionLine () {
      if (this.docOfLine === 'day') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            // colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                this.currentDate
              ]
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'month') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: this.dataDate
              // {
              //   // data: this.dataDate
              //   data:
              //   '1',
              //   '2',
              //   '3'
              // },
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'quarter') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มีนาคม',
                'เมษายน - มิถุนายน',
                'กรกฎาคม - กันยายน',
                'ตุลาคม - ธันวาคม'
              ]
              // categories: this.dataDate
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'halfyear') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                // return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                return console.log(val)
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              categories: [
                'มกราคม - มิถุนายน',
                'กรกฎาคม - ธันวาคม'
              ]
              // {
              //   // data: this.dataDate
              //   data:
              //   '1',
              //   '2',
              //   '3'
              // },
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else if (this.docOfLine === 'year') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'line',
              toolbar: {
                show: false
              }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
            },
            markers: {
              size: 1
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent']
            },
            xaxis: {
              // categories: this.dataDate
              categories: [
                'มกราคม',
                'กุมภาพันธ์',
                'มีนาคม',
                'เมษายน',
                'พฤษภาคม',
                'มิถุนายน',
                'กรกฎาคม',
                'สิงหาคม',
                'กันยายน',
                'ตุลาคม',
                'พฤศจิกายน',
                'ธันวาคม'
              ]
            },
            // yaxis: {
            //   labels: {
            //     formatter: function (val) {
            //       return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            //     }
            //   }
            // },
            fill: {
              opacity: 1
            },
            tooltip: {
              y: {
                formatter: function (val) {
                  return '' + val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
                }
              }
            }
          }
        }
      } else {
        return null
      }
    },
    optionPie () {
      if (this.docOfPie === 'day') {
        if (this.dataRevenuePie === 0) {
          return {
            chartOptions: {
              legend: {
                position: 'bottom',
                horizontalAlign: 'center'
              },
              chart: {
                height: 350,
                type: 'donut',
                toolbar: {
                  show: false
                }
              },
              labels: {
                show: false
              }
            }
          }
        } else {
          return {
            chartOptions: {
              legend: {
                position: 'bottom',
                horizontalAlign: 'center'
              },
              chart: {
                height: 350,
                type: 'donut',
                toolbar: {
                  show: false
                }
              },
              labels: this.dataNamePie
            }
          }
        }
      } else if (this.docOfPie === 'month') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else if (this.docOfPie === 'quarter') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else if (this.docOfPie === 'halfyear') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else if (this.docOfPie === 'year') {
        return {
          chartOptions: {
            legend: {
              position: 'bottom',
              horizontalAlign: 'center'
            },
            chart: {
              height: 350,
              type: 'donut',
              toolbar: {
                show: false
              }
            },
            labels: this.dataNamePie
          }
        }
      } else {
        return null
      }
    },
    series () {
      if (this.docOfBar === 'day') {
      // กราฟแท่ง
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'month') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'quarter') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'halfyear') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else if (this.docOfBar === 'year') {
        return {
          seriesBar: [
            {
              name: 'QT',
              data: this.dataRevenueBar,
              color: '#587fe3'
            },
            {
              name: 'SO',
              data: this.dataRevenueSOBar,
              color: '#f87956'
            },
            {
              name: 'PR',
              data: this.dataRevenuePRBar,
              color: '#67b7dc'
            },
            {
              name: 'PO',
              data: this.dataRevenuePOBar,
              color: '#7d72b9'
            }
          ]
        }
      } else {
        return {
          seriesBar: [],
          seriesLine: [],
          seriesPie: []
        }
      }
    },
    seriesLine () {
      if (this.docOfLine === 'day') {
      // กราฟเส้น
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'month') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              // data: [12, 23, 59],
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'quarter') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'halfyear') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else if (this.docOfLine === 'year') {
        return {
          series: [
            {
              name: 'QT',
              data: this.dataRevenue,
              color: '#587fe3'
            }
          ]
        }
      } else {
        return {
          seriesLine: [],
          seriesBar: [],
          seriesPie: []
        }
      }
    },
    seriesPie () {
      if (this.docOfPie === 'day') {
        if (this.dataRevenuePie === 0) {
          return {
            series: []
          }
        } else {
          return {
            series: this.dataRevenuePie
          }
        }
      } else if (this.docOfPie === 'month') {
        return {
          series: this.dataRevenuePie
        }
      } else if (this.docOfPie === 'quarter') {
        return {
          series: this.dataRevenuePie
        }
      } else if (this.docOfPie === 'halfyear') {
        return {
          series: this.dataRevenuePie
        }
      } else if (this.docOfPie === 'year') {
        return {
          series: this.dataRevenuePie
        }
      } else {
        return {
          seriesLine: [],
          seriesBar: [],
          seriesPie: []
        }
      }
    }
  },
  methods: {
    getPageData () {
      // โหลดครั้งแรกตอนเปิดหน้านี้
      this.getRevenuGraphSummary(this.startDate, this.endDate, this.shopID, this.dateFilter)
    },
    openDialog (item) {
      this.typePayment = ''
      this.dialog_detail = true
      this.order_Detail = item.product_list
      this.typePayment = item.type_payment
    },
    CloseDialog (val) {
      this.type = val
      this.dialog_detail = false
    },
    ChangeChartOptions (val) {
      this.chartOptions = {
        chart: {
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          },
          markers: {
            size: 4, // Adjust the size of markers as per your preference
            colors: undefined,
            strokeColors: '#fff',
            strokeWidth: 2,
            strokeOpacity: 0.9,
            strokeDashArray: 0,
            fillOpacity: 1,
            discrete: [],
            shape: 'circle',
            radius: 2,
            offsetX: 0,
            offsetY: 0,
            onClick: undefined,
            onDblClick: undefined,
            showNullDataPoints: true,
            hover: {
              // size: undefined,
              sizeOffset: 6
            }
          }
        },
        xaxis: {
          categories: this.showMonthDropdown ? val : this.showDatePicker ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#008FFB', '#FF4560'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      }
    },
    async handleSelectChange () {
      // เรียกตอนเปลี่ยนร้านค้า
      // console.log('Selected item:', this.selectedItemShop)
      this.shopID = this.selectedItemShop
      this.$store.commit('openLoader')
      await this.getPageData()
      await this.getOrderList(this.startDate, this.endDate, this.shopID, this.dateFilter)
      await this.getTopProducts(this.startDate, this.endDate, this.shopID, this.dateFilter)
      await this.getTopBuyers(this.startDate, this.endDate, this.shopID, this.dateFilter)
      this.$store.commit('closeLoader')
    },
    async getShopData () {
      // รายชื่อร้านค้าทั้งหมด เส้น listAllShop
      await this.$store.dispatch('actionListAllShopData')
      var response = await this.$store.state.ModuleDashBoardForAdmin.stateListAllShopData
      if (response.ok === 'y') {
        // console.log('actionListAllShopData', response)
        var statAllShop = [{ name_th: 'ทั้งหมด', id: -2 }]
        this.itemsShop = response.query_result
        this.itemsShop = this.itemsShop.filter(item => item.is_JV === 'yes')
        this.itemsShop = statAllShop.concat(this.itemsShop)
      }
    },
    async handleSelectChangeSeller () {
      // เรียกตอนเปลี่ยนร้านค้า
      // console.log('Selected item Seller:', this.selectedItemSellerShop)
      this.sellerID = this.selectedItemSellerShop
      if (this.selectedItemSellerShop === 'ทั้งหมด') {
        this.sellerID = -3
      }
      this.$store.commit('openLoader')
      await this.getQT()
      await this.getSumTrend(this.docOfLine)
      await this.getSumDocQT(this.docOfBar)
      await this.getSumDocSO(this.docOfBar)
      await this.getSumDocPR(this.docOfBar)
      await this.getSumDocPO(this.docOfBar)
      await this.getSumTopFiveQT(this.docOfPie)
      this.$store.commit('closeLoader')
      // console.log('sellerID', this.sellerID)
    },
    async getSellerShopData () {
      // รายชื่อร้านค้าทั้งหมด เส้น listAllShop
      await this.$store.dispatch('actionListAllShopData')
      var response = await this.$store.state.ModuleDashBoardForAdmin.stateListAllShopData
      if (response.ok === 'y') {
        // console.log('actionListAllShopData', response)
        var statAllSellerShop = [{ name_th: 'ทั้งหมด', id: -3 }]
        this.itemsSellerShop = response.query_result
        this.itemsSellerShop = this.itemsSellerShop.filter(item => item.is_JV === 'yes')
        this.itemsSellerShop = statAllSellerShop.concat(this.itemsSellerShop)
      }
    },
    async getRevenuGraphSummary (startDate, endDate, shopID, dateFilter) {
      // ข้อมูล กราฟ
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      // console.log(data, 'RGS')
      await this.$store.dispatch('actionRevenuGraphSummary', data)
      var response = await this.$store.state.ModuleDashBoard.stateRevenuGraphSummary
      if (response.ok === 'y') {
        // console.log('actionRevenuGraphSummary', response)
        var dataTable = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dataTable.push(response.query_result.revenugrap[i].revenu)
        }
        var dayInMonth = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dayInMonth.push(new Date(response.query_result.revenugrap[i].date).toLocaleDateString('th-TH', { month: 'long', day: 'numeric' }))
        }
        var dateToDate = []
        for (let i = 0; i < response.query_result.revenugrap.length; i++) {
          dateToDate.push(new Date(response.query_result.revenugrap[i].date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        }
        // console.log(dayInMonth, 'dayInMonth')
        if (response.ok === 'y') {
          this.chartSeries = await [{ name: 'รายได้', data: dataTable }]
          if (this.showMonthDropdown) {
            this.ChangeChartOptions(dayInMonth)
          } else if (this.showDatePicker) {
            this.ChangeChartOptions(dateToDate)
          } else {
            this.ChangeChartOptions(false)
          }
          if (response.query_result.sumaryDocument === '' || response.query_result.sumaryDocument === null || response.query_result.sumaryDocument === undefined) {
            // ในกรณีที่ ค่าจำนวนรายการสั่งซื้อทั้งหมด = ค่าว่าง, null, undefined
            this.totalOrder = 0 // จำนวนรายการสั่งซื้อทั้งหมด
          } else {
            this.totalOrder = response.query_result.sumaryDocument // จำนวนรายการสั่งซื้อทั้งหมด
          }
          if (response.query_result.sumaryRevenu === '' || response.query_result.sumaryRevenu === null || response.query_result.sumaryRevenu === undefined) {
            // ในกรณีที่ ค่ายอดขายทั้งหมด = ค่าว่าง, null, undefined
            this.totalSale = 0 // ยอดขายทั้งหมด
          } else {
            this.totalSale = response.query_result.sumaryRevenu // ยอดขายทั้งหมด
          }
          if (response.query_result.fee === '' || response.query_result.fee === null || response.query_result.fee === undefined) {
            // ในกรณีที่ ค่าธรรมเนียม = ค่าว่าง, null, undefined
            this.totalFee = 0 // ค่าธรรมเนียม
          } else {
            this.totalFee = response.query_result.fee // ค่าธรรมเนียม
          }
          if (response.query_result.net_amount === '' || response.query_result.net_amount === null || response.query_result.net_amount === undefined) {
            // ในกรณีที่ ยอดเงินสุทธิ = ค่าว่าง, null, undefined
            this.totalNetAmount = 0 // ยอดเงินสุทธิ
          } else {
            this.totalNetAmount = response.query_result.net_amount // ยอดเงินสุทธิ
          }
        } else {
          // if response.message === 'n'
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: response.message
          })
        }
      }
    },
    async getOrderList (startDate, endDate, shopID, dateFilter) {
      // ข้อมูลในตาราง
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionOrderList', data)
      var response = await this.$store.state.ModuleDashBoard.stateOrderList
      if (response.ok === 'y') {
        // console.log('actionOrderList', response.query_result.orderList)
        this.saleOrder = response.query_result.orderList
        var count = 1
        this.saleOrder.forEach((item) => {
          item.i = count
          count = count + 1
        })
        // this.saleOrder = datasale
        // console.log(this.saleOrder, 'tong')
      }
    },
    async getTopProducts (startDate, endDate, shopID, dateFilter) {
      // เซอร์วิสขายดี
      this.bestSeller = []
      this.orderValue = []
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionTopProducts', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopProducts
      if (response.ok === 'y') {
        // console.log('actionTopProducts', response)
        this.bestSeller = response.query_result.countProduct
        this.orderValue = response.query_result.sumRevenuProduct
      } else {
        this.bestSeller = []
        this.orderValue = []
      }
    },
    async getTopBuyers (startDate, endDate, shopID, dateFilter) {
      // มูลค่าการสั่งซื้อ
      this.topBuyers = []
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.$store.dispatch('actionTopBuyers', data)
      var response = await this.$store.state.ModuleDashBoard.stateTopBuyers
      if (response.ok === 'y') {
        // console.log('actionTopBuyers', response)
        this.topBuyers = response.query_result
      } else {
        this.topBuyers = []
      }
    },
    async getExportDashboard (startDate, endDate, shopID, dateFilter, exportDashboard, fileName) {
      // Export
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        exportdashboard: exportDashboard,
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'admin'
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/exportdashboard`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', fileName + '.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    closeDateSelect () {
      this.modalDateSelect = false
      this.dates = []
    },
    handleOptionChange () {
      // Reset selections when the option changes
      this.selectedYear = null
      this.selectedMonth = null
      this.selectedMonthValue = null
      this.selectedDates = []
    },
    async saveDates (val) {
      // save วัน เมื่อเลือกรายวัน
      if (this.dates.length === 1) {
        // กรณี เลือกหนึ่งวัน
        this.getRevenuGraphSummary(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getOrderList(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopProducts(this.dates, this.dates, this.shopID, this.dateFilter)
        this.getTopBuyers(this.dates, this.dates, this.shopID, this.dateFilter)
        this.startDate = this.dates
        this.endDate = this.dates
        this.modalDateSelect = false
      } else {
        // ถ้าเลือกหลายวัน จะเข้ามาตรงนี้เพื่อเรียงวันเริ่มต้นกับสิ้นสุดให้ถูกต้อง
        this.$refs.modalDateSelect.save(val)
        var Range = await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.dateRange = Range
        this.getRevenuGraphSummary(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getOrderList(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopProducts(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.getTopBuyers(this.dates[0], this.dates[1], this.shopID, this.dateFilter)
        this.startDate = this.dates[0]
        this.endDate = this.dates[1]
        this.modalDateSelect = false
      }
    },
    onDropdownSelected (option) {
      // ตอนเลือก รายวัน, รายเดือน, รายปี
      this.dates = []
      this.selectedDropdown = option
      if (option === 'รายปี' || option === 'รายเดือน') {
        this.showYearDropdown = true
        if (option === 'รายปี') {
          this.dateFilter = 'year'
          this.startDate = this.selectedYear
          this.endDate = this.selectedYear
          this.showMonthDropdown = false
          this.showDatePicker = false
          this.selectedDates = null
          this.saleOrder = []
          this.bestSeller = []
          this.orderValue = []
          this.topBuyers = []
          this.getRevenuGraphSummary(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getOrderList(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getTopProducts(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          this.getTopBuyers(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
          // this.onYearSelected(new Date().getFullYear())
        }
      } else {
        this.showYearDropdown = false
      }
      this.selectedYear = new Date().getFullYear()
      if (option === 'รายเดือน') {
        this.showMonthDropdown = true
        // this.selectedYear = null
      } else {
        this.showMonthDropdown = false
      }
      this.selectedMonth = null
      this.selectedMonthValue = null
      if (option === 'รายวัน') {
        this.showDatePicker = true
        this.dateFilter = 'day'
      } else {
        this.showDatePicker = false
      }
      this.selectedDates = null
    },
    onMonthSelected (month) {
      // เมื่อเลือกรายเดือน
      this.dateFilter = 'month'
      this.selectedMonth = month.text
      this.selectedMonthValue = `${this.selectedYear}-${month.value}`
      this.startDate = this.selectedMonthValue
      this.endDate = this.selectedMonthValue
      // console.log(this.selectedMonthValue, 'selecteddd')
      this.getRevenuGraphSummary(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getOrderList(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopProducts(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      this.getTopBuyers(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
    },
    onYearSelected (year) {
      // เมื่อเลือกรายปี
      if (this.dateFilter === 'year') {
        this.dateFilter = 'year'
        this.selectedYear = year
        this.startDate = this.selectedYear
        this.endDate = this.selectedYear
        // console.log(this.selectedYear, 'selecteddd')
        this.getRevenuGraphSummary(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getOrderList(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getTopProducts(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
        this.getTopBuyers(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter)
      } else if (this.dateFilter === 'month') {
        this.selectedYear = year
        var changeYear = this.selectedMonthValue.split('-')
        this.selectedMonthValue = `${year}-${changeYear[1]}`
        this.getRevenuGraphSummary(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getOrderList(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getTopProducts(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
        this.getTopBuyers(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter)
      }
    },
    onChartSelected (choice) {
      if (choice === 'revenue') {
        this.chartSelected = 'revenue'
        this.RevenueEnabled = true
        this.DocumentEnabled = false
        // console.log('Revenue', this.RevenueEnabled)
        // console.log('Document', this.DocumentEnabled)
        // console.log('chartSelected', this.chartSelected)
      } else if (choice === 'document') {
        this.chartSelected = 'document'
        this.DocumentEnabled = true
        this.RevenueEnabled = false
        // console.log('Revenue', this.RevenueEnabled)
        // console.log('Document', this.DocumentEnabled)
        // console.log('chartSelected', this.chartSelected)
      }
    },
    async getQT () {
      // console.log('QT')
      // actionsGetQTDashBoard
      var item = {
        seller_shop_id: this.sellerID
      }
      await this.$store.dispatch('actionsGetQTDashBoard', item)
      var response = await this.$store.state.ModuleShop.stateGetQTDashBoard
      // console.log('response getQT', response)
      if (response.ok === 'y') {
        // console.log('QT', response.query_result[0][0])
        // console.log('SO', response.query_result[0][1])
        // console.log('PO', response.query_result[0][2])
        // console.log('PR', response.query_result[0][3])
        for (var i = 0; i < response.query_result[0].length; i++) {
          if (response.query_result[0][i].type === 'QT') {
            this.docQT = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docRevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          } else if (response.query_result[0][i].type === 'SO') {
            this.docSO = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docSORevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          } else if (response.query_result[0][i].type === 'PO') {
            this.docPO = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docPORevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          } else if (response.query_result[0][i].type === 'PR') {
            this.docPR = response.query_result[0][i].sumDocumentQT === null ? 0 : response.query_result[0][i].sumDocumentQT
            this.docPRRevenue = response.query_result[0][i].sumRevenueQT === null ? 0 : response.query_result[0][i].sumRevenueQT
          }
        }
        // this.listQT = response.query_result[0]
        // for (const item of response.query_result[0]) {
        //   console.log('item------->', item)
        //   // this.docQT = item.sumDocumentQT
        //   // this.docRevenue = item.sumRevenueQT
        // }
        // this.docQT = response.query_result[0].sumDocumentQT
        // this.docRevenue = response.query_result[0].sumRevenueQT
      } else {
        this.docQT = 0
        this.docRevenue = 0
        this.docSO = 0
        this.docSORevenue = 0
        this.docPO = 0
        this.docPORevenue = 0
        this.docPR = 0
        this.docPRRevenue = 0
      }
    },
    async getSumTrend (val) {
      this.dataDate = []
      this.dataRevenue = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerID,
          type: val
        }
      } else {
        data = {
          seller_shop_id: this.sellerID,
          type: val
        }
      }
      await this.$store.dispatch('actionsGetSumTrendDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumTrendDashBoard
      this.setOfData = await response.query_result.data
      if (this.setOfData.length !== 0) {
        for (const item of this.setOfData) {
          this.dataDate.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.Revenue === undefined || item.Revenue === null) {
            item.Revenue = 0
            this.dataRevenue.push(item.Revenue)
          } else {
            this.dataRevenue.push(item.Revenue)
            // console.log(this.dataRevenue)
          }
          // console.log('item------->', this.dataDate, this.dataRevenue)
        }
      } else {
        this.dataDate.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenue = []
      }
    },
    async getSumDocQT (val) {
      // this.isLoad = true
      var setOfData = []
      this.dataDateBar = []
      this.dataRevenueBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocQTDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocQTDashBoard
      // console.log('response QT------->', response.query_result.data)
      setOfData = await response.query_result.data
      if (setOfData.length !== 0) {
        for (const item of setOfData) {
          this.dataDateBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          // var example = [1, 2, 3, 4]
          // this.dataRevenueBar = example
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenueBar.push(item.numDoc)
          } else {
            this.dataRevenueBar.push(item.numDoc)
          }
          // console.log('item QT------->' + val, this.dataDateBar, this.dataRevenueBar)
        }
        // this.isLoad = false
      } else {
        this.dataDateBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenueBar = []
        // this.isLoad = false
      }
    },
    async getSumDocSO (val) {
      var setOfDataSO = []
      this.dataDateSOBar = []
      this.dataRevenueSOBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocSODashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocSODashBoard
      setOfDataSO = await response.query_result.data
      // console.log('setOfData SO------->', setOfDataSO)
      if (setOfDataSO.length !== 0) {
        for (const item of setOfDataSO) {
          this.dataDateSOBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          // var example = [4, 2, 10, 1]
          // this.dataRevenueSOBar = example
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenueSOBar.push(item.numDoc)
          } else {
            this.dataRevenueSOBar.push(item.numDoc)
          }
          // console.log('item SO------->' + val, this.dataDateSOBar, this.dataRevenueSOBar)
        }
      } else {
        this.dataDateSOBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenueSOBar = []
      }
    },
    async getSumDocPR (val) {
      var setOfDataPR = []
      this.dataDatePRBar = []
      this.dataRevenuePRBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocPRDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocPRDashBoard
      setOfDataPR = await response.query_result.data
      // console.log('setOfData PR------->', setOfDataPR)
      if (setOfDataPR.length !== 0) {
        for (const item of setOfDataPR) {
          this.dataDatePRBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenuePRBar.push(item.numDoc)
          } else {
            this.dataRevenuePRBar.push(item.numDoc)
          }
          // console.log('item PR------->' + val, this.dataDatePRBar, this.dataRevenuePRBar)
        }
      } else {
        this.dataDatePRBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenuePRBar = []
      }
    },
    async getSumDocPO (val) {
      var setOfDataPO = []
      this.dataDatePOBar = []
      this.dataRevenuePOBar = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      } else {
        data = {
          seller_shop_id: this.sellerID,
          type: val,
          customer: 'all'
        }
      }
      await this.$store.dispatch('actionsGetSumDocPODashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumDocPODashBoard
      // console.log('response PO------->', response.query_result.data)
      setOfDataPO = await response.query_result.data
      if (setOfDataPO.length !== 0) {
        for (const item of setOfDataPO) {
        // console.log('item------->', item)
          this.dataDatePOBar.push(new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          if (item.numDoc === undefined) {
            item.numDoc = 0
            this.dataRevenuePOBar.push(item.numDoc)
          } else {
            this.dataRevenuePOBar.push(item.numDoc)
          }
          // console.log('item------->' + val, this.dataDatePOBar, this.dataRevenuePOBar)
        }
      } else {
        this.dataDatePOBar.push(new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        this.dataRevenuePOBar = []
      }
    },
    setDate () {
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'Asia/Bangkok'
      }
      var date = new Date().toLocaleDateString(undefined, options)
      this.currentDate = date
      // var date = new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
      // this.currentDate = date.getDate() + '/' + (date.getMonth() + 1) + '/' + date.getFullYear()
      // console.log(this.currentDate)
      // console.log(date)
    },
    setMonth () {
      var date = new Date()
      var month = date.toLocaleString('default', { month: 'long' })
      this.currentMonth = month
      // console.log(this.currentMonth)
    },
    docLine (actionKey) {
      if (actionKey === 'day') {
        this.docOfLine = 'day'
        this.setDate()
      } else if (actionKey === 'month') {
        this.docOfLine = 'month'
      } else if (actionKey === 'quarter') {
        this.docOfLine = 'quarter'
      } else if (actionKey === 'halfyear') {
        this.docOfLine = 'halfyear'
      } else if (actionKey === 'year') {
        this.docOfLine = 'year'
      }
    },
    docBar (actionKey) {
      if (actionKey === 'day') {
        this.docOfBar = 'day'
        this.setDate()
        // console.log(this.docOfBar)
      } else if (actionKey === 'month') {
        this.docOfBar = 'month'
      } else if (actionKey === 'quarter') {
        this.docOfBar = 'quarter'
      } else if (actionKey === 'halfyear') {
        this.docOfBar = 'halfyear'
      } else if (actionKey === 'year') {
        this.docOfBar = 'year'
      }
    },
    docPie (actionKey) {
      if (actionKey === 'day') {
        this.docOfPie = 'day'
      } else if (actionKey === 'month') {
        this.docOfPie = 'month'
      } else if (actionKey === 'quarter') {
        this.docOfPie = 'quarter'
      } else if (actionKey === 'halfyear') {
        this.docOfPie = 'halfyear'
      } else if (actionKey === 'year') {
        this.docOfPie = 'year'
      }
    },
    async getSumTopFiveQT (val) {
      var setOfData = []
      this.dataNamePie = []
      this.dataRevenuePie = []
      var data = ''
      if (val === undefined) {
        val = 'month'
        data = {
          seller_shop_id: this.sellerID,
          type: val
        }
      } else {
        data = {
          seller_shop_id: this.sellerID,
          type: val
        }
      }
      await this.$store.dispatch('actionsGetSumTopFiveQTDashBoard', data)
      var response = await this.$store.state.ModuleShop.stateGetSumTopFiveQTDashBoard
      setOfData = await response.query_result.data
      this.listTopFive = setOfData
      // console.log('listTopFive', this.listTopFive)
      if (setOfData.length !== 0) {
        for (const item of setOfData) {
          // this.nameTopFive = item.name
          // this.qtTopFive = item.numDoc
          // this.revenueTopFive = item.Revenue
          // console.log('item------->', this.nameTopFive, this.qtTopFive, this.revenueTopFive)
          this.dataRevenuePie.push(item.Revenue)
          this.dataNamePie.push(item.name)
          // console.log('dataPie', this.dataRevenuePie, this.dataNamePie)
        }
      } else {
        this.dataRevenuePie = []
        // console.log('dataRevenuePie = 0', this.dataRevenuePie)
      }
    },
    formatNumberTooltip (value) {
      if (typeof value !== 'undefined') {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      return ''
    },
    formatNumber (number) {
      const formattedNumber = new Intl.NumberFormat('en-US', {
        notation: 'compact',
        compactDisplay: 'short'
      }).format(number)
      return formattedNumber
    },
    formatter (value) {
      // console.log('value', value)
      if (value > 0) {
        if (value === Infinity) {
          return '1'
        } else {
          return Math.round(value)
        }
      } else {
        return '0'
      }
    }
  }
}
</script>

<style scoped>
.text-container {
  overflow: visible;  /* or 'auto' or 'scroll' depending on your requirements */
  white-space: nowrap;  /* prevent text from wrapping */
}
.two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
}
.one-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.custom-text-field {
  height: 37px;
  width: 280px;
}
.custom-chip {
  overflow: visible !important;
}
.no-word-break {
  word-break: normal;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.backgroundSellerDashboard {
  max-width: 100% !important;
  background: #F7FCFC;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C
}
.exportButtonText {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF
}
.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
.active-btn-selected {
  background-color: white !important;
  color: #27ab9c;
  border-radius: 20px;
  border: none;
  box-shadow: none;
  margin: 5px 10px;
}
.active-btn-default {
  background-color: #f3f5f7 !important;
  color: #8dd4cc;
  border-radius: 20px;
  border: none;
  box-shadow: none;
}
.btn-box {
  background-color: #f3f5f7;
  border-radius: 30px;
  width: 100%;
  padding: 5px;
}
.v-chip-group .v-chip {
  margin-right: -4px;
}
</style>

<style>
.card {
  max-width: none;
}
</style>
<style scoped>
.v-btn__content {
    font-size: 16px;
}
.QT .card {
  border-radius: 5px !important;
  background-color: #f5f5f5;
}
.custom-chip.v-chip--active {
  background-color: #757575 !important; /* สีเทาเข้ม */
  color: #ffffff !important; /* สีขาว */
}
</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(12) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          // background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(12) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
