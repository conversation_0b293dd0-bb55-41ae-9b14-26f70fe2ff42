<template>
  <!-- <div>1234</div> -->
  <v-container style="min-height: 650px" :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card
      width="100%"
      height="100%"
      elevation="0"
      :class="MobileSize ? 'px-2' : ''"
    >
      <v-row dense class="mb-4">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-5' : 'px-0'">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title
              style="
                font-weight: bold;
                font-size: 24px;
                line-height: 32px;
                color: #333333;
              "
              >จัดการกลุ่มลูกค้า</v-card-title
            >
          </v-row>
          <v-row v-else>
            <v-card-title
              class="pl-2"
              style="
                font-weight: bold;
                font-size: 18px;
                line-height: 32px;
                color: #333333;
              "
              ><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()"
                >mdi-chevron-left</v-icon
              >จัดการกลุ่มลูกค้า</v-card-title
            >
          </v-row>
        </v-col>
        <v-col cols="12" class="pb-10" :class="MobileSize ? 'px-2' : 'px-3'">
          <v-row dense class="px-0">
            <v-col
              cols="12"
              :class="MobileSize || IpadProSize || IpadSize ? 'pb-0' : ''"
            >
              <a-tabs @change="getListTierSales" class="px-2 py-0">
                <a-tab-pane :key="0"
                  ><span slot="tab" style="font-size: 18px"
                    >ทั้งหมด
                    <v-chip
                      small
                      text-color="#27AB9C"
                      color="rgba(39, 171, 156, 0.10)"
                      >{{ tierList.count_all }}</v-chip
                    ></span
                  ></a-tab-pane
                >
                <a-tab-pane :key="1"
                  ><span slot="tab" style="font-size: 18px"
                    >กำลังใช้งาน
                    <v-chip small text-color="#52C41A" color="#F0FEE8">{{
                      tierList.count_active
                    }}</v-chip></span
                  ></a-tab-pane
                >
                <a-tab-pane :key="2"
                  ><span slot="tab" style="font-size: 18px"
                    >ยกเลิก
                    <v-chip
                      small
                      text-color="#F5222D"
                      color="rgba(245, 34, 45, 0.10)"
                      >{{ tierList.count_inactive }}</v-chip
                    ></span
                  ></a-tab-pane
                >
                <!-- <template v-slot:tabBarExtraContent>
                  <v-switch v-if="!MobileSize && !IpadSize" :ripple="false" class="float-left hide-background-hover" :class="MobileSize ? 'pb-5' : IpadProSize || IpadSize ? 'pb-4' : 'pb-4'" v-model="tierList.on_off" value="yes" :label="tierList.on_off === 'yes' ? 'เปิดการขอเป็นคู่ค้า' : 'ปิดการขอเป็นคู่ค้า'" color="#52C41A" style="font-size: 14px !important;" inset hide-details @click="turnOnOffRequessPartner()"></v-switch>
                </template> -->
              </a-tabs>
            </v-col>
          </v-row>
          <v-row dense class="px-0 pb-6 pt-2">
            <v-col cols="12" md="4" sm="12" class="pt-3">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  align-items: center;
                  color: #333333;
                  font-weight: 600;
                "
                v-if="StateStatus == 0"
                >รายการกลุ่มลูกค้าทั้งหมด {{ showCountOrder }} รายการ</span
              >
              <span
                v-if="StateStatus == 1"
                style="
                  font-size: 16px;
                  line-height: 24px;
                  align-items: center;
                  color: #333333;
                  font-weight: 600;
                "
                >รายการกลุ่มลูกค้ากำลังใช้งานทั้งหมด
                {{ showCountOrder }} รายการ</span
              >
              <span
                v-if="StateStatus == 2"
                style="
                  font-size: 16px;
                  line-height: 24px;
                  align-items: center;
                  color: #333333;
                  font-weight: 600;
                "
                >รายการกลุ่มลูกค้ายกเลิกทั้งหมด
                {{ showCountOrder }} รายการ</span
              >
            </v-col>
            <v-col cols="12" md="4" sm="12">
              <v-text-field
                v-model="search"
                style="border-radius: 8px"
                append-icon="mdi-magnify"
                placeholder="ค้นหาจากชื่อกลุ่มลูกค้า"
                outlined
                dense
                hide-details
              ></v-text-field>
            </v-col>
            <v-col
              cols="12"
              sm="4"
              align="end"
              class="mt-1"
              v-if="!MobileSize && !IpadSize"
            >
              <v-btn
                :block="MobileSize"
                rounded
                class="pt-0"
                width="153"
                height="40"
                :class="MobileSize ? 'mt-0' : 'mt-0 '"
                color="#27AB9C"
                dark
                @click="addTier()"
                ><v-icon left>mdi-plus</v-icon>เพิ่มกลุ่มลูกค้า</v-btn
              >
            </v-col>
            <!-- <v-col v-if="MobileSize || IpadSize" :cols="MobileSize || IpadProSize || IpadSize ? 12 : 6" align="end" class="mb-0 pt-0" :class="IpadProSize || IpadSize ? 'pt-2' : 'px-2'">
              <v-switch v-if="MobileSize || IpadSize" :ripple="false" class="float-left hide-background-hover mt-2 pt-0" :class="MobileSize ? 'pb-5' : IpadProSize || IpadSize ? '' : 'ml-14'" v-model="tierList.on_off" value="yes" label="เปิด-ปิดการขอเป็นคู่ค้า" color="#52C41A" style="font-size: 14px !important;" inset hide-details @click="turnOnOffRequessPartner()"></v-switch>
              <v-btn :block="MobileSize" rounded width="153" height="40" class="pt-0" :class="MobileSize ? 'mt-6': 'mt-0 '" color="#27AB9C" dark @click="addTier()"><v-icon left>mdi-plus</v-icon>เพิ่มกลุ่มคู่ค้า</v-btn>
            </v-col> -->
          </v-row>
          <v-data-table
            v-if="dataTable.length !== 0"
            @pagination="countSales"
            :page.sync="page"
            :headers="headers"
            :items="dataTable"
            :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
            :search="search"
            item-key="referance_id"
            color="blue"
            class="elevation-1 px-0"
            no-results-text="ไม่พบกลุ่มคู่ค้าที่ค้นหา"
          >
            <template v-slot:[`item.tier_name`]="{ item }">
              {{ item.tier_name }}
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              {{
                new Date(item.created_at).toLocaleDateString('th-TH', {
                  timeZone: 'UTC',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })
              }}
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'active'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759"
                  >กำลังใช้งาน</v-chip
                >
              </span>
              <span v-else-if="item.status === 'inactive'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B"
                  >ยกเลิก</v-chip
                >
              </span>
            </template>
            <template v-slot:[`item.manage`]="{ item }">
              <v-row
                no-gutters
                justify="space-around"
                align="center"
                class="px-4"
              >
                <v-card class="" title="รายละเอียด">
                  <v-btn text color="#27AB9C" small @click="openDetail(item)">
                    <v-icon class="pr-1" color="27AB9C"
                      >mdi-file-document-outline</v-icon
                    >
                  </v-btn>
                </v-card>
                <v-card class="" title="แก้ไข">
                  <v-btn
                    text
                    color="#27AB9C"
                    small
                    @click="openDetailEdit(item)"
                  >
                    <v-icon class="" color="27AB9C">mdi-pencil-outline</v-icon>
                  </v-btn>
                </v-card>
                <v-card class="" title="ลบ">
                  <v-btn
                    text
                    x-small
                    color="#F5222D"
                    small
                    @click="deleteTier('delete', item)"
                  >
                    <v-icon color="#F5222D">mdi-delete-outline</v-icon>
                  </v-btn>
                </v-card>
              </v-row>
            </template>
          </v-data-table>
          <v-row
            justify="center"
            align-content="center"
            v-if="this.disableTable === true"
          >
            <v-col cols="12" align="center">
              <div class="my-5">
                <v-img
                  src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png"
                  max-height="500px"
                  max-width="500px"
                  height="100%"
                  width="100%"
                  contain
                  aspect-ratio="2"
                ></v-img>
              </div>
              <h2
                v-if="StateStatus === 0"
                style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c"
              >
                <b>ยังไม่มีรายการจัดการกลุ่มคู่ค้า</b>
              </h2>
              <h2
                v-if="StateStatus === 1"
                style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c"
              >
                <b
                  >ยังไม่มีรายการตั้งค่ากลุ่มคู่ค้าที่{{
                    StateStatus === 1 ? 'กำลังใช้งาน' : 'ยกเลิก'
                  }}</b
                >
              </h2>
              <h2
                v-if="StateStatus === 2"
                style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c"
              >
                <b>ยังไม่มีรายการยกเลิกกลุ่มคู่ค้า</b>
              </h2>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <!-- เพิ่มกลุ่มลูกค้า -->
    <v-dialog
      v-model="createTire"
      :width="MobileSize ? '100%' : IpadSize ? '100%' : '555'"
      persistent
    >
      <v-card
        elevation="0"
        style="background: #ffffff; border-radius: 24px; overflow-x: hidden"
      >
        <v-form ref="FormSettingTier" :lazy-validation="lazy">
          <v-card-text class="px-0 pt-0">
            <div
              :style="
                MobileSize
                  ? 'width: 100%'
                  : IpadSize
                  ? 'width: 100%'
                  : 'width: 555px'
              "
              class="backgroundHead"
              style="position: absolute; height: 120px"
            >
              <v-row style="height: 120px">
                <v-col style="text-align: center" class="pt-4">
                  <span
                    :class="
                      MobileSize
                        ? 'title-mobile white--text'
                        : 'title white--text'
                    "
                    ><b>เพิ่มข้อมูลกลุ่มคู่ค้า</b></span
                  >
                </v-col>
                <v-btn fab small @click="cancel('create')" icon class="mt-3"
                  ><v-icon color="white">mdi-close</v-icon></v-btn
                >
              </v-row>
            </div>
            <div
              style="
                position: relative;
                padding: 0px 12px 0px;
                display: flex;
                padding-top: 60px;
              "
            >
              <v-row
                :width="MobileSize ? '100%' : '782px'"
                style="
                  height: 50px;
                  border-radius: 24px 24px 0px 0px;
                  background: #ffffff;
                "
              >
                <v-col style="text-align: center"> </v-col>
              </v-row>
            </div>
            <div class="" style="position: relative">
              <v-card
                elevation="0"
                width="100%"
                height="100%"
                style="background: #ffffff; border-radius: 20px 20px 0px 0px"
                :style="
                  MobileSize
                    ? 'padding: 20px 20px 10px 20px;'
                    : 'padding: 40px 48px 10px 48px;'
                "
              >
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img
                          src="@/assets/Create_Store/partnerShopDetail.png"
                          max-height="62"
                          max-width="62"
                        ></v-img>
                        <span
                          class="pt-5 pl-4"
                          style="
                            font-weight: 600;
                            color: #333333;
                            font-size: 16px;
                          "
                        >
                          ข้อมูลกลุ่มลูกค้าของร้านค้า
                        </span>
                      </v-row>
                      <!-- เก็บไว้ใช้แก้ไข -->
                      <!-- <span class="ml-auto pt-3" style="text-align: end;">
                        <v-row dense>
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะการใช้งาน :</span>
                          <v-switch color="#52C41A" class="mt-0 px-1 pt-2" inset v-model="changeStatus" @click="updateStatusSettingTier()"></v-switch>
                          <span class="mt-1" v-if="data.status === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                          </span>
                          <span class="mt-1" v-else>
                            <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                          </span>
                        </v-row>
                      </span> -->
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="12" sm="7" class="pa-0">
                      <span
                        >ชื่อกลุ่มลูกค้า <span style="color: red">*</span></span
                      >
                      <v-text-field
                        style="border-radius: 8px"
                        class="input_text namedoc_input"
                        placeholder="ระบุชื่อ"
                        outlined
                        dense
                        v-model="tierName"
                        :rules="Rules.empty"
                        @keypress="CheckSpacebar($event)"
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col cols="12" md="5" sm="5" class="pa-0"></v-col> -->
                    <v-col cols="12" md="11" sm="5" class="pa-0">
                      <span
                        >ส่วนลดตามระดับกลุ่มลูกค้า
                        <span style="color: red">*</span></span
                      >
                      <v-row
                        no-gutters
                        v-for="(item, index) in dataOfTierList"
                        :key="index"
                      >
                        <v-radio-group
                          v-model="item.discount_type"
                          :rules="Rules.emptyCheckbox"
                        >
                          <v-row dense align="center">
                            <v-radio
                              class="pa-0"
                              label="ลดเป็น %"
                              value="percent"
                            ></v-radio>
                            <v-radio
                              class="pb-2 pl-3"
                              label="ลดเป็นบาท"
                              value="bath"
                            ></v-radio>
                          </v-row>
                        </v-radio-group>
                        <!-- <span>Level {{ index }}</span> -->
                        <v-row no-gutters>
                          <v-text-field
                            v-if="item.discount_type === ''"
                            v-model="item.discount"
                            style="border-radius: 8px"
                            type="number"
                            class="input_text namedoc_input"
                            placeholder="ระบุส่วนลด"
                            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
                            outlined
                            dense
                            suffix="% / บาท"
                          >
                          </v-text-field>
                          <v-text-field
                            v-if="item.discount_type === 'percent'"
                            v-model="item.discount"
                            style="border-radius: 8px"
                            type="number"
                            class="input_text namedoc_input"
                            placeholder="ระบุส่วนลด"
                            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
                            outlined
                            dense
                            suffix="%"
                            :rules="Rules.percentPattern"
                          >
                          </v-text-field>
                          <v-text-field
                            v-if="item.discount_type === 'bath'"
                            v-model="item.discount"
                            style="border-radius: 8px"
                            type="number"
                            class="input_text namedoc_input"
                            placeholder="ระบุส่วนลด"
                            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
                            outlined
                            dense
                            suffix="บาท"
                            :rules="Rules.bathPattern"
                          >
                          </v-text-field>
                          <v-btn
                            v-if="dataOfTierList.length !== 1"
                            class="mx-2 mt-1"
                            outlined
                            fab
                            x-small
                            style="
                              background: #ff515a;
                              border: 1px solid #f2f2f2;
                              box-sizing: border-box;
                              border-radius: 999px;
                            "
                            @click="deleteTextField(index, 'create')"
                            ><v-icon color="white">mdi-minus</v-icon></v-btn
                          >
                          <v-btn
                            v-if="index !== 0 || dataOfTierList.length === 1"
                            class="mx-2 mt-1"
                            outlined
                            fab
                            x-small
                            style="
                              background: #27ab9c;
                              border: 1px solid #f2f2f2;
                              box-sizing: border-box;
                              border-radius: 999px;
                            "
                            @click="addTextField('create')"
                            :disabled="dataOfTierList.length > 20"
                            ><v-icon color="white">mdi-plus</v-icon></v-btn
                          >
                        </v-row>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-row
              dense
              style="height: 88px; background: #f5fcfb"
              :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'"
            >
              <v-btn
                rounded
                width="125"
                height="40"
                class="my-auto"
                outlined
                color="#27AB9C"
                @click="cancel('create')"
                >ยกเลิก</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                rounded
                width="125"
                height="40"
                class="white--text my-auto"
                color="#27AB9C"
                @click="openAwaitModal('create')"
                >บันทึก</v-btn
              >
            </v-row>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- Await Tier -->
    <v-dialog v-model="dialogAwaitTier" width="424" persistent>
      <v-card style="background: #ffffff; border-radius: 24px">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon @click="closeDialogAwait()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center">
            <p
              style="
                font-weight: 700;
                font-size: 24px;
                line-height: 24px;
                color: #333333;
              "
              class="my-4"
            >
              <b>{{
                actions === 'edit'
                  ? 'แก้ไขข้อมูลกลุ่มคู่ค้า'
                  : actions === 'delete'
                  ? 'ลบข้อมูลกลุ่มคู่ค้า'
                  : 'สร้างข้อมูลกลุ่มคู่ค้า'
              }}</b>
            </p>
            <span
              style="
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                color: #9a9a9a;
              "
              >คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span
            >
          </v-card-text>
          <v-card-text class="px-0">
            <v-row dense justify="center">
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                outlined
                rounded
                color="#27AB9C"
                class="mr-4"
                @click="closeDialogAwait()"
                >ยกเลิก</v-btn
              >
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                class="white--text"
                rounded
                color="#27AB9C"
                @click="sendSettingTier(actions)"
                >ตกลง</v-btn
              >
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- รายละเอียด -->
    <v-dialog
      v-model="detailTier"
      :width="MobileSize ? '100%' : IpadSize ? '100%' : '782'"
      persistent
    >
      <v-card
        elevation="0"
        style="background: #ffffff; border-radius: 24px; overflow-x: hidden"
      >
        <v-form ref="FormSettingTier" :lazy-validation="lazy">
          <v-card-text class="px-0 pt-0">
            <div
              :style="
                MobileSize
                  ? 'width: 100%'
                  : IpadSize
                  ? 'width: 100%'
                  : 'width: 782px'
              "
              class="backgroundHead"
              style="position: absolute; height: 120px"
            >
              <v-row style="height: 120px">
                <v-col style="text-align: center" class="pt-4">
                  <span
                    :class="
                      MobileSize
                        ? 'title-mobile white--text'
                        : 'title white--text'
                    "
                    ><b>ข้อมูลกลุ่มคู่ค้า</b></span
                  >
                </v-col>
                <v-btn fab small @click="cancel('detail')" icon class="mt-3"
                  ><v-icon color="white">mdi-close</v-icon></v-btn
                >
              </v-row>
            </div>
            <div
              style="
                position: relative;
                padding: 0px 12px 0px;
                display: flex;
                padding-top: 60px;
              "
            >
              <v-row
                :width="MobileSize ? '100%' : '782px'"
                style="
                  height: 50px;
                  border-radius: 24px 24px 0px 0px;
                  background: #ffffff;
                "
              >
                <v-col style="text-align: center"> </v-col>
              </v-row>
            </div>
            <div class="" style="position: relative">
              <v-card
                elevation="0"
                width="100%"
                height="100%"
                style="background: #ffffff; border-radius: 20px 20px 0px 0px"
                :style="
                  MobileSize
                    ? 'padding: 20px 20px 10px 20px;'
                    : 'padding: 40px 48px 10px 48px;'
                "
              >
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img
                          src="@/assets/Create_Store/partnerShopDetail.png"
                          max-height="62"
                          max-width="62"
                        ></v-img>
                        <span
                          class="pt-5 pl-4"
                          style="
                            font-weight: 600;
                            color: #333333;
                            font-size: 16px;
                          "
                        >
                          ข้อมูลกลุ่มลูกค้าของร้านค้า
                        </span>
                      </v-row>
                      <!-- เก็บไว้ใช้แก้ไข -->
                      <span class="ml-auto pt-3" style="text-align: end">
                        <v-row dense>
                          <span
                            class="mt-2 pr-2"
                            style="
                              font-weight: 400;
                              color: #333333;
                              font-size: 16px;
                            "
                            >สถานะการใช้งาน :</span
                          >
                          <v-switch
                            color="#52C41A"
                            class="mt-0 px-1 pt-2"
                            inset
                            v-model="changeStatus"
                            @click="updateStatusSettingTier()"
                            disabled
                          ></v-switch>
                          <span class="mt-1" v-if="statusDetail === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A"
                              >กำลังใช้งาน</v-chip
                            >
                          </span>
                          <span class="mt-1" v-if="statusDetail === 'inactive'">
                            <v-chip color="#FEE7E8" text-color="#F5222D"
                              >ยกเลิกใช้งาน</v-chip
                            >
                          </span>
                        </v-row>
                      </span>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="4" md="2" sm="2">
                          <span
                            class=""
                            :style="MobileSize ? 'font-size: 14px;' : ''"
                            >ชื่อกลุ่มคู่ค้า :</span
                          >
                        </v-col>
                        <v-col cols="8" md="10" sm="10">
                          <span class=""
                            ><b class="detailTierText">{{
                              tierNameDetail
                            }}</b></span
                          >
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="4" md="2" sm="2">
                          <span
                            class=""
                            :style="MobileSize ? 'font-size: 14px;' : ''"
                            >ส่วนลด :</span
                          >
                        </v-col>
                        <v-col cols="8" md="10" sm="10">
                          <!-- <pre>{{tierListDetail}}</pre> -->
                          <div
                            v-for="(item, index) in tierListDetail"
                            :key="index"
                          >
                            <p
                              class="my-0"
                              v-if="item.discount_type === 'percent'"
                            >
                              <b class="detailTierText"
                                >ระดับที่ : {{ item.tier_level }} (
                                ลดเป็นเปอร์เซ็นต์ )</b
                              >
                            </p>
                            <p
                              class="my-0"
                              v-if="item.discount_type === 'bath'"
                            >
                              <b class="detailTierText"
                                >ระดับที่ : {{ item.tier_level }} ( ลดเป็นบาท
                                )</b
                              >
                            </p>
                            <p
                              class="my-0 pb-2"
                              v-if="item.discount_type === 'bath'"
                            >
                              <b class="detailTierText"
                                >ลด : {{ item.discount_bath }} บาท</b
                              >
                            </p>
                            <p
                              class="my-0 pb-2"
                              v-if="item.discount_type === 'percent'"
                            >
                              <b class="detailTierText"
                                >ลด : {{ item.discount_percent }} เปอร์เซ็น</b
                              >
                            </p>
                            <v-divider></v-divider>
                          </div>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- แก้ไข -->
    <v-dialog
      v-model="editTire"
      :width="MobileSize ? '100%' : IpadSize ? '100%' : '555'"
      persistent
    >
      <v-card
        elevation="0"
        style="background: #ffffff; border-radius: 24px; overflow-x: hidden"
      >
        <v-form ref="FormSettingTier" :lazy-validation="lazy">
          <v-card-text class="px-0 pt-0">
            <div
              :style="
                MobileSize
                  ? 'width: 100%'
                  : IpadSize
                  ? 'width: 100%'
                  : 'width: 555px'
              "
              class="backgroundHead"
              style="position: absolute; height: 120px"
            >
              <v-row style="height: 120px">
                <v-col style="text-align: center" class="pt-4">
                  <span
                    :class="
                      MobileSize
                        ? 'title-mobile white--text'
                        : 'title white--text'
                    "
                    ><b>แก้ไขข้อมูลกลุ่มคู่ค้า</b></span
                  >
                </v-col>
                <v-btn fab small @click="cancel('edit')" icon class="mt-3"
                  ><v-icon color="white">mdi-close</v-icon></v-btn
                >
              </v-row>
            </div>
            <div
              style="
                position: relative;
                padding: 0px 12px 0px;
                display: flex;
                padding-top: 60px;
              "
            >
              <v-row
                :width="MobileSize ? '100%' : '555px'"
                style="
                  height: 50px;
                  border-radius: 24px 24px 0px 0px;
                  background: #ffffff;
                "
              >
                <v-col style="text-align: center"> </v-col>
              </v-row>
            </div>
            <div class="" style="position: relative">
              <v-card
                elevation="0"
                width="100%"
                height="100%"
                style="background: #ffffff; border-radius: 20px 20px 0px 0px"
                :style="
                  MobileSize
                    ? 'padding: 20px 20px 10px 20px;'
                    : 'padding: 40px 48px 10px 48px;'
                "
              >
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img
                          src="@/assets/Create_Store/partnerShopDetail.png"
                          max-height="62"
                          max-width="62"
                        ></v-img>
                        <span
                          class="pt-5 pl-4"
                          style="
                            font-weight: 600;
                            color: #333333;
                            font-size: 16px;
                          "
                        >
                          ข้อมูลกลุ่มลูกค้าของร้านค้า
                        </span>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="12" sm="7" class="pa-0">
                      <span class="ml-auto pt-3" style="text-align: end">
                        <v-row dense>
                          <span
                            class="mt-2 pr-2"
                            style="
                              font-weight: 400;
                              color: #333333;
                              font-size: 16px;
                            "
                            >สถานะการใช้งาน :</span
                          >
                          <v-switch
                            color="#52C41A"
                            class="mt-0 px-1 pt-2"
                            inset
                            v-model="changeStatus"
                          ></v-switch>
                          <span class="mt-1" v-if="statusDetail === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A"
                              >กำลังใช้งาน</v-chip
                            >
                          </span>
                          <span class="mt-1" v-if="statusDetail === 'inactive'">
                            <v-chip color="#FEE7E8" text-color="#F5222D"
                              >ยกเลิกใช้งาน</v-chip
                            >
                          </span>
                        </v-row>
                      </span>
                      <span
                        >ชื่อกลุ่มลูกค้า <span style="color: red">*</span></span
                      >
                      <v-text-field
                        style="border-radius: 8px"
                        class="input_text namedoc_input"
                        placeholder="ระบุชื่อ"
                        outlined
                        dense
                        v-model="tierName"
                        :rules="Rules.empty"
                        @keypress="CheckSpacebar($event)"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="11" sm="5" class="pa-0">
                      <span
                        >ส่วนลดตามระดับกลุ่มลูกค้า
                        <span style="color: red">*</span></span
                      >
                      <v-row
                        no-gutters
                        v-for="(item, index) in tierListDetail"
                        :key="index"
                      >
                        <v-radio-group v-model="item.discount_type">
                          <v-row dense align="center">
                            <v-radio
                              class="pa-0"
                              label="ลดเป็น %"
                              value="percent"
                            ></v-radio>
                            <v-radio
                              class="pb-2 pl-3"
                              label="ลดเป็นบาท"
                              value="bath"
                            ></v-radio>
                          </v-row>
                        </v-radio-group>
                        <v-row no-gutters>
                          <v-text-field
                            v-if="item.discount_type === ''"
                            v-model="item.discount"
                            style="border-radius: 8px"
                            type="number"
                            class="input_text namedoc_input"
                            placeholder="ระบุส่วนลด"
                            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
                            outlined
                            dense
                            suffix="% / บาท"
                          >
                          </v-text-field>
                          <v-text-field
                            v-if="item.discount_type === 'percent'"
                            v-model="item.discount"
                            style="border-radius: 8px"
                            type="number"
                            class="input_text namedoc_input"
                            placeholder="ระบุส่วนลด"
                            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
                            outlined
                            dense
                            suffix="%"
                            :rules="Rules.percentPattern"
                          >
                          </v-text-field>
                          <v-text-field
                            v-if="item.discount_type === 'bath'"
                            v-model="item.discount"
                            style="border-radius: 8px"
                            type="number"
                            class="input_text namedoc_input"
                            placeholder="ระบุส่วนลด"
                            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
                            outlined
                            dense
                            suffix="บาท"
                            :rules="Rules.bathPattern"
                          >
                          </v-text-field>
                          <v-btn
                            v-if="tierListDetail.length !== 1"
                            class="mx-2 mt-1"
                            outlined
                            fab
                            x-small
                            style="
                              background: #ff515a;
                              border: 1px solid #f2f2f2;
                              box-sizing: border-box;
                              border-radius: 999px;
                            "
                            @click="deleteTextField(index, 'edit', item)"
                            ><v-icon color="white">mdi-minus</v-icon></v-btn
                          >
                          <v-btn
                            v-if="index !== 0 || tierListDetail.length === 1"
                            class="mx-2 mt-1"
                            outlined
                            fab
                            x-small
                            style="
                              background: #27ab9c;
                              border: 1px solid #f2f2f2;
                              box-sizing: border-box;
                              border-radius: 999px;
                            "
                            @click="addTextField('edit')"
                            :disabled="tierListDetail.length > 20"
                            ><v-icon color="white">mdi-plus</v-icon></v-btn
                          >
                        </v-row>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-row
              dense
              style="height: 88px; background: #f5fcfb"
              :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'"
            >
              <v-btn
                rounded
                width="125"
                height="40"
                class="my-auto"
                outlined
                color="#27AB9C"
                @click="cancel('edit')"
                >ยกเลิก</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                rounded
                width="125"
                height="40"
                class="white--text my-auto"
                color="#27AB9C"
                @click="openAwaitModal('edit')"
                >บันทึก</v-btn
              >
            </v-row>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      page: 1,
      lazy: false,
      search: '',
      showCountOrder: 0,
      detailTier: false,
      dialogAwaitTier: false,
      createTire: false,
      radioPercentBath: '',
      sellerShopID: '',
      statusDetail: '',
      changeStatus: false,
      editTire: false,
      tierID: '',
      tierIdDelete: '',
      tierName: '',
      tierNameDetail: '',
      tierList: {},
      tierListDetail: [],
      dataTable: [],
      actions: '',
      StateStatus: 0,
      disableTable: false,
      dataOfTierList: [
        {
          tier_level: 0,
          discount: '',
          discount_percent: 0,
          discount_bath: 0,
          discount_type: 'percent'
        }
      ],
      headers: [
        {
          text: 'ชื่อกลุ่มลูกค้า',
          value: 'tier_name',
          width: '200',
          align: 'start',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'วันที่สร้าง',
          value: 'created_at',
          sortable: false,
          width: '180',
          filterable: false,
          align: 'start',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'สถานะ',
          value: 'status',
          width: '180',
          sortable: false,
          filterable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'จัดการ',
          value: 'manage',
          sortable: false,
          filterable: false,
          width: '250',
          align: 'center',
          class: 'backgroundTable fontTable--text'
        }
      ],
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        emptyCheckbox: [v => !!v || ''],
        percentPattern: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (v <= 100) || 'กรอกจำนวนที่ไม่เกิน 100'
        ],
        bathPattern: [v => !!v || 'กรุณากรอกข้อมูล']
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push('/listCustomerGroupSalesMobile')
      } else {
        this.$router.push('/listCustomerGroupSales')
      }
    },
    changeStatus () {
      if (this.changeStatus === true) {
        this.statusDetail = 'active'
      } else if (this.changeStatus === false) {
        this.statusDetail = 'inactive'
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    var sellerShopID = JSON.parse(localStorage.getItem('shopDetail')).id
    // var sellerShopID = localStorage.getItem('shopDetail').id
    this.sellerShopID = sellerShopID
    this.getListTierSales(0)
  },
  methods: {
    // test () {
    //   console.log('test', this.$refs.FormSettingTier)
    // },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router
          .push({
            path:
              '/sellerMobile?ShopID=' +
              shopDetail.id +
              '&ShopName=' +
              shopDetail.name
          })
          .catch(() => {})
      } else {
        this.$router.push({
          path:
            '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name
        })
      }
    },
    countSales (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async openDetailEdit (item) {
      // console.log('item', item)
      this.tierName = item.tier_name
      this.tierListDetail = item.level_discount_percent
      this.statusDetail = item.status
      this.tierID = item.id
      if (this.statusDetail === 'active') {
        this.changeStatus = true
      } else if (this.statusDetail === 'inactive') {
        this.changeStatus = false
      }
      this.editTire = true
      var data = {
        seller_shop_id: this.sellerShopID,
        tier_id: item.id
      }
      await this.$store.dispatch('actionsGetDetailTierSale', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetDetailTierSale
      var dataLevelDiscount = res.data
      this.tierListDetail = dataLevelDiscount[0].level_discount_percent
      // // console.log('res', dataLevelDiscount[0].level_discount_percent)
    },
    async openDetail (item) {
      // console.log('item', item)
      this.tierNameDetail = item.tier_name
      this.tierListDetail = item.level_discount_percent
      this.statusDetail = item.status
      if (this.statusDetail === 'active') {
        this.changeStatus = true
      } else if (this.statusDetail === 'inactive') {
        this.changeStatus = false
      }
      this.detailTier = true
    },
    async getListTierSales (list) {
      // console.log('getListTierSales', list)
      var dataList = list
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.sellerShopID
      }
      await this.$store.dispatch('actionsGetListTierSale', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetListTierSale
      // console.log('res', res.message)
      if (res.result === 'SUCCESS') {
        if (res.message === 'Get customer tier list successfully.') {
          this.$store.commit('closeLoader')
          this.page = 1
          this.tierList = res.data
          this.StateStatus = dataList
          if (dataList === 0) {
            this.dataTable = this.tierList.data_all
          } else if (dataList === 1) {
            this.dataTable = this.tierList.data_active
          } else if (dataList === 2) {
            this.dataTable = this.tierList.data_inactive
          }
          // console.log('this.dataTable', this.dataTable)
          if (this.dataTable.length === 0) {
            this.disableTable = true
          } else {
            this.disableTable = false
          }
        }
      } else if (
        res.message ===
        'An error has occurred. Please try again in an hour or two.'
      ) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      } else if (res.message === 'Not found data') {
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        }
      }
      // this.dataTable = response.data
      // // console.log('getListTierSales---->', this.dataTable)
    },
    closeDialogAwait () {
      this.dialogAwaitTier = false
    },
    async sendSettingTier (action) {
      // console.log('action', action)
      if (action === 'create') {
        this.dataOfTierList.forEach(element => {
          if (element.discount_type === 'bath') {
            element.discount_bath = Number(element.discount)
          } else if (element.discount_type === 'percent') {
            element.discount_percent = Number(element.discount)
          }
        })
        var data = {
          seller_shop_id: this.sellerShopID,
          tier_name: this.tierName,
          discount_percent: 0,
          show_buyer: 'Y',
          req_document: 'N',
          tier_document_list: [],
          level_discount_percent: this.dataOfTierList
        }
        await this.$store.dispatch('actionsCreateTierSale', data)
        var response = await this.$store.state.ModuleSaleOrder
          .stateCreateTierSale
        if (response.message === 'Create tier customer successfully.') {
          this.dialogAwaitTier = false
          this.createTire = false
          this.getListTierSales(0)
          this.$refs.FormSettingTier.resetValidation()
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'success',
            title: 'สร้างกลุ่มลูกค้าสำเร็จ'
          })
        } else if (
          response.message ===
          'An error has occurred. Please try again in an hour or two.'
        ) {
          this.dialogAwaitTier = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${response.message}`
          })
        }
      } else if (action === 'edit') {
        this.tierListDetail.forEach(element => {
          if (element.discount_type === 'bath') {
            element.discount_bath = Number(element.discount)
          } else if (element.discount_type === 'percent') {
            element.discount_percent = Number(element.discount)
          }
        })
        var data2 = {
          tier_id: this.tierID,
          seller_shop_id: this.sellerShopID,
          tier_name: this.tierName,
          discount_percent: 0,
          show_buyer: 'Y',
          req_document: 'N',
          tier_document_list: [],
          level_discount_percent: this.tierListDetail,
          status: this.statusDetail
        }
        await this.$store.dispatch('actionsEditTierSale', data2)
        var res = await this.$store.state.ModuleSaleOrder.stateEditTierSale
        if (res.message === 'Update tier customer successfully.') {
          this.dialogAwaitTier = false
          this.editTire = false
          this.getListTierSales(0)
          this.$refs.FormSettingTier.resetValidation()
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'success',
            title: 'แก้ไขกลุ่มลูกค้าสำเร็จ'
          })
        } else if (
          res.message ===
          'An error has occurred. Please try again in an hour or two.'
        ) {
          this.dialogAwaitTier = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        } else if (
          res.message === 'Customer in use. Cannot inactive this tier.'
        ) {
          this.dialogAwaitTier = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'กลุ่มลูกค้านี้ถูกใช้งานอยู่ไม่สามารถปิดการใช้งานได้',
            text: `${res.message}`
          })
        }
      } else if (action === 'delete') {
        var data3 = {
          seller_shop_id: this.sellerShopID,
          tier_id: this.tierIdDelete
        }
        await this.$store.dispatch('actionsDeleteTierSale', data3)
        var res2 = await this.$store.state.ModuleSaleOrder.stateDeleteTierSale
        // console.log('res2', res2.message)
        if (res2.message === 'Delete tier customer success.') {
          this.dialogAwaitTier = false
          this.getListTierSales(0)
          this.$refs.FormSettingTier.resetValidation()
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'success',
            title: 'ลบกลุ่มลูกค้าสำเร็จ'
          })
        } else if (
          res2.message ===
          'An error has occurred. Please try again in an hour or two.'
        ) {
          this.dialogAwaitTier = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        } else if (
          res2.message === 'Customer in use. Cannot delete this tier.'
        ) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'กลุ่มลูกค้านี้ถูกใช้งานอยู่ไม่สามารถลบได้',
            text: `${res2.message}`
          })
        }
      }
    },
    openAwaitModal (type, item) {
      // console.log('type', type, item, this.$refs.FormSettingTier.validate())
      if (this.$refs.FormSettingTier.validate()) {
        if (type === 'create') {
          this.actions = type
          this.dialogAwaitTier = true
        } else if (type === 'edit') {
          this.actions = type
          this.dialogAwaitTier = true
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน'
        })
      }
      // if (type === 'create') {
      //   this.actions = type
      //   this.dialogAwaitTier = true
      // } else if (type === 'edit') {
      //   this.actions = type
      //   this.dialogAwaitTier = true
      // } else if (type === 'delete') {
      //   // console.log('item', item)
      //   this.tierIdDelete = item.id
      //   this.actions = type
      //   this.dialogAwaitTier = true
      // }
    },
    async deleteTier (type, item) {
      if (type === 'delete') {
        // console.log('item', item)
        this.tierIdDelete = item.id
        this.actions = type
        this.dialogAwaitTier = true
      }
    },
    async deleteTextField (index, type, item) {
      if (type === 'create') {
        this.dataOfTierList.splice(index, 1)
      } else if (type === 'edit') {
        this.$store.commit('openLoader')
        // console.log('item', index, type, item, this.tierID)
        var data = {
          seller_shop_id: this.sellerShopID,
          tier_id: this.tierID,
          tier_level: item.tier_level
        }
        // stateCheckTierSale
        await this.$store.dispatch('actionsCheckTierSale', data)
        var res = await this.$store.state.ModuleSaleOrder.stateCheckTierSale
        // console.log('res', res)
        if (res.message === 'Customer not in use this tier level.') {
          this.$store.commit('closeLoader')
          this.tierListDetail.splice(index, 1)
        } else if (
          res.message === 'Customer in use. Cannot delete this tier level.'
        ) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ไม่สามารถลบข้อมูลได้',
            text: 'มีลูกค้าใช้งานอยู่ ไม่สามารถลบ tier level นี้ได้'
          })
        } else if (
          res.message ===
          'An error has occurred. Please try again in an hour or two.'
        ) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        }
        // // console.log('res', res)
        // this.tierListDetail.splice(index, 1)
      }
    },
    addTextField (type) {
      if (type === 'create') {
        this.dataOfTierList.push({
          tier_level: this.dataOfTierList.length,
          discount: '',
          discount_percent: 0,
          discount_bath: 0,
          discount_type: 'percent'
        })
      } else if (type === 'edit') {
        this.tierListDetail.push({
          tier_level: this.tierListDetail.length,
          discount: '',
          discount_percent: 0,
          discount_bath: 0,
          discount_type: 'percent'
        })
      }
    },
    addTier () {
      this.createTire = true
      // this.$router.push({ name: 'addTier' })
    },
    cancel (type) {
      // console.log('type', type)
      if (type === 'create') {
        // this.discount_type = 'percent'
        this.tierName = ''
        this.$refs.FormSettingTier.resetValidation()
        this.dataOfTierList = []
        this.dataOfTierList.push({
          tier_level: 0,
          discount: '',
          discount_percent: 0,
          discount_bath: 0,
          discount_type: ''
        })
        this.createTire = false
      } else if (type === 'detail') {
        this.detailTier = false
      } else if (type === 'edit') {
        this.tierName = ''
        this.$refs.FormSettingTier.resetValidation()
        this.editTire = false
      }
    }
  }
}
</script>

<style scoped>
.detailTierText {
  color: #333333;
}
</style>
