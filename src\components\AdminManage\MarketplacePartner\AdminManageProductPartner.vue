<template>
  <v-container class="pa-4">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-col>
        <span v-if="MobileSize" class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">
          <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>สินค้าบริการของ Partner</span>
        <span v-else class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">สินค้าบริการของ Partner</span>
      </v-col>

      <v-card-text class="px-2">
        <v-row>
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาข้อมูลจากชื่อร้านค้า Partner" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
        </v-row>

        <v-row>
          <v-col v-if="!MobileSize && !IpadSize" cols="12" class="d-flex flex-row" style="align-items: center;">
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              ประเภทบริการ :
            </span>
            <v-col cols="3">
              <v-select
                class="setCustomSelect"
                v-model="selectType"
                :items="itemsSelect"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                item-text="text"
                item-value="value"
                dense
                outlined
                hide-details
                style="border-radius: 8px;"
              />
            </v-col>
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              สถานะ :
            </span>
            <v-col cols="3">
              <v-select
                class="setCustomSelect"
                v-model="selectStatus"
                :items="itemsStatus"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                item-text="text"
                item-value="value"
                dense
                outlined
                hide-details
                style="border-radius: 8px;"
              />
            </v-col>
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              วันที่สร้างบริการ :
            </span>
            <v-col cols="3">
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangeDate1"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    hide-details
                    placeholder="วว/ดด/ปปปป"
                    style="border-radius: 8px;"
                  >
                    <v-icon slot="append" color="#CCCCCC">
                      mdi-calendar-multiselect
                    </v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="red" @click="CloseModalRangeDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn text color="primary" @click="setValueRangeDate(dateRange)">
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-col>
          <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              ประเภทบริการ :
            </span>
            <v-col cols="6" class="pa-2">
              <v-select
                class="setCustomSelect"
                v-model="selectType"
                :items="itemsSelect"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                item-text="text"
                item-value="value"
                dense
                outlined
                hide-details
                style="border-radius: 8px;"
              />
            </v-col>
          </v-col>
          <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              สถานะ :
            </span>
            <v-col cols="6" class="pa-2">
              <v-select
                class="setCustomSelect"
                v-model="selectStatus"
                :items="itemsStatus"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
                item-text="text"
                item-value="value"
                dense
                outlined
                hide-details
                style="border-radius: 8px;"
              />
            </v-col>
          </v-col>
          <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
            <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
              วันที่สร้างบริการ :
            </span>
            <v-col cols="6" class="pa-2">
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangeDate1"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    hide-details
                    placeholder="วว/ดด/ปปปป"
                    style="border-radius: 8px;"
                  >
                    <v-icon slot="append" color="#CCCCCC">
                      mdi-calendar-multiselect
                    </v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="red" @click="CloseModalRangeDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn text color="primary" @click="setValueRangeDate(dateRange)">
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-col>
        </v-row>

        <!-- <v-row align="center" justify="space-between">
          <v-col cols="12" md="4" sm="6" class="pl-2 pr-2">
            <v-row dense align="center">
              <v-col cols="5" style="text-align: center;">
                <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                  ประเภทบริการ :
                </span>
              </v-col>
              <v-col cols="7">
                <v-select
                  class="setCustomSelect"
                  v-model="selectType"
                  :items="itemsSelect"
                  append-icon="mdi-chevron-down"
                  :menu-props="{ offsetY: true }"
                  item-text="text"
                  item-value="value"
                  dense
                  outlined
                  hide-details
                  style="border-radius: 8px;"
                />
              </v-col>
            </v-row>
          </v-col>

          <v-col cols="12" md="4" sm="6" class="pl-2 pr-2">
            <v-row dense align="center">
              <v-col cols="5" style="text-align: center;">
                <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                  สถานะ :
                </span>
              </v-col>
              <v-col cols="7">
                <v-select
                  class="setCustomSelect"
                  v-model="selectStatus"
                  :items="itemsStatus"
                  append-icon="mdi-chevron-down"
                  :menu-props="{ offsetY: true }"
                  item-text="text"
                  item-value="value"
                  dense
                  outlined
                  hide-details
                  style="border-radius: 8px;"
                />
              </v-col>
            </v-row>
          </v-col>

          <v-col cols="12" md="4" sm="6" class="pl-2 pr-2">
            <v-row dense align="center">
              <v-col cols="5" style="text-align: center;">
                <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                  วันที่สร้างบริการ :
                </span>
              </v-col>
              <v-col cols="7">
                <v-dialog
                  ref="modalRangeDate"
                  v-model="modalRangeDate"
                  :return-value.sync="dateRange"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="RangeDate1"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      dense
                      hide-details
                      placeholder="วว/ดด/ปปปป"
                      style="border-radius: 8px;"
                    >
                      <v-icon slot="append" color="#CCCCCC">
                        mdi-calendar-multiselect
                      </v-icon>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    color="#27AB9C"
                    v-model="dateRange"
                    scrollable
                    reactive
                    locale="Th-th"
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="red" @click="CloseModalRangeDate()">
                      ยกเลิก
                    </v-btn>
                    <v-btn text color="primary" @click="setValueRangeDate(dateRange)">
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
            </v-col>
            </v-row>
          </v-col>
        </v-row> -->

        <v-row>
          <v-col cols="12">
            <h4 style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 400;">
              <!-- <b>รายการร้านค้า Partner ทั้งหมด {{ countPartner }} ร้านค้า</b> -->
              <b>รายการ Partner ทั้งหมด {{ countPartner }} รายการ</b>
            </h4>
          </v-col>
        </v-row>

        <v-row>
          <v-col>
            <!-- Data table -->
            <v-card elevation="0" v-if="noInfo === false">
              <v-data-table
                :headers="headers"
                :items="filteredPartners"
                :search="search"
                no-data-text="ไม่พบรายการร้านค้า Partner"
                :items-per-page="10"
                class="elevation-1"
                item-key="i"
              >
                <template v-slot:[`item.partnerdetail.created_date`]="{ item }">
                  <span v-if="item.partnerdetail && item.partnerdetail.created_date">
                    {{ new Date(item.partnerdetail && item.partnerdetail.created_date).toLocaleDateString('th-TH', {
                      timeZone: "UTC",
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) }}
                  </span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.partnerdetail.status`]="{ item }">
                  <span v-if="item.partnerdetail && item.partnerdetail.status === 'approve'">
                    <v-chip class="ma-2" color="#E8F5E9" text-color="#388E3C"><b>อนุมัติแล้ว</b></v-chip>
                  </span>
                  <span v-if="item.partnerdetail && item.partnerdetail.status === 'pending'">
                    <v-chip class="ma-2" color="#FFFDE7" text-color="#FBC02D"><b>รออนุมัติ</b></v-chip>
                  </span>
                  <span v-if="item.partnerdetail && item.partnerdetail.status === 'reject'">
                    <v-chip class="ma-2" color="#E1F5FE" text-color="#0288D1"><b>รอแก้ไขข้อมูล</b></v-chip>
                  </span>
                </template>
                <template v-slot:[`item.partnerdetail.service_type`]="{ item }">
                  <v-row dense justify="center" align="center">
                    <div style="max-width: 250px; overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
                      <v-chip-group style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                        <v-chip v-if="item.partnerdetail && item.partnerdetail.service_type.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b></v-chip>
                        <v-chip v-if="item.partnerdetail && item.partnerdetail.service_type.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b></v-chip>
                        <v-chip v-if="item.partnerdetail && item.partnerdetail.service_type.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>POS</b></v-chip>
                        <v-chip v-if="item.partnerdetail && item.partnerdetail.service_type.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>OMS</b></v-chip>
                        <v-chip v-if="item.partnerdetail && item.partnerdetail.service_type.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b></v-chip>
                      </v-chip-group>
                    </div>
                  </v-row>
                </template>
                <template v-slot:[`item.partnerdetail.approved_at`]="{ item }">
                  <span v-if="item.partnerdetail && item.partnerdetail.approved_at">
                    {{ item.partnerdetail && item.partnerdetail.approved_at === '-'
                      ? '-'
                      : new Date(item.partnerdetail && item.partnerdetail.approved_at).toLocaleDateString('th-TH', {
                          timeZone: "UTC",
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                    }}
                  </span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.detail`]="{ item }">
                  <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          width="30"
                          height="30"
                          v-bind="attrs"
                          v-on="on"
                          style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          outlined icon @click="showDetailProductShopPartner(item)">
                          <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                        </v-btn>
                      </template>
                      <span>รายละเอียดร้านค้า Partner</span>
                  </v-tooltip>
                  <v-btn small text rounded color="#27AB9C" @click="showDetailProductShopPartner(item)">
                    <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
                  </v-btn>
                </template>
              </v-data-table>
            </v-card>
            <v-card v-else elevation="0">
                <v-col cols="12" align="center">
                  <div class="my-5">
                    <v-img
                        src="@/assets/noinfo.png"
                        max-height="500px"
                        max-width="500px"
                        height="100%"
                        width="100%"
                        contain
                        aspect-ratio="2">
                    </v-img>
                  </div>
                    <h2 style="padding-top: 20px; padding-bottom: 50px; color: #9A9A9A">
                      <span>ไม่มีข้อมูลรายการร้านค้า Partner</span>
                    </h2>
                </v-col>
              </v-card>
          </v-col>
        </v-row>

      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      noInfo: true,
      search: '',
      selectType: 'all',
      selectStatus: 'all',
      itemsSelect: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'ERP', value: 'ERP' },
        { text: 'POS', value: 'POS' },
        { text: 'OMS', value: 'OMS' },
        { text: 'Web Development', value: 'Web Development' },
        { text: 'Marketing', value: 'Marketing' }
      ],
      itemsStatus: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'อนุมัติแล้ว', value: 'approve' },
        { text: 'รออนุมัติ', value: 'pending' },
        { text: 'รอแก้ไขข้อมูล', value: 'reject' }
      ],
      modalRangeDate: false,
      dateRange: '',
      start_date: '',
      end_date: '',
      RangeDate1: '',
      dataselectdate: '',
      headers: [
        // { text: 'ชื่อร้านค้า Partner', width: '200', align: 'center', sortable: true, value: 'partnerdetail.shop_name', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ Partner', width: '200', align: 'center', sortable: false, value: 'partnerdetail.shop_name', class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทบริการ', width: '250', align: 'center', sortable: false, value: 'partnerdetail.service_type', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สร้างบริการ', width: '160', sortable: false, align: 'center', value: 'partnerdetail.created_date', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', width: '160', sortable: false, align: 'center', value: 'partnerdetail.status', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่อนุมัติ', width: '160', sortable: false, align: 'center', value: 'partnerdetail.approved_at', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', width: '160', sortable: false, align: 'center', value: 'detail', class: 'backgroundTable fontTable--text' }
      ],
      detailpartner: [],
      partnerdetail: [],
      accountdetail: [],
      packages: []

    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    countPartner () {
      return this.filteredPartners.length
    },
    noInfoComputed () {
      return this.filteredPartners.length === 0
    },
    filteredPartners () {
      return this.detailpartner.filter(partner => {
        // ค้นหาชื่อ
        const searchMatch = this.search === '' || (partner.partnerdetail && partner.partnerdetail.shop_name && partner.partnerdetail.shop_name.toLowerCase().includes(this.search.trim().toLowerCase()))
        // ประเภทบริการ
        const typeMatch = this.selectType === 'all' || (partner.partnerdetail && partner.partnerdetail.service_type && partner.partnerdetail.service_type.replace(/\["|"]/g, '').replace(/","/g, ', ').split(', ').includes(this.selectType))
        // สถานะ
        const statusMatch = this.selectStatus === 'all' || (partner.partnerdetail && partner.partnerdetail.status === this.selectStatus)
        // วันที่สร้าง
        const dateMatch = !this.RangeDate1 || (new Date(partner.partnerdetail && partner.partnerdetail.created_date) >= new Date(this.start_date) && new Date(partner.partnerdetail && partner.partnerdetail.created_date) <= new Date(this.end_date))

        return searchMatch && typeMatch && statusMatch && dateMatch
      })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminManageProductPartnerMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'AdminManageProductPartner')
        this.$router.push({ path: '/AdminManageProductPartner' }).catch(() => {})
      }
    },
    search () {
      this.noInfo = this.noInfoComputed
    }
  },
  mounted () {

  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    window.scrollTo(0, 0)
    this.getServicePartnerDetail()
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    formatDate (date) {
      const options = { year: 'numeric', month: '2-digit', day: '2-digit' }
      return new Date(date).toLocaleDateString('th-TH', options)
    },
    async setValueRangeDate (val) {
      // console.log('1', val)
      if (val) {
        const date = new Date(val)
        this.start_date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString()
        this.end_date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)).toISOString()
        this.RangeDate1 = this.formatDate(val)
      }
      await this.fetchSelectDateDetailPartner()
      this.modalRangeDate = false
    },
    async CloseModalRangeDate () {
      this.dateRange = null
      this.RangeDate1 = ''
      this.start_date = ''
      this.end_date = ''
      this.modalRangeDate = false
    },
    async showDetailProductShopPartner (item) {
      if (this.MobileSize) {
        this.$router.replace(`/DetailProductShopPartnerMobile?partnerCode=${item.partner_code}`)
      } else {
        this.$router.replace(`/DetailProductShopPartner?partnerCode=${item.partner_code}`)
      }
    },
    async getServicePartnerDetail () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsServicePartner')
      var response = await this.$store.state.ModuleAdminPanit.stateServicePartner

      if (response.message === 'get product service success.') {
        this.$store.commit('closeLoader')
        this.detailpartner = Object.values(response.data)
        if (this.detailpartner.length > 0) {
          this.noInfo = false
        } else {
          this.detailpartner = []
          this.noInfo = true
        }
      }
    },
    async fetchSelectDateDetailPartner () {
      this.partnerDetails = this.detailpartner.filter(partner => {
        const rawDate = partner.partnerdetail && partner.partnerdetail.created_date

        if (!rawDate) {
          return false
        }

        const partnerDate = new Date(rawDate).toISOString()
        return partnerDate >= this.start_date && partnerDate <= this.end_date
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
