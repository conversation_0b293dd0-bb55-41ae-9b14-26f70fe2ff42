<template>
  <v-footer padless :class="!MobileSize ? 'mt-4' : 'mt-10'">
    <v-card
     flat
     tile
     class="text-center"
     style="background-color: #F3F5F7;"
     width="100%"
    >
      <!-- <v-row> -->
      <!-- web size -->
      <v-card v-once v-if="!IpadProSize" class="hidden-sm-and-down" height="100%" width="100%" elevation="0" color="#FFFFFF">
        <v-card-text>
          <v-row dense class="d-flex justify-space-between">
            <v-card v-for="(item, index) in dataList" :key="index" elevation="0" color="#FFFFFF" height="190px">
              <v-card :key="index" dense elevation="0" color="#FFFFFF" :width="widthOfScreen <= 1460 ? '100%' : '240px'" style="justify-content: center;">
                <v-col cols="12" md="12">
                  <v-avatar size="52" tile>
                    <!-- <v-img v-lazyload :src="item.picture" contain height="60"></v-img> -->
                    <img :alt="'footerImage' + index" loading="lazy" decoding="async" :src="item.picture" height="52" class="footerImageContain">
                  </v-avatar>
                </v-col>
                <p class="headsubFooter" style="color: #041B3C;">{{ item.title }}</p>
                <div style="max-width: 153px; width: 100%; justify-content: center; display: inline-block;">
                  <span class="textsubFooter">{{ item.text }}</span>
                </div>
              </v-card>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
      <!-- IpadProsize -->
      <v-card v-once v-if="IpadProSize" class="hidden-sm-and-down" elevation="0" color="#F3F5F7" width="100%" >
        <v-card  width="100%" height="100%" elevation="0" color="#FFFFFF">
          <v-row dense justify="center">
            <v-card v-for="(item, index) in dataList" :key="index" elevation="0" width="360" height="100" class="mt-4 mb-5" color="#FFFFFF">
              <v-card :key="index" dense elevation="0" width="100%" height="100" color="#FFFFFF">
                <v-row dense>
                  <v-col sm="4" xs="4">
                    <v-avatar tile size="76">
                      <img :alt="'footerImage' + index" loading="lazy" decoding="async" :src="item.picture" max-height="60" max-width="60" class="footerImageContain">
                      <!-- <v-img v-lazyload :src="item.picture" contain max-height="80" max-width="80"></v-img> -->
                    </v-avatar>
                  </v-col>
                  <v-col sm="8" xs="6">
                    <v-row dense>
                      <v-col sm="12">
                        <div style="width: 226px; height: 26px;">
                          <span class="headsubFooterIpad" style="color: #27AB9C;">{{ item.title }}</span>
                        </div>
                      </v-col>
                      <v-col sm="12">
                        <div style="width: 226px; height: 51px;">
                          <span class="textsubFooterIpad">{{ item.text }}</span>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-card>
          </v-row>
        </v-card>
      </v-card>
      <!-- Ipad Size -->
      <v-card v-once  v-else-if="IpadSize && !MobileSize" elevation="0" class="hidden-md-and-up" color="#F3F5F7" width="100%" >
        <v-card  width="100%" height="100%" elevation="0" color="#FFFFFF">
          <v-row dense justify="center">
            <v-card v-for="(item, index) in dataList" :key="index" color="#FFFFFF" elevation="0" width="230" height="190" class="mt-4 mb-5">
              <v-card :key="index" dense elevation="0" class="mx-2" color="#FFFFFF">
                <v-col cols="12" md="12" align="center">
                  <v-avatar tile size="76">
                    <img :alt="'footerImage' + index" loading="lazy" decoding="async" :src="item.picture" max-height="52" max-width="52" class="footerImageContain">
                    <!-- <v-img v-lazyload :src="item.picture" contain max-height="80" max-width="80"></v-img> -->
                  </v-avatar>
                  <!-- <v-img v-lazyload :src="item.picture" contain height="60" width="60"></v-img> -->
                </v-col>
                <p style="font-size: 18px; font-weight: 700; margin-bottom: 4px; color: #041B3C;">{{ item.title }}</p>
                <div style="width: 215px;">
                  <span style="text-align: center; font-size: 16px; align-items: center; font-weight: 400; color: #4F688C;">{{ item.text }}</span>
                </div>
              </v-card>
              <!-- <v-card :key="index" dense elevation="0" width="100%" height="100">
                <v-row dense>
                  <v-col sm="4" xs="4">
                    <v-img v-lazyload :src="item.picture" contain max-height="80" max-width="80"></v-img>
                  </v-col>
                  <v-col sm="8" xs="6">
                    <v-row dense>
                      <v-col sm="12">
                        <div style="width: 226px; height: 26px;">
                          <span style="font-size: 18px; font-weight: bold; margin-bottom: 0px; color: #27AB9C; line-height: 26px;">{{ item.title }}</span>
                        </div>
                      </v-col>
                      <v-col sm="12">
                        <div style="width: 226px; height: 51px;">
                          <span style="font-size: 16px; font-weight: 600; line-height: 24px;">{{ item.text }}</span>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card> -->
            </v-card>
          </v-row>
        </v-card>
      </v-card>
      <!-- MoblieSize -->
      <v-card v-once class="hidden-md-and-up" v-if="MobileSize && !IpadSize" height="100%" width="100%" elevation="0">
        <v-card-text>
          <v-row align-content="center" justify="center" dense>
            <v-card v-for="(item, index) in dataList" :key="index" elevation="0">
              <v-card :key="index" dense elevation="0" class="mx-2">
                <v-col cols="12" md="12">
                  <img :alt="'footerImage' + index" loading="lazy" decoding="async" :src="item.picture" max-height="40" max-width="40" class="footerImageContain">
                </v-col>
                <p class="headsubFooterMobile" style="color: #041B3C;">{{ item.title }}</p>
                <div style="width: 150px;">
                  <span class="textsubFooterMobile">{{ item.text }}</span>
                </div>
              </v-card>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
      <!-- </v-row> -->
    </v-card>
    <!-- Mobile Footer -->
    <v-card v-once flat tile v-if="MobileSize" style="background-color: #FFFFFF;" width="100%">
      <v-card-text>
        <v-row>
          <v-col cols="6">
            <v-row class="ml-1 pt-2">
              <v-avatar size="80" tile>
                <v-img v-lazyload src="https://nexgencommerce.one.th/static/seller_shop_image/27/1QqBm_1693299174.png" contain></v-img>
              </v-avatar>
            </v-row>
          </v-col>
          <v-col cols="6">
            <v-row class="ml-1 pt-2 d-flex justify-end" >
              <v-avatar size="70" tile>
                <v-img v-lazyload src="@/assets/logoonechat.png" contain></v-img>
              </v-avatar>
            </v-row>
          </v-col>
          <!-- <v-col cols="4" md="6" sm="6" align="end">
            <v-row justify="end" class="pr-6 mt-2">
              <v-avatar size="50" tile>
                <v-img src="@/assets/logoonechat.png" contain></v-img>
              </v-avatar>
            </v-row>
          </v-col> -->
        </v-row>
      </v-card-text>
      <v-card-text class="pt-0">
        <v-row justify="start">
          <v-col cols="12">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.HeadOffice') }}</b></span><br>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.Company') }}</span><br/>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.INET') }}</span><br/>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.Address') }}</span><br/>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.Phone') }}</span><br/>
            <a class="textFooterAddress" :href="`mailto:<EMAIL>`">{{ $t('Footer.DetailAddress.Email') }}</a>
          </v-col>
          <v-col cols="12">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.aboutAs') }}</b></span><br>
            <div class="d-flex" style="gap: 5vw;">
              <span class="textFooterAddress" style="cursor: pointer;" @click="policyPage()">{{ $t('Footer.DetailAbout.Policy') }}</span>
              <span class="textFooterAddress" style="cursor: pointer;" @click="termsofusePage()">{{ $t('Footer.DetailAbout.TermsOfUse') }}</span>
              <span class="textFooterAddress" style="cursor: pointer;" @click="DeletingUserData()">การลบข้อมูลผู้ใช้</span>
            </div>
          </v-col>
          <v-col cols="6">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.PaymentMethods') }}</b></span><br>
            <v-row class="pt-3"><v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/thaidotcomlogo.png" contain height="20" width="20" max-height="20" max-width="20" class="ml-2 mr-2"></v-img><span style="font-size: 12px; color: #333333;">{{ $t('Footer.DetailPayment.Payment') }}</span></v-row>
            <v-row class="ml-5 mt-4">
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/Visa.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/Mastercard.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/jcb.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/Union.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/paymentNetwork.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
            </v-row>
            <v-row class="ml-5 mt-4">
              <!-- <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/ktb1.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img> -->
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/bbl.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/k.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/scb.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img>
            </v-row>
            <v-row class="ml-5 mt-4">
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/ITMX-LOGO.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
            </v-row>
          </v-col>
          <v-col cols="6">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.FollowUs') }}</b></span><br>
            <v-row class="pt-3"><v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Contact/PNG/facebook.png" contain height="20" width="20" max-height="20" max-width="20" class="ml-2 mr-2"></v-img><a href="https://www.facebook.com/NexGenCommerceTH" target="_blank" style="font-size: 12px; color: #333333;">Nex Gen Commerce</a></v-row>
            <v-row class="pt-5"><v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Contact/PNG/line.png" contain height="20" width="20" max-height="20" max-width="20" class="ml-2 mr-2"></v-img><a href="https://line.me/ti/p/~@nexgencommerce" target="_blank" style="font-size: 12px; color: #333333;">Nex Gen Commerce</a></v-row>
          </v-col>
          <v-col cols="12">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.Certification') }}</b></span><br>
             <a href="https://dbdregistered.dbd.go.th/api/public/trustmarkinfo/0105562011033/128064">
               <v-img v-lazyload src="@/assets/bns_registered.png" contain width="100px" height="100px"></v-img>
             </a>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <span style="color: #333333;">© Copyright 2022, Internet Thailand Public Company Limited.</span>
      </v-card-text>
      <!-- <div id="Certificate-banners" style="background-color: #E9ECF0; width: 100%; padding-left: 45%;"></div> -->
    </v-card>
    <!-- Web Footer -->
    <v-card v-once flat tile class="text-center" v-else style="background-color: white;" width="100%" >
      <v-card-text>
        <v-row>
          <v-col cols="6">
            <v-row class="ml-1 pt-2">
              <v-avatar size="80" tile>
                <v-img v-lazyload src="https://nexgencommerce.one.th/static/seller_shop_image/27/1QqBm_1693299174.png" contain></v-img>
              </v-avatar>
            </v-row>
          </v-col>
          <v-col cols="6">
            <v-row class="ml-1 pt-2 d-flex justify-end" >
              <v-avatar size="70" tile>
                <v-img v-lazyload src="@/assets/logoonechat.png" contain></v-img>
              </v-avatar>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-text class="pt-0">
        <v-row :justify="IpadSize ? 'start' : 'center'" align-content="center">
          <v-col cols="12" md="3" sm="4" style="text-align: left;" :class="IpadSize ? 'ml-4' : 'ml-16'">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.HeadOffice') }}</b></span><br>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.Company') }}</span><br/>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.INET') }}</span><br/>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.Address') }}</span><br/>
            <span class="textFooterAddress">{{ $t('Footer.DetailAddress.Phone') }}</span><br/>
            <a class="textFooterAddress" :href="`mailto:<EMAIL>`">{{ $t('Footer.DetailAddress.Email') }}</a>
            <!-- <span style="font-size: 12px; color: #333333;">เบอร์โทรศัพท์: 02257700 - Callcenter</span><br/>
            <span style="font-size: 12px; color: #333333;">อีเมล: <EMAIL></span> -->
          </v-col>
          <v-col cols="12" md="2" sm="3" style="text-align: left;">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.aboutAs') }}</b></span><br>
            <span class="textFooterAddress texthover" @click="policyPage()" style="cursor: pointer;">{{ $t('Footer.DetailAbout.Policy') }}</span><br/>
            <span class="textFooterAddress texthover" style="cursor: pointer;" @click="termsofusePage()">{{ $t('Footer.DetailAbout.TermsOfUse') }}</span><br/>
            <span class="textFooterAddress texthover" style="cursor: pointer;" @click="DeletingUserData()">{{ $t('Footer.DetailAbout.DeletingUserData') }}</span>
          </v-col>
          <!-- <v-col cols="12" md="2" style="text-align: left;">
            <span style="font-size: 12px; font-weight: bold; color: #333333;">หมวดหมู่สินค้า</span><br/>
            <v-row dense>
              <v-col cols="12" md="6" style="text-align: left;">
                <span style="font-size: 12px; color: #333333;">อุปกรณ์สำนักงาน</span><br/>
                <span style="font-size: 12px; color: #333333;">ไอที</span><br/>
                <span style="font-size: 12px; color: #333333;">เฟอร์นิเจอร์</span><br/>
                <span style="font-size: 12px; color: #333333;">อุปกรณืทำความสะอาด</span><br/>
                <span style="font-size: 12px; color: #333333;">ครัว&เครื่องใช้</span><br/>
                <span style="font-size: 12px; color: #333333;">เวชภัณฑ์</span>
              </v-col>
              <v-col cols="12" md="6" style="text-align: left;">
                <span style="font-size: 12px; color: #333333;">อุปกรณ์โรงงาน</span><br/>
                <span style="font-size: 12px; color: #333333;">เครื่องมือช่าง</span><br/>
                <span style="font-size: 12px; color: #333333;">สมาร์ทโฟน&ไลฟ์สไตล์</span><br/>
                <span style="font-size: 12px; color: #333333;">ของขวัญ&งานพิมพ์</span><br/>
                <span style="font-size: 12px; color: #333333;">ซอร์ฟแวร์&บริการ</span><br/>
                <span style="font-size: 12px; color: #333333;">หนังสือ&บันเทิง</span>
               </v-col>
            </v-row>
          </v-col> -->
          <v-col cols="12" md="2" sm="3" style="text-align: left;">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.PaymentMethods') }}</b></span><br>
            <v-row class="pt-3"><v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/thaidotcomlogo.png" contain height="20" width="20" max-height="20" max-width="20" class="ml-2 mr-2"></v-img><span class="textFooterAddress">{{ $t('Footer.DetailPayment.Payment') }}</span></v-row>
            <!-- <v-col cols="12" md="2" v-for="item1 in iconPaymentRowOne" :key="item1" class="mt-2">
              <v-row>
                <v-img :src="item1" contain height="25" width="25" max-height="25" max-width="25" class="ml-8"></v-img>
              </v-row>
            </v-col> -->
            <v-row class="ml-5 mt-4">
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/Visa.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/Mastercard.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/jcb.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/Union.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/paymentNetwork.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
            </v-row>
            <v-row class="ml-5 mt-4">
              <!-- <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/ktb1.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img> -->
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/bbl.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/k.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img>
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/scb.png" contain height="20" width="20" max-height="25" max-width="25" class="ml-1"></v-img>
            </v-row>
            <v-row class="ml-5 mt-4">
              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Payment/ITMX-LOGO.png" contain height="25" width="25" max-height="25" max-width="25" class="ml-1"></v-img>
            </v-row>
          </v-col>
          <v-col cols="12" md="2" sm="4" style="text-align: left;" :class="IpadSize ? 'ml-4' : ''">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.FollowUs') }}</b></span><br>
            <v-row class="pt-3"><v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Contact/PNG/facebook.png" contain height="20" width="20" max-height="20" max-width="20" class="ml-2 mr-2"></v-img><a href="https://www.facebook.com/NexGenCommerceTH" target="_blank" style="font-size: 12px; color: #333333;">Nex Gen Commerce</a></v-row>
            <v-row class="pt-5"><v-img v-lazyload src="@/assets/ImageINET-Marketplace/ICONPayment/Contact/PNG/line.png" contain height="20" width="20" max-height="20" max-width="20" class="ml-2 mr-2"></v-img><a href="https://line.me/ti/p/~@nexgencommerce" target="_blank" style="font-size: 12px; color: #333333;">Nex Gen Commerce</a></v-row>
          </v-col>
          <v-col cols="12" md="2" sm="4" style="text-align: left;">
            <span style="font-size: medium; color: #3EC6B6;"><b>{{ $t('Footer.Certification') }}</b></span><br>
            <v-row class="pl-2">
              <a href="https://dbdregistered.dbd.go.th/api/public/trustmarkinfo/0105562011033/128064">
                <v-img v-lazyload src="@/assets/bns_registered.png" contain width="100px" height="100px"></v-img>
              </a>
            </v-row>
          </v-col>
        </v-row>
        <!-- <v-row justify="start" v-if="IpadSize">
          <v-col cols="12" md="2" sm="4" style="text-align: left;" class="ml-4">
            <span class="headFooterAddress">เครื่องหมายรับรอง</span><br/>
            <v-row class="pt-3">
              <div id="Certificate-banners" class="pr-4"></div>
            </v-row>
          </v-col>
        </v-row> -->
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text style="background-color: #F3F5F7;">
        <span style="color: #333333;">© Copyright 2022, Nex Gen Shop.</span>
      </v-card-text>
    </v-card>
  </v-footer>
</template>

<script>
export default {
  data () {
    return {
      widthOfScreen: window.innerWidth,
      curruntYear: '',
      icons: [
        'mdi-facebook',
        'mdi-twitter',
        'mdi-linkedin',
        'mdi-instagram'
      ],
      dataList: [
        { picture: require('@/assets/ImageINET-Marketplace/Service/IconPNG/shield.png'), title: this.$t('Footer.Account'), text: this.$t('Footer.Detail.Account') },
        { picture: require('@/assets/ImageINET-Marketplace/Service/IconPNG/chats.png'), title: this.$t('Footer.Chat'), text: this.$t('Footer.Detail.Chat') },
        { picture: require('@/assets/ImageINET-Marketplace/Service/IconPNG/package.png'), title: this.$t('Footer.Shop'), text: this.$t('Footer.Detail.Shop') },
        { picture: require('@/assets/ImageINET-Marketplace/Service/IconPNG/dollar.png'), title: this.$t('Footer.Payment'), text: this.$t('Footer.Detail.Payment') },
        { picture: require('@/assets/ImageINET-Marketplace/Service/IconPNG/truck.png'), title: this.$t('Footer.Shipping'), text: this.$t('Footer.Detail.Shipping') },
        { picture: require('@/assets/ImageINET-Marketplace/Service/IconPNG/customerService.png'), title: this.$t('Footer.CustomerService'), text: this.$t('Footer.Detail.CustomerService') }
      ]
    }
  },
  created () {
    // this.getFullYear()
  },
  beforeDestroy () { // if using Vue 2
    window.removeEventListener('resize', this.handleResizeFooter)
  },
  mounted () {
    this.handleResizeFooter()
    window.addEventListener('resize', this.handleResizeFooter)
    // window.onload = () => {
    //   // Set script DBD in head
    //   const DBD = document.createElement('script')
    //   DBD.setAttribute('id', 'dbd-init')
    //   DBD.setAttribute('src', 'https://www.trustmarkthai.com/callbackData/initialize.js?t=9ec546-31-5-3adc8433326c967aef3303a6293493fd3fdff45')
    //   document.head.appendChild(DBD)
    //   // Logo DBD
    //   const el = document.createElement('div')
    //   el.setAttribute('id', 'Certificate-banners')
    //   const box = document.getElementById('Certificate-banners')
    //   box.appendChild(el)
    // }
    // this.getFullYear()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    handleResizeFooter () {
      this.windowWidth = window.innerWidth
      this.$forceUpdate()
    },
    policyPage () {
      this.$router.push({ path: '/policy' }).catch(() => {})
    },
    termsofusePage () {
      this.$router.push({ path: '/termsofuse' }).catch(() => {})
    },
    DeletingUserData () {
      this.$router.push({ path: '/DeletingUserData' }).catch(() => {})
    }
    // getFullYear () {
    //   var curruntYear = new Date()
    //   this.curruntYear = curruntYear.getFullYear()
    // }
  }
  // methods: {
  //   getDBDLogo () {
  //     // Set script DBD in head
  //     const DBD = document.createElement('script')
  //     DBD.setAttribute('id', 'dbd-init')
  //     DBD.setAttribute('src', 'https://www.trustmarkthai.com/callbackData/initialize.js?t=9ec546-31-5-3adc8433326c967aef3303a6293493fd3fdff45')
  //     document.head.appendChild(DBD)
  //     // Logo DBD
  //     const el = document.createElement('div')
  //     el.setAttribute('id', 'Certificate-banners')
  //     const box = document.getElementById('logo-DBD')
  //     box.appendChild(el)
  //   }
  // }
}
</script>
