<template>
  <v-dialog
    max-width="800"
    v-model="modal"
  >
    <template v-slot:activator="{ on, attrs }">
        <v-btn
        color="primary"
        v-bind="attrs"
        v-on="on"
        >From the bottom</v-btn>
    </template>
    <template v-slot:default="modal">
      <v-card v-if="modal">
        <v-toolbar
            color="primary"
            dark
        >
        <v-col cols="11" class="d-flex justify-center">
          <span style="font-size: 24px">การคืนเงิน/คืนสินค้า</span>
        </v-col>
        <v-btn @click="cancelRefund" icon>
            <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row class="mt-5">
                <v-col>
                    <span style="font-size: 20px; color: black; font-weight: bold;">เหตุผลในการคืนสินค้าเงิน/คืนสินค้า</span>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-text-field
                      outlined
                      v-model="selectedReason"
                      dense
                      disabled
                    ></v-text-field>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <span style="font-size: 20px; color: black; font-weight: bold;">รายละเอียด</span>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-textarea
                        label="กรอกรายละเอียดปัญกาที่พบ เพื่อให้เจ้าหน้าที่ หรือผู้ขาย ใช้ในการพิจารณาเพิ่มเติม"
                        value=""
                        outlined
                        disabled
                    ></v-textarea>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <span style="font-size: 20px; color: black; font-weight: bold;">รูปภาพสินค้า</span>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                  <v-card
                    v-if="FlashsTest.length === 0"
                    class="mt-3"
                    elevation="0"
                    :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px;'"
                    height="400px%"
                    @click="onPickFile()"
                    >
                    <v-card-text >
                        <v-row dense align="center" justify="center" style="cursor: pointer;">
                        <v-file-input
                            v-model="DataImage"
                            :items="DataImage"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImage()"
                            id="file_input"
                            multiple :clearable="false"
                            style="display:none"
                        >
                        </v-file-input>
                        <v-col cols="12" md="12">
                            <v-row justify="center" align="center">
                            <v-col cols="12" md="12" align="center">
                                <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                width="280.34"
                                height="154.87"
                                contain
                                ></v-img>
                            </v-col>
                            <v-col cols="12" md="12" style="text-align: center;">
                                <span style="line-height: 24px; font-weight: 400;"
                                :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                                <span style="line-height: 24px; font-weight: 400;"
                                :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                <span style="line-height: 16px; font-weight: 400;"
                                :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                            </v-col>
                            </v-row>
                        </v-col>
                        </v-row>
                    </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-card-text>
        <v-card-actions class="justify-end">
            <v-col class="d-flex justify-end">
                <v-btn
                    class="mr-3"
                    @click="cancelRefund"
                >ยกเลิก</v-btn>
                <v-btn color="#27ab9c" class="white--text" @click="confirmRefund">บันทึก</v-btn>
            </v-col>
        </v-card-actions>
      </v-card>
    </template>
  </v-dialog>
</template>

<script>
export default {
  data () {
    return {
      itemsReason: [
        'ฉันไม่ได้รับพัสดุของคำสั่งซื้อนี้',
        'ได้รับสินค้าไม่ครบ / ชิ้นส่วนไม่ครบ',
        'ได้รับกล่องเปล่า',
        'สินค้าที่ได้รับมาผิด ไม่ใช่สินค้าที่สั่ง',
        'สินค้าสภาพไม่ดีหรือมีความเสียหาย',
        'การทำงานของสินค้าไม่สมบูรณ์',
        'ต้องการคืนสินค้าในสภาพสมบูรณ์',
        'สินค้าผิดลิขสิทธิ์',
        'สินค้าไม่ตรงตามรายละเอียดที่ระบุไว้'
      ],
      selectedReason: 'ได้รับกล่องเปล่า',
      DataImage: [],
      FlashsTest: [],
      theRedI: true,
      modal: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    DataDialog (data) {
      this.modal = true
    }
  }
}
</script>

<style>

</style>
