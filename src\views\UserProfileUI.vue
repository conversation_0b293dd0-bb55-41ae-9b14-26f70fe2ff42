<template>
  <div>
    <!-- Breadcrumb User Detail -->
    <v-breadcrumbs :items="itemsBreadcrumb" :key="keyChange" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }" v-snip="1">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container class="pt-0">
      <v-row v-if="!MobileSize">
        <v-col cols="12" md="3" sm="4">
          <v-card style="border-radius: 8px; border: 1px solid #F2F2F2;" class="mt-6" max-height="100%" height="900px" elevation="0">
            <v-list nav>
              <!-- บัญชีของฉัน -->
              <v-list-group
                v-for="item in items"
                :key="item.keys"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#3EC6B6"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="selectedItem"
                  :mandatory="selectedItem === 5 ? false : selectedItem === 6 ? false : selectedItem === 7 ? false : selectedItem === 8 ? false : selectedItem === 9 ? false : selectedItem === 10 ? false : selectedItem === 11 ? false: selectedItem === 12 ? false : selectedItem === 13 ? false : selectedItem === 14 ? false : selectedItem === 15 ? false : selectedItem === 16 ? false : true"
                >
                  <v-list-item
                    v-for="child in filteredItems[0].items"
                    :key="child.key"
                    color="#3EC6B6"
                    dense
                    class="pl-16"
                  >
                    <v-list-item-content @click="changePath(child.path)">
                      <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
              <!-- รายการสั่งซื้อSale -->
              <div v-if="RoleUser === 'sale_order_no_JV'">
                <v-list-group
                  v-for="item in itemsSaleOrder"
                  :key="item.keys"
                  v-model="item.active"
                  :prepend-icon="item.action"
                  no-action
                  color="#3EC6B6"
                >
                  <template v-slot:activator>
                    <v-list-item-content>
                      <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                    </v-list-item-content>
                  </template>

                  <v-list-item-group
                    v-model="select"
                    :mandatory="selectedItem === 4 ? true : selectedItem === 5 ? true : selectedItem === 6 ? true : selectedItem === 7 ? true : selectedItem === 8 ? true : false"
                  >
                    <v-list-item
                      v-for="child in item.items"
                      :key="child.key"
                      color="#3EC6B6"
                      dense
                      class="pl-16"
                    >
                      <v-list-item-content @click="changePath(child.path)">
                        <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list-group>
              </div>
               <!-- รายการสั่งซื้อ -->
              <div v-else-if="RoleUser === 'ext_buyer'">
                <v-list-group
                  v-for="item in itemsOrder"
                  :key="item.keys"
                  v-model="item.active"
                  :prepend-icon="item.action"
                  no-action
                  color="#3EC6B6"
                >
                  <template v-slot:activator>
                    <v-list-item-content>
                      <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                    </v-list-item-content>
                  </template>

                  <v-list-item-group
                    v-model="select"
                    :mandatory="selectedItem === 4 ? true : selectedItem === 5 ? true : selectedItem === 6 ? true : selectedItem === 7 ? true : selectedItem === 8 ? true : false"
                  >
                    <v-list-item
                      v-for="child in item.items"
                      :key="child.key"
                      color="#3EC6B6"
                      dense
                      class="pl-16"
                    >
                      <v-list-item-content @click="changePath(child.path)">
                        <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list-group>

                <v-list-group
                  v-for="item in itemsAffiliate"
                  :key="item.keys"
                  v-model="item.active"
                  :prepend-icon="item.action"
                  no-action
                  color="#3EC6B6"
                >
                  <template v-slot:activator>
                    <v-list-item-content>
                      <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                    </v-list-item-content>
                  </template>
                  <v-list-item-group
                    v-model="select2"
                    :mandatory="selectedItem === 9 ? true : selectedItem === 10 ? true : selectedItem === 11 ? true : selectedItem === 12 ? true : selectedItem === 13 ? true : selectedItem === 14 ? true : selectedItem === 15 ? true : selectedItem === 16 ? true : false"
                  >
                    <v-list-item
                      v-for="child in item.items"
                      :key="child.key"
                      color="#3EC6B6"
                      dense
                      class="pl-16"
                    >
                      <v-list-item-content @click="changePath(child.path)">
                        <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
              </v-list-group>

              </div>
              <!-- <v-list-group
                v-for="item in itemsPoint"
                :key="item.keys"
                v-model="item.active"
                :prepend-icon="item.action"
                no-action
                color="#3EC6B6"
              >
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group
                  v-model="select"
                >
                    <v-list-item
                      v-for="child in item.items"
                      :key="child.key"
                      color="#3EC6B6"
                      dense
                      class="pl-16"
                    >
                      <v-list-item-content @click="changePath(child.path)">
                        <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list-group> -->
                <v-list-group
                  v-for="item in itemsChat"
                  :key="item.keys"
                  v-model="item.active"
                  :prepend-icon="item.action"
                  no-action
                  color="#3EC6B6"
                >
                  <template v-slot:activator>
                    <v-list-item-content>
                      <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                    </v-list-item-content>
                  </template>

                  <v-list-item-group
                    v-model="select"
                  >
                    <v-list-item
                      v-for="child in item.items"
                      :key="child.key"
                      color="#3EC6B6"
                      dense
                      class="pl-16"
                    >
                      <v-list-item-content @click="changePath(child.path)">
                        <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list-group>
                <!-- affiliate -->
                <!-- <v-list-group
                  v-for="item in itemAffiliate"
                  :key="item.keys"
                  v-model="item.active"
                  :prepend-icon="item.action"
                  no-action
                  color="#3EC6B6"
                >
                  <template v-slot:activator>
                    <v-list-item-content>
                      <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 24px;">{{ item.title }}</v-list-item-title>
                    </v-list-item-content>
                  </template>

                  <v-list-item-group
                    v-model="select"
                    :mandatory="selectedItem === 4 ? true : selectedItem === 5 ? true : selectedItem === 6 ? true : selectedItem === 7 ? true : selectedItem === 8 ? true : false"
                  >
                    <v-list-item
                      v-for="child in item.items"
                      :key="child.key"
                      color="#3EC6B6"
                      dense
                      class="pl-16"
                    >
                      <v-list-item-content @click="changePath(child.path)">
                        <v-list-item-action style="white-space: normal; font-size: 18px !important; line-height: 24px;">{{ child.title }}</v-list-item-action>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list-group> -->
              <!-- <v-list-item-group
               v-model="select"
               :mandatory="selectedItem !== 4 ? false : true"
              >
                <v-list-item
                 color="#3EC6B6"
                >
                  <v-list-item-icon>
                    <v-icon>mdi-file-document-outline</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content @click="changePath('pobuyerProfile')">
                    <v-list-item-title style="white-space: normal; font-weight: bold; line-height: 30px;">รายการสั่งซื้อของฉัน</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group> -->

            </v-list>
          </v-card>
        </v-col>
        <!-- </v-navigation-drawer> -->
        <v-col cols="12" md="9" sm="8">
          <v-main style="padding: 0px;">
            <v-container>
              <v-card style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0" max-height="100%" height="100%" class="mt-3" :class="IpadSize ? 'px-0 py-0' : MobileSize ? '' : ''">
                <router-view></router-view>
              </v-card>
            </v-container>
          </v-main>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'

export default {
  // components: {
  //   UserProfile: () => import('@/components/UserProfile/UserProfileUI')
  // },
  async created () {
    this.$EventBus.$emit('getPath')
    this.$EventBus.$on('changeNavAccount', this.changeNavAccount)
    // this.$EventBus.$emit('CheckFooter')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('roleUser')) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RoleUser = 'ext_buyer'
    }
    await this.getConsent()
    this.changeNavAccount()
    // console.log('this.itemsAffiliate[0]', this.itemsAffiliate[0])
    // console.log('this.changeNav', this.$EventBus.$on('changeNav', this.changeNav))
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('GetConsent', this.getConsent)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('GetConsent')
    })
  },
  watch: {
    // selectedItem (val) {
    //   console.log('selectedItem', val)
    // },
    // select2 (val) {
    //   console.log('select2', val)
    //   this.select2 = val
    // },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userprofile' }).catch(() => {})
      }
    }
  },
  computed: {
    filteredItems () {
      if (this.RoleUser !== 'ext_buyer') {
        return [
          {
            ...this.items[0],
            items: this.items[0].items.filter(item => item.key === 0)
          }
        ]
      }
      return this.items
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  data () {
    return {
      isBuyer: '',
      keyChange: 0,
      itemsBreadcrumb: [],
      selectedItem: 0,
      select: 0,
      select2: 0,
      items: [
        {
          action: 'mdi-account',
          active: true,
          keys: 1,
          items: [
            { key: 0, title: `${this.$t('MenuBuyer.menuMyProfile')}`, path: 'userprofile' },
            // { key: 1, title: 'เปลี่ยนรหัสผ่าน', path: 'changePassword' },
            { key: 1, title: `${this.$t('MenuBuyer.menuBankAccount')}`, path: 'BankAccountUser' },
            { key: 2, title: `${this.$t('MenuBuyer.menuShippingAddress')}`, path: 'addressProfile' },
            { key: 3, title: `${this.$t('MenuBuyer.menuFavoriteProduct')}`, path: 'favorite' },
            { key: 4, title: `${this.$t('MenuBuyer.menuCouponPoint')}`, path: 'MyCouponsAndPoints' }
          ],
          title: `${this.$t('MenuBuyer.titleUserProfile')}`
        }
      ],
      itemsOrder: [
        {
          action: 'mdi-file-document-outline',
          active: false,
          keys: 2,
          items: [
            { key: 0, title: `${this.$t('MenuBuyer.menuMyOrders')}`, path: 'pobuyerProfile' },
            { key: 1, title: `${this.$t('MenuBuyer.menuProfileRecord')}`, path: 'pobuyerProfileRecord' },
            // { key: 1, title: 'ติดตามสถานะสินค้า', path: 'tackingbuyer' },
            // { key: 2, title: 'รายการคืนสินค้าของฉัน', path: 'refundProduct' },
            { key: 3, title: `${this.$t('MenuBuyer.menuReview')}`, path: 'reviewBuyer' }
          ],
          title: `${this.$t('MenuBuyer.titleManageOrders')}`
        }
      ],
      itemsSaleOrder: [
        {
          action: 'mdi-file-document-outline',
          active: false,
          keys: 3,
          items: [
            { key: 0, title: `${this.$t('MenuBuyer.menuMyOrders')}`, path: 'ListOrderBySales' },
            { key: 1, title: `${this.$t('MenuBuyer.menuProfileRecord')}`, path: 'pobuyerProfileRecordSales' }
            // { key: 1, title: 'ติดตามสถานะสินค้า', path: 'tackingbuyer' },
            // { key: 2, title: 'รายการคืนสินค้าของฉัน', path: 'refundProduct' },
            // { key: 3, title: 'การประเมินความพึงพอใจ', path: 'reviewBuyer' }
          ],
          title: `${this.$t('MenuBuyer.titleManageSales')}`
        }
      ],
      itemsAffiliate: [{}],
      // itemAffiliate: [
      //   {
      //     action: 'mdi-storefront-outline',
      //     active: false,
      //     keys: 4,
      //     items: [
      //       { key: 0, title: 'การแชร์ของฉัน', path: 'SharedBuyerAffiliate' },
      //       { key: 1, title: 'รายงานการคลิก', path: 'ClickBuyerAffiliate' },
      //       { key: 1, title: 'รายงานคำสั่งซื้อ', path: 'orderedBuyerAffilate' }
      //     ],
      //     title: 'affiliate'
      //   }
      // ],
      // itemsPoint: [
      //   {
      //     action: 'mdi-file-document-outline',
      //     active: false,
      //     keys: 4,
      //     items: [
      //       { key: 0, title: 'แต้มของฉัน', path: 'pobuyerprofilepointallshop' }
      //       // { key: 1, title: 'ติดตามสถานะสินค้า', path: 'tackingbuyer' },
      //       // { key: 2, title: 'รายการคืนสินค้าของฉัน', path: 'refundProduct' },
      //       // { key: 3, title: 'การประเมินความพึงพอใจ', path: 'reviewBuyer' }
      //     ],
      //     title: 'แต้ม'
      //   }
      // ],
      itemsChat: [
        {
          action: 'mdi-chat-outline',
          active: false,
          keys: 5,
          items: [
            { key: 0, title: `${this.$t('MenuBuyer.menuMyChat')}`, path: 'ChatAll' }
            // { key: 1, title: 'ติดตามสถานะสินค้า', path: 'tackingbuyer' },
            // { key: 2, title: 'รายการคืนสินค้าของฉัน', path: 'refundProduct' },
            // { key: 3, title: 'การประเมินความพึงพอใจ', path: 'reviewBuyer' }
          ],
          title: `${this.$t('MenuBuyer.titleChat')}`
        }
      ]
      // itemsPoint: [
      //   {
      //     action: 'mdi-file-document-outline',
      //     active: false,
      //     keys: 4,
      //     items: [
      //       { key: 0, title: 'แต้มของฉัน', path: 'pobuyerprofilepointallshop' }
      //       // { key: 1, title: 'ติดตามสถานะสินค้า', path: 'tackingbuyer' },
      //       // { key: 2, title: 'รายการคืนสินค้าของฉัน', path: 'refundProduct' },
      //       // { key: 3, title: 'การประเมินความพึงพอใจ', path: 'reviewBuyer' }
      //     ],
      //     title: 'แต้ม'
      //   }
      // ]
    }
  },
  methods: {
    changePath (val) {
      // console.log('changePath----->', val)
      // console.log('changePath----->', this.$router.currentRoute.name)
      this.$router.push({ path: `${val}` }).catch(() => {})
    },
    gotoBannerPage (val) {
      this.$router.push({ path: `${val.href}` }).catch(() => {})
    },
    changeNavAccount () {
      // console.log('1-select2', this.select2)
      // console.log('2-selectedItem', this.selectedItem)
      // this.select2 = 0
      // console.log('this.itemsAffiliate[0]', this.itemsAffiliate[0])
      this.itemsBreadcrumb = []
      if (this.$router.currentRoute.name === 'userprofileUI') {
        this.keyChange = 1
        this.selectedItem = 0
        this.select = 5
        this.items[0].active = true
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleUserProfile')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/userprofile'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuMyProfile')}`,
            id: 3,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'BankAccountUser') {
        this.keyChange = 2
        this.selectedItem = 1
        this.select = 6
        this.items[0].active = true
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleUserProfile')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/userprofile'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuBankAccount')}`,
            id: 5,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'addressProfile') {
        this.keyChange = 3
        this.selectedItem = 2
        this.select = 7
        this.items[0].active = true
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleUserProfile')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/userprofile'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuShippingAddress')}`,
            id: 5,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'favorite') {
        this.keyChange = 4
        this.selectedItem = 3
        this.select = 8
        this.items[0].active = true
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleUserProfile')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/userprofile'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuFavoriteProduct')}`,
            id: 4,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'MyCouponsAndPoints') {
        this.keyChange = 5
        this.selectedItem = 4
        this.select = 9
        this.items[0].active = true
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleUserProfile')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/userprofile'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuCouponPoint')}`,
            id: 5,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'pobuyeruiProfile' || this.$router.currentRoute.name === 'pobuyerdetailui') {
        this.keyChange = 6
        this.select = 0
        this.selectedItem = 4
        this.items[0].active = false
        this.itemsOrder[0].active = true
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleManageOrders')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/pobuyerProfile'
          },
          // {
          //   category_name: 'จัดการรายการสั่งซื้อ Sale Order',
          //   id: 1,
          //   disabled: false,
          //   color: '#636363',
          //   href: '/ListOrderBySales'
          // },
          {
            category_name: `${this.$t('MenuBuyer.menuMyOrders')}`,
            id: 6,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'tackingbuyer') {
        // this.select = 1
        // this.selectedItem = 5
        // this.items[0].active = false
        // this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'refundProduct' || this.$router.currentRoute.name === 'refundDetail') {
        // this.select = 2
        // this.selectedItem = 6
        // this.items[0].active = false
        // this.itemsOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'reviewBuyer') {
        this.keyChange = 7
        this.select = 2
        this.selectedItem = 6
        this.items[0].active = false
        this.itemsOrder[0].active = true
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleManageOrders')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/pobuyerProfile'
          },
          // {
          //   category_name: 'จัดการรายการสั่งซื้อ Sale Order',
          //   id: 1,
          //   disabled: false,
          //   color: '#636363',
          //   href: '/ListOrderBySales'
          // },
          {
            category_name: `${this.$t('MenuBuyer.menuReview')}`,
            id: 7,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'pobuyeruiProfileRecord') {
        this.keyChange = 8
        this.select = 1
        this.selectedItem = 5
        this.items[0].active = false
        this.itemsOrder[0].active = true
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleManageOrders')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/pobuyerProfile'
          },
          // {
          //   category_name: 'จัดการรายการสั่งซื้อ Sale Order',
          //   id: 1,
          //   disabled: false,
          //   color: '#636363',
          //   href: '/ListOrderBySales'
          // },
          {
            category_name: `${this.$t('MenuBuyer.menuProfileRecord')}`,
            id: 8,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'ListOrderBySales' || this.$router.currentRoute.name === 'DetailDueDateSaleOrderBySale' || this.$router.currentRoute.name === 'DetailOrderSalesNoJV') {
        this.keyChange = 9
        this.select = 2
        this.selectedItem = 4
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = true
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: 'จัดการรายการสั่งซื้อ Sale Order',
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: 'รายการสั่งซื้อ Sale Order',
            id: 8,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'pobuyeruiProfileRecordSales') {
        // console.log('pobuyerProfileRecordSales----->')
        this.keyChange = 10
        this.select = 3
        this.selectedItem = 6
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = true
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: 'จัดการรายการสั่งซื้อ Sale Order',
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: 'ประวัติรายการสั่งซื้อสินค้า Sale Order',
            id: 8,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'pobuyerprofilepointallshop') {
        // console.log('pobuyerProfileRecordSales----->')
        this.keyChange = 11
        this.select = 4
        this.selectedItem = 0
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        this.itemsAffiliate[0].active = false
        // this.itemsPoint[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: 'แต้มของฉัน',
            id: 8,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'chatall') {
        // console.log('pobuyerProfileRecordSales----->')
        this.keyChange = 12
        this.select = 5
        this.selectedItem = 0
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsSaleOrder[0].active = false
        // this.itemsPoint[0].active = false
        this.itemsChat[0].active = true
        this.itemsAffiliate[0].active = false
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuMyChat')}`,
            id: 9,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'consentAffiliate' || this.$router.currentRoute.name === 'showProductSellerJoinAffiliate' || this.$router.currentRoute.name === 'showShopSellerAffiliate') {
        // console.log('select2', this.select2)
        // console.log('selectedItem', this.selectedItem)
        this.keyChange = 13
        this.select2 = 0
        this.selectedItem = 9
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        if (this.$router.currentRoute.name === 'consentAffiliate') {
          this.itemsBreadcrumb = [
            {
              category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
              id: 0,
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
              id: 1,
              disabled: false,
              color: '#636363',
              href: '/showShopSellerAffiliate'
            },
            {
              category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
              id: 9,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        } else if (this.$router.currentRoute.name === 'showProductSellerJoinAffiliate' || this.$router.currentRoute.name === 'showShopSellerAffiliate') {
          this.keyChange = 14
          this.itemsBreadcrumb = [
            {
              category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
              id: 0,
              disabled: false,
              color: '#636363',
              href: '/'
            },
            {
              category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
              id: 1,
              disabled: false,
              color: '#636363',
              href: '/showShopSellerAffiliate'
            },
            {
              category_name: `${this.$t('MenuBuyer.menuShopSeller')}`,
              id: 9,
              disabled: true,
              color: '#3EC6B6',
              href: '/'
            }
          ]
        }
        // console.log('1', this.select2)
      } else if (this.$router.currentRoute.name === 'productAffiliate') {
        this.keyChange = 15
        this.select2 = 1
        this.selectedItem = 10
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuProductAffiliate')}`,
            id: 12,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
        // console.log(this.select2)
      } else if (this.$router.currentRoute.name === 'editPay') {
        this.keyChange = 16
        this.select2 = 2
        this.selectedItem = 11
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuEditPay')}`,
            id: 10,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
        // console.log('2', this.select2)
      } else if (this.$router.currentRoute.name === 'editSocail') {
        this.keyChange = 17
        this.select2 = 3
        this.selectedItem = 12
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuEditSocial')}`,
            id: 11,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'SharedBuyerAffiliate') {
        this.keyChange = 18
        this.select2 = 4
        this.selectedItem = 13
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuMyShared')}`,
            id: 12,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'ClickBuyerAffiliate') {
        this.keyChange = 19
        this.select2 = 5
        this.selectedItem = 14
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuClick')}`,
            id: 13,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'orderedBuyerAffilate') {
        this.keyChange = 20
        this.select2 = 6
        this.selectedItem = 15
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuOrderAffiliate')}`,
            id: 14,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      } else if (this.$router.currentRoute.name === 'withdrawAffiliate') {
        this.keyChange = 21
        this.select2 = 7
        this.selectedItem = 16
        this.items[0].active = false
        this.itemsOrder[0].active = false
        this.itemsChat[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsBreadcrumb = [
          {
            category_name: `${this.$t('MenuBuyer.breadcrumbHome')}`,
            id: 0,
            disabled: false,
            color: '#636363',
            href: '/'
          },
          {
            category_name: `${this.$t('MenuBuyer.titleAffiliate')}`,
            id: 1,
            disabled: false,
            color: '#636363',
            href: '/showShopSellerAffiliate'
          },
          {
            category_name: `${this.$t('MenuBuyer.menuWithdraw')}`,
            id: 15,
            disabled: true,
            color: '#3EC6B6',
            href: '/'
          }
        ]
      }
      // this.$forceUpdate()
    },
    closeMenu (val) {
      this.selectedItem = val
    },
    async getConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      this.isBuyer = res.isBuyer
      this.itemsAffiliate = []
      if (this.isBuyer === '0') {
        this.itemsAffiliate = [
          {
            action: 'mdi mdi-chart-line',
            active: false,
            keys: 4,
            items: [
              { key: 0, title: `${this.$t('MenuBuyer.titleAffiliate')}`, path: 'consentAffiliate' }
            ],
            title: `${this.$t('MenuBuyer.titleAffiliate')}`
          }
        ]
      } else {
        this.itemsAffiliate = [
          {
            action: 'mdi mdi-chart-line',
            active: false,
            keys: 4,
            items: [
              { key: 1, title: `${this.$t('MenuBuyer.menuShopSeller')}`, path: 'showShopSellerAffiliate' },
              { key: 2, title: `${this.$t('MenuBuyer.menuProductAffiliate')}`, path: 'productAffiliate' },
              { key: 3, title: `${this.$t('MenuBuyer.menuEditPay')}`, path: 'editPay' },
              { key: 4, title: `${this.$t('MenuBuyer.menuEditSocial')}`, path: 'editSocail' },
              { key: 5, title: `${this.$t('MenuBuyer.menuMyShared')}`, path: 'SharedBuyerAffiliate' },
              { key: 6, title: `${this.$t('MenuBuyer.menuClick')}`, path: 'ClickBuyerAffiliate' },
              { key: 7, title: `${this.$t('MenuBuyer.menuOrderAffiliate')}`, path: 'orderedBuyerAffilate' },
              { key: 8, title: `${this.$t('MenuBuyer.menuWithdraw')}`, path: 'withdrawAffiliate' }
            ],
            title: `${this.$t('MenuBuyer.titleAffiliate')}`
          }
        ]
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.backgroundPage{
  background-color: #F6F6F6;
}
.container {
  max-width: 100%;
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  white-space: nowrap;
  list-style-type: none;
  margin-bottom: 24px;
  /* padding: 8px 0px 8px 75px !important; */
}
.v-breadcrumbs__item  {
  color: #3EC6B6 !important;
}
.v-breadcrumbs li .v-icon {
  color: #3EC6B6 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
</style>
