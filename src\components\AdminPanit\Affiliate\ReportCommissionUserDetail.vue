<template>
  <v-container :style="MobileSize ? 'background-color: white' : ''">
    <v-row>
      <v-col class="d-flex align-center">
        <v-btn @click="backPagesMobile" icon v-if="MobileSize">
          <v-icon>mdi-chevron-left</v-icon>
        </v-btn>
        <v-btn @click="backPages" icon v-else>
          <v-icon>mdi-chevron-left</v-icon>
        </v-btn>
        <span style="font-size: 18px; font-weight: bold;">รายละเอียดค่าคอมมิชชันของผู้ใช้งาน Affiliate</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-card>
          <v-data-table
            :headers="ShopPerUserTableHearders"
            :items="userDetailTable"
            style="white-space: nowrap;"
          >
            <template v-slot:[`item.index`]="{index}">
              <span>{{index + 1}}</span>
            </template>
            <template v-slot:[`item.paid_datetime`]="{ item }">
              {{new Date(item.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      userDetailTable: [],
      headersUserTable: [
        { text: 'ลำดับ', value: 'index', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้าน', value: 'name_th', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะของร้าน', value: 'shop_status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชัน', value: 'total_commission', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      ShopPerUserTableHearders: [
        { text: 'ลำดับ', value: 'index', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้าน', value: 'name_th', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสคำสั่งซื้อ', value: 'order_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชัน', value: 'estimate_commission', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ชำระ', value: 'paid_datetime', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      userDetailBody: {
        user_id: null
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    // await this.fetchUserDetail()
    await this.reFetchUserDetail()
  },
  methods: {
    async fetchUserDetail () {
      var response = this.$store.state.ModuleAdminManage.UserDetailCommissionsAffiliate
      this.userDetailTable = response
    },
    async backPages () {
      this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
    },
    async backPagesMobile () {
      this.$router.push({ path: '/reportCommissionAffiliateAdminMobile' }).catch(() => {})
    },
    async reFetchUserDetail () {
      this.$store.commit('openLoader')
      this.userDetailBody = { user_id: Number(this.$route.query.id) }
      // this.userDetailBody.user_id = this.$router.query.id
      // var Id = this.$router.query.id
      await this.$store.dispatch('actionsUserComissionAffiliateTable', this.userDetailBody)
      var response = await this.$store.state.ModuleAdminManage.stateUserComissionAffiliateTable
      console.log(response.data.user_data[0].seller_shops_commission)
      this.userDetailTable = response.data.user_data[0].seller_shops_commission
      this.$store.commit('closeLoader')
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdminMobile' }).catch(() => {})
      } else {
        // localStorage.setItem('pathAdmin', 'reportCommissionAffiliateUserDetailAdmin')
        this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdmin' }).catch(() => {})
        // this.$router.push({ path: '/reportCommissionAffiliateUserDetailAdmin' }).catch(() => {})
      }
    }
  }
}
</script>
