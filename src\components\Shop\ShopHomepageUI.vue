<template>
  <v-col cols="12" :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : 'pa-0'">
    <!-- <v-col> -->
      <!-- <v-container :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : ''"> -->
        <v-breadcrumbs :items="RoleUser == 'sale_order' || RoleUser === 'sale_order_no_JV' ? itemsSale : itemsPage" class="breadcrumbsPadding " v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
          <template v-slot:divider>
            <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
          </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
              <span style="z-index:1;" :style="{ color: item.disabled === true ? '#27AB9C' : '#636363', 'font-size': '16px' }">{{ item.text
          }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <!-- </v-container> -->
    <!-- </v-col> -->
    <v-container :class="IpadProSize ? 'px-2' : IpadSize ? 'px-2' : MobileSize ? 'px-2' : ''">
      <v-col cols="12" md="12" :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : ''">
        <v-container :class="MobileSize || IpadSize || IpadProSize? 'pa-0': ''">
          <v-card
            max-height="100%"
            max-width="100%"
            elevation="0"
            :class="IpadSize? 'mt-6': 'mt-1'"
            v-if="dataShop !== ''"
          >
            <v-row no-gutters>
              <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 360px;': MobileSize ? 'position: relative; height: 200px;' :IpadProSize?'position: relative; height: 380px;': 'position: relative; height: 475px;'">
                <v-carousel
                  style="position: absolute; border-radius: 12px;"
                  cycle
                  :height="IpadSize ? '220' : IpadProSize? '290': MobileSize && bannerShop.length !== 0 ? '120': MobileSize && bannerShop.length === 0 ? '120' : '380'"
                  hide-delimiter-background
                  show-arrows-on-hover
                  :show-arrows="bannerShop.length > 1 ? true:false"
                  hide-delimiters
                >
                  <div v-if="bannerShop.length !== 0">
                    <v-carousel-item
                      eager
                      v-for="(slide, i) in bannerShop"
                      :key="i"
                    >
                    <div v-if="slide.type === 'banner'">
                        <v-img
                          loading="lazy"
                          :alt="'bannerShop-' + i"
                          :src="slide.path"
                          height="100%"
                          width="100%"
                          :max-height="IpadSize ? '220' : IpadProSize ? '290' :MobileSize ? '120' : '380'"
                          :max-width="IpadSize ? '100%': MobileSize ? '100%' : '1376'"
                          style="background-size: cover; background-position: center; background-repeat: no-repeat;"
                        ></v-img>
                      </div>
                      <div v-else-if="slide.type === 'vdo'">
                        <Media
                          :kind="'video'"
                          :isMuted="false"
                          :src="[slide.path]"
                          :autoplay="false"
                          :controls="true"
                          :loop="true"
                          @pause="handle"
                          :ref="'video_player'"
                          width="630"
                          height="400"
                        ></Media>
                      </div>
                      <div v-else-if="slide.type === 'image'">
                        <v-img
                          loading="lazy"
                          :src="slide.path"
                          height="100%"
                          width="100%"
                          :max-height="IpadSize ? '220' : IpadProSize ? '290' :MobileSize ? '120' : '380'"
                          :max-width="IpadSize ? '100%': MobileSize ? '100%' : '1376'"
                          contain
                        ></v-img>
                      </div>
                    </v-carousel-item>
                  </div>
                  <div v-else>
                    <!-- <v-carousel
                      :height=" IpadSize ? '290': MobileSize ? '120':'500'"
                    > -->
                      <v-carousel-item eager>
                        <div>
                          <v-img
                            src="@/assets/ImageINET-Marketplace/Shop/NoImgStore.png"
                            :height="IpadSize? '100%': '100%'"
                            width="100%"
                            contain
                          ></v-img>
                        </div>
                      </v-carousel-item>
                    <!-- </v-carousel> -->
                  </div>
                </v-carousel>
                <v-col :class=" IpadSize? 'pa-0':'pt-4 pr-0 pb-2'" v-if="RowUserData === 'purchaser' && statusShop && chackpartner && status_btn && contact" :style="MobileSize? 'position: absolute; margin-top: 20px; text-align-last: end;':IpadSize? 'position: absolute; margin-top: 150px; text-align-last: end;': 'position: absolute; margin-top: 212px; text-align-last: end;'">
                  <v-chip
                    style="height: 44px; width: 120px;"
                    v-if="chackpartner && status_btn && contact && !MobileSize"
                    class="pa-4 ChipWating"
                    color="#FCF0DA"
                    text-color="#FAAD14"
                    >
                    <v-img sizes="24" src="@/assets/ImageINET-Marketplace/Shop/iconShop/watingStatus.png"></v-img>
                    <span class="ml-2" style="font-size: 16px; font-weight: 600;">{{ $t('ShopPage.PendingApproval') }}</span>
                  </v-chip>
                  <v-chip
                    style="height: 8px; width: 70px;"
                    v-else-if="chackpartner && status_btn && contact && MobileSize"
                    class="pa-3 ChipWating"
                    color="#FCF0DA"
                    text-color="#FAAD14"
                    >
                    <v-img style="height: 14px; width: 14px;" src="@/assets/ImageINET-Marketplace/Shop/iconShop/watingStatus.png"></v-img>
                    <span class="ml-1" style="font-size: 10px; font-weight: 600;">{{ $t('ShopPage.PendingApproval') }}</span>
                  </v-chip>
                </v-col>
                <v-col :style="IpadSize? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                  <v-row no-gutters>
                    <v-col dense no-gutters :cols="MobileSize? 2: 3" :md="IpadProSize ? 4 : 3" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0 pr-7'" style="text-align: center;">
                      <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '70' : '244'" v-if="shop_logo === ''">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/Shop/Store2.png"
                          style="border: 10px solid #ffffff"
                        ></v-img>
                      </v-avatar>
                      <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '70' : '244'" v-else>
                        <v-img
                          :src="`${shop_logo}?=${time}`"
                          contain
                          style="border: 5px solid #ffffff"
                        ></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col :cols="MobileSize? 10: 9" :md="IpadProSize ? 8 : 9" :class="IpadSize? 'pb-0 mb-15':MobileSize ? '' : 'pt-16'">
                      <v-row dense no-gutters :justify="IpadSize? 'center': 'start'" v-if="!MobileSize" :class="IpadSize? 'pa-0 pt-11': 'pa-2 pl-0 pr-1 pt-16'">
                        <v-col cols="12" md="12" sm="12" xs="12" class="pt-4 mt-1"
                          v-if="
                            dataShop.shop_name_th !== '' &&
                            dataShop.shop_name_th !== null
                          "
                        >
                          <span v-if="dataShop.shop_name_en !== '' && dataShop.shop_name_en !== null">
                            <p :class="IpadSize? 'mb-0 ipadfont' :'mb-1 Deskfont'"
                              style=" color: #333333;"
                            >
                              {{ dataShop.shop_name_en }}
                          <br>
                            </p>
                            <p :class="IpadSize? 'mb-0 ipadfonts' :'mb-2'"
                              style="color: #333333;"
                            >
                              {{ dataShop.shop_name_th }}
                            </p>
                          </span>
                          <span v-else>
                            <p :class="IpadSize? 'mb-0 ipadfont' : 'mt-2 mb-6 Deskfont'"
                            style="color: #333333;"
                            >
                              {{ dataShop.shop_name_th }}
                              <br>
                            </p>
                          </span>
                          <v-row class="mt-3 mb-2">
                            <v-col cols="12" class="py-0 d-flex">
                                  <div class="d-flex">
                                    <v-btn
                                    color="primary"
                                    fab
                                    x-small
                                    dark
                                    @click="copyShortLink(dataShop.shop_id)"
                                    class="mr-2"
                                    style="margin-top: -2px; box-shadow: none;"
                                  >
                                    <v-icon>mdi-share</v-icon>
                                  </v-btn>
                                </div>
                                <div class="d-flex">
                                <!-- <v-avatar :size="IpadSize? '15': IpadProSize? '20': '28'"> -->
                                  <v-btn
                                  v-if="checkBot"
                                  color="#428bca"
                                  fab
                                  x-small
                                  dark
                                  @click="sentChat(dataShop)"
                                  class="mr-2"
                                  style="margin-top: -2px; box-shadow: none;"
                                >
                                  <v-icon>mdi-chat</v-icon>
                                </v-btn>
                                <!-- </v-avatar> -->
                                </div>
                                <div v-if="FacebookUrl !== ''" class="d-flex">
                                <!-- <v-avatar :size="IpadSize? '15': IpadProSize? '20': '28'"> -->
                                  <a :href="`${FacebookUrl}`" target="_blank">
                                    <v-btn
                                    color="#3b5998"
                                    fab
                                    x-small
                                    dark
                                    style="margin-top: -2px; box-shadow: none;">
                                    <v-img :class="IpadSize || IpadProSize? 'classFBLineSizeIpad':'classFBLineSizeDesk'"
                                      src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                                    ></v-img>
                                    </v-btn>
                                  </a>
                                <!-- </v-avatar> -->
                                </div>
                                <div class="d-flex mr-1" v-if="LineId !== ''" :style="IpadProSize || IpadSize ? 'margin-left:8px;' : 'margin-left:8px;'">
                                <!-- <v-avatar :size="IpadSize? '15': IpadProSize? '20': '28'"> -->
                                  <a :href="`${LineId}`" target="_blank">
                                    <v-btn
                                    color="#39cd00"
                                    fab
                                    x-small
                                    dark
                                    style="margin-top: -2px; box-shadow: none;">
                                      <v-img :class="IpadSize || IpadProSize? 'classFBLineSizeIpad':'classFBLineSizeDesk'"
                                      src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                                    ></v-img>
                                  </v-btn>
                                  </a>
                                <!-- </v-avatar> -->
                                </div>
                                <div class="d-flex ml-2"  style="border-left: solid 1px #333333;">
                                  <v-row no-gutters class="ml-1">
                                    <v-img style="width: 24px; height: 24px;" src="@/assets/ImageINET-Marketplace/Shop/iconShop/Star.png"></v-img>
                                    <span v-if="starShop !== 0" style="color: #333333;" class=" starFont ml-2">{{ $t('ShopPage.StoreRating') }} : {{Number(parseFloat(starShop)).toLocaleString(undefined, {minimumFractionDigits: 1})}} / 5.0</span>
                                    <span v-else style="color: #333333;" class=" starFont ml-2">{{ $t('ShopPage.StoreRating') }} : {{ $t('ShopPage.NoRatingYet') }} </span>
                                  </v-row>
                                </div>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" sm="12" xs="12">
                          <v-row v-if="!MobileSize">
                            <!-- <v-row
                              v-if="RowUserData === 'purchaser' && statusShop"
                              dense
                            >
                          {{ chackpartner }} {{ contact }} {{ statusOpenPartner }} {{ ParnertPerminsion }}
                              <v-btn small text color="#27AB9C" @click="gotoShopDetail()">
                                <v-icon small class="pr-1">mdi-chat</v-icon> แชท
                              </v-btn>
                              <v-divider v-if="chackpartner || statusOpenPartner && ParnertPerminsion" vertical class="ml-2 mr-2"></v-divider>
                              {{ !chackpartner }} {{ contact }} {{ statusOpenPartner }} {{ ParnertPerminsion }}
                              <v-btn
                                v-if="
                                  !chackpartner &&
                                  contact &&
                                  statusOpenPartner &&
                                  ParnertPerminsion
                                "
                                dense
                                dark
                                outlined
                                color="#27AB9C"
                                style="
                                  height: 28px;
                                  margin-left: 8px;
                                  margin-top: 5px;
                                "
                                @click="OpenModalPartner()"
                              >
                                ยื่นคำขอคู่ค้า
                              </v-btn>
                              <v-btn v-if="chackpartner && !status_btn && contact" class="ml-1" dense dark outlined
                                color="#27AB9C" style="height: 28px" @click="ModalCreateQuotation()">
                                <v-icon small>mdi-pencil</v-icon> {{ $t('ShopPage.CreateQuotation') }}
                              </v-btn>
                              <v-btn v-if="chackpartner && !status_btn && contact" class="ml-1" dense dark outlined
                                color="#27AB9C" style="height: 28px" @click="OpenModalSpecialPrice()">
                                <v-icon small>mdi-file-document</v-icon> ร้องขอราคาพิเศษ
                              </v-btn>
                              <v-chip
                                v-if="chackpartner && status_btn && contact"
                                small
                                class="ma-1"
                                color="#FCF0DA"
                                text-color="#FAAD14"
                                >รออนุมัติ
                                รออนุมัติ {{chackpartner}}{{status_btn}}{{contact}}
                              </v-chip>
                              <v-chip
                                v-if="!chackpartner && !contact"
                                small
                                class="ma-1"
                                color="#FCF0DA"
                                text-color="#FAAD14"
                              >
                                {{!chackpartner}} {{!contact}} {{statusOpenPartner}} {{ParnertPerminsion}}
                                ติดต่อร้านค้า(ยื่นคำขอคู่ค้า)
                              </v-chip>
                              <span
                                v-else
                                class="ml-2 ma-1"
                                style="
                                  color: #636363;
                                  font-size: 14px;
                                  font-weight: 400;
                                "
                              >
                                <a
                                  v-if="response_coupon !== 0"
                                  @click="openModalCoupons()"
                                >
                                  <U>คูปองส่วนลดจากร้านค้า</U></a
                                >
                              </span>
                              <v-btn small text color="#27AB9C" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-account-plus</v-icon> ติดตาม</v-btn>
                            </v-row> -->
                            <!-- <span
                              v-else
                              class="ml-3"
                              style="
                                color: #636363;
                                font-size: 14px;
                                font-weight: 400;
                              "
                            >
                              <a
                                v-if="response_coupon !== 0"
                                @click="openModalCoupons()"
                              >
                                <U>คูปองส่วนลดจากร้านค้า</U></a
                              >
                            </span> -->
                            <v-col cols="12" md="12" sm="12" :class="IpadSize? 'pt-5 pb-0 ' :IpadProSize? ' pt-7 pb-0': 'pt-4 pb-0'">
                              <p style="font-weight: bold; font-size: 18px" :class="IpadSize? 'pt-3 mb-0': 'pt-3 mb-0'">
                                {{ $t('ShopPage.AllProductsList') }}
                              </p>
                            </v-col>
                            <v-col cols="12" md="12" sm="12" class="px-1">
                              <v-row
                                :justify="MobileSize ? 'start' : 'space-around'"
                                dense
                              >
                                <v-card
                                  @click="GetAllProducts(AllProduct, 'all_product')"
                                  outlined
                                  class="v-card-store"
                                  :class="IpadSize ? 'pa-auto ma-1 v-card-storeIpad' : IpadProSize ? 'pa-auto ma-1 v-card-storeIpadPro': 'pa-auto ma-2 pr-1 v-card-storeDesk'"
                                >
                                  <v-card-text>
                                    <v-row justify="end">
                                      <v-col cols="5" md="5" :class="IpadSize? 'pa-1' : ''">
                                        <v-avatar :size="IpadSize || IpadProSize? '25': '50'">
                                          <v-img src="@/assets/ImageINET-Marketplace/Shop/AllProduct.png">
                                          </v-img>
                                        </v-avatar>
                                      </v-col>
                                      <v-col
                                        cols="7"
                                        md="7"
                                        :class="IpadSize ? 'pt-4' :IpadProSize? 'pt-5' : 'pt-6'"
                                      >
                                        <v-row justify="end">
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countAll }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countAll }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countAll }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countAll }}
                                          </p>
                                          <p
                                          v-else
                                            :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countAll }}
                                          </p>
                                        </v-row>
                                        <v-row justify="end">
                                          <p
                                            style="color: #636363; font-weight: 700"
                                            :style="IpadSize? 'font-size: 8px;' :IpadProSize ?'font-size: 10px;': 'font-size: 14px;'"
                                          >
                                            {{ $t('ShopPage.AllProducts') }}
                                          </p>
                                        </v-row>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                                <v-card
                                  @click="GetAllProducts(Recommended, 'recommended_product')"
                                  outlined
                                  class="v-card-store"
                                  :class="IpadSize ? 'pa-auto ma-1 v-card-storeIpad' : IpadProSize? 'pa-auto ma-1 v-card-storeIpadPro': 'pa-auto ma-2 pr-1 v-card-storeDesk'"
                                >
                                  <v-card-text>
                                    <v-row justify="end">
                                      <v-col cols="5" md="5" :class="IpadSize? 'pa-1' : ''">
                                        <v-avatar :size="IpadSize || IpadProSize? '25': '50'">
                                          <v-img src="@/assets/ImageINET-Marketplace/Shop/suggestion.png">
                                          </v-img>
                                        </v-avatar>
                                      </v-col>
                                      <v-col
                                        cols="7"
                                        md="7"
                                        :class="IpadSize ? 'pt-4' :IpadProSize? 'pt-5' : 'pt-6'"
                                      >
                                        <v-row justify="end">
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countRecommend }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countRecommend }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countRecommend }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countRecommend }}
                                          </p>
                                          <p
                                          v-else :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countRecommend }}
                                          </p>
                                        </v-row>
                                        <v-row justify="end">
                                          <p
                                            style="color: #636363; font-weight: 700"
                                            :style=" IpadSize ? 'font-size: 7px;'  :IpadProSize ?'font-size: 10px;' : 'font-size: 14px;'"
                                          >
                                            {{ $t('ShopPage.RecommendedPromotion') }}
                                          </p>
                                        </v-row>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                                <v-card
                                  @click="GetAllProducts(NewProduct, 'new_product')"
                                  outlined
                                  class="v-card-store"
                                  :class="IpadSize ? 'pa-auto ma-1 v-card-storeIpad' : IpadProSize? 'pa-auto ma-1 v-card-storeIpadPro': 'pa-auto ma-2 pr-1 v-card-storeDesk'"
                                >
                                  <v-card-text>
                                    <v-row justify="end">
                                      <v-col cols="5" md="5" :class="IpadSize? 'pa-1' : ''">
                                        <v-avatar :size="IpadSize || IpadProSize? '25': '50'">
                                          <v-img src="@/assets/ImageINET-Marketplace/Shop/new.png">
                                          </v-img>
                                        </v-avatar>
                                      </v-col>
                                      <v-col
                                        cols="7"
                                        md="7"
                                        :class="IpadSize ? 'pt-4' :IpadProSize? 'pt-5' : 'pt-6'"
                                      >
                                        <v-row justify="end">
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countNew }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countNew }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countNew }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countNew }}
                                          </p>
                                          <p
                                          v-else :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countNew }}
                                          </p>
                                        </v-row>
                                        <v-row justify="end">
                                          <p
                                            style="color: #636363; font-weight: 700"
                                            :style="IpadSize ? 'font-size: 8px;' :IpadProSize ?'font-size: 10px;': 'font-size: 14px;'"
                                          >
                                            {{ $t('ShopPage.NewArrival') }}
                                          </p>
                                        </v-row>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                                <v-card
                                  @click="GetAllProducts(SaleProduct, 'sale_product')"
                                  outlined
                                  class="v-card-store"
                                  :class="IpadSize ? 'pa-auto ma-1 v-card-storeIpad' : IpadProSize? 'pa-auto ma-1 v-card-storeIpadPro': 'pa-auto ma-2 pr-1 v-card-storeDesk'"
                                >
                                  <v-card-text>
                                    <v-row justify="end">
                                      <v-col cols="5" md="5" :class="IpadSize? 'pa-1' : ''">
                                        <v-avatar :size="IpadSize || IpadProSize? '25': '50'">
                                          <v-img src="@/assets/ImageINET-Marketplace/Shop/discountProduct.png">
                                          </v-img>
                                        </v-avatar>
                                      </v-col>
                                      <v-col
                                        cols="7"
                                        md="7"
                                        :class="IpadSize ? 'pt-4' :IpadProSize? 'pt-5' : 'pt-6'"
                                      >
                                        <v-row justify="end">
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countSale }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countSale }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countSale }}
                                          </p>
                                          <p :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countSale }}
                                          </p>
                                          <p
                                          v-else :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            style="color: #27ab9c; font-weight: bold"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countSale }}
                                          </p>
                                        </v-row>
                                        <v-row justify="end">
                                          <p
                                            style="color: #636363; font-weight: 700"
                                            :style="IpadSize ? 'font-size: 8px;' :IpadProSize ?'font-size: 10px;': 'font-size: 14px;'"
                                          >
                                            {{ $t('ShopPage.DiscountedProducts') }}
                                          </p>
                                        </v-row>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                                <v-card
                                  @click="GetAllProducts(BestSeller, 'best_seller')"
                                  outlined
                                  class="v-card-store"
                                  :class="IpadSize ? 'pa-auto ma-1 v-card-storeIpad' : IpadProSize? 'pa-auto ma-1 v-card-storeIpadPro': 'pa-auto ma-2 pr-1 v-card-storeDesk'"
                                >
                                  <v-card-text>
                                    <v-row justify="end">
                                      <v-col cols="5" md="5" :class="IpadSize? 'pa-1' : ''">
                                        <v-avatar :size="IpadSize || IpadProSize? '25': '50'">
                                          <v-img src="@/assets/ImageINET-Marketplace/Shop/bestSeller.png">
                                          </v-img>
                                        </v-avatar>
                                      </v-col>
                                      <v-col
                                        cols="7"
                                        md="7"
                                        :class="IpadSize ? 'pt-4' :IpadProSize? 'pt-5' : 'pt-6'"
                                      >
                                        <v-row justify="end">
                                          <p v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                            style="color: #27ab9c; font-weight: bold"
                                            :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countBestSeller }}
                                          </p>
                                          <p
                                          v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                          {{ countBestSeller }}
                                          </p>
                                          <p
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countBestSeller }}
                                          </p>
                                          <p
                                          v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                            style="color: #27ab9c; font-weight: bold"
                                            :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countBestSeller }}
                                          </p>
                                          <p
                                          v-else
                                            style="color: #27ab9c; font-weight: bold"
                                            :class="IpadProSize || IpadSize? 'ipadProShophomepage' : ''"
                                            :style="
                                              IpadSize ? 'font-size: 16px;' : 'font-size: 28px;'
                                            "
                                          >
                                            {{ countBestSeller }}
                                          </p>
                                        </v-row>
                                        <v-row justify="end">
                                          <p
                                            style="color: #636363; font-weight: 700"
                                            :style="IpadSize ? 'font-size: 8px;' :IpadProSize ?'font-size: 10px;' : 'font-size: 14px;'"
                                          >
                                            {{ $t('ShopPage.BestSellers') }}
                                          </p>
                                        </v-row>
                                      </v-col>
                                    </v-row>
                                  </v-card-text>
                                </v-card>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-row v-else>
                          </v-row>
                        </v-col>
                      </v-row>
                      <!-- mobile name shop -->
                      <v-row dense no-gutters justify="center" v-if="MobileSize" class="mt-1">
                        <v-col
                          cols="12"
                          md="12"
                          sm="12"
                          xs="12"
                          :class="dataShop.shop_name_en !== null ? 'pl-8 pt-8' : 'pl-8 pt-2'"
                          v-if="
                            dataShop.shop_name_en !== '' &&
                            dataShop.shop_name_en !== null
                          "
                        >
                          <p
                            class="mb-1"
                            style="
                              color: #333333;
                              font-weight: bold;
                              font-size: 10px;
                              height: 10px;
                              text-transform: uppercase;
                            "
                          >
                            {{ dataShop.shop_name_en }}
                          </p>
                        </v-col>
                        <v-col
                          :class="dataShop.shop_name_th !== null && dataShop.shop_name_en !== null ? 'pl-8':'pl-8 pt-8'"
                          col="12"
                          sm="12"
                          v-if="
                            dataShop.shop_name_th !== '' &&
                            dataShop.shop_name_th !== null
                          "
                        >
                          <p
                            class="mb-1"
                            style="
                              color: #333333;
                              font-weight: bold;
                              font-size: 10px;
                              height: 10px;
                              text-transform: uppercase;
                            "
                          >
                            {{ dataShop.shop_name_th }}
                          </p>
                          <v-col cols="12" :class="dataShop.shop_name_en === '' || dataShop.shop_name_en === null ? 'pl-3 pa-0 pt-2': 'pl-3 pa-0 pt-2'">
                            <v-row no-gutters class="mt-2 mb-2">
                              <v-col cols="12" class="pa-0 mb-3" style="display: flex; flex-direction: row; align-items: center; ">
                                <!-- <div class="d-flex"> -->
                                <!-- <v-avatar :size="IpadSize? '15': IpadProSize? '20': '28'"> -->
                                    <v-btn
                                      color="primary"
                                      fab
                                      height="16px"
                                      width="16px"
                                      dark
                                      @click="copyShortLink(dataShop.shop_id)"
                                      class="mr-1"
                                      style="box-shadow: none;"
                                    >
                                      <v-icon size="10">mdi-share</v-icon>
                                    </v-btn>
                                      <v-btn
                                      v-if="checkBot"
                                      color="#428bca"
                                      fab
                                      height="16px"
                                      width="16px"
                                      dark
                                      @click="sentChat(dataShop)"
                                      class="mr-1"
                                      style="box-shadow: none;"
                                    >
                                      <v-icon size="10">mdi-chat</v-icon>
                                    </v-btn>
                                    <!-- </v-avatar> -->
                                    <!-- </div> -->
                                    <v-avatar size="16" class="mr-1" v-if="FacebookUrl !== ''" style="display: flex; justify-content: center; align-items: center;">
                                      <v-img
                                        src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                                      ></v-img>
                                    </v-avatar>
                                    <v-avatar size="16" v-if="LineId !== ''"  style="display: flex; justify-content: center; align-items: center;">
                                      <v-img
                                        src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                                      ></v-img>
                                    </v-avatar>
                                  <div class="d-flex ml-1 mt-2" style="border-left: solid 1px #C4C4C4;">
                                    <!-- <v-row no-gutters> -->
                                      <v-img class="ml-1" style="width: 16px; height: 16px;" src="@/assets/ImageINET-Marketplace/Shop/iconShop/Star.png"></v-img>
                                      <span style="color: #333333; font-size: 10px;margin-top: 2px !important;" class=" ml-1 mb-2"> {{ $t('ShopPage.StoreRating') }} : {{starShop !== 0? Number(parseFloat(starShop)).toLocaleString(undefined, {minimumFractionDigits: 1}) + ' / 5.0': this.$t('ShopPage.NoRatingYet') }}</span>
                                      <!-- <span v-else style="color: #333333; font-size: 10px;" class="ml-1 mb-2">{{ $t('ShopPage.StoreRating') }} : ยังไม่มีคะแนน </span> -->
                                    <!-- </v-row> -->
                                  </div>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-row v-if="MobileSize" :class="dataShop.shop_description !== null ? 'pt-1': 'pt-1'" style="justify-content: center;">
                      <v-col cols="12" class="pa-0 pt-1 pb-2 my-1">
                        <p class="mb-0" style="font-weight: bold; font-size: 14px">
                          {{ $t('ShopPage.AllProductsList') }}
                        </p>
                      </v-col>
                      <v-col cols="12" align="center">
                        <v-row
                          justify="start"
                        >
                          <v-card
                            @click="GetAllProducts(AllProduct, 'all_product')"
                            outlined
                            class="v-card-store v-card-storeMobile"
                          >
                            <v-card-text class="pa-1">
                              <v-row justify="center" >
                                <v-col cols="12" class="textCardMobile">
                                  <v-row no-gutters>
                                    <v-col cols="4"  align="center" style="padding-top: 6px !important;">
                                      <v-avatar size="32">
                                        <v-img src="@/assets/ImageINET-Marketplace/Shop/AllProduct.png">
                                        </v-img>
                                      </v-avatar>
                                    </v-col>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="end"> -->
                                    <v-col cols="8" align="end" class="textCardNumberMobile">
                                      <p class="ma-0"
                                      v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countAll }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countAll }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countAll }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countAll }}
                                      </p>
                                      <p class="mb-0"
                                      v-else
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countAll }}
                                      </p>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="end"> -->
                                      <span>
                                        {{ $t('ShopPage.AllProducts') }}
                                      </span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <v-card
                            @click="GetAllProducts(Recommended, 'recommended_product')"
                            outlined
                            class="v-card-store v-card-storeMobile "
                          >
                            <v-card-text class="pa-1">
                              <v-row justify="center">
                                <v-col cols="12" class="textCardMobile">
                                  <v-row no-gutters>
                                    <v-col cols="4" align="center" style="padding-top: 6px !important;">
                                      <v-avatar size="32">
                                        <v-img src="@/assets/ImageINET-Marketplace/Shop/suggestion.png">
                                        </v-img>
                                      </v-avatar>
                                    </v-col>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="center"> -->
                                    <v-col cols="8" align="end" class="textCardNumberMobile">
                                      <p class="mb-0"
                                      v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countRecommend }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countRecommend }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countRecommend }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countRecommend }}
                                      </p>
                                      <p class="mb-0"
                                      v-else
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countRecommend }}
                                      </p>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="end"> -->
                                      <span>
                                        {{ $t('ShopPage.RecommendedPromotion') }}
                                      </span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <v-card
                            @click="GetAllProducts(NewProduct, 'new_product')"
                            outlined
                            class="v-card-store v-card-storeMobile "
                          >
                            <v-card-text class="pa-1">
                              <v-row justify="center">
                                <v-col cols="12" class="textCardMobile">
                                  <v-row no-gutters>
                                    <v-col cols="4" align="center" style="padding-top: 6px !important;">
                                      <v-avatar size="32">
                                        <v-img src="@/assets/ImageINET-Marketplace/Shop/new.png">
                                        </v-img>
                                      </v-avatar>
                                    </v-col>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="center"> -->
                                    <v-col cols="8" align="end" class="textCardNumberMobile">
                                      <p class="mb-0"
                                      v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countNew }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countNew }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countNew }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countNew }}
                                      </p>
                                      <p class="mb-0"
                                      v-else
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countNew }}
                                      </p>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="end"> -->
                                      <span>
                                        {{ $t('ShopPage.NewArrival') }}
                                    </span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <v-card
                            @click="GetAllProducts(SaleProduct, 'sale_product')"
                            outlined
                            class="v-card-store v-card-storeMobile "
                          >
                            <v-card-text class="pa-1">
                              <v-row align-content="center">
                                <v-col cols="12" class="textCardMobile">
                                  <v-row no-gutters>
                                    <v-col cols="4" align="center" style="padding-top: 6px !important;">
                                      <v-avatar size="32">
                                        <v-img src="@/assets/ImageINET-Marketplace/Shop/discountProduct.png">
                                        </v-img>
                                      </v-avatar>
                                    </v-col>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="center"> -->
                                    <v-col cols="8" align="end" class="textCardNumberMobile">
                                      <p class="mb-0"
                                      v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{countSale }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{countSale }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{countSale }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{countSale }}
                                      </p>
                                      <p class="mb-0"
                                      v-else
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{countSale }}
                                      </p>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="end"> -->
                                      <span>
                                        {{ $t('ShopPage.DiscountedProducts') }}
                                      </span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <v-card
                            @click="GetAllProducts(BestSeller, 'best_seller')"
                            outlined
                            class="v-card-store v-card-storeMobile "
                          >
                            <v-card-text class="pa-1">
                              <v-row align-content="center">
                                <v-col cols="12" class="textCardMobile">
                                  <v-row no-gutters>
                                    <v-col cols="4" align="center" style="padding-top: 6px !important;">
                                      <v-avatar size="32">
                                        <v-img src="@/assets/ImageINET-Marketplace/Shop/bestSeller.png">
                                        </v-img>
                                      </v-avatar>
                                    </v-col>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="center"> -->
                                    <v-col cols="8" align="end" class="textCardNumberMobile">
                                      <p class="mb-0"
                                      v-if="this.statusShop === true & this.status_btn === false & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === false"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countBestSeller }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === false & this.chackpartner === false & this.contact === false & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countBestSeller }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === false & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countBestSeller }}
                                      </p>
                                      <p class="mb-0"
                                      v-else-if="this.statusShop === true & this.status_btn === true & this.chackpartner === true & this.contact === true & this.statusOpenPartner === true & this.ParnertPerminsion === true"
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countBestSeller }}
                                      </p>
                                      <p class="mb-0"
                                      v-else
                                        style="color: #27ab9c; font-weight: bold;"
                                      >
                                        {{ countBestSeller }}
                                      </p>
                                  <!-- </v-row>
                                  <v-row no-gutters justify="end"> -->
                                      <span>
                                        {{ $t('ShopPage.BestSellers') }}
                                      </span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-row>
                      </v-col>
                    </v-row>
                    <!-- <v-col cols="12" class="mt-4" v-if="MobileSize">
                      <v-row v-if="RowUserData === 'purchaser' && statusShop" dense>
                        <v-btn
                          v-if="
                            !chackpartner &&
                            contact &&
                            statusOpenPartner &&
                            ParnertPerminsion
                          "
                          dense
                          dark
                          outlined
                          color="#27AB9C"
                          style="height: 28px; margin-left: 3px; font-size: 12px; padding-left:3px; padding-right:3px;"
                          @click="OpenModalPartner()"
                        >
                          ยื่นคำขอคู่ค้า
                        </v-btn>
                        <v-btn v-if="chackpartner && !status_btn && contact" class="ml-1" dense dark outlined
                          color="#27AB9C" style="height: 28px" @click="ModalCreateQuotation()">
                          <v-icon small>mdi-pencil</v-icon>{{ $t('ShopPage.CreateQuotation') }}
                        </v-btn>
                        <v-btn v-if="chackpartner && !status_btn && contact" class="ml-1" dense dark outlined
                          color="#27AB9C" style="height: 28px" @click="OpenModalSpecialPrice()">
                          <v-icon small>mdi-file-document</v-icon> ร้องขอราคาพิเศษ
                        </v-btn>
                        <v-chip
                          v-if="chackpartner && status_btn && contact"
                          small
                          class="ma-1"
                          color="#FCF0DA"
                          text-color="#FAAD14"
                          >รออนุมัติ
                        </v-chip>
                        <v-chip
                          v-if="!chackpartner && !contact"
                          small
                          class="ma-1"
                          color="#FCF0DA"
                          text-color="#FAAD14"
                        >
                          ติดต่อร้านค้า(ยื่นคำขอคู่ค้า)
                        </v-chip>
                        <span
                          v-else
                          class="ml-2 ma-1 mt-2"
                          style="color: #636363; font-size: 14px; font-weight: 400"
                        >
                          <a
                            v-if="response_coupon !== 0"
                            @click="openModalCoupons()"
                          >
                            <U>คูปองส่วนลดจากร้านค้า</U></a
                          >
                        </span>
                      </v-row>
                      <span
                        v-else
                        class="ml-1 ma-1"
                        style="color: #636363; font-size: 14px; font-weight: 400"
                      >
                        <a v-if="response_coupon !== 0" @click="openModalCoupons()">
                          <U>คูปองส่วนลดจากร้านค้า</U></a
                        >
                      </span>
                    </v-col> -->
                  </v-row>
                </v-col>
              </v-col>
              <v-col cols="12" md="12" sm="12" :class="MobileSize? 'pt-0':'mb-0 pt-15'">
                <v-row
                  dense
                  :class="
                    MobileSize ? 'mt-16' : IpadProSize || IpadSize ? 'mt-0' : 'mt-16 pt-8'
                  "
                >
                  <v-col
                    cols="12"
                    md="12"
                    :class="IpadSize ? 'pl-8 pt-0':IpadProSize? 'pl-5 pt-16':MobileSize? '':'pt-16 pl-14'"
                    v-if="dataShop.shop_description !== null"
                  >
                    <!-- <v-avatar size="28"> -->
                    <v-row no-gutters :class="dataShop.shop_description !== null && MobileSize ? 'pt-16 mt-16' :dataShop.shop_description !== null && IpadSize? 'pt-6':dataShop.shop_description !== null && IpadProSize? 'pt-16':''">
                      <div :class="MobileSize? 'd-flex pt-2': 'd-flex'">
                        <v-img :width="IpadSize? '18': MobileSize ? '18' : '24'" :height="IpadSize? '18': MobileSize ? '18' : '24'" src="@/assets/ImageINET-Marketplace/Shop/shop1.png">
                        </v-img>
                        <!-- </v-avatar> -->
                        <span class="pl-2" :style="IpadSize ? 'font-weight: bold; font-size: 15px':MobileSize ? 'font-weight: bold; font-size: 14px' : 'font-weight: bold; font-size: 20px'">{{ $t('ShopPage.AboutStore') }}</span><br>
                      </div>
                    </v-row>
                    <div :class="MobileSize? 'py-4': 'pt-3'">
                      <span :style="IpadSize ? 'font-size: 13px;':MobileSize ? 'font-size: 14px' : 'font-size: 18px;'" v-html="dataShop.shop_description"></span>
                    </div>
                    <!-- <p  style=" font-size: 13px;margin-Top:-10px">{{ dataShop.shop_description }}</p> -->
                    <!-- <v-textarea
                      style="font-size: 18px;"
                      auto-grow
                      rows="2"
                      hide-details
                      readonly
                      :value="dataShop.shop_description"
                      rounded
                    ></v-textarea> -->
                    <!-- <span style=" font-size: 13px;margin-Top:-10px" v-html="dataShop.shop_description"></span> -->
                    <!-- <ckeditor :editor="editor" :config="editorConfig" v-model="dataShop.shop_description" @ready="onReady"></ckeditor> -->
                  </v-col>
                  <!-- <v-col>
                    <p  style=" font-size: 13px;">{{ dataShop.shop_description }}</p>
                  </v-col> -->
                  <!-- <v-col cols="12" md="12" class="pt-4">
                    <p style="font-weight: bold; font-size: 13px">
                      {{ $t('ShopPage.AllProductsList') }}
                    </p>
                  </v-col> -->
                </v-row>
              </v-col>
            </v-row>
          </v-card>
          <v-container v-if="RowUserData === 'purchaser' && !chackpartner && !MobileSize" :class="MobileSize || IpadSize ? 'pa-0' : ''">
            <a-row
              type="flex"
              justify="center"
              style="margin-top: 10%; margin-bottom: 10%"
              v-if="
                AllProduct.length === 0 &&
                NewProduct.length === 0 &&
                BestSeller.length === 0 &&
                Recommended.length === 0 &&
                SaleProduct.length === 0 &&
                NormalProduct.length === 0 &&
                dataShop !== '' && !cancelPartner
              "
            >
              <template>
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '40px' }"
                >
                  <h1 slot="description"
                  style="
                    color: #636363;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                  ">
                    {{ $t('ShopPage.RegisteredOnly') }}
                  </h1>
                  <br />
                  <h1 slot="description"
                  style="
                    color: #9A9A9A;
                    font-size: 16px;
                    line-height: 22.4px;
                    font-weight: 400;">
                    {{ $t('ShopPage.ClickIfInterested') }}
                    <a @click="OpenModalPartner()"
                    style="
                    color: #1B5DD6;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                    text-decoration: underline;">{{ $t('ShopPage.RegisterAsPartner') }}</a>
                  </h1>
                  <br />
                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              </template>
            </a-row>
            <a-row
              type="flex"
              justify="center"
              style="margin-top: 10%; margin-bottom: 10%"
              v-else-if="
                AllProduct.length === 0 &&
                NewProduct.length === 0 &&
                BestSeller.length === 0 &&
                Recommended.length === 0 &&
                SaleProduct.length === 0 &&
                NormalProduct.length === 0 &&
                dataShop === ''&& !cancelPartner
              "
            >
              <template>
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '40px' }"
                >
                  <h1 slot="description" style="font-weight: bold">
                    {{ $t('ShopPage.StoreNotFound') }}
                  </h1>
                  <h3 slot="description" style="font-weight: bold">
                    {{ $t('ShopPage.PleaseCheckStore') }}
                  </h3>
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              </template>
            </a-row>
            <a-row
              type="flex"
              justify="center"
              style="margin-top: 10%; margin-bottom: 10%"
              v-else
            >
              <template>
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '40px' }"
                >
                  <h1 slot="description"
                  style="
                    color: #636363;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                  ">
                    {{ $t('ShopPage.ContactStore') }}
                  </h1>
                  <br />
                  <h1 slot="description"
                  style="
                    color: #9A9A9A;
                    font-size: 16px;
                    line-height: 22.4px;
                    font-weight: 400;">{{ $t('ShopPage.OrContactSupport1') }}
                    <span
                    style="
                    color: #1B5DD6;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                    text-decoration: underline;">{{this.dataShop.shop_phone}}</span>
                  </h1>
                  <br />
                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              </template>
            </a-row>
          </v-container>
          <v-container v-else-if="RowUserData === 'purchaser' && !chackpartner && MobileSize" :class="MobileSize || IpadSize ? 'pa-0' : ''">
            <a-row
              class="pt-16 mt-16"
              type="flex"
              justify="center"
              style="margin-top: 10%; margin-bottom: 10%"
              v-if="
                AllProduct.length === 0 &&
                NewProduct.length === 0 &&
                BestSeller.length === 0 &&
                Recommended.length === 0 &&
                SaleProduct.length === 0 &&
                NormalProduct.length === 0 &&
                dataShop !== '' && !cancelPartner
              "
            >
              <template>
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '40px' }"
                >
                  <h1 slot="description"
                  style="
                    color: #636363;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                  ">
                    {{ $t('ShopPage.RegisteredOnly') }}
                  </h1>
                  <br />
                  <h1 slot="description"
                  style="
                    color: #9A9A9A;
                    font-size: 16px;
                    line-height: 22.4px;
                    font-weight: 400;">
                    {{ $t('ShopPage.ClickIfInterested') }}
                    <a @click="OpenModalPartner()"
                    style="
                    color: #1B5DD6;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                    text-decoration: underline;">{{ $t('ShopPage.RegisterAsPartner') }}</a>
                  </h1>
                  <br />
                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              </template>
            </a-row>
            <a-row
              type="flex"
              justify="center"
              style="margin-top: 10%; margin-bottom: 10%"
              v-else-if="
                AllProduct.length === 0 &&
                NewProduct.length === 0 &&
                BestSeller.length === 0 &&
                Recommended.length === 0 &&
                SaleProduct.length === 0 &&
                NormalProduct.length === 0 &&
                dataShop === '' && !cancelPartner
              "
            >
              <template>
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '40px' }"
                >
                  <h1 slot="description" style="font-weight: bold">
                    {{ $t('ShopPage.StoreNotFound') }}
                  </h1>
                  <h3 slot="description" style="font-weight: bold">
                    {{ $t('ShopPage.PleaseCheckStore') }}
                  </h3>
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              </template>
            </a-row>
            <a-row
              class="pt-16 mt-16"
              type="flex"
              justify="center"
              style="margin-top: 10%; margin-bottom: 10%"
              v-else
            >
              <template>
                <a-empty
                  :image="require('@/assets/Not_Result.png')"
                  :image-style="{ height: '150px', marginBottom: '40px' }"
                >
                  <h1 slot="description"
                  style="
                    color: #636363;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                  ">
                    {{ $t('ShopPage.ContactStore') }}
                  </h1>
                  <br />
                  <h1 slot="description"
                  style="
                    color: #9A9A9A;
                    font-size: 16px;
                    line-height: 22.4px;
                    font-weight: 400;">
                    {{ $t('ShopPage.OrContactSupport1') }}
                    <span
                    style="
                    color: #1B5DD6;
                    font-size: 18px;
                    line-height: 25.2px;
                    font-weight: 600;
                    text-decoration: underline;">{{this.dataShop.shop_phone}}</span>
                  </h1>
                  <br />
                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                </a-empty>
              </template>
            </a-row>
          </v-container>
          <a-row
            v-else-if="
              AllProduct.length === 0 &&
              NewProduct.length === 0 &&
              BestSeller.length === 0 &&
              Recommended.length === 0 &&
              SaleProduct.length === 0 &&
              NormalProduct.length === 0 &&
              status_btn === false &&
              dataShop !== ''
            "
            :class="MobileSize? 'pt-16 mt-16':''"
            type="flex"
            justify="center"
            style="margin-top: 12%; margin-bottom: 10%"
          >
            <template>
              <a-empty
                :image="require('@/assets/Not_Result.png')"
                :image-style="{ height: '150px', marginBottom: '40px' }"
              >
                <h1 slot="description"
                style="
                  color: #636363;
                  font-size: 18px;
                  line-height: 25.2px;
                  font-weight: 600;
                ">{{ $t('ShopPage.NoProductYet') }}
                </h1>
                <br />
                <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
              </a-empty>
            </template>
          </a-row>
          <a-row
            v-else-if="
              AllProduct.length === 0 &&
              NewProduct.length === 0 &&
              BestSeller.length === 0 &&
              Recommended.length === 0 &&
              SaleProduct.length === 0 &&
              NormalProduct.length === 0 &&
              status_btn === true &&
              dataShop !== ''
            "
            type="flex"
            justify="center"
            :style=" MobileSize? 'margin-top: 30%; margin-bottom: 10%' : IpadSize ? 'margin-top: 20%; margin-bottom: 10%': ' margin-top: 10%; margin-bottom: 10%'"
          >
            <template>
              <a-empty
                :image="require('@/assets/ImageINET-Marketplace/Shop/iconShop/watingToBePartner.png')"
                :image-style="{ height: '150px', marginBottom: '40px' }"
              >
                <h1 slot="description"
                style="
                  color: #636363;
                  font-size: 18px;
                  line-height: 25.2px;
                  font-weight: 600;
                ">{{ $t('ShopPage.RequestSubmitted') }}
                </h1>
                <br />
                <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
              </a-empty>
            </template>
          </a-row>
          <a-row
            type="flex"
            justify="center"
            style="margin-top: 10%; margin-bottom: 10%"
            v-else-if="
              AllProduct.length === 0 &&
              NewProduct.length === 0 &&
              BestSeller.length === 0 &&
              Recommended.length === 0 &&
              SaleProduct.length === 0 &&
              NormalProduct.length === 0 &&
              dataShop === ''
            "
          >
            <template>
              <a-empty
                :image="require('@/assets/Not_Result.png')"
                :image-style="{ height: '150px', marginBottom: '40px' }"
              >
                <h1 slot="description" style="font-weight: bold">
                  {{ $t('ShopPage.StoreNotFound') }}
                </h1>
                <h3 slot="description" style="font-weight: bold">
                  {{ $t('ShopPage.PleaseCheckStore') }}
                </h3>
                <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
              </a-empty>
            </template>
          </a-row>
          <v-container v-else :class="MobileSize && dataShop.shop_description === null ? 'pa-0 pt-16 mt-12': MobileSize ? 'pa-0': IpadSize? 'mt-5': dataShop.shop_description === null && IpadProSize ? 'mt-16 pt-16 ' : 'mt-5 px-0'">
            <!-- <v-tabs
              color="#27AB9C"
            > -->
            <v-col cols="12" align="end" v-if="showCustomer === true && RoleUser === 'sale_order_no_JV'">
              <v-chip color="#4499E2" class="chipcus mx-1" outlined >{{ $t('ShopPage.ExternalOrder') }}</v-chip>
              <v-chip v-if="cusCode !== 0 && cusCode !== null" color="#4499E2" class="chipcus mx-1" outlined >{{ $t('ShopPage.CustomerCode') }}: {{cusCode}}</v-chip>
              <v-chip class="chipcus mx-1" :class="MobileSize? 'mt-0':''" color="#4499E2" text-color="white" @click="SaleVendor === true ? chooseTypeCus('vendor'):dialogChooseTypeCustomer = true">{{ $t('ShopPage.SelectCustomerInfo') }}</v-chip>
            </v-col>
            <v-col cols="12" align="end" v-else-if="RoleUser === 'sale_order'">
              <v-chip color="#4499E2" class="chipcus mx-1" outlined >{{ $t('ShopPage.InternalOrder') }}</v-chip>
            </v-col>
            <v-row :class="IpadProSize ? 'px-0' : IpadSize ? 'px-0' : MobileSize ? 'px-2' : ''">
              <v-col cols="12" :class="IpadProSize ? 'px-0' : IpadSize ? 'px-0' : MobileSize ? 'px-0' : ''">
              <a-tabs v-model="PageTab" @change="changePageProduct" class="colorTap mt-2 mb-2">
              <!-- <v-tab v-for="(item, index) in itemsDescription" :key="index">
                <span style="padding-top: 0.5em; padding-left: 10px; font-weight: bold; font-size: 20px;">{{ item }}</span>
              </v-tab> -->
              <a-tab-pane  :key="0" class="mt-6"><span slot="tab" :style="MobileSize? 'font-size: 15px;': 'padding-top: 0.5em; padding-left: 10px; font-weight: bold; font-size: 20px;'">{{ $t('ShopPage.Title') }}</span>
                <div v-if="newsShop.length !== 0">
                  <v-col class="px-0">
                    <v-carousel
                      style="border-radius: 12px;"
                      cycle
                      :height="IpadSize ? '220' : IpadProSize ? '290' :MobileSize ? '120' : '380'"
                      :width="IpadSize ? '100%': MobileSize ? '100%' : '1376'"
                      hide-delimiter-background
                      :show-arrows="false"
                      hide-delimiters>
                      <v-carousel-item v-for="(slide, i) in newsShop" :key="i">
                        <div v-if="slide.type === 'news'">
                          <v-img
                            loading="lazy"
                            :alt="'bannerAdvert-' + i"
                            :src="slide.path"
                            height="100%"
                            width="100%"
                            :max-height="IpadSize ? '220' : IpadProSize ? '290' :MobileSize ? '120' : '380'"
                            :max-width="IpadSize ? '100%': MobileSize ? '100%' : '1376'"
                            style="background-size: cover; background-position: center; background-repeat: no-repeat;"
                          ></v-img>
                      </div>
                      </v-carousel-item>
                    </v-carousel>
                  </v-col>
                </div>
                <!-- <v-col v-if="!MobileSize && !IpadSize">
                  <div style="position: relative">
                    <span style="font-size: 23px; color: #27AB9C; background-color: white; margin-left: 50px; margin-top:20px; position: absolute; z-index:3"><b>โค้ดส่วนลดจากร้านค้า</b></span>
                    <div style="height: 20px;" class="d-flex justify-center">
                      <v-sheet
                        class="mx-auto"
                        :max-width="IpadProSize ? '95vw' : '1220px'"
                        style="
                          background-color: transparent;
                          position: absolute;
                          z-index: 3;
                          margin-top: 60px;
                        "
                      >
                        <v-slide-group
                          class=""
                          active-class="success"
                          show-arrows
                          center-active
                        >
                          <template v-slot:next>
                            <v-icon color="#27AB9C" large>mdi-chevron-right-circle</v-icon>
                          </template>
                          <template v-slot:prev>
                            <v-icon color="#27AB9C" large>mdi-chevron-left-circle</v-icon>
                          </template>
                          <v-slide-item v-for="(Conpon, i) in dataConpon" :key="i" class="ma-2">
                              <v-img max-height="12vw" max-width="18vw" contain src="@/assets/ConponNGC/shopConpon/backgroundConpon.png">
                                <v-col cols="12" class="pa-2">
                                  <v-row no-gutters>
                                    <v-col cols="6" md="6" align="start" class="pl-6">
                                      <span style="color: #27AB9C; font-size: 14px; font-weight: 600;"> {{Conpon.startText}}</span><br>
                                      <span> {{ $t('ShopPage.Minimum') }} {{Conpon.limitPrice}} บาท</span><br>
                                      <span class="mb-4" style="font-size: 10px;"> {{ $t('ShopPage.ValidUntil') }} {{Conpon.limitTime}}</span><br>
                                    </v-col>
                                    <v-col cols="6" md="6" align="end">
                                      <v-row no-gutters justify="center">
                                        <span style="color: #FB9372; font-size: 12px;"> {{Conpon.sale}}</span><br>
                                        <span style="color: #F56E22; font-size: 28px; font-weight: 600;"> {{Conpon.saleNumber}} {{Conpon.unit}}</span><br>
                                        <v-btn color="#F56E22" style="color:white;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-img>
                          </v-slide-item>
                        </v-slide-group>
                      </v-sheet>
                    </div>
                    <v-img
                      style="z-index: 0"
                      class="rounded-lg"
                      src="@/assets/ConponNGC/shopConpon/background.png"
                    ></v-img>
                  </div>
                </v-col> -->
                <v-col v-if="RoleUser === 'ext_buyer' && dataCoupons.length !== 0 && (RoleUser !== 'sale_order' && SaleVendor === false)" cols="12" md="12" class="backIMG" style="position: relative">
                  <v-chip class="mr-4 ChipHeadCoupons" style="pointer-events: none; position: absolute; font-size: 23px; color: #27AB9C; background-color: white; z-index: 3;">
                    <b style=" margin-left: 10px;" > {{ $t('ShopPage.StoreCoupon') }}</b>
                  </v-chip>
                  <v-row justify="end" no-gutters > <span @click="getAllCoupon()" style="font-size: 16px; color: #27AB9C;" class="couponAll" >{{ $t('ShopPage.ViewAll') }}</span></v-row>
                  <div :class=" MobileSize? 'pa-1 pt-1':'pa-4 pt-6'">
                    <section>
                      <vue-horizontal-list :items="dataCoupons"
                        active-class="success"
                        show-arrows
                        center-active
                        :options="optionsCard">
                        <template v-slot:nav-prev>
                          <v-icon color="#27AB9C" large>mdi-chevron-left</v-icon>
                        </template>

                        <template v-slot:nav-next>
                          <v-icon color="#27AB9C" large>mdi-chevron-right</v-icon>
                        </template>

                        <template v-slot:default="{ item }">
                          <v-col>
                            <v-col :class="!MobileSize && !IpadSize ? 'couponIMGDesk': MobileSize? 'couponIMGMobile': 'couponIMG'">
                                <v-col cols="12" md="12" class="pt-0" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                                  <v-row no-gutters>
                                    <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                                      <v-row no-gutters v-if="item.coupon_type === 'free_product'">
                                        <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                                          <!-- <span style="color: #27AB9C; font-size: 14px; font-weight: 600;"> {{item.coupon_name | truncate(20, '...')}}</span><br> -->
                                          <span style="color: #27AB9C; font-size: 14px; font-weight: 600; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"> {{ item.coupon_name }}</span>
                                          <span @click="condition(item)" class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;">{{ $t('ShopPage.CouponConditions') }}</span><br>
                                          <span class="mb-4" style="font-size: 10px;">
                                            <span v-if="item.couponDate.useEndDate !== null">{{ $t('ShopPage.ValidUntil') }} {{item.couponDate.useEndDate}}</span>
                                            <span v-else>{{ $t('ShopPage.NoExpiry') }}</span>
                                          </span><br>
                                          <v-col cols="12" md="12" class="pa-0">
                                            <v-row no-gutters>
                                              <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" :class="IpadSize || IpadProSize ? 'vProgressLinearIped': MobileSize? 'vProgressLinearMobile':'vProgressLinearDesk'" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="item.powerOfEndDate">
                                                <template #progress="{ value }">
                                                <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                                </template>
                                              </v-progress-linear>
                                              <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span>
                                            </v-row>
                                          </v-col>
                                        </v-col>
                                        <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                          <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_product' ? $t('ShopPage.Coupon') : $t('ShopPage.Discount') }}</span><br>
                                          <span style="color: #F56E22; font-size: 22px; font-weight: 600;">{{ $t('ShopPage.Freebie') }}</span><br>
                                          <v-btn v-if="item.is_collected === 'N'" @click="getCouponsCode(item)" color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                          <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SavedCode') }}</v-btn>
                                        </v-col>
                                      </v-row>
                                      <v-row no-gutters v-else>
                                        <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                                          <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ item.coupon_name }}</span>
                                          <span style="font-size: 10px;"> {{ $t('ShopPage.Minimum') }} {{item.spend_minimum}} {{ $t('ShopPage.THB') }}</span><br>
                                          <span class="mb-4" style="font-size: 10px;">
                                            <span v-if="item.couponDate.useEndDate !== null">{{ $t('ShopPage.ValidUntil') }} {{item.couponDate.useEndDate}}</span>
                                            <span v-else>{{ $t('ShopPage.NoExpiry') }}</span></span><br>
                                          <v-col cols="12" md="12" class="pa-0">
                                            <v-row no-gutters>
                                              <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" :class="IpadSize || IpadProSize ? 'vProgressLinearIped': MobileSize? 'vProgressLinearMobile':'vProgressLinearDesk'" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="item.powerOfEndDate">
                                                <template #progress="{ value }">
                                                <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                                </template>
                                              </v-progress-linear>
                                              <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span>
                                            </v-row>
                                          </v-col>
                                        </v-col>
                                        <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                          <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? $t('ShopPage.FreeShippingCode') : $t('ShopPage.Discount') }}</span><br>
                                          <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%': $t('ShopPage.THB') }}</span><br>
                                          <v-btn v-if="item.is_collected === 'N'" @click="getCouponsCode(item)" color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                          <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SavedCode') }}</v-btn>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                            </v-col>
                          </v-col>
                        </template>
                      </vue-horizontal-list>
                    </section>
                  </div>
                </v-col>
                <FlashSaleShop v-if="RoleUser !== 'sale_order' && SaleVendor === false"/>
                <div :class="IpadSize || IpadProSize? 'pt-5': MobileSize? 'pt-2': 'pt-5 pr-3'" style="font-size: 18px;">
                  <v-col cols="12" md="12" class="px-0" :class="IpadProSize ? 'cardProductIpadProFrist' : ''">
                    <ListShopProduct
                      :propsData="BestSeller"
                      typeProduct="best_seller"
                      :header="this.$t('ShopPage.BestSellers')"
                      :isCheck="isActiveBestSelle"
                      v-if="countBestSeller !== 0 && dataShop !== '' && isActiveBestSelle"
                    />
                    <ListShopProduct
                      :propsData="BestSeller"
                      typeProduct="best_seller"
                      :header="this.$t('ShopPage.BestSellers')"
                      :isCheck="isActiveBestSelle"
                      v-else-if="countBestSeller !== 0 && dataShop !== '' && isActiveBestSelle === false"
                    />
                    <ListShopProduct
                      :propsData="AllProduct"
                      :header="this.$t('ShopPage.AllProducts')"
                      typeProduct="all_product"
                      :isCheck="isActiveAllProduct"
                      v-if="countAll !== 0 && dataShop !== '' && isActiveAllProduct"
                    />
                    <ListShopProduct
                      :propsData="AllProduct"
                      :header="this.$t('ShopPage.AllProducts')"
                      typeProduct="all_product"
                      :isCheck="isActiveAllProduct"
                      v-else-if="countAll !== 0 && dataShop !== '' && isActiveAllProduct === false"
                    />
                    <ListShopProduct
                      :propsData="Recommended"
                      typeProduct="recommended_product"
                      :header="this.$t('ShopPage.Recommended')"
                      :isCheck="isActiveRecommentProduct"
                      v-if="countRecommend !== 0 && dataShop !== '' && isActiveRecommentProduct"
                    />
                    <ListShopProduct
                      :propsData="Recommended"
                      typeProduct="recommended_product"
                      :header="this.$t('ShopPage.Recommended')"
                      :isCheck="isActiveRecommentProduct"
                      v-else-if="countRecommend !== 0 && dataShop !== '' && isActiveRecommentProduct === false"
                    />
                    <ListShopProduct
                      :propsData="NewProduct"
                      typeProduct="new_product"
                      :header="this.$t('ShopPage.NewArrival')"
                      :isCheck="isActiveNewProduct"
                      v-if="countNew !== 0 && dataShop !== '' && isActiveNewProduct"
                    />
                    <ListShopProduct
                      :propsData="NewProduct"
                      typeProduct="new_product"
                      :header="this.$t('ShopPage.NewArrival')"
                      :isCheck="isActiveNewProduct"
                      v-else-if="countNew !== 0 && dataShop !== '' && isActiveNewProduct === false"
                    />
                    <ListShopProduct
                      :propsData="SaleProduct"
                      typeProduct="sale_product"
                      :header="this.$t('ShopPage.DiscountedProducts')"
                      :isCheck="isActiveSaleProduct"
                      v-if="countSale !== 0 && dataShop !== '' && isActiveSaleProduct"
                    />
                    <ListShopProduct
                      :propsData="SaleProduct"
                      typeProduct="sale_product"
                      :header="this.$t('ShopPage.DiscountedProducts')"
                      :isCheck="isActiveSaleProduct"
                      v-else-if="countSale !== 0 && dataShop !== '' && isActiveSaleProduct === false"
                    />
                    <ListShopProduct
                      :propsData="NormalProduct"
                      typeProduct="normal_product"
                      :header="this.$t('ShopPage.GeneralProducts')"
                      :isCheck="isActiveGeneralProduct"
                      v-if="NormalProduct.length !== 0 && dataShop !== '' && isActiveGeneralProduct"
                    />
                    <ListShopProduct
                      :propsData="NormalProduct"
                      typeProduct="normal_product"
                      :header="this.$t('ShopPage.GeneralProducts')"
                      :isCheck="isActiveGeneralProduct"
                      v-else-if="NormalProduct.length !== 0 && dataShop !== '' && isActiveGeneralProduct === false"
                    />
                    <ListShopProduct
                      :propsData="OutProduct"
                      typeProduct="out_product"
                      :header="this.$t('ShopPage.OutOfStock')"
                      :isCheck="isActiveOutOfStockProduct"
                      v-if="OutProduct.length !== 0 && dataShop !== '' && isActiveOutOfStockProduct"
                    />
                    <ListShopProduct
                      :propsData="OutProduct"
                      typeProduct="out_product"
                      :header="this.$t('ShopPage.OutOfStock')"
                      :isCheck="isActiveOutOfStockProduct"
                      v-else-if="OutProduct.length !== 0 && dataShop !== '' && isActiveOutOfStockProduct === false"
                    />
                  </v-col>
                </div>
              </a-tab-pane>
              <a-tab-pane :key="1" class="mt-6"><span slot="tab" :style="MobileSize? 'font-size: 15px;': 'padding-top: 0.5em; padding-left: 10px; font-weight: bold; font-size: 20px;'" ref="targetSection">{{ $t('ShopPage.AllProductsCategory') }}</span>
                <div v-if="!MobileSize">
                  <div :class="IpadSize ? 'pl-0' : 'pl-0'" v-if="dataShop.total_product !== 0 && dataShop.total_product !== null">
                    <v-col cols="12" class="pl-0 pb-0">
                      <v-row>
                        <v-col :cols="IpadSize || IpadProSize? '3':'2'" class="pl-3 pb-0">
                          <v-row no-gutters>
                            <div class="d-flex">
                              <v-img class="mt-2 mr-2" :width="IpadSize? '18': '24'" :height="IpadSize? '18': '24'" src="@/assets/ImageINET-Marketplace/Shop/iconShop/grid.png"></v-img>
                              <span :style="IpadSize? 'font-size: 15px; font-weight: bold;': 'font-size: 20px; font-weight: bold;'" class="mt-1"> {{ $t('ShopPage.Category') }}</span>
                            </div>
                          </v-row>
                          <v-spacer class="mt-3" style="border-top: 1px solid #DAF1E9;"></v-spacer>
                        </v-col>
                        <v-col :cols="IpadSize ||IpadProSize? '9':'10'" :class="IpadSize? 'pl-4' : ''">
                          <v-row no-gutters>
                            <v-col cols="12" md="12">
                              <v-row :justify="IpadSize || IpadProSize ? 'start': 'end'" :class="IpadSize? 'pr-2':'pr-6'">
                                <!-- <v-col v-if="IpadProSize || IpadSize" class="px-0">
                                  <span style="color: #9A9A9A; font-size: 13px;">เรียงตาม: </span>
                                </v-col>
                                <v-col :cols="IpadSize || IpadProSize? '2':'2'" :class="!IpadProSize && !IpadSize? '' : 'pa-0 pt-1'" :style="IpadSize ? 'text-align: start;':'text-align: end;'">
                                    <span v-if="!IpadProSize && !IpadSize" :class="IpadSize? '': 'pt-3 pr-2'" style="color: #9A9A9A; font-size: 13px;">เรียงตาม: </span>
                                    <v-btn
                                      @click="GetProductType('')"
                                      outlined
                                      rounded
                                      :class="IpadSize? 'pa-0 btnSizeIpad':' v-chip-store btnSizeDesk'"
                                      :color="typeList === '' ? '#ffffff' :'#27AB9C'"
                                      :style="typeList === '' ? 'background-color: #27AB9C;' : ''">
                                      <span> ทั้งหมด </span>
                                    </v-btn>
                                </v-col>
                                <v-col :cols="IpadSize || IpadProSize? '2': '1'" :class="IpadSize? 'px-0':'px-0'" :style="IpadSize ? 'text-align: center;':'text-align: center;'">
                                  <v-btn
                                    @click="GetProductType('new')"
                                    outlined
                                    rounded
                                    :class="IpadSize ? 'pa-0 btnSizeIpad': IpadProSize ? 'px-5 py-5':'v-chip-store btnSizeDesk'"
                                    :color="typeList === 'new' ? '#ffffff' :'#27AB9C'"
                                    :style="typeList === 'new' ? 'background-color: #27AB9C;' : ''">
                                    ล่าสุด
                                  </v-btn>
                                </v-col>
                                <v-col :cols="IpadSize || IpadProSize? '2': '1'" :class="IpadSize? 'px-0':'px-0'" :style="IpadSize ? 'text-align: center;':'text-align: center;'">
                                  <v-btn
                                    @click="GetProductType('best-seller')"
                                    outlined
                                    rounded
                                    :class="IpadSize? 'pa-0 btnSizeIpad':'v-chip-store btnSizeDesk'"
                                    :color="typeList === 'best-seller' ? '#ffffff' :'#27AB9C'"
                                    :style="typeList === 'best-seller' ? 'background-color: #27AB9C;' : ''">
                                    ขายดี
                                  </v-btn>
                                </v-col>
                                <v-col :cols="IpadSize || IpadProSize? '2': '1'" :class="IpadSize? 'px-0':'px-0'" :style="IpadSize ? 'text-align: center;':'text-align: center;'">
                                  <v-btn
                                    @click="GetProductType('best-seller')"
                                    outlined
                                    rounded
                                    :class="IpadSize? 'pa-0 btnSizeIpad':' v-chip-store btnSizeDesk'"
                                    :color="typeList === 'best-seller' ? '#ffffff' :'#27AB9C'"
                                    :style="typeList === 'best-seller' ? 'background-color: #27AB9C;' : ''">
                                    ขายดี
                                  </v-btn>
                                </v-col><v-col :cols="IpadSize || IpadProSize? '2': '1'" :class="IpadSize? 'px-0':'px-0'" :style="IpadSize ? 'text-align: center;':'text-align: center;'">
                                  <v-btn
                                    @click="GetProductType('best-seller')"
                                    outlined
                                    rounded
                                    :class="IpadSize? 'pa-0 btnSizeIpad':' v-chip-store btnSizeDesk'"
                                    :color="typeList === 'best-seller' ? '#ffffff' :'#27AB9C'"
                                    :style="typeList === 'best-seller' ? 'background-color: #27AB9C;' : ''">
                                    ขายดี
                                  </v-btn>
                                </v-col> -->
                                <v-col :cols="IpadSize || IpadProSize? '6':'3'" :class="IpadSize  || IpadProSize? 'pt-1 pa-0 pl-1': 'pt-5'">
                                  <v-row no-gutters>
                                    <v-col :align="IpadSize || IpadProSize? 'start': 'end'"  class="pa-0">
                                      <v-row class="d-flex justify-end" no-gutters>
                                        <v-col :cols="IpadSize || IpadProSize? '3':'4'" class="mt-2">
                                          <span style="font-size: 16px;" class="pr-4">
                                            {{ $t('ShopPage.Type') }} :
                                          </span>
                                        </v-col>
                                        <v-col :cols="IpadSize || IpadProSize? '9': '8'" class="pr-4 pl-2">
                                          <v-select
                                            style="border-radius: 8px;"
                                            outlined
                                            dense
                                            v-model="typeList"
                                            :items="itemTypeList"
                                            @change="GetProductType(typeList)"
                                            placeholder="เลือก"
                                            hide-details
                                          >
                                            <template v-slot:selection="{ item }">
                                              <span>{{ item.text }}</span>
                                            </template>
                                            <template v-slot:append>
                                              <v-icon>mdi-chevron-down</v-icon>
                                            </template>
                                          </v-select>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <v-col :cols="IpadSize || IpadProSize? '6':'3'" :class="IpadSize || IpadProSize? 'pt-1 pa-0 pl-1': 'pt-5'">
                                  <v-row no-gutters>
                                    <v-col :align="IpadSize || IpadProSize? 'start': 'end'"  class="pa-0">
                                      <v-row class="d-flex justify-end" no-gutters>
                                        <v-col :cols="IpadSize || IpadProSize? '3':'4'" class="mt-2">
                                          <span style="font-size: 16px;" class="pr-4">
                                            {{ $t('ShopPage.Price') }} :
                                          </span>
                                        </v-col>
                                        <v-col :cols="IpadSize || IpadProSize? '9': '8'">
                                          <v-select
                                            style="border-radius: 8px;"
                                            outlined
                                            dense
                                            v-model="priceList"
                                            :items="itemPriceList"
                                            @change="GetProductPrice(priceList)"
                                            placeholder="เลือก"
                                            hide-details
                                          >
                                            <template v-slot:selection="{ item }">
                                              <span>{{ item.text }}</span>
                                            </template>
                                            <template v-slot:append>
                                              <v-icon>mdi-chevron-down</v-icon>
                                            </template>
                                          </v-select>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col>
                      <v-row dense>
                        <v-col v-if="checkCategory === 'no'" :cols="IpadSize ||IpadProSize ? '3':'2'" style="border-bottom: thin solid #fafafa;" :class="IpadSize? 'pa-0 pt-7':'pa-0 pt-4'">
                          <!-- <div v-for="(item, index) in DataCategory" :key="index" style="" class="cat">
                            <v-col class="pl-0" :style="categoryID === item.ms_category_id ? 'background-color: #E9FFF8; font-size: 16px; color: #27AB9C; font-weight: 600;' : 'font-size: 16px; font-weight: 400;'" @click="selectCategory(item.ms_category_id)">
                              <div>
                                {{item.category_name}}
                              </div>
                            </v-col>
                          </div> -->
                          <v-list dense nav style="border-radius: 8px;" class="custom-list">
                            <template v-for="(item, i) in DataCategory" >
                              <v-list-group v-if="item.children.length !== 0" @click="selectCategory(item.ms_category_id, item.name)" :key="i" >
                                <template v-slot:activator>
                                  <v-list-item-content style="border-radius: 8px;">
                                      <v-list-item-title ><v-icon :color="categoryID === item.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon> <span style="font-size: 16px;" :style="categoryID === item.ms_category_id ? 'color: rgb(39, 171, 156);' : ''">{{ item.category_name }}</span></v-list-item-title>
                                  </v-list-item-content>
                                </template>
                                <v-list-item v-for="(record, i) in item.children" :key="i" @click="selectCategory(record.ms_category_id)" class="cat"  :style="categoryID === record.ms_category_id ? 'background-color: #E9FFF8;' : ''">
                                  <v-list-item-content class="ml-4">
                                      <v-list-item-title> <v-icon :color="categoryID === record.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon><span :style="categoryID === record.ms_category_id ? 'font-size: 16px; color: #27AB9C; font-weight: 600;': 'font-size: 16px; font-weight: 400;'">{{ record.category_name }}</span></v-list-item-title>
                                  </v-list-item-content>
                                </v-list-item>
                            </v-list-group>
                              <v-list-item-content :key="i" v-else class="cat px-2" @click="selectCategory(item.ms_category_id)" :style="categoryID === item.ms_category_id ? 'background-color: #E9FFF8; font-size: 16px; color: #27AB9C; font-weight: 600;' : 'font-size: 16px; font-weight: 400;'" style="border-radius: 8px;">
                                <v-list-item-title ><v-icon :color="categoryID === item.ms_category_id ? '#27AB9C':''">mdi-circle-small</v-icon> <span >{{ item.category_name }}</span></v-list-item-title>
                              </v-list-item-content>
                            </template>
                          </v-list>
                        </v-col>
                        <v-col v-else :cols="IpadSize ||IpadProSize ? '3':'2'" style="border-bottom: thin solid #fafafa;" :class="IpadSize? 'pa-0 pt-7':'pa-0 pt-4'">
                          <v-list dense nav style="border-radius: 8px;" class="custom-list">
                            <template v-for="(item, i) in DataCategory" >
                              <v-list-group v-if="item.children.length !== 0" @click="selectCategory(item.ms_category_id, item.name)" :key="i" >
                                <template v-slot:activator>
                                  <v-list-item-content style="border-radius: 8px;">
                                      <v-list-item-title ><v-icon :color="categoryID === item.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon> <span style="font-size: 16px;" :style="categoryID === item.ms_category_id ? 'color: rgb(39, 171, 156);' : ''">{{ item.category_name }}</span></v-list-item-title>
                                  </v-list-item-content>
                                </template>
                                <v-list-item v-for="(record, indexRecord) in item.children" :key="indexRecord" @click.stop="selectCategory(record.ms_category_id, $event)" class="cat"  :style="categoryID === record.ms_category_id ? 'background-color: #E9FFF8;' : ''">
                                  <v-list-item-content class="ml-4 d-flex" style="gap: .3vw;" >
                                      <v-list-item-title> <v-icon :color="categoryID === record.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon><span :style="categoryID === record.ms_category_id ? 'font-size: 16px; color: #27AB9C; font-weight: 600;': 'font-size: 16px; font-weight: 400;'">{{ record.category_name }}</span></v-list-item-title>
                                      <v-list-item v-for="(recordDeep1, indexDeep1) in record.children" :key="indexDeep1" @click.stop="selectCategory(recordDeep1.ms_category_id, $event)" class="cat"  :style="categoryID === recordDeep1.ms_category_id ? 'background-color: #E9FFF8;' : ''">
                                        <v-list-item-content class="ml-4 d-flex" style="gap: .3vw;">
                                            <v-list-item-title> <v-icon :color="categoryID === recordDeep1.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon><span :style="categoryID === recordDeep1.ms_category_id ? 'font-size: 16px; color: #27AB9C; font-weight: 600;': 'font-size: 16px; font-weight: 400;'">{{ recordDeep1.category_name}}</span></v-list-item-title>
                                            <v-list-item v-for="(recordDeep2, indexDeep2) in recordDeep1.children" :key="indexDeep2" @click.stop="selectCategory(recordDeep2.ms_category_id, $event)" class="cat"  :style="categoryID === recordDeep2.ms_category_id ? 'background-color: #E9FFF8;' : ''">
                                              <v-list-item-content class="ml-4">
                                                  <v-list-item-title> <v-icon :color="categoryID === recordDeep2.ms_category_id ? '#27AB9C': ''">mdi-circle-small</v-icon><span :style="categoryID === recordDeep2.ms_category_id ? 'font-size: 16px; color: #27AB9C; font-weight: 600;': 'font-size: 16px; font-weight: 400;'">{{ recordDeep2.category_name}}</span></v-list-item-title>
                                              </v-list-item-content>
                                            </v-list-item>
                                        </v-list-item-content>
                                      </v-list-item>
                                  </v-list-item-content>
                                </v-list-item>
                            </v-list-group>
                              <v-list-item-content :key="i" v-else class="cat px-2" @click="selectCategory(item.ms_category_id)" :style="categoryID === item.ms_category_id ? 'background-color: #E9FFF8; font-size: 16px; color: #27AB9C; font-weight: 600;' : 'font-size: 16px; font-weight: 400;'" style="border-radius: 8px;">
                                <v-list-item-title ><v-icon :color="categoryID === item.ms_category_id ? '#27AB9C':''">mdi-circle-small</v-icon> <span >{{ item.category_name }}</span></v-list-item-title>
                              </v-list-item-content>
                            </template>
                          </v-list>
                        </v-col>
                        <v-col :cols="IpadSize ||IpadProSize ? '9': '10'" :md=" IpadSize? '9': '9'" :class="IpadSize? 'pa-0 pt-4':''">
                          <v-col v-if="AllChangeProduct.length !== 0">
                            <v-row>
                              <v-col align="center" v-for="(item, index) in paginated" :key="index" class="px-0" :class="IpadSize ? 'cardProductIpad' :IpadProSize ? 'cardProductIpadPro' : 'cardProductDesk'">
                                <CardProducts :itemProduct='item' align="start"/>
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col>
                                <v-pagination
                                  color="#3EC6B6"
                                  v-model="pageNumber"
                                  :length="pageMax"
                                  class="paginationStyle"
                                  @input="pageChange($event)"
                                > </v-pagination>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col v-else>
                            <v-row justify="center" style="height: 250px;">
                              <!-- <template> -->
                                <a-empty
                                  :image="require('@/assets/Not_Result.png')"
                                  :image-style="{ height: '150px', marginBottom: '20px', marginTop: '60px' }"
                                >
                                  <h1 slot="description"
                                  style="
                                    color: #636363;
                                    font-size: 18px;
                                    line-height: 25.2px;
                                    font-weight: 600;
                                  ">{{ $t('ShopPage.NoProduct') }}
                                  </h1>
                                  <br />
                                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                                </a-empty>
                              <!-- </template> -->
                            </v-row>
                          </v-col>
                        </v-col>
                      </v-row>
                    </v-col>
                  </div>
                  <div class="pl-5" style="font-size: 18px;" v-else>
                    <v-row justify="center" style="height: 200px;">
                      <span v-if="RoleUser !== 'purchaser'" style="font-size: 18px; font-weight: bold;">{{ $t('ShopPage.NoProductYet') }}</span>
                    </v-row>
                  </div>
                </div>
                <!-- mobile -->
                <div v-else>
                  <div class="pl-0" v-if="dataShop.total_product !== 0 && dataShop.total_product !== null">
                    <v-col cols="12" class="pl-0">
                      <v-row>
                        <!-- <v-col cols="12" md="2" class="pl-3 pr-0 py-0">
                          <v-row no-gutters>
                            <div class="d-flex">
                              <v-img class="mt-2 mr-1" width="15" height="15" src="@/assets/ImageINET-Marketplace/Shop/iconShop/grid.png"></v-img>
                              <span style="font-size: 17px; font-weight: bold;" class="mt-1"> หมวดหมู่</span>
                            </div>
                          </v-row>
                          <v-spacer class="mt-3" style="border-top: 1px solid #DAF1E9;"></v-spacer>
                        </v-col> -->
                        <v-col cols="12" md="10">
                          <v-row>
                            <v-col cols="12" md="12">
                              <v-row justify="center">
                                <!-- หมวดหมู่ -->
                                <v-col cols="12">
                                  <v-row>
                                    <v-col class="pb-0 pt-0">
                                      <v-row no-gutters>
                                        <v-col cols="2" class="mt-2">
                                          <span style="font-size: 14px; font-weight: 500 !important; color:#333333;">
                                            {{ $t('ShopPage.Category') }}
                                          </span>
                                        </v-col>
                                        <v-col cols="10" v-if="checkCategory === 'no'">
                                          <v-select
                                            style="border-radius: 8px;"
                                            outlined
                                            dense
                                            v-model="categoryID"
                                            item-text="category_name"
                                            item-value="ms_category_id"
                                            :items="DataCategory"
                                            @change="selectCategory(categoryID)"
                                            placeholder="เลือก"
                                            hide-details
                                          >
                                            <!-- <template v-slot:selection="{ item }">
                                              <span>{{ item.text }}</span>
                                            </template> -->
                                            <template v-slot:append>
                                              <v-icon>mdi-chevron-down</v-icon>
                                            </template>
                                          </v-select>
                                        </v-col>
                                        <v-col cols="10" v-else>
                                          <treeselect
                                            :value="selectCategoryCustom || null"
                                            :options="DataCategory || null"
                                            :normalizer="normalizer"
                                            placeholder="เลือกหมวดหมู่"
                                            style="z-index: 999;"
                                            :append-to-body="true"
                                            @input="selectCategory"
                                          ></treeselect>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <!-- เรียงตาม -->
                                <v-col cols="12">
                                  <v-row>
                                    <v-col class="pb-0">
                                      <v-row no-gutters>
                                        <v-col cols="2" class="mt-2">
                                          <span style="font-size: 14px; font-weight: 500 !important; color:#9A9A9A;">
                                            {{ $t('ShopPage.SortBy') }}
                                          </span>
                                        </v-col>
                                        <v-col cols="4">
                                          <v-select
                                            style="border-radius: 8px;"
                                            outlined
                                            dense
                                            v-model="typeList"
                                            :items="itemTypeList"
                                            @change="GetProductType(typeList)"
                                            placeholder="เลือก"
                                            hide-details
                                          >
                                            <!-- <template v-slot:selection="{ item }">
                                              <span>{{ item.text }}</span>
                                            </template> -->
                                            <template v-slot:append>
                                              <v-icon>mdi-chevron-down</v-icon>
                                            </template>
                                          </v-select>
                                        </v-col>
                                        <v-col cols="6" align="end">
                                          <v-btn width="111" height="38" color="#27AB9C" rounded outlined @click="dialogFunnelMobile = true"> <v-img size="24" src="@/assets/ImageINET-Marketplace/Shop/funnelMobile.png"></v-img>{{ $t('ShopPage.Filter') }}
                                          </v-btn>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-row dense class="px-0">
                        <!-- <v-col cols="2" style="border-bottom: thin solid #fafafa;" class="pl-0 pr-0">
                          <div v-for="(item, index) in DataCategory" :key="index" style="" class="cat">
                            <v-col class="pl-0" :style="categoryID === item.ms_category_id ? 'background-color: #E9FFF8; font-size: 10px; color: #27AB9C; font-weight: 600;' : 'font-size: 10px; font-weight: 400;'" @click="selectCategory(item.ms_category_id)">
                              <div>
                                {{item.category_name}}
                              </div>
                            </v-col>
                          </div>
                        </v-col> -->
                        <v-col cols="12" md="12">
                          <v-col v-if="AllChangeProduct.length !== 0" class="px-0">
                            <!-- <v-row v-if="!MobileSize" justify="start">
                              <v-col cols="6" v-for="(item, index) in paginated" :key="index">
                                <CardProducts :itemProduct='item' />
                              </v-col>
                            </v-row> -->
                            <v-row dense class="px-0">
                              <v-col class="px-0 cardProductMobile" v-for="(item, index) in paginated" :key="index">
                                <CardProductsMobile :itemProduct='item' />
                              </v-col>
                            </v-row>
                            <v-row>
                              <v-col>
                                <v-pagination
                                  color="#3EC6B6"
                                  v-model="pageNumber"
                                  :length="pageMax"
                                  total-visible="5"
                                  class="paginationStyle"
                                  @input="pageChange($event)"
                                > </v-pagination>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col v-else>
                            <v-row justify="center" style="height: 270px;">
                              <!-- <template> -->
                                <a-empty
                                  :image="require('@/assets/Not_Result.png')"
                                  :image-style="{ height: '150px', marginBottom: '20px', marginTop: '80px' }"
                                >
                                  <h1 slot="description"
                                  style="
                                    color: #636363;
                                    font-size: 18px;
                                    line-height: 25.2px;
                                    font-weight: 600;
                                  ">{{ $t('ShopPage.NoProduct') }}
                                  </h1>
                                  <br />
                                  <!-- <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบอีกครั้ง</h3> -->
                                  <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
                                </a-empty>
                              <!-- </template> -->
                            </v-row>
                          </v-col>
                        </v-col>
                      </v-row>
                    </v-col>
                  </div>
                  <div class="pl-5" style="font-size: 18px;" v-else>
                    <v-row justify="center" style="height: 200px;">
                      <span v-if="RoleUser !== 'purchaser'" style="font-size: 18px; font-weight: bold;">{{ $t('ShopPage.NoProductYet') }}</span>
                    </v-row>
                  </div>
                </div>
              </a-tab-pane>
              <a-tab-pane :key="2" class="mt-6">
                <span slot="tab" :style="MobileSize? 'font-size: 15px;': 'padding-top: 0.5em; padding-left: 10px; font-weight: bold; font-size: 20px;'">{{ $t('ShopPage.Article') }}</span>
                <v-row>
                  <v-col v-for="item in paginatedArticles" :key="item.id" cols="12" md="6" lg="4">
                    <v-card
                      :height="'100%'"
                      elevation="0"
                      style="display: flex; flex-direction: column; cursor: pointer; border-radius: 20px; filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.04)) drop-shadow(0px 2px 4px rgba(96, 97, 112, 0.16));"
                    >
                      <div class="pt-2 px-2" style="min-height: 180px;" id="image">
                        <div v-if="item.header_image_video.includes('youtube')">
                          <Youtube
                            ref="youtube"
                            :video-id="youtube_parser(item.header_image_video)"
                            @playing="playing"
                            :player-vars="playerVars"
                            style="width:100%; height:180px; border-radius:20px;"
                          />
                        </div>
                        <div v-else>
                          <v-img :src="item.header_image_video" cover :height="IpadSize ? 240 : 180" style="border-radius:20px;" width="auto" class="" contain></v-img>
                        </div>
                      </div>

                      <div style="min-height:100px;">
                        <v-card-title class="cardTitle pa-0">
                          <v-col cols="12">
                            <div :style="MobileSize ? '' : ''">
                              <span :style="MobileSize ? 'font-weight: 700; font-size: 16px;' : 'font-weight: 700; font-size: 18px;'"
                                    style="display: inline-block; font-weight: 700; font-size: 24px; line-height: 1.5em; min-height: 20px; height: 50px; overflow: hidden; text-overflow: ellipsis;">
                                    {{ item.title | truncate(70, '...') }}
                              </span><br />
                            </div>
                          </v-col>
                        </v-card-title>
                        <v-card-subtitle class="cardDetail pa-0">
                          <v-col cols="12" style="height: 50px; color:#9A9A9A; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: normal; text-align: start;">
                            {{ item.shot_description }}
                          </v-col>
                        </v-card-subtitle>
                      </div>

                      <v-card-actions class="pt-2 ml-2 cardDetail" style="margin-bottom: auto;">
                        <v-icon class="icon">mdi-eye</v-icon>
                        <span class="ml-2">View {{ item.view }}</span>
                        <v-spacer></v-spacer>
                        <v-btn color="primary" rounded outlined small @click="goToArticle(item)">{{ $t('ShopPage.ReadMore') }}</v-btn>
                        <v-spacer></v-spacer>
                        <span class="mr-2">{{ calDiffDate(item.created_at, new Date()) }}</span>
                      </v-card-actions>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row v-if="disabledPagination === false">
                  <v-col cols="12" class="pa-5">
                    <v-card-title style="display: flex; justify-content: center;">{{ $t('ShopPage.NoArticles') }}</v-card-title>
                  </v-col>
                </v-row>
                <v-row justify="center" class="mt-5 pt-2 mb-1" v-if="disabledPagination === true">
                  <v-pagination
                    color="#3EC6B6"
                    v-model="currentPageArticle"
                    :length="totalPagesArticle"
                    class="paginationStyle"
                    total-visible="5"
                  ></v-pagination>
                </v-row>
              </a-tab-pane>
            </a-tabs>
              </v-col>
            </v-row>
            <!-- </v-tabs> -->
          </v-container>
          <!-- modal  -->
          <v-dialog v-model="dialogChooseTypeCustomer" width="450" persistent>
            <v-card style="border-radius: 24px; background-color: #27AB9C;">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
                <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">{{ $t('ShopPage.CustomerTypeList') }}
                </span>
                <v-btn icon dark @click="closeDialog()">
                  <v-icon color="#FFFFFF">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card class="pa-4">
                <v-col cols="12">
                  <v-row no-gutters>
                    <v-col cols="6" class="pr-2">
                      <v-card height="150" class="cardChooseType" @click="chooseTypeCus('business')">
                        <h2>{{ $t('ShopPage.Company') }}</h2>
                        <h4 style="color:red;">{{ $t('ShopPage.CompanyOrder') }}</h4>
                      </v-card>
                    </v-col>
                    <v-col cols="6" class="pl-2">
                      <v-card height="150" class="cardChooseType" @click="chooseTypeCus('general')">
                        <h2>{{ $t('ShopPage.Individual') }}</h2>
                        <h4 style="color:red;">{{ $t('ShopPage.PersonalOrder') }}</h4>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-card>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialogAllCoupon" width="425" persistent>
            <v-card style="border-radius: 24px; background-color: #27AB9C;">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
                <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">{{ $t('ShopPage.AllCoupons') }}
                </span>
                <v-btn icon dark @click="dialogAllCoupon = false">
                  <v-icon color="#FFFFFF">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card class="pa-0 custom-scroll" style="background-color: #CEF0EC; max-height: 630px; overflow-y: auto; overflow-x: hidden;">
                <v-col cols="12" class="px-6">
                  <v-row no-gutters>
                    <v-col cols="12" class="pb-2" v-for="(item, index) in dataCoupons" :key="index">
                      <v-col class="couponIMGMobile">
                        <v-col cols="12" md="12" class="pt-0" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                          <v-row no-gutters>
                            <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                              <v-row no-gutters v-if="item.coupon_type === 'free_product'">
                                <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start"  >
                                  <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"> {{item.coupon_name }}</span>
                                  <span @click="condition(item)" class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;">{{ $t('ShopPage.CouponConditions') }}</span><br>
                                  <span class="mb-4" style="font-size: 10px;">
                                    <span v-if="item.couponDate.useEndDate !== null">{{ $t('ShopPage.ValidUntil') }} {{item.couponDate.useEndDate}}</span>
                                    <span v-else>{{ $t('ShopPage.NoExpiry') }}</span></span><br>
                                  <v-col cols="12" md="12" class="pa-0">
                                    <!-- <v-row no-gutters>
                                      <v-col cols="8" md="8" class="pa-0 pt-2">
                                        <v-progress-linear color="#C04F36" class="powerOfEndDate" :value="item.powerOfEndDate"></v-progress-linear>
                                      </v-col>
                                      <v-col cols="4" md="4" class="pl-2 pb-2"> <span style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span></v-col>
                                    </v-row> -->
                                    <v-row no-gutters>
                                      <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="item.powerOfEndDate">
                                        <template #progress="{ value }">
                                        <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                        </template>
                                      </v-progress-linear>
                                      <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span>
                                    </v-row>
                                  </v-col>
                                </v-col>
                                <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                  <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_product' ? $t('ShopPage.Coupon') : $t('ShopPage.Discount') }}</span><br>
                                  <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{ $t('ShopPage.Freebie') }}</span><br>
                                  <v-btn v-if="item.is_collected === 'N'" @click="getCouponsCode(item)" color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                  <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SavedCode') }}</v-btn>
                                </v-col>
                              </v-row>
                              <v-row no-gutters v-else>
                                <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                                  <span style="color: #27AB9C; font-size: 14px; font-weight: 600; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{item.coupon_name }}</span>
                                  <span> {{ $t('ShopPage.Minimum') }} {{item.spend_minimum}} {{ $t('ShopPage.THB') }}</span><br>
                                  <span class="mb-4" style="font-size: 10px;">
                                    <span v-if="item.couponDate.useEndDate !== null">{{ $t('ShopPage.ValidUntil') }} {{item.couponDate.useEndDate}}</span>
                                    <span v-else>{{ $t('ShopPage.NoExpiry') }}</span></span><br>
                                  <v-col cols="12" md="12" class="pa-0">
                                    <!-- <v-row no-gutters>
                                      <v-col cols="8" md="8" class="pa-0 pt-2">
                                        <v-progress-linear color="#C04F36" class="powerOfEndDate" :value="item.powerOfEndDate"></v-progress-linear>
                                      </v-col>
                                      <v-col cols="4" md="4" class="pl-2 pb-2"> <span style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span></v-col>
                                    </v-row> -->
                                    <v-row no-gutters>
                                      <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="item.powerOfEndDate">
                                        <template #progress="{ value }">
                                        <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                        </template>
                                      </v-progress-linear>
                                      <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span>
                                    </v-row>
                                  </v-col>
                                </v-col>
                                <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                  <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? $t('ShopPage.FreeShippingCode') :  $t('ShopPage.Discount') }}</span><br>
                                  <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%': $t('ShopPage.THB') }}</span><br>
                                  <v-btn v-if="item.is_collected === 'N'" @click="getCouponsCode(item)" color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                  <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SavedCode') }}</v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                    </v-col>
                    </v-col>
                  </v-row>
                </v-col>
              </v-card>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialogConditionCoupon" width="425" persistent>
            <v-card style="border-radius: 24px; background-color: #27AB9C;">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
                <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">{{ $t('ShopPage.DiscountCodeConditions') }}
                </span>
                <v-btn icon dark @click="dialogConditionCoupon = false">
                  <v-icon color="#FFFFFF">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card class="pa-0" style="background-color: #FFFFFF;" v-if="conditionData !== ''">
                <v-col cols="12">
                  <v-row no-gutters>
                    <v-col cols="12" class="pb-2">
                      <v-col class="couponIMGMobile">
                        <v-col cols="12" md="12" class="pt-0" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                          <v-row no-gutters>
                            <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                              <v-row no-gutters v-if="conditionData.coupon_type === 'free_product'">
                                <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                                  <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  overflow: hidden; white-space: nowrap; text-overflow: ellipsis; max-width: 120px;"> {{conditionData.coupon_name }}</span><br>
                                  <span class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;"></span><br>
                                  <span v-if="conditionData.couponDate.useEndDate !== null" class="mb-4" style="font-size: 10px;"> {{ $t('ShopPage.ValidUntil') }} {{conditionData.couponDate.useEndDate}}</span>
                                  <span v-else style="font-size: 10px;">{{ $t('ShopPage.NoExpiry') }}</span>
                                  <v-col cols="12" md="12" class="pa-0">
                                    <!-- <v-row no-gutters>
                                      <v-col cols="8" md="8" class="pa-0 pt-2">
                                        <v-progress-linear color="#C04F36" class="powerOfEndDate" :value="item.powerOfEndDate"></v-progress-linear>
                                      </v-col>
                                      <v-col cols="4" md="4" class="pl-2 pb-2"> <span style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span></v-col>
                                    </v-row> -->
                                    <v-row no-gutters>
                                      <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="conditionData.powerOfEndDate">
                                        <template #progress="{ value }">
                                        <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                        </template>
                                      </v-progress-linear>
                                      <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{conditionData.powerOfEndDate}} %</span>
                                    </v-row>
                                  </v-col>
                                </v-col>
                                <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                  <span style="color: #FB9372; font-size: 12px;"> {{conditionData.coupon_type === 'free_product' ? $t('ShopPage.Coupon') : $t('ShopPage.Discount')}}</span><br>
                                  <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{ $t('ShopPage.Freebie') }}</span><br>
                                  <v-btn v-if="conditionData.is_collected === 'N'" @click="getCouponsCode(conditionData)" color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                  <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SavedCode') }}</v-btn>
                                </v-col>
                              </v-row>
                              <v-row no-gutters v-else>
                                <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                                  <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  overflow: hidden; white-space: nowrap; text-overflow: ellipsis; max-width: 120px;">{{conditionData.coupon_name }}</span><br>
                                  <span> {{ $t('ShopPage.Minimum') }} {{conditionData.spend_minimum}} {{ $t('ShopPage.THB') }}</span><br>
                                  <span class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;"></span><br>
                                  <span v-if="conditionData.couponDate.useEndDate !== null" class="mb-4" style="font-size: 10px;"> {{ $t('ShopPage.ValidUntil') }} {{conditionData.couponDate.useEndDate}}</span>
                                  <span v-else style="font-size: 10px;">{{ $t('ShopPage.NoExpiry') }}</span>
                                  <v-col cols="12" md="12" class="pa-0">
                                    <!-- <v-row no-gutters>
                                      <v-col cols="8" md="8" class="pa-0 pt-2">
                                        <v-progress-linear color="#C04F36" class="powerOfEndDate" :value="item.powerOfEndDate"></v-progress-linear>
                                      </v-col>
                                      <v-col cols="4" md="4" class="pl-2 pb-2"> <span style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{item.powerOfEndDate}} %</span></v-col>
                                    </v-row> -->
                                    <v-row no-gutters>
                                      <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="conditionData.powerOfEndDate">
                                        <template #progress="{ value }">
                                        <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                        </template>
                                      </v-progress-linear>
                                      <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">{{ $t('ShopPage.Used') }} {{conditionData.powerOfEndDate}} %</span>
                                    </v-row>
                                  </v-col>
                                </v-col>
                                <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                  <span style="color: #FB9372; font-size: 12px;"> {{conditionData.coupon_type === 'free_shipping' ? $t('ShopPage.FreeShippingCode') :  $t('ShopPage.Discount') }}</span><br>
                                  <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{conditionData.discount_amount}} {{conditionData.discount_type === 'percent'? '%': $t('ShopPage.THB') }}</span><br>
                                  <v-btn v-if="conditionData.is_collected === 'N'" @click="getCouponsCode(conditionData)" color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SaveCode') }}</v-btn>
                                  <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('ShopPage.SavedCode') }}</v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-col>
                    </v-col>
                    <v-col>
                      <v-col cols="12">
                        <span style="font-weight: 600;">{{ $t('ShopPage.PromotionType') }}</span><br>
                        <span class="pl-2">{{conditionData.coupon_type === 'free_product'?  $t('ShopPage.Freebie') :conditionData.coupon_type === 'discount'?  $t('ShopPage.ProductDiscount') : $t('ShopPage.ShippingDiscount') }}</span>
                      </v-col>
                      <v-col cols="12">
                        <span style="font-weight: 600;">{{ $t('ShopPage.CouponEligibility') }}</span><br>
                        <span v-if="conditionData.quota === null" class="pl-2">{{ $t('ShopPage.Unlimited') }}</span>
                        <span v-else class="pl-2">{{conditionData.quota}} {{ $t('ShopPage.Coupon') }}</span>
                      </v-col>
                      <v-col cols="12" v-if="conditionData.coupon_description !== null && conditionData.coupon_description !== ''">
                        <span style="font-weight: 600;">{{ $t('ShopPage.DiscountCodeDetails') }}</span><br>
                        <span class="pl-2" v-html="conditionData.coupon_description"></span>
                      </v-col>
                      <v-col cols="12" v-if="conditionData.coupon_product_free.length !== 0">
                        <span style="font-weight: 600;">{{ $t('ShopPage.FreebieDetails') }}</span><br>
                        <v-row no-gutters class="pa-0" v-for="(buyItem, index) in conditionData.coupon_product_free.product_details_buy" :key="'pair-' + index">
                          <template v-if="conditionData.coupon_product_free.product_details_free[index]">
                            <!-- รายการสั่งซื้อ -->
                            <v-col cols="12" class="pa-0 pt-2">
                              <span style="font-weight: 600;" class="pl-2">{{ $t('ShopPage.OrderList') }}</span>
                              <v-row no-gutters class="pa-0">
                                <v-col cols="4" class="pa-0" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="buyItem.image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span><b>{{ $t('ShopPage.ProductName') }}: </b>{{ buyItem.name }}</span><br>
                                  <span><b>{{ $t('ShopPage.Options') }}: </b>{{ buyItem.attribute_priority_1 }}{{ buyItem.attribute_priority_2 === '-' ? '' : `, ${buyItem.attribute_priority_2}` }}</span><br>
                                  <span><b>{{ $t('ShopPage.Quantity') }}: </b>{{ buyItem.quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- ของแถม -->
                            <v-col cols="12" class="pa-0 pt-2">
                              <!-- <span style="font-weight: 600;" class="pl-2">ของแถม</span> -->
                              <v-col class="text-center rounded-b-0" style="background-color: #E9F6F5; border-top-right-radius: 8px; border-top-left-radius: 8px;">
                                <span style="font-weight: 600; font-size: 14px; color: #27AB9C;" class="">ของแถม</span>
                              </v-col>
                              <v-row no-gutters class="pa-0 py-2" style="background-color: #F5F5F5; border: 2px solid #E9F6F5; border-top-width: 0px;">
                                <v-col cols="4" class="pa-0" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="conditionData.coupon_product_free.product_details_free[index].image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span><b>{{ $t('ShopPage.ProductName') }}: </b>{{ conditionData.coupon_product_free.product_details_free[index].name }}</span><br>
                                  <span><b>{{ $t('ShopPage.Options') }}: </b>{{ conditionData.coupon_product_free.product_details_free[index].attribute_priority_1 }}{{ conditionData.coupon_product_free.product_details_free[index].attribute_priority_2 === '-' ? '' : `, ${conditionData.coupon_product_free.product_details_free[index].attribute_priority_2}` }}</span><br>
                                  <span><b>{{ $t('ShopPage.Quantity') }}: </b>{{ conditionData.coupon_product_free.product_details_free[index].quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- Divider -->
                            <v-col cols="12" v-if="index < conditionData.coupon_product_free.product_details_buy.length - 1" class="py-2">
                              <v-divider></v-divider>
                            </v-col>
                          </template>
                        </v-row>
                      </v-col>
                    </v-col>
                  </v-row>
                </v-col>
              </v-card>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialogListPartner" width="540" persistent>
            <v-card style="border-radius: 24px; background-color: #27AB9C;">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
              <v-btn icon dark @click="closed()">
                  <v-icon color="#FFFFFF">mdi-arrow-left</v-icon>
                </v-btn>
                <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">{{ $t('ShopPage.CustomerList') }}
                </span>
                <v-btn icon dark @click="closed()">
                  <v-icon color="#FFFFFF">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card width="540" class="pa-4">
                <v-card-text class="pa-0">
                  <v-col cols="12">
                    <v-row no-gutters>
                      <v-col :cols="canManageCus === 'N'? 12: 8" :md="canManageCus === 'N' ? 12:8" :sm="canManageCus === 'N' ? 12: 8" class="pr-2">
                        <v-text-field
                          v-model="search"
                          dense
                          hide-details
                          outlined
                          :placeholder="$t('ShopPage.FindNamePartner')"
                          append-icon="mdi-magnify"
                          style="border-radius: 8px;"
                        ></v-text-field>
                      </v-col>
                      <!-- <v-col cols="4" align="end"><v-btn color="#27AB9C" style="border-radius: 8px; color: white;" @click="ManageCompanyAddress('add', dataCusList)">เพิ่มข้อมูลลูกค้า</v-btn></v-col> -->
                      <!-- <v-col cols="4" align="end"><v-btn color="#27AB9C" style="border-radius: 8px; color: white;" @click="addCustomerData()">เพิ่มข้อมูลลูกค้า</v-btn></v-col> -->
                      <v-col cols="4" md="4" sm="4" align="end" v-if="canManageCus === 'Y'">
                        <v-btn v-if="!MobileSize" class="addAddress" color="#27AB9C" style="border-radius: 8px; color: white; font-size: 0.75rem;" @click="addCustomerData()">{{ $t('ShopPage.AddCustomerInfo') }}</v-btn>
                        <v-btn v-else color="#27AB9C" style="border-radius: 8px; color: white; font-size: 0.75rem;" @click="addCustomerData()">{{ $t('ShopPage.AddInfo') }}</v-btn>
                      </v-col>
                    </v-row>
                    <v-col cols="12" class="px-0" align="center" v-for="(dataList, i) in filteredItems " :key="i.id"  >
                      <v-btn class="addAddress" block @click="openAddress(dataList.id, dataList.cus_code)" color="#27ab9c" outlined >{{dataList.cus_name}}</v-btn>
                    </v-col>
                    <v-col cols="12" align="end" v-if="dataCusListAll.length !== 0 && search === ''">
                      <v-pagination
                      color="#27AB9C"
                      v-model="pageListcustomer"
                      :length="pageMaxListcustomer"
                      circle
                    > </v-pagination>
                    </v-col>
                    <!-- <span>ไม่มีรายชื่อที่คุณค้นหา</span> -->
                  </v-col>
                </v-card-text>
              </v-card>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialogListAddressCustomer" width="650" persistent>
            <v-card style="border-radius: 24px; background-color: #27AB9C;">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
                <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">{{ $t('ShopPage.SelectAddress') }}
                </span>
                <!-- <v-btn icon dark @click="dialogListAddressCustomer = false">
                  <v-icon color="#FFFFFF">mdi-close</v-icon>
                </v-btn> -->
              </v-toolbar>
              <v-card class="pa-4">
                <v-col cols="12" class="pt-2">
                  <v-row dense class="mt-0" vt-if="userdetail.length !== 0">
                    <v-col cols="12">
                      <span style="font-size:18px; font-weight: 700;">{{ $t('ShopPage.AllAddresses') }} {{totalAddress}} {{ $t('ShopPage.ItemList') }}</span>
                    </v-col>
                    <v-col cols="12" class="pt-2" v-for="(item, index) in userdetail" :key="index">
                      <v-card class="addAddress" v-model="item.default_address" @click="setDefaultAdress(item)" min-height="149" elevation="0" outlined style="border-radius: 8px;" :style="item.default_address === 'Y' ? 'border-color: #27AB9C' : 'border-color: #C4C4C4'">
                      <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
                        <v-row class="pa-4">
                          <v-col cols="12" md="12">
                            <v-row dense>
                              <!-- action -->
                              <v-col cols="12" md="12" class="d-flex">
                                <div class="mr-auto" >
                                  <span style="font-weight: 700; font-size: 18px;">{{ item.name }}</span>
                                </div>
                              </v-col>
                              <v-col cols="12" md="12">
                                <span v-snip="3" style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.detail_address }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.zip_code }}</span>
                              </v-col>
                              <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                              <v-col cols="12" md="12">
                                <v-radio-group v-model="item.default_address">
                                  <v-radio
                                    color="#27AB9C"
                                    :label="$t('AddressProfilePage.SetDefaultAddress')"
                                    value="Y"
                                    style="color: #333333"
                                  ></v-radio>
                                </v-radio-group>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                    <v-col cols="12" align="end" v-if="canManageCus === 'Y'">
                      <v-btn color="#27AB9C" block class="addAddress" outlined @click="addAddressCus('addAddress')"><v-icon>mdi-home-plus</v-icon> {{ $t('ShopPage.AddNewAddress') }}</v-btn>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-pagination
                        color="#27AB9C"
                        v-model="pageAddress"
                        :length="pageMaxAddress"
                        circle
                      > </v-pagination>
                      <!-- <v-btn v-if="SaleCanAddCus === 'Y'" color="#27AB9C" style="color: white;" @click="addAddressCus('addAddress')">{{ $t('ShopPage.AddNewAddress') }}</v-btn> -->
                    </v-col>
                  </v-row>
                </v-col>
              </v-card>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialogPartner" width="752px" persistent scrollable>
            <v-form ref="formPartner" :lazy-validation="lazy">
              <v-card style="border-radius: 24px; background-color: #27AB9C;">
                <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
                  <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">{{ $t('ShopPage.PartnerRequest') }}
                  </span>
                  <v-btn icon dark @click="closeModal()">
                    <v-icon color="#FFFFFF">mdi-close</v-icon>
                  </v-btn>
                </v-toolbar>
                <v-card style="border-radius: 20px;">
                  <v-card-text>
                    <v-col>
                      <v-row no-gutters>
                        <v-col cols="12">
                          <v-row no-gutters>
                            <v-col cols="1" md="1" sm="2" style="text-align-last: start;">
                              <v-avatar rounded="50" size="62">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/Shop/Store2.png"
                                ></v-img>
                              </v-avatar>
                            </v-col>
                            <v-col cols="11" md="11" sm="10" class="pl-0">
                              <v-row dense no-gutters justify="start">
                                <v-col cols="12" md="12" sm="12" xs="12">
                                  <p
                                    :class="!MobileSize ? 'mt-5 ml-4' : 'mt-5 ml-12'"
                                    :style="!MobileSize ?
                                      'font-weight: 600; font-size: 16px; text-transform: uppercase; color: #333333;': 'font-weight: 600; font-size: 14px; text-transform: uppercase; color: #333333;'"
                                  > {{ $t('ShopPage.StoreName') }} :
                                    {{
                                      dataShop.shop_name_en === null
                                        ? dataShop.shop_name_th
                                        : dataShop.shop_name_en
                                    }}
                                  </p>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                      <v-spacer :style="MobileSize ? 'border-top: 1px solid #F2F2F2; margin-top: 24px;': 'border-top: 1px solid #F2F2F2; margin-top: 31px;'"></v-spacer>
                    </v-col>
                    <div>
                      <v-col cols="12">
                        <span style="font-weight: 600; font-size: 16px; text-transform: uppercase; color: #333333;">{{ $t('ShopPage.PartnerRequestDetails') }}</span>
                      </v-col>
                    <v-col cols="12" class="pb-0">
                        <span
                          style="
                            font-weight: 400;
                            font-size: 16px;
                            line-height: 22px;
                            color: #333333;
                          "
                          >{{ $t('ShopPage.Email') }} <span style="color: red"> {{ $t('ShopPage.EmailNote') }}</span></span
                        >
                      <v-form ref="EmailForm">
                        <v-text-field
                          style="border-radius: 8px;"
                          v-model="email"
                          :rules="Rules.Email"
                          dense
                          outlined
                          @input="checkEmaildis()"
                          :placeholder="this.$t('ShopPage.EmailNote2')"
                        ></v-text-field>
                      </v-form>
                      </v-col>
                    </div>
                    <div
                      v-if="
                        this.response_doc.length !== 0 &&
                        this.response_doc.document_list.length !== 0
                      "
                    >
                      <div
                      class="pt-5"
                      style="margin-left: 12px; margin-right: 12px"
                        v-if="
                          this.response_doc.document_list[0].tier_id !== null ||
                          this.response_doc.document_list[0].tier_id === '-'
                        "
                      >
                        <span
                          style="
                            border-radius: 8px;
                            font-weight: 400;
                            font-size: 16px;
                            line-height: 22px;
                            color: #333333;
                          "
                          >{{ $t('ShopPage.CustomerGroup') }} <span style="color: red">*</span></span
                        >
                        <v-select
                          v-model="selectgroup"
                          :items="response_doc.document_list"
                          return-object
                          item-text="tier_name"
                          outlined
                          dense
                          :placeholder="this.$t('ShopPage.SpecifyGroup')"
                          @input="checkDocOfTier(selectgroup)"
                          :rules="[itemRules.docCheck(selectgroup)]"
                          required
                        ></v-select>
                      </div>
                    </div>
                    <div
                      v-if="
                        this.response_doc.length !== 0 &&
                        this.response_doc.document_list.length !== 0
                      "
                    >
                      <v-col class="pl-3" v-if="datapartner.length !== 0 && selectgroup.document.length !== 0 ">
                        <span style="font-size: 16px; font-weight: 600; line-height: 22.4px; color: #333333;">{{ $t('ShopPage.RequiredDocuments') }} <span style="font-size: 12px; font-weight: 400; line-height: 16.8px; color: #CCCCCC;">{{ $t('ShopPage.UploadNote') }}</span></span>
                      </v-col>
                      <v-col class="pl-3" v-else-if="datapartner.length !== 0 && (response_doc.document_list[0].tier_id === null || response_doc.document_list[0].tier_id === '-') && response_doc.document_list[0].document.length !== 0">
                        <span style="font-size: 16px; font-weight: 600; line-height: 22.4px; color: #333333;">{{ $t('ShopPage.RequiredDocuments') }} <span style="font-size: 12px; font-weight: 400; line-height: 16.8px; color: #CCCCCC;">{{ $t('ShopPage.UploadNote') }}</span></span>
                      </v-col>
                      <div
                        v-for="(item, index) in response_doc.document_list[0].tier_id === null ? datapartner[0].document : selectgroup.document"
                        :key="index"
                      >
                        <v-col class="pt-0" v-if="item.length !== 0">
                          <v-card class="mt-4 rounded-lg" style="box-shadow: none; border: solid 1px #F3F5F7;" v-if="MobileSize">
                            <v-row class="pa-2">
                              <v-col cols="4" md="2" xs="2" sm="4">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png"
                                  width="55px"
                                  height="55px"
                                  contain
                                >
                                </v-img>
                              </v-col>
                              <v-col cols="7" md="7" xs="12" sm="7">
                                <v-row no-gutters>
                                  <v-col cols="12">
                                    <v-layout
                                      row
                                      wrap
                                      align-center
                                      style="margin-top: 5px"
                                    >
                                      <v-flex>
                                        <span
                                          style="
                                            font-weight: 600;
                                            font-size: 16px;
                                            line-height: 22px;
                                            color: #333333;
                                          "
                                          >{{ item.name_document }}</span
                                        >
                                      </v-flex>
                                    </v-layout>
                                  </v-col>
                                  <v-col cols="12">
                                    <v-layout
                                      row
                                      wrap
                                      align-center
                                      style="margin-top: 0.5px"
                                    >
                                      <v-flex>
                                        <v-row no-gutters>
                                          <v-col
                                            v-if="selectgroup.document.length !== 0"
                                            cols="12"
                                            md="3"
                                            xs="12"
                                            sm="3"
                                            style="text-align-last: end;"
                                          >
                                            <v-btn
                                              v-if="
                                                !item.InputFile &&
                                                selectgroup.document[index].status ===
                                                  false
                                              "
                                              rounded
                                              dense
                                              dark
                                              outlined
                                              color="#27AB9C"
                                              @click="onPickFile(index); selectIndex(index)"
                                              style="height: 30px; width: 100px; font-size: 10px;"
                                            >
                                              <v-file-input
                                                v-model="DataFile"
                                                :items="DataFile"
                                                accept=".pdf"
                                                @change="UploadFile($event)"
                                                id="file_input"
                                                multiple
                                                :clearable="false"
                                                style="display: none"
                                              >
                                              </v-file-input>
                                              <v-img src="@/assets/ImageINET-Marketplace/Shop/iconShop/upload.png"></v-img>
                                              <span class="ml-2">{{ $t('ShopPage.UploadFile') }}</span>
                                            </v-btn>
                                            <!-- <v-row
                                              v-if="
                                                item.InputFile &&
                                                selectgroup.document[index].status ===
                                                  false
                                              "
                                              class="d-flex justify-end"
                                              :class="MobileSize ? 'pr-4':'mt-2 pr-4'"
                                            >
                                              <v-btn
                                                icon
                                                @click="show_inputimagse(index, item)"
                                              >
                                                <v-icon color="#27AB9C"
                                                  >mdi-chevron-up</v-icon
                                                >
                                              </v-btn>
                                            </v-row> -->
                                            <v-row
                                              v-if="
                                                selectgroup.document[index].status ===
                                                true
                                              "
                                              class="d-flex justify-start mt-2"
                                            >
                                              <!-- <v-btn icon>
                                                <v-icon @click="openToPDF(index)"
                                                  >mdi-eye</v-icon
                                                >
                                              </v-btn>
                                              <v-btn @click="delete_file(index)" icon>
                                                <v-icon>mdi-delete</v-icon>
                                              </v-btn> -->
                                              <v-chip style="border-radius: 40px !important;" @click="openToPDF(index)" color="#1B5DD61A"><p class="text-truncate mb-0 mt-1 px-0" style=" color: #1B5DD6; border-bottom: solid 1px; font-size: 14px; font-weight: 400; line-height: 19.6px;">{{nameDoc[index].name}}</p>
                                                <v-btn x-small color="#1B5DD6" @click="delete_file(index)" icon>
                                                  <v-icon>mdi-close</v-icon>
                                                </v-btn>
                                              </v-chip>
                                            </v-row>
                                          </v-col>
                                          <v-col v-else cols="12" md="3" xs="12" sm="3" style="text-align-last: end;">
                                            <v-btn
                                              v-if="
                                                !item.InputFile &&
                                                item.status === false
                                              "
                                              rounded
                                              dense
                                              dark
                                              outlined
                                              color="#27AB9C"
                                              @click="onPickFile(index); selectIndex(index)"
                                              style="height: 40px; margin-top: 5px; width: 149px;"
                                            >
                                              <v-file-input
                                                v-model="DataFile"
                                                :items="DataFile"
                                                accept=".pdf"
                                                @change="UploadFile($event)"
                                                id="file_input"
                                                multiple
                                                :clearable="false"
                                                style="display: none"
                                              >
                                              </v-file-input>
                                              <v-img src="@/assets/ImageINET-Marketplace/Shop/iconShop/upload.png"></v-img>
                                              <span class="ml-2">{{ $t('ShopPage.UploadFile') }}</span>
                                            </v-btn>
                                            <!-- <v-row
                                              v-if="
                                                item.InputFile &&
                                                item.status === false
                                              "
                                              class="d-flex justify-end"
                                              :class="MobileSize ? 'pr-4':'mt-2 pr-4'"
                                            >
                                              <v-btn
                                                icon
                                                @click="show_inputimagse(index, item)"
                                              >
                                                <v-icon color="#27AB9C"
                                                  >mdi-chevron-up</v-icon
                                                >
                                              </v-btn>
                                            </v-row> -->
                                            <v-row
                                              v-if="item.status === true"
                                              class="d-flex justify-start mt-2"
                                            >
                                              <!-- <v-btn icon>
                                                <v-icon @click="openToPDF(index)"
                                                  >mdi-eye</v-icon
                                                >
                                              </v-btn>
                                              <v-btn @click="delete_file(index)" icon>
                                                <v-icon>mdi-delete</v-icon>
                                              </v-btn> -->
                                              <v-chip style="border-radius: 40px !important;" @click="openToPDF(index)" color="#1B5DD61A"><p class="text-truncate mb-0 mt-1 px-0" style=" color: #1B5DD6; border-bottom: solid 1px; font-size: 14px; font-weight: 400; line-height: 19.6px;">{{nameDoc[index].name}}</p>
                                                <v-btn x-small color="#1B5DD6" @click="delete_file(index)" icon>
                                                  <v-icon>mdi-close</v-icon>
                                                </v-btn>
                                              </v-chip>
                                            </v-row>
                                          </v-col>
                                        </v-row>
                                      </v-flex>
                                    </v-layout>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-card>
                          <v-card class="mt-4 rounded-lg" style="box-shadow: none; border: solid 1px #F3F5F7;" v-else>
                            <v-row class="pa-2">
                              <v-col cols="3" md="2" xs="2" sm="2">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png"
                                  width="55px"
                                  height="55px"
                                  contain
                                >
                                </v-img>
                              </v-col>
                              <v-col cols="7" md="6" xs="12" sm="6">
                                <v-layout row wrap align-center style="margin-top: 10px">
                                  <v-flex>
                                    <span
                                      style="
                                        font-weight: 600;
                                        font-size: 16px;
                                        line-height: 22px;
                                        color: #333333;
                                      "
                                      >{{ item.name_document }}</span
                                    >
                                  </v-flex>
                                </v-layout>
                              </v-col>
                              <v-col
                                v-if="selectgroup.document.length !== 0"
                                cols="12"
                                md="4"
                                xs="12"
                                sm="4"
                                style="text-align-last: end;"
                              >
                                <v-btn
                                  v-if="
                                    !item.InputFile &&
                                    selectgroup.document[index].status === false
                                  "
                                  rounded
                                  dense
                                  dark
                                  outlined
                                  color="#27AB9C"
                                  @click="onPickFile(index); selectIndex(index)"
                                  style="height: 40px; margin-top: 5px; width: 149px;"
                                >
                                  <v-file-input
                                    v-model="DataFile"
                                    :items="DataFile"
                                    accept=".pdf"
                                    @change="UploadFile($event)"
                                    id="file_input"
                                    multiple
                                    :clearable="false"
                                    style="display: none"
                                  >
                                  </v-file-input>
                                  <v-img src="@/assets/ImageINET-Marketplace/Shop/iconShop/upload.png"></v-img>
                                  <span class="ml-2">{{ $t('ShopPage.UploadFile') }}</span>
                                </v-btn>
                                <!-- <v-row
                                  v-if="
                                    item.InputFile &&
                                    selectgroup.document[index].status === false
                                  "
                                  class="d-flex justify-end"
                                  :class="MobileSize ? 'pr-4':'mt-2 pr-4'"
                                >
                                  <v-btn icon @click="show_inputimagse(index, item)">
                                    <v-icon color="#27AB9C">mdi-chevron-up</v-icon>
                                  </v-btn>
                                </v-row> -->
                                <v-row
                                  v-if="selectgroup.document[index].status === true"
                                  class="d-flex justify-end mt-2 pr-4"
                                >
                                  <!-- <v-btn icon>
                                    <v-icon @click="openToPDF(index)">mdi-eye</v-icon>
                                  </v-btn> -->

                                  <v-chip style="border-radius: 40px !important;" @click="openToPDF(index)" color="#1B5DD61A"><p class="text-truncate mb-0 mt-1 px-0" style=" color: #1B5DD6; border-bottom: solid 1px; font-size: 14px; font-weight: 400; line-height: 19.6px;">{{nameDoc[index].name}}</p>
                                    <v-btn x-small color="#1B5DD6" @click="delete_file(index)" icon>
                                      <v-icon>mdi-close</v-icon>
                                    </v-btn>
                                  </v-chip>
                                </v-row>
                              </v-col>
                              <v-col v-else cols="12" md="3" xs="12" sm="3" style="text-align-last: end;">
                                <v-btn
                                  v-if="
                                    !item.InputFile && item.status === false
                                  "
                                  rounded
                                  dense
                                  dark
                                  outlined
                                  color="#27AB9C"
                                  @click="onPickFile(index); selectIndex(index)"
                                  style="height: 40px; margin-top: 5px; width: 149px;"
                                >
                                  <v-file-input
                                    v-model="DataFile"
                                    :items="DataFile"
                                    accept=".pdf"
                                    @change="UploadFile($event)"
                                    id="file_input"
                                    multiple
                                    :clearable="false"
                                    style="display: none"
                                  >
                                  </v-file-input>
                                  <v-img src="@/assets/ImageINET-Marketplace/Shop/iconShop/upload.png"></v-img>
                                  <span class="ml-2">{{ $t('ShopPage.UploadFile') }}</span>
                                </v-btn>
                                <!-- <v-row
                                  v-if="
                                    item.InputFile && item.status === false
                                  "
                                  class="d-flex justify-end "
                                  :class="MobileSize ? 'pr-4':'mt-2 pr-4'"
                                >
                                  <v-btn icon @click="show_inputimagse(index, item)">
                                    <v-icon color="#27AB9C">mdi-chevron-up</v-icon>
                                  </v-btn>
                                </v-row> -->
                                <v-row
                                  v-if="item.status === true"
                                  class="d-flex justify-end mt-2 pr-4"
                                >
                                  <!-- <v-btn icon>
                                    <v-icon @click="openToPDF(index)">mdi-eye</v-icon>
                                  </v-btn>
                                  <v-btn @click="delete_file(index)" icon>
                                    <v-icon>mdi-delete</v-icon>
                                  </v-btn> -->
                                  <v-chip style="border-radius: 40px !important;" @click="openToPDF(index)" color="#1B5DD61A"><p class="text-truncate mb-0 mt-1 px-0" style=" color: #1B5DD6; border-bottom: solid 1px; font-size: 14px; font-weight: 400; line-height: 19.6px;">{{nameDoc[index].name}}</p>
                                    <v-btn x-small color="#1B5DD6" @click="delete_file(index)" icon>
                                      <v-icon>mdi-close</v-icon>
                                    </v-btn>
                                  </v-chip>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-card>
                        </v-col>
                      </div>
                    </div>
                  </v-card-text>
                  <v-card-actions>
                    <v-container style="display: flex; justify-content: flex-end">
                      <v-col cols="6" class="pa-0">
                        <v-btn
                          rounded
                          dense
                          dark
                          outlined
                          color="#27AB9C"
                          class="pl-7 pr-7 mt-2"
                          @click="closeModal()"
                        >
                          {{ $t('ShopPage.Cancel') }}
                        </v-btn>
                      </v-col>
                      <v-col cols="6" style="text-align: end;" class="pa-0">
                        <v-btn
                        v-if="this.datapartner.length !== 0"
                          rounded
                          dense
                          color="#27AB9C"
                          class="ml-4 mt-2 pl-8 pr-8 white--text"
                          @click="confirm()"
                          :disabled="!checkDoc && emailCheck ? false : true"
                        >
                          <!-- ส่งคำขอ มีเอกสาร -->
                          {{ $t('ShopPage.SubmitRequest') }}
                        </v-btn>
                        <v-btn
                        v-else
                          rounded
                          dense
                          color="#27AB9C"
                          class="ml-4 mt-2 pl-8 pr-8 white--text"
                          @click="confirm()"
                          :disabled="emailCheck ? false : true"
                        >
                          <!-- ส่งคำขอ ไม่มีเอกสาร-->
                          {{ $t('ShopPage.SubmitRequest') }}
                        </v-btn>
                      </v-col>
                    </v-container>
                  </v-card-actions>
                </v-card>
              </v-card>
            </v-form>
          </v-dialog>
          <v-dialog v-model="ModalCreate" width="600px" persistent scrollable>
            <v-card>
              <v-toolbar align="center" color="#BDE7D9" dark dense>
                <span class="flex text-center ml-5" style="font-size: 20px">
                  <font color="#27AB9C">{{ $t('ShopPage.CreateQuotation') }}</font>
                </span>
                <v-btn icon dark @click="ModalCreate = false">
                  <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card-text>
                <v-row class="mt-4">
                  <v-col cols="12" md="6" sm="6">
                    <span style="line-height: 24px; font-size: 16px; color: #333333"
                      >{{ $t('ShopPage.Budget') }}</span
                    >
                    <v-select
                      v-model="selectBudget"
                      :items="itemBudget"
                      item-text="text"
                      item-value="value"
                      outlined
                      dense
                    ></v-select>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span style="line-height: 24px; font-size: 16px; color: #333333"
                      >{{ $t('ShopPage.DeductBudget') }}</span
                    >
                    <v-select
                      v-model="selectCutBudget"
                      :items="itemCutBudget"
                      item-text="text"
                      item-value="value"
                      outlined
                      dense
                    ></v-select>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions>
                <v-container style="display: flex; justify-content: flex-end">
                  <v-btn
                    dense
                    dark
                    outlined
                    color="#27AB9C"
                    class="pl-7 pr-7 mt-2"
                    @click="ModalCreate = false"
                  >
                    {{ $t('ShopPage.Cancel') }}
                  </v-btn>
                  <v-btn
                    dense
                    color="#27AB9C"
                    class="ml-4 mt-2 pl-8 pr-8 white--text"
                    @click="CreateQuotation()"
                  >
                    {{ $t('ShopPage.Confirm') }}
                  </v-btn>
                </v-container>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialog_pdf" width="1000px" persistent scrollable>
            <v-card>
              <v-toolbar align="center" color="#BDE7D9" dark dense>
                <span class="flex text-center ml-5" style="font-size: 20px">
                  <font color="#27AB9C">{{ $t('ShopPage.DocumentSample') }}</font>
                </span>
                <v-btn icon dark @click="dialog_pdf = false">
                  <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <object
                width="1000"
                height="700"
                :data="pdftofile"
                type="application/pdf"
              ></object>
            </v-card>
          </v-dialog>
          <!-- <v-img src="@/assets/Group_1504775827.png"></v-img> -->
          <v-dialog
            v-model="dialog_partner"
            content-class="elevation-0"
            width="752"
            v-if="RowUserData === 'purchaser' && statusShop && !MobileSize"
          >
            <!-- <div style="display: flex; justify-content: center">
              <v-img
                src="@/assets/Group_112034567.png"
                style="z-index: 10; position: absolute; width: 750px"
              ></v-img>
            </div> -->
            <div class="d-flex justify-center">
              <v-card
                class="d-flex justify-center"
                style="
                  background: #f9f9f9;
                  padding-left: 30px;
                  padding-right: 30px;
                  padding-bottom: 30px;
                  border-radius: 24px;
                "
              >
                <div>
                  <!-- <div
                    class="d-flex justify-end pt-2"
                    style="margin-top: -50px; margin-right: -20px"
                  >
                  </div> -->
                  <v-row dense justify="end" class="pt-4">
                    <v-icon color="#CCCCCC" @click="dialog_partner = false" size="30"
                    >mdi-close</v-icon>
                  </v-row>
                  <div class="d-flex justify-center">
                    <v-img
                      src="@/assets/ImageINET-Marketplace/Shop/Frame.png"
                      max-width="497"
                      max-height="356"
                      height="100%"
                      width="100%"
                      style="margin-top: 16px ; margin-bottom: 40px;"
                    >
                    </v-img>
                  </div>
                  <div class="d-flex justify-center">
                    <h1
                      style="
                        align-self: center;
                        color: #333333;
                        font-family: Poppins;
                        font-size: 28px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 39.2px;
                      "
                    >
                      {{ $t('ShopPage.RegisteredOnly') }}
                      <!-- {{statusShop}} -->
                    </h1>
                  </div>
                  <!-- <div class="d-flex justify-end" style="margin-top:-50px; margin-right:-20px">
                    <v-btn icon style="color:red;" @click="OpenModalPartner()"><v-icon style="font-size:40px">mdi-close-circle-outline</v-icon></v-btn>
                  </div> -->
                  <v-row class="py-3 px-14">
                    <p
                      style="
                        color: #9A9A9A;
                        font-family: Poppins;
                        font-size: 18px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 25.2px;
                      "
                    >
                      {{ $t('ShopPage.MustRegisterToView') }}
                    </p>
                  </v-row>
                  <v-row justify="center" class="pb-7">
                    <v-btn
                    rounded
                    color="#27AB9C"
                    class="px-16"
                    v-if="contact && !chackpartner"
                      @click="OpenModalPartner()"
                    ><a
                      style="
                        color: #FFFFFF;
                        font-family: Poppins;
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 22.4px;
                      ">{{ $t('ShopPage.RegisterAsPartner') }}</a>
                    </v-btn></v-row>
                    <v-row justify="center" v-if="!contact && !chackpartner">
                    <p
                      style="
                        color: #27AB9C;
                        font-family: Poppins;
                        font-size: 18px;
                        font-style: normal;
                        line-height: 32px;
                      "
                    >{{ $t('ShopPage.OrContactSupport') }}
                      {{this.dataShop.shop_phone}}
                    </p>
                    </v-row>
                </div>
              </v-card>
            </div>
          </v-dialog>
          <v-dialog
            v-model="dialog_partner"
            content-class="elevation-0"
            v-else-if="RowUserData === 'purchaser' && statusShop && MobileSize"
          >
            <!-- <div class="justify-center"> -->
              <v-card
                class="d-flex justify-center"
                style="
                  background: #f9f9f9;
                  padding-left: 20px;
                  padding-right: 20px;
                  padding-bottom: 30px;
                  padding-top: 10px;
                  margin-top: 10px;
                  border-radius: 24px;
                "
              >
                <div>
                  <v-app-bar flat color="rgba(0, 0, 0, 0)">
                    <v-spacer></v-spacer>
                    <v-btn @click="dialog_partner = false" color="#CCCCCC" icon >
                      <v-icon>mdi-close</v-icon>
                    </v-btn>
                  </v-app-bar>
                  <v-col style="text-align-last: center;">
                  <!-- <div class="d-flex justify-center"> -->
                    <v-col style="text-align: -webkit-center;">
                      <v-img
                        src="@/assets/ImageINET-Marketplace/Shop/Frame.png"
                        style="max-width: 240px; max-height: 200px;"
                      ></v-img>
                    </v-col>
                  <!-- </div> -->
                  <!-- <div class="d-flex justify-center"> -->
                    <h1
                      style="
                        align-self: center;
                        color: #333333;
                        font-family: Poppins;
                        font-size: 15px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 39.2px;
                      "
                    >
                      {{ $t('ShopPage.RegisteredOnly') }}
                      <!-- {{statusShop}} -->
                    </h1>
                  <!-- </div> -->
                  <!-- <div class="d-flex justify-end" style="margin-top:-50px; margin-right:-20px">
                    <v-btn icon style="color:red;" @click="OpenModalPartner()"><v-icon style="font-size:40px">mdi-close-circle-outline</v-icon></v-btn>
                  </div> -->
                  <v-row no-gutters justify="center">
                    <p
                      style="
                        color: #9A9A9A;
                        font-family: Poppins;
                        font-size: 10px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 25.2px;
                      "
                    >
                      {{ $t('ShopPage.MustRegisterToView') }}
                    </p>
                  </v-row>
                  <v-row justify="center" class="pb-7">
                    <v-btn
                    rounded
                    color="#27AB9C"
                    class="px-16"
                    v-if="contact && !chackpartner"
                      @click="OpenModalPartner()"
                    ><a
                      style="
                        color: #FFFFFF;
                        font-family: Poppins;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 22.4px;
                      ">{{ $t('ShopPage.RegisterAsPartner') }}</a>
                    </v-btn></v-row>
                    <v-row justify="center" v-if="!contact && !chackpartner">
                    <p
                      style="
                        color: #27AB9C;
                        font-family: Poppins;
                        font-size: 18px;
                        font-style: normal;
                        line-height: 32px;
                      "
                    >{{ $t('ShopPage.OrContactSupport') }}
                      {{this.dataShop.shop_phone}}
                    </p>
                    </v-row>
                    </v-col>
                </div>
              </v-card>
            <!-- </div> -->
          </v-dialog>
          <v-dialog v-model="dialogConfirm" width="424" persistent>
            <v-card :height=" !MobileSize? '452':'500'" style="border-radius: 24px !important; background: #FFFFFF;">
              <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/Shop/iconShop/warn.png')">
                <v-app-bar flat color="rgba(0, 0, 0, 0)">
                  <v-toolbar-title></v-toolbar-title>
                  <v-spacer></v-spacer>
                  <v-btn @click="dialogConfirm = false" color="#CCCCCC" icon >
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </v-app-bar>
              </v-img>
              <v-card-text style="text-align: center;">
                <p style="font-weight: 700; font-size: 24px; line-height: 33.6px; color: #333333;" class="my-4"><b>{{ $t('ShopPage.PartnerRequest') }}</b></p>
                <span style="font-weight: 400; font-size: 16px; line-height: 22.4px; color: #9A9A9A;">{{ $t('ShopPage.ConfirmPartnerRequest') }}</span>
              </v-card-text>
              <v-card-text>
                <v-row dense justify="center" class="pt-3">
                  <v-btn width="156" height="38" outlined rounded color="#27AB9C" :class="!MobileSize? 'mr-4': ''" @click="OpenModalPartner()">{{ $t('ShopPage.Cancel') }}</v-btn>
                  <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="SavePartner()">{{ $t('ShopPage.Confirm') }}</v-btn>
                </v-row>
              </v-card-text>
            </v-card>
          </v-dialog>
          <v-dialog v-model="dialogSuccess" width="424" persistent>
              <v-card height="452" style="border-radius: 24px !important; background: #FFFFFF;">
                <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/Shop/iconShop/correct.png')">
                  <v-app-bar flat color="rgba(0, 0, 0, 0)">
                    <v-toolbar-title></v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn @click="dialogSuccess = false" color="#CCCCCC" icon >
                      <v-icon>mdi-close</v-icon>
                    </v-btn>
                  </v-app-bar>
                </v-img>
                <v-card-text style="text-align: center;">
                  <p style="font-weight: 700; font-size: 24px; line-height: 33.6px; color: #333333;" class="my-4"><b>{{ $t('ShopPage.PartnerRequestSent') }}</b></p>
                  <span style="font-weight: 400; font-size: 16px; line-height: 22.4px; color: #9A9A9A;">{{ $t('ShopPage.AlreadyRequested') }}</span>
                </v-card-text>
                <v-card-text class="pt-3">
                  <v-row dense justify="center">
                    <v-btn @click="dialogSuccess = false" width="156" height="38" class="white--text" rounded color="#27AB9C">{{ $t('ShopPage.Confirm') }}</v-btn>
                  </v-row>
                </v-card-text>
              </v-card>
          <!-- </v-container> -->
          </v-dialog>
          <v-dialog v-model="dialogFunnelMobile">
            <v-card height="248" style="border-radius: 24px !important; background: #FFFFFF;">
              <v-app-bar flat color="rgba(0, 0, 0, 0)">
                <v-row no-gutters justify="end">
                  <v-toolbar-title style="font-size: 16px !important; font-weight: 600 !important;">{{ $t('ShopPage.Filter') }}</v-toolbar-title>
                </v-row>
                <v-row no-gutters justify="end" >
                  <v-btn @click="dialogFunnelMobile = false" color="#CCCCCC" icon >
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </v-row>
              </v-app-bar>
              <v-card-text class="px-0" >
                <!-- ประเภท -->
                  <v-col cols="12">
                    <v-row>
                      <v-col class="py-0">
                        <v-row no-gutters>
                          <v-col cols="3" class="mt-2">
                            <span style="font-size: 16px; color:#333333;">
                              {{ $t('ShopPage.Type') }}:
                            </span>
                          </v-col>
                          <v-col cols="9">
                            <v-select
                              style="border-radius: 8px;"
                              outlined
                              dense
                              v-model="typeList"
                              :items="itemTypeList"
                              placeholder="เลือก"
                              hide-details
                            >
                            <template v-slot:selection="{ item }">
                                <span>{{ item.text | truncate(11, '...')}}</span>
                              </template>
                              <template v-slot:append>
                                <v-icon>mdi-chevron-down</v-icon>
                              </template>
                            </v-select>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- ราคา -->
                  <v-col cols="12">
                    <v-row>
                      <v-col class="pb-0">
                        <v-row no-gutters>
                          <v-col cols="3" class="mt-2">
                            <span style="font-size: 16px; color:#333333;">
                              {{ $t('ShopPage.Price') }} :
                            </span>
                          </v-col>
                          <v-col :cols="IpadSize? '8': '9'">
                            <v-select
                              style="border-radius: 8px;"
                              outlined
                              dense
                              v-model="priceList"
                              :items="itemPriceList"
                              placeholder="เลือก"
                              hide-details
                            >
                              <template v-slot:selection="{ item }">
                                <span>{{ item.text | truncate(10, '...') }}</span>
                              </template>
                              <template v-slot:append>
                                <v-icon>mdi-chevron-down</v-icon>
                              </template>
                            </v-select>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
              </v-card-text>
              <v-card-text class="pt-3 px-0">
                <v-row dense>
                  <v-col cols="6" align="end">
                    <v-btn @click="reSet()" :disabled="!typeList && !priceList" width="125" height="36" outlined rounded color="white"> <span :style="!typeList && !priceList? 'color:#CCCCCC;':'color:#27AB9C;'">{{ $t('ShopPage.Clear') }}</span></v-btn>
                  </v-col>
                  <v-col cols="6" align="start">
                    <v-btn @click="GetProductTypeAndPrice(typeList, priceList)" :disabled="!typeList && !priceList" width="125" height="36" class="white--text" rounded color="#27AB9C" >{{ $t('ShopPage.ConfirmAction') }}</v-btn>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-dialog>
          <!-- dialog Copy -->
          <!-- <v-dialog v-model='dialogCopy' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="dialogCopy = false">
            <v-card min-height='100%' style="background-color: #27AB9C;">
              <v-card-actions>
                <v-card-text style="text-align: center; color: white;">
                  {{ dialogCopyText }}
                </v-card-text>
                <v-spacer></v-spacer>
                <v-btn plain fab x-small @click='dialogCopy = false' icon><v-icon color='white'>mdi-close</v-icon></v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog> -->
          <ModalManageCustomerSale ref="ModalManageCustomerSale" />
          <ModalSpecialPriceRequest ref="ModalSpecialPriceRequest" />
          <ModalCouponShop ref="ModalCouponShop" />
          <EditModalAddress ref="EditModalAddress" />
          <!-- <EditModalAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page" :customerSaleData="customerSaleData" /> -->
          <!-- <ModalTaxAddress
          ref="ModalTaxAddress"
          :title="titleTaxAddress"
          :page="page"
        /> -->
        </v-container>
      </v-col>
    </v-container>
  </v-col>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import VueHorizontalList from 'vue-horizontal-list'
import { Decode, Encode } from '@/services'
import Media from '@dongido/vue-viaudio'
import { Row, Empty, Tabs } from 'ant-design-vue'
import { createChat } from '@/components/library/CallChatMe/callChatMe'
import { Youtube } from 'vue-youtube'
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
// const NewProduct = []
// const bestSeller = []
export default {
  metaInfo () {
    return {
      title: this.shopName + ' | Nex Gen Commerce',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        {
          vmid: 'description',
          name: 'description',
          content: this.shopName
        },
        {
          property: 'og:site_name',
          vmid: 'og:site_name',
          name: 'site_name',
          content: 'https://nexgencommerce.one.th/'
        },
        {
          property: 'og:title',
          vmid: 'og:title',
          name: 'title',
          content: this.shopName + ' | Nex Gen Commerce'
        },
        {
          property: 'og:description',
          vmid: 'og:description',
          content: this.shopName
        },
        { property: 'og:type', vmid: 'og:type', content: 'website' },
        { property: 'og:url', vmid: 'og:url', content: this.message }
        // { property: 'og:image', name: 'image', content: this.primaryImage },
        // { property: 'og:image:width', content: '640' },
        // { property: 'og:image:height', content: '480' }
      ]
    }
  },
  components: {
    VueHorizontalList,
    Media,
    Youtube,
    'a-row': Row,
    'a-empty': Empty,
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    ModalManageCustomerSale: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/CustomerSaleModel'),
    ModalSpecialPriceRequest: () => import('@/components/Modal/SpecialPriceModal'),
    ModalCouponShop: () => import('@/components/Modal/CouponShopPage'),
    EditModalAddress: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditAddressCustomerSale'),
    ListShopProduct: () => import('../Shop/ListShopProduct'),
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsMobile: () => import('@/components/Card/ProductCardResponsive'),
    FlashSaleShop: () => import('@/components/Shop/flashSaleShop'),
    Treeselect
  },
  data () {
    return {
      FacebookUrl: '',
      LineId: '',
      SaleOrderNoJVType: '',
      SaleVendor: false,
      PageTab: 0,
      pages: 1,
      cusID: 0,
      conditionData: '',
      dialogConditionCoupon: false,
      dialogAllCoupon: false,
      // dialogCopy: false,
      // dialogCopyText: '',
      shortLink: '',
      disabledPagination: false,
      playerVars: {
        autoplay: 0,
        controls: 1
      },
      currentPageArticle: 1,
      itemsPerPageArticle: 6,
      itemArticle: [],
      pageShop: '',
      // powerOfEndDateToShow: 0,
      optionsCard: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 1 },
          { start: 768, end: 992, size: 2 },
          { start: 992, end: 1200, size: 3 },
          { start: 1200, end: 1300, size: 4 },
          // { start: 1200, end: 1300, size: 3 },
          { size: 4 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1200,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      optionsItems: {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 3 },
          { start: 768, end: 992, size: 4 },
          { start: 992, end: 1200, size: 5 },
          { size: 5 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 10
        }
      },
      dataCoupons: [
        // {
        //   limitTime: '20/10/2566',
        //   startText: 'ซื้อร้านนี้ครั้งแรก',
        //   limitPrice: '999',
        //   unit: 'บาท',
        //   saleNumber: '1',
        //   sale: 'ส่วนลด'
        // },
        // {
        //   limitTime: '20/10/2566',
        //   startText: 'ซื้อร้านนี้ครั้งแรก',
        //   limitPrice: '999',
        //   unit: 'บาท',
        //   saleNumber: '2',
        //   sale: 'ส่วนลด'
        // },
        // {
        //   limitTime: '20/10/2566',
        //   startText: 'ซื้อร้านนี้ครั้งแรก',
        //   limitPrice: '999',
        //   unit: 'บาท',
        //   saleNumber: '3',
        //   sale: 'ส่วนลด'
        // },
        // {
        //   limitTime: '20/10/2566',
        //   startText: 'ซื้อร้านนี้ครั้งแรก',
        //   limitPrice: '999',
        //   unit: 'บาท',
        //   saleNumber: '4',
        //   sale: 'ส่วนลด'
        // },
        // {
        //   limitTime: '20/10/2566',
        //   startText: 'ซื้อร้านนี้ครั้งแรก',
        //   limitPrice: '999',
        //   unit: 'บาท',
        //   saleNumber: '5',
        //   sale: 'ส่วนลด'
        // }
      ],
      checkBot: false,
      partnerStatus: false,
      countAll: 0,
      countRecommend: 0,
      countNew: 0,
      countSale: 0,
      countBestSeller: 0,
      limit: 15,
      pathShopSale: '',
      SaleCanAddCus: 'N',
      canManageCus: 'N',
      itemsSale: [],
      currentPage: 1,
      pageListcustomer: 1,
      pageMaxListcustomer: null,
      customerSaleData: [],
      cusCode: 0,
      dialogChooseTypeCustomer: false,
      SaleID: 0,
      cusType: '',
      // titleCompanyAddress: '',
      showCustomer: false,
      userdetail: [],
      dialogListAddressCustomer: false,
      page: '',
      EditAddressDetail: '',
      titleAddress: '',
      search: '',
      namePartner: '',
      dataCusList: [],
      dataCusListAll: [],
      dialogListPartner: false,
      dialogFunnelMobile: false,
      cancelPartner: false,
      starShop: 0,
      emailCheck: false,
      dialogSuccess: false,
      dialogConfirm: false,
      nameDoc: [],
      itemsDes: '',
      sellerShopID: '',
      categoryid: '',
      categoryID: '',
      priceList: '',
      typeList: '',
      current: 1,
      pageSize: 15,
      pageMax: null,
      pageMaxAddress: null,
      totalAddress: 0,
      pageAddress: 1,
      DataCategory: [],
      tooltipVisible: false,
      headersCategory: [
        {
          sortable: false,
          value: 'category_name'
        }
        // { text: 'รหัสการสั่งซื้อ', value: 'payment_credit_term_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'จำนวนเงิน', value: 'total_amount', filterable: false, sortable: false, align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      itemTypeList: [
        { text: this.$t('ShopPage.All'), value: '' },
        { text: this.$t('ShopPage.GeneralProducts'), value: 'general' },
        { text: this.$t('ShopPage.Recommended'), value: 'recommend' },
        { text: this.$t('ShopPage.BestSellers'), value: 'best-seller' },
        { text: this.$t('ShopPage.NewArrival'), value: 'new' },
        { text: this.$t('ShopPage.DiscountedProducts'), value: 'sale' }
      ],
      itemPriceList: [
        { text: this.$t('SelectPrice.All'), value: '' },
        { text: this.$t('SelectPrice.Higher'), value: 'DESC' },
        { text: this.$t('SelectPrice.Lower'), value: 'ASC' }
      ],
      changePage: '',
      changeText: '',
      priceText: '',
      // itemsDescription: [
      //   'หน้าแรก', 'สินค้าทั้งหมด/หมวดหมู่ทั้งหมด'
      // ],
      RoleUser: '',
      itemsPage: [
        {
          text: this.$t('Headers.Home'),
          disabled: false,
          href: '/'
        },
        {
          text: this.$t('ShopPage.Shop'),
          disabled: true,
          href: 'ShoppageUI'
        }
      ],
      dialog_partner_mobile: false,
      dialog_partner: false,
      selectBudget: '',
      itemBudget: [
        { text: 'งบดำเนินการ', value: 'operating_budget' },
        { text: 'งบลงทุน', value: 'investment_budget' },
        { text: 'งบรายจ่ายประจำ', value: 'regular_expenditure_budget' }
      ],
      selectCutBudget: '',
      itemCutBudget: [
        { text: 'ต้นทุนขาย (COGS)', value: 'COGS' },
        { text: 'ค่าใช้จ่ายและบริการ (SG&A)', value: 'SG&A' },
        { text: 'ต้นทุนวิจัยและพัฒนา (R&D)', value: 'R&D' }
      ],
      email: '',
      checkDoc: true,
      oneData: [],
      DataFile: [],
      ModalCreate: false,
      selectgroup: {
        document: []
      },
      can_request_partner: true,
      pdftofile: '',
      company_id: '',
      user_id: '',
      Detail: {
        product_file: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      ParnertPerminsion: false,
      chackpartner: false,
      shopname: 'ร้านขายของ 1',
      shopImage: '',
      statusOpenPartner: true,
      dialogPartner: false,
      contact: true,
      response_doc: [],
      shop_logo: '',
      tabs: null,
      Path: process.env.VUE_APP_DOMAIN,
      items: ['สินค้าทั้งหมด', 'สินค้าขายดี'],
      RowUserData: '',
      // roleCustomer: '',
      NewProduct: [],
      // bestSeller,
      NormalProduct: [],
      OutProduct: [],
      SaleProduct: [],
      AllProduct: [],
      BestSeller: [],
      Recommended: [],
      AllChangeProduct: [],
      CouponsIteam: [],
      dataShop: [],
      bannerShop: [],
      newsShop: [],
      PathImage: process.env.VUE_APP_IMAGE,
      status: false,
      colors: [
        'indigo',
        'warning',
        'pink darken-2',
        'red lighten-1',
        'deep-purple accent-4'
      ],
      slides: ['First', 'Second', 'Third', 'Fourth', 'Fifth'],
      concludeAllProduct: [
        { text: this.$t('ShopPage.AllProducts'), value: '3562' },
        { text: this.$t('ShopPage.RecommendedPromotion'), value: '420' },
        { text: this.$t('ShopPage.NewArrival'), value: '50' },
        { text: this.$t('ShopPage.DiscountedProducts'), value: '150' },
        { text: this.$t('ShopPage.BestSellers'), value: '50' }
      ],
      datapartner: [],
      // editor: ClassicEditor,
      dialog_pdf: false,
      purchaser: '',
      status_btn: false,
      statusShop: true,
      statusPartner: true,
      response_coupon: 0,
      time: '',
      mail: '',
      i: 0,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'imageUpload',
          '|',
          'blockquote',
          'inserttable',
          'undo',
          'redo'
        ],
        image: {
          toolbar: ['imageStyle:block', 'imageStyle:side']
        },
        table: {
          contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
        }
      },
      dataRole: '',
      companyId: null,
      lazy: false,
      itemRules: {
        docCheck (val) {
          // console.log('val', val)
          // console.log('this.checkDoc------>', this.checkDoc)
          // if (val.document.length === 0) {
          //   return 'กรุณาเลือกกลุ่มลูกค้า'
          // } else
          if (this.checkDoc === false || null || undefined || 0) {
            return 'กรุณาเลือกไฟล์'
          }
        }
      },
      Rules: {
        Email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ]
      },
      refCode: '',
      callbackPath: '',
      itemListFlashSale: [],
      shopName: '',
      checkCategory: '',
      openCategories: {},
      filterCategory: [],
      allCategory: {},
      dataListCategory: [],
      selectCategoryCustom: '',
      normalizer (node) {
        const id = 'ms_category_id'
        const labelKey = 'category_name'
        const childrenKey = 'children'
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey] && node[childrenKey].length ? node[childrenKey] : undefined
        }
      },
      isActiveAllProduct: false,
      isActiveBestSelle: false,
      isActiveNewProduct: false,
      isActiveRecommentProduct: false,
      isActiveSaleProduct: false,
      isActiveGeneralProduct: false,
      isActiveOutOfStockProduct: false
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  async created () {
    window.scrollTo(0, 0)
    if (this.MobileSize) {
      this.$EventBus.$emit('GetLink')
    }
    var CurrentPath = ''
    CurrentPath = this.$router.currentRoute.path
    sessionStorage.setItem('pathRedirect', CurrentPath)
    var sellershop = this.$route.params.data.split('-')
    this.$store.commit('openLoader')
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('PageTab')) {
      this.dataTab = JSON.parse(localStorage.getItem('PageTab'))
      this.PageTab = parseInt(this.dataTab.PageTab)
      this.changePage = this.dataTab.changePage
      this.priceList = this.dataTab.priceList
      // this.typeList =
    } else {
      this.PageTab = 0
      this.changePage = ''
      this.priceList = ''
    }
    this.pageNumber = parseInt(this.$route.query.page)
    // เก็บ ref_code และ callBack path
    this.checkGOValueRedirect()
    this.$EventBus.$on('getSellerShopPage', this.getSellerShopPage)
    this.pathRoute = this.$router.currentRoute.path.substring(1)
    this.message = this.Path + this.pathRoute
    // var countpath = window.location.href.split('-')
    var countpath = this.$router.currentRoute.params.data.split('-')
    // var idshop = this.pathRoute.split('-')
    this.shopName = ''
    // วนลูปผ่าน array
    for (let i = 0; i < countpath.length - 1; i++) {
      // เช็คเฉพาะ index แรกและก่อนถึง index สุดท้าย
      this.shopName += countpath[i] + ' '
    }
    // ตัดช่องว่างที่ส่วนท้ายสุดของ string ออก
    this.shopName = this.shopName.trim()
    var idshop = countpath[countpath.length - 1]
    if (localStorage.getItem('roleUser') !== null) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
      // this.RoleUser = 'sale_order_no_JV'
    } else {
      this.RoleUser = 'ext_buyer'
    }
    if (this.RoleUser === 'sale_order' || this.RoleUser === 'sale_order_no_JV') {
      this.pathShopSale = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
      var path = this.pathShopSale
      localStorage.setItem('pathShopSale', Encode.encode(path))
      this.pathshop = this.pathShopSale.path.toString()
      this.itemsSale = [
        {
          text: this.$t('Headers.Home'),
          disabled: false,
          href: this.pathshop
        },
        {
          text: this.$t('ShopPage.Shop'),
          disabled: true,
          href: this.pathshop
        }
      ]
    }
    if (localStorage.getItem('ClickgoToCheckOut') !== null) {
      if (localStorage.getItem('list_Company_detail') !== null) {
        var auth = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
        if (
          auth.can_use_function_in_company.partner === undefined ||
          auth.can_use_function_in_company.partner === 0 ||
          auth.can_use_function_in_company.partner === null
        ) {
          this.ParnertPerminsion = false
        } else {
          this.ParnertPerminsion = true
        }
      } else {
        this.ParnertPerminsion = false
      }
    }
    localStorage.setItem('shopID', idshop)
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.user_id = this.oneData.user.user_id
      if (localStorage.getItem('roleUser') !== null) {
        this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
        // this.RowUserData = 'sale_order_no_JV'
        // this.RoleUser = 'sale_order_no_JV'
        // ฟิกกก
      } else {
        this.RowUserData = 'ext_buyer'
      }
      if (localStorage.getItem('shopID') !== null) {
        this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      } else if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        // console.log(this.$router.currentRoute.params.data)
        this.sellerShopID = sellershop
      }
      var partnerId = localStorage.getItem('partner_id')
      if (partnerId !== null) {
        this.cusID = parseInt(partnerId)
      } else {
        this.cusID = 0
      }
      if (this.RoleUser !== 'sale_order_no_JV' || this.cusID !== 0) {
        await this.getListCouponsistNGC()
      }
    } else {
      if (localStorage.getItem('shopID') !== null) {
        this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      } else if (this.RowUserData === 'sale_order' || this.RowUserData === 'sale_order_no_JV') {
        this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        // console.log(this.$router.currentRoute.params.data)
        this.sellerShopID = sellershop
      }
      await this.getListCouponsistNGC()
    }
    // const companyId = []
    if (localStorage.getItem('SetRowCompany') !== null && this.RowUserData === 'purchaser') {
      const companyId = JSON.parse(
        Decode.decode(localStorage.getItem('SetRowCompany'))
      )
      // console.log('companyId11111', companyId)
      this.purchaser = companyId.position.purchaser
      this.companyId = companyId.company.company_id
      this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
    } else if (this.RowUserData === 'sale_order') {
      this.companyId = localStorage.getItem('PartnerID')
      // console.log('created', this.companyId)
      this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      // this.dialogListPartner = true
      // console.log('this.sellerShopID01', this.sellerShopID)
    } else if (this.RowUserData === 'sale_order_no_JV') {
      this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      // console.log('this.sellerShopID1', this.sellerShopID)
      var shopdetailData = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
      if (shopdetailData.sale_order_type !== undefined) {
        this.SaleOrderNoJVType = shopdetailData.sale_order_type
        this.SaleVendor = true
      } else {
        this.SaleOrderNoJVType = ''
        this.SaleVendor = false
      }
      // console.log('shopdetailData', shopdetailData)
      if (localStorage.getItem('sale_order_customer') !== null) {
        var type = JSON.parse(localStorage.getItem('sale_order_customer')).role
        // console.log('1111111', type)
        if (type !== 'JV_customer' && this.SaleVendor === false) {
          this.cusCode = localStorage.getItem('cusCode')
        } else if (this.SaleVendor === true && localStorage.getItem('cusCode') === null) {
          this.chooseTypeCus('vendor')
        } else {
          this.cusCode = 0
        }
      } else {
        // console.log('2222222')
        if (this.SaleVendor === true) {
          this.chooseTypeCus('vendor')
        } else {
          this.dialogChooseTypeCustomer = true
        }
      }
      await this.getDetailSale(this.sellerShopID)
      this.showCustomer = true
    } else {
      // console.log('else')
      if (localStorage.getItem('shopID') !== null) {
        this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      } else {
        // console.log(this.$router.currentRoute.params.data)
        this.sellerShopID = sellershop[1]
      }
      this.purchaser = ''
      this.companyId = '-1'
    }
    // console.log('this.RowUserData', this.RoleUser)
    // this.changePage = ''
    // console.log('this.companyId', this.companyId)
    // console.log('SaleVendor', this.SaleVendor)
    await this.getBanner()
    if (this.checkCategory === 'yes') {
      this.categoryID = 0
    } else {
      this.categoryID = 1
    }
    if (this.RoleUser === 'sale_order_no_JV') {
      await this.CheckSaleCanManage()
    }
    var idStart = 1
    // await this.getcountSummaryProduct()
    await this.GetCategory(idStart)
    // await this.getProductFlashSale()
    // await this.ChackPartnerShop()
    await this.getDataShopPage()
    await this.checkRequestPartner()
    await this.getapidetaildocument()
    // await this.getCoupon()
    // await this.selectCategory()
    // await this.getListCouponsistNGC()
  },
  watch: {
    selectCategoryCustom (val) {
      this.selectCategory(val)
    },
    pageAddress (val) {
      // console.log('valpageAddress', val)
      this.openAddress()
    },
    pageListcustomer (val) {
      // console.log('valpageAddress', val)
      this.changePageCustomer()
    },
    async $route (to, from) {
      // console.log('route====>', this.$route)
      // console.log('from====>', from)
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page
      // console.log('route====>', this.$route.query.shared_token)
      // console.log('getIDToParams====>', getIDToParams)
      // console.log('getIDFromParams====>', getIDFromParams)
      if (this.$route.query.shared_token !== undefined) {
        if (getIDFromParams !== undefined && getIDToParams !== undefined) {
          if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
            this.pageNumber = parseInt(this.$route.query.page)
            // this.pageChange(this.pageNumber)
          }
        }
      } else {
        if (getIDFromParams !== undefined && getIDToParams !== undefined) {
          if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
            this.pageNumber = parseInt(this.$route.query.page)
            // this.pageChange(this.pageNumber)
          }
        }
      }
    }
    // ,
    // dialogCopy (val) {
    //   if (val) {
    //     setTimeout(() => {
    //       this.dialogCopy = false
    //     }, 1000)
    //   }
    // }
  },
  computed: {
    initialRoute () {
      return this.$store.state.ModuleGlobal.initialRoute
    },
    previousRoute () {
      return this.$store.state.ModuleGlobal.previousRoute
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        // window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.AllChangeProduct
    },
    filteredItems () {
      return this.search
        ? this.dataCusListAll.filter(item =>
          item.cus_name.toLowerCase().includes(this.search.toLowerCase())
        )
        : this.dataCusList
    },
    totalPagesArticle () {
      return Math.ceil(this.itemArticle.length / this.itemsPerPageArticle)
    },
    paginatedArticles () {
      const start = (this.currentPageArticle - 1) * this.itemsPerPageArticle
      const end = start + this.itemsPerPageArticle
      return this.itemArticle.slice(start, end)
    }
    // headersMobile () {
    //   const headersMobile = [
    //     {
    //       title: 'สินค้าทั้งหมด',
    //       dataIndex: 'productCategory',
    //       key: 'productCategoryv',
    //       scopedSlots: { customRender: 'productCategory' },
    //       width: '100%'
    //     }
    //   ]
    //   return headersMobile
    // }
  },
  mounted () {
    window.scrollTo(0, 0)
    if (this.MobileSize) {
      this.$EventBus.$emit('GetLink')
    }
    this.$EventBus.$on('ChackRowUser', this.ChackRowUser)
    // this.$EventBus.$on('getCustomDetail', this.getCustomDetail)
    this.$EventBus.$on('getRole', this.getRole)
    this.$EventBus.$on('getListCouponsistNGC', this.getListCouponsistNGC)
    this.$EventBus.$on('EditAddressCustomerComplete', this.getCustom)
    this.$EventBus.$on('listAddress', this.openAddress)
    // this.$EventBus.$on('getDetailSale', this.getDetailSale)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('ChackRowUser')
      // this.$EventBus.$off('getDetailSale')
      this.$EventBus.$off('getRole')
      this.$EventBus.$off('getListCouponsistNGC')
      this.$EventBus.$off('EditAddressCustomerComplete')
      this.$EventBus.$off('listAddress')
    })
  },
  methods: {
    checkDocOfTier (item) {
      if (item.document.length === 0) {
        this.checkDoc = false
      } else {
        this.checkDoc = true
      }
    },
    checkGOValueRedirect () {
      var refcode = ''
      refcode = this.$route.query.ref_code
      var callback = ''
      callback = this.$route.query.callback_url
      if (refcode !== undefined) {
        this.refCode = refcode
      } else {
        this.refCode = ''
      }
      if (callback !== undefined) {
        this.callbackPath = callback
      } else {
        this.callbackPath = ''
      }
      // console.log('ref_code and callback ====>', this.refCode, this.callbackPath)
    },
    async CheckSaleCanManage () {
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      // console.log('response', response.data)
      var sellerShopID
      var data
      if (this.RoleUser === 'sale_order_no_JV') {
        sellerShopID = localStorage.getItem('ShopID')
      } else {
        sellerShopID = localStorage.getItem('shopID')
      }
      response.data.list_shop_detail.forEach(e => {
        if (e.seller_shop_id === parseInt(sellerShopID)) {
          data = e.can_use_function_in_shop
          if (parseInt(data.manage_sale_order) === 1) {
            this.canManageCus = 'Y'
          } else {
            if (this.SaleCanAddCus === 'Y') {
              this.canManageCus = 'Y'
            } else {
              this.canManageCus = 'N'
            }
          }
          // console.log('this.canManageCus =====>', this.canManageCus)
        }
      })
    },
    async sentChat (item) {
      // var checkShopId = localStorage.getItem('shopIdForChat')
      if (localStorage.getItem('oneData') !== null) {
        const tempChat = JSON.parse(localStorage.getItem('userChat') || '[]')
        var checkShopId = tempChat.map(e => { return e.shopId })
        var duplicate = null
        // console.log('tempChat', tempChat, item)
        var res = []
        this.$store.commit('openLoader')
        if (this.oneData.length !== 0) {
          // const sharedToken = await createChat.sharetoken()
          const req = await {
            user_id: this.oneData.user.user_id,
            shopNGSID: item.shop_id,
            shopNGSName: item.shop_name_en !== '' && item.shop_name_en !== null ? item.shop_name_en : item.shop_name_th,
            sharedToken: null
          }
          await this.$store.dispatch('actionsCreateBotChat', req)
          res = await this.$store.state.ModuleHompage.stateCreateBotChat
          if (res.data.length !== 0) {
            const data = await {
              botID: res.data[0].botID
            }
            await createChat.chatMe2(data)
            this.$store.commit('closeLoader')
            await this.$EventBus.$emit('initChat')
            // await window['sentChatMore']()
            // console.log('sentChatResss', res)
          } else {
            this.$swal.fire(
              {
                icon: 'error',
                html: `<h3>${decodeURIComponent((res.message + '').replace(/\+/g, '%20'))}</h3>`,
                showConfirmButton: false,
                timer: 1500
              })
            this.$store.commit('closeLoader')
          }
        } else {
          // console.log('เข้าไหม')
          const req = {
            user_id: null
          }
          await this.$store.dispatch('actionslistBotChatWithUser', req)
          var ress = await this.$store.state.ModuleHompage.statelistBotChatWithUser
          if (ress.result === 'SUCCESS') {
            // console.log('เข้าไหม2')
            const dataTemp = ress.data.listChat.filter(e => {
              return [e.shopNGSID].includes(item.shop_id)
            })
            // console.log('dataTemp', dataTemp)
            if (dataTemp.length !== 0) {
              const data = await {
                botID: dataTemp[0].botID,
                botToken: dataTemp[0].botToken,
                ImgShop: dataTemp[0].ImgShop,
                botName: dataTemp[0].botName,
                shopId: dataTemp[0].shopNGSID
              }
              if (tempChat === null || !checkShopId.includes(dataTemp[0].shopNGSID)) {
                // console.log('chat have')
                await tempChat.push(data)
                await localStorage.setItem('userChat', JSON.stringify(tempChat))
              }
              // }
              await createChat.chatMe(data, duplicate)
              this.$store.commit('closeLoader')
            } else {
              this.$swal.fire({ html: 'ยังไม่สามารถแชทกับร้านค้านี้ได้ <br/> กรุณา Login เข้าสู่ระบบ', icon: 'warning', timer: 2500, showConfirmButton: false })
              this.$store.commit('closeLoader')
            }
          }
        }
      } else {
        this.$swal.fire({ text: 'กรุณาเข้าสู่ระบบก่อนแชทกับร้านค้า', icon: 'warning', timer: 2500, showConfirmButton: false })
        setTimeout(() => {
          this.$router.push({ path: '/Login' }).catch(() => {})
        }, 100)
      }
    },
    async getRole (role) {
      // window.location.reload()
      // console.log('role', role)
      this.dataCoupons = []
      this.RoleUser = role
      this.RowUserData = role
      if (this.RoleUser === 'sale_order' || this.RoleUser === 'sale_order_no_JV') {
        this.sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        this.sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      // console.log('this.sellerShopID', this.sellerShopID)
      if (this.RoleUser === 'sale_order_no_JV') {
        this.getDetailSale()
        var shopdetailData = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
        if (shopdetailData.sale_order_type !== undefined) {
          this.SaleOrderNoJVType = shopdetailData.sale_order_type
          this.SaleVendor = true
        } else {
          this.SaleOrderNoJVType = ''
          this.SaleVendor = false
        }
        if (this.cusID !== 0) {
          this.getListCouponsistNGC()
        } else {
          this.dataCoupons = []
        }
        this.dialogChooseTypeCustomer = true
      }
      await this.getBanner()
      await this.getDataShopPage()
      await this.getListCouponsistNGC()
      // console.log('his.cusID', this.cusID)
    },
    getCustom (data) {
      this.dialogListAddressCustomer = false
      // console.log('getCustom', data)
      if (data !== undefined) {
        this.cusCode = data.cus_code
        this.cusID = data.cus_id
        localStorage.setItem('partner_id', this.cusID)
        this.getCustomDetail(this.cusID)
      }
      this.closed()
      this.dialogChooseTypeCustomer = false
      this.getDetailSale()
    },
    async getDetailSale (sellerID) {
      // console.log(' this.getDetailSale', this.SaleVendor)
      this.$store.commit('openLoader')
      this.cusCode = localStorage.getItem('cusCode')
      this.dialogListAddressCustomer = false
      this.showCustomer = true
      if (this.SaleOrderNoJVType === '') {
        var data = {
          seller_shop_id: this.sellerShopID,
          role: this.RoleUser,
          sale_id: '',
          general_page: this.pageListcustomer === null ? 1 : this.pageListcustomer,
          business_page: this.pageListcustomer === null ? 1 : this.pageListcustomer
        }
      } else {
        data = {
          seller_shop_id: this.sellerShopID,
          role: this.RoleUser,
          sale_id: '',
          general_page: this.pageListcustomer === null ? 1 : this.pageListcustomer,
          sale_order_type: this.SaleOrderNoJVType,
          vendor_page: this.pageListcustomer === null ? 1 : this.pageListcustomer,
          business_page: this.pageListcustomer === null ? 1 : this.pageListcustomer
        }
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsGetSaleOrder', data)
      var res = await this.$store.state.ModuleShop.stateGetSaleOrder
      // console.log('res', res)
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.SaleID = res.data[0].id
        localStorage.setItem('SaleID', this.SaleID)
        // console.log('manage_customer', res.data[0].manage_customer)
        // this.SaleCanAddCus = res.data[0].SaleCanAddCus
        this.SaleCanAddCus = res.data[0].manage_customer
        // console.log('this.SaleCanAddCus', this.SaleCanAddCus)
        if (this.roleCustomer === 'business' && this.SaleVendor === false) {
          this.dataCusList = res.data[0].business_customer
          this.dataCusListAll = res.data[0].business_customer_all
          this.currentPage = res.data[0].business_current_page
          this.pageMaxListcustomer = parseInt(res.data[0].business_total_page)
          // console.log('1')
        } else if (this.SaleVendor === true) {
          // console.log('2', res.data[0].vendor_customer)
          this.dataCusList = res.data[0].vendor_customer
          this.dataCusListAll = res.data[0].vendor_customer_all
          this.currentPage = res.data[0].vendor_current_page
          this.pageMaxListcustomer = parseInt(res.data[0].vendor_total_page)
        } else if (this.roleCustomer === 'general' && this.SaleVendor === false) {
          // console.log('3')
          this.dataCusList = res.data[0].general_customer
          this.dataCusListAll = res.data[0].general_customer_all
          this.currentPage = res.data[0].general_current_page
          this.pageMaxListcustomer = parseInt(res.data[0].general_total_page)
        }
        // console.log('datacus', this.dataCusList)
      } else if (res.message === 'Sales man not found.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์เข้าถึงการใช้งาน sale order', showConfirmButton: false, timer: 2500 })
        var role = {
          role: 'ext_buyer'
        }
        localStorage.setItem('roleUser', JSON.stringify(role))
        this.$router.push({ path: '/' }).catch(() => {})
        this.$EventBus.$emit('CheckPermission')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2500 })
        // console.log('error')
      }
    },
    async getcountSummaryProduct () {
      var data = {
        role_user: this.RoleUser,
        company_id: this.companyId,
        seller_shop_id: this.sellerShopID
      }
      await this.$store.dispatch('actionsCountSummaryProduct', data)
      var res = await this.$store.state.ModuleShop.stateCountSummaryProduct
      // console.log('res', res)
      if (res.ok === 'y') {
        if (this.partnerStatus === false && this.RoleUser === 'purchaser') {
          this.countAll = 0
          this.countRecommend = 0
          this.countNew = 0
          this.countSale = 0
          this.countBestSeller = 0
        } else {
          this.countAll = res.query_result.all
          this.countRecommend = res.query_result.recommend
          this.countNew = res.query_result.new
          this.countSale = res.query_result.sale
          this.countBestSeller = res.query_result.best_seller
          this.$store.commit('closeLoader')
        }
        // console.log('this.countAll', this.countAll)
        // console.log('this.countRecommend', this.countRecommend)
        // console.log('this.countNew', this.countNew)
        // console.log('this.countSale', this.countSale)
        // console.log('this.countBestSeller', this.countBestSeller)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: '<h5>ไม่สามารถนับจำนวนสินค้าได้ โปรดลองอีกครั้งในภายหลัง</h5>', showConfirmButton: false, timer: 1500 })
      }
    },
    getAllCoupon () {
      // console.log('getAllCoupon')
      this.dialogAllCoupon = true
    },
    changePageCustomer () {
      // console.log('this.pageListcustomer', this.pageListcustomer)
      // this.pageListcustomer = this.currentPage
      this.getDetailSale()
    },
    async setDefaultAdress (item) {
      // console.log('setDefaultAdress', item)
      localStorage.setItem('partner_id', this.cusID)
      // localStorage.setItem('customer_data', item)
      // var dataGet = ''
      const data = {
        seller_shop_id: this.sellerShopID,
        role: this.RowUserData,
        cus_id: this.cusID,
        address_id: item.id,
        name: item.name,
        first_name: item.first_name,
        last_name: item.last_name,
        phone: item.phone,
        house_no: item.house_no,
        room_no: item.room_no,
        floor: item.floor,
        building: item.building,
        moo_ban: item.moo_ban,
        moo_no: item.moo_no,
        soi: item.soi,
        yaek: item.yaek,
        street: item.street,
        sub_district: item.sub_district,
        district: item.district,
        province: item.province,
        postcode: item.postcode,
        // default_address: item.default_address
        default_address: 'Y'
      }
      // console.log('this.roleCustomer', this.roleCustomer)
      // console.log('dataGet====>', data)
      await this.$store.dispatch('actionsUpdateCustomerAddress', data)
      var res = await this.$store.state.ModuleSaleOrder.stateUpdateCustomerAddress
      // var res = {
      //   message: 'Update customer address successfully.'
      // }
      if (res.message === 'Update customer address successfully.') {
        this.$swal.fire({ icon: 'success', title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>', showConfirmButton: false, timer: 1500 })
        // this.userdetail = [...res.data]
        this.dialogListAddressCustomer = false
        this.pageListcustomer = 1
        this.getListCouponsistNGC()
        this.$EventBus.$emit('getCartPopOver')
        if (this.roleCustomer === 'general') {
          localStorage.setItem('AddressCustomerSale', Encode.encode(data))
        } else {
          localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(data))
        }
        this.getCustomDetail(this.cusID)
        // this.showCustomer = true
      } else {
        this.dialogListAddressCustomer = false
        this.$swal.fire({ icon: 'error', title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>', showConfirmButton: false, timer: 1500 })
      }
      // this.getAddress()
    },
    async getCustomDetail (id) {
      // console.log('id', id)
      var data = {
        seller_shop_id: this.sellerShopID,
        cus_id: id === null ? this.cusID : id
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsGetDetailCustomer', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetDetailCustomer
      if (res.message === 'Get detail customer success.') {
        var dataCustomer = res.data[0]
        this.cusCode = dataCustomer.cus_code
        localStorage.setItem('cusCode', this.cusCode)
        var customerAddress = dataCustomer.customer_address
        var customerInvAddress = dataCustomer.customer_inv_address
        var addressDefault = []
        var addressInvAddressDefault = []
        if (customerAddress.length > 1 || customerInvAddress.length > 1) {
          customerAddress.forEach((e, i) => {
            if (e.default_address === 'Y') {
              addressDefault.push(e)
            }
          })
          customerInvAddress.forEach((e, i) => {
            if (e.default_address === 'Y') {
              addressInvAddressDefault.push(e)
            }
          })
          dataCustomer.customer_address = addressDefault
          dataCustomer.customer_inv_address = addressInvAddressDefault
        }
        // console.log('dataCustomer', dataCustomer)
        if (this.roleCustomer === 'general') {
          localStorage.setItem('AddressCustomerSale', Encode.encode(dataCustomer))
        } else if (this.roleCustomer === 'vendor') {
          localStorage.setItem('customer_data', Encode.encode(dataCustomer))
          // console.log('dataCustomer', dataCustomer)
        } else {
          localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(dataCustomer))
        }
        // console.log('AddressCustomerBussinessSale', dataCustomer.customer_address[0])
      } else {
        this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2500 })
      }
    },
    async chooseTypeCus (type) {
      var data
      if (type === 'business') {
        data = {
          role_customer: 'business'
        }
        // console.log('องค์กร')
      } else if (type === 'vendor') {
        // console.log('vendor')
        data = {
          role_customer: 'vendor'
        }
      } else {
        data = {
          role_customer: 'general'
        }
        // console.log('{{ $t('ShopPage.Individual') }}')
      }
      this.roleCustomer = data.role_customer
      localStorage.setItem('sale_order_customer', JSON.stringify(data))
      // console.log('this.cusType', localStorage.getItem('sale_order_customer'))
      await this.getDetailSale()
      this.dialogListPartner = true
    },
    async openAddress (id, cusCode) {
      // console.log('openAddress')
      this.dialogChooseTypeCustomer = false
      if (id && cusCode !== undefined) {
        this.cusID = id
        this.cusCode = cusCode
      }
      if (this.pageAddress !== 1) {
        // window.scrollTo(0, 0)
      }
      var data = {
        seller_shop_id: this.sellerShopID,
        role: this.RowUserData,
        cus_id: this.cusID,
        page: this.pageAddress === null ? 1 : this.pageAddress
      }
      // console.log('openAddress0002', this.cusID)
      await this.$store.dispatch('actionsGetListCustomerAddress', data)
      var res = await this.$store.state.ModuleShop.stateGetListCustomerAddress
      if (res.result === 'SUCCESS') {
        this.userdetail = res.data.address
        this.totalAddress = res.data.total_address
        this.pageMaxAddress = parseInt(res.data.total_address / 5) === 0 ? 1 : Math.ceil(res.data.total_address / 5)
        // if (this.userdetail.length === 1) {
        //   this.showCustomer = true
        // } else {
        this.dialogListAddressCustomer = true
        // }
      } else {
        // console.log('error')
      }
      // console.log('this.userdetail', this.userdetail)
      this.dialogListPartner = false
    },
    GetProductTypeAndPrice (text, price) {
      this.typeList = text
      this.changeText = text
      this.priceText = price
      this.dialogFunnelMobile = false
      this.selectCategory()
    },
    reSet () {
      this.typeList = ''
      this.priceList = ''
    },
    confirm () {
      this.dialogPartner = false
      this.dialogConfirm = true
    },
    changePageProduct (item) {
      // console.log('item', item)
      this.itemsDes = item
      this.PageTab = item
      var dataSet = {
        PageTab: this.PageTab,
        changePage: this.changePage,
        priceList: this.priceText
      }
      localStorage.setItem('PageTab', JSON.stringify(dataSet))
      if (item === 1) {
        this.selectCategory()
        // this.pageChange()
        //  this.$router.push({ path: `/shoppage/${shopCleaned}-${val.shop_id}?type=frist&page=1` })
      } else if (item === 2) {
        this.getDataArticle(this.sellerShopID, this.RoleUser)
      } else {
        // this.$router.push(`/${this.$route.path}?type=first&page=1`).catch(() => {})
        // this.$router.push(`/${this.$route.path}?page=${item}`).catch(() => {})
        // this.$router.push(`/${this.$route.path}-fristPage${this.pages}`).catch(() => {})
      }
      // console.log('changePageProduct', this.$route.path)
    },
    scrollToTargetSection () {
      const target = this.$refs.targetSection
      if (target) {
        target.scrollIntoView({ behavior: 'smooth' })
      }
    },
    pageChange (val) {
      this.$router.push(`${this.$route.path}?page=${val}`).catch(() => {})
      this.pageNumber = val
      this.selectCategory()
      this.scrollToTargetSection()
      // window.scrollTo(0, 800)
      // this.$refs.targetSection.$el.scrollIntoView({ behavior: 'smooth' })
      // console.log('valpageChange')
      // this.$router.push(`/ListProduct/${this.typeProduct}?page=${val}`).catch(() => {})
    },
    GetProductPrice (text) {
      this.priceText = text
      this.selectCategory()
    },
    GetProductType (text) {
      // console.log('GetProductTyspe')
      this.typeList = text
      this.changePage = text
      this.selectCategory()
      this.getDataArticle(this.sellerShopID, this.RoleUser)
    },
    async GetCategory () {
      var companyID
      if (localStorage.getItem('oneData') !== null) {
        if (this.RoleUser !== 'sale_order') {
          if (localStorage.getItem('SetRowCompany') !== null) {
            companyID = this.companyId
            // console.log('companyID', companyID)
          } else {
            companyID = '-1'
          }
          // sellerShopID = JSON.parse(localStorage.getItem('shopID'))
        } else {
          if (localStorage.getItem('sale_order_customer') !== null) {
            var typeSale = JSON.parse(localStorage.getItem('sale_order_customer')).role
          }
          // console.log('typeSale', typeSale)
          if (typeSale !== 'JV_customer' && this.RowUserData === 'sale_order_no_JV') {
            // console.log('1')
            companyID = '-1'
          } else {
            // console.log('2')
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          // console.log('GetCategory')
          // companyID = JSON.parse(localStorage.getItem('PartnerID'))
          // sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
        }
      } else {
        companyID = ''
        // sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      // console.log('companyID', companyID, typeSale)
      var data = {
        role_user: this.RoleUser,
        company_id: companyID,
        seller_shop_id: this.sellerShopID,
        type_category: this.checkCategory === 'yes' ? 'custom_category' : ''
        // limit: this.limit
      }
      // console.log('data figdataRoleไว้=========>', data)
      await this.$store.dispatch('actionsGetCategoryShopList', data)
      var res = await this.$store.state.ModuleShop.stateGetCategoryShopList
      // console.log('res', res)
      if (res.ok === 'y') {
        if (res.query_result.categoryshop_llst.length !== 0) {
          this.DataCategory = res.query_result.categoryshop_llst
        } else {
          this.DataCategory = [{
            ms_category_id: 0,
            category_name: 'สินค้าทั้งหมด',
            hierachy: 'all',
            children: []
          }]
        }
        // console.log(this.DataCategory, 'ดูค่า')
      } else {
        this.$store.commit('closeLoader')
        this.DataCategory = []
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 3000,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: res.message
        // })
      }
      this.$store.commit('closeLoader')
    },
    toggleCategory (categoryID) {
      // ปิดเฉพาะหมวดหมู่ที่ถูกคลิก
      this.$set(this.openCategories, categoryID, !this.openCategories[categoryID])
    },
    async selectCategory (categoryID, event) {
      this.isActiveAllProduct = false
      if (!this.MobileSize) {
        if (event) event.stopPropagation()
      }
      // console.log('categoryID', categoryID)
      this.$store.commit('openLoader')
      // console.log('selectCategory', this.changePage)
      var dataRole = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataRole = 'ext_buyer'
      }
      // console.log('dataRole', dataRole)
      this.AllChangeProduct = []
      // this.changePage = this.changeText
      if (this.changePage === '') {
        this.AllChangeProduct = this.AllProduct
      } else if (this.changePage === 'new') {
        this.AllChangeProduct = this.NewProduct
      } else if (this.changePage === 'best-seller') {
        this.AllChangeProduct = this.BestSeller
      } else if (this.changePage === 'recommend') {
        this.AllChangeProduct = this.Recommended
      } else if (this.changePage === 'sale') {
        this.AllChangeProduct = this.SaleProduct
      } else if (this.changePage === 'general') {
        this.AllChangeProduct = this.NormalProduct
      }
      this.categoryid = this.categoryID
      // console.log('categoryID-**', this.categoryid)
      if (categoryID !== undefined && name !== undefined) {
        this.AllChangeProduct = []
        this.categoryID = categoryID
      } else if (categoryID !== undefined && name === undefined) {
        this.pageNumber = 1
        this.AllChangeProduct = []
        this.categoryID = categoryID
        // console.log('pageNum', this.$route)
      } else {
        this.AllChangeProduct = []
        this.categoryID = this.categoryid
      }
      var companyID
      // var sellerShopID
      if (localStorage.getItem('oneData') !== null) {
        if (dataRole !== 'sale_order') {
          if (localStorage.getItem('SetRowCompany') !== null) {
            companyID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany'))).company.company_id
            // console.log('companytest', companyID)
          } else {
            companyID = '-1'
          }
          // sellerShopID = JSON.parse(localStorage.getItem('shopID'))
        } else {
          companyID = JSON.parse(localStorage.getItem('PartnerID'))
          // console.log('companyIDSale', companyID)
          // sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
        }
      } else {
        companyID = ''
        // sellerShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      // console.log('companyID====>', companyID)
      var data = {
        role_user: dataRole,
        company_id: companyID === null ? -1 : companyID,
        category: this.categoryID,
        seller_shop_id: this.sellerShopID,
        orderBy: this.priceText,
        status_product: this.changePage,
        limit: this.limit,
        page: this.pageNumber,
        type_category: this.checkCategory === 'yes' ? 'custom_category' : ''
      }
      // console.log('dataBestSeller', data)
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var res = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('resSelect', res)
      if (res.ok === 'y') {
        var dataSet = {
          PageTab: this.PageTab,
          changePage: this.changePage,
          priceList: this.priceText
        }
        localStorage.setItem('PageTab', JSON.stringify(dataSet))
        if (this.changePage === '' && res.query_result.length !== 0) {
          this.AllProduct = []
          this.AllProduct = await [...res.query_result]
          // console.log('all product ======>', this.AllProduct)
          this.status = true
          this.isActiveAllProduct = true
        } else {
          this.AllProduct = []
          this.isActiveAllProduct = true
        }
        this.AllChangeProduct = res.query_result
        // this.pageMax = parseInt(this.AllChangeProduct.length / 15) === 0 ? 1 : Math.ceil(this.AllChangeProduct.length / 15)
        this.pageMax = parseInt(res.pagination.max_page)
        if (this.callbackPath === undefined && this.refCode === undefined) {
          this.$router.push(`${this.$route.path}?page=${this.pageNumber}`).catch(() => {})
        }
        // this.pageMax = parseInt(res.pagination.max_page)
        // console.log('pagemax', this.pageMax)
        if (categoryID !== undefined) {
          this.scrollToTargetSection()
        }
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
        // this.pageMax = parseInt(this.AllChangeProduct.length / 15) === 0 ? 1 : Math.ceil(this.AllChangeProduct.length / 15)
        this.pageMax = parseInt(res.pagination.max_page)
      }
      this.$store.commit('closeLoader')
    },
    GetAllProducts (allItem, header) {
      if (allItem.length !== 0) {
        localStorage.setItem('itemShop', Encode.encode(allItem))
        this.$router.push(`/ListShopProduct/${header}?page=1`).catch(() => {})
      }
    },
    ModalCreateQuotation () {
      this.ModalCreate = true
    },
    async CreateQuotation () {
      localStorage.setItem('part_old', this.$router.currentRoute.path)
      var dataRole = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        dataRole = 'ext_buyer'
      }
      const pathBack = window.location.href.split('/')
      this.$store.state.ModuleAdminManage.stateURLQu = `/${
        pathBack[3]
      }/${decodeURI(pathBack[4])}`
      // console.log('AS', pathBack[4], 'EX', decodeURI(pathBack[4]), 'URL', this.$store.state.ModuleAdminManage.stateURLQu)
      const dataForm = await {
        role_user: dataRole.role,
        user_id: this.user_id,
        id: this.companyId,
        seller_shop_id: this.sellerShopID,
        company_id: this.companyId,
        qu_id: ''
      }
      await this.$store.dispatch('actionsEditQU', dataForm)
      var res = await this.$store.state.ModuleAdminManage.stateEditQU
      await this.$store.dispatch('actionsGetDataQt', dataForm)
      const { result = '', data = {} } = await this.$store.state
        .ModuleAdminManage.stateGetDataQt
      if (result === 'SUCCESS') {
        const d = new Date()
        var dataQT = {
          product_of_shop: res.data.product_of_shop,
          business_detail: res.data.business_detail,
          detail: res.data.qu_detail,
          selectBudget: this.selectBudget,
          selectCutBudget: this.selectCutBudget,
          qu_detail: {
            product_list: [],
            date: d.toLocaleDateString('th-TH', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            dateStart: '',
            dateEnd: '',
            credit_term: '',
            sale_detail: data.sale_detail,
            user_detail: data.user_detail,
            seller_shop_id: this.sellerShopID,
            company_id: this.companyId
          }
        }
        this.$store.state.ModuleAdminManage.QuotationformData = await dataQT
        await this.$router
          .push({ path: '/Quotation?role=purchaser&qu_id=0' })
          .catch(() => {})
      }
    },
    async ChackRowUser () {
      this.$EventBus.$emit('getPath')
      // this.$EventBus.$on('getSellerShopPage', this.getSellerShopPage)
      this.pathRoute = this.$router.currentRoute.path.substring(1)
      this.message = this.Path + this.pathRoute
      var countpath = this.$router.currentRoute.params.data.split('-')
      var idshop = countpath[countpath.length - 1]
      var dataRole = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataRole = 'ext_buyer'
      }
      localStorage.setItem('shopID', idshop)
      this.oneData = []
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        this.oneData = JSON.parse(
          Decode.decode(localStorage.getItem('oneData'))
        )
        this.RowUserData = JSON.parse(localStorage.getItem('roleUser')).role
      }
      // const companyId = []
      if (localStorage.getItem('SetRowCompany') !== null) {
        const companyId = JSON.parse(
          Decode.decode(localStorage.getItem('SetRowCompany'))
        )
        this.purchaser = companyId.position.purchaser
        this.companyId = companyId.company.company_id
      } else if (dataRole === 'sale_order') {
        this.companyId = localStorage.getItem('PartnerID')
      } else {
        this.purchaser = ''
        this.companyId = ''
      }
      await this.checkRequestPartner()
      // await this.ChackPartnerShop()
      await this.getapidetaildocument()
      // await this.getDataShopPage()
      // this.getSellerShopPage()
    },
    async checkRequestPartner () {
      // console.log('checkRequestPartner')
      var dataRole = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataRole = 'ext_buyer'
      }
      var Data = {
        company_id: this.companyId,
        seller_shop_id: this.sellerShopID
      }
      if (dataRole === 'purchaser') {
        await this.$store.dispatch('actionsCheckRequestPartner', Data)
        var res = await this.$store.state.ModuleShop.stateCheckRequestPartner
        // console.log('res', res.data.partner_status)
        // var check = 0
        if (res.result === 'SUCCESS') {
          if (res.data.shop_status !== 'inactive') {
            this.statusShop = true
            if (res.data.partner_status === 'Sending request partner.') {
              // request ยื่นคำขอ
              // check = 1
              this.status_btn = true
              this.chackpartner = true
              this.contact = true
            // } else if (res.data.partner_status === 'Shop reject a partner request.') {
              // reject ปฏิเสธ
              // check = 1
              // this.status_btn = false
              // this.chackpartner = true
              // this.contact = false
            } else if (res.data.partner_status === 'Shop cancel a partner.') {
              // inactive ยกเลิกคำขอ
              // check = 1
              this.status_btn = false
              this.chackpartner = false
              this.contact = false
              this.cancelPartner = true
            } else if (res.data.partner_status === 'Already partner.') {
              // active เป็นคู่ค้าแล้ว
              // check = 1
              this.status_btn = false
              this.chackpartner = true
              this.contact = true
              this.ParnertPerminsion = false
              this.partnerStatus = true
            } else if (res.data.partner_status === 'Check request partner.' || res.data.partner_status === 'Shop reject a partner request.') {
              if (this.can_request_partner === true) {
                this.chackpartner = false
                this.status_btn = false
                this.contact = true
              }
            }
          } else {
            this.statusShop = false
          }
        } else {
          this.chackpartner = true
          this.status_btn = false
          this.contact = true
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาลองใหม่อีกครั้ง',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.status_btn = false
        this.chackpartner = true
        this.contact = true
        this.ParnertPerminsion = true
      }
      // if (check === 0 && this.can_request_partner === true) {
      //   this.status_btn = false
      //   this.chackpartner = false
      //   this.contact = true
      //   this.ParnertPerminsion = true
      // }
      await this.checkPartner()
      await this.getcountSummaryProduct()
    },
    openToPDF (index) {
      this.dialog_pdf = true
      this.pdftofile =
        'data:application/pdf;base64,' +
        this.Detail.product_file[index].file.toString()
      // console.log('this.pdftofile', this.pdftofile)
    },
    show_inputimagse (index, item) {
      // console.log('item', item)
      if (this.selectgroup.document.length !== 0) {
        // console.log('this.selectgroup.document', this.selectgroup.document)
        if (this.selectgroup.document[index].InputFile === false) {
          this.selectgroup.document[index].InputFile = true
        } else {
          this.selectgroup.document[index].InputFile = false
        }
      } else {
        if (this.datapartner[0].document[index].InputFile === false) {
          this.datapartner[0].document[index].InputFile = true
        } else if (this.datapartner[0].document[index].InputFile === true) {
          this.datapartner[0].document[index].InputFile = false
        }
      }
    },
    closeModal () {
      this.$refs.formPartner.resetValidation()
      this.selectgroup = {
        document: []
      }
      this.dialogPartner = false
      this.email = ''
    },
    OpenModalPartner () {
      this.dialog_partner = false
      this.dialogPartner = true
      this.getapidetaildocument()
    },
    followShop () {
      this.statusPartner = 'send'
    },
    unfollowShop () {
      this.statusPartner = ''
    },
    async getDataShopPage () {
      // console.log('getDataShopPage')
      this.$store.commit('openLoader')
      if (this.$router.currentRoute.params.data !== undefined) {
        var data
        this.dataShop = []
        var path = this.$router.currentRoute.params.data
        var cleanPath = path.split('-')
        if (
          localStorage.getItem('roleUser') !== null &&
          localStorage.getItem('oneData') !== null
        ) {
          var dataRole = JSON.parse(localStorage.getItem('roleUser'))
          // var dataRole = { role: 'sale_order_no_JV' }
          // ฟิก
          if (dataRole.role === 'sale_order' || dataRole.role === 'sale_order_no_JV') {
            const PartnerID = JSON.parse(localStorage.getItem('PartnerID'))
            // console.log('', PartnerID)
            data = {
              seller_shop_id: cleanPath[cleanPath.length - 1],
              company_id: PartnerID === null ? '-1' : PartnerID,
              role: dataRole.role
            }
            // console.log('data01', data)
          } else {
            data = {
              seller_shop_id: cleanPath[cleanPath.length - 1],
              company_id: this.companyId.toString(),
              role: dataRole.role
            }
          }
        } else {
          data = {
            seller_shop_id: cleanPath[cleanPath.length - 1],
            role: 'ext_buyer'
          }
        }
        // console.log('dataaaaa', data)
        await this.$store.dispatch('actionsShopDetailPage', data)
        var response = await this.$store.state.ModuleShop.stateShopDetailPage
        if (response.result === 'SUCCESS') {
          this.time = new Date().getTime()
          this.dataShop = response.data
          if (this.dataShop.shop_media.length !== 0) {
            this.shop_logo = this.dataShop.shop_media[0].media_path
            // console.log('shop_logo', this.shop_logo)
          } else {
            this.shop_logo = ''
          }
          this.status = true
          await this.getSellerShopPage()
        } else {
          this.$store.commit('closeLoader')
          if (response.message === 'Not found this shop.') {
            this.dataShop = []
            this.status = true
          } else if (response.message === 'This user is unauthorized.') {
            this.$EventBus.$emit('refreshToken')
          }
        }
      } else {
        // console.log('checkPartner')
        this.$store.commit('closeLoader')
      }
    },
    async getSellerShopPage () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        // this.$store.commit('openLoader')
        this.GetProductType(this.changePage)
        this.getNewProduct()
        this.getBestSellerProduct()
        this.getRecommentProduct()
        this.getSaleProduct()
        this.getGeneralProduct()
        this.getOutOfStockProduct()
      }
      // this.$store.commit('closeLoader')
    },
    // สินค้ามาใหม่
    async getNewProduct () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        this.isActiveNewProduct = false
        var dataNew = {}
        // var path = this.$router.currentRoute.params.data
        // var cleanPath = path.split('-')
        var companyID
        var dataRole = ''
        if (localStorage.getItem('roleUser') !== null) {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
        } else {
          dataRole = 'ext_buyer'
        }
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        if (
          localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          if (dataRole.role !== 'sale_order') {
            if (localStorage.getItem('SetRowCompany') !== null) {
              companyID = this.companyId
            } else {
              companyID = '-1'
            }
          } else {
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          dataNew = {
            role_user: this.RoleUser,
            company_id: companyID === null ? '-1' : companyID,
            category: '',
            seller_shop_id: this.sellerShopID,
            orderBy: '',
            status_product: 'new',
            limit: this.limit,
            page: 1
          }
        } else {
          if (this.oneData.length !== 0) {
            companyID = '-1'
            dataNew = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'new',
              limit: this.limit,
              page: 1
            }
          } else {
            companyID = '-1'
            dataNew = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'new',
              limit: this.limit,
              page: 1
            }
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataNew)
        var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response.ok === 'y') {
          if (response.query_result.length !== 0) {
            this.NewProduct = []
            this.NewProduct = await [...response.query_result]
            this.isActiveNewProduct = true
          } else {
            this.NewProduct = []
            this.isActiveNewProduct = true
          }
        } else {
          this.NewProduct = []
          this.isActiveNewProduct = true
        }
      } else {
        this.isActiveNewProduct = true
      }
    },
    // สินค้าขายดี
    async getBestSellerProduct () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        this.isActiveBestSelle = false
        var dataBestSeller = {}
        // var path = this.$router.currentRoute.params.data
        // var cleanPath = path.split('-')
        var companyID
        var dataRole = ''
        if (localStorage.getItem('roleUser') !== null) {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
        } else {
          dataRole = 'ext_buyer'
        }
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          if (dataRole.role !== 'sale_order') {
            if (localStorage.getItem('SetRowCompany') !== null) {
              companyID = this.companyId
            } else {
              companyID = '-1'
            }
          } else {
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          dataBestSeller = {
            role_user: this.RoleUser,
            company_id: companyID === null ? '-1' : companyID,
            category: '',
            seller_shop_id: this.sellerShopID,
            orderBy: '',
            status_product: 'best-seller',
            limit: this.limit,
            page: 1
          }
        } else {
          if (this.oneData.length !== 0) {
            companyID = '-1'
            dataBestSeller = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'best-seller',
              limit: this.limit,
              page: 1
            }
          } else {
            companyID = '-1'
            dataBestSeller = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'best-seller',
              limit: this.limit,
              page: 1
            }
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataBestSeller)
        var response2 = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response2.ok === 'y') {
          if (response2.query_result.length !== 0) {
            this.BestSeller = []
            this.BestSeller = await [...response2.query_result]
            this.isActiveBestSelle = true
          } else {
            this.BestSeller = []
            this.isActiveBestSelle = true
          }
        } else {
          this.BestSeller = []
          this.isActiveBestSelle = true
        }
      } else {
        this.isActiveBestSelle = true
      }
    },
    // สินค้าแนะนำ
    async getRecommentProduct () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        this.isActiveRecommentProduct = false
        var dataRec = {}
        // var path = this.$router.currentRoute.params.data
        // var cleanPath = path.split('-')
        var companyID
        var dataRole = ''
        if (localStorage.getItem('roleUser') !== null) {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
        } else {
          dataRole = 'ext_buyer'
        }
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          if (dataRole.role !== 'sale_order') {
            if (localStorage.getItem('SetRowCompany') !== null) {
              companyID = this.companyId
            } else {
              companyID = '-1'
            }
          } else {
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          dataRec = {
            role_user: this.RoleUser,
            company_id: companyID === null ? '-1' : companyID,
            category: '',
            seller_shop_id: this.sellerShopID,
            orderBy: '',
            status_product: 'recommend',
            limit: this.limit,
            page: 1
          }
        } else {
          if (this.oneData.length !== 0) {
            companyID = '-1'
            dataRec = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'recommend',
              limit: this.limit,
              page: 1
            }
          } else {
            companyID = '-1'
            dataRec = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'recommend',
              limit: this.limit,
              page: 1
            }
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataRec)
        var response3 = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (response3.ok === 'y') {
          if (response3.query_result.length !== 0) {
            this.Recommended = []
            this.Recommended = await [...response3.query_result]
            // console.log('all product ======>', this.AllProduct)
            this.isActiveRecommentProduct = true
          } else {
            this.Recommended = []
            this.isActiveRecommentProduct = true
          }
        } else {
          this.Recommended = []
          this.isActiveRecommentProduct = true
        }
      } else {
        this.isActiveRecommentProduct = true
      }
    },
    // สินค้า Sale
    async getSaleProduct () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        this.isActiveSaleProduct = false
        var dataSale = {}
        // var path = this.$router.currentRoute.params.data
        // var cleanPath = path.split('-')
        var companyID
        var dataRole = ''
        if (localStorage.getItem('roleUser') !== null) {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
        } else {
          dataRole = 'ext_buyer'
        }
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          if (dataRole.role !== 'sale_order') {
            if (localStorage.getItem('SetRowCompany') !== null) {
              companyID = this.companyId
            } else {
              companyID = '-1'
            }
          } else {
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          dataSale = {
            role_user: dataRole.role,
            company_id: companyID === null ? '-1' : companyID,
            category: '',
            seller_shop_id: this.sellerShopID,
            orderBy: '',
            status_product: 'sale',
            limit: this.limit,
            page: 1
          }
        } else {
          if (this.oneData.length !== 0) {
            companyID = '-1'
            dataSale = {
              role_user: dataRole.role,
              company_id: companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'sale',
              limit: this.limit,
              page: 1
            }
          } else {
            companyID = '-1'
            dataSale = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'sale',
              limit: this.limit,
              page: 1
            }
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataSale)
        var resSale = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        if (resSale.ok === 'y') {
          if (resSale.query_result.length !== 0) {
            this.SaleProduct = []
            this.SaleProduct = await [...resSale.query_result]
            this.isActiveSaleProduct = true
          } else {
            this.SaleProduct = []
            this.isActiveSaleProduct = true
          }
        } else {
          this.SaleProduct = []
          this.isActiveSaleProduct = true
        }
      } else {
        this.isActiveSaleProduct = true
      }
    },
    // สินค้าทั่วไป
    async getGeneralProduct () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        this.isActiveGeneralProduct = false
        var dataNormal = {}
        // var path = this.$router.currentRoute.params.data
        // var cleanPath = path.split('-')
        var companyID
        var dataRole = ''
        if (localStorage.getItem('roleUser') !== null) {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
        } else {
          dataRole = 'ext_buyer'
        }
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          if (dataRole.role !== 'sale_order') {
            if (localStorage.getItem('SetRowCompany') !== null) {
              companyID = this.companyId
            } else {
              companyID = '-1'
            }
          } else {
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          dataNormal = {
            role_user: dataRole.role,
            company_id: companyID === null ? '-1' : companyID,
            category: '',
            seller_shop_id: this.sellerShopID,
            orderBy: '',
            status_product: 'general',
            limit: this.limit,
            page: 1
          }
        } else {
          if (this.oneData.length !== 0) {
            companyID = '-1'
            dataNormal = {
              role_user: dataRole.role,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'general',
              limit: this.limit,
              page: 1
            }
          } else {
            companyID = '-1'
            dataNormal = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: 'general',
              limit: this.limit,
              page: 1
            }
          }
        }
        await this.$store.dispatch('actionsSelectCategoryShopList', dataNormal)
        var resNor = await this.$store.state.ModuleShop.stateSelectCategoryShopList
        // console.log('resnor', resNor)
        if (resNor.ok === 'y') {
          if (resNor.query_result.length !== 0) {
            this.NormalProduct = []
            this.NormalProduct = await [...resNor.query_result]
            this.isActiveGeneralProduct = true
          } else {
            this.NormalProduct = []
            this.isActiveGeneralProduct = true
          }
        } else {
          this.NormalProduct = []
          this.isActiveGeneralProduct = true
        }
      } else {
        this.isActiveGeneralProduct = true
      }
    },
    // สินค้าหมด
    async getOutOfStockProduct () {
      if (this.$router.currentRoute.name === 'ShoppageUI') {
        this.isActiveOutOfStockProduct = false
        var dataOutOfStock = {}
        // var path = this.$router.currentRoute.params.data
        // var cleanPath = path.split('-')
        var companyID
        var dataRole = ''
        if (localStorage.getItem('roleUser') !== null) {
          dataRole = JSON.parse(localStorage.getItem('roleUser'))
        } else {
          dataRole = 'ext_buyer'
        }
        this.id_company = this.companyId
        if (this.id_company === undefined) {
          this.id_company = -1
        } else {
          this.id_company = this.companyId
        }
        if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
          if (dataRole.role !== 'sale_order') {
            if (localStorage.getItem('SetRowCompany') !== null) {
              companyID = this.companyId
            } else {
              companyID = '-1'
            }
          } else {
            companyID = JSON.parse(localStorage.getItem('PartnerID'))
          }
          dataOutOfStock = {
            role_user: dataRole.role,
            company_id: companyID === null ? '-1' : companyID,
            category: '',
            seller_shop_id: this.sellerShopID,
            orderBy: '',
            status_product: '',
            limit: this.limit,
            page: 1
          }
        } else {
          if (this.oneData.length !== 0) {
            companyID = '-1'
            dataOutOfStock = {
              role_user: dataRole.role,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: '',
              limit: this.limit,
              page: 1
            }
          } else {
            companyID = '-1'
            dataOutOfStock = {
              role_user: this.RoleUser,
              company_id: companyID === null ? '-1' : companyID,
              category: '',
              seller_shop_id: this.sellerShopID,
              orderBy: '',
              status_product: '',
              limit: this.limit,
              page: 1
            }
          }
        }
        await this.$store.dispatch('actionsOutOfStockProducts', dataOutOfStock)
        var resOut = await this.$store.state.ModuleShop.stateOutOfStockProducts
        if (resOut.ok === 'y') {
          if (resOut.query_result.length !== 0) {
            this.OutProduct = []
            this.OutProduct = await [...resOut.query_result]
            this.OutProduct = this.OutProduct.map((item, index) => ({
              ...item,
              normalOutStock: true
            }))
            this.isActiveOutOfStockProduct = true
          } else {
            this.OutProduct = []
            this.isActiveOutOfStockProduct = true
          }
        } else {
          this.OutProduct = []
          this.isActiveOutOfStockProduct = true
        }
      } else {
        this.isActiveOutOfStockProduct = true
      }
    },
    GetAllProduct () {
      this.$router.push(`/ListProduct/${this.header}?page=1`).catch(() => {})
    },
    // cleanData () {
    //   var array1 = this.propsData
    //   var i
    //   for (i = 0; i < 6; i++) {
    //     this.cleandata.push(array1[i])
    //   }
    //   console.log(this.cleandata)
    // }
    delete_file (index) {
      this.Detail.product_file[index] = ''
      this.nameDoc[index].name = ''
      if (this.selectgroup.document.length !== 0) {
        this.selectgroup.document[index].status = false
        this.selectgroup.document[index].InputFile = false
        this.checkDoc = true
      } else {
        this.datapartner[0].document[index].status = false
        this.datapartner[0].document[index].InputFile = false
        this.checkDoc = true
      }
    },
    selectIndex (index) {
      this.i = index
    },
    UploadFile () {
      if (this.selectgroup.document.length !== 0) {
        // console.log('ifUploadFile')
        this.selectgroup.document[this.i].status = true
        this.selectgroup.document[this.i].InputFile = false
        // this.checkDoc = this.selectgroup.document
        // console.log('this.selectgroup.document------->', this.selectgroup.document)
        for (const item of this.selectgroup.document) {
          // console.log('item---->', item)
          if (item.status === true) {
            this.checkDoc = false
          } else {
            this.checkDoc = true
            break
          }
        }
      } else {
        // console.log('elseUploadFile')
        this.datapartner[0].document[this.i].status = true
        this.datapartner[0].document[this.i].InputFile = false
        for (const item of this.datapartner[0].document) {
          if (item.status === true) {
            this.checkDoc = false
          } else {
            this.checkDoc = true
            break
          }
        }
      }
      for (let i = 0; i < this.DataFile.length; i++) {
        const element = this.DataFile[i]
        const imageSize = element.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            // var url = URL.createObjectURL(element)
            // this.Detail.product_file.push({
            //   file_data: resultReader.split(',')[1],
            //   path: url,
            //   name: this.DataFile[i].name,
            //   type: (this.DataFile[i].type.split('/', 1)).toString()
            // })
            // console.log('this.DataFile[i]', this.DataFile[i])
            this.Detail.product_file[this.i] = {
              name_document: this.DataFile[i].name,
              file: resultReader.split(',')[1]
            }
            // this.nameDoc = this.Detail.product_file[this.i].name_document
            this.nameDoc[this.i].name = this.Detail.product_file[this.i].name_document
            this.DataFile = []
          }
        } else {
          if (this.selectgroup.document.length !== 0) {
            this.selectgroup.document[this.i].status = false
            this.selectgroup.document[this.i].InputFile = false
          } else {
            this.datapartner[0].document[this.i].status = false
            this.datapartner[0].document[this.i].InputFile = false
          }
          this.DataFile = []
          this.Detail.product_file = []
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่ไฟล์ไม่เกิน 5 mb',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
      // console.log('test', this.Detail.product_file)
    },
    onPickFile (index) {
      document.getElementById('file_input').click()
    },
    async getapidetaildocument () {
      // console.log('getapidetaildocument')
      if (localStorage.getItem('shopID') !== undefined) {
        var DATA = {
          seller_shop_id: localStorage.getItem('shopID')
        }
        await this.$store.dispatch('actionsDetailDocumentShop', DATA)
        var response = await this.$store.state.ModuleShop.stateDetailDocumentShop
        // console.log('response', response.data)
        if (response.code === 200 && response.data.length !== 0) {
          this.response_doc = await this.$store.state.ModuleShop.stateDetailDocumentShop.data
          this.datapartner = this.response_doc.document_list
          for (let i = 0; i < this.response_doc.document_list.length; i++) {
            if (this.response_doc.document_list[i].document.length !== 0) {
              for (let j = 0; j < this.response_doc.document_list[i].document.length; j++) {
                this.nameDoc.push({
                  name: ''
                })
              }
            }
          }
          // if (this.datapartner.length !== 0) {
          //   for (var i = 0; i < this.response_doc.document_list[0].document.length; i++) {
          //     this.nameDoc.push({
          //       name: ''
          //     })
          //   }
          // } else {
          //   this.nameDoc = []
          // }
          if (this.response_doc.can_request_partner === 'yes') {
            this.can_request_partner = true
          } else {
            this.can_request_partner = false
          }
        } else {
          this.dialogPartner = false
          if (response.message === 'Seller shop do not have partner.') {
            this.statusOpenPartner = false
          } else {
            if (response.message === 'Seller shop is inactive.') {
              if (JSON.parse(localStorage.getItem('roleUser')).role !== 'sale_order_no_JV') {
                this.$swal.fire({
                  showConfirmButton: false,
                  timer: 3000,
                  timerProgressBar: true,
                  icon: 'error',
                  html: '<h3>ขออภัยในความไม่สะดวก <br/> เนื่องจากร้านค้านี้ได้ทำการปิดร้านอยู่</h3>'
                })
              }
              if (JSON.parse(localStorage.getItem('roleUser')).role === 'sale_order') {
                this.$router.push({ path: '/designShop' })
              } else if (JSON.parse(localStorage.getItem('roleUser')).role !== 'sale_order_no_JV') {
                this.$router.push({ path: '/' })
              }
            }
          }
        }
      }
    },
    async SavePartner () {
      this.dialogConfirm = false
      this.$store.commit('openLoader')
      // alert(this.selectgroup.document.length)
      if (this.$refs.formPartner.validate(true)) {
        if (this.Detail.product_file.length === this.selectgroup.document.length || this.datapartner[0].document.length === this.Detail.product_file.length) {
          var Data = {
            // business_id: this.oneData.user.business_id.toString(),
            email: this.email,
            business_id: localStorage.getItem('business_id'),
            seller_shop_id: this.sellerShopID,
            company_id: this.companyId.toString(),
            tier_id:
              this.selectgroup.tier_id === undefined
                ? null
                : this.selectgroup.tier_id,
            document_list: this.Detail.product_file
          }
          // console.log('datapartner', Data)
          await this.$store.dispatch('actionsPostPartnerBuyer', Data)
          this.response = await this.$store.state.ModuleShop.staterequest
          // this.response = {
          //   code: 200
          // }
          if (this.response.code === 200) {
            this.$store.commit('closeLoader')
            this.dialogPartner = false
            this.status_btn = true
            this.chackpartner = true
            this.Detail.product_file = []
            this.$EventBus.$emit('getItemNoti')
            this.dialogSuccess = true
            // this.$swal.fire({
            //   showConfirmButton: false,
            //   // timer: 1500,
            //   timerProgressBar: true,
            //   icon: 'success',
            //   html: '<h3>ยื่นคำขอเป็นคู่ค้าสำเร็จ</h3>'
            // })
          } else {
            this.$store.commit('closeLoader')
            if (
              this.response.message ===
              "Special characters are not allowed. Please check your ['name_document'] and try again."
            ) {
              this.dialogPartner = false
              this.$swal.fire({
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true,
                icon: 'error',
                html: '<h3>เกิดข้อผิดพลาด ชื่อไฟล์ไม่สามารถใส่อักษรพิเศษได้ ยกเว้น อักษรตัวเลข หรือ - หรือ _  </h3>'
              })
            } else {
              this.$store.commit('closeLoader')
              this.dialogPartner = false
              this.$swal.fire({
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true,
                icon: 'error',
                html: '<h3>เกิดข้อผิดพลาด</h3>'
              })
            }
          }
        } else {
          this.$store.commit('closeLoader')
          this.dialogPartner = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>กรุณาอัปโหลดเอกสารให้ครบ</h3>'
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>กรุณาตรวจสอบข้อมูลใหม่อีกรอบ</h3>'
        })
      }
    },
    OpenModalSpecialPrice () {
      var data = ''
      this.$refs.ModalSpecialPriceRequest.open(
        data,
        'fromShopHomePage',
        this.dataShop.shop_name_th
      )
    },
    async openModalCoupons () {
      var path = this.$router.currentRoute.params.data
      var cleanPath = path.split('-')
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      this.$refs.ModalCouponShop.open(
        this.CouponsIteam,
        cleanPath[cleanPath.length - 1],
        this.id_company,
        path
      )
    },
    // async getCoupon () {
    //   var dataRole = ''
    //   if (localStorage.getItem('roleUser') !== null) {
    //     dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //   } else {
    //     dataRole = 'ext_buyer'
    //   }
    //   var path = this.$router.currentRoute.params.data
    //   var cleanPath = path.split('-')
    //   this.id_company = this.companyId
    //   if (this.id_company === undefined) {
    //     this.id_company = -1
    //   } else {
    //     this.id_company = this.companyId
    //   }
    //   var data = {
    //     seller_shop_id: cleanPath[cleanPath.length - 1],
    //     role_user: dataRole.role,
    //     company_id: this.id_company,
    //     list_type: 'shop'
    //   }
    //   await this.$store.dispatch('actionsAllCouponInShop', data)
    //   var response = await this.$store.state.ModuleMyCouponsPoints
    //     .stateAllCouponInShop
    //   if (response.result === 'SUCCESS') {
    //     this.response_coupon = response.data.length
    //     this.CouponsIteam = []
    //     for (let i = 0; i < response.data.length; i++) {
    //       this.CouponsIteam.push({
    //         image: response.data[i].couponImagePath,
    //         name: response.data[i].couponName,
    //         description: response.data[i].couponDescription,
    //         couponDate: {
    //           useStartDate: response.data[i].collectStartDate,
    //           useEndDate: response.data[i].collectEndDate
    //         },
    //         status: response.data[i].status,
    //         couponId: response.data[i].couponId,
    //         shop_name: response.data[i].shop_name
    //       })
    //     }
    //   }
    // },
    async checkPartner () {
      this.dialog_partner = true
      if (
        this.chackpartner === true &&
        this.status_btn === true &&
        this.contact === true
      ) {
        this.dialog_partner = false
      } else if (
        this.chackpartner === true &&
        this.status_btn === false &&
        this.contact === true
      ) {
        this.dialog_partner = false
      } else if (
        this.chackpartner === false &&
        this.status_btn === false &&
        this.contact === false
      ) {
        // กรณีร้านค้า inactive
        this.dialog_partner = false
      }
    },
    checkEmaildis () {
      // console.log('test')
      // this.$refs.EmailForm.validate(true)
      if (this.$refs.EmailForm.validate(true)) {
        this.emailCheck = true
      } else {
        this.emailCheck = false
      }
      // console.log(this.emailCheck)
    },
    addAddressCus (actions) {
      var val = {
        seller_shop_id: this.sellerShopID,
        role: this.RowUserData,
        cus_id: this.cusID,
        name: '',
        first_name: '',
        last_name: '',
        phone: '',
        house_no: '',
        room_no: '',
        floor: '',
        building: '',
        moo_ban: '',
        moo_no: '',
        soi: '',
        yaek: '',
        street: '',
        sub_district: '',
        district: '',
        province: '',
        postcode: ''
      }
      this.customerSaleData = ''
      this.EditAddressDetail = val
      this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
      this.page = 'shoppage'
      localStorage.setItem('AddAddressCustomer', Encode.encode(val))
      // console.log('val', val)
      this.$EventBus.$emit('actionSale', actions)
      this.$refs.EditModalAddress.open(val, this.titleAddress, this.page, actions)
      // this.$EventBus.$emit('EditModalAddress')
      // console.log('addAddressCus', data)
    },
    addCustomerData () {
      // this.dialogChooseTypeCustomer = false
      this.showCustomer = true
      // this.dialogListPartner = false
      this.$refs.ModalManageCustomerSale.open('เพิ่มข้อมูลลูกค้า', 'shoppage', this.SaleID, '', this.SaleVendor)
    },
    async getBanner () {
      // this.$store.commit('openLoader')
      var data = {
        role_user: this.RoleUser,
        seller_shop_id: this.sellerShopID
      }
      // console.log('datagetBanner', data)
      await this.$store.dispatch('actionDetailShop', data)
      var res = this.$store.state.ModuleShop.stateDatailShop
      if (res.result === 'SUCCESS') {
        localStorage.setItem('shopName', res.data[0].name_th)
        this.checkCategory = res.data[0].custom_category
        this.FacebookUrl = res.data[0].facebook_url === null ? '' : res.data[0].facebook_url
        this.LineId = res.data[0].line_id === null ? '' : 'https://line.me/ti/p/~' + res.data[0].line_id
        // console.log('this.FacebookUrl', this.FacebookUrl)
        if (res.data[0].image_banner.length !== 0) {
          this.bannerShop = res.data[0].image_banner
        } else {
          this.bannerShop = ''
        }
        if (res.data[0].image_news.length !== 0) {
          this.newsShop = res.data[0].image_news
        } else {
          this.newsShop = ''
        }
        if (res.data[0].shop_rate_score !== null) {
          this.starShop = res.data[0].shop_rate_score
        } else {
          this.starShop = 0
        }
        if (res.data[0].seller_shop_bot_data !== undefined) {
          if (res.data[0].seller_shop_bot_data.length !== 0) {
            this.checkBot = true
          } else {
            this.checkBot = false
          }
        } else {
          this.checkBot = false
        }
        // console.log('newsShop', this.newsShop)
      } else {
        this.bannerShop = ''
        this.newsShop = ''
        this.checkBot = false
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 3000,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: res.message
        // })
      }
      // console.log('getBanner')
      // this.$store.commit('closeLoader')
    },
    async getListCouponsistNGC () {
      // console.log('getListCouponsistNGC')
      var dataRole
      var companyID = '-1'
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        dataRole = 'ext_buyer'
      }
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      if (
        localStorage.getItem('roleUser') !== null &&
        localStorage.getItem('oneData') !== null
      ) {
        if (dataRole.role !== 'sale_order') {
          if (localStorage.getItem('SetRowCompany') !== null) {
            companyID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany'))).company.company_id
          } else {
            companyID = '-1'
          }
        } else {
          companyID = JSON.parse(localStorage.getItem('PartnerID'))
        }
      }
      var data = {
        role_user: this.RoleUser,
        company_id: companyID,
        shop_id: this.sellerShopID,
        customer_id: this.RoleUser === 'sale_order_no_JV' ? this.cusID : '-1'
      }
      // console.log('data', data)
      await this.$store.dispatch('actionListCoupon', data)
      var res = this.$store.state.ModuleShop.stateListCoupon
      // console.log('data', data)
      // console.log('res', res)
      if (res.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.dataCoupons = res.data.coupon
        for (let i = 0; i < this.dataCoupons.length; i++) {
          this.dataCoupons[i].couponDate = ''
          this.dataCoupons[i].powerOfEndDate = ''
          this.dataCoupons[i].couponDate = {
            useStartDate: (this.dataCoupons[i].use_startdate !== null && this.dataCoupons[i].use_startdate !== '') ? this.formatDateToShow(this.dataCoupons[i].use_startdate) : null,
            useEndDate: (this.dataCoupons[i].use_enddate !== null && this.dataCoupons[i].use_enddate !== '') ? this.formatDateToShow(this.dataCoupons[i].use_enddate) : null
          }
          this.dataCoupons[i].powerOfEndDate = parseInt(parseFloat(parseInt(this.dataCoupons[i].use_count) / parseInt(this.dataCoupons[i].quota)) * 100)
        }
        // this.formatDateToShow(this.CouponsIteam.couponDate.useStartDate)
        // console.log('this.CouponsIteam', this.CouponsIteam)
        this.$store.commit('closeLoader')
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่</h3>'
          })
        }
      }
    },
    condition (item) {
      // console.log('ยังไม่ได้ทำจ้า', item)
      this.conditionData = item
      // console.log('this.conditionData', this.conditionData)
      this.dialogConditionCoupon = true
    },
    async getCouponsCode (item) {
      var dataRole, companyID
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        dataRole = 'ext_buyer'
      }
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      if (
        localStorage.getItem('roleUser') !== null &&
        localStorage.getItem('oneData') !== null
      ) {
        if (dataRole.role !== 'sale_order') {
          if (localStorage.getItem('SetRowCompany') !== null) {
            companyID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany'))).company.company_id
          } else {
            companyID = '-1'
          }
        } else {
          companyID = JSON.parse(localStorage.getItem('PartnerID'))
        }
      }
      var data = {
        role_user: this.RoleUser,
        company_id: companyID,
        shop_id: this.sellerShopID,
        customer_id: this.RoleUser === 'sale_order_no_JV' ? this.cusID : '-1',
        coupon_id: item.id
      }
      // console.log('data', data)
      await this.$store.dispatch('actionCollectCoupon', data)
      var resGetCoupons = this.$store.state.ModuleShop.stateCollectCoupon
      // console.log('resGetCoupons', resGetCoupons)
      if (resGetCoupons.result === 'Success') {
        this.$swal.fire({
          icon: 'success',
          text: 'เก็บคูปองสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        // this.dataCoupons = res.data.coupon
        await this.getListCouponsistNGC()
      } else {
        if (resGetCoupons.message === 'This user is Unauthorized') {
          if (localStorage.getItem('oneData') === null) {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาเข้าสู่ระบบ',
              showConfirmButton: false,
              timer: 1500
            })
            this.$router.push({ path: '/Login' }).catch(() => {})
          } else {
            this.$EventBus.$emit('refreshToken')
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'ไม่สามารถเก็บคูปองได้',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
      // console.log('resgetCode', res)
    },
    formatDateToShow (data) {
      if (!data) return null
      const datePart = data.split(' ')[0] // ตัดเฉพาะส่วนวันที่ก่อนเวลา
      const [year, month, day] = datePart.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    closed () {
      // console.log('closed')
      this.search = ''
      this.pageListcustomer = 1
      this.dialogListPartner = false
    },
    closeDialog () {
      this.dialogChooseTypeCustomer = false
      // console.log('this.cusCode', this.cusCode)
      if (this.cusCode === 0) {
        localStorage.removeItem('sale_order_customer')
      }
    },
    // async copyShortLink (id) {
    //   this.$store.commit('openLoader')
    //   const payload = {
    //     seller_shop_id: id
    //   }
    //   // console.log('data item', item)
    //   await this.axios({
    //     url: `${process.env.VUE_APP_BACK_END2}users/share_link_shop`,
    //     method: 'POST',
    //     data: payload
    //   }).then((response) => {
    //     // console.log('response', response)
    //     this.shortLink = response.data.data.apiToRedirect + '-' + id
    //     this.$store.commit('closeLoader')
    //   })
    //   navigator.clipboard.writeText(this.shortLink)
    //   this.$swal.fire({
    //     showConfirmButton: false,
    //     timer: 2000,
    //     timerProgressBar: true,
    //     icon: 'success',
    //     html: '<h3>คัดลอกลิงก์สำเร็จ</h3>'
    //   })
    //   // this.dialogCopyText = 'คัดลอกลิงก์สำเร็จ'
    //   // this.dialogCopy = true
    // },
    async copyShortLink (id) {
      this.$store.commit('openLoader')
      var link = window.location.href
      // console.log('window.location.href', window.location.href)
      link = link.split('?')[0]
      link = decodeURIComponent(link)
      // console.log(link)
      const payload = {
        link: link
      }
      // console.log('data item', item)
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}users/generate_shop_URL`,
        method: 'POST',
        data: payload
      }).then((response) => {
        this.shortLink = response.data.data.short_url
        // console.log('this.shortLink', this.shortLink)
      })
      navigator.clipboard.writeText(this.shortLink)
      this.$store.commit('closeLoader')
      this.$swal.fire({
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true,
        icon: 'success',
        html: '<h3>คัดลอกลิงก์สำเร็จ</h3>'
      })
      // this.dialogCopyText = 'คัดลอกลิงก์สำเร็จ'
      // this.dialogCopy = true
    },
    async getDataArticle (id, role) {
      if (role === 'ext_buyer') {
        role = 'buyer'
      }
      this.$store.commit('openLoader')
      const payload = {
        seller_shop_id: id,
        role: role
      }
      // console.log('data item', item)
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END}api/list_article_with_seller_shop_v2`,
        method: 'POST',
        data: payload
      }).then((response) => {
        // console.log('response', response)
        // console.log('response', response.data.data[0])
        this.itemArticle = response.data.data.sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at)
        })
        if (response.data.data.length === 0) {
          this.disabledPagination = false
        } else {
          this.disabledPagination = true
        }
        this.$store.commit('closeLoader')
      })
    },
    calDiffDate (dateA, dateB) {
      var date1 = new Date(dateA)
      var date2 = new Date(dateB)
      var DiffInTime = date2.getTime() - date1.getTime()

      var DiffInDays = DiffInTime / (1000 * 3600 * 24)
      var DiffMin = DiffInDays < 1 ? (date2.getTime() - date1.getTime()) / (1000 * 60) : 0
      var DiffHours = DiffMin > 70 ? DiffMin / 60 : 0
      // console.log(dateA, DiffInDays, DiffMin, DiffHours)
      return (DiffInDays > 1 ? `${Math.floor(DiffInDays)} วันที่ผ่านมา` : DiffMin > 70 ? `${Math.floor(DiffHours)} ชั่วโมงที่ผ่านมา` : `${Math.floor(DiffMin)} นาทีที่ผ่านมา`)
    },
    youtube_parser (url) {
      let ID = ''
      url = url.replace(/(>|<)/gi, '').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
      if (url[2] !== undefined) {
        ID = url[2].split(/[^0-9a-z_-]/i)
        ID = ID[0]
      } else {
        ID = false
      }
      return ID
    },
    playing () {
      // console.log('we are watching!!!')
    },
    async goToArticle (item) {
      if (this.MobileSize) {
        this.$router.push({
          path: `/ViewArticleMobile?Article=${item.id}&Name=${item.name_th}&ID=${item.seller_shop_id}`
        }).catch(() => {})
      } else {
        this.$router.push({
          path: `/ViewArticle?Article=${item.id}&Name=${item.name_th}&ID=${item.seller_shop_id}`
        }).catch(() => {})
      }
    }
    // async getProductFlashSale () {
    //   // var shopID = localStorage.getItem('shopSellerID')
    //   var data = {
    //     // seller_shop_id: parseInt(shopID)
    //     seller_shop_id: 5
    //   }
    //   await this.$store.dispatch('actionsGetFlashSale', data)
    //   var response = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
    //   if (response.result === 'SUCCESS') {
    //     // console.log('response', response)
    //     // console.log('response itemListDetailArticle', response.data.data)
    //     this.itemListFlashSale = response.data.data
    //     // if (response.data.length === 0) {
    //     //   this.disabledArticle = false
    //     // } else {
    //     //   this.disabledArticle = true
    //     // }
    //   }
    // }
  }
}
</script>

<style scoped>
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
/* .powerOfEndDate {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
} */
.btnSizeIpad {
  width: 80px !important;
}
.btnSizeDesk {
  width: 80px !important;
  height: 40px !important;
}
.couponAll {
  cursor: pointer;
}
.couponAll:hover {
  transform: scale(1.02) !important;
}
.progress-gradient {
width: 100%;
height: 100%;
border-radius: 48px;
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.vProgressLinearDesk {
  max-width: 4.5vw;
}
.vProgressLinearIped {
  max-width: 8vw;
}
.vProgressLinearMobile {
  max-width: 23vw;
}
.condition{
  pointer-events: auto;
}
.condition:hover {
  pointer-events: auto;
}
.Test {
  padding-top: 5%;
}
.backIMG{
  background-image: url('../../assets/ConponNGC/shopConpon/background.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMG{
  background-image: url('../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMGMobile{
  background-image: url('../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
}
.couponIMGDesk{
  background-image: url('../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  padding: 1%;
  width: 350px;
}
.cardChooseType{
  padding-top: 35px;
  text-align: center;
  border-radius: 10px;
}
.couponHead {
  position: absolute;
  padding-top: 7%;
  padding-left: 34%;
  font-weight: 700;
  font-size: 96px;
  color: #E19B24; background-image: -webkit-linear-gradient(315deg, #E19B24 49%, #FFC700 77%); background-clip: text; -webkit-background-clip: text; text-fill-color: transparent; -webkit-text-fill-color: transparent;
}
.coupon {
  font-weight: 700;
  font-size: 24px;
  position: absolute;
  padding-top: 25%;
  padding-left: 80%;
}
.btnCoupon {
  /* position: absolute;
  padding-top: 29.5%; */
  background-image: linear-gradient(#D2A00A, #FFDF81, #D2A00A);
  color: #99620E;
  font-weight: 700;
}
.cardChooseType:hover {
  transform: scale(1.05);
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
  border-bottom: 1px solid #27AB9C !important;
}
.chipcus {
  border-radius: 6px !important;
  /* background-color: #27AB9C !important;
  color: white !important; */
}
>>> .v-dialog {
    box-shadow: none;
}
.setbackground > .v-dialog .v-dialog--active {
  box-shadow: none !important;
}
.ipadProShophomepage {
  margin-bottom: 0px !important;
  font-size: 22px;
}
.v-card-store {
  border-radius: 8px !important;
  border-color: #27AB9C;
  border-width: 1px;
}
.v-card-storeIpad {
  width: 100px;
  height: 66px;
}
.v-card-storeIpadPro {
  width: 118px;
  height: 72px;
}
.v-card-storeMobile {
  width: 32%;
  height: 60px;
  margin-right: 4px;
  margin-bottom: 4px;
}
.textCardNumberMobile {
  font-size: 9px !important;
  font-weight: 700;
}
.textCardMobile {
  font-size: 9px !important;
  margin-top: 0%;
  margin-bottom: 0%;
  font-weight: 500;
  color: #636363;
  /* padding-top: 25%; */
  /* padding-right: 25%;
  padding-left: 25%; */
}
.v-card-storeDesk {
  max-width: 18%;
  width: 18%;
  height: 87px;
}
.v-chip-store {
  font-weight: 500;
  line-height: 22.4px;
  font-size: 16px;
}
.v-chip-storeMobile {
  font-weight: 400;
  line-height: 22.4px;
  font-size: 10px;
}
.classFBLineSizeIpad {
  height: 24px;
  width: 24px;
  max-width: 24px;
}
.classFBLineSizeDesk {
height: 24px;
width: 24px;
max-width: 24px;
}
>>>.cardProductIpad {
  width: 33.3%;
  max-width: 33.3%;
  flex-basis: 33.3%;
}
>>>.cardProductMobile {
  width: 50%;
  max-width: 50%;
  flex-basis: 20%;
}
>>>.cardProductDesk{
  width: 20%;
  max-width: 20%;
  flex-basis: 20%;
}
>>>.cardProductIpadPro {
  width: 25%;
  max-width: 25%;
  flex-basis: 20%;
}
>>>.cardProductIpadProFrist {
  width: 100%;
  max-width: 100%;
  /* flex-basis: 50%; */
}
>>> .cat:hover {
 cursor: pointer;
 background-color: #F0F0F0 !important;
}
>>> .v-input__slot .v-select__selection--comma {
  color: #000000;
}
.Deskfont {
  font-weight: bold;
  font-size: 24px;
  text-transform: uppercase;
}
.ipadfont {
  font-weight: bold;
  font-size: 16px;
  text-transform: uppercase;
}
.ipadfonts {
  font-weight: bold;
  font-size: 10px;
  text-transform: uppercase;
}
>>> .theme--light.v-pagination .v-pagination__navigation {
  box-shadow: none !important;
}
>>>.ChipWating  {
  /* left: 0.8%; */
  border-radius: 24px 0px 0px 24px !important;
}
>>>.ChipHeadCoupons  {
  /* left: 0.8%; */
  border-radius: 0px 4px 4px 0px !important;
}
>>>.ChipWatingMobile {
  left: 0.8%;
  border-radius: 24px 0px 0px 24px !important;
}
>>>.starFont {
  font-size: 18px;
  font-weight: 400;
  line-height: 25.2px;
}
>>> .colorTap .ant-tabs-ink-bar {
  border-bottom: 2px solid #27AB9C !important;
  background-color: #27AB9C;
}
</style>
<style lang="scss" scoped>
::v-deep .custom-list {
    .v-list-item__icon:last-of-type:not(:only-child) {
      margin-left: 0 !important; /* Override margin-left */
    }
  }
</style>
<style scoped>
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  height: 50%;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  margin-top: 15px;
  margin-bottom: 15px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  margin-top: 15px;
  margin-bottom: 15px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
</style>
