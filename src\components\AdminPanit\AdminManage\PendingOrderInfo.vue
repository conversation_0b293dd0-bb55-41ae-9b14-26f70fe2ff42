<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <div class="d-flex align-center pb-2">
        <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ข้อมูลออเดอร์คงค้าง</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ข้อมูลออเดอร์คงค้าง</v-card-title>
        <v-spacer></v-spacer>
        <v-menu
          v-model="menu"
          offset-y
          :close-on-content-click="false"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn v-if="!MobileSize" v-bind="attrs" v-on="on" :style="!IpadProSize && !IpadSize ? 'padding: 35px;' : 'padding: 35px; width: 26.5vw;'" color="#f4fcff">
              <div style="display: flex; gap: 1vw;">
                <v-icon v-if="!IpadProSize && !IpadSize" color="#2d9cdb" style="background-color: #d6eefa; padding: 5px; border-radius: .5vw;">mdi-calendar</v-icon>
                <div style="display: flex; flex-direction: column; align-items: flex-start;">
                  <span>Filter Period</span>
                  <span style="font-size: 12px">{{ displayDateFilterText }}</span>
                </div>
                <v-icon class="mdi-rotate-180 mb-2" color="#b9bbbd">mdi-apple-keyboard-control</v-icon>
              </div>
            </v-btn>
            <v-btn v-else v-bind="attrs" v-on="on" style="padding: .5vw;" color="#f4fcff">
              <div style="display: flex; gap: 1vw;">
                <v-icon color="#2d9cdb">mdi-filter</v-icon>
                <div style="display: flex; flex-direction: column; align-items: flex-start;">
                  <span>ตัวกรอง</span>
                  <!-- <span style="font-size: 12px">{{ displayDateFilterText }}</span> -->
                </div>
              </div>
            </v-btn>
          </template>

          <v-card>
            <v-card-text>
              <v-radio-group v-model="selectedFilterType" row>
                <v-radio label="รายปี" value="yearly"></v-radio>
                <v-radio label="รายเดือน" value="monthly"></v-radio>
                <v-radio label="รายวัน" value="daily"></v-radio>
              </v-radio-group>

              <template v-if="selectedFilterType === 'yearly'">
                <v-select
                  v-model="selectedYear"
                  :items="years"
                  label="ปี"
                  dense
                  outlined
                >
                  <template slot="selection">
                    {{selectedYear + 543}}
                  </template>
                  <template slot="item" slot-scope="data">
                    {{data.item + 543}}
                  </template>
                </v-select>
              </template>

              <template v-if="selectedFilterType === 'monthly'">
                <v-select
                  v-model="selectedYearMonth"
                  :items="years"
                  label="ปี"
                  dense
                  outlined
                >
                  <template slot="selection">
                    {{selectedYearMonth + 543}}
                  </template>
                  <template slot="item" slot-scope="data">
                    {{data.item + 543}}
                  </template>
                </v-select>
                <v-select
                  v-model="selectedMonth"
                  :items="months"
                  label="เดือน"
                  dense
                  outlined
                ></v-select>
              </template>

              <template v-if="selectedFilterType === 'daily'">
                <v-date-picker
                  v-model="selectedDays"
                  range
                  locale="th"
                  :show-current="true"
                  @change="handleDateChange"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                ></v-date-picker>
              </template>
            </v-card-text>

            <v-card-actions style="display: flex; justify-content: center;">
              <v-btn color="primary" @click="applyDateFilter">ยืนยัน</v-btn>
              <v-btn text @click="menu = false">ยกเลิก</v-btn>
            </v-card-actions>
          </v-card>
        </v-menu>
      </div>
      <div v-if="MobileSize" style="margin: 5vw;">
        <v-select
          v-model="mobileMenuSelect"
          :items="mobileMenu"
          label="เลือกเมนู"
          dense
          outlined
          @change="handleMenuChange"
        ></v-select>
      </div>
    </v-card>
    <v-card elevation="0" class="mt-3" v-if="mobileMenuSelect === 'ภาพรวม Order คงค้าง' && MobileSize || !MobileSize">
      <v-row>
        <v-col cols="12" sm="12" md="3">
          <v-row>
            <v-col cols="12" sm="12" class="pl-8 d-flex justify-start align-center mb-4">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/calendar.jpg" width="20px" class="mr-2">
              <span style="font-weight: bold; font-size: 18px; color: #6B7A99;">วันที่สั่ง Order</span>
            </v-col>
          </v-row>
          <v-card elevation="0" style="background-color: #FF66330D;">
            <v-row no-gutters>
              <v-col class="my-4 pr-8 d-flex" style="justify-content: space-between;">
                <span style="color: #FF6633; font-size: 16px;" class="mr-6 ml-6">ข้อมูลสะสม ณ วันที่</span>
                <span style="color: #FF6633; font-size: 16px;">{{dateNow}}</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" sm="12" md="9">
          <v-row>
            <v-col>
              <v-card elevation="0" style="border-radius: 0px; border: 2px dashed #a0a0a0; padding: .5vw;">
                <v-row>
                  <v-col class="d-flex align-center ml-3">
                    <img src="@/assets/ImageINET-Marketplace/ICONShop/Group.jpg" width="25px" class="mr-1">
                    <span>จำนวน Order ทั้งหมดรายวัน</span>
                  </v-col>
                  <v-spacer v-if="!MobileSize"></v-spacer>
                  <v-col class="pa-5 pr-16">
                    <v-card elevation="0" style="border-radius: 0px; background-color: #D8E8FF87;" class="d-flex justify-center align-center">
                      <span class="mr-6" style="font-size: 24px; font-weight: bold; color: #4C6A79">{{overallOrder}}</span>
                      <span style="font-size: 10px; font-weight: bold; color: #4C6A79">รายการ</span>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-card style="border-radius: 0px;  border: 2px dashed #a0a0a0; padding: .5vw; box-shadow: 0px 0px 0px 0px;">
                <v-row>
                  <v-col class="d-flex align-center ml-3">
                    <img src="@/assets/ImageINET-Marketplace/ICONShop/box.jpg" width="25px" class="mr-1">
                    <span>Order คงค้างทั้งหมด</span>
                  </v-col>
                  <v-spacer v-if="!MobileSize"></v-spacer>
                  <v-col class="pa-5 pr-16">
                    <v-card elevation="0" style="border-radius: 0px; background-color: #D8E8FF87;" class="d-flex justify-center align-center">
                      <span class="mr-6" style="font-size: 24px; font-weight: bold; color: #4C6A79">{{onProgressOrder}}</span>
                      <span style="font-size: 10px; font-weight: bold; color: #4C6A79">รายการ</span>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <!-- <v-row>
        <v-col cols="12" md="3" sm="12" class="d-flex align-center">
          <v-row>
            <v-col cols="12" sm="12" class="pl-8 d-flex justify-start align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/calendar.jpg" width="20px" class="mr-2">
              <span style="font-weight: bold; font-size: 18px; color: #6B7A99;">วันที่สั่ง Order</span>
            </v-col>
          </v-row>
          <v-row v-if="MobileSize || IpadSize" no-gutters>
            <v-col cols="12" :class="MobileSize || IpadSize ? 'd-flex justify-end pr-3' : ''">
              <span style="color: #FF6633; font-size: 10px;" :class="MobileSize || IpadSize ? '' : 'mr-6 ml-6'">ข้อมูลสะสม ณ วันที่</span>
            </v-col>
            <v-col cols="12" :class="MobileSize || IpadSize ? 'd-flex justify-end pr-3' : ''">
              <span style="color: #FF6633;">{{displayDateFilterText}}</span>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="9" sm="12" >
          <v-card elevation="0" style="border-radius: 0px; border: 2px dashed #a0a0a0; padding: .5vw; white-space: nowrap;">
            <v-row>
              <v-col class="d-flex align-center ml-3">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/Group.jpg" width="25px" class="mr-1">
                <span>จำนวน Order ทั้งหมดรายวัน</span>
              </v-col>
              <v-spacer v-if="!MobileSize"></v-spacer>
              <v-col class="pa-5 pr-16">
                <v-card elevation="0" style="border-radius: 0px; background-color: #D8E8FF87;" class="d-flex justify-center align-center">
                  <span class="mr-6" style="font-size: 24px; font-weight: bold; color: #4C6A79">{{onProgressOrder}}</span>
                  <span style="font-size: 10px; font-weight: bold; color: #4C6A79">รายการ</span>
                </v-card>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="3" sm="12" class="d-flex align-center" v-if="!MobileSize && !IpadSize">
          <v-card elevation="0" style="background-color: #FF66330D;">
            <v-row no-gutters v-if="!MobileSize && !IpadSize">
              <v-col>
                <span style="color: #FF6633; font-size: 10px;" class="mr-6 ml-6">ข้อมูลสะสม ณ วันที่</span>
              </v-col>
            </v-row>
            <v-row no-gutters v-if="!MobileSize">
              <v-col class="d-flex justify-start px-6 align-start">
                <span style="color: #FF6633;">{{displayDateFilterText}}</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" md="9" sm="12">
          <v-card style="border-radius: 0px;  border: 2px dashed #a0a0a0; padding: .5vw; white-space: nowrap; box-shadow: 0px 0px 0px 0px;">
            <v-row>
              <v-col class="d-flex align-center ml-3">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/box.jpg" width="25px" class="mr-1">
                <span>Order คงค้างทั้งหมด</span>
              </v-col>
              <v-spacer v-if="!MobileSize"></v-spacer>
              <v-col class="pa-5 pr-16">
                <v-card elevation="0" style="border-radius: 0px; background-color: #D8E8FF87;" class="d-flex justify-center align-center">
                  <span class="mr-6" style="font-size: 24px; font-weight: bold; color: #4C6A79">{{overallOrder}}</span>
                  <span style="font-size: 10px; font-weight: bold; color: #4C6A79">รายการ</span>
                </v-card>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row> -->
    </v-card>
    <v-card style="border-radius: 0px; border: 2px dashed #a0a0a0; box-shadow: 0px 0px 0px 0px; margin-top: 12px" class="pa-3" v-if="mobileMenuSelect === 'ภาพรวม Order คงค้าง' && MobileSize || !MobileSize">
      <v-row>
        <v-col class="d-flex align-center justify-start">
          <img src="@/assets/ImageINET-Marketplace/ICONShop/pending.jpg" width="30px" class="mr-1">
          <span style="font-weight: bold; font-size: 20px; color: #6B7A99; margin-right: 3px;" class="mr-3">Status Order: </span>
          <div style="background-color: #D8E9FF; border-radius: 5px;" class="px-3">
            <span style="font-weight: bold; font-size: 20px; color: #6B7A99;">On process</span>
          </div>
        </v-col>
      </v-row>
      <v-row class="pa-3">
        <v-col cols="12" md="4" sm="12" :class="MobileSize ? 'pa-1 mb-3' : 'pa-1 mb-2'">
          <v-card style="background-color: #F2FFF1;" :style=" IpadProSize ? 'height: 183px' : ''" class="mb-3">
            <v-row>
              <v-col class="d-flex align-center pa-5 pb-0">
                <v-icon color="#06D6A0">mdi-circle-medium</v-icon>
                <span style="font-size: 18px;" :style="IpadSize ? 'font-size: 24px' : ''">Order คงค้าง 1-3 วัน</span>
                <v-spacer></v-spacer>
                <v-btn icon color="#06D6A0" @click="getReportExcel('normal')" height="45" width="45">
                  <v-icon size="30">mdi-file-excel-outline</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="pa-3 pl-8">
                <span style="color: #06D6A0;" :style="IpadSize ? 'font-size: 24px' : ''">({{normalRate}}%)</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-end align-end pa-0 pb-1" :class="IpadSize ? 'align-center' : IpadProSize ? 'pa-6 pt-0' : ''">
                <span class="mr-3" style="font-size: 40px; font-weight: bold; color: #595959;">{{normalItem}}</span>
                <span class="pr-5" style="font-size: 10px; font-weight: bold; color: #595959;" :style="IpadSize ? 'font-size: 24px' : IpadProSize ? 'font-size: 20px' : 'font-size: 20px'">รายการ</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" md="4" sm="12" :class="MobileSize ? 'pa-1 mb-3' : 'pa-1 pb-3'">
          <v-card style="background-color: #FFFDED;" :style=" IpadProSize ? 'height: 183px' : ''" class="mb-3" >
            <v-row>
              <v-col class="d-flex align-center pa-5 pb-0">
                <v-icon color="#FFD166">mdi-circle-medium</v-icon>
                <span style="font-size: 18px;" :style="IpadSize ? 'font-size: 24px' : ''">Order คงค้าง 4-6 วัน</span>
                <v-spacer></v-spacer>
                <v-btn icon color="#FFD166" @click="getReportExcel('monitor')" height="45" width="45">
                  <v-icon size="30">mdi-file-excel-outline</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="pa-3 pl-8">
                <span style="color: #FFD166;" :style="IpadSize ? 'font-size: 24px' : ''">({{monitorRate}}%)</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-end align-end pa-0 pb-1" :class="IpadSize ? 'align-center' : IpadProSize ? 'pa-6 pt-0' : ''">
                <span class="mr-3" style="font-size: 40px; font-weight: bold; color: #595959;">{{monitorItem}}</span>
                <span class="pr-5" style="font-size: 10px; font-weight: bold; color: #595959;" :style="IpadSize ? 'font-size: 24px' : IpadProSize ? 'font-size: 20px' : 'font-size: 20px'">รายการ</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" md="4" sm="12" class="pa-1">
          <v-card style="background-color: #FFF6F6;" :style=" IpadProSize ? 'height: 183px' : ''" class="mb-3">
            <v-row>
              <v-col class="d-flex align-center pa-5 pb-0">
                <v-icon color="#D56062">mdi-circle-medium</v-icon>
                <span style="font-size: 18px;" :style="IpadSize ? 'font-size: 24px' : ''">Order คงค้าง 7 หรือมากกว่า</span>
                <v-spacer></v-spacer>
                <v-btn icon color="#D56062" @click="getReportExcel('alarm')" height="45" width="45">
                  <v-icon size="30">mdi-file-excel-outline</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="pa-3 pl-8">
                <span style="color: #D56062;" :style="IpadSize ? 'font-size: 24px' : ''">({{alarmRate}}%)</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-end align-end pa-0 pb-1" :class="IpadSize ? 'align-center' : IpadProSize ? 'pa-0' : ''">
                <span class="mr-3" style="font-size: 40px; font-weight: bold; color: #595959;">{{alarmItem}}</span>
                <span class="pr-5" style="font-size: 10px; font-weight: bold; color: #595959;" :style="IpadSize ? 'font-size: 24px' : IpadProSize ? 'font-size: 20px' : 'font-size: 20px'">รายการ</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <!-- desk top -->
    <v-card elevation="0" class="mt-3" v-if="(mobileMenuSelect === 'ภาพรวม Order คงค้าง' && MobileSize) || !MobileSize">
      <!-- <v-row v-if="!MobileSize && !IpadSize && !IpadProSize">
        <v-col class="d-flex " style="gap: 8px">
          <v-card width="300px" class="pa-2 align-center" style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); color: #595959; border-radius: 0px; display: flex; height: 55px;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/carlendar2.jpg" width="25px" class="mr-1">
            <span class="mx-2">จำนวนวันทั้งหมด</span>
            <div style="background-color: #D8E9FF; border-radius: 5px;" class="px-1">
              <span style="color: black; font-weight: bold;">{{daysDifference}}</span>
            </div>
            <span class="ml-2">Day</span>
          </v-card>
          <v-card width="200px" style="background-color: #D8E9FF; font-size: 12px; font-weight: bold; height: 55px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center">
                <span>Total Order Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end">
                <span class="mr-1">{{successGroupItem}}</span>
                <span class="mr-1" style="font-size: 10px">รายการ</span>
                <span style="font-size: 10px; color: #595959;">({{successGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
          <v-card width="200px" style="background-color: #D8E9FF; font-size: 12px; font-weight: bold; height: 55px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center">
                <span>Total Order Cancel</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end">
                <span class="mr-1">{{cancelGroupItem}}</span>
                <span class="mr-1" style="font-size: 10px">รายการ</span>
                <span style="font-size: 10px; color: #595959;">({{cancelGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
          <v-card width="200px" style="background-color: #D8E9FF; font-size: 12px; font-weight: bold; height: 55px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center">
                <span>Total Order Return Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end">
                <span class="mr-1">{{returnGroupItem}}</span>
                <span class="mr-1" style="font-size: 10px">รายการ</span>
                <span style="font-size: 10px; color: #595959;">({{returnGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
          <v-card width="200px" style="background-color: #D8E9FF; font-size: 12px; font-weight: bold; height: 55px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center">
                <span>Total Order On Process</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end">
                <span class="mr-1">{{onProcessGroupItem}}</span>
                <span class="mr-1" style="font-size: 10px">รายการ</span>
                <span style="font-size: 10px; color: #595959;">({{onProcessGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
          <v-card width="200px" style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); font-size: 10px; font-weight: bold; height: 55px; border-radius: 0px;" class="pl-1">
            <v-row no-gutters class="mt-1">
              <v-col class="d-flex justify-start">
                <span>Status</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #FFF1F2; height: 12px;" class="px-1">
                  <span style="font-size: 8px;">On Process</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusOnProcessItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #E6FFF7; height: 12px" class="px-1">
                  <span style="font-size: 8px;">Close</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusCloseItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row> -->
      <!-- mobile -->
      <v-row v-if="MobileSize">
        <!-- กล่องแรก -->
        <v-col cols="12" class="pb-0">
          <v-card class="px-2 align-center justify-center" style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); color: #595959; border-radius: 0px; display: flex; height: 60px;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/carlendar2.jpg" width="25px" class="mr-1">
            <span class="mx-2"  style="font-size: 18px">จำนวนวันทั้งหมด</span>
            <div style="background-color: #D8E9FF; border-radius: 5px;" class="px-1">
              <span style="color: black; font-weight: bold;">{{daysDifference}}</span>
            </div>
            <span class="ml-2">Day</span>
          </v-card>
        </v-col>
          <!-- กล่องหก -->
        <!-- <v-col cols="12" class="pb-0 pt-2 mt-1 mb-2">
          <v-card style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px; line-height: 15px;" class="pl-1">
            <v-row no-gutters class="pt-2 pl-2">
              <v-col class="d-flex justify-start">
                <span>Status</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <div style="background-color: #FFF1F2; height: 12px;" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">On Process</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusCloseItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #E6FFF7; height: 12px" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">Close</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusOnProcessItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col> -->
          <!-- กล่องสอง -->
        <v-col cols="12" class="pb-0">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-0">
              <v-col class="d-flex justify-center pt-1">
                <span style="font-size: 16px">Total Order Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1" style="font-size: 16px">{{successGroupItem}}</span>
                <span class="mr-1" style="font-size: 16px">รายการ</span>
                <span style="font-size: 16px; color: #595959;">({{successGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสาม -->
        <v-col cols="12" class="pb-0 pt-1">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-1">
                <span style="font-size: 18px;">Total Order Cancel</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end pt-1">
                <span class="mr-1" style="font-size: 16px">{{cancelGroupItem}}</span>
                <span class="mr-1" style="font-size: 16px">รายการ</span>
                <span style="font-size: 16px; color: #595959;">({{cancelGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสี่ -->
        <v-col cols="12" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-1">
                <span style="font-size: 18px">Total Order Return Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1" style="font-size: 16px">{{returnGroupItem}}</span>
                <span class="mr-1" style="font-size: 16px">รายการ</span>
                <span style="font-size: 16px; color: #595959;">({{returnGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องห้า -->
        <v-col cols="12" class="pt-2 pb-0">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-1">
                <span style="font-size: 18px">Total Order On Process</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1" style="font-size: 16px">{{onProcessGroupItem}}</span>
                <span class="mr-1" style="font-size: 16px">รายการ</span>
                <span style="font-size: 16px; color: #595959;">({{onProcessGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 74px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span style="font-size: 18px">Total Order Lost</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{lostGroupItem}}</span>
                <span class="mr-1" style="font-size: 16px">รายการ</span>
                <span style="font-size: 16px; color: #595959;">({{lostGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <!-- Ipad -->
      <v-row v-if="IpadSize">
        <!-- กล่องแรก -->
        <v-col cols="6" class="pb-0">
          <v-card class="px-2 align-center justify-center" style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); color: #595959; border-radius: 0px; display: flex; height: 60px;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/carlendar2.jpg" width="25px" class="mr-1">
            <span class="mx-2">จำนวนวันทั้งหมด</span>
            <div style="background-color: #D8E9FF; border-radius: 5px;" class="px-1">
              <span style="color: black; font-weight: bold;">{{daysDifference}}</span>
            </div>
            <span class="ml-2">Day</span>
          </v-card>
        </v-col>
          <!-- กล่องหก -->
        <v-col cols="6" class="pb-0 pt-4 pb-2">
          <v-card style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px; line-height: 15px" class="pl-1">
            <v-row no-gutters class="pt-2 pl-2">
              <v-col class="d-flex justify-start">
                <span>Status</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <div style="background-color: #FFF1F2; height: 12px;" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">On Process</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusOnProcessItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #E6FFF7; height: 12px" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">Close</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusCloseItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสอง -->
        <v-col cols="6" class="pb-0 pt-1">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{successGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{successGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสาม -->
        <v-col cols="6" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Cancel</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end pt-1">
                <span class="mr-1">{{cancelGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{cancelGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสี่ -->
        <v-col cols="6" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Return Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{returnGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{returnGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องห้า -->
        <v-col cols="6" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order On Process</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{onProcessGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{onProcessGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Lost</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{lostGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{lostGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <!-- IpadPro -->
      <v-row v-if="IpadProSize">
        <!-- กล่องแรก -->
        <v-col cols="6" class="pb-0">
          <v-card class="px-2 align-center justify-center" style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); color: #595959; border-radius: 0px; display: flex; height: 60px;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/carlendar2.jpg" width="25px" class="mr-1">
            <span class="mx-2">จำนวนวันทั้งหมด</span>
            <div style="background-color: #D8E9FF; border-radius: 5px;" class="px-1">
              <span style="color: black; font-weight: bold;">{{daysDifference}}</span>
            </div>
            <span class="ml-2">Day</span>
          </v-card>
        </v-col>
        <!-- กล่องหก -->
        <v-col cols="6" class="pb-0 pt-3 pb-2">
          <v-card style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px; line-height: 15px" class="pl-1">
            <v-row no-gutters class="pt-2 pl-2">
              <v-col class="d-flex justify-start">
                <span>Status</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <div style="background-color: #FFF1F2; height: 12px;" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">On Process</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusOnProcessItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #E6FFF7; height: 12px" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">Close</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusCloseItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสอง -->
        <v-col cols="6" class="pb-0 pt-1">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{successGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{successGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสาม -->
        <v-col cols="6" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Cancel</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end pt-1">
                <span class="mr-1">{{cancelGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{cancelGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสี่ -->
        <v-col cols="6" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Return Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{returnGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{returnGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องห้า -->
        <v-col cols="6" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order On Process</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{onProcessGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{onProcessGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Lost</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{lostGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{lostGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-if="!MobileSize && !IpadSize && !IpadProSize">
        <!-- กล่องแรก -->
        <v-col cols="6" class="pb-0">
          <v-card class="px-2 align-center justify-center" style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); color: #595959; border-radius: 0px; display: flex; height: 60px;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/carlendar2.jpg" width="25px" class="mr-1">
            <span class="mx-2">จำนวนวันทั้งหมด</span>
            <div style="background-color: #D8E9FF; border-radius: 5px;" class="px-1">
              <span style="color: black; font-weight: bold;">{{daysDifference}}</span>
            </div>
            <span class="ml-2">Day</span>
          </v-card>
        </v-col>
        <!-- กล่องหก -->
        <v-col cols="6" class="pb-0 pt-3 pb-2">
          <v-card style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px; line-height: 15px" class="pl-1">
            <v-row no-gutters class="pt-2 pl-2">
              <v-col class="d-flex justify-start">
                <span>Status</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <div style="background-color: #FFF1F2; height: 12px;" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">On Process</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusOnProcessItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #E6FFF7; height: 12px" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">Close</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusCloseItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสอง -->
        <v-col cols="6" class="pb-0 pt-1">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{successGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{successGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสาม -->
        <v-col cols="6" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Cancel</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-end pt-1">
                <span class="mr-1">{{cancelGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{cancelGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องสี่ -->
        <v-col cols="6" class="pb-0 pt-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Return Success</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{returnGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{returnGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
          <!-- กล่องห้า -->
        <v-col cols="6" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order On Process</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{onProcessGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{onProcessGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" class="pb-0 pt-2 pb-2">
          <v-card style="background-color: #D8E9FF; font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px;">
            <v-row no-gutters class="mt-2">
              <v-col class="d-flex justify-center pt-2">
                <span>Total Order Lost</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <span class="mr-1">{{lostGroupItem}}</span>
                <span class="mr-1" style="font-size: 12px">รายการ</span>
                <span style="font-size: 12px; color: #595959;">({{lostGroupRate}}%)</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <v-row v-if="mobileMenuSelect === 'ตาราง Order คงค้างรายวัน' && MobileSize || !MobileSize">
      <v-spacer></v-spacer>
      <v-col cols="4" md="3" sm="4">
        <v-select
          outlined
          dense
          hide-details
          v-model="StatusFilter.value"
          :items="StatusFilterItem"
          style="border: 1px dashed; border-radius: 8px"
          class="setCustomSelect"
        ></v-select>
      </v-col>
    </v-row>
    <v-card elevation="0" v-if="mobileMenuSelect === 'ตาราง Order คงค้างรายวัน' && MobileSize || !MobileSize">
      <!-- กล่องหก -->
      <v-row v-if="MobileSize">
        <v-col cols="12" class="pb-0 pt-2 mt-1 mb-2">
          <v-card style="background: linear-gradient(0deg, #EBF4FF, #FFFFFF); font-size: 14px; font-weight: bold; height: 60px; border-radius: 0px; line-height: 15px;" class="pl-1">
            <v-row no-gutters class="pt-2 pl-2">
              <v-col class="d-flex justify-start">
                <span>Status</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center pt-1">
                <div style="background-color: #FFF1F2; height: 12px;" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">On Process</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusCloseItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col class="d-flex justify-center align-center">
                <div style="background-color: #E6FFF7; height: 12px" class="px-1 d-flex align-center">
                  <span style="font-size: 10px;">Close</span>
                </div>
                <v-spacer></v-spacer>
                <span style="font-size: 12px; font-weight: bold;" class="pr-1">{{statusOnProcessItemCount}}</span>
                <span class="pr-1">วัน</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-card>
            <v-data-table
              :headers="headers"
              :items="DataTable"
              :search="StatusFilter.value"
              style="white-space: nowrap;"
              height="500"
              fixed-header
              :items-per-page="-1"
              hide-default-footer
            >
              <template v-slot:[`item.order_date`]="{ item }">
                <span>{{new Date(item.order_date).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'  })}}</span>
              </template>
              <template v-slot:[`item.max_received_days`]="{ item }">
                <v-row>
                  <v-col cols="6">
                    <span>{{item.in_progress_order === 0 ? item.max_received_days : item.max_in_progress_days}}</span>
                  </v-col>
                  <v-col cols="6">
                    <v-icon :color="item.date_color === 'green' ? '#06D6A0' : item.date_color === 'red' ? '#D56062' : '#FFD166' ">mdi-circle-medium</v-icon>
                  </v-col>
                </v-row>
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <v-chip :color="item.status === 'On Process' ? '#FFF1F2' : '#E6FFF7' ">
                  <span>{{item.status}}</span>
                </v-chip>
                <span></span>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      menu: false,
      selectedYear: new Date().getFullYear(),
      selectedYearMonth: new Date().getFullYear(),
      selectedMonth: String(new Date().getMonth() + 1).padStart(2, '0'),
      selectedDays: [],
      selectedFilterType: 'yearly',
      displayDateFilterText: '',
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      headers: [
        { text: 'วันที่สั่ง Order', value: 'order_date', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'จำนวน Order รายวัน', value: 'total_order', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'Order Success', value: 'success_order', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'Order Cancel', value: 'cancel_order', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'Order Return Success', value: 'return_order', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'Order On Process', value: 'in_progress_order', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'Order Lost', value: 'lost_order', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'สินค้ารอขนส่ง (วัน)', value: 'max_received_days', width: '50', align: 'center', filterable: false, sortable: true, class: 'table-blue-header fontTable--text' },
        { text: 'Status', value: 'status', width: '50', align: 'center', filterable: true, sortable: true, class: 'table-blue-header fontTable--text' }
      ],
      DataTable: [],
      StatusFilterItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'On Process', value: 'On Process' },
        { text: 'Close', value: 'Close' }
      ],
      StatusFilter: { text: 'ทั้งหมด', value: '' },
      DateFilterData: {
        start_date: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end_date: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
        // start_date: '',
        // end_date: ''
      },
      StatusOrder: [],
      normalItem: '',
      normalRate: '',
      monitorItem: '',
      monitorRate: '',
      alarmItem: '',
      alarmRate: '',
      onProgressOrder: '',
      overallOrder: '',
      cancelGroupItem: '',
      cancelGroupRate: '',
      onProcessGroupItem: '',
      onProcessGroupRate: '',
      returnGroupItem: '',
      returnGroupRate: '',
      successGroupItem: '',
      successGroupRate: '',
      statusCloseItemCount: 0,
      statusOnProcessItemCount: 0,
      years: '',
      // selectedYear: '',
      // selectedYearMonth: '',
      // mobileMenuSelect: false,
      mobileMenu: [
        'ภาพรวม Order คงค้าง',
        'ตาราง Order คงค้างรายวัน'
      ],
      mobileMenuSelect: 'ภาพรวม Order คงค้าง',
      dateNow: new Date().toLocaleDateString('th-TH', { day: '2-digit', month: '2-digit', year: 'numeric' }),
      totalDays: 0,
      targetDate: new Date('2024-08-22'),
      currentDate: new Date(),
      exportExcelBody: {},
      lostGroupRate: '',
      lostGroupItem: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dateFilterText () {
      const isLeapYear = (year) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
      const monthsWith30Days = ['04', '06', '09', '11']
      const monthsWith31Days = ['01', '03', '05', '07', '08', '10', '12']
      // Get current date details
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = String(today.getMonth() + 1).padStart(2, '0') // month is zero-based
      const currentDay = String(today.getDate()).padStart(2, '0')
      if (this.selectedFilterType === 'yearly') {
        const isCurrentYear = parseInt(this.selectedYear) === currentYear
        const endDate = isCurrentYear
          ? `${currentYear}-${currentMonth}-${currentDay}`
          : `${this.selectedYear}-12-31`
        return `${this.selectedYear}-01-01 - ${endDate}`
      } else if (this.selectedFilterType === 'monthly') {
        if (this.selectedMonth !== null) {
          const isCurrentMonthAndYear =
            (parseInt(this.selectedYearMonth) === currentYear && this.selectedMonth === currentMonth)
          let endDate
          if (monthsWith31Days.includes(this.selectedMonth)) {
            endDate = isCurrentMonthAndYear ? currentDay : '31'
          } else if (this.selectedMonth === '02') {
            endDate = isLeapYear(parseInt(this.selectedYearMonth)) ? '29' : '28'
            if (isCurrentMonthAndYear) endDate = currentDay
          } else if (monthsWith30Days.includes(this.selectedMonth)) {
            endDate = isCurrentMonthAndYear ? currentDay : '30'
          }
          return `${this.selectedYearMonth}-${this.selectedMonth}-01 - ${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
        } else {
          return ''
        }
      } else if (this.selectedDays.length > 1) {
        if (this.selectedDays[0] > this.selectedDays[1]) {
          return `${this.selectedDays[1]} - ${this.selectedDays[0]}`
        } else {
          return `${this.selectedDays[0]} - ${this.selectedDays[1]}`
        }
      } else if (this.selectedDays.length === 1) {
        return `${this.selectedDays[0]} - ${this.selectedDays[0]}`
      } else {
        return this.selectedDays[0] || ''
      }
    },
    daysDifference () {
      const timeDifference = this.currentDate - this.targetDate
      return Math.ceil(timeDifference / (1000 * 60 * 60 * 24))
    }
  },
  async created () {
    await this.generateYears()
    await this.getOverViewOnProcess('active')
    await this.getOverViewOnProcessTable('active')
  },
  mounted () {
    this.selectedFilterType = 'yearly'
    this.displayDateFilterText = this.dateFilterText
    this.intervalId = setInterval(async () => {
      await this.getOverViewOnProcess()
      await this.getOverViewOnProcessTable()
    }, 10000)
  },
  beforeDestroy () {
    clearInterval(this.intervalId)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/pendingOrderInfoMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/pendingOrderInfo' }).catch(() => {})
      }
    },
    selectedFilterType (val) {
      this.selectedYear = new Date().getFullYear()
      if (val === 'daily') {
        this.formatCurrentDate()
      } else if (val === 'monthly') {
        this.selectedMonth = String(new Date().getMonth() + 1).padStart(2, '0')
      }
    }
  },
  methods: {
    handleMenuChange () {
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    handleDateChange (dates) {
      this.selectedDays = dates
    },
    async getOverViewOnProcess (active) {
      if (active) {
        this.$store.commit('openLoader')
      }
      await this.$store.dispatch('actionsgetOverViewOnProcess', this.DateFilterData)
      var response = await this.$store.state.ModuleDashboardTransport.stateOverViewOnProcess
      this.normalItem = response.data.on_process_group_stage[0].normal.items
      this.normalRate = response.data.on_process_group_stage[0].normal.rate
      this.monitorItem = response.data.on_process_group_stage[0].monitor.items
      this.monitorRate = response.data.on_process_group_stage[0].monitor.rate
      this.alarmItem = response.data.on_process_group_stage[0].alarm.items
      this.alarmRate = response.data.on_process_group_stage[0].alarm.rate
      this.onProgressOrder = response.data.on_progress_order
      this.overallOrder = response.data.overall_order
      this.cancelGroupItem = response.data.summary_group_status[0].cancel.items
      this.cancelGroupRate = response.data.summary_group_status[0].cancel.rate
      this.onProcessGroupItem = response.data.summary_group_status[0].on_process.items
      this.onProcessGroupRate = response.data.summary_group_status[0].on_process.rate
      this.returnGroupItem = response.data.summary_group_status[0].return.items
      this.returnGroupRate = response.data.summary_group_status[0].return.rate
      this.successGroupItem = response.data.summary_group_status[0].success.items
      this.successGroupRate = response.data.summary_group_status[0].success.rate
      this.lostGroupItem = response.data.summary_group_status[0].lost.items
      this.lostGroupRate = response.data.summary_group_status[0].lost.rate
      if (active) {
        this.$store.commit('closeLoader')
      }
    },
    async getOverViewOnProcessTable (active) {
      if (active) {
        this.$store.commit('openLoader')
      }
      await this.$store.dispatch('actionsgetOverViewOnProcessTable', this.DateFilterData)
      var response = await this.$store.state.ModuleDashboardTransport.stateOverViewOnProcessTable
      this.DataTable = response.data[0].summary_order_table
      this.statusCloseItemCount = response.data[0].status.close
      this.statusOnProcessItemCount = response.data[0].status.on_process
      if (active) {
        this.$store.commit('closeLoader')
      }
    },
    dateFormat (inputDate, format) {
      const date = new Date(inputDate)
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      format = format.replace('MM', month.toString().padStart(2, '0'))
      if (format.indexOf('yyyy') > -1) {
        format = format.replace('yyyy', year.toString())
      } else if (format.indexOf('yy') > -1) {
        format = format.replace('yy', year.toString().substr(2, 2))
      }
      format = format.replace('dd', day.toString().padStart(2, '0'))
      return format
    },
    async applyDateFilter () {
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = String(today.getMonth() + 1).padStart(2, '0')
      const currentDay = String(today.getDate()).padStart(2, '0')

      if (this.selectedFilterType === 'yearly') {
        const isCurrentYear = parseInt(this.selectedYear) === currentYear
        this.DateFilterData = {
          start_date: `${this.selectedYear}-01-01`,
          end_date: isCurrentYear ? `${currentYear}-${currentMonth}-${currentDay}` : `${this.selectedYear}-12-31`
        }
        this.menu = false
        await this.getSummaryOrder('active')
      } else if (this.selectedFilterType === 'monthly') {
        const isLeapYear = (year) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
        const monthsWith31Days = ['01', '03', '05', '07', '08', '10', '12']
        const monthsWith30Days = ['04', '06', '09', '11']
        if (this.selectedMonth !== null) {
          const isCurrentMonthAndYear = (parseInt(this.selectedYearMonth) === currentYear && this.selectedMonth === currentMonth)
          let endDate
          if (this.selectedMonth === '02') {
            endDate = isLeapYear(parseInt(this.selectedYearMonth)) ? '29' : '28'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.DateFilterData = {
              start_date: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end_date: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          } else if (monthsWith30Days.includes(this.selectedMonth)) {
            endDate = '30'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.DateFilterData = {
              start_date: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end_date: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          } else if (monthsWith31Days.includes(this.selectedMonth)) {
            endDate = '31'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.DateFilterData = {
              start_date: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end_date: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          }
          this.menu = false
          await this.getSummaryOrder('active')
        } else {
          await this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกเดือน'
          })
          this.menu = true
        }
      } else if (this.selectedFilterType === 'daily') {
        if (this.selectedDays.length !== 0) {
          if (this.selectedDays.length === 1) {
            this.DateFilterData = { start_date: this.selectedDays[0], end_date: this.selectedDays[0] }
          } else if (this.selectedDays.length > 1) {
            if (this.selectedDays[0] > this.selectedDays[1]) {
              this.DateFilterData = {
                start_date: this.selectedDays[1],
                end_date: this.selectedDays[0]
              }
            } else {
              this.DateFilterData = {
                start_date: this.selectedDays[0],
                end_date: this.selectedDays[1]
              }
            }
          }
          this.menu = false
          await this.getSummaryOrder('active')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกวัน'
          })
          this.menu = true
        }
      }
      this.displayDateFilterText = this.dateFilterText
      if (this.selectedFilterType === 'yearly') {
        this.selectedMonth = null
        this.selectedDays = []
        this.selectedYearMonth = new Date().getFullYear()
      } else if (this.selectedFilterType === 'monthly') {
        this.selectedYear = new Date().getFullYear()
        this.selectedDays = []
      } else {
        this.selectedYear = new Date().getFullYear()
        this.selectedMonth = null
        this.selectedYearMonth = new Date().getFullYear()
      }
    },
    async getSummaryOrder (active) {
      const [start, end] = this.dateFilterText.includes(' - ')
        ? this.dateFilterText.split(' - ') : ['', this.dateFilterText]
      this.DateFilterData = { start_date: start, end_date: end }
      await this.getOverViewOnProcess(active)
      await this.getOverViewOnProcessTable(active)
    },
    async generateYears () {
      this.years = []
      const currentYear = new Date().getFullYear()
      const years = []
      for (let year = 2022; year <= currentYear; year++) {
        years.push(year)
      }
      this.years = years
    },
    async backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async formatCurrentDate () {
      var datenow = new Date()
      var year = datenow.getFullYear()
      var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
      var day = datenow.getDate().toString().padStart(2, '0')
      var formattedDate = `${year}-${month}-${day}`
      this.selectedDays = [formattedDate]
    },
    async getReportExcel (val) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.exportExcelBody = {
        status: val
      }
      this.$store.commit('openLoader')
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}iship/dashboard/export_status_order`,
        data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        // console.log(response, 'response')
        const today = new Date()
        const year = today.getFullYear()
        const month = String(today.getMonth() + 1).padStart(2, '0')
        const date = String(today.getDate()).padStart(2, '0')
        const formattedDate = `${month}_${date}_${year}`
        if (val === 'normal') {
          fileLink.setAttribute('download', `report_order_status_normal_${formattedDate}.xlsx`)
        } else if (val === 'monitor') {
          fileLink.setAttribute('download', `report_order_status_monitor_${formattedDate}.xlsx`)
        } else if (val === 'alarm') {
          fileLink.setAttribute('download', `report_order_status_alarm_${formattedDate}.xlsx`)
        }
        document.body.appendChild(fileLink)
        fileLink.click()
      })
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style>
.table-blue-header {
  background-color: #D8E9FF
}
</style>

<style scoped>
::v-deep th.table-blue-header {
  background-color: #d8e9ff !important;
  color: black !important;
}

.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
</style>
