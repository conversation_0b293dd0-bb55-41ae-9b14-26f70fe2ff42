import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async GraczSellerShopPage (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/g/get_seller_shop_detail_mp`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GraczEditSellerShopPage (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/g/edit_seller_shop_mp`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
