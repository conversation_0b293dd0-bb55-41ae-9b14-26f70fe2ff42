<template>
  <v-container>
    <div v-if="MobileSize" style="margin-top: -4vw;">
      <v-select
        v-model="mobileMenuSelect"
        :items="mobileMenu"
        label="เลือกเมนู"
        dense
        outlined
        @change="handleMenuChange"
      ></v-select>
    </div>
    <div v-if="MobileSize">
      <div v-if="mobileMenuSelect === 'กราฟแสดงรายได้'">
        <v-row class="mb-2">
          <v-col class="d-flex ma-3 justify-center align-center" style="background-color: #f8fbff; border-radius: 1vw; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;">
            <v-avatar class="mr-5" :size="MobileSize ? 50 : IpadSize ? 40 : 150" tile width="80"><img src="@/assets/ImageINET-Marketplace/ICONDashboard/totalIncome.png" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;"></v-avatar>
            <div class="d-flex flex-column align-center">
              <span style="font-size: large;">รายได้ทั้งหมด</span>
              <span style="font-size: small; color: #636363;">(หลังหักค่าธรรมเนียม)</span>
              <span style="font-size: xx-large; color: #27AB9C; font-weight: bold;">{{formattedTotalIncome}}</span>
              <span style="color: #636363; font-size: medium;">บาท</span>
            </div>
          </v-col>
        </v-row>
        <v-card>
          <v-card-title primary-title>
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="20">
                  <v-img contain :src="statisticsIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">กราฟแสดงรายได้</span>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text>
            <apexchart  height="400" type="line" :options="dataTransfer.chartOptions" :series="dataTransfer.series"></apexchart>
          </v-card-text>
        </v-card>
      </div>
      <div v-if="mobileMenuSelect === 'ประวัติการได้รับเงิน'">
        <v-row>
          <v-col cols="12">
            <v-avatar rounded size="20">
              <v-img contain src="@/assets/ImageINET-Marketplace/ICONDashboard/record.png"></v-img>
            </v-avatar>
            <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400; color: #27AB9C; font-weight: bold;">ประวัติการได้รับ ({{listTransfer.length}} รายการ)</span>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :items="listTransfer"
            :search="search"
            style="width:100%; text-align: center;"
            height="100%"
            :page.sync="page"
            @pagination="countRequest"
            no-results-text="ไม่พบข้อมูลรายการโอนเงิน"
            no-data-text="ไม่พบข้อมูลรายการโอนเงิน"
            :update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.paid_datetime`]="{ item }">
                {{ formatDate(item.paid_datetime) }}
              </template>
              <template v-slot:[`item.bank_no`]="{ item }">
                {{  item.bank_no.slice(0, -3) + 'xxx' }}
              </template>
              <template v-slot:[`item.bank_name`]="{ item }">
                <span v-if="MobileSize">{{  substring(item.bank_name) }}</span>
                <span v-else>{{  item.bank_name }}</span>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </div>
    <div v-else>
      <v-card>
        <v-card-title primary-title>
          <v-row>
            <v-col cols="12">
              <v-avatar rounded size="20">
                <v-img contain :src="statisticsIconPath"></v-img>
              </v-avatar>
              <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">กราฟแสดงรายได้</span>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card-text>
          <apexchart  height="400" type="line" :options="dataTransfer.chartOptions" :series="dataTransfer.series"></apexchart>
        </v-card-text>
      </v-card>
      <v-row>
        <v-col class="d-flex ma-3 mt-8 justify-center align-center" style="background-color: #f8fbff; border-radius: 1vw; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;">
          <v-avatar class="mr-5" :size="MobileSize ? 50 : IpadSize ? 40 : 150" tile width="80"><img src="@/assets/ImageINET-Marketplace/ICONDashboard/totalIncome.png" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;"></v-avatar>
          <div class="d-flex flex-column align-center">
            <span style="font-size: large;">รายได้ทั้งหมด</span>
            <span style="font-size: small; color: #636363;">(หลังหักค่าธรรมเนียม)</span>
            <span style="font-size: xx-large; color: #27AB9C; font-weight: bold;">{{formattedTotalIncome}}</span>
            <span style="color: #636363; font-size: medium;">บาท</span>
          </div>
        </v-col>
        <v-col cols="12">
          <v-avatar rounded size="20">
            <v-img contain src="@/assets/ImageINET-Marketplace/ICONDashboard/record.png"></v-img>
          </v-avatar>
          <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400; color: #27AB9C; font-weight: bold;">ประวัติการได้รับ ({{listTransfer.length}} รายการ)</span>
        </v-col>
      </v-row>
      <v-row dense>
        <v-col cols="12">
          <v-data-table
          :headers="headers"
          :items="listTransfer"
          :search="search"
          style="width:100%; text-align: center;"
          height="100%"
          :page.sync="page"
          @pagination="countRequest"
          no-results-text="ไม่พบข้อมูลรายการโอนเงิน"
          no-data-text="ไม่พบข้อมูลรายการโอนเงิน"
          :update:items-per-page="itemsPerPage"
          :items-per-page="10"
          class="elevation-1 mt-4"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          >
            <template v-slot:[`item.paid_datetime`]="{ item }">
              {{ formatDate(item.paid_datetime) }}
            </template>
            <template v-slot:[`item.bank_no`]="{ item }">
              {{  item.bank_no.slice(0, -3) + 'xxx' }}
            </template>
            <template v-slot:[`item.bank_name`]="{ item }">
              <span v-if="MobileSize">{{  substring(item.bank_name) }}</span>
              <span v-else>{{  item.bank_name }}</span>
            </template>
            <template v-slot:[`item.total_price_vat`]="{ item }">
              <span>{{ String(item.total_price_vat).replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
export default {
  components: {
    apexchart: VueApexCharts
  },
  props: {
    payloadTransfer: {
      type: Object
    }
  },
  data () {
    return {
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      listPrice: [],
      listTransfer: [],
      search: '',
      total_income: '',
      total_record: '',
      itemsPerPage: 10,
      page: 1,
      listTotal_price: [],
      listDataGraph: [],
      headers: [
        { text: 'รหัสการสั่งซื้อ', value: 'purchase_order_number', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่พาร์ทเนอร์ได้รับเงิน', value: 'paid_datetime', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ธนาคารที่รับเงิน', value: 'bank_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขบัญชีที่รับเงิน', value: 'bank_no', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ยอดเงินที่ได้รับ', value: 'total_price_vat', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      mobileMenu: [
        'กราฟแสดงรายได้',
        'ประวัติการได้รับเงิน'
      ],
      mobileMenuSelect: 'กราฟแสดงรายได้'
    }
  },
  created () {
    this.getTransferPartner()
  },
  computed: {
    dataTransfer () {
      return {
        series: [],
        chartOptions: {
          chart: {
            id: 'income-difference-chart',
            // stacked: false,
            toolbar: {
              show: false // Set toolbar to false to hide it
            },
            colors: ['#008FFB', '#FF4560']
          },
          markers: {
            size: 5, // Adjust the size of markers as per your preference
            colors: '#ab93f9',
            strokeColors: '#AE8FF7',
            strokeWidth: 2,
            strokeOpacity: 0.9,
            strokeDashArray: 0,
            fillOpacity: 0,
            discrete: [],
            shape: 'circle',
            radius: 2,
            offsetX: 0,
            offsetY: 0,
            onDblClick: undefined,
            showNullDataPoints: true,
            hover: {
              size: undefined,
              sizeOffset: 6
            }
          },
          xaxis: {
            categories: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
          },
          yaxis: {
            labels: {
              formatter: function (value) {
                // Format the number with commas for thousands
                return new Intl.NumberFormat('en-US').format(value)
              }
            }
          },
          plotOptions: {
            bar: {
              horizontal: false
            }
          },
          dataLabels: {
            enabled: false
          },
          colors: ['#008FFB', '#FF4560'],
          tooltip: {
            custom: function ({ series, seriesIndex, dataPointIndex, w }) {
              const months = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
              const value = series[seriesIndex][dataPointIndex]
              var img = require('@/assets/ImageINET-Marketplace/ICONDashboard/icomTransferGraph.png')
              return `<div style="
                  background: white; 
                  padding: 10px; 
                  border-radius: 8px;
                  box-shadow: 0px 2px 10px rgba(0,0,0,0.1);
                  text-align: left;
                ">
                  <div style="display: flex; align-items: center; gap: 1vw;">
                    <img height="24" width="24" src="${img}"/>
                    <strong>${months[dataPointIndex]}</strong>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 5px;">
                    <span style="color: #AE8FF7; font-size: 16px;">🟣</span>
                    <span style="margin-left: 5px;">${value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')} บาท</span>
                  </div>
                </div>`
            }
          }
          // tooltip: {
          //   y: {
          //     formatter: function (val) {
          //       return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
          //     }
          //   }
          // }
        }
      }
    },
    formattedTotalIncome () {
      const value = this.total_income
      if (value >= 10000000) {
        return (value / 1000000).toFixed(1) + 'M'
      } else {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    payloadTransfer: {
      async handler () {
        await this.getTransferPartner()
      },
      deep: true
    }
  },
  methods: {
    handleMenuChange () {
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    substring (bankName) {
      return bankName.length > 15 ? bankName.substring(0, 15) + '...' : bankName
    },
    formatDate (dateString) {
      // console.log(dateString)
      if (dateString === '-') return '-'
      const date = new Date(dateString)
      const options = { day: '2-digit', month: '2-digit', year: 'numeric' }
      const formattedDate = date.toLocaleDateString('th-TH', options)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${formattedDate} ${hours}.${minutes} น.`
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getTransferPartner () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetTransfer', this.payloadTransfer)
      var res = await this.$store.state.ModuleBusiness.stateGetTransfer
      if (res.code === 200) {
        this.listTransfer = res.data
        this.total_income = res.total_price
        this.listTotal_price = res.summaryData
        this.listDataGraph = this.listTotal_price.map(item => item.total_price)
        this.dataTransfer.series = [
          {
            name: 'ยอดเงินรวม',
            data: this.listDataGraph
          }
        ]
        // console.log(this.listTotal_price)
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style>

</style>
