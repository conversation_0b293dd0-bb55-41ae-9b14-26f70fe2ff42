<template>
<div class="div_overflow">
  <v-row no-gutters justify="center">
<ModalImage />
    <v-col cols="9" >
      <v-form ref="addShop" v-model="checkFormAdd" :lazy-validation="lazy">
        <v-row class="ml-2 mr-2" no-gutters>
          <v-col cols="12" class="mt-5 mb-5">
            <v-card outlined>
              <v-row  no-gutters align="center">
                <v-col cols="12" class="mt-5 ml-10">
                  <h2><v-icon large class="mr-5">mdi-comment-processing-outline</v-icon>เเนะนำการขายของบน INET Market</h2>
                </v-col>
                <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                <v-col cols="12" md="10" offset="1" class="pl-3">
                  <span>ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span><br>
                  </v-col>
                <v-col cols="12" md="10" offset="1" class="pl-6 pb-5">
                  <span>1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span><br>
                  <span>2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <v-chip x-small class="mr-2 ml-2" @click="OpenModal" color="success" outlined><v-icon left small>mdi-hand-pointing-right</v-icon>เอกสารนี้</v-chip>มาทาง email <EMAIL> ของ <u>Thai Payment Gateway</u> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span><br>
                  <span>3. หลังจากที่ทาง Thai Payment Gateway ตรวจสอบเสร็จ จะส่ง email กลับมาให้ผ่าน email ที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี Thai Payment Gateway ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span><br>
                  <span>4. ระหว่างที่รอ คุณสามารถ upload สินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ (หน้านี้)</span><br>
                  <span>5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span><br>
                  <span>แนะนำเพิ่มเติม: คุณสามารถโหลด <u>One Chat</u> application ซึ่งคุณสามารถ add bot INET Market ซึ่งจะแจ้งเตือนคุณได้ทันที หากมีการสั่งซื้อเกิดขึ้น</span>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-card outlined>
              <v-row  no-gutters align="center">
                <v-col cols="12" class="mt-5 ml-10">
                  <h2><v-icon large class="mr-5">mdi-card-account-details</v-icon>ข้อมูลร้านค้าของฉัน</h2>
                </v-col>
                <v-col cols="12"><v-divider></v-divider></v-col>
                <!-- Image -->
                <v-col cols="12" md="12" class="py-5" @click="changePic()">
                  <v-row justify="center" align="center">
                    <!-- <v-avatar size="164" class='ml-6' style="cursor: pointer;">
                      <v-img :src="imageBase" v-if="imageBase !== ''"></v-img>
                      <v-img v-else src="@/assets/noprofile.png"></v-img>
                    </v-avatar> -->
                    <v-hover v-slot="{ hover }">
                      <div :elevation="hover ? 16 : 2" :class="{ 'on-hover': hover }" class="mx-auto" color="grey lighten-4">
                        <div style="position: relative;" >
                          <!-- <div class="mb-5 coppa_box"> -->
                          <v-avatar size="164" class='ml-6' style="cursor: pointer;">
                            <v-img :src="imageBase" v-if="imageBase !== ''"></v-img>
                            <v-img v-else src="@/assets/noprofile.png" class="pointPic"></v-img>
                          </v-avatar>
                          <span style="position: absolute; margin-top: 120px; right: 10px;" v-if="imageBase === ''"><v-icon x-large color="success">mdi-plus-circle</v-icon></span>
                          <!-- </div> -->
                          <!-- +-+++++++ -->
                          <!-- <v-expand-transition>
                            <div
                              v-if="true"
                              @click="changePic()"
                              class="d-flex transition-fast-in-fast-out base4 v-card--reveal title white--text"
                            >เลือกรูปภาพ ..</div>
                          </v-expand-transition> -->
                        </div>
                      </div>
                    </v-hover>
                  </v-row>
                  <!-- <v-text-field :label="$t('textshop.dialogPlaceholder.logoLabel')" @click='onPickFile()'  v-model='imageName' prepend-icon="mdi-image"></v-text-field> -->
                  <input
                    type="file"
                    style="display: none"
                    ref="image"
                    id="picTure"
                    accept="image/*"
                    @change="showPicture"
                  >
                </v-col>
                <!-- Shop Name Thai -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*ชื่อ(TH)</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="shopNameTH" placeholder="ชื่อ(TH)" dense :rules="Rules.shopNameTH"></v-text-field>
                </v-col>
                <!-- Shop Name Eng -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*ชื่อ(EN)</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="shopNameEN" placeholder="ชื่อ(TH)" dense :rules="Rules.shopNameEN"></v-text-field>
                </v-col>
                <!-- Business Code -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0" ><span class="f-right">*รหัสร้านค้า</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="businessCode" placeholder="รหัสร้านค้า" dense :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Website -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">เว็บไซต์</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="website" placeholder="เว็บไซต์" dense :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Tax ID -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right" style="font-size: 12px;">*รหัสประจำตัวผู้เสียภาษี</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="taxID" dense placeholder="รหัสประจำตัวผู้เสียภาษี" :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Bank Account -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">*เลขบัญชีธนาคาร</span></v-col>
                <v-col cols="12" md="9" class="pl-3 pb-0">
                  <v-text-field outlined v-model="bankAccount" dense placeholder="เลขบัญชีธนาคาร" :rules="Rules.empty"></v-text-field>
                </v-col>
                 <!-- Bank Name -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">*ชื่อธนาคาร</span></v-col>
                <v-col cols="12" md="9" class="pl-3 pb-0">
                <v-select :items="bankSelectName" item-text="name" item-value="value" label="ชื่อธนาคาร" dense v-model="bankName" outlined :rules="Rules.empty"></v-select>
                </v-col>
                <!-- Email -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">*อีเมล</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="email" dense placeholder="อีเมล" :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Phone -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">*เบอร์ติดต่อ</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="phoneOne" dense placeholder="เบอร์ติดต่อ" :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Fax -->
                <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">แฟกซ์</span></v-col>
                <v-col cols="12" md="9" class="pl-3 pb-0">
                  <v-text-field outlined v-model="fax" dense placeholder="แฟกซ์" :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Tax ID -->
                <!-- <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right" style="font-size: 12px;">*รหัสประจำตัวผู้เสียภาษี</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="taxID" dense placeholder="รหัสประจำตัวผู้เสียภาษี"></v-text-field>
                </v-col> -->
                <!-- Bank Account -->
                <!-- <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">เลขบัญชีธนาคาร</span></v-col>
                <v-col cols="12" md="9" class="pl-3 pb-0">
                  <v-text-field outlined v-model="bankAccount" dense placeholder="เลขบัญชีธนาคาร"></v-text-field>
                </v-col> -->
                <!-- Merchant Key -->
                <!-- <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">*{{ $t('textshop.dialogSeller.merchantKey') }}</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="merchantKey" dense :placeholder="$t('textshop.dialogPlaceholder.merchantKeyLabel')" :rules="merchantKeyRules"></v-text-field>
                </v-col> -->
                <!-- SSH Key -->
                <!-- <v-col cols="12" md="2" class="pl-3 pb-5 pt-0"><span class="f-right">{{ $t('textshop.dialogSeller.sshKey') }}</span></v-col>
                <v-col cols="12" md="4" class="pl-3 pb-0">
                  <v-text-field outlined v-model="sshKey" dense :placeholder="$t('textshop.dialogPlaceholder.sshKeyLabel')" :rules="sshKeyRules"></v-text-field>
                </v-col> -->
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="12" class="mt-5">
            <v-card outlined>
              <v-row no-gutters align="center">
                <v-col cols="11" class="mt-5 ml-10">
                  <h2><v-icon large class="mr-5">mdi-map-marker-radius</v-icon>ที่อยู่ร้านค้า</h2>
                </v-col>
                <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                <!-- House Number -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*บ้านเลขที่</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="houseNo" placeholder="บ้านเลขที่" dense :rules="Rules.empty"></v-text-field>
                </v-col>
                <!-- Address Detail -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*รายละเอียดที่อยู่</span></v-col>
                <v-col cols="12" md="9" class="pl-3">
                  <v-text-field outlined v-model="addressDetail" placeholder="รายละเอียดที่อยู่" dense :rules="Rules.empty"></v-text-field>
                  <!-- <v-textarea no-resize rows="1" auto-grow row-height="15" outlined v-model="addressDetail" :placeholder="$t('textshop.dialogPlaceholder.addressDetail')" dense :rules="detailRules"></v-textarea> -->
                </v-col>
                <!-- Sub District -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*ตำบล</span></v-col>
                <v-col cols="12" md="9" class="pl-3  mb-4">
                  <addressinput-subdistrict label="" placeholder="ตำบล" v-model="subdistrict"  :rules="Rules.empty"/>
                </v-col>
                <!-- District -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*อำเภอ</span></v-col>
                <v-col cols="12" md="9" class="pl-3  mb-4">
                  <addressinput-district label="" v-model="district"  placeholder="อำเภอ" :rules="Rules.empty"/>
                </v-col>
                <!-- Province -->
                <v-col cols="12" md="2" class="pl-3 pb-5"><span class="f-right">*จังหวัด</span></v-col>
                <v-col cols="12" md="9" class="pl-3  mb-4">
                  <addressinput-province label="" v-model="province" placeholder="จังหวัด" :rules="Rules.empty"/>
                </v-col>
                <!-- Zip Code -->
                <v-col cols="12" md="2" class="pl-5 pb-6"><span class="f-right">*รหัสไปรษณีย์</span></v-col>
                <v-col cols="12" md="9" class="pl-3 mb-4">
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="รหัสไปรษณีย์" :rules="Rules.empty"/>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
        <v-row no-gutters justify="center" align="center" class="mt-5">
          <!-- <v-btn color="black" outlined class="mr-4" @click="Cancel()">ยกเลิก</v-btn> -->
          <v-btn color="success"  @click="Confirm()">ยืนยัน</v-btn>
        </v-row>
      </v-form>
    </v-col>
    <!-- create vue tour -->
  </v-row>
</div>
      <!-- <v-snackbar
      v-model="successSnackbar"
      :multi-line="multiLine"
      color="success"
      top
      :timeout="timeout"
      >
        {{ textSucess }}
        <template v-slot:action="{ attrs }">
          <v-btn text v-bind="attrs" @click="successSnackbar = false">
            Close
          </v-btn>
        </template>
      </v-snackbar>
      <v-snackbar
      v-model="errorSnackbar"
      :multi-line="multiLine"
      color="error"
      top
      :timeout="timeout"
      >
        {{ textError }}
        <template v-slot:action="{ attrs }">
          <v-btn text v-bind="attrs" @click="errorSnackbar = false">
            Close
          </v-btn>
        </template>
    </v-snackbar> -->
</template>

<script>
// import { Decode } from '@/services'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import ModalImage from '@/components/Modal/Image'
Vue.use(VueThailandAddress)
export default {
  components: {
    ModalImage
  },
  data () {
    return {
      checkshop: 'ไม่มีร้าน',
      checkFormAdd: true,
      onedata: [],
      accesstoken: '',
      lazy: false,
      showImage: '',
      DataImage: [],
      imageName: '',
      shopNameTH: '',
      shopNameEN: '',
      businessCode: '',
      email: '',
      website: '',
      houseNo: '',
      addressDetail: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      phoneOne: '',
      phoneTwo: '',
      fax: '',
      taxID: '',
      bankAccount: '',
      bankName: '',
      bankSelectName: [{ value: 'KTB', name: 'ธนาคารกรุงไทย' },
        { value: 'BBL', name: 'ธนาคารกรุงเทพ' },
        { value: 'TMB', name: 'ทหารไทย' },
        { value: 'SCB', name: 'ไทยพานิชย์' },
        { value: 'BAY', name: 'ธนาคารกรุงศรีอยุธยา' },
        { value: 'UOBT', name: 'ธนาคารยูโอบี' },
        { value: 'GSB', name: 'ธนาคารออมสิน' },
        { value: 'BAAC', name: 'ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร' }],
      merchantKey: '',
      sshKey: '',
      zipCodeData: [],
      districtData: [],
      subDistrictData: [],
      provinceData: [],
      Data: require('thai-data'),
      imageBase: '',
      seller_shop_id: '',
      Rules: {
        required: [{ required: true, message: 'กรุณากรอกข้อมูล' }],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        shopNameTH: [
          v => !!v || 'กรุณากรอกชื่อร้าน'
        ],
        shopNameEN: [
          v => !!v || 'กรุณากรอกชื่อร้านภาษาอังกฤษ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ]
      }
    }
  },
  created () {
    // console.log('เข้า create shop')
    this.CheckShop()
    // if (localStorage.hasOwnProperty('oneData')) {
    //   this.onedata = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('oneData'))))
    //   this.accesstoken = this.onedata.access_token
    // }
    // const GetallData = this.Data.allData().map((item) => {
    //   return item.zipCode;
    // });
    // this.zipCodeData = GetallData;
  },
  computed: {
    ModalAddSellerShop () {
      return this.$store.state.Seller.stateModalAddSellerShop
    }
  },
  watch: {

  },
  methods: {
    OpenModal () {
      this.$EventBus.$emit('opencomment')
    },
    async CheckShop () {
      this.$EventBus.$emit('changeNav')
      await this.$store.dispatch('actionsGetShopData')
      var response = await this.$store.state.ModuleShop.stateShopData
      // console.log('response shop data====> หน้า shop', response.data)
      if (response.data.length !== 0) {
        this.checkshop = 'มีร้านค้า'
      } else {
        this.checkshop = 'ไม่มีร้านค้า'
      }
      this.SetdataShop(response.data[0])
    },
    SetdataShop (val) {
      this.seller_shop_id = val.seller_shop_id
      // console.log('val =====รับค่ามาจากหลังบ้าน SetdataShop', val)
      if (this.checkshop === 'มีร้านค้า') {
        this.shopNameTH = val.name_th
        this.shopNameEN = val.name_en
        this.businessCode = val.business_code
        this.website = val.url_name
        this.DataImage = []
        this.email = val.seller_email[0].seller_email
        this.phoneOne = val.seller_phone[0].phone
        this.phoneTwo = ''
        this.fax = val.seller_fax[0].fax
        this.addressDetail = val.detail
        this.houseNo = val.house_no
        this.subdistrict = val.sub_district
        this.district = val.district
        this.province = val.province
        this.zipcode = val.zipcode
        this.imageName = ''
        this.imageBase = val.path_logo !== '' ? `${process.env.VUE_APP_IMAGE}${val.path_logo}` : ''
        this.taxID = val.seller_tax_id
        this.bankAccount = val.seller_account_bank
        this.bankName = val.seller_name_bank
      }
    },
    changePic () {
      document.getElementById('picTure').click()
    },
    async Cancel () {
      this.shopNameTH = ''
      this.shopNameEN = ''
      this.businessCode = ''
      this.website = ''
      this.DataImage = []
      this.email = ''
      this.phoneOne = ''
      this.phoneTwo = ''
      this.fax = ''
      this.addressDetail = ''
      this.houseNo = ''
      this.subdistrict = ''
      this.district = ''
      this.province = ''
      this.zipcode = ''
      this.imageName = ''
      this.imageBase = ''
      this.taxID = ''
      this.bankAccount = ''
      this.resetValidation()
      this.$router.push('MyShop')
    },
    onPickFile () {
      this.$refs.image.click()
    },
    showPicture (e) {
      const files = e.target.files
      if (files[0] !== undefined) {
        this.imageName = files[0].name
        const element = files[0]
        const reader = new FileReader()
        reader.readAsDataURL(element)
        reader.onload = () => {
          this.imageBase = reader.result
          this.showImage = URL.createObjectURL(element)
        }
      }
    },
    async Confirm () {
      if (this.subdistrict === '' || this.district === '' || this.province === '' || this.zipcode === '') {
        this.$swal.fire({ text: 'กรุณากรอกที่อยู่ให้ครบถ้วน', icon: 'error', timer: 2500, showConfirmButton: false })
      } else if (this.imageBase === '') {
        this.$swal.fire({ text: 'กรุณาใส่รูปภาพ', icon: 'error', timer: 2500, showConfirmButton: false })
      } else if (this.$refs.addShop.validate(true)) {
        var dataShop = {
          seller_shop_id: this.seller_shop_id,
          seller_name_th: this.shopNameTH,
          seller_name_en: this.shopNameEN,
          business_code: this.businessCode,
          url_name: this.website,
          tax_id: this.taxID,
          account_bank: this.bankAccount,
          name_bank: this.bankName,
          ssh_key: '',
          merchant_key: '',
          path_logo: this.imageBase,
          seller_email: [{
            email: this.email
          }],
          seller_phone: [
            {
              phone: this.phoneOne
            }
          ],
          seller_fax: [{
            fax: this.fax
          }],
          seller_address_detail: this.addressDetail,
          seller_house_no: this.houseNo,
          seller_sub_district: this.subdistrict,
          seller_district: this.district,
          seller_province: this.province,
          seller_zip_code: this.zipcode
        }
        // console.log('datadhop', dataShop)
        var formdata = {
          // type: 'Bearer',
          // access_token: this.accesstoken,
          dataShop: dataShop
        }
        // console.log('FormData ก่อนส่งให้หลังบ้าน', formdata)
        await this.$store.dispatch('UpdateSellerShopDetail', formdata)
        // console.log('formdataaaa', formdata)
        var response = await this.$store.state.ModuleManageShop.UpdateSellerShopDetail
        if (response.result === 'SUCCESS') {
          this.$swal.fire({ text: `${response.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
          this.$router.push('/shop')
        } else {
          this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      }
    },
    resetValidation () {
      this.$refs.addShop.resetValidation()
    }
  }
}
</script>

<style scoped>
.f-right {
  float: right;
}
.div_overflow {
  overflow: auto;
  width:100%;
  height:89vh
}
.pointPic {
  opacity: 0.5;
}
.pointPic:hover {
  opacity: 1.0;
}
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}
</style>
