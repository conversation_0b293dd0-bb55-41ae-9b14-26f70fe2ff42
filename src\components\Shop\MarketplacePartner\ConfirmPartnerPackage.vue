<template>
  <div>
    <v-container>
      <v-row>
        <v-col :cols="(IpadSize || IpadProSize || MobileSize) ? 12 : 8">
        <!-- <v-col cols="auto"> -->
          <v-card class="pa-4">
            <span :style="MobileSize ? 'font-size: 18px;' : 'font-size: 22px;'">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>
              <b>รายการสั่งซื้อสินค้า</b>
            </span>
            <div class="pt-5">
              <img
                src="@/assets/Marketplace_partner/shopCF.png"
                width="25px"
                class="mr-2"
              />
              <span style="color: #F4BC5F; font-size: 16px;"><b>ร้านค้า : {{ this.showDataServicePartner.partner_name }}</b></span>
              <v-divider class="my-4"></v-divider>
              <v-data-table
                :headers="headers"
                :items="selectPartnerPackage"
                no-data-text="ไม่พบรายการสั่งซื้อ Partner Package"
                class="elevation-1"
                hide-default-footer
              >
                <template v-slot:[`item.package_detail`]="{ item }">
                  <div class="scrollable-content pt-3" ref="termsContent" @scroll="handleScroll" v-html="item.package_detail"></div>
                </template>
                <template v-slot:[`item.payment_type`]="{ item }">
                  <span v-if="item.payment_type === 'Monthly'">รายเดือน</span>
                  <span v-else-if="item.payment_type === 'Yearly'">รายปี</span>
                  <span v-else-if="item.payment_type === 'Percent'">เปอร์เซ็น</span>
                  <span v-else>{{ item.payment_type }}</span>
                </template>
                <template v-slot:[`item.package_functions`]="{ item }">
                  <div ref="termsContent" @scroll="handleScroll">
                    <ul>
                      <!-- <li v-if="item.package_functions.length === 0">ไม่มีข้อมูล</li> -->
                      <li v-for="(func, index) in item.package_functions" :key="index">
                        {{ func.name }} : {{ func.limit === -1 ? 'ไม่จำกัด' : func.limit }}
                      </li>
                    </ul>
                  </div>
                </template>
                <template v-slot:[`item.package_price`]="{ item }">
                  <span>{{ Number(item.package_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </template>
              </v-data-table>
            </div>
            <div class="pt-5">
              <v-form ref="detailShop" :lazy-validation="lazy">
                <!-- <v-card class="pa-3 elevation-0" style="background-color: #E6F5F3;"> -->
                <v-card class="pa-3 elevation-0" style="background-color: #e6f5f327;">
                  <img
                    src="@/assets/Marketplace_partner/shoppingCF.png"
                    width="25px"
                    class="mr-2"
                  />
                  <span style="font-size: 16px;"><b>รายละเอียดรายการสั่งซื้อ</b></span>
                  <v-row class="pt-5">
                    <!-- <v-col cols="12" md="6">
                      <span>จำนวนเดือน <span style="color: red;">*</span></span>
                      <v-select
                        class="setCustomSelect custom-placeholder"
                        v-model="SelectAmountMonth"
                        :items="itemsSelectAmountMonth"
                        append-icon="mdi-chevron-down"
                        :menu-props="{ offsetY: true }"
                        item-text="text"
                        item-value="value"
                        placeholder="กรุณาเลือกจำนวนเดือน"
                        :rules="Rules.ItemAmountMouth"
                        dense
                        outlined
                        style="border-radius: 8px;"
                      />
                    </v-col> -->
                    <v-col cols="12" md="6">
                      <span>จำนวนเดือน <span style="color: red;">*</span></span>
                      <v-select
                        class="setCustomSelect custom-placeholder"
                        v-model="SelectAmountMonth"
                        :items="itemsSelectAmountMonth"
                        append-icon="mdi-chevron-down"
                        :menu-props="{ offsetY: true }"
                        item-text="text"
                        item-value="value"
                        placeholder="กรุณาเลือกจำนวนเดือน"
                        :rules="Rules.ItemAmountMouth"
                        dense
                        outlined
                        style="border-radius: 8px;"
                        :disabled="packagePaymentType === 'Yearly'"
                        v-bind:value="packagePaymentType === 'Yearly' ? 12 : SelectAmountMonth"
                      />
                    </v-col>
                    <v-col cols="12">
                      <v-row>
                        <v-col cols="12">
                          <span><b>ราคารวมทั้งหมด (เฉพาะ Package ตามวันที่สัญญา) :  {{ Number(total).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</b></span>
                        </v-col>
                        <v-col cols="12" md="6">
                          <span>วันที่เริ่มสัญญา <span style="color: red;">*</span></span>
                          <v-dialog
                            ref="menuStartDate"
                            v-model="menuStartDate"
                            persistent
                            width="290px"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-text-field
                                v-model="formattedStartDate"
                                :rules="Rules.ItemStartDate"
                                readonly
                                outlined
                                dense
                                class="custom-placeholder"
                                placeholder="กรุณาเลือกวันที่เริ่มสัญญา"
                                v-bind="attrs"
                                v-on="on"
                                append-icon="mdi-calendar-range"
                              ></v-text-field>
                            </template>
                            <v-date-picker v-model="tempStartDate" :min="minDate">
                              <v-spacer></v-spacer>
                              <v-btn text color="red" @click="CancelconfirmStartDate()">ยกเลิก</v-btn>
                              <v-btn text color="primary" @click="confirmStartDate()">ตกลง</v-btn>
                            </v-date-picker>
                          </v-dialog>
                        </v-col>
                        <v-col cols="12" md="6">
                          <span>วันที่สิ้นสุดสัญญา <span style="color: red;">*</span></span>
                          <v-text-field v-model="formattedEndDate" class="custom-placeholder" placeholder="กรุณาเลือกวันที่สิ้นสุดสัญญา" :rules="Rules.ItemEndDate" disabled readonly outlined dense append-icon="mdi-calendar-range"></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card>
              </v-form>
            </div>
          </v-card>
        </v-col>

        <v-col :cols="(IpadSize || IpadProSize || MobileSize) ? 12 : 4">
        <!-- <v-col cols="auto"> -->
          <v-card class="pa-4">
            <span :style="MobileSize ? 'font-size: 18px;' : 'font-size: 22px;'"><b>สรุปราคาสินค้าที่สั่งซื้อ</b></span>
            <div class="pt-5">
              <v-row>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6">
                  <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right">
                  <span><b>{{  Number(this.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</b></span>
                </v-col>
                <!-- <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6">
                  <span>คูปองส่วนลด</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right">
                  <v-chip
                    color="#27AB9C"
                    outlined
                    class="d-flex align-center justify-center flex-wrap text-center px-2"
                  >
                    <span class="px-2 text-truncate">คูปองส่วนลด</span>
                    <v-icon>mdi-ticket-percent-outline</v-icon>
                  </v-chip>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6">
                  <span>ส่วนลด</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right">
                  <span><b>0.00 บาท</b></span>
                </v-col> -->
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6">
                  <span>ภาษีมูลค่าเพิ่ม </span>
                  <span style="color: #A1A1A1;">(7%)</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right">
                  <span><b>{{  Number(this.Vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6">
                  <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right">
                  <span><b>{{  Number(this.VATincluded).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</b></span>
                </v-col>
                <v-col cols="12" class="pt-0 pb-0">
                  <v-divider class="my-4"></v-divider>
                </v-col>
                <v-col cols="12">
                  <span style="color: #FAAD14;">วิธีการชำระเงิน</span>
                  <v-radio-group v-model="selectedPaymentMethod" :row="true" style="margin-top: auto;">
                    <v-radio
                      v-for="(method, index) in showDataServicePartner.payment_method"
                      :key="index"
                      :label="getOriginalPaymentMethod(method)"
                      :value="method"
                    ></v-radio>
                  </v-radio-group>
                  <p v-if="!isPaymentValid" style="color: red;">กรุณาเลือกช่องทางการชำระเงิน</p>
                </v-col>
                <v-col cols="12" class="pt-0 pb-0">
                  <v-divider class="my-4"></v-divider>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6">
                  <span style="font-size: 20px;"><b>ราคารวมทั้งหมด</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right">
                  <span style="color: #27AB9C; font-size: 20px;"><b>{{  Number(this.totalPrice).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></span>
                  <span style="font-size: 20px;"><b> บาท</b></span>
                </v-col>
                <!-- <v-col cols="12" class="pt-0 pb-0">
                  <v-divider class="my-4"></v-divider>
                </v-col> -->
                <v-col cols="12">
                  <!-- <v-btn style="border-radius: 50px;" class="white--text" block color="#27AB9C" @click="PaymentPackage()"> -->
                  <v-btn style="border-radius: 50px;" class="white--text" block color="#27AB9C" @click="confirmPackage()">
                    <span style="font-size: 16px; font-style: general;"><b>ชำระเงิน</b></span>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <v-dialog v-model="dialogConfirm" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text>
          <div class="text-center mb-4">
            <span><b>ยืนยันคำสั่งซื้อ</b></span><br>
            <span>
              คุณต้องการทำรายการนี้ ใช่หรือไม่
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogConfirm = false">ยกเลิก</v-btn>
          <v-btn rounded dark color="#27AB9C" class="ma-2" style="width: 100px;" @click="PaymentQRCode()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="DialogQR" :width="MobileSize ? '100%' : '640'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
        <v-card-text class="px-0">
          <div  :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
            style="position: absolute; height: 120px; ">
            <v-row style="height: 120px; ">
              <v-col style="text-align: center;" class="pt-6">
                <span style="margin-left: 0px"
                :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{ QRShop === true ? 'สแกน QR Code ชำระเงินร้านค้า' : 'สแกน QR Code ชำระเงิน' }}</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '640px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
              style="background: #FFFFFF; border-radius: 20px;">
              <div style="text-align: center;">
                <v-col class="py-0">
                  <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${imageBase64}`" v-if="ImageQR !== ''"/>
                </v-col>
                <v-col class="py-0">
                  <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                </v-col>
                <div>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 20px; font-weight: 700;'">ยอดชำระเงินจำนวน : {{ netPrice ?
                      Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}
                      บาท</span>
                  </v-col>
                  <v-col>
                    <span :style="MobileSize ? 'font-size: 13px; font-weight: 400;' : 'font-size: 14px; font-weight: 400;'">รหัสอ้างอิง {{Ref1}}</span>
                  </v-col>
                  <v-col class="py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 600; color: #A1A1A1;' : 'font-size: 14px; font-weight: 600; color: #A1A1A1;'">สามารถชำระเงินได้ตามขั้นตอนนี้
                      (กรณีชำระเงินผ่านมือถือ)</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">2. เปิดแอปธนาคารของท่าน
                      และเลือกเมนูสแกน QR Code</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span :style="MobileSize ? 'font-size: 12px; font-weight: 400; color: #A1A1A1;' : 'font-size: 14px; font-weight: 400; color: #A1A1A1;'">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                      Code</span>
                  </v-col>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import moment from 'moment'
export default {
  data () {
    return {
      SelectPartnerCode: '',
      dataServicePartner: [],
      showDataServicePartner: [],
      showpackages: [],
      headers: [
        // { text: 'ชื่อ Package', width: '110', align: 'center', sortable: false, value: 'package_name', class: 'backgroundTable fontTable--text' },
        // { text: 'รายละเอียดสินค้า', width: '250', align: 'center', sortable: false, value: 'package_detail', class: 'backgroundTable fontTable--text' },
        // { text: 'ราคาสินค้า', width: '100', sortable: false, align: 'center', value: 'package_price', class: 'backgroundTable fontTable--text' },
        // { text: 'การชำระเงิน', width: '100', sortable: false, align: 'center', value: 'payment_type', class: 'backgroundTable fontTable--text' },
        // { text: 'ราคารวม', width: '100', sortable: false, align: 'center', value: 'package_price', class: 'backgroundTable fontTable--text' }
        { text: 'ชื่อ Package', width: '110', align: '', sortable: false, value: 'package_name', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียดสินค้า', width: '200', align: '', sortable: false, value: 'package_functions', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาสินค้า', width: '100', sortable: false, align: '', value: 'package_price', class: 'backgroundTable fontTable--text' },
        { text: 'การชำระเงิน', width: '100', sortable: false, align: '', value: 'payment_type', class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', width: '100', sortable: false, align: '', value: 'package_price', class: 'backgroundTable fontTable--text' }
      ],
      selectPartnerPackage: [],
      isScrollComplete: false,
      itemsSelectAmountMonth: [
        { text: '1 เดือน', value: '1' },
        { text: '2 เดือน', value: '2' },
        { text: '3 เดือน', value: '3' },
        { text: '4 เดือน', value: '4' },
        { text: '5 เดือน', value: '5' },
        { text: '6 เดือน', value: '6' },
        { text: '7 เดือน', value: '7' },
        { text: '8 เดือน', value: '8' },
        { text: '9 เดือน', value: '9' },
        { text: '10 เดือน', value: '10' },
        { text: '11 เดือน', value: '11' },
        { text: '12 เดือน', value: '12' }
      ],
      SelectAmountMonth: '',
      months: '',
      tempStartDate: null,
      startDate: null,
      endDate: null,
      startDateToBackend: null,
      endDateToBackend: null,
      total: 0,
      menuStartDate: false,
      lazy: false,
      Rules: {
        ItemAmountMouth: [
          v => (v && v.length !== 0) || 'กรุณาเลือกจำนวนเดือน'
        ],
        ItemStartDate: [
          v => (v && v.length !== 0) || 'กรุณาเลือกวันที่เริ่มสัญญา'
        ],
        ItemEndDate: [
          v => (v && v.length !== 0) || 'กรุณาเลือกวันที่สิ้นสุดสัญญา'
        ],
        ItemTypePay: [
          v => !!v || 'กรุณาเลือกช่องทางการชำระเงิน'
        ]
      },
      selectedPaymentMethod: null,
      finalPaymentMethod: null,
      price: '',
      Discount: '',
      sumVat: '',
      Vat: '',
      VATincluded: '',
      totalPrice: '',
      packagePaymentType: '',
      DialogQR: false,
      QRShop: false,
      imageBase64: '',
      ImageQR: '',
      netPrice: '',
      Ref1: '',
      Ref2: '',
      paymenttransactionnumber: '',
      TypeOS: '',
      dialogConfirm: false,
      minDate: moment().startOf('month').format('YYYY-MM-DD')
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    formattedStartDate () {
      return this.startDate ? this.formatDate(this.startDate) : ''
    },
    formattedEndDate () {
      return this.endDate ? this.formatDate(this.endDate) : ''
    },
    isPaymentValid () {
      return !!this.selectedPaymentMethod
    }
  },
  watch: {
    MobileSize (val) {
      var PartnerCode = localStorage.getItem('SelectPartnerCode')
      if (val === true) {
        this.$router.push({ path: `/ConfirmPartnerPackageMobile?SelectPartnerCode=${PartnerCode}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ConfirmPartnerPackage?SelectPartnerCode=${PartnerCode}` }).catch(() => {})
      }
    },
    SelectAmountMonth: 'calculateEndDate',
    startDate: 'calculateEndDate',
    packagePaymentType (newValue) {
      if (newValue === 'Yearly') {
        this.SelectAmountMonth = '12'
      }
    }
  },
  created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNav')
    this.SelectPartnerCode = localStorage.getItem('SelectPartnerCode')
    this.selectPartnerPackage = [JSON.parse(Decode.decode(localStorage.getItem('partnerPackageDetail')))]
    this.TypeOS = this.detectOS()
    // console.log('2', this.selectPartnerPackage)
    if (Array.isArray(this.selectPartnerPackage) && this.selectPartnerPackage.length > 0) {
      this.packagePaymentType = this.selectPartnerPackage[0].payment_type
      // console.log('3', this.packagePaymentType)
    }
    this.getdataServicePartner()
  },
  mounted () {

  },
  methods: {
    backtoPage () {
      if (this.MobileSize) {
        this.$router.replace(`/ShopPartnerDetailsMobile?SelectPartnerCode=${this.SelectPartnerCode}`)
      } else {
        this.$router.replace(`/ShopPartnerDetails?SelectPartnerCode=${this.SelectPartnerCode}`)
      }
    },
    async getdataServicePartner () {
      this.$store.commit('openLoader')
      const shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopSellerID
      }

      await this.$store.dispatch('actionGetListDetailPartnerShop', data)
      var response = await this.$store.state.ModuleShop.stateGetListDetailPartnerShop

      if (response.message === 'list partner success.') {
        this.$store.commit('closeLoader')
        this.dataServicePartner = Object.values(response.data).filter(partnerCode => partnerCode.partner_code === this.SelectPartnerCode)
        this.showDataServicePartner = this.dataServicePartner[0]
        this.getAvaliablePackage = this.showDataServicePartner.avaliable_package
        this.showpackages = await this.showDataServicePartner.packages
        // console.log(this.showDataServicePartner)
        // console.log(this.showpackages)
      }
    },
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    },
    getLastDayOfPreviousMonth () {
      const today = new Date()
      const lastDayOfPreviousMonth = new Date(today.getFullYear(), today.getMonth(), 0)
      return lastDayOfPreviousMonth.toISOString().split('T')[0] // Format as yyyy-mm-dd
    },
    CancelconfirmStartDate () {
      this.startDate = ''
      this.endDate = ''
      this.menuStartDate = false
    },
    confirmStartDate () {
      if (this.tempStartDate) {
        this.startDate = this.tempStartDate
        this.calculateEndDate()
        this.menuStartDate = false
      }
    },
    calculateEndDate () {
      if (this.startDate && this.SelectAmountMonth) {
        const start = moment(this.startDate)
        const endDate = start.add(this.SelectAmountMonth, 'months')
        this.endDate = endDate.format('YYYY-MM-DD')

        this.total = Number(this.SelectAmountMonth * this.selectPartnerPackage[0].package_price)
        this.price = Number(this.total)
        this.VATincluded = Number((this.total * 1.07).toFixed(2))
        this.Vat = Number((this.total * 0.07).toFixed(2))
        this.totalPrice = Number(this.VATincluded)
      }
    },
    formatDate (date) {
      return new Date(date).toLocaleDateString('th-TH', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    },
    getOriginalPaymentMethod (method) {
      if (method === 'qrcode') {
        return 'QR Code'
      } else if (method === 'creditcard') {
        return 'Credit Card'
      } else {
        return method
      }
    },
    formatPaymentMethod (method) {
      if (method === 'qrcode') {
        return 'QR Code Payment'
      } else if (method === 'creditcard') {
        return 'Credit Card'
      } else {
        return method
      }
    },
    confirmPackage () {
      if (this.$refs.detailShop.validate(true) && this.isPaymentValid) {
        this.dialogConfirm = true
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน'
        })
      }
    },
    async PaymentQRCode () {
      this.dialogConfirm = false
      this.$store.commit('openLoader')
      const paymentType = this.getOriginalPaymentMethod(this.selectedPaymentMethod)
      const shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
      const SelectPackage = localStorage.getItem('SelectPackage')

      var dataNormal = {
        service_id: this.showDataServicePartner.service_id,
        partner_code: this.showDataServicePartner.partner_code,
        seller_shop_id: shopSellerID,
        package_code: SelectPackage,
        status: 'waiting',
        payment_method: this.formatPaymentMethod(this.selectedPaymentMethod),
        contract_start: this.startDate,
        contract_end: this.endDate,
        price: this.price,
        vat_price: this.Vat,
        total_price: this.totalPrice
      }

      await this.$store.dispatch('actionUpdateStatusAfterBuyPartner', dataNormal)
      var responseNormal = await this.$store.state.ModuleShop.stateUpdateStatusAfterBuyPartner

      if (responseNormal.message === 'Create order success.') {
        if (paymentType === 'QR Code') {
          var dataOrderNumber = {
            payment_transaction_number: responseNormal.data.payment_transaction_number
          }
          this.paymenttransactionnumber = responseNormal.data.payment_transaction_number
          await this.$store.dispatch('actionPayWithQRCode', dataOrderNumber)
          var resQR = await this.$store.state.ModuleShop.statePayWithQRCode
          if (resQR.result === 'SUCCESS') {
            if (resQR.message !== 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
              this.QRShop = false
            } else if (resQR.message === 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
              this.QRShop = true
            } else {
              this.QRShop = false
            }
            this.netPrice = await resQR.data.net_price
            this.Ref1 = await resQR.data.ref1
            this.Ref2 = await resQR.data.ref2
            var base64 = resQR.data.img_base
            this.imageBase64 = 'data:image/png;base64,' + base64
            var ImageQR = await resQR.data.img_base64
            this.ImageQR = ImageQR
            this.$store.commit('closeLoader')
            this.DialogQR = true
            if (resQR.message !== 'ดึง QR Code ของร้านค้านั้นสำเร็จ') {
              var data = {
                payment_transaction_number: responseNormal.data.payment_transaction_number
              }
              var value = data.payment_transaction_number
              const maxAttempts = 15
              let currentAttempt = 1
              while (currentAttempt <= maxAttempts) {
                await this.$store.dispatch('actionCheckResultQRCodeMarketplace', data)
                const resCheckQR = await this.$store.state.ModuleShop.stateCheckResultQRCodeMarketplace
                if (this.CloseDialog === true) {
                  break
                }
                if (resCheckQR.result === 'SUCCESS') {
                  this.$router.push({ path: `/PaymentPackage?id=${value}` }).catch(() => {})
                  break
                }
                await new Promise(resolve => setTimeout(resolve, 10000))
                currentAttempt++
                if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
                  this.$swal.fire({
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true,
                    icon: 'error',
                    text: 'การชำระเงินไม่เสร็จสมบูรณ์'
                  })
                  this.$router.push({ path: '/ShopJoinPartnerDetails' }).catch(() => {})
                }
              }
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true,
                icon: 'error',
                text: `${resQR.message}`
              })
            }
          }
        } else {
          var resCC
          var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
          data = {
            go_local: goLocalValue,
            payment_transaction_number: responseNormal.data.payment_transaction_number
          }
          await this.$store.dispatch('actionPayWithCreditCard', data)
          resCC = await this.$store.state.ModuleShop.statePayWithCreditCard
          if (resCC.result === 'SUCCESS') {
            localStorage.setItem('PaymentData', Encode.encode(resCC.data))
            this.$router.push('/RedirectPaymentPage').catch(() => {})
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 3500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
            })
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseNormal.message}`
        })
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    closeDialogQR () {
      if (!this.MobileSize) {
        this.$router.push({ path: `/DetailOrderProductShop?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/DetailOrderProductShopMobile?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>

.scrollable-content {
  max-height: 150px;
  overflow-y: auto;
  /* padding: 10px; */
}

.custom-placeholder ::placeholder {
  font-size: 14px !important;
  color: #888; /* เปลี่ยนสีถ้าต้องการ */
}

</style>

<style lang="scss" scoped>
  // ::v-deep table {
  //   tbody {
  //     tr {
  //       td:nth-child(5) {
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //         z-index: 10;
  //         background: white;
  //       }
  //     }
  //   }
  //   thead {
  //     tr {
  //       th:nth-child(1) {
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //         z-index: 10;
  //         background: white;
  //       }
  //     }
  //   }
  //   thead {
  //     tr {
  //       th:nth-child(5) {
  //         z-index: 11;
  //         background: white;
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //       }
  //     }
  //   }
  // }
</style>
