<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0">
      <!-- Header -->
      <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-0' : 'px-0 py-0'">
        <v-row class="mx-0" v-if="!MobileSize">
          <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">e-Withholding Tax</v-card-title>
        </v-row>
        <v-row v-else>
          <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> e-Withholding Tax</v-card-title>
        </v-row>
      </v-col>
      <!-- Title -->
      <v-card-title v-if="MobileSize" style="font-weight: 600; font-size: 18px; line-height: 26px;white-space: normal; display: inline-block; word-break:break-word;">ระบบ NGC สามารถที่ตอบโจทย์แบบเบ็ดเสร็จจบในขั้นตอนเดียวที่ช่วยลดขั้นตอน และเพิ่มความสะดวก ในการหักภาษี สะดวกทั้งผู้รับ และ ผู้จ่ายไม่ต้องเก็บเอกสารในรูปแบบกระดาษอีกต่อไป</v-card-title>
      <v-card-title v-else style="font-weight: 600; font-size: 18px; line-height: 26px;white-space: normal; display: inline-block; word-break:break-word;">ระบบ NGC สามารถที่ตอบโจทย์แบบเบ็ดเสร็จจบในขั้นตอนเดียวที่ช่วยลดขั้นตอน และเพิ่มความสะดวก ในการหักภาษี สะดวกทั้งผู้รับ และ ผู้จ่ายไม่ต้องเก็บเอกสารในรูปแบบกระดาษอีกต่อไป</v-card-title>
      <!-- Text E-tax -->
      <v-card-text id="v-step-0">
        <!-- <v-timeline dense id="v-step-0">
          <v-timeline-item fill-dot color="#E6E6E6" large>
            <template v-slot:icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step_1.png" max-height="24" max-width="24" contain></v-img>
            </template>
            <v-col cols="12" md="11">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">1. สมัครกับบัญชีผู้ใช้กับ <a href="https://etax.one.th/portal/login" style="color: #1B5DD6; font-weight: bold; text-decoration: underline;">e-Tax</a></span>
            </v-col>
          </v-timeline-item>
          <v-timeline-item fill-dot color="#E6E6E6" large>
            <template v-slot:icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step_2.png" max-height="24" max-width="24" contain></v-img>
            </template>
            <v-col cols="12" md="11">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">2. นำ User code และ Access Key มากรอกที่ด้านล่าง (กรณี e-Tax type R1)</span>
            </v-col>
          </v-timeline-item>
          <v-timeline-item fill-dot color="#E6E6E6" large>
            <template v-slot:icon>
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/step_3.png" max-height="24" max-width="24" contain></v-img>
            </template>
            <v-col cols="12" md="11">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">3. หลักจากนั้นเมื่อไหร่ก็ตามที่มีลูกค้าคำสั่งซื้อและจ่ายเงินสำเร็จ ใบกำกับภาษีจะถูกสร้างขึ้นในระบบ e-Tax</span>
            </v-col>
          </v-timeline-item>
        </v-timeline> -->
        <!-- ส่วนแสดง Text Error/Success -->
        <v-row dense justify="center" class="mt-6">
          <v-col cols="12" align="center">
            <v-row dense justify="center">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/shop_1.png" contain max-height="57" max-width="57" :class="MobileSize ? 'mt-1 mb-4' : 'mr-4 mt-1'"></v-img>
              <div style="border-radius: 8px; background: #FBE5E4; min-height: 62px;" :style="IpadSize ? 'width: 360px;' : 'width: 680px;'" v-if="CheckeWHTCredential === false">
                <span class="px-2" style="color: #F5222D; font-weight: 700; display: block; align-items: center; padding-top: 15px;" :style="IpadSize ? 'font-size: 14px; line-height: 22px;' : 'font-size: 20px; line-height: 30px;'">{{ textToShow }}</span>
              </div>
              <div style="border-radius: 8px; background: rgba(82, 196, 26, 0.2); min-height: 62px;" :style="IpadSize ? 'width: 360px;' : 'width: 680px;'" v-else-if="CheckeWHTCredential === true">
                <span class="px-2" style="color: #52C41A; font-weight: 700; font-size: 20px; line-height: 30px; display: block; align-items: center; padding-top: 16px;" :style="IpadSize ? 'font-size: 14px; line-height: 22px;' : 'font-size: 20px; line-height: 30px;'">{{ textToShow }} <v-icon color="#27AB9C">mdi-checkbox-marked-circle</v-icon></span>
              </div>
            </v-row>
          </v-col>
        </v-row>
        <!-- ส่วน Card กรอก e-Tax -->
        <v-row dense justify="center" class="mt-8">
          <v-col cols="12" align="center">
            <v-form ref="formETax" :lazy-validation="lazy">
              <v-card elevation="0" style="background: #EBEBEB; border: 1px solid #EBEBEB; border-radius: 8px;" width="416" height="100%">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="12">
                      <div style="border: 2px solid #5BC3A2; border-radius: 8px; background: #FFFFFF; height: 62px;">
                        <span style="font-weight: 700; font-size: 20px; line-height: 30px; color: #333333; display: block; align-items: center; padding-top: 14px;">Tax ID : {{ tax_id }}</span>
                      </div>
                    </v-col>
                    <v-row dense class="py-1 px-1">
                      <v-col cols="12" align="start">
                        <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Api Key <span style="color: red;">*</span></span>
                        <v-text-field v-model="apikey" dense solo placeholder="ระบุ Api Key" style="border-radius: 8px;" :rules="Rules.apiKey"></v-text-field>
                      </v-col>
                      <v-col cols="12" align="start">
                        <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Authorization <span style="color: red;">*</span></span>
                        <v-text-field v-model="authorization" dense solo placeholder="ระบุ Authorization" style="border-radius: 8px;" :rules="Rules.AccessKey"></v-text-field>
                      </v-col>
                    </v-row>
                  </v-row>
                </v-card-text>
                <v-card-actions class="mb-4">
                  <v-row justify="end" dense>
                    <v-btn color="#27AB9C" rounded outlined class="px-7 mr-2" @click="cancel()">ยกเลิก</v-btn>
                    <v-btn color="#27AB9C" rounded class="white--text px-8 mr-2" @click="confirm()">ยืนยัน</v-btn>
                  </v-row>
                </v-card-actions>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      taxID: '',
      lazy: false,
      CheckeWHTCredential: false,
      authorization: '',
      apikey: '',
      onedata: [],
      Rules: {
        AccessKey: [
          v => !!v || 'กรุณากรอก Authorization'
        ],
        apiKey: [
          v => !!v || 'กรุณากรอก Api Key'
        ]
      },
      company_id: '',
      tax_id: '',
      textToShow: '',
      statusForConfirm: ''
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    this.company_id = companyId.company.company_id
    this.$store.commit('openLoader')
    await this.getCompanyData()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/eWHTCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/eWHTCompany' }).catch(() => {})
      }
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    async getCompanyData () {
      var data = {
        company_id: this.company_id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      // console.log('detail ===========>', response)
      if (response.result === 'SUCCESS') {
        var detailCompany = await response.data
        this.company_id = detailCompany.id
        this.tax_id = detailCompany.tax_id
        await this.checkeWHT()
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่</h3>'
          })
        }
      }
    },
    async checkeWHT () {
      const data = {
        company_id: this.company_id,
        tax_id: this.tax_id
      }
      await this.$store.dispatch('actionsGetDataEwth', data)
      const response = await this.$store.state.ModuleAdminManage.stateGetDataEwth
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.CheckeWHTCredential = true
        this.statusForConfirm = 'edit'
        this.textToShow = 'บริษัทนี้ได้สมัครบริการ e-Withholding Tax เรียบร้อย'
        this.apikey = response.data[0].api_key
        this.authorization = response.data[0].authorization
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.CheckeWHTCredential = false
          this.statusForConfirm = 'add'
          if (response.message === 'Not found company_ewth_auth with this data') {
            this.textToShow = 'บริษัทนี้ยังไม่ได้สมัครบริการ e-Withholding Tax'
          } else {
            this.textToShow = 'เกิดข้อผิดพลาดภายในระบบ กรุณาติดต่อเจ้าหน้าที่'
          }
        }
      }
    },
    cancel () {
      this.apikey = ''
      this.authorization = ''
      this.$refs.formETax.reset()
    },
    async confirm () {
      this.$store.commit('openLoader')
      const data = {
        company_id: this.company_id,
        tax_id: this.tax_id,
        api_key: this.apikey,
        authorization: this.authorization
      }
      let response = ''
      if (this.statusForConfirm === 'add') {
        await this.$store.dispatch('actionsAddDataEwth', data)
        response = await this.$store.state.ModuleAdminManage.stateAddDataEwth
      } else {
        await this.$store.dispatch('actionsEditDataEwth', data)
        response = await this.$store.state.ModuleAdminManage.stateEditDataEwth
      }
      if (response.message === 'Insert company_ewth_auth data success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'เพิ่มข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.checkeWHT()
      } else if (response.message === 'Update company_ewth_auth data success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'แก้ไขข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.checkeWHT()
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: response.message,
            showConfirmButton: false,
            timer: 2500
          })
        }
      }
    }
  }
}
</script>
