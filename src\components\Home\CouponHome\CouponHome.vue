<template>
  <v-container v-if="CouponsIteam.length > 0">
    <v-row>
      <h2 class="pt-1 ml-3 mt-2 mb-4 diaplayWeb"
        style="font-weight: bold; font-size: 32px; line-height: 48px; color: #333333;">คูปองส่วนลด</h2>
      <h2 class="pt-1 ml-4 mt-2 mb-4 displayIPAD"
        style="font-weight: bold; font-size: 20px; line-height: 48px; color: #333333;">คูปองส่วนลด</h2>
      <h2 class="pt-1 ml-3 mt-2 mb-4 displayMobile"
        style="font-weight: bold; font-size: 18px; line-height: 48px; color: #333333;">คูปองส่วนลด</h2>
      <v-spacer></v-spacer>
      <v-btn text @click="ItemCouponAll()" color="#27AB9C " class="mt-4 diaplayWeb" plain style="font-weight: 600;">
        ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2">
          <v-icon color="#008E00">mdi-chevron-right</v-icon>
        </v-btn>
      </v-btn>
      <v-btn text @click="ItemCouponAll()" color="#27AB9C" class="mt-4 displayIPAD" plain style="font-weight: 600;">
        ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2">
          <v-icon color="#008E00">mdi-chevron-right</v-icon>
        </v-btn>
      </v-btn>
      <v-btn text @click="ItemCouponAll()" color="#27AB9C" class="mt-4 displayMobile" plain style="font-weight: 600;">
        ดูทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2">
          <v-icon color="#008E00">mdi-chevron-right</v-icon>
        </v-btn>
      </v-btn>
    </v-row>
    <VueSlickCarousel v-if="MobileSize" v-bind="settings" ref="carousel">
      <v-container class="pr-0" v-for="(items, index) in CouponsIteam" :key="index">
        <CardCouponMobile v-if="index === 0" :items="items" :keep="true" colorCard="blue" />
        <CardCouponMobile v-if="index === 1" :items="items" :keep="true" colorCard="green" />
        <CardCouponMobile v-if="index === 2" :items="items" :keep="true" colorCard="purpler" />
      </v-container>
    </VueSlickCarousel>
    <v-row v-else>
      <v-col class="d-flex justify-center" v-for="(items, index) in CouponsIteam" :key="index" cols="12" sm="4" xs="12"
        md="4">
        <span v-if="IpadProSize">
          <CardCouponMobile v-if="index === 0" :items="items" :keep="true" colorCard="blue" />
          <CardCouponMobile v-if="index === 1" :items="items" :keep="true" colorCard="green" />
          <CardCouponMobile v-if="index === 2" :items="items" :keep="true" colorCard="purpler" />
        </span>
        <span v-else-if="IpadSize">
          <CardCouponMobile v-if="index === 0" :items="items" :keep="true" colorCard="blue" />
          <CardCouponMobile v-if="index === 1" :items="items" :keep="true" colorCard="green" />
          <CardCouponMobile v-if="index === 2" :items="items" :keep="true" colorCard="purpler" />
        </span>
        <span v-else>
          <CardCoupon v-if="index === 0" :items="items" :keep="true" colorCard="blue" />
          <CardCoupon v-if="index === 1" :items="items" :keep="true" colorCard="green" />
          <CardCoupon v-if="index === 2" :items="items" :keep="true" colorCard="purpler" />
        </span>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel-theme.css'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'
export default {
  components: {
    VueSlickCarousel,
    CardCoupon: () => import('@/components/CardCoupon/CardCoupon'),
    CardCouponMobile: () => import('@/components/CardCoupon/CardCouponMobileUI')
  },
  data () {
    return {
      pageMax: 1,
      pageNumber: 1,
      companyId: '',
      id_company: '',
      CouponsIteam: [],
      settings: {
        infinite: false,
        slidesToShow: 1.2,
        speed: 900,
        rows: 1,
        slidesPerRow: 1,
        slidesToScroll: 1
      }
    }
  },
  created () {
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.getCouponHome()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    this.$EventBus.$on('KeepCouponPageHome', this.getCouponHome)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('KeepCouponPageHome')
    })
  },
  methods: {
    async getCouponHome () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('SetRowCompany') !== null) {
        const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        this.companyId = companyId.company.company_id
      } else {
        this.companyId = ''
      }
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      var data = {
        seller_shop_id: '-1',
        role_user: dataRole.role,
        company_id: this.id_company,
        list_type: 'panit',
        page: 1
      }
      await this.$store.dispatch('actionsAllCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateAllCouponInShop
      this.CouponsIteam = []
      for (let i = 0; i < 3; i++) {
        this.CouponsIteam.push({
          image: response.data.list_coupon[i].couponImagePath,
          name: response.data.list_coupon[i].couponName,
          description: response.data.list_coupon[i].couponDescription,
          couponDate: {
            useStartDate: response.data.list_coupon[i].useStartDate,
            useEndDate: response.data.list_coupon[i].useEndDate
          },
          status: response.data.list_coupon[i].status,
          couponId: response.data.list_coupon[i].couponId,
          shop_name: response.data.list_coupon[i].shop_name
        })
      }
    },
    ItemCouponAll () {
      this.$router.push({ path: '/AllCoupons' })
    }
  }
}
</script>

<style scoped>
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
      display: none;
  }

  .diaplayWeb {
      display: none;
  }
  }

  @media screen and (min-width: 1280px) {
      .displayIPAD {
          display: none;
      }

      .displayMobile {
          display: none;
      }

      .diaplayWeb {
          display: inline;
      }
  }
</style>
