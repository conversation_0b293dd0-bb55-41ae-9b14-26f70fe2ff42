<template>
  <v-container>
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">{{$t('ReviewListBuyer.title')}}</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> {{$t('ReviewListBuyer.title')}}</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="py-0">
          <a-tabs @change="SelectReviewTabs">
            <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
            <a-tab-pane :key="$t('ReviewListBuyer.notEvaluation')"><span slot="tab">{{$t('ReviewListBuyer.notEvaluation')}} <a-tag color="#E9A016" style="border-radius: 8px;">{{ countReviewWaiting }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="$t('ReviewListBuyer.assessed')"><span slot="tab">{{$t('ReviewListBuyer.assessed')}} <a-tag color="#1AB759" style="border-radius: 8px;">{{ countReviewSuccess }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-container>
          <v-col v-if="disableTable === true" cols="12" md="6" sm="6" class="pl-0 pr-0 pt-0">
            <v-text-field v-model="search" dense hide-details outlined rounded :placeholder="$t('ReviewListBuyer.searchOrder')">
              <v-icon slot="append">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col v-if="disableTable === true" cols="12" md="12" sm="12" :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'pl-2 pr-2 mb-3'">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === $t('ReviewListBuyer.notEvaluation')">{{$t('ReviewListBuyer.orderNot')}} {{ showCountOrder }} {{$t('ReviewListBuyer.order')}}</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === $t('ReviewListBuyer.assessed')">{{$t('ReviewListBuyer.orderSuccess')}} {{ showCountOrder }} {{$t('ReviewListBuyer.order')}}</span>
          </v-col>
          <v-card v-if="disableTable === true" outlined class="small-card mb-5" min-height="512">
            <v-data-table
              :headers="StateStatus === $t('ReviewListBuyer.notEvaluation') ? headers : headersAssess"
              :items="DataTable"
              :search="search"
              :custom-filter="customFilter"
              style="width:100%;"
              height="100%"
              @pagination="countOrdar"
              class=""
              :no-results-text="$t('ReviewListBuyer.noResultsText')"
              :no-data-text="$t('ReviewListBuyer.noDataText')"
              :update:items-per-page="getItemPerPage"
              :footer-props="{'items-per-page-text': $t('ReviewListBuyer.numberRows')}"
              :items-per-page="10"
            >
              <template v-slot:[`item.expired_review`]="{ item }">
                <span v-if="item.expired_review === '-'"> - </span>
                <span v-else>{{ formatDateToShow(item.expired_review) }}</span>
              </template>
              <template v-slot:[`item.invoice`]="{ item }">
                <div v-if="item">
                  -
                </div>
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'reviewed'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">{{ $t('ReviewListBuyer.status1') }}</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_review'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#E9A016">{{ $t('ReviewListBuyer.status2') }}</v-chip>
                </span>
              </template>
              <template v-slot:[`item.actions`]="{ item }">
                <v-btn v-if="item.status === 'reviewed'" color="#27AB9C" class="white--text" small @click="viewDetail(item)">
                  {{ $t('ReviewListBuyer.btnDetail') }}
                </v-btn>
                <v-btn v-else color="#52C41A" class="white--text" small @click="review(item)">
                  {{ $t('ReviewListBuyer.btnReview') }}
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
          <v-col cols="12" v-if="disableTable === false" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>{{ $t('ReviewListBuyer.noOrderEvaluated') }} {{ StateStatus }}</b></h2>
          </v-col>
        </v-container>
      </v-row>
    </v-card>
    <ModalReviewProduct ref="ModalReviewProduct" />
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    ModalReviewProduct: () => import('@/components/UserProfile/ModalReview/ReviewProduct')
  },
  data () {
    return {
      disableTable: false,
      search: '',
      reviewList: {},
      StateStatus: `${this.$t('ReviewListBuyer.notEvaluation')}`,
      keyCheckHead: 0,
      headers: [
        { text: `${this.$t('ReviewListBuyer.thOrder')}`, value: 'order_number', sortable: false, align: 'center', width: '140', class: 'backgroundTable fontTable--text' },
        { text: `${this.$t('ReviewListBuyer.thStatus')}`, filterable: false, value: 'status', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: `${this.$t('ReviewListBuyer.thExpired')}`, filterable: false, value: 'expired_review', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: `${this.$t('ReviewListBuyer.thManage')}`, filterable: false, value: 'actions', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      headersAssess: [
        { text: `${this.$t('ReviewListBuyer.thOrder')}`, value: 'order_number', sortable: false, align: 'center', width: '140', class: 'backgroundTable fontTable--text' },
        { text: `${this.$t('ReviewListBuyer.thStatus')}`, filterable: false, value: 'status', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: `${this.$t('ReviewListBuyer.thExpired')}`, filterable: false, value: 'expired_review', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: `${this.$t('ReviewListBuyer.thManage')}`, filterable: false, value: 'actions', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      countReviewWaiting: 0,
      countReviewSuccess: 0,
      countRefundAll: 0,
      showCountOrder: 0,
      pageCount: 5,
      page: 1,
      itemsPerPage: 10,
      DataTable: [],
      dataRole: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAccount')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.ListRefundDataTable()
    }
  },
  mounted () {
    this.$EventBus.$on('SentGetReview', this.refreshData)
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    StateStatus (val) {
      if (val === `${this.$t('ReviewListBuyer.notEvaluation')}`) {
        this.DataTable = this.reviewList.order_waiting_review !== undefined ? this.reviewList.order_waiting_review : []
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === `${this.$t('ReviewListBuyer.assessed')}`) {
        this.DataTable = this.reviewList.order_review !== undefined ? this.reviewList.order_review : []
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/reviewBuyerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/reviewBuyer' }).catch(() => {})
      }
    }
  },
  methods: {
    formatDateToShow (data) {
      if (!data) return null
      // const datePart = data.split(/T| /)[0]
      const [year, month, day] = data.split('-')
      if (this.$i18n.locale === 'th') {
        const monthName = new Date(`${year}-${month}-01`).toLocaleString(this.$i18n.locale, { month: 'long' })
        return `${day} ${monthName} ${parseInt(year) + 543}`
      } else {
        const monthName = new Date(`${year}-${month}-01`).toLocaleString(this.$i18n.locale === 'en', { month: 'long' })
        return `${day} ${monthName} ${year}`
      }
    },
    customFilter (items, search, filters) {
      var searchByOrderNumber = filters.order_number.indexOf(search) !== -1
      return searchByOrderNumber
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    refreshData () {
      this.ListRefundDataTable()
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
    },
    SelectReviewTabs (item) {
      this.StateStatus = item
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async ListRefundDataTable () {
      this.$store.commit('openLoader')
      var dataSent = {
        role_user: 'ext_buyer',
        company_id: '-1'
      }
      await this.$store.dispatch('actionsListOrderReviewBuyer', dataSent)
      var res = await this.$store.state.ModuleReviewBuyer.stateListOrderReviewBuyer
      if (res.message === 'Get list reviews successful.') {
        this.$store.commit('closeLoader')
        this.reviewList = res.data
        this.countReviewWaiting = this.reviewList.order_waiting_review.length
        this.countReviewSuccess = this.reviewList.order_review.length
        if (this.StateStatus === `${this.$t('ReviewListBuyer.notEvaluation')}`) {
          this.DataTable = this.reviewList.order_waiting_review
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === `${this.$t('ReviewListBuyer.assessed')}`) {
          this.DataTable = this.reviewList.order_review
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 7000,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR',
            text: `${res.message}`
          })
        }
      }
    },
    viewDetail (item) {
      const actions = 'edit'
      this.$refs.ModalReviewProduct.open(item, item.order_number, actions, 'ext_buyer')
      // this.$router.push({ path: '/returnDetail' }).catch(() => {})
    },
    review (item) {
      const actions = 'create'
      this.$refs.ModalReviewProduct.open(item, item.order_number, actions, 'ext_buyer')
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
