<template>
  <div>
    <v-container grid-list-xs>
      <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-2">
        <span style="font-weight: bold; font-size: 24px; line-height: 32px; align-items: center; color: #333333;" class="pl-4">รายการคำสั่งซื้อสินค้าทั้งหมด</span>
        <v-row class="pt-4 pl-4 pr-4" justify="center" align-content="center">
          <v-col cols="4" md="4" sm="4">
            <v-card width="317" height="129" outlined style="box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15); border-radius: 8px;" :style="keyCheckHead === 0 ? 'background-color: #FFFAE6; border-color: #FFBA00; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.45);' : 'background-color: #FFFFFF'">
              <v-row class="pt-4 pl-6" justify="center" align-content="center">
                <v-col cols="4" >
                  <v-img src="@/assets/ImageINET-Marketplace/ICONApproved/ApproveWait.png" contain></v-img>
                </v-col>
                <v-col cols="8">
                  <v-row dense>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; line-height: 24px; text-align: left; letter-spacing: 0em;">คำสั่งซื้อรออนุมัติ</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 64px; font-weight: 700; line-height: 60px; text-align: left; letter-spacing: 0em; color: #FFBA00;">{{ sumAwaitApproved }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="4" md="4" sm="4">
            <v-card width="317" height="129" outlined style="box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15); border-radius: 8px;" :style="keyCheckHead === 1 ? 'background-color: #CAF7E3; border-color: #1AB759; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.45);' : 'background-color: #FFFFFF'">
              <v-row class="pt-4 pl-6" justify="center" align-content="center">
                <v-col cols="4" >
                  <v-img src="@/assets/ImageINET-Marketplace/ICONApproved/ApproveSuccess.png" contain></v-img>
                </v-col>
                <v-col cols="8">
                  <v-row dense>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; line-height: 24px; text-align: left; letter-spacing: 0em;">คำสั่งซื้อที่อนุมัติ</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 64px; font-weight: 700; line-height: 60px; text-align: left; letter-spacing: 0em; color: #1AB759;">{{ sumAprproved }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="4" md="4" sm="4">
            <v-card width="317" height="129" outlined style="box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15); border-radius: 8px;" :style="keyCheckHead === 2 ? 'background-color: #FFEBE4; border-color: #FF4300; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.45);' : 'background-color: #FFFFFF'">
              <v-row class="pt-4 pl-6"  justify="center" align-content="center">
                <v-col cols="4">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONApproved/ApproveCancel.png" contain></v-img>
                </v-col>
                <v-col cols="8">
                  <v-row dense>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; line-height: 24px; text-align: left; letter-spacing: 0em;">คำสั่งซื้อที่ยกเลิก</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 64px; font-weight: 700; line-height: 60px; text-align: left; letter-spacing: 0em; color: #FF4300;">{{ sumCancelApproved }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
      <v-card elevation="0">
        <!-- <v-card-title>รายการซื้อของฉัน</v-card-title> -->
        <!-- <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay> -->
        <v-row no-gutters>
          <v-col cols="12" class="py-0 px-4">
            <a-tabs @change="SelectDetailOrder">
              <a-tab-pane v-for="item in OrderNameApproved" :key="item.key" :tab="item.name"></a-tab-pane>
            </a-tabs>
          </v-col>
          <v-col v-if="disableTable === true" cols="12" md="6" class="pl-4">
            <v-text-field v-model="search" dense hide-details outlined rounded placeholder="ค้นหาจากชื่อผู้ขาย หมายเลขคำสั่งซื้อ หรือชื่อสินค้าในทุกคำสั่งซื้อ">
              <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" v-if="keyCheckHead === 0 && sumAwaitApproved !== 0" class="pl-4 pt-6">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333;">แสดงรายการจำนวนคำสั่งซื้อที่รออนุมัติ {{ rowPerPage &lt; 0 ? sumAwaitApproved : rowPerPage }} รายการ</span>
          </v-col>
          <v-col cols="12" v-else-if="keyCheckHead === 1 && sumAprproved !== 0" class="pl-4 pt-6">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333;">แสดงรายการจำนวนคำสั่งซื้อที่อนุมัติ {{ rowPerPage &lt; 0 ? sumAprproved : rowPerPage }} รายการ</span>
          </v-col>
          <v-col cols="12" v-else-if="keyCheckHead === 2 && sumCancelApproved !== 0" class="pl-4 pt-6">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333;">แสดงรายการจำนวนคำสั่งซื้อที่ยกเลิก {{ rowPerPage &lt; 0 ? sumCancelApproved : rowPerPage }} รายการ</span>
          </v-col>
          <v-col cols="12">
            <v-card v-if = "disableTable === true" outlined class="small-card mx-4 my-5">
              <v-data-table
              :headers="keyCheckHead == 0 ? headersApprove : keyCheckHead == 1 ? headersSuccess : headersCancel"
              :items="DataTable"
              :search="search"
              :items-per-page="10"
              :footer-props="footerProps"
              :update:items-per-page="getItemPerPage"
              height="480"
              >
                <template v-slot:[`item.created_at`]="{ item }">
                  {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
                </template>
                <template v-slot:[`item.payment_transaction_number`]="{ item }">
                  <span>{{item.payment_transaction_number}}</span>
                </template>
                <template v-slot:[`item.net_price`]="{ item }">
                  {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                </template>
                <template v-slot:[`item.status`]="{ item }">
                  <span v-if="item.status === 'Pending'">
                    <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
                  </span>
                  <span v-else-if="item.status === 'Approved'">
                    <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
                  </span>
                  <span v-else-if="item.status === 'Not Approved'">
                    <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกคำสั่งซื้อ</v-chip>
                  </span>
                </template>
                <template v-slot:[`item.buyer_received_status`]="{ item }">
                  <v-row class="pt-5">
                    <v-select v-model="item.buyer_received_status" :items="receive_items" item-text="text" item-value="value" @change="UpdateStatusBuyer(item)" outlined dense></v-select>
                  </v-row>
                </template>
                <template v-slot:[`item.detail`]="{ item }">
                  <v-btn text rounded color="#27AB9C" small @click="goDetailPO(item)">
                    <b>รายละเอียด</b>
                    <v-icon x-small>mdi-greater-than</v-icon>
                  </v-btn>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
          <v-col cols="12" v-if="disableTable === false" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
              <b v-if="keyCheckHead === 0">ไม่มีคำสั่งซื้อที่รออนุมัติ</b>
              <b v-if="keyCheckHead === 1">ไม่มีคำสั่งซื้อที่อนุมัติ</b>
              <b v-if="keyCheckHead === 2">ไม่มีคำสั่งซื้อที่ยกเลิก</b>
            </h2>
          </v-col>
        </v-row>
      </v-card>
    </v-container>
  </div>
</template>

<script>
import { Encode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      footerProps: { 'items-per-page-options': [5, 10, 15, 30, -1] },
      orderList: [],
      StateStatus: 0,
      rowPerPage: 10,
      disableTable: false,
      dataRole: '',
      OrderNameApproved: [
        // { key: 0, name: 'ข้อมูลไม่ครบ' },
        { key: 0, name: 'คำสั่งซื้อรออนุมัติ' },
        { key: 1, name: 'คำสั่งซื้อที่อนุมัติ' },
        { key: 2, name: 'คำสั่งซื้อที่ยกเลิก' }
      ],
      keyCheckHead: 0,
      headersApprove: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'net_price', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ผู้ขออนุมัติ', value: 'buyer_name', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการอนุมัติ', value: 'status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: ' ', value: 'detail', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersSuccess: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'net_price', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่อนุมัติ', value: 'time_approve', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการอนุมัติ', value: 'status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: ' ', value: 'detail', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersCancel: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'net_price', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ยกเลิก', value: 'time_approve', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการอนุมัติ', value: 'status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: ' ', value: 'detail', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: '',
      sumAwaitApproved: 0,
      sumAprproved: 0,
      sumCancelApproved: 0,
      dataRoleApprove: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    DataTable (val) {
      // console.log('DataTable', val)
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.orderList.data.order_pending
        this.keyCheckHead = 0
        this.sumAwaitApproved = this.orderList.data.order_pending.length
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.data.order_approved
        this.keyCheckHead = 1
        this.sumAprproved = this.orderList.data.order_approved.length
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.data.order_not_approved
        this.keyCheckHead = 2
        this.sumCancelApproved = this.orderList.data.order_not_approved.length
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  async created () {
    if (localStorage.getItem('oneData') !== null) {
      // console.log('StateStatus', this.StateStatus)
      this.$EventBus.$emit('changeNav')
      this.$EventBus.$on('getPOBuyer', this.SwitchRole)
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('roleUserApprove') !== null) {
        this.dataRoleApprove = JSON.parse(localStorage.getItem('roleUserApprove'))
      }
      this.ListDataTable()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    getItemPerPage (val) {
      this.rowPerPage = val
    },
    async ListDataTable () {
      // await this.$store.dispatch('actionDetListOrderApprove')
      // this.orderList = await this.$store.state.ModuleOrder.stateListOrderApprove
      this.orderList = {
        result: 'success',
        data: {
          order_pending: [
            {
              created_at: '6/10/22',
              payment_transaction_number: '1234567890',
              net_price: '120.12345',
              status: 'Pending',
              buyer: 'what',
              buyer_received_status: 'the',
              detail: ''
            }
          ],
          order_approved: [
            {
              created_at: '1/12/22',
              payment_transaction_number: '4329874922',
              net_price: '400',
              status: 'Approved',
              buyer: 'what',
              buyer_received_status: 'the',
              detail: '',
              time_approve: '1/12/22'
            },
            {
              created_at: '1/12/22',
              payment_transaction_number: '67890654',
              net_price: '400',
              status: 'Approved',
              buyer: 'what',
              buyer_received_status: 'the',
              detail: '',
              time_approve: '1/12/22'
            },
            {
              created_at: '1/12/22',
              payment_transaction_number: '097754',
              net_price: '400',
              status: 'Approved',
              buyer: 'what',
              buyer_received_status: 'the',
              detail: '',
              time_approve: '1/12/22'
            }],
          order_not_approved: [
            {
              created_at: '1/12/22',
              payment_transaction_number: '4329874922',
              net_price: '400',
              status: 'Approved',
              buyer: 'what',
              buyer_received_status: 'the',
              detail: '',
              time_approve: '1/13/22'
            },
            {
              created_at: '1/12/22',
              payment_transaction_number: '4329874922',
              net_price: '400',
              status: 'Approved',
              buyer: 'what',
              buyer_received_status: 'the',
              detail: '',
              time_approve: '1/13/22'
            }]
        }
      }

      // console.log('orderlist', this.orderList)
      if (this.orderList.result !== 'FAILED') {
        this.sumAwaitApproved = this.orderList.data.order_pending.length
        this.sumAprproved = this.orderList.data.order_approved.length
        this.sumCancelApproved = this.orderList.data.order_not_approved.length
        // this.overlay = true
        // this.ProcurementData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('ProcurementData'))))
        // this.getDataTable()
        if (this.StateStatus === 0) {
          this.DataTable = this.orderList.data.order_pending
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 1) {
          this.DataTable = this.orderList.data.order_approved
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 2) {
          this.DataTable = this.orderList.data.order_not_approved
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
      } else {
        // console.log('เข้ามาแล้วโว้ยยยยยยยย')
        this.$swal.fire({ icon: 'error', title: this.orderList.message, showConfirmButton: false, timer: 1500 })
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    async UpdateStatusBuyer (item) {
      // console.log(item)
      if (item.seller_sent_status === 'not_sent') {
        this.$swal.fire({
          icon: 'warning',
          text: 'สินค้ากำลังจัดส่ง',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      } else {
        const update = {
          order_number: item.order_number,
          buyer_received_status: item.buyer_received_status
        }
        // console.log('update', update)
        await this.$store.dispatch('actionUpdateStatusBuyer', update)
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      }
    },
    async GoToPayment (item) {
      const PaymentID = {
        payment_transaction_number: item.payment_transaction_number
      }
      // console.log('payment_transaction_number', PaymentID)
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      // console.log('respose paymenttttttttt', response)
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    async orderDetail (val) {
      // console.log('path to not tell =====>', val.pdf_for_buyer)
      window.open(`${val.pdf_for_buyer}`)
      // var data = {
      //   payment_transaction_number: val.payment_transaction_number
      // }
      //   console.log('data naja', data)
      // await this.$store.dispatch('actionOrderDetail', data)
      // await this.$router.push({ path: '/quotation1shop' }).catch(() => {})
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('order detail na', response)
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      //   console.log('กี่ร้านนะะะะะะะะะ', response.data[0].order_number[0])
      // }
    },
    SelectDetailOrder (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.ListDataTable()
    },
    // async getDataTable () {
    //   this.overlay = true
    //   const data = {
    //     procurement_org_id: this.ProcurementData.procurement_org_id
    //   }
    //   await this.$store.dispatch('actionListOrderProcurement', data)
    //   var response = await this.$store.state.ModuleCart.stateListOrderProcurement
    //   // console.log('response List Order Procurement', response)
    //   if (response.result === 'SUCCESS') {
    //     this.responseData = response.data
    //     this.overlay = false
    //   }
    // },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateDetailOrder
      // console.log('response detail order', response)
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem('MyOrderDetail', Encode.encode(JSON.stringify(response.data)))
        this.$router.push('/myorderdetail')
      }
    },
    goDetailPO (item) {
      var data = {
        payment_transaction_number: item.payment_transaction_number,
        role_user: this.dataRoleApprove.role
      }
      localStorage.setItem('orderNumber', Encode.encode(data))
      this.$router.push({ path: '/approveDetail' }).catch(() => {})
      // if (item.transaction_status === 'Pending' || item.transaction_status === 'Approve') {
      //   this.$router.push('/pobuyerdetailapproveui')
      // } else {
      //   this.$router.push('/pobuyerdetailui')
      // }
    }
  }
}
</script>
