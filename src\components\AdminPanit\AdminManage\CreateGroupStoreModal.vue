<template>
<div class="text-center">
    <v-dialog v-model="openModalCreateGroupShop" width="732" persistent>
      <v-card min-height="558px">
        <v-toolbar dark dense elevation="0" color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>เพิ่ประเภทร้านค้า</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-form ref="FormSettingTier" :lazy-validation="lazy">
          <!-- <v-toolbar dark dense elevation="0" color="#BDE7D9">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>{{ title }}</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar> -->
          <v-card-text class="px-0 pt-0">
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '782px'" style="height: 50px; border-radius: 0px 0px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 20px 20px 10px 20px;' : 'padding: 0px 48px 10px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/Create_Store/partnerShopDetail.png" max-height="62" max-width="62"></v-img>
                        <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 16px;"> ประเภทร้านค้า </span>
                      </v-row>
                      <span class="ml-auto pt-3" style="text-align: end;" v-if="actions === 'edit'">
                        <v-row dense>
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะการใช้งาน :</span>
                          <v-switch color="#52C41A" class="mt-0 px-1 pt-2" inset v-model="changeStatus" @click="updateStatusSettingTier(changeStatus)"></v-switch>
                          <span class="mt-1" v-if="data.status === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                          </span>
                          <span class="mt-1" v-else>
                            <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                          </span>
                        </v-row>
                      </span>
                    </v-col>
                    <v-col cols="12" v-else>
                      <v-row dense>
                        <v-img src="@/assets/Create_Store/partnerShopDetail.png" max-height="62" max-width="62"></v-img>
                        <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 14px;"> ข้อมูลกลุ่มของร้านค้า </span>
                      </v-row>
                      <v-row class="pt-3 pl-4" v-if="actions === 'edit'">
                        <v-row dense>
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 14px;">สถานะการใช้งาน :</span>
                          <v-switch color="#52C41A" class="mt-0 px-1 pt-2" inset v-model="changeStatus" @click="updateStatusSettingTier(changeStatus)"></v-switch>
                          <span class="mt-1" v-if="data.status === 'active'">
                            <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                          </span>
                          <span class="mt-1" v-else>
                            <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                          </span>
                        </v-row>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="12" sm="12">
                      <span>ชื่อกลุ่มร้าน<span style="color: red;">*</span></span>
                      <v-text-field style="border-radius: 8px;" class="input_text namedoc_input" placeholder="ระบุชื่อ" outlined dense v-model="FormData.group_name" @keypress="CheckSpacebar($event)"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="12" sm="12">
                      <span>คำอธิบายสั้นๆ<span style="color: red;">*</span></span>
                      <v-text-field style="border-radius: 8px;" class="input_text namedoc_input" placeholder="" outlined dense v-model="FormData.group_shop_short_description" oninput="this.value = this.value.replace(/^0/, '')">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="12" sm="12">
                      <span>คำอธิบาย<span style="color: red;">*</span></span>
                      <v-textarea
                        name="input-7-4"
                        label=""
                        value=""
                        outlined v-model="FormData.group_shop_description"  oninput="this.value = this.value.replace(/^0/, '')"
                      >
                    </v-textarea>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-card class="pa-5" elevation="0" width="100%" height="100%" style="background: #F2F2F2; border-radius: 8px;">
                          <v-row>
                            <v-col cols="6" class="pa-3">
                              <span style="font-weight: bold; line-height: 26px; color: #333333;" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 18px;'">รายชื่อร้านค้า</span>
                            </v-col>
                            <!-- <v-col cols="6" align="end" class="pa-3">
                              <v-btn v-if="FormData.group_seller_shop.length === 0" text @click="addform()" color="#27AB9C" style="color: #27AB9C;">
                                <v-icon left color="#27AB9C">mdi-plus</v-icon>เพิ่มข้อมูล
                              </v-btn>
                            </v-col> -->
                          <!-- </v-row>
                            <v-row class="mt-2" v-if="FormData.group_seller_shop.length > 0" no-gutters>
                              <v-col cols="12" md="12">
                              <div v-for="(item, index) in FormData.group_seller_shop" :key="index" draggable="true" @dragstart="dragStart(index, $event)" @dragover.prevent @dragenter="dragEnter" @dragleave="dragLeave" @dragend="dragEnd" @drop="dragFinish(index, $event)">
                              <v-select
                              style="display: inline-block;"
                                :items="itemGroupName"
                                v-model="selectionItem"
                                outlined
                                item-value="seller_shop_id"
                                v-on:change="setModel"
                                v-on:mousedown="setIndex(index)"
                                return-object
                                :disabled="item.disabled"
                              >
                                <template v-slot:selection="{ item }">
                                  <v-row justify="center">
                                    <v-col cols="12" lg="6" md="3">
                                    <img :src="item.shop_profile"  width="60px"  height="30px"/>
                                  </v-col>
                                <v-col cols="12" lg="6" md="9" class="mt-3">
                                  {{ item.shop_name }}
                                </v-col>
                                </v-row>
                                </template>
                                <template v-slot:item="{ item }">
                                  <v-row justify="center">
                                    <v-col cols="12" lg="6" md="3">
                                    <img :src="item.shop_profile !== null ? item.shop_profile : require('@/assets/ImageINET-Marketplace/Shop/iconShop/no-image.png')"  width="60px" height="30px"/>
                                  </v-col>
                                <v-col cols="12" lg="6" md="9">
                                  {{ item.shop_name}}
                                </v-col>
                                </v-row>
                                  </template>
                              </v-select>
                                <v-col :cols="MobileSize ? 10 : 11" class="py-0">
                                <v-text-field class="namedoc_input" v-model="item.name_document" placeholder="ระบุชื่อเอกสาร" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                              </v-col>
                              <div  style="display: inline-block;" :cols="MobileSize ? 2 : 1" md="8" class="mt-0 pl-0 py-0">
                                <v-btn @click="deleteGroupName(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                              </div>
                            </div>
                          </v-col>
                            </v-row>
                          <v-row>
                            <v-col v-if="FormData.group_seller_shop.length > 0" cols="12" align="center" class="pa-0">
                              <v-btn text @click="addform()" color="#27AB9C" style="color: #27AB9C;">
                                <v-icon left color="#27AB9C">mdi-plus</v-icon>เพิ่มข้อมูล
                              </v-btn>
                            </v-col>
                          </v-row> -->
                        <v-col cols="12">
                          <v-autocomplete
                            v-model="selectGroup"
                            :disabled="isUpdating"
                            :items="itemGroupName"
                            color="blue-grey-lighten-2"
                            item-title="shop_name"
                            item-value="seller_shop_id"
                            chips
                            closable-chips
                            multiple
                          >
                          <template v-slot:selection="data">
                            <draggable
                :id="data.index"
                :list="selectGroup"
                v-bind="dragOptionsChips"
                :move="moveData"
                @change="changeData"
            >
                  <v-chip
                  draggable
                    v-model="selectGroup[data.index]"
                    :key="data.item"
                    @mousedown.stop
                    @click.stop
                    v-bind="data.attrs"
                    :input-value="data.selected"
                    close
                    @click="data.select"
                    @click:close="remove(data.item)"
                  >
                    <v-avatar left>
                      <v-img :src="data.item.shop_profile"></v-img>
                    </v-avatar>
                    {{ data.item.shop_name }}
                  </v-chip>
                </draggable>
                </template>
                <template v-slot:item="data">
                  <template v-if="typeof data.item !== 'object'">
                    <v-list-item-content v-text="data.item"></v-list-item-content>
                  </template>
                  <template v-else>
                    <v-list-item-avatar>
                      <img :src="data.item.shop_profile">
                    </v-list-item-avatar>
                    <v-list-item-content>
                      <v-list-item-title v-html="data.item.shop_name"></v-list-item-title>
                    </v-list-item-content>
                  </template>
                </template>
                          </v-autocomplete>
                        </v-col>
                          </v-row>
                        </v-card>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12">
                  <span class="subTitle">เพิ่มรูปภาพโลโก้ร้านค้า</span>
                    <v-card
                    v-if="Detail2.product_image.length === 0"
                    class="mt-3"
                    elevation="0"
                    style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px;"
                    height="400px%"
                    @click="onPickFile2()"
                    >
                      <v-card-text >
                        <v-row dense align="center" justify="center" style="cursor: pointer;">
                          <v-file-input v-model="DataImage2" :items="DataImage2"
                            accept="image/jpeg, image/jpg, image/png" @change="UploadImage2()" id="file_input2"
                            multiple :clearable="false" style="display:none">
                          </v-file-input>
                          <v-col cols="12" md="12">
                            <v-row justify="center" align="center">
                              <v-col cols="12" md="12" align="center">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                  width="280.34"
                                  height="154.87"
                                  contain
                                ></v-img>
                              </v-col>
                              <v-col cols="12" md="12" style="text-align: center;">
                                <span style="line-height: 24px; font-weight: 400;"
                                  :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                                <span style="line-height: 24px; font-weight: 400;"
                                  :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                <span style="line-height: 16px; font-weight: 400;"
                                  :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">{{'(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)'}}</span><br />
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  <v-card
                    v-if="Detail2.product_image.length !== 0 "
                    elevation="0" width="100%" height="300px"
                    style="background: #FFFFFF; border-radius: 8px;"
                  >
                    <v-card-text>
                      <div class="mt-4">
                        <draggable v-model="Detail2.product_image" :move="onMove" @start="drag = true"
                          @end="drag = false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                          <v-row align="center" justify="center">
                          <v-col v-for="(item, index) in Detail2.product_image" :key="index" cols="12" md="6" sm="6">
                            <v-card outlined class="pa-1" width="350" height="260">
                              <v-img :src="item.url[0]" :lazy-src="item.url[0]" width="350" height="250" contain>
                                <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                  <v-icon x-small color="white" dark
                                    @click="RemoveImage2(index, item)">mdi-close</v-icon>
                                </v-btn>
                              </v-img>
                            </v-card>
                          </v-col>
                        </v-row>
                        </draggable>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
                    <v-col cols="12" md="12" class="mt-6 px-0">
                      <span class="subTitle pl-4">เพิ่มรูปภาพแบนเนอร์ร้านค้า</span>
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                <v-card-text>
                  <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                    @click="onPickFile()">
                    <v-card-text>
                      <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                        <v-file-input v-model="DataImage" :items="DataImage" accept="image/jpeg, image/jpg, image/png"
                          @change="UploadImage()" id="file_input" multiple :clearable="false" style="display:none">
                        </v-file-input>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="280.34"
                              height="154.87" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-6">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="5" style="text-align: center;">
                              <span
                                style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มรูปภาพของคุณที่นี่</span><br />
                              <span
                                style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG,
                                .PNG
                                เพิ่มได้สูงสุด 6 รูปภาพ)</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span
                                  style="color: red;">***</span> หมายเหตุ
                                ไฟล์รูปควรมีขนาดไม่เกิน 2 MB</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <!-- <span style="font-size: 12px; line-height: 16px; font-weight: 400;">
                    <span style="color: red;">***</span> หมายเหตุ รูปแรกจะเป็นรูปโปรไฟล์(Profile)
                  </span> -->
                  <div v-if="Detail.product_image.length !== 0" class="mt-4">
                    <draggable v-model="Detail.product_image" :move="onMove" @start="drag=true" @end="drag=false"
                      class="pl-5 pr-5 row  fill-height align-center sortable-list">
                      <v-col v-for="(item, index) in Detail.product_image" :key="index" cols="6" sm="4" md="2">
                        <v-card v-if="item.type === 'image'" outlined class="pa-1" width="146" height="146">
                          <v-img :src="item.path" :lazy-src="item.url" width="130" height="130" contain>
                            <v-btn icon x-small style="float: right; background-color: #ff5252;">
                              <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                            </v-btn>
                          </v-img>
                        </v-card>
                        <!-- <v-card v-else outlined  class="pa-1" width="146" height="146"  >
                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                        </v-btn>
                      <video    autoplay loop muted playsinline  >
                        <source :src="item.length ? item.path + '?=' + `${currentTime.getTime()}`:item.path" type="video/mp4" >
                      </video>
                    </v-card> -->
                      </v-col>
                    </draggable>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded width="125" height="40" class="white--text my-auto" color="#27AB9C" @click="saveData()">บันทึก</v-btn>
            </v-row>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      dragged: {
        from: -1,
        to: -1,
        newIndex: -1
      },
      setStaus: '',
      dragging: -1,
      selectIndex: null,
      openModalCreateGroupShop: false,
      upload_file_images: [],
      itemGroupName: [],
      DataImage: [],
      DataImage2: [],
      Detail2: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      Detail: {
        product_image: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      FormData: {
        group_name: '',
        group_shop_description: '',
        group_shop_short_description: '',
        group_seller_shop: [],
        group_seller_shop_id: [],
        group_shop_logo: ''
      },
      selectGroup: [],
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        emptyCheckbox: [v => !!v || '']
      }
    }
  },
  created () {
    // this.$EventBus.$on('modalCreateGroupShop', this.manageGroupStoreModal)
    this.getNameGroupStore()
    window.setInterval(async () => {
      await console.log('ทุกๆๆ 5 นาที')
    }, 50000)
  },
  watch: {
    selectionItem () {
      return this.selectGroup
    },
    isUpdating (val) {
      if (val) {
        setTimeout(() => (this.isUpdating = false), 3000)
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    dragOptionsChips () {
      return {
        animation: 200,
        group: 'group',
        disabled: false,
        ghostClass: 'ghost',
        sort: true
      }
    }
  },
  methods: {
    moveData (value) {
      this.dragged = {
        from: parseInt(value.from.id),
        to: parseInt(value.to.id),
        newIndex: value.draggedContext.futureIndex
      }
    },
    changeData (value) {
      if (value.removed) {
        // insert
        this.selectGroup.splice(this.dragged.to + this.dragged.newIndex, 0, this.selectGroup[this.dragged.from])
        // delete
        if (this.dragged.from < this.dragged.to) {
          this.selectGroup.splice(this.dragged.from, 1)
        } else {
          this.selectGroup.splice(this.dragged.from + 1, 1)
        }
      }
    },
    removeItemAt (index) {
      this.selectGroup.splice(index, 1)
    },
    dragStart (which, ev) {
      ev.dataTransfer.setData('Text', this.id)
      ev.dataTransfer.dropEffect = 'move'
      this.dragging = which
    },
    dragEnter (ev) {
      /*
      if (ev.clientY > ev.target.height / 2) {
        ev.target.style.marginBottom = '10px'
      } else {
        ev.target.style.marginTop = '10px'
      }
      */
    },
    dragLeave (ev) {
      /*
      ev.target.style.marginTop = '2px'
      ev.target.style.marginBottom = '2px'
      */
    },
    dragEnd (ev) {
      this.dragging = -1
    },
    dragFinish (to, ev) {
      // console.log('fgfg', this.dragging, to, ev)
      this.moveItem(this.dragging, to)
      ev.target.style.marginTop = '2px'
      ev.target.style.marginBottom = '2px'
    },
    moveItem (from, to) {
      if (to === -1) {
        this.removeItemAt(from)
      } else {
        var temp = this.selectGroup
        temp.splice(to, 0, this.selectGroup.splice(from, 1)[0])
        this.selectGroup = []
        this.selectGroup = temp
        this.FormData.group_seller_shop = []
        this.FormData.group_seller_shop = this.selectGroup
        // for (const c in this.Detail.product_image) {
        //   if (this.Detail.product_image[c].showStartDate !== null && this.Detail.product_image[c].showEndDate !== null) {
        //     this.selectTimes[c] = '2'
        //   } else {
        //     this.selectTimes[c] = '1'
        //   }
        // }
        // ('sdl2', this.selectGroup)
      }
    },
    checkLastest (index) {
      var a = this.length - 1
      if (index === a) {
        return false
      } else {
        return true
      }
    },
    async setIndex (index) {
      // console.log('cccindex', index)
      this.selectIndex = await index
      // console.log('cccindex', this.selectIndex, index)
    },
    setModel (item) {
      this.selectGroup[this.selectIndex] = item

      this.itemGroupName.forEach(e => { if (!this.selectGroup.map(x => { return x.seller_shop_id }).includes(e.seller_shop_id)) { e.disabled = false } else { e.disabled = true } })
      // console.log('cccc', item, this.selectIndex, this.selectGroup)
      // if (this.selectGroup.includes(item.seller_shop_id)) {

      // } else if (!this.selectGroup.includes(item.seller_shop_id)) {
      //   if () {
      //     this.selectGroup.push(item.seller_shop_id)
      //     item.disabled = true
      //   }
      // }
    },
    async getNameGroupStore () {
      // await this.$store.dispatch('actionsGraczSellerShopPage', data)
      // var res = await this.$store.state.ModuleGraczShop.stateGraczSellerShopPage
      // const data = {
      //   id: '1'
      // }
      await this.$store.dispatch('actionsGroupStoreName')
      var response = await this.$store.state.ModuleHompage.stateGroupStoreName
      // console.log('fffhhiiii', response)
      if (response.result === 'SUCCESS') {
        this.itemGroupName = await response.data.seller_shop_data.map(el => {
          return {
            ...el,
            disabled: false
          }
        })
        // this.addform()
      } else {
      }
    },
    addform () {
      const data = { image: '', name: '', group_id: null }
      this.FormData.group_seller_shop.push(data)
    },
    manageGroupStoreModal (status = null, item) {
      if (status !== null) {
        this.setStaus = status
        this.FormData.group_name = item.group_name
        this.FormData.group_shop_short_description = item.group_shop_short_description
        // this.FormData.group_shop_logo = item.group_shop_media_path
        const detail2 = {
          url: [item.group_shop_media_path]
        }
        this.Detail2.product_image.push(detail2)
        this.DataImage = item.group_shop_media_path
        const detail = {
          type: 'image',
          path: item.group_shop_banner_path,
          url: item.group_shop_banner_path
        }
        this.Detail.product_image.push(detail)
        this.FormData.group_shop_description = item.group_shop_description
        this.selectGroup = item.seller_shop_data
        // console.log('sdsd', item, status)
        this.openModalCreateGroupShop = true
      } else {
        this.openModalCreateGroupShop = true
      }
    },
    deleteGroupName (index) {
      this.FormData.group_seller_shop.splice(index, 1)
    },
    cancel () {
      this.openModalCreateGroupShop = false
    },
    async saveData () {
      // console.log('selectGroup', this.selectGroup)
      if (this.setStaus === 'openEditModal') {
      } else {
        var list = []
        var list2 = []
        for (let i = 0; i < this.Detail.product_image.length; i++) {
          if (this.Detail.product_image[i].name === 'default') {
            list.push({
              id: this.Detail.product_image[i].id
            })
          } else {
            list.push({
              id: '-1',
              image_data: this.Detail.product_image[i].image_data
            })
          }
        }
        for (let i = 0; i < this.Detail2.product_image.length; i++) {
          if (this.Detail2.product_image[i].name === 'default') {
            list2.push({
              id: this.Detail2.product_image[i].id
            })
          } else {
            list2.push({
              id: '-1',
              image_data: this.Detail2.product_image[i].image_data
            })
          }
        }
        // console.log('DFG', this.checkVal(this.FormData.group_name, 'name'))
        if (this.checkVal(this.FormData.group_name, 'name') || this.checkVal(list2[0].image_data, 'logo')) {
          var delVal = await {
            group_name: this.FormData.group_name,
            group_shop_description: this.FormData.group_shop_description,
            group_shop_short_description: this.FormData.group_shop_short_description,
            group_seller_shop_id: this.selectGroup,
            group_shop_logo: list2[0].image_data,
            group_shop_banner: list[0].image_data
          }
          // console.log('delVal', delVal)
          await this.$store.dispatch('actionsCreateGroupStoreName', delVal)
          var response = await this.$store.state.ModuleHompage.stateCreateGroupStoreName
          if (response.result === 'SUCCESS') {
            await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
            await this.$EventBus.$emit('callModalCreateGroupStore')
            this.openModalCreateGroupShop = await false
          } else {
          }
        }
      }
      // // console.log('FormData', response)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    onPickFile2 () {
      document.getElementById('file_input2').click()
    },
    checkVal (val, name) {
      if (name === 'name' && (val === '' || val === undefined || val === null)) {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ชื่อประเภทร้านค้าไม่สามารถว่างได้' })
        return false
      }
      if (name === 'logo' && (val === '' || val === undefined || val === null)) {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'โลโก้ประเภทร้านค้าไม่สามารถว่างได้' })
        return false
      }
    },
    async UploadImage2 () {
      for (let i = 0; i < this.DataImage2.length; i++) {
        const element = this.DataImage2[i]
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
          var url = URL.createObjectURL(element)
          const image = new Image()
          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => {
              const dimensions = {
                height: image.height,
                width: image.width
              }
              resolve(dimensions)
            }
            image.src = url
          })
          if (this.Detail2.product_image.length < 2) {
            if (i < 1) {
              if (imageDimensions.height <= 620 && imageDimensions.width <= 1480) {
                const reader = new FileReader()
                reader.readAsDataURL(element)
                reader.onload = () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  if (this.$route.query.Status !== 'Edit') {
                    this.upload_file_images = element // เอาค่าไฟล์มาเก็บเอาไว้เพื่อเอาไปใช้ในการส่ง api ตอนกด confirmCreate
                    this.Detail2.product_image.push({
                      image_data: resultReader.split(',')[1],
                      url: [url],
                      link: '',
                      name: this.DataImage2[i].name
                    })
                  } else {
                    this.upload_file_images = element // เอาค่าไฟล์มาเก็บเอาไว้เพื่อเอาไปใช้ในการส่ง api ตอนกด confirmCreate
                    this.Detail2.product_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImage2[i].name
                    })
                  }
                }
              } else {
                this.upload_file_images = []
                this.$swal.fire({ icon: 'warning', text: 'โปรดใช้รูปตามขนาดที่กำหนด', showConfirmButton: false, timer: 1500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
        }
      }
    },
    RemoveImage2 (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail2.product_image_delete.push(val.id)
        }
        this.Detail2.product_image.splice(index, 1)
      } else {
        this.Detail2.product_image.splice(index, 1)
      }
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.Detail.product_image.splice(index, 1)
      } else {
        this.Detail.product_image.splice(index, 1)
      }
      this.DataImage = []
    },
    UploadImage () {
      // console.log(this.DataImage, 'this.DataImage')
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      // this.Detail.product_image = []
      // console.log(this.Detail.product_image.length, 'aaaaa')
      if (this.Detail.product_image.length < 6) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          if (imageSize < 2) {
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = () => {
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (this.Detail.product_image.length < 6) {
                this.Detail.product_image.push({
                  image_data: resultReader.split(',')[1],
                  path: url,
                  name: this.DataImage[i].name,
                  type: (this.DataImage[i].type.split('/', 1)).toString()
                })
                // console.log(this.Detail.product_image.length, 'aaaaa2')
              } else {
                this.$swal.fire({
                  icon: 'warning',
                  text: 'กรุณาใส่รูปไม่เกิน 6 รูป',
                  showConfirmButton: false,
                  timer: 1500
                })
              }
              // console.log(this.Detail.product_image, 'this.Detail.product_image')
              // mediaType = this.DataImage[i].type
              // var checkType = mediaType.split('/', 1)
              // if (checkType.toString() === 'video') {
              //   checkType = 'vdo'
              // } else {
              //   checkType = 'image'
              // }
              // this.shop_media.push({
              //   media: element,
              //   media_type: checkType
              // })
              // console.log(this.shop_media, 'this.shop_media')
            }
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
              showConfirmButton: false,
              timer: 1500
            })
          }
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาใส่รูปไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    }
  }
}
</script>
