<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <!-- Modal เพิ่มตำแหน่ง -->
    <v-dialog v-model="modalAddPosition" :style="MobileSize ? 'z-index: 16000004' : ''" width="674px" persistent>
      <v-card width="100%">
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="CloseModalAddPosition()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-form ref="FormAddPosition" :lazy-validation="lazy">
          <v-container grid-list-xs>
            <v-card-text>
              <v-row  class="" v-if="!MobileSize">
                <v-col cols="12" md="2" sm="2">
                  <v-img
                    lazy-src="@/assets/Businessman.png"
                    max-height="100"
                    max-width="200"
                    src="@/assets/Businessman.png"
                    contain
                  ></v-img>
                </v-col>
                <v-col cols="12" md="10" sm="8" class="mt-10 pt-2" style="font-size: 16px;">
                  รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                </v-col>
              </v-row>
              <v-row v-else>
                <v-col cols="3" md="2">
                  <v-img
                    lazy-src="@/assets/Businessman.png"
                    max-height="100"
                    max-width="200"
                    src="@/assets/Businessman.png"
                  ></v-img>
                </v-col>
                <v-col cols="9" md="10" class="pt-4" style="font-size: 14px;">
                  รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                </v-col>
              </v-row>
              <v-row class="mt-2">
                <v-col cols="12" md="12" sm="2" xs="12" class="pt-2">
                  <span>ชื่อตำแหน่ง <span style="color: red;">*</span></span>
                </v-col>
              </v-row>
              <v-row dense no-gutters>
                <v-col cols="12" md="12" sm="12" xs="12">
                  <v-text-field placeholder="ระบุตำแหน่ง" id="PositionField" outlined dense v-model="position_name" @keydown.enter.prevent="submit" :rules="Rules.position_name" ></v-text-field>
                </v-col>
              </v-row>
              <v-row style="margin: -5px" dense no-gutters>
                <v-col cols="12">
                  <v-list>
                    <v-subheader :class="MobileSize ? 'px-0' : ''" style="font-weight: 400; font-size: 16px; line-height: 1px; color: #00000099;">เมนูร้านค้าที่คุณต้องการแสดง :</v-subheader>
                    <v-list-item-group
                      v-model="positionType"
                      multiple
                    >
                      <template v-for="(item, i) in itemMenuShop">
                        <v-list-item
                          :key="i"
                          :value="item.key"
                          v-model="item.checked"
                          :input-value="item.checked"
                          :class="MobileSize ? 'px-0' : ''"
                          class="max-v-list-height"
                          dense
                        >
                          <template v-slot:default="{ }">
                            <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 16px; line-height: 16px; color: #00000099;' : 'font-weight: 400; font-size: 16px; line-height: 16px; color: #00000099;'">
                              <!-- <v-list-item-title v-text="item.title"></v-list-item-title> -->
                              {{item.title}}
                            </v-list-item-content>

                            <v-list-item-action>
                              <v-checkbox
                                v-model="item.checked"
                                :input-value="item.checked"
                                return-o
                              ></v-checkbox>
                            </v-list-item-action>
                          </template>
                          </v-list-item>
                          </template>
                    </v-list-item-group>
                  </v-list>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="CloseModalAddPosition()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" :disabled="(position_name !== ' ' && position_name !== '') ? false : true" color="#27AB9C" @click="createPosition()">บันทึก</v-btn>
            </v-card-actions>
          </v-container>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- Modal แสดงข้อมูล -->
    <v-dialog v-model="modalShowPosition" width="674px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
      <v-card class=".rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px" v-if="!MobileSize">
            <font color="#27AB9C">รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</font>
          </span>
          <span class="flex text-center ml-5" style="font-size:14px" v-else>
            <font color="#27AB9C">รายละเอียดตำแหน่งและสิทธิ์การใช้งาน</font>
          </span>
          <v-btn icon dark @click="modalShowPosition = !modalShowPosition">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="mb-15">
          <v-form ref="FormAddPosition" :lazy-validation="lazy">
          <v-row class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar color="#F2F2F2" rounded icon size="70">
                <!-- <v-img src="@/assets/Create_Store/industry2.png"></v-img> -->
                <v-img contain :src="require('@/assets/icons/use.jpg')"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="8" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p class="mt-5" style="font-weight: 400; font-size: 20px; text-transform: #333333;" v-if="!MobileSize">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p class="mt-5" style="font-weight: 400; font-size: 14px; text-transform: #333333;" v-else>
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-card class="rounded-lg"  elevation="0" style="border: 1px solid #E6E6E6;">
                <v-row>
                  <v-col cols="12" md="12" sm="12" xs="12">
                    <v-row>
                      <v-col cols="12">
                        <v-container>
                          <v-row no-gutters>
                            <v-col cols="10" md="10">
                              <p class="mt-0"
                                style="font-weight: 400; font-size: 16px; line-height: 20px; color: #333333;">
                                ตำแหน่ง : {{ position_NameUser }}
                              </p>
                            </v-col>
                            <v-col cols="2" md="2" align="right">
                              <div class="mr-3" style="margin-Top: -8px">
                                <v-btn v-if="openStatus === 'active'" @click="ShowEditPosition()" icon dense>
                                  <v-avatar rounded size="18">
                                    <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                                  </v-avatar>
                                </v-btn>
                              </div>
                            </v-col>
                            <v-col cols="12" md="12">
                              <p class="mt-0"
                                style="font-weight: 400; font-size: 16px; line-height: 8px; color: #27AB9C;">
                                สิทธิ์การใช้งาน
                              </p>
                            </v-col>
                            <v-col v-for="(item, index) in itemMenuShop2" :key="index" cols="12" md="12">
                              <v-row dense>
                                <v-col cols="10" md="11">
                                  <v-list-item-content
                                    :style="!MobileSize ? 'font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;' : 'font-weight: 400; font-size: 16px; line-height: 16px; color:: #333333; padding-top: 8px'">
                                    {{item.title}}
                                  </v-list-item-content>
                                </v-col>
                                <v-col cols="2" md="1">
                                  <v-checkbox readonly v-model="item.checked" style="margin-Top: -1px" dense
                                    color="#27AB9C" hide-details class="shrink"></v-checkbox>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-container>
                      </v-col>
                      <!-- <v-col cols="2">
                        <div class="mt-4">
                          <v-btn @click="EditPosition()" icon dense>
                            <v-avatar rounded size="18">
                              <v-img contain :src="require('@/assets/icons/Union.png')"></v-img>
                            </v-avatar>
                          </v-btn>
                        </div>
                      </v-col> -->
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" xs="12" align="right" class="pr-8">
                    <v-row no-gutters>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <div disable class=" mb-6 ml-2" style="border-bottom: #E6E6E6 4px dashed;"></div>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <span style="font-weight: 500; font-size: 12px; line-height: 1px; color: #333333;">สร้างเมื่อ
                          :  {{ new Date(created_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' }) }}</span>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" xs="12">
                        <span style="font-weight: 500; font-size: 12px; line-height: 1px; color: #333333;">แก้ไขเมื่อ
                          : {{ new Date(update_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' }) }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-container style="display: flex; justify-content: flex-end">
            <v-btn dense dark color="success" class="pl-7 pr-7 mt-2" @click="activePosition('active', positionId, typeId)" v-if="openStatus === 'inactive'">
              เปิดใช้งาน
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="modalShowPosition = !modalShowPosition">
              ย้อนกลับ
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Modal แก้ไขตำแหน่ง -->
    <v-dialog v-model="modalEditPosition" width="600px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
      <v-card class=".rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px" v-if="!MobileSize">
            <font color="#27AB9C">แก้ไขตำแหน่งและสิทธิ์การใช้งาน</font>
          </span>
          <span class="flex text-center ml-5" style="font-size:14px" v-else>
            <font color="#27AB9C">แก้ไขตำแหน่งและสิทธิ์การใช้งาน</font>
          </span>
          <v-btn icon dark @click="modalEditPosition = !modalEditPosition">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-form ref="FormAddPosition" :lazy-validation="lazy" :class="MobileSize ? '' : 'mt-5'">
          <v-row no-gutters class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar rounded size="72">
                <v-img src="@/assets/Businessman.png"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p class="mt-5" style="font-weight: 400; font-size: 20px; text-transform: #333333;" v-if="!MobileSize">
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p class="mt-5 pl-2" style="font-weight: 400; font-size: 16px; text-transform: #333333;" v-else>
                    รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col class="mt-8" cols="12" md="12">
              <p style="font-weight: 400; font-size: 16px; line-height: 1px; color:: #333333;">
                ชื่อตำแหน่ง
              </p>
              <v-text-field placeholder="ระบุตำแหน่ง" v-model="position_NameUser" @keydown.enter.prevent="submit"  :rules="Rules.position_name" dense outlined></v-text-field>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12" dense>
              <v-row dense v-for="(item, i) in itemMenuShopAll[0]" :key="i">
                <v-list-item
                  :value="item.key"
                  :class="MobileSize ? 'px-0' : ''"
                  class="max-v-list-height"
                  dense
                >
                <template v-slot:default="{ active }">
                  <v-list-item-content :style="MobileSize ? 'font-weight: 400; font-size: 15px; line-height: 16px; color: #00000099;' : 'font-weight: 400; font-size: 15px; line-height: 1px; color: #00000099;'">
                    <!-- <v-list-item-title  v-text="item.title"></v-list-item-title> -->
                    {{ item.title }}
                  </v-list-item-content>

                  <v-list-item-action>
                    <v-checkbox
                      :input-value="active"
                      v-model="item.checked"
                      :v-model="active"
                      style="margin-Top: -1px" dense color="#27AB9C" hide-details
                    class="shrink"
                    ></v-checkbox>
                  </v-list-item-action>
                  </template>
                </v-list-item>
              </v-row>
            </v-col>
          </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-container style="display: flex; justify-content: flex-end">
            <v-btn dense dark color="error" class="pl-7 pr-7 mt-2" @click="cancelPosition('inactive')">
              ยกเลิกการใช้งาน
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="editForm()">
              บันทึก
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Modal ยืนยันเปิดการใช้งานมอบอำนาจ -->
    <v-dialog v-model="ModalConfirmAutoApprove" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CancelApproveAuto(TextSwitch)"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{TextSwitch}}การใช้งานมอบอำนาจ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CancelApproveAuto(TextSwitch)">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ApproveAuto(TextSwitch)">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-row dense justify="center">
      <v-col cols="12" class="px-0 py-0">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense>
            <v-col cols="12" md="6" sm="6">
              <v-card-title class="pb-0" style="font-weight: bold; font-size:24px; line-height: 32px;" v-if="!MobileSize">จัดการตำแหน่งและสิทธิ์การใช้งาน</v-card-title>
              <v-card-title class="px-0" style="font-weight: bold; font-size: 18px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> จัดการตำแหน่งและสิทธิ์การใช้งาน</v-card-title>
            </v-col>
            <v-col cols="12" md="6" sm="6" class="d-flex justify-end">
              <v-switch class="pr-4" false-value="no" true-value="yes" inset v-model="ActiveAttorney" :label="ActiveAttorney === 'yes' ? 'เปิดการใช้งานมอบอำนาจ' : 'ปิดการใช้งานมอบอำนาจ'" readonly @click="SwitchActiver(ActiveAttorney)"></v-switch>
            </v-col>
          </v-row>
          <v-card-text>
            <v-row dense no-gutters class="mb-2">
              <v-col cols="12" class="py-0 mb-0">
                <a-tabs @change="SelectDetailPosition">
                  <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                  <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countAll }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="1"><span slot="tab">ใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{ countActivePosition }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{ countInactivePosition }}</a-tag></span></a-tab-pane>
                </a-tabs>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="6" sm="12" xs="12" class="mb-0 mt-0 py-0">
                <v-text-field
                v-model="search"
                dense
                outlined
                placeholder="ค้นหาจากตำแหน่ง"
                >
                  <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="6" sm="12" xs="12" align="end" class="py-0" v-if="!IpadSize">
                <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="openModalAddPosition()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
              <v-col v-if="disableTable === true" cols="12" md="12" sm="8" :class="MobileSize ? 'pl-3 pt-2' : IpadSize ? 'pl-3 pt-1' :'pl-4 pt-0'">
                <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 16px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="StateStatus === 0">แสดงตำแหน่งในร้านค้าทั้งหมด {{ showCountOrder }} รายการ</span>
                <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 16px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 1">แสดงตำแหน่งในร้านค้าที่ใช้งานทั้งหมด {{ showCountOrder }} รายการ</span>
                <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 16px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 2">แสดงตำแหน่งในร้านค้าที่ยกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
              </v-col>
              <v-col cols="12" md="6" sm="4" xs="12" align="end" class="py-0" v-if="IpadSize">
                <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="openModalAddPosition()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
              <v-col cols="12" md="12" sm="12" xs="12" v-if="disableTable === true">
                <v-card outlined class="mb-4">
                  <v-data-table
                  :headers="keyCheckHead == 0 ? headersAll : keyCheckHead == 1 ? headersActivePosition : headersInactivePosition"
                  :items="DataTable"
                  :items-per-page="10"
                  :page.sync="page"
                  :search="search"
                  @pagination="countCompany"
                  no-results-text="ไม่พบตำแหน่งที่ค้นหา"
                  no-data-text="ไม่มีตำแหน่งในตาราง"
                  class=""
                  :update:items-per-page="getItemPerPage"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  >
                  <template v-slot:[`item.position_name`]="{ item:{ position_name } = {} }">
                    {{ position_name }}
                  </template>
                  <template v-slot:[`item.created_at`]="{ item:{ created_at } = {} }">
                    {{ new Date(created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}
                  </template>
                  <template v-slot:[`item.status`]="{ item:{ status } = {} }">
                    <span v-if="status === 'active'">
                      <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                    </span>
                    <span v-else-if="status === 'inactive'">
                      <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f50">ยกเลิก</v-chip>
                    </span>
                  </template>
                  <template v-slot:[`item.actions`]="{ item }">
                    <v-row dense justify="center">
                      <span
                        outlined
                        color="#27AB9C"
                        @click="ShowDetail(item)"
                        cclass="pt-4 pb-4"
                        style="cursor: pointer; color: #27AB9C;"
                      >
                      <v-icon color="#27AB9C" class="">mdi-file-document-outline</v-icon>
                        รายละเอียด <v-icon style="color: #27AB9C;">mdi-chevron-right</v-icon>
                      </span>
                    </v-row>
                  </template>
                  </v-data-table>
                </v-card>
                <!-- <div class="text-center pt-2">
                  <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
                </div> -->
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" v-if="disableTable === false">
              <!-- <v-col cols="12" class="py-0">
                <a-tabs @change="SelectDetailPosition">
                  <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{
                  countAll }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="1"><span slot="tab">กำลังใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{
                  countActivePosition }}</a-tag></span></a-tab-pane>
                  <a-tab-pane :key="2"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{
                  countInactivePosition }}</a-tag></span></a-tab-pane>
                </a-tabs>
              </v-col> -->
              <v-col cols="12" md="12" align="center">
                <div class="my-5">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px"
                    max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0">
                  <span
                    style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีการเพิ่มตำแหน่ง</span><br />
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span
                      style="font-size: 28px;">“เพิ่มข้อมูล”</span> เพื่อเพิ่มตำแหน่งที่สมบูรณ์ของคุณ</span>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีตำแหน่งในร้านค้าที่ใช้งาน</span><br />
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีตำแหน่งในร้านค้าที่ยกเลิก</span><br />
                </h2>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      ActiveAttorney: 'no',
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      search: '',
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: true,
      keyCheckHead: 0,
      countAll: 0,
      countActivePosition: 0,
      countInactivePosition: 0,
      DataTable: [],
      lazy: false,
      companyData: {},
      seller_shop_id: '',
      pathEditCompany: '',
      modalAddPosition: false,
      modalEditPosition: false,
      modalShowPosition: false,
      checkboxPosition: [],
      position_name: '',
      positionType: [],
      listData: [],
      selectType: [],
      selectType3: [],
      itemMenuShop: [],
      itemMenuShop2: [],
      itemMenuShop3: [],
      Rules: {
        position_name: [
          v => !!v || 'กรุณากรอกตำแหน่ง',
          v => v.charAt(0) !== ' ' || 'กรุณากรอกตำแหน่ง'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่',
          v => (/^[-0-9/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => ((/^[0-9/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ]
      },
      headersAll: [
        { text: 'ตำแหน่ง', value: 'position_name', sortable: false, width: '170', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'created_at', filterable: false, sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', value: 'actions', filterable: false, width: '180', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersActivePosition: [
        { text: 'ตำแหน่ง', value: 'position_name', sortable: false, width: '170', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'created_at', filterable: false, sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', value: 'actions', filterable: false, width: '180', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersInactivePosition: [
        { text: 'ตำแหน่ง', value: 'position_name', sortable: false, width: '170', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่สร้างข้อมูล', value: 'created_at', filterable: false, sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การจัดการ', value: 'actions', filterable: false, width: '180', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      positionId: '',
      typeId: '',
      setCompany: '0',
      setPermission: '0',
      Partner: '0',
      Order: '0',
      approveOrder: '0',
      Payment: '0',
      Report: '0',
      Tracking: '0',
      Refund: '0',
      Review: '0',
      SaleOrder: '0',
      ApproveOrerSale: '0',
      ManageSaleOrder: '0',
      ManagePromotion: '0',
      ManageAffiliate: '0',
      ManagePartner: '0',
      ManageAccountBank: '0',
      sumOne: [],
      sumZero: [],
      created_date: '',
      update_date: '',
      position_NameUser: '',
      position_ID: '',
      itemMenuShopAll: [],
      seller_id: '',
      num: 0,
      openStatus: '',
      list_seller: [],
      checkedAffiliate: '0',
      ModalConfirmAutoApprove: false,
      TextSwitch: ''
    }
  },
  async created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNav')
    // if (localStorage.getItem('CompanyData') !== null) {
    // this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
    // console.log('companyData***', this.companyData)
    //
    // this.init()
    await this.getConsent()
  },
  mounted () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    position_name (val) {
      if (val.charAt(0) === '') {
        var inputField = document.getElementById('PositionField')
        inputField.addEventListener('keydown', (event) => {
          if (event.keyCode === 32 && event.target.selectionStart === 0) {
            event.preventDefault()
          }
        })
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listShopPositionMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listShopPosition' }).catch(() => {})
      }
    },
    StateStatus (val) {
    // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.listData.all
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.listData.list_position_active
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.listData.list_position_inactive
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    CancelApproveAuto (item) {
      if (item === 'เปิด') {
        this.ActiveAttorney = 'no'
        this.ModalConfirmAutoApprove = false
      } else {
        this.ActiveAttorney = 'yes'
        this.ModalConfirmAutoApprove = false
      }
    },
    async ApproveAuto (item) {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      if (item === 'เปิด') {
        this.ActiveAttorney = 'yes'
      } else {
        this.ActiveAttorney = 'no'
      }
      var data = {
        seller_shop_id: shopId,
        status: this.ActiveAttorney
      }
      await this.$store.dispatch('actionsSetActiveAttorney', data)
      var res = await this.$store.state.ModuleShop.stateSetActiveAttorney
      if (res.ok === 'y') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          html: `<h3>${this.TextSwitch}การใช้งานมอบอำนาจสำเร็จ</้>`
        })
        this.ModalConfirmAutoApprove = false
        this.getListShop()
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: 'เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง'
        })
        this.ActiveAttorney = this.ActiveAttorney === 'yes' ? 'no' : 'yes'
        this.ModalConfirmAutoApprove = false
        this.$store.commit('closeLoader')
      }
    },
    SwitchActiver (item) {
      this.ModalConfirmAutoApprove = true
      this.TextSwitch = item === 'yes' ? 'ปิด' : 'เปิด'
    },
    async getConsent () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (res.isSeller === '1') {
        this.checkedAffiliate = res.isSeller
      }
      await this.getListShop()
      this.$store.commit('closeLoader')
    },
    submit () {
    },
    async activePosition (status, positionID, TypeID) {
      var text = ''
      var btnColor = ''
      text = 'ยืนยันการเปิดการใช้งาน ใช่หรือไม่?'
      btnColor = '#1ab759'
      var data = {
        position_id: positionID,
        position_status: status,
        type: 'shop',
        type_id: TypeID
      }
      await this.$swal.fire({
        text: `${text}`,
        type: 'warning',
        // buttons: {
        //   confirm: 'ยืนยัน',
        //   cancel: 'ย้อนกลับ'
        // }
        showCancelButton: true,
        confirmButtonColor: `${btnColor}`,
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ย้อนกลับ',
        closeOnConfirm: false,
        closeOnCancel: false,
        reverseButtons: true
      }).then(async (result) => {
        if (result.isConfirmed) {
          await this.$store.dispatch('actionUpdatePositionStatus', data)
          var dataResponse = await this.$store.state.ModuleShop.stateUpdatePositionStatus
          if (dataResponse.result === 'SUCCESS') {
            this.getListShop()
            this.$swal.fire(
              {
                icon: 'success',
                html: '<h3>ยืนยันการเปิดใช้งานสำเร็จ</h3>',
                showConfirmButton: false,
                timer: 1500
              })
            this.modalShowPosition = false
          } else {
            this.getListShop()
            this.$swal.fire(
              {
                icon: 'error',
                html: '<h3>ยืนยันการเปิดการใช้งานไม่สำเร็จ</h3>',
                showConfirmButton: false,
                timer: 1500
              })
            this.modalShowPosition = false
          }
        } else {
          this.modalShowPosition = false
        }
      })
    },
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async cancelPosition (status) {
      var text = ''
      var btnColor = ''
      if (status === 'active') {
        text = 'ยืนยันการเปิดการใช้งาน ใช่หรือไม่?'
        btnColor = '#1ab759'
      } else {
        text = 'ยืนยันการยกเลิกการใช้งาน ใช่หรือไม่?'
        btnColor = '#f5222d'
      }
      var datas = await {
        position_id: this.positionId,
        position_status: status,
        type: 'shop',
        type_id: this.typeId
      }
      await this.$swal.fire({
        text: `${text}`,
        type: 'warning',
        // buttons: {
        //   confirm: 'ยืนยัน',
        //   cancel: 'ย้อนกลับ'
        // }
        showCancelButton: true,
        confirmButtonColor: `${btnColor}`,
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ย้อนกลับ',
        closeOnConfirm: false,
        closeOnCancel: false,
        reverseButtons: true
      }).then(async (result) => {
        if (result.isConfirmed) {
          await this.$store.dispatch('actionUpdatePositionStatus', datas)
          var data = await this.$store.state.ModuleShop.stateUpdatePositionStatus
          if (data.result === 'SUCCESS') {
            this.getListShop()
            if (status === 'active') {
              this.$swal.fire(
                {
                  icon: 'success',
                  text: 'ยืนยันการเปิดใช้งานสำเร็จ',
                  showConfirmButton: false,
                  timer: 1500
                })
              this.modalShowPosition = false
            } else {
              this.$swal.fire(
                {
                  icon: 'success',
                  text: 'ยืนยันการยกเลิกการใช้งานสำเร็จ',
                  showConfirmButton: false,
                  timer: 1500
                })
              this.modalEditPosition = false
            }
          } else {
            this.getListShop()
            this.$swal.fire(
              {
                icon: 'error',
                text: 'ยืนยันการยกเลิกการใช้งานไม่สำเร็จ',
                showConfirmButton: false,
                timer: 1500
              })
            this.modalEditPosition = false
          }
        } else {
          this.modalEditPosition = false
          this.modalShowPosition = false
        }
      })
    },
    async init () {
      // console.log('init')
      const dataSent = {
        company_id: this.companyData.id
      }
      await this.$store.dispatch('actionslistPositionOfCompany', dataSent)
      var { data: { all = [] } = {} } = await this.$store.state.ModuleAdminManage.statelistPositionOfCompany
      this.DataTable = all
      this.disableTable = true
      // console.log('listPositionOfCompany', all)
    },
    CloseModalAddPosition () {
      this.$refs.FormAddPosition.reset()
      this.modalAddPosition = !this.modalAddPosition
    },
    async getListShop () {
      this.itemMenuShop = []
      this.countAll = 0
      this.countActivePosition = 0
      this.countInactivePosition = 0
      const dataSent = await {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionListPositionInShop', dataSent)
      var response = await this.$store.state.ModuleShop.stateListPositionInShop
      // console.log('888 Res', response)
      if (response.result === 'SUCCESS') {
        this.listData = await response.data
        this.ActiveAttorney = await response.data.use_Attorney
        if (this.ActiveAttorney === 'yes') {
          this.itemMenuShop = [
            { key: 1, title: 'การจัดการข้อมูลของสินค้า', manage_product: '0', checked: false },
            { key: 2, title: 'การจัดการการตั้งค่าภายในร้านค้า', manage_setting_shop: '0', checked: false },
            { key: 3, title: 'การจัดการสต๊อกของสินค้า', manage_stock: '0', checked: false },
            { key: 4, title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน', manage_order: '0', checked: false },
            { key: 5, title: 'การจัดการแดชบอร์ดของร้านค้า', manage_dashboard: '0', checked: false },
            { key: 6, title: 'การจัดการรายได้ (ใช้หนังสือมอบอำนาจ)', manage_income: '0', checked: false },
            { key: 7, title: 'การจัดการสิทธิ์และตำแหน่ง', manage_user_with_position: '0', checked: false },
            { key: 8, title: 'การจัดการการคืนสินค้า', manage_product_refund: '0', checked: false },
            { key: 9, title: 'การติดตามสินค้าของทางร้าน', manage_tracking: '0', checked: false },
            { key: 10, title: 'การจัดการขนส่งสินค้า', manage_shipping: '0', checked: false },
            { key: 11, title: 'การจัดการใบสั่งขาย (ใช้หนังสือมอบอำนาจ)', sale_order: '0', checked: false },
            { key: 12, title: 'การจัดการอนุมัติใบสั่งขาย', manage_approve_order: '0', checked: false },
            { key: 13, title: 'การจัดการรายการฝ่ายขาย', manage_sale_order: '0', checked: false },
            { key: 14, title: 'การจัดการโปรโมชันร้านค้า', manage_promotion: '0', checked: false }
          ]
        } else {
          this.itemMenuShop = [
            { key: 1, title: 'การจัดการข้อมูลของสินค้า', manage_product: '0', checked: false },
            { key: 2, title: 'การจัดการการตั้งค่าภายในร้านค้า', manage_setting_shop: '0', checked: false },
            { key: 3, title: 'การจัดการสต๊อกของสินค้า', manage_stock: '0', checked: false },
            { key: 4, title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน', manage_order: '0', checked: false },
            { key: 5, title: 'การจัดการแดชบอร์ดของร้านค้า', manage_dashboard: '0', checked: false },
            { key: 6, title: 'การจัดการรายได้', manage_income: '0', checked: false },
            { key: 7, title: 'การจัดการสิทธิ์และตำแหน่ง', manage_user_with_position: '0', checked: false },
            { key: 8, title: 'การจัดการการคืนสินค้า', manage_product_refund: '0', checked: false },
            { key: 9, title: 'การติดตามสินค้าของทางร้าน', manage_tracking: '0', checked: false },
            { key: 10, title: 'การจัดการขนส่งสินค้า', manage_shipping: '0', checked: false },
            { key: 11, title: 'การจัดการใบสั่งขาย', sale_order: '0', checked: false },
            { key: 12, title: 'การจัดการอนุมัติใบสั่งขาย', manage_approve_order: '0', checked: false },
            { key: 13, title: 'การจัดการรายการฝ่ายขาย', manage_sale_order: '0', checked: false },
            { key: 14, title: 'การจัดการโปรโมชันร้านค้า', manage_promotion: '0', checked: false }
          ]
        }
        if (this.checkedAffiliate === '1') {
          this.itemMenuShop.push({
            key: 15,
            title: 'การจัดการโปรแกรม Affiliate',
            manage_promotion: '0',
            checked: false
          })
        }
        this.itemMenuShop.push(
          {
            key: 16,
            title: 'จัดการโปรแกรม partner',
            manage_partner: '0',
            checked: false
          }
        )
        this.itemMenuShop.push(
          {
            key: 17,
            title: 'จัดการบัญชีร้านค้า',
            manage_account_bank: '0',
            checked: false
          }
        )
        // console.log('listData', this.listData, this.StateStatus)
        this.companyData = await response.data
        this.countAll = await response.data.all.length
        this.countActivePosition = await response.data.count_position_active
        this.countInactivePosition = await response.data.count_position_inactive
        // console.log(this.countAll)
        if (this.StateStatus === 0) {
          this.DataTable = response.data.all
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.countAll = this.DataTable.length
            this.disableTable = true
          }
        } else if (this.StateStatus === 1) {
          this.DataTable = response.data.list_position_active
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 2) {
          this.DataTable = response.data.list_position_inactive
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.countInactivePosition = this.DataTable.length
            this.disableTable = true
          }
          // this.DataTable = response.data.list_position_inactive
          // this.countInactivePosition = this.DataTable.length
          // this.disableTable = false
        }
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ไม่สามารถทำรายการได้</h3>' })
        }
      }
    },
    async ShowDetail (val) {
      this.itemMenuShopAll = []
      this.itemMenuShop2 = []
      this.itemMenuShop3 = []
      this.openStatus = []
      const dataSent = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        position_id: val.id
      }
      await this.$store.dispatch('actionGetPositionDetailInShop', dataSent)
      var { data = [], result = '', message } = await this.$store.state.ModuleShop.stateGetPositionDetailInShop
      if (result === 'SUCCESS') {
        this.created_date = data[0].created_at
        this.update_date = data[0].updated_at
        this.position_NameUser = data[0].position_name
        this.position_ID = data[0].id
        this.positionId = data[0].id
        this.typeId = dataSent.seller_shop_id
        this.openStatus = data[0].status
        for (const x in data) {
          if (data[x].manage_product === '1') {
            const dataManageProduct = {
              key: 1,
              title: 'การจัดการข้อมูลของสินค้า',
              manage_product: data[x].manage_product,
              checked: true
            }
            this.itemMenuShop2.push(dataManageProduct)
          } else {
            const dataManageProduct = {
              key: 1,
              title: 'การจัดการข้อมูลของสินค้า',
              set_company: data[x].manage_product,
              checked: false
            }
            this.itemMenuShop3.push(dataManageProduct)
          }
          if (data[x].manage_setting_shop === '1') {
            const dataManageSettingShop = {
              key: 2,
              title: 'การจัดการการตั้งค่าภายในร้านค้า',
              manage_setting_shop: data[x].manage_setting_shop,
              checked: true
            }
            this.itemMenuShop2.push(dataManageSettingShop)
          } else {
            const dataManageSettingShop = {
              key: 2,
              title: 'การจัดการการตั้งค่าภายในร้านค้า',
              manage_setting_shop: data[x].manage_setting_shop,
              checked: false
            }
            this.itemMenuShop3.push(dataManageSettingShop)
          }
          if (data[x].manage_stock === '1') {
            const dataManageStock = {
              key: 3,
              title: 'การจัดการสต็อกของสินค้า',
              manage_stock: data[x].manage_stock,
              checked: true
            }
            this.itemMenuShop2.push(dataManageStock)
          } else {
            const dataManageStock = {
              key: 3,
              title: 'การจัดการสต็อกของสินค้า',
              manage_stock: data[x].manage_stock,
              checked: false
            }
            this.itemMenuShop3.push(dataManageStock)
          }
          if (data[x].manage_order === '1') {
            const dataManageOrder = {
              key: 4,
              title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน',
              manage_order: data[x].manage_order,
              checked: true
            }
            this.itemMenuShop2.push(dataManageOrder)
          } else {
            const dataManageOrder = {
              key: 4,
              title: 'การจัดการออเดอร์ที่มีการเข้ามาภายในร้าน',
              manage_order: data[x].manage_order,
              checked: false
            }
            this.itemMenuShop3.push(dataManageOrder)
          }
          if (data[x].manage_dashboard === '1') {
            const dataManageDashboard = {
              key: 5,
              title: 'การจัดการแดชบอร์ดของร้านค้า',
              manage_dashboard: data[x].manage_dashboard,
              checked: true
            }
            this.itemMenuShop2.push(dataManageDashboard)
          } else {
            const dataManageDashboard = {
              key: 5,
              title: 'การจัดการแดชบอร์ดของร้านค้า',
              manage_dashboard: data[x].manage_dashboard,
              checked: false
            }
            this.itemMenuShop3.push(dataManageDashboard)
          }
          if (data[x].manage_income === '1') {
            const dataManageIncome = {
              key: 6,
              title: this.ActiveAttorney === 'no' ? 'การจัดการรายได้' : 'การจัดการรายได้ (ใช้หนังสือมอบอำนาจ)',
              manage_income: data[x].manage_income,
              checked: true
            }
            this.itemMenuShop2.push(dataManageIncome)
          } else {
            const dataManageIncome = {
              key: 6,
              title: this.ActiveAttorney === 'no' ? 'การจัดการรายได้' : 'การจัดการรายได้ (ใช้หนังสือมอบอำนาจ)',
              manage_income: data[x].manage_income,
              checked: false
            }
            this.itemMenuShop3.push(dataManageIncome)
          }
          if (data[x].manage_user_with_position === '1') {
            const datamanageUserWithPosition = {
              key: 7,
              title: 'การจัดการสิทธิ์และตำแหน่ง',
              manage_user_with_position: data[x].manage_user_with_position,
              checked: true
            }
            this.itemMenuShop2.push(datamanageUserWithPosition)
          } else {
            const datamanageUserWithPosition = {
              key: 7,
              title: 'การจัดการสิทธิ์และตำแหน่ง',
              manage_user_with_position: data[x].manage_user_with_position,
              checked: false
            }
            this.itemMenuShop3.push(datamanageUserWithPosition)
          }
          if (data[x].manage_product_refund === '1') {
            const dataManageProductRefund = {
              key: 8,
              title: 'การจัดการการคืนสินค้า',
              manage_product_refund: data[x].manage_product_refund,
              checked: true
            }
            this.itemMenuShop2.push(dataManageProductRefund)
          } else {
            const dataManageProductRefund = {
              key: 8,
              title: 'การจัดการการคืนสินค้า',
              manage_product_refund: data[x].manage_product_refund,
              checked: false
            }
            this.itemMenuShop3.push(dataManageProductRefund)
          }
          if (data[x].manage_tracking === '1') {
            const dataManageTracking = {
              key: 9,
              title: 'การติดตามสินค้าของทางร้าน',
              manage_tracking: data[x].manage_tracking,
              checked: true
            }
            this.itemMenuShop2.push(dataManageTracking)
          } else {
            const dataManageTracking = {
              key: 9,
              title: 'การติดตามสินค้าของทางร้าน',
              manage_tracking: data[x].manage_tracking,
              checked: false
            }
            this.itemMenuShop3.push(dataManageTracking)
          }
          if (data[x].manage_shipping === '1') {
            const datamanageShipping = {
              key: 10,
              title: 'การจัดการขนส่งสินค้า',
              manage_shipping: data[x].manage_shipping,
              checked: true
            }
            this.itemMenuShop2.push(datamanageShipping)
          } else {
            const datamanageShipping = {
              key: 10,
              title: 'การจัดการขนส่งสินค้า',
              manage_shipping: data[x].manage_shipping,
              checked: false
            }
            this.itemMenuShop3.push(datamanageShipping)
          }
          if (data[x].sale_order === '1') {
            const dataSaleOrder = {
              key: 11,
              title: this.ActiveAttorney === 'no' ? 'การจัดการใบสั่งขาย' : 'การจัดการใบสั่งขาย (ใช้หนังสือมอบอำนาจ)',
              manage_shipping: data[x].sale_order,
              checked: true
            }
            this.itemMenuShop2.push(dataSaleOrder)
          } else {
            const dataSaleOrder = {
              key: 11,
              title: this.ActiveAttorney === 'no' ? 'การจัดการใบสั่งขาย' : 'การจัดการใบสั่งขาย (ใช้หนังสือมอบอำนาจ)',
              manage_shipping: data[x].sale_order,
              checked: false
            }
            this.itemMenuShop3.push(dataSaleOrder)
          }
          if (data[x].manage_approve_order === '1') {
            const datamanageApproveOrder = {
              key: 12,
              title: 'การจัดการอนุมัติใบสั่งขาย',
              manage_shipping: data[x].manage_approve_order,
              checked: true
            }
            this.itemMenuShop2.push(datamanageApproveOrder)
          } else {
            const datamanageApproveOrder = {
              key: 12,
              title: 'การจัดการอนุมัติใบสั่งขาย',
              manage_shipping: data[x].manage_approve_order,
              checked: false
            }
            this.itemMenuShop3.push(datamanageApproveOrder)
          }
          if (data[x].manage_sale_order === '1') {
            const datamanageApproveOrder = {
              key: 13,
              title: 'การจัดการรายการฝ่ายขาย',
              manage_shipping: data[x].manage_sale_order,
              checked: true
            }
            this.itemMenuShop2.push(datamanageApproveOrder)
          } else {
            const datamanageApproveOrder = {
              key: 13,
              title: 'การจัดการรายการฝ่ายขาย',
              manage_shipping: data[x].manage_sale_order,
              checked: false
            }
            this.itemMenuShop3.push(datamanageApproveOrder)
          }
          if (data[x].manage_promotion === '1') {
            const datamanagePromotion = {
              key: 14,
              title: 'การจัดการโปรโมชันร้านค้า',
              manage_shipping: data[x].manage_promotion,
              checked: true
            }
            this.itemMenuShop2.push(datamanagePromotion)
          } else {
            const datamanagePromotion = {
              key: 14,
              title: 'การจัดการโปรโมชันร้านค้า',
              manage_shipping: data[x].manage_promotion,
              checked: false
            }
            this.itemMenuShop3.push(datamanagePromotion)
          }
          if (data[x].manage_affiliate === '1') {
            const datamanageAffiliate = {
              key: 15,
              title: 'การจัดการโปรแกรม Affiliate',
              manage_shipping: data[x].manage_affiliate,
              checked: true
            }
            this.itemMenuShop2.push(datamanageAffiliate)
          } else {
            const datamanageAffiliate = {
              key: 15,
              title: 'การจัดการโปรแกรม Affiliate',
              manage_shipping: data[x].manage_affiliate,
              checked: false
            }
            this.itemMenuShop3.push(datamanageAffiliate)
          }
          if (data[x].manage_partner === '1') {
            const datamanagePartner = {
              key: 16,
              title: 'จัดการโปรแกรม partner',
              manage_shipping: data[x].manage_partner,
              checked: true
            }
            this.itemMenuShop2.push(datamanagePartner)
          } else {
            const datamanagePartner = {
              key: 16,
              title: 'จัดการโปรแกรม partner',
              manage_shipping: data[x].manage_partner,
              checked: false
            }
            this.itemMenuShop3.push(datamanagePartner)
          }
          if (data[x].manage_account_bank === '1') {
            const datamanageAccount = {
              key: 17,
              title: 'จัดการบัญชีร้านค้า',
              manage_shipping: data[x].manage_account_bank,
              checked: true
            }
            this.itemMenuShop2.push(datamanageAccount)
          } else {
            const datamanageAccount = {
              key: 17,
              title: 'จัดการบัญชีร้านค้า',
              manage_shipping: data[x].manage_account_bank,
              checked: false
            }
            this.itemMenuShop3.push(datamanageAccount)
          }
          this.itemMenuShopAll.push([...this.itemMenuShop2, ...this.itemMenuShop3].sort((a, b) => a.key - b.key))
        }
        // console.log('Val: ', this.sumZero, this.sumOne)
        this.modalShowPosition = !this.modalShowPosition
      } else {
        if (message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>คุณไม่สามารถเข้าถึงการใช้งานฟังก์ชั่นนี้ได้</h3>' })
        }
      }
    },
    openModalAddPosition () {
      this.position_name = ''
      this.setCompany = '0'
      this.setPermission = '0'
      this.Partner = '0'
      this.Order = '0'
      this.approveOrder = '0'
      this.Payment = '0'
      this.Report = '0'
      this.Tracking = '0'
      this.Refund = '0'
      this.Review = '0'
      this.SaleOrder = '0'
      this.ApproveOrerSale = '0'
      this.ManageSaleOrder = '0'
      this.ManagePromotion = '0'
      this.ManageAffiliate = '0'
      this.ManagePartner = '0'
      this.ManageAccountBank = '0'
      this.itemMenuShop.forEach(array => {
        array.checked = false
      })
      if (this.checkedAffiliate === '1') {
        this.itemMenuShop[14].checked = false
      }
      this.modalAddPosition = !this.modalAddPosition
    },
    ShowEditPosition () {
      this.modalShowPosition = !this.modalShowPosition
      this.modalEditPosition = !this.modalEditPosition
    },
    async editForm () {
      // console.log(this.selectType3)
      // var status = false
      this.$store.commit('openLoader')
      if (this.$refs.FormAddPosition.validate(true)) {
        const ArrSum = this.itemMenuShopAll[0]
        for (const value in ArrSum) {
          if (ArrSum[value].key === 1 && ArrSum[value].checked === true) {
            this.setCompany = '1'
          }
          if (ArrSum[value].key === 2 && ArrSum[value].checked === true) {
            this.setPermission = '1'
          }
          if (ArrSum[value].key === 3 && ArrSum[value].checked === true) {
            this.Partner = '1'
          }
          if (ArrSum[value].key === 4 && ArrSum[value].checked === true) {
            this.Order = '1'
          }
          if (ArrSum[value].key === 5 && ArrSum[value].checked === true) {
            this.approveOrder = '1'
          }
          if (ArrSum[value].key === 6 && ArrSum[value].checked === true) {
            this.Payment = '1'
          }
          if (ArrSum[value].key === 7 && ArrSum[value].checked === true) {
            this.Report = '1'
          }
          if (ArrSum[value].key === 8 && ArrSum[value].checked === true) {
            this.Tracking = '1'
          }
          if (ArrSum[value].key === 9 && ArrSum[value].checked === true) {
            this.Refund = '1'
          }
          if (ArrSum[value].key === 10 && ArrSum[value].checked === true) {
            this.Review = '1'
          }
          if (ArrSum[value].key === 11 && ArrSum[value].checked === true) {
            this.SaleOrder = '1'
          }
          if (ArrSum[value].key === 12 && ArrSum[value].checked === true) {
            this.ApproveOrerSale = '1'
          }
          if (ArrSum[value].key === 13 && ArrSum[value].checked === true) {
            this.ManageSaleOrder = '1'
          }
          if (ArrSum[value].key === 14 && ArrSum[value].checked === true) {
            this.ManagePromotion = '1'
          }
          if (ArrSum[value].key === 15 && ArrSum[value].checked === true) {
            this.ManageAffiliate = '1'
          }
          if (ArrSum[value].key === 16 && ArrSum[value].checked === true) {
            this.ManagePartner = '1'
          }
          if (ArrSum[value].key === 17 && ArrSum[value].checked === true) {
            this.ManageAccountBank = '1'
          }
          // this.num++
          // if (this.num > 9) {
          //   status = true
          // }
        }
        const dataSent = await {
          position_id: this.position_ID,
          seller_shop_id: localStorage.getItem('shopSellerID'),
          position_name: this.position_NameUser,
          status: 'active',
          manage_product: this.setCompany,
          manage_setting_shop: this.setPermission,
          manage_stock: this.Partner,
          manage_order: this.Order,
          manage_dashboard: this.approveOrder,
          manage_income: this.Payment,
          manage_user_with_position: this.Report,
          manage_product_refund: this.Tracking,
          manage_tracking: this.Refund,
          manage_shipping: this.Review,
          sale_order: this.SaleOrder,
          manage_approve_order: this.ApproveOrerSale,
          manage_sale_order: this.ManageSaleOrder,
          manage_promotion: this.ManagePromotion,
          manage_affiliate: this.ManageAffiliate,
          manage_partner: this.ManagePartner,
          manage_account_bank: this.ManageAccountBank
        }
        // this.positionId = await this.position_ID
        // this.typeId = await localStorage.getItem('shopSellerID')
        await this.$store.dispatch('actionCreateUpdatePositionInShop', dataSent)
        var { result = '' } = await this.$store.state.ModuleShop.stateCreateUpdatePositionInShop
        if (result === 'SUCCESS') {
          await this.$store.dispatch('actionsAuthorityUser')
          var response = await this.$store.state.ModuleUser.stateAuthorityUser
          this.list_seller = response.data.list_shop_detail
          var shopSellerID = localStorage.getItem('shopSellerID')
          for (let i = 0; i < this.list_seller.length; i++) {
            if (shopSellerID === (this.list_seller[i].seller_shop_id === null ? '' : this.list_seller[i].seller_shop_id.toString())) {
              localStorage.setItem('list_shop_detail', Encode.encode(this.list_seller[i]))
            }
          }
          await this.$EventBus.$emit('AuthorityUser')
          await this.$EventBus.$emit('AuthorityUsers')
          this.modalEditPosition = false
          this.itemMenuShop2 = []
          this.itemMenuShop3 = []
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>แก้ไขรายละเอียดตำแหน่งและสิทธิ์การใช้งานสำเร็จ</h3>' })
          this.setCompany = '0'
          this.setPermission = '0'
          this.Partner = '0'
          this.Order = '0'
          this.approveOrder = '0'
          this.Payment = '0'
          this.Report = '0'
          this.Tracking = '0'
          this.Refund = '0'
          this.Review = '0'
          this.SaleOrder = '0'
          this.ApproveOrerSale = '0'
          this.ManageSaleOrder = '0'
          this.ManagePromotion = '0'
          this.ManageAffiliate = '0'
          this.ManagePartner = '0'
          this.ManageAccountBank = '0'
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
        }
      }
    },
    async createPosition () {
      // var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.$store.commit('openLoader')
      if (this.$refs.FormAddPosition.validate(true)) {
        for (const value in this.itemMenuShop) {
          // console.log('createPosition**', value)
          if (this.itemMenuShop[value].key === 1 && this.itemMenuShop[value].checked === true) {
            this.setCompany = '1'
          }
          if (this.itemMenuShop[value].key === 2 && this.itemMenuShop[value].checked === true) {
            this.setPermission = '1'
          }
          if (this.itemMenuShop[value].key === 3 && this.itemMenuShop[value].checked === true) {
            this.Partner = '1'
          }
          if (this.itemMenuShop[value].key === 4 && this.itemMenuShop[value].checked === true) {
            this.Order = '1'
          }
          if (this.itemMenuShop[value].key === 5 && this.itemMenuShop[value].checked === true) {
            this.approveOrder = '1'
          }
          if (this.itemMenuShop[value].key === 6 && this.itemMenuShop[value].checked === true) {
            this.Payment = '1'
          }
          if (this.itemMenuShop[value].key === 7 && this.itemMenuShop[value].checked === true) {
            this.Report = '1'
          }
          if (this.itemMenuShop[value].key === 8 && this.itemMenuShop[value].checked === true) {
            this.Tracking = '1'
          }
          if (this.itemMenuShop[value].key === 9 && this.itemMenuShop[value].checked === true) {
            this.Refund = '1'
          }
          if (this.itemMenuShop[value].key === 10 && this.itemMenuShop[value].checked === true) {
            this.Review = '1'
          }
          if (this.itemMenuShop[value].key === 11 && this.itemMenuShop[value].checked === true) {
            this.SaleOrder = '1'
          }
          if (this.itemMenuShop[value].key === 12 && this.itemMenuShop[value].checked === true) {
            this.ApproveOrerSale = '1'
          }
          if (this.itemMenuShop[value].key === 13 && this.itemMenuShop[value].checked === true) {
            this.ManageSaleOrder = '1'
          }
          if (this.itemMenuShop[value].key === 14 && this.itemMenuShop[value].checked === true) {
            this.ManagePromotion = '1'
          }
          if (this.itemMenuShop[value].key === 15 && this.itemMenuShop[value].checked === true) {
            this.ManageAffiliate = '1'
          }
          if (this.itemMenuShop[value].key === 16 && this.itemMenuShop[value].checked === true) {
            this.ManagePartner = '1'
          }
          if (this.itemMenuShop[value].key === 17 && this.itemMenuShop[value].checked === true) {
            this.ManageAccountBank = '1'
          }
          // console.log('setCompany', value, key, this.setCompany)
        }
        // [FormsAll.qu_detail.buyer_name !== '' && FormsAll.qu_detail.qu_number !== ''].includes(true)
        const dataSent = {
          position_id: '-1',
          seller_shop_id: localStorage.getItem('shopSellerID'),
          position_name: this.position_name,
          status: 'active',
          manage_product: this.setCompany,
          manage_setting_shop: this.setPermission,
          manage_stock: this.Partner,
          manage_order: this.Order,
          manage_dashboard: this.approveOrder,
          manage_income: this.Payment,
          manage_user_with_position: this.Report,
          manage_product_refund: this.Tracking,
          manage_tracking: this.Refund,
          manage_shipping: this.Review,
          sale_order: this.SaleOrder,
          manage_approve_order: this.ApproveOrerSale,
          manage_sale_order: this.ManageSaleOrder,
          manage_promotion: this.ManagePromotion,
          manage_affiliate: this.ManageAffiliate,
          manage_partner: this.ManagePartner,
          manage_account_bank: this.ManageAccountBank
        }
        // const dataSent = {
        //   business_id: companyData.business_id,
        //   company_id: companyData.id,
        //   name: this.position_name,
        //   position_data: {
        //     set_company: this.setCompany,
        //     set_permission: this.setPermission,
        //     partner: this.Partner,
        //     order: this.Order,
        //     approve_order: this.approveOrder,
        //     payment: this.Payment,
        //     report: this.Report,
        //     tracking: this.Tracking,
        //     refund: this.Refund,
        //     review: this.Review
        //   }
        // }
        await this.$store.dispatch('actionCreateUpdatePositionInShop', dataSent)
        var { result = '' } = await this.$store.state.ModuleShop.stateCreateUpdatePositionInShop
        var data = await this.$store.state.ModuleShop.stateCreateUpdatePositionInShop
        if (result === 'SUCCESS') {
          this.getListShop()
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>สร้างรายละเอียดตำแหน่งและสิทธิ์การใช้งานสำเร็จ</h3>' })
          this.modalAddPosition = !this.modalAddPosition
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({ icon: 'error', title: data.message, showConfirmButton: false, timer: 2000 })
          this.modalAddPosition = !this.modalAddPosition
          this.$store.commit('closeLoader')
        }
      }
    },
    countCompany (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    SelectDetailPosition (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
      this.page = 1
    },
    async gotoCompanyDetail (val) {
      var data = {
        company_id: val.id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      // console.log(response)
      if (response.result === 'SUCCESS') {
        if (response.message === 'Show company detail success.') {
          localStorage.setItem('companyData', Encode.encode(response.data))
          this.$EventBus.$emit('getCompanyName')
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style lang="scss" scoped>
::v-deep .swal-title {
  margin: 0px;
  font-size: 16px;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.21);
  margin-bottom: 28px;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
}
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.max-v-list-height {
  max-height: 10px;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
