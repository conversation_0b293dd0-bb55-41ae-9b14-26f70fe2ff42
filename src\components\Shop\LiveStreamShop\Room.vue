<template>
  <v-container>
    <v-row justify="center">
      <v-col cols="12" md="6">
        <div id="video-container" class="video-section">
          <!-- The video element -->
          <video id="local-video" autoplay playsinline></video>

          <!-- Button container to center the buttons at the bottom -->
          <div class="button-container">
            <v-btn
              v-if="room"
              class="video-button"
              fab
              small
              @click="toggleMic"
            >
              <v-icon>{{ isMicOn ? 'mdi-microphone' : 'mdi-microphone-off' }}</v-icon>
            </v-btn>
            <v-btn
              v-if="room"
              class="video-button"
              fab
              small
              @click="toggleCamera"
            >
              <v-icon>{{ isCameraOn ? 'mdi-video' : 'mdi-video-off' }}</v-icon>
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row justify="center" class="mt-5">
      <v-col cols="12" md="6" class="text-center">
        <h1>LiveKit Room</h1>
        <v-btn color="primary" @click="joinRoom" v-if="!room">Join Room</v-btn>
        <v-btn color="error" @click="disconnect" v-if="room">Disconnect</v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Room, createLocalTracks, RoomEvent, VideoPresets, createLocalVideoTrack } from 'livekit-client'

export default {
  data () {
    return {
      room: null,
      videoTracks: [],
      audioTracks: [],
      isMicOn: true,
      isCameraOn: true,
      showPopup: false
    }
  },
  mounted () {
    document.getElementById('video-container').style.display = 'none'
  },
  methods: {
    async joinRoom () {
      this.isMicOn = true
      this.isCameraOn = true
      const serverUrl = 'wss://meet-lab.one.th' // Replace with your LiveKit server URL
      const roomName = 'NGS'
      const shopID = '19' // Example shop ID
      let accessToken = ''

      try {
        // Fetch the token from your backend
        const data = { shopId: shopID, shopName: roomName }
        await this.$store.dispatch('actionsGenTokenStream', data)
        const response = await this.$store.state.ModuleLiveStream.stateGenTokenStream
        if (response.message === 'สร้าง token สำเร็จ.') {
          accessToken = response.token
        } else {
          console.error('Token generation failed:', response)
          return
        }

        // Connect to the room
        this.room = new Room({
          adaptiveStream: true,
          dynacast: true,
          videoCaptureDefaults: {
            resolution: VideoPresets.h720.resolution
          }
        })
        await this.room.connect(serverUrl, accessToken, { autoSubscribe: true })
        console.log('Room connected')

        // Create and publish local tracks
        const localTracks = await createLocalTracks({ video: true, audio: true })
        const localVideoTrack = localTracks.find((track) => track.kind === 'video')
        const localAudioTrack = localTracks.find((track) => track.kind === 'audio')

        // Video
        if (localVideoTrack) {
          const localVideo = document.getElementById('local-video')
          if (localVideo) {
            localVideo.srcObject = await localVideoTrack.mediaStream
          }
          await this.room.localParticipant.publishTrack(localVideoTrack)
        } else {
          console.error('No local video track available.')
        }

        // Audio
        if (localAudioTrack) {
          await this.room.localParticipant.publishTrack(localAudioTrack)
        } else {
          console.error('No local audio track available.')
        }

        // Store the tracks for later use
        this.videoTracks.push(localVideoTrack)
        this.audioTracks.push(localAudioTrack)

        // Handle participant events
        this.room.on(RoomEvent.ParticipantConnected, this.onParticipantConnected)
        this.room.on(RoomEvent.ParticipantDisconnected, this.onParticipantDisconnected)
        document.getElementById('video-container').style.display = 'inline'
      } catch (error) {
        console.error('Error joining the room:', error)
      }
    },

    onParticipantConnected (participant) {
      console.log('Participant connected:', participant.identity)
      participant.on(RoomEvent.TrackSubscribed, (track) => {
        if (track.kind === 'video') {
          this.addVideoTrack(track)
        }
      })
    },

    async toggleMic () {
      const audioTrack = this.audioTracks[0]
      if (audioTrack) {
        try {
          if (this.isMicOn) {
            // Stop and unpublish the current audio track
            // audioTrack.mute()
            this.room.localParticipant.setMicrophoneEnabled(false)
            this.isMicOn = false
          } else {
            // Recreate and publish a new audio track
            // audioTrack.unmute()
            await navigator.mediaDevices.getUserMedia({ video: true })
            this.room.localParticipant.setMicrophoneEnabled(true)
            this.isMicOn = true
          }
        } catch (error) {
          console.error('Error toggling microphone:', error)
        }
      }
    },

    async toggleCamera () {
      try {
        if (this.isCameraOn) {
          // Stop all video tracks
          this.videoTracks.forEach((track) => {
            track.stop() // Stops the media stream
            track.detach() // Detaches the track from any DOM elements
          })

          // Unpublish the video track from the room
          const videoTrack = this.videoTracks[0]
          if (videoTrack) {
            this.room.localParticipant.unpublishTrack(videoTrack)
          }

          // Clear the videoTracks array
          this.videoTracks = []
          this.isCameraOn = false
        } else {
          // Recreate and publish a new video track
          const videoTrack = await createLocalVideoTrack()

          // Attach the video track to the local video element
          const videoElement = document.getElementById('local-video')
          if (videoElement) {
            videoTrack.attach(videoElement)
          }

          // Publish the new track to the room
          await this.room.localParticipant.publishTrack(videoTrack)

          // Store the track for later reference
          this.videoTracks.push(videoTrack)
          this.isCameraOn = true
        }
      } catch (error) {
        console.error('Error toggling camera:', error)
      }
    },

    onParticipantDisconnected (participant) {
      console.log('Participant disconnected:', participant.identity)
      this.removeParticipantTracks(participant)
    },

    addVideoTrack (track) {
      this.videoTracks.push(track)
      // Logic to attach the video track to your UI can be added here
    },

    removeParticipantTracks (participant) {
      participant.tracks.forEach((publication) => {
        if (publication.track && publication.track.kind === 'video') {
          this.removeVideoTrack(publication.track)
        }
      })
    },

    removeVideoTrack (track) {
      this.videoTracks = this.videoTracks.filter((t) => t.sid !== track.sid)
    },

    disconnect () {
      // Stop all local tracks (audio and video)
      if (this.room) {
        for (const publication of this.room.localParticipant.trackPublications.values()) {
          const track = publication.track // Get the track (audio/video)
          if (track) {
            track.stop() // Stop the media track
            track.detach() // Detach it from the DOM
            // Unpublish the track from the room
            this.room.localParticipant.unpublishTrack(track)
          } else {
            console.warn(`Track is undefined for publication with sid: ${publication.trackSid}`)
          }
        }
        // Extra step to stop any remaining active media tracks
        // this.stopAllMediaStreams()
        this.audioTracks.forEach((track) => {
          track.stop()
          track.detach()
        })
        this.videoTracks.forEach((track) => {
          track.stop() // Stops the media stream
          track.detach() // Detaches the track from any DOM elements
        })
        // Disconnect from the room
        this.room.disconnect()
        this.room = null

        // Reset UI and state
        this.isMicOn = false
        this.isCameraOn = false

        // Clear video tracks and close popup
        this.videoTracks = []
        this.audioTracks = []
        // Clear the video preview container
        const videoElement = document.getElementById('local-video')
        if (videoElement) {
          videoElement.srcObject = null // Clear the video element's source
        }
        document.getElementById('video-container').style.display = 'none'
        console.log('Disconnected from the room, mic and video stopped.')
      }
    }
  },
  beforeDestroy () {
    this.disconnect()
  }
}
</script>

<style scoped>
#video-container {
  position: relative;
  display: inline-block;
  width: 100%;
  height: auto;
}

video {
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  height: auto;
  transform: scaleX(-1);
}

.button-container {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.video-button {
  position: relative;
  z-index: 10;
}
</style>
