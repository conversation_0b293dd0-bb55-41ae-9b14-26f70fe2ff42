<template lang="html">
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="overflow: hidden;">
      <v-row dense>
        <v-col cols="12" class="mb-2 mx-1 mt-2">
          <v-row justify="start">
            <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">สต๊อกสินค้า</v-card-title>
            <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> สต๊อกสินค้า</v-card-title>
            <!-- <v-icon color="#27AB9C" class="mb-1 pt-2 ml-2">mdi-alert-circle-outline</v-icon> -->
            <!-- <v-tooltip v-model="show" color="rgba(255, 255, 255, .1)" right>
              <template v-slot:activator="{ on, attrs }">
                <v-icon
                  color="#27AB9C"
                  dark
                  v-bind="attrs"
                  v-on="on"
                  class="mb-1 pt-2 ml-2"
                >
                  mdi-information-outline
                </v-icon>
              </template>
              <v-img
                src="@/assets/ImageINET-Marketplace/ICONShop/suggest-stock.png"
                contain
                width="452.18"
                height="119"
                style="margin-left: -85px; margin-top: 130px;"
              ></v-img>
            </v-tooltip> -->
          </v-row>
        </v-col>
        <v-row
        justify="center"
        align-content="center"
        v-if="disableTable === false"
      >
        <v-col cols="12" md="12" align="center">
          <div class="my-5">
            <v-img
              src="@/assets/ImageINET-Marketplace/ICONShop/None-stock.png"
              max-height="500px"
              max-width="500px"
              height="100%"
              width="100%"
              contain
              aspect-ratio="2"
            ></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
            <span style="font-weight: bold; font-size: 24px; line-height: 32px;"
              >คุณยังไม่มีสต๊อกสินค้า</span
            ><br />
            <span style="font-weight: bold; font-size: 24px; line-height: 32px;"
              >กด
              <span style="font-size: 28px;">“เพิ่มสินค้า”</span>
              เพื่อร้านค้าที่สมบูรณ์ของคุณ</span
            >
          </h2>
        </v-col>
      </v-row>
        <v-col v-if="disableTable === true" >
          <TableInventory :props="DataTable" />
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
export default {
  components: {
    TableInventory: () =>
      import('@/components/Inventory/MyInventory/TableInventoryUI')
  },
  data () {
    return {
      DataTable: [],
      show: false,
      disableTable: true
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/inventoryMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/inventory' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // GetInventoryData () {
    //   var response = this.$store.state.ModuleInventory
    //     .stateGetInventoryData
    //   console.log('response inventory data====>', response)
    //   // this.$EventBus.$emit('send_shop', response.data[0])
    //   var DataTable = []
    //   if (response.result === 'SUCCESS') {
    //     DataTable = response.data
    //   } else {
    //     DataTable = []
    //   }
    //   return DataTable
    // }
  },
  async created () {
    this.GetInventoryData()
    this.$EventBus.$emit('changeNav')
    // this.$store.dispatch('actionsGetInventoryData', {
    //   seller_shop_id: this.$store.state.ModuleShop.stateShopData.data[0].seller_shop_id.toString()
    // })
    // this.$store.dispatch('actionsGetShopData')
  },
  methods: {
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    async GetInventoryData () {
      var shopID = localStorage.getItem('shopSellerID')
      var dataShopID = {
        seller_shop_id: shopID
      }
      await this.$store.dispatch('actionsGetShopData', dataShopID)
      const shopData = await this.$store.state.ModuleShop.stateShopData.data
      // console.log('shop id', shopData.seller_shop_id)
      var data = {
        seller_shop_id: shopData.seller_shop_id.toString()
      }
      await this.$store.dispatch('actionsGetInventoryData', data)
      var response = await this.$store.state.ModuleInventory.stateGetInventoryData
      // console.log('response inventory data====>', response)
      // this.$EventBus.$emit('send_shop', response.data[0])
      if (response.result === 'SUCCESS') {
        this.DataTable = response.data
      } else {
        this.DataTable = []
      }
      if (this.DataTable.length === 0) {
        this.disableTable = false
      } else {
        this.disableTable = true
      }
    }
  }
}
</script>

<style lang="css" scoped>
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>
