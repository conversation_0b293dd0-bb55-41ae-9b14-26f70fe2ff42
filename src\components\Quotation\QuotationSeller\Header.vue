<template>
  <v-container grid-list-xs>
    <!-- logo, หัวกระดาษ, สร้างเมื่อ -->
    <v-row class="mb-5">
      <v-col cols="4" class="pa-0">
        <v-row>
          <div class="line_size">
            <v-col cols="12" class="mb-3 pt-0 pb-0">
              <v-img
                class="titleleft"
                style="background-color: white"
                :src="require(`@/assets/new_epro.png`)"
              ></v-img>
            </v-col>
          </div>
        </v-row>
      </v-col>
      <v-col cols="4" class="pa-0">
        <v-row>
          <v-col cols="12" class="text-center pl-1 pr-1">
            <span class="titlerightQuotation">ใบเสนอราคา</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="4" class="pa-0">
        <v-row>
          <v-col cols="6" style="text-align: right" class="POpadding1">
            <span class="contact_center font-weight-bold">
              รหัสการสั่งซื้อ
            </span>
          </v-col>
          <v-col cols="6" style="text-align: left" class="POpadding">
            <span class="contact_center">{{ OrderSellerDetailProp.transaction_number }}</span>
          </v-col>
          <v-col cols="6" style="text-align: right" class="POpadding1">
            <span class="contact_center font-weight-bold"> สร้างเมื่อ </span>
          </v-col>
          <v-col cols="6" style="text-align: left" class="POpadding">
            <span class="contact_center">{{ OrderSellerDetailProp.order_created_date.substring(10, 0) }}</span>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <!-- user name , address , payment transection -->
    <v-row>
      <v-col cols="12" md="12" class="pt-0">
      </v-col>
      <v-col cols="12" md="12">
        <!-- หัวข้อtitle -->
        <v-row>
          <v-col cols="3" style="text-align: right">
            <span class="header_a"
              >รหัสการสั่งซื้อ&nbsp;&nbsp;:&nbsp;&nbsp;</span
            >
          </v-col>
          <v-col cols="2">
            <!-- <span class="header_b">นายทองดี เสมอมิ่ง&nbsp;&nbsp;:&nbsp;&nbsp;</span> -->
            <span class="header_b">{{ OrderSellerDetailProp.transaction_number }}</span>
          </v-col>
        </v-row>
        <!-- บรรทัดสอง -->
        <v-row>
          <v-col cols="3" style="text-align: right">
            <span class="header_a"
              >ชื่อผู้ซื้อ&nbsp;&nbsp;:&nbsp;&nbsp;</span
            >
          </v-col>
          <v-col cols="2">
            <!-- <span class="header_b">นายทองดี เสมอมิ่ง&nbsp;&nbsp;:&nbsp;&nbsp;</span> -->
            <span class="header_b">{{ OrderSellerDetailProp.owner_order }}</span>
          </v-col>
        </v-row>
        <!-- บรรทัดสาม  -->
        <v-row>
          <v-col cols="3" style="text-align: right">
            <span class="header_a"
              >ที่อยู่จัดส่ง&nbsp;&nbsp;:&nbsp;&nbsp;</span
            >
          </v-col>
          <v-col cols="7" >
            <p class="header_b mt-1">{{ OrderSellerDetailProp.user_address }}</p>
          </v-col>
          <!-- <v-col cols="3" style="text-align: right">
            <span class="header_a"
              >ที่อยู่จัดส่งเพิ่มเติม&nbsp;&nbsp;:&nbsp;&nbsp;</span
            >
          </v-col>
          <v-col cols="9">
            <span class="header_b">รหัสไปรษณีย์ 10150 เบอร์โทรศัพท์ 0985541123</span>
          </v-col> -->
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: ['OrderSellerDetailProp']
}
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css?family=Sarabun&display=swap");
.header_a {
  font-family: "Sarabun" !important;
  font-weight: bold;
  font-size: 12px !important;
  padding-left: 20px;
}
.header_b {
  font-family: "Sarabun" !important;
  font-size: 10px !important;
}
.contact_center {
  font-family: "Sarabun" !important;
  font-size: 12px !important;
}
.POpadding1 {
  padding-top: 0px;
  padding-left: 0px;
  padding-right: 5px;
  padding-bottom: 0px;
}
.POpadding {
  padding-top: 0px;
  padding-left: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
}
.titleright {
  font-family: "Sarabun" !important;
  font-size: 12px !important;
  text-align: right;
  color: rgb(3, 39, 0);
  background-color: white;
  margin-top: -15px;
}
.titlerightQuotation {
  font-size: 32px !important;
  font-family: "Sarabun" !important;
  text-align: center;
  color: rgb(0, 0, 0);
  font-weight: bold;
  line-height: 10px;
  margin-top: 25px;
  margin-bottom: 20px;
}
.titleleft {
  text-align: left;
  width: 200px;
  height: 50px;
}
.line_size {
  font-family: "Sarabun" !important;
  line-height: 1 !important;
}
</style>
