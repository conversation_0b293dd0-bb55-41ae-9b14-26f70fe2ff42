<template>
  <div class="text-center">
    <v-dialog v-model="ModalCoupon" width="684" persistent :style="MobileSize ? 'z-index: 16000103;' : ''">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 8px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'pt-6 px-3' : 'pt-7 px-2'">
                    <v-card-text v-if="!MobileSize" class="pa-0">
                      <v-col class="py-0">
                          <v-row class="">
                            <v-col cols="12" class="py-0">
                              <v-row dense class="pt-2 pb-6 align-center">
                                <v-img src="@/assets/pointtoearn.png" max-width="26" max-height="26">
                                </v-img>
                                <span class="pl-2" style="font-size: 18px; font-weight: 600;">
                                  {{ $t('ListPoint.StoreDiscountPoint') }}
                                </span>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-img class="my-4 pt-3" src="@/assets/bannerpoint.png" height="120" style="border-radius: 8px; align-items: center;">
                            <span class="pl-8 pt-2" style="font-size: 40px; font-weight: 800; color: #FFFFFF;">{{PointData.all_point ? Number(parseInt(PointData.all_point)).toLocaleString() : '0'}}
                              <span style="font-size: 16px; font-weight: 400; color: #FFFFFF;">{{ $t('ListPoint.Points') }}</span>
                            </span>
                          </v-img>
                          <span style="font-size: 16px; color: #333333;">
                            {{ $t('ListPoint.RedeemPoints') }}
                          </span>
                          <v-text-field outlined dense hide-details v-model="inputValue" @change="checkInput" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')">
                          </v-text-field>
                          <v-card class="mt-4" style="background-color: #FAFAFA;" elevation="0">
                            <v-card-text class="pa-0">
                              <v-col class="pb-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;">{{ $t('ListPoint.PointUsageRules') }}</span>
                              </v-col>
                              <v-col class="py-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{MaxPoint ? $t('ListPoint.MaximumUsablePoints')  + ' ' +  Number(parseInt(MaxPoint)).toLocaleString() + ' ' + $t('ListPoint.Points') : $t('ListPoint.MaximumUsablePoints') + ' 0 ' + $t('ListPoint.Points')}}</span>
                              </v-col>
                              <v-col class="py-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{PointData.x_point || PointData.x_baht ? Number(parseInt(PointData.x_point)).toLocaleString() + $t('ListPoint.Points') +' = ' + Number(parseInt(PointData.x_baht)).toLocaleString()  + ' ' +  $t('ListPoint.Baht')
                                  : '0 ' + $t('ListPoint.Points') + ' = 0 ' + $t('ListPoint.Baht')}}</span>
                              </v-col>
                              <v-col class="py-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{PointData.point_order_total || PointData.point_received ? $t('ListPoint.Spend') + ' ' + Number(parseInt(PointData.point_order_total)).toLocaleString() + ' ' + $t('ListPoint.Baht') + ' ' + $t('ListPoint.Earn') + ' ' + Number(parseInt(PointData.point_received)).toLocaleString() + ' ' + $t('ListPoint.Points') : $t('ListPoint.Spend') + ' 0 ' + $t('ListPoint.Baht') + ' ' + $t('ListPoint.Earn') + ' 0 ' + $t('ListPoint.Points')}}</span>
                              </v-col>
                              <v-col class="pt-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{ $t('ListPoint.PointUsageMustNotExceed') }} 25% {{ $t('ListPoint.OrderTotal') }}</span>
                              </v-col>
                            </v-card-text>
                          </v-card>
                      </v-col>
                    </v-card-text>
                    <v-card-text v-if="MobileSize" class="pa-0">
                      <v-col class="py-0">
                          <v-row class="">
                            <v-col cols="12" class="py-0">
                              <v-row dense class="pt-2 pb-6 align-center">
                                <v-img src="@/assets/pointtoearn.png" max-width="26" max-height="26">
                                </v-img>
                                <span class="pl-2" style="font-size: 18px; font-weight: 600;">
                                  {{ $t('ListPoint.StoreDiscountPoint') }}
                                </span>
                              </v-row>
                            </v-col>
                          </v-row>
                          <v-img class="my-4 pt-3" src="@/assets/bannerpointmini.png" height="100" style="border-radius: 8px; align-items: center;">
                            <span class="pl-8 pt-2" style="font-size: 32px; font-weight: 800; color: #FFFFFF;">{{PointData.all_point ? Number(parseInt(PointData.all_point)).toLocaleString() : '0'}}
                              <span style="font-size: 14px; font-weight: 400; color: #FFFFFF;">{{ $t('ListPoint.Points') }}</span>
                            </span>
                          </v-img>
                          <span style="font-size: 16px; color: #333333;">
                            {{ $t('ListPoint.RedeemPoints') }}
                          </span>
                          <v-text-field outlined dense hide-details v-model="inputValue" @change="checkInput" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')">
                          </v-text-field>
                          <v-card class="mt-4" style="background-color: #FAFAFA;" elevation="0">
                            <v-card-text class="pa-0">
                              <v-col class="pb-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;">{{ $t('ListPoint.PointUsageRules') }}</span>
                              </v-col>
                              <v-col class="py-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{MaxPoint ? $t('ListPoint.MaximumUsablePoints') + Number(parseInt(MaxPoint)).toLocaleString() + $t('ListPoint.Points') : $t('ListPoint.MaximumUsablePoints') + ' 0 ' + $t('ListPoint.Points')}}</span>
                              </v-col>
                              <v-col class="py-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{PointData.x_point || PointData.x_baht ? Number(parseInt(PointData.x_point)).toLocaleString() + $t('ListPoint.Points') + ' = ' + Number(parseInt(PointData.x_baht)).toLocaleString() + $t('ListPoint.Baht')
                                  : '0 ' + $t('ListPoint.Points') + ' = 0 ' + $t('ListPoint.Baht')}}</span>
                              </v-col>
                              <v-col class="py-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{PointData.point_order_total || PointData.point_received ? $t('ListPoint.Spend') + Number(parseInt(PointData.point_order_total)).toLocaleString() + $t('ListPoint.Baht') + $t('ListPoint.Earn')  + ' ' + Number(parseInt(PointData.point_received)).toLocaleString() + $t('ListPoint.Points')
                                  : $t('ListPoint.Spend') + ' 0 ' + $t('ListPoint.Baht') + ' ' + $t('ListPoint.Earn') + ' 0 ' + $t('ListPoint.Points')}}</span>
                              </v-col>
                              <v-col class="pt-1">
                                <span class="dynamic-font" style="font-size: 14px; font-weight: 500; color: #636363;"> •  {{ $t('ListPoint.PointUsageMustNotExceed') }} 25% {{ $t('ListPoint.OrderTotal') }}</span>
                              </v-col>
                            </v-card-text>
                          </v-card>
                      </v-col>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="px-12 justify-center" style="height: 100px; background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="Close()" >{{ $t('ListPoint.Cancel') }}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="!selectPoint" class="px-10 white--text " style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="confirm()" >{{ $t('ListPoint.Confirm') }}</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="px-4 justify-center" style="height: 88px; background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="Close()" >{{ $t('ListPoint.Cancel') }}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="!selectPoint" class="px-10 white--text" style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="confirm()" >{{ $t('ListPoint.Confirm') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      DataShop: [],
      CouponDiscount: 0,
      pricePoint: 0,
      MaxPoint: 0,
      checkradio: false,
      PointData: [],
      selectPoint: '',
      inputValue: '',
      show: true,
      UserPoint: 0,
      ShopPointDetail: [],
      ShopID: '',
      dataCouponList: [],
      search: '',
      checkSelect: false,
      CouponID: '',
      CollectID: '',
      CouponImage: '',
      CouponName: '',
      CouponCode: '',
      CouponDescription: '',
      CollectStartdate: '',
      CollectEnddate: '',
      UseStartdate: '',
      UseEnddate: '',
      CouponType: '',
      Quota: '',
      UseCount: '',
      UserCap: '',
      SpendMinimum: '',
      DiscountAmount: '',
      ProductList: '',
      DiscountType: '',
      SellerShopID: '',
      Status: '',
      Cards: [],
      Shipping: [],
      Free: [],
      ModalDetailCoupon: false,
      selectCoupon: [],
      showAllCards: false,
      showAllShipping: false,
      showAllFree: false,
      ModalCoupon: false,
      showProductPriceDiscount: '3',
      showShippingDiscount: '3',
      showFreeGiftCoupon: '3',
      page: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredItemsCards () {
      return this.dataCouponList.filter(item => {
        return item.coupon_name.toLowerCase().includes(this.search.toLowerCase())
      })
    }
  },
  mounted () {
    this.$EventBus.$on('getListPoint', this.getListCoupon)
    this.$EventBus.$on('clearCoupon', this.clearCoupon)
    this.$EventBus.$on('clearPoint', this.clearPoint)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getListPoint')
      this.$EventBus.$off('clearCoupon')
      this.$EventBus.$off('clearPoint')
    })
  },
  created () {
    // this.getListCoupon()
    // console.log('%c Hello Bug ', 'background: red; color: #000; padding: 4px; border-radius: 2px; margin-left: 1ch;', this.Test)
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  watch: {
    // selectPoint (item) {
    //   console.log('object', item)
    // }
    inputValue (val) {
      if (val) {
        if (val === 0 || val === '') {
          this.selectPoint = false
        } else {
          this.selectPoint = true
        }
      } else {
        this.selectPoint = false
      }
    }
  },
  methods: {
    checkInput () {
      if (parseInt(this.MaxPoint) < parseInt(this.PointData.all_point)) {
        if (parseInt(this.inputValue) > parseInt(this.MaxPoint)) {
          this.inputValue = parseInt(this.MaxPoint)
        } else if (parseInt(this.inputValue) < 0) {
          this.inputValue = 0
        }
      } else {
        if (parseInt(this.inputValue) > parseInt(this.PointData.all_point)) {
          this.inputValue = parseInt(this.PointData.all_point)
        } else if (parseInt(this.inputValue) < 0) {
          this.inputValue = 0
        }
      }
    },
    // async getListUserPointBySellerShopID () {
    //   var UserDetail = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
    //   var data = {
    //     seller_shop_id: this.ShopID
    //   }
    //   await this.$store.dispatch('actionsgetListUserPointBySellerShopID', data)
    //   var res = await this.$store.state.ModuleManagePoint.stategetListUserPointBySellerShopID
    //   if (res.result === 'SUCCESS') {
    //     if (res.data.length !== 0) {
    //       res.data.forEach(element => {
    //         if (element.user_id === UserDetail.data[0].id) {
    //           this.UserPoint.all_point = element.all_point
    //         }
    //       })
    //     } else {
    //       this.UserPoint = 0
    //     }
    //   }
    //   // console.log(this.UserPoint)
    // },
    // async getSellerShopPointDetail () {
    //   var data = {
    //     seller_shop_id: this.ShopID
    //   }
    //   await this.$store.dispatch('actionsgetSellerShopPointDetail', data)
    //   var res = await this.$store.state.ModuleManagePoint.stategetSellerShopPointDetail
    //   // console.log('object', res)
    //   if (res.result === 'SUCCESS') {
    //     this.ShopPointDetail = res.data
    //   }
    // },
    async getListUserPointByUser () {
      this.PointData = []
      this.MaxPoint = 0
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
      var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var data = {
        role_user: RoleUser.role,
        customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
        seller_shop_id: this.DataShop.seller_shop_id,
        company_id: onedata.cartData.company_id,
        com_perm_id: onedata.cartData.com_perm_id
      }
      await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
      var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
      if (res.result === 'SUCCESS') {
        this.PointData = res.data[0]
        // console.log('this.PointData', this.PointData)
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('ListPoint.PointError') + '</h3>'
        })
      }
    },
    formatDateToShow (data) {
      if (!data) return null
      const date = data.split('T')
      // console.log('date', date)
      const [year, month, day] = date[0].split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    async getListCoupon () {
      this.Cards = []
      this.Shipping = []
      this.Free = []
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      const normalLength = []
      const onetimeLength = []
      const recurringLength = []
      const ProductListLength = []
      if (this.DataShop.product_list === undefined) {
        if (this.DataShop.product_general !== undefined) {
          this.DataShop.product_general.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              normalLength.push(e)
            }
          })
        }
        if (this.DataShop.product_recurring !== undefined) {
          this.DataShop.product_recurring.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              recurringLength.push(e)
            }
          })
        }
        if (this.DataShop.product_onetime !== undefined) {
          this.DataShop.product_onetime.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              onetimeLength.push(e)
            }
          })
        }
      } else {
        if (this.DataShop.product_list !== undefined) {
          this.DataShop.product_list.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              ProductListLength.push(e)
            }
          })
        }
      }
      const matchedProducts = couponData.product.filter((calItem) => {
        const isMatched = normalLength.some(normalItem => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === normalItem.product_id
          } else {
            const productAttributeId = parseInt(normalItem.product_attribute_detail.product_attribute_id.split(' ')[0], 10)
            return calItem.product_attribute_id === productAttributeId
          }
        }) || recurringLength.some(recurringItem => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === recurringItem.product_id
          } else {
            const productAttributeId = parseInt(recurringItem.product_attribute_detail.product_attribute_id.split(' ')[0], 10)
            return calItem.product_attribute_id === productAttributeId
          }
        }) || onetimeLength.some(onetimeItem => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === onetimeItem.product_id
          } else {
            const productAttributeId = parseInt(onetimeItem.product_attribute_detail.product_attribute_id.split(' ')[0], 10)
            return calItem.product_attribute_id === productAttributeId
          }
        }) || ProductListLength.some(ProductList => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === ProductList.product_id
          } else {
            const productAttributeId = parseInt(ProductList.product_attribute_detail.product_attribute_id)
            return calItem.product_attribute_id === productAttributeId
          }
        })
        return isMatched
      })
      couponData.net_price = this.DataShop.total_price_no_vat
      couponData.price_inc_vat = this.DataShop.total_price_vat
      couponData.total_price_general = this.DataShop.total_price_no_vat_general
      couponData.product = matchedProducts
      couponData.shop_id = this.DataShop.seller_shop_id
      // console.log('couponDatapoint', couponData)
      // localStorage.setItem('couponData', Encode.encode(couponData))
      this.ShopID = this.DataShop.seller_shop_id
      await this.getListUserPointByUser()
      // var CouponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      var pricePoint = parseFloat(this.PointData.x_baht) / parseFloat(this.PointData.x_point)
      this.pricePoint = pricePoint
      var maxPoint = parseInt(((0.25 * (couponData.net_price - this.CouponDiscount)) / parseFloat(pricePoint)) - 1)
      this.MaxPoint = maxPoint <= 0 ? 0 : maxPoint
      // console.log('CouponData', couponData)
      // this.getListUserPointBySellerShopID()
      // this.getSellerShopPointDetail()
      await this.$store.dispatch('actionsListCoupon', couponData)
      var res = await this.$store.state.ModuleCart.stateListCoupon
      if (res.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.dataCouponList = res.data.coupon
        res.data.coupon.forEach(element => {
          const existingCardIndex = this.Cards.findIndex(card => card.coupon_id === element.coupon_id)
          const existingShippingIndex = this.Shipping.findIndex(shipping => shipping.coupon_id === element.coupon_id)
          const existingFreeIndex = this.Free.findIndex(free => free.coupon_id === element.coupon_id)
          if (existingCardIndex === -1 && existingShippingIndex === -1 && existingFreeIndex === -1) {
            if (element.coupon_type === 'discount') {
              this.Cards.push({ ...element })
            } else if (element.coupon_type === 'free_shipping') {
              this.Shipping.push({ ...element })
            } else {
              this.Free.push({ ...element })
            }
          }
        })
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>' + this.$t('ListPoint.SystemError') + '</h3>'
          })
        }
      }
    },
    backstep () {
      this.ModalDetailCoupon = false
    },
    OpenDetail (item) {
      this.ModalDetailCoupon = true
      this.CouponID = item.coupon_id
      this.CollectID = item.collect_id
      this.CouponImage = item.coupon_image
      this.CouponName = item.coupon_name
      this.CouponCode = item.coupon_code
      this.CouponDescription = item.coupon_description
      this.CollectStartdate = item.collect_startdate
      this.CollectEnddate = item.collect_enddate
      this.UseStartdate = item.use_startdate
      this.UseEnddate = item.use_enddate
      this.CouponType = item.coupon_type
      this.Quota = item.quota
      this.UseCount = item.use_count
      this.UserCap = item.user_cap
      this.SpendMinimum = item.spend_minimum
      this.DiscountAmount = item.discount_amount
      this.DiscountMaximum = item.discount_maximum
      this.ProductList = item.product_list
      this.DiscountType = item.discount_type
      this.SellerShopID = item.seller_shop_id
      this.Status = item.status
    },
    Close () {
      if (this.selectCoupon !== '' && this.checkSelect === false) {
        this.selectCoupon = ''
      }
      if (this.selectPoint !== '' && this.checkSelect === false) {
        this.selectPoint = ''
        this.inputValue = ''
      }
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
    },
    clearCoupon () {
      this.selectCoupon = ''
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
      this.checkSelect = false
    },
    clearPoint (item) {
      this.selectPoint = false
      this.inputValue = 0
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
      this.checkSelect = false
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.point = onedata.cartData.point.filter(
        point => point.seller_shop_id !== item.seller_shop_id
      )
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    ShowProductPriceDiscount () {
      this.showAllCards = true
      this.showProductPriceDiscount = this.Cards.length
    },
    ShowShippingDiscount () {
      this.showAllShipping = true
      this.showShippingDiscount = this.Shipping.length
    },
    ShowFreeGiftCoupon () {
      this.showAllFree = true
      this.showFreeGiftCoupon = this.Free.length
    },
    async open (page, item, point, baht, discoupon, data) {
      this.DataShop = data
      // console.log(discoupon)
      this.CouponDiscount = discoupon === '' ? 0 : discoupon
      if (item !== '') {
        this.selectCoupon = item
      }
      if (point !== null && point !== '') {
        this.inputValue = 0
      } else {
        point = 0
        this.inputValue = point
      }
      if (this.inputValue === 0) {
        // console.log(1)
        this.selectPoint = false
        this.checkradio = false
      } else {
        // console.log(2)
        this.selectPoint = true
        this.checkradio = true
      }
      this.page = page
      this.ModalCoupon = true
    },
    confirm () {
      // var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      if (this.selectCoupon.length === 0 && this.selectPoint === false) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('ListPoint.PleaseSelectDiscount') + '</h3>'
        })
      } else if (parseInt(this.inputValue) > this.MaxPoint) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>' + this.$t('ListPoint.PointExceedLimit') + '</h3>'
        })
      } else {
        // this.selectPoint = this.inputValue
        if (this.selectPoint !== false) {
          var Point = [{
            seller_shop_id: this.DataShop.seller_shop_id,
            total_point: this.inputValue * this.pricePoint
          }]
        }
        var Coupon = [].concat(this.Cards, this.Shipping, this.Free).filter(element => {
          return element.coupon_id === this.selectCoupon
        })
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (!Array.isArray(onedata.cartData.point)) {
          // ถ้าไม่ใช่ Array ให้แปลงเป็น Array ว่าง
          onedata.cartData.point = []
        }

        // เพิ่ม Object ใหม่เข้าไปใน Array
        onedata.cartData.point.push({
          seller_shop_id: this.DataShop.seller_shop_id,
          total_point: this.inputValue * this.pricePoint
        })
        localStorage.setItem('oneData', Encode.encode(onedata))
        if (this.page === 'checkoutSaleOrder' || this.page === 'checkout') {
          this.$EventBus.$emit('SelectCouponCheckout', Coupon, Point)
        } else if (this.page === 'QTCheckout') {
          this.$EventBus.$emit('SelectCouponQT', Coupon, Point)
        } else {
          this.$EventBus.$emit('SelectCoupon', Coupon, Point)
        }
        this.selectPoint = ''
        this.inputValue = ''
        // this.checkSelect = true
        this.ModalCoupon = false
        this.ModalDetailCoupon = false
        this.showProductPriceDiscount = 3
        this.showShippingDiscount = 3
        this.showFreeGiftCoupon = 3
        this.showAllCards = false
        this.showAllShipping = false
        this.showAllFree = false
      }
    }
  }
}
</script>

<style>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-input--selection-controls {
  margin-top: 0px;
}
</style>
<style scoped>
.dynamic-font-card {
  font-size: calc(1vw + 1vh + .5vmin); /* Adjust this value as needed */
  color: red;
}
.dynamic-font {
  display: block; /* Ensures the span takes up the full width */
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
