<template>
  <div>
    <v-dialog v-model="modalAddPosition" width="579px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="!MobileSize">
              <v-toolbar-title><span style="color: #27AB9C;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>กำหนดตำแหน่งและสิทธิ์การใช้งาน</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialog()" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container class="mb-0">
          <v-card-text class="mb-0 pb-0">
            <v-row v-if="!MobileSize">
              <v-col cols="12" md="2" sm="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="12" md="10" sm="10" class="mt-5 pt-2" style="font-size: 16px;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col cols="3" md="2" sm="2">
                <v-img lazy-src="@/assets/Businessman.png" max-height="100" max-width="200"
                  src="@/assets/Businessman.png"></v-img>
              </v-col>
              <v-col cols="9" md="10" sm="10" class="mt-2 pt-2" style="font-size: 14px; font-weight: 600;">
                รายละเอียดตำแหน่งและสิทธิ์การใช้งาน
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="12">
                <v-text-field :rules="Rules.emailRules" @keyup.enter="searchData" v-model="email_user" solo dense
                  label="ระบุอีเมล" append-icon="mdi-magnify" ref="Emails">
                </v-text-field>
              </v-col>
            </v-row>
            <v-row v-if="notFoundUser">
              <v-col cols="12" class="center-screen" style="display: flex; justify-content: center;">
                    <v-img lazy-src="@/assets/NotData.png" max-height="187.79px" max-width="172.15px"
                  src="@/assets/NotData.png"></v-img>
              </v-col>
              <v-row dense no-gutters justify="center" class="mb-4">
                <v-col cols="12" md="12" sm="12" xs="12" align="center">
                  <span>ไม่พบข้อมูล</span>
                </v-col>
              </v-row>
            </v-row>
          </v-card-text>
        </v-container>
        <v-container v-show="FoundUser">
          <div>
            <v-card v-if="!MobileSize">
              <v-form ref="FormAddPosition" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="12" md="3" sm="3" justify-center>
                    <v-card-text class="rounded-lg mt-2">
                      <v-img lazy-src="img_detail" max-height="150" max-width="150" :src="img_detail" contain></v-img>
                    </v-card-text>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-row no-gutters class="mt-3 ml-6">
                      <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                          ชื่อ-สกุล :
                        </p>
                      </v-col>
                      <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 14px; color: #333333;">
                          {{ detail_name }}
                        </p>
                      </v-col>
                      <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          อีเมล :
                        </p>
                      </v-col>
                      <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                          {{ detail_email }}
                        </p>
                      </v-col>
                      <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                          เบอร์โทรศัพท์ :
                        </p>
                      </v-col>
                      <v-col cols="8">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                          {{ detail_phone }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row dense no-gutters>
                      <v-col cols="12" class="mt-4 ml-4">
                        <v-autocomplete v-model="values" :rules="Rules.position_name" :items="itemPosition" dense chips small-chips
                          item-text="position_name" return-object label="กรุณาเลือกตำแหน่ง" multiple solo @change="onChange" :item-disabled="isItemDisabled">
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
            </v-card>
            <v-card v-else>
              <v-card-text class="px-0">
                <v-form ref="FormAddPosition" :lazy-validation="lazy">
                  <v-row no-gutters>
                    <v-col cols="3" md="3" sm="3" justify-center>
                      <v-card-text class="rounded-lg mt-2">
                        <v-img lazy-src="img_detail" max-height="88" max-width="100" :src="img_detail" contain></v-img>
                      </v-card-text>
                    </v-col>
                    <v-col cols="9" md="8" sm="8">
                      <v-row no-gutters class="mt-3 ml-6">
                        <v-col cols="4">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            ชื่อ-สกุล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_name }}
                          </p>
                        </v-col>
                        <v-col cols="3">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            อีเมล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_email }}
                          </p>
                        </v-col>
                        <v-col cols="6">
                          <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                            เบอร์โทรศัพท์ :
                          </p>
                        </v-col>
                        <v-col cols="6">
                          <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ detail_phone }}
                          </p>
                        </v-col>
                      </v-row>
                      <v-row dense no-gutters>
                        <v-col cols="12" class="mt-4 ml-4">
                          <v-autocomplete v-model="values" :items="itemPosition" dense chips small-chips :rules="Rules.position_name"
                            item-text="position_name" return-object label="กรุณาเลือกตำแหน่ง" @change="onChange" :item-disabled="isItemDisabled" multiple solo style="font-size: 14px; width: 195px;">
                          </v-autocomplete>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-card>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn class="px-5" outlined color="#27AB9C" @click="modalAddPosition = !modalAddPosition">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="AddUserPositions()" :disabled="disableButton">บันทึก</v-btn>
            </v-card-actions>
          </div>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      lazy: false,
      disableButton: false,
      modalShowPosition: false,
      modalAddPosition: false,
      modalEditPosition: false,
      // changeToSignCA: false,
      sendData: [],
      name: '',
      itemPosition: [],
      values: [],
      listPositionNew: [],
      listPositionOld: [],
      positionsOld: [],
      userIDs: [],
      IsuserIDs: [],
      // new
      notFoundUser: false,
      FoundUser: false,
      email_user: '',
      detail_name: '',
      detail_email: '',
      detail_phone: '',
      img_detail: '',
      user_id: '',
      Rules: {
        position_name: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ]
      }
    }
  },
  watch: {
    sendData (newValue) {
      this.values = newValue.positions || []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    // open (userIDs) {
    //   this.userIDs = userIDs
    //   console.log(this.userIDs)
    // },
    isItemDisabled (item) {
      // ตรวจสอบว่ามีการเลือกตำแหน่งหรือไม่ และถ้ามีก็จะทำให้ตัวเลือกอื่น ๆ ถูกปิดใช้งาน
      // console.log(item.id, 'id')
      // console.log(this.values, 'id val')
      if (item.position_name === 'เจ้าของนิติบุคคล') {
        return true
      }
      return this.values.length > 0 && !this.values.find(value => value.position_id === item.id || value.id === item.id)
    },
    onChange (newValues) {
      // หากมีการเลือกตัวเลือกใหม่ ให้เก็บเฉพาะค่าที่เลือก
      this.values = newValues
    },
    open (userIDs) {
      this.userIDs = userIDs
      // console.log(this.userIDs)
      this.email_user = ''
      this.FoundUser = false
      this.modalAddPosition = !this.modalAddPosition
      this.getBusinessTaxID()
      // this.getListPositions()
    },
    async getBusinessTaxID (status) {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      const taxID = await this.$store.state.ModuleUser.stateAuthorityUser
      this.detailBusiness = taxID.data
      if (this.detailBusiness.length === 0) {
        this.$swal.fire({
          icon: 'error',
          text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
          showConfirmButton: false,
          timer: 2500
        })
        if (!this.MobileSize) {
          this.$router.push('/detailbusinesssid')
        } else {
          this.$router.push('/detailbusinesssidMobile')
        }
      } else {
        this.taxID = taxID.data.array_business[0].owner_tax_id
        const data = {
          status: status,
          tax_id: this.taxID
        }
        await this.$store.dispatch('actionsManageUser', data)
        const detailUser = await this.$store.state.ModuleBusiness.stateManageUser
        if (detailUser.result === 'SUCCESS') {
          // console.log(detailUser.data)
          this.$EventBus.$emit('changeNav')
          this.userDataList = detailUser.data
          // console.log('0', this.userDataList)
          if (this.userDataList.length !== 0) {
            this.userDataList.forEach(element => {
              element.name = element.first_name_th + ' ' + element.last_name_th
            })
            for (var i = 0; i < this.userDataList.length; i++) {
              this.userDataList[i].indexOfUser = i + 1
            }
          }
          this.countUserAll = this.userDataList.length
          this.countUserActive = detailUser.data.filter(user => user.status === 'active').length
          this.countUserInActive = detailUser.data.filter(user => user.status === 'inactive').length
        }
      }
      this.$store.commit('closeLoader')
    },
    closeDialog () {
      this.modalAddPosition = !this.modalAddPosition
      this.email_user = ''
      this.FoundUser = false
    },
    async searchData () {
      await this.$store.dispatch('actionsAuthorityUser')
      const taxID = await this.$store.state.ModuleUser.stateAuthorityUser
      var bizid = localStorage.getItem('business_id')
      var ownerBusiness = taxID.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
      this.taxID = ownerBusiness[0].owner_tax_id
      this.getListPositions()
      const isValidate = this.$refs.Emails.validate()
      if (isValidate) {
        var dataSearch = {
          tax_id: this.taxID,
          email: this.email_user
        }
        // console.log('1', dataSearch)
        await this.$store.dispatch('actionsSearchUser', dataSearch)
        var response = await this.$store.state.ModuleBusiness.stateSearchUser
        // console.log('2', response)
        if (response.code === 200) {
          if (response.message === 'Find user with email successfully') {
            // console.log('3', response)
            this.IsuserIDs = response.data.user.map(user => user.id)
            // console.log('1', this.userIDs)
            // console.log('2', this.IsuserIDs)
            // console.log('3', this.userIDs.some(id => this.IsuserIDs.includes(id)))
            if (this.userIDs.some(id => this.IsuserIDs.includes(id))) {
              // console.log('first')
              this.$swal.fire({
                icon: 'warning',
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true,
                html: '<h3>Email นี้ มีข้อมูลในนิติบุคคลอยู่แล้ว</h3>'
              })
            } else {
              this.modalEditPosition = false
              this.FoundUser = true
              this.notFoundUser = false
              this.values = ''

              // Access the first user in the array
              const user = response.data.user[0]

              this.img_detail = user.img_path === null ? '' : user.img_path
              this.detail_name = user.first_name_th + ' ' + user.last_name_th
              this.detail_email = user.email
              this.detail_phone = user.phone
              this.user_id = user.id

              this.$refs.FormAddPosition.resetValidation()
            }
            // this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
          } else if (response.message === 'Not found user with email.') {
            // console.log('เข้ามาแล้วจ้าาา')
            this.notFoundUser = true
            this.FoundUser = false
            this.$swal.fire({ icon: 'error', showConfirmButton: false, timer: 2000, html: '<h3>กรุณาตรวจสอบ Email อีกครั้ง เนื่องจากไม่พบ Email ในระบบ</h3>' })
          } else {
            this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
          }
        } else {
          this.notFoundUser = true
          this.FoundUser = false
          this.$swal.fire({ icon: 'error', showConfirmButton: false, timer: 2000, html: '<h3>ไม่พบข้อมูลในระบบ กรุณาตรวจสอบอีกครั้ง</h3>' })
        }
      } else {
        this.email_user = ''
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>โปรดตรวจสอบอีกครั้ง</h3>' })
      }
      // console.log('test', response)
    },
    async getListPositions () {
      const data = {
        tax_id: this.taxID
      }
      await this.$store.dispatch('actionsListPositions', data)
      const positions = this.$store.state.ModuleBusiness.stateListPositions
      this.itemPosition = positions.data.list_positions.filter(position => position.active_status === 'active')
    },
    async AddUserPositions () {
      this.$store.commit('openLoader')
      this.disableButton = true

      // เช็ค values มีค่าและไม่เป็น array ว่าง
      if (!this.values || this.values.length === 0) {
        this.disableButton = false
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          title: 'ไม่มีข้อมูลตำแหน่งที่เลือก',
          text: 'กรุณาเลือกตำแหน่งก่อนที่จะบันทึก'
        })
        return
      }

      // ทำการรวมค่าเก่าและใหม่เข้าด้วยกัน
      this.listPositionOld = this.values.map(position => position.position_id)
      this.listPositionNew = this.values.map(position => position.id)
      this.listAll = [...this.listPositionOld, ...this.listPositionNew]

      // filter ค่าที่เป็น null หรือ undefined ออก
      const filteredList = this.listAll.filter(item => item !== null && item !== undefined)

      var dataAssignPositions = {
        tax_id: this.taxID,
        assigned_user_id: this.user_id,
        assigned_position_id: filteredList
      }

      // console.log(dataAssignPositions)

      try {
        await this.$store.dispatch('actionsAssignPositionsUser', dataAssignPositions)
        var response = await this.$store.state.ModuleBusiness.stateAssignPositionsUser

        if (response.result === 'SUCCESS') {
          // this.modalEditPosition = false
          this.disableButton = false
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'ดำเนินการสำเร็จ'
          })
          this.$EventBus.$emit('getBusinessTaxID')
          this.modalAddPosition = false
        } else {
          this.disableButton = true
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'เกิดข้อผิดพลาด',
            text: 'กรุณาติดต่อเจ้าหน้าที่'
          })
          this.$store.commit('closeLoader')
        }
      } catch (error) {
        this.disableButton = false
        this.$store.commit('closeLoader')
        console.error('An error occurred:', error)
        this.$swal.fire({
          icon: 'error',
          title: 'เกิดข้อผิดพลาด',
          text: 'ไม่สามารถดำเนินการได้'
        })
      }
    }
  }
}
</script>

<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
