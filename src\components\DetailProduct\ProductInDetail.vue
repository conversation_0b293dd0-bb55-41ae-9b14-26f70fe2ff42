<template>
  <v-container class="px-0">
    <v-row align="center" justify="space-between" v-if="!MobileSize && !IpadSize" class="ml-2">
      <span class="pt-1 mt-2 pl-0 diaplayWeb HeaderWebProduct" style="font-size: 24px; font-weight: 700; color: #3EC6B6;">{{ header }}</span>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#3EC6B6" class="mt-4 diaplayWeb" plain style="font-weight: 600; text-transform: none;">{{ $t('ProductPage.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
      <!-- <h2 class="pt-1 ml-4 mt-2 mb-4 displayIPAD HeaderIpadProduct">{{ header }}</h2> -->
      <!-- <h2 class="pt-1 ml-3 mt-2 mb-4">{{ header }}</h2>
      <v-divider class="mt-3 ml-4" style="color:#DAF1E9; border:1px solid" ></v-divider>
      <v-btn  text @click="GetAllProduct" color="#008E00" class="mt-4 displayIPAD" plain style="font-weight: 600;">{{ $t('ProductPage.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
      <v-btn  text @click="GetAllProduct" color="#008E00" class="mt-4 displayMobile" plain style="font-weight: 600;">{{ $t('ProductPage.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn> -->
    </v-row>
    <v-row v-if="MobileSize" align="center" justify="space-between" class="mb-2 ml-0">
      <h2 style="font-size: 18px; color:#3EC6B6; font-weight: 600;" class="">{{ header }}</h2>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct === 'product_interested' ? 'recommended_product_for_you' : this.typeProduct === 'same_shop' ? 'same_shop' : ''}?page=1`" color="#3EC6B6" class="mb-2" plain style="font-weight: 600; text-transform: none;">{{ $t('ProductPage.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
    </v-row>
    <v-row v-if="IpadSize" align="center" justify="space-between" class="mb-2 ml-1">
      <h2 style="color:#3EC6B6; font-weight: 600;" class="">{{ header }}</h2>
      <v-btn text @click.prevent="GetAllProduct()" :href="`${path}` + `ListProduct/${this.typeProduct}?page=1`" color="#3EC6B6" class="mb-2" plain style="font-weight: 600; text-transform: none;">{{ $t('ProductPage.ViewAll') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-2"><v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn></v-btn>
    </v-row>
    <!-- <pre>{{propsData}}</pre> -->
    <vue-horizontal-list :items='propsData' :options='options' v-if="!MobileSize && !IpadSize && HorizontalIpad > 1264">
      <template v-slot:nav-prev>
        <div><v-icon color="#008E00" size="32">mdi-chevron-left</v-icon></div>
      </template>
      <template v-slot:nav-next>
        <div><v-icon color="#008E00" size="32">mdi-chevron-right</v-icon></div>
      </template>
      <template v-slot:default='{ item }'>
        <a-skeleton :loading="check === true ? !loading : loading">
          <CardProducts :itemProduct='item' />
        </a-skeleton>
      </template>
    </vue-horizontal-list>
    <v-row justify="start" dense v-if="!MobileSize && !IpadSize && HorizontalIpad < 1264">
      <v-col cols="3" v-for="(item, index) in propsData.slice(0, 6)" :key="index" class="px-2 mb-4 mt-3" style="display: flex; justify-content: center;">
        <CardProductsResponsive :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row justify="start" dense v-if="IpadSize">
      <v-col cols="6" sm="3" xs="6" v-for="(item, index) in propsData.slice(0, 4)" :key="index" class="px-0 mb-4" style="display: flex; justify-content: center;">
        <CardProductsResponsive :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row justify="start" dense v-else-if="MobileSize && !IpadSize" class="px-0">
      <v-col cols="6" sm="6" xs="6" v-for="(item, index) in propsData.slice(0, 4)" :key="index" class="px-1" style="display: flex; justify-content: center;">
        <CardProductsResponsive :itemProduct='item' />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'
import { Skeleton } from 'ant-design-vue'
export default {
  props: ['propsData', 'typeProduct', 'check', 'ShopID', 'hierachy'],
  components: {
    VueHorizontalList,
    'a-skeleton': Skeleton,
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      options: {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 16
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      loading: true,
      cleandata: [],
      path: process.env.VUE_APP_DOMAIN
    }
  },
  async created () {
    await this.getHeaderThai()
    this.cleanData()
  },
  computed: {
    HorizontalIpad () {
      const val = window.innerWidth
      return val
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    GetAllProduct () {
      localStorage.setItem('hierachy', this.hierachy)
      this.$router.push(`/ListProduct/${this.typeProduct}?page=1`).catch(() => {})
    },
    getHeaderThai () {
      if (this.typeProduct === 'all_product') {
        this.header = this.$t('ProductPage.All')
      } else if (this.typeProduct === 'similar_products') {
        this.header = this.$t('ProductPage.RelatedCategory')
      } else if (this.typeProduct === 'recommended_product_for_you') {
        this.header = this.$t('ProductPage.RecommendedProducts')
      } else if (this.typeProduct === 'same_shop') {
        this.header = this.$t('ProductPage.SameStoreProducts')
      }
    },
    cleanData () {
      // console.log(this.propsData)
      var array1 = this.propsData
      var i
      for (i = 0; i < 6; i++) {
        this.cleandata.push(array1[i])
      }
      // console.log(this.cleandata)
    }
  }
}
</script>
<style lang="scss"  scoped>
.slick-slider {
  width: 100%;
  height: 100%;
  padding: 1%;
  ::v-deep .slick-arrow:before {
    color: #008E00;
    font-size: 30px;
  }
}
</style>

<style scoped>
.container {
  max-width: 1250px;
}
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>

<style>
.vhl-btn-left {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  /* background: #DCFFDC !important; */
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  z-index: 2;
  opacity: 0.7;
}
.vhl-btn-right {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  /* background: #DCFFDC !important; */
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  z-index: 2;
  opacity: 0.7;
}
.vhl-btn-right {
  margin-left: auto;
  /* margin-right: -16px !important; */
}
.vhl-btn-left[data-v-8b923bbc] {
  /* margin-left: -16px !important; */
  margin-right: auto;
}
</style>
