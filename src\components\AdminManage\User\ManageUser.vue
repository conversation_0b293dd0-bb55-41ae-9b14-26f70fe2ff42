<template>
  <v-container>
    <v-row dense justify="center">
      <v-col cols="12">
        <v-row class="mb-2 ml-1">
          <v-col cols="6" align="start" class="pl-0">
            <span style="font-weight: bold; font-size: 28px;">{{ userAction }}</span>
          </v-col>
          <v-col cols="6" align="end">
            <v-btn class="ml-2" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createUserByEmail()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มด้วยอีเมลที่มีอยู่แล้ว</v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12">
        <v-card elevation="0" width="100%" height="100%">
          <v-row justify="center" dense class="mx-5">
            <v-col cols="12" md="11" sm="12" xs="12">
              <v-form ref="form" v-model="valid" lazy-validation>
                <v-row justify="center" dense class="mt-5">
                  <!-- ชื่อ(ภาษาไทย)  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>ชื่อ (ภาษาไทย)<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-text-field v-model="firstnameTH" dense persistent-hint outlined :rules="Rules.nameTHRules"></v-text-field>
                    </v-row>
                  </v-col>
                  <!-- นามสกุล(ภาษาไทย)  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>นามสกุล (ภาษาไทย)<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-text-field v-model="lastnameTH" dense persistent-hint outlined :rules="Rules.nameTHRules"></v-text-field>
                    </v-row>
                  </v-col>
                  <!-- ชื่อ(ภาษาอังกฤษ)  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>ชื่อ (ภาษาอังกฤษ)<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-text-field v-model="firstnameEN" dense persistent-hint outlined :rules="Rules.nameENRules"></v-text-field>
                    </v-row>
                  </v-col>
                  <!-- นามสกุล(ภาษาอังกฤษ)  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>นามสกุล (ภาษาอังกฤษ)<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-text-field v-model="lastnameEN" dense persistent-hint outlined :rules="Rules.nameENRules"></v-text-field>
                    </v-row>
                  </v-col>
                  <!-- อีเมล  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>อีเมลผู้ใช้งาน<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-text-field v-model="email" dense outlined maxLength="10"  :rules="Rules.email"></v-text-field>
                    </v-row>
                  </v-col>
                  <!-- หมายเลขโทรศัพท์  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>หมายเลขโทรศัพท์<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-text-field v-model="tel" dense outlined maxLength="10"  :rules="Rules.tel"  oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-row>
                  </v-col>
                  <!-- สิทธิ์การเข้าใช้งาน -->
                  <v-col cols="12" md="12" sm="12" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <v-col cols="2">
                        <span>สิทธิ์การเข้าใช้งาน<span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="3">
                        <v-checkbox class="ml-0 pl-0 pt-0 mt-0" v-model="item.selected" v-for="(item, i) in permissionMenu" :key="i" :label="item.value">
                          <template v-slot:label>
                            <div>{{item.name}}</div>
                          </template>
                        </v-checkbox>
                      </v-col>
                      <v-col cols="7" class="mt-12">
                        <v-row v-if="permissionMenu[1].selected === true">
                          <v-col cols="4">
                            <span>จำนวนวันรอการอนุมัติ<span style="color: red;">*</span></span>
                          </v-col>
                          <v-col cols="4">
                            <v-row dense justify="start">
                              <v-text-field style="height: 20px;" v-model="approveDate" dense outlined></v-text-field>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="mb-5 pb-5">
                    <v-row justify="end" dense>
                      <v-btn text color="#27AB9C" @click="backToUserPage()" class="mr-2">ย้อนกลับ</v-btn>
                      <v-btn color="primary" @click="saveAddUser()">บันทึก</v-btn>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog v-model="dialog" width="600">
      <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>กรุณากรอกอีเมลเพื่อค้นหาอีเมลที่มีอยู่แล้ว</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialog()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-row dense class="ma-2" justify="center">
            <!-- อีเมล  -->
            <v-col cols="12">
              <v-row dense justify="start" class="ml-1">
                <v-text-field v-model="createByEmail" dense outlined maxLength="10" placeholder="<EMAIL>" :rules="Rules.email"></v-text-field>
              </v-row>
            </v-col>
            <v-col cols="12">
              <v-row justify="end" dense>
                <v-btn color="primary" @click="searchEmail()">บันทึก</v-btn>
              </v-row>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
// import Address2021 from '@/Thailand_Address/address2021'
export default {
  data () {
    return {
      dialog: false,
      stepper: 1,
      valid: true,
      valid1: true,
      checkbox: [],
      permissionMenu: [
        { name: 'ผู้ช่วยผู้ดูแลระบบ', value: 'adminAssistant', selected: false },
        { name: 'ผู้อนุมัติ', value: 'approver', selected: false },
        { name: 'ผู้สั่งซื้อ', value: 'buyer', selected: false }
      ],
      firstnameTH: '',
      firstnameEN: '',
      lastnameTH: '',
      lastnameEN: '',
      approveDate: '',
      tel: '',
      email: '',
      createByEmail: '',
      backpage: '',
      userAction: '',
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        nameTHRules: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[ก-๏\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        nameENRules: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[A-Za-z_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        emailRule: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[a-zA-Z0-9._-]+@[a-zA-Z]+([.]?[a-zA-Z])*(\.[a-zA-Z]{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  created () {
    if (this.$route.query.Status === 'Create') {
      this.userAction = 'เพิ่มผู้ใช้งาน'
    } else {
      this.userAction = 'แก้ไขผู้ใช้งาน'
      this.getEditUser()
    }
    this.$EventBus.$emit('changeTitle', 'ผู้ใช้งาน')
    this.$EventBus.$emit('changeNavAdminManage')
  },
  methods: {
    getEditUser () {
      const data = {
        firstnameTH: 'ทดสอบ ผู้ใช้งาน',
        firstnameEN: 'test user',
        lastnameTH: 'ทดสอบนามสกุล',
        lastnameEN: 'testLastname',
        email: '<EMAIL>',
        verifyEmail: true,
        tel: '0888233335',
        permission: { admin: true, adminAssistant: false, approver: true, buyer: true },
        userBuyerSetting: { group: '', department: '', approverType: '', approver: '' },
        userEmployeetSetting: { group: '', department: '', approverType: '', approver: '' },
        approveDate: '4'
      }
      this.firstnameTH = data.firstnameTH
      this.lastnameTH = data.lastnameTH
      this.firstnameEN = data.firstnameEN
      this.lastnameEN = data.lastnameEN
      this.email = data.email
      this.tel = data.tel
      this.verifyEmail = data.verifyEmail
      this.approveDate = data.approveDate
      data.permission.adminAssistant === true ? this.permissionMenu[0].selected = true : this.permissionMenu[0].selected = false
      data.permission.approver === true ? this.permissionMenu[1].selected = true : this.permissionMenu[1].selected = false
      data.permission.buyer === true ? this.permissionMenu[2].selected = true : this.permissionMenu[2].selected = false
      // console.log('firstnameTH', this.firstnameTH)
    },
    createUserByEmail () {
      this.dialog = !this.dialog
      // console.log('create by email')
    },
    saveAddUser () {
      // console.log('Save Add User')
    },
    searchEmail () {
      // console.log('searchEmail')
    },
    backToUserPage () {
      this.backpage = localStorage.getItem('backToPage')
      if (this.backpage === 'usersCompany') {
        this.$router.push({ path: '/usersCompany' }).catch(() => {})
      } else if (this.backpage === 'detailUserCompany') {
        this.$router.push({ path: '/detailUserCompany' }).catch(() => {})
      }
    },
    closeDialog () {
      this.dialog = !this.dialog
    }
  }
}
</script>

<style scoped>
</style>
