<template>
  <v-container :style="MobileSize ? 'background-color: white;' : ''">
    <v-dialog
      v-model="dialog"
      content-class="elevation-0"
      persistent
    >
      <v-card
        max-width="450px"
        style="border-radius: 24px"
      >
        <v-card-title>
          <v-row no-gutters>
              <v-col class="d-flex justify-center mt-4 mb-5">
                <span style="font-size: 16px; font-weight: bold;" class="text-center ml-12">ตัวกรอง</span>
              </v-col>
              <v-btn icon dark @click="dialog = false" class="mr-4 mt-2">
                <v-icon color="#333">mdi-close</v-icon>
              </v-btn>
            </v-row>
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col class="d-flex justify-center" >
              <v-card max-width="380px" style="background-color: #f3f5f7; border-radius: 12px;" class="pa-1" flat>
                <v-row align="center" v-if="selectedDropdown === 'รายปี'">
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" color="white" @click="setFilter('รายปี')" elevation="0">
                      <span class="text-16-color">รายปี</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn :disabled="tabs === 2" style="width: 80px; border-radius: 10px;" @click="setFilter('รายเดือน')" elevation="0">
                      <span class="text-16">รายเดือน</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn :disabled="tabs === 2" style="width: 80px; border-radius: 10px;" @click="setFilter('รายวัน')" elevation="0">
                      <span class="text-16">รายวัน</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row align="center" v-if="selectedDropdown === 'รายเดือน'">
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายปี')" elevation="0">
                      <span class="text-16">รายปี</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" color="white" @click="setFilter('รายเดือน')" elevation="0">
                      <span class="text-16-color">รายเดือน</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายวัน')" elevation="0">
                      <span class="text-16">รายวัน</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row align="center" v-if="selectedDropdown === 'รายวัน'">
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายปี')" elevation="0">
                      <span class="text-16">รายปี</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายเดือน')" elevation="0">
                      <span class="text-16">รายเดือน</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" color="white" @click="setFilter('รายวัน')" elevation="0">
                      <span class="text-16-color">รายวัน</span>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row v-if="selectedDropdown === 'รายปี'">
            <v-col cols="2" align="center" class="d-flex align-center">
              <span>ปี:</span>
            </v-col>
            <v-col cols="10">
              <v-select
                outlined
                placeholder="เลือกปี"
                v-model="selectedYear"
                rounded
                dense
                hide-details
                :items="years"
              >
                <template slot="selection">
                  {{selectedYear + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
            </v-col>
          </v-row>
          <v-row v-if="selectedDropdown === 'รายเดือน'">
            <v-col cols="2" align="center" class="d-flex align-center">
              <span>ปี:</span>
            </v-col>
            <v-col cols="10">
              <v-select
                outlined
                placeholder="เลือกปี"
                v-model="selectedYear"
                rounded
                dense
                hide-details
                :items="years"
              >
                <template slot="selection">
                  {{selectedYear + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
            </v-col>
            <v-col cols="2" align="center" class="d-flex align-center">
              <span>เดือน:</span>
            </v-col>
            <v-col cols="10">
              <v-select
                outlined
                placeholder="เลือกเดือน"
                v-model="selectedMonth"
                rounded
                dense
                hide-details
                :items="months"
                item-text="text"
                item-value="value"
              >
              </v-select>
            </v-col>
          </v-row>
          <v-row v-if="selectedDropdown === 'รายวัน'">
            <v-col cols="2" align="center" class="d-flex align-center px-0 justify-center">
              <span style="font-size: 16px;">วันที่:</span>
            </v-col>
            <v-col cols="10">
              <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
                <template v-slot:activator = "{ on, attrs }">
                  <v-text-field
                    v-bind="attrs"
                    v-on="on"
                    v-model="daterange"
                    placeholder="วว/ดด/ปปปป"
                    dense
                    rounded
                    readonly
                    hide-details
                    class="d-inline-block ml-2 custom-text-field"
                    style="border: 1px solid #EBEBEB; border-radius: 8px;"
                  >
                    <v-spacer></v-spacer>
                    <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  style="font-size:29px !important; height: 480px !important"
                  v-model="dates"
                  scrollable
                  reactive
                  locale="Th-th"
                  range
                  no-title
                  full-width
                  :min="minDate"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-row>
                    <v-col align="end">
                      <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                      <v-btn text color="primary" @click="saveDatesMobile(dates)">ตกลง</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
          <v-row style="position: buttom;">
            <v-col cols="6" align="center" >
              <v-btn class="elevation-0" rounded color="white" @click="clearFilterDialog()">
                <span class="text-16-color">ล้างค่า</span>
              </v-btn>
            </v-col>
            <v-col cols="6" align="center">
              <v-btn class="elevation-0" block rounded color="#27ab9c" @click="submitFilterDialog()">
                <span class="white--text">ยืนยัน</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-row>
      <v-col cols="5" md="4" sm="4">
        <span style="font-size: 24px; font-weight: bold;"><v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>แดชบอร์ด</span>
      </v-col>
      <v-col cols="7" md="8" sm="8">
        <v-row>
          <v-col v-if="IpadSize || IpadProSize" cols="12" align="end" :class="MobileSize ? 'd-flex justify-start' : ''">
            <v-row>
              <v-col>
                <span style="font-size: 16px;">แสดงผล:</span>
                <v-menu offset-y :disabled="tabs === 2">
                <template v-slot:activator= "{ on, attrs }">
                  <v-btn v-bind="attrs" v-on="on" color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px">
                    <span :style="tabs === 2 ? 'font-size: 16px; font-weight: 400; color: #cccccc' : 'font-size: 16px; font-weight: 400; color: #333333'">
                      {{ selectedDropdown || 'รายปี' }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                  <v-list>
                    <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
                    <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
                    <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
            <v-row v-if="showYearDropdown">
              <v-col>
                <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
                <v-menu v-if="showYearDropdown" offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                      <span style="font-size: 16px; font-weight: 400; color: #333333">
                        {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                      </span>
                      <v-spacer></v-spacer>
                      <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
            <v-row v-if="showMonthDropdown">
              <v-col>
                <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
                <v-menu v-if="showMonthDropdown" offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                      <span style="font-size: 16px; font-weight: 400; color: #333333">
                        {{ monthName === null ? 'เลือกเดือน' : monthName }}
                      </span>
                      <v-spacer></v-spacer>
                      <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelected(month.value)">{{ month.text }}</v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" v-if="showDatePicker">
                <v-row>
                  <v-col cols="3" md="6" class="d-flex align-center justify-end">
                    <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
                  </v-col>
                  <v-col cols="9" md="6">
                    <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
                      <template v-slot:activator = "{ on, attrs }">
                        <v-text-field
                          v-bind="attrs"
                          v-on="on"
                          v-model="daterange"
                          placeholder="วว/ดด/ปปปป"
                          dense
                          rounded
                          readonly
                          hide-details
                          class="d-inline-block ml-2 custom-text-field"
                          style="border: 1px solid #EBEBEB; border-radius: 8px;"
                        >
                          <v-spacer></v-spacer>
                          <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        style="font-size:29px !important; height: 480px !important"
                        v-model="dates"
                        scrollable
                        reactive
                        locale="Th-th"
                        range
                        no-title
                        full-width
                        :min="minDate"
                        :max="
                          new Date(
                            Date.now() - new Date().getTimezoneOffset() * 60000
                          )
                            .toISOString()
                            .substr(0, 10)
                        "
                      >
                      <v-row>
                        <v-col align="end">
                          <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                          <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                        </v-col>
                      </v-row>
                      </v-date-picker>
                    </v-dialog>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col v-else-if="MobileSize" cols="12" class="d-flex justify-end">
            <v-btn
              rounded
              outlined
              color="#27ab9c"
              @click="dialog = true"
              dense
            >
              <v-icon color="#27ab9c">mdi-filter-outline</v-icon>
              <span>ตัวกรอง</span>
            </v-btn>
          </v-col>
          <v-col v-else cols="12" align="end" :class="MobileSize ? 'd-flex justify-start' : ''">
            <span style="font-size: 16px;">แสดงผล:</span>
            <v-menu offset-y :disabled="tabs === 2">
            <template v-slot:activator= "{ on, attrs }">
              <v-btn v-bind="attrs" v-on="on" color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px">
                <span :style="tabs === 2 ? 'font-size: 16px; font-weight: 400; color: #cccccc' : 'font-size: 16px; font-weight: 400; color: #333333'">
                  {{ selectedDropdown || 'รายปี' }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
              <v-list>
                <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
                <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
                <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
              </v-list>
            </v-menu>
            <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
            <v-menu v-if="showYearDropdown" offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">
                    {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                  </span>
                  <v-spacer></v-spacer>
                  <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
              </v-list>
            </v-menu>
            <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
            <v-menu v-if="showMonthDropdown" offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">
                    {{ monthName === null ? 'เลือกเดือน' : monthName }}
                  </span>
                  <v-spacer></v-spacer>
                  <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelected(month.value)">{{ month.text }}</v-list-item>
              </v-list>
            </v-menu>
            <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
            <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
              <template v-slot:activator = "{ on, attrs }">
                <v-text-field
                  v-bind="attrs"
                  v-on="on"
                  v-model="daterange"
                  placeholder="วว/ดด/ปปปป"
                  dense
                  rounded
                  readonly
                  hide-details
                  class="d-inline-block ml-2 custom-text-field"
                  style="border: 1px solid #EBEBEB; border-radius: 8px;"
                >
                  <v-spacer></v-spacer>
                  <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                style="font-size:29px !important; height: 480px !important"
                v-model="dates"
                scrollable
                reactive
                locale="Th-th"
                range
                no-title
                full-width
                :min="minDate"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-row>
                  <v-col align="end">
                    <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                    <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                  </v-col>
                </v-row>
              </v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <a-tabs @change="selectTabs">
          <a-tab-pane :key="0"><span slot="tab">ข้อมูลร้านค้า </span></a-tab-pane>
          <a-tab-pane :key="1"><span slot="tab">ข้อมูลรายได้ </span></a-tab-pane>
          <a-tab-pane :key="2"><span slot="tab">ข้อมูลรายการได้รับเงิน </span></a-tab-pane>
        </a-tabs>
      </v-col>
    </v-row>
    <v-row v-if="tabs === 0">
      <v-col>
        <DetailAllShopDashboard :payload="payload" />
      </v-col>
    </v-row>
    <v-row v-if="tabs === 1">
      <v-col>
        <IncomeDashboard :payload="payload" :selectedDropdown="selectedDropdown" :selectedYear="selectedYear" :selectedMonth="selectedMonth" :dates="dates"/>
      </v-col>
    </v-row>
    <v-row v-if="tabs === 2">
      <v-col>
        <TransferInfo :payloadTransfer="payloadTransfer"/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    DetailAllShopDashboard: () => import(/* webpackPrefetch: true */ '@/components/Business/DashboardPartnerTab/DetailAllShopDashboard'),
    IncomeDashboard: () => import(/* webpackPrefetch: true */ '@/components/Business/DashboardPartnerTab/IncomeDashboard'),
    TransferInfo: () => import(/* webpackPrefetch: true */ '@/components/Business/DashboardPartnerTab/TransferInfo')
  },
  data () {
    return {
      tabs: 0,
      selectTypeFilter: 'year',
      selectedDropdown: 'รายปี',
      startDays: null,
      endDate: null,
      showYearDropdown: true,
      showMonthDropdown: false,
      showDatePicker: false,
      modalDateSelect: false,
      monthName: null,
      selectedYear: new Date().getFullYear(),
      years: [],
      dates: [],
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      selectedMonth: (new Date().getMonth() + 1).toString().padStart(2, '0'),
      minDate: '2022-01-01',
      maxDate: '2025-12-31',
      payload: {
        partner_code: '154',
        date_start: '2024-01-01',
        date_end: '2025-12-31'
      },
      taxId: '',
      dialog: false,
      payloadTransfer: {
        partner_code: '',
        year: new Date().getFullYear()
      },
      partnerCode: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    daterange () {
      if (this.dates.length > 1) {
        var startDate = new Date(this.dates[0]).toLocaleDateString('th-TH')
        var endDate = new Date(this.dates[1]).toLocaleDateString('th-TH')
        var dateLenght = startDate + ' - ' + endDate
        return dateLenght
      } else if (this.dates.length === 1) {
        var oneDay = new Date(this.dates[0]).toLocaleDateString('th-TH')
        return oneDay
      }
      return this.dates.join(' - ')
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardPartner' }).catch(() => {})
      }
    }
  },
  async created () {
    await this.generateYears()
    await this.getTaxId()
    await this.getPartnerCode()
  },
  methods: {
    backtoUserMenu () {
      this.$router.push('/detailbusinesssidMobileMenu')
    },
    selectTabs (item) {
      this.tabs = item
      if (this.tabs === 2) {
        this.selectedDropdown = 'รายปี'
        this.showYearDropdown = true
        this.showDatePicker = false
        this.showMonthDropdown = false
      }
    },
    onDropdownSelected (filter) {
      this.selectedDropdown = filter
      if (this.selectedDropdown === 'รายปี') {
        this.showYearDropdown = true
        this.showDatePicker = false
        this.showMonthDropdown = false
        this.onYearSelected(new Date().getFullYear())
      } else if (this.selectedDropdown === 'รายเดือน') {
        // console.log(this.selectedMonth, '555555')
        // this.onYearSelected(this.selectedYear)
        this.onYearSelected(new Date().getFullYear())
        this.showYearDropdown = true
        this.showMonthDropdown = true
        this.showDatePicker = false
      } else if (this.selectedDropdown === 'รายวัน') {
        // this.dates = getDate
        this.dates = [new Date().toISOString().substring(0, 10)]
        this.payload.date_start = this.dates[0]
        this.payload.date_end = this.dates[0]
        this.showDatePicker = true
        this.showYearDropdown = false
        this.showMonthDropdown = false
      }
    },
    async generateYears () {
      this.years = []
      const currentYear = new Date().getFullYear()
      const years = []
      for (let year = 2022; year <= currentYear; year++) {
        years.push(year)
      }
      this.years = years
      const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0')
      this.selectedMonth = currentMonth
      this.onMonthSelected(this.selectedMonth)
    },
    async onYearSelected (year) {
      if (this.selectedDropdown === 'รายเดือน') {
        // console.log('เข้ามั้ย', this.selectedDropdown)
        // this.onMonthSelected(String(new Date().getMonth() + 1).padStart(2, '0'))
      }
      this.selectedYear = year
      // this.selectedMonth = null
      if (this.selectedDropdown === 'รายปี') {
        this.payload.date_start = year + '-01-01'
        this.payload.date_end = year + '-12-31'
        this.payloadTransfer.year = year
      } else if (this.selectedDropdown === 'รายเดือน') {
        // this.payload.date_start = year + '-01-01'
        // this.payload.date_end = year + '-12-31'
        this.onMonthSelected(String(new Date().getMonth() + 1).padStart(2, '0'))
        // this.selectedMonth = ''
      }
    },
    async onMonthSelected (month) {
      // console.log(typeof this.selectedMonth, '555555')
      const thaiMonths = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const thaiDate = `${thaiMonths[month - 1]}`
      this.monthName = thaiDate
      this.selectedMonth = month
      if (this.selectedDropdown === 'รายเดือน') {
        // this.payload.date_start = this.selectedYear + '-' + month + '-01'
        // this.payload.date_end = this.selectedYear + '-' + month + '-31'
        this.payload = {
          date_start: this.selectedYear + '-' + month + '-01',
          date_end: this.selectedYear + '-' + month + '-31',
          partner_code: this.partnerCode
        }
      }
    },
    closeDateSelect () {
      this.modalDateSelect = false
      this.dates = []
    },
    async saveDates (val) {
      this.modalDateSelect = false
      this.$EventBus.$emit('confirmDatePickerDashboardParnter')
      if (this.dates.length === 1) {
        this.payload.date_start = val[0]
        this.payload.date_end = val[0]
      } else {
        this.$refs.modalDateSelect.save(val)
        await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.payload.date_start = val[0]
        this.payload.date_end = val[1]
      }
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        // this.taxId = response.data.array_business[0].owner_tax_id
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
        }
      }
      this.$store.commit('closeLoader')
    },
    async getPartnerCode (val, isNewTaxID) {
      var data = {
        id_card_num: isNewTaxID === 'newTaxId' ? val.tax_id : this.taxId
      }
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        // console.log(response.data.id_card_num, 'partner')
        this.partnerCode = response.data.partner_code
        this.payload.partner_code = response.data.partner_code
        this.payloadTransfer.partner_code = response.data.partner_code
        this.onYearSelected(this.selectedYear)
      } else {
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่สามารถดึง partner code ได้',
          showConfirmButton: false,
          timer: 2500
        })
      }
    },
    async saveDatesMobile (val) {
      if (this.dates.length === 1) {
        // this.chartDataBody.start = val[0]
        // this.chartDataBody.end = val[0]
        // this.payload.date_start = val[0]
        // this.payload.date_end = val[0]
        this.modalDateSelect = false
      } else if (this.dates.length === 0) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกวันที่'
        })
        this.dates = []
        this.chartDataBody.start = null
        this.chartDataBody.end = null
        this.series = []
        this.chartOptionsDay.xaxis.categories = []
      } else {
        this.$refs.modalDateSelect.save(val)
        await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.chartDataBody.start = val[0]
        this.chartDataBody.end = val[1]
      }
    },
    async setFilter (filter) {
      this.selectedDropdown = filter
      var datenow = new Date()
      if (filter === 'รายปี') {
        var year = datenow.getFullYear()
        // console.log(year, 'year')
        this.selectedYear = year
        var start = this.selectedYear + '-01-01'
        var end = this.selectedYear + '-12-31'
        // this.chartDataBody.start = start
        // this.chartDataBody.end = end
      } else if (filter === 'รายเดือน') {
        year = datenow.getFullYear()
        this.selectedYear = year
        var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
        this.selectedMonth = month
        this.chartDataBody.filter = 'month'
        const thaiMonths = [
          'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
          'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
        ]
        const thaiDate = `${thaiMonths[month - 1]}`
        this.monthName = thaiDate
        start = this.selectedYear + '-' + month + '-01'
        end = this.selectedYear + '-' + month + '-31'
        this.chartDataBody.start = start
        this.chartDataBody.end = end
      } else if (filter === 'รายวัน') {
        this.showDatePicker = true
        this.showYearDropdown = false
        this.showMonthDropdown = false
        this.chartDataBody.filter = 'day'
        await this.formatCurrentDate()
      }
    },
    async formatCurrentDate () {
      var datenow = new Date()
      var year = datenow.getFullYear()
      var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
      var day = datenow.getDate().toString().padStart(2, '0')
      var formattedDate = `${year}-${month}-${day}`
      this.dates = [formattedDate]
    },
    async submitFilterDialog () {
      this.dialog = false
      this.mobileTitlesSelected = 'ข้อมูลการขายสินค้าผ่านลิงค์ affiliate'
      if (this.selectedDropdown === 'รายปี') {
        // this.chartOptions.xaxis.categories = []
        // this.onYearSelectedMobile()
        await this.onYearSelected(this.selectedYear)
      } else if (this.selectedDropdown === 'รายเดือน') {
        this.chartOptionsMonth = []
        this.monthDate = []
        await this.onMonthSelected(this.selectedMonth)
      } else if (this.selectedDropdown === 'รายวัน') {
        if (this.dates.length === 1) {
          this.payload.date_start = this.dates[0]
          this.payload.date_end = this.dates[0]
        } else {
          this.$refs.modalDateSelect.save(this.dates)
          await this.dates.sort((a, b) => {
            var startDay = new Date(a)
            var endDay = new Date(b)
            return startDay - endDay
          })
          this.payload.date_start = this.dates[0]
          this.payload.date_end = this.dates[1]
        }
        // if (this.chartDataBody.start !== null) {
        //   this.chartOptionsDay.xaxis.categories = []
        //   this.dialog = false
        // } else if (this.chartDataBody.start === null || this.dates.length === 0) {
        //   this.$swal.fire({
        //     showConfirmButton: false,
        //     timer: 1500,
        //     timerProgressBar: true,
        //     icon: 'error',
        //     title: 'กรุณาเลือกวันที่'
        //   })
        //   this.chartOptionsDay.xaxis.categories = []
        //   this.dates = []
        //   this.series = []
        // }
      }
    },
    async clearFilterDialog () {
      this.selectedMonth = null
      this.selectedYear = null
      this.dates = []
    },
    backtoMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    }
  }
}
</script>

<style>

</style>
