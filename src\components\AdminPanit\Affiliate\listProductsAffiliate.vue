<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card  width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" class="pl-0" v-if="!MobileSize">
        <v-icon color="#27AB9C" size="30" @click="backtoPage">mdi-chevron-left</v-icon>รายการสินค้า ร้านค้า
        <span style="color: #27AB9C; margin-left: 5px;">{{ sellerName }}</span>
      </v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoPageMobile">mdi-chevron-left</v-icon>รายการสินค้า ร้านค้า
        <span style="color: #27AB9C; margin-left: 5px;">{{ sellerName }}</span>
      </v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="searchName" @change="getDataTable(options.page = 1)" placeholder="ค้นหาจากชื่อสินค้า affiliate" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
            <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อสินค้าทั้งหมด {{ totalItems }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายชื่อสินค้าทั้งหมด {{ totalItems }} รายการ</span>
          </v-col>
        </v-row>
        <v-row :class="IpadSize ? 'pl-3' : MobileSize ? 'pa-0': 'px-10'">
          <v-col :cols="IpadProSize ? 4 : IpadSize || MobileSize ? 6 : 3" v-for="(item, index) in tableData" :key="index">
            <v-hover v-slot="{ hover }">
              <v-card outlined class="rounded-lg px-2 custom-card" height="100%" width="220px" :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;">
                <v-col cols="12" md="12" class="px-0 pb-0">
                  <v-img v-lazyload
                    src="@/assets/NoImage.png"
                    height="115"
                    width="100%"
                    style="border-radius: 8px;"
                    v-if="item.media_path === null || item.media_path === ''"
                  >
                  </v-img>
                  <v-img
                    v-lazyload
                    :src="item.media_path"
                    height="115"
                    width="100%"
                    style="border-radius: 8px;"
                    contain
                    v-else
                    loading="lazy"
                    class="align-start"
                  >
                  </v-img>
                </v-col>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-col cols="12" md="12" class="px-0 pb-0">
                      <v-row no-gutters>
                        <v-col cols="12" md="10">
                          <p v-bind="attrs" v-on="on" :class="MobileSize || IpadSize || IpadProSize ? 'mb-0 text-truncate' : 'mb-0 text-truncate d-inline-block'" style="max-width: 180px; font-size: 16px; font-weight: 700;">{{ item.name }}</p>
                        </v-col>
                      </v-row>
                    </v-col>
                  </template>
                  <span>{{ item.name }}</span>
                </v-tooltip>
                <v-card-text class="pt-4 px-0">
                  <div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                      <span style="font-size: 20px; font-weight: 700;">฿ {{ item.price }}</span>
                      <p class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">ขายแล้ว {{ item.sold }} ชิ้น</p>
                    </div>
                  </div>
                </v-card-text>
                <v-card-text class="pa-0">
                  <div>
                    <span style="color: #27AB9C" v-if="item.commission_type === 'percent'">
                      อัตราค่าคอมมิชชั่น {{ item.commission_rate }} %
                    </span>
                    <span style="color: #27AB9C" v-if="item.commission_type === 'baht'">
                      อัตราค่าคอมมิชชั่น {{ item.commission_rate }} บาท
                    </span>
                    <span style="color: #27AB9C" v-if="item.commission_type === null">
                      อัตราค่าคอมมิชชั่น {{ item.commission_rate }}
                    </span>
                  </div>
                  <div>
                    <v-row no-gutters justify="end" align="center">
                      <v-col cols="auto" class="pt-2 pb-2">
                        <a :href="item.url" target="_blank">
                          <v-btn class="white--text" color="#27AB9C">ดูสินค้า</v-btn>
                        </a>
                      </v-col>
                    </v-row>
                  </div>
                </v-card-text>
              </v-card>
            </v-hover>
          </v-col>
        </v-row>
        <v-row justify="center" class="mt-6">
          <v-pagination
            color="#27AB9C"
            v-model="options.page"
            :length="totalPages"
            :total-visible="7"
            circle
          ></v-pagination>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      sellerName: '',
      id: null,
      searchName: '',
      tableData: [],
      options: {
        current: 1,
        page: 1,
        itemsPerPage: 16
      },
      totalItems: 0,
      totalPages: 0,
      searchPages: 1
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // fillterItems () {
    //   return this.tableData.filter(item => item.name.toLowerCase().includes(this.search.toLowerCase()))
    // }
  },
  watch: {
    'options.page' () {
      this.getDataTable()
      window.scrollTo(0, 0)
    },
    MobileSize (val) {
      if (val) {
        this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
      } else {
        localStorage.setItem('pathAdmin', 'sellerJoinAffiliate')
        this.$router.push({ path: '/AdminPanit' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.id = this.$route.query.id
    this.sellerName = this.$route.query.name
    this.getDataTable()
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
    },
    backtoPageMobile () {
      this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
    },
    async getDataTable () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.id,
        count: this.options.itemsPerPage,
        pages: this.options.page,
        search: this.searchName
      }
      await this.$store.dispatch('actionsGetProductSellerAffiliate', data)
      const response = await this.$store.state.ModuleAdminManage.stateGetProductSellerAffiliate
      if (response.code === 200) {
        this.tableData = response.data
        this.totalItems = response.max_items
        this.totalPages = response.max_pages
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.custom-card {
  border: 1px solid #BDE7D9 !important;
  border-color: #BDE7D9 !important
}
</style>
