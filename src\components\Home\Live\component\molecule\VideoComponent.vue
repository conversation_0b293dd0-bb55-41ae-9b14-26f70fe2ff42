<template>
  <div v-if="track">
    <h1>{{ participantIdentity + (local ? ' (You)' : '') }}</h1>
    <v-icon small class="mr-1">mdi-eye</v-icon>{{ participantCount }}
    <v-row justify="center" style="min-height: 80vh">
      <v-col cols="8" style="" align="center">
        <video ref="videoElement" :id="track.sid" width="100%"></video>
        <v-btn
          outlined
          @click="$emit('leaveRoom')"
          style="border: none;"
          class="pt-4 pb-4"
          v-if="local"
        >
          <v-icon color="red">mdi-phone-hangup</v-icon>
        </v-btn>
      </v-col>

      <v-col cols="4">
        <div style="background-attachment: scroll;">
          <v-card width="530px" height="80vh" rounded style="display: flex !important; flex-direction: column;">
            <v-card-title style="font-weight: 700; font-size: 18px; line-height: 30px; color:#333333;">
              <p>Chat Room</p>
            </v-card-title>
            <v-card-subtitle><v-divider></v-divider></v-card-subtitle>
            <v-card-text style="flex-grow: 1; overflow: auto;">
              <div>
                <v-card height="80vh" width="100%" color="#FAFAFA" style="margin-bottom: 20px; border-radius: 10px; box-shadow:0 !important;" class="d-flex" elevation="0">
                  <v-card-text>
                    <div v-for="(item, index) in listMessage" :value="index" :key="index">
                      <div class="pb-2">
                        <span style="font-weight: 700px; font-size: 18px;">{{ item.from }}</span><br/>
                        <span>{{ item.message }}</span>
                      </div>
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
            <v-card-text>
              <v-row style="margin-top: 10px;">
                <v-text-field
                  v-model="message"
                  dense
                  hide-details
                  outlined
                  style="max-width: 250px;"
                ></v-text-field>
                <v-btn color="primary" @click="sendMessage" :disabled="message == ''" class="ml-2">Send</v-btn>
              </v-row>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import {
  // eslint-disable-next-line camelcase
  DataPacket_Kind,
  RoomEvent,
  LocalVideoTrack,
  RemoteVideoTrack
} from 'livekit-client'

export default {
  props: {
    track: {
      type: [LocalVideoTrack, RemoteVideoTrack],
      required: true
    },
    participantIdentity: {
      type: String,
      required: true
    },
    local: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    },
    token: {
      type: String,
      default: ''
    },
    room: {
      type: [],
      required: true
    }
  },
  data () {
    return {
      videoElement: null,
      APPLICATION_SERVER_URL: '',
      LIVEKIT_URL: '',
      listMessage: [],
      participantCount: 0
    }
  },
  async mounted () {
    this.configureUrls()
    this.track.attach(this.$refs.videoElement)
    if (this.room) {
      // console.log('numParticipants:', this.room.numParticipants, ' numPublishers:', this.room.numPublishers)
      this.participantCount = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
      this.room.on(
        RoomEvent.TrackSubscribed,
        (_track, publication, participant) => {
          this.remoteTracksMap.set(publication.trackSid, {
            trackPublication: publication,
            participantIdentity: participant.identity
          })
          this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
        }
      )

      this.room.on(RoomEvent.ParticipantConnected, (participant) => {
        this.participantCount = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
        // console.log('Video ParticipantConnected')
        // console.log('first', participant)
      })

      this.room.on(RoomEvent.DataReceived, (payload, participant, kind, topic) => {
        // console.log('📡 การรับข้อมูลเริ่มทำงาน')
        const message = new TextDecoder().decode(payload)
        // console.log(`📩 ได้รับข้อความ: ${message}`)
        // console.log(`👤 จาก: ${participant ? participant.identity : 'ไม่ทราบ'}`)
        // console.log(`📦 ประเภท: ${kind}`)
        // console.log(`📌 หัวข้อ: ${topic}`)
        this.listMessage.push({
          from: participant ? participant.identity : 'ไม่ทราบ',
          message: message
        })
      })

      this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        this.participantCount = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
        // console.log('live ParticipantDisconnected')
        // console.log('first', participant)
      })

      this.room.on(RoomEvent.TrackUnsubscribed, (_track, publication, participant) => {
        if (participant.identity === 'Host') {
          this.$EventBus.$emit('leaveRoom')
        }
        // this.remoteTracksMap.delete(publication.trackSid)
        this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      })
    }
  },
  beforeDestroy () {
    // this.track.detach()
  },
  methods: {
    async sendMessage () {
      try {
        // this.room = new Room()
        // await this.room.connect(this.LIVEKIT_URL, this.token)
        // console.log(this.room.state)
        const message = this.message
        const data = new TextEncoder().encode(message)

        // eslint-disable-next-line camelcase
        await this.room.localParticipant.publishData(data, DataPacket_Kind.RELIABLE, {
          destinationIdentities: [],
          topic: 'chat'
        })
        // console.log('✅ ส่งข้อความสำเร็จ')

        this.listMessage.push({
          from: 'You',
          message: this.message
        })
      } catch (error) {
        console.error('❌ ส่งข้อความไม่สำเร็จ:', error)
      }
    },
    configureUrls () {
      if (!this.APPLICATION_SERVER_URL) {
        if (window.location.hostname === 'localhost') {
          this.APPLICATION_SERVER_URL = 'http://localhost:6080/'
        } else {
          this.APPLICATION_SERVER_URL = `https://${window.location.hostname}:6443/`
        }
      }

      if (!this.LIVEKIT_URL) {
        if (window.location.hostname === 'localhost') {
          this.LIVEKIT_URL = 'wss://helloworld-nt1b7zmh.livekit.cloud'
        } else {
          this.LIVEKIT_URL = `wss://${window.location.hostname}:7443/`
        }
      }
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative;
  background: #3b3b3b;
  aspect-ratio: 16/9;
  border-radius: 6px;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
}

.video-container .participant-data {
  position: absolute;
  top: 0;
  left: 0;
}

.participant-data p {
  background: #f8f8f8;
  margin: 0;
  padding: 0 5px;
  color: #777777;
  font-weight: bold;
  border-bottom-right-radius: 4px;
}

/* Media Queries */
@media screen and (max-width: 480px) {
  .video-container {
    aspect-ratio: 9/16;
  }
}
</style>
