<template>
  <div>
    <!-- Modal ค้นหาบริษัทของฉัน -->
    <v-dialog v-model="modalCompany" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('Menu.Company') }}</b></span>
              </v-col>
              <v-btn fab small @click="modalCompany = !modalCompany" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- New Select Company -->
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.titleSeller') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ countMyCompany }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8" align="end" v-if="haveOwnerCompany && list_business.length > 0">
                    <v-btn class="ml-2" rounded color="#27AB9C" height="40" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createCompany()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus</v-icon>{{ $t('ModalMenu.addCompany') }}</v-btn>
                  </v-col>
                  <v-col cols="12" md="8" sm="8" v-if="haveBusinessID !== null && list_company.length !== 0">
                    <v-text-field v-model="searchCompany" dense outlined style="border-radius: 8px;"
                      :placeholder="$t('ModalMenu.textSearchSeller')">
                      <v-icon slot="append" color="#CCCCCC">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                     :headers="headerTableCompany"
                     :items="filteredList"
                     :no-data-text="$t('ModalMenu.noData')"
                     :no-results-text="$t('ModalMenu.noResults')"
                     :items-per-page="10"
                     :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                     style="overflow-x: hidden !important;"
                    >
                     <template v-slot:[`item.index`]="{ item }">
                        {{ filteredList.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                     </template>
                     <template v-slot:[`item.companyInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="3" sm="2" md="2" class="pr-0">
                            <v-avatar v-if="MobileSize" size="50" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                            <v-avatar v-else size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col :cols="$i18n.locale === 'th' ? 7 : 6" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_th }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_th }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;" v-if="userdetail.ext_buyer === '1' && purchaser === true">
                            <v-btn text color="#27AB9C" @click="MyDetailCompany(item)" :disabled="item.id === selectCompanyID ? false : true"><span style="text-decoration: underline; font-size: small;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;" v-else-if="userdetail.ext_buyer === '1' && purchaser === false">
                            <v-btn text color="#27AB9C" @click="MyDetailCompany(item)"><span style="text-decoration: underline; font-size: small;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                     </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in filteredList" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col :cols="$i18n.locale === 'th' ? 5 : 4" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1" style="display: flex; margin: auto;" v-if="userdetail.ext_buyer === '1' && purchaser === true">
                        <v-btn text color="#27AB9C" @click="MyDetailCompany(item)" :disabled="item.id === selectCompanyID ? false : true"><span style="text-decoration: underline; font-size: 10px;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                          mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1" style="display: flex; margin: auto;" v-else-if="userdetail.ext_buyer === '1' && purchaser === false">
                        <v-btn text color="#27AB9C" @click="MyDetailCompany(item)"><span style="text-decoration: underline; font-size: 10px;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                          mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <!-- ของเก่า -->
                <!-- <v-container grid-list-xs>
                  <v-card-title>
                    <v-row dense>
                      <v-col v-if="MobileSize" cols="12" sm="6"  md="6">
                        <span style="font-size: 14px; font-weight: 700;">รายชื่อบริษัทของฉัน ( {{ countMyCompany }} รายชื่อ)</span>
                      </v-col>
                      <v-col v-else cols="12" sm="6" md="6">
                        รายชื่อบริษัทของฉัน ( {{ countMyCompany }} รายชื่อ)
                      </v-col>
                      <v-col cols="12" sm="6" md="6" align="end" v-if="haveBusinessID !== null && list_company.length === 0">
                        <v-btn class="ml-2" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createCompany()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus</v-icon>เพิ่มบริษัท</v-btn>
                      </v-col>
                    </v-row>
                  </v-card-title>
                  <v-card-text>
                    <v-row dense>
                      <v-col cols="12" md="7" sm="6" xs="12">
                        <v-text-field v-model="searchCompany" dense outlined style="border-radius: 6px;"
                          placeholder="ค้นหาชื่อบริษัท">
                          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                        </v-text-field>
                      </v-col>
                    </v-row>
                    <v-row dense no-gutters>
                      <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in filteredList" :key="index" class="mb-4">
                        <v-card width="100%" height="100%" outlined
                          style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
                          <v-card-text>
                            <v-row dense no-gutters>
                              <v-col cols="3" sm="2" md="2" class="pr-0">
                                <v-avatar v-if="MobileSize" size="50" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                                  rounded>
                                  <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                                  </v-img>
                                </v-avatar>
                                <v-avatar v-else size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                                  rounded>
                                  <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                                  </v-img>
                                </v-avatar>
                              </v-col>
                              <v-col cols="7" :class="MobileSize ? '':'pt-4 pl-0'" >
                                <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                                  {{ item.name_th }}
                                </div>
                                <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                                  {{ item.name_th }}
                                </div>
                              </v-col>
                              <v-col cols="12" sm="2" md="2" class="pt-3 pl-6" :align="MobileSize ? 'right' : ''" v-if="userdetail.ext_buyer === '1' && purchaser === true">
                                <v-btn text color="#27AB9C" @click="MyDetailCompany(item)" :disabled="item.id === selectCompanyID ? false : true">รายละเอียด <v-icon color="#27AB9C">
                                    mdi-chevron-right</v-icon>
                                </v-btn>
                              </v-col>
                              <v-col cols="12" sm="2" md="2" class="pt-3 pl-6" :align="MobileSize ? 'right' : ''" v-else-if="userdetail.ext_buyer === '1' && purchaser === false">
                                <v-btn text color="#27AB9C" @click="MyDetailCompany(item)">รายละเอียด <v-icon color="#27AB9C">
                                    mdi-chevron-right</v-icon>
                                </v-btn>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-container> -->
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Modal ค้นหาบริษัทของฉัน -->
    <v-dialog v-model="modalShopList" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'" :style="MobileSize ? 'font-size:20px;' : ''"><b>{{ $t('ModalMenu.shopList') }}</b></span>
              </v-col>
              <v-btn fab small @click="modalShopList = !modalShopList" icon :class="MobileSize ? 'mt-1' : 'mt-3' "><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- New Select Company -->
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myShop') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ countMyShop }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <!-- <v-col cols="12" md="8" sm="8" align="end" >
                    <v-btn class="ml-2" rounded color="#27AB9C" height="40" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createCompany()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus</v-icon>เพิ่มบริษัท</v-btn>
                  </v-col> -->
                  <v-col cols="12" md="8" sm="8" >
                    <v-text-field v-model="searchShopList" dense outlined style="border-radius: 8px;"
                      :placeholder="$t('ModalMenu.textSearchName')">
                      <v-icon slot="append" color="#CCCCCC">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                     :headers="headerTableShopList"
                     :items="filteredListShop"
                     :no-data-text="$t('ModalMenu.noDataShop')"
                     :no-results-text="$t('ModalMenu.noResultsShop')"
                     :items-per-page="10"
                     :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                     style="overflow-x: hidden !important;"
                    >
                     <!-- <template v-slot:[`item.index`]="{ index }">
                        {{ filteredListShop.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                        {{ index + 1 }}
                     </template> -->
                     <!-- <template v-slot:[`item.ShopInfo`]="{ item }">
                        {{ item.shop_name }}
                     </template> -->
                     <template v-slot:[`item.ShopInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="3" sm="2" md="2" class="pr-0">
                            <v-avatar v-if="MobileSize" size="50" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                            <v-avatar v-else size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn text color="#27AB9C" @click="GoToSeller(item)" ><span style="text-decoration: underline;">{{ $t('ModalMenu.selectShop') }}</span> <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                          <!-- <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;" v-if="userdetail.ext_buyer === '1' && purchaser === true">
                            <v-btn text color="#27AB9C" @click="MyDetailCompany(item)" :disabled="item.id === selectCompanyID ? false : true"><span style="text-decoration: underline;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;" v-else-if="userdetail.ext_buyer === '1' && purchaser === false">
                            <v-btn text color="#27AB9C" @click="MyDetailCompany(item)"><span style="text-decoration: underline;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col> -->
                        </v-row>
                     </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in filteredListShop" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.shop_name }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1" style="display: flex; margin: auto;">
                        <v-btn text color="#27AB9C" @click="GoToSeller(item)" ><span style="text-decoration: underline; font-size: 12px;">{{ $t('ModalMenu.selectShop') }}</span> <v-icon color="#27AB9C">
                          mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                      <!-- <v-col cols="4" class="pt-4 ml-1" style="display: flex; margin: auto;" v-else-if="userdetail.ext_buyer === '1' && purchaser === false">
                        <v-btn text color="#27AB9C" @click="MyDetailCompany(item)"><span style="text-decoration: underline; font-size: 12px;">{{ $t('ModalMenu.selectCompany') }}</span> <v-icon color="#27AB9C">
                          mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col> -->
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- List Company -->
    <v-dialog v-model="modalListCompany" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('Menu.DetailSelect.Purchaser') }}</b></span>
              </v-col>
              <v-btn fab small @click="modalListCompany = !modalListCompany" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- New Select Company -->
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myPurchaser') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ list_company.length }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-text-field v-model="searchCompany" dense outlined style="border-radius: 6px;"
                      :placeholder="$t('ModalMenu.textSearchPurchaser')">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                     :headers="headerTableCompany"
                     :items="filteredCompany"
                     :no-data-text="$t('ModalMenu.noData')"
                     :no-results-text="$t('ModalMenu.noResults')"
                     :items-per-page="10"
                     :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                     style="overflow-x: hidden !important;"
                    >
                     <template v-slot:[`item.index`]="{ item }">
                        {{ filteredCompany.map(function(x) {return x.company_id; }).indexOf(item.company_id) + 1 }}
                     </template>
                     <template v-slot:[`item.companyInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="4" sm="2" md="2" class="pr-0">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="8" :sm="$i18n.locale === 'th' ? 7 : 5" :md="$i18n.locale === 'th' ? 7 : 5" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.compant_name_th }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.compant_name_th }}
                              <!-- {{item.array_position}}<br><br>{{item}} -->
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;" >
                            <v-btn text color="#27AB9C" @click="getListPositionCompany(item.array_position, item)">
                              <span style="text-decoration: underline; font-size: small;">{{ $t('ModalMenu.selectPurchaser') }}</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                     </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in filteredCompany" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col :cols="$i18n.locale === 'th' ? 5 : 4" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.compant_name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.compant_name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="getListPositionCompany(item.array_position, item)">
                          <span style="text-decoration: underline; font-size: 10px;">{{ $t('ModalMenu.selectPurchaser') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <!-- ของเก่า -->
          <!-- <v-container grid-list-xs>
            <v-card-title>
              <v-row dense>
                <v-col  v-if="!MobileSize" cols="12">
                  รายชื่อบริษัทของฉัน ( {{ list_company.length }} รายชื่อ)
                </v-col>
                <v-col v-else cols="12">
                <span style=" font-size: 14px; font-weight: 700;">รายชื่อบริษัทของฉัน ( {{ list_company.length }} รายชื่อ)</span>
                </v-col>
              </v-row>
            </v-card-title>
            <v-card-text>
              <v-row dense>
                <v-col cols="12" md="7" sm="6" xs="12">
                  <v-text-field v-model="searchCompany" dense outlined style="border-radius: 6px;"
                    placeholder="ค้นหาชื่อบริษัท">
                    <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row dense no-gutters>
                <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in filteredCompany" :key="index"
                  class="mb-4">
                  <v-card width="100%" height="100%" outlined
                    style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
                    <v-card-text>
                      <v-row dense no-gutters>
                        <v-col cols="4" sm="2" md="2" class="pr-0">
                          <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                            rounded>
                            <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                            </v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="8" sm="7" md="7" :class="MobileSize ? '' : 'pt-4 pl-0'">
                          <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                            {{ item.compant_name_th }}
                          </div>
                          <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                            {{ item.compant_name_th }}
                          </div>
                        </v-col>
                        <v-col cols="12" sm="2" md="2" :class=" MobileSize ? '' : 'pt-3 pl-6'"  :align="MobileSize ? 'right' : ''">
                          <v-btn text color="#27AB9C" @click="getListPositionCompany(item.array_position,item)">
                            {{ $t('ModalMenu.selectCompany') }}
                            <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
          </v-container> -->
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- List Position Company -->
    <v-dialog v-model="modalListPosition" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.positionCompany') }}</b></span>
              </v-col>
              <v-btn fab small @click="modalListPosition = !modalListPosition" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 20px 10px 20px'">
              <v-card-text class="pa-0">
                <!-- New Select Position Company -->
                <v-row dense class="d-flex pb-6">
                  <v-col cols="12" md="12" sm="12" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myPosition') }} </span> <span style="font-size: 14px; font-weight: 400; color: #333333;">({{ list_position.length }} {{ $t('ModalMenu.list') }})</span>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                     :headers="headerTablePosition"
                     :items="list_position"
                     :no-data-text="$t('ModalMenu.noDataPosition')"
                     :no-results-text="$t('ModalMenu.noResultsLocation')"
                     :items-per-page="10"
                     :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                     style="overflow-x: hidden !important;"
                    >
                     <template v-slot:[`item.index`]="{ item }">
                        {{ list_position.map(function(x) {return x.role_id; }).indexOf(item.role_id) + 1 }}
                     </template>
                     <template v-slot:[`item.PositionInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="3" sm="2" md="2" class="pr-0">
                            <v-avatar v-if="MobileSize" size="50" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                            <v-avatar v-else size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" :sm="$i18n.locale === 'th'? 7 : 6" :md="$i18n.locale === 'th' ? 7 : 6" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.role_name }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.role_name }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;" >
                            <v-btn text color="#27AB9C" @click="setCompanyName(item)"><span style="text-decoration: underline;">{{ $t('ModalMenu.selectPosition') }}</span> <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                     </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in list_position" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.role_name|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.role_name }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="setCompanyName(item)">
                          <span style="text-decoration: underline; font-size: 10px;">{{ $t('ModalMenu.selectPosition') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <!-- ของเก่า -->
          <!-- <v-container grid-list-xs>
            <v-card-title>
              <v-row dense>
                <v-col v-if="MobileSize" cols="12">
                  <span style=" font-size: 12px; font-weight: 700;">รายชื่อตำแหน่งของบริษัทของฉัน ({{ list_position.length }} รายชื่อ)</span>
                </v-col>
                <v-col v-else cols="12">
                  รายชื่อตำแหน่งของบริษัทของฉัน ( {{ list_position.length }} รายชื่อ)
                </v-col>
              </v-row>
            </v-card-title>
            <v-card-text>
              <v-row dense no-gutters>
                <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in list_position" :key="index" class="mb-4">
                  <v-card width="100%" height="100%" outlined
                    style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
                    <v-card-text>
                      <v-row dense no-gutters>
                        <v-col cols="3" sm="2" md="2" class="pr-0">
                          <v-avatar v-if="MobileSize" size="50" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                            rounded>
                            <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                            </v-img>
                          </v-avatar>
                          <v-avatar v-else size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                            rounded>
                            <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                            </v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="7" sm="7" md="7" :class="MobileSize ? '-' : 'pt-4 pl-0'">
                          <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                            {{ item.role_name }}
                          </div>
                          <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                            {{ item.role_name }}
                          </div>
                        </v-col>
                        <v-col cols="12" sm="2" md="2" class="pt-3 pl-6" :align="MobileSize ?  'right' : ''">
                          <v-btn text color="#27AB9C" @click="setCompanyName(item)">เลือกตำแหน่ง <v-icon color="#27AB9C">
                              mdi-chevron-right</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
          </v-container> -->
        </v-card-text>
      </v-card>
    </v-dialog>

    <v-dialog v-model="modalBusiness" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.listBusiness') }}</b></span>
              </v-col>
              <v-btn fab small @click="modalBusiness = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myBusiness') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ listBusiness.length }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <v-col cols="12" :md="list_business.length === 0 ? '4' : '8'" :sm="list_business.length === 0 ? '4' : '8'">
                    <v-text-field v-model="searchBusiness" dense outlined style="border-radius: 6px;"
                      :placeholder="$t('ModalMenu.textSearchCor')">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4" v-if="list_business.length === 0">
                    <v-btn block color="#27AB9C" @click="gotoCreateBussiness">
                      <span class="white--text">{{ $t('ModalMenu.regisBusiness') }}</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                      :headers="headerTableBusiness"
                      :items="listBusiness"
                      :search="searchBusiness"
                      :no-data-text="$t('ModalMenu.noDataCor')"
                      :no-results-text="$t('ModalMenu.noResultsCor')"
                      :items-per-page="10"
                      :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                      style="overflow-x: hidden !important;"
                    >
                      <template v-slot:[`item.name_on_document_th`]="{ item }">
                        <v-row dense no-gutters class="py-3" style="display: flex; align-items: center;">
                          <v-col cols="3" sm="2" md="2" class="pr-0 d-flex justify-start">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="6" :sm="$i18n.locale === 'th' ? 7 : 6" :md=" $i18n.locale === 'th' ? 7 : 6" :class="MobileSize ? 'pl-2 d-flex justify-start' : ''" style="display: flex; width: 18vw;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: start;">
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <span v-bind="attrs" v-on="on">
                                    {{ item.name_on_document_th|truncate(20, '...') }}
                                  </span>
                                </template>
                                <span> {{ item.name_on_document_th }}</span>
                              </v-tooltip>
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: start; width: 35vw;">
                              {{ item.name_on_document_th }}
                            </div>
                          </v-col>
                          <v-col cols="3" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn text color="#27AB9C" @click="chooseCorporlation(item)">
                              <span style="text-decoration: underline;">{{ $t('ModalMenu.selectCor') }}</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in listBusiness" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_on_document_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.name_on_document_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="chooseCorporlation(item)">
                          <span style="text-decoration: underline; font-size: 10px;">{{ $t('ModalMenu.selectCor') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <a-popover v-model="visible" trigger="click" placement="bottomRight">
      <template slot="title" v-if="MobileSize">
        <!-- <v-avatar size="30">
          <img :src="`${imagePath}`" v-if="imagePath !== null" width="34" height="34" contain />
          <v-icon size="34" color="white" large v-else>mdi-account-circle-outline</v-icon>
        </v-avatar> -->
        <span style="color: black; font-size: 16px;" class="pt-6 pl-1 pr-1">{{ name|truncate(30, '...') }}
          <!-- <p style="font-size: 12px !important; line-height: 16px;" >({{changeRole === 'ext_buyer' ? 'ผู้ซื้อทั่วไป' : changeRole === 'purchaser' ? 'ผู้ซื้อองค์กร' : changeRole === 'admin' ? 'แอดมิน' : 'ผู้อนุมัติ' }})</p> -->
          <p style="font-size: 10px !important; line-height: 16px;" :style="checkAdminShop === 'adminShop' ? 'color: #27AB9C !important; font-weight: 600 !important;' : ''">{{(changeRole === 'ext_buyer' && checkAdminShop === '') ? 'ผู้ซื้อทั่วไป' : (changeRole === 'purchaser' && checkAdminShop === '') ? 'ผู้ซื้อองค์กร' : (changeRole === 'admin' && checkAdminShop === '') ? 'แอดมิน' : ((changeRole === 'sale_order' || changeRole === 'sale_order_no_JV' || changeRole === 'sale_order_vendor') && checkAdminShop === '') ? 'SaleOrder' : checkAdminShop === 'adminShop' ? 'ผู้ขาย' : 'ผู้อนุมัติ'}}</p>
        </span>
      </template>
      <template slot="content">
        <!-- <Menu style="width: 256px; height: 290px; overflow-y: auto; overflow-x: hidden;" mode="inline" :default-selected-keys="['0']" :selected-keys="['0']"> -->
        <Menu style="width: 256px; height: 100%; overflow-y: auto; overflow-x: hidden;" mode="inline" :default-selected-keys="['0']" :selected-keys="['0']">
          <Menuitem @click="LinkPage('userprofileMobile')" v-if="MobileSize">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-account</v-icon>{{ $t('Menu.Profile') }}
          </Menuitem>
          <Menuitem @click="LinkPage('userprofile')" v-else>
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-account</v-icon>{{ $t('Menu.Profile') }}
          </Menuitem>
          <Menuitem v-if="(Role !== 'purchaser' && Role !== 'sale_order' && Role !== 'sale_order_no_JV') && MobileSize" @click="LinkPage('pobuyerProfileMobile')">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi mdi-file-document-multiple-outline</v-icon>{{ $t('Menu.Orders') }}
          </Menuitem>
          <Menuitem v-if="(Role !== 'purchaser' && Role !== 'sale_order' && Role !== 'sale_order_no_JV') && !MobileSize" @click="LinkPage('pobuyerProfile')">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi mdi-file-document-multiple-outline</v-icon>{{ $t('Menu.Orders') }}
          </Menuitem>
          <Menuitem v-if="Role === 'sale_order_no_JV' && MobileSize" @click="LinkPage('ListOrderBySalesMobile')">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi mdi-file-document-multiple-outline</v-icon>{{ $t('Menu.Orders') }}
          </Menuitem>
          <Menuitem v-if="Role === 'sale_order_no_JV' && !MobileSize" @click="LinkPage('ListOrderBySales')">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi mdi-file-document-multiple-outline</v-icon>{{ $t('Menu.Orders') }}
          </Menuitem>
          <SubMenu v-if="$router.currentRoute.name !== 'Checkout'">
            <span slot="title">
              <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-account-convert</v-icon>{{ $t('Menu.Select') }}
            </span>
            <Menuitem v-if="(onePosition.admin_platform === true || onePosition.super_admin_platform === true) && !MobileSize" @click="LinkPage('admin')"><v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-shield-account</v-icon>{{ $t('Menu.DetailSelect.AdminSystem') }}</Menuitem>
            <Menuitem v-if="(onePosition.admin_platform === true || onePosition.super_admin_platform === true) && MobileSize" @click="LinkPage('admin')"><v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-shield-account</v-icon>{{ $t('Menu.DetailSelect.AdminSystem') }}</Menuitem>
            <!-- <div v-if="userdetail.current_role_user !== undefined">
              <SubMenu v-if="userdetail.each_partner_permission.length !== 0 && userdetail.admin === '1'">
                <span slot="title">
                  <v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-shield-account</v-icon>
                  <span>ผู้ดูแลระบบ</span>
                </span>
                <Menuitem v-for="(item, index) in userdetail.each_partner_permission" :key="index">
                  <span v-if="item.admin === 1" @click="LinkPage('admin', item.company_id)">{{ item.company_name_th}}</span>
                </Menuitem>
              </SubMenu>
            </div> -->
            <!-- <Menuitem v-if="this.onedata.user.current_role_user.seller_admin && this.onedata.user.current_role_user !== undefined" @click="LinkPage('Seller_admin')"><v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-account-settings</v-icon>ผู้ดูแลระบบผู้ขาย</Menuitem>
            <Menuitem v-if="userdetail.approve_manager === '1'" @click="LinkPage('approved')"><v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-account-multiple-check</v-icon>ผู้อนุมัติ</Menuitem> -->
            <Menuitem v-if="userdetail.ext_buyer === '1'" @click="LinkPage('ext_buyer', '')">
              <v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-account-cash</v-icon>{{ $t('Menu.DetailSelect.Buyer') }}
            </Menuitem>
            <Menuitem v-if="purchaser === true && selectCompanyName === ''" @click="getListComponent()">
              <v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-account-tie</v-icon>{{ $t('Menu.DetailSelect.Purchaser') }}
            </Menuitem>
            <SubMenu v-if="purchaser === true && selectCompanyName !== ''">
              <span slot="title" @click="getListComponent()">
                <v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-account-tie</v-icon>{{ $t('Menu.DetailSelect.Purchaser') }}
              </span>
              <Menuitem style="white-space: normal; line-height: 20px;" width="10px" height="80px"
                v-if="selectCompanyName !== ''">
                <v-icon small style="margin-right: 6px; margin-left: -4px; ">mdi-account-cash
                </v-icon>
                {{selectCompanyName}}
              </Menuitem>
            </SubMenu>
            <!-- {{ sale_order_shop.sale_order }} -->
            <!-- v-if="this.sale_order_shop.sale_order.length !== 0" -->
            <Menuitem @click="getShop()" v-if="saleOrderShopHave || saleOrderShop">
              <v-icon small style="margin-right: 6px; margin-left: -4px;">mdi-briefcase-account</v-icon>{{ $t('Menu.DetailSelect.Sales') }}
            </Menuitem>
          </SubMenu>
          <!-- <Menuitem  @click="LinkPage('pobuyerui')"><v-icon style="margin-right: 6px; margin-left: -4px;">mdi-cart</v-icon>การซื้อของฉัน</Menuitem> -->
          <!-- <Menuitem @click="LinkPage('createbusinesssid')"
            v-if="haveBusinessID === null && userdetail.one_id !== null">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-domain</v-icon>ลงทะเบียนนิติบุคคล
          </Menuitem>
          <Menuitem @click="LinkPage('detailbusinesssid')"
            v-else-if="userdetail.one_id !== null && haveBusinessID !== null">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-domain</v-icon>ดูข้อมูลทะเบียนนิติบุคคล
          </Menuitem>
          <Menuitem @click="LinkPage('Company')" v-if="userdetail.one_id !== null && haveBusinessID !== null">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-domain</v-icon>ข้อมูลบริษัท
          </Menuitem> -->
          <!-- <Menuitem :disabled="Role === 'sale_order' || Role === 'sale_order_no_JV'" @click="LinkPage('createbusinesssid')"
            v-if="(haveBusiness === false && haveCitizen === false && listBusiness.length === 0) && userdetail.one_id !== null">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-file-document-edit-outline</v-icon>
            ข้อมูลนิติบุคคล
          </Menuitem> -->
          <Menuitem :disabled="Role === 'sale_order' || Role === 'sale_order_no_JV'" @click="checkBusinessEKYC"
            v-if="userdetail.one_id !== null">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-file-document-edit-outline</v-icon>
            {{ $t('Menu.BusinessEntity') }}
          </Menuitem>
          <!-- <Menuitem vi :disabled="Role === 'sale_order' || Role === 'sale_order_no_JV'" @click="openChooseTaxId"
            v-else-if="userdetail.one_id !== null && (haveBusiness === true || haveCitizen === true || listBusiness.length !== 0) && MobileSize === false">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-file-document-outline</v-icon>
            ข้อมูลนิติบุคคล
          </Menuitem>
          <Menuitem :disabled="Role === 'sale_order' || Role === 'sale_order_no_JV'" @click="openChooseTaxId"
            v-else-if="userdetail.one_id !== null && (haveBusiness === true || haveCitizen === true || listBusiness.length !== 0) && MobileSize === true">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-file-document-outline</v-icon>
            ข้อมูลนิติบุคคล
          </Menuitem> -->
          <Menuitem :disabled="Role === 'sale_order' || Role === 'sale_order_no_JV'" @click="LinkPage('Company')" v-if="userdetail.one_id !== null && (list_business.length !== 0 || list_company.length !== 0)">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-domain</v-icon>{{ $t('Menu.Company') }}
          </Menuitem>
          <SubMenu v-if="typeUser === 'eprocurement_user' && list_seller.length !== 0  && list_seller.length <= 5">
            <span slot="title">
              <Menuitem>
                <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-storefront</v-icon>{{ $t('Menu.Shops') }}
              </Menuitem>
            </span>
            <Menuitem v-for="(item, index) in list_seller" :key="index" @click="GoToSeller(item)">
              <!-- {{ item.shop_name_th === null ? item.shop_name : item.shop_name_th }} -->
              {{ item.shop_name }}
            </Menuitem>
            <Menuitem @click="GoToCreateShop('web')" v-if="!MobileSize && (ownerShop === false && list_business.length !== 0 && list_business[0].status === 'verified')"><v-icon style="margin-right: 6px; margin-left: -4px;" small>mdi-plus-circle-outline</v-icon>{{ $t('Menu.ShopRegister') }}</Menuitem>
            <Menuitem @click="GoToCreateShop('mobile')" v-else-if="MobileSize && (ownerShop === false && list_business.length !== 0 && list_business[0].status === 'verified')"><v-icon style="margin-right: 6px; margin-left: -4px;" small>mdi-plus-circle-outline</v-icon>{{ $t('Menu.ShopRegister') }}</Menuitem>
          </SubMenu>
          <Menuitem v-if="typeUser === 'eprocurement_user' && list_seller.length !== 0 && list_seller.length > 5" @click="LinkPage('ShopList')">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-storefront</v-icon>{{ $t('Menu.Shops') }}
          </Menuitem>
          <SubMenu v-else-if="typeUser === 'eprocurement_user' && list_seller.length === 0 && list_business.length !== 0 && list_business[0].status === 'verified'">
            <span slot="title">
              <Menuitem>
                <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-storefront</v-icon>{{ $t('Menu.Shops') }}
              </Menuitem>
            </span>
            <Menuitem @click="GoToCreateShop('web')" v-if="!MobileSize">
              <v-icon style="margin-right: 6px; margin-left: -4px;" small>mdi-plus-circle-outline</v-icon>{{ $t('Menu.ShopRegister') }}
            </Menuitem>
            <Menuitem @click="GoToCreateShop('mobile')" v-else-if="MobileSize">
              <v-icon style="margin-right: 6px; margin-left: -4px;" small>mdi-plus-circle-outline</v-icon>{{ $t('Menu.ShopRegister') }}
            </Menuitem>
          </SubMenu>
          <Menuitem v-if="MobileSize">
            <div>
              <div class="pr-0" style="width: 100%; display: flex;">
                <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-translate</v-icon>
                <span style="width: 85%;">{{ $t('Menu.Language') }}</span>
                <span v-if="$i18n.locale === 'th'" style="width: 15%; text-align: end; font-weight: 600;" @click="changeLang('en')">TH</span>
                <span v-if="$i18n.locale === 'en'" style="width: 15%; text-align: end; font-weight: 600;" @click="changeLang('th')">EN</span>
              </div>
            </div>
          </Menuitem>
          <Menuitem @click="LinkPage('logout')">
            <v-icon style="margin-right: 6px; margin-left: -4px;">mdi-logout-variant</v-icon>{{ $t('Menu.Logout') }}
          </Menuitem>
        </Menu>
      </template>
      <v-btn v-if="!MobileSize && !IpadSize" elevation="0" color="#F3F5F7E5" style="width: 235px; height: 48px; padding: 4px 12px; border-radius: 8px !important;">
        <v-card class="mx-0 px-0" style="background-color: transparent;" :elevation="0">
          <v-col class="px-0 py-0">
            <v-avatar size="34">
              <v-img v-lazyload :src="`${imagePath}`" v-if="imagePath !== null && imagePath !== ''"></v-img>
              <v-icon size="34" color="#333333" large v-else>mdi-account-circle-outline</v-icon>
            </v-avatar>
          </v-col>
        </v-card>
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <!-- |truncate(15, '...') -->
            <span  v-bind="attrs" v-on="on" style="color: #333333; font-weight: 600; font-size: 16px; line-height: 22px; max-width: 165px;" class="pl-2 d-inline-block text-truncate" >{{ name }}<br/>
              <span style="font-size: 10px; line-height: 16px; font-weight: 400; color: #333333; float: left;" :style="checkAdminShop === 'adminShop' ? 'color: #27AB9C !important; font-weight: 600 !important;' : ''">{{(changeRole === 'ext_buyer' && checkAdminShop === '') ? $t('Menu.DetailSelect.Buyer') : (changeRole === 'purchaser' && checkAdminShop === '') ? $t('Menu.DetailSelect.Purchaser') : (changeRole === 'admin' && checkAdminShop === '') ? $t('Menu.DetailSelect.Admin') : ((changeRole === 'sale_order' || changeRole === 'sale_order_no_JV' || changeRole === 'sale_order_vendor') && checkAdminShop === '') ? 'SaleOrder' : checkAdminShop === 'adminShop' ? $t('Menu.DetailSelect.Seller') : $t('Menu.DetailSelect.Approver')}}</span>
            </span>
          </template>
          <span>{{ name }}</span>
        </v-tooltip>
        <v-icon class="pr-4" right color="#27AB9C">
          mdi-chevron-down
        </v-icon>
      </v-btn>
      <v-btn v-else-if="!MobileSize && IpadSize" elevation="0" color="#F3F5F7E5" style="height: 48px; padding: 4px 12px; border-radius: 8px !important;">
        <v-card class="mx-0 px-0" style="background-color: transparent;" :elevation="0">
          <v-col class="px-0 py-0">
            <v-avatar size="34" style="position: relative;">
              <v-img v-lazyload :src="`${imagePath}`" v-if="imagePath !== null && imagePath !== ''"></v-img>
              <v-icon size="34" color="#333333" large v-else>mdi-account-circle-outline</v-icon>
            </v-avatar>
          </v-col>
        </v-card>
        <span style="color: #333333; font-weight: 600;" class="pt-6 pl-1 pr-1 pb-2">{{ name|truncate(15, '...') }}<br/>
          <p style="font-size: 10px; float: left; font-weight: 400; color: #333333;" :style="checkAdminShop === 'adminShop' ? 'color: #27AB9C !important; font-weight: 600 !important;' : ''">{{(changeRole === 'ext_buyer' && checkAdminShop === '') ? $t('Menu.DetailSelect.Buyer') : (changeRole === 'purchaser' && checkAdminShop === '') ? $t('Menu.DetailSelect.Purchaser') : (changeRole === 'admin' && checkAdminShop === '') ? $t('Menu.DetailSelect.Admin') : ((changeRole === 'sale_order' || changeRole === 'sale_order_no_JV' || changeRole === 'sale_order_vendor') && checkAdminShop === '') ? 'SaleOrder' : checkAdminShop === 'adminShop' ? $t('Menu.DetailSelect.Seller') : $t('Menu.DetailSelect.Approver')}}</p></span>
          <v-icon right color="#27AB9C">
            mdi-chevron-down
          </v-icon>
      </v-btn>
      <v-btn icon elevation="0" v-else class="hidden-md-and-up pr-0 pl-0" color="#F3F5F7" v-bind:style="{'margin-Top':'-2px'}">
        <v-card class="mx-auto" width="35" height="35" style="border-radius: 50%; background-color: transparent;" :elevation="0">
          <v-col class="px-0 py-0">
            <v-avatar size="34" style="position:relative;">
              <v-img v-lazyload :src="`${imagePath}`" v-if="imagePath !== null && imagePath !== ''"></v-img>
              <v-icon size="34" color="#333333" large v-else>mdi-account-circle-outline</v-icon>
            </v-avatar>
          </v-col>
        </v-card>
      </v-btn>
    </a-popover>
    <!-- Model เลือกร้านค้า ModelShop JV -->
    <v-dialog v-model="ModelShop" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.shopList') }}</b></span>
              </v-col>
              <v-btn fab small @click="closeGetShop('ModelShop')" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- New Select Company -->
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myShop') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ listSellerFilter.length }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-text-field v-model="searchShop" dense outlined style="border-radius: 6px;"
                      :placeholder="$t('ModalMenu.textSearchName')">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                      :headers="headerTableShop"
                      :items="listSellerFilter"
                      :no-data-text="$t('ModalMenu.noDataShop')"
                      :no-results-text="$t('ModalMenu.noResultsShop')"
                      :items-per-page="10"
                      :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                      style="overflow-x: hidden !important;"
                    >
                      <template v-slot:[`item.index`]="{ item }">
                        {{ listSellerFilter.map(function(x) {return x.seller_shop_id; }).indexOf(item.seller_shop_id) + 1 }}
                      </template>
                      <template v-slot:[`item.ShopInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="4" sm="2" md="2" class="pr-0">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="8" sm="7" md="7" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn text color="#27AB9C" @click="selectPartner(item)">
                              <span style="text-decoration: underline;">{{ $t('ModalMenu.selectShop') }}</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in listSellerFilter" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.shop_name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="selectPartner(item)">
                          <span style="text-decoration: underline; font-size: 12px;">{{ $t('ModalMenu.selectShop') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <!-- <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C" style="font-weight: bold;">รายชื่อร้านค้า</font>
            </span>
            <v-btn icon dark @click="closeGetShop ()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar> -->
          <!-- <v-card-text >
            <v-row class="mt-5">
              <v-col cols="12" md="12" class="mr-0">
                <v-row dense no-gutters>
                  <v-avatar rounded size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/industry_1.png" contain max-height="36" max-width="36"></v-img>
                  </v-avatar>
                  <v-card-title>
                  <v-row dense no-gutters>
                      <p  v-if="!MobileSize" cols="12" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">
                        ร้านค้าของฉัน ( {{countValOfShopHave}} ร้าน )
                      </p>
                      <v-col v-else cols="12">
                      <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ร้านค้าของฉัน ( {{countValOfShopHave}} ร้าน )</p>
                    </v-col>
                  </v-row>
                  </v-card-title>
                </v-row>
              </v-col>
            </v-row>
            <v-row>
            <v-col cols="12" >
            <div v-for="(item, index) in list_seller" :key="index">
              <v-card outlined  style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px; margin-bottom: 16px;" v-if="item.can_use_function_in_shop.sale_order === '1'">
                  <v-card-text  >
                    <v-row dense no-gutters >
                      <v-col cols="4" sm="2" md="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="8" sm="7" md="7" :class="MobileSize ? '' : 'pt-4 pl-0'">
                        <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.shop_name_th }}
                        </div>
                        <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.shop_name_th }}
                        </div>
                      </v-col>
                      <v-col cols="12" sm="2" md="2" :class=" MobileSize ? '' : 'pt-3 pl-6'"  :align="MobileSize ? 'right' : ''">
                        <v-btn text color="#27AB9C" @click="selectPartner(item)">
                          เลือกร้านค้า
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
              </v-card>
            </div>
            </v-col>
            </v-row>
          </v-card-text> -->
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Model เลือกร้านค้า ModelShop No JV -->
    <v-dialog v-model="ModelShopNoJV" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.shopList') }}</b></span>
              </v-col>
              <v-btn fab small @click="closeGetShop('ModelShopNoJV')" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myShop') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ listSellerFilterNoJV.length }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-text-field v-model="searchShop" dense outlined style="border-radius: 6px;"
                      :placeholder="$t('ModalMenu.textSearchName')">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                      :headers="headerTableShop"
                      :items="listSellerFilterNoJV"
                      :no-data-text="$t('ModalMenu.noDataShop')"
                      :no-results-text="$t('ModalMenu.noResultsShop')"
                      :items-per-page="10"
                      :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                      style="overflow-x: hidden !important;"
                    >
                      <template v-slot:[`item.index`]="{ item }">
                        {{ listSellerFilterNoJV.map(function(x) {return x.seller_shop_id; }).indexOf(item.seller_shop_id) + 1 }}
                      </template>
                      <template v-slot:[`item.ShopInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="4" sm="2" md="2" class="pr-0">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="8" sm="7" md="7" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn text color="#27AB9C" @click="setSaleOrder(item, 'General')">
                              <span style="text-decoration: underline;">{{ $t('ModalMenu.selectShop') }}</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in listSellerFilterNoJV" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.shop_name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.shop_name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="setSaleOrder(item, 'General')">
                          <span style="text-decoration: underline; font-size: 12px;">{{ $t('ModalMenu.selectShop') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <!-- <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C" style="font-weight: bold;">รายชื่อร้านค้า</font>
            </span>
            <v-btn icon dark @click="closeGetShop ()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar> -->
          <!-- <v-card-text >
            <v-row class="mt-5">
              <v-col cols="12" md="12" class="mr-0">
                <v-row dense no-gutters>
                  <v-avatar rounded size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/industry_1.png" contain max-height="36" max-width="36"></v-img>
                  </v-avatar>
                  <v-card-title>
                  <v-row dense no-gutters>
                      <p  v-if="!MobileSize" cols="12" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">
                        ร้านค้าของฉัน ( {{countValOfShopHave}} ร้าน )
                      </p>
                      <v-col v-else cols="12">
                      <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ร้านค้าของฉัน ( {{countValOfShopHave}} ร้าน )</p>
                    </v-col>
                  </v-row>
                  </v-card-title>
                </v-row>
              </v-col>
            </v-row>
            <v-row>
            <v-col cols="12" >
            <div v-for="(item, index) in list_seller" :key="index">
              <v-card outlined  style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px; margin-bottom: 16px;" v-if="item.can_use_function_in_shop.sale_order === '1'">
                  <v-card-text  >
                    <v-row dense no-gutters >
                      <v-col cols="4" sm="2" md="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="8" sm="7" md="7" :class="MobileSize ? '' : 'pt-4 pl-0'">
                        <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.shop_name_th }}
                        </div>
                        <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.shop_name_th }}
                        </div>
                      </v-col>
                      <v-col cols="12" sm="2" md="2" :class=" MobileSize ? '' : 'pt-3 pl-6'"  :align="MobileSize ? 'right' : ''">
                        <v-btn text color="#27AB9C" @click="selectPartner(item)">
                          เลือกร้านค้า
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
              </v-card>
            </div>
            </v-col>
            </v-row>
          </v-card-text> -->
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModelShopVendor" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.shopList') }}</b></span>
              </v-col>
              <v-btn fab small @click="closeGetShop('ModelShopVendor')" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myShop') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{ listSellerFilterVendor.length }} {{ $t('ModalMenu.unitOrder') }})</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-text-field v-model="searchShop" dense outlined style="border-radius: 6px;"
                      :placeholder="$t('ModalMenu.textSearchName')">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                      :headers="headerTableShop"
                      :items="listSellerFilterVendor"
                      :no-data-text="$t('ModalMenu.noDataShop')"
                      :no-results-text="$t('ModalMenu.noResultsShop')"
                      :items-per-page="10"
                      :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                      style="overflow-x: hidden !important;"
                    >
                      <template v-slot:[`item.index`]="{ item }">
                        {{ listSellerFilterVendor.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                      </template>
                      <template v-slot:[`item.ShopInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="4" sm="2" md="2" class="pr-0">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="8" sm="7" md="7" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_th }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_th }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn :disabled="item.sale_id === -1" text color="#27AB9C" @click="setSaleOrder(item, 'Vendor')">
                            <!-- <v-btn text color="#27AB9C"> -->
                              <span style="text-decoration: underline;">{{ $t('ModalMenu.selectShop') }}</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in listSellerFilterVendor" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn :disabled="item.sale_id === -1" text color="#27AB9C" @click="setSaleOrder(item, 'Vendor')">
                        <!-- <v-btn text color="#27AB9C"> -->
                          <span style="text-decoration: underline; font-size: 12px;">{{ $t('ModalMenu.selectShop') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <!-- <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C" style="font-weight: bold;">รายชื่อร้านค้า</font>
            </span>
            <v-btn icon dark @click="closeGetShop ()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar> -->
          <!-- <v-card-text >
            <v-row class="mt-5">
              <v-col cols="12" md="12" class="mr-0">
                <v-row dense no-gutters>
                  <v-avatar rounded size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/industry_1.png" contain max-height="36" max-width="36"></v-img>
                  </v-avatar>
                  <v-card-title>
                  <v-row dense no-gutters>
                      <p  v-if="!MobileSize" cols="12" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">
                        ร้านค้าของฉัน ( {{countValOfShopHave}} ร้าน )
                      </p>
                      <v-col v-else cols="12">
                      <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ร้านค้าของฉัน ( {{countValOfShopHave}} ร้าน )</p>
                    </v-col>
                  </v-row>
                  </v-card-title>
                </v-row>
              </v-col>
            </v-row>
            <v-row>
            <v-col cols="12" >
            <div v-for="(item, index) in list_seller" :key="index">
              <v-card outlined  style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px; margin-bottom: 16px;" v-if="item.can_use_function_in_shop.sale_order === '1'">
                  <v-card-text  >
                    <v-row dense no-gutters >
                      <v-col cols="4" sm="2" md="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="8" sm="7" md="7" :class="MobileSize ? '' : 'pt-4 pl-0'">
                        <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.shop_name_th }}
                        </div>
                        <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.shop_name_th }}
                        </div>
                      </v-col>
                      <v-col cols="12" sm="2" md="2" :class=" MobileSize ? '' : 'pt-3 pl-6'"  :align="MobileSize ? 'right' : ''">
                        <v-btn text color="#27AB9C" @click="selectPartner(item)">
                          เลือกร้านค้า
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
              </v-card>
            </div>
            </v-col>
            </v-row>
          </v-card-text> -->
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Model Sale Order -->
    <!-- 750 -->
    <v-dialog v-model="ModelSaleSelectModelShop" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.saleOrderFormat') }}</b></span>
              </v-col>
              <v-btn fab small @click="closePatternSale()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <!-- New Select Type Sale Order -->
              <v-row v-if="!MobileSize">
                <v-col cols="4">
                  <v-card @click="SetTypeSaleOrder('Vendor')" height="150px" class="rounded-xl cardChooseType">
                    <v-card-text class="">
                      <v-col cols="12" class="">
                        <h1 class="text-center">Customer</h1>
                        <p class="text-center" style="color:red">{{ $t('ModalMenu.orderForCus') }}</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col cols="4">
                  <v-card @click="SetTypeSaleOrder('JV')" height="150px" class="rounded-xl cardChooseType">
                    <v-card-text class="">
                      <v-col cols="12" class="">
                        <h1 class="text-center">JV</h1>
                        <p class="text-center" style="color:red">{{ $t('ModalMenu.orderInet') }}</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col cols="4">
                  <v-card @click="SetTypeSaleOrder('General')" height="150px" class="rounded-xl cardChooseType">
                    <v-card-text >
                      <v-col cols="12" class="">
                        <h1 class="text-center">{{ $t('ModalMenu.general') }}</h1>
                        <p class="text-center" style="color:red">{{ $t('ModalMenu.externalCus') }}</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <!-- <v-row v-else class="d-block"> -->
              <v-row v-else class="d-block">
                <v-col>
                  <v-card @click="SetTypeSaleOrder('Vendor')" height="150px" class="rounded-xl cardChooseType">
                    <v-card-text class="">
                      <v-col cols="12" class="d-inline">
                        <h1 class="text-center">Customer</h1>
                        <p class="text-center" style="color:red">{{ $t('ModalMenu.orderForCus') }}</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col>
                  <v-card @click="SetTypeSaleOrder('JV')" height="150px" class="rounded-xl cardChooseType">
                    <v-card-text class="">
                      <v-col cols="12" class="d-inline">
                        <h1 class="text-center">JV</h1>
                        <p class="text-center" style="color:red">{{ $t('ModalMenu.orderInet') }}</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col>
                  <v-card @click="SetTypeSaleOrder('General')" height="150px" class="rounded-xl cardChooseType">
                    <v-card-text >
                      <v-col cols="12" class="d-inline">
                        <h1 class="text-center">{{ $t('ModalMenu.general') }}</h1>
                        <p class="text-center" style="color:red">{{ $t('ModalMenu.externalCus') }}</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <!-- <v-card-text class="pa-0">
              </v-card-text> -->
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Model Type Customer -->
    <!-- <v-dialog v-model="ModelTypeCustomer" :width="MobileSize ? '100%' : IpadSize ? '100%' : '600'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 600px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รูปแบบ Sale Oder</b></span>
              </v-col>
              <v-btn fab small @click="closePatternSale()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-row >
                <v-col cols="6">
                  <v-card @click="SetTypeCustomer('company')" height="150px" class="rounded-xl">
                    <v-card-text class="">
                      <v-col cols="12" class="">
                        <h1 class="text-center">บริษัท</h1>
                        <p class="text-center" style="color:red">* การสั่งซื้อให้องค์กรณ์บริษัท</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col cols="6">
                  <v-card @click="SetTypeCustomer('person')" height="150px" class="rounded-xl ">
                    <v-card-text >
                      <v-col cols="12" class="">
                        <h1 class="text-center">บุคคล</h1>
                        <p class="text-center" style="color:red">* การสั่งซื้อให้บุคคลธรรมดา</p>
                      </v-col>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog> -->
    <!-- Model เลือกคู่ค้า -->
    <v-dialog v-model="ModelPartner" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('ModalMenu.listPartner') }}</b></span>
              </v-col>
              <v-btn fab small @click="closeGetPartner()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- New Select Company -->
                <v-row dense class="d-flex">
                  <v-col cols="12" md="4" sm="4" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">{{ $t('ModalMenu.myPartners') }}</span> <span style="font-size: 14px; font-weight: 400; color: #333333;">( {{filteredItems.length}} {{ $t('ModalMenu.unitOrder') }} )</span>
                  </v-col>
                  <v-col cols="12" md="8" sm="8">
                    <v-text-field v-model="searchCompany" @keyup="searchPartner()"  dense outlined style="border-radius: 8px;"
                      :placeholder="$t('ModalMenu.textSearchPartners')">
                      <v-icon slot="append" color="#CCCCCC">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                      :headers="headerTablePartner"
                      :items="filteredItems"
                      :no-data-text="$t('ModalMenu.noDataPartner')"
                      :no-results-text="$t('ModalMenu.noResultsPartner')"
                      :items-per-page="10"
                      :footer-props="{'items-per-page-text':$t('ModalMenu.numberRows')}"
                      style="overflow-x: hidden !important;"
                    >
                      <template v-slot:[`item.index`]="{ item }">
                        {{ filteredItems.map(function(x) {return x.company_id; }).indexOf(item.company_id) + 1 }}
                      </template>
                      <template v-slot:[`item.PartnerInfo`]="{ item }">
                        <v-row dense no-gutters class="py-3">
                          <v-col cols="4" sm="2" md="2" class="pr-0">
                            <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                              rounded>
                              <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                              </v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="8" sm="7" md="7" :class="MobileSize ? 'pl-2' : ''" style="display: flex;">
                            <div v-if="MobileSize" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.company_name_th }}
                            </div>
                            <div v-else style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.company_name_th }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" md="2" style="display: flex; margin: auto;">
                            <v-btn text color="#27AB9C" @click="setSaleOrder(item)">
                              <span style="text-decoration: underline;">{{ $t('ModalMenu.selectPartner') }}</span>
                              <v-icon color="#27AB9C">
                                mdi-chevron-right</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                  </v-col>
                  <v-col cols="12" style="border-radius: 8px;" v-else>
                    <v-row dense v-for="(item, index) in filteredItems" :key="index">
                      <v-col cols="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="5" :class="MobileSize ? 'pl-6' : ''" style="display: flex;">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <div v-on="on" v-bind="attrs" style="font-weight: 400; font-size: 14px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                              {{ item.company_name_th|truncate(24, '...') }}
                            </div>
                          </template>
                          <span>{{ item.company_name_th }}</span>
                        </v-tooltip>
                      </v-col>
                      <v-col cols="4" class="pt-4 ml-1">
                        <v-btn text color="#27AB9C" @click="setSaleOrder(item)">
                          <span style="text-decoration: underline; font-size: 12px;">{{ $t('ModalMenu.selectShop') }}</span>
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <!-- <v-card-text>
            <v-row class="mt-5">
              <v-col cols="12" md="12" class="mr-0">
                <v-row dense no-gutters>
                  <v-avatar rounded size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/industry_1.png" contain max-height="36" max-width="36"></v-img>
                  </v-avatar>
                  <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">เลือกกลุ่มคู่ค้าของฉัน ( {{filteredItems.length}} รายชื่อ )</p>
                  <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>เลือกกลุ่มคู่ค้าของฉัน ( {{filteredItems.length}} รายชื่อ )</p>
                </v-row>
              </v-col>
            </v-row>
            <v-col cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3 pt-3' : 'pl-2 pr-2 mb-3 pt-3'">
            <v-text-field class=".rounded-lg" v-model="searchCompany" @keyup="searchPartner()"  placeholder="ค้นหาจากชื่อบริษัทผู้ซื้อ" outlined dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-row >
            <v-col cols="12" v-for="(item, index) in filteredItems" :key="index">
              <v-card outlined  style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
              <v-card width="100%" height="100%" outlined
                  style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
                  <v-card-text>
                    <v-row dense no-gutters>
                      <v-col cols="4" sm="2" md="2" class="pr-0">
                        <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;"
                          rounded>
                          <v-img v-lazyload src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="36" height="36">
                          </v-img>
                        </v-avatar>
                      </v-col>
                      <v-col cols="8" sm="7" md="7" :class="MobileSize ? '' : 'pt-4 pl-0'">
                        <div v-if="MobileSize" style="font-weight: 700; font-size: 16px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.company_name_th }}
                        </div>
                        <div v-else style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333; display: flex; align-items: center;">
                          {{ item.company_name_th }}
                        </div>
                      </v-col>
                      <v-col cols="12" sm="2" md="2" :class=" MobileSize ? '' : 'pt-3 pl-6'"  :align="MobileSize ? 'right' : ''">
                        <v-btn text color="#27AB9C" @click="setSaleOrder(item)">
                          {{ $t('ModalMenu.selectCompany') }}
                          <v-icon color="#27AB9C">
                            mdi-chevron-right</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-card>
            </v-col>
          </v-row>
          </v-card-text> -->
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- <ModalChooseCustomer ref="ModalChooseCustomer" /> -->
    <!-- <ModalChooseShopModal ref="ModalChooseShopModal" /> -->
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Popover, Menu } from 'ant-design-vue'
const Menuitem = Menu.Item
const SubMenu = Menu.SubMenu
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    'a-popover': Popover,
    Menu,
    Menuitem,
    SubMenu
    // ModalChooseCustomer: () => import('@/components/Gracz/Modal/ChooseCustomerModal'),
    // ModalChooseShopModal: () => import('@/components/Gracz/Modal/ChooseShopModal')
  },
  data () {
    return {
      list_shop: [],
      checkAdminShop: '',
      reloadRole: '',
      Role: '',
      ModelShop: false,
      ModelShopNoJV: false,
      ModelShopVendor: false,
      ModelPartner: false,
      ModelSaleSelectModelShop: false,
      modalShopList: false,
      saleOrderShop: '',
      saleOrderShopHave: '',
      // listOfShop: [],
      listOfActivePartner: [],
      ListNavbar: [
        { key: 'sub1', icon: 'user', name: 'บัญชีของฉัน', path: 'userprofile', child: [] },
        { key: 'sub2', icon: 'team', name: 'เลือกสิทธื์ผู้ใช้งาน', path: '', child: [] },
        { key: 'sub3', icon: 'shopping-cart', name: 'การซื้อของฉัน', path: 'pobuyer', child: [] },
        { key: 'sub4', icon: 'shop', name: 'ร้านค้า', path: 'seller', child: [] },
        { key: 'sub5', icon: 'shopping', name: 'ระบบสั่งซื้อออนไลน์', path: '', child: [] },
        { key: 'sub6', icon: 'export', name: 'ออกจากระบบ', path: 'logout', child: [] }
      ],
      name: '',
      imagePath: '',
      visible: false,
      superadmin: false,
      selleradmin: false,
      countMyCompany: 0,
      countMyShop: 0,
      modalListCompany: false,
      modalListPosition: false,
      purchaser: false,
      ext_buyer: false,
      company_user: false,
      searchCompany: '',
      searchShopList: '',
      search: '',
      modalCompany: false,
      myOldData: [],
      myCompanyData: [],
      // myCompanyDataPosition: [],
      onedata: [],
      role: '',
      disableRole: true,
      userdetail: [],
      changeRole: 'ext_buyer',
      haveBusinessID: '',
      haveCompany: '',
      detail: [],
      lengthShopUser: 0,
      selectCompanyName: '',
      selectDataCompany: '',
      list_company: [],
      list_position: [],
      list_seller: [],
      nameCom: '',
      companyID: '',
      selectCompanyID: '',
      onePosition: [],
      typeUser: '',
      list_business: [],
      ownerShop: false,
      haveBusiness: false,
      haveCitizen: false,
      graczData: [],
      saleOrder: 0,
      shopDetail: '',
      valueOfShop: '',
      valOfShopHave: [],
      countValOfShopHave: 0,
      countValOfShopHaveJV: 0,
      dataSearch: [],
      shopID: '',
      headerTableCompany: [
        { text: `${this.$t('ModalMenu.no')}`, value: 'index', width: '10%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('ModalMenu.companyName')}`, value: 'companyInfo', width: '90%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerTableShopList: [
        // { text: `${this.$t('ModalMenu.no')}`, value: 'index', width: '10%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('ModalMenu.shopName')}`, value: 'ShopInfo', width: '90%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerTablePosition: [
        { text: `${this.$t('ModalMenu.no')}`, value: 'index', width: '10%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('ModalMenu.companyName')}`, value: 'PositionInfo', width: '80%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerTablePartner: [
        { text: `${this.$t('ModalMenu.no')}`, value: 'index', width: '10%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('ModalMenu.partnerName')}`, value: 'PartnerInfo', width: '80%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headerTableShop: [
        { text: `${this.$t('ModalMenu.no')}`, value: 'index', width: '10%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('ModalMenu.shopName')}`, value: 'ShopInfo', width: '80%', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      searchShop: '',
      shopIsJV: [],
      shopIsVendor: [],
      shopIsNoJV: [],
      listBusiness: [],
      searchBusiness: '',
      modalBusiness: false,
      headerTableBusiness: [
        { text: `${this.$t('ModalMenu.nameCor')}`, value: 'name_on_document_th', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      haveOwnerCompany: true,
      array_business: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredList () {
      return this.myCompanyData.filter(companyData => {
        return companyData.name_th.toLowerCase().includes(this.searchCompany.toLowerCase())
      })
    },
    filteredListShop () {
      // return this.list_seller
      return this.list_seller.filter(shopData => {
        if (shopData.shop_name === null) {
          shopData.shop_name = ''
        }
        if (this.searchShopList !== '') {
          // console.log(shopData.shop_name)
          return shopData.shop_name.toLowerCase().includes(this.searchShopList.toLowerCase())
        } else {
          return shopData
        }
        // return shopData.shop_name.toLowerCase().includes(this.searchShopList.toLowerCase())
      })
    },
    filteredCompany () {
      // console.log(this.list_company)
      return this.list_company.filter(companyData => {
        if (this.searchCompany !== '') {
          return companyData.compant_name_th.toLowerCase().includes(this.searchCompany.toLowerCase())
        } else {
          return companyData
        }
      })
    },
    filteredItems () {
      const query = this.searchCompany.toLowerCase()
      return this.listOfActivePartner.filter(item => item.company_name_th.toLowerCase().includes(query))
    },
    lengthShop () {
      // console.log(this.detail)
      if (this.detail.list_shop_data !== undefined && this.detail.list_shop_data.length !== 0) {
        return this.detail.list_shop_data.length === 0 ? Boolean(false) : Boolean(true)
      } else {
        return this.onedata.user.list_shop_detail.length === 0 ? Boolean(false) : Boolean(true)
      }
    },
    listSellerFilterNoJV () {
      return this.list_seller.filter(shopData => {
        if (this.searchShop !== '') {
          if (shopData.can_use_function_in_shop.sale_order_no_jv === '1') {
            return shopData.shop_name_th.toLowerCase().includes(this.searchShop.toLowerCase())
          }
        } else {
          if (shopData.can_use_function_in_shop.sale_order_no_jv === '1') {
            return shopData
          }
        }
      })
    },
    listSellerFilterVendor () {
      return this.list_shop.filter(shopData => {
        if (this.searchShop !== '') {
          if (shopData.is_JV === 'yes') {
            return shopData.name_th.toLowerCase().includes(this.searchShop.toLowerCase())
          }
        } else {
          if (shopData.is_JV === 'yes') {
            return shopData
          }
        }
      })
    },
    listSellerFilter () {
      return this.list_seller.filter(shopData => {
        if (this.searchShop !== '') {
          if (shopData.can_use_function_in_shop.sale_order === '1' && shopData.is_JV === 'yes') {
            return shopData.shop_name_th.toLowerCase().includes(this.searchShop.toLowerCase())
          }
        } else {
          if (shopData.can_use_function_in_shop.sale_order === '1' && shopData.is_JV === 'yes') {
            return shopData
          }
        }
      })
    }
  },
  mounted () {
    this.$EventBus.$on('getUserDetailMP', this.AuthorityUser)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getUserDetailMP')
    })
  },
  async created () {
    this.$EventBus.$on('getBusinessMP', this.getListTaxID)
    this.$EventBus.$on('setPositionCompanyFromSpecialPrice', this.setCompanyFromSpecialPrice)
    this.$EventBus.$on('closeModalSuccess', this.closeModalSuccess)
    this.$EventBus.$on('openModalSuccess', this.openModalSuccess)
    this.$EventBus.$on('getUserDetail', this.getUserDetail)
    this.$EventBus.$on('getCompany', this.getCompany)
    this.$EventBus.$on('LinkPage', this.LinkPage)
    this.$EventBus.$on('AuthorityUser', this.AuthorityUser)
    this.$EventBus.$on('MyDetailCompany', this.MyDetailCompany)
    this.$EventBus.$on('Logout', this.Logout)
    this.$EventBus.$on('resetAdminShop', this.resetAdminShop)
    this.$EventBus.$on('refreshToken', this.refreshToken)
    // this.$EventBus.$on('reloadPage', this.reloadPage)
    this.$EventBus.$on('CheckPermission', this.CheckPermission)
    this.$EventBus.$on('refreshAuthorityUser', this.AuthorityUser)
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    // console.log('this.shopDetail', this.shopDetail)
    // this.getListPartner()
    // console.log(this.onedata)
    if (this.onedata.user !== undefined) {
      // this.name = this.onedata.user.username
      // this.role = this.onedata.user.current_role_user
      this.onePosition = this.onedata.user.current_role_user
      // var roleCheck = JSON.parse(localStorage.getItem('roleUser'))
      // console.log(roleCheck)
      // roleCheck.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
      this.CheckPermission()
      if (localStorage.getItem('selectCompanyName') !== undefined) {
        if (localStorage.getItem('selectCompanyName') !== null) {
          this.selectCompanyName = localStorage.getItem('selectCompanyName')
        } else {
          this.selectCompanyName = ''
        }
        // this.selectCompanyName = localStorage.getItem('selectCompanyName')
      } else {
        this.selectCompanyName = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        const compayId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        this.selectCompanyID = compayId.company.company_id
      }
    } else {
      this.name = ''
    }
    this.Role = JSON.parse(localStorage.getItem('roleUser')).role
    if (this.Role === 'sale_order') {
      this.reloadRole = 'sale'
    } else {
      this.reloadRole = JSON.parse(localStorage.getItem('roleUser')).role
    }
    // this.getShopData()
    this.getUserDetail()
    this.getCompany()
    await this.AuthorityUser()
    // this.CheckBusiness()
    await this.getListTaxID()
    // console.log('this.list_seller---->', this.list_seller)
    if (localStorage.getItem('lang') !== null) {
      var lang = localStorage.getItem('lang')
      this.changeLang(lang)
    } else {
      localStorage.setItem('lang', 'th')
      this.changeLang('th')
    }
  },
  beforeDestroy () {
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getUserDetail')
    })
  },
  watch: {
  },
  methods: {
    // SetTypeCustomer (type) {
    //   // console.log('type', type)
    //   if (type === 'company') {
    //     localStorage.setItem('general_customer', 'company_customer')
    //   } else {
    //     localStorage.setItem('general_customer', 'person_customer')
    //   }
    // },
    changeLang (value) {
      // console.log('lang =====>', value)
      localStorage.setItem('lang', value)
      this.$i18n.locale = value
      this.$forceUpdate()
    },
    async getListTaxID () {
      this.listBusiness = []
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      for (var i = 0; i < this.array_business.length; i++) {
        this.listBusiness.push(
          { name_on_document_th: this.array_business[i].name_on_document_th, business_id: this.array_business[i].business_id, tax_id: this.array_business[i].owner_tax_id }
        )
        // if (response.data.list_business.length === 0) {
        //   this.$swal.fire({
        //     title: 'คุณต้องการสมัครบัญชีนิติบุคคลของตัวเองหรือไม่',
        //     icon: 'warning',
        //     confirmButtonText: 'ตกลง'
        //   }).then(async (result) => {
        //     if (result.isConfirmed) {
        //       await this.gotoCreateBussiness()
        //       // if (this.MobileSize === false) {
        //       //   this.$router.push({ path: '/createbusinesssid' }).catch(() => {})
        //       // } else {
        //       // }
        //     }
        //   })
        // }
      }
    },
    async getShopData () {
      var data = {
        userId: this.onedata.user.user_id
      }
      await this.$store.dispatch('actionsListShopJV', data)
      var response = await this.$store.state.ModuleUser.stateListShopJV
      if (response.ok === 'y') {
        this.list_shop = response.query_result.map(result => {
          var checksale = false
          if (result.position_and_role && result.positionDetails) {
            const relevantPositions = result.positionDetails.filter(position =>
              result.position_and_role.includes(position.id)
            )
            relevantPositions.sort((a, b) => {
              const countOnes = obj => Object.values(obj).filter(v => v === 1).length
              return countOnes(b) - countOnes(a)
            })
            if (relevantPositions.length > 0) {
              const mainPosition = relevantPositions[0]
              if (mainPosition.sale_order === '1') {
                checksale = true
              }
            }
          } else {
            checksale = false
          }
          return {
            ...result,
            checksale
          }
        })
      }
      // console.log('this.list_shop', this.list_shop)
    },
    async SetTypeSaleOrder (type) {
      // console.log('type', type)
      // var role
      if (type === 'JV') {
        // role = { role: 'JV_customer' }
        // localStorage.setItem('sale_order_customer', JSON.stringify(role))
        this.ModelSaleSelectModelShop = false
        this.ModelShop = true
      } else if (type === 'General') {
        // role = { role: 'General_customer' }
        // localStorage.setItem('sale_order_customer', JSON.stringify(role))
        this.ModelSaleSelectModelShop = false
        this.ModelShopNoJV = true
      } else if (type === 'Vendor') {
        // role = { role: 'General_customer' }
        // localStorage.setItem('sale_order_customer', JSON.stringify(role))
        await this.getShopData()
        this.ModelSaleSelectModelShop = false
        this.ModelShopVendor = true
      }
    },
    closePatternSale () {
      this.ModelSaleSelectModelShop = false
    },
    resetAdminShop () {
      localStorage.removeItem('checkAdminShop')
      localStorage.removeItem('sellerShopMP')
      this.checkAdminShop = ''
    },
    searchPartner () {
      // console.log(this.searchCompany)
    },
    async selectPartner (item) {
      var val = item
      // console.log('item', item)
      for (let i = 0; i < this.list_seller.length; i++) {
        if (item.seller_shop_id === this.list_seller[i].seller_shop_id) {
          // console.log('tttt', this.list_seller[i].seller_shop_id)
          localStorage.setItem('list_shop_detail', Encode.encode(this.list_seller[i]))
          // console.log('selectPartner', this.list_seller)
        }
      }
      // console.log(val.shop_name_th + '------>', val)
      // console.log(val.seller_shop_id)
      var role = { role: 'JV_customer' }
      localStorage.setItem('sale_order_customer', JSON.stringify(role))
      this.valueOfShop = val
      await this.getListPartner(val.seller_shop_id)
      localStorage.setItem('ShopID', val.seller_shop_id)
      localStorage.setItem('ShopDetailSale', Encode.encode(val))
      this.ModelShop = false
      this.ModelPartner = true
    },
    closeGetPartner () {
      this.ModelPartner = false
    },
    async setSaleOrder (item, type) {
      // console.log('item', item, type)
      var val = item
      var typeRoleSale = type
      var role = {}
      if (typeRoleSale === 'General') {
        // console.log('NoJV----->')
        localStorage.removeItem('cusCode')
        localStorage.removeItem('AddressCustomerSale')
        localStorage.removeItem('partner_id')
        localStorage.removeItem('AddressCustomerBussinessSale')
        localStorage.removeItem('sale_order_customer')
        role = { role: 'sale_order_no_JV' }
        localStorage.setItem('roleUser', JSON.stringify(role))
        this.$EventBus.$emit('role', role.role)
        // this.$EventBus.$emit('getDetailSale')
        this.valueOfShop = val
        localStorage.setItem('ShopID', val.seller_shop_id)
        localStorage.setItem('ShopDetailSale', Encode.encode(val))
        this.$EventBus.$emit('getRole', role.role)
        await this.LinkPage('sale_order_no_JV')
        this.ModelShopNoJV = false
      } else if (typeRoleSale === 'Vendor') {
        localStorage.removeItem('cusCode')
        localStorage.removeItem('AddressCustomerSale')
        localStorage.removeItem('partner_id')
        localStorage.removeItem('AddressCustomerBussinessSale')
        localStorage.removeItem('sale_order_customer')
        role = { role: 'sale_order_vendor' }
        localStorage.setItem('roleUser', JSON.stringify(role))
        this.$EventBus.$emit('role', role.role)
        // this.$EventBus.$emit('getDetailSale')
        this.valueOfShop = val
        localStorage.setItem('ShopID', val.id)
        val.sale_order_type = 'vendor'
        localStorage.setItem('ShopDetailSale', Encode.encode(val))
        this.$EventBus.$emit('getRole', role.role)
        await this.LinkPage('sale_order_vendor')
        this.ModelShopVendor = false
      } else {
        // console.log('JV----->', val.company_id)
        localStorage.setItem('PartnerID', val.company_id)
        await this.LinkPage('sale_order', val.company_id)
        this.ModelPartner = false
      }
    },
    getShop () {
      this.shopIsJV = this.list_seller.filter(shopData =>
        shopData.can_use_function_in_shop.sale_order === '1' && shopData.is_JV === 'yes'
      )
      // this.shopIsVendor = this.list_shop.filter(shopData =>
      //   shopData.is_JV === 'yes' && shopData.shop_status === 'active'
      // )
      this.shopIsNoJV = this.list_seller.filter(shopData => shopData.can_use_function_in_shop.sale_order_no_jv === '1')
      // console.log('this.shopIsNoJV', this.shopIsNoJV)
      if (this.shopIsJV.length === 0 && this.shopIsNoJV.length !== 0) {
        this.ModelShopNoJV = true
        this.visible = false
      } else if (this.shopIsJV.length !== 0 && this.shopIsNoJV.length === 0) {
        this.ModelShop = true
        this.visible = false
      } else if (this.shopIsJV.length !== 0 && this.shopIsNoJV.length !== 0) {
        this.ModelSaleSelectModelShop = true
        this.visible = false
      }
    },
    closeGetShop (val) {
      if (val === 'ModelShop') {
        this.ModelShop = false
      } else if (val === 'ModelShopNoJV') {
        this.ModelShopNoJV = false
      } else if (val === 'ModelShopVendor') {
        this.ModelShopVendor = false
      }
    },
    async getListPartner (val) {
      this.shopID = val
      var data = {
        seller_shop_id: val
      }
      await this.$store.dispatch('actionsListPartner', data)
      var response = await this.$store.state.ModulePartner.stateListPartner
      // console.log('response', response)
      if (response.result === 'SUCCESS') {
        this.listOfActivePartner = response.data.active
      } else {
        this.$message.error(response.message)
      }
    },
    async CheckBusiness () {
      await this.$store.dispatch('actionsCheckeKYC')
      var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      if (response.result === 'SUCCESS') {
        this.haveBusiness = response.data.have_business === 'yes'
        this.haveCitizen = response.data.have_citizen === 'yes'
      }
    },
    async getBussineData () {
      await this.$store.dispatch('actionsGetBusinessData')
      var response = await this.$store.state.ModuleUser.stateGetBusiness
      // console.log(response)
      if (response.result === 'SUCCESS') {
      }
    },
    async AuthorityUser () {
      // console.log('AuthorityUser===>')
      // console.log('พี่มาแล้ว')
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      this.typeUser = response.data.type_user
      this.company_user = response.data.current_role_user.company_user
      this.ext_buyer = response.data.current_role_user.ext_buyer
      this.purchaser = response.data.current_role_user.purchaser
      this.selleradmin = response.data.current_role_user.seller_admin
      this.superadmin = response.data.current_role_user.super_admin
      this.list_business = response.data.list_business
      this.list_company = response.data.list_company
      this.array_business = response.data.array_business
      // this.haveOwnerCompany = response.data.list_business.filter(ownerCompany =>
      //   ownerCompany.business_id === this.list_business.id
      // )
      if (this.list_business.length > 0) {
        for (var i = 0; i < this.list_company.length; i++) {
          if (this.list_company[i].business_id === this.list_business[0].id) {
            this.haveOwnerCompany = false
          }
        }
      } else if (this.list_business.length === 0) {
        this.haveOwnerCompany = false
      }
      // console.log(this.haveOwnerCompany, 'haveOwnerCompany')
      // if (response.data.list_business.length !== 0) {
      //   this.$swal.fire({
      //     title: 'คุณต้องการสมัครบัญชีนิติบุคคลของตัวเองหรือไม่',
      //     icon: 'warning',
      //     confirmButtonText: 'ตกลง'
      //   }).then((result) => {
      //     if (result.isConfirmed) {
      //       this.$router.push({ path: '/createbusinesssid' }).catch(() => {})
      //     }
      //   })
      // }
      this.list_seller = response.data.list_shop_detail
      this.countMyShop = response.data.list_shop_detail.length
      this.ownerShop = response.data.is_owner_shop
      localStorage.setItem('AuthorityUser', Encode.encode(response.data.list_shop_detail))
      // console.log(response.data.list_business[0], 'list_business')
      localStorage.setItem('business_code', response.data.list_business.length === 0 ? '' : response.data.list_business[0].business_code)
      // console.log('list_seller', response)
      for (const item of this.list_seller) {
        if (item.can_use_function_in_shop.sale_order !== undefined) {
          if (item.can_use_function_in_shop.sale_order === '1' && item.is_JV === 'yes') {
            this.saleOrderShopHave = true
          }
          if (item.can_use_function_in_shop.sale_order === '1' || item.can_use_function_in_shop.sale_order_no_jv === '1') {
            this.saleOrderShop = true
          }
        }
      }
    },
    getListComponent () {
      this.modalListCompany = true
      this.visible = false
    },
    async getListPositionCompany (item, data) {
      var taxID
      this.list_company.forEach(element => {
        if (element.company_id === data.company_id) {
          taxID = element.tax_id
        }
      })
      localStorage.setItem('tax_id', taxID)
      var a = []
      this.selectCompanyName = ''
      for (let i = 0; i < item.length; i++) {
        if (item[i].purchaser) {
          a.push(item[i])
        }
      }
      this.selectDataCompany = data
      this.selectCompanyID = this.selectDataCompany.company_id
      localStorage.setItem('business_id', this.selectDataCompany.business_id)
      // console.log(this.selectDataCompany, this.selectCompanyID)
      // console.log('list_position', a)
      this.list_position = a
      // console.log(this.list_position)
      // this.myCompanyDataPosition = a
      if (this.list_position.length !== 0) {
        this.modalListCompany = false
        this.modalListPosition = true
      } else {
        var role = 'purchaser'
        localStorage.removeItem('SetRowCompany')
        const data = {
          company_id: this.selectCompanyID
        }
        await this.$store.dispatch('actionsDetailCompany', data)
        var responseCompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
        if (responseCompany.result === 'SUCCESS') {
          var listPositonInCompany = ''
          for (let i = 0; i < this.list_company.length; i++) {
            if (this.selectCompanyID === this.list_company[i].company_id) {
              // console.log('list_company----->', this.list_company[i])
              // console.log('list_company----->', this.list_company[i].can_use_function_in_company)
              // localStorage.removeItem('list_Company_detail')
              listPositonInCompany = this.list_company[i].can_use_function_in_company
              localStorage.setItem('list_Company_detail', Encode.encode(this.list_company[i]))
            }
          }
          this.modalListCompany = false
          this.LinkPage(role, this.selectDataCompany.company_id)
          localStorage.setItem('CompanyData', Encode.encode(responseCompany.data))
          if (!this.MobileSize) {
            if (listPositonInCompany.set_company === '1') {
              this.$router.push({ path: '/detailCompany' }).catch(() => {})
            } else if (listPositonInCompany.set_permission === '1') {
              this.$router.push({ path: '/ManagePosition' }).catch(() => {})
            } else if (listPositonInCompany.partner === '1') {
              this.$router.push({ path: '/Partner' }).catch(() => {})
            } else if (listPositonInCompany.report === '1') {
              this.$router.push({ path: '/Report' }).catch(() => {})
            } else if (listPositonInCompany.order === '1' || listPositonInCompany.payment === '1') {
              this.$router.push({ path: '/orderCompany' }).catch(() => {})
            }
            // this.$router.push({ path: '/detailCompany' }).catch(() => {})
          } else {
            this.$router.push({ path: '/companyMobile' }).catch(() => {})
          }
        }
      }
    },
    setCompanyName (name) {
      // console.log('NAN========', this.selectDataCompany)
      // console.log('name', name)
      this.modalListPosition = false
      var KepData = {
        company: {
          compant_name_en: this.selectDataCompany.compant_name_en,
          compant_name_th: this.selectDataCompany.compant_name_th,
          company_id: this.selectDataCompany.company_id
        },
        position: name
      }
      localStorage.setItem('SetRowCompany', Encode.encode(KepData))
      // console.log('SetRowCompany', KepData)
      // localStorage.setItem('selectCompanyName', name.role_name)
      this.selectCompanyName = `${this.$t('ModalMenu.role')}` + name.role_name + `${this.$t('ModalMenu.company')}` + this.selectDataCompany.compant_name_th
      localStorage.setItem('selectCompanyName', this.selectCompanyName)
      // console.log('selectCompanyName', this.selectCompanyName)
      // console.log('SetRowCompany', KepData)
      var val
      if (name.purchaser) {
        val = 'purchaser'
      } else {
        val = 'ext_buyer'
      }
      // alert(val)
      this.selectCompanyID = this.selectDataCompany.company_id
      // console.log('selectCompanyID', this.selectCompanyID)
      this.LinkPage(val, this.selectDataCompany.company_id)
    },
    async getCompany () {
      await this.$store.dispatch('actionslistCompany')
      var response = await this.$store.state.ModuleAdminManage.stateListCompany
      // console.log('company ===========>', response)
      if (response.result === 'SUCCESS') {
        this.myCompanyData = response.data.active
        this.countMyCompany = response.data.total_active
        this.myOldData = this.myCompanyData
      } else {
        this.myCompanyData = []
        this.countMyCompany = 0
      }
    },
    MyDetailCompany (val) {
      // console.log('val', val)
      this.$store.commit('openLoader')
      this.resetAdminShop()
      var permissionsInCompany = ''
      for (let i = 0; i < this.list_company.length; i++) {
        if (this.list_company[i].company_id === val.id) {
          permissionsInCompany = this.list_company[i].can_use_function_in_company
          localStorage.setItem('list_Company_detail', Encode.encode(this.list_company[i]))
        }
      }
      // console.log(permissionsInCompany)
      localStorage.setItem('CompanyData', Encode.encode(val))
      this.modalCompany = false
      if (!this.MobileSize) {
        this.$store.commit('closeLoader')
        if (permissionsInCompany.set_company === '1') {
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        } else if (permissionsInCompany.set_permission === '1') {
          this.$router.push({ path: '/ManagePosition' }).catch(() => {})
        } else if (permissionsInCompany.partner === '1') {
          this.$router.push({ path: '/Partner' }).catch(() => {})
        } else if (permissionsInCompany.report === '1') {
          this.$router.push({ path: '/Report' }).catch(() => {})
        } else if (permissionsInCompany.order === '1' || permissionsInCompany.payment === '1') {
          this.$router.push({ path: '/orderCompany' }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      }
    },
    searchData () {
      var findValue = this.myCompanyData
      // console.log('findData', findValue)
      if (this.searchCompany !== '' && this.searchCompany !== null) {
        // for (var i = 0; i < findValue.length; i++) {
        //   if (findValue[i].companyName === this.searchCompany) {
        //     this.myCompanyData = [findValue[i]]
        //   }
        // }
        this.myCompanyData = findValue.filter(e => e.name_th === this.searchCompany)
        // console.log('filter data=======>', this.myCompanyData)
      } else {
        this.myCompanyData = []
        this.myCompanyData = this.myOldData
      }
    },
    GoToCreateShop (val) {
      if (val === 'web') {
        this.$router.push({ path: '/createShop' }).catch(() => {})
        this.$EventBus.$emit('CheckShop')
        this.$EventBus.$emit('checkpath')
      } else if (val === 'mobile') {
        this.$router.push({ path: '/createShopMobile' }).catch(() => {})
        this.$EventBus.$emit('CheckShop')
        this.$EventBus.$emit('checkpath')
      }
    },
    async GoToSeller (item) {
      // console.log('GoToSeller', item)
      this.$store.commit('openLoader')
      localStorage.setItem('shopSellerID', item.seller_shop_id)
      var dataShop = {
        id: item.seller_shop_id,
        name: item.shop_name
      }
      localStorage.setItem('shopDetail', JSON.stringify(dataShop))
      var permissionsInShop = ''
      var shopIsJV = ''
      var listShopAttorney = []
      var isAttorney = true
      this.checkAdminShop = 'adminShop'
      localStorage.setItem('checkAdminShop', this.checkAdminShop)
      for (let i = 0; i < this.list_seller.length; i++) {
        if (item.seller_shop_id === this.list_seller[i].seller_shop_id) {
          shopIsJV = this.list_seller[i].is_JV
          // isAttorney = this.list_seller[i].is_attorney
          permissionsInShop = this.list_seller[i].can_use_function_in_shop
          localStorage.setItem('list_shop_detail', Encode.encode(this.list_seller[i]))
        }
      }
      var shopname = item.shop_name_th === null ? item.shop_name : item.shop_name_th
      await this.$store.dispatch('actionListReciver')
      var responseListReciver = await this.$store.state.ModuleShop.stateListReciver
      if (responseListReciver.ok === 'y') {
        if (responseListReciver.query_result !== null) {
          for (let j = 0; j < responseListReciver.query_result.length; j++) {
            if (responseListReciver.query_result[j].sell_shop_id === item.seller_shop_id) {
              listShopAttorney = responseListReciver.query_result[j].receiver
              isAttorney = listShopAttorney.every((listAttorney) => listAttorney.is_attorney === 'no')
            }
          }
        } else {
          isAttorney = true
        }
      }
      if (!this.MobileSize) {
        this.$EventBus.$emit('CheckShop')
        this.$EventBus.$emit('checkpath')
        this.$EventBus.$emit('getCountInTable')
        this.$EventBus.$emit('checkJVShop')
        this.$EventBus.$emit('AuthorityUsers')
        if (isAttorney === true) {
          this.$store.commit('closeLoader')
          if (permissionsInShop.manage_product === '1') {
            this.$router.push({ path: '/seller?ShopID=' + item.seller_shop_id + '&ShopName=' + shopname }).catch(() => {})
          } else if (permissionsInShop.manage_order === '1') {
            this.$router.push({ path: '/poseller' }).catch(() => {})
          } else if (permissionsInShop.manage_setting_shop === '1') {
            this.$router.push({ path: '/designShop' }).catch(() => {})
          } else if (permissionsInShop.manage_dashboard === '1') {
            if (shopIsJV === 'yes') {
              this.$router.push({ path: '/dashboard' }).catch(() => {})
            } else {
              this.$router.push({ path: '/sellerdashboard' }).catch(() => {})
            }
          } else if (permissionsInShop.manage_user_with_position === '1') {
            this.$router.push({ path: '/listShopPosition' }).catch(() => {})
          } else if (permissionsInShop.manage_setting_shop === '1') {
            this.$router.push({ path: '/SettingPartnerRequest' }).catch(() => {})
          } else if (permissionsInShop.sale_order_no_jv === '1' || permissionsInShop.manage_sale_order === '1') {
            this.$router.push({ path: '/listCustomerSaleOrder' }).catch(() => {})
          } else if (permissionsInShop.manage_approve_order === '1' || permissionsInShop.manage_sale_order === '1') {
            this.$router.push({ path: '/ManageSalesApproval' }).catch(() => {})
          } else if (permissionsInShop.manage_promotion === '1') {
            this.$router.push({ path: '/manageCoupon' }).catch(() => {})
          }
        } else {
          this.$store.commit('closeLoader')
          this.$router.push({ path: '/Attorney' }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('CheckShop')
        this.$EventBus.$emit('checkpath')
        this.$EventBus.$emit('getCountInTable')
        this.$EventBus.$emit('checkJVShop')
        this.$EventBus.$emit('AuthorityUsers')
        if (isAttorney === true) {
          this.$router.push({ path: '/sellerMobile?ShopID=' + item.seller_shop_id + '&ShopName=' + shopname }).catch(() => {})
        } else {
          this.$router.push({ path: '/AttorneyMobile' }).catch(() => {})
        }
      }
      this.modalShopList = false
    },
    CheckPermission () {
      if (this.onedata.user.social_type === 'oneid') {
        if (this.onedata.user.username === null) {
          this.name = this.onedata.user.email
        } else {
          this.name = this.onedata.user.username
        }
      } else if (this.onedata.user.social_type === 'google') {
        if (this.onedata.user.username === null) {
          this.name = this.onedata.user.email
        } else {
          this.name = this.onedata.user.username
        }
      } else if (this.onedata.user.social_type === 'line') {
        if (this.onedata.user.username === null) {
          this.name = this.onedata.user.email
        } else {
          this.name = this.onedata.user.username
        }
      } else if (this.onedata.user.social_type === 'facebook') {
        if (this.onedata.user.username === null) {
          this.name = this.onedata.user.email
        } else {
          this.name = this.onedata.user.username
        }
      }
      // this.name = this.onedata.user.username
      this.role = this.onedata.user.current_role_user
      var roleCheck = JSON.parse(localStorage.getItem('roleUser'))
      // console.log('roleCheck----->', roleCheck)
      var roleAdmin = ''
      if (localStorage.getItem('roleUserAdmin') !== null) {
        roleAdmin = 'admin'
      } else {
        roleAdmin = ''
      }
      if (localStorage.getItem('checkAdminShop') !== null) {
        this.checkAdminShop = 'adminShop'
      }
      if (roleCheck.role === 'ext_buyer' && roleAdmin === '') {
        this.changeRole = 'ext_buyer'
      } else if (roleCheck.role === 'purchaser' && roleAdmin === '') {
        this.changeRole = 'purchaser'
      } else if (roleAdmin !== '') {
        this.changeRole = 'admin'
      } else if (roleCheck.role === 'sale_order' && roleAdmin === '') {
        this.changeRole = 'sale_order'
      } else if (roleCheck.role === 'sale_order_no_JV' && roleAdmin === '') {
        this.changeRole = 'sale_order_no_JV'
      } else {
        this.changeRole = 'approve_manager'
      }
      // console.log('Tong', this.onedata.user)
      // roleCheck.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : roleCheck.role = 'purchaser' ? this.changeRole = 'purchaser' : roleCheck.role = 'sale_oerder' ? this.changeRole = 'sale_oerder'
      // changeRole === 'ext_buyer' ? 'ผู้ซื้อทั่วไป' : changeRole === 'purchaser' ? 'ผู้ซื้อองค์กร' : changeRole === 'admin' ? 'แอดมิน' : changeRole === 'sale_order' ? 'SaleOrder' : 'ผู้อนุมัติ'
    },
    LinkPage (val, componyID) {
      var data
      if (val === 'ext_buyer' && componyID === '') {
        this.selectCompanyName = ''
        this.selectCompanyID = ''
        localStorage.removeItem('SetRowCompany')
        localStorage.removeItem('tax_id')
        localStorage.removeItem('SaleID')
        localStorage.removeItem('partner_id')
        localStorage.removeItem('cusCode')
        localStorage.removeItem('AddressCustomerBussinessSale')
        localStorage.removeItem('AddressCustomerDetail')
        localStorage.removeItem('AddressCustomerSale')
        localStorage.removeItem('sale_order_customer')
        this.$router.push({ path: '/' }).catch(() => {})
      }
      if (val === 'logout') {
        localStorage.removeItem('PageTab')
        this.Logout()
      } else if ((val === 'purchaser' || val === 'ext_buyer') && this.userdetail.approve_manager !== '1') {
        // alert('dd')
        localStorage.removeItem('partner_id')
        localStorage.removeItem('SaleID')
        localStorage.removeItem('cusCode')
        localStorage.removeItem('AddressCustomerBussinessSale')
        localStorage.removeItem('AddressCustomerDetail')
        localStorage.removeItem('AddressCustomerSale')
        localStorage.removeItem('sale_order_customer')
        localStorage.removeItem('PartnerID')
        data = {
          role: val === 'purchaser' ? 'purchaser' : 'ext_buyer'
        }
        // console.log('changeRole', this.changeRole)
        this.checkAdminShop = ''
        val === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.Role = val
        this.$EventBus.$emit('getCartPopOver') //
        this.$EventBus.$emit('KeepCouponPageAll')
        this.$EventBus.$emit('KeepCouponPageHome')
        this.$EventBus.$emit('getCart') // ตะกร้าสินค้า
        this.$EventBus.$emit('getPOBuyer')
        this.$EventBus.$emit('getDetailPOBuyer')
        if (val === 'ext_buyer') {
          this.$EventBus.$emit('getHomepageItems')
        } else {
          this.$EventBus.$emit('getBestSeller')
        }
        this.$EventBus.$emit('getProductDetail')
        this.$EventBus.$emit('getAllNewProduct')
        this.$EventBus.$emit('getAllBestSeller')
        this.$EventBus.$emit('getResultSearch')
        this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('ChackRowUser')
        this.$EventBus.$emit('getSellerShopPage')
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('checkRoleCardUI')
        this.$EventBus.$emit('checkRoleCardRes')
        this.$EventBus.$emit('checkRoleCardIpad')
        this.$EventBus.$emit('checkRole')
        this.$EventBus.$emit('getUserDetail')
        this.$EventBus.$emit('role', this.Role)
        this.$EventBus.$emit('getRole', this.Role)
        this.$EventBus.$emit('GetPopularProduct')
        this.$router.push({ path: '/' }).catch(() => {})
        // this.reloadPage()
      } else if (val === 'admin') {
        this.resetAdminShop()
        if (this.onePosition.admin_platform !== undefined) {
          if (this.onePosition.admin_platform === true || this.onePosition.super_admin_platform === true) {
            this.changeRole = 'admin'
            this.Role = val
            data = {
              role: 'admin'
            }
            localStorage.setItem('roleUserAdmin', JSON.stringify(data))
            if (this.MobileSize) {
              this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
            } else {
              this.$router.push({ name: 'dashboardShopAdmin' }).catch(() => {})
            }
          } else {
            const Toast = this.$swal.mixin({
              toast: true,
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true
            })
            Toast.fire({
              icon: 'warning',
              title: `${this.$t('ModalMenu.notPermission')}`,
              text: `${this.$t('ModalMenu.textContact')}`
            })
          }
        }
        this.checkAdminShop = ''
      } else if (val === 'Seller_admin') {
        this.resetAdminShop()
        this.checkAdminShop = ''
        if (this.userdetail.business_id !== null && this.userdetail.seller_shop_admin !== '0') {
          window.location.assign(`${process.env.VUE_APP_DOMAIN}backend/?business=${this.userdetail.business_id}&token=${this.onedata.user.access_token}&role=seller_admin`)
        } else {
          const Toast = this.$swal.mixin({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
          Toast.fire({
            icon: 'warning',
            title: `${this.$t('ModalMenu.noID')}`,
            text: `${this.$t('ModalMenu.createCompany')}`
          })
        }
      } else if (val === 'approved') {
        this.resetAdminShop()
        this.checkAdminShop = ''
        data = {
          role: 'approver'
        }
        this.changeRole = 'approver'
        localStorage.setItem('roleUserApprove', JSON.stringify(data))
        this.$router.push({ path: '/approved' }).catch(() => {})
      } else if ((val === 'purchaser' || val === 'ext_buyer') && this.userdetail.approve_manager === '1') {
        data = {
          role: val === 'purchaser' ? 'purchaser' : 'ext_buyer'
        }
        val === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.reloadRole = val
        this.Role = val
        this.$EventBus.$emit('getCartPopOver')
        this.$EventBus.$emit('KeepCouponPageAll')
        this.$EventBus.$emit('KeepCouponPageHome')
        this.$EventBus.$emit('getCart')
        this.$EventBus.$emit('getPOBuyer')
        this.$EventBus.$emit('getDetailPOBuyer')
        if (val === 'ext_buyer') {
          this.$EventBus.$emit('getHomepageItems')
        } else {
          this.$EventBus.$emit('getBestSeller')
        }
        this.$EventBus.$emit('getProductDetail')
        this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        // this.$EventBus.$emit('ChackPartnerShop')
        this.$EventBus.$emit('getAllNewProduct')
        this.$EventBus.$emit('getAllBestSeller')
        this.$EventBus.$emit('getResultSearch')
        this.$EventBus.$emit('ListRefundDataTable')
        this.$EventBus.$emit('getSellerShopPage')
        this.$EventBus.$emit('ChackRowUser')
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('checkRoleCardUI')
        this.$EventBus.$emit('checkRoleCardRes')
        this.$EventBus.$emit('checkRoleCardIpad')
        this.$EventBus.$emit('checkRole')
        this.$EventBus.$emit('role', this.Role)
        this.$EventBus.$emit('getRole', this.Role)
        this.$EventBus.$emit('GetPopularProduct')
        this.$router.push({ path: '/' }).catch(() => {})
        // this.reloadPage()
      } else if (val === 'sale_order') {
        // console.log('sale_order', val, componyID)
        this.resetAdminShop()
        this.checkAdminShop = ''
        localStorage.removeItem('SetRowCompany')
        localStorage.removeItem('selectCompanyName')
        localStorage.removeItem('tax_id')
        localStorage.removeItem('SaleID')
        localStorage.removeItem('business_id')
        localStorage.removeItem('partner_id')
        localStorage.removeItem('cusCode')
        localStorage.removeItem('AddressCustomerBussinessSale')
        localStorage.removeItem('AddressCustomerDetail')
        localStorage.removeItem('AddressCustomerSale')
        localStorage.removeItem('sale_order_customer')
        data = {
          role: 'sale_order'
        }
        if (val === 'sale_order') { this.changeRole = 'sale_order' }
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.Role = val
        this.$EventBus.$emit('getCartPopOver') //
        this.$EventBus.$emit('KeepCouponPageAll')
        this.$EventBus.$emit('KeepCouponPageHome')
        this.$EventBus.$emit('getCart') // ตะกร้าสินค้า
        this.$EventBus.$emit('getPOBuyer')
        this.$EventBus.$emit('getDetailPOBuyer')
        // this.$EventBus.$emit('getHomepageItems')
        this.$EventBus.$emit('getProductDetail') //
        // this.$EventBus.$emit('getAllNewProduct') //
        this.$EventBus.$emit('getAllBestSeller') //
        this.$EventBus.$emit('getResultSearch')
        this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('ChackRowUser') //
        this.$EventBus.$emit('getSellerShopPage') //
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('ChackPartnerShop')
        this.$EventBus.$emit('checkRoleCardUI')
        this.$EventBus.$emit('checkRoleCardRes')
        this.$EventBus.$emit('checkRoleCardIpad')
        this.$EventBus.$emit('GetPopularProduct')
        this.$EventBus.$emit('checkRole')
        this.$EventBus.$emit('getUserDetail')
        this.$EventBus.$emit('role', this.Role)
        this.$EventBus.$emit('getRole', this.Role)
        // console.log('มาไหม', this.valueOfShop.seller_shop_id)
        const shopCleaned = encodeURIComponent(this.valueOfShop.shop_name.replace(/\s/g, '-'))
        // console.log('shopCleaned', shopCleaned, this.valueOfShop.seller_shop_id)
        this.$router.push({ path: `/shoppage/${shopCleaned}-${this.valueOfShop.seller_shop_id}` }).catch(() => {})
        if (this.reloadRole === 'sale') {
          window.location.reload()
        }
        // const shopCleaned = val.shop_name.replace(/\s/g, '-')
        // this.$router.push({ path: `/shoppage/${shopCleaned}-${val.shop_id}` }).catch(() => {})
        // this.$router.push({ path: '/' }).catch(() => {})
      } else if (val === 'sale_order_no_JV') {
        this.resetAdminShop()
        this.checkAdminShop = ''
        localStorage.removeItem('SetRowCompany')
        localStorage.removeItem('selectCompanyName')
        localStorage.removeItem('SaleID')
        localStorage.removeItem('tax_id')
        localStorage.removeItem('business_id')
        data = {
          role: 'sale_order_no_JV'
        }
        if (val === 'sale_order_no_JV') { this.changeRole = 'sale_order_no_JV' }
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.Role = val
        this.$EventBus.$emit('getCartPopOver') //
        this.$EventBus.$emit('KeepCouponPageAll')
        this.$EventBus.$emit('KeepCouponPageHome')
        this.$EventBus.$emit('getCart') // ตะกร้าสินค้า
        this.$EventBus.$emit('getPOBuyer')
        // this.$EventBus.$emit('getDetailPOBuyer')
        // this.$EventBus.$emit('getHomepageItems')
        this.$EventBus.$emit('getProductDetail') //
        // this.$EventBus.$emit('getAllNewProduct') //
        this.$EventBus.$emit('getAllBestSeller') //
        this.$EventBus.$emit('getResultSearch')
        // this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('ChackRowUser') //
        this.$EventBus.$emit('getSellerShopPage') //
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('ChackPartnerShop')
        this.$EventBus.$emit('checkRoleCardUI')
        this.$EventBus.$emit('checkRoleCardRes')
        this.$EventBus.$emit('checkRoleCardIpad')
        this.$EventBus.$emit('checkRole')
        this.$EventBus.$emit('getUserDetail')
        this.$EventBus.$emit('role', this.Role)
        this.$EventBus.$emit('getRole', this.Role)
        this.$EventBus.$emit('GetPopularProduct')
        // console.log('มาไหม', this.valueOfShop.seller_shop_id)
        const shopCleaned = encodeURIComponent(this.valueOfShop.shop_name.replace(/\s/g, '-'))
        this.$router.push({ path: `/shoppage/${shopCleaned}-${this.valueOfShop.seller_shop_id}` }).catch(() => {})
        if (this.reloadRole === 'sale') {
          window.location.reload()
        }
        // const shopCleaned = val.shop_name.replace(/\s/g, '-')
        // this.$router.push({ path: `/shoppage/${shopCleaned}-${val.shop_id}` }).catch(() => {})
        // this.$router.push({ path: '/' }).catch(() => {})
      } else if (val === 'sale_order_vendor') {
        this.resetAdminShop()
        this.checkAdminShop = ''
        localStorage.removeItem('SetRowCompany')
        localStorage.removeItem('selectCompanyName')
        localStorage.removeItem('SaleID')
        localStorage.removeItem('tax_id')
        localStorage.removeItem('business_id')
        data = {
          role: 'sale_order_no_JV'
        }
        if (val === 'sale_order_vendor') { this.changeRole = 'sale_order_no_JV' }
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.Role = 'sale_order_no_JV'
        this.$EventBus.$emit('getCartPopOver') //
        this.$EventBus.$emit('KeepCouponPageAll')
        this.$EventBus.$emit('KeepCouponPageHome')
        this.$EventBus.$emit('getCart') // ตะกร้าสินค้า
        this.$EventBus.$emit('getPOBuyer')
        // this.$EventBus.$emit('getDetailPOBuyer')
        // this.$EventBus.$emit('getHomepageItems')
        this.$EventBus.$emit('getProductDetail') //
        // this.$EventBus.$emit('getAllNewProduct') //
        this.$EventBus.$emit('getAllBestSeller') //
        this.$EventBus.$emit('getResultSearch')
        // this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('ChackRowUser') //
        this.$EventBus.$emit('getSellerShopPage') //
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('ChackPartnerShop')
        this.$EventBus.$emit('checkRoleCardUI')
        this.$EventBus.$emit('checkRoleCardRes')
        this.$EventBus.$emit('checkRoleCardIpad')
        this.$EventBus.$emit('checkRole')
        this.$EventBus.$emit('getUserDetail')
        this.$EventBus.$emit('role', this.Role)
        this.$EventBus.$emit('GetPopularProduct')
        // console.log('มาไหม', this.valueOfShop.seller_shop_id)
        const shopCleaned = encodeURIComponent(this.valueOfShop.name_th.replace(/\s/g, '-'))
        this.$router.push({ path: `/shoppage/${shopCleaned}-${this.valueOfShop.id}` }).catch(() => {})
        this.$EventBus.$emit('getRole', this.Role)
        if (this.reloadRole === 'sale') {
          window.location.reload()
        }
        // const shopCleaned = val.shop_name.replace(/\s/g, '-')
        // this.$router.push({ path: `/shoppage/${shopCleaned}-${val.shop_id}` }).catch(() => {})
        // this.$router.push({ path: '/' }).catch(() => {})
      } else if (val === 'Company') {
        this.visible = false
        this.modalCompany = !this.modalCompany
      } else if (val === 'ShopList') {
        this.visible = false
        this.searchShopList = ''
        this.modalShopList = !this.modalShopList
      } else if (val !== this.$router.currentRoute.name) {
        this.resetAdminShop()
        this.$router.push({ path: `/${val}` }).catch(() => {})
        this.visible = false
      }
      if (val === 'purchaser' || val === 'ext_buyer') {
        this.checkAdminShop = ''
        this.resetAdminShop()
        if (this.$router.currentRoute.name !== '') {
          localStorage.removeItem('ShopDetailSale')
          localStorage.removeItem('pathShopSale')
          localStorage.removeItem('partner_id')
          localStorage.removeItem('SaleID')
          localStorage.removeItem('cusCode')
          localStorage.removeItem('AddressCustomerBussinessSale')
          localStorage.removeItem('AddressCustomerDetail')
          localStorage.removeItem('AddressCustomerSale')
          localStorage.removeItem('sale_order_customer')
          this.$router.push({ path: '/' })
          if (this.reloadRole !== 'sale' && val !== this.reloadRole) {
            window.location.reload()
          }
        }
      }
      if (this.Role === 'sale_order') {
        // console.log('sale_order---->', this.Role)
        this.reloadRole = 'sale'
      } else {
        // console.log('5555555')
        this.$EventBus.$emit('getDetailBussiness')
        this.reloadRole = this.Role
      }
    },
    // async LinkPageGraczMenu (val) {
    //   if (val === '' && val !== this.$router.currentRoute.name) {
    //     this.visible = false
    //     this.$router.push({ path: `/${val}` }).catch(() => {})
    //   } else if (val !== this.$router.currentRoute.name) {
    //     if (val === 'Order/SellerShopDetail') {
    //       this.GoToSellerShopGracz(this.graczData.seller_shop_id, this.graczData.shop_name, this.graczData)
    //     } else {
    //       if (val === 'Order/UserProfileDetail' || val === 'Order/UserProfileDetailMobile') {
    //         this.$EventBus.$emit('changeToAdminProfile')
    //         if (this.MobileSize) {
    //           this.$router.push({ path: '/Order/UserProfileDetailMobile' }).catch(() => {})
    //         } else {
    //           this.$router.push({ path: '/Order/UserProfileDetail' }).catch(() => {})
    //         }
    //       } else {
    //         this.$router.push({ path: `/${val}` }).catch(() => {})
    //       }
    //     }
    //     this.visible = false
    //   }
    // },
    // reloadPage () {
    //   window.location.reload()
    // },
    Logout () {
      this.resetAdminShop()
      localStorage.removeItem('partner_id')
      localStorage.removeItem('PartnerID')
      localStorage.removeItem('pathShopSale')
      localStorage.removeItem('AddAddressCustomer')
      localStorage.removeItem('AddressCustomerSale')
      localStorage.removeItem('ShopDetailSale')
      localStorage.removeItem('roleUser')
      localStorage.removeItem('roleUserApprove')
      localStorage.removeItem('roleUserAdmin')
      localStorage.removeItem('oneData')
      localStorage.removeItem('orderNumber')
      localStorage.removeItem('orderNumberSeller')
      localStorage.removeItem('shopSellerID')
      localStorage.removeItem('shopDetail')
      localStorage.removeItem('AllShopSearch')
      localStorage.removeItem('CompanyData')
      sessionStorage.removeItem('pathRedirect')
      // localStorage.removeItem('orderRefundNumber')
      localStorage.removeItem('list_shop_detail')
      localStorage.removeItem('selectCompanyName')
      localStorage.removeItem('SetRowCompany')
      localStorage.removeItem('tax_id')
      localStorage.removeItem('SaleID')
      localStorage.removeItem('AuthorityUser')
      localStorage.removeItem('an_id')
      window.location.assign(`${process.env.VUE_APP_LOGOUT}`)
    },
    async createCompany () {
      await this.$store.dispatch('actionsCreateCompanyByBiz')
      var response = await this.$store.state.ModuleAdminManage.stateCreateCompanyByBiz
      // console.log(response)
      if (response.result === 'SUCCESS') {
        // this.$router.push({ path: '/manageCompany?Status=Create' }).catch(() => {})
        this.$swal.fire({ icon: 'success', title: `${this.$t('ModalMenu.createComSuccess')}`, showConfirmButton: false, timer: 2000 })
        await this.AuthorityUser()
        await this.getCompany()
        await this.getUserDetail()
        await this.$EventBus.$emit('refreshCreateCompany')
        this.modalCompany = !this.modalCompany
      } else if (response.message === 'Not found business data.') {
        this.$swal.fire({ icon: 'warning', title: `${this.$t('ModalMenu.noDataYourCor')}`, showConfirmButton: false, timer: 2000 })
      } else if (response.message === 'Business is pending.') {
        this.$swal.fire({ icon: 'warning', title: 'นิติบุคคลของคุณกำลังรอการอนุมัติ', showConfirmButton: false, timer: 2000 })
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    closeModalSuccess () {
      this.visible = false
    },
    openModalSuccess () {
      this.visible = true
    },
    async refreshToken () {
      const dataUserID = {
        user_id: this.onedata.user.user_id
      }
      await this.$store.dispatch('actionsRefreshToken', dataUserID)
      const responseRefresh = await this.$store.state.ModuleUser.stateRefreshToken
      if (responseRefresh.message === 'กรุณา Login ใหม่อีกครั้ง') {
        this.Logout()
        if (this.MobileSize) {
          this.$router.push({ path: '/LoginMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Login' }).catch(() => {})
        }
      } else if (responseRefresh.message === 'Get Token Success') {
        this.onedata.user.access_token = responseRefresh.data.access_token
        localStorage.setItem('oneData', Encode.encode(this.onedata))
        window.location.reload()
      } else if (responseRefresh.message === 'Refresh Token Success') {
        this.onedata.user.access_token = responseRefresh.data.access_token
        localStorage.setItem('oneData', Encode.encode(this.onedata))
        window.location.reload()
      }
    },
    async getUserDetail () {
      this.detail = []
      this.companyID = this.onedata.user.company_id
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionsUserDetailPage', data)
      var response = await this.$store.state.ModuleUser.stateUserDetailPage
      localStorage.setItem('UserDetail', Encode.encode(response))
      // console.log('user Detail====>', response.data)
      if (response.code === 401) {
        // this.Logout()
        await this.refreshToken()
        // const dataUserID = {
        //   user_id: this.onedata.user.user_id
        // }
        // await this.$store.dispatch('actionsRefreshToken', dataUserID)
        // const responseRefresh = await this.$store.state.ModuleUser.stateRefreshToken
        // if (responseRefresh.message === 'กรุณา Login ใหม่อีกครั้ง') {
        //   this.Logout()
        //   if (this.MobileSize) {
        //     this.$router.push({ path: '/LoginMobile' }).catch(() => {})
        //   } else {
        //     this.$router.push({ path: '/Login' }).catch(() => {})
        //   }
        // } else if (responseRefresh.message === 'Get Token Success') {
        //   this.onedata.user.access_token = responseRefresh.data.access_token
        //   localStorage.setItem('oneData', Encode.encode(this.onedata))
        //   window.location.reload()
        // } else if (responseRefresh.message === 'Refresh Token Success') {
        //   this.onedata.user.access_token = responseRefresh.data.access_token
        //   localStorage.setItem('oneData', Encode.encode(this.onedata))
        //   window.location.reload()
        // }
      }
      // console.log('user Detail====>', response.data[0])
      if (response.data[0].first_name_th !== null && response.data[0].last_name_th) {
        this.name = response.data[0].first_name_th + ' ' + response.data[0].last_name_th
      } else if (this.onedata.user.username !== '' && this.onedata.user.username !== null) {
        this.name = this.onedata.user.username
      } else {
        this.name = this.onedata.user.username_oneid
      }
      this.detail = response.data[0]
      // console.log(this.detail)
      this.imagePath = response.data[0].img_path
      this.userdetail = response.data[0].permissions
      this.haveBusinessID = this.userdetail.business_id
      this.haveCompany = this.userdetail.have_company
      // console.log(this.userdetail, this.haveBusinessID)
    },
    setCompanyFromSpecialPrice () {
      this.purchaser = true
      if (localStorage.getItem('selectCompanyName') !== undefined) {
        if (localStorage.getItem('selectCompanyName') !== null) {
          this.selectCompanyName = localStorage.getItem('selectCompanyName')
        } else {
          this.selectCompanyName = ''
        }
      } else {
        this.selectCompanyName = ''
      }
      var data = { role: 'purchaser' }
      localStorage.setItem('roleUser', JSON.stringify(data))
      var roleCheck = JSON.parse(localStorage.getItem('roleUser'))
      this.changeRole = roleCheck.role
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return (`${this.$t('ModalMenu.plsLogin')}`, 'warning')
      } else if (msg === 'Channel not found. Please check and try again.') {
        return [`${this.$t('ModalMenu.channelNot')}`, 'warning']
      } else if (msg === 'Shop not found. Please check and try again.') {
        return [`${this.$t('ModalMenu.shopNot')}`, 'warning']
      } else if (msg === 'Employee not found. Please check and try again.') {
        return [`${this.$t('ModalMenu.employNot')}`, 'warning']
      } else if (msg === 'Seller shop not found.' || msg === 'Not Found Seller Shop') {
        return [`${this.$t('ModalMenu.shopNo')}`, 'warning']
      } else if (msg === 'Not access this function.') {
        return [`${this.$t('ModalMenu.noAccess')}`, 'warning']
      } else {
        return [msg, 'error']
      }
    },
    async GoToSellerShopGracz (id, name, shop) {
      const customerDetail = {
        cus_name: '',
        cus_id: '',
        cus_code: '',
        channel_name: '',
        channel_id: '',
        cart_data: '',
        cus_address: '',
        onlinePurchaseStatus: true
      }
      await localStorage.setItem('OnlinePurchase', Encode.encode(customerDetail))
      await localStorage.setItem('GraczShopData', Encode.encode(shop))
      this.$EventBus.$emit('OnlinePurchase')
      this.$EventBus.$emit('changeAppBarGracz')
      this.$EventBus.$emit('changeToAdminShop')
      if (this.MobileSize) {
        const routeData = this.$router.resolve({ name: 'GraczSellerShopMobile' })
        window.location.assign(routeData.href, '_blank')
        // this.$router.push('/Order/SellerShopMobile').catch(() => {})
      } else {
        this.$router.push(`/Order/SellerShopDetail/${name}-${id}`).catch(() => {})
      }
    },
    // checkPermission () {
    //   if (this.onedata.user.current_role_user !== undefined) {
    //     var role = this.onedata.user.current_role_user
    //     if (role.seller_admin) {
    //       this.ListNavbar = ListNavBar.ListNavbar_3
    //     } else if (role.purchaser) {
    //       this.ListNavbar = ListNavBar.ListNavbar_2
    //     } else if (role.ext_buyer) {
    //       this.ListNavbar = ListNavBar.ListNavbar_1
    //     }
    //   }
    // }
    async openChooseTaxId () {
      // this.getListTaxID()
      // await this.confirmChooseTaxId()
      this.modalBusiness = true
      this.visible = false
    },
    async confirmChooseTaxId (taxId) {
      if (this.MobileSize === true) {
        if (this.list_business.length === 0 && this.listBusiness.length === 0) {
          this.$swal.fire({
            icon: 'warning',
            title: `${this.$t('ModalMenu.noBus')}`,
            showConfirmButton: true
          }).then(async (result) => {
            if (result.isConfirmed) {
              await this.LinkPage('createbusinesssidMobile')
            }
          })
          // window.location.reload()
        } else {
          this.getBussineData()
          await this.LinkPage('detailbusinesssidMobileMenu')
          await this.updateOwnerPosition(taxId)
          // window.location.reload()
        }
      } else {
        if (this.list_business.length === 0 && this.listBusiness.length === 0) {
          this.$swal.fire({
            icon: 'warning',
            title: `${this.$t('ModalMenu.noRegis')}`,
            showConfirmButton: true,
            confirmButtonText: `${this.$t('ModalMenu.noBus')}`,
            showCancelButton: true,
            cancelButtonText: `${this.$t('DashboardAffiliate.btnCancel')}`
          }).then(async (result) => {
            if (result.isConfirmed) {
              await this.LinkPage('createbusinesssid')
              this.modalBusiness = false
            }
          })
          // window.location.reload()
        } else {
          this.getBussineData()
          await this.$EventBus.$emit('getDetailBusinessData')
          this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
          await this.updateOwnerPosition(taxId)
          await this.CheckUser(taxId)
          // console.log(taxId, 'login')
          // await this.LinkPage('detailbusinesssid')
          // this.$EventBus.$emit('getDetailBussiness')
          // console.log('เข้าอันนี้')
          // await window.location.reload()
        }
      }
    },
    async CheckUser (val) {
      var data = {
        tax_id: val
      }
      await this.$store.dispatch('actionsCheckUser', data)
    },
    async chooseCorporlation (val) {
      // console.log(val, 'val')
      // await this.confirmChooseTaxId()
      localStorage.removeItem('business_id')
      localStorage.setItem('business_id', val.business_id)
      await this.confirmChooseTaxId(val.tax_id)
      this.$EventBus.$emit('CheckPartnerCode', val, 'newTaxId')
      this.modalBusiness = false
    },
    async gotoCreateBussiness () {
      this.modalBusiness = false
      if (this.MobileSize === false) {
        await this.LinkPage('createbusinesssid')
      } else {
        await this.LinkPage('createbusinesssidMobile')
      }
    },
    async updateOwnerPosition (taxId) {
      // console.log(taxId, 'taxId')
      var data = {
        tax_id: taxId
      }
      await this.$store.dispatch('actionsUpdateOwnerPosition', data)
      // var response = await this.$store.state.ModuleBusiness.stateUpdateOwnerPosition
      // console.log(response)
    },
    async checkBusinessEKYC () {
      this.CheckBusiness()
      if (this.haveBusiness === false && this.haveCitizen === false && this.listBusiness.length === 0) {
        this.LinkPage('createbusinesssid')
      } else if (this.haveBusiness === true || this.haveCitizen === true || this.listBusiness.length !== 0) {
        this.openChooseTaxId()
      }
    }
  }
}
</script>

<style>
.ant-popover-inner-content{
padding: 0;
}
.ant-menu-item:hover, .ant-menu-item-active, .ant-menu:not(.ant-menu-inline), .ant-menu-submenu-open, .ant-menu-submenu-active, .ant-menu-submenu-title:hover {
  color: #27AB9C;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
  font-weight: 700 !important;
}
.cardChooseType{
  text-align: center;
  border-radius: 10px;
}
.cardChooseType:hover {
  transform: scale(1.05);
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
  border-bottom: 1px solid #27AB9C !important;
}
</style>

<style lang="scss">
.ant-menu-submenu-title:hover {
  .ant-menu-submenu-arrow::before, .ant-menu-submenu-arrow::after {
    background: #27AB9C!important;
  }
}
</style>
<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
