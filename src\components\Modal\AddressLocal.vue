<template>
  <div class="text-center">
    <v-dialog v-model="dialog" width="500" persistent>
      <v-card>
      <v-container grid-list-xs>
        <v-card-title class="headline">เพิ่มที่อยู่ใหม่</v-card-title>
        <v-card-text>
          <v-form ref="FormAddress" :lazy-validation="lazy">
          <a-row type="flex">
            <a-col :span='24'>
              <v-text-field placeholder="ชื่อ-นามสกุล" v-model="name" :rules="Rules.name" outlined dense class="input_text"></v-text-field>
            </a-col>
            <a-col :span='24'>
              <v-text-field placeholder="หมายเลขโทรศัพท์" v-model="phone" :rules="Rules.tel" outlined dense class="input_text"></v-text-field>
            </a-col>
            <a-col :span='24'>
              <v-text-field placeholder="อาคาร, ถนน, เเละอื่นๆ" v-model="detail" outlined dense class="input_text"></v-text-field>
            </a-col>
            <a-col :span='24'>
              <addressinput-subdistrict label=""  v-model="subdistrict" placeholder="เเขวง / ตำบล"/>
            </a-col>
            <a-col :span='24' class="mt-5">
              <addressinput-district label="" v-model="district"  placeholder="เขต / อำเภอ" />
            </a-col>
            <a-col :span='24' class="mt-5">
              <addressinput-province label="" v-model="province" placeholder="จังหวัด" />
            </a-col>
            <a-col :span='24' class="mt-5">
              <addressinput-zipcode label="" v-model="zipcode" placeholder="รหัสไปรษณีย์" />
            </a-col>
          </a-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <a-button class="mr-3" @click="cancel">ยกเลิก</a-button>
          <a-button class="border_confirm mr-2" @click="CreateAddress">ยืนยัน</a-button>
        </v-card-actions>
      </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import { Button, Col, Row } from 'ant-design-vue'
Vue.use(VueThailandAddress)
export default {
  props: ['checkAddressLocal'],
  components: {
    'a-row': Row,
    'a-col': Col,
    'a-button': Button
  },
  data () {
    return {
      lazy: false,
      name: '',
      phone: '',
      detail: '',
      dialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        name: [
          v => !!v || 'กรุณากรอกชื่อผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ]
      }
    }
  },
  watch: {
    checkAddressLocal (val) {
      this.dialog = val
    }
  },
  created () {
    this.dialog = this.checkAddressLocal
  },
  methods: {
    cancel () {
      this.$router.push({ path: '/shoppingcart' })
    },
    async CreateAddress () {
      if (this.$refs.FormAddress.validate(true)) {
        // console.log('Open')
        var data = {
          first_name: this.name,
          last_name: '',
          house_no: '',
          address_detail: this.detail,
          sub_district: this.subdistrict,
          district: this.district,
          province: this.province,
          phone: this.phone,
          zipcode: this.zipcode
        }
        this.$EventBus.$emit('SentGetAddressLocal', data)
        this.dialog = false
      }
    }
  }
}
</script>

<style>
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
/* .ant-input {
  opacity: 1;
  font-size: 14px;
  padding-left: 10px;
} */
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
</style>
