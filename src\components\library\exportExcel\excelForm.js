import FileSaver from 'file-saver'
import XLSX from 'xlsx-js-style'
import { defaultCellStyle, headerCellStyle2, evenRowStyle2 } from './options'

export const exportExcelForm = (obj, refs) => {
  const data = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['การเข้าใช้งานระบบของผู้ใช้งาน', null, null, null, null, null, null, null, null],
    [null],
    [
      null,
      'ระหว่างวันที่:',
      null,
      refs.startDate + ' - ' + refs.endDate
    ],
    [null, 'จำนวนการชำระเงินที่สำเร็จ:', null, obj.transition],
    [null],
    [
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ]
  ]
  const sheet = sheetFromArrayOfArrays2(data)
  // Header merge: r: row row; c: column column
  const mergeTitle = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } },
    { s: { r: 3, c: 1 }, e: { r: 3, c: 2 } },
    { s: { r: 3, c: 3 }, e: { r: 3, c: 6 } },
    { s: { r: 4, c: 1 }, e: { r: 4, c: 2 } },
    { s: { r: 4, c: 3 }, e: { r: 4, c: 6 } },
    { s: { r: 5, c: 1 }, e: { r: 5, c: 2 } },
    { s: { r: 5, c: 3 }, e: { r: 5, c: 6 } },
    { s: { r: 6, c: 1 }, e: { r: 6, c: 2 } },
    { s: { r: 6, c: 3 }, e: { r: 6, c: 6 } },
    { s: { r: 7, c: 1 }, e: { r: 7, c: 2 } },
    { s: { r: 7, c: 3 }, e: { r: 7, c: 6 } },
    { s: { r: 8, c: 1 }, e: { r: 8, c: 2 } },
    { s: { r: 8, c: 3 }, e: { r: 8, c: 6 } },
    { s: { r: 10, c: 0 }, e: { r: 10, c: 0 } },
    { s: { r: 11, c: 1 }, e: { r: 11, c: 1 } },
    { s: { r: 12, c: 1 }, e: { r: 12, c: 1 } },
    { s: { r: 13, c: 2 }, e: { r: 13, c: 2 } },
    { s: { r: 14, c: 2 }, e: { r: 14, c: 2 } },
    { s: { r: 15, c: 2 }, e: { r: 15, c: 2 } },
    { s: { r: 16, c: 2 }, e: { r: 16, c: 2 } },
    { s: { r: 17, c: 2 }, e: { r: 17, c: 2 } },
    { s: { r: 18, c: 2 }, e: { r: 18, c: 2 } },
    { s: { r: 19, c: 2 }, e: { r: 19, c: 2 } },
    { s: { r: 20, c: 2 }, e: { r: 20, c: 2 } },
    { s: { r: 21, c: 2 }, e: { r: 21, c: 2 } },
    { s: { r: 22, c: 2 }, e: { r: 22, c: 2 } },
    { s: { r: 23, c: 2 }, e: { r: 23, c: 2 } },
    { s: { r: 24, c: 2 }, e: { r: 24, c: 2 } },
    { s: { r: 25, c: 2 }, e: { r: 25, c: 2 } },
    { s: { r: 26, c: 2 }, e: { r: 26, c: 2 } },
    { s: { r: 27, c: 2 }, e: { r: 27, c: 2 } },
    { s: { r: 28, c: 2 }, e: { r: 28, c: 2 } },
    { s: { r: 29, c: 2 }, e: { r: 29, c: 2 } },
    { s: { r: 30, c: 2 }, e: { r: 30, c: 2 } },
    { s: { r: 31, c: 2 }, e: { r: 31, c: 2 } },
    { s: { r: 32, c: 2 }, e: { r: 32, c: 2 } },
    { s: { r: 33, c: 2 }, e: { r: 33, c: 2 } },
    { s: { r: 34, c: 2 }, e: { r: 34, c: 2 } },
    { s: { r: 35, c: 2 }, e: { r: 35, c: 2 } },
    { s: { r: 36, c: 2 }, e: { r: 36, c: 2 } },
    { s: { r: 37, c: 2 }, e: { r: 37, c: 2 } },
    { s: { r: 38, c: 2 }, e: { r: 38, c: 2 } },
    { s: { r: 39, c: 2 }, e: { r: 39, c: 2 } },
    { s: { r: 40, c: 2 }, e: { r: 40, c: 2 } },
    { s: { r: 41, c: 2 }, e: { r: 41, c: 2 } },
    { s: { r: 42, c: 1 }, e: { r: 42, c: 2 } },
    { s: { r: 42, c: 3 }, e: { r: 42, c: 4 } },
    { s: { r: 43, c: 1 }, e: { r: 43, c: 2 } },
    { s: { r: 43, c: 3 }, e: { r: 43, c: 4 } },
    { s: { r: 44, c: 1 }, e: { r: 44, c: 2 } },
    { s: { r: 44, c: 3 }, e: { r: 44, c: 4 } },
    { s: { r: 45, c: 1 }, e: { r: 45, c: 2 } },
    { s: { r: 45, c: 3 }, e: { r: 45, c: 4 } },
    { s: { r: 46, c: 1 }, e: { r: 46, c: 2 } },
    { s: { r: 46, c: 3 }, e: { r: 46, c: 4 } }
  ]
  sheet['!merges'] = mergeTitle
  sheet['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  const sheetCols = [
    { wch: 15 },
    { wch: 50 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 }
  ]
  sheet['!cols'] = sheetCols
  addRangeBorder(mergeTitle, sheet)
  // ----------------------sheets 2*************************
  const data3 = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['การเข้าใช้งานระบบของผู้ใช้งาน', null, null, null, null, null, null, null, null],
    [null],
    [null, 'ประเภท', null, ' จำนวน(คน)'],
    [null, 'ผู้ใช้ทั้งหมด:', null, obj.sellerAndUser.user_total],
    [null, 'ผู้ใช้ที่มีการใช้งาน:', null, obj.sellerAndUser.user_active],
    [null, 'ผู้ใช้ใหม่:', null, obj.sellerAndUser.user_new],
    [null],
    [
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ]
  ]
  const sheet2 = sheetFromArrayOfArrays2(data3)
  const mergeTitle2 = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } },
    { s: { r: 3, c: 1 }, e: { r: 3, c: 2 } },
    { s: { r: 3, c: 3 }, e: { r: 3, c: 6 } },
    { s: { r: 4, c: 1 }, e: { r: 4, c: 2 } },
    { s: { r: 4, c: 3 }, e: { r: 4, c: 6 } },
    { s: { r: 5, c: 1 }, e: { r: 5, c: 2 } },
    { s: { r: 5, c: 3 }, e: { r: 5, c: 6 } },
    { s: { r: 6, c: 1 }, e: { r: 6, c: 2 } },
    { s: { r: 6, c: 3 }, e: { r: 6, c: 6 } },
    { s: { r: 7, c: 1 }, e: { r: 7, c: 2 } },
    { s: { r: 7, c: 3 }, e: { r: 7, c: 6 } },
    { s: { r: 8, c: 1 }, e: { r: 8, c: 2 } },
    { s: { r: 8, c: 3 }, e: { r: 8, c: 6 } },
    { s: { r: 10, c: 3 }, e: { r: 10, c: 7 } },
    { s: { r: 11, c: 1 }, e: { r: 11, c: 1 } },
    { s: { r: 12, c: 1 }, e: { r: 12, c: 1 } },
    { s: { r: 13, c: 2 }, e: { r: 13, c: 2 } },
    { s: { r: 14, c: 2 }, e: { r: 14, c: 2 } },
    { s: { r: 15, c: 2 }, e: { r: 15, c: 2 } },
    { s: { r: 16, c: 2 }, e: { r: 16, c: 2 } },
    { s: { r: 17, c: 2 }, e: { r: 17, c: 2 } },
    { s: { r: 18, c: 2 }, e: { r: 18, c: 2 } },
    { s: { r: 19, c: 2 }, e: { r: 19, c: 2 } },
    { s: { r: 20, c: 2 }, e: { r: 20, c: 2 } },
    { s: { r: 21, c: 2 }, e: { r: 21, c: 2 } },
    { s: { r: 22, c: 2 }, e: { r: 22, c: 2 } },
    { s: { r: 23, c: 2 }, e: { r: 23, c: 2 } },
    { s: { r: 24, c: 2 }, e: { r: 24, c: 2 } },
    { s: { r: 25, c: 2 }, e: { r: 25, c: 2 } },
    { s: { r: 26, c: 2 }, e: { r: 26, c: 2 } },
    { s: { r: 27, c: 2 }, e: { r: 27, c: 2 } },
    { s: { r: 28, c: 2 }, e: { r: 28, c: 2 } },
    { s: { r: 29, c: 2 }, e: { r: 29, c: 2 } },
    { s: { r: 30, c: 2 }, e: { r: 30, c: 2 } },
    { s: { r: 31, c: 2 }, e: { r: 31, c: 2 } },
    { s: { r: 32, c: 2 }, e: { r: 32, c: 2 } },
    { s: { r: 33, c: 2 }, e: { r: 33, c: 2 } },
    { s: { r: 34, c: 2 }, e: { r: 34, c: 2 } },
    { s: { r: 35, c: 2 }, e: { r: 35, c: 2 } },
    { s: { r: 36, c: 2 }, e: { r: 36, c: 2 } },
    { s: { r: 37, c: 2 }, e: { r: 37, c: 2 } },
    { s: { r: 38, c: 2 }, e: { r: 38, c: 2 } },
    { s: { r: 39, c: 2 }, e: { r: 39, c: 2 } },
    { s: { r: 40, c: 2 }, e: { r: 40, c: 2 } },
    { s: { r: 41, c: 2 }, e: { r: 41, c: 2 } },
    { s: { r: 42, c: 1 }, e: { r: 42, c: 2 } },
    { s: { r: 42, c: 3 }, e: { r: 42, c: 4 } },
    { s: { r: 43, c: 1 }, e: { r: 43, c: 2 } },
    { s: { r: 43, c: 3 }, e: { r: 43, c: 4 } },
    { s: { r: 44, c: 1 }, e: { r: 44, c: 2 } },
    { s: { r: 44, c: 3 }, e: { r: 44, c: 4 } },
    { s: { r: 45, c: 1 }, e: { r: 45, c: 2 } },
    { s: { r: 45, c: 3 }, e: { r: 45, c: 4 } },
    { s: { r: 46, c: 1 }, e: { r: 46, c: 2 } },
    { s: { r: 46, c: 3 }, e: { r: 46, c: 4 } }
  ]
  sheet2['!merges'] = mergeTitle
  sheet2['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet2['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  sheet2['!cols'] = sheetCols
  // -------------------------------------------sheet3-------------------------
  const data4 = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['การเข้าใช้งานระบบของร้านค้า', null, null, null, null, null, null, null, null],
    [null],
    [null, 'ประเภท', null, ' จำนวน(คน)'],
    [null, 'ผู้ใช้ทั้งหมด:', null, obj.sellerAndUser.seller_total],
    [null, 'ผู้ใช้ที่มีการใช้งาน:', null, obj.sellerAndUser.seller_active],
    [null, 'ผู้ใช้ใหม่:', null, obj.sellerAndUser.seller_new],
    [null],
    [
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ]
  ]
  const sheet4 = sheetFromArrayOfArrays2(data4)
  const mergeTitle4 = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } },
    { s: { r: 3, c: 1 }, e: { r: 3, c: 2 } },
    { s: { r: 3, c: 3 }, e: { r: 3, c: 6 } },
    { s: { r: 4, c: 1 }, e: { r: 4, c: 2 } },
    { s: { r: 4, c: 3 }, e: { r: 4, c: 6 } },
    { s: { r: 5, c: 1 }, e: { r: 5, c: 2 } },
    { s: { r: 5, c: 3 }, e: { r: 5, c: 6 } },
    { s: { r: 6, c: 1 }, e: { r: 6, c: 2 } },
    { s: { r: 6, c: 3 }, e: { r: 6, c: 6 } },
    { s: { r: 7, c: 1 }, e: { r: 7, c: 2 } },
    { s: { r: 7, c: 3 }, e: { r: 7, c: 6 } },
    { s: { r: 8, c: 1 }, e: { r: 8, c: 2 } },
    { s: { r: 8, c: 3 }, e: { r: 8, c: 6 } },
    { s: { r: 10, c: 3 }, e: { r: 10, c: 7 } },
    { s: { r: 11, c: 1 }, e: { r: 11, c: 1 } },
    { s: { r: 12, c: 1 }, e: { r: 12, c: 1 } },
    { s: { r: 13, c: 2 }, e: { r: 13, c: 2 } },
    { s: { r: 14, c: 2 }, e: { r: 14, c: 2 } },
    { s: { r: 15, c: 2 }, e: { r: 15, c: 2 } },
    { s: { r: 16, c: 2 }, e: { r: 16, c: 2 } },
    { s: { r: 17, c: 2 }, e: { r: 17, c: 2 } },
    { s: { r: 18, c: 2 }, e: { r: 18, c: 2 } },
    { s: { r: 19, c: 2 }, e: { r: 19, c: 2 } },
    { s: { r: 20, c: 2 }, e: { r: 20, c: 2 } },
    { s: { r: 21, c: 2 }, e: { r: 21, c: 2 } },
    { s: { r: 22, c: 2 }, e: { r: 22, c: 2 } },
    { s: { r: 23, c: 2 }, e: { r: 23, c: 2 } },
    { s: { r: 24, c: 2 }, e: { r: 24, c: 2 } },
    { s: { r: 25, c: 2 }, e: { r: 25, c: 2 } },
    { s: { r: 26, c: 2 }, e: { r: 26, c: 2 } },
    { s: { r: 27, c: 2 }, e: { r: 27, c: 2 } },
    { s: { r: 28, c: 2 }, e: { r: 28, c: 2 } },
    { s: { r: 29, c: 2 }, e: { r: 29, c: 2 } },
    { s: { r: 30, c: 2 }, e: { r: 30, c: 2 } },
    { s: { r: 31, c: 2 }, e: { r: 31, c: 2 } },
    { s: { r: 32, c: 2 }, e: { r: 32, c: 2 } },
    { s: { r: 33, c: 2 }, e: { r: 33, c: 2 } },
    { s: { r: 34, c: 2 }, e: { r: 34, c: 2 } },
    { s: { r: 35, c: 2 }, e: { r: 35, c: 2 } },
    { s: { r: 36, c: 2 }, e: { r: 36, c: 2 } },
    { s: { r: 37, c: 2 }, e: { r: 37, c: 2 } },
    { s: { r: 38, c: 2 }, e: { r: 38, c: 2 } },
    { s: { r: 39, c: 2 }, e: { r: 39, c: 2 } },
    { s: { r: 40, c: 2 }, e: { r: 40, c: 2 } },
    { s: { r: 41, c: 2 }, e: { r: 41, c: 2 } },
    { s: { r: 42, c: 1 }, e: { r: 42, c: 2 } },
    { s: { r: 42, c: 3 }, e: { r: 42, c: 4 } },
    { s: { r: 43, c: 1 }, e: { r: 43, c: 2 } },
    { s: { r: 43, c: 3 }, e: { r: 43, c: 4 } },
    { s: { r: 44, c: 1 }, e: { r: 44, c: 2 } },
    { s: { r: 44, c: 3 }, e: { r: 44, c: 4 } },
    { s: { r: 45, c: 1 }, e: { r: 45, c: 2 } },
    { s: { r: 45, c: 3 }, e: { r: 45, c: 4 } },
    { s: { r: 46, c: 1 }, e: { r: 46, c: 2 } },
    { s: { r: 46, c: 3 }, e: { r: 46, c: 4 } }
  ]
  sheet4['!merges'] = mergeTitle
  sheet4['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet4['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  sheet4['!cols'] = sheetCols
  // -------------------------------------------sheet4-------------------------
  const data5 = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['10 อันดับร้านค้าจำนวนรายการสั่งซื้อมากที่สุด', null, null, null, null, null, null, null, null],
    [null],
    [
      'ที่',
      'ชื่อร้าน',
      'จำนวนรายการสั่งซื้อ',
      'เปอร์เซ็น (%)'
    ]
  ]
  for (var i = 0; i < obj.topOrder.length; i++) {
    // var number = 0
    data5.push([
      obj.topOrder[i].number || '',
      obj.topOrder[i].seller_name || '',
      obj.topOrder[i].total_order || '',
      obj.topOrder[i].percent + '%' || ''
    ])
  }
  data5.push([])
  const sheet5 = sheetFromArrayOfArrays(data5)
  const mergeTitle5 = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } }
  ]
  sheet5['!merges'] = mergeTitle5
  sheet5['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet5['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  sheet5['!cols'] = sheetCols
  // -------------------------------------------sheet5-------------------------
  const data6 = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['10 อันดับร้านค้ามูลค่ารายการสั่งซื้อมากที่สุด', null, null, null, null, null, null, null, null],
    [null],
    [
      'ที่',
      'ชื่อร้าน',
      'มูลค่า (บาท)',
      'เปอร์เซ็น (%)',
      null,
      null,
      null,
      null
    ]
  ]
  for (var x = 0; x < obj.topValues.length; x++) {
    data6.push([
      obj.topValues[x].number || '',
      obj.topValues[x].seller_name || '',
      obj.topValues[x].total_price || '',
      obj.topValues[x].percent + '%' || ''
    ])
  }
  const sheet6 = sheetFromArrayOfArrays(data6)
  const mergeTitle6 = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } }
  ]
  sheet6['!merges'] = mergeTitle6
  sheet6['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet6['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  sheet6['!cols'] = sheetCols
  // -------------------------------------------sheet6-------------------------
  const data7 = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['เปรียบเทียบจำนวนผู้ใช้งานแบบรายเดือน', null, null, null, null, null, null, null, null],
    [null],
    [
      obj.headerUser[0],
      obj.headerUser[1],
      obj.headerUser[2],
      obj.headerUser[3],
      obj.headerUser[4],
      obj.headerUser[5],
      null,
      null
    ]
  ]
  for (var y = 0; y < 1; y++) {
    // var number = 0
    for (var array3 in obj.comparedUser) {
      data7.push([
        obj.comparedUser[array3].type || '',
        obj.comparedUser[array3].month1 || '',
        obj.comparedUser[array3].month2 || '',
        obj.comparedUser[array3].month3 || '',
        obj.comparedUser[array3].month4 || '',
        obj.comparedUser[array3].month5 || ''
      ])
    }
  }
  const sheet7 = sheetFromArrayOfArrays(data7)
  const mergeTitle7 = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } }
  ]
  sheet7['!merges'] = mergeTitle7
  sheet7['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet7['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  sheet7['!cols'] = sheetCols
  // -------------------------------------------sheet7-------------------------
  const data8 = [
    [
      'Panit | สินค้า e-commerce หลากหลาย',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null
    ],
    ['เปรียบเทียบจำนวนร้านค้าแบบรายเดือน', null, null, null, null, null, null, null, null],
    [null],
    [
      obj.headerSeller[0],
      obj.headerSeller[1],
      obj.headerSeller[2],
      obj.headerSeller[3],
      obj.headerSeller[4],
      obj.headerSeller[5],
      null,
      null
    ]
  ]
  for (var c = 0; c < 1; c++) {
    // var number = 0
    for (var array4 in obj.comparedSeller) {
      data8.push([
        obj.comparedSeller[array4].type || '',
        obj.comparedSeller[array4].month1 || '',
        obj.comparedSeller[array4].month2 || '',
        obj.comparedSeller[array4].month3 || '',
        obj.comparedSeller[array4].month4 || '',
        obj.comparedSeller[array4].month5 || ''
      ])
    }
  }
  const sheet8 = sheetFromArrayOfArrays(data8)
  const mergeTitle8 = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 7 } }
  ]
  sheet8['!merges'] = mergeTitle8
  sheet8['!freeze'] = {
    xSplit: '1',
    ySplit: '6',
    topLeftCell: 'B7',
    activePane: 'bottomRight',
    state: 'frozen'
  }
  sheet8['!margins'] = {
    left: 1.0,
    right: 1.0,
    top: 1.0,
    bottom: 1.0,
    header: 0.5,
    footer: 0.5
  }
  sheet8['!cols'] = sheetCols
  addRangeBorder(mergeTitle, sheet)
  addRangeBorder(mergeTitle2, sheet2)
  addRangeBorder(mergeTitle4, sheet4)
  addRangeBorder(mergeTitle5, sheet5)
  addRangeBorder(mergeTitle6, sheet6)
  addRangeBorder(mergeTitle7, sheet7)
  addRangeBorder(mergeTitle8, sheet8)
  const arrSheet = [
    { sheet: sheet, sheetName: 'ภาพรวมระบบ PANIT' },
    { sheet: sheet2, sheetName: 'การเข้าใช้งานระบบของผู้ใช้งาน' },
    { sheet: sheet4, sheetName: 'การเข้าใช้งานระบบของร้านค้า' },
    { sheet: sheet5, sheetName: 'จำนวนรายการสั่งซื้อ' },
    { sheet: sheet6, sheetName: 'มูลค่ารายการสั่งซื้อ' },
    { sheet: sheet7, sheetName: 'ผู้ใช้งานแบบรายเดือน' },
    { sheet: sheet8, sheetName: 'ร้านค้าแบบรายเดือน' }
  ]
  const wbBlob = sheet2blob(arrSheet)
  // save download
  FileSaver.saveAs(
    wbBlob,
    'panit_summary_dashboard.xlsx'
  )
}
const sheetFromArrayOfArrays = data => {
  const headerSub = [
    'ประเภท',
    'ที่',
    'ชื่อร้าน',
    'จำนวนรายการสั่งซื้อ',
    'เปอร์เซ็น (%)',
    'มูลค่า (บาท)',
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม'
  ]
  const ws = {}
  const range = { s: { c: 10000000, r: 10000000 }, e: { c: 0, r: 0 } }
  for (let R = 0; R !== data.length; ++R) {
    for (let C = 0; C !== data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R
      if (range.s.c > C) range.s.c = C
      if (range.e.r < R) range.e.r = R
      if (range.e.c < C) range.e.c = C
      const cell = { v: data[R][C], s: defaultCellStyle }
      if (cell.v == null) continue
      if (headerSub.includes(cell.v)) {
        cell.s = evenRowStyle2
      }
      const cellref = XLSX.utils.encode_cell({ c: C, r: R })
      if (typeof cell.v === 'number') cell.t = 'n'
      else if (typeof cell.v === 'boolean') cell.t = 'b'
      else if (cell.v instanceof Date) {
        cell.t = 'n'
        cell.z = XLSX.SSF._table[14]
        cell.v = this.dateNum(cell.v)
      } else cell.t = 's'
      ws[cellref] = cell
    }
  }
  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range)
  return ws
}
const sheetFromArrayOfArrays2 = (data) => {
  const headerTxt = [
    ' Panit | สินค้า e-commerce หลากหลาย'
  ]
  const headerSub = [
    ' จำนวน(คน)',
    'ประเภท',
    'ระหว่างวันที่:',
    'จำนวนการชำระเงินที่สำเร็จ:'
  ]
  const ws = {}
  const range = { s: { c: 10000000, r: 10000000 }, e: { c: 0, r: 0 } }
  for (let R = 0; R !== data.length; ++R) {
    for (let C = 0; C !== data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R
      if (range.s.c > C) range.s.c = C
      if (range.e.r < R) range.e.r = R
      if (range.e.c < C) range.e.c = C
      const cell = { v: data[R][C], s: defaultCellStyle }
      if (cell.v == null) continue
      if (headerSub.includes(cell.v)) {
        cell.s = evenRowStyle2
      }
      if (headerTxt.includes(cell.v)) {
        cell.s = headerCellStyle2
      }
      const cellref = XLSX.utils.encode_cell({ c: C, r: R })
      if (typeof cell.v === 'number') cell.t = 'n'
      else if (typeof cell.v === 'boolean') cell.t = 'b'
      else if (cell.v instanceof Date) {
        cell.t = 'n'
        cell.z = XLSX.SSF._table[14]
        cell.v = this.dateNum(cell.v)
      } else cell.t = 's'
      ws[cellref] = cell
    }
  }
  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range)
  return ws
}

const sheet2blob = (arrsheet) => {
  const workbook = {
    SheetNames: [],
    Sheets: {}
  }
  workbook.SheetNames.push(arrsheet[0].sheetName)
  workbook.Sheets[arrsheet[0].sheetName] = arrsheet[0].sheet
  workbook.SheetNames.push(arrsheet[1].sheetName)
  workbook.Sheets[arrsheet[1].sheetName] = arrsheet[1].sheet
  workbook.SheetNames.push(arrsheet[2].sheetName)
  workbook.Sheets[arrsheet[2].sheetName] = arrsheet[2].sheet
  workbook.SheetNames.push(arrsheet[3].sheetName)
  workbook.Sheets[arrsheet[3].sheetName] = arrsheet[3].sheet
  workbook.SheetNames.push(arrsheet[4].sheetName)
  workbook.Sheets[arrsheet[4].sheetName] = arrsheet[4].sheet
  workbook.SheetNames.push(arrsheet[5].sheetName)
  workbook.Sheets[arrsheet[5].sheetName] = arrsheet[5].sheet
  workbook.SheetNames.push(arrsheet[6].sheetName)
  workbook.Sheets[arrsheet[6].sheetName] = arrsheet[6].sheet
  // workbook.Sheets[arrsheet[0].sheetName] = arrsheet[0].sheet
  // workbook.Sheets[arrsheet[1].sheetName] = arrsheet[1].sheet
  const wopts = {
    bookType: 'xlsx',
    bookSST: false,
    type: 'binary'
  }
  const wbout = XLSX.write(workbook, wopts, {
    defaultCellStyle: defaultCellStyle
  })
  const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' })
  function s2ab (s) {
    const buf = new ArrayBuffer(s.length)
    const view = new Uint8Array(buf)
    for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
    return buf
  }
  return blob
}

const addRangeBorder = (range, ws) => {
  const arr = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ]

  range.forEach(item => {
    const startColNumber = Number(item.s.r)
    const endColNumber = Number(item.e.r)
    const startRowNumber = Number(item.s.c)
    const endRowNumber = Number(item.e.c)
    const test = ws[arr[startRowNumber] + (startColNumber + 1)]
    for (let col = startColNumber; col <= endColNumber; col++) {
      for (let row = startRowNumber; row <= endRowNumber; row++) {
        ws[arr[row] + (col + 1)] = test
      }
    }
  })
  return ws
}
