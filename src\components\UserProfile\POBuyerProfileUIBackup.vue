<template>
  <div>
  <!-- <v-container grid-list-xs> -->
    <v-card elevation="0">
      <!-- <v-card-title>รายการซื้อของฉัน</v-card-title> -->
      <!-- <v-overlay :value="overlay">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay> -->
      <v-row no-gutters>
        <v-col v-if="dataRole.role === 'purchaser'" cols="12" class="py-0">
          <a-tabs @change="SelectDetailOrder">
            <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if="dataRole.role === 'ext_buyer'" cols="12" class="py-0">
          <a-tabs @change="SelectDetailOrder">
            <a-tab-pane v-for="item in OrderNameExtBuyer" :key="item.key" :tab="item.name"></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if = "disableTable === true" cols="4" class="ml-4">
          <v-text-field v-model="search" dense hide-details outlined rounded placeholder="ค้นหาใบสั่งซื้อ">
            <v-icon slot="append">mdi-magnify </v-icon>
          </v-text-field>
        </v-col>
        <v-col cols="12">
          <v-card v-if = "disableTable === true" outlined class="small-card mx-4 my-5">
            <v-data-table
             :headers="keyCheckHead == 3 ? headersSuccess : keyCheckHead == 1 ? headersPending : keyCheckHead == 2 ? headersNoPaid : keyCheckHead == 4 ? headersFail : keyCheckHead == 6 ? headersApprove : keyCheckHead == 0 ? headersAll : headers"
             :items="DataTable"
             :search="search"
             height="480"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                <div v-if="item.pdf_for_buyer !== ''">
                  <a @click="orderDetail(item)">{{item.payment_transaction_number}}</a>
                </div>
                <div v-else>
                  {{item.payment_transaction_number}}
                </div>
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                {{ Number(item.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              </template>
              <template v-slot:[`item.payment`]="{ item }">
                <v-row justify="center" >
                  <v-btn v-if="item.seller_sent_status ==='Success'" text disabled rounded color="#27AB9C" small @click="GoToPayment(item)">
                    <b>จ่ายเงิน</b>
                    <v-icon x-small>mdi-greater-than</v-icon>
                  </v-btn>
                  <v-btn v-else-if="item.seller_sent_status ==='cancel'" text disabled rounded color="#27AB9C" small @click="GoToPayment(item)">
                    <b>จ่ายเงิน</b>
                    <v-icon x-small>mdi-greater-than</v-icon>
                  </v-btn>
                  <v-btn v-else text rounded color="#27AB9C" small @click="GoToPayment(item)">
                    <b>จ่ายเงิน</b>
                    <v-icon x-small>mdi-greater-than</v-icon>
                  </v-btn>
                </v-row>
              </template>
              <template v-slot:[`item.transaction_status`]="{ item }">
                <span v-if="item.transaction_status === 'Success' && item.seller_sent_status !== 'cancel'">
                  <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                </span>
                <span v-else-if="item.seller_sent_status === 'cancel' || item.transaction_status === 'Cancel'">
                  <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกสินค้า</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Pending'">
                  <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Approve'">
                  <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Fail'">
                  <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ชำระเงินไม่สำเร็จ</v-chip>
                </span>
                <span v-else>
                  <v-chip class="ma-2" color="#E5EFFF" text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip>
                </span>
              </template>
              <template v-slot:[`item.buyer_received_status`]="{ item }">
                <v-row class="pt-5">
                  <v-select v-model="item.buyer_received_status" :items="receive_items" item-text="text" item-value="value" @change="UpdateStatusBuyer(item)" outlined dense></v-select>
                </v-row>
              </template>
              <template v-slot:[`item.detail`]="{ item }">
                <v-btn text rounded color="#27AB9C" small @click="goDetailPO(item)">
                  <b>รายละเอียด</b>
                  <v-icon x-small>mdi-greater-than</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>ไม่มีคำสั่งซื้อ</b></h2>
        </v-col>
      </v-row>
    </v-card>
  <!-- </v-container> -->
  </div>
</template>

<script>
import { Encode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      orderList: [],
      StateStatus: 0,
      disableTable: false,
      dataRole: '',
      OrderNamePurchaser: [
        // { key: 0, name: 'ข้อมูลไม่ครบ' },
        { key: 0, name: 'ทั้งหมด' },
        { key: 2, name: 'ยังไม่ชำระเงิน' },
        { key: 1, name: 'รออนุมัติ' },
        { key: 6, name: 'อนุมัติ' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' },
        { key: 4, name: 'ยกเลิก' }

      ],
      OrderNameExtBuyer: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 2, name: 'ยังไม่ชำระเงิน' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' },
        { key: 4, name: 'ยกเลิก' }
      ],
      keyCheckHead: 0,
      headers: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        // { text: 'ร้านค้า', value: 'order_number' },
        // { text: 'สินค้า', value: 'order_number' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สั่งซื้อ', value: 'paid_datetime', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จ่ายเงิน', value: 'payment', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersSuccess: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชำระเงินเมื่อ', value: 'paid_datetime', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' }
        // { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        // { text: 'วันที่ได้รับของ', value: 'received_date', align: 'center' }
      ],
      headersPending: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: ' ', value: 'detail', align: 'center', class: 'backgroundTable fontTable--text' }
        // { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        // { text: 'วันที่ได้รับของ', value: 'received_date', align: 'center' }
      ],
      headersApprove: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: ' ', value: 'detail', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersNoPaid: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จ่ายเงิน', value: 'payment', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersFail: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สั่งซื้อ', value: 'paid_datetime', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersAll: [
        { text: 'วันที่', value: 'created_at', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: ' ', value: 'detail', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    }
  },
  watch: {
    DataTable (val) {
      // console.log('DataTable', val)
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.orderList.all
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.pending
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.not_paid
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.DataTable = this.orderList.success
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 4) {
        this.DataTable = this.orderList.cancel
        this.keyCheckHead = 4
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 5) {
        this.DataTable = this.orderList.fail
        this.keyCheckHead = 5
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 6) {
        this.DataTable = this.orderList.approve
        this.keyCheckHead = 6
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  async created () {
    // console.log('StateStatus', this.StateStatus)
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    this.ListDataTable()
  },
  methods: {
    async ListDataTable () {
      var data = {
        role_user: this.dataRole.role
      }
      await this.$store.dispatch('actionListOrderBuyer', data)
      this.orderList = await this.$store.state.ModuleOrder.stateOrderListData.data
      // console.log('orderlist', this.orderList)
      // this.overlay = true
      // this.ProcurementData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('ProcurementData'))))
      // this.getDataTable()
      if (this.StateStatus === 0) {
        this.DataTable = this.orderList.all
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 1) {
        this.DataTable = this.orderList.pending
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 2) {
        this.DataTable = this.orderList.not_paid
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 3) {
        this.DataTable = this.orderList.success
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 4) {
        this.DataTable = this.orderList.cancel
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 5) {
        this.DataTable = this.orderList.fail
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (this.StateStatus === 6) {
        this.DataTable = this.orderList.approve
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    async UpdateStatusBuyer (item) {
      // console.log(item)
      if (item.seller_sent_status === 'not_sent') {
        this.$swal.fire({
          icon: 'warning',
          text: 'สินค้ากำลังจัดส่ง',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      } else {
        const update = {
          order_number: item.order_number,
          buyer_received_status: item.buyer_received_status
        }
        // console.log('update', update)
        await this.$store.dispatch('actionUpdateStatusBuyer', update)
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      }
    },
    async GoToPayment (item) {
      const PaymentID = {
        payment_transaction_number: item.payment_transaction_number
      }
      // console.log('payment_transaction_number', PaymentID)
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      // console.log('respose paymenttttttttt', response)
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    async orderDetail (val) {
      // console.log('path to not tell =====>', val.pdf_for_buyer)
      window.open(`${val.pdf_for_buyer}`)
      // var data = {
      //   payment_transaction_number: val.payment_transaction_number
      // }
      //   console.log('data naja', data)
      // await this.$store.dispatch('actionOrderDetail', data)
      // await this.$router.push({ path: '/quotation1shop' }).catch(() => {})
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('order detail na', response)
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      //   console.log('กี่ร้านนะะะะะะะะะ', response.data[0].order_number[0])
      // }
    },
    SelectDetailOrder (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.ListDataTable()
    },
    // async getDataTable () {
    //   this.overlay = true
    //   const data = {
    //     procurement_org_id: this.ProcurementData.procurement_org_id
    //   }
    //   await this.$store.dispatch('actionListOrderProcurement', data)
    //   var response = await this.$store.state.ModuleCart.stateListOrderProcurement
    //   // console.log('response List Order Procurement', response)
    //   if (response.result === 'SUCCESS') {
    //     this.responseData = response.data
    //     this.overlay = false
    //   }
    // },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateDetailOrder
      // console.log('response detail order', response)
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem('MyOrderDetail', Encode.encode(JSON.stringify(response.data)))
        this.$router.push('/myorderdetail')
      }
    },
    goDetailPO (item) {
      var data = {
        payment_transaction_number: item.payment_transaction_number,
        role_user: this.dataRole.role
      }
      localStorage.setItem('orderNumber', Encode.encode(data))
      if (item.transaction_status === 'Pending' || item.transaction_status === 'Approve') {
        this.$router.push('/pobuyerdetailapprove')
      } else {
        this.$router.push('/pobuyerdetail')
      }
    }
  }
}
</script>
