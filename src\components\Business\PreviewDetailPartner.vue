<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" style="background: #FFFFFF;">
    <v-card width="100%" height="100%" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">Preview สินค้าและบริการ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon>Preview สินค้าและบริการ</v-card-title>
    </v-card>
    <v-row class="mx-1">
      <v-col>
        <span style="font-size: medium">ตัวอย่างรายละเอียดของ Partner และ Package</span>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-card-text>
            <span style="font-size: medium"><b>ข้อมูลร้านค้า Partner</b></span>
          </v-card-text>
          <v-card-text>
          <!-- ข้อมูลส่วนแรก รูป และ detail ต่าง ๆ -->
            <div class="pb-10" v-if="!MobileSize && !IpadSize">
                <v-card elevation="0">
                    <v-row no-gutters>
                    <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 340px;': MobileSize ? 'position: relative; height: 210px;' :IpadProSize?'position: relative; height: 460px;': 'position: relative; height: 519px;'">
                        <v-carousel
                        :class="IpadSize ? 'pa-5' : 'pa-4'"
                        style="position: absolute; border-radius: 12px;"
                        :height=" IpadSize ? '220': IpadProSize? '290': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length !== 0 ? '110': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length === 0 ? '120':'380'"
                        hide-delimiters
                        >
                        <v-carousel-item v-for="(image, index) in filteredBanners" :key="index">
                            <v-img
                            :src="image.media_path"
                            height="100%"
                            width="100%"
                            alt="Software Marketplace"
                            style="border-radius: 1vw !important;"
                            ></v-img>
                        </v-carousel-item>
                        <template v-if="filteredBanners.length === 0">
                            <v-carousel-item>
                            <v-img
                                src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                                height="100%"
                                width="100%"
                                contain
                            ></v-img>
                            </v-carousel-item>
                        </template>
                        </v-carousel>
                        <v-col :style="IpadSize ? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                        <v-row no-gutters>
                            <v-col dense no-gutters :cols="MobileSize? 5 : 5" :md="IpadProSize ? 5 : 4" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0'" style="text-align: center;">
                            <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-if="showpartnerdetail && showpartnerdetail.media && showpartnerdetail.media.find(image => image.image_type === 'main')">
                                <v-img
                                :src="showpartnerdetail.media.find(image => image.image_type === 'main').media_path"
                                contain
                                style="border: 5px solid #ffffff"
                                ></v-img>
                            </v-avatar>
                            <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-else>
                                <v-img
                                src="@/assets/NoImage.png"
                                style="border: 10px solid #ffffff"
                                ></v-img>
                            </v-avatar>
                            </v-col>
                            <v-col :cols="MobileSize ? 7 : 7" :md="IpadProSize ? 7 : 8" :style="MobileSize ? 'padding-top: 35px' :IpadSize ? 'padding-top: 80px' : 'padding-top: 150px'">
                            <span :style="IpadSize ? 'font-size: medium;' : 'font-size: 24px;' "><b>{{ showpartnerdetail && showpartnerdetail.partner_name }}</b></span><br>
                            <span><b>{{ showpartnerdetail && showpartnerdetail.business_name_th }}</b></span><br>
                            <span :class="IpadSize || IpadProSize ? 'inline-flex' : 'inline-flex-desktop'">
                                <a>
                                <v-btn
                                color="#3b5998"
                                fab
                                x-small
                                dark
                                style="margin-top: -2px; box-shadow: none;">
                                <v-img width="0"
                                    src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                                ></v-img>
                                </v-btn>
                                </a>
                                <a>
                                <v-btn
                                color="#39cd00"
                                fab
                                x-small
                                dark
                                style="margin-top: -2px; box-shadow: none;">
                                    <v-img width="0"
                                    src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                                ></v-img>
                                </v-btn>
                                </a>
                                <span>| {{ showpartnerdetail && showpartnerdetail.partner_phone_no }}</span>
                            </span>
                            </v-col>
                        </v-row>
                        </v-col>
                    </v-col>
                    <v-col cols="12" class="d-flex align-center px-5">
                        <img
                        src="@/assets/Marketplace_partner/Capa_1.png"
                        alt="เกี่ยวกับ Partner"
                        width="25px"
                        class="mr-2"
                        />
                        <span><b>เกี่ยวกับ Partner</b></span>
                    </v-col>
                    <v-col cols="12" class="px-5 mt-3 d-flex flex-column">
                        <span>{{ showpartnerdetail && showpartnerdetail.detail }}</span>
                        <span>เว็บไซต์ : {{ showpartnerdetail && showpartnerdetail.url_name }}</span>
                        <div class="d-flex align-center">
                        <span>ประเภทบริการ : </span>
                        <v-chip-group class="d-inline-flex" style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('ERP')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b>
                            <!-- style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" v-bind:style="{ padding: MobileSize ? '3px 6px' : '', fontSize: MobileSize ? '10px' : '' }"><b>ERP</b> -->
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Web Development')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b>
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('POS')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>POS</b>
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('OMS')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>OMS</b>
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Marketing')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b>
                            </v-chip>
                        </v-chip-group>
                        </div>
                    </v-col>
                    </v-row>
                </v-card>
            </div>
            <div class="pb-10" v-if="MobileSize || IpadSize">
                <v-card elevation="0">
                    <v-row no-gutters>
                    <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 340px;': MobileSize ? 'position: relative; height: 210px;' :IpadProSize?'position: relative; height: 460px;': 'position: relative; height: 519px;'">
                        <v-carousel
                        style="position: absolute; border-radius: 12px; padding: 4vw;"
                        :height=" IpadSize ? '220': IpadProSize? '290': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length !== 0 ? '110': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length === 0 ? '120':'380'"
                        hide-delimiters
                        >
                        <v-carousel-item v-for="(image, index) in filteredBanners" :key="index">
                            <v-img
                            :src="image.media_path"
                            height="100%"
                            width="100%"
                            alt="Software Marketplace"
                            style="border-radius: 1vw !important;"
                            ></v-img>
                        </v-carousel-item>
                        <template v-if="filteredBanners.length === 0">
                            <v-carousel-item>
                            <v-img
                                src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                                height="100%"
                                width="100%"
                                contain
                            ></v-img>
                            </v-carousel-item>
                        </template>
                        </v-carousel>
                        <v-col :style="IpadSize ? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                        <v-row no-gutters>
                            <v-col dense no-gutters :cols="MobileSize? 5 : 5" :md="IpadProSize ? 5 : 4" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0'" style="text-align: center;">
                            <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-if="showpartnerdetail && showpartnerdetail.media && showpartnerdetail.media.find(image => image.image_type === 'main')">
                                <v-img
                                :src="showpartnerdetail.media.find(image => image.image_type === 'main').media_path"
                                contain
                                style="border: 5px solid #ffffff"
                                ></v-img>
                            </v-avatar>
                            <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-else>
                                <v-img
                                src="@/assets/NoImage.png"
                                style="border: 10px solid #ffffff"
                                ></v-img>
                            </v-avatar>
                            </v-col>
                            <v-col :cols="MobileSize ? 7 : 7" :md="IpadProSize ? 7 : 8" :style="MobileSize ? 'padding-top: 43px' :IpadSize ? 'padding-top: 80px' : 'padding-top: 150px'">
                            <span :style="MobileSize ? 'font-size: small;' : 'font-size: small;' "><b>{{ showpartnerdetail && showpartnerdetail.partner_name }}</b></span><br>
                            <span style="font-size: small"><b>{{ showpartnerdetail && showpartnerdetail.business_name_th }}</b></span><br>
                            <span :class="IpadSize || IpadProSize ? 'inline-flex' : 'inline-flex-desktop'">
                                <a>
                                <v-btn
                                color="#3b5998"
                                fab
                                x-small
                                dark
                                style="margin-top: -2px; box-shadow: none;">
                                <v-img width="0"
                                    src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                                ></v-img>
                                </v-btn>
                                </a>
                                <a>
                                <v-btn
                                color="#39cd00"
                                fab
                                x-small
                                dark
                                style="margin-top: -2px; box-shadow: none;">
                                    <v-img width="0"
                                    src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                                ></v-img>
                                </v-btn>
                                </a>
                                <span>| {{ showpartnerdetail && showpartnerdetail.partner_phone_no }}</span>
                            </span>
                            </v-col>
                        </v-row>
                        </v-col>
                    </v-col>
                    <v-col cols="12" class="d-flex align-center px-5 mt-5">
                        <img
                        src="@/assets/Marketplace_partner/Capa_1.png"
                        alt="เกี่ยวกับ Partner"
                        width="25px"
                        class="mr-2"
                        />
                        <span><b>เกี่ยวกับ Partner</b></span>
                    </v-col>
                    <v-col cols="12" class="px-5 pt-3 d-flex flex-column">
                        <span>{{ showpartnerdetail && showpartnerdetail.detail }}</span>
                        <span class="mt-2">เว็บไซต์ : {{ showpartnerdetail && showpartnerdetail.url_name }}</span>
                        <div style="display: flex; overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px; max-width: 100%;">
                        <span style="position: sticky; left: 0; background-color: white; flex-shrink: 0; z-index: 1; display: flex; align-items: center;">
                            ประเภทบริการ :
                        </span>
                        <v-chip-group class="d-inline-flex" style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('ERP')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b>
                            <!-- style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" v-bind:style="{ padding: MobileSize ? '3px 6px' : '', fontSize: MobileSize ? '10px' : '' }"><b>ERP</b> -->
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Web Development')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b>
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('POS')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>POS</b>
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('OMS')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>OMS</b>
                            </v-chip>
                            <v-chip
                            v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Marketing')"
                            label
                            class="ml-2"
                            style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b>
                            </v-chip>
                        </v-chip-group>
                        </div>
                    </v-col>
                    </v-row>
                </v-card>
            </div>
            <v-col cols="12" class="px-5">
                <v-row style="margin-left: 0px; margin-right: 0px">
                    <h1 style="font-size:24px; font-weight: 700; color:#27AB9C;">สินค้า</h1>
                    <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px;"></v-spacer>
                </v-row>
            </v-col>
             <!-- สินค้า ชื่อ detail -->
            <v-col cols="12" class="text-center" style="font-size: 18px;">
                <span><b>{{ product_name }}</b></span>
                </v-col>
                <v-col cols="12" class="text-center">
                <div class="scrollable-content-service-detail showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="description"></div>
            </v-col>
            <!-- รายการ package -->
            <div class="pb-10" v-if="!MobileSize">
                <v-col cols="12" class="d-flex justify-center" :style="IpadSize ? 'gap: 2vw; flex-wrap: wrap; height: 48vw; overflow-y: auto;' : IpadProSize ? 'gap: 2vw; flex-wrap: wrap; height: 37vw; overflow-y: auto;' : 'gap: 1vw;'">
                    <v-card v-for="(item, index) in dataServices" :key="index" class="pa-3" :style="IpadSize ? 'border-radius: 1vw; width: 35vw;' : IpadProSize ? 'border-radius: 1vw; width: 25vw;' : 'border-radius: 1vw; width: 18vw;'">
                    <v-card-text class="d-flex flex-column">
                        <span style="color: #000;">package</span>
                        <span v-if="item.selectPackage === 'PACK-S'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">S - Small</span>
                        <span v-else-if="item.selectPackage === 'PACK-M'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">M - Medium</span>
                        <span v-else-if="item.selectPackage === 'PACK-L'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">L - Large</span>
                        <span v-else-if="item.selectPackage === 'CUSTOM'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">Custom</span>
                    </v-card-text>
                    <v-card-text style="color: #000; margin-top: -1vw;">
                        <span v-if="item.selectTypePayment === 'เปอร์เซ็นต์'"><span class="font-weight-bold" style="font-size: x-large;">{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เปอร์เซ็นต์(%)</span></span>
                        <span v-else-if="item.selectTypePayment === 'รายเดือน'"><span class="font-weight-bold" style="font-size: x-large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เดือน</span></span>
                        <span v-else><span class="font-weight-bold" style="font-size: x-large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/ปี</span></span>
                    </v-card-text>
                    <span class="mb-5 pa-3">รายละเอียด package :</span>
                    <v-card-text :style="IpadSize || IpadProSize ? 'height: 17vw; overflow-y: auto;' : 'height: 10vw; overflow-y: auto;'">
                        <div class="mt-2" v-for="(itemFunc, indexFunc) in item.dataFunction" :key="indexFunc">
                        <span v-if="item.dataFunction.length !== 0 && item.dataFunction[0].nameFunc !== ''">
                            <v-icon class="mr-2" color="#52c41a">mdi-check-circle</v-icon>
                            <span>{{itemFunc.nameFunc}} - </span>
                            <span>{{itemFunc.countTransaction}} </span>
                            <span> {{itemFunc.unitType}}</span>
                        </span>
                        <!-- <span v-else>กำหนดรายละเอียดการใช้งานได้เอง</span> -->
                        </div>
                        <div class="mt-2">
                        <span v-if="item.selectPackage === 'CUSTOM'">กำหนดรายละเอียดการใช้งานได้เอง</span>
                        </div>
                    </v-card-text>
                    </v-card>
                </v-col>
            </div>
            <v-card style="border: none !important;" outlined v-else>
                <v-col style="height: 90vw; overflow-y: auto; margin-top: -2vw;">
                    <v-card class="ml-1 mr-1 mb-5" v-for="(item, index) in dataServices" :key="index">
                    <v-card-text class="d-flex flex-column">
                        <span style="color: #000;">package</span>
                        <span v-if="item.selectPackage === 'PACK-S'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">S - Small</span>
                        <span v-else-if="item.selectPackage === 'PACK-M'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">M - Medium</span>
                        <span v-else-if="item.selectPackage === 'PACK-L'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">L - Large</span>
                        <span v-else-if="item.selectPackage === 'CUSTOM'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">Custom</span>
                    </v-card-text>
                    <v-card-text style="color: #000; margin-top: -6vw;">
                        <span v-if="item.selectTypePayment === 'เปอร์เซ็นต์'"><span class="font-weight-bold" style="font-size: large;">{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เปอร์เซ็นต์(%)</span></span>
                        <span v-else-if="item.selectTypePayment === 'รายเดือน'"><span class="font-weight-bold" style="font-size: large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เดือน</span></span>
                        <span v-else><span class="font-weight-bold" style="font-size: large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/ปี</span></span>
                    </v-card-text>
                    <span class="mb-5 pa-3">รายละเอียด package :</span>
                    <v-card-text style="height: 45vw; overflow-y: auto;">
                        <div class="mt-2" v-for="(itemFunc, indexFunc) in item.dataFunction" :key="indexFunc">
                        <span v-if="item.dataFunction.length !== 0 && item.dataFunction[0].nameFunc !== ''">
                            <v-icon class="mr-2" color="#52c41a">mdi-check-circle</v-icon>
                            <span>{{itemFunc.nameFunc}} : </span>
                            <span>{{itemFunc.countTransaction}} </span>
                            <span> {{itemFunc.unitType}}</span>
                        </span>
                        <!-- <span v-else>กำหนดรายละเอียดการใช้งานได้เอง</span> -->
                        </div>
                        <div class="mt-2">
                        <span v-if="item.selectPackage === 'CUSTOM'">กำหนดรายละเอียดการใช้งานได้เอง</span>
                        </div>
                    </v-card-text>
                </v-card>
            </v-col>
            </v-card>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      product_name: '',
      description: '',
      package_name: '',
      payment_type: '',
      price: '',
      package_detail: '',
      service_detail: '',
      listService: [],
      listPackage: [],
      statusApprove: '',
      taxId: '',
      partnerCode: '',
      dialogShowMore: false,
      dataMore: '',
      partnerName: '',
      detailPartner: null,
      excess_transaction_price: '',
      showpartnerdetail: [],
      dataServices: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredBanners () {
      return Array.isArray(this.showpartnerdetail.media)
        ? this.showpartnerdetail.media.filter(image => image.image_type === 'banner')
        : []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    this.$store.commit('openLoader')
    await this.getTaxId()
    await this.getDetailPartner()
    await this.getPartnerCode()
    await this.getDetailPackage()
    this.$store.commit('closeLoader')
    // this.getPartnerCode()
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/PreviewDetailPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/PreviewDetailPartner' }).catch(() => {})
      }
    }
  },
  methods: {
    backToMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    },
    async getTaxId () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          // console.log('see tax id', this.taxId)
          // this.taxId = response.data.array_business[0].owner_tax_id
        }
      }
    },
    async getPartnerCode () {
      var data = {
        id_card_num: this.taxId
      }
      // this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        if (response.data.partner_code !== null) {
          this.havePartner = false
          this.partnerCode = response.data.partner_code
        } else {
          this.havePartner = true
        }
      }
      // this.$store.commit('closeLoader')
    },
    async getDetailPackage () {
      // this.$store.commit('openLoader')
      var data = { partner_code: this.partnerCode }
      // console.log('see data', data)
      await this.$store.dispatch('actionsGetDetailPackage', data)
      var response = await this.$store.state.ModuleBusiness.stateGetDetailPackage
      if (response.code === 200) {
        // this.$store.commit('closeLoader')
        if (response.data.length !== 0) {
          this.listService = response.data[0]
          this.product_name = this.listService.partner_name
          this.description = this.listService.detail
          this.dataServices = this.listService.list_package.map(packageItem => ({
            id: packageItem.id,
            statusPackage: packageItem.status,
            remarkPackage: packageItem.remark,
            selectPackage: packageItem.package_code,
            statusConnect: packageItem.partner_connect,
            // selectTypePayment: packageItem.payment_type === 'Monthly' ? 'รายเดือน' : packageItem.payment_type === 'Yearly' ? 'รายปี' : 'เปอร์เซ็นต์',
            selectTypePayment: packageItem.payment_type === 'Monthly' ? 'รายเดือน' : packageItem.payment_type === 'Yearly' ? 'รายปี' : '',
            productPrice: packageItem.package_code === 'CUSTOM' ? '>' + packageItem.price : packageItem.price,
            descriptionPackage: packageItem.package_detail === null ? '' : packageItem.package_detail,
            descriptionCondition: packageItem.service_detail,
            phone: packageItem.phone,
            email: packageItem.email,
            dataFunction: packageItem.list_function ? packageItem.list_function.map(listFunc => ({
              idFunc: listFunc.id,
              nameFunc: listFunc.name,
              unitType: listFunc.unit_function,
              countTransaction: listFunc.limit === -1 ? 'ไม่จำกัด' : listFunc.limit,
              priceTransaction: listFunc.excess_transaction_price === 0 ? '' : listFunc.excess_transaction_price,
              selectTypeUnit: listFunc.excess_transaction_unit === 'baht' ? 'บาท' : listFunc.excess_transaction_unit === 'percent' ? 'เปอร์เซ็นต์' : '',
              checkbox: listFunc.limit === -1
            }))
              : []
          }))
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async getDetailPartner () {
      var data = {
        id_card_num: this.taxId
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerList', data)
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerList
      this.showpartnerdetail = response.data[0]
      if (response.code === 200 && response.data.length !== 0) {
        if (this.showpartnerdetail.new_address.length === 0) {
          this.address = response.data[0].address[0]
        } else {
          this.address = response.data[0].new_address[0]
        }
      }
      this.$store.commit('closeLoader')
    },
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    }
  }
}
</script>

<style>

</style>
