import Dashaffilate from './axios_dashboard_affiliate_shop'

const Mo<PERSON>leDashboardAffiliateShop = {
  state: {
    transactions: null,
    affiliateTable: [],
    affiliateUsersTable: [],
    paginationProductsTable: [],
    paginationUsersTable: [],
    topTenProduct: [],
    dashboardAffiliateChart: [],
    excelDownload: null,
    topTenSoldsProduct: [],
    sellerReport: []
  },
  mutations: {
    mutationsDashboardGraph (state, data) {
      state.transactions = data
    },
    mutationsAffiliateTable (state, data) {
      state.affiliateTable = data
    },
    mutationsAffiliateUsersTable (state, data) {
      state.affiliateUsersTable = data
    },
    mutationsPaginationProductTable (state, data) {
      state.paginationProductsTable = data
    },
    mutationsPaginationUsersTable (state, data) {
      state.paginationUsersTable = data
    },
    mutationsTopTenProduct (state, data) {
      state.topTenProduct = data
    },
    mutationsDashboardCharts (state, data) {
      state.dashboardAffiliateChart = data
    },
    mutationsExportExcel (state, data) {
      state.excelDownload = data
    },
    mutationsToptenSolds (state, data) {
      state.topTenSoldsProduct = data
    },
    mutationsSellerReport (state, data) {
      state.sellerReport = data
    }
  },
  actions: {
    async actionDashboardAffiliateGraph (context, access) {
      const responseData = await Dashaffilate.DashboardAffiliateGraph(access)
      await context.commit('mutationsDashboardGraph', responseData)
    },
    async actionAffiliateTable (context) {
      const responseData = await Dashaffilate.AffiliateTable()
      await context.commit('mutationsAffiliateTable', responseData)
    },
    async actionAffiliateUsersTable (context) {
      const responseData = await Dashaffilate.AffiliateUserTable()
      await context.commit('mutationsAffiliateUsersTable', responseData)
    },
    async actionPaginationsTable (context, access) {
      const responseData = await Dashaffilate.PaginationTable(access)
      await context.commit('mutationsPaginationTable', responseData)
    },
    async actionPaginationsProductsTable (context, access) {
      const reponseData = await Dashaffilate.PaginationTableProduct(access)
      await context.commit('mutationsPaginationProductTable', reponseData)
    },
    async actionPaginationsUsersTable (context, access) {
      const reponseData = await Dashaffilate.PaginationTableUsers(access)
      await context.commit('mutationsPaginationUsersTable', reponseData)
    },
    async actionTopTenMostSoldPropuct (context, access) {
      const reponseData = await Dashaffilate.TopTenMostSoldPropuct(access)
      await context.commit('mutationsTopTenProduct', reponseData)
    },
    async actionChartsAffiliateDashboard (context, access) {
      const reponseData = await Dashaffilate.SummaryChart(access)
      await context.commit('mutationsDashboardCharts', reponseData)
    },
    async actionExportExcel (context, access) {
      const reponseData = await Dashaffilate.excelDownload(access)
      await context.commit('mutationsExportExcel', reponseData)
    },
    async actionToptenSoldProduct (context, access) {
      const reponseData = await Dashaffilate.SellerTopSolds(access)
      await context.commit('mutationsToptenSolds', reponseData)
    },
    async actionSellerReport (context, access) {
      const reponseData = await Dashaffilate.SellerReport(access)
      await context.commit('mutationsSellerReport', reponseData)
    }
  }
}

export default ModuleDashboardAffiliateShop
