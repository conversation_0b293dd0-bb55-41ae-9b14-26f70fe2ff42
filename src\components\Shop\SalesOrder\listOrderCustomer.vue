<template>
  <v-container>
    <v-card
      width="100%"
      height="100%"
      elevation="0"
      class="mb-4 mt-2"
      style="overflow: hidden"
    >
      <v-card-text class="px-2">
        <v-row class="mx-0" v-if="!MobileSize">
          <v-card-title
            class="pl-2"
            style="
              font-weight: bold;
              font-size: 24px;
              line-height: 32px;
              color: #333333;
            "
            >
            <v-icon color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>
            ประวัติรายการสั่งซื้อลูกค้า</v-card-title>
        </v-row>
        <v-row class="" v-else>
          <v-card-title
            style="
              font-weight: bold;
              font-size: 18px;
              line-height: 32px;
              color: #333333;
            "
            ><v-icon color="#1AB759" class="mr-2" @click="backtoMenu()"
              >mdi-chevron-left</v-icon
            >
            ประวัติรายการสั่งซื้อลูกค้า</v-card-title
          >
        </v-row>
        <!-- แถว 1 -->
        <v-row dense>
          <!-- <v-col cols="12" class="px-2 py-0">
            <a-tabs @change="SelectDetailOrder">
              <a-tab-pane key="ทั้งหมด"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="ยังไม่ชำระเงิน"><span slot="tab">ยังไม่ชำระเงิน <a-tag color="#E9A016" style="border-radius: 8px;">{{ countOrderNotpaid }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="ชำระเงินสำเร็จ"><span slot="tab">ชำระเงินสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderSuccess }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="ชำระเงินไม่สำเร็จ"><span slot="tab">ชำระเงินไม่สำเร็จ <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderFail }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="ชำระเงินแบบเครดิตเทอม"><span slot="tab">ชำระเงินแบบเครดิตเทอม <a-tag color="#1B5DD6" style="border-radius: 8px;">{{ countOrderCreditTerm }}</a-tag></span></a-tab-pane>
              <a-tab-pane key="ยกเลิก"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col> -->
          <v-col
            cols="7"
            md="8"
            sm="12"
            :class="!MobileSize ? 'pl-2 pt-0 pt-6' : 'mb-3'"
          >
            <v-text-field
              v-model="search"
              dense
              hide-details
              style="border-radius: 8px;"
              :class="MobileSize ? '' : IpadSize ? 'pr-0' : 'pr-4'"
              outlined
              placeholder="ค้นหาจากชื่อผู้ขาย หมายเลขคำสั่งซื้อ หรือชื่อสินค้าในทุกคำสั่งซื้อ"
            >
              <v-icon slot="append" color="#CCCCCC">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="3" v-if="MobileSize">
            <v-btn @click="OpenModalFilter()" outlined rounded color="#27AB9C" height="36"><v-icon size="24" left dark>mdi-filter-outline</v-icon>ตัวกรอง</v-btn>
          </v-col>
          <v-col
            v-if="!MobileSize"
            cols="12"
            md="4"
            sm="12"
            :class="!MobileSize && !IpadSize ? 'pt-0' : IpadSize ? '' : 'pl-2 pr-2 mb-3'"
          >
            <v-row dense :class="!MobileSize ? 'pt-6' : ''">
              <v-col cols="4" md="4" sm="3" :class="!MobileSize ? 'pt-3' : 'pt-3 pr-0'">
                <span
                  style="
                    font-size: 16px;
                    line-height: 24px;
                    color: #333333;
                    font-weight: 400;
                  "
                  >สถานะรายการ :</span
                >
              </v-col>
              <v-col cols="8" md="8" sm="9">
                <v-select
                  v-model="statusSelect"
                  :items="statusItem"
                  item-text="text"
                  item-value="value"
                  append-icon="mdi-chevron-down"
                  outlined
                  class="setCustomSelect"
                  dense
                  @change="getOrder()"
                  style="border-radius: 8px;"
                  hide-details
                ></v-select>
              </v-col>
              <!-- <v-col cols="6">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >ใบกำกับภาษี</span
                >
                <v-select
                  v-model="InvoiceSelect"
                  :items="invoiceItem"
                  item-text="text"
                  item-value="value"
                  @change="getOrder()"
                  outlined
                  dense
                ></v-select>
              </v-col> -->
            </v-row>
          </v-col>
        </v-row>
        <!-- แถว 2 -->
        <v-row dense class="px-1" v-if="!MobileSize">
          <v-col cols="12" md="4" sm="6">
            <v-row dense :class="!MobileSize ? 'pt-5' : 'pt-0'">
              <v-col cols="4" :class="!MobileSize ? 'pt-3 pr-0' : 'pt-3 pr-0'">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >Pay Type : </span
                >
              </v-col>
              <v-col cols="8">
                <v-select
                  v-model="PayTypeSelect"
                  :items="payTypeItem"
                  item-text="text"
                  item-value="value"
                  @change="getOrder()"
                  append-icon="mdi-chevron-down"
                  style="border-radius: 8px;"
                  class="setCustomSelect"
                  :class="MobileSize ? '' : IpadSize ? 'pr-0' : 'pr-4'"
                  outlined
                  dense
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-row dense :class="!MobileSize ? 'pt-5' : 'pt-0'">
              <v-col cols="4" :class="!MobileSize ? 'pt-3 pr-0 pl-0' : 'pt-3 pr-0'">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >วันที่สั่งซื้อ : </span
                >
              </v-col>
              <v-col cols="8">
                <v-dialog
                  ref="dialogBuyDate"
                  v-model="modalBuyDate"
                  :return-value.sync="date"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="buyDate"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      style="border-radius: 8px;"
                      dense
                      @change="getOrder()"
                      :class="MobileSize ? '' : IpadSize ? 'pr-0' : 'pr-4'"
                      placeholder="วว/ดด/ปปปป"
                      ><v-icon slot="append" color="#CCCCCC"
                        >mdi-calendar-multiselect</v-icon
                      ></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="date"
                    color = "#27AB9C"
                    scrollable
                    reactive
                    locale="Th-th"
                    @change="setValueBuyDate(date)"
                    :max="
                      new Date(
                        Date.now() - new Date().getTimezoneOffset() * 60000
                      )
                        .toISOString()
                        .substr(0, 10)
                    "
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="primary" @click="closeModalBuyDate($refs)">
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="
                        $refs.dialogBuyDate.save(date);
                        getOrder()
                      "
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="12">
            <v-row dense :class="!MobileSize && !IpadSize ? 'pt-5' : 'pt-0'">
              <v-col cols="4" sm="3" :class="!MobileSize ? 'pt-3 pr-0' : 'pt-3 pr-0'">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >วันที่อนุมัติ : </span
                >
              </v-col>
              <v-col cols="8" sm="9" class="pl-0 pr-0">
                <v-dialog
                  ref="dialogAcceptDate"
                  v-model="modalAcceptDate"
                  :return-value.sync="date1"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="acceptDate"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      @change="getOrder()"
                      style="border-radius: 8px;"
                      dense
                      placeholder="วว/ดด/ปปปป"
                      ><v-icon slot="append" color="#CCCCCC"
                        >mdi-calendar-multiselect</v-icon
                      ></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="date1"
                    scrollable
                    reactive
                    locale="Th-th"
                    @change="setValueAcceptDate(date1)"
                    color = "#27AB9C"
                    :max="
                      new Date(
                        Date.now() - new Date().getTimezoneOffset() * 60000
                      )
                        .toISOString()
                        .substr(0, 10)
                    "
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="primary"
                    @click="
                      closeModalAcceptDate($refs)">
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="
                        $refs.dialogAcceptDate.save(date1)
                        getOrder()
                      "
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <!-- แถว 3 -->
        <v-row dense class="px-1" v-if="!MobileSize">
          <v-col cols="12" md="8" sm="12">
            <v-row dense>
              <v-col cols="4" md="3" sm="3" :class="!MobileSize ? 'pt-3 pl-0 pr-0' : 'pt-3 px-0'">
                <span
                  style="
                    font-size: 16px;
                    line-height: 24px;
                    color: #333333;
                    font-weight: 400;
                  "
                  >วันที่รอบบริการ :</span
                >
              </v-col>
              <v-col cols="8" md="9" sm="9" >
                <v-dialog
                  ref="modalRangeDate"
                  v-model="modalRangeDate"
                  :return-value.sync="dateRange"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="RangeDate1"
                      v-bind="attrs"
                      v-on="on"
                      style="border-radius: 8px;"
                      outlined
                      dense
                      :class="MobileSize || IpadSize ? '' : 'pr-4'"
                      placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                      ><v-icon slot="append" color="#CCCCCC"
                        >mdi-calendar-multiselect</v-icon
                      ></v-text-field>
                  </template>
                  <v-date-picker
                    color="#27AB9C"
                    v-model="dateRange"
                    scrollable
                    range
                    reactive
                    locale="Th-th"
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="primary" @click="CloseModalRangeDate()">
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="setValueRangeDate(dateRange)"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="12">
            <v-row dense>
              <v-col cols="3" sm="2" class="pt-3">
                <span
                  style="
                    font-size: 16px;
                    line-height: 24px;
                    color: #333333;
                    font-weight: 400;
                  "
                  >WHT :</span
                >
              </v-col>
              <v-col cols="9" sm="10">
                <v-select
                  v-model="statusSelect"
                  :items="statusItem"
                  item-text="text"
                  item-value="value"
                  append-icon="mdi-chevron-down"
                  outlined
                  dense
                  class="setCustomSelect"
                  @change="getOrder()"
                  style="border-radius: 8px;"
                  hide-details
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <!-- แถวที่ 4 -->
        <v-row dense class="px-1" v-if="!MobileSize">
          <v-col cols="12" md="6" sm="12">
            <v-row dense :class="MobileSize ? 'pt-3' : 'pt-3'">
              <v-col cols="5" sm="4" class="pt-3">
                <span
                  style="
                    font-size: 16px;
                    line-height: 24px;
                    color: #333333;
                    font-weight: 400;
                  "
                  >สถานะการชำระเงิน :</span
                >
              </v-col>
              <v-col cols="7" sm="8">
                <v-select
                  v-model="selectPaymentStatus"
                  :items="statusPaymentStatusItem"
                  item-text="text"
                  item-value="value"
                  append-icon="mdi-chevron-down"
                  outlined
                  dense
                  class="setCustomSelect"
                  @change="getOrder()"
                  style="border-radius: 8px;"
                  hide-details
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row dense class="px-0">
          <v-col cols="12" md="5" sm="12" align="start" :class="MobileSize ? 'pl-2' : 'pt-6'">
            <span
              :class="MobileSize ? '' : ''"
              style="line-height: 24px; align-items: center; color: #333333; font-weight: 400;"
              :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'"
              >รายการสั่งซื้อลูกค้า {{ showCountOrder }} รายการ</span
            >
          </v-col>
          <v-col cols="12" class="mt-2" v-if="MobileSize">
            <v-btn @click="linkToEfact()" rounded color="#27AB9C" height="40" class="white--text mr-2">เข้าระบบ E-FACT</v-btn>
            <v-btn v-if="MobileSize" @click="downloadExcel()" rounded width="143" height="40" outlined color="#27AB9C" :disabled="showCountOrder === 0">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/file-export-solid.png')" max-width="16" max-height="16"></v-img>Export File
            </v-btn>
          </v-col>
          <v-col cols="12" md="7" sm="12" align="end" class="pt-3" v-if="!MobileSize">
            <v-btn @click="linkToEfact()" rounded color="#27AB9C" height="40" class="white--text mr-2">เข้าระบบ E-FACT</v-btn>
            <v-btn @click="reSetSearch()" class="white--text mr-2" rounded color="#27AB9C" width="100" height="40"
              ><v-icon small class="mr-1">mdi-restart</v-icon
              >ล้างค่า</v-btn
            >
            <v-btn @click="downloadExcel()" rounded width="153" height="40" outlined color="#27AB9C" :disabled="showCountOrder === 0">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/file-export-solid.png')" max-width="16" max-height="16"></v-img>Export File</v-btn>
          </v-col>
        </v-row>
        <v-row dense class="px-0">
          <v-col cols="12">
            <v-card
              v-if="disableTable === true"
              outlined
              class="small-card my-5"
              min-height="436"
            >
              <v-data-table
                :headers="headersAll"
                :items="DataTableLasted"
                :search="search"
                :page.sync="page"
                style="width: 100%"
                height="100%"
                @pagination="countOrdar"
                :items-per-page="10"
                class=""
                no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
                :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
              >
                <template v-slot:[`item.approved_at`]="{ item }">
                  <span v-if="item.approved_at !== '-' && item.approved_at === null">
                    {{
                    new Date(item.approved_at).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })
                  }}
                  </span>
                  <span v-else>
                    -
                  </span>
                </template>
                <template v-slot:[`item.order_number`]="{ item }">
                  <a v-if="item.qt_order !== '-' && item.transaction_status === 'Success'" :href="item.pdf_qt_path" target="_blank" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_number }}</a>
                  <span v-else>{{ item.order_number }}</span>
                </template>
                <template v-slot:[`item.order_created_date`]="{ item }">
                  <v-row class="pl-0" style="height: 40px;">
                    <!-- <div v-if="item.condition_send_cs !== '-'" class="mr-2" :style="item.condition_send_cs === 'green' ? 'border: 2px solid green' : item.condition_send_cs === 'yellow' ? 'border: 2px solid yellow' : 'border: 2px solid red'"></div> -->
                    <div style="display: flex; justify-content: center; align-items: center;">
                      {{new Date(item.order_created_date).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}
                    </div>
                  </v-row>
                </template>
                <template v-slot:[`item.pay_type`]="{ item }">
                  <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                  <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                  <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.order_type`]="{ item }">
                  <div v-if="item.status_approve !== 'Normal' && item.status_approve !== 'Reject'">
                    <span style="color: #636363;" v-if="item.detail_status !== 'ยกเลิกคำสั่งซื้อ' && (item.transaction_status === '-' || item.transaction_status === null)"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ item.order_type }}</span>
                    <span v-else-if="item.detail_status !== 'ยกเลิกคำสั่งซื้อ' && item.transaction_status !== '-'" :style="{ 'color' : getTextColorStatus(item.transaction_status)}"><v-icon :color="getTextColorStatus(item.transaction_status)">mdi-circle-medium</v-icon>{{ getTextStatus(item.transaction_status) }}</span>
                    <span style="color: #D1392B;" v-else><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ item.detail_status }}</span>
                  </div>
                  <div>
                    <span v-if="item.status_approve === 'Normal'" style="color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รออนุมัติคำสั่งซื้อ</span>
                    <span v-if="item.status_approve === 'Reject'" style="color: #D1392B;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>ปฏิเสธคำสั่งซื้อ</span>
                  </div>
                </template>
                <template v-slot:[`item.paid_datetime`]="{ item }">
                  {{
                    item.paid_datetime === '-'
                      ? item.paid_datetime
                      : new Date(item.paid_datetime).toLocaleDateString('th-TH', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: 'numeric',
                          minute: 'numeric'
                        })
                  }}
                </template>
                <template v-slot:[`item.contractDate`]="{ item }">
                  <span v-if="(item.start_date_contract !== '-' && item.start_date_contract === null) && (item.end_date_contract !== '-' && item.end_date_contract === null)">
                    {{
                      item.start_date_contract === '-'
                        ? item.start_date_contract
                        : new Date(item.start_date_contract).toLocaleDateString('th-TH', {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric'
                          })
                    }} -
                    {{
                      item.end_date_contract === '-'
                        ? item.end_date_contract
                        : new Date(item.end_date_contract).toLocaleDateString('th-TH', {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric'
                          })
                    }}
                  </span>
                  <span v-else> - </span>
                </template>
                <template v-slot:[`item.po_document_id`]="{ item }">
                  <a v-if="item.po_document_id !== '-'&& item.po_external !== '-'" :href="item.po_external" target="_blank">{{item.po_document_id}}</a>
                  <span v-else-if="item.po_document_id !== '-' && item.po_external === '-'">{{item.po_document_id}}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.pr_document_id`]="{ item }">
                  <a v-if="item.pr_document_id !== '-' && item.pr_external !== '-'" :href="item.pr_external" target="_blank">{{item.pr_document_id}}</a>
                  <span v-else-if="item.pr_document_id !== '-' && item.pr_external === '-'">{{item.pr_document_id}}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.so_document_id`]="{ item }">
                  <a v-if="(item.so_document_id !== '-' || item.ref_callback_so_id !== '-') && item.so_external !== '-'" :href="item.so_external" target="_blank">{{ item.so_document_id !== '-' ? item.so_document_id : item.ref_callback_so_id }}</a>
                  <span v-else-if="(item.so_document_id !== '-' || item.ref_callback_so_id !== '-') && item.so_external === '-'">{{ item.so_document_id !== '-' ? item.so_document_id : item.ref_callback_so_id }}</span>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.payment_transaction_status`]="{ item }">
                  <!-- <v-chip v-if="item.payment_transaction_status !== 'ยังไม่ชำระเงิน'" color="#F0F9EE" text-color="#1AB759">{{ item.payment_transaction_status }}</v-chip>
                  <v-chip v-else color="#FCF0DA" text-color="#E9A016">{{ item.payment_transaction_status }}</v-chip> -->
                  <v-chip v-if="item.transaction_status !== 'Success'" color="#FCF0DA" text-color="#E9A016">ยังไม่ชำระเงิน</v-chip>
                  <v-chip v-else color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                </template>
                <template v-slot:[`item.pv_no`]="{ item }">
                  <v-chip v-if="item.pv_no !== '-'" outlined color="blue" @click="openModaleWHT(item.pv_no)">ดูรายละเอียด</v-chip>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.transportation_status`]="{ item }">
                  <v-chip v-if="item.transportation_status !== '-'" :color="getColor(item.transportation_status)" :text-color="getTextColor(item.transportation_status)">{{ item.transportation_status }}</v-chip>
                  <span v-else>{{ item.transportation_status }}</span>
                </template>
                <template v-slot:[`item.order_mobilyst_no`]="{ item }">
                  <a v-if="item.order_mobilyst_no !== '-' && (item.url_tracking !== '-' && item.url_tracking !== '')" target="_blank" :href="item.url_tracking" style="text-decoration: underline; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</a>
                  <div @click="copyClipboard()" v-else-if="(item.order_mobilyst_no !== '-' && item.url_tracking === '')" style="cursor: pointer;" class="pl-2">
                    <input type="text" :value="item.order_mobilyst_no" id="trackingNumberList" style="display: none;">
                    <span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;">{{ item.order_mobilyst_no }}</span>
                  </div>
                  <span v-else>{{ item.order_mobilyst_no }}</span>
                </template>
                <template v-slot:[`item.payment_transaction_number`]="{ item }">
                  <div v-if="item.qt_order !== '-' && item.transaction_status === 'Success'">
                    <a @click="orderDetail(item)">{{
                      item.payment_transaction_number
                    }}</a>
                  </div>
                  <div v-else>
                    {{ item.payment_transaction_number }}
                  </div>
                </template>
                <template
                  v-slot:[`item.pdf_qt_path`]="{ item }"
                >
                  <div v-if="item.qt_number !== '-'">
                    <a @click="QuotationShow(item)">
                      <v-btn
                        x-small
                        style="
                          border: 1px solid #f2f2f2;
                          box-sizing: border-box;
                          box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                          border-radius: 4px;
                        "
                        :style="
                          IpadProSize
                            ? 'max-width: 24px; max-height: 24px;'
                            : IpadSize
                            ? 'max-width: 16px; max-height: 16px;'
                            : 'max-width: 32px; max-height: 32px;'
                        "
                        class="pt-4 pb-4"
                      >
                        <v-icon color="#27AB9C">mdi-file-document</v-icon>
                      </v-btn>
                    </a>
                  </div>
                  <div v-else-if="item.qt_order !== '-' && item.transaction_status === 'Success'">
                    <a @click="QuotationShow(item)">
                      <v-btn
                        x-small
                        style="
                          border: 1px solid #f2f2f2;
                          box-sizing: border-box;
                          box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                          border-radius: 4px;
                        "
                        :style="
                          IpadProSize
                            ? 'max-width: 24px; max-height: 24px;'
                            : IpadSize
                            ? 'max-width: 16px; max-height: 16px;'
                            : 'max-width: 32px; max-height: 32px;'
                        "
                        class="pt-4 pb-4"
                      >
                        <v-icon color="#27AB9C">mdi-file-document</v-icon>
                      </v-btn>
                    </a>
                  </div>
                  <div v-else>
                    <span>{{ '-' }}</span>
                  </div>
                </template>
                <template v-slot:[`item.pdf_cs_path`]="{ item }">
                  <div v-if="item.pdf_cs_path !== '-' && item.cs_number !== '-'" >
                    <a @click="costSheetShow(item)">
                      <v-btn
                        x-small
                        style="
                          border: 1px solid #f2f2f2;
                          box-sizing: border-box;
                          box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                          border-radius: 4px;
                        "
                        :style="
                          IpadProSize
                            ? 'max-width: 24px; max-height: 24px;'
                            : IpadSize
                            ? 'max-width: 16px; max-height: 16px;'
                            : 'max-width: 32px; max-height: 32px;'
                        "
                        class="pt-4 pb-4"
                      >
                        <v-icon color="#27AB9C">mdi-file-document</v-icon>
                      </v-btn>
                    </a>
                  </div>
                  <div v-else>
                    <span>{{ '-' }}</span>
                  </div>
                </template>
                <template v-slot:[`item.transaction_code_icon`]="{ item }">
                  <div
                    v-if="
                      item.transaction_code !== '-' &&
                      item.required_invoice !== '-'
                    "
                  >
                    <!-- <a @click="GetETaxPDF(item)"> -->
                    <v-btn
                      x-small
                      @click="GetETaxPDF(item)"
                      style="
                        border: 1px solid #f2f2f2;
                        box-sizing: border-box;
                        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                      "
                      :style="
                        IpadProSize
                          ? 'max-width: 24px; max-height: 24px;'
                          : IpadSize
                          ? 'max-width: 16px; max-height: 16px;'
                          : 'max-width: 32px; max-height: 32px;'
                      "
                      class="pt-4 pb-4"
                    >
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn>
                    <!-- </a> -->
                  </div>
                  <div
                    v-else-if="
                      item.transaction_code === '-' &&
                      item.required_invoice !== '-'
                    "
                  >
                    <span>{{ item.required_invoice }}</span>
                  </div>
                  <div v-else>
                    <span>{{ '-' }}</span>
                  </div>
                </template>
                <template v-slot:[`item.qt_order_invoice`]="{ item }">
                  <div v-if="item.qt_order_invoice !== '-' && item.qt_order_invoice !== ''">
                    <v-btn
                      x-small
                      @click="GetInvoice(item)"
                      style="
                        border: 1px solid #f2f2f2;
                        box-sizing: border-box;
                        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                      "
                      :style="
                        IpadProSize
                          ? 'max-width: 24px; max-height: 24px;'
                          : IpadSize
                          ? 'max-width: 16px; max-height: 16px;'
                          : 'max-width: 32px; max-height: 32px;'
                      "
                      class="pt-4 pb-4"
                    >
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn>
                  </div>
                  <div v-else>
                    <span>{{ '-' }}</span>
                  </div>
                </template>
                <template v-slot:[`item.QT_order_due_date`]="{ item }">
                  <div v-if="item.transaction_status === 'Success' && item.transaction_status_payment === 'Success'">
                    <a @click="goToDetailDueDate(item.order_number, customerId, item.sale_id)">ชำระเงินสำเร็จ</a>
                  </div>
                  <div v-else-if="item.transaction_status === 'Not Paid' && item.transaction_status_payment === 'Not Paid'">
                    <!-- {{new Date(item.invoice_due_date).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}} -->
                    <a v-if="item.invoice_due_date !== '-' && item.invoice_due_date !== ''" @click="goToDetailDueDate(item.order_number, customerId, item.sale_id)">{{new Date(item.invoice_due_date).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</a>
                  </div>
                  <div v-else>
                    <span>-</span>
                  </div>
                </template>
                <template v-slot:[`item.total_amount`]="{ item }">
                  {{
                    Number(item.total_amount).toLocaleString(undefined, {
                      minimumFractionDigits: 2
                    })
                  }}
                </template>
                <template v-slot:[`item.payment`]="{ item }">
                  <v-row justify="center">
                    <v-btn
                      v-if="item.seller_sent_status === 'Success'"
                      text
                      disabled
                      rounded
                      color="#1AB759"
                      small
                      @click="GoToPayment(item)"
                    >
                      <b>จ่ายเงิน</b>
                      <v-icon small>mdi-chevron-right</v-icon>
                    </v-btn>
                    <v-btn
                      v-else-if="item.seller_sent_status === 'cancel'"
                      text
                      disabled
                      rounded
                      color="#1AB759"
                      small
                      @click="GoToPayment(item)"
                    >
                      <b>จ่ายเงิน</b>
                      <v-icon small>mdi-chevron-right</v-icon>
                    </v-btn>
                    <v-btn
                      v-else
                      text
                      rounded
                      color="#1AB759"
                      small
                      @click="GoToPayment(item)"
                    >
                      <b>จ่ายเงิน</b>
                      <v-icon small>mdi-chevron-right</v-icon>
                    </v-btn>
                  </v-row>
                </template>
                <template v-slot:[`item.transaction_status`]="{ item }">
                  <span
                    v-if="
                      item.transaction_status === 'Success' &&
                      item.seller_sent_status !== 'cancel'
                    "
                  >
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#F0F9EE"
                      text-color="#1AB759"
                      >ชำระเงินสำเร็จ</v-chip
                    >
                  </span>
                  <span
                    v-else-if="
                      item.seller_sent_status === 'cancel' ||
                      item.transaction_status === 'Cancel'
                    "
                  >
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#f7c5ad"
                      text-color="#f50"
                      >ยกเลิกสินค้า</v-chip
                    >
                  </span>
                  <span v-else-if="item.transaction_status === 'Pending'">
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#FCF0DA"
                      text-color="#E9A016"
                      >รออนุมัติ</v-chip
                    >
                  </span>
                  <span v-else-if="item.transaction_status === 'Approve'">
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#F0F9EE"
                      text-color="#1AB759"
                      >อนุมัติ</v-chip
                    >
                  </span>
                  <span v-else-if="item.transaction_status === 'Credit'">
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#E5EFFF"
                      text-color="#1B5DD6"
                      >ชำระเงินแบบเครดิตเทอม</v-chip
                    >
                  </span>
                  <span v-else-if="item.transaction_status === 'Fail'">
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#F7D9D9"
                      text-color="#D1392B"
                      >ชำระเงินไม่สำเร็จ</v-chip
                    >
                  </span>
                  <span v-else>
                    <v-chip
                      :class="!MobileSize ? 'ma-2' : 'ma-0'"
                      color="#FCF0DA"
                      text-color="#E9A016"
                      >ยังไม่ชำระเงิน</v-chip
                    >
                  </span>
                </template>
                <template v-slot:[`item.buyer_received_status`]="{ item }">
                  <v-row class="pt-5">
                    <v-select
                      v-model="item.buyer_received_status"
                      :items="receive_items"
                      item-text="text"
                      item-value="value"
                      @change="UpdateStatusBuyer(item)"
                      outlined
                      dense
                    ></v-select>
                  </v-row>
                </template>
                <template v-slot:[`item.detail`]="{ item }">
                  <v-menu offset-y>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        v-on="on"
                        class="pt-4 pb-4"
                        x-small
                        outlined
                        style="
                          max-width: 32px;
                          max-height: 32px;
                          border-radius: 4px;
                          border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                          background: var(--neutral-ffffff, #fff);
                          box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                        "
                      >
                        <!-- <b>รายละเอียด</b> -->
                        <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="(items, index) in actionsItem"
                        :key="index"
                        link
                      >
                        <v-list-item-content
                          @click="gotoActions(item, items.value)"
                        >
                          <v-list-item-title>{{ items.text }}</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
        </v-row>
        <v-col cols="12" class="py-0">
          <!-- <a-tabs @change="SelectDetailOrder">
            <a-tab-pane :key="0"><span slot="tab">รายการสั่งซื้อที่ยังไม่จัดส่ง <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countPendingList }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">รายการสั่งซื้อที่จัดส่งแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countSuccessList }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">รายการสั่งซื้อที่ยังไม่ชำระเงิน <a-tag color="#FFA500" style="border-radius: 8px;">{{ countNotPendingList }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">รายการสั่งซื้อที่ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countCencelList }}</a-tag></span></a-tab-pane>
          </a-tabs> -->
        </v-col>
        <!-- <v-row v-if="disableTable === false" dense>
          <v-col cols="12" md="12" sm="12" xs="12" align="end" :class="!MobileSize ? 'pt-1 pr-4' : 'pl-2 pr-2 mb-3'">
            <v-btn :block="MobileSize" :class="MobileSize ? 'my-2 white--text' : 'mr-4 white--text'" style="border-radius: 4px;" @click="linkToETax()" color="#27AB9C">เข้าระบบ e-Tax</v-btn>
            <v-btn :block="MobileSize" style="border-radius: 4px;" color="#27AB9C" outlined @click="GotoETaxCredential()">e-Tax Credential</v-btn>
          </v-col>
        </v-row> -->
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
        <v-row
          justify="center"
          align-content="center"
          v-if="disableTable === false"
        >
          <v-col cols="12" align="center">
            <div class="my-5">
              <v-img
                src="@/assets/emptypo.png"
                max-height="500px"
                max-width="500px"
                height="100%"
                width="100%"
                contain
                aspect-ratio="2"
              ></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c">
              <!-- <b>คุณยังไม่มีรายสั่งซื้อสินค้าที่{{
                  StateStatus === 0
                    ? 'ยังไม่ดำเนินการ'
                    : StateStatus === 1
                    ? 'ดำเนินการแล้ว'
                    : 'ยกเลิก'
                }}</b
              > -->
              <b>คุณยังไม่มีรายสั่งซื้อสินค้า</b>
            </h2>
          </v-col>
        </v-row>
        <!-- </v-card> -->
        <!-- <v-row no-gutters justify="start" v-if="disableTable === true">
          <v-col cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-3 pt-6' : 'pl-2 pr-2 mb-3'">
            <v-text-field color="#27AB9C" v-model="search" dense hide-details outlined placeholder="ค้นหาข้อมูล" append-icon="mdi-magnify"></v-text-field>
          </v-col> -->
        <!-- <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-2 pt-0' : 'pl-2 pr-2 mb-3'">
            <v-row dense>
              <v-col cols="6">
                <span style="font-size: 16px; line-height: 24px; color: #333333;">Pay Type</span>
                <v-select v-model="PayTypeSelect" :items="payTypeItem" item-text="text" item-value="value" outlined dense></v-select>
              </v-col>
              <v-col cols="6">
                <span style="font-size: 16px; line-height: 24px; color: #333333;">ใบกำกับภาษี</span>
                <v-select v-model="InvoiceSelect" :items="invoiceItem" item-text="text" item-value="value" outlined dense></v-select>
              </v-col>
            </v-row>
          </v-col> -->
        <!-- <v-col v-if="disableTable === true" cols="12" md="12" sm="12"  :class="!MobileSize ? 'pl-3 pr-3 mb-3 pt-6' : 'pl-2 pr-2 mb-3'">
            <v-row dense>
              <v-col cols="12" md="4" sm="12">
                <span style="font-size: 16px; line-height: 24px; color: #333333;">Status</span>
                <v-select v-model="selected" :items="item_selected" menu-props="offset-y" placeholder="ทั้งหมด" outlined dense @change="getOrder()"></v-select>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <span style="font-size: 16px; line-height: 24px; color: #333333;">Start Date</span>
                <v-dialog
                  ref="modalStartDate"
                  v-model="modalStartDate"
                  :return-value.sync="startdate"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field readonly v-model="contractStartDate" v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="startdate"
                    scrollable
                    reactive
                    locale="Th-th"
                    @change="setValueStartDate(startdate)"
                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  >
                    <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="closeModaltStartDate()"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="$refs.modalStartDate.save(startdate)"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <span style="font-size: 16px; line-height: 24px; color: #333333;">End Date</span>
                <v-dialog
                  ref="modalEndDate"
                  v-model="modalEndDate"
                  :return-value.sync="enddate"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field :disabled="searchContractStartDate !== '' ? false : true" readonly v-model="contractEndDate" v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="enddate"
                    scrollable
                    reactive
                    locale="Th-th"
                    @change="setValueContractEndDate(enddate)"
                    :min="searchContractStartDate"
                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  >
                    <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="closeModalEndDate()"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="$refs.modalEndDate.save(enddate), getOrder()"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
          </v-col>
        </v-row> -->
      </v-card-text>
    </v-card>
    <v-dialog v-model="ModalFilter" width="100%" persistent>
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 20px;">
        <v-card-text>
          <v-toolbar flat color="rgba(0, 0, 0, 0)">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title><span style="color: #333333; font-size: 16px;"><b>ตัวกรอง</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="ModalFilter = !ModalFilter" icon><v-icon color="#CCCCCC">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-row dense>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
                >สถานะรายการ :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="statusSelect"
                :items="statusItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                class="setCustomSelect"
                dense
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >Pay Type : </span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="PayTypeSelect"
                :items="payTypeItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                style="border-radius: 8px;"
                class="setCustomSelect"
                :class="MobileSize ? '' : 'pr-4'"
                outlined
                hide-details
                dense
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่สั่งซื้อ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogBuyDate"
                v-model="modalBuyDate"
                :return-value.sync="date"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="buyDate"
                    v-bind="attrs"
                    v-on="on"
                    hide-details
                    outlined
                    style="border-radius: 8px;"
                    dense
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="date"
                  color = "#27AB9C"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueBuyDate(date)"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="closeModalBuyDate($refs)">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="
                      $refs.dialogBuyDate.save(date);
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่อนุมัติ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogAcceptDate"
                v-model="modalAcceptDate"
                :return-value.sync="date1"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="acceptDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    hide-details
                    style="border-radius: 8px;"
                    dense
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="date1"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueAcceptDate(date1)"
                  color = "#27AB9C"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary"
                  @click="
                    closeModalAcceptDate($refs)">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="
                      $refs.dialogAcceptDate.save(date1)
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >วันที่รอบบริการ :</span
              >
            </v-col>
            <v-col cols="12" >
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangeDate1"
                    v-bind="attrs"
                    v-on="on"
                    style="border-radius: 8px;"
                    outlined
                    dense
                    hide-details
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  range
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="CloseModalRangeDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="setValueRangeDate(dateRange)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >WHT :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="statusSelect"
                :items="statusItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                dense
                class="setCustomSelect"
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >สถานะการชำระเงิน :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="selectPaymentStatus"
                :items="statusPaymentStatusItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                dense
                class="setCustomSelect"
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-row dense justify="center">
            <v-btn width="125" height="36" text color="#27AB9C" @click="reSetSearch()">ล้างค่า</v-btn>
            <v-btn width="125" height="36" rounded color="#27AB9C" class="white--text" @click="getOrder()">ยืนยัน</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
        <!-- Dialog e-WHT -->
        <v-dialog v-model="dialogeWHT" width="850px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
          <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
            <v-card-text class="px-0 pb-0">
              <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 850px'" class="backgroundHead" style="position: absolute; height: 120px;">
                <v-row style="height: 120px;">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายละเอียดข้อมูลรายการ</b></span>
                  </v-col>
                  <v-btn fab small @click="dialogeWHT = !dialogeWHT" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </div>
              <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
                <v-row :width="MobileSize ? '100%' : '850px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                  <v-col style="text-align: center;">
                  </v-col>
                </v-row>
              </div>
              <div class="backgroundContent" style="position: relative;">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 10px 20px 10px;' : 'padding: 40px 48px 20px 48px;'">
                  <v-card-text class="pa-0">
                    <v-row dense style="color: #333333; font-size: 15px;">
                      <v-col cols="12" md="6" sm="6">
                        <span>เลขที่เอกสารใบสำคัญจ่าย : {{ dataeWHT.pv_no }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันที่จ่ายเงิน : {{ new Date(pvDate).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' }) }}</span>
                      </v-col>
                      <!-- รายละเอียดการชำระเงิน -->
                      <v-col cols="12" md="12" sm="12">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-size: 18px;"><b><v-icon color="#27AB9C">mdi-circle-medium</v-icon> รายละเอียดการชำระเงิน</b></span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>สถานะการชำระเงิน : {{ dataeWHTPayment.payment_status }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>วันที่/เวลาการชำระเงินเข้าสู่ระบบ e-WHT: {{ dataeWHTPayment.payment_date !== null ? new Date(dataeWHTPayment.payment_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- สถานะการโอนเงินไปยังปลายทาง -->
                      <v-col cols="12" md="12" sm="12">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-size: 18px;"><b><v-icon color="#27AB9C">mdi-circle-medium</v-icon> รายละเอียดสถานะการโอนเงิน</b></span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>สถานะการโอนเงินไปยังปลายทาง : {{ dataeWHTTransfer.transfer_status }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>วันที่/เวลาโอนเงินไปยังปลายทาง: {{ dataeWHTTransfer.transfer_date !== null ? new Date(dataeWHTTransfer.transfer_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!--  สถานะการส่งข้อมูลภาษีหัก ณ ที่จ่าย -->
                      <v-col cols="12" md="12" sm="12">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-size: 18px;"><b><v-icon color="#27AB9C">mdi-circle-medium</v-icon> สถานะการส่งข้อมูลภาษีหัก ณ ที่จ่าย</b></span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>สถานะการส่งข้อมูลภาษีหัก ณ ที่จ่าย : {{ dataeWHTCheck.wht_data }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>วันที่/เวลาการส่งข้อมูลภาษีหัก ณ ที่จ่าย : {{ dataeWHTCheck.wht_data_date !== null ? new Date(dataeWHTCheck.wht_data_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>สถานะการโอนเงิน wht ไปยังกรมสรรพากร : {{ dataeWHTCheck.transfer_rd }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>วันที่/เวลาการโอนเงิน wht ไปยังกรมสรรพากร : {{ dataeWHTCheck.transfer_rd_date !== null ? new Date(dataeWHTCheck.transfer_rd_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>สถานะการกระทบยอด : {{ dataeWHTCheck.reconcile }}</span>
                          </v-col>
                          <v-col cols="12" md="6" sm="6">
                            <span>วันที่/เวลาการกระทบยอด : {{ dataeWHTCheck.reconcile_date !== null ? new Date(dataeWHTCheck.reconcile_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
          </v-card>
        </v-dialog>
  </v-container>
</template>

<script>
import axios from 'axios'
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      ExportReports: '',
      customerId: '',
      dataOfDueDate: [],
      dialogeWHT: false,
      dataeWHT: '',
      dataeWHTPayment: '',
      dataeWHTTransfer: '',
      dataeWHTCheck: '',
      selectPaymentStatus: '',
      modalRangeDate: false,
      RangeDate1: [],
      modalBuyDate: false,
      modalAcceptDate: false,
      modalContractStartDate: false,
      modalContractEndDate: false,
      modalStartDate: false,
      modalEndDate: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      // date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      startdate: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      enddate: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      dateRange: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      searchBuyDate: '',
      buyDate: '',
      searchAcceptDate: '',
      acceptDate: '',
      searchContractStartDate: '',
      contractStartDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      PayTypeSelect: '',
      InvoiceSelect: '',
      statusSelect: '',
      acceptSelect: '',
      statusImportantSelect: '',
      orderList: [],
      seller_shop_id: '',
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: false,
      name: 'ImageItem',
      payTypeItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'General', value: 'General' },
        { text: 'Onetime', value: 'Onetime' },
        { text: 'Recurring', value: 'Recurring' }
      ],
      invoiceItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ขอใบกำกับภาษี', value: 'yes' },
        { text: 'ไม่ขอใบกำกับภาษี', value: 'no' }
      ],
      statusItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'New Service', value: 'new_service' },
        { text: 'Change', value: 'change' },
        { text: 'Renew', value: 'renew' },
        { text: 'Change&Renew', value: 'change&renew' },
        { text: 'Terminate', value: 'terminate' }
      ],
      statusPaymentStatusItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ชำระเงินสำเร็จ', value: 'Success' },
        { text: 'ยังไม่ชำระเงิน', value: 'Not Paid' }
      ],
      statusImportant: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'เขียว', value: 'green' },
        { text: 'เหลือง', value: 'yellow' },
        { text: 'แดง', value: 'red' }
      ],
      actionsItem: [
        { text: 'รายละเอียด', value: 'detail' }
        // { text: 'Change', value: 'change' },
        // { text: 'Renew', value: 'renew' },
        // { text: 'Change&Renew', value: 'both' },
        // { text: 'Terminate', value: 'terminate' }
      ],
      AcceptItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'เขียว', value: 'green' },
        { text: 'เหลือง', value: 'yellow' },
        { text: 'แดง', value: 'red' }
      ],
      headersAll: [
        {
          text: 'วันที่ทำรายการ',
          value: 'order_created_date',
          align: 'start',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'order_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Pay Type',
          value: 'pay_type',
          sortable: false,
          aligpay_typen: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะสั่งซื้อ',
          value: 'payment_transaction_status',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะรายการ',
          value: 'order_type',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ชื่อฝ่ายขาย',
          value: 'sale_name',
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ชื่อลูกค้า',
          value: 'cus_name',
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่อนุมัติ',
          value: 'approved_at',
          align: 'start',
          filterable: false,
          width: '180px',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ใบเสร็จ',
          value: 'receipt_number',
          filterable: false,
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบเสนอราคา',
          value: 'pdf_qt_path',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบแจ้งหนี้',
          value: 'qt_order_invoice',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่ครบกำหนดชำระ',
          value: 'QT_order_due_date',
          filterable: false,
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        // {
        //   text: 'e-WHT',
        //   value: 'pv_no',
        //   filterable: false,
        //   sortable: false,
        //   width: '125px',
        //   align: 'start',
        //   class: 'backgroundTable fontTable--text fontSizeDetail'
        // },
        {
          text: 'วันที่รอบบริการ',
          value: 'contractDate',
          filterable: false,
          sortable: false,
          width: '200px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบกำกับภาษี',
          value: 'transaction_code_icon',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ PR',
          value: 'pr_document_id',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ PO',
          value: 'po_document_id',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ SO',
          value: 'so_document_id',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'cost sheet',
          value: 'pdf_cs_path',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะการจัดส่ง',
          value: 'transportation_status',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Tracking Number',
          value: 'order_mobilyst_no',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'detail',
          align: 'start',
          filterable: false,
          sortable: false,
          class: 'backgroundTable fontTable--text'
        }
      ],
      seller_sent_status: '',
      send_items: [
        { text: 'จัดส่งแล้ว', value: 'sent' },
        { text: 'ยังไม่จัดส่ง', value: 'not_sent' },
        { text: 'ยกเลิก', value: 'cancel' }
      ],
      send_items_notPaid: [
        { text: 'ยังไม่จัดส่ง', value: 'not_sent' },
        { text: 'ยกเลิก', value: 'cancel' }
      ],
      status_items: [
        { text: 'ดำเนินการแล้ว', value: 'not_sent' },
        { text: 'ยังไม่ดำเนินการ', value: 'cancel' }
      ],
      DataTable: [],
      DataTableLasted: [],
      UrlExponential: '',
      customClick: record => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: '',
      page: 1,
      countPendingList: 0,
      countSuccessList: 0,
      countCencelList: 0,
      countNotPendingList: 0,
      OrderNameList: [
        { key: 0, name: 'รายการสั่งซื้อที่ยังไม่ดำเนินการ' },
        { key: 1, name: 'รายการสั่งซื้อที่ดำเนินการแล้ว' },
        { key: 2, name: 'รายการสั่งซื้อที่ยกเลิก' }
      ],
      selected: '',
      item_selected: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'Complete', value: 'complete' },
        { text: 'Change', value: 'change' },
        { text: 'Renew', value: 'renew' },
        { text: 'Terminate', value: 'terminate' }
      ],
      roleUser: '',
      // data: '',
      date1: '',
      date2: '',
      date3: '',
      startDateToSend: '',
      endDateToSend: '',
      ModalFilter: false,
      pvDate: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }

    // GetSellerShop () {
    //   var orderList = []
    //   orderList = this.$store.state.ModuleOrder.stateOrderListSeller.data
    //   return orderList
    // }
  },
  watch: {
    dateRange (val) {
      // console.log('dateRange', val)
      this.startDateToSend = val[0] !== undefined ? val[0] : ''
      this.endDateToSend = val[1] !== undefined ? val[1] : ''
      this.contractStartDate = val[0] !== undefined ? this.formatDateToShow(val[0]) : ''
      this.contractEndDate = val[1] !== undefined ? this.formatDateToShow(val[1]) : ''
      if (this.contractStartDate !== '' && this.contractEndDate !== '') {
        this.RangeDate1 = this.contractStartDate + ' - ' + this.contractEndDate
      } else {
        this.RangeDate1 = ''
      }
    },
    overlay (val) {
      val &&
        setTimeout(() => {
          this.overlay = false
        }, 500)
    }
    // MobileSize (val) {
    //   if (val === true) {
    //     this.$router.push({ path:  `/ListOrderCustomerMobile?Id=${item.cus_id}` }).catch(() => {})
    //   } else {
    //     this.$router.push({ path:  `/ListOrderCustomer?Id=${item.cus_id}` })
    //   }
    // }
  },
  async created () {
    this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    this.$EventBus.$emit('changeNav')
    // window.addEventListener('storage', function (event) {
    //   if (event.key === 'oneData' && !event.newValue) {
    //     window.location.assign('/')
    //   }
    // })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    }
    this.getOrder()
    // this.getDueDate()
    // await this.$store.dispatch('actionListOrderSeller', shopData)
    // this.orderList = await this.$store.state.ModuleOrder.stateOrderListSeller.data
    // this.ProcurementData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('ProcurementData'))))
    // this.getDataTable()
    // if (this.StateStatus === 0) {
    //   this.DataTable = this.orderList.data_incomplete
    // } else if (this.StateStatus === 1) {
    //   this.DataTable = this.orderList.not_paid
    // } else if (this.StateStatus === 2) {
    //   this.DataTable = this.orderList.success
    // } else if (this.StateStatus === 3) {
    //   this.DataTable = this.orderList.cancel
    // } else if (this.StateStatus === 4) {
    //   this.DataTable = this.orderList.fail
    // }
    localStorage.removeItem('sale_order')
  },
  methods: {
    backtoMenu () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/listCustomerSaleOrder' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listCustomerSaleOrderMobile' }).catch(() => {})
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    goToDetailDueDate (val, item, sale) {
      if (this.MobileSize) {
        this.$router.push({ path: `/DetailDueDateCustomerMobile?Id=${val}&CusId=${item}&SaleId=${sale}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/DetailDueDateCustomer?Id=${val}&CusId=${item}&SaleId=${sale}` }).catch(() => {})
      }
    },
    changeFormat (val) {
      var pvDate = val
      let dateToReturn = ''

      var year = pvDate.substring(0, 4)
      var month = pvDate.substring(4, 6)
      var day = pvDate.substring(6, 8)

      dateToReturn = year + '-' + month + '-' + day

      return dateToReturn
    },
    async openModaleWHT (pvNo) {
      this.$store.commit('openLoader')
      this.dataeWHT = ''
      this.pvDate = ''
      this.dataeWHTPayment = ''
      this.dataeWHTTransfer = ''
      this.dataeWHTCheck = ''
      const data = {
        pv_no: pvNo
      }
      await this.$store.dispatch('actionsStatusPvNo', data)
      const response = await this.$store.state.ModuleAdminManage.stateStatusPvNo
      if (response.message === 'Get Inquiry Status Success') {
        this.dataeWHT = await response.data
        this.pvDate = await this.changeFormat(this.dataeWHT.pv_date)
        this.dataeWHTPayment = await response.data.payment
        this.dataeWHTTransfer = await response.data.transfer
        this.dataeWHTCheck = await response.data.ewht
        this.$store.commit('closeLoader')
        this.dialogeWHT = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    async linkToEfact () {
      this.$store.commit('openLoader')
      var TaxIDShop = ''
      var shopID = localStorage.getItem('shopSellerID').toString()
      var data = {
        seller_id: shopID
      }
      await this.$store.dispatch('actionsCheckTaxIDShop', data)
      var responseData = await this.$store.state.ModuleETax.stateCheckTaxIDShop
      if (responseData.result === 'SUCCESS') {
        if (responseData.data !== '-') {
          TaxIDShop = await responseData.data
          // console.log(TaxIDShop)
          await this.$store.dispatch('actionsLoginBySharedTokenEfac', TaxIDShop)
          var response = await this.$store.state.ModuleETax.stateLoginBySharedTokenEfac
          // console.log('response', response)
          if (response.message === 'Get Link Redirect Success') {
            this.$store.commit('closeLoader')
            window.open(response.data.link, '_blank')
          } else {
            this.$store.commit('closeLoader')
          }
        } else {
          this.$store.commit('closeLoader')
          TaxIDShop = '-'
        }
      } else {
        this.$store.commit('closeLoader')
      }
    },
    OpenModalFilter () {
      this.ModalFilter = true
    },
    copyClipboard () {
      const track = document.getElementById('trackingNumberList')
      // Select the text field
      track.select()
      track.setSelectionRange(0, 99999) // For mobile devices

      // Copy the text inside the text field
      navigator.clipboard.writeText(track.value)
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        icon: 'success',
        html: '<span style="padding-left: 10px;">คัดลอกสำเร็จ</span>'
      })
    },
    async GetETaxPDF (val) {
      // console.log('valGetETaxPDF', val)
      var data = {
        transactionCode: val.transaction_code
      }
      const timeoutId = setTimeout(async () => {
        await this.$store.dispatch('ActionsGetETaxPDF', data)
        var response = await this.$store.state.ModuleCart.stateGetETaxPDF
        // console.log('response', response)
        if (response.result === 'OK') {
          if (response.etaxResponse.status === 'OK') {
            if (response.etaxResponse.urlPdf !== undefined) {
              window.open(`${response.etaxResponse.urlPdf}`, '_blank')
              // console.log('response', response.etaxResponse.urlPdf)
            } else {
              window.open(`${response.etaxResponse.pdfURL}`, '_blank')
            }
          }
        } else {
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ไม่พบเอกสารใบกำกับภาษี'
          })
        }
      }, 1000)
      // console.log('4', timeoutId)
      if (timeoutId > 1000) {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่พบเอกสารใบกำกับภาษี'
        })
      }
      // await this.$store.dispatch('ActionsGetETaxPDF', data)
      // var response = await this.$store.state.ModuleCart.stateGetETaxPDF
      // if (response.result === 'OK') {
      //   if (response.etaxResponse.status === 'OK') {
      //     window.open(`${response.etaxResponse.pdfURL}`)
      //   }
      // }
    },
    async setValueRangeDate (val) {
      this.$refs.modalRangeDate.save(val)
      var Range = await val.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
      this.dateRange = Range
      if (!this.MobileSize) {
        await this.getOrder()
      }
    },
    SortDate (RangeDate) {
      return RangeDate.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
    },
    async CloseModalRangeDate () {
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.startDateToSend = ''
      this.endDateToSend = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.dateRange = []
      this.RangeDate1 = []
      if (!this.MobileSize) {
        await this.getOrder()
      }
    },
    gotoActions (item, select) {
      if (select === 'detail') {
        var data = {
          order_number: item.order_number,
          payment_transaction_number: item.transaction_number
        }
        localStorage.setItem('orderNumberSeller', Encode.encode(data))
        var OrderNumber = item.order_number
        var transactionNumber = item.transaction_number
        // console.log(OrderNumber, transactionNumber)
        if (this.MobileSize) {
          this.$router.push({ path: `/DetailOrderCustomerMobile?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/DetailOrderCustomer?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}` }).catch(() => {})
        }
      } else if (select === 'change') {
      } else if (select === 'renew') {
      } else if (select === 'both') {
      } else if (select === 'terminate') {
      }
    },
    setValueBuyDate (val) {
      this.searchBuyDate = val
      // console.log(this.searchDateNotFormat)
      this.buyDate = this.formatDateToShow(val)
    },
    setValueAcceptDate (val) {
      this.searchAcceptDate = val
      // console.log(this.searchDateNotFormat)
      this.acceptDate = this.formatDateToShow(val)
    },
    setValueStartDate (val) {
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      this.contractEndDate = ''
    },
    setValueContractEndDate (val) {
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
    },
    closeModalBuyDate ($refs) {
      this.modalBuyDate = false
      $refs.dialogBuyDate.save('')
      this.date = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchBuyDate = ''
      this.buyDate = ''
      this.date = ''
    },
    closeModalAcceptDate ($refs) {
      this.modalAcceptDate = false
      $refs.dialogAcceptDate.save('')
      this.date1 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchAcceptDate = ''
      this.acceptDate = ''
      this.date1 = ''
    },
    closeModalContractStartDate ($refs) {
      this.modalContractStartDate = false
      $refs.dialogContractStartDate.save('')
      this.date2 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.date2 = ''
      this.contractEndDate = ''
    },
    closeModalContractEndDate ($refs) {
      this.modalContractEndDate = false
      $refs.dialogContractEndDate.save('')
      this.date3 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchContractEndDate = ''
      this.contractEndDate = ''
    },
    closeModaltStartDate () {
      this.modalStartDate = false
      // $refs.dialogAcceptDate.save('')
      this.startdate = new Date(
        Date.now() - new Date().getTimezoneOffset() * 60000
      )
        .toISOString()
        .substr(0, 10)
      this.searchContractStartDate = ''
      // this.contractStartDate = ''
    },
    closeModalEndDate () {
      this.modalEndDate = false
      // $refs.dialogAcceptDate.save('')
      this.enddate = new Date(
        Date.now() - new Date().getTimezoneOffset() * 60000
      )
        .toISOString()
        .substr(0, 10)
      this.searchContractEndDate = ''
      // this.contractEndDate = ''
    },
    linkToETax () {
      window.open('https://devinet-etax.one.th/portal/login', '_blank')
    },
    GotoETaxCredential () {
      if (this.MobileSize) {
        this.$router.push({ path: '/EtaxCredentailMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/EtaxCredentail' }).catch(() => {})
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router
          .push({
            path:
              '/sellerMobile?ShopID=' +
              shopDetail.id +
              '&ShopName=' +
              shopDetail.name
          })
          .catch(() => {})
      } else {
        this.$router.push({
          path:
            '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name
        })
      }
    },
    // async UpdateStatusSeller (val) {
    //   const update = {
    //     order_number: val.order_number,
    //     seller_sent_status: val.seller_sent_status
    //   }
    //   await this.$store.dispatch('actionUpdateStatusSeller', update)
    //   this.$swal.fire({
    //     icon: 'success',
    //     title: 'บันทึกการส่งสินค้าสำเร็จ',
    //     showConfirmButton: false,
    //     timer: 1500
    //   })
    //   this.GetSellerShop()
    // },
    async orderDetail (val) {
      var data = {
        order_number: val.order_number,
        payment_transaction_number: val.transaction_number
      }
      localStorage.setItem('orderNumberSeller', Encode.encode(data))
      var OrderNumber = val.order_number
      var transactionNumber = val.transaction_number
      if (this.MobileSize) {
        this.$router
          .push({
            path: `/DetailOrderSalesMobile?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}`
          })
          .catch(() => {})
      } else {
        this.$router
          .push({
            path: `/DetailOrderSales?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}`
          })
          .catch(() => {})
      }
      // await this.$store.dispatch('actionOrderDetailSeller', data)
      // await this.$store.state.ModuleOrder.stateOrderDetailSeller.data
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      // }
    },
    async orderTransactionnumber (val) {
      window.open(`${val.pdf_for_seller}`)
    },
    async GetInvoice (val) {
      window.open(`${val.qt_order_invoice}`)
    },
    // async GetSellerShop () {
    //   this.DataTable = []
    //   this.countPendingList = 0
    //   this.countSuccessList = 0
    //   this.countCencelList = 0
    //   this.countNotPendingList = 0
    //   const shopId = localStorage.getItem('shopSellerID')
    //   var data = {
    //     seller_shop_id: shopId
    //   }
    //   await this.$store.dispatch('actionListOrderSeller', data)
    //   var responseData = await this.$store.state.ModuleOrder.stateOrderListSeller
    //   if (responseData.code !== 401) {
    //     this.orderList = responseData.data
    //     this.countPendingList = this.orderList.pending.length
    //     this.countSuccessList = this.orderList.success.length
    //     this.countCencelList = this.orderList.cancel.length
    //     this.countNotPendingList = this.orderList.not_paid.length
    //     if (this.StateStatus === 0) {
    //       this.DataTable = this.orderList.pending
    //     } else if (this.StateStatus === 1) {
    //       this.DataTable = this.orderList.success
    //     } else if (this.StateStatus === 2) {
    //       this.DataTable = this.orderList.not_paid
    //     } else if (this.StateStatus === 3) {
    //       this.DataTable = this.orderList.cancel
    //     }
    //     if (this.orderList.length === 0) {
    //       this.disableTable = false
    //     } else {
    //       this.disableTable = true
    //     }
    //   } else {
    //     this.$store.commit('closeLoader')
    //     localStorage.removeItem('roleUser')
    //     localStorage.removeItem('roleUserApprove')
    //     localStorage.removeItem('oneData')
    //     localStorage.removeItem('orderNumber')
    //     localStorage.removeItem('orderNumberSeller')
    //     this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
    //     window.location.assign('/')
    //   }
    // },
    // SelectDetailOrder (item) {
    //   this.page = 1
    //   this.StateStatus = item
    //   if (this.StateStatus === 0) {
    //     this.DataTable = this.orderList.pending
    //   } else if (this.StateStatus === 1) {
    //     this.DataTable = this.orderList.success
    //   } else if (this.StateStatus === 2) {
    //     this.DataTable = this.orderList.not_paid
    //   } else if (this.StateStatus === 3) {
    //     this.DataTable = this.orderList.cancel
    //   }
    //   if (this.DataTable.length === 0) {
    //     this.disableTable = false
    //   } else {
    //     this.disableTable = true
    //   }
    // },
    // async getDataTable () {
    //   this.overlay = true
    //   const data = {
    //     procurement_org_id: this.ProcurementData.procurement_org_id
    //   }
    //   await this.$store.dispatch('actionListOrderProcurement', data)
    //   var response = await this.$store.state.ModuleCart.stateListOrderProcurement
    //   if (response.result === 'SUCCESS') {
    //     this.responseData = response.data
    //     this.overlay = false
    //   }
    // },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateDetailOrder
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem(
          'MyOrderDetail',
          Encode.encode(JSON.stringify(response.data))
        )
        this.$router.push('/myorderdetail')
      }
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    getColor (item) {
      if (item === 'การจัดส่งสำเร็จ') return '#F0F9EE'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#DBECFA'
      else if (item === 'ส่งคืนสินค้า') return '#F7D9D9'
      else return '#FCF0DA'
    },
    getTextColor (item) {
      if (item === 'การจัดส่งสำเร็จ') return '#1AB759'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#2A70C3'
      else if (item === 'ส่งคืนสินค้า') return '#D1392B'
      else return '#E9A016'
    },
    getTextColorStatus (item) {
      if (item === 'Success') return '#52C41A'
      else if (item === 'Fail') return '#D1392B'
      else return '#FAAD14'
    },
    getTextStatus (item) {
      if (item === 'Success') return 'ชำระเงินแล้ว'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else return 'รอชำระเงิน'
    },
    getStatus (item) {
      if (item === 'Pending') return 'รออนุมัติ'
      else if (item === 'Not Paid') return 'ยังไม่ชำระเงิน'
      else if (item === 'การจัดส่งสำเร็จ') return 'ชำระเงินสำเร็จ'
      else if (item === 'Approve') return 'วางบิล'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Credit Term') return 'ชำระเงินแบบเครดิตเทอม'
      else return 'ยกเลิกคำสั่งซื้อ'
    },
    reSetSearch () {
      this.searchContractStartDate = ''
      this.date = ''
      this.date1 = ''
      this.date2 = ''
      this.date3 = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.PayTypeSelect = ''
      this.InvoiceSelect = ''
      this.buyDate = ''
      this.acceptDate = ''
      this.statusImportantSelect = ''
      this.statusSelect = ''
      this.search = ''
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.selectPaymentStatus = ''
      this.dateRange = []
      this.RangeDate1 = []
      this.getOrder()
    },
    async downloadExcel () {
      this.$store.commit('openLoader')
      const authHeaders = {
        Authorization: `Bearer ${this.oneData.user.access_token}`
      }
      try {
        const response = await axios({
          url: `${this.ExportReports}`,
          method: 'GET',
          responseType: 'blob',
          headers: authHeaders
        })
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'result.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      } catch (error) {
        // console.error(error)
        this.$store.commit('closeLoader')
      }
    },
    // async getFilterDate (startDate, endDate) {
    //   await this.getOrder(startDate, endDate)
    // },
    async getOrder () {
      this.$store.commit('openLoader')
      if (localStorage.getItem('list_shop_detail') !== null) {
        var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      }
      const data = {
        sale_id: dataDetail.can_use_function_in_shop.manage_sale_order === '1' ? '' : dataDetail.sale_id,
        cus_id: this.$route.query.Id,
        seller_shop_id: this.seller_shop_id,
        search_keyword: '',
        pay_type: this.PayTypeSelect,
        required_invoice: this.InvoiceSelect,
        create_date: this.buyDate,
        approved_at: this.acceptDate,
        start_date_contract: this.contractStartDate,
        end_date_contract: this.contractEndDate,
        order_status: this.statusSelect,
        condition_send_cs: this.statusImportantSelect,
        payment_transaction_status: this.selectPaymentStatus
      }
      await this.$store.dispatch('actionsListOrderCustomer', data)
      var res = await this.$store.state.ModuleSaleOrder.stateListOrderCustomer
      // console.log('------>', res)
      if (res.code === 200) {
        // console.log(1)
        this.DataTable = res.data.order_data
        this.customerId = res.data.order_data.length !== 0 ? res.data.order_data[0].customer_id : ''
        this.ExportReports = res.data.order_reports
        // console.log('this.DataTable', this.DataTable)
        this.UrlExponential = res.data.export_reports
        if (this.DataTable.length > 0) {
          this.disableTable = true
        }
        this.ModalFilter = false
        await this.getDueDate()
      } else {
        // console.log(2)
        this.$store.commit('closeLoader')
        if (res.code === 401) {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.ModalFilter = false
          this.$swal.fire({
            icon: 'error',
            text: `${res.message}`,
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    async getDueDate () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsGetDetailDueDateSaleOrder', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetDetailDueDateSaleOrder
      this.dataOfDueDate = await res.data
      // console.log('getDueDate---->', this.dataOfDueDate)
      // console.log('DataTable---->', this.DataTable)
      this.DataTable.find((item) => {
        var data = this.dataOfDueDate.find((data) => (data.order_number === item.order_number) && data.invoice_due_date !== '-')
        if (data) {
          item.invoice_due_date = data.invoice_due_date
          item.transaction_status = data.transaction_status
          item.transaction_status_payment = data.transaction_status_payment
        } else {
          item.invoice_due_date = '-'
          // item.transaction_status = data.transaction_status
          // item.transaction_status_payment = data.transaction_status_payment
        }
      })
      this.DataTableLasted = this.DataTable
      this.$store.commit('closeLoader')
      // console.log('DataTableLasted---->', this.DataTableLasted)
    },
    costSheetShow (item) {
      var costSheet = item.pdf_cs_path
      window.open(costSheet)
    },
    QuotationShow (item) {
      // console.log(item)
      window.open(item.pdf_qt_path)
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #333333 !important;
}
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
