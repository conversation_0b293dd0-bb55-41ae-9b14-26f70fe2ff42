<template>
  <v-card width="100%" height="100%" elevation="0" :class="MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4'">
    <v-col class="pt-6">
      <span v-if="MobileSize" class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">
        <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ปรับแต่งส่วนของประเภทร้านค้า</span>
      <span v-else class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">ปรับแต่งส่วนของประเภทร้านค้า</span>
    </v-col>
    <v-col class="pt-3" style="max-width: 200px; margin-top: -10px;">
      <v-switch
      v-model="switchActive"
      :label="switchActive ? 'เปิดการใช้งาน' : 'ปิดการใช้งาน'"
      @change="handleActive"
      hide-details
    ></v-switch>
    </v-col>
    <v-col :class="MobileSize ? 'mb-3' : 'pt-1'">
      <v-radio-group v-model="switchType" row>
        <span style="font-size: 14px; align-items: center; display: flex; font-weight: 600;" class="mr-2">ใช้รูปแบบ : </span>
        <v-radio style="font-size: 14px;" label="Default" value="default" @click="handleType(switchType)"></v-radio>
        <v-radio style="font-size: 14px;" label="Custom" value="custom" @click="handleType(switchType)"></v-radio>
        <v-radio style="font-size: 14px;" label="Banner" value="banner" @click="handleType(switchType)"></v-radio>
      </v-radio-group>
    </v-col>
    <v-container :class="MobileSize ? 'pa-0' : 'pt-4 pb-2'">
      <v-row dense class="pl-2">
        <v-tabs
          v-model="tab"
          background-color="transparent"
          @change="changeTab(tab)"
          grow
        >
          <v-tab
          v-for="item in itemTab"
          :key="item"
          >
          <span style="font-size: medium; font-weight: 600;">{{ item }}</span>
          </v-tab>
        </v-tabs>
      </v-row>
    <v-row class="pt-2 pb-2">
        <v-card-title v-if="tab === 0" style="font-weight: 600;">ปรับแต่ง Banner</v-card-title>
        <v-col cols="12" v-if="tab === 0">
            <v-row>
              <!-- ข้อความ banner -->
              <v-col cols="12" class="pl-4 my-3 d-flex flex-column align-center justify-content-center">
                    <v-text-field class="mb-3" style="width: 80%;" dense v-model="textBanner" counter maxlength="50" outlined label="แก้ไขข้อความ Banner"></v-text-field>
                    <v-color-picker
                        hide-canvas
                        hide-inputs
                        hide-mode-switch
                        hide-sliders
                        show-swatches
                        v-model="textColorBanner"
                        swatches-max-height="120"
                        width="80%"
                        class="mx-auto"
                    ></v-color-picker>
              </v-col>
              <!-- รูป banner -->
                <v-col cols="12">
                  <v-row dense align="center" justify="center">
                    <v-col cols="12">
                      <v-card-text style="font-weight: 500; font-size: medium; line-height: 1;">รูปภาพ Banner</v-card-text>
                    </v-col>
                    <v-card class="mt-3 pa-1" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 80%; height: 300px; display: flex; flex-direction: column; align-items: center;">
                      <v-img v-if="backgroundImageBanner !== ''" :src="backgroundImageBanner" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
                      <v-card-text>
                          <v-col cols="12">
                            <v-row>
                              <v-col cols="12" align="center" v-if="backgroundImageBanner === ''">
                                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                              </v-col>
                              <v-col cols="12" style="text-align: center;">
                                <span style="line-height: 16px; font-weight: 400; font-size: smaller;">
                                    (ไฟล์นามสกุล .jpg, .jpeg, .png)
                                </span><br />
                                <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                                  เพิ่มรูปภาพ
                                  <input
                                    ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeBanner($event)"
                                    style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                                  />
                                </v-btn>
                                <v-btn v-if="backgroundImageBanner !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageBanner()"><v-icon small>mdi-delete</v-icon></v-btn>
                              </v-col>
                            </v-row>
                          </v-col>
                      </v-card-text>
                    </v-card>
                  </v-row>
                </v-col>
              <!-- รูป logo banner -->
                <v-col cols="12">
                  <v-row dense align="center" justify="center">
                    <v-col cols="12">
                      <v-card-text style="font-weight: 500; font-size: medium; line-height: 1;">รูปภาพ Logo</v-card-text>
                    </v-col>
                    <v-card class="mt-3 pa-1" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 80%; height: 300px; display: flex; flex-direction: column; align-items: center;">
                      <v-card-text style="display: flex; justify-content: center;">
                        <v-img contain :max-width="MobileSize ? '200' : '260'" :max-height="MobileSize ? '120' : '160'" v-if="logoImage !== ''" :src="logoImage" style="width: 100%; margin-top: 10px;"></v-img>
                      </v-card-text>
                      <v-card-text>
                          <v-col cols="12">
                            <v-row>
                              <v-col cols="12" align="center" v-if="logoImage === ''">
                                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                              </v-col>
                              <v-col cols="12" style="text-align: center;">
                                <span style="line-height: 16px; font-weight: 400; font-size: smaller;">
                                  (ไฟล์นามสกุล .jpg, .jpeg, .png)
                                </span><br />
                                <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                                  เพิ่มรูปภาพ
                                  <input
                                    ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLogo($event)"
                                    style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                                  />
                                </v-btn>
                                <v-btn v-if="logoImage !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLogo()"><v-icon small>mdi-delete</v-icon></v-btn>
                              </v-col>
                            </v-row>
                          </v-col>
                      </v-card-text>
                    </v-card>
                  </v-row>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? '12' : '4'" :class="MobileSize ? 'pl-6 my-2 d-flex flex-column' : 'pl-4 my-6 d-flex flex-column'">
                  <span class="mb-2" style="font-weight: 500; font-size: small; line-height: 1;">ปรับสีข้อความปุ่มทั้งหมด</span>
                  <v-color-picker
                        hide-canvas
                        hide-inputs
                        hide-mode-switch
                        hide-sliders
                        show-swatches
                        v-model="textColorAllBtn"
                        swatches-max-height="120"
                        width="98%"
                  ></v-color-picker>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? '12' : '4'" :class="MobileSize ? 'pl-6 my-2 d-flex flex-column' : 'pl-4 my-6 d-flex flex-column'">
                  <span class="mb-2" style="font-weight: 500; font-size: small; line-height: 1;">ปรับสีพื้นหลังปุ่มทั้งหมด</span>
                    <v-color-picker
                        hide-canvas
                        hide-inputs
                        hide-mode-switch
                        hide-sliders
                        show-swatches
                        v-model="bgColorAllBtn"
                        swatches-max-height="120"
                        width="98%"
                    ></v-color-picker>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? '12' : '4'" :class="MobileSize ? 'pl-6 my-2 d-flex flex-column' : 'pl-4 my-6 d-flex flex-column'">
                  <span class="mb-2" style="font-weight: 500; font-size: small; line-height: 1;">ปรับสีปุ่มเลื่อนซ้าย-ขวา ใน Layout</span>
                    <v-color-picker
                        hide-canvas
                        hide-inputs
                        hide-mode-switch
                        hide-sliders
                        show-swatches
                        v-model="colorArrowBtn"
                        swatches-max-height="120"
                        width="98%"
                    ></v-color-picker>
                </v-col>
            </v-row>
            <v-card-title :class="MobileSize ? 'mt-2' : ''" style="font-weight: 600;">ปรับแต่ง Layout</v-card-title>
            <v-row>
              <v-col :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6' : 'my-2'">
                <v-select v-model="selectLayout" :items="itemSelectLayout" item-text="name" item-value="value" label="จำนวน Layout" @change="handleSelectLayout" hide-details dense outlined></v-select>
              </v-col>
              <v-col :cols="MobileSize ? '12' : '6'" v-if="disabledSelectLayout" :class="MobileSize ? 'px-6' : 'my-2'">
                <v-select v-model="selectSizeLayout" :items="itemSelectSizeLayout" item-text="name" item-value="value" label="ขนาด Layout" @change="handleSelectSizeLayout" hide-details dense outlined></v-select>
              </v-col>
              <v-col :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6 my-2 d-flex flex-column' : 'pl-4 my-2 d-flex flex-column'">
                    <v-text-field class="mb-3" dense v-model="textLayout1" counter maxlength="50" outlined label="แก้ไขข้อความ Layout ด้านซ้าย"></v-text-field>
                    <v-color-picker
                        hide-canvas
                        hide-inputs
                        hide-mode-switch
                        hide-sliders
                        show-swatches
                        v-model="textColorLayout1"
                        swatches-max-height="120"
                        width="98%"
                    ></v-color-picker>
                </v-col>
                <v-col v-if="disabledSelectLayout" :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6 my-2 d-flex flex-column' : 'pl-4 my-2 d-flex flex-column'">
                    <v-text-field class="mb-3" dense v-model="textLayout2" counter maxlength="50" outlined label="แก้ไขข้อความ Layout ด้านขวา"></v-text-field>
                    <v-color-picker
                        hide-canvas
                        hide-inputs
                        hide-mode-switch
                        hide-sliders
                        show-swatches
                        v-model="textColorLayout2"
                        swatches-max-height="120"
                        width="98%"
                    ></v-color-picker>
                </v-col>
              <v-col cols="12">
                <v-row dense align="center" justify="center">
                  <v-col cols="12">
                    <v-card-title style="font-weight: 500; font-size: medium; line-height: 1;">รูปภาพ Layout 1</v-card-title>
                  </v-col>
                  <v-card class="mt-3 pa-1" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 80%; height: 300px; display: flex; flex-direction: column; align-items: center;">
                    <v-img v-if="backgroundImageLayout1 !== ''" :src="backgroundImageLayout1" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
                    <v-card-text>
                        <v-col cols="12">
                          <v-row>
                            <v-col cols="12" align="center" v-if="backgroundImageLayout1 === ''">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                            </v-col>
                            <v-col cols="12" style="text-align: center;">
                              <span style="line-height: 16px; font-weight: 400; font-size: smaller;">
                                  (ไฟล์นามสกุล .jpg, .jpeg, .png)
                              </span><br />
                              <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                                เพิ่มรูปภาพ
                                <input
                                  ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayout1($event)"
                                  style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                                />
                              </v-btn>
                              <v-btn v-if="backgroundImageLayout1 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayout1()"><v-icon small>mdi-delete</v-icon></v-btn>
                            </v-col>
                          </v-row>
                        </v-col>
                    </v-card-text>
                  </v-card>
                </v-row>
              </v-col>
              <v-col cols="12" v-if="disabledSelectLayout">
                <v-row dense align="center" justify="center">
                  <v-col cols="12">
                    <v-card-title style="font-weight: 500; font-size: medium; line-height: 1;">รูปภาพ Layout 2</v-card-title>
                  </v-col>
                  <v-card class="mt-3 pa-1" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 80%; height: 300px; display: flex; flex-direction: column; align-items: center;">
                    <v-img v-if="backgroundImageLayout2 !== ''" :src="backgroundImageLayout2" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
                    <v-card-text>
                        <v-col cols="12">
                          <v-row>
                            <v-col cols="12" align="center" v-if="backgroundImageLayout2 === ''">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                            </v-col>
                            <v-col cols="12" style="text-align: center;">
                              <span style="line-height: 16px; font-weight: 400; font-size: smaller;">
                                  (ไฟล์นามสกุล .jpg, .jpeg, .png)
                              </span><br />
                              <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                                เพิ่มรูปภาพ
                                <input
                                  ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayout2($event)"
                                  style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                                />
                              </v-btn>
                              <v-btn v-if="backgroundImageLayout2 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayout2()"><v-icon small>mdi-delete</v-icon></v-btn>
                            </v-col>
                          </v-row>
                        </v-col>
                    </v-card-text>
                  </v-card>
                </v-row>
              </v-col>
              <v-col cols="12">
                <v-card-title style="font-weight: 600;">ปรับแต่งประเภทร้านค้า</v-card-title>
              </v-col>
              <v-col  :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6' : ''">
                <v-autocomplete
                  v-model="selectGroupShop1"
                  :items="itemGroupShopAll1"
                  @change="itemGroupShopLayout1(selectGroupShop1)"
                  color="primary"
                  label="เลือกประเภทร้านค้าใน layout 1"
                  item-title="shop_name"
                  item-value="shop_id"
                  style="border-radius: 8px;"
                  chips
                  closable-chips
                  multiple
                  outlined
                  return-object
                  hide-details
                >
                  <template v-slot:selection="data">
                    <v-chip
                      :key="data.index"
                      :input-value="data.selected"
                      @click="data.select"
                    >
                      <v-avatar left>
                        <img v-if="data.item.shop_logo" :src="data.item.shop_logo">
                        <img v-else src="@/assets/NoImage.png" alt="No Image">
                      </v-avatar>
                      {{ data.item.shop_name }}
                    </v-chip>
                  </template>
                  <template v-slot:item="data">
                    <template v-if="typeof data.item !== 'object'">
                      <v-list-item-content>{{ data.item }}</v-list-item-content>
                    </template>
                    <template v-else>
                      <v-list-item-avatar>
                        <img v-if="data.item.shop_logo" :src="data.item.shop_logo">
                        <img v-else src="@/assets/NoImage.png" alt="No Image">
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-title >{{ data.item.shop_name }}</v-list-item-title>
                      </v-list-item-content>
                    </template>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col  :cols="MobileSize ? '12' : '6'" :class="MobileSize ? 'px-6' : ''" v-if="disabledSelectLayout">
                <v-autocomplete
                  v-model="selectGroupShop2"
                  :items="itemGroupShopAll2"
                  @change="itemGroupShopLayout2(selectGroupShop2)"
                  color="primary"
                  label="เลือกประเภทร้านค้าใน layout 2"
                  item-title="shop_name"
                  item-value="shop_id"
                  style="border-radius: 8px;"
                  chips
                  closable-chips
                  multiple
                  outlined
                  return-object
                  hide-details
                >
                  <template v-slot:selection="data">
                    <v-chip
                      :key="data.index"
                      :input-value="data.selected"
                      @click="data.select"
                    >
                      <v-avatar left>
                        <img v-if="data.item.shop_logo" :src="data.item.shop_logo">
                        <img v-else src="@/assets/NoImage.png" alt="No Image">
                      </v-avatar>
                      {{ data.item.shop_name }}
                    </v-chip>
                  </template>
                  <template v-slot:item="data">
                    <template v-if="typeof data.item !== 'object'">
                      <v-list-item-content>{{ data.item }}</v-list-item-content>
                    </template>
                    <template v-else>
                      <v-list-item-avatar>
                        <img v-if="data.item.shop_logo" :src="data.item.shop_logo">
                        <img v-else src="@/assets/NoImage.png" alt="No Image">
                      </v-list-item-avatar>
                      <v-list-item-content>
                        <v-list-item-title >{{ data.item.shop_name }}</v-list-item-title>
                      </v-list-item-content>
                    </template>
                  </template>
                </v-autocomplete>
              </v-col>
            </v-row>
        </v-col>
    </v-row>
    <v-row :style="MobileSize ? 'margin-top: 10px;' : 'margin-top: 20px;'"  v-if="tab === 0">
      <v-col cols="12">
      <v-card-title style="font-weight: 600; margin-bottom: 10px;">ตัวอย่างที่ปรับแต่ง</v-card-title>
      </v-col>
        <v-col cols="12">
          <v-card :style="{ backgroundImage: `url(${backgroundImageBanner})`, backgroundPosition: 'center'}" style="border-radius: 8px; position: relative;">
              <div style="display: flex; justify-content: flex-end;" :style="MobileSize || IpadSize  ? 'gap: 0; margin-left: 20%; width: 80%;' : IpadProSize ? 'gap: 1vw; margin-left: 24%; width: 76%;' : 'gap: 2vw; margin-left: 20%; width: 80%;'">
                  <img v-if="logoImage !== ''" :src="logoImage" :height="MobileSize ? '36px' : IpadSize ? '38px' : IpadProSize ? '70px' : '100px'" :style="MobileSize ? 'position: absolute; top: -16px; left: 4px;' : IpadSize  ? 'position: absolute; top: -14px; left: 10px;' : IpadProSize ? 'position: absolute; top: -30px; left: 10px;' : 'position: absolute; top: -54px; left: 10px;'" alt="logo">
                  <v-card-text style="text-align: center; font-weight: 600;" :style="MobileSize ? `margin-top:-4px; margin-right:-14px; font-size: xx-small; color: ${textColorBanner};` : IpadSize  ? `margin-right:-10px; font-size: smaller; color: ${textColorBanner};` : IpadProSize ? `font-size: medium; color: ${textColorBanner};` : `font-size: x-large; color: ${textColorBanner};`">{{ textBanner }}</v-card-text>
                  <v-btn v-if="!MobileSize && ! IpadSize" style="font-weight: 600;" class="my-3 mx-2" :style="`background-color: ${bgColorAllBtn}; color: ${textColorAllBtn};`" small>ดูทั้งหมด</v-btn>
                  <v-btn v-else style="font-weight: 600;" class="my-3 mx-2" :style="`background-color: ${bgColorAllBtn}; color: ${textColorAllBtn};`" x-small>ดูทั้งหมด</v-btn>
              </div>
                  <!-- size ipadpro & pc -->
                  <div v-if="!MobileSize && !IpadSize" class="d-flex justify-center pb-5 displayIPADPro" style="gap: 5%; height: 100%; padding-top: 3vw;" :style="IpadProSize ? 'padding-top: 3vw;' : 'padding-top: 2vw;'">
                    <!-- Layout ด้านซ้าย -->
                    <div :style="{ width: widthLeft }">
                      <v-sheet class="mx-auto" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout1})`, backgroundPosition: 'center'}">
                        <v-card-text style="text-align: center; font-weight: 600;" :style="IpadProSize ? `font-size: medium; color: ${textColorLayout1};` : `font-size: large; color: ${textColorLayout1};`">{{ textLayout1 }}</v-card-text>
                        <v-slide-group v-model="model" class="pa-2" active-class="success" show-arrows>
                          <template #prev>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-left</v-icon>
                            </v-btn>
                          </template>
                          <template #next>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-right</v-icon>
                            </v-btn>
                          </template>
                          <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout1" :key="index">
                            <v-card class="ma-2" style="display: flex; flex-direction: column; justify-content: flex-end; align-items: center;" :style="IpadProSize ? 'width: 80px; height: 100px;' : 'width: 120px; height: 140px;'">
                              <div :style="IpadProSize ? 'height: 66%; width: 100%;' : 'height: 74%; width: 100%;'" class="d-flex justify-center pa-1">
                                <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                                <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                              </div>
                              <div :style="IpadProSize ? 'height: 34%; width: 100%;' : 'height: 26%; width: 100%;'" class="d-flex justify-center pa-1">
                                <v-tooltip top>
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-col align="center" v-bind="attrs" v-on="on">
                                      <span class="text-truncate d-inline-block" :style="IpadProSize ? 'font-size: smaller;' : 'font-size: smaller;'" style="font-weight: 600; width: 96%; line-height: 1; padding: -10px;">{{ itemGroupShop.shop_name }}</span>
                                    </v-col>
                                  </template>
                                  <span>{{ itemGroupShop.shop_name }}</span>
                                </v-tooltip>
                              </div>
                            </v-card>
                          </v-slide-item>
                        </v-slide-group>
                      </v-sheet>
                    </div>
                    <!-- Layout ด้านขวา -->
                    <div v-if="disabledSelectLayout" :style="{ width: widthRight }">
                      <v-sheet class="mx-auto" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout2})`, backgroundPosition: 'center'}">
                        <v-card-text style="text-align: center; font-weight: 600;" :style="IpadProSize ? `font-size: medium; color: ${textColorLayout2};` : `font-size: large; color: ${textColorLayout2};`">{{ textLayout2 }}</v-card-text>
                        <v-slide-group v-model="model" class="pa-2" active-class="success" show-arrows>
                          <template #prev>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-left</v-icon>
                            </v-btn>
                          </template>
                          <template #next>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-right</v-icon>
                            </v-btn>
                          </template>
                          <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout2" :key="index">
                            <v-card class="ma-2" style="display: flex; flex-direction: column; justify-content: flex-end; align-items: center;" :style="IpadProSize ? 'width: 80px; height: 100px;' : 'width: 120px; height: 140px;'">
                              <div :style="IpadProSize ? 'height: 66%; width: 100%;' : 'height: 74%; width: 100%;'" class="d-flex justify-center pa-1">
                                <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                                <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                              </div>
                              <div :style="IpadProSize ? 'height: 34%; width: 100%;' : 'height: 26%; width: 100%;'" class="d-flex justify-center pa-1">
                                <v-tooltip top>
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-col align="center" v-bind="attrs" v-on="on">
                                      <span class="text-truncate d-inline-block" :style="IpadProSize ? 'font-size: smaller;' : 'font-size: smaller;'" style="font-weight: 600; width: 96%; line-height: 1; padding: -10px;">{{ itemGroupShop.shop_name }}</span>
                                    </v-col>
                                  </template>
                                  <span>{{ itemGroupShop.shop_name }}</span>
                                </v-tooltip>
                              </div>
                            </v-card>
                          </v-slide-item>
                        </v-slide-group>
                      </v-sheet>
                    </div>
                  </div>
                  <!-- size Mobile & ipad -->
                  <div v-else class="d-flex justify-center flex-column align-center pb-3" style="height: 100%; padding-top: 1vw;">
                    <!-- Layout ด้านซ้าย -->
                    <div style="width: 90%;">
                      <v-sheet class="mx-auto mb-2" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout1})`, backgroundPosition: 'center'}">
                        <v-card-text style="text-align: center; font-weight: 600; margin: -12px 0;" :style="MobileSize ? `font-size: xx-small; color: ${textColorLayout1};` : `font-size: smaller; color: ${textColorLayout1};`">{{ textLayout1 }}</v-card-text>
                        <v-slide-group v-model="model" class="pa-2" active-class="success" style="margin-top: -10px;" show-arrows>
                          <template #prev>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-left</v-icon>
                            </v-btn>
                          </template>
                          <template #next>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-right</v-icon>
                            </v-btn>
                          </template>
                          <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout1" :key="index">
                            <v-card class="ma-1" style="display: flex; flex-direction: column; align-items: center; width: 66px; height: 80px;">
                              <div style="height: 70%; width: 100%;" class="d-flex justify-center pa-1">
                                <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                                <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                              </div>
                              <div style="height: 30%; width: 100%; margin-top: -10px !important;" class="d-flex justify-center pa-1">
                                <v-tooltip top>
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-col align="center" v-bind="attrs" v-on="on">
                                      <span class="text-truncate d-inline-block" style="font-weight: 600; width: 96%; line-height: 1;" :style="MobileSize ? `font-size: xx-small;` : `font-size: x-small;`">{{ itemGroupShop.shop_name }}</span>
                                    </v-col>
                                  </template>
                                  <span>{{ itemGroupShop.shop_name }}</span>
                                </v-tooltip>
                              </div>
                            </v-card>
                          </v-slide-item>
                        </v-slide-group>
                      </v-sheet>
                    </div>
                    <!-- Layout ด้านขวา -->
                    <div style="width: 90%;">
                      <v-sheet class="mx-auto mb-2" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout2})`, backgroundPosition: 'center'}">
                        <v-card-text style="text-align: center; font-weight: 600; margin: -12px 0;" :style="MobileSize ? `font-size: xx-small; color: ${textColorLayout2};` : `font-size: smaller; color: ${textColorLayout2};`">{{ textLayout2 }}</v-card-text>
                        <v-slide-group v-model="model" class="pa-2" active-class="success" style="margin-top: -10px;" show-arrows>
                          <template #prev>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-left</v-icon>
                            </v-btn>
                          </template>
                          <template #next>
                            <v-btn icon :color="colorArrowBtn" x-small>
                              <v-icon>mdi-chevron-right</v-icon>
                            </v-btn>
                          </template>
                          <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout2" :key="index">
                            <v-card class="ma-1" style="display: flex; flex-direction: column; align-items: center; width: 66px; height: 80px;">
                              <div style="height: 70%; width: 100%;" class="d-flex justify-center pa-1">
                                <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                                <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                              </div>
                              <div style="height: 30%; width: 100%; margin-top: -10px !important;" class="d-flex justify-center pa-1">
                                <v-tooltip top>
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-col align="center" v-bind="attrs" v-on="on">
                                      <span class="text-truncate d-inline-block" style="font-weight: 600; width: 96%; line-height: 1;" :style="MobileSize ? `font-size: xx-small;` : `font-size: x-small;`">{{ itemGroupShop.shop_name }}</span>
                                    </v-col>
                                  </template>
                                  <span>{{ itemGroupShop.shop_name }}</span>
                                </v-tooltip>
                              </div>
                            </v-card>
                          </v-slide-item>
                        </v-slide-group>
                      </v-sheet>
                    </div>
                  </div>
          </v-card>
        </v-col>
    </v-row>
    <v-row class="my-5 py-5 mr-1" justify="end"  v-if="tab === 0">
        <v-btn v-if="!MobileSize && !IpadSize" style="font-size: medium;" color="primary" @click="updateEditGroupShop">บันทึก</v-btn>
        <v-btn v-else small style="font-size: small;" color="primary" @click="updateEditGroupShop">บันทึก</v-btn>
    </v-row>
    <v-row dense v-if="tab === 1" class="mb-4 px-1">
      <v-col v-if="tab === 1" cols="12" :class="MobileSize ? 'd-flex align-center mb-4 px-2' : 'd-flex align-center mb-4'">
        <v-card-title style="font-weight: 600; margin-left: -12px;">ปรับแต่ง Banner</v-card-title>
        <v-spacer></v-spacer>
        <v-btn style="font-weight: 600;" color="error" small @click="dialogConfirmDelete = true"><v-icon small>mdi-delete</v-icon> ลบข้อมูล</v-btn>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
        <v-select v-model="selectLayoutBanner" :items="itemSelectLayoutBanner" item-text="name" item-value="value" label="จำนวน Layout" @change="handleSelectLayoutBanner" hide-details dense outlined></v-select>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? '12' : '6'">
        <v-select v-model="selectTypeBanner" :items="itemSelectTypeBanner" item-text="name" item-value="value" label="ประเภท Banner" @change="handleSelectTypeBanner" hide-details dense outlined></v-select>
      </v-col>
    </v-row>
    <v-row dense v-if="tab === 1" class="mb-4 px-1">
      <v-col cols="12" v-if="selectLayoutBanner" class="d-flex justify-center flex-column align-start" style="width: 100%;">
        <v-card-title style="font-weight: 600; font-size: medium;">Layout 1</v-card-title>
        <v-autocomplete v-if="type_layout_banner !== 'image' && this.type_layout_banner !== 'link' && this.type_layout_banner !== null" v-model="selectIDBanner1" :items="itemSelectIDBanner" item-text="name" item-value="id" :label="labelSelectID" @change="handleSelectIDBanner1" hide-details dense outlined :style="MobileSize || IpadSize ? 'width: 100%;' : 'width: 50%;'"></v-autocomplete>
        <v-text-field v-if="this.type_layout_banner === 'link' && this.type_layout_banner !== null" v-model="LinkBanner1" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับเว็บไซต์</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image1 !== ''" :src="image1" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image1 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner1($event, 'web')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image1 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner1('web')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับแอปพลิเคชัน</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image1_mobile !== ''" :src="image1_mobile" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image1_mobile === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner1($event, 'mobile')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image1_mobile !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner1('mobile')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="selectLayoutBanner === '2' || selectLayoutBanner === '3' || selectLayoutBanner === '4'" class="d-flex justify-center flex-column align-start" style="width: 100%;">
        <v-card-title style="font-weight: 600; font-size: medium;">Layout 2</v-card-title>
        <v-autocomplete v-if="type_layout_banner !== 'image' && this.type_layout_banner !== 'link' && this.type_layout_banner !== null" v-model="selectIDBanner2" :items="itemSelectIDBanner" item-text="name" item-value="id" :label="labelSelectID" @change="handleSelectIDBanner2" hide-details dense outlined :style="MobileSize || IpadSize ? 'width: 100%;' : 'width: 50%;'"></v-autocomplete>
        <v-text-field v-if="this.type_layout_banner === 'link' && this.type_layout_banner !== null" v-model="LinkBanner2" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับเว็บไซต์</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image2 !== ''" :src="image2" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image2 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner2($event, 'web')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image2 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner2('web')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับแอปพลิเคชัน</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image2_mobile !== ''" :src="image2_mobile" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image2_mobile === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner2($event, 'mobile')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image2_mobile !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner2('mobile')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="selectLayoutBanner === '3'  || selectLayoutBanner === '4'" class="d-flex justify-center flex-column align-start" style="width: 100%;">
        <v-card-title style="font-weight: 600; font-size: medium;">Layout 3</v-card-title>
        <v-autocomplete v-if="type_layout_banner !== 'image' && this.type_layout_banner !== 'link' && this.type_layout_banner !== null" v-model="selectIDBanner3" :items="itemSelectIDBanner" item-text="name" item-value="id" :label="labelSelectID" @change="handleSelectIDBanner3" hide-details dense outlined :style="MobileSize || IpadSize ? 'width: 100%;' : 'width: 50%;'"></v-autocomplete>
        <v-text-field v-if="this.type_layout_banner === 'link' && this.type_layout_banner !== null" v-model="LinkBanner3" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับเว็บไซต์</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image3 !== ''" :src="image3" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image3 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner3($event, 'web')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image3 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner3('web')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับแอปพลิเคชัน</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image3_mobile !== ''" :src="image3_mobile" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image3_mobile === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner3($event, 'mobile')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image3_mobile !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner3('mobile')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="selectLayoutBanner === '4'" class="d-flex justify-center flex-column align-start" style="width: 100%;">
        <v-card-title style="font-weight: 600; font-size: medium;">Layout 4</v-card-title>
        <v-autocomplete v-if="type_layout_banner !== 'image' && this.type_layout_banner !== 'link' && this.type_layout_banner !== null" v-model="selectIDBanner4" :items="itemSelectIDBanner" item-text="name" item-value="id" :label="labelSelectID" @change="handleSelectIDBanner4" hide-details dense outlined :style="MobileSize || IpadSize ? 'width: 100%;' : 'width: 50%;'"></v-autocomplete>
        <v-text-field v-if="this.type_layout_banner === 'link' && this.type_layout_banner !== null" v-model="LinkBanner4" label="กรุณาเพิ่มลิงก์ที่ต้องการลิงก์" hide-details dense outlined style="width: 99%;"></v-text-field>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับเว็บไซต์</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image4 !== ''" :src="image4" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image4 === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner4($event, 'web')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image4 !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner4('web')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
        <span style="font-weight: 400; font-size: medium;" class="mt-3">สำหรับแอปพลิเคชัน</span>
        <v-card class="mt-3 py-1 mx-2" elevation="0" style="border: 2px dashed #2A70C3; box-sizing: border-box; border-radius: 8px; width: 98%; height: 320px; display: flex; flex-direction: column; align-items: center;">
          <v-img v-if="image4_mobile !== ''" :src="image4_mobile" contain :max-width="MobileSize ? '280' : '360'" :max-height="MobileSize ? '140' : '180'" style="width: 100%; margin-top: 10px;"></v-img>
          <v-card-text>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12" align="center" v-if="image4_mobile === ''">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="220" height="110" contain></v-img>
                  </v-col>
                  <v-col cols="12" style="text-align: center;">
                    <span style="line-height: 16px; font-weight: 400; font-size: smaller;">(ไฟล์นามสกุล .jpg, .jpeg, .png)</span><br />
                    <v-btn color="primary" style="position: relative; overflow: hidden; width: 100px;" small class="mt-2">
                      เพิ่มรูปภาพ
                      <input
                        ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChangeLayoutBanner4($event, 'mobile')"
                        style="opacity: 0; position: absolute; left: 0; top: 0; width: 100px; height: 100%; cursor: pointer; z-index: 1;"
                      />
                    </v-btn>
                    <v-btn v-if="image4_mobile !== ''" color="error" dense small class="mt-2 ml-2" @click="removeImageLayoutBanner4('mobile')"><v-icon small>mdi-delete</v-icon></v-btn>
                  </v-col>
                </v-row>
              </v-col>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row :style="MobileSize ? 'margin-top: 10px;' : 'margin-top: 20px;'"  v-if="tab === 1">
      <v-col cols="12">
        <v-card-title style="font-weight: 600;">ตัวอย่างที่ปรับแต่ง</v-card-title>
      </v-col>
      <v-col cols="12">
        <v-card elevation="1" style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%;" class="pa-1">
          <!-- size ipadpro & pc -->
          <div v-if="!MobileSize && !IpadSize" class="d-flex justify-center mx-auto">
            <!-- 1 Layout -->
            <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%;" v-if="selectLayoutBanner === '1'">
              <div style="width: 100%; max-height: 350px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image1 !== ''" :src="image1" style="height: 350px;"></v-img>
                  <span v-if="image1 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
            </div>
            <!-- 2 Layout -->
            <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="selectLayoutBanner === '2'">
              <div style="width: 50%; max-height: 350px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image1 !== ''" :src="image1" style="height: 350px;"></v-img>
                  <span v-if="image1 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
              <div style="width: 50%; max-height: 350px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image2 !== ''" :src="image2" style="height: 350px;"></v-img>
                  <span v-if="image2 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
            </div>
            <!-- 3 Layout -->
            <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="selectLayoutBanner === '3'">
              <div style="width: 66%; max-height: 350px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image1 !== ''" :src="image1" style="height: 350px;"></v-img>
                  <span v-if="image1 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
              <div style="width: 34%; gap: 6px; max-height: 350px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image2 !== ''" :src="image2" style="height: 172px;"></v-img>
                  <span v-if="image2 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image3 !== ''" :src="image3" style="height: 172px;"></v-img>
                  <span v-if="image3 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
            </div>
            <!-- 4 Layout -->
            <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="selectLayoutBanner === '4'">
              <div style="width: 50%; gap: 6px; max-height: 350px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image1 !== ''" :src="image1" style="height: 172px;"></v-img>
                  <span v-if="image1 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image3 !== ''" :src="image3" style="height: 172px;"></v-img>
                  <span v-if="image3 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
              <div style="width: 50%; gap: 6px; max-height: 350px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image2 !== ''" :src="image2" style="height: 172px;"></v-img>
                  <span v-if="image2 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="image4 !== ''" :src="image4" style="height: 172px;"></v-img>
                  <span v-if="image4 === ''" style="color: black; font-size: medium;">ไม่มีรูปภาพ</span>
                </div>
              </div>
            </div>
          </div>
          <!-- size ipad && Mobile -->
          <div v-if="IpadSize && !MobileSize" class="d-flex justify-center mx-auto" style="width: 100%; overflow: hidden;">
            <v-row dense justify="center" :class="MobileSize ? 'pa-0' : 'mt-8'">
              <v-card elevation="0" style="width: 80%; max-width: 80vw; max-height: 250px; height: 100%;" class="d-flex justify-center">
                <v-carousel cycle height="250px" show-arrows-on-hover>
                  <v-card-text>
                    <v-carousel-item v-if="image1 !== ''">
                      <v-img :src="image1" style="border-radius: 4px;" max-width="100%" max-height="250px"></v-img>
                    </v-carousel-item>
                    <v-carousel-item v-if="image2 !== '' && (selectLayoutBanner !== '1')">
                      <v-img :src="image2" style="border-radius: 4px;" max-width="100%" max-height="250px"></v-img>
                    </v-carousel-item>
                    <v-carousel-item v-if="image3 !== '' && (selectLayoutBanner === '3' || selectLayoutBanner === '4')">
                      <v-img :src="image3" style="border-radius: 4px;" max-width="100%" max-height="250px"></v-img>
                    </v-carousel-item>
                    <v-carousel-item v-if="image4 !== '' && selectLayoutBanner === '4'">
                      <v-img :src="image4" style="border-radius: 4px;" max-width="100%" max-height="250px"></v-img>
                    </v-carousel-item>
                  </v-card-text>
                </v-carousel>
              </v-card>
            </v-row>
          </div>
          <div v-if="MobileSize && !IpadSize" class="d-flex justify-center mx-auto" style="width: 100%; overflow: hidden;">
            <v-row dense justify="center" :class="MobileSize ? 'pa-0' : 'mt-8'">
              <v-card elevation="0" style="width: 100%; max-width: 100vw; max-height: 250px; height: 100%;" class="d-flex justify-center">
                <v-carousel cycle height="190px" show-arrows-on-hover>
                  <v-card-text>
                    <v-carousel-item v-if="image1 !== ''">
                      <v-img :src="image1" style="border-radius: 4px;" max-width="100%" max-height="190px"></v-img>
                    </v-carousel-item>
                    <v-carousel-item v-if="image2 !== '' && (selectLayoutBanner !== '1')">
                      <v-img :src="image2" style="border-radius: 4px;" max-width="100%" max-height="190px"></v-img>
                    </v-carousel-item>
                    <v-carousel-item v-if="image3 !== '' && (selectLayoutBanner === '3' || selectLayoutBanner === '4')">
                      <v-img :src="image3" style="border-radius: 4px;" max-width="100%" max-height="190px"></v-img>
                    </v-carousel-item>
                    <v-carousel-item v-if="image4 !== '' && selectLayoutBanner === '4'">
                      <v-img :src="image4" style="border-radius: 4px;" max-width="100%" max-height="190px"></v-img>
                    </v-carousel-item>
                  </v-card-text>
                </v-carousel>
              </v-card>
            </v-row>
          </div>
      </v-card>
    </v-col>
    </v-row>
    <v-row class="my-5 py-5 mr-1" justify="end"  v-if="tab === 1">
      <v-btn v-if="!MobileSize && !IpadSize" style="font-size: medium;" color="primary" @click="updateEditBannerGroupShopV2('edit')">บันทึก</v-btn>
      <v-btn v-else small style="font-size: small;" color="primary" @click="updateEditBannerGroupShopV2('edit')">บันทึก</v-btn>
    </v-row>
    <!-- Dialog  -->
    <v-dialog v-model="dialogConfirm" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการทำรายการ
          </span>
           <v-btn icon dark @click="closeDialogConfirm()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: medium; font-weight: 600; line-height: 3;">{{ messageConfirm }}</span><br>
              <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
            </v-col>

          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirm()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmStatus(statusConfirm)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog ลบ  -->
    <v-dialog v-model="dialogConfirmDelete" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการทำรายการ
          </span>
           <v-btn icon dark @click="dialogConfirmDelete = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการลบข้อมูล</span><br>
              <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
            </v-col>

          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogConfirmDelete = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click=" updateEditBannerGroupShopV2('delete')">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    </v-container>
  </v-card>
</template>

<script>
export default {
  data () {
    return {
      itemGroupShopAll: [],
      itemGroupShopAll1: [],
      itemGroupShopAll2: [],
      model: null,
      logoImage: '',
      backgroundImageBanner: '',
      textBanner: '',
      textColorBanner: '',
      selectLayout: '',
      selectSizeLayout: '',
      disabledSelectLayout: false,
      itemSelectLayout:
      [
        { name: '1', value: '1' },
        { name: '2', value: '2' }
      ],
      itemSelectSizeLayout:
      [
        { name: '45 : 45', value: { width1: '45', width2: '45' } },
        { name: '40 : 50', value: { width1: '40', width2: '50' } },
        { name: '50 : 40', value: { width1: '50', width2: '40' } },
        { name: '30 : 60', value: { width1: '30', width2: '60' } },
        { name: '60 : 30', value: { width1: '60', width2: '30' } }
      ],
      backgroundImageLayout1: '',
      backgroundImageLayout2: '',
      widthLeft: '90%',
      widthRight: '',
      textLayout1: '',
      textColorLayout1: '',
      textLayout2: '',
      textColorLayout2: '',
      textColorAllBtn: '#333333',
      bgColorAllBtn: '#ffffff',
      colorArrowBtn: '#333333',
      selectGroupShop1: '',
      selectGroupShop2: '',
      groupShopLayout1: [],
      groupShopLayout2: [],
      bg_banner: '',
      logo_banner: '',
      bg_layout1: '',
      bg_layout2: '',
      num_layout: '1',
      size_layout1: '',
      size_layout2: '',
      switchActive: true,
      dataActive: '',
      switchType: 'custom',
      dataType: '',
      itemTab: [
        'Custom', 'Banner'
      ],
      tab: 0,
      backgroundImageLayoutLeftTop: '',
      backgroundImageLayoutLeftBottom: '',
      backgroundImageLayoutRightTop: '',
      backgroundImageLayoutRightBottom: '',
      linkLeftTop: '',
      linkLeftBottom: '',
      linkRightTop: '',
      linkRightBottom: '',
      bg_layout_left_top: '',
      bg_layout_left_bottom: '',
      bg_layout_right_top: '',
      bg_layout_right_bottom: '',
      num_layout_banner: '1',
      itemSelectLayoutBanner:
      [
        { name: '1', value: '1' },
        { name: '2', value: '2' },
        { name: '3', value: '3' },
        { name: '4', value: '4' }
      ],
      selectLayoutBanner: '',
      dialogConfirm: false,
      messageConfirm: '',
      statusConfirm: '',
      type_layout_banner: '',
      itemSelectTypeBanner:
      [
        { name: 'หน้าประเภทร้านค้า', value: 'group' },
        { name: 'หน้าร้านค้า', value: 'shop' },
        { name: 'รูปภาพ (แสดงแค่รูปภาพ)', value: 'image' },
        { name: 'ลิงก์ไปยังเว็บไซต์ภายนอก', value: 'link' }
      ],
      selectTypeBanner: '',
      itemSelectIDBanner: [],
      labelSelectID: '',
      image1: '',
      image2: '',
      image3: '',
      image4: '',
      image_layout_1: '',
      image_layout_2: '',
      image_layout_3: '',
      image_layout_4: '',
      image1_mobile: '',
      image2_mobile: '',
      image3_mobile: '',
      image4_mobile: '',
      image_layout_1_mobile: '',
      image_layout_2_mobile: '',
      image_layout_3_mobile: '',
      image_layout_4_mobile: '',
      selectIDBanner1: '',
      selectIDBanner2: '',
      selectIDBanner3: '',
      selectIDBanner4: '',
      clearData2: '',
      clearData3: '',
      clearData4: '',
      LinkBanner1: '',
      LinkBanner2: '',
      LinkBanner3: '',
      LinkBanner4: '',
      dialogConfirmDelete: false,
      image_banner: [],
      image_banner_mobile: []

    }
  },
  async created () {
    await this.getResultGroupShop()
    await this.getDetailGroupShop()
    await this.getActiveGroupShop()
    await this.getTypeGroupShop()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminGroupShopBGManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminGroupShopBGManage' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async getActiveGroupShop () {
      var data = {
        status: this.dataActive || ''
      }
      await this.$store.dispatch('actionActiveCustomGroupShop', data)
      var response = await this.$store.state.ModuleShop.stateActiveCustomGroupShop
      if (response.code === 200) {
        this.dataActive = response.data.status
        if (this.dataActive === 'active') {
          this.switchActive = true
        } else if (this.dataActive === 'inactive') {
          this.switchActive = false
        }
      } else {
        this.dataActive = 'inactive'
        this.switchActive = false
      }
    },
    handleActive (value) {
      if (value === true) {
        this.dataActive = 'active'
        this.$swal.fire({ icon: 'success', text: 'เปิดการใช้งานสำเร็จ', showConfirmButton: false, timer: 2000 })
      } else if (value === false) {
        this.dataActive = 'inactive'
        this.$swal.fire({ icon: 'success', text: 'ปิดการใช้งานสำเร็จ', showConfirmButton: false, timer: 2000 })
      }
      this.getActiveGroupShop()
    },
    async getTypeGroupShop () {
      var data = {
        status: this.dataType || ''
      }
      await this.$store.dispatch('actionTypeGroupShop', data)
      var response = await this.$store.state.ModuleShop.stateTypeGroupShop
      if (response.code === 200) {
        this.dataType = response.data.status_edit
        this.switchType = this.dataType
      }
    },
    handleType (value) {
      this.dialogConfirm = true
      this.statusConfirm = value
      this.messageConfirm = 'คุณต้องการใช้รูปแบบ ' + value
    },
    async getDetailGroupShop () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetCustomGroupShop')
      var response = await this.$store.state.ModuleShop.stateGetCustomGroupShop
      // console.log('response', response)
      if (response.code === 200) {
        this.logoImage = response.data.logo_banner
        this.logo_banner = response.data.logo_banner
        this.textBanner = response.data.text_banner
        this.textLayout1 = response.data.text_layout1
        this.textLayout2 = response.data.text_layout2
        this.backgroundImageBanner = response.data.bg_banner
        this.bg_banner = response.data.bg_banner
        this.backgroundImageLayout1 = response.data.bg_layout1
        this.bg_layout1 = response.data.bg_layout1
        this.backgroundImageLayout2 = response.data.bg_layout2
        this.bg_layout2 = response.data.bg_layout2
        this.groupShopLayout1 = response.data.group_shop_layout1
        this.selectGroupShop1 = this.groupShopLayout1
        this.groupShopLayout2 = response.data.group_shop_layout2
        this.selectGroupShop2 = this.groupShopLayout2
        this.textColorBanner = response.data.text_banner_color
        this.textColorLayout1 = response.data.color_layout1
        this.textColorLayout2 = response.data.color_layout2
        this.textColorAllBtn = response.data.color_text_btn_all
        this.bgColorAllBtn = response.data.color_btn_all
        this.colorArrowBtn = response.data.color_btn_arrow
        this.num_layout = response.data.num_layout
        this.selectLayout = response.data.num_layout
        if (this.selectLayout === '2') {
          this.disabledSelectLayout = true
        }
        this.size_layout1 = response.data.size_layout1
        this.size_layout2 = response.data.size_layout2
        this.widthLeft = response.data.size_layout1
        this.widthRight = response.data.size_layout2
        if (this.size_layout1 === '45%') {
          this.selectSizeLayout = { width1: '45', width2: '45' }
        } else if (this.size_layout1 === '50%') {
          this.selectSizeLayout = { width1: '50', width2: '40' }
        } else if (this.size_layout1 === '40%') {
          this.selectSizeLayout = { width1: '40', width2: '50' }
        } else if (this.size_layout1 === '60%') {
          this.selectSizeLayout = { width1: '60', width2: '30' }
        } else if (this.size_layout1 === '30%') {
          this.selectSizeLayout = { width1: '30', width2: '60' }
        } else if (this.size_layout1 === '90%') {
          this.selectSizeLayout = { width1: '90', width2: '0' }
        }
      }
      this.$store.commit('closeLoader')
    },
    async getDetailBannerGroupShop () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetBannerGroupShop')
      var response = await this.$store.state.ModuleShop.stateGetBannerGroupShop
      // console.log('response', response)
      if (response.code === 200) {
        this.backgroundImageLayoutLeftTop = response.data.image_layout_left_top
        this.backgroundImageLayoutLeftBottom = response.data.image_layout_left_bottom
        this.backgroundImageLayoutRightTop = response.data.image_layout_right_top
        this.backgroundImageLayoutRightBottom = response.data.image_layout_right_bottom
        this.bg_layout_left_top = response.data.image_layout_left_top
        this.bg_layout_left_bottom = response.data.image_layout_left_bottom
        this.bg_layout_right_top = response.data.image_layout_right_top
        this.bg_layout_right_bottom = response.data.image_layout_right_bottom
        this.linkLeftTop = response.data.link_layout_left_top
        this.linkLeftBottom = response.data.link_layout_left_bottom
        this.linkRightTop = response.data.link_layout_right_top
        this.linkRightBottom = response.data.link_layout_right_bottom
        this.num_layout_banner = response.data.num_layout_banner
        if (this.num_layout_banner === null || this.num_layout_banner === '1') {
          this.selectLayoutBanner = '1'
          this.num_layout_banner = '1'
        } else if (this.num_layout_banner === '2') {
          this.selectLayoutBanner = this.num_layout_banner
        } else if (this.num_layout_banner === '3') {
          this.selectLayoutBanner = this.num_layout_banner
        } else if (this.num_layout_banner === '4') {
          this.selectLayoutBanner = this.num_layout_banner
        }
      }
      this.$store.commit('closeLoader')
    },
    async getDetailBannerGroupShopV2 () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetBannerGroupShopV2')
      var response = await this.$store.state.ModuleShop.stateGetBannerGroupShopV2
      // console.log('response', response)
      if (response.code === 200) {
        // console.log('response.data', response.data)
        this.type_layout_banner = response.data.type_layout_banner
        if (this.type_layout_banner === 'group_shop') {
          this.type_layout_banner = 'group'
        }
        this.selectTypeBanner = this.type_layout_banner
        await this.handleSelectTypeBanner(this.type_layout_banner)
        this.num_layout_banner = response.data.num_layout_banner
        this.selectLayoutBanner = this.num_layout_banner || '1'
        this.num_layout_banner = this.selectLayoutBanner
        const banners = response.data.banner
        if (banners[0]) {
          this.image_layout_1_mobile = banners[0].image || ''
          this.image1_mobile = this.image_layout_1_mobile
          this.image_layout_1 = banners[0].image_web || ''
          this.image1 = this.image_layout_1
          this.LinkBanner1 = banners[0].link || ''
          if (this.type_layout_banner !== 'image' && this.type_layout_banner !== 'link') {
            await this.handleSelectIDBanner1(parseInt(banners[0].action_id) || '')
          }
        }

        if (banners[1] && this.num_layout_banner >= '2') {
          this.image_layout_2_mobile = banners[1].image || ''
          this.image2_mobile = this.image_layout_2_mobile
          this.image_layout_2 = banners[1].image_web || ''
          this.image2 = this.image_layout_2
          this.LinkBanner2 = banners[1].link || ''
          if (this.type_layout_banner !== 'image' && this.type_layout_banner !== 'link') {
            await this.handleSelectIDBanner2(parseInt(banners[1].action_id) || '')
          }
        }

        if (banners[2] && this.num_layout_banner >= '3') {
          this.image_layout_3_mobile = banners[2].image || ''
          this.image3_mobile = this.image_layout_3_mobile
          this.image_layout_3 = banners[2].image_web || ''
          this.image3 = this.image_layout_3
          this.LinkBanner3 = banners[2].link || ''
          if (this.type_layout_banner !== 'image' && this.type_layout_banner !== 'link') {
            await this.handleSelectIDBanner3(parseInt(banners[2].action_id) || '')
          }
        }

        if (banners[3] && this.num_layout_banner >= '4') {
          this.image_layout_4_mobile = banners[3].image || ''
          this.image4_mobile = this.image_layout_4_mobile
          this.image_layout_4 = banners[3].image_web || ''
          this.image4 = this.image_layout_4
          this.LinkBanner4 = banners[3].link || ''
          if (this.type_layout_banner !== 'image' && this.type_layout_banner !== 'link') {
            await this.handleSelectIDBanner4(parseInt(banners[3].action_id) || '')
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async getResultGroupShop () {
      this.$store.commit('openLoader')
      const value = ''
      var dataAllShop = {
        group_id: value
      }
      await this.$store.dispatch('actionFilterGroupShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateFilterGroupShop
      if (response.result === 'SUCCESS') {
        this.itemGroupShopAll = response.data.group_seller_data.map(el => {
          return {
            shop_logo: el.group_shop_media_path,
            shop_name: el.group_name,
            shop_id: el.id
          }
        })
        this.itemGroupShopAll1 = this.itemGroupShopAll
        this.itemGroupShopAll2 = this.itemGroupShopAll
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
        }
      }
      this.$store.commit('closeLoader')
    },
    itemGroupShopLayout1 (item) {
      this.groupShopLayout1 = item
    },
    itemGroupShopLayout2 (item) {
      this.groupShopLayout2 = item
    },
    async onFileChangeBanner (event) {
      // console.log('event', event.target.files)
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        // console.log('file', file)
        if (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png') {
          this.backgroundImageBanner = URL.createObjectURL(file)
          const reader = new FileReader()
          const resultReader = await new Promise((resolve) => {
            reader.readAsDataURL(file)
            reader.onload = () => resolve(reader.result)
          })
          this.bg_banner = resultReader.split(',')[1]
          // console.log('this.bg_banner', this.bg_banner)
          // this.bg_banner = `image/jpeg;base64,${this.bg_banner}`
        }
      } else {
        this.backgroundImageBanner = ''
      }
    },
    removeImageBanner () {
      this.backgroundImageBanner = ''
      this.bg_banner = ''
      this.$refs.fileInput.value = ''
    },
    async onFileChangeLogo (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png') {
          this.logoImage = URL.createObjectURL(file)
          const reader = new FileReader()
          const resultReader = await new Promise((resolve) => {
            reader.readAsDataURL(file)
            reader.onload = () => resolve(reader.result)
          })
          this.logo_banner = resultReader.split(',')[1]
          // console.log('this.logo_banner', this.logo_banner)
        }
      } else {
        this.logoImage = ''
      }
    },
    removeImageLogo () {
      this.logoImage = ''
      this.logo_banner = ''
      this.$refs.fileInput.value = ''
    },
    async onFileChangeLayout1 (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png') {
          this.backgroundImageLayout1 = URL.createObjectURL(file)
          const reader = new FileReader()
          const resultReader = await new Promise((resolve) => {
            reader.readAsDataURL(file)
            reader.onload = () => resolve(reader.result)
          })
          this.bg_layout1 = resultReader.split(',')[1]
          // console.log('this.bg_layout1', this.bg_layout1)
        }
      } else {
        this.backgroundImageLayout1 = ''
      }
    },
    removeImageLayout1 () {
      this.backgroundImageLayout1 = ''
      this.bg_layout1 = ''
      this.$refs.fileInput.value = ''
    },
    async onFileChangeLayout2 (event) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]
        if (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png') {
          this.backgroundImageLayout2 = URL.createObjectURL(file)
          const reader = new FileReader()
          const resultReader = await new Promise((resolve) => {
            reader.readAsDataURL(file)
            reader.onload = () => resolve(reader.result)
          })
          this.bg_layout2 = resultReader.split(',')[1]
        }
      } else {
        this.backgroundImageLayout2 = ''
      }
    },
    removeImageLayout2 () {
      this.backgroundImageLayout2 = ''
      this.bg_layout2 = ''
      this.$refs.fileInput.value = ''
    },
    handleSelectLayout (value) {
      if (value === '1') {
        this.disabledSelectLayout = false
        this.widthLeft = '90%'
        this.widthRight = '0'
      } else if (value === '2') {
        this.disabledSelectLayout = true
        this.widthLeft = '45%'
        this.widthRight = '45%'
      }
      this.num_layout = value
    },
    handleSelectSizeLayout (value) {
      // console.log(value)
      this.widthLeft = value.width1 + '%'
      this.widthRight = value.width2 + '%'
      this.size_layout1 = value.width1
      this.size_layout2 = value.width2
    },
    async updateEditGroupShop () {
      if (this.num_layout === '1') {
        this.size_layout1 = '90'
        this.size_layout2 = '0'
      }
      this.size_layout1 = this.size_layout1.replace(/%/g, '')
      this.size_layout2 = this.size_layout2.replace(/%/g, '')
      var payload = {
        logo_banner: this.logo_banner ? this.logo_banner : '',
        text_banner: this.textBanner ? this.textBanner : '',
        text_layout1: this.textLayout1 ? this.textLayout1 : '',
        text_layout2: this.textLayout2 ? this.textLayout2 : '',
        bg_banner: this.bg_banner ? this.bg_banner : '',
        bg_layout1: this.bg_layout1 ? this.bg_layout1 : '',
        bg_layout2: this.bg_layout2 ? this.bg_layout2 : '',
        group_shop_layout1: this.groupShopLayout1 ? this.groupShopLayout1 : [],
        group_shop_layout2: this.groupShopLayout2 ? this.groupShopLayout2 : [],
        size_layout1: this.size_layout1 ? this.size_layout1 : '',
        size_layout2: this.size_layout2 ? this.size_layout2 : '',
        text_banner_color: this.textColorBanner ? this.textColorBanner : '',
        color_layout1: this.textColorLayout1 ? this.textColorLayout1 : '',
        color_layout2: this.textColorLayout2 ? this.textColorLayout2 : '',
        num_layout: this.num_layout ? this.num_layout : 1,
        color_text_btn_all: this.textColorAllBtn ? this.textColorAllBtn : '',
        color_btn_all: this.bgColorAllBtn ? this.bgColorAllBtn : '',
        color_btn_arrow: this.colorArrowBtn ? this.colorArrowBtn : ''
      }
      // console.log('payload', payload)
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionCustomGroupShop', payload)
      var response = await this.$store.state.ModuleShop.stateCustomGroupShop
      if (response.code === 200) {
        // console.log('response', response)
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getDetailGroupShop()
        if (this.dataType === 'banner' || this.dataType === 'default') {
          this.handleType('custom')
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
      }
    },
    async changeTab (val) {
      this.tab = val
      if (this.tab === 0) {
        await this.getDetailGroupShop()
        // await this.getDataProductGroupShop()
      } else if (this.tab === 1) {
        // await this.getGroupStore()
        // await this.getDetailBannerGroupShop()
        await this.getDetailBannerGroupShopV2()
      }
    },
    async updateEditBannerGroupShop () {
      if (this.selectLayoutBanner === '1') {
        this.bg_layout_left_bottom = ''
        this.linkLeftBottom = ''
        this.bg_layout_right_top = ''
        this.linkRightTop = ''
        this.bg_layout_right_bottom = ''
        this.linkRightBottom = ''
      } else if (this.selectLayoutBanner === '2') {
        this.bg_layout_left_bottom = ''
        this.linkLeftBottom = ''
        this.bg_layout_right_bottom = ''
        this.linkRightBottom = ''
      } else if (this.selectLayoutBanner === '3') {
        this.bg_layout_left_bottom = ''
        this.linkLeftBottom = ''
      }
      var payload = {
        image_layout_left_top: this.bg_layout_left_top ? this.bg_layout_left_top : '',
        image_layout_left_bottom: this.bg_layout_left_bottom ? this.bg_layout_left_bottom : '',
        image_layout_right_top: this.bg_layout_right_top ? this.bg_layout_right_top : '',
        image_layout_right_bottom: this.bg_layout_right_bottom ? this.bg_layout_right_bottom : '',
        link_layout_left_top: this.linkLeftTop ? this.linkLeftTop : '',
        link_layout_left_bottom: this.linkLeftBottom ? this.linkLeftBottom : '',
        link_layout_right_top: this.linkRightTop ? this.linkRightTop : '',
        link_layout_right_bottom: this.linkRightBottom ? this.linkRightBottom : '',
        num_layout_banner: this.selectLayoutBanner
      }
      // console.log('payload', payload)
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionEditBannerGroupShop', payload)
      var response = await this.$store.state.ModuleShop.stateEditBannerGroupShop
      if (response.code === 200) {
        // console.log('response', response)
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getDetailBannerGroupShop()
        if (this.dataType === 'custom' || this.dataType === 'default') {
          this.handleType('banner')
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
      }
    },
    handleSelectLayoutBanner (value) {
      this.num_layout_banner = value
      this.image_banner = []
      this.image_banner_mobile = []
      this.selectIDBanner1 = null
      this.LinkBanner1 = null
      this.selectIDBanner2 = null
      this.LinkBanner2 = null
      this.selectIDBanner3 = null
      this.LinkBanner3 = null
      this.selectIDBanner4 = null
      this.LinkBanner4 = null
      this.removeImageLayoutBanner1()
      this.removeImageLayoutBanner2()
      this.removeImageLayoutBanner3()
      this.removeImageLayoutBanner4()
    },
    confirmStatus (status) {
      this.dataType = status
      this.getTypeGroupShop()
      this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 1500 })
      this.dialogConfirm = false
    },
    closeDialogConfirm () {
      this.switchType = this.dataType
      this.dialogConfirm = false
    },
    async handleSelectTypeBanner (value) {
      // console.log('value', value)
      this.type_layout_banner = value
      this.selectIDBanner1 = ''
      this.selectIDBanner2 = ''
      this.selectIDBanner3 = ''
      this.selectIDBanner4 = ''
      if (value === 'group') {
        await this.getGroupShop()
        this.labelSelectID = 'เลือกประเภทร้านค้าที่ต้องการลิงก์'
      } else if (value === 'shop') {
        await this.getShop()
        this.labelSelectID = 'เลือกร้านค้าที่ต้องการลิงก์'
      }
    },
    async handleSelectIDBanner1 (value) {
      var selectedBanner = await this.itemSelectIDBanner.find(item => item.id === value)
      this.selectIDBanner1 = { name: selectedBanner.name, id: selectedBanner.id }
      // console.log('this.selectIDBanner1', this.selectIDBanner1)
    },
    async handleSelectIDBanner2 (value) {
      var selectedBanner = await this.itemSelectIDBanner.find(item => item.id === value)
      this.selectIDBanner2 = { name: selectedBanner.name, id: selectedBanner.id }
    },
    async handleSelectIDBanner3 (value) {
      var selectedBanner = await this.itemSelectIDBanner.find(item => item.id === value)
      this.selectIDBanner3 = { name: selectedBanner.name, id: selectedBanner.id }
    },
    async handleSelectIDBanner4 (value) {
      var selectedBanner = await this.itemSelectIDBanner.find(item => item.id === value)
      this.selectIDBanner4 = { name: selectedBanner.name, id: selectedBanner.id }
    },
    // async onFileChangeLayoutBanner1 (event) {
    //   if (event.target.files && event.target.files.length > 0) {
    //     const file = event.target.files[0]

    //     if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
    //       const url = URL.createObjectURL(file)
    //       this.image1 = url

    //       const reader = new FileReader()
    //       reader.onload = async () => {
    //         this.image_layout_1 = reader.result.split(',')[1]

    //         const Banner = { image: [this.image_layout_1], type: 'custom_banner' }

    //         this.$store.commit('openLoader')

    //         await this.$store.dispatch('actionsUploadToS3', Banner)
    //         const response = this.$store.state.ModuleShop.stateUploadToS3

    //         if (response.message === 'List Success.') {
    //           this.$store.commit('closeLoader')
    //           this.image_layout_1 = response.data.list_path[0].path
    //         }
    //         this.$forceUpdate()
    //         // console.log('this.image1', this.image1)
    //         // console.log('this.image_layout_1', this.image_layout_1)
    //       }
    //       reader.readAsDataURL(file)
    //     } else {
    //       this.$swal.fire({
    //         icon: 'warning',
    //         text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
    //         showConfirmButton: false,
    //         timer: 1500
    //       })
    //     }
    //   } else {
    //     // console.warn('ไม่มีไฟล์ที่เลือก')
    //   }
    // },
    async onFileChangeLayoutBanner1 (event, status) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]

        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          if (status === 'web') {
            this.image1 = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner = this.image_banner.filter(banner => banner.layout !== '1')
              this.image_banner.push({ image: [base64Image], type: 'custom_banner', layout: '1', num_layout: this.num_layout_banner })
              // console.log('this.image_banner', this.image_banner)
              this.$forceUpdate()
              // console.log('this.image1', this.image1)
            }
            reader.readAsDataURL(file)
          } else if (status === 'mobile') {
            this.image1_mobile = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner_mobile = this.image_banner_mobile.filter(banner => banner.layout !== '1')
              this.image_banner_mobile.push({ image: [base64Image], type: 'custom_banner_mobile', layout: '1', num_layout: this.num_layout_banner })
              // console.log('this.image_banner_mobile', this.image_banner_mobile)
              this.$forceUpdate()
              // console.log('this.image1_mobile', this.image1_mobile)
            }
            reader.readAsDataURL(file)
            this.$forceUpdate()
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayoutBanner1 (status) {
      if (status === 'web') {
        this.image1 = ''
        this.image_layout_1 = ''
      } else if (status === 'mobile') {
        this.image1_mobile = ''
        this.image_layout_1_mobile = ''
      } else if (status === '' || status === null || status === undefined) {
        this.image1 = ''
        this.image_layout_1 = ''
        this.image1_mobile = ''
        this.image_layout_1_mobile = ''
      }
      this.$refs.fileInput.value = ''
    },
    async onFileChangeLayoutBanner2 (event, status) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]

        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          if (status === 'web') {
            this.image2 = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner = this.image_banner.filter(banner => banner.layout !== '2')
              this.image_banner.push({ image: [base64Image], type: 'custom_banner', layout: '2', num_layout: this.num_layout_banner })
              // console.log('this.image_banner', this.image_banner)
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else if (status === 'mobile') {
            this.image2_mobile = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner_mobile = this.image_banner_mobile.filter(banner => banner.layout !== '2')
              this.image_banner_mobile.push({ image: [base64Image], type: 'custom_banner_mobile', layout: '2', num_layout: this.num_layout_banner })
              // console.log('this.image_banner_mobile', this.image_banner_mobile)
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
            this.$forceUpdate()
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayoutBanner2 (status) {
      if (status === 'web') {
        this.image2 = ''
        this.image_layout_2 = ''
      } else if (status === 'mobile') {
        this.image2_mobile = ''
        this.image_layout_2_mobile = ''
      } else if (status === '' || status === null || status === undefined) {
        this.image2 = ''
        this.image_layout_2 = ''
        this.image2_mobile = ''
        this.image_layout_2_mobile = ''
      }
      this.$refs.fileInput.value = ''
    },
    async onFileChangeLayoutBanner3 (event, status) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]

        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          if (status === 'web') {
            this.image3 = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner = this.image_banner.filter(banner => banner.layout !== '3')
              this.image_banner.push({ image: [base64Image], type: 'custom_banner', layout: '3', num_layout: this.num_layout_banner })
              // console.log('this.image_banner', this.image_banner)
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else if (status === 'mobile') {
            this.image3_mobile = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner_mobile = this.image_banner_mobile.filter(banner => banner.layout !== '3')
              this.image_banner_mobile.push({ image: [base64Image], type: 'custom_banner_mobile', layout: '3', num_layout: this.num_layout_banner })
              // console.log('this.image_banner_mobile', this.image_banner_mobile)
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
            this.$forceUpdate()
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayoutBanner3 (status) {
      if (status === 'web') {
        this.image3 = ''
        this.image_layout_3 = ''
      } else if (status === 'mobile') {
        this.image3_mobile = ''
        this.image_layout_3_mobile = ''
      } else if (status === '' || status === null || status === undefined) {
        this.image3 = ''
        this.image_layout_3 = ''
        this.image3_mobile = ''
        this.image_layout_3_mobile = ''
      }
      this.$refs.fileInput.value = ''
    },
    async onFileChangeLayoutBanner4 (event, status) {
      if (event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0]

        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          if (status === 'web') {
            this.image4 = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner = this.image_banner.filter(banner => banner.layout !== '4')
              this.image_banner.push({ image: [base64Image], type: 'custom_banner', layout: '4', num_layout: this.num_layout_banner })
              // console.log('this.image_banner', this.image_banner)
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else if (status === 'mobile') {
            this.image4_mobile = url
            const reader = new FileReader()
            reader.onload = () => {
              let base64Image = ''
              base64Image = reader.result.split(',')[1]
              this.image_banner_mobile = this.image_banner_mobile.filter(banner => banner.layout !== '4')
              this.image_banner_mobile.push({ image: [base64Image], type: 'custom_banner_mobile', layout: '4', num_layout: this.num_layout_banner })
              // console.log('this.image_banner_mobile', this.image_banner_mobile)
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
            this.$forceUpdate()
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    removeImageLayoutBanner4 (status) {
      if (status === 'web') {
        this.image4 = ''
        this.image_layout_4 = ''
      } else if (status === 'mobile') {
        this.image4_mobile = ''
        this.image_layout_4_mobile = ''
      } else if (status === '' || status === null || status === undefined) {
        this.image4 = ''
        this.image_layout_4 = ''
        this.image4_mobile = ''
        this.image_layout_4_mobile = ''
      }
      this.$refs.fileInput.value = ''
    },
    async getGroupShop () {
      this.$store.commit('openLoader')
      var dataAllShop = {
        group_id: ''
      }
      await this.$store.dispatch('actionFilterGroupShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateFilterGroupShop
      if (response.result === 'SUCCESS') {
        this.itemSelectIDBanner = []
        this.itemSelectIDBanner = response.data.group_seller_data.map(item => {
          return {
            name: item.group_name,
            id: item.id
          }
        })
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
        }
      }
      this.$store.commit('closeLoader')
    },
    async getShop () {
      this.$store.commit('openLoader')
      const data = {
        unset_shop_profile: 'YES'
      }
      await this.$store.dispatch('actionsGroupStoreName', data)
      var response = await this.$store.state.ModuleHompage.stateGroupStoreName
      if (response.result === 'SUCCESS') {
        this.itemSelectIDBanner = []
        this.itemSelectIDBanner = response.data.seller_shop_data.filter(item => item.shop_status === 'active').map(item => {
          return {
            name: item.shop_name,
            id: item.seller_shop_id
          }
        })
      } else {
      }
      this.$store.commit('closeLoader')
    },
    async uploadS3 () {
      if (this.image_banner.length > 0) {
        this.$store.commit('openLoader')
        for (const banner of this.image_banner) {
          const layout = banner.layout
          await this.$store.dispatch('actionsUploadToS3', banner)
          const response = await this.$store.state.ModuleShop.stateUploadToS3
          if (response.code === 200) {
            const uploadedPath = response.data.list_path[0].path
            if (layout === '1') {
              this.image_layout_1 = uploadedPath
            }
            if (layout === '2') {
              this.image_layout_2 = uploadedPath
            }
            if (layout === '3') {
              this.image_layout_3 = uploadedPath
            }
            if (layout === '4') {
              this.image_layout_4 = uploadedPath
            }
          }
        }
        this.$forceUpdate()
        this.$store.commit('closeLoader')
      }
      this.image_banner = []
    },
    async uploadS3Mobile () {
      if (this.image_banner_mobile.length > 0) {
        this.$store.commit('openLoader')
        for (const banner of this.image_banner_mobile) {
          const layout = banner.layout
          await this.$store.dispatch('actionsUploadToS3', banner)
          const response = await this.$store.state.ModuleShop.stateUploadToS3
          if (response.code === 200) {
            const uploadedPath = response.data.list_path[0].path
            if (layout === '1') {
              this.image_layout_1_mobile = uploadedPath
            }
            if (layout === '2') {
              this.image_layout_2_mobile = uploadedPath
            }
            if (layout === '3') {
              this.image_layout_3_mobile = uploadedPath
            }
            if (layout === '4') {
              this.image_layout_4_mobile = uploadedPath
            }
          }
        }
        this.$forceUpdate()
        this.$store.commit('closeLoader')
      }
      this.image_banner_mobile = []
    },
    async updateEditBannerGroupShopV2 (status) {
      var isValid = true
      var type = ''
      var payload = {}
      type = this.type_layout_banner
      if (type === 'group') {
        type = 'group_shop'
      }
      if (this.image_banner.length !== 0 || this.image_banner_mobile.length !== 0) {
        await this.uploadS3()
        await this.uploadS3Mobile()
      }
      // console.log('this.image_layout_1', this.image_layout_1)
      if ((this.type_layout_banner === '' || this.type_layout_banner === null || this.type_layout_banner === undefined) && status === 'edit') {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกประเภท Banner', showConfirmButton: false, timer: 1500 })
        isValid = false
      } else if ((this.type_layout_banner === 'group' || this.type_layout_banner === 'shop') && status === 'edit') {
        if (this.selectLayoutBanner === '1') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.selectIDBanner1.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '2') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.selectIDBanner1.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.selectIDBanner2.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '3') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.selectIDBanner1.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.selectIDBanner2.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.selectIDBanner3.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '4') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_4 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_4_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.selectIDBanner1.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.selectIDBanner2.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.selectIDBanner3.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.selectIDBanner4.id === undefined) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเลือกข้อมูลของ layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        }
      } else if (this.type_layout_banner === 'image' && status === 'edit') {
        if (this.selectLayoutBanner === '1') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '2') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '3') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '4') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_4 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_3_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image_layout_4_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        }
      } else if (this.type_layout_banner === 'link' && status === 'edit') {
        if (this.selectLayoutBanner === '1') {
          if (this.image_layout_1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '2') {
          if (this.image1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.LinkBanner2 === '' || this.LinkBanner2 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '3') {
          if (this.image1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image3 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image3_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.LinkBanner2 === '' || this.LinkBanner2 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.LinkBanner3 === '' || this.LinkBanner3 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        } else if (this.selectLayoutBanner === '4') {
          if (this.image1 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image2 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image3 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image4 === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของเว็บไซต์ใน layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.image1_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image2_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image3_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.image4_mobile === '') {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพของแอปพลิเคชันใน layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
          if (this.LinkBanner1 === '' || this.LinkBanner1 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 1', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.LinkBanner2 === '' || this.LinkBanner2 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 2', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.LinkBanner3 === '' || this.LinkBanner3 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 3', showConfirmButton: false, timer: 1500 })
            isValid = false
          } else if (this.LinkBanner4 === '' || this.LinkBanner4 === null) {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มลิงก์ของ layout 4', showConfirmButton: false, timer: 1500 })
            isValid = false
          }
        }
      }
      if (isValid === true) {
        if (status === 'delete') {
          payload = {
            banner: [],
            num_layout_banner: null,
            type_layout_banner: null
          }
        } else if (status === 'edit') {
          payload = {
            banner: [
              {
                image: this.image_layout_1_mobile,
                image_web: this.image_layout_1,
                name: this.selectIDBanner1.name === undefined || this.selectIDBanner1.name === null || this.type_layout_banner === 'image' ? '' : this.selectIDBanner1.name,
                action_id: this.selectIDBanner1.id === undefined || this.selectIDBanner1.id === null || this.type_layout_banner === 'image' ? '' : String(this.selectIDBanner1.id),
                action_type: this.type_layout_banner,
                link: this.type_layout_banner === 'link' ? this.LinkBanner1 : '',
                layout: '1'
              },
              {
                image: this.selectLayoutBanner === '1' ? '' : this.image_layout_2_mobile,
                image_web: this.selectLayoutBanner === '1' ? '' : this.image_layout_2,
                name: this.selectLayoutBanner === '1' || this.selectIDBanner2.name === undefined || this.selectIDBanner2.name === null || this.type_layout_banner === 'image' ? '' : this.selectIDBanner2.name,
                action_id: this.selectLayoutBanner === '1' || this.selectIDBanner2.id === undefined || this.selectIDBanner2.id === null || this.type_layout_banner === 'image' ? '' : String(this.selectIDBanner2.id),
                action_type: this.selectLayoutBanner === '1' ? '' : this.type_layout_banner,
                link: this.type_layout_banner === 'link' ? this.LinkBanner2 : '',
                layout: '2'
              },
              {
                image: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' ? '' : this.image_layout_3_mobile,
                image_web: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' ? '' : this.image_layout_3,
                name: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' || this.selectIDBanner3.name === undefined || this.selectIDBanner3.name === null || this.type_layout_banner === 'image' ? '' : this.selectIDBanner3.name,
                action_id: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' || this.selectIDBanner3.id === undefined || this.selectIDBanner3.id === null || this.type_layout_banner === 'image' ? '' : String(this.selectIDBanner3.id),
                action_type: this.selectLayoutBanner === '1' || this.selectLayoutBanner === '2' ? '' : this.type_layout_banner,
                link: this.type_layout_banner === 'link' ? this.LinkBanner3 : '',
                layout: '3'
              },
              {
                image: this.selectLayoutBanner !== '4' ? '' : this.image_layout_4_mobile,
                image_web: this.selectLayoutBanner !== '4' ? '' : this.image_layout_4,
                name: this.selectLayoutBanner !== '4' || this.selectIDBanner4.name === undefined || this.selectIDBanner4.name === null || this.type_layout_banner === 'image' ? '' : this.selectIDBanner4.name,
                action_id: this.selectLayoutBanner !== '4' || this.selectIDBanner4.id === undefined || this.selectIDBanner4.id === null || this.type_layout_banner === 'image' ? '' : String(this.selectIDBanner4.id),
                action_type: this.selectLayoutBanner !== '4' ? '' : this.type_layout_banner,
                link: this.type_layout_banner === 'link' ? this.LinkBanner4 : '',
                layout: '4'
              }
            ],
            num_layout_banner: this.num_layout_banner,
            type_layout_banner: type
          }
        }
        this.$store.commit('openLoader')
        try {
          await this.$store.dispatch('actionEditBannerGroupShopV2', payload)
          var response = await this.$store.state.ModuleShop.stateEditBannerGroupShopV2
          if (response.code === 200) {
            this.$store.commit('closeLoader')
            if (status === 'delete') {
              this.dialogConfirmDelete = false
              await this.$swal.fire({ icon: 'success', text: 'ลบข้อมูลสำเร็จ', showConfirmButton: false, timer: 1500 })
            } else if (status === 'edit') {
              await this.$swal.fire({ icon: 'success', text: 'บันทึกข้อมูลสำเร็จ', showConfirmButton: false, timer: 1500 })
            }
            // console.log('response.data', response.data)
            this.removeImageLayoutBanner1()
            this.removeImageLayoutBanner2()
            this.removeImageLayoutBanner3()
            this.removeImageLayoutBanner4()
            await this.getDetailBannerGroupShopV2()
            if (this.dataType === 'custom' || this.dataType === 'default') {
              this.handleType('banner')
            }
          }
        } catch (error) {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
        }
      }
    }
  }
}
</script>

<style scoped>
.backgroundColorPage {
  background-color: transparent;
}
</style>
