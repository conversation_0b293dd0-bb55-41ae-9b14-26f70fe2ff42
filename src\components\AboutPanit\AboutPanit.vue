<template>
  <!-- <v-container> -->
  <div>
    <div style="background: #FFFFFF;">
      <v-row :style="{'padding': MobileSize || IpadSize ? '20px 20px 20px 20px': IpadProSize ? '20px 50px 20px 50px' :'20px 100px 20px 100px'}">
        <v-col cols="12">
        <v-breadcrumbs :items="items">
          <template v-slot:divider>
            <v-icon>mdi-chevron-right</v-icon>
          </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
              <span :style="{color: item.disabled === true ? '#27AB9C' : '#636363','font-size': '16px'}">{{ item.text }}</span>
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
        <v-divider class="mt-1"></v-divider>
        </v-col>
        <v-col cols="12">
          <v-row justify="center" align="center">
            <v-col :cols="MobileSize || IpadSize ? 12 : 6" justify="center">
              <v-img src="@/assets/emptypo.png" contain></v-img>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 6">
              <v-row dense justify="center">
                <v-col cols="12" :class="MobileSize ? 'pt-1' : 'pt-2'">
                  <!-- <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/4.png" contain height="40px" width="40px"></v-img> -->
                  <span style="font-weight: bold; color: teal" :style="{ 'font-size': MobileSize ? '18px' : '24px'}">เกี่ยวกับระบบ Panit และ วิธีการสมัคร</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'pt-1' : 'pt-4'">
                  <span :style="{ 'font-size': MobileSize ? '16px' : '18px'}">ระบบ Panit เป็นระบบจัดซื้อจัดจ้างออนไลน์ ในระบบ Panit จะมีการสมัครเข้าระบบสำหรับผู้ใช้งาน 2 ประเภท</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'pt-1' : 'pt-4'">
                  <span :style="{ 'font-size': MobileSize ? '16px' : '18px'}">
                    1. ผู้ที่ต้องการจะทำการขายของ เช่น โรงงานที่ทำการจัดส่ง และ ผู้ค้าขายส่ง จะถูกเรียกว่า ผู้ค้าองค์กร จะสามารถทำการลงสินค้าต่างๆ ในระบบ Panit เพื่อให้ผู้ใช้งานต่างๆ สามารถทำการจัดซื้อจัดจ้างได้
                  </span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'pt-1' : 'pt-4'">
                  <span :style="{ 'font-size': MobileSize ? '16px' : '18px'}">
                    2. ผู้ที่ต้องการจะทำการสั่งซื้อ เช่น ร้านค้าปลีก หรือ องค์กรที่ต้องการจัดซื้อสินค้าจากผู้ค้าองค์กร จะถูกเรียกว่า ผู้ซื้อองค์กร ซึ่ง ผู้ซื้อองค์กร จะอยู่ภายใต้ ผู้ค้าองค์กร เช่น ร้านขายยา และ คลินิกต่างๆ จะอยู่ภายใต้ โรงงานผลิตเภสัชภัณฑ์ เป็นต้น ในกรณีนี้ ผู้ซื้อองค์กรแต่ละรายที่อยู่ภายใต้ผู้ค้าองค์กร สามารถได้รับสิทธิพิเศษบางอย่างจากผู้ค้าองค์กรได้ เช่น สามารถซื้อสินค้าบางอย่างในราคาพิเศษได้
                  </span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'pt-1' : 'pt-4'">
                  <span :style="{ 'font-size': MobileSize ? '16px' : '18px'}">
                    Panit เป็นระบบทีเชื่อมต่อกับ One ID, บริษัท ขนส่ง Mobilyst, และ Thai Dotcom Payment ดังนั้น ในการสมัครระบบ Panit ทางผู้สมัครจะต้องเตรียมข้อมูลและเอกสารตามที่ระบุด้านล่าง ซึ่งข้อมูลและเอกสารจะถูกตรวจสอบโดย One ID, บริษัท ขนส่ง Mobilyst, และ Thai Dotcom Payment เพื่อทำการเปิดบัญชี
                  </span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </div>
    <v-div :style="{'background': '#FFFFFF', 'margin-top': MobileSize || IpadSize || IpadProSize ? '60px' : '110px'}">
      <v-row :style="{'padding': MobileSize || IpadSize ? '20px 20px 20px 20px': IpadProSize ? '20px 50px 20px 50px' : '20px 100px 20px 100px'}" justify="center">
        <v-col cols="12" lg="12" sm="12">
          <v-timeline align-top :dense="$vuetify.breakpoint.smAndDown" :size="$timeline-dot-large-size">
            <v-timeline-item v-for="(step, i) in steps" :key="i" :color="step.color" :large="i === 0 || i === 3 || i === 4" :small="i === 1 || i === 2" :icon="step.icon" fill-dot>
              <div>
                  <b v-if="i === 0 || i === 3 || i === 4" :style="{'font-size': MobileSize ? '18px' : IpadProSize || IpadSize ? '22px' : '24px'}" :class="`font-weight-blod mb-4 ${step.color}--text`">{{step.title}}</b>
                  <b v-if="i === 1" style="color: #27AB9C;" :style="{'font-size': MobileSize ? '16px' : IpadProSize || IpadSize ? '18px' : '20px'}"><img src="@/assets/ImageINET-Marketplace/ICONShop/loudspeaker.png" contain height="40" width="40" style="margin-right: 2px;"/>{{step.title}}</b>
                  <b v-if="i === 2" style="color: #27AB9C;" :style="{'font-size': MobileSize ? '16px' : IpadProSize || IpadSize ? '18px' : '20px'}"><img src="@/assets/ImageINET-Marketplace/ICONShop/loudspeaker.png" contain height="40" width="40" style="margin-right: 2px;"/>{{step.title}}</b>
                  <v-div v-if="i === 0">
                  <v-row class="d-flex d-flex" :class="MobileSize || IpadProSize || IpadSize ? '' : 'mt-3'">
                    <v-col cols="12">
                    </v-col>
                  </v-row>
                </v-div>
                <v-div v-if="i === 1">
                  <v-row class="d-flex d-flex" :class="MobileSize || IpadProSize || IpadSize ? '' : 'mt-3'">
                    <v-col cols="12">
                      <p class="sub-title-about mt-6" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '20px'}">เอกสาร PDF ที่จำเป็นต้องมี</p>
                      <span class="about-text" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">
                        <ul>
                          <li class="py-2">หนังสือรับรองบริษัท จากกรมพัฒนาธุรกิจการค้า (DBD) อายุการรับรองไม่เกิน 6 เดือน จำนวน 1 ฉบับ</li>
                          <li class="py-2">แบบ บอจ. 5 สำเนาบัญชีรายชื่อจำนวนผู้ถือหุ้น จำนวน 1 ฉบับ</li>
                          <li class="py-2">สำเนาบัตรประชาชนผู้ถือหุ้นตั้งแต่ 25% ขึ้นไป (สำหรับชาวต่างชาติ ใช้พาสปอร์ต)</li>
                          <li class="py-2">แบบ บอจ. 3 รายการจดทะเบียนจัดตั้ง จำนวน 1 ฉบับ</li>
                          <li class="py-2">สำเนาบัตรประชาชนคณะกรรมการผู้มีอำนาจลงนาม จำนวน 1 ฉบับ</li>
                          <li class="py-2">แบบ บอจ. 3 รายการจดทะเบียนจัดตั้ง จำนวน 1 ฉบับ</li>
                          <li class="py-2">สำเนาหน้าแรกของสมุดบัญชีธนาคารที่แจ้งเพื่อรับเงินจาก INET จำนวน 1 ฉบับ (โดยบัญชีจะต้องเป็นของธนาคารแห่งประเทศไทย และชื่อบัญชีจะต้องตรงกับชื่อที่ระบุบนหนังสือรับรองบริษัทตามสัญญาฉบับนี้)</li>
                        </ul>
                      </span>
                      <p class="sub-title-about mt-6" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '20px'}">เอกสาร PDF อื่นๆ ที่จำเป็น ถ้ามี</p>
                      <span class="about-text" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">
                        <ul>
                          <li class="py-2">ใบทะเบียนภาษีมูลค่าเพิ่ม ภ.พ. 20 (ถ้ามี) จำนวน 1 ฉบับ</li>
                          <li class="py-2">สำเนาบัตรประชาชนผู้รับมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</li>
                          <li class="py-2">หนังสือมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</li>
                        </ul>
                      </span>
                      <p class="sub-title-about mt-6" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '20px'}">สำหรับชาวต่างชาติ</p>
                      <span class="about-text" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">
                        <ul>
                          <li>ใช้พาสปอร์ต ใบอนุญาติทำงานในไทย และหลักฐานยืนยันที่อยู่ จำนวน 1 ฉบับ (ทะเบียนบ้าน ท.ร. 13 (เล่มสีเหลือง) หรือ ใบแจ้งยอดบัตรเครดิต หรือใบแจ้งอดสาธารณูปโภค ซึ่งระบุชื่อ และที่อยู่ปัจจุบัน)</li>
                        </ul>
                      </span>
                    </v-col>
                  </v-row>
                </v-div>
                <v-div v-if="i === 2">
                  <v-row class="d-flex d-flex" :class="MobileSize || IpadProSize || IpadSize ? '' : 'mt-3'">
                    <v-col cols="12">
                      <p class="sub-title-about mt-6" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '20px'}">เอกสาร PDF ที่จำเป็นต้องมี</p>
                      <span class="about-text" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">
                        <ul>
                          <li>หนังสือรับรองบริษัท จากกรมพัฒนาธุรกิจการค้า (DBD) อายุการรับรองไม่เกิน 6 เดือน จำนวน 1 ฉบับ</li>
                        </ul>
                      </span>
                      <p class="sub-title-about mt-6" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '20px'}">เอกสาร PDF อื่นๆ ที่จำเป็น ถ้ามี</p>
                      <span class="about-text" :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">
                        <ul>
                          <li class="py-2">สำเนาบัตรประชาชนผู้รับมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</li>
                          <li>หนังสือมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</li>
                        </ul>
                      </span>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col class="note-title">
                      <span :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}"><span style="color: red; font-weight: bold;">***</span> ในการส่งเอกสาร ให้สร้าง Folder ใน Google Drive ของคุณ และ upload PDF ต่างๆ เข้าไปยัง Folder ของ Google Drive ซึ่งจะถูกใช้ในขั้นตอนถัดไป</span>
                    </v-col>
                  </v-row>
                </v-div>
              </div>
              <v-div v-if="i === 3">
                  <v-row class="d-flex d-flex" :class="MobileSize || IpadProSize || IpadSize ? '' : 'mt-3'">
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <span :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">สำหรับ <b style="color: #27AB9C;">ผู้ค้าองค์กร</b> ให้เข้ามากรอกข้อมูลที่  <img src="@/assets/ImageINET-Marketplace/ICONRegister/6.png" contain height="20" width="20" @click="OpenFormPurchaser" style="cursor: pointer;"/><v-chip color="#E6F5F3" class="px-2 ml-2" style="cursor: pointer;" @click="OpenFormPurchaser" small><span style="color: #27AB9C; font-weight: 400; font-size: 14px;">คลิก</span></v-chip></span><br><br>
                      <span :style="{'font-size': MobileSize || IpadProSize || IpadSize ? '16px' : '18px'}">สำหรับ <b style="color: #4CAF50;">ผู้ซื้อองค์กร</b> ให้เข้ามากรอกข้อมูลที่  <img src="@/assets/ImageINET-Marketplace/ICONRegister/6.png" contain height="20" width="20" @click="OpenFormBuyer" style="cursor: pointer;"/><v-chip color="#E8F5E9" class="px-2 ml-2" style="cursor: pointer;" @click="OpenFormBuyer" small><span style="color: #4CAF50; font-weight: 400; font-size: 14px;">คลิก</span></v-chip></span>
                    </v-col>
                  </v-row>
                </v-div>
                <v-div v-if="i === 4">
                  <v-row class="d-flex d-flex" :class="MobileSize || IpadProSize || IpadSize ? '' : 'mt-3'">
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <span :style="{ 'font-size': MobileSize ? '16px' : '18px'}">
                        โปรดทำการรอ อย่างน้อย 1-3 วันทำการ ซึ่งมีเจ้าหน้าของ One ID, บริษัท ขนส่ง Mobilyst, และ Thai Dotcom Payment ทำการดำเนินเอกสาร และจะมีเจ้าหน้าที่มาขอ Request Access เข้าไปใน Folder ของ Google Drive ของท่านที่กล่าวมาใน ขั้นตอนที่ 1
                      </span>
                    </v-col>
                  </v-row>
                </v-div>
            </v-timeline-item>
          </v-timeline>
        </v-col>
      </v-row>
    </v-div>
  </div>
  <!-- </v-container> -->
</template>

<script>
export default {
  data () {
    return {
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'เกี่ยวกับระบบ Panit และ วิธีการสมัคร',
          disabled: true,
          href: ''
        }
      ],
      steps: [
        {
          color: 'teal',
          step: '1',
          icon: 'mdi-file-document-multiple-outline',
          title: 'ขั้นตอนที่ 1 เตรียมเอกสาร PDF'
        },
        {
          color: 'teal',
          step: '2',
          icon: '',
          title: 'สำหรับผู้ค้าองค์กร'
        },
        {
          color: 'teal',
          step: '3',
          icon: '',
          title: 'สำหรับผู้ซื้อองค์กร'
        },
        {
          color: 'green',
          step: '4',
          icon: 'mdi-email',
          title: 'ขั้นตอนที่ 2 การส่งข้อมูล'
        },
        {
          color: 'amber',
          step: '5',
          icon: 'mdi-timer-sand',
          title: 'ขั้นตอนที่ 3 รอการดำเนินเอกสาร'
        }
      ]
    }
  },
  computed: {
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  methods: {
    OpenFormPurchaser () {
      window.open('https://forms.gle/6PJ7Vv6RGt15V4V16')
    },
    OpenFormBuyer () {
      window.open('https://forms.gle/mpnoqLWMdQpoCdiMA')
    }
  }
}
</script>

<style scoped>
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 0px 0px 0px 0px !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C;
}
.container {
  max-width: 1400px !important;
}
.v-btn-toggle--group > .v-btn.v-btn {
  background-color: transparent !important;
  border-color: transparent;
  margin: 0px !important;
  min-width: auto;
}
.sub-title-about {
  color: #27AB9C;
  background-color: #E6F5F3;
  border-radius: 20px;
  padding: 0px 12px 0px 12px;
  width: max-content;
}
.sub-title-about-buyer {
  color: #4CAF50;
  background-color: #E8F5E9;
  border-radius: 20px;
  padding: 0px 12px 0px 12px;
  width: max-content;
}
.note-title {
  background-color: #FCECDB;
  border-radius: 20px;
  width: max-content;
}
.about-text {
  word-break: break-word;
}
</style>
