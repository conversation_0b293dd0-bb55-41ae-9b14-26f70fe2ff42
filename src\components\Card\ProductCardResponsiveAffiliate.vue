<template>
  <v-hover
    v-slot="{ hover }"
  >
    <!-- <v-card outlined class="rounded-lg px-2 custom-card pt-2"  :href="pathProductDetail" height="100%" width="188" :elevation="hover ? 8 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" @click="DetailProduct(itemProduct)"> -->
    <v-card outlined class="rounded-lg px-2 custom-card pt-2"  :href="itemProduct.link ? itemProduct.link : pathProductDetail" height="100%" width="210px" :elevation="hover ? 8 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;">
      <v-row dense>
      </v-row>
      <v-col cols="12" md="12" class="px-0 pb-0">
        <v-img
        v-lazyload
        :gradient="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock') ? '#33333373, #33333373' : ''"
        :src="itemProduct.images_URL[0]"
        loading='lazy'
        height="115"
        width="100%"
        style="border-radius: 8px;"
        contain
        v-if="itemProduct.images_URL && itemProduct.images_URL.length !== 0"
        class="align-start"
        >
        <v-chip v-if="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock')" color="#33333380" text-color="white" style="position:absolute; z-index:3; top:38%; left:25.5%">สินค้าหมด</v-chip>
          <!-- <v-chip
          v-if="itemProduct.stock_count === 0"
          class="ma-2"
          text-color="#D1392B"
          color="rgba(255, 255, 255)"
          small
          >
            <v-avatar
              left
              color="#D1392B"
              size="10"
            >
              <v-icon small color="white">mdi-close</v-icon>
            </v-avatar>
            สินค้าหมด
          </v-chip> -->
        </v-img>
        <v-img v-lazyload
        :gradient="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock') ? '#33333373, #33333373' : ''"
        src="@/assets/NoImage.png"
        height="115"
        width="100%"
        style="border-radius: 8px;"
        v-else>
        <v-chip v-if="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock')" color="#33333380" text-color="white" style="position:absolute; z-index:3; top:38%; left:25.5%">สินค้าหมด</v-chip>
          <!-- <v-chip
          v-if="itemProduct.stock_count === 0 || itemProduct.stock_status === 'out of stock'"
          class="ma-2"
          text-color="#D1392B"
          color="rgba(255, 255, 255)"
          small
          >
            <v-avatar
              left
              color="#D1392B"
              size="10"
            >
              <v-icon small color="white">mdi-close</v-icon>
            </v-avatar>
            สินค้าหมด
          </v-chip> -->
          <!-- <v-row  dense>
            <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
              <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
              <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
              <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
              <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
              <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
              <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
              <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
              <v-img src="@/assets/Tag/Event1.png" height="70" width="55" contain style="margin-left: 10px; margin-top: -20px;" v-else-if="itemProduct.message_status === 'e-receipt'"></v-img>
            </v-col> -->
            <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && !IpadSize && MobileSize">
              <v-img src="@/assets/icons/Discount.png" height="45" width="45" contain style="margin-left: 30%;">
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
              </v-img>
            </v-col>
            <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && IpadSize && !MobileSize">
              <v-img src="@/assets/icons/Discount.png" height="45" width="45" contain style="margin-left: 30%;">
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
              </v-img>
            </v-col>
            <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize">
              <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span><br/>
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span><br/>
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;">ลด</span>
              </v-img>
            </v-col> -->
          <!-- </v-row> -->
        </v-img>
      </v-col>
      <!-- <p class="mb-0 mt-1 px-0 textSKUCardMobile">{{ itemProduct.sku }}</p> -->
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <!-- <p v-bind="attrs" v-on="on" style="font-size: 12px; font-weight: bold; line-height: 16px; color: #333333; height: 15px;" class="mb-0 mt-1 px-2">{{ itemProduct.name }}</p> -->
          <!-- <p v-bind="attrs" v-on="on" class="mb-0 mt-3 px-0 textProductMobile">{{ itemProduct.name }}</p> -->
          <!-- <p v-bind="attrs" v-on="on" style="font-size: 12px; font-weight: bold; line-height: 16px; color: #333333; height: 15px;" class="mb-0 mt-1 px-2" v-snip="1">{{ itemProduct.name }}</p> -->
           <v-col cols="12" md="12" class="px-0 pb-0">
            <v-row no-gutters>
              <v-col cols="12" md="10">
                <h1 v-bind="attrs" v-on="on" class="mb-0 text-truncate" style="max-width: 100vw; font-size: 16px; font-weight: 700;">{{ itemProduct.name }}</h1>
              </v-col>
            </v-row>
          </v-col>
        </template>
        <span>{{ itemProduct.name }}</span>
      </v-tooltip>
       <v-row no-gutters :class="itemProduct.short_description === null || itemProduct.short_description === ''? 'mb-0': 'mb-0'">
        <p v-if="itemProduct.short_description === null || itemProduct.short_description === ''" class="text-truncate mb-0" :style="IpadProSize? 'font-size: 10px; font-weight: 400; max-width: 160px; color:transparent':'font-size: 12px; font-weight: 400; max-width: 160px; color:transparent'"></p><br v-if="itemProduct.short_description === null || itemProduct.short_description === ''"/>
        <p v-else class="text-truncate mb-0" style="font-size: 12px; font-weight: 400; color: #9A9A9A; max-width: 160px;">{{ itemProduct.short_description }}</p>
      </v-row>
      <v-col cols="12" class="pa-0" :class="MobileSize || IpadSize ? 'pb-1' : 'pb-4'">
        <v-chip x-small v-if="itemProduct.fda_number !== null && itemProduct.fda_number !== '' && itemProduct.fda_number !== undefined" color="#F3F5F7" class="square-chip" text-color="#636363"><v-img style="border-radius: 999px;" max-height="14px" max-width="14px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">{{ $t('Affiliate.Product.FDAApproved') }}</span></v-chip>
        <p v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; max-width: 132px; color:transparent">-</p>
      </v-col>
      <v-card-text class="pt-6 px-0" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          {{ $t('Affiliate.Product.ContactSupport') }}
        </span>
      </v-card-text>
      <v-card-text class="pt-6 px-0" v-if="!MobileSize && !IpadSize && IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;" >฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p>
          </div>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 10px;">
          {{ $t('Affiliate.Product.ContactSupport') }}
        </span>
      </v-card-text>
      <v-card-text class="pt-6 px-0" v-else-if="!MobileSize && IpadSize && !IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;" >฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p>
          </div>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
              <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 10px;">
          {{ $t('Affiliate.Product.ContactSupport') }}
        </span>
      </v-card-text>
      <v-card-text class="pt-6 px-0" v-else-if="MobileSize && !IpadSize && !IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;" >฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;">{{ $t('Affiliate.Product.Sold') }} {{itemProduct.sold | formatNumber }} {{ $t('Affiliate.Product.Unit') }}</p>
            <!-- <p v-else class="pr-1 mb-0" style="font-size: 12px; font-weight: 400; margin-top: 1px;"></p> -->
          </div>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          {{ $t('Affiliate.Product.ContactSupport') }}
        </span>
      </v-card-text>
      <v-card-actions class="pa-0 mb-2">
        <v-row dense>
          <v-col cols="12">
            <span style="color: #27AB9C; font-size: 13px;">{{ $t('Affiliate.Product.CommissionRate') }} {{itemProduct.commission_rate}} %</span>
          </v-col>
        <v-col cols="12">
          <v-row justify="space-between" align="center">
            <v-col cols="auto">
              <v-checkbox v-model="itemProduct.selected" @click="selectProductAffiliate(itemProduct.selected)" class="mt-0"></v-checkbox>
            </v-col>
            <div v-if="!MobileSize && !IpadSize && !IpadProSize">
              <v-col cols="auto">
                <v-btn small class="white--text" color="#27AB9C" @click="selectProductURL()" style="text-transform: none;">{{ $t('Affiliate.Product.GetLink') }}</v-btn>
              </v-col>
            </div>
            <div v-if="!MobileSize && !IpadSize && IpadProSize">
              <v-col cols="auto">
                <v-btn  class="white--text" color="#27AB9C" @click="selectProductURL()" style="text-transform: none;">{{ $t('Affiliate.Product.GetLink') }}</v-btn>
              </v-col>
            </div>
            <div v-if="!MobileSize && IpadSize && !IpadProSize">
              <v-col cols="auto">
                <v-btn  class="white--text" color="#27AB9C" @click="selectProductURL()" style="text-transform: none;">{{ $t('Affiliate.Product.GetLink') }}</v-btn>
              </v-col>
            </div>
            <div v-if="MobileSize && !IpadSize && !IpadProSize">
              <v-col cols="auto">
                <v-btn small class="white--text" color="#27AB9C" @click="selectProductURL()" style="text-transform: none;">{{ $t('Affiliate.Product.GetLink') }}</v-btn>
              </v-col>
            </div>
          </v-row>
        </v-col>
        </v-row>
      </v-card-actions>
        <v-dialog persistent v-model="showDialog" max-width="600px">
          <v-card>
            <v-card-title class="headline">{{ $t('Affiliate.Product.ProductOfferLink') }}</v-card-title>
            <v-card-text>
              <v-radio-group hidden v-model="subType" row>
                <v-radio label="มาตรฐาน" value="standard"></v-radio>
                <v-radio label="ขั้นกว่า" value="special"></v-radio>
              </v-radio-group>
              <div v-if="subType === 'special'">
                <v-text-field name="Sub_id 1" label="Sub_id 1" placeholder="ตัวอย่าง: SportShoes" v-model="sub_id_1"></v-text-field>
                <v-text-field name="Sub_id 2" label="Sub_id 2" placeholder="ตัวอย่าง: InstagramFeed" v-model="sub_id_2"></v-text-field>
                <v-text-field name="Sub_id 3" label="Sub_id 3" placeholder="ตัวอย่าง: 1212BirthdaySale" v-model="sub_id_3"></v-text-field>
                <v-text-field name="Sub_id 4" label="Sub_id 4" v-model="sub_id_4"></v-text-field>
                <v-text-field name="Sub_id 5" label="Sub_id 5" v-model="sub_id_5"></v-text-field>
                <p>หมายเหตุ: คุณสามารถเพิ่มพารามิเตอร์เพิ่มเติมเพื่อติดตามประสิทธิภาพลิ้งค์ของคุณโดยการติดแท็ก Sub_Id คลิก "เพิ่มการเชื่อมโยง" ต่อท้ายพารามิเตอร์ที่จะเชื่อมโยงของคุณ ค่าตัวเลข (a-z,A-Z, 0-9)</p>
                <v-btn class="white--text" color="#27AB9C" @click="addSubId()">เพิ่มการเชื่อมโยง</v-btn>
              </div>
              <v-text-field
                :label="$t('Affiliate.Product.PleaseCopyShortLink')"
                v-model="link"
                readonly>
              </v-text-field>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="primary" @click="copyLink()" style="text-transform: none;">{{ $t('Affiliate.Product.CopyLink') }}</v-btn>
              <v-btn text @click="showDialog = false" style="text-transform: none;">{{ $t('Affiliate.Product.Close') }}</v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
    </v-card>
  </v-hover>
</template>

<script>
import { Decode } from '@/services'
import Vue from 'vue'
export default {
  props: ['itemProduct', 'selectAll'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discription: 'หมวกนิรภัยป้องกันอุบัติหมวกนิรภัยป้องกันอุบัติ',
      rating: 5,
      favorite: false,
      priceSame: false,
      oneData: [],
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN,
      productID: '',
      namesPath: '',
      roleUser: '',
      productImage: '',
      selected: this.itemProduct.selected,
      showDialog: false,
      product_id: '',
      seller_shop_id: '',
      url: '',
      subType: 'standard',
      sub_id_1: '',
      sub_id_2: '',
      sub_id_3: '',
      sub_id_4: '',
      sub_id_5: '',
      link: ''
    }
  },
  async created () {
    await this.$EventBus.$emit('clearAllProductAffiliate')
    await this.$EventBus.$emit('clearAllProductShowProductSellerJoinAffiliate')
    this.$EventBus.$on('checkRoleCardRes', this.checkRoleCardRes)
    if (localStorage.getItem('roleUser') !== null) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    } else {
      this.roleUser = {
        role: 'ext_buyer'
      }
    }
    this.productImage = this.itemProduct.images_URL[0]
    this.formatSold()
    if (this.itemProduct !== undefined) {
      if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
        if (this.itemProduct.link) {
          // console.log('tt1')
          this.pathProductDetail = this.itemProduct.link
        } else {
          // console.log('els')
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.id)
        }
      } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
        if (this.itemProduct.link) {
          this.pathProductDetail = this.itemProduct.link
        } else {
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.product_id)
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    // selectAll (newVal) {
    //   this.selected = newVal
    // }
  },
  mounted () {
    this.$EventBus.$on('clearSelectedProductsResponsive', this.clearSelectedProductsResponsive)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('clearSelectedProductsResponsive')
    })
  },
  methods: {
    formatSold () {
      var k = this.$t('Affiliate.Product.K')
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + k
        }
        return value.toString()
      })
    },
    checkRoleCardRes () {
      this.roleUser = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        this.roleUser = {
          role: 'ext_buyer'
        }
      }
    },
    DetailProduct (val) {
      if (val !== 'no') {
        // console.log(val)
        const nameCleaned = val.name.replace(/\s/g, '-')
        if (val.id !== undefined && val.id !== '') {
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } }).catch(() => {})
        } else {
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.product_id}` } }).catch(() => {})
        }
        // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } })
        // window.location.assign(routeData.href, '_blank')
        // this.$router.push({ path: `${routeData.href}` }).catch(() => {})
      }
    },
    async selectProductURL () {
      this.$store.commit('openLoader')
      this.showDialog = true

      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id,
        product_id: this.itemProduct.id,
        seller_shop_id: this.itemProduct.seller_shop_id,
        url: this.itemProduct.productURL,
        sub_type: this.subType,
        sub_id_1: this.sub_id_1,
        sub_id_2: this.sub_id_2,
        sub_id_3: this.sub_id_3,
        sub_id_4: this.sub_id_4,
        sub_id_5: this.sub_id_5
      }

      // console.log('data', data)

      await this.$store.dispatch('actionsAffiliateGenerateShortUrl', data)
      const responseGenerateShortUrl = await this.$store.state.ModuleAffiliate.stateAffiliateGenerateShortUrl
      if (responseGenerateShortUrl.message === 'get short url success' || responseGenerateShortUrl.message === 'Generate short url success') {
        this.link = responseGenerateShortUrl.data
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.showDialog = false
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: `${this.$t('Affiliate.Product.CopyError')}` })
      }
    },
    addSubId () {
      this.subIds.push('')
    },
    copyLink () {
      navigator.clipboard.writeText(this.link).then(() => {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: this.$t('Affiliate.Product.CopySuccess')
        })
      })
    },
    async selectProductAffiliate (selected) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const selectProduct = {
        seller_shop_id: this.itemProduct.seller_shop_id,
        product_id: this.itemProduct.id,
        product_name: this.itemProduct.name,
        sku: this.itemProduct.sku,
        price: this.itemProduct.real_price,
        commission_rate: this.itemProduct.commission_rate,
        url: this.itemProduct.productURL
      }

      let dataDetail = []
      dataDetail = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate
      // const dataDetail = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliate
      // console.log('dataDetail', dataDetail)

      if (selected) {
        if (dataDetail.length === 0) {
          dataDetail.push({ product_list: [selectProduct] })
        } else {
          dataDetail[0].product_list.push(selectProduct)
        }
      } else {
        if (dataDetail.length > 0) {
          dataDetail[0].product_list = dataDetail[0].product_list.filter(product => product.product_id !== selectProduct.product_id)
        }
      }

      const formattedData = {
        user_id: onedata.user.user_id,
        sub_type: this.subType,
        sub_id_1: '',
        sub_id_2: '',
        sub_id_3: '',
        sub_id_4: '',
        sub_id_5: '',
        product_list: dataDetail.length > 0 ? dataDetail[0].product_list : []
      }

      this.formattedData = formattedData
      this.$store.commit('mutationSeletedProductAffiliateAll', this.formattedData)

      // var data = this.$store.state.ModuleAffiliate.stateSeletedProductAffiliateAll
      // console.log('dataDetail2', data)

      this.$EventBus.$emit('updateSelected', selected, this.itemProduct.id)
    },
    clearSelectedProductsResponsive () {
      this.$store.commit('mutationSeletedProductAffiliateAll', {
        user_id: '',
        sub_type: '',
        sub_id_1: '',
        sub_id_2: '',
        sub_id_3: '',
        sub_id_4: '',
        sub_id_5: '',
        product_list: []
      })
    }
  }
}
</script>

<style scoped>
.square-chip {
  padding: 1px 0px 0px 1px;
  width: 53%; /* กำหนดความกว้าง */
  height: 14px; /* กำหนดความสูง */
  font-size: 10px; /* ขนาดตัวอักษร */
}
/* .square-chipIPadPro {
  padding: 1px 0px 0px 1px;
  width: 66%;
  font-size: 10px;
}
.square-chipIPad {
  padding: 1px 0px 0px 1px;
  width: 68%;
  height: 14px;
  font-size: 10px;
} */
.custom-card {
  border: 1px solid #BDE7D9 !important; /* สีขอบของการ์ด */
  border-color: #BDE7D9 !important; /* สีขอบของการ์ดเมื่อไม่ได้โฮเวอร์ */
}
@media (max-width: 1366px) and (min-width: 1250px) {
  /* Media Query สำหรับ iPad Pro (1024px) */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 62%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
  .card {
    max-width: 19.5vw;
  }
}
@media (max-width: 1180px) and (min-width: 1025px) {
  /* Media Query สำหรับ iPad air แนวนอน */
  .square-chip {
    /* padding: 0px !important; */
    width: 52%;
    /* height: 14px;
    font-size: 10px; */
  }
  .card {
    max-width: 18vw;
  }
}
@media (max-width: 1250px) and (min-width: 1181px) {
  /* Media Query สำหรับ โน๊ตบุ๊คหน้าจอขนาดเล็ก */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 52%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
  .card {
    max-width: 16.5vw;
  }
}
</style>
