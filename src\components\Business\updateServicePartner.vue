<template>
  <v-container style="background: #FFFFFF;" :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333; display: flex; gap: .5vw;" v-if="!MobileSize"><v-icon class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>
        <span v-if="statusApprove === 'reject'">สินค้าบริการของฉัน</span>
        <span v-else-if="statusApprove === 'approve'">แก้ไขข้อมูลสินค้าบริการ</span>
        <v-chip color="#edf2f8" v-if="statusApprove === 'reject'"><span style="color: #1b5dd6; font-size: 16px;">รอแก้ไขข้อมูล</span></v-chip>
      </v-card-title>
      <v-card-title style="font-weight: 700; display: flex; gap: 1vw;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>
        <span v-if="statusApprove === 'reject'">สินค้าบริการของฉัน</span>
        <span v-else-if="statusApprove === 'approve'">แก้ไขข้อมูลสินค้าบริการ</span>
        <v-chip color="#edf2f8" v-if="statusApprove === 'reject'"><span style="color: #1b5dd6; font-size: 16px;">รอแก้ไขข้อมูล</span></v-chip>
      </v-card-title>
    </v-card>
    <v-container>
      <div style="background-color: #f7fffc; padding: 1vw;">
        <img class="mr-1" src="@/assets/Coorperation/iconInfoShop.png" width="20" height="20">
        <span style="font-size: 16px; font-weight: bold; color: #27AB9C">ข้อมูลสินค้า</span>
      </div>
      <v-row>
        <v-col cols="12" class="mt-3">
          <span style="font-size: medium">ชื่อสินค้า</span>
          <v-text-field
            v-model="detailPackageName"
            outlined
            placeholder="ระบุชื่อสินค้า"
            dense
            disabled
          ></v-text-field>
        </v-col>
        <v-col :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -2vw;'">
          <span style="font-size: medium">รายละเอียดสินค้า/คำอธิบายเพิ่มเติม</span>
          <ckeditor disabled placeholder="ระบุรายละเอียด Package" :editor="editor" :config="editorConfig" v-model="detailDescription" @ready="onEditorReady"></ckeditor>
          <!-- <v-textarea
            outlined
            v-model="detailDescription"
            disabled
          ></v-textarea> -->
        </v-col>
      </v-row>
      <div style="padding: 1vw;">
        <img class="mr-1" src="@/assets/Coorperation/iconProductList.png" width="24" height="24">
        <span style="font-size: 16px; font-weight: bold;">รายการสินค้า</span>
      </div>
      <div
        v-for="(item, index) in dataServices"
        :key="index"
      >
        <v-row style="background-color: #f3f5f7; padding: 1vw; margin: .5vw; border-radius: .5vw;">
          <v-col cols="12" class="d-flex align-center" style="justify-content: space-between;">
            <span style="font-weight: bold; color: #27AB9C; font-size: 16px;">รายการที่ {{ index + 1 }}</span>
            <v-btn color="success" text @click="removeItem(index, item.id)" :disabled="item.statusConnect === 'active'">
              <v-icon color="#27AB9C">mdi-trash-can-outline</v-icon>
              <span v-if="item.statusConnect === 'active'" style="text-decoration: underline; color: #777">ลบ</span>
              <span v-else style="text-decoration: underline; color: #27AB9C">ลบ</span>
            </v-btn>
          </v-col>
          <v-col cols="12" v-if="item.id !== null && item.statusPackage === 'reject'">
            <v-card class="pa-3" style="background-color: #fafafa;">
              <v-row>
                <v-col cols="12">
                  <span class="mr-2"><v-icon>mdi-calendar-month</v-icon> วันที่ส่งแก้ไขข้อมูล :</span>
                  <span style="font-weight: bold;">{{ item.remarkPackage.update_at }}</span>
                </v-col>
                <v-col cols="12">
                  <span class="mr-2"><v-icon>mdi-file-document-edit-outline</v-icon> หมายเหตุส่งแก้ไขข้อมูล :</span>
                  <span style="font-weight: bold;">{{ item.remarkPackage.remark }}</span>
                  <span style="font-weight: bold;"><v-btn v-if="item.remarkPackage.remark.length > 70" text color="#27AB9C" @click="openDialog(item.remark.remark)">ดูเพิ่มเติม</v-btn></span>
                  <!-- <span v-else></span> -->
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? 12 : 4">
            <span>ชื่อ Package <span style="color: #FF0000;">*</span></span>
            <v-select
              :items="availablePackages(index)"
              placeholder="ระบุชื่อ Package"
              outlined
              item-text="detail"
              item-value="package_code"
              dense
              v-model="item.selectPackage"
              :rules="Rules.selectPackage"
              :disabled="item.statusConnect === 'active'"
            ></v-select>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? 'margin-top: -9vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
            <span>ประเภทการชำระเงิน <span style="color: #FF0000;">*</span></span>
            <v-select
              :items="listTypePayment"
              placeholder="เลือกประเภทการชำระเงิน"
              outlined
              item-text="name"
              item-value="code"
              dense
              v-model="item.selectTypePayment"
              :rules="Rules.selectTypePayment"
              :disabled="item.statusConnect === 'active' || item.selectPackage === 'CUSTOM'"
            ></v-select>
          </v-col>
          <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? 'margin-top: -9vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
            <span>ราคาสินค้า (บาท) <span style="color: #FF0000;">*</span></span>
            <v-text-field
              v-model="item.productPrice"
              outlined
              placeholder="ระบุราคาสินค้า"
              dense
              :rules="Rules.productPrice"
              :disabled="item.statusConnect === 'active' || item.selectPackage === 'CUSTOM'"
              oninput="this.value = this.value.replace(/^[.]/, '').replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^0+--(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
            ></v-text-field>
          </v-col>
          <!-- part function -->
          <v-col cols="12" class="pa-3" :style="MobileSize ? 'margin-top: -2vw;' : IpadProSize || IpadSize ? 'margin-top: -4vw;' : 'margin-top: -2vw;'" v-if="item.selectPackage !== 'CUSTOM'">
            <v-card v-for="(itemFunc, indexFunc) in item.dataFunction" :key="indexFunc" class="pa-3 mt-3" style="background-color: #fafafa;">
              <v-row class="pa-2">
                <v-col cols="12" class="d-flex align-center">
                  <span style="font-weight: bold; font-size: 16px;">รายการฟังก์ชันที่ {{ indexFunc + 1 }} </span>
                  <v-spacer></v-spacer>
                  <v-btn color="success" text @click="removeItemFunc(index, indexFunc, itemFunc.idFunc)" :disabled="item.statusConnect === 'active'">
                    <v-icon color="#27AB9C">mdi-trash-can-outline</v-icon>
                    <span v-if="item.statusConnect === 'active'" style="text-decoration: underline; color: #777">ลบ</span>
                    <span v-else style="text-decoration: underline; color: #27AB9C">ลบ</span>
                  </v-btn>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? 'margin-top: -3vw;' : 'margin-top: .8vw;'">
                  <span>รายการฟังก์ชั่นของ Package <span style="color: #FF0000;">*</span></span>
                  <v-text-field
                    v-model="itemFunc.nameFunc"
                    outlined
                    placeholder="ระบุรายการฟังก์ชันของ Package"
                    dense
                    :disabled="item.statusConnect === 'active'"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : 2" :style="MobileSize ? 'margin-top: -3vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: .8vw;'">
                  <span>Unit type <span style="color: #FF0000;">*</span></span>
                  <v-text-field
                    v-model="itemFunc.unitType"
                    outlined
                    placeholder="ระบุหน่วย"
                    dense
                    :disabled="itemFunc.checkbox || item.statusConnect === 'active'"
                    :rules="!itemFunc.checkbox ? Rules.unitType : []"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : 6" :style="MobileSize ? 'margin-top: -9vw;' : IpadSize ? 'margin-top: -4vw;' : ''">
                  <div class="d-flex flex-column" v-if="MobileSize">
                    <span>จำนวน Transaction ของ Package <span style="color: #FF0000;">*</span></span>
                    <v-checkbox
                      v-model="itemFunc.checkbox"
                      size="small"
                      label="ไม่จำกัด"
                      style="font-size: small;"
                      class="chackbox"
                      :disabled="item.statusConnect === 'active'"
                    ></v-checkbox>
                  </div>
                  <div class="d-flex align-center" v-else>
                    <span>จำนวน Transaction ของ Package <span style="color: #FF0000;">*</span></span>
                    <v-spacer></v-spacer>
                    <v-checkbox
                      v-model="itemFunc.checkbox"
                      size="small"
                      label="ไม่จำกัด"
                      style="font-size: small;"
                      class="chackbox"
                      oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                      :disabled="item.statusConnect === 'active'"
                    ></v-checkbox>
                  </div>
                  <v-text-field
                    v-model="itemFunc.countTransaction"
                    outlined
                    placeholder="ระบุจำนวน Transaction"
                    dense
                    :disabled="itemFunc.checkbox || item.statusConnect === 'active'"
                    oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : 6" :style="MobileSize ? 'margin-top: -9vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -2vw;'">
                  <span>หน่วยราคาส่วนเกิน <span style="font-size: small; color: #989898;"> (ไม่จำเป็น)</span></span>
                  <v-select
                    :items="listTypeUnit"
                    placeholder="เลือกหน่วยราคาส่วนเกิน"
                    outlined
                    item-text="name"
                    item-value="code"
                    dense
                    v-model="itemFunc.selectTypeUnit"
                    :disabled="itemFunc.checkbox || item.statusConnect === 'active'"
                  ></v-select>
                </v-col>
                <v-col :cols="MobileSize || IpadSize ? 12 : 6" :style="MobileSize ? 'margin-top: -9vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -2vw;'">
                  <span>ราคา Transaction ส่วนเกิน <span style="font-size: small; color: #989898;"> (ไม่จำเป็น)</span></span>
                  <v-text-field
                    v-model="itemFunc.priceTransaction"
                    outlined
                    placeholder="ระบุจำนวน Transaction"
                    dense
                    :disabled="itemFunc.checkbox || item.statusConnect === 'active'"
                    :rules="Rules.priceTransaction"
                    oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                    hint="เป็นตัวเลขเท่านั้น"
                    persistent-hint
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col cols="12" class="d-flex justify-center" v-if="item.selectPackage !== 'CUSTOM'">
            <v-btn color="#27AB9C" text @click="addItemFunc(index)" :disabled="item.statusConnect === 'active'">
              <v-icon>mdi-plus-circle-outline</v-icon>
              <span style="text-decoration: underline;">เพิ่มรายการฟังก์ชัน</span>
            </v-btn>
          </v-col>
          <v-col cols="12" class="detail" :style="item.selectPackage === 'CUSTOM' ? 'margin-top: -2.5vw;' : ''">
            <span>รายละเอียด Package /คำอธิบายเพิ่มเติม <span style="font-size: small; color: #989898;"> (ไม่จำเป็น)</span></span>
            <div>
              <ckeditor @ready="onEditorReady" v-if="item.statusConnect === 'active'" disabled placeholder="ระบุรายละเอียด Package" :style="{ color: item.statusConnect === 'active' ? 'gray' : 'black', border: '1px #A0A0A0 solid' }" :rules="Rules.descriptionPackage" :editor="editor" :config="editorConfig" v-model="item.descriptionPackage"></ckeditor>
              <ckeditor v-else placeholder="ระบุรายละเอียด Package" :style="{ color: item.statusConnect === 'active' ? 'gray' : 'black', border: '1px #A0A0A0 solid' }" :rules="Rules.descriptionPackage" :editor="editor" :config="editorConfig" v-model="item.descriptionPackage"></ckeditor>
            </div>
          </v-col>
          <v-col cols="12" class="detail">
            <span>รายละเอียดเงื่อนไขการบริการ <span style="color: #FF0000;">*</span></span>
            <div>
              <ckeditor @ready="onEditorReady" v-if="item.statusConnect === 'active'" disabled placeholder="ระบุรายละเอียดเงื่อนไขบริการ" :style="{ color: item.statusConnect === 'active' ? 'gray' : 'black', border: '1px #A0A0A0 solid' }" :editor="editor" :config="editorConfig" v-model="item.descriptionCondition"></ckeditor>
              <ckeditor v-else placeholder="ระบุรายละเอียดเงื่อนไขบริการ" :style="{ color: item.statusConnect === 'active' ? 'gray' : 'black', border: '1px #A0A0A0 solid' }" :editor="editor" :config="editorConfig" v-model="item.descriptionCondition"></ckeditor>
              <span v-if="!item.descriptionCondition" style="color: red; font-size: small;">กรุณากรอกรายละเอียดเงื่อนไขการบริการ</span>
            </div>
          </v-col>
          <v-col :cols="MobileSize ? 12 : 6" v-if="item.selectPackage === 'CUSTOM'" :style="MobileSize ? 'margin-top: -3vw;' : ''">
            <span style="font-size: medium">เบอร์โทรศัพท์ <span style="color: #FF0000;">*</span></span>
            <v-text-field
              v-model="item.phone"
              outlined
              placeholder="ระบุเบอร์โทรศัพท์"
              dense
              :rules="Rules.phone"
              :disabled="item.statusConnect === 'active'"
              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').substring(0, 10)"
            ></v-text-field>
          </v-col>
          <v-col :cols="MobileSize ? 12 : 6" v-if="item.selectPackage === 'CUSTOM'" :style="MobileSize ? 'margin-top: -9vw;' : ''">
            <span style="font-size: medium">อีเมล <span style="color: #FF0000;">*</span></span>
            <v-text-field
              v-model="item.email"
              outlined
              placeholder="ระบุอีเมล"
              dense
              :rules="Rules.email"
              :disabled="item.statusConnect === 'active'"
              oninput="this.value = this.value.replace(/[^a-zA-Z_.@0-9\s]/g, '')"
            ></v-text-field>
          </v-col>
        </v-row>
      </div>
      <v-col cols="12" class="d-flex justify-center">
        <v-btn color="#27AB9C" text v-if="dataServices.length < 4" @click="addItem">
          <v-icon>mdi-plus-circle-outline</v-icon>
          <span style="text-decoration: underline;">เพิ่มรายการ</span>
        </v-btn>
      </v-col>
      <v-col class="d-flex" style="justify-content: space-between">
        <v-btn  @click="backtoPage()" color="#27AB9C" outlined style=" border-radius: 2vw;">ยกเลิก</v-btn>
        <v-spacer></v-spacer>
        <!-- <v-btn class="mr-3" :disabled="!isFormValid" @click="dialogPreviewPackage = true" outlined color="#27AB9C" style=" border-radius: 2vw;">
          <v-icon class="mr-2">mdi-eye</v-icon>
          <span>ดูตัวอย่าง</span>
        </v-btn> -->
        <v-btn :disabled="!isFormValid"  @click="dialogPreviewPackage = true" color="#27AB9C" style=" border-radius: 2vw; color: #fff;">บันทึก</v-btn>
      </v-col>
    </v-container>
    <!-- dialog preview package -->
    <v-dialog content-class="elevation-0" v-model="dialogPreviewPackage" :width="MobileSize ? '100%' : '60%'" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative; background-color: #27AB9C;"
        >
        <v-row>
          <v-col style="text-align: center;" class="pt-4">
            <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ตัวอย่างแสดงรายละเอียด Package</b></span>
          </v-col>
          <v-btn fab small @click="dialogPreviewPackage = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
        </v-row>
        </v-app-bar>

        <!-- ข้อมูลส่วนแรก รูป และ detail ต่าง ๆ -->
        <div class="pb-10" v-if="!MobileSize">
          <v-col class="px-5">
            <span style="font-size: large; font-weight: bold;">ข้อมูลร้านค้า Partner</span>
          </v-col>
          <v-card elevation="0">
            <v-row no-gutters>
              <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 340px;': MobileSize ? 'position: relative; height: 210px;' :IpadProSize?'position: relative; height: 460px;': 'position: relative; height: 519px;'">
                <v-carousel
                  :class="IpadSize ? 'pa-5' : 'pa-4'"
                  style="position: absolute; border-radius: 12px;"
                  :height=" IpadSize ? '220': IpadProSize? '290': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length !== 0 ? '110': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length === 0 ? '120':'380'"
                  hide-delimiters
                >
                  <v-carousel-item v-for="(image, index) in filteredBanners" :key="index">
                    <v-img
                      :src="image.media_path"
                      height="100%"
                      width="100%"
                      alt="Software Marketplace"
                      style="border-radius: 1vw !important;"
                    ></v-img>
                  </v-carousel-item>
                  <template v-if="filteredBanners.length === 0">
                    <v-carousel-item>
                      <v-img
                        src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                        height="100%"
                        width="100%"
                        contain
                      ></v-img>
                    </v-carousel-item>
                  </template>
                </v-carousel>
                <v-col :style="IpadSize ? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                  <v-row no-gutters>
                    <v-col dense no-gutters :cols="MobileSize? 5 : 5" :md="IpadProSize ? 5 : 4" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0'" style="text-align: center;">
                      <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-if="showpartnerdetail && showpartnerdetail.media && showpartnerdetail.media.find(image => image.image_type === 'main')">
                        <v-img
                          :src="showpartnerdetail.media.find(image => image.image_type === 'main').media_path"
                          contain
                          style="border: 5px solid #ffffff"
                        ></v-img>
                      </v-avatar>
                      <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-else>
                        <v-img
                          src="@/assets/NoImage.png"
                          style="border: 10px solid #ffffff"
                        ></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col :cols="MobileSize ? 7 : 7" :md="IpadProSize ? 7 : 8" :style="MobileSize ? 'padding-top: 35px' :IpadSize ? 'padding-top: 80px' : 'padding-top: 150px'">
                      <span :style="IpadSize ? 'font-size: medium;' : 'font-size: 24px;' "><b>{{ showpartnerdetail && showpartnerdetail.partner_name }}</b></span><br>
                      <span><b>{{ showpartnerdetail && showpartnerdetail.business_name_th }}</b></span><br>
                      <span :class="IpadSize || IpadProSize ? 'inline-flex' : 'inline-flex-desktop'">
                        <a>
                          <v-btn
                          color="#3b5998"
                          fab
                          x-small
                          dark
                          style="margin-top: -2px; box-shadow: none;">
                          <v-img width="0"
                            src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                          ></v-img>
                          </v-btn>
                        </a>
                        <a>
                          <v-btn
                          color="#39cd00"
                          fab
                          x-small
                          dark
                          style="margin-top: -2px; box-shadow: none;">
                            <v-img width="0"
                            src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                          ></v-img>
                        </v-btn>
                        </a>
                        <span>| {{ showpartnerdetail && showpartnerdetail.partner_phone_no }}</span>
                      </span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-col>
              <v-col cols="12" class="d-flex align-center px-5">
                <img
                  src="@/assets/Marketplace_partner/Capa_1.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>เกี่ยวกับ Partner</b></span>
              </v-col>
              <v-col cols="12" class="px-5 mt-3 d-flex flex-column">
                <span>{{ showpartnerdetail && showpartnerdetail.detail }}</span>
                <span>เว็บไซต์ : {{ showpartnerdetail && showpartnerdetail.url_name }}</span>
                <div class="d-flex align-center">
                  <span>ประเภทบริการ : </span>
                  <v-chip-group class="d-inline-flex" style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('ERP')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b>
                      <!-- style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" v-bind:style="{ padding: MobileSize ? '3px 6px' : '', fontSize: MobileSize ? '10px' : '' }"><b>ERP</b> -->
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Web Development')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b>
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('POS')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>POS</b>
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('OMS')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>OMS</b>
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Marketing')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b>
                    </v-chip>
                  </v-chip-group>
                </div>
              </v-col>
            </v-row>
          </v-card>
        </div>
        <div class="pb-10" v-if="MobileSize">
          <v-col class="px-5">
            <span style="font-size: large; font-weight: bold;">ข้อมูลร้านค้า Partner</span>
          </v-col>
          <v-card elevation="0">
            <v-row no-gutters>
              <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 340px;': MobileSize ? 'position: relative; height: 210px;' :IpadProSize?'position: relative; height: 460px;': 'position: relative; height: 519px;'">
                <v-carousel
                  style="position: absolute; border-radius: 12px; padding: 4vw;"
                  :height=" IpadSize ? '220': IpadProSize? '290': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length !== 0 ? '110': MobileSize && showpartnerdetail.media && showpartnerdetail.media.length === 0 ? '120':'380'"
                  hide-delimiters
                >
                  <v-carousel-item v-for="(image, index) in filteredBanners" :key="index">
                    <v-img
                      :src="image.media_path"
                      height="100%"
                      width="100%"
                      alt="Software Marketplace"
                      style="border-radius: 1vw !important;"
                    ></v-img>
                  </v-carousel-item>
                  <template v-if="filteredBanners.length === 0">
                    <v-carousel-item>
                      <v-img
                        src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                        height="100%"
                        width="100%"
                        contain
                      ></v-img>
                    </v-carousel-item>
                  </template>
                </v-carousel>
                <v-col :style="IpadSize ? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                  <v-row no-gutters>
                    <v-col dense no-gutters :cols="MobileSize? 5 : 5" :md="IpadProSize ? 5 : 4" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0'" style="text-align: center;">
                      <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-if="showpartnerdetail && showpartnerdetail.media && showpartnerdetail.media.find(image => image.image_type === 'main')">
                        <v-img
                          :src="showpartnerdetail.media.find(image => image.image_type === 'main').media_path"
                          contain
                          style="border: 5px solid #ffffff"
                        ></v-img>
                      </v-avatar>
                      <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-else>
                        <v-img
                          src="@/assets/NoImage.png"
                          style="border: 10px solid #ffffff"
                        ></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col :cols="MobileSize ? 7 : 7" :md="IpadProSize ? 7 : 8" :style="MobileSize ? 'padding-top: 43px' :IpadSize ? 'padding-top: 80px' : 'padding-top: 150px'">
                      <span :style="MobileSize ? 'font-size: small;' : 'font-size: small;' "><b>{{ showpartnerdetail && showpartnerdetail.partner_name }}</b></span><br>
                      <span style="font-size: small"><b>{{ showpartnerdetail && showpartnerdetail.business_name_th }}</b></span><br>
                      <span :class="IpadSize || IpadProSize ? 'inline-flex' : 'inline-flex-desktop'">
                        <a>
                          <v-btn
                          color="#3b5998"
                          fab
                          x-small
                          dark
                          style="margin-top: -2px; box-shadow: none;">
                          <v-img width="0"
                            src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                          ></v-img>
                          </v-btn>
                        </a>
                        <a>
                          <v-btn
                          color="#39cd00"
                          fab
                          x-small
                          dark
                          style="margin-top: -2px; box-shadow: none;">
                            <v-img width="0"
                            src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                          ></v-img>
                        </v-btn>
                        </a>
                        <span>| {{ showpartnerdetail && showpartnerdetail.partner_phone_no }}</span>
                      </span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-col>
              <v-col cols="12" class="d-flex align-center px-5 mt-5">
                <img
                  src="@/assets/Marketplace_partner/Capa_1.png"
                  alt="เกี่ยวกับ Partner"
                  width="25px"
                  class="mr-2"
                />
                <span><b>เกี่ยวกับ Partner</b></span>
              </v-col>
              <v-col cols="12" class="px-5 pt-3 d-flex flex-column">
                <span>{{ showpartnerdetail && showpartnerdetail.detail }}</span>
                <span class="mt-2">เว็บไซต์ : {{ showpartnerdetail && showpartnerdetail.url_name }}</span>
                <div style="display: flex; overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px; max-width: 100%;">
                  <span style="position: sticky; left: 0; background-color: white; flex-shrink: 0; z-index: 1; display: flex; align-items: center;">
                    ประเภทบริการ :
                  </span>
                  <v-chip-group class="d-inline-flex" style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('ERP')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b>
                      <!-- style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" v-bind:style="{ padding: MobileSize ? '3px 6px' : '', fontSize: MobileSize ? '10px' : '' }"><b>ERP</b> -->
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Web Development')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b>
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('POS')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>POS</b>
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('OMS')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>OMS</b>
                    </v-chip>
                    <v-chip
                      v-if="showpartnerdetail && showpartnerdetail.service_type && showpartnerdetail.service_type.includes('Marketing')"
                      label
                      class="ml-2"
                      style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b>
                    </v-chip>
                  </v-chip-group>
                </div>
              </v-col>
            </v-row>
          </v-card>
        </div>
        <v-col cols="12" class="px-5">
          <v-row style="margin-left: 0px; margin-right: 0px">
            <h1 style="font-size:24px; font-weight: 700; color:#27AB9C;">สินค้า</h1>
            <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px;"></v-spacer>
          </v-row>
        </v-col>
        <!-- สินค้า ชื่อ detail -->
        <v-col cols="12" class="text-center" style="font-size: 18px;">
          <span><b>{{ detailPackageName }}</b></span>
        </v-col>
        <v-col cols="12" class="text-center">
          <div class="scrollable-content-service-detail showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="detailDescription"></div>
        </v-col>
        <!-- รายการ package -->
        <div class="pb-10" v-if="!MobileSize">
          <v-col cols="12" class="d-flex justify-center" :style="IpadSize ? 'gap: 2vw; flex-wrap: wrap; height: 48vw; overflow-y: auto;' : IpadProSize ? 'gap: 2vw; flex-wrap: wrap; height: 37vw; overflow-y: auto;' : 'gap: 1vw;'">
            <v-card v-for="(item, index) in dataServices" :key="index" class="pa-3" :style="IpadSize ? 'border-radius: 1vw; width: 35vw;' : IpadProSize ? 'border-radius: 1vw; width: 25vw;' : 'border-radius: 1vw; width: 18vw;'">
              <v-card-text class="d-flex flex-column">
                <span style="color: #000;">package</span>
                <span v-if="item.selectPackage === 'PACK-S'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">S - Small</span>
                <span v-else-if="item.selectPackage === 'PACK-M'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">M - Medium</span>
                <span v-else-if="item.selectPackage === 'PACK-L'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">L - Large</span>
                <span v-else-if="item.selectPackage === 'CUSTOM'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">Custom</span>
              </v-card-text>
              <v-card-text style="color: #000; margin-top: -1vw;">
                <span v-if="item.selectTypePayment === 'เปอร์เซ็นต์'"><span class="font-weight-bold" style="font-size: x-large;">{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เปอร์เซ็นต์(%)</span></span>
                <span v-else-if="item.selectTypePayment === 'รายเดือน'"><span class="font-weight-bold" style="font-size: x-large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เดือน</span></span>
                <span v-else><span class="font-weight-bold" style="font-size: x-large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/ปี</span></span>
              </v-card-text>
              <span class="mb-5 pa-3">รายละเอียด package :</span>
              <v-card-text :style="IpadSize || IpadProSize ? 'height: 17vw; overflow-y: auto;' : 'height: 10vw; overflow-y: auto;'">
                <div class="mt-2" v-for="(itemFunc, indexFunc) in item.dataFunction" :key="indexFunc">
                  <span v-if="item.dataFunction.length !== 0 && item.dataFunction[0].nameFunc !== ''">
                    <v-icon class="mr-2" color="#52c41a">mdi-check-circle</v-icon>
                    <span>{{itemFunc.nameFunc}} - </span>
                    <span>{{itemFunc.countTransaction}} </span>
                    <span> {{itemFunc.unitType}}</span>
                  </span>
                  <!-- <span v-else>กำหนดรายละเอียดการใช้งานได้เอง</span> -->
                </div>
                <div class="mt-2">
                  <span v-if="item.selectPackage === 'CUSTOM'">กำหนดรายละเอียดการใช้งานได้เอง</span>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </div>
        <v-card style="border: none !important;" outlined v-else>
          <v-col style="height: 90vw; overflow-y: auto; margin-top: -2vw;">
            <v-card class="ml-1 mr-1 mb-5" v-for="(item, index) in dataServices" :key="index">
              <v-card-text class="d-flex flex-column">
                <span style="color: #000;">package</span>
                <span v-if="item.selectPackage === 'PACK-S'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">S - Small</span>
                <span v-else-if="item.selectPackage === 'PACK-M'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">M - Medium</span>
                <span v-else-if="item.selectPackage === 'PACK-L'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">L - Large</span>
                <span v-else-if="item.selectPackage === 'CUSTOM'" class="font-weight-bold" style="font-size: medium; color: #2f8bdc;">Custom</span>
              </v-card-text>
              <v-card-text style="color: #000; margin-top: -6vw;">
                <span v-if="item.selectTypePayment === 'เปอร์เซ็นต์'"><span class="font-weight-bold" style="font-size: large;">{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เปอร์เซ็นต์(%)</span></span>
                <span v-else-if="item.selectTypePayment === 'รายเดือน'"><span class="font-weight-bold" style="font-size: large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/เดือน</span></span>
                <span v-else><span class="font-weight-bold" style="font-size: large;">฿{{item.productPrice}}</span> <span style="font-size: medium; color: #6f6c8f">/ปี</span></span>
              </v-card-text>
              <span class="mb-5 pa-3">รายละเอียด package :</span>
              <v-card-text style="height: 45vw; overflow-y: auto;">
                <div class="mt-2" v-for="(itemFunc, indexFunc) in item.dataFunction" :key="indexFunc">
                  <span v-if="item.dataFunction.length !== 0 && item.dataFunction[0].nameFunc !== ''">
                    <v-icon class="mr-2" color="#52c41a">mdi-check-circle</v-icon>
                    <span>{{itemFunc.nameFunc}} : </span>
                    <span>{{itemFunc.countTransaction}} </span>
                    <span> {{itemFunc.unitType}}</span>
                  </span>
                  <!-- <span v-else>กำหนดรายละเอียดการใช้งานได้เอง</span> -->
                </div>
                <div class="mt-2">
                  <span v-if="item.selectPackage === 'CUSTOM'">กำหนดรายละเอียดการใช้งานได้เอง</span>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-card>
        <v-card-actions class="d-flex justify-center pa-5" style="gap: 1vw; margin-top: -1vw;">
        <!-- <v-spacer></v-spacer> -->
          <v-btn
            color="primary"
            outlined
            rounded
            width="8vw"
            height="40"
            @click="dialogPreviewPackage = false"
          >
            ยกเลิก
          </v-btn>
          <v-btn
            color="primary"
            width="8vw"
            height="40"
            rounded
            @click="openDialogUpdate"
          >
            ยืนยัน
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- dialog confirm step 1 -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="closeChooseAddPackage()"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/Coorperation/iconConfirmEdit1.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะแก้ไขข้อมูลสินค้าบริการใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeChooseAddPackage()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editPackage()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog content-class="elevation-0" v-model="dialogConfirmEdit" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="backtoPage()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;" v-if="!MobileSize">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขสินค้าเรียบร้อย</b></p>
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>กรุณารอเจ้าหน้าที่ตรวจสอบ</b></p>
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ภายใน 24 ชั่วโมง</b></p>
          </v-card-text>
          <v-card-text style="text-align: center;" v-else>
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขสินค้าเรียบร้อย</b></p>
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>กรุณารอเจ้าหน้าที่ตรวจสอบ</b></p>
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>ภายใน 24 ชั่วโมง</b></p>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="backtoPage()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- show more remark -->
    <v-dialog content-class="elevation-0" v-model="dialogShowMore" :width="MobileSize ? '100%' : IpadSize || IpadProSize ? '50%' : '40%'">
      <v-card style="border-radius: 1.5vw;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>หมายเหตุส่งแก้ไขข้อมูล</b></span>
            </v-col>
            <v-btn fab small @click="dialogShowMore = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>

        <v-card-text class="pt-4">
          <pre style="white-space: wrap" v-html="dataMore"></pre>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
import { Decode } from '@/services'
export default {
  data () {
    return {
      //   data list detail partner detail
      listService: [],
      product_name: '',
      description: '',
      listPackage: [],
      listPackageName: [],
      listDetailPackage: [],
      // listTypePayment: ['รายเดือน', 'รายปี', 'เปอร์เซ็นต์'],
      listTypePayment: ['รายเดือน', 'รายปี'],
      statusApprove: '',
      lengthPackage: [],
      //   v-model pull data
      detailPackageName: '',
      detailDescription: '',
      dataServices: [],
      dataFunction: [],
      listTypeUnit: ['บาท', 'เปอร์เซ็นต์'],
      //   rules ดักค่า
      editor: ClassicEditor,
      productPrice: '',
      Rules: {
        productName: [v => !!v || 'กรุณากรอกชื่อสินค้า'],
        description: [v => !!v || 'กรุณากรอกรายละเอียดสินค้า/คำอธิบายเพิ่มเติม'],
        selectPackage: [v => !!v || 'กรุณาเลือก Package'],
        selectTypePayment: [v => !!v || 'กรุณาเลือกประเภทการชำระเงิน'],
        productPrice: [
          v => !!v || 'กรุณากรอกราคาสินค้า',
          // v => /^[0-9]+$/.test(v) || 'กรุณากรอกตัวเลขเท่านั้น',
          v => v > 0 || 'กรุณากรอกราคาที่มากกว่า 0'
        ],
        descriptionPackage: [v => !!v || 'กรุณากรอกรายละเอียด Package'],
        descriptionCondition: [v => !!v || 'กรุณากรอกรายละเอียดเงื่อนไขการบริการ']
      },
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'imageUpload',
          '|',
          'blockquote',
          'inserttable',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        },
        item: {
          descriptionPackage: '',
          descriptionCondition: ''
        }
      },
      dialogAwaitConfirm: false,
      dialogConfirmEdit: false,
      dialogPreviewPackage: false,
      taxId: '',
      partnerCode: '',
      dialogShowMore: false,
      dataMore: '',
      detailNull: '-',
      checkbox: false,
      itemDelete: [],
      showpartnerdetail: []
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value !== null) {
        if (value.length > limit) {
          value = value.substring(0, (limit - 4)) + '...'
        }
        return value
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    isFormValid () {
      return this.dataServices.every((item) => {
        const isCustom = item.selectPackage === 'CUSTOM'

        return (
          item.selectPackage &&
          item.selectTypePayment &&
          item.productPrice &&
          item.descriptionCondition &&
          (!isCustom || (item.phone && item.email)) &&
          item.dataFunction.every((itemFunc) => {
            const isCheck = itemFunc.checkbox
            // console.log(isCheck, 7878)
            if (!isCustom) {
              if (isCheck) {
                if (!(itemFunc.nameFunc && itemFunc.countTransaction)) {
                  return false
                }
              } else if (!(itemFunc.nameFunc && itemFunc.unitType && itemFunc.countTransaction)) {
                return false
              }
            }
            // if (!isCustom && isCheck) {
            //   if (!(itemFunc.nameFunc && itemFunc.countTransaction)) {
            //     return false
            //   }
            // }
            return true
          })
        )
      })
    },
    truncatedDescription () {
      return this.description.length > 100
        ? this.description.substring(0, 100) + '...'
        : this.description
    },
    filteredBanners () {
      return Array.isArray(this.showpartnerdetail.media)
        ? this.showpartnerdetail.media.filter(image => image.image_type === 'banner')
        : []
    }
  },
  async created () {
    this.$store.commit('openLoader')
    await this.getTaxId()
    await this.getDetailPartner()
    await this.getPartnerCode()
    await this.getDetailPackage()
    await this.getPackage()
    this.$store.commit('closeLoader')
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/updateServicePartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/updateServicePartner' }).catch(() => {})
      }
    },
    dataServices: {
      handler (newVal) {
        // console.log(newVal, 'newVal 5555')
        newVal.forEach(service => {
          if (service.selectPackage === 'CUSTOM') {
            service.selectTypePayment = 'รายเดือน'
            service.productPrice = '>10000'
          } else {
            if (service.productPrice === '>10000') {
              service.productPrice = ''
            }
          }

          service.dataFunction.forEach(itemFunc => {
            if (itemFunc.checkbox) {
              itemFunc.countTransaction = 'ไม่จำกัด'
              itemFunc.selectTypeUnit = ''
              itemFunc.priceTransaction = ''
              itemFunc.unitType = ''
            } else {
              if (itemFunc.countTransaction === 'ไม่จำกัด') {
                itemFunc.countTransaction = ''
              }
            }
          })
        })
      },
      deep: true
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    },
    openDialogUpdate () {
      this.dialogPreviewPackage = false
      this.dialogAwaitConfirm = true
    },
    onEditorReady (editor) {
      editor.editing.view.change(writer => {
        writer.setStyle('color', '#a0a0a0', editor.editing.view.document.getRoot())
      })
    },
    openDialog (item) {
      this.dialogShowMore = true
      this.dataMore = item
    },
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/serviceProductPartner' }).catch(() => { })
      } else {
        this.$router.push({ path: '/serviceProductPartnerMobile' }).catch(() => { })
      }
    },
    async getTaxId () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          // console.log('see tax id', this.taxId)
          // this.taxId = response.data.array_business[0].owner_tax_id
        }
      }
    },
    // async getPartnerList () {
    //   var data = {
    //     id_card_num: this.taxId // ใช้ไอดี mock up อยู่
    //   }
    //   this.$store.commit('openLoader')
    //   await this.$store.dispatch('actionsGetPartnerList', data)
    //   var response = await this.$store.state.ModuleBusiness.stateGetPartnerList
    //   this.showpartnerdetail = response.data[0]
    //   if (response.code === 200 && response.data.length !== 0) {
    //     this.partnerName = this.showpartnerdetail.partner_name
    //     this.detailPackageName = this.partnerName
    //     this.detailPartner = this.showpartnerdetail.detail
    //     this.detailDescription = this.detailPartner
    //     if (this.detailDescription === null) {
    //       this.detailDescription = '-'
    //     }
    //     if (this.showpartnerdetail.new_address.length === 0) {
    //       this.address = response.data[0].address[0]
    //     } else {
    //       this.address = response.data[0].new_address[0]
    //     }
    //   }
    // },
    async getPartnerCode () {
      var data = {
        id_card_num: this.taxId
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        if (response.data.partner_code !== null) {
          this.havePartner = false
          this.partnerCode = response.data.partner_code
          // console.log('55555', this.partnerCode)
          this.$store.commit('closeLoader')
        } else {
          this.havePartner = true
        }
      }
      // this.$store.commit('closeLoader')
    },
    async getDetailPackage () {
      this.$store.commit('openLoader')
      var data = { partner_code: this.partnerCode }
      await this.$store.dispatch('actionsGetDetailPackage', data)
      var response = await this.$store.state.ModuleBusiness.stateGetDetailPackage
      if (response.code === 200) {
        this.listService = response.data[0]
        this.product_name = this.listService.partner_name
        this.lengthPackage = this.listService.list_package
        this.description = this.listService.detail
        this.listDetailPackage = this.listService.list_package
        this.statusApprove = this.listService.status
        this.listPackage = this.listService.list_package
        // add data
        this.detailPackageName = this.product_name
        this.detailDescription = this.description
        this.dataServices = this.listService.list_package.map(packageItem => ({
          id: packageItem.id,
          statusPackage: packageItem.status,
          remarkPackage: packageItem.remark,
          selectPackage: packageItem.package_code,
          statusConnect: packageItem.partner_connect,
          // selectTypePayment: packageItem.payment_type === 'Monthly' ? 'รายเดือน' : packageItem.payment_type === 'Yearly' ? 'รายปี' : 'เปอร์เซ็นต์',
          selectTypePayment: packageItem.payment_type === 'Monthly' ? 'รายเดือน' : packageItem.payment_type === 'Yearly' ? 'รายปี' : '',
          productPrice: packageItem.package_code === 'CUSTOM' ? '>' + packageItem.price : packageItem.price,
          descriptionPackage: packageItem.package_detail === null ? '' : packageItem.package_detail,
          descriptionCondition: packageItem.service_detail,
          phone: packageItem.phone,
          email: packageItem.email,
          dataFunction: packageItem.list_function ? packageItem.list_function.map(listFunc => ({
            idFunc: listFunc.id,
            nameFunc: listFunc.name,
            unitType: listFunc.unit_function,
            countTransaction: listFunc.limit === -1 ? 'ไม่จำกัด' : listFunc.limit,
            priceTransaction: listFunc.excess_transaction_price === 0 ? '' : listFunc.excess_transaction_price,
            selectTypeUnit: listFunc.excess_transaction_unit === 'baht' ? 'บาท' : listFunc.excess_transaction_unit === 'percent' ? 'เปอร์เซ็นต์' : '',
            checkbox: listFunc.limit === -1
          }))
            : []
        }))
      }
    },
    addItem () {
      this.dataServices.push({
        id: null,
        selectPackage: '',
        selectTypePayment: '',
        productPrice: '',
        descriptionPackage: '',
        descriptionCondition: '',
        phone: '',
        email: '',
        dataFunction: [
          {
            idFunc: '',
            nameFunc: '',
            unitType: '',
            countTransaction: '',
            priceTransaction: '',
            selectTypeUnit: '',
            checkbox: false
          }
        ]
      })
      // console.log('dataServices', this.dataServices)
    },
    addItemFunc (serviceIndex) {
      this.dataServices[serviceIndex].dataFunction.push({
        idFunc: null,
        nameFunc: '',
        unitType: '',
        countTransaction: '',
        priceTransaction: '',
        selectTypeUnit: '',
        checkbox: false
      })
    },

    async removeItemFunc (serviceIndex, funcIndex, id) {
      this.dataServices[serviceIndex].dataFunction.splice(funcIndex, 1)
      this.itemDelete.push(id)
    },
    async deleteItemFunc () {
      this.$store.commit('openLoader')
      var data = {
        function_id: this.itemDelete
      }
      await this.$store.dispatch('actionsCancelFunction', data)
      var response = await this.$store.state.ModuleBusiness.stateCancelFunction
      if (response.code === 200) {
        this.$store.commit('closeLoader')
      }
    },
    async removeItem (index, id) {
      this.dataServices.splice(index, 1)
      // console.log('see id', id)
      this.$store.commit('openLoader')
      var data = { package_id: String(id) }
      await this.$store.dispatch('actionsCancelPackage', data)
      var response = await this.$store.state.ModuleBusiness.stateCancelPackage
      if (response.code === 200) {
        this.cancelPackage = response.data
        // console.log('see cancelPackage', this.cancelPackage)
        this.$store.commit('closeLoader')
      }
    },
    async getPackage () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPackage')
      var response = await this.$store.state.ModuleBusiness.stateGetPackage
      if (response.code === 200) {
        this.listPackageName = response.data
        // console.log(this.listPackageName)
        // this.$store.commit('closeLoader')
      }
    },
    availablePackages (index) {
      const selectPackages = this.dataServices
        .filter((_, i) => i !== index)
        .map(item => item.selectPackage)
      return this.listPackageName.filter(packageItem => !selectPackages.includes(packageItem.package_code))
    },
    async editPackage () {
      await this.deleteItemFunc()
      var listPackage = await Promise.all(
        this.dataServices.map(async (e) => {
          return {
            id: e.id,
            package_code: e.selectPackage,
            // payment_type: e.selectTypePayment === 'รายเดือน' ? 'Monthly' : e.selectTypePayment === 'รายปี' ? 'Yearly' : 'Percent',
            payment_type: e.selectTypePayment === 'รายเดือน' ? 'Monthly' : e.selectTypePayment === 'รายปี' ? 'Yearly' : '',
            price: String(e.productPrice).replace('>', ''),
            package_detail: await this.convertBase64ToLinkInHTML(e.descriptionPackage),
            service_detail: await this.convertBase64ToLinkInHTML(e.descriptionCondition),
            phone: e.phone,
            email: e.email,
            list_function: e.dataFunction.map((item) => {
              return {
                id: item.idFunc,
                name: item.nameFunc,
                unit_function: item.unitType,
                limit: item.countTransaction,
                excess_transaction_unit: item.selectTypeUnit === '' ? 'ไม่จำกัด' : item.selectTypeUnit,
                excess_transaction_price: item.priceTransaction === '' ? '0' : item.priceTransaction
              }
            })
          }
        })
      )
      this.$store.commit('openLoader')
      var data = {
        partner_code: this.partnerCode,
        // partner_name: this.detailPackageName,
        // detail: this.detailDescription,
        list_package: listPackage
      }
      // console.log(data, 'see data')
      await this.$store.dispatch('actionsEditPackage', data)
      var response = await this.$store.state.ModuleBusiness.stateEditPackage
      if (response.code === 200) {
        this.detailEditPackage = response.data
        await this.$store.commit('closeLoader')
        this.dialogConfirmEdit = true
      } else if (response.message === 'Please add at least 1 package before update.') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'กรุณาเพิ่มแพ็คเกจอย่างน้อย 1 รายการ' })
        this.dialogAwaitConfirm = false
      } else if (response.message === 'Failed to update or create package.') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'warning', text: 'แก้ไข package ไม่สำเร็จ กรุณาติดต่อเจ้าหน้าที่' })
        this.dialogAwaitConfirm = false
      } else if (response.code === 400 && response.message !== {}) {
        if (response.message.list_function[0] === 'At least 1 function item should be added to PACK-S.') {
          this.$store.commit('closeLoader')
          await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'กรุณาเพิ่ม Function ของ package - S อย่างน้อย 1 รายการ' })
          this.dialogAwaitConfirm = false
        } else if (response.message.list_function[0] === 'At least 1 function item should be added to PACK-M.') {
          this.$store.commit('closeLoader')
          await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'กรุณาเพิ่ม Function ของ package - M อย่างน้อย 1 รายการ' })
          this.dialogAwaitConfirm = false
        } else {
          this.$store.commit('closeLoader')
          await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'กรุณาเพิ่ม Function ของ package - L อย่างน้อย 1 รายการ' })
          this.dialogAwaitConfirm = false
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async convertBase64ToLinkInHTML (textHTML) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const base64Pattern = /<img[^>]+src=["'](data:image\/[^"']+)["'][^>]*>/g
      const matches = [...textHTML.matchAll(base64Pattern)]
      this.$store.commit('openLoader')
      if (matches.length === 0) {
        return textHTML
      }
      for (const match of matches) {
        const base64 = match[1]
        const response = await fetch(`${process.env.VUE_APP_NEW_BACK_END}api/upload_to_s3`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${oneData.user.access_token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            image: [base64.split(',')[1]],
            type: 'package_description',
            partner_code: this.partnerCode
          })
        })
        const result = await response.json()
        if (result.data.list_path[0].path) {
          textHTML = textHTML.replace(base64, result.data.list_path[0].path)
        }
        this.$store.commit('closeLoader')
      }
      return textHTML
    },
    closeChooseAddPackage () {
      this.dialogAwaitConfirm = false
      this.dialogPreviewPackage = true
    },
    substring (data) {
      return data.length > 70 ? data.substring(0, 70) + '...' : data
    },
    async getDetailPartner () {
      var data = {
        id_card_num: this.taxId
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerList', data)
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerList
      this.showpartnerdetail = response.data[0]
      // console.log(this.showpartnerdetail)
      if (response.code === 200 && response.data.length !== 0) {
        if (this.showpartnerdetail.new_address.length === 0) {
          this.address = response.data[0].address[0]
        } else {
          this.address = response.data[0].new_address[0]
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style>

</style>
<style scoped>
.ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-rounded-corners {
  background-color: #fff;
}
:deep(.chackbox) .v-messages{
  display: none !important;
}
.v-input--selection-controls {
  margin: 0 !important;
  padding: 0 !important;
}
</style>
