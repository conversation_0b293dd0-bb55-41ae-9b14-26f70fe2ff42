<template>
  <v-container grid-list-xs>
    <v-row dense>
      <v-col cols="12">
        <h1>รถเข็นสินค้าของฉัน</h1>
      </v-col>
      <v-col cols="12">
        <ShoppingCartLogin v-if="checkLogin === true"/>
        <ShoppingCartLocalStorage  v-else/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  components: {
    ShoppingCartLocalStorage: () => import(/* webpackPrefetch: true */ '@/components/Cart/ShoppingCartLocalStorage'),
    ShoppingCartLogin: () => import(/* webpackPrefetch: true */ '@/components/Cart/ShoppingCartLogin')
  },
  data () {
    return {
      checkLogin: false
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('oneData') !== null) {
      this.checkLogin = true
    } else {
      this.checkLogin = false
    }
  }
}
</script>
