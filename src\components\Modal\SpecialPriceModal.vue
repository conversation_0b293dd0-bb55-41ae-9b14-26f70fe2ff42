<template>
  <v-dialog v-model="openSpecialPriceRequestModal" width="850px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
    <v-card width="100%" height="100%" class="rounded-lg">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span class="flex text-center ml-5" style="font-size:20px">
          <font color="#27AB9C">การร้องขอราคาพิเศษ</font>
        </span>
        <v-btn icon dark @click="openSpecialPriceRequestModal = !openSpecialPriceRequestModal">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row class="mt-2">
        <v-col cols="12" md="12">
          <v-row dense>
            <v-col cols="12">
              <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
              <span class="ml-3" style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">{{ shopName }}</span>
            </v-col>
            <v-col cols="12">
              <!-- <v-select multiple return-object v-model="selectedItem" :items="searchProductList" item-text="name" item-value="id" @change="updateSelectData()" outlined dense> -->
              <v-autocomplete
                return-object
                v-model="selectedItem"
                :items="searchProductList"
                outlined
                dense
                chips
                small-chips
                multiple
                :menu-props="{ overflowY: true, maxHeight: 250, bottom: true }"
                item-text="name"
                item-value="searchId"
                @change="(event) => updateSelectData(event)"
                no-data-text="ไม่พบสินค้าที่ค้นหา"
              >
                <template v-slot:selection="data">
                  <v-chip
                    class="ma-1"
                    v-bind="data.attrs"
                    :input-value="data.selected"
                    close
                    @click="data.select"
                    @click:close="remove(data.item)"
                  >
                    <v-avatar left>
                      <v-img :src="data.item.product_image" v-if="data.item.product_image !== null"></v-img>
                      <img src="@/assets/NoImage.png" v-else />
                    </v-avatar>
                    <span>{{ data.item.name|truncate(15, '...') }}</span>
                    <span v-snip="1">
                      <span class="ml-1" v-if="data.item.have_attribute === 'yes' && data.item.attribute_1_key !== null" >{{ data.item.attribute_1_key }} : {{ data.item.attribute_priority_1|truncate(15, '...')  }}</span>
                      <span class="ml-1" v-if="data.item.have_attribute === 'yes' && data.item.attribute_2_key !== null"> {{ data.item.attribute_2_key }} : {{ data.item.attribute_priority_2|truncate(15, '...')  }}</span>
                    </span>
                  </v-chip>
                </template>
                <template v-slot:item="{ item, attrs }">
                  <v-container :class="MobileSize ? 'px-0' : 'pa-2'">
                    <v-row no-gutters v-if="!MobileSize">
                      <v-col cols="2">
                        <v-icon
                          v-if="selectedItem.length !== 0 && selectedItem.includes(item)"
                          color="primary"
                          class="mr-3">
                          mdi-checkbox-marked
                        </v-icon>
                        <v-icon v-else class="mr-3">
                          mdi-checkbox-blank-outline
                        </v-icon>
                        <img v-bind="attrs" :src="item.product_image" width="50" height="50" v-if="item.product_image !== null">
                        <img src="@/assets/NoImage.png" width="50" height="50" v-else />
                      </v-col>
                      <v-col cols="7" style="max-width: 380px;">
                        <span :class="MobileSize ? 'mobile-font-size' : ''">{{ item.name|truncate(20, '...') }}</span>
                        <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_1_key !== null" >{{ item.attribute_1_key }} : {{ item.attribute_priority_1| truncate(20, '...') }}</span>
                        <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_2_key !== null"> {{ item.attribute_2_key }} : {{ item.attribute_priority_2| truncate(20, '...') }}</span>
                      </v-col>
                      <v-col cols="3" align="end">
                        <span :class="MobileSize ? 'mobile-font-size' : ''">ราคา {{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span><br>
                      </v-col>
                    </v-row>
                    <v-row no-gutters v-else style="width: 250px;">
                      <v-col cols="5" class="pt-2">
                        <v-icon
                          v-if="selectedItem.length !== 0 && selectedItem.includes(item)"
                          color="primary"
                          class="mr-3">
                          mdi-checkbox-marked
                        </v-icon>
                        <v-icon v-else class="mr-3">
                          mdi-checkbox-blank-outline
                        </v-icon>
                        <img v-bind="attrs" :src="item.product_image" width="50" height="50" v-if="item.product_image !== null">
                        <img src="@/assets/NoImage.png" width="50" height="50" v-else />
                      </v-col>
                      <v-col cols="7" class="pl-2">
                        <span :class="MobileSize ? 'mobile-font-size' : ''">{{ item.name|truncate(20, '...') }}</span><br/>
                        <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_1_key !== null" >{{ item.attribute_1_key }} : {{ item.attribute_priority_1| truncate(20, '...') }}</span>
                        <span :class="MobileSize ? 'mobile-font-size' : ''" v-if="item.have_attribute === 'yes' && item.attribute_2_key !== null"> {{ item.attribute_2_key }} : {{ item.attribute_priority_2| truncate(20, '...') }}</span>
                        <br v-if="item.have_attribute === 'yes'" /><span :class="MobileSize ? 'mobile-font-size' : ''">ราคา {{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span><br>
                      </v-col>
                    </v-row>
                  </v-container>
                </template>
              </v-autocomplete>
              <!-- </v-select> -->
              <!-- <v-text-field class="rounded-lg" v-model="search" placeholder="ค้นหาสินค้า" outlined dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field> -->
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="12">
          <v-row dense>
            <v-col cols="12">
              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รายการสินค้า</span>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="12" v-if="checkTable">
              <v-data-table
              :headers="headersEditProduct"
              :items="DataTableProduct.product_list"
              style="width: 100%;"
              height="100%"
              no-results-text="ไม่พบรายการร้องขอพิเศษ"
              no-data-text="ไม่มีรายการร้องขอพิเศษ"
              class="elevation-1 mt-4"
              hide-default-footer
              :disable-pagination="true"
              >
              <template v-slot:[`item.product_detail`]="{ item }">
                <v-row class="py-2" :style="MobileSize ? '' : 'width:250px'" justify-center>
                  <v-col :cols="MobileSize ? 12 : 3" class="px-0 mx-2 my-auto" justify-center>
                    <v-img width="60px" height="60px" contain :src="`${item.product_image}`" v-if="item.product_image !== ''"/>
                    <v-img width="60px" height="60px" contain src="@/assets/NoImage.png" v-else />
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 7" :align="!MobileSize ? '' : 'left'" :class="!MobileSize ? 'px-0 ml-0 my-auto' : 'px-1 ml-0 pt-0'" :style="{ 'width': MobileSize ? '68px' : '' }">
                    <span class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{item.product_name}}</span><br/>
                    <span v-if="item.have_attribute === 'yes' && item.key_1_value !== '' && item.key_1_value !== null" class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.key_1_value }}: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                    <span v-if="item.have_attribute === 'yes' && item.key_2_value !== '' && item.key_2_value !== null" class="mb-0 ml-1" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.key_2_value }}: {{ item.product_attribute_detail.attribute_priority_2 }}</span>
                    <p v-if="item.status_data_change === 'yes'" class="mb-0" style="font-size: 10px; color: red;">สินค้ามีการเปลี่ยนแปลง กรุณาลบสินค้าหรือติดต่อร้านค้า</p>
                  </v-col>
                </v-row>
              </template>
              <template v-slot:[`item.price`]="{ item }">
                <v-card outlined max-width="200px;" :height="MobileSize ? 'auto' : '50px'" :width="!MobileSize ? '100%' : '100px'" :class="MobileSize? 'ma-2' : ''">
                  <v-card-text class="py-2 px-1">
                    <v-row justify="center" class="py-2 px-3">
                      <v-col cols="12" md="12" class="pa-0" align="center">
                        <v-text-field
                          :style="MobileSize ? 'font-weight: 400; font-size: 14px; line-height: 22px; color: #333333; height: auto;' : 'height: 50px; width:100px'"
                          v-model="item.price"
                          dense
                          hide-details
                          solo flat
                          class="pa-0 text-price"
                          @change="changeInputPriceAndQuantity(item, 'price')"
                          oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"
                          :readonly="item.status_data_change === 'yes'"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </template>
              <template v-slot:[`item.quantity`]="{ item }">
                <v-card outlined height="50px" :width="!MobileSize ? '100%' : '100px'" :class="MobileSize? 'ma-2' : ''">
                  <v-card-text class="py-2 px-1">
                    <v-row justify="center" class="py-2 px-3">
                      <v-col cols="4" md="4" class="pa-0" align="center">
                        <v-hover v-slot="{ hover }">
                          <v-btn :disabled="item.quantity <= 1 || item.status_data_change === 'yes'" @click="item.quantity > 1 && item.price.toString() !== '0.00' ? item.quantity-- : '', changeInputPriceAndQuantity(item, 'quantity')" elevation="0" class="mt-1" :color="hover ? '#A1A1A1' : '#E6E6E6'" style="min-width: 26px !important; height: 30px !important; padding: 0px 8px !important;">
                            <v-icon class="px-0" color="white" small>mdi-minus</v-icon>
                          </v-btn>
                        </v-hover>
                      </v-col>
                      <v-col cols="4" md="4" class="pa-0" align="center">
                        <v-text-field
                          v-model="item.quantity"
                          dense
                          solo flat
                          :style="MobileSize ? 'font-weight: 400; font-size: 14px; line-height: 22px; color: #333333' : 'width:60px'"
                          type="number"
                          class="pa-0 quantity-input"
                          @click="item.price.toString() == '0.00' ? errMsg() : ''"
                          @change="changeInputPriceAndQuantity(item, 'quantity')"
                          :readonly="item.status_data_change === 'yes' || item.price.toString() === '0.00'"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="4" md="4" sm="4" class="pa-0" align="center">
                        <v-hover v-slot="{ hover }">
                          <v-btn :disabled="item.quantity > item.stock || item.status_data_change === 'yes'" @click="item.price.toString() !== '0.00' ? item.quantity++ : '', changeInputPriceAndQuantity(item, 'quantity')" elevation="0" class="mt-1" :color="hover ? '#A1A1A1' : '#E6E6E6'" style="min-width: 28px !important; height: 30px !important; padding: 0px 8px !important;">
                            <v-icon class="px-0" color="white" small>mdi-plus</v-icon>
                          </v-btn>
                        </v-hover>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </template>
              <template v-slot:[`item.net_price`]="{ item }">
                <span>{{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </template>
              <template v-slot:[`item.action`]="{ item, index }">
                <v-row>
                  <v-col cols="12">
                    <v-card elevation="1" width="100%" height="100%">
                    <v-btn color="#27AB9C" icon @click="deleteCartItem(item, index)">
                      <v-icon class="button-edit-delete">mdi-delete-outline</v-icon>
                    </v-btn>
                    </v-card>
                  </v-col>
                </v-row>
              </template>
              </v-data-table>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col align="right">
              <span>ราคารวมทั้งสิ้น (ไม่รวม % ภาษี)</span>
              <span> {{ Number(totalPriceNoVat).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</span>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col align="right">
              <v-btn :loading="loading" :disabled="'product_list' in DataTableProduct && DataTableProduct.product_list.length === 0 || checkKeyClick === true" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="sendSpecialPriceRequest()" >
                ร้องขอราคาพิเศษ
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      loading: false,
      checkTable: true,
      lazy: false,
      search: '',
      selectedItem: [],
      openSpecialPriceRequestModal: false,
      items: '',
      DataTableProduct: {
        product_list: []
      },
      searchProductList: [],
      headersEditProduct: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', width: 260, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', width: 120, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', width: 180, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'net_price', width: 120, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: '', value: 'action', width: '50px', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      companyId: null,
      sellerShopID: null,
      userId: '',
      count: 0,
      totalPriceNoVat: 0,
      keys: '',
      priceProduct: 0,
      productStock: null,
      attrbuteIDProduct: null,
      productImage: null,
      shopName: '',
      checkKeyClick: false
    }
  },
  watch: {
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  mounted () {
  },
  created () {
    this.$EventBus.$on('UpdatesettingTierSuccess', this.cancelAndEmitData)
    this.$EventBus.$on('cancelProcessGetNewData', this.getDetailSettingTier)
  },
  beforeDestroy () {
    this.$EventBus.$off('UpdatesettingTierSuccess')
    this.$EventBus.$off('cancelProcessGetNewData')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    async open (data, keys, shopName) {
      this.keys = keys
      this.shopName = shopName
      this.DataTableProduct.product_list = []
      this.selectedItem = []
      this.openSpecialPriceRequestModal = true
      if (localStorage.getItem('SetRowCompany') !== null) {
        const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        this.companyId = companyId.company.company_id
      }
      var path = this.$router.currentRoute.params.data
      var cleanPath = path.split('-')
      this.sellerShopID = keys === 'fromProductPage' ? parseInt(data.productDetail.seller_shop_id) : parseInt(cleanPath[cleanPath.length - 1])
      this.userId = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.getSearchProductData()
      if (keys === 'fromProductPage') {
        this.prepareDataFromProductPageToTable(data)
      }
    },
    prepareDataFromProductPageToTable (data) {
      this.selectedItem = []
      if (data.productDetail.have_attribute === 'yes') {
        this.getProductAttributeID(data)
      }
      this.DataTableProduct.product_list.push({
        product_id: data.productDetail.product_id,
        product_name: data.productDetail.product_name,
        product_image: data.productDetail.have_attribute === 'yes' ? this.productImage : data.productDetail.list_product_image.length !== 0 ? data.productDetail.list_product_image[0].media_path : '',
        have_attribute: data.productDetail.have_attribute,
        key_1_value: data.productDetail.have_attribute === 'yes' ? data.productDetail.key_1_value : null,
        key_2_value: data.productDetail.have_attribute === 'yes' ? data.productDetail.key_2_value : null,
        product_attribute_detail: {
          attribute_priority_1: data.productDetail.have_attribute === 'yes' ? data.attribute_option_1 : null,
          attribute_priority_2: data.productDetail.have_attribute === 'yes' ? data.attribute_option_2 : null,
          product_attribute_id: data.productDetail.have_attribute === 'yes' ? this.attrbuteIDProduct : null
        },
        price: data.productDetail.have_attribute === 'yes' ? data.priceSelect : data.productDetail.real_price,
        quantity: data.quantity,
        net_price: data.productDetail.have_attribute === 'yes' ? this.priceProduct * parseInt(data.quantity) : data.productDetail.real_price * parseInt(data.quantity),
        stock: data.productDetail.have_attribute === 'yes' ? this.productStock : data.productDetail.for_edit_effective_stock,
        max_per_order: data.productDetail.max_per_order,
        min_per_order: data.productDetail.min_per_order,
        service_type: data.productDetail.service_type
      })
      // this.selectedItem = this.DataTableProduct.product_list
      this.DataTableProduct.product_list.forEach(element => {
        this.searchProductList.forEach((search, i) => {
          if (element.have_attribute === 'yes') {
            if (parseInt(element.product_attribute_detail.product_attribute_id) + ',id' + element.product_id === search.searchId) {
              if (this.selectedItem.length !== 0) {
                const check = this.selectedItem.find(e => e.searchId === search.searchId)
                if (check === false) {
                  this.selectedItem.push(search)
                }
              } else {
                this.selectedItem.push(search)
              }
            }
          } else {
            if (parseInt(element.product_id) === parseInt(search.product_id)) {
              this.selectedItem.push(search)
            }
          }
        })
      })
      this.updateTable()
    },
    getProductAttributeID (data) {
      data.productDetail.attribute_manage.forEach(element => {
        if (element.attribute_priority_1 === data.attribute_option_1) {
          element.attribute_priority_2.forEach(atr2 => {
            if (atr2.value === data.attribute_option_2) {
              this.attrbuteIDProduct = atr2.product_attribute_id
              this.productStock = atr2.effective_stock
              this.productImage = element.attribute_image.image_data === '' || element.attribute_image.image_data === undefined ? data.productDetail.list_product_image[0].media_path : element.attribute_image.image_data
            }
          })
        }
      })
    },
    async getSearchProductData () {
      var data = {
        company_id: this.companyId,
        seller_shop_id: this.sellerShopID,
        user_id: this.userId ? this.userId.user.user_id : ''
      }
      await this.$store.dispatch('actionsListProductAttribute', data)
      var response = await this.$store.state.ModuleAdminManage.stateListProductAttribute
      if (response.message === 'Find product for QU success.') {
        this.searchProductList = response.data
        this.searchProductList.forEach((element, i) => {
          this.searchProductList[i].price = element.real_price
          this.searchProductList[i].quantity = element.quantity
          this.searchProductList[i].total_price = null
          this.searchProductList[i].searchId = element.have_attribute === 'yes' ? element.attribute_id + ',id' + element.product_id : element.product_id
        })
      }
    },
    errMsg () {
      const msg = 'ไม่สามารถเปลี่ยนแปลงจำนวนสินค้าได้เนื่องจากราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
      this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', text: msg })
    },
    async changeInputPriceAndQuantity (item, type) {
      if (type === 'price') {
        this.inputPrice(item)
      } else if (type === 'quantity') {
        if (parseFloat(item.price) === 0.00) {
          item.quantity = parseInt(item.quantity)
          const msg = 'ไม่สามารถเปลี่ยนแปลงจำนวนสินค้าได้เนื่องจากราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', text: msg })
        } else {
          this.inputQuantity(item)
        }
      }
    },
    async inputPrice (item) {
      if (parseFloat(item.price) === 0.00) {
        item.price = parseFloat(item.price).toFixed(2)
        this.checkKeyClick = true
        await this.updateTable()
      } else if (item.price === '' || item.quantity === null || item.price === 'NaN') {
        item.price = 0.00
        this.checkKeyClick = true
        await this.updateTable()
      } else {
        item.price = parseFloat(item.price).toFixed(2)
        this.checkKeyClick = false
        await this.updateTable()
      }
    },
    async inputQuantity (item) {
      if (parseInt(item.quantity) === 0 || item.quantity === '' || item.quantity === null || item.quantity === 'NaN') {
        const msg = 'จำนวนสินค้าควรเป็นจำนวนบวกและมีค่ามากกว่า 0 และ ไม่ใช่ค่าว่าง'
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', text: msg })
        item.quantity = 1
        await this.updateTable()
      } else if (parseInt(item.quantity) > parseInt(item.stock)) {
        const msg = `ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว (สินค้าคงเหลือ ${item.stock} ชิ้น)`
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'warning', text: msg })
        item.quantity = parseInt(item.stock)
        await this.updateTable()
      } else {
        await this.updateTable()
      }
    },
    deleteCartItem (item, index) {
      this.$swal.fire({
        icon: 'warning',
        text: 'คุณต้องการที่จะลบสินค้านี้หรือไม่?',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        cancelButtonColor: '#FF3F00',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.DataTableProduct.product_list.splice(index, 1)
          const indexSelect = this.selectedItem.findIndex(e => item.have_attribute === 'yes' ? item.product_attribute_detail.product_attribute_id === e.attribute_id : item.product_id === e.product_id)
          this.selectedItem.splice(indexSelect, 1)
          this.updateTable()
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    async sendSpecialPriceRequest () {
      this.loading = true
      this.$store.commit('openLoader')
      this.checkKeyClick = true
      const productList = []
      this.DataTableProduct.product_list.forEach(element => {
        productList.push({
          product_id: element.product_id,
          product_name: element.product_name,
          attribute_id: element.product_attribute_detail.product_attribute_id === null ? '-1' : element.product_attribute_detail.product_attribute_id,
          quantity: parseInt(element.quantity),
          price: parseFloat(element.price),
          stock: element.stock,
          product_image: element.product_image
        })
      })
      const data = {
        seller_shop_id: this.sellerShopID,
        company_id: this.companyId,
        product_list: productList
      }
      await this.$store.dispatch('actionsRequestSpecialPrice', data)
      var response = await this.$store.state.ModuleAdminManage.stateRequestSpecialPrice
      if (response.message === 'your request special price successful') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('getItemNoti')
        this.checkKeyClick = false
        this.openSpecialPriceRequestModal = !this.openSpecialPriceRequestModal
        this.$swal.fire({ text: 'ส่งการร้องขอราคาพิเศษแล้ว', icon: 'success', timer: 2500, showConfirmButton: false })
      } else if (response.result === 'quantity หรือ price ควรเป็นจำนวนบวกและมีค่ามากกว่า 0') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'จำนวน หรือ ราคา ควรเป็นจำนวนที่มีค่ามากกว่า 0'
        })
      } else if (response.result === 'request special price can not more than or equal products price') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: response.message
        })
      } else if (response.result === 'can not request special price more than your credit') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'ไม่สามารถร้องขอราคาพิเศษได้ เนื่องจากราคาสินค้าเกินวงเงินของบริษัท'
        })
      } else if (response.result === 'Can not buy more than stock.') {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: response.message
        })
      } else {
        this.loading = false
        this.$store.commit('closeLoader')
        this.checkKeyClick = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    prepareUpdateSelectData (element) {
      this.DataTableProduct.product_list.push({
        product_id: element.product_id,
        product_name: element.name,
        product_image: element.product_image,
        have_attribute: element.have_attribute,
        key_1_value: element.attribute_1_key,
        key_2_value: element.attribute_2_key,
        product_attribute_detail: {
          attribute_priority_1: element.attribute_priority_1,
          attribute_priority_2: element.attribute_priority_2,
          product_attribute_id: element.attribute_id
        },
        price: parseFloat(element.real_price).toFixed(2),
        quantity: 1,
        net_price: 0,
        stock: element.stock_count,
        service_type: element.service_type
      })
    },
    updateSelectData (value) {
      if (value.length < this.DataTableProduct.product_list.length) {
        this.DataTableProduct.product_list.forEach((element, index) => {
          const check = value.some(e => element.have_attribute === 'yes' ? element.product_attribute_detail.product_attribute_id === e.attribute_id : element.product_id === e.product_id)
          if (check === false) {
            this.DataTableProduct.product_list.splice(index, 1)
          }
        })
        this.updateTable()
      } else {
        value.forEach((element, i) => {
          const check2 = this.DataTableProduct.product_list.some(e => e.have_attribute === 'yes' ? e.product_attribute_detail.product_attribute_id === element.attribute_id : e.product_id === element.product_id)
          const checkItemBulkInTable = this.DataTableProduct.product_list.some(e => e.service_type === 'bulk')
          // เช็คว่าใน table มีสินค้าที่มีขนาดใหญ่แล้วรึยังถ้ามีอยู่แล้วจะการแจ้งเตือนว่าสามารถซื้อสินค้าขนาดใหญ่ได้แค่ชิ้นเดียวและไม่ add ชิ้นอื่นลงใน table
          if (check2 === false && checkItemBulkInTable === false) {
            this.prepareUpdateSelectData(element)
            this.updateTable()
          } else if (check2 === false && checkItemBulkInTable === true) {
            // เช็คว่าสินค้าชิ้นที่เลือกเป็นสินค้าขนาดใหญ่ไหมถ้าใช่จะไม่ add ลง table
            if (element.service_type !== 'bulk') {
              this.prepareUpdateSelectData(element)
              this.updateTable()
            } else {
              this.selectedItem.splice(i, 1)
              this.$swal.fire({
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                icon: 'warning',
                text: 'ไม่สามารถเพิ่มสินค้าได้ เนื่องจากเป็นสินค้าขนาดใหญ่ สามารถซื้อสินค้าขนาดใหญ่ได้ 1 ชิ้นต่อคำสั่งซื้อ'
              })
            }
          }
        })
      }
    },
    prepareDataTable (response) {
      this.totalPriceNoVat = response.total_price_no_vat
      response.product_list.forEach(element => {
        this.DataTableProduct.product_list.push({
          product_id: element.product_id,
          product_name: element.product_name,
          product_image: element.product_image,
          have_attribute: element.have_attribute,
          key_1_value: element.key_1_value,
          key_2_value: element.key_2_value,
          product_attribute_detail: {
            attribute_priority_1: element.product_attribute_detail.attribute_priority_1,
            attribute_priority_2: element.product_attribute_detail.attribute_priority_2,
            product_attribute_id: element.product_attribute_detail.product_attribute_id
          },
          price: parseFloat(element.price).toFixed(2),
          quantity: element.quantity,
          net_price: element.total_price,
          stock: element.stock,
          service_type: element.service_type
        })
      })
    },
    async updateTable () {
      this.$store.commit('openLoader')
      const data = {
        product_list: this.DataTableProduct.product_list
      }
      await this.$store.dispatch('actionsUpdateListDataTableSpecialPrice', data)
      var response = await this.$store.state.ModuleAdminManage.stateUpdateListDataTableSpecialPrice
      this.DataTableProduct.product_list = []
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.prepareDataTable(response)
        // this.DataTableProduct.product_list = response.product_list
      } else if (response.message === 'price can not less than 0') {
        this.$store.commit('closeLoader')
        this.prepareDataTable(response)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'ราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
        })
      } else if (response.result === 'เป็นสินค้าขนาดใหญ่สามารถซื้อได้ 1 ชิ้นเท่านั้น') {
        this.$store.commit('closeLoader')
        this.prepareDataTable(response)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: response.message
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${response.message}`
        })
      }
    },
    remove (item) {
      const index = this.selectedItem.findIndex(e => e.searchId === item.searchId)
      this.selectedItem.splice(index, 1)
      const indexI = this.DataTableProduct.product_list.findIndex(e => e.have_attribute === 'yes' ? e.product_attribute_detail.product_attribute_id === item.attribute_id : e.product_id === item.product_id)
      this.DataTableProduct.product_list.splice(indexI, 1)
      this.checkTable = true
    }
  }
}
</script>

<style>
.quantity-input .v-input__slot {
  padding: 0 4px !important;
}
</style>
