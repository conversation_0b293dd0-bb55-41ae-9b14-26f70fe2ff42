<template>
  <v-card outlined class="ma-5">
    <!-- <v-data-table
      hide-default-footer
      disable-sort
      :disable-pagination="disable_pagination"

      :items="table1"
      item-key="name"
      :calculate-widths="widths"
      dense
      class="fixed_header"
    >
      <template v-slot:[`item.sku`]="{ item }" :width="headers.width">
        <span style="font-size: 10px; font-weight: 301">{{ item.sku }}</span>
      </template>
      <template v-slot:[`item.name`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{ item.name }}</span>
      </template>
      <template v-slot:[`item.quantity`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.quantity).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.price).toLocaleString()
        }}</span>
      </template>
      <template v-slot:[`item.discount_percent`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          item.discount_percent
        }}</span>
      </template>
      <template v-slot:[`item.net_price`]="{ item }">
        <span style="font-size: 10px; font-weight: 301">{{
          Number(item.net_price).toLocaleString()
        }}</span>
      </template>
    </v-data-table> -->
    <!-- <pre>{{ OrderDetailProp }}</pre> -->
    <v-data-table
      hide-default-footer
      disable-sort
      :headers="headers"
      :items="product_each"
      item-key="id"
      dense
      class="fixed_header"
    >
      <template v-slot:[`item.net_price`]="{ item }">
        {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
      </template>
    </v-data-table>
  </v-card>
</template>
<script>
export default {
  props: ['OrderDetailProp'],
  created () {
    // console.log('หนึ่งสั่งให้ log', this.OrderDetailProp)
    this.foreach(this.OrderDetailProp)
  },
  methods: {
    foreach (val) {
      val.order_number.forEach(element1 => {
        // console.log('element1', element1)
        element1.data.product_list.forEach(element => {
          // console.log('log elemnt เฉยๆ', element)
          this.product_each.push(element)
        })
      })
    }
  },
  watch: {
    OrderDetailProp (val) {
      // console.log('val =======', val)
      this.foreach(val)
    // val.order_number.forEach(element1 => {
    //   console.log('ele1', element1)
    // element1.order_number.forEach(element => {
    //   console.log('element===', element)
    //   element.data.product_list.forEach(element2 => {
    //     console.log('element2 ===', element2)
    //     element2.order_number = element.order_number
    //     this.product_each.push(element2)
    //   })
    // })
    // })
    // console.log('val', this.product_each)
    }
  },
  data: () => ({
    product_each: [],
    headers: [
      {
        text: 'รหัสสินค้า',
        value: 'sku',
        align: 'center',
        divider: true,
        width: '100'
      },
      { text: 'ชื่อร้านค้า', value: 'shop_name', align: 'center', divider: true },
      { text: 'ชื่อสินค้า', value: 'product_name', align: 'center', divider: true },
      { text: 'จำนวน', value: 'quantity', align: 'center', width: 50, divider: true },
      {
        text: 'ราคา',
        value: 'net_price',
        align: 'center',
        width: '100'
      }
    ]
  })
}
</script>
