<template>
    <v-container>
      <v-carousel
       cycle
       height="100%"
       hide-delimiter-background
       show-arrows-on-hover
      >
        <v-carousel-item
         v-for="(slide, i) in slides"
         :key="i"
        >
          <v-img :src="slide" height="400" width="100%"></v-img>
        </v-carousel-item>
      </v-carousel>
    </v-container>
</template>

<script>
export default {
  data () {
    return {
      slides: [
        require('@/assets/ImageINET-Marketplace/Banner/Banner-1.png'),
        require('@/assets/ImageINET-Marketplace/Banner/Banner-2.png')
      ]
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1250px;
}
</style>
