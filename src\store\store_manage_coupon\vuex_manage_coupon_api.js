import AxiosUser from './axios_manage_coupon_api'

const ModuleManageCoupon = {
  state: {
    // User Detail Page
    statecreateCoupon: [],
    stateCategoryShopList: [],
    stategetCoupon: [],
    stateEditCoupon: [],
    stategetDetailCoupon: [],
    statechangeStatusCoupon: [],
    statechangeDeleteCoupon: [],
    // เส้นดึง product category ใหม่ ของเก่ง
    stateListProductInShop: []
  },
  mutations: {
    // User Detail Page
    mutationscreateCoupon (state, data) {
      state.statecreateCoupon = data
    },
    mutationcategoryShopList (state, data) {
      state.stateCategoryShopList = data
    },
    mutationgetCoupon (state, data) {
      state.stategetCoupon = data
    },
    mutationEditCoupon (state, data) {
      state.stateEditCoupon = data
    },
    mutationgetDetailCoupon (state, data) {
      state.stategetDetailCoupon = data
    },
    mutationchangeStatusCoupon (state, data) {
      state.statechangeStatusCoupon = data
    },
    mutationchangeDeleteCoupon (state, data) {
      state.statechangeDeleteCoupon = data
    },
    mutationListProductInShop (state, data) {
      state.stateListProductInShop = data
    }
  },
  actions: {
    // User Detail Page
    async actionscreateCoupon (context, access) {
      const responseData = await AxiosUser.createCoupon(access)
      // console.log('response', responseData)
      await context.commit('mutationscreateCoupon', responseData)
    },
    async actionscategoryShopList (context, access) {
      const responseData = await AxiosUser.categoryShopList(access)
      // console.log('response', responseData)
      await context.commit('mutationcategoryShopList', responseData)
    },
    async actionsgetCoupon (context, access) {
      const responseData = await AxiosUser.getCoupon(access)
      // console.log('response', responseData)
      await context.commit('mutationgetCoupon', responseData)
    },
    async actionsEditCoupon (context, access) {
      const responseData = await AxiosUser.EditCoupon(access)
      // console.log('response', responseData)
      await context.commit('mutationEditCoupon', responseData)
    },
    async actionsgetDetailCoupon (context, access) {
      const responseData = await AxiosUser.getDetailCoupon(access)
      // console.log('response', responseData)
      await context.commit('mutationgetDetailCoupon', responseData)
    },
    async actionschangeStatusCoupon (context, access) {
      const responseData = await AxiosUser.changeStatusCoupon(access)
      // console.log('response', responseData)
      await context.commit('mutationchangeStatusCoupon', responseData)
    },
    async actionschangeDeleteCoupon (context, access) {
      const responseData = await AxiosUser.changeDeleteCoupon(access)
      // console.log('response', responseData)
      await context.commit('mutationchangeDeleteCoupon', responseData)
    },
    async actionsListProductInShop (context, access) {
      const responseData = await AxiosUser.ListProductInShop(access)
      await context.commit('mutationListProductInShop', responseData)
    }
  }
}
export default ModuleManageCoupon
