<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0 my-0'">
    <v-card elevation="0" width="100%" height="100%">
      <!-- <v-row dense class="mb-6 mt-2">
        <v-col cols="12" md="12" sm="12" xs="12">
          <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="pl-1">รายการสินค้า</span>
        </v-col>
      </v-row> -->
      <v-card-title v-if="!MobileSize" style="font-weight: 700; font-size:24px; line-height: 32px; color: #333333;" class="pt-4">จัดการความคิดเห็น</v-card-title>
      <v-card-title v-else style="font-weight: 600; font-size: 18px; line-height: 22px; color: #333333;" class="pt-6"><v-icon color="#27AB9C" class="mr-2" @click="backToPoseller()">mdi-chevron-left</v-icon> จัดการความคิดเห็น</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="12" sm="12" xs="12">
            <TableProduct />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
import { Decode } from '@/services'
export default {
  components: {
    TableProduct: () => import('@/components/Shop/ManageComments/TableCommentProduct')
  },
  data () {
    return {
      OrderName: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 1, name: 'พร้อมขาย' },
        { key: 2, name: 'หมด' }
      ],
      SelectDataTable: [],
      DataTable: [],
      Shopname: '',
      seller_shop_id: '',
      checkShop: 'ไม่มีร้านค้า',
      userDetail: [],
      onedata: [],
      dataTablecomment: []
    }
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/UPS')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user !== 'eprocurement_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('ChangeActiveMenu', true)
    // this.CheckShop()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageCommentsMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageComments' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  methods: {
    backToPoseller () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async CheckShop () {
      this.$EventBus.$emit('changeNav')
      this.$EventBus.$emit('ChangeActiveMenu', true)
      // this.$EventBus.$on('SetDataShop', this.SetDataShop)
      // await this.$store.dispatch('actionsGetShopData')
      // var response = await this.$store.state.ModuleShop.stateShopData
      // console.log('response shop data====>', response.data)
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var data = {
      //   role_user: dataRole.role
      // }
      // await this.$store.dispatch('actionsUserDetailPage', data)
      // const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
      // console.log('userdetail data', userdetail)
      // this.userdetail = userdetail.data[0].permissions
      // this.SetDataShop()
    }
    // async SetDataShop () {
    //   this.$store.commit('openLoader')
    //   const shopSellerID = localStorage.getItem('shopSellerID').toString()
    //   const dataAll = await {
    //     seller_shop_id: shopSellerID,
    //     stars: '0',
    //     page: '1'
    //   }
    //   await this.$store.dispatch('actionGetallProductReviewSeller', dataAll)
    //   var responseDeleteProduct = await this.$store.state.ModuleOrder.stateGetallProductReviewSeller
    //   console.log('responseDeleteProduct', responseDeleteProduct.data)
    //   if (responseDeleteProduct.code !== 401) {
    //     if (responseDeleteProduct.data.length !== 0) {
    //       this.DataTable = responseDeleteProduct.data
    //       console.log('this.data+1', this.DataTable)
    //       this.$store.commit('closeLoader')
    //       this.checkShop = 'มีร้านค้า'
    //     } else {
    //       this.DataTable = []
    //       console.log('this.data+1', this.DataTable)
    //       this.$store.commit('closeLoader')
    //       this.checkShop = 'มีร้านค้า'
    //     }
    //   } else {
    //     this.$store.commit('closeLoader')
    //     localStorage.removeItem('roleUser')
    //     localStorage.removeItem('roleUserApprove')
    //     localStorage.removeItem('oneData')
    //     localStorage.removeItem('orderNumber')
    //     localStorage.removeItem('orderNumberSeller')
    //     this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
    //     window.location.assign('/')
    //   }
    // }
  }
}
</script>
