<template>
  <v-container>
    <v-card elevation="0" width="100%" height="100%">
      <v-card-text>
        <v-row no-gutters>
          <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3' : 'pl-2 pr-2 mb-3 pt-3'">
            <v-row dense>
              <v-btn icon @click="goBack()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon></v-btn>
              <h2 :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'">รายการสั่งซื้อสินค้าแบบเครดิตเทอม</h2>
            </v-row>
          </v-col>
          <v-col v-if="disableTable === true" cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-2 pt-2' : 'pl-0 pr-2 mb-3 pt-3'">
            <v-row dense>
              <v-col cols="12" md="6" sm="12" xs="12">
                <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากงวดหรือรหัสการสั่งซื้อ" outlined rounded dense hide-details>
                  <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
              </v-col>
              <!-- ปิดเพราะคิด Flow ใหม่จาก SA -->
              <!-- <v-col cols="6" align="end" class="pt-4" v-if="ChangeTermStatus !== ''">
                สถานะคำร้องของวดใหม่ :
                <v-chip dense color="#FAAD14" text-color="#FCF0DA" v-if="ChangeTermStatus === 'Pending'"> รออนุมัติ</v-chip>
                <v-chip dense color="#27AB9C" text-color="#FCF0DA" v-if="ChangeTermStatus === 'Approved'"> อนุมัติ</v-chip>
                <v-chip dense color="#F7D9D9" text-color="#F5222D" v-if="ChangeTermStatus === 'Cancel'"> ยกเลิก</v-chip>
                <v-chip dense color="#F7D9D9" text-color="#F5222D" v-if="ChangeTermStatus === 'Not Approved'"> ไม่อนุมัติ</v-chip>
              </v-col> -->
            </v-row>
          </v-col>
          <v-col cols="12" class="pt-3 pb-3" v-if="disableTable === true">
            <v-row dense>
              <v-col cols="12" md="6" class="pt-2">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'">รายการการชำระเงินแบบเครดิตเทอม {{ DataCount }} รายการ</span>
              </v-col>
              <!-- ปิดเพราะคิด Flow ใหม่จาก SA -->
              <!-- <v-col align="end" cols="12" md="6" sm="12" xs="12" v-if="DataDetail[0].transaction_status !== 'Success'">
                <v-btn dense  color="#27AB9C" class="pl-7 pr-7 mt-0 py-5 white--text" @click="ModalDetailRequest = true, ResetNewRequest()" v-if="ChangeTermStatus === '' || ChangeTermStatus === 'Cancel'"><v-icon>mdi-plus</v-icon>ร้องของวดใหม่</v-btn>
                <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-0 py-5" @click="dialogCancel = true, ResetNewRequest()" v-if="ChangeTermStatus === 'Pending'">ยกเลิกคำร้องขอ</v-btn>
              </v-col> -->
            </v-row>
          </v-col>
          <v-col cols="12" v-if="disableTable === true">
            <v-data-table
              :headers="headersDetail"
              :items="DataDetail"
              :search="search"
              style="width:100%;"
              height="100%"
              :page.sync="page"
              @pagination="countRequest"
              no-results-text="ไม่พบรายการสั่งซื้อ"
              no-data-text="ไม่มีรายการสั่งซื้อ"
              :update:items-per-page="itemsPerPage"
              :items-per-page="10"
              class="elevation-1 mt-4">
              <template v-slot:[`item.credit_term`]="{item}">
                <span>งวดที่ {{item.credit_term}}</span>
              </template>
              <!-- <template v-slot:[`item.total_amount`]="{item}">
                <span>{{ Number(item.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
              </template> -->
              <template v-slot:[`item.total_amount`]="{item}">
                <span>{{ item.total_amount }} </span>
              </template>
              <template v-slot:[`item.paid_datetime`]="{ item }">
                <span>{{ item.paid_datetime === null ? '-' : item.paid_datetime }}</span>
              </template>
              <template v-slot:[`item.due_date`]="{ item }">
                <span>{{ item.due_date === null ? '-' : item.due_date }}</span>
              </template>
              <template v-slot:[`item.transaction_status`]="{ item }">
                <span v-if="item.transaction_status === 'Success'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Not Paid' || item.transaction_status === 'Overdue'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#E5EFFF" text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Fail'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#F5222D" dark>ชำระเงินไม่สำเร็จ</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Cancel'">
                  <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#F5222D" dark>ชำระเงินไม่สำเร็จ</v-chip>
                </span>
              </template>
              <template v-slot:[`item.pdf_path`]="{ item }">
                <v-btn max-width="32px" max-height="32px" color="#ffff" v-if="item.pdf_path !== null" @click="goPDF(item)">
                  <v-icon color="#27AB9C"> mdi-file-document </v-icon>
                </v-btn>
                <div style="color: #27AB9C;" :class="!MobileSize? 'px-12' : 'pa-0 ma-0'" v-else >
                  -
                </div>
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <div :class="!MobileSize? 'px-12' : 'pa-0 ma-0'" :style="{'text-align': MobileSize ? 'left': '' }" v-if="item.transaction_status === 'Success'">
                  <v-icon color="#27AB9C">mdi-check</v-icon>
                </div>
                <v-btn
                  :disabled = "(ChangeTermStatus === 'Pending' || item.valid2pay === 'cant pay' || item.can_payment === 'no') ? true : false"
                  text rounded color="#27AB9C" :class="!MobileSize? 'px-12' : 'pa-0 ma-0'" small @click="goPaid(item.order_number)" v-else>
                  <b>ชำระเงิน</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-col>
          <v-col cols="12" v-if="disableTable === false" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการเครดิตเทอม</b></h2>
          </v-col>

          <!-- Modal show detail of request -->
          <v-dialog v-model="ModalDetailRequest" width="730px" persistent>
            <v-form ref="form" lazy-validation>
              <v-card width="100%" :height="loading ? '320px' : '100%'" class="rounded-lg">
                <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                  <span class="flex text-center ml-5" style="font-size:20px">
                    <font color="#27AB9C">ร้องขอเปลี่ยนแปลงงวด</font>
                  </span>
                  <v-btn icon dark @click="ClosseModalDetailRequest()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                  </v-btn>
                </v-toolbar>
                <v-card-text>
                  <v-row class="mt-2" dense>
                    <v-col cols="12" md="12" class="pb-0">
                      <v-row dense class="pb-0">
                        <v-col cols="12" md="6" sm="12">
                          <v-row dense>
                            <v-col cols="12" md="4" sm="12" class="mt-2 mr-0 ml-0">
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ราคาต่องวด : </p>
                            </v-col>
                            <v-col cols="12" md="7" sm="12" class="px-0 ml-0">
                              <v-autocomplete
                                v-model="SelectedTypeValue"
                                @change="SelectType()"
                                :items="TypeValue"
                                :rules="[v => !!v || 'กรุณาเลือกราคาต่องวด']"
                                item-text="text"
                                item-value="value"
                                dense
                                filled
                                hide-details
                              ></v-autocomplete>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" class="pb-0">
                          <v-row dense>
                            <v-col cols="12" md="4" sm="12" class="mt-2 mr-0">
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">จำนวนงวด : </p>
                            </v-col>
                            <v-col cols="12" md="7" sm="12" class="px-0 ml-0">
                              <v-autocomplete
                                v-model="SelectedTermValue"
                                :items="TermValue"
                                @change="SelectTerm()"
                                :rules="[v => !!v || 'กรุณาเลือกจำนวนงวด']"
                                dense
                                filled
                                hide-details
                              ></v-autocomplete>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-row justify="center">
                      <v-col align="center" class="mt-12">
                        <v-progress-circular
                        :active="loading"
                        :indeterminate="loading"
                        absolute
                        bottom
                        :size="100"
                        color="#27AB9C"
                        v-if="loading && Term === 1"
                      ></v-progress-circular>
                      </v-col>
                    </v-row>
                    <v-col cols="12" md="12" sm="12" class="pt-0" v-if="!loading">
                      <v-row dense>
                        <v-col cols="12">
                          <v-data-table
                          :headers="headersTerm"
                          :items="DataTerm"
                          color="blue-grey lighten-2"
                          style="width:100%;"
                          height="100%"
                          no-data-text="ไม่มีรายการร้องขอเปลี่ยนงวด"
                          class="elevation-1 mt-4 row-height-180"
                          hide-default-footer
                          :items-per-page="12"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                          >
                          <template v-slot:[`item.term`]="{item}">
                            <span>งวดที่ {{item.term}}</span>
                          </template>
                          <template v-slot:[`item.term_price`]="{item, index}">
                            <div class="mt-4">
                              <v-text-field
                              :readonly="Type === 'Normal' ? true : index === DataTerm.length - 1 ? true : false"
                              v-model="item.term_price"
                              oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"
                              :rules="index !== DataTerm.length - 1 ? Rules.number: ''"
                              @keypress="onlyForCurrency($event, index)"
                              @keyup="CalWithPrice()"
                              outlined
                              dense
                              class=""
                              ></v-text-field>
                            </div>
                          </template>
                          </v-data-table>
                        </v-col>
                      </v-row>
                      <v-row dense style="background-color:#FAFBFB; border-radius:8px;">
                        <v-col align="right">
                          <span  style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">ราคารวมทั้งสิ้น </span>
                          <span  style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;"> <b>{{Total}}</b> </span>
                          <span  style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"> บาท</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col align="right">
                          <v-btn dense dark outlined color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--tex" @click="ModalDetailRequest = false">
                            ยกเลิก
                          </v-btn>
                          <v-btn :disabled="SelectedTypeValue !== '' && SelectedTermValue !== '' ? false : true" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="CheckCalculate()">
                            ร้องขอ
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-form>
          </v-dialog>
          <!-- dialog comfirm Approve -->
          <v-dialog  v-model="dialogApprove" width="730px" persistent>
            <v-card class="rounded-lg">
              <v-toolbar color="#BDE7D9"  dark dense>
                <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">ยืนยันการร้องขอ</font></span>
                <v-btn
                  icon
                  dark
                  @click="dialogApprove = false"
                  >
                  <v-icon  color="#27AB9C">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <br/><br/>
              <v-card-text >
                <v-row dense justify="start">
                  <v-col cols="12" md="12" sm="12">
                    <v-row dense>
                      <v-col cols="12" md="12" sm="12" class="mt-2 mr-0 ml-0">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เหตุผลการร้องขอ : </p>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="px-0 ml-0">
                        <v-autocomplete
                          v-model="SelectedReason"
                          :items="ReasonValue"
                          dense
                          filled
                          auto-select-first
                        ></v-autocomplete>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-row dense>
                      <v-col cols="12" md="12" sm="12" class="mt-2 mr-0">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">คำอธิบาย </p>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="px-0 ml-0">
                        <v-textarea
                          v-model="Description"
                          counter="250"
                          dense
                          outlined
                          maxLength="250"
                        ></v-textarea>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions >
            <v-row justify="end" dense class="mb-4">
              <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogApprove = false">ยกเลิก</v-btn>
              <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Request()">ตกลง</v-btn>
            </v-row>
            </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- dialog comfirm Cancel -->
          <v-dialog  v-model="dialogCancel" width="450px" persistent>
            <v-card align="center" class="rounded-lg">
              <v-toolbar color="#BDE7D9"  dark dense>
                <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">ยืนยันยกเลิกการร้องขอ</font></span>
                <v-btn
                  icon
                  dark
                  @click="dialogCancel = false"
                  >
                  <v-icon  color="#27AB9C">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <br/><br/>
              <v-card-text >
                <span>
                  คุณต้องการยกเลิกคำขอนี้<br/>
                  คุณต้องการทำรายการนี้ ใช่ หรือไม่
                </span>
              </v-card-text>
              <v-card-actions >
            <v-row justify="center" dense class="mb-4">
              <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogApprove = false">ยกเลิก</v-btn>
              <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Cancel()">ตกลง</v-btn>
            </v-row>
            </v-card-actions>
            </v-card>
          </v-dialog>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      loading: false,
      timer: '',
      Rules: {
        number: [
          v => !!v || 'กรุณากรอกจำนวนเงิน'
        ]
      },
      compDetail: '',
      order_number: '',
      // modal , dialog, table
      disableTable: false,
      ModalDetailRequest: false,
      dialogApprove: false,
      dialogCancel: false,

      // table detail credit term--------------------------------------------------------
      search: '',
      page: 1,
      pageCount: 0,
      itemsPerPage: 10,
      DataCount: 0,
      DataDetail: [],
      headersDetail: [
        { text: 'งวด', value: 'credit_term', sortable: false, align: 'center', width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_credit_term_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนเงิน', value: 'total_amount', filterable: false, sortable: false, align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', filterable: false, width: '150px', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วัน/เวลากำหนดชำระ', value: 'due_date', filterable: false, align: 'center', width: '150px', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการชำระเงิน', value: 'transaction_status', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ใบแจ้งหนี้', value: 'pdf_path', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],

      // Modal Request New Credit Term--------------------------------------------------------
      TermValue: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      TypeValue: [{ text: 'ราคาเท่ากันทุกงวด', value: 'Normal' }, { text: 'กำหนดราคาเอง', value: 'WithPrice' }],
      ReasonValue: ['ร้องของวดใหม่'],
      SelectedTermValue: '',
      SelectedTypeValue: '',
      SelectedReason: 'ร้องของวดใหม่',
      Description: '',
      Type: '',
      Term: '',
      headersTerm: [
        { text: 'งวดที่', value: 'term', width: '40%', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนเงิน', value: 'term_price', width: '20%', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      DataTerm: [],
      Total: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.compDetail = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.order_number = this.$route.query.order_number
      // console.log('เข้า Created')
      this.CreditTermDetail()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      var number = JSON.parse(localStorage.getItem('creditTermOrderNumber'))
      if (val === true) {
        this.$router.push({ path: `/companyListCreditTermMobile?order_number=${number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/companyListCreditTerm?order_number=${number}` }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    checkDate (date) {
      // console.log(date)
      if (date !== null) {
        return new Date(date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })
      } else {
        return null
      }
    },
    checkPayment (creditTerm, data) {
      creditTerm = parseInt(creditTerm)
      var index = creditTerm - 2
      if (creditTerm > 1) {
        if (data[index].transaction_status === 'Not Paid') {
          return 'cant pay'
        } if (data[index].transaction_status === 'Success') {
          return 'can pay'
        }
      } else if (creditTerm === 1) {
        return 'can pay'
      }
    },
    async CreditTermDetail () {
      var data2sent = {
        company_id: this.compDetail.id,
        order_number: this.order_number
      }
      await this.$store.dispatch('actionsListCreditTerm', data2sent)
      var response = await this.$store.state.ModuleAdminManage.stateListCreditTerm
      if (response.result === 'SUCCESS') {
        this.disableTable = true
        this.ChangeTermStatus = await response.data.change_term_status
        // this.DataDetail = response.data.list_credit_term
        this.DataDetail = await response.data.list_credit_term.map(x => {
          return {
            credit_term: x.credit_term,
            payment_credit_term_number: x.payment_credit_term_number,
            paid_datetime: this.checkDate(x.paid_datetime),
            due_date: this.checkDate(x.due_date),
            // due_date: new Date(x.due_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }),
            total_amount: x.total_amount,
            transaction_status: x.transaction_status,
            pdf_path: x.pdf_path,
            order_number: x.order_number,
            valid2pay: this.checkPayment(x.credit_term, response.data.list_credit_term),
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            buyer_name: x.buyer_name,
            can_payment: x.can_payment
          }
        })
        // console.log(this.DataDetail)
        this.DataCount = this.DataDetail.length
      }
      // console.log('data send API', data2sent)
      // console.log('res from API', response)
    },
    getRequest (item) {
      this.StateStatus = item
      // i dnk
      this.page = 1
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async goPaid (item) {
      var dataPayment = {
        payment_transaction_number: item
      }
      await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
      var resRedirect = this.$store.state.ModuleCart.stateGetPaymentPage
      localStorage.setItem('PaymentData', Encode.encode(resRedirect))
      this.$router.push('/RedirectPaymentPage')
      // this.$router.push({ path: '/' }).catch(() => {})
      // await this.$router.push({ path: '/checkout' }).catch(() => {})
    },
    async goPDF (item) {
      localStorage.setItem('creditTerm', Encode.encode(item))
      if (this.MobileSize === true) {
        await this.$router.push({ path: `/invoicePDFMobile?order_number=${item.order_number}` }).catch(() => {})
      } else {
        await this.$router.push({ path: `/invoicePDF?order_number=${item.order_number}` }).catch(() => {})
      }
    },
    goBack () {
      localStorage.removeItem('creditTerm')
      if (this.MobileSize === true) {
        this.$router.push({ path: '/companyListCreditOrderMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/companyListCreditOrder' }).catch(() => {})
      }
    },
    async ResetNewRequest () {
      this.SelectedTermValue = ''
      this.SelectedTypeValue = ''
      // this.SelectedReason = ''
      this.Description = ''
      this.Type = ''
      this.Term = ''
      this.DataTerm = []
      this.Total = ''
      this.$refs.form.resetValidation()
    },
    SelectType () {
      this.$refs.form.resetValidation()
      this.Type = ''
      this.Type = this.SelectedTypeValue
      this.AddDataTable()
    },
    SelectTerm () {
      this.$refs.form.resetValidation()
      this.Term = ''
      this.Term = this.SelectedTermValue
      this.AddDataTable()
    },
    async AddDataTable () {
      this.DataTerm = []
      if (this.Type === 'Normal') {
        var DataSentAPI = {
          order_number: this.order_number,
          term: this.Term,
          term_price: [],
          type: this.Type
        }
        await this.$store.dispatch('actionsCalculateRequestCreditTerm', DataSentAPI)
        var response = this.$store.state.ModuleAdminManage.stateCalculateRequestCreditTerm
        if (response.result === 'SUCCESS') {
          var ShowData = []
          for (let i = 0; i < this.Term; i++) {
            ShowData.push({
              term: ('0' + (i + 1)).toString().slice(-2),
              term_price: response.data.term_price[i]
            })
          }
          this.DataTerm = response.data.term_price
          this.Total = response.data.total_amount
        }
      } else if (this.Type === 'WithPrice') {
        var ListItemPrice = []
        for (let i = 0; i < this.Term; i++) {
          ListItemPrice.push({
            term: ('0' + (i + 1)).toString().slice(-2),
            term_price: ''
          })
        }
        if (this.Term === 1) {
          this.loading = true
          // this.CalWithPrice()
          var ListItemPrice1 = []
          for (var i = 0; i < this.DataTerm.length - 1; i++) {
            if (this.DataTerm[i].term_price !== '') {
              ListItemPrice1.push({
                term: this.DataTerm[i].term,
                term_price: this.limitDecimalPlaces(this.DataTerm[i].term_price, 2)
              })
            } else {
              ListItemPrice1.push({
                term: this.DataTerm[i].term,
                term_price: ''
              })
            }
          }
          var DataSentAPI1 = {
            order_number: this.order_number,
            term: this.SelectedTermValue,
            term_price: ListItemPrice1,
            type: this.Type
          }
          await this.$store.dispatch('actionsCalculateRequestCreditTerm', DataSentAPI1)
          var response1 = this.$store.state.ModuleAdminManage.stateCalculateRequestCreditTerm
          this.loading = false
          if (response1.result === 'SUCCESS') {
            this.DataTerm = response1.data.term_price
            this.Total = response1.data.total_amount
            var NewDataTerm = []
            for (let i = 0; i < this.DataTerm.length; i++) {
              if (this.DataTerm[i].term_price !== null) { // ถ้า term price ไม่ใช่ค่า null
                if (this.DataTerm[i].term_price.substring(0, 1) === '0') { // ถ้า term price ขึ้นต้นด้วย 0
                  if (this.DataTerm[i].term_price.length >= 2) { // ถ้า term price lenght มากว่า2
                    if (this.DataTerm[i].term_price.substring(0, 2) === '0.') {
                      NewDataTerm.push({
                        term: this.DataTerm[i].term,
                        term_price: this.DataTerm[i].term_price
                      })
                    } else {
                      NewDataTerm.push({
                        term: this.DataTerm[i].term,
                        term_price: this.DataTerm[i].term_price.substring(1)
                      })
                    }
                  } else {
                    NewDataTerm.push({
                      term: this.DataTerm[i].term,
                      term_price: this.DataTerm[i].term_price
                    })
                  }
                } else { // term price ไม่ขึ้นต้นด้วย 0 ไม่แก้อะไร
                  NewDataTerm.push({
                    term: this.DataTerm[i].term,
                    term_price: this.DataTerm[i].term_price
                  })
                }
              } else { // ถ้า term price null ไม่แก้อะไร
                NewDataTerm.push({
                  term: this.DataTerm[i].term,
                  term_price: this.DataTerm[i].term_price === null ? '' : this.DataTerm[i].term_price
                })
              }
            }
            this.DataTerm = NewDataTerm
          } else {
            if (response1.message === 'Price More Than Total Amount.') {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: 'ราคาเกินราคารวมสุทธิ' })
            }
          }
        } else {
          this.DataTerm = ListItemPrice
        }
      }
      // console.log('Data Sent 2 API', DataSentAPI)
      // console.log('Res fm API', response)
    },
    onlyForCurrency (e, val) {
      const keyCode = (e.keyCode ? e.keyCode : e.which)
      if (keyCode === 46) {
        this.DataTerm[val].term_price = this.DataTerm[val].term_price.replace(/^\s+/, '')
        if (this.DataTerm[val].term_price.length === 0) {
          e.preventDefault()
        }
      }
    },
    async CalWithPrice () {
      var ListItemPrice = []
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        for (var i = 0; i < this.DataTerm.length - 1; i++) {
          if (this.DataTerm[i].term_price !== '') {
            ListItemPrice.push({
              term: this.DataTerm[i].term,
              term_price: this.limitDecimalPlaces(this.DataTerm[i].term_price, 2)
            })
          } else {
            ListItemPrice.push({
              term: this.DataTerm[i].term,
              term_price: ''
            })
          }
        }
        var DataSentAPI = {
          order_number: this.order_number,
          term: this.SelectedTermValue,
          term_price: ListItemPrice,
          type: this.Type
        }
        await this.$store.dispatch('actionsCalculateRequestCreditTerm', DataSentAPI)
        var response = this.$store.state.ModuleAdminManage.stateCalculateRequestCreditTerm
        if (response.result === 'SUCCESS') {
          this.DataTerm = response.data.term_price
          this.Total = response.data.total_amount
          var NewDataTerm = []
          for (let i = 0; i < this.DataTerm.length; i++) {
            if (this.DataTerm[i].term_price !== null) { // ถ้า term price ไม่ใช่ค่า null
              if (this.DataTerm[i].term_price.substring(0, 1) === '0') { // ถ้า term price ขึ้นต้นด้วย 0
                if (this.DataTerm[i].term_price.length >= 2) { // ถ้า term price lenght มากว่า2
                  if (this.DataTerm[i].term_price.substring(0, 2) === '0.') {
                    NewDataTerm.push({
                      term: this.DataTerm[i].term,
                      term_price: this.DataTerm[i].term_price
                    })
                  } else {
                    NewDataTerm.push({
                      term: this.DataTerm[i].term,
                      term_price: this.DataTerm[i].term_price.substring(1)
                    })
                  }
                } else {
                  NewDataTerm.push({
                    term: this.DataTerm[i].term,
                    term_price: this.DataTerm[i].term_price
                  })
                }
              // if (this.DataTerm[i].term_price.substring(0, 1) === '0' || this.DataTerm[i].term_price.substring(0, 2) === '0.') { // term price ขึ้นต้นด้วย 0
                // NewDataTerm.push({
                //   term: this.DataTerm[i].term,
                //   term_price: this.DataTerm[i].term_price.substring(0, 2) === '0.' ? this.DataTerm[i].term_price : '0'
                // })
              } else { // term price ไม่ขึ้นต้นด้วย 0 ไม่แก้อะไร
                NewDataTerm.push({
                  term: this.DataTerm[i].term,
                  term_price: this.DataTerm[i].term_price
                })
              }
            } else { // ถ้า term price null ไม่แก้อะไร
              NewDataTerm.push({
                term: this.DataTerm[i].term,
                term_price: this.DataTerm[i].term_price === null ? '' : this.DataTerm[i].term_price
              })
            }
          }
          this.DataTerm = NewDataTerm
          // var length = this.DataTerm.length
          // this.DataTerm[length - 1].term_price = response.data.term_price
        } else {
          if (response.message === 'Price More Than Total Amount.') {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: 'ราคาเกินราคารวมสุทธิ' })
          }
        }
        this.loading = false
      }, 2300)
    },
    limitDecimalPlaces (e, count) {
      if (e !== null) {
        if (e.indexOf('.') === -1) { return parseFloat(e).toFixed(2) }
        if ((e.length - e.indexOf('.')) > count) {
          return parseFloat(e).toFixed(count)
        } else if (((e.length - e.indexOf('.') === 1) || (e.length - e.indexOf('.') === 2))) {
          return parseFloat(e).toFixed(2)
        }
      } else {
        return ''
      }
    },
    async CheckCalculate () {
      var DataSentAPI = {
        order_number: this.order_number,
        term: this.SelectedTermValue,
        term_price: this.DataTerm
      }
      await this.$store.dispatch('actionsCheckCalculate', DataSentAPI)
      var response = this.$store.state.ModuleAdminManage.stateCheckCalculate
      if (response.result === 'SUCCESS') {
        this.OpenDialog()
      } else {
        if (response.message === 'Price More Than Total Amount.') {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: 'ราคาเกินราคารวมสุทธิ' })
        }
      }
      // console.log('Data Sent 2 API', DataSentAPI)
      // console.log('Res fm API', response)
    },
    async OpenDialog () {
      if (this.$refs.form.validate(true)) {
        this.dialogApprove = true
        this.ModalDetailRequest = false
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async Request () {
      // console.log('เข้า Request')
      var DataSentAPI = {
        order_number: this.order_number,
        company_id: this.compDetail.id,
        request_term: this.Term,
        price_list: this.DataTerm,
        reason: this.SelectedReason,
        description: this.Description,
        type: this.Type
      }
      await this.$store.dispatch('actionsRequestChangeTerm', DataSentAPI)
      var response = this.$store.state.ModuleAdminManage.stateRequestChangeTerm
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ text: 'ร้องคำขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
      } else if (response.result === 'FAILED') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: response.message
        })
      }
      this.dialogApprove = false
      this.CreditTermDetail()
      this.SelectedTermValue = ''
      this.SelectedTypeValue = ''
      // console.log('Data Sent to API: ', DataSentAPI)
      // console.log('Result fm API: ', response)
    },
    async Cancel () {
      // console.log('เข้า Cancel')
      var dataDetail = {
        order_number: this.order_number,
        company_id: this.compDetail.id,
        status_approval: 'Cancel'
      }
      await this.$store.dispatch('actionsCancelRequestChangeTerm', dataDetail)
      var response = await this.$store.state.ModuleAdminManage.stateCancelRequestChangeTerm
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ text: 'ยกเลิกคำขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.dialogCancel = false
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: `ยกเลิกไม่สำเร็จ ${response.message}`
        })
      }
      this.DetailList = []
      this.CreditTermDetail()
      // console.log('send data 2 Approve API list', dataDetail)
      // console.log('res data fm Approve API list', response)
    },
    ClosseModalDetailRequest () {
      this.SelectedTermValue = ''
      this.SelectedTypeValue = ''
      this.$refs.form.resetValidation()
      this.ModalDetailRequest = false
    }
  }
}
</script>

<style scoped>
.fontSizeTitle {
  font-weight: 700; font-size: 20px; line-height: 24px; color: #333333; padding-top: 5px;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 16px; line-height: 33px; color: #333333;
}
.fontSizeTitle2 {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile2 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #636363 !important;
}
</style>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(8) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(8) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
