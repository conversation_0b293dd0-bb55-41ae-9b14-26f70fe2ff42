<template>
  <v-container>
  <v-col class="pt-6">
    <span class="f-left ml-3" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">จัดการ Landing Page</span>
    <br>
    <v-col>
      <div class="d-flex justify-space-between align-center">
        <span class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 22px;">Banner หลัก</span>
        <v-btn color="#27AB9C" text dark style="font-size:16px;" @click="EditBigBanner"><v-icon class="pr-2" size="20"  >mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
      </div>
        <v-card v-if="BigBanner.length !== 0" no-gutter width="100%" class="rounded-lg mt-5" style="padding: 10px 14px 10px 14px;">
      <v-carousel cycle
          height="414"
          hide-delimiter-background
          show-arrows-on-hover
          style="margin-top: 10px;"
          class="pa-0 rounded-lg">
        <v-carousel-item
          v-for="(item,i) in BigBanner"
          :key="i"
          class="rounded-lg"
        >
        <v-img v-if="item.link_banner !== '-'" v-lazyload loading='lazy' class="rounded-lg" style=" cursor: pointer;" :src="item.image_path" height="414" width="100%" :lazy-src="item.image_path_lazy"  @click="navigateToLink(item.link_banner)" ></v-img>
        <v-img v-if="item.link_banner === '-'" v-lazyload loading='lazy' class="rounded-lg" :src="item.image_path" height="414" width="100%" :lazy-src="item.image_path_lazy" ></v-img>
        </v-carousel-item>
      </v-carousel>
        </v-card>
        <v-row class="mt-5" dense v-if="BigBanner.length === 0">
        <v-col cols="12" align="center">
          <v-row dense v-if="showSkeletonLoader === true">
            <v-col>
              <v-skeleton-loader
                max-width="100%"
                v-bind="attrs"
                type="image"
              ></v-skeleton-loader>
            </v-col>
          </v-row>
          <v-card v-if="showSkeletonLoader === false" outlined width="100%" height="300" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; display: flex; justify-content: center; align-items: center;" >
            <span style="font-size: 18px;">ไม่มีรูปภาพ Banner</span>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
    <v-col>
      <div class="d-flex justify-space-between align-center">
        <span class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 22px;">Banner รอง 1</span>
          <v-btn color="#27AB9C" text dark style="font-size:16px;" @click="EditBigBannerA" ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
      </div>
      <v-img v-if="Banner1.length !== 0 && Banner1[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg mt-5" :src="Banner1[0].image_path" :lazy-src="Banner1[0].image_path_lazy" @click="navigateToLink(Banner1[0].link_banner)" height="270"></v-img>
      <v-img v-if="Banner1.length !== 0 && Banner1[0].link_banner === '-'" class="rounded-lg mt-5" :src="Banner1[0].image_path" :lazy-src="Banner1[0].image_path_lazy" height="270"></v-img>
      <v-row class="mt-5" dense v-if="Banner1.length === 0">
        <v-col cols="12" align="center">
          <v-row dense v-if="showSkeletonLoader === true">
            <v-col>
              <v-skeleton-loader
                max-width="100%"
                v-bind="attrs"
                type="image"
              ></v-skeleton-loader>
            </v-col>
          </v-row>
          <v-card v-if="showSkeletonLoader === false" outlined width="100%" height="300" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; display: flex; justify-content: center; align-items: center;" >
            <span style="font-size: 18px;">ไม่มีรูปภาพ Banner</span>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
    <v-col>
      <div class="d-flex justify-space-between align-center">
        <span class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 22px;">Banner รอง 2</span>
          <v-btn color="#27AB9C" text dark style="font-size:16px;" @click="EditBigBannerB" ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
      </div>
      <v-img v-if="Banner2.length !== 0 && Banner2[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg mt-5" :src="Banner2[0].image_path" :lazy-src="Banner2[0].image_path_lazy" @click="navigateToLink(Banner2[0].link_banner)" height="270" ></v-img>
      <v-img v-if="Banner2.length !== 0 && Banner2[0].link_banner === '-'" class="rounded-lg mt-5" :src="Banner2[0].image_path" :lazy-src="Banner2[0].image_path_lazy" height="270"></v-img>
      <v-row class="mt-5" dense v-if="Banner2.length === 0">
        <v-col cols="12" align="center">
          <v-row dense v-if="showSkeletonLoader === true">
            <v-col>
              <v-skeleton-loader
                max-width="100%"
                v-bind="attrs"
                type="image"
              ></v-skeleton-loader>
            </v-col>
          </v-row>
          <v-card v-if="showSkeletonLoader === false" outlined width="100%" height="300" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; display: flex; justify-content: center; align-items: center;" >
            <span style="font-size: 18px;">ไม่มีรูปภาพ Banner</span>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
    <v-col>
      <div class="d-flex justify-space-between align-center">
        <span class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 22px;">Banner รอง 3</span>
          <v-btn color="#27AB9C" text dark style="font-size:16px;" @click="EditBigBannerC" ><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
      </div>
      <v-img v-if="Banner3.length === 1 && Banner3[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg mt-5" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)" height="270" ></v-img>
      <v-img v-if="Banner3.length === 1 && Banner3[0].link_banner === '-'" class="rounded-lg mt-5" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" height="270" ></v-img>
      <div v-if="Banner3.length === 2" >
        <v-row class="mb-6 mt-5" >
          <v-col cols="6">
            <v-img v-if="Banner3[0].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-width="auto" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy" @click="navigateToLink(Banner3[0].link_banner)"></v-img>
            <v-img v-if="Banner3[0].link_banner === '-'" class="rounded-lg" max-width="auto" :src="Banner3[0].image_path" :lazy-src="Banner3[0].image_path_lazy"></v-img>
          </v-col>
          <v-col cols="6">
            <v-img v-if="Banner3[1].link_banner !== '-'" class="rounded-lg" style="cursor: pointer; " max-width="auto" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy" @click="navigateToLink(Banner3[1].link_banner)"></v-img>
            <v-img v-if="Banner3[1].link_banner === '-'" class="rounded-lg" max-width="auto" :src="Banner3[1].image_path" :lazy-src="Banner3[1].image_path_lazy"></v-img>
          </v-col>
        </v-row>
      </div>
      <v-row class="mt-5" dense v-if="Banner3.length === 0">
        <v-col cols="12" align="center">
          <v-row dense v-if="showSkeletonLoader === true">
            <v-col>
              <v-skeleton-loader
                max-width="100%"
                v-bind="attrs"
                type="image"
              ></v-skeleton-loader>
            </v-col>
          </v-row>
          <v-card v-if="showSkeletonLoader === false" outlined width="100%" height="300" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; display: flex; justify-content: center; align-items: center;" >
            <span style="font-size: 18px;">ไม่มีรูปภาพ Banner</span>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
  </v-col>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      showSkeletonLoader: false,
      BigBanner: [],
      Banner1: [],
      Banner2: [],
      Banner3: []
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    // this.$EventBus.$emit('changeNav')
    this.showSkeletonLoader = true
    await this.getDetailShop()
    // console.log(this.BigBanner)
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminBannerManageMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'adminBannerManage')
        this.$router.push({ path: '/adminBannerManage' }).catch(() => {})
      }
    }
  },
  methods: {
    navigateToLink (link) {
      window.location.href = link
    },
    async getDetailShop () {
      await this.$store.dispatch('actionsDetailLandingPage')
      var response = await this.$store.state.ModuleAdminManage.stateDetailLandingPage
      if (response.result === 'SUCCESS') {
        // console.log(response)
        if (response.data.image_big_banner_web.length > 0) {
          for (let i = 0; i < response.data.image_big_banner_web.length; i++) {
            this.BigBanner.push({
              image_path: response.data.image_big_banner_web[i].path,
              image_path_lazy: response.data.image_big_banner_web[i].path_lazy,
              link_banner: response.data.image_big_banner_web[i].href
            })
          }
          // console.log(this.BigBanner)
        } else {
          this.BigBanner = []
        }
        if (response.data.image_banner_1.length > 0) {
          for (let i = 0; i < response.data.image_banner_1.length; i++) {
            this.Banner1.push({
              image_path: response.data.image_banner_1[i].path,
              image_path_lazy: response.data.image_banner_1[i].path_lazy,
              link_banner: response.data.image_banner_1[i].href
            })
          }
        } else {
          this.Banner1 = []
        }
        if (response.data.image_banner_2.length > 0) {
          for (let i = 0; i < response.data.image_banner_2.length; i++) {
            this.Banner2.push({
              image_path: response.data.image_banner_2[i].path,
              image_path_lazy: response.data.image_banner_2[i].path_lazy,
              link_banner: response.data.image_banner_2[i].href
            })
          }
        } else {
          this.Banner2 = []
        }
        if (response.data.image_banner_3.length > 0) {
          for (let i = 0; i < response.data.image_banner_3.length; i++) {
            this.Banner3.push({
              image_path: response.data.image_banner_3[i].path,
              image_path_lazy: response.data.image_banner_3[i].path_lazy,
              link_banner: response.data.image_banner_3[i].href
            })
          }
        } else {
          this.Banner3 = []
        }
        this.showSkeletonLoader = false
      }
    },
    EditBigBanner () {
      this.$router.push({ path: '/BigBannerEdit' }).catch(() => { })
    },
    EditBigBannerA () {
      this.$router.push({ path: '/BannerAEdit' }).catch(() => { })
    },
    EditBigBannerB () {
      this.$router.push({ path: '/BannerBEdit' }).catch(() => { })
    },
    EditBigBannerC () {
      this.$router.push({ path: '/BannerCEdit' }).catch(() => { })
    }
  }
}
</script>
