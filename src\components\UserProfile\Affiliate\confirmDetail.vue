<template>
<v-container class="pa-4">
  <v-card :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']" elevation="0">
      <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
        รายละเอียดข้อมูล
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        รายละเอียดข้อมูล
      </v-card-title>

      <v-divider class="my-4"></v-divider>
      <div class="px-6">
        <span class="text-start" style="font-size: 20px; font-weight: 700;">ประเภทบัญชี</span>

        <v-form ref="FormbuyerDetailType" :lazy-validation="lazy" v-model="formValid">
          <v-row>
              <v-col cols="12" class="mt-4 reduce-spacing">
                <span class="labelInputSize">ประเภทบัญชี <span style="color: red;">*</span></span>
                <v-text-field
                  :value="account_type === 'savings' ? 'ออมทรัพย์' : account_type === 'current' ? 'ฝากประจำ' : ''"
                  disabled
                  outlined
                  dense>
                </v-text-field>
            </v-col>
          </v-row>
        </v-form>

        <v-divider class="my-4"></v-divider>

        <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลการชำระเงิน</span>

        <v-form ref="FormbuyerDetailPay" :lazy-validation="lazy" v-model="formValid">
          <v-row>
            <v-col cols="6" class="mt-4 reduce-spacing">
              <span>ชื่อบัญชี <span style="color: red;">*</span></span>
              <v-text-field v-model="bank_username" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="6" class="mt-4 reduce-spacing">
              <span>ชื่อธนาคาร <span style="color: red;">*</span></span>
              <v-text-field v-model="bank_name" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="6" class="reduce-spacing">
              <span>ชื่อสาขาธนาคาร <span style="color: red;">*</span></span>
              <v-text-field v-model="bank_branch" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="6" class="reduce-spacing">
              <span>หมายเลขบัญชีธนาคาร <span style="color: red;">*</span></span>
              <v-text-field v-model="bank_no" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>รูปหน้าบัญชีธนาคาร <span style="color: red;">*</span></span>
              <v-card
                v-if="bookbankImageUrl"
                class="d-flex justify-center align-center mb-6"
                style="padding: 10px; max-width: 300px; max-height: 300px; overflow: hidden;"
              >
                <v-img :src="bookbankImageUrl" style="width: 100%; height: 100%; object-fit: contain;"></v-img>
              </v-card>
            </v-col>
          </v-row>
        </v-form>

        <v-divider class="my-4"></v-divider>

        <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลบัญชีโซเชียลมีเดีย</span>

        <v-form ref="FormbuyerDetailSocail" :lazy-validation="lazy" v-model="formValid">
          <v-row>
            <v-col cols="12" class="mt-4 reduce-spacing">
              <span>Facebook</span>
              <v-text-field v-model="facebook_link" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>TikTok</span>
              <v-text-field v-model="tiktok_link" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>Youtube</span>
              <v-text-field v-model="youtube_link" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>Instagram</span>
              <v-text-field v-model="instagram_link" disabled outlined dense></v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>Line</span>
              <v-text-field v-model="line_link" disabled outlined dense></v-text-field>
            </v-col>
          </v-row>
        </v-form>

         <v-card-actions style="padding-top: 20px;">
          <v-btn class="px-5" outlined color="#27AB9C" @click="beforeDetailSocail()">ย้อนกลับ</v-btn>
          <v-spacer></v-spacer>
          <v-btn class="px-5 white--text" color="#27AB9C" @click="confirmDetail()">ยืนยันการเข้าร่วม</v-btn>
        </v-card-actions>

      </div>
  </v-card>
</v-container>
</template>

<script>
import { Decode } from '@/services'

export default {
  data () {
    return {
      lazy: false,
      formValid: false,
      account_type: '',
      bank_username: '',
      bank_name: '',
      bank_branch: '',
      bank_no: '',
      bookbankImage: null,
      bookbankImageUrl: null,
      bookbank_image_old: '',
      facebook_link: '',
      tiktok_link: '',
      youtube_link: '',
      instagram_link: '',
      line_link: ''
    }
  },
  mounted () {
    this.confirmDetailAll()
  },
  computed: {
    MobileSize () {
      return this.$vuetify.breakpoint.xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    confirmDetailAll () {
      var dataDetail = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay
      var object = {
        ...dataDetail[0],
        ...dataDetail[1]
      }

      this.account_type = object.account_type
      this.bank_username = object.bank_username
      this.bank_name = object.bank_name
      this.bank_branch = object.bank_branch
      this.bank_no = object.bank_no
      this.bookbank_image = object.bookbank_image
      this.bookbankImageUrl = object.bookbankImageUrl
      this.facebook_link = object.facebook_link
      this.tiktok_link = object.tiktok_link
      this.youtube_link = object.youtube_link
      this.instagram_link = object.instagram_link
      this.line_link = object.line_link
    },
    async confirmDetail () {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const formData = new FormData()
      formData.append('user_id', onedata.user.user_id)
      formData.append('bank_name', this.bank_name)
      formData.append('bank_branch', this.bank_branch)
      formData.append('bank_username', this.bank_username)
      formData.append('bank_no', this.bank_no)
      formData.append('account_type', this.account_type)
      formData.append('bookbank_image', this.bookbank_image)
      formData.append('facebook_link', this.facebook_link)
      formData.append('tiktok_link', this.tiktok_link)
      formData.append('youtube_link', this.youtube_link)
      formData.append('instagram_link', this.instagram_link)
      formData.append('line_link', this.line_link)

      await this.$store.dispatch('actionsAffiliateBuyerCreateDetail', formData)
      const responseBuyerCreateDetail = await this.$store.state.ModuleAffiliate.stateAffiliateBuyerCreateDetail
      console.log('Buyer Create Detail Response:', responseBuyerCreateDetail)

      var data = {
        user_id: onedata.user.user_id,
        approve_consent: true
      }

      await this.$store.dispatch('actionsAffiliateConfirmJoin', data)
      const responseConfirmJoin = await this.$store.state.ModuleAffiliate.stateAffiliateConfirmJoin

      if (responseConfirmJoin.message === 'User joined') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ผู้ใช้ได้เข้าร่วม'
        }).then(() => { this.$router.push('/productAffiliate') })
      } else if (responseConfirmJoin.message === 'This user is already joined') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ผู้ใช้เข้าร่วมแล้ว'
        }).then(() => { this.$router.push('/productAffiliate') })
      } else if (responseConfirmJoin.message === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true,
        //   icon: 'info',
        //   title: 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
        // })
      } else if (responseConfirmJoin.message === 'Failed to join') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ไม่สามารถเข้าร่วมได้'
        })
      } else if (responseConfirmJoin.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: `${responseConfirmJoin.message}`
        })
      }
    },
    beforeDetailSocail () {
      this.$router.push({ name: 'socailMediaDetail' })
    }
  }
}
</script>
