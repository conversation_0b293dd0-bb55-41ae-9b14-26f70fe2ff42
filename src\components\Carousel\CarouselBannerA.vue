<template>
  <v-row class="d-flex justify-center" justify="center" dense :class="MobileSize ? 'px-4' : 'ma-4'">
    <v-col cols="12" align="center" style="border-radius: 8px; " :class="MobileSize ? 'pa-0' : ''">
      <!-- <v-img @click="test()" style="cursor: pointer;" width="1225" src="@/assets/NewBanner.png"></v-img> -->
      <!-- <v-img @click="test()" style="cursor: pointer;" class="rounded-lg" width="1225" max-height="270" :src="this.imageBanner" :lazy-src="this.imageLazyBanner"></v-img> -->
      <!-- <v-img style="margin: 0px 165px 0px 165px" src="@/assets/NewBanner.png"></v-img> -->
      <v-img v-if="Banner1.length !== 0 && Banner1[0].link_banner !== '-'" style="cursor: pointer;" class="rounded-lg my-4" :src="Banner1[0].image_path" :lazy-src="Banner1[0].image_path_lazy" @click="navigateToLink(Banner1[0].link_banner)" :height="MobileSize ? 'auto' : '100%'" :max-height="MobileSize ? '160px' : '270px'" :width="IpadSize ? '100%' : MobileSize ? '398px' : '1225px'"></v-img>
      <v-img v-if="Banner1.length !== 0 && Banner1[0].link_banner === '-'" class="rounded-lg my-4" :src="Banner1[0].image_path" :lazy-src="Banner1[0].image_path_lazy" :max-height="MobileSize ? '160px' : '270px'" :height="MobileSize ? 'auto' : '100%'" :max-width="IpadSize ? '100%' : MobileSize ? '398px' : '1225px'"></v-img>
    </v-col>
  </v-row>
</template>

<script>
export default {
  data () {
    return {
      Banner1: [],
      listBanner: [],
      imageBanner: '',
      imageLazyBanner: ''
    }
  },
  created () {
    this.GetBanner()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    navigateToLink (link) {
      window.location.href = link
    },
    async GetBanner () {
      await this.$store.dispatch('actionsGetBanner')
      var response = await this.$store.state.ModuleHompage.stateGetBanner
      // console.log('response_GetBannerA', response.data.image_banner_1)
      // console.log('response_GetBannerA', response.data.image_banner_1[0])
      // this.listBanner = response.data.image_banner_1[0]
      // this.imageBanner = this.listBanner.path
      // this.imageLazyBanner = this.listBanner.path_lazy
      if (response.data.image_banner_1.length > 0) {
        for (let i = 0; i < response.data.image_banner_1.length; i++) {
          this.Banner1.push({
            image_path: response.data.image_banner_1[i].path,
            image_path_lazy: response.data.image_banner_1[i].path_lazy,
            link_banner: response.data.image_banner_1[i].href
          })
        }
      } else {
        this.Banner1 = []
      }
    }
  }
}
</script>
