<template>
  <div class="pa-12">
    <div v-if="!connected">
      <v-row justify="center" class="">
        <v-col cols="4" align="center" align-self="center">
          <h1>เข้าห้องไลฟ์</h1>

          <v-col cols="10" align="start">
            <span>ห้องไลฟ์</span>
            <v-text-field
              v-model="roomName"
              dense
              hide-details
              outlined
              placeholder="กรอกชื่อห้องไลฟ์"
            ></v-text-field>
          </v-col>

          <v-col cols="10" align="start" align-self="end">
            <span>ชื่อผู้ใช้งาน</span>
            <v-text-field
              v-model="participantName"
              dense
              hide-details
              outlined
              placeholder="กรอกชื่อผู้ใช้งาน"
            ></v-text-field>
          </v-col>
          <v-btn color="primary" @click="joinRoome" :disabled="roomName == '' ||  participantName == ''">Join Room</v-btn>
        </v-col>
      </v-row>
    </div>

    <div v-else>
      <div v-for="remoteTrack of remoteTracksMap.values()" :key="remoteTrack.trackPublication.trackSid">
          <VideoComponent
            v-if="remoteTrack.trackPublication.kind === 'video' && remoteTrack.participantIdentity == 'Host'"
            :track="remoteTrack.trackPublication.videoTrack"
            :participantIdentity="remoteTrack.participantIdentity"
            :message="message"
            :room="room"
            @leaveRoom="leaveRoom"
          />
      </div>
    </div>
  </div>
</template>

<script>
import VideoComponent from './component/VideoComponent.vue'
import {
  Room,
  RoomEvent
} from 'livekit-client'
export default {
  components: {
    VideoComponent
  },
  data () {
    return {
      roomName: 'flash sale 50%',
      participantName: 'watcher' + Math.floor(Math.random() * 100),
      remoteTracksMap: new Map(),
      // remoteTracksMap: [],
      room: null,
      token: null,
      localTrack: null,
      connected: false,
      APPLICATION_SERVER_URL: '',
      LIVEKIT_URL: '',
      message: 'im watching now good stream' + Math.floor(Math.random() * 100),
      remoteTrack: null,
      remoteIdentity: ''
      // liveUrl: process.env.LIVEKIT_WS_URL
    }
  },
  created () {
    this.$EventBus.$on('leaveRoom', this.leaveRoom)
    this.configureUrls()
  },
  methods: {
    async joinRoome () {
      this.roomOnEverything()
      try {
        this.token = await this.getToken(this.roomName, this.participantName)
        await this.room.connect(this.LIVEKIT_URL, this.token)
        console.log('token create stream')
        this.connected = true
        // await this.room.localParticipant.enableCameraAndMicrophone()
        // const videoPublication = this.room.localParticipant.videoTrackPublications.values().next().value
        // this.localTrack = videoPublication ? videoPublication.videoTrack : undefined
      } catch (error) {
        console.error('There was an error connecting to the room:', error.message)
        await this.leaveRoom()
      }
    },
    async roomOnEverything () {
      this.room = new Room()
      this.room.on(
        RoomEvent.TrackSubscribed,
        (_track, publication, participant) => {
          this.remoteTracksMap.set(publication.trackSid, {
            trackPublication: publication,
            participantIdentity: participant.identity
          })
          this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
        }
      )

      this.room.on(RoomEvent.ParticipantConnected, (participant) => {
        console.log('live ParticipantConnected')
        console.log('first', participant)
      })

      this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        console.log('live ParticipantDisconnected')
        console.log('first', participant)
      })

      this.room.on(RoomEvent.TrackUnsubscribed, (_track, publication, participant) => {
        console.log(_track, publication, participant)
        console.log(participant.identity === 'Host')
        // this.remoteTracksMap.delete(publication.trackSid)
        this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      })
    },

    async getToken (roomName, participantName) {
      const response = await fetch(this.APPLICATION_SERVER_URL + 'token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roomName, participantName })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(`Failed to get token: ${error.errorMessage}`)
      }

      const data = await response.json()
      return data.token
    },

    async leaveRoom () {
      console.log('leaveRoom', this.room)
      if (this.room) {
        await this.room.disconnect()

        this.connected = false
        this.room = undefined
        // this.remoteTracksMap.clear()
      }
    },

    configureUrls () {
      if (!this.APPLICATION_SERVER_URL) {
        if (window.location.hostname === 'localhost') {
          this.APPLICATION_SERVER_URL = 'http://localhost:6080/'
        } else {
          this.APPLICATION_SERVER_URL = `https://${window.location.hostname}:6443/`
        }
      }

      if (!this.LIVEKIT_URL) {
        if (window.location.hostname === 'localhost') {
          this.LIVEKIT_URL = 'wss://helloworld-nt1b7zmh.livekit.cloud'
        } else {
          this.LIVEKIT_URL = `wss://${window.location.hostname}:7443/`
        }
      }
    }
  }
}
</script>

<style>

</style>
