<template>
  <v-dialog v-model="visible" width="425" persistent>
    <v-card style="border-radius: 24px; background-color: #27AB9C;">
      <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
        <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">คูปองทั้งหมด
        </span>
        <v-btn icon dark @click="$emit('close')">
          <v-icon color="#FFFFFF">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card class="pa-0" style="background-color: #CEF0EC;">
        <v-col cols="12" class="px-6">
          <v-row no-gutters>
            <v-col cols="12" class="pb-2" v-for="(item, index) in dataCoupons" :key="index">
              <v-col class="couponIMGMobile">
                <v-col cols="12" md="12" class="pt-0" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                  <v-row no-gutters>
                    <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                      <v-row no-gutters v-if="item.coupon_type === 'free_product'">
                        <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start"  >
                          <span style="color: #27AB9C; font-size: 14px; font-weight: 600;  display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"> {{item.coupon_name }}</span>
                          <span @click="$emit('btn1', item)" class="couponAll" style="color: #F56E22; font-size: 10px; font-weight: 600;">เงื่อนไขการใช้คูปอง</span><br>
                          <span class="mb-4" style="font-size: 10px;">
                            <span v-if="item.useEndDate !== null">ใช้ได้ถึง {{item.useEndDate}}</span>
                            <span v-else>ไม่มีวันหมดอายุ</span></span><br>
                          <v-col cols="12" md="12" class="pa-0">
                            <v-row no-gutters>
                              <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="item.powerOfEndDate">
                                <template #progress="{ value }">
                                <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                </template>
                              </v-progress-linear>
                              <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">ใช้แล้ว {{item.powerOfEndDate}} %</span>
                            </v-row>
                          </v-col>
                        </v-col>
                        <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                          <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_product' ? 'คูปอง': 'ส่วนลด'}}</span><br>
                          <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> แถมฟรี</span><br>
                          <v-btn v-if="item.is_collected === 'N'" @click="$emit('btn2', item)" color="#F56E22" style="color:white;" small rounded>เก็บโค้ด</v-btn>
                          <v-btn v-else disabled color="#F56E22" style="color:white;" small rounded>เก็บโค้ดแล้ว</v-btn>
                        </v-col>
                      </v-row>
                      <v-row no-gutters v-else>
                        <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start" >
                          <span style="color: #27AB9C; font-size: 14px; font-weight: 600; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{item.coupon_name }}</span>
                          <span> ขั้นต่ำ {{item.spend_minimum}} บาท</span><br>
                          <span class="mb-4" style="font-size: 10px;">
                            <span v-if="item.useEndDate !== null">ใช้ได้ถึง {{item.useEndDate}}</span>
                            <span v-else>ไม่มีวันหมดอายุ</span></span><br>
                          <v-col cols="12" md="12" class="pa-0">
                            <v-row no-gutters>
                              <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 80px; height: 5px; border-radius: 48px;" :value="item.powerOfEndDate">
                                <template #progress="{ value }">
                                <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                </template>
                              </v-progress-linear>
                              <span class="mt-1 ml-2" style="font-size: 8px; color: #27AB9C;">ใช้แล้ว {{item.powerOfEndDate}} %</span>
                            </v-row>
                          </v-col>
                        </v-col>
                        <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                          <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? 'โค้ดส่งฟรี' : 'ส่วนลด'}}</span><br>
                          <span style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%':'บาท'}}</span><br>
                          <v-btn v-if="item.is_collected === 'N'" @click="$emit('btn2', item)" color="#F56E22" style="color:white;" small rounded>เก็บโค้ด</v-btn>
                          <v-btn v-else disabled color="#F56E22" style="color:white;" small rounded>เก็บโค้ดแล้ว</v-btn>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-col>
            </v-col>
          </v-row>
        </v-col>
      </v-card>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataCoupons: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    initialRoute () {
      return this.$store.state.ModuleGlobal.initialRoute
    },
    previousRoute () {
      return this.$store.state.ModuleGlobal.previousRoute
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped>
.couponAll {
  cursor: pointer;
}
.couponAll:hover {
  transform: scale(1.02) !important;
}
.progress-gradient {
width: 100%;
height: 100%;
border-radius: 48px;
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.vProgressLinearDesk {
  max-width: 4.5vw;
}
.vProgressLinearIped {
  max-width: 8vw;
}
.vProgressLinearMobile {
  max-width: 23vw;
}

.backIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/background.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
}
.couponIMGDesk{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  padding: 1%;
  /* width: 100%; */
}
</style>
