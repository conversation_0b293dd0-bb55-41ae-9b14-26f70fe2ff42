<template>
  <v-container>
    <v-row dense no-gutters justify="end">
      <v-col cols="12" md="4" sm="4" xs="12">
        <v-text-field
         v-model="search"
         rounded
         dense
         outlined
         placeholder="ค้นหาคู่ค้าองค์กร"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row v-if="props.length !== 0">
      <v-col cols="12" class="pl-4 pt-6">
        <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">แสดงรายการจำนวนสินค้า {{ itemsPerPage &lt; 0 ? props.length : itemsPerPage }} รายการ</span>
      </v-col>
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card outlined class="mb-4">
          <v-data-table  v-if="type === 'PartnerList'"
           :headers="headersListUser"
           :items="props"
           :items-per-page="5"
           :search="search"
           no-results-text="ไม่พบคู่ค้าองค์กรที่ค้นหา"
           no-data-text="ไม่มีคู่ค้าองค์กรในตาราง"
           :update:items-per-page="getItemPerPage"
          >
            <template v-slot:[`item.detail`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  outlined
                  color="#27AB9C"
                  style="border: 1px solid #27AB9C; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  @click="gotoPartnerDetail(item)"
                  :to="pathEditUser"
                  class="pt-4 pb-4"
                >
                  รายละเอียด
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <v-row dense justify="center">
                <span v-if="item.status === true" style="color: green; font-weight: bold;">เปิด</span>
              </v-row>
            </template>
          </v-data-table>
          <v-data-table
           v-else
           :headers="headerUserRequest"
           :items="props"
           :items-per-page="5"
           :search="search"
           no-results-text="ไม่พบคู่ค้าองค์กรที่ค้นหา"
           no-data-text="ไม่มีคู่ค้าองค์กรในตาราง"
           :update:items-per-page="getItemPerPage"
          >
            <template v-slot:[`item.status`]="{ item }">
              <v-row dense justify="center">
                <span v-if="item.status === 'request'" style="color: #F9A825; font-weight: bold;">กำลังส่งคำขอ</span>
              </v-row>
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  outlined
                  color="red"
                  style="border: 1px solid red; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  @click="NotAllowPartner(item)"
                  class="pt-4 pb-4"
                >
                  ลบคำขอ
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.request_date`]="{ item }">
              {{ new Date(item.request_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}
            </template>
            <template v-slot:[`item.update_date`]="{ item }">
              {{ new Date(item.update_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}
            </template>
          </v-data-table>
        </v-card>
        <!-- <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div> -->
      </v-col>
    </v-row>
    <v-row justify="center" align-content="center" v-else>
      <v-col cols="12" md="12" align="center">
        <div class="my-5">
          <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
        </div>
        <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีบริษัท</span><br/>
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;">กด <span style="font-size: 28px;">“เพิ่มบริษัท”</span> เพื่อเพิ่มบริษัทที่สมบูรณ์ของคุณ</span>
        </h2>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
export default {
  props: ['props', 'type'],
  data () {
    return {
      status: false,
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      search: '',
      pathEditUser: '',
      header: '',
      headersListUser: [
        { text: 'ลำดับที่', value: 'id', sortable: false, align: 'center', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า (ภาษาไทย)', value: 'name_th', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า (ภาษาอังกฤษ)', value: 'name_en', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'detail', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headerUserRequest: [
        { text: 'ลำดับที่', value: 'id', sortable: false, align: 'center', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า (ภาษาไทย)', value: 'name_th', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า (ภาษาอังกฤษ)', value: 'name_en', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ส่งคำขอ', value: 'request_date', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่อัปเดตล่าสุด', value: 'update_date', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'รายชื่อคู่ค้าองค์กร')
    this.$EventBus.$emit('changeNavAdminManage')
  },
  methods: {
    gotoPartnerDetail (val) {
      localStorage.setItem('detailPartner', Encode.encode(val))
      this.$router.push({ path: '/partnerDetail' }).catch(() => {})
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
    },
    NotAllowPartner () {
      this.$swal.fire({
        icon: 'warning',
        title: 'ต้องการยกเลิกคำขอร้านค้าใช่หรือไม่?',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
        } else {
        }
      }).catch(() => {
      })
    }
  }
}
</script>

<style>
.backgroundTableCustom {
  color: #27ab9c !important;
  font-size: 12px;
  text-align: center;
  background-color: #e6f5f3 !important;
  border-color: #e6f5f3 !important;
}
</style>
