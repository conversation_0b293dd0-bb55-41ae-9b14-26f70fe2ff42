<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row v-if="!MobileSize && !IpadSize">
        <v-col cols="8">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;">จัดการขนส่งระบบ</v-card-title>
        </v-col>
        <v-col cols="4" style="display: flex; justify-content: end; align-items: center;">
          <v-btn color="#38b2a4" class="mr-4" rounded @click="settingShipping()">
            <span class="white--text">ตั้งค่าขนส่งระบบ</span>
          </v-btn>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col cols="12">
          <v-card-title style="font-weight: 700;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> จัดการขนส่งระบบ</v-card-title>
        </v-col>
        <v-col cols="12" style="display: flex; justify-content: center; align-items: center; margin-top: -20px;">
          <v-btn color="#38b2a4" width="90%" rounded @click="settingShipping()">
            <span class="white--text">ตั้งค่าขนส่งระบบ</span>
          </v-btn>
        </v-col>
      </v-row>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าของระบบ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="adminShopList.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อร้านค้าของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="adminShopList.length !== 0 && (MobileSize || IpadSize)">รายชื่อร้านค้าของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="adminShopList"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อร้านค้าของระบบ"
                no-data-text="ไม่มีชื่อร้านค้าของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ item }">
                    {{ adminShopList.map(x => {return x.id; }).indexOf(item.id) + 1 }}
                  </template>
                  <!-- <template v-slot:[`item.shipping`]="{ item }">
                    {{ (item.shipping_method.length !== 0 && item.shipping_method !== '[]' && item.shipping_method !== null) ? 'ใช้ขนส่งภายในระบบ' : 'ไม่ใช้ขนส่งภายในระบบ' }}
                  </template> -->
                  <template v-slot:[`item.actions`]="{ item }">
                    <v-btn @click="gotoSittingShop(item)" text color="#27AB9C">ดูขนส่งร้านค้า</v-btn>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- Dialog ตั้งค่าขนส่งระบบ -->
    <v-dialog v-model="modalSitting" persistent :width="MobileSize ? '100%' : '554px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '554px'">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ตั้งค่าขนส่งระบบ
          </span>
            <v-btn icon dark @click="CloseDialogSettingCourier()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="pt-4">
          <v-row dense>
            <v-col cols="12">
              <span style="color: #333333; font-weight: 700; font-size: 16px;">เลือกขนส่งที่ใช้ภายในระบบ</span>
            </v-col>
            <v-col cols="12">
              <v-row dense>
                <v-col cols="12" md="6" sm="6" v-for="(item, index) in listShipping" :key="index">
                  <v-card width="100%" outlined style="border-radius: 8px;" :style="shippingMethod === item.value ? 'border: 2px solid #27AB9C;' : ''" @click="shippingMethod === item.value ? '' : selectShipping(item.value)">
                    <v-card-text>
                      <v-row justify="center" dense>
                        <v-img :src="item.image" height="100px" width="100px" max-height="100px" max-width="100px" contain></v-img>
                      </v-row>
                      <v-row dense justify="center" class="pt-4">
                        <span style="color: #333333; font-weight: 700; font-size: 16px;">{{ item.title }}</span>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense class="mt-2">
            <v-col v-if="listCourier.length !== 0" cols="12">
              <span style="color: #333333; font-weight: 700; font-size: 16px;">เลือกรายการขนส่ง</span>
            </v-col>
            <v-col v-if="listCourier.length !== 0" cols="12">
              <v-checkbox v-model="checkAllCourier" dense label="เลือกทั้งหมด" hide-details @change="CheckAllCourier(listCourier, checkAllCourier)"></v-checkbox>
            </v-col>
            <v-col cols="12" v-for="(item, index) in listCourier" :key="index" @click="Checkbutton(listCourier)">
              <v-card class="pa-2" elevation="0" width="100%" height="100%" style="border: 1px solid; border-radius: 20px;">
                  <v-row dense>
                  <v-col cols="1" class="pt-4">
                    <v-checkbox v-model="item.status" false-value="no" true-value="yes" dense hide-details></v-checkbox>
                  </v-col>
                  <v-col cols="4" md="2" sm="2">
                    <v-avatar size="64" style="border: 1px solid">
                      <v-img :src="`${item.media_path}`" contain max-height="100%" max-width="100%" height="64" width="64"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" md="9" sm="9" class="pt-6">
                    <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">{{ item.name }}</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row v-if="listCourier.length !== 0" dense justify="center" class="pb-4 px-4">
            <v-btn outlined rounded color="#27AB9C" width="110" height="40" class="mr-auto" @click="CloseDialogSettingCourier()">ย้อนกลับ</v-btn>
            <v-btn color="#27AB9C" rounded class="white--text" width="110" height="40" @click="SaveSettingCourier(listCourier)">บันทึก</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog ดูขนส่งร้านค้า -->
    <v-dialog v-model="modalShippingShop" persistent :width="MobileSize ? '100%' : '554px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '554px'">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ขนส่งระบบของร้านค้า
          </span>
            <v-btn icon dark @click="CloseDialogShopCourier()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="pt-4">
          <v-row dense>
            <v-col v-if="!MobileSize">
              <v-row style="align-items: baseline;" class="pl-3 pb-4 justify-start">
                <v-switch class="pl-1" false-value="no" true-value="yes" inset v-model="Switch" readonly @click="SwitchShipping(Switch)"></v-switch>
                <span class="pb-0" style="font-weight: 400; font-size:18px; line-height: 32px;">เปิด-ปิด ขนส่งระบบของร้านค้า</span>
              </v-row>
            </v-col>
            <v-col cols="12">
              <span style="color: #333333; font-weight: 700; font-size: 16px;">ขนส่งที่ใช้ภายในระบบ</span>
            </v-col>
            <v-col cols="12">
              <v-row v-if="Shopshipping.length !== 0 || Switch === 'yes'" dense>
                <v-col cols="12" md="6" sm="6" v-for="(item, index) in listShipping" :key="index" >
                  <v-card width="100%" outlined style="border-radius: 8px;" :style="shippingMethod === item.value ? 'border: 2px solid #27AB9C;' : ''" @click="shippingMethod === item.value ? '' : selectShippingV2(item.value)">
                    <v-card-text>
                      <v-row justify="center" dense>
                        <v-img :src="item.image" height="100px" width="100px" max-height="100px" max-width="100px" contain></v-img>
                      </v-row>
                      <v-row dense justify="center" class="pt-4">
                        <span style="color: #333333; font-weight: 700; font-size: 16px;">{{ item.title }}</span>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <v-row v-else dense>
                <v-col cols="12">
                  <v-card width="100%" outlined style="border-radius: 8px; border: 2px solid #27AB9C;">
                    <v-card-text>
                      <v-row justify="center" dense>
                        <v-img src="@/assets/iShip/car.png" height="100px" width="100px" max-height="100px" max-width="100px" contain></v-img>
                      </v-row>
                      <v-row dense justify="center" class="pt-4">
                        <span style="color: #333333; font-weight: 700; font-size: 16px;">ร้านค้ายังไม่มีการตั้งค่าขนส่ง</span>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense class="mt-2" v-if="listCourier2.length !== 0">
            <v-col cols="12">
              <span style="color: #333333; font-weight: 700; font-size: 16px;">รายการขนส่ง</span>
            </v-col>
            <v-col v-if="listCourier2.length !== 0" cols="12">
              <v-checkbox v-model="checkAllCourier" dense label="เลือกทั้งหมด" hide-details @change="CheckAllCourier2(listCourier2, checkAllCourier)"></v-checkbox>
            </v-col>
            <v-col cols="12" v-for="(item, index) in listCourier2" :key="index"  @click="Checkbutton(listCourier2)">
              <v-card class="pa-2" elevation="0" widlistCourierth="100%" height="100%" style="border: 1px solid; border-radius: 20px;">
                  <v-row dense>
                    <v-col cols="1" class="pt-4">
                      <v-checkbox v-model="item.status" false-value="no" true-value="yes" dense hide-details></v-checkbox>
                    </v-col>
                  <v-col cols="4" md="2" sm="2">
                    <v-avatar size="64" style="border: 1px solid">
                      <v-img :src="`${item.media_path}`" contain max-height="100%" max-width="100%" height="64" width="64"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" md="9" sm="9" class="pt-6">
                    <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">{{ item.name }}</span>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row v-if="listCourier2.length !== 0" dense justify="center" class="pb-4 px-4">
            <v-btn outlined rounded color="#27AB9C" width="110" height="40" class="mr-auto" @click="CloseDialogShopCourier()">ย้อนกลับ</v-btn>
            <v-btn color="#27AB9C" rounded class="white--text" width="110" height="40" @click="SaveSettingCourier2(listCourier2)">บันทึก</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirmShipping" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CancelShipping(TextSwitch)"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{TextSwitch}}ขนส่งระบบของร้านค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CancelShipping(TextSwitch)">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="OpenCloseShipping(TextSwitch)">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      ShopID: '',
      listCourier2: [],
      ListShippingMobilyst: [],
      ListShippingIship: [],
      TextSwitch: '',
      ModalConfirmShipping: false,
      Switch: '',
      Shopshipping: [],
      search: '',
      adminShopList: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขประจำตัวผู้เสียภาษี', value: 'tax_id', width: '180', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_th', width: '170', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้าน', value: 'shop_name', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', filterable: false, width: '150', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      dataShop: [],
      checkAllCourier: false,
      disableButton: false,
      modalShippingShop: false,
      modalSitting: false,
      shippingMethod: '',
      ListShipping: [],
      listShipping: [
        { image: require('@/assets/logo_mobilyst.png'), title: 'Mobilyst', value: 'mobilyst' },
        { image: require('@/assets/logo_iShip.png'), title: 'iShip', value: 'iship' }
      ],
      listCourier: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
      this.listAllShipping()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageShippingMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'ManageShipping')
        this.$router.push({ path: '/ManageShipping' }).catch(() => {})
      }
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async SaveSettingCourier2 (item) {
      var courier = []
      // console.log('item', item)
      if (item.every((key) => key.status === 'no')) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาเลือกขนส่งอย่างน้อย 1 ขนส่ง'
        })
      } else {
        if (item[0].service_provider === 'ISHIP') {
          this.ListShippingIship = item
          this.ListShippingMobilyst.forEach(element => {
            element.status = 'no'
          })
          courier = this.ListShippingIship
            .filter(listItem => listItem.status === 'yes')
            .map(listItem => listItem.code)
        }
        if (item[0].service_provider === 'Mobilyst') {
          this.ListShippingMobilyst = item
          this.ListShippingIship.forEach(element => {
            element.status = 'no'
          })
          courier = this.ListShippingMobilyst
            .filter(listItem => listItem.status === 'yes')
            .map(listItem => listItem.code)
        }
        var data = {
          seller_shop_id: this.ShopID,
          shipping_method: [
            {
              service: item[0].service_provider,
              courier: courier
            }
          ]
        }
        // console.log(data)
        await this.$store.dispatch('actionsEditSellerShopShipping', data)
        var res2 = await this.$store.state.ModuleAdminManage.stateEditSellerShopShipping
        // console.log('res2', res2)
        if (res2.message === 'Edit seller shop data success.') {
          // console.log(1)
          this.$store.commit('closeLoader')
          this.modalShippingShop = false
          this.shippingMethod = ''
          this.listCourier2 = []
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'success',
            text: 'ดำเนินการตั้งค่าขนส่งสำเร็จ'
          })
          this.getShopData()
          this.listAllShipping()
        } else {
          // console.log(2)
          this.$store.commit('closeLoader')
          this.modalShippingShop = false
          this.shippingMethod = ''
          this.listCourier2 = []
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
        }
      }
    },
    CancelShipping (item) {
      if (item === 'เปิด') {
        this.Switch = 'no'
        this.ModalConfirmShipping = false
      } else {
        this.Switch = 'yes'
        this.ModalConfirmShipping = false
      }
    },
    async OpenCloseShipping (item) {
      this.$store.commit('openLoader')
      if (item === 'เปิด') {
        this.Switch = 'yes'
      } else {
        this.Switch = 'no'
      }
      // this.ModalConfirmShipping = false
      // this.Switch = this.Switch === 'yes' ? 'yes' : 'no'
      // this.$store.commit('closeLoader')
      if (this.Switch === 'no') {
        var data = {
          seller_shop_id: this.ShopID,
          shipping_method: []
        }
        await this.$store.dispatch('actionsEditSellerShopShipping', data)
        var res2 = await this.$store.state.ModuleAdminManage.stateEditSellerShopShipping
        if (res2.message === 'Edit seller shop data success.') {
          // console.log(1)
          this.$store.commit('closeLoader')
          this.modalShippingShop = false
          this.ModalConfirmShipping = false
          this.shippingMethod = ''
          this.listCourier2 = []
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'success',
            text: 'ปิดการตั้งค่าขนส่งสำเร็จ'
          })
          this.getShopData()
          this.listAllShipping()
        } else {
          // console.log(2)
          this.$store.commit('closeLoader')
          this.modalShippingShop = false
          this.ModalConfirmShipping = false
          this.shippingMethod = ''
          this.listCourier2 = []
          // this.Switch = this.Switch === 'yes' ? 'no' : 'yes'
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
          this.getShopData()
          this.listAllShipping()
        }
      } else {
        this.Switch = this.Switch === 'yes' ? 'yes' : 'no'
        this.ModalConfirmShipping = false
        this.$store.commit('closeLoader')
      }
      // await this.$store.dispatch('actionsAffiliateShippingStatus', data)
      // var res = await this.$store.state.ModuleAffiliate.stateAffiliateShippingStatus
      // if (res.message === 'update status to yes success' || res.message === 'update status to no success') {
      //   this.$swal.fire({
      //     showConfirmButton: false,
      //     timer: 2500,
      //     timerProgressBar: true,
      //     icon: 'success',
      //     html: `<h3>${this.TextSwitch}การอนุมัติอัตโนมัติผู้เข้าร่วมสำเร็จ</้>`
      //   })
      //   this.ModalConfirmShipping = false
      //   this.getListApprove()
      //   this.$store.commit('closeLoader')
      // } else {
      //   this.$swal.fire({
      //     showConfirmButton: false,
      //     timer: 2500,
      //     timerProgressBar: true,
      //     icon: 'error',
      //     title: 'เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง'
      //   })
      // this.Switch = this.Switch === 'yes' ? 'no' : 'yes'
      // this.ModalConfirmShipping = false
      // this.$store.commit('closeLoader')
    },
    SwitchShipping (item) {
      this.ModalConfirmShipping = true
      this.TextSwitch = item === 'yes' ? 'ปิด' : 'เปิด'
    },
    CloseDialogShopCourier () {
      this.shippingMethod = ''
      this.listCourier2 = []
      this.modalSitting = false
      this.checkAllCourier = false
      this.modalShippingShop = false
    },
    gotoSittingShop (item) {
      this.ShopID = item.id
      this.Shopshipping = item.shipping_method
      // console.log('this.Shopshipping', this.Shopshipping)
      if (this.Shopshipping.length !== 0) {
        this.Switch = 'yes'
        if (this.Shopshipping[0].service === 'ISHIP') {
          var shopShippingCodes = new Set()
          this.Shopshipping.forEach(ship => {
            ship.courier.forEach(courier => {
              shopShippingCodes.add(courier.code)
            })
          })
          this.ListShippingIship.forEach(listItem => {
            if (shopShippingCodes.has(listItem.code)) {
              listItem.status = 'yes'
            } else {
              listItem.status = 'no'
            }
          })
          this.shippingMethod = 'iship'
          this.selectShippingV2(this.shippingMethod)
        } else {
          var shopShippingCodes2 = new Set()
          this.Shopshipping.forEach(ship => {
            ship.courier.forEach(courier => {
              shopShippingCodes2.add(courier.code)
            })
          })
          this.ListShippingMobilyst.forEach(listItem => {
            if (shopShippingCodes2.has(listItem.code)) {
              listItem.status = 'yes'
            } else {
              listItem.status = 'no'
            }
          })
          this.shippingMethod = 'mobilyst'
          this.selectShippingV2(this.shippingMethod)
        }
      } else {
        this.Switch = 'no'
        this.shippingMethod = ''
      }
      // console.log('this.ListShippingIshipV2', this.ListShippingIship)
      this.modalShippingShop = true
    },
    async SaveSettingCourier (val) {
      // console.log('val', val)
      if (val.every((key) => key.status === 'no')) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาเลือกขนส่งอย่างน้อย 1 ขนส่ง'
        })
      } else {
        if (val[0].service_provider === 'ISHIP') {
          this.ListShipping.list_iship = val
          this.ListShipping.list_mobilyst.forEach(element => {
            element.status = 'no'
          })
        }
        if (val[0].service_provider === 'Mobilyst') {
          this.ListShipping.list_mobilyst = val
          this.ListShipping.list_iship.forEach(element => {
            element.status = 'no'
          })
        }
        var data = {
          list_mobilyst: this.ListShipping.list_mobilyst,
          list_iship: this.ListShipping.list_iship
        }
        // console.log('object2', data)
        await this.$store.dispatch('actionsEditCourier', data)
        var res = await this.$store.state.ModuleAdminManage.stateEditCourier
        if (res.result === 'Success') {
          this.$store.commit('closeLoader')
          this.modalSitting = false
          this.shippingMethod = ''
          this.listCourier = []
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'success',
            text: 'ดำเนินการตั้งค่าขนส่งสำเร็จ'
          })
        } else {
          this.$store.commit('closeLoader')
          this.modalSitting = false
          this.shippingMethod = ''
          this.listCourier = []
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3500,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
        }
      }
    },
    async listAllShipping () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListAllCourier')
      var res = await this.$store.state.ModuleAdminManage.stateListAllCourier
      if (res.result === 'Success') {
        this.$store.commit('closeLoader')
        this.ListShipping = res.data
        this.ListShippingIship = this.ListShipping.list_iship
        // console.log('this.ListShippingIship', this.ListShippingIship)
        this.ListShippingMobilyst = this.ListShipping.list_mobilyst
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
      }
    },
    selectShippingV2 (value) {
      this.shippingMethod = value
      this.checkAllCourier = false
      // this.listAllShipping()
      this.listCourier2 = []
      if (value === 'iship') {
        this.listCourier2 = this.ListShippingIship
        // for (var i = 0; i < this.ListShippingIship.length; i++) {
        //   if (this.ListShippingIship[i].status === 'yes') {
        //     this.listCourier2.push(this.ListShippingIship[i])
        //   }
        // }
        if (this.Shopshipping.length === 0) {
          this.listCourier2.forEach(element => {
            element.status = 'no'
          })
        }
        if (this.ListShippingIship.every((key) => key.status === 'yes')) {
          console.log('1')
          this.checkAllCourier = true
        } else {
          console.log('2')
          this.checkAllCourier = false
        }
      } else {
        this.listCourier2 = this.ListShippingMobilyst
        // for (i = 0; i < this.ListShippingMobilyst.length; i++) {
        //   if (this.ListShippingMobilyst[i].status === 'yes') {
        //     this.listCourier.push(this.ListShippingMobilyst[i])
        //   }
        // }
        if (this.Shopshipping.length === 0) {
          this.listCourier2.forEach(element => {
            element.status = 'no'
          })
        }
        if (this.ListShippingMobilyst.every((key) => key.status === 'yes')) {
          this.checkAllCourier = true
        } else {
          this.checkAllCourier = false
        }
      }
    },
    selectShipping (value) {
      this.shippingMethod = value
      this.checkAllCourier = false
      this.listAllShipping()
      this.listCourier = []
      if (value === 'iship') {
        // for (var i = 0; i < this.ListShipping.list_iship.length; i++) {
        //   if (this.ListShipping.list_iship[i].status === 'yes') {
        //     this.listCourier.push(this.ListShipping.list_iship[i])
        //   }
        // }
        this.listCourier = this.ListShipping.list_iship
        if (this.listCourier.every((key) => key.status === 'yes')) {
          this.checkAllCourier = true
        } else {
          this.checkAllCourier = false
        }
      } else {
        // for (i = 0; i < this.ListShipping.list_mobilyst.length; i++) {
        //   if (this.ListShipping.list_mobilyst[i].status === 'yes') {
        //     this.listCourier.push(this.ListShipping.list_mobilyst[i])
        //   }
        // }
        this.listCourier = this.ListShipping.list_mobilyst
        if (this.listCourier.every((key) => key.status === 'yes')) {
          this.checkAllCourier = true
        } else {
          this.checkAllCourier = false
        }
      }
    },
    async CloseDialogSettingCourier () {
      this.shippingMethod = ''
      this.listCourier = []
      this.modalSitting = false
      this.checkAllCourier = false
      await this.getCourier()
    },
    async settingShipping () {
      await this.listAllShipping()
      // console.log('this.ListShipping', this.ListShipping)
      this.shippingMethod = ''
      if (this.ListShipping.use_iship === true && this.ListShipping.use_mobilyst === false) {
        this.shippingMethod = 'iship'
        this.selectShipping(this.shippingMethod)
      } else if (this.ListShipping.use_mobilyst === true && this.ListShipping.use_iship === false) {
        this.shippingMethod = 'mobilyst'
        this.selectShipping(this.shippingMethod)
      } else {
        this.shippingMethod = ''
      }
      // console.log('this.shippingMethod', this.shippingMethod)
      this.modalSitting = true
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    CheckAllCourier (data, check) {
      // console.log('object', data, check)
      if (check === true) {
        for (var i = 0; i < data.length; i++) {
          if (this.listCourier[i].status === 'no') {
            data[i].status = 'yes'
          }
        }
        this.disableButton = false
      } else {
        for (var j = 0; j < data.length; j++) {
          data[j].status = 'no'
        }
        this.disableButton = true
      }
    },
    CheckAllCourier2 (data, check) {
      // console.log('object', data, check)
      if (check === true) {
        for (var i = 0; i < data.length; i++) {
          if (this.listCourier2[i].status === 'no') {
            data[i].status = 'yes'
          }
        }
        this.disableButton = false
      } else {
        for (var j = 0; j < data.length; j++) {
          data[j].status = 'no'
        }
        this.disableButton = true
      }
    },
    Checkbutton (val) {
      if (val.every((key) => key.status === 'no')) {
        this.disableButton = true
      } else {
        this.disableButton = false
        if (val.every((key) => key.status === 'yes')) {
          this.checkAllCourier = true
        } else {
          this.checkAllCourier = false
        }
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetShopDataAdmin')
      var response = await this.$store.state.ModuleAdminManage.stateGetShopDataAdmin
      if (response.message === 'List Seller Shop Success.') {
        this.$store.commit('closeLoader')
        this.adminShopList = [...response.data]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
