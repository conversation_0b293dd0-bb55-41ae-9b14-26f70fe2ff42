<template>
<v-container aling="center" style="margin-top:30px;margin-bottom:30px">
  <v-card class="rounded container">
   <v-row>
    <v-col cols="12" align="center" class="mb-10 mt-5">
        <span style="font-size:24px;" >ลงทะเบียนผู้ซื้อ</span>
    </v-col>

    <v-col  cols="12" md="5" :style="MobileSize || IpadSize || IpadProSize ? '': 'margin-left:90px'">
     <v-row>
       <v-avatar color="#F3F5F7"  class="ml-4 rounded-circle "   :width="IpadProSize ? '100px' : IpadSize ? '70px' :'70px'" :height="IpadProSize ? '100px' : IpadSize ? '70px' : '70px'">
         <v-img  src="@/assets/icons/checklist 1.png" contain></v-img>
       </v-avatar>
       <span style="font-size:16 ;margin-top: 30px;" >ข้อมูลส่วนบุคคล</span>
       <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 40px; margin-left: 2px;" ></v-spacer>
       <v-btn  icon  outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;"   class="mt-6 mr-5" >
         <v-icon color="#27AB9C" @click="reloadpage()">mdi-file-document</v-icon>
       </v-btn>
     </v-row>
     <v-container>
        <v-col cols="12" class="mt-10">
          <span>คำนำหน้าชื่อ (ภาษาไทย)</span>
          <v-row>
            <v-radio-group :error ="checkerror && title_name_th === '' ? true : false" :readonly="this.ProfileDate.user_data.account_title_th === null ? false : true" success class="ml-2" row v-model="title_name_th">
              <v-radio
                v-for="n in text_th"
                :key="n"
                :label="n"
                :value="n"
              >
              <template v-slot:label>
                <div class="black--text">
                  {{n}}
                </div>
              </template>
              </v-radio>
            </v-radio-group>
          </v-row>
        </v-col>
        <v-col cols="12">
            <span>ชื่อ (ภาษาไทย)</span>
            <v-text-field
            placeholder="ระบุชื่อภาษาไทย"
            v-model="first_name_th"
            :disabled="this.ProfileDate.user_data.first_name_th === null ? false : true"
            :error ="checkerror && first_name_th === '' ? true : false"
            :rules="Rules.first_name_th"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>นามสกุล (ภาษาไทย)</span>
            <v-text-field
            placeholder="ระบุนามสกุลภาษาไทย"
            v-model="last_name_th"
            :disabled ="this.ProfileDate.user_data.last_name_th === null ? false : true"
            :error ="checkerror && last_name_th === '' ? true : false"
            :rules="Rules.last_name_th"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
           <span>คำนำหน้าชื่อ (ภาษาอังกฤษ)</span>
            <v-row>
                <v-radio-group :error ="checkerror && title_name_eng === '' ? true : false" :readonly="this.ProfileDate.user_data.account_title_eng === null ? false : true" success class="ml-2" row v-model="title_name_eng">
                    <v-radio
                        v-for="n in text_en"
                        :key="n"
                        :label="n"
                        :value="n"
                    >
                     <template v-slot:label>
                        <div class="black--text">
                          <span>{{n}}</span>
                        </div>
                      </template>
                    </v-radio>
                </v-radio-group>
            </v-row>
        </v-col>
        <v-col cols="12">
            <span>ชื่อ (ภาษาอังกฤษ)</span>
            <v-text-field
            placeholder="ระบุชื่อภาษาอังกฤษ"
            v-model="first_name_eng"
            :disabled ="this.ProfileDate.user_data.first_name_eng === null ? false : true"
            :error ="checkerror && first_name_eng === '' ? true : false"
            :rules="Rules.first_name_eng"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>นามสกุล (ภาษาอังกฤษ)</span>
            <v-text-field
            placeholder="ระบุนามสกุลภาษาอังกฤษ"
            v-model="last_name_eng"
            :disabled ="this.ProfileDate.user_data.last_name_eng === null ? false : true"
            :error ="checkerror && last_name_eng === '' ? true : false"
            :rules="Rules.last_name_eng"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>เลขบัตรประจำตัวประชาชน</span>
            <v-text-field
            placeholder="ระบุเลขบัตรประจำตัวประชาชน 13 หลัก"
            v-model="id_card_number"
            :error ="checkerror && id_card_number === '' ? true : false"
            :rules="Rules.Idcard"
            maxlength="13"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>ชื่อผู้ใช้งาน</span>
            <v-text-field
            placeholder="ระบุชื่อผู้ใช้งาน"
            v-model="username"
            :error ="checkerror && username === '' ? true : false"
            :rules="Rules.user"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>รหัสผ่าน</span>
            <v-text-field
            :append-icon="value ? 'mdi-eye-off' : 'mdi-eye'"
            @click:append="() => (value = !value)"
            :type="value ? 'password' : 'text'"
            v-model="password"
            placeholder="ระบุรหัสผ่าน"
            :error ="checkerrors || checkerror && password === '' ? true : false"
            :rules="Rules.password"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>ยืนยันรหัสผ่าน</span>
            <v-text-field
            :append-icon="values ? 'mdi-eye-off' : 'mdi-eye'"
            @click:append="() => (values = !values)"
            :type="values ? 'password' : 'text'"
            v-model="re_password"
            placeholder="ยืนยันรหัสผ่าน"
            :error ="checkerrors || checkerror && re_password === '' ? true : false"
            :rules="Rules.password"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>E-mail</span>
            <v-text-field
            placeholder="ระบุE-mail"
            v-model="email"
            :rules="Rules.emailRules"
            outlined
            :error ="checkerror && email === '' ? true : false"
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
          <span>วันเกิด</span>
          <date-dropdown  :disabled="this.ProfileDate.user_data.birth_date === null ? false : true"   :default="this.birth_date"    min="1960"   max="2030"   v-model="selectedDate"></date-dropdown>
          <!-- <v-row>
            <v-col cols="4">
              <v-select  :items="days"  label="วันที่เกิด"  dense  outlined ></v-select>
            </v-col>
            <v-col cols="4">
              <v-select v-model="monthIndex" :items="mountoption" label="เดือน" dense   outlined >
                <template  v-slot:item="{ item }">
                  {{item.name}}
                </template>
              </v-select>
            </v-col>
            <v-col cols="4">
              <v-select    label="ปี"  dense  outlined ></v-select>
            </v-col>
          </v-row> -->
        </v-col>
      </v-container>
     </v-col>
     <v-col cols="12" md="1">
         <v-divider  v-if="MobileSize || IpadSize" ></v-divider>
         <v-divider v-else style="margin-left:20px" vertical></v-divider>
     </v-col>
     <v-col  cols="12" md="5" :style="MobileSize || IpadSize || IpadProSize ? '' : 'margin-left:-40px'">
     <v-row>
       <v-avatar class="ml-4 rounded-circle " color="#F3F5F7"   :width="IpadProSize ? '100px' : IpadSize ? '70px' :'70px'" :height="IpadProSize ? '100px' : IpadSize ? '70px' : '70px'">
         <v-img src="@/assets/icons/checklist_2.png" contain></v-img>
       </v-avatar>
       <span style="font-size:16 ;margin-top: 30px;" >ข้อมูลองค์กร</span>
       <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 40px; margin-left: 2px;" ></v-spacer>
       <v-btn  icon  outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;"   class="mt-6 mr-5" >
         <v-icon color="#27AB9C" @click="reloadpage()">mdi-file-document</v-icon>
       </v-btn>
     </v-row>
     <v-container>
        <v-col cols="12" class="mt-10">
            <span>ชื่อสถานที่ประกอบการ (ภาษาไทย)</span>
          <v-text-field
            placeholder="ระบุชื่อสถานที่ประกอบการภาษาไทย"
            v-model="biz_name_th"
            :disabled="this.Datas.name_on_document_th === null ? false : true"
            :error ="checkerror && biz_name_th === '' ? true : false"
            outlined
            dense
          ></v-text-field>
        </v-col>

        <v-col cols="12">
            <span>ชื่อสถานที่ประกอบการ (ภาษาอังกฤษ)</span>
            <v-text-field
            placeholder="ระบุชื่อสถานที่ประกอบการภาษาอังกฤษ"
            v-model="biz_name_eng"
            :disabled="this.Datas.name_on_document_eng === null ? false : true"
            :error ="checkerror && biz_name_eng === '' ? true : false"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>รหัสสาขา</span>
            <v-text-field
            placeholder="ระบุรหัสสาขา "
            v-model="branch_no"
            :disabled="this.Datas.branch_no === null ? false : true"
            :error ="checkerror && branch_no === '' ? true : false"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>ชื่อสาขา</span>
            <v-text-field
            placeholder="ระบุชื่อสาขา"
            v-model="branch_name"
            :disabled="this.Datas.branch_name === null ? false : true"
            :error ="checkerror && branch_name === '' ? true : false"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>หมายเลขประจำตัวผู้เสียภาษี </span>
            <v-text-field
            placeholder="ระบุหมายเลขประจำตัวผู้เสียภาษี 13 หลัก"
            v-model="tax_id"
            :disabled="this.Datas.tax_id === null ? false : true"
            :error ="checkerror && tax_id === '' ? true : false"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>E-mail</span>
            <v-text-field
            placeholder="ระบุอีเมล"
            v-model="biz_email"
            :rules="Rules.emailRules"
            type="email"
            :error ="checkerror && biz_email === '' ? true : false"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
          <span>เบอร์โทรศัพท์สาขา</span>
          <v-text-field
            placeholder="ระบุเบอร์โทรศัพท์สาขา"
            v-model="tel_no"
            :rules="Rules.tel"
            :error ="checkerror && tel_no === '' ? true : false"
            maxlength="10"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>เบอร์โทรศัพท์มือถือ</span>
            <v-text-field
            placeholder="ระบุเบอร์โทรศัพท์มือถือที่สามารถติดต่อได้ 10 หลัก"
            v-model="business_mobile_no"
            :rules="Rules.tel"
            :error ="checkerror && business_mobile_no === '' ? true : false"
            maxlength="10"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <span>ที่อยู่สถานประกอบการ</span>
            <v-text-field
            placeholder="ระบที่อยูุ่สถานที่ประกอบการ"
            v-model="address_detail"
            :error ="checkerror && address_detail === '' ? true : false"
            disabled
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12">
            <v-row>
              <v-col cols="6">
                <span>อำเภอ/เขต</span>
                <!-- <v-text-field
                  placeholder="ระบุอำเภอ"
                  v-model="district"
                  disabled
                  outlined
                  dense
                  class="ml-1"
                ></v-text-field> -->
                <v-select :label="district" :error ="checkerror && district === '' ? true : false"   placeholder="ระบุอำเภอ" disabled dense  outlined ></v-select>
              </v-col>
              <v-col cols="6">
                <span>ตำบล/แขวง</span>
                <!-- <v-text-field
                  placeholder="ระบุตำบล"
                  v-model="sub_district"
                  outlined
                  disabled
                  dense
                  class="ml-1"
                ></v-text-field> -->
                <v-select  :label="sub_district" :error ="checkerror && sub_district=== '' ? true : false"  placeholder="ระบุตำบล" disabled dense  outlined ></v-select>
              </v-col>
              <v-col cols="6">
                <span>จังหวัด</span>
                <!-- <v-text-field
                  placeholder="ระบุจังหวัด"
                  v-model="province"
                  outlined
                  disabled
                  dense
                  class="ml-1"
                ></v-text-field> -->
                <v-select  :label="province" :error ="checkerror && province=== '' ? true : false" placeholder="ระบุจังหวัด" disabled dense  outlined ></v-select>
              </v-col>
              <v-col cols="6" dense>
                <span>รหัสไปรษณีย์</span>
                <!-- <v-text-field
                  placeholder="ระบุรหัสไปรษณีย์"
                  v-model="zip_code"
                  outlined
                  disabled
                  dense
                  class="ml-1"
                ></v-text-field> -->
                <v-select  :label="zip_code" :error ="checkerror && zip_code=== '' ? true : false" placeholder="ระบุรหัสไปรษณีย์" disabled dense  outlined ></v-select>
              </v-col>
            </v-row>
        </v-col>
        <v-col cols="12" >
          <v-checkbox  success @click="consentalert()" v-model="checkbox">
            <template v-slot:label>
                <div class="black--text" >
                ยอมรับ
                <a
                    target="_blank"
                    href=""
                >ข้อกำหนดการใช้บริการ</a>และ<a
                    target="_blank"
                    href=""
                >นโยบายคุ้มครองส่วนบุคคล</a>
                </div>
            </template>
          </v-checkbox>
        </v-col>
        <v-col cols="12" >
            <v-btn :disabled="checkbox ? false : true" class="white--text" @click="success()" color="#27AB9C" block>ยืนยัน</v-btn>
        </v-col>
      </v-container>
     </v-col>
    </v-row>
  </v-card>
  <v-dialog  v-model="dialog" width="600" persistent>
     <v-card>
      <v-card-title >
       <span>กรุณาตรวจสอบ</span>
      </v-card-title><br/>
      <v-card-text>
        <span class="ml-10">เกิดข้อผิดพลาด กรุณาทำการ "ยอมรับ" ข้อกำหนดการใช้บริการและนโยบายคุ้มครองส่วนบุคคล</span>
      </v-card-text>
      <v-card-actions>
      <v-spacer></v-spacer>
        <!-- <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="colse()">ยกเลิก</v-btn> -->
        <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="colse()">เข้าใจแล้ว</v-btn>
      </v-card-actions>
     </v-card>
    </v-dialog>
   <v-dialog  v-model="consent" width="1231" style="height: 300px;" scrollable  persistent>
     <v-card>
      <v-card-title >
       <v-spacer></v-spacer>
       <v-icon @click="colse()">mdi-close</v-icon>
      </v-card-title>
       <v-layout justify-center >
      <div align="center" justify="center">
        <!-- <v-img width="128" height="128" src="@/assets/consent.png"></v-img> -->
        <!-- <br/> -->
        <v-row >
          <v-col cols="12"><v-img width="128" height="128" src="@/assets/consent.png"></v-img></v-col>
          <v-col cols="1"><v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 15px;" ></v-spacer></v-col>
          <v-col cols="10"><h2>ความยินยอมในการเก็บรวบรวม ใช้หรือเปิดเผยข้อมูลส่วนบุคคล ระบบหมอพร้อม Station</h2></v-col>
          <v-col cols="1"> <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 15px; " ></v-spacer></v-col>
        </v-row>
        <!-- <v-row>
        <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 15px; margin-left: 40px;margin-right:20px" ></v-spacer>
         <h5>ความยินยอมในการเก็บรวบรวม ใช้หรือเปิดเผยข้อมูลส่วนบุคคล ระบบหมอพร้อม Station</h5>
        <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 15px; margin-left: 20px;margin-right:20px" ></v-spacer>
        </v-row> -->
      </div>
       </v-layout >
      <v-card-text >
        <div class="ml-10 mr-5">
        <h3>1. วัตถุประสงค์ในการเก็บรวบรวมใช้เปิดเผยข้อมูลส่วนบุคคล</h3>
        <span class="ml-5">ในกรณีทีท่านเข้าใช้งานระบบหมอพร้อม Station ผ่านกระทรวงสาธารณสุข (เว็บแอพพลิเคชันหมอ) ข้อมูลส่วนบุคคลของท่านจะถูกเก็บไว้ และอาจถูกใช้เพื่อวัตถุประสงค์ต่อไปนี้</span><br/>
        <span class="ml-10">1. เพื่อใช้นการเข้าใช้งาน พิสูจน์และยืนยันตัวตนในการเข้าใช้งานฟังก์ชัน หมอพร้อม Commerce บนแอพพลิเคชันหมอพร้อม Station<br/>
              <span class="ml-10">เพื่อใช้ในการซื้อขายผลิตภัณฑ์เวชกรรมในฟังก์ชัน หมอพร้อม Commerce</span><br/>
              <span class="ml-5">หากภายหลังมีการเปลี่ยนแปลงวัตถุประสงค์ในการเก็บรวบรวมข้อมูลส่วนบุคคล กระทรวงสาธารณสุขจะแจ้งให้ท่านทราบและขอความยืนยอมจากท่านเพิ่มเติม</span>
        </span>
        <h3>2. ข้อมูลที่ท่านได้ให้ไว้กับกระทรวงสาธารณสุข ผ่านเว็บแอพพลิเคชั่นผ่านหมอพร้อมและหมอพร้อมสเตชั่น</h3>
        <span class="ml-10">2.1 ข้อมูลที่ท่านให้ไว้กับกระทรวงสาธารณสุข (แอพพลิเคชันหมอพร้อม) เพื่อใช้สำหรับเอกสารยินยอมในการเก็บรวบรวมใช้ หรือเปิดเผยข้อมูลส่วนบุคคล บนระบบหมอพร้อม Station</span>
        <h3>ฟังก์ชัน หมอพร้อม Commerce</h3>
        <span>
          <h3>ข้อมูลของผู้ได้รับอนุญาต/ผู้ยื่นคำขอ</h3>
          <div class="ml-10">
          ก. ชื่อ-นามสกุลแพทย์<br/>
          ข. เลขประจำตัวประชาชน<br/>
          ค. เบอร์โทรศัพท์มือถือ<br/>
          ง. อีเมล<br/>
          </div>
        </span >
        <span class="ml-10">
          <h3>ข้อมูลของผู้ได้รับอนุญาต/ผู้ยื่นคำขอ</h3>
          <div class="ml-10">
          ก. ชื่อสถานพยาบาล<br/>
          ข. เบอร์โทรศัพท์มือถือ<br/>
          ค. อีเมล<br/>
          ง. ที่อยู่สถานที่ประกอบการ<br/>
          </div>
        </span>
        <div class="ml-10 mt-2">
        <span>2.2 เมื่อท่านติดต่อกระทรวงสาธารณสุข กระทรวงสาธารณสุขอาจขอข้อมูลเพื่อยืนยันตัวตนของท่านและข้อมูลที่ท่านสอบถามเพื่อจะได้คำตอบกลับได้อย่างถูกต้อง ทั้งนี้
             กระทรวงสาธารณสุขอาจบันทึกข้อมูลการติดต่อของท่านในระบบของกระทรวงสาธารณสุข</span>
        </div>
        <span class="ml-10">ท่านสามารถศึกษาข้อกำหนดและเงื่อนไขในการใช้บริการ Application “หมอพร้อม” สำหรับการใช้งานแอปพลิเคชันในฐานะบุคคลทั่วไปได้ที่
        <a class="ml-10">https://mohpromt.moph.go.th/mpc/termapplication/</a></span>
        <div class="mt-2">
          <h3>3.ข้อจำกัดในการนำข้อมูลส่วนบุคคลไปใช้</h3>
          <span class="ml-10">3.1 กรพทรวงสาธารณสุขจะเก็บรวบรวมใช้ และเปิดเผยข้อมูลส่วนบุคคลของท่านเพื่อวัตถุประสงค์ตามข้อ 1 เท่านั้น</span>
        </div>
        </div>
      </v-card-text>
      <v-layout justify-center style="background-color:#F3F5F7">
      <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="colse()">ไม่ยอมรับข้อตกลง</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="agree()">ฉันเข้าใจและยอมรับ</v-btn>
       </v-container>
      </v-card-actions>
      </v-layout >
     </v-card>
    </v-dialog>
</v-container>
</template>

<script>
import DateDropdown from 'vue-date-dropdown'
// import Address2021 from '@/Thailand_Address/address2021'
import { Decode } from '@/services'
export default {
  components: {
    DateDropdown
  },
  data () {
    return {
      checkerror: false,
      checkerrors: false,
      selectedDate: '',
      consent: false,
      value: true,
      values: true,
      checkbox: false,
      dialog: false,
      user_type: 'business',
      title_name_th: '',
      first_name_th: '',
      last_name_th: '',
      title_name_eng: '',
      first_name_eng: '',
      last_name_eng: '',
      birth_date: '',
      id_card_number: '',
      email: '',
      mobile_no: '',
      username: '',
      password: '',
      re_password: '',
      biz_name_th: '',
      biz_name_eng: '',
      tax_id: '',
      tel_no: '',
      business_mobile_no: '',
      biz_email: '',
      branch_name: '',
      branch_no: '',
      house_no: '',
      address_detail: '',
      province: '',
      district: '',
      sub_district: '',
      zip_code: '',
      one_id: '',
      mountoption: [
        {
          name: 'January',
          value: 0
        },
        {
          name: 'February',
          value: 1
        },
        {
          name: 'March',
          value: 2
        },
        {
          name: 'April',
          value: 3
        },
        {
          name: 'May',
          value: 4
        },
        {
          name: 'June',
          value: 5
        },
        {
          name: 'July',
          value: 6
        },
        {
          name: 'August',
          value: 7
        },
        {
          name: 'September',
          value: 8
        },
        {
          name: 'October',
          value: 9
        },
        {
          name: 'November',
          value: 10
        },
        {
          name: 'December',
          value: 11
        }
      ],
      text_th: [
        'นาย', 'นางสาว', 'นาง'
      ],
      text_en: [
        'Mr', 'Miss', 'Mis'
      ],
      item: [
        '10310'
      ],
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        textnameth: [
          v => /^[ก-๏\s,0-9]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        emailRules: [
          v => !v || /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v) || 'กรุณากรอกEmail'
        ],
        textnameeng: [
          v => /^[A-Za-z_@.,/#&+-\s,0-9]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        first_name_th: [
          v => !!v || 'กรุณากรอกชื่อจริงภาษาไทย'
        ],
        last_name_th: [
          v => !!v || 'กรุณากรอกนามสกุลภาษาไทย'
        ],
        first_name_eng: [
          v => !!v || /^[A-Za-z_@.,/#&+-\s,0-9]+$/.test(v) || 'กรุณากรอกชื่อจริงภาษาอังกฤษ'
        ],
        last_name_eng: [
          v => !!v || /^[A-Za-z_@.,/#&+-\s,0-9]+$/.test(v) || 'กรุณากรอกนามสกุลภาษาอังกฤษ'
        ],
        user: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้งาน'
        ],
        Idcard: [
          v => /^[0-9]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ],
        tel: [
          v => /^[0-9]+$/.test(v) || 'กรุณากรอกเบอร์โทรศัพท์(เป็นตัวเลข)'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่'
        ],
        room_no: [
          v => !!v || 'กรุณาระบุห้องเลขที่'
        ],
        floor: [
          v => !!v || 'กรุณาระบุชั้นที่'
        ],
        building_name: [
          v => !!v || 'กรุณาระบุอาคาร'
        ],
        moo_ban: [
          v => !!v || 'กรุณาระบุหมู่บ้าน'
        ],
        moo_no: [
          v => !!v || 'กรุณาระบุหมู่ที่'
        ],
        soi: [
          v => !!v || 'กรุณาระบุตรอก/ซอย'
        ],
        yaek: [
          v => !!v || 'กรุณาระบุแยก'
        ],
        street: [
          v => !!v || 'กรุณาระบุถนน'
        ]
      },
      today: '',
      daysInMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
      targetDate: '',
      days: [],
      Datas: '',
      ProfileDate: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    // this.today = new Date()
    // this.targetDate = new Date(this.today.getFullYear(), this.today.getMonth(), this.today.getDate())
    // this.setDate(this.targetDate)
    // this.setYears(100)
    this.Datas = JSON.parse(Decode.decode(localStorage.getItem('BussinessData')))
    this.ProfileDate = JSON.parse(Decode.decode(localStorage.getItem('AccessToken')))
    // console.log('test_Data', this.Datas)
    // console.log('test_Data', this.ProfileDate)
    this.accessToken = this.ProfileDate.access_token
    this.user_type = 'business'
    this.title_name_th = this.ProfileDate.user_data.account_title_th
    this.first_name_th = this.ProfileDate.user_data.first_name_th
    this.last_name_th = this.ProfileDate.user_data.last_name_th
    this.title_name_eng = this.ProfileDate.user_data.account_title_eng
    this.first_name_eng = this.ProfileDate.user_data.first_name_eng
    this.last_name_eng = this.ProfileDate.user_data.last_name_eng
    this.birth_date = this.ProfileDate.user_data.birth_date
    this.id_card_number = ''
    this.email = ''
    this.mobile_no = this.ProfileDate.mobile_no
    this.username = ''
    this.password = ''
    this.re_password = ''
    this.biz_name_th = this.Datas.name_on_document_th
    this.biz_name_eng = this.Datas.name_on_document_eng
    this.tax_id = this.Datas.tax_id
    this.tel_no = ''
    this.business_mobile_no = ''
    this.biz_email = ''
    this.branch_name = this.Datas.branch_name
    this.branch_no = this.Datas.branch_no
    this.house_no = ''
    this.address_detail = this.Datas.address.building_name
    this.province = this.Datas.address.province
    this.district = this.Datas.address.tambon
    this.sub_district = this.Datas.address.amphoe
    this.zip_code = this.Datas.address.zipcode
    this.one_id = this.ProfileDate.one_id_no
  },
  methods: {
    consentalert () {
      if (this.checkbox === true) {
        this.consent = true
      }
    },
    async success () {
      if (this.checkbox === true) {
        if (this.password !== this.re_password) {
          this.checkerrors = true
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3><br/><h4>กรุณาตรวจสอบ รหัสผ่านไม่ตรงกัน</h4>' })
          window.scrollTo(0, 0)
        } else {
          if (this.business_mobile_no === '' || this.title_name_th === '' || this.first_name_th === '' || this.last_name_th === '' ||
             this.title_name_eng === '' || this.first_name_eng === '' || this.last_name_eng === '' || this.birth_date === '' || this.id_card_number === '' || this.username === '' ||
             this.password === '' || this.re_password === '' || this.email === '' || this.biz_name_th === '' || this.biz_name_eng === '' ||
             this.tax_id === '' || this.tel_no === '' || this.zip_code === '' || this.sub_district === ''
          ) {
            this.checkerror = true
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3><br/><h4>กรุณากรอกข้อมูลให้ครบ</h4>' })
            window.scrollTo(0, 0)
          } else {
            var dataRegister = {
              user_type: this.user_type,
              title_name_th: this.title_name_th,
              first_name_th: this.first_name_th,
              last_name_th: this.last_name_th,
              title_name_eng: this.title_name_eng,
              first_name_eng: this.first_name_eng,
              last_name_eng: this.last_name_eng,
              birth_date: this.birth_date,
              id_card_number: this.id_card_number,
              email: this.email,
              mobile_no: this.mobile_no,
              username: this.username,
              password: this.password,
              re_password: this.re_password,
              biz_name_th: this.biz_name_th,
              biz_name_eng: this.biz_name_eng,
              tax_id: this.tax_id,
              tel_no: this.tel_no,
              business_mobile_no: this.business_mobile_no,
              biz_email: this.biz_email,
              branch_name: this.branch_name,
              branch_no: this.branch_no,
              house_no: this.house_no,
              address_detail: this.address_detail,
              province: this.province,
              district: this.district,
              sub_district: this.sub_district,
              zip_code: this.zip_code,
              one_id: this.one_id
            }
            const auth = {
              headers: { Authorization: `Bearer ${this.accessToken}` }
            }
            var response = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/register_business_moph_commerce`, dataRegister, auth)
            // await this.$store.dispatch('actionsRegisterbuyer', dataRegister)
            // var response = this.$store.state.ModuleRegister.stateRegisterbuyer
            if (response.data.code === 200) {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ลงทะเบียนผู้ซื้อสำเร็จ</h3>' })
              localStorage.removeItem('OTPData')
              localStorage.removeItem('AccessToken')
              localStorage.removeItem('BussinessData')
              this.$router.push({ path: '/' })
            } else {
              this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
            }
          }
        }
      } else {
        this.dialog = true
      }
    },
    colse () {
      this.consent = false
      this.checkbox = false
    },
    agree () {
      this.consent = false
      this.checkbox = true
    }
  //   setDate (date) {
  //     this.setDays(date.getMonth())
  //     this.$('#select-day').val(date.getDate())
  //     this.$('#select-month').val(date.getMonth())
  //     this.$('#select-year').val(date.getFullYear())
  //   },
  //   setDays (monthIndex) {
  //     var optionCount = 0
  //     var daysCount = this.daysInMonth[0]
  //     if (optionCount < daysCount) {
  //       for (let i = optionCount; i < daysCount; i++) {
  //         this.days.push(i + 1)
  //       }
  //     } else {
  //       for (let i = daysCount; i < optionCount; i++) {
  //         var optionItem = '#select-day option[value=' + (i + 1) + ']'
  //         this.$(optionItem).remove()
  //       }
  //     }
  //   },
  //   setYears (val) {
  //     var year = 1950
  //     for (var i = 0; i < val; i++) {
  //       ('#select-year').append(this.$('<option></option>').attr('value', year + i).text(year + i))
  //     }
  //   }
  }
}
</script>

<style lans="scss">
  .v-application .success--text {
      color: #27AB9C !important;
      caret-color: #27AB9C !important;
  }
</style>
