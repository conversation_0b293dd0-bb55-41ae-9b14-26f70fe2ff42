<template>
  <v-container>
    <v-overlay :value="overlay2">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <a-row type="flex" v-if="shopSearch.length !== 0">
      <a-col :span="24">
        <a-row type="flex">
          <a-col :span="12">
            <span style="font-weight: bold;">ร้านค้าที่เกี่ยวข้องกับ "{{ textSearch }}"</span>
          </a-col>
          <a-col :span="12" style="margin-top: -10px;">
            <a-row type="flex" justify="end">
              <v-btn text color="success" @click="gotoShowAllShop()">ร้านค้าอื่นๆ<v-icon small class="pt-1">mdi-chevron-right</v-icon></v-btn>
            </a-row>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="24" style="padding: 0; margin-top: -20px; margin-bottom: -20px;">
        <a-divider></a-divider>
      </a-col>
      <a-col :span="24" style="margin-top: 0px;">
        <v-card class="mx-auto mt-5 mb-5" max-width="100%" outlined hover @click="gotoShopDetail(shopSearchShow)">
          <v-card-text style="padding: 1.5625rem;">
            <v-row no-gutters justify="start">
                <v-col cols="1" md="1" sm="12" xs="12">
                <v-avatar size="60" @click="gotoShopDetail(shopSearchShow)" v-if="shopSearchShow.path_logo !== ''" style="cursor: pointer;">
                  <img
                    alt="user"
                    :src="`${shopSearchShow.path_logo}?=${new Date().getTime()}`"
                  >
                </v-avatar>
                <v-avatar size="60" v-else style="cursor: pointer;" @click="gotoShopDetail(shopSearchShow)">
                  <v-icon>
                    mdi-storefront
                  </v-icon>
                </v-avatar>
                </v-col>
                <v-col cols="2" md="2" sm="12" xs="12">
                <v-row dense no-gutters justify="start">
                    <v-col cols="12" md="12" sm="12" xs="12">
                        <p style="font-weight: bold; font-size: 15px;">{{ shopSearchShow.name_th }}</p>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12">
                        <v-btn outlined small color="orange" @click="gotoShopDetail(shopSearchShow)"><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
                    </v-col>
                </v-row>
                </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </a-col>
    </a-row>
    <a-row type="flex" :gutter="[16, 8]" v-if="productSearch.length !== 0">
      <a-col :span="24">
        <a-row type="flex">
          <a-col :span="24">
            <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} ชิ้น ในการค้นหาคำว่า "{{ textSearch }}"</span>
          </a-col>
          <a-col :span="24" style="padding: 0; margin-top: -20px;">
            <a-divider></a-divider>
          </a-col>
          <a-col :xs="24" :sm="12" :md="4" v-for="(item, index) in productSearch" :key="index">
            <CardProducts :itemProduct="item" />
          </a-col>
        </a-row>
      </a-col>
    </a-row>
    <a-row type="flex" justify="center" style="margin-top: 10%;" v-else-if="shopSearch.length === 0 && productSearch.length === 0">
      <template>
        <a-empty
         :image-style="{height: '100px'}"
        >
          <h1 slot="description">ไม่พบผลการค้นหา</h1>
          <h3 slot="description">ลองใช้คำอื่นที่แตกต่างหรือคำอื่นที่มีความหมายกว้างกว่านี้</h3>
        </a-empty>
       </template>
    </a-row>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import { Col, Row, Divider, Empty } from 'ant-design-vue'
export default {
  components: {
    'a-row': Row,
    'a-col': Col,
    'a-divider': Divider,
    'a-empty': Empty,
    CardProducts: () => import('@/components/Card/ProductCard')
  },
  data () {
    return {
      textSearch: '',
      productSearch: [],
      shopSearch: [],
      overlay2: true,
      productCount: null,
      shopCount: null,
      PathImage: process.env.VUE_APP_IMAGE,
      shopSearchShow: []
    }
  },
  async created () {
    this.$EventBus.$emit('searchdata')
    this.$EventBus.$emit('getPath')
    await this.getResultSearch()
  },
  mounted () {
    this.$EventBus.$on('getResultSearch', this.getResultSearch)

    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getResultSearch')
    })
  },
  methods: {
    async getResultSearch () {
      this.textSearch = this.$router.currentRoute.params.data
      var data
      if (localStorage.getItem('roleUser') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        data = {
          text: this.$router.currentRoute.params.data,
          role_user: dataRole.role
        }
      } else {
        data = {
          text: this.$router.currentRoute.params.data,
          role_user: 'ext_buyer'
        }
      }
      // console.log('data to search=======>', data)
      await this.$store.dispatch('actionSearchText', data)
      var response = await this.$store.state.ModuleHompage.stateSearchText
      // console.log('Result Search ======>', response)
      if (response.result === 'SUCCESS') {
        this.productSearch = response.data.product
        this.productCount = this.productSearch.length
        this.shopSearch = response.data.seller_shop
        this.shopSearchShow = response.data.seller_shop[0]
        // console.log('shopSearchShow======>', this.shopSearchShow)
        this.shopCount = this.shopSearch.length
        // console.log(this.shopSearch)
        if (response.data.product.length === 0 && response.data.seller_shop.length === 0) {
          this.productSearch = []
          this.shopSearch = []
        //   this.$swal.fire({
        //     icon: 'error',
        //     title: 'ไม่เจอสินค้าที่ค้นหา',
        //     showConfirmButton: false,
        //     timer: 1500
        //   })
        }
        this.overlay2 = false
      }
    },
    gotoShopDetail (val) {
      const shopCleaned = encodeURIComponent(val.name_th.replace(/\s/g, '-'))
      this.$router.push(`/shoppage/${shopCleaned}-${val.id}`).catch(() => {})
    },
    gotoShowAllShop () {
      localStorage.setItem('AllShopSearch', Encode.encode(this.shopSearch))
      this.$router.push(`/search-shop/${this.textSearch}`).catch(() => {})
    }
  }
}
</script>

<style scoped>

</style>
