import Vue from 'vue'
import App from './App.vue'
import VueCookies from 'vue-cookies'
// import './registerServiceWorker'
import router from './router'
import store from './store'
import vuetify from './plugins/vuetify'
import VueSweetalert2 from 'vue-sweetalert2'
import axios from 'axios'
import VueAxios from 'vue-axios'
import VueMeta from 'vue-meta'
import CKEditor from '@ckeditor/ckeditor5-vue'
import VueSnip from 'vue-snip'
import LazyLoadDirective from './directives/LazyLoadDirective'
import VueI18n from 'vue-i18n'
// import translations
import th from './locales/th.js'
import en from './locales/en.js'

// import { io } from 'socket.io-client'
// style Global
import './assets/styles.css'
import './assets/style_project.css'
import './assets/content-style.css'
import 'sweetalert2/dist/sweetalert2.min.css'
// import Antd from 'ant-design-vue'
// import VueThailandAddress from 'vue-thailand-address'
// import VueMask from 'v-mask'
// import 'vue-thailand-address/dist/vue-thailand-address.css'
// import VueClipboard from 'vue-clipboard2'
// import VueJsonLD from 'vue-jsonld'
// import 'vue-cool-select/dist/themes/bootstrap.css'
// import '@fortawesome/fontawesome-free/css/all.css'
// import '@fortawesome/fontawesome-free/js/all.js'
// import 'ant-design-vue/dist/antd.css'
// import VueSocials from 'vue-socials'
// import VueApexCharts from 'vue-apexcharts'
// import VueMoment from 'vue-moment'
// import moment from 'moment'
// require('moment/locale/th')
// moment.locale('th')
// Vue.use(VueApexCharts)
// Vue.component('apexchart', VueApexCharts)
// Vue.prototype.$socket = io('http://**********:3000')
Vue.use(VueSnip)
Vue.use(CKEditor)
Vue.use(VueAxios, axios)
Vue.use(VueSweetalert2)
Vue.use(VueCookies)
Vue.use(VueMeta, {
  keyName: 'metaInfo',
  attribute: 'data-vue-meta',
  tagIDKeyName: 'vmid',
  refreshOnceOnNavigation: true
})
// Vue.use(VueSocialSharing)
// Vue.use(VueSocials)
// Vue.use(Antd)
// Vue.use(VueThailandAddress)
// VueClipboard.config.autoSetContainer = true
// Vue.use(VueClipboard)
// Vue.use(VueMask)
// Vue.use(VueJsonLD)
// Vue.use(VueMoment, {
//   moment
// })

Vue.use({
  install: function (Vue, options) {
    Vue.prototype.$jQuery = require('jquery')
  }
})
Vue.use(VueI18n)
export const i18n = new VueI18n({
  locale: 'th',
  messages: { th, en }
})

// Vue.filter('formatDate', function (val) {
//   if (val) {
//     return moment(String(val)).format('MM-DD-YYYY')
//   }
// })
Vue.config.productionTip = false
Vue.config.performance = true
Vue.directive('lazyload', LazyLoadDirective)

Vue.prototype.$EventBus = new Vue()
// Vue.prototype.$liff = window.liff

new Vue({
  i18n,
  router,
  store,
  vuetify,
  render: function (h) { return h(App) }
}).$mount('#app')
