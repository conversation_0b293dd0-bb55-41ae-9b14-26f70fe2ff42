const ModuleGlobal = {
  state: {
    Loader: false,
    initialRoute: null,
    previousRoute: null
  },
  mutations: {
    closeLoader (state) {
      state.Loader = false
    },
    openLoader (state) {
      state.Loader = true
    },
    setInitialRoute (state, route) {
      state.initialRoute = route
    },
    setPreviousRoute (state, route) {
      state.previousRoute = route
    }
  },
  actions: {
  }
}
export default ModuleGlobal
