<template>
    <manageDocumentPartnerRequest/>
</template>
<script>
export default {
  components: {
    manageDocumentPartnerRequest: () => import('@/components/Shop/Partner/ManageDocumentPartner')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SettingPartnerRequestMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/SettingPartnerRequest' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>
