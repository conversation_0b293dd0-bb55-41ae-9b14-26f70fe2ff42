<template>
  <v-container>
    <!-- Website -->
    <div style="background: #FFFFFF; border: 1px solid #F3F5F7; border-radius: 12px;" v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="6" md="12" align="center" class="my-16">
            <v-form ref="formOTP" :lazy-validation="lazy">
              <v-card width="100%" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;" elevation="0">
                <v-card-text>
                  <v-container>
                    <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                      <v-img :src="require('@/assets/OTP_icon.png')" max-height="401" max-width="316" contain/>
                    </v-row>
                    <v-row no-gutters dense class="mx-12" justify="center">
                      <v-col cols="12" md="3" sm="12">
                        <v-otp-input
                         v-model="otp"
                         :length="length"
                         plain
                         oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        ></v-otp-input>
                      </v-col>
                      <v-col cols="12" md="12" sm="12">
                        <span>Ref: {{ refCode }}</span><v-btn icon @click="RefreshOTP()" small class="ml-2"><v-icon color="#27AB9C">mdi-refresh</v-icon></v-btn>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="mt-10">
                        <v-btn color="#27AB9C" style="padding-left: 8%; padding-right: 8%; color: white;"  @click="checkOTP()" :disabled="!isActive">ยืนยัน</v-btn>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div style="background: #FFFFFF; border: 1px solid #F3F5F7; border-radius: 12px;" v-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <v-form ref="formOTP" :lazy-validation="lazy">
            <v-card width="100%" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;" elevation="0">
              <v-card-text>
                <v-container>
                  <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                    <v-img :src="require('@/assets/OTP_icon.png')" max-height="401" max-width="316" contain/>
                  </v-row>
                  <v-row no-gutters dense class="mx-12" justify="center">
                    <v-col cols="12" md="12" sm="12">
                      <v-otp-input
                        v-model="otp"
                        :length="length"
                        plain
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                      ></v-otp-input>
                    </v-col>
                    <v-col cols="12" md="12" sm="12">
                      <span>Ref: {{ refCode }}</span><v-btn icon @click="RefreshOTP()" small class="ml-2"><v-icon color="#27AB9C">mdi-refresh</v-icon></v-btn>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" class="mt-10">
                      <v-btn color="#27AB9C" block style="padding-left: 8%; padding-right: 8%; color: white;"  @click="checkOTP()" :disabled="!isActive">ยืนยัน</v-btn>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card-text>
            </v-card>
          </v-form>
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div  style="background: #FFFFFF; border: 1px solid #F3F5F7; border-radius: 12px;" v-if="MobileSize">
      <v-container class="my-6">
        <v-row dense justify="center" align-content="center">
          <v-col cols="12" md="12">
            <v-form ref="formOTP" :lazy-validation="lazy">
              <v-card width="100%" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;" elevation="0">
                <v-card-text>
                  <v-container>
                    <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                      <v-img :src="require('@/assets/OTP_icon.png')" max-height="301" max-width="216" contain/>
                    </v-row>
                    <v-row no-gutters dense class="mx-0" justify="center">
                      <v-col cols="12" md="12" sm="12">
                        <v-otp-input
                         v-model="otp"
                         :length="length"
                         plain
                         oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        ></v-otp-input>
                      </v-col>
                      <v-col cols="12" md="12" sm="12">
                        <span>Ref: {{ refCode }}</span><v-btn icon @click="RefreshOTP()" small class="ml-2"><v-icon color="#27AB9C">mdi-refresh</v-icon></v-btn>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="mt-10">
                        <v-btn color="#27AB9C" block style="padding-left: 8%; padding-right: 8%; color: white;"  @click="checkOTP()" :disabled="!isActive">ยืนยัน</v-btn>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      dataOTP: [],
      refCode: '',
      otpCode: '',
      mobile: '',
      length: 6,
      otp: '',
      email: '',
      phone: '',
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ]
      }
    }
  },
  created () {
    this.dataOTP = JSON.parse(Decode.decode(localStorage.getItem('OTPData')))
    this.refCode = this.dataOTP.ref_code
    this.otpCode = this.dataOTP.otp
    this.mobile = this.dataOTP.mobileNo
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    isActive () {
      return this.otp.length === this.length
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async RefreshOTP () {
      this.otp = ''
      var mobile = this.mobile
      var dataReOTP = {
        mobile_no: this.mobile
      }
      await this.$store.dispatch('actionsGetOTP', dataReOTP)
      var resReOTP = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
      if (resReOTP.result === 'SUCCESS') {
        this.refCode = resReOTP.data.ref_code
        this.otpCode = resReOTP.data.otp
        localStorage.removeItem('OTPData')
        var dataOTP = {
          otp: resReOTP.data.otp,
          ref_code: resReOTP.data.ref_code,
          mobileNo: mobile
        }
        localStorage.setItem('OTPData', Encode.encode(dataOTP))
      }
    },
    async checkOTP () {
      if (this.otp === this.otpCode) {
        this.$store.commit('openLoader')
        var data = {
          mobile_no: this.mobile,
          otp: this.otp
        }
        // console.log(data)
        await this.$store.dispatch('actionsConfirmOTP', data)
        var res = await this.$store.state.ModuleRegisMorpromt.stateConfirmOTP
        if (res.result === 'SUCCESS') {
          // console.log(res.data)
          this.$store.commit('closeLoader')
          localStorage.setItem('AccessToken', Encode.encode(res.data))
          this.$router.push({ path: '/findTax' }).catch(() => {})
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          html: 'คุณกรอกรหัส OTP ผิด',
          title: 'คุณต้องการส่งรหัสใหม่หรือไม่',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
          // cancelButtonColor: '#d33'
        }).then(async (result) => {
          this.otp = ''
          var mobile = this.mobile
          if (result.isConfirmed) {
            this.$store.commit('openLoader')
            var dataReOTP = {
              mobile_no: this.mobile
            }
            await this.$store.dispatch('actionsGetOTP', dataReOTP)
            var resReOTP = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
            if (resReOTP.result === 'SUCCESS') {
              this.$store.commit('closeLoader')
              this.refCode = resReOTP.data.ref_code
              this.otpCode = resReOTP.data.otp
              localStorage.removeItem('OTPData')
              var dataOTP = {
                otp: resReOTP.data.otp,
                ref_code: resReOTP.data.ref_code,
                mobileNo: mobile
              }
              localStorage.setItem('OTPData', Encode.encode(dataOTP))
            }
            // this.googleSentData()
          } else if (result.isDismissed) {
            this.$router.push({ path: '/regisMorhpromt' }).catch(() => {})
          }
        }).catch(() => {
        })
      }
    }
  }
}
</script>

<style scoped>
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
