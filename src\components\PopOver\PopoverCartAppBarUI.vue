<template>
  <div>
    <a-popover v-model="visibleCart" :placement="MobileSize ? 'bottom' : 'bottomRight'" trigger="click" v-if="this.productList.length !== 0">
      <template slot="content" >
        <a-card v-if="MobileSize" class="cart-popover" style="max-width: 290px; width: 290px; overflow-y: auto; max-height: 250px; border-radius: 8px;">
          <a-row type="flex" justify="center" >
            <a-col :span="24" v-for="(item, index) in newCartItem" :key="index">
              <div class="container" style="padding-left: 0px; padding-right: 0px;">
                <a-row type="flex">
                  <a-col cols="4" :span="4">
                    <v-img :src="`${item.product_image}`" class="image" contain v-if="item.product_image !== ''" />
                    <v-img src="@/assets/NoImage.png" class="image" v-else />
                  </a-col>
                  <a-col cols="8" :span="20">
                    <span style="padding-left: 30px; font-weight: bold">{{ setProductName(item.product_name) }}</span><br>
                    <span style="padding-left: 30px">{{ $t('AppBar.sku') }}: {{ setProductSku(item.main_sku) }}</span><br>
                    <span style="padding-left: 30px;">{{ $t('AppBar.Quantity') }}: {{ item.quantity }}</span>
                    <span style="padding-left: 30px">฿ {{ Number(item.vat_default === 'yes' ? parseFloat(item.revenue_default) + parseFloat(item.vat_include) : item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
                    <div v-if="item.have_attribute !== 'no'">
                      <div v-if="checkOneData === null">
                        <span v-if="item.attribute_option_1" style="padding-left: 30px;">{{ item.key_1_value | truncatename(5, ' ')}}: {{ item.attribute_option_1 | truncate(15, '...') }}</span><br/>
                        <span v-if="item.attribute_option_2" style="padding-left: 30px">{{ item.key_2_value | truncatename(5, ' ') }}: {{ item.attribute_option_2 | truncate(15, '...') }}</span>
                      </div>
                      <div v-else>
                        <span v-if="item.product_attribute_detail.attribute_priority_1" style="padding-left: 30px;">{{ item.product_attribute_detail.key_1_value | truncatename(5, ' ') }}: {{ item.product_attribute_detail.attribute_priority_1 | truncate(15, '...') }}</span> <br/>
                        <span v-if="item.product_attribute_detail.attribute_priority_2" style="padding-left: 30px">{{ item.product_attribute_detail.key_2_value | truncatename(5, ' ') }}: {{ item.product_attribute_detail.attribute_priority_2 | truncate(15, '...') }}</span>
                      </div>
                    </div>
                    <span style="padding-left: 30px" v-if="item.pay_type !== 'general'">Pay Type: {{ item.pay_type === 'onetime' ?  'Onetime' : 'Recurring'}}</span>
                  </a-col>
                  <!-- <a-col cols="12" :span="2" style="padding-left: 0px; transform: translateY(154px);">
                    <a-icon type="delete" @click="dialogDelete(item)"/>
                  </a-col> -->
                </a-row>
                <a-row>
                  <a-col :span="22"></a-col>
                  <a-col cols="12" :span="2" style="padding-left: 0px;">
                    <a-icon type="delete" @click="dialogDelete(item)"/>
                  </a-col>
                </a-row>
              </div>
              <a-divider v-if="newCartItem.length !== 1 && index !== (newCartItem.length - 1)" style="margin-bottom: 0px; margin-top: 0px;"/>
            </a-col>
          </a-row>
        </a-card>
        <a-card v-else class="cart-popover" style="max-width: 360px; width: 360px; overflow-y: auto; max-height: 250px; border-radius: 8px;">
          <a-row type="flex" justify="center" >
            <a-col :span="24" v-for="(item, index) in newCartItem" :key="index">
              <div class="container">
                <a-row type="flex">
                  <a-col cols="4" :span="4">
                    <v-img :src="`${item.product_image}`" class="image" contain v-if="item.product_image !== ''" />
                    <v-img src="@/assets/NoImage.png" class="image" v-else />
                  </a-col>
                  <a-col cols="6" :span="18">
                    <span style="padding-left: 25px; font-weight: bold">{{ setProductName(item.product_name) }}</span><br>
                    <span style="padding-left: 25px">{{ $t('AppBar.sku') }}: {{ setProductSku(item.main_sku) }}</span><br>
                    <span style="padding-left: 25px;">{{ $t('AppBar.Quantity') }}: {{ item.quantity }}</span>
                    <span style="padding-left: 25px">฿ {{ Number(item.vat_default === 'yes' ? parseFloat(item.revenue_default) + parseFloat(item.vat_include) : item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
                    <div v-if="item.have_attribute !== 'no'">
                      <div v-if="checkOneData === null">
                        <span v-if="item.attribute_option_1" style="padding-left: 25px;">{{ item.key_1_value | truncatename(8, ' ') }}: {{ item.attribute_option_1 | truncate(20, '...') }}</span><br/>
                        <span v-if="item.attribute_option_2" style="padding-left: 25px">{{ item.key_2_value | truncatename(8, ' ') }}: {{ item.attribute_option_2 | truncate(20, '...') }}</span>
                      </div>
                      <div v-else>
                        <span v-if="item.product_attribute_detail.attribute_priority_1" style="padding-left: 25px;">{{ item.product_attribute_detail.key_1_value | truncatename(8, ' ') }}: {{ item.product_attribute_detail.attribute_priority_1 | truncate(20, '...') }}</span> <br/>
                        <span v-if="item.product_attribute_detail.attribute_priority_2" style="padding-left: 25px">{{ item.product_attribute_detail.key_2_value | truncatename(8, ' ') }}: {{ item.product_attribute_detail.attribute_priority_2 | truncate(20, '...') }}</span>
                      </div>
                    </div>
                    <span style="padding-left: 25px" v-if="item.pay_type !== 'general'">Pay Type: {{ item.pay_type === 'onetime' ?  'Onetime' : 'Recurring'}}</span>
                  </a-col>
                  <a-col cols="2" :span="2" style="padding-left: 0px">
                    <br>
                    <br>
                    <br>
                    <a-icon type="delete" @click="dialogDelete(item)"/>
                  </a-col>
                </a-row>
              </div>
              <a-divider v-if="newCartItem.length !== 1 && index !== (newCartItem.length - 1)" style="margin-bottom: 0px; margin-top: 0px;"/>
            </a-col>
          </a-row>
        </a-card>
        <a-row justify="center" type="flex">
          <a-col :span="24">
            <a-row justify="center" type="flex">
              <a-col :span="20" style="padding: 10px 0">
                <a-button block class="ant-btn-primary" @click="goCart()">
                  {{ $t('AppBar.ViewCart') }}
                </a-button>
                <!-- <a-button block icon="shopping-cart" @click="goCart()">
                  ไปที่รถเข็นสินค้า
                </a-button> -->
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </template>
      <v-btn icon @mousedown.right="goCartRightClick()" :href="path + 'shoppingcart'">
        <v-badge class="mt-1" :content="countCart" :value="countCart" color="red" top bordered overlap>
          <!-- <v-icon color="#FFFFFF">mdi-cart-outline</v-icon> -->
          <v-img :src="require('@/assets/CartNew.png')" :max-height="MobileSize ? '24px' : '32px'" :max-width="MobileSize ? '24px' : '32px'"></v-img>
        </v-badge>
      </v-btn>
    </a-popover>
    <a-popover  v-model="visibleNoCart" :placement="MobileSize ? 'bottom' : 'bottomRight'" trigger="click" v-else>
      <v-btn icon>
        <v-badge class="mt-1" :content="countCart" :value="countCart" color="red" top bordered overlap>
          <!-- <v-icon color="#FFFFFF">mdi-cart-outline</v-icon> -->
          <v-img :src="require('@/assets/CartNew.png')" contain :max-height="MobileSize ? '24px' : '32px'" :max-width="MobileSize ? '24px' : '32px'"></v-img>
        </v-badge>
      </v-btn>
      <template slot="content">
        <a-card v-if="MobileSize" style="width: 280px; height: 280px;" align="center">
          <v-img src="@/assets/emptycart.png" max-height="2000px" max-width="2000px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          <h2 style="padding-top: 20px; color: #27AB9C;"><b>{{ $t('AppBar.NoCart') }}</b></h2>
        </a-card>
        <a-card v-else style="width: 350px; height: 250px;" align="center">
          <v-img src="@/assets/emptycart.png" max-height="2000px" max-width="2000px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          <h2 style="padding-top: 20px; color: #27AB9C;"><b>{{ $t('AppBar.NoCart') }}</b></h2>
        </a-card>
      </template>
    </a-popover>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Button, Card, Col, Divider, Icon, Popover, Row } from 'ant-design-vue'
export default {
  components: {
    'a-popover': Popover,
    'a-card': Card,
    'a-row': Row,
    'a-col': Col,
    'a-icon': Icon,
    'a-divider': Divider,
    'a-button': Button
  },
  data () {
    return {
      productList: [],
      countCart: null,
      CartLangth: null,
      visibleNoCart: false,
      visibleCart: false,
      tempProductList: [],
      checkOneData: '',
      path: process.env.VUE_APP_DOMAIN,
      couponDetail: null,
      checkCancelCoupon: false,
      userDetailSelling: '',
      customerData: ''
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    },
    truncatename: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit))
      }
      return value
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    newCartItem () {
      const newCartItem = this.productList
      return newCartItem
    }
  },
  watch: {
    productList (val) {
      // console.log('productList', val)
    }
  },
  async created () {
    window.scrollTo(0, 0)
    if (localStorage.getItem('UserDetailSelling') !== null) {
      this.userDetailSelling = JSON.parse(Decode.decode(localStorage.getItem('UserDetailSelling')))
    }
    if (localStorage.getItem('OnlinePurchase') !== null) {
      this.customerData = JSON.parse(Decode.decode(localStorage.getItem('OnlinePurchase')))
    }
    this.checkOneData = localStorage.getItem('oneData')
    // this.$EventBus.$on('getCartPopOver', this.Start)
    this.$EventBus.$on('closeModalCartNoLogin', this.closeModalCartNoLogin)
    this.$EventBus.$on('closeModalCart', this.closeModalCart)
    // this.getDetailCartGracz()
    this.Start()
  },
  mounted () {
    this.$EventBus.$on('getCartPopOver', this.Start)
    this.$EventBus.$on('getPopoverPanit', this.backToStartPanit)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getCartPopOver')
      this.$EventBus.$off('getPopoverPanit')
    })
  },
  methods: {
    backToStartPanit () {
      // this.countCart = 0
      if (localStorage.getItem('oneData') === null) {
        this.getDetailCartLocal()
      } else {
        this.getDetailCartLogin()
      }
    },
    Start () {
      if (this.userDetailSelling) {
        this.getDetailCartGracz()
      } else {
        if (localStorage.getItem('oneData') === null) {
          this.getDetailCartLocal()
        } else {
          this.getDetailCartLogin()
        }
      }
    },
    checkDataShopList (data) {
      const shoplist = []
      data.forEach(element => {
        shoplist.push({ shop_id: element.shop_id, selectData: [], shop_name: element.shop_name, product_list: this.checkProductList(element.product_list) })
      })
      return shoplist
    },
    checkProductList (data) {
      const productlist = []
      data.forEach(element => {
        productlist.push({
          key_1_value: element.key_1_value,
          key_2_value: element.key_2_value,
          have_attribute: element.have_attribute,
          product_id: element.product_id,
          attribute_option_1: element.attribute_option_1,
          attribute_option_2: element.attribute_option_2,
          product_attribute_id: element.product_attribute_id,
          product_name: element.product_name,
          product_image: element.product_image,
          sku: element.sku,
          quantity: element.quantity,
          price: element.price,
          stock: element.stock,
          net_price: element.net_price,
          max_per_order: element.max_per_order,
          min_per_order: element.min_per_order
        })
      })
      return productlist
    },
    async getDetailCartLocal () {
      this.productList = []
      if (localStorage.getItem('_cartData') !== null) {
        var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
        const data = {
          shop_to_cal: [],
          product_to_calculate: [],
          shop_list: this.checkDataShopList(cartData.shop_list)
        }
        await this.$store.dispatch('ActionLocalstorageDetailCart', data)
        var res = await this.$store.state.ModuleCart.stateLocalstorageDetailCart
        if (res.message === 'Get localstorage detail cart success') {
          var setData = {
            product_to_cal: res.data.product_to_calculate.length !== 0 ? res.data.product_to_calculate : [],
            shop_to_cal: res.data.shop_to_cal.length !== 0 ? res.data.shop_to_cal : [],
            address_data: {},
            shop_list: res.data.shop_list
          }
          localStorage.setItem('_cartData', Encode.encode(setData))
          for (let index = 0; index < res.data.shop_list.length; index++) {
            const element = res.data.shop_list[index]
            for (let j = 0; j < element.product_list.length; j++) {
              const element2 = element.product_list[j]
              this.productList.push(element2)
            }
          }
          this.countCart = this.productList.length
        }
        // const selectDataCartLocal = cartData.shop_list[0].product_list.map(item => item.have_attribute === 'yes' ? item.product_attribute_id : item.sku)
        // localStorage.setItem('_selectDataCartFirstTime', Encode.encode(selectDataCartLocal))
      }
    },
    async getDetailCartLogin () {
      // console.log('getDetailCartLogin')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // console.log('dataRole.role', dataRole.role)
      var companyDetail
      var sellerShopID
      if (localStorage.getItem('SetRowCompany') !== null) {
        // console.log('1')
        companyDetail = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      } else if (dataRole.role === 'sale_order') {
        // console.log('2')
        sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
        var companyID = JSON.parse(localStorage.getItem('PartnerID'))
      } else {
        // console.log('3')
        sellerShopID = ''
      }
      // // var CartLangth
      var ListProduct = []
      // // console.log('in get cart login')
      this.productList = []
      var data
      if (dataRole.role === 'sale_order_no_JV') {
        sellerShopID = JSON.parse(localStorage.getItem('ShopID'))
        var customerID = JSON.parse(localStorage.getItem('partner_id'))
        data = {
          role_user: dataRole.role,
          seller_shop_id: sellerShopID,
          product_to_calculate: [],
          pay_type: '',
          coupon: this.couponDetail !== null ? this.couponDetail : [],
          company_id: -1,
          company_position: -1,
          com_perm_id: -1,
          customer_id: customerID
        }
      } else {
        data = {
          role_user: dataRole.role,
          seller_shop_id: sellerShopID,
          product_to_calculate: [],
          pay_type: '',
          coupon: this.couponDetail !== null ? this.couponDetail : [],
          company_id: dataRole.role === 'ext_buyer' ? -1 : dataRole.role === 'sale_order' ? companyID : companyDetail !== undefined ? companyDetail.company.company_id : -1,
          company_position: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.role_id : -1,
          com_perm_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.com_perm_id : -1
        }
      }
      // console.log('ssssata1', data)
      await this.$store.dispatch('ActionDetailCart', data)
      const res = await this.$store.state.ModuleCart.stateDetailCart
      // console.log('res =========================', res)
      if (res.message === 'Get detail cart success') {
        this.productList = []
        this.tempProductList = []
        this.checkCancelCoupon = true
        ListProduct = res.data[0].shop_list
        // ListProduct = [
        //   {
        //     selectData: [],
        //     seller_shop_id: 1,
        //     seller_shop_name: 'ร้านเจเจ',
        //     product_onetime: [
        //       {
        //         product_id: '63',
        //         main_sku: 'INET-*********',
        //         sku: 'INET-*********',
        //         item_code: 'I-CLD-01-00011',
        //         product_name: 'Commercial Plus-VDisk (SSD Caching) Snapshot 7 Coppies(On Site) & 7 Coppies(Off Site)',
        //         unit_type: 'GB',
        //         quantity: '10',
        //         cost_unit: '2.29',
        //         internal: '2.29',
        //         external_jv: '0.00',
        //         external: '0.00',
        //         inventory_code: 'INET-*********',
        //         product_ratio: '1',
        //         actual_stock: 0,
        //         effective_stock: 0,
        //         manufacturer_data: {
        //           id: 6,
        //           manufacturer_name: 'Default'
        //         },
        //         supplier_data: {
        //           id: 6,
        //           manufacturer_name: 'Default'
        //         },
        //         have_attribute: 'no',
        //         product_status: 'active',
        //         product_image: '',
        //         volumn: {
        //           width: '0',
        //           length: '0',
        //           height: '0',
        //           weight: '0'
        //         },
        //         revenue_default: 100,
        //         vat_type: 0,
        //         vat_revenue: 0,
        //         revenue_vat: 1000
        //       }
        //     ],
        //     product_recurring: [
        //       {
        //         product_id: '65',
        //         main_sku: 'INET-230621003',
        //         sku: 'INET-230621003',
        //         item_code: 'I-CLD-01-00006',
        //         product_name: 'Commercial Plus -VCPU(VCPU)',
        //         unit_type: 'VCPU',
        //         quantity: '5',
        //         cost_unit: '121.90',
        //         internal: '121.90',
        //         external_jv: '0.00',
        //         external: '0.00',
        //         inventory_code: 'INET-230621003',
        //         product_ratio: '1',
        //         actual_stock: 0,
        //         effective_stock: 0,
        //         manufacturer_data: {
        //           id: 6,
        //           manufacturer_name: 'Default'
        //         },
        //         supplier_data: {
        //           id: 6,
        //           manufacturer_name: 'Default'
        //         },
        //         have_attribute: 'no',
        //         product_status: 'status',
        //         product_image: '',
        //         volumn: {
        //           width: '0',
        //           length: '0',
        //           height: '0',
        //           weight: '0'
        //         },
        //         revenue_default: 300,
        //         vat_type: 7,
        //         vat_revenue: 105,
        //         revenue_vat: 1605
        //       }
        //     ]
        //   },
        //   {
        //     selectData: [],
        //     seller_shop_id: 4,
        //     seller_shop_name: 'ร้าน ทดสอบ E-Ordering',
        //     product_recurring: [
        //       {
        //         product_id: '66',
        //         main_sku: 'INET-230621004',
        //         sku: 'INET-230621004',
        //         item_code: 'I-CLD-01-00011',
        //         product_name: 'Commercial Plus-VDisk (SSD Caching) Snapshot 7 Coppies(On Site) & 7 Coppies(Off Site)',
        //         unit_type: 'GB',
        //         quantity: '3',
        //         cost_unit: '2.29',
        //         internal: '2.29',
        //         external_jv: '0.00',
        //         external: '0.00',
        //         inventory_code: 'INET-230621004',
        //         product_ratio: '1',
        //         actual_stock: 0,
        //         effective_stock: 0,
        //         manufacturer_data: {
        //           id: 6,
        //           manufacturer_name: 'Default'
        //         },
        //         supplier_data: {
        //           id: 6,
        //           manufacturer_name: 'Default'
        //         },
        //         have_attribute: 'no',
        //         product_status: 'active',
        //         product_image: '',
        //         volumn: {
        //           width: '0',
        //           length: '0',
        //           height: '0',
        //           weight: '0'
        //         },
        //         revenue_default: 400,
        //         vat_type: 7,
        //         vat_revenue: 84,
        //         revenue_vat: 1284
        //       }
        //     ]
        //   }
        // ]
        this.tempProductList = res.data[0]
        if (ListProduct.length !== 0) {
          for (let index = 0; index < ListProduct.length; index++) {
            const element = ListProduct[index]
            if (element.product_onetime !== undefined) {
              for (let j = 0; j < element.product_onetime.length; j++) {
                element.product_onetime[j].pay_type = 'onetime'
                this.productList.push(element.product_onetime[j])
              }
            }
            if (element.product_recurring !== undefined) {
              for (let k = 0; k < element.product_recurring.length; k++) {
                element.product_recurring[k].pay_type = 'recurring'
                this.productList.push(element.product_recurring[k])
              }
            }
            if (element.product_general !== undefined) {
              for (let l = 0; l < element.product_general.length; l++) {
                element.product_general[l].pay_type = 'general'
                this.productList.push(element.product_general[l])
              }
            }

            this.countCart = this.productList.length
            // this.$EventBus.$emit('getCartTable')
            // var oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            // console.log('111', oneData)
            // // console.log(ListProduct)
            // if (oneData.cartData !== undefined) {
            //   if (oneData.cartData.length !== 0) {
            //     oneData.cartData.checkAll = false
            //     oneData.cartData.checkProductNormal = false
            //     oneData.cartData.checkProductOneTime = false
            //     oneData.cartData.checkProductRecurring = false
            //     oneData.cartData.checkStatus = false
            //     oneData.cartData.shop_list = ListProduct
            //   }
            // }
            // localStorage.setItem('oneData', Encode.encode(oneData))
            // console.log(oneData)
          }
          // console.log('product List', this.productList, this.countCart)
        } else {
          this.countCart = 0
          // this.$EventBus.$emit('getCartTable')
        }
        // const selectDataCartLocal = this.productList.map(item => item.have_attribute === 'yes' ? item.product_attribute_detail.product_attribute_id : item.sku)
        // localStorage.setItem('_selectDataCartFirstTime', Encode.encode(selectDataCartLocal))
        // localStorage.setItem('_productListCartLoginFirstTime', Encode.encode(this.productList))
      } else if (res.message === 'Not fail cart.') {
        this.countCart = 0
      } else if (res.message === 'Not found cart') {
        this.productList = []
        this.countCart = 0
      } else if (res.message === 'No product in cart.') {
        this.countCart = 0
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        if (this.MobileSize) {
          this.$swal.fire({ toast: true, showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>SERVER ERROR</h3>' })
        } else {
          this.$swal.fire({ toast: true, showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'SERVER ERROR' })
        }
      }
    },
    dialogDelete (item) {
      if (this.MobileSize) {
        this.$swal.fire({
          icon: 'warning',
          html: '<h3>คุณต้องการลบสินค้าหรือไม่</h3>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.UpdateCart(item)
          } else if (result.isDismissed) {
            this.getDetailCart()
          }
        }).catch(() => {
        })
      } else {
        this.$swal.fire({
          icon: 'warning',
          title: 'คุณต้องการลบสินค้าหรือไม่',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.UpdateCart(item)
          } else if (result.isDismissed) {
            this.getDetailCart()
          }
        }).catch(() => {
        })
      }
    },
    UpdateCart (val) {
      this.updateCartPanit(val)
      // if (this.userDetailSelling) {
      //   this.updateCartGracz(val)
      // } else {
      //   this.updateCartPanit(val)
      // }
    },
    async updateCartPanit (val) {
      // console.log('updateCartPanit', val)
      var attributeDetail
      var productAttributeDetail = []
      if (localStorage.getItem('oneData') === null) {
        var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
        for (let index = 0; index < cartData.shop_list.length; index++) {
          const element = cartData.shop_list[index]
          for (let index2 = 0; index2 < element.product_list.length; index2++) {
            const element2 = element.product_list[index2]
            if (element2.have_attribute === 'yes') {
              if (element2.product_attribute_id === val.product_attribute_id) {
                element.product_list.splice(index2, 1)
                if (this.MobileSize) {
                  this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>' })
                } else {
                  this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ลบสินค้าในรถเข็นเรียบร้อย' })
                }
              }
            } else {
              if (element2.sku === val.sku) {
                element.product_list.splice(index2, 1)
                if (this.MobileSize) {
                  this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>' })
                } else {
                  this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ลบสินค้าในรถเข็นเรียบร้อย' })
                }
              }
            }
          }
          if (element.product_list.length === 0) {
            cartData.shop_list.splice(index, 1)
          }
        }
        if (cartData.length === 0) {
          localStorage.removeItem('_cartData')
          this.Start()
        } else {
          await localStorage.setItem('_cartData', Encode.encode(cartData))
          this.Start()
          // this.$EventBus.$emit('getCartTable')
        }
        if (val.product_attribute_detail !== undefined) {
          attributeDetail = val.product_attribute_detail
        } else {
          productAttributeDetail.push({ product_attribute_id: '-1', attribute_priority_1: '', attribute_priority_2: '' })
          attributeDetail = productAttributeDetail[0]
        }
        // console.log('attributeDetail1', attributeDetail)
      } else {
        const dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyID
        var companyDetail
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyDetail = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        } else if (dataRole.role === 'sale_order') {
          companyID = JSON.parse(localStorage.getItem('PartnerID'))
        }
        if (val.product_attribute_detail !== undefined) {
          attributeDetail = val.product_attribute_detail
        } else {
          productAttributeDetail.push({ product_attribute_id: '-1', attribute_priority_1: '', attribute_priority_2: '' })
          attributeDetail = productAttributeDetail[0]
        }
        let sellerShopID = ''
        // console.log('this.tempProductList===========>', this.tempProductList)
        this.tempProductList.shop_list.forEach(element => {
          // element.product_list.forEach(product => {
          //   if (product.product_id === val.product_id) {
          //     sellerShopID = element.seller_shop_id
          //   }
          // })
          if (element.product_recurring !== undefined) {
            element.product_recurring.forEach(productList => {
              if (productList.product_id === val.product_id) {
                sellerShopID = element.seller_shop_id
              }
            })
          }
          if (element.product_onetime !== undefined) {
            // console.log(element.product_onetime)
            // console.log(typeof element.product_onetime)
            element.product_onetime.forEach(productList => {
              if (productList.product_id === val.product_id) {
                sellerShopID = element.seller_shop_id
              }
            })
          }
          if (element.product_general !== undefined) {
            element.product_general.forEach(productList => {
              if (productList.product_id === val.product_id) {
                sellerShopID = element.seller_shop_id
              }
            })
          }
        })
        var data
        if (dataRole.role === 'sale_order_no_JV') {
          var customerID = JSON.parse(localStorage.getItem('partner_id'))
          data = {
            seller_shop_id: sellerShopID,
            role_user: dataRole.role,
            product_id: val.product_id,
            pay_type: val.pay_type,
            product_attribute_detail: attributeDetail,
            // attribute_option_1: val.product_attribute_detail.attribute_priority_1 === null ? '' : val.product_attribute_detail.attribute_priority_1,
            // attribute_option_2: val.product_attribute_detail.attribute_priority_2 === null ? '' : val.product_attribute_detail.attribute_priority_2,
            order_type: 'newser',
            quantity: 0,
            company_id: -1,
            company_position: -1,
            com_perm_id: -1,
            customer_id: customerID
          }
        } else {
        // console.log('attributeDetail2', attributeDetail)
          data = {
            seller_shop_id: sellerShopID,
            role_user: dataRole.role,
            product_id: val.product_id,
            pay_type: val.pay_type,
            product_attribute_detail: attributeDetail,
            // attribute_option_1: val.product_attribute_detail.attribute_priority_1 === null ? '' : val.product_attribute_detail.attribute_priority_1,
            // attribute_option_2: val.product_attribute_detail.attribute_priority_2 === null ? '' : val.product_attribute_detail.attribute_priority_2,
            order_type: 'newser',
            quantity: 0,
            company_id: dataRole.role === '"ext_buyer"' ? -1 : dataRole.role === 'sale_order' ? companyID : companyDetail !== undefined ? companyDetail.company.company_id : -1,
            company_position: dataRole.role === '"ext_buyer"' ? -1 : companyDetail !== undefined ? companyDetail.position.role_id : -1,
            com_perm_id: dataRole.role === '"ext_buyer"' ? -1 : companyDetail !== undefined ? companyDetail.position.com_perm_id : -1
          }
        }
        // console.log('data', data)
        await this.$store.dispatch('ActionUpdateCart', data)
        const res = await this.$store.state.ModuleCart.stateUpdateCart
        if (res.message === 'Update cart success' || res.message === 'Delete cart success') {
          if (this.MobileSize) {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>' })
          } else {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ลบสินค้าในรถเข็นเรียบร้อย' })
          }
          // this.Start()
          localStorage.setItem('cartUpdated', Date.now())
          this.getDetailCartLogin()
          // this.$EventBus.$emit('getCartTable')
          // this.$EventBus.$emit('getCartPopOver')
        } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
          if (this.MobileSize) {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ใส่ข้อมูลไม่ครบ</h3>' })
          } else {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ใส่ข้อมูลไม่ครบ' })
          }
        } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
          if (this.MobileSize) {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง</h3>' })
          } else {
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง' })
          }
        }
      }
    },
    setProductName (productname) {
      if (productname.length > 22) {
        productname = productname.slice(0, 22) + '...'
      }
      return productname
    },
    setProductSku (productsku) {
      if (productsku) {
        if (productsku.length > 10) {
          productsku = productsku.slice(0, 10) + '...'
        }
        return productsku
      }
    },
    async goCart () {
      localStorage.removeItem('_sellerShop')
      localStorage.removeItem('_selectProduct')
      localStorage.removeItem('_productToCal')
      this.cancelCoupon()
      this.$EventBus.$emit('closeModalCart')
      if (this.userDetailSelling) {
        this.$router.push({ path: '/Order/ShoppingCart' }).catch(() => {})
      } else {
        this.$router.push({ path: '/shoppingcart' }).catch(() => {})
      }
    },
    goCartRightClick () {
      localStorage.removeItem('_sellerShop')
      localStorage.removeItem('_selectProduct')
      localStorage.removeItem('_productToCal')
      this.cancelCoupon()
      this.$EventBus.$emit('closeModalCart')
    },
    closeModalCartNoLogin () {
      this.visibleNoCart = false
    },
    closeModalCart () {
      this.visibleCart = false
    },
    cancelCoupon () {
      // used point
      if (localStorage.getItem('PointDetail') !== null) {
        var point = JSON.parse(Decode.decode(localStorage.getItem('PointDetail')))
        this.couponDetail = {
          coupon_sort: '',
          coupon_id: '',
          coupon_name: '',
          coupon_discount: '',
          point_sort: 'cancel',
          point: point.point
        }
        this.removeCouponOrPoint()
      }
      // used coupon
      if (localStorage.getItem('CouponDetail') !== null) {
        var coupon = JSON.parse(Decode.decode(localStorage.getItem('CouponDetail')))
        this.couponDetail = {
          coupon_sort: 'cancel',
          coupon_id: coupon.coupon_id,
          coupon_name: -1,
          coupon_discount: '',
          point_sort: '',
          point: ''
        }
        this.removeCouponOrPoint()
      }
    },
    removeCouponOrPoint () {
      this.getDetailCartLogin()
      if (this.checkCancelCoupon === true) {
        localStorage.removeItem('CouponDetail')
        localStorage.removeItem('PointDetail')
      }
    },
    async getDetailCartGracz () {
      var promotionSelected = ''
      if (localStorage.getItem('GraczPromotionSelected') !== null) {
        promotionSelected = JSON.parse(Decode.decode(localStorage.getItem('GraczPromotionSelected')))
        promotionSelected.product_promotion = []
      }
      var ListProduct = []
      this.productList = []
      var shopData = this.userDetailSelling
      var data = {
        seller_shop_id: shopData.seller_shop_id,
        cus_id: shopData.position === 'super_admin' || shopData.position === 'admin' ? -1 : this.customerData.cus_id,
        emp_id: shopData.emp_id,
        product_to_calculate: [],
        coupon: [],
        promotion_discount: promotionSelected
      }
      await this.$store.dispatch('actionsGraczGetDetailCart', data)
      const res = await this.$store.state.ModuleGraczCart.stateGraczGetDetailCart
      // console.log('res =========================', res)
      if (res.message === 'Get detail cart success') {
        this.productList = []
        this.tempProductList = []
        this.checkCancelCoupon = true
        ListProduct = res.data.shop_list
        this.tempProductList = res.data
        // console.log('this.tempProductList //////', res.data)
        for (let index = 0; index < ListProduct.length; index++) {
          const element = ListProduct[index].product_list
          for (let j = 0; j < element.length; j++) {
            this.productList.push(element[j])
          }
          this.countCart = this.productList.length
        }
      } else if (res.message === 'No product in cart.') {
        this.countCart = 0
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        if (this.MobileSize) {
          this.$swal.fire({ toast: true, showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>SERVER ERROR</h3>' })
        } else {
          this.$swal.fire({ toast: true, showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'SERVER ERROR' })
        }
      }
    }
  }
}
</script>
<style lang="css" scoped>
.image {
  max-width: 55px !important;
  width: 55px;
  height: 55px;
  border-radius: 4px;
  /*
  border: 1px solid #EBEBEB;
  box-sizing: border-box;
  */
}

.ant-card-body {
  padding: 24px 24px 0px 24px;
}
.ant-btn-primary {
  background-color: #27AB9C;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  border-color: transparent;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.ant-popover-inner-content {
  padding: 0px !important;
}
.cart-popover .ant-card-body {
  padding: 16px 12px 0px 12px;
}

</style>
