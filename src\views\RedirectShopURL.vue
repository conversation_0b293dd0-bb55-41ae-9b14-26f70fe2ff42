<template>
    <div></div>
</template>

<script>
export default {
  data () {
    return {
      link: '',
      isAndroid: false,
      isiOS: false,
      URL: '',
      URLToStore: '',
      shopID: ''
    }
  },
  async created () {
    // console.log('เข้าจริงๆไหมนะ')
    // เข้ามาเอา link ครั้งแรก
    this.link = window.location.href
    //  Get ID จากร้าน
    var path = this.$router.currentRoute.params.data
    var cleanPath = path.split('-')
    let isAppOpened = false
    let timeout
    // set use Agent
    const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
    this.isAndroid = /Android/i.test(userAgent)
    this.isiOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream
    this.URL = ''
    this.URLToStore = ''
    this.shopID = cleanPath[cleanPath.length - 1]
    // set data ยิงครั้งแรก
    var dataGetlinkMobile = {
      product_id: '',
      seller_shop_id: this.shopID,
      type: 'sellerShop'
    }
    await this.$store.dispatch('actionsGetLinkMobile', dataGetlinkMobile)
    const response = await this.$store.state.ModuleHompage.stateGetLinkMobile
    if (this.isAndroid === true) {
      this.URL = response.data.intentLink
    } else if (this.isiOS === true) {
      this.URL = response.data.short_url
      this.URL = this.URL.replace(/https/gi, 'nexgencommerce')
      this.URLToStore = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
    } else {
      this.URL = this.link.replace(/linkShop/gi, 'api/backend_2/shop')
    }
    const onVisibilityChange = () => {
      // console.log(document.hidden)
      if (document.hidden) {
        isAppOpened = true
        clearTimeout(timeout)
      }
    }

    // console.log(this.URL, this.URLToStore)
    document.addEventListener('visibilitychange', onVisibilityChange)

    const now = Date.now()
    window.location.href = this.URL

    if (this.isiOS === true) {
      timeout = setTimeout(() => {
        const elapsed = Date.now() - now
        if (!isAppOpened && elapsed < 5000) {
          window.location.href = this.URLToStore
          // isRedirected = true
        }
        document.removeEventListener('visibilitychange', onVisibilityChange)
      }, 4000)
    }
    // window.location.replace(URL)
  }
}
</script>

<!-- <template>
  <div></div>
</template>

<script>
export default {
  data () {
    return {
      link: '',
      isAndroid: false,
      isIphone: false,
      isIpad: false,
      isMac: false,
      URL: '',
      URLToStore: ''
    }
  },
  async created () {
    this.link = window.location.href
    const userAgent = navigator.userAgent
    const isTouchDevice = navigator.maxTouchPoints > 1
    // ตรวจสอบประเภทอุปกรณ์
    this.isAndroid = /Android/i.test(userAgent)
    this.isIphone = /iPod|iPhone/i.test(userAgent) || isTouchDevice
    this.isIpad = /iPad|Macintosh/i.test(userAgent) && isTouchDevice
    this.isMac = /Macintosh/i.test(userAgent) && !isTouchDevice
    if (this.isAndroid) {
      this.URL = this.link.replace(/link\/shop/gi, 'api/backend_2/shop_mobile')
      this.URLToStore = 'https://play.google.com/store/apps/details?id=com.inet.nexgenshop'
    } else if (this.isIphone || this.isIpad) {
      this.URL = this.link.replace(/https/gi, 'nexgencommerce').replace(/link\/shop/gi, 'api/backend_2/shop_mobile')
      this.URLToStore = 'https://apps.apple.com/us/app/nexgen/id6651839687'
    } else if (this.isMac) {
      this.URL = this.link.replace(/link\/shop/gi, 'api/backend_2/shop_mobile')
    } else {
      this.URL = this.link.replace(/link\/shop/gi, 'api/backend_2/shop_mobile')
    }

    const now = Date.now()
    setTimeout(() => {
      const elapsed = Date.now() - now
      window.location.href = elapsed < 2000 && this.URLToStore ? this.URLToStore : this.URL
    }, 1500)
  }
}
</script> -->
