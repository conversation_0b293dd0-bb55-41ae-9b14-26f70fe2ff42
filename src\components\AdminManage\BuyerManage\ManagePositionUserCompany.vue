<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title
        v-if="!MobileSize"
        style="
          font-weight: bold;
          font-size: 24px;
          line-height: 32px;
          color: #333333;
        "
        >จัดการผู้ใช้งานภายในบริษัท</v-card-title
      >
      <v-card-title
        v-else
        class="px-0"
        style="
          font-weight: bold;
          font-size: 18px;
          line-height: 32px;
          color: #333333;
        "
        ><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()"
          >mdi-chevron-left</v-icon
        >
        จัดการผู้ใช้งานภายในบริษัท</v-card-title
      >
      <v-row no-gutters>
        <v-col cols="12" class="px-2 py-0">
          <a-tabs @change="selectOrder">
            <a-tab-pane :key="0"
              ><span slot="tab"
                >ทั้งหมด
                <a-tag color="#27AB9C" style="border-radius: 8px">{{
                  countPOAll
                }}</a-tag></span
              ></a-tab-pane
            >
            <a-tab-pane :key="1"
              ><span slot="tab"
                >ใช้งาน
                <a-tag color="#1AB759" style="border-radius: 8px">{{
                  countPOSuccess
                }}</a-tag></span
              ></a-tab-pane
            >
            <a-tab-pane :key="2"
              ><span slot="tab"
                >ยกเลิก
                <a-tag color="#F5222D" style="border-radius: 8px">{{
                  countPOReject
                }}</a-tag></span
              ></a-tab-pane
            >
          </a-tabs>
        </v-col>
        <v-col
          cols="12"
          md="6"
          sm="12"
          :class="!MobileSize ? 'pl-2 pt-0' : 'pl-2 pr-2 mb-3'"
        >
          <v-text-field
            v-model="search"
            dense
            hide-details
            v-if="disableTable === true"
            outlined
            placeholder="ค้นหาจากชื่อ-นามสกุลหรืออีเมลผู้ใช้งาน"
          >
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col
          cols="12"
          md="6"
          sm="2"
          align="right"
          :class="MobileSize ? 'px-2' : 'pr-4'"
          v-if="StateStatus !== 2 && !IpadSize"
        >
          <v-btn
            v-if="MobileSize || IpadSize"
            @click="openDialog()"
            :block="MobileSize"
            color="#27AB9C"
            dense
            :class="MobileSize ? 'white--text' : 'px-2 white--text'"
          >
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
          <v-btn
            v-else
            @click="openDialog()"
            color="#27AB9C"
            dense
            class="pl-4 pr-4 pr white--text"
          >
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
        </v-col>
        <v-col
          cols="12"
          sm="9"
          v-if="disableTable === true"
          :class="
            MobileSize
              ? 'pl-2 pr-2 mb-3 mt-3'
              : IpadSize
              ? 'pl-2 pr-2 pt-2 mb-3 mt-3'
              : 'pl-3 pr-3 mb-3 mt-3'
          "
        >
          <span
            style="
              font-size: 16px;
              line-height: 24px;
              align-items: center;
              color: #333333;
              font-weight: 600;
            "
            v-if="StateStatus === 0"
            >รายการผู้ใช้งานในบริษัททั้งหมด {{ showCountOrder }} รายการ</span
          >
          <span
            style="
              font-size: 16px;
              line-height: 24px;
              align-items: center;
              color: #333333;
              font-weight: 600;
            "
            v-else-if="StateStatus === 1"
            >รายการผู้ใช้งานในบริษัท {{ showCountOrder }} รายการ</span
          >
          <span
            style="
              font-size: 16px;
              line-height: 24px;
              align-items: center;
              color: #333333;
              font-weight: 600;
            "
            v-else-if="StateStatus === 2"
            >รายการยกเลิกผู้ใช้งานในบริษัท {{ showCountOrder }} รายการ</span
          >
        </v-col>
        <v-col
          cols="12"
          md="6"
          sm="2"
          align="right"
          :class="MobileSize ? 'px-2' : 'pr-4 pt-4 pb-4'"
          v-if="StateStatus !== 2 && IpadSize"
        >
          <v-btn
            v-if="MobileSize || IpadSize"
            @click="openDialog()"
            :block="MobileSize"
            color="#27AB9C"
            dense
            :class="MobileSize ? 'white--text' : 'px-2 white--text'"
          >
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
          <v-btn
            v-else
            @click="openDialog()"
            color="#27AB9C"
            dense
            class="pl-4 pr-4 pr white--text"
          >
            <v-icon>mdi-plus</v-icon>เพิ่มข้อมูล
          </v-btn>
        </v-col>
        <v-col cols="12">
          <v-card
            v-if="disableTable === true"
            outlined
            class="small-card mx-2 my-2"
            min-height="436"
          >
            <v-data-table
              :headers="
                keyCheckHead == 0
                  ? headers
                  : keyCheckHead == 1
                  ? headersSuccess
                  : keyCheckHead == 2
                  ? headersFail
                  : keyCheckHead == 3
                  ? headersWaitingApprove
                  : keyCheckHead == 4
                  ? headersEdited
                  : headersFail
              "
              :items="DataTable"
              :search="search"
              :page.sync="page"
              style="width: 100%"
              height="100%"
              @pagination="countOrdar"
              :items-per-page="10"
              no-results-text="ไม่มีข้อมูลที่ค้นหาในตาราง"
              class=""
              :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
            >
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'success'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F0F9EE"
                    text-color="#1A75B9"
                    >สำเร็จ</v-chip
                  >
                </span>
                <span v-else-if="item.status === 'active'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F0F9EE"
                    text-color="#1AB759"
                    >ใช้งานได้</v-chip
                  >
                </span>
                <span v-else-if="item.status === 'waiting_approve'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#FAAD14"
                    >รออนุมัติ</v-chip
                  >
                </span>
                <span v-else-if="item.status === 'edited'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#FAAD14"
                    >ถูกแก้ไข</v-chip
                  >
                </span>
                <span v-else-if="item.status === 'reject'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F7D9D9"
                    text-color="#F5222D"
                    >ยกเลิก</v-chip
                  >
                </span>
                <span v-else>
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F7D9D9"
                    text-color="#F5222D"
                    >ยกเลิก</v-chip
                  >
                </span>
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{
                  new Date(item.created_at).toLocaleDateString('th-TH', {
                    year: 'numeric',
                    month: 'numeric',
                    day: 'numeric'
                  })
                }}
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <!-- <v-btn
                  @click="DetailDialog(item.user_id)"
                  style="
                    border: 1px solid #f2f2f2;
                    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                    border-radius: 4px;
                  "
                  outlined
                  small
                >
                  <v-icon color="#27AB9C" class=""
                    >mdi-file-document-outline</v-icon
                  >
                </v-btn> -->
                <v-btn
                  @click="DetailDialog(item.user_id)"
                  text
                  rounded
                  color="#27AB9C"
                  small
                >
                  <b>รายละเอียด</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img
              src="@/assets/emptypo.png"
              max-height="500px"
              max-width="500px"
              height="100%"
              width="100%"
              contain
              aspect-ratio="2"
            ></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c">
            <b v-if="StateStatus === 0">คุณยังไม่มีรายการ</b>
            <b v-else-if="StateStatus === 1"
              >คุณยังไม่มีรายการที่ผู้ใช้งานในบริษัท</b
            >
            <b v-else-if="StateStatus === 2"
              >คุณยังไม่มีรายการที่ยกเลิกผู้ใช้งานในบริษัท</b
            >
          </h2>
        </v-col>
      </v-row>
    </v-card>

    <!-- dialog Add User -->
    <v-dialog
      v-model="dialog_user"
      width="569px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">การกำหนดสิทธิ์การใช้งาน</font>
          </span>
          <v-btn icon dark @click="dialog_user = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar rounded size="72">
                <!-- <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png"></v-img> -->
                <v-img contain :src="require('@/assets/icons/use.jpg')"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p
                    class="mt-5"
                    style="
                      font-weight: 400;
                      font-size: 20px;
                      text-transform: #333333;
                    "
                    v-if="!MobileSize"
                  >
                    กำหนดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p
                    class="mt-5 pl-2"
                    style="
                      font-weight: 400;
                      font-size: 19px;
                      text-transform: #333333;
                    "
                    v-else
                  >
                    กำหนดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <p
                class="mt-5"
                style="
                  font-weight: 600;
                  font-size: 15px;
                  text-transform: #333333;
                  margin-bottom: 5px;
                "
              >
                ค้นหาผู้ใช้งาน
              </p>
              <v-text-field
                :rules="Rules.emailRules"
                @keyup.enter="searchUser"
                placeholder="ระบุอีเมลของพนักงาน"
                v-model="inputEmailUser"
                append-icon="mdi-magnify"
                dense
                outlined
                ref="Emails"
              ></v-text-field>
            </v-col>
            <v-col
              v-if="
                select_user.length === 0 &&
                searchCount === 1 &&
                already_have_user === false
              "
              cols="12"
              md="12"
              align="center"
            >
              <v-img
                class="mb-10"
                width="100"
                height="100"
                contain
                :src="require('@/assets/icons/NoFile.png')"
              ></v-img>
              <p style="font-weight: 700; font-size: 18px; text-transform: #333333;">ไม่พบข้อมูล</p>
              <p style="font-weight: 500; font-size: 16px; text-transform: #333333;">กรุณาเพิ่มข้อมูลใน <a href=""><U color="#1B5DD6"><font color="#1B5DD6">One ID</font></U></a> หรือ <font color="#27AB9C"> แชทกับพาณิชย์ </font></p>
            </v-col>
            <v-col
              v-if="
                select_user.length === 0 &&
                searchCount === 1 &&
                already_have_user === true
              "
              cols="12"
              md="12"
              align="center"
            >
              <v-img
                class="mb-10"
                width="100"
                height="100"
                contain
                :src="require('@/assets/icons/NoFile.png')"
              ></v-img>
              <p
                style="
                  font-weight: 700;
                  font-size: 18px;
                  text-transform: #333333;
                "
              >
                {{ messageForShow }}
              </p>
              <p
                style="
                  font-weight: 500;
                  font-size: 16px;
                  text-transform: #333333;
                "
                v-if="messageForShow === 'ผู้ใช้งานมีข้อมูลในระบบแล้ว'"
              >
                กรุณาแก้ข้อมูลภายในระบบ
              </p>
            </v-col>
            <v-col
              v-if="select_user.length !== 0 && searchCount === 1"
              style="margin-top: -20px"
              cols="12"
              md="12"
            >
              <!-- card Detail in Add -->
              <v-card
                v-if="optionDetail"
                @click="EditOption()"
                class="rounded-lg"
              >
                <v-container>
                  <v-row no-gutters>
                    <v-col cols="2" md="3" sm="3">
                      <v-card class="rounded-lg">
                        <v-img
                          width="172px"
                          height="152.26px"
                          :src="imgUser"
                          v-if="!MobileSize"
                        ></v-img>
                        <v-img
                          width="100%"
                          height="100%"
                          contain
                          :src="imgUser"
                          v-else
                        ></v-img>
                      </v-card>
                    </v-col>
                    <v-col v-if="this.type === 'readonly'" cols="12" md="8">
                      <v-row no-gutters class="mt-3 ml-4">
                        <v-col cols="4" md="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            ชื่อ-สกุล :
                          </p>
                        </v-col>
                        <v-col cols="8" md="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ nameUser }}
                          </p>
                        </v-col>
                        <v-col cols="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            อีเมล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ emailUser }}
                          </p>
                        </v-col>
                        <v-col cols="6" md="4" sm="4">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            เบอร์โทรศัพท์ :
                          </p>
                        </v-col>
                        <v-col cols="6" md="8" sm="8">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 600; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ phoneUser }}
                          </p>
                        </v-col>
                        <v-col
                          cols="12"
                          v-for="(item, index) in Position_Company_User"
                          :key="index"
                        >
                          <v-row>
                            <v-col cols="3">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                ตำแหน่ง :
                              </p>
                            </v-col>
                            <v-col cols="9">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                    : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                                "
                              >
                                {{ item.PositionName }}
                              </p>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
              <!-- card  Add -->
              <v-card v-else class="rounded-lg">
                <v-container>
                  <v-row no-gutters>
                    <v-col cols="12" md="3">
                      <v-card class="rounded-lg">
                        <v-img
                          width="172px"
                          height="152.26px"
                          :src="imgUser"
                        ></v-img>
                      </v-card>
                    </v-col>
                    <v-col v-if="this.type === 'readonly'" cols="12" md="8">
                      <v-row no-gutters class="mt-3 ml-4">
                        <v-col cols="4" md="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            ชื่อ-สกุล :
                          </p>
                        </v-col>
                        <v-col cols="8" md="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ nameUser }}
                          </p>
                        </v-col>
                        <v-col cols="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            อีเมล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ emailUser }}
                          </p>
                        </v-col>
                        <v-col cols="6" md="4" sm="4">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            เบอร์โทรศัพท์ :
                          </p>
                        </v-col>
                        <v-col cols="6" md="8" sm="8">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ phoneUser }}
                          </p>
                        </v-col>
                        <v-row no-gutters v-if="this.buildUser.length !== 0">
                          <v-col
                            cols="12"
                            v-for="(item, index) in buildUser"
                            :key="index"
                          >
                            <v-row>
                              <v-col cols="3">
                                <p
                                  :style="
                                    MobileSize
                                      ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                      : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                  "
                                >
                                  ตำแหน่ง :
                                </p>
                              </v-col>
                              <v-col cols="7" md="6" sm="6">
                                <p
                                  :style="
                                    MobileSize
                                      ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                      : 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                  "
                                >
                                  {{ item.PositionName }}
                                </p>
                              </v-col>
                              <v-col cols="2">
                                <v-row>
                                  <v-col cols="2">
                                    <v-btn
                                      @click="DelectPositionUserBuild(item)"
                                      icon
                                      style="margin-top: -10px"
                                    >
                                      <v-avatar rounded size="18">
                                        <v-img
                                          contain
                                          :src="
                                            require('@/assets/icons/trash.png')
                                          "
                                        >
                                        </v-img>
                                      </v-avatar>
                                    </v-btn>
                                  </v-col>
                                  <v-col cols="2">
                                    <v-btn
                                      v-if="
                                        index === buildUser.length - 1 && !add
                                      "
                                      @click="AddPositionUserBuild()"
                                      icon
                                      style="margin-top: -9px"
                                    >
                                      <v-icon size="19"
                                        >mdi-plus-circle-outline</v-icon
                                      >
                                    </v-btn>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                        <v-col v-else cols="12">
                          <v-row no-gutters>
                            <v-col cols="12">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                ตำแหน่ง :
                              </p>
                            </v-col>
                            <v-col cols="12">
                              <v-row no-gutters>
                                <v-col cols="10" md="8" sm="8">
                                  <!-- <v-text-field @keyup.enter="AddUserPosotion" v-model="NamePositionAdd"
                                    min-height="2px" outlined dense>
                                  </v-text-field> -->
                                  <v-autocomplete
                                    @change="AddUserPosotion"
                                    v-model="NamePositionAdd"
                                    :items="testpositionname"
                                    placeholder="พิมพ์ชื่อตำแหน่ง"
                                    outlined
                                    dense
                                    :menu-props="{ maxWidth: '350' }"
                                  >
                                  </v-autocomplete>
                                </v-col>
                                <v-col cols="2">
                                  <v-btn icon>
                                    <v-icon size="19"
                                      >mdi-plus-circle-outline</v-icon
                                    >
                                  </v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col v-if="addBuild" cols="12">
                          <v-row no-gutters>
                            <v-col cols="12">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                ตำแหน่ง :
                              </p>
                            </v-col>
                            <v-col cols="12">
                              <v-row no-gutters>
                                <v-col cols="10" md="8" sm="8">
                                  <!-- <v-text-field @keyup.enter="AddUserPosotion" v-model="NamePositionAdd"
                                    min-height="2px" outlined dense>
                                  </v-text-field> -->
                                  <v-autocomplete
                                    @change="AddUserPosotion"
                                    v-model="NamePositionAdd"
                                    :items="testpositionname"
                                    placeholder="พิมพ์ชื่อตำแหน่ง"
                                    outlined
                                    dense
                                    :menu-props="{ maxWidth: '350' }"
                                  >
                                  </v-autocomplete>
                                </v-col>
                                <v-col cols="2">
                                  <v-btn icon>
                                    <v-icon size="19"
                                      >mdi-plus-circle-outline</v-icon
                                    >
                                  </v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
              <v-container
                v-if="!optionDetail"
                style="display: flex; justify-content: flex-end"
              >
                <v-btn
                  dense
                  dark
                  outlined
                  color="#27AB9C"
                  class="pl-7 pr-7 mt-2"
                  @click="closeAddUser()"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  dense
                  color="#27AB9C"
                  class="ml-4 mt-2 pl-8 pr-8 white--text"
                  @click="CreateUserPosition()"
                >
                  บันทึก
                </v-btn>
              </v-container>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- dialog  detail -->
    <v-dialog
      v-model="dialog_detail"
      width="579px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">การกำหนดสิทธิ์การใช้งาน</font>
          </span>
          <v-btn icon dark @click="CloseDialog('readonly')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row class="mt-5">
            <v-col cols="3" md="2" sm="2" class="mr-0">
              <v-avatar rounded size="72">
                <!-- <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png"></v-img> -->
                <v-img contain :src="require('@/assets/icons/use.jpg')"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="9" md="10" sm="10">
              <v-row dense no-gutters justify="start">
                <v-col cols="12" md="12" sm="12" xs="12">
                  <p
                    class="mt-5"
                    style="
                      font-weight: 400;
                      font-size: 20px;
                      text-transform: #333333;
                    "
                    v-if="!MobileSize"
                  >
                    กำหนดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                  <p
                    class="mt-3 pl-2"
                    style="
                      font-weight: 400;
                      font-size: 16px;
                      text-transform: #333333;
                    "
                    v-else
                  >
                    กำหนดตำแหน่งและสิทธิ์การใช้งาน
                  </p>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-card
                class="rounded-lg"
                elevation="0"
                style="border: 1px solid #e6e6e6"
              >
                <v-card-text>
                  <v-row no-gutters>
                    <v-col cols="2" md="3" sm="3">
                      <v-card class="rounded-lg" elevation="0">
                        <v-img
                          width="172px"
                          height="152.26px"
                          :src="imgUser"
                          v-if="!MobileSize"
                        ></v-img>
                        <v-img
                          width="100%"
                          height="100%"
                          contain
                          :src="imgUser"
                          v-else
                        ></v-img>
                      </v-card>
                    </v-col>
                    <v-col
                      v-if="this.type === 'readonly'"
                      cols="10"
                      md="8"
                      sm="8"
                    >
                      <v-row no-gutters class="mt-3 ml-4">
                        <v-col cols="4" md="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            ชื่อ-สกุล :
                          </p>
                        </v-col>
                        <v-col cols="8" md="9">
                          <p
                            v-if="!MobileSize"
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ nameUser }}
                          </p>
                          <v-row dense no-gutters v-else>
                            <v-col cols="10">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                    : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                                "
                              >
                                {{ nameUser }}
                              </p>
                            </v-col>
                            <v-col
                              v-if="this.type === 'readonly' && MobileSize"
                              cols="2"
                            >
                              <v-btn @click="EditType('edit')" icon dense small>
                                <v-avatar rounded size="18">
                                  <v-img
                                    contain
                                    :src="require('@/assets/icons/Union.png')"
                                  ></v-img>
                                </v-avatar>
                              </v-btn>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col cols="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            อีเมล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ emailUser }}
                          </p>
                        </v-col>
                        <v-col cols="6" md="4" sm="4">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            เบอร์โทรศัพท์ :
                          </p>
                        </v-col>
                        <v-col cols="6" md="8" sm="8">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 600; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ phoneUser }}
                          </p>
                        </v-col>
                        <v-col
                          cols="12"
                          v-for="(item, index) in Position_Company_User"
                          :key="index"
                        >
                          <v-row dense>
                            <v-col
                              cols="4"
                              md="3"
                              sm="3"
                              :class="MobileSize ? 'pr-0' : ''"
                            >
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                ตำแหน่ง :
                              </p>
                            </v-col>
                            <v-col cols="8" md="9" sm="9">
                              <p
                                style="
                                  font-weight: 600;
                                  font-size: 14px;
                                  line-height: 16px;
                                  color: #333333;
                                "
                                v-if="!MobileSize"
                              >
                                {{ item.PositionName }}
                              </p>
                              <p
                                style="
                                  font-weight: 600;
                                  font-size: 14px;
                                  line-height: 16px;
                                  color: #333333;
                                "
                                v-else
                              >
                                {{ item.PositionName }}
                              </p>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col v-else cols="10" md="9" sm="9">
                      <v-row no-gutters class="mt-3 ml-4">
                        <v-col cols="4" md="3" sm="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            ชื่อ-สกุล :
                          </p>
                        </v-col>
                        <v-col cols="8" md="9" sm="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ nameUser }}
                          </p>
                        </v-col>
                        <v-col cols="3">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            อีเมล :
                          </p>
                        </v-col>
                        <v-col cols="9">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ emailUser }}
                          </p>
                        </v-col>
                        <v-col cols="6" md="4" sm="4">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                            "
                          >
                            เบอร์โทรศัพท์ :
                          </p>
                        </v-col>
                        <v-col cols="6" md="8" sm="8">
                          <p
                            :style="
                              MobileSize
                                ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                : 'font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;'
                            "
                          >
                            {{ phoneUser }}
                          </p>
                        </v-col>
                        <v-row
                          no-gutters
                          v-if="this.Position_Company_User.length !== 0"
                        >
                          <v-col
                            cols="12"
                            v-for="(item, index) in Position_Company_User"
                            :key="index"
                          >
                            <v-row no-gutters>
                              <v-col cols="4" md="3" sm="3">
                                <p
                                  :style="
                                    MobileSize
                                      ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                      : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                  "
                                >
                                  ตำแหน่ง :
                                </p>
                              </v-col>
                              <v-col cols="8" md="9" sm="9">
                                <v-row no>
                                  <v-col cols="8">
                                    <p
                                      :style="
                                        MobileSize
                                          ? 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                          : 'font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;'
                                      "
                                    >
                                      {{ item.PositionName }}
                                    </p>
                                  </v-col>
                                  <v-col cols="2">
                                    <v-row>
                                      <v-col cols="2">
                                        <v-btn
                                          v-if="
                                            item.PositionName !==
                                            'เจ้าของนิติบุคคล'
                                          "
                                          @click="DelectPositionUser(item)"
                                          icon
                                          style="margin-top: -10px"
                                        >
                                          <v-avatar rounded size="18">
                                            <v-img
                                              contain
                                              :src="
                                                require('@/assets/icons/trash.png')
                                              "
                                            >
                                            </v-img>
                                          </v-avatar>
                                        </v-btn>
                                      </v-col>
                                      <v-col cols="2">
                                        <v-btn
                                          v-if="
                                            index ===
                                              Position_Company_User.length -
                                                1 && !add
                                          "
                                          @click="AddPositionUser()"
                                          icon
                                          style="margin-top: -9px"
                                        >
                                          <!-- <v-avatar rounded size="18"> -->
                                          <v-icon size="19"
                                            >mdi-plus-circle-outline</v-icon
                                          >
                                          <!-- </v-avatar> -->
                                        </v-btn>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                        <v-col v-else cols="12">
                          <v-row no-gutters>
                            <v-col cols="12">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                ตำแหน่ง :
                              </p>
                            </v-col>
                            <v-col cols="12">
                              <v-row no-gutters>
                                <v-col cols="10" md="8" sm="8">
                                  <!-- <v-text-field @keyup.enter="SaveAddPosition" v-model="NamePositionAdd"
                                    min-height="2px" outlined dense>
                                  </v-text-field> -->
                                  <v-autocomplete
                                    @change="SaveAddPosition"
                                    v-model="NamePositionAdd"
                                    :items="testpositionname"
                                    placeholder="พิมพ์ชื่อตำแหน่ง"
                                    outlined
                                    dense
                                    :menu-props="{ maxWidth: '350' }"
                                  >
                                  </v-autocomplete>
                                </v-col>
                                <v-col cols="2">
                                  <v-btn icon>
                                    <v-icon size="19"
                                      >mdi-plus-circle-outline</v-icon
                                    >
                                  </v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                        <v-col v-if="add" cols="12">
                          <v-row no-gutters>
                            <v-col cols="12">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                ตำแหน่ง :
                              </p>
                            </v-col>
                            <v-col cols="12">
                              <v-row no-gutters>
                                <v-col cols="10" md="8" sm="8">
                                  <!-- <v-text-field @keyup.enter="SaveAddPosition" v-model="NamePositionAdd"
                                    min-height="2px" outlined dense>
                                  </v-text-field> -->
                                  <v-autocomplete
                                    @change="SaveAddPosition"
                                    v-model="NamePositionAdd"
                                    :items="testpositionname"
                                    placeholder="พิมพ์ชื่อตำแหน่ง"
                                    outlined
                                    dense
                                    :menu-props="{ maxWidth: '350' }"
                                  >
                                  </v-autocomplete>
                                </v-col>
                                <v-col cols="2">
                                  <v-btn icon>
                                    <v-icon size="19"
                                      >mdi-plus-circle-outline</v-icon
                                    >
                                  </v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                            <v-col v-if="limitDateChank" cols="12">
                              <p
                                :style="
                                  MobileSize
                                    ? 'font-weight: 400; font-size: 14px; line-height: 16px;'
                                    : 'font-weight: 400; font-size: 14px; line-height: 8px;'
                                "
                              >
                                จำนวนวันรอการอนุมัติ :
                              </p>
                            </v-col>
                            <v-col v-if="limitDateChank" cols="12">
                              <v-row no-gutters>
                                <v-col cols="8">
                                  <!-- <v-text-field @keyup.enter="SaveAddPosition" v-model="limitDate" min-height="2px"
                                    outlined dense>
                                  </v-text-field> -->
                                  <v-autocomplete
                                    @change="SaveAddPosition"
                                    clearable
                                    v-model="limitDate"
                                    :items="testpositionname"
                                    placeholder="พิมพ์ชื่อตำแหน่ง"
                                    outlined
                                    dense
                                    append-icon
                                  >
                                  </v-autocomplete>
                                </v-col>
                                <v-col cols="2">
                                  <v-btn icon>
                                    <v-icon size="19"
                                      >mdi-plus-circle-outline</v-icon
                                    >
                                  </v-btn>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col
                      v-if="this.type === 'readonly' && !MobileSize"
                      cols="1"
                      md="1"
                      sm="1"
                    >
                      <div>
                        <v-btn @click="EditType('edit')" icon dense>
                          <v-avatar rounded size="18">
                            <v-img
                              contain
                              :src="require('@/assets/icons/Union.png')"
                            ></v-img>
                          </v-avatar>
                        </v-btn>
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col
              v-if="this.type === 'readonly'"
              cols="12"
              md="12"
              sm="12"
              xs="12"
              align="right"
              class="pr-8"
            >
              <div
                disable
                class="mt-4 mb-6"
                style="border-bottom: #e6e6e6 4px dashed"
              ></div>
              <v-row>
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span
                    v-if="createDate !== ''"
                    style="
                      font-weight: 500;
                      font-size: 12px;
                      line-height: 1px;
                      color: #333333;
                    "
                    >สร้างเมื่อ :
                    {{
                      new Date(createDate).toLocaleDateString('th-TH', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    }}</span
                  >
                  <span
                    v-if="createDate === ''"
                    style="
                      font-weight: 500;
                      font-size: 12px;
                      line-height: 1px;
                      color: #333333;
                    "
                    >สร้างเมื่อ : -
                  </span>
                </v-col>
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span
                    v-if="update !== ''"
                    style="
                      font-weight: 500;
                      font-size: 12px;
                      line-height: 1px;
                      color: #333333;
                    "
                    >แก้ไขเมื่อ :
                    {{
                      new Date(update).toLocaleDateString('th-TH', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    }}</span
                  >
                  <span
                    v-if="update === ''"
                    style="
                      font-weight: 500;
                      font-size: 12px;
                      line-height: 1px;
                      color: #333333;
                    "
                    >แก้ไขเมื่อ : -
                  </span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions v-if="this.type !== 'readonly'">
          <v-container style="display: flex; justify-content: flex-end">
            <v-btn
              dense
              dark
              outlined
              color="#27AB9C"
              class="pl-7 pr-7 mt-2"
              @click="EditType('readonly')"
            >
              ยกเลิก
            </v-btn>
            <v-btn
              dense
              color="#27AB9C"
              class="ml-4 mt-2 pl-8 pr-8 white--text"
              @click="saveEdit()"
            >
              บันทึก
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      quantity: 0,
      countPOAll: 0,
      countPOSuccess: 0,
      countPOActive: 0,
      countPOWaitingApprove: 0,
      countPOEdited: 0,
      countPOReject: 0,
      searchCount: 0,
      inputEmailUser: '',
      search: '',
      imgUser: '',
      nameUser: '',
      emailUser: '',
      phoneUser: '',
      dialog_detail: false,
      createDate: '',
      update: '',
      copy: [],
      type: 'readonly',
      add: false,
      addBuild: false,
      copyData: [],
      openAdd: true,
      NamePositionAdd: '',
      optionDetail: true,
      buildUser: [],
      name: 'รายละเอียดตำแหน่งและสิทธิ์การใช้งาน',
      dialog_user: false,
      orderList: [],
      Position_Company_User: [],
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: false,
      companyData: [],
      seller_shop_id: null,
      dataRole: '',
      page: 1,
      keyCheckHead: 0,
      dialog_rank: false,
      AllPosotion: [],
      list_data: [],
      PositionDetailAll: [],
      user_id: '',
      limitDate: '',
      limitDateChank: false,
      select_user: [],
      testpositionname: [],
      already_have_user: false,
      Rules: {
        position_name: [v => !!v || 'กรุณากรอกตำแหน่ง'],
        last_name: [v => !!v || 'กรุณากรอกนามสกุลผู้รับ'],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่',
          v =>
            /^[-0-9/]+$/.test(v) || v.length === 0 || 'กรุณาระบุตัวเลขเท่านั้น',
          v =>
            /^[0-9/]+$/.test(v) ||
            v.length === 0 ||
            (/^[-]+$/.test(v) && v.length === 1) ||
            'ระบุข้อมูลไม่ถูกต้อง'
        ],
        moo_no: [
          v =>
            /^[-0-9]+$/.test(v) || v.length === 0 || 'กรุณาระบุตัวเลขเท่านั้น',
          v =>
            /^[0-9]+$/.test(v) ||
            v.length === 0 ||
            (/^[-]+$/.test(v) && v.length === 1) ||
            'ระบุข้อมูลไม่ถูกต้อง'
        ],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v =>
            /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ]
      },
      headers: [
        {
          text: 'ชื่อ-นามสกุล',
          value: 'fullname_th',
          sortable: false,
          align: 'start',
          width: '200',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'อีเมล',
          value: 'email',
          sortable: false,
          width: '175',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะ',
          value: 'status',
          filterable: false,
          sortable: false,
          width: '170',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'action',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '200',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      headersSuccess: [
        {
          text: 'ชื่อ-นามสกุล',
          value: 'fullname_th',
          sortable: false,
          align: 'start',
          width: '200',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'อีเมล',
          value: 'email',
          sortable: false,
          width: '175',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะ',
          value: 'status',
          filterable: false,
          sortable: false,
          align: 'center',
          width: '170',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'action',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '200',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      headersFail: [
        {
          text: 'ชื่อ-นามสกุล',
          value: 'fullname_th',
          sortable: false,
          align: 'start',
          width: '200',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'อีเมล',
          value: 'email',
          sortable: false,
          width: '175',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะ',
          value: 'status',
          filterable: false,
          sortable: false,
          align: 'center',
          width: '170',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'action',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '200',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      business: '',
      messageForShow: ''
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router
          .push({ path: '/ManageCompanyPostionUserMobile' })
          .catch(() => {})
      } else {
        this.$router.push({ path: '/ManageCompanyPostionUser' }).catch(() => {})
      }
    },
    overlay (val) {
      val &&
        setTimeout(() => {
          this.overlay = false
        }, 500)
    },
    StateStatus (val) {
      // this.Position_Company_User = this.copyData
      if (val === 0) {
        this.DataTable = this.orderList.data.all
        // this.showCountOrder = this.orderList.data.all.length
        this.countOrdar()
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.data.active
        // this.showCountOrder = this.orderList.data.active.length
        this.countOrdar()
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.data.inactive
        // this.showCountOrder = this.orderList.data.inactive.length
        this.countOrdar()
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    window.scrollTo(0, 0)
    // console.log('StateStatus', this.StateStatus)
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(
        Decode.decode(localStorage.getItem('CompanyData'))
      )
      // this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
      this.business = localStorage.getItem('business_code')
      this.getListData()
      this.listPosition()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    CloseDialog (val) {
      this.type = val
      this.dialog_detail = false
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    closeAddUser () {
      this.dialog_user = false
      this.searchCount = 0
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    DelectPositionUserBuild (item) {
      for (let i = 0; i < this.buildUser.length; i++) {
        if (
          item.PositionName.toString() ===
          this.buildUser[i].PositionName.toString()
        ) {
          this.buildUser.splice(i, 1)
        }
      }
    },
    async listPosition () {
      //  เป็นส่วนของการลิสส์ ข้อมูลที่เป็นตำแหน่งของ บริษัทนั้นๆ
      this.testpositionname = []
      var datas = {
        company_id: this.companyData.id
      }
      await this.$store.dispatch('actionsListUserCompanyPosition', datas)
      var orderList = await this.$store.state.ModuleShop.stateListUserCompany
      if (orderList.result === 'SUCCESS') {
        this.AllPosotion = orderList.data.active
        for (let i = 0; i < this.AllPosotion.length; i++) {
          this.testpositionname.push(this.AllPosotion[i].name.toString())
        }
      } else {
        if (orderList.message === 'This user is Unauthorized' || orderList.message === 'This user is unauthorized.' || orderList.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: orderList.message
          })
        }
      }
    },
    async searchUser () {
      this.select_user = []
      var dataUser = {
        type: 'company',
        id_type: this.companyData.id,
        business_id: this.business,
        email: this.inputEmailUser
      }
      await this.$store.dispatch('actionsSearchUsercompany', dataUser)
      var datauser = await this.$store.state.ModuleShop.stateSearchUserCompany
      if (datauser.code === 200) {
        this.select_user = datauser.data
        this.user_id = datauser.data.user_id
        this.imgUser = datauser.data.img_path
        this.nameUser = datauser.data.fullname_th
        this.emailUser = datauser.data.email
        this.phoneUser = datauser.data.phone
      } else {
        if (datauser.message === 'This user is Unauthorized' || datauser.message === 'This user is unauthorized.' || datauser.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          if (datauser.message === 'User is already exist in this company.') {
            this.already_have_user = true
            this.select_user = []
            this.messageForShow = 'ผู้ใช้งานมีข้อมูลในระบบแล้ว'
          } else if (datauser.message === 'User not found in your business. Please contact PANIT admin.') {
            this.already_have_user = true
            this.select_user = []
            this.messageForShow = 'ไม่พบผู้ใช้งานนี้ในบริษัท โปรดติดต่อแอดมิน'
          } else if (datauser.message === 'Not Found Business Account Please contact PANIT admin.') {
            this.already_have_user = true
            this.select_user = []
            this.messageForShow = 'ไม่พบผู้ใช้งานนี้ในบริษัท โปรดติดต่อแอดมิน'
          } else {
            this.already_have_user = false
            this.select_user = []
          }
        }
      }
      this.searchCount = 1
    },
    EditOption () {
      if (this.optionDetail === true) {
        this.optionDetail = false
      } else {
        this.optionDetail = true
      }
    },
    DelectPositionUser (item) {
      this.add = false
      this.NamePositionAdd = ''
      for (let i = 0; i < this.Position_Company_User.length; i++) {
        if (
          item.PositionName.toString() ===
          this.Position_Company_User[i].PositionName.toString()
        ) {
          this.copy.push(this.Position_Company_User[i])
          this.Position_Company_User.splice(i, 1)
        }
      }
    },
    openDialog () {
      this.dialog_user = true
      this.searchCount = 0
      this.select_user = []
      this.inputEmailUser = ''
      this.getDetailUser()
      this.$refs.Emails.resetValidation()
    },
    AddPositionUser () {
      this.add = true
      this.openAdd = false
    },
    AddPositionUserBuild () {
      this.addBuild = true
    },
    AddUserPosotion () {
      // เป็นส่วนของการ เพิ่มตำแหน่งตอนสร้าง
      this.addBuild = false
      var a = true
      if (this.NamePositionAdd !== '') {
        for (let i = 0; i < this.AllPosotion.length; i++) {
          if (this.NamePositionAdd.toString() === this.AllPosotion[i].name.toString()) {
            this.buildUser.push({
              PositionName: this.NamePositionAdd,
              com_user_perm_id: '',
              limit_date: ''
            })
            a = false
          }
        }
        if (a) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ไม่พบตำแหน่งที่ได้ทำการ<br/> โปรดตรวจสอบอีกครั้ง</h3>'
          })
        }
      } else {
        this.NamePositionAdd = ''
      }
      this.NamePositionAdd = ''
      // console.log('test', this.buildUser)
    },
    SaveAddPosition () {
      var c = false
      var chack = false
      // var chackIndetail = false
      var d = ''
      if (this.NamePositionAdd !== '') {
        for (let i = 0; i < this.AllPosotion.length; i++) {
          if (
            this.NamePositionAdd.toString() ===
            this.AllPosotion[i].name.toString()
          ) {
            c = true
            // d = this.AllPosotion[i].id
          }
        }
        if (c) {
          for (let j = 0; j < this.Position_Company_User.length; j++) {
            if (
              this.NamePositionAdd.toString() ===
              this.Position_Company_User[j].PositionName.toString()
            ) {
              chack = true
            }
          }
          if (!chack) {
            for (let k = 0; k < this.PositionDetailAll.length; k++) {
              if (
                this.NamePositionAdd.toString() ===
                this.PositionDetailAll[k].position_name.toString()
              ) {
                // chackIndetail = true
                d = this.PositionDetailAll[k].com_user_perm_id
              }
            }
            this.Position_Company_User.push({
              PositionName: this.NamePositionAdd,
              com_user_perm_id: d,
              limit_date: ''
            })
            this.NamePositionAdd = ''
            this.add = false
          } else {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'error',
              html: '<h3>มีตำแหน่งนี้ในผู้ใช้งานแล้วแล้ว</h3>'
            })
          }
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ไม่พบตำแหน่งที่ได้ทำการ<br/> โปรดตรวจสอบอีกครั้ง</h3>'
          })
        }
      } else {
        this.NamePositionAdd = ''
        this.add = false
      }
    },
    EditType (chossetype) {
      this.type = chossetype
      this.add = false
      this.DetailDialog(this.user_id)
    },
    async DetailDialog (item) {
      this.dialog_detail = true
      this.user_id = item
      var detailUser = {
        company_id: this.companyData.id,
        user_id: item
      }
      await this.$store.dispatch('actionsDetailUserCompany', detailUser)
      var datauser = await this.$store.state.ModuleShop.stateDetailUserCompany
      if (datauser.result === 'SUCCESS') {
        this.imgUser = datauser.data.user_data.img_path
        this.nameUser = datauser.data.user_data.fullname_th
        this.emailUser = datauser.data.user_data.email
        this.phoneUser = datauser.data.user_data.phone
        this.createDate = datauser.data.user_data.created_at
        this.update = datauser.data.user_data.updated_at
        // this.copy = datauser.data.company_position
        this.PositionDetailAll = datauser.data.company_position
        this.Position_Company_User = []
        this.copy = []
        for (let i = 0; i < datauser.data.company_position.length; i++) {
          // this.Position_Company_User.push(datauser.data.company_position[i].position_name)
          if (datauser.data.company_position[i].status !== 'inactive') {
            this.Position_Company_User.push({
              PositionName: datauser.data.company_position[i].position_name,
              com_user_perm_id:
                datauser.data.company_position[i].com_user_perm_id,
              company_position_id:
                datauser.data.company_position[i].company_position_id,
              limit_date: ''
            })
          }
        }
        this.copyData = this.Position_Company_User
      } else {
        if (datauser.message === 'This user is Unauthorized' || datauser.message === 'This user is unauthorized.' || datauser.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: datauser.message
          })
        }
      }
    },
    selectOrder (item) {
      this.StateStatus = item
      this.page = 1
      this.getListData()
    },
    async getListData () {
      var dataSeller = {
        company_id: this.companyData.id
      }
      await this.$store.dispatch('actionsListCompanyPosition', dataSeller)
      this.orderList = await this.$store.state.ModuleShop
        .stateListCompanyPorsition
      if (this.orderList.result === 'SUCCESS') {
        if (
          this.orderList.message ===
          'Get the list of users in the company successfully.'
        ) {
          this.countPOAll = this.orderList.data.total_all
          this.countPOReject = this.orderList.data.total_inactive
          this.countPOSuccess = this.orderList.data.total_active
          if (this.StateStatus === 0) {
            this.DataTable = this.orderList.data.all
            // this.showCountOrder = this.orderList.data.all.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
              this.showCountOrder = 0
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            this.DataTable = this.orderList.data.active
            // this.showCountOrder = this.orderList.data.active.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
              this.showCountOrder = 0
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            this.DataTable = this.orderList.data.inactive
            // this.showCountOrder = this.orderList.data.inactive.length
            if (this.DataTable.length === 0) {
              this.disableTable = false
              this.showCountOrder = 0
            } else {
              this.disableTable = true
            }
          }
        } else {
          this.$swal.fire({
            icon: 'error',
            text: 'ผู้ใช้งานนี้ไม่มีสิทธิ์การเข้าถึงใบเสนอราคาบริษัท',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        }
      } else {
        if (this.orderList.message === 'This user is Unauthorized' || this.orderList.message === 'This user is unauthorized.' || this.orderList.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: this.orderList.message
          })
        }
      }
    },
    async getDetailUser (item) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var detailUser = {
        company_id: this.companyData.id,
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsDetailUserCompany', detailUser)
      var datauser = await this.$store.state.ModuleShop.stateDetailUserCompany
      if (datauser.result === 'SUCCESS') {
        this.imgUser = datauser.data.user_data.img_path
        this.nameUser = datauser.data.user_data.fullname_th
        this.emailUser = datauser.data.user_data.email
        this.phoneUser = datauser.data.user_data.phone
        // this.createDate = '2022-06-16T02:27:03.000000Z'
        // this.update = '2022-06-16T02:27:03.000000Z'
        // console.log('test user', datauser)
      } else {
        if (datauser.message === 'This user is Unauthorized' || datauser.message === 'This user is unauthorized.' || datauser.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: datauser.message
          })
        }
      }
    },
    async saveEdit () {
      this.$store.commit('openLoader')
      this.list_data = []
      // console.log('testaa', this.AllPosotion)
      if (this.add === false) {
        for (let i = 0; i < this.Position_Company_User.length; i++) {
          for (let j = 0; j < this.AllPosotion.length; j++) {
            if (
              this.Position_Company_User[i].PositionName.toString() ===
              this.AllPosotion[j].name
            ) {
              this.list_data.push({
                com_user_perm_id:
                  this.Position_Company_User[i].com_user_perm_id,
                company_position_id: parseInt(this.AllPosotion[j].id),
                limit_day: '0',
                status: 'active'
              })
            }
          }
        }
        for (let i = 0; i < this.copy.length; i++) {
          var a = 0
          for (let j = 0; j < this.list_data.length; j++) {
            if (
              this.copy[i].company_position_id.toString() ===
              this.list_data[j].company_position_id.toString()
            ) {
              this.list_data[j].status = 'inactive'
              a = 1
            }
          }
          if (a === 0) {
            this.list_data.push({
              com_user_perm_id: this.copy[i].com_user_perm_id,
              company_position_id: this.copy[i].company_position_id,
              limit_day: '',
              status: 'inactive'
            })
          }
        }
        // console.log('testss', this.copy)
        // console.log('tests', this.list_data)
        var dataSave = {
          company_id: this.companyData.id,
          user_id: this.user_id,
          list_data: this.list_data
        }
        await this.$store.dispatch('actionsEditPositionCompany', dataSave)
        var data = await this.$store.state.ModuleShop.stateEditPositionCompany
        if (data.code === 200) {
          await this.$store.dispatch('actionsAuthorityUser')
          var response = await this.$store.state.ModuleUser.stateAuthorityUser
          var listCompany = await response.data.list_company
          for (let i = 0; i < listCompany.length; i++) {
            if (this.companyData.id === listCompany[i].company_id) {
              localStorage.removeItem('list_Company_detail')
              localStorage.setItem('list_Company_detail', Encode.encode(listCompany[i]))
            }
          }
          // await this.$EventBus.$emit('checkPathCompany')
          await this.$EventBus.$emit('chackAuthorityCompanyMenu')
          await this.$EventBus.$emit('chackAuthorityCompanyMenuMobile')
          this.dialog_detail = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            html: '<h3>แก้ไขสำเร็จ</h3>'
          })
          this.getListData()
          this.$store.commit('closeLoader')
        } else {
          if (data.message === 'This user is Unauthorized' || data.message === 'This user is unauthorized.' || data.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$store.commit('closeLoader')
            this.dialog_detail = false
            this.$swal.fire({
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'error',
              html: '<h3>ไม่สามารถกำหนดตำแหน่งและสิทธิ์การใช้งานในตำแหน่งเจ้าของนิติบุคคลได้</h3>'
            })
          }
        }
        // console.log('test', data)
        this.type = 'readonly'
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>กรุณายืนยันการเพิ่มตำแหน่ง ก่อนกดบันทึก</h3>'
        })
        this.type = 'readonly'
      }
      this.type = 'readonly'
    },
    async CreateUserPosition () {
      // console.log(this.buildUser)
      this.$store.commit('openLoader')
      var list = []
      for (let i = 0; i < this.buildUser.length; i++) {
        for (let j = 0; j < this.AllPosotion.length; j++) {
          if (
            this.buildUser[i].PositionName.toString() ===
            this.AllPosotion[j].name.toString()
          ) {
            list.push({
              id: this.AllPosotion[j].id,
              // limit_day: this.buildUser[i].limit_date
              limit_day: 0
            })
          }
        }
      }
      // console.log('test list', list)
      var dataCreate = {
        company_id: this.companyData.id,
        user_id: this.user_id,
        company_position_id: list
      }
      await this.$store.dispatch('actionCreatePositonUser', dataCreate)
      var data = await this.$store.state.ModuleShop.stateCreatePositionCompany
      if (data.code === 200) {
        await this.$store.dispatch('actionsAuthorityUser')
        var response = await this.$store.state.ModuleUser.stateAuthorityUser
        var listCompany = await response.data.list_company
        for (let i = 0; i < listCompany.length; i++) {
          if (this.companyData.id === listCompany[i].company_id) {
            localStorage.removeItem('list_Company_detail')
            localStorage.setItem('list_Company_detail', Encode.encode(listCompany[i]))
          }
        }
        // await this.$EventBus.$emit('checkPathCompany')
        await this.$EventBus.$emit('chackAuthorityCompanyMenu')
        await this.$EventBus.$emit('chackAuthorityCompanyMenuMobile')
        this.dialog_user = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          html: '<h3>สร้างรายการสำเร็จ</h3>'
        })
        this.getListData()
        this.$store.commit('closeLoader')
      } else {
        if (data.message === 'This user is Unauthorized' || data.message === 'This user is unauthorized.' || data.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.dialog_user = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>เกิดข้อผิดพลาด</h3>'
          })
        }
      }
      // console.log('test', data)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep table {
  tbody {
    tr {
      td:nth-child(4) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
      }
    }
  }
  thead {
    tr {
      th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
      }
    }
  }
  thead {
    tr {
      th:nth-child(4) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
      }
    }
  }
}
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
