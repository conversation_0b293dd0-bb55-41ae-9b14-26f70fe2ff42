<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">จัดการแท็กร้านค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToSellerMenu()">mdi-chevron-left</v-icon> จัดการแท็กร้านค้า
      </v-card-title>

      <v-col cols="12">
        <v-row>
          <v-col :cols="MobileSize ? 12 : 4">
            <v-col cols="12">
              <span style="font-size: 18px;"><b>หมวดหมู่แท็ก</b></span>
            </v-col>
            <v-col cols="12" v-if="this.listShopTag.length === 0">
              <v-card
                elevation="0"
                style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
              >
                <v-col cols="12" class="pa-2">
                  <v-list dense nav>
                    <v-list-item>
                      <v-list-item-content>
                        <v-list-item-title class="d-flex justify-center align-center">ไม่มีหมวดหมู่แท็ก</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                </v-col>
              </v-card>
            </v-col>
            <v-col cols="12" v-else>
              <v-card
                elevation="0"
                style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
              >
                <v-col cols="12" class="pa-2">
                  <v-list dense nav>
                    <v-list-item v-if="selectedTags.length > 0" @click="clearFilter()">
                      <v-list-item-content style="text-align: end;">
                        <v-list-item-title style="cursor: pointer; color: #27AB9C; font-weight: bold;">
                          ล้างตัวกรองแท็ก
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                    <v-list-group
                      v-for="category in listShopTag"
                      :key="category.tag_category_id"
                      v-model="openCategories[category.tag_category_id]"
                      no-action
                    >
                      <template v-slot:activator>
                        <v-list-item-content>
                          <v-list-item-title><v-icon color="#27AB9C">mdi-circle-small</v-icon>{{ category.tag_category_name }}</v-list-item-title>
                        </v-list-item-content>
                      </template>

                      <v-list-item
                        v-for="tag in category.tag_list"
                        :key="tag.tag_id"
                        @click="toggleTagSelection(tag)"
                        :style="{
                          cursor: 'pointer',
                          backgroundColor: selectedTags.includes(tag) ? '#E0F7F4' : '',
                          color: selectedTags.includes(tag) ? '#27AB9C' : ''
                        }"
                      >
                        <v-list-item-content>
                          <v-list-item-title :style="{ fontWeight: selectedTags.includes(tag) ? 'bold' : 'normal' }">
                            {{ tag.tag_name }}
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list-group>
                  </v-list>
                </v-col>
              </v-card>
            </v-col>
          </v-col>
          <v-col :cols="MobileSize ? 12 : 8">
            <v-col cols="12">
              <span style="font-size: 18px;"><b>เพิ่มแท็กร้านค้า</b></span>
            </v-col>
            <v-col cols="12">
              <v-text-field v-model="search" @keyup="searchData(search)" placeholder="ค้นหาชื่อแท็กสินค้า" outlined rounded dense hide-details style="border-radius: 8px;">
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col cols="12" style="text-align: end;">
              <v-btn rounded color="#27AB9C" style="color: white; font-size: 16px;" @click="openCreateShopTag()">เพิ่มแท็กร้านค้า</v-btn>
            </v-col>
            <v-col cols="12" v-if="filteredTags.length === 0">
              <v-card elevation="0" style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;">
                <v-container>
                  <v-col cols="12">
                    <div class="d-flex flex-column align-center justify-center">
                      <v-avatar size="200">
                        <v-img src="@/assets/tag.png" width="300" height="300" contain></v-img>
                      </v-avatar>
                      <span style="font-size: 18px;">ไม่มีแท็กร้านค้า โปรดทำการเลือกแท็ก</span>
                    </div>
                  </v-col>
                </v-container>
              </v-card>
            </v-col>
            <v-col cols="12" v-else>
              <v-card elevation="0" style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;">
                <v-container>
                  <v-col cols="12" class="pb-0">
                    <div v-for="category in filteredTags" :key="category.tag_category_id" class="mb-4">
                      <v-row align="center">
                        <v-col :cols="MobileSize ? 5 : 8" class="d-flex align-center pa-2">
                          <span style="font-size: 18px; font-weight: bold;">
                            {{ category.tag_category_name }}
                          </span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 4" class="d-flex justify-end pa-2" style="align-items: center;">
                          <v-btn icon small @click="editCategory(category)">
                            <v-icon>mdi-pencil</v-icon>
                          </v-btn>
                          <v-btn icon small @click="openDialogDeleteCategory(category)">
                            <v-icon>mdi-close</v-icon>
                          </v-btn>
                          <span class="pr-2 pl-2">{{ category.tag_category_status === 'active' ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}</span>
                          <v-switch
                            v-model="category.tag_category_status"
                            @change="UpdateShopTagStatus(category)"
                            :true-value="'active'"
                            :false-value="'inactive'"
                            hide-details
                            inset
                            style="margin-top: 0px; padding-top: 0px;"
                          >
                          </v-switch>
                        </v-col>
                      </v-row>

                      <div v-for="tag in category.tag_list" :key="tag.tag_id">
                        <v-row align="center" class="tag-item">
                          <v-col cols="10" class="d-flex align-center pa-2">
                            <span>{{ tag.tag_name }}</span>
                          </v-col>
                          <v-col cols="2" class="d-flex justify-end pa-2">
                            <v-btn icon small @click="editTag(tag)">
                              <v-icon>mdi-pencil</v-icon>
                            </v-btn>
                            <v-btn icon small @click="openDialogDeleteTag(tag, category)">
                              <v-icon>mdi-close</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </div>
                    </div>
                  </v-col>
                </v-container>
              </v-card>
            </v-col>
          </v-col>
        </v-row>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogAddTag" persistent max-width="500px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogAddTag = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>เพิ่มแท็กใหม่</b></span>
        </v-card-title>
        <br>
        <v-card-text class="pb-0">
          <v-col cols="12" class="pb-8">
            <v-text-field
              v-model="newCategory"
              label="เพิ่มหมวดหมู่แท็กใหม่"
              clearable
              hide-details
              dense
              solo
            ></v-text-field>
          </v-col>

          <v-col cols="12">
            <v-autocomplete
              v-model="selectedCategory"
              :items="categoryOptions"
              label="หรือเลือกหมวดหมู่แท็กที่มีอยู่"
              clearable
              hide-details
              dense
              solo
            ></v-autocomplete>
          </v-col>

          <v-col cols="12">
            <v-row>
              <v-col :cols="MobileSize ? 8 : 9">
                <v-text-field
                  v-model="newTag"
                  label="ชื่อแท็ก"
                  clearable
                  dense
                  solo
                  hide-details
                  @keyup.enter="addTagToList()"
                ></v-text-field>
              </v-col>
              <v-col :cols="MobileSize ? 4 : 3" style="display: flex; align-items: center;">
                <v-btn rounded color="#27AB9C" style="color: white;" @click="addTagToList()">เพิ่มแท็ก</v-btn>
              </v-col>
            </v-row>
          </v-col>

          <v-col cols="12">
            <v-chip-group
              column
              style="max-height: 100px; overflow-y: auto; display: flex; flex-wrap: wrap;"
            >
              <v-chip
                v-for="(tag, index) in tagList"
                :key="index"
                close
                @click:close="removeAddTag(index)"
              >
                {{ tag.name }}
              </v-chip>
            </v-chip-group>
          </v-col>
        </v-card-text>

        <v-card-actions>
          <v-col cols="12" class="d-flex justify-center pa-0">
            <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogAddTag = false">ยกเลิก</v-btn>
            <v-btn rounded class="ma-2" color="#27AB9C" style="color: white; width: 100px;" @click="CreateShopTag()">บันทึก</v-btn>
          </v-col>
        </v-card-actions>
     </v-card>
    </v-dialog>

    <v-dialog v-model="editCategoryDialog" persistent max-width="400px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="editCategoryDialog = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>แก้ไขหมวดหมู่แท็ก</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-text-field
            outlined
            dense
            hide-details
            label="ชื่อหมวดหมู่"
            v-model="editCategoryData.tag_category_name"
          />
        </v-card-text>
        <v-card-actions>
          <v-col cols="12" class="d-flex justify-center pa-0">
            <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="editCategoryDialog = false">ยกเลิก</v-btn>
            <v-btn rounded class="ma-2" color="#27AB9C" style="color: white; width: 100px;" @click="submitCategoryEdit()">บันทึก</v-btn>
          </v-col>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="editTagDialog" persistent max-width="400px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="editTagDialog = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>แก้ไขแท็ก</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-text-field
            outlined
            dense
            hide-details
            label="ชื่อแท็ก"
            v-model="editTagData.tag_name"
          />
        </v-card-text>
        <v-card-actions>
          <v-col cols="12" class="d-flex justify-center pa-0">
            <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="editTagDialog = false">ยกเลิก</v-btn>
            <v-btn rounded class="ma-2" color="#27AB9C" style="color: white; width: 100px;" @click="submitTagEdit()">บันทึก</v-btn>
          </v-col>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogDeleteCategory" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDeleteCategory = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #f6f6f6">
          <v-img
            src="@/assets/tagDelete.png"
            contain
            max-width="200"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ลบหมวดหมู่แท็ก</b></span><br><br>
            <span style="font-size: 16px;">คุณแน่ใจหรือไม่ว่าต้องการลบหมวดหมู่แท็ก <b>{{ this.tagCategoryName }}</b> นี้</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogDeleteCategory = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="removeCategory()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogDeleteTag" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDeleteTag = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #f6f6f6">
          <v-img
            src="@/assets/tagDelete.png"
            contain
            max-width="200"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ลบแท็ก</b></span><br><br>
            <span style="font-size: 16px;">คุณแน่ใจหรือไม่ว่าต้องการลบแท็ก <b>{{ this.tagName }}</b> นี้</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogDeleteTag = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="removeTag()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      listShopTag: [],
      originalShopTag: [],
      search: '',
      shopID: '',
      tagList: [],
      dialogAddTag: false,
      selectedCategory: null,
      newCategory: '',
      newTag: '',
      tagID: '',
      tagName: '',
      tagCategoryID: '',
      tagCategoryName: '',
      tagCategoryStatus: '',
      tagCategoryType: '',
      categoryOptions: [],
      switchTagCategoryStatus: '',
      editCategoryDialog: false,
      editCategoryData: {
        tag_category_id: null,
        tag_category_name: '',
        tag_category_type: '',
        tag_category_status: '',
        tag_list: []
      },
      editTagDialog: false,
      editTagData: {
        tag_id: null,
        tag_name: ''
      },
      dialogDeleteCategory: false,
      dialogDeleteTag: false,
      selectedTags: [],
      openCategories: {}
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    filteredTags () {
      if (this.selectedTags.length === 0) {
        // หากไม่มีการเลือก แสดงทั้งหมด
        return this.listShopTag
      }
      // กรองรายการแท็กในฝั่งขวาตามแท็กที่เลือกในฝั่งซ้าย
      return this.listShopTag.map(category => {
        return {
          ...category,
          tag_list: category.tag_list.filter(tag =>
            this.selectedTags.some(selectedTag => selectedTag.tag_id === tag.tag_id)
          )
        }
      }).filter(category => category.tag_list.length > 0) // กรองหมวดหมู่ที่ไม่มีแท็กที่เลือก
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageTagShopMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ManageTagShop' }).catch(() => { })
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
    this.ListShopTag()
  },
  methods: {
    backToSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    toggleTagSelection (tag) {
      this.selectedTags = []

      const index = this.selectedTags.findIndex(t => t.tag_id === tag.tag_id)
      if (index === -1) {
        // ถ้าไม่ได้เลือกแท็กนี้มาก่อน
        this.selectedTags.push(tag)
      } else {
        // ถ้ากดเลือกแท็กที่เลือกอยู่แล้ว
        this.selectedTags.splice(index, 1)
      }
    },
    clearFilter () {
      this.selectedTags = []

      this.listShopTag.forEach(category => {
        this.openCategories[category.tag_category_id] = false
      })
    },
    async searchData (search) {
      if (search.length > 0) {
        this.listShopTag = await this.originalShopTag.filter(category => {
          return category.tag_list.some(tag =>
            tag.tag_name.toLowerCase().includes(search.toLowerCase())
          )
        })
      } else {
        this.listShopTag = await [...this.originalShopTag]
      }
    },
    async ListShopTag () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('actionListShopTag', data)
      var responseData = await this.$store.state.ModuleShop.stateListShopTag
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listShopTag = responseData.data
        this.originalShopTag = responseData.data
        this.categoryOptions = responseData.data.map(category => category.tag_category_name)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    async UpdateShopTagStatus (category) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        tag_category_id: category.tag_category_id,
        tag_category_status: category.tag_category_status
      }
      await this.$store.dispatch('actionUpdateShopTagStatus', data)
      var responseData = await this.$store.state.ModuleShop.stateUpdateShopTagStatus
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        const message = category.tag_category_status === 'active'
          ? 'เปิดใช้งานแท็กสำเร็จ'
          : 'ปิดใช้งานแท็กสำเร็จ'

        this.$swal.fire({
          icon: 'success',
          text: message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.ListShopTag()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    openCreateShopTag () {
      this.selectedCategory = null
      this.newCategory = ''
      this.newTag = ''
      this.tagList = []
      this.dialogAddTag = true
    },
    async CreateShopTag () {
      this.$store.commit('openLoader')
      this.dialogAddTag = false
      const categoryName = this.newCategory || this.selectedCategory
      if (!categoryName || this.tagList.length === 0) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'info',
          html: '<span style="font-size: 16px;">กรุณาเลือกหรือเพิ่มหมวดหมู่ และเพิ่มแท็กอย่างน้อย 1 รายการ</span>',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        return
      }

      if (this.selectedCategory) {
        const categoryToUpdate = this.listShopTag.find(category => category.tag_category_name === categoryName)
        this.tagCategoryID = categoryToUpdate.tag_category_id
        this.tagCategoryType = categoryToUpdate.tag_category_type
        this.tagCategoryStatus = categoryToUpdate.tag_category_status

        const updatedTagList = [
          ...categoryToUpdate.tag_list,
          ...this.tagList.map(tag => ({
            tag_id: -1,
            tag_index: tag.index + 1,
            tag_name: tag.name
          }))
        ]

        categoryToUpdate.tag_list = updatedTagList

        const payloadUpdate = {
          seller_shop_id: this.shopID,
          tag_category_id: this.tagCategoryID,
          tag_category_name: categoryName,
          tag_category_type: this.tagCategoryType,
          tag_category_status: this.tagCategoryStatus,
          tag_list: updatedTagList
        }

        await this.$store.dispatch('actionUpdateShopTag', payloadUpdate)
        var responseUpdate = await this.$store.state.ModuleShop.stateUpdateShopTag
        if (responseUpdate.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'success',
            text: 'เพิ่มแท็กสำเร็จ',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2000
          })
          this.ListShopTag()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: responseUpdate.message,
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2000
          })
        }
      } else {
        const payloadCreate = {
          seller_shop_id: this.shopID,
          tag_category_name: categoryName,
          tag_category_type: 'product',
          tag_list: this.tagList
        }

        await this.$store.dispatch('actionCreateShopTag', payloadCreate)
        var responseCreate = await this.$store.state.ModuleShop.stateCreateShopTag
        if (responseCreate.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'success',
            text: 'เพิ่มแท็กสำเร็จ',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2000
          })
          this.ListShopTag()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: responseCreate.message,
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2000
          })
        }
      }
    },
    addTagToList () {
      if (this.newTag) {
        this.tagList.push({ name: this.newTag, index: this.tagList.length })
        this.newTag = ''
      }
    },
    removeAddTag (index) {
      this.tagList.splice(index, 1)
    },
    editCategory (category) {
      this.editCategoryData = JSON.parse(JSON.stringify(category))
      this.editCategoryDataName = this.editCategoryData.tag_category_name
      this.editCategoryDialog = true
    },
    async submitCategoryEdit () {
      this.$store.commit('openLoader')
      const payloadUpdate = this.editCategoryData
      await this.$store.dispatch('actionUpdateShopTag', payloadUpdate)
      var responseUpdate = await this.$store.state.ModuleShop.stateUpdateShopTag
      if (responseUpdate.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'แก้ไขหมวดหมู่แท็กสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.ListShopTag()
        this.editCategoryDialog = false
      } else {
        this.$store.commit('closeLoader')
        this.editCategoryDialog = false
        this.$swal.fire({
          icon: 'error',
          text: responseUpdate.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    openDialogDeleteCategory (category) {
      this.tagCategoryID = category.tag_category_id
      this.tagCategoryName = category.tag_category_name
      this.dialogDeleteCategory = true
    },
    async removeCategory () {
      this.$store.commit('openLoader')
      const payloadDelete = {
        seller_shop_id: this.shopID,
        tag_category_id: this.tagCategoryID
      }

      await this.$store.dispatch('actionDeleteShopTag', payloadDelete)
      var responseDelete = await this.$store.state.ModuleShop.stateDeleteShopTag
      if (responseDelete.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'ลบหมวดหมู่แท็กสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.ListShopTag()
        this.dialogDeleteCategory = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseDelete.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    editTag (tag) {
      this.editTagData = JSON.parse(JSON.stringify(tag))
      this.editTagCategoryId = this.findCategoryIdByTag(tag.tag_id)
      this.editTagDialog = true
    },
    findCategoryIdByTag (tadID) {
      for (const category of this.listShopTag) {
        if (category.tag_list.some(t => t.tag_id === tadID)) {
          return category.tag_category_id
        }
      }
      return null
    },
    async submitTagEdit () {
      this.$store.commit('openLoader')
      const category = this.listShopTag.find(c => c.tag_category_id === this.editTagCategoryId)
      if (!category) return

      const tagIndex = category.tag_list.findIndex(t => t.tag_id === this.editTagData.tag_id)
      if (tagIndex !== -1) {
        category.tag_list.splice(tagIndex, 1, JSON.parse(JSON.stringify(this.editTagData)))
      }

      const payloadUpdate = {
        seller_shop_id: category.seller_shop_id,
        tag_category_id: category.tag_category_id,
        tag_category_name: category.tag_category_name,
        tag_category_type: category.tag_category_type,
        tag_category_status: category.tag_category_status,
        tag_list: category.tag_list.map((tag, index) => ({
          ...tag,
          tag_index: index
        }))
      }

      await this.$store.dispatch('actionUpdateShopTag', payloadUpdate)
      var responseUpdate = await this.$store.state.ModuleShop.stateUpdateShopTag
      if (responseUpdate.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'แก้ไขแท็กสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.ListShopTag()
        this.editTagDialog = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseUpdate.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    openDialogDeleteTag (tag, category) {
      this.tagCategoryID = category.tag_category_id
      this.tagID = tag.tag_id
      this.tagName = tag.tag_name
      this.dialogDeleteTag = true
    },
    async removeTag () {
      this.$store.commit('openLoader')
      const payloadDelete = {
        seller_shop_id: this.shopID,
        tag_category_id: this.tagCategoryID,
        tag_id: this.tagID
      }

      await this.$store.dispatch('actionDeleteShopTagItem', payloadDelete)
      var responseDelete = await this.$store.state.ModuleShop.stateDeleteShopTagItem
      if (responseDelete.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'ลบแท็กสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.ListShopTag()
        this.dialogDeleteTag = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseDelete.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    }
  }
}
</script>

<style scoped>
.tag-item {
  border-radius: 8px;
  border: 1px solid #ddd;
  /* padding: 8px; */
  margin-top: 20px;
  background-color: #ffffff;
}
</style>
