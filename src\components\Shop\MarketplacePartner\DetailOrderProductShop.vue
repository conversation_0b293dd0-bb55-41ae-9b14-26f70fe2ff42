<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-3" @click="backtoseller()">mdi-chevron-left</v-icon>รายละเอียดการสั่งซื้อสินค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoseller()">mdi-chevron-left</v-icon>รายละเอียดการสั่งซื้อสินค้า</v-card-title>
    <v-card :width="MobileSize ? '96%' : '98%'" style="background: #f9fafd;" elevation="0" class="mx-2 pa-4 my-2">
      <v-row class="d-flex" :style="MobileSize || IpadSize ? 'font-size: small; margin-left: -12px;' : 'font-size: 15px;'">
        <v-col :cols="MobileSize || IpadSize ? '12' : '7'">
          <v-row>
            <v-col cols="5" style="font-weight: 700;">รหัสการสั่งซื้อ :</v-col>
            <v-col cols="7">{{ orderNumber }}</v-col>
          </v-row>
          <v-row>
            <v-col cols="5" style="font-weight: 700;">ผู้ซื้อ :</v-col>
            <v-col cols="7">{{ itemDetailOrder.fullname }}</v-col>
          </v-row>
          <v-row>
            <v-col cols="5" style="font-weight: 700;">วันที่ทำรายการ :</v-col>
            <v-col cols="7">{{ itemDetailOrder.created_at !== '-' ? formatDate(itemDetailOrder.created_at) : '-' }}</v-col>
          </v-row>
          <v-row>
            <v-col cols="5" style="font-weight: 700;">วันที่ชำระเงิน :</v-col>
            <v-col cols="7">{{ itemDetailOrder.transaction_at !== '-' ? formatDate(itemDetailOrder.transaction_at) : '-' }}</v-col>
          </v-row>
          <v-row>
            <v-col cols="5" style="font-weight: 700;">ใบกำกับภาษี :</v-col>
            <v-col cols="7">
              <span v-if="itemDetailOrder.receipt_transaction_code !== '-'" @click="receiptBill(itemDetailOrder.receipt_transaction_code)" style="font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{ itemDetailOrder.bill_order_number }}</span>
              <span v-else>-</span>
          </v-col>
          </v-row>
        </v-col>
        <v-col :cols="MobileSize || IpadSize ? '12' : '5'">
          <v-row class="d-flex align-center justify-end mr-1 my-auto">
            <v-card style="background: #ffffff; border-radius: 10px;" :style="MobileSize || IpadSize ? 'width: 96%;' : 'width: 90%;'" elevation="0" class="">
              <v-row class="pa-4">
                <v-col cols="12" style="font-weight: 700;" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">สถานะคำสั่งซื้อ : <span :style="itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ' ? 'color: #52c41a;' : 'color: #47b8ff;'" style="text-align: center; line-height: 1;"><v-icon large :color="itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ' ? '#52c41a' : '#47b8ff'">mdi-circle-small</v-icon>{{ itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ' ? 'ชำระเงินแล้ว' : 'สั่งซื้อแล้ว' }}</span></v-col>
                <!-- <v-col cols="5" style="align-items: center;"></v-col> -->
              </v-row>
            </v-card>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-row>
      <v-card-title class="mx-2 mt-3" style="font-weight: 700; font-size: medium; color: #333333;"><v-img src="@/assets/Marketplace_partner/doc.png" width="24" height="24" class="mr-3"></v-img>รายละเอียดเอกสาร</v-card-title>
    </v-row>
    <v-row class="mx-2 my-2" dense style="border-bottom: solid 1px #F2F2F2;">
      <v-col :cols="MobileSize || IpadSize ? '12' : '6'" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">
        <v-row dense>
          <v-col cols="5" style="font-weight: 700;">เลขที่ใบเสนอราคา :</v-col>
          <v-col cols="7">
            <span v-if="itemDetailOrder.QT_order_path !== '-'" @click="window.open(itemDetailOrder.QT_order_path, '_blank')" style="font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{ 'QT-' + textSplit }}</span>
            <span v-else>-</span>
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="5" style="font-weight: 700;">เลขที่ใบสั่งซื้อ (PO) :</v-col>
          <v-col cols="7">
            <span v-if="itemDetailOrder.PO_External_path !== '-'" @click="window.open(itemDetailOrder.PO_External_path, '_blank')" style="font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{ 'PO-' + textSplit }}</span>
            <span v-else>-</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? '12' : '6'" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">
        <v-row dense>
          <v-col cols="5" style="font-weight: 700;">เลขที่ใบขอซื้อ (PR) :</v-col>
          <v-col cols="7">
            <span v-if="itemDetailOrder.PR_External_path !== '-'" @click="window.open(itemDetailOrder.PR_External_path, '_blank')" style="font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{ 'PR-' + textSplit }}</span>
            <span v-else>-</span>
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="5" style="font-weight: 700;">เลขที่ Sale Order :</v-col>
          <v-col cols="7">
            <span v-if="itemDetailOrder.SO_External_path !== '-'" @click="window.open(itemDetailOrder.SO_External_path, '_blank')" style="font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{ 'SO-' + textSplit }}</span>
            <span v-else>-</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? '12' : '6'" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">
        <v-row dense>
          <v-col cols="5" style="font-weight: 700;">เลขที่ใบเสร็จ :</v-col>
          <v-col cols="7">
            <span v-if="itemDetailOrder.receipt_transaction_code !== '-'" @click="receiptBill(itemDetailOrder.receipt_transaction_code)" style="font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{ itemDetailOrder.bill_order_number }}</span>
            <span v-else>-</span>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <!-- <v-row>
      <v-card-title class="mx-2 mt-3" style="font-weight: 700; font-size: medium; color: #333333;"><v-img src="@/assets/iconMap.png" width="24" height="24" class="mr-3"></v-img>ที่อยู่ในการจัดส่งสินค้า</v-card-title>
    </v-row>
    <v-row class="mx-2 my-2" dense style="border-bottom: solid 1px #F2F2F2;">
      <span class="mb-1">ที่อยู่</span>
    </v-row> -->
    <v-row>
      <v-card-title class="mx-2 mt-3" style="font-weight: 700; font-size: medium; color: #333333;"><v-img src="@/assets/Marketplace_partner/location.png" width="24" height="24" class="mr-3"></v-img>ที่อยู่ในการจัดส่งใบกำกับภาษี</v-card-title>
    </v-row>
    <v-row class="mx-2 my-2" dense style="border-bottom: solid 1px #F2F2F2;" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">
      <span class="mb-1 pl-2">{{ itemDetailOrder.detail }}</span>
    </v-row>
    <v-row>
      <v-card-title class="mx-2 mt-3" style="font-weight: 700; font-size: medium; color: #333333;"><v-img src="@/assets/Marketplace_partner/shopping.png" width="24" height="24" class="mr-3"></v-img>รายการสั่งซื้อสินค้า</v-card-title>
    </v-row>
    <v-row class="mx-2 my-2" dense style="border-bottom: solid 1px #F2F2F2;">
      <v-col cols="12">
        <v-data-table
          :headers="headers"
          :items="itemDetailPackage"
          class="elevation-1 flex-table mb-2"
          no-results-text="ไม่พบข้อมูล"
          no-data-text="ไม่พบข้อมูล"
          hide-default-footer
          width="90%"
          style="align-items: flex-start;"
        >
        <template v-slot:[`item.package_code`]="{ item }">
          <span v-if="item.package_code" style="white-space: nowrap;">{{ item.package_code }}</span>
          <span v-else>-</span>
        </template>
        <template v-slot:[`item.packageDetail`]="{ item }">
          <div v-if="item.packageDetail.length">
          <div v-for="(detail, index) in item.packageDetail" :key="index">
            <span class="showTable" style="display: block;">
              {{ detail }}
            </span>
          </div>
        </div>
        <div v-else>
          <span class="showTable" style="display: block;">-</span>
        </div>
        </template>
        <template v-slot:[`item.packagePrice`]="{ item }">
          <span v-if="item.packagePrice" style="white-space: nowrap;">{{ formatNumber(item.packagePrice) }}</span>
          <span v-else>-</span>
        </template>
        <template v-slot:[`item.packagePaymentType`]="{ item }">
          <span v-if="item.packagePaymentType" style="white-space: nowrap;">{{ item.packagePaymentType === 'Monthly' ? 'รายเดือน' : item.packagePaymentType === 'Yearly' ? 'รายปี' : item.packagePaymentType === 'Percent' ? 'เปอร์เซ็นต์' : '-' }}</span>
          <span v-else>-</span>
        </template>
        </v-data-table>
      </v-col>
      <v-col cols="12" class="d-flex flex-column">
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-left: -10px;' : 'font-size: medium;'">ราคาไม่รวมภาษีมูลค่าเพิ่ม</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-right: -10px;' : 'font-size: medium;'">{{ formatNumber(itemDetailOrder.total_price_no_vat) }} บาท</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-left: -10px;' : 'font-size: medium;'">ภาษีมูลค่าเพิ่ม <span style="color: #b3b3b3;">(7%)</span></v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-right: -10px;' : 'font-size: medium;'">{{ formatNumber(itemDetailOrder.total_vat) }} บาท</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-left: -10px;' : 'font-size: medium;'">ราคารวมภาษีมูลค่าเพิ่ม</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-right: -10px;' : 'font-size: medium;'">{{ formatNumber(itemDetailOrder.total_price_vat) }} บาท</v-card-text>
        </div>
        <div class="d-flex justify-content-between">
          <v-card-text style="text-align: left; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: medium; margin-left: -10px;' : 'font-size: large;'">ราคารวมทั้งหมด</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600; color: #27ab9c;" :style="MobileSize || IpadSize ? 'font-size: medium; margin-right: -10px;' : 'font-size: large;'">{{ formatNumber(itemDetailOrder.total_price_vat) }} <span style="color: black;">บาท</span></v-card-text>
        </div>
        <v-card v-if="itemDetailOrder.transaction_text !== 'ชำระเงินสำเร็จ'" style="background-color: #fafafa; border-radius: 8px;" elevation="0" class="ma-2">
          <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
            <v-card-text style="text-align: left; font-size: large;">รูปแบบการชำระเงิน</v-card-text>
            <v-card-text style="text-align: right; font-size: large; font-weight: 600;">{{ itemDetailOrder.payType }}</v-card-text>
          </div>
          <div class="d-flex justify-content-between">
            <v-card-text style="text-align: left; font-size: large;">ธนาคารที่ชำระเงิน</v-card-text>
            <v-card-text style="text-align: right; font-size: large; font-weight: 600;">{{ itemDetailOrder.bank_name }}</v-card-text>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <v-row>
      <v-card-title class="mx-2 mt-3" style="font-weight: 700; font-size: medium; color: #333333;"><v-img src="@/assets/Marketplace_partner/inventory.png" width="24" height="24" class="mr-3"></v-img>รายละเอียดรายการสั่งซื้อ</v-card-title>
    </v-row>
    <v-row class="mx-2 my-2" dense style="border-bottom: solid 1px #F2F2F2;">
      <v-col v-if="!MobileSize && !IpadSize" cols="12" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">
        <span style="font-weight: 700;" class="mr-3">วันที่เริ่มสัญญา :</span>
        <span class="mr-6">{{ itemDetailOrder.date_start !== '-' ? formatDateThai(itemDetailOrder.date_start) : '-' }}</span>
        <span style="font-weight: 700;" class="mr-3">วันที่สิ้นสุดสัญญา :</span>
        <span class="mr-6">{{ itemDetailOrder.date_end !== '-' ? formatDateThai(itemDetailOrder.date_end) : '-' }}</span>
      </v-col>
      <v-col v-if="MobileSize || IpadSize" cols="12">
        <div class="d-flex justify-content-between">
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="font-weight: 700; margin: -10px 0 -10px -10px; text-align: left;">วันที่เริ่มสัญญา :</v-card-text>
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="text-align: right; margin: -10px -10px -10px 0;">{{ itemDetailOrder.date_start !== '-' ? formatDateThai(itemDetailOrder.date_start) : '-' }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between">
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="font-weight: 700; margin: -10px 0 -10px -10px;text-align: left;">วันที่สิ้นสุดสัญญา :</v-card-text>
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="text-align: right; margin: -10px -10px -10px 0;">{{ itemDetailOrder.date_end !== '-' ? formatDateThai(itemDetailOrder.date_end) : '-' }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between">
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="font-weight: 700; margin: -10px 0 -10px -10px;text-align: left;">ราคารวมทั้งหมด (เฉพาะ Package ตามวันที่สัญญา) :</v-card-text>
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="text-align: right; margin: -10px -10px -10px 0;">{{ formatNumber(itemDetailOrder.total_price_vat) }} บาท</v-card-text>
        </div>
        <div class="d-flex justify-content-between">
          <v-card-text :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="font-weight: 700; margin: -10px 0 -10px -10px;text-align: left;">SO Type :</v-card-text>
          <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="text-align: right;">
            <v-chip v-if="itemDetailOrder.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)" small>One Time</v-chip>
            <v-chip v-else-if="itemDetailOrder.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)" small>Recurring</v-chip>
            <v-chip v-else-if="itemDetailOrder.pay_type === 'general'" text-color="#808B96" color="#F4F6F6" small>General</v-chip>
          </span>
        </div>
      </v-col>
      <v-col v-if="!MobileSize && !IpadSize" cols="12" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: 15px;'">
        <span style="font-weight: 700;" class="mr-3">ราคารวมทั้งหมด (เฉพาะ Package ตามวันที่สัญญา) :</span>
        <span class="mr-6">{{ formatNumber(itemDetailOrder.total_price_vat) }} บาท</span>
        <span style="font-weight: 700;" class="mr-3">SO Type :</span>
        <span class="mr-6">
            <v-chip v-if="itemDetailOrder.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)" small>One Time</v-chip>
            <v-chip v-else-if="itemDetailOrder.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)" small>Recurring</v-chip>
            <v-chip v-else-if="itemDetailOrder.pay_type === 'general'" text-color="#808B96" color="#F4F6F6" small>General</v-chip>
        </span>
      </v-col>
    </v-row>
    <v-row>
      <v-card-title class="mx-2 mt-3" style="font-weight: 700; font-size: medium; color: #333333;"><v-img src="@/assets/Marketplace_partner/receipt.png" width="24" height="24" class="mr-3"></v-img>
        การชำระเงิน <span style="text-align: center; line-height: 1;" :style="itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ' ? 'color: #52c41a;' : 'color: #47b8ff;'"><v-icon :color="itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ' ? '#52c41a' : '#47b8ff'" large>mdi-circle-small</v-icon>{{ itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ' ? 'ชำระเงินแล้ว' : 'สั่งซื้อแล้ว' }}</span></v-card-title>
    </v-row>
    <v-row v-if="itemDetailOrder.transaction_text === 'ชำระเงินสำเร็จ'" class="mx-1 my-2">
      <v-col cols="12" class="d-flex flex-column" style="margin-top: -12px;">
        <div class="d-flex justify-content-between" style="margin-top: -6px; margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">รหัสการสั่งซื้อ</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ itemDetailOrder.order_number }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">จำนวนเงิน</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ formatNumber(itemDetailOrder.total_price_vat) }} บาท</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">วันและเวลาที่ชำระเงิน</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ itemDetailOrder.transaction_at !== '-' ? formatDateEng(itemDetailOrder.transaction_at) : '-' }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">Ref</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ itemDetailOrder.ref }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">รูปแบบการชำระเงิน</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ itemDetailOrder.payType }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">ธนาคารที่ชำระเงิน</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ itemDetailOrder.bank_name }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">ผลการชำระเงิน</v-card-text>
          <v-card-text style="text-align: right; font-weight: 600;" :style="MobileSize || IpadSize ? 'font-size: small; margin-top: -10px;' : 'font-size: medium;'">{{ itemDetailOrder.transaction_text }}</v-card-text>
        </div>
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text style="text-align: left; font-size: small;">คุณได้ชำระเงินเสร็จเรียบร้อยแล้ว ขอบคุณสำหรับการใช้บริการ</v-card-text>
        </div>
      </v-col>
    </v-row>
    <v-row v-else class="mx-1 my-2">
      <v-col cols="12" class="d-flex flex-column" style="margin-top: -12px;">
        <div class="d-flex justify-content-between" style="margin-bottom: -16px;">
          <v-card-text class="mb-2" style="text-align: left;" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'">รายละเอียดคำสั่งซื้อนี้ยังไม่ต้องดำเนินการชำระเงิน</v-card-text>
        </div>
      </v-col>
    </v-row>
  </v-card>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      orderNumber: '',
      textSplit: '',
      itemDetailOrder: [],
      itemDetailPackage: [],
      headers: [
        { text: 'ชื่อ Package', value: 'package_code', align: 'start', width: '20%', filterable: false, sortable: false, class: 'backgroundTable fontTable--text colorGray' },
        { text: 'รายละเอียด Package', value: 'packageDetail', align: 'start', width: '40%', filterable: false, sortable: false, class: 'backgroundTable fontTable--text colorGray' },
        { text: 'ราคา Package', value: 'packagePrice', align: 'start', width: '20%', filterable: false, sortable: false, class: 'backgroundTable fontTable--text colorGray' },
        { text: 'การชำระเงิน', value: 'packagePaymentType', align: 'start', width: '20%', filterable: false, sortable: false, class: 'backgroundTable fontTable--text colorGray' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        // this.$router.push(`/DetailOrderProductPartnerMobile?orderNumber=${this.orderNumber}`).catch(() => {})
        this.$router.push('/orderListPartnerMobile').catch(() => {})
      } else {
        this.$router.push(`/DetailOrderProductShop?orderNumber=${this.orderNumber}&billNumber=${this.billNumber}`).catch(() => {})
      }
    }
  },
  async created () {
    this.orderNumber = this.$route.query.orderNumber
    this.textSplit = this.orderNumber.split('-')[1]
    this.billNumber = this.$route.query.billNumber
    await this.getDetailOrderList()
  },
  methods: {
    backtoseller () {
      if (this.MobileSize) {
        this.$router.push({ path: '/orderListPartnerMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/orderListPartner' }).catch(() => { })
      }
    },
    async getDetailOrderList () {
      var data = {
        order_number: this.orderNumber,
        bill_order_number: this.billNumber
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetDetailPartnerOrderList', data)
      var response = await this.$store.state.ModuleBusiness.stateGetDetailPartnerOrderList
      if (response.code === 200) {
        // console.log('response.data', response.data)
        this.itemDetailOrder = response.data[0]
        this.itemDetailPackage = response.data
        // console.log('this.itemDetailOrder', this.itemDetailOrder)
        // this.itemOrderList = [...response.data]
        // console.log('this.itemOrderList', this.itemOrderList)
        // this.countOrderList = response.data.length
      }
      this.$store.commit('closeLoader')
    },
    formatDate (dateString) {
      const monthNames = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const date = new Date(dateString)
      const day = date.getDate()
      const month = monthNames[date.getMonth()]
      const year = date.getFullYear() + 543
      const hours = date.getHours()
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${day} ${month} ${year} ${hours}:${minutes} น.`
    },
    formatDateThai (dateString) {
      dateString = String(dateString)
      const monthNames = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const [day, month, year] = dateString.split('/')
      const editDay = parseInt(day, 10)
      const editMonth = monthNames[parseInt(month, 10) - 1]
      const editYear = parseInt(year) + 543
      return `${editDay} ${editMonth} พ.ศ.${editYear}`
    },
    formatDateEng (dateString) {
      const [date, time] = dateString.split(' ')
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year} ${time}`
    },
    formatNumber (data) {
      var format = ''
      if (data === '-' || data === null) {
        format = '0'
      } else {
        format = Number(data).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }
      return format
    },
    async receiptBill (val) {
      const data = {
        transactionCode: val
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}partner/etax/decument_receipt`,
          method: 'POST',
          data: data
        }).then((response) => {
          window.open(response.data.etaxResponse.pdfURL, '_blank')
        })
      } catch (error) {
        // console.log('error', error)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    }
  }
}
</script>

<style scoped>
::v-deep .ck-content .image {
  margin: 0 !important;
}
::v-deep .ck-content .image img {
  margin-bottom: .5vw !important;
  width: 24vw !important;
}
::v-deep th.colorGray {
  background-color: #f3f5f7 !important;
  color: black !important;
}
.package-detail {
  display: block;
  max-height: 240px;
  overflow-y: auto;
  padding: 5px;
  border: 1px solid #f3f5f7;
}
</style>
