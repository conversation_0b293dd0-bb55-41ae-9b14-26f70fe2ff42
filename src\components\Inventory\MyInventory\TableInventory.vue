<template lang="html">
  <div>
    <a-row type="flex">
      <a-col :span="24">
        <v-card outlined class="mt-5">
          <v-data-table
            :headers="headers"
            :items="props"
            @page-count="pageCount = $event"
            :page.sync="page"
            :items-per-page="itemsPerPage"
            class="elevation-0, rounded-lg"
            :search="search"
            hide-default-footer
            no-data-text="ไม่มีสต๊อกสินค้า"
          >
            <template v-slot:[`item.actual_stock`]="{ item }">
              <span>{{ Number(item.actual_stock).toLocaleString(undefined) }}</span>
            </template>
            <template v-slot:[`item.effective_stock`]="{ item }">
              <span>{{ Number(item.effective_stock).toLocaleString(undefined) }}</span>
            </template>
          </v-data-table>
        </v-card>
        <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { Col, Row } from 'ant-design-vue'
export default {
  props: ['props'],
  components: {
    'a-row': Row,
    'a-col': Col
  },
  data () {
    return {
      pageCount: 5,
      page: 1,
      itemsPerPage: 4,
      search: '',
      headers: [
        { text: 'รหัสสต๊อกสินค้า', value: 'inventory_code', sortable: false, align: 'left' },
        { text: 'actual_stock', value: 'actual_stock', sortable: false },
        { text: 'effective_stock', value: 'effective_stock', sortable: false }
        // { text: 'edit', value: 'edit', sortable: false, align: 'center' }
      ]
    }
  }
}
</script>

<style lang="css" scoped>
</style>
