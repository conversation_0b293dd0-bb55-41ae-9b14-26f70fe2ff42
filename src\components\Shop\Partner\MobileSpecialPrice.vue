<template>
  <v-card elevation="0" width="100%" height="100%">
    <!-- {{ MobileSize }}
    {{ IpadSize }}
    {{ IpadProSize }} -->
    <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="pt-6">การร้องขอราคาพิเศษ</v-card-title>
    <v-card-text>
      <v-row no-gutters>
        <v-col cols="12" class="py-0 pr-2">
          <a-tabs @change="getRequest">
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countall }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countWaiting }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">อนุมัติ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countActive }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">สร้างใบเสนอราคาแล้ว <a-tag color="#42A5F5" style="border-radius: 8px;">{{ countSuccessQu }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">สร้างรายการสั่งซื้อแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countSuccess }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="5"><span slot="tab">แก้ไข <a-tag color="#F5222D" style="border-radius: 8px;">{{ countEdited }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="6"><span slot="tab">ยกเลิก/ปฏิเสธ <a-tag color="#F5222D" style="border-radius: 8px;">{{ countCancel }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize || !IpadSize || !IpadProSize ? 'pl-0 pr-3 mb-3 pt-3' : 'pl-2 pr-2 mb-3 pt-3'">
          <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากชื่อบริษัทหรือหมายเลขร้องขอ" outlined dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col cols="12" class="pt-3 pb-3" v-if="disableTable === true">
          <v-row dense>
            <v-col cols="12" md="6" sm="12" class="pt-2">
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 0">รายการร้องขอราคาพิเศษทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 1">รายการร้องขอราคาพิเศษที่รออนุมัติทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 2">รายการร้องขอราคาพิเศษที่อนุมัติทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 3">รายการร้องขอราคาพิเศษที่สร้างใบเสนอราคาแล้วทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 4">รายการร้องขอราคาพิเศษที่สร้างรายการสั่งซื้อแล้วทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 5">รายการร้องขอราคาพิเศษที่แก้ไขทั้งหมด {{ showCountRequest }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 6">รายการร้องขอราคาพิเศษที่ยกเลิก/ปฏิเสธทั้งหมด {{ showCountRequest }} รายการ</span>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-data-table
           :headers="headers"
           :items="ListData"
           :search="search"
           style="width:100%;"
           height="100%"
           :page.sync="page"
           @pagination="countRequest"
           no-results-text="ไม่พบรายการร้องขอพิเศษ"
           no-data-text="ไม่มีรายการร้องขอพิเศษ"
           :update:items-per-page="itemsPerPage"
           :items-per-page="5"
           class="elevation-1 mt-4"
           v-if="disableTable === true"
          >
            <template v-slot:[`item.created_at`]="{ item }">
              {{ new Date(item.created_at).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' }) }}
            </template>
            <template v-slot:[`item.total_price`]="{ item }">
              <span>{{ Number(item.total_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            </template>
            <template v-slot:[`item.payment_transaction_number`]="{ item }">
              <span>{{ item.status ===  'success' ? item.payment_transaction_number : item.status === 'success_qu' ? item.qu_number : '-' }}</span>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'success_qu'">
                <v-chip class="ma-2" color="#E3F2FD" text-color="#42A5F5">สร้างใบเสนอราคาแล้ว</v-chip>
              </span>
              <span v-else-if="item.status === 'active'">
                <v-chip class="ma-2" color="#F1F8E9" text-color="#8BC34A">อนุมัติ</v-chip>
              </span>
              <span v-else-if="item.status === 'success'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">สร้างรายการสั่งซื้อแล้ว</v-chip>
              </span>
              <span v-else-if="item.status === 'waiting_approve'">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
              </span>
              <span v-else-if="item.status === 'edited'">
                <v-chip class="ma-2" color="#f7c5ad" text-color="#f50">แก้ไข</v-chip>
              </span>
              <span v-else-if="item.status === 'inactive'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ยกเลิก</v-chip>
              </span>
              <span v-else-if="item.status === 'reject'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ปฏิเสธ</v-chip>
              </span>
            </template>
            <template v-slot:[`item.manages`]="{ item }">
              <v-row>
                <span class="ma-2"
                  style="line-height: 22px; color:  #27AB9C; cursor: pointer"
                  @click="RequestDetail(item.special_price_code, item.status)" v-if="item.status !== 'inactive' && item.status !== 'reject' ">รายละเอียด <v-icon size="15" color="#27AB9C">
                    mdi-chevron-right</v-icon>
                </span>
              </v-row>
            </template>
          </v-data-table>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการร้องขอราคาพิเศษ</b></h2>
        </v-col>
      </v-row>
    </v-card-text>

    <!-- Modal show detail of request -->
    <v-dialog v-model="ModalDetailRequest" width="730px" persistent>
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">ร้องขอราคาพิเศษ</font>
          </span>
          <v-btn icon dark @click="ModalDetailRequest = !ModalDetailRequest">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row class="mt-2">
            <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12" md="12">
                  <v-row :style="MobileSize ? 'max-height: 65px' : 'max-height: 40px;'">
                    <v-col cols="12" md="3">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขร้องขอ :</p>
                    </v-col>
                    <v-col cols="12" md="8" class="px-0">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>{{DetailList.special_price_code}}</b></p>
                    </v-col>
                  </v-row>
                  <v-row :style="MobileSize ? 'max-height: 65px' : 'max-height: 40px;'">
                    <v-col cols="12" md="3">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">บริษัท :</p>
                    </v-col>
                    <v-col cols="12" md="8" class="px-0">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>{{DetailList.name_th}}</b></p>
                    </v-col>
                  </v-row>
                  <v-row :style="MobileSize ? 'max-height: 65px' : 'max-height: 40px;'">
                    <v-col cols="12" md="3">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ผู้ร้องขอ :</p>
                    </v-col>
                    <v-col cols="12" md="8" class="px-0">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>{{ DetailList.c_firstname}} {{ DetailList.c_lastname}}</b></p>
                    </v-col>
                  </v-row>
                  <v-row :style="MobileSize ? 'max-height: 65px' : 'max-height: 40px;'">
                    <v-col cols="12" md="3">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">วันที่ร้องขอ :</p>
                    </v-col>
                    <v-col cols="12" md="8" class="px-0">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                        <b>{{ new Date(DetailList.created_at).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' }) }}</b>
                      </p>
                    </v-col>
                  </v-row>
                  <v-row :style="MobileSize ? 'max-height: 65px' : 'max-height: 40px;'">
                    <v-col cols="12" md="3">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">สถานะ :</p>
                    </v-col>
                    <v-col cols="12" md="8" class="px-0">
                      <p v-if="DetailList.status === 'waiting_approve'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>รออนุมัติ</b></p>
                      <p v-else-if="DetailList.status === 'active' || DetailList.status === 'success'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>อนุมัติ</b></p>
                      <p v-else-if="DetailList.status === 'success_qu'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>สร้างใบเสนอราคาแล้ว</b></p>
                      <p v-else-if="DetailList.status === 'edited'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>แก้ไข</b></p>
                      <p v-else-if="DetailList.status === 'inactive'" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"><b>ยกเลิก</b></p>
                    </v-col>
                  </v-row>
                  <v-row :style="MobileSize ? 'max-height: 65px' : 'max-height: 40px;'">
                    <v-col cols="12" md="3">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อัปเดตข้อมูลล่าสุด :</p>
                    </v-col>
                    <v-col cols="12" md="8" class="px-0">
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">
                        <b>{{ new Date(DetailList.updated_at).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' }) }}</b>
                      </p>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รายการสินค้า {{ProductList.length }} รายการ</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12">
                  <v-data-table
                  :headers="headersProduct"
                  :items="ProductList"
                  style="width:100%;"
                  height="100%"
                  no-results-text="ไม่พบรายการร้องขอพิเศษ"
                  no-data-text="ไม่มีรายการร้องขอพิเศษ"
                  class="elevation-1 mt-4 row-height-180"
                  hide-default-footer
                  >
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <v-row v-if="!MobileSize" class="px-0" justify-center>
                      <v-col md="3" class="px-0 mx-auto" justify-center>
                        <v-img :src="`${item.product_image}`" v-if="item.product_image !== ''" max-height="60px" max-width="60px" class="mt-3"/>
                        <v-img src="@/assets/NoImage.png" v-else max-height="60px" max-width="60px"/>
                      </v-col>
                      <v-col md="7" class="px-0 ml-0 my-auto">
                        <p class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{item.product_name}}<br/></p>
                        <p class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-if="item.key_1_value !== null && item.key_1_value !== ''">{{item.key_1_value}} : {{item.product_attribute_detail.attribute_priority_1}}<br/></p>
                        <p class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-if="item.key_2_value !== null && item.key_1_value !== ''">{{item.key_2_value}} : {{item.product_attribute_detail.attribute_priority_2}}<br/></p>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    <span>{{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                  <template v-slot:[`item.net_price`]="{ item }">
                    <span>{{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                  </v-data-table>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col align="right">
                  <span>ราคารวมทั้งสิ้น (ไม่รวม % ภาษี)</span>
                  <span> {{ Number(DetailList.total_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
              </v-row>
              <v-row dense v-if="DetailList.status === 'waiting_approve' || DetailList.status === 'edited'">
                <v-col align="right">
                  <v-btn dense dark outlined color="#F5222D" class="pl-7 pr-7 mt-2" @click="ConfirmCancel()">
                    ปฏิเสธ
                  </v-btn>
                  <v-btn dense dark outlined color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--tex" @click="Edit()">
                    แก้ไข
                  </v-btn>
                  <v-btn v-if="DetailList.status !== 'edited'" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="ConfirmApprove()">
                    อนุมัติ
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Modal edit detail -->
    <v-dialog v-model="ModalEditRequest" width="1000px" persistent>
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">แก้ไขการร้องขอราคาพิเศษ</font>
          </span>
          <v-btn icon dark @click="closeEditModal()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row class="mt-2">
            <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12" md="6">
                  <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
                  <span class="ml-3" style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ร้านค้า {{DetailList.name_th}}</span>
                </v-col>
                <v-col cols="12" md="6" align="right">
                  <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขร้องขอ : {{spe_price_id}}</span>
                </v-col>
                <v-col cols="12" md="4">
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รายการสินค้า</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="12">
                  <v-data-table
                  :headers="headersEditProduct"
                  :items="ProductListEdit"
                  style="width:100%;"
                  height="100%"
                  no-results-text="ไม่พบรายการร้องขอพิเศษ"
                  no-data-text="ไม่มีรายการร้องขอพิเศษ"
                  class="elevation-1 mt-4"
                  hide-default-footer
                  >
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <v-row v-if="!MobileSize" class="px-0" justify-center>
                      <v-col md="3" class="px-0 mx-auto" justify-center>
                        <v-img width="60px" height="60px" :src="`${item.product_image}`" v-if="item.product_image !== ''"/>
                        <v-img width="60px" height="60px" src="@/assets/NoImage.png" v-else />
                      </v-col>
                      <v-col md="7" class="px-0 ml-0 my-auto">
                        <p class="mb-0" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{item.product_name}}</p>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    <v-card outlined height="auto" width="100%">
                      <v-card-text class="py-2 px-1">
                        <v-row justify="center" class="py-2 px-3">
                          <v-col cols="12" md="12" class="pa-0" align="center">
                            <v-text-field
                              v-model="item.price"
                              @change="changePriceSwal(item, 'UPDATE')"
                              dense
                              hide-details
                              solo flat
                              class="pa-0"
                              oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </template>
                  <template v-slot:[`item.quantityEdit`]="{ item }">
                    <v-card outlined height="auto" width="100%">
                      <v-card-text class="py-2 px-1">
                        <v-row justify="center" class="py-2 px-3">
                          <v-col cols="12" md="4" class="pa-0" align="center">
                            <v-hover v-slot="{ hover }">
                              <v-btn :disabled="item.quantity <= 1" @click="item.quantity > 1 ? item.quantity-- : '', item.quantity > 1 ? changeQuantitySwal(item, 'UPDATE') : ''" elevation="0" class="mt-1" :color="hover ? '#A1A1A1' : '#E6E6E6'" style="min-width: 26px !important; height: 30px !important; padding: 0px 8px !important;">
                                <v-icon class="px-0" color="white" small>mdi-minus</v-icon>
                              </v-btn>
                            </v-hover>
                          </v-col>
                          <v-col cols="12" md="4" class="pa-0" align="center">
                            <v-text-field
                              v-model="item.quantity"
                              @change="changeQuantitySwal(item, 'UPDATE')"
                              dense
                              solo flat
                              class="pa-0"
                              hide-details
                              oninput="this.value = parseInt(this.value.replace(/[^\d]/g, '').replace(/(\..*)\./g, '$1'))"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12" md="4" sm="4" class="pa-0" align="center">
                            <v-hover v-slot="{ hover }">
                              <v-btn :disabled="item.quantity > parseFloat(item.stock)" @click="item.quantity++, changeQuantitySwal(item, 'UPDATE')" elevation="0" class="mt-1" :color="hover ? '#A1A1A1' : '#E6E6E6'" style="min-width: 28px !important; height: 30px !important; padding: 0px 8px !important;">
                                <v-icon class="px-0" color="white" small>mdi-plus</v-icon>
                              </v-btn>
                            </v-hover>
                            <!-- <v-btn @click="item.quantity++, changeQuantitySwal(item, 'UPDATE')" elevation="0" class="mt-1" color="#E6E6E6" dark style="min-width: 28px !important; height: 30px !important; padding: 0px 8px !important;">
                              <v-icon class="px-0" small>mdi-plus</v-icon>
                            </v-btn> -->
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </template>
                  <template v-slot:[`item.net_price`]="{ item }">
                    <span>{{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                  <template v-slot:[`item.action`]="{ item, index }">
                    <v-row v-if="!MobileSize">
                      <v-col cols="12">
                        <v-card elevation="1" width="100%" height="100%">
                        <v-btn color="#27AB9C" icon @click="deleteItem(item, index)" :disabled="ProductListEdit.length <= 1 ? true : false">
                          <v-icon class="button-edit-delete">mdi-delete-outline</v-icon>
                        </v-btn>
                        </v-card>
                      </v-col>
                    </v-row>
                  </template>
                  </v-data-table>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col align="right">
                  <span>ราคารวมทั้งสิ้น (ไม่รวม % ภาษี)</span>
                  <span> {{ Number(this.total_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col align="right">
                  <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="ConfirmEdit()">
                    บันทึก
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- dialog comfirm Approve -->
    <v-dialog  v-model="dialogApprove" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">อนุมัติการร้องขอราคาพิเศษ</font></span>
          <v-btn
            icon
            dark
            @click="dialogApprove = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/><br/>
        <v-card-text >
          <span>
            คุณต้องการอนุมัติคำขอนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeDialog('Approve')">ยกเลิก</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Approve()">ตกลง</v-btn>
       </v-container>
      </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- dialog comfirm cancle -->
    <v-dialog  v-model="dialogCancel" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">ปฏิเสธการร้องขอ</font></span>
          <v-btn
            icon
            dark
            @click="dialogCancel = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/><br/>
        <v-card-text >
          <span>
            คุณต้องการปฏิเสธคำขอนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeDialog('Cancel')">ยกเลิก</v-btn>
        <v-btn dense  color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Cancle()">ตกลง</v-btn>
       </v-container>
      </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- dialog comfirm Edit -->
    <v-dialog  v-model="dialogEdit" width="400" persistent>
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9"  dark dense>
          <span class="flex text-center ml-5" style="font-size:20px"><font color="#27AB9C">แก้ไขการร้องขอ</font></span>
          <v-btn
            icon
            dark
            @click="dialogEdit = false"
            >
            <v-icon  color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br/><br/>
        <v-card-text >
          <span>
            คุณต้องการแก้ไขคำขอนี้ ใช่ หรือไม่
          </span>
        </v-card-text>
        <v-card-actions >
       <v-container >
        <v-btn dense  dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeDialog('Edit')">ยกเลิก</v-btn>
        <v-btn dense :loading="loading" color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="Save()">ตกลง</v-btn>
       </v-container>
      </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      loading: false,
      shopDetail: '',
      spe_price_id: '',
      total_price: '',

      // modal,dialog,table
      disableTable: false,
      ModalDetailRequest: false,
      ModalEditRequest: false,
      dialogApprove: false,
      dialogCancel: false,
      dialogEdit: false,

      // tab bar------------------------------------------------------------------------------------------
      checkQuantity: false,
      showCountRequest: 0,
      StateStatus: 0,
      countall: 0,
      countWaiting: 0,
      countActive: 0,
      countEdited: 0,
      countCancel: 0,
      countSuccess: 0,
      countSuccessQu: 0,

      // table list special price---------------------------------------------
      search: '',
      page: 1,
      itemsPerPage: 10,
      requestList: [],
      ListData: [],
      headers: [
        { text: 'ชื่อบริษัท', value: 'name_th', sortable: false, class: 'backgroundTable fontTable--text', width: 270 },
        { text: 'หมายเลขร้องขอ', value: 'special_price_code', sortable: false, class: 'backgroundTable fontTable--text', width: 200 },
        { text: 'วันที่ร้องขอ', value: 'created_at', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', width: 180 },
        { text: 'ราคารวม', value: 'total_price', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', width: 120 },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเลขอ้างอิง', value: 'payment_transaction_number', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', width: 230 },
        { text: 'การจัดการ', value: 'manages', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', width: 150 }
      ],

      // table detail special price--------------------------------------------------
      DetailList: [],
      ProductList: [],
      DataBacklog: [],
      headersProduct: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', width: '40%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', width: '20%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', width: '20%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'net_price', width: '20%', sortable: false, class: 'backgroundTable fontTable--text' }
      ],

      // table Edit detail special price--------------------------------------------------
      ProductListEdit: '',
      EditData: [],
      headersEditProduct: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', width: '40%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', width: '25%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantityEdit', width: '27%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'net_price', width: '18%', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: '', value: 'action', width: '10%', sortable: false, class: 'backgroundTable fontTable--text' }
      ],

      // Calculate Price in Edit Mode
      disabledinput_plus: false
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('checkpath')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    this.getListRequest()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    StateStatus (val) {
      // console.log('state is', val)
      if (val === 0) {
        this.ListData = this.requestList.all !== undefined ? this.requestList.all : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.ListData = this.requestList.waiting_approve !== undefined ? this.requestList.waiting_approve : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.ListData = this.requestList.active !== undefined ? this.requestList.active : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.ListData = this.requestList.success_qu !== undefined ? this.requestList.success_qu : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 4) {
        this.ListData = this.requestList.success !== undefined ? this.requestList.success : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 5) {
        this.ListData = this.requestList.edited !== undefined ? this.requestList.edited : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 6) {
        this.ListData = this.requestList.cancel !== undefined ? this.requestList.cancel : []
        // console.log('data', this.ListData)
        if (this.ListData.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    getRequest (item) {
      this.StateStatus = item
      // i dnk
      this.page = 1
    },
    async getListRequest () {
      var data = {
        seller_shop_id: this.shopDetail.id
      }
      // console.log('send data 2 API list', data)
      await this.$store.dispatch('actionsListSpecialPriceSeller', data)
      var response = await this.$store.state.ModuleShop.stateListSpecialPriceSeller
      if (response.message === 'Success' || response.message === 'ไม่พบข้อมูล') {
        this.requestList = response.data
        if (this.requestList !== '') {
          this.ListData = this.requestList
          this.countall = this.ListData.all.length
          this.countWaiting = this.ListData.waiting_approve.length
          this.countActive = this.ListData.active.length
          this.countEdited = this.ListData.edited.length
          this.countCancel = this.ListData.cancel.length
          this.countSuccess = this.ListData.success.length
          this.countSuccessQu = this.ListData.success_qu.length
          if (this.StateStatus === 0) {
            this.ListData = this.requestList.all
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            this.ListData = this.requestList.waiting_approve
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            this.ListData = this.requestList.active
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 3) {
            this.ListData = this.requestList.success_qu
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 4) {
            this.ListData = this.requestList.success
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 5) {
            this.ListData = this.requestList.edited
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 5) {
            this.ListData = this.requestList.inactive
            if (this.ListData.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          }
        } else {
          this.disableTable = false
        }
      }
      // console.log('send data 2 API list', data)
      // console.log('res data fm API list', response)
    },
    async RequestDetail (item, status) {
      this.spe_price_id = item
      if (status !== 'inactive') {
        this.DataBacklog = []
        var dataDetail = {
          spe_price_id: item
        }
        await this.$store.dispatch('actionsDetailSpecialPrice', dataDetail)
        var response = await this.$store.state.ModuleShop.stateDetailSpecialPrice
        if (response.message === 'Success') {
          // console.log(response.message)
          this.DetailList = response.data[0]
          this.total_price = this.DetailList.total_price
          this.ProductList = response.data[0].product_list
          for (var i = 0; i < this.ProductList.length; i++) {
            this.ProductList[i].maxValue = this.ProductList[i].price
            this.ProductList[i].oldQuantity = this.ProductList[i].quantity
          }
          this.ModalDetailRequest = true
        } else {
          this.DetailList = []
          this.ProductList = []
          this.spe_price_id = ''
        }
        // console.log('send data 2 API Detail', dataDetail)
        // console.log('res data fm API Datail', response)
      }
    },
    async ConfirmApprove () {
      this.dialogApprove = !this.dialogApprove
    },
    async Approve () {
      var dataDetail = {
        status: 'active',
        spe_price_id: this.spe_price_id
      }
      await this.$store.dispatch('actionsApproveSpecialPrice', dataDetail)
      var response = await this.$store.state.ModuleShop.stateApproveSpecialPrice
      if (response.message === 'Update success') {
        this.$swal.fire({ text: 'อนุมัติคำขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.dialogApprove = !this.dialogApprove
        this.ModalDetailRequest = !this.ModalDetailRequest
        this.getListRequest()
        this.spe_price_id = ''
      } else {
        var msg = response.data.error
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'อนุมัติไม่สำเร็จ' + msg
        })
        this.dialogApprove = !this.dialogApprove
      }
      // console.log('send data 2 Approve API list', dataDetail)
      // console.log('res data fm Approve API list', response)
    },
    async ConfirmCancel () {
      this.dialogCancel = !this.dialogCancel
    },
    async Cancle () {
      var dataDetail = {
        status: 'reject',
        spe_price_id: this.spe_price_id
      }
      await this.$store.dispatch('actionsApproveSpecialPrice', dataDetail)
      var response = await this.$store.state.ModuleShop.stateApproveSpecialPrice
      if (response.message === 'Update success') {
        this.$swal.fire({ text: 'ปฏิเสธรายการร้องขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.dialogCancel = !this.dialogCancel
        this.ModalDetailRequest = !this.ModalDetailRequest
        this.getListRequest()
        this.spe_price_id = ''
      } else {
        var msg = response.data.error
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'ปฏิเสธรายการร้องขอไม่สำเร็จ' + msg
        })
        this.dialogCancel = !this.dialogCancel
      }
      // console.log('send data 2 Approve/Reject API list', dataDetail)
      // console.log('res data fm Approve/Reject API list', response)
    },
    async Edit () {
      // var CleanData = []
      for (var i = 0; i < this.ProductList.length; i++) {
        // console.log(this.ProductList[i].price.toString().indexOf('.'))
        if (this.ProductList[i].price.toString().indexOf('.') === -1) {
          this.ProductList[i].price = parseFloat(this.ProductList[i].price).toFixed(2)
        }
      }
      this.ProductListEdit = this.ProductList
      this.ModalEditRequest = !this.ModalEditRequest
    },
    async changeQuantitySwal (item, strkey) {
      // console.log('product list', this.DataBacklog, this.DetailList)
      // if (item.quantity === '' || parseFloat(item.quantity) === 0 || (parseFloat(item.quantity) >= parseFloat(item.min_per_order) && parseFloat(item.quantity) <= parseFloat(item.max_per_order))) {
      if (item.quantity === '' || parseFloat(item.quantity) === 0 || parseFloat(item.quantity) < parseFloat(item.stock) || item.quantity === 'NaN') {
        if (strkey === 'UPDATE') {
          if (parseFloat(item.quantity) === 0 || parseFloat(item.quantity) < 0 || item.quantity === '' || item.quantity === 'NaN') {
            // const msg = 'สินค้าชิ้นนี้จำกัดการสั่งซื้อต่ำสุดที่ ' + item.min_per_order + ' และการสั่งซื้อสูงสุดที่ ' + item.max_per_order
            const msg = 'จำนวนสินค้าควรเป็นจำนวนบวกและมีค่ามากกว่า 0 และ ไม่ใช่ค่าว่าง'
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
            this.ModalEditRequest = true
            item.quantity = 1
            await this.updateCartItem(item, strkey)
          } else if (parseFloat(item.quantity) > parseFloat(item.stock)) {
            const msg = 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว'
            this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
            this.ModalEditRequest = true
            item.quantity = ''
            // await this.updateCartItem(item, strkey)
          } else {
            // console.log('quantity')
            await this.updateCartItem(item, strkey)
          }
        }
      } else {
        const msg = 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว'
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
        item.quantity = item.stock
        // await this.updateCartItem(item, strkey)
        // if (parseFloat(item.quantity) <= parseFloat(item.min_per_order)) {
        //   const msg = 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณลดจำนวนสินค้าถึงจำนวนที่กำหนดแล้ว'
        //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
        //   item.quantity = 1
        // } else if (parseFloat(item.quantity) >= parseFloat(item.max_per_order)) {
        //   const msg = 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว'
        //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
        //   item.quantity = item.max_per_order
        // }
      }
    },
    async changePriceSwal (item, strkey) {
      // console.log('changePriceSwal', item.price)
      if (strkey === 'UPDATE') {
        if (parseFloat(item.price) === 0 || parseFloat(item.price) < 0 || item.price === '') {
          // console.log('quantity == empthy/0')
          const msg = 'ราคาสินค้าควรมีราคามากกว่า 0 และ ไม่ใช่ค่าว่าง'
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
          this.ModalEditRequest = !this.ModalEditRequest
          item.price = parseFloat(item.maxValue).toFixed(2)
        } else {
          // if (item.price > item.maxValue) {
          //   const msg = 'ราคาสินค้าไม่ควรเกินกว่าราคาตั้งต้น'
          //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
          //   item.price = item.maxValue
          //   this.ModalEditRequest = !this.ModalEditRequest
          // } else {
          //   console.log('quantity')
          //   await this.updateCartItem(item, strkey)
          // }
          await this.updateCartItem(item, strkey)
        }
      }
    },
    deleteItem (item, index) {
      // console.log('deleteCartItem', item)
      this.$swal.fire({
        icon: 'warning',
        text: 'คุณต้องการที่จะลบสินค้านี้หรือไม่?',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        cancelButtonColor: '#FF3F00',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.ProductListEdit.splice(index, 1)
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    async updateCartItem (item, strkey) {
      var newProductList = []
      if (strkey === 'UPDATE') {
        for (let i = 0; i < this.ProductListEdit.length; i++) {
          newProductList[i] = {
            product_id: this.ProductListEdit[i].product_id,
            attribute_id: this.ProductListEdit[i].product_attribute_detail.product_attribute_id,
            quantity: this.ProductListEdit[i].quantity,
            price: parseFloat(this.ProductListEdit[i].price).toFixed(2)
          }
        }
      }
      const data = {
        product_list: newProductList
      }
      this.EditData = newProductList
      await this.$store.dispatch('actionsCalculateSpecialPrice', data)
      const res = await this.$store.state.ModuleShop.stateCalculateSpecialPrice
      if (res.code === 200) {
        if (strkey === 'UPDATE') {
          // console.log('hello1')
          if (parseFloat(item.quantity) === 0) {
            this.shopNameList.data = []
            this.selectedRowKeys = []
          } else {
            // console.log('hello2')
            item.quantity >= item.stock ? this.disabledinput_plus = true : this.disabledinput_plus = false
          }
          // console.log(this.ProductListEdit)
          for (var j = 0; j < this.ProductListEdit.length; j++) {
            this.ProductListEdit[j].price = parseFloat(res.product_list[j].price).toFixed(2)
            this.ProductListEdit[j].quantity = res.product_list[j].quantity
            this.ProductListEdit[j].net_price = parseFloat(res.product_list[j].total_price).toFixed(2)
          }
          this.total_price = parseFloat(res.total_price_no_vat).toFixed(2)
        }
        // console.log('send data 2 API Cal', data)
        // console.log('res data fm API Cal', res)
      }
    },
    async ConfirmEdit () {
      this.dialogEdit = !this.dialogEdit
    },
    async Save () {
      // console.log(this.ProductListEdit)
      this.loading = true
      var setEditData = []
      for (let i = 0; i < this.ProductListEdit.length; i++) {
        setEditData.push({
          product_id: this.ProductListEdit[i].product_id,
          attribute_id: this.ProductListEdit[i].product_attribute_detail.product_attribute_id,
          quantity: parseInt(this.ProductListEdit[i].quantity),
          price: parseFloat(this.ProductListEdit[i].price).toFixed(2),
          product_image: this.ProductListEdit[i].product_image
        })
      }
      var dataDetail = {
        spe_price_id: this.spe_price_id,
        product_list: setEditData
      }
      // console.log('EditSave', dataDetail)
      await this.$store.dispatch('actionsEditSpecialPrice', dataDetail)
      var response = await this.$store.state.ModuleShop.stateEditSpecialPrice
      // console.log(response.data)
      if (response.message === 'Update success') {
        this.loading = false
        this.$swal.fire({ text: 'แก้ไขคำขอสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.ModalEditRequest = !this.ModalEditRequest
        this.ModalDetailRequest = !this.ModalDetailRequest
        this.dialogEdit = !this.dialogEdit
        this.spe_price_id = ''
        this.total_price = ''
        this.getListRequest()
      } else if (response.message === 'Can not buy more than stock.') {
        this.loading = false
        this.dialogEdit = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'จำนวนสินค้าที่ร้องขอเกินจำนวนสต๊อกสินค้า'
        })
      } else if (response.data.message === 'Can not buy more than stock.') {
        this.loading = false
        this.dialogEdit = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'จำนวนสินค้าที่ร้องขอเกินจำนวนสต๊อกสินค้า'
        })
      } else if (response.data.message === 'request special price can not more than or equal products price') {
        this.loading = false
        this.dialogEdit = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: response.message !== undefined ? response.message : response.data.message
        })
      } else if (response.data.message === 'parameter error') {
        this.loading = false
        const msg = 'ราคาสินค้าไม่ควรเกินกว่าราคาตั้งต้น'
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
        this.dialogEdit = false
        this.quantity = this.maxValue
      } else if (response.result === 'parameter invalid') {
        this.loading = false
        const msg = response.message
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
        this.dialogEdit = false
        this.quantity = this.maxValue
      } else if (response.result === 'ERROR') {
        this.loading = false
        const msg = response.message
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: msg })
        this.dialogEdit = false
        this.quantity = this.maxValue
      } else {
        this.loading = false
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: response.data.message
        })
        this.ModalEditRequest = !this.ModalEditRequest
        this.ModalDetailRequest = !this.ModalDetailRequest
        this.dialogEdit = !this.dialogEdit
      }
      // console.log('send data 2 Edit API list', dataDetail)
      // console.log('res data fm Edit API list', response)
    },
    async closeDialog (item) {
      // console.log(item)
      if (item === 'Approve') {
        this.dialogApprove = !this.dialogApprove
      } else if (item === 'Cancel') {
        this.dialogCancel = !this.dialogCancel
      } else if (item === 'Edit') {
        this.dialogEdit = !this.dialogEdit
      }
    },
    async closeEditModal () {
      this.ModalEditRequest = !this.ModalEditRequest
      this.RequestDetail(this.spe_price_id)
    },
    // dnt kn
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    }
  }
}
</script>
