<template>
  <div>
    <h1>ตัวอย่างการไลฟ์</h1>
    <v-row justify="center" style="min-height: 80vh">
      <v-col cols="8" style="margin-top: 10px;" align="center">
        <v-icon small class="mr-1">mdi-eye</v-icon>0
        <!-- <video ref="videoElement" :id="track.sid" width="100%" style="transform: scaleX(-1)"></video>
        <audio ref="audioElement" autoplay></audio> -->
        <div style="background-color: #000000; height: 313px;" class="d-flex justify-center align-center">
          <span class="white--text" style="font-size: 24px;">ไม่มีการเชื่อมต่อห้องไลฟ์</span>
        </div>
        <v-row dense no-gutters class="pa-1">
          <v-btn icon>
            <v-icon v-if="play">mdi-pause</v-icon>
            <v-icon v-else>mdi-play</v-icon>
          </v-btn>

          <v-btn icon class="ml-auto">
            <v-icon>mdi-fullscreen</v-icon>
          </v-btn>
        </v-row>
        <v-btn
          disabled
          outlined
          style="border: none;"
          class="pt-4 pb-4"
        >
          <v-icon color="#707070" disabled>mdi-phone-hangup</v-icon>
        </v-btn>
      </v-col>

      <v-col cols="4">
        <div style="background-attachment: scroll;">
          <v-card width="530px" height="80vh" rounded style="display: flex !important; flex-direction: column;">
            <v-card-title style="font-weight: 700; font-size: 18px; line-height: 30px; color:#333333;">
              <p>Chat Room</p>
            </v-card-title>
            <v-card-subtitle><v-divider></v-divider></v-card-subtitle>
            <v-card-text style="flex-grow: 1; overflow: auto;">
              <div>
                <v-card height="80vh" width="100%" color="#FAFAFA" style="margin-bottom: 20px; border-radius: 10px; box-shadow:0 !important;" class="d-flex" elevation="0">
                  <v-card-text>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
            <v-card-text>
              <v-row style="margin-top: 10px;">
                <v-text-field
                  v-model="message"
                  dense
                  hide-details
                  outlined
                  style="max-width: 250px;"
                  readonly
                >
                </v-text-field>
                <v-btn icon>
                  <v-icon>mdi-send</v-icon>
                </v-btn>
                <!-- <v-btn color="primary" :disabled="message == ''" class="ml-2">Send</v-btn> -->
              </v-row>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>

export default {
  data () {
    return {
      videoElement: null,
      APPLICATION_SERVER_URL: '',
      LIVEKIT_URL: '',
      listMessage: [],
      participantCount: 0,
      play: false,
      message: ''
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative;
  background: #3b3b3b;
  aspect-ratio: 16/9;
  border-radius: 6px;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
}

.video-container .participant-data {
  position: absolute;
  top: 0;
  left: 0;
}

.participant-data p {
  background: #f8f8f8;
  margin: 0;
  padding: 0 5px;
  color: #777777;
  font-weight: bold;
  border-bottom-right-radius: 4px;
}

/* Media Queries */
@media screen and (max-width: 480px) {
  .video-container {
    aspect-ratio: 9/16;
  }
}
</style>
