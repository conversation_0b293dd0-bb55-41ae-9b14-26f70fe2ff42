<template>
  <v-container>
    <v-row v-if="MobileSize">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card class="mt-6" max-height="100%" height="100%" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item-group
              color="#27AB9C"
            >
              <v-list-item
                v-for="(item) in filteredItems"
                :key="item.key"
                :disabled="item.disable"
                @click="changePage(item.path)"
              >
                <v-list-item-icon>
                  <v-icon >{{ item.action }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
                <v-list-item-action v-if="item.title !== 'บัญชีของฉัน'">
                  <v-icon>mdi-chevron-right</v-icon>
                </v-list-item-action>
              </v-list-item>
            </v-list-item-group>
            <div v-if="RoleUser === 'sale_order_no_JV'">
              <v-list-item-group
                color="#27AB9C"
              >
                <v-list-item
                v-for="(item) in itemslistSaleOrder"
                  :key="item.key"
                  :disabled="item.disable"
                  @click="changePage(item.path)"
                >
                  <v-list-item-icon>
                    <v-icon >{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'จัดการรายการสั่งซื้อ Sales Order'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </div>
            <div v-else-if="RoleUser === 'ext_buyer'">
              <v-list-item-group
                color="#27AB9C"
              >
                <v-list-item
                  v-for="(item) in itemslist"
                  :key="item.key"
                  :disabled="item.disable"
                  @click="changePage(item.path)"
                >
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'รายการสั่งซื้อ'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
                <v-list-item
                  v-for="(item) in itemsAffiliate"
                  :key="item.key"
                  :disabled="item.disable"
                  @click="changePage(item.path)"
                >
                  <v-list-item-icon>
                    <v-icon >{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'โปรแกรม Affiliate'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </div>
            <!-- <div v-else>
              <v-list-item-group
                color="#27AB9C"
              >
                <v-list-item
                v-for="(item, i) in itemsAffiliate"
                  :key="i"
                  :disabled="item.disable"
                  @click="changePage(item.path)"
                >
                  <v-list-item-icon>
                    <v-icon>{{ item.action }}</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                  <v-list-item-action v-if="item.title !== 'affiliate'">
                    <v-icon>mdi-chevron-right</v-icon>
                  </v-list-item-action>
                </v-list-item>
              </v-list-item-group>
            </div> -->
          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9">
        <v-main style="padding: 0px;">
          <v-container>
            <v-card max-height="100%" height="100%" class="mt-3" style="border-radius: 8px; border: 1px solid #F2F2F2;">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
// components: {
//   UserProfile: () => import('@/components/UserProfile/UserProfileUI')
// },
  created () {
    this.$EventBus.$emit('getPath')
    this.$EventBus.$on('changeNavAccount', this.changeNavAccount)
    // this.$EventBus.$emit('CheckFooter')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('roleUser')) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RoleUser = 'ext_buyer'
    }
    this.getConsent()
    this.changeNavAccount()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    selectedItem (val) {
      // console.log(val)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userprofile' }).catch(() => {})
      }
    }
  },
  computed: {
    filteredItems () {
      if (this.RoleUser !== 'ext_buyer') {
        return this.items.filter(item => item.title === 'บัญชีของฉัน' || item.title === 'ข้อมูลของฉัน')
      }
      return this.items
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  data () {
    return {
      RoleUser: '',
      selectedItem: 0,
      select: 0,
      items: [
        { key: 0, action: 'mdi-account', title: `${this.$t('MenuBuyer.titleUserProfile')}`, disable: true },
        { key: 1, action: '', title: `${this.$t('MenuBuyer.menuMyProfile')}`, path: 'userprofileDetail', disable: false },
        { key: 2, action: '', title: `${this.$t('MenuBuyer.menuBankAccount')}`, path: 'BankAccountUserMobile', disable: false },
        // { key: 1, title: 'เปลี่ยนรหัสผ่าน', path: 'changePassword' },
        { key: 3, action: '', title: `${this.$t('MenuBuyer.menuShippingAddress')}`, path: 'addressProfileMobile', disable: false },
        { key: 4, action: '', title: `${this.$t('MenuBuyer.menuFavoriteProduct')}`, path: 'favoriteMobile', disable: false },
        { key: 5, action: '', title: `${this.$t('MenuBuyer.menuCouponPoint')}`, path: 'MyCouponsAndPointsMobile', disable: false },
        { key: 6, action: '', title: `${this.$t('MenuBuyer.FriendGetFriends')}`, path: 'FriendGetFriendsMobile', disable: false },
        { key: 7, action: '', title: `${this.$t('MenuBuyer.menuMyChat')}`, path: 'chatAll', disable: false }
        // { key: 5, action: '', title: 'โปรแกรม Affiliate', path: 'buyerAffiliateMobile', disable: false }
      ],
      itemslist: [
        { key: 6, action: 'mdi-file-document-outline', title: `${this.$t('MenuBuyer.titleManageOrders')}`, disable: true },
        { key: 7, action: '', title: `${this.$t('MenuBuyer.menuMyOrders')}`, path: 'pobuyerProfileMobile', disable: false },
        { key: 8, action: '', title: `${this.$t('MenuBuyer.menuProfileRecord')}`, path: 'pobuyerProfileRecordMobile', disable: false },
        // { key: 7, action: '', title: 'รายการคืนสินค้าของฉัน', path: 'refundProductMobile', disable: false },
        { key: 9, action: '', title: `${this.$t('MenuBuyer.menuReview')}`, path: 'reviewBuyerMobile', disable: false }
      ],
      // itemsAffiliate: [
      //   { key: 9, action: 'mdi-storefront', title: 'โปรแกรม Affiliate', disable: true },
      //   { key: 10, action: '', title: 'โปรแกรม Affiliate ', path: 'buyerAffiliateMobile', disable: false },
      //   { key: 11, action: '', title: 'การชำระเงิน', path: 'editPayMobile', disable: false },
      //   { key: 12, action: '', title: 'บัญชีโซเชียลมีเดีย', path: 'editSocailMobile', disable: false }
      // ],
      itemsAffiliate: [],
      itemslistSaleOrder: [
        { key: 10, action: 'mdi-file-document-outline', title: `${this.$t('MenuBuyer.titleManageSales')}`, disable: true },
        { key: 11, action: '', title: 'รายการสั่งซื้อของฉัน', path: 'ListOrderBySalesMobile', disable: false },
        { key: 12, action: '', title: 'ประวัติรายการสั่งซื้อของฉัน', path: 'pobuyerProfileRecordMobile', disable: false }
        // { key: 7, action: '', title: 'รายการคืนสินค้าของฉัน', path: 'refundProductMobile', disable: false },
        // { key: 8, action: '', title: 'การประเมินความพึงพอใจ', path: 'reviewBuyerMobile', disable: false }
      ]
      // itemAffiliate: [
      //   { key: 4, action: 'mdi-storefront-outline', title: 'affiliate', disable: true },
      //   { key: 5, action: '', title: 'การแชร์ของฉัน', path: 'SharedBuyerAffiliateMobile', disable: false },
      //   { key: 6, action: '', title: 'รายงานการคลิก', path: 'ClickBuyerAffiliateMobile', disable: false },
      //   { key: 8, action: '', title: 'รายงานคำสั่งซื้อ', path: 'orderedBuyerAffilateMobile', disable: false }
      // ]
      // {
      //   action: 'mdi-file-document-outline',
      //   key: 2,
      //   items: [{ key: 5, title: 'รายการสั่งซื้อของฉัน', path: 'pobuyeruiProfile' }],
      //   title: 'รายการสั่งซื้อ'
      // }
    }
  },
  methods: {
    changePage (val) {
      this.$router.push({ path: `/${val}` }).catch(() => {})
    },
    changeNavAccount () {
      if (this.$router.currentRoute.name === 'userprofileUI') {
        this.selectedItem = 0
        this.select = 1
      } else if (this.$router.currentRoute.name === 'favorite') {
        this.selectedItem = 2
        this.select = 3
      } else if (this.$router.currentRoute.name === 'addressProfile') {
        this.selectedItem = 1
        this.select = 2
      } else if (this.$router.currentRoute.name === 'pobuyeruiProfile') {
        this.select = 4
        this.selectedItem = this.select
        this.items[0].active = false
      }
    },
    async getConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      this.isBuyer = res.isBuyer
      this.itemsAffiliate = []
      if (this.isBuyer === '0') {
        this.itemsAffiliate = [
          { key: 13, action: 'mdi mdi-chart-line', title: `${this.$t('MenuBuyer.titleAffiliate')}`, disable: true },
          { key: 14, action: '', title: `${this.$t('MenuBuyer.titleAffiliate')}`, path: 'consentAffiliateMobile', disable: false }
        ]
      } else {
        this.itemsAffiliate = [
          { key: 13, action: 'mdi mdi-chart-line', title: `${this.$t('MenuBuyer.titleAffiliate')}`, disable: true },
          { key: 14, action: '', title: `${this.$t('MenuBuyer.menuShopSeller')}`, path: 'showShopSellerAffiliateMobile', disable: false },
          { key: 15, action: '', title: `${this.$t('MenuBuyer.menuProductAffiliate')}`, path: 'productAffiliateMobile', disable: false },
          { key: 16, action: '', title: `${this.$t('MenuBuyer.menuEditPay')}`, path: 'editPayMobile', disable: false },
          { key: 17, action: '', title: `${this.$t('MenuBuyer.menuEditSocial')}`, path: 'editSocailMobile', disable: false },
          { key: 18, action: '', title: `${this.$t('MenuBuyer.menuMyShared')}`, path: 'SharedBuyerAffiliateMobile', disable: false },
          { key: 19, action: '', title: `${this.$t('MenuBuyer.menuClick')}`, path: 'ClickBuyerAffiliateMobile', disable: false },
          { key: 20, action: '', title: `${this.$t('MenuBuyer.menuOrderAffiliate')}`, path: 'orderedBuyerAffilateMobile', disable: false },
          { key: 21, action: '', title: `${this.$t('MenuBuyer.menuWithdraw')}`, path: 'withdrawAffiliateMobile', disable: false }
        ]
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.backgroundPage{
background-color: #F6F6F6;
}
.container {
max-width: 100%;
}
.v-application ul, .v-application ol {
  padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
  padding: 0px 0px !important;
}
</style>
