<template>
  <v-container grid-list-xs>
    <v-row dense>
      <v-col cols="12" md="12">
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 10px; font-weight: bold">รวมเงิน :</span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 10px; font-weight: 300">
              {{ Number(total_price_no_vat).toLocaleString() }}
            </span>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 10px; font-weight: bold">
              ส่วนลด :
            </span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 10px; font-weight: 300">
              {{ total_discount }}
            </span>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 10px; font-weight: bold">
              เงินหลังหักส่วนลด :
            </span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 10px; font-weight: 300">
              {{ Number(total_price_discount).toLocaleString() }}
            </span>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 10px; font-weight: bold">
              ภาษีมูลค่าเพิ่ม (7%) :
            </span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 10px; font-weight: 300">
              {{ Number(total_vat).toLocaleString() }}
            </span>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 10px; font-weight: bold">
              ค่าจัดส่ง :
            </span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 10px; font-weight: 300">
              {{ Number(total_shipping).toLocaleString() }}
            </span>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="9" style="text-align: right" class="pt-0">
            <span style="font-size: 10px; font-weight: bold">
              จำนวนเงินทั้งสิ้น :
            </span>
          </v-col>
          <v-col cols="3" style="text-align: left" class="pt-0">
            <span style="font-size: 10px; font-weight: 300">
              {{ Number(net_price).toLocaleString() }}
            </span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="7">
        <v-img class="footerimg" style="background-color: white" :src="require(`@/assets/ISO.png`)"></v-img>
      </v-col>
      <v-col cols="5">
        <v-row dense>
          <v-col cols="12" md="12" class="text-center">
            <span class="approvedtr">{{ $t("pendingOrder.name") }}</span>
          </v-col>
          <v-col cols="12" md="12" class="text-center">
            <span class="approvedtr">{{ $t("PurchaserName.name") }} : …………………………………………</span>
          </v-col>
          <v-col cols="12" md="12" class="text-center">
            <span class="approvedtr">(…………………………………………)</span>
          </v-col>
          <v-col cols="12" md="12" class="text-center">
            <span class="approvedtr">{{ $t("date.name") }} : ……………………………………</span>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data: () => ({
    total_discount: '',
    total_price_discount: '',
    total_price_no_vat: '',
    total_shipping: '',
    total_vat: '',
    net_price: ''
  }),
  created () {
    this.getOrderDetail()
  },
  methods: {
    getOrderDetail () {
      const OrderDetail = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('PDF_Data'))))
      this.total_discount = OrderDetail.shopData.total_discount
      this.total_price_discount = OrderDetail.shopData.total_price_discount
      this.total_price_no_vat = OrderDetail.shopData.total_price_no_vat
      this.total_shipping = OrderDetail.shopData.total_shipping
      this.total_vat = OrderDetail.shopData.total_vat
      this.net_price = OrderDetail.shopData.net_price
    }
  }
}
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css?family=Sarabun&display=swap");

.footerimg {
  font-family: "Sarabun" !important;
  text-align: right;
  color: rgb(3, 39, 0);
  background-color: white;
  width: 70%;
}
.approvedtr {
  font-family: "Sarabun" !important;
  text-align: right;
  font-size: 13px !important;
  line-height: 12px;
}
</style>
