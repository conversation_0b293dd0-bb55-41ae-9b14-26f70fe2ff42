import AxiosAffiliate from './axios_affiliate.js'

const ModuleAffiliate = {
  state: {
    stateAffiliateConsent: [],
    stateAffiliateBuyerCreateDetail: [],
    stateAffiliateDetailPay: [],
    stateAffiliateConfirmJoin: [],
    stateAffiliateUpdatePayment: [],
    stateAffiliateUpdateSocial: [],
    stateAffiliateShowDetailBuyer: [],
    stateAffiliateShowProduct: [],
    stateAffiliateGenerateShortUrl: [],
    stateSellerJoinAffiliate: [],
    stateSelectProductAffiliate: [],
    stateAddProductAffiliate: [],
    stateSeletedProductList: [],
    stateListUserJoin: [],
    stateApproveBuyer: [],
    stateAffiliateListShop: [],
    stateAffiliateSearchShop: [],
    stateAffiliateBuyerJoinSeller: [],
    stateAffiliateProductBySeller: [],
    stateSeletedProductAffiliate: [],
    stateSeletedProductAffiliateAll: [],
    stateAffiliateListBank: [],
    stateAffiliateAutoApprove: [],
    stateDetailUserEKYC: [],
    stateAffiListProductByCategory: [],
    stateSearchProductAffiliate: [],
    stateSearchShopProductAffiliate: []
  },
  mutations: {
    mutationAffiliateConsent (state, data) {
      state.stateAffiliateConsent = data
    },
    mutationAffiliateBuyerCreateDetail (state, data) {
      state.stateAffiliateBuyerCreateDetail = data
    },
    mutationAffiliateConfirmJoin (state, data) {
      state.stateAffiliateConfirmJoin = data
    },
    mutationAffiliateUpdatePayment (state, data) {
      state.stateAffiliateUpdatePayment = data
    },
    mutationAffiliateUpdateSocail (state, data) {
      state.stateAffiliateUpdateSocail = data
    },
    mutationAffiliateShowDetailBuyer (state, data) {
      state.stateAffiliateShowDetailBuyer = data
    },
    mutationAffiliateShowProduct (state, data) {
      state.stateAffiliateShowProduct = data
    },
    mutationAffiliateGenerateShortUrl (state, data) {
      state.stateAffiliateGenerateShortUrl = data
    },
    mutationSellerJoinAffiliate (state, data) {
      state.stateSellerJoinAffiliate = data
    },
    mutationSelectProductAffiliate (state, data) {
      state.stateSelectProductAffiliate = data
    },
    mutationAddProductAffiliate (state, data) {
      state.stateAddProductAffiliate = data
    },
    mutationSeletedProductList (state, data) {
      state.stateSeletedProductList = data
    },
    mutationListUserJoin (state, data) {
      state.stateListUserJoin = data
    },
    mutationApproveBuyer (state, data) {
      state.stateApproveBuyer = data
    },
    mutationAffiliateListShop (state, data) {
      state.stateAffiliateListShop = data
    },
    mutationAffiliateSearchShop (state, data) {
      state.stateAffiliateSearchShop = data
    },
    mutationAffiliateBuyerJoinSeller (state, data) {
      state.stateAffiliateBuyerJoinSeller = data
    },
    mutationAffiliateProductBySeller (state, data) {
      state.stateAffiliateProductBySeller = data
    },
    mutationSeletedProductAffiliateAll (state, data) {
      state.stateSeletedProductAffiliateAll = data
    },
    mutationAffiliateListBank (state, data) {
      state.stateAffiliateListBank = data
    },
    mutationAffiliateAutoApprove (state, data) {
      state.stateAffiliateAutoApprove = data
    },
    mutationDetailUserEKYC (state, data) {
      state.stateDetailUserEKYC = data
    },
    mutationAffiListProductByCategory (state, data) {
      state.stateAffiListProductByCategory = data
    },
    mutationSearchProductAffiliate (state, data) {
      state.stateSearchProductAffiliate = data
    },
    mutationSearchShopProductAffiliate (state, data) {
      state.stateSearchShopProductAffiliate = data
    }
  },
  actions: {
    async actionsAffiliateConsent (context, access) {
      const response = await AxiosAffiliate.AffiliateConsent(access)
      await context.commit('mutationAffiliateConsent', response)
    },
    async actionsAffiliateBuyerCreateDetail (context, access) {
      const response = await AxiosAffiliate.AffiliateBuyerCreateDetail(access)
      await context.commit('mutationAffiliateBuyerCreateDetail', response)
    },
    async actionsAffiliateConfirmJoin (context, access) {
      const response = await AxiosAffiliate.AffiliateConfirmJoin(access)
      await context.commit('mutationAffiliateConfirmJoin', response)
    },
    async actionsAffiliateUpdatePayment (context, access) {
      const response = await AxiosAffiliate.AffiliateUpdatePayment(access)
      await context.commit('mutationAffiliateUpdatePayment', response)
    },
    async actionsAffiliateUpdateSocail (context, access) {
      const response = await AxiosAffiliate.AffiliateUpdateSocail(access)
      await context.commit('mutationAffiliateUpdateSocail', response)
    },
    async actionsAffiliateShowDetailBuyer (context, access) {
      const response = await AxiosAffiliate.AffiliateShowDetailBuyer(access)
      await context.commit('mutationAffiliateShowDetailBuyer', response)
    },
    async actionsAffiliateShowProduct (context, access) {
      const response = await AxiosAffiliate.AffiliateShowProduct(access)
      await context.commit('mutationAffiliateShowProduct', response)
    },
    async actionsAffiliateGenerateShortUrl (context, access) {
      const response = await AxiosAffiliate.AffiliateGenerateShortUrl(access)
      await context.commit('mutationAffiliateGenerateShortUrl', response)
    },
    async actionsSellerJoinAffiliate (context, access) {
      const response = await AxiosAffiliate.SellerJoinAffiliate(access)
      await context.commit('mutationSellerJoinAffiliate', response)
    },
    async actionsSelectProductAffiliate (context, access) {
      const response = await AxiosAffiliate.SelectProductAffiliate(access)
      await context.commit('mutationSelectProductAffiliate', response)
    },
    async actionsAddProductAffiliate (context, access) {
      const response = await AxiosAffiliate.AddProductAffiliate(access)
      await context.commit('mutationAddProductAffiliate', response)
    },
    async actionsSeletedProductList (context, access) {
      const response = await AxiosAffiliate.SeletedProductList(access)
      await context.commit('mutationSeletedProductList', response)
    },
    async actionsListUserJoin (context, access) {
      const response = await AxiosAffiliate.ListUserJoin(access)
      await context.commit('mutationListUserJoin', response)
    },
    async actionsApproveBuyer (context, access) {
      const response = await AxiosAffiliate.ApproveBuyer(access)
      await context.commit('mutationApproveBuyer', response)
    },
    async actionsAffiliateListShop (context, access) {
      const response = await AxiosAffiliate.AffiliateListShop(access)
      await context.commit('mutationAffiliateListShop', response)
    },
    async actionsAffiliateSearchShop (context, access) {
      const response = await AxiosAffiliate.AffiliateSearchShop(access)
      await context.commit('mutationAffiliateSearchShop', response)
    },
    async actionsAffiliateBuyerJoinSeller (context, access) {
      const response = await AxiosAffiliate.AffiliateBuyerJoinSeller(access)
      await context.commit('mutationAffiliateBuyerJoinSeller', response)
    },
    async actionsAffiliateProductBySeller (context, access) {
      const response = await AxiosAffiliate.AffiliateProductBySeller(access)
      await context.commit('mutationAffiliateProductBySeller', response)
    },
    async actionsAffiliateListBank (context, access) {
      const response = await AxiosAffiliate.AffiliateListBank(access)
      await context.commit('mutationAffiliateListBank', response)
    },
    async actionsAffiliateAutoApprove (context, access) {
      const response = await AxiosAffiliate.AffiliateAutoApprove(access)
      await context.commit('mutationAffiliateAutoApprove', response)
    },
    async actionsDetailUserEKYC (context, access) {
      const response = await AxiosAffiliate.DetailUserEKYC(access)
      await context.commit('mutationDetailUserEKYC', response)
    },
    async actionsAffiListProductByCategory (context, access) {
      const response = await AxiosAffiliate.AffiListProductByCategory(access)
      await context.commit('mutationAffiListProductByCategory', response)
    },
    async actionsSearchProductAffiliate (context, access) {
      const response = await AxiosAffiliate.SearchProductAffiliate(access)
      await context.commit('mutationSearchProductAffiliate', response)
    },
    async actionsSearchShopProductAffiliate (context, access) {
      const response = await AxiosAffiliate.SearchShopProductAffiliate(access)
      await context.commit('mutationSearchShopProductAffiliate', response)
    }
  }
}

export default ModuleAffiliate
