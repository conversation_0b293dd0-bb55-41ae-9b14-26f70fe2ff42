<template>
  <v-container :class="MobileSize ? 'background_productMobile mt-3' : 'background_product pa-6'" style="background-color: #FFFFFF">
    <v-row>
      <v-col>
        <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">จัดการคูปอง</span>
      </v-col>
      <v-col :cols="IpadSize ? '12' : ''" class="d-flex justify-end">
        <v-btn class="mr-2" style="border: 1px solid #27AB9C" outlined rounded @click="setValueDate(new Date().toISOString().slice(0, 10), 'date'); dialogTemplateCoupon = true;"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">template คูปอง</span></v-btn>
        <v-btn style="border: 1px solid #27AB9C" outlined rounded @click="dialogCoupon01 = true"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">เพิ่มคูปอง</span></v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="6" sm="6">
        <v-text-field v-model="textSearch" placeholder="ค้นหารายการโปรโมชัน" outlined dense hide-details @click="current = 1">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" md="4" sm="4">
        <v-select class="setCustomSelect vSelectLineHeight" v-model="searchByType" :items="type" item-text="title" item-value="value" outlined dense hide-details @click="current = 1"></v-select>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <span class="pb-0" style="font-weight: 600; font-size:16px; line-height: 24px;">โปรโมชันทั้งหมด({{ numOfAll }})</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="4" sm="6" v-for="(itemmain, index) in paginated" :key="index">
        <v-card outlined style="display: flex; flex-direction: column; border: 1px solid #F2F2F2; border-radius: 12px;" width="100%" height="100%">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-row dense class="pl-2 pt-1"  style="display: flex; align-items: center;">
              <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">การแสดงโปรโมชัน</span>
              <v-switch color="#52C41A" @change="changePls(itemmain)" v-model="itemmain.show_status" true-value="active" false-value="inactive" hide-details inset class="pl-4"></v-switch>
            </v-row>
            <v-spacer></v-spacer>
            <v-menu bottom left :offset-y="true">
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#27AB9C" icon v-bind="attrs" v-on="on" >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <!-- List menu promotions -->
            <v-list>
              <v-list-item
                v-for="(item, i) in actionChoice"
                :key="i"
                @click="item.value === 'edit' ? gateOpen(itemmain) : openDialogdel(itemmain)"
                :disabled="item.value === 'delete' && itemmain.can_delete === 'no'"
              >
                <v-list-item-icon class="mr-1">
                  <v-icon color='#27AB9C'>{{ item.icon }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
          </v-app-bar>
          <v-card-text>
            <v-row>
              <v-col>
                <v-img v-if="itemmain.coupon_image !== null" :src="itemmain.coupon_image" height="150" contain></v-img>
                <v-img v-else src="@/assets/coupon_image/empty_coupon.png" contain height="150"></v-img>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="pb-0">
                <v-card-title class="px-1 pb-2"><span style="font-weight: 700; font-size: 18px; line-height: 28px;">{{ itemmain.coupon_name | truncate(30, '...') }}</span></v-card-title>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="pt-0">
                <v-card-subtitle class="px-1 pt-1 pb-0"><span style=" font-size: 14px; line-height: 24px;">โค้ดคูปอง:</span><span v-if="itemmain.coupon_type !== 'free_product'" style="font-weight: 600; font-size: 14px; line-height: 24px;"> {{ itemmain.coupon_code }} </span><span v-else style="font-weight: 600; font-size: 14px; line-height: 24px;"> -</span></v-card-subtitle>
              </v-col>
            </v-row>
            <v-row class="px-1">
              <v-col>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ itemmain.coupon_type === 'discount' ? 'ส่วนลด' : itemmain.coupon_type === 'free_shipping' ? 'ฟรีค่าจัดส่ง' : itemmain.coupon_type === 'free_product' ? 'X แถม Y' : '' }}</span>
              </v-col>
              <v-col style="display: flex; justify-content: flex-end;">
                <span v-if="itemmain.user_cap"><span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ itemmain.user_cap }} ครั้ง</span> / ผู้ใช้</span>
                <span v-else><span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">ใช้ได้ไม่จำกัดครั้ง </span>/ ผู้ใช้</span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text class="py-2" style="background-color: #d1edea;">
            <v-row>
              <v-col style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <span>ใช้ไปแล้ว</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{itemmain.use_count}}</span>
              </v-col>
              <v-divider class="my-3" vertical style="border: 1px solid #FFFFFF; height: 50px;" />
              <v-col style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <span>คงเหลือ</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ itemmain.quota - itemmain.use_count }}</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-if="paginated.length > 0">
      <!-- <v-pagination color="#27AB9C" v-model="pageNumber" :length="(searchByType === 'all' || searchByType === '')  && textSearch === '' ? pageMax : 1" :total-visible="10" @input="pageChange()">
      </v-pagination> -->
      <v-pagination color="#27AB9C" v-model="pageNumber" :length="checkLengthPagination()" :total-visible="10" ></v-pagination>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-else>
      <span>ไม่พบโปรโมชัน</span>
    </v-row>
    <v-dialog v-model="dialogCoupon01" width="500px" round>
      <v-card style="max-width: 500px;">
        <v-toolbar height="50px" style="background-color: #d1edea;">
          <v-row dense class="ma-2">
            <v-col style="display: flex; align-items: center; justify-content: center;">
              <span style="font-size: 20px; font-weight: 700;">เพิ่มคูปอง</span>
            </v-col>
          </v-row>
        </v-toolbar>
        <v-col>
          <v-row class="pa-3">
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" width="185" elevation="0" @click="openPath('discount')">
                <v-col>
                  <v-row>
                    <v-img src="@/assets/coupon_image/discount.png" height="140" width="125"></v-img>
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      ส่วนลด
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" width="185" elevation="0" @click="openPath('freeshippng')">
                <v-col>
                  <v-row>
                    <v-img src="@/assets/coupon_image/shipping.png" height="140" width="125"></v-img>
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      ฟรีค่าจัดส่ง
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" width="185" elevation="0" @click="openPath('xandy')">
                <v-col>
                  <v-row>
                    <v-img src="@/assets/coupon_image/xgifty.png" height="140" width="125"></v-img>
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      X แถม Y
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDelForsure" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 22px; font-weight: 700;">ลบโปรโมชัน</span>
          </v-row>
          <v-btn fab small @click="closeDialog1()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text style="height: 150px !important; display: flex; justify-content: center; align-items: center;">
            <v-col>
              <v-row class="pb-2" style="justify-content: center;">
                <span style="font-size: 18px; font-weight: 600;">ต้องการลบโปรโมชัน " {{ itemForDelName }} " หรือไม่</span>
              </v-row>
            </v-col>
          </v-card-text>
          <v-card-actions>
            <v-row>
              <v-col style=" display: flex; justify-content: center; align-items: center;">
                <v-btn rounded color="#27AB9C" height="40" width="120" outlined @click="closeDialog1()" elevation="0">ยกเลิก</v-btn>
              </v-col>
              <v-col style=" display: flex; justify-content: center; align-items: center;">
                <v-btn rounded color="#27AB9C" height="40" width="120" style="color: #FFFFFF" @click="delStart()" elevation="0">ยืนยัน</v-btn>
              </v-col>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Template Coupon -->
    <v-dialog v-model="dialogTemplateCoupon" :width="MobileSize ? '100%' : IpadSize ? '100%' : '790'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 790px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>Template คูปอง</b></span>
              </v-col>
              <v-btn fab small @click="dialogTemplateCoupon = !dialogTemplateCoupon; resetCouponDialog()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '790px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card class="mx-0" elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex">
                  <!-- ข้อมูลคูปอง -->
                  <span class="mr-auto pb-1" style="font-size: 16px; font-weight: 600; color: #333333; align-content: center;"><v-icon small color="#27AB9C">mdi-arrow-right-drop-circle</v-icon> ข้อมูลโปรโมชัน :</span>
                  <v-col cols="12">
                    <div class="d-inline-flex mr-auto my-1">
                      <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">ชนิดคูปอง :</span>
                      <v-select v-model="typeCoupon" style="font-size: 14px;" @change="handleTypeCoupon" :items="itemCoupon" item-text="text" item-value="value" placeholder="เลือกชนิดคูปอง" dense outlined hide-details></v-select>
                    </div>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <!-- ชื่อคูปอง -->
                      <v-col :cols="typeCoupon !== 'free_product' ? '6' : '12'">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">ชื่อคูปอง :</span>
                          <v-text-field v-model="couponName" style="font-size: 14px;" dense outlined hide-details></v-text-field>
                        </div>
                      </v-col>
                      <!-- SKU -->
                      <v-col v-if="typeCoupon !== 'free_product'" cols="6">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">รหัสคูปอง :</span>
                          <v-text-field v-model="couponCode" style="font-size: 14px;" dense outlined hide-details disabled></v-text-field>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <!-- จำนวนคูปอง -->
                      <v-col cols="6">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">จำนวนคูปอง :</span>
                          <v-text-field v-model="amountUse" style="font-size: 14px;" dense outlined hide-details></v-text-field>
                        </div>
                      </v-col>
                      <!-- จำนวนคูปอง/คน -->
                      <v-col cols="6">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">จำนวนคูปอง/คน :</span>
                          <v-text-field v-model="amountCap" style="font-size: 14px;" dense outlined hide-details></v-text-field>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="12" class="my-1">
                        <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333; align-content: center;"><v-icon small color="#27AB9C">mdi-arrow-right-drop-circle</v-icon> การใช้โปรโมชัน :</span>
                      </v-col>
                      <!-- ประเภทส่วนลด -->
                      <v-col cols="12" v-if="typeCoupon !== 'free_product'" class="">
                        <v-radio-group
                          v-model="typeDiscount"
                          row
                          hide-details
                          dense
                          class="mt-0"
                        >
                          <v-radio
                            label="ลดเป็นบาท"
                            value="baht"
                          ></v-radio>
                          <v-radio
                            label="ลดเป็นเปอร์เซ็นต์"
                            value="percent"
                          ></v-radio>
                        </v-radio-group>
                      </v-col>
                      <!-- จ่ายขั้นต่ำ -->
                       <v-col cols="6" v-if="typeCoupon !== 'free_product'">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">ขั้นต่ำ :</span>
                          <v-text-field v-model="spendMinimum" style="font-size: 14px;" dense outlined hide-details></v-text-field>
                        </div>
                      </v-col>
                      <!-- ลดสูงสุด -->
                      <v-col cols="6" v-if="typeCoupon !== 'free_product'">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">ลดสูงสุด :</span>
                          <v-text-field v-model="discount" style="font-size: 14px;" dense outlined hide-details :suffix="typeDiscount === 'baht' ? 'บาท' : '%'"></v-text-field>
                        </div>
                      </v-col>
                      <!-- ใช้ได้ตั้งแต่วันที่ -->
                      <v-col cols="12">
                        <div class="d-inline-flex my-1">
                          <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">ใช้ได้ตั้งแต่วันที่ :</span>
                          <v-dialog
                            ref="dialogStartDateUse"
                            v-model="dialogStartDateUse"
                            :return-value.sync="date"
                            width="290px"
                            persistent
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-text-field
                                v-model="sentStartDate"
                                v-bind="attrs"
                                placeholder="วว/ดด/ปป"
                                outlined
                                readonly
                                dense
                                hide-details
                                v-on="on"
                                :rules="Rules.empty"
                              >
                                <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                              </v-text-field>
                            </template>
                            <v-date-picker
                              v-model="date"
                              scrollable
                              reactive
                              locale="TH-th"
                              :min="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                            >
                              <v-row dense>
                                <!-- <v-col cols="12" class="pt-0">
                                  <v-col cols="12" class="pt-0">
                                    <a-time-picker
                                      v-model="time"
                                      :bordered="false"
                                      style="width: 100%;"
                                      format="HH:mm:ss น."
                                      valueFormat="HH:mm:ss"
                                      size="large"
                                      placeholder="00.00.00 น."
                                      :disabled="date === ''"
                                      :placement="'topLeft'"
                                      :disabledHours="disabledHoursUse"
                                      :disabledMinutes="disabledMinutesUse"
                                      :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                                    />
                                  </v-col>
                                </v-col> -->
                                <v-col cols="12" align="end">
                                  <v-btn text color="primary" @click="dialogStartDateUse = false" > ยกเลิก </v-btn>
                                  <v-btn text color="primary" :disabled="date == ''" @click="setValueDate(date, 'date'), $refs.dialogStartDateUse.save(date), date2 = '', sentEndDate2 = '', time2 = ''"> บันทึก</v-btn>
                                </v-col>
                              </v-row>
                            </v-date-picker>
                          </v-dialog>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- ส่วนของสินค้า ฟรีค่าจัดส่ง -->
                  <v-col cols="12" v-if="typeCoupon === 'free_shipping' || typeCoupon === 'discount'">
                    <div>
                      <span class="pr-2" style="font-size: 14px; font-weight: 600; color: #333333; align-content: center;">สินค้าที่เข้าร่วม</span>
                        <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px; font-size: 14px;'">
                          <treeselect v-model="productList" class="my-1" :defaultExpandLevel="1" @input="theRedP = true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :rules="Rules.empty" :options="dataListPrimeShipping" :multiple="true" openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม" />
                        </div>
                    </div>
                  </v-col>
                  <!-- ส่วนของสินค้า 1 แถม 1 -->
                  <v-col cols="12" class="mb-2" v-if="typeCoupon === 'free_product'">
                    <div>
                      <v-row v-for="(item, index) in productList" :key="index">
                        <v-col cols="11">
                          <v-row dense>
                            <v-col cols="4">
                              <span>ซื้อ</span><span style="color: red;"> *</span>
                              <div :style="item.productIdFail ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                                <treeselect class="" :key="componentAlpha" :backspaceRemoves="false" v-model="item.productId" noChildrenText="ไม่มีสินค้าในหมวดหมู่นี้" @input="deleteFromAlpha(item, 'alpha')" :disable-branch-nodes="true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :options="dataProductListInShopByCatagory"  openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
                              </div>
                            </v-col>
                            <v-col cols="2">
                              <span>จำนวน</span><span style="color: red;"> *</span>
                              <v-text-field v-model="item.productNum" :rules="Rules.empty" dense type="number" outlined hide-details></v-text-field>
                            </v-col>
                            <v-col cols="4">
                              <span>แถม</span><span style="color: red;"> *</span>
                              <div :style="item.productFreeIdFail ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
                                <treeselect class="" :key="componentBeta" :backspaceRemoves="false" v-model="item.productFreeId" noChildrenText="ไม่มีสินค้าในหมวดหมู่นี้" @input="deleteFromBeta(item, 'beta')" :disable-branch-nodes="true" no-results-text='ไม่พบสินค้าที่เข้าร่วม' :options="dataProductListInShopByCatagoryFree" openDirection="top" placeholder="เลือกสินค้าที่เข้าร่วม"/>
                              </div>
                            </v-col>
                            <v-col cols="2">
                              <span>จำนวน</span><span style="color: red;"> *</span>
                              <v-text-field v-model="item.productFreeNum" :rules="Rules.empty" dense type="number" outlined hide-details></v-text-field>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                  <!-- ตัวอย่างคูปอง -->
                  <v-col cols="12">
                    <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333; align-content: center;"><v-icon small color="#27AB9C">mdi-arrow-right-drop-circle</v-icon> ตัวอย่างคูปอง :</span>
                    <v-card class="d-flex align-center mt-2 pa-5 mx-auto" outlined style="border: solid #F7F7F7; background-color: #FAFAFA;" :style="IpadSize ? 'gap: 3vw; height: 20vw; width: 80vw; border-radius: 1vw;' : IpadProSize ? 'gap: 3vw; height: 15vw; width: 45vw;' : 'gap: 2vw; border-radius: .5vw; width: 45vw;'">
                      <div :class="IpadSize ? 'ml-3' : ''">
                        <!-- <v-avatar @click="gotoShopDetail(item.shop_name, item.seller_shop_id)" :size="MobileSize ? 90 : 100" tile v-if="item.shop_logo !== ''" width="80"><img :src="item.shop_logo" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 1vw;"></v-avatar> -->
                        <v-avatar :size="MobileSize ? 90 : 100" tile width="80"><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/coupon_image/empty_coupon.png" alt=""></v-avatar>
                      </div>
                      <div :style="IpadSize ? 'width: 43vw;' : IpadProSize ? '' : 'width: 20vw;'">
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span style="color: #F15A24; font-size: large; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substringCouponShop(couponName) }}</b></span>
                          </template>
                          <span>{{ couponName }}</span>
                        </v-tooltip><br>
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <span v-bind="attrs" v-on="on">สำหรับร้านค้า {{ substring(shopName) }} เท่านั้น</span>
                          </template>
                          <span>สำหรับร้านค้า {{ shopName }} เท่านั้น</span>
                        </v-tooltip><br>
                        <span style="color: #636363;">ขั้นต่ำ {{ spendMinimum }} .- </span>
                        <span style="color: #636363;" v-if="discount !== ''"> ไม่เกิน {{ discount }} .-</span><br>
                        <span style="color: #636363;" v-if="sentStartDate !== ''">
                          <span>ใช้ได้ตั้งแต่วันที่ {{ new Date(dateToShow).toLocaleDateString("th-TH", { day: "numeric", month: "long" }) }} </span>
                          <!-- <span v-if="item.use_enddate !== ''">- {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span> -->
                        </span>
                      </div>
                      <div class="ma-auto">
                        <v-btn color="#ff9200" rounded style="color: #fff; text-transform: none;"><b>{{ $t('Coupon.Claim') }}</b></v-btn>
                      </div>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" class="d-flex justify-center mt-5">
                    <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="dialogTemplateCoupon = false; resetCouponDialog()">ยกเลิก</v-btn>
                    <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="createCoupon()">บันทึก</v-btn>
                  </v-col>
                  </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
        </v-img>
        <v-container>
          <v-card-text>
            <v-col>
              <v-row class="pb-2" style="justify-content: center;">
                <span style="font-size: 20px; font-weight: 600;">เพิ่มคูปองสำเร็จ</span>
              </v-row>
              <v-row style="justify-content: center;">
                <!-- <span style="font-size: 20px; font-weight: 600">"สำเร็จ"</span> -->
              </v-row>
            </v-col>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogFail" width="500">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
        </v-img>
        <v-container>
          <v-card-text>
            <v-col>
              <v-row class="d-flex justify-center">
                <span style="font-size: 18px; font-weight: 600;">{{ dialogFailContext }}</span>
              </v-row>
            </v-col>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import dayjs from 'dayjs'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Treeselect from '@riophae/vue-treeselect'
export default {
  components: {
    Treeselect
  },
  data () {
    return {
      seller_shop_id: '',
      pageMax: null,
      current: 1,
      pageSize: 6,
      textSearch: '',
      countPromotion: 0,
      dialogCoupon01: false,
      type:
      [
        { title: 'ทั้งหมด', value: 'all' },
        { title: 'โค้ดส่วนลด', value: 'discount' },
        { title: 'ฟรีค่าจัดส่ง', value: 'free_shipping' },
        { title: 'ซื้อ X แถม Y', value: 'free_product' }
      ],
      actionChoice: [
        { title: 'แก้ไขโปรโมชัน', value: 'edit', icon: 'mdi-pencil', link: '' },
        { title: 'ลบโปรโมชัน', value: 'delete', icon: 'mdi-delete', link: '' }
      ],
      searchByType: 'all',
      dataMain: [],
      itemForDel: null,
      itemForDelName: null,
      dialogDelForsure: false,
      dialogTemplateCoupon: false,
      itemCoupon: [
        { text: 'โค้ดส่วนลด', value: 'discount' },
        { text: 'ฟรีค่าจัดส่ง', value: 'free_shipping' },
        { text: 'ซื้อ 1 แถม 1', value: 'free_product' }
      ],
      typeCoupon: '',
      couponName: '',
      couponCode: '',
      amountUse: '',
      amountCap: '',
      shopName: '',
      typeDiscount: '',
      spendMinimum: '',
      discount: '',
      date: '',
      time: '',
      dialogStartDateUse: false,
      sentStartDate: '',
      dateToShow: '',
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล']
      },
      dataListPrimeFreeProduct: [],
      dataListPrimeShipping: [],
      dataAlpha: [],
      dataBeta: [],
      productList: [],
      componentAlpha: 0,
      componentBeta: 0,
      dataProductListInShopByCatagory: [],
      dataProductListInShopByCatagoryFree: [],
      productList2: [],
      dialogSuccess: false,
      dialogFail: false,
      dialogFailContext: '',
      theRedP: true,
      createFail: true
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        // document.getElementById('promotion').scrollIntoView()
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.textSearch === '' && this.searchByType === 'all') {
        return this.dataMain.slice(this.indexStart, this.indexEnd)
      } else {
        var items = this.dataMain.filter(e => {
          if (this.textSearch !== '' && this.searchByType === 'all') {
            return e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          } else if (this.textSearch === '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase())
          } else if (this.textSearch !== '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase()) && e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          }
        })
        return items.slice(this.indexStart, this.indexEnd)
      }
    },
    numOfAll () {
      if (this.textSearch === '' && this.searchByType === 'all') {
        return this.dataMain.length
      } else {
        var items = this.dataMain.filter(e => {
          if (this.textSearch !== '' && this.searchByType === 'all') {
            return e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          } else if (this.textSearch === '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase())
          } else if (this.textSearch !== '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase()) && e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          }
        })
        return items.length
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    typeCoupon (val) {
      var date = dayjs().format('DDMMYY')
      var time = dayjs().format('HHmm')
      // console.log(date, time)
      if (val === 'discount') {
        this.couponCode = 'DIS' + date + time
        this.couponName = 'โค้ดส่วนลด'
        this.amountCap = '1'
        this.typeDiscount = 'baht'
      } else if (val === 'free_shipping') {
        this.couponCode = 'FREE' + date + time
        this.couponName = 'ฟรีค่าจัดส่ง'
        this.amountCap = '1'
        this.typeDiscount = 'baht'
      } else if (val === 'free_product') {
        this.couponCode = ''
        this.couponName = 'ซื้อ 1 แถม 1'
        this.amountCap = '1'
      } else {
        this.couponCode = ''
        this.couponName = ''
        this.amountCap = ''
        this.spendMinimum = ''
        this.discount = ''
        this.typeDiscount = ''
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('CheckShop')
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    if (localStorage.getItem('shopDetail') !== null) {
      this.shopName = JSON.parse(localStorage.getItem('shopDetail')).name
    } else {
      this.shopName = ''
    }
    this.getdata()
  },
  methods: {
    setValueDate (dateDay, proof) {
      // console.log('1')
      // console.log('dateDay', dateDay)
      if (!dateDay) return null
      this.dateToShow = dateDay
      const [year, month, day] = dateDay.split('-')
      const yearChange = parseInt(year) + 543
      this.sentStartDate = `${day}/${month}/${yearChange}`
    },
    substring (data) {
      if (data !== '') {
        if (this.MobileSize || this.IpadSize || this.IpadProSize) {
          return data.length > 15 ? data.substring(0, 15) + '...' : data
        } else {
          return data.length > 20 ? data.substring(0, 20) + '...' : data
        }
      }
    },
    substringCouponShop (data) {
      if (data !== '') {
        if (this.MobileSize || this.IpadSize || this.IpadProSize) {
          return data.length > 20 ? data.substring(0, 20) + '...' : data
        } else {
          return data.length > 40 ? data.substring(0, 40) + '...' : data
        }
      }
    },
    async delStart () {
      this.$store.commit('openLoader')
      var data = {
        coupon_id: this.itemForDel.id
      }
      await this.$store.dispatch('actionschangeDeleteCoupon', data)
      const res2 = await this.$store.state.ModuleManageCoupon.statechangeDeleteCoupon
      if (res2.result === 'Success') {
        this.closeDialog1()
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'ลบโปรโมชันเสร็จสิ้น', showConfirmButton: false, timer: 2500 })
        // setTimeout(() => {
        //   window.location.reload()
        // }, 2000)
        await this.getdata()
      } else {
        this.closeDialog1()
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${res2.message}`, showConfirmButton: false, timer: 2500 })
      }
    },
    closeDialog1 () {
      this.itemForDel = null
      this.itemForDelName = null
      this.dialogDelForsure = false
    },
    openDialogdel (item) {
      this.itemForDel = item
      this.itemForDelName = item.coupon_name
      this.dialogDelForsure = true
    },
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    gateOpen (item) {
      // console.log(item, '9000000')
      if (item.coupon_type === 'discount') {
        if (this.MobileSize) {
          this.$router.push({ path: `/coupondiscountMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/coupondiscount?status=edit&id=${item.id}` }).catch(() => {})
        }
      } else if (item.coupon_type === 'free_shipping') {
        if (this.MobileSize) {
          this.$router.push({ path: `/couponfreeshippingMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/couponfreeshipping?status=edit&id=${item.id}` }).catch(() => {})
        }
      } else if (item.coupon_type === 'free_product') {
        if (this.MobileSize) {
          this.$router.push({ path: `/couponfreeproductMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/couponfreeproduct?status=edit&id=${item.id}` }).catch(() => {})
        }
      }
    },
    openPath (path) {
      if (path === 'discount') {
        if (this.MobileSize) {
          this.$router.push({ path: '/coupondiscountMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/coupondiscount' }).catch(() => {})
        }
      } else if (path === 'freeshippng') {
        if (this.MobileSize) {
          this.$router.push({ path: '/couponfreeshippingMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/couponfreeshipping' }).catch(() => {})
        }
      } else if (path === 'xandy') {
        if (this.MobileSize) {
          this.$router.push({ path: '/couponfreeproductMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/couponfreeproduct' }).catch(() => {})
        }
      }
    },
    async changePls (item) {
      this.$store.commit('openLoader')
      var data = {
        coupon_id: item.id
      }
      await this.$store.dispatch('actionschangeStatusCoupon', data)
      const res = await this.$store.state.ModuleManageCoupon.statechangeStatusCoupon
      // console.log(res, 'res')
      if (res.result === 'Success') {
      } else {
      }
      this.$store.commit('closeLoader')
    },
    async getdata () {
      this.$store.commit('openLoader')
      this.dataMain = []
      var data = {
        shop_id: this.seller_shop_id,
        coupon_status: null
      }
      await this.$store.dispatch('actionsgetCoupon', data)
      const res2 = await this.$store.state.ModuleManageCoupon.stategetCoupon
      if (res2.result === 'Success') {
        this.dataMain = res2.data.coupon
        // console.log('dataMain', this.dataMain)
        this.pageMax = parseInt(this.dataMain.length / 6) === 0 ? 1 : Math.ceil(this.dataMain.length / 6)
        this.$store.commit('closeLoader')
      } else {
        this.dataMain = []
        this.$store.commit('closeLoader')
      }
      // console.log(this.dataMain, 'res2')
    },
    checkLengthPagination () { // คำนวณจำนวน pagnation
      // console.log('first')
      // ถูกเรียกจาก pagination ด้านบน
      // Math.ceil ปัดขึ้นตลอด
      var max = null
      var items = this.dataMain.filter(e => {
        if (this.textSearch !== '' && this.searchByType === 'all') {
          return e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
        } else if (this.textSearch === '' && this.searchByType !== 'all') {
          return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase())
        } else if (this.textSearch !== '' && this.searchByType !== 'all') {
          return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase()) && e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
        } else {
          return this.dataMain
        }
      })
      this.countPromotion = items.length
      max = Math.ceil(items.length / this.pageSize)
      this.pageNumber = this.pageNumber > max ? max : this.pageNumber
      return max
    },
    async handleTypeCoupon (val) {
      // console.log('123')
      if (val === 'free_product') {
        // console.log('456')
        this.productList = [{ productId: null, productIdFail: true, productNum: 1, productFreeId: null, productFreeIdFail: true, productFreeNum: 1 }]
        await this.getdataNew()
      } else if (val === 'free_shipping' || val === 'discount') {
        this.productList = []
        await this.getdataNew()
      }
    },
    async getdataNew () {
      var sittingCategory = {}
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsListProductInShop', data)
      const response = await this.$store.state.ModuleManageCoupon.stateListProductInShop
      var filteredData = []
      filteredData = response.data[0]
      if (this.typeCoupon === 'free_product') {
        this.dataListPrimeFreeProduct = [filteredData]
        if (filteredData.product_list.length !== 0) {
          sittingCategory = {
            id: 0,
            label: 'หมวดหมู่ทั้งหมด',
            children: [filteredData]
          }
        } else {
          sittingCategory = {
            id: 0,
            label: 'หมวดหมู่ทั้งหมด',
            children: filteredData.sub_category
          }
        }
        this.dataProductListInShopByCatagory = await this.transformDataToTreeFreeProduct(sittingCategory.children)
        this.dataProductListInShopByCatagoryFree = await this.transformDataToTreeFreeProduct(sittingCategory.children)
      } else if (this.typeCoupon === 'free_shipping' || this.typeCoupon === 'discount') {
        if (filteredData.product_list.length !== 0) {
          sittingCategory = [{
            id: 'special_1',
            label: 'หมวดหมู่ทั้งหมด',
            children: [filteredData]
          }]
        } else {
          sittingCategory = [{
            id: 'special_1',
            label: 'ทั้งหมด',
            children: filteredData.sub_category
          }]
        }
        sittingCategory[0].children = await this.transformDataToTree(sittingCategory[0].children)
        this.dataListPrimeShipping = await sittingCategory
      }
    },
    forceRerenderAlpha () {
      this.componentAlpha += 1
    },
    forceRerenderBeta () {
      this.componentBeta += 1
    },
    transformDataToTreeFreeProduct (data) {
      return data.map((item) => {
        const transformed = {
          id: item.category_id,
          label: item.category_name,
          children: []
        }

        // เพิ่ม sub_category
        if (item.sub_category && item.sub_category.length > 0) {
          transformed.children = this.transformDataToTreeFreeProduct(item.sub_category)
        }

        // เพิ่ม product_list
        if (item.product_list && item.product_list.length > 0) {
          const products = item.product_list

          const productItems = products.map((product) => {
            const productNode = {
              id: product.product_id,
              label: product.product_name
              // children: []
            }

            // เพิ่ม attribute ถ้ามี
            if (product.attributes && product.attributes.length > 0) {
              productNode.children = product.attributes.map((attr) => ({
                id: attr.product_attribute_id,
                label: attr.product_attribute_name
              }))
            }

            return productNode
          })

          if (productItems.length > 0) {
            transformed.children.push(...productItems)
          }
        }

        return transformed
      })
    },
    transformDataToTree (data) {
      return data.map((item) => {
        const transformed = {
          id: item.hierachy,
          label: item.category_name,
          children: []
        }

        // เพิ่ม sub_category
        if (item.sub_category && item.sub_category.length > 0) {
          transformed.children = this.transformDataToTree(item.sub_category)
        }

        // เพิ่ม product_list
        if (item.product_list && item.product_list.length > 0) {
          const products = item.product_list

          const productItems = products.map((product) => {
            const productNode = {
              id: product.product_id,
              label: product.product_name
              // children: []
            }

            // เพิ่ม attribute ถ้ามี
            if (product.attributes && product.attributes.length > 0) {
              productNode.children = product.attributes.map((attr) => ({
                id: attr.product_attribute_id,
                label: attr.product_attribute_name
              }))
            }

            return productNode
          })

          if (productItems.length > 0) {
            transformed.children.push(...productItems)
          }
        }

        return transformed
      })
    },
    deleteFromAlpha (item, typeSelect) {
      // console.log(item, typeSelect, 'aaaaaalpha')
      item.productIdFail = true
      if (typeSelect === 'alpha') {
        this.forceRerenderAlpha()
        this.dataAlpha.forEach(q => {
          q.sub_category_Alpha.forEach(w => {
            w.product_list_Alpha2.forEach(e => {
              if (e.have_attribute_Alpha3 === 'no') {
                if (this.productList.some(m => m.productId === e.product_id_Alpha3)) {
                  e.isDisabled = false
                } else {
                  e.isDisabled = false
                }
              } else {
                e.sub_product_Alpha3.forEach(r => {
                  if (this.productList.some(n => n.productId === r.product_attribute_id_Alpha4)) {
                    r.isDisabled = false
                  } else {
                    r.isDisabled = false
                  }
                })
              }
            })
          })
        })
      }
    },
    deleteFromBeta (item, typeSelect) {
      // console.log(item, typeSelect, 'bbbbbbbeTa')
      item.productFreeIdFail = true
      if (typeSelect === 'beta') {
        this.forceRerenderBeta()
        this.dataBeta.forEach(q => {
          // console.log(q.category_id_Beta)
          q.sub_category_Beta.forEach(w => {
            w.product_list_Beta2.forEach(e => {
              if (e.have_attribute_Beta3 === 'no') {
                if (this.productList.some(m => m.productFreeId === e.product_id_Beta3)) {
                  e.isDisabled = false
                } else {
                  e.isDisabled = false
                }
              } else {
                e.sub_product_Beta3.forEach(r => {
                  if (this.productList.some(n => n.productFreeId === r.product_attribute_id_Beta4)) {
                    r.isDisabled = false
                  } else {
                    r.isDisabled = false
                  }
                })
              }
            })
          })
        })
      }
    },
    resetCouponDialog () {
      this.typeCoupon = ''
      this.couponName = ''
      this.couponCode = ''
      this.amountUse = ''
      this.amountCap = ''
      this.shopName = ''
      this.typeDiscount = 'baht'
      this.spendMinimum = ''
      this.discount = ''
      this.date = ''
      this.time = ''
      this.dialogStartDateUse = false
      this.sentStartDate = ''
      this.dateToShow = ''
      this.productList = []
    },
    seekingNull () {
      this.productList.forEach(e => {
        if (e.productId === null || e.productId === undefined) {
          e.productIdFail = false
        }
        if (e.productFreeId === null || e.productFreeId === undefined) {
          e.productFreeIdFail = false
        }
      })
    },
    searchInAttributes (productId, product) {
      for (var attribute of product.attributes) {
        if (attribute.product_attribute_id === productId) {
          return {
            product_id: product.product_id,
            product_attribute_id: productId
          }
        }
      }
      return null // ถ้าไม่เจอใน attributes
    },
    searchProductInList (productId, category) {
      for (var product of category.product_list) {
        if (product.product_id === productId) {
          // ถ้าเจอใน product_list
          return {
            product_id: product.product_id,
            product_attribute_id: '-1'
          }
        }
        // ถ้าไม่เจอใน product_list แต่มี attributes ให้ค้นหาใน attributes
        if (product.attributes && product.attributes.length > 0) {
          var attributeResult = this.searchInAttributes(productId, product)
          if (attributeResult) return attributeResult
        }
      }
      return null // ถ้าไม่เจอ
    },
    searchProductInCategories (productId, categoryList) {
      for (var category of categoryList) {
        // ค้นหาผลิตภัณฑ์ใน product_list
        var productResult = this.searchProductInList(productId, category)
        if (productResult) return productResult

        // ค้นหาผลิตภัณฑ์ใน sub_category ถ้ามี
        if (category.sub_category && category.sub_category.length > 0) {
          var subCategoryResult = this.searchProductInCategories(productId, category.sub_category)
          if (subCategoryResult) return subCategoryResult
        }
      }
      return null // ถ้าไม่เจอ
    },
    searchInAttributesFreeProduct (productId, product) {
      for (var attribute of product.attributes) {
        if (attribute.product_attribute_id === productId) {
          return {
            free_product_id: product.product_id,
            free_product_attribute_id: productId
          }
        }
      }
      return null // ถ้าไม่เจอใน attributes
    },
    searchProductInListFreeProduct (productId, category) {
      for (var product of category.product_list) {
        if (product.product_id === productId) {
          // ถ้าเจอใน product_list
          return {
            free_product_id: product.product_id,
            free_product_attribute_id: '-1'
          }
        }
        // ถ้าไม่เจอใน product_list แต่มี attributes ให้ค้นหาใน attributes
        if (product.attributes && product.attributes.length > 0) {
          var attributeResult = this.searchInAttributesFreeProduct(productId, product)
          if (attributeResult) return attributeResult
        }
      }
      return null // ถ้าไม่เจอ
    },
    searchProductInCategoriesFreeProduct (productId, categoryList) {
      for (var category of categoryList) {
        // ค้นหาผลิตภัณฑ์ใน product_list
        var productResult = this.searchProductInListFreeProduct(productId, category)
        if (productResult) return productResult

        // ค้นหาผลิตภัณฑ์ใน sub_category ถ้ามี
        if (category.sub_category && category.sub_category.length > 0) {
          var subCategoryResult = this.searchProductInCategoriesFreeProduct(productId, category.sub_category)
          if (subCategoryResult) return subCategoryResult
        }
      }
      return null // ถ้าไม่เจอ
    },
    openDialogFail (type) {
      // console.log('123')
      if (type === 'typeCoupon') {
        this.dialogFailContext = 'กรุณาระบุชนิดคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'couponName') {
        this.dialogFailContext = 'กรุณากรอกชื่อคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'amountUse') {
        this.dialogFailContext = 'กรุณากรอกจำนวนคูปองสูงสุด'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'sentStartDate') {
        this.dialogFailContext = 'กรุณากรอกวันที่เริ่มเก็บคูปอง'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'spendMinimum') {
        this.dialogFailContext = 'กรุณากรอกค่าใช้จ่ายขั้นต่ำ'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 1500)
      } else if (type === 'discountAmount') {
        this.dialogFailContext = 'กรุณาตรวจสอบการกรอกส่วนลดเป็นจำนวนเงิน'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 2000)
      } else if (type === 'discountAmountPercent') {
        this.dialogFailContext = 'กรุณาตรวจสอบการกรอกส่วนลดเป็นจำนวนเปอร์เซ็นต์'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 2000)
      } else if (type === 'productList') {
        this.dialogFailContext = 'กรุณากรอกสินค้าที่เข้าร่วม'
        this.dialogFail = true
        setTimeout(() => {
          this.dialogFail = false
        }, 2000)
      }
    },
    selectAll () {
      if (this.selectAllShop === true) {
        this.SelectShop = this.itemShop.map(item => item.id)
      } else {
        this.SelectShop = []
      }
    },
    findProductById (data, id) {
      let result = null
      for (const node of data) {
        if (node.id === id) {
          return node // เจอ id ที่ต้องการ
        }
        if (node.children) {
          result = this.findProductById(node.children, id) // ค้นหาใน children
          if (result) return result
        }
      }
      return result // คืนค่า null หากไม่พบ
    },
    async createCoupon () {
      // console.log('this.productList.length', this.productList.length)
      // console.log('this.productList', this.productList)
      var myCateory = []
      var mySubCateory = []
      var formData = new FormData()
      if (this.typeCoupon === '' || this.typeCoupon === null) {
        // console.log('เข้า typeCoupon')
        this.createFail = true
        this.openDialogFail('typeCoupon')
        return
      }
      if (this.couponName === '' || this.couponName === null) {
        // console.log('เข้า couponName')
        this.createFail = true
        this.openDialogFail('couponName')
        return
      }
      if (this.amountUse === '' || this.amountUse === null) {
        // console.log('เข้า amountUse')
        this.createFail = true
        this.openDialogFail('amountUse')
        return
      }
      if (this.sentStartDate === '' || this.sentStartDate === null) {
        // console.log('เข้า sentStartDate')
        this.createFail = true
        this.openDialogFail('sentStartDate')
        return
      }
      if ((this.typeCoupon === 'free_shipping' || this.typeCoupon === 'discount') && (this.spendMinimum === '' || this.spendMinimum === null || parseInt(this.spendMinimum) === 0)) {
        // console.log('เข้า spendMinimum')
        this.createFail = true
        this.openDialogFail('spendMinimum')
        return
      }
      if ((this.typeCoupon === 'free_shipping' || this.typeCoupon === 'discount') && (this.typeDiscount === 'baht' && (this.discount === '' || this.discount === null))) {
        // console.log('เข้า baht')
        this.createFail = true
        this.openDialogFail('discountAmount')
        return
      }
      if ((this.typeCoupon === 'free_shipping' || this.typeCoupon === 'discount') && (this.typeDiscount === 'percent' && (this.discount === '' || this.discount === null))) {
        // console.log('เข้า percent')
        this.createFail = true
        this.openDialogFail('discountAmountPercent')
        return
      }
      if (this.productList.length === 0) {
        // console.log('เข้า productList')
        this.createFail = true
        this.openDialogFail('productList')
        this.theRedP = false
        return
      }
      // this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 1500 })
      if (this.typeCoupon === 'free_product' && this.productList.length !== 0) {
        this.seekingNull()
        this.productList2 = [{
          product_id: 0,
          product_attribute_id: 0,
          minimumBuy: 0,
          free_product_id: 0,
          free_product_attribute_id: 0,
          totalFree: 0
        }]
        this.productList2 = await this.productList.map(product => {
          const productResult = this.searchProductInCategories(product.productId, this.dataListPrimeFreeProduct)
          const freeProductResult = this.searchProductInCategoriesFreeProduct(product.productFreeId, this.dataListPrimeFreeProduct)
          return {
            product_id: productResult.product_id,
            product_attribute_id: productResult.product_attribute_id,
            free_product_id: freeProductResult.free_product_id,
            free_product_attribute_id: freeProductResult.free_product_attribute_id,
            minimumBuy: product.productNum,
            totalFree: product.productFreeNum
          }
        })
      }
      if ((this.typeCoupon === 'free_product') && (this.productList2.length === 0)) {
        // console.log('เข้า productList2')
        this.createFail = true
        this.openDialogFail('productList')
        this.theRedP = false
        return
      }
      this.createFail = false
      if (this.typeCoupon === 'free_shipping' || this.typeCoupon === 'discount') {
        this.productList2 = []
        myCateory = []
        mySubCateory = []
        // console.log('discount productList', this.productList)
        this.productList.forEach(element => {
          // console.log('discount element', element)
          if (isNaN(Number(element))) {
            var categoryId = element.split('_').pop()
            if ((element.match(/_/g) || []).length === 1) {
              myCateory.push(categoryId)
            } else {
              mySubCateory.push(categoryId)
            }
          } else {
            const productDetail = this.findProductById(this.dataListPrimeShipping, element)
            if (productDetail.children !== undefined) {
              productDetail.children.forEach(element => {
                this.productList2.push({ product_id: productDetail.id, product_attribute_id: element.id })
              })
            } else {
              this.productList2.push({ product_id: productDetail.id, product_attribute_id: '-1' })
            }
          }
        })
      }
      // console.log('myCateory', myCateory)
      // console.log('mySubCateory', mySubCateory)
      const [day, month, yearBE] = this.sentStartDate.split('/')
      const yearAD = parseInt(yearBE) - 543
      const dateObj = new Date(`${yearAD}-${month}-${day}`)
      const StartDate = dateObj.toISOString().slice(0, 10)
      formData.append('coupon_name', this.couponName)
      formData.append('coupon_code', this.couponCode)
      formData.append('collect_startdate', StartDate + ' ' + '00:00:00')
      formData.append('use_startdate', StartDate + ' ' + '00:00:00')
      formData.append('coupon_type', this.typeCoupon)
      formData.append('quota', this.amountUse)
      formData.append('user_cap', this.amountCap)
      if (this.typeCoupon === 'free_shipping' || this.typeCoupon === 'discount') {
        formData.append('spend_minimum', this.spendMinimum)
        formData.append('discount_type', this.typeDiscount)
        formData.append('discount_amount', this.discount)
        formData.append('cateory', JSON.stringify(myCateory))
        formData.append('sub_category', JSON.stringify(mySubCateory))
        formData.append('raw_list', JSON.stringify(this.productList))
      }
      if (this.typeCoupon === 'free_product') {
        myCateory = []
        mySubCateory = []
        formData.append('cateory', JSON.stringify(myCateory))
        formData.append('sub_category', [JSON.stringify(mySubCateory)])
      }
      formData.append('product_list', JSON.stringify(this.productList2))
      formData.append('seller_shop_id', this.seller_shop_id)
      // console.log(formData, 'formData')
      if (this.createFail === false) {
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionscreateCoupon', formData)
        var res = await this.$store.state.ModuleManageCoupon.statecreateCoupon
        if (res.result === 'Success') {
          this.$store.commit('closeLoader')
          this.dialogTemplateCoupon = false
          this.dialogSuccess = true
          setTimeout(() => {
            this.dialogSuccess = false
          }, 2000)
          this.getdata()
        } else if (res.result === 'SKU duplicate') {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 5000 })
        } else if (res.message === 'collectStartDate not over useStartDate') {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', text: 'ควรกำหนดช่วงเวลาในการจัดเก็บเวาเชอร์ให้เริ่มต้นก่อนช่วงเวลาในการใช้งานเวาเชอร์', showConfirmButton: false, timer: 5000 })
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', text: 'กรุณาลองอีกครั้งในภายหลัง', showConfirmButton: false, timer: 3500 })
        }
      }
      // console.log(res)
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.title1 {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.subTitle1 {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.detail1 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.rule {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #C4C4C4;
}
::v-deep .v-text-field--outlined.v-input--dense.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 36px;
}
</style>
