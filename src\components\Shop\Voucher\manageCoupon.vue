<template>
  <v-container :class="MobileSize ? 'background_productMobile mt-3' : 'background_product pa-6'" style="background-color: #FFFFFF">
    <v-row>
      <v-col>
        <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">จัดการคูปอง</span>
      </v-col>
      <v-col style="display: flex; justify-content: flex-end;">
        <v-btn style="border: 1px solid #27AB9C" outlined rounded @click="dialogCoupon01 = true"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">เพิ่มคูปอง</span></v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="6" sm="6">
        <v-text-field v-model="textSearch" placeholder="ค้นหารายการโปรโมชัน" outlined dense hide-details @click="current = 1">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" md="4" sm="4">
        <v-select class="setCustomSelect vSelectLineHeight" v-model="searchByType" :items="type" item-text="title" item-value="value" outlined dense hide-details @click="current = 1"></v-select>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <span class="pb-0" style="font-weight: 600; font-size:16px; line-height: 24px;">โปรโมชันทั้งหมด({{ numOfAll }})</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="4" sm="6" v-for="(itemmain, index) in paginated" :key="index">
        <v-card outlined style="display: flex; flex-direction: column; border: 1px solid #F2F2F2; border-radius: 12px;" width="100%" height="100%">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-row dense class="pl-2 pt-1"  style="display: flex; align-items: center;">
              <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">การแสดงโปรโมชัน</span>
              <v-switch color="#52C41A" @change="changePls(itemmain)" v-model="itemmain.show_status" true-value="active" false-value="inactive" hide-details inset class="pl-4"></v-switch>
            </v-row>
            <v-spacer></v-spacer>
            <v-menu bottom left :offset-y="true">
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#27AB9C" icon v-bind="attrs" v-on="on" >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <!-- List menu promotions -->
            <v-list>
              <v-list-item
                v-for="(item, i) in actionChoice"
                :key="i"
                @click="item.value === 'edit' ? gateOpen(itemmain) : openDialogdel(itemmain)"
                :disabled="item.value === 'delete' && itemmain.can_delete === 'no'"
              >
                <v-list-item-icon class="mr-1">
                  <v-icon color='#27AB9C'>{{ item.icon }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
          </v-app-bar>
          <v-card-text>
            <v-row>
              <v-col>
                <v-img v-if="itemmain.coupon_image !== null" :src="itemmain.coupon_image" height="150" contain></v-img>
                <v-img v-else src="@/assets/coupon_image/empty_coupon.png" contain height="150"></v-img>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="pb-0">
                <v-card-title class="px-1 pb-2"><span style="font-weight: 700; font-size: 18px; line-height: 28px;">{{ itemmain.coupon_name | truncate(30, '...') }}</span></v-card-title>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="pt-0">
                <v-card-subtitle class="px-1 pt-1 pb-0"><span style=" font-size: 14px; line-height: 24px;">โค้ดคูปอง:</span><span v-if="itemmain.coupon_type !== 'free_product'" style="font-weight: 600; font-size: 14px; line-height: 24px;"> {{ itemmain.coupon_code }} </span><span v-else style="font-weight: 600; font-size: 14px; line-height: 24px;"> -</span></v-card-subtitle>
              </v-col>
            </v-row>
            <v-row class="px-1">
              <v-col>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ itemmain.coupon_type === 'discount' ? 'ส่วนลด' : itemmain.coupon_type === 'free_shipping' ? 'ฟรีค่าจัดส่ง' : itemmain.coupon_type === 'free_product' ? 'X แถม Y' : '' }}</span>
              </v-col>
              <v-col style="display: flex; justify-content: flex-end;">
                <span v-if="itemmain.user_cap"><span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ itemmain.user_cap }} ครั้ง</span> / ผู้ใช้</span>
                <span v-else><span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">ใช้ได้ไม่จำกัดครั้ง </span>/ ผู้ใช้</span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text class="py-2" style="background-color: #d1edea;">
            <v-row>
              <v-col style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <span>ใช้ไปแล้ว</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{itemmain.use_count}}</span>
              </v-col>
              <v-divider class="my-3" vertical style="border: 1px solid #FFFFFF; height: 50px;" />
              <v-col style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <span>คงเหลือ</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ itemmain.quota - itemmain.use_count }}</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-if="paginated.length > 0">
      <!-- <v-pagination color="#27AB9C" v-model="pageNumber" :length="(searchByType === 'all' || searchByType === '')  && textSearch === '' ? pageMax : 1" :total-visible="10" @input="pageChange()">
      </v-pagination> -->
      <v-pagination color="#27AB9C" v-model="pageNumber" :length="checkLengthPagination()" :total-visible="10" ></v-pagination>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-else>
      <span>ไม่พบโปรโมชัน</span>
    </v-row>
    <v-dialog v-model="dialogCoupon01" width="500px" round>
      <v-card style="max-width: 500px;">
        <v-toolbar height="50px" style="background-color: #d1edea;">
          <v-row dense class="ma-2">
            <v-col style="display: flex; align-items: center; justify-content: center;">
              <span style="font-size: 20px; font-weight: 700;">เพิ่มคูปอง</span>
            </v-col>
          </v-row>
        </v-toolbar>
        <v-col>
          <v-row class="pa-3">
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" width="185" elevation="0" @click="openPath('discount')">
                <v-col>
                  <v-row>
                    <v-img src="@/assets/coupon_image/discount.png" height="140" width="125"></v-img>
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      ส่วนลด
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" width="185" elevation="0" @click="openPath('freeshippng')">
                <v-col>
                  <v-row>
                    <v-img src="@/assets/coupon_image/shipping.png" height="140" width="125"></v-img>
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      ฟรีค่าจัดส่ง
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" width="185" elevation="0" @click="openPath('xandy')">
                <v-col>
                  <v-row>
                    <v-img src="@/assets/coupon_image/xgifty.png" height="140" width="125"></v-img>
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      X แถม Y
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDelForsure" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 22px; font-weight: 700;">ลบโปรโมชัน</span>
          </v-row>
          <v-btn fab small @click="closeDialog1()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text style="height: 150px !important; display: flex; justify-content: center; align-items: center;">
            <v-col>
              <v-row class="pb-2" style="justify-content: center;">
                <span style="font-size: 18px; font-weight: 600;">ต้องการลบโปรโมชัน " {{ itemForDelName }} " หรือไม่</span>
              </v-row>
            </v-col>
          </v-card-text>
          <v-card-actions>
            <v-row>
              <v-col style=" display: flex; justify-content: center; align-items: center;">
                <v-btn rounded color="#27AB9C" height="40" width="120" outlined @click="closeDialog1()" elevation="0">ยกเลิก</v-btn>
              </v-col>
              <v-col style=" display: flex; justify-content: center; align-items: center;">
                <v-btn rounded color="#27AB9C" height="40" width="120" style="color: #FFFFFF" @click="delStart()" elevation="0">ยืนยัน</v-btn>
              </v-col>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- <pre> -->
      <!-- {{ paginated }} -->
      <!-- {{ dataMain }} -->
    <!-- </pre> -->
  </v-container>
</template>

<script>
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: {
  },
  data () {
    return {
      seller_shop_id: '',
      pageMax: null,
      current: 1,
      pageSize: 6,
      textSearch: '',
      countPromotion: 0,
      dialogCoupon01: false,
      type:
      [
        { title: 'ทั้งหมด', value: 'all' },
        { title: 'โค้ดส่วนลด', value: 'discount' },
        { title: 'ฟรีค่าจัดส่ง', value: 'free_shipping' },
        { title: 'ซื้อ X แถม Y', value: 'free_product' }
      ],
      actionChoice: [
        { title: 'แก้ไขโปรโมชัน', value: 'edit', icon: 'mdi-pencil', link: '' },
        { title: 'ลบโปรโมชัน', value: 'delete', icon: 'mdi-delete', link: '' }
      ],
      searchByType: 'all',
      dataMain: [],
      itemForDel: null,
      itemForDelName: null,
      dialogDelForsure: false
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        // document.getElementById('promotion').scrollIntoView()
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.textSearch === '' && this.searchByType === 'all') {
        return this.dataMain.slice(this.indexStart, this.indexEnd)
      } else {
        var items = this.dataMain.filter(e => {
          if (this.textSearch !== '' && this.searchByType === 'all') {
            return e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          } else if (this.textSearch === '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase())
          } else if (this.textSearch !== '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase()) && e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          }
        })
        return items.slice(this.indexStart, this.indexEnd)
      }
    },
    numOfAll () {
      if (this.textSearch === '' && this.searchByType === 'all') {
        return this.dataMain.length
      } else {
        var items = this.dataMain.filter(e => {
          if (this.textSearch !== '' && this.searchByType === 'all') {
            return e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          } else if (this.textSearch === '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase())
          } else if (this.textSearch !== '' && this.searchByType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase()) && e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
          }
        })
        return items.length
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('CheckShop')
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    this.getdata()
  },
  methods: {
    async delStart () {
      this.$store.commit('openLoader')
      var data = {
        coupon_id: this.itemForDel.id
      }
      await this.$store.dispatch('actionschangeDeleteCoupon', data)
      const res2 = await this.$store.state.ModuleManageCoupon.statechangeDeleteCoupon
      if (res2.result === 'Success') {
        this.closeDialog1()
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'ลบโปรโมชันเสร็จสิ้น', showConfirmButton: false, timer: 2500 })
        // setTimeout(() => {
        //   window.location.reload()
        // }, 2000)
        await this.getdata()
      } else {
        this.closeDialog1()
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${res2.message}`, showConfirmButton: false, timer: 2500 })
      }
    },
    closeDialog1 () {
      this.itemForDel = null
      this.itemForDelName = null
      this.dialogDelForsure = false
    },
    openDialogdel (item) {
      this.itemForDel = item
      this.itemForDelName = item.coupon_name
      this.dialogDelForsure = true
    },
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    gateOpen (item) {
      // console.log(item, '9000000')
      if (item.coupon_type === 'discount') {
        if (this.MobileSize) {
          this.$router.push({ path: `/coupondiscountMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/coupondiscount?status=edit&id=${item.id}` }).catch(() => {})
        }
      } else if (item.coupon_type === 'free_shipping') {
        if (this.MobileSize) {
          this.$router.push({ path: `/couponfreeshippingMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/couponfreeshipping?status=edit&id=${item.id}` }).catch(() => {})
        }
      } else if (item.coupon_type === 'free_product') {
        if (this.MobileSize) {
          this.$router.push({ path: `/couponfreeproductMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/couponfreeproduct?status=edit&id=${item.id}` }).catch(() => {})
        }
      }
    },
    openPath (path) {
      if (path === 'discount') {
        if (this.MobileSize) {
          this.$router.push({ path: '/coupondiscountMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/coupondiscount' }).catch(() => {})
        }
      } else if (path === 'freeshippng') {
        if (this.MobileSize) {
          this.$router.push({ path: '/couponfreeshippingMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/couponfreeshipping' }).catch(() => {})
        }
      } else if (path === 'xandy') {
        if (this.MobileSize) {
          this.$router.push({ path: '/couponfreeproductMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/couponfreeproduct' }).catch(() => {})
        }
      }
    },
    async changePls (item) {
      this.$store.commit('openLoader')
      var data = {
        coupon_id: item.id
      }
      await this.$store.dispatch('actionschangeStatusCoupon', data)
      const res = await this.$store.state.ModuleManageCoupon.statechangeStatusCoupon
      // console.log(res, 'res')
      if (res.result === 'Success') {
      } else {
      }
      this.$store.commit('closeLoader')
    },
    async getdata () {
      this.$store.commit('openLoader')
      this.dataMain = []
      var data = {
        shop_id: this.seller_shop_id,
        coupon_status: null
      }
      await this.$store.dispatch('actionsgetCoupon', data)
      const res2 = await this.$store.state.ModuleManageCoupon.stategetCoupon
      if (res2.result === 'Success') {
        this.dataMain = res2.data.coupon
        // console.log('dataMain', this.dataMain)
        this.pageMax = parseInt(this.dataMain.length / 6) === 0 ? 1 : Math.ceil(this.dataMain.length / 6)
        this.$store.commit('closeLoader')
      } else {
        this.dataMain = []
        this.$store.commit('closeLoader')
      }
      // console.log(this.dataMain, 'res2')
    },
    checkLengthPagination () { // คำนวณจำนวน pagnation
      // console.log('first')
      // ถูกเรียกจาก pagination ด้านบน
      // Math.ceil ปัดขึ้นตลอด
      var max = null
      var items = this.dataMain.filter(e => {
        if (this.textSearch !== '' && this.searchByType === 'all') {
          return e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
        } else if (this.textSearch === '' && this.searchByType !== 'all') {
          return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase())
        } else if (this.textSearch !== '' && this.searchByType !== 'all') {
          return e.coupon_type.toLowerCase() === (this.searchByType.toLowerCase()) && e.coupon_name.toLowerCase().includes(this.textSearch.toLowerCase())
        } else {
          return this.dataMain
        }
      })
      this.countPromotion = items.length
      max = Math.ceil(items.length / this.pageSize)
      this.pageNumber = this.pageNumber > max ? max : this.pageNumber
      return max
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.title1 {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.subTitle1 {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.detail1 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.rule {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #C4C4C4;
}
</style>
