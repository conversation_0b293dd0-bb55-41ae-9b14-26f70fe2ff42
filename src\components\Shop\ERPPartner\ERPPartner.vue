<template>
  <v-container style="height: 100%" :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card
      width="100%"
      height="100%"
      elevation="0"
      :class="MobileSize ? 'px-2' : 'px-2'"
    >
      <v-row class="mb-4 px-0">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-5' : 'px-0'">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title
              style="
                font-weight: bold;
                font-size: 24px;
                line-height: 32px;
                color: #333333;
              "
              >เชื่อมต่อบริการ Partner</v-card-title
            >
          </v-row>
          <v-row v-else>
            <v-card-title
              class="pl-2"
              style="
                font-weight: bold;
                font-size: 18px;
                line-height: 32px;
                color: #333333;
              "
            >
              <v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()"
                >mdi-chevron-left</v-icon
              >เชื่อมต่อบริการ Partner</v-card-title
            >
          </v-row>
        </v-col>
        <v-col
          cols="12"
          sm="12"
          align="end"
          class="pa-2"
          v-if="!MobileSize && !IpadSize"
        >
          <v-btn
            :block="MobileSize"
            rounded
            outlined
            class="pt-0"
            width="153"
            height="40"
            :class="MobileSize ? 'mt-0' : 'mt-0 '"
            color="#27AB9C"
            dark
            @click="openEX()"
            >ตัวอย่างข้อมูล</v-btn
          >
          <v-btn
            :block="MobileSize"
            rounded
            class="pt-0 ml-2"
            width="153"
            height="40"
            :class="MobileSize ? 'mt-0' : 'mt-0 '"
            color="#27AB9C"
            dark
            @click="DetailCreateEdit('create')"
            >เพิ่มการเชื่อมต่อ</v-btn
          >
        </v-col>

        <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาข้อมูลจากชื่อร้านค้า Partner" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
        <v-col cols="12">
          <v-row>
            <v-col v-if="!MobileSize && !IpadSize" cols="12" class="d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                วันที่เชื่อมบริการ :
              </span>
              <v-col cols="3">
                <v-dialog
                  ref="modalRangeDate"
                  v-model="modalRangeDate"
                  :return-value.sync="dateRange"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="RangeDate1"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      dense
                      hide-details
                      placeholder="วว/ดด/ปปปป"
                      style="border-radius: 8px;"
                    >
                      <v-icon slot="append" color="#CCCCCC">
                        mdi-calendar-multiselect
                      </v-icon>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    color="#27AB9C"
                    v-model="dateRange"
                    scrollable
                    reactive
                    locale="Th-th"
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="red" @click="CloseModalRangeDate()">
                      ยกเลิก
                    </v-btn>
                    <v-btn text color="primary" @click="setValueRangeDate(dateRange)">
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                    ประเภทบริการ :
              </span>
              <v-col cols="3">
                <v-select
                    class="setCustomSelect"
                    v-model="selectType"
                    :items="itemsSelect"
                    append-icon="mdi-chevron-down"
                    :menu-props="{ offsetY: true }"
                    item-text="text"
                    item-value="value"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
              <span class="pl-3" style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                    สถานะ :
                  </span>
              <v-col cols="3">
                <v-select
                    class="setCustomSelect"
                    v-model="selectStatus"
                    :items="itemsStatus"
                    append-icon="mdi-chevron-down"
                    :menu-props="{ offsetY: true }"
                    item-text="text"
                    item-value="value"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                วันที่เชื่อมบริการ :
              </span>
              <v-col cols="5" class="pa-2">
                <v-dialog
                  ref="modalRangeDate"
                  v-model="modalRangeDate"
                  :return-value.sync="dateRange"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="RangeDate1"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      dense
                      hide-details
                      placeholder="วว/ดด/ปปปป"
                      style="border-radius: 8px;"
                    >
                      <v-icon slot="append" color="#CCCCCC">
                        mdi-calendar-multiselect
                      </v-icon>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    color="#27AB9C"
                    v-model="dateRange"
                    scrollable
                    reactive
                    locale="Th-th"
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="red" @click="CloseModalRangeDate()">
                      ยกเลิก
                    </v-btn>
                    <v-btn text color="primary" @click="setValueRangeDate(dateRange)">
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                    ประเภทบริการ :
              </span>
              <v-col cols="5" class="pa-2">
                <v-select
                    class="setCustomSelect"
                    v-model="selectType"
                    :items="itemsSelect"
                    append-icon="mdi-chevron-down"
                    :menu-props="{ offsetY: true }"
                    item-text="text"
                    item-value="value"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
            <v-col v-if="MobileSize || IpadSize" cols="12" class="pt-2 pb-2 d-flex flex-row" style="align-items: center;">
              <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                    สถานะ :
                  </span>
              <v-col cols="5" class="pa-2">
                <v-select
                    class="setCustomSelect"
                    v-model="selectStatus"
                    :items="itemsStatus"
                    append-icon="mdi-chevron-down"
                    :menu-props="{ offsetY: true }"
                    item-text="text"
                    item-value="value"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
              </v-col>
            </v-col>
          </v-row>
        </v-col>

        <v-col cols="12" v-if="!MobileSize && !IpadSize">
          <v-avatar rounded size="25">
            <v-img contain src="@/assets/Marketplace_partner/GroupPartner.png">
            </v-img>
          </v-avatar>
          <span class="pl-3" style="font-weight: 600;  color: #27AB9C"><b>รายการเชื่อมต่อทั้งหมด {{ countData }} รายการ</b></span>
        </v-col>
        <v-col cols="12" v-if="MobileSize" class="d-flex align-center">
          <v-avatar rounded size="25">
            <v-img contain src="@/assets/Marketplace_partner/GroupPartner.png">
            </v-img>
          </v-avatar>
          <span class="pl-3" style="font-weight: 600; color: #27AB9C"><b>รายการเชื่อมต่อทั้งหมด {{ countData }} รายการ</b></span>
        </v-col>
        <v-col cols="12" v-if="IpadSize" class="d-flex align-center">
          <v-avatar rounded size="25">
            <v-img contain src="@/assets/Marketplace_partner/GroupPartner.png">
            </v-img>
          </v-avatar>
          <span class="pl-3" style="font-weight: 600; color: #27AB9C"><b>รายการเชื่อมต่อทั้งหมด {{ countData }} รายการ</b></span>
        </v-col>
        <v-col
          cols="12"
          class="mt-1"
          v-if="MobileSize"
        >
          <v-btn
            rounded
            outlined
            class="pt-0 mb-2"
            width="100%"
            height="35"
            color="#27AB9C"
            dark
            @click="openEX()"
            >ตัวอย่างข้อมูล</v-btn
          >
          <v-btn
            rounded
            class="pt-0"
            width="100%"
            height="35"
            color="#27AB9C"
            dark
            @click="DetailCreateEdit('create')"
            >เพิ่มการเชื่อมต่อ</v-btn
          >
        </v-col>
        <v-col
          cols="12"
          align="end"
          class="mt-1"
          v-if="IpadSize"
        >
            <v-btn
            rounded
            outlined
            class="pt-0 mb-2"
            width="100%"
            height="35"
            color="#27AB9C"
            dark
            @click="openEX()"
            >ตัวอย่างข้อมูล</v-btn
          >
          <v-btn
            rounded
            class="pt-0"
            width="100%"
            height="35"
            color="#27AB9C"
            dark
            @click="DetailCreateEdit('create')"
            >เพิ่มการเชื่อมต่อ</v-btn
          >
        </v-col>
        <v-col v-if="countData !== 0">
          <v-data-table
            :headers="headers"
            :items="filteredPartners"
            :search="search"
            no-data-text="ไม่พบรายการ Partner ที่เชื่อมต่อ"
            :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
            :update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 px-0"
          >
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'active'">
                <v-chip class="ma-0" color="#F0F9EE" text-color="#1AB759"><b>กำลังใช้งาน</b></v-chip>
              </span>
              <span v-else-if="item.status === 'inactive'">
                <v-chip class="ma-0" color="#FBE5E4" text-color="#F5222D"><b>ปิดการใช้งาน</b></v-chip>
              </span>
              <span v-else-if="item.status === 'suspended'">
                <v-chip class="ma-0" color="#EBEBEB" text-color="#9A9A9A"><b>ระงับการใช้งาน</b></v-chip>
              </span>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              <span v-if="item.created_at">
                {{ new Date(item.created_at).toLocaleDateString('th-TH', {
                  timeZone: "UTC",
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric'
                }) }}
              </span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.service_provider_type`]="{ item }">
              <v-row dense justify="center" align="center">
                <div style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px; max-width: 250px;">
                  <!-- <v-chip-group style="display: flex; flex-wrap: nowrap; min-width: fit-content;"> -->
                  <v-chip-group style="display: flex; flex-wrap: nowrap;">
                    <v-chip v-if="item.service_provider_type.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b></v-chip>
                    <v-chip v-if="item.service_provider_type.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b></v-chip>
                    <v-chip v-if="item.service_provider_type.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>POS</b></v-chip>
                    <v-chip v-if="item.service_provider_type.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>OMS</b></v-chip>
                    <v-chip v-if="item.service_provider_type.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b></v-chip>
                  </v-chip-group>
                </div>
              </v-row>
            </template>
            <template v-slot:[`item.storename`]="{ item }">
              <span @click="copyToClipboard(item.storename)" class="d-inline-block" :style="MobileSize ? 'max-width: 200px; cursor: pointer;' : 'max-width: 100%; cursor: pointer;'">{{item.storename}}</span>
            </template>
            <template v-slot:[`item.api_key`]="{ item }">
              <span @click="copyToClipboard(item.api_key)" class="d-inline-block" :style="MobileSize ? 'max-width: 200px; cursor: pointer;' : 'max-width: 100%; cursor: pointer;'">{{item.api_key}}</span>
            </template>
            <template v-slot:[`item.api_secret`]="{ item }">
              <span @click="copyToClipboard(item.api_secret)" class="d-inline-block" :style="MobileSize ? 'max-width: 200px; cursor: pointer;' : 'max-width: 100%; cursor: pointer;'">{{item.api_secret}}</span>
            </template>
            <template v-slot:[`item.have_sso`]="{ item }">
              <div v-if="item.have_sso === 'yes'">
                <v-btn dark small color="#27AB9C" @click="dialogOpenLoginSSO(item)"><v-icon>mdi-login</v-icon></v-btn>
              </div>
              <div v-else>
                ไม่มีระบบ Partner
              </div>
            </template>
            <template v-slot:[`item.manage`]="{ item }">
              <v-row no-gutters justify="space-around" align="center">
                <v-card class="" title="รายละเอียด">
                  <v-btn text color="#27AB9C" small @click="DetailCreateEdit('detail', item)">
                    <v-icon class="pr-1" color="27AB9C">mdi-file-document-outline</v-icon>
                  </v-btn>
                </v-card>
              </v-row>
            </template>
          </v-data-table>
        </v-col>
        <v-col v-else class="mt-8">
          <v-col class="py-0" cols="12" align="center">
            <v-img width="300" height="300" src="@/assets/Marketplace_partner/OBJECTS.png"></v-img>
          </v-col>
          <v-col class="pb-0 pt-4" cols="12" align="center">
            <span style="font-size: 20px; font-weight: 700;">ยังไม่มีข้อมูลรายการเชื่อมต่อ</span>
          </v-col>
        </v-col>
      </v-row>
    </v-card>

    <v-dialog v-model="DialogPartner" :width="MobileSize ? '100%' : IpadSize ? '100%' : '900'" persistent>
      <v-card
      elevation="0"
      style="background: #ffffff; border-radius: 24px; overflow-x: hidden; overflow-y: hidden"
    >
      <v-form ref="FormSettingTier" :lazy-validation="lazy">
        <v-card-text class="px-0 pt-0">
          <div
            :style="
              MobileSize
                ? 'width: 100%'
                : IpadSize
                ? 'width: 100%'
                : 'width: 900px'
            "
            class="backgroundHead"
            style="position: absolute; height: 120px"
          >
            <v-row style="height: 120px">
              <v-col style="text-align: center" class="pt-4">
                <span
                  :class="
                    MobileSize
                      ? 'title-mobile white--text'
                      : 'title white--text'
                  "
                  :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"
                  ><b>{{title}}</b></span
                >
              </v-col>
              <v-btn fab small @click="cancel()" icon class="mt-3"
                ><v-icon color="white">mdi-close</v-icon></v-btn
              >
            </v-row>
          </div>
          <div
            style="
              position: relative;
              padding: 0px 12px 0px;
              display: flex;
              padding-top: 60px;
            "
          >
            <v-row
              :width="MobileSize ? '100%' : '650px'"
              style="
                height: 50px;
                border-radius: 24px 24px 0px 0px;
                background: #ffffff;
              "
            >
              <v-col style="text-align: center"> </v-col>
            </v-row>
          </div>
          <div class="" style="position: relative">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #ffffff; border-radius: 20px 20px 0px 0px"
              :style="
                MobileSize
                  ? 'padding: 20px 20px 10px 20px;'
                  : 'padding: 30px 30px 10px 30px;'
              "
            >
              <v-col class="pa-0">
                <v-col cols="12" md="12" sm="12" class="pa-0" v-if="title === 'เพิ่มการเชื่อมต่อ'">
                  <v-tabs
                    grow
                    v-model="selectedTab"
                    background-color="#27AB9C"
                    dark
                    style="border-radius: 5px;"
                  >
                    <v-tab :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'"><b>มีข้อมูล API Key จาก Partner</b></v-tab>
                    <v-tab :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'"><b>ไม่มีข้อมูล API Key จาก Partner</b></v-tab>
                  </v-tabs>
                </v-col>
                <div v-if="title === 'เพิ่มการเชื่อมต่อ'">
                  <v-col cols="12" md="12" sm="12" class="text-center pa-6">
                    <span :style="MobileSize ? 'font-weight: 700; font-size: 16px; color:black;' : 'font-weight: 700; font-size: 18px; color:black;'"><b>เลือกร้านค้า Partner ที่สนใจเข้าร่วมเพื่อกรอกข้อมูล</b></span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pa-0">
                    <span style="font-weight: 700; font-size: 16px; color:black;">
                      ชื่อร้านค้า Partner ที่สนใจเข้าร่วม <span style="color: red;">*</span>
                    </span><br>
                    <v-select
                      outlined
                      dense
                      v-model="selectedPartner"
                      :items="dataListInterestedPartner"
                      item-text="partner_name"
                      item-value="partner_name"
                      no-data-text="ไม่มีร้านค้า Partner ที่สนใจเข้าร่วม"
                      :rules="Rules.empty"
                      :menu-props="{ closeOnContentClick: true }"
                      class="hide-input"
                    >
                      <!-- แสดงชื่อ Partner + ประเภทบริการที่เลือก -->
                      <template v-slot:selection="{ item }">
                        <v-row dense class="align-center">
                          <div class="flex-grow-1 mr-auto">
                            <span>{{ item.partner_name }}</span>
                          </div>
                          <div class="ml-auto chip-wrapper">
                            <v-chip v-if="item.service_type.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" class="ma-1"><b>ERP</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;" class="ma-1"><b>Web Development</b></v-chip>
                            <v-chip v-if="item.service_type.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;" class="ma-1"><b>POS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;" class="ma-1"><b>OMS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;" class="ma-1"><b>Marketing</b></v-chip>
                          </div>
                        </v-row>
                        <!-- <div class="d-flex align-center">
                          <div class="flex-grow-1">
                            <span>{{ item.partner_name }}</span>
                          </div>
                          <div class="chip-wrapper">
                            <v-chip v-if="item.service_type.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" class="ma-1"><b>ERP</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;" class="ma-1"><b>Web Development</b></v-chip>
                            <v-chip v-if="item.service_type.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;" class="ma-1"><b>POS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;" class="ma-1"><b>OMS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;" class="ma-1"><b>Marketing</b></v-chip>
                          </div>
                        </div> -->
                      </template>

                      <!-- Dropdown List -->
                      <template v-slot:item="{ item }">
                        <v-row dense class="align-center" @click="selectItem(item)">
                          <div class="mr-auto">
                            <span>{{ item.partner_name }}</span>
                          </div>
                          <div class="ml-auto chip-wrapper">
                            <v-chip v-if="item.service_type.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" class="ma-1"><b>ERP</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;" class="ma-1"><b>Web Development</b></v-chip>
                            <v-chip v-if="item.service_type.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;" class="ma-1"><b>POS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;" class="ma-1"><b>OMS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;" class="ma-1"><b>Marketing</b></v-chip>
                          </div>
                        </v-row>
                        <!-- <div class="d-flex align-center" @click="selectItem(item)">
                          <div class="flex-grow-1">
                            <span>{{ item.partner_name }}</span>
                          </div>
                          <div class="chip-wrapper">
                            <v-chip v-if="item.service_type.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" class="ma-1"><b>ERP</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;" class="ma-1"><b>Web Development</b></v-chip>
                            <v-chip v-if="item.service_type.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;" class="ma-1"><b>POS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;" class="ma-1"><b>OMS</b></v-chip>
                            <v-chip v-if="item.service_type.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;" class="ma-1"><b>Marketing</b></v-chip>
                          </div>
                        </div> -->
                      </template>
                    </v-select>
                  </v-col>
                </div>
                <div v-if="selectedTab === 0">
                  <v-card style="background-color: #E6F5F3;">
                    <v-card-text :class="MobileSize ? '' : ''">
                      <v-form ref="formERPPartnerIsAPI" :lazy-validation="lazy">
                        <v-row>
                          <v-col cols="12" class="pb-0">
                            <v-row dense>
                              <v-col cols="12" md="6" sm="6" v-if="title !== 'เพิ่มการเชื่อมต่อ'">
                                <span style="font-weight: 700; font-size: 16px; color:black;">ชื่อ Partner <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="partnerName" outlined dense :rules="Rules.empty"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="6" sm="6" v-if="title !== 'เพิ่มการเชื่อมต่อ'">
                                <span style="font-weight: 700; font-size: 16px; color:black;">Service Type</span><br>
                                <!-- <v-text-field v-if="selectedPartnerDetails" :value="selectedPartnerDetails.service_type.join(', ')" outlined dense disabled></v-text-field> -->
                                 <v-card elevation="0">
                                  <div style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
                                    <v-chip-group style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                                      <v-chip v-if="serviceType.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" class="ma-1"><b>ERP</b></v-chip>
                                      <v-chip v-if="serviceType.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;" class="ma-1"><b>Web Development</b></v-chip>
                                      <v-chip v-if="serviceType.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;" class="ma-1"><b>POS</b></v-chip>
                                      <v-chip v-if="serviceType.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;" class="ma-1"><b>OMS</b></v-chip>
                                      <v-chip v-if="serviceType.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;" class="ma-1"><b>Marketing</b></v-chip>
                                    </v-chip-group>
                                  </div>
                                 </v-card>
                              </v-col>
                              <v-col cols="12">
                                <span style="font-weight: 700; font-size: 16px; color:black;">Storename <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="storename" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                              </v-col>
                              <v-col cols="12">
                                <span style="font-weight: 700; font-size: 16px; color:black;">Apikey <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="apikey" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                              </v-col>
                              <v-col cols="12">
                                <span style="font-weight: 700; font-size: 16px; color:black;">Apisecret <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="apisecret" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-row dense>
                                  <span style="font-weight: 700; font-size: 16px; color:black; margin-right: 15px;">ซิงค์รายการสินค้าอัตโนมัติ</span>
                                  <v-switch
                                    :disabled="title === 'รายละเอียด'"
                                    v-model="autoSyncProduct"
                                    inset
                                    :true-value="'yes'"
                                    :false-value="'no'"
                                    style="margin-top: 0px; padding-top: 0px;"
                                  ></v-switch>
                                </v-row>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-row dense>
                                  <span style="font-weight: 700; font-size: 16px; color:black; margin-right: 15px;">ซิงค์รายการสั่งซื้ออัตโนมัติ</span>
                                  <v-switch
                                    :disabled="title === 'รายละเอียด'"
                                    v-model="autoSyncOrder"
                                    inset
                                    :true-value="'yes'"
                                    :false-value="'no'"
                                    style="margin-top: 0px; padding-top: 0px;"
                                  ></v-switch>
                                </v-row>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-row dense>
                                  <span style="font-weight: 700; font-size: 16px; color:black; margin-right: 15px;">Partner เป็นคลังสินค้าหลัก</span>
                                  <v-switch
                                    :disabled="title === 'รายละเอียด'"
                                    v-model="autoMainExternalStock"
                                    inset
                                    :true-value="'yes'"
                                    :false-value="'no'"
                                    style="margin-top: 0px; padding-top: 0px;"
                                  ></v-switch>
                                </v-row>
                              </v-col>
                              <!-- <v-row v-if="autoSyncProduct === 'yes' || autoSyncOrder === 'yes'">
                                <v-col cols="12">
                                  <span style="font-weight: 700; font-size: 16px; color:black;">Key 1 <span style="color: red;">*</span></span>
                                  <v-text-field :disabled="title === 'รายละเอียด'" v-model="key1" outlined dense :rules="Rules.empty"></v-text-field>
                                </v-col>
                                <v-col cols="12">
                                  <span style="font-weight: 700; font-size: 16px; color:black;">Key 2 <span style="color: red;">*</span></span>
                                  <v-text-field :disabled="title === 'รายละเอียด'" v-model="key2" outlined dense :rules="Rules.empty"></v-text-field>
                                </v-col>
                                <v-col cols="12">
                                  <span style="font-weight: 700; font-size: 16px; color:black;">Key 3 <span style="color: red;">*</span></span>
                                  <v-text-field :disabled="title === 'รายละเอียด'" v-model="key3" outlined dense :rules="Rules.empty"></v-text-field>
                                </v-col>
                              </v-row> -->
                            </v-row>
                          </v-col>
                          <v-col v-if="this.title !== 'เพิ่มการเชื่อมต่อ'" cols="12" class="pb-0">
                            <v-row dense class="align-baseline">
                              <v-col cols="12">
                                <span style="font-weight: 700; font-size: 16px; color:black;">คลังสินค้า <span style="color: red;">*</span></span>
                                <v-select
                                  :disabled="title === 'รายละเอียด'"
                                  v-model="Warehouse"
                                  :items="warehouseOptions2"
                                  item-value="warehouse_id"
                                  item-text="warehouse_name"
                                  outlined
                                  dense
                                  :rules="Rules.empty"
                                >
                              </v-select>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-form>
                      <v-card-actions v-if="!MobileSize">
                        <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="red" class="white--text" rounded width="150" height="40" @click="closePartner()">ปิดการใช้งาน</v-btn>
                        <v-spacer></v-spacer>
                        <v-btn v-if="title === 'เพิ่มการเชื่อมต่อ'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="confirm()">เชื่อมต่อระบบ</v-btn>
                        <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="confirmEdit(DataPartner)">บันทึกการเชื่อมต่อ</v-btn>
                        <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'active'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="DetailCreateEdit('edit', DataPartner)">แก้ไขการเชื่อมต่อ</v-btn>
                        <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'inactive'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="openPartner(DataPartner)">เปิดการใช้งาน</v-btn>
                      </v-card-actions>
                      <v-card-actions class="mt-6" justify="center" v-else>
                        <v-row justify="center">
                          <v-btn v-if="title === 'เพิ่มการเชื่อมต่อ'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="confirm()">เชื่อมต่อระบบ</v-btn>
                          <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="confirmEdit(DataPartner)">บันทึกการเชื่อมต่อ</v-btn>
                          <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'active'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="DetailCreateEdit('edit', DataPartner)">แก้ไขการเชื่อมต่อ</v-btn>
                          <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'inactive'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="openPartner(DataPartner)">เปิดการใช้งาน</v-btn>
                          <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="red" class="white--text mt-2 mb-2" rounded width="80%" height="35" @click="closePartner()">ปิดการใช้งาน</v-btn>
                        </v-row>
                      </v-card-actions>
                    </v-card-text>
                  </v-card>
                </div>

                <div v-if="selectedTab === 1">
                  <v-card style="background-color: #E6F5F3;">
                    <v-card-text :class="MobileSize ? 'pa-2' : 'pa-6'">
                      <v-form ref="formERPPartnerNoAPI" :lazy-validation="lazy">
                        <v-row :hidden="title === 'เพิ่มการเชื่อมต่อ'">
                          <v-col cols="12" class="pb-0">
                            <v-row dense class="align-baseline">
                              <v-col cols="12" md="6" sm="6" v-if="title !== 'เพิ่มการเชื่อมต่อ'">
                                <span style="font-weight: 700; font-size: 16px; color:black;">ชื่อ Partner <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="partnerName" outlined dense :rules="Rules.empty"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="6" sm="6" v-if="title !== 'เพิ่มการเชื่อมต่อ'">
                                <span style="font-weight: 700; font-size: 16px; color:black;">Service Type</span><br>
                                <v-card elevation="0">
                                  <div style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
                                    <v-chip-group style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                                      <v-chip v-if="serviceType.includes('ERP')" label style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" class="ma-1"><b>ERP</b></v-chip>
                                      <v-chip v-if="serviceType.includes('Web Development')" label style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;" class="ma-1"><b>Web Development</b></v-chip>
                                      <v-chip v-if="serviceType.includes('POS')" label style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;" class="ma-1"><b>POS</b></v-chip>
                                      <v-chip v-if="serviceType.includes('OMS')" label style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;" class="ma-1"><b>OMS</b></v-chip>
                                      <v-chip v-if="serviceType.includes('Marketing')" label style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;" class="ma-1"><b>Marketing</b></v-chip>
                                    </v-chip-group>
                                  </div>
                                 </v-card>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">Storename <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="storename" outlined dense :rules="Rules.empty" @keypress="CheckSpacebar($event)"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">รหัสคลังสินค้า <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'แก้ไขการเชื่อมต่อ' || title === 'รายละเอียด'" v-model="warehouseCode" outlined dense :rules="Rules.empty" @keypress="CheckSpacebarV2($event)"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">ชื่อคลังสินค้า <span style="color: red;">*</span></span>
                                <v-text-field :disabled="title === 'รายละเอียด'" v-model="warehouseNames" outlined dense :rules="Rules.empty" @keypress="CheckSpacebarV2($event)"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">คลังสินค้าหลัก <span style="color: red;">*</span></span>
                                <v-radio-group
                                  class="pa-0"
                                  v-model="setdefaultWarehouse"
                                  :rules="Rules.empty"
                                  :row="true"
                                  :disabled="title === 'รายละเอียด'"
                                  style="margin-top: 8px;"
                                >
                                  <v-radio label="ใช่" value="yes"></v-radio>
                                  <v-radio label="ไม่ใช่" value="no"></v-radio>
                                </v-radio-group>
                              </v-col>
                              <v-col cols="12" md="4" sm="4">
                                <span style="font-weight: 700; font-size: 16px; color:black;">รายละเอียดที่อยู่</span>
                                <v-text-field
                                  :disabled="title === 'รายละเอียด'"
                                  v-model="detailAddress"
                                  outlined
                                  dense
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4" sm="4">
                                <span style="font-weight: 700; font-size: 16px; color:black;">บ้านเลขที่</span>
                                <v-text-field
                                  :disabled="title === 'รายละเอียด'"
                                  v-model="houseNo"
                                  outlined
                                  dense
                                  oninput="this.value = this.value.replace(/[^0-9\s\/]/g, '').replace(/(\..*)\./g, '$1')"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4" sm="4">
                                <span style="font-weight: 700; font-size: 16px; color:black;">หมู่ที่</span>
                                <v-text-field
                                  :disabled="title === 'รายละเอียด'"
                                  v-model="mooNo"
                                  outlined
                                  dense
                                  oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">ตำบล <span style="color: red;">*</span></span>
                                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" :disabled="title === 'รายละเอียด'" label="" v-model="subDistrict"/>
                                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">อำเภอ <span style="color: red;">*</span></span>
                                <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" :disabled="title === 'รายละเอียด'" v-model="district" />
                                <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">จังหวัด <span style="color: red;">*</span></span>
                                  <addressinput-province label="" :disabled="title === 'รายละเอียด'" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                              </v-col>
                              <v-col cols="12" md="6" sm="6">
                                <span style="font-weight: 700; font-size: 16px; color:black;">รหัสไปรษณีย์ <span style="color: red;">*</span></span>
                                  <addressinput-zipcode label="" :disabled="title === 'รายละเอียด'" v-model="zipCode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col v-if="this.title !== 'เพิ่มการเชื่อมต่อ'" cols="12" class="pb-0">
                            <v-row dense class="align-baseline">
                              <v-col cols="12">
                                <span style="font-weight: 700; font-size: 16px; color:black;">คลังสินค้า <span style="color: red;">*</span></span>
                                <v-select
                                  :disabled="title === 'รายละเอียด'"
                                  v-model="Warehouse"
                                  :items="warehouseOptions2"
                                  item-value="warehouse_id"
                                  item-text="warehouse_name"
                                  outlined
                                  dense
                                  :rules="Rules.empty"
                                >
                              </v-select>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-form>
                      <v-card-actions v-if="!MobileSize" :style="title === 'เพิ่มการเชื่อมต่อ' ? 'display: flex; justify-content: center;' : ''">
                        <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="red" class="white--text" rounded width="150" height="40" @click="closePartner()">ปิดการใช้งาน</v-btn>
                        <v-spacer v-if="title !== 'เพิ่มการเชื่อมต่อ'"></v-spacer>
                        <v-btn v-if="title === 'เพิ่มการเชื่อมต่อ'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="confirm()">เชื่อมต่อระบบ</v-btn>
                        <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="confirmEdit(DataPartner)">บันทึกการเชื่อมต่อ</v-btn>
                        <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'active'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="DetailCreateEdit('edit', DataPartner)">แก้ไขการเชื่อมต่อ</v-btn>
                        <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'inactive'" color="#27AB9C" rounded width="150" height="40" class="white--text" @click="openPartner(DataPartner)">เปิดการใช้งาน</v-btn>
                      </v-card-actions>
                      <v-card-actions class="mt-6" justify="center" v-else>
                        <v-row justify="center">
                          <v-btn v-if="title === 'เพิ่มการเชื่อมต่อ'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="confirm()">เชื่อมต่อระบบ</v-btn>
                          <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="confirmEdit(DataPartner)">บันทึกการเชื่อมต่อ</v-btn>
                          <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'active'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="DetailCreateEdit('edit', DataPartner)">แก้ไขการเชื่อมต่อ</v-btn>
                          <v-btn v-if="title === 'รายละเอียด' && DataPartner.status === 'inactive'" color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="openPartner(DataPartner)">เปิดการใช้งาน</v-btn>
                          <v-btn v-if="title === 'แก้ไขการเชื่อมต่อ'" color="red" class="white--text mt-2 mb-2" rounded width="80%" height="35" @click="closePartner()">ปิดการใช้งาน</v-btn>
                        </v-row>
                      </v-card-actions>
                    </v-card-text>
                  </v-card>
                </div>
              </v-col>
            </v-card>
          </div>
        </v-card-text>
      </v-form>
    </v-card>
    </v-dialog>

    <v-dialog v-model="DialogWarehouse" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card
      elevation="0"
      style="background: #ffffff; border-radius: 24px; overflow-x: hidden; overflow-y: hidden"
    >
      <v-form ref="FormSettingTier" :lazy-validation="lazy">
        <v-card-text class="px-0 pt-0">
          <div
            :style="
              MobileSize
                ? 'width: 100%'
                : IpadSize
                ? 'width: 100%'
                : 'width: 750px'
            "
            class="backgroundHead"
            style="position: absolute; height: 120px"
          >
            <v-row style="height: 120px">
              <v-col style="text-align: center" class="pt-4">
                <span
                  :class="
                    MobileSize
                      ? 'title-mobile white--text'
                      : 'title white--text'
                  "
                  :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"
                  ><b>เลือกคลังสินค้า</b></span
                >
              </v-col>
            </v-row>
          </div>
          <div
            style="
              position: relative;
              padding: 0px 12px 0px;
              display: flex;
              padding-top: 60px;
            "
          >
            <v-row
              :width="MobileSize ? '100%' : '650px'"
              style="
                height: 50px;
                border-radius: 24px 24px 0px 0px;
                background: #ffffff;
              "
            >
              <v-col style="text-align: center"> </v-col>
            </v-row>
          </div>
          <div class="" style="position: relative">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #ffffff; border-radius: 20px 20px 0px 0px"
              :style="
                MobileSize
                  ? 'padding: 20px 20px 10px 20px;'
                  : 'padding: 40px 48px 10px 48px;'
              "
            >
            <v-col class="pa-0">
              <v-card style="background-color: #E6F5F3;">
                <v-card-text :class="MobileSize ? 'pa-2' : 'pa-6'">
                  <v-col cols="12" class="px-0 mb-2 pt-0">
                      <v-row dense class="align-baseline">
                        <v-col cols="4">
                          <span style="font-weight: 700; font-size: 16px; color:black;">ชื่อ Partner</span>
                        </v-col>
                        <v-col cols="8" v-if="title === 'เพิ่มการเชื่อมต่อ'">
                              <v-select
                                outlined
                                dense
                                disabled
                                v-model="selectedPartner"
                                :items="this.dataListInterestedPartner"
                                item-text="partner_name"
                                item-value="partner_name"
                              ></v-select>
                        </v-col>
                        <v-col cols="8" v-else>
                          <v-text-field disabled v-model="partnerName" outlined dense></v-text-field>
                        </v-col>
                      </v-row>
                  </v-col>
                  <v-form ref="formERPPartner2" :lazy-validation="lazy">
                    <v-row>
                      <v-col cols="12" class="pb-0">
                        <v-row dense class="align-baseline">
                          <v-col cols="4">
                            <span style="font-weight: 700; font-size: 16px; color:black;">คลังสินค้า</span>
                          </v-col>
                          <v-col cols="8">
                            <v-select v-model="WarehousePartner" :items="warehouseOptions" item-value="warehouse_id" item-text="warehouse_name" outlined dense :rules="Rules.empty">
                            </v-select>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-form>
                  <v-card-actions v-if="!MobileSize" class="d-flex justify-end">
                    <v-btn color="#27AB9C" rounded width="150" height="40" class="white--text" @click="confirmEdit(ItemWarehouse)">บันทึก</v-btn>
                  </v-card-actions>
                  <v-card-actions class="mt-6" justify="center" v-else>
                    <v-row justify="center">
                      <v-btn color="#27AB9C" rounded width="80%" height="35" class="white--text mb-2" @click="confirmEdit(ItemWarehouse)">บันทึก</v-btn>
                    </v-row>
                  </v-card-actions>
                </v-card-text>
              </v-card>
            </v-col>
            </v-card>
          </div>
        </v-card-text>
      </v-form>
    </v-card>
    </v-dialog>

    <v-dialog v-model="DialogEX" :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" persistent>
      <v-card
      elevation="0"
      style="background: #ffffff; border-radius: 24px; overflow-x: hidden; overflow-y: hidden"
    >
      <v-form ref="FormSettingTier" :lazy-validation="lazy">
        <v-card-text class="px-0 pt-0">
          <div
            :style="
              MobileSize
                ? 'width: 100%'
                : IpadSize
                ? 'width: 100%'
                : 'width: 750px'
            "
            class="backgroundHead"
            style="position: absolute; height: 120px"
          >
            <v-row style="height: 120px">
              <v-col style="text-align: center" class="pt-4">
                <span
                  :class="
                    MobileSize
                      ? 'title-mobile white--text'
                      : 'title white--text'
                  "
                  :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"
                  ><b>ตัวอย่างข้อมูล</b></span
                >
              </v-col>
              <v-btn fab small @click="cancelEX()" icon class="mt-3"
                ><v-icon color="white">mdi-close</v-icon></v-btn
              >
            </v-row>
          </div>
          <div
            style="
              position: relative;
              padding: 0px 12px 0px;
              display: flex;
              padding-top: 60px;
            "
          >
            <v-row
              :width="MobileSize ? '100%' : '750px'"
              style="
                height: 50px;
                border-radius: 24px 24px 0px 0px;
                background: #ffffff;
              "
            >
              <v-col style="text-align: center"> </v-col>
            </v-row>
          </div>
          <div class="" style="position: relative">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #ffffff; border-radius: 20px 20px 0px 0px"
              :style="
                MobileSize
                  ? 'padding: 20px 20px 10px 20px;'
                  : 'padding: 40px 48px 10px 48px;'
              "
            >
            <v-img width="100%" height="100%" :src="require('@/assets/ERPEX.jpg')" cover></v-img>
            </v-card>
          </div>
        </v-card-text>
      </v-form>
    </v-card>
    </v-dialog>

    <v-dialog v-model="dialogAwait" width="424" persistent>
      <v-card style="background: #ffffff; border-radius: 24px">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon @click="closeDialogAwait()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center">
            <p
              style="
                font-weight: 700;
                font-size: 24px;
                line-height: 24px;
                color: #333333;
              "
              class="my-4"
            >
              <b>ปิดการใช้งานการเชื่อมต่อบริการ</b>
            </p>
            <span
              style="
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                color: #9a9a9a;
              "
              >คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span
            >
          </v-card-text>
          <v-card-text class="px-0">
            <v-row dense justify="center">
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                outlined
                rounded
                color="#27AB9C"
                class="mr-4"
                @click="closeDialogAwait()"
                >ยกเลิก</v-btn
              >
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                class="white--text"
                rounded
                color="#27AB9C"
                @click="confirmClosePartner(DataPartner)"
                >ตกลง</v-btn
              >
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogLoginSSO" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogLoginSSO = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>ยืนยันการเข้าระบบ Partner</b></span><br><br>
            <span>
              คุณต้องการทำรายการนี้ ใช่ หรือไม่
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogLoginSSO = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="gotoLoginSSO()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>

import Vue from 'vue'
import Address2021 from '@/Thailand_Address/address2021'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      selectedTab: 0,
      Warehouse: '',
      ItemWarehouse: [],
      warehouseOptions: [],
      warehouseOptions2: [],
      WarehousePartner: '',
      DialogWarehouse: false,
      itemsPerPage: 10,
      countData: 0,
      lazy: false,
      DialogEX: false,
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล']
      },
      shopID: '',
      dialogAwait: false,
      itemSelected: [
        {
          name: 'ZORT',
          image: require('@/assets/zort-logo.png')
        }
      ],
      DataPartner: [],
      partnerName: '',
      storename: '',
      apikey: '',
      apisecret: '',
      title: '',
      DialogPartner: false,
      search: '',
      selectType: 'all',
      selectStatus: 'all',
      itemsSelect: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'ERP', value: 'ERP' },
        { text: 'POS', value: 'POS' },
        { text: 'OMS', value: 'OMS' },
        { text: 'Web Development', value: 'Web Development' },
        { text: 'Marketing', value: 'Marketing' }
      ],
      itemsStatus: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'กำลังใช้งาน', value: 'active' },
        { text: 'ปิดการใช้งาน', value: 'inactive' },
        // { text: 'ยกเลิกการใช้งาน', value: 'delete' },
        { text: 'ระงับการใช้งาน', value: 'suspended' }
      ],
      modalRangeDate: false,
      dateRange: '',
      start_date: '',
      end_date: '',
      RangeDate1: '',
      dataselectdate: '',
      dataTable: [],
      headers: [
        {
          text: 'ชื่อ Partner',
          value: 'service_provider',
          width: '200',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'วันที่เชื่อมบริการ',
          value: 'created_at',
          width: '150',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'ประเภทการให้บริการ',
          value: 'service_provider_type',
          width: '200',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'storename',
          value: 'storename',
          width: '200',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'apikey',
          value: 'api_key',
          width: '300',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'api_secret',
          value: 'api_secret',
          width: '300',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'สถานะ',
          value: 'status',
          width: '100',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'เข้าระบบ Partner',
          value: 'have_sso',
          width: '150',
          align: 'center',
          sortable: false,
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'จัดการ',
          value: 'manage',
          sortable: false,
          width: '100',
          filterable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        }
      ],
      dataListInterestedPartner: [],
      selectedPartner: '',
      warehouseCode: '',
      warehouseNames: '',
      setdefaultWarehouse: '',
      houseNo: '',
      mooNo: '',
      district: '',
      subDistrict: '',
      province: '',
      zipCode: '',
      detailAddress: '',
      partnerCode: '',
      partnerPackage: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      // key1: '',
      // key2: '',
      // key3: '',
      autoSyncProduct: 'no',
      autoSyncOrder: 'no',
      autoMainExternalStock: 'yes',
      haveAPI: '',
      serviceType: [],
      selectPackageCode: '',
      selectPartnerCode: '',
      dialogLoginSSO: false,
      serviceProviderId: '',
      serviceId: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    selectedPartnerDetails () {
      return this.dataListInterestedPartner.find(
        (partner) => partner.partner_name === this.selectedPartner || this.partnerName
      )
    },
    hasZortInPartnerName () {
      const nameToCheck = (this.selectedPartner || this.partnerName || '').toLowerCase()
      const zortNames = [
        'ZORT',
        'zort',
        'ซอร์ท',
        'บริษัท ZORT',
        'บริษัท Zort',
        'บริษัท zort',
        'บริษัท ซอร์ท',
        'บริษัทซอร์ท'
      ]
      return zortNames.some(zortName => nameToCheck.includes(zortName.toLowerCase()))
    },
    countPartner () {
      return this.filteredPartners.length
    },
    noInfoComputed () {
      return this.filteredPartners.length === 0
    },
    filteredPartners () {
      // console.log(this.dataTable)
      return this.dataTable.filter(partner => {
        // ค้นหาชื่อ
        const searchMatch = this.search === '' || partner.service_provider.toLowerCase().includes(this.search.trim().toLowerCase())
        // ประเภทบริการ
        const typeMatch = this.selectType === 'all' || partner.service_provider_type.includes(this.selectType)
        // สถานะ
        const statusMatch = this.selectStatus === 'all' || partner.status === this.selectStatus

        const dateMatch = !this.RangeDate1 || (new Date(partner.created_at) >= new Date(this.start_date) && new Date(partner.created_at) <= new Date(this.end_date))

        return searchMatch && typeMatch && statusMatch && dateMatch
      })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ERPPartnerMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ERPPartner' }).catch(() => { })
      }
    },
    subDistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipCode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipCode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipCode = ''
          this.subDistrict = ''
          this.province = ''
        }
      } else {
        this.zipCode = ''
        this.subDistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipCode = ''
          this.subDistrict = ''
          this.district = ''
        }
      } else {
        this.zipCode = ''
        this.subDistrict = ''
        this.district = ''
      }
    },
    zipCode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subDistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subDistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNav')
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
    this.getDetailPartnerERP()
    this.GetListInterestedPartner()
  },
  methods: {
    formatDate (date) {
      const options = { year: 'numeric', month: '2-digit', day: '2-digit' }
      return new Date(date).toLocaleDateString('th-TH', options)
    },
    async setValueRangeDate (val) {
      // console.log('1', val)
      if (val) {
        const date = new Date(val)
        this.start_date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString()
        this.end_date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)).toISOString()
        this.RangeDate1 = this.formatDate(val)
      }
      await this.fetchSelectDateDetailPartner()
      this.modalRangeDate = false
    },
    async CloseModalRangeDate () {
      this.dateRange = null
      this.RangeDate1 = ''
      this.start_date = ''
      this.end_date = ''
      this.modalRangeDate = false
    },
    async fetchSelectDateDetailPartner () {
      // console.log(this.dataTable)
      this.partnerDetails = this.dataTable.filter(partner => {
        const rawDate = partner.created_at

        if (!rawDate) {
          return false
        }

        const partnerDate = new Date(rawDate).toISOString()
        return partnerDate >= this.start_date && partnerDate <= this.end_date
      })
    },
    selectItem (item) {
      this.selectedPartner = item.partner_name
      this.selectPackageCode = item.package_code
      this.selectPartnerCode = item.partner_code
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    openEX () {
      this.DialogEX = true
    },
    cancelEX () {
      this.DialogEX = false
    },
    async confirmEdit (val) {
      let isValid = false
      if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
        isValid = this.$refs.formERPPartnerIsAPI.validate(true)
      } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
        isValid = this.$refs.formERPPartnerNoAPI.validate(true) || this.$refs.formERPPartner2.validate(true)
      }

      if (isValid) {
        // if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
        //   if (!this.district || !this.subDistrict || !this.province || !this.zipCode) {
        //     this.$swal.fire({
        //       icon: 'warning',
        //       title: '<h5>กรุณากรอกข้อมูลที่อยู่ให้ครบถ้วน</h5>',
        //       showConfirmButton: false,
        //       timerProgressBar: true,
        //       timer: 1500
        //     })
        //     return
        //   }
        // }
        // console.log(isValid)
        // console.log('1')
        this.$store.commit('openLoader')
        let data

        if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
          data = {
            provider: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.service_provider : this.partnerName,
            seller_shop_id: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.seller_shop_id : this.shopID,
            service_key_id: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.service_key_id : val.service_key_id,
            storename: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.storename : this.storename,
            api_key: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.api_key : this.apikey,
            api_secret: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.api_secret : this.apisecret,
            // key1: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.key1 : this.key1,
            // key2: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.key2 : this.key2,
            // key3: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.key3 : this.key3,
            auto_sync_product: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.auto_sync_product : this.autoSyncProduct,
            auto_sync_order: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.auto_sync_order : this.autoSyncOrder,
            main_external_stock: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.main_external_stock : this.autoMainExternalStock,
            default_warehouse_id: this.ItemWarehouse.length !== 0 ? this.WarehousePartner : this.Warehouse,
            have_api_key: 'yes'
          }
        } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
          data = {
            service_key_id: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.service_key_id : val.service_key_id,
            default_warehouse_id: this.ItemWarehouse.length !== 0 ? this.WarehousePartner : this.Warehouse,
            // partner_code: this.dataListInterestedPartner.partner_code,
            // package_code: this.dataListInterestedPartner.package_code,
            provider: this.selectedPartner,
            seller_shop_id: this.shopID,
            storename: this.ItemWarehouse.length !== 0 ? this.ItemWarehouse.storename : this.storename,
            warehouse_code: this.warehouseCode,
            warehouse_name: this.warehouseNames,
            default_warehouse: this.setdefaultWarehouse,
            have_api_key: 'no',
            warehouse_address: {
              detail: this.detailAddress,
              house_no: this.houseNo,
              moo_no: this.mooNo,
              district: this.district,
              sub_district: this.subDistrict,
              province: this.province,
              zip_code: this.zipCode
            }
          }
        }

        await this.$store.dispatch('actionsUpdateServiceKeyERP', data)
        var res = await this.$store.state.ModuleShop.stateUpdateServiceKeyERP
        if (res.result === 'SUCCESS') {
          if (this.ItemWarehouse.length !== 0) {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'success',
              text: 'เลือกคลังสินค้าสำเร็จ',
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 1500
            })
          } else {
            this.$swal.fire({
              icon: 'success',
              text: 'แก้ไขการเชื่อมต่อสำเร็จ',
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 1500
            })
          }
          if (res.data.warning_alert && res.data.warning_alert.length) {
            this.$store.commit('closeLoader')
            const alertList = Array.isArray(res.data.warning_alert)
              ? `<ul style="padding-left: 1.2em; text-align: left;">
                  ${res.data.warning_alert.map(item => `<li>${item}</li>`).join('')}
                </ul>`
              : `<ul><li>${res.data.warning_alert}</li></ul>`

            this.$swal.fire({
              icon: 'info',
              html: alertList,
              confirmButtonColor: '#27AB9C',
              confirmButtonText: 'ปิด'
            })
          }
          this.DialogPartner = false
          this.ItemWarehouse = []
          this.WarehousePartner = ''
          this.DialogWarehouse = false
          this.$nextTick(() => {
            if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
              this.$refs.formERPPartnerIsAPI.resetValidation()
            } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
              this.$refs.formERPPartnerNoAPI.resetValidation()
            }
          })
          this.getDetailPartnerERP()
        } else if (res.result === 'FAILED') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: res.message,
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          title: '<span style="font-size: 20px;">กรอกข้อมูลไม่ครบหรือไม่ถูกต้อง<br>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</span>',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    async confirm () {
      if (this.selectedPartner === '') {
        this.$swal.fire({
          icon: 'warning',
          title: '<span style="font-size: 22px;">กรุณาเลือกชื่อร้านค้า Partner ที่สนใจเข้าร่วม</span>',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      } else {
        // let isValid = false
        // if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
        //   isValid = this.$refs.formERPPartnerIsAPI.validate(true)
        // } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
        //   isValid = this.$refs.formERPPartnerNoAPI.validate(true)
        // }

        // if (isValid) {
        //   if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
        //     if (!this.district || !this.subDistrict || !this.province || !this.zipCode) {
        //       this.$swal.fire({
        //         icon: 'warning',
        //         title: '<h5>กรุณากรอกข้อมูลที่อยู่ให้ครบถ้วน</h5>',
        //         showConfirmButton: false,
        //         timerProgressBar: true,
        //         timer: 1500
        //       })
        //       return
        //     }
        //   }

        this.$store.commit('openLoader')

        let data
        if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
          // console.log('YES')
          data = {
            partner_code: this.selectPartnerCode,
            package_code: this.selectPackageCode,
            provider: this.selectedPartner,
            seller_shop_id: this.shopID,
            storename: this.storename,
            api_key: this.apikey,
            api_secret: this.apisecret,
            auto_sync_product: this.autoSyncProduct,
            auto_sync_order: this.autoSyncOrder,
            main_external_stock: this.autoMainExternalStock,
            have_api_key: 'yes'
          }
        } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
          // console.log('NO')
          data = {
            partner_code: this.selectPartnerCode,
            package_code: this.selectPackageCode,
            provider: this.selectedPartner,
            seller_shop_id: this.shopID,
            storename: this.storename,
            warehouse_code: this.warehouseCode,
            warehouse_name: this.warehouseNames,
            default_warehouse: this.setdefaultWarehouse,
            have_api_key: 'no',
            warehouse_address: {
              detail: this.detailAddress,
              house_no: this.houseNo,
              moo_no: this.mooNo,
              district: this.district,
              sub_district: this.subDistrict,
              province: this.province,
              zip_code: this.zipCode
            }
          }
        }

        await this.$store.dispatch('actionsSetServiceKeyERP', data)
        var res = await this.$store.state.ModuleShop.stateSetServiceKeyERP
        if (res.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'success',
            text: 'เพิ่มการเชื่อมต่อสำเร็จ',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
          if (res.data.warning_alert && res.data.warning_alert.length) {
            this.$store.commit('closeLoader')
            const alertList = Array.isArray(res.data.warning_alert)
              ? `<ul style="padding-left: 1.2em; text-align: left;">
                  ${res.data.warning_alert.map(item => `<li>${item}</li>`).join('')}
                </ul>`
              : `<ul><li>${res.data.warning_alert}</li></ul>`

            this.$swal.fire({
              icon: 'info',
              html: alertList,
              confirmButtonColor: '#27AB9C',
              confirmButtonText: 'ปิด'
            })
          }
          this.DialogPartner = false
          await this.getDetailPartnerERP()
          this.ItemWarehouse = res.data
          this.warehouseOptions = this.ItemWarehouse.warehouse_list.map(warehouse => ({
            warehouse_id: warehouse.warehouse_id,
            warehouse_name: warehouse.warehouse_name
          }))
          this.DialogWarehouse = true
          this.$nextTick(() => {
            if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
              this.$refs.formERPPartnerIsAPI.resetValidation()
            } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
              this.$refs.formERPPartnerNoAPI.resetValidation()
            }
          })
        } else if (res.result === 'FAILED') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: res.message,
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
        // } else {
        //   this.$swal.fire({
        //     icon: 'warning',
        //     title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>',
        //     showConfirmButton: false,
        //     timerProgressBar: true,
        //     timer: 1500
        //   })
        // }
      }
    },
    async getDetailPartnerERP () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        provider: ''
      }
      await this.$store.dispatch('actionsDetailServiceKeyERP', data)
      var res = await this.$store.state.ModuleShop.stateDetailServiceKeyERP
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dataTable = res.data.shop_service_list
        this.countData = this.dataTable ? this.dataTable.length : 0
      } else if (res.result === 'FAILED') {
        if (res.message === 'Your role not found in this shop.') {
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: res.message,
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    closePartner () {
      this.dialogAwait = true
    },
    async openPartner (val) {
      this.$store.commit('openLoader')
      var data = {
        provider: this.partnerName,
        service_key_id: val.service_key_id,
        seller_shop_id: this.shopID,
        status: 'active'
      }
      // var dataSync = {
      //   action: 'sync',
      //   shop_id: this.shopID,
      //   service_id: val.service_id
      // }
      await this.$store.dispatch('actionsUpdateStatusServiceKeyERP', data)
      var res = await this.$store.state.ModuleShop.stateUpdateStatusServiceKeyERP
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'เปิดการใช้งานสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        this.DialogPartner = false
        this.dialogAwait = false
        this.getDetailPartnerERP()

        // await this.$store.dispatch('actionsSetSyncShopErp', dataSync)
        // var response = await this.$store.state.ModuleShop.stateSetSyncShopErp
        // if (response.result === 'SUCCESS') {
        //   this.$swal.fire({
        //     icon: 'success',
        //     text: 'เปิดการใช้งานสำเร็จ',
        //     showConfirmButton: false,
        //     timer: 1500
        //   })
        //   this.DialogPartner = false
        //   this.dialogAwait = false
        //   this.getDetailPartnerERP()
        // } else {
        //   this.$store.commit('closeLoader')
        //   this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'error', text: `${response.message}` })
        //   this.dialogAwait = false
        // }
      } else if (res.result === 'FAILED') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: res.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        this.dialogAwait = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        this.dialogAwait = false
      }
    },
    async confirmClosePartner (val) {
      this.$store.commit('openLoader')
      var data = {
        provider: this.partnerName,
        service_key_id: val.service_key_id,
        seller_shop_id: this.shopID,
        status: 'inactive'
      }
      // var dataSync = {
      //   action: 'unsync',
      //   shop_id: this.shopID,
      //   service_id: val.service_id
      // }
      await this.$store.dispatch('actionsUpdateStatusServiceKeyERP', data)
      var res = await this.$store.state.ModuleShop.stateUpdateStatusServiceKeyERP
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'ปิดการใช้งานสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        this.DialogPartner = false
        this.dialogAwait = false
        this.getDetailPartnerERP()
        // await this.$store.dispatch('actionsSetSyncShopErp', dataSync)
        // var response = await this.$store.state.ModuleShop.stateSetSyncShopErp
        // if (response.result === 'SUCCESS') {
        //   this.$swal.fire({
        //     icon: 'success',
        //     text: 'ปิดการใช้งานสำเร็จ',
        //     showConfirmButton: false,
        //     timer: 1500
        //   })
        //   this.DialogPartner = false
        //   this.dialogAwait = false
        //   this.getDetailPartnerERP()
        // } else {
        //   this.$store.commit('closeLoader')
        //   this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'error', text: `${response.message}` })
        //   this.dialogAwait = false
        // }
      } else if (res.result === 'FAILED') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: res.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        this.dialogAwait = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        this.dialogAwait = false
      }
    },
    closeDialogAwait () {
      this.dialogAwait = false
    },
    cancel () {
      this.partnerName = ''
      this.storename = ''
      this.apikey = ''
      this.apisecret = ''
      this.DialogPartner = false
      this.$nextTick(() => {
        if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
          this.$refs.formERPPartnerIsAPI.resetValidation()
        } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
          this.$refs.formERPPartnerNoAPI.resetValidation()
        }
      })
    },
    DetailCreateEdit (type, item) {
      if (type === 'create') {
        this.title = 'เพิ่มการเชื่อมต่อ'
        this.GetListInterestedPartner()
        this.partnerName = ''
        this.storename = ''
        this.apikey = ''
        this.apisecret = ''
        this.autoSyncProduct = ''
        this.autoSyncOrder = ''
        this.autoMainExternalStock = 'yes'
        this.key1 = ''
        this.key2 = ''
        this.key3 = ''

        this.selectedPartner = ''
        this.warehouseCode = ''
        this.warehouseNames = ''
        this.setdefaultWarehouse = ''
        this.detailAddress = ''
        this.houseNo = ''
        this.mooNo = ''
        this.district = ''
        this.subDistrict = ''
        this.province = ''
        this.zipCode = ''

        this.haveAPI = ''
        this.selectedTab = null

        this.DialogPartner = true
        this.$nextTick(() => {
          if (this.selectedTab === 0 && this.$refs.formERPPartnerIsAPI) {
            this.$refs.formERPPartnerIsAPI.resetValidation()
          } else if (this.selectedTab === 1 && this.$refs.formERPPartnerNoAPI) {
            this.$refs.formERPPartnerNoAPI.resetValidation()
          }
        })
      } else if (type === 'detail') {
        this.title = 'รายละเอียด'
        this.DataPartner = item

        this.haveAPI = item.have_api_key

        if (this.haveAPI === 'yes') {
          this.selectedTab = 0
        } else if (this.haveAPI === 'no') {
          this.selectedTab = 1
        }

        this.warehouseOptions2 = this.DataPartner.warehouse_list.map(warehouse => ({
          warehouse_id: warehouse.warehouse_id,
          warehouse_code: warehouse.warehouse_code,
          warehouse_name: warehouse.warehouse_name,
          default_warehouse: warehouse.default_warehouse,
          detail: warehouse.address_detail.detail,
          house_no: warehouse.address_detail.house_no,
          moo_no: warehouse.address_detail.moo_no,
          district: warehouse.address_detail.district,
          province: warehouse.address_detail.province,
          sub_district: warehouse.address_detail.sub_district,
          zip_code: warehouse.address_detail.zip_code
        }))

        // console.log('1', this.warehouseOptions2)

        // const defaultWarehouse = this.warehouseOptions2.find(
        //   warehouse => warehouse.default_warehouse === 'yes' || warehouse.default_warehouse === 'no'
        // )
        const defaultWarehouse = this.warehouseOptions2.find(
          warehouse => warehouse.default_warehouse === 'yes'
        ) || this.warehouseOptions2.find(
          warehouse => warehouse.default_warehouse === 'no'
        )

        // console.log('2', defaultWarehouse)

        if (defaultWarehouse) {
          this.Warehouse = defaultWarehouse.warehouse_id
        }

        // console.log('3', defaultWarehouse)

        if (defaultWarehouse) {
          this.warehouseCode = defaultWarehouse.warehouse_code
          this.warehouseNames = defaultWarehouse.warehouse_name
          this.setdefaultWarehouse = defaultWarehouse.default_warehouse
          this.detailAddress = defaultWarehouse.detail
          this.houseNo = defaultWarehouse.house_no
          this.mooNo = defaultWarehouse.moo_no
          this.district = defaultWarehouse.district
          this.subDistrict = defaultWarehouse.sub_district
          this.province = defaultWarehouse.province
          this.zipCode = defaultWarehouse.zip_code
        }

        this.partnerName = item.service_provider
        this.storename = item.storename
        this.apikey = item.api_key
        this.apisecret = item.api_secret
        this.selectedPartner = item.service_provider
        // this.key1 = item.key1
        // this.key2 = item.key2
        // this.key3 = item.key3
        this.autoSyncProduct = item.auto_sync_product
        this.autoSyncOrder = item.auto_sync_order
        this.autoMainExternalStock = item.main_external_stock
        this.serviceType = item.service_provider_type
        this.DialogPartner = true
      } else if (type === 'edit') {
        this.title = 'แก้ไขการเชื่อมต่อ'
        this.DataPartner = item

        this.haveAPI = item.have_api_key

        if (this.haveAPI === 'yes') {
          this.selectedTab = 0
        } else if (this.haveAPI === 'no') {
          this.selectedTab = 1
        }

        this.warehouseOptions2 = this.DataPartner.warehouse_list.map(warehouse => ({
          warehouse_id: warehouse.warehouse_id,
          warehouse_name: warehouse.warehouse_name,
          default_warehouse: warehouse.default_warehouse
        }))

        // const defaultWarehouse = this.warehouseOptions2.find(
        //   warehouse => warehouse.default_warehouse === 'yes' || warehouse.default_warehouse === 'no'
        // )

        const defaultWarehouse = this.warehouseOptions2.find(
          warehouse => warehouse.default_warehouse === 'yes'
        ) || this.warehouseOptions2.find(
          warehouse => warehouse.default_warehouse === 'no'
        )

        if (defaultWarehouse) {
          this.Warehouse = defaultWarehouse.warehouse_id
        }

        this.partnerName = item.service_provider
        this.storename = item.storename
        this.apikey = item.api_key
        this.apisecret = item.api_secret
        this.DialogPartner = true
      }
    },
    async GetListInterestedPartner () {
      this.$store.commit('openLoader')
      var sellerShopID = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: sellerShopID
      }

      await this.$store.dispatch('actionListInterestedPartner', data)
      var res = await this.$store.state.ModuleShop.stateListInterestedPartner
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dataListInterestedPartner = res.data
      }
    },
    copyToClipboard (text) {
      navigator.clipboard.writeText(text).then(() => {
        this.$swal.fire({
          icon: 'success',
          text: 'คัดลอกสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }).catch(() => {
        this.$swal.fire({
          icon: 'success',
          text: 'คัดลอกไม่สำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      })
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subDistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipCode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    CheckSpacebarV2 (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    dialogOpenLoginSSO (item) {
      this.dialogLoginSSO = true
      this.serviceProviderId = item.service_provider_id
      this.serviceId = item.service_id
    },
    async gotoLoginSSO () {
      this.dialogLoginSSO = false
      this.$store.commit('openLoader')
      var sellerShopID = localStorage.getItem('shopSellerID')
      var data = {
        service_provider_id: this.serviceProviderId,
        service_id: this.serviceId,
        seller_shop_id: sellerShopID
      }

      await this.$store.dispatch('actionLoginSSO', data)
      var res = await this.$store.state.ModuleShop.stateLoginSSO
      console.log(res)
      if (res.result === 'SUCCESS') {
        this.GotoSSO = res.data.redirect_url
        this.$store.commit('closeLoader')

        this.$swal.fire({
          icon: 'success',
          title: '<span style="font-size: 18px;">กำลังนำท่านเข้าสู่ระบบ Partner โปรดรอสักครู่...</span>',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2300
        }).then(() => {
          window.location.href = this.GotoSSO
        })
      } else if (res.result === 'FAILED') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: res.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<!-- <style scoped>
  /deep/ .v-select__selections input {
    display: none !important;
  }
</style> -->

<style scoped>
  ::v-deep(.v-select__selections input) {
    display: none !important;
  }

  ::v-deep(.v-slide-group__prev--disabled) {
    display: none !important;
  }
</style>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.input_text-thai-address input.th-address-input:disabled {
  color: rgba(0, 0, 0, 0.38) !important;
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}

.v-chip-group .v-slide-group__content {
  padding: 0;
}
</style>
