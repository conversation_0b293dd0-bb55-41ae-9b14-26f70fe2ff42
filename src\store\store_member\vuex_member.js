import AxiosMember from './axios_member'

const ModuleMember = {
  state: {
    stateListMemberLevelRank: [],
    stateListMemberRanked: [],
    stateCreateMember: []
  },
  mutations: {
    mutationsListMemberLevelRank (state, data) {
      state.stateListMemberLevelRank = data
    },
    mutationsListMemberRanked (state, data) {
      state.stateListMemberRanked = data
    },
    mutationsCreateMember (state, data) {
      state.stateCreateMember = data
    }
  },
  actions: {
    async actionsListMemberLevelRank (context, access) {
      var response = await AxiosMember.ListMemberLevelRank(access)
      await context.commit('mutationsListMemberLevelRank', response)
    },
    async actionsListMemberRanked (context, access) {
      var response = await AxiosMember.ListMemberRanked(access)
      await context.commit('mutationsListMemberRanked', response)
    },
    async actionsCreateMember (context, access) {
      var response = await AxiosMember.CreateMember(access)
      await context.commit('mutationsCreateMember', response)
    }
  }
}

export default ModuleMember
