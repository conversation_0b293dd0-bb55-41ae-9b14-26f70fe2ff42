import AxiosMember from './axios_member'

const Mo<PERSON>leMember = {
  state: {
    stateListMemberLevelRank: [],
    stateListMemberRanked: []
  },
  mutations: {
    mutationsListMemberLevelRank (state, data) {
      state.stateListMemberLevelRank = data
    },
    mutationsListMemberRanked (state, data) {
      state.stateListMemberRanked = data
    }
  },
  actions: {
    async actionsListMemberLevelRank (context, access) {
      var response = await AxiosMember.ListMemberLevelRank(access)
      await context.commit('mutationsListMemberLevelRank', response)
    },
    async actionsListMemberRanked (context, access) {
      var response = await AxiosMember.ListMemberRanked(access)
      await context.commit('mutationsListMemberRanked', response)
    }
  }
}

export default ModuleMember
