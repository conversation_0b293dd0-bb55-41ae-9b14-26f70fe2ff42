<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="d-flex align-center justify-space-between" v-if="!MobileSize">
        <div class="d-flex align-center"><v-icon class="mr-2" style="color: #27AB9C;" @click="backToListDelivery2()">mdi-chevron-left</v-icon>รายละเอียดใบส่งสินค้า</div>
        <v-btn v-if="this.dataDetails.status === 'inprogress'" @click="openDialogWaitingConfirm()" color="#27AB9C" style="border-radius: 20px; color: #F2F2F2;" class="px-4"><v-icon class="pr-2">mdi-package-variant-closed</v-icon> ได้รับสินค้าแล้ว</v-btn>
      </v-card-title>
      <v-card-title style="font-weight: 700;" class="d-flex align-center justify-space-between" v-else>
        <div class="d-flex align-center"><v-icon class="mr-2" color="#27AB9C" @click="backToListDelivery2()">mdi-chevron-left</v-icon>รายละเอียดใบส่งสินค้า</div>
        <v-btn v-if="this.dataDetails.status === 'inprogress'" @click="openDialogWaitingConfirm()" color="#27AB9C" style="border-radius: 20px; color: #F2F2F2;" class="px-4"><v-icon class="pr-2">mdi-package-variant-closed</v-icon> ได้รับสินค้าแล้ว</v-btn>
      </v-card-title>

      <v-col>
        <v-row>
          <v-col cols="12">
            <v-card elevation="0" style="background-color: #F9FAFD;">
              <v-col>
                <v-row>
                  <v-col :cols="MobileSize || IpadSize ? 12 : 7">
                    <v-row>
                      <v-col :cols="MobileSize ? 5 : 3">
                        <span><b>รหัสการสั่งซื้อ : </b></span>
                      </v-col>
                      <v-col :cols="MobileSize ? 7 : 9">
                        <span>{{ this.dataDetails.order_number }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col :cols="MobileSize ? 5 : 3">
                        <span><b>รหัสใบส่งสินค้า : </b></span>
                      </v-col>
                      <v-col :cols="MobileSize ? 7 : 9">
                        <span>{{ this.dataDetails.delivery_number }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col :cols="MobileSize ? 5 : 3">
                        <span><b>วันที่ทำรายการ : </b></span>
                      </v-col>
                      <v-col :cols="MobileSize ? 7 : 9">
                        <span>{{ this.dataDetails.created_at }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="MobileSize || IpadSize ? 12 : 5">
                    <v-card elevation="0" style="background-color: white;" class="pa-3">
                      <v-row style="display: flex; align-items: center;">
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>สถานะคำสั่งซื้อ : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 8">
                          <v-chip v-if="this.dataDetails.status === 'waiting'" text-color="#FAAD14" color="#FFF2E0">รอจัดส่งสินค้า</v-chip>
                          <v-chip v-if="this.dataDetails.status === 'inprogress'" text-color="#3178BB" color="#EAF4FF">กำลังจัดส่งสินค้า</v-chip>
                          <v-chip v-if="this.dataDetails.status === 'success'" text-color="#31BB31" color="#EAFFF4">จัดส่งสินค้าสำเร็จ</v-chip>
                        </v-col>
                      </v-row>
                      <v-row style="display: flex; align-items: center;">
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>วัน-เวลาส่งสินค้า : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 8">
                          <span>{{ this.dataDetails.start_date }}</span>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col :cols="MobileSize ? 5 : 4">
                          <span><b>วัน-เวลารับสินค้า : </b></span>
                        </v-col>
                        <v-col :cols="MobileSize ? 7 : 8">
                          <span v-if="this.dataDetails.end_shipping === 'Invalid date'">-</span>
                          <span v-else>{{ this.dataDetails.end_shipping }}</span>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-card>
          </v-col>
          <v-col cols="12">
            <div class="d-flex">
              <v-avatar rounded size="20">
                <v-img contain src="@/assets/shopDelivery/map.png"></v-img>
              </v-avatar>
              <span class="ml-3" style="font-size: 16px;"><b>ที่อยู่ในการจัดส่งสินค้า</b></span>
            </div>
          </v-col>
          <v-col cols="12">
            <v-row>
              <v-col cols="auto">
                <span><b>{{ this.dataDetails.buyer_name }}</b></span><span style="margin: 0 8px;">|</span><span><b>{{ this.dataDetails.phone }}</b></span>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <span>{{ this.dataDetails.address }}</span>
          </v-col>
          <v-col cols="12">
            <div class="d-flex">
              <v-avatar rounded size="20">
                <v-img contain src="@/assets/shopDelivery/shopping.png"></v-img>
              </v-avatar>
              <span class="ml-3" style="font-size: 16px;"><b>รายการสั่งซื้อสินค้า</b></span>
            </div>
          </v-col>
          <v-col cols="12">
            <v-data-table
              :headers="headers"
              :items="productDetails"
              hide-default-footer
              fixed-header
              style="max-height: 400px; overflow-y: auto;"
            >
              <template v-slot:[`item.detail`]="{ item }">
                <v-list-item class="pa-0">
                  <v-img
                    v-if="item.image && item.image !== '-' && item.image !== null"
                    :src="item.image"
                    max-height="70"
                    max-width="70"
                    class="mr-2"
                  ></v-img>
                  <img
                    v-else
                    src="@/assets/NoImage.png"
                    style="max-width: 70px; max-height: 70px;"
                    class="mr-2"
                  />
                  <v-list-item-content>
                    <v-list-item-title v-if="MobileSize" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 180px;">
                      {{ item.product_name }}
                    </v-list-item-title>
                    <v-list-item-title v-else style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 500px;">
                      {{ item.product_name }}
                    </v-list-item-title>
                    <v-list-item-subtitle>รหัสสินค้า: {{ item.product_id }}</v-list-item-subtitle>
                    <v-list-item-subtitle v-if="item.attribute && item.attribute !== '-'">
                      {{ item.attribute }}
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
              </template>
              <template v-slot:[`item.real_price`]="{ item }">
                <span>{{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </template>
              <template v-slot:[`item.total_price`]="{ item }">
                <span>{{ Number(item.total_price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogConfirm" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #FDFAF9; padding: 50px;">
          <div>
            <v-img
              src="@/assets/shopDelivery/confirm.png"
              contain
              max-width="200"
            ></v-img>
          </div>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยอมรับสินค้า</b></span><br><br>
            <span style="font-size: 16px;">คุณได้รับสินค้าเรียบร้อยแล้ว</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" color="#27AB9C" style="border-color: #27AB9C; color: #F2F2F2; width: 100px; font-size: 16px;" @click="dialogConfirm = false">ปิด</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogWaitingConfirm" persistent max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogWaitingConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center pb-0" style="border-radius: 35px 35px 0 0; background: #FDFAF9; padding: 20px;">
          <div>
            <v-img
              src="@/assets/shopDelivery/info.png"
              contain
              max-width="200"
            ></v-img>
          </div>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 24px;"><b>ยืนยันการรับสินค้า</b></span><br><br>
            <span style="font-size: 16px;">คุณได้รับสินค้าเรียบร้อยแล้วใช่หรือไม่</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogWaitingConfirm = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="SuccessDelivery()">ยืนยัน</v-btn>
        </v-card-actions>
        <br>
      </v-card>
    </v-dialog>

    </v-container>
</template>

<script>
export default {
  data () {
    return {
      dataDetails: [],
      productDetails: [],
      headers: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '150' },
        { text: 'รายละเอียดสินค้า', value: 'detail', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '250' },
        { text: 'ราคาต่อชิ้น', value: 'real_price', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '100' },
        { text: 'จำนวน', value: 'shipping_amount', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '100' },
        { text: 'ราคารวม', value: 'total_price', sortable: false, align: 'start', class: 'fontTable--text fontSizeDetail', width: '100' }
      ],
      lazy: false,
      dialogEdit: false,
      dialogSuccessEdit: false,
      modalRangeDate: false,
      deliveryDate: '',
      sendDeliveryDate: '',
      selectDeliveryDate: '',
      modalTimePicker: false,
      deliveryTime: '',
      dialogConfirm: false,
      dialogWaitingConfirm: false,
      deliveryNumber: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    isDataComplete () {
      return !!this.selectDeliveryDate && !!this.deliveryTime
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    this.orderDeliveryNumber = this.$route.query.orderDeliveryNumber
    this.orderDeliveryOrderDetail()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/ReceiveItemsDetailsMobile?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ReceiveItemsDetails?orderDeliveryNumber=${this.orderDeliveryNumber}` }).catch(() => {})
      }
    }
  },
  methods: {
    backToListDelivery2 () {
      if (this.MobileSize) {
        this.$router.push({ path: 'ReceiveItemsMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: 'ReceiveItems' }).catch(() => {})
      }
    },
    async orderDeliveryOrderDetail () {
      this.$store.commit('openLoader')
      var data = {
        order_delivery_number: this.orderDeliveryNumber
      }
      await this.$store.dispatch('actionOrderDeliveryOrderDetail', data)
      var responseData = await this.$store.state.ModuleShop.stateOrderDeliveryOrderDetail
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dataDetails = responseData.data.DeliveryDetail[0]
        this.productDetails = responseData.data.DeliveryDetail[0].product_details
      }
    },
    openDialogWaitingConfirm () {
      this.dialogWaitingConfirm = true
    },
    async SuccessDelivery () {
      this.$store.commit('openLoader')
      var data = {
        order_delivery_number: this.orderDeliveryNumber
      }
      await this.$store.dispatch('actionsConfirmOrderDeliveryCompany', data)
      var responseData = await this.$store.state.ModuleAdminManage.stateConfirmOrderDeliveryCompany
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogWaitingConfirm = false
        this.dialogConfirm = true
        await this.orderDeliveryOrderDetail()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    }
  }
}
</script>

<style scoped>
  ::v-deep(.theme--light.v-data-table.v-data-table--fixed-header thead th) {
    background: #F3F5F7 !important;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.12) !important;
  }
</style>
