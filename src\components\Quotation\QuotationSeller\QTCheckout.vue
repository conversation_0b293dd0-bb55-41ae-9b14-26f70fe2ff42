<template>
  <v-main  :style="MobileSize ? 'overflow-y: hidden' : ''" style="padding: 0px;">
    <v-container :style="MobileSize ? 'overflow-y: hidden;width: 2000px' : '' " style="min-height: 1000px;" >
      <v-card elevation="0" width="100%" height="100%">
          <v-card elevation="0" width="100%" height="100%">
            <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="pt-6" v-if="!MobileSize">จัดการใบเสนอราคา</v-card-title>
            <v-card-title style="font-weight: 600; font-size: 20px; line-height: 22px; color: #333333;" class="pt-6" v-else> ตั้งค่าใบเสนอราคา</v-card-title>
          </v-card>
      </v-card>
      <div class="pa-3 py-0" style="display: flex;justify-content:center">
        <!-- min-height: 880px -->
        <!-- height: 1485px -->
        <div class="pa-6" style="background-color: rgb(196, 196, 196);">
          <div class="mt-2" :style="MobileSize ? 'width: 1100px' : ''" style="background-color: white; margin-left: auto; margin-right: auto;">
            <v-row  class="pa-12 pt-10">
              <v-col cols="2" align-self="center" align="center">
                <v-row justify="center">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NoImageShop.png" width="134" height="134" max-width="134" max-height="134" contain class="" v-if="Detail.shop_logo_image.length === 0 || showError === true"></v-img>
                    <v-img :src="Detail.shop_logo_image[0].path" width="134" height="134" max-width="134" max-height="134" contain class="" v-else></v-img>
                    <!-- <v-btn width="125" height="40" text rounded color="#1B5DD6" class="" @click="uploadImageShop()" v-if="Detail.shop_logo_image.length === 0"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                    <v-file-input
                      v-model="DataImageShop"
                      accept="image/*"
                      @change="UploadImageShop()"
                      id="imageShop"
                      :clearable="false"
                      style="display:none"
                    /> -->
                </v-row>
                <!-- wait upload
                <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-4">
                  <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span>
                  <v-spacer></v-spacer>
                  <span class="textUploadsizeImage pt-1">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span>
                </v-row>
                <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                  <v-progress-linear :value="percentUpload" height="7" rounded :active="show"></v-progress-linear>
                </v-row>
                <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-1">
                  <span class="textUploadsizeImage">กำลังอัปโหลด...</span>
                  <v-spacer></v-spacer>
                  <span class="textUploadpercentImage">{{ percentUpload }} %</span>
                </v-row>
                <v-row justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                  <v-btn text color="#27AB9C" style="text-decoration-line: underline; font-size: 14px;" @click="cancelImageShop()">ยกเลิก</v-btn>
                </v-row>
                upload success
                <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === false" class="pt-0">
                  <v-col cols="12" class="pt-2">
                    <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 20) }}<span v-if="Detail.shop_logo_image[0].name.length > 20">...</span></span><br/>
                    <span class="textUploadsizeImage">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span>
                  </v-col>
                  <v-col cols="12" class="pt-0">
                    <v-btn width="50" height="40" text color="#27AB9C" @click="uploadImageShop()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                  </v-col>
                </v-row>
                upload fail
                <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === true" class="pt-0">
                  <v-col cols="12" class="pt-2">
                    <span class="textUploadnameImageFail" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span><br/>
                    <span class="textFailUpload">{{ showErrorText }}</span>
                  </v-col>
                  <v-col cols="12" class="pt-0">
                    <v-btn width="50" height="40" text color="#636363" @click="cancelImageShop()"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                  </v-col>
                </v-row> -->
              </v-col>
              <v-col cols="10" align="start">
                <v-col cols="12" class="pa-1">
                  <span style="font-size: 14px; font-weight: 700;">
                    <input type="text" disabled v-model="shopName"  style="border-radius:7px; border:1px solid grey; width: 25%;padding-left: 5px;">
                  </span>
                </v-col>
                <v-col cols="12" class="pa-1">
                  <span style="font-size: 14px; font-weight: 700;">
                    บ้านเลขที่ <input type="text" disabled v-model="Detail.shop_address[0].house_no"  style="border-radius:7px; border:1px solid grey; width: 5%; text-align: center;">
                    แขวง/ตำบล <input type="text" disabled v-model="Detail.shop_address[0].sub_district"  style="border-radius:7px; border:1px solid grey; width: 11%; text-align: center;">
                    เขต/อำเภอ <input type="text" disabled v-model="Detail.shop_address[0].district"  style="border-radius:7px; border:1px solid grey; width: 11%; text-align: center;">
                    จังหวัด <input type="text" disabled v-model="Detail.shop_address[0].province"  style="border-radius:7px; border:1px solid grey; width: 15%; text-align: center;">
                    รหัสไปรษณีย์ <input type="text" disabled v-model="Detail.shop_address[0].zipcode"  style="border-radius:7px; border:1px solid grey; width: 10%; text-align: center;">
                  </span>
                </v-col>
                <v-col cols="12" class="pa-1">
                  <span style="font-size: 14px; font-weight: 700;">
                    โทร: <input type="text" disabled v-model="mobileNumber"  style="border-radius:7px; border:1px solid grey; width: 12%; text-align: center;">
                    มือถือ: <input type="text" disabled v-model="mobileNumber"  style="border-radius:7px; border:1px solid grey; width: 12%; text-align: center;">
                    อีเมล: <input type="text" disabled v-model="email"  style="border-radius:7px; border:1px solid grey; width: 25%; text-align: center;">
                  </span>
                </v-col>
                <v-col cols="12" class="pa-1">
                  <v-row>
                    <v-col cols="6">
                      <span style="font-size: 14px; font-weight: 700;">
                        เลขประจำตัวผู้เสียภาษีอากร: {{ taxNumber }}
                      </span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-size: 14px; font-weight: 700;">
                        (

                        <input type="text" disabled v-model="branch"  style="border-radius:7px; border:1px solid grey; width: 25%;text-align: center;">
                        )
                      </span>
                    </v-col>
                  </v-row>

                </v-col>

              </v-col>
              <v-col class="mt-2" cols="12" align="center">
                <span style="font-size: 14px; font-weight: 700;">
                  ใบเสนอราคา / Quotation
                </span>
              </v-col>
              <v-col class="mt-2" cols="12" style>
                <v-row>
                    <v-col cols="" style="border: 1px solid black;border-collapse: collapse;;border-right:0px">
                      <div style="display:flex">
                        <v-row>
                          <v-col cols="2">
                            <span style="font-size: 12px; font-weight: 700;">
                              ชื่อลูกค้า
                            </span>
                          </v-col>
                          <v-col>
                            <span>
                              <input type="text" disabled :value="addressCustomer.cus_name" style="border-radius:7px; border:1px solid grey;width: 95%;padding-left: 5px;">
                            </span>
                          </v-col>
                        </v-row>
                      </div>
                      <div class="mt-1" style="display:flex">
                        <v-row>
                          <v-col cols="2">
                            <span style="font-size: 12px; font-weight: 700;" class="mr-1">
                              ที่อยู่
                            </span>
                          </v-col>
                          <v-col>
                            <span style="font-size: 12px; font-weight: 700;" class="mr-1">
                              <textarea type="text" disabled v-model="addressCustomer.customer_address_text" style="border-radius: 7px; border: 1px solid grey; width: 95%; padding-left: 5px; height: 70px;resize: none;background-color: rgb(220, 220, 220)">  </textarea>
                            </span>
                          </v-col>
                        </v-row>
                      </div>
                    </v-col>
                    <v-col cols="2" style="border: 1px solid black;border-collapse: collapse;;border-right:0px">

                    </v-col>
                    <v-col cols="5" style="border: 1px solid black;border-collapse: collapse;">
                      <v-row class="">
                        <v-col cols="3" class="pr-0 pb-0">
                          <span style="font-size: 12px; font-weight: 700;">
                            เลขที่ใบเสนอราคา
                          </span>
                        </v-col>
                        <v-col cols="7" class="pl-1 pr-0 pb-0">
                          <input v-if="orderNumber !== undefined" disabled type="text" :value="orderNumber"  style="border-radius:7px; border:1px solid grey; width: 100%; text-align: left;height: 70%;padding-left: 5px;">
                        </v-col>
                      </v-row>
                      <v-row class="">
                        <v-col cols="2" class="pr-0 pb-0 pt-0 align-self-center">
                          <span style="font-size: 12px; font-weight: 700">
                            วันที่
                          </span>
                        </v-col>
                        <!-- <v-col cols="4" class="pl-1 pr-0 pb-0 pt-0 ml-2">
                          <v-menu
                            ref="menu"
                            v-model="menu"
                            :close-on-content-click="false"
                            :return-value.sync="date"
                            transition="scale-transition"
                            offset-y
                            min-width="auto"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-text-field
                                :value="computedDateFormattedMomentjs"
                                readonly
                                v-bind="attrs"
                                v-on="on"
                              ></v-text-field>
                            </template>
                            <v-date-picker
                              v-model="date"
                              scrollable
                              locale="Th-th"
                            >
                              <v-spacer></v-spacer>
                              <v-btn
                                text
                                color="primary"
                                @click="$refs.menu.save(date)"
                              >
                                ยืนยัน
                              </v-btn>
                            </v-date-picker>
                          </v-menu>
                        </v-col> -->
                      </v-row>
                      <v-row class="">
                        <v-col cols="12" class="pt-0 pb-0">
                          <span style="font-size: 12px; font-weight: 700;">
                            เงื่อนไขการชำระเงิน
                          </span>
                        </v-col>
                      </v-row>
                      <v-row class="">
                        <v-col cols="12" class="pt-0 pb-0">
                          <span style="font-size: 12px; font-weight: 700;">
                            เครดิต
                          </span>
                          <span style="font-size: 12px; font-weight: 400;">
                            30 วัน
                          </span>
                        </v-col>
                      </v-row>
                      <v-row class="">
                        <v-col cols="12" class="pt-0">
                          <span style="font-size: 12px; font-weight: 700;">
                            จำนวนงวด
                          </span>
                          <span style="font-size: 12px; font-weight: 400;">
                            1 งวด
                          </span>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
              </v-col>

              <v-col class="" style="margin-top: -1px;" cols="12">
                <v-row>
                    <v-col cols="1" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        ลำดับ
                      </span>
                    </v-col>
                    <v-col cols="6" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        รายละเอียด
                      </span>
                    </v-col>
                    <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        จำนวน
                      </span>
                    </v-col>
                    <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        หน่วย
                      </span>
                    </v-col>
                    <v-col cols="2" align="center" style="border: 1px solid black;border-collapse: collapse;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        ราคาต่อหน่วย
                      </span>
                    </v-col>
                  </v-row>
              </v-col>

              <v-col v-for="(product_list, productindex) in itemsCart.choose_list[0].product_list" :key="productindex" class="" style="margin-top: -1px;" cols="12">
                <v-row>
                    <v-col cols="1" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        {{ productindex+1 }}
                      </span>
                    </v-col>
                    <v-col cols="6" align="start" align-self="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <v-row>
                        <v-col cols="10">
                          <!-- {{ productList.list_product[0] }} -->
                          <!-- {{ product_list }} -->
                          <v-autocomplete
                            :items="productList.list_product"
                            item-text="product_name_2"
                            item-value="product_id"
                            outlined
                            hide-details
                            v-model="itemsCart.choose_list[0].product_list[productindex]"
                            return-object
                            @change="calculatePrice('price_no_vat')"
                            max-height="100px"
                          >
                            <template v-slot:item="data">
                              <template v-if="typeof data.item !== 'object'">
                                <v-list-item-content v-text="data.item"></v-list-item-content>
                              </template>
                              <template v-else>
                                <v-list-item-avatar>
                                  <img v-if="data.item.product_image !== '' && data.item.product_image !== null " :src="data.item.product_image">
                                  <img v-else src="@/assets/NoImage.png">
                                </v-list-item-avatar>
                                <v-list-item-content>
                                  <v-list-item-title v-html="data.item.product_name"></v-list-item-title>
                                  <v-list-item-subtitle style="color:grey" v-html="data.item.subtitle"></v-list-item-subtitle>
                                </v-list-item-content>
                              </template>
                            </template>
                          </v-autocomplete>
                        </v-col>
                        <v-col cols="2" style="align-self:center;display: flex;justify-content:end;">
                          <v-icon v-if="itemsCart.choose_list[0].product_list.length !== 1" class="add-product" color="red" @click="deleteProduct(productindex)" >mdi-minus-box</v-icon>
                        </v-col>
                      </v-row>
                        <!-- <input type="text" v-model="product_list.product_name"  style="border-radius:7px; border:1px solid grey; width: 100%;padding-left: 5px;"> -->
                    </v-col>
                    <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        <input @change="checkQuantity(),calculatePrice('Quantity')" type="text" v-model="product_list.quantity" oninput="this.value = this.value.replace(/^0/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')" style="border-radius:7px; border:1px solid grey; width: 100%;text-align: center;">
                      </span>
                    </v-col>
                    <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        <input type="text" disabled v-model="product_list.unit_type"  style="border-radius:7px; border:1px solid grey; width: 100%;text-align: center;">
                      </span>
                    </v-col>
                    <v-col cols="2" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px">
                      <span style="font-size: 12px; font-weight: 400;">
                        <input @change="calculatePrice(productindex)" type="text" v-model="product_list.price_no_vat" oninput="this.value = this.value.replace(/^[.]/, '').replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"  style="border-radius:7px; border:1px solid grey; width: 100%;text-align: right;padding-right: 5px;">
                      <p v-if="product_list.vat_default === 'yes'" :style="IpadSize ? 'font-size: 7px' : 'font-size: 10px' " style="color:red" class="mb-0">(ราคารวมภาษีมูลค่าเพิ่ม)</p>
                      <p v-else style="color:red" :style="IpadSize ? 'font-size: 7px' : 'font-size: 10px' " class="mb-0">(ราคาไม่รวมภาษีมูลค่าเพิ่ม)</p>
                      </span>
                    </v-col>
                  </v-row>
              </v-col>

              <v-col class="" style="margin-top: -1px;" cols="12">
                <v-row>
                    <v-col cols="1" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">

                    </v-col>
                    <v-col cols="6" align="end" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                      <v-row>
                        <v-col cols="10">
                        </v-col>
                        <v-col cols="2" style="align-self:right;">
                          <v-icon class="add-product" color="#27AB9C" @click="plusProduct()" >mdi-plus-box</v-icon>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">

                    </v-col>
                    <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">

                    </v-col>
                    <v-col cols="2" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px">

                    </v-col>
                  </v-row>
              </v-col>

              <v-col class="" style="margin-top: -1px;" cols="12">
                <!-- border-left: 1px solid black; -->
                <v-row style="" fill-height>
                    <v-col cols="7" align="center" class="" align-self="end"  style="border-bottom: 1px solid black;border-collapse: collapse;min-height:214px;border-left: 1px solid black;display:flex;align-items: end; justify-content: space-around;">
                        <span style="font-size: 12px; font-weight: 700;">
                          ตัวอักษร ( {{thaiTotal}} )
                        </span>
                    </v-col>
                    <v-col cols="3" align="center" class="pa-0" style="border-bottom: 1px solid black;border-collapse: collapse;border-bottom: 0px;">
                      <span style="font-size: 12px; font-weight: 400;">
                        <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 700;">
                            รวมเงิน
                          </span>
                        </v-col>
                        <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 700;">
                            ส่วนลด
                            <v-btn class="float-end" v-if="CouponData.length !== 0" color="#27AB9C" text @click="clickPoint(CouponData[0].coupon_id, PointData, XBaht, itemsCart.choose_list[0].total_coupon_discount)" :style="IpadSize ? 'font-size: 11px' : 'font-size: 14px;'" style=" font-weight: 500; margin-top: -10px;padding: 0px;">
                              <span class="px-2" style="text-decoration-line: underline;">แต้มส่วนลด</span>
                              </v-btn>
                              <v-btn class="float-end" v-else color="#27AB9C" text @click="clickPoint('', PointData, XBaht, itemsCart.choose_list[0].total_coupon_discount)" :style="IpadSize ? 'font-size: 11px' : 'font-size: 14px;'" style=" font-weight: 500; margin-top: -10px;padding: 0px;">
                              <span class="px-2" style="text-decoration-line: underline;">แต้มส่วนลด</span>
                              </v-btn>
                            <v-btn v-if="CouponData.length === 0"  @click="clickCoupon('', PointData)" class="float-end " color="#27AB9C" text :class="btnEstimateCost === false ? '' : 'theme--dark'"
                              :style="IpadSize ? 'font-size: 11px' : 'font-size: 14px;'" style=" font-weight: 500; margin-top: -10px;padding: 0px;"><span
                              style="text-decoration-line: underline;">คูปองส่วนลด</span>
                            </v-btn>
                            <v-btn v-else @click="clickCoupon(CouponData[0].coupon_id, PointData)" class="float-end " color="#27AB9C" text :class="btnEstimateCost === false ? '' : 'theme--dark'"
                              :style="IpadSize ? 'font-size: 11px' : 'font-size: 14px;'" style=" font-weight: 500; margin-top: -10px;padding: 0px;"><span
                              style="text-decoration-line: underline;">คูปองส่วนลด</span>
                            </v-btn>
                          </span>
                        </v-col>
                        <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 700;">
                            ภาษีมูลค่าเพิ่ม 7%
                          </span>
                        </v-col>
                        <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 700;">
                            <v-row>
                              <v-col cols="6">
                                ค่าจัดส่ง
                              </v-col>
                              <v-col cols="6">
                                <v-btn class="float-end " v-if="SelectShipping.length !== 0" color="#27AB9C" text :disabled="btnEstimateCost === false" :class="btnEstimateCost === false ? '' : 'theme--dark'"
                                  :style="IpadSize ? 'font-size: 11px' : 'font-size: 14px;'" style=" font-weight: 500; margin-top: -10px;padding: 0px;" @click="orderNumber === undefined ? EstimateCost() : EstimateCostEdit()"><span
                                  style="text-decoration-line: underline;">เลือกการจัดส่ง</span>
                                </v-btn>
                              </v-col>
                            </v-row>

                          </span>
                        </v-col>
                        <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 700;">
                            ราคารวมทั้งสิ้น
                          </span>
                        </v-col>
                      </span>
                    </v-col>
                    <v-col cols="2" align="center" class="pa-0" style="border-bottom: 1px solid black;border-collapse: collapse;border-top: 0px;border-bottom: 0px;">
                      <span style="font-size: 12px; font-weight: 400;">
                        <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 400;">
                            <input disabled type="text" v-model="itemsCart.choose_list[0].total_price_no_vat"  style="border-radius:7px; border:1px solid grey; width: 100%;text-align: right;padding-right: 5px;">
                          </span>
                        </v-col>
                        <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 400;">
                            <input @change="calculatePrice('Discount')" disabled type="text" v-model="this.itemsCart.choose_list[0].total_discount"  style="border-radius:7px; border:1px solid grey; width: 100%;text-align: right;padding-right: 5px;">
                          </span>
                        </v-col>
                        <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 400;">
                            <input @change="calculatePrice('Vat')" disabled type="text" v-model="itemsCart.choose_list[0].total_vat"  style="border-radius:7px; border:1px solid grey; width: 100%;text-align: right;padding-right: 5px;">
                          </span>
                        </v-col>
                        <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 400;">
                            <input @change="calculatePrice('Shipping')" disabled type="text" v-model="shipping_price"  style="border-radius:7px; border:1px solid grey; width: 100%;text-align: right;padding-right: 5px;">
                          </span>
                        </v-col>
                        <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;height: 43px;">
                          <span style="font-size: 12px; font-weight: 400;">
                            <input disabled type="text" v-model="itemsCart.choose_list[0].net_price" style="border-radius:7px; border:1px solid grey; width: 100%;text-align: right;padding-right: 5px;">
                          </span>
                        </v-col>
                      </span>
                    </v-col>
                  </v-row>
              </v-col>

              <v-col class="mt-3" style="" cols="12">
                <span style="font-size: 12px; font-weight: 400;">
                  หมายเหตุ
                  <!-- <input type="text" v-model="note" style="border-radius:7px; border:1px solid grey; width: 95%; text-align: left;padding-left: 5px;"> -->
                </span>
              </v-col>
              <v-col class="mt-1" style="" cols="12">
                <span style="font-size: 12px; font-weight: 400;">
                  ขอบพระคุณที่ท่านให้ความสนใจบริการของเรา
                </span>
              </v-col>

              <v-col class="mt-12" cols="12">
                <v-row>
                    <v-col cols="4" align="center" style="">
                      <span style="font-size: 12px; font-weight: 400;">
                        สั่งซื้อโดย / Order By
                      </span>
                      <br>
                      <div class="mt-12" style="border-bottom:1px solid black ;width: 70%;">
                      </div>

                      <v-row class="mt-5" style="width: 70%;">
                        <span class="mr-auto">
                          (
                        </span>
                        <input type="text" disabled v-model="addressCustomer.cus_name" style="border-radius:7px; border:1px solid grey; width: 90%; text-align: center;">
                        <span class="ml-auto">
                          )
                        </span>
                      </v-row>
                    </v-col>
                    <v-col cols="4" align="center" style="">
                      <span style="font-size: 12px; font-weight: 400;">
                        ออกโดย / Prepared By
                      </span>
                      <br>
                      <div class="mt-12" style="border-bottom:1px solid black ;width: 70%;">
                      </div>

                      <v-row class="mt-5" style="width: 70%;">
                        <span class="mr-auto">
                          (
                        </span>
                        <input type="text" disabled v-model="prepare_by"  style="border-radius:7px; border:1px solid grey; width: 90%; text-align: center;">
                        <span class="ml-auto">
                          )
                        </span>
                      </v-row>
                    </v-col>
                    <v-col cols="4" align="center" style="">
                      <span style="font-size: 12px; font-weight: 400;">
                        ผู้มีอำนาจอนุมัติ / Authorized Signatory
                      </span>
                      <br>
                      <div class="mt-12" style="border-bottom:1px solid black ;width: 70%;">
                      </div>

                      <v-row class="mt-5" style="width: 70%;">
                        <span class="mr-auto">
                          (
                        </span>
                        <input type="text" disabled v-model="authorize_by" style="border-radius:7px; border:1px solid grey; width: 90%; text-align: center;">
                        <span class="ml-auto">
                          )
                        </span>
                      </v-row>
                    </v-col>
                  </v-row>
              </v-col>
              <!-- <v-img :src="Detail.shop_logo_image[0].path" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-else></v-img> -->
            </v-row>

          </div>
          <div>
            <v-row class="mt-4">
              <v-col cols="12" align="end">
                <v-btn class="d-inline rounded-e-xl mr-auto" style="border-radius: 7px 7px 7px 7px !important;" width="116" height="39" dark elevation="0" @click="confirm()" color="#27AB9C">
                  ยืนยัน
                </v-btn>
              </v-col>
            </v-row>

          </div>
        </div>
      </div>
    </v-container>
    <CouponCart ref="CouponCart" />
    <PointCart ref="PointCart" />
    <!-- Dialog เลือกขนส่ง -->
    <v-dialog v-model="DialogTransport" :width="MobileSize ? '100%' : '600'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 600px'" class="backgroundHead"
            style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span
                  :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เลือกขนส่ง</b></span>
              </v-col>
              <v-btn fab small @click="DialogTransport = !DialogTransport" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '459px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-img class="float-left mt-n2 mr-2" src="@/assets/Transport.png" width="30" height="30" contain></v-img>
                <span :style="MobileSize ? 'font-size: 18px; font-weight: 700;' : 'font-size: 24px; font-weight: 700;'" >ขนส่งในการจัดส่งสินค้า</span>
                <div v-if="!MobileSize">
                  <v-card class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in estimateCostData"
                    :key="index"
                    style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                    <v-row>
                      <v-col cols="1" class="align-self-center mt-2">
                        <v-radio-group v-model="radioTransport" class="mt-0 pt-0">
                          <v-radio color="#27AB9C" :value="item.tpl_name" @click="selectTransport(item)" style="color: #333333">
                          </v-radio>
                        </v-radio-group>
                      </v-col>
                      <v-img class="rounded-circle ma-4" style="border: 1px solid" :src="item.media_path" contain max-width="125" max-height="125"></v-img>
                      <!-- <v-img v-if="item.businessType === 'THAILAND_POST'" class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/THAILAND_POST.jpg" contain max-width="125" max-height="125"></v-img>
                      <v-img v-else class="rounded-circle ma-4" style="border: 1px solid" src="@/assets/Flash-express.jpg" contain max-width="125" max-height="125"></v-img> -->
                      <v-col  class="align-self-center text-end">
                        <span :style="MobileSize ? 'font-size: 16px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'">{{item.tpl_name}}</span><br>
                        <span :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #27AB9C;' : 'font-size: 18px; font-weight: 700; color: #27AB9C;'">ค่าส่งเริ่มต้น {{item.upCountry === true ? Math.ceil(item.estimatePrice) + Math.ceil(item.upCountryAmount) : Math.ceil(item.estimatePrice)}} บาท</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </div>
                <div v-else>
                  <v-card class="mt-7 pa-4" elevation="0" width="100%" height="100%" v-for="(item, index) in estimateCostData"
                    :key="index"
                    style="background: #FFFFFF; border-radius: 8px; border: 2px solid var(--neutral-ebebeb, #EBEBEB);">
                    <v-row>
                      <v-col cols="1" class="align-self-center mt-2">
                        <v-radio-group v-model="radioTransport" class="mt-0 pt-0">
                          <v-radio color="#27AB9C" :value="item.tpl_name" @click="selectTransport(item)" style="color: #333333">
                          </v-radio>
                        </v-radio-group>
                      </v-col>
                      <v-col cols="4" class="pr-0">
                        <v-img class="rounded-circle ma-4" style="border: 1px solid" :src="item.media_path" contain max-width="60" max-height="60"></v-img>
                        <!-- <v-img v-if="item.businessType === 'THAILAND_POST'" class="rounded-pill ma-0 ml-2" style="border: 1px solid" src="@/assets/THAILAND_POST.jpg" max-width="60" max-height="60"></v-img>
                        <v-img v-else class="rounded-pill ma-0 ml-2" style="border: 1px solid" src="@/assets/Flash-express.jpg" max-width="60" max-height="60"></v-img> -->
                      </v-col>
                      <v-specer class="mx-auto"></v-specer>
                      <v-col cols="auto" class="text-end align-self-center">
                        <span :style="MobileSize ? 'font-size: 12px; font-weight: 700;' : 'font-size: 18px; font-weight: 700;'">{{item.tpl_name}}</span><br>
                        <span :style="MobileSize ? 'font-size: 12px; font-weight: 700; color: #27AB9C;' : 'font-size: 18px; font-weight: 700; color: #27AB9C;'">ค่าส่งเริ่มต้น {{item.upCountry === true ? Math.ceil(item.estimatePrice) + Math.ceil(item.upCountryAmount) : Math.ceil(item.estimatePrice)}} บาท</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-main>
</template>

<script>
import THBText from 'thai-baht-text'
import { Decode, Encode } from '@/services'
import Vue from 'vue'

export default {
  components: {
    CouponCart: () => import('@/components/Cart/ModalCoupon/ListCoupon'),
    PointCart: () => import('@/components/Cart/ModalCoupon/ListPoint')
  },
  data () {
    return {
      XBaht: '',
      test: 0,
      shopName: '',
      shortName: '',
      percentUpload: 0,
      taxNumber: '',
      prepare_by: '',
      authorize_by: '',
      branch: '',
      fullname: '',
      mobileNumber: '',
      DataImageShop: null,
      email: '',
      CartAddress: [],
      itemsCart: {
        choose_list: [{
          product_list: []
        }]
      },
      note: '',
      Detail: {
        seller_shop_id: '',
        shop_logo_image: [],
        image_banner: [],
        image_news: [],
        shop_image: [],
        shop_image_banner: [],
        shop_advert: [],
        shop_name: '',
        shop_url: '',
        tax_id: '',
        line_id: '',
        shop_description: '',
        payment_method: [],
        shipping_method: [],
        installment_method: [],
        shop_status: '',
        public_show: '',
        partner_show: '',
        have_partner: '',
        facebook_url: '',
        shop_type: '',
        merchant_key: '',
        first_name: '',
        last_name: '',
        team_email: '',
        shop_address: [
          {
            id: '',
            default_address: '',
            house_no: '',
            detail: '',
            province: '',
            district: '',
            sub_district: '',
            zipcode: ''
          }
        ],
        shop_email: [],
        payment_costs: [],
        shop_phone: [
          { phone: '' },
          { phone: '' }
        ]
        // shop_shipping_type: []
      },
      productList: [],
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      menu: false,
      modal: false,
      menu2: false,
      addressCustomer: {
        cus_name: '',
        customer_address: [],
        customer_address_text: ''
      },
      btnEstimateCost: true,
      estimateCostData: [],
      DialogTransport: false,
      userdetail: [],
      radioTransport: '',
      shipping_price: '',
      ShippingData: [],
      CouponData: [],
      PointData: '',
      pointData: '',
      orderNumber: '',
      from: '',
      id: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    thaiTotal () {
      return THBText(this.itemsCart.choose_list[0].net_price)
    },
    computedDateFormattedMomentjs () {
      const formatDate = new Date(this.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
      return formatDate
    }
  },
  mounted () {
    this.$EventBus.$on('SelectCouponQT', this.calculatePrice)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('SelectCouponQT')
    })
  },
  watch: {
    // MobileSize (val) {
    //   if (val === true) {
    //     this.$router.push({ path: '/partnerSellerMobile' }).catch(() => {})
    //   } else {
    //     this.$router.push({ path: '/partnerSeller' }).catch(() => {})
    //   }
    // }
    // 'itemsCart.choose_list[0].productlist.quantity' (newValue, oldValue) {
    //   if (oldValue !== undefined) {
    //     if (newValue[0].choose_list.length === oldValue[0].choose_list.length) {
    //     }
    //   }
    // },
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // this.shopID = localStorage.getItem('shopSellerID').toString()
    this.from = this.$route.query.from
    this.orderNumber = this.$route.query.order_number
    this.id = this.$route.query.id
    if (this.orderNumber !== undefined) {
      this.couponData = []
    } else {
      if (this.onedata.cartData !== undefined) {
        this.couponData = this.onedata.cartData.coupon
      }
    }
    await this.GetUserDetail()
    var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    var cusData
    if (this.orderNumber === undefined) {
      // console.log(2)
      this.shopID = this.onedata.cartData.seller_shop_id
      await this.GetDetailShop()
      await this.getAddress()
      if (dataRole.role === 'sale_order_no_JV') {
        const roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer

        if (roleCustomer === 'general') {
          cusData = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerSale')))
        } else {
          cusData = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerBussinessSale')))
        }
        this.addressCustomer.cus_name = cusData.cus_name
        this.addressCustomer = cusData
        this.addressCustomer.customer_address_text = `บ้านเลขที่ ${this.addressCustomer.customer_address[0].house_no} ${this.addressCustomer.customer_address[0].detail_address} แขวง/ตำบล ${this.addressCustomer.customer_address[0].sub_district} เขต/อำเภอ ${this.addressCustomer.customer_address[0].district} จังหวัด ${this.addressCustomer.customer_address[0].province} รหัสไปรษณีย์ ${this.addressCustomer.customer_address[0].postcode} เบอร์มือถือ ${this.addressCustomer.customer_address[0].phone} เบอร์โทรศัพท์ - `
      }
    } else {
      // console.log(1)
      this.shopID = this.id
      await this.getOrderDetail()
      await this.GetDetailShop()
      cusData = this.itemsCart.cus_address_data
      this.addressCustomer.customer_address = cusData
      this.addressCustomer.cus_name = this.itemsCart.cus_address_data[0].name
      this.addressCustomer.customer_address_text = `บ้านเลขที่ ${this.addressCustomer.customer_address[0].house_no} ${this.addressCustomer.customer_address[0].detail_address} แขวง/ตำบล ${this.addressCustomer.customer_address[0].sub_district} เขต/อำเภอ ${this.addressCustomer.customer_address[0].district} จังหวัด ${this.addressCustomer.customer_address[0].province} รหัสไปรษณีย์ ${this.addressCustomer.customer_address[0].postcode} เบอร์มือถือ ${this.addressCustomer.customer_address[0].phone} เบอร์โทรศัพท์ - `
    }
    this.getListUserPointByUser()
    await this.getListProduct()
  },
  methods: {
    async getListUserPointByUser () {
      this.XBaht = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(onedata)
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
      var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var data = {
        role_user: RoleUser.role,
        customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
        seller_shop_id: onedata.cartData.seller_shop_id === undefined || onedata.cartData.seller_shop_id === null ? shopID : onedata.cartData.seller_shop_id,
        company_id: onedata.cartData.company_id,
        com_perm_id: onedata.cartData.com_perm_id
      }
      await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
      var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
      if (res.result === 'SUCCESS') {
        this.XBaht = res.data[0].x_baht
        this.XBaht = parseFloat(res.data[0].x_baht) / parseFloat(res.data[0].x_point)
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>แต้มขัดข้อง กรุณาลองใหม่ภายหลัง</h3>'
        })
      }
    },
    clickPoint (item, point, baht, discoupon) {
      this.page = 'QTCheckout'
      this.$refs.PointCart.open(this.page, item, point, baht, discoupon)
      this.$EventBus.$emit('getListPoint')
    },
    async calculatePrice (key) {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.pointData = await this.onedata.cartData.point
      this.PointData = this.pointData
      if (this.ShippingData.estimatePrice !== undefined && this.ShippingData.estimatePrice !== null) {
        this.radioTransport = this.ShippingData.tpl_name
        this.shipping_price = this.ShippingData.upCountry === true ? Math.ceil(this.ShippingData.estimatePrice) + Math.ceil(this.ShippingData.upCountryAmount) : Math.ceil(this.ShippingData.estimatePrice)
      } else {
        this.radioTransport = ''
        this.shipping_price = parseFloat(0).toFixed(2)
      }
      this.itemsCart.choose_list[0].total_price_no_vat = 0
      this.itemsCart.choose_list[0].net_price = 0
      // let discountPerProduct = 0
      // if (this.itemsCart.discount_type === 'percent') {
      //   discountPerProduct = this.itemsCart.discount_percent
      // } else if (this.itemsCart.discount_type === 'bath') {
      //   discountPerProduct = this.itemsCart.discount_bath / this.itemsCart.choose_list[0].product_list.length
      // }
      // if (this.itemsCart.choose_list[0].product_list[i].price_show !== undefined) {
      //     this.itemsCart.choose_list[0].product_list[i].revenue_default = (this.itemsCart.choose_list[0].product_list[i].price_show * 7 / 107)
      //   }
      var index = key
      if (Number.isInteger(index)) {
        if (this.itemsCart.choose_list[0].product_list[index].vat_default === 'yes' && (this.itemsCart.choose_list[0].product_list[index].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[index].vat_type === 7)) {
          this.itemsCart.choose_list[0].product_list[index].revenue_default = this.itemsCart.choose_list[0].product_list[index].price_no_vat * 100 / 107
        } else {
          this.itemsCart.choose_list[0].product_list[index].revenue_default = this.itemsCart.choose_list[0].product_list[index].price_no_vat
        }
      }
      var totalTierDiscount = 0
      if (this.itemsCart.discount_type === 'bath') {
        this.itemsCart.discount_type = 'baht'
      }
      if (this.itemsCart.discount_type === 'percent' || this.itemsCart.discount_type === 'baht') {
        if (this.itemsCart.discount_type === 'percent') {
          totalTierDiscount = Number.parseFloat(this.itemsCart.discount_percent)
        } else if (this.itemsCart.discount_type === 'baht') {
          totalTierDiscount = Number.parseFloat(this.itemsCart.discount_bath / this.itemsCart.choose_list[0].product_list.length)
        }
      }
      var allSameVatDefault = true
      var totalPriceNoVat = 0
      var totalPriceVat = 0
      var totalPriceAfterTier = 0
      this.CouponData = Array.isArray(key) ? key : this.onedata.cartData.coupon

      var coupon = this.CouponData
      var point = this.pointData !== '' && this.pointData !== null && this.pointData !== undefined ? this.pointData : 0
      var totalCouponDiscount = 0
      var includeVat = 0
      var excludeVat = 0
      var totalPriceNoVatGeneral = 0
      var newTotalPriceNoVatGeneral = 0
      for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
        this.itemsCart.choose_list[0].product_list[i].revenue_default = Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default)
        var revenueDefaultWithVat = 0
        revenueDefaultWithVat = this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[i].vat_type === 7 ? this.itemsCart.choose_list[0].product_list[i].revenue_default + (this.itemsCart.choose_list[0].product_list[i].revenue_default * 7 / 100) : this.itemsCart.choose_list[0].product_list[i].revenue_default
        var revenueDefaultWithVatTotal = (Number.parseFloat(revenueDefaultWithVat) * Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].quantity))
        if (this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes') {
          this.itemsCart.choose_list[0].product_list[i].revenue_vat = revenueDefaultWithVatTotal
          this.itemsCart.choose_list[0].product_list[i].vat_revenue = this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[i].vat_type === 7 ? (revenueDefaultWithVatTotal * 7 / 107) : 0
        } else {
          this.itemsCart.choose_list[0].product_list[i].vat_revenue = this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[i].vat_type === 7 ? (revenueDefaultWithVatTotal * 7 / 107) : 0
          this.itemsCart.choose_list[0].product_list[i].revenue_vat = revenueDefaultWithVatTotal
        }
        if (this.itemsCart.choose_list[0].product_list[i].product_type === 'general') {
          totalPriceNoVatGeneral += this.itemsCart.choose_list[0].product_list[i].revenue_vat - this.itemsCart.choose_list[0].product_list[i].vat_revenue
        }
        if (i > 0) {
          if (this.itemsCart.choose_list[0].product_list[i].vat_default !== this.itemsCart.choose_list[0].product_list[i - 1].vat_default) {
            allSameVatDefault = false
          }
        }
        totalPriceNoVat += (this.itemsCart.choose_list[0].product_list[i].revenue_default * parseInt(this.itemsCart.choose_list[0].product_list[i].quantity))
        totalPriceVat += this.itemsCart.choose_list[0].product_list[i].revenue_vat
      }
      var couponDiscount = 0
      var couponShippingDiscount = 0
      var totalCheckMaximum = 0
      if (coupon.length > 0) {
        if (coupon[0].discount_type === 'percent' || coupon[0].discount_type === 'baht') {
          if (coupon[0].coupon_type === 'free_shipping') {
            couponShippingDiscount = coupon[0].discount_amount
            if (coupon[0].discount_type === 'percent') {
              totalCheckMaximum = (this.shipping_price * coupon[0].discount_amount) / 100
              if (coupon[0].discount_maximum !== null) {
                if (totalCheckMaximum >= coupon[0].discount_maximum) {
                  couponShippingDiscount = coupon[0].discount_maximum
                }
              }
            }
          } else {
            couponDiscount = coupon[0].discount_amount
            var couponDiscountPerProduct = coupon[0].discount_amount / this.itemsCart.choose_list[0].product_list.length
            if (coupon[0].discount_type === 'percent') {
              if (coupon[0].discount_maximum !== null) {
                totalCheckMaximum = (totalPriceVat * coupon[0].discount_amount) / 100
                if (totalCheckMaximum >= coupon[0].discount_maximum) {
                  coupon[0].discount_type = 'baht'
                  couponDiscountPerProduct = coupon[0].discount_maximum / this.itemsCart.choose_list[0].product_list.length
                  totalCouponDiscount = coupon[0].discount_maximum
                  couponDiscount = coupon[0].discount_maximum
                }
              }
            }
          }
        } else {
          couponDiscount = 1
        }
      }
      // this.shipping_price -= couponShippingDiscount
      this.shipping_price = parseFloat(this.shipping_price - couponShippingDiscount).toFixed(2)

      if (this.itemsCart.choose_list[0].product_list.length > 0) {
        var pointPerProduct = point / this.itemsCart.choose_list[0].product_list.length
        var basePriceToCal = 0
        var totalAfterPoint = 0
        var totalAfterCoupon = 0
        this.itemsCart.choose_list[0].total_tier_discount = parseFloat(0)
        this.itemsCart.choose_list[0].total_coupon_discount = parseFloat(0)
        this.itemsCart.choose_list[0].total_discount = parseFloat(0)
        if (allSameVatDefault) {
          newTotalPriceNoVatGeneral = totalPriceNoVatGeneral
          basePriceToCal = this.itemsCart.choose_list[0].product_list[0].vat_default === 'yes' ? totalPriceVat : totalPriceNoVat
          this.itemsCart.choose_list[0].total_price_no_vat = parseFloat(basePriceToCal).toFixed(2)
          totalAfterPoint = basePriceToCal - point
          var netPriceAfterCoupon = basePriceToCal - point
          if (coupon.length > 0) {
            netPriceAfterCoupon = coupon[0].discount_type === 'no' ? totalAfterPoint : coupon[0].discount_type === 'baht' ? totalAfterPoint - couponDiscount : totalAfterPoint * ((100 - couponDiscount) / 100)
            totalCouponDiscount = coupon[0].discount_type === 'no' ? 0 : coupon[0].discount_type === 'baht' ? couponDiscount : (totalAfterPoint * couponDiscount) / 100
            this.itemsCart.choose_list[0].total_coupon_discount += coupon[0].discount_type === 'no' ? 0 : coupon[0].discount_type === 'baht' ? couponDiscount : (totalAfterPoint * couponDiscount) / 100
          }
          if (this.itemsCart.choose_list[0].product_list[0].vat_default === 'yes') {
            this.itemsCart.choose_list[0].net_price = parseFloat(this.itemsCart.discount_type === 'baht' ? netPriceAfterCoupon - totalTierDiscount : netPriceAfterCoupon * ((100 - totalTierDiscount) / 100)).toFixed(2)
            this.itemsCart.choose_list[0].total_tier_discount += this.itemsCart.discount_type === 'baht' ? Number.parseFloat(totalTierDiscount) : Number.parseFloat((netPriceAfterCoupon * totalTierDiscount) / 100)
            includeVat = this.itemsCart.choose_list[0].product_list[0].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[0].vat_type === '7' ? this.itemsCart.choose_list[0].net_price * (7 / 107) : 0
          } else {
            totalPriceAfterTier = this.itemsCart.discount_type === 'baht' ? netPriceAfterCoupon - (totalTierDiscount * this.itemsCart.choose_list[0].product_list.length) : netPriceAfterCoupon - ((netPriceAfterCoupon * totalTierDiscount) / 100)
            this.itemsCart.choose_list[0].total_tier_discount += this.itemsCart.discount_type === 'baht' ? Number.parseFloat(totalTierDiscount * this.itemsCart.choose_list[0].product_list.length) : Number.parseFloat((netPriceAfterCoupon * totalTierDiscount) / 100)
            excludeVat = this.itemsCart.choose_list[0].product_list[0].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[0].vat_type === '7' ? totalPriceAfterTier * (7 / 100) : 0
            this.itemsCart.choose_list[0].net_price = parseFloat(totalPriceAfterTier + excludeVat).toFixed(2)
          }
          this.itemsCart.choose_list[0].total_discount = parseFloat(totalCouponDiscount + (totalTierDiscount * this.itemsCart.choose_list[0].product_list.length)).toFixed(2)
        } else {
          for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
            if (this.itemsCart.choose_list[0].product_list[i].product_type === 'general') {
              newTotalPriceNoVatGeneral += this.itemsCart.choose_list[0].product_list[i].revenue_default * Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].quantity)
            }
            basePriceToCal = this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes' ? Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_vat) : this.itemsCart.choose_list[0].product_list[i].revenue_default * Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].quantity)
            this.itemsCart.choose_list[0].total_price_no_vat += this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes' ? Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_vat) : this.itemsCart.choose_list[0].product_list[i].revenue_default * Number.parseFloat(this.itemsCart.choose_list[0].product_list[i].quantity)
            totalAfterPoint = basePriceToCal - pointPerProduct
            totalAfterCoupon = basePriceToCal - pointPerProduct
            if (coupon.length > 0) {
              totalAfterCoupon = coupon[0].discount_type === 'no' ? totalAfterPoint : coupon[0].discount_type === 'baht' ? (totalAfterPoint) - couponDiscountPerProduct : totalAfterPoint * ((100 - couponDiscount) / 100)
              this.itemsCart.choose_list[0].total_coupon_discount += coupon[0].discount_type === 'no' ? 0 : coupon[0].discount_type === 'baht' ? couponDiscountPerProduct : (totalAfterPoint * couponDiscount) / 100
            }
            const PriceAfterTier = this.itemsCart.discount_type === 'baht' ? Number.parseFloat(totalAfterCoupon - totalTierDiscount) : Number.parseFloat(totalAfterCoupon - ((totalAfterCoupon * totalTierDiscount) / 100))
            totalPriceAfterTier += this.itemsCart.discount_type === 'baht' ? Number.parseFloat(totalAfterCoupon - totalTierDiscount) : Number.parseFloat(totalAfterCoupon - ((totalAfterCoupon * totalTierDiscount) / 100))
            this.itemsCart.choose_list[0].total_tier_discount += Number.parseFloat(this.itemsCart.discount_type === 'baht' ? totalTierDiscount : (totalAfterCoupon * totalTierDiscount) / 100)
            if (this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes') {
              includeVat += this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[0].vat_type === '7' ? PriceAfterTier * (7 / 107) : 0
            } else {
              excludeVat += this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[0].vat_type === '7' ? PriceAfterTier * (7 / 100) : 0
            }
          }
          this.itemsCart.choose_list[0].total_price_no_vat = parseFloat(this.itemsCart.choose_list[0].total_price_no_vat).toFixed(2)
          this.itemsCart.choose_list[0].net_price = parseFloat(totalPriceAfterTier + excludeVat).toFixed(2)
        }
        this.itemsCart.total_price_no_vat_general = newTotalPriceNoVatGeneral
        this.itemsCart.choose_list[0].total_discount = parseFloat(this.itemsCart.choose_list[0].total_tier_discount + this.itemsCart.choose_list[0].total_coupon_discount + point).toFixed(2)
        this.itemsCart.choose_list[0].total_vat = parseFloat(includeVat + excludeVat).toFixed(2)
        this.itemsCart.choose_list[0].total_quantity = this.itemsCart.choose_list[0].product_list.length
      }

      if (this.itemsCart.use_estimate === 'yes') {
        this.$store.commit('openLoader')
        const dataSendEstimate = {
          seller_shop_id: this.itemsCart.choose_list[0].seller_shop_id,
          product_list: this.itemsCart.choose_list[0].product_list,
          user_address: this.itemsCart.cus_address_data
        }
        await this.$store.dispatch('ActionEstimateEdit', dataSendEstimate)
        var resEstimate = await this.$store.state.ModuleCart.stateEstimateEdit
        if (resEstimate.code === 200) {
          this.itemsCart.choose_list[0].net_price = parseFloat(Number.parseFloat(this.itemsCart.choose_list[0].net_price) + Number.parseFloat(resEstimate.data.shipping_price)).toFixed(2)
          this.shipping_price = parseFloat(resEstimate.data.shipping_price).toFixed(2)
        }
        this.$store.commit('closeLoader')
      } else {
        this.itemsCart.choose_list[0].net_price = parseFloat(Number.parseFloat(this.itemsCart.choose_list[0].net_price) + Number.parseFloat(this.shipping_price)).toFixed(2)
      }

      // for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
      //   this.itemsCart.choose_list[0].product_list[i].revenue_default = parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default)
      //   this.itemsCart.choose_list[0].product_list[i].price = parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default)
      //   if (this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes' && (this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[i].vat_type === 7)) {
      //     this.itemsCart.choose_list[0].product_list[i].price_show = parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default + (this.itemsCart.choose_list[0].product_list[i].revenue_default * 7 / 100))
      //   } else {
      //     this.itemsCart.choose_list[0].product_list[i].price_show = parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default)
      //   }
      //   revenueDefaultWithVat = this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[i].vat_type === 7 ? parseFloat(this.itemsCart.choose_list[0].product_list[i].price + (this.itemsCart.choose_list[0].product_list[i].price * 7 / 100)) : this.itemsCart.choose_list[0].product_list[i].price
      //   revenueDefaultWithVatTotal = parseFloat(parseFloat(revenueDefaultWithVat * parseInt(this.itemsCart.choose_list[0].product_list[i].quantity)))
      //   this.itemsCart.choose_list[0].product_list[i].vat_revenue = this.itemsCart.choose_list[0].product_list[i].vat_type === '7.00' || this.itemsCart.choose_list[0].product_list[i].vat_type === 7 ? parseFloat(revenueDefaultWithVatTotal * 7 / 107) : 0
      //   this.itemsCart.choose_list[0].product_list[i].revenue_vat = parseFloat(revenueDefaultWithVatTotal)
      //   revenueDefaultWithDiscountTotal = revenueDefaultWithVatTotal
      //   this.itemsCart.choose_list[0].product_list[i].price_no_vat_with_total = parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_vat - this.itemsCart.choose_list[0].product_list[i].vat_revenue).toFixed(2)
      //   this.itemsCart.choose_list[0].product_list[i].price_no_vat = parseFloat(this.itemsCart.choose_list[0].product_list[i].price_show).toFixed(2)
      //   this.itemsCart.choose_list[0].total_price_no_vat += parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_vat - this.itemsCart.choose_list[0].product_list[i].vat_revenue)
      //   this.itemsCart.choose_list[0].net_price += parseFloat(revenueDefaultWithDiscountTotal)
      //   vat += this.itemsCart.choose_list[0].product_list[i].vat_revenue
      // }
      // this.itemsCart.choose_list[0].total_vat = parseFloat(vat).toFixed(2)
      // this.itemsCart.choose_list[0].total_price_no_vat = parseFloat(this.itemsCart.choose_list[0].total_price_no_vat).toFixed(2)

      // this.itemsCart.choose_list[0].net_price = parseFloat(this.itemsCart.choose_list[0].net_price + parseFloat(this.shipping_price)).toFixed(2)
    },
    checkQuantity () {
      for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
        if (this.itemsCart.choose_list[0].product_list[i].quantity > this.itemsCart.choose_list[0].product_list[i].effective_stock) {
          this.itemsCart.choose_list[0].product_list[i].quantity = this.itemsCart.choose_list[0].product_list[i].effective_stock
        }
        if (this.itemsCart.choose_list[0].product_list[i].quantity === '') {
          this.itemsCart.choose_list[0].product_list[i].quantity = 1
        }
      }
    },
    async confirm () {
      if (this.orderNumber !== undefined) {
        // this.from !== '' &&  this.id
        this.itemsCart.choose_list[0].invoice_id = this.itemsCart.cus_address_data[0].id
        this.itemsCart.choose_list[0].total_point = this.pointData
        if (this.from !== '') {
          this.$store.commit('openLoader')
          var data = {
            order_number: this.orderNumber,
            order_data: this.itemsCart.choose_list
          }
          await this.$store.dispatch('actionseditOrderQT', data)
          const editQT = await this.$store.state.ModuleCart.stateeditOrderQT
          this.$store.commit('closeLoader')
          if (editQT.code === 200) {
            this.$router.push(`/${this.from}?QUNumber=${this.orderNumber}&status=edit&id=${this.id}`).catch(() => {})
          } else {
            return this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ไม่สามารถสร้างเอกสารได้ กรุณาติดต่อเจ้าหน้าที่'
            })
          }
        }
      } else {
        var productToCalculate = []
        var error = false
        this.itemsCart.choose_list[0].product_list.forEach(element => {
          var vatDefault = element.vat_default
          var vatType = element.vat_type
          if (element.product_attribute_detail === undefined) {
            error = true
            return this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: 'กรุณากรอกข้อมูลให้ถูกต้อง'
            })
          }
          if (typeof element.product_id === 'string') {
            var newProductId = element.product_id.split('_')
            element.product_id = parseInt(newProductId[0])
          }
          productToCalculate.push({
            attribute_priority_1: element.product_attribute_detail.attribute_priority_1,
            attribute_priority_2: element.product_attribute_detail.attribute_priority_2,
            item_code_pr_buyer: null,
            price: vatDefault === 'yes' && (vatType === '7.00' || vatType === 7) ? element.price_no_vat - (element.vat_revenue / element.quantity) : element.price_no_vat,
            product_attribute_id: element.product_attribute_detail.product_attribute_id,
            product_id: element.product_id,
            quantity: element.quantity,
            revenue_default: vatDefault === 'yes' && (vatType === '7.00' || vatType === 7) ? element.price_no_vat - (element.vat_revenue / element.quantity) : element.price_no_vat,
            revenue_vat: element.revenue_vat,
            vat_revenue: element.vat_revenue
          })
        })
        if (!error) {
          this.onedata.cartData.product_to_calculate = productToCalculate
          this.onedata.cartData.shipping_data = this.ShippingData
          this.onedata.cartData.edit_Quotation = 'yes'
          this.onedata.cartData.coupon = this.CouponData
          this.onedata.cartData.usePointOrNot = this.onedata.cartData.point !== 0 && this.onedata.cartData.point !== '' && this.onedata.cartData.point !== null && this.onedata.cartData.point !== undefined ? 'yes' : 'no'
          localStorage.setItem('oneData', Encode.encode(this.onedata))
          this.$router.push('/checkoutSaleOrder').catch(() => {})
        }
      }
    },
    clickCoupon (item, point) {
      this.page = 'QTCheckout'
      if (this.orderNumber === undefined) {
        const product = []
        for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
          const dataproduct = {
            attribute_priority_1: this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_1 == null ? '' : this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_1,
            attribute_priority_2: this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_2 == null ? '' : this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_2,
            product_attribute_id: this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.product_attribute_id,
            product_id: this.itemsCart.choose_list[0].product_list[i].product_id,
            quantity: this.itemsCart.choose_list[0].product_list[i].quantity,
            revenue_default: this.itemsCart.choose_list[0].product_list[i].revenue_default,
            revenue_vat: this.itemsCart.choose_list[0].product_list[i].revenue_vat,
            vat_revenue: this.itemsCart.choose_list[0].product_list[i].vat_revenue
          }
          product.push(dataproduct)
        }
        this.couponData = {
          role_user: 'sale_order_no_JV',
          total_price_general: this.itemsCart.total_price_no_vat_general,
          company_id: this.itemsCart.company_id,
          customer_id: this.itemsCart.customer_id,
          shop_id: this.itemsCart.choose_list[0].seller_shop_id,
          // net_price: parseFloat(this.itemsCart.choose_list[0].net_price),
          net_price: parseFloat(this.itemsCart.choose_list[0].total_price_no_vat),
          price_inc_vat: parseFloat(this.itemsCart.choose_list[0].total_price_vat),
          product: product
        }
        localStorage.setItem('couponData', Encode.encode(this.couponData))
      }
      this.$refs.CouponCart.open(this.page, item, point)
      this.$EventBus.$emit('getListCoupon')
    },
    async getListProduct () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      const auth = {
        headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
      }
      // await this.$store.dispatch('GetProductBySellerID', data)
      const response = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/product/list_product_cart`, data, auth)
      // clg
      if (response.data.code === 200) {
        this.productList = response.data.data
        var productDuplicateId = -1
        for (let i = 0; i < this.productList.list_product.length; i++) {
          if (productDuplicateId === this.productList.list_product[i].product_id) {
            this.productList.list_product[i].product_id += '_' + this.productList.list_product[i].product_attribute_detail.product_attribute_id
          }
          if (this.productList.list_product[i].vat_default === 'yes') {
            this.productList.list_product[i].price_no_vat = parseFloat(parseFloat(this.productList.list_product[i].revenue_default) + ((parseFloat(this.productList.list_product[i].revenue_default) * 7) / 100)).toFixed(2)
          } else {
            this.productList.list_product[i].price_no_vat = parseFloat(parseFloat(this.productList.list_product[i].revenue_default)).toFixed(2)
          }
          if (this.productList.list_product[i].product_attribute_detail.product_attribute_id !== '-1') {
            this.productList.list_product[i].product_name_2 = this.productList.list_product[i].product_name + '(' + this.productList.list_product[i].product_attribute_detail.attribute_priority_1
            this.productList.list_product[i].subtitle = this.productList.list_product[i].product_attribute_detail.attribute_priority_1
            if (this.productList.list_product[i].product_attribute_detail.attribute_priority_1 !== '') {
              this.productList.list_product[i].product_name_2 += ' ' + this.productList.list_product[i].product_attribute_detail.attribute_priority_2 + ')'
              this.productList.list_product[i].subtitle += ' ' + this.productList.list_product[i].product_attribute_detail.attribute_priority_2
            } else {
              this.productList.list_product[i].product_name_2 += ')'
            }
          } else {
            this.productList.list_product[i].product_name_2 = this.productList.list_product[i].product_name
            this.productList.list_product[i].subtitle = ''
          }
          if (this.productList.list_product[i].effective_stock <= 0) {
            this.productList.list_product[i].disabled = true
            this.productList.list_product[i].product_name += '<span style="color:red"> *สินค้าหมด </span>'
          }
          productDuplicateId = this.productList.list_product[i].product_id
        }
      }
      this.$store.commit('closeLoader')
    },
    plusProduct () {
      this.itemsCart.choose_list[0].product_list.push({
        product_name: '',
        quantity: 1,
        unit_type: 'ชิ้น',
        price_no_vat: '0.00',
        revenue_default: '0.00'
      })
    },
    deleteProduct (productindex) {
      this.itemsCart.choose_list[0].product_list.splice(productindex, 1)
      this.calculatePrice()
    },
    save (date) {
      this.$refs.menu.save(date)
    },
    async GetUserDetail () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))

      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionsUserDetailPage', data)
      const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
      if (userdetail.message !== 'This user is unauthorized.') {
        this.userdetail = userdetail.data[0]
        if (this.userdetail.first_name_th === '' && this.userdetail.last_name_th === '') {
          this.prepare_by = '-'
        } else {
          this.prepare_by = this.userdetail.first_name_th + ' ' + this.userdetail.last_name_th
        }
      }
    },
    async GetDetailShop () {
      this.$store.commit('openLoader')
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.result === 'SUCCESS') {
          this.isJVShop = response.data[0].is_JV === 'yes'
          this.Detail.seller_shop_id = this.shopID
          if (response.data[0].shop_image.length !== 0) {
            this.Detail.shop_logo_image.push({
              image_data: response.data[0].shop_image[0].media_path,
              image_data_lazy: response.data[0].shop_image[0].media_path_lazy,
              path: response.data[0].shop_image[0].media_path,
              name: response.data[0].shop_image[0].name,
              size: ''
            })
            this.show = false
            this.showError = false
          } else {
            this.show = false
            this.showError = false
            this.Detail.shop_logo_image = []
          }
          this.shopName = response.data[0].shop_name
          this.taxNumber = response.data[0].tax_id
          this.branch = response.data[0].branch !== undefined ? response.data[0].branch : 'สำนักงานใหญ่'
          this.shopURL = response.data[0].url_name
          this.descriptionShop = response.data[0].shop_description
          this.shortName = response.data[0].short_name + '-20240200001'
          this.mobile = response.data[0].shop_phone.length > 0 ? response.data[0].shop_phone[0].phone : ''
          this.facebook = response.data[0].facebook_url
          if (response.data[0].shipping_method.length !== 0) {
            this.switchShipping = true
            this.SelectShipping = response.data[0].shipping_method
          } else {
            this.switchShipping = false
            this.SelectShipping = []
          }
          if (response.data[0].payment_method !== null) {
            if (response.data[0].payment_method.length !== 0) {
              this.SelectPaymentType = response.data[0].payment_method
            } else {
              this.SelectPaymentType = []
            }
          } else {
            this.SelectPaymentType = []
          }
          this.selectInstallmentType = response.data[0].installment_method
          this.line = response.data[0].line_id
          if (response.data[0].shop_status === 'active') {
            this.openshop = true
          } else {
            this.openshop = false
          }
          if (response.data[0].shop_status === 'active') {
            this.publicshop = true
          } else {
            this.publicshop = false
          }
          if (response.data[0].partner_show === 'yes') {
            this.partner = true
          } else {
            this.partner = false
          }
          this.MerchantKey = response.data[0].merchant_key
          if (response.data[0].payment_costs !== null) {
            if (response.data[0].payment_costs.length !== 0) {
              this.SelectType = 'contact'
              this.SelectTypePay = response.data[0].payment_costs
            } else {
              this.SelectType = 'no_contact'
              this.SelectTypePay = []
            }
          } else {
            this.SelectType = 'no_contact'
            this.SelectTypePay = []
          }
          this.name = response.data[0].first_name
          this.surname = response.data[0].last_name
          this.email = response.data[0].shop_email.length !== 0 ? response.data[0].shop_email[0].seller_email : ''
          this.mobileNumber = response.data[0].shop_phone.length > 1 ? response.data[0].shop_phone[1].phone : ''
          this.Detail.shop_address[0].id = response.data[0].address_detail[0].id
          this.addressDetail = response.data[0].address_detail[0].detail
          this.Detail.shop_address = response.data[0].address_detail
          this.houseNo = response.data[0].address_detail[0].house_no
          if (this.isJVShop) {
            this.teamEmail = response.data[0].team_email
          } else {
            this.teamEmail = ''
          }
          this.subdistrict = response.data[0].address_detail[0].sub_district
          this.district = response.data[0].address_detail[0].district
          this.province = response.data[0].address_detail[0].province
          this.details = response.data[0].address_detail[0].details
          this.zipcode = response.data[0].address_detail[0].zipcode
          this.default_address = response.data[0].address_detail[0].default_address === 'main'
          // this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
        }
      }
    },
    async getAddress () {
      // this.userdetail = []
      this.$store.commit('openLoader')
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const dataRole = JSON.parse(localStorage.getItem('roleUser'))
      const PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      const data = {
        seller_shop_id: ShopID,
        role: dataRole.role,
        cus_id: PartnerID,
        page: 1
      }
      await this.$store.dispatch('actionsGetListCustomerAddress', data)
      const res = await this.$store.state.ModuleShop.stateGetListCustomerAddress
      if (res.code === 200) {
        this.showAddress = false
        this.userdetail = ''
        this.userdetail = res.data.address
        // this.$store.commit('closeLoader')
      }
      this.userdetail.forEach(element => {
        if (element.default_address === 'Y') {
          const roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
          if (roleCustomer === 'general') {
            element.name_th = ''
          } else {
            element.first_name = ''
            element.last_name = ''
          }
          this.CartAddress = [
            {
              name_th: element.name,
              id: element.id,
              first_name: element.first_name,
              last_name: element.last_name,
              detail: element.detail_address,
              house_no: element.house_no,
              room_no: element.room_no,
              floor: element.floor,
              building_name: element.building,
              moo_ban: element.moo_ban,
              moo_no: element.moo_no,
              soi: element.soi,
              yaek: element.yaek,
              street: element.street,
              sub_district: element.sub_district,
              district: element.district,
              province: element.province,
              zip_code: element.postcode,
              phone: element.phone,
              phone_ext: '',
              status: 'Y'
            }
          ]
        }
      })
      await this.getCart()
      this.$store.commit('closeLoader')
    },
    selectTransport (item) {
      if (this.radioTransport === item.tpl_name) {
        this.nameTransport = item.tpl_name
        this.costTransport = item.estimatePrice
        // var shippingData = {
        //   tpl_name: item.tpl_name,
        //   service_provider: item.service_provider,
        //   estimatePrice: item.estimatePrice,
        //   pricePolicy: item.pricePolicy,
        //   upCountry: item.upCountry,
        //   upCountryAmount: item.upCountryAmount,
        //   insureDeclareValue: item.insureDeclareValue,
        //   valueInsuranceFee: item.valueInsuranceFee,
        //   freightInsureEnabled: item.freightInsureEnabled,
        //   freightInsurance: item.freightInsurance,
        //   expressCategory: item.expressCategory,
        //   insured: item.insured,
        //   netEstimatePrice: item.netEstimatePrice,
        //   businessType: item.businessType,
        //   distance: item.distance,
        //   travelTime: item.travelTime,
        //   vehicleType: item.vehicleType,
        //   formulaName: item.formulaName,
        //   formulaCode: item.formulaCode,
        //   riderActive: item.riderActive,
        //   vehicleType_Id: item.vehicleType_Id,
        //   rawEstimatePrice: item.rawEstimatePrice,
        //   discountAmount: item.discountAmount,
        //   discountAmountPercent: item.discountAmountPercent,
        //   topUpAmount: item.topUpAmount,
        //   topUpAmountPercent: item.topUpAmountPercent,
        //   serviceType: item.serviceType
        // }
        var shippingData = item
        this.ShippingData = shippingData
        this.shipping_price = parseFloat(shippingData.estimatePrice).toFixed(2)
        this.calculatePrice('shipping')
        this.DialogTransport = false
      }
    },
    async EstimateCost () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      // var comPermId = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyId = companyDataID.company.company_id
      } else {
        companyId = -1
      }
      const ShopID = JSON.parse(localStorage.getItem('ShopID'))
      const PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var conditionMet = false
      if (this.userdetail[0].detail_address === '' || this.userdetail.length === 0) {
        this.DialogTransport = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาเลือกที่อยู่จัดส่งสินค้าก่อน หรือ กรอก/เพิ่มที่อยู่จัดส่งสินค้าให้สมบูรณ์ก่อน</h3>'
        })
        this.DialogAddress = true
        conditionMet = true
      }
      if (conditionMet) {
        this.$store.commit('closeLoader')
        return
      }
      var data = {
        address: this.CartAddress,
        role_user: dataRole.role,
        company_id: companyId,
        customer_id: PartnerID,
        com_perm_id: '-1',
        seller_shop_id: ShopID
      }
      await this.$store.dispatch('actionsEstimateCost', data)
      var res = await this.$store.state.ModuleCart.stateEstimateCost
      if (res.result === 'Success') {
        this.DialogTransport = true
        this.estimateCostData = res.data
        this.$store.commit('closeLoader')
        if (this.estimateCostData.length === 0 && this.itemsCart.shipping_method !== 0) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ไม่มีขนส่งรองรับ</h3>'
          })
          this.backstep()
        }
      } else if (res.code === 400) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          html: `<h4>${res.message}</h4>`
        })
        this.backstep()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.DialogTransport = false
      }
    },
    async EstimateCostEdit () {
      this.$store.commit('openLoader')
      const dataSendlistEstimateEdit = {
        seller_shop_id: this.itemsCart.choose_list[0].seller_shop_id,
        product_list: this.itemsCart.choose_list[0].product_list,
        user_address: this.itemsCart.cus_address_data
      }
      await this.$store.dispatch('actionslistEstimateCostEdit', dataSendlistEstimateEdit)
      var res = await this.$store.state.ModuleCart.statelistEstimateCostEdit
      if (res.result === 'SUCCESS') {
        this.DialogTransport = true
        this.estimateCostData = res.data
        this.$store.commit('closeLoader')
        if (this.estimateCostData.length === 0 && this.itemsCart.shipping_method !== 0) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ไม่มีขนส่งรองรับ</h3>'
          })
          this.backstep()
        }
      } else if (res.code === 400) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          html: `<h4>${res.message}</h4>`
        })
        this.backstep()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
        this.DialogTransport = false
      }
    },
    uploadImageShop () {
      document.getElementById('imageShop').click()
    },
    UploadImageShop () {
      this.show = true
      this.showError = false
      this.showErrorText = ''
      this.percentUpload = 0
      var data = {}
      const element = this.DataImageShop
      const imageSize = element.size / 1024 / 1024
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
        this.Detail.shop_logo_image = []
        if (imageSize < 1) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = async () => {
            var resultReader = reader.result
            var url = URL.createObjectURL(element)
            data = {
              image: [resultReader.split(',')[1]],
              type: 'shop',
              seller_shop_id: this.shopID
            }
            await this.$store.dispatch('actionsUploadToS3', data)
            var response = await this.$store.state.ModuleShop.stateUploadToS3
            if (response.message === 'List Success.') {
              this.Detail.shop_logo_image.push({
                image_data: response.data.list_path[0].path,
                image_data_lazy: response.data.list_path[0].path_lazy,
                path: url,
                name: this.DataImageShop.name,
                size: this.DataImageShop.size
              })
              setInterval(() => {
                if (this.percentUpload === 75) {
                  this.show = false
                }
                this.percentUpload += 25
              }, 100)
            }
          }
        } else {
          this.show = false
          this.showError = true
          this.showErrorText = 'ไฟล์มีขนาดใหญ่เกินไป'
          this.Detail.shop_logo_image.push({
            name: this.DataImageShop.name
          })
          // this.$swal.fire({
          //   icon: 'warning',
          //   text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 1 MB',
          //   showConfirmButton: false,
          //   timer: 1500
          // })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    cancelImageShop () {
      this.show = false
      this.percentUpload = 0
      this.DataImageShop = []
      this.Detail.shop_logo_image = []
    },
    async getCart () {
      this.$store.commit('openLoader')
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var PartnerID = JSON.parse(localStorage.getItem('PartnerID'))
      var res

      this.checkOwnShop = 'N'
      this.cartData = onedata.cartData
      this.cartData.company_id = dataRole.role === 'sale_order_no_JV' ? -1 : PartnerID

      this.cartData.address = this.CartAddress
      if (this.cartData.type_shipping === '') {
        this.cartData.type_shipping = 'online'
      }
      // this.cartData.address = CartAddress
      if (this.reShippingData === false && this.radios === 'radio-2') {
        Vue.set(this.cartData, 'shipping_data', this.ShippingData)
      } else {
        this.ShippingData = []
        Vue.set(this.cartData, 'shipping_data', this.ShippingData)
        this.radioTransport = ''
        this.radioCreditTerm = 'No'
        this.radioPayment = ''
        this.reShippingData = false
      }

      await this.$store.dispatch('ActionGetCart', this.cartData)
      res = await this.$store.state.ModuleCart.stateGetCart

      if (res.message === 'Get cart success') {
        this.itemsCart = res.data
        for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
          // this.itemsCart.choose_list[0].product_list[i].price_no_vat = parseFloat(parseInt(this.itemsCart.choose_list[0].product_list[i].revenue_default) + parseInt(this.itemsCart.choose_list[0].product_list[i].vat_include)).toFixed(2)
          if (this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes') {
            this.itemsCart.choose_list[0].product_list[i].price_no_vat = parseFloat(parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default) + ((parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default) * 7) / 100)).toFixed(2)
          } else {
            this.itemsCart.choose_list[0].product_list[i].price_no_vat = parseFloat(parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default)).toFixed(2)
          }
        }
        this.itemsCart.choose_list[0].total_price_no_vat = parseFloat(this.itemsCart.choose_list[0].total_price_no_vat).toFixed(2)
        this.itemsCart.choose_list[0].total_discount = parseFloat(this.itemsCart.total_discount).toFixed(2)
        this.itemsCart.choose_list[0].total_discount = 0
        this.itemsCart.choose_list[0].total_shipping = parseFloat(this.itemsCart.choose_list[0].total_shipping).toFixed(2)
        this.itemsCart.choose_list[0].total_vat = parseFloat(this.itemsCart.choose_list[0].total_vat).toFixed(2)
        this.itemsCart.choose_list[0].net_price = parseFloat(this.itemsCart.choose_list[0].net_price - this.itemsCart.total_discount).toFixed(2)
        if (this.shipping_price === '') {
          this.shipping_price = parseFloat(0).toFixed(2)
        }

        // this.itemsCart.choose_list[0].product_list.forEach(element => {
        //   if (element.item_code_pr_buyer === null) {
        //     element.item_code_pr_buyer = ''
        //   }
        // })
        if (res.data.check_own_shop === 'yes') {
          this.checkOwnShop = 'Y'
        } else {
          this.checkOwnShop = 'N'
        }
        if (this.itemsCart.total_price_no_vat >= 50000) {
          this.contractSet = true
        }
        // this.googleSentData()
        if (this.itemsCart.address_data.length !== 0) {
          if (dataRole.role === 'purchaser') {
            var addressPurchaser = ''
            this.choose_list = this.itemsCart.choose_list[0].pay_type
            this.address_data = this.itemsCart.address_data[0]
            this.Fullname = this.address_data.first_name + ' ' + this.address_data.last_name
            // addressPurchaser = this.address_data.detail + ' ' + 'แขวง/ตำบล' + ' ' + this.address_data.district + ' ' + 'จังหวัด' + ' ' + this.address_data.province + ' ' + this.address_data.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + this.address_data.phone
            this.Address = addressPurchaser
          } else {
            this.itemsCart.address_data.forEach(element => {
              if (element.default_address === 'Y' || element.default_address !== undefined) {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address = ''
                address = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address
              } else {
                this.address_data = element
                this.Fullname = element.first_name + ' ' + element.last_name
                var address1 = ''
                address1 = element.detail + ' ' + 'แขวง/ตำบล' + ' ' + element.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + element.district + ' ' + 'จังหวัด' + ' ' + element.province + ' ' + element.zip_code + ' ' + 'เบอร์โทรศัพท์' + ' ' + element.phone
                this.Address = address1
              }
            })
          }
        } else {
          this.Fullname = ''
          this.Address = 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'
        }
        // get user data
        // var data = {
        //   role_user: dataRole.role
        // }
        // await this.$store.dispatch('actionsUserDetailPage', data)
        // var response = await this.$store.state.ModuleUser.stateUserDetailPage
        var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
        var user = response.data[0]
        this.buyer_name = user.first_name_th + ' ' + user.last_name_th
        this.buyer_phone = user.phone
        this.buyer_email = user.email
        // get admin data
        const sendId = { user_id: user.id }
        await this.$store.dispatch('ActionGetAdminData', sendId)
        var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
        var adminStatus = false
        if (responseAdmin.data.length !== 0) {
          adminStatus = true
        } else {
          adminStatus = false
        }
        if (adminStatus === true) {
          this.sentDataPPL()
        }
        this.overlay = false
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ใส่ข้อมูลไม่ครบ'
        })
        this.backstep()
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'SERVER ERROR'
        })
        this.backstep()
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง'
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ'
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${'รหัสสินค้า' + ' ' + res.data[0].sku + ' ' + 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่'}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        this.backstep()
      } else if (res.message === 'ไม่พบที่อยู่สำหรับจัดส่งสินค้า กรุุณาทำการเพิ่มข้อมูลที่อยู่ใหม่อีกครั้ง') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/addressProfileMobile' })
        } else {
          this.$router.push({ path: '/addressProfile' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.SelectCouponOrPoint = true
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else if (res.message === 'Company User Permission Not Found.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        })
        this.backstep()
      } else if (res.message === 'Not found cart') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 2500,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        // })
        this.backstep()
        this.goHomePage()
      } else {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        await this.$EventBus.$emit('CancleBookCouponCheckout')
        this.backstep()
      }
      this.ShippingData = this.onedata.cartData.shipping_data
      if (!Array.isArray(this.ShippingData)) {
        this.shipping_price = parseFloat(this.ShippingData.estimatePrice).toFixed(2)
        this.calculatePrice('shipping')
      }
      this.calculatePrice('shipping')
      // this.$store.commit('closeLoader')
    },
    async getOrderDetail () {
      this.$store.commit('openLoader')
      const dataSend = {
        order_number: this.orderNumber
      }
      await this.$store.dispatch('ActionGetOrderEdit', dataSend)
      var res = await this.$store.state.ModuleCart.stateGetOrderEdit
      if (res.message === 'Get cart success') {
        this.itemsCart = res.data
        this.PointData = this.itemsCart.choose_list[0].total_point
        this.shopID = this.itemsCart.choose_list[0].seller_shop_id
        this.SelectShipping = []

        for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
          // this.itemsCart.choose_list[0].product_list[i].price_no_vat = parseFloat(parseInt(this.itemsCart.choose_list[0].product_list[i].revenue_default) + parseInt(this.itemsCart.choose_list[0].product_list[i].vat_include)).toFixed(2)
          this.itemsCart.choose_list[0].product_list[i].price_no_vat = Number.parseFloat(parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default))
          if (this.itemsCart.choose_list[0].product_list[i].vat_default === 'yes') {
            this.itemsCart.choose_list[0].product_list[i].price_no_vat += Number.parseFloat(parseFloat(this.itemsCart.choose_list[0].product_list[i].revenue_default * 7) / 100)
          }
        }
        this.itemsCart.choose_list[0].total_price_no_vat = parseFloat(this.itemsCart.choose_list[0].total_price_no_vat).toFixed(2)
        this.itemsCart.choose_list[0].total_discount = parseFloat(Number(this.itemsCart.choose_list[0].total_discount) + Number(this.PointData)).toFixed(2)
        // this.itemsCart.choose_list[0].total_discount = 0
        this.itemsCart.choose_list[0].total_shipping = parseFloat(this.itemsCart.choose_list[0].total_shipping).toFixed(2)
        this.itemsCart.choose_list[0].total_vat = parseFloat(this.itemsCart.choose_list[0].total_vat).toFixed(2)
        this.itemsCart.choose_list[0].net_price = parseFloat(this.itemsCart.choose_list[0].net_price).toFixed(2)
        if (this.shipping_price === '') {
          this.shipping_price = parseFloat(0).toFixed(2)
        }
        const product = []
        for (let i = 0; i < this.itemsCart.choose_list[0].product_list.length; i++) {
          const dataproduct = {
            attribute_priority_1: this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_1 == null ? '' : this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_1,
            attribute_priority_2: this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_2 == null ? '' : this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.attribute_priority_2,
            product_attribute_id: this.itemsCart.choose_list[0].product_list[i].product_attribute_detail.product_attribute_id,
            product_id: this.itemsCart.choose_list[0].product_list[i].product_id,
            quantity: this.itemsCart.choose_list[0].product_list[i].quantity,
            revenue_default: this.itemsCart.choose_list[0].product_list[i].revenue_default,
            revenue_vat: this.itemsCart.choose_list[0].product_list[i].revenue_vat,
            vat_revenue: this.itemsCart.choose_list[0].product_list[i].vat_revenue
          }
          product.push(dataproduct)
        }
        this.couponData = {
          role_user: 'sale_order_no_JV',
          total_price_general: Number(this.itemsCart.choose_list[0].total_price_no_vat),
          company_id: this.itemsCart.company_id,
          customer_id: this.itemsCart.customer_id,
          shop_id: this.itemsCart.choose_list[0].seller_shop_id,
          // net_price: parseFloat(this.itemsCart.choose_list[0].net_price),
          net_price: parseFloat(this.itemsCart.choose_list[0].total_price_no_vat),
          price_inc_vat: parseFloat(this.itemsCart.choose_list[0].total_price_vat),
          product: product
        }
        localStorage.setItem('couponData', Encode.encode(this.couponData))
        // this.itemsCart.choose_list[0].product_list.forEach(element => {
        //   if (element.item_code_pr_buyer === null) {
        //     element.item_code_pr_buyer = ''
        //   }
        // })
        if (res.data.check_own_shop === 'yes') {
          this.checkOwnShop = 'Y'
        } else {
          this.checkOwnShop = 'N'
        }
        if (this.itemsCart.total_price_no_vat >= 50000) {
          this.contractSet = true
        }
        // get user data
        // var data = {
        //   role_user: dataRole.role
        // }
        // await this.$store.dispatch('actionsUserDetailPage', data)
        // var response = await this.$store.state.ModuleUser.stateUserDetailPage
        var response = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
        var user = response.data[0]
        this.buyer_name = user.first_name_th + ' ' + user.last_name_th
        this.buyer_phone = user.phone
        this.buyer_email = user.email
        // get admin data
        const sendId = { user_id: user.id }
        await this.$store.dispatch('ActionGetAdminData', sendId)
        var responseAdmin = await this.$store.state.ModuleCart.stateGetAdminData
        var adminStatus = false
        if (responseAdmin.data.length !== 0) {
          adminStatus = true
        } else {
          adminStatus = false
        }
        if (adminStatus === true) {
          this.sentDataPPL()
        }
        this.overlay = false
      } else if (res.message === 'Some parameter missing. [product_to_cal, shop_to_cal, address_id]') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ใส่ข้อมูลไม่ครบ'
        })
        this.backstep()
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'SERVER ERROR'
        })
        this.backstep()
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง'
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ'
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${'รหัสสินค้า' + ' ' + res.data[0].sku + ' ' + 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่'}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        this.backstep()
      } else if (res.message === 'ไม่พบที่อยู่สำหรับจัดส่งสินค้า กรุุณาทำการเพิ่มข้อมูลที่อยู่ใหม่อีกครั้ง') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/addressProfileMobile' })
        } else {
          this.$router.push({ path: '/addressProfile' })
        }
      } else if (res.message === 'This user is unauthorized.') {
        this.SelectCouponOrPoint = true
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else if (res.message === 'Company User Permission Not Found.') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        })
        this.backstep()
      } else if (res.message === 'Not found cart') {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        // this.$swal.fire({
        //   showConfirmButton: false,
        //   timer: 2500,
        //   timerProgressBar: true,
        //   icon: 'error',
        //   text: 'กรุณาตรวจสอบ เนื่องจากมีการเปลี่ยนแปลงสิทธิ์ของผู้ใช้งาน'
        // })
        this.backstep()
        this.goHomePage()
      } else {
        this.SelectCouponOrPoint = true
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
        await this.$EventBus.$emit('CancleBookCouponCheckout')
        this.backstep()
      }
    }
  }
}
</script>

<style scoped>
input:focus {
 outline: 1px solid #27AB9C;
}

textarea:focus {
 outline: 1px solid #27AB9C;
}

input:disabled {
 background-color: rgb(220, 220, 220);
 pointer-events:none;
}

/* input:hover {
 outline: 1px solid #27AB9C;
} */

.add-product {
  background-size: cover;
}

.add-product:hover {
  background-color: rgba(206, 206, 206, 0.54);
  /* border-radius: 10px; */
}

</style>
