<template>
  <div class="body" style="margin: 12px 18px 12px 18px">
    <v-col style="margin-left: 15px">
      <v-row class="d-flex align-center mt-0" style="margin-left: -20px">
        <a href="/orderCompany">
          <v-icon style="color: #27ab9c">mdi-chevron-left</v-icon>
        </a>
        <h1 class="renew" style="margin-top: 10px; margin-left: 10px">
          Change & Renew Order
        </h1>
      </v-row>
      <v-row style="margin-top: 20px">
        <p class="subtitle">เลขที่อ้างอิงรหัสสั่งซื้อ :</p>
        <p class="refNoB">{{ refNo }}</p>
        <p class="refNoB">|</p>
        <span @click="OpenPDF()">
          <a
            ><v-icon style="color: #27ab9c; margin-left: 5px"
              >mdi-eye-outline</v-icon
            ></a
          >
          <a class="quotation_sample"><u>ตัวอย่างใบเสนอราคา</u></a>
        </span>
      </v-row>
      <!-- รายการสินค้าที่สั่งซื้อ -->
      <v-row style="margin-top: 20px">
        <h1 class="List_of_products">รายการสินค้าที่สั่งซื้อ</h1>
        <v-divider
          style="
            border-top: 2px solid #ebebeb;
            margin-left: 16px;
            margin-right: 25px;
            display: flex;
            align-self: center;
            margin-bottom: 10px;
          "
        ></v-divider>
      </v-row>
      <v-row style="margin-top: 20px"> </v-row>
    </v-col>
    <v-col style="margin-top: -20px">
      <a-table
        :style="{ 'margin-top': index === 0 ? '' : '12px' }"
        :showHeader="true"
        :columns="headers"
        :data-source="products"
        :locale="{ emptyText: 'ไม่มีข้อมูลสินค้าในตาราง' }"
        :rowKey="(record, index) => checkRowKey(record)"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange
        }"
        @update:selected="onSelectionChange"
        :pagination="false"
      >
        <template slot="title">
          <v-row class="text-left">
            <v-col cols="6">
              <v-row class="ml-2">
                <v-checkbox
                  style="margin-top: -1px"
                  class="float-left"
                  color="#27AB9C"
                  v-model="selectAll"
                  @change="onSelectAllChange"
                ></v-checkbox>
                <span
                  class="mt-1"
                  style="
                    font-weight: 500;
                    line-height: 24px;
                    font-size: 16px;
                    color: #333333;
                  "
                  >{{ products.length }} รายการสินค้า</span
                >
              </v-row>
            </v-col>
            <v-col>
              <v-row
                class="d-flex justify-end"
                style="margin-bottom: 14px; margin-right: 15px"
              >
                <v-btn
                  color="#27AB9C"
                  height="32px"
                  width="149px"
                  style="color: white"
                  @click="openModalPurchaseOrder(index)"
                  ><v-icon>mdi-plus</v-icon>เพิ่มรายการสินค้า</v-btn
                >
                <v-btn
                  :disabled="selectedRowKeys.length > 0 ? false : true"
                  outlined
                  color="red"
                  height="32px"
                  width="149px"
                  style="margin-left: 10px"
                  @click="deleteProductAll()"
                  ><v-icon>mdi-delete-outline</v-icon>ลบรายการที่เลือก</v-btn
                >
              </v-row>
            </v-col>
          </v-row>
        </template>
        <template slot="productdetails" slot-scope="text, record">
          <v-row>
            <v-col cols="12" md="4" class="pr-0 py-1">
              <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
              <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
            </v-col>
            <v-col cols="12" md="8">
              <p class="mb-0">{{ record.product_name }}</p>
              <!-- <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
              <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span> -->
            </v-col>
          </v-row>
        </template>
        <template slot="actions" slot-scope="text, record">
          <v-icon
            color="red"
            class="button-edit-delete"
            @click="deleteProduct(record)"
          >
            mdi-delete-outline
          </v-icon>
        </template>
        <template slot="revenue_price"  slot-scope="products">
          <span> {{ Number(products.revenue_price * products.quantity).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} </span>
        </template>
        <template slot="revenue_default"  slot-scope="text, products">
          <span> {{ Number(products.revenue_default).toLocaleString(undefined, { minimumFractionDigits: 2 }) }} </span>
        </template>
        <template slot="quantity" slot-scope="text, products, record">
          <v-col cols="12" class="py-0 px-0">
            <v-btn
              elevation="1"
              color="#27AB9C"
              x-small
              :max-width="24"
              :min-height="24"
              style="min-width: 24px !important"
              @click="
                products.quantity--, products.quantity >= 1 ? changeQuantity(products, record, 'DELETE') : products.quantity++
              "
              :disabled="products.quantity <= 1 ? '' : disabled"
            >
              <v-icon small color="white">mdi-minus</v-icon>
            </v-btn>
            <input
              v-model="products.quantity"
              @change="changeQuantity(products, record, 'EDITE')"
              class="AddNumberProduct"
              size="4"
              type="text"
              style="font-size: 16px;text-align: center;"
              oninput="this.value = this.value.replace(/^[0]/, '').replace(/[^0-9.]/g, '1').replace(/(\..*)\./g, '$1')"
            />
            <v-btn
              elevation="1"
              color="#27AB9C"
              x-small
              class="plus-icon"
              :max-width="24"
              :min-height="24"
              style="min-width: 24px !important"
              @click="
                products.quantity++, changeQuantity(products, record, 'ADD')
              "
            >
              <v-icon small color="white"> mdi-plus</v-icon>
            </v-btn>
          </v-col>
        </template>
      </a-table>
    </v-col>
    <!-- <v-divider
      style="
        border-top: 2px solid #ebebeb;
        margin-left: 15px;
        margin-right: 15px;
      "
    ></v-divider> -->
    <v-row class="mt-3">
      <v-col>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ราคาไม่รวมภาษีมูลค่าเพิ่ม</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <span class="cost"><span>{{ Number(total_price_no_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท</span
            >
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ส่วนลด</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost">0.00 บาท</p>
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ภาษีมูลค่าเพิ่ม</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost">
              <span>{{ Number(total_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท
            </p>
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ราคารวมภาษีมูลค่าเพิ่ม</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost">
              <span>{{ Number(total_price_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท
            </p>
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end a">
          <v-col cols="4" class="d-flex justify-end">
            <p>ราคารวมทั้งหมด</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost a">
              <span>{{ Number(total_price_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท
            </p>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <!-- รายละเอียดรายการสั่งซื้อ -->
    <v-col style="margin-left: 24px; margin-right: 24px">
      <v-row style="margin-top: 20px">
        <h1 class="List_of_products">รายละเอียดรายการสั่งซื้อ</h1>
        <v-divider
          style="
            border-top: 2px solid #ebebeb;
            margin-left: 16px;
            margin-right: 38px;
            display: flex;
            align-self: center;
            margin-bottom: 10px;
          "
        ></v-divider>
      </v-row>
      <v-row style="margin-left: -12px; margin-top: 25px">
        <v-col cols="4">
          <v-row>
            <p style="color: #636363">วันที่เริ่มสัญญาเดิม :</p>
            <span> {{ formatDateToShowText(startDate) }}</span>
          </v-row>
        </v-col>
        <v-col cols="4">
          <v-row>
            <p style="color: #636363">วันที่สิ้นสุดสัญญาเดิม :</p>
            <span> {{ formatDateToShowText(endDate) }}</span>
          </v-row>
        </v-col>
      </v-row>
    </v-col>
    <v-col style="margin-left: 12px; margin-right: 24px">
      <v-row>
        <v-col cols="12" md="4" sm="6">
          <span style="line-height: 24px; font-size: 16px; color: #333333"
            >วันที่เริ่มสัญญา <span style="color: red">*</span></span
          >
          <v-dialog
            ref="dialogContractStartDate"
            v-model="modalContractStartDate"
            :return-value.sync="date"
            persistent
            width="290px"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                readonly
                disabled
                v-model="FormattedstartDateShow"
                v-bind="attrs"
                v-on="on"
                outlined
                dense
                placeholder="วว/ดด/ปปปป"
                ><v-icon disabled slot="append" color="#27AB9C"
                  >mdi-calendar-multiselect</v-icon
                ></v-text-field
              >
            </template>
            <v-date-picker
              readonly
              color="#27AB9C"
              v-model="FormattedstartDate"
              scrollable
              locale="Th-th"
              :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
              >
              <v-spacer></v-spacer>
              <v-btn
                text
                color="primary"
                @click="closeModalContractStartDate()"
              >
                ยกเลิก
              </v-btn>
              <v-btn
                text
                color="primary"
                @click="$refs.dialogContractStartDate.save(date)"
              >
                ตกลง
              </v-btn>
            </v-date-picker>
          </v-dialog>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <span style="line-height: 24px; font-size: 16px; color: #333333">
            วันที่สิ้นสุดสัญญา <span style="color: red">*</span>
          </span>
          <v-dialog
            ref="dialogContractEndDate"
            v-model="modalContractEndDate"
            persistent
            width="290px"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="endDateContractShow"
                v-on="on"
                v-bind="attrs"
                outlined
                dense
                placeholder="วว/ดด/ปปปป"
                ><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              color="#27AB9C"
              v-model="endDateContract"
              scrollable
              reactive
              locale="Th-th"
              :min="FormattedstartDate"
            >
              <v-spacer></v-spacer>
              <v-btn text color="primary" @click="closeModalContractEndDate()">
                ยกเลิก
              </v-btn>
              <v-btn
                text
                color="primary"
                @click="getSelectDateEnd(endDateContract)"
              >
                ตกลง
              </v-btn>
            </v-date-picker>
          </v-dialog>
        </v-col>
        <v-col cols="4" style="display: flex; align-items: center">
          <span
            style="
              color: rgb(99, 99, 99);
              padding-left: 50px;
              padding-bottom: 20px;
            "
            >Pay Type :
          </span>
          <span
            style="
              padding-left: 5px;
              padding-bottom: 20px;
              font-size: 16px;
              /* font-family: Poppins; */
              font-weight: 500;
              line-height: 24px;
            "
            >{{ pay_type }}</span
          >
        </v-col>
      </v-row>
    </v-col>
    <v-col style="margin-left: 12px; margin-right: 24px; margin-top: -20px">
      <v-row style="margin-left: 6px">
        <v-col cols="4" style="align-self: center">
          <v-row>
            <v-switch
              inset
              v-model="switchState"
              disabled
              style="width: 42px; height: 24px"
            ></v-switch>
            <p
              style="margin-left: 20px; align-self: flex-end; margin-top: 20px"
              v-if="switchState"
            >
              เลือกใช้ส่วนลด
            </p>
            <p
              style="margin-left: 20px; align-self: flex-end; margin-top: 20px"
              v-else
            >
              ไม่ใช้ส่วนลด
            </p>
          </v-row>
        </v-col>
        <v-col cols="8" style="margin-left: -8px">
          <v-row>
          <v-col>
              <label for="">ส่วนลด</label>
              <v-select
                v-if="switchState"
                dense
                outlined
                placeholder="เลือกส่วนลด"
              ></v-select>
              <v-select
                v-else
                dense
                outlined
                disabled
                placeholder="เลือกส่วนลด"
              ></v-select>
            </v-col>
            <v-col>
              <label for="">ยอดส่วนลด</label>
              <v-text-field
                v-if="switchState"
                dense
                outlined
                placeholder="0.00"
                suffix="บาท"
              ></v-text-field>
              <v-text-field
                v-else
                dense
                outlined
                disabled
                placeholder="0.00"
                suffix="บาท"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" style="margin-top: -20px">
          <v-row>
            <v-checkbox v-model="checkbox"></v-checkbox>
            <v-row style="margin-top: 20px; margin-left: 6px">
              <p
                style="
                  font-size: 16px;
                  /* font-family: Poppins; */
                  font-weight: 500;
                  line-height: 24px;
                "
              >
                ต้องการระบุสัญญาบริการ
              </p>
            </v-row>
          </v-row>
        </v-col>
        <v-col style="margin-left: -15px">
          <p
            style="
              font-size: 16px;
              /* font-family: Poppins; */
              line-height: 24px;
              margin-bottom: 5px;
            "
          >
            หมายเหตุ
          </p>
          <v-textarea
            outlined
            v-model="remark"
            placeholder="ระบุหมายเหตุ"
            oninput="this.value = this.value.replace(/^[0-9!/*๑-๙฿@#$%&()_+{}:;<>,.?~]|^[\s]/, '')"
          ></v-textarea>
        </v-col>
      </v-row>
      <v-row class="d-flex justify-center" style="margin-bottom: 14px">
        <v-btn
          outlined
          color="#27AB9C"
          height="32px"
          width="146px"
          @click="dialog_Cancel = true"
          >ยกเลิก</v-btn
        >
        <v-btn
          :disabled="endDateContract === '' || endDateContractShow === ''"
          color="#27AB9C"
          height="32px"
          width="146px"
          style="color: white; margin-left: 16px"
          @click="dialog_Confirm = true"
          >ยืนยันการขอซื้อ</v-btn
        >
      </v-row>
    </v-col>
    <PuchaseOrderModalOrdal ref="ModalPurchaseOrder"/>
    <v-dialog v-model="dialog_Confirm" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">บันทึก Change&Renew Order</font>
          </span>
          <v-btn icon dark @click="dialog_Confirm = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  font-weight: 200;
                  font-size: 18px;
                  line-height: 26px;
                  color: #333333;
                "
              >
                ต้องการต่อสัญญาการสั่งซื้อสินค้าใช่หรือไม่
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn
              dense
              dark
              outlined
              color="#27AB9C"
              class="pl-7 pr-7 mt-1"
              @click="dialog_Confirm = false"
            >
              ยกเลิก
            </v-btn>
            <v-btn
              dense
              color="#27AB9C"
              class="ml-4 mt-1 pl-8 pr-8 white--text"
              @click="confirm()"
            >
              ยืนยัน
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Confirm2" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">บันทึก Chage&Renew Order</font>
          </span>
          <v-btn icon dark @click="dialog_Confirm2 = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row style="display: contents">
              <v-icon x-large style="font-size: 85px; color: #27ab9c"
                >mdi-checkbox-marked-circle</v-icon
              >
            </v-row>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  color: var(--primary, #27ab9c);
                  text-align: center;
                  /* font-family: Poppins; */
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 24px;
                "
              >
                คุณได้ทำต่อสัญญาการสั่งซื้อสินค้า เรียบร้อย
                กรุณารอผู้ขายอนุมัติการสั่งซื้อ
              </span>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Cancel" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">ยกเลิก Change&Renew Order</font>
          </span>
          <v-btn icon dark @click="dialog_Cancel = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row class="py-2 px-0 mt-4" style="display: flex; justify-content: center">
              <span style="font-weight: 200;font-size: 18px;line-height: 26px;color: #333333;">
                ต้องการยกเลิกการ Change&Renew Orderใช่หรือไม่
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-1" @click="dialog_Cancel = false">
              ยกเลิก
            </v-btn>
            <v-btn dense color="#27AB9C" class="ml-4 mt-1 pl-8 pr-8 white--text" @click="OpenCancelChangeAndRenew()">
              ยืนยัน
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Cancel2" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">ยกเลิก Change&Renew Order</font>
          </span>
          <v-btn icon dark @click="CancelChangeAndRenew()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row style="display: contents">
              <v-icon x-large style="font-size: 85px; color: #27ab9c"
                >mdi-checkbox-marked-circle</v-icon
              >
            </v-row>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  color: var(--primary, #27ab9c);
                  text-align: center;
                  /* font-family: Poppins; */
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 24px;
                "
              >
                คุณได้ยกเลิกการทำรายการดังกล่าวเรียบร้อย
              </span>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  data () {
    return {
      dialog_Confirm: false,
      dialog_Confirm2: false,
      dialog_Cancel: false,
      dialog_Cancel2: false,
      dataRole: '',
      selected: [],
      checkAll: false,
      newDate: new Date().toISOString().substr(0, 10),
      // date1: false,
      dialog: false,
      modalContractStartDate: false,
      modalContractEndDate: false,
      dataResponseDetail: [],
      products: [],
      // checkAll: [],
      start_date_contact: '',
      pay_type: 'Onetime',
      remark: '',
      startDate: '',
      startDateShow: '',
      FormattedstartDate: '',
      FormattedstartDateShow: '',
      end_date: '',
      checkbox: false,
      checkboxAll: false,
      switchState: false,
      // coupon: 'ใช้ส่วนลด',
      total_quantity: 0,
      total_price_no_vat: 0,
      total_discount: 0,
      total_price_discount: 0,
      total_vat: 0,
      total_price_vat: 0,
      total_price_vat_with_gp: 0,
      total_shipping: 0,
      dataOrderchang: {
        total_quantity: '',
        total_price_no_vat: '',
        total_discount: '',
        total_price_discount: '',
        total_vat: '',
        total_price_vat: '',
        total_price_vat_with_gp: '',
        total_shipping: '',
        net_price: '',
        order_number: '',
        change_reason: '',
        start_date_contract: '',
        end_date_contract: '',
        product_list: []
      },
      listOrderNo: 2,
      refNo: '',
      selectedRowKeys: [],
      selectAll: false,
      result: 0,
      selectedItems: [],
      counts: 1,
      data2: '',
      endDateContract: '',
      endDateContractShow: ''
      // selectAll: false
    }
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รหัสสินค้า',
          dataIndex: 'sku',
          key: 'sku',
          slots: { title: 'sku' },
          scopedSlots: { customRender: 'sku' },
          width: '20%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          slots: { title: 'customTitleProductdetails' },
          scopedSlots: { customRender: 'productdetails' },
          width: '20%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'revenue_default',
          scopedSlots: { customRender: 'revenue_default' },
          key: 'revenue_default',
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          // dataIndex: 'revenue_price',
          scopedSlots: { customRender: 'revenue_price' },
          align: 'center',
          key: 'products',
          width: '10%'
        },
        {
          title: 'จัดการ',
          align: 'center',
          key: 'actions',
          scopedSlots: { customRender: 'actions' },
          width: '20%'
        }
      ]
      return headers
    }
  },
  components: {
    'a-table': Table,
    PuchaseOrderModalOrdal: () =>
      import(/* webpackPrefetch: true */ '@/components/AdminManage/Order/PuchaseOrderModalOrdal.vue')
  },
  created () {
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (
      localStorage.getItem('oneData') !== null &&
      localStorage.getItem('CompanyData') !== null
    ) {
      this.getDataChange()
      this.SearchProduct()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('clearData', this.clearData)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('clearData')
    })
  },
  methods: {
    deleteProductAll () {
      for (let i = 0; i < this.selectedRowKeys.length; i++) {
        const index = this.products.findIndex(
          product => product.main_sku === this.selectedRowKeys[i]
        )
        this.products.splice(index, 1)
      }
      this.selectedRowKeys = []
      this.clearData()
    },
    deleteProduct (record) {
      const index = this.products.findIndex(
        product => product.main_sku === record.main_sku
      )
      this.products.splice(index, 1)
      this.clearData()
    },
    onSelectAllChange () {
      if (this.selectAll === true) {
        this.products.forEach(element => {
          this.selectedRowKeys.push(element.main_sku)
        })
      } else {
        this.selectedRowKeys = []
      }
    },
    onSelectChange (selection) {
      this.selectedRowKeys = selection
      if (this.selectedRowKeys.length === 0) {
        this.selectAll = false
      } else {
        if (this.products.length !== this.selectedRowKeys.length) {
          this.selectAll = false
        } else {
          this.selectAll = true
        }
      }
      // console.log('onSelectChange------->', this.selectedRowKeys)
    },
    onSelectionChange (selectedItems) {
      // Perform actions based on the selected row keys or items
      // console.log('Selected row keys:', this.selectedRowKeys)
      // console.log('Selected items:', selectedItems)
    },
    closeModalContractEndDate () {
      this.endDateContract = ''
      this.endDateContractShow = ''
      this.modalContractEndDate = false
    },
    closeModalContractStartDate () {
      this.modalContractStartDate = false
    },
    updatetotal () {
      // console.log(this.total_price_no_vat)
      for (var i = 0; i < this.products.length; i++) {
        // console.log(this.total_price_no_vat)
        this.total_price_no_vat = this.total_price_no_vat + (parseFloat(this.products[i].revenue_price) * (this.products[i].quantity))
      }
      this.total_vat = this.total_price_no_vat * 0.07
      this.total_price_vat = this.total_price_no_vat * 1.07
      this.total_price_vat_with_gp = this.total_price_no_vat * 1.07 + this.total_shipping
    },
    clearData () {
      this.total_quantity = 0
      this.total_price_no_vat = 0
      this.total_discount = 0
      this.total_price_discount = 0
      this.total_vat = 0
      this.total_price_vat = 0
      this.total_price_vat_with_gp = 0
      this.total_shipping = 0
      this.updatetotal()
    },
    async getDataChange () {
      this.$store.commit('openLoader')
      const companyId = JSON.parse(
        Decode.decode(localStorage.getItem('CompanyData'))
      )
      var dataGetChange = {
        role_user: 'purchaser',
        company_id: companyId.id,
        payment_transaction_number: this.$route.query.orderNumber
      }
      await this.$store.dispatch('actionListOrderSellerDetail', dataGetChange)
      var res = await this.$store.state.ModuleOrder.stateOrderListSellerDetail
      if (res.code === 200) {
        this.$store.commit('closeLoader')
        this.dataResponseDetail = res.data
        this.refNo = this.dataResponseDetail.payment_transaction
        this.products = this.dataResponseDetail.data_list[0].product_list
        // console.log(this.products)
        this.seller_shop_id = this.dataResponseDetail.data_list[0].shop_id
        this.startDate = this.dataResponseDetail.start_date_contract
        this.endDate = this.dataResponseDetail.end_date_contract
        var keepStartDate = this.checkDateToShow(this.dataResponseDetail.start_date_contract)
        // console.log('this.dataResponseDetail.start_date_contract', this.dataResponseDetail.start_date_contract)
        // console.log('keepStartDate', keepStartDate)
        this.FormattedstartDate = this.formatDate(keepStartDate)
        this.FormattedstartDateShow = this.formatDateToShow(keepStartDate)
        this.pay_type = this.dataResponseDetail.pay_type
        this.checkbox = this.dataResponseDetail.contract_service
        this.remark = this.dataResponseDetail.remark
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'เกิดข้อผิดพลาด'
        })
      }
      this.updatetotal()
    },
    async confirm () {
      this.$store.commit('openLoader')
      var data = {
        order_number: this.refNo,
        change_renew_reason: this.remark,
        end_date_contract: this.endDateContract,
        product_list: this.$store.state.ModuleOrder.stateOrderListSellerDetail.data.data_list[0].product_list,
        total_quantity: 0,
        total_price_no_vat: this.total_price_no_vat,
        total_discount: 0,
        total_price_discount: 0,
        total_vat: this.total_vat,
        total_price_vat: this.total_price_vat,
        total_price_vat_with_gp: this.total_price_vat_with_gp,
        total_shipping: 0,
        net_price: 0
      }
      await this.$store.dispatch('actionOrderRenewAndChange', data)
      var res = await this.$store.state.ModuleOrder.stateOrderRenewAndChange
      if (res.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'สำเร็จ',
          showConfirmButton: false,
          timer: 2000
        })
        this.dialog_Confirm = false
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: res.message,
          showConfirmButton: false,
          timer: 2000
        })
        this.dialog_Confirm = false
      }
    },
    OpenPDF () {
      window.open(`${this.dataResponseDetail.pdf_path}`)
    },
    checkRowKey (record) {
      return record.sku
    },
    Cancel () {
      this.$router.push({ path: '/orderCompany' }).catch(() => {})
    },
    toggleAll () {
      this.selectedItems = this.selectAll ? [...this.products] : []
    },
    updateQuantity (item) {
      // Do any additional logic or API calls here if needed
      // console.log(item.quantity)
    },
    async changeQuantity (products, record, key) {
      // console.log(key)
      // console.log(products.quantity)
      if (key === 'ADD') {
        // console.log('ADD')
        this.products[record].revenue_vat =
          this.products[record].quantity * parseFloat(this.products[record].revenue_default)
        this.total_price_no_vat =
          this.total_price_no_vat + this.products[record].revenue_default
        this.total_vat = this.total_price_no_vat * 0.07
        this.total_price_vat = this.total_price_no_vat * 1.07
        this.total_price_vat_with_gp =
          this.total_price_no_vat * 1.07 + this.total_shipping
      } else if (key === 'DELETE') {
        // console.log('DELETE')
        this.products[record].revenue_vat =
          this.products[record].quantity * this.products[record].revenue_default
        this.total_price_no_vat =
          this.total_price_no_vat - this.products[record].revenue_default
        this.total_vat = this.total_price_no_vat * 0.07
        this.total_price_vat = this.total_price_no_vat * 1.07
        this.total_price_vat_with_gp =
          this.total_price_no_vat * 1.07 + this.total_shipping
      } else if (key === 'EDITE') {
        // console.log('EDITE')
        // console.log(' this.products[record].quantity', this.products[record].quantity)
        if (this.products[record].quantity === '') {
          this.products[record].quantity = 1
        }
        var A = this.products[record].quantity * this.products[record].revenue_default
        console.log(A)
        // console.log(this.products[record].revenue_default)
        // console.log('else')
        this.products[record].revenue_vat =
            this.products[record].revenue_default * products.quantity
        // console.log(this.total_price_no_vat)
        this.total_vat = this.total_price_no_vat * 0.07
        this.total_price_vat = this.total_price_no_vat * 1.07
        this.total_price_vat_with_gp =
            this.total_price_no_vat * 1.07 + this.total_shipping
        this.clearData()
      }
      // console.log(this.total_price_no_vat)
    },
    CheckOrderChang () {
      this.dataOrderchang.product_list = this.products
      this.dataOrderchang.total_quantity = this.total_quantity
      this.dataOrderchang.total_price_no_vat = this.total_price_no_vat
      this.dataOrderchang.total_discount = this.total_discount
      this.dataOrderchang.total_price_discount = this.total_price_discount
      this.dataOrderchang.total_vat = this.total_vat
      this.dataOrderchang.total_price_vat_with_gp = this.total_price_vat_with_gp
      this.dataOrderchang.total_shipping = this.total_shipping
      this.check = true
      this.OrderChang()
    },
    async deleteSelected (item) {
      if (this.MobileSize) {
        this.$swal
          .fire({
            icon: 'warning',
            html: '<h3>คุณต้องการลบสินค้าหรือไม่</h3>',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          })
          .then(result => {
            if (result.isConfirmed) {
              this.selectedDelete(item)
            } else if (result.dismiss === this.$swal.DismissReason.cancel) {
            }
          })
          .catch(() => {})
      } else {
        this.$swal
          .fire({
            icon: 'warning',
            title: 'คุณต้องการลบสินค้าหรือไม่',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          })
          .then(result => {
            if (result.isConfirmed) {
              this.selectedDelete(item)
            } else if (result.dismiss === this.$swal.DismissReason.cancel) {
            }
          })
          .catch(() => {})
      }
    },
    async SearchProduct () {
      var barr = ''
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      barr = {
        role_user: 'purchaser',
        company_id: companyId.id,
        seller_shop_id: this.seller_shop_id,
        keyword: '',
        order_by_price: ''
      }
      await this.$store.dispatch('actionSearchProduct', barr)
      const res = await this.$store.state.ModuleOrder.stateSearchProduct
      console.log('SearchProduct', res)
    },
    async openModalPurchaseOrder (index) {
      this.SearchProduct()
      this.$refs.ModalPurchaseOrder.open()
      // this.$store.state.ModuleOrder.stateopenModalPurchaseOrder = await true
      // for (let i = 0; i < this.$store.state.ModuleAdminManage.ListProductOfShop.length; i++) {
      //   this.$store.state.ModuleAdminManage.ListProductOfShop[i].quantity = 1
      // }
      // await this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.splice(index, 1)
      // this.$store.state.ModuleShop.stateIndexAddProduct = await index
    },
    async OrderChang () {
      this.dataOrderchang.order_number = this.orderNumber
      this.dataOrderchang.change_reason = this.res.data.action_reason
      this.dataOrderchang.start_date_contract = this.res.data.start_date_contract
      this.dataOrderchang.end_date_contract = this.res.data.end_date_contract
      if (this.check === true) {
        await this.$store.dispatch('actionOrderChange', this.dataOrderchang)
        const res = await this.$store.state.ModuleOrder.stateOrderChange
        console.log('ผล', res.data)
      }
    },
    getSelectDateStart (val) {
      if (val !== '') {
        this.$refs.dialogContractEndDate.save(val)
        this.dateFormatted = this.formatDate(val)
        this.dateFormattedShow = this.formatDateToShow(val)
        this.dateFormatted2 = ''
        this.dateFormatted2Show = ''
        this.modalContractStartDate = false
      }
    },
    getSelectDateEnd (val) {
      console.log('endDateContract', val)
      if (val !== '') {
        this.$refs.dialogContractEndDate.save(val)
        this.endDateContract = this.formatDate(val)
        this.endDateContractShow = this.formatDateToShow(val)
        this.modalContractEndDate = false
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    formatDateToShowText (date) {
      if (!date || date === '-') return '-'
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    checkDateToShow (date) {
      if (!date) return null
      var yearEndDate = this.endDate.split('-')[0]
      const oldDate = date.split('-')
      var newDate = ` ${yearEndDate}-${oldDate[1]}-${oldDate[2]}`
      if (parseInt(oldDate[2]) <= 15) {
        var d1 = '01'
        var current
        if (new Date(newDate).getMonth() === 11) {
          current = new Date(new Date(newDate).getFullYear() + 1, 0, 1)
        } else {
          current = new Date(new Date(newDate).getFullYear(), new Date(newDate).getMonth() + 1, 1)
        }
        var endyear = current.getFullYear()
        var endMonth = ('0' + (current.getMonth() + 1)).slice(-2)
        return `${endyear}-${endMonth}-${d1}`
      } else {
        var d2 = '01'
        var current2
        if (new Date(newDate).getMonth() + 1 === 11) {
          current2 = new Date(new Date(newDate).getFullYear() + 1, 0, 1)
        } else {
          current2 = new Date(new Date(newDate).getFullYear(), new Date(newDate).getMonth() + 2, 1)
        }
        var endyear2 = current2.getFullYear()
        var endMonth2 = ('0' + (current2.getMonth() + 1)).slice(-2)
        return `${endyear2}-${endMonth2}-${d2}`
      }
    },
    OpenCancelChangeAndRenew () {
      this.dialog_Cancel = false
      this.dialog_Cancel2 = true
    },
    CancelChangeAndRenew () {
      this.dialog_Cancel = false
      this.dialog_Cancel2 = false
      this.$router.push({ path: '/orderCompany' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #d8efe4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27ab9c !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
.check-all .v-input__control {
  height: 0px;
}
.check-all .v-input--selection-controls__input .v-icon {
  color: #d9d9d9;
}
* {
  margin: 0;
  padding: 0;
  outline: 1px solid rgba(9, 255, 0, 0);
}
.v-btn:not(.v-btn--round).v-size--default {
  height: 32px;
  min-width: 32px;
  padding: 0px 0px;
}
.v-btn:not(.v-btn--round).v-size--x-small {
  height: 20px;
  min-width: 20px;
}
.List_of_products {
  color: #333333;
  font-size: 20px;
  /* font-family: Poppins; */
  font-weight: 700;
  line-height: 30px;
}
.renew {
  font-size: 20px;
  /* font-family: Poppins; */
  font-weight: 700;
  line-height: 30px;
}
.subtitle {
  color: #636363;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 400;
  line-height: 24px;
}
.refNoB {
  color: #333333;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 800;
  line-height: 24px;
  margin-left: 5px;
}
.quotation_sample {
  color: #27ab9c;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 400;
  line-height: 24px;
  margin-left: 5px;
}
.cost {
  text-align: right;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 600;
  line-height: 24px;
}
.r {
  margin-bottom: -35px;
  color: #333333;
}
.a {
  font-size: 20px;
  /* font-family: Poppins; */
  font-weight: 700;
  line-height: 30px;
}
</style>
<style scoped>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #d8efe4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27ab9c !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
</style>
<style scoped>
::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.AddNumberProduct {
  height: 30px;
  width: 60px;
  box-shadow: inset 0 1px 3px 0 rgba(232, 232, 232, 0.5);
  background-color: #ffffff;
  text-align: center;
}
</style>
