<template lang="html">
  <div style="background-color: #fff;">
    <FilterUI/>
    <div v-show="numChange === '1'"><Timeline/></div>
    <div v-show="numChange === '0'"><LineMultiUI/></div>
    <TableUI/>
    <DataTableUI/>
    <bestList />
    <!-- <component :is="Frequency" /> -->
   <!--  <FrequencyUI /> -->
    <!-- <TestTable /> -->
    <!-- <v-btn @click="getSumData" >SumData</v-btn> -->
       <!--  <v-sheet>
          <v-slide-group>
            <v-slide-item v-for="(x, name, i) in dataSum" :key="i">
        <v-card width="206px" class="ml-1 mr-5 mb-10 mt-4">
              <v-card-title class="subheading font-weight-bold">
                {{ name }}
              </v-card-title>
              <v-divider></v-divider>
              <v-list class="inner-right" id="style-15">
              <v-list-item v-for="y in x" :key="y">
                  <v-list-item-content>{{y}}</v-list-item-content>
                </v-list-item>
              </v-list>
        </v-card>
        </v-slide-item>
        </v-slide-group>
        </v-sheet> -->
  </div>
</template>

<script>
// import FrequencyUI from '@/components/Dashboard/frequencyTable'
// import dataTest from '@/components/library/dataTest.json'
export default {
  data () {
    return {
      freq: false,
      dataFreq: [],
      dataSum: [],
      headers2: [],
      SETT: [],
      toDay: new Date().toISOString().slice(0, 10),
      Day: `${new Date().toISOString().slice(0, 7)}-01`,
      numChange: '1'
    }
  },
  components: {
    FilterUI: () => import('@/components/Dashboard/Filter'),
    LineMultiUI: () => import('@/components/Dashboard/LineMulti'),
    TableUI: () => import('@/components/Dashboard/Table'),
    DataTableUI: () => import('@/components/Dashboard/DataTable'),
    Timeline: () => import('@/components/Dashboard/chartTimeline'),
    bestList: () => import('@/components/Dashboard/bestList')
    // FrequencyUI
    // FrequencyUI: () => import('@/components/Dashboard/frequencyTable')
    // TestTable: () => import('@/components/Dashboard/TestTable')
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.$EventBus.$emit('changeNav')
    this.init()
    this.$EventBus.$on('FuntionChange', this.FuntionChange)
    this.$EventBus.$on('FuntionChange2', this.FuntionChange2)
  },
  destroyed () {
    this.$EventBus.$off('FuntionChange')
    this.$EventBus.$off('FuntionChange2')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    dataSum (e) {
      // console.log('EdataSum', e)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    // Frequency () {
    //   if (this.$store.state.ModuleShop.stateSetCompo1) {
    //     return ''
    //   } else {
    //     return FrequencyUI
    //   }
    // }
    Sums () {
      if (Object.values(this.dataSum).length !== 0) {
        return this.dataSum
      } else {
        return ''
      }
    }
    // dataSum (e) {
    //   console.log('EdataSum', e)
    // }
  },
  methods: {
    async init () {
      const data = await {
        start_date: '',
        end_date: '',
        search_type: 'all'
      }
      await this.$store.dispatch('actionsDashboard', data)
      // const selectedMonth = 12
      // const selectedYear = 2022
      // // const weekTransitionDays = []
      // const firstDayOfMonth = (new Date(selectedYear, selectedMonth - 1, 1)).getDay()
      // const daysInMonth = (new Date(selectedYear, selectedMonth, 0)).getDate()
      // console.log('firstDayOfMonth:', firstDayOfMonth, 'daysInMonth:', daysInMonth)
    },
    async FuntionChange (num) {
      // console.log('FuntionChange')
      // console.log('Funtion', num.number)
      this.numChange = await num.number
    },
    async FuntionChange2 (num) {
      // console.log('FuntionChange')
      // console.log('Funtion', num.number)
      this.numChange = await num.number
      // this.$EventBus.$emit('ResetTable')
    }
    // async freqs () {
    //   // const data = await {
    //   //   start_date: '2021-01-01',
    //   //   end_date: '2022-04-07'
    //   // }
    //   // await this.$store.dispatch('actionsDashboard', data)
    //   var response = await this.$store.state.ModuleShop.stateDashboard
    //   this.dataFreq = response.data
    //   var gfrequency = {}
    //   var gheader = []
    //   var gbody = []
    //   // var sumbody = await {}
    //   for (const [key, value] of Object.entries(response.data[0])) {
    //     const head = {
    //       text: key,
    //       value: key.toLowerCase()
    //     }
    //     console.log(value)
    //     this.headers2.push(head)
    //     // console.log('headers2', this.headers2)
    //   }
    //   for (const [key, value] of Object.entries(response.data)) {
    //     // this.arrRows[this.curr] = {}
    //     const en = Object.entries(value)
    //     const sr = en.sort((x, y) => y[1] - x[1])
    //     for (const c in sr) {
    //       gheader[c] = sr[c][0]
    //       gbody[c] = sr[c][1]
    //     }
    //     console.log('key', key)

    //     for (var i = 0; i < gbody.length; i++) {
    //       if (!(gheader[i] in gfrequency)) {
    //         gfrequency[gheader[i]] = {}
    //       }
    //       if (!(gbody[i] in gfrequency[gheader[i]])) {
    //         gfrequency[gheader[i]][gbody[i]] = 0
    //       }
    //       gfrequency[gheader[i]][gbody[i]] += 1
    //       this.sd = gfrequency
    //       // console.log('gfrequency', gfrequency)
    //     }
    //   }
    //   var output = []
    //   for (const [a, b] of Object.entries(this.sd)) {
    //     for (const [x, y] of Object.entries(b)) {
    //       // console.log('sum :', `${x} (${y})`, a)
    //       const txt = `${x} (${y})`
    //       if (!(a in output)) {
    //         output[a] = []
    //       }
    //       output[a].push(txt)
    //     }
    //     this.dataSum = Object.assign({}, output)
    //   }
    // }
  }
}
</script>

<style lang="css" scoped>
.inner-right {
    height: 300px;
    max-height: 300px;
    overflow-y: scroll;
}
#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
