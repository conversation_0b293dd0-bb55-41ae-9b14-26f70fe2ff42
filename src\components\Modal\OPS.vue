<template>
  <div class="text-center">
    <v-dialog v-model="dialog" :width="MobileSize ? '100%' : IpadSize ? '100%' : '60%'" persistent>
      <v-card width="100%" height="100%" max-width="100%" style="border-radius: 12px;">
        <v-row class="py-8" dense no-gutters v-if="!MobileSize">
          <v-img src="@/assets/inetlogo.png" contain height="58" width="121" position="right" class="mr-4"></v-img>
          <v-img src="@/assets/ThaiPayment.jpg" contain height="58" width="200" position="left" class="ml-4"></v-img>
        </v-row>
        <v-row class="py-8" dense no-gutters v-else>
          <v-img src="@/assets/inetlogo.png" contain height="40" width="83" position="right" class="mr-4"></v-img>
          <v-img src="@/assets/ThaiPayment.jpg" contain height="34" width="115" position="left" class="ml-4"></v-img>
        </v-row>
        <v-card-title>
          <v-row justify="center" align-content="center" v-if="!MobileSize">
            <h2 style="font-size: 28px; font-weight: 700; color: #27AB9C;">OPS -Application Document</h2>
          </v-row>
          <v-row justify="center" align-content="center" v-else>
            <h2 style="font-weight: 600; font-size: 14px; line-height: 22px; color: #27AB9C;">OPS -Application Document</h2>
          </v-row>
        </v-card-title>
        <v-card-text :class="MobileSize ? 'px-0' : ''">
          <v-col cols="12" md="10" sm="12" v-if="!MobileSize">
            <p style="font-weight: 600; font-size: 18px; color: #333333;"><img src="@/assets/ImageINET-Marketplace/ICONRegister/Groupสำเนา.png" contain height="24" width="22" class="mr-2"/> เอกสารประกอบการใช้งานระบบ OPS</p>
            <p class="font_text">1. หนังสือรับรองบริษัท จากกรมพัฒนาธุรกิจการค้า (DBD) อายุการรับรองไม่เกิน 6 เดือน จำนวน 1 ฉบับ</p>
            <p class="font_text">2. แบบ บอจ. 5 สำเนาบัญชีรายชื่อจำนวนผู้ถือหุ้น จำนวน 1 ฉบับ</p>
            <p class="font_text">3. สำเนาบัตรประชาชนผู้ถือหุ้นตั้งแต่ 25% ขึ้นไป (สำหรับชาวต่างชาติ ใช้พาสปอร์ต)</p>
            <p class="font_text">4. แบบ บอจ. 3  รายการจดทะเบียนจัดตั้ง จำนวน 1 ฉบับ</p>
            <p class="font_text">5. ใบทะเบียนภาษีมูลค่าเพิ่ม ภ.พ. 20 (ถ้ามี) จำนวน 1 ฉบับ</p>
            <p class="font_text">6. สำเนาบัตรประชาชนคณะกรรมการผู้มีอำนาจลงนาม จำนวน 1 ฉบับ</p>
            <p style="margin-bottom: 0px;" class="font_text">7. สำหรับชาวต่างชาติใช้พาสปอร์ต ใบอนุญาติทำงานในไทย และหลักฐานยืนยันที่อยู่ จำนวน 1 ฉบับ</p>
            <p style="margin-left: 16px;" class="font_text">- ทะเบียนบ้าน ท.ร. 13 (เล่มสีเหลือง) หรือ ใบแจ้งยอดบัตรเครดิต หรือใบแจ้งอดสาธารณูปโภค ซึ่งระบุชื่อ และที่อยู่ปัจจุบัน</p>
            <p class="font_text">8. สำเนาบัตรประชาชนผู้รับมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</p>
            <p class="font_text">9. หนังสือมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</p>
            <p style="margin-bottom: 0px;" class="font_text">10. สำเนาหน้าแรกของสมุดบัญชีธนาคารที่แจ้งเพื่อรับเงินจาก INET จำนวน 1 ฉบับ</p>
            <p class="font_text">(โดยบัญชีจะต้องเป็นของธนาคารแห่งประเทศไทย และชื่อบัญชีจะต้องตรงกับชื่อที่ระบุบนหนังสือรับรองบริษัทตามสัญญาฉบับนี้)</p>
          </v-col>
          <v-col cols="12" v-else>
            <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;"><img src="@/assets/ImageINET-Marketplace/ICONRegister/Groupสำเนา.png" contain height="24" width="22" class="mr-2"/> เอกสารประกอบการใช้งานระบบ OPS</p>
            <p class="font_text_mobile">1. หนังสือรับรองบริษัท จากกรมพัฒนาธุรกิจการค้า (DBD) อายุการรับรองไม่เกิน 6 เดือน จำนวน 1 ฉบับ</p>
            <p class="font_text_mobile">2. แบบ บอจ. 5 สำเนาบัญชีรายชื่อจำนวนผู้ถือหุ้น จำนวน 1 ฉบับ</p>
            <p class="font_text_mobile">3. สำเนาบัตรประชาชนผู้ถือหุ้นตั้งแต่ 25% ขึ้นไป (สำหรับชาวต่างชาติ ใช้พาสปอร์ต)</p>
            <p class="font_text_mobile">4. แบบ บอจ. 3  รายการจดทะเบียนจัดตั้ง จำนวน 1 ฉบับ</p>
            <p class="font_text_mobile">5. ใบทะเบียนภาษีมูลค่าเพิ่ม ภ.พ. 20 (ถ้ามี) จำนวน 1 ฉบับ</p>
            <p class="font_text_mobile">6. สำเนาบัตรประชาชนคณะกรรมการผู้มีอำนาจลงนาม จำนวน 1 ฉบับ</p>
            <p style="margin-bottom: 0px;" class="font_text_mobile">7. สำหรับชาวต่างชาติใช้พาสปอร์ต ใบอนุญาติทำงานในไทย และหลักฐานยืนยันที่อยู่ จำนวน 1 ฉบับ</p>
            <p style="margin-left: 16px;" class="font_text_mobile">- ทะเบียนบ้าน ท.ร. 13 (เล่มสีเหลือง) หรือ ใบแจ้งยอดบัตรเครดิต หรือใบแจ้งอดสาธารณูปโภค ซึ่งระบุชื่อ และที่อยู่ปัจจุบัน</p>
            <p class="font_text_mobile">8. สำเนาบัตรประชาชนผู้รับมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</p>
            <p class="font_text_mobile">9. หนังสือมอบอำนาจ จำนวน 1 ฉบับ (ถ้ามี)</p>
            <p style="margin-bottom: 0px;" class="font_text_mobile">10. สำเนาหน้าแรกของสมุดบัญชีธนาคารที่แจ้งเพื่อรับเงินจาก INET จำนวน 1 ฉบับ</p>
            <p class="font_text_mobile">(โดยบัญชีจะต้องเป็นของธนาคารแห่งประเทศไทย และชื่อบัญชีจะต้องตรงกับชื่อที่ระบุบนหนังสือรับรองบริษัทตามสัญญาฉบับนี้)</p>
          </v-col>
        </v-card-text>
        <v-card-actions class="mr-8 pb-10" v-if="!MobileSize">
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" dark @click="Change()" class="pl-4 pr-4">ฉันเข้าใจและยอมรับ</v-btn>
        </v-card-actions>
        <v-card-actions v-else class="pb-10 pt-0">
          <v-row dense justify="center">
            <v-btn color="#27AB9C" dark @click="Change()" class="pl-4 pr-4">ฉันเข้าใจและยอมรับ</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
export default {
  data () {
    return {
      dialog: false
    }
  },
  created () {
    this.$EventBus.$on('openOPS', this.Change)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    Change () {
      this.dialog = !this.dialog
    }
  }
}
</script>

<style scoped>
.font_text {
  font-weight: 400;
  font-size: 16px;
  color: #333333;
}
.font_text_mobile {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #333333;
}
</style>
