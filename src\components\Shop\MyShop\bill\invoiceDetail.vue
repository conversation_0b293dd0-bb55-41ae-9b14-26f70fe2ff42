<template>
  <div>
    <v-container>
      <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
        <h2 v-if="!MobileSize" class="ml-4" style="font-size:15px"><B>การวางบิล</B></h2>
      </v-card>
      <v-row dense justify="center">
        <v-col cols="12" md="12" sm="12" xs="12" class="px-0 mx-0">
          <v-card elevation="0" width="100%" height="100%">
            <v-card-text>
              <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
                <v-card-text>
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">รายละเอียดใบแจ้งหนี้</span><br/>
                  <v-divider></v-divider>
                  <v-row class="ml-4 mt-3">
                    <v-col cols="12" md="2"><span>ใบเสนอราคา </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>
                    </v-col>
                    <v-col cols="12" md="2"><span>ใบเสนอราคา </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>                    </v-col>
                  </v-row>
                  <v-row class="ml-4">
                    <v-col cols="12" md="2"><span>เลขที่ใบกำกับภาษี </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>
                    </v-col>
                    <v-col cols="12" md="2"><span>ธนาคารผู้รับ </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>                    </v-col>
                  </v-row>
                  <v-row class="ml-4 mt-3">
                    <v-col cols="12" md="2"><span>ราคารวม </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>
                    </v-col>
                    <v-col cols="12" md="2"><span>เลขที่บัญชีธนาคาร </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>                    </v-col>
                  </v-row>
                  <v-row class="ml-4">
                    <v-col cols="12" md="2"><span>ราคาฐานภาษี </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>
                    </v-col>
                    <v-col cols="12" md="2"><span>รอบบริการ (จาก) </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>                    </v-col>
                  </v-row>
                  <v-row class="ml-4 mt-3">
                    <v-col cols="12" md="2"><span>ภาษีมูลค่าเพิ่ม </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>
                    </v-col>
                    <v-col cols="12" md="2"><span>รอบบริการ (ถึง) </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>                    </v-col>
                  </v-row>
                  <v-row class="ml-4">
                    <v-col cols="12" md="2"><span>อัตราภาษีหัก ณ ที่จ่าย </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>
                    </v-col>
                    <v-col cols="12" md="2"><span>วันที่ใบแจ้งหนี้ </span></v-col>
                    <v-col cols="12" md="4">
                      <span><v-text-field dense placeholder=""  outlined maxLength="15"></v-text-field></span>                    </v-col>
                  </v-row>
                  <v-row class="ml-4 mt-3">
                    <v-col cols="12" md="2"><span>ยอดรวมสุทธิ </span></v-col>
                    <v-col cols="12" md="4">
                      <span>{{total}} THB</span>
                    </v-col>
                  </v-row>
                  <v-row class="ml-4 mt-3">
                    <v-col cols="12" md="2"><span>ภาษีหัก ณ ที่จ่าย</span></v-col>
                    <v-col cols="12" md="4">
                      <span>{{tax}} THB</span>
                    </v-col>
                  </v-row>
                  <v-row class="ml-4 mt-3">
                    <v-col cols="12" md="2"><span>ราคาสุทธิ</span></v-col>
                    <v-col cols="12" md="4">
                      <span>{{net}} THB</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row dense justify="center">
        <v-col cols="12" md="12" sm="12" xs="12" class="px-0 mx-0">
          <v-card elevation="0" width="100%" height="100%">
            <v-card-text>
              <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
                <v-card-text>
                  <v-row dense>
                    <v-col cols="1">
                      <v-avatar size="60" style="background: #F2F2F2; border: 1px solid #E6E6E6; border-radius: 8px;">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="40" height="40"></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="10" class="mt-2">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">ไฟล์หลัก</span><br/>
                      <v-file-input
                        accept=".pdf"
                        label="File input"
                      ></v-file-input>
                      <!-- <v-card
                        elevation="0"
                        style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                        @click="onPickFile()"
                      >
                      <v-card-text>
                        <v-row
                          no-gutters
                          align="center"
                          justify="center"
                          style="cursor: pointer;"
                        >
                          <v-file-input
                            v-model="file"
                            :items="file"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImage()"
                            id="file_input"
                            :clearable="true"
                            multiple
                            style="display:none"
                          >
                          </v-file-input>
                          <v-col cols="12" md="12" class="mb-6">
                            <v-row justify="center" class="pt-10">
                              <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                width="280.34"
                                height="154.87"
                                contain
                              ></v-img>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12" class="mt-6">
                            <v-row justify="center" align="center">
                              <v-col cols="12" md="12" style="text-align: center;">
                                <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br/>
                                <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br/>
                                <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ไฟล์นามสกุล .JPEG,PNG เพิ่มได้สูงสุด 1 รูปภาพ)</span><br/>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      </v-card> -->
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-card-text>
            <v-card-actions>
              <v-row justify="end" dense class="mr-2">
                <v-btn text outlined class="pr-8 pl-8 mr-4" color="primary" style="border: 1px solid #27AB9C;">ยกเลิก</v-btn>
                <v-btn color="primary" class="pr-8 pl-8" @click="confirm()" v-if="statusPage !== 'Edit'">บันทึก</v-btn>
                <v-btn color="primary" class="pr-8 pl-8" @click="confirmEdit()" v-else>บันทึก</v-btn>
              </v-row>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
export default {
  data () {
    return {
      total: '0.00',
      tax: '0.00',
      net: '0.00',
      file: []
    }
  },
  methods: {
    UploadImage () {
      // console.log(this.file, 'this.file')
      // if (this.Detail.product_image.length < 1) {
      //   for (let i = 0; i < this.file.length; i++) {
      //     const element = this.file[i]
      //     const imageSize = element.size / 1024 / 1024
      //     if (imageSize < 2) {
      //       const reader = new FileReader()
      //       reader.readAsDataURL(element)
      //       reader.onload = () => {
      //         var resultReader = reader.result
      //         var url = URL.createObjectURL(element)
      //         if (this.$route.query.Status !== 'Edit') {
      //           this.Detail.product_image.push({
      //             image_data: resultReader.split(',')[1],
      //             path: url,
      //             name: this.file[i].name
      //           })
      //           this.Detail.company_logo = resultReader.split(',')[1]
      //         } else {
      //           this.Detail.product_image.push({
      //             image_data: resultReader.split(',')[1],
      //             media_path: url,
      //             name: this.file[i].name
      //           })
      //           this.Detail.company_logo = resultReader.split(',')[1]
      //         }
      //       }
      //     } else {
      //       this.$swal.fire({
      //         icon: 'warning',
      //         text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
      //         showConfirmButton: false,
      //         timer: 1500
      //       })
      //     }
      //   }
      // } else {
      //   this.$swal.fire({
      //     icon: 'warning',
      //     text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป',
      //     showConfirmButton: false,
      //     timer: 1500
      //   })
      // }
      // console.log('me', this.Detail.product_image)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    }
  }

}
</script>
