<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> คูปองทั้งหมดของ <span style="color: #27AB9C;" class="pl-3">{{ fullname }}</span></v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> คูปองทั้งหมดของ <span style="color: #27AB9C;" class="pl-3">{{ fullname }}</span></v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อคูปอง" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-2 pt-2' : 'pl-2 pr-2 pt-3'" :style="MobileSize ? '' : 'display: flex; justify-content: flex-end;'">
            <v-btn :block="MobileSize" @click="modalAddCoupon = true" rounded color="#27AB9C" ><span style="color: #FFFFFF;"><v-icon class="pr-2">mdi-ticket-percent-outline</v-icon>เพิ่มคูปอง</span></v-btn>
          </v-col>
          <v-col cols="12" md="12">
            <v-row class="my-3">
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อคูปองของผู้ใช้งานทั้งหมด {{ showCountCoupon }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายชื่อคูปองขอผู้ใช้งานทั้งหมด {{ showCountCoupon }} รายการ</span>
              </v-col>
            </v-row>
            <div>
              <v-row dense>
                <v-col cols="12">
                  <v-data-table
                   :headers="headers"
                   :items="items"
                   :search="search"
                   style="width:100%; white-space: nowrap;"
                   height="100%"
                   no-results-text="ไม่พบรายชื่อคูปอง"
                   no-data-text="ไม่มีรายชื่อคูปองของผู้ใช้งาน"
                   @pagination="countRequest"
                   :items-per-page="10"
                   :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  >
                  <template v-slot:[`item.coupon_code`] = "{ item }">
                    <span v-if="item.coupon_code !== null">{{ item.coupon_code }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.coupon_name`]="{ item }">
                    <span v-if="item.coupon_name">{{ item.coupon_name }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.use_startdate`] = "{ item }">
                    <span v-if="item.use_startdate !== null">{{ new Date(item.use_startdate).toLocaleString('th-TH', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      timeZone: 'Asia/Bangkok'
                    }) }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.use_enddate`] = "{ item }">
                    <span v-if="item.use_enddate !== null">{{ new Date(item.use_enddate).toLocaleString('th-TH', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      timeZone: 'Asia/Bangkok'
                    }) }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.user_use`]="{ item }">
                    <div class="statusPosition">
                      <v-icon v-if="item.user_use === 'N'" color="#1AB759" x-small>mdi-brightness-1</v-icon>
                      <v-icon v-else-if="item.user_use === 'Y'" color="#FF0000" x-small>mdi-brightness-1</v-icon>
                      <span class="pl-2" :style="item.user_use === 'N' ? 'color: #1AB759' : 'color: #FF0000'">{{ item.user_use === 'N' ? 'ยังไม่ใช้งาน' : 'ใช้งานแล้ว' }}</span>
                    </div>
                  </template>
                  <template v-slot:[`item.seller_shop_id`]="{ item }">
                    <v-chip small :color="item.seller_shop_id !== -1 ? '#d7e2f6' : '#def9d1'" :text-color="item.seller_shop_id !== -1 ? '#1c3d77' : '#52C41A'" style="font-weight: bold;">
                      <v-tooltip bottom v-if="item.seller_shop_id !== -1">
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on">
                            ร้านค้า ({{ truncatedShopName(item.shop_name) }})
                          </span>
                        </template>
                        <span>{{ item.shop_name }}</span>
                      </v-tooltip>
                      <span v-else>
                        ระบบ
                      </span>
                    </v-chip>
                  </template>
                </v-data-table>
              </v-col>
              </v-row>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-dialog persistent content-class="elevation-0" v-model="modalAddCoupon" :width="MobileSize ? '100%' : IpadSize ? '80%' : IpadProSize ? '60%' : '40%'">
      <v-card width="100%" style="border-radius: 1.5vw;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มคูปองให้ผู้ใช้งาน</b></span>
            </v-col>
            <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>

        <v-card-text class="pt-4" style="max-height: 500px; overflow-y: auto;">
          <div>
            <span>เพิ่มคูปองจากรหัสคูปอง : </span>
            <v-col cols="12">
              <div style="display: flex; gap: .5vw; margin-top: .5vw;">
                <v-text-field @keyup.enter="searchCoupon()" style="border-radius: 8px;" class="input_text namedoc_input" v-model="couponId" placeholder="รหัสคูปอง" outlined dense></v-text-field>
                <v-btn :disabled="couponId === '' || couponId === null" @click="searchCoupon()" color="primary" >ค้นหา</v-btn>
              </div>
            </v-col>
          </div>
          <div v-if="showForm">
            <v-col v-if="!MobileSize" cols="12" style="margin-top: -2.5vw;">
              <v-card class="d-flex mb-3 pa-2 align-center formatBox" v-for="(item, index) in listAllCoupons" :key="index">
                <v-col cols="1">
                  <v-checkbox :disabled="item.is_collected === 'Y'" @change="collectCouponId(item)" v-model="item.selected" :ripple="false"></v-checkbox>
                </v-col>
                <v-col cols="4">
                  <v-row dense no-gutters>
                    <v-col cols="12" class="d-flex justify-center">
                      <span>{{ item.coupon_code }}</span>
                    </v-col>
                    <v-col cols="12" class="d-flex justify-center align-center">
                      <v-chip x-small :color="item.seller_shop_id !== -1 ? '#d7e2f6' : '#def9d1'" :text-color="item.seller_shop_id !== -1 ? '#1c3d77' : '#52C41A'" style="font-weight: bold;">{{ item.seller_shop_id !== -1 ? `คูปองร้านค้า (${item.shop_name})` : 'คูปองระบบ' }}</v-chip>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="3">
                  <v-icon class="pr-1" v-if="item.is_collected === 'N'" color="#1AB759" x-small>mdi-brightness-1</v-icon>
                  <v-icon class="pr-1" v-else-if="item.is_collected === 'Y'" color="#FF0000" x-small>mdi-brightness-1</v-icon>
                  <span :style="item.is_collected === 'N' ? 'color: #1AB759' : 'color: #FF0000'">{{ item.is_collected === 'Y' ? 'มีคูปองแล้ว' : 'ยังไม่มีคูปอง' }}</span>
                </v-col>
                <v-col cols="4">
                  <span style="font-size: small; font-weight: bold;">เริ่มต้น:<span class="pl-2" style="font-weight: lighter;">{{ item.use_startdate !== null ? new Date(item.use_startdate).toLocaleString('th-TH', {year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'Asia/Bangkok' }) : '-' }}</span></span><br/>
                  <span style="font-size: small; font-weight: bold;">สิ้นสุด:<span class="pl-2" style="font-weight: lighter;"> {{ item.use_enddate !== null ? new Date(item.use_enddate).toLocaleString('th-TH', {year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'Asia/Bangkok' }) : '-' }}</span></span>
                </v-col>
              </v-card>
            </v-col>
            <v-col v-if="MobileSize" cols="12" style="margin-top: -9vw;">
              <v-card class="mb-3 pa-2 formatBox" v-for="(item, index) in listAllCoupons" :key="index">
                <v-col cols="12">
                  <v-row dense class="justify-end">
                    <v-icon class="pr-1" v-if="item.is_collected === 'N'" color="#1AB759" x-small>mdi-brightness-1</v-icon>
                    <v-icon class="pr-1" v-else-if="item.is_collected === 'Y'" color="#FF0000" x-small>mdi-brightness-1</v-icon>
                    <span :style="item.is_collected === 'N' ? 'color: #1AB759' : 'color: #FF0000'">{{ item.is_collected === 'Y' ? 'มีคูปองแล้ว' : 'ยังไม่มีคูปอง' }}</span>
                  </v-row>
                  <v-row dense class="mt-2">
                    <v-col cols="3"><v-checkbox :disabled="item.is_collected === 'Y'" @change="collectCouponId(item)" hide-details v-model="item.selected" :ripple="false"></v-checkbox></v-col>
                    <v-col cols="9">
                      <v-row dense>
                       <span>{{ item.coupon_code }}</span>
                      </v-row>
                      <v-row dense class="mt-2">
                        <v-chip x-small :color="item.seller_shop_id !== -1 ? '#d7e2f6' : '#def9d1'" :text-color="item.seller_shop_id !== -1 ? '#1c3d77' : '#52C41A'" style="font-weight: bold;">{{ item.seller_shop_id !== -1 ? `คูปองร้านค้า (${item.shop_name})` : 'คูปองระบบ' }}</v-chip>
                      </v-row>
                      <v-row dense class="mt-3">
                        <span style="font-size: small; font-weight: bold;">เริ่มต้น:<span class="pl-2" style="font-weight: lighter;">{{ item.use_startdate !== null ? new Date(item.use_startdate).toLocaleString('th-TH', {year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'Asia/Bangkok' }) : '-' }}</span></span>
                      </v-row>
                      <v-row dense class="mt-2">
                        <span style="font-size: small; font-weight: bold;">สิ้นสุด:<span class="pl-2" style="font-weight: lighter;"> {{ item.use_enddate !== null ? new Date(item.use_enddate).toLocaleString('th-TH', {year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'Asia/Bangkok' }) : '-' }}</span></span>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-card>
            </v-col>
          </div>
        </v-card-text>
        <v-card-title v-if="showForm" style="background-color: #F5FCFB;">
          <v-col cols="12" class="d-flex justify-center py-1">
            <v-btn :disabled="selectedAdd.length === 0" @click="addCoupon()" rounded color="#27AB9C">
              <span style="color: #FFFFFF;">
                <v-icon>mdi-plus</v-icon>เพิ่มคูปอง
              </span>
            </v-btn>
          </v-col>
        </v-card-title>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      items: [],
      headers: [
        { text: 'รหัสคูปอง', value: 'coupon_code', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: 'ชื่อคูปอง', value: 'coupon_name', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: 'คูปอง', value: 'seller_shop_id', width: '80', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: 'สถานะ', value: 'user_use', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: 'วันที่เริ่ม', value: 'use_startdate', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: 'วันที่สิ้นสุด', value: 'use_enddate', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' }
      ],
      modalAddCoupon: false,
      couponId: '',
      showForm: false,
      listAllCoupons: [],
      search: '',
      showCountCoupon: 0,
      userId: null,
      fullname: '',
      selectedAdd: []
    }
  },
  created () {
    this.fullname = this.$route.query.name
    this.userId = Number(this.$route.query.userId)
    this.GetlistAllCoupons()
  },
  computed: {
    truncatedShopName () {
      return (name) => {
        if (!name) return ''
        return name.length > 15 ? name.substring(0, 12) + '...' : name
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      // console.log('valdetail', val)
      if (val === true) {
        this.$router.push({ path: '/adminUserWebMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminUserWeb' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/adminUserWeb' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminUserWebMobile' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountCoupon = pagination.itemsLength
    },
    cancel () {
      this.modalAddCoupon = false
      this.showForm = false
      this.couponId = ''
      this.selectedAdd = []
    },
    async GetlistAllCoupons () {
      this.$store.commit('openLoader')
      var data = {
        user_id: this.userId
      }
      await this.$store.dispatch('actionsGetListCouponUser', data)
      var response = await this.$store.state.ModuleAdminManage.stateGetListCouponUser
      if (response.result === 'Success') {
        this.items = response.data.coupon.active
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async searchCoupon () {
      this.$store.commit('openLoader')
      this.selectedAdd = []
      var data = {
        user_id: this.userId,
        coupon_code: this.couponId
      }
      await this.$store.dispatch('actionsSearchCouponAdmin', data)
      var response = await this.$store.state.ModuleAdminManage.stateSearchCoupon
      if (response.code === 200) {
        this.showForm = true
        this.listAllCoupons = response.data.coupon
        this.listAllCoupons.forEach(coupon => {
          coupon.selected = false
        })
        this.$store.commit('closeLoader')
      } else if (response.code === 400) {
        this.$store.commit('closeLoader')
        this.showForm = false
        this.selectedAdd = []
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>ไม่พบคูปอง</h3>'
        })
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    collectCouponId (item) {
      const index = this.selectedAdd.indexOf(item.coupon_id)
      if (item.selected) {
        if (index === -1) this.selectedAdd.push(item.coupon_id)
      } else {
        if (index !== -1) this.selectedAdd.splice(index, 1)
      }
    },
    async addCoupon () {
      // console.log('addCoupon', this.selectedAdd)
      this.$store.commit('openLoader')
      var data = {
        user_id: this.userId,
        coupon_id: this.selectedAdd
      }
      await this.$store.dispatch('actionsAddCouponUserAdmin', data)
      var response = await this.$store.state.ModuleAdminManage.stateAddCouponUserAdmin
      if (response.code === 200) {
        this.selectedAdd = []
        this.modalAddCoupon = false
        this.showForm = false
        this.couponId = ''
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          html: '<h3>เพิ่มคูปองสำเร็จ</h3>'
        })
        this.GetlistAllCoupons()
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>เพิ่มคูปองไม่สำเร็จ</h3>'
        })
      }
    }
  }
}
</script>

<style scoped>
  .formatBox {
    border: 1px solid #cbeae0;
    border-radius: 20px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px !important;
  }
</style>
