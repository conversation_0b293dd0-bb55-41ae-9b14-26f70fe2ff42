import AxiosManageShop from './axios_manage_api'

const ModuleManageShop = {
  state: {
    Category: [],
    Supplier: [],
    Brand: [],
    ProductBySellerID: [],
    EditProduct: '',
    GetUserAddress: '',
    CreateAddressUser: '',
    CheckAddressUserLocal: '',
    UpdateSellerShopDetail: '',
    CreateInventory: '',
    CreateProductShop: '',
    GetInventory: '',
    EditProductShop: '',
    CreateSupplier: '',
    CreateBrand: '',
    listTierAndDoc: [],
    newCategories: [],
    GetStatusShop: [],
    GetListIship: [],
    GetCourierType: [],
    CancelOrderIship: [],
    UpdateOrderIship: [],
    GetStatusIship: [],
    GetListCourierIship: [],
    PrintListCourierIship: [],
    CreateRequestCourierIship: [],
    PrintLabelPDFIship: [],
    PrintLabelShippingPDFIship: [],
    GetCourierList: [],
    GetCourierListII: [],
    UpdateCourierList: [],
    UpdateStatusShipping: [],
    stateListCreateShipping: [],
    stateCreateOrderIshipSelect: [],
    stateUpdateRemarkShop: [],
    stateUpdateRemarkAdmin: [],
    stateRequestSellerShop: [],
    stateRequestSellerShopDetail: [],
    CreateRequestCourierIshipV2: [],
    stateGetListCourierIshipV2: [],
    stateGetListIshipAdmin: [],
    stateEditShippingAddress: [],
    stateGetB2BDeliveryList: [],
    GetListIshipIII: [],
    GetCourierTypeII: [],
    GetShipmentsImages: []
  },
  mutations: {
    SetCategory (state, data) {
      state.Category = data
    },
    SetSupplier (state, data) {
      state.Supplier = data
    },
    SetBrand (state, data) {
      state.Brand = data
    },
    SetProductBySellerID (state, data) {
      state.ProductBySellerID = data
    },
    SetEditProduct (state, data) {
      state.EditProduct = data
    },
    SetGetUserAddress (state, data) {
      state.GetUserAddress = data
    },
    SetCreateAddressUser (state, data) {
      state.CreateAddressUser = data
    },
    SetCheckAddressUserLocal (state, data) {
      state.CheckAddressUserLocal = data
    },
    SetUpdateSellerShopDetail (state, data) {
      state.UpdateSellerShopDetail = data
    },
    SetCreateInventory (state, data) {
      state.CreateInventory = data
    },
    SetCreateProductShop (state, data) {
      state.CreateProductShop = data
    },
    SetGetInventory (state, data) {
      state.GetInventory = data
    },
    SetEditProductShop (state, data) {
      state.EditProductShop = data
    },
    SetCreateSupplier (state, data) {
      state.CreateSupplier = data
    },
    SetCreateBrand (state, data) {
      state.CreateBrand = data
    },
    SetlistTierAndDoc (state, data) {
      state.listTierAndDoc = data
    },
    MutationCreateCategories (state, data) {
      state.newCategories = data
    },
    MutationsGetStatusShop (state, data) {
      state.GetStatusShop = data
    },
    MutationsGetListIship (state, data) {
      state.GetListIship = data
    },
    MutationsGetCourierType (state, data) {
      state.GetCourierType = data
    },
    MutationsCancelOrderIship (state, data) {
      state.CancelOrderIship = data
    },
    MutationsUpdateOrderIship (state, data) {
      state.UpdateOrderIship = data
    },
    MutationsGetStatusIship (state, data) {
      state.GetStatusIship = data
    },
    MutationsGetListCourierIship (state, data) {
      state.GetListCourierIship = data
    },
    MutationsPrintListCourierIship (state, data) {
      state.PrintListCourierIship = data
    },
    MutationsCreateRequestCourierIship (state, data) {
      state.CreateRequestCourierIship = data
    },
    MutationsPrintLabelPDFIship (state, data) {
      state.PrintLabelPDFIship = data
    },
    MutationsPrintLabelShippingPDFIship (state, data) {
      state.PrintLabelShippingPDFIship = data
    },
    MutationsGetCourierList (state, data) {
      state.GetCourierList = data
    },
    MutationsGetCourierListII (state, data) {
      state.GetCourierListII = data
    },
    MutationsUpdateCourierList (state, data) {
      state.UpdateCourierList = data
    },
    MutationsUpdateStatusShipping (state, data) {
      state.UpdateStatusShipping = data
    },
    MutationsListCreateShipping (state, data) {
      state.stateListCreateShipping = data
    },
    MutationsCreateOrderIshipSelect (state, data) {
      state.stateCreateOrderIshipSelect = data
    },
    MutationsUpdateRemarkShop (state, data) {
      state.stateUpdateRemarkShop = data
    },
    MutationsUpdateRemarkAdmin (state, data) {
      state.stateUpdateRemarkAdmin = data
    },
    MutationsRequestSellerShop (state, data) {
      state.stateRequestSellerShop = data
    },
    MutationsRequestSellerShopDetail (state, data) {
      state.stateRequestSellerShopDetail = data
    },
    MutationsCreateRequestCourierIshipV2 (state, data) {
      state.CreateRequestCourierIshipV2 = data
    },
    MutationsGetListCourierIshipV2 (state, data) {
      state.stateGetListCourierIshipV2 = data
    },
    MutationsGetListIshipAdmin (state, data) {
      state.stateGetListIshipAdmin = data
    },
    MutationsEditShippingAddress (state, data) {
      state.stateEditShippingAddress = data
    },
    MutationsGetB2BDeliveryList (state, data) {
      state.stateGetB2BDeliveryList = data
    },
    MutationsGetListIshipIII (state, data) {
      state.GetListIshipIII = data
    },
    MutationsGetCourierTypeII (state, data) {
      state.GetCourierTypeII = data
    },
    MutationsGetShipmentsImages (state, data) {
      state.GetShipmentsImages = data
    }
  },
  actions: {
    async GetCagegory (context) {
      var res = await AxiosManageShop.GetCagegory()
      await context.commit('SetCategory', res)
    },
    async GetSupplier (context) {
      var res = await AxiosManageShop.GetSupplier()
      await context.commit('SetSupplier', res)
    },
    async GetBrand (context) {
      var res = await AxiosManageShop.GetBrand()
      await context.commit('SetBrand', res)
    },
    async GetProductBySellerID (context, data) {
      var res = await AxiosManageShop.GetProductBySellerID(data)
      await context.commit('SetProductBySellerID', res)
    },
    async CreateBrand (context, data) {
      var res = await AxiosManageShop.CreateBrand(data)
      await context.commit('SetCreateBrand', res)
    },
    async CreateSupplier (context, data) {
      var res = await AxiosManageShop.CreateSupplier(data)
      await context.commit('SetCreateSupplier', res)
    },
    async GetDefaultCagegory (context) {
      var res = await AxiosManageShop.GetDefaultCagegory()
      await context.commit('SetCategory', res)
    },
    async GetUserAddress (context, data) {
      var res = await AxiosManageShop.GetUserAddress(data)
      await context.commit('SetGetUserAddress', res)
    },
    async CreateAddressUser (context, data) {
      var res = await AxiosManageShop.CreateAddressUser(data)
      await context.commit('SetCreateAddressUser', res)
    },
    async CheckAddressUserLocal (context, data) {
      var res = await AxiosManageShop.CheckAddressUserLocal(data)
      await context.commit('SetCheckAddressUserLocal', res)
    },
    async UpdateAddressCart (context, data) {
      await AxiosManageShop.UpdateAddressCart(data)
      // await context.commit('SetCreateAddressUser', res)
    },
    async UpdateSellerShopDetail (context, data) {
      var res = await AxiosManageShop.UpdateSellerShopDetail(data.dataShop)
      await context.commit('SetUpdateSellerShopDetail', res)
    },
    async CreateInventory (context, data) {
      var res = await AxiosManageShop.CreateInventory(data)
      await context.commit('SetCreateInventory', res)
    },
    async CreateProductShop (context, data) {
      var res = await AxiosManageShop.CreateProductShop(data)
      await context.commit('SetCreateProductShop', res)
    },
    async EditProductShop (context, data) {
      var res = await AxiosManageShop.EditProductShop(data)
      await context.commit('SetEditProductShop', res)
    },
    async GetInventory (context, data) {
      var res = await AxiosManageShop.GetInventory(data)
      await context.commit('SetGetInventory', res)
    },
    async GetListTierAndDoc (context, data) {
      var res = await AxiosManageShop.ListTierAndDoc(data)
      await context.commit('SetlistTierAndDoc', res)
    },
    async ActionCreateCategories (context, data) {
      var res = await AxiosManageShop.CreateCagegory(data)
      await context.commit('MutationCreateCategories', res)
    },
    async ActionsGetStatusShop (context, data) {
      var res = await AxiosManageShop.GetStatusShop(data)
      await context.commit('MutationsGetStatusShop', res)
    },
    async ActionsGetListIship (context, data) {
      var res = await AxiosManageShop.GetListIship(data)
      await context.commit('MutationsGetListIship', res)
    },
    async ActionsGetCourierType (context, data) {
      var res = await AxiosManageShop.GetCourierType(data)
      await context.commit('MutationsGetCourierType', res)
    },
    async ActionsCancelOrderIship (context, data) {
      var res = await AxiosManageShop.CancelOrderIship(data)
      await context.commit('MutationsCancelOrderIship', res)
    },
    async ActionsUpdateOrderIship (context, data) {
      var res = await AxiosManageShop.UpdateOrderIship(data)
      await context.commit('MutationsUpdateOrderIship', res)
    },
    async ActionsGetStatusIship (context, data) {
      var res = await AxiosManageShop.GetStatusIship(data)
      await context.commit('MutationsGetStatusIship', res)
    },
    async ActionsGetListCourierIship (context, data) {
      var res = await AxiosManageShop.GetListCourierIship(data)
      await context.commit('MutationsGetListCourierIship', res)
    },
    async ActionsPrintListCourierIship (context, data) {
      var res = await AxiosManageShop.PrintListCourierIship(data)
      await context.commit('MutationsPrintListCourierIship', res)
    },
    async ActionsCreateRequestCourierIship (context, data) {
      var res = await AxiosManageShop.CreateRequestCourierIship(data)
      await context.commit('MutationsCreateRequestCourierIship', res)
    },
    async ActionsPrintLabelPDFIship (context, data) {
      var res = await AxiosManageShop.PrintLabelPDFIship(data)
      await context.commit('MutationsPrintLabelPDFIship', res)
    },
    async ActionsPrintLabelShippingPDFIship (context, data) {
      var res = await AxiosManageShop.PrintLabelShippingPDFIship(data)
      await context.commit('MutationsPrintLabelShippingPDFIship', res)
    },
    async ActionsGetCourierList (context, data) {
      var res = await AxiosManageShop.GetCourierList(data)
      await context.commit('MutationsGetCourierList', res)
    },
    async ActionsGetCourierListII (context, data) {
      var res = await AxiosManageShop.GetCourierListII(data)
      await context.commit('MutationsGetCourierListII', res)
    },
    async ActionsUpdateCourierList (context, data) {
      var res = await AxiosManageShop.UpdateCourierList(data)
      await context.commit('MutationsUpdateCourierList', res)
    },
    async ActionsUpdateStatusShipping (context, data) {
      var res = await AxiosManageShop.UpdateStatusShipping(data)
      await context.commit('MutationsUpdateStatusShipping', res)
    },
    async ActionsListCreateShipping (context, data) {
      var res = await AxiosManageShop.ListCreateShipping(data)
      await context.commit('MutationsListCreateShipping', res)
    },
    async ActionsCreateOrderIshipSelect (context, data) {
      var res = await AxiosManageShop.CreateOrderIshipSelect(data)
      await context.commit('MutationsCreateOrderIshipSelect', res)
    },
    async ActionsUpdateRemarkShop (context, data) {
      var res = await AxiosManageShop.UpdateRemarkShop(data)
      await context.commit('MutationsUpdateRemarkShop', res)
    },
    async ActionsUpdateRemarkAdmin (context, data) {
      var res = await AxiosManageShop.UpdateRemarkAdmin(data)
      await context.commit('MutationsUpdateRemarkAdmin', res)
    },
    async ActionsRequestSellerShop (context, data) {
      var res = await AxiosManageShop.RequestSellerShop(data)
      await context.commit('MutationsRequestSellerShop', res)
    },
    async ActionsRequestSellerShopDetail (context, data) {
      var res = await AxiosManageShop.RequestSellerShopDetail(data)
      await context.commit('MutationsRequestSellerShopDetail', res)
    },
    async ActionsCreateRequestCourierIshipV2 (context, data) {
      var res = await AxiosManageShop.CourierIshipV2(data)
      await context.commit('MutationsCreateRequestCourierIshipV2', res)
    },
    async ActionsGetListCourierIshipV2 (context, data) {
      var res = await AxiosManageShop.GetListCourierIshipV2(data)
      await context.commit('MutationsGetListCourierIshipV2', res)
    },
    async ActionsGetListIshipAdmin (context, data) {
      var res = await AxiosManageShop.GetListIshipAdmin(data)
      await context.commit('MutationsGetListIshipAdmin', res)
    },
    async ActionsEditShippingAddress (context, data) {
      var res = await AxiosManageShop.EditShippingAddress(data)
      await context.commit('MutationsEditShippingAddress', res)
    },
    async ActionsGetB2BDeliveryList (context, data) {
      var res = await AxiosManageShop.GetB2BDeliveryList(data)
      await context.commit('MutationsGetB2BDeliveryList', res)
    },
    async ActionsGetListIshipIII (context, data) {
      var res = await AxiosManageShop.GetListIshipIII(data)
      await context.commit('MutationsGetListIshipIII', res)
    },
    async ActionsGetCourierTypeII (context, data) {
      var res = await AxiosManageShop.GetCourierTypeII(data)
      await context.commit('MutationsGetCourierTypeII', res)
    },
    async ActionsGetShipmentsImages (context, data) {
      var res = await AxiosManageShop.GetShipmentsImages(data)
      await context.commit('MutationsGetShipmentsImages', res)
    }
  }
}
export default ModuleManageShop
