<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row>
        <!-- หัวข้อเรื่อง -->
        <v-col cols="12">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการขายสินค้าของร้านค้า</v-card-title>
          <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon>รายการขายสินค้าของร้านค้า</v-card-title>
        </v-col>
        <v-col  cols="12" :style="MobileSize ? 'margin-top: -10vw;' : 'margin-top: -2vw;'">
          <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
            <v-card-text>
              <!-- ค้นหาจากหมายเลขคำสั่งซื้อ -->
              <v-row>
                <v-col :cols="MobileSize ? 8 : 6" :style="MobileSize ? 'display: flex; flex-direction: column; padding: 0; margin-right: 2vw;' : ''">
                  <v-text-field
                    placeholder="ค้นหาจากหมายเลขคำสั่งซื้อ"
                    outlined
                    dense
                    hide-details
                    v-model="selectedOrderId"
                  ></v-text-field>
                </v-col>
                <!-- ฟิลเตอร์ข้อมูล -->
                <v-col cols="3" v-if="MobileSize" style="padding: 0;">
                  <v-btn @click="OpenModalFilter()" outlined rounded color="#27AB9C" height="36"><v-icon size="24" left dark>mdi-filter-outline</v-icon>ตัวกรอง</v-btn>
                </v-col>
                <!-- เลือกร้านค้า -->
                <v-col v-if="!MobileSize" cols="6">
                  <v-select
                    :items="sellerCheckboxMenu"
                    v-model="selectedCompany"
                    placeholder="เลือกร้านค้า"
                    item-text="name_th"
                    item-value="id"
                    outlined
                    multiple
                    dense
                    hide-details
                    no-data-text="ไม่มีร้านค้า"
                    @change="fetchFilterShop"
                  >
                    <template v-slot:selection="{ item, index }">
                      <v-chip v-if="index === 0">
                        <span>{{ item.name_th }}</span>
                      </v-chip>
                      <span
                        v-if="index === 1"
                        class="grey--text text-caption"
                      >
                        (+{{ selectedCompany.length - 1 }} อื่นๆ)
                      </span>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
              <!-- รายการสั่งซื้อร้านค้า -->
              <v-row v-if="!MobileSize">
                <!-- สถานะรายการ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">สถานะรายการ :</span>
                  <v-select
                    outlined
                    dense
                    v-model="selectedStatusList"
                    :items="statusList"
                    hide-details
                    @change="fetchFilterOrderType"
                  ></v-select>
                </v-col>
                <!-- Pay type -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">Pay type :</span>
                  <v-select
                    outlined
                    dense
                    :items="payTypeList"
                    v-model="selectedPaytype"
                    hide-details
                    @change="fetchFilterPayType"
                  ></v-select>
                </v-col>
                <!-- วันที่สั่งซื้อ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">วันที่สั่งซื้อ :</span>
                  <v-dialog
                    ref="dialogBuyDate"
                    v-model="startDateDialogOpen"
                    persistent
                    width="480px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="showBuyDate"
                        v-bind="attrs"
                        v-on="on"
                        hide-details
                        outlined
                        style="border-radius: 8px;"
                        dense
                        :class="MobileSize ? '' : ''"
                        placeholder="วว/ดด/ปปปป"
                        ><v-icon slot="append" color="#CCCCCC"
                          >mdi-calendar-multiselect</v-icon
                        ></v-text-field
                      >
                    </template>
                    <v-date-picker
                      v-model="selectedCreateDate"
                      color = "#27AB9C"
                      scrollable
                      reactive
                      full-width
                      locale="Th-th"
                      :max="
                        new Date(
                          Date.now() - new Date().getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .substr(0, 10)
                      "
                    >
                      <v-spacer></v-spacer>
                      <v-btn text color="primary" @click="closeStartDate()">
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="saveStartDate();
                        "
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <!-- วันที่อนุมัติ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">วันที่อนุมัติ :</span>
                  <v-dialog
                    ref="dialogBuyDate"
                    v-model="approveDateDialogOpen"
                    persistent
                    width="480px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="showApproveDate"
                        v-bind="attrs"
                        v-on="on"
                        hide-details
                        outlined
                        style="border-radius: 8px;"
                        dense
                        :class="MobileSize ? '' : ''"
                        placeholder="วว/ดด/ปปปป"
                        ><v-icon slot="append" color="#CCCCCC"
                          >mdi-calendar-multiselect</v-icon
                        ></v-text-field
                      >
                    </template>
                    <v-date-picker
                      v-model="approveDate"
                      color = "#27AB9C"
                      scrollable
                      reactive
                      full-width
                      locale="Th-th"
                      :max="
                        new Date(
                          Date.now() - new Date().getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .substr(0, 10)
                      "
                    >
                      <v-spacer></v-spacer>
                      <v-btn text color="primary" @click="closeApproveDate()">
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="saveApproveDate();"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <!-- วันที่รอบบริการ -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">วันที่รอบบริการ :</span>
                  <v-dialog
                    ref="modalRangeDate"
                    v-model="dateRangeDialogOpen"
                    persistent
                    width="480px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        readonly
                        v-model="showDateRange"
                        v-bind="attrs"
                        v-on="on"
                        style="border-radius: 8px;"
                        outlined
                        dense
                        hide-details
                        :class="MobileSize ? '' : ''"
                        placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                        ><v-icon slot="append" color="#CCCCCC"
                          >mdi-calendar-multiselect</v-icon
                        ></v-text-field>
                    </template>
                    <v-date-picker
                      color="#27AB9C"
                      v-model="selectedDateRange"
                      scrollable
                      range
                      reactive
                      full-width
                      locale="Th-th"
                    >
                      <v-spacer></v-spacer>
                      <v-btn text color="primary" @click="closeDateRangeDialog()">
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="saveDateRangeDialog(selectedDateRange)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <!-- สถานะการชำระเงิน -->
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; flex-direction: column">
                  <span class="pr-2">สถานะการชำระเงิน :</span>
                  <v-select
                    :items="paidStatusList"
                    v-model="selectedPaidStatus"
                    dense
                    outlined
                    hide-details
                    @change="fetchTransactionsStatus"
                  ></v-select>
                </v-col>
              </v-row>
            </v-card-text>
            <v-row :style="MobileSize ? 'display: flex; flex-direction: column;' : ''">
              <v-col v-if="!MobileSize" :style="!MobileSize ? 'padding: 2.5vw 2.5vw 0 2.5vw; display: flex; justify-content: space-between; align-items: center;' : 'margin-left: 2vw; padding: 5vw 4vw;'">
                <span :style="!MobileSize ? 'font-size: 18px; font-weight: bold;' : ''">รายการสั่งซื้อสินค้าทั้งหมด</span>
                <div style="display: flex; gap: .5vw">
                  <v-btn color="#38b2a4" rounded class="white--text" @click="clearFilter">ล้างค่า</v-btn>
                  <v-btn color="#38b2a4" rounded class="white--text" @click="exportExcel">EXPORT FILE</v-btn>
                </div>
              </v-col>
              <v-row v-else dense>
                <v-col style="padding: 5vw 0 0 6vw;">
                  <span
                    :class="MobileSize ? '' : ''"
                    style="line-height: 24px; align-items: center; color: #333333; font-weight: 400;"
                    :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'"
                    >รายการสั่งซื้อสินค้า {{dataTable.length}} รายการ</span
                  >
                </v-col>
                <v-col style="padding: 0vw 6vw 3vw 6vw;" cols="12" class="mt-2" v-if="MobileSize">
                  <v-btn block rounded color="#27AB9C" height="40" class="white--text mr-2" @click="exportExcel">
                    <v-icon>mdi-file-export</v-icon> Export File
                  </v-btn>
                </v-col>
              </v-row>
            </v-row>
            <v-row>
              <v-col>
                <v-card style="border-radius: 5px;" class="mt-5">
                  <v-data-table
                    :headers="headersOrdersTable"
                    :items="dataTable"
                    :search="selectedOrderId"
                    style="width: 100%; white-space: nowrap; border-width: 20px;"
                    height="100%"
                    :items-per-page="5"
                    no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
                    no-data-text="ไม่มีข้อมูลรายการสั่งซื้อในตาราง"
                    :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
                  >
                    <template v-slot:[`item.created_at`]="{ item }">
                      <span>{{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                    </template>
                    <template v-slot:[`item.pay_type`]="{ item }">
                      <v-chip color="#eff2f2" v-if="item.pay_type === 'general'">
                        <span style="color: #808c96;">{{item.pay_type}}</span>
                      </v-chip>
                      <v-chip color="#d2daec" v-else-if="item.pay_type === 'onetime'">
                        <span style="color: #1b5dd8;">{{item.pay_type}}</span>
                      </v-chip>
                      <v-chip color="#f1decf" v-else-if="item.pay_type === 'recurring'">
                        <span style="color: red;">{{item.pay_type}}</span>
                      </v-chip>
                    </template>
                    <template v-slot:[`item.order_type`]="{ item }">
                    <div v-if="item.shop_approve !== 'waiting_approve' && item.shop_approve !== 'reject'">
                      <span style="color: #636363;" v-if="item.detail_status !== 'ยกเลิกคำสั่งซื้อ' && (item.transaction_status === '-' || item.transaction_status === null)"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ item.order_type }}</span>
                      <span v-else-if="item.detail_status !== 'ยกเลิกคำสั่งซื้อ' && item.transaction_status !== '-'" :style="{ 'color' : getTextColorStatus(item.transaction_status)}"><v-icon :color="getTextColorStatus(item.transaction_status)">mdi-circle-medium</v-icon>{{ getTextStatus(item.transaction_status) }}</span>
                      <span style="color: #D1392B;" v-else><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ item.detail_status }}</span>
                    </div>
                    <div>
                      <span v-if="item.shop_approve === 'waiting_approve'" style="color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รออนุมัติคำสั่งซื้อ</span>
                      <span v-if="item.shop_approve === 'reject'" style="color: #D1392B;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>ปฏิเสธคำสั่งซื้อ</span>
                    </div>
                  </template>
                  <template v-slot:[`item.date_contract`]="{ item }">
                    <span>{{formatDate(item.date_contract)}}</span>
                  </template>
                    <template v-slot:[`item.transaction_status`]="{ item }">
                    <span
                      v-if="
                        item.transaction_status === 'Success' &&
                        item.seller_sent_status !== 'cancel'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >ชำระเงินสำเร็จ</v-chip
                      >
                    </span>
                    <span
                      v-else-if="
                        item.seller_sent_status === 'cancel' ||
                        item.transaction_status === 'Cancel'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#f7c5ad"
                        text-color="#f50"
                        >ยกเลิกสินค้า</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Pending'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >รออนุมัติ</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Approve'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >อนุมัติ</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Credit'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#E5EFFF"
                        text-color="#1B5DD6"
                        >ชำระเงินแบบเครดิตเทอม</v-chip
                      >
                    </span>
                    <span v-else-if="item.transaction_status === 'Fail'">
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F7D9D9"
                        text-color="#D1392B"
                        >ชำระเงินไม่สำเร็จ</v-chip
                      >
                    </span>
                    <span v-else>
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >ยังไม่ชำระเงิน</v-chip
                      >
                    </span>
                  </template>
                    <template v-slot:[`item.approve_at`] = "{ item }">
                      <span>{{new Date(item.approve_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                    </template>
                    <template v-slot:[`item.quatation_sheet`] = "{ item }">
                      <v-btn x-small class="elevation-0 py-5 px-0" @click="getQuatationSheet(item.quatation_sheet)">
                        <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                      </v-btn>
                    </template>
                    <template v-slot:[`item.tax_receipt`] = "{ item }">
                      <v-btn v-if="item.tax_receipt !== '-'" x-small class="elevation-0 py-5 px-0" @click="getConstSheet(item.tax_receipt)">
                        <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                      </v-btn>
                      <span v-else>-</span>
                    </template>
                    <template v-slot:[`item.qt_order_invoice`] = "{ item }">
                      <v-btn v-if="item.qt_order_invoice !== '-'" x-small class="elevation-0 py-5 px-0" @click="getConstSheet(item.qt_order_invoice)">
                        <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                      </v-btn>
                      <span v-else>-</span>
                    </template>
                    <template v-slot:[`item.cost_sheet`] = "{ item }">
                      <v-btn v-if="item.cost_sheet !== '-'" x-small class="elevation-0 py-5 px-0" @click="getConstSheet(item.cost_sheet)">
                        <v-icon color="rgb(39, 171, 156)">mdi-file-document</v-icon>
                      </v-btn>
                      <span v-else>-</span>
                    </template>
                    <template v-slot:[`item.detail`]="{ item }">
                      <v-menu offset-y>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn
                            v-bind="attrs"
                            v-on="on"
                            class="pt-4 pb-4"
                            x-small
                            outlined
                            style="
                              max-width: 32px;
                              max-height: 32px;
                              border-radius: 4px;
                              border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                              background: var(--neutral-ffffff, #fff);
                              box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                            "
                          >
                            <!-- <b>รายละเอียด</b> -->
                            <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                          </v-btn>
                        </template>
                        <v-list>
                          <v-list-item
                            v-for="(items, index) in actionsItem"
                            :key="index"
                            link
                          >
                            <v-list-item-content
                              @click="gotoActions(item, items.value)"
                            >
                              <v-list-item-title>{{ items.text }}</v-list-item-title>
                            </v-list-item-content>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </template>
                    <template v-slot:[`item.order_status`]="{ item }">
                      <span
                      v-if="
                        item.order_status === 'ชำระเงินสำเร็จ'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >ชำระเงินสำเร็จ</v-chip
                      >
                    </span>
                      <span
                      v-else-if="
                        item.order_status === 'ยังไม่ชำระเงิน'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >ยังไม่ชำระเงิน</v-chip
                      >
                    </span>
                      <span
                      v-else-if="
                        item.order_status === 'ยกเลิกคำสั่งซื้อ'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#f7c5ad"
                        text-color="#D1392B"
                        >ยกเลิกคำสั่งซื้อ</v-chip
                      >
                    </span>
                    </template>
                    <template v-slot:[`item.item_status`]="{ item }">
                      <span
                      v-if="
                        item.item_status === 'ชำระเงินแล้ว'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >ชำระเงินแล้ว</v-chip
                      >
                    </span>
                      <span
                      v-else-if="
                        item.item_status === 'รอชำระเงิน'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >รอชำระเงิน</v-chip
                      >
                    </span>
                    <span
                      v-else-if="
                        item.item_status === 'เกินกำหนดชำระ'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#f7c5ad"
                        text-color="#D1392B"
                        >เกินกำหนดชำระ</v-chip
                      >
                    </span>
                    <span
                      v-else-if="
                        item.item_status === 'ยกเลิกคำสั่งซื้อ'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#f7c5ad"
                        text-color="#D1392B"
                        >ยกเลิกคำสั่งซื้อ</v-chip
                      >
                    </span>
                    </template>
                    <template v-slot:[`item.delivery_status`]="{ item }">
                      <span
                      v-if="
                        item.delivery_status === 'พัสดุถึงผู้รับปลายทาง'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#F0F9EE"
                        text-color="#1AB759"
                        >{{item.delivery_status}}</v-chip
                      >
                    </span>
                      <span
                      v-else-if="
                        item.delivery_status === 'รอชำระเงิน'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#FCF0DA"
                        text-color="#E9A016"
                        >{{item.delivery_status}}</v-chip
                      >
                    </span>
                    <span
                      v-else-if="
                        item.delivery_status === 'นำส่งไม่สำเร็จหรือมีการปฏิเสธจากผู้รับ' ||
                        item.delivery_status === 'เคลมและไม่มีการส่งคืนสินค้า' ||
                        item.delivery_status === 'มีการตัดสินใจตีคืนพัสดุกลับไปต้นทาง' ||
                        item.delivery_status === 'ยกเลิกงาน' ||
                        item.delivery_status === 'พนักงานรับพัสดุในจุดต้นทางแล้วผู้ส่งเปลี่ยนใจเรียกคืนสินค้า'
                      "
                    >
                      <v-chip
                        :class="!MobileSize ? 'ma-2' : 'ma-0'"
                        color="#f7c5ad"
                        text-color="#D1392B"
                        >{{item.delivery_status}}</v-chip
                      >
                    </span>
                    <span
                      v-else-if="
                        item.delivery_status === '-'
                      "
                    >
                     -
                    </span>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="ModalFilter" width="100%" persistent>
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 20px;">
        <v-card-text>
          <v-toolbar flat color="rgba(0, 0, 0, 0)">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title><span style="color: #333333; font-size: 16px;"><b>ตัวกรอง</b></span></v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small @click="ModalFilter = !ModalFilter" icon><v-icon color="#CCCCCC">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-row dense>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
                >เลือกร้านค้า :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                :items="sellerCheckboxMenu"
                v-model="selectedCompany"
                placeholder="เลือกร้านค้า"
                item-text="name_th"
                item-value="id"
                outlined
                multiple
                dense
                hide-details
                style="border-radius: 8px;"
                append-icon="mdi-chevron-down"
              >
                <template v-slot:selection="{ item, index }">
                  <v-chip v-if="index === 0">
                    <span>{{ item.name_th }}</span>
                  </v-chip>
                  <span
                    v-if="index === 1"
                    class="grey--text text-caption"
                  >
                    (+{{ selectedCompany.length - 1 }} อื่นๆ)
                  </span>
                </template>
              </v-select>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 500;
                "
                >สถานะรายการ :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                v-model="selectedStatusList"
                :items="statusList"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                class="setCustomSelect"
                dense
                style="border-radius: 8px;"
                hide-details
                @change="fetchFilterOrderType"
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >Pay Type : </span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                :items="payTypeList"
                v-model="selectedPaytype"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                style="border-radius: 8px;"
                class="setCustomSelect"
                :class="MobileSize ? '' : 'pr-4'"
                outlined
                hide-details
                dense
                @change="fetchFilterPayType"
              ></v-select>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่สั่งซื้อ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogBuyDate"
                v-model="startDateDialogOpen"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="showBuyDate"
                    v-bind="attrs"
                    v-on="on"
                    hide-details
                    outlined
                    style="border-radius: 8px;"
                    dense
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="selectedCreateDate"
                  color = "#27AB9C"
                  scrollable
                  reactive
                  locale="Th-th"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="closeStartDate()">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="saveStartDate()
                    "
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่อนุมัติ : </span
              >
            </v-col>
            <v-col cols="12">
              <v-dialog
                ref="dialogAcceptDate"
                v-model="approveDateDialogOpen"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="showApproveDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    hide-details
                    style="border-radius: 8px;"
                    dense
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="approveDate"
                  scrollable
                  reactive
                  locale="Th-th"
                  color = "#27AB9C"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text color="primary"
                    @click="closeApproveDate()">ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="saveApproveDate()">ตกลง
                    </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >วันที่รอบบริการ :</span
              >
            </v-col>
            <v-col cols="12" >
              <v-dialog
                ref="modalRangeDate"
                v-model="dateRangeDialogOpen"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="showDateRange"
                    v-bind="attrs"
                    v-on="on"
                    style="border-radius: 8px;"
                    outlined
                    dense
                    hide-details
                    :class="MobileSize ? '' : 'pr-4'"
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field>
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="selectedDateRange"
                  scrollable
                  range
                  reactive
                  locale="Th-th"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text color="primary"
                    @click="closeDateRangeDialog()">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="saveDateRangeDialog(selectedDateRange)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >สถานะการชำระเงิน :</span
              >
            </v-col>
            <v-col cols="12">
              <v-select
                :items="paidStatusList"
                v-model="selectedPaidStatus"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                dense
                class="setCustomSelect"
                style="border-radius: 8px;"
                hide-details
                @change="fetchTransactionsStatus"
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-row dense justify="center">
            <v-btn width="125" height="36" text color="#27AB9C" @click="clearFilter">ล้างค่า</v-btn>
            <v-btn width="125" height="36" rounded color="#27AB9C" class="white--text" @click="saveFilterMobile()">ยืนยัน</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dataTable: [],
      statusList: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'New Service', value: 'newser' },
        { text: 'Change', value: 'change' },
        { text: 'Renew', value: 'renew' },
        { text: 'Change&Renew', value: 'change_and_renew' },
        { text: 'Terminate', value: 'terminate' }
      ],
      selectedStatusList: '',
      selectedPaytype: '',
      payTypeList: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'General', value: 'general' },
        { text: 'OneTime', value: 'onetime' },
        { text: 'Recurring', value: 'recurring' }
      ],
      selectedPaidStatus: '',
      paidStatusList: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ชำระเงินสำเร็จ', value: 'Success' },
        { text: 'ยังไม่ชำระเงิน', value: 'Not Paid' },
        { text: 'ยกเลิกคำสั่งซื้อ', value: 'Cancel' }
      ],
      headersOrdersTable: [
        { text: 'วันที่ทำรายการ', value: 'created_at', align: 'center', class: 'backgroundTable' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', align: 'center', class: 'backgroundTable' },
        { text: 'Pay Type', value: 'pay_type', align: 'center', class: 'backgroundTable' },
        { text: 'สถานะสั่งซื้อ', value: 'order_status', align: 'center', class: 'backgroundTable' },
        { text: 'สถานะรายการ', value: 'item_status', align: 'center', class: 'backgroundTable' },
        { text: 'ผู้ซื้อ', value: 'buyer_name', align: 'center', class: 'backgroundTable' },
        { text: 'วันที่อนุมัติ', value: 'approve_at', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ใบเสร็จ', value: 'orderIDRef', align: 'center', class: 'backgroundTable' },
        { text: 'ใบเสนอราคา', value: 'quatation_sheet', align: 'center', class: 'backgroundTable' },
        { text: 'ใบแจ้งหนี้', value: 'qt_order_invoice', align: 'center', class: 'backgroundTable' },
        { text: 'วันที่รอบบริการ', value: 'date_contract', align: 'center', class: 'backgroundTable' },
        { text: 'ใบกำกับภาษี', value: 'tax_receipt', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ PR', value: 'pr_document_id', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ PO', value: 'po_document_id', align: 'center', class: 'backgroundTable' },
        { text: 'เลขที่ SO', value: 'ref_callback_so_id', align: 'center', class: 'backgroundTable' },
        { text: 'cost sheet', value: 'cost_sheet', align: 'center', class: 'backgroundTable' },
        { text: 'สถานะการจัดส่ง', value: 'delivery_status', align: 'center', class: 'backgroundTable' },
        { text: 'Tracking Number', value: 'tracking_number', align: 'center', class: 'backgroundTable' },
        { text: 'จัดการ', value: 'detail', align: 'center', class: 'backgroundTable' }
      ],
      actionsItem: [
        { text: 'รายละเอียด', value: 'detail' }
      ],
      ordersRequestbody: {
        tax_id: this.taxId,
        list: [],
        type: 'seller',
        search: '',
        order_type: '',
        pay_type: '',
        order_created_at: '',
        start_date_contract: '',
        end_date_contreact: '',
        approve_at: '',
        transaction_status: '',
        count: -1,
        pages: 1
      },
      companylistRequestBody: {
        tax_id: this.taxId,
        type: 'seller'
      },
      selectedOrderId: '',
      selectedPayType: '',
      sellerCheckboxMenu: [],
      selectedSellerCheckbox: [],
      startDateDialogOpen: false,
      selectedCreateDate: '',
      selectedEndDate: '',
      approveDateDialogOpen: false,
      approveDate: '',
      showApproveDate: '',
      showBuyDate: '',
      showDateRange: '',
      selectedDateRange: [],
      dateRangeDialogOpen: false,
      shoplist: [],
      companiesList: [],
      ModalFilter: false,
      selectedCompany: [],
      taxId: ''
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavBusiness')
    await this.getTaxId()
  },
  watch: {
    companylistRequestBody (val) {
      // console.log(val)
    },
    selectedStatusList (val) {
      this.ordersRequestbody.order_type = val
      // console.log(val, 'selectedStatusList')
    },
    selectedSellerCheckbox (val) {
      // console.log(val)
    },
    selectedCreateDate (val) {
      // console.log(val, 'selectedCreateDate')
    },
    selectedDateRange (val) {
      // console.log(val, 'selectedDateRange')
    },
    shoplist (val) {
      // console.log(val)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listOrderShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listOrderShop' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    async gotoActions (val) {
      if (this.MobileSize === false) {
        this.$router.push({ path: `/detailOrderShop?order_number=${val.order_number}&seller_shop_id=${val.seller_shop_id}&tracking_number=${val.tracking_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/detailOrderShopMobile?order_number=${val.order_number}&seller_shop_id=${val.seller_shop_id}&tracking_number=${val.tracking_number}` }).catch(() => {})
      }
    },
    async fetchCompanylist () {
      // this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListCompany', this.companylistRequestBody)
      var response = await this.$store.state.ModuleBusiness.stateListCompany
      if (response.ok === 'y') {
        var listShop = response.result.companies
        for (var i = 0; i < listShop.length; i++) {
          this.sellerCheckboxMenu.push(listShop[i])
          this.shoplist.push(listShop[i].id)
          this.selectedCompany.push(listShop[i])
          this.ordersRequestbody.list.push(listShop[i].id)
        }
      } else if (response.code === 400) {
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่มีข้อมูลร้านค้าในระบบ',
          showConfirmButton: false,
          timer: 2500
        })
        this.$store.commit('closeLoader')
      }
      // this.$store.commit('closeLoader')
    },
    async fetchListBusinessOrders () {
      if (this.ordersRequestbody.list.length !== 0) {
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsListBusinessOrders', this.ordersRequestbody)
        var response = await this.$store.state.ModuleBusiness.stateListBusinessOrders
        this.dataTable = response.result.orders
        this.$store.commit('closeLoader')
      }
    },
    async closeStartDate () {
      this.startDateDialogOpen = false
      this.selectedCreateDate = ''
      this.showBuyDate = null
      this.ordersRequestbody.start_date_contract = ''
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async saveStartDate () {
      this.startDateDialogOpen = false
      this.showBuyDate = this.formatDateToShow(this.selectedCreateDate)
      this.ordersRequestbody.order_created_at = this.selectedCreateDate
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async closeApproveDate () {
      this.approveDateDialogOpen = false
      this.approveDate = ''
      this.showApproveDate = null
      this.ordersRequestbody.approve_at = ''
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async saveApproveDate () {
      this.approveDateDialogOpen = false
      this.ordersRequestbody.approve_at = this.approveDate
      this.showApproveDate = this.formatDateToShow(this.approveDate)
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async closeDateRangeDialog () {
      this.dateRangeDialogOpen = false
      this.selectedDateRange = []
      this.ordersRequestbody.start_date_contract = ''
      this.ordersRequestbody.end_date_contreact = ''
      this.showDateRange = ''
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async saveDateRangeDialog (date) {
      var dates = ''
      if (date[0] > date[1]) {
        this.ordersRequestbody.start_date_contract = date[1]
        this.ordersRequestbody.end_date_contreact = date[0]
        dates = new Date(date[1]).toLocaleDateString('th-TH') + ' - ' + new Date(date[0]).toLocaleDateString('th-TH')
      } else if (date[0] < date[1]) {
        this.ordersRequestbody.start_date_contract = date[0]
        this.ordersRequestbody.end_date_contreact = date[1]
        dates = new Date(date[0]).toLocaleDateString('th-TH') + ' - ' + new Date(date[1]).toLocaleDateString('th-TH')
      } else if (date[0] === date[1]) {
        this.ordersRequestbody.start_date_contract = date[0]
        this.ordersRequestbody.end_date_contreact = date[0]
        dates = new Date(date[0]).toLocaleDateString('th-TH')
      } else if (date.length === 1) {
        this.ordersRequestbody.start_date_contract = date[0]
        this.ordersRequestbody.end_date_contreact = date[0]
        dates = new Date(date[0]).toLocaleDateString('th-TH')
      }
      this.dateRangeDialogOpen = false
      this.showDateRange = dates
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async fetchTransactionsStatus () {
      this.ordersRequestbody.transaction_status = this.selectedPaidStatus
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async fetchFilterOrderType () {
      this.ordersRequestbody.order_type = this.selectedStatusList
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async fetchFilterPayType () {
      this.ordersRequestbody.pay_type = this.selectedPaytype
      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async fetchFilterShop () {
      // console.log(this.selectedCompany)
      if (this.MobileSize === false) {
        this.ordersRequestbody.list = this.selectedCompany
        if (this.selectedCompany.length !== 0) {
          await this.fetchListBusinessOrders()
        }
      }
    },
    async fetchOrderShop () {
    },
    async clearFilter () {
      this.selectedOrderId = ''
      this.selectedStatusList = ''
      this.selectedSellerCheckbox = []
      this.selectedPaytype = ''
      this.selectedCreateDate = ''
      this.showBuyDate = ''
      this.approveDate = ''
      this.showApproveDate = ''
      this.showDateRange = ''
      this.selectedPaidStatus = ''
      this.selectedDateRange = []
      this.selectedCompany = this.shoplist
      this.ordersRequestbody = {
        tax_id: this.taxId,
        type: 'seller',
        list: this.shoplist,
        search: '',
        order_type: '',
        pay_type: '',
        order_created_at: '',
        start_date_contract: '',
        end_date_contreact: '',
        approve_at: '',
        transaction_status: '',
        count: -1,
        pages: 1
      }

      if (this.MobileSize === false) {
        await this.fetchListBusinessOrders()
      }
    },
    async getQuatationSheet (val) {
      window.open(val, '_blank')
    },
    async getConstSheet (val) {
      window.open(val, '_blank')
    },
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}business/exportBussinessOrder`,
        data: this.ordersRequestbody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'report.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    OpenModalFilter () {
      this.ModalFilter = true
    },
    async saveFilterMobile () {
      this.ordersRequestbody = {
        tax_id: this.taxId,
        type: 'seller',
        list: this.selectedCompany,
        search: '',
        order_type: this.selectedStatusList,
        pay_type: this.selectedPaytype,
        start_date_contract: '',
        end_date_contreact: '',
        order_created_at: this.selectedCreateDate,
        approve_at: this.approveDate,
        transaction_status: this.selectedPaidStatus,
        count: -1,
        pages: 1
      }
      if (this.selectedDateRange.length === 2) {
        if (this.selectedDateRange[0] > this.selectedDateRange[1]) {
          this.ordersRequestbody.start_date_contract = this.selectedDateRange[1]
          this.ordersRequestbody.end_date_contreact = this.selectedDateRange[0]
        } else if (this.selectedDateRange[0] < this.selectedDateRange[1]) {
          this.ordersRequestbody.start_date_contract = this.selectedDateRange[0]
          this.ordersRequestbody.end_date_contreact = this.selectedDateRange[1]
        }
      } else if (this.selectedDateRange.length === 1) {
        this.ordersRequestbody.start_date_contract = this.selectedDateRange[0]
        this.ordersRequestbody.end_date_contreact = this.selectedDateRange[0]
      }
      await this.fetchListBusinessOrders()
      this.ModalFilter = false
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    getTextColorStatus (item) {
      if (item === 'Success') return '#52C41A'
      else if (item === 'Fail') return '#D1392B'
      else if (item === 'Cancel') return '#D1392B'
      else return '#FAAD14'
    },
    getTextStatus (item) {
      if (item === 'Success') return 'ชำระเงินแล้ว'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Cancel') return 'เกินกำหนดชำระ'
      else return 'รอชำระเงิน'
    },
    getColor (item) {
      if (item === 'การจัดส่งสำเร็จ') return '#F0F9EE'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#DBECFA'
      else if (item === 'ส่งคืนสินค้า') return '#F7D9D9'
      else return '#FCF0DA'
    },
    getTextColor (item) {
      if (item === 'การจัดส่งสำเร็จ') return '#1AB759'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#2A70C3'
      else if (item === 'ส่งคืนสินค้า') return '#D1392B'
      else return '#E9A016'
    },
    formatDate (val) {
      if (val === '-') {
        return ' - '
      } else if (val !== undefined) {
        var newDate = val.split(' - ')
        var startDate = new Date(newDate[0]).toLocaleDateString('th-TH', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
        var endDate = new Date(newDate[1]).toLocaleDateString('th-TH', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
        newDate = startDate + ' - ' + endDate
        return newDate
      } else {
        return ' - '
      }
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        // this.taxId = response.data.array_business[0].owner_tax_id
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          this.companylistRequestBody.tax_id = this.taxId
          this.ordersRequestbody.tax_id = this.taxId
          await this.fetchCompanylist()
          await this.fetchListBusinessOrders()
        }
        this.$store.commit('closeLoader')
      }
    },
    backToMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep table {
  tbody {
  tr {
      td:nth-child(19) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th:nth-child(1) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th:nth-child(19) {
      z-index: 11;
      background: white;
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      }
  }
  }
}
</style>
