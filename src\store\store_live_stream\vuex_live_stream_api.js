import AxiosLiveStream from './axios_live_stream_api'

const ModuleLiveStream = {
  state: {
    stateGenTokenStream: [],
    stateLiveListProduct: []
  },
  mutations: {
    mutationsGenTokenStream (state, data) {
      state.stateGenTokenStream = data
    },
    mutationsLiveListProduct (state, data) {
      state.stateLiveListProduct = data
    }
  },
  actions: {
    // Gen Live Stream
    async actionsGenTokenStream (context, access) {
      const responseData = await AxiosLiveStream.GenTokenStream(access)
      await context.commit('mutationsGenTokenStream', responseData)
    },
    async actionsLiveListProduct (context, access) {
      const responseData = await AxiosLiveStream.LiveListProduct(access)
      await context.commit('mutationsLiveListProduct', responseData)
    }
  }
}
export default ModuleLiveStream
