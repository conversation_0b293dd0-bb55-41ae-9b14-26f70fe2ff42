<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ร้านค้าเข้าร่วม affiliate</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ร้านค้าเข้าร่วม affiliate</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าที่เข้าร่วม affiliate" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row >
              <v-col :cols="MobileSize ? 4 : IpadSize ? 4 : 6" :class="IpadSize ? 'pt-0 d-flex align-end': MobileSize ? 'pt-3 pl-4' : 'pt-6'">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อร้านค้าที่เข้าร่วมทั้งหมด {{ tableData.length }} รายการ</span>
                <span style="font-size: 14px; line-height: 24px; align-items: center; color: gray;" v-else-if="(MobileSize)">{{ tableData.length }} รายการ</span>
                <span style="font-size: 14px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(IpadSize)">ร้านค้า {{ tableData.length }} รายการ</span>
              </v-col>
              <v-spacer></v-spacer>
              <v-col cols="6" class="d-flex justify-end">
                <v-btn v-if="MobileSize" small rounded elevation="0" dark color="#27AB9C" @click="exportSellerExcel()">Export Excel</v-btn>
                <v-btn v-else-if="!MobileSize" rounded elevation="0" dark color="#27AB9C" @click="exportSellerExcel()">Export Excel</v-btn>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12" :class="MobileSize ? 'pt-0' : 'pt-2'">
                <v-data-table
                  :headers="header"
                  :items="tableData"
                  :search="search"
                  class="elevation-1"
                  :items-per-page="10"
                  style="width:100%;"
                  height="100%">
                  <template v-slot:[`item.indexTable`]="{index}">
                    {{ index + 1 }}
                  </template>
                  <template v-slot:[`item.path_logo`]="{item}">
                    <v-col>
                      <v-avatar v-if="item.path_logo === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                      </v-avatar>
                      <v-avatar v-else rounded size="44" color="#FFF">
                        <v-img contain :src="item.path_logo"></v-img>
                      </v-avatar>
                    </v-col>
                  </template>
                  <template v-slot:[`item.status`]="{item}">
                    <v-chip :color="item.status === 'yes' ? '#f0f9ee' : 'rgb(247, 217, 217)'" :text-color="item.status === 'yes' ? 'green' : 'red'">
                      {{ statusShop(item.status)}}
                    </v-chip>
                  </template>
                  <template v-slot:[`item.seller_shop_id`]="{item}">
                    <v-btn v-if="item.itemsCount !== 0" outlined color="#27AB9C" @click="MobileSize ? listProductsMobile(item.seller_shop_id, item.shop_name) : listProducts(item.seller_shop_id, item.shop_name)">ดูรายการสินค้า</v-btn>
                    <v-btn v-if="item.itemsCount === 0" disabled outlined color="#27AB9C" @click="MobileSize ? listProductsMobile(item.seller_shop_id, item.shop_name) : listProducts(item.seller_shop_id, item.shop_name)">ดูรายการสินค้า</v-btn>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      search: '',
      header: [
        { text: 'ลำดับ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '50', value: 'indexTable' },
        { text: 'โลโก้', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'path_logo' },
        { text: 'ชื่อร้าน', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'shop_name' },
        { text: 'สถานะ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'status' },
        { text: 'จำนวนสินค้า', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'itemsCount' },
        { text: 'สินค้า', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'seller_shop_id' }
      ],
      tableData: []
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    // this.getTableData()
    if (localStorage.getItem('oneData') !== null) {
      this.getTableData()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      console.log(val)
      if (val === true) {
        this.$router.push({ path: '/sellerJoinAffiliateMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'sellerJoinAffiliate')
        this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    async getTableData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetSellerJoinAffiliate')
      var response = await this.$store.state.ModuleAdminManage.stateGetSellerJoinAffiliate
      if (response.code === 200) {
        this.tableData = response.data
      }
      this.$store.commit('closeLoader')
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    statusShop (val) {
      if (val === 'yes') {
        return 'เปิดใช้งาน'
      } else if (val === 'no') {
        return 'ไม่เปิดใช้งาน'
      }
    },
    listProducts (shopID, name) {
      this.$router.push(`/listProductsAffiliate?id=${shopID}&name=${name}`)
    },
    listProductsMobile (shopID, name) {
      this.$router.push(`/listProductsAffiliateMobile?id=${shopID}&name=${name}`)
    },
    async exportSellerExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}affiliate/export/seller`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        console.log(response)
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'sellerJoinAffiliate.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    }
  }
}
</script>
