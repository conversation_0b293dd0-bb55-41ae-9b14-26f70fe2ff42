<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" style="background: #FFFFFF;">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0 d-flex align-center">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการแบนเนอร์โฆษณาและข่าวสาร</v-card-title>
      <v-card-title style="font-weight: 700; font-size: medium;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการแบนเนอร์โฆษณาและข่าวสาร</v-card-title>
      <v-spacer></v-spacer>
      <div class="d-flex align-baseline">
        <span class="pr-3" :style="MobileSize ? 'font-size: small;' : 'font-size: 16px;'">เปิดใช้งาน</span>
        <v-switch
          v-model="isActive"
          inset
          color="#27AB9C"
          class="my-0"
        ></v-switch>
      </div>
    </v-card>
    <v-row class="ml-1">
      <v-col cols="12" class="mt-2 d-flex">
        <span class="mr-3" style="font-size: medium; width: 120px;">ค่าเริ่มต้น</span>
        <v-radio-group v-model="defaultSetting" row class="my-0">
          <v-radio style="font-size: 14px;" label="กำหนดภาพเป็นค่าเริ่มต้น" value="popup_image"></v-radio>
          <v-radio style="font-size: 14px;" label="กำหนดวีดีโอเป็นค่าเริ่มต้น" value="popup_video"></v-radio>
        </v-radio-group>
      </v-col>
    </v-row>
    <v-row no-gutters v-if="defaultSetting === 'popup_image'">
      <v-col class="pa-0">
        <v-col cols="12" v-if="defaultSetting === 'popup_image'">
          <v-icon>mdi-menu-right</v-icon>
          <span>ภาพแบนเนอร์โฆษณาและข่าวสาร</span>
        </v-col>
        <v-row class="pl-3 pt-2 pr-3 ma-2"
          v-for="(item, index) in dataGroupImage"
          :key="index"
          :v-if="defaultSetting === 'popup_image'"
          style="border-radius: 1.5vw; box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;"
        >
          <v-col>
            <v-btn color="#27AB9C" text @click="removeItem(index)">
              <v-icon>mdi-trash-can-outline</v-icon>
              <span>ลบ</span>
            </v-btn>
          </v-col>
          <v-col cols="12" style="position: relative; margin-top: -1vw;" v-if="item.DataImage !== ''">
            <v-card
              class="mt-3 pa-3"
              elevation="0"
              :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
              @click="onPickFileBanner(index)"
            >
              <v-row dense align="center" justify="center">
                <v-col cols="12" md="12" align="center">
                  <v-img :src="item.DataImage" style="width: 350px; border-radius: inherit;"></v-img>
                </v-col>
              </v-row>
              <v-btn
                v-if="DataImagePopup"
                text
                small
                fab
                style="position: absolute; top: 5px; right: 5px; z-index: 1; background-color: #f44336;"
                @click="removeImagePopup(index)"
              >
                <v-icon small color="#fff">mdi-delete</v-icon>
              </v-btn>
            </v-card>
          </v-col>
          <v-col cols="12" md="12" v-else>
            <v-card
              class="mt-3"
              elevation="0"
              :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
              @click="onPickFileBanner(index)"
            >
              <v-card-text>
                <v-row dense align="center" justify="center" style="cursor: pointer;">
                  <v-file-input
                    v-model="DataImage"
                    accept="image/jpeg, image/jpg, image/png"
                    @change="onFileSelectedBanner(index, DataImage)"
                    :id="'file_input_banner' + index"
                    multiple
                    :clearable="false"
                    style="display:none"
                  ></v-file-input>
                  <v-col cols="12" md="12">
                    <v-row justify="center" align="center">
                      <v-col cols="12" md="12" align="center">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                          width="280.34"
                          height="154.87"
                          contain
                        ></v-img>
                      </v-col>
                      <v-col cols="12" md="12" style="text-align: center;">
                        <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                        <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                        <span :style="IpadSize ? { 'font-size': '9px' } : { 'font-size': '12px' }">
                          {{ layout === 'vertical' ? 'ขนาดรูปภาพ 420 x 540 px  ไฟล์นามสกุล .JPEG,PNG' : 'ขนาดรูปภาพ 1480 x 620 px  ไฟล์นามสกุล .JPEG,PNG' }}
                        </span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col :cols="MobileSize ? 12 : IpadSize ? 9 : 10" class="d-flex ml-2">
            <div class="mr-2">
              <span>การจัดวาง :</span>
            </div>
            <v-radio-group v-model="item.layout" row>
              <v-radio style="font-size: 14px;" label="แนวตั้ง" value="vertical"></v-radio>
              <v-radio style="font-size: 14px;" label="แนวนอน" value="horizontal"></v-radio>
            </v-radio-group>
          </v-col>
          <v-col class="ml-2" cols="12" md="12" style="margin-top: -2vw;">
            <v-row dense align="center" justify="center" style="cursor: pointer;">
              <v-col cols="12">
                <span>Link สำหรับรูปภาพ</span>
                <span style="color: red;"> *</span>
              </v-col>
              <v-col cols="12">
                  <v-text-field v-model="item.additional_link" placeholder="กรอก URL สำหรับรูปภาพ" dense outlined></v-text-field>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-col class="d-flex justify-center" v-if="dataGroupImage.length < 10">
          <v-btn color="#27AB9C" text style="border-radius: 2vw;" @click="addImage">
            <v-icon class="mr-2" color="#27AB9C">mdi-plus-circle</v-icon>
            <span style="color: #27AB9C; font-size: medium;">ภาพแบนเนอร์โฆษณาและข่าวสาร</span>
          </v-btn>
        </v-col>
      </v-col>
    </v-row>

    <v-row class="ma-1" v-if="defaultSetting === 'popup_video'">
      <v-col cols="12">
        <v-icon>mdi-menu-right</v-icon>
        <span>วีดิโอแบนเนอร์โฆษณาและข่าวสาร</span>
      </v-col>
      <v-col cols="12" style="position: relative; margin-top: -1vw;">
        <v-card
          class="mt-3 pa-3"
          elevation="0"
          :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
        >
          <div v-if="videoURL && videoURL.includes('youtube')" style="display: flex; justify-content: center;">
            <Youtube
              :key="videoURL"
              ref="youtube"
              :video-id="youtube_parser(videoURL)"
              @playing="playing"
              :player-vars="playerVars"
              style="width: 350px; height: 197px;">
            </Youtube>
          </div>
          <div v-else style="display: flex; justify-content: center;" class="flex-column align-center">
            <v-img
              src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
              width="280.34"
              height="154.87"
              contain
            ></v-img>
            <span class="mt-5" style="line-height: 24px; font-weight: 400;">ยังไม่ได้เพิ่มลิงก์วิดีโอ</span>
          </div>
          <v-btn
            v-if="videoURL && videoURL.includes('youtube')"
            text
            small
            fab
            style="position: absolute; top: 5px; right: 5px; z-index: 1; background-color: #f44336;"
            @click="removeVDOPopup()"
          >
            <v-icon small color="#fff">mdi-delete</v-icon>
          </v-btn>
        </v-card>
      </v-col>
    </v-row>
    <v-col :cols="MobileSize ? 12 : IpadSize ? 9 : 10" class="d-flex ml-2" v-if="defaultSetting === 'popup_video'">
      <div class="mr-2">
        <span>การจัดวาง :</span>
      </div>
      <v-radio-group v-model="layoutVideo" row>
        <v-radio style="font-size: 14px;" label="แนวตั้ง" value="vertical"></v-radio>
        <v-radio style="font-size: 14px;" label="แนวนอน" value="horizontal"></v-radio>
      </v-radio-group>
    </v-col>
    <v-row class="ma-1" v-if="defaultSetting === 'popup_video'">
      <v-col cols="12" md="12">
        <v-row dense align="center" justify="center" style="cursor: pointer;">
          <v-col cols="12">
            <span>URL ของวิดีโอ <span style="color:#FF0000">*</span></span>
          </v-col>
          <v-col cols="12">
              <v-text-field v-model="videoURL" :rules="rules.linkYoutubeCantEmpty" placeholder="URL ของวิดีโอ" dense outlined></v-text-field>
          </v-col>
        </v-row>
      </v-col>
    </v-row>

    <v-card-actions>
      <v-row class="mb-2" justify="end" dense>
        <v-btn outlined height="48" width="110" dense rounded dark color="#27AB9C" class="mr-4" @click="cancelCreateImagePopup()">ยกเลิก</v-btn>
        <v-btn height="48" width="110" color="#27AB9C" dark dense rounded class="" @click="CreateImagePopup()">บันทึก</v-btn>
      </v-row>
    </v-card-actions>

    <v-dialog v-model="dialogConfirmCreate" width="464" persistent>
      <v-card style="background: #FFFFFF; border-radius: 12px;" height="100%">
        <v-toolbar align="center" color="#C8F3E5" dark dense elevation="0">
          <span class="flex text-center" style="font-weight: 700; font-size: 18px; line-height: 24px; color: #27AB9C; padding-left: 30px;">
            แก้ไขแบนเนอร์โฆษณาและข่าวสาร
          </span>
          <v-btn icon dark @click="dialogConfirmCreate = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense justify="center">
            <v-col cols="12" align="center" class="mt-8 mb-8">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="">คุณได้ทำการแก้ไขแบนเนอร์โฆษณาและข่าวสาร</span><br/>
              <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="pt-4">และต้องการดำเนินการแก้ไขแบนเนอร์โฆษณาและข่าวสารใช่ หรือไม่?</span>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-row class="mb-2" justify="center" dense>
            <v-btn outlined height="48" width="110" dense rounded dark color="#27AB9C" class="mr-4" @click="dialogConfirmCreate = false">ยกเลิก</v-btn>
            <v-btn height="48" width="110" color="#27AB9C" dark dense rounded class="" @click="handleConfirm()">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { Youtube } from 'vue-youtube'
export default {
  components: {
    Youtube
  },
  data () {
    return {
      dialogConfirmCreate: false,
      theRedI: true,
      isActive: false,
      popupID: '',
      isSwitchOn: '',
      layout: '',
      defaultSetting: '',
      rules: {
        linkYoutubeCantEmpty: [
          v => !!v || 'กรุณากรอก URL ของวิดีโอ'
        ]
      },
      DataImagePopup: [],
      ImagePopupVertical: '',
      ImagePopupHorizontal: '',
      imageURL: '',
      videoURL: '',
      playerVars: {
        autoplay: 0,
        controls: 1
      },
      listMedia: [],
      dataGroupImage: [],
      DataImage: '',
      ImagePopupVerticalGroup: [],
      ImagePopupHorizontalGroup: [],
      layoutVideo: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminManagePopupMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'AdminManagePopup')
        this.$router.push({ path: '/AdminManagePopup' }).catch(() => {})
      }
    },
    isActive (newValue) {
      this.isSwitchOn = newValue ? 'active' : 'inactive'
      // console.log(this.isSwitchOn)
    }
  },
  mounted () {

  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    window.scrollTo(0, 0)

    this.DetailPopup()
  },
  methods: {
    async removeItem (index) {
      this.dataGroupImage.splice(index, 1)
    },
    addImage () {
      this.dataGroupImage.push({
        additional_link: '',
        layout: '',
        DataImage: ''
      })
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    onPickFileBanner (index) {
      document.getElementById(`file_input_banner${index}`).click()
    },
    async onFileSelectedBanner (index, files) {
      if (files && files.length > 0) {
        // console.log(files, index, 'onFileSelectedBanner')
        const file = files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          const image = new Image()

          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => resolve({ width: image.width, height: image.height })
            image.src = url
          })

          // ตรวจสอบขนาดตาม layout
          const isVertical = this.layout === 'vertical'
          const maxWidth = isVertical ? 420 : 1480
          const maxHeight = isVertical ? 540 : 620

          if (imageDimensions.height <= maxHeight && imageDimensions.width <= maxWidth) {
            const reader = new FileReader()
            reader.onload = async () => {
              this.base64Image = reader.result.split(',')[1]
              const Banner = {
                image: [this.base64Image],
                type: isVertical ? 'vertical' : 'horizontal',
                seller_shop_id: 'all'
              }

              this.$store.commit('openLoader')

              await this.$store.dispatch('actionsUploadToS3', Banner)
              const response = this.$store.state.ModuleShop.stateUploadToS3

              if (response.message === 'List Success.') {
                this.$store.commit('closeLoader')
                // เก็บ path แยกกันตาม layout
                if (isVertical) {
                  this.ImagePopupVertical = response.data.list_path[0].path
                  this.ImagePopupVerticalGroup[index] = response.data.list_path[0].path
                  this.dataGroupImage[index].DataImage = response.data.list_path[0].path
                  this.dataGroupImage[index].layout = 'vertical'
                } else {
                  this.ImagePopupHorizontal = response.data.list_path[0].path
                  this.ImagePopupHorizontalGroup[index] = response.data.list_path[0].path
                  this.dataGroupImage[index].DataImage = response.data.list_path[0].path
                  this.dataGroupImage[index].layout = 'horizontal'
                }
              }
              this.$forceUpdate()
            }
            reader.readAsDataURL(file)
          } else {
            this.$swal.fire({
              icon: 'warning',
              text: `โปรดใช้รูปภาพตามขนาดและสัดส่วนที่กำหนด (${maxWidth}x${maxHeight})`,
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true
            })
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ (JPEG, JPG, PNG)',
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true
          })
        }
      } else {
        console.warn('ไม่มีไฟล์ที่เลือก')
      }
    },
    youtube_parser (url) {
      // console.log('0', url)
      let ID = ''
      url = url.replace(/(>|<)/gi, '').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
      if (url[2] !== undefined) {
        ID = url[2].split(/[^0-9a-z_-]/i)
        ID = ID[0]
      } else {
        // console.log('เข้ามาทำไม')
        ID = false
      }
      // console.log(ID)
      return ID
    },
    playing () {
      // console.log('we are watching!!!')
    },
    removeImagePopup (index) {
      if (this.layout === 'vertical') {
        this.ImagePopupVertical = ''
      } else {
        this.ImagePopupHorizontal = ''
      }
      this.dataGroupImage[index].DataImage = ''
    },
    removeVDOPopup () {
      this.videoURL = ''
    },
    async cancelCreateImagePopup () {
      await this.DetailPopup()
    },
    CreateImagePopup () {
      this.dialogConfirmCreate = true
    },
    async handleConfirm () {
      if (this.defaultSetting === 'popup_image') {
        await this.confirmUpdate()
      } else if (this.defaultSetting === 'popup_video') {
        await this.confirmUpdateVDO()
      }

      this.dialogConfirmCreate = false
    },
    async confirmUpdate () {
      this.$store.commit('openLoader')
      // console.log(this.dataGroupImage)
      var prepareListMedia = this.dataGroupImage.map(e => {
        return {
          link_media: e.DataImage,
          additional_link: e.additional_link,
          layout: e.layout
        }
      })
      // console.log(prepareListMedia, 'prepareListMedia')
      const data = {
        popup_id: this.popupID || null, // null สำหรับสร้างใหม่
        status: this.isSwitchOn,
        type: 'popup_image',
        list_media: prepareListMedia
      }
      // const data = {
      //   popup_id: this.popupID || null, // null สำหรับสร้างใหม่
      //   status: this.isSwitchOn,
      //   layout: this.layout,
      //   type: this.defaultSetting,
      //   link_media: this.layout === 'vertical' ? this.ImagePopupVertical : this.ImagePopupHorizontal,
      //   additional_link: this.imageURL ? this.imageURL : ''
      // }

      // ลบ popup_id ถ้าค่าเป็น null (สร้างใหม่)
      if (!data.popup_id) {
        delete data.popup_id
      }

      await this.$store.dispatch('actionsUpdatePopUp', data)
      const res = await this.$store.state.ModuleAdminPanit.stateUpdatePopUp

      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogConfirmCreate = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: data.popup_id ? 'แก้ไขแบนเนอร์โฆษณาและข่าวสารสำเร็จ' : 'สร้างแบนเนอร์โฆษณาและข่าวสารใหม่สำเร็จ'
        })
        this.DetailPopup()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          title: 'เกิดข้อผิดพลาด',
          text: res.message || 'ไม่สามารถบันทึกข้อมูลได้',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    },
    async confirmUpdateVDO () {
      this.$store.commit('openLoader')
      // var prepareListMedia = this.dataGroupImage.map(e => {
      //   return {
      //     link_media: this.videoURL,
      //     additional_link: '',
      //     layout: ''
      //   }
      // })
      var prepareListMedia = {
        link_media: this.videoURL,
        additional_link: this.videoURL,
        layout: this.layoutVideo
      }
      var data = {
        popup_id: this.popupID || null, // null สำหรับสร้างใหม่
        status: this.isSwitchOn,
        type: 'popup_video',
        list_media: [prepareListMedia]
      }
      // var data = {
      //   popup_id: this.popupID || null, // null สำหรับสร้างใหม่
      //   status: this.isSwitchOn,
      //   layout: this.layout,
      //   type: 'popup_video',
      //   link_media: this.videoURL,
      //   additional_link: ''
      // }

      // ลบ popup_id ถ้าค่าเป็น null (สร้างใหม่)
      if (!data.popup_id) {
        delete data.popup_id
      }

      await this.$store.dispatch('actionsUpdatePopUp', data)
      var res = await this.$store.state.ModuleAdminPanit.stateUpdatePopUp

      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogConfirmCreate = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: data.popup_id ? 'แก้ไขแบนเนอร์โฆษณาและข่าวสารสำเร็จ' : 'สร้างแบนเนอร์โฆษณาและข่าวสารใหม่สำเร็จ'
        })
        this.DetailPopup()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          title: 'เกิดข้อผิดพลาด',
          text: res.message || 'ไม่สามารถบันทึกข้อมูลได้',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    },
    async DetailPopup () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPopUp')
      const res = await this.$store.state.ModuleAdminPanit.stateGetPopUp

      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')

        if (res.data) {
          const detail = res.data
          this.popupID = detail.id
          this.defaultSetting = detail.type || ''
          this.isActive = detail.status === 'active'
          this.listMedia = detail.list_media
          if (detail.type === 'popup_video') {
            this.videoURL = detail.list_media[0].link_media
            this.dataGroupImage = [{
              additional_link: '',
              layout: '',
              DataImage: ''
            }]
            this.layoutVideo = detail.list_media[0].layout
            // console.log(detail.list_media.layout, 456)
          } else {
            this.dataGroupImage = this.listMedia.map(item => ({
              ...item,
              additional_link: item.additional_link,
              layout: item.layout,
              DataImage: item.link_media
            }))
          }
          // console.log('seeee 5555', this.listMedia)
          // console.log('seeee 5555', this.dataGroupImage)
          // this.popupID = detail.id
          // this.isActive = detail.status === 'active'
          // this.layout = detail.layout || ''
          // this.defaultSetting = detail.type || ''
          // this.imageURL = detail.additional_link || ''

          // ตรวจสอบว่า link_media เป็น YouTube URL ไหม
          const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/\n\s]+\/\S+|(?:v|e(?:mbed)?)\/([\w-]+)|.*v=([\w-]+)))|youtu\.be\/([\w-]+)/
          // console.log(youtubeRegex.test(detail.list_media[0].link_media), 'boolean')
          if (youtubeRegex.test(detail.list_media[0].link_media)) {
            // ถ้าเป็นลิงก์ YouTube ให้ตั้งค่า videoURL
            this.videoURL = detail.list_media[0].link_media
            this.ImagePopupVertical = ''
            this.ImagePopupHorizontal = ''
          } else {
            // ถ้าไม่ใช่ลิงก์ YouTube ให้ถือว่าเป็น URL ของภาพ
            this.videoURL = ''
            this.ImagePopupVertical = detail.link_media || ''
            this.ImagePopupHorizontal = detail.link_media || ''
          }
        } else {
          this.popupID = null // ไม่มีข้อมูล -> สร้างใหม่
          this.isActive = ''
          this.layout = ''
          this.defaultSetting = ''
          this.ImagePopupVertical = ''
          this.ImagePopupHorizontal = ''
          this.additional_link = ''
          this.videoURL = ''
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          title: 'เกิดข้อผิดพลาด',
          text: res.message || 'ไม่สามารถดึงข้อมูลได้',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    }
  }
}
</script>

<style scoped>
.v-input--selection-controls {
  margin-top: 0;
  padding-top: 0;
}

.v-radio-group {
  margin-bottom: 0;
}
</style>
