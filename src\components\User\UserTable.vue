<template>
  <div class="ma-5">
    <!-- <v-container> -->
      <v-row class="mb-4">
        <v-col cols="12" md="6" sm="12">
          <h2>รายการผู้ใช้งาน</h2>
        </v-col>
        <v-spacer></v-spacer>
        <v-col cols="12" md="4" sm="12">
          <v-text-field
           v-model="search"
           append-icon="mdi-magnify"
           placeholder="ค้นหาผู้ใช้งาน"
           dense
           filled
           rounded
           single-line
           hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-data-table
        :headers="headers"
        :items="itemsUser"
        :search="search"
        no-results-text="ไม่พบข้อมูลที่ค้นหา"
        class="elevation-1"
        @page-count="pageCount = $event"
        :page.sync="page"
        :items-per-page="itemsPerPage"
        hide-default-footer
      >
        <template  v-slot:[`item.setting`]="{ item }">
          <v-icon v-if="item.setting === true" color="success" small>mdi-check-circle</v-icon>
          <v-icon v-else color="error" small>mdi-close-circle</v-icon>
        </template>
        <template  v-slot:[`item.admin`]="{ item }">
          <v-icon v-if="item.admin === true" color="success" small>mdi-check-circle</v-icon>
          <v-icon v-else color="error" small>mdi-close-circle</v-icon>
        </template>
        <template  v-slot:[`item.assistant`]="{ item }">
          <v-icon v-if="item.assistant === true" color="success" small>mdi-check-circle</v-icon>
          <v-icon v-else color="error" small>mdi-close-circle</v-icon>
        </template>
        <template  v-slot:[`item.appover`]="{ item }">
          <v-icon v-if="item.appover === true" color="success" small>mdi-check-circle</v-icon>
          <v-icon v-else color="error" small>mdi-close-circle</v-icon>
        </template>
        <template  v-slot:[`item.buyer`]="{ item }">
          <v-icon v-if="item.buyer === true" color="success" small>mdi-check-circle</v-icon>
          <v-icon v-else color="error" small>mdi-close-circle</v-icon>
        </template>
        <template  v-slot:[`item.verify`]="{ item }">
          <v-icon v-if="item.verify === true" color="success" small>mdi-check-circle</v-icon>
          <v-icon v-else color="error" small>mdi-close-circle</v-icon>
        </template>
        <template  v-slot:[`item.detail`]="{ item }">
          <v-chip outlined color="green" @click="goToDetail(item)">
            รายละเอียด
          </v-chip>
          <!-- <v-btn outlined dense @click="goToDetail(item)" small color="success">รายละเอียด</v-btn> -->
        </template>
        <template  v-slot:[`item.active`]="{ item }">
          <v-switch v-model="item.active" color="success" @change="ChangeStatus(item)"></v-switch>
        </template>
      </v-data-table>
      <div class="text-center pt-2">
        <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
      </div>
    <!-- </v-container> -->
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      search: '',
      headers: [
        {
          text: 'ลำดับที่',
          align: 'center',
          value: 'id'
        },
        { text: 'ชื่อ', align: 'center', value: 'name' },
        { text: 'อีเมล', align: 'center', value: 'email' },
        { text: 'การตั้งค้าผู้ซื้อ', align: 'center', value: 'setting' },
        { text: 'จำนวนแผนก', align: 'center', value: 'department' },
        { text: 'เข้าใช้งานล่าสุด', align: 'center', value: 'arrivalTime' },
        { text: 'ผู้ดูแลระบบ', align: 'center', value: 'admin' },
        { text: 'ผู้ช่วยผู้ดูแลระบบ', align: 'center', value: 'assistant' },
        { text: 'ผู้อนุมัติ', align: 'center', value: 'appover' },
        { text: 'ผู้สั่งซื้อ', align: 'center', value: 'buyer' },
        { text: 'การยืนยันอีเมล', align: 'center', value: 'verify' },
        { text: 'ข้อมูล', align: 'center', value: 'detail' },
        { text: 'สถานะ', align: 'center', value: 'active' }
      ],
      itemsUser: [
        {
          id: 1,
          name: 'นายกีฬา สั่งซื้อ',
          email: '<EMAIL>',
          setting: true,
          department: '1',
          arrivalTime: '23/02/2021',
          mobile: '0875888995',
          appoverDate: '3',
          firstnameTH: 'นายกีฬา',
          lastnameTH: 'สั่งซื้อ',
          firstnameEN: 'sport',
          lastnameEN: 'purchaser',
          admin: true,
          assistant: false,
          appover: true,
          buyer: true,
          verify: false,
          active: true
        },
        {
          id: 2,
          name: 'เก็ท เก็ท',
          email: '<EMAIL>',
          setting: false,
          department: '-',
          arrivalTime: '23/02/2021',
          mobile: '0998579458',
          appoverDate: '3',
          firstnameTH: 'เก็ท',
          lastnameTH: 'เก็ท',
          firstnameEN: 'Get',
          lastnameEN: 'Get',
          admin: false,
          assistant: true,
          appover: true,
          buyer: false,
          verify: false,
          active: true
        }
      ],
      pageCount: 5,
      page: 1,
      itemsPerPage: 5
    }
  },
  methods: {
    goToDetail (val) {
      localStorage.setItem('DetailUser', Encode.encode(val))
      this.$router.push('/detailuser').catch(() => {})
      // console.log(val)
    },
    ChangeStatus (val) {
      this.$swal.fire({
        title: 'คุณต้องการที่จะเปลี่ยนสิทธิ์ผู้ใช้คนนี้หรือไม่',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ต้องการ',
        cancelButtonText: 'ไม่ต้องการ'
      }).then((result) => {
        if (result.isConfirmed) {
          this.itemsUser.active = !val
        } else {
          this.itemsUser.active = val
        }
      })
    }
  }
}
</script>
