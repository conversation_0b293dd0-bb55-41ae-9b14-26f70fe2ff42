<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row dense :class="MobileSize ? 'mx-2 my-2' : 'mx-1'">
        <v-col cols="12" class="d-flex">
          <span :style="MobileSize ? 'font-size: 18px; ' : IpadSize ? 'font-size: 18px;' : 'font-size: 24px; '" class="mr-auto" style="font-weight: 700; color: #333333;">
            <v-icon color="#27AB9C" class="mr-2" @click="backtoPOBuyer()">mdi-chevron-left</v-icon> รายละเอียดการสั่งซื้อสินค้า
          </span>
          <v-spacer></v-spacer>
          <div v-if="!MobileSize && !IpadSize">
            <!-- ต้องดักอีกหนึ่ง status จากเส้นจิม ให้ status === '' || status === 'reject' -->
            <!-- <v-btn v-if="(items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received') && items.transaction_status === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4" @click="changeStatusOrder('cancel', items)">ยกเลิกคำสั่งซื้อ</v-btn> -->
            <!-- <v-btn v-if="(items.detail_status === 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Cancel') && items.transaction_status === 'Success' && items.transportation_status === 'ส่งคืนสินค้า'" @click="goToRefundPage()" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4">รายการคืนสินค้า</v-btn> -->
            <!-- ต้องดักอีกหนึ่ง status จากเส้นจิม status === waiting -->
             <!-- ปุ่มนี้นะโวคด้านล่างคอมเมนต์นี้ ถ้าจะเปิด -->
            <!-- <v-btn v-if="(items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received') && items.transaction_status === 'Waiting_Cancel' && (statusCancel !== '' && statusCancel !== 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" rounded height="40" @click="getDataSubmitOrder(items)" width="150" color="#27AB9C" outlined class="mr-4">ยอมรับยกเลิกคำสั่งซื้อ</v-btn> -->
            <!-- <v-btn v-if="items.company_id !== '-1' && items.sale_order === 'no' && items.shop_approve === 'waiting_approve'" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4" @click="changeStatusOrder('reject', items)">ปฏิเสธคำสั่งซื้อ</v-btn> -->
            <!-- <v-btn v-if="items.company_id !== '-1' && items.sale_order === 'no' && items.shop_approve === 'waiting_approve'" rounded height="40" width="135" color="#27AB9C" class="white--text" @click="changeStatusOrder('approve', items)">ยอมรับคำสั่งซื้อ</v-btn> -->
            <!-- <v-btn v-if="items.type_shipping === 'front' && items.tracking[0].status_tracking === 'Not Sent' && items.is_lotus === 'N' && items.transaction_status !== 'Waiting_Cancel'" rounded height="40" width="113" color="#27AB9C" class="white--text" @click="changeStatusOrder('accepted', items)">เข้ารับสินค้า</v-btn> -->
            <v-btn v-if="items.is_lotus === 'Y' && items.transaction_status === 'Pending'" rounded height="40" color="#27AB9C" class="white--text" @click="ApproveSlipModal(items)">ยืนยันสลิป</v-btn>
            <!-- <v-btn v-if="items.product_type !== 'service' && items.type_shipping === 'online' && items.tracking[0].status_tracking === 'Not Sent' && items.transportation_type === '-' && (items.transaction_status !== 'Not Paid' && items.transaction_status !== 'Fail')" rounded height="40" width="xc 113" color="#27AB9C" class="white--text" @click="addOwnTransportation()">จัดส่งสินค้า</v-btn> -->
          </div>
        </v-col>
        <v-col cols="12" class="d-flex justify-end" v-if="IpadSize">
          <!-- <v-btn v-if="(items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received') && items.transaction_status === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4" @click="changeStatusOrder('cancel', items)">ยกเลิกคำสั่งซื้อ</v-btn> -->
          <!-- ต้องดักอีกหนึ่ง status จากเส้นจิม -->
          <!-- <v-btn v-if="(items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received') && items.transaction_status === 'Waiting_Cancel' && (statusCancel !== '' && statusCancel !== 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" rounded height="40" @click="getDataSubmitOrder(items)" width="150" color="#27AB9C" outlined class="mr-4">ยอมรับยกเลิกคำสั่งซื้อ</v-btn> -->
          <!-- <v-btn v-if="items.company_id !== '-1' && items.sale_order === 'no' && items.shop_approve === 'waiting_approve'" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4" @click="changeStatusOrder('reject', items)">ปฏิเสธคำสั่งซื้อ</v-btn> -->
          <!-- <v-btn v-if="items.company_id !== '-1' && items.sale_order === 'no' && items.shop_approve === 'waiting_approve'" rounded height="40" width="135" color="#27AB9C" class="white--text" @click="changeStatusOrder('approve', items)">ยอมรับคำสั่งซื้อ</v-btn> -->
          <!-- <v-btn v-if="items.type_shipping === 'front' && items.tracking[0].status_tracking === 'Not Sent' && items.is_lotus === 'N' && items.transaction_status !== 'Waiting_Cancel'" rounded height="40" width="113" color="#27AB9C" class="white--text" @click="changeStatusOrder('accepted', items)">เข้ารับสินค้า</v-btn> -->
          <v-btn v-if="items.is_lotus === 'Y' && items.transaction_status === 'Pending'" rounded height="40" color="#27AB9C" class="white--text" @click="ApproveSlipModal(items)">ยืนยันสลิป</v-btn>
          <!-- <v-btn v-if="items.product_type !== 'service' && items.type_shipping === 'online' && items.tracking[0].status_tracking === 'Not Sent' && items.transportation_type === '-' && (items.transaction_status !== 'Not Paid' && items.transaction_status !== 'Fail')" rounded height="40" width="113" color="#27AB9C" class="white--text" @click="addOwnTransportation()">จัดส่งสินค้า</v-btn> -->
        </v-col>
        <v-col cols="12" class="d-flex justify-end" v-if="MobileSize">
          <!-- <v-btn v-if="(items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received') && items.transaction_status === 'Success' && (statusCancel === '' || statusCancel === 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4" @click="changeStatusOrder('cancel', items)">ยกเลิกคำสั่งซื้อ</v-btn> -->
          <!-- ดักอีกหนึ่ง status จากเส้นจิม -->
          <!-- <v-btn v-if="(items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received') && items.transaction_status === 'Waiting_Cancel' && (statusCancel !== '' && statusCancel !== 'reject') && items.order_mobilyst_no === '-' && items.is_lotus === 'N'" rounded height="40" @click="getDataSubmitOrder(items)" small width="135" color="#27AB9C" outlined>ยอมรับยกเลิกคำสั่งซื้อ</v-btn> -->
          <!-- <v-btn v-if="items.company_id !== '-1' && items.sale_order === 'no' && items.shop_approve === 'waiting_approve'" rounded height="40" width="135" color="#27AB9C" outlined class="mr-4" @click="changeStatusOrder('reject', items)">ปฏิเสธคำสั่งซื้อ</v-btn> -->
          <!-- <v-btn v-if="items.company_id !== '-1' && items.sale_order === 'no' && items.shop_approve === 'waiting_approve'" rounded height="40" width="135" color="#27AB9C" class="white--text" @click="changeStatusOrder('approve', items)">ยอมรับคำสั่งซื้อ</v-btn> -->
          <!-- <v-btn v-if="items.type_shipping === 'front' && items.tracking[0].status_tracking === 'Not Sent' && items.is_lotus === 'N' && items.transaction_status !== 'Waiting_Cancel'" rounded height="40" width="113" color="#27AB9C" class="white--text" @click="changeStatusOrder('accepted', items)">เข้ารับสินค้า</v-btn> -->
          <v-btn v-if="items.is_lotus === 'Y' && items.transaction_status === 'Pending'" rounded height="40" color="#27AB9C" class="white--text" @click="ApproveSlipModal(items)">ยืนยันสลิป</v-btn>
          <!-- <v-btn v-if="items.product_type !== 'service' && items.type_shipping === 'online' && items.tracking[0].status_tracking === 'Not Sent' && items.transportation_type === '-' && (items.transaction_status !== 'Not Paid' && items.transaction_status !== 'Fail')" rounded height="40" width="xc 113" color="#27AB9C" class="white--text" @click="addOwnTransportation()">จัดส่งสินค้า</v-btn> -->
        </v-col>
        <!-- ข้อมูลรายละเอียด -->
        <v-col cols="12" class="pt-6">
          <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px; background: #F9FAFD;">
            <v-card-text>
              <v-row dense>
                <!-- ข้อมูลส่วนแรก -->
                <v-col cols="12" md="6" sm="12">
                  <v-row>
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">รหัสการสั่งซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.payment_transaction }}</span>
                    </v-col>
                    <!-- เพิ่มมา รอต่อ api -->
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">รหัสอ้างอิงการสั่งซื้อ : </span>
                      <span v-if="items.pay_type !== 'recurring'" style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.payment_transaction }}</span>
                      <span v-if="items.pay_type === 'recurring'" style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].payment_credit_term_number }}</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ผู้ซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" >{{ items.buyer_name === '' ? '-' : items.buyer_name }}</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วันที่ทำรายการ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ dateCreateOrderStep1 }} น.</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วันที่ชำระเงิน : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-if="items.receipt[0].updated_at !== ''">{{ new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', {year: 'numeric',month: 'long',day: 'numeric',hour: 'numeric',minute: 'numeric' }) }} น.</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>-</span>
                      <!-- เพิ่มมา รอต่อ api -->
                       <!-- เปิดกลับมาใช้ -->
                      <!-- ต้องspan style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.receipt[0].updated_at !== '' ? new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ใบกำกับภาษี : </span>
                        <a v-if="items.transaction_code !== '-' && items.required_invoice !== '-'" @click="GetETax(items)">
                          <span style="font-size: 16px; font-weight: 400; color: #1B5DD6; border-bottom: 1px solid;">{{items.payment_transaction}}</span>
                        </a>
                        <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else-if="items.transaction_code === '-' && items.required_invoice !== '-'">{{ items.required_invoice }}</span>
                        <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>{{ '-' }}</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" v-if="items.invoice_path === '-'">{{ items.invoice_path }}</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>{{ items.invoice_path }}</span> -->
                    </v-col>
                    <!-- เพิ่มมา รอต่อ api -->
                    <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ใบกำกับภาษี : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-if="items.transaction_code !== '-' && items.required_invoice !== '-'" @click="GetETax(items.transaction_code)">{{ items.payment_transaction }}</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>-</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : ''">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">หมายเหตุถึงร้านค้า : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-if="items.remark_to_shop !== '-' && items.remark_to_shop !== null">{{ items.remark_to_shop }}</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>-</span>
                      <!-- <v-col cols="12">
                        <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-if="items.remark_to_shop !== '-' && items.remark_to_shop !== null">{{ items.remark_to_shop }}</span>
                        <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>-</span>
                      </v-col> -->
                    </v-col>
                  </v-row>
                </v-col>
                <!-- ข้อมูลส่วนสอง -->
                <v-col cols="12" md="6" sm="12" style="display: flex;" :class="MobileSize ? 'px-0' : ''">
                  <v-row dense style="margin: auto;">
                    <v-col cols="12" class="d-flex flex-column">
                      <v-card elevation="0" class="ml-auto" :width="!IpadSize && !MobileSize ? '316' : '100%'" height="100%" style="border-radius: 8px; background: #FFFFFF;">
                        <v-card-text>
                          <v-row dense>
                            <v-col cols="12">
                              <v-row>
                                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">สถานะคำสั่งซื้อ : </span>
                                  <!-- <span style="font-size: 16px; font-weight: 400; color: #333333;"><v-chip text-color="#27AB9C" color="#DAF1E9">{{ items.transaction_status }}</v-chip></span> -->
                                  <span v-if="items.pay_type !== 'recurring'">
                                    <span v-if="items.shop_approve !== 'waiting_approve' && items.shop_approve !== 'reject'">
                                      <span v-if="items.type_shipping === 'front' && items.transportation_status === '-' && items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ได้รับสินค้าแล้ว</span>
                                      <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.transportation_status === '-'" style="font-size: 16px; font-weight: 400;" :class="MobileSize ? 'ml-auto' : ''" :style="{ 'color' : getTextColor(items.transaction_status) }"><v-icon :color="getTextColor(items.transaction_status)">mdi-circle-medium</v-icon>{{ getStatus(items.transaction_status) }}</span>
                                      <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.transportation_status === 'การจัดส่งสำเร็จ'" style="font-size: 16px; font-weight: 400; color: #16D2A5;" :class="MobileSize ? 'ml-auto' : ''"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ได้รับสินค้าแล้ว</span>
                                      <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.transportation_status !== 'การจัดส่งสำเร็จ'" style="font-size: 16px; font-weight: 400; color: #16D2A5;" :class="MobileSize ? 'ml-auto' : ''" :style="{ 'color' : getTextColorTransportation(items.transportation_status) }"><v-icon :color="getTextColorTransportation(items.transportation_status)">mdi-circle-medium</v-icon>{{ items.transportation_status }}</span>
                                      <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.transportation_status !== 'การจัดส่งสำเร็จ'" style="font-size: 16px; font-weight: 400; color: #16D2A5;" :class="MobileSize ? 'ml-auto' : ''" :style="{ 'color' : getTextColorTransportation(items.transportation_status) }"><v-icon :color="getTextColorTransportation(items.transportation_status)">mdi-circle-medium</v-icon>{{ items.transportation_status }}</span>
                                      <span v-else style="font-size: 16px; font-weight: 400; color: #636363;"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ items.detail_status }}
                                        <v-tooltip bottom v-if="items.detail_status === 'ยกเลิกคำสั่งซื้อ' && statusCancel === '' && reasonRefund !== ''">
                                          <template v-slot:activator="{ on, attrs }">
                                            <v-icon
                                              color="#636363"
                                              v-bind="attrs"
                                              v-on="on"
                                            >
                                              mdi-information-outline
                                            </v-icon>
                                          </template>
                                          <span>{{reasonRefund}}</span>
                                        </v-tooltip>
                                      </span>
                                      <!-- <v-row no-gutters v-if="items.detail_status === 'ยกเลิกคำสั่งซื้อ' && statusCancel === '' && reasonRefund !== ''">
                                        <v-tooltip bottom>
                                          <template v-slot:activator="{ on, attrs }">
                                            <v-icon
                                              color="#636363"
                                              v-bind="attrs"
                                              v-on="on"
                                            >
                                              mdi-information-outline
                                            </v-icon>
                                          </template>
                                          <span>{{reasonRefund}}</span>
                                        </v-tooltip>
                                      </v-row> -->
                                    </span>
                                    <span v-else>
                                      <span v-if="items.shop_approve === 'waiting_approve'" style="font-size: 16px; font-weight: 400; color: #FAAD14;" :class="MobileSize ? 'ml-auto' : ''"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รออนุมัติคำสั่งซื้อ</span>
                                      <span v-else style="font-size: 16px; font-weight: 400; color: #D1392B;" :class="MobileSize ? 'ml-auto' : ''"><v-icon color="#D1392B">mdi-circle-medium</v-icon>ปฏิเสธคำสั่งซื้อ</span>
                                    </span>
                                  </span>
                                  <span v-if="items.pay_type === 'recurring'">
                                    <span v-if="items.detail_credit_term[0].transaction_status === 'Not Paid'" style="font-size: 16px; font-weight: 400; color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รอชำระเงิน</span>
                                    <span v-if="items.detail_credit_term[0].transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #52C41A;"><v-icon color="#52C41A">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
                                  </span>
                                </v-col>
                                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''" v-if="items.type_shipping !== 'front' && (items.pay_type === 'general' || items.product_type === 'general')">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วัน-เวลาส่งสินค้า : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ (dateCreateOrderStep3 === 'Invalid Date' || dateCreateOrderStep3 === '') ? '-' : dateCreateOrderStep3  }}</span>
                                </v-col>
                                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''" v-if="items.type_shipping !== 'front' && (items.pay_type === 'general' || items.product_type === 'general')">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วัน-เวลารับสินค้า : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ dateCreateOrderStep4 === 'Invalid Date' ? '-' : dateCreateOrderStep4  }}</span>
                                </v-col>
                                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''" v-if="items.type_shipping === 'front' && (items.pay_type === 'general' || items.product_type === 'general')">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วัน-เวลารับสินค้า : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{  items.received_date !== '' && items.received_date !== '-' ? new Date(items.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) +  'น.' : '-' }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                      <v-card elevation="0" class="ml-auto mt-4" :width="!IpadSize && !MobileSize ? '316' : '100%'" height="100%" style="border-radius: 8px; background: #FFFFFF;">
                        <v-card-text>
                          <v-row dense>
                            <!-- เพิ่มมา รอต่อ api -->
                            <v-col cols="12">
                              <v-row>
                                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วันกำหนดส่งชำระ : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-if="items.detail_credit_term[0].due_date !== '-'">{{ new Date(items.detail_credit_term[0].due_date).toLocaleDateString('th-TH', {year: 'numeric',month: 'long',day: 'numeric' }) }}</span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>-</span>
                                </v-col>
                                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">จำนวนวันที่เกินกำหนดชำระ : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-if="items.detail_credit_term[0].overdue !== '-'">{{ items.detail_credit_term[0].overdue }} วัน</span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''" v-else>-</span>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <!-- รายละเอียดเอกสาร -->
        <v-col cols="12" class="pt-6" sm="12">
          <v-row dense>
            <v-col cols="6" sm="6" align="start">
              <v-row dense class="pt-2">
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/doc.png')" max-height="24" max-width="24"></v-img>
                <span :style="MobileSize ? 'font-size: 16px; font-weight: 700; color: #333333;': 'font-size: 18px; font-weight: 700; color: #333333;'" class="pl-3">
                  รายละเอียดเอกสาร
                </span>
              </v-row>
            </v-col>
            <v-col cols="6" sm="6" align="end">
              <v-btn :style="MobileSize ? 'font-size: 12px !important; font-weight: 700;': 'font-size: 16px; font-weight: 700;'" outlined color="#27AB9C" rounded :width="MobileSize? '100%':'225'" :height="MobileSize? '30':'40'" @click="dialogUploadPDF = true"><v-icon left :size=" MobileSize? '18': '24'">mdi-file-plus-outline</v-icon>เพิ่มรายละเอียดเอกสาร</v-btn>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="6" sm="12">
              <v-row class="pt-6">
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เลขที่ใบเสนอราคา : </span>
                  <a v-if="items.payment_transaction !== '' && QTorder !== '-' && items.transaction_status === 'Success'" :href="QTorder" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;" :class="MobileSize ? 'ml-auto' : ''"> {{items.payment_transaction}}</a>
                  <span v-else-if="items.payment_transaction !== '' && QTorder !== '-' && items.transaction_status !== 'Success'" style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''"> {{items.payment_transaction}}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">-</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เลขที่ใบสั่งซื้อ (PO) : </span>
                  <a :class="MobileSize ? 'ml-auto' : ''" v-if="((documents.PoNumber !== '' && documents.PoNumber !== null) || items.po_document_id !== '-') && (poPDF !== '-' || items.PO_External !== '-')" :href="poPDF !== '-' ? poPDF : items.PO_External" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{(documents.PoNumber !== '' && documents.PoNumber !== null)? documents.PoNumber : items.po_document_id}}</a>
                  <span :class="MobileSize ? 'ml-auto' : ''" v-else-if="((documents.PoNumber !== '' && documents.PoNumber !== null) || items.po_document_id !== '-') && (poPDF === '-' || items.PO_External === '-')"> {{ (documents.PoNumber !== '' && documents.PoNumber !== null) ? documents.PoNumber : items.po_document_id }}</span>
                  <span :class="MobileSize ? 'ml-auto' : ''" v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เลขที่ใบเสร็จ : </span>
                  <span :class="MobileSize ? 'ml-auto' : ''" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.receipt_number }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="12">
              <v-row class="pt-6">
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เลขที่ใบขอซื้อ (PR) : </span>
                  <a v-if="((documents.PrNumber !== '' && documents.PrNumber !== null) || items.pr_document_id !== '-') && (prPDF !== '-' || items.PR_External !== '-')" :href="prPDF !== '-' ? prPDF : items.PR_External" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{ (documents.PrNumber !== '' && documents.PrNumber !== null) ? documents.PrNumber : items.pr_document_id }}</a>
                  <span v-else-if="((documents.PrNumber !== '' && documents.PrNumber !== null) || items.pr_document_id !== '-') && (prPDF === '-' || items.PR_External === '-')"> {{ (documents.PrNumber !== '' && documents.PrNumber !== null) ? documents.PrNumber : items.pr_document_id }}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? 'd-flex' : ''">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">เลขที่ Sale Order : </span>
                  <a v-if="((documents.SoNumber !== '' && documents.SoNumber !== null) || items.ref_callback_so_id !== '-' || items.so_document_id !== '-') && soPDF !== '-'" :href="soPDF" target="_blank" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;"> {{ (documents.SoNumber !== '' && documents.SoNumber !== null) ? documents.SoNumber : items.ref_callback_so_id !== '-' ? items.ref_callback_so_id : items.so_document_id !== '-' ? items.so_document_id : documents.SoNumber }}</a>
                  <span v-else-if="((documents.SoNumber !== '' && documents.SoNumber !== null) || items.ref_callback_so_id !== '-' || items.so_document_id !== '-') && soPDF === '-'"> {{ (documents.SoNumber !== '' && documents.SoNumber !== null) ? documents.SoNumber : items.ref_callback_so_id !== '-' ? items.ref_callback_so_id : items.so_document_id !== '-' ? items.so_document_id : documents.SoNumber }}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- <v-row dense v-else>
            <v-col cols="12" md="12" sm="12">
              <v-row class="pt-6">
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row> -->
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- ที่อยู่ในการจัดส่งสินค้า -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/map.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3 pt-1">
              {{ items.type_shipping === 'front' ? 'ที่อยู่ในการรับสินค้า' : 'ที่อยู่ในการจัดส่งสินค้า' }}
            </span>
            <v-chip v-if="items.type_shipping === 'front'" color="#ADDFFF" text-color="#0059FF" class="ml-2">รับสินค้าหน้าร้าน</v-chip>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12" v-if="items.type_shipping === 'online' && (items.pay_type === 'general' || items.product_type === 'general')">
              <v-row class="pt-6">
                <v-col cols="12">
                  <!-- เพิ่มมา รอต่อ api -->
                  <div v-if="items.buyer_name !== '' &&  items.address_data_v2.length === 0"><span style="font-size: 16px; font-weight: 600; color: #333333;">{{ items.buyer_name === '' ? '-' : items.buyer_name }} </span><span style="color: #ebebeb;">|</span><span style="font-size: 16px; font-weight: 600; color: #333333;"> {{ items.address_data_v2.length === 0 ? '-' : items.address_data_v2[0].phone }}</span><br></div>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span><br/>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.address_data }}</span><br/>
                  <span v-if="items.note_address !== '' && items.note_address !== undefined && items.note_address !== null" style="font-size: 16px; font-weight: 400;"><b>หมายเหตุ :</b> {{ items.note_address }}</span>
                </v-col>
              </v-row>
              <v-row dense class="pl-1 pt-3" v-if="items.transportation_type !== '-'">
                <v-col cols="12" md="6" sm="12">
                  <v-row dense>
                    <span style="font-size: 16px; font-weight: 600;">รูปแบบการจัดส่ง : </span>
                    <span style="font-size: 16px; font-weight: 400;" class="pl-2">{{items.transportation_type }}</span>
                  </v-row>
                </v-col>
                <v-col cols="12" md="6" sm="12">
                  <v-row dense>
                    <span style="font-size: 16px; font-weight: 600;">Tracking Number : </span>
                    <div v-if="items.list_tracking.length !== 0" class="d-flex flex-wrap">
                      <div v-for="(item, index) in items.list_tracking" :key="index">
                        <span v-if="item.url_tracking !== '-' && item.url_tracking !== ''" :style="item.url_tracking !== '-' && item.url_tracking !== '' ? 'text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C !important; cursor: pointer;' : 'font-size: 16px; font-weight: 400;'" class="pl-2" @click="linkToURLTracking(item.url_tracking)">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                        <a id="urlTracking" :href="urlTracking" target="_blank" style="display: none;"></a>
                        <div @click="copyClipboard()" v-if="item.url_tracking === ''" style="cursor: pointer;" class="pl-2">
                          <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C;">{{ item.tracking_no + `${index === items.list_tracking.length - 1 ? '' : ','}` }}</span>
                          <input type="text" :value="item.tracking_no" id="trackingNumber" style="display: none;">
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <span style="font-size: 16px; font-weight: 400;" class="pl-2"> - </span>
                    </div>
                    <!-- <input type="text" :value="items.order_mobilyst_no" id="trackingNumber" style="display: none;">
                    <a :href="items.url_tracking" v-if="items.order_mobilyst_no !== '-' && (items.url_tracking !== '-' && items.url_tracking !== '')" style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6 !important;" class="pl-2" target="_blank">ติดตามสถานะจัดส่ง</a>
                    <div @click="copyClipboard()" v-if="(items.order_mobilyst_no !== '-' && items.url_tracking === '')" style="cursor: pointer;" class="pl-2">
                      <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #1B5DD6;">คัดลอก</span>
                    </div> -->
                  </v-row>
                </v-col>
              </v-row>
              <v-row dense class="pl-1 pt-3" v-if="items.order_mobilyst_no !== '-' && items.url_barcode_picture !== ''">
                <v-col cols="12" md="6" sm="12">
                  <v-row dense>
                    <span style="font-size: 16px; font-weight: 600;" class="pt-2">ดาวน์โหลด QR Code : </span>
                    <v-btn text color="#27AB9C" @click="getQrCode(items)"><v-icon class="pr-2">mdi-download-circle-outline</v-icon> <span style="text-decoration: underline;">ดาวน์โหลด QR Code</span></v-btn>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12" v-else>
              <v-row class="pt-6">
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- ที่อยู่ในการจัดส่งใบกำกับภาษี -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/location.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              ที่อยู่ในการจัดส่งใบกำกับภาษี
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12">
              <v-row class="pt-2">
                <v-col cols="12">
                  <!-- เพิ่มมา รอต่อ api -->
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" v-if="items.invoice_detail.length !== 0">{{ items.invoice_detail[0].name }}</span><br>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-if="items.invoice_address !== ''">{{ items.invoice_address }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- รายการสั่งซื้อสินค้า -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/shopping.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              รายการสั่งซื้อสินค้า
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12">
              <v-row class="pt-6" :class="MobileSize ? 'px-2' : ''">
                <v-col cols="12" style="padding: 8px 16px; border-radius: 4px; background: #DAF1E9;" v-if="MobileSize">
                  <span style="color: #27AB9C; font-weight: 600; font-size: 14px; line-height: 19px;">รายละเอียดสินค้า</span>
                </v-col>
                <v-col cols="12" v-for="(order, index) in items.data_list" :key="index" :class="MobileSize ? 'pt-0 px-0' : ''">
                  <v-data-table
                  :headers="MobileSize ? headerMobile : header"
                  :items="order.product_list"
                  style="width: 100%"
                  :class="MobileSize ? '' : 'row-height-64'"
                  :items-per-page="100"
                  :hide-default-header="MobileSize ? true : false"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row dense>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''" max-height="48" max-width="48"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0" style="font-size: 14px; font-weight: 400; color: #333333;">{{ item.product_name }}</p>
                          <span v-if="item.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{item.product_attribute_detail.key_1_value}}: <b>{{item.product_attribute_detail.attribute_priority_1}}</b></span>
                          <span v-if="item.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{item.product_attribute_detail.key_2_value}}: <b>{{item.product_attribute_detail.attribute_priority_2}}</b></span>
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ item.vat_default === 'yes' ? Number(parseFloat(item.revenue_default) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) :  Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <!-- <template v-slot:[`item.revenue_amount`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template> -->
                    <template v-slot:[`item.productdetailsMobile`]="{ item }">
                      <v-row class="d-flex px-0">
                        <v-col class="ml-auto" style="max-width: 100px; margin: auto;">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''" width="100" height="100" max-width="80" max-height="80"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" width="80" height="80" v-else/>
                        </v-col>
                        <v-col class="mlauto" align="start">
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">รหัส SKU :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.sku }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">{{ item.product_name }}</span><br/>
                          <span v-if="item.product_attribute_detail.attribute_priority_1" class="mb-0" style="font-size: 14px; color: #333333; font-weight: 500;">{{item.product_attribute_detail.key_1_value}}: <b>{{item.product_attribute_detail.attribute_priority_1}}</b></span><br/>
                          <span v-if="item.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0" style="font-size: 14px; color: #333333; font-weight: 500;">{{item.product_attribute_detail.key_2_value}}: <b>{{item.product_attribute_detail.attribute_priority_2}}</b></span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">ราคาต่อชิ้น :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.vat_default === 'yes' ? Number(parseFloat(item.revenue_default) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) :  Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">จำนวน :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.quantity }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">ราคารวม :</span><span style="font-size: 14px; color: #333333; font-weight: 500;">{{ Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                          <!-- <span>Amount :</span><span>{{ Number(item.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/> -->
                        </v-col>
                      </v-row>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="12" class="pt-4" v-if="items.product_free !== null && items.product_free.length !== 0">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" >สินค้าแถม</span>
                  <v-data-table
                  :headers="MobileSize ? headerMobile : header"
                  :items="items.product_free"
                  style="width: 100%"
                  :class="MobileSize ? '' : 'row-height-64'"
                  :items-per-page="100"
                  :hide-default-header="MobileSize ? true : false"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row dense>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== '' && item.product_image !== null" max-height="48" max-width="48"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0" style="font-size: 14px; font-weight: 400; color: #333333;">{{ item.product_name }}</p>
                          <!-- <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
                          <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span> -->
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.revenue_default`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.revenue_amount`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.productdetailsMobile`]="{ item }">
                      <v-row class="d-flex px-0">
                        <v-col class="ml-auto" style="max-width: 100px; margin: auto;">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== '' && item.product_image !== null" width="100" height="100" max-width="80" max-height="80"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" width="80" height="80" v-else/>
                        </v-col>
                        <v-col class="mlauto" align="start">
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">รหัส SKU :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.sku }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">{{ item.product_name }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">ราคาต่อชิ้น :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">จำนวน :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.quantity }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">ราคารวม :</span><span style="font-size: 14px; color: #333333; font-weight: 500;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                        </v-col>
                      </v-row>
                    </template>
                  </v-data-table>
                </v-col>
                <v-col cols="12" class="pt-4 py-0" v-if="items.point !== null || (items.coupon.length !== 0 && items.coupon !== null)">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" class="pl-3">โปรโมชันและคูปองส่วนลด</span>
                  <v-card class="pt-4" style="background: #FFFFFF; border-radius: 4px;" min-height="100%" elevation="0">
                    <v-toolbar align="start" color="#E6F5F3" dark dense elevation="0" v-if="items.point !== null">
                      <span class="" style="font-size:16px; font-weight:500; color: #333333;">
                        <font>ส่วนลดจากแต้ม {{items.point === null ? 0 : items.point}} บาท</font>
                      </span>
                    </v-toolbar>
                    <v-container class="px-0" v-if="items.coupon !== null && items.coupon.length !== 0">
                      <v-card-text class="pa-0">
                        <v-row :class=" MobileSize? 'pa-1 pt-1':'pt-6'">
                          <v-col cols="12" md="4" sm="6" v-for="(item, index) in items.coupon" :key="index" class="pa-1">
                            <v-col :class="!MobileSize && !IpadSize ? 'couponIMGDesk': MobileSize? 'couponIMGMobile py-0': 'couponIMG'">
                              <v-col cols="12" md="12" class="" :class="MobileSize? 'pa-1': IpadSize? 'pl-3': IpadProSize? 'pl-0': 'pl-0'">
                                <v-row no-gutters>
                                  <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                                    <v-row no-gutters>
                                      <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start">
                                        <span style="color: #27AB9C; font-size: 14px; font-weight: 600;">{{item.coupon_name | truncate(15, '...')}}</span><br>
                                        <span style="font-size: 12px;"> ขั้นต่ำ {{item.spend_minimum}} บาท</span><br>
                                      </v-col>
                                      <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                        <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? 'โค้ดส่งฟรี' : 'ส่วนลด'}}</span><br>
                                        <span v-if="item.coupon_type === 'free_product'" style="color: #F56E22; font-size: 22px; font-weight: 600;"> แถมฟรี</span><br>
                                        <span v-if="item.coupon_type !== 'free_product'" style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%':'บาท'}}</span><br>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-col>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-container>
                  </v-card>
                </v-col>
              </v-row>
              <v-row class="pt-6">
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ภาษีมูลค่าเพิ่ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคารวมภาษีมูลค่าเพิ่ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto ml-3" style="font-size: 16px; font-weight: 400; color: red;">ส่วนลดคูปอง(ร้านค้า)</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ Number(items.total_coupon_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto ml-3" style="font-size: 16px; font-weight: 400; color: red;">ส่วนลดคูปอง(ระบบ)</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ Number(items.total_coupon_platform_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto ml-3" style="font-size: 16px; font-weight: 400; color: red;">ส่วนลดแต้ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ Number(items.total_point).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคาหลังหักส่วนลด</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_price_after_all_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ค่าจัดส่ง</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.shipping_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto ml-3" style="font-size: 16px; font-weight: 400; color: red;">ส่วนลดค่าจัดส่ง(ร้านค้า)</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ Number(items.total_coupon_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto ml-3" style="font-size: 16px; font-weight: 400; color: red;">ส่วนลดค่าจัดส่ง(ระบบ)</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: red;">- {{ Number(items.total_coupon_platform_shipping_discount_v2).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <!-- <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ส่วนลดคูปอง</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_coupon_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ส่วนลดคูปองระบบ</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_coupon_platform_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคารวมส่วนลด</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ภาษีมูลค่าเพิ่ม <span style="font-size: 16px; font-weight: 400; color: #A1A1A1;">(7%)</span></span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคารวมภาษีมูลค่าเพิ่ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex pb-0">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ค่าจัดส่ง</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex pt-0">
                  <span class="mr-auto" style="font-size: 10px; font-weight: 400; color: #A1A1A1;">
                      ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป<br/>
                      ขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ
                  </span>
                </v-col> -->
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 700; color: #333333;" :class="MobileSize ? 'pt-2' : 'pt-2'">ราคารวมทั้งหมด</span>
                  <span class="ml-auto" style="font-size: 24px; font-weight: 700; color: #27AB9C;">{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} <span style="font-size: 20px; font-weight: 700; color: #333333;">บาท</span></span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6" v-if="items.pay_type !== 'general'">
          <v-divider></v-divider>
        </v-col>
        <!-- รายละเอียดรายการสั่งซื้อ -->
        <!-- <v-col cols="12" class="pt-6" v-if="items.pay_type !== 'general'">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/inventory.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              รายละเอียดรายการสั่งซื้อ
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12" class="pt-6">
              <v-row>
                <v-col cols="12" md="4" sm="6" :style="MobileSize ? 'margin-bottom: -10px;' : ''">
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">วันที่เริ่มสัญญา : </span><span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.start_date_contract !== '' && items.start_date_contract !== null ? new Date(items.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '-' }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6" :style="MobileSize ? 'margin-bottom: -10px;' : ''">
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">วันที่สิ้นสุดสัญญา : </span><span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; color: #333333;">{{ items.end_date_contract !== '' && items.end_date_contract !== null ? new Date(items.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '-' }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">Pay Type : </span>
                  <v-chip v-if="items.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                  <v-chip v-else-if="items.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" v-else>-</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <v-row>
                <v-col cols="12" md="4" sm="6" :style="MobileSize ? 'margin-bottom: -10px;' : ''">
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">ส่วนลด : </span><span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; color: #333333;">{{ items.discount_amount }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6" :style="MobileSize ? 'margin-bottom: -10px;' : ''">
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">หมวดงบประมาณ : </span><span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; color: #333333;">{{ items.type_budget !== null ? items.type_budget : '-' }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6" :style="MobileSize ? 'margin-bottom: 6px;' : ''">
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">หมวดตัดงบ : </span><span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; color: #333333;">{{ items.budget_cut !== null ? items.budget_cut : '-' }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <v-row>
                <v-col cols="12" md="6" sm="6" :style="MobileSize ? 'margin-bottom: -10px;' : ''">
                  <span v-if="items.remark !== ''" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">หมายเหตุ : </span><span v-if="items.remark !== ''" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; color: #333333;">{{ items.remark }}</span>
                  <span v-else :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 600; color: #333333;">หมายเหตุ : -</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col> -->
         <!-- รายละเอียดการชำระเงิน -->
          <!-- เพิ่มมา รอต่อ api -->
         <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/Marketplace_partner/inventory.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              รายละเอียดการชำระเงิน
            </span>
          </v-row>
          <v-row v-if="!MobileSize && !IpadSize" dense class="mt-4">
            <div :style="IpadProSize ? 'width: 23vw;' : 'width: 24vw;'" :class="MobileSize ? 'd-flex' : ''">
              <v-row dense class="mb-1">
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วันที่เริ่มสัญญา :</span></v-col>
                <v-col cols="7"><span style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].invoice_start_date !== '-' ? new Date(items.detail_credit_term[0].invoice_start_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '-' }}</span></v-col>
              </v-row>
              <v-row dense class="mb-1">
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">งวดที่ :</span></v-col>
                <v-col cols="7"><span style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].num_of_credit_term === 0 ? '-' : items.detail_credit_term[0].num_of_credit_term }}</span></v-col>
              </v-row>
              <v-row dense class="mb-1">
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ส่วนลด :</span></v-col>
                <v-col cols="7"><span style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].discount === '-' ? '-' : Number(items.detail_credit_term[0].discount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' %' }}</span></v-col>
              </v-row>
              <v-row dense class="mb-1">
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">หมายเหตุ :</span></v-col>
                <v-col cols="7"><span style="font-size: 14px; font-weight: 400; color: #636363; cursor: pointer;" @click="openDialogRemark(items.detail_credit_term[0].remark)">ดูหมายเหตุ <v-icon color="#636363">mdi-alert-circle-outline</v-icon></span></v-col>
              </v-row>
            </div>
            <div :style="IpadProSize ? 'width: 25vw;' : 'width: 28vw;'" :class="MobileSize ? 'd-flex' : ''">
              <v-row dense class="mb-1">
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">วันที่สิ้นสุดสัญญา :</span></v-col>
                <v-col cols="7"><span style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].invoice_end_date !== '-' ? new Date(items.detail_credit_term[0].invoice_end_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '-' }}</span></v-col>
              </v-row>
              <v-row dense class="mb-1">
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">จำนวนเงิน :</span></v-col>
                <v-col cols="7">
                  <span v-if="items.pay_type !== 'recurring'" style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.net_price === 0 ? '-' : Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span>
                  <span v-if="items.pay_type === 'recurring'" style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].total_amount === 0 ? '-' : Number(items.detail_credit_term[0].total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span>
                </v-col>
              </v-row>
              <v-row dense>
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">ยอดเงิน :</span></v-col>
                <v-col cols="7"><span style="font-size: 14px; font-weight: 400; color: #333333;" :class="MobileSize ? 'ml-auto' : ''">{{ items.detail_credit_term[0].discount_price === '-' ? '-' : Number(items.detail_credit_term[0].discount_price).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span></v-col>
              </v-row>
            </div>
            <div :style="IpadProSize ? 'width: 15vw;' : 'width: 16vw;'" :class="MobileSize ? 'd-flex' : ''">
              <v-row dense>
                <v-col cols="5"><span style="font-size: 14px; font-weight: 600; color: #333333;" :class="MobileSize ? 'mr-auto' : ''">SO Type :</span></v-col>
                <v-col cols="7">
                  <span :style="MobileSize ? 'font-size: 12px;' : 'font-size: 13px;'" style="text-align: right;">
                    <v-chip v-if="items.pay_type === 'onetime'" small text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                    <v-chip v-else-if="items.pay_type === 'recurring'" small text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                    <v-chip v-else-if="items.pay_type === 'general'" small text-color="#808B96" color="#F4F6F6">General</v-chip>
                  </span>
                </v-col>
              </v-row>
            </div>
          </v-row>
          <v-row v-if="MobileSize || IpadSize" no-gutters>
            <v-col cols="11" class="mt-5">
              <v-row no-gutters>
                <v-col cols="6" md="3">
                  <span>วันที่เริ่มสัญญา : </span>
                </v-col>
                <v-col cols="6" md="9">
                  <span>{{ items.detail_credit_term[0].invoice_start_date !== '-' ? new Date(items.detail_credit_term[0].invoice_start_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '-' }}</span>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>วันที่สิ้นสุดสัญญา : </span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <span>{{ items.detail_credit_term[0].invoice_end_date !== '-' ? new Date(items.detail_credit_term[0].invoice_end_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '-' }}</span>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>SO Type : </span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <v-chip v-if="items.pay_type === 'onetime'" style="font-size: 12px; font-weight: 300;" small text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                  <v-chip v-else-if="items.pay_type === 'recurring'" style="font-size: 12px; font-weight: 300;" small text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                  <v-chip v-else-if="items.pay_type === 'general'" style="font-size: 12px; font-weight: 300;" small text-color="#808B96" color="#F4F6F6">General</v-chip>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>งวดที่ : </span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <span>{{ items.detail_credit_term[0].num_of_credit_term === 0 ? '-' : items.detail_credit_term[0].num_of_credit_term }}</span>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>จำนวนเงิน : </span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <span v-if="items.pay_type !== 'recurring'">{{ items.net_price === 0 ? '-' : Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span>
                  <span v-if="items.pay_type === 'recurring'">{{ items.detail_credit_term[0].total_amount === 0 ? '-' : Number(items.detail_credit_term[0].total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>ส่วนลด :</span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <span>{{ items.detail_credit_term[0].discount === '-' ? '-' : Number(items.detail_credit_term[0].discount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' %' }}</span>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>ยอดเงิน :</span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <span>{{ items.detail_credit_term[0].discount_price === '-' ? '-' : Number(items.detail_credit_term[0].discount_price).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span>
                </v-col>
                <v-col cols="6" md="3" class="mt-2">
                  <span>หมายเหตุ :</span>
                </v-col>
                <v-col cols="6" md="9" class="mt-2">
                  <span style="font-size: 14px; font-weight: 400; color: #636363; cursor: pointer;" @click="openDialogRemark(items.detail_credit_term[0].remark)">ดูหมายเหตุ <v-icon color="#636363">mdi-alert-circle-outline</v-icon></span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- การชำระเงิน -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/receipt.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              การชำระเงิน
            </span>
            <div class="pl-3 pt-1 pb-2" v-if="items.pay_type !== 'recurring'">
              <span v-if="items.shop_approve !== 'waiting_approve' && items.shop_approve !== 'reject'">
                <span style="color: #E5EFFF; font-size: 14px;" v-if="items.status === 'Credit'"><v-icon color="#52C41A">mdi-circle-medium</v-icon>ชำระเงินแบบเครดิตเทอม</span>
                <span style="color: #52C41A; font-size: 14px;" v-else-if="items.transaction_status === 'Success'"><v-icon color="#52C41A">mdi-circle-medium</v-icon>ชำระเงินสำเร็จ</span>
                <span style="color: #D1392B; font-size: 14px;" v-else-if="items.transaction_status === 'Cancel'"><v-icon color="#D1392B">mdi-circle-medium</v-icon>เกินกำหนดชำระ</span>
                <span style="color: #FFB300; font-size: 14px;" v-else-if="items.transaction_status === 'Fail'"><v-icon color="#FFB300">mdi-circle-medium</v-icon>ชำระเงินไม่สำเร็จ</span>
                <span style="color: #FFB300; font-size: 14px;" v-else-if="items.transaction_status === 'Waiting_Cancel'"><v-icon color="#FFB300">mdi-circle-medium</v-icon>รออนุมัติยกเลิก</span>
                <span style="color: #FAAD14; font-size: 14px;" v-else><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รอการชำระเงิน</span>
              </span>
              <span>
                <span v-if="items.shop_approve === 'waiting_approve'" style="color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รออนุมัติคำสั่งซื้อ</span>
                <span v-if="items.shop_approve === 'reject'" style="color: #D1392B;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>ปฏิเสธคำสั่งซื้อ</span>
              </span>
            </div>
            <div v-if="items.pay_type === 'recurring'">
              <span v-if="items.detail_credit_term[0].transaction_status === 'Not Paid'" style="font-size: 16px; font-weight: 400; color: #FAAD14;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>รอชำระเงิน</span>
              <span v-if="items.detail_credit_term[0].transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #52C41A;"><v-icon color="#52C41A">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
            </div>
          </v-row>
          <v-row class="mb-2" dense v-if="items.shop_approve !== 'waiting_approve' && items.shop_approve !== 'reject'">
            <!-- transaction_status credit -->
            <v-row v-if="items.transaction_status === 'Not Paid' && items.pay_type !== 'recurring'" no-gutters>
              <v-col cols="12" class="mt-4">
                <span class="mr-2" style="font-size: 16px; color: #333333; font-weight: 400;">รายละเอียดคำสั่งซื้อนี้ยังไม่ชำระเงิน</span>
              </v-col>
            </v-row>
            <v-row v-if="items.detail_credit_term[0].transaction_status === 'Not Paid' && items.pay_type === 'recurring'" no-gutters>
              <v-col cols="12" class="mt-4">
                <span class="mr-2" style="font-size: 16px; color: #333333; font-weight: 400;">รายละเอียดคำสั่งซื้อนี้ยังไม่ชำระเงิน</span>
              </v-col>
            </v-row>
            <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
              <v-col cols="12" class="mt-4">
                <span class="mr-2" style="font-size: 16px; color: #333333; font-weight: 400;">คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย</span>
              </v-col>
            </v-row>
            <v-row v-else-if="items.transaction_status === 'Fail' && statusPayment" no-gutters>
              <v-col cols="12" class="mt-4">
                <span class="mr-2" style="font-size: 16px; color: #333333; font-weight: 400;">คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง</span>
                <!-- <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn> -->
              </v-col>
            </v-row>
            <v-row v-else no-gutters>
              <v-col v-if="items.status !== 'Credit' && statusPayment" cols="12" class="mt-4">
                <span class="mr-2" style="font-size: 16px; color: #333333; font-weight: 400;">คุณยังไม่ได้ทำการชำระเงิน กรุณาชำระเงินผ่านบริการทุกช่องทางของ <b>Thaidotcom Payment</b> โดยสามารถชำระเงินได้ที่นี่</span>
                <!-- <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn> -->
              </v-col>
            </v-row>
          </v-row>
          <v-row dense>
            <!-- <v-col cols="12" class="mt-4">
              <span style="font-size: 20px;"><b>หลักฐานการชำระเงิน</b></span>
            </v-col> -->
            <v-row v-if="(items.transaction_status === 'Success' && items.pay_type !== 'recurring') || (items.detail_credit_term[0].transaction_status === 'Success' && items.pay_type === 'recurring')" no-gutters>
              <v-col cols="12" class="mt-5">
                <v-row>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">รหัสการชำระเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ items.receipt[0].orderId === '' ? '-' : items.receipt[0].orderId }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">จำนวนเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ items.receipt[0].TxnAmount === '' ? '-' : Number(items.receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">วันและเวลาที่ชำระเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{items.receipt[0].updated_at === '' ? '-' : new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">Ref</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ items.receipt[0].orderIDRef === '' ? '-' : items.receipt[0].orderIDRef }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-weight: 400; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'">รูปแบบการชำระเงิน</span>
                    <span class="ml-auto" style="font-weight: 700; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'" v-if="items.installment_month === '0'">{{ items.receipt[0].payType }}</span>
                    <span class="ml-auto" style="font-weight: 700; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'" v-else>{{ items.receipt[0].payType }} ({{items.installment_month}}x @0%)</span>
                  </v-col>
                  <!-- <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">ธนาคารที่ชำระเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ bankName }}</span>
                  </v-col> -->
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">ผลการชำระเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">
                      {{ items.status === 'Credit' ? 'ชำระเงินแบบเครดิตเทอม' : items.transaction_status === 'Success' ? 'ชำระเงินสำเร็จ' : items.transaction_status === 'Cancel' ? 'ยกเลิกสินค้า' : items.transaction_status === 'Fail' ? 'ชำระเงินไม่สำเร็จ' : 'ยังไม่ชำระเงิน' }}
                    </span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- <v-row v-else>
              <v-col cols="12" class="mt-5 ml-1">
                <span>-</span>
              </v-col>
            </v-row> -->
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <!-- หมายเหตุ -->
     <!-- เพิ่มมา รอต่อ api -->
    <v-dialog v-model="modalRemark" persistent :width="MobileSize ? '90%' : IpadSize ? '60%' : '50%'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 100%'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>หมายเหตุการชำระเงิน</b></span>
              </v-col>
              <v-btn @click="modalRemark = false" fab small icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 20px 48px 10px 48px;' : 'padding: 20px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" sm="12" class="px-2 py-2">
                    <v-textarea readonly v-model="remarkRecurring" :counter="250" maxLength="250" outlined style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogUploadPDF" width="752px" persistent scrollable>
      <v-form ref="formUploadPDF" :lazy-validation="lazy">
        <v-card style="border-radius: 24px; background-color: #27AB9C;">
          <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
            <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">อัปโหลดเอกสาร
            </span>
            <v-btn icon dark @click="closeModal()">
              <v-icon color="#FFFFFF">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card style="border-radius: 20px;">
            <v-card-text>
              <div>
                <v-col class="pl-3">
                  <span style="font-size: 16px; font-weight: 600; line-height: 22.4px; color: #333333;">เอกสารที่ต้องอัปโหลด <span style="font-size: 12px; font-weight: 400; line-height: 16.8px; color: #CCCCCC;">(รองรับไฟล์นามสกุล .pdf และขนาดไฟล์ไม่เกิน 5mb)</span></span>
                </v-col>
                <div>
                  <v-col class="pt-0" v-for=" (item, index) in itemsDoc" :key="index">
                    <span>เอกสาร {{item.text}}</span>
                    <v-card class="mt-4 rounded-lg" style="border: solid 1px #F3F5F7;" elevation="0" width="100%" height="100%">
                      <v-card-text class="pa-2">
                        <v-row class="pa-2">
                          <v-col cols="3" md="2" xs="2" sm="2">
                            <v-img
                              src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png"
                              width="55px"
                              height="55px"
                              contain>
                            </v-img>
                          </v-col>
                          <v-col cols="7" md="6" xs="12" sm="6">
                            <v-layout row wrap align-center style="margin-top: 10px">
                              <v-flex>
                                <span
                                  style="
                                    font-weight: 600;
                                    font-size: 16px;
                                    line-height: 22px;
                                    color: #333333;
                                  ">
                                  <v-text-field
                                    v-model="item.value"
                                    oninput="this.value = this.value.replace(/[^0-9a-zA-Z-]/g, '').replace(/(\..*)\./g, '$1')"
                                    style="border-radius: 8px;"
                                    dense
                                    outlined
                                    :rules="itemRules.documents"
                                    placeholder="กรุณากรอกชื่อเอกสาร"
                                    :maxLength="25"
                                    >
                                  </v-text-field>
                                </span>
                              </v-flex>
                            </v-layout>
                          </v-col>
                          <v-col
                            cols="12"
                            md="4"
                            sm="4"
                            style="text-align-last: end; margin: auto;"
                            class="rounded-lg"
                            @click="selectIndex(item.value, index , item.file)"
                          >
                            <v-row justify="center" v-if="item.file === ''">
                              <v-file-input
                                outlined
                                v-model="DataFile"
                                :items="DataFile"
                                accept=".pdf"
                                @change="UploadFile($event, item.value, index)"
                                id="file_input"
                                multiple
                                :clearable="false"
                                style="display: none">
                              </v-file-input>
                              <v-btn
                                :disabled="!itemsDoc[index].value"
                                rounded
                                dense
                                outlined
                                color="#27AB9C"
                                style="height: 40px; margin-top: 5px; width: 149px;"
                                @click="onPickFile()"
                              >
                                <v-img src="@/assets/ImageINET-Marketplace/Shop/iconShop/upload.png"></v-img>
                                <span class="ml-2">อัปโหลดไฟล์</span>
                              </v-btn>
                            </v-row>
                            <v-row justify="center" v-else>
                              <v-btn color="#1B5DD6" @click="delete_file(index)" rounded outlined>
                                <span v-if="typeof(item.file) === 'string'">{{item.file|truncate(15, '...')}}</span>
                                <span v-else>{{item.file.name|truncate(15, '...')}}</span>
                                <v-icon>mdi-close</v-icon>
                              </v-btn>
                            </v-row>
                            <v-row
                              class="d-flex justify-end"
                              :class="MobileSize ? 'pr-4':'pr-4'"
                            >
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                    </v-col>
                </div>
              </div>
            </v-card-text>
            <v-card-actions>
              <v-container style="display: flex; justify-content: flex-end">
                <v-col cols="6" class="pa-0">
                  <v-btn
                    rounded
                    dense
                    dark
                    outlined
                    color="#27AB9C"
                    class="pl-7 pr-7 mt-2"
                    @click="closeModal()"
                  >
                    ยกเลิก
                  </v-btn>
                </v-col>
                <v-col cols="6" style="text-align: end;" class="pa-0">
                  <v-btn :disabled="checkDis(itemsDoc)"
                    rounded
                    dense
                    color="#27AB9C"
                    class="ml-4 mt-2 pl-8 pr-8 white--text"
                    @click="confirm()"
                  >
                    บันทึก
                  </v-btn>
                </v-col>
              </v-container>
            </v-card-actions>
          </v-card>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- Modal กรอกยกเลิกคำสั่งซื้อ -->
    <v-dialog v-model="modalInputReason" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ยกเลิกคำสั่งซื้อ</b></span>
              </v-col>
              <v-btn fab small @click="CloseModalInputReason()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ itemOrder.payment_transaction }}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ itemOrder.buyer_name }}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ dateCreateOrderStep1 }}น.</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reason" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="CloseModalInputReason()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" :disabled="reason !== '' ? false : true" height="40" class="white--text" @click="confirmCancelOrder()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Await Confirm Order -->
    <v-dialog v-model="modalAwaitCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogConfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกคำสั่งซื้อ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณแน่ใจหรือไม่ว่าต้องการยกเลิก</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คำสั่งซื้อรายการนี้</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogConfirmCancel()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmCancel()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Cancel Order -->
    <v-dialog v-model="modalSuccessCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeSuccessCancelOrder()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกคำสั่งซื้อเรียบร้อย</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยกเลิกคำสั่งซื้อ</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessCancelOrder()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Confirm Order Refund-->
    <v-dialog v-model="modalAwaitCancelOrderRefund" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogConfirmCancelRefund()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{cancelType === 'submit' ? 'อนุมัติคำขอยกเลิกคำสั่งซื้อ' : 'ปฏิเสธคำขอยกเลิกคำสั่งซื้อ'}}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{cancelType === 'submit' ? 'คุณแน่ใจหรือไม่ว่าต้องการอนุมัติ' : 'คุณแน่ใจหรือไม่ว่าต้องการปฏิเสธ'}}</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คำขอยกเลิกคำสั่งซื้อรายการนี้</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogConfirmCancelRefund()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmCancelRefund()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Cancel Order Refund-->
    <v-dialog v-model="modalSuccessCancelOrderRefund" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeSuccessCancelOrderRefund()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{cancelType === 'submit' ? 'อนุมติคำขอยกเลิกคำสั่งซื้อเรียบร้อย' : 'ปฏิเสธคำขอยกเลิกคำสั่งซื้อเรียบร้อย'}}</b></p>
            <span v-if="cancelType === 'cancel'" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการปฏิเสธคำขอยกเลิกคำสั่งซื้อ</span>
            <span v-if="cancelType === 'submit'" style="font-weight: 700; font-size: 16px; line-height: 24px; color: red;">*ผู้ซื้อจะได้รับเงินคืนเวลา 22.00 น.</span><br>
            <span v-if="cancelType === 'submit'" style="font-weight: 700; font-size: 16px; line-height: 24px; color: red;"> ภายในวันที่ร้านค้าอนุมัติ*</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessCancelOrderRefund()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal กรอกกรณีใช้ขนส่งภายนอก -->
    <v-dialog v-model="modalAddTrackingTransport" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-form ref="modalTrackingOwnform" :lazy-validation="lazyform">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pt-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ข้อมูลการจัดส่ง</b></span>
                </v-col>
                <v-btn fab small @click="ClosemodalAddTrackingTransport()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
                <v-card-text class="pa-0">
                  <v-row dense justify="center">
                    <v-col cols="12" align="center">
                      <v-img :src="require('@/assets/ImageINET-Marketplace/Shop/owntracking.png')" max-height="147" max-width="320" width="100%" height="100%"></v-img>
                    </v-col>
                  </v-row>
                  <v-row dense justify="center" class="mt-10">
                    <!-- รูปแบบการจัดส่ง -->
                    <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color: #333333; line-height: 22px;">รูปแบบการจัดส่ง <span style="color: red;">*</span></span>
                      <v-select v-model="ownShipping" outlined dense style="border-radius: 8px;" placeholder="เลือกรูปแบบการจัดส่ง" class="setCustomSelect" :items="dataShipping" item-text="ship_name" item-value="ship_name" :rules="itemRules.selectOwnShipping"></v-select>
                    </v-col>
                    <!-- Tracking Number -->
                    <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color: #333333; line-height: 22px;">Tracking Number <span style="color: red;">*</span></span>
                      <v-text-field v-model="trackingNumOwn" outlined dense style="border-radius: 8px;" placeholder="กรอก Tracking Number" :rules="itemRules.trackingNumOwn"></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions style="height: 88px; background-color: #F5FCFB;">
            <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="ClosemodalAddTrackingTransport()">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" rounded width="125" :disabled="(ownShipping !== '' && trackingNumOwn !== '') ? false : true" height="40" class="white--text" @click="confirmUpdateTracking()">ยืนยัน</v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- modal buyer cancel order -->
    <v-dialog v-model="modalInputReasonRefund" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ยกเลิกคำสั่งซื้อ</b></span>
              </v-col>
              <v-btn fab small @click="closemodalInputReasonRefund('details')" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{itemOrder.payment_transaction}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.buyer_name === '' ? '-' : items.buyer_name }}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-3">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(timeStampCancel).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric'
                    })}}น.</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 pa-0 align-center' : 'd-flex ma-0 pa-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 12" :class="MobileSize ? '' : ''">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field readonly v-model="urName" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                    <!-- <v-col :cols="MobileSize ? 12 : 12">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เบอร์โทรศัพท์ <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field readonly v-model="telNumber" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col> -->
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 align-center' : 'ma-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 12" :class="MobileSize ? '' : ''">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-select readonly v-model="bankNameRefund" :items="listAccount" item-text="name" item-value="code" :menu-props="{ offsetY: false, maxHeight: '231px', maxWidth: '280px', overflowY: 'hidden' }" dense outlined hide-details></v-select>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 12">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field readonly v-model="bankNumberRefund" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea readonly v-model="reasonRefund" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="red" rounded outlined width="125" height="40" @click="openModalRejectRefund()">ปฏิเสธ</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" @click="openDialogConfirmCancelRefund('submit')" class="white--text">อนุมัติ</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- after push reject button -->
    <v-dialog v-model="modalrejectrefund" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ยกเลิกคำสั่งซื้อ</b></span>
              </v-col>
              <v-btn fab small @click="closemodalInputReasonRefund('reject')" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <v-row dense class="mt-2">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลปฏิเสธคำขอยกเลิกคำสั่งซื้อ <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reasonRejectRefund" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลปฏิเสธคำขอยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closemodalInputReasonRefund('reject')">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" :disabled="reasonRejectRefund === ''" rounded width="125" height="40" @click="openDialogConfirmCancelRefund('cancel')" class="white--text">ยันยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- modal aprrove slip -->
    <v-dialog v-model="ModalApproveSlip" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ยืนยันสลิป</b></span>
              </v-col>
              <v-btn fab small @click="CloseModalApproveSlip()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล slip -->
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;" v-if="imageSlip !== ''">
                  <v-col cols="12" md="12" sm="12" align="center">
                    <v-img loading="lazy" :src="imageSlip" height="100%" width="100%" max-height="600" max-width="300" contain></v-img>
                  </v-col>
                </v-row>
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;" v-if="imageSlip === ''">
                  <v-col cols="12" md="12" sm="12" align="center">
                    <span style="font-weight: 700; color: #333333; font-size: 16px;">ไม่มีรูปภาพสลิป</span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="confirmApproveSlip('reject')">ไม่อนุมัติ</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text" @click="confirmApproveSlip('approve')">อนุมัติ</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      lazyform: false,
      dataShipping: [
        { id: 1, ship_link: 'https://track.thailandpost.co.th/', ship_name: 'THAILAND POST' },
        { id: 2, ship_link: 'https://www.jtexpress.co.th/', ship_name: 'J&T EXPRESS' },
        { id: 3, ship_link: 'https://th.kerryexpress.com/th/track/', ship_name: 'KERRY EXPRESS' },
        { id: 4, ship_link: 'https://www.scgexpress.co.th/tracking/', ship_name: 'SCG EXPRESS' },
        { id: 5, ship_link: 'https://www.dhl.com/th-th/home/<USER>', ship_name: 'DHL EXPRESS' },
        { id: 6, ship_link: 'https://www.best-inc.co.th/track', ship_name: 'BEST EXPRESS' },
        { id: 7, ship_link: 'https://www.ninjavan.co/th-th/tracking', ship_name: 'NINJA VAN' },
        { id: 8, ship_link: 'https://www.flashexpress.co.th/fle/tracking', ship_name: 'FLASH EXPRESS' },
        { id: 9, ship_link: '', ship_name: 'อื่นๆ' }
      ],
      timeStampCancel: '',
      statusCancel: '',
      cancelType: '',
      reasonRejectRefund: '',
      modalAwaitCancelOrderRefund: false,
      modalSuccessCancelOrderRefund: false,
      modalrejectrefund: false,
      detailCancel: [],
      listAccount: [],
      reasonRefund: '',
      urName: '',
      bankNameRefund: '',
      telNumber: '',
      bankNumberRefund: '',
      modalInputReasonRefund: false,
      ownShipping: '',
      trackingNumOwn: '',
      checkDisConfirm: true,
      QTorder: '',
      poPDF: '',
      prPDF: '',
      soPDF: '',
      documents: [],
      shopID: 0,
      name: '',
      i: 0,
      itemsDoc: [
        { text: 'PO', value: '', file: '' },
        { text: 'PR', value: '', file: '' },
        { text: 'SO', value: '', file: '' }
      ],
      Detail: {
        product_file: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      DataFile: [],
      statusPayment: false,
      dialogUploadPDF: false,
      itemColor: 'green',
      // overlay: false,
      items: [{ data_list: [] }],
      paymentNumber: {},
      cardProduct: [],
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingText: '',
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: '',
      created_at: '',
      status: '',
      mobilystTrackingNo: '',
      flashTracking: '',
      status_items: [
        // { text: 'ดำเนินการแล้ว', value: 'ดำเนินการแล้ว' },
        { text: 'ยังไม่ดำเนินการ', value: 'ยังไม่ดำเนินการ' }
        // { text: 'ยกเลิก', value: 'ยกเลิก' }
      ],
      status_items_success: [
        { text: 'ดำเนินการแล้ว', value: 'ดำเนินการแล้ว' }
      ],
      dialogChangstatus: false,
      disablecancel: false,
      textstatus: '',
      valChangStatus: '',
      itemStatus: '',
      estep: 0,
      ordernumber: '',
      transactionNumber: '',
      termNumber: '',
      isUpdate: false,
      checkbox: false,
      menu: false,
      menu2: false,
      menu3: false,
      time: null,
      modal2: false,
      dateSent: null,
      dateReceived: null,
      lazy: false,
      qrcode: '',
      // flashMCHID: process.env.VUE_APP_FLASH,
      itemRules: {
        documents: [
          v => !!v || 'กรุณาระบุชื่อเอกสาร'],
        dateSent: [
          v => !!v || 'กรุณาระบุวันที่ส่ง'],
        time: [
          v => !!v || 'กรุณาระบุเวลา'],
        dateReceived: [
          v => !!v || 'กรุณาระบุวันที่รับ'],
        selectOwnShipping: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการจัดส่ง'
        ],
        trackingNumOwn: [
          v => !!v || 'กรุณากรอก Tracking Number'
        ]
      },
      routes: [{
        routedAt: 1523356924,
        routeAction: 'DELIVERY_CONFIRM',
        message: 'พัสดุของคุณถูกเซ็นรับแล้ว เซ็นรับโดย TH01011C27',
        state: 5
      }, {
        routedAt: 1523356924,
        routeAction: 'DELIVERY_TICKET_CREATION_SCAN',
        message: 'มีพัสดุรอการนำส่ง กรุณารอการติดต่อจากเจ้าหน้าที่ Mobilyst Tech',
        state: 2
      }, {
        routedAt: 1523356560,
        routeAction: 'SHIPMENT_WAREHOUSE_SCAN',
        message: 'พัสดุของคุณอยู่ที่ กทม. จะถูกส่งไปยัง จตุโชติ-DC',
        state: 3
      }, {
        routedAt: 1523356029,
        routeAction: 'RECEIVED',
        message: 'zhao=DC พนักงานเข้ารับพัสดุแล้ว',
        state: 6
      }],
      header: [
        { text: 'รหัส SKU', value: 'sku', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียดสินค้า', value: 'productdetails', sortable: false, align: 'start', width: '25%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคาต่อชิ้น', value: 'revenue_default', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวน', value: 'quantity', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคารวม', value: 'total_revenue_default', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
        // { text: 'Amount', value: 'revenue_amount', filterable: false, sortable: false, align: 'start', width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      optionsCard: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 1 },
          { start: 768, end: 992, size: 2 },
          { start: 992, end: 1200, size: 3 },
          { start: 1200, end: 1300, size: 4 },
          // { start: 1200, end: 1300, size: 3 },
          { size: 4 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1200,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      // headerFree: [
      //   { text: 'รหัส SKU', value: 'sku', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รายละเอียดสินค้า', value: 'productdetails', sortable: false, align: 'start', width: '25%', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคาต่อชิ้น', value: 'revenue_default', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'จำนวน', value: 'quantity', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคารวม', value: 'total_revenue_default', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'Amount', value: 'revenue_amount', filterable: false, sortable: false, align: 'start', width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      headerMobile: [
        { text: 'รายละเอียดสินค้า', value: 'productdetailsMobile', sortable: false, width: '100%', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      itemOrder: [],
      reason: '',
      modalInputReason: false,
      getStatusOrder: '',
      modalAwaitCancelOrder: false,
      modalSuccessCancelOrder: false,
      modalAddTrackingTransport: false,
      ModalApproveSlip: false,
      imageSlip: '',
      urlTracking: '',
      trackingNoOutSource: '',
      modalRemark: false,
      remarkRecurring: ''
    }
  },
  async created () {
    this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    this.$EventBus.$on('SentGetReview', this.getItemProduct)
    this.$EventBus.$emit('changeNav')
    this.ordernumber = this.$route.query.orderNumber
    this.transactionNumber = this.$route.query.tranNumber
    this.termNumber = this.$route.query.termNumber
    // console.log('this.termNumber', this.termNumber)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null) {
      // if (localStorage.getItem('CompanyData') !== null) {
      //   const companyId = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      //   this.paymentNumber = {
      //     payment_transaction_number: this.ordernumber,
      //     role_user: 'purchaser',
      //     company_id: companyId.id
      //   }
      //   this.getFranchise()
      //   this.getItemProduct()
      // } else {
      this.paymentNumber = {
        order_number: this.ordernumber,
        payment_transaction_number: this.transactionNumber,
        num_credit_term: this.termNumber
      }
      await this.getItemProductB2B()
      await this.getOrderDocument()
      await this.getDetailCancel()
      // }
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  beforeDestroy () {
    localStorage.setItem('orderNumber', this.ordernumber)
    localStorage.setItem('transactionNumber', this.transactionNumber)
    localStorage.setItem('termNumber', this.termNumber)
    this.$EventBus.$off('SentGetReview')
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รหัสสินค้า',
          dataIndex: 'sku',
          scopedSlots: { customRender: 'sku' },
          key: 'sku',
          align: 'start',
          width: '15%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          align: 'start',
          scopedSlots: { customRender: 'productdetails' },
          width: '25%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'revenue_default',
          scopedSlots: { customRender: 'revenue_default' },
          key: 'revenue_default',
          align: 'start',
          width: '15%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'start',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'total_revenue_default',
          scopedSlots: { customRender: 'total_revenue_default' },
          key: 'total_revenue_default',
          align: 'start',
          width: '15%'
        },
        {
          title: 'Amount',
          dataIndex: 'revenue_amount ',
          scopedSlots: { customRender: 'revenue_amount ' },
          key: 'revenue_amount ',
          align: 'start',
          width: '15%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        }
      ]
      return headersMobile
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    ownShipping (val) {
      if (val === 'อื่นๆ') {
        this.trackingNumOwn = '-'
      } else {
        this.trackingNumOwn = ''
      }
    },
    async MobileSize (val) {
      if (val === true) {
        localStorage.setItem('orderNumber', this.ordernumber)
        localStorage.setItem('transactionNumber', this.transactionNumber)
        localStorage.setItem('termNumber', this.termNumber)
        await this.$router.push({ path: `/POSellerB2BDetailMobile?orderNumber=${this.ordernumber}&tranNumber=${this.transactionNumber}&termNumber=${this.termNumber}` }).catch(() => {})
      } else {
        localStorage.setItem('orderNumber', this.ordernumber)
        localStorage.setItem('transactionNumber', this.transactionNumber)
        localStorage.setItem('termNumber', this.termNumber)
        await this.$router.push({ path: `/POSellerB2BDetail?orderNumber=${this.ordernumber}&tranNumber=${this.transactionNumber}&termNumber=${this.termNumber}` }).catch(() => {})
      }
    }
  },
  methods: {
    linkToURLTracking (url) {
      this.urlTracking = url
      setTimeout(() => {
        document.getElementById('urlTracking').click()
      }, 200)
    },
    ApproveSlipModal (item) {
      this.imageSlip = ''
      this.imageSlip = item.data_slip !== null ? item.data_slip[0] : ''
      this.ModalApproveSlip = true
    },
    CloseModalApproveSlip () {
      this.ModalApproveSlip = false
    },
    async confirmApproveSlip (val) {
      if (val === 'approve') {
        this.$store.commit('openLoader')
        const data = {
          order_id: this.items.payment_transaction,
          status: 'approve'
        }
        await this.$store.dispatch('actionApproveSlipPayment', data)
        const response = await this.$store.state.ModuleShop.stateApproveSlipPayment
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'success',
            text: response.message
          })
          this.ModalApproveSlip = false
          await this.changeStatusOrder('accepted', this.items)
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: response.message
          })
        }
      } else {
        this.ModalApproveSlip = false
      }
    },
    copyClipboard () {
      const track = document.getElementById('trackingNumber')
      // Select the text field
      track.select()
      track.setSelectionRange(0, 99999) // For mobile devices

      // Copy the text inside the text field
      navigator.clipboard.writeText(track.value)
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        position: 'center',
        icon: 'success',
        title: 'คัดลอกสำเร็จ'
      })
    },
    async confirmUpdateTracking () {
      this.$store.commit('openLoader')
      const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      const data = {
        seller_shop_id: shopDetail.id,
        tracking_number: this.trackingNumOwn,
        ship_name: this.ownShipping,
        order_number: this.items.payment_transaction
      }
      await this.$store.dispatch('actionsUpdateTrackingNumber', data)
      const response = await this.$store.state.ModuleShop.stateUpdateTrackingNumber
      if (response.query_result.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.modalAddTrackingTransport = false
        this.$swal.fire({
          icon: 'success',
          text: 'อัพเดต Tracking Number สำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.getItemProductB2B()
        await this.getOrderDocument()
      } else {
        this.$store.commit('closeLoader')
        this.modalAddTrackingTransport = false
        this.$swal.fire({
          icon: 'error',
          text: this.messageErrorTrackingnumber(response.query_result.message),
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    messageErrorTrackingnumber (message) {
      if (message === 'Update tracking unsuccess. Please try agian.') {
        return 'อัพเดตเลย Tracking Number ไม่สำเร็จ โปรดลองอีกครั้ง'
      } else if (message === 'Order number not found.') {
        return 'ไม่พบเลขออเดอร์นี้ในระบบ'
      } else if (message === 'ship_name is not allowed') {
        return 'รูปแบบขนส่งนี้ไม่รองรับในระบบ'
      } else if (message === 'seller_shop_id is required') {
        return 'โปรดใส่ ID ของร้านค้า'
      } else if (message === 'Duplicate entry') {
        return 'เลข Tracking Number นี้มีอยู่ในระบบแล้ว'
      } else {
        return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
      }
    },
    addOwnTransportation () {
      this.ownShipping = ''
      this.trackingNumOwn = ''
      this.modalAddTrackingTransport = true
    },
    ClosemodalAddTrackingTransport () {
      this.$refs.modalTrackingOwnform.resetValidation()
      this.modalAddTrackingTransport = false
    },
    async changeStatusOrder (status, item) {
      const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      var data = ''
      var response = ''
      this.itemOrder = ''
      this.getStatusOrder = ''
      if (status === 'accepted') {
        data = {
          payment_transaction_number: item.payment_transaction,
          order_number: item.order_number,
          seller_shop_id: shopDetail.id,
          role_user: 'seller',
          status: 'accepted',
          reason: ''
        }
        await this.$store.dispatch('actionsAccecptProduct', data)
        response = await this.$store.state.ModuleShop.stateAccecptProduct
        if (response.result === 'SUCCESS') {
          this.$swal.fire({
            icon: 'success',
            text: 'เข้ารับสินค้าสำเร็จ',
            showConfirmButton: false,
            timer: 1500
          })
          await this.getItemProductB2B()
          await this.getOrderDocument()
        } else {
        }
      } else if (status === 'approve') {
        data = {
          payment_transaction_number: item.payment_transaction,
          order_number: item.order_number,
          seller_shop_id: shopDetail.id,
          role_user: 'seller',
          status: 'approve',
          reason: ''
        }
        await this.$store.dispatch('actionsAccecptProduct', data)
        response = await this.$store.state.ModuleShop.stateAccecptProduct
        if (response.result === 'SUCCESS') {
          this.$swal.fire({
            icon: 'success',
            text: 'อนุมัติรายการสั่งซื้อสำเร็จ',
            showConfirmButton: false,
            timer: 1500
          })
          await this.getItemProductB2B()
          await this.getOrderDocument()
        }
      } else if (status === 'reject') {
        data = {
          payment_transaction_number: item.payment_transaction,
          order_number: item.order_number,
          seller_shop_id: shopDetail.id,
          role_user: 'seller',
          status: 'reject',
          reason: ''
        }
        await this.$store.dispatch('actionsAccecptProduct', data)
        response = await this.$store.state.ModuleShop.stateAccecptProduct
        if (response.result === 'SUCCESS') {
          this.$swal.fire({
            icon: 'success',
            text: 'ไม่อนุมัติรายการสั่งซื้อสำเร็จ',
            showConfirmButton: false,
            timer: 1500
          })
          await this.getItemProductB2B()
          await this.getOrderDocument()
        }
      } else {
        this.itemOrder = item
        this.getStatusOrder = status
        this.reason = ''
        this.modalInputReason = true
      }
    },
    confirmCancelOrder () {
      this.modalInputReason = false
      this.modalAwaitCancelOrder = true
    },
    async confirmCancel () {
      this.modalAwaitCancelOrder = false
      const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      var data = ''
      data = {
        payment_transaction_number: this.itemOrder.payment_transaction,
        order_number: this.itemOrder.order_number,
        seller_shop_id: shopDetail.id,
        role_user: 'seller',
        status: this.getStatusOrder,
        reason: this.reason
      }
      await this.$store.dispatch('actionsAccecptProduct', data)
      const resposnse = await this.$store.state.ModuleShop.stateAccecptProduct
      if (resposnse.result === 'SUCCESS') {
        this.modalSuccessCancelOrder = true
      } else {
        this.$swal.fire({
          icon: 'error',
          text: resposnse.message,
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    closeDialogConfirmCancel () {
      this.modalAwaitCancelOrder = true
    },
    CloseModalInputReason () {
      this.reason = ''
      this.modalInputReason = false
    },
    getQrCode (items) {
      const link = items.url_barcode_picture
      const a = document.createElement('a')
      a.setAttribute('href', link)
      a.setAttribute('target', '_blank')
      a.click()
    },
    async closeSuccessCancelOrder () {
      this.modalSuccessCancelOrder = false
      await this.getItemProductB2B()
      await this.getOrderDocument()
    },
    checkDis (val) {
      // console.log('tttt', val)
      if (val[0].file || val[1].file || val[2].file) {
        return false
      } else {
        return true
      }
    },
    closeModal () {
      this.$refs.formUploadPDF.resetValidation()
      // this.selectgroup = {
      //   document: []
      // }
      this.dialogUploadPDF = false
    },
    delete_file (i) {
      this.itemsDoc[i].file = ''
      this.Detail.product_file[i] = ''
      this.$refs.formUploadPDF.resetValidation()
    },
    openToPDF (index) {
      this.dialog_pdf = true
      this.pdftofile =
        'data:application/pdf;base64,' +
        this.Detail.product_file[index].file.toString()
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    selectIndex (name, index, file) {
      this.i = index
      this.name = name
    },
    UploadFile (event, name, index) {
      // console.log('this.itemsDoc11', this.itemsDoc)
      for (let i = 0; i < this.DataFile.length; i++) {
        const element = this.DataFile[i]
        const imageSize = element.size / 1024 / 1024
        if (imageSize < 2) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            this.Detail.product_file[this.i] = {
              name_document: this.name,
              file: resultReader.split(',')[1]
            }
            this.DataFile = []
            this.itemsDoc[this.i].file = event[0]
            // console.log('check', this.checkDisConfirm)
            // console.log('this.itemsDocevent', this.itemsDoc)
          }
        } else {
          this.Detail.product_file = []
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่ไฟล์ไม่เกิน 5 mb',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    async confirm () {
      // console.log('itemsDoc===>', this.itemsDoc)
      this.$store.commit('openLoader')
      // if (this.$refs.formUploadPDF.validate()) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.ordernumber = this.$route.query.orderNumber
      const formData = new FormData()
      formData.append('order_Number', this.ordernumber)
      formData.append('sellerShopId', this.shopID)
      formData.append('userId', onedata.user.user_id)
      formData.append('poFile', this.itemsDoc[0].file)
      formData.append('prFile', this.itemsDoc[1].file)
      formData.append('soFile', this.itemsDoc[2].file)
      formData.append('poNumber', this.itemsDoc[0].value === null ? this.itemsDoc[0].value = '' : this.itemsDoc[0].value)
      formData.append('prNumber', this.itemsDoc[1].value === null ? this.itemsDoc[1].value = '' : this.itemsDoc[1].value)
      formData.append('soNumber', this.itemsDoc[2].value === null ? this.itemsDoc[2].value = '' : this.itemsDoc[2].value)
      await this.$store.dispatch('actionUploadOrderDocument', formData)
      var res = await this.$store.state.ModuleOrder.stateUploadOrderDocument
      if (res.message === 'File uploaded successfully') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'บันทึกสำเร็จ'
        })
        this.dialogUploadPDF = false
        await this.getItemProductB2B()
        await this.getOrderDocument()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
      // } else {
      //   this.$store.commit('closeLoader')
      //   this.$swal.fire({
      //     showConfirmButton: false,
      //     timer: 1500,
      //     timerProgressBar: true,
      //     icon: 'warning',
      //     title: 'กรุณากรอกชื่อเอกสาร'
      //   })
      // }
    },
    async getOrderDocument () {
      this.$store.commit('openLoader')
      var data = {
        orderNumber: this.ordernumber
      }
      // console.log('data', data)
      await this.$store.dispatch('actionGetOrderDocument', data)
      var res = await this.$store.state.ModuleOrder.stateGetOrderDocument
      if (res.message === 'Get order document successfully') {
        this.documents = res.data
        this.itemsDoc[0].value = this.documents.PoNumber
        this.itemsDoc[1].value = this.documents.PrNumber
        this.itemsDoc[2].value = this.documents.SoNumber
        this.itemsDoc[0].file = this.documents.poFile
        this.itemsDoc[1].file = this.documents.prFile
        this.itemsDoc[2].file = this.documents.soFile
        // console.log('itemsDoc', this.itemsDoc)
      } else {
        this.documents = []
      }
      this.$store.commit('closeLoader')
      // console.log(' this.documents', this.documents)
    },
    async GetETax (val) {
      // console.log('valGetETax', val)
      // ของใหม่
      var data = {
        transactionCode: val
      }
      await this.$store.dispatch('ActionsGetETaxPDF', data)
      const response = await this.$store.state.ModuleCart.stateGetETaxPDF
      if (response.result === 'OK') {
        if (response.etaxResponse.status === 'OK') {
          var pdfUrl = ''
          if (response.etaxResponse.urlPdf !== undefined) {
            this.pdfUrl = response.etaxResponse.urlPdf
            pdfUrl = response.etaxResponse.urlPdf
          } else {
            this.pdfUrl = response.etaxResponse.pdfURL
            pdfUrl = response.etaxResponse.pdfURL
          }
          // ใช้วิธีเปิดลิงก์ที่ทำงานได้ทุกแพลตฟอร์ม
          const link = document.createElement('a')
          link.href = pdfUrl
          link.target = '_blank'
          link.rel = 'noopener noreferrer'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่พบเอกสารใบกำกับภาษี'
        })
      }
      // ของเก่า
      // var data = {
      //   transactionCode: val.transaction_code
      // }
      // await this.$store.dispatch('ActionsGetETaxPDF', data)
      // var response = await this.$store.state.ModuleCart.stateGetETaxPDF
      // if (response.result === 'OK') {
      //   if (response.etaxResponse.status === 'OK') {
      //     if (response.etaxResponse.urlPdf !== undefined) {
      //       window.open(`${response.etaxResponse.urlPdf}`, '_blank')
      //       // console.log('response', response.etaxResponse.urlPdf)
      //     } else {
      //       window.open(`${response.etaxResponse.pdfURL}`, '_blank')
      //     }
      //     // window.open(`${response.etaxResponse.pdfURL}`)
      //   }
      // }
    },
    async getItemProductB2B () {
      // this.overlay = true
      this.$store.commit('openLoader')
      this.ordernumber = this.$route.query.orderNumber
      await this.$store.dispatch('actionOrderDetailSeller', this.paymentNumber)
      var res = await this.$store.state.ModuleOrder.stateOrderDetailSeller
      this.shopID = res.data.data_list[0].shop_id
      this.mobilystTrackingNo = res.data.order_mobilyst_no
      this.qrcode = res.data.url_barcode_picture
      if (res.result === 'SUCCESS') {
        this.poPDF = res.data.PO_External
        this.prPDF = res.data.PR_External
        this.soPDF = res.data.SO_External
        this.QTorder = res.data.QT_order
        this.items = res.data
        if (this.items.coupon === null) {
          this.items.coupon = []
        }
        if (this.items.product_free === null) {
          this.items.product_free = []
        }
        this.cardProduct = this.items.data_list[0]
        if (this.items.receipt.length !== 0) {
          if (this.items.receipt[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.items.receipt[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.items.receipt[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.items.receipt[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.items.receipt[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else if (this.items.receipt[0].bankNo === 'CIMB') {
            this.bankName = 'ธนาคารซีไอเอ็มบี'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        this.trackingStatus = this.items.receipt
        if (this.isValidDate(this.items.receipt[0].created_at)) {
          this.created_at = new Date(
            this.items.receipt[0].created_at
          ).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        } else {
          this.created_at = '-'
        }
        if (this.isValidDate(this.items.tracking[0].time_step_1)) {
          this.dateCreateOrderStep1 = new Date(this.items.tracking[0].time_step_1).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        }
        if (this.isValidDate(this.items.tracking[0].time_step_2)) {
          this.dateCreateOrderStep2 = new Date(this.items.tracking[0].time_step_2).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        }
        // console.log('this.items.tracking[0].time_step_3', this.items.tracking[0].time_step_3)
        if (this.isValidDate(this.items.tracking[0].time_step_3)) {
          this.dateCreateOrderStep3 = new Date(this.items.tracking[0].time_step_3).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          })
        }
        if (this.isValidDate(this.items.tracking[0].time_step_4)) {
          this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
            // hour: 'numeric',
            // minute: 'numeric'
          })
        } else {
          this.dateCreateOrderStep4 = this.items.tracking[0].time_step_4
        }
        if (this.items.tracking.length !== 0) {
          if (this.items.tracking[0].status_tracking === 'Not Paid') {
            this.trackingText = 'ที่ต้องรอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Success') {
            this.trackingText = 'คำสั่งซื้อที่ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Sent') {
            this.trackingText = 'ที่ต้องจัดส่ง'
          } else if (this.items.tracking[0].status_tracking === 'Sent') {
            this.trackingText = 'จัดส่งแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Received') {
            this.trackingText = 'ได้รับสินค้าแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Received') {
            this.trackingText = 'ยังไม่ได้รับสินค้า'
          } else if (this.items.tracking[0].status_tracking === 'Cancel by approver') {
            this.trackingText = 'ยกเลิกโดยผู้อนุมัติ'
          } else if (this.items.tracking[0].status_tracking === 'Pick Up') {
            this.trackingText = 'รับที่หน้าร้าน'
          }
        } else {
          this.trackingText = ''
        }
        // this.overlay = false
        this.itemStatus = this.items.status
        if (this.itemStatus === 'ยกเลิก' || this.itemStatus === 'ดำเนินการแล้ว') {
          this.disablecancel = true
        }
        // this.$store.commit('closeLoader')
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else if (res.message === 'Not found!. The user is not seller in this shop.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: 'Not found!. The user is not seller in this shop.'
        })
        this.$router.push({ path: '/' })
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
      }
    },
    gotoCrediterm (val) {
      if (this.MobileSize) {
        this.$router.push({ path: `/companyListCreditTermMobile?order_number=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/companyListCreditTerm?order_number=${val}` }).catch(() => {})
      }
    },
    backtoPOBuyer () {
      if (this.MobileSize) {
        this.$router.push({ path: '/POSellerB2BListMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/POSellerB2BList' }).catch(() => {})
      }
    },
    GoToMobily (Track) {
      window.open(Track)
    },
    async getFranchise () {
      var Franchise = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      if (Franchise.can_use_function_in_company.payment !== undefined) {
        this.statusPayment = true
      } else {
        this.statusPayment = false
      }
    },
    async getItemProduct () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsDetailOrderPurchaser', this.paymentNumber)
      var res = await this.$store.state.ModuleAdminManage.stateDetailOrderPurchaser
      if (res.message === 'Get detail order purchaser success') {
        this.$store.commit('closeLoader')
        this.items = res.data
        if (this.items.receipt.length !== 0) {
          if (this.items.receipt[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.items.receipt[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.items.receipt[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.items.receipt[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.items.receipt[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        // ต่อ Flash
        // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (this.items.order_mobilyst_no !== '') {
          var response = {
            code: 1,
            data: {
              customaryPno: null,
              eSignature: null,
              origPno: 'TH014276DR4A',
              pno: 'TH014276DR4A',
              returnedPno: null,
              routes: null,
              state: 0,
              stateChangeAt: null,
              stateText: '',
              ticketPickupId: null
            },
            message: 'success'
          }
          // console.log('res in Flash', response)
          this.flashTrackingData = response.data
          if (this.flashTrackingData !== null) {
            this.mockupTracking = this.flashTrackingData
            if (this.flashTrackingData.routes === null) {
              this.step = 0
            } else {
              this.step = this.flashTrackingData.routes.length
            }
          } else {
            this.step = 0
          }
        } else {
          this.flashTrackingNo = ''
        }
        this.trackingStatus = this.items.tracking[0].status_tracking
        this.dateCreateOrderStep1 = new Date(this.items.tracking[0].time_step_1).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep2 = new Date(this.items.tracking[0].time_step_2).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep3 = new Date(this.items.tracking[0].time_step_3).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderCancel = new Date(this.items.tracking[0].time_cancel).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        // this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
        if (this.items.tracking.length !== 0) {
          if (this.items.tracking[0].status_tracking === 'Not Paid') {
            this.trackingText = 'ที่ต้องรอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Success') {
            this.trackingText = 'คำสั่งซื้อที่ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Sent') {
            this.trackingText = 'ที่ต้องจัดส่ง'
          } else if (this.items.tracking[0].status_tracking === 'Sent') {
            this.trackingText = 'จัดส่งแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Received') {
            this.trackingText = 'ได้รับสินค้าแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Received') {
            this.trackingText = 'ยังไม่ได้รับสินค้า'
          } else if (this.items.tracking[0].status_tracking === 'Cancel by approver') {
            this.trackingText = 'ยกเลิกโดยผู้อนุมัติ'
          } else if (this.items.tracking[0].status_tracking === 'Cancel') {
            this.trackingText = 'ยกเลิกคำสั่งซื้อ'
          }
        } else {
          this.trackingText = ''
        }
        this.CheckAcceptProduct()
        this.$store.commit('closeLoader')
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else {
        this.$store.commit('closeLoader')
        if (res.message !== 'ผู้ใช้งานนี้ถูกใช้งานอยู่') {
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: res.message
          })
        } else {
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: res.message
          })
          localStorage.removeItem('oneData')
          this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    refundProductBuyer (order) {
      this.$refs.ModalRefundProductBuyer.open(order, order.order_number, 'purchaser')
    },
    contactSeller () {
      // console.log('contact seller')
    },
    async CheckAcceptProduct () {
      var data = {
        payment_transaction_number: this.items.payment_transaction
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      this.checkAcceptProduct = res.data
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    async acceptProduct (order) {
      // อาจจะมีการเพิ่มค่าที่ส่งไป api ถ้าหากมีแยกหลาย order
      var data = {
        payment_transaction_number: this.items.payment_transaction,
        order_number: order.order_number,
        status: 'accepted'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      if (res.message === 'Update status success.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว'
        })
        this.getItemProduct()
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    isValidDate (dateObject) {
      return new Date(dateObject).toString() !== 'Invalid Date'
    },
    openModalReviewProduct (order, index) {
      const actions = 'create'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'purchaser')
    },
    openModalEditReviewProduct (order, index) {
      const actions = 'edit'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'purchaser')
    },
    async GoToPayment () {
      const PaymentID = {
        role_user: 'purchaser'
      }
      await this.$store.dispatch('ActionGetPaymentPageB2B', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPageB2B
      // console.log('respose paymenttttttttt', response)
      window.location.replace(response.data.link_url)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    getColor (item) {
      if (item === 'Pending') return '#FCF0DA'
      else if (item === 'Not Paid') return '#FCF0DA'
      else if (item === 'Success') return '#F0F9EE'
      else if (item === 'Approve') return '#F0F9EE'
      else if (item === 'Fail') return '#F7D9D9'
      else if (item === 'Credit Term') return '#E5EFFF'
      else if (item === 'Cash') return '#F0F9EE'
      else return '#F7D9D9'
    },
    getTextColor (item) {
      if (item === 'Pending') return '#E9A016'
      else if (item === 'Not Paid') return '#E9A016'
      else if (item === 'Success' || item === 'Waiting_Cancel') return '#1AB759'
      else if (item === 'Approve') return '#1AB759'
      else if (item === 'Fail') return '#D1392B'
      else if (item === 'Credit Term') return '#1B5DD6'
      else if (item === 'Cash') return '#1AB759'
      else return '#D1392B'
    },
    getTextColorTransportation (item) {
      if (item === 'อยู่ระหว่างดำเนินการ') return '#E9A016'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#1B5DD6'
      else if (item === 'ส่งคืนสินค้า') return '#D1392B'
      else return '#E9A016'
    },
    getStatus (item) {
      if (item === 'Pending') return 'รออนุมัติ'
      else if (item === 'Not Paid') return 'รอชำระเงิน'
      else if (item === 'Success' || item === 'Waiting_Cancel') return 'ชำระเงินสำเร็จ'
      else if (item === 'Approve') return 'วางบิล'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Credit Term') return 'ชำระเงินแบบเครดิตเทอม'
      else if (item === 'Cash') return 'ชำระเงินสด'
      else return 'ยกเลิกคำสั่งซื้อ'
    },
    goToRefundPage () {
      localStorage.setItem('orderNumber', this.ordernumber)
      localStorage.setItem('transactionNumber', this.transactionNumber)
      localStorage.setItem('termNumber', this.termNumber)
      this.$router.push({ path: '/refundpage' }).catch(() => {})
    },
    closemodalInputReasonRefund (val) {
      if (val === 'details') {
        this.modalInputReasonRefund = false
      } else {
        this.reasonRejectRefund = ''
        this.modalrejectrefund = false
        this.modalInputReasonRefund = true
      }
    },
    openModalRejectRefund () {
      this.modalInputReasonRefund = false
      this.modalrejectrefund = true
    },
    async getDataSubmitOrder (items) {
      this.itemOrder = items
      this.listAccount = []
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      if (response.code === 200) {
        this.listAccount = await response.data
        // this.listAccount = response.data.map((item) => ({
        //   text: item.name
        // }))
        // console.log('see', this.listAccount)
        await this.getDetailCancel()
        this.modalInputReasonRefund = true
      } else {
        this.listAccount = []
        await this.getDetailCancel()
        this.modalInputReasonRefund = true
      }
      // await this.$store.dispatch('actionsListBank')
      // var response = await this.$store.state.ModuleShop.stateListBank
      // if (response.code === 200) {
      //   this.listAccount = response.data
      //   console.log(response, 'listAccount')
      //   // this.listAccount = response.data.map((item) => ({
      //   //   text: item.name
      //   // }))
      //   // console.log('see', this.listAccount)
      // } else {
      //   this.listAccount = []
      // }
    },
    openDialogConfirmCancelRefund (val) {
      this.cancelType = ''
      this.cancelType = val
      this.modalrejectrefund = false
      this.modalInputReasonRefund = false
      this.modalAwaitCancelOrderRefund = true
    },
    closeDialogConfirmCancelRefund () {
      this.modalAwaitCancelOrderRefund = false
    },
    async closeSuccessCancelOrderRefund () {
      this.modalSuccessCancelOrderRefund = false
      await this.getItemProductB2B()
      await this.getOrderDocument()
      await this.getDetailCancel()
    },
    async confirmCancelRefund () {
      this.modalAwaitCancelOrderRefund = false
      if (this.cancelType === 'cancel') {
        this.$store.commit('openLoader')
        const data = {
          order_number: this.paymentNumber.payment_transaction_number,
          status: 'reject',
          remark_seller: this.reasonRejectRefund
        }
        await this.$store.dispatch('actionApproveAndRejectOrder', data)
        var response = this.$store.state.ModuleOrder.stateApproveAndRejectOrder
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalSuccessCancelOrderRefund = true
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: response.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else if (this.cancelType === 'submit') {
        this.$store.commit('openLoader')
        const data = {
          order_number: this.paymentNumber.payment_transaction_number,
          status: 'approve',
          remark_seller: this.reasonRejectRefund
        }
        await this.$store.dispatch('actionApproveAndRejectOrder', data)
        var responseNopayment = this.$store.state.ModuleOrder.stateApproveAndRejectOrder
        if (responseNopayment.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalSuccessCancelOrderRefund = true
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: responseNopayment.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          text: 'ไม่สามารถยกออร์เดอร์ได้',
          showConfirmButton: false,
          timer: 1500
        })
      }
      // const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      // var data = ''
      // data = {
      //   payment_transaction_number: this.itemOrder.payment_transaction,
      //   order_number: this.itemOrder.order_number,
      //   seller_shop_id: shopDetail.id,
      //   role_user: 'seller',
      //   status: this.getStatusOrder,
      //   reason: this.reason
      // }
      // await this.$store.dispatch('actionsAccecptProduct', data)
      // const resposnse = await this.$store.state.ModuleShop.stateAccecptProduct
      // if (resposnse.result === 'SUCCESS') {
      //   this.modalSuccessCancelOrder = true
      // } else {
      //   this.$swal.fire({
      //     icon: 'error',
      //     text: resposnse.message,
      //     showConfirmButton: false,
      //     timer: 1500
      //   })
      // }
    },
    async getDetailCancel () {
      this.$store.commit('openLoader')
      const data = {
        order_number: this.paymentNumber.payment_transaction_number
      }
      await this.$store.dispatch('actionCancelOrderDetails', data)
      var response = this.$store.state.ModuleOrder.stateCancelOrderDetails
      if (response.result === 'SUCCESS') {
        this.detailCancel = response.data
        // เช็คจาก key status
        if (this.detailCancel[0].status !== '') {
          // ให้เซ็ตค่า
          this.timeStampCancel = this.detailCancel[0].updated_at
          this.statusCancel = this.detailCancel[0].status
          this.urName = this.detailCancel[0].account_name
          this.bankNameRefund = this.detailCancel[0].bank_code
          this.telNumber = this.detailCancel[0].phone
          this.bankNumberRefund = this.detailCancel[0].account_no
          this.reasonRefund = this.detailCancel[0].remark_buyer
        } else {
          this.timeStampCancel = ''
          this.urName = ''
          this.bankNameRefund = ''
          this.telNumber = ''
          this.bankNumberRefund = ''
          this.reasonRefund = this.detailCancel[0].remark_buyer
        }
      } else {
        this.timeStampCancel = ''
        this.statusCancel = ''
        this.urName = ''
        this.bankNameRefund = ''
        this.telNumber = ''
        this.bankNumberRefund = ''
        this.reasonRefund = ''
      }
      this.$store.commit('closeLoader')
    },
    openDialogRemark (data) {
      this.remarkRecurring = data
      this.modalRemark = true
    }
  }
}
</script>

<style>
.v-data-table.row-height-64 td {
  height: 80px !important;
}
</style>

<style lang="css" scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
/* .v-application .mb-12 {
    margin-bottom: 12px !important;
} */
::v-deep .ant-table-pagination {
  display: none;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.bgShippingUPS {
  background-color: #F3F5F7;
}
.fontActive {
  color: #27AB9C;
}
.fontInactive {
  color: #A6A6A6;
}
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 18px;
}
.fontSizeTotalPriceMobile {
  font-size: 16px;
}
.fontSizeAddressDetail {
  font-size: 16px;
}
.buttonFontSize {
  font-size: 14px;
  font-weight: normal;
}
.captionSku {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
.fontSizeDetail {
  font-size: 14px;
}
.fontSizeDetailMobile {
  font-size: 12px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>

<style scoped>

.couponIMGDesk{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  background-size: contain;
  padding: 1%;
  box-shadow: 5px 5px 5px 0px gray;
  /* height: 200px; */
  /* width: 250px; */
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
  box-shadow: 5px 5px 5px 0px gray;
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  background-size: cover;
  padding: 1%;
  box-shadow: 5px 5px 5px 0px gray;
}
</style>
