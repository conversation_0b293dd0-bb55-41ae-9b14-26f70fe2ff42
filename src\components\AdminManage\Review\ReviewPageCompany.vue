<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">การประเมินความพึงพอใจ</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> การประเมินความพึงพอใจ</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="px-2 py-0">
          <a-tabs @change="SelectReviewTabs">
            <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
            <a-tab-pane key="ยังไม่ได้ประเมิน"><span slot="tab">ยังไม่ได้ประเมิน <a-tag color="#E9A016" style="border-radius: 8px;">{{ countReviewWaiting }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ประเมินแล้ว"><span slot="tab">ประเมินแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countReviewSuccess }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-1 mt-1' : 'pl-2 pr-2 mb-3'">
          <v-text-field v-model="search" dense hide-details outlined rounded placeholder="ค้นหาจากรหัสการสั่งซื้อสินค้า">
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-4 pr-3 mt-3' : 'pl-2 pr-2 mb-3 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 'ยังไม่ได้ประเมิน'">รายการยังไม่ได้ประเมินทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ประเมินแล้ว'">รายการประเมินแล้วทั้งหมด {{ showCountOrder }} รายการ</span>
        </v-col>
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-4 my-5" min-height="512">
            <v-data-table
              :headers="StateStatus === 'ยังไม่ได้ประเมิน' ? headers : headersAssess"
              :items="DataTable"
              :search="search"
              :custom-filter="customFilter"
              style="width:100%;"
              height="100%"
              @pagination="countOrdar"
              no-results-text="ไม่มีข้อมูลการประเมินความพึงพอใจสินค้าที่ค้นหา"
              no-data-text="ไม่มีข้อมูลการประเมินความพึงพอใจสินค้าในตาราง"
              :update:items-per-page="getItemPerPage"
              class=""
              :items-per-page="10"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <!-- <template v-slot:[`item.product_image`]="{ item }">
                <v-img v-if="item.product_image !== ''" :src="`${item.product_image}`" contain width="70" height="70"/>
                <v-img v-else src="@/assets/NoImage.png" width="70" height="70"/>
              </template> -->
              <template v-slot:[`item.expired_review`]="{ item }">
                <span v-if="item.expired_review === '-'"> - </span>
                <span v-else >{{new Date(item.expired_review).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
              </template>
              <template v-slot:[`item.invoice`]="{ item }">
                <div v-if="item">
                  -
                </div>
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'reviewed'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0F9EE" text-color="#1AB759">ประเมินความพึงพอใจแล้ว</v-chip>
                </span>
                <span v-else-if="item.status === 'waiting_review'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FCF0DA" text-color="#E9A016">รอการประเมินความพึงพอใจ</v-chip>
                </span>
              </template>
              <template v-slot:[`item.actions`]="{ item }">
                <v-btn v-if="item.status === 'reviewed'" color="#27AB9C" class="white--text" small @click="viewDetail(item)">
                  รายละเอียด
                </v-btn>
                <v-btn v-else color="#52C41A" class="white--text" small @click="review(item)">
                  ประเมินความพึงพอใจ
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12"  v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h3 v-if="IpadSize" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการประเมินความพึงพอใจที่{{ StateStatus }}</b></h3>
          <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการประเมินความพึงพอใจที่{{ StateStatus }}</b></h2>
        </v-col>
      </v-row>
    </v-card>
    <ModalReviewProduct ref="ModalReviewProduct" :style="MobileSize ? 'z-index: 16000004' : ''" />
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    ModalReviewProduct: () => import(/* webpackPrefetch: true */ '@/components/UserProfile/ModalReview/ReviewProduct')
  },
  data () {
    return {
      disableTable: false,
      search: '',
      reviewList: {},
      StateStatus: 'ยังไม่ได้ประเมิน',
      keyCheckHead: 0,
      headers: [
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', align: 'center', sortable: false, width: '140', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ประเมินภายในวันที่', value: 'expired_review', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--textfontSizeDetail ' },
        { text: 'จัดการ', value: 'actions', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersAssess: [
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', align: 'center', sortable: false, width: '140', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'แก้ไขภายในวันที่', value: 'expired_review', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'actions', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      countReviewWaiting: 0,
      countReviewSuccess: 0,
      countRefundAll: 0,
      showCountOrder: 0,
      pageCount: 5,
      page: 1,
      itemsPerPage: 10,
      DataTable: [],
      dataRole: '',
      companyId: null,
      companyData: []
    }
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.companyId = this.companyData.id
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.ListRefundDataTableCompany()
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('SentGetReview', this.refreshData)
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    StateStatus (val) {
      // console.log('val', val)
      if (val === 'ยังไม่ได้ประเมิน') {
        this.DataTable = this.reviewList.order_waiting_review !== undefined ? this.reviewList.order_waiting_review : []
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ประเมินแล้ว') {
        this.DataTable = this.reviewList.order_review !== undefined ? this.reviewList.order_review : []
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/reviewCompanyMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/reviewCompany' }).catch(() => { })
      }
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    customFilter (items, search, filters) {
      var searchByOrderNumber = filters.order_number.indexOf(search) !== -1
      return searchByOrderNumber
    },
    refreshData () {
      this.ListRefundDataTableCompany()
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    },
    SelectReviewTabs (item) {
      // console.log('SelectReviewTabs', item)
      this.StateStatus = item
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async ListRefundDataTableCompany () {
      this.$store.commit('openLoader')
      // console.log('this.companyId.id', this.companyId)
      this.dataRole.role = 'purchaser'
      // console.log('this.dataRole.role', this.dataRole.role)
      var companyID
      if (this.dataRole.role === 'ext_buyer') {
        companyID = '-1'
      } else if (this.dataRole.role === 'purchaser') {
        companyID = this.companyId
      }
      var dataSent = {
        role_user: this.dataRole.role,
        company_id: companyID
      }
      await this.$store.dispatch('actionsListOrderReviewBuyer', dataSent)
      var res = await this.$store.state.ModuleReviewBuyer.stateListOrderReviewBuyer
      if (res.message === 'Get list reviews successful.') {
        this.$store.commit('closeLoader')
        this.reviewList = res.data
        this.countReviewWaiting = this.reviewList.order_waiting_review.length
        this.countReviewSuccess = this.reviewList.order_review.length
        if (this.StateStatus === 'ยังไม่ได้ประเมิน') {
          this.DataTable = this.reviewList.order_waiting_review
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 'ประเมินแล้ว') {
          this.DataTable = this.reviewList.order_review
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      }
    },
    viewDetail (item) {
      // console.log('viewDetail', item)
      const actions = 'edit'
      this.$refs.ModalReviewProduct.open(item, item.order_number, actions, 'purchaser')
      // this.$router.push({ path: '/returnDetail' }).catch(() => {})
    },
    review (item) {
      // console.log('review', item)
      const actions = 'create'
      this.$refs.ModalReviewProduct.open(item, item.order_number, actions, 'purchaser')
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
