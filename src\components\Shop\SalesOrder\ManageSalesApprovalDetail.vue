<template>
  <v-container>
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title
        v-if="!MobileSize"
        style="
          font-weight: bold;
          font-size: 24px;
          line-height: 32px;
          color: #333333;
        "
        ><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()"
          >mdi-chevron-left</v-icon
        >{{ approvalTitle }}</v-card-title
      >
      <v-card-title
        v-else
        class="px-0"
        style="
          font-weight: bold;
          font-size: 18px;
          line-height: 32px;
          color: #333333;
        "
        ><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()"
          >mdi-chevron-left</v-icon
        >{{ approvalTitle }}</v-card-title
      >
      <v-card-text>
        <v-row no-gutters>
          <v-col cols="12" md="1" class="d-flex align-center">
            <p style="font-weight: 600; font-size: 16px; color: #333333">
              ชื่อรูปแบบ :
            </p>
          </v-col>
          <v-col cols="12" md="3">
            <v-text-field
              v-model="name"
              @keypress="CheckSpacebarName($event)"
              style="border-radius: 8px"
              outlined
              dense
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="8" v-if="!MobileSize">
            <v-row no-gutters justify="end">
              <v-btn
                rounded
                width="125"
                height="40"
                class="white--text my-auto"
                color="#27AB9C"
                :disabled="approverA.length === 0 || name === ''"
                @click="
                  Status === 'edit'
                    ? openAwaitModal('edit')
                    : openAwaitModal('create')
                "
                >บันทึก</v-btn
              >
            </v-row>
          </v-col>
        </v-row>
        <!-- ผู้อนุมัติลำดับที่ : 1 -->
        <v-card elevation="0" class="" style="border: solid 1px #cccccc">
          <v-row class="pa-10">
            <v-row no-gutters v-if="!MobileSize">
              <v-col cols="12" md="6">
                <p style="font-weight: 600; font-size: 16px; color: #333333">
                  ผู้อนุมัติลำดับที่ : 1 <span style="color: red">สำหรับเซ็นในช่อง ออกโดย</span>
                </p>
              </v-col>
              <v-col cols="12" md="6">
                <v-row no-gutters justify="end" v-if="CheckEditA === false">
                  <v-btn
                    class="mr-8 d-flex"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="CheckApprover('ApproverA')"
                    :disabled="approverA.length > 4"
                    ><v-icon small class="mr-2">mdi-plus</v-icon>เพิ่ม</v-btn
                  >
                  <v-btn
                    :disabled="approverA.length === 0"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('EditA')"
                    ><v-icon small class="mr-2">mdi-pencil</v-icon>แก้ไข</v-btn
                  >
                </v-row>
                <v-row no-gutters justify="end" v-else>
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('CheckEditA')"
                    ><v-icon small class="mr-2">mdi-close</v-icon>ยกเลิก</v-btn
                  >
                </v-row>
              </v-col>
              <v-row class="mt-1">
                <v-col
                  cols="4"
                  class="ma-0"
                  v-for="(item, index) of approverA"
                  :key="index"
                >
                  <v-card>
                    <v-card-text>
                      <v-row no-gutters justify="end">
                        <v-col cols="12" md="3">
                          <v-avatar size="60">
                            <v-img
                              v-if="item.img_path !== null"
                              :src="item.img_path"
                            ></v-img>
                            <v-img v-else src="@/assets/NoImage.png"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="12" md="9">
                          <p>
                            ชื่อ: <b>{{ item.name }}</b>
                          </p>
                          <p>
                            อีเมล: <b>{{ item.email }}</b>
                          </p>
                        </v-col>
                        <v-btn
                          :disabled="
                            approverA.length === 1 &&
                            approverB.length !== 0 &&
                            approverC.length !== 0
                          "
                          v-if="Action === 'EditA'"
                          rounded
                          small
                          outlined
                          color="red"
                          @click="Delete(index, item)"
                          ><v-icon>mdi-delete</v-icon></v-btn
                        >
                        <!-- <v-btn text elevation="0">
                        <u style="color:#27AB9C; font-weight:600; font-size:18;">รายละเอียด</u>
                      </v-btn> -->
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-row>
            <v-row no-gutters v-else>
              <v-col cols="12">
                <p style="font-weight: 600; font-size: 16px; color: #333333">
                  ผู้อนุมัติลำดับที่ : 1 <span style="color: red">สำหรับเซ็นในช่อง ออกโดย</span>
                </p>
              </v-col>
              <v-col cols="12">
                <v-row no-gutters justify="center" v-if="CheckEditA === false">
                  <v-btn
                    class="mr-8 d-flex"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="CheckApprover('ApproverA')"
                    :disabled="approverA.length > 4"
                    ><v-icon small class="mr-2">mdi-plus</v-icon>เพิ่ม</v-btn
                  >
                  <v-btn
                    :disabled="approverA.length === 0"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('EditA')"
                    ><v-icon small class="mr-2">mdi-pencil</v-icon>แก้ไข</v-btn
                  >
                </v-row>
                <v-row no-gutters justify="center" v-else>
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('CheckEditA')"
                    ><v-icon small class="mr-2">mdi-close</v-icon>ยกเลิก</v-btn
                  >
                </v-row>
              </v-col>
              <v-row class="mt-1">
                <v-col
                  cols="12"
                  class="ma-0"
                  v-for="(item, index) of approverA"
                  :key="index"
                >
                  <v-card>
                    <v-card-text>
                      <v-row no-gutters justify="end">
                        <v-col cols="12">
                          <v-avatar size="60">
                            <v-img
                              v-if="item.img_path !== null"
                              :src="item.img_path"
                            ></v-img>
                            <v-img v-else src="@/assets/NoImage.png"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="12" class="mt-3">
                          <p>
                            ชื่อ: <b>{{ item.name }}</b>
                          </p>
                          <p>
                            อีเมล: <b>{{ item.email }}</b>
                          </p>
                        </v-col>
                        <v-btn
                          :disabled="
                            approverA.length === 1 &&
                            approverB.length !== 0 &&
                            approverC.length !== 0
                          "
                          v-if="Action === 'EditA'"
                          rounded
                          small
                          outlined
                          color="red"
                          @click="Delete(index, item)"
                          ><v-icon>mdi-delete</v-icon></v-btn
                        >
                        <!-- <v-btn text elevation="0">
                        <u style="color:#27AB9C; font-weight:600; font-size:18;">รายละเอียด</u>
                      </v-btn> -->
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-row>
          </v-row>
        </v-card>
        <!-- ผู้อนุมัติลำดับที่ : 2 -->
        <v-card elevation="0" style="border: solid 1px #cccccc" class="mt-2">
          <v-row class="pa-10">
            <v-row no-gutters>
              <v-col cols="12" md="6">
                <p style="font-weight: 600; font-size: 16px; color: #333333">
                  ผู้อนุมัติลำดับที่ : 2 <span style="color: red">สำหรับเซ็นในช่อง ผู้มีอำนาจอนุมัติ</span>
                </p>
              </v-col>
              <v-col cols="12" md="6" v-if="!MobileSize">
                <v-row no-gutters justify="end" v-if="CheckEditB === false">
                  <v-btn
                    class="mr-8 d-flex"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="CheckApprover('ApproverB')"
                    :disabled="approverA.length === 0"
                    ><v-icon small class="mr-2">mdi-plus</v-icon>เพิ่ม</v-btn
                  >
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('EditB')"
                    :disabled="approverB.length === 0"
                    ><v-icon small class="mr-2">mdi-pencil</v-icon>แก้ไข</v-btn
                  >
                </v-row>
                <v-row no-gutters justify="end" v-else>
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('CheckEditB')"
                    ><v-icon small class="mr-2">mdi-close</v-icon>ยกเลิก</v-btn
                  >
                </v-row>
              </v-col>
              <v-col cols="12" md="6" v-else>
                <v-row no-gutters justify="center" v-if="CheckEditB === false">
                  <v-btn
                    class="mr-8 d-flex"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="CheckApprover('ApproverB')"
                    :disabled="approverA.length === 0"
                    ><v-icon small class="mr-2">mdi-plus</v-icon>เพิ่ม</v-btn
                  >
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('EditB')"
                    :disabled="approverB.length === 0"
                    ><v-icon small class="mr-2">mdi-pencil</v-icon>แก้ไข</v-btn
                  >
                </v-row>
                <v-row no-gutters justify="center" v-else>
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('CheckEditB')"
                    ><v-icon small class="mr-2">mdi-close</v-icon>ยกเลิก</v-btn
                  >
                </v-row>
              </v-col>
              <v-row class="mt-1" v-if="!MobileSize">
                <v-col
                  cols="4"
                  class="ma-0"
                  v-for="(item, index) of approverB"
                  :key="index"
                >
                  <v-card>
                    <v-card-text>
                      <v-row no-gutters justify="end">
                        <v-col cols="12" md="3">
                          <v-avatar size="60">
                            <v-img
                              v-if="item.img_path !== null"
                              :src="item.img_path"
                            ></v-img>
                            <v-img v-else src="@/assets/NoImage.png"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="12" md="9">
                          <p>
                            ชื่อ: <b>{{ item.name }}</b>
                          </p>
                          <p>
                            อีเมล: <b>{{ item.email }}</b>
                          </p>
                        </v-col>
                        <!-- approverA.length === 1 && approverB.length !== 0 && approverC.length !== 0 -->
                        <v-btn
                          :disabled="
                            approverB.length === 1 && approverC.length !== 0
                          "
                          v-if="Action === 'EditB'"
                          rounded
                          small
                          outlined
                          color="red"
                          @click="Delete(index, item)"
                          ><v-icon>mdi-delete</v-icon></v-btn
                        >
                        <!-- <v-btn text elevation="0">
                        <u style="color:#27AB9C; font-weight:600; font-size:18;">รายละเอียด</u>
                      </v-btn> -->
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <v-row class="mt-1" v-else>
                <v-col
                  cols="12"
                  class="ma-0"
                  v-for="(item, index) of approverB"
                  :key="index"
                >
                  <v-card>
                    <v-card-text>
                      <v-row no-gutters justify="end">
                        <v-col cols="12">
                          <v-avatar size="60">
                            <v-img
                              v-if="item.img_path !== null"
                              :src="item.img_path"
                            ></v-img>
                            <v-img v-else src="@/assets/NoImage.png"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="12" class="mt-3">
                          <p>
                            ชื่อ: <b>{{ item.name }}</b>
                          </p>
                          <p>
                            อีเมล: <b>{{ item.email }}</b>
                          </p>
                        </v-col>
                        <!-- approverA.length === 1 && approverB.length !== 0 && approverC.length !== 0 -->
                        <v-btn
                          :disabled="
                            approverB.length === 1 && approverC.length !== 0
                          "
                          v-if="Action === 'EditB'"
                          rounded
                          small
                          outlined
                          color="red"
                          @click="Delete(index, item)"
                          ><v-icon>mdi-delete</v-icon></v-btn
                        >
                        <!-- <v-btn text elevation="0">
                        <u style="color:#27AB9C; font-weight:600; font-size:18;">รายละเอียด</u>
                      </v-btn> -->
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-row>
          </v-row>
        </v-card>
        <!-- ผู้อนุมัติลำดับที่ : 3 -->
        <v-card elevation="0" style="border: solid 1px #cccccc" class="mt-2">
          <v-row class="pa-10">
            <v-row no-gutters>
              <v-col cols="12" md="6">
                <p style="font-weight: 600; font-size: 16px; color: #333333">
                  ผู้อนุมัติลำดับที่ : 3
                </p>
              </v-col>
              <v-col cols="12" md="6" v-if="!MobileSize">
                <v-row no-gutters justify="end" v-if="CheckEditC === false">
                  <v-btn
                    class="mr-8"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="CheckApprover('ApproverC')"
                    :disabled="approverB.length === 0"
                    ><v-icon small class="mr-2">mdi-plus</v-icon>เพิ่ม</v-btn
                  >
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('EditC')"
                    :disabled="approverC.length === 0"
                    ><v-icon small class="mr-2">mdi-pencil</v-icon>แก้ไข</v-btn
                  >
                </v-row>
                <v-row no-gutters justify="end" v-else>
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('CheckEditC')"
                    ><v-icon small class="mr-2">mdi-close</v-icon>ยกเลิก</v-btn
                  >
                </v-row>
              </v-col>
              <v-col cols="12" md="6" v-else>
                <v-row no-gutters justify="center" v-if="CheckEditC === false">
                  <v-btn
                    class="mr-8"
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="CheckApprover('ApproverC')"
                    :disabled="approverB.length === 0"
                    ><v-icon small class="mr-2">mdi-plus</v-icon>เพิ่ม</v-btn
                  >
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('EditC')"
                    :disabled="approverC.length === 0"
                    ><v-icon small class="mr-2">mdi-pencil</v-icon>แก้ไข</v-btn
                  >
                </v-row>
                <v-row no-gutters justify="center" v-else>
                  <v-btn
                    rounded
                    outlined
                    color="#27AB9C"
                    @click="Edit('CheckEditC')"
                    ><v-icon small class="mr-2">mdi-close</v-icon>ยกเลิก</v-btn
                  >
                </v-row>
              </v-col>
              <v-row class="mt-1" v-if="!MobileSize">
                <v-col
                  cols="4"
                  class="ma-0"
                  v-for="(item, index) of approverC"
                  :key="index"
                >
                  <v-card>
                    <v-card-text>
                      <v-row no-gutters justify="end">
                        <v-col cols="12" md="3">
                          <v-avatar size="60">
                            <v-img
                              v-if="item.img_path !== null"
                              :src="item.img_path"
                            ></v-img>
                            <v-img v-else src="@/assets/NoImage.png"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="12" md="9">
                          <p>
                            ชื่อ: <b>{{ item.name }}</b>
                          </p>
                          <p>
                            อีเมล: <b>{{ item.email }}</b>
                          </p>
                        </v-col>
                        <v-btn
                          v-if="Action === 'EditC'"
                          rounded
                          small
                          outlined
                          color="red"
                          @click="Delete(index, item)"
                          ><v-icon>mdi-delete</v-icon></v-btn
                        >
                        <!-- <v-btn text elevation="0">
                        <u style="color:#27AB9C; font-weight:600; font-size:18;">รายละเอียด</u>
                      </v-btn> -->
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <v-row class="mt-1" v-else>
                <v-col
                  cols="12"
                  class="ma-0"
                  v-for="(item, index) of approverC"
                  :key="index"
                >
                  <v-card>
                    <v-card-text>
                      <v-row no-gutters justify="center">
                        <v-col cols="12">
                          <v-avatar size="60">
                            <v-img
                              v-if="item.img_path !== null"
                              :src="item.img_path"
                            ></v-img>
                            <v-img v-else src="@/assets/NoImage.png"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="12" class="mt-3">
                          <p>
                            ชื่อ: <b>{{ item.name }}</b>
                          </p>
                          <p>
                            อีเมล: <b>{{ item.email }}</b>
                          </p>
                        </v-col>
                        <v-btn
                          v-if="Action === 'EditC'"
                          rounded
                          small
                          outlined
                          color="red"
                          @click="Delete(index, item)"
                          ><v-icon>mdi-delete</v-icon></v-btn
                        >
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-row>
          </v-row>
        </v-card>
        <v-row no-gutters justify="end" class="mt-3" v-if="MobileSize">
          <v-btn
            rounded
            width="125"
            height="40"
            class="white--text my-auto"
            color="#27AB9C"
            :disabled="approverA.length === 0 || name === ''"
            @click="
              Status === 'edit'
                ? openAwaitModal('edit')
                : openAwaitModal('create')
            "
            >บันทึก</v-btn
          >
        </v-row>
      </v-card-text>
    </v-card>
    <v-dialog
    persistent
      v-model="ShowSelectApprover"
      :width="MobileSize ? '100%' : IpadSize ? '100%' : '600'"
    >
      <v-card
        elevation="0"
        style="background: #ffffff; border-radius: 24px; overflow-x: hidden"
      >
        <v-card-text class="px-0 pt-0">
          <div
            :style="
              MobileSize
                ? 'width: 100%'
                : IpadSize
                ? 'width: 100%'
                : 'width: 600px'
            "
            class="backgroundHead"
            style="position: absolute; height: 120px"
          >
            <v-row style="height: 120px">
              <v-col style="text-align: center" class="pt-4">
                <span
                  :class="
                    MobileSize
                      ? 'title-mobile white--text'
                      : 'title white--text'
                  "
                  :style="MobileSize ? 'font-size: 18px' : 'font-size: 24px'"
                >
                  <b>รายละเอียด</b>
                </span>
              </v-col>
              <v-btn
                fab
                small
                @click="cancel('ShowSelectApprover')"
                icon
                class="mt-3"
              >
                <v-icon color="white">mdi-close</v-icon>
              </v-btn>
            </v-row>
          </div>
          <div
            style="
              position: relative;
              padding: 0px 12px 0px;
              display: flex;
              padding-top: 60px;
            "
          >
            <v-row
              :width="MobileSize ? '100%' : '782px'"
              style="
                height: 50px;
                border-radius: 24px 24px 0px 0px;
                background: #ffffff;
              "
            >
              <v-col style="text-align: center"> </v-col>
            </v-row>
          </div>
          <div class="" style="position: relative">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #ffffff; border-radius: 20px 20px 0px 0px"
              :style="
                MobileSize
                  ? 'padding: 20px 20px 10px 20px;'
                  : 'padding: 40px 48px 10px 48px;'
              "
            >
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" class="d-flex" v-if="!MobileSize">
                    <v-row dense class="mr-auto">
                      <v-img
                        src="@/assets/Create_Store/partnerShopDetail.png"
                        max-height="62"
                        max-width="62"
                      ></v-img>
                      <span
                        class="pt-5 pl-4"
                        style="
                          font-weight: 600;
                          color: #333333;
                          font-size: 16px;
                        "
                        v-if="Action === 'A'"
                      >
                        รายชื่อผู้อนุมัติ ลำดับที่ 1
                      </span>
                      <span
                        class="pt-5 pl-4"
                        style="
                          font-weight: 600;
                          color: #333333;
                          font-size: 16px;
                        "
                        v-if="Action === 'B'"
                      >
                        รายชื่อผู้อนุมัติ ลำดับที่ 2
                      </span>
                      <span
                        class="pt-5 pl-4"
                        style="
                          font-weight: 600;
                          color: #333333;
                          font-size: 16px;
                        "
                        v-if="Action === 'C'"
                      >
                        รายชื่อผู้อนุมัติ ลำดับที่ 3
                      </span>
                    </v-row>
                  </v-col>
                </v-row>
                <v-col cols="12" md="12" v-if="!MobileSize && !IpadSize">
                  <p :style="MobileSize ? 'font-size:16px' : 'font-size:18px'">
                    ค้นหารายชื่อผู้อนุมัติ
                  </p>
                  <v-text-field
                    v-model="search"
                    style="border-radius: 8px"
                    append-icon="mdi-magnify"
                    placeholder="ค้นหารายชื่อผู้อนุมัติ"
                    outlined
                    dense
                  ></v-text-field>
                  <v-card
                    elevation="0"
                    style="border: solid 1px #cccccc"
                    class="mt-2"
                  >
                    <v-card
                      class="pa-4"
                      elevation="0"
                      v-for="(item, index) of DetailApproverFilter"
                      :key="index"
                    >
                      <v-row
                        no-gutters
                        class="pa-4"
                        style="border: solid 1px #cccccc; cursor: pointer"
                        @click="addToListApprover(item)"
                      >
                        <v-col
                          cols="4"
                          style="border: solid 1px #cccccc"
                          class="d-flex align-center"
                        >
                          <v-container style="">
                            <v-img
                              v-if="item.img_path === null"
                              class="rounded-lg"
                              src="@/assets/NoImage.png"
                              height="100%"
                            ></v-img>
                            <v-img
                              v-else
                              class="rounded-lg"
                              :src="item.img_path"
                              contain
                            ></v-img>
                          </v-container>
                        </v-col>
                        <v-col cols="8" class="pa-2">
                          <p style="">
                            ชื่อ-สกุล:<b>{{ item.name }}</b>
                          </p>
                          <p style="">
                            อีเมล:<b>{{ item.email }}</b>
                          </p>
                          <p style="">
                            เบอร์โทรศัพท์:<b>{{ item.phone }}</b>
                          </p>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-card>
                </v-col>
                <v-col cols="12" md="12" v-if="MobileSize">
                  <p :style="MobileSize ? 'font-size:16px' : 'font-size:20px'">
                    ค้นหารายชื่อผู้อนุมัติ
                  </p>
                  <v-text-field
                    v-model="search"
                    style="border-radius: 8px"
                    append-icon="mdi-magnify"
                    placeholder="ค้นหารายชื่อผู้อนุมัติ"
                    outlined
                    dense
                  ></v-text-field>
                  <v-card
                    elevation="0"
                    style="border: solid 1px #cccccc"
                    class="mt-2"
                  >
                    <v-card
                      class="pa-4"
                      elevation="0"
                      v-for="(item, index) of DetailApproverFilter"
                      :key="index"
                    >
                      <v-row
                        no-gutters
                        class="pa-4"
                        style="border: solid 1px #cccccc; cursor: pointer"
                        @click="addToListApprover(item)"
                      >
                        <v-col
                          cols="12"
                          style="border: solid 1px #cccccc"
                          class="d-flex align-center"
                        >
                          <v-container style="">
                            <v-img
                              v-if="item.img_path === null"
                              class="rounded-lg"
                              src="@/assets/NoImage.png"
                              height="100%"
                            ></v-img>
                            <v-img
                              v-else
                              height="150"
                              class="rounded-lg"
                              :src="item.img_path"
                              contain
                            ></v-img>
                          </v-container>
                        </v-col>
                        <v-col cols="12" class="pa-2">
                          <p style="">
                            ชื่อ-สกุล:<b>{{ item.name }}</b>
                          </p>
                          <p style="">
                            อีเมล:<b>{{ item.email }}</b>
                          </p>
                          <p style="">
                            เบอร์โทรศัพท์:<b>{{ item.phone }}</b>
                          </p>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-card>
                </v-col>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
    persistent
      v-model="SelectApprover"
      :width="MobileSize ? '100%' : IpadSize ? '100%' : '600'"
    >
      <v-card
        elevation="0"
        style="background: #ffffff; border-radius: 24px; overflow-x: hidden"
      >
        <v-card-text class="px-0 pt-0">
          <div
            :style="
              MobileSize
                ? 'width: 100%'
                : IpadSize
                ? 'width: 100%'
                : 'width: 600px'
            "
            class="backgroundHead"
            style="position: absolute; height: 120px"
          >
            <v-row style="height: 120px">
              <v-col style="text-align: center" class="pt-4">
                <span
                  :class="
                    MobileSize
                      ? 'title-mobile white--text'
                      : 'title white--text'
                  "
                  :style="MobileSize ? 'font-size: 20px' : 'font-size: 22px'"
                >
                  <b>รายละเอียด</b>
                </span>
              </v-col>
              <v-btn
                fab
                small
                @click="cancel('SelectApprover')"
                icon
                class="mt-3"
              >
                <v-icon color="white">mdi-close</v-icon>
              </v-btn>
            </v-row>
          </div>
          <div
            style="
              position: relative;
              padding: 0px 12px 0px;
              display: flex;
              padding-top: 60px;
            "
          >
            <v-row
              :width="MobileSize ? '100%' : '782px'"
              style="
                height: 50px;
                border-radius: 24px 24px 0px 0px;
                background: #ffffff;
              "
            >
              <v-col style="text-align: center"> </v-col>
            </v-row>
          </div>
          <div class="" style="position: relative">
            <v-card
              elevation="0"
              width="100%"
              height="100%"
              style="background: #ffffff; border-radius: 20px 20px 0px 0px"
              :style="
                MobileSize
                  ? 'padding: 20px 20px 10px 20px;'
                  : 'padding: 40px 48px 10px 48px;'
              "
            >
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" class="d-flex" v-if="!MobileSize">
                    <v-row dense class="mr-auto">
                      <v-img
                        src="@/assets/Create_Store/partnerShopDetail.png"
                        max-height="62"
                        max-width="62"
                      ></v-img>
                      <span
                        class="pt-5 pl-4"
                        style="
                          font-weight: 600;
                          color: #333333;
                          font-size: 16px;
                        "
                        v-if="Action === 'A'"
                      >
                        รายชื่อผู้อนุมัติ ลำดับที่ 1
                      </span>
                      <span
                        class="pt-5 pl-4"
                        style="
                          font-weight: 600;
                          color: #333333;
                          font-size: 16px;
                        "
                        v-if="Action === 'B'"
                      >
                        รายชื่อผู้อนุมัติ ลำดับที่ 2
                      </span>
                      <span
                        class="pt-5 pl-4"
                        style="
                          font-weight: 600;
                          color: #333333;
                          font-size: 16px;
                        "
                        v-if="Action === 'C'"
                      >
                        รายชื่อผู้อนุมัติ ลำดับที่ 3
                      </span>
                    </v-row>
                  </v-col>
                </v-row>
                <v-col cols="12" md="12">
                  <v-card
                    elevation="0"
                    style="border: solid 1px #cccccc"
                    class="mt-2"
                  >
                    <v-card
                      class="pa-4"
                      elevation="0"
                      v-for="(item, index) of CheckApproverDetail"
                      :key="index"
                    >
                      <v-row
                        no-gutters
                        class="pa-4"
                        style="border: solid 1px #cccccc"
                        v-if="!MobileSize && !IpadSize"
                      >
                        <v-col
                          cols="4"
                          style="border: solid 1px #cccccc"
                          class="d-flex align-center"
                        >
                          <v-container style="">
                            <v-img
                              v-if="item.img_path === null"
                              class="rounded-lg"
                              src="@/assets/NoImage.png"
                              height="100%"
                            ></v-img>
                            <v-img
                              v-else
                              height="150"
                              class="rounded-lg"
                              :src="item.img_path"
                              contain
                            ></v-img>
                          </v-container>
                        </v-col>
                        <v-col cols="8" class="pa-2">
                          <p style="">
                            ชื่อ-สกุล:<b>{{ item.name }}</b>
                          </p>
                          <p style="">
                            อีเมล:<b>{{ item.email }}</b>
                          </p>
                          <p style="">
                            เบอร์โทรศัพท์:<b>{{ item.phone }}</b>
                          </p>
                        </v-col>
                      </v-row>
                      <v-row
                        no-gutters
                        class="pa-4"
                        style="border: solid 1px #cccccc"
                        v-if="MobileSize"
                      >
                        <v-col
                          cols="12"
                          style="border: solid 1px #cccccc"
                          class="d-flex align-center"
                        >
                          <v-container style="">
                            <v-img
                              v-if="item.img_path === null"
                              class="rounded-lg"
                              src="@/assets/NoImage.png"
                              height="100%"
                            ></v-img>
                            <v-img
                              v-else
                              height="150"
                              class="rounded-lg"
                              :src="item.img_path"
                              contain
                            ></v-img>
                          </v-container>
                        </v-col>
                        <v-col cols="12" class="pa-2">
                          <p style="">
                            ชื่อ-สกุล:<b>{{ item.name }}</b>
                          </p>
                          <p style="">
                            อีเมล:<b>{{ item.email }}</b>
                          </p>
                          <p style="">
                            เบอร์โทรศัพท์:<b>{{ item.phone }}</b>
                          </p>
                        </v-col>
                      </v-row>
                      <v-row
                        no-gutters
                        class="pa-4"
                        style="border: solid 1px #cccccc"
                        v-if="IpadSize"
                      >
                        <v-col
                          cols="12"
                          style="border: solid 1px #cccccc"
                          class="d-flex align-center"
                        >
                          <v-container style="">
                            <v-img
                              v-if="item.img_path === null"
                              class="rounded-lg"
                              src="@/assets/NoImage.png"
                              height="100%"
                            ></v-img>
                            <v-img
                              v-else
                              height="150"
                              class="rounded-lg"
                              :src="item.img_path"
                              contain
                            ></v-img>
                          </v-container>
                        </v-col>
                        <v-col cols="12" class="pa-2">
                          <p style="">
                            ชื่อ-สกุล:<b>{{ item.name }}</b>
                          </p>
                          <p style="">
                            อีเมล:<b>{{ item.email }}</b>
                          </p>
                          <p style="">
                            เบอร์โทรศัพท์:<b>{{ item.phone }}</b>
                          </p>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-card>
                </v-col>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-row
            dense
            style="height: 88px; background: #f5fcfb"
            :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'"
          >
            <v-btn
              rounded
              width="125"
              height="40"
              class="my-auto"
              outlined
              color="#27AB9C"
              @click="goBack()"
              >ย้อนกลับ</v-btn
            >
            <v-spacer></v-spacer>
            <v-btn
              rounded
              width="125"
              height="40"
              class="white--text my-auto"
              color="#27AB9C"
              @click="openAwaitModal()"
              >บันทึก</v-btn
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitTier" width="424" persistent>
      <v-card style="background: #ffffff; border-radius: 24px">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon @click="closeDialogAwait()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center">
            <p
              style="
                font-weight: 700;
                font-size: 24px;
                line-height: 24px;
                color: #333333;
              "
              class="my-4"
            >
              <b>{{
                Action === 'A'
                  ? 'รายชื่อผู้อนุมัติ ลำดับที่ 1'
                  : Action === 'B'
                  ? 'รายชื่อผู้อนุมัติ ลำดับที่ 2'
                  : Action === 'C'
                  ? 'รายชื่อผู้อนุมัติ ลำดับที่ 3'
                  : Action === 'create'
                  ? 'บันทึกรูปแบบผู้อนุมัติ'
                  : Action === 'delete'
                  ? 'ลบรูปแบบผู้อนุมัติ'
                  : 'แก้ไขรูปแบบผู้อนุมัติ'
              }}</b>
            </p>
            <span
              style="
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                color: #9a9a9a;
              "
              >คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span
            >
          </v-card-text>
          <v-card-text class="px-0">
            <v-row dense justify="center">
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                outlined
                rounded
                color="#27AB9C"
                class="mr-4"
                @click="closeDialogAwait()"
                >ยกเลิก</v-btn
              >
              <v-btn
                :width="MobileSize ? '125' : '156'"
                height="38"
                class="white--text"
                rounded
                color="#27AB9C"
                @click="SettingApprover(Action)"
                >ตกลง</v-btn
              >
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data: () => ({
    path: '',
    IdApprover: 0,
    CheckEditA: false,
    CheckEditB: false,
    CheckEditC: false,
    name: '',
    dialogAwaitTier: false,
    dataIndex: 0,
    Status: '',
    search: '',
    ShowSelectApprover: false,
    SelectApprover: false,
    sellerShopID: '',
    Action: '',
    CheckApproverDetail: [],
    DetailApprover: [],
    approverA: [],
    approverB: [],
    approverC: [],
    DataOfApprover: [
      {
        approver: [],
        budget: 0
      }
    ]
  }),
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    DetailApproverFilter () {
      return this.DetailApprover.filter(item => {
        const name = item.name ? item.name.toLowerCase() : ''
        const email = item.email ? item.email.toLowerCase() : ''
        const phone = item.phone ? item.phone.toLowerCase() : ''
        return name.includes(this.search.toLowerCase()) ||
              email.includes(this.search.toLowerCase()) ||
              phone.includes(this.search.toLowerCase())
      })
    },
    approvalTitle () {
      const isPartnerPage = this.$route.path.includes('Partner')
      return isPartnerPage ? 'สร้างรูปแบบการอนุมัติคู่ค้า' : 'สร้างรูปแบบการอนุมัติฝ่ายขาย'
    }
  },
  watch: {},
  created () {
    this.$EventBus.$emit('changeNav')
    // console.log('this.$route.query.Status', this.$route.query.Status)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.sellerShopID = sellerShopID
    this.Status = this.$route.query.Status
    if (this.$route.query.Status === 'edit') {
      this.IdApprover = this.$route.query.id
      this.GetDetailApprover()
    }
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
  },
  methods: {
    CheckSpacebarName (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    closeDialogAwait () {
      this.search = ''
      this.Action = ''
      this.dialogAwaitTier = false
    },
    Delete (index, item) {
      this.dialogAwaitTier = true
      this.dataIndex = index
      // console.log('index', index)
      // this.approverA.splice(index, 1)
      // this.CheckApproverDetail.splice
    },
    Edit (Action) {
      if (Action === 'EditA') {
        this.CheckEditA = true
      } else if (Action === 'EditB') {
        this.CheckEditB = true
      } else if (Action === 'EditC') {
        this.CheckEditC = true
      }
      if (Action === 'CheckEditA') {
        this.CheckEditA = false
      } else if (Action === 'CheckEditB') {
        this.CheckEditB = false
      } else if (Action === 'CheckEditC') {
        this.CheckEditC = false
      }
      this.Action = Action
      // this.dialogAwaitTier = true
    },
    cancel (type) {
      this.CheckApproverDetail = []
      if (type === 'ShowSelectApprover') {
        this.ShowSelectApprover = false
        this.search = ''
      } else if (type === 'SelectApprover') {
        this.SelectApprover = false
      }
    },
    goBack () {
      this.CheckApproverDetail = []
      this.SelectApprover = false
      this.ShowSelectApprover = true
      this.CheckApprover()
    },
    backToMenu () {
      var path = this.$router.currentRoute.path.includes('Partner') ? 'ManageSalesPartnerApproval' : 'ManageSalesApproval'
      var pathMobile = this.$router.currentRoute.path.includes('Partner') ? '' : 'ManageSalesApprovalMobile'
      if (this.MobileSize) {
        this.$router
          .push({ path: `/${pathMobile}` })
          .catch(() => {})
      } else {
        this.$router.push({ path: `/${path}` }).catch(() => {})
      }
    },
    openAwaitModal (Action) {
      if (Action === 'create') {
        this.Action = 'create'
      } else if (Action === 'edit') {
        this.Action = 'edit'
      }
      // console.log('Action', Action)
      this.dialogAwaitTier = true
    },
    async SettingApprover (Action) {
      // console.log('Action', Action)
      if (Action === 'A') {
        this.approverA.push(this.CheckApproverDetail[0])
        this.dialogAwaitTier = false
        this.SelectApprover = false
      } else if (Action === 'B') {
        this.approverB.push(this.CheckApproverDetail[0])
        this.dialogAwaitTier = false
        this.SelectApprover = false
      } else if (Action === 'C') {
        this.approverC.push(this.CheckApproverDetail[0])
        this.dialogAwaitTier = false
        this.SelectApprover = false
      } else if (Action === 'EditA') {
        this.approverA.splice(this.dataIndex, 1)
        this.CheckEditA = false
        this.dialogAwaitTier = false
      } else if (Action === 'EditB') {
        this.approverB.splice(this.dataIndex, 1)
        this.CheckEditB = false
        this.dialogAwaitTier = false
      } else if (Action === 'EditC') {
        this.approverC.splice(this.dataIndex, 1)
        this.CheckEditC = false
        this.dialogAwaitTier = false
      } else if (Action === 'create') {
        await this.CheckApprover('create')
        await this.CreateListApprover()
        this.dialogAwaitTier = false
      } else if (Action === 'edit') {
        await this.CheckApprover('edit')
        await this.EditListApprover()
        this.dialogAwaitTier = false
      }
      this.Action = ''
      this.CheckApproverDetail = []
    },
    addToListApprover (val) {
      this.CheckApproverDetail.push(val)
      this.search = ''
      this.ShowSelectApprover = false
      this.SelectApprover = true
      // this.approverA.push(val)
    },
    async CreateListApprover () {
      this.approverA = this.approverA.map(item => {
        return item.user_id
      })
      this.approverB = this.approverB.map(item => {
        return item.user_id
      })
      this.approverC = this.approverC.map(item => {
        return item.user_id
      })
      var data = {
        seller_shop_id: this.sellerShopID,
        approve_position_name: this.name,
        approve_position_budget: 0,
        list_approver: [
          {
            approver: this.approverA,
            budget: 0
          },
          {
            approver: this.approverB,
            budget: 0
          },
          {
            approver: this.approverC,
            budget: 0
          }
        ],
        type: this.$router.currentRoute.path.includes('Partner') ? 'partner_order' : 'sale_order'
      }
      // console.log('dataCreate---->', data)
      await this.$store.dispatch('actionsCreateListApproverSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder
        .stateCreateListApproverSalesOrder
      if (res.message === 'Create approve Position Success.') {
        var path = this.$router.currentRoute.path.includes('Partner') ? '/ManageSalesPartnerApproval' : '/ManageSalesApproval'
        if (this.MobileSize) {
          this.$router
            .push({ path: '/ManageSalesApprovalMobile' })
            .catch(() => {})
        } else {
          this.$router.push({ path: path }).catch(() => {})
        }
        // this.$router.push({ path: '/ManageSalesApproval' }).catch(() => {})
      }
      // console.log('res', res)
    },
    async EditListApprover () {
      this.approverA = this.approverA.map(item => {
        return item.user_id
      })
      this.approverB = this.approverB.map(item => {
        return item.user_id
      })
      this.approverC = this.approverC.map(item => {
        return item.user_id
      })
      var data = {
        seller_shop_id: this.sellerShopID,
        approve_position_id: this.IdApprover,
        approve_position_name: this.name,
        approve_position_budget: 0,
        list_approver: [
          {
            approver: this.approverA,
            budget: 0
          },
          {
            approver: this.approverB,
            budget: 0
          },
          {
            approver: this.approverC,
            budget: 0
          }
        ]
      }
      await this.$store.dispatch('actionsGetEditApproverSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder
        .stateGetEditApproverSalesOrder
      if (res.message === 'Edit Approve Position Success.') {
        var path = this.$router.currentRoute.path.includes('Partner') ? '/ManageSalesPartnerApproval' : '/ManageSalesApproval'
        this.$router.push({ path: path }).catch(() => {})
      }
      // console.log('res', res)
    },
    async CheckApprover (type) {
      // console.log('type', type)
      this.$store.commit('openLoader')
      if (type === 'ApproverA') {
        this.Action = 'A'
      } else if (type === 'ApproverB') {
        this.Action = 'B'
      } else if (type === 'ApproverC') {
        this.Action = 'C'
      }
      this.DataOfApprover = []
      for (const item of this.approverA) {
        this.DataOfApprover.push({
          approver: [item.user_id],
          budget: 0
        })
      }
      for (const item of this.approverB) {
        this.DataOfApprover.push({
          approver: [item.user_id],
          budget: 0
        })
      }
      for (const item of this.approverC) {
        this.DataOfApprover.push({
          approver: [item.user_id],
          budget: 0
        })
      }
      // console.log('approverB', this.approverB)
      // console.log('DataOfApprover------>', this.DataOfApprover)
      if (type !== 'delete') {
        this.ShowSelectApprover = true
      }
      if (type === 'create' || type === 'edit') {
        this.ShowSelectApprover = false
      }
      var data = {
        seller_shop_id: this.sellerShopID,
        list_approver: this.DataOfApprover
      }
      await this.$store.dispatch('actionsCheckListApproverSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder
        .stateCheckListApproverSalesOrder
      this.$store.commit('closeLoader')
      this.DetailApprover = res.data.list_aprrover
    },
    async GetDetailApprover () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.sellerShopID,
        approve_position_id: this.IdApprover
      }
      await this.$store.dispatch('actionsGetDetailApproverSalesOrder', data)
      var res = await this.$store.state.ModuleSaleOrder
        .stateGetDetailApproverSalesOrder
      if (res.message === 'Get Detail Approver Success.') {
        // console.log(res.data.approver_list)
        this.name = res.data.approve_position_data.approve_position_name
        this.approverA = res.data.approver_list.approver_list_1.approver
        this.approverB = res.data.approver_list.approver_list_2.approver
        this.approverC = res.data.approver_list.approver_list_3.approver
      } else {
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>
