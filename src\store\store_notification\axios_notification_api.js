import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async ListNotification (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}notification/listnotification`, data, auth)
      // console.log('response list notification', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateNotification (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}notification/update_to_entity`, data, auth)
      // console.log('response update notification', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ReadAllNotification () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}notification/approveall`, auth)
      // console.log('response update notification', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
