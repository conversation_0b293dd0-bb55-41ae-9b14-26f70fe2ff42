<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col cols="6">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการ Flash Sale</v-card-title>
          <v-card-title style="font-weight: 700; font-size: 16px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backTomenu()">mdi-chevron-left</v-icon>จัดการ Flash Sale</v-card-title>
        </v-col>
        <v-col cols="6" v-if="dataList.length === 0" class="d-flex justify-end">
          <v-btn color="#27ab9c" style="border-radius: 1.5vw;" @click="addFlashSale()">
            <v-icon color="#fff" class="mr-1">mdi-plus-circle</v-icon>
            <span style="color: #fff;">เพิ่ม Flash Sale</span>
          </v-btn>
        </v-col>
        <v-col cols="6" v-else class="d-flex justify-end" style="gap:.5vw">
          <v-btn style="border-radius: 1.5vw;" @click="editFlashSale()" class="ml-1" outlined color="#26ab9c">แก้ไข</v-btn>
          <v-btn style="border-radius: 1.5vw;" @click='dialogDeleteFlashSale(dataList[0].id)' outlined color="#f86d62">ลบ</v-btn>
        </v-col>
      </v-row>
      <div v-if="dataList.length !== 0">
        <v-row v-for="(items, index) in paginated" :key="index" class="pa-2 ma-2" style="background-color: #fafafa; border-radius: 1vw;">
          <v-col cols="12">
            <v-icon>mdi-menu-right</v-icon>
            <span>ภาพแบนเนอร์</span>
          </v-col>
          <v-col cols="12" align="center" style="margin-top: -1vw">
            <v-card width="100%" :height="MobileSize ? '100px' : '300px'" elevation="0">
              <v-img :src="items.image_path" v-if="items.image_path !== null" width="100%" height="100%" contain></v-img>
              <v-img src="@/assets/NoImage.png" v-else width="100%" height="266px" contain></v-img><br />
            </v-card>
          </v-col>
          <v-col cols="6" class="d-flex align-center">
            <v-icon>mdi-menu-right</v-icon>
            <span>รายการสินค้า</span>
          </v-col>
          <!-- list Product -->
          <v-col cols="12" style="margin-top: -1vw" class="mb-2 px-0" v-if="items.product_list.length !== 0" align="start">
            <v-container style="margin-top: -1vw">
              <!-- Products Mobile -->
              <v-row class="ma-1" v-if="MobileSize">
                <v-col class="d-flex flex-wrap justify-center" style="gap: 1vw">
                  <div class="formatBox" style="width: 22vw; height: 55vw;" v-for="(product, index) in paginatedProducts" :key="index">
                    <div style="height: 15vw; margin: auto;">
                      <img v-if="product.images_URL.length !== 0 && product.have_attribute === 'no'" style="width: 15vw" :src="product.images_URL[0]" alt="">
                      <img v-else-if="product.product_color_image_path !== null && product.have_attribute === 'yes'" style="width: 15vw" :src="product.product_color_image_path" alt="">
                      <img style="width: 15vw" v-else src="@/assets/NoImage.png" alt="">
                    </div>
                    <span v-if="product.name !== '' && product.have_attribute === 'no'" style="font-size: 12px" class="text-center mt-1">{{ substring(product.name) }}</span>
                    <span v-if="product.attribute_priority_1 !== '' && product.have_attribute === 'yes'" style="font-size: 12px" class="text-center mt-1">{{ substring(product.name) }}{{ '(' + substring(product.attribute_priority_1) + ')' }}</span>
                    <span class="ma-auto" style="font-weight: 600; font-size: 14px; line-height: 29px; color: #F5222D;">
                      ฿ {{ Number(product.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                    </span>
                    <span style="font-weight: 700; font-size: 12px; line-height: 16px; color: #FAAD14; letter-spacing: -0.2px; margin: auto;" v-if="product.sold !== '-'">ขายแล้ว {{ kFormatter(product.sold) }} ชิ้น</span>
                  </div>
                </v-col>
              </v-row>
               <!-- Products Ipad & DaeskTop -->
              <v-row class="ma-1" v-else>
                <v-col class="d-flex flex-wrap" :style="product_list.length < 4 ? 'gap: 1vw;' : 'gap: 1vw; justify-content: center;'">
                  <div class="formatBox" :style="IpadSize ? 'width: 22.5vw; height: 28vw;' : IpadProSize ? 'width: 18vw; height: 25vw;' : 'width: 14vw; height: 18vw;'" v-for="(product, index) in paginatedProducts" :key="index">
                    <v-col>
                      <img v-if="product.images_URL.length !== 0 && product.have_attribute === 'no'" :style=" IpadProSize || IpadSize ? 'width: 10vw; margin: auto; display: flex;' : 'width: 5vw; margin: auto; display: flex;'" :src="product.images_URL[0]" alt="">
                      <img v-else-if="product.product_color_image_path !== null && product.have_attribute === 'yes'" :style=" IpadProSize || IpadSize ? 'width: 10vw; margin: auto; display: flex;' : 'width: 5vw; margin: auto; display: flex;'" :src="product.product_color_image_path" alt="">
                      <img :style=" IpadProSize || IpadSize ? 'width: 7vw; margin: auto; display: flex;' : 'width: 5vw; margin: auto; display: flex;'" v-else src="@/assets/NoImage.png" alt="">
                    </v-col>
                    <span v-if="product.name !== '' && product.have_attribute === 'no'" class="text-center mt-3">{{ substring(product.name) }}</span>
                    <span v-if="product.attribute_priority_1 !== '' && product.have_attribute === 'yes'" class="text-center mt-3">{{ substring(product.name) }}{{ '(' + substring(product.attribute_priority_1) + ')' }}</span>
                    <div class="d-flex justify-center">
                      <span style="font-size: 12px; font-weight: 600; line-height: 19px; color: #636363; text-decoration-line: line-through;" class="pt-1">฿ {{ Number(product.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      <v-chip v-if="product.discount_percent !== '0%' && product.discount_percent !== '0'" color="#FBE5E4" text-color="#FF0000" style="border-radius: 4px;" :class="MobileSize ? 'px-2 ml-1' : 'ml-2'" small><span class="discountText">ส่วนลด {{ product.discount_percent }}</span></v-chip>
                    </div>
                    <span class="ma-auto" style="font-weight: 600; font-size: medium; line-height: 29px; color: #F5222D;">
                      ฿ {{ Number(product.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                    </span>
                    <span style="font-weight: 700; font-size: 14px; line-height: 16px; color: #FAAD14; letter-spacing: -0.2px; margin: auto;" v-if="product.sold !== '-'">ขายแล้ว {{ kFormatter(product.sold) }} ชิ้น</span>
                  </div>
                </v-col>
              </v-row>
              <v-row class="pa-3" v-if="totalPages > 1" style="margin-top: -1vw">
                <v-col class="text-center">
                  <v-pagination v-model="currentPage" :length="totalPages" circle></v-pagination>
                </v-col>
              </v-row>
            </v-container>
          </v-col>
          <v-card flat tile height="100%" width="100%" v-else class="ma-3">
            <v-col cols="12" align-self="center" align="center">
              <p style="font-size: 1.25rem; font-weight: 700;color: #C4C4C4;">ไม่มีรายการสินค้าในหัวข้อนี้</p>
            </v-col>
          </v-card>
        </v-row>
        <!-- <v-pagination
          v-model="page"
          :length="pageMax"
          prev-icon="mdi-menu-left"
          next-icon="mdi-menu-right"
        ></v-pagination> -->
      </div>
      <div v-else class="ma-3">
        <v-card class="mt-5">
          <v-card>
            <v-card-text class="d-flex align-center flex-column">
              <v-img
                src="@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png"
                width="400"
                contain
              ></v-img>
              <span style="font-size: 18px">ไม่มีรายการแฟลชเซลล์ในร้านค้านี้</span>
            </v-card-text>
          </v-card>
        </v-card>
      </div>
      <v-dialog content-class="elevation-0" v-model='dialogDelete' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openDialog = false" scrollable>
        <v-card min-height='100%' style="border-radius: 1.5vw; overflow: hidden;">
          <v-card-title class="d-flex justify-end" :class="MobileSize ? 'pa-0' : ''">
            <v-btn plain fab small @click='dialogDelete = false' icon><v-icon color='#BABABA'>mdi-close</v-icon></v-btn>
          </v-card-title>
          <v-row style="margin-top: -2.5vw">
            <v-col cols="12">
              <v-img height="100%">
                <img class="d-flex ma-auto" style="border-radius: 1.5vw; width: 15vw; position: relative;" src="@/assets/ImageINET-Marketplace/ICONShop/warning.jpg" alt="Product Image">
              </v-img>
            </v-col>
          </v-row>
          <v-container style="margin-top: -1.5vw">
            <v-card-text style="text-align: center; font-size: large;">
              <span style="font-size: 16px; text-align: center">
                ต้องการลบแฟลชเซลล์ นี้ ใช่ หรือ ไม่ ?
              </span>
            </v-card-text>
            <v-card-actions>
              <v-row dense class='d-flex justify-center' style=" gap: 1vw">
                <v-btn class="white--text ml-2" rounded color="#27AB9C"  @click="deleteFlashSale()">ตกลง</v-btn>
                <v-btn outlined rounded color="#27AB9C" @click='dialogDelete = false'>ยกเลิก</v-btn>
              </v-row>
            </v-card-actions>
          </v-container>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
import { msgErr, statusErr } from '@/enum/GetError'
import { Decode } from '@/services'
export default {
  components: {
    // CardProductsFlashSale: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSale')
    // CountdownDate: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CountdownDate'),
    // CountdownDateEnd: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CountdownDateEnd')
  },
  data () {
    return {
      orderList: [],
      dataList: [],
      StateStatus: 0,
      activeTab: 'tab1',
      showCountOrder: 0,
      disableTable: false,
      startFlashSale: false,
      endFlashSale: false,
      // pageMax: null,
      current: 1,
      pageSize: 3,
      DataTable: [],
      overlay: false,
      countActive: 0,
      countInactive: 0,
      dialogSuccess: false,
      dialogConfirm: false,
      deletFlashSaleItem: '',
      countItemAll: null,
      flashsaleID: null,
      dialogShowProduct: false,
      product_list: {},
      currentPage: 1,
      // itemsPerPage: 4,
      allProduct: [],
      dialogDelete: false
    }
  },
  computed: {
    checkWidth () {
      return window.screen.width
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    itemsPerPage () {
      if (this.IpadProSize || this.MobileSize) {
        return 3
      } else {
        return 4
      }
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return (this.current - 1) * pageSize
    },
    indexEnd () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return this.indexStart + pageSize
    },
    paginated () {
      return this.dataList.slice(this.indexStart, this.indexEnd)
    },
    paginatedProducts () {
      if (this.product_list && this.product_list.length) {
        const start = (this.currentPage - 1) * this.itemsPerPage
        const end = start + this.itemsPerPage
        return this.product_list.slice(start, end)
      }
      return []
    },
    totalPages () {
      return Math.ceil(this.product_list.length / this.itemsPerPage)
    }
  },
  watch: {
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    page (newPage) {
      this.getData()
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageFlashSaleMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageFlashSale' }).catch(() => {})
      }
    }
  },
  mounted () {
    this.$EventBus.$on('getNewDataFlashSaleList', this.getData)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getNewDataFlashSaleList')
    })
  },
  destroy () {
    clearInterval(this.timer)
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      // this.getDataCoupons()
      await this.getData()
      // this.getPoint()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    addFlashSale () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSaleCreate' }).catch(() => { })
      } else {
        this.$router.push({ path: '/manageFlashSaleCreateMobile' }).catch(() => { })
      }
    },
    editFlashSale (items) {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSaleEdit' }).catch(() => { })
      } else {
        this.$router.push({ path: '/manageFlashSaleEditMobile' }).catch(() => { })
      }
    },
    scrollToTopPage () {
      window.scrollTo(0, 0)
    },
    async getData () {
      this.$store.commit('openLoader')
      var shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopSellerID,
        page: 1,
        limit: 1
      }
      await this.$store.dispatch('actionsGetFlashSale', data)
      var responseData = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
      if (responseData.code === 200) {
        this.$store.commit('closeLoader')
        this.dataList = responseData.data.data
        this.countItemAll = responseData.data.count_flash_sale
        if (this.dataList.length > 0 && this.dataList[0].product_list) {
          this.product_list = this.dataList[0].product_list
        } else {
          this.product_list = []
        }
      } else {
        this.$store.commit('closeLoader')
        if (responseData.message === 'Not access this function') {
          await this.$swal.fire({
            icon: 'warning',
            text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้',
            showConfirmButton: false,
            timer: 2000
          })
          this.$router.push({ path: '/userInfo' }).catch(() => {})
        } else if (responseData.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(responseData.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(responseData.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    dialogDeleteFlashSale (itemID) {
      this.dialogDelete = true
      this.flashsaleID = itemID
      // console.log(itemID)
    },
    async deleteFlashSale () {
      // this.$store.commit('openLoader')
      var data = { flashsale_id: this.flashsaleID }
      // console.log('see', data)
      await this.$store.dispatch('actionsDeleteFlashSale', data)
      var response = await this.$store.state.ModuleManageFlashSale.stateDeleteFlashSale
      if (response.code === 200) {
        // this.$store.commit('closeLoader')
        this.dialogDelete = false
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          text: 'ลบข้อมูลแฟลชเซลล์สำเร็จ'
        }).then(() => {
          this.getData()
        })
      } else if (response.message === 'Flash sale not found or deleted') {
        this.dialogDelete = false
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ไม่พบ Flashsale ID'
        })
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    backTomenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    substring (data) {
      return data.length > 20 ? data.substring(0, 20) + '...' : data
    },
    kFormatter (num) {
      return Math.abs(num) > 999 ? Math.sign(num) * ((Math.abs(num) / 1000).toFixed(1)) + 'k' : Math.sign(num) * Math.abs(num)
    }
  }
}
</script>
<style scoped>
.vSelectLineHeight /deep/ .v-select__selection--comma {
  line-height: 25px !important;
}
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.formatBox {
  display: flex;
  flex-direction: column;
  box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
  padding: 1.5vw;
  border-radius: .5vw;
  background-color: #fff;
}
</style>

<style>
.v-tabs:not(.v-tabs--vertical):not(.v-tabs--right) > .v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes) .v-slide-group__prev {
    display: none;
    visibility: hidden;
}
</style>
