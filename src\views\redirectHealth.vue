<template>
  <div></div>
</template>

<script>
import { Encode } from '@/services'
import qs from 'qs'
export default {
  data () {
    return {
    }
  },
  async created () {
    this.$store.commit('openLoader')
    // step 2 : เอา code จาก moph id
    var data = {
      code: await this.$router.currentRoute.query.code
    }
    var response = ''
    const dataMoph = {
      grant_type: 'authorization_code',
      client_id: process.env.VUE_APP_MOPH_CLIENT_ID,
      client_secret: process.env.VUE_APP_MOPH_CLIENT_SECRET,
      code: data.code,
      redirect_uri: process.env.VUE_APP_MOPH_REDIRECT_URI
    }
    response = await this.axios.post(`${process.env.VUE_APP_MOPH_ID}`, qs.stringify(dataMoph), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    console.log('response ===>', response)
    // step 3 : เอา code ไปยิง provider id
    if (response.data.message === 'You logged in successfully') {
      var accessToken = ''
      accessToken = response.data.data.access_token
      var dataProvider = {
        client_id: process.env.VUE_APP_PROVIDER_CLIENT_ID,
        secret_key: process.env.VUE_APP_PROVIDER_SECRET_KEY,
        token_by: 'Health ID',
        token: accessToken
      }
      var responseProvider = await this.axios.post(`${process.env.VUE_APP_PROVIDER_ID}`, dataProvider, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      console.log('responseProvider ===>', responseProvider)
      // step 4 : เอา access token ไปยิงเอาข้อมูล
      if (responseProvider.data.message === 'OK') {
        var accessTokenProvider = ''
        accessTokenProvider = responseProvider.data.data.access_token
        const auth = {
          headers: {
            Authorization: `${accessTokenProvider}`
            // 'client-id': process.env.VUE_APP_PROVIDER_CLIENT_ID,
            // 'secret-key': process.env.VUE_APP_PROVIDER_SECRET_KEY
          }
        }
        var responseProfile = await this.axios.get(`${process.env.VUE_APP_BACK_END3}get_profile`, auth)
        console.log('responseProfile ===>', responseProfile)
        if (responseProfile.data.message === 'OK' || responseProfile.data.message === 'This account is already in the system') {
          this.$store.commit('closeLoader')
          console.log(responseProfile.data.data)
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 2500,
          //   timerProgressBar: true,
          //   icon: 'success',
          //   text: 'เข้าสู่ระบบสำเร็จ'
          // })
          const auth = {
            headers: { Authorization: `Bearer ${accessTokenProvider}` }
          }
          const responseUserDetail = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail_mp_v2`, '', auth)
          if (responseUserDetail.data.code !== 500) {
            var onedata = {}
            onedata.user = responseUserDetail.data.data
            localStorage.setItem('oneData', Encode.encode(onedata))
            // set การ redirect หน้า
            // var PathRedirect = ''
            // if (this.$router.currentRoute.query.redirect_url !== undefined) {
            //   PathRedirect = this.$router.currentRoute.query.redirect_url + '?page=1&ref_code=' + this.$router.currentRoute.query.ref_code + '&callback_url=' + this.$router.currentRoute.query.callback_url
            // } else {
            //   PathRedirect = '/'
            // }
            var dataRole = {
              role: 'ext_buyer'
            }
            localStorage.setItem('roleUser', JSON.stringify(dataRole))
            this.$EventBus.$emit('LoginUser')
            this.$EventBus.$emit('checkPDPA')
            this.$EventBus.$emit('getCartPopOver')
            this.$EventBus.$emit('getItemNoti')
            this.$EventBus.$emit('checkChatMe')
            this.$router.push({ path: '/' }).catch(() => {})
          } else {
            localStorage.removeItem('oneData')
            this.$router.push({ path: '/' }).catch(() => {})
          }
        } else {
          this.$store.commit('closeLoader')
          if (responseProfile.data.message === 'Token expired') {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ข้อมูลในการตรวจสอบตัวตนไม่ถูกต้อง ไม่มีสิทธิ์ในการเข้าถึงทรัพยากรที่ร้องขอ'
            })
            setTimeout(() => {
              window.location.assign(`${process.env.VUE_APP_REDIRECT_HEALTHID}`)
            }, 2500)
          } else {
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: responseProfile.data.message
            })
            this.$router.push({ path: '/' }).catch(() => {})
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: responseProvider.data.message
        })
        this.$router.push({ path: '/' }).catch(() => {})
      }
    } else {
      this.$store.commit('closeLoader')
      this.$swal.fire({
        showConfirmButton: false,
        timer: 2500,
        timerProgressBar: true,
        icon: 'error',
        html: response.data.message
      })
      this.$router.push({ path: '/' }).catch(() => {})
    }
  }
}
</script>

<style>

</style>
