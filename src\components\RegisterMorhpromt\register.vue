<template>
  <div>
    <!-- Website -->
    <div style="background: #F6F6F6;" v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="6" md="12" align="center" class="my-16">
            <v-form ref="formRegis" :lazy-validation="lazy">
              <v-card width="604px" height="477px" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
                <v-card-text>
                  <v-container>
                    <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                      <v-img :src="require('@/assets/new_logo_panit.png')" max-height="117" max-width="123" contain/>
                    </v-row>
                    <v-row dense justify="center" align-content="center" class="mt-6 mb-8">
                      <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #000000;">เข้าสู่ระบบ</span>
                    </v-row>
                    <v-row no-gutters dense class="mx-12">
                      <v-col cols="12" md="12" sm="12">
                        <v-text-field v-model="telephone" outlined placeholder="กรอกหมายเลขโทรศัพท์" dense maxLength="10" :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="mt-8">
                        <v-row dense justify="center" align-content="center">
                          <v-checkbox
                            v-model="checkConsent"
                            hide-details
                            class="mt-0 pt-0"
                          ></v-checkbox>
                          <span class="pl-4" style="font-size: 14px; line-height: 22px; color: #333333; font-weight: 400; padding-top: 1px;">ยอมรับ ข้อกำหนดการใช้บริการ และ นโยบายคุ้มครองส่วนบุคคล</span>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" sm="12" class="mt-10">
                        <v-btn color="#27AB9C" block :disabled="checkConsent === false ? true : false" style="color: white;" @click="confirmOTP()">ยืนยัน</v-btn>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div style="background: #F6F6F6;" v-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <v-card width="60%" height="477px" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
            <v-card-text>
              <v-container>
                <v-row dense justify="center" align-content="center" class="mt-0 mb-4">
                  <v-img :src="require('@/assets/new_logo_panit.png')" max-height="117" max-width="123" contain/>
                </v-row>
                <v-row dense justify="center" align-content="center" class="mt-6 mb-8">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #000000;">เข้าสู่ระบบ</span>
                </v-row>
                <v-row no-gutters dense class="mx-12">
                  <v-col cols="12" md="12" sm="12">
                    <v-text-field v-model="telephone" outlined placeholder="กรอกหมายเลขโทรศัพท์" dense maxLength="10" :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="mt-4">
                    <v-row dense justify="center" align-content="center">
                      <v-checkbox
                        v-model="checkConsent"
                        hide-details
                        class="mt-0 pt-0"
                        label="ยอมรับ ข้อกำหนดการใช้บริการ และ นโยบายคุ้มครองส่วนบุคคล"
                      ></v-checkbox>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="mt-10">
                    <v-btn color="#27AB9C" block :disabled="checkConsent === false ? true : false" style="color: white;">ยืนยัน</v-btn>
                  </v-col>
                </v-row>
              </v-container>
            </v-card-text>
          </v-card>
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div  style="background: #F6F6F6;" v-if="MobileSize">
      <v-container class="my-6">
        <v-row dense justify="center" align-content="center">
          <v-col cols="12" md="12">
            <v-card width="343px" height="477px" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
              <v-card-text>
                <v-container>
                  <v-row dense justify="center" align-content="center" class="mt-0 mb-4">
                    <v-img :src="require('@/assets/new_logo_panit.png')" max-height="117" max-width="123" contain/>
                  </v-row>
                  <v-row dense justify="center" align-content="center" class="mt-6 mb-8">
                    <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #000000;">เข้าสู่ระบบ</span>
                  </v-row>
                  <v-row no-gutters dense class="mx-12">
                    <v-col cols="12" md="12" sm="12">
                      <v-text-field v-model="telephone" outlined placeholder="กรอกหมายเลขโทรศัพท์" dense maxLength="10" :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" class="mt-4">
                      <v-row dense justify="center" align-content="center">
                        <v-checkbox
                          v-model="checkConsent"
                          hide-details
                          class="mt-0 pt-0"
                          label="ยอมรับ ข้อกำหนดการใช้บริการ และ นโยบายคุ้มครองส่วนบุคคล"
                        ></v-checkbox>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" class="mt-10">
                      <v-btn color="#27AB9C" block :disabled="checkConsent === false ? true : false" style="color: white;">ยืนยัน</v-btn>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      telephone: '',
      checkConsent: false,
      lazy: false,
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ]
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
  },
  methods: {
    async confirmOTP () {
      if (this.$refs.formRegis.validate(true)) {
        this.$store.commit('openLoader')
        var data = {
          mobile_no: this.telephone
        }
        await this.$store.dispatch('actionsGetOTP', data)
        var res = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
        // console.log(res)
        if (res.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          var dataOTP = {
            otp: res.data.otp,
            ref_code: res.data.ref_code,
            mobileNo: this.telephone
          }
          localStorage.setItem('OTPData', Encode.encode(dataOTP))
          // console.log(dataOTP)
          this.$router.replace({ path: '/otp' }).catch(() => {})
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', title: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
        }
      }
    }
  }
}
</script>

<style scoped>
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
