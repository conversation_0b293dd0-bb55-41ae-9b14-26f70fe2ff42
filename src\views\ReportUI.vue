<template>
  <v-container>
    <div style="background-color: #fff;">
      <FilterUI/>
      <DataTableUI/>
    </div>
  </v-container>
</template>

<script>
// import FrequencyUI from '@/components/Dashboard/frequencyTable'
// import dataTest from '@/components/library/dataTest.json'
export default {
  data () {
    return {
      freq: false,
      dataFreq: [],
      dataSum: [],
      headers2: [],
      SETT: [],
      toDay: new Date().toISOString().slice(0, 10),
      Day: `${new Date().toISOString().slice(0, 7)}-01`,
      numChange: '1'
    }
  },
  components: {
    FilterUI: () => import('@/components/Report/Filter'),
    DataTableUI: () => import('@/components/Report/DataTable')
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    // this.init()
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$on('FuntionChange', this.FuntionChange)
    this.$EventBus.$on('FuntionChange2', this.FuntionChange2)
  },
  destroyed () {
    this.$EventBus.$off('FuntionChange')
    this.$EventBus.$off('FuntionChange2')
  },
  watch: {
    MobileSize (val) {
      // if (val === true) {
      //   this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      // } else {
      //   this.$router.push({ path: '/dashboard' }).catch(() => {})
      // }
      if (val === true) {
        this.$router.push({ path: '/ReportMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Report' }).catch(() => {})
      }
    },
    dataSum (e) {
      // console.log('EdataSum', e)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    // Frequency () {
    //   if (this.$store.state.ModuleShop.stateSetCompo1) {
    //     return ''
    //   } else {
    //     return FrequencyUI
    //   }
    // }
    Sums () {
      if (Object.values(this.dataSum).length !== 0) {
        return this.dataSum
      } else {
        return ''
      }
    }
    // dataSum (e) {
    //   console.log('EdataSum', e)
    // }
  },
  methods: {
    async FuntionChange (num) {
      // console.log('FuntionChange')
      // console.log('Funtion', num.number)
      this.numChange = await num.number
    },
    async FuntionChange2 (num) {
      // console.log('FuntionChange')
      // console.log('Funtion', num.number)
      this.numChange = await num.number
      // this.$EventBus.$emit('ResetTable')
    }
  }
}
</script>

<style lang="css" scoped>
.inner-right {
    height: 300px;
    max-height: 300px;
    overflow-y: scroll;
}
#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
