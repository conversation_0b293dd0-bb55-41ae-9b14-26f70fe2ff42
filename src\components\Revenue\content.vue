<template>
  <div>
<v-row v-resize="onResize" >
  <div class="h-card pt-10 ml-4" v-if="!isMobile">
    <div class="h-img-b ml-8 pt-5">
      <v-img
        class="mx-auto py-auto"
        lazy-src="@/assets/icons/shopping-basket 1.png"
        max-height="48"
        max-width="48"
        src="@/assets/icons/shopping-basket 1.png"
      ></v-img>
      </div>
      <div class="mt-10 pt-0 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
      ถอนเงิน
    </div>
    <div class="ml-5">
        รายการถอนเงิน {{ListData}} รายการ
    </div>
    <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 0.9vw; line-height: 40px;'">
      {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
      <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
    </div>
  </div>
  <v-col v-else cols="12" align="center">
  <div class="h-card">
    <div class="h-img-b mt-10 pt-6">
    <v-img
        class="mx-auto py-auto"
        lazy-src="@/assets/icons/shopping-basket 1.png"
        max-height="48"
        max-width="48"
        src="@/assets/icons/shopping-basket 1.png"
      ></v-img>
    </div>
    <div class="mt-10" style="font-weight: 700; font-size: 16px; line-height: 24px;">
      ถอนเงิน
    </div>
    <div class="">
        รายการถอนเงิน {{ListData}} รายการ
    </div>
    <div class="mt-1 fontsize-28" style="font-weight: 700; font-size: 0.9vw; line-height: 40px;">
      {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
      <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
    </div>
  </div>
  </v-col>
</v-row>
  <div v-if="loading">
    <v-list-item>
      <v-list-item-title ><v-row justify="center" ><v-col cols="3"></v-col> <v-col>
        <ldsFacebook />
        </v-col><v-col cols="3"></v-col></v-row></v-list-item-title>
      </v-list-item>
    </div>
  <div v-else>
   <!--  <v-row
    class="justify-start ml-2 mb-0"
    >
    <v-col>
    <v-tabs>
    <v-tab>ทั้งหมด</v-tab>
    <v-tab>รออนุมัติ</v-tab>
    <v-tab>อนุมัติ</v-tab>
    <v-tab>ไม่อนุมัติ</v-tab>
  </v-tabs>
      </v-col>
    </v-row> -->
      <v-row class="pb-0 mb-0 mt-10">
        <v-col cols="12" md="6" class="mb-0 pb-0 pl-3 ml-3">
          <v-text-field
            v-model="search"
            dense
            outlined
            :class="isMobile ? 'mx-5' : 'mb-0 pb-0'"
            style="border-radius: 6px;"
            placeholder="รหัสการสั่งซื้อ, เลขที่ทำรายการชำระเงิน"
          ><v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon></v-text-field>
        </v-col>
        <v-col cols="12"  md="3" class="pb-0 mb-0" align="right">
          <div class="mx-10 px-10 pb-0 mb-0"></div>
          <section v-if="Object.values(itemsData).length !== 0" >
    <xlsx-workbook>
      <xlsx-sheet
        :collection="sheet.data"
        v-for="sheet in sheets"
        :key="sheet.name"
        :sheet-name="sheet.name"
        class="pb-0 mb-0"
      />
      <xlsx-download>
        <v-btn
        dark
        color="#27AB9C"
        class="mx-0 pl-0 pb-0 mb-0"
        outlined
        ><v-icon
        style="font-size: 25px;"
      large
      color="darken-2"
    >
      mdi-upload-outline
    </v-icon><span style="font-size: 13px;">Export</span></v-btn>
      </xlsx-download>
    </xlsx-workbook>
  </section>
        </v-col>
        <v-col cols="12" md="1" :align="isMobile ? 'center' : 'right'" class="px-3 mx-0">
        <v-btn
        dark
        color="#27AB9C"
        @click="ref2"
        >
        ถอนเงิน
        </v-btn>
        </v-col>
      </v-row>
<v-row v-resize="onResize">
  <v-col cols="12">
  <v-data-table
          :headers="dataMain['headers-2']"
          :items="itemsData"
          :search="search"
          color="#D4F1E4"
          id="DataMain"
          hide-default-header
          class="my-1 mx-3"
    >
     <template v-slot:header="{ props: { headers } }">
        <thead>
          <tr>
            <th v-for="h in headers" :class="h.class" :key="h.code">
              <span>{{h.text}}</span>
            </th>
          </tr>
        </thead>
    </template>
          <template
            v-slot:[`item.index`]="{ index: number }" >{{number +1}}
          </template>
           <template
            v-slot:[`item.orderIDRef`]="{ item: { orderIDRef } = {} }">{{ orderIDRef }}
          </template>
            <template
            v-slot:[`item.date`]="{ item: { date } = {} }">{{new Date(parseDate(date)).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}
          </template>
            <template
            v-slot:[`item.total_amount`]="{ item: {  total_amount } = {} }">{{ Number(total_amount).toLocaleString() }}
          </template>
           <template
            v-slot:[`item.payment_transaction_number`]="{ item: {  payment_transaction_number } = {} }">{{ payment_transaction_number }}
          </template>
           <template
            v-slot:[`item.transaction_status`]="{ item: { transaction_status } = {} }">
            <v-chip
            :style="transaction_status === 'Success' ? 'color: #42B971': 'color: #fcf0da'"
            class="ma-2"
            :color="transaction_status === 'Success' ? '#F0F9EE': '#e9a016'"
            >
            {{transaction_status === 'Success' ? 'ถอนเงินสำเร็จ' :  'รอการถอนเงิน'}}
          </v-chip>
          </template>
          <template
            v-slot:[`item.income`]="{ item: {  income } = {} }">{{ income.toLocaleString() }}
          </template>
            <!-- <template
            v-slot:[`item.detail`]="{ item }">
            <div
            v-if="item"
            style="color: #27AB9C"
            >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
          </template> -->
    </v-data-table>
  </v-col>
</v-row>
    <ListRevenue />
  </div>
  </div>
</template>
<script>
import dataMap from '../library/TestTable.json'
// import lds from '../loading/lds-facebook'
// import eventBus from '@/components/eventBus'
import { XlsxWorkbook, XlsxSheet, XlsxDownload } from 'vue-xlsx'

export default {
  components: {
    XlsxDownload,
    XlsxSheet,
    XlsxWorkbook,
    ListRevenue: () => import('./dialogRevenue'),
    ldsFacebook: () => import('@/components/loading/lds-facebook.vue')
  },
  data () {
    return {
      dataMain: dataMap,
      itemsData: [],
      itemsData2: [],
      itemsData3: [],
      search: '',
      index: 1,
      counterList: '',
      ListData: '',
      sheets: [],
      loading: false,
      percentage: '',
      isMobile: false
    }
  },
  created () {
    this.$EventBus.$on('filterData', this.filterData)
    this.$EventBus.$on('filterDate', this.filterDate)
    this.$EventBus.$on('ref', this.ref)
    this.init()
  },
  destroyed () {
    this.$EventBus.$off('filterData')
    this.$EventBus.$off('filterDate')
    this.$EventBus.$off('ref')
  },
  methods: {
    async init () {
      const data = await {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        choose_type: '',
        choose_date: ''
      }
      // console.log('data list ==========>', data)
      await this.$store.dispatch('actionsListRefMerchant', data)
      const { data: { incomeShop = '', listRefShare, listRefund, percentageNewOld = '' } = {} } = await this.$store.state.ModuleShop.stateListRefMerchant
      this.itemsData = await listRefShare
      this.itemsData3 = await listRefund
      this.ListData = await listRefShare.length
      this.counterList = await incomeShop
      this.percentage = await percentageNewOld
      this.sheets = await [{ name: 'รายการสำเร็จ', data: this.mapExcel(this.itemsData) }, { name: 'สินค้าที่ถูกตีกลับ', data: this.mapExcelSheetTwo(this.itemsData3) }]
      // this.sheets = await [{ name: 'SheetTwo', data: this.mapExcel(this.itemsData) }]
      // console.log('this.itemsData', this.itemsData)
    },
    async ref2 () {
      // console.log('dialogRevenue')
      this.$EventBus.$emit('dialogRevenue')
    },
    async ref () {
      this.loading = await true
      const data = await {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionsRefMerchant', data)
      this.itemsData2 = await this.$store.state.ModuleShop.stateRefMerchant
      if (this.itemsData2.result === 'SUCCESS') {
        await this.succesStatus()
        await this.$swal.fire({ icon: 'success', text: 'ถอนเงิน สำเร็จ', timer: 1800, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'Not Found Data' || this.itemsData2.result === 'SERVER ERROR') {
        this.succesStatus()
        this.$swal.fire({ icon: 'error', text: 'ไม่มีข้อมูลสำหรับการถอนเงิน / ถอนเงินไม่ได้เนื่องจากไม่มีข้อมูลในการถอนเงิน', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else {
        this.succesStatus()
        this.$swal.fire({ icon: 'error', text: `${this.itemsData2.result}`, timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      }
    },
    succesStatus () {
      // console.log('in function')
      this.init()
      this.$EventBus.$emit('appendData')
    },
    async filterData (data) {
      // console.log(data)
      const data2 = await {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        choose_type: data.name,
        choose_date: ''
      }
      // console.log(data)
      await this.$store.dispatch('actionsListRefMerchant', data2)
      const { data: { incomeShop = '', listRefShare, percentageNewOld = '' } = {} } = await this.$store.state.ModuleShop.stateListRefMerchant
      this.percentage = await percentageNewOld
      this.counterList = await incomeShop
      this.itemsData = await listRefShare
      this.ListData = await listRefShare.length
      this.sheets = await [{ name: 'SheetOne', data: this.itemsData }]
    },
    async filterDate () {
      // console.log('เข้า function')
      // console.log('filterDate', this.$store.state.ModuleShop.dateBus)
      const date = await this.$store.state.ModuleShop.dateBus
      const data2 = await {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        choose_type: '',
        choose_date: date
      }
      await this.$store.dispatch('actionsListRefMerchant', data2)
      const { data: { incomeShop = '', listRefShare, percentageNewOld = '' } = {} } = await this.$store.state.ModuleShop.stateListRefMerchant
      this.itemsData = await listRefShare
      this.ListData = await listRefShare.length
      this.counterList = await incomeShop
      this.percentage = await percentageNewOld
      this.sheets = await [{ name: 'SheetOne', data: this.itemsData }]
    },
    commaSeparateNumber (val) {
      while (/(\d + )(\d{3})/.test(val.toString())) {
        val = val.toString().replace(/(\d + )(\d{3})/, '$1' + ',' + '$2')
      }
      return val
    },
    parseDate (date) {
      // console.log('DATE', date)
      if (!date) return null
      const [day, month, year] = date.split('-')
      return `${year}-${month}-${day}`
    },
    mapExcel (val) {
      // var excel = {}
      // const data = []
      // var top = [
      //   'เลขที่ทำรายการชำระเงิน',
      //   'ราคาสุทธิที่ผู้ซื้อชำระผ่านระบบ',
      //   'สถานะการชำระเงิน',
      //   'ยอดคงเหลือหลังหักค่าธรรมเนียมรับชำระเงิน',
      //   'เลขอ้างอิงการรับชำระ',
      //   'ราคาขนส่งรวมราคาประมาณค่า',
      //   'ราคาขนส่งรวมราคาจริง',
      //   'วันที่ทำธุรกรรม',
      //   'เวลาทำธุรกรรม',
      //   'ค่าธรรมเนียมรับชำระเงิน',
      //   'ค่าธรรมเนียมรับชำระเงินพร้อมภาษี 7 %',
      //   'ค่าธรรมเนียม GP',
      //   'รายได้สุทธิหลังหักค่าธรรมเนียมรับชำระเงิน GP และ ขนส่ง',
      //   'การจัดส่งสำเร็จ',
      //   'การตีกลับสินค้า'
      // ]
      // for (const i in val) {
      //   // for (const n in top) {
      //   // excel['เลขที่ทำรายการชำระเงิน'] = val[i].payment_transaction_number
      //   data.push({
      //     ' เลขที่ทำรายการชำระเงินราคา ': val[i].payment_transaction_number
      //   })
      //   // }
      // }
      // return data
      const Excel = val.map(x => {
        return {
          'เลขที่ทำรายการชำระเงิน ': x.payment_transaction_number,
          'ราคาสุทธิที่ผู้ซื้อชำระผ่านระบบ ': x.total_amount,
          'สถานะการชำระเงิน ': x.transaction_status,
          'ยอดคงเหลือหลังหักค่าธรรมเนียมรับชำระเงิน ': x.final_amount,
          'เลขอ้างอิงการรับชำระ ': x.orderIDRef,
          'ราคาขนส่งรวม(ราคาประมาณค่า) ': x.total_shipping,
          'ราคาขนส่ง(รวมราคาจริง) ': x.extra_shipping,
          'วันที่ทำธุรกรรม ': x.date,
          'เวลาทำธุรกรรม ': x.time,
          'ค่าธรรมเนียมรับชำระเงิน ': x.fee,
          'ค่าธรรมเนียมรับชำระเงินพร้อมภาษี 7 % ': x.fee_vat,
          'ค่าธรรมเนียม GP ': x.gp,
          'รายได้สุทธิหลังหักค่าธรรมเนียมรับชำระเงิน GP และ ขนส่ง ': x.income,
          'ประเภทขนส่ง ': x.service_type,
          'การจัดส่งสำเร็จ ': x.shipping_status,
          'การตีกลับสินค้า ': x.shipping_return_status
        }
      })
      // console.log('excel:', Excel)
      return Excel
    },
    mapExcelSheetTwo (val) {
      const Excel = val.map(x => {
        return {
          'เลขที่ทำรายการชำระเงิน ': x.payment_transaction_number,
          'ราคาสินค้ารวมภาษี ': x.total_price_vat,
          'ราคาขนส่งรวม(ราคาประมาณค่า) ': x.total_shipping,
          'ราคาสุทธิรวมภาษีและขนส่ง ': x.net_price,
          'เลขอ้างอิงการรับชำระ ': x.orderIDRef,
          'สถานะขนส่ง ': x.delivery_type,
          'ประเภทขนส่ง ': x.service_type,
          'รายการ SKU  ': x.product_list,
          'ราคาขนส่งรวม (ราคาจริง) ': x.extra_shipping,
          'วันที่ทำธุรกรรม ': x.date,
          'เวลาทำธุรกรรม ': x.time
        }
      })
      // console.log('excel:', Excel)
      return Excel
    },
    onResize () {
      if (window.innerWidth < 650) {
        this.isMobile = true
      } else {
        this.isMobile = false
      }
    }
  }
}
</script>
<style scoped>
.header-style {
  background: #D4F1E4 !important;
}
.status-1 {
  color: #E9A016;
}
</style>
