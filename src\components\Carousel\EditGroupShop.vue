<template>
  <div v-if="disabledGroupShop">
    <v-container v-if="!MobileSize && !IpadSize" class="mt-4">
      <v-row :style="logoImage !== '' ? `margin-top: 40px;` : `margin-top: 24px;`">
        <v-col cols="12">
          <v-card :style="{ backgroundImage: `url(${backgroundImageBanner})`, backgroundPosition: 'center'}" style="border-radius: 8px; position: relative; max-width: 1223px;" class="mx-auto">
            <div style="display: flex; justify-content: flex-end;" :style="MobileSize || IpadSize  ? 'gap: 0; margin-left: 20%; width: 80%;' : IpadProSize ? 'gap: 1vw; margin-left: 24%; width: 76%;' : 'gap: 2vw; margin-left: 18%; width: 82%;'">
              <img v-if="logoImage !== ''" :src="logoImage" :height="MobileSize ? '36px' : IpadSize ? '38px' : IpadProSize ? '70px' : '100px'" :style="MobileSize ? 'position: absolute; top: -16px; left: 4px;' : IpadSize  ? 'position: absolute; top: -14px; left: 10px;' : IpadProSize ? 'position: absolute; top: -30px; left: 10px;' : 'position: absolute; top: -54px; left: 10px;'" alt="logo">
              <v-card-text style="text-align: center; font-weight: 600;" :style="MobileSize ? `margin-top:-4px; margin-right:-14px; font-size: xx-small; color: ${textColorBanner};` : IpadSize  ? `margin-right:-10px; font-size: smaller; color: ${textColorBanner};` : IpadProSize ? `font-size: large; color: ${textColorBanner};` : `font-size: x-large; color: ${textColorBanner};`">{{ textBanner }}</v-card-text>
              <v-btn style="font-weight: 600;" class="my-3 mx-2" :style="`background-color: ${bgColorAllBtn}; color: ${textColorAllBtn};`" small @click.prevent="AllGroupShop()" :href="pathAllGroupShop">ดูทั้งหมด</v-btn>
            </div>
            <!-- size ipadpro & pc -->
            <div class="d-flex justify-center pb-5" style="gap: 5%; height: 100%; padding-top: 3vw;" :style="IpadProSize ? 'padding-top: 3vw;' : 'padding-top: 2vw;'">
              <!-- Layout ด้านซ้าย -->
              <div :style="{ width: widthLeft }">
                <v-sheet class="mx-auto" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout1})`, backgroundPosition: 'center'}">
                  <v-card-text style="text-align: center; font-weight: 600;" :style="IpadProSize ? `font-size: medium; color: ${textColorLayout1};` : `font-size: large; color: ${textColorLayout1};`">{{ textLayout1 }}</v-card-text>
                  <v-slide-group v-model="model" class="pa-2" active-class="success" show-arrows>
                    <template #prev>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-left</v-icon>
                      </v-btn>
                    </template>
                    <template #next>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-right</v-icon>
                      </v-btn>
                    </template>
                    <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout1" :key="index">
                      <v-card class="ma-2" @click.prevent="gotoGroupHome(itemGroupShop)" style="display: flex; flex-direction: column; justify-content: flex-end; align-items: center;" :style="IpadProSize ? 'width: 86px; height: 100px;' : 'width: 126px; height: 140px;'">
                        <div :style="IpadProSize ? 'height: 66%; width: 100%;' : 'height: 74%; width: 100%;'" class="d-flex justify-center pa-1">
                          <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                          <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                        </div>
                        <div :style="IpadProSize ? 'height: 34%; width: 100%;' : 'height: 26%; width: 100%;'" class="d-flex justify-center pa-1">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col align="center" v-bind="attrs" v-on="on">
                                <span class="text-truncate d-inline-block" :style="IpadProSize ? 'font-size: smaller;' : 'font-size: smaller;'" style="font-weight: 600; width: 96%; line-height: 1; padding: -10px;">{{ itemGroupShop.shop_name }}</span>
                              </v-col>
                            </template>
                            <span>{{ itemGroupShop.shop_name }}</span>
                          </v-tooltip>
                        </div>
                      </v-card>
                    </v-slide-item>
                  </v-slide-group>
                </v-sheet>
              </div>
              <!-- Layout ด้านขวา -->
              <div v-if="disabledSelectLayout" :style="{ width: widthRight }">
                <v-sheet class="mx-auto" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout2})`, backgroundPosition: 'center'}">
                  <v-card-text style="text-align: center; font-weight: 600;" :style="IpadProSize ? `font-size: medium; color: ${textColorLayout2};` : `font-size: large; color: ${textColorLayout2};`">{{ textLayout2 }}</v-card-text>
                  <v-slide-group v-model="model" class="pa-2" active-class="success" show-arrows>
                    <template #prev>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-left</v-icon>
                      </v-btn>
                    </template>
                    <template #next>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-right</v-icon>
                      </v-btn>
                    </template>
                    <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout2" :key="index">
                      <v-card class="ma-2" @click.prevent="gotoGroupHome(itemGroupShop)" style="display: flex; flex-direction: column; justify-content: flex-end; align-items: center;" :style="IpadProSize ? 'width: 86px; height: 100px;' : 'width: 126px; height: 140px;'">
                        <div :style="IpadProSize ? 'height: 66%; width: 100%;' : 'height: 74%; width: 100%;'" class="d-flex justify-center pa-1">
                          <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                          <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                        </div>
                        <div :style="IpadProSize ? 'height: 34%; width: 100%;' : 'height: 26%; width: 100%;'" class="d-flex justify-center pa-1">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col align="center" v-bind="attrs" v-on="on">
                                <span class="text-truncate d-inline-block" :style="IpadProSize ? 'font-size: smaller;' : 'font-size: smaller;'" style="font-weight: 600; width: 96%; line-height: 1; padding: -10px;">{{ itemGroupShop.shop_name }}</span>
                              </v-col>
                            </template>
                            <span>{{ itemGroupShop.shop_name }}</span>
                          </v-tooltip>
                        </div>
                      </v-card>
                    </v-slide-item>
                  </v-slide-group>
                </v-sheet>
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
    <v-container v-if="MobileSize || IpadSize" :class="MobileSize || IpadSize ? 'mt-0 pt-0' : 'mt-4'">
      <v-row :style="logoImage !== '' ? `margin-top: 40px;` : `margin-top: 24px;`">
        <div :style="MobileSize ? 'width:100%; margin-top: -30px;' : 'width:96%; margin: 0 2%; border-radius: 10px; margin-top: 14px;'">
          <v-card :style="[{ backgroundImage: `url(${backgroundImageBanner})`, backgroundPosition: 'center'}, IpadSize ? { borderRadius: '10px' } : {} ]" style=" position: relative; max-width: 1223px;" class="mx-auto">
            <div style="display: flex; justify-content: flex-end;" :style="MobileSize ? 'gap: 0; margin-left: 12%; width: 88%; padding-top: 2vh;' : IpadSize  ? 'gap: 0; margin-left: 12%; width: 88%; padding-top: 3vh;' : IpadProSize ? 'gap: 1vw; margin-left: 24%; width: 76%;' : 'gap: 2vw; margin-left: 18%; width: 82%;'">
              <img v-if="logoImage !== ''" :src="logoImage" :height="MobileSize ? '50px' : IpadSize ? '70px' : IpadProSize ? '70px' : '100px'" :style="MobileSize ? 'position: absolute; top: -20px; left: 4px;' : IpadSize  ? 'position: absolute; top: -24px; left: 10px;' : IpadProSize ? 'position: absolute; top: -30px; left: 10px;' : 'position: absolute; top: -54px; left: 10px;'" alt="logo">
              <v-card-text style="text-align: center; font-weight: 600;" :style="MobileSize ? `margin-top:-4px; margin-right:-14px; font-size: 11px; color: ${textColorBanner};` : IpadSize  ? `margin-right:-10px; font-size: medium; color: ${textColorBanner};` : IpadProSize ? `font-size: medium; color: ${textColorBanner};` : `font-size: x-large; color: ${textColorBanner};`">{{ textBanner }}</v-card-text>
              <v-btn style="font-weight: 600;" class="my-4 mx-2" :style="`background-color: ${bgColorAllBtn}; color: ${textColorAllBtn};`" x-small @click.prevent="AllGroupShop()" :href="pathAllGroupShop">ดูทั้งหมด</v-btn>
            </div>
            <!-- size Mobile & ipad -->
            <div class="d-flex justify-center flex-column align-center pb-3" style="height: 100%; padding-top: 1vw;">
              <!-- Layout ด้านซ้าย -->
              <div style="width: 90%;">
                <v-sheet class="mx-auto mb-2" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout1})`, backgroundPosition: 'center'}">
                  <v-card-text style="text-align: center; font-weight: 600; margin: -12px 0;" :style="MobileSize ? `font-size: x-small; color: ${textColorLayout1};` : `font-size: small; color: ${textColorLayout1};`">{{ textLayout1 }}</v-card-text>
                  <v-slide-group v-model="model" class="pa-2" active-class="success" style="margin-top: -10px;" show-arrows>
                    <template #prev>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-left</v-icon>
                      </v-btn>
                    </template>
                    <template #next>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-right</v-icon>
                      </v-btn>
                    </template>
                    <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout1" :key="index">
                      <v-card class="ma-1" @click.prevent="gotoGroupHome(itemGroupShop)" style="display: flex; flex-direction: column; align-items: center; width: 70px; height: 80px;">
                        <div style="height: 70%; width: 100%;" class="d-flex justify-center pa-1">
                          <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                          <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                        </div>
                        <div style="height: 30%; width: 100%; margin-top: -10px !important;" class="d-flex justify-center pa-1">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col align="center" v-bind="attrs" v-on="on">
                                <span class="text-truncate d-inline-block" style="font-weight: 600; width: 96%; line-height: 1;" :style="MobileSize ? `font-size: xx-small;` : `font-size: x-small;`">{{ itemGroupShop.shop_name }}</span>
                              </v-col>
                            </template>
                            <span>{{ itemGroupShop.shop_name }}</span>
                          </v-tooltip>
                        </div>
                      </v-card>
                    </v-slide-item>
                  </v-slide-group>
                </v-sheet>
              </div>
              <!-- Layout ด้านขวา -->
              <div style="width: 90%;">
                <v-sheet class="mx-auto mb-2" elevation="1" width="100%" style="border-radius: 8px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;" :style="{ backgroundImage: `url(${backgroundImageLayout2})`, backgroundPosition: 'center'}">
                  <v-card-text style="text-align: center; font-weight: 600; margin: -12px 0;" :style="MobileSize ? `font-size: x-small; color: ${textColorLayout2};` : `font-size: small; color: ${textColorLayout2};`">{{ textLayout2 }}</v-card-text>
                  <v-slide-group v-model="model" class="pa-2" active-class="success" style="margin-top: -10px;" show-arrows>
                    <template #prev>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-left</v-icon>
                      </v-btn>
                    </template>
                    <template #next>
                      <v-btn icon :color="colorArrowBtn" x-small>
                        <v-icon>mdi-chevron-right</v-icon>
                      </v-btn>
                    </template>
                    <v-slide-item v-for="(itemGroupShop, index) in groupShopLayout2" :key="index">
                      <v-card class="ma-1" @click.prevent="gotoGroupHome(itemGroupShop)" style="display: flex; flex-direction: column; align-items: center; width: 70px; height: 80px;">
                        <div style="height: 70%; width: 100%;" class="d-flex justify-center pa-1">
                          <img v-if="itemGroupShop.shop_logo !== ''" :src="itemGroupShop.shop_logo" width="94%" height="auto" alt="logo">
                          <img v-else :src="require('@/assets/NoImage.png')" width="94%" height="auto" alt="logo">
                        </div>
                        <div style="height: 30%; width: 100%; margin-top: -10px !important;" class="d-flex justify-center pa-1">
                          <v-tooltip top>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col align="center" v-bind="attrs" v-on="on">
                                <span class="text-truncate d-inline-block" style="font-weight: 600; width: 96%; line-height: 1;" :style="MobileSize ? `font-size: xx-small;` : `font-size: x-small;`">{{ itemGroupShop.shop_name }}</span>
                              </v-col>
                            </template>
                            <span>{{ itemGroupShop.shop_name }}</span>
                          </v-tooltip>
                        </div>
                      </v-card>
                    </v-slide-item>
                  </v-slide-group>
                </v-sheet>
              </div>
            </div>
          </v-card>
        </div>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      model: null,
      logoImage: '',
      backgroundImageBanner: '',
      textBanner: '',
      textColorBanner: '',
      disabledSelectLayout: false,
      backgroundImageLayout1: '',
      backgroundImageLayout2: '',
      widthLeft: '90%',
      widthRight: '',
      textLayout1: '',
      textColorLayout1: '',
      textLayout2: '',
      textColorLayout2: '',
      textColorAllBtn: '#333333',
      bgColorAllBtn: '#ffffff',
      colorArrowBtn: '#333333',
      groupShopLayout1: [],
      groupShopLayout2: [],
      disabledGroupShop: false,
      pathAllGroupShop: process.env.VUE_APP_DOMAIN + 'allGroupShop?page=1'
    }
  },
  async created () {
    this.getDetailGroupShop()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    AllGroupShop () {
      this.$router.push({ path: '/allGroupShop?page=1' }).catch(() => {})
    },
    async getDetailGroupShop () {
      await this.$store.dispatch('actionGetCustomGroupShop')
      var response = await this.$store.state.ModuleShop.stateGetCustomGroupShop
      // console.log('response', response)
      if (response.code === 200) {
        this.logoImage = response.data.logo_banner
        this.textBanner = response.data.text_banner
        this.textLayout1 = response.data.text_layout1
        this.textLayout2 = response.data.text_layout2
        this.backgroundImageBanner = response.data.bg_banner
        this.backgroundImageLayout1 = response.data.bg_layout1
        this.backgroundImageLayout2 = response.data.bg_layout2
        this.groupShopLayout1 = response.data.group_shop_layout1
        this.groupShopLayout2 = response.data.group_shop_layout2
        this.textColorBanner = response.data.text_banner_color
        this.textColorLayout1 = response.data.color_layout1
        this.textColorLayout2 = response.data.color_layout2
        this.textColorAllBtn = response.data.color_text_btn_all
        this.bgColorAllBtn = response.data.color_btn_all
        this.colorArrowBtn = response.data.color_btn_arrow
        if (response.data.num_layout === '2') {
          this.disabledSelectLayout = true
        }
        this.widthLeft = response.data.size_layout1
        this.widthRight = response.data.size_layout2
        this.disabledGroupShop = true
      } else {
        this.disabledGroupShop = false
      }
    },
    gotoGroupHome (val) {
      // console.log('val------>', val)
      localStorage.setItem('groupshop_name', Encode.encode(val.shop_name))
      localStorage.setItem('groupshop_id', Encode.encode(val.shop_id))
      const shopCleaned = val.shop_name.replace(/\s/g, '-')
      this.$router
        .push({ path: `/GroupShoppage/${shopCleaned}-${val.shop_id}` })
        .catch(() => {})
    }
  }
}
</script>

<style scoped>

</style>
