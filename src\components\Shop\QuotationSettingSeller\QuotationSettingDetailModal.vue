<template>
  <div class="text-center">
    <v-dialog v-model="ModalSettingDetailQuotation" width="600" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>รายละเอียดรูปแบบหมายเลขใบเสนอราคา</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text>
            <v-row>
              <v-col cols="12" v-if="data.use_logo === 'Y'">
                <v-img :src="data.logo_path" :lazy-src="data.logo_path" width="100" height="100" contain></v-img>
              </v-col>
              <v-col cols="12">
                <span class="float-left">ชื่อเริ่มต้นผู้ทำการออกใบเสนอราคา : </span>
                <b class="pl-3">{{ data.prepared_by_default }} </b>
              </v-col>
              <v-col cols="12">
                <span class="float-left">รูปแบบหมายเลขใบเสนอราคา : </span>
                <b class="pl-3">{{ data.code_pattern !== null ? data.code_pattern : '-' }} </b>
              </v-col>
              <v-col cols="12">
                <span class="float-left">ต้องการใช้วันที่ในรูปแบบหรือไม่ : </span>
                <b class="pl-3">{{ data.use_date_in_pattern !== null ? data.use_date_in_pattern === 'Y' ? 'ใช่' : 'ไม่' : '-' }} </b>
              </v-col>
              <v-col cols="12">
                <span class="float-left">จำนวนตัวเลขที่ต้องการใช้ในรูปแบบ : </span>
                <b class="pl-3" v-if="data.number_pattern !== null">{{ data.number_pattern }} หลัก </b>
                <b class="pl-3" v-else> - </b>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn :class="MobileSize ? 'px-3' : 'px-5'" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn v-if="data.status === 'active'" class="px-5 white--text" color="#D1392B" @click="updateStatusSettingQu('inactive')">ยกเลิกใช้งาน</v-btn>
            <v-btn v-if="data.status === 'inactive'" class="px-5 white--text" color="#1AB759" @click="updateStatusSettingQu('active')">เปิดใช้งาน</v-btn>
            <v-btn :class="MobileSize ? 'px-3 white--text' : 'px-5 white--text'" color="#27AB9C" @click="editSettingQuotation()">แก้ไข</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <ModalSettingQuotation ref="ModalSettingQuotation" />
  </div>
</template>

<script>
// import { Decode } from '@/services'
// import eventBus from '@/components/eventBus'
export default {
  components: {
    ModalSettingQuotation: () => import('@/components/Shop/QuotationSettingSeller/QuotationSettingModal')
  },
  data () {
    return {
      lazy: false,
      ModalSettingDetailQuotation: false,
      action: '',
      data: ''
    }
  },
  watch: {
  },
  mounted () {
  },
  created () {
    this.$EventBus.$on('editSettingQUSuccess', this.cancel)
  },
  beforeDestroy () {
    this.$EventBus.$off('editSettingQUSuccess')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('IpadProSize')
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('IpadSize')
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    async open (item) {
      this.$store.commit('openLoader')
      // console.log('open ModalSettingDetailQuotation item', item)
      const data = {
        seller_shop_id: item.seller_shop_id,
        qu_setting_id: item.id
      }
      await this.$store.dispatch('actionsDetailQuotation', data)
      const res = await this.$store.state.ModuleSettingQuotation.stateDetailQuotation
      // console.log('res', res)
      if (res.message === 'Detail QU Setting success.') {
        this.$store.commit('closeLoader')
        this.data = res.data[0]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
      this.ModalSettingDetailQuotation = true
    },
    cancel () {
      this.ModalSettingDetailQuotation = false
    },
    editSettingQuotation () {
      // console.log('editSettingQuotation')
      this.cancel()
      this.$refs.ModalSettingQuotation.open(this.data, 'edit', 'detail')
    },
    async updateStatusSettingQu (status) {
      const data = {
        seller_shop_id: this.data.seller_shop_id,
        qu_setting_id: this.data.id,
        status: status
      }
      await this.$store.dispatch('actionsUpdateStatusQuotation', data)
      const res = await this.$store.state.ModuleSettingQuotation.stateUpdateStatusQuotation
      if (res.message === 'Update status QU Setting success.') {
        this.$EventBus.$emit('settingQUSuccess')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ดำเนินการสำเร็จ'
        })
        this.ModalSettingDetailQuotation = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>
<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
.title-product {
  font-size: 14px;
}
.detail-product {
  font-size: 14px;
}
.shop-name {
  font-weight: 700;
  font-size: 20px;
}
</style>
