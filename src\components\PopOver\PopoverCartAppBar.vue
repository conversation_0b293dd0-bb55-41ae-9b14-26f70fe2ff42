<template>
  <a-popover v-model="visibleCart" placement="bottomRight" trigger="click" v-if="this.productList.length !== 0">
    <template slot="content" >
      <a-card style="max-width: 350px; width: 350px; overflow-y: scroll; height: 250px;">
        <a-row type="flex" justify="center" >
          <a-col :span="24" v-for="(item, index) in newCartItem" :key="index">
            <div class="container">
              <a-row type="flex">
                <a-col :span="4">
                  <v-img :src="`${item.product_image.url}`" class="image" v-if="item.product_image.url !== null" />
                  <v-img src="@/assets/NoImage.png" class="image" v-else />
                </a-col>
                <a-col :span="18">
                  <span style="padding-left: 25px; font-weight: bold">{{item.product_name}}</span><br>
                  <span style="padding-left: 25px">รหัสสินค้า: {{item.sku}}</span><br>
                  <span style="padding-left: 25px;">จำนวน: {{item.quantity}}</span>
                  <span style="padding-left: 25px">฿ {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </a-col>
                <a-col :span="2" style="padding-left: 0px">
                  <br>
                  <br>
                  <a-icon type="delete" @click="dialogDelete(item)"/>
                </a-col>
              </a-row>
            </div>
            <a-divider style="margin-bottom: 0px; margin-top: 0px;"/>
          </a-col>
        </a-row>
      </a-card>
      <a-row justify="center" type="flex">
        <a-col :span="24">
          <a-row justify="center" type="flex">
            <a-col :span="12" style="padding: 10px 0">
              <a-button block icon="shopping-cart" @click="goCart()">
                ไปที่รถเข็นสินค้า
              </a-button>
            </a-col>
          </a-row>
        </a-col>
      </a-row>
    </template>
    <v-btn class="hidden-sm-and-down mt-3" icon>
      <v-badge :content="countCart" :value="countCart" color="red" top bordered overlap>
        <v-icon color="white">mdi-cart</v-icon>
      </v-badge>
    </v-btn>
  </a-popover>
  <a-popover v-model="visibleNoCart" placement="bottomRight" trigger="click" v-else>
    <v-btn class="hidden-sm-and-down mt-3" icon>
      <v-badge :content="countCart" :value="countCart" color="red" top bordered overlap>
        <v-icon  color="white">mdi-cart</v-icon>
      </v-badge>
    </v-btn>
    <template slot="content">
      <a-card style="width: 350px; height: 250px;" align="center">
        <a-icon type="shopping-cart" :style="{ fontSize: '28px' }"/>
        <span style="font-weight: bold">ไม่มีสินค้าในรถเข็น.</span>
      </a-card>
    </template>
  </a-popover>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      productList: [],
      countCart: null,
      CartLangth: null,
      visibleNoCart: false,
      visibleCart: false
    }
  },
  computed: {
    newCartItem () {
      const newCartItem = this.productList
      return newCartItem
    }
  },
  watch: {
    productList (val) {
      // console.log('productList', val)
    }
  },
  async created () {
    this.$EventBus.$on('getCartPopOver', this.Start)
    this.$EventBus.$on('closeModalCartNoLogin', this.closeModalCartNoLogin)
    this.$EventBus.$on('closeModalCart', this.closeModalCart)
    this.Start()
  },
  methods: {
    async Start () {
      if (localStorage.getItem('oneData') === null) {
        // console.log('getDetailCartLocal')
        this.getDetailCartLocal()
      } else {
        this.getDetailCartLogin()
        // await this.$store.dispatch('actionsUserDetailPage')
        // const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
        // console.log('user detail', userdetail)
        // if (dataRole.role === 'ext_buyer') {
        //   var CartLangth = userdetail.data[0].cart_data.ext_buyer
        //   if (CartLangth.length === 0) {
        //     this.getDetailCartLogin()
        //   } else {
        //
        //   }
        // } else {
        //   this.CartLangth = JSON.parse(userdetail.data[0].cart_data.purchaser.shop_list)[0].product_list
        // }
        // // console.log('getDetailCartLogin', this.CartLangth)
        // if (this.CartLangth !== 0) {
        //   this.getDetailCartLogin()
        // } else {
        //   this.productList = []
        //   this.countCart = 0
        // }
      }
    },
    async getDetailCartLocal () {
      this.productList = []
      if (localStorage.getItem('_cartData') !== null) {
        var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
        var data = {
          shop_to_cal: [],
          product_to_cal: [],
          shop_list: cartData.shop_list
        }
        // console.log('cartData =======', cartData)
        await this.$store.dispatch('ActionLocalstorageDetailCart', data)
        const res = await this.$store.state.ModuleCart.stateLocalstorageDetailCart
        if (res.message === 'Get localstorage detail cart success') {
          // console.log('response ===', res.data)
          var setData = {
            product_to_cal: res.data.product_to_cal,
            shop_to_cal: res.data.product_to_cal,
            address_data: {},
            shop_list: res.data.shop_list
          }
          await localStorage.setItem('_cartData', Encode.encode(setData))
          for (let index = 0; index < res.data.shop_list.length; index++) {
            const element = res.data.shop_list[index]
            for (let j = 0; j < element.product_list.length; j++) {
              const element2 = element.product_list[j]
              this.productList.push(element2)
            }
          }
          this.countCart = this.productList.length
          // console.log('not have one data', this.productList)
        }
      } else {
        // console.log('null')
        this.productList = []
        this.countCart = 0
      }
    },
    async getDetailCartLogin () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var CartLangth
      var ListProduct = []
      // console.log('in get cart login')
      this.productList = []
      var data = {
        role_user: dataRole.role,
        shop_to_cal: [],
        product_to_cal: []
      }
      await this.$store.dispatch('ActionDetailCart', data)
      const res = await this.$store.state.ModuleCart.stateDetailCart
      // console.log('res =========================', res)
      if (res.message === 'Get detail cart success') {
        this.productList = []
        ListProduct = res.data.shop_list
        // console.log('ListProduct', ListProduct)
        for (let index = 0; index < ListProduct.length; index++) {
          const element = ListProduct[index].product_list
          for (let j = 0; j < element.length; j++) {
            this.productList.push(element[j])
          }
          this.countCart = this.productList.length
        }
      } else if (res.message === 'No product in cart.') {
        this.countCart = 0
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    dialogDelete (item) {
      // console.log('item ===', item)
      // const ToastDelete = this.$swal.mixin({
      //   toast: true,
      //   showCancelButton: true,
      //   confirmButtonText: 'ยืนยัน',
      //   cancelButtonText: 'ยกเลิก',
      //   cancelButtonColor: '#d33'
      // })
      this.$swal.fire({
        toast: true,
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        cancelButtonColor: '#d33',
        icon: 'warning',
        title: 'คุณต้องการลบสินค้าหรือไม่'
      }).then((result) => {
        if (result.isConfirmed) {
          this.UpdateCart(item)
        } else if (result.isDismissed) {
          this.getDetailCart()
        }
      }).catch(() => {
      })
    },
    async UpdateCart (val) {
      if (localStorage.getItem('oneData') === null) {
        var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
        for (let index = 0; index < cartData.shop_list.length; index++) {
          const element = cartData.shop_list[index]
          for (let index2 = 0; index2 < element.product_list.length; index2++) {
            const element2 = element.product_list[index2]
            if (element2.product_id === val.product_id) {
              element.product_list.splice(index2, 1)
            }
          }
          if (element.product_list.length === 0) {
            cartData.shop_list.splice(index, 1)
          }
        }
        // console.log('cart length', cartData)
        if (cartData.length === 0) {
          localStorage.removeItem('_cartData')
          // console.log('remove')
          this.Start()
        } else {
          await localStorage.setItem('_cartData', Encode.encode(cartData))
          this.Start()
        }
      } else {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        // console.log('dataRole', dataRole)
        var data = {
          seller_shop_id: val.shop_id,
          role_user: dataRole.role,
          sku: val.sku,
          quantity: 0
        }
        await this.$store.dispatch('ActionUpdateCart', data)
        const res = await this.$store.state.ModuleCart.stateUpdateCart
        if (res.message === 'Update Cart Success') {
          this.Start()
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'ลบสินค้าในรถเข็นเรียบร้อย'
          })
          this.Start()
          // this.$EventBus.$emit('getCartPopOver')
        } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'ใส่ข้อมูลไม่ครบ'
          })
        } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR'
          })
        }
      }
    },
    goCart () {
      this.$router.push('/shoppingcart')
    },
    closeModalCartNoLogin () {
      this.visibleNoCart = false
    },
    closeModalCart () {
      this.visibleCart = false
    }
  }
}
</script>
