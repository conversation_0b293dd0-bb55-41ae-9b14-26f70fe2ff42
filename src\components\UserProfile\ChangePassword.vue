<template>
  <div>
    <v-form ref="ChangePasswordform" :lazy-validation="lazy">
      <v-card width="100%" height="700" elevation="0" class="mb-4">
        <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">เปลี่ยนรหัสผ่าน</v-card-title>
        <v-row justify="center" align-content="center">
          <v-col cols="12" md="12" class="mt-16">
            <v-row justify="center">
              <v-img src="@/assets/ImageINET-Marketplace/ICONProfile/Group_759.png" width="196px" height="168px" contain></v-img>
            </v-row>
          </v-col>
          <v-card-text>
            <v-col cols="12" md="12" class="mt-4">
              <v-row justify="center" align-content="center" dense class="mx-2">
                <!-- รหัสผ่านปัจจุบัน -->
                <v-col cols="12" md="2" class="pt-3">
                  <span>รหัสผ่านปัจจุบัน</span><span style="float: right;"> :</span>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                   v-model="oldPassword"
                   placeholder="ระบุรหัสผ่านปัจจุบัน"
                   outlined
                   dense
                   :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'"
                   :type="show1 ? 'text' : 'password'"
                   @click:append="show1 = !show1"
                   :rules="itemRules.Password"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row justify="center" align-content="center" dense class="mx-2">
                <!-- รหัสผ่านปัจจุบัน -->
                <v-col cols="12" md="2" class="pt-3">
                  <span>รหัสผ่านใหม่</span><span style="float: right;"> :</span>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                   v-model="newPassword"
                   placeholder="ระบุรหัสผ่านใหม่"
                   outlined dense
                   :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'"
                   :type="show2 ? 'text' : 'password'"
                   @click:append="show2 = !show2"
                   :rules="itemRules.Password"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row justify="center" align-content="center" dense class="mx-2 pb-0">
                <!-- รหัสผ่านปัจจุบัน -->
                <v-col cols="12" md="2" class="pt-3">
                  <span>ยืนยันรหัสผ่านใหม่</span><span style="float: right;"> :</span>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                   v-model="confirmPassword"
                   placeholder="ยืนยันรหัสผ่านใหม่"
                   outlined dense
                   :append-icon="show3 ? 'mdi-eye' : 'mdi-eye-off'"
                   :type="show3 ? 'text' : 'password'"
                   @click:append="show3 = !show3"
                   :rules="itemRules.confirmPassword"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row justify="center" align-content="center" dense class="mx-2 mt-0">
                <!-- action -->
                <v-col cols="12" md="5">
                  <v-row justify="end" style="margin-right: 1px;">
                    <v-btn outlined color="#27AB9C" class="mr-4" @click="backToHome()">ยกเลิก</v-btn>
                    <v-btn color="#27AB9C" dark @click="CheckTypeReset()">บันทึก</v-btn>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
          </v-card-text>
        </v-row>
      </v-card>
    </v-form>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      lazy: false,
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
      show1: false,
      show2: false,
      show3: false,
      onedata: [],
      itemRules: {
        Password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ],
        confirmPassword: [
          v => !!v || 'กรุณากรอกรยืนยันรหัสผ่าน'
        ]
      }
    }
  },
  created () {
    // console.log('เข้า ChangePassword')
    if (localStorage.getItem('oneData') !== null) {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(this.onedata)
    } else {
      this.$router.push({ path: '/ ' }).catch(() => {})
    }
    this.$EventBus.$emit('changeNav')
  },
  methods: {
    backToHome () {
      this.$refs.ChangePasswordform.reset()
      this.$router.push({ path: '/ ' }).catch(() => {})
    },
    CheckTypeReset () {
      if (this.$refs.ChangePasswordform.validate(true)) {
        if (this.onedata.user.type_user === 'oneID_user') {
          this.changePasswordOneID()
        } else {
          this.changePasswordMarket()
        }
      }
    },
    changePasswordOneID () {
      this.$swal.fire({ icon: 'warning', text: 'อยู่ในขั้นตอนพัฒนา โปรดรออีกหน่อย!!!', showConfirmButton: false, timer: 1500 })
    },
    async changePasswordMarket () {
      var data = {
        old_password: this.oldPassword,
        new_password: this.newPassword,
        chk_new_password: this.confirmPassword
      }
      await this.$store.dispatch('actionChangePasswordMarket', data)
      var response = await this.$store.state.ModuleUser.stateChangePasswordMarket
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', text: 'คุณได้ทำการเปลี่ยนรหัสผ่านสำเร็จ', showConfirmButton: false, timer: 1500 })
        // this.$EventBus.$emit('Logout')
      } else {
        this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2500 })
      }
    }
  }
}
</script>
