<template>
  <div>
    <v-card v-if="track" rounded>
      <div v-if="play">
        <video ref="videoElement" :id="track.sid" height="400px" width="100%" style="transform: scaleX(-1)"></video>
        <audio ref="audioElement" :id="audio.trackSid"></audio>
      </div>
      <video v-else :poster="imgURL" ref="videoElement1" height="400px" width="100%"></video>

      <div class="text-overlay">
        <div rounded style="display: flex !important; flex-direction: column; min-height: 300px;">
          <div>
            <div style="flex-grow: 1;">
              <v-row class="px-4" dense no-gutters>
                <div style="background-color: red; border-radius: 5px 0  0 5px;" class="pr-2 pl-1 pb-1">
                  <span class="live-text"><v-icon class="mr-1" color="white" x-small>mdi-circle-medium</v-icon>Live</span>
                </div>
                <div style="background-color: gray; border-radius: 0 5px  5px 0;" class="px-2 pb-1">
                  <span class="live-text"><v-icon class="mr-1" color="white" x-small>mdi-eye</v-icon>{{ updateViewers > 0 ? updateViewers : viewers }}</span>
                </div>
              </v-row>
            </div>
            <div style="height:300px" class="hoverCard sd-from pt-2">
              <v-btn v-if="!play" @click="track.length == 0 ? newRoom() : controlsVideo('play')" class="buttonVideo" outlined style="font-weight: 700;">
                <v-icon>mdi-play</v-icon>
              </v-btn>
              <v-btn v-else icon @click="controlsVideo('pause')" class="buttonVideo" outlined style="font-weight: 700;">
                <v-icon>mdi-pause</v-icon>
              </v-btn>
            </div>

            <div style="margin-top: 10px;">
              <v-row class="px-4" dense no-gutters>
                <v-col cols="12" align="start">
                  <v-row dense>
                    <v-col cols="2" align-self="center">
                      <TwoLayerCircle
                      :size="40"
                      :value="100"
                      color="grey"
                      :imgURL="imgURL"
                      :smallSize="true"
                      />
                    </v-col>
                    <v-col class="pl-4">
                      <span style="color: black;"><b>{{ title | truncate(30, '...') }}</b></span><br/>
                      <span class="" style="color: black">{{ shopName }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </div>
          </div>
        </div>
      </div>
    </v-card>
  </div>
</template>

<script>
import { Decode } from '@/services'
import TwoLayerCircle from './TwoLayerCircle.vue'
import {
  Room,
  RoomEvent
} from 'livekit-client'
export default {
  filters: {
    truncate: function (value, limit) {
      if (value !== null & value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    TwoLayerCircle
  },
  props: {
    roomName: {
      type: String,
      required: true
    },
    title: {
      type: String
    },
    shopName: {
      type: String,
      required: true
    },
    imgURL: {
      type: String,
      required: true
    },
    sid: {
      type: String,
      required: true
    },
    viewers: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      room: null,
      remoteTracksMap: new Map(),
      track: [],

      updateViewers: 0,
      getTrackSuccess: false,
      play: false,
      pause: false,

      // LIVEKIT_URL: 'wss://helloworld-nt1b7zmh.livekit.cloud'
      LIVEKIT_URL: 'wss://meet-lab.one.th'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  async mounted () {
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.userName = onedata.user.username
  },
  methods: {
    async newRoom (item) {
      // ครั้งแรกจะไม่มี item เวลากด next จะมี item เดิมอยู่ต้องลบก่อน
      // console.log('newRoom')
      if (item !== undefined) {
        await this.leaveRoom()
      }
      this.room = new Room()
      this.remoteTracksMap = new Map()
      await this.room.on(
        RoomEvent.TrackSubscribed,
        (_track, publication, participant) => {
          this.updateViewers = this.room.numParticipants
          // console.log('TrackSubscribed')
          this.remoteTracksMap.set(publication.trackSid, {
            trackPublication: publication,
            participantIdentity: participant.identity
          })
        }
      )

      this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        this.updateViewers = this.room.numParticipants
        // console.log('Host ออกจากห้อง')
        // console.log('Live: first', participant)
        if (participant.identity.includes('Host')) {
          this.listLiving = this.listLiving.filter(obj => obj.name !== participant.identity)
          if (this.listLiving.length > 1) {
            if (this.currentIndex === this.listLiving.length - 1) { // ถ้าอยู่หน้าสุดท้ายของสไลด์
              this.currentIndex = 0
              document.getElementById('prev').click()
            } else {
              document.getElementById('next').click()
            }
          }
        }
      })
      this.getRoom()
    },
    async getRoom () {
      var roomName = this.roomName
      if (roomName) {
        this.token = await this.getToken(roomName, this.userName)
        await this.room.connect(this.LIVEKIT_URL, this.token)
        // console.log('room', this.room)
        this.updateViewers = this.room.numParticipants

        for (const remoteTrack of this.remoteTracksMap.values()) {
          if (remoteTrack.trackPublication.kind === 'video') {
            this.track = remoteTrack.trackPublication.videoTrack
          } else {
            this.audio = remoteTrack.trackPublication.audioTrack
          }
        }
        // console.log('track', this.track)
        this.play = true
        await this.track.attach(this.$refs.videoElement)
        await this.track.attach(this.$refs.videoElement)
        this.audio.attach(this.$refs.audioElement)
      }
    },
    async getToken (roomName, participantName) {
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roomName, participantName })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(`Failed to get token: ${error.errorMessage}`)
      }

      const data = await response.json()
      return data.token
    },
    async controlsVideo (action) {
      if (action === 'play' && this.pause) {
        this.play = true
        this.pause = false
        await this.track.attach(this.$refs.videoElement)
        await this.track.attach(this.$refs.videoElement)
        this.audio.attach(this.$refs.audioElement)
      } else {
        this.play = false
        this.pause = true
        document.getElementById(this.track.sid).pause()
        this.$refs.audioElement.pause()
      }
    }
  }
}
</script>

<style scoped>
.text-overlay {
  position: absolute;
  top: 10px;
  background-color: transparent;
  color: white;
  width: 100%;
}
.live-text {
  color: white;
  font-size: 12px;
}
.buttonVideo {
  border: 3px solid;
  border-radius: 30px;
  top: 70%;
  opacity: 0;
  background-color: whitesmoke;
}
.icon {
  color: #757D8A;
 }
.sd-from:hover .buttonVideo {
  opacity: 100;
  z-index: 2;
}
 .icon {
  color: #757D8A;
 }
.sd-from:hover .icon {
  color: #C4C4C4;
}
.sd-from:hover {
  display: block;
  border-bottom-right-radius: 20px 20px;
  border-bottom-left-radius: 20px 20px;
  z-index: 2;
}
.hoverCard:hover {
  /* background-color: rgba(193, 193, 193, 1); */
}
.divCenter {
  text-align: center;
  margin-top: 100px;
  margin-bottom: 100px;
}
</style>
