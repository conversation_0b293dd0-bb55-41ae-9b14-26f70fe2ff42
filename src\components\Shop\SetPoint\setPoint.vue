<template>
  <v-container :class="MobileSize ? 'background_productMobile my-4' : 'background_product pa-6'" style="background-color: #FFFFFF">
      <v-row>
        <v-col>
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">จัดการแต้มส่วนลด</span>
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-row style="align-items: baseline;" class="pl-3">
            <v-switch class="pl-1" false-value="no" true-value="yes" inset v-model="usePointOrNot"></v-switch>
            <span class="pb-0" style="font-weight: 400; font-size:18px; line-height: 32px;">เปิด-ปิดการใช้งานแต้มส่วนลด</span>
          </v-row>
        </v-col>
      </v-row>
      <v-row class="pt-6">
        <v-col cols="4" md="3" sm="3">
          <span style=" font-size:16px; line-height: 24px;">ยอดคำสั่งซื้อ</span>
        </v-col>
        <v-col cols="8" md="6" sm="6">
          <v-text-field outlined dense v-model="baht1" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')">
          </v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="4" md="3" sm="3">
          <span style=" font-size:16px; line-height: 24px;">แต้มที่ได้</span>
        </v-col>
        <v-col cols="8" md="6" sm="6">
          <v-text-field outlined dense v-model="point1">
          </v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="3" sm="3">
          <span style=" font-size:16px; line-height: 24px;">x แต้มต่อ x บาท</span>
        </v-col>
        <v-col cols="6" md="3" sm="3">
          <span style=" font-size:16px; line-height: 24px;">แต้ม</span>
          <v-text-field outlined type="number" dense v-model="point2">
          </v-text-field>
        </v-col>
        <v-col cols="6" md="3" sm="3">
          <span style=" font-size:16px; line-height: 24px;">บาท</span>
          <v-text-field outlined type="number" dense v-model="baht2">
          </v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="3" sm="6">
          <span style=" font-size:16px; line-height: 24px;">ระยะเวลาวันหมดอายุสะสมแต้ม</span>
        </v-col>
        <v-col cols="12" md="3" sm="6">
          <v-radio-group class="mt-0" v-model="statusTimer">
            <v-radio label="ไม่กำหนด" value="permanent"></v-radio>
            <v-radio label="กำหนด" value="expire"></v-radio>
          </v-radio-group>
        </v-col>
        <v-col cols="6" md="2" sm="2" class="pl-1" v-if="statusTimer === 'expire'">
          <span>วันที่เริ่มต้น</span><br>
          <div>
            <v-dialog
              ref="dialogStartDate"
              v-model="dialogStartDate"
              :return-value.sync="date1"
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentStartDate"
                  v-bind="attrs"
                  placeholder="วว/ดด/ปป"
                  outlined
                  readonly
                  dense
                  hide-details
                  v-on="on"
                >
                  <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="date1"
                scrollable
                reactive
                locale="TH-th"
                :min="date2 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                @change=" setValueDate(date1, 'date1'), $refs.dialogStartDate.save(date1)"
              >
              </v-date-picker>
            </v-dialog>
          </div>
        </v-col>
        <v-col cols="6" md="2" sm="2" class="pl-1" v-if="statusTimer === 'expire'">
          <span>วันที่สิ้นสุด</span><br>
          <div>
            <v-dialog
              ref="dialogEndDate"
              v-model="dialogEndDate"
              :return-value.sync="date2"
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentEndDate"
                  v-bind="attrs"
                  placeholder="วว/ดด/ปป"
                  outlined
                  readonly
                  dense
                  hide-details
                  v-on="on"
                  :disabled="date1 === '' ? true : false"
                >
                  <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="date2"
                scrollable
                reactive
                locale="TH-th"
                :min="date1"
                @change=" setValueDate(date2, 'date2'), $refs.dialogEndDate.save(date2)"
              >
              </v-date-picker>
            </v-dialog>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col align="end">
          <v-btn class="mr-6" outlined rounded style="height: 40px; width: 90px; color: rgb(39, 171, 156);">
            ยกเลิก
          </v-btn>
          <v-btn class="white--text" @click="dataPrime.length === 0 ? createPoint() : editPoint()" rounded style="height: 40px; width: 90px; background-color: rgb(39, 171, 156); border-color: rgb(39, 171, 156);">
            บันทึก
          </v-btn>
        </v-col>
      </v-row>
      <v-dialog v-model="dialogSuccess" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="373">
        <v-card style="background: #FFFFFF; border-radius: 4px;" min-height="246px">
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C" v-if="dataPrime.length === 0">สร้างแต้มส่วนลดเสร็จสิ้น</font>
              <font color="#27AB9C" v-else>แก้ไขแต้มส่วนลดเสร็จสิ้น</font>
            </span>
            <v-btn icon dark @click="dialogSuccess = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-container>
            <v-card-text>
              <v-row justify="center" no-gutters dense>
                <v-col cols="12" align="center">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
                </v-col>
                <v-col cols="12" align="center" class="mt-6">
                  <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: #333333;" class="pt-0 mt-0">เสร็จสิ้น</p>
                </v-col>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- {{ date1 }}
      {{ date2 }} -->
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
export default {
  data () {
    return {
      baht1: '',
      point1: '',
      baht2: 0,
      point2: 0,
      dialogSuccess: false,
      dialogStartDate: false,
      dialogEndDate: false,
      date1: '',
      date2: '',
      sentStartDate: '',
      sentEndDate: '',
      statusTimer: 'no',
      shopId: '',
      usePointOrNot: 'no',
      seller_shop_id: '',
      dataPrime: []
    }
  },
  created () {
    // this.getdata()
    this.shopId = JSON.parse(localStorage.getItem('shopDetail')).id
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    // console.log(this.shopId, 'this.shopId')
    this.$EventBus.$emit('changeNav')
    // this.setValueDate(this.date1, 'date1').$refs.dialogEndDate.save(this.date1)
    this.getdata()
  },
  mounted () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  methods: {
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    setValueDate (time, proof) {
      if (!time) return null
      const [year, month, day] = time.split('-')
      const yearChange = parseInt(year) + 543
      if (proof === 'date1') {
        this.sentStartDate = `${day}/${month}/${yearChange}`
      } else if (proof === 'date2') {
        this.sentEndDate = `${day}/${month}/${yearChange}`
      }
    },
    async createPoint () {
      const data = {
        seller_shop_id: this.shopId,
        use_point: this.usePointOrNot,
        point_order_total: this.baht1,
        point_received: this.point1,
        x_point: parseInt(this.point2),
        x_baht: parseInt(this.baht2),
        point_expire_type: this.statusTimer,
        point_expire_start: this.date1,
        point_expire_end: this.date2
      }
      await this.$store.dispatch('actionsCreatePointSet', data)
      var res = await this.$store.state.ModuleManagePoint.stateCreatePointSet
      // console.log(res.result, '82052025202520')
      if (res.result === 'SUCCESS') {
        this.dialogSuccess = true
        setTimeout(() => {
          this.dialogSuccess = false
          window.location.reload()
        }, 1500)
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: res.message })
      }
    },
    async editPoint () {
      // console.log('1321232132132132132132')
      const data = {
        seller_shop_id: this.shopId,
        use_point: this.usePointOrNot,
        point_order_total: this.baht1,
        point_received: this.point1,
        x_point: parseInt(this.point2),
        x_baht: parseInt(this.baht2),
        point_expire_type: this.statusTimer,
        point_expire_start: this.date1,
        point_expire_end: this.date2
      }
      await this.$store.dispatch('actionsEditSellerShopPoint', data)
      var res = await this.$store.state.ModuleManagePoint.stateEditSellerShopPoint
      // console.log(res, '82052025202520')
      if (res.result === 'SUCCESS') {
        this.dialogSuccess = true
        setTimeout(() => {
          this.dialogSuccess = false
          window.location.reload()
        }, 1500)
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: res.message })
      }
    },
    async getdata () {
      const data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsgetSellerShopPointDetail', data)
      var res = await this.$store.state.ModuleManagePoint.stategetSellerShopPointDetail
      // console.log(res)
      if (res.result === 'SUCCESS') {
        this.dataPrime = res.data
        this.usePointOrNot = this.dataPrime.use_point
        this.baht1 = this.dataPrime.point_order_total
        this.point1 = this.dataPrime.point_received
        this.point2 = this.dataPrime.x_point
        this.baht2 = this.dataPrime.x_baht
        this.statusTimer = this.dataPrime.point_expire_type
        this.date1 = this.dataPrime.point_expire_start
        this.date2 = this.dataPrime.point_expire_end
        this.setValueDate(this.date1, 'date1')
        this.setValueDate(this.date2, 'date2')
      } else {
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
</style>
