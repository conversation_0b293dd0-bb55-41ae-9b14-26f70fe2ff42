<template>
  <v-container class="pa-4 pb-1">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row dense :class="MobileSize ? 'mx-2 my-2' : ''">
        <v-col :cols="MobileSize ? '12' : '6'" md="6" sm="6" xs="12" class="d-flex" align="start">
          <span :style="MobileSize ? 'font-size: 18px; ' : 'font-size: 24px; '" class="mr-auto" style="font-weight: 700; color: #333333;">
            <v-icon color="#27AB9C" class="mr-2" @click="backtoPOBuyer()">mdi-chevron-left</v-icon> รายละเอียดการสั่งซื้อสินค้า
          </span>
        </v-col>
        <v-col :cols="MobileSize ? '12' : '6'" md="6" sm="6" xs="12" :align="MobileSize? 'center': 'end'">
          <v-btn v-if="trackingStatus === 'Not Received' && typeShipping !== 'front' && items.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'" @click="acceptProduct()" rounded outlined color="#27AB9C"><b class="buttonFontSize">ได้รับสินค้าแล้ว</b></v-btn>
          <v-btn v-if="dataRole.role === 'purchaser' && (items.transaction_status === 'Success' || items.detail_credit_term.transaction_status_term === 'Success' )" class="ml-2" @click="addtoCart()" color="primary" rounded>
            <v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง
          </v-btn>
        </v-col>
        <!-- ข้อมูลรายละเอียด -->
        <v-col cols="12" class="pt-6">
          <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px; background: #F9FAFD;">
            <v-card-text>
              <v-row dense>
                <!-- ข้อมูลส่วนแรก -->
                <v-col cols="12" md="6" sm="12">
                  <v-row>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.payment_transaction }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสอ้างอิงการสั่งซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.detail_credit_term.payment_credit_term_number !== '-' ? items.detail_credit_term.payment_credit_term_number : items.payment_transaction }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.buyer_name }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ dateCreateOrderStep1 }}น.</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ชำระเงิน : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.receipt[0].updated_at !== '' ? new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) + 'น.' : '-' }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">ใบกำกับภาษี : </span>
                      <a v-if=" items.transaction_code !== '-' && items.required_invoice !== '-'" @click="GetETaxPDF(items)">
                        <span style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;"> {{items.payment_transaction}}</span>
                      </a>
                      <span v-else-if="items.transaction_code === '-' && items.required_invoice !== '-'">{{ items.required_invoice }}</span>
                      <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- ข้อมูลส่วนสอง -->
                <v-col cols="12" md="6" sm="12" style="display: flex;">
                  <!-- style="margin: auto;" -->
                  <v-row dense>
                    <v-col cols="12" class="d-flex">
                      <v-card elevation="0" class="ml-auto" :width="!IpadSize && !MobileSize ? '316' : '100%'" height="100%" style="border-radius: 8px; background: #FFFFFF;">
                        <v-card-text>
                          <v-row dense>
                            <v-col cols="12">
                              <v-row>
                                <!-- <v-col cols="12" v-if="productType[0].product_type !== 'general'">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;">สถานะคำสั่งซื้อ : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;"><v-chip text-color="#27AB9C" color="#DAF1E9">{{ items.order_status }}</v-chip></span>
                                </v-col> -->
                                <v-col cols="12">
                                  <span style="font-size: 16px; font-weight: 600; color: #333333;">สถานะคำสั่งซื้อ : </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" class="pl-2" v-if="items.pay_type !== 'recurring'">
                                    <span v-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.transaction_status !== 'Success'" style="font-size: 16px; font-weight: 400;" :style="{ 'color' : getTextColor(items.transaction_status) }"><v-icon :color="getTextColor(items.transaction_status)">mdi-circle-medium</v-icon>{{ getStatus(items.transaction_status) }}</span>
                                    <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ได้รับสินค้าแล้ว</span>
                                    <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
                                    <span v-else style="font-size: 16px; font-weight: 400; color: #636363;"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ items.detail_status }}
                                      <v-tooltip color="#33333373" bottom>
                                        <template v-slot:activator="{ on, attrs }">
                                            <v-icon
                                              color="#989898"
                                              v-bind="attrs"
                                              v-on="on"
                                            >mdi-information-outline</v-icon>
                                        </template>
                                        <span>ขอยกเลิกการสั่งซื้อสินค้า</span> <br>
                                        <span>ตามรายการสั่งซื้อ</span>
                                      </v-tooltip>
                                    </span>
                                  </span>
                                  <span style="font-size: 16px; font-weight: 400; color: #333333;" class="pl-2" v-else>
                                    <span v-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.detail_credit_term.transaction_status_term !== 'Success'" style="font-size: 16px; font-weight: 400;" :style="{ 'color' : getTextColor(items.detail_credit_term.transaction_status_term) }"><v-icon :color="getTextColor(items.detail_credit_term.transaction_status_term)">mdi-circle-medium</v-icon>{{ getStatus(items.detail_credit_term.transaction_status_term) }}</span>
                                    <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.detail_credit_term.transaction_status_term === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ได้รับสินค้าแล้ว</span>
                                    <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.detail_credit_term.transaction_status_term === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
                                    <span v-else style="font-size: 16px; font-weight: 400; color: #636363;"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ items.detail_status }}
                                      <v-tooltip color="#33333373" bottom>
                                        <template v-slot:activator="{ on, attrs }">
                                            <v-icon
                                              color="#989898"
                                              v-bind="attrs"
                                              v-on="on"
                                            >mdi-information-outline</v-icon>
                                        </template>
                                        <span>ขอยกเลิกการสั่งซื้อสินค้า</span> <br>
                                        <span>ตามรายการสั่งซื้อ</span>
                                      </v-tooltip>
                                    </span>
                                  </span>
                                </v-col>
                                <v-col cols="12" dense v-if="items.product_type === 'general'" class="pa-0">
                                  <v-col cols="12" v-if="items.type_shipping !== 'front'">
                                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วัน-เวลาส่งสินค้า : </span>
                                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ dateCreateOrderStep3 === 'Invalid Date' ? '-' : dateCreateOrderStep3 + 'น.' }}</span>
                                  </v-col>
                                  <v-col cols="12">
                                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วัน-เวลารับสินค้า : </span>
                                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ dateCreateOrderStep4 === 'Invalid Date' ? '-' : dateCreateOrderStep4 + 'น.'  }}</span>
                                  </v-col>
                                </v-col>
                                <v-col cols="12" dense class="pt-5 pa-0">
                                  <v-col cols="12">
                                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันกำหนดส่งชำระ : </span>
                                    <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.detail_credit_term.due_date}}</span>
                                  </v-col>
                                  <v-col cols="12">
                                    <span style="font-size: 16px; font-weight: 600; color: #333333;">จำนวนวันที่เกินกำหนดชำระ : </span>
                                    <span v-if="items.detail_credit_term.Overdue !== '-' && items.detail_credit_term.Overdue !== ''" style="font-size: 16px; font-weight: 400; color: #333333;">{{items.detail_credit_term.Overdue}} วัน</span>
                                    <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                                  </v-col>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                    <v-col cols="12" class="d-flex pt-4" >
                      <span v-if="items.detail_status === '-'" :style="MobileSize? 'font-size: 15px; font-weight: 500; line-height: 22px; color: #989898;': 'font-size: 16px; font-weight: 500; line-height: 22px; color: #989898;'" :class=" MobileSize? 'mr-auto':'ml-auto'"><v-icon color="#989898">mdi-information-outline</v-icon>ถ้าต้องการยกเลิกคำสั่งซื้อกรุณาติดต่อร้านค้า</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <!-- รายละเอียดเอกสาร -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/doc.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              รายละเอียดเอกสาร
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="6" sm="12">
              <v-row class="pt-6">
                <v-col cols="12" v-if="items.pay_type !== 'recurring'">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสนอราคา : </span>
                  <span @click="openPDF(items, 'QT')" style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;" v-if="items.payment_transaction !== '-' && items.transaction_status === 'Success' ">{{ items.payment_transaction }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else-if="items.payment_transaction !== '-' && items.transaction_status !== 'Success'">{{ items.payment_transaction }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
                <v-col cols="12" v-else>
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสนอราคา : </span>
                  <span @click="openPDF(items, 'QT')" style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;" v-if="items.payment_transaction !== '-' && items.detail_credit_term.transaction_status_term === 'Success' ">{{ items.payment_transaction }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else-if="items.payment_transaction !== '-' && items.detail_credit_term.transaction_status_term !== 'Success'">{{ items.payment_transaction }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบสั่งซื้อ (PO) : </span>
                  <span @click="openPDF(items, 'PO')" style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;" v-if="items.po_document_id !== '-'">{{ items.po_document_id }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสร็จ : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.receipt_number !== undefined? items.receipt_number:items.receipt[0].orderIDRef}}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="12">
              <v-row class="pt-6">
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบขอซื้อ (PR) : </span>
                  <span @click="openPDF(items, 'PR')" style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;" v-if="items.pr_document_id !== '-'">{{ items.pr_document_id }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ Sale Order : </span>
                  <span @click="openPDF(items, 'SO')" style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;" v-if="items.so_document_id !== '-'">{{ items.so_document_id }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- ที่อยู่ในการจัดส่งสินค้า -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/map.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3 pt-1">
              {{ items.type_shipping === 'front' ? 'ที่อยู่ในการรับสินค้า' : "ที่อยู่ในการจัดส่งสินค้า" }}
            </span>
            <v-chip v-if="items.type_shipping === 'front'" color="#ADDFFF" text-color="#0059FF" class="ml-2">รับสินค้าหน้าร้าน</v-chip>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12">
              <v-row class="pt-6">
                <v-col cols="12" style="font-size: 16px; font-weight: 400; color: #333333;">
                  <span v-if="items.product_type === 'general' && items.type_shipping !== 'front'">
                    {{ items.address_data }}<br/>
                    <span v-if="items.note_address !== '' && items.note_address !== undefined && items.note_address !== null" style="font-size: 16px; font-weight: 400;"><b>หมายเหตุ :</b> {{ items.note_address }}</span>
                  </span>
                  <span v-else-if="items.type_shipping === 'front'">{{ items.shipping_detail }}</span>
                  <span v-else>-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- ที่อยู่ในการจัดส่งใบกำกับภาษี -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/location.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              ที่อยู่ในการจัดส่งใบกำกับภาษี
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12">
              <v-row class="pt-6">
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-if="items.invoice_address !== '' ">{{ items.invoice_address }}</span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- รายการสั่งซื้อสินค้า -->
        <v-col cols="12" class="pt-6" >
          <!-- สถานะการสั่งซื้อ -->
          <v-col cols="12" class="px-0" v-if="trackingStatus !== 'Cancel' && trackingStatus !== 'refund' && (items.detail_credit_term.num_of_credit_term === '-' || items.detail_credit_term.num_of_credit_term === '1')">
            <v-row no-gutters class="pt-5 pb-5">
              <v-col cols="6">
                <v-row no-gutters>
                  <div class="d-flex">
                    <v-row no-gutters>
                      <v-img class="sizeIMG" src="@/assets/tracking Head.png"></v-img>
                    </v-row>
                    <span class="pl-3" style="font-size: 18px; font-weight: 700; color: #333333;">สถานะการสั่งซื้อ</span>
                  </div>
                </v-row>
              </v-col>
              <!-- <v-col cols="6" align="end" :class="MobileSize? '': 'pr-8'" v-if="trackingStatus === 'Not Received' && typeShipping !== 'front' && checkAcceptProduct[0].status === 'waiting_accept' && items.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'">
                <v-btn @click="acceptProduct()" rounded class="white--text px-5" color="#27AB9C"><b class="buttonFontSize">ได้รับสินค้าแล้ว</b></v-btn>
              </v-col> -->
            </v-row>
            <!-- รูปสถานะการสั่งซื้อ MobileSize || IpadSize || IpadProSize-->
            <div v-if="MobileSize || IpadSize || IpadProSize">
              <v-row no-gutters class="mx-4">
                <v-col cols="12" v-if="productNormal.length === 0 && productService.length !== 0 && typeShipping !== 'front'">
                  <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-2">
                    <v-col cols="12" align="center" class="pb-3">
                      <v-img src="@/assets/stepperNew/service/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                    </v-col>
                    <v-col cols="3" align="center" class="pr-2">
                      <span class="fontActive captionSku"><b>คำสั่งซื้อใหม่</b></span>
                    </v-col>
                    <v-col cols="3" align="center" class="pl-4">
                      <span class="fontInactive captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else class="mb-2">
                    <v-col cols="12" align="center" class="pb-3">
                      <v-img src="@/assets/stepperNew/service/step2.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku"><b>กรอกข้อมูลทั่วไป</b></span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" v-else-if="typeShipping === 'front'">
                  <v-row no-gutters v-if="trackingStatus === 'Cash'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepper-onshop.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">กรอกข้อมูลทั่วไป</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">รอเข้ารับสินค้า</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">รับสำเร็จ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
                    <v-col cols="12" align="center" class="px-3">
                      <v-img src="@/assets/stepperNew/receive/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">กรอกข้อมูลทั่วไป</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku pl-3">รอเข้ารับสินค้า</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku pl-2">รับสำเร็จ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Not Paid'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepperNew/receive/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive">คำสั่งซื้อใหม่</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">รอเข้ารับสินค้า</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" v-else>
                  <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
                    <v-col cols="12" align="center" class="pb-2 px-3">
                      <v-img src="@/assets/stepperNew/transport/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">คำสั่งซื้อใหม่</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku"><b>ชำระเงินแล้ว</b></span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">จัดส่งแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Not Sent'" class="mb-5">
                    <v-col cols="12" align="center" class="pb-2 px-3">
                      <v-img src="@/assets/stepperNew/transport/step2.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">จัดส่งแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Sent'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepperNew/transport/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">จัดส่งแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Not Received'" class="mb-5">
                    <v-col cols="12" align="center" class="pb-2 px-3">
                      <v-img src="@/assets/stepperNew/transport/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">จัดส่งแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Received'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepperNew/transport/step4.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">กรอกข้อมูลทั่วไป</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">จัดส่งแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">คำสั่งซื้อใหม่</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">คำสั่งซื้อทำการจัดส่งแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="captionSku">ผู้ซื้อตรวจสอบและขอคืนสินค้า</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">คำสั่งซื้อใหม่</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontPass captionSku">คำสั่งซื้อที่ชำระเงินแล้ว</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontActive captionSku">ที่ต้องจัดส่ง</span>
                    </v-col>
                    <v-col cols="3" align="center">
                      <span class="fontInactive captionSku">ที่ต้องได้รับ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Cancel by approver'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepper7.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="6" align="left" :style="{'padding-left': IpadSize ? '20px' : '10px'}">
                      <span class="captionSku">คำสั่งซื้อใหม่</span>
                    </v-col>
                    <v-col cols="6" align="right" :style="{'padding-right': IpadSize ? '20px' : '10px'}">
                      <span class="captionSku">ยกเลิกคำสั่งซื้อ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-else-if="trackingStatus === 'Cancel'" class="mb-5">
                    <v-col cols="12" align="center">
                      <v-img src="@/assets/stepper8.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="10"></v-img>
                    </v-col>
                    <v-col cols="6" align="left" :style="{'padding-left': IpadSize ? '20px' : '10px'}">
                      <span class="captionSku">คำสั่งซื้อใหม่</span>
                    </v-col>
                    <v-col cols="6" align="right" :style="{'padding-right': IpadSize ? '20px' : '10px'}">
                      <span class="captionSku">ยกเลิกคำสั่งซื้อ</span>
                    </v-col>
                  </v-row>
                  <v-row no-gutters v-if="trackingStatus === ''" class="mb-5">
                    <h1>ไม่พบข้อมูล</h1>
                  </v-row>
                </v-col>
              </v-row>
            </div>
            <!-- รูปสถานะการสั่งซื้อ Desktop-->
            <div v-else class="d-flex">
              <!-- สินค้าบริการอย่างเดียว -->
              <v-row no-gutters v-if="productNormal.length === 0 && productService.length !== 0 && typeShipping !== 'front'">
                <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/service/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <!-- <span class="fontInactive">ที่ต้องได้รับ</span> -->
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive">คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontInactive"><b>ชำระเงินแล้ว</b></span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <!-- <span class="fontInactive">ที่ต้องได้รับ</span> -->
                  </v-col>
                  <v-col cols="3" align="center">
                    <!-- <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span> -->
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <!-- <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(เหลือเวลาชำระเงินถึง {{new Date(items.last_payment_date_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/service/step2.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <!-- <span class="fontInactive">ที่ต้องได้รับ</span> -->
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontPass">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive"><b>ชำระเงินแล้ว</b></span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <!-- <span class="fontInactive">ที่ต้องได้รับ</span> -->
                  </v-col>
                  <v-col cols="3" align="center">
                    <!-- <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span> -->
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
              </v-row>
              <!-- สินค้าทั่วไปรับหน้าร้าน -->
              <v-row v-else-if="typeShipping === 'front'">
                <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/receive/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive pr-8">คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontInactive pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-6">
                    <span class="fontInactive">รอเข้ารับสินค้า</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-9">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                </v-row>
                <!-- <v-row no-gutters v-else-if="trackingStatus === 'Cash'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper-onshop.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>ชำระเงินสด</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>รอเข้ารับสินค้า</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>รับสำเร็จ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                </v-row> -->
                <!-- <v-row no-gutters v-else-if="trackingStatus === 'Not Sent'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/receive/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center" >
                    <span class="fontPass pr-8">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive pl-8">รอเข้ารับสินค้า</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive pl-9">รับสำเร็จ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-3">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row> -->
                <v-row no-gutters v-else-if="trackingStatus === 'Not Sent'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/receive/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center" >
                    <span class="fontPass pr-8">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-2">
                    <span class="fontActive">รอเข้ารับสินค้า</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontInactive">รับสำเร็จ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-3">(วันที่ {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Received'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/receive/step4.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center" >
                    <span class="fontPass pr-8">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-5">
                    <span class="fontPass pr-3">รอเข้ารับสินค้า</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontActive">รับสำเร็จ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-3">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pl-3">(วันที่ {{ dateCreateOrderStep3 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pl-6">(วันที่ {{ dateCreateOrderStep4 }})</span>
                  </v-col>
                </v-row>
              </v-row>
              <v-row no-gutters v-else>
                <v-row no-gutters v-if="trackingStatus === 'Not Paid'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/transport/step1.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive pr-8">คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive pl-1"><b>ชำระเงินแล้ว</b></span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive pl-6">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontInactive pl-6">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <!-- <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(เหลือเวลาชำระเงินถึง {{new Date(items.last_payment_date_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}})</span>
                  </v-col> -->
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Not Sent'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/transport/step2.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-8">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center" >
                    <!-- <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span> -->
                    <span class="fontActive pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-4">
                    <span class="fontInactive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-8">
                    <span class="fontInactive pl-2">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-3">(วันที่ {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
                <!-- <v-row no-gutters v-else-if="trackingStatus === 'Sent'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepper3.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass ">คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row> -->
                <v-row no-gutters v-else-if="trackingStatus === 'Not Received'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/transport/step3.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-8">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center" class="pl-1">
                    <span class="fontActive">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive pl-9">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-3">(วันที่ {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ {{ dateCreateOrderStep3 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Received'" class="mb-5">
                  <v-col cols="12" align="center" class="px-10 pb-5">
                    <v-img src="@/assets/stepperNew/transport/step4.svg" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-8">กรอกข้อมูลทั่วไป</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass pr-3">ชำระเงินแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontPass">จัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive pl-2">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-8">(วันที่ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pr-3">(วันที่ {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ {{ dateCreateOrderStep3 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder pl-2">(วันที่ {{ dateCreateOrderStep4 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'waiting_refund' || trackingStatus === 'refund'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อทำการจัดส่งแล้ว</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>ผู้ซื้อตรวจสอบและขอคืนสินค้า</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ทำการจัดส่ง {{ dateCreateOrderStep3 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่คืนสินค้า {{ dateCreateOrderStep4 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Success'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper5.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span>คำสั่งซื้อที่ชำระเงินแล้ว ({{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} บาท)</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontActive">ที่ต้องจัดส่ง</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontInactive">ที่ต้องได้รับ</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="3" align="center">
                    <span class="fontSizeStepOrder">(วันที่ชำระเงิน {{ dateCreateOrderStep2 }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-else-if="trackingStatus === 'Cancel by approver'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper7.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '50px' : '80px'}">
                    <span>คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="6" :style="{'padding-right': IpadProSize ? '40px' : '75px'}">
                    <span>ยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">(วันที่ยกเลิก {{ dateCreateOrderCancel }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-if="trackingStatus === 'Cancel'" class="mb-5">
                  <v-col cols="12" align="center">
                    <v-img src="@/assets/stepper8.png" max-height="1000px" max-width="1000px" height="100%" width="100%" contain aspect-ratio="9"></v-img>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '50px' : '80px'}">
                    <span>คำสั่งซื้อใหม่</span>
                  </v-col>
                  <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '40px' : '75px'}">
                    <span>ยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="6" align="left" :style="{'padding-left': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">(วันที่สั่งซื้อ {{ dateCreateOrderStep1 }})</span>
                  </v-col>
                  <v-col cols="6" align="right" :style="{'padding-right': IpadProSize ? '10px' : '50px'}">
                    <span class="fontSizeStepOrder">(วันที่ยกเลิก {{ dateCreateOrderCancel }})</span>
                  </v-col>
                </v-row>
                <v-row no-gutters v-if="trackingStatus === ''" class="mb-5">
                  <h1>ไม่พบข้อมูล</h1>
                </v-row>
              </v-row>
            </div>
          </v-col>
          <v-col cols="12" md="12" sm="12" class="px-0 pt-0" v-if="productNormal.length !== 0 && typeShipping !== 'front' && (items.detail_credit_term.num_of_credit_term === '-' || items.detail_credit_term.num_of_credit_term === '1')">
            <v-row no-gutters>
              <v-col :cols="MobileSize? '12': '6'" md="6" sm="6">
                <span style="font-size: 16px; font-weight: 600;" :class="MobileSize? '': 'pr-4'">รูปแบบการจัดส่ง : </span>
                <span :style="MobileSize? 'font-size: 14px; font-weight: 400;': 'font-size: 16px; font-weight: 400;'">{{ items.transportation_type }}</span>
              </v-col>
              <v-col :cols="MobileSize? '12': '6'" md="6" sm="6">
                <span style="font-size: 16px; font-weight: 600;" :class="MobileSize? '': 'pr-4'">Tracking Number: </span>
                <span :style="MobileSize? 'font-size: 12px; font-weight: 400;': 'font-size: 16px; font-weight: 400;'" :class="MobileSize ? 'pr-2': 'pr-4'" >{{items.order_mobilyst_no}}</span>
                <a v-if="items.order_mobilyst_no !== '-' && items.order_mobilyst_no !== '' && items.url_tracking !== '-' && items.url_tracking !== ''" target="_blank" :href="items.url_tracking" :style="MobileSize? 'font-size: 12px; font-weight: 400; border-bottom: solid 1px #1B5DD6; color:#1B5DD6;':'font-size: 16px; font-weight: 400; border-bottom: solid 1px #1B5DD6; color:#1B5DD6;'" >ติดตามสถานะจัดส่ง</a>
                <span v-if="items.order_mobilyst_no !== '-' && items.order_mobilyst_no !== '' && (items.url_tracking === '-' || items.url_tracking === '')" ref="trackNumber" >
                  <v-tooltip :color="changeCol? '#27AB9C73':'#33333373'" bottom>
                    <template v-slot:activator="{ on, attrs }">
                        <v-icon
                          class="copySTY"
                          :color="changeCol? '#27AB9C' :''"
                          v-bind="attrs"
                          v-on="on"
                          @click="copyText(items.order_mobilyst_no)"
                        >mdi-content-copy</v-icon>
                    </template>
                    <v-icon v-if="changeCol" color="#27AB9C">mdi-check</v-icon>
                    <span v-else>copy</span>
                  </v-tooltip>
                </span>
                <!-- <span v-else :style="MobileSize? 'font-size: 12px; font-weight: 400;':'font-size: 16px; font-weight: 400;'">{{items.url_tracking === '-' || items.url_tracking === ''? '':'ติดตามสถานะจัดส่ง'}}</span> -->
              </v-col>
            </v-row>
          </v-col>
          <v-row dense class="pt-3">
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/shopping.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              รายการสั่งซื้อสินค้า
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12">
              <!-- <v-row class="pt-6">
                <v-col cols="12" v-for="(order, index) in items.data_list" :key="index">
                  <v-data-table
                  :headers="header"
                  :items="order.product_list"
                  style="width: 100%"
                  class='row-height-64'
                  :items-per-page="100"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row dense>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''" max-height="48" max-width="48"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0" style="font-size: 14px; font-weight: 400; color: #333333;">{{ item.product_name }}</p>
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.revenue_amount`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row> -->
              <v-row :class="MobileSize? '': 'pt-3'">
                <!-- สินค้าทั่วไป -->
                <v-col cols="12" v-if="productNormal.length !== 0">
                  <span style="font-size: 16px; font-weight: 700;">สินค้าทั่วไป</span>
                  <v-data-table
                  :headers="MobileSize ? headerMobile : header"
                  :items="productNormal"
                  style="width: 100%"
                  :class="MobileSize ? '' : 'row-height-64'"
                  :items-per-page="100"
                  :hide-default-header="MobileSize ? true : false"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-col cols="12">
                        <v-row dense no-gutters >
                          <v-col aling="end" :cols="MobileSize? '5': '12'" md="4" class="pa-0">
                            <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''" :max-height=" MobileSize? '48':'48'" :max-width=" MobileSize? '48':'48'"/>
                            <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                          </v-col>
                          <v-col :cols="MobileSize? '7': '12'" md="8" :align="MobileSize? 'start':''" :class="MobileSize? 'pl-2':''">
                            <p v-if="MobileSize" class="mb-0" style="font-size: 10px; font-weight: 400; color: #333333;">{{ item.sku }}</p>
                            <p class="mb-0" :style="MobileSize? 'font-size: 10px; font-weight: 400; color: #333333;':'font-size: 14px; font-weight: 400; color: #333333;'">{{ item.product_name }}</p>
                              <div v-if="item.product_attribute_detail.key_1_value !== undefined && item.product_attribute_detail.key_1_value !== null && item.product_attribute_detail.key_1_value !== ''">
                                <span class="captionNameATBKeyOut">{{ item.product_attribute_detail.key_1_value }} </span>
                                <span class="mb-0 captionNameOut"> : {{item.product_attribute_detail.attribute_priority_1}}</span><br>
                                <div v-if="item.product_attribute_detail.key_2_value !== undefined && item.product_attribute_detail.key_2_value !== ''">
                                  <span class="captionNameATBKeyOut">{{ item.product_attribute_detail.key_2_value }} </span>
                                  <span class="mb-0 captionNameOut"> : {{item.product_attribute_detail.attribute_priority_2}}</span>
                                </div>
                                <v-col class="pa-0" v-if="MobileSize">
                                  <p>ราคาต่อชิ้น: {{ Number(item.vat_default === 'yes' ? parseFloat(item.real_price) + parseFloat(item.vat_include) : item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                                  <p>จำนวน: {{item.quantity}}</p>
                                  <p>ราคารวม: {{ Number(item.vat_default === 'yes'? ((parseFloat(item.real_price) + parseFloat(item.vat_include))* parseInt(item.quantity)):item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                                  <p>Amount: {{ Number(item.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                                </v-col>
                              </div>
                          </v-col>
                        </v-row>
                      </v-col>
                    </template>
                    <template v-slot:[`item.revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.real_price`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.vat_default === 'yes' ? parseFloat(item.real_price) + parseFloat(item.vat_include) : item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.vat_default === 'yes'? ((parseFloat(item.real_price) + parseFloat(item.vat_include))* item.quantity):item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.revenue_amount`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </v-data-table>
                </v-col>
                <!-- สินค้าบริการ-->
                <v-col cols="12" v-if="productService.length !== 0">
                  <span style="font-size: 16px; font-weight: 700;">สินค้า service</span>
                  <v-data-table
                  :headers="MobileSize ? headerMobile : header"
                  :items="productService"
                  style="width: 100%"
                  :class="MobileSize ? '' : 'row-height-64'"
                  :items-per-page="100"
                  :hide-default-header="MobileSize ? true : false"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row dense>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''" max-height="48" max-width="48"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0" style="font-size: 14px; font-weight: 400; color: #333333;">{{ item.product_name }}</p>
                          <!-- <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
                          <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span> -->
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.real_price`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.vat_default === 'yes' ? parseFloat(item.real_price) + parseFloat(item.vat_include) : item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.revenue_amount`]="{ item }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(item.revenue_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </v-data-table>
                </v-col>
                <v-col cols="12" class="pt-4" v-if="items.product_free.length !== 0 && items.product_free !== null">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">สินค้าแถม</span>
                  <v-data-table
                  :headers="MobileSize ? headerMobile : header"
                  :items="items.product_free"
                  style="width: 100%"
                  :class="MobileSize ? '' : 'row-height-64'"
                  :items-per-page="100"
                  :hide-default-header="MobileSize ? true : false"
                  hide-default-footer
                  >
                    <template v-slot:[`item.productdetails`]="{ item }">
                      <v-row dense>
                        <v-col cols="12" md="4" class="pr-0 py-1">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== '' && item.product_image !== null" max-height="48" max-width="48"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" max-height="48" max-width="48" v-else/>
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0" style="font-size: 14px; font-weight: 400; color: #333333;">{{ item.product_name }}</p>
                          <!-- <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
                          <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span> -->
                        </v-col>
                      </v-row>
                    </template>
                    <template v-slot:[`item.revenue_default`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_default`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.revenue_amount`]="{ }">
                      <span style="font-weight: 400; font-size: 14px; line-height: 26px; color: #000000;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template v-slot:[`item.productdetailsMobile`]="{ item }">
                      <v-row class="d-flex px-0">
                        <v-col class="ml-auto" style="max-width: 100px; margin: auto;">
                          <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== '' && item.product_image !== null" width="100" height="100" max-width="80" max-height="80"/>
                          <v-img src="@/assets/NoImage.png" class="imageshow" width="80" height="80" v-else/>
                        </v-col>
                        <v-col class="mlauto" align="start">
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">รหัส SKU :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.sku }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">{{ item.product_name }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">ราคาต่อชิ้น :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                          <span style="font-size: 12px; color: #989898; font-weight: 400;">จำนวน :</span><span class="pl-1" style="font-size: 12px; color: #333333; font-weight: 400;">{{ item.quantity }}</span><br/>
                          <span style="font-size: 14px; color: #333333; font-weight: 500;">ราคารวม :</span><span style="font-size: 14px; color: #333333; font-weight: 500;">{{ Number(0).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br/>
                        </v-col>
                      </v-row>
                    </template>
                  </v-data-table>
                </v-col>
                <v-col cols="12" class="pt-4 py-0" v-if="items.point !== null || (items.coupon.length !== 0 && items.coupon !== null)">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;" class="pl-3">โปรโมชันและคูปองส่วนลด</span>
                  <v-card class="pt-4" style="background: #FFFFFF; border-radius: 4px;" min-height="100%" elevation="0">
                    <v-toolbar align="start" color="#E6F5F3" dark dense elevation="0" v-if="items.point !== null">
                      <span class="" style="font-size:16px; font-weight:500; color: #333333;">
                        <font>ส่วนลดจากแต้ม {{items.point === null ? 0 : items.point}} บาท</font>
                      </span>
                    </v-toolbar>
                    <v-container class="px-0" v-if="items.coupon.length !== 0 && items.coupon !== null">
                      <v-card-text class="pa-0">
                        <div :class=" MobileSize? 'pa-1 pt-1':'pt-6'">
                          <v-col cols="12" md="4" sm="6" v-for="(item, index) in items.coupon" :key="index" class="pa-0">
                            <v-col :class="!MobileSize && !IpadSize ? 'couponIMGDesk': MobileSize? 'couponIMGMobile py-0': 'couponIMG'">
                              <v-col cols="12" md="12" class="" :class="MobileSize? 'pa-1': IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'">
                                <v-row no-gutters>
                                  <v-col cols="12" md="12" sm="12" :style="MobileSize ? 'padding-left: 6% !important;': 'padding-left: 10% !important;'">
                                    <v-row no-gutters>
                                      <v-col :cols="MobileSize? '5':'6'" md="6" sm="5" align="start">
                                        <span style="color: #27AB9C; font-size: 14px; font-weight: 600;">{{item.coupon_name | truncate(15, '...')}}</span><br>
                                        <span> ขั้นต่ำ {{item.spend_minimum}} บาท</span><br>
                                      </v-col>
                                      <v-col :cols="MobileSize? '6':'6'" md="6" sm="6" align="end">
                                        <span style="color: #FB9372; font-size: 12px;"> {{item.coupon_type === 'free_shipping' ? 'โค้ดส่งฟรี' : 'ส่วนลด'}}</span><br>
                                        <span v-if="item.coupon_type === 'free_product'" style="color: #F56E22; font-size: 22px; font-weight: 600;"> แถมฟรี</span><br>
                                        <span v-if="item.coupon_type !== 'free_product'" style="color: #F56E22; font-size: 22px; font-weight: 600;"> {{item.discount_amount}} {{item.discount_type === 'percent'? '%':'บาท'}}</span><br>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-col>
                          </v-col>
                        </div>
                      </v-card-text>
                    </v-container>
                  </v-card>
                </v-col>
              </v-row>
              <v-row :class="MobileSize? 'mt-5': 'pt-3'">
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ส่วนลด</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ภาษีมูลค่าเพิ่ม
                    <!-- <span style="font-size: 16px; font-weight: 400; color: #A1A1A1;">(7%)</span> -->
                  </span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ราคารวมภาษีมูลค่าเพิ่ม</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex pb-0">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 400; color: #333333;">ค่าจัดส่ง</span>
                  <span class="ml-auto" style="font-size: 16px; font-weight: 700; color: #333333;">{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" class="d-flex pt-0">
                  <span class="mr-auto" style="font-size: 10px; font-weight: 400; color: #A1A1A1;">
                      ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป<br/>
                      ขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ
                  </span>
                </v-col>
                <v-col cols="12" class="d-flex">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 700; color: #333333;">ราคารวมทั้งหมด</span>
                  <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #27AB9C;">{{ Number(items.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} <span style="font-size: 20px; font-weight: 700; color: #333333;">บาท</span></span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- รายละเอียดรายการสั่งซื้อ -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/inventory.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              รายละเอียดรายการสั่งซื้อ
            </span>
          </v-row>
          <v-row dense>
            <v-col cols="12" md="12" sm="12" class="pt-6">
              <v-row>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่เริ่มสัญญา : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.detail_credit_term.invoice_start_date !== '' && items.detail_credit_term.invoice_start_date !== null ? items.detail_credit_term.invoice_start_date : '-' }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่สิ้นสุดสัญญา : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.detail_credit_term.invoice_end_date !== '' && items.detail_credit_term.invoice_end_date !== null ? items.detail_credit_term.invoice_end_date : '-' }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">Pay Type : </span>
                  <v-chip v-if="items.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                  <v-chip v-else-if="items.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                  <v-chip v-else-if="items.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
                  <span v-else>-</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <v-row>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">งวดที่ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.detail_credit_term.num_of_credit_term}}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <!-- <span style="font-size: 16px; font-weight: 600; color: #333333;">หมวดงบประมาณ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.type_budget !== '' || items.type_budget !== null? items.type_budget: '-' }}</span> -->
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">จำนวนเงิน : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.detail_credit_term.total_amount !== '-' && items.detail_credit_term.total_amount !== '' ?  Number(items.detail_credit_term.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' : '-'}}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pt-3">
              <v-row>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">ส่วนลด : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.type_b2b_discount === 'baht' ? Number(items.total_b2b_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' : items.type_b2b_discount === 'percent' ? items.percent_b2b_discount + ' %' : '-' }}</span>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <!-- <span style="font-size: 16px; font-weight: 600; color: #333333;">หมวดงบประมาณ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.type_budget !== '' || items.type_budget !== null? items.type_budget: '-' }}</span> -->
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">ยอดส่วนลด : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.total_b2b_discount !== '-' ? Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) + ' บาท' : '-' }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pt-3">
              <v-row>
                <v-col cols="12" md="4" sm="6">
                  <!-- <span style="font-size: 16px; font-weight: 600; color: #333333;">หมวดงบประมาณ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.type_budget !== '' || items.type_budget !== null? items.type_budget: '-' }}</span> -->
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">หมวดงบประมาณ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <!-- <span style="font-size: 16px; font-weight: 600; color: #333333;">หมวดตัดงบ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.budget_cut !== '' || items.budget_cut !== null? items.budget_cut: '-' }}</span> -->
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">หมวดตัดงบ : </span><span style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pt-3">
              <v-row>
                <v-col cols="12" md="6" sm="6">
                  <span v-if="items.remark !== ''" style="font-size: 16px; font-weight: 600; color: #333333;">หมายเหตุ : </span><span v-if="items.remark !== ''" style="font-size: 16px; font-weight: 400; color: #333333;">{{ items.remark }}</span>
                  <span v-else style="font-size: 16px; font-weight: 600; color: #333333;">หมายเหตุ : -</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="pt-6">
          <v-divider></v-divider>
        </v-col>
        <!-- การชำระเงิน -->
        <v-col cols="12" class="pt-6">
          <v-row dense>
            <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/receipt.png')" max-height="24" max-width="24"></v-img>
            <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
              การชำระเงิน
            </span>
            <div class="pl-3 pt-1" v-if="items.pay_type !== 'recurring'">
              <span v-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.transaction_status !== 'Success'" style="font-size: 16px; font-weight: 400;" :style="{ 'color' : getTextColor(items.transaction_status) }"><v-icon :color="getTextColor(items.transaction_status)">mdi-circle-medium</v-icon>{{ getStatus(items.transaction_status) }}</span>
              <!-- <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ได้รับสินค้าแล้ว</span> -->
              <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
              <span v-else style="font-size: 16px; font-weight: 400; color: #636363;"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ items.detail_status }}</span>
              <!-- <v-chip v-if="items.status === 'Credit'" color="#E5EFFF" small text-color="#1B5DD6">ชำระเงินแบบเครดิตเทอม</v-chip>
              <v-chip v-else-if="items.transaction_status === 'Success'" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสำเร็จ</v-chip>
              <v-chip v-else-if="items.transaction_status === 'Cancel'" color="#F7D9D9" small text-color="#D1392B">ยกเลิกคำสั่งซื้อ</v-chip>
              <v-chip v-else-if="items.transaction_status === 'Fail'" color="#FFECB3" small text-color="#FFB300">ชำระเงินไม่สำเร็จ</v-chip>
              <v-chip v-else color="#E5EFFF" small text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip> -->
            </div>
            <div class="pl-3 pt-1" v-else>
              <span v-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.detail_credit_term.transaction_status_term !== 'Success'" style="font-size: 16px; font-weight: 400;" :style="{ 'color' : getTextColor(items.detail_credit_term.transaction_status_term) }"><v-icon :color="getTextColor(items.detail_credit_term.transaction_status_term)">mdi-circle-medium</v-icon>{{ getStatus(items.detail_credit_term.transaction_status_term) }}</span>
              <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking === 'Received' && items.detail_credit_term.transaction_status_term === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ได้รับสินค้าแล้ว</span>
              <span v-else-if="items.detail_status !== 'ยกเลิกคำสั่งซื้อ' && items.tracking[0].status_tracking !== 'Received' && items.detail_credit_term.transaction_status_term === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>ชำระเงินแล้ว</span>
              <span v-else style="font-size: 16px; font-weight: 400; color: #636363;"><v-icon color="#636363">mdi-circle-medium</v-icon>{{ items.detail_status }}</span>
              <!-- <v-chip v-if="items.status === 'Credit'" color="#E5EFFF" small text-color="#1B5DD6">ชำระเงินแบบเครดิตเทอม</v-chip>
              <v-chip v-else-if="items.transaction_status === 'Success'" color="#E6F5F3" small text-color="#27AB9C">ชำระเงินสำเร็จ</v-chip>
              <v-chip v-else-if="items.transaction_status === 'Cancel'" color="#F7D9D9" small text-color="#D1392B">ยกเลิกคำสั่งซื้อ</v-chip>
              <v-chip v-else-if="items.transaction_status === 'Fail'" color="#FFECB3" small text-color="#FFB300">ชำระเงินไม่สำเร็จ</v-chip>
              <v-chip v-else color="#E5EFFF" small text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip> -->
            </div>
            <!-- กรณี B2B JV สามารถชำระเงินได้แค่สินค้าทั่วไป -->
            <!-- <v-row no-gutters justify="end" class="pt-2" v-if="(shopJV === 'yes' && productType[0].product_type === 'general' && ((items.transaction_status === 'Not Paid' || items.transaction_status === 'Cancel' || items.transaction_status === 'Fail')  && items.detail_status === '-'))">
              <v-btn small class="white--text" color="#27AB9C" rounded @click="dialogChooesPayType = true">
                <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                <span class="pt-1">ชำระเงิน</span>
              </v-btn>
            </v-row> -->
            <!-- กรณี B2B ธรรมดา สามารถชำระเงินได้ทั้งหมด -->
            <!-- <v-row no-gutters justify="end" class="pt-2" v-else-if="shopJV !== 'yes' && ((items.transaction_status === 'Not Paid' || items.transaction_status === 'Cancel' || items.transaction_status === 'Fail') && items.detail_status === '-')">
              <v-btn small class="white--text" color="#27AB9C" rounded @click="dialogChooesPayType = true">
                <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                <span class="pt-1">ชำระเงิน</span>
              </v-btn>
            </v-row> -->
            <!-- กรณีผู้ซื้อองค์กรต้องรอร้านค้าอนุมัติรายการสั่งซื้อก่อนชำระเงินเท่านั้น -->
            <v-row no-gutters justify="end" class="pt-2" v-if="(items.sale_order === 'yes' || items.status_approve === 'Approve') && (items.transaction_status === 'Not Paid' || items.transaction_status === 'Cancel' || items.transaction_status === 'Fail') && this.items.pay_type !== 'recurring'">
              <v-btn :disabled="items.detail_credit_term.payment_credit_term_number === '-'" small class="white--text" color="#27AB9C" rounded @click="CheckStockBeforeOpenModalPayment(items.payment_transaction)">
                <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                <span class="pt-1">ชำระเงิน</span>
              </v-btn>
            </v-row>
            <v-row no-gutters justify="end" class="pt-2" v-if="(items.sale_order === 'yes' || items.status_approve === 'Approve') && items.detail_credit_term.transaction_status_term !== 'Success' && this.items.pay_type === 'recurring'">
              <v-btn :disabled="items.detail_credit_term.payment_credit_term_number === '-'" small class="white--text" color="#27AB9C" rounded @click="CheckStockBeforeOpenModalPayment(items.payment_transaction)">
                <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                <span class="pt-1">ชำระเงิน</span>
              </v-btn>
            </v-row>
            <v-row no-gutters v-else-if="((items.transaction_status === 'Not Paid' && items.detail_credit_term.transaction_status_term !== 'Success') || items.transaction_status === 'Cancel' || items.transaction_status === 'Fail') && items.status_approve !== 'Approve'" justify="end">
              <v-chip small :text-color="items.status_approve === 'Reject'? '#D1392B':'#E9A016'" :color="items.status_approve === 'Reject'? '#F7D9D9':'#FCF0DA'" rounded>
                <span v-if="items.status_approve === 'Reject'"  class="pt-1">ไม่อนุมัติคำสั่งซื้อ/ยกเลิกคำสั่งซื้อ</span>
                <span v-else class="pt-1">รออนุมัติคำสั่งซื้อ</span>
              </v-chip>
            </v-row>
          </v-row>
          <v-row dense v-if=" items.pay_type === 'general'">
            <v-col v-if="items.sale_order === 'no' && items.status_approve !== 'Approve'">
              <v-row no-gutters v-if="items.transaction_status !== 'Success' && items.status_approve === 'Reject'">
                <!-- <span class="mr-2">ร้านไม่อนุมัติคำสั่งซื้อ หรือ ยกเลิกคำสั่งซื้อ</span> -->
              </v-row>
              <v-row no-gutters v-else>
                <span class="mr-2">กรุณารอร้านค้าอนุมัติคำสั่งซื้อของท่าน</span>
              </v-row>
            </v-col>
            <v-col v-else>
              <!-- transaction_status credit -->
              <v-row v-if="items.transaction_status === 'Success'" no-gutters>
                <!-- <v-col cols="12" class="mt-5">
                  <span style="color: #27AB9C;"><b>&#8226; หลักฐานการชำระเงิน</b></span>
                </v-col> -->
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Cancel' && dataRole.role === 'purchaser'" no-gutters>
                <v-col cols="12" class="mt-4">
                  <span class="mr-2">คุณได้ทำการยกเลิกคำสั่งซื้อ หากต้องการซื้อสินค้าอีกครั้ง สามารถเข้าไปเลือกซื้อสินค้ากับเราได้เลย</span>
                </v-col>
              </v-row>
              <v-row v-else-if="items.transaction_status === 'Fail' && statusPayment" no-gutters>
                <v-col cols="12" class="mt-4">
                  <span class="mr-2">คุณชำระเงินไม่สำเร็จ กรุณาตรวจสอบการชำระเงินของคุณอีกครั้ง</span>
                  <!-- <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn> -->
                </v-col>
              </v-row>
              <v-row v-else no-gutters>
                <v-col v-if="items.status !== 'Credit' && statusPayment" cols="12" class="mt-4">
                  <span class="mr-2">คุณยังไม่ได้ทำการชำระเงิน กรุณาชำระเงินผ่านบริการทุกช่องทางของ <b>Thaidotcom Payment</b> โดยสามารถชำระเงินได้ที่นี่</span>
                  <!-- <v-btn class="white--text" color="#27AB9C" small @click="GoToPayment()">ชำระเงิน</v-btn> -->
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense>
            <!-- <v-col cols="12" class="mt-4">
              <span style="font-size: 20px;"><b>หลักฐานการชำระเงิน</b></span>
            </v-col> -->
            <v-row v-if="(items.transaction_status === 'Success' && this.items.pay_type !== 'recurring') || (items.detail_credit_term.transaction_status_term === 'Success' && this.items.pay_type === 'recurring')" no-gutters>
              <v-col cols="12" class="mt-5">
                <v-row>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">รหัสการชำระเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ items.detail_credit_term.payment_credit_term_number !== '-' ? items.detail_credit_term.payment_credit_term_number : items.receipt[0].orderId }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">จำนวนเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ items.detail_credit_term.total_amount !== '-' ? items.detail_credit_term.total_amount : Number(items.receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">วันและเวลาที่ชำระเงิน
                      <!-- <span style="font-size: 16px; font-weight: 400; color: #A1A1A1;">(7%)</span> -->
                    </span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{new Date(items.receipt[0].updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">Ref</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">{{ items.receipt[0].orderIDRef }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-weight: 400; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'">รูปแบบการชำระเงิน</span>
                    <span class="ml-auto" style="font-weight: 700; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'" v-if="items.installment_month === '0'">{{ items.receipt[0].payType }}</span>
                    <span class="ml-auto" style="font-weight: 700; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'" v-else>{{ items.receipt[0].payType }} ({{items.installment_month}}x @0%)</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-weight: 400; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'">ธนาคารที่ชำระเงิน</span>
                    <span class="ml-auto" style="font-weight: 700; color: #333333;" :style="MobileSize?'font-size: 14px;':'font-size: 18px;'">{{ bankName }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex">
                    <span class="mr-auto" style="font-size: 18px; font-weight: 400; color: #333333;">ผลการชำระเงิน</span>
                    <span class="ml-auto" style="font-size: 18px; font-weight: 700; color: #333333;">
                      {{ items.status === 'Credit' ? 'ชำระเงินแบบเครดิตเทอม' : items.transaction_status === 'Success' ? 'ชำระเงินสำเร็จ' : items.detail_credit_term.transaction_status_term === 'Success' ? 'ชำระเงินสำเร็จ' : items.transaction_status === 'Cancel' ? 'ยกเลิกคำสั่งซื้อ' : items.transaction_status === 'Fail' ? 'ชำระเงินไม่สำเร็จ' : 'ยังไม่ชำระเงิน' }}
                    </span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- <v-row v-else>
              <v-col cols="12" class="mt-5 ml-1">
                <span>-</span>
              </v-col>
            </v-row> -->
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="DialogQR" persistent :width="MobileSize ? '100%' : '640'">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
              style="position: absolute; height: 120px; ">
              <v-row style="height: 120px; ">
                <v-col style="text-align: center;" class="pt-4">
                  <span style="margin-left: 47px"
                    :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>สแกน QR Code
                      ชำระเงิน</b></span>
                </v-col>
                <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                    color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '640px'"
                style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
                style="background: #FFFFFF; border-radius: 20px;">
                <div style="text-align: center;">
                  <!-- <v-img height="280" width="280" style="margin-inline: auto;" :src="Image"></v-img>
                  <v-btn @click="saveQRCode()" color="#27AB9C" rounded width="125" height="40" class="white--text my-8">บันทึกรูปภาพ</v-btn> -->
                  <v-col class="py-0">
                    <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="Image"/>
                  </v-col>
                  <v-col class="py-0">
                    <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                  </v-col>
                  <div>
                    <v-col>
                      <span style="font-size: 20px; font-weight: 700;">ยอดชำระเงินจำนวน : {{ Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
                        บาท</span>
                    </v-col>
                    <v-col>
                      <span style="font-size: 14px; font-weight: 400;">รหัสอ้างอิง {{Ref1}}</span>
                    </v-col>
                    <v-col class="py-0">
                      <span style="font-size: 14px; font-weight: 600; color: #A1A1A1;">สามารถชำระเงินได้ตามขั้นตอนนี้
                        (กรณีชำระเงินผ่านมือถือ)</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">2. เปิดแอปธนาคารของท่าน
                        และเลือกเมนูสแกน QR Code</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                        Code</span>
                    </v-col>
                  </div>
                </div>
              </v-card>
            </div>
          </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="dialogChooesPayType" persistent width="550">
      <v-card :height=" MobileSize || IpadSize ? '':''" :width="MobileSize || IpadSize ? '':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="55px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="closeDialogPayment()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-card-title style="place-content: center;" class="px-0">
          <span style="font-size: 20px;font-weight: 700; color: #FAAD14;">วิธีการชำระเงิน</span>
        </v-card-title>
        <v-card-text style="place-content: center;">
          <v-row no-gutters justify="center">
            <v-radio-group v-model="radioPayment" row class="ma-0 pa-0">
              <!-- <v-col cols="12">
                <v-row no-gutters justify="start"> -->
                  <v-radio v-if="items.payment_method[0] === 'qrcode' || items.payment_method[1] === 'qrcode' || items.payment_method[2] === 'qrcode'" value="radio-qr" @click="setRadioCreditTermNo()"><template v-slot:label >
                      <span style="font-size: 16px;">QR Code</span>
                    </template>
                  </v-radio>
                  <v-radio v-if="items.payment_method[0] === 'creditcard' || items.payment_method[1] === 'creditcard' || items.payment_method[2] === 'creditcard'" value="radio-credit" @click="setRadioCreditTermNo()"><template v-slot:label>
                      <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                    </template>
                  </v-radio>
                <!-- </v-row>
              </v-col> -->
              <!-- <v-col cols="12">
                <v-row no-gutters justify="start"> -->
                  <v-radio v-if="items.payment_method[0] === 'installment' || items.payment_method[1] === 'installment' || items.payment_method[2] === 'installment'" value="radio-installment"><template v-slot:label>
                      <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card แบบผ่อนชำระ</span>
                    </template>
                  </v-radio>
                  <v-radio :disabled="disabledEWHT" v-if="items.bank_code !== null && items.bank_code !== ''" value="radio-e-WHT"><template v-slot:label>
                      <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Transfer</span>
                    </template>
                  </v-radio>
                <!-- </v-row>
              </v-col> -->
            </v-radio-group>
          </v-row>
          <div v-if="radioPayment === 'radio-installment'" class="mt-0 mb-4 mx-3">
            <v-row align="center">
              <v-col :cols="MobileSize ? 6 : 4" class="pt-0 pl-8">
                <span style="font-size: 16px;">ระยะเวลาผ่อนชำระ</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 8" class="pb-0">
                <v-select outlined dense label="เลือกระยะเวลาผ่อนชำระ" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" style="border-radius: 8px;">
                  <template v-slot:append>
                    <v-icon>mdi-chevron-down</v-icon>
                  </template>
                  <template v-slot:no-data>
                    <v-list-item>
                      <v-list-item-content class="text-center">
                        <v-list-item-title>ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง <span style="color: #27AB9C;">'ขั้นต่ำ'</span> ที่กำหนดไว้</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </div>
        </v-card-text>
        <v-card-text >
          <v-row dense justify="center">
            <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4"
              @click="closeDialogPayment()">ยกเลิก</v-btn>
            <v-btn :disabled="radioPayment === 'no' || radioPayment === 'radio-installment' && radioCreditTerm === 'No'" :width="MobileSize? '100':'156'" height="38" class="white--text " rounded color="#27AB9C"
              @click="confirmPayment()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirm" width="424" persistent>
      <v-card :height=" MobileSize || IpadSize ? '400':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn @click="dialogConfirm = false" color="#CCCCCC" icon>
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b></b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่
              หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogConfirm = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize? '100':'156'" height="38" class="white--text" rounded color="#27AB9C" @click="radioPayment === 'radio-qr' ? GetQRCode('cashPayment') : (radioPayment === 'radio-credit' || radioPayment === 'radio-installment') ? GetCC('cashPayment') : GeteWHT('e-WHT')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      disabledEWHT: true,
      taxId: '',
      username: '',
      TypeOS: '',
      CloseDialog: false,
      checkAccept: '',
      radioCreditTerm: 'No',
      itemsCreditTerm: [
        { value: '3', label: '3 เดือน' },
        { value: '6', label: '6 เดือน' },
        { value: '10', label: '10 เดือน' }
      ],
      changeCol: false,
      CheckPayment: '',
      typeShipping: '',
      radioPayment: 'no',
      Ref1: '',
      ImageQR: '',
      Image: '',
      netPrice: '',
      DialogQR: false,
      dialogChooesPayType: false,
      dialogConfirm: false,
      shopJV: '',
      productType: [],
      productNormal: [],
      productService: [],
      items: [],
      paymentNumber: {},
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingText: '',
      flashTrackingNo: '',
      flashTrackingData: {},
      receivedDate: '',
      sentDate: '',
      sentTime: '',
      step: 0,
      flashMCHID: process.env.VUE_APP_FLASH,
      checkAcceptProduct: [],
      mockupTracking: {},
      flashRoutes: [],
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: '',
      dateCreateOrderCancel: '',
      companyId: null,
      statusPayment: false,
      mobilystTrackingNo: '',
      itemStatus: '',
      headerMobile: [
        { text: 'รายละเอียดสินค้า', value: 'productdetails', sortable: false, width: '100%', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      header: [
        { text: 'รหัส SKU', value: 'sku', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียดสินค้า', value: 'productdetails', sortable: false, align: 'start', width: '25%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคาต่อชิ้น', value: 'revenue_default', align: 'start', filterable: false, sortable: false, width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวน', value: 'quantity', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคารวม', value: 'total_revenue_default', filterable: false, sortable: false, width: '15%', align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'Amount', value: 'revenue_amount', filterable: false, sortable: false, align: 'start', width: '15%', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      imageBase64: ''
    }
  },
  async created () {
    this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    this.$EventBus.$on('SentGetReview', this.getItemProduct)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    this.TypeOS = this.detectOS()
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.paymentNumber = {
        payment_transaction_number: this.$route.query.orderNumber,
        role_user: 'purchaser',
        company_id: companyId.id,
        id_of_credit_term: this.$route.query.termId
      }
      this.getFranchise()
      this.getItemProduct()
      // this.GetTaxID()
      // this.userDetailMpV2()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('SentGetReview')
  },
  computed: {
    filteredCreditTerms () {
      return this.itemsCreditTerm.map(term => {
        // console.log('this.items', this.items)
        const correspondingInstallment = this.items.installment_method.find(installment => installment.month === term.value)
        // เพิ่ม property displayText เพื่อแสดงราคาในตัวเลือก
        return {
          ...term,
          displayText: correspondingInstallment ? `${term.label} - ${Number(correspondingInstallment.price).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} บาท/เดือน` : term.label
        }
      }).filter(term => {
        // กรองตามเงื่อนไขที่ต้องการ
        const correspondingInstallment = this.items.installment_method.find(installment => installment.month === term.value)
        return correspondingInstallment && correspondingInstallment.price >= 500
      })
    },
    headers () {
      const headers = [
        {
          title: 'รหัสสินค้า',
          dataIndex: 'sku',
          scopedSlots: { customRender: 'sku' },
          key: 'sku',
          align: 'start',
          width: '15%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          align: 'start',
          scopedSlots: { customRender: 'productdetails' },
          width: '25%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'real_price',
          scopedSlots: { customRender: 'real_price' },
          key: 'real_price',
          align: 'start',
          width: '15%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'start',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'total_revenue_default',
          scopedSlots: { customRender: 'total_revenue_default' },
          key: 'total_revenue_default',
          align: 'start',
          width: '15%'
        },
        {
          title: 'Amount',
          dataIndex: 'revenue_amount ',
          scopedSlots: { customRender: 'revenue_amount ' },
          key: 'revenue_amount ',
          align: 'start',
          width: '15%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headersMobile
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      // console.log('valdetail', val)
      if (val === true) {
        this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${this.paymentNumber.payment_transaction_number}&termId=${this.paymentNumber.id_of_credit_term}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/orderDetailCompany?orderNumber=${this.paymentNumber.payment_transaction_number}&termId=${this.paymentNumber.id_of_credit_term}` }).catch(() => {})
      }
    }
  },
  methods: {
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    async addtoCart () {
      this.$store.commit('openLoader')
      var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      var companyId = companyDataID.company.company_id
      var ComPermId = companyDataID.position.com_perm_id
      var data = {
        order_number: this.paymentNumber.payment_transaction_number,
        role_user: 'purchaser',
        company_id: companyId,
        com_perm_id: ComPermId
      }
      await this.$store.dispatch('actionsRepeatOrder', data)
      const response = await this.$store.state.ModuleCart.stateRepeatOrder
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/shoppingcart' }).catch(() => {})
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'ระบบขัดข้องไม่สามารถสั่งซื้อสินค้าอีกครั้งได้'
          })
        }
      }
    },
    async CheckEWHT () {
      await this.GetTaxID()
      await this.userDetailMpV2()
      if (this.taxId !== '' && this.username !== '') {
        var data = {
          tax_id: this.taxId,
          username: this.username
        }
        await this.$store.dispatch('actionPaymenyCheckewht', data)
        const response = await this.$store.state.ModuleOrder.statePaymenyCheckewht
        if (response.code === 200 && response.data.length !== 0) {
          this.disabledEWHT = false
        } else {
          this.disabledEWHT = true
        }
      } else {
        this.disabledEWHT = true
      }
    },
    async GetTaxID () {
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      var data = {
        company_id: companyData.id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      if (response.result === 'SUCCESS') {
        this.taxId = response.data.tax_id
        console.log('taxid', this.taxId)
      }
    },
    async userDetailMpV2 () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        this.username = response.data.username
        console.log('username', this.username)
      }
    },
    async CheckStockBeforeOpenModalPayment (orderNumber) {
      this.$store.commit('openLoader')
      var messageCheckError = ''
      var i
      var data = {
        payment_transaction_number: orderNumber
      }
      await this.$store.dispatch('actionsCheckStockBeforePayment', data)
      const response = await this.$store.state.ModuleCart.stateCheckStockBeforePayment
      if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
        await this.CheckEWHT()
        this.$store.commit('closeLoader')
        this.dialogChooesPayType = true
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
        for (i = 0; i < response.data.product_free.length; i++) {
          messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
        }
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          for (i = 0; i < response.data.product_list.length; i++) {
            messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
          }
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
          })
        }
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    setRadioCreditTermNo () {
      this.radioCreditTerm = 'No'
    },
    copyText (text) {
      navigator.clipboard.writeText(text)
      this.changeCol = true
      setTimeout(() => {
        this.changeCol = false
      }, 1000)
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        icon: 'success',
        html: '<span style="padding-left: 10px;">คัดลอกสำเร็จ</span>'
      })
    },
    getStatus (item) {
      if (item === 'Pending') return 'รออนุมัติ'
      else if (item === 'Not Paid') return 'รอชำระเงิน'
      else if (item === 'Success') return 'ชำระเงินสำเร็จ'
      else if (item === 'Approve') return 'วางบิล'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Credit Term') return 'ชำระเงินแบบเครดิตเทอม'
      else if (item === 'Cash') return 'ชำระเงินสด'
      else return 'ยกเลิกคำสั่งซื้อ'
    },
    getTextColor (item) {
      if (item === 'Pending') return '#E9A016'
      else if (item === 'Not Paid') return '#E9A016'
      else if (item === 'Success') return '#1AB759'
      else if (item === 'Approve') return '#1AB759'
      else if (item === 'Fail') return '#D1392B'
      else if (item === 'Credit Term') return '#1B5DD6'
      else if (item === 'Cash') return '#1AB759'
      else return '#D1392B'
    },
    getTextColorTransportation (item) {
      if (item === 'อยู่ระหว่างดำเนินการ') return '#E9A016'
      else if (item === 'อยู่ระหว่างการขนส่ง') return '#1B5DD6'
      else if (item === 'ส่งคืนสินค้า') return '#D1392B'
      else return '#E9A016'
    },
    async GetETaxPDF (val) {
      var data = {
        transactionCode: val.transaction_code
      }
      const timeoutId = setTimeout(async () => {
        await this.$store.dispatch('ActionsGetETaxPDF', data)
        var response = await this.$store.state.ModuleCart.stateGetETaxPDF
        // console.log('response', response)
        if (response.result === 'OK') {
          if (response.etaxResponse.status === 'OK') {
            if (response.etaxResponse.urlPdf !== undefined) {
              window.open(`${response.etaxResponse.urlPdf}`, '_blank')
            } else {
              window.open(`${response.etaxResponse.pdfURL}`, '_blank')
            }
            // console.log('response', response.etaxResponse.urlPdf)
          }
        } else {
          if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$swal.fire({
              toast: true,
              showConfirmButton: false,
              timer: 1000,
              timerProgressBar: true,
              icon: 'error',
              title: 'ไม่พบเอกสารใบกำกับภาษี'
            })
          }
        }
      }, 1000)
      // console.log('4', timeoutId)
      if (timeoutId > 1000) {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่พบเอกสารใบกำกับภาษี'
        })
      }
    },
    gotoCrediterm (val) {
      if (this.MobileSize) {
        this.$router.push({ path: `/companyListCreditTermMobile?order_number=${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/companyListCreditTerm?order_number=${val}` }).catch(() => {})
      }
    },
    backtoPOBuyer () {
      if (this.MobileSize) {
        this.$router.push({ path: '/orderCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      }
    },
    trackingMobilyst (Track) {
      window.open(Track)
    },
    async getFranchise () {
      var Franchise = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
      if (Franchise.can_use_function_in_company.payment !== undefined) {
        this.statusPayment = true
      } else {
        this.statusPayment = false
      }
    },
    async getItemProduct () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetDetailPurchaserV2', this.paymentNumber)
      var res = await this.$store.state.ModuleAdminManage.stateGetDetailPurchaserV2
      if (res.message === 'Get detail order purchaser success') {
        this.$store.commit('closeLoader')
        this.items = res.data
        if (this.items.coupon === null) {
          this.items.coupon = []
        }
        if (this.items.product_free === null) {
          this.items.product_free = []
        }
        // this.CheckPayment = res.data.payment_method
        // console.log('ress', res.data.data_list[0s].product_list)
        this.productType = res.data.data_list[0].product_list
        this.shopJV = res.data.data_list[0].is_JV
        if (this.productType.length !== 0 && this.checkAccept !== 'ยอมรับสินค้าแล้ว') {
          this.productType.forEach(e => {
            // console.log('e', e)
            if (e.product_type !== 'service') {
              this.productNormal.push(e)
            } else {
              this.productService.push(e)
            }
          })
        }
        // console.log('this.productNormal', this.productNormal)
        // console.log('this.productService', this.productService)
        if (this.items.receipt.length !== 0) {
          if (this.items.receipt[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.items.receipt[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.items.receipt[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.items.receipt[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.items.receipt[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else if (this.items.receipt[0].bankNo === 'CIMB') {
            this.bankName = 'ธนาคารซีไอเอ็มบี'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        // ต่อ Flash
        // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        if (this.items.order_mobilyst_no !== '') {
          var response = {
            code: 1,
            data: {
              customaryPno: null,
              eSignature: null,
              origPno: 'TH014276DR4A',
              pno: 'TH014276DR4A',
              returnedPno: null,
              routes: null,
              state: 0,
              stateChangeAt: null,
              stateText: '',
              ticketPickupId: null
            },
            message: 'success'
          }
          // console.log('res in Flash', response)
          this.flashTrackingData = response.data
          if (this.flashTrackingData !== null) {
            this.mockupTracking = this.flashTrackingData
            if (this.flashTrackingData.routes === null) {
              this.step = 0
            } else {
              this.step = this.flashTrackingData.routes.length
            }
          } else {
            this.step = 0
          }
        } else {
          this.flashTrackingNo = ''
        }
        this.typeShipping = this.items.type_shipping
        this.trackingStatus = this.items.tracking[0].status_tracking
        if (this.items.detail_status !== '-') {
          if (this.items.detail_status === 'ยกเลิกคำสั่งซื้อ') {
            this.trackingStatus = 'Cancel'
            this.items.transaction_status = 'Cancel'
          } else {
            this.trackingStatus = 'refund'
            this.items.transaction_status = 'refund'
          }
        }
        // console.log('this.items.detail_status', this.items.detail_status)
        // console.log('this.trackingStatus', this.trackingStatus)
        // console.log(' this.trackingStatus', this.trackingStatus)
        this.dateCreateOrderStep1 = new Date(this.items.tracking[0].time_step_1).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep2 = new Date(this.items.tracking[0].time_step_2).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep3 = new Date(this.items.tracking[0].time_step_3).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        this.dateCreateOrderCancel = new Date(this.items.tracking[0].time_cancel).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' })
        // this.dateCreateOrderStep4 = new Date(this.items.tracking[0].time_step_4).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
        if (this.items.tracking.length !== 0) {
          if (this.items.tracking[0].status_tracking === 'Not Paid') {
            this.trackingText = 'ที่ต้องรอชำระเงิน'
          } else if (this.items.tracking[0].status_tracking === 'Success') {
            this.trackingText = 'คำสั่งซื้อที่ชำระเงินแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Sent') {
            this.trackingText = 'ที่ต้องจัดส่ง'
          } else if (this.items.tracking[0].status_tracking === 'Sent') {
            this.trackingText = 'จัดส่งแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Received') {
            this.trackingText = 'ได้รับสินค้าแล้ว'
          } else if (this.items.tracking[0].status_tracking === 'Not Received') {
            this.trackingText = 'ยังไม่ได้รับสินค้า'
          } else if (this.items.tracking[0].status_tracking === 'Cancel by approver') {
            this.trackingText = 'ยกเลิกโดยผู้อนุมัติ'
          } else if (this.items.tracking[0].status_tracking === 'Cancel') {
            this.trackingText = 'ยกเลิกคำสั่งซื้อ'
          }
        } else {
          this.trackingText = ''
        }
        // console.log('this.items', this.items)
        this.CheckAcceptProduct()
        this.$store.commit('closeLoader')
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          if (res.message !== 'ผู้ใช้งานนี้ถูกใช้งานอยู่') {
            this.$swal.fire({
              toast: true,
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'error',
              title: res.message
            })
          } else {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
            // this.$swal.fire({
            //   toast: true,
            //   showConfirmButton: false,
            //   timer: 1500,
            //   timerProgressBar: true,
            //   icon: 'error',
            //   title: res.message
            // })
            // localStorage.removeItem('oneData')
            // this.$router.push({ path: '/' }).catch(() => {})
          }
        }
      }
    },
    refundProductBuyer (order) {
      this.$refs.ModalRefundProductBuyer.open(order, order.order_number, 'purchaser')
    },
    contactSeller () {
      // console.log('contact seller')
    },
    async CheckAcceptProduct () {
      var data = {
        payment_transaction_number: this.items.payment_transaction
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      this.checkAcceptProduct = res.data
      // this.checkAcceptProduct[0].status = 'waiting_accept'
      // console.log('checkAcceptProduct', this.checkAcceptProduct[0].status)
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    async acceptProduct () {
      // อาจจะมีการเพิ่มค่าที่ส่งไป api ถ้าหากมีแยกหลาย order
      var data = {
        payment_transaction_number: this.items.payment_transaction,
        order_number: this.items.payment_transaction,
        status: 'accepted',
        role_user: 'purchaser'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      // console.log('resacceptProduct')
      // console.log('data', data)
      if (res.message === 'Update status success.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว'
        })
        this.getItemProduct()
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR'
          })
        }
      }
      this.checkAccept = 'ยอมรับสินค้าแล้ว'
    },
    openPDF (val, type) {
      if (type === 'QT') {
        window.open(`${val.QT_order}`)
      } else if (type === 'PO') {
        window.open(`${val.PO_External}`)
      } else if (type === 'PR') {
        window.open(`${val.PR_External}`)
      } else if (type === 'SO') {
        window.open(`${val.SO_External}`)
      }
    },
    openModalReviewProduct (order, index) {
      const actions = 'create'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'purchaser')
    },
    openModalEditReviewProduct (order, index) {
      const actions = 'edit'
      this.$refs.ModalReviewProduct.open(this.items.data_list[index].product_list, order.order_number, actions, 'purchaser')
    },
    async GoToPayment () {
      const PaymentID = {
        role_user: 'purchaser'
      }
      await this.$store.dispatch('ActionGetPaymentPageB2B', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPageB2B
      // console.log('respose paymenttttttttt', response)
      window.location.replace(response.data.link_url)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    async GetQRCode (paymentTypeData) {
      // console.log('GetQRCode', paymentTypeData)
      this.$store.commit('openLoader')
      const paymentType = paymentTypeData
      if (paymentType === 'cashPayment') {
        await this.openDialogQR()
      }
    },
    async openDialogQR () {
      // console.log('openDialogQR')
      var data
      var resQR = ''
      var orderNumber = []
      orderNumber.push(this.items.detail_credit_term.payment_credit_term_number === '-' ? this.items.payment_transaction : this.items.detail_credit_term.payment_credit_term_number)
      data = {
        payment_transaction_number: orderNumber
      }
      await this.$store.dispatch('actionsGetQRCodeB2B', data)
      resQR = await this.$store.state.ModuleCart.stateGetQRCodeB2B
      if (resQR.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        this.netPrice = resQR.data.net_price
        this.Ref1 = resQR.data.ref1
        this.Ref2 = resQR.data.ref2
        this.imageBase64 = 'data:image/png;base64,' + resQR.data.img_base
        this.ImageQR = ''
        this.ImageQR = await resQR.data.img_base64
        var orderNumberB2B = await resQR.data.order_b2b
        setTimeout(() => {
          this.showIMG(this.ImageQR, orderNumberB2B)
        }, 1000)
        // this.DialogQR = true
      } else if (resQR.result === 'FAILED') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ERROR ไม่สามารถชำระเงินได้'
        })
        this.DialogQR = false
        this.dialogConfirm = false
      } else {
        if (resQR.message === 'This user is Unauthorized' || resQR.message === 'This user is unauthorized.' || resQR.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
          this.DialogQR = false
          this.dialogConfirm = false
        }
        // }
        // this.$store.commit('closeLoader')
      }
    },
    async showIMG (ImageQR, orderNumberB2B) {
      // console.log('ImageQR', ImageQR)
      this.Image = ImageQR
      // console.log('ImageQR', ImageQR)
      this.DialogQR = true
      this.$store.commit('closeLoader')
      var data
      data = {
        payment_transaction_number: orderNumberB2B
      }
      var value = data.payment_transaction_number
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'การชำระเงินไม่เสร็จสมบูรณ์'
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    async GetCC (paymentTypeData) {
      this.$store.commit('openLoader')
      this.dialogConfirm = false
      var data
      var resCC
      var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
      var orderNumber = []
      orderNumber.push(this.items.detail_credit_term.payment_credit_term_number === '-' ? this.items.payment_transaction : this.items.detail_credit_term.payment_credit_term_number)
      data = {
        go_local: goLocalValue,
        payment_transaction_number: orderNumber,
        term: this.radioCreditTerm === 'No' ? '' : this.radioCreditTerm
      }
      await this.$store.dispatch('actionsGetCCB2B', data)
      resCC = await this.$store.state.ModuleCart.stateGetCCB2B
      if (resCC.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        localStorage.setItem('PaymentData', Encode.encode(resCC.data))
        this.$router.push('/RedirectPaymentPage').catch(() => {})
      } else if (resCC.message === 'ERROR ระบบ Payment มีปัญหาไม่สามารถส่งหรือรับข้อมูลได้') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ระบบ Payment มีปัญหา',
          text: 'ไม่สามารถชำระเงินได้'
        })
      } else {
        if (resCC.message === 'This user is Unauthorized' || resCC.message === 'This user is unauthorized.' || resCC.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ไม่สามารถชำระเงินได้'
          })
        }
      }
      this.$store.commit('closeLoader')
    },
    async GeteWHT () {
      this.$store.commit('openLoader')
      var data
      var orderNumber = []
      orderNumber.push(this.items.detail_credit_term.payment_credit_term_number === '-' ? this.items.payment_transaction : this.items.detail_credit_term.payment_credit_term_number)
      data = {
        // order_number: this.items.payment_transaction
        payment_transaction_number: orderNumber
      }
      await this.$store.dispatch('actionSendEWHT', data)
      const response = await this.$store.state.ModuleCart.stateSendEWHT
      if (response.message === 'PaymentVoucher Import Successfully') {
        this.dialogConfirm = false
        await this.$store.dispatch('actionsRedirectWHT')
        const response = await this.$store.state.ModuleCart.stateRedirectWHT
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'success',
          text: 'สร้างคำสั่งจ่ายเงินสำเร็จ'
        })
        const newTab = window.open('', '_blank')
        if (newTab) {
          newTab.location.href = response.url
        } else {
          window.location.href = response.url
        }
        // this.dialogConfirm = false
        // await this.getFranchise()
        // await this.getItemProduct()
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: response.message
          })
        }
      }
    },
    confirmPayment () {
      this.dialogChooesPayType = false
      this.dialogConfirm = true
    },
    closeDialogPayment () {
      this.dialogChooesPayType = false
      this.radioPayment = 'no'
    },
    async closeDialogQR () {
      // this.$store.commit('openLoader')
      this.DialogQR = false
      this.CloseDialog = true
      this.dialogConfirm = false
      this.$store.commit('closeLoader')
      // this.dialogConfirm = false
      // var companyId = ''
      // var role = JSON.parse(localStorage.getItem('roleUser'))
      // if (role.role !== 'ext_buyer') {
      //   if (localStorage.getItem('SetRowCompany') !== null) {
      //     var companyDataID = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      //     companyId = companyDataID.company.company_id
      //   } else {
      //     companyId = -1
      //   }
      //   const data = {
      //     company_id: companyId
      //   }
      //   await this.$store.dispatch('actionsDetailCompany', data)
      //   await this.$store.dispatch('actionsAuthorityUser')
      //   var responsecompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
      //   var responseposition = await this.$store.state.ModuleUser.stateAuthorityUser
      //   var listcompany = responseposition.data.list_company
      //   for (let i = 0; i < listcompany.length; i++) {
      //     if (responsecompany.data.id === listcompany[i].company_id) {
      //       localStorage.removeItem('list_Company_detail')
      //       localStorage.setItem('list_Company_detail', Encode.encode(listcompany[i]))
      //     }
      //   }
      //   localStorage.setItem('CompanyData', Encode.encode(responsecompany.data))
      // }
      // if (role.role === 'ext_buyer') {
      //   this.$store.commit('closeLoader')
      //   if (!this.MobileSize) {
      //     this.$router.push({ path: `/pobuyerdetail?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: `/pobuyerdetailMobile?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
      //   }
      // } else {
      //   this.$store.commit('closeLoader')
      //   if (!this.MobileSize) {
      //     this.$router.push({ path: `/orderDetailCompany?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${this.paymenttransactionnumber}` }).catch(() => {})
      //   }
      // }
    }
  }
}
</script>

<style>
.v-data-table.row-height-64 td {
  height: 80px !important;
}
</style>

<style lang="css" scoped>
.copySTY:hover {
  transform: scale(1.3);
}
/* .v-application .mb-12 {
    margin-bottom: 12px !important;
} */
.fontActive {
  font-size: 14px;
  font-weight: 700;
  color: #27AB9C;
}
.fontInactive {
  color: #BDE7D9;
  font-weight: 700;
  font-size: 14px;
}
.fontPass {
  color: #333333;
  font-weight: 700;
  font-size: 14px;
}
::v-deep .ant-table-pagination {
  display: none;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.bgShippingUPS {
  background-color: #F3F5F7;
}
/* .fontActive {
  color: #27AB9C;
} */
/* .fontInactive {
  color: #A6A6A6;
} */
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 18px;
}
.fontSizeTotalPriceMobile {
  font-size: 16px;
}
.fontSizeAddressDetail {
  font-size: 16px;
}
.buttonFontSize {
  font-size: 14px;
  font-weight: normal;
}
.captionSku {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
.fontSizeDetail {
  font-size: 14px;
}
.fontSizeDetailMobile {
  font-size: 12px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>

<style scoped>

.couponIMGDesk{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  background-size: contain;
  padding: 1%;
  box-shadow: 5px 5px 5px 0px gray;
  /* height: 200px; */
  /* width: 250px; */
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
  box-shadow: 5px 5px 5px 0px gray;
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  background-size: cover;
  padding: 1%;
  box-shadow: 5px 5px 5px 0px gray;
}
</style>
