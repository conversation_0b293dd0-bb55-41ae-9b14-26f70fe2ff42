<template>
  <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0">
    <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px;" @click="backtoPage()"><v-icon color="#27AB9C" class="mr-2">mdi-chevron-left</v-icon> ร้านค้าขายดี Top 10666</v-card-title>
    <v-card-text class="my-4">
      <v-row dense>
        <v-col cols="12" md="6" v-for="(item, index) in dataShop" :key="index">
          <v-card width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0">
            <v-card-text>
              <v-row dense>
                <v-col cols="12" md="1" v-if="item.number < 5" class="mr-3">
                  <v-avatar size="45">
                    <v-img contain v-if="item.number === 1" src="@/assets/icons/level_1.png"></v-img>
                    <v-img contain v-else-if="item.number === 2" src="@/assets/icons/level_2.png"></v-img>
                    <v-img contain v-else-if="item.number === 3" src="@/assets/icons/level_3.png"></v-img>
                    <v-img contain v-else-if="item.number === 4" src="@/assets/icons/level_4.png"></v-img>
                    <v-img contain v-else-if="item.number === 5" src="@/assets/icons/level_5.png"></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="12" md="2">
                  <v-avatar tile size="63">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" max-height="120px" max-width="120px"></v-img>
                    <!-- <v-img :src="item.image_shop" max-height="120px" max-width="120px" style="border-radius: 8px;"></v-img> -->
                  </v-avatar>
                </v-col>
                <v-col cols="12" :md="index < 5 ? 6 : 7" class="pt-2">
                  <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">{{ item.shop_name }}</span><br/>
                  <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;">{{ item.shop_name }}</span>
                </v-col>
                <!-- <v-col cols="12" md="1" :class="index < 5 ? 'mt-2 mr-2' : 'mt-2 mr-4'">
                  <div :style="{'width': index < 5 ? '105px' : '115px'}" style="height: 40px; background: #F8FAFC; border-radius: 8px;">
                    <v-row justify="center" class="pt-2">
                      <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #27AB9C;">Partner <v-icon color="#27AB9C">mdi-bookmark</v-icon></span>
                    </v-row>
                  </div>
                </v-col> -->
              </v-row>
              <v-row dense class="mt-6">
                <v-col cols="12">
                  <v-row dense>
                    <v-icon class="ml-1 pr-2" color="#A1A1A1">mdi-file-document-outline</v-icon>
                    <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">รายการชำระเงินสำเร็จ : <b>{{ item.transaction }}</b> รายการ</span>
                  </v-row>
                </v-col>
              </v-row>
              <v-row dense class="mt-6">
                <v-col cols="12">
                  <v-row dense>
                    <v-icon class="ml-1 pr-2" color="#A1A1A1">mdi-currency-usd</v-icon>
                    <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">จำนวนเงิน : <b style="font-size: 24px; line-height: 32px; color: #52C41A;">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}</b> บาท</span>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dataShop: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    window.scrollTo(0, 0)
    this.$store.commit('openLoader')
    this.getDataShop()
  },
  methods: {
    backtoPage () {
      localStorage.removeItem('AllShopTopTen')
      this.$router.push({ path: '/Transaction' }).catch(() => {})
    },
    getDataShop () {
      this.dataShop = []
      this.dataShop = this.companyData = JSON.parse(Decode.decode(localStorage.getItem('AllShopTopTen')))
      this.$store.commit('closeLoader')
    }
  }
}
</script>
