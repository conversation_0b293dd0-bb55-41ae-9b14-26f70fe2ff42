<template>
  <v-container>
    <v-row>
      <h2 class="pt-1 ml-2 mt-2 mb-4" style="font-weight: 900;">{{ header }}</h2>
      <v-spacer style="border-top: 2px solid #bbb; margin-top: 28px; margin-left: 10px; margin-right: 12px;"></v-spacer>
      <!-- <v-btn text color="#27AB9C" class="mt-2" style="text-decoration: underline;">ดูหมวดหมู่ทั้งหมด</v-btn> -->
    </v-row>
    <div v-for="(item, index) in propsDataCat[0].data" :key="index">
      <v-row>
        <v-col cols="12" md="2" sm="12" v-for="(item1, index) in item.category_list" :key="index" style="padding-right: 0 !important;">
          <!-- {{ item1 }} -->
          <v-card width="180" height="250">
            <v-img :src="item1.category_img" width="180" height="250">
              <!-- <v-row
               justify="center"
               align-content="center"
              > -->
                <h3 style="color: white; text-align: center; justify-content: center; align-items: center; display: flex; font-weight: bold; height: 80%;">
                  {{ item1.category_name }}
                </h3>
                <div style="text-align: center; display: block;">
                  <p style="color: white; text-decoration: underline; cursor: pointer; text-align: center;">ดูทั้งหมด</p>
                </div>
                <!-- <div style="text-align: center; display: block;"> -->
                <!-- </div> -->
              <!-- </v-row> -->
            </v-img>
          </v-card>
        </v-col>
        <v-col cols="12" md="10" sm="12" class="mt-0">
          <vue-horizontal-list :items='item.product_list' :options='options'>
            <template v-slot:default='{ item }'>
            <!-- <a-skeleton :loading="check === true ? !loading : loading"> -->
              <CardProducts :itemProduct='item' />
            <!-- </a-skeleton> -->
            </template>
          </vue-horizontal-list>
          <!-- <v-sheet
            elevation="0"
            width="100%"
            style="background-color: rgb(245, 245, 245); padding-left: 0 !important;"
            class="mr-0 ml-0"
          >
            <v-slide-group
             show-arrows
             mandatory
            >
              <v-slide-item
                v-for="(item2, index) in item.product_list"
                :key="index"
                class="mr-2"
               >
                <CardProducts :itemProduct='item2' />
               </v-slide-item>
            </v-slide-group>
          </v-sheet> -->
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'
export default {
  props: ['propsDataCat', 'header'],
  components: {
    VueHorizontalList,
    CardProducts: () => import('@/components/Card/ProductCardUI')
  },
  data () {
    return {
      options: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 4 },
          { start: 992, end: 1200, size: 5 },
          { size: 5 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 12
        }
      },
      loading: true
    }
  },
  created () {
    // console.log(this.propsDataCat[0].data)
  }
}
</script>

<style>
.backgroundPage{
  background-color: rgb(245, 245, 245)
}
.vhl-item[data-v-8b923bbc] {
  box-sizing: content-box;
  padding-top: 0px !important;
  padding-bottom: 24px;
  z-index: 1;
  min-height: 1px;
}
.vhl-navigation[data-v-8b923bbc] {
  display: flex;
  align-items: center;
  position: absolute;
  width: 100%;
  height: 100%;
  margin-top: -20px !important;
}
</style>
