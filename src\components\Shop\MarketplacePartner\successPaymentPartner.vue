<template>
    <v-container class="pa-2">
      <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
        <div class="pt-2 px-2" style="position: relative; text-align: center; width: 100%;">
          <img
            src="@/assets/Marketplace_partner/FrameSuccess.png"
            alt="Software Marketplace"
            width="100%">
          <p style="
              position: absolute;
              top: 45%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-size: 3vw;
              font-weight: bold;
              text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            ">
            คำสั่งซื้อของคุณ
          </p>
          <p style="
              position: absolute;
              top: 75%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-size: 1.8vw;
              font-weight: bold;
              text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
          ">ขอบคุณที่ใช้บริการของเรา</p>
        </div>
        <br>
        <div class="px-2" v-if="!MobileSize">
          <v-row>
            <v-col cols="12" :md="IpadProSize ? '7' : '8' ">
              <v-row>
                <v-col cols="12" class="pa-2">
                  <span style="font-size: 16px;"><b>การชำระเงิน</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>รหัสการสั่งซื้อ</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ itemsResult.data_payment[0].orderId }}</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>จำนวนเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ Number(itemsResult.data_payment[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>วันและเวลาที่ชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) }}</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>Ref</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ itemsResult.data_payment[0].orderIDRef }}</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>รูปแบบการชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ itemsResult.data_payment[0].payType }}</b></span>
                </v-col>
                <!-- <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>ธนาคารที่ชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ bankName }}</b></span>
                </v-col> -->
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>ผลการชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2" v-if="itemsResult.data_payment[0].rmsg === 'Success'">
                  <span style="color: #52C41A;"><b>ชำระเงินสำเร็จ</b></span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" :md="IpadProSize ? '5' : '4' " >
              <v-row>
                <v-col cols="12" class="pa-2">
                  <span style="font-size: 16px;"><b>สถานะการสั่งซื้อ</b></span>
                </v-col>
                <v-col cols="12" class="pa-2">
                  <img
                    src="@/assets/Marketplace_partner/FrameSuccess1.png"
                    alt="Software Marketplace"
                    class="img-responsive"
                    >
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-5 pb-5">
              <v-data-table
                :headers="headers"
                :items="paymentList"
                item-value="purchaseOrderNumber"
                :items-per-page="itemsPerPage"
                :page.sync="page"
              >
                <template v-slot:[`item.index`]="{ index }">
                  {{ (page - 1) * itemsPerPage + index + 1 }}
                </template>
                <template v-slot:[`item.amountMoney`]="{ item }">
                  <span>{{item.amountMoney !== '-' ? Number(item.amountMoney).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}</span>
                </template>
              </v-data-table>
            </v-col>
            <v-col cols="12">
              <v-divider class="my-4"></v-divider>
              <v-row dense justify="center">
                <v-col cols="12" class="pa-1 text-center" :class="MobileSize ? 'd-flex flex-column align-center' : 'd-flex flex-row justify-center'">
                  <v-btn
                    :width="MobileSize ? '100%' : ''"
                    :class="MobileSize ? '': 'mr-4'"
                    height="38"
                    outlined
                    rounded
                    color="#27AB9C"
                    class="mb-2"
                    @click="goToPackage()"
                  >
                    <b>ไปยังหน้ารายการชำระเงิน</b>
                  </v-btn>
                  <v-btn
                    :width="MobileSize ? '100%' : ''"
                    height="38"
                    class="white--text"
                    rounded
                    color="#27AB9C"
                    @click="goToLinkERPPartner()"
                  >
                    <b>ไปยังหน้าเชื่อมต่อบริการ</b>
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
        <div class="px-2" v-if="MobileSize">
          <v-row>
            <v-col cols="12" md="3">
              <v-row>
                <v-col cols="12" class="pa-2">
                  <span style="font-size: 16px;"><b>สถานะการสั่งซื้อ</b></span>
                </v-col>
                <v-col cols="12" class="pa-2">
                  <img
                    src="@/assets/Marketplace_partner/FrameSuccess1.png"
                    alt="Software Marketplace"
                    width="100%">
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="9">
              <v-row>
                <v-col cols="12" class="pa-2">
                  <span style="font-size: 16px;"><b>การชำระเงิน</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>รหัสการสั่งซื้อ</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ itemsResult.data_payment[0].orderId }}</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>จำนวนเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ Number(itemsResult.data_payment[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }} บาท</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>วันและเวลาที่ชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ new Date(itemsResult.data_payment[0].created_at).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) }}</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>Ref</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ itemsResult.data_payment[0].orderIDRef }}</b></span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>รูปแบบการชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ itemsResult.data_payment[0].payType }}</b></span>
                </v-col>
                <!-- <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>ธนาคารที่ชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2">
                  <span><b>{{ bankName }}</b></span>
                </v-col> -->
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" class="pa-2">
                  <span>ผลการชำระเงิน</span>
                </v-col>
                <v-col :cols="MobileSize ? 6 : 12" md="6" sm="6" align="right" class="pa-2" v-if="itemsResult.data_payment[0].rmsg === 'Success'">
                  <span style="color: #52C41A;"><b>ชำระเงินสำเร็จ</b></span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-5 pb-5">
              <v-data-table
                :headers="headers"
                :items="paymentList"
                item-value="purchaseOrderNumber"
                :items-per-page="itemsPerPage"
                :page.sync="page"
              >
                <template v-slot:[`item.index`]="{ index }">
                  {{ (page - 1) * itemsPerPage + index + 1 }}
                </template>
                <template v-slot:[`item.amountMoney`]="{ item }">
                  <span>{{item.amountMoney !== '-' ? Number(item.amountMoney).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00' }}</span>
                </template>
              </v-data-table>
            </v-col>
            <v-col cols="12">
              <v-divider class="my-4"></v-divider>
              <v-row dense justify="center">
                <v-col cols="12" class="pa-1 text-center" :class="MobileSize ? 'd-flex flex-column align-center' : 'd-flex flex-row justify-center'">
                  <v-btn
                    :width="MobileSize ? '100%' : ''"
                    :class="MobileSize ? '': 'mr-4'"
                    height="38"
                    outlined
                    rounded
                    color="#27AB9C"
                    class="mb-2"
                    @click="goToPackage()"
                  >
                    <b>ไปยังหน้า Package Partner</b>
                  </v-btn>
                  <v-btn
                    :width="MobileSize ? '100%' : ''"
                    height="38"
                    class="white--text"
                    rounded
                    color="#27AB9C"
                    @click="goToLinkERPPartner()"
                  >
                    <b>ไปยังหน้าเชื่อมต่อบริการ</b>
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
      </v-card>
    </v-container>
  </template>

<script>
export default {
  data () {
    return {
      itemsPerPage: 5,
      page: 1,
      itemsResult: [],
      bankName: '',
      paymentList: [],
      headers: [
        {
          text: 'ลำดับ',
          value: 'index',
          sortable: false,
          align: 'start',
          width: '50px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสคำสั่งซื้อ',
          value: 'purchaseOrderNumber',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Partner',
          value: 'partnerName',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขใบแจ้งหนี้',
          value: 'invoiceNumber',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ยอดใบแจ้งหนี้',
          value: 'amountMoney',
          sortable: false,
          align: 'start',
          width: '150px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  // watch: {
  //   MobileSize (val) {
  //     if (val === true) {
  //       this.$router.push({ path: `/successPaymentPartnerMobile?id=${this.$router.currentRoute.query.id}` }).catch(() => {})
  //     } else {
  //       this.$router.push({ path: `/successPaymentPartner?id=${this.$router.currentRoute.query.id}` }).catch(() => {})
  //     }
  //   }
  // },
  async created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNav')
    var items = []
    items = JSON.parse(localStorage.getItem('orderPartner')) || []
    var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
    await this.checkResult()
    await this.getDetails(shopID, items)
  },
  methods: {
    async getDetails (id, order) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: id,
        billOrderNumber: order
      }
      await this.$store.dispatch('actionDetailOrderPurchasePartner', data)
      var response = await this.$store.state.ModuleOrder.stateDetailOrderPurchasePartner
      if (response.code === 200) {
        this.paymentList = response.data
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    goToLinkERPPartner () {
      localStorage.removeItem('orderPartner')
      if (this.MobileSize) {
        this.$router.replace({ path: '/ERPPartnerMobile' }).catch(() => { })
      } else {
        this.$router.replace({ path: '/ERPPartner' }).catch(() => { })
      }
    },
    goToPackage () {
      localStorage.removeItem('orderPartner')
      if (this.MobileSize) {
        this.$router.replace('/paymentPartnerMobile')
      } else {
        this.$router.replace('/paymentPartner')
      }
    },
    async checkResult () {
      this.$store.commit('openLoader')
      var data
      data = {
        payment_transaction_number: this.$router.currentRoute.query.id
      }
      await this.$store.dispatch('actionCheckResultQRCodeMarketplace', data)
      const res = await this.$store.state.ModuleShop.stateCheckResultQRCodeMarketplace
      if (res.result === 'SUCCESS') {
        this.itemsResult = res.data
        if (this.itemsResult.length !== 0) {
          if (this.itemsResult.data_payment[0].bankNo === 'SCB') {
            this.bankName = 'ธนาคารไทยพาณิชย์ (SCB)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'BBL') {
            this.bankName = 'ธนาคารกรุงเทพ (BBL)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'KTB') {
            this.bankName = 'ธนาคารกรุงไทย (KTB)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'BAY') {
            this.bankName = 'ธนาคารกรุงศรีอยุธยา (BAY)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'KTC') {
            this.bankName = 'บริษัทบัตรกรุงไทย (KTC)'
          } else if (this.itemsResult.data_payment[0].bankNo === 'CIMB') {
            this.bankName = 'ธนาคารซีไอเอ็มบี'
          } else {
            this.bankName = 'ธนาคารอื่นๆ'
          }
        } else {
          this.bankName = ''
        }
        // this.CheckUpdateOrderRepeat()
        this.$store.commit('closeLoader')
        // console.log(this.dataSellerShop)
      } else if (res.result === 'ERROR') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'การชำระเงินไม่เสร็จสมบูรณ์'
        })
        this.$router.push({ path: '/ShopJoinPartnerDetails' }).catch(() => {})
      }
    }
  }
}
</script>
  <style scoped>
  .img-responsive {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
  }
  </style>
