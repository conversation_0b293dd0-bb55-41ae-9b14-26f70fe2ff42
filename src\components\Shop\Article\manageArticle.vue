<template>
  <v-container class="pa-2">
    <div :class="MobileSize ? 'pa-4' : 'mt-0 pa-4'" style="background-color: #FFFFFF; min-height: 1000px;">
      <v-col cols="12" md="3" sm="12" align="start" :class="MobileSize ? 'title_Mobile' : 'title'">
        <v-row dense >
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>
          จัดการบทความ
        </v-row>
      </v-col>

      <div class="add-article">
        <v-row dense no-gutters class="d-flex mt-4 mb-4">
          <!-- Check for IpadSize to align both elements in the same row -->
          <v-col cols="12" md="6" :align="MobileSize || IpadSize ? 'center' : 'start'" :class="MobileSize || IpadSize ? 'mb-4' : 'mr-auto'">
            <v-text-field v-model="textSearch" @keyup.enter="getArticle()" outlined dense style="border-radius: 8px;" hide-details placeholder="ค้นหาบทความ">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" :align="MobileSize ? 'center' : 'end'" class="ml-auto">
            <v-btn @click="createArticle()" color="#27AB9C" :height="MobileSize ? '48' : ''" :block="MobileSize || IpadSize ? true : false" dark dense rounded class="pl-9 pr-9">
              <v-icon left>mdi-plus</v-icon>
              <span>เพิ่มบทความ</span>
            </v-btn>
          </v-col>
        </v-row>
      </div>

      <div id="listData" v-if="show">
        <!-- Skeleton Loader -->
        <v-row v-if="showSkeletonLoader && !MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="12" md="4" v-for="n in 3" :key="n" class="d-flex">
            <v-skeleton-loader type="card" class="my-3" style="width: 100%;"></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-row v-if="showSkeletonLoader && !MobileSize && !IpadSize && IpadProSize">
          <v-col cols="12" md="4" v-for="n in 3" :key="n">
            <v-skeleton-loader type="card" class="my-3" style="width: 100%;"></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-row v-if="showSkeletonLoader && !MobileSize && IpadSize && !IpadProSize">
          <v-col cols="12" md="4" sm="6" v-for="n in 3" :key="n" class="d-flex">
            <v-skeleton-loader type="card" class="my-3" style="width: 100%;"></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-row v-if="showSkeletonLoader && MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="12" v-for="n in 3" :key="n" class="d-flex">
            <v-skeleton-loader type="card" class="my-3" style="width: 100%;"></v-skeleton-loader>
          </v-col>
        </v-row>

        <div v-if="!showSkeletonLoader && dataList.length !== 0" class="mt-10">
          <div :class="MobileSize ? 'mt-4' : 'mt-10'">
            <v-row>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-for="(item, index) in newsListShow"
                :key="index"
                :align="IpadSize ? 'center' : 'start'"
              >
                <v-card
                  :height="'100%'"
                  elevation="0"
                  style="display: flex; flex-direction: column; cursor: pointer; border-radius: 20px; filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.04)) drop-shadow(0px 2px 4px rgba(96, 97, 112, 0.16));"
                >
                  <div class="d-flex py-3 px-3">
                    <span class="switch mt-1 mr-2">แสดงบทความ</span>
                    <v-switch v-model="item.article_status" @click="switchStatus(item.id, item.status)" inset color="#52C41A" class="mt-0 mr-auto" hide-details></v-switch>
                    <v-btn @click="goToEdit(item)" color="#38C9B9" x-small icon dark dense rounded>
                      <v-avatar size="22" color="#38C9B9">
                        <v-icon x-small color="#FFFFFF">mdi-pencil</v-icon>
                      </v-avatar>
                    </v-btn>
                    <v-btn @click="dialogConfirmDelete = true, selectedItem = item" color="#38C9B9" x-small class="ml-2" icon dark dense rounded>
                      <v-avatar size="22" color="#38C9B9">
                        <v-icon x-small color="#FFFFFF">mdi-delete</v-icon>
                      </v-avatar>
                    </v-btn>
                  </div>

                  <div style="min-height: 180px;" id="image">
                    <div v-if="item.link_video === '0'">
                      <v-img v-if="item.header_image === ''" src="" cover :height="IpadSize ? 240 : 180" width="auto" class="" contain></v-img>
                      <v-img v-else cover :height="IpadSize ? 240 : 180" width="auto" class="" :src="item.header_image" contain></v-img>
                    </div>
                    <div v-else>
                      <Youtube
                        ref="youtube"
                        v-if="item.header_image.includes('youtube')"
                        :video-id="youtube_parser(item.header_image)"
                        @playing="playing"
                        :player-vars="playerVars"
                        style="width: 100%; height: 180px;">
                      </Youtube>
                      <!-- <iframe v-else-if="item.header_image.includes('facebook')" :src="`https://www.facebook.com/plugins/post.php?href=${item.header_image}`" style="border:none; overflow:hidden; width: 100%; height: 180px; opacity:100%;" controls contain allow="web-share"></iframe>
                      <video v-else style="width: 100%; height: 180px;" controls contain>
                        <source :src="item.header_image.includes('/drive.google.com/') ? `https://drive.google.com/uc?export=download&id=${getIdFromUrlDrive(item.header_image)}` : item.header_image" id="video_here">
                      </video> -->
                    </div>
                  </div>

                  <div style="min-height:100px;">
                    <v-card-title class="cardTitle pa-0">
                      <v-col cols="12">
                        <div :style="MobileSize ? '' : ''">
                          <span :style="MobileSize ? 'font-weight: 700; font-size: 16px;' : 'font-weight: 700; font-size: 18px;'"
                                style="display: inline-block; font-weight: 700; font-size: 24px; line-height: 1.5em; min-height: 20px; height: 50px; overflow: hidden; text-overflow: ellipsis;">
                                {{ item.title | truncate(70, '...') }}
                          </span><br />
                        </div>
                      </v-col>
                    </v-card-title>
                    <v-card-subtitle class="cardDetail pa-0">
                      <v-col cols="12" style="height: 50px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: normal; text-align: start;">
                        {{ item.shot_description }}
                      </v-col>
                    </v-card-subtitle>
                  </div>

                  <v-card-actions class="pt-0 ml-2 cardDetail" style="margin-bottom: auto;">
                    <v-icon class="icon">mdi-eye</v-icon>
                    <span class="ml-2">View {{ item.view }}</span>
                    <v-spacer></v-spacer>
                    <span class="mr-2">{{ calDiffDate(item.updated_at, new Date()) }}</span>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </div>

          <v-row no-gutters justify="center" class="py-6" v-if="hidePagegination">
            <v-pagination
              color="#27AB9C"
              v-model="pageNumber"
              :length="pageMax"
              @input="getDatapage($event)"
              :total-visible="MobileSize ? '4' : '10'">
            </v-pagination>
          </v-row>
        </div>

        <div v-if="!showSkeletonLoader && showNoResults" class="mt-10">
          <v-col cols="12" md="12">
            <v-row justify="center" align="center">
              <v-col cols="12" md="12" align="center" class="mt-2">
                <v-img src="@/assets/newarticle1.png" width="596px" height="363px" contain></v-img>
              </v-col>
              <v-col cols="12" md="12" style="text-align: center;">
                <span style="font-size: 24px; line-height: 24px; font-weight: 400; color:#27AB9C;">ไม่พบบทความ</span><br />
              </v-col>
            </v-row>
          </v-col>
        </div>
        <div v-else-if="!showSkeletonLoader && dataList.length === 0">
          <v-col cols="12" md="12">
            <v-row justify="center" align="center">
              <v-col cols="12" md="12" align="center" class="mt-2">
                <v-img src="@/assets/article.png" width="596px" height="363px" contain></v-img>
              </v-col>
              <v-col cols="12" md="12" style="text-align: center;">
                <span style="font-size: 24px; line-height: 24px; font-weight: 400; color:#333333;">คุณยังไม่มีบทความ</span><br />
                <span style="font-size: 24px; line-height: 24px; font-weight: 400; color:#333333;">กด <span style="color:#27AB9C;">“เพิ่มบทความ”</span> เพื่อข้อมูลที่สมบูรณ์ของคุณ</span><br />
              </v-col>
            </v-row>
          </v-col>
        </div>
      </div>

      <v-dialog v-model="dialogConfirmDelete" width="464" persistent>
        <v-card style="background: #FFFFFF; border-radius: 12px;" height="100%">
          <v-toolbar align="center" color="#C8F3E5" dark dense elevation="0">
            <span class="flex text-center" style="font-weight: 700; font-size: 18px; line-height: 24px; color: #27AB9C;">
              ลบบทความ
            </span>
            <v-btn icon dark @click="dialogConfirmDelete = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row dense justify="center">
              <v-col cols="12" align="center" class="mt-8 mb-8">
                <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="">คุณได้ทำการลบบทความ</span><br/>
                <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="pt-4">คุณต้องการดำเนินการต่อ ใช่ หรือ ไม่ ?</span>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-row class="mb-2" justify="center" dense>
              <v-btn outlined height="48" width="110" dense rounded dark color="#27AB9C" class="mr-4" @click="dialogConfirmDelete = false">ยกเลิก</v-btn>
              <v-btn height="48" width="110" color="#27AB9C" dark dense rounded class="" @click="confirmDelete()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

    </div>
  </v-container>
</template>

<script>
import { Youtube } from 'vue-youtube'
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    Youtube
  },
  data () {
    return {
      show: false,
      showSkeletonLoader: true,
      // ข้อมูลเกี่ยวกับ id="listData"
      textSearch: '',
      dataList: [],
      // ข้อมูลเกี่ยวกับ pagination
      pageMax: null,
      current: 1,
      itemPerPage: 9,
      hidePagegination: true,

      selectedItem: [],
      dialogConfirmDelete: false,

      // youtube
      playerVars: {
        autoplay: 0,
        controls: 1
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.itemPerPage
    },
    indexEnd () {
      return this.indexStart + this.itemPerPage
    },
    newsListShow () {
      return this.dataList.slice(this.indexStart, this.indexEnd)
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    if (this.$route.query.page !== undefined) {
      this.pageNumber = parseInt(this.$route.query.page)
    }
    this.getArticle()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    async $route (to, from) {
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page
      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          this.pageNumber = parseInt(this.$route.query.page)
          this.getDatapage(this.pageNumber)
        }
      }
    }
  },
  methods: {
    backtoMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    async getArticle () {
      this.$store.commit('openLoader')
      this.showSkeletonLoader = true
      const shopId = localStorage.getItem('shopSellerID')
      this.dataList = []
      var data = {
        role: 'seller',
        seller_shop_id: shopId
      }

      await this.$store.dispatch('actionsListArticleWithSellerShop', data)
      var res = await this.$store.state.ModuleArticle.stateListArticleWithSellerShop

      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.showSkeletonLoader = false
        const allArticles = [...res.data]

        if (this.textSearch.trim() !== '') {
          const searchQuery = this.textSearch.trim().toLowerCase()
          this.dataList = allArticles.filter(article =>
            article.title.toLowerCase().includes(searchQuery)
          )
        } else {
          this.dataList = allArticles
        }

        // console.log(this.dataList)

        // this.pageMax = this.dataList.length === 0 ? 1 : Math.ceil(this.dataList.length / 9)
        this.pageMax = parseInt(this.dataList.length / 9) === 0 ? 1 : Math.ceil(this.dataList.length / 9)

        // ตรวจสอบเพิ่มเติมสำหรับไม่มีบทความ
        if (this.textSearch.trim() !== '' && this.dataList.length === 0) {
          this.showNoResults = true
        } else {
          this.showNoResults = false
        }

        // อัปเดตสถานะบทความและรูปภาพ
        this.dataList.forEach(element => {
          element.article_status = element.article_status === 'active'
          element.header_image = (element.header_image_video === 'image' && element.header_image_video !== '')
            ? element.header_image_video + '?' + new Date()
            : element.header_image_video
        })

        // console.log(this.dataList)

        this.dataList.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
        this.show = true
      } else {
        this.$store.commit('closeLoader')
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${res.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    createArticle () {
      if (this.MobileSize) {
        this.$router.push({ path: '/createArticleMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createArticle' }).catch(() => {})
      }
    },
    goToEdit (item) {
      if (this.MobileSize) {
        this.$router.push({ path: `/editArticleMobile?ArticleID=${item.id}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/editArticle?ArticleID=${item.id}` }).catch(() => {})
      }
    },
    async switchStatus (id, status) {
      this.$store.commit('openLoader')
      var data = {
        article_status: status ? 'active' : 'inactive',
        article_id: id
      }
      await this.$store.dispatch('actionsChangeStatus', data)
      var res = await this.$store.state.ModuleArticle.stateChangeStatus
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${res.message}`
        })
      }
    },
    async confirmDelete () {
      this.dialogConfirmDelete = false
      this.$store.commit('openLoader')
      var data = {
        article_id: this.selectedItem.id
      }
      await this.$store.dispatch('actionsDeleteArticle', data)
      var res = await this.$store.state.ModuleArticle.stateDeleteArticle
      // console.log(res)
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ลบบทความสำเร็จ'
        })
        this.getArticle()
      } else {
        this.$store.commit('closeLoader')
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${res.message}`
        })
      }
    },
    async getDatapage (page) {
      // ตรวจสอบว่าหมายเลขหน้าปัจจุบันไม่ตรงกับหมายเลขหน้าที่ต้องการ
      if (this.$route.query.page !== String(page)) {
        await this.getArticle()
        // หากไม่ตรง, นำทางไปยังหน้าบทความที่มีหมายเลขหน้าตามที่กำหนด
        if (this.MobileSize) {
          await this.$router.push({ path: '/manageArticleMobile', query: { page } })
        } else {
          await this.$router.push({ path: '/manageArticle', query: { page } })
        }
        // อัปเดตค่าหมายเลขหน้าใน component
        this.pageNumber = page
      }
    },
    calDiffDate (dateA, dateB) {
      var date1 = new Date(dateA)
      var date2 = new Date(dateB)
      var DiffInTime = date2.getTime() - date1.getTime()

      var DiffInDays = DiffInTime / (1000 * 3600 * 24)
      var DiffMin = DiffInDays < 1 ? (date2.getTime() - date1.getTime()) / (1000 * 60) : 0
      var DiffHours = DiffMin > 70 ? DiffMin / 60 : 0
      // console.log(dateA, DiffInDays, DiffMin, DiffHours)
      return (DiffInDays > 1 ? `${Math.floor(DiffInDays)} วันที่ผ่านมา` : DiffMin > 70 ? `${Math.floor(DiffHours)} ชั่วโมงที่ผ่านมา` : `${Math.floor(DiffMin)} นาทีที่ผ่านมา`)
    },
    getIdFromUrlDrive (url) {
      return url.match(/[-\w]{25,}/)
    },
    youtube_parser (url) {
      // console.log('0', url)
      let ID = ''
      url = url.replace(/(>|<)/gi, '').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
      if (url[2] !== undefined) {
        ID = url[2].split(/[^0-9a-z_-]/i)
        ID = ID[0]
      } else {
        // console.log('เข้ามาทำไม')
        ID = false
      }
      // console.log(ID)
      return ID
    },
    playing () {
      // console.log('we are watching!!!')
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.title_Mobile {
  font-weight: 700; font-size: 18px; line-height: 24px; color: #333333;
}
.cardTitle {
  font-weight: 600; font-size: 17px; line-height: 24px; color: #27AB9C;
}
.cardDetail {
  font-weight: 300; font-size: 15px; line-height: 22px; color: #636363;
}
</style>
