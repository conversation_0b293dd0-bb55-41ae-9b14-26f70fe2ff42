<template>
  <v-container>
    <a-card>
      <a-row>
        <a-col :span='24' class="mb-2">
          <a-row type="flex" justify="start">
            <v-icon x-large class="mb-2">mdi-store-outline</v-icon><span class="pl-5 pt-1 headline">ร้านค้าของฉัน</span>
          </a-row>
        </a-col>
        <a-col :span='24'>
            <TableShop :props='DataTable' />
          </a-col>
        </a-row>
    </a-card>
  </v-container>
</template>
<script>
export default {
  components: {
    TableShop: () => import('@/components/Shop/MyShop/TableShop')
  },
  data () {
    return {
      DataTable: []
    }
  },
  created () {
    this.getShopData()
  },
  methods: {
    async getShopData () {
      await this.$store.dispatch('actionsGetShopData')
      var response = await this.$store.state.ModuleShop.stateShopData
      // console.log('response shop data====>', response.data)
      this.$EventBus.$emit('send_shop', response.data[0])
      if (response.result === 'SUCCESS') {
        this.DataTable = response.data
      } else {
        this.DataTable = []
      }
    }
  }
}
</script>
