<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">จัดการผู้ซื้อองค์กร</v-card-title>
      <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> จัดการผู้ซื้อองค์กร</v-card-title>
      <v-card-text>
        <v-row dense class="mt-2">
          <v-col cols="12" md="6" sm="7">
            <v-text-field v-model="search" dense hide-details rounded outlined placeholder="ค้นหาจากรูปแบบการอนุมัติ ชื่อ-นามสกุล อีเมลหรือเบอร์โทรศัพท์">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" sm="5" align="end" :class="MobileSize ? 'pt-4' : ''">
            <v-btn dark :block="MobileSize" color="#27AB9C" @click="addBuyerApprove()"><v-icon color="" class="pr-2">mdi-plus</v-icon> เพิ่มรายชื่อ</v-btn>
          </v-col>
        </v-row>
        <v-row dense class="mt-4" v-if="showTable === true">
          <v-col cols="12" class="mb-4">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายการผู้ซื้อองค์กร {{ showCountOrder }} รายการ</span>
          </v-col>
          <v-col cols="12" md="12" sm="12" xs="12">
            <v-card outlined class="mb-4">
              <v-data-table
              :headers="headersListBuyer"
              :items="DataTable"
              :items-per-page="10"
              :page.sync="page"
              :search="search"
              @pagination="countOrdar"
              no-results-text="ไม่พบข้อมูลผู้ซื้อจากคำที่ค้นหา"
              no-data-text="ไม่มีข้อมูลผู้ซื้อในตาราง"
              :update:items-per-page="getItemPerPage"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              >
                <template v-slot:[`item.actions`]="{ item }">
                  <v-row dense>
                    <v-col cols="6">
                      <v-btn
                      x-small
                      @click="editBuyer(item)"
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      outlined
                      class="pt-4 pb-4"
                      :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                      >
                        <v-icon color="#27AB9C" :size="18">mdi-pencil-outline</v-icon>
                      </v-btn>
                    </v-col>
                    <v-col cols="6">
                      <v-btn
                      x-small
                      @click="confirmSettingAdmin('deleteBuyerApprove', item)"
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      outlined
                      class="pt-4 pb-4"
                      :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                      >
                        <v-icon color="#27AB9C" :size="18">mdi-delete-outline</v-icon>
                      </v-btn>
                    </v-col>
                  </v-row>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
        </v-row>
        <v-row no-gutters v-if="showTable === false">
          <v-col cols="12" md="12" sm="12" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการผู้ซื้อ</b></h2>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- Modal เพิ่มผู้ซื้อ -->
    <v-dialog v-model="modalAddBuyerApprove" width="732px" persistent>
      <v-card width="100%" height="100%" :min-height="clickSelectBuyer === false ? '558px' : '600px'" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px" v-if="clickSelectBuyer === false">
            <font color="#27AB9C">รายชื่อ</font>
          </span>
          <span class="flex text-center ml-5" style="font-size:20px" v-else>
            <font color="#27AB9C">การตั้งค่ารายละเอียดผู้ซื้อ</font>
          </span>
          <v-btn icon dark @click="CloseModalAddBuyer('close')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-form ref="FormSetBuyerApprove" :lazy-validation="lazy">
          <v-card-text>
            <v-row dense class="my-4">
              <v-col cols="12">
                <v-img class="float-left" src="@/assets/ICON/Buyer.png" contain width="60px" height="60px"></v-img>
                <v-card-title style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">รายชื่อผู้ซื้อ</v-card-title>
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" :class="MobileSize ? '' : 'px-12'" v-if="clickSelectBuyer === false">
              <v-col cols="12" :class="MobileSize ? '' : 'px-12'">
                <p>ค้นหาผู้ใช้งาน</p>
                <v-text-field class="rounded-lg" v-model="searchBuyer" @keydown.enter.prevent="submit" placeholder="ค้นหาผู้ใช้งาน" outlined dense hide-details>
                  <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : 'px-12'" v-if="notfoundBuyer === false" style="max-height: 250px;" :style="{'overflow-y': filteredList.length === 0 ? 'hidden' : 'scroll'}">
                <div v-for="(item, index) in filteredList" :key="index">
                  <v-card outlined class="mb-4" @click="selectBuyer(item)">
                    <div class="d-flex flex-no-wrap">
                      <v-avatar class="ma-4" :size="MobileSize ? '60' : '132'" tile>
                        <v-img :src="item.img_path" contain style="border-radius: 8px;" v-if="item.img_path !== null"></v-img>
                        <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                      </v-avatar>
                      <div class="ma-4">
                        <p>ชื่อ-สกุล : <b>{{ item.name }}</b></p>
                        <p>อีเมล : <b>{{ item.email }}</b></p>
                        <p>เบอร์โทรศัพท์ : <b>{{ item.phone }}</b></p>
                        <!-- <p class="mb-0">ตำแหน่ง : <b>{{ item.username }}</b></p> -->
                      </div>
                    </div>
                  </v-card>
                </div>
              </v-col>
              <v-col cols="12" class="px-12" v-if="filteredList.length === 0 && searchBuyer !== ''" align="center">
                <div style="padding-top: 30px;">
                  <v-img src="@/assets/icons/notfoundAdminUser.png" max-height="146px" max-width="135px" height="100%" width="100%" contain></v-img>
                </div>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #333333;">
                  <span style="font-weight: bold; font-size: 24px; line-height: 30px;">ไม่พบผู้ใช้งานนี้ในรายชื่อผู้ซื้อองค์กร หรือ ผู้ใช้งานนี้ถูกใช้งานแล้ว</span><br/>
                </h2>
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" :class="MobileSize ? '' : 'px-12'" v-else>
              <v-col cols="12" :class="MobileSize ? '' : 'px-12'">
                <v-card outlined>
                  <v-card-text class="px-0">
                    <div class="d-flex flex-no-wrap">
                      <v-avatar class="ma-4" :size="MobileSize ? '60' : '132'" tile>
                        <v-img :src="selectBuyerItem.img_path" contain style="border-radius: 8px;" v-if="selectBuyerItem.img_path !== null "></v-img>
                        <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                      </v-avatar>
                      <div class="ma-4">
                        <p>ชื่อ-สกุล : <b>{{ selectBuyerItem.name }}</b></p>
                        <p>อีเมล : <b>{{ selectBuyerItem.email }}</b></p>
                        <p>เบอร์โทรศัพท์ : <b>{{ selectBuyerItem.phone }}</b></p>
                        <p class="mb-0">รูปแบบการอนุมัติ <span style="color: red;">*</span> :
                          <v-select
                            v-model="select"
                            :items="listTypeApprove"
                            item-text="name"
                            item-value="approve_position_id"
                            placeholder="กรุณาเลือก"
                            dense
                            :style="MobileSize ? 'width: 190px !important;' : ''"
                            outlined
                            class="mt-2"
                            :rules="Rules.selectType"
                          ></v-select>
                        </p>
                      </div>
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <v-row cols="12" :class="MobileSize ? 'mt-10 pb-2' : 'px-12 mt-10 pb-2'" v-if="filteredList.length !== 0">
              <v-col cols="12" :class="MobileSize ? '' : 'px-12 pt-0'" :align="MobileSize ? 'center' : 'end'">
                <v-btn outlined class="mr-2" color="#27AB9C" @click="CloseModalAddBuyer('close')" v-if="clickSelectBuyer === false">ยกเลิก</v-btn>
                <v-btn outlined class="mr-2" color="#27AB9C" @click="CloseModalAddBuyer('back')" v-else>ยกเลิก</v-btn>
                <v-btn class="white--text" color="#27AB9C" @click="confirmSettingAdmin('addBuyerApprove', '')" :disabled="clickSelectBuyer === false ? true : false">บันทึก</v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- Modal แก้ไขผู้ซื้อ -->
    <v-dialog v-model="modalEditBuyerApprove" width="732px" persistent>
      <v-card width="100%" height="100%" min-height="600px" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">การตั้งค่ารายละเอียดผู้ซื้อ</font>
          </span>
          <v-btn icon dark @click="CloseModalEditBuyer()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-form ref="FormSetBuyerApprove" :lazy-validation="lazy">
          <v-card-text>
            <v-row dense class="my-4">
              <v-col cols="12">
                <v-img class="float-left" src="@/assets/ICON/Buyer.png" contain width="60px" height="60px"></v-img>
                <v-card-title style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">รายชื่อผู้ซื้อ</v-card-title>
              </v-col>
            </v-row>
            <v-row justify="center" align-content="center" :class="MobileSize ? '' : 'px-12'">
              <v-col cols="12" :class="MobileSize ? '' : 'px-12'">
                <v-card outlined >
                  <div class="d-flex flex-no-wrap">
                    <v-avatar class="ma-4" :size="MobileSize ? '60' : '132'" tile>
                      <v-img :src="editBuyerItem.img_path" contain style="border-radius: 8px;" v-if="editBuyerItem.img_path !== null "></v-img>
                      <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                    </v-avatar>
                    <div class="ma-4">
                      <p>ชื่อ-สกุล : <b>{{ editBuyerItem.name }}</b></p>
                      <p>อีเมล : <b>{{ editBuyerItem.email }}</b></p>
                      <p>เบอร์โทรศัพท์ : <b>{{ editBuyerItem.phone }}</b></p>
                      <!-- <p class="mb-0">ตำแหน่ง : <b>{{ editBuyerItem.username }}</b></p> -->
                      <p class="mb-0">รูปแบบการอนุมัติ <span style="color: red;">*</span> :
                        <v-select
                          v-model="select"
                          :items="listTypeApprove"
                          item-text="name"
                          item-value="approve_position_id"
                          placeholder="กรุณาเลือก"
                          :style="MobileSize ? 'width: 190px !important;' : ''"
                          dense
                          outlined
                          class="mt-2"
                          :rules="Rules.selectType"
                        ></v-select>
                      </p>
                    </div>
                  </div>
                </v-card>
              </v-col>
            </v-row>
            <v-row cols="12" :class="MobileSize ? 'mt-10 pb-2' : 'px-12 mt-10 pb-2'">
              <v-col cols="12" :class="MobileSize ? '' : 'px-12 pt-0'" :align="MobileSize ? 'center' : 'end'">
                <v-btn outlined class="mr-2" color="#27AB9C" @click="CloseModalEditBuyer()">ยกเลิก</v-btn>
                <v-btn class="white--text" color="#27AB9C" @click="confirmSettingAdmin('editBuyerApprove', '')">บันทึก</v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- Modal ยืนยันการเพิ่ม/แก้ไข/ลบ ผู้ซื้อ -->
    <v-dialog v-model="modalConfirmBuyerApprove" width="464px" persistent>
      <v-card width="100%" height="100%" min-height="246px" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px" v-if="statusSitting === 'addBuyerApprove' || statusSitting === 'editBuyerApprove'">
            <font color="#27AB9C">การตั้งค่ารายละเอียดผู้ซื้อ</font>
          </span>
          <span class="flex text-center ml-5" style="font-size:20px" v-else>
            <font color="#27AB9C">ลบผู้ซื้อ</font>
          </span>
          <v-btn icon dark @click="CloseModalConfirmBuyer()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row justify="center" no-gutters dense v-if="statusSitting === 'addBuyerApprove'">
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;">คุณได้ทำการตั้งค่ารายละเอียดผู้ซื้อ</p>
              </v-col>
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">คุณต้องการทำรายการนี้ ใช่ หรือไม่</p>
              </v-col>
            </v-row>
            <v-row justify="center" no-gutters dense v-else-if="statusSitting === 'editBuyerApprove'">
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;">คุณได้ทำการแก้ไข การตั้งค่ารายละเอียดผู้ซื้อ</p>
              </v-col>
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">คุณต้องการทำรายการนี้ ใช่ หรือไม่</p>
              </v-col>
            </v-row>
            <v-row justify="center" no-gutters dense v-else>
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;">คุณได้ทำการลบผู้ซื้อ</p>
              </v-col>
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">คุณต้องการทำรายการนี้ ใช่ หรือไม่</p>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row justify="center" class="mb-4">
              <v-btn class="mr-4" color="#27AB9C" dark outlined style="border: 1px solid #27AB9C; border-radius: 8px;" @click="CloseModalConfirmBuyer('add')" v-if="statusSitting === 'addBuyerApprove'">ยกเลิก</v-btn>
              <v-btn class="mr-4" color="#27AB9C" dark outlined style="border: 1px solid #27AB9C; border-radius: 8px;" @click="CloseModalConfirmBuyer('edit')" v-else-if="statusSitting === 'editBuyerApprove'">ยกเลิก</v-btn>
              <v-btn class="mr-4" color="#27AB9C" dark outlined style="border: 1px solid #27AB9C; border-radius: 8px;" @click="CloseModalConfirmBuyer('delete')" v-else>ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark style="border: 1px solid #27AB9C; border-radius: 8px;" @click="confirm('add')" v-if="statusSitting === 'addBuyerApprove'">ตกลง</v-btn>
              <v-btn color="#27AB9C" dark style="border: 1px solid #27AB9C; border-radius: 8px;" @click="confirm('edit')" v-else-if="statusSitting === 'editBuyerApprove'">ตกลง</v-btn>
              <v-btn color="#27AB9C" dark style="border: 1px solid #27AB9C; border-radius: 8px;" @click="confirm('delete')" v-else>ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- modal Success การเพิ่ม/แก้ไข/ลบ ผู้ซื้อ -->
    <v-dialog v-model="dialogSuccess" persistent width="373">
      <v-card style="background: #FFFFFF; border-radius: 4px;" min-height="246px">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title>
                <span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''" v-if="statusSitting === 'addBuyerApprove' || statusSitting === 'editBuyerApprove'"><b>การตั้งค่ารายละเอียดผู้ซื้อ</b></span>
                <span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''" v-else><b>ลบผู้ซื้อ</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeModalComfirm()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row justify="center" no-gutters dense>
              <v-col cols="12" align="center">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
              </v-col>
              <v-col cols="12" align="center" class="mt-6">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">เสร็จสิ้น</p>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      showTable: false,
      showCountOrder: 0,
      search: '',
      dataRole: '',
      headersListBuyer: [
        { text: 'รูปแบบการอนุมัติ', value: 'appove_position_name', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-นามสกุล', value: 'name', sortable: false, align: 'start', width: '160', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทรศัพท์', value: 'phone', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' }
      ],
      page: 1,
      DataTable: [],
      Rules: {
        selectType: [
          v => !!v || 'กรุณาเลือกข้อมูล'
        ]
      },
      modalAddBuyerApprove: false,
      modalEditBuyerApprove: false,
      searchBuyer: '',
      notfoundBuyer: Boolean,
      clickSelectBuyer: false,
      selectBuyerItem: [],
      searchBuyerList: [],
      editBuyerItem: [],
      deleteBuyerItem: [],
      name: '',
      select: '',
      lazy: false,
      modalConfirmBuyerApprove: false,
      statusSitting: '',
      dialogSuccess: false,
      listBuyer: [],
      listTypeApprove: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      //   this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
      this.getListBuyerData()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    filteredList () {
      return this.listBuyer.filter(listBuyer => {
        return listBuyer.name.toLowerCase().includes(this.searchBuyer.toLowerCase()) || listBuyer.email.toLowerCase().includes(this.searchBuyer.toLowerCase()) || listBuyer.phone.toLowerCase().includes(this.searchBuyer.toLowerCase())
      })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageBuyerApproveMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageBuyerApprove' }).catch(() => {})
      }
    },
    searchBuyer (val) {
      // console.log('value =====>', val)
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async addBuyerApprove () {
      this.$store.commit('openLoader')
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        company_id: dataCompany,
        com_perm_id: companyDataSet.position.com_perm_id
      }
      await this.$store.dispatch('actionsListSelectPurchaser', data)
      var response = await this.$store.state.ModuleApprove.stateListSelectPurchaser
      // console.log(response.data)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listBuyer = await response.data.list_select_purchaser
        // this.listTypeApprove = await response.data.list_approve_position
        this.notfoundBuyer = false
        this.clickSelectBuyer = false
        this.searchBuyer = ''
        this.select = ''
        this.modalAddBuyerApprove = !this.modalAddBuyerApprove
      }
    },
    selectBuyer (item) {
      this.selectBuyerItem = item
      this.clickSelectBuyer = true
    },
    CloseModalAddBuyer (val) {
      if (val === 'close') {
        this.modalAddBuyerApprove = !this.modalAddBuyerApprove
        this.searchBuyer = ''
        this.select = ''
        this.notfoundBuyer = null
        this.clickSelectBuyer = false
      } else if (val === 'back') {
        this.searchBuyer = ''
        this.clickSelectBuyer = false
        this.select = ''
      }
    },
    editBuyer (item) {
      // console.log(item)
      this.editBuyerItem = item
      this.select = item.appove_position_id
      this.modalEditBuyerApprove = !this.modalEditBuyerApprove
    },
    CloseModalEditBuyer () {
      this.modalEditBuyerApprove = !this.modalEditBuyerApprove
    },
    CloseModalConfirmBuyer (val) {
      if (val === 'add') {
        this.searchBuyer = ''
        this.select = ''
        this.notfoundBuyer = null
        this.clickSelectBuyer = false
        this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
      } else {
        this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
      }
    },
    async getListBuyerData () {
      this.$store.commit('openLoader')
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        company_id: dataCompany,
        com_perm_id: companyDataSet.position.com_perm_id
      }
      await this.$store.dispatch('actionsListPurchaser', data)
      var response = await this.$store.state.ModuleApprove.stateListPurchaserHaveApprovePosition
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DataTable = response.data.approve_position_purchaser_data
        this.listTypeApprove = await response.data.list_approve_position
        if (this.DataTable.length !== 0) {
          this.showTable = true
        } else {
          this.showTable = false
        }
      } else {
        this.$store.commit('closeLoader')
        this.DataTable = []
      }
    },
    confirmSettingAdmin (status, item) {
      this.statusSitting = status
      if (this.statusSitting === 'addBuyerApprove' || this.statusSitting === 'editBuyerApprove') {
        if (this.$refs.FormSetBuyerApprove.validate(true)) {
          if (this.statusSitting === 'addBuyerApprove') {
            this.modalAddBuyerApprove = !this.modalAddBuyerApprove
          } else {
            this.modalEditBuyerApprove = !this.modalEditBuyerApprove
          }
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
        } else {
          this.$nextTick(() => {
            const el = document.getElementsByClassName('error--text')
            if (el) {
              document
                .getElementsByClassName('error--text')[0]
                .scrollIntoView({ behavior: 'smooth', block: 'end' })
            }
          })
        }
      } else {
        this.deleteBuyerItem = item
        this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
      }
    },
    async confirm (val) {
      this.$store.commit('openLoader')
      var data = ''
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      if (val === 'add') {
        data = {
          company_id: dataCompany,
          com_perm_id: companyDataSet.position.com_perm_id,
          approve_position_id: this.select,
          select_user_id: this.selectBuyerItem.user_id
        }
        // console.log(data)
        await this.$store.dispatch('actionsSettingPurchaser', data)
        var responseAdd = await this.$store.state.ModuleApprove.stateSettingPurchaserApprovePosition
        if (responseAdd.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
          this.dialogSuccess = !this.dialogSuccess
        } else {
          this.$store.commit('closeLoader')
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
          this.$swal.fire({ icon: 'error', text: `${responseAdd.message}`, showConfirmButton: false, timer: 1500 })
        }
      } else if (val === 'edit') {
        data = {
          company_id: dataCompany,
          com_perm_id: companyDataSet.position.com_perm_id,
          approve_position_id: this.select,
          select_user_id: this.editBuyerItem.user_id
        }
        await this.$store.dispatch('actionsSettingPurchaser', data)
        var responseEdit = await this.$store.state.ModuleApprove.stateSettingPurchaserApprovePosition
        if (responseEdit.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
          this.dialogSuccess = !this.dialogSuccess
        } else {
          this.$store.commit('closeLoader')
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
          this.$swal.fire({ icon: 'error', text: `${responseEdit.message}`, showConfirmButton: false, timer: 1500 })
        }
      } else {
        data = {
          company_id: dataCompany,
          com_perm_id: companyDataSet.position.com_perm_id,
          approve_position_id: this.deleteBuyerItem.appove_position_id,
          select_user_id: this.deleteBuyerItem.user_id
        }
        await this.$store.dispatch('actionsDeletePurchaser', data)
        var responseDelete = await this.$store.state.ModuleApprove.stateDeletePurchaserApprovePosition
        if (responseDelete.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
          this.dialogSuccess = !this.dialogSuccess
        } else {
          this.$store.commit('closeLoader')
          this.modalConfirmBuyerApprove = !this.modalConfirmBuyerApprove
          this.$swal.fire({ icon: 'error', text: `${responseDelete.message}`, showConfirmButton: false, timer: 1500 })
        }
      }
    },
    async closeModalComfirm () {
      this.dialogSuccess = !this.dialogSuccess
      await this.getListBuyerData()
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(5) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(5) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
