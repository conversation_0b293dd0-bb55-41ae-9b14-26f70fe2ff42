<template>
<v-card width="100%" elevation="0">
    <v-card-text class="px-1">
    <v-row>
  <v-col cols="12">
    <!-- <v-btn @click="setIconData">55555</v-btn> -->
    <!-- <img src="./info.jpg" /> -->
   <table class="display nowrap" id="example" cellpadding="0" cellspacing="0" style="width:100%">
     <thead>
        <tr>
          <th class="fontData">
            ลำดับ
          </th>
          <th class="fontData">
            รหัสร้านค้า
          </th>
          <th class="fontData">
            ร้านค้า
          </th>
          <th class="fontData">
            เลขที่ทำรายการชำระเงิน
          </th>
          <th class="fontData">
            วันที่ชำระเงิน
          </th>
          <th class="fontData">
            ราคาสุทธิ
          </th>
          <th class="fontData">
            กำไรของระบบ
          </th>
        </tr>
      </thead>
    <tbody>
      <tr v-for="(item, index) in dataResponse" :key="index">
        <td >{{item.num}}</td>
        <td >{{item.seller_shop_id}}</td>
        <td >{{item.shop_name }}</td>
        <td >{{item.payment_transaction_id}}</td>
        <td >{{item.paid_datetime }}</td>
        <td >{{item.total_amount}}</td>
        <td >{{item.total_price_vat_with_gp}}</td>
      </tr>
    </tbody>
  </table>
  </v-col>
  </v-row>
    </v-card-text>
  </v-card>
</template>
<script>
// import 'jquery/dist/jquery.min.js'
// import { jquery, jqDatatable, dataTableBTN, jszip, pdf, vfsFonts, html5, print } from '../library/dataTableNet.js'
// import dataMap from '@/components/library/TestTable.json'
// import { Decode } from '@/services'
import 'datatables.net'
import 'datatables.net-dt/css/jquery.dataTables.min.css'
import 'jszip'
import 'datatables.net-buttons-dt'
import 'datatables.net-buttons-dt/css/buttons.dataTables.min.css'
import 'datatables.net-buttons/js/buttons.colVis'
import 'datatables.net-buttons/js/buttons.flash'
import 'datatables.net-buttons/js/buttons.html5'
import 'datatables.net-buttons/js/buttons.print'
import 'datatables.net-buttons/js/dataTables.buttons'
import 'datatables.net-responsive-dt'
import $ from 'jquery'
window.JSZip = require('jszip')
export default {
  data: () => ({
    loading: false,
    dataRes: [],
    dataMobile: [],
    toDay: new Date().toISOString().slice(0, 10),
    Day: `${new Date().toISOString().slice(0, 7)}-01`,
    // dataMain: dataMap,
    search: '',
    headerProps: {
      sortByText: 'เรียงตาม'
    },
    isMobile: false,
    selectDataForm: ''
  }),
  beforeCreate () {
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$on('tableReload', this.tableReload)
  },
  beforeMount () {
  },
  mounted () {
    this.init()
    this.setIconData()
  },
  beforeUpload () {
  },
  updated () {
  },
  destroyed () {
    this.$EventBus.$off('tableReload')
  },
  computed: {
    isMobile () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dataResponse () {
      return this.$store.state.ModuleAdminManage.stateGPTable.map((x, i) => {
        return {
          num: i + 1,
          // paid_datetime: x.paid_datetime !== '' ? new Date(x.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '',
          paid_datetime: x.paid_datetime !== '' ? this.formatDateToShow(x.paid_datetime.substr(0, 10)) : '',
          payment_transaction_id: x.payment_transaction_id + '#' + x.link_pdf,
          seller_shop_id: x.seller_shop_id,
          shop_name: x.shop_name,
          total_amount: x.total_amount,
          total_price_vat_with_gp: x.total_price_vat_with_gp
        }
      })
    }
  },
  methods: {
    // onResize () {
    //   if (window.innerWidth < 650) {
    //     this.isMobile = true
    //   } else {
    //     this.isMobile = false
    //   }
    // },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    async init () {
      await setTimeout(function () {
        $('#example').DataTable({
          dom: 'Bfrtip',
          destroy: true,
          responsive: {
            details: {
              display: $.fn.dataTable.Responsive.display.childRowImmediate
            }
          },
          // responsive: true,
          orderCellsTop: true,
          fixedHeader: true,
          language: {
            Search: '',
            searchPlaceholder: 'ค้นหา',
            emptyTable: 'ไม่มีข้อมูลกำไรเบื้องต้นในตาราง',
            zeroRecords: 'ไม่พบข้อมูลกำไรเบื้องต้นในตาราง'
          },
          oLanguage: {
            sSearch: '',
            searchPlaceholder: ''
          },
          columnDefs: [{
            targets: 3,
            data: 'payment_transaction_id',
            render: function (data, type, row, meta) {
              var arr = data.split('#')
              return '<a href="' + arr[1] + '"  target="_blank">' + arr[0] + '</a>'
            }
          }],
          columns: [
            { data: 'num' },
            { data: 'seller_shop_id' },
            { data: 'shop_name' },
            { data: 'payment_transaction_id' },
            { data: 'paid_datetime' },
            { data: 'total_amount' },
            { data: 'total_price_vat_with_gp' }
          ],
          buttons: [
            {
              extend: 'csvHtml5',
              text: '<i class="fa fa-file"></i> Csv',
              charset: 'utf-8',
              extension: '.csv',
              filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
              bom: true
            },
            {
              extend: 'copy',
              text: '<i class="fa fa-copy"></i> Copy',
              titleAttr: 'Copy'
            },
            {
              extend: 'excel',
              text: ' <i class="fa fa-file-excel"></i> Excel',
              exportOptions: {
                format: {
                  body: function (data, row, column, node) {
                    var cellData = column === 3 ? data.split('>')[1].substring(0, data.split('>')[1].length - 3) : data
                    // var subCell = cellData.indexOf('<') < 0 ? cellData : $(cellData).text()
                    // console.log('subCell', cellData)
                    return column === 3 ? '\u200C' + cellData : cellData
                  }
                }
              }
            },
            {
              extend: 'print',
              text: '<i class="fa fa-print"></i> Print',
              titleAttr: 'Print'
            }
          ]
        }
        )
      }, 500)
    },
    setIconData () {
      $('#help').remove()
      setTimeout(function () {
        var img = require('@/assets/icon_image/infoGP.png')
        $('#example_filter').append('<i id="help" class="bi bi-exclamation-circle fa-lg ml-2" style="color: #27AB9C;"></i>')
        $(`<div class="hide" style="position: absolute; z-index: 1; margin-left: 20vw" ><img src="${img}" width="182px" height="206px"/></div>`).appendTo('#example_filter')
        $('.hide').hide()
        $('#help').hover(function () { $('.hide').show() }, function () { $('.hide').hide() })
      }, 500)
    },
    async tableReload () {
      // $('#help').remove()
      // console.log('เข้าreloadDataTable')
      // await this.$store.dispatch('actionsAllGP', datasent)
      // const { data = {} } = await this.$store.state.ModuleAdminManage.stateAllGP
      // this.dataRes = await data.transferredd_data.map((x, i) => {
      //   return {
      //     num: i + 1,
      //     paid_datetime: x.paid_datetime !== '' ? new Date(x.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) : '',
      //     payment_transaction_id: x.payment_transaction_id,
      //     seller_shop_id: x.seller_shop_id,
      //     shop_name: x.shop_name,
      //     total_amount: x.total_amount,
      //     link_pdf: x.link_pdf,
      //     total_price_vat_with_gp: x.total_price_vat_with_gp
      //   }
      // })
      // this.dataRes = await [{ created_at: '2022-06-21 12:06:54', payment_transaction_number: '220621000000000010', total_amount: '2165', paid_datetime: '22025-05-10 00:00:00', name_th: null, quatation_id: '' }]
      var table = await $('#example').DataTable({
        dom: 'Bfrtip',
        destroy: true,
        responsive: {
          details: {
            display: $.fn.dataTable.Responsive.display.childRowImmediate
          }
        },
        // responsive: true,
        language: {
          Search: '',
          searchPlaceholder: 'ค้นหา',
          emptyTable: 'ไม่มีข้อมูลกำไรเบื้องต้นในตาราง',
          zeroRecords: 'ไม่พบข้อมูลกำไรเบื้องต้นในตาราง'
        },
        oLanguage: {
          sSearch: '',
          searchPlaceholder: ''
        },
        columnDefs: [{
          targets: 3,
          data: 'payment_transaction_id',
          render: function (data, type, row, meta) {
            var arr = data.split('#')
            return '<a href="' + arr[1] + '"  target="_blank">' + arr[0] + '</a>'
          }
        }],
        // columnDefs: [
        //   {
        //     targets: [5],
        //     visible: false,
        //     searchable: false
        //   }
        // ],
        // columns: [
        //   { data: 'created_at' },
        //   {
        //     data: 'payment_transaction_number',
        //     render: function (data, type, row) {
        //       return type === 'export' ? data.toString() : data.toString()
        //     }
        //   },
        //   { data: 'quatation_id' },
        //   { data: 'name_th' },
        //   { data: 'total_amount' },
        //   { data: 'paid_datetime' }
        // ],
        columns: [
          { data: 'num' },
          { data: 'seller_shop_id' },
          { data: 'shop_name' },
          { data: 'payment_transaction_id' },
          { data: 'paid_datetime' },
          { data: 'total_amount' },
          { data: 'total_price_vat_with_gp' }
        ],
        buttons: [
          {
            extend: 'csv',
            text: '<i class="fa fa-file"></i> Csv',
            charset: 'utf-8',
            extension: '.csv',
            filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            bom: true
          },
          {
            extend: 'copy',
            text: '<i class="fa fa-copy"></i> Copy',
            titleAttr: 'Copy'
          },
          {
            extend: 'excel',
            text: ' <i class="fa fa-file-excel"></i> Excel',
            exportOptions: {
              format: {
                body: function (data, row, column, node) {
                  var cellData = column === 3 ? data.split('>')[1].substring(0, data.split('>')[1].length - 3) : data
                  // var subCell = cellData.indexOf('<') < 0 ? cellData : $(cellData).text()
                  // console.log('subCell', cellData)
                  return column === 3 ? '\u200C' + cellData : cellData
                }
              }
            }
          },
          {
            extend: 'print',
            text: '<i class="fa fa-print"></i> Print',
            titleAttr: 'Print'
          }
        ]
      })
      await table.clear().rows.add(this.dataResponse).draw()
    }
  }
}
</script>
<style scoped="scss">
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.3/font/bootstrap-icons.css");

@media only screen and (max-width: 650px) {
  .table-striped {
    display: none;
  }
  ::v-deep .dataTables_paginate.paging_simple_numbers {
    display: none;
  }
  ::v-deep #example_filter.dataTables_filter {
    display: none;
  }
}
@media only screen and (max-width: 750px) {
  li.paginate_button.previous {
    display: inline;
  }
  li.paginate_button.next {
    display: inline;
  }
  li.paginate_button {
    display: none;
  }
}
@media only screen and (min-width: 750px) {
  ::v-deep .mobile {
    display: none;
  }

}
::v-deep #example > thead > tr:nth-child(2) > th {
  background-color: white;
}
::v-deep #example_info {
  display: none;
}
::v-deep .dataTables_filter {
  margin-bottom: 1em;
}
::v-deep .dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    width: 500px;
    background-color: transparent;
    margin-left: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABmJLR0QA/wD/AP+gvaeTAAABs0lEQVQ4ja3TTWsTURjF8f+ZTMUwLV10VSgVVBhDt4KrutB2aYVgBqSC4LQ7v4IbBf0KlmYErYJMJpZ+h4KLrgOm1IVvq+IqaqPNzOMiddFwk2L1LO9z+XEuPBf+UzRsEG4lE6XDfDYv9KPN5EeiKP8rKMySy5g9BBYB/+h436CuM3rSvhl3XJB3DGkky5htA188z7vy81up7BtTSPdl3LBf9rbSXJse2eioyTYobtfiV4MXZ9K0HNDZBBtv1+J5JHM3MnsEbLgQgM9RdCDfX0bMhVl9yfm0cCuZABZk9tSF/Mm76t2vgoag6oRKh/ks4He/+61RUL84LUPnnVBP/gHA2UkvOAmSFMis64R28/EPwL4VvesnNsKuFdKOEyKKckkJhT2YSdPyMKSSri8CV8eMZ24IsDEeS3gBnc1Lb55PuZBCeg2WtqKVvcH5sc2uNNemi7zUQMyZlIqiJVNgaAFsHiwF1ZDF7VurL4dC/WqmMKsvCapmuoDoStopFZa0opW9MFu/gykZxIZ+2lEJsyTCbMPQ6m4tfnFqqI/1m/V63sX3t+99Oq0DQKVZP/dPgCu/AanyqVoXnFHTAAAAAElFTkSuQmCC) !important;
    background-position: 470px 10px !important;
    outline: transparent;
    border: 1px solid #cacaca;
    border-radius: 6px;
  }
  ::v-deep .input {
    display: inline-block;
    white-space: nowrap;
  }
  ::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
}
::v-deep #example {
  padding-top: 12px;
}
::v-deep .sorting {
  color: #e6f5f3;
  background-color: #e6f5f3;
}
::v-deep table.dataTable tbody td.sorting_1 {
  text-align: center;
}
::v-deep #example > thead > tr > th {
  background-color: #e6f5f3;
  color: #27AB9C;
  font-size: 12px;
}
::v-deep table.dataTable th, table.dataTable td  {
  border-bottom: 1px solid #e7e7e7;
}
::v-deep div.dt-buttons {
  float: right;
 }
 ::v-deep .dataTables_wrapper .dataTables_filter  {
  float: left;
 }
  .fontData {
    font-size: 14px;
  }
  .tableLong {
    width: 200px;
  }
  .container {
    margin-right: 25px;
  }
  .sec {
    width: 30vw !important;
  }
  .dot {
    display: inline-block;
    width: 80px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
.hiDe {
  display: none;
}
.dot2 {
    display: inline-block;
    width: 120px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  top: 50%;
  left: 5px;
  height: 1em;
  width: 1em;
  margin-top: -9px;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control {
  padding-left: 27px;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control:before {
  left: 4px;
  height: 14px;
  width: 14px;
  border-radius: 14px;
  line-height: 14px;
  text-indent: 3px;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control,
table.dataTable.dtr-column > tbody > tr > th.dtr-control,
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  top: 50%;
  left: 50%;
  height: 0.8em;
  width: 0.8em;
  margin-top: -0.5em;
  margin-left: -0.5em;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable > tbody > tr.child {
  padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}
table.dataTable > tbody > tr.child ul.dtr-details {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
  padding-top: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
  border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}
div.dtr-modal {
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 50%;
  overflow: auto;
  margin: auto;
  z-index: 102;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: 0.5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 1em;
}
div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  border: 1px solid #eaeaea;
  background-color: #f9f9f9;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}
div.dtr-modal div.dtr-modal-close:hover {
  background-color: #eaeaea;
}
div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.6);
}

@media screen and (max-width: 567px) {
  div.dtr-modal div.dtr-modal-display {
    width: 95%;
  }
}

</style>
