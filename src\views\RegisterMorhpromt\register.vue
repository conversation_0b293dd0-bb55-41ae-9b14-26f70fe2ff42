<template>
  <v-app id="inspire" class="backgroundPage">
    <v-app-bar
      absolute
      app
      dark
      color="#27AB9C"
      elevate-on-scroll
      scroll-target="#scrolling-techniques-7"
    >
      <v-toolbar-title :style="{ 'padding-left': MobileSize ? '0px': '3%' }">
        <v-img :src="require('@/assets/inet_logo.png')" contain  class="mr-5"  max-height="65" max-width="150" v-if="!MobileSize"/>
        <v-img :src="require('@/assets/inet_logo.png')" contain  max-height="65" max-width="100" v-else/>
      </v-toolbar-title>
    </v-app-bar>
    <v-main>
      <v-container fluid>
        <router-view></router-view>
      </v-container>
    </v-main>
    <Footer />
  </v-app>
</template>

<script>
export default {
  components: {
    Footer: () => import(/* webpackPrefetch: true */ '@/components/Home/FooterUI')
  },
  data: () => ({
    drawer: true,
    selectedItem: 0,
    titleList: 'บริษัท',
    listMenu: [
      { key: 0, title: 'รายละเอียดของ', icon: 'mdi-information', path: '/detailCompany' },
      { key: 1, title: 'แผนก', icon: 'mdi-file-tree', path: '/departmentsCompany' },
      { key: 2, title: 'ผู้ใช้งาน', icon: 'mdi-account-circle', path: '/usersCompany' },
      { key: 3, title: 'รายชื่อคู่ค้าองค์กร', icon: 'mdi-account-group', path: '/partnerCompany' },
      { key: 4, title: 'รายการสั่งซื้อทั้งหมด', icon: 'mdi-file-document-multiple', path: '/orderCompany' }
    ],
    CompanyName: '',
    userdetail: '',
    username: '',
    fullname: ''
  }),
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  watch: {
  },
  async created () {
  },
  methods: {
  }
}
</script>

<style scoped>
.theme--dark .v-bar--underline.theme--light, .theme--light .v-bar--underline.theme--light {
  border-bottom-color: rgba(0,0,0,.12)!important;
}
.backgroundPage{
  background-color: #F6F6F6;
}
</style>
