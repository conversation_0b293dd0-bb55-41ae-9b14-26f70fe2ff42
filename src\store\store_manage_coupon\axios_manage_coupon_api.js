import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async createCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/create_couponV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async categoryShopList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/list_product_by_category`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/edit_couponV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getDetailCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/get_detail_coupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async changeStatusCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/change_coupon_status`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async changeDeleteCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/delete_couponV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListProductInShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/list_product_by_categoryV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
