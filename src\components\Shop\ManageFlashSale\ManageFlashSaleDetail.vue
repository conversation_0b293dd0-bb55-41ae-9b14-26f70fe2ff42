<template>
  <v-container grid-list-xs rounded :class="MobileSize ? 'background_color_Mobile' : 'background_color'">
    <v-row :justify="MobileSize ? 'start' : 'space-between'" :class="MobileSize ? 'pt-3 pb-3 px-3' : 'pt-3 px-3'">
      <v-col :cols="MobileSize ? 12 : 6">
        <span v-if="MobileSize" class="pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
          <v-icon style="color: #2A70C3;" @click="back()">mdi-chevron-left</v-icon> รายละเอียดแฟลชเซลล์
        </span>
        <span v-else class="pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
          รายละเอียดแฟลชเซลล์
        </span>
      </v-col>
      <v-col cols="12" md="6" v-if="dataList.type === 'starting' || dataList.type === 'countdown'" align="end">
        <v-btn :style="MobileSize ? 'width: 100%' : 'width: 168px'" @click="editFlashSale(dataList)" style="height: 42px;" class="my-auto white--text" dense rounded dark color="#2A70C3"><v-icon small left>mdi-pencil</v-icon>แก้ไขรายละเอียด</v-btn>
      </v-col>
      <v-col cols="12" md="6" v-else-if="dataList.type === 'end'" align="end">
        <v-btn @click="exportFlashSale()" :class="MobileSize ? 'mb-3' : 'ml-2 d-inline-block'" style="height: 42px;" outlined dense rounded dark color="#2A70C3" :style="MobileSize ? 'width: 100%' : 'width: 240px'"><v-icon left>mdi-arrow-up</v-icon>Export ข้อมูลแฟลชเซลล์</v-btn>
        <v-btn @click="repeatCreateFlashSale(dataList)" :class="MobileSize ? '' : 'ml-2 d-inline-block'" style="height: 42px;" class="white--text" dense rounded dark color="#2A70C3" :style="MobileSize ? 'width: 100%' : 'width: 240px'"><v-icon left>mdi-repeat</v-icon>สร้างแฟลชเซลล์เดิมอีกครั้ง</v-btn>
      </v-col>
    </v-row>
    <v-row justify="center" align="center">
      <v-col cols="12" class="mb-3 px-2" >
        <v-card elevation="0" style="background-color: transparent; border-radius: 8px;">
          <v-card-text :class="MobileSize ? 'pt-0' : ''">
            <v-row :class="MobileSize ? '' : 'mt-6'">
              <!-- วันเวลาเริ่มต้น - สิ้นสุด -->
              <v-col cols="12" align="start">
                <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="font-weight: 500; color: #333333;">วันเวลาเริ่มต้น - สิ้นสุด :
                  <span v-if="dataList.start_date" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="font-weight: 700; color: #2A70C3;">
                    {{ formatDateFlashSale(dataList.start_date) }} น. - {{ formatDateFlashSale(dataList.end_date) }} น.
                  </span>
                </span>
              </v-col>
              <!-- ตัวนับเวลาถอยหลังก่อนเริ่มแฟลชเซลล์ -->
              <v-col cols="12" align="start">
                <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="font-weight: 600; color: #333333;">ตัวนับเวลาถอยหลังก่อนเริ่มแฟลชเซลล์ :
                  <span v-if="dataList.countdown_date">{{ formatDateFlashSale(dataList.countdown_date) }} น.</span>
                </span>
              </v-col>
              <!-- Banner FlashSale -->
              <v-col cols="12" align="center">
                <v-card width="100%" :height="MobileSize ? '100px' : '300px'" elevation="0">
                  <v-img :src="dataList.image_path" v-if="dataList.image_path !== null" width="100%" height="100%" contain></v-img>
                  <v-img src="@/assets/NoImage.png" v-else width="100%" height="266px" contain></v-img><br />
                </v-card>
              </v-col>
              <!-- Count Down FlashSale -->
              <v-col v-if="MobileSize && flashSaleProductList.length !== 0" cols="12" md="8" sm="8" class="px-0 mt-2">
                <span class="my-auto" style="font-weight: 700; font-size: 18px; color: #333333;">{{ dataList.type === 'starting' || dataList.type === 'end' ? 'จบภายใน' : 'เริ่มภายใน' }} </span>
                <CountdownDate :endDate="countDownDate(dataList)" page="detail"/>
                <!-- <CountdownDateEnd v-else :endDate="countDownDate(dataList)"/> -->
              </v-col>
              <v-col v-else-if="!MobileSize && flashSaleProductList.length !== 0" cols="12" md="8" sm="8" class="d-flex mt-2">
                <span class="mr-2 my-auto" style="font-weight: 700; font-size: 18px; color: #333333;">{{ dataList.type === 'starting' || dataList.type === 'end' ? 'จบภายใน : ' : 'เริ่มภายใน : ' }} </span>
                <CountdownDate :endDate="countDownDate(dataList)" page="detail"/>
                <!-- <CountdownDateEnd v-else :endDate="countDownDate(dataList)"/> -->
              </v-col>
              <v-col cols="12" md="4" sm="4" v-if="!MobileSize"></v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <!-- list Product FlashSale -->
    <v-row justify="center" align="center" class="px-3 mt-0 mb-4" v-if="flashSaleProductList.length !== 0">
      <v-row justify="center">
        <v-col cols="12">
          <span class="pl-3" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="line-height: 24px; align-items: center; color: #333333; font-weight: 600">
            รายการสินค้าที่เข้าร่วมทั้งหมด {{ countProduct }} รายการ
          </span>
        </v-col>
        <!-- <v-col cols="12" v-if="flashSaleProductList.length !== 0"> -->
          <v-container id="cardProduct" style="max-width: 1250px;">
            <v-row>
              <v-col align="center" v-for="(c, index) in paginated" :key="index" :class="MobileSize ? 'custom2cols pa-1' : IpadSize ? 'custom3cols pa-2' : IpadProSize ? 'custom4cols pa-2' : 'custom5cols pa-2'">
                <CardProductsFlashSale :itemProduct='c' pageCheck="detail" align="start"/>
              </v-col>
            </v-row>
          </v-container>
        <!-- </v-col> -->
        <v-col cols="12" v-if="flashSaleProductList.length !== 0" class="mt-2">
          <v-pagination color="#2A70C3" v-model="page" :length="checkLengthPagination()" @input="slideToTop()" :total-visible="10"></v-pagination>
        </v-col>
      </v-row>
    </v-row>
    <!-- Empty list Product FlashSale -->
    <v-row justify="center" align-content="center" v-else class="mt-6">
      <v-col cols="12" md="12" align="center">
        <!-- <div class="my-5">
          <v-img src="@/assets/NSG/FlashSale/empty-flash-sale.png" max-height="350px" max-width="350px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
        </div> -->
        <h2 style="padding-top: 10px; padding-bottom: 50px; color: #9A9A9A;">
          <span style="font-weight: 400; font-size: 18px; line-height: 22px;">ไม่มีรายการสินค้าแฟลชเซลล์</span><br/>
        </h2>
      </v-col>
    </v-row>
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64" color="#2A70C3"></v-progress-circular>
    </v-overlay>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
import { msgErr, statusErr } from '@/enum/GetError'
export default {
  components: {
    CardProductsFlashSale: () => import('@/components/NSG/Shop/ManageFlashSale/FlashSaleItem/'),
    CountdownDate: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CountdownDate')
    // CountdownDateEnd: () => import('@/components/NSG/Shop/ManageFlashSale/FlashSaleItem/CountdownDateEnd')
  },
  data () {
    return {
      overlay: false,
      countProduct: 0,
      pageMax: null,
      current: 1,
      pageSize: 3,
      itemPerPage: 15,
      dataList: {},
      flashSaleProductList: [],
      flashSaleId: ''
    }
  },
  computed: {
    checkWidth () {
      return window.screen.width
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    page: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
      }
    },
    indexStart () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return (this.current - 1) * pageSize
    },
    indexEnd () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return this.indexStart + pageSize
    },
    paginated () {
      return this.flashSaleProductList.slice(this.indexStart, this.indexEnd)
    }
  },
  watch: {
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    }
  },
  mounted () {
    this.$EventBus.$on('getNewDataFlashSaleDetail', this.getData)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getNewDataFlashSaleDetail')
    })
  },
  async created () {
    this.$EventBus.$emit('SelectPath')
    this.flashSaleId = this.$route.query.flashSaleID
    window.addEventListener('storage', function (event) {
      if (event.key === 'authNSG' && !event.newValue) {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    })
    if (localStorage.getItem('authNSG') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    await this.getData()
  },
  methods: {
    slideToTop () {
      var elmnt = document.getElementById('cardProduct')
      window.scrollTo({ top: (window.scrollY - elmnt.offsetHeight), left: 0, behavior: 'smooth' })
    },
    back () {
      this.$EventBus.$emit('openNavBar')
    },
    checkLengthPagination () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return Math.ceil(this.flashSaleProductList.length / pageSize)
    },
    kFormatter (num) {
      return Math.abs(num) > 999 ? Math.sign(num) * ((Math.abs(num) / 1000).toFixed(1)) + 'k' : Math.sign(num) * Math.abs(num)
    },
    countDownDate (item) {
      // type = "starting" => ให้แสดงคำว่า จบภายใน แล้วนับถอยหลังจากปัจจุบันไปถึง end_date
      // type = "countdown" => ให้แสดงคำว่า เริ่มภายใน แล้วนับถอยหลังจากปัจจุบันไปถึง start_date
      if (item.type === 'starting') {
        return Date.parse(item.end_date)
      } else {
        return Date.parse(item.start_date)
      }
    },
    async getData () {
      this.$store.commit('openLoader')
      var data = {
        flashsale_id: this.$route.query.flashSaleID
      }
      await this.$store.dispatch('actionDetailFlashSaleSeller', data)
      var responseData = await this.$store.state.NSGManageFlashSale.stateDetailFlashSaleSeller
      if (responseData.result === 'SUCCESS') {
        this.dataList = responseData.data
        console.log(this.dataList)
        if (responseData.data.product_list.length !== 0) {
          this.flashSaleProductList = responseData.data.product_list
          this.countProduct = this.flashSaleProductList.length
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (responseData.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => {})
        } else if (responseData.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(responseData.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(responseData.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    editFlashSale (item) {
      this.$router.push({ path: `/manageFlashSaleEdit?flashSaleID=${item.flashsale_id}` }).catch(() => {})
    },
    repeatCreateFlashSale (item) {
      this.$router.push({ path: `/manageFlashSaleEdit?flashSaleID=${item.flashsale_id}` }).catch(() => {})
    },
    async exportFlashSale () {
      const listFlashSale = []
      listFlashSale.push({
        flashsale_id: this.dataList.flashsale_id
      })
      console.log(listFlashSale)
      var data = {
        start_date: this.searchStartDateNotFormat === undefined ? '' : this.searchStartDateNotFormat,
        end_date: this.searchEndDateNotFormat === undefined ? '' : this.searchEndDateNotFormat,
        list_flashsale: listFlashSale
      }
      await this.$store.dispatch('actionExportFlashSale', data)
      var responseData = await this.$store.state.NSGManageFlashSale.stateExportFlashSale
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        const link = document.createElement('a')
        link.href = responseData.data
        link.download = 'falsh Sale'
        link.click()
      } else {
        this.$store.commit('closeLoader')
        if (responseData.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => {})
        } else if (responseData.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(responseData.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(responseData.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    formatDateFlashSale (date) {
      if (!date) return null
      const [year, month, dayTime] = date.split('-')
      const [day, time] = dayTime.split(' ')
      const yearChange = parseInt(year) + 543
      const [hour, min] = time.split(':')
      return `${day}/${month}/${yearChange} ${hour}:${min}`
    }
  }
}
</script>
<style scoped>
.vSelectLineHeight /deep/ .v-select__selection--comma {
  line-height: 25px !important;
}
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.v-data-table /deep/
.v-data-table__wrapper .v-data-table__mobile-table-row {
  margin-bottom: 20px;
  border: 1px solid #ededed;
  display: block;
  border-radius: 8px;
}
@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.6rem;
    padding: 0 0 0 8px;
  }
}
@media only screen and (min-width: 768px) {
  /* For desktop: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    /* font-size: 14px; */
    padding: 0 0 0 8px;
  }
}
.v-data-table__empty-wrapper {
    text-align: center;
    place-self: center;
}
</style>
