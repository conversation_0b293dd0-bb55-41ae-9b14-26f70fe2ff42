<template>
  <v-container class="pa-4">
    <v-card :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']" elevation="0">
      <v-card-title class="pb-0" style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">
        ตั้งค่าการชำระเงิน
      </v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>
        ตั้งค่าการชำระเงิน
      </v-card-title>

      <v-divider class="my-4"></v-divider>

      <div class="px-6">
        <span class="text-start" style="font-size: 20px; font-weight: 700;">ประเภทบัญชี</span>

        <v-form ref="FormbuyerDetailType" :lazy-validation="lazy" v-model="FormbuyerDetailType">
          <v-row>
            <v-col cols="12" class="mt-4 reduce-spacing">
              <span class="labelInputSize">ประเภทบัญชี <span style="color: red;">*</span></span>
              <v-select :items="accountTypes" item-text="text" item-value="value" placeholder="เลือกประเภทบัญชี" v-model="account_type" outlined dense :rules="Rules.account_type"></v-select>
            </v-col>
          </v-row>
        </v-form>

        <v-divider class="my-4"></v-divider>

        <span class="text-start" style="font-size: 20px; font-weight: 700;">ข้อมูลการชำระเงิน</span>

        <v-form ref="FormbuyerDetailPay" :lazy-validation="lazy" v-model="FormbuyerDetailPay">
          <v-row>
            <v-col cols="6" class="mt-4 reduce-spacing">
              <span>ชื่อบัญชี <span style="color: red;">*</span></span>
              <v-text-field class="input-text" placeholder="ระบุชื่อบัญชี" v-model="bank_username" outlined dense :rules="Rules.bank_username"></v-text-field>
            </v-col>
            <v-col cols="6" class="mt-4 reduce-spacing">
              <span>ชื่อธนาคาร <span style="color: red;">*</span></span>
              <v-select :items="banklist" item-text="nameTh" placeholder="เลือกชื่อธนาคาร" v-model="bank_name" outlined dense :rules="Rules.bank_name"></v-select>
            </v-col>
            <v-col cols="6" class="reduce-spacing">
              <span>ชื่อสาขาธนาคาร <span style="color: red;">*</span></span>
              <v-text-field class="input-text" placeholder="ระบุชื่อสาขาธนาคาร" v-model="bank_branch" outlined dense :rules="Rules.bank_branch"></v-text-field>
            </v-col>
            <v-col cols="6" class="reduce-spacing">
              <span>หมายเลขบัญชีธนาคาร <span style="color: red;">*</span></span>
              <v-text-field class="input-text" placeholder="ระบุหมายเลขบัญชีธนาคาร" v-model="bank_no"
                outlined dense :rules="Rules.bank_no"
                :maxlength="maxBankNumber">
              </v-text-field>
            </v-col>
            <v-col cols="12" class="reduce-spacing">
              <span>รูปหน้าบัญชีธนาคาร <span style="color: red;">*</span></span>
              <br>
              <v-btn @click="triggerFileInput()" color="#27AB9C" class="white--text rounded-button">อัปโหลดรูปหน้าบัญชีธนาคาร</v-btn>
              <input type="file" ref="fileInput" @change="handleFileUpload($event)" style="display: none;">
              <div v-if="bookbankImageUrl" class="mt-2">
                <v-card class="d-flex justify-center align-center mb-6"
                style="padding: 10px; max-width: 300px; max-height: 300px; overflow: hidden;">
                  <img :src="bookbankImageUrl" alt="Bank Book Image" style="width: 100%; height: 100%; object-fit: contain;">
                </v-card>
              </div>
            </v-col>
          </v-row>
        </v-form>

        <v-card-actions style="padding-top: 20px;">
          <v-spacer></v-spacer>
          <v-btn class="px-5 white--text" color="#27AB9C" @click="nextSocailMedia()" :disabled="!formValid">ถัดไป</v-btn>
        </v-card-actions>

      </div>
    </v-card>
  </v-container>
</template>

<script>
import banklist from './bankdetail.json'

export default {
  data () {
    return {
      lazy: false,
      FormbuyerDetailType: false,
      FormbuyerDetailPay: false,
      banklist: banklist.data,
      accountTypes: [
        { text: 'ออมทรัพย์', value: 'savings' },
        { text: 'ฝากประจำ', value: 'current' }
      ],
      account_type: '',
      bank_username: '',
      bank_name: '',
      bank_branch: '',
      bank_no: '',
      bookbankImage: null,
      bookbankImageUrl: null,
      Rules: {
        account_type: [
          v => !!v || 'กรุณาเลือกประเภทบัญชี'
        ],
        bank_username: [
          v => !!v || 'กรุณาระบุชื่อบัญชี'
        ],
        bank_name: [
          v => !!v || 'กรุณาเลือกชื่อธนาคาร'
        ],
        bank_branch: [
          v => !!v || 'กรุณาระบุชื่อสาขาธนาคาร'
        ],
        bank_no: [
          v => !!v || 'กรุณาระบุหมายเลขบัญชีธนาคาร',
          v => (v && v.length <= this.maxBankNumber) || 'หมายเลขบัญชีเกินจำนวนที่กำหนด',
          v => /^[0-9]+$/.test(v) || 'กรุณากรอกเฉพาะตัวเลข'
        ],
        bookbankImage: [
          v => !!v || 'กรุณาอัปโหลดรูปภาพ'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      return this.$vuetify.breakpoint.xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    maxBankNumber () {
      if (!this.bank_name) return 10
      const bank = this.banklist.find(item => item.nameTh === this.bank_name)
      return bank ? bank.maxNumber : 10
    },
    formValid () {
      return this.FormbuyerDetailType && this.FormbuyerDetailPay
    }
  },
  created () {
    const previousDataPay = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[0]
    if (previousDataPay) {
      this.account_type = previousDataPay.account_type || ''
      this.bank_username = previousDataPay.bank_username || ''
      this.bank_name = previousDataPay.bank_name || ''
      this.bank_branch = previousDataPay.bank_branch || ''
      this.bank_no = previousDataPay.bank_no || ''
      this.bookbankImage = previousDataPay.bookbank_image || ''
      this.bookbankImageUrl = previousDataPay.bookbankImageUrl || ''
    }

    const latestDataArrayPay = []
    latestDataArrayPay.push({
      account_type: this.account_type,
      bank_username: this.bank_username,
      bank_name: this.bank_name,
      bank_branch: this.bank_branch,
      bank_no: this.bank_no,
      bookbankImage: this.bookbankImage,
      bookbankImageUrl: this.bookbankImageUrl
    })

    const previousDataSocail = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay[1]
    if (previousDataSocail) {
      this.facebook_link = previousDataSocail.facebook_link || ''
      this.tiktok_link = previousDataSocail.tiktok_link || ''
      this.youtube_link = previousDataSocail.youtube_link || ''
      this.instagram_link = previousDataSocail.instagram_link || ''
      this.line_link = previousDataSocail.line_link || ''
    }

    const latestDataArraySocail = []
    latestDataArraySocail.push({
      facebook_link: this.facebook_link,
      tiktok_link: this.tiktok_link,
      youtube_link: this.youtube_link,
      instagram_link: this.instagram_link,
      line_link: this.line_link
    })
  },
  methods: {
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    nextSocailMedia () {
      var dataDetail = this.$store.state.ModuleAffiliate.stateAffiliateDetailPay
      var object = {
        ...dataDetail[0],
        ...dataDetail[1]
      }

      this.object = object

      this.$store.state.ModuleAffiliate.stateAffiliateDetailPay = [
        {
          account_type: this.account_type,
          bank_username: this.bank_username,
          bank_name: this.bank_name,
          bank_branch: this.bank_branch,
          bank_no: this.bank_no,
          bookbank_image: this.bookbankImage,
          bookbankImageUrl: this.bookbankImageUrl
        }
      ]

      this.$router.push({ name: 'socailMediaDetail', params: { detail: dataDetail[1] } })
    },
    triggerFileInput () {
      this.$refs.fileInput.click()
    },
    handleFileUpload (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.bookbankImageUrl = reader.result
            this.bookbankImage = file
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500
        })
      }
    }
  }
}
</script>
