<template>
  <v-container>
    <v-breadcrumbs :items="items">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
         :href="item.href"
         :disabled="item.disabled"
        >
          {{ item.text }}
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-row justify="center" class="my-4">
      <h2 :style="!MobileSize ? 'font-weight: 700; font-size: 24px;' : 'font-weight: 700; font-size: 18px;' "> ข้อมูลนิติบุคคล </h2>
    </v-row>
    <v-row justify="center" class="my-2" v-if="MobileSize">
      <h2 style="font-weight: 700; font-size: 16px; line-height: 24px;">แนะนำการขายของบน INET Marketplace</h2>
    </v-row>
    <v-row justify="start" class="my-4" v-if="MobileSize">
      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span>
    </v-row>
    <v-row justify="start" class="my-4" v-if="MobileSize">
      <v-col cols="12" class="px-0">
        <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="3">
                <v-btn fab color="#F2F2F2" disabled>
                  <v-avatar size="32" tile>
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group756.png" width="30" height="30" contain></v-img>
                  </v-avatar>
                </v-btn>
              </v-col>
              <v-col cols="9">
                <span style="font-weight: 400; font-size: 14px; line-height: 22px;">1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" class="px-0">
        <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="3">
                <v-btn fab color="#F2F2F2" disabled>
                  <v-avatar size="32" tile>
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group.png" width="30" height="30" contain></v-img>
                  </v-avatar>
                </v-btn>
              </v-col>
              <v-col cols="9">
                <span style="font-weight: 400; font-size: 14px; line-height: 22px;">2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <img src="@/assets/ImageINET-Marketplace/ICONRegister/6.png" contain height="20" width="20" @click="OpenModal" style="cursor: pointer;"/><v-chip color="#E6F5F3" class="pl-1 pr-1 ml-1" style="cursor: pointer;" @click="OpenModal" small><span style="color: #27AB9C; font-weight: 400; font-size: 14px;">เอกสารแนบ</span></v-chip> มาทาง <span style="color: #1B5DD6">email <EMAIL></span> ของ <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" class="px-0">
        <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="3">
                <v-btn fab color="#F2F2F2" disabled>
                  <v-avatar size="32" tile>
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/3.png" width="30" height="30"  contain></v-img>
                  </v-avatar>
                </v-btn>
              </v-col>
              <v-col cols="9">
                <span style="font-weight: 400; font-size: 14px; line-height: 22px;">3. หลังจากที่ทาง <span style="font-weight: bold;">Thai Payment Gateway</span> ตรวจสอบเสร็จ จะส่งอีเมลกลับมาให้ผ่านอีเมลที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" class="px-0">
        <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="3">
                <v-btn fab color="#F2F2F2" disabled>
                  <v-avatar size="32" tile>
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/4.png" width="30" height="30" contain></v-img>
                  </v-avatar>
                </v-btn>
              </v-col>
              <v-col cols="9">
                <span style="font-weight: 400; font-size: 14px; line-height: 22px;">4. ระหว่างที่รอ คุณสามารถอัปโหลดสินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" class="px-0">
        <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="3">
                <v-btn fab color="#F2F2F2" disabled>
                  <v-avatar size="32" tile>
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/5.png" width="31" height="30" contain></v-img>
                  </v-avatar>
                </v-btn>
              </v-col>
              <v-col cols="9">
                <span style="font-weight: 400; font-size: 14px; line-height: 22px;">5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-card :class="!MobileSize ? '' : 'px-0 mx-0'">
      <v-container >
        <ModalImage />
        <v-row no-gutters justify="center" class="pa-3" v-if="!MobileSize">
          <v-col cols="12" class="ma-8">
            <v-row justify="center">
              <v-col cols="12" align="center" class="py-0">
                <h2 style="font-weight: 700; font-size: 20px;">แนะนำการขายของบน INET Marketplace</h2>
              </v-col>
            </v-row>
            <v-row justify="start">
              <v-col cols="12" class="mt-4 ml-4">
                <span style="font-weight: 400; font-size: 16; color: #333333;">ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span>
                <v-timeline
                  dense
                >
                  <v-timeline-item fill-dot color="#F2F2F2" large>
                    <template v-slot:icon>
                      <!-- <v-avatar size="20"> -->
                        <img src="@/assets/ImageINET-Marketplace/ICONRegister/Group756.png">
                      <!-- </v-avatar> -->
                    </template>
                    <v-col cols="12" md="11">
                      <span>1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span>
                    </v-col>
                  </v-timeline-item>
                  <v-timeline-item fill-dot color="#F2F2F2" large>
                    <template v-slot:icon>
                      <!-- <v-avatar size="20"> -->
                      <img src="@/assets/ImageINET-Marketplace/ICONRegister/Group.png">
                      <!-- </v-avatar> -->
                    </template>
                    <v-col cols="12" md="11">
                      <span>2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <img src="@/assets/ImageINET-Marketplace/ICONRegister/6.png" contain height="20" width="20" @click="OpenModal" style="cursor: pointer;"/><v-chip color="#E6F5F3" class="pl-1 pr-1 ml-1" style="cursor: pointer;" @click="OpenModal" small><span style="color: #27AB9C; font-weight: 400; font-size: 14px;">เอกสารแนบ</span></v-chip> มาทาง <span style="color: #1B5DD6">email <EMAIL></span> ของ <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span>
                    </v-col>
                  </v-timeline-item>
                  <v-timeline-item fill-dot color="#F2F2F2" large>
                    <template v-slot:icon>
                      <!-- <v-avatar size="20"> -->
                      <img src="@/assets/ImageINET-Marketplace/ICONRegister/3.png">
                      <!-- </v-avatar> -->
                    </template>
                    <v-col cols="12" md="11">
                      <span>3. หลังจากที่ทาง <span style="font-weight: bold;">Thai Payment Gateway</span> ตรวจสอบเสร็จ จะส่งอีเมลกลับมาให้ผ่านอีเมลที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span><br>
                    </v-col>
                  </v-timeline-item>
                  <v-timeline-item fill-dot color="#F2F2F2" large>
                    <template v-slot:icon>
                      <!-- <v-avatar size="20"> -->
                      <img src="@/assets/ImageINET-Marketplace/ICONRegister/4.png">
                      <!-- </v-avatar> -->
                    </template>
                    <v-col cols="12" md="11">
                      <span>4. ระหว่างที่รอ คุณสามารถอัปโหลดสินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ</span>
                    </v-col>
                  </v-timeline-item>
                  <v-timeline-item fill-dot color="#F2F2F2" large>
                    <template v-slot:icon>
                      <!-- <v-avatar size="20"> -->
                      <img src="@/assets/ImageINET-Marketplace/ICONRegister/5.png">
                      <!-- </v-avatar> -->
                    </template>
                    <v-col cols="12" md="11">
                      <span>5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span><br>
                      <!-- <span>แนะนำเพิ่มเติม: คุณสามารถโหลด <span style="color: #1B5DD6">One Chat application</span> ซึ่งคุณสามารถ <span style="font-weight: bold;">add bot oneforallmarket_bot</span> ซึ่งจะแจ้งเตือนคุณได้ทันที หากมีการสั่งซื้อเกิดขึ้น</span> -->
                    </v-col>
                  </v-timeline-item>
                </v-timeline>
              </v-col>
            </v-row>
            <!-- <v-card outlined>
              <v-row  no-gutters align="center">
                <v-col cols="12" class="mt-5 ml-10">
                  <h2><v-icon large class="mr-5">mdi-comment-processing-outline</v-icon>เเนะนำการขายของบน INET Market</h2>
                </v-col>
                <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                <v-col cols="12" md="10" offset="1" class="pl-3">
                  <span>ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span><br>
                  </v-col>
                <v-col cols="12" md="10" offset="1" class="pl-6 pb-5">
                  <span>1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span><br>
                  <span>2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <v-chip x-small class="mr-2 ml-2" @click="OpenModal" color="success" outlined><v-icon left small>mdi-hand-pointing-right</v-icon>เอกสารนี้</v-chip>มาทาง email <EMAIL> ของ <u>Thai Payment Gateway</u> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span><br>
                  <span>3. หลังจากที่ทาง Thai Payment Gateway ตรวจสอบเสร็จ จะส่ง email กลับมาให้ผ่าน email ที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี Thai Payment Gateway ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span><br>
                  <span>4. ระหว่างที่รอ คุณสามารถ upload สินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ</span><br>
                  <span>5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span><br>
                  <span>แนะนำเพิ่มเติม: คุณสามารถโหลด <u>One Chat</u> application ซึ่งคุณสามารถ add bot oneforallmarket_bot ซึ่งจะแจ้งเตือนคุณได้ทันที หากมีการสั่งซื้อเกิดขึ้น</span>
                </v-col>
              </v-row>
            </v-card> -->
          </v-col>
        </v-row>
        <!-- Select Business Type -->
        <v-row dense justify="center" class="mb-4">
          <v-col cols="12" md="6">
            <v-card class="d-flex justify-center" outlined style="border: 2px solid #27AB9C;" width="100%" height="100%">
              <v-tabs
                v-model="tab"
                centered
                icons-and-text
                color="#4b8bad"
                class="mt-4"
              >
                <v-tabs-slider color="#4b8bad"></v-tabs-slider>
                <!-- บัญชีนิติบุคคล -->
                <v-tab href="#tab-1" class="pb-4">
                  บัญชีนิติบุคคล
                  <v-icon large>mdi-office-building</v-icon>
                </v-tab>
                <!-- บัญชีนิติบุคคลอื่นๆ -->
                <v-tab href="#tab-2" class="pb-4" @click="checkeKYC()">
                  บัญชีนิติบุคคลอื่นๆ
                  <v-icon large>mdi-storefront-outline</v-icon>
                </v-tab>
              </v-tabs>
            </v-card>
          </v-col>
        </v-row>
        <v-tabs-items v-model="tab" class="mb-4 mt-8">
          <v-tab-item :value="'tab-1'">
            <!-- select type tax -->
            <v-card elevation="0" width="98%" height="98%" class="ml-4 mr-4" v-if="DetailBusiness.length === 0">
              <v-col cols="12" class="ml-2 mb-0 pb-0">
                <v-row dense>
                  <v-col cols="12" md="12" sm="12">
                    <h2 style="font-weight: 700; font-size: 20px;">กรุณาเลือกการลงทะเบียนบัญชีนิติบุคคล</h2>
                  </v-col>
                  <v-container>
                    <v-radio-group
                      v-model="selectType"
                      row
                      hide-details
                    >
                      <!-- <v-radio
                        label="ลงทะเบียนโดยการกรอกข้อมูลใหม่"
                        color="#27AB9C"
                        value="none"
                      ></v-radio> -->
                      <!-- <v-radio
                        label="ลงทะเบียนโดยการดึงข้อมูลจากกรมสรรพากร"
                        color="#27AB9C"
                        value="TaxByRevenue"
                      ></v-radio> -->
                      <v-radio
                        label="ลงทะเบียนโดยการดึงข้อมูลจาก One ID"
                        color="#27AB9C"
                        value="TaxByOneID"
                      ></v-radio>
                    </v-radio-group>
                  </v-container>
                  <!-- <v-row dense v-if="selectType">
                    <v-col cols="12" class="mt-0 pt-0 ml-8">
                      <v-radio-group
                        v-model="selectTypeOne"
                        row
                      >
                        <v-radio
                          label="ลงทะเบียนนิติบุคคล"
                          color="#27AB9C"
                          value="none"
                        ></v-radio>
                        <v-radio
                          label="ลงทะเบียนนิติบุคคลอื่นๆ"
                          color="#27AB9C"
                          value="TaxByOneID"
                        ></v-radio>
                      </v-radio-group>
                    </v-col>
                  </v-row> -->
                </v-row>
              </v-col>
            </v-card>
          </v-tab-item>
          <!-- v-if="checkeKYCUser" -->
          <v-tab-item :value="'tab-2'" v-if="checkeKYCUser === true">
            <!-- select type tax -->
            <v-card elevation="0" width="98%" height="98%" class="ml-4 mr-4" v-if="DetailBusinessOther.length === 0">
              <v-col cols="12" class="ml-2 mb-0 pb-0">
                <v-row dense>
                  <v-col cols="12" md="12" sm="12">
                    <h2 style="font-weight: 700; font-size: 20px;">กรุณาเลือกการลงทะเบียนบัญชีนิติบุคคลอื่นๆ</h2>
                  </v-col>
                  <v-container>
                    <v-radio-group
                      v-model="selectTypeOther"
                      row
                      hide-details
                    >
                      <v-radio
                        label="ลงทะเบียนโดยการกรอกข้อมูลใหม่"
                        color="#27AB9C"
                        value="noneOther"
                      ></v-radio>
                      <!-- <v-radio
                        label="ลงทะเบียนโดยการดึงข้อมูลจากกรมสรรพากร"
                        color="#27AB9C"
                        value="TaxByRevenue"
                      ></v-radio> -->
                      <!-- <v-radio
                        label="ลงทะเบียนโดยการดึงข้อมูลจาก One ID"
                        color="#27AB9C"
                        value="TaxByOneIDOther"
                      ></v-radio> -->
                    </v-radio-group>
                  </v-container>
                  <!-- <v-row dense v-if="selectType">
                    <v-col cols="12" class="mt-0 pt-0 ml-8">
                      <v-radio-group
                        v-model="selectTypeOne"
                        row
                      >
                        <v-radio
                          label="ลงทะเบียนนิติบุคคล"
                          color="#27AB9C"
                          value="none"
                        ></v-radio>
                        <v-radio
                          label="ลงทะเบียนนิติบุคคลอื่นๆ"
                          color="#27AB9C"
                          value="TaxByOneID"
                        ></v-radio>
                      </v-radio-group>
                    </v-col>
                  </v-row> -->
                </v-row>
              </v-col>
            </v-card>
          </v-tab-item>
        </v-tabs-items>
        <!-- form input tax_ID -->
        <!-- <v-row dense> -->
        <v-col cols="12" class="ml-6 mt-0 mb-0 pb-0">
          <v-form ref="FormTaxID" :lazy-validation="lazy1" :style="showtaxSearch === true ? 'dispaly: block' : 'display: none'">
            <v-col cols="12" class="mt-0 pt-0">
              <v-row dense>
                <v-col cols="12" md="2" sm="12" class="pt-4">
                  <span>เลขประจำตัวผู้เสียภาษี :</span>
                </v-col>
                <v-col cols="12" md="3" sm="12">
                  <v-text-field  v-model="taxIDSearch" placeholder="กรุณาระบุเลขประจำตัวผู้เสียภาษี" outlined dense :rules="Rules.empty" :maxLength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="CheckSpacebar($event)"></v-text-field>
                </v-col>
                <v-col cols="12" md="1" sm="12" class="mt-1">
                  <v-btn small color="#27AB9C" @click="searchTaxID()" dark>ค้นหา</v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-form>
        </v-col>
        <!-- form input tax_ID Other -->
        <!-- <v-row dense> -->
        <v-col cols="12" class="ml-6 mt-0 mb-0 pb-0">
          <v-form ref="FormTaxIDOther" :lazy-validation="lazy2" :style="showtaxSearchOther === true ? 'dispaly: block' : 'display: none'">
            <v-col cols="12" class="mt-0 pt-0">
              <v-row dense>
                <v-col cols="12" md="2" sm="12" class="pt-4">
                  <span>เลขประจำตัวผู้เสียภาษี :</span>
                </v-col>
                <v-col cols="12" md="3" sm="12">
                  <v-text-field  v-model="taxIDSearchOther" placeholder="กรุณาระบุเลขประจำตัวผู้เสียภาษี" outlined dense :rules="Rules.empty" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12" md="1" sm="12" class="mt-1">
                  <v-btn small color="#27AB9C" @click="searchTaxIDOther()" dark>ค้นหา</v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-form>
        </v-col>
        <!-- ลงทะเบียนนิติบุคคลแบบ นิติบุคคล -->
        <v-form ref="Formcreate" :lazy-validation="lazy" v-if="showForm === true && DetailBusiness.length === 0 && selectType !== ''">
          <div>
            <v-card outlined class="mt-0 mb-4" style="border: 1px solid #27AB9C;">
              <v-card-title>
                <h4 style="font-weight: 700; font-size: 18px;">ข้อมูลนิติบุคคล</h4>
              </v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>ประเภทนิติบุคคล(ไทย)</span><span style="color: red;"> *</span>
                    <v-select
                      v-model="businessType"
                      :items="businessTypeTHItem"
                      item-text="nameTH"
                      item-value="value"
                      placeholder="ประเภทนิติบุคคล(ไทย)"
                      outlined
                      :readonly="typeTaxID === 'TaxByOneID' ? true : false"
                      dense
                      :rules="Rules.empty"
                    >
                    </v-select>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ชื่อบริษัท(ไทย)</span><span style="color: red;"> *</span>
                    <v-text-field v-model="businessNameTH" placeholder="ชื่อบริษัท(ไทย)" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessNameTHRules" @keypress="isLetterThai($event)">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4" :class="IpadSize ? 'pr-2' : ''">
                    <span>ชื่อสำหรับแสดงบนเอกสาร(ไทย)</span><span style="color: red;"> *</span>
                    <v-text-field v-model="businessDocNameTH" placeholder="ชื่อสำหรับแสดงบนเอกสาร(ไทย)" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessDocNameTHRules" @keypress="isLetterThai($event)">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- ชื่ออังกฤษ -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>ประเภทนิติบุคคล(อังกฤษ)</span><span style="color: red;"> *</span>
                      <v-select
                      v-model="businessType"
                      :items="businessTypeTHItem"
                      item-text="nameEN"
                      item-value="value"
                      placeholder="Please choose a legal entity"
                      outlined
                      :readonly="typeTaxID === 'TaxByOneID' ? true : false"
                      dense
                      :rules="Rules.empty"
                    >
                      </v-select>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ชื่อบริษัท(อังกฤษ)</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="businessNameEN" placeholder="ชื่อบริษัท(อังกฤษ)" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessNameENRules" @keypress="isLetterEng($event)">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4" :class="IpadSize ? 'pr-2' : ''">
                    <span>ชื่อสำหรับแสดงบนเอกสาร(อังกฤษ)</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="businessDocNameEN" placeholder="ชื่อสำหรับแสดงบนเอกสาร(อังกฤษ)" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessDocNameENRules" @keypress="isLetterEng($event)">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- เลขประจำตัวผู้เสียภาษี -->
                <v-row>
                  <v-col cols="12" md="6" sm="6">
                    <span>เลขประจำตัวผู้เสียภาษี</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="taxId" placeholder="เลขประจำตัวผู้เสียภาษี" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.empty" :maxlength="max" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span>อีเมล</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="mail" placeholder="อีเมล" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.emailRules" @keypress="isLetterEngEmail($event)">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- สาขา -->
                <v-row>
                  <v-col cols="12" md="6" sm="6">
                    <span>ชื่อสาขา</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="branchName" placeholder="ชื่อสาขา" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.empty">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span>รหัสสาขา</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="branchCode" placeholder="รหัสสาขา (กรอกได้เฉพาะตัวเลข)" :readonly="typeTaxID === 'TaxByOneID' ? true : false" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :disabled='disabledBranchCode' :maxlength='5'  :rules="Rules.branchno">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- เบอร์โทรศัพท์ -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>เบอร์โทรศัพท์</span><span style="color: red;"> *</span>เบอร์โทรศัพท์
                    <v-text-field  v-model="tel" placeholder="เบอร์โทรศัพท์" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :maxlength="maxTel" :rules="Rules.tel" v-mask="tel.length <= 9 ? '##-###-####' : tel.length === 10 ? '###-###-####' : ''">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>เบอร์มือถือ</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="phone" placeholder="เบอร์มือถือ" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.tel" :maxlength="maxPhone" v-mask="'###-###-####'">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>เบอร์โทรสาร</span>
                    <v-text-field  v-model="fax" placeholder="เบอร์โทรสาร" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :maxlength="maxTel" v-mask="'##-###-####'">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- ที่อยู่ตามใบกำกับภาษีของร้าน -->
                <v-card-title class="pl-0">
                  <h4 style="font-weight: 700; font-size: 18px;">ที่อยู่ตามใบกำกับภาษีของร้าน</h4>
                </v-card-title>
                <!-- ที่อยู่ -->
                <v-row>
                  <v-col cols="12" md="6" sm="4">
                    <span>เลขรหัสประจำที่อยู่ </span>
                    <v-text-field  v-model="addressCode" placeholder="เลขรหัสประจำที่อยู่" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense >
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="8">
                    <span>ที่อยู่</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="houseNo" placeholder="ที่อยู่" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.empty">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- เลขที่ห้อง  -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>ห้องเลขที่ </span>
                    <v-text-field  v-model="roomNo" placeholder="ห้องเลขที่" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense >
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ชั้นที่ </span>
                    <v-text-field  v-model="floor" placeholder="ชั้นที่" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>อาคาร </span>
                    <v-text-field  v-model="buildingName" placeholder="อาคาร" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- หมู่บ้าน -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>หมู่บ้าน </span>
                    <v-text-field  v-model="houseName" placeholder="หมู่บ้าน" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>หมู่ที่ </span>
                    <v-text-field  v-model="houseGroup" placeholder="หมู่ที่" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ตรอก/ซอย </span>
                    <v-text-field  v-model="alley" placeholder="ตรอก/ซอย" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- แยก -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>แยก </span>
                  <v-text-field v-model="cross" placeholder="แยก" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ถนน </span>
                    <v-text-field v-model="road" placeholder="ถนน" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
                    <addressinput-subdistrict :rules="Rules.empty" label="" v-model="subdistrict" v-if="typeTaxID === 'none'" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุตำบล/แขวง"/>
                    <div v-if="typeTaxID === 'none' && checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <!-- <v-autocomplete outlined dense :items="itemProvince" v-model="province" item-text="name_th" item-value="id" placeholder="จังหวัด" :rules="Rules.empty"  v-if="typeTaxID === 'none' && "></v-autocomplete> -->
                    <v-text-field outlined dense v-model="subdistricttext" placeholder="ตำบล/แขวง" :rules="Rules.empty" v-if="typeTaxID !== 'none'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                  </v-col>
                </v-row>
                <!-- ตำบล -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
                    <addressinput-district label="" v-if="typeTaxID === 'none'" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุอำเภอ/เขต" />
                    <div v-if="typeTaxID === 'none' && checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <!-- <v-autocomplete outlined dense :items="itemDistrict" v-model="district" item-text="name_th" item-value="id" placeholder="อำเภอ" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                    <v-text-field outlined dense v-model="districttext" placeholder="อำเภอ/เขต" :rules="Rules.empty" v-if="typeTaxID !== 'none'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>จังหวัด</span><span style="color: red;"> *</span>
                    <addressinput-province label="" v-if="typeTaxID === 'none'" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                    <div v-if="typeTaxID === 'none' && checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <!-- <v-autocomplete outlined dense :items="itemSubdistrict" v-model="subdistrict" item-text="name_th" item-value="id" placeholder="ตำบล" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                    <v-text-field outlined dense v-model="provincetext" placeholder="จังหวัด" :rules="Rules.empty" v-if="typeTaxID !== 'none'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                    <addressinput-zipcode label="" v-if="typeTaxID === 'none'" numbered v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                    <div v-else-if="typeTaxID === 'none' && checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <v-text-field disabled v-if="typeTaxID !== 'none'" label="" v-model="zipcode" placeholder="รหัสไปรษณีย์" required outlined dense/>
                  </v-col>
                </v-row>
                <!-- อัพโหลดหนังสือรองรับนิติบุคคล -->
                <v-card-title class="pl-0" v-if="typeTaxID === 'none'">
                  <h4 style="font-weight: 700; font-size: 18px;">เอกสารแนบ</h4>
                </v-card-title>
                <v-row v-if="typeTaxID === 'none'">
                  <v-col cols="12" md="12" class="mt-0">
                    <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                      <v-card-title>อัปโหลดหนังสือรับรองนิติบุคคล</v-card-title>
                      <v-card-text>
                        <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFile()">
                          <v-card-text>
                            <v-row no-gutters justify="center" style="cursor: pointer;">
                              <v-file-input
                                v-model="DataImage"
                                :items="DataImage"
                                accept=".pdf"
                                @change="UploadImage()"
                                id="file_input"
                                multiple
                                :clearable="false"
                                style="display:none">
                              </v-file-input>
                              <v-col cols="12" md="12" class="mb-6">
                                <v-row justify="center" class="pt-10">
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="280.34" height="154.87" contain></v-img>
                                </v-row>
                              </v-col>
                              <v-col cols="12" md="12" class="mt-6">
                                <v-row justify="center">
                                  <v-col cols="12" md="4" style="text-align: center;">
                                    <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                                    <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                                    <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                                    <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุ ไฟล์ควรมีขนาดไม่เกิน 10MB</span>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                        <div v-if="file.length !== 0" class="mt-4">
                          <draggable v-model="file"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                            <v-col v-for="(item, index) in file" :key="index" cols="12" md="2">
                              <v-card outlined class="pa-1" width="146" height="100%">
                                <v-card-text>
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="130" height="130" contain>
                                    <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                      <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                    </v-btn>
                                  </v-img>
                                  <p style="text-align: center;">{{ item.name }}</p>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </draggable>
                        </div>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row justify="right" class="my-4 ml-4" v-if="typeTaxID === 'none'">
                  <h3 style="font-weight: bold; color: color: #333333; font-size: 24px; line-height: 32px;">ดาวน์โหลดเอกสาร</h3>
                </v-row>
                <v-row justify="right" dense class="mb-6 ml-4" v-if="typeTaxID === 'none'">
                  <v-col cols="12" md="12">
                    <v-row>
                      <v-card outlined width="386px" height="100px" style="border-radius: 8px; border: 1px solid #F2F2F2;" class="mr-4" @click="DownloadForm('POA')" dense>
                        <!-- <v-card-text> -->
                          <v-row dense>
                            <v-col cols="12" md="3" sm="3" class="mt-2 ml-2">
                              <v-avatar
                               width="84px"
                               height="84px"
                               color="#EBEEF6"
                               style="border-radius: 8px;"
                              >
                                <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/word.png" width="60px" height="60px" contain></v-img>
                              </v-avatar>
                            </v-col>
                            <v-col cols="12" md="8" sm="8" class="mt-6">
                              <span style="font-weight: bold; font-size: 16px; line-height: 24px; color: #333333;">หนังสือมอบอำนาจ.docx</span><br/>
                              <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 18 KB</span>
                            </v-col>
                          </v-row>
                        <!-- </v-card-text> -->
                      </v-card>
                      <v-card outlined width="386px" height="100px" style="border-radius: 8px; border: 1px solid #F2F2F2;" @click="DownloadForm('POAS')" dense>
                        <!-- <v-card-text> -->
                          <v-row dense>
                            <v-col cols="12" md="3" sm="3" class="mt-2 ml-2">
                              <v-avatar
                               width="84px"
                               height="84px"
                               color="#EBEEF6"
                               style="border-radius: 8px;"
                              >
                                <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/word.png" width="60px" height="60px" contain></v-img>
                              </v-avatar>
                            </v-col>
                            <v-col cols="12" md="8" sm="8" class="mt-6">
                              <span style="font-weight: bold; font-size: 16px; line-height: 24px; color: #333333;">หนังสือมอบอำนาจช่วง.docx</span><br/>
                              <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 17 KB</span>
                            </v-col>
                          </v-row>
                        <!-- </v-card-text> -->
                      </v-card>
                    </v-row>
                  </v-col>
                </v-row>
                <!-- อัพโหลดหนังสือมอบอำนาจ -->
                <v-row v-if="typeTaxID === 'none'">
                  <v-col cols="12" md="12" class="mt-0">
                    <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                      <v-card-title>อัปโหลดหนังสือมอบอำนาจ</v-card-title>
                      <v-card-text>
                        <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFile1()">
                          <v-card-text>
                            <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                              <v-file-input
                                v-model="DataImage1"
                                :items="DataImage1"
                                accept=".pdf"
                                @change="UploadImage1()"
                                id="file_input1"
                                multiple
                                :clearable="false"
                                style="display:none">
                              </v-file-input>
                              <v-col cols="12" md="12" class="mb-6">
                                <v-row justify="center" class="pt-10">
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="280.34" height="154.87" contain></v-img>
                                </v-row>
                              </v-col>
                              <v-col cols="12" md="12" class="mt-6">
                                <v-row justify="center" align="center">
                                  <v-col cols="12" md="4" sm="4" style="text-align: center;">
                                    <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                                    <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                                    <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                                    <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุ ไฟล์ควรมีขนาดไม่เกิน 10MB</span>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                        <div v-if="file1.length !== 0" class="mt-4">
                          <draggable v-model="file1"  :move="onMove1" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                            <v-col v-for="(item, index) in file1" :key="index" cols="12" md="2">
                              <v-card outlined class="pa-1" width="146" height="100%">
                                <v-card-text>
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="130" height="130" contain>
                                    <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                      <v-icon x-small color="white" dark @click="RemoveImage1(index, item)">mdi-close</v-icon>
                                    </v-btn>
                                  </v-img>
                                  <p style="text-align: center;">{{ item.name }}</p>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </draggable>
                        </div>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <!-- ปุ่ม -->
              <v-card-actions class="mb-4">
                <v-spacer></v-spacer>
                <v-btn outlined color="#27AB9C" class="pl-8 pr-8" @click="cancelPage()">ยกเลิก</v-btn>
                <v-btn color="#27AB9C" dark @click="createbiz()" class="pl-4 pr-4">ลงทะเบียน</v-btn>
              </v-card-actions>
            </v-card>
          </div>
        </v-form>
        <!-- ลงทะเบียนนิติบุคคลเรียบร้อยแบบ นิติบุคคล -->
        <v-form ref="Formcreate" :lazy-validation="lazy" v-if="showForm === true && DetailBusiness.length !== 0 && selectType !== ''" class="px-0 mx-0">
          <!-- ชื่อไทย -->
          <!-- <v-row no-gutters justify="center">
          <v-col cols="10" class="mb-10">
            <v-card outlined>
              <v-row  no-gutters align="center">
                <v-col cols="12" class="mt-5 ml-10">
                  <h2><v-icon large class="mr-5">mdi-comment-processing-outline</v-icon>เเนะนำการขายของบน INET Market</h2>
                </v-col>
                <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                <v-col cols="12" md="10" offset="1" class="pl-3">
                  <span>ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span><br>
                  </v-col>
                <v-col cols="12" md="10" offset="1" class="pl-6 pb-5">
                  <span>1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span><br>
                  <span>2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <v-chip x-small class="mr-2 ml-2" @click="OpenModal" color="success" outlined><v-icon left small>mdi-hand-pointing-right</v-icon>เอกสารนี้</v-chip>มาทาง email <EMAIL> ของ <u>Thai Payment Gateway</u> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span><br>
                  <span>3. หลังจากที่ทาง Thai Payment Gateway ตรวจสอบเสร็จ จะส่ง email กลับมาให้ผ่าน email ที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี Thai Payment Gateway ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span><br>
                  <span>4. ระหว่างที่รอ คุณสามารถ upload สินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ</span><br>
                  <span>5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span><br>
                  <span>แนะนำเพิ่มเติม: คุณสามารถโหลด <u>One Chat</u> application ซึ่งคุณสามารถ add bot oneforallmarket_bot ซึ่งจะแจ้งเตือนคุณได้ทันที หากมีการสั่งซื้อเกิดขึ้น</span>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          </v-row> -->
          <v-card outlined class="mt-0 mb-4" style="border: 1px solid #27AB9C;">
            <v-card-title>
              <h4 style="font-weight: 700; font-size: 18px;" class="mt-1">ข้อมูลนิติบุคคล </h4>
              <span v-if="DetailBusiness.status_approve === 'pending'">
                <v-chip small class="ml-3" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
              </span>
              <span v-else-if="DetailBusiness.status_approve === 'approve'">
                <v-chip small class="ml-3" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
              </span>
              <span v-else>
                <v-chip small class="ml-3" color="#FBE5E4" text-color="#F5222D">ไม่อนุมัติ</v-chip>
              </span>
            </v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>ประเภทนิติบุคคล(ไทย)</span><span style="color: red;"> *</span>
                  <v-select
                    v-model="businessType"
                    :items="businessTypeTHItem"
                    item-text="nameTH"
                    item-value="value"
                    placeholder="ประเภทนิติบุคคล(ไทย)"
                    outlined
                    dense
                    readonly
                    :rules="Rules.empty"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ชื่อบริษัท(ไทย)</span><span style="color: red;"> *</span>
                  <v-text-field v-model="DetailBusiness.first_name_th" placeholder="ชื่อบริษัท(ไทย)" outlined dense :rules="Rules.empty" readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4" :class="IpadSize ? 'pr-2' : ''">
                  <span>ชื่อสำหรับแสดงบนเอกสาร(ไทย)</span><span style="color: red;"> *</span>
                  <v-text-field v-model="DetailBusiness.name_on_document_th" placeholder="ชื่อสำหรับแสดงบนเอกสาร" outlined dense :rules="Rules.empty" readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- ชื่ออังกฤษ -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>ประเภทนิติบุคคล(อังกฤษ)</span><span style="color: red;"> *</span>
                    <v-select
                    v-model="businessType"
                    :items="businessTypeTHItem"
                    item-text="nameEN"
                    item-value="value"
                    placeholder="Please choose a legal entity"
                    outlined
                    readonly
                    dense
                    :rules="Rules.empty"
                  >
                    </v-select>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ชื่อบริษัท(อังกฤษ)</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusiness.first_name_eng" placeholder="ชื่อบริษัท(อังกฤษ)" outlined dense :rules="Rules.empty" readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4" :class="IpadSize ? 'pr-2' : ''">
                  <span>ชื่อสำหรับแสดงบนเอกสาร(อังกฤษ)</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusiness.name_on_document_eng" placeholder="ชื่อสำหรับแสดงบนเอกสาร" outlined dense :rules="Rules.empty" readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- เลขประจำตัวผู้เสียภาษี -->
              <v-row>
                <v-col cols="12" md="6" sm="6">
                  <span>เลขประจำตัวผู้เสียภาษี</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusiness.id_card_num" placeholder="เลขประจำตัวผู้เสียภาษี" outlined dense :rules="Rules.empty" :maxlength="max" readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span>อีเมล</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusiness.email" placeholder="อีเมล" outlined dense :rules="Rules.emailRules" readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- สาขา -->
              <v-row>
                <v-col cols="12" md="6" sm="6">
                  <span>ชื่อสาขา</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusiness.branch_name" placeholder="ชื่อสาขา" outlined dense :rules="Rules.empty" readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span>รหัสสาขา</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusiness.branch_no" placeholder="รหัสสาขา" outlined dense readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- เบอร์โทรศัพท์ -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>เบอร์โทรศัพท์</span>
                  <v-text-field v-model="tel" placeholder="เบอร์โทรศัพท์" outlined dense :maxlength="maxTel" v-mask="tel.length <= 9 ? '##-###-####' : tel.length === 10 ? '###-###-####' : ''" readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>เบอร์มือถือ</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="phone" placeholder="เบอร์มือถือ" outlined dense :rules="Rules.tel" :maxlength="maxPhone" v-mask="'###-###-####'" readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>เบอร์โทรสาร</span>
                  <v-text-field  v-model="fax" placeholder="เบอร์โทรสาร" outlined dense :maxlength="maxTel" v-mask="'##-###-####'" readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- ที่อยู่ตามใบกำกับภาษีของร้าน -->
              <v-card-title class="pl-0">
                <h4 style="font-weight: 700; font-size: 18px;">ที่อยู่ตามใบกำกับภาษีของร้าน</h4>
              </v-card-title>
              <!-- ที่อยู่ -->
              <v-row>
                <v-col cols="12" md="6" sm="4">
                  <span>เลขรหัสประจำที่อยู่ </span>
                  <v-text-field  v-model="addressCode" placeholder="เลขรหัสประจำที่อยู่" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="8">
                  <span>ที่อยู่</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="address" placeholder="ที่อยู่" outlined dense :rules="Rules.empty" readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- เลขที่ห้อง  -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>ห้องเลขที่ </span>
                  <v-text-field  v-model="roomNo" placeholder="ห้องเลขที่" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ชั้นที่ </span>
                  <v-text-field  v-model="floor" placeholder="ชั้นที่" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>อาคาร </span>
                  <v-text-field  v-model="buildingName" placeholder="อาคาร" outlined dense readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- หมู่บ้าน -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>หมู่บ้าน </span>
                  <v-text-field  v-model="houseName" placeholder="หมู่บ้าน" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>หมู่ที่ </span>
                  <v-text-field  v-model="houseGroup" placeholder="หมู่ที่" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ตรอก/ซอย </span>
                  <v-text-field  v-model="alley" placeholder="ตรอก/ซอย" outlined dense readonly>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- แยก -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>แยก </span>
                <v-text-field v-model="cross" placeholder="แยก" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ถนน </span>
                  <v-text-field v-model="road" placeholder="ถนน" outlined dense readonly>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>จังหวัด</span><span style="color: red;"> *</span>
                    <!-- <addressinput-province label="" v-model="province" placeholder="จังหวัด" required/> -->
                    <v-text-field outlined dense v-model="provinceText" placeholder="จังหวัด" :rules="Rules.empty" readonly></v-text-field>
                    <!-- <v-autocomplete outlined dense :items="itemProvince" v-model="province" item-text="name_th" item-value="id" placeholder="จังหวัด" :rules="Rules.empty" readonly></v-autocomplete> -->
                </v-col>
              </v-row>
              <!-- ตำบล -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
                    <v-text-field outlined dense v-model="districtText" placeholder="อำเภอ/เขต" :rules="Rules.empty" readonly></v-text-field>
                    <!-- <v-autocomplete outlined dense :items="itemDistrict" v-model="district" item-text="name_th" item-value="id" placeholder="อำเภอ" :rules="Rules.empty" readonly></v-autocomplete> -->
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ตำบล/แขวง</span><span style="color: red;"> *</span>
                    <v-text-field outlined dense v-model="subdistricttext" placeholder="ตำบล/แขวง" :rules="Rules.empty" readonly></v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                  <v-text-field v-model="zipcodeText" placeholder="รหัสไปรษณีย์" required outlined dense readonly/>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-form>
        <!-- ลงทะเบียนนิติบุคคลแบบ นิติบุคคลอื่นๆ -->
        <!-- นิติบุคคลอื่นๆ -->
        <v-form ref="FormcreateOther" :lazy-validation="lazy3" v-if="showFormOther === true && DetailBusinessOther.length === 0 && selectTypeOther !== ''">
          <div :style="showFormOther === true ? 'dispaly: block' : 'display: none'">
            <v-card outlined class="mt-0 mx-4 mb-4" style="border: 1px solid #27AB9C;">
              <v-card-title>
                <h4 style="font-weight: 700; font-size: 18px;">ข้อมูลนิติบุคคลอื่นๆ</h4>
              </v-card-title>
              <v-card-text>
                <v-row>
                  <!-- ประเภทกิจการ -->
                  <v-col cols="12" md="6" sm="6">
                    <span>ประเภทกิจการ</span><span style="color: red;"> *</span>
                    <v-select
                      v-model="businessTypeOther"
                      :items="businessTypeTHItemOther"
                      item-text="nameTH"
                      item-value="value"
                      placeholder="ประเภทกิจการ"
                      outlined
                      dense
                      :rules="Rules.empty"
                    >
                    </v-select>
                  </v-col>
                </v-row>
                <!-- ชื่อกิจการ -->
                <v-row>
                  <v-col cols="12" md="6" sm="6">
                    <span>ชื่อกิจการ(ไทย)</span><span style="color: red;"> *</span>
                    <v-text-field v-model="businessNameTHOther" placeholder="ชื่อกิจการ(ไทย)" outlined dense :rules="Rules.businessNameTHOtherRules" @keypress="isLetterThai($event)">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span>ชื่อกิจการ(อังกฤษ)</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="businessNameENOther" placeholder="ชื่อกิจการ(อังกฤษ)" outlined dense :rules="Rules.businessNameENOtherRules" @keypress="isLetterEng($event)">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- เลขประจำตัวผู้เสียภาษี -->
                <v-row>
                  <v-col cols="12" md="12" sm="12">
                    <span>เลขประจำตัวผู้เสียภาษี(เจ้าของกิจการ)</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="taxIdOther" placeholder="เลขประจำตัวผู้เสียภาษี(เจ้าของกิจการ)" outlined dense :rules="Rules.empty" :maxlength="max" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')">
                    </v-text-field>
                  </v-col>
                  <!-- <v-col cols="12" md="3">
                    <v-btn color="#3cc474" dark rounded class="mt-5 px-4">ตรวจสอบ ONE ID</v-btn>
                  </v-col> -->
                </v-row>
                <!-- เบอร์โทรศัพท์ -->
                <v-row>
                  <v-col cols="12" md="6" sm="6">
                    <span>อีเมล</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="mailOther" placeholder="อีเมล" outlined dense :rules="Rules.emailRules" @keypress="isLetterEngEmail($event)">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span>เบอร์โทรศัพท์</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="telOther" placeholder="เบอร์โทรศัพท์" outlined dense :maxlength="12" :rules="Rules.tel" v-mask="'###-###-####'">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- สาขา -->
                <v-row>
                  <v-col cols="12" md="6" sm="6">
                    <span>ชื่อสาขา</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="branchNameOther" placeholder="ชื่อสาขา" outlined dense :rules="Rules.empty">
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span>รหัสสาขา</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="branchCodeOther" placeholder="รหัสสาขา (กรอกได้เฉพาะตัวเลข)" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :disabled='disabledBranchCodeOther' :maxlength='5'  :rules="Rules.branchno">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- ที่อยู่กิจการ -->
                <v-card-title class="pl-0">
                  <h4 style="font-weight: 700; font-size: 18px;">ที่อยู่กิจการ</h4>
                </v-card-title>
                <!-- ที่อยู่ -->
                <v-row>
                  <v-col cols="12" md="6" sm="4">
                    <span>เลขรหัสประจำที่อยู่ </span>
                    <v-text-field  v-model="addressCodeOther" placeholder="เลขรหัสประจำที่อยู่" outlined dense >
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" sm="8">
                    <span>ที่อยู่</span><span style="color: red;"> *</span>
                    <v-text-field  v-model="houseNoOther" placeholder="ที่อยู่" outlined dense :rules="Rules.empty">
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- เลขที่ห้อง  -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>ห้องเลขที่ </span>
                    <v-text-field  v-model="roomNoOther" placeholder="ห้องเลขที่" outlined dense >
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ชั้นที่ </span>
                    <v-text-field  v-model="floorOther" placeholder="ชั้นที่" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>อาคาร </span>
                    <v-text-field  v-model="buildingNameOther" placeholder="อาคาร" outlined dense>
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- หมู่บ้าน -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>หมู่บ้าน </span>
                    <v-text-field  v-model="houseNameOther" placeholder="หมู่บ้าน" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>หมู่ที่ </span>
                    <v-text-field  v-model="houseGroupOther" placeholder="หมู่ที่" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ตรอก/ซอย </span>
                    <v-text-field  v-model="alleyOther" placeholder="ตรอก/ซอย" outlined dense>
                    </v-text-field>
                  </v-col>
                </v-row>
                <!-- แยก -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>แยก </span>
                  <v-text-field v-model="crossOther" placeholder="แยก" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ถนน </span>
                    <v-text-field v-model="roadOther" placeholder="ถนน" outlined dense>
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
                    <addressinput-subdistrict :rules="Rules.empty" label="" v-model="subdistrictOther" v-if="typeTaxIDOther === 'noneOther'" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุตำบล/แขวง"/>
                    <div v-if="typeTaxIDOther === 'noneOther' && checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <!-- <v-autocomplete outlined dense :items="itemProvince" v-model="province" item-text="name_th" item-value="id" placeholder="จังหวัด" :rules="Rules.empty"  v-if="typeTaxID === 'none' && "></v-autocomplete> -->
                    <v-text-field outlined dense v-model="subdistricttext" placeholder="ตำบล/แขวง" :rules="Rules.empty" v-if="typeTaxIDOther !== 'noneOther'" :readonly="typeTaxIDOther === 'TaxByOneID' ? true : false"></v-text-field>
                  </v-col>
                </v-row>
                <!-- ตำบล -->
                <v-row>
                  <v-col cols="12" md="4" sm="4">
                    <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
                    <addressinput-district label="" v-if="typeTaxIDOther === 'noneOther'" v-model="districtOther" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุอำเภอ/เขต" />
                    <div v-if="typeTaxIDOther === 'noneOther' && checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <!-- <v-autocomplete outlined dense :items="itemDistrict" v-model="district" item-text="name_th" item-value="id" placeholder="อำเภอ" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                    <v-text-field outlined dense v-model="districttext" placeholder="อำเภอ/เขต" :rules="Rules.empty" v-if="typeTaxIDOther !== 'noneOther'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>จังหวัด</span><span style="color: red;"> *</span>
                    <addressinput-province label="" v-if="typeTaxIDOther === 'noneOther'" v-model="provinceOther" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                    <div v-if="typeTaxIDOther === 'noneOther' && checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <!-- <v-autocomplete outlined dense :items="itemSubdistrict" v-model="subdistrict" item-text="name_th" item-value="id" placeholder="ตำบล" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                    <v-text-field outlined dense v-model="provincetext" placeholder="จังหวัด" :rules="Rules.empty" v-if="typeTaxIDOther !== 'noneOther'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" sm="4">
                    <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                    <addressinput-zipcode label="" v-if="typeTaxIDOther === 'noneOther'" numbered v-model="zipcodeOther" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                    <div v-else-if="typeTaxIDOther === 'noneOther' && checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                    <v-text-field disabled v-if="typeTaxIDOther !== 'noneOther'" label="" v-model="zipcode" placeholder="รหัสไปรษณีย์" required outlined dense/>
                  </v-col>
                </v-row>
              </v-card-text>
              <!-- ปุ่ม -->
              <v-card-actions class="mb-4">
                <v-spacer></v-spacer>
                <v-btn outlined color="#27AB9C" class="pl-8 pr-8" @click="cancelPage()">ยกเลิก</v-btn>
                <v-btn color="#27AB9C" dark @click="createbizOther()" class="pl-4 pr-4">ลงทะเบียน</v-btn>
              </v-card-actions>
            </v-card>
          </div>
        </v-form>
        <!-- ลงทะเบียนนิติบุคคลเรียบร้อยแบบ นิติบุคคลอื่นๆ -->
        <v-form ref="FormcreateOther" :lazy-validation="lazy" v-if="showFormOther === true && DetailBusinessOther.length !== 0 && selectTypeOther !== ''">
          <v-card outlined class="mt-0 mb-4" style="border: 1px solid #27AB9C;">
            <v-card-title>
              <h4 style="font-weight: 700; font-size: 18px;">ข้อมูลนิติบุคคลอื่นๆ</h4>
              <span v-if="DetailBusinessOther.status_approve === 'pending'">
                <v-chip small class="ml-3" color="#FCF0DA" text-color="#FAAD14">รออนุมัติ</v-chip>
              </span>
              <span v-else-if="DetailBusinessOther.status_approve === 'approve'">
                <v-chip small class="ml-3" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
              </span>
              <span v-else>
                <v-chip small class="ml-3" color="#FBE5E4" text-color="#F5222D">ไม่อนุมัติ</v-chip>
              </span>
            </v-card-title>
            <v-card-text>
              <v-row>
                <!-- ประเภทกิจการ -->
                <v-col cols="12" md="6" sm="12">
                  <span>ประเภทกิจการ</span><span style="color: red;"> *</span>
                  <v-select
                    v-model="businessTypeOther"
                    :items="businessTypeTHItemOther"
                    item-text="nameTH"
                    item-value="value"
                    placeholder="ประเภทกิจการ"
                    outlined
                    readonly
                    dense
                    :rules="Rules.empty"
                  >
                  </v-select>
                </v-col>
              </v-row>
              <!-- ชื่อกิจการ -->
              <v-row>
                <v-col cols="12" md="6" sm="6">
                  <span>ชื่อกิจการ(ไทย)</span><span style="color: red;"> *</span>
                  <v-text-field v-model="DetailBusinessOther.first_name_th" placeholder="ชื่อกิจการ(ไทย)" outlined dense readonly :rules="Rules.businessNameTHOtherRules" @keypress="isLetterThai($event)">
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span>ชื่อกิจการ(อังกฤษ)</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusinessOther.first_name_eng" placeholder="ชื่อกิจการ(อังกฤษ)" outlined dense readonly :rules="Rules.businessNameENOtherRules" @keypress="isLetterEng($event)">
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- เลขประจำตัวผู้เสียภาษี -->
              <v-row>
                <v-col cols="12" md="12" sm="12">
                  <span>เลขประจำตัวผู้เสียภาษี(เจ้าของกิจการ)</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusinessOther.id_card_num" placeholder="เลขประจำตัวผู้เสียภาษี(เจ้าของกิจการ)" outlined dense readonly :rules="Rules.empty" :maxlength="max" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')">
                  </v-text-field>
                </v-col>
                <!-- <v-col cols="12" md="3">
                  <v-btn color="#3cc474" dark rounded class="mt-5 px-4">ตรวจสอบ ONE ID</v-btn>
                </v-col> -->
              </v-row>
              <!-- เบอร์โทรศัพท์ -->
              <v-row>
                <v-col cols="12" md="6" sm="6">
                  <span>อีเมล</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusinessOther.email" readonly placeholder="อีเมล" outlined dense :rules="Rules.emailRules" @keypress="isLetterEngEmail($event)">
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span>เบอร์โทรศัพท์</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusinessOther.mobile_no" readonly placeholder="เบอร์โทรศัพท์" outlined dense :maxlength="12" :rules="Rules.tel" v-mask="'###-###-####'">
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- สาขา -->
              <v-row>
                <v-col cols="12" md="6" sm="6">
                  <span>ชื่อสาขา</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusinessOther.branch_name" readonly placeholder="ชื่อสาขา" outlined dense :rules="Rules.empty">
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <span>รหัสสาขา</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="DetailBusinessOther.branch_no" readonly placeholder="รหัสสาขา (กรอกได้เฉพาะตัวเลข)" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :disabled='disabledBranchCodeOther' :maxlength='5'  :rules="Rules.branchno">
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- ที่อยู่กิจการ -->
              <v-card-title class="pl-0">
                <h4 style="font-weight: 700; font-size: 18px;">ที่อยู่กิจการ</h4>
              </v-card-title>
              <!-- ที่อยู่ -->
              <v-row>
                <v-col cols="12" md="6" sm="4">
                  <span>เลขรหัสประจำที่อยู่ </span>
                  <v-text-field  v-model="addressCodeOther" readonly placeholder="เลขรหัสประจำที่อยู่" outlined dense >
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="6" sm="8">
                  <span>ที่อยู่</span><span style="color: red;"> *</span>
                  <v-text-field  v-model="houseNoOther" readonly placeholder="ที่อยู่" outlined dense :rules="Rules.empty">
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- เลขที่ห้อง  -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>ห้องเลขที่ </span>
                  <v-text-field  v-model="roomNoOther" readonly placeholder="ห้องเลขที่" outlined dense >
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ชั้นที่ </span>
                  <v-text-field  v-model="floorOther" readonly placeholder="ชั้นที่" outlined dense>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>อาคาร </span>
                  <v-text-field  v-model="buildingNameOther" readonly placeholder="อาคาร" outlined dense>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- หมู่บ้าน -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>หมู่บ้าน </span>
                  <v-text-field  v-model="houseNameOther" readonly placeholder="หมู่บ้าน" outlined dense>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>หมู่ที่ </span>
                  <v-text-field  v-model="houseGroupOther" readonly placeholder="หมู่ที่" outlined dense>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ตรอก/ซอย </span>
                  <v-text-field  v-model="alleyOther" readonly placeholder="ตรอก/ซอย" outlined dense>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- แยก -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>แยก </span>
                <v-text-field v-model="crossOther" readonly placeholder="แยก" outlined dense>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ถนน </span>
                  <v-text-field v-model="roadOther" readonly placeholder="ถนน" outlined dense>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
                  <v-text-field outlined dense v-model="subdistrictTextOther" readonly placeholder="ตำบล/แขวง" :rules="Rules.empty"></v-text-field>
                </v-col>
              </v-row>
              <!-- ตำบล -->
              <v-row>
                <v-col cols="12" md="4" sm="4">
                  <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
                  <v-text-field outlined dense v-model="districtTextOther" readonly placeholder="อำเภอ/เขต" :rules="Rules.empty"></v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>จังหวัด</span><span style="color: red;"> *</span>
                  <v-text-field outlined dense v-model="provinceTextOther"  placeholder="จังหวัด" readonly :rules="Rules.empty"></v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="4">
                  <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                  <v-text-field disabled v-model="zipcodeTextOther" placeholder="รหัสไปรษณีย์" readonly required outlined dense/>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-form>
      </v-container>
      <!-- ปุ่ม -->
      <!-- <v-row justify="center">
        <v-card-actions>
          <v-btn rounded color="success" @click="createbiz()">ลงทะเบียน</v-btn>
        </v-card-actions>
      </v-row> -->
    </v-card>
  </v-container>
</template>

<script>
import Vue from 'vue'
// import Province from '@/Thailand_Address/province'
// import District from '@/Thailand_Address/district'
// import Subdistrict from '@/Thailand_Address/subdistrict'
import draggable from 'vuedraggable'
import ModalImage from '@/components/Modal/OPS'
import Address2021 from '@/Thailand_Address/address2021'
import VueMask from 'v-mask'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
Vue.use(VueThailandAddress)
Vue.use(VueMask)
export default {
  components: {
    draggable,
    ModalImage
  },
  data () {
    return {
      tab: null,
      DetailBusiness: [],
      DetailBusinessOther: [],
      itemProvince: [],
      itemDistrict: [],
      itemSubdistrict: [],
      taxIDSearch: '',
      taxIDSearchOther: '',
      lazy: false,
      lazy1: false,
      lazy2: false,
      lazy3: false,
      showtaxSearch: false,
      showtaxSearchOther: false,
      max: 13,
      maxTel: 13,
      subdistrictValue: '',
      provincetext: '',
      provinceValue: '',
      districttext: '',
      districtValue: '',
      maxPhone: 12,
      selectTypeOther: null,
      selectType: null,
      disabledBranchCode: true,
      checkFirstTime: 0,
      disabledBranchCodeOther: true,
      businessType: '',
      checkeKYCUser: false,
      businessTypeTHItem: [
        { nameTH: 'ห้างหุ้นส่วนสามัญ', nameEN: 'Ordinary Partnership', value: 1 },
        { nameTH: 'ห้างหุ้นส่วนจำกัด', nameEN: 'Limited Partnership', value: 2 },
        { nameTH: 'บริษัทจำกัด', nameEN: 'Company Limited', value: 3 },
        { nameTH: 'บริษัทมหาชนจำกัด', nameEN: 'Public Limited Company', value: 4 },
        { nameTH: 'นิติบุคคลอื่นๆ ภายใต้กฎหมายเฉพาะ', nameEN: 'Other', value: 5 }
      ],
      businessNameTH: '',
      businessDocNameTH: '',
      businessNameEN: '',
      businessDocNameEN: '',
      taxId: '',
      branchName: '',
      branchCode: '',
      mail: '',
      tel: '',
      phone: '',
      fax: '',
      addressCode: '',
      address: '',
      roomNo: '',
      floor: '',
      buildingName: '',
      houseName: '',
      houseNo: '',
      houseGroup: '',
      alley: '',
      cross: '',
      road: '',
      province: '',
      district: '',
      subdistrict: '',
      provinceOther: '',
      districtOther: '',
      subdistrictOther: '',
      zipcodeOther: '',
      checkresult: '',
      zipcode: '',
      account_title_th: '',
      account_title_eng: '',
      showForm: false,
      showFormOther: false,
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'ข้อมูลนิติบุคคล',
          disabled: true,
          href: ''
        }
      ],
      // zipCodeData: [],
      // districtData: [],
      // subDistrictData: [],
      // provinceData: [],
      Data: require('thai-data'),
      Rules: {
        required: [{ required: true, message: 'กรุณากรอกข้อมูล' }],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        branchno: [
          v => !!v || 'กรุณากรอกรหัสสาขา',
          v => (v.length >= 5 && v !== null) || 'กรุณากรอกรหัสสาขาให้ครบ 5 ตัว'
        ],
        shopNameTH: [
          v => !!v || 'กรุณากรอกชื่อร้าน'
        ],
        shopNameEN: [
          v => !!v || 'กรุณากรอกชื่อร้านภาษาอังกฤษ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ]
      },
      fileRecords: [],
      fileRecords2: [],
      uploadUrl: 'https://www.mocky.io/v2/5d4fb20b3000005c111099e3',
      uploadHeaders: { 'X-Test-Header': 'vue-file-agent' },
      fileRecordsForUpload: [], // maintain an upload queue
      BusinessID: '',
      subdistricttext: '',
      zipcodeText: '',
      provinceText: '',
      districtText: '',
      businessTypeOther: '',
      checkTypeSelect: false,
      checkSubDistrictError: false,
      checkDistrictError: false,
      checkProvinceError: false,
      checkZipcodeError: false,
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      businessTypeTHItemOther: [
        { nameTH: 'การค้าเร่ การค้าแผงลอย', value: '0' },
        { nameTH: 'กิจการเพื่อการบำรุงศาสนาหรือเพื่อการกุศล', value: '1' },
        { nameTH: 'กิจการของนิติบุคคลซึ่งได้มีพระราชบัญญัติ หรือพระราชกฤษฎีกาจัดตั้งขึ้น', value: '2' },
        { nameTH: 'กิจการของกระทรวง ทบวง กรม', value: '3' },
        { nameTH: 'กิจการของมูลนิธิ สมาคม สหกรณ์', value: '4' },
        { nameTH: 'พาณิชยกิจซึ่งรัฐมนตรีได้ประกาศในราชกิจจานุเบกษา', value: '5' },
        { nameTH: 'อื่น ๆ', value: '6' }
      ],
      businessNameTHOther: '',
      businessNameENOther: '',
      taxIdOther: '',
      mailOther: '',
      telOther: '',
      branchNameOther: '',
      branchCodeOther: '',
      addressCodeOther: '',
      houseNoOther: '',
      roomNoOther: '',
      floorOther: '',
      buildingNameOther: '',
      houseNameOther: '',
      houseGroupOther: '',
      alleyOther: '',
      crossOther: '',
      roadOther: '',
      typeTaxIDOther: '',
      subdistrictTextOther: '',
      districtTextOther: '',
      provinceTextOther: '',
      zipcodeTextOther: ''
    }
  },
  async created () {
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      // this.itemProvince = Province.provinces
      // const GetallData = this.Data.allData().map((item) => {
      //   return item.zipCode
      // })
      // this.zipCodeData = GetallData
      this.$store.commit('openLoader')
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var data = {
      //   role_user: dataRole.role
      // }
      // await this.$store.dispatch('actionsUserDetailPage', data)
      // var response = await this.$store.state.ModuleUser.stateUserDetailPage
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      this.BusinessID = response.data.list_business[0].id
      if (this.checkFirstTime === 0) {
        this.checkeKYC()
      }
      await this.getDetailBusinessData()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    checkFirstTime (val) {
      // console.log('checkFirstTime ==========>', val)
    },
    taxId (val) {
      if (val !== '') {
        this.disabledBranchCode = false
      } else {
        this.disabledBranchCode = true
      }
    },
    taxIdOther (val) {
      if (val !== '') {
        this.disabledBranchCodeOther = false
      } else {
        this.disabledBranchCodeOther = true
      }
    },
    tab (val) {
      // console.log(val)
      if (this.DetailBusiness.length === 0 && this.DetailBusinessOther.length === 0) {
        if (val === 'tab-1') {
          this.selectType = ''
          this.selectTypeOther = ''
          this.typeTaxID = ''
          this.showForm = false
          this.showFormOther = false
          this.showtaxSearch = false
          this.showtaxSearchOther = false
          this.$refs.FormcreateOther.reset()
          this.$refs.Formcreate.reset()
          this.checkSubDistrictError = false
          this.checkDistrictError = false
          this.checkProvinceError = false
          this.checkZipcodeError = false
          this.province = ''
          this.district = ''
          this.subdistrict = ''
          this.zipcode = ''
          this.provinceOther = ''
          this.districtOther = ''
          this.subdistrictOther = ''
          this.zipcodeOther = ''
        } else {
          this.selectType = ''
          this.selectTypeOther = ''
          this.typeTaxID = ''
          this.showForm = false
          this.showFormOther = false
          this.showtaxSearch = false
          this.showtaxSearchOther = false
          this.$refs.FormcreateOther.reset()
          this.$refs.Formcreate.reset()
          this.checkSubDistrictError = false
          this.checkDistrictError = false
          this.checkProvinceError = false
          this.checkZipcodeError = false
          this.province = ''
          this.district = ''
          this.subdistrict = ''
          this.zipcode = ''
          this.provinceOther = ''
          this.districtOther = ''
          this.subdistrictOther = ''
          this.zipcodeOther = ''
        }
      } else {
        if (val === 'tab-1') {
          this.showForm = true
          this.showFormOther = false
          if (this.DetailBusiness.length === 0 && this.DetailBusinessOther.length !== 0) {
            this.province = ''
            this.district = ''
            this.subdistrict = ''
            this.zipcode = ''
            this.selectTypeOther = 'noneOther'
            this.selectType = ''
          } else if (this.DetailBusinessOther.length === 0 && this.DetailBusiness.length !== 0) {
            this.provinceOther = ''
            this.districtOther = ''
            this.subdistrictOther = ''
            this.zipcodeOther = ''
            this.selectTypeOther = ''
            this.selectType = 'none'
          } else {
            this.selectTypeOther = 'noneOther'
            this.selectType = 'none'
          }
        } else {
          this.showForm = false
          this.showFormOther = true
          if (this.DetailBusiness.length === 0 && this.DetailBusinessOther.length !== 0) {
            this.province = ''
            this.district = ''
            this.subdistrict = ''
            this.zipcode = ''
            this.selectTypeOther = 'noneOther'
            this.selectType = ''
          } else if (this.DetailBusinessOther.length === 0 && this.DetailBusiness.length !== 0) {
            this.provinceOther = ''
            this.districtOther = ''
            this.subdistrictOther = ''
            this.zipcodeOther = ''
            this.selectTypeOther = ''
            this.selectType = 'none'
          } else {
            this.selectTypeOther = 'noneOther'
            this.selectType = 'none'
          }
        }
      }
    },
    selectTypeOther (val) {
      // console.log(val)
      if (val === 'TaxByOneIDOther') {
        this.typeTaxIDOther = val
        this.$refs.FormTaxIDOther.resetValidation()
        this.showtaxSearchOther = true
        this.taxIDSearchOther = ''
        this.$refs.FormcreateOther.reset()
        this.showFormOther = false
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else if (val === 'noneOther') {
        this.typeTaxIDOther = val
        this.$refs.FormTaxIDOther.resetValidation()
        this.showtaxSearchOther = false
        this.taxIDSearchOther = ''
        this.showFormOther = true
        this.$refs.FormcreateOther.reset()
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      }
    },
    selectType (val) {
      // console.log(val)
      if (this.DetailBusiness.length === 0) {
        if (val === 'TaxByRevenue') {
          this.typeTaxID = val
          this.$refs.Formcreate.reset()
          this.showForm = false
          this.showtaxSearch = true
          this.taxIDSearch = ''
          this.$refs.FormTaxID.resetValidation()
          this.checkSubDistrictError = false
          this.checkDistrictError = false
          this.checkProvinceError = false
          this.checkZipcodeError = false
        } else if (val === 'TaxByOneID') {
          this.typeTaxID = val
          this.$refs.Formcreate.reset()
          this.showForm = false
          this.showtaxSearch = true
          this.taxIDSearch = ''
          this.checkSubDistrictError = false
          this.checkDistrictError = false
          this.checkProvinceError = false
          this.checkZipcodeError = false
          this.$refs.FormTaxID.resetValidation()
        } else if (val === 'none') {
          this.$refs.Formcreate.reset()
          this.typeTaxID = val
          this.showForm = true
          this.showtaxSearch = false
          this.showFormOther = false
          this.checkSubDistrictError = false
          this.checkDistrictError = false
          this.checkProvinceError = false
          this.checkZipcodeError = false
        }
      }
    },
    // province (val) {
    //   console.log('province pv', val)
    //   const result = District.districts.filter((data) => {
    //     return data.province_id === val
    //   })
    //   this.itemDistrict = result
    //   this.subdistrict = ''
    //   this.zipcode = ''
    // },
    // district (val) {
    //   console.log('district pv', val)
    //   const result = Subdistrict.subdistricts.filter((data) => {
    //     // console.log(data)
    //     return data.amphure_id === val
    //   })
    //   // console.log(result)
    //   this.itemSubdistrict = result
    //   this.zipcode = ''
    // },
    // subdistrict (val) {
    //   console.log('subdistrict pv', val)
    //   const result = Subdistrict.subdistricts.filter((data) => {
    //     return data.id === val
    //   })
    //   this.zipcode = result[0].zip_code
    //   console.log('zipcode', this.zipcode)
    // },
    businessType (val) {
      // console.log('val', val)
      if (val === 1) {
        this.account_title_th = 'ห้างหุ้นส่วนสามัญ'
        this.account_title_eng = 'Ordinary Partnership'
      } else if (val === 2) {
        this.account_title_th = 'ห้างหุ้นส่วนจำกัด'
        this.account_title_eng = 'Limited Partnership'
      } else if (val === 3) {
        this.account_title_th = 'บริษัทจำกัด'
        this.account_title_eng = 'Company Limited'
      } else if (val === 4) {
        this.account_title_th = 'บริษัทมหาชนจำกัด'
        this.account_title_eng = 'Public Limited Company'
      } else {
        this.account_title_th = 'นิติบุคคลอื่นๆ ภายใต้กฏหมายเฉพาะ'
        this.account_title_eng = 'Other'
      }
      // console.log(this.account_title_eng, this.account_title_th)
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      // this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      // console.log(val)
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    },
    subdistrictOther (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcodeOther = ''
        this.districtOther = ''
        this.provinceOther = ''
      }
    },
    districtOther (val) {
      this.checkDistrictError = false
      // this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcodeOther = ''
        this.subdistrictOther = ''
        this.provinceOther = ''
      }
    },
    provinceOther (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcodeOther = ''
        this.subdistrictOther = ''
        this.districtOther = ''
      }
    },
    zipcodeOther (val) {
      this.checkZipcodeError = false
      // console.log(val)
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrictOther = ''
        this.districtOther = ''
        this.provinceOther = ''
      }
    }
  },
  beforeDestroy () {
    this.checkFirstTime = 0
  },
  methods: {
    async checkeKYC () {
      // alert('123456789')
      // this.checkeKYCUser = false
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsCheckeKYC')
      var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.checkeKYCUser = response.data.eKYC_approve === 'yes'
        if (response.data.eKYC_approve === 'no') {
          this.$store.commit('closeLoader')
          if (this.checkFirstTime !== 0) {
            this.checkeKYCUser = false
            if (this.MobileSize) {
              this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false })
            } else {
              this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false })
            }
          }
        } else {
          if (response.data.eKYC_expire === 'yes') {
            if (this.MobileSize) {
              this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false })
            } else {
              this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false })
            }
          } else {
            this.DetailBusinessOther = await response.data.data_citizen
            if (this.DetailBusinessOther.length !== 0) {
              this.$store.commit('closeLoader')
              this.typeTaxIDOther = 'noneOther'
              this.showFormOther = true
              this.addressCodeOther = this.DetailBusinessOther.address.house_code !== null ? this.DetailBusinessOther.address.house_code : '-'
              this.houseNoOther = this.DetailBusinessOther.address.house_no !== null ? this.DetailBusinessOther.address.house_no : '-'
              this.roomNoOther = this.DetailBusinessOther.address.room_no !== null ? this.DetailBusinessOther.address.room_no : '-'
              this.floorOther = this.DetailBusinessOther.address.floor !== null ? this.DetailBusinessOther.address.floor : '-'
              this.buildingNameOther = this.DetailBusinessOther.address.building_name !== null ? this.DetailBusinessOther.address.building_name : '-'
              this.houseNameOther = this.DetailBusinessOther.address.moo_ban !== null ? this.DetailBusinessOther.address.moo_ban : '-'
              this.houseGroupOther = this.DetailBusinessOther.address.moo_no !== null ? this.DetailBusinessOther.address.moo_no : '-'
              this.alleyOther = this.DetailBusinessOther.address.soi !== null ? this.DetailBusinessOther.address.soi : '-'
              this.crossOther = this.DetailBusinessOther.address.yaek !== null ? this.DetailBusinessOther.address.yaek : '-'
              this.roadOther = this.DetailBusinessOther.address.street !== null ? this.DetailBusinessOther.address.street : '-'
              this.subdistrictTextOther = this.DetailBusinessOther.address.tambon
              this.districtTextOther = this.DetailBusinessOther.address.amphoe
              this.provinceTextOther = this.DetailBusinessOther.address.province
              this.zipcodeTextOther = this.DetailBusinessOther.address.zipcode
              if (this.DetailBusinessOther.account_title_th === 'การค้าเร่ การค้าแผงลอย') {
                this.businessTypeOther = '0'
              } else if (this.DetailBusinessOther.account_title_th === 'กิจการเพื่อการบำรุงศาสนาหรือเพื่อการกุศล') {
                this.businessTypeOther = '1'
              } else if (this.DetailBusinessOther.account_title_th === 'กิจการของนิติบุคคลซึ่งได้มีพระราชบัญญัติ หรือพระราชกฤษฎีกาจัดตั้งขึ้น') {
                this.businessTypeOther = '2'
              } else if (this.DetailBusinessOther.account_title_th === 'กิจการของกระทรวง ทบวง กรม') {
                this.businessTypeOther = '3'
              } else if (this.DetailBusinessOther.account_title_th === 'กิจการของมูลนิธิ สมาคม สหกรณ์') {
                this.businessTypeOther = '4'
              } else if (this.DetailBusinessOther.account_title_th === 'พาณิชยกิจซึ่งรัฐมนตรีได้ประกาศในราชกิจจานุเบกษา') {
                this.businessTypeOther = '5'
              } else {
                this.businessTypeOther = '6'
              }
            } else {
              this.DetailBusinessOther = []
              this.$store.commit('closeLoader')
            }
          }
        }
      }
      this.checkFirstTime = this.checkFirstTime + 1
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    isLetterEng (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z0-9():&.,-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    isLetterEngEmail (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z0-9_@.,/#&+-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    isLetterThai (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[ก-๏0-9():.,-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    DownloadForm (val) {
      if (val === 'POA') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88.docx')
      } else if (val === 'POAS') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%87.docx')
      }
    },
    OpenModal () {
      this.$EventBus.$emit('openOPS')
    },
    async getDetailBusinessData () {
      this.$store.commit('openLoader')
      var data = {
        business_id: this.BusinessID
      }
      await this.$store.dispatch('actionDetailBusiness', data)
      var response = await this.$store.state.ModuleBusiness.stateDetailBusiness
      // console.log('response for detail business ===>', response.data)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.showForm = true
        this.selectType = 'none'
        this.DetailBusiness = await response.data
        this.tel = this.DetailBusiness.tel_no !== null ? this.DetailBusiness.tel_no : '-'
        this.phone = this.DetailBusiness.mobile_no !== null ? this.DetailBusiness.mobile_no : '-'
        this.fax = this.DetailBusiness.address.fax_number !== null ? this.DetailBusiness.address.fax_number : '-'
        this.addressCode = this.DetailBusiness.address.house_code !== null ? this.DetailBusiness.address.house_code : '-'
        this.address = this.DetailBusiness.address.house_no !== null ? this.DetailBusiness.address.house_no : '-'
        this.roomNo = this.DetailBusiness.address.room_no !== null ? this.DetailBusiness.address.room_no : '-'
        this.buildingName = this.DetailBusiness.address.building_name !== null ? this.DetailBusiness.address.building_name : '-'
        this.floor = this.DetailBusiness.address.floor !== null ? this.DetailBusiness.address.floor : '-'
        this.houseName = this.DetailBusiness.address.moo_ban !== null ? this.DetailBusiness.address.moo_ban : '-'
        this.houseGroup = this.DetailBusiness.address.moo_no !== null ? this.DetailBusiness.address.moo_no : '-'
        this.alley = this.DetailBusiness.address.soi !== null ? this.DetailBusiness.address.soi : '-'
        this.road = this.DetailBusiness.address.street !== null ? this.DetailBusiness.address.street : '-'
        this.cross = this.DetailBusiness.address.yaek !== null ? this.DetailBusiness.address.yaek : '-'
        this.province = this.DetailBusiness.address.province
        this.district = this.DetailBusiness.address.amphoe
        this.subdistricttext = this.DetailBusiness.address.tambon
        this.districtText = this.DetailBusiness.address.amphoe
        this.provinceText = this.DetailBusiness.address.province
        this.zipcodeText = this.DetailBusiness.address.zipcode
        // this.subdistrict = this.DetailBusiness.address.tambon
        // if (typeof this.DetailBusiness.address.tambon === 'number') {
        //   const result = Subdistrict.subdistricts.filter((data) => {
        //     return data.id === this.DetailBusiness.address.tambon
        //   })
        //   this.subdistricttext = result[0].name_th
        // } else {
        //   this.subdistricttext = this.DetailBusiness.address.tambon
        // }
        // if (typeof this.DetailBusiness.address.province === 'number') {
        //   const result = Province.provinces.filter((data) => {
        //     return data.id === this.DetailBusiness.address.province
        //   })
        //   this.provinceText = result[0].name_th
        // } else {
        //   this.provinceText = this.DetailBusiness.address.province
        // }
        // if (typeof this.DetailBusiness.address.amphoe === 'number') {
        //   const result = District.districts.filter((data) => {
        //     return data.id === this.DetailBusiness.address.amphoe
        //   })
        //   this.districtText = result[0].name_th
        // } else {
        //   this.districtText = this.DetailBusiness.address.amphoe
        // }
        // console.log('subdistricttext', this.subdistricttext)
        // this.zipcodeText = this.DetailBusiness.address.zipcode
        if (this.DetailBusiness.account_title_th === 'ห้างหุ้นส่วนสามัญ') {
          this.businessType = 1
        } else if (this.DetailBusiness.account_title_th === 'ห้างหุ้นส่วนจำกัด') {
          this.businessType = 2
        } else if (this.DetailBusiness.account_title_th === 'บริษัทจำกัด') {
          this.businessType = 3
        } else if (this.DetailBusiness.account_title_th === 'บริษัทมหาชนจำกัด') {
          this.businessType = 4
        } else {
          this.businessType = 5
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 3000 })
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    async createbiz () {
      if (this.$refs.Formcreate.validate(true)) {
        // console.log(this.tel, this.fax)
        if (this.typeTaxID === 'none') {
          if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
            if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
              const check = this.checkSendAddress()
              if (check.length !== 0) {
                if (this.approveFileSuccess === '' && this.resultDocumentSuccess === '') {
                  this.$swal.fire({ icon: 'warning', title: 'กรุณาอัปโหลดหนังสือรองรับนิติบุคคลและหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
                } else if (this.approveFileSuccess !== '' && this.resultDocumentSuccess === '') {
                  this.$swal.fire({ icon: 'warning', title: 'กรุณาอัปโหลดหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
                } else if (this.approveFileSuccess === '' && this.resultDocumentSuccess !== '') {
                  this.$swal.fire({ icon: 'warning', title: 'กรุณาอัปโหลดหนังสือรองรับนิติบุคคล', showConfirmButton: false, timer: 1500 })
                } else {
                  var formDataBusiness = {
                    account_title_th: this.account_title_th,
                    first_name_th: this.businessNameTH,
                    account_title_eng: this.account_title_eng,
                    first_name_eng: this.businessNameEN,
                    id_card_num: this.taxId,
                    email: this.mail,
                    tel_no: this.tel !== null ? this.tel.replace(/-/g, '') : '',
                    mobile_no: this.phone !== null ? this.phone.replace(/-/g, '') : '',
                    approve_document: this.approveFileSuccess,
                    result_meeting: this.resultDocumentSuccess,
                    thai_email: this.mail,
                    department: 'ฝ่ายบริหาร',
                    position: 'CEO',
                    house_code: this.addressCode,
                    house_no: this.houseNo,
                    room_no: this.roomNo,
                    moo_ban: this.houseName,
                    moo_no: this.houseGroup,
                    building_name: this.buildingName,
                    floor: this.floor,
                    yaek: this.cross,
                    street: this.road,
                    fax_number: this.fax !== null ? this.fax.replace(/-/g, '') : '',
                    soi: this.alley,
                    province: this.checkTypeSelect === true ? this.provincetext : this.province,
                    tambon: this.checkTypeSelect === true ? this.subdistricttext : this.subdistrict,
                    amphoe: this.checkTypeSelect === true ? this.districttext : this.district,
                    zipcode: this.zipcode,
                    country: 'ประเทศไทย',
                    branch_no: this.branchCode,
                    branch_name: this.branchName,
                    type_verified: ''
                  }
                  // console.log('form data======>', formDataBusiness)
                  await this.$store.dispatch('actionCreateBusiness', formDataBusiness)
                  var response = await this.$store.state.ModuleBusiness.stateCreateBusiness
                  // console.log(response)
                  if (response.result === 'SUCCESS') {
                    this.$swal.fire({ icon: 'success', title: response.message, showConfirmButton: false, timer: 1500 })
                    this.$EventBus.$emit('getUserDetail')
                    this.$EventBus.$emit('AuthorityUser')
                    this.$router.push({ path: '/' }).catch(() => {})
                  } else {
                    this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
                  }
                }
              } else {
                this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 2500 })
          }
        } else {
          // if (this.approveFileSuccess === '' && this.resultDocumentSuccess === '') {
          //   this.$swal.fire({ icon: 'warning', title: 'กรุณาอัพโหลดหนังสือรองรับนิติบุคคลและหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
          // } else if (this.approveFileSuccess !== '' && this.resultDocumentSuccess === '') {
          //   this.$swal.fire({ icon: 'warning', title: 'กรุณาอัพโหลดหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
          // } else if (this.approveFileSuccess === '' && this.resultDocumentSuccess !== '') {
          //   this.$swal.fire({ icon: 'warning', title: 'กรุณาอัพโหลดหนังสือรองรับนิติบุคคล', showConfirmButton: false, timer: 1500 })
          // } else {
          var formDataBusiness1 = {
            account_title_th: this.account_title_th,
            first_name_th: this.businessNameTH,
            account_title_eng: this.account_title_eng,
            first_name_eng: this.businessNameEN,
            id_card_num: this.taxId,
            email: this.mail,
            tel_no: this.tel !== null ? this.tel.replace(/-/g, '') : '',
            mobile_no: this.phone !== null ? this.phone.replace(/-/g, '') : '',
            approve_document: '',
            result_meeting: '',
            thai_email: this.mail,
            department: 'ฝ่ายบริหาร',
            position: 'CEO',
            house_code: this.addressCode,
            house_no: this.houseNo,
            room_no: this.roomNo,
            moo_ban: this.houseName,
            moo_no: this.houseGroup,
            building_name: this.buildingName,
            floor: this.floor,
            yaek: this.cross,
            street: this.road,
            fax_number: this.fax !== null ? this.fax.replace(/-/g, '') : '',
            soi: this.alley,
            province: this.checkTypeSelect === true ? this.provincetext : this.province,
            tambon: this.checkTypeSelect === true ? this.subdistricttext : this.subdistrict,
            amphoe: this.checkTypeSelect === true ? this.districttext : this.district,
            zipcode: this.zipcode,
            country: 'ประเทศไทย',
            branch_no: this.branchCode,
            branch_name: this.branchName,
            type_verified: 'verified'
          }
          // console.log('form data======>', formDataBusiness)
          await this.$store.dispatch('actionCreateBusiness', formDataBusiness1)
          var response1 = await this.$store.state.ModuleBusiness.stateCreateBusiness
          // console.log(response1)
          if (response1.result === 'SUCCESS') {
            this.$swal.fire({ icon: 'success', title: response1.message, showConfirmButton: false, timer: 1500 })
            this.$EventBus.$emit('getUserDetail')
            this.$EventBus.$emit('AuthorityUser')
            this.$router.push({ path: '/' }).catch(() => {})
          } else {
            this.$swal.fire({ icon: 'error', title: response1.message, showConfirmButton: false, timer: 1500 })
          }
        }
        // }
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 1500 })
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Token not found.') {
        return 'ไม่พบโทเค็นในระบบ'
      } else if (msg === 'This user has not been verified Ekyc By ID Card') {
        return 'ผู้ใช้งานนี้ยังไม่ได้ยืนยันรหัสประชาชนกับ eKYC ในระบบ'
      } else if (msg === 'This TAX_ID is already exist.') {
        return 'เลขประจำตัวผู้เสียภาษีนี้ถูกใช้งานแล้ว'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Invalid format for business_type_other.') {
        return 'ประเภทกิจการไม่ถูกต้อง'
      } else if (msg === 'Create business other failed.') {
        return 'ลงทะเบียนนิติบุคคลอื่นๆไม่สำเร็จ'
      } else if (msg === 'tax_id is incorrect.') {
        return 'เลขประจำตัวผู้เสียภาษีนี้ไม่ถูกต้อง'
      } else if (msg === 'id_card_num not approved eKYC.') {
        return 'เลขประจำตัวผู้เสียภาษีนี้ ยังไม่ผ่านการยืนยัน eKYC'
      } else {
        return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
      }
    },
    async createbizOther () {
      if (this.$refs.FormcreateOther.validate(true)) {
        if (this.typeTaxIDOther === 'noneOther') {
          if ((this.checksubdistrictConfirm(this.subdistrictOther) || this.checkdistrictConfirm(this.districtOther) || this.checkprovinceConfirm(this.provinceOther) || this.checkzipcodeConfirm(this.zipcodeOther))) {
            if (this.subdistrictOther === this.checkSubdistrict && this.districtOther === this.checkDistrict && this.provinceOther === this.checkProvince && this.zipcodeOther === this.checkZipcode) {
              const check = this.checkSendAddress()
              if (check.length !== 0) {
                var formdataOther = {
                  business_type_other: this.businessTypeOther,
                  first_name_th: this.businessNameTHOther,
                  first_name_eng: this.businessNameENOther,
                  id_card_num: this.taxIdOther,
                  email: this.mailOther,
                  mobile_no: this.telOther !== null ? this.telOther.replace(/-/g, '') : '',
                  house_code: this.addressCodeOther !== '' ? this.addressCodeOther : '-',
                  house_no: this.houseNoOther !== '' ? this.houseNoOther : '-',
                  room_no: this.roomNoOther !== '' ? this.roomNoOther : '-',
                  moo_ban: this.houseNameOther !== '' ? this.houseNameOther : '-',
                  moo_no: this.houseGroupOther !== '' ? this.houseGroupOther : '-',
                  building_name: this.buildingNameOther !== '' ? this.buildingNameOther : '-',
                  floor: this.floorOther !== '' ? this.floorOther : '-',
                  yaek: this.crossOther !== '' ? this.crossOther : '-',
                  street: this.roadOther !== '' ? this.roadOther : '-',
                  soi: this.alleyOther !== '' ? this.alleyOther : '-',
                  province: this.provinceOther,
                  tambon: this.subdistrictOther,
                  amphoe: this.districtOther,
                  zipcode: this.zipcodeOther,
                  branch_no: this.branchCodeOther,
                  branch_name: this.branchNameOther,
                  type_verified: 'not_verified'
                }
                // console.log('data formdata other', formdataOther)
                await this.$store.dispatch('actionsCreateBusinessOther', formdataOther)
                var response = await this.$store.state.ModuleBusiness.stateCreateBusinessOther
                // console.log(response)
                if (response.result === 'SUCCESS') {
                  this.$swal.fire({ icon: 'success', title: response.message, showConfirmButton: false, timer: 1500 })
                  this.$EventBus.$emit('getUserDetail')
                  this.$EventBus.$emit('AuthorityUser')
                  this.$router.push({ path: '/' }).catch(() => {})
                } else {
                  var msg = this.getErrorMsg(response.message)
                  this.$swal.fire({
                    icon: 'error',
                    text: msg,
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
              } else {
                this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 1500 })
      }
    },
    // Upload File หนังสือรองรับนิติบุคคล
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      this.file = []
      if (this.DataImage.length <= 1) {
        this.DataImage.forEach((file) => {
          this.file.push({
            name: file.name,
            type: file.type
          })
          this.uploadInServe(file)
        })
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณาอัปโหลดได้แค่ 1 ไฟล์', showConfirmButton: false, timer: 1500 })
      }
    },
    async uploadInServe (val) {
      const formData = new FormData()
      formData.append('approveDocument', val)
      await this.$store.dispatch('actionUploadApprover', formData)
      var response = await this.$store.state.ModuleBusiness.stateUploadApprover
      // console.log('response upload =====>', response.data)
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: 'อัปโหลดหนังสือรับรองนิติบุคคลสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.approveFileSuccess = response.data.response_step_1
        // console.log(this.approveFileSuccess)
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage (index, val) {
      this.DataImage = []
      this.file.splice(index, 1)
    },
    // Upload File หนังสือมอบอำนาจ
    onPickFile1 () {
      document.getElementById('file_input1').click()
    },
    UploadImage1 () {
      // console.log(this.DataImage1)
      this.file1 = []
      if (this.DataImage1.length <= 1) {
        this.DataImage1.forEach((file) => {
          this.file1.push({
            name: file.name,
            type: file.type
          })
          this.uploadInServe1(file)
        })
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณาอัปโหลดได้แค่ 1 ไฟล์', showConfirmButton: false, timer: 1500 })
      }
    },
    async uploadInServe1 (val) {
      const formData = new FormData()
      formData.append('resultDocument', val)
      await this.$store.dispatch('actionUploadResultDocument', formData)
      var response = await this.$store.state.ModuleBusiness.stateUploadResultDocument
      // console.log('response upload =====>', response.data)
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: 'อัปโหลดหนังสือมอบอำนาจสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.resultDocumentSuccess = response.data.response_step_2
        // console.log(this.resultDocumentSuccess)
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    onMove1 ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage1 (index, val) {
      this.DataImage1 = []
      this.file1.splice(index, 1)
    },
    async searchTaxID () {
      this.checkTypeSelect = true
      if (this.$refs.FormTaxID.validate(true)) {
        var response
        var data = {
          tax_id: this.taxIDSearch
        }
        if (this.typeTaxID === 'TaxByRevenue') {
          // console.log('TaxByRevenue', this.taxIDSearch)
          await this.$store.dispatch('actionFindTaxIDByRevenue', data)
          response = await this.$store.state.ModuleBusiness.stateFindTaxIDByRevenue
          // console.log('response TaxByRevenue', response)
          if (response.result === 'SUCCESS') {
            var dataTax = response.data
            // console.log('response', response.data)
            this.$swal.fire({ text: 'ดึงข้อมูลสำเร็จ', icon: 'success', timer: 1500, showConfirmButton: false })
            this.showtaxSearch = false
            this.account_title_th = dataTax.account_title_th
            this.businessNameTH = dataTax.first_name_th
            this.mail = dataTax.email
            this.branchCode = dataTax.branch_no
            this.branchName = dataTax.branch_name
            this.fax = dataTax.fax_number === null ? '-' : dataTax.fax_number
            this.tel = dataTax.tel_no === null ? '-' : dataTax.tel_no
            this.phone = dataTax.mobile_no === null ? '-' : dataTax.mobile_no
            this.buildingName = dataTax.address.building_name
            this.floor = dataTax.address.floor
            this.road = dataTax.address.street
            this.cross = dataTax.address.yaek
            this.houseNo = dataTax.address.house_no
            this.roomNo = dataTax.address.room_no
            this.houseName = dataTax.address.moo_ban
            this.alley = dataTax.address.soi
            this.provincetext = dataTax.address.province
            this.districttext = dataTax.address.amphoe
            this.subdistricttext = dataTax.address.tambon
            this.zipcode = dataTax.address.zipcode
            this.showForm = true
          } else {
            this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
          }
        } else if (this.typeTaxID === 'TaxByOneID') {
          // console.log('TaxByRevenue', this.taxIDSearch)
          await this.$store.dispatch('actionFindTaxIDByOneID', data)
          response = await this.$store.state.ModuleBusiness.stateFindTaxIDByOneID
          // console.log('response TaxByOneID', response)
          if (response.result === 'SUCCESS') {
            var dataBusiness = response.data
            // console.log('response', response.data)
            this.$swal.fire({ text: 'ดึงข้อมูลสำเร็จ', icon: 'success', timer: 1500, showConfirmButton: false })
            this.showtaxSearch = false
            if (dataBusiness.account_title_th === 'ห้างหุ้นส่วนสามัญ') {
              this.businessType = 1
            } else if (dataBusiness.account_title_th === 'ห้างหุ้นส่วนจำกัด') {
              this.businessType = 2
            } else if (dataBusiness.account_title_th === 'บริษัทจำกัด') {
              this.businessType = 3
            } else if (dataBusiness.account_title_th === 'บริษัทมหาชนจำกัด') {
              this.businessType = 4
            } else {
              this.businessType = 5
            }
            this.businessNameTH = dataBusiness.first_name_th
            this.businessNameEN = dataBusiness.first_name_eng
            this.businessDocNameTH = dataBusiness.name_on_document_th
            this.businessDocNameEN = dataBusiness.name_on_document_eng !== null ? dataBusiness.name_on_document_eng : '-'
            this.taxId = dataBusiness.tax_id
            this.mail = dataBusiness.email
            this.branchCode = dataBusiness.branch_no
            this.branchName = dataBusiness.branch_name
            this.fax = dataBusiness.fax_number === null ? '-' : dataBusiness.fax_number
            this.tel = dataBusiness.tel_no === null ? '-' : dataBusiness.tel_no
            this.phone = dataBusiness.mobile_no === null ? '-' : dataBusiness.mobile_no
            this.buildingName = dataBusiness.address.building_name
            this.floor = dataBusiness.address.floor
            this.road = dataBusiness.address.street
            this.cross = dataBusiness.address.yaek !== null ? dataBusiness.address.yaek : '-'
            var dataDetail = dataBusiness.address.data_detail === null ? '' : dataBusiness.address.data_detail
            this.houseNo = dataBusiness.address.house_no + ' ' + dataDetail
            this.roomNo = dataBusiness.address.room_no !== null ? dataBusiness.address.room_no : '-'
            this.houseName = dataBusiness.address.moo_ban !== null ? dataBusiness.address.moo_ban : '-'
            this.alley = dataBusiness.address.soi !== null ? dataBusiness.address.soi : '-'
            this.provincetext = dataBusiness.address.province
            this.districttext = dataBusiness.address.amphoe
            this.subdistricttext = dataBusiness.address.tambon
            this.zipcode = dataBusiness.address.zipcode
            this.showForm = true
          } else {
            this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
          }
        }
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        if (this.tab === 'tab-1') {
          return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
        } else {
          return data.district === this.subdistrictOther && data.amphoe === this.districtOther && data.province === this.provinceOther && data.zipcode === Number(this.zipcodeOther)
        }
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style scoped>
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 0px 0px 0px 0px !important;
}
.container {
  max-width: 1400px !important;
  padding: 0px 12px 12px 12px;
}
.v-timeline-item__body {
  position: relative;
  height: 100%;
  padding-top: 8px !important;
  flex: 1 1 auto;
}
.v-timeline-item {
  display: flex;
  padding-bottom: 12px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
