<template>
 <v-container>
  <v-row>
    <v-col cols="12">
      <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;">
        <v-icon color="#27AB9C" class="mr-2" @click="backtoList">
          mdi-chevron-left
        </v-icon>
        แก้ไขใบเสนอราคา
      </span>
    </v-col>
  </v-row>
  <div
    class="center-screen R-Main mt-4 pt-6"
    style="background-color: #C4C4C4;
    overflow-y: auto;
    margin-top: 2em;
    margin-bottom: 3em;
    padding-top: 4em;
    padding-bottom: 4em;"
   >
  <div v-for="(item ,indexPage) in items" :key="indexPage" class="mb-6">
  <v-card  class="R-F-P pa-4" >
   <div class="document active" >
    <div class="spreadSheetGroup" :style="IpadSize ? 'width: 740px' : IpadProSize ? 'width: 980px; background-position: center; border-style: solid;border-width: 2px;' : 'width: 1200px;background-position: center; border-style: solid;border-width: 2px;'">
     <v-row class="ml-1 mt-1" no-gutters>
       <v-col v-if="quotationDetail.qu_detail.seller_shop_logo === null || quotationDetail.qu_detail.seller_shop_logo === '' || quotationDetail.qu_detail.seller_shop_logo === undefined" cols="2" md="3">
         <v-img src="@/assets/ICON/Notlog.jpg" height="122px" contain></v-img>
       </v-col>
       <v-col v-else cols="2" md="3">
         <v-img :src="quotationDetail.qu_detail.seller_shop_logo" height="122px" contain></v-img>
       </v-col>
       <v-col cols="10" md="9" align="end">
        <v-row class="mt-2 mr-2" no-gutters>
          <v-col cols="12">
            <strong v-if="quotationDetail.qu_detail.seller_shop_name_en === null || quotationDetail.qu_detail.seller_shop_name_en === ''" style="font-size: 28px;">
              Internet Thailand Public Company Limited
            </strong>
            <strong v-else style="font-size: 28px;">
              {{ quotationDetail.qu_detail.seller_shop_name_en }}
            </strong>
          </v-col>
          <v-col v-if="ChackAddress" cols="12">
              <span v-if="quotationDetail.qu_detail.seller_shop_address.address === null || quotationDetail.qu_detail.seller_shop_address.address === ''"  style="font-size: 12px;" :class="addressCompany !== null ? 'mr-0' : ''">
                1768 Thai Summit Tower, 10th - 12th Floor and IT Floor<br/>
                New Petchaburi Road, Khwaeng Bang Kapi, Khet Huay Khwang, Bangkok 10310<br/>
                Tel. (************* Fax (*************, (*************
              </span>
              <span v-else  style="font-size: 12px;" :class="addressCompany !== null ? 'mr-0' : ''">
                {{quotationDetail.qu_detail.seller_shop_address.address_line1}}<br>
                {{quotationDetail.qu_detail.seller_shop_address.address_line2}}<br>
                {{quotationDetail.qu_detail.seller_shop_address.address_line3}}<br>
              </span>
          </v-col>
        </v-row>
       </v-col>
     </v-row>
     <v-row v-if="quotationDetail.qu_detail.json_data !== undefined" class="mt-4 ml-1 mb-1" no-gutters>
      <v-col cols="6" >
        <v-row no-gutters>
          <v-col cols="12" md="12" align="left" style="font-size: 22px; font-weight: bold">
            <span class="ml-3">Quotation / Purchase Order</span>
          </v-col>
          <v-col cols="12" md="12" align="left" >
            <div style="border: 1px solid #A1A1A1;border-radius: 1px;">
             <v-row class="ml-3 mt-2" no-gutters>
               <v-col cols="12">
                 <v-row class="mt-1" no-gutters>
                  <v-col cols="3">
                    <span style="font-size: 16px;">Customer Name :</span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field
                      disabled
                      v-model="quotationDetail.qu_detail.json_data.customer_data.name"
                      label=""
                      solo
                      dense
                      placeholder="ระบุ Customer Name"
                    ></v-text-field>
                   </v-col>
                 </v-row>
               </v-col>
               <v-col cols="12">
                <v-row no-gutters>
                  <v-col cols="3">
                    <span style="font-size: 16px;">Company Name:</span>
                  </v-col>
                  <v-col cols="8">
                    <v-text-field
                      label=""
                      disabled
                      v-model="quotationDetail.qu_detail.json_data.customer_data.company_name"
                      solo
                      dense
                      placeholder="ระบุ Company Name"
                    ></v-text-field>
                   </v-col>
                 </v-row>
               </v-col>
               <v-col cols="12">
                <v-row no-gutters>
                  <v-col cols="3">
                    <span style="font-size: 16px;">Address :</span>
                  </v-col>
                  <v-col cols="8">
                      <v-text-field
                      solo
                      disabled
                      v-model="quotationDetail.qu_detail.json_data.customer_data.address"
                      name="input-7-4"
                      label=""
                      placeholder="ระบุ Address"
                      class="my-0 py-0"
                    ></v-text-field>
                   </v-col>
                 </v-row>
               </v-col>
               <v-col cols="12">
                <v-row  no-gutters>
                  <v-col cols="3">
                    <span style="font-size: 16px;">Tel No. :</span>
                  </v-col>
                  <v-col cols="3">
                    <v-text-field
                      label=""
                      disabled
                      v-model="quotationDetail.qu_detail.json_data.customer_data.tel_no"
                      placeholder="ระบุ Tel No"
                      solo
                      dense
                    ></v-text-field>
                   </v-col>
                  <v-col cols="2" align="center">
                    <span style="font-size: 16px;">Mobile :</span>
                  </v-col>
                  <v-col cols="3">
                    <v-text-field
                      label=""
                      placeholder="ระบุ Mobile"
                      disabled
                      v-model="quotationDetail.qu_detail.json_data.customer_data.mobile_no"
                      solo
                      dense
                    ></v-text-field>
                   </v-col>
                 </v-row>
               </v-col>
               <v-col cols="12">
                <v-row  no-gutters>
                  <v-col cols="3">
                    <span style="font-size: 16px;">Email :</span>
                  </v-col>
                  <v-col cols="8" class="mb-2">
                    <v-text-field
                      label=""
                      disabled
                      v-model="quotationDetail.qu_detail.json_data.customer_data.email"
                      solo
                      dense
                      placeholder="ระบุ Email"
                      hide-details
                    ></v-text-field>
                   </v-col>
                 </v-row>
               </v-col>
             </v-row>
            </div>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="6">
        <v-row  class="mt-16" no-gutters>
          <v-col cols="12" class="mt-3">
            <v-row no-gutters>
              <v-col cols="5" align="end" class="mt-1">
                <span style="font-size: 14px;">Quotation No. : </span>
              </v-col>
              <v-col cols="5" class="ml-2">
                <v-text-field
                  label="* จะได้รับหลังจากบันทึกข้อมูลแล้ว *"
                  disabled
                  v-model="quotationDetail.qu_detail.json_data.qu_number"
                  solo
                  dense
                  placeholder="* จะได้รับหลังจากบันทึกข้อมูลแล้ว *"
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row no-gutters>
              <v-col cols="5" class="mt-2" align="end">
                <span style="font-size: 14px;">Quotation Date : </span>
              </v-col>
              <v-col cols="5" class="ml-2 mt-2">
                <v-text-field
                  label=""
                  solo
                  dense
                  disabled
                  hide-details
                  v-model="quotationDetail.qu_detail.json_data.qu_date"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row class="my-0"  no-gutters>
              <v-col cols="5" class="mt-2" align="end">
                <span style="font-size: 14px;">Sale Name : </span>
              </v-col>
              <v-col cols="5" class="ml-2 mt-2">
                <v-text-field
                  label=""
                  solo
                  dense
                  hide-details
                  disabled
                  v-model="quotationDetail.qu_detail.json_data.sale_data.name"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row class="my-0" no-gutters>
              <v-col cols="5" class="mt-2" align="end">
                <span style="font-size: 14px;">Tel No. : </span>
              </v-col>
              <v-col cols="5" class="ml-2 mt-2">
                <v-text-field
                  label=""
                  solo
                  dense
                  disabled
                  v-model="quotationDetail.qu_detail.json_data.sale_data.tel_no"
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row class="my-0" no-gutters>
              <v-col cols="5" class="mt-2" align="end">
                <span style="font-size: 14px;">Mobile : </span>
              </v-col>
              <v-col cols="5" class="ml-2 mt-2">
                <v-text-field
                  label=""
                  v-model="quotationDetail.qu_detail.json_data.sale_data.mobile_no"
                  solo
                  disabled
                  dense
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row no-gutters>
              <v-col cols="5" class="mt-2" align="end">
                <span style="font-size: 14px;">Email : </span>
              </v-col>
              <v-col cols="5" class="ml-2 mt-2">
                <v-text-field
                  label=""
                  disabled
                  v-model="quotationDetail.qu_detail.json_data.sale_data.email"
                  solo
                  dense
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-row>

    <!-- Start to Table -->
    <table v-if="quotationDetail.qu_detail.product_list !== null" :class="quotationDetail.qu_detail.seller_shop_logo === null ? 'proposedWork background-image' : 'proposedWork'"  v-bind:style="{ 'background-image': 'url(' + quotationDetail.qu_detail.seller_shop_logo + ')', 'background-position': 'center', 'background-size': '340px 150px', 'opacity': '0.9'}"  width="100%" height="740px">
      <thead>
        <tr>
          <th>Item</th>
          <th>Descriotipn</th>
          <th>Qty</th>
          <th>Unit</th>
          <th>Unit Price</th>
          <th class="amountColumn">Amount</th>
          <th class="docEdit trAdd" @click="addRow" ><v-icon color="white">mdi-plus</v-icon></th>
        </tr>
      </thead>
      <tbody>
        <tr v-for='(item, index) in quotationDetail.qu_detail.product_list' :key="index">
          <td style="text-align: center;vertical-align: middle;width: 0%;"><div style="font-size: 16px; font-weight: bold;">{{index + 1}}</div></td>
          <td class="description px-2 my-0 pt-2 pb-0" style="width: 50%; height: 2px;">
            <div v-if="item.status !== '0'">
              <table>
                <tbody>
                  <tr>
                    <td style="border: hidden;text-align:left" width="50%">
                      <span style="font-size: 14px;">{{item.product_name !== '' ? item.product_name+' ' : item.product_name}}</span>
                      <span>
                        <div>
                          sku: {{ item.sku !== '' ? item.sku : '' }}
                        </div>
                        {{ item.description !== '' ? item.description : '' }}
                       </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else>
              <v-btn
                class="ma-2"
                color="success"
                @click="openModalPurchaseOrder(index)"
              >
                เพิ่มรายการ
              </v-btn>
            </div>
          </td>
          <td class="" style="width: 10%;">
            <v-text-field
            label=""
            type="number"
            :max="item.stock !== '' ? item.stock : ''"
            min="1"
            solo
            oninput="this.value = parseInt(this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1'))"
            dense
            v-model="item.quantity"
            disabled
            :rules="[v => !!v || 'กรุณากรอกจำนวน']"
          ></v-text-field>
          </td>
          <td
            style="width: 5%;"
            >
            <span style="font-size: 16px;">
              Months
            </span>
          </td>
          <td class="unit"
            style="width: 8%;"
            >
              <v-text-field
              :label="formatTofixed(parseFloat((item.revenue_default) * item.quantity))"
              solo
              dense
              disabled
            ></v-text-field>
          </td>
          <td class="amountColumn rowTotal"
            style="width: 15%;"
            >
              <v-text-field
              :label="formatTofixed(parseFloat((item.revenue_default * item.quantity) * month))"
              solo
              dense
              disabled
            ></v-text-field>
          </td>
          <td class="docEdit tdDelete" @click="deleteRow(index)">
            <div style="padding-top: 80%;">
            <v-icon >mdi-delete-outline</v-icon>
            </div>
          </td>
        </tr>
        <tr v-if="quotationDetail.qu_detail.product_list.length < 9">
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <td style="" colspan="3">
            <v-row no-gutters>
              <v-col cols="3" style="text-align:center">
                <span style="font-size: 14px; font-weight: 600;">ระยะเวลาการให้บริการขั้นต่ำ:</span>
              </v-col>
              <v-col cols="4" style="text-align:center">
                <span style="font-size: 14px; font-weight: 600;">วันที่เริ่มสัญญา</span>
              </v-col>
              <v-col cols="3" style="text-align:center">
                <span style="font-size: 14px; font-weight: 600;">วันที่สิ้นสุดสัญญา</span>
              </v-col>
            </v-row>
          </td>
          <td style="text-align:center; font-weight: 600;"> Total </td>
          <td style="text-align:center">{{formatTofixed(parseFloat(excludingVat))}}</td>
          <td class=" subtotal">{{formatTofixed(parseFloat(excludingVatAmount))}}</td>
          <td class="docEdit"></td>
        </tr>
        <tr>
          <td style="border-top: hidden" colspan="3">
            <v-row class="d-flex" no-gutters>
              <v-col cols="3" style="text-align:center">
                <span class="pt-8" style="font-size: 14px; font-weight: 200;">{{ month }} Months</span>
              </v-col>
              <v-col cols="3" style="text-align:center" class="ml-10">
                <v-menu
                  ref="menu1"
                  v-model="menu1"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  max-width="290px"
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="dateFormattedStart"
                      label=""
                      hint=""
                      persistent-hint
                      append-icon="mdi-calendar"
                      dense
                      outlined
                      v-bind="attrs"
                      v-on="on"
                      hide-details
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="dateFormattedStart"
                    no-title
                    locale="th"
                    @input="menu1 = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col cols="3" style="text-align:center" class="ml-6">
               <v-menu
                  ref="menu2"
                  v-model="menu2"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  max-width="290px"
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="dateFormattedEnd"
                      label=""
                      hint=""
                      persistent-hint
                      append-icon="mdi-calendar"
                      dense
                      outlined
                      v-bind="attrs"
                      v-on="on"
                      hide-details
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="dateFormattedEnd"
                    no-title
                    class="calendar"
                    locale="th"
                    :min="quotationDetail.dateStart"
                    @input="menu2 = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
            </v-row>
          </td>
          <td style="text-align:center; font-weight: 600;"> Vat</td>
          <td style="text-align:center; font-weight: 600;"> 7% </td>
          <td style="text-align:center; font-weight: 600;">
            {{formatTofixed(parseFloat(Vat))}}
          </td>
          <td class="docEdit"></td>
        </tr>
        <tr>
          <td style="text-align:center; background-color: #51862E;color: #FFFFFF;" colspan="5">Grand Total</td>
          <td class=" subtotal">{{formatTofixed(parseFloat(includingVat))}}</td>
          <td class="docEdit"></td>
        </tr>
        <tr >
          <td style="font-size: 12px;text-align:center" colspan="6">
            <span v-if="ChackStatusInet" style="font-size: 12px;">
              <font class="mr-2">หมายเหตุ</font>
              กรณีลูกค้าต้องการใช้บริการ INET Privacy Box ลูกค้าต้องดําเนินการลงนามรับทราบข้อตกลงการใช้บริการ INET Privacy Box หน้าที่ 2 เพิ่ม <br/> เติมเงื่อนไขการให้บริการ
            </span>
            <span v-else style="font-size: 12px;">
              เงื่อนไขการให้บริการ
            </span>
          </td>
        </tr>
      </tfoot>
    </table>
    <!-- end to Table -->
    <v-divider style="border: 1px solid #000000;margin-top: -12px;" class="mb-2"></v-divider>
    <v-row justify="space-between"  class="mr-1">
      <v-col align="start" cols="6">
        <span v-for="(items,index) in Datasone" :key="index">
          <span v-html="items.body"></span>
        </span>
      </v-col>
      <v-col align="start"  cols="6">
        <div v-for="(items,index) in Datastwo" :key="index">
          <span v-html="items.body"></span>
        </div>
      </v-col>
    </v-row>
    <v-divider style="border: 1px solid #000000;" class="mt-2 mb-2"></v-divider>
    <v-row justify="space-between">
      <v-col v-if="ChackStatusInet"  style="font-size: 12px;font-weight: 600;" align="start" cols="12">
        <span>INET Company Certified</span><br/>
        <span>ISO/IEC 20000:2018 (Information Technology Service Management), ISO/IEC 27001:2013 (Information Security Management)</span><br/>
        <span>ISO/IEC 27799:2016 (Health informatics-Information security), ISO/IEC 22301:2012 (Business Continuity Management)</span><br/>
        <span>ISO 27017:2015 (Cloud security), CSA Security, Trust & Assurance Registry (STAR), มาตรฐาน ISO/IEC 27018:2014</span>
      </v-col>
      <v-col v-else  style="font-size: 12px;font-weight: 600;" align="start" cols="12">
        <span>Service Certified:</span><br/>
        <span> - ISO/IEC 20000- 1 :2011 (Information Technology Service Management), ISO/ IEC 27001:2013 (Information Security Management)</span><br/>
        <span> - ISO/IEC 27799 :2016 (Health informatics-Information security), ISO / IE C 22301:2012 (Business Continuity Management)</span><br/>
        <span> - ISO 27017: 2015 (Cloud security), CSA Security, Trust & Assurance Registry (STAR), มาตรฐาน ISO/IEC 27018:2014</span><br/>
        <span> - SLA (Service Level Agreement) 99.90%</span><br/>
        <span> - Premier Support (24*7) Email : <EMAIL> , Call : 02-257-7111</span>
      </v-col>
    </v-row>
    <v-divider style="border: 1px solid #000000;" class="mt-4"></v-divider>
    <table style="width:100%;margin: -2px;padding-right: -20px;" border="2">
      <tbody>
        <tr>
          <td colspan="1" style="text-align:center; width:30%;background-color: #D9F0BE">
            <strong style="font-size: 14px;font-weight: 600;">Acceptance By Customer</strong>
          </td>
          <td colspan="2" style="text-align:center; width:30%;background-color: #D9F0BE">
            <strong v-if="ChackStatusInet" style="font-size: 14px;font-weight: 600;">Internet Thailand Public Co.,Ltd.</strong>
            <strong v-else style="font-size: 14px;font-weight: 600;">One Email Company Limited</strong>
          </td>
        </tr>
        <tr>
          <td style="text-align:center; width:30%;">
            <strong>We agree to order the service as quoted</strong>
          </td>
          <td style="text-align:center; width:30%;">
            <strong>Quoted By</strong>
          </td>
          <td style="text-align:center; width:30%;">
            <strong>Approve By</strong>
          </td>
        </tr>
        <tr style="border-top: hidden;">
          <td>
            <v-text-field
            class="ml-14 mr-14 mt-12"
            width=""
            label=""
            disabled
            dense
            ></v-text-field>
            <div>Signature of Authorized Person with Company's Seal</div>
            <tr>
              <td style="border: hidden;" class="mt-2">
                 Position :    .......................................................................
              </td>
            </tr>
            <tr>
              <td style="border: hidden;"  class="mt-2">
                Date :    .......................................................................
              </td>
            </tr>
          </td>
          <td>
            <v-text-field
              class="ml-14 mr-14 mt-10"
              width=""
              label=""
              disabled
              dense
              hide-details
            ></v-text-field>
            <table style="width:100%;">
              <tbody>
                <tr>
                  <td style="text-align:right;">
                  <div class="mb-5 mt-4">(</div>
                  </td>
                  <td style="border: hidden;margin-top: 4px;">
                    <span style="font-size: 14px;font-weight: 400;" >
                      มาริษา มารอบี
                    </span>
                    <div style="border-bottom: 1px dotted"></div>
                  <!-- <v-text-field
                    v-model="quotationDetail.prepared_by"
                    disabled
                    label="มาริษา มารอบี"
                    dense
                    style="text-align:center; "
                    hide-details
                  >
                  </v-text-field> -->
                  </td>
                  <td style="text-align:left; border: hidden; padding-top: 15px;">
                  <div class="mb-3 mt-2">)</div>
                  </td>
                </tr>
              </tbody>
            </table>
            <tr>
              <td style="border: hidden;">
                Position :
              </td>
              <td style="border: hidden; width: 300px;">
                <span style="font-size: 14px;font-weight: 400;" >
                  Business Support
                </span>
                <div style="border-bottom: 1px dotted"></div>
              </td>
            </tr>
          </td>
          <td>
            <v-text-field
              class="ml-14 mr-14 mt-10"
              width=""
              label=""
              disabled
              dense
              hide-details
            ></v-text-field>
            <table style="width:100%">
              <tbody>
                <tr>
                  <td style="text-align:right;">
                  <div class="mb-5 mt-4">(</div>
                  </td>
                  <td style="border: hidden;margin-top: 3px;">
                    <span style="font-size: 14px;font-weight: 400;" >
                      ลลิตา ปึงศิริพัฒนา
                    </span>
                    <div style="border-bottom: 1px dotted"></div>
                  </td>
                  <td style="text-align:left; border: hidden; padding-top: 0px;">
                  <div class="mb-3 mt-2">)</div>
                  </td>
                </tr>
              </tbody>
            </table>
            <tr>
              <td style="border: hidden; margin-top: -10px;">
                 Position :
              </td>
              <td style="border: hidden;width: 300px;">
                <span style="font-size: 14px;font-weight: 400;" >
                  Senior Business Development
                </span>
                <div style="border-bottom: 1px dotted"></div>
              </td>
            </tr>
          </td>
        </tr>
    </tbody>
  </table>
  </div>
  <PurchaseOrderModal/>
  <selectCopons />
  <v-dialog v-model="quotationModel" width="800">
    <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>ข้อมูลใบเสนอราคา</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="quotationModel = false" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <div style="text-align: center;" >
        <div v-if="loading2" style="display: inline-block; justify-content: center;"  class="pb-10">
          <div class="spinner">
          <div></div>
          <div></div>
          <p class="p-text">กำลังโหลด...</p>
           </div>
        </div>
        <div v-else>
        <v-container grid-list-xs class="mt-5">
            <v-row>
              <v-col cols="4">รวมเงิน</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_price) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">ส่วนลด</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_discount) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">ภาษีมูลค่าเพิ่ม</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_vat) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">ค่าจัดส่ง</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.total_shipping): ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
            <v-row>
              <v-col cols="4">รวมทั้งสิ้น</v-col>
              <v-col cols="4">{{itemShipping !== '' ? formatTofixed(itemShipping.net_price) : ''}}</v-col>
              <v-col cols="4">บาท</v-col>
            </v-row>
          <v-card-actions class="mt-5">
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="quotationModel = false">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" :loading="loading" :disabled="!quotationModel" @click="postFormData">ยืนยัน</v-btn>
          </v-card-actions>
        </v-container>
        </div>
        </div>
      </v-card>
  </v-dialog>
  </div>
 </v-card>
 </div>
 </div>
 <v-row class="mb-5">
    <v-col cols="12" align="right">
       <v-btn color="success" v-if="user_role === 'seller'" :disabled="quotationDetail.total_shipping === ''" class="mr-2" @click="EditQt">ยืนยัน</v-btn>
       <v-btn color="success" v-else :disabled="!CHKproductList"  class="mr-2" @click="createQT()">บันทึกข้อมูล</v-btn>
    </v-col>
  </v-row>
</v-container>
</template>
<script>
import THBText from 'thai-baht-text'
import { Encode, Decode } from '@/services'
export default {
  components: {
    PurchaseOrderModal: () => import('@/components/Modal/PurchaseOrderModal')
  },
  data () {
    return {
      DetailProduct: [{ description: '', quantity: '', unit: '', cost: '' }],
      ChackAddress: true,
      overlay: true,
      imgShow: false,
      chkBtn: [],
      chkForm: true,
      user_role: '',
      ChackStatusInet: false,
      items: [
        {
          index: 1
        }
      ],
      DatasoneInet: [
        {
          body: '<p style="font-size: 12px;color: #333333;">1. กําหนดยืนราคา 30 วัน</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px">2. เก็บค่าบริการล่วงหน้าเป็นงวด งวดละ 1 เดือน (ตามข้อตกลงว่าจ้าง)</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px">3.หากผู้ใช้บริการผิดนัดไม่ชำระค่าบริการและ/หรือค่าธรรมเนียมเกินกว่าระยะเวลาที่กําหนดผู้ใช้บริการตกลงยินยอมให้INETดําเนินการดังต่อ ไปนี้</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.1 ในกรณีีที่ผู้ใช้บริการผิดนัดไมชำระค่าบริการและ/หรือค่าธรรมเนียมให้ครบถ้วนภายใน 0-15 วัน นับถัดจากวันถึงกําหนดชําระ INET ขอสงวนสิทธิในการระงับการซื้อบริการอื่นๆ ของผู้ใช้บริการเพิ่มเติมจนกว่าผู้ใช้บริการจะชําระหนี้ที่ค้างชําระอยู่นั้นให้แก่INETจนครบถ้วน</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.2 ในกรณีที่ผู้ใช้บริการผิดนัดไม่ชำระค่าบริการและ/หรือค่าธรรมเนียมให้ครบถ้วนภายใน 15-30 วันนับถัดจากวันถึงกําหนดชําระINET ขอสงวนสิทธิในการระงับการให้บริการทั้งหมดหรือบางส่วนในทันที จนกว่าผู้ใช้บริการจะชําระหนี้ที่ค้างชําระอยู่น่ั้นให้แก่ INET จนครบถ้วน ทั้งนี้ ผู้ใช้ บริการตกลงและรับทราบว่าหากผู้ใช้บริการประสงค์ที่จะใช้บริการต่อภายหลังจากที่ INET ได้ระงับการให้บริการผู้ใช้บริการจะต้องแจ้งความ ประสงค์ในการใช้บริการต่อให้ INET ทราบเป็น ลายลักษณ์อักษรและต้องชําระค่าบริการเพิ่ม เติมเป็นจํานวน 1,500 บาท/VM ก่อนที่ INET จะ ดําเนินการลบ VirtualMachine(VM) ของผู้ใช้บริการตามที่กําหนดในข้อ3.33</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.3 ในกรณีที่ผู้ใช้บริการผิดนัดไม่ชำระค่าบริการและ/หรือค่าธรรมเนียมให้ครบถ้วนเกินกว่า 30 วัน นับถัดจากวันถึงกำหนดชำระ INET ขอสงวนสิทธิในการเข้าถึงและลบ Virtual Machine (VM) รวมถึงข้อมูลทั้งหมดของผู้ใช้บริการ ทั้งนี้ ผู้ใช้บริการรับทราบว่าภายหลังจากการลบ Virtual Machine (VM) และข้อมูลข้างต้น INET จะไม่สามารถกู้ข้อมูลดังกล่าวคืนได้</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.4 ในกรณีผู้ใช้บริการผิดนัดไม่ชำระค่าธรรมเนียมหรือค่าบริการใดๆ ภายในระยะเวลาที่กำหนด ผู้ใช้บริการต้องชำระดอกเบี้ยผิดนัดในอัตรา ร้อยละ 1.5 ต่อเดือนของค่าธรรมเนียมหรือค่าใช้บริการที่ค้างชำระ และในกรณีที่บริษัทฯต้องเสียค่าใช้จ่ายในการติดตามหนี้ และ/หรือค่าเสียหายอื่นอันเกิดจากการผิดนัด ผู้ใช้บริการจะต้องรับผิดชดใช้ค่าใช้จ่าย และ/หรือค่าเสียหายนั้นๆทั้งหมด </p>'
        }
      ],
      DatastwoInet: [
        {
          body: '<p style="font-size: 12px;color: #333333"><span class="ml-4"></span>ทั้งนี้ ผู้ใช้บริการตกลงและรับทราบว่า การลบ Virtual Machine (VM) และข้อมูลของผู้ใช้บริการข้างต้น เป็นการดำเนินการที่เกิดขึ้น จากการปฏิบัติผิดสัญญาของผู้ใช้บริการ ดังนั้น INET จึงไม่มีความรับผิดตามกฎหมาย รวมถึง ไม่รับผิดชอบในความเสียหายอย่างใดที่เกิดขึ้นหรืออาจจะเกิดขึ้นแก่ผู้ใช้บริการ อันเนื่องมาจากการใช้สิทธิระงับการให้บริการและการลบ Virtual Machine (VM) และข้อมูลของผู้ใช้บริการตามสัญญาข้อนี้ และ INET สงวนสิทธิที่จะเลิกสัญญาและเรียกร้องค่าเสียหายและค่าใช้จ่าย (ซึ่งรวมถึงแต่ไม่จำกัดเพียงค่าเสียหายหรือค่าใช้จ่ายที่ระบุไว้ในสัญญาฉบับนี้) จากผู้ใช้บริการเพราะเหตุที่ผู้ใช้บริการผิดนัดชำระหนี้ดังกล่าวด้วย ในการนี้ ผู้ใช้บริการตกลงสละสิทธิในการเรียกร้องค่าเสียหาย หรือดำเนินการทางกฎหมายใดๆ ที่เกี่ยวข้องกับกรณีดังกล่าวทั้งหมด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">4. ก่อนครบกำหนดระยะเวลาขั้นต่ำที่ระบุไว้ ไม่น้อยกว่า 30 (สามสิบ)วัน หากผู้ใช้บริการไม่ประสงค์จะต่ออายุการใช้บริการ ให้บอกกล่าวให้บริษัทฯทราบเป็นลายลักษณ์อักษร มิเช่นนั้นแล้วผู้ใช้บริการตกลงให้มีการต่ออายุการใช้บริการออกไปโดยไม่มีกำหนดระยะเวลา จนกว่าผู้ใช้บริการจะได้มีการแจ้งยกเลิกการใช้บริการเป็นลายลักษณ์อักษรมายังบริษัทฯ)</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">5. กรณีที่บริษัทฯ ได้ทำการติดตั้งบริการแล้ว หากผู้ใช้บริการยกเลิกบริการ ก่อนครบระยะเวลาที่กำหนด โดยไม่ได้เกิดจากความผิดของบริษัทฯ ผู้ใช้บริการมีหน้าที่ชำระค่าปรับ และค่าเสียหาย ให้แก่บริษัทฯ ตามที่บริษัทฯ กำหนด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #33333; margin-top: -5px">6. การรับประกันคุณภาพบริการ SLA Uptime 99.95% หรือขึ้นอยู่กับเอกสาร Service Level Agreement บริษัท อินเทอร์เน็ตประเทศไทย จำกัด (มหาชน) ยกเว้นแต่เหตุขัดข้องนั้นเกิดจากเหตุสุดสุ วิสัย หรือเกิดจากผู้ใช้บริการ</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">7. ผู้ใช้บริการต้องไม่กระทำการใดๆ ที่เป็นต้นเหตุทำให้บุคคลภายนอกเกิดความเสียหาย หรือไม่กระทำ การใดๆ อันเป็นความผิดต่อ พรบ. ว่าด้วย การกระทำ ความผิดเกี่ยวกับคอมพิวเตอร์ หรือกฎหมายอื่นใด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">8. บริษัทฯ ตกลงที่จะไม่เปิดเผยข้อมูลของผู้ใช้บริการ เว้นแต่เป็นการขอข้อมูลตามที่กฎหมายกำหนด</p>'
        }
      ],
      Datasone: [
        {
          body: '<p style="font-size: 12px;color: #333333;">1. กําหนดยืนราคา 30 วัน</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px">2. เก็บค่าบริการล่วงหน้าเป็นงวด งวดละ 1 เดือน (ตามข้อตกลงว่าจ้าง)</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px">3.หากผู้ใช้บริการผิดนัดไม่ชำระค่าบริการและ/หรือค่าธรรมเนียมเกินกว่าระยะเวลาที่กําหนดผู้ใช้บริการตกลงยินยอมให้ One Email ดําเนินการดังต่อ ไปนี้</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.1 ในกรณีีที่ผู้ใช้บริการผิดนัดไมชำระค่าบริการและ/หรือค่าธรรมเนียมให้ครบถ้วนภายใน 0-15 วัน นับถัดจากวันถึงกําหนดชําระ One Email ขอสงวนสิทธิในการระงับการซื้อบริการอื่นๆ ของผู้ใช้บริการเพิ่มเติมจนกว่าผู้ใช้บริการจะชําระหนี้ที่ค้างชําระอยู่นั้นให้แก่ One Email จนครบถ้วน</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.2 ในกรณีที่ผู้ใช้บริการผิดนัดไม่ชำระค่าบริการและ/หรือค่าธรรมเนียมให้ครบถ้วนภายใน 15-30 วันนับถัดจากวันถึงกําหนดชําระ One Email ขอสงวนสิทธิในการระงับการให้บริการทั้งหมดหรือบางส่วนในทันที จนกว่าผู้ใช้บริการจะชําระหนี้ที่ค้างชําระอยู่น่ั้นให้แก่ One Email จนครบถ้วน ทั้งนี้ ผู้ใช้บริการตกลงและรับทราบว่าหากผู้ใช้บริการประสงค์ที่จะใช้บริการต่อภายหลังจากที่ One Email ได้ระงับการให้บริการ ผู้ใช้บริการจะต้องแจ้งความประสงค์ในการใช้บริการต่อให้ One Email ทราบเป็น ลายลักษณ์อักษรและต้องชําระค่าบริการเพิ่มเติมเป็นจํานวน 35% จากระยะเวลาสัญญาทีเหลืออยู่ ก่อนที่ One Email จะดําเนินการลบข้อมูล ของผู้ใช้บริการตามที่กําหนดในข้อ 3.3</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.3 ในกรณีที่ผู้ใช้บริการผิดนัดไม่ชำระค่าบริการและ/หรือค่าธรรมเนียมให้ครบถ้วนเกินกว่า 30 วัน นับถัดจากวันถึงกำหนดชำระ One Email ขอสงวนสิทธิในการเข้าถึง รวมถึงข้อมูลทั้งหมดของผู้ใช้บริการ ทั้งนี้ผู้ใช้บริการรับทราบว่าภายหลังจากการลบข้อมูลจะไม่สามารถกู้ข้อมูลดังกล่าวคืนได้</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -10px"><span class="ml-4"></span>3.4 ในกรณีผู้ใช้บริการผิดนัดไม่ชำระค่าธรรมเนียมหรือค่าบริการใดๆ ภายในระยะเวลาที่กำหนด ผู้ใช้บริการต้องชำระดอกเบี้ยผิดนัดในอัตรา ร้อยละ 1.5 ต่อเดือนของค่าธรรมเนียมหรือค่าใช้บริการที่ค้างชำระและในกรณีที่บริษัทฯต้องเสียค่า</p>'
        }
      ],
      Datastwo: [
        {
          body: '<p style="font-size: 12px;color: #333333">ใช้จ่ายในการติดตามหนี้ และ/หรือค่าเสียหายอื่นอันเกิดจากการผิดนัด ผู้ใช้บริการจะต้องรับผิดชดใช้ค่าใช้จ่าย และ/หรือค่าเสียหายนั้นๆทั้งหมด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333"><span class="ml-4"></span>ทั้งนี้ผู้ใช้บริการตกลงและรับทราบว่าการลบข้อมูลของผู้ใช้บริการข้างต้น เป็นการดำเนินการที่เกิดขึ้นจากการปฏิบัติผิดสัญญาของผู้ใช้บริการ ดังนั้นทาง One Email จึงไม่มีความรับผิดตามกฎหมาย รวมถึงไม่รับผิดชอบในความเสียหายอย่างใดที่เกิดขึ้นหรืออาจจะเกิดขึ้นแก่ผู้ใช้บริการอันเนื่องมาจากการใช้สิทธิระงับการให้บริการและการลบข้อมูลของผู้ใช้บริการตามสัญญาข้อนี้ และ One Email สงวนสิทธิที่จะเลิกสัญญาและเรียกร้องค่าเสียหายและค่าใช้จ่าย (ซึ่งรวมถึงแต่ไม่จำกัดเพียงค่าเสียหายหรือค่าใช้จ่ายที่ระบุไว้ในสัญญาฉบับนี้) จากผู้ใช้บริการเพราะเหตุที่ผู้ใช้บริการผิดนัดชำระหนี้ดังกล่าวด้วย ในการนี้ ผู้ใช้บริการตกลงสละสิทธิในการเรียกร้องค่าเสียหายหรือดำเนินการทางกฎหมายใดๆที่เกี่ยวข้องกับกรณีดังกล่าวทั้งหมด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">4. ก่อนครบกำหนดระยะเวลาขั้นต่ำที่ระบุไว้ ไม่น้อยกว่า 30 (สามสิบ)วัน หากผู้ใช้บริการไม่ประสงค์จะต่ออายุการใช้บริการ ให้บอกกล่าวให้บริษัทฯทราบเป็นลายลักษณ์อักษร มิเช่นนั้นแล้วผู้ใช้บริการตกลงให้มีการต่ออายุการใช้บริการออกไปโดยไม่มีกำหนดระยะเวลาจนกว่าผู้ใช้บริการจะได้มีการแจ้งยกเลิกการใช้บริการเป็นลายลักษณ์อักษรมายังบริษัทฯ</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">5. กรณีที่บริษัทฯ ได้ทำการติดตั้งบริการแล้ว หากผู้ใช้บริการยกเลิกบริการก่อนครบระยะเวลาที่กำหนดโดยไม่ได้เกิดจากความผิดของบริษัทฯ ผู้ใช้บริการมีหน้าที่ชำระค่าปรับและค่าเสียหาย ให้แก่บริษัทฯตามที่บริษัทฯ กำหนด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #33333; margin-top: -5px">6. การรับประกันคุณภาพบริการ SLA Uptime 99.90% หรือขึ้นอยู่กับเอกสาร Service Level Agreement บริษัท วัน อีเมล จํากัด ยกเว้นแต่เหตุขัดข้องนั้นเกิดจากเหตุสุดสุ วิสัย หรือเกิดจากผู้ใช้บริการ</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">7. ผู้ใช้บริการต้องไม่กระทำการใดๆ ที่เป็นต้นเหตุทำให้บุคคลภายนอกเกิดความเสียหาย หรือไม่กระทำการใดๆ อันเป็นความผิดต่อ พรบ. ว่าด้วยการกระทำความผิดเกี่ยวกับคอมพิวเตอร์หรือกฎหมายอื่นใด</p>'
        },
        {
          body: '<p style="font-size: 12px;color: #333333; margin-top: -5px">8. บริษัทฯ ตกลงที่จะไม่เปิดเผยข้อมูลของผู้ใช้บริการเว้นแต่เป็นการขอข้อมูลตามที่กฎหมายกำหนด</p>'
        }
      ],
      Rules: {
        number: [
          v => !!v || 'กรุณากรอกค่าจัดส่ง'
        ]
      },
      itemSelect: [
        { id: 1, name: 'ชำระทันที', name_eng: '' },
        { id: 2, name: 'เครดิตเทอม', name_eng: 'Credit Term' }
      ],
      itemSelectInstallments: [
        { id: 1, name: 1 },
        { id: 2, name: 2 },
        { id: 3, name: 3 },
        { id: 4, name: 4 },
        { id: 5, name: 5 },
        { id: 6, name: 6 },
        { id: 7, name: 7 },
        { id: 8, name: 8 },
        { id: 9, name: 9 },
        { id: 10, name: 10 },
        { id: 11, name: 11 },
        { id: 12, name: 12 }
      ],
      formData: {
        customerName: '',
        detailsProduct: [
          { description: '', quantity: '', unit: '', cost: '' }
        ],
        address: '',
        taxpayerIdentificationNo: '',
        quotationNumber: '',
        date: '',
        credit: '',
        signaturePersonality1: '',
        signaturePersonality2: '',
        signaturePersonality3: ''
      },
      CHKproductList: true,
      discountAll: '',
      couponId: '',
      unitCP: '',
      points: '',
      quotationModel: false,
      quotationId: '',
      loading: false,
      loading2: false,
      roleQuShop: '',
      dataForm: {},
      numOfCreditTerm: '',
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      menu: false,
      modal: false,
      menu1: false,
      menu2: false,
      dateStart: '',
      mountTotal: 0,
      dateFormattedStart: '',
      dateFormattedEnd: '',
      btnShipping: true,
      itemShipping: '',
      something: '',
      amount: 0,
      UserRoleSet: {
        buyer_name: false,
        address: false,
        tax_id: false,
        dates: false,
        qu_number: false,
        payment_method: false,
        credit_term: false,
        total_shipping: false,
        buyer_name_by: false,
        prepared_by: false,
        approve_by: false,
        remark: false,
        logo: false,
        couponAndpoint: false,
        installment: false,
        btnAdd: false,
        btnRemove: false,
        quantity: false,
        thText: false
      }
    }
  },
  async created () {
    this.user_role = this.$route.query.role
    this.quotationId = this.$route.query.qu_id
    var dataForm = JSON.parse(Decode.decode(localStorage.getItem('dataForm')))
    await this.$store.dispatch('actionsEditQU', dataForm)
    const { result = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQU
    if (result === 'SUCCESS') {
      this.$store.state.ModuleAdminManage.QuotationformData = await data
      if (this.quotationDetail.qu_detail.json_data.date.data_date.length > 1) {
        this.dateFormattedStart = this.quotationDetail.qu_detail.json_data.date.data_date[0].start_day
        this.dateFormattedEnd = this.quotationDetail.qu_detail.json_data.date.data_date[this.quotationDetail.qu_detail.json_data.date.data_date.length - 1].end_day
      } else {
        this.dateFormattedStart = this.quotationDetail.qu_detail.json_data.date.data_date[0].start_day
        this.dateFormattedEnd = this.quotationDetail.qu_detail.json_data.date.data_date[0].end_day
      }
      this.$store.commit('closeLoader')
    }
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    if (this.user_role === 'purchaser') {
      this.$EventBus.$emit('changeNavCompany')
      this.$EventBus.$emit('chackAuthority')
    } else {
      this.$EventBus.$emit('changeNav')
      this.$EventBus.$emit('AuthorityUsers')
    }
  },
  mounted () {
    if (this.quotationDetail.qu_detail.product_list.length === 0) {
      this.CHKproductList = false
    }
    if (this.user_role === 'purchaser') {
      this.UserRoleSet.btnAdd = true
      this.UserRoleSet.btnRemove = true
      if (this.quotationDetail.qu_detail.status === 'active' && this.quotationDetail.qu_detail.status_use_discount === 'yes') {
        this.UserRoleSet.couponAndpoint = true
      } else {
        this.UserRoleSet.buyer_name = true
        this.UserRoleSet.payment_method = true
        this.UserRoleSet.buyer_name_by = true
        this.UserRoleSet.dates = true
        this.UserRoleSet.installment = true
        this.UserRoleSet.btnAdd = true
        this.UserRoleSet.btnRemove = true
        this.UserRoleSet.quantity = true
      }
    } else {
      this.UserRoleSet.payment_method = true
      this.UserRoleSet.logo = true
      this.UserRoleSet.qu_number = true
      this.UserRoleSet.dates = true
      this.UserRoleSet.total_shipping = true
      this.UserRoleSet.remark = true
      this.UserRoleSet.prepared_by = true
      this.UserRoleSet.approve_by = true
      this.UserRoleSet.installment = true
      this.UserRoleSet.btnAdd = true
      this.UserRoleSet.btnRemove = true
      this.UserRoleSet.quantity = true
    }
  },
  computed: {
    InnerWidth () {
      return window.innerWidth
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    excludingVat () {
      if (this.quotationDetail.qu_detail.product_list !== undefined) {
        return this.quotationDetail.qu_detail.product_list.reduce((a, c) => {
          return a + Number((c.revenue_default * c.quantity) || 0)
        }, 0)
      } else {
        return 0
      }
    },
    excludingVatAmount () {
      return this.quotationDetail.qu_detail.product_list.reduce((a, c) => {
        const [year, month, day] = this.dateFormattedStart.split('-')
        const [day2] = day.split(' ')
        var Start = `${day2} ${month} ${year}`
        console.log('tongckeck', this.daysInMonth(month, year))
        console.log('dateFormattedStart', Start)
        const [yearEnd, monthEnd, dayEnd] = this.dateFormattedEnd.split('-')
        const [dayEnd2] = dayEnd.split(' ')
        var End = `${dayEnd2} ${monthEnd} ${yearEnd}`
        console.log('dateFormattedEnd', End)
        var totalMonth = (monthEnd - month) === 0 ? (monthEnd - month) + 1 : (monthEnd - month) + 1
        var totalPrice = 0
        for (let i = 0; i < totalMonth; i++) {
          if (i === 0) {
            totalPrice += (Number((c.revenue_default) / this.daysInMonth(month, year))) * ((this.daysInMonth(month, year) - day2) + 1)
          } else if (i === (totalMonth - 1)) {
            totalPrice += (Number((c.revenue_default) / this.daysInMonth(month, year))) * dayEnd2
          } else {
            totalPrice += (Number((c.revenue_default) / this.daysInMonth(month, year)))
          }
        }
        return a + Number(totalPrice)
        // return a + Number((Math.abs(c.quantity) * c.revenue_default) * this.month || 0)
      }, 0)
    },
    includingVat () {
      return parseFloat(this.excludingVatAmount) + parseFloat(this.Vat)
    },
    Vat () {
      return parseFloat(this.excludingVatAmount * 0.07).toFixed(2)
    },
    thaiIncludingVat () {
      return THBText(this.excludingVat + parseFloat(this.quotationDetail.total_shipping !== '' ? this.quotationDetail.total_shipping : 0))
    },
    addressCompany () {
      return this.$store.state.ModuleAdminManage.QuotationformData.user_detail
    },
    productShop () {
      return this.$store.state.ModuleAdminManage.QuotationformData.product_of_shop
    },
    quotationDetail () {
      return this.$store.state.ModuleAdminManage.QuotationformData
    },
    checkStock () {
      if (this.quotationDetail.qu_detail.product_list !== undefined) {
        return this.quotationDetail.qu_detail.product_list.map(x => { return { quantity: Math.abs(x.quantity), stock: x.stock } })
      } else {
        return ''
      }
    },
    checkButton () {
      return this.quotationDetail.qu_detail.product_list.map(x => { return { quantity: Math.abs(x.quantity) } })
    },
    dateTH () {
      return new Date(this.quotationDetail.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
    },
    convertDate () {
      if (this.quotationDetail.dateEnd !== '') {
        const [year, month, day] = this.quotationDetail.qu_detail.json_data.date.data_date[0].start_day.split('-')
        const [day2] = day.split(' ')
        return `${day2}-${month}-${year}`
      } else {
        return ''
      }
    },
    dateTime () {
      return ''
    },
    convertDateEnd () {
      if (this.quotationDetail.dateEnd !== '') {
        const [year, month, day] = this.quotationDetail.dateEnd.split('-')
        const [day2] = day.split(' ')
        return `${day2}-${month}-${year}`
      } else {
        return ''
      }
    },
    dateTimeEnd () {
      return this.convertDateEnd
    },
    computedDateFormatted () {
      return this.formatDate(this.quotationDetail.date)
    },
    month () {
      if (this.dateFormattedStart !== '' && this.dateFormattedEnd !== '') {
        const [year, month, day] = this.dateFormattedStart.split('-')
        const [day2] = day.split(' ')
        var Start = `${day2} ${month} ${year}`
        console.log('tongckeck', this.daysInMonth(month, year))
        console.log('dateFormattedStart', Start)
        const [yearEnd, monthEnd, dayEnd] = this.dateFormattedEnd.split('-')
        const [dayEnd2] = dayEnd.split(' ')
        var End = `${dayEnd2} ${monthEnd} ${yearEnd}`
        console.log('dateFormattedEnd', End)
        var totalMonth = (monthEnd - month) === 0 ? (monthEnd - month) + 1 : (monthEnd - month) + 1
        return totalMonth
        // return Math.abs(this.getMonthDifference(new Date(this.dateFormattedEnd), new Date(this.dateFormattedStart))) === 0 ? 1 : Math.abs(this.getMonthDifference(new Date(this.dateFormattedEnd), new Date(this.dateFormattedStart)))
      } else {
        return 1
      }
    }
  },
  watch: {
    'quotationDetail.payment_method' (e) {
      if (e === 'ชำระทันที') {
        this.quotationDetail.credit_term = 0
      } else {
        this.quotationDetail.credit_term = this.quotationDetail.credit_term_default
      }
    },
    'quotationDetail.qu_detail.product_list' (list) {
      if (list.length !== 0 && list.every(x => x.quantity !== '' && x.quantity > 0)) {
        this.CHKproductList = true
      } else {
        this.CHKproductList = false
      }
    },
    dateTime (val) {
      this.dateFormattedStart = this.formatDate(this.quotationDetail.dateStart)
    },
    // dateTimeEnd (val) {
    //   this.dateFormattedEnd = this.formatDate(this.quotationDetail.dateEnd)
    // },
    checkStock (val) {
      for (const a in val) {
        this.quotationDetail.product_list[a].quantity = Math.abs(val[a].quantity)
      }
    },
    checkButton (val) {
      this.btnShipping = val.every(x => x.quantity !== '' && x.quantity > 0)
    }
  },
  methods: {
    getMonthDifference (startDate, endDate) {
      return (
        endDate.getMonth() -
        startDate.getMonth() +
        12 * (endDate.getFullYear() - startDate.getFullYear())
      )
    },
    daysInMonth (month, year) {
      return new Date(year, month, 0).getDate()
    },
    formatDate (date) {
      if (!date) return null
      const dates = new Date(date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
      return `${dates}`
    },
    afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [day, month, year] = date.split('-')
      return `${year}-${month}-${day}`
    },
    dateTimeIH (e) {
      return new Date(this.quotationDetail.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
    },
    formatTofixed (val) {
      return val.toFixed(2)
    },
    // function open Add item
    async openModalPurchaseOrder (index) {
      this.$store.state.ModuleShop.openModalPurchaseOrder = await true
      for (let i = 0; i < this.$store.state.ModuleAdminManage.ListProductOfShop.length; i++) {
        this.$store.state.ModuleAdminManage.ListProductOfShop[i].quantity = 1
      }
      await this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.splice(index, 1)
    },
    // function Add Row
    addRow () {
      this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.push({ status: '0' })
      if (this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.length === (11 * this.items.length)) {
        this.items.push({
          index: 1
        })
      }
      this.$store.state.ModuleAdminManage.ListProductOfShop = this.productShop
    },
    // function Delete Row
    deleteRow (index) {
      this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.splice(index, 1)
      if (this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.length < (11 * this.items.length) && this.items.length > 1) {
        this.items.splice(index, 1)
      }
    },
    async EditQt () {
      var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
      const [year, month, day] = this.dateFormattedStart.split('-')
      const [day2] = day.split(' ')
      var datadateStart = `${day2}-${month}-${year}`
      const [yearEnd, monthEnd, dayEnd] = this.dateFormattedStart.split('-')
      const [dayEnd2] = dayEnd.split(' ')
      var datadateEnd = `${dayEnd2}-${monthEnd}-${yearEnd}`
      var dataproduct = FormsAll.qu_detail.product_list.map(e => {
        return {
          product_id: e.product_id,
          // attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
          price: e.revenue_default,
          main_sku: e.product_sku,
          sku: e.sku,
          item_code: e.item_code === null ? '' : e.item_code,
          product_name: e.product_name,
          unit_type: e.unit_type === null ? '' : e.unit_type,
          quantity: e.quantity,
          cost_unit: e.cost_unit,
          internal: e.internal,
          external_jv: e.external_jv,
          external: e.external,
          actual_cost: e.actual_cost,
          eng_cost: e.eng_cost,
          inventory_code: e.inventory_code,
          product_ratio: e.product_ratio,
          actual_stock: e.actual_stock,
          effective_stock: e.effective_stock,
          category_data: e.category_data,
          manufacturer_data: e.manufacturer_data,
          supplier_data: e.supplier_data,
          have_attribute: e.have_attribute,
          product_status: e.product_status,
          product_image: e.product_image,
          volumn: {
            width: 0,
            length: 0,
            height: 0,
            weight: 0
          },
          revenue_default: e.revenue_default * e.quantity,
          vat_type: 7,
          vat_revenue: (e.revenue_default * e.quantity) * 0.07,
          revenue_vat: (e.revenue_default * e.quantity) + ((e.revenue_default * e.quantity) * 0.07)
        }
      })
      var DataQT = {
        order_number: this.quotationDetail.qu_detail.qu_number.slice(3),
        seller_shop_id: this.quotationDetail.qu_detail.seller_shop_id,
        company_id: this.quotationDetail.qu_detail.company_id,
        com_perm_id: this.quotationDetail.qu_detail.com_perm_id,
        pay_type: this.quotationDetail.qu_detail.pay_type,
        type_budget: this.quotationDetail.qu_detail.type_budget,
        budget_cut: this.quotationDetail.qu_detail.budget_cut,
        customer_data: {
          name: this.quotationDetail.qu_detail.json_data.customer_data.name,
          company_name: this.quotationDetail.qu_detail.json_data.customer_data.company_name,
          address: this.quotationDetail.qu_detail.json_data.customer_data.address === null ? '' : this.quotationDetail.qu_detail.json_data.customer_data.address,
          tel_no: this.quotationDetail.qu_detail.json_data.customer_data.tel_no,
          mobile_no: this.quotationDetail.qu_detail.json_data.customer_data.mobile_no === null ? ' ' : this.quotationDetail.qu_detail.json_data.customer_data.mobile_no,
          email: this.quotationDetail.qu_detail.json_data.customer_data.email
        },
        sale_data: {
          name: this.quotationDetail.qu_detail.json_data.sale_data.name,
          tel_no: this.quotationDetail.qu_detail.json_data.sale_data.tel_no,
          mobile_no: this.quotationDetail.qu_detail.json_data.sale_data.mobile_no,
          email: this.quotationDetail.qu_detail.json_data.sale_data.email
        },
        date: {
          term: this.month,
          start_date: datadateStart,
          end_date: datadateEnd
        },
        quoted: {
          name: '',
          position: ''
        },
        approved: {
          name: '',
          position: ''
        },
        product_list: dataproduct
      }
      // console.log('tong action', DataQT)
      await this.$store.dispatch('actionsEditQt_V2', DataQT)
      var responseCreateQt = this.$store.state.ModuleAdminManage.stateEditQtV2
      if (responseCreateQt.code === 200) {
        window.open(responseCreateQt.data.path_pdf_qt, '_blank')
        this.$router.push({ path: '/QuotationAll' })
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
    },
    backtoList () {
      this.$router.push({ path: `${this.$store.state.ModuleAdminManage.stateURLQu}` }).catch(() => { })
    },
    async createQT () {
      var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
      var dataproduct = FormsAll.qu_detail.qu_detail.product_list.map(e => {
        return {
          product_id: e.product_id,
          attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
          price: e.revenue_default,
          main_sku: e.product_sku,
          sku: e.sku,
          item_code: e.item_code === null ? '' : e.item_code,
          product_name: e.product_name,
          unit_type: e.unit_type === null ? '' : e.unit_type,
          quantity: e.quantity,
          cost_unit: e.cost_unit,
          internal: e.internal,
          external_jv: e.external_jv,
          external: e.external,
          actual_cost: e.actual_cost,
          eng_cost: e.eng_cost,
          inventory_code: e.inventory_code,
          product_ratio: e.product_ratio,
          actual_stock: e.actual_stock,
          effective_stock: e.effective_stock,
          category_data: e.category_data,
          manufacturer_data: e.manufacturer_data,
          supplier_data: e.supplier_data,
          have_attribute: e.have_attribute,
          product_status: e.product_status,
          product_image: e.product_image,
          volumn: {
            width: 0,
            length: 0,
            height: 0,
            weight: 0
          },
          revenue_default: e.revenue_default * e.quantity,
          vat_type: 7,
          vat_revenue: (e.revenue_default * e.quantity) * 0.07,
          revenue_vat: (e.revenue_default * e.quantity) + ((e.revenue_default * e.quantity) * 0.07)
        }
      })
      var DataQT = {
        seller_shop_id: this.quotationDetail.seller_shop_id,
        company_id: this.quotationDetail.company_id,
        com_perm_id: this.$store.state.ModuleAdminManage.QuotationformData.detail.com_perm_id,
        pay_type: this.$store.state.ModuleAdminManage.QuotationformData.detail.pay_type,
        type_budget: this.$store.state.ModuleAdminManage.QuotationformData.detail.type_budget,
        budget_cut: this.$store.state.ModuleAdminManage.QuotationformData.detail.budget_cut,
        customer_data: {
          name: this.quotationDetail.user_detail.customer_name,
          company_name: this.quotationDetail.user_detail.company_name,
          address: this.quotationDetail.user_detail.address,
          tel_no: this.quotationDetail.user_detail.tel_no,
          mobile_no: this.quotationDetail.user_detail.mobile,
          email: this.quotationDetail.user_detail.email
        },
        sale_data: {
          name: this.quotationDetail.sale_detail.sale_name,
          tel_no: this.quotationDetail.sale_detail.sale_tel_number,
          mobile_no: this.quotationDetail.sale_detail.sale_mobile,
          email: this.quotationDetail.sale_detail.sale_email
        },
        date: {
          term: this.month,
          start_date: this.quotationDetail.dateStart,
          end_date: this.quotationDetail.dateEnd
        },
        quoted: {
          name: '',
          position: ''
        },
        approved: {
          name: '',
          position: ''
        },
        product_list: dataproduct
      }
      await this.$store.dispatch('actionsCreateQt', DataQT)
      var responseCreateQt = this.$store.state.ModuleAdminManage.stateCreateQt
      if (responseCreateQt.code === 200) {
        window.open(responseCreateQt.data.path_pdf_qt, '_blank')
        if (localStorage.getItem('part_old') !== null) {
          this.$router.push({ path: localStorage.getItem('part_old') })
        } else {
          this.$router.push({ path: '/' })
        }
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
    },
    async shippingCostSeller () {
      this.loading = true
      this.unitCP = ''
      if (this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon.length !== 0) {
        this.discountAll = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].real_discount
        this.couponId = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.coupon[0].couponId
        this.unitCP = await 'คูปอง'
      } else if (this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point.length !== 0) {
        this.discountAll = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].amount
        this.points = await this.$store.state.ModuleMyCouponsPoints.SelectCouponOrPoint.point[0].amount
        this.unitCP = await 'คะแนน'
      } else {
        this.discountAll = ''
        this.couponId = ''
        this.points = ''
      }
      var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
      // console.log('qu_detail.id22', FormsAll.qu_detail.id)
      const dataForm = {
        role_user: this.user_role,
        qu_id: '-1',
        total_discount: this.discountAll,
        seller_shop_id: FormsAll.qu_detail.seller_shop_id,
        company_id: FormsAll.qu_detail.company_id,
        product_list: FormsAll.qu_detail.product_list.map(e => {
          return {
            product_id: e.product_id,
            attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
            have_attribute: e.product_attribute_detail.product_attribute_id === null ? 'no' : 'yes',
            quantity: e.quantity,
            price: e.price
          }
        })
      }
      await this.$store.dispatch('actionsEstimateQu', dataForm)
      var { result = '', data = {}, message = '' } = await this.$store.state.ModuleAdminManage.stateEstimateQu
      if (result === 'SUCCESS') {
        this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.total_shipping = data.total_shipping
        this.loading = false
      } else if (result === 'FAILED' && message === 'ข้อมูลไม่ครบ [หมายเหตุ] กรุณาตรวจสอบและลองใหม่อีกครั้ง') {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>ข้อมูลไม่ครบ</h3>' + 'กรุณากรอกหมายเหตุให้ครบ' })
        this.loading = false
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' + message })
        this.loading = false
      }
    },
    async postFormData () {
      if (this.quotationDetail.total_shipping !== '') {
        this.loading = true
        var FormsAll = await this.$store.state.ModuleAdminManage.QuotationformData
        if (this.quotationId !== '-1') {
          this.dataForm = await {
            role_user: this.user_role,
            num_of_credit_term: this.quotationDetail.payment_method === 'เครดิตเทอม' ? this.quotationDetail.num_of_credit_term : 1,
            total_discount: this.discountAll,
            coupon_id: this.couponId,
            point: this.points,
            qu_id: this.quotationId === '0' ? '' : this.quotationId,
            logo_path: this.$store.state.ModuleAdminManage.stateImgQu !== '' ? this.$store.state.ModuleAdminManage.stateImgQu : FormsAll.qu_detail.logo_path,
            seller_shop_id: FormsAll.qu_detail.seller_shop_id,
            company_id: FormsAll.qu_detail.company_id,
            com_perm_id: FormsAll.qu_detail.com_perm_id,
            qu_number: FormsAll.qu_detail.qu_number,
            company_name: FormsAll.qu_detail.company_name,
            address: FormsAll.qu_detail.address,
            tax_id: FormsAll.qu_detail.tax_id,
            date: this.afterDate(FormsAll.qu_detail.date),
            payment_method: FormsAll.qu_detail.payment_method,
            credit_term: FormsAll.qu_detail.credit_term,
            buyer_name: FormsAll.qu_detail.buyer_name,
            prepared_by: FormsAll.qu_detail.prepared_by,
            approve_by: FormsAll.qu_detail.approve_by,
            total_shipping: FormsAll.qu_detail.total_shipping,
            note: FormsAll.qu_detail.note,
            product_list: FormsAll.qu_detail.product_list.length !== 0 ? FormsAll.qu_detail.product_list.map(e => {
              return {
                product_id: e.product_id,
                attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
                quantity: e.quantity,
                price: e.revenue_default
              }
            }) : []
          }
        } else {
          this.dataForm = await {
            role_user: this.user_role,
            num_of_credit_term: this.quotationDetail.payment_method === 'เครดิตเทอม' ? this.quotationDetail.num_of_credit_term : 1,
            total_discount: this.discountAll,
            coupon_id: this.couponId,
            point: this.points,
            qu_id: '',
            logo_path: this.$store.state.ModuleAdminManage.stateImgQu !== '' ? this.$store.state.ModuleAdminManage.stateImgQu : FormsAll.qu_detail.logo_path,
            seller_shop_id: FormsAll.qu_detail.seller_shop_id,
            company_id: FormsAll.qu_detail.company_id,
            com_perm_id: FormsAll.qu_detail.com_perm_id,
            qu_number: FormsAll.qu_detail.qu_number,
            company_name: FormsAll.qu_detail.company_name,
            address: FormsAll.qu_detail.address,
            tax_id: FormsAll.qu_detail.tax_id,
            date: this.afterDate(FormsAll.qu_detail.date),
            payment_method: FormsAll.qu_detail.payment_method,
            credit_term: FormsAll.qu_detail.credit_term,
            buyer_name: FormsAll.qu_detail.buyer_name,
            prepared_by: FormsAll.qu_detail.prepared_by,
            approve_by: FormsAll.qu_detail.approve_by,
            total_shipping: FormsAll.qu_detail.total_shipping,
            note: FormsAll.qu_detail.note,
            product_list: FormsAll.qu_detail.product_list.length !== 0 ? FormsAll.qu_detail.product_list.map(e => {
              return {
                product_id: e.product_id,
                attribute_id: e.product_attribute_detail.product_attribute_id === null ? -1 : e.product_attribute_detail.product_attribute_id,
                quantity: e.quantity,
                price: e.revenue_default
              }
            }) : []
            // product_list: FormsAll.qu_detail.product_list
          }
        }
        const dataFormPurchaser = {
          address_id: '',
          budget_start_date: null,
          business_id: null,
          code: FormsAll.business_detail.business_code,
          company_address_id: 0,
          created_at: FormsAll.business_detail.created_at,
          created_by: FormsAll.business_detail.created_by,
          credit: 0,
          customer_code: null,
          customer_type_id: null,
          discount_percent: FormsAll.qu_detail.product_list.find(x => x.discount_percent),
          fax: FormsAll.business_detail.fax_no,
          fix_price_end_date: '',
          fix_price_start_date: '',
          id: FormsAll.qu_detail.company_id,
          img_path: '',
          name_en: FormsAll.business_detail.first_name_eng,
          name_th: FormsAll.business_detail.first_name_th,
          phone: FormsAll.business_detail.mobile_no,
          status: '',
          tax_id: FormsAll.qu_detail.tax_id,
          tel: null,
          type_budget: null,
          type_budget_roll_over: '',
          type_level: '',
          type_price: '',
          type_promotion: '',
          type_shipping: '',
          updated_at: '',
          updated_by: ''
        }
        const qp = await FormsAll.qu_detail.product_list.every(x => x.quantity !== '' && x.quantity > 0)
        const q = await [FormsAll.qu_detail.buyer_name !== '' && FormsAll.qu_detail.qu_number !== ''].includes(true)
        if (q && qp) {
          await this.$store.dispatch('actionsEditQuotation', this.dataForm)
          var { result = '', message = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQuotation
          this.loading = false
          if (this.user_role === 'seller' && result === 'SUCCESS') {
            await this.$router.push({ path: `/QuotationDetail?QUNumber=${data.qu_id}&id=${FormsAll.qu_detail.seller_shop_id}&comID=${FormsAll.qu_detail.company_id}` }).catch(() => { })
          } else if (this.user_role === 'purchaser' && result === 'SUCCESS') {
            await localStorage.setItem('dataFormPurchaser', Encode.encode(dataFormPurchaser))
            await this.$router.push({ path: `/QUCompanyDetail?QU_ID=${data.qu_id}&id=${FormsAll.qu_detail.company_id}&shopID=${FormsAll.qu_detail.seller_shop_id}` }).catch(() => { })
          } else if (this.user_role === 'purchaser' && result === 'FAILED' && message === 'quantity should be than 0.') {
            this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกจำนวนสินค้า</h3>' })
          } else if (this.user_role === 'purchaser' && result === 'FAILED' && message === 'Data missing. Please check your ["product_list"] and try again.') {
            this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณาตรวจสอบรายการสินค้าของคุณ</h3>' })
          } else {
            this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' + message })
          }
        } else {
          this.loading = false
          this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกข้อมูลให้ครบ</h3>' })
        }
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: '<h3>กรุณากรอกข้อมูลค่าจัดส่ง</h3>' })
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import './po.css';
@import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@100;200&display=swap');
.logo-qu {
  background-image: url("data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%201320%20300%22%3E%0A%09%3Ctext%20x%3D%2250%25%22%20y%3D%2250%25%22%20dy%3D%22.35em%22%20text-anchor%3D%22middle%22%3E%0A%09%09LOGO%0A%09%3C%2Ftext%3E%0A%3C%2Fsvg%3E%09");
  background-repeat: no-repeat;
}
.background-image {
  background-image: url(~@/assets/ICON/Notlog.jpg);
  background-position: center;
  background-size: 400px 190px;
}
::v-deep .v-messages__message {
  font-size: 8px;
}
::v-deep .theme--light.v-icon:focus::after {
  opacity: 0;
}
::v-deep #char1 .v-text-field {
  width: 25px;
}
::v-deep.v-text-field.v-text-field--solo .v-input__control {
  min-height: 0px;
  padding: 0;
}

::v-deep.v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
  min-height: 28px;
}
::v-deep.v-input__control {
    display: flex;
    width: 100%;
}
.center-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex-direction: column;
}
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
svg {
  font-family: 'Russo One', sans-serif;
  position: absolute;
  width: 100%; height: 100%;
  border-radius: 50%;
}
svg text {
  text-transform: uppercase;
  animation: stroke 5s infinite alternate;
  stroke-width: 2;
  stroke: #365fa0;
  font-size: 140px;
}
@keyframes stroke {
  0%   {
    fill: rgba(72,138,20,0); stroke: rgba(54,95,160,1);
    stroke-dashoffset: 25%; stroke-dasharray: 0 50%; stroke-width: 2;
  }
  70%  {fill: rgba(72,138,20,0); stroke: rgba(54,95,160,1); }
  80%  {fill: rgba(72,138,20,0); stroke: rgba(54,95,160,1); stroke-width: 3; }
  100% {
    fill: rgba(72,138,204,1); stroke: rgba(54,95,160,0);
    stroke-dashoffset: -25%; stroke-dasharray: 50% 0; stroke-width: 0;
  }
}
</style>

<style>
thead {
  color: #fff;
}
</style>
<style lang="scss">
.v-text-field--filled.v-input--dense.v-text-field--single-line > .v-input__control > .v-input__slot, .v-text-field--filled.v-input--dense.v-text-field--outlined > .v-input__control > .v-input__slot, .v-text-field--filled.v-input--dense.v-text-field--outlined.v-text-field--filled > .v-input__control > .v-input__slot, .v-text-field--full-width.v-input--dense.v-text-field--single-line > .v-input__control > .v-input__slot, .v-text-field--full-width.v-input--dense.v-text-field--outlined > .v-input__control > .v-input__slot, .v-text-field--full-width.v-input--dense.v-text-field--outlined.v-text-field--filled > .v-input__control > .v-input__slot, .v-text-field--outlined.v-input--dense.v-text-field--single-line > .v-input__control > .v-input__slot, .v-text-field--outlined.v-input--dense.v-text-field--outlined > .v-input__control > .v-input__slot, .v-text-field--outlined.v-input--dense.v-text-field--outlined.v-text-field--filled > .v-input__control > .v-input__slot {
  min-height: 34px;
}
</style>
