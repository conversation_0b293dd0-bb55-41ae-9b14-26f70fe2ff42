<template>
  <v-container>
    <v-breadcrumbs :items="items">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
         :href="item.href"
         :disabled="item.disabled"
        >
          {{ item.text }}
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <CategoryCarousel :pathCate="items" />
    <CategoryPage :bestCategory="productBestSeller" :categoryName="headerCategory" :productCategory="productCategory"/>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
const productBestSeller = []
for (let i = 0; i < 6; i++) {
  productBestSeller.push({
    product_id: i,
    name: `Data Title productBestSeller ${i}`,
    price: ` ${i + 1 * 100}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
const productCategory = []
for (let i = 0; i < 48; i++) {
  productCategory.push({
    product_id: i,
    name: `Data Title product ${i}`,
    price: ` ${(i + 1) * 100}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CategoryCarousel: () => import('../components/Category/CategoryCarousel'),
    CategoryPage: () => import('../components/Category/CategoryPage')
  },
  data () {
    return {
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        }
      ],
      productBestSeller,
      categoryList: [],
      headerCategory: '',
      productCategory
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.categoryList = JSON.parse(Decode.decode(localStorage.getItem('categoryList')))
    this.items.push({
      text: this.categoryList.category_name,
      disabled: true,
      href: ''
    })
    this.headerCategory = this.categoryList.category_name
  }
}
</script>

<style scoped>
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 0px 0px 0px 12px !important;
}
.container {
  max-width: 1250px;
}
</style>
