<template>
  <v-container v-if="checkShop !== 'ไม่มีร้านค้า'">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-2">
      <v-col>
        <!-- Web -->
        <v-row dense>
          <!-- <v-col cols="12" md="3" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">
            รายการสินค้า
          </v-col> -->
          <v-col cols="12" md="2">
            <v-row dense class="mb-2">
              <v-col cols="12" xs="12" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" class="d-flex">
                <div><v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon> รายการสินค้า</div>
                <v-btn class="mb-2 ml-auto" text color="#1B5DD6" @click="DownloadExcel()" v-if="this.isJV === 'yes' && MobileSize"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn>
                <v-btn class="mb-2 ml-auto" text color="#1B5DD6" @click="openDialogForDownloadExcel()" v-if="this.isJV === 'no' && MobileSize"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="10" sm="12" align="end" class="pt-0" v-if="!MobileSize" data-v-step="2">
            <!-- :block="MobileSize || IpadSize" -->
            <v-btn text color="#1B5DD6" @click="DownloadExcel()" v-if="this.isJV === 'yes'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn>
            <v-btn text color="#1B5DD6" @click="openDialogForDownloadExcel()" v-if="this.isJV === 'no'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn>
            <v-btn outlined color="#27AB9C" rounded width="225" v-if="this.isJV === 'yes'" height="40" @click="ImportExcel" data-v-step="3"><v-icon left size="24">mdi-file-plus-outline</v-icon>เพิ่มสินค้าจากไฟล์ Excel</v-btn>
            <input @click="event => event.target.value = null" v-if="this.isJV === 'yes'" @change="UploadExcel($event)" id="importExcel" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
            <v-btn :disabled="DataTable.total_products_count === 0" @click="DialogUpdateExcael = true" class="mr-2" outlined color="#27AB9C" rounded width="225" v-if="this.isJV === 'no'" height="40"><v-icon left size="24">mdi-update</v-icon>อัพเดตสินค้าจากไฟล์ Excel</v-btn>
            <v-btn :class="IpadSize || IpadProSize ? 'ml-2 mt-2' : ''" outlined color="#27AB9C" rounded width="225" v-if="this.isJV === 'no'" height="40" @click="openDialoguploadExcelNoJV()" data-v-step="3"><v-icon left size="24">mdi-file-plus-outline</v-icon>เพิ่มสินค้าจากไฟล์ Excel</v-btn>
            <!-- <v-btn outlined color="#27AB9C" rounded width="225" v-if="this.isJV === 'no'" height="40" @click="ImportExcelNoJV" data-v-step="3"><v-icon left size="24">mdi-file-plus-outline</v-icon>เพิ่มสินค้าจากไฟล์ Excel</v-btn> -->
            <!-- <input @click="event => event.target.value = null" v-if="this.isJV === 'no'" @change="UploadExcelNoJV($event)" id="importExcelNoJV" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  /> -->
            <v-btn color="#27AB9C" width="113" height="40" :class="IpadSize || IpadProSize ? 'ml-2 mt-2' : 'ml-2'" rounded class="white--text" @click="CreateProduct" data-v-step="5"><v-icon left size="24">mdi-plus</v-icon>เพิ่มสินค้า</v-btn>
            <!-- <v-btn color="#27AB9C" width="113" height="40" :class="IpadSize ? 'ml-2 mt-2' : 'ml-2'" rounded class="white--text" @click="SyncProductERP" data-v-step="5" v-if="openSyncERP">SYNC ERP</v-btn> -->
          </v-col>
          <v-col cols="12" class="px-0" v-else>
            <v-btn block outlined color="#27AB9C" rounded v-if="this.isJV === 'yes'" height="40" @click="ImportExcel"><v-icon left size="24">mdi-file-plus-outline</v-icon>เพิ่มสินค้าจากไฟล์ Excel</v-btn>
            <input @click="event => event.target.value = null" v-if="this.isJV === 'yes'" @change="UploadExcel($event)" id="importExcel" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
            <v-btn block outlined color="#27AB9C" rounded v-if="this.isJV === 'no'" height="40" @click="openDialoguploadExcelNoJV()"><v-icon left size="24">mdi-file-plus-outline</v-icon>เพิ่มสินค้าจากไฟล์ Excel</v-btn>
            <v-btn :disabled="DataTable.total_products_count === 0" block outlined color="#27AB9C" rounded v-if="this.isJV === 'no'" height="40" @click="DialogUpdateExcael = true" class="mt-2"><v-icon left size="24">mdi-update</v-icon>อัพเดตสินค้าจากไฟล์ Excel</v-btn>
            <!-- <v-btn outlined color="#27AB9C" rounded width="225" v-if="this.isJV === 'no'" height="40" @click="ImportExcelNoJV"><v-icon left size="24">mdi-file-plus-outline</v-icon>เพิ่มสินค้าจากไฟล์ Excel</v-btn> -->
            <!-- <input @click="event => event.target.value = null" v-if="this.isJV === 'no'" @change="UploadExcelNoJV($event)" id="importExcelNoJV" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  /> -->
            <v-btn block class="mt-2" color="#27AB9C" height="40" rounded :class="this.$vuetify.breakpoint.lgOnly? 'white--text':'white--text'" @click="CreateProduct"><v-icon left size="24">mdi-plus</v-icon>เพิ่มสินค้า</v-btn>
            <!-- <v-btn block class="mt-2" color="#27AB9C" height="40" rounded :class="this.$vuetify.breakpoint.lgOnly? 'white--text':'white--text'" @click="SyncProductERP" v-if="openSyncERP">SYNC ERP</v-btn> -->
          </v-col>
        </v-row>
        <v-row dense class="mb-6 mt-2" v-if="!MobileSize">
          <v-col cols="12">
            <v-card width="100%" class="mt-4" height="100%" elevation="0" style="background: #F4F9FB; border-radius: 8px;">
              <v-card-text class="pa-0">
                <v-row>
                  <v-col cols="6" :style="IpadSize ? 'padding: 34px 0px 16px 16px;' : 'padding: 34px 0px 8px 16px;'" class="d-inline-block text-truncate">
                    <v-icon color="#27AB9C" class="px-2">mdi-storefront</v-icon>
                    <span style="font-size: 18px; font-weight: 400; line-height: 140%; color: #333333;" class="pt-2">ร้านค้า : </span><span style="font-size: 24px; font-weight: 700; line-height: 140%; color: #333333;" class="pt-2">{{ Shopname }}</span><br/><br/>
                    <v-icon color="#27AB9C" class="px-2">mdi-cube-outline</v-icon>
                    <span style="font-size: 18px; font-weight: 400; line-height: 140%; color: #333333;" class="pt-2">จำนวนสินค้า : </span>
                    <span style="font-size: 20px; font-weight: 700; line-height: 140%; color: #27AB9C;" class="pt-2 classTextCountProduct">{{ DataTable.total_products_count }}</span>
                  </v-col>
                  <v-col cols="6" class="pt-0 pb-2" style="display: flex; justify-content: flex-end;">
                    <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/ShopNew.png')" max-height="100%" max-width="379" width="100%" height="100%"></v-img>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <!-- Mobile -->
        <v-row dense class="mb-2 mt-2" v-else>
          <v-col cols="12">
            <v-card width="100%" class="mt-4 cardMobile" max-height="84" elevation="0">
              <v-card-text class="pa-0" style="max-height: 84px;">
                <v-row class="d-flex">
                  <v-col class="mr-auto">
                    <v-icon color="#27AB9C" class="px-2">mdi-storefront</v-icon>
                    <span style="font-size: 12px; font-weight: 400; line-height: 16px; color: #333333;" class="pt-2">ร้านค้า : </span>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <!-- <span v-bind="attrs" v-on="on" style="font-size: 14px; font-weight: 700; line-height: 19px; color: #333333;" class="pt-2">{{ Shopname|truncate(15, '...') }}</span> -->
                        <span v-bind="attrs" v-on="on" style="font-size: 14px; font-weight: 700; line-height: 19px; color: #333333; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" class="pt-2">{{ Shopname }}</span>
                      </template>
                      <span>{{ Shopname }}</span>
                    </v-tooltip>
                    <br/>
                    <v-icon color="#27AB9C" class="px-2 mt-1 mb-2">mdi-cube-outline</v-icon>
                    <span style="font-size: 12px; font-weight: 400; line-height: 16px; color: #333333;" class="pt-2 mt-2">จำนวนสินค้า : </span>
                    <span style="font-size: 12px; font-weight: 700; line-height: 16px; color: #27AB9C;" class="pt-2 mt-2 classTextCountProduct">{{ DataTable.total_products_count }}</span>
                  </v-col>
                  <!-- <v-col class="pt-0 pb-2 ml-auto">
                    <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/ShopNew.png')" max-height="84" max-width="170" width="100%" height="84"></v-img>
                  </v-col> -->
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <!-- <v-col md="1" sm="1" xs="1" class="pt-1">
            <v-col cols="12" class="py-0">
              <v-row dense>
                <v-col cols="3">
                  <v-avatar
                    tile
                    width="105px"
                    height="105px"
                  >
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/StoreNew.png" contain v-if="seller_shop_logo === ''"></v-img>
                    <v-img :src="seller_shop_logo.media_path" contain v-else></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="8" class="pl-4 ml-2">
                  <v-col md="12" sm="12" xs="12" class="pb-0">
                    <span style="font-size: 14px; line-height: 24px; font-weight: bold; color: #27AB9C;">{{ Shopname }}</span>
                  </v-col>
                  <v-col cols="12" md="5" sm="5" xs="12">
                    <v-row dense no-gutters>
                      <span style="color: #000000; font-size: 10px; font-weight: 400; line-height: 14px;">จำนวนสินค้า: {{DataTable.total_products_count}}</span>
                    </v-row>
                  </v-col>
                </v-col>
              </v-row>
            </v-col>
          </v-col> -->
        </v-row>
      </v-col>
      <!-- <v-col> -->
        <!-- <a-tabs>
          <a-tab-pane v-for="item in OrderName" :key="item.key" :tab="item.name">
            <a-row type="flex">
              <a-col :span="24"> -->
                <TableProduct :props='DataTable' :ShopID="seller_shop_id" :ShopName="Shopname" :JV="isJV" :openSyncERP="openSyncERP"/>
              <!-- </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs> -->
      <!-- </v-col> -->
    </v-card>
    <!-- <v-tour name="myTour.ShopPage" :steps="steps" :options="myOptions"></v-tour> -->
    <!-- Modal แสดงการ update product ที่ไม่สำเร็จ -->
    <v-dialog v-model="DialogResultUpdateProduct" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'" :style="!MobileSize ? '' : 'font-size: 16px'"><b>ผลลัพธ์การอัพเดตข้อมูล</b></span>
              </v-col>
              <v-btn fab small @click="DialogResultUpdateProduct = !DialogResultUpdateProduct" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex">
                  <v-col cols="12" md="12" sm="12" class="pt-3 mr-auto">
                    <span v-if="!MobileSize" style="font-size: 18px; font-weight: 500; color: #333333;">รายการผลลัพธ์อัพเดตข้อมูลไม่สำเร็จ {{ dataProductCantUpdate.length }} รายการ</span>
                    <span v-if="MobileSize" style="font-size: 16px; font-weight: 500; color: #333333;">อัพเดตไม่สำเร็จ {{ dataProductCantUpdate.length }} รายการ</span>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;">
                    <v-data-table
                     :headers="headTableImportNoSuccess"
                     :items="dataProductCantUpdate"
                     :items-per-page="10"
                     :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                     :style="!MobileSize ? 'overflow-x: hidden !important;' : 'overflow-x: hidden !important; white-space: nowrap;'"
                    >
                     <!-- <template v-slot:[`item.index`]="{ item }">
                        {{ dataProductNotSuccess.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                     </template> -->
                    </v-data-table>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal แสดงการ import product ที่ไม่สำเร็จ -->
    <v-dialog v-model="DialogResultImportProduct" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ผลลัพธ์ข้อมูลนำเข้า</b></span>
              </v-col>
              <v-btn fab small @click="DialogResultImportProduct = !DialogResultImportProduct" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex">
                  <v-col cols="12" md="12" sm="12" class="pt-3 mr-auto">
                    <span style="font-size: 18px; font-weight: 500; color: #333333;">รายการผลลัพธ์ข้อมูลนำเข้าไม่สำเร็จ {{ dataProductNotSuccess.length }} รายการ</span>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col cols="12" style="border-radius: 8px;" v-if="!MobileSize">
                    <v-data-table
                     :headers="headTableImportNoSuccess"
                     :items="dataProductNotSuccess"
                     :items-per-page="10"
                     :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                     style="overflow-x: hidden !important;"
                    >
                     <template v-slot:[`item.index`]="{ item }">
                        {{ dataProductNotSuccess.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}
                     </template>
                    </v-data-table>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal เลือกตัวเลือกไฟล์สินค้า -->
    <v-dialog v-model="DialogDownloadExcelNoJV" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '480'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 480px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ตัวอย่างไฟล์</b></span>
              </v-col>
              <v-btn fab small @click="DialogDownloadExcelNoJV = !DialogDownloadExcelNoJV" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '480px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="6" sm="12" style="border-radius: 8px;">
                    <v-card style="border-radius: 8px;" width="100%" height="100%" outlined @click="DownloadExcelNoJV()">
                      <v-card-text style="text-align: center;">
                        <v-icon color="#27AB9C" size="40">mdi-file-import</v-icon>
                      </v-card-text>
                      <v-card-text class="addUnderline">
                        ตัวอย่างไฟล์นำเข้าสินค้า (แบบเต็ม)
                      </v-card-text>
                    </v-card>
                    <!-- <v-btn text color="#1B5DD6" @click="DownloadExcelNoJV()" v-if="this.isJV === 'no'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn> -->
                  </v-col>
                  <v-col cols="12" md="6" sm="12" style="border-radius: 8px;">
                    <v-card style="border-radius: 8px;" width="100%" height="100%" outlined @click="DownloadExcelNoJVSimple()">
                      <v-card-text style="text-align: center;">
                        <v-icon color="#27AB9C" size="40">mdi-file-import-outline</v-icon>
                      </v-card-text>
                      <v-card-text class="addUnderline">
                          ตัวอย่างไฟล์นำเข้าสินค้า (แบบย่อ)
                        </v-card-text>
                      <!-- <v-btn text color="#1B5DD6" @click="DownloadExcelNoJVSimple()" v-if="this.isJV === 'no'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn> -->
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <!-- <v-card-actions>
                <v-row dense class="mt-2">
                  <span style="color: red; font-size: 12px; text-decoration-line: underline;">*** หมายเหตุ ***</span><br/>
                  <ul>
                    <li style="font-size: 12px;"><span style="text-decoration-line: underline; font-weight: 700;">ตัวอย่างไฟล์นำเข้าสินค้า (แบบย่อ)</span> จะไม่มีการกรอก เเบรนด์, ตัวแทนจำหน่าย, สถานะข้อความและภาษีมูลค่าเพิ่มของสินค้า โดยระบบจะตั้งค่าให้เป็นค่าเริ่มต้น</li>
                  </ul>
                </v-row>
              </v-card-actions> -->
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal upload ตัวเลือกไฟล์สินค้า -->
    <v-dialog v-model="DialoguploadExcelNoJV" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '480'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 480px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มสินค้าจากไฟล์</b></span>
              </v-col>
              <v-btn fab small @click="DialoguploadExcelNoJV = !DialoguploadExcelNoJV" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '480px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="6" sm="12" style="border-radius: 8px;">
                    <v-card style="border-radius: 8px;" width="100%" height="100%" outlined @click="ImportExcelNoJV">
                      <v-card-text style="text-align: center;">
                        <v-icon color="#27AB9C" size="40">mdi-file-upload</v-icon>
                      </v-card-text>
                      <v-card-text class="addUnderline">
                        เพิ่มสินค้าจากไฟล์ Excel (แบบเต็ม)
                      </v-card-text>
                    </v-card>
                    <input @click="event => event.target.value = null" v-if="this.isJV === 'no'" @change="UploadExcelNoJV($event)" id="importExcelNoJV" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
                    <!-- <v-btn text color="#1B5DD6" @click="DownloadExcelNoJV()" v-if="this.isJV === 'no'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn> -->
                  </v-col>
                  <v-col cols="12" md="6" sm="12" style="border-radius: 8px;">
                    <v-card style="border-radius: 8px;" width="100%" height="100%" outlined @click="importExcelNoJVSimple">
                      <v-card-text style="text-align: center;">
                        <v-icon color="#27AB9C" size="40">mdi-file-upload-outline</v-icon>
                      </v-card-text>
                      <v-card-text class="addUnderline">
                        เพิ่มสินค้าจากไฟล์ Excel (แบบย่อ)
                      </v-card-text>
                      <!-- <v-btn text color="#1B5DD6" @click="DownloadExcelNoJVSimple()" v-if="this.isJV === 'no'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn> -->
                    </v-card>
                    <input @click="event => event.target.value = null" v-if="this.isJV === 'no'" @change="UploadExcelNoJVSimple($event)" id="importExcelNoJVSimple" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal update ตัวเลือกไฟล์สินค้า -->
    <v-dialog v-model="DialogUpdateExcael" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '480'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 480px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัพเดตสินค้าจากไฟล์</b></span>
              </v-col>
              <v-btn fab small @click="DialogUpdateExcael = !DialogUpdateExcael" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '480px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense class="d-flex justify-center">
                  <v-col cols="12" md="6" sm="12" style="border-radius: 8px;">
                    <v-card style="border-radius: 8px;" width="100%" height="100%" outlined @click="updateExcel">
                      <v-card-text style="text-align: center;">
                        <v-icon color="#27AB9C" size="40">mdi-file-upload</v-icon>
                      </v-card-text>
                      <v-card-text class="addUnderline" style="text-align: center;">
                        อัพเดตสินค้าจากไฟล์ excel
                      </v-card-text>
                    </v-card>
                    <input @click="event => event.target.value = null" v-if="this.isJV === 'no'" @change="UpdateExcelProduct($event)" id="updateExcel" style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
                    <!-- <v-btn text color="#1B5DD6" @click="DownloadExcelNoJV()" v-if="this.isJV === 'no'" data-v-step="4"><span style="font-size: 12px; font-weight: 400; line-height: 140%; text-decoration-line: underline;">ตัวอย่างไฟล์นำเข้าสินค้า</span></v-btn> -->
                  </v-col>
                </v-row>
                <v-row dense class="pt-4">
                  <v-col cols="12" md="6" sm="12">
                    <v-btn text color="#1B5DD6" @click="expDialogUpdateExcel = true">
                      <span style="font-size: 12px; font-weight: 400; text-decoration-line: underline;">วิธีอัพเดตสินค้าจากไฟล์ excel</span>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="expDialogUpdateExcel" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '630'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 630px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>วิธีอัพเดตสินค้าจากไฟล์ excel</b></span>
              </v-col>
              <v-btn fab small @click="expDialogUpdateExcel = !expDialogUpdateExcel" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '480px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-timeline dense>
                    <v-timeline-item  color="#E6E6E6" large>
                      <template v-slot:icon>
                        <v-icon color="#27AB9C" size="30">mdi-file-export</v-icon>
                      </template>
                      <v-col cols="12" md="12" sm="12">
                        <span style="font-size: 16px; font-weight: 400;">1) เลือกสินค้าที่จะ export หรือ เลือกทั้งหมดโดยกดที่มุมบนซ้ายมือของตาราง จากนั้นกดปุ่ม <v-btn small dense outlined rounded color="#27AB9C">export รายการสินค้า</v-btn></span>
                      </v-col>
                    </v-timeline-item>
                    <v-timeline-item color="#E6E6E6" large>
                      <template v-slot:icon>
                        <v-icon color="#27AB9C" size="30">mdi-file-excel</v-icon>
                      </template>
                      <v-col cols="12" md="12" sm="12" class="pt-5">
                        <span style="font-size: 16px; font-weight: 400;">2) ทำการแก้ไขส่วนที่ต้องการเปลี่ยนแปลง เช่น ชื่อสินค้า, ราคา,</span><br v-if="!MobileSize">
                        <span style="font-size: 16px; font-weight: 400;">จำนวนสต๊อค</span>
                      </v-col>
                    </v-timeline-item>
                    <v-timeline-item color="#E6E6E6" large>
                      <template v-slot:icon>
                        <v-icon color="#27AB9C" size="30">mdi-file-upload</v-icon>
                      </template>
                      <v-col cols="12" md="12" sm="12" class="pt-5">
                        <span style="font-size: 16px; font-weight: 400;">3) บันทึกไฟล์และกดปุ่ม <v-icon color="#27AB9C" size="20">mdi-file-upload</v-icon> อัพเดตสินค้าจากไฟล์ excel เพื่ออัพโหลดไฟล์</span>
                      </v-col>
                    </v-timeline-item>
                  </v-timeline>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogSyncERPResult" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '900'" content-class="elevation-0">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%; position: relative; height: 60px; top: 0px;' : IpadSize ? 'width: 100%; position: relative; height: 120px;' : 'width: 900px; height: 120px;'" class="backgroundHead">
            <v-row :style="MobileSize ? 'height: 80px;' : 'height: 120px;'">
              <v-col style="text-align: center;" :class="MobileSize ? 'mt-2' : 'pt-4'">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ดำเนินการสำเร็จ</b></span>
              </v-col>
              <v-btn fab small @click="DialogSyncERPResult = !DialogSyncERPResult" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <!-- <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '900px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div> -->
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 10px 0px 10px 30px;' : 'padding: 10px 15px 10px 15px'">
              <v-card-text :class="MobileSize ? 'pa-0' : 'pa-0 pr-8'">
                <v-row :class="MobileSize ? 'mt-5' :'mt-1'">
                  <v-col cols="12" md="6" sm="8">
                    <v-text-field v-model="searchProduct" dense hide-details outlined placeholder="ค้นหาจากชื่อร้านค้า" style="border-radius: 8px;">
                      <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                    </v-text-field>
                  </v-col>
                </v-row>
                <v-row dense no-gutters class="mb-2">
                  <v-col cols="12" class="py-0 mb-0">
                    <a-tabs v-model="activeTab" :show-arrows="IpadSize || IpadProSize">
                      <a-tab-pane :key="0">
                        <span slot="tab">ดำเนินการสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{ ProductSyncSuccessCount }}</a-tag></span>
                      </a-tab-pane>
                      <a-tab-pane :key="1">
                        <span slot="tab">ดำเนินการไม่สำเร็จ <a-tag color="#D1392B" style="border-radius: 8px;">{{ ProductSyncFailCount }}</a-tag></span>
                      </a-tab-pane>
                    </a-tabs>
                  </v-col>
                </v-row>
                <v-row dense>
                  <!-- <v-col cols="12" md="12" sm="12" style="border-radius: 8px;" class="d-flex">
                    <v-col cols="6">
                      <v-card outlined class="pt-2 scroll-card" style="border-color: #27AB9C; border-width: 3px; border-radius: 10px;" height="290">
                      <v-row>
                        <v-col class="d-flex justify-center">
                          <v-img width="120" height="120" src="@/assets/icons/correct.png" contain></v-img>
                        </v-col>
                      </v-row>
                        <v-col class="d-flex justify-center mt-2">
                          <span style="font-size: 24px; font-weight: bold; color: #27AB9C;">ดำเนินการสำเร็จ {{ProductSyncSuccessCount}} รายการ</span>
                        </v-col>
                        <v-col class="d-flex justify-center mt-2">
                          <v-btn outlined color="#27AB9C"><span>ดูรายละเอียด</span></v-btn>
                        </v-col>
                      </v-card>
                    </v-col>
                    <v-col cols="6">
                      <v-card outlined class="pt-2 scroll-card" style="border-color: #27AB9C; border-width: 3px; border-radius: 10px;" height="290">
                      <v-row>
                        <v-col class="d-flex justify-center">
                          <v-img width="120" height="120" src="@/assets/icons/incorrect.png" contain></v-img>
                        </v-col>
                      </v-row>
                        <v-col class="d-flex justify-center mt-2">
                          <span style="font-size: 24px; font-weight: bold; color: #27AB9C;">ดำเนินการไม่สำเร็จ {{ProductSyncFailCount}} รายการ</span>
                        </v-col>
                        <v-col class="d-flex justify-center mt-2">
                          <v-btn outlined color="#27AB9C"><span>ดูรายละเอียด</span></v-btn>
                        </v-col>
                      </v-card>
                    </v-col>
                    <v-divider vertical></v-divider>
                    <v-col cols="6" class="mt-0">
                      <v-card outlined style="border-radius: 20px; border: 0px" height="290">
                      <v-row>
                        <v-col class="d-flex justify-center">
                          <v-img width="120" height="120" src="@/assets/icons/incorrect.png" contain></v-img>
                        </v-col>
                      </v-row>
                        <v-col>
                          <v-card>
                            <v-data-table
                            :headers="headersSuccessSyncERP"
                            :items="ProductSyncFail"
                            :items-per-page="5"
                          ></v-data-table>
                          </v-card>
                        </v-col>
                      </v-card>
                    </v-col>
                  </v-col> -->
                  <v-col cols="12" v-if="activeTab === 0">
                    <v-row>
                      <v-col>
                        <v-card>
                          <v-data-table
                          :headers="headersSuccessSyncERP"
                          :items="ProductSyncSuccess"
                          :items-per-page="5"
                          :search="searchProduct"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        ></v-data-table>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" v-if="activeTab === 1">
                    <v-row>
                      <v-col>
                        <v-card>
                          <v-data-table
                          :headers="headersFailSyncERP"
                          :items="ProductSyncFail"
                          :items-per-page="5"
                          :search="searchProduct"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        >
                          <template v-slot:[`item.message`]="{ item }">
                            <span v-if="item.message === 'Duplicated Product Sku.'">สินค้าชิ้นนี้ถูกซิงค์เรียบร้อยแล้ว</span>
                            <span v-else>{{item.message}}</span>
                          </template>
                        </v-data-table>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
              <DetailUserModal ref="DetailUserModal" />
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-row>
          <v-col cols="6">
            <span>55555</span>
          </v-col>
          <v-divider vertical></v-divider>
          <v-col cols="6">

          </v-col>
        </v-row> -->
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import { Decode } from '@/services'
import axios from 'axios'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    TableProduct: () => import('@/components/Shop/ShopProduct/TableProductUI'),
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    DetailUserModal: () => import('@/components/Business/dialogDetailUser.vue')
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      // myOptions: {
      //   useKeyboardNavigation: false,
      //   labels: {
      //     buttonSkip: 'ข้าม',
      //     buttonPrevious: 'ย้อนกลับ',
      //     buttonNext: 'ถัดไป',
      //     buttonStop: 'เสร็จสิ้น'
      //   },
      //   highlight: true
      // },
      // steps: [
      //   {
      //     target: '[data-v-step="2"]',
      //     content: 'ถ้าคุณยังไม่มีสินค้า คุณสามารถเพิ่มสินค้าของคุณได้ 2 แบบคือ',
      //     params: {
      //       placement: 'botton'
      //     }
      //   }
      // ],
      OrderName: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 1, name: 'พร้อมขาย' },
        { key: 2, name: 'หมด' }
      ],
      SelectDataTable: [],
      DataTable: [],
      Shopname: '',
      seller_shop_id: '',
      seller_shop_logo: '',
      checkShop: 'ไม่มีร้านค้า',
      userDetail: [],
      onedata: [],
      checkOwnerShop: false,
      isJV: 'no',
      DialogResultUpdateProduct: false,
      DialogResultImportProduct: false,
      headTableImportNoSuccess: [
        { text: 'รหัส SKU', value: 'value', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'การตรวจสอบข้อมูล', value: 'message', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      expDialogUpdateExcel: false,
      dataProductNotSuccess: [],
      dataProductCantUpdate: [],
      DialogUpdateExcael: false,
      DialogDownloadExcelNoJV: false,
      DialoguploadExcelNoJV: false,
      DialogSyncERPResult: false,
      ProductSyncSuccess: [],
      ProductSyncSuccessCount: 0,
      ProductSyncFail: [],
      ProductSyncFailCount: 0,
      headersSuccessSyncERP: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersFailSyncERP: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'หมายเหตุ', value: 'message', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      activeTab: 0,
      searchProduct: '',
      openSyncERP: false,
      sellerShopId: ''
    }
  },
  async created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    this.sellerShopId = JSON.parse(localStorage.getItem('shopSellerID'))
    await this.CheckShop()
    await this.GetShopSyncERPStatus()
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$tours['myTour.ShopPage'].start()
    this.$EventBus.$on('CheckShop', this.CheckShop)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('CheckShop')
    })
  },
  watch: {
    MobileSize (val) {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (val === true) {
        this.$router.push({ path: '/sellerShopMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      }
    },
    activeTab (val) {
      this.searchProduct = ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    updateExcel () {
      document.getElementById('updateExcel').click()
    },
    ImportExcel () {
      document.getElementById('importExcel').click()
    },
    ImportExcelNoJV () {
      document.getElementById('importExcelNoJV').click()
    },
    importExcelNoJVSimple () {
      document.getElementById('importExcelNoJVSimple').click()
    },
    async DownloadExcel () {
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/product/export_product_template`,
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        // fileLink.setAttribute('href', fileURL)
        // fileLink.setAttribute('sandbox', 'allow-downloads, allow-scripts')
        fileLink.setAttribute('download', 'import_product_template.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async DownloadExcelNoJVSimple () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        seller_shop_id: this.sellerShopId,
        type: 'short'
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END2}exports/product/template`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob',
        data: data
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        // fileLink.setAttribute('href', fileURL)
        // fileLink.setAttribute('sandbox', 'allow-downloads, allow-scripts')
        fileLink.setAttribute('download', 'template_import_product_' + this.$route.query.ShopName + '_short.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    openDialogForDownloadExcel () {
      this.DialogDownloadExcelNoJV = !this.DialogDownloadExcelNoJV
    },
    openDialoguploadExcelNoJV () {
      this.DialoguploadExcelNoJV = !this.DialoguploadExcelNoJV
    },
    async DownloadExcelNoJV () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        seller_shop_id: this.sellerShopId,
        type: 'full'
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END2}exports/product/template`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob',
        data: data
      }).then((response) => {
        // console.log(response)
        this.DialogDownloadExcelNoJV = false
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        // fileLink.setAttribute('href', fileURL)
        // fileLink.setAttribute('sandbox', 'allow-downloads, allow-scripts')
        fileLink.setAttribute('download', 'template_import_product_' + this.$route.query.ShopName + '_full.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async UploadExcel (e) {
      // console.log('excelFile =======>', this.excelFile)
      this.$store.commit('openLoader')
      var files = e.target.files
      var f = files[0]
      // console.log(f)
      var data = new FormData()
      data.append('seller_shop_id', this.sellerShopId)
      data.append('file_products', f)
      await this.$store.dispatch('actionImportExcel', data)
      var response = await this.$store.state.ModuleShop.stateImportExcel
      // console.log('import !!! =====>', response)
      if (response.code === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'นำเข้าสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.$EventBus.$emit('CheckShop')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${response.message}`, showConfirmButton: false, timer: 3500 })
      }
    },
    async UploadExcelNoJV (e) {
      // console.log('excelFile =======>', e.target.files)
      this.$store.commit('openLoader')
      var files = e.target.files
      var f = files[0]
      // console.log(f)
      var data = new FormData()
      data.append('seller_shop_id', this.sellerShopId)
      data.append('ms_product', f)
      await this.$store.dispatch('actionImportExcelNoJV', data)
      var response = await this.$store.state.ModuleShop.stateImportExcelNoJV
      // console.log('import !!! =====>', response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DialoguploadExcelNoJV = false
        if (response.data.success.length !== 0 && response.data.errors.length === 0) {
          this.$swal.fire({ icon: 'success', text: 'นำเข้าสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        } else if (response.data.success.length !== 0 && response.data.errors.length !== 0) {
          this.$swal.fire({ icon: 'warning', text: 'นำเข้าสินค้าสำเร็จบางชิ้นและสินค้าบางชิ้นนำเข้าไม่สำเร็จ โปรดตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 1500 })
          this.dataProductNotSuccess = []
          if (response.data.errors[0].message === 'ชื่อคอลัมน์ไม่ถูกต้อง') {
            this.dataProductNotSuccess = response.data.errors.slice(1)
          } else {
            this.dataProductNotSuccess = response.data.errors
          }
          this.DialogResultImportProduct = true
        } else {
          this.$swal.fire({ icon: 'error', text: 'นำเข้าสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
          this.dataProductNotSuccess = []
          if (response.data.errors[0].message === 'ชื่อคอลัมน์ไม่ถูกต้อง') {
            this.dataProductNotSuccess = response.data.errors.slice(1)
          } else {
            this.dataProductNotSuccess = response.data.errors
          }
          this.DialogResultImportProduct = true
        }
        await this.CheckShop()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${response.message}`, showConfirmButton: false, timer: 3500 })
      }
    },
    async UpdateExcelProduct (e) {
      // console.log('excelFile =======>', e.target.files)
      this.$store.commit('openLoader')
      var files = e.target.files
      var f = files[0]
      // console.log(f)
      var data = new FormData()
      data.append('seller_shop_id', this.sellerShopId)
      data.append('update_product', f)
      await this.$store.dispatch('actionsupdateExcelProducts', data)
      var response = await this.$store.state.ModuleShop.stateupdateExcelProducts
      // console.log('import !!! =====>', response)
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.DialogUpdateExcael = false
        await this.$swal.fire({ icon: 'success', text: 'อัพเดตสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        await this.CheckShop()
      } else if (response.code === 400) {
        this.$store.commit('closeLoader')
        if (response.data.errors.length !== 0) {
          this.DialogUpdateExcael = false
          await this.$swal.fire({ icon: 'error', text: 'อัพเดตสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
          this.dataProductCantUpdate = []
          this.dataProductCantUpdate = response.data.errors
          this.DialogResultUpdateProduct = true
        } else {
          this.$swal.fire({ icon: 'warning', text: `${response.message}`, showConfirmButton: false, timer: 3500 })
        }
      } else {
        var error = 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
        this.$store.commit('closeLoader')
        this.DialogUpdateExcael = false
        this.$swal.fire({ icon: 'warning', text: `${error}`, showConfirmButton: false, timer: 3500 })
      }
    },
    async UploadExcelNoJVSimple (e) {
      // console.log('excelFile =======>', e.target.files)
      this.$store.commit('openLoader')
      var files = e.target.files
      var f = files[0]
      // console.log(f)
      var data = new FormData()
      data.append('seller_shop_id', this.sellerShopId)
      data.append('ms_product', f)
      await this.$store.dispatch('actionsImportExcelNoJVSimple', data)
      var response = await this.$store.state.ModuleShop.stateImportExcelNoJVSimple
      // console.log('import !!! =====>', response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DialoguploadExcelNoJV = false
        if (response.data.success.length !== 0 && response.data.errors.length === 0) {
          this.$swal.fire({ icon: 'success', text: 'นำเข้าสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        } else if (response.data.success.length !== 0 && response.data.errors.length !== 0) {
          this.$swal.fire({ icon: 'warning', text: 'นำเข้าสินค้าสำเร็จบางชิ้นและสินค้าบางชิ้นนำเข้าไม่สำเร็จ โปรดตรวจสอบข้อมูลอีกครั้ง', showConfirmButton: false, timer: 1500 })
          this.dataProductNotSuccess = []
          if (response.data.errors[0].message === 'ชื่อคอลัมน์ไม่ถูกต้อง') {
            this.dataProductNotSuccess = response.data.errors.slice(1)
          } else {
            this.dataProductNotSuccess = response.data.errors
          }
          this.DialogResultImportProduct = true
        } else {
          this.$swal.fire({ icon: 'error', text: 'นำเข้าสินค้าไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
          this.dataProductNotSuccess = []
          if (response.data.errors[0].message === 'ชื่อคอลัมน์ไม่ถูกต้อง') {
            this.dataProductNotSuccess = response.data.errors.slice(1)
          } else {
            this.dataProductNotSuccess = response.data.errors
          }
          this.DialogResultImportProduct = true
        }
        await this.CheckShop()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: `${response.message}`, showConfirmButton: false, timer: 3500 })
      }
    },
    CreateProduct () {
      if (this.MobileSize === true) {
        this.$router.push({ path: `/manageproductMobile?Status=Create&ShopID=${this.seller_shop_id}` })
      } else {
        this.$router.push({ path: `/manageproduct?Status=Create&ShopID=${this.seller_shop_id}` })
      }
    },
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    async CheckShop () {
      this.$store.commit('openLoader')
      this.$EventBus.$emit('changeNav')
      this.$EventBus.$emit('ChangeActiveMenu', true)
      this.$EventBus.$on('SetDataShop', this.SetDataShopData)
      // await this.$store.dispatch('actionsGetShopData')
      // var response = await this.$store.state.ModuleShop.stateShopData
      // console.log('response shop data====>', response.data)
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionsUserDetailPage', data)
      const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
      // console.log('userdetail data', userdetail)
      if (userdetail.message !== 'This user is unauthorized.') {
        this.userdetail = userdetail.data[0].permissions
        // this.SetDataShop(response.data[0])
        var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
        var shopId = shopDetail.id
        var shopName = shopDetail.name
        var dataDetail = []
        if (localStorage.getItem('list_shop_detail') !== null) {
          dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
        } else {
          dataDetail = []
        }
        if (shopId !== '' && shopName !== '') {
          if (dataDetail.can_use_function_in_shop.manage_account_bank === '1') {
            const data = {
              seller_shop_id: shopId
            }
            await this.$store.dispatch('actionAccountDetailShop', data)
            var response = await this.$store.state.ModuleShop.stateAccountDetailShop
            if (response.message === 'Success') {
              if (response.data.list_account.length !== 0) {
                if (response.data.payment_method.length !== 0) {
                  await this.checkJVShop()
                  await this.SetDataShop(shopId, shopName)
                } else {
                  this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มช่องทางการชำระเงิน', showConfirmButton: false, timer: 1500 })
                  setTimeout(() => {
                    if (this.MobileSize) {
                      this.$router.push('/ManageShopAccountMobile').catch(() => {})
                    } else {
                      this.$router.push('/ManageShopAccount').catch(() => {})
                    }
                  }, 1500)
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มบัญชีร้านค้าและช่องทางการชำระเงิน', showConfirmButton: false, timer: 1500 })
                setTimeout(() => {
                  if (this.MobileSize) {
                    this.$router.push('/ManageShopAccountMobile').catch(() => {})
                  } else {
                    this.$router.push('/ManageShopAccount').catch(() => {})
                  }
                }, 1500)
              }
            }
          } else {
            await this.checkJVShop()
            await this.SetDataShop(shopId, shopName)
          }
        } else {
          window.location.assign('/')
        }
      } else {
        this.$EventBus.$emit('refreshToken')
      }
      this.$store.commit('closeLoader')
    },
    async SetDataShopData () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      var shopId = shopDetail.id
      var shopName = shopDetail.name
      await this.checkJVShop()
      await this.SetDataShop(shopId, shopName)
    },
    async checkJVShop () {
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopDetail')).id,
        role: 'seller'
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        // console.log('response.data', response.data[0])
        if (response.data[0].is_JV === 'yes') {
          this.isJV = 'yes'
        } else {
          this.isJV = 'no'
        }
      }
    },
    async SetDataShop (shopId, shopName) {
      // console.log('Val ===', Object.keys(val).length)
      this.DataTable = []
      if (shopId !== '' && shopName !== '') {
        this.Shopname = shopName
        this.seller_shop_id = shopId
        var dataShop = {
          id: this.seller_shop_id,
          name: this.Shopname
        }
        localStorage.setItem('shopDetail', JSON.stringify(dataShop))
        var data = {
          seller_shop_id: parseInt(this.seller_shop_id)
        }
        const auth = {
          headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
        }
        // await this.$store.dispatch('GetProductBySellerID', data)
        var dataResponse = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/product/list_product`, data, auth)
        // console.log(dataResponse)
        if (dataResponse.data.result === 'SUCCESS') {
          this.DataTable = await dataResponse.data.data
          this.seller_shop_logo = dataResponse.data.data.seller_shop_logo
          // console.log(this.seller_shop_logo)
          this.checkOwnerShop = dataResponse.data.data.is_owner_business
          // console.log('this.data', this.checkOwnerShop)
          this.checkShop = 'มีร้านค้า'
          this.$EventBus.$emit('getCountInTable')
        } else {
          if (dataResponse.data.message === 'This user is not in your shop') {
            window.location.assign('/')
          } else {
            window.location.assign('/')
          }
        }
      } else {
        window.location.assign('/')
      }
    },
    async SyncProductERP () {
      this.$store.commit('openLoader')
      var data = {
        shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsAddProductERP', data)
      var response = await this.$store.state.ModuleShop.stateAddProductERP
      if (response.code === 200) {
        this.ProductSyncSuccessCount = response.data.success
        this.ProductSyncSuccess = response.data.success_list
        this.ProductSyncFailCount = response.data.fail
        this.ProductSyncFail = response.data.fail_list
        this.DialogSyncERPResult = true
      } else if (response.code !== 200) {
        this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 3500 })
      }
      this.$store.commit('closeLoader')
    },
    async GetShopSyncERPStatus () {
      var data = {
        shop_id: this.sellerShopId
      }
      await this.$store.dispatch('actionsListSyncShopErp', data)
      var response = await this.$store.state.ModuleShop.stateListSyncShopErp
      // if (response.data.length === 0) {
      if (response.message === 'Get list shop ERP success.') {
        if (response.data.length !== 0) {
          if (response.data[0].service_sync === 'yes') {
            this.openSyncERP = true
          } else if (response.data[0].service_sync === 'no') {
            this.openSyncERP = false
          }
        } else {
          this.openSyncERP = false
        }
      }
      // }
      // else if (response.data.length > 0) {
      //   console.log('เข้าไหม')
      //   this.openSyncERP = true
      //   for (var i = 0; response.data.length < i; i++) {
      //     if (response.data[i].service_sync === 'no') {
      //       this.openSyncERP = false
      //     }
      //   }
      // }
    }
  }
}
</script>

<style scoped>
.cardMobile {
  background: url('../../../assets/ImageINET-Marketplace/ICONShop/ShopNew.png');
  border-radius: 8px;
  background-color: #F4F9FB;
  background-repeat: no-repeat;
  background-position: right center;
  background-attachment: initial;
  background-size: 50%;
  height: 100%;
}
.classTextCountProduct {
  padding: 4px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 35px;
  background: #DAF1E9;
  width: 100%;
}
.addUnderline {
  text-decoration-line: none;
  color: #333333;
  font-weight: 400;
}
.addUnderline:hover {
  text-decoration-line: underline;
  color: #1B5DD6;
  font-weight: 600;
}
.scroll-card {
  height: 260px; /* Set a fixed height */
  overflow-y: auto; /* Enable vertical scroll */
  overflow-x: hidden;
}
</style>
