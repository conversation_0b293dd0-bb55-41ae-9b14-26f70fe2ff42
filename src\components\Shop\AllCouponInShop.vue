<template>
    <v-container grid-list-xs>
        <v-overlay :value="overlay2">
            <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
        <v-row dense align-content="center" justify="center">
            <v-col cols="12" md="12" xs="12">
                <v-row class="mt-2">
                    <v-spacer style="border-top: 1px solid #bbb; margin-top: 24px;"></v-spacer>
                    <v-chip class="ma-2" color="#BDE7D9" label>
                        <h2 style="color: #27AB9C; font-weight: bold; font-size: 1.25rem;" class="pt-3">
                            คูปองทั้งหมดของร้าน {{header}}</h2>
                    </v-chip>
                    <v-spacer style="border-top: 1px solid #bbb; margin-top: 24px;"></v-spacer>
                </v-row>
            </v-col>
        </v-row>
        <v-row>
            <v-col v-for="(items, index) in paginated" :key="index" cols="12" xs="6" md="4">
                <CardCoupon
                    v-if="index === 0 || index === 1 || index === 2 || index === 6 || index === 7 || index === 8"
                    :items="items" :keep="true" colorCard="blue" />
                <CardCoupon v-else :items="items" :keep="true" colorCard="green" />
            </v-col>
        </v-row>
        <v-row justify="center" class="my-6">
            <v-pagination color="#27AB9C" v-model="pageNumber" :length="pageMax" :total-visible="7"
                @change="pageChange()"></v-pagination>
        </v-row>
        <br />
    </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardCoupon: () => import('@/components/CardCoupon/CardCoupon')
  },
  data () {
    return {
      header: '',
      overlay2: true,
      shopId: '',
      CouponsIteam: [],
      pageMax: null,
      current: 1,
      pageSize: 12
    }
  },
  created () {
    var path = this.$router.currentRoute.params.data
    var cleanPath = path.split('-')
    this.header = cleanPath[0]
    this.shopId = cleanPath[cleanPath.length - 1]
    this.getCoupon()
  },
  mounted () {
    this.$EventBus.$on('AllCouponInShop', this.getCoupon)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('AllCouponInShop')
    })
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.CouponsIteam.slice(this.indexStart, this.indexEnd)
    }
  },
  methods: {
    async getCoupon () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (localStorage.getItem('SetRowCompany') !== null) {
        const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        this.companyId = companyId.company.company_id
      } else {
        this.companyId = ''
      }
      var data = {
        seller_shop_id: this.shopId,
        role_user: dataRole.role,
        company_id: this.companyId,
        list_type: 'shop'
      }
      await this.$store.dispatch('actionsAllCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateAllCouponInShop
      this.response_coupon = response.data.length
      this.CouponsIteam = []
      for (let i = 0; i < response.data.length; i++) {
        this.CouponsIteam.push({
          image: response.data[i].couponImagePath,
          name: response.data[i].couponName,
          description: response.data[i].couponDescription,
          couponDate: {
            useStartDate: response.data[i].collectStartDate,
            useEndDate: response.data[i].collectEndDate
          },
          status: response.data[i].status,
          couponId: response.data[i].couponId,
          shop_name: response.data[i].shop_name
        })
      }
      this.overlay2 = false
      this.pageMax = parseInt(this.CouponsIteam.length / 12) === 0 ? 1 : Math.ceil(this.CouponsIteam.length / 12)
    }
  }
}
</script>
