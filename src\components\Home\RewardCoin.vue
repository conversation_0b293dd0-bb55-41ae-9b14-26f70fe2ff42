<template>
  <v-container :class="IpadProSize ? 'pt-0 py-2' : MobileSize || IpadSize ? 'pa-0' : 'pa-0'" style="display: flex; justify-content: center;">
    <!-- {{ itemsCoin.title }} -->
    <v-card v-if="isLoadReward === false && !MobileSize && !IpadSize" style="max-width: 1400px !important; border-radius: 16px;" width="98%" height="100%" elevation="0" :class="IpadProSize || IpadSize ? 'pt-0' : MobileSize ? 'pa-0' : ''" :style="itemsCoin.bg_type === 'Color' ? `background-color: ${itemsCoin.bg_color};` : itemsCoin.bg_type === 'Image' ? `background-image: url('${itemsCoin.bg_image}');`
    : itemsCoin.bg_type === 'Gradient' ? `background: linear-gradient(90deg, ${itemsCoin.bg_color} -4%, ${itemsCoin.gradient_color} 103.33%); box-shadow: 0px 0px 1px 0px #C3CDD517; box-shadow: 0px 0px 1px 0px #C3CDD50D; box-shadow: 0px 0px 1px 0px #C3CDD503; box-shadow: 1px 0px 1px 0px #C3CDD500;` : 'background-color: #333;'">
      <div class="d-flex align-center">
        <span style="font-size: large; font-weight: 600; line-height: 3;" :style="`color: ${itemsCoin.title.color};`" class="pl-4">{{ lang === 'th' ? itemsCoin.title.text : lang === 'en' && (itemsCoin.title.text_eng === null || itemsCoin.title.text_eng === '' || itemsCoin.title.text_eng === '-') ? itemsCoin.title.text : itemsCoin.title.text_eng }}</span>
      </div>
      <div class="d-flex align-center justify-center mt-n5">
        <span style="font-size: large; font-weight: 600; line-height: 2.5;" :style="`color: ${itemsCoin.number_color};`">{{ items.coins !== null || items.coins !== 0 ? items.coins : '0.00' }}</span>
      </div>
      <div class="d-flex align-center justify-center">
        <v-btn  @click="rewardCoin" rounded elevation="0" style="font-weight: 600; height: 30px; font-size: 14px;" :style="`color: ${itemsCoin.check_in.color};`" :color="itemsCoin.check_in.btn_color">{{ lang === 'th' ? itemsCoin.check_in.text : lang === 'en' && (itemsCoin.check_in.text_eng === null || itemsCoin.check_in.text_eng === '' || itemsCoin.check_in.text_eng === '-') ? itemsCoin.check_in.text : itemsCoin.check_in.text_eng }}</v-btn>
      </div>
        <div class="d-flex align-center justify-center pt-6 mb-6">
        <div class="d-flex align-center justify-center" v-for="(day, index) in days" :key="index">
          <div class="d-flex flex-column align-center" style="gap: 4px;">
            <span style="font-size: small; font-weight: 600;" :style="`color: ${itemsCoin.coin_color};`">{{ day.coin }}</span>
            <v-img v-if="itemsCoin.image_coin !== ''" width="24" height="24" :src="items.total_check_in_weekend > 0 && index < items.total_check_in_weekend ? itemsCoin.image_check_coin : itemsCoin.image_coin"></v-img>
            <div v-if="itemsCoin.image_coin === ''" style="width: 20px; height: 20px; border-radius: 50%; background-color: #333333;"></div>
            <span class="mt-1" style="font-size: small; font-weight: 600;" :style="`color: ${itemsCoin.day_color};`">{{ day.day }}</span>
          </div>
          <v-divider v-if="day.id !== 7" :style="MobileSize || IpadSize ? `width: 2vw; border-width: 2px 0 0 0; border-color: ${itemsCoin.line_color};` : `width: 3vw; border-width: 3px 0 0 0; border-color: ${itemsCoin.line_color};`"></v-divider>
        </div>
      </div>
    </v-card>
    <v-card v-if="isLoadReward === false && (MobileSize || IpadSize)" style="max-width: 1400px !important; border-radius: 16px;" width="98%" height="100%" elevation="0" :class="IpadProSize || IpadSize ? 'pt-0' : MobileSize ? 'pa-0' : ''" :style="itemsCoin.bg_type === 'Color' ? `background-color: ${itemsCoin.bg_color};` : itemsCoin.bg_type === 'Image' ? `background-image: url('${itemsCoin.bg_image}');`
    : itemsCoin.bg_type === 'Gradient' ? `background: linear-gradient(90deg, ${itemsCoin.bg_color} -4%, ${itemsCoin.gradient_color} 103.33%); box-shadow: 0px 0px 1px 0px #C3CDD517; box-shadow: 0px 0px 1px 0px #C3CDD50D; box-shadow: 0px 0px 1px 0px #C3CDD503; box-shadow: 1px 0px 1px 0px #C3CDD500;` : 'background-color: #333;'">
      <div class="d-flex align-center">
        <span style="font-size: large; font-weight: 600; line-height: 3;" :style="`color: ${itemsCoin.title.color};`" class="pl-4">{{ lang === 'th' ? itemsCoin.title.text : lang === 'en' && (itemsCoin.title.text_eng === null || itemsCoin.title.text_eng === '' || itemsCoin.title.text_eng === '-') ? itemsCoin.title.text : itemsCoin.title.text_eng }}</span>
        <v-spacer></v-spacer>
        <v-btn  @click="rewardCoin" rounded elevation="0" class="mr-3" style="font-weight: 600; height: 26px; font-size: 12px;" :style="`color: ${itemsCoin.check_in.color};`" :color="itemsCoin.check_in.btn_color">{{ lang === 'th' ? itemsCoin.check_in.text : lang === 'en' && (itemsCoin.check_in.text_eng === null || itemsCoin.check_in.text_eng === '' || itemsCoin.check_in.text_eng === '-') ? itemsCoin.check_in.text : itemsCoin.check_in.text_eng }}</v-btn>
      </div>
      <div class="d-flex align-center justify-center mt-n4">
        <span style="font-size: large; font-weight: 600; line-height: 2.5;" :style="`color: ${itemsCoin.number_color};`">{{ items.coins !== null || items.coins !== 0 ? items.coins : '0.00' }}</span>
      </div>
      <div class="d-flex align-center justify-center">
      </div>
        <div class="d-flex align-center justify-center pt-1 mb-2">
        <div class="d-flex align-center justify-center" v-for="(day, index) in days" :key="index">
          <div class="d-flex flex-column align-center" style="gap: 4px;">
            <span style="font-size: small; font-weight: 600;" :style="`color: ${itemsCoin.coin_color};`">{{ day.coin }}</span>
            <v-img v-if="itemsCoin.image_coin !== ''" width="20" height="20" :src="items.total_check_in_weekend > 0 && index < items.total_check_in_weekend ? itemsCoin.image_check_coin : itemsCoin.image_coin"></v-img>
            <div v-if="itemsCoin.image_coin === ''" style="width: 20px; height: 20px; border-radius: 50%; background-color: #333333;"></div>
            <span class="mt-1" style="font-size: small; font-weight: 600;" :style="`color: ${itemsCoin.day_color};`">{{ day.day }}</span>
          </div>
          <v-divider v-if="day.id !== 7" :style="MobileSize || IpadSize ? `width: 3vw; border-width: 2px 0 0 0; border-color: ${itemsCoin.line_color};` : `width: 3vw; border-width: 3px 0 0 0; border-color: ${itemsCoin.line_color};`"></v-divider>
        </div>
      </div>
    </v-card>
    <div v-if="isLoadReward === true" style="width: 100%; max-width: 1400px;">
      <v-skeleton-loader type="image" height="250"></v-skeleton-loader>
      <!-- <div v-for="item in 8" :key="item"  style="width: 80px; height: 80px;">
        <v-skeleton-loader type="image" height="80"></v-skeleton-loader>
      </div> -->
    </div>
    <!-- Dialog coin -->
    <v-dialog v-model="dialogCoin" persistent width="400">
      <v-card elevation="0" width="100%">
        <v-card-text>
          <v-row justify="center" dense class="pt-8">
            <v-col cols="12" align="center">
              <v-img :src="imageCoin" contain></v-img>
              <div class="mt-2 mb-4">
                <span style="font-size: large; font-weight: 600;">{{ messageCoin }}</span><br>
              </div>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-row justify="center" class="pb-6">
            <v-btn width="100" height="40" rounded color="#27AB9C" class="white--text" @click="dialogCoin = false">
              ตกลง
            </v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      items: [],
      isLoadIcon: false,
      isLoadReward: false,
      days: [
        { id: 1, coin: '0.10', day: 'วันที่ 1' },
        { id: 2, coin: '0.10', day: 'วันที่ 2' },
        { id: 3, coin: '0.10', day: 'วันที่ 3' },
        { id: 4, coin: '0.20', day: 'วันที่ 4' },
        { id: 5, coin: '0.20', day: 'วันที่ 5' },
        { id: 6, coin: '0.20', day: 'วันที่ 6' },
        { id: 7, coin: '0.50', day: 'วันที่ 7' }
      ],
      dialogCoin: false,
      imageCoin: '',
      messageCoin: '',
      coinData: null,
      itemsCoin: [],
      lang: 'th'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    MacBookSize () {
      const { lg } = this.$vuetify.breakpoint
      return !!lg
    }
  },
  async created () {
    this.isLoadIcon = true
    await this.getDataBannerWeb()
    await this.getDataCoin()
    this.lang = localStorage.getItem('lang')
    // this.calculateNumIcon()
  },
  methods: {
    async getDataBannerWeb () {
      const data = null
      await this.$store.dispatch('actionsgetDataIcon', data)
      const response = this.$store.state.ModuleAdminManage.stategetDataIcon
      if (response.code === 200) {
        // console.log('response', response)
        if (response.data.Coin.length !== 0) {
          this.itemsCoin = response.data.Coin
          this.days = this.itemsCoin.days
        }
        this.isLoadIcon = false
      }
    },
    async getDataCoin () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetDataCoin')
      const response = this.$store.state.ModuleHompage.stateGetDataCoin
      // console.log('response', response)
      if (response.code === 200 || response.code === 201) {
        this.isLoadIcon = false
        this.$store.commit('closeLoader')
        // console.log('response.data', response.data)
        this.items = response.data
        if (this.items.total_check_in_weekend !== null && this.items.total_check_in_weekend !== 0) {
          this.days = this.days.map(day => {
            if (this.items.total_check_in_weekend === day.id) {
              return { ...day, day: 'วันนี้' }
            }
            return day
          })
        }
        if (this.items.total_check_in_weekend !== null && this.items.total_check_in_weekend !== 0 && this.items.total_check_in_weekend !== 7) {
          this.coinData = this.days.find(day => (this.items.total_check_in_weekend + 1) === day.id)
          // console.log('this.coinData 1', this.coinData)
        } else {
          this.coinData = this.days.find(day => day.id === 1)
          // console.log('this.coinData 2', this.coinData)
        }
        // if (response.data.custom_icon.length !== 0) {
        //   this.items = response.data
        // } else {
        //   this.items = []
        // }
        // console.log('this.items', this.items)
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
      this.$EventBus.$emit('changeReadyCarousel')
      // this.isLoadIcon = false
    },
    async rewardCoin () {
      this.$store.commit('openLoader')
      // console.log('this.coinData.coin', this.coinData.coin)
      var data = {
        coins: this.coinData.coin
      }
      await this.$store.dispatch('actionsCheckInCoin', data)
      const response = this.$store.state.ModuleHompage.stateCheckInCoin
      // console.log('response', response)
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        // console.log('response.data', response.data)
        this.messageCoin = 'ยินดีด้วยคุณได้รับ Coin ' + this.coinData.coin + ' coins'
        await this.getDataCoin()
        this.imageCoin = require('@/assets/ImageINET-Marketplace/menu/coin_today.png')
        this.dialogCoin = true
      } else if (response.code === 400) {
        this.$store.commit('closeLoader')
        await this.getDataCoin()
        this.imageCoin = require('@/assets/ImageINET-Marketplace/menu/coin.png')
        this.messageCoin = 'วันนี้คุณรับ coin ไปแล้ว'
        this.dialogCoin = true
      } else if (response.code === 401) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: 'กรุณาเข้าสู่ระบบ', showConfirmButton: false, timer: 2000 })
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>

<style scoped>
::v-deep .v-dialog {
  overflow-y: hidden;
}
.backgroundAppBar {
  background: linear-gradient(90deg, #2D95FF -4%, #54ECB5 103.33%);
  /* border-bottom: 1px solid #F5F5F5; */
  box-shadow: 0px 0px 1px 0px #C3CDD517;
  box-shadow: 0px 0px 1px 0px #C3CDD50D;
  box-shadow: 0px 0px 1px 0px #C3CDD503;
  box-shadow: 1px 0px 1px 0px #C3CDD500;
}
.backgroundCoin {
  background: linear-gradient(180deg, #ffffff -4%, #2D95FF 103.33%);
  /* border-bottom: 1px solid #F5F5F5; */
  box-shadow: 0px 0px 1px 0px #C3CDD517;
  box-shadow: 0px 0px 1px 0px #C3CDD50D;
  box-shadow: 0px 0px 1px 0px #C3CDD503;
  box-shadow: 1px 0px 1px 0px #C3CDD500;
}
</style>
