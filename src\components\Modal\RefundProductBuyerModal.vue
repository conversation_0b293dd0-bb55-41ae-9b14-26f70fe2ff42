<template>
  <div class="text-center">
    <v-dialog v-model="openModalRefundProductBuyer" width="800" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>คืนสินค้า</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <!-- กรอกชื่อร้านค้า -->
          <v-form ref="FormRefundProduct" :lazy-validation="lazy">
            <v-card-text>
              <v-row>
                <v-col>
                  <v-avatar class="float-left" tile :width="IpadProSize ? '70px' : IpadSize ? '70px' :'70px'" :height="IpadProSize ? '70px' : IpadSize ? '70px' : '70px'">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/store-return-product.png" contain></v-img>
                  </v-avatar>
                  <v-row class="float-left ml-2">
                    <v-col cols="12" class="pb-0 pl-1">
                      <span style="font-size: 16px; font-weight: 400;">ชื่อร้านค้า</span>
                    </v-col>
                    <v-col cols="12" class="pt-1 pl-1">
                      <v-text-field readonly v-model="productData.shop_name" outlined dense placeholder="ระบุชื่อร้านค้า"></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <!-- ข้อมมูลสินค้า -->
              <v-row>
                <v-col cols="12">
                  <span>รายละเอียดสินค้า</span>
                </v-col>
                <v-row class="ml-3 my-2" v-for="(item, index) in productData.product_list" :key="index">
                  <v-col :cols="MobileSize ? 5 : IpadSize ? 3 : 6" md="2" class="pr-0">
                    <v-row>
                      <v-col cols="9" class="py-0"><span class="title-product">ชื่อสินค้า</span></v-col>
                      <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                      <v-col cols="9" class="py-0"><span class="title-product">รหัสสินค้า</span></v-col>
                      <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                      <v-col cols="12" v-if="item.have_attribute === 'yes'">
                        <div v-if="item.have_attribute === 'yes'">
                          <div v-if="item.key_2_value !== null && item.key_2_value !== ''">
                            <v-row>
                              <v-col cols="9" class="py-0"><span class="title-product">{{item.key_1_value}}</span></v-col>
                              <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                              <v-col cols="9" class="py-0"><span class="title-product">{{item.key_2_value}}</span></v-col>
                              <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                            </v-row>
                          </div>
                          <div v-else>
                            <v-row>
                              <v-col cols="9" class="py-0"><span class="title-product">{{item.key_1_value}}</span></v-col>
                              <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                            </v-row>
                          </div>
                        </div>
                      </v-col>
                      <v-col cols="9" class="py-0"><span class="title-product">จำนวนสินค้า</span></v-col>
                      <v-col cols="3" class="py-0"><span align="start">:</span></v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="MobileSize ? 7 : IpadSize ? 9 : 6" md="10" class="pl-0">
                    <v-row>
                      <v-col cols="12" class="py-0"><span class="detail-product" align="start"><b>{{item.product_name}}</b></span></v-col>
                      <v-col cols="12" class="py-0"><span class="detail-product" align="start"><b>{{item.sku}}</b></span></v-col>
                      <v-col cols="12"  v-if="item.have_attribute === 'yes'">
                        <div v-if="item.have_attribute === 'yes'">
                          <div v-if="item.key_2_value !== null || item.key_2_value !== ''">
                            <v-row>
                              <v-col cols="12" class="py-0"><span class="detail-product">{{item.product_attribute_detail.attribute_priority_1}}</span></v-col>
                              <v-col cols="12" class="py-0"><span class="detail-product">{{item.product_attribute_detail.attribute_priority_2}}</span></v-col>
                            </v-row>
                          </div>
                          <div v-else>
                            <v-row>
                              <v-col cols="12" class="py-0"><span class="detail-product">{{item.product_attribute_detail.attribute_priority_1}}</span></v-col>
                            </v-row>
                          </div>
                        </div>
                      </v-col>
                      <v-col cols="12" class="py-0"><span class="detail-product" align="start"><b>{{item.quantity}}</b></span></v-col>
                    </v-row>
                  </v-col>
                  <v-divider class="mt-3" v-if="productData.product_list.length !== 1 && index !== (productData.product_list.length - 1)" style="border: 1px solid #F2F2F2;"></v-divider>
                </v-row>
              </v-row>
              <!-- สาเหตุในการคืนสินค้า -->
              <v-row class="mt-9">
                <v-col cols="12" class="pb-0">
                  <span>สาเหตุในการคืนสินค้า <span style="color: red;">*</span></span><br>
                  <v-col cols="6" class="pl-0">
                    <v-select v-model="detailProduct.returnProductSelectReasonReason" :items="returnProductItems" item-text="text" item-value="value" placeholder="ระบุสาเหตุ" color="#27AB9C" style="font-size: 14px; min-height: 50px;" outlined dense></v-select>
                  </v-col>
                </v-col>
              </v-row>
              <!-- เพิ่มรูปภาพ -->
              <v-row>
                <v-col cols="12" md="12">
                  <span>รูปภาพสินค้า</span><span style="color: red;"> *</span><br>
                  <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                    <v-card-text>
                      <v-card elevation="0"  @click="onPickFile()" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;">
                        <v-card-text>
                          <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                            <v-file-input
                              v-model="DataImage"
                              :items="DataImage"
                              accept="image/jpeg, image/jpg, image/png, video/quicktime, video/mp4"
                              @change="UploadImage()"
                              id="file_input"
                              multiple
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-col cols="12" md="12" class="mb-6">
                              <v-row justify="center" class="pt-10">
                                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="280.34" height="154.87" contain></v-img>
                              </v-row>
                            </v-col>
                            <v-col cols="12" md="12" class="mt-6">
                              <v-row justify="center" align="center">
                                <v-col cols="12" style="text-align: center;">
                                  <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มรูปภาพหรือวิดีโอของคุณที่นี่</span><br />
                                  <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกรูปภาพหรือวิดีโอจากคอมพิวเตอร์ของคุณ</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .JPEG,PNG,MP4,MOV เพิ่มได้สูงสุด 6 รูปภาพ/วิดีโอ)</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุไฟล์รูปควรมีขนาดไม่เกิน 2 MB และขนาดไฟล์วิดีโอควรมีขนาดไม่เกิน 20 MB</span>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                      <div v-if="detailProduct.product_image.length !== 0" class="mt-4">
                        <draggable v-model="detailProduct.product_image" :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                          <v-col v-for="(item, index) in detailProduct.product_image" :key="index" cols="6" md="3" sm="4" :class="MobileSize ? 'px-1' : ''">
                            <v-card v-if="item.type === 'video/mp4' || item.type === 'video/quicktime'" outlined class="pa-1" width="146" height="100%">
                              <v-card-text>
                                <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                  <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                </v-btn>
                                <video autoplay loop muted playsinline id="upload-video" width="100%" height="100%" class="py-0" controls>
                                  <source :src="item.length ? item.url + '?=' + `${currentTime.getTime()}`: item.url"  type="video/mp4">
                                </video>
                              </v-card-text>
                            </v-card>
                            <v-card v-else outlined class="pa-1" width="146" height="146">
                              <v-img :src="item.url" :lazy-src="item.url" height="130" contain>
                                <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                  <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                </v-btn>
                              </v-img>
                            </v-card>
                          </v-col>
                        </draggable>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
              <!-- รายละเอียดการคืนสินค้า -->
              <v-row class="mt-3">
                <v-col cols="12" class="pb-0">
                  <span>รายละเอียด</span><br>
                  <v-col class="px-0">
                    <v-textarea v-model="detailProduct.returnProductDescription" solo name="input-7-4" placeholder="ระบุรายละเอียด" :maxlength="1024" :counter="1024"></v-textarea>
                  </v-col>
                </v-col>
              </v-row>
              <!-- อีเมล -->
              <v-row class="mt-3">
                <v-col cols="12" class="pb-0">
                  <span>อีเมล</span><span style="color: red;"> *</span><br>
                  <v-col class="px-0">
                    <v-text-field v-model="detailProduct.email" outlined dense placeholder="ระบุอีเมล" :rules="Rules.email"></v-text-field>
                  </v-col>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="requestRefundProduct()">ส่งคำขอ</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
// import { Decode } from '@/services'
export default {
  components: { draggable },
  data () {
    return {
      data: [],
      lazy: false,
      disabledForm: true,
      openModalRefundProductBuyer: false,
      shop_name: '',
      role: '',
      productData: {},
      detailProduct: {
        order_number: '',
        product_image: [],
        returnProductDescription: '',
        email: '',
        returnProductSelectReasonReason: ''
      },
      DataImage: [],
      returnProductItems: [
        { text: 'ฉันไม่ได้รับสินค้าสำหรับคำสั่งซื้อนี้', value: 'ฉันไม่ได้รับสินค้าสำหรับคำสั่งซื้อนี้' },
        { text: 'ได้รับสินค้าที่ไม่สมบูรณ์ (ชิ้นส่วนบางชิ้นหายไป)', value: 'ได้รับสินค้าที่ไม่สมบูรณ์ (ชิ้นส่วนบางชิ้นหายไป)' },
        { text: 'ได้รับสินค้าที่ไม่ถูกต้องตามที่ได้สั่ง เช่น ไซส์ผิด สีผิด สินค้าผิด', value: 'ได้รับสินค้าที่ไม่ถูกต้องตามที่ได้สั่ง เช่น ไซส์ผิด สีผิด สินค้าผิด' },
        { text: 'ได้รับสินค้าสภาพไม่ดี', value: 'ได้รับสินค้าสภาพไม่ดี' },
        { text: 'ได้รับสินค้าที่การทำงานไม่สมบูรณ์', value: 'ได้รับสินค้าที่การทำงานไม่สมบูรณ์' }
      ],
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        email: [
          // v => !!v || 'กรุณาระบุอีเมล',
          v => /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+([.]?[a-zA-Z])*(\.[a-zA-Z]{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ]
        // first_name: [
        //   v => !!v || 'กรุณากรอกชื่อจริงผู้รับ'
        // ]
      }
    }
  },
  watch: {
  },
  mounted () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    open (items, orderNumber, roleUser) {
      this.productData = items
      this.detailProduct.order_number = orderNumber
      this.role = roleUser
      this.clearData()
      this.openModalRefundProductBuyer = true
    },
    cancel () {
      this.openModalRefundProductBuyer = false
      // this.$router.push({ path: '/pobuyerdetail' })
    },
    clearData () {
      this.detailProduct.product_image = []
      this.detailProduct.email = ''
      this.detailProduct.returnProductDescription = ''
      this.detailProduct.returnProductSelectReasonReason = ''
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    RemoveImage (index, val) {
      this.detailProduct.product_image.splice(index, 1)
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    UploadImage () {
      if (this.DataImage !== undefined) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          const imageSize = element.size / 1024 / 1024
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'video/mp4' || element.type === 'video/quicktime') {
            if (imageSize > 2 && (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png')) {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพที่มีขนาดไม่เกิน 2 MB', showConfirmButton: false, timer: 2500 })
            } else if (imageSize > 20 && (element.type === 'video/mp4' || element.type === 'video/quicktime')) {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มวิดีโอที่มีขนาดไม่เกิน 20 MB', showConfirmButton: false, timer: 2500 })
            } else {
              // const imageSize = element.size / 1024 / 1024
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.detailProduct.product_image.length < 6) {
                  if (this.$route.query.Status !== 'Edit') {
                    this.detailProduct.product_image.push({
                      image_data: resultReader,
                      url: url,
                      type: element.type,
                      name: this.DataImage[i].name
                    })
                  } else {
                    this.detailProduct.product_image.push({
                      image_data: resultReader,
                      media_path: url,
                      type: element.type,
                      name: this.DataImage[i].name
                    })
                  }
                } else {
                  this.$swal.fire({ icon: 'warning', text: 'สามารถเพิ่มรูปภาพและวิดีโอได้สูงสุด 6 รูป/วิดีโอ', showConfirmButton: false, timer: 2500 })
                }
              }
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์ jpeg, jpg, png, mp4, mov เท่านั้น', showConfirmButton: false, timer: 2500 })
          }
        }
      }
    },
    async requestRefundProduct () {
      if (this.$refs.FormRefundProduct.validate(true) && this.detailProduct.returnProductSelectReasonReason !== '' && this.detailProduct.email !== '' && this.detailProduct.product_image.length !== 0) {
        var imageList = []
        if (this.detailProduct.product_image.length !== 0) {
          this.detailProduct.product_image.forEach(element => {
            if (element.type === 'video/mp4' || element.type === 'video/quicktime') {
              imageList.push({ vdo: element.image_data })
            } else {
              imageList.push({ image: element.image_data })
            }
          })
        }
        const data = {
          reference_id: this.detailProduct.order_number,
          reason: this.detailProduct.returnProductSelectReasonReason,
          description: this.detailProduct.returnProductDescription,
          email: this.detailProduct.email,
          image: imageList
        }
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsSendRefundBuyer', data)
        var res = await this.$store.state.ModuleOrder.stateSendRefundBuyer
        if (res.message === 'Send data refund success') {
          this.$store.commit('closeLoader')
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'success', text: 'ส่งคำขอการคืนสินค้าเรียบร้อยแล้ว', timer: 2500 })
          this.openModalRefundProductBuyer = false
          this.$EventBus.$emit('getItemNoti')
          if (this.role === 'ext_buyer') {
            if (this.MobileSize) {
              this.$router.push({ path: '/refundProductMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/refundProduct' }).catch(() => {})
            }
          } else {
            if (this.MobileSize) {
              this.$router.push({ path: '/refundCompanyMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/refundCompany' }).catch(() => {})
            }
          }
        } else if (res.message === 'In process of delivery') {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', title: 'ไม่สามารถทำรายการนี้ได้', text: 'เนื่องจากสินค้าของคุณยังในอยู่ระหว่างการจัดส่ง' })
          this.$store.commit('closeLoader')
        } else if (res.message === 'Image has limit 6 img') {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป', timer: 2500 })
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'error', text: 'ดำเนินการไม่สำเร็จ' })
          this.$store.commit('closeLoader')
        }
      } else {
        if (this.detailProduct.returnProductSelectReasonReason === '') {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'กรุณาเลือกสาเหตุในการคืนสินค้า', timer: 2500 })
        } else if (this.detailProduct.product_image.length === 0) {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'คุณยังไม่ได้เพิ่มรูปภาพ กรุณาเพิ่มรูปภาพ', timer: 2500 })
        } else if (this.detailProduct.email === '') {
          this.$swal.fire({ showConfirmButton: false, timerProgressBar: true, icon: 'warning', text: 'กรุณากรอกอีเมล', timer: 2500 })
        }
      }
    },
    resetValidation () {
      this.$refs.FormRefundProduct.resetValidation()
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
#upload-video {
  margin-top: 20px;
  border-radius: 0px;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
.title-product {
  font-size: 14px;
}
.detail-product {
  font-size: 14px;
}
</style>
