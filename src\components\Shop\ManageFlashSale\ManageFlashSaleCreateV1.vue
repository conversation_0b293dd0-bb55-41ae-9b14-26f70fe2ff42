<template>
  <v-container grid-list-xs rounded :class="MobileSize ? 'background_color_Mobile' : 'background_color'">
    <v-row justify="start" class="pt-3 pb-3 px-3" v-if="MobileSize">
      <span class="pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
        <v-icon style="color: #27ab9c;" @click="backToHome()">mdi-chevron-left</v-icon> เพิ่มแฟลชเซลล์
      </span>
    </v-row>
    <v-row justify="space-between" class="pt-3 px-3" v-else>
      <span class="pl-4 pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
        <v-icon style="color: #27ab9c;" @click="backToHome()">mdi-chevron-left</v-icon> เพิ่มแฟลชเซลล์
      </span>
    </v-row>
    <v-form ref="FormProductFlash">
    <div>
      <v-row class="mb-4 mt-2">
        <v-col cols="12" md="12">
          <span class="subTitle pl-3">เพิ่มรูปภาพแบนเนอร์สำหรับแฟลชเซลล์ </span>
            <v-card
              v-if="FlashsTest.length === 0"
              class="mt-3"
              elevation="0"
              :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px;'"
              height="400px%"
              @click="onPickFile()"
            >
              <v-card-text >
                <v-row dense align="center" justify="center" style="cursor: pointer;">
                  <v-file-input
                    v-model="DataImage"
                    :items="DataImage"
                    accept="image/jpeg, image/jpg, image/png"
                    @change="UploadImage()"
                    id="file_input"
                    multiple :clearable="false"
                    style="display:none"
                  >
                  </v-file-input>
                  <v-col cols="12" md="12">
                    <v-row justify="center" align="center">
                      <v-col cols="12" md="12" align="center">
                        <v-img
                          src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                          width="280.34"
                          height="154.87"
                          contain
                        ></v-img>
                      </v-col>
                      <v-col cols="12" md="12" style="text-align: center;">
                        <span style="line-height: 24px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                        <span style="line-height: 24px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                        <span style="line-height: 16px; font-weight: 400;"
                          :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            <v-card
              v-if="FlashsTest.length !== 0"
              class="mt-3"
              elevation="0"
              style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
              height="400px%"
            >
              <v-card-text>
                <div class="mt-4">
                  <!-- <draggable
                    v-model="FlashsTest"
                    :move="onMove"
                    @start="drag = true"
                    @end="drag = false"
                    class="pl-5 pr-5 row  fill-height align-center sortable-list"
                  > -->
                    <v-col v-for="(item, index) in FlashsTest" :key="index" cols="12" class="pa-6" >
                      <div class="d-flex justify-center">
                      <v-card outlined max-width="950px" max-height="400px">
                          <v-img :src="item.url" :lazy-src="item.url" :max-width="MobileSize ? '340px' : IpadSize ? '430px' : IpadProSize ? '660px' : '950px'" max-height="400px" contain>
                            <v-btn icon x-small style="float: right; background-color: #ff5252; margin-top: 12px; margin-right: 12px">
                              <v-icon x-small color="white" dark
                                  @click="RemoveImage(index, item)">mdi-close
                              </v-icon>
                            </v-btn>
                          </v-img>
                        </v-card>
                      </div>
                    </v-col>
                  <!-- </draggable> -->
                </div>
              </v-card-text>
            </v-card>
        </v-col>
      </v-row>
    </div>
    <div>
        <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3 mt-6" >
          <v-col cols="12">
            <span>สินค้าที่เข้าร่วม <span style="color:#F5222D">*</span></span>
            <div :style="theRedP ? '' : 'border: 2px solid red; box-sizing: border-box; border-radius: 8px;'">
              <treeselect
                v-model="itemFlashSale"
                :multiple="true"
                :options="dataItem"
                placeholder="เลือกสินค้าที่เข้าร่วม..."
                :normalizer="normalizer"
                valueFormat="object"
                @input="updateFlash()"
                :disable-branch-nodes="true"
                openDirection="top"
                />
            </div>
          </v-col>
        </v-row>
        <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3 mt-6" v-if="itemFlashSalePhase2.length > 0">
          <v-col>
            <v-row class="pb-6 pl-3"><span>รายการสินค้าที่เข้าร่วมทั้งหมด {{ itemFlashSalePhase2.length }} รายการ </span></v-row>
              <template>
                <!-- <v-form ref="FormProductFlash2"> -->
                  <v-data-table
                    dense
                    :headers="headers"
                    :items="itemFlashSalePhase2"
                    item-key="name"
                    class="elevation-1"
                    :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                    @update:page="pageChange(itemFlashSalePhase2)"
                    style="white-space:nowrap;"
                  >
                  <template v-slot:[`item.number`]="{ item }">
                    <span>{{ itemFlashSalePhase2.indexOf(item) + 1 }}</span>
                  </template>
                  <template v-slot:[`item.images_URL`]="{ item }">
                    <v-card class="pa-1" width="100px" height="100px" elevation="0" contain>
                      <v-img width="100px" height="90px" v-if="item.images_URL !== '' && item.images_URL !== null && item.images_URL !== undefined" :src="item.have_attribute === 'yes' ? item.color_image_path :item.images_URL" contain></v-img>
                      <v-img width="100px" v-else src="@/assets/NoImage.png" contain></v-img>
                    </v-card>
                  </template>
                  <template v-slot:[`item.detail`]="{ item }">
                    <span> {{ item.product_id }} </span> <br>
                    <span> {{ item.product_name|truncate(26) }} {{item.attribute_priority_1 === null ? '' : '(' + item.attribute_priority_1 + ')'}}</span> <br>
                    <span> {{ item.attribute }} </span>
                  </template>
                  <template v-slot:[`item.fake_price`]="{ item }">
                    <span> {{ Number(item.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span> <br>
                  </template>
                  <template v-slot:[`item.real_price`]="{ item }">
                    <span> {{ Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span> <br>
                  </template>
                  <template v-slot:[`item.flashprice`]="{ item }">
                    <v-text-field
                      v-model="item.flashprice"
                        placeholder="ระบุราคาแฟลชเซลล์"
                        outlined
                        dense
                        oninput="this.value = this.value.replace(/^[.]/, '').replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                        @change="realPriceNoGood(item, itemFlashSalePhase2.indexOf(item))"
                        :rules="rule.amountVoucherWhenEdit(item.flashprice, item.real_price)"
                    ></v-text-field>
                  </template>
                  <template v-slot:[`item.actions`]="{ item }">
                    <v-btn
                      x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="DeleteFromTable(item)">
                      <v-icon color="#A1A1A1" small>mdi-delete-outline</v-icon>
                    </v-btn>
                  </template>
                  </v-data-table>
                <!-- </v-form> -->
              </template>
          </v-col>
        </v-row>
        <v-row justify="end" class="px-5 my-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeCreateFlash()" class="mr-2">ยกเลิก</v-btn>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="itemFlashSale.length === 0" @click="dialogSuccess = true">บันทึก</v-btn>
        </v-row>
        <!-- <v-dialog v-model="dialogSuccess" :width="MobileSize ? '100%' : '464px'" persistent>
          <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '464px'">
            <v-toolbar align="center" color="#DBECFA" dark dense elevation="0">
              <span class="flex text-center" :style="MobileSize ? 'font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
                เพิ่มแฟลชเซลล์
              </span>
              <v-btn icon dark @click="closeDialogSuccess()">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text>
              <v-row justify="center" dense class="py-8">
                <v-icon size="70" color="#27AB9C">mdi-check-circle</v-icon>
              </v-row>
              <v-row justify="center" dense>
                <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;">เสร็จสิ้น</p>
              </v-row>
            </v-card-text>
          </v-card>
        </v-dialog> -->
        <v-dialog content-class="elevation-0" v-model='dialogSuccess' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openDialog = false" scrollable>
        <v-card min-height='100%' style="border-radius: 1.5vw; overflow: hidden;">
          <v-card-title class="d-flex justify-end">
            <v-btn plain fab small @click='dialogSuccess = false' icon><v-icon color='#BABABA'>mdi-close</v-icon></v-btn>
          </v-card-title>
          <v-row style="margin-top: -2.5vw">
            <v-col cols="12">
              <v-img height="100%">
                <img class="d-flex ma-auto" style="border-radius: 1.5vw; width: 15vw; position: relative;" src="@/assets/ImageINET-Marketplace/ICONShop/warning.jpg" alt="Product Image">
              </v-img>
            </v-col>
          </v-row>
          <v-container style="margin-top: -1.5vw">
            <v-card-text style="text-align: center; font-size: large;">
              <span style="font-size: 16px; text-align: center">
                ยืนยันยันการสร้างแฟลชเซลล์นี้หรือไม่ ?
              </span>
            </v-card-text>
            <v-card-actions>
              <v-row dense class='d-flex justify-center' style=" gap: 1vw">
                <v-btn  class="white--text ml-2" rounded color="#27AB9C"  @click="createFlash()">ตกลง</v-btn>
                <v-btn outlined rounded color="#27AB9C" @click='dialogSuccess = false'>ยกเลิก</v-btn>
                <!-- <v-btn v-if="!MobileSize" class="white--text ml-2" text color="#27AB9C"  @click="openDialog = false">ยกเลิก</v-btn> -->
                <!-- <v-btn v-else style="font-size: small;" class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openDialog = false">ตกลง</v-btn> -->
              </v-row>
            </v-card-actions>
          </v-container>
          </v-card>
        </v-dialog>
      </div>
    </v-form>
      <!-- {{ date + ' ' + startTimeCollect }} -->
      <!-- {{ date1 + ' ' + startTimeCollect1 }} -->
      <!-- {{ date2 + ' ' + startTimeCollect2 }}  -->
      <!-- <pre>
        {{ this.itemFlashSale }}
      </pre> -->
      <!-- {{ selectHs + ' ' + selectMs }} -->
      <!-- {{ dateAlter1 }} -->
      <!-- {{ timeOriginH + ' ' + timeOriginM }} -->
      <!-- {{ date + ' ' + dateOrigin }}   -->
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64" color="#27AB9C"></v-progress-circular>
    </v-overlay>
  </v-container>
</template>

<script>
import { msgErr, statusErr } from '@/enum/GetError'
import Treeselect from '@riophae/vue-treeselect'
import { Decode } from '@/services'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// import { Encode } from '@/services'
// import { msgErr, statusErr } from '@/enum/GetError'
export default {
  components: {
    Treeselect
  },
  filters: {
    truncate: function (value, limit) {
      if (!value) return ''
      if (value.length > limit) {
        value = value.substring(0, (limit - 3)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      productList: [],
      timeOriginH: '',
      DataImage: [],
      overlay: false,
      dialogStartDate: false,
      dialogEndDate: false,
      dialogCountdownDate: false,
      searchStartDate: '',
      searchEndDate: '',
      countdownDate: '',
      startTimeCollect: '',
      startTimeCollect1: '',
      startTimeCollect2: '',
      selectHs: '',
      selectMs: '',
      selectHs1: '',
      selectMs1: '',
      selectHs2: '',
      selectMs2: '',
      Hs: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'],
      Ms: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59'],
      dataItem: [],
      itemFlashSale: [],
      itemFlashSalePhase2: [],
      realPriceForSure: '',
      normalizer (node) {
        let id
        let childrenKey
        let labelKey

        if (node.type === 'category') {
          id = 'hierachy'
          childrenKey = node.sub_category === null ? 'product_list' : 'sub_category'
          labelKey = 'category_name'
        } else if (node.type === 'product') {
          id = 'product_id'
          childrenKey = node.sub_product.length === 0 ? '' : 'sub_product'
          labelKey = 'product_name'
        } else if (node.type === 'product_attribute') {
          id = 'product_attribute_id'
          labelKey = 'product_attribute_name'
        } else if (node.type === 'special_1_category') {
          id = 'all_id'
          childrenKey = 'all'
          labelKey = 'all_name'
        } else {
          id = 'product_id'
          childrenKey = 'product_list'
          labelKey = 'product_attribute_name'
        }
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey]
        }
      },
      rule01: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล']
      },
      rule: {
        amountVoucherWhenEdit (falshSalePrice, realPrice) {
          if (falshSalePrice === '' || isNaN(parseFloat(falshSalePrice))) {
            return ['กรุณากรอกราคาแฟลชเซลล์']
          } else if (parseInt(falshSalePrice) <= parseInt(0)) {
            return ['กรุณากรอกราคาให้มากกว่า 0']
          } else {
            const falshSalePriceCheck = falshSalePrice.replace(/,/g, '')
            if (parseFloat(falshSalePriceCheck) >= parseFloat(realPrice)) {
              return ['กรุณากรอกราคาให้ต่ำกว่าราคาขาย']
            }
          }
        }
      },
      dateOrigin: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      timeOrigin: '',
      date: '',
      date1: '',
      date2: '',
      dateAlter: '',
      dateAlter1: '',
      create_date: '',
      create_date1: '',
      create_date2: '',
      dialogSuccess: false,
      FlashsTest: [],
      headers: [
        { text: 'ลำดับที่', value: 'number', sortable: false },
        { text: 'รูปภาพสินค้า', value: 'images_URL' },
        { text: 'รายละเอียดสินค้า', value: 'detail' },
        { text: 'ราคาตั้งต้น', value: 'fake_price' },
        { text: 'ราคาขาย', value: 'real_price' },
        { text: 'ราคาแฟลชเซลล์', value: 'flashprice' },
        { text: 'จัดการ', value: 'actions' }
      ],
      theRedI: true,
      theRedP: true,
      oldData: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    // WatchTheTime () {
    //   return this.selectHs + ':' + this.selectMs
    // },
    // WatchTheTime1 () {
    //   return this.selectHs1 + ':' + this.selectMs1
    // },
    // WatchTheTime2 () {
    //   return this.selectHs2 + ':' + this.selectMs2
    // },
    checkWidth () {
      return window.screen.width
    }
  },
  watch: {
    // WatchTheTime (val) {
    //   this.startTimeCollect = this.WatchTheTime
    // },
    // WatchTheTime1 (val) {
    //   this.startTimeCollect1 = this.WatchTheTime1
    // },
    // WatchTheTime2 (val) {
    //   this.startTimeCollect2 = this.WatchTheTime2
    // },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    itemFlashSale (item, ATBItem) {
      this.productList = []
      this.productAttributeList = []
      if (item !== undefined) {
        item.forEach(e => {
          this.productList.push(e.product_id)
          this.productAttributeList.push(e.product_attribute_id)
        })
      }
      if (item.length < ATBItem.length) {
        this.updateFlash('del', item.length)
      }
      if (item.length > ATBItem.length) {
        this.updateFlash('add', item.length)
      }
      // this.getProduct()
    },
    itemFlashSalePhase2 (val) {
    }
  },
  mounted () {
  },
  async created () {
    // this.currentDateTimeH()
    // this.currentDateTimeM()
    // setInterval(function () {
    //   this.currentDateTimeM()
    // }, 10 * 1000)
    // alert(this.timeOrigin)
    this.$EventBus.$emit('SelectPath')
    this.dateOpen()
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      // this.getDataCoupons()
      this.seller_shop_id = localStorage.getItem('shopSellerID')
      // await this.getData()
      await this.getDataForTree()
      // this.getPoint()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    async getProduct () {
      this.oldData = this.itemFlashSalePhase2
      // var shopID = localStorage.getItem('shopSellerID')
      var data = {
        // seller_shop_id: parseInt(shopID),
        // raw_list: this.productList
        product_list: this.productList,
        attribute_list: this.productAttributeList
      }
      if (this.productList.length !== 0 && this.productAttributeList !== 0) {
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsGetListProductDetail', data)
        const res = await this.$store.state.ModuleManageFlashSale.stateGetListProductDetail
        // this.itemFlashSalePhase2 = res.data
        var newData = res.data
        const ids = new Set(this.oldData.map(item1 => item1.have_attribute === 'no' ? item1.product_id : item1.attribute_id))
        newData.forEach(item2 => {
          if (!ids.has(item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id)) {
            this.oldData.push(item2)
            ids.add(item2.have_attribute === 'no' ? item2.product_id : item2.attribute_id) // อัปเดต Set
          }
        })
        this.itemFlashSalePhase2 = await [...this.oldData]
        this.$store.commit('closeLoader')
      }
    },
    pageChange (item) {
      this.$refs.FormProductFlash2.validate()
    },
    dateOpen () {
      this.searchStartDate = this.formatDateToShow(this.date)
      this.searchEndDate = this.formatDateToShow(this.date1)
    },
    currentDateTimeM () {
      const current = new Date()
      this.timeOriginM = current.getMinutes()
      // this.$forceUpdate()
      return this.timeOriginM
    },
    currentDateTimeH () {
      const current = new Date()
      this.timeOriginH = current.getHours()
      // this.timeOriginM = current.getMinutes()
      return this.timeOriginH
    },
    realPriceNoGood (item, index) {
      this.realPriceForSure = ''
      this.realPriceForSure = item.real_price
      if (item.flashprice === 'NaN' || item.flashprice === '') {
        this.itemFlashSalePhase2[index].flashprice = ''
      } else {
        this.itemFlashSalePhase2[index].flashprice = this.itemFlashSalePhase2[index].flashprice.replace(/,/g, '')
        this.itemFlashSalePhase2[index].flashprice = Number(this.itemFlashSalePhase2[index].flashprice).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }
    },
    async getDataForTree () {
      // await this.$store.dispatch('actionGetForSelectFlashSaleSeller')
      // var res = await this.$store.state.NSGManageFlashSale.stateGetForSelectFlashSaleSeller
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionscategoryShopList', data)
      var res = await this.$store.state.ModuleManageCoupon.stateCategoryShopList
      if (res.result === 'Success') {
        this.dataItem = res.data
      } else {
        if (res.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(res.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(res.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    closeCreateFlash () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSale' })
      } else {
        this.$router.push({ path: '/manageFlashSaleMobile' })
      }
    },
    async updateFlash (text, index) {
      if (text === 'add') {
        // this.itemFlashSalePhase2.push({
        //   attribute: this.itemFlashSale[index === 0 ? index : index - 1].attribute,
        //   product_id: this.itemFlashSale[index === 0 ? index : index - 1].sku,
        //   product_name: this.itemFlashSale[index === 0 ? index : index - 1].name2,
        //   id: this.itemFlashSale[index === 0 ? index : index - 1].product_id,
        //   attribute_id: this.itemFlashSale[index === 0 ? index : index - 1].have_attribute === 'no' ? '-1' : this.productList[index === 0 ? index : index - 1].toString(),
        //   fake_price: this.itemFlashSale[index === 0 ? index : index - 1].fake_price,
        //   real_price: this.itemFlashSale[index === 0 ? index : index - 1].real_price,
        //   media_path: this.itemFlashSale[index === 0 ? index : index - 1].media_path
        // })
        this.theRedP = true
      }
      if (text === 'del') {
        // this.itemFlashSalePhase2 = this.itemFlashSale
        // this.itemFlashSalePhase2.splice(index === 0 ? index : index - 1, 1)
      }
      this.productListID = this.itemFlashSale.map(e => e.product_id)
      this.productAttributeList = this.itemFlashSale.map(e => e.have_attribute === 'no' ? '' : e.product_attribute_id)
      await this.getProduct()
      const filterStepOne = await this.itemFlashSale.map(e => { return e.product_attribute_id === undefined ? e.product_id : e.product_attribute_id })
      const filterStepTwo = Array.isArray(this.itemFlashSalePhase2)
        ? this.itemFlashSalePhase2.filter(x => {
          const id = x.attribute_id === null ? x.product_id : x.attribute_id
          return filterStepOne.includes(id)
        })
        : []
      filterStepTwo.sort((a, b) => {
        const idA = a.attribute_id === null ? a.product_id : a.attribute_id
        const idB = b.attribute_id === null ? b.product_id : b.attribute_id
        return filterStepOne.indexOf(idA) - filterStepOne.indexOf(idB)
      })
      // this.oldData = this.itemFlashSalePhase2
      // this.oldData.sort((a, b) => {
      //   const idA = a.attribute_id === null ? a.product_id : a.attribute_id
      //   const idB = b.attribute_id === null ? b.product_id : b.attribute_id
      //   return filterStepOne.indexOf(idA) - filterStepOne.indexOf(idB)
      // })
      this.itemFlashSalePhase2 = await filterStepTwo
    },
    async createFlash () {
      if (this.$refs.FormProductFlash.validate(true)) {
        this.itemFlashSalefinalPhase = this.itemFlashSalePhase2.map(e => {
          if (e.flashprice !== '' && e.flashprice !== null && e.flashprice !== undefined) {
            return {
              product_attribute_id: e.attribute_id === undefined || e.attribute_id === null ? '-1' : e.attribute_id.toString(),
              product_id: e.product_id.toString(),
              flashsale_price: e.flashprice.replace(/,/g, '')
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มราคาสินค้าในอีกหน้าให้ครบถ้วน', showConfirmButton: false, timer: 2000 })
            this.itemFlashSalefinalPhase = ''
          }
        })
        if (this.FlashsTest[0] === '' || this.FlashsTest[0] === null || this.FlashsTest[0] === undefined) {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพ', showConfirmButton: false, timer: 2000 })
          this.theRedI = false
        } else if (this.itemFlashSalePhase2.length === 0) {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มสินค้าแฟลชเซลล์', showConfirmButton: false, timer: 2000 })
          this.theRedP = false
        }
        // else if (this.create_date === '' || this.create_date === null || this.create_date === undefined) {
        //   this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มวันเริ่มต้นแฟลชเซลล์', showConfirmButton: false, timer: 2000 })
        // } else if (this.create_date1 === '' || this.create_date1 === null || this.create_date1 === undefined) {
        //   this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มวันสิ้นสุดแฟลชเซลล์', showConfirmButton: false, timer: 2000 })
        // }
        // for (let index = 0; index < this.itemFlashSalefinalPhase.length; index++) {
        //   if (this.itemFlashSalefinalPhase[index] === '' || this.itemFlashSalefinalPhase[index] === null || this.itemFlashSalefinalPhase[index] === undefined) {
        //     this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มสินค้าแฟลชเซลล์', showConfirmButton: false, timer: 2000 })
        //     break
        //   }
        // }
        var data = {
          seller_shop_id: this.seller_shop_id,
          image_path: this.FlashsTest[0].image_data,
          // start_date: this.create_date,
          // end_date: this.create_date1,
          start_date: '2024-07-15',
          end_date: '2024-07-15',
          countdown_date: this.create_date2,
          status: 'active',
          product_list: this.itemFlashSalefinalPhase
        }
        await this.$store.dispatch('actionsCreateFlashSale', data)
        const res = await this.$store.state.ModuleManageFlashSale.stateCreateFlashSale
        if (res.result === 'SUCCESS') {
          this.dialogSuccess = false
          await this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: 'สร้างรายการแฟลชเซลล์สำเร็จ'
          }).then(() => {
            this.dialogSuccess = false
            if (!this.MobileSize) {
              this.$router.push({ path: '/manageFlashSale' })
            } else {
              this.$router.push({ path: '/manageFlashSaleMobile' })
            }
          })
        } else if (res.result === 'FAILED') {
          this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 2000 })
        } else {
          if (res.message === 'Not access this function') {
            await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
            this.$router.push({ path: '/userInfo' }).catch(() => {})
          } else if (res.result === 'FAILED') {
            if (res.message === 'Have flashsale in this start_date') {
              this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในวันที่เริ่มต้นนี้แล้ว', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'Have flashsale in this end_date') {
              this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในวันที่สิ้นสุดนี้แล้ว', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'Have flashsale in this start_date and end_date') {
              this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในช่วงเวลานี้แล้ว', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'end_date not over start_date') {
              this.$swal.fire({ icon: 'error', text: 'วันที่สิ้นสุดต้องไม่น้อยกว่าวันที่เริ่มต้น', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'countdown_date not over start_date') {
              this.$swal.fire({ icon: 'error', text: 'ตัวนับเวลาถอยหลังต้องไม่น้อยกว่าวันที่เริ่มต้น', showConfirmButton: false, timer: 2000 })
            } else if (res.message === 'start_date not over countdown_date') {
              this.$swal.fire({ icon: 'error', text: 'วันที่เริ่มต้น มากกว่าตัวนับเวลาถอยหลังก่อนเริ่มแฟลชเซลล์ไม่ได้', showConfirmButton: false, timer: 2000 })
            }
          } else {
            var [msg, iconMsg] = statusErr(res.code)
            this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณากรอกข้อมูลแฟลชเซลล์ให้ครบถ้วน', showConfirmButton: false, timer: 2000 })
        if (this.itemFlashSalePhase2.length === 0) {
          this.theRedP = false
        }
        this.theRedI = false
      }
    },
    async DeleteFromTable (item) {
      var indexX = this.itemFlashSalePhase2.indexOf(item)
      this.itemFlashSale.splice(indexX, 1)
      await this.updateFlash('del', this.itemFlashSalePhase2.length)
    },
    scrollToTopPage () {
      window.scrollTo(0, 0)
    },
    setValueStartDate () {
      // this.searchEndDate = ''
      this.searchStartDate = this.formatDateToShow(this.date) + '  ' + this.startTimeCollect
      this.create_date = this.date + 'T' + this.startTimeCollect + ':00'
    },
    setValueStartDate1 () {
      this.searchEndDate = this.formatDateToShow(this.date1) + ' ' + this.startTimeCollect1
      this.create_date1 = this.date1 + 'T' + this.startTimeCollect1 + ':00'
    },
    setValueStartDate2 () {
      this.countdownDate = this.formatDateToShow(this.date2) + ' ' + this.startTimeCollect2
      this.create_date2 = this.date2 + 'T' + this.startTimeCollect2 + ':00'
    },
    grandReset () {
      if (this.create_date1 !== '') {
        this.searchEndDate = ''
        this.date1 = ''
        this.startTimeCollect1 = ''
        this.create_date1 = ''
        this.selectHs1 = ''
        this.selectMs1 = ''
      }
      if (this.create_date2 !== '') {
        this.countdownDate = ''
        this.date2 = ''
        this.startTimeCollect2 = ''
        this.create_date2 = ''
        this.selectHs2 = ''
        this.selectMs2 = ''
      }
    },
    setDateAlter () {
      const currentDate = new Date(this.date)
      const currentDate1 = new Date(this.date)
      currentDate.setDate(currentDate.getDate() - 1)
      currentDate1.setDate(currentDate1.getDate() + 1)
      this.dateAlter = currentDate.toISOString().substr(0, 10)
      this.dateAlter1 = currentDate1.toISOString().substr(0, 10)
    },
    setStartTime () {
      if (this.date !== this.dateOrigin) {
        this.selectHs = '00'
        this.selectMs = '00'
      } else {
        if (this.currentDateTimeH() >= 10) {
          this.selectHs = this.currentDateTimeH().toString()
        } else {
          this.selectHs = '0' + this.currentDateTimeH().toString()
        }
        if (this.currentDateTimeM() >= 10) {
          this.selectMs = this.currentDateTimeM().toString()
        } else {
          this.selectMs = '0' + this.currentDateTimeM().toString()
        }
        this.startTimeCollect = ''
      }
    },
    setEndTime () {
      this.selectHs1 = '23'
      this.selectMs1 = '59'
    },
    // deleteStartTimeCollect () {
    //   // alert(this.startTimeCollect1)
    //   this.startTimeCollect = ''
    // },
    // deleteStartTimeCollect1 () {
    //   // alert(this.startTimeCollect1)
    //   this.startTimeCollect1 = ''
    // },
    // deleteStartTimeCollect2 () {
    //   // alert(this.startTimeCollect1)
    //   this.startTimeCollect2 = ''
    // },
    closeDialogSuccess () {
      this.dialogSuccess = false
    },
    closeDialogModal () {
      this.searchStartDate = ''
      this.date = ''
      this.startTimeCollect = ''
      this.create_date = ''
      this.selectHs = ''
      this.selectMs = ''
      this.dialogStartDate = false
    },
    closeDialogModal1 () {
      this.searchEndDate = ''
      this.date1 = ''
      this.startTimeCollect1 = ''
      this.create_date1 = ''
      this.selectHs1 = ''
      this.selectMs1 = ''
      this.dialogEndDate = false
    },
    closeDialogModal2 () {
      this.countdownDate = ''
      this.date2 = ''
      this.startTimeCollect2 = ''
      this.create_date2 = ''
      this.selectHs2 = ''
      this.selectMs2 = ''
      this.dialogCountdownDate = false
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    async UploadImage () {
      const shopId = localStorage.getItem('shopSellerID')
      for (let i = 0; i < this.DataImage.length; i++) {
        const element = this.DataImage[i]
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
          var url = URL.createObjectURL(element)
          const image = new Image()
          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => {
              const dimensions = {
                height: image.height,
                width: image.width
              }
              resolve(dimensions)
            }
            image.src = url
          })
          if (this.FlashsTest.length < 2) {
            if (i < 1) {
              if (imageDimensions.height <= 620 && imageDimensions.width <= 1480) {
                const reader = new FileReader()
                reader.readAsDataURL(element)
                reader.onload = async () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  this.FlashsTest.push({
                    image_data: resultReader.split(',')[1],
                    url: url,
                    link: ''
                  })

                  const data = {
                    image: [this.FlashsTest[0].image_data],
                    type: '',
                    seller_shop_id: shopId
                  }
                  this.$store.commit('openLoader')
                  await this.$store.dispatch('actionsUploadToS3', data)
                  const response = await this.$store.state.ModuleShop.stateUploadToS3

                  if (response.message === 'List Success.') {
                    this.FlashsTest[0].image_data = response.data.list_path[0].path
                    this.$store.commit('closeLoader')
                  }
                  this.theRedI = true
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'โปรดใช้รูปตามขนาดที่กำหนด', showConfirmButton: false, timer: 1500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
        }
      }
    },
    async FilterDefault (startDate, endDate) {
      // await this.getBuyer(startDate, endDate)
    },
    RemoveImage (index, val) {
      this.FlashsTest.splice(index, 1)
    },
    backToHome () {
      this.$EventBus.$emit('openNavBar')
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageFlashSale' })
      } else {
        this.$router.push({ path: '/manageFlashSaleMobile' })
      }
    }
  }
}
</script>
<style scoped>
::v-deep .v-date-picker-table {
  height: 200px !important;
}
.v-select__selection--comma {
  margin-left: 40px;
}
.v-menu__content {
  /* padding-block: 5px; */
  width: 258px;
  margin-top: 40px;
  z-index: 200;
  background-color: rgb(223, 231, 236);
  overflow: hidden;
  box-shadow: none;
}
.vSelectLineHeight /deep/ .v-select__selection--comma {
  line-height: 25px !important;
}
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.v-data-table /deep/
.v-data-table__wrapper .v-data-table__mobile-table-row {
  margin-bottom: 20px;
  border: 1px solid #ededed;
  display: block;
  border-radius: 8px;
}
@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.6rem;
    padding: 0 0 0 8px;
  }
}
@media only screen and (min-width: 768px) {
  /* For desktop: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    /* font-size: 14px; */
    padding: 0 0 0 8px;
  }
}
.v-data-table__empty-wrapper {
    text-align: center;
    place-self: center;
}
</style>
