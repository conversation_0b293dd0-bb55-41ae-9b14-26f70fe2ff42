<template lang="html">
  <div>
    <div v-if="checkLogin === true">
      <CheckoutUI/>
    </div>
    <div v-else>
      <CheckoutLocalUI/>
      <!-- <ModalAddressLocalUI  /> -->
    </div>
    <!-- <ProductSameShop :propsData='GetAllProduct.recommend' header='สินค้าที่คุณอาจจะชอบ' :check='status'/> -->
  </div>
</template>

<script>
const recommend = []
for (let i = 0; i < 50; i++) {
  recommend.push({
    product_id: i,
    name: `Data Title recommend ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CheckoutUI: () => import(/* webpackPrefetch: true */ '@/components/Cart/CheckoutUI'),
    // ModalAddress: () => import('@/components/Modal/AddAddressProfile'),
    CheckoutLocalUI: () => import(/* webpackPrefetch: true */ '@/components/Cart/CheckoutLocalUI')
    // ModalAddressLocalUI: () => import('@/components/Modal/AddressLocalUI')
    // ProductSameShop: () => import('@/components/DetailProduct/ProductSameShop'),
  },
  data () {
    return {
      GetAllProduct: {
        recommend
      },
      checkLogin: false,
      propsAddress: [],
      check: false,
      status: false
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    // this.$EventBus.$on('SentGetCart', this.checkAddress)
    // this.$EventBus.$on('EditAddressComplete', (data) => { this.checkAddress(data) })
    if (localStorage.getItem('oneData') !== null) {
      this.checkLogin = true
    } else {
      this.checkLogin = false
      // this.check = true
    }
  },
  methods: {
    async checkAddressLocal () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      // console.log('this.propsAddress[0].address_data.length', this.propsAddress[0].address_data.length)
      if (this.propsAddress[0].address_data.length === 0) {
        this.check = true
      }
    }
  }
}
</script>

<style lang="css" scoped>
</style>
