import AxiosWithdrawAffiliate from './axios_withdraw_affiliate'
const ModuleWithdrawAffiliate = {
  state: {
    stateTransactionUserAffiliate: [],
    stateDashboardAffiliate: [],
    stateTransferAffiliateClick: []
  },
  mutations: {
    mutationsTransactionUserAffiliate (state, data) {
      state.stateTransactionUserAffiliate = data
    },
    mutationsDashboardAffiliate (state, data) {
      state.stateDashboardAffiliate = data
    },
    mutationsTransferAffiliateClick (state, data) {
      state.stateTransferAffiliateClick = data
    }
  },
  actions: {
    async actionsTransactionUserAffiliate (context, access) {
      const response = await AxiosWithdrawAffiliate.getTransactionUserAffiliate(access)
      await context.commit('mutationsTransactionUserAffiliate', response)
    },
    async actionsDashboardAffiliate (context, access) {
      const response = await AxiosWithdrawAffiliate.getDashboardAffiliate(access)
      await context.commit('mutationsDashboardAffiliate', response)
    },
    async actionTransferAffiliateClick (context, access) {
      const response = await AxiosWithdrawAffiliate.getTransferAffiliateClick(access)
      await context.commit('mutationsTransferAffiliateClick', response)
    }
  }
}
export default ModuleWithdrawAffiliate
