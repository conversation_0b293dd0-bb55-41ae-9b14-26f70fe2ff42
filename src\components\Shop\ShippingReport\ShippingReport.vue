<template>
  <div>
    <v-container :class="MobileSize ? 'mt-2' : ''">
      <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
        <h2 v-if="!MobileSize" class="ml-4" style="font-size:24px"><B>รายงานการจัดส่ง</B></h2>
        <v-row cols="12" md="12" class="pa-3" v-if="MobileSize">
          <v-icon v-if="MobileSize || IpadSize " @click="Cancel()" color="#27AB9C" class="mb-0 ml-2">mdi-chevron-left
          </v-icon>
          <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="ml-2">เรียกพนักงานรับพัสดุ</span>
        </v-row>
        <v-container>
          <v-tabs v-model="activeTab" @change="getOrderReturn" :show-arrows="IpadSize || IpadProSize" class="pb-6">
            <v-tab :key="0"> <!-- "key" attribute is not necessary for Vuetify tabs -->
              <span>ทั้งหมด <v-chip color="#27AB9C" class="white--text">{{ countAll }}</v-chip></span>
            </v-tab>
            <v-tab :key="1">
              <span>จัดส่งสำเร็จ <v-chip color="#1AB759" class="white--text">{{ countSuccess }}</v-chip></span>
            </v-tab>
            <v-tab :key="4">
              <span>กำลังดำเนินการ <v-chip color="#434bf7" class="white--text">{{ countWaiting }}</v-chip></span>
            </v-tab>
            <v-tab :key="2">
              <span>กำลังจัดส่ง <v-chip color="#FAD02C" class="white--text">{{ countOnDelivering }}</v-chip></span>
            </v-tab>
            <v-tab :key="3">
              <span>ถูกตีกลับ/ยกเลิก <v-chip color="#D1392B" class="white--text">{{ countReturn }}</v-chip></span>
            </v-tab>
            <v-tab :key="6">
              <span>เรียกพนังงานขนส่ง <v-chip color="#FFA500" class="white--text">{{ countAllocating }}</v-chip></span>
            </v-tab>
          </v-tabs>
          <div v-if="dataTable.length > 0">
          <v-text-field :style="IpadSize ? '' : 'width:400px'" v-model="search" append-icon="mdi-magnify" placeholder="ค้นหาจากรหัสการสั่งซื้อ"
            outlined dense rounded hide-details></v-text-field><br />
          <h4 v-if="!MobileSize" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" class="mb-6 mt-1">
            {{ pageHeaders }} {{ showCountOrder }} รายการ
          </h4>
          <v-data-table v-model="selected" @toggle-select-all="selectAllToggle" :headers="headers" :items="dataTable"
            :search="search" item-key="reference_id" @pagination="countOrder"  color="blue" show-select :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-1" no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา" no-data-text="ไม่มีรายการในตาราง">
            <!-- <template v-slot:[`item.data-table-select`]="{ item, isSelected, select}">
              <v-simple-checkbox :value="isSelected" :readonly="item.status === 'สร้างใหม่' && item.business_type === 'FLASH' ? false:true"
                :disabled="item.status === 'สร้างใหม่' && item.business_type === 'FLASH'? false:true" @input="select($event)"></v-simple-checkbox>
            </template> -->
            <template v-slot:[`header.data-table-select`]="{ on, props }">
                <v-simple-checkbox
                  v-model="checkboxAll"
                  :indeterminate="checkboxIndeterminate"
                  :ripple="false"
                  v-bind="props"
                  v-on="on"
                ></v-simple-checkbox>
            </template>
             <template v-slot:[`item.data-table-select`]="{ item, isSelected, select }">
                <v-simple-checkbox :value="isSelected" :readonly="item.status === 'สร้างใหม่' && item.business_type === 'FLASH' ? false : true"
                  :disabled="item.status === 'สร้างใหม่' && item.business_type === 'FLASH' ? false : true" @input="select($event)" @click="chackselectitem()"></v-simple-checkbox>
              </template>
            <template v-slot:[`item.reference_id`]="{ item }">
              <!-- <span><a :href="item.url_barcode_picture" target="_blank">{{item.reference_id}}</a></span> -->
              <span>{{item.reference_id}}</span>
            </template>
            <template v-slot:[`item.order_no`]="{ item }">
              <span><a :href="item.url_tracking" target="_blank">{{item.order_no}}</a></span>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'สร้างใหม่'">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">รอเรียกพนังงานขนส่ง</v-chip>
              </span>
              <span v-else-if="item.status === 'เรียกพนักงานรับพัสดุ'">
                <v-chip class="ma-2" color="#fff9de" text-color="#fac807">เรียกพนักงานรับพัสดุ</v-chip>
              </span>
              <span v-else-if="item.status === 'พนักงานรับพัสดุในจุดต้นทาง'">
                <v-chip class="ma-2" color="#fff9de" text-color="#fac807">พนักงานรับพัสดุในจุดต้นทาง</v-chip>
              </span>
              <span v-else-if="item.status === 'พัสดุอยู่ระหว่างการขนส่งจากสาขาต้นทางไปสาขาปลายทาง'">
                <v-chip class="ma-2" color="#fff9de" text-color="#fac807">พัสดุอยู่ระหว่างการขนส่งจากสาขาต้นทางไปสาขาปลายทาง</v-chip>
              </span>
              <span v-else-if="item.status === 'พัสดุถึงสาขาปลายทางอยู่ระหว่างการจัดส่ง'">
                <v-chip class="ma-2" color="#fff9de" text-color="#fac807">พัสดุถึงสาขาปลายทางอยู่ระหว่างการจัดส่ง</v-chip>
              </span>
              <span v-else-if="item.status === 'พัสดุถึงผู้รับปลายทาง'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พัสดุถึงผู้รับปลายทาง</v-chip>
              </span>
              <span v-else-if="item.status === 'ไม่สามารถติดต่อผู้รับได้ พัสดุตีกลับสาขาปลายทาง รอจัดส่งใหม่อีกครั้ง'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ไม่สามารถติดต่อผู้รับได้ พัสดุตีกลับสาขาปลายทาง รอจัดส่งใหม่อีกครั้ง</v-chip>
              </span>
              <span v-else-if="item.status === 'นำส่งไม่สำเร็จหรือมีการปฏิเสธจากผู้รับ'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">นำส่งไม่สำเร็จหรือมีการปฏิเสธจากผู้รับ</v-chip>
              </span>
              <span v-else-if="item.status === 'เคลมและไม่มีการส่งคืนสินค้า'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">เคลมและไม่มีการส่งคืนสินค้า</v-chip>
              </span>
              <span v-else-if="item.status === 'มีการตัดสินใจตีคืนพัสดุกลับไปต้นทาง'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">มีการตัดสินใจตีคืนพัสดุกลับไปต้นทาง</v-chip>
              </span>
              <span v-else-if="item.status === 'มีการยืนยันน้ำหนักและขนาดมาจากผู้ขนส่ง'">
                <v-chip class="ma-2" color="#E9EAFE" text-color="#111FFA">มีการยืนยันน้ำหนักและขนาดมาจากผู้ขนส่ง</v-chip>
              </span>
              <span v-else-if="item.status === 'มีการยืนยันราคาค่าขนส่งมาจากผู้ขนส่ง'">
                <v-chip class="ma-2" color="#E9EAFE" text-color="#111FFA">มีการยืนยันราคาค่าขนส่งมาจากผู้ขนส่ง</v-chip>
              </span>
              <span v-else-if="item.status === 'พนักงานรับพัสดุในจุดต้นทางแล้วผู้ส่งเปลี่ยนใจเรียกคืนสินค้า'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">พนักงานรับพัสดุในจุดต้นทางแล้วผู้ส่งเปลี่ยนใจเรียกคืนสินค้า</v-chip>
              </span>
              <span v-else-if="item.status === 'ยกเลิกงาน'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกงาน</v-chip>
              </span>
            </template>
            <template v-slot:[`item.date`]="{ item }">
              {{ new Date(item.date).toLocaleDateString('th-TH', {timeZone: "UTC", year: 'numeric', month:
              'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.callcuriers`]="{ item }">
              <v-row v-if="item.business_type === 'OUT_SOURCE'" >
                <v-col>
                  <!-- <v-chip dense class="ma-2" outlined color="#27AB9C" style="pointer-events: none;"> -->
                    <B>ขนส่งนอกระบบ</B>
                  <!-- </v-chip> -->
                </v-col>
              </v-row>
              <v-row v-else-if="item.status === 'สร้างใหม่' && item.business_type === 'FLASH'">
                <v-col>
                  <!-- <v-btn :disabled="selected.length !== 0 && item.status === 1 ? true:false || item.status === 1? false:true " dense
                    class="ma-2" outlined color="#27AB9C" @click="OpenDialogCallCurier(item)"><B>เรียกพนักงาน</B>
                  </v-btn> -->
                  <v-btn dense class="ma-2" outlined color="#27AB9C" @click="OpenDialogCallCurier(item)">
                    <B>เรียกพนักงาน</B>
                  </v-btn>
                </v-col>
              </v-row>
              <v-row v-else-if="item.status === 'ยกเลิกงาน'">
                <v-col>
                </v-col>
              </v-row>
              <v-row v-else>
                <v-col>
                  <v-btn outlined class="custom-btn-style"
                  @click="OpenDialogBarCode(item)"
                  >
                    <v-icon>mdi-dots-vertical</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </template>
          </v-data-table>

          <div class="pt-2"><span style="color:red">หมายเหตุ</span> : ขนส่งของ SCG จะไม่มีการเรียกพนักงานจัดส่ง แต่จะเข้ามารับประมาณเที่ยงของทุกวัน</div>
          <v-container v-if="!MobileSize">
            <v-row dense justify="end">
              <!-- <v-col cols="3" md="1" :style="IpadProSize ? 'margin-right: 2%' : ''"> -->
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true" outlined
                  @click="OpenDialogDelete()"><B>ยกเลิก</B></v-btn>
              <!-- </v-col> -->
              <!-- <v-col cols="7" :md="IpadProSize ? 4 : IpadSize ? 5 : 3"> -->
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true"
                  class="ml-1 pl-8 pr-8 white--text" @click="OpenDialogCallCurierAll()">
                  <B>เรียกพนักงานทั้งหมด({{this.selected.length}})</B>
                </v-btn>
              <!-- </v-col> -->
            </v-row>
          </v-container>
          <v-container v-if="MobileSize">
            <v-row class="justify-end">
              <v-col cols="3">
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true" outlined
                  @click="OpenDialogDelete()"><B>ยกเลิก</B></v-btn>
              </v-col>
              <v-col cols="9">
                <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true"
                  class="ml-1 pl-8 pr-8 white--text" @click="OpenDialogCallCurierAll()">
                  <B>เรียกพนักงานทั้งหมด({{this.selected.length}})</B>
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </div>
        </v-container>
        <v-container v-if="dataTable.length === 0">
          <v-row justify="center" align-content="center" >
            <v-col cols="12" md="12" align="center" style="min-height: 636px;">
              <div style="padding-top: 90px;">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                  height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการ{{ emptyPageText }}</span><br />
              </h2>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-container>

    <v-dialog v-model="dialog" persistent max-width="550" style="overflow: hidden;">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">เรียกพนักงานรับพัสดุทั้งหมด</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card width="100%" style="overflow: hidden;" align="center">
        <!-- <v-card-title>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณากดยืนยันเพื่อทำการเรียกพนักงานรับพัสดุ</span>
        </v-card-title><br /> -->
        <v-card-text class="pt-6">
          <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">กรุณาตรวจสอบก่อนที่จะทำการยืนยันการเรียกพนักงานรับพัสดุ</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessCurier()">ตกลง</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Delete" width="550" persistent>
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">ยกเลิกการเลือก</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card align="center">
        <!-- <v-card-title>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณากดยืนยันเพื่อทำการยกเลิกการเลือก</span>
        </v-card-title><br /> -->
        <v-card-text class="pt-6">
          <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">กรุณาตรวจสอบก่อนที่จะยกเลิกการเรียกพนักงานรับพัสดุทั้งหมด</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="CancelSelect()">ตกลง</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogAllselect" width="600" persistent>
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">เรียกพนักงานรับพัสดุทั้งหมด</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card align="center">
        <!-- <v-card-title>
          <span style="white-space: normal; display: inline-block; word-break:break-word; width:600px">กรุณากดยืนยันเพื่อทำการเรียกพนักงานรับพัสดุทั้งหมด</span>
        </v-card-title><br /> -->
        <v-card-text class="pt-6">
          <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px;">กรุณาตรวจสอบก่อนที่จะทำการยืนยันการเรียกพนักงานรับพัสดุทั้งหมด {{ this.selected.length }} รายการ <br /> ที่ได้ทำการเลือกไว้</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()">ยกเลิก</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessCurierAll()">ตกลง</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogBarCode" persistent max-width="600" style="overflow: hidden;">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">จัดการออเดอร์</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card style="background: #FFFFFF; border-radius: 2px;">
        <v-card-text>
          <v-row justify="center" class="pt-4" dense>
            <v-col cols="4">
              <v-card @click="DownloadBarShort()" :width="MobileSize ? '100%' : '100%'" :height="MobileSize ? '100%' : '150px'">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center" class="pt-4">
                      <v-icon color="#27AB9C" size="40">mdi-qrcode</v-icon>
                    </v-col>
                    <v-col cols="12" align="center" class="pt-4">
                      <span class="text-break">Barcode แบบย่อ</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="4">
              <v-card @click="DownloadBarFull()" :width="MobileSize ? '100%' : '100%'" :height="MobileSize ? '100%' : '150px'">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center" class="pt-4">
                      <v-icon size="40" color="#27AB9C">mdi-barcode-scan</v-icon>
                    </v-col>
                    <v-col cols="12" align="center" class="pt-4">
                      <span class="text-break">Barcode แบบเต็ม</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="4">
              <v-card @click="TrackingUrl()" :width="MobileSize ? '100%' : '100%'" :height="MobileSize ? '100%' : '150px'">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center" class="pt-8">
                      <v-img width="30" src="@/assets/icons/Vector.png" contain></v-img>
                    </v-col>
                    <v-col cols="12" align="center" class="pt-4">
                      <span class="text-break">ติดตามสถานะ</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- <v-dialog v-model="dialogBarShort" persistent max-width="600" style="overflow: hidden;">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">barcode and preview</font>
        </span>
        <v-btn icon dark @click="CloseBarCode()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card style="background: #FFFFFF; border-radius: 2px;">
        <v-card-text>
          <v-row justify="center" class="pt-4" dense>
            <v-col>
              <v-img></v-img>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog> -->
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data: () => ({
    activeTab: null,
    a: false,
    quantity: 0,
    res: [],
    respons: [],
    oneData: [],
    selected: [],
    data: [],
    Message: '',
    DataCurier: '',
    search: '',
    shopID: '',
    pageHeaders: '',
    emptyPageText: '',
    dialog: false,
    dialog_Delete: false,
    dialogAllselect: false,
    dialogBarCode: false,
    dialogBarShort: false,
    downloadBarShort: '',
    downloadBarFull: '',
    checkboxAll: false,
    trackingUrl: '',
    disabledCount: 0,
    dataTable: [],
    checkboxIndeterminate: false,
    showCountOrder: '',
    countAll: '',
    countSuccess: '',
    countWaiting: '',
    countOnDelivering: '',
    countReturn: '',
    countAllocating: '',
    referenceIDCallRider: '',
    token: '',
    headers: [
      { text: 'รหัสการสั่งซื้อ', value: 'reference_id', width: '140', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'ชื่อ - นามสกุล ผู้ซื้อ', value: 'buyer_name', width: '170', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'รหัสการติดตาม', value: 'order_no', width: '140', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'สถานะ', value: 'status', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'อัปเดตล่าสุด', value: 'date', sortable: false, width: '150', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'จัดการ', value: 'callcuriers', sortable: false, width: '180', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
    ],
    headersMobile: [
      { text: 'รหัสการสั่งซื้อ', value: 'reference_id', width: '140', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'ชื่อ-นามสกุล', value: 'buyer_name', width: '200', align: 'start', sortable: false, filterable: false, class: 'backgroundTable fontTable--text white-space: normal;' },
      { text: 'รหัสการติดตาม', value: 'order_no', width: '140', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'สถานะ', value: 'status', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: 'อัปเดตล่าสุด', value: 'updated_at', sortable: false, width: '150', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
      { text: '', value: 'callcuriers', sortable: false, width: '180', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
    ],
    Rules: {
      curiernumber: [
        v => (/^[1-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้นและไม่ติดลบ'
      ]
    }
  }),
  created () {
    this.$EventBus.$emit('AuthorityUsers')
    this.$EventBus.$emit('changeNav')
    this.getSummaryOrderByShopID()
    this.getAllOrderByShopID('all')
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    var dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    if (localStorage.getItem('list_shop_detail') !== null) {
      if (dataDetail.can_use_function_in_shop.manage_order === '1') {
        // this.getListCurier()
        const self = this
        this.data.map(item => {
          if (item.status !== 1) self.disabledCount += 1
        })
      } else {
        this.$router.push({ path: '/' })
      }
    } else {
      this.$router.push({ path: '/' })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ShippingReportMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ShippingReport' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    chackselectitem () {
      if (this.selected.length === 1) {
        this.checkboxAll = false
        this.checkboxIndeterminate = false
      }
    },
    OpenTracking (val) {
      window.open(val.url_tracking)
      // window.open('https://mobilysttech-poc.inet.co.th/staging/tracking3pl/tracking3pl?mode=readonly&track=' + val.order_no + '&type=D')
    },
    countOrder (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    Cancel () {
      this.$router.push({ path: '/sellerMobile?ShopID=' + this.oneData.user.list_shop_detail[0].seller_shop_id + '&ShopName=' + this.oneData.user.list_shop_detail[0].shop_name_th })
    },
    CancelSelect () {
      this.selected = []
      this.dialog_Delete = false
    },
    OpenDialogDelete () {
      this.dialog_Delete = true
    },
    OpenDialogCallCurier (item) {
      this.dialog = true
      this.DataCurier = item
      this.referenceIDCallRider = item.reference_id
    },
    OpenDialogCallCurierAll () {
      this.dialogAllselect = true
    },
    async SuccessCurier () {
      this.dialog = false
      this.$store.commit('openLoader')
      this.getCallRider()
    },
    async SuccessCurierAll () {
      this.dialog = false
      this.$store.commit('openLoader')
      for (let i = 0; i < this.selected.length; i++) {
        var data = {
          reference_id: this.selected[i].reference_id,
          seller_shop_id: JSON.parse(localStorage.getItem('shopDetail')).id
        }
        await this.$store.dispatch('actionCallRider', data)
        var response = await this.$store.state.ModuleShippingReport.stateCallRider
        if (response.ok === 'y') {
          console.log('actionCallRider', response)
        }
      }
      this.dialogAllselect = false
      this.selected = []
      await this.getAllOrderByShopID('all')
      this.$store.commit('closeLoader')
    },
    OpenDialogBarCode (item) {
      this.dialogBarCode = true
      this.downloadBarShort = item.url_barcode_print_small
      this.downloadBarFull = item.url_barcode_print_large
      this.trackingUrl = item.url_tracking
    },
    DownloadBarShort () {
      // this.dialogBarShort = true
      window.open(this.downloadBarShort)
    },
    DownloadBarFull () {
      window.open(this.downloadBarFull)
    },
    TrackingUrl () {
      window.open(this.trackingUrl)
    },
    async getSummaryOrderByShopID () {
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopDetail')).id
      }
      await this.$store.dispatch('actionSummaryOrderByShopID', data)
      var response = await this.$store.state.ModuleShippingReport.stateSummaryOrderByShopID
      if (response.ok === 'y') {
        console.log('actionSummaryOrderByShopID', response)
        this.countAll = response.query_result[0].allOrder
        this.countSuccess = response.query_result[0].deliverySuccess
        this.countWaiting = response.query_result[0].orderInProgress
        this.countOnDelivering = response.query_result[0].deliveryInProgress
        this.countReturn = response.query_result[0].deliveryReturn
        this.countAllocating = response.query_result[0].allocating
      }
    },
    async getAllOrderByShopID (status, shopID) {
      var data = {
        status: status,
        seller_shop_id: JSON.parse(localStorage.getItem('shopDetail')).id
      }
      await this.$store.dispatch('actionAllOrderByShopID', data)
      var response = await this.$store.state.ModuleShippingReport.stateAllOrderByShopID
      if (response.ok === 'y') {
        console.log('actionAllOrderByShopID', response.query_result)
        this.selected = []
        this.checkboxAll = false
        this.checkboxIndeterminate = false
        this.dataTable = response.query_result.result
      }
    },
    async getCallRider () {
      var data = {
        reference_id: this.referenceIDCallRider,
        seller_shop_id: JSON.parse(localStorage.getItem('shopDetail')).id
      }
      await this.$store.dispatch('actionCallRider', data)
      var response = await this.$store.state.ModuleShippingReport.stateCallRider
      if (response.ok === 'y') {
        console.log('actionCallRider', response)
        await this.getAllOrderByShopID('all')
        this.$store.commit('closeLoader')
      }
    },
    async getCallRiderAll (referenceid) {
      var data = {
        reference_id: referenceid,
        seller_shop_id: JSON.parse(localStorage.getItem('shopDetail')).id
      }
      await this.$store.dispatch('actionCallRider', data)
      var response = await this.$store.state.ModuleShippingReport.stateCallRider
      if (response.ok === 'y') {
        console.log('actionCallRider', response)
        await this.getAllOrderByShopID('all')
        this.$store.commit('closeLoader')
      }
    },
    async getOrderReturn (item) {
      if (item === 0) {
        this.getAllOrderByShopID('all', this.shopID)
        this.selected = []
        this.pageHeaders = 'รายการสั่งซื้อทั้งหมด'
        this.emptyPageText = 'เรียกพนักงานรับพัสดุ'
      } else if (item === 1) {
        this.getAllOrderByShopID('delivery success', this.shopID)
        this.selected = []
        this.pageHeaders = 'รายการจัดส่งสำเร็จทั้งหมด'
        this.emptyPageText = 'จัดส่งสำเร็จ'
      } else if (item === 2) {
        this.getAllOrderByShopID('order in progress', this.shopID)
        this.selected = []
        this.pageHeaders = 'รายการกำลังดำเนินการทั้งหมด'
        this.emptyPageText = 'กำลังดำเนินการ'
      } else if (item === 3) {
        this.getAllOrderByShopID('delivery in progress', this.shopID)
        this.selected = []
        this.pageHeaders = 'รายการกำลังจัดส่งทั้งหมด'
        this.emptyPageText = 'กำลังจัดส่ง'
      } else if (item === 4) {
        this.getAllOrderByShopID('delivery return', this.shopID)
        this.selected = []
        this.pageHeaders = 'รายการถูกตีกลับ/ยกเลิกทั้งหมด'
        this.emptyPageText = 'ถูกตีกลับ/ยกเลิก'
      } else if (item === 5) {
        this.getAllOrderByShopID('allocating', this.shopID)
        this.selected = []
        this.pageHeaders = 'รายการเรียกพนังงานขนส่งทั้งหมด'
        this.emptyPageText = 'เรียกพนังงานขนส่ง'
      }
    },
    Close () {
      this.dialog_Delete = false
      this.dialog = false
      this.dialogAllselect = false
      this.DataCurier = ''
      this.dialogBarCode = false
    },
    CloseBarCode () {
      this.dialogBarShort = false
    },
    selectAllToggle (props) {
      if (this.search === '') {
        if (this.selected.length !== this.dataTable.length - this.disabledCount && this.checkboxAll) {
          // if (this.a) {
          //   this.selected = []
          //   this.a = false
          // } else {
          //   this.selected = []
          //   this.a = true
          //   const self = this
          //   this.dataTable.forEach(item => {
          //     if (item.status === 'สร้างใหม่' && item.business_type === 'FLASH') {
          //       self.selected.push(item)
          //     }
          //   })
          // }
          console.log('เลือก', this.checkboxAll)
          this.selected = []
          const self = this
          this.dataTable.forEach(item => {
            if (item.status === 'สร้างใหม่' && item.business_type === 'FLASH') {
              self.selected.push(item)
            }
          })
          if (this.selected.length > 0 && this.dataTable.length === this.selected.length) {
            console.log('เลือกได้ทั้งหมดเลย')
            this.checkboxAll = true
            this.checkboxIndeterminate = false
          } else if (this.selected.length > 0) {
            console.log('เลือกได้ทั้งหมดที่เลือกได้')
            this.checkboxIndeterminate = true
          }
        } else {
          this.selected = []
          this.checkboxAll = false
          this.checkboxIndeterminate = false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.custom-btn-style {
  width: 50px;
  height: 50px;
  border: 1px solid #F2F2F2;
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
}
.text-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.text-break {
  white-space: normal;
  display: inline-block;
  word-break: break-word;
  max-width: 600px;
}
.v-data-table /deep/ .v-data-table-header-mobile__wrapper {
  display: flex;
  justify-content: end;
}
.v-data-table /deep/ .v-simple-checkbox {
  padding-left: 25px;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.whiteNumber {
  color: #FFF;
}
.v-data-table /deep/ .v-data-table__wrapper .v-data-table__mobile-row {
  border-bottom: 0px !important;
  padding-left: 4px;
  padding-right: 4px;
}

</style>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  ::v-deep .elevation-1 tr th:first-of-type, td:first-of-type {
    background-color: #E6F5F3;
    border-style: none !important;
  }
</style>
