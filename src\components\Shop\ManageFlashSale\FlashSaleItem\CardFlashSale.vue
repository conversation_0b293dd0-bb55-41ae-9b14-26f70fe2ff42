<template>
  <v-hover v-slot="{ hover }" v-if="itemProduct !== undefined">
    <v-card v-if="itemProduct.id !== ''" class="px-0 py-2" height="100%" :width="MobileSize ? 120 : (IpadSize ? 170 : IpadProSize ? 185 : 230)" :elevation="hover ? 6 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer; border-radius: 6px; border: 1px solid #FFA337;">      <!-- <v-row dense>
        <v-col cols="12" md="12" sm="12" xs="12" class="ma-2" v-if="itemProduct.quantity === 0 || itemProduct.quantity === null">
          <v-img src="@/assets/Tag/newPreorder.png" height="29" width="73" contain style="margin-left: -24px; margin-top: 0px;" ></v-img>
        </v-col>
        <v-col cols="6" md="6" sm="6" xs="6" class="pt-2 pb-0" v-else>
          <v-img src="@/assets/Tag/newSale.svg" height="29" width="79" contain style="margin-left: -6px; margin-top: 0px;" v-if="itemProduct.message_status === 'sale'"></v-img>
          <v-img src="@/assets/Tag/NewNew.svg" height="29" width="76" contain style="margin-left: -6px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
          <v-img src="@/assets/Tag/Suggest.svg" height="29" width="69" contain style="margin-left: -9px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
          <v-img src="@/assets/Tag/newPreorder.png" height="29" width="73" contain style="margin-left: -24px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
          <v-img src="@/assets/Tag/BestSellerNew.svg" height="29" width="117" contain style="margin-left: -5px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
        </v-col>
      </v-row> -->
      <v-img
        :src="itemProduct.images_URL[0]"
        loading="lazy"
        :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170"
        :width="MobileSize ? '50%' : IpadProSize || IpadSize ? '50%' : '80%'"
        v-if="itemProduct.images_URL.length !== 0 && itemProduct.have_attribute === 'no'"
        contain
        class="align-center d-flex justify-center"
        style="position: relative; margin: auto;"
      >
      </v-img>
      <v-img
        :src="itemProduct.product_color_image_path"
        loading="lazy"
        :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170"
        :width="MobileSize ? '50%' : IpadProSize || IpadSize ? '50%' : '80%'"
        v-else-if="itemProduct.product_color_image_path !== '' && itemProduct.have_attribute === 'yes'"
        contain
        class="align-center d-flex justify-center"
        style="position: relative; margin: auto;"
      >
      </v-img>
      <v-img src="@/assets/NoImage.png" :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170" width="100%" contain v-else @click="DetailProduct(itemProduct)">
      </v-img>
      <v-tooltip top v-if="itemProduct.attribute_priority_1 !== '' && itemProduct.have_attribute === 'yes'">
        <template v-slot:activator="{ on }">
          <v-row dense>
            <v-col cols="12" class="pl-0 mt-1">
              <p v-on="on" style="font-size: 14px; font-weight: bold; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; color: #000000; height: 32px;" class="mb-0 mt-1 px-2 pl-3">{{ itemProduct.attribute_priority_1 }}</p>
            </v-col>
          </v-row>
        </template>
        <span>{{ itemProduct.attribute_priority_1 }}</span>
      </v-tooltip>
      <v-tooltip top v-if="itemProduct.name !== '' && itemProduct.have_attribute === 'no'">
        <template v-slot:activator="{ on }">
          <v-row dense>
            <v-col cols="12" class="pl-0 mt-1">
              <p v-on="on" style="font-size: 14px; font-weight: bold; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; color: #000000; height: 32px;" class="mb-0 mt-1 px-2 pl-3">{{ itemProduct.name }}</p>
            </v-col>
          </v-row>
        </template>
        <span>{{ itemProduct.name }}</span>
      </v-tooltip>
      <!-- <v-card-text class="py-0 pb-0 px-2 mb-3" style="height: 46px;">
        <div v-if="itemProduct.attribute !== null">
          <p style="font-size: 12px; line-height: 16px; font-weight: 400; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; line-height: 24px; color: #333333; height: 40px;">{{ itemProduct.attribute }}</p>
        </div>
      </v-card-text> -->
      <!-- <v-card-text class="py-1 pb-0 px-2" :style="checkWidthScreen > 320 ? 'width: 152px;' : 'width: 100px;'" style="font-size: 12px; line-height: 16px; color: #333333; height: 56px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden;" v-if="itemProduct.attribute === null"></v-card-text> -->
      <!-- <v-card-text class="py-0 px-2 pb-2">
        <v-row dense>
          <v-rating
            v-model="itemProduct.stars"
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
          ></v-rating>
          <v-spacer></v-spacer>
        </v-row>
      </v-card-text> -->
      <v-card-text>
        <v-row dense>
          <v-col cols="12">
            <div style="min-height: 20px">
              <v-row dense class="pl-1">
                <span style="font-size: 16px; font-weight: 600; line-height: 19px; color: #636363; text-decoration-line: line-through;" class="pt-1">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                <v-chip v-if="itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0'" color="#FBE5E4" text-color="#FF0000" style="border-radius: 4px;" :class="MobileSize ? 'px-2 ml-1' : 'ml-2'" small><span class="discountText">ส่วนลด {{ itemProduct.discount_percent }}</span></v-chip>
              </v-row>
            </div>
          </v-col>
          <v-col cols="12">
            <span :style="MobileSize ? 'font-weight: 600; font-size: 18px; line-height: 29px; color: #F5222D;' : 'font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;'">
              ฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
            </span>
          </v-col>
          <v-col cols="12">
            <span style="font-weight: 700; font-size: 14px; line-height: 16px; color: #FAAD14; letter-spacing: -0.2px;" v-if="itemProduct.sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.sold) }} ชิ้น</span>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-card v-else height="340" :width="MobileSize ? 146 : 230" :elevation="hover ? 6 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer; border-radius: 6px !important; border: 1px solid #B9DAF6 !important;" outlined >
      <!-- <v-row dense>
        <v-col cols="12" md="12" sm="12" xs="12" class="ma-2" v-if="itemProduct.quantity === 0 || itemProduct.quantity === null">
          <v-img src="@/assets/Tag/newPreorder.png" height="29" width="73" contain style="margin-left: -24px; margin-top: 0px;" ></v-img>
        </v-col>
        <v-col cols="6" md="6" sm="6" xs="6" class="pt-2 pb-0" v-else>
          <v-img src="@/assets/Tag/newSale.svg" height="29" width="79" contain style="margin-left: -6px; margin-top: 0px;" v-if="itemProduct.message_status === 'sale'"></v-img>
          <v-img src="@/assets/Tag/NewNew.svg" height="29" width="76" contain style="margin-left: -6px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
          <v-img src="@/assets/Tag/Suggest.svg" height="29" width="69" contain style="margin-left: -9px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
          <v-img src="@/assets/Tag/newPreorder.png" height="29" width="73" contain style="margin-left: -24px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
          <v-img src="@/assets/Tag/BestSellerNew.svg" height="29" width="117" contain style="margin-left: -5px; margin-top: 0px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
        </v-col>
      </v-row> -->
      <v-img
       :src="itemProduct.images_URL[0]"
       loading="lazy"
       :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170"
       width="100%"
       v-if="itemProduct.images_URL !== null && itemProduct.images_URL !== '' && itemProduct.images_URL !== undefined"
       contain
       class="align-start"
       style="position: relative;"
      >
      </v-img>
      <v-img src="@/assets/NoImage.png" :height="MobileSize || checkWidth === 1280 ? 123 : IpadProSize || IpadSize ? 200 : checkWidth >= 1900 ? 210 : 170" width="100%" contain v-else @click="DetailProduct(itemProduct)">
      </v-img>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <!-- <v-card-text v-bind="attrs" v-on="on" style="font-size: 16px; font-weight: bold; line-height: 30px; color: #333333; word-break: keep-all;" class="pb-0 px-2 truncate">{{ itemProduct.name|truncate(52, '...') }}</v-card-text> -->
          <v-row dense>
            <v-col cols="12" class="pl-0">
              <p v-on="on" style="font-size: 14px; font-weight: bold; display: block; -webkit-box-orient: vertical; -webkit-line-clamp: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; color: #000000; height: 32px;" class="mb-0 mt-1 px-2 pl-3">{{ itemProduct.product_name }}</p>
            </v-col>
          </v-row>
          <!-- <v-card-text v-bind="attrs" v-on="on" style="font-size: 12px; font-weight: bold; line-height: 30px; color: #333333; max-height: 90px;" class="pb-0 px-2">{{ itemProduct.name|truncate(75, '...') }}</v-card-text> -->
        </template>
        <span>{{ itemProduct.product_name }}</span>
      </v-tooltip>
      <!-- <p class="pt-2 px-2 mb-1" style="font-size: 14px; line-height: 26px; color: #333333; height: 57px;" v-snip="2" v-if=" itemProduct.short_description !== null">{{ itemProduct.short_description }}</p> -->
      <!-- <v-card-text class="pt-2 mb-4 px-2" style="font-size: 14px; line-height: 26px; color: #333333; height: 57px; width: 214px; width: 144px; height: 45px; word-break: keep-all;" v-if=" itemProduct.short_description !== null">{{ itemProduct.short_description|truncate(34, '...') }}</v-card-text> -->
      <!-- <v-card-text class="pt-2 mb-4 px-2" style="font-size: 14px; line-height: 26px; color: #333333; height: 57px; width: 214px; width: 144px; height: 45px; word-break: keep-all;" v-else></v-card-text> -->
      <!-- <v-card-text class="py-0 pb-0 px-2 mb-3" style="height: 46px;">
        <div v-if="itemProduct.attribute !== null">
          <p style="font-size: 12px; line-height: 16px; font-weight: 400; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; line-height: 24px; color: #333333; height: 40px;">{{ itemProduct.attribute }}</p>
        </div>
      </v-card-text> -->
      <v-card-text class="pt-1">
        <v-row dense>
          <v-rating
            v-model="itemProduct.stars"
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
          ></v-rating>
          <!-- <v-spacer></v-spacer>
          <v-btn icon small @click="CheckaddFavorites()" @click.prevent="pathProductDetail">
            <v-icon color="#D1392B" v-if="itemProduct.isFavorite === false || itemProduct.isFavorite === 'false'">mdi-heart-outline</v-icon>
            <v-icon color="#D1392B" v-else>mdi-heart</v-icon>
          </v-btn> -->
        </v-row>
      </v-card-text>
      <v-card-text class="pt-0">
        <v-row dense>
          <v-col cols="12">
            <v-row dense class="pl-1">
              <span style="font-size: 16px; font-weight: 600; line-height: 19px; color: #636363; text-decoration-line: line-through;" class="pt-1">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              <v-chip v-if="itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0'" color="#FBE5E4" text-color="#FF0000" style="border-radius: 4px;" :class="MobileSize ? 'px-2 ml-1' : 'ml-2'" small><span class="discountText">ส่วนลด {{ itemProduct.discount_percent }}</span></v-chip>
            </v-row>
          </v-col>
          <v-col cols="12">
            <span style="font-weight: 600; font-size: 24px; line-height: 29px; color: #F5222D;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          </v-col>
          <v-col cols="12">
            <span style="font-weight: 700; font-size: 14px; line-height: 16px; color: #FAAD14; letter-spacing: -0.2px;" v-if="itemProduct.sold !== '-'">ขายแล้ว {{ kFormatter(itemProduct.sold) }} ชิ้น</span>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-hover>
</template>

<script>
export default {
  props: ['itemProduct', 'pageCheck'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discription: 'หมวกนิรภัยป้องกันอุบัติหมวกนิรภัยป้องกันอุบัติ',
      rating: 5,
      favorite: false,
      priceSame: false,
      oneData: [],
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN
    }
  },
  created () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkWidth () {
      return window.screen.width
    },
    checkWidthScreen () {
      if (window.screen.width <= 567) {
        return '145'
      } else if (window.screen.width <= 768) {
        return '152'
      } else if (window.screen.width <= 834) {
        return '166'
      } else if (window.screen.width <= 992) {
        return '170'
      } else if (window.screen.width <= 1024) {
        return '151'
      } else if (window.screen.width <= 1180) {
        return '182'
      } else if (window.screen.width <= 1200) {
        return '192'
      } else if (window.screen.width <= 1280) {
        return '142'
      } else {
        if (this.IpadSize) {
          return '148'
        } else if (window.screen.width >= 1600) {
          return '207.5'
        } else {
          return '191.5'
        }
      }
    },
    checkWidthScreenDetail () {
      if (window.screen.width <= 567) {
        return '145'
      } else if (window.screen.width <= 768) {
        return '160'
      } else if (window.screen.width <= 834) {
        return '175'
      } else if (window.screen.width <= 992) {
        return '170'
      } else if (window.screen.width <= 1024) {
        return '158'
      } else if (window.screen.width <= 1180) {
        return '189'
      } else if (window.screen.width <= 1200) {
        return '192'
      } else if (window.screen.width <= 1280) {
        return '149'
      } else {
        if (this.IpadSize) {
          return '156'
        } else if (window.screen.width >= 1600) {
          return '212.5'
        } else {
          return '196.5'
        }
      }
    }
  },
  methods: {
    kFormatter (num) {
      return Math.abs(num) > 999 ? Math.sign(num) * ((Math.abs(num) / 1000).toFixed(1)) + 'k' : Math.sign(num) * Math.abs(num)
    }
  }
}
</script>

<style scoped>
.discountText {
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
}
.priceDecrese {
  font-size: 16px;
  text-decoration: line-through;
  color: #636363;
  font-weight: 500;
  line-height: 22px;
  margin-right: 0px;
}
.specialPrice {
  font-size: 24px;
  font-weight: bold;
  color: #D1392B;
  line-height: 40px;
}
</style>
